"""
用户引导服务
提供智能的用户引导和帮助功能
"""

from enum import Enum
from typing import Dict, List, Optional, Tuple
from telegram import Update, InlineKeyboardButton, InlineKeyboardMarkup
from telegram.ext import ContextTypes
from sqlalchemy.orm import Session

from app.models.telegram_user import TelegramUser
from app.models.telegram_group import TelegramGroup
from app.core.logging import get_logger

logger = get_logger(__name__)


class UserState(Enum):
    """用户状态枚举"""
    NEW_USER = "new_user"                    # 新用户，首次交互
    UNVERIFIED = "unverified"               # 未验证用户
    VERIFIED = "verified"                   # 已验证用户
    GROUP_UNBOUND = "group_unbound"         # 群组未绑定
    GROUP_BOUND = "group_bound"             # 群组已绑定
    FULLY_READY = "fully_ready"             # 完全就绪，可以使用所有功能


class GuideType(Enum):
    """引导类型枚举"""
    WELCOME = "welcome"                     # 欢迎引导
    VERIFICATION = "verification"           # 验证引导
    BINDING = "binding"                     # 绑定引导
    USAGE = "usage"                         # 使用引导
    ERROR_RECOVERY = "error_recovery"       # 错误恢复引导


class UserGuideService:
    """用户引导服务"""
    
    def __init__(self, db: Session, config):
        self.db = db
        self.config = config
        self.logger = logger
        
        # 管理员联系方式配置
        self.admin_contact = {
            "telegram": "@admin_username",  # 可以从配置中读取
            "email": "<EMAIL>",
            "phone": "+86-xxx-xxxx-xxxx"
        }
    
    async def analyze_user_state(self, update: Update) -> UserState:
        """分析用户当前状态"""
        try:
            user_id = update.effective_user.id
            chat_id = update.effective_chat.id
            
            # 检查用户验证状态
            telegram_user = self.db.query(TelegramUser).filter_by(
                telegram_user_id=user_id
            ).first()
            
            # 检查群组绑定状态
            telegram_group = self.db.query(TelegramGroup).filter_by(
                chat_id=chat_id
            ).first()
            
            # 判断用户状态
            if not telegram_user:
                return UserState.NEW_USER
            elif not telegram_user.is_verified():
                return UserState.UNVERIFIED
            elif not telegram_group or not telegram_group.is_active():
                return UserState.GROUP_UNBOUND
            else:
                return UserState.FULLY_READY
                
        except Exception as e:
            self.logger.error(f"分析用户状态失败: {e}")
            return UserState.NEW_USER
    
    async def get_smart_guide(
        self, 
        update: Update, 
        context: ContextTypes.DEFAULT_TYPE,
        guide_type: Optional[GuideType] = None,
        error_context: Optional[Dict] = None
    ) -> Tuple[str, Optional[InlineKeyboardMarkup]]:
        """获取智能引导内容"""
        try:
            user_state = await self.analyze_user_state(update)
            user_name = self._get_user_display_name(update)
            
            if guide_type == GuideType.ERROR_RECOVERY:
                return await self._get_error_recovery_guide(update, error_context, user_state)
            elif guide_type == GuideType.WELCOME:
                return await self._get_welcome_guide(update, user_state, user_name)
            elif guide_type == GuideType.VERIFICATION:
                return await self._get_verification_guide(update, user_state, user_name)
            elif guide_type == GuideType.BINDING:
                return await self._get_binding_guide(update, user_state, user_name)
            else:
                # 根据用户状态自动选择引导类型
                return await self._get_contextual_guide(update, user_state, user_name)
                
        except Exception as e:
            self.logger.error(f"获取智能引导失败: {e}")
            return self._get_fallback_guide()
    
    async def _get_welcome_guide(
        self, 
        update: Update, 
        user_state: UserState, 
        user_name: str
    ) -> Tuple[str, Optional[InlineKeyboardMarkup]]:
        """获取欢迎引导"""
        
        base_welcome = f"""👋 **欢迎使用沃尔玛绑卡统计机器人，{user_name}！**

🤖 我是您的智能助手，让我来帮您快速上手！

"""
        
        if user_state == UserState.NEW_USER:
            message = base_welcome + """🚀 **快速开始指南**

要使用本机器人，您需要完成以下步骤：

**第一步：身份验证** ⏱️ 约5分钟
• 输入 `/verify` 开始身份验证
• 获取验证令牌并发送给管理员
• 等待管理员审核通过

**第二步：群组绑定** ⏱️ 约2分钟
• 管理员提供绑定令牌
• 在群组中输入 `/bind <令牌>`
• 完成群组绑定

**第三步：开始使用** ⏱️ 立即可用
• 输入 `/stats` 查看统计数据
• 享受便捷的数据查询服务

💡 **现在就开始**：输入 `/verify` 开始第一步！"""

            keyboard = InlineKeyboardMarkup([
                [InlineKeyboardButton("🔐 开始验证", callback_data="start_verify")],
                [InlineKeyboardButton("📞 联系管理员", callback_data="contact_admin")],
                [InlineKeyboardButton("❓ 查看帮助", callback_data="show_help")]
            ])
            
        elif user_state == UserState.UNVERIFIED:
            message = base_welcome + """✅ **您已在系统中注册**

但还需要完成身份验证才能使用功能。

📋 **下一步操作**：
1. 检查您的验证状态：`/status`
2. 如需重新验证：`/verify`
3. 联系管理员完成审核

⏱️ **预计完成时间**：5-30分钟（取决于管理员响应速度）"""

            keyboard = InlineKeyboardMarkup([
                [InlineKeyboardButton("📊 查看状态", callback_data="check_status")],
                [InlineKeyboardButton("🔐 重新验证", callback_data="start_verify")],
                [InlineKeyboardButton("📞 联系管理员", callback_data="contact_admin")]
            ])
            
        else:
            message = base_welcome + """🎉 **您已完成身份验证！**

现在可以使用所有功能了。

🚀 **立即开始**：
• `/stats` - 查看今日数据
• `/stats_week` - 查看本周数据
• `/stats_month` - 查看本月数据

🔍 **CK查询**：
• `CK今日` 或 `ck统计` - 查看CK使用情况
• `ck情况` - 查看CK当前状态
• `今天ck` - 查看今日CK数据

💡 **小贴士**：
• 支持自然语言查询，如 "ck今日统计"
• 随时输入 `/help` 获取详细帮助
• 点击下方按钮快速查询"""

            keyboard = InlineKeyboardMarkup([
                [InlineKeyboardButton("📊 查看今日数据", callback_data="stats_today")],
                [InlineKeyboardButton("📈 查看本周数据", callback_data="stats_week")],
                [InlineKeyboardButton("🔍 CK查询", callback_data="ck_stats_today")],
                [InlineKeyboardButton("❓ 查看帮助", callback_data="show_help")]
            ])
        
        return message, keyboard
    
    async def _get_verification_guide(
        self, 
        update: Update, 
        user_state: UserState, 
        user_name: str
    ) -> Tuple[str, Optional[InlineKeyboardMarkup]]:
        """获取验证引导"""
        
        if user_state == UserState.NEW_USER:
            message = f"""🔐 **身份验证指南 - {user_name}**

为了保护数据安全，您需要先完成身份验证。

📋 **详细步骤**：

**步骤1：申请验证** ⏱️ 1分钟
• 输入命令：`/verify`
• 系统生成您的专属验证令牌

**步骤2：联系管理员** ⏱️ 2分钟
• 将验证令牌发送给管理员
• 管理员联系方式：{self.admin_contact['telegram']}

**步骤3：等待审核** ⏱️ 5-30分钟
• 管理员会在系统中审核您的申请
• 审核通过后您会收到通知

💡 **为什么需要验证？**
• 🔒 保护敏感数据安全
• 👤 建立您的身份档案
• 🎯 确保授权访问

🚀 **现在就开始**：输入 `/verify` 申请验证！"""

        elif user_state == UserState.UNVERIFIED:
            message = f"""⏳ **验证进行中 - {user_name}**

您已提交验证申请，正在等待管理员审核。

📊 **当前状态**：
• ✅ 验证申请已提交
• ⏳ 等待管理员审核
• ❌ 尚未完成验证

📞 **如需催促审核**：
• 联系管理员：{self.admin_contact['telegram']}
• 邮箱：{self.admin_contact['email']}

🔄 **如需重新申请**：
• 输入 `/verify` 重新生成令牌"""

        else:
            message = f"""✅ **验证已完成 - {user_name}**

恭喜！您的身份验证已通过。

🎉 **您现在可以**：
• 查询统计数据
• 使用所有机器人功能
• 享受便捷的数据服务

🚀 **立即开始使用**：输入 `/stats` 查看数据！"""

        keyboard = InlineKeyboardMarkup([
            [InlineKeyboardButton("📊 查看状态", callback_data="check_status")],
            [InlineKeyboardButton("📞 联系管理员", callback_data="contact_admin")],
            [InlineKeyboardButton("❓ 查看帮助", callback_data="show_help")]
        ])
        
        return message, keyboard
    
    def _get_user_display_name(self, update: Update) -> str:
        """获取用户显示名称"""
        user = update.effective_user
        if user.first_name and user.last_name:
            return f"{user.first_name} {user.last_name}"
        elif user.first_name:
            return user.first_name
        elif user.username:
            return f"@{user.username}"
        else:
            return "朋友"
    
    async def _get_binding_guide(
        self,
        update: Update,
        user_state: UserState,
        user_name: str
    ) -> Tuple[str, Optional[InlineKeyboardMarkup]]:
        """获取绑定引导"""

        chat = update.effective_chat
        user = update.effective_user

        # 检查用户在群组中的权限
        is_admin = False
        try:
            if chat.type in ['group', 'supergroup']:
                chat_member = await update.get_bot().get_chat_member(chat.id, user.id)
                is_admin = chat_member.status in ['creator', 'administrator']
        except Exception:
            pass

        if is_admin:
            message = f"""🔗 **群组绑定指南 - {user_name}**

您是群组管理员，可以直接执行绑定操作。

📋 **绑定步骤**：

**步骤1：获取绑定令牌** ⏱️ 2分钟
• 联系系统管理员
• 说明需要绑定此群组
• 获取绑定令牌

**步骤2：执行绑定** ⏱️ 1分钟
• 在此群组中输入：`/bind <绑定令牌>`
• 等待绑定完成确认

**步骤3：通知成员** ⏱️ 1分钟
• 告知群组成员绑定已完成
• 成员需要先验证身份才能使用

📝 **令牌格式示例**：
```
/bind tg_bind_WOAGd547pnWHxsHquj2yfAOWyghqT9Hx
```

💡 **注意事项**：
• 绑定令牌有效期24小时
• 只有管理员可以执行绑定
• 绑定后群组成员需要验证身份

📞 **管理员联系方式**：{self.admin_contact['telegram']}"""

        else:
            message = f"""🔗 **群组绑定指南 - {user_name}**

当前群组尚未绑定，您是普通成员，无法直接执行绑定。

📋 **您需要做的**：

**步骤1：联系群组管理员** ⏱️ 2分钟
• 告知管理员需要绑定群组
• 提供系统管理员联系方式

**步骤2：等待管理员操作** ⏱️ 5-10分钟
• 管理员获取绑定令牌
• 管理员执行绑定操作

**步骤3：验证您的身份** ⏱️ 5分钟
• 群组绑定后，输入 `/verify` 验证身份
• 完成验证后即可使用功能

🔍 **如何找到群组管理员**：
• 点击群组名称查看成员列表
• 查看"管理员"部分
• 联系任意一位管理员

📞 **系统管理员联系方式**：{self.admin_contact['telegram']}"""

        keyboard = InlineKeyboardMarkup([
            [InlineKeyboardButton("📊 查看状态", callback_data="check_status")],
            [InlineKeyboardButton("📞 联系管理员", callback_data="contact_admin")],
            [InlineKeyboardButton("❓ 查看帮助", callback_data="show_help")]
        ])

        return message, keyboard

    async def _get_error_recovery_guide(
        self,
        update: Update,
        error_context: Optional[Dict],
        user_state: UserState
    ) -> Tuple[str, Optional[InlineKeyboardMarkup]]:
        """获取错误恢复引导"""

        error_type = error_context.get('error_type', 'unknown') if error_context else 'unknown'
        user_name = self._get_user_display_name(update)

        if error_type == 'user_not_verified':
            message = f"""🔐 **需要身份验证 - {user_name}**

您尝试使用的功能需要先完成身份验证。

❌ **当前问题**：身份未验证
✅ **解决方案**：完成身份验证流程

📋 **立即解决**：

**方法1：快速验证** ⏱️ 5分钟
1. 输入 `/verify` 申请验证
2. 将验证令牌发送给管理员：{self.admin_contact['telegram']}
3. 等待审核通过

**方法2：检查状态** ⏱️ 1分钟
• 输入 `/status` 查看当前验证状态
• 可能您已提交申请，正在审核中

💡 **为什么需要验证？**
• 🔒 保护数据安全
• 👤 确保授权访问
• 📊 建立使用档案

🚀 **现在就开始**：输入 `/verify` 开始验证！"""

        elif error_type == 'verification_guidance':
            message = f"""💡 **验证流程详细说明 - {user_name}**

看起来您对验证流程有疑问，让我为您详细解释：

📋 **完整验证流程**：

**第一步：申请验证** ⏱️ 1分钟
• 输入：`/verify`（不带任何参数）
• 系统自动生成验证令牌
• 您会收到一个32位的验证令牌

**第二步：联系管理员** ⏱️ 2-5分钟
• 复制系统生成的验证令牌
• 发送给管理员：{self.admin_contact['telegram']}
• 或发邮件到：{self.admin_contact['email']}

**第三步：等待审核** ⏱️ 5-30分钟
• 管理员在后台系统中审核
• 审核通过后您会收到通知
• 输入 `/status` 可查看进度

❓ **常见问题解答**：

**Q: 我需要提供什么验证码？**
A: 您不需要提供验证码，系统会自动生成验证令牌

**Q: 验证令牌是什么样的？**
A: 类似这样：`tg_verify_WOAGd547pnWHxsHquj2yfAOW`

**Q: 验证令牌有效期多长？**
A: 通常为60分钟，请及时联系管理员

**Q: 如果令牌过期了怎么办？**
A: 重新输入 `/verify` 申请新的验证令牌

🚀 **立即开始**：输入 `/verify` 申请验证！

📞 **需要帮助**：
• 管理员：{self.admin_contact['telegram']}
• 邮箱：{self.admin_contact['email']}"""

        elif error_type == 'group_not_bound':
            message = f"""🔗 **群组未绑定 - {user_name}**

当前群组尚未绑定到商户系统。

❌ **当前问题**：群组未绑定
✅ **解决方案**：完成群组绑定

📋 **解决步骤**：

**如果您是群组管理员**：
1. 联系系统管理员获取绑定令牌
2. 在群组中输入：`/bind <令牌>`
3. 等待绑定完成

**如果您是普通成员**：
1. 联系群组管理员
2. 告知需要绑定群组
3. 等待管理员完成绑定

📞 **联系方式**：
• 系统管理员：{self.admin_contact['telegram']}
• 邮箱：{self.admin_contact['email']}"""

        else:
            message = f"""❌ **遇到问题了 - {user_name}**

系统遇到了一些问题，让我来帮您解决。

🔧 **通用解决方案**：

1. **检查状态**：输入 `/status` 查看当前状态
2. **查看帮助**：输入 `/help` 获取详细指导
3. **重新开始**：输入 `/start` 重新开始流程

💡 **常见问题**：
• 身份未验证 → 输入 `/verify` 开始验证
• 群组未绑定 → 联系管理员获取绑定令牌
• 权限不足 → 检查您的账户状态

📞 **需要人工协助**：{self.admin_contact['telegram']}"""

        keyboard = InlineKeyboardMarkup([
            [InlineKeyboardButton("📊 查看状态", callback_data="check_status")],
            [InlineKeyboardButton("🔐 开始验证", callback_data="start_verify")],
            [InlineKeyboardButton("📞 联系管理员", callback_data="contact_admin")]
        ])

        return message, keyboard

    async def _get_contextual_guide(
        self,
        update: Update,
        user_state: UserState,
        user_name: str
    ) -> Tuple[str, Optional[InlineKeyboardMarkup]]:
        """根据用户状态获取上下文相关的引导"""

        if user_state == UserState.NEW_USER:
            return await self._get_welcome_guide(update, user_state, user_name)
        elif user_state == UserState.UNVERIFIED:
            return await self._get_verification_guide(update, user_state, user_name)
        elif user_state == UserState.GROUP_UNBOUND:
            return await self._get_binding_guide(update, user_state, user_name)
        else:
            return await self._get_usage_guide(update, user_state, user_name)

    async def _get_usage_guide(
        self,
        update: Update,
        user_state: UserState,
        user_name: str
    ) -> Tuple[str, Optional[InlineKeyboardMarkup]]:
        """获取使用指南"""

        message = f"""🎉 **功能使用指南 - {user_name}**

恭喜！您已完成所有设置，可以使用全部功能了。

📊 **数据查询方式**：

**🔸 命令方式**：
• `/stats` - 查看今日绑卡数据
• `/stats_week` - 查看本周数据
• `/stats_month` - 查看本月数据

**🔸 自然语言方式**：
• `CK今日` 或 `ck统计` - 查看CK使用情况
• `今日统计` 或 `查看数据` - 查看今日数据
• `本周数据` 或 `周统计` - 查看本周数据
• `本月统计` 或 `月数据` - 查看本月数据

**🔸 快捷按钮**：
• 点击下方按钮直接查询

**🔍 CK相关查询**：
• `ck情况` - 查看CK当前状态
• `CK使用情况` - 查看CK使用详情
• `ck数据` - 查看CK统计数据
• `今天ck` - 查看今日CK使用

**⚙️ 状态查询**：
• `/status` 或 `状态` - 查看群组和个人状态
• `/help` 或 `帮助` - 显示帮助信息

💡 **使用技巧**：
• 支持中英文混合输入，如 "ck今日统计"
• 可以随时输入命令获取最新数据
• 遇到问题时，机器人会给出具体建议
• 支持多种表达方式，选择您习惯的即可

🚀 **立即开始**：试试输入 `CK今日` 或点击下方按钮！"""

        keyboard = InlineKeyboardMarkup([
            [InlineKeyboardButton("📊 今日数据", callback_data="stats_today")],
            [InlineKeyboardButton("📈 本周数据", callback_data="stats_week")],
            [InlineKeyboardButton("📅 本月数据", callback_data="stats_month")],
            [InlineKeyboardButton("🔍 CK查询", callback_data="ck_stats_today")],
            [InlineKeyboardButton("❓ 查看帮助", callback_data="show_help")]
        ])

        return message, keyboard

    def get_admin_contact_info(self) -> str:
        """获取管理员联系信息"""
        return f"""📞 **管理员联系方式**

• **Telegram**：{self.admin_contact['telegram']}
• **邮箱**：{self.admin_contact['email']}
• **电话**：{self.admin_contact['phone']}

⏰ **工作时间**：周一至周五 9:00-18:00

💡 **联系时请说明**：
• 您的Telegram用户名
• 遇到的具体问题
• 需要的帮助类型"""

    def _get_fallback_guide(self) -> Tuple[str, None]:
        """获取备用引导信息"""
        return """❌ **系统暂时无法提供个性化帮助**

请尝试以下基础操作：
• `/help` - 查看帮助信息
• `/status` - 查看当前状态
• `/verify` - 开始身份验证

如需人工协助，请联系管理员。""", None
