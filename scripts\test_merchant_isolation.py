#!/usr/bin/env python3
"""
测试商户隔离查询
"""
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from app.db.session import SessionLocal
from app.crud import card as card_crud

def test_merchant_isolation():
    """测试商户隔离查询"""
    # 测试记录
    record_id = "1e461906-1ffb-4d99-9fc8-401990413426"
    correct_merchant_id = 2
    wrong_merchant_id = 999
    
    print(f"测试记录ID: {record_id}")
    print(f"正确商户ID: {correct_merchant_id}")
    print(f"错误商户ID: {wrong_merchant_id}")
    
    # 方法1: 基础查询（不考虑商户）
    print("\n1. 基础查询（不考虑商户隔离）:")
    try:
        with SessionLocal() as db:
            record1 = card_crud.get(db, record_id)
            if record1:
                print("   ✅ 基础查询找到记录")
                print(f"   商户ID: {record1.merchant_id}")
            else:
                print("   ❌ 基础查询未找到记录")
    except Exception as e:
        print(f"   ❌ 基础查询失败: {e}")
    
    # 方法2: 正确商户ID的隔离查询
    print("\n2. 正确商户ID的隔离查询:")
    try:
        with SessionLocal() as db:
            record2 = card_crud.get_card_record(db, record_id, correct_merchant_id)
            if record2:
                print("   ✅ 商户隔离查询找到记录")
                print(f"   商户ID: {record2.merchant_id}")
                print(f"   状态: {record2.status}")
                print(f"   回调状态: {record2.callback_status}")
            else:
                print("   ❌ 商户隔离查询未找到记录")
    except Exception as e:
        print(f"   ❌ 商户隔离查询失败: {e}")
    
    # 方法3: 错误商户ID的隔离查询
    print("\n3. 错误商户ID的隔离查询:")
    try:
        with SessionLocal() as db:
            record3 = card_crud.get_card_record(db, record_id, wrong_merchant_id)
            if record3:
                print("   ❌ 意外找到记录（商户隔离失效）")
                print(f"   商户ID: {record3.merchant_id}")
            else:
                print("   ✅ 正确拒绝了错误商户的查询")
    except Exception as e:
        print(f"   ❌ 错误商户查询失败: {e}")
    
    # 方法4: 测试优化回调服务的查询方法
    print("\n4. 测试优化回调服务的查询:")
    try:
        from app.services.optimized_callback_service import optimized_callback_service
        import uuid
        
        with SessionLocal() as db:
            # 模拟优化回调服务的查询逻辑
            record4 = card_crud.get_card_record(db, record_id, correct_merchant_id)
            if record4:
                print("   ✅ 优化回调服务查询方法找到记录")
                print(f"   商户ID: {record4.merchant_id}")
                print(f"   状态: {record4.status}")
                print(f"   回调状态: {record4.callback_status}")
            else:
                print("   ❌ 优化回调服务查询方法未找到记录")
    except Exception as e:
        print(f"   ❌ 优化回调服务查询失败: {e}")

if __name__ == "__main__":
    test_merchant_isolation()
