<script setup>
import { useRouter } from 'vue-router'

const router = useRouter()

const goHome = () => {
    router.push('/')
}

const goBack = () => {
    router.go(-1)
}
</script>

<template>
    <div class="error-page">
        <div class="error-container">
            <div class="error-code">404</div>
            <div class="error-title">页面不存在</div>
            <div class="error-desc">抱歉，您访问的页面不存在</div>
            <div class="error-actions">
                <el-button type="primary" @click="goHome">返回首页</el-button>
                <el-button @click="goBack">返回上一页</el-button>
            </div>
        </div>
    </div>
</template>

<style scoped>
.error-page {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100vh;
    background-color: #f9fafc;
}

.error-container {
    text-align: center;
    padding: 40px;
}

.error-code {
    font-size: 120px;
    font-weight: bold;
    color: #909399;
    margin-bottom: 20px;
    line-height: 1;
}

.error-title {
    font-size: 24px;
    font-weight: bold;
    color: #303133;
    margin-bottom: 15px;
}

.error-desc {
    font-size: 16px;
    color: #606266;
    margin-bottom: 30px;
}

.error-actions {
    display: flex;
    justify-content: center;
    gap: 20px;
}
</style>