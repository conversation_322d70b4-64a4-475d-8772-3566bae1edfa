{"name": "walmart-bind-card-admin", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "dev:clean": "vite --force", "build": "vite build", "preview": "vite preview", "clean": "rimraf node_modules/.vite dist", "clean:win": "if exist \"node_modules\\.vite\" rmdir /s /q \"node_modules\\.vite\" && if exist \"dist\" rmdir /s /q \"dist\""}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "@vueuse/core": "^13.1.0", "axios": "^1.8.4", "crypto-js": "^4.2.0", "echarts": "^5.6.0", "element-plus": "^2.9.7", "pinia": "^3.0.2", "vue": "^3.5.13", "vue-router": "^4.5.0", "walmart-bind-card-admin": "file:"}, "devDependencies": {"@vitejs/plugin-vue": "^5.2.1", "rimraf": "^6.0.1", "sass": "^1.87.0", "unplugin-auto-import": "^19.1.2", "unplugin-vue-components": "^28.4.1", "vite": "^6.2.0"}}