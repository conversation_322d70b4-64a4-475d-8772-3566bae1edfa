import asyncio
from datetime import datetime
from typing import Dict, Any, Optional
from time import time

from sqlalchemy.orm import Session
from sqlalchemy.ext.asyncio import AsyncSession
from typing import Union
from app.core.logging import get_logger
from app.crud import card as card_crud
from app.crud import merchant as merchant_crud
from app.crud.card import get_card_record_async
from app.crud.merchant import get_merchant_async
from app.models import CardStatus
from app.models.binding_log import LogLevel
from app.schemas.card import CardRecordUpdate
from app.services.binding_log_service import BindingLogService
from app.services.walmart_api_service import WalmartAPIService
from app.services.amount_validation_service import AmountValidationService
from app.utils.queue_producer import send_callback_task
from app.utils.time_utils import (
    get_current_time,
    get_current_timestamp,
    datetime_to_isoformat,
    calculate_duration_seconds,
    TimeTracker
)
from app.utils.response_data_handler import response_data_handler

# 创建日志记录器
logger = get_logger("binding_process_service")


class BindingProcessService:
    """专门处理绑卡流程的服务"""
    
    def __init__(self):
        self.api_service = WalmartAPIService()
        self.amount_service = AmountValidationService()
    
    async def process_binding_request(
        self,
        db: Union[Session, AsyncSession],
        record_id: int,
        merchant_id: int,
        unencrypted_card_password: Optional[str],
        ext_data: Optional[str] = None,
        extra_params: Optional[Dict[str, Any]] = None,
        is_recovery: bool = False,
        recovery_attempt: int = 0,
        debug: bool = False,
    ):
        """
        处理绑卡请求的主要流程

        Args:
            db: 数据库会话
            record_id: 记录ID
            merchant_id: 商家ID
            unencrypted_card_password: 未加密的卡密
            ext_data: 扩展数据
            extra_params: 额外参数
            is_recovery: 是否是恢复处理
            recovery_attempt: 恢复尝试次数
        """
        # 使用统一的时间跟踪器
        time_tracker = TimeTracker(f"binding_process_{record_id}")
        start_process_time = time_tracker.start()

        # 记录开始处理日志
        await self._log_process_start(
            db, record_id, merchant_id, is_recovery, recovery_attempt, ext_data, time_tracker
        )

        try:
            # 执行绑卡处理流程
            await self._execute_binding_flow(
                db, record_id, merchant_id, unencrypted_card_password,
                ext_data, extra_params, time_tracker, debug
            )
        except Exception as e:
            await self._handle_process_exception(
                db, record_id, None, e, time_tracker,
                None, None, merchant_id, ext_data
            )

        # 记录处理完成
        time_tracker.end()
        final_duration = time_tracker.get_duration_seconds()
        logger.info(
            f"[PROCESS_COMPLETE] 绑卡处理完成 | record_id={record_id} | "
            f"duration_seconds={final_duration:.2f}"
        )

    def _calculate_total_process_time(self, record) -> float:
        """
        计算真正的总处理时间：从绑卡记录创建到现在的时间

        Args:
            record: 绑卡记录对象

        Returns:
            float: 总处理时间（秒）
        """
        from app.utils.time_utils import get_current_time, ensure_timezone

        # 确保时区一致性
        created_at_tz = ensure_timezone(record.created_at)
        current_time = get_current_time()

        # 计算总处理时间
        total_duration = (current_time - created_at_tz).total_seconds()

        logger.info(
            f"[PROCESS_TIME_CALC] 计算总处理时间 | record_id={record.id} | "
            f"created_at={created_at_tz.isoformat()} | current_time={current_time.isoformat()} | "
            f"total_duration_seconds={total_duration:.2f}"
        )

        return total_duration

    async def _execute_binding_flow(
        self, db: Union[Session, AsyncSession], record_id: int, merchant_id: int,
        unencrypted_card_password: str, ext_data: Optional[str],
        extra_params: Optional[Dict[str, Any]], time_tracker: TimeTracker,
        debug: bool = False
    ):
        """执行绑卡处理流程"""
        # 加载并验证记录
        record = await self._load_and_validate_record(db, record_id, merchant_id)
        if not record:
            return

        trace_id = record.trace_id
        card_number = record.card_number

        # 加载并验证商家
        merchant = await self._load_and_validate_merchant(
            db, merchant_id, record_id, time_tracker
        )
        if not merchant:
            return

        # 调用API并处理结果
        await self._call_api_and_process_result(
            db, record, merchant, unencrypted_card_password,
            extra_params, time_tracker, trace_id, card_number, ext_data, debug
        )

        # 确保数据库事务提交后再发送回调任务
        try:
            # 支持同步和异步数据库会话的事务提交
            if isinstance(db, AsyncSession):
                await db.commit()  # 异步提交
            else:
                db.commit()  # 同步提交
            logger.debug(f"[BINDING_COMMIT] 绑卡数据库事务已提交 | record_id={record_id}")

            # 【关键修复】只有在绑卡成功且余额获取成功后才发送回调
            if merchant.callback_url:
                await self._send_callback_if_ready(record_id, merchant_id, ext_data, trace_id, db)
        except Exception as e:
            logger.error(f"[BINDING_COMMIT_ERROR] 提交事务或发送回调失败 | record_id={record_id} | error={e}")
            # 支持同步和异步数据库会话的事务回滚
            if isinstance(db, AsyncSession):
                await db.rollback()  # 异步回滚
            else:
                db.rollback()  # 同步回滚
            raise

    async def _call_api_and_process_result(
        self, db: Session, record, merchant, unencrypted_card_password: str,
        extra_params: Optional[Dict[str, Any]], time_tracker: TimeTracker,
        trace_id: str, card_number: str, ext_data: Optional[str] = None, debug: bool = False
    ):
        """调用API并处理结果"""
        # 记录API调用开始
        logger.info(
            f"[PROCESS_API_CALL_START] 开始调用沃尔玛API | record_id={record.id} | "
            f"trace_id={trace_id} | merchant_id={merchant.id} | card_number={card_number[:6]}***"
        )

        # 调用沃尔玛API
        result = await self.api_service.call_walmart_api_with_retry(
            db, record, merchant, unencrypted_card_password, extra_params, debug
        )

        # 【修复】计算真正的总处理时间：从绑卡记录创建到现在的时间
        process_duration_seconds = self._calculate_total_process_time(record)

        # 记录API调用完成
        result_status = "SUCCESS" if result.get("success") else "FAILED"
        logger.info(
            f"[PROCESS_API_CALL_COMPLETE] API调用完成 | record_id={record.id} | "
            f"trace_id={trace_id} | result={result_status} | duration_seconds={process_duration_seconds:.2f}"
        )

        # 处理API结果
        await self._process_api_result(
            db, record, result, process_duration_seconds, trace_id, card_number, merchant, ext_data, debug
        )

    async def _log_process_start(
        self, db: Session, record_id: int, merchant_id: int,
        is_recovery: bool, recovery_attempt: int, ext_data: Optional[str],
        time_tracker: TimeTracker
    ):
        """记录处理开始日志"""
        log_prefix = "[RECOVERY_PROCESS_START]" if is_recovery else "[PROCESS_START]"
        recovery_info = f" | recovery_attempt={recovery_attempt}" if is_recovery else ""
        logger.info(
            f"{log_prefix} 开始处理绑卡请求 | record_id={record_id} | merchant_id={merchant_id}{recovery_info}"
        )

        try:
            message = "开始恢复处理绑卡请求" if is_recovery else "开始处理绑卡请求"
            details = {
                "merchant_id": merchant_id,
                "ext_data": ext_data,
                "process_start_time": datetime_to_isoformat(get_current_time()),
            }

            if is_recovery:
                details["is_recovery"] = True
                details["recovery_attempt"] = recovery_attempt

            # 【安全修复】创建BindingLogService实例
            binding_log_service = BindingLogService(db)
            await binding_log_service.log_system(
                db=db,
                card_record_id=str(record_id),
                message=message,
                log_level=LogLevel.INFO,
                details=details,
            )
        except Exception as e:
            logger.error(f"[PROCESS_LOG_ERROR] 记录处理开始日志失败 | error={str(e)}")

    async def _load_and_validate_record(
        self, db, record_id: int, merchant_id: int
    ):
        """加载并验证记录"""
        # 支持同步和异步数据库会话
        if isinstance(db, AsyncSession):
            record = await get_card_record_async(db, str(record_id), merchant_id)
        else:
            record = card_crud.get_card_record(db, str(record_id), merchant_id)

        if not record:
            logger.error(
                f"[PROCESS_ERROR] 卡记录不存在 | record_id={record_id} | merchant_id={merchant_id}"
            )
            return None

        trace_id = record.trace_id
        card_number = record.card_number

        logger.info(
            f"[PROCESS_RECORD_LOADED] 已加载卡记录 | record_id={record_id} | "
            f"trace_id={trace_id} | card_number={card_number[:6]}*** | status={record.status}"
        )

        # 检查记录状态，防止重复处理
        if record.status != CardStatus.PENDING:
            logger.warning(
                f"[PROCESS_SKIP] 跳过非PENDING状态记录 | record_id={record_id} | "
                f"trace_id={trace_id} | current_status={record.status}"
            )
            return None

        return record

    async def _load_and_validate_merchant(
        self, db, merchant_id: int, record_id: int, time_tracker: TimeTracker
    ):
        """加载并验证商家"""
        # 支持同步和异步数据库会话
        if isinstance(db, AsyncSession):
            merchant = await get_merchant_async(db, merchant_id)
        else:
            merchant = merchant_crud.get_merchant(db, merchant_id)

        if not merchant:
            logger.error(
                f"[PROCESS_ERROR] 商家不存在 | merchant_id={merchant_id} | record_id={record_id}"
            )
            # 【修复】不传入duration，让方法自动计算总处理时间
            await self._update_record_failed(
                db, record_id, "商家配置不存在"
            )
            return None

        # 检查商家状态
        if not merchant.status:
            logger.warning(
                f"[PROCESS_ERROR] 商家已禁用 | merchant_id={merchant_id} | "
                f"merchant_name={merchant.name} | record_id={record_id}"
            )
            # 【修复】不传入duration，让方法自动计算总处理时间
            await self._update_record_failed(
                db, record_id, "商家已禁用"
            )
            return None

        logger.info(
            f"[PROCESS_MERCHANT_LOADED] 已加载商家信息 | merchant_id={merchant_id} | "
            f"merchant_name={merchant.name} | has_callback_url={bool(merchant.callback_url)}"
        )

        return merchant

    async def _process_api_result(
        self, db: Session, record, result: Dict[str, Any],
        process_duration_seconds: float, trace_id: str, card_number: str,
        merchant, ext_data: Optional[str] = None, debug: bool = False
    ):
        """处理API调用结果"""
        api_result_status = "SUCCESS" if result["success"] else "FAILED"
        logger.info(
            f"[PROCESS_API_CALL_COMPLETE] API调用完成 | record_id={record.id} | "
            f"trace_id={trace_id} | result={api_result_status} | "
            f"duration_seconds={process_duration_seconds:.2f}"
        )

        # 准备更新数据的基础部分
        raw_response = result.get("raw_response") or result.get("raw_response_text")

        # 【新增】处理响应数据大小限制，用于card_records表
        processed_response, response_warning = None, None
        if raw_response:
            try:
                processed_response, response_warning = response_data_handler.process_response_data(
                    raw_response, f"card_record_{record.id}"
                )
                if response_warning:
                    logger.warning(f"卡记录响应数据处理警告: {response_warning} | record_id: {record.id}")
            except Exception as e:
                logger.error(f"处理卡记录响应数据失败: {e} | record_id: {record.id}")
                processed_response = {"_error": "响应数据处理失败", "_original_error": str(e)}

        update_data_dict = {
            "process_time": process_duration_seconds,
            "response_data": processed_response,
        }

        if result["success"]:
            # 处理成功情况
            await self._handle_success_result(
                db, record, result, update_data_dict, trace_id, card_number, debug
            )
        else:
            # 处理失败情况 - 使用原子性绑卡服务确保CK预占用回滚
            error_code = result.get("error_code", "UNKNOWN")
            walmart_ck_id = result.get("walmart_ck_id")

            # 【修复】确保walmart_ck_id是数字类型，便于后续日志记录
            walmart_ck_id_int = None
            if isinstance(walmart_ck_id, int) and walmart_ck_id > 0:
                walmart_ck_id_int = walmart_ck_id
            elif isinstance(walmart_ck_id, str) and walmart_ck_id.isdigit():
                walmart_ck_id_int = int(walmart_ck_id)

            logger.warning(
                f"[PROCESS_FAILED] 绑卡失败 | record_id={record.id} | "
                f"trace_id={trace_id} | card_number={card_number[:6]}*** | "
                f"error={result['error']} | error_code={error_code} | "
                f"walmart_ck_id={walmart_ck_id} | duration_seconds={process_duration_seconds:.2f}"
            )

            # 【关键修复】使用原子性绑卡服务处理失败结果，确保CK预占用回滚
            atomic_success = False
            if walmart_ck_id_int:
                try:
                    from app.services.atomic_binding_service import AtomicBindingService
                    atomic_service = AtomicBindingService(db)

                    atomic_success = await atomic_service.execute_atomic_binding(
                        record_id=str(record.id),
                        merchant_id=record.merchant_id,
                        api_result=result,
                        is_success=False  # 绑卡失败
                    )

                    if atomic_success:
                        logger.info(f"[ATOMIC_FAILURE_SUCCESS] 原子性绑卡失败处理成功，CK预占用已回滚 | record_id={record.id} | walmart_ck_id={walmart_ck_id_int}")
                        # 设置标记表示已经使用原子性服务处理
                        update_data_dict["_atomic_processed"] = True
                    else:
                        logger.error(f"[ATOMIC_FAILURE_FAILED] 原子性绑卡失败处理失败 | record_id={record.id} | walmart_ck_id={walmart_ck_id_int}")
                except Exception as e:
                    logger.error(f"[ATOMIC_FAILURE_ERROR] 原子性绑卡失败处理异常: {e} | record_id={record.id}")

            # 如果原子性服务处理失败，回退到原有逻辑（但仍需手动回滚CK预占用）
            if not atomic_success:
                update_data_dict["status"] = CardStatus.FAILED
                update_data_dict["error_message"] = result["error"]

                # 【关键修复】手动回滚CK预占用
                if walmart_ck_id_int:
                    try:
                        from app.services.simplified_ck_service import AtomicCKUpdateService
                        ck_service = AtomicCKUpdateService(db)
                        await ck_service.commit_ck_usage(walmart_ck_id_int, success=False)
                        logger.info(f"[CK_ROLLBACK_SUCCESS] 手动回滚CK预占用成功 | walmart_ck_id={walmart_ck_id_int}")
                    except Exception as e:
                        logger.error(f"[CK_ROLLBACK_ERROR] 手动回滚CK预占用失败: {e} | walmart_ck_id={walmart_ck_id_int}")
                else:
                    logger.warning(f"[CK_ROLLBACK_SKIP] 无有效CK ID，跳过预占用回滚 | record_id={record.id}")

            # 记录详细的失败信息到绑卡日志
            try:
                # 【修复】根据错误来源设置正确的错误前缀
                error_source = "walmart_api" if any(keyword in result["error"].lower() for keyword in ["请先去登录", "errorcode", "logid", "沃尔玛"]) else "system"

                # 【修复】获取原始响应数据
                raw_response = result.get("raw_response")

                # 【安全修复】创建BindingLogService实例
                binding_log_service = BindingLogService(db)
                await binding_log_service.log_error(
                    db=db,
                    card_record_id=str(record.id),
                    error_message=f"绑卡流程失败: {result['error']}",
                    details={
                        "trace_id": trace_id,
                        "error_code": error_code,
                        "walmart_ck_id": walmart_ck_id_int or "未知",  # 【修复】在details中使用正确的格式
                        "process_duration_seconds": process_duration_seconds,
                        "card_number_masked": f"{card_number[:6]}***",
                        "error_source": error_source,  # 添加错误来源标识
                    },
                    walmart_ck_id=walmart_ck_id_int,  # 【修复】使用正确的CK ID
                    error_source=error_source,  # 传递错误来源
                    raw_response=raw_response,  # 【修复】保存原始响应数据
                )
            except Exception as e:
                logger.error(f"记录绑卡失败详细日志失败: {str(e)}")

            # 【修复】失败时也要保存CK信息，确保数据完整性
            await self._save_ck_information(db, record, result, update_data_dict, trace_id, is_success=False)

            # 【修复】确保update_data_dict中有walmart_ck_id，用于状态变更日志
            if walmart_ck_id_int and "walmart_ck_id" not in update_data_dict:
                update_data_dict["walmart_ck_id"] = walmart_ck_id_int

        # 更新记录
        await self._update_record_with_status_change(
            db, record, update_data_dict, trace_id
        )

        # 【关键修复】绑卡失败时也要触发回调通知
        if not result["success"]:
            try:
                if merchant and merchant.callback_url:
                    logger.info(
                        f"[CALLBACK_FAILURE] 绑卡失败，发送失败回调 | "
                        f"record_id={record.id} | trace_id={trace_id} | "
                        f"error={result.get('error', '未知错误')}"
                    )
                    await self._send_callback_task(record.id, record.merchant_id, ext_data, trace_id)
                else:
                    logger.debug(
                        f"[CALLBACK_SKIP] 商户未配置回调URL，跳过失败回调 | "
                        f"record_id={record.id} | merchant_id={record.merchant_id}"
                    )
            except Exception as callback_error:
                logger.error(
                    f"[CALLBACK_FAILURE_ERROR] 发送失败回调时发生异常 | "
                    f"record_id={record.id} | error={str(callback_error)}"
                )



    async def _handle_success_result(
        self, db: Session, record, result: Dict[str, Any],
        update_data_dict: Dict[str, Any], trace_id: str, card_number: str, debug: bool = False
    ):
        """处理成功的API结果 - 使用原子性绑卡服务"""
        logger.info(
            f"[BIND_SUCCESS] 绑卡API调用成功 | record_id={record.id} | "
            f"trace_id={trace_id} | card_number={card_number[:6]}***"
        )

        # 使用原子性绑卡服务处理成功结果
        from app.services.atomic_binding_service import AtomicBindingService
        atomic_service = AtomicBindingService(db)

        # 确保result中包含必要的CK信息和debug标识
        walmart_ck_id = result.get('walmart_ck_id')
        if not walmart_ck_id:
            logger.error(f"[ATOMIC_ERROR] API结果中缺少walmart_ck_id | record_id={record.id}")
            # 回退到原有逻辑
            update_data_dict["status"] = CardStatus.SUCCESS
            update_data_dict["error_message"] = None
            await self._save_ck_information(db, record, result, update_data_dict, trace_id, is_success=True)
            return

        # 将debug标识添加到API结果中，以便传递给原子性服务
        result['debug'] = debug

        # 使用原子性服务处理绑卡成功
        atomic_success = await atomic_service.execute_atomic_binding(
            record_id=str(record.id),
            merchant_id=record.merchant_id,
            api_result=result,
            is_success=True
        )

        if atomic_success:
            logger.info(f"[ATOMIC_SUCCESS] 原子性绑卡处理成功 | record_id={record.id}")
            # 原子性服务已经更新了状态，但保留update_data_dict用于后续金额更新
            # 只清除可能冲突的字段，保留其他字段用于后续处理
            conflicting_fields = ["status", "error_message", "walmart_ck_id", "department_id", "bind_time"]
            for field in conflicting_fields:
                update_data_dict.pop(field, None)
            # 设置一个标记表示已经使用原子性服务处理
            update_data_dict["_atomic_processed"] = True
        else:
            logger.error(f"[ATOMIC_FAILED] 原子性绑卡处理失败，回退到原有逻辑 | record_id={record.id}")
            # 回退到原有逻辑
            update_data_dict["status"] = CardStatus.SUCCESS
            update_data_dict["error_message"] = None
            await self._save_ck_information(db, record, result, update_data_dict, trace_id, is_success=True)

        # 尝试获取金额，但不影响绑卡成功状态
        try:
            amount_validation_result = await self.amount_service.validate_card_amount(
                db, record, result, debug
            )

            # 更新金额相关字段
            update_data_dict.update(amount_validation_result["update_fields"])

            # 如果金额验证失败，记录警告但保持绑卡成功状态
            if not amount_validation_result["success"]:
                logger.warning(
                    f"[AMOUNT_FETCH_FAILED] 绑卡成功但金额获取失败 | record_id={record.id} | "
                    f"trace_id={trace_id} | error={amount_validation_result['error_message']}"
                )
                # 在响应数据中记录金额获取失败信息，但不影响绑卡状态
                if "response_data" not in update_data_dict:
                    update_data_dict["response_data"] = {}
                update_data_dict["response_data"]["amount_fetch_warning"] = {
                    "message": "绑卡成功，但获取卡片金额失败",
                    "error": amount_validation_result["error_message"],
                    "timestamp": datetime.now().isoformat()
                }
            else:
                logger.info(
                    f"[AMOUNT_VALIDATION_SUCCESS] 绑卡成功且金额验证通过 | record_id={record.id}"
                )

        except Exception as e:
            logger.error(
                f"[AMOUNT_FETCH_EXCEPTION] 绑卡成功但金额获取过程异常 | record_id={record.id} | "
                f"trace_id={trace_id} | exception={str(e)}"
            )
            # 记录异常但不影响绑卡成功状态
            if "response_data" not in update_data_dict:
                update_data_dict["response_data"] = {}
            update_data_dict["response_data"]["amount_fetch_exception"] = {
                "message": "绑卡成功，但获取卡片金额过程中发生异常",
                "error": str(e),
                "timestamp": datetime.now().isoformat()
            }

        # 记录最终状态
        if update_data_dict.get("_atomic_processed"):
            # 原子性服务已处理，绑卡成功
            logger.info(
                f"[PROCESS_SUCCESS] 绑卡成功(原子性处理) | record_id={record.id} | "
                f"trace_id={trace_id} | card_number={card_number[:6]}***"
            )
        elif update_data_dict.get("status") == CardStatus.SUCCESS:
            logger.info(
                f"[PROCESS_SUCCESS] 绑卡成功 | record_id={record.id} | "
                f"trace_id={trace_id} | card_number={card_number[:6]}*** | "
                f"duration_seconds={update_data_dict.get('process_time', 0):.2f}"
            )
        else:
            logger.warning(
                f"[PROCESS_FAILED] 绑卡失败(金额问题) | record_id={record.id} | "
                f"trace_id={trace_id} | card_number={card_number[:6]}*** | "
                f"error={update_data_dict.get('error_message')}"
            )

    async def _update_record_with_status_change(
        self, db: Session, record, update_data_dict: Dict[str, Any], trace_id: str
    ):
        """更新记录并记录状态变更"""
        # 检查是否已经被原子性服务处理
        if update_data_dict.get("_atomic_processed"):
            # 原子性服务已处理核心字段，但仍需更新金额等其他字段
            # 移除标记字段，只保留需要更新的字段
            update_dict_copy = {k: v for k, v in update_data_dict.items() if k != "_atomic_processed"}
            if not update_dict_copy:
                logger.info(f"[ATOMIC_SKIP] 记录已被原子性服务处理，无其他字段需要更新 | record_id={record.id}")
                return
            logger.info(f"[ATOMIC_PARTIAL_UPDATE] 原子性处理后更新其他字段 | record_id={record.id} | fields={list(update_dict_copy.keys())}")
            update_data_dict = update_dict_copy

        update_data = CardRecordUpdate(**update_data_dict)
        
        # 记录状态变更日志
        old_status = record.status
        new_status = update_data_dict.get("status", old_status)
        if old_status != new_status:
            logger.info(
                f"[PROCESS_STATUS_CHANGE] 记录状态变更 | record_id={record.id} | "
                f"trace_id={trace_id} | old_status={old_status} | new_status={new_status}"
            )
            # 【修复】从update_data_dict中获取walmart_ck_id，确保状态变更日志也包含CK信息
            status_change_walmart_ck_id = update_data_dict.get("walmart_ck_id")

            # 【安全修复】创建BindingLogService实例
            binding_log_service = BindingLogService(db)
            await binding_log_service.log_status_change(
                db=db,
                card_record_id=str(record.id),
                old_status=old_status,
                new_status=new_status,
                details={
                    "process_time": update_data_dict.get("process_time"),
                    "error_message": update_data_dict.get("error_message"),
                    "trace_id": trace_id,
                    "timestamp": datetime_to_isoformat(get_current_time()),
                },
                walmart_ck_id=status_change_walmart_ck_id,  # 【修复】传递CK ID到状态变更日志
            )

        # 更新记录
        logger.info(
            f"[PROCESS_UPDATE_RECORD] 更新记录状态 | record_id={record.id} | new_status={new_status}"
        )
        card_crud.update_card_record(db, record, update_data)

    async def _send_callback_task(
        self, record_id: int, merchant_id: int, ext_data: Optional[str], trace_id: str
    ):
        """发送回调任务到队列"""
        logger.info(
            f"[PROCESS_CALLBACK_QUEUE] 发送回调任务到队列 | record_id={record_id} | trace_id={trace_id}"
        )
        await send_callback_task({
            "record_id": record_id,
            "merchant_id": merchant_id,
            "retry_count": 0,
            "ext_data": ext_data,
            "trace_id": trace_id,
        })

    async def _handle_process_exception(
        self, db: Session, record_id: int, trace_id: str, e: Exception,
        time_tracker: TimeTracker, process_duration_seconds: Optional[float],
        callback_url: Optional[str], merchant_id: int, ext_data: Optional[str]
    ):
        """处理处理过程中的异常"""
        logger.exception(
            f"[PROCESS_EXCEPTION] 绑卡过程中发生异常 | record_id={record_id} | trace_id={trace_id} | error={str(e)}"
        )
        
        if process_duration_seconds is None:
            process_duration_seconds = time_tracker.get_duration_seconds()

        # 记录异常详细信息
        # 【安全修复】创建BindingLogService实例
        binding_log_service = BindingLogService(db)
        await binding_log_service.log_error(
            db=db,
            card_record_id=str(record_id),
            error_message=f"处理异常",
            details={
                "exception": str(e),
                "trace_id": trace_id,
                "process_time": process_duration_seconds,
                "timestamp": datetime_to_isoformat(get_current_time()),
            },
        )

        # 更新记录为失败状态
        await self._update_record_failed(
            db, record_id, f"处理异常: {str(e)}", process_duration_seconds
        )

        # 即使发生异常，也尝试回调（如果有回调URL）
        if callback_url:
            try:
                await self._send_callback_task(record_id, merchant_id, ext_data, trace_id)
            except Exception as callback_error:
                logger.exception(
                    f"[PROCESS_CALLBACK_ERROR] 创建回调任务异常 | record_id={record_id} | error={str(callback_error)}"
                )

    async def _update_record_failed(
        self, db: Session, record_id: int, reason: str, duration: Optional[float] = None
    ):
        """更新记录为失败状态"""
        from app.schemas.card import CardRecordUpdate

        # 【修复】如果没有传入duration，则计算真正的总处理时间
        if duration is None:
            record = card_crud.get_card_record(db, str(record_id))
            if record:
                duration = self._calculate_total_process_time(record)
            else:
                duration = 0.0

        logger.info(
            f"[UPDATE_FAILED_START] 开始更新失败记录 | record_id={record_id} | "
            f"reason='{reason}' | total_duration={duration:.2f}s"
        )

        update_data = CardRecordUpdate(
            status=CardStatus.FAILED,
            process_time=duration,
            error_message=reason,
        )

        try:
            # 需要先获取记录 - 使用正确的方法名
            record = card_crud.get_card_record(db, str(record_id))
            if record:
                card_crud.update_card_record(db, record, update_data)
                logger.info(
                    f"[UPDATE_FAILED_SUCCESS] 成功更新失败记录 | record_id={record_id} | reason='{reason}'"
                )
            else:
                logger.error(f"[UPDATE_FAILED_ERROR] 找不到记录 | record_id={record_id}")
        except Exception as update_exc:
            logger.error(
                f"[UPDATE_FAILED_ERROR] 更新失败记录时发生错误 | record_id={record_id} | error={str(update_exc)}"
            )
            # 支持同步和异步数据库会话的事务回滚
            if hasattr(db, 'rollback'):
                if hasattr(db.rollback, '__call__'):
                    db.rollback()  # 同步回滚
                else:
                    # 异步回滚需要await，但这里在except块中，暂时跳过
                    pass

    async def _save_ck_information(
        self, db: Session, record, result: Dict[str, Any],
        update_data_dict: Dict[str, Any], trace_id: str, is_success: bool = True
    ):
        """
        保存使用的CK信息到card_record表（无论成功失败）

        Args:
            db: 数据库会话
            record: 卡记录对象
            result: API调用结果，包含walmart_ck_id
            update_data_dict: 更新数据字典
            trace_id: 追踪ID
            is_success: 是否绑卡成功，影响CK使用统计记录
        """
        try:
            # 【修复】从API结果中获取使用的CK ID，支持多种字段名
            walmart_ck_id = result.get("walmart_ck_id")
            department_id = result.get("department_id")

            # 【关键修复】如果API结果中没有CK ID，尝试从其他来源获取
            if not walmart_ck_id:
                # 尝试从绑卡上下文或其他来源获取CK信息
                # 这种情况通常发生在绑卡失败但仍需要记录CK使用情况时
                logger.warning(
                    f"[CK_INFO_WARNING] API结果中未找到walmart_ck_id，尝试从其他来源获取 | "
                    f"record_id={record.id} | trace_id={trace_id} | result_keys={list(result.keys())}"
                )

                # 如果仍然没有CK ID，则无法保存CK信息
                # 但这种情况应该很少发生，因为我们已经在API调用层面修复了这个问题
                if not walmart_ck_id:
                    logger.error(
                        f"[CK_INFO_ERROR] 无法获取CK ID，跳过CK信息保存 | "
                        f"record_id={record.id} | trace_id={trace_id}"
                    )
                    return

            # 如果API结果中已包含department_id，直接使用；否则从数据库查询
            if department_id is not None:
                update_data_dict["walmart_ck_id"] = walmart_ck_id
                update_data_dict["department_id"] = department_id
                logger.info(
                    f"[CK_INFO_SUCCESS] 从API结果直接获取CK信息 | record_id={record.id} | "
                    f"trace_id={trace_id} | walmart_ck_id={walmart_ck_id} | department_id={department_id}"
                )
            else:
                # 从数据库获取CK详细信息以获取department_id
                from app.services.walmart_ck_service_new import WalmartCKService
                ck_service = WalmartCKService(db)
                walmart_ck = ck_service.get(walmart_ck_id)

                if not walmart_ck:
                    logger.error(
                        f"[CK_INFO_ERROR] 无法获取CK详细信息 | record_id={record.id} | "
                        f"trace_id={trace_id} | walmart_ck_id={walmart_ck_id}"
                    )
                    return

                # 保存CK信息到更新数据中
                update_data_dict["walmart_ck_id"] = walmart_ck_id
                update_data_dict["department_id"] = walmart_ck.department_id

            # 获取最终的department_id用于日志记录
            final_department_id = update_data_dict.get("department_id")

            status_text = "成功" if is_success else "失败"
            logger.info(
                f"[CK_INFO_SUCCESS] 成功保存CK信息({status_text}场景) | record_id={record.id} | "
                f"trace_id={trace_id} | walmart_ck_id={walmart_ck_id} | "
                f"department_id={final_department_id} | merchant_id={record.merchant_id} | "
                f"is_success={is_success}"
            )

            # 注意：CK使用统计的记录由调用方负责，这里只保存CK信息到记录中
            # 避免重复调用record_ck_usage，因为在其他地方（如card_record_service）已经处理
            status_text = "成功" if is_success else "失败"
            logger.info(f"[CK_USAGE_INFO] CK使用统计将由调用方处理({status_text}场景) | walmart_ck_id={walmart_ck_id}")

        except Exception as e:
            logger.error(
                f"[CK_INFO_EXCEPTION] 保存CK信息时发生异常 | record_id={record.id} | "
                f"trace_id={trace_id} | exception={str(e)}"
            )
            # 不抛出异常，避免影响绑卡成功状态

    async def _send_callback_if_ready(
        self,
        record_id: int,
        merchant_id: int,
        ext_data: Optional[str],
        trace_id: str,
        db: Session
    ):
        """
        检查绑卡状态和余额信息，只有在条件满足时才发送回调

        Args:
            record_id: 记录ID
            merchant_id: 商户ID
            ext_data: 扩展数据
            trace_id: 追踪ID
            db: 数据库会话
        """
        try:
            # 检查数据库会话类型并使用对应的查询方法
            from sqlalchemy.ext.asyncio import AsyncSession
            if isinstance(db, AsyncSession):
                # 异步会话 - 使用异步查询
                from app.crud.card import get_card_record_async
                record = await get_card_record_async(db, record_id)
            else:
                # 同步会话 - 使用同步查询
                from app.crud.card_record import card_record
                record = card_record.get(db, record_id)

            if not record:
                logger.error(f"[CALLBACK_CHECK] 记录不存在 | record_id={record_id}")
                return

            # 检查绑卡是否成功
            if record.status != CardStatus.SUCCESS:
                logger.info(
                    f"[CALLBACK_SKIP] 绑卡未成功，跳过回调 | "
                    f"record_id={record_id} | status={record.status}"
                )
                return

            # 【增强】使用回调数据验证器检查数据完整性
            from app.services.callback_data_validator import CallbackDataValidator

            validator = CallbackDataValidator()

            # 预验证回调数据
            test_callback_data = validator.prepare_and_validate_callback_data(
                record=record,
                trace_id=trace_id,
                retry_count=0,  # 测试用，实际重试次数在回调时确定
                ext_data=ext_data,
                balance_fetch_success=True  # 先假设成功，实际检查在下面
            )

            validation_summary = validator.get_validation_summary()

            # 检查余额信息是否已获取
            has_balance = (
                record.balance is not None or
                record.cardBalance is not None or
                record.balanceCnt is not None
            )

            if has_balance and not validation_summary["has_errors"]:
                # 余额信息已获取且数据验证通过，可以发送回调
                logger.info(
                    f"[CALLBACK_READY] 绑卡成功且数据验证通过，发送回调 | "
                    f"record_id={record_id} | balance={record.balance} | "
                    f"cardBalance={record.cardBalance} | balanceCnt={record.balanceCnt} | "
                    f"validation_warnings={validation_summary['warning_count']}"
                )
                await self._send_callback_task(record_id, merchant_id, ext_data, trace_id)
            elif validation_summary["has_errors"]:
                # 数据验证失败，记录错误但仍尝试发送回调
                logger.error(
                    f"[CALLBACK_VALIDATION_FAILED] 回调数据验证失败，仍尝试发送 | "
                    f"record_id={record_id} | errors={validation_summary['error_count']} | "
                    f"details={validation_summary['errors']}"
                )
                await self._send_callback_task(record_id, merchant_id, ext_data, trace_id)
            else:
                # 【严格逻辑】余额信息未获取，先同步重试获取余额，只有成功获取到真实金额才发送回调
                logger.warning(
                    f"[CALLBACK_NO_BALANCE] 绑卡成功但余额未获取，尝试同步重试获取余额 | "
                    f"record_id={record_id} | trace_id={trace_id} | "
                    f"validation_warnings={validation_summary['warning_count']}"
                )

                # 尝试同步重试获取余额
                balance_retry_success = await self._retry_balance_sync(record_id, merchant_id, trace_id)

                if balance_retry_success:
                    # 余额重试成功，发送包含真实余额的回调
                    logger.info(
                        f"[CALLBACK_BALANCE_RETRY_SUCCESS] 余额重试成功，发送完整回调 | "
                        f"record_id={record_id} | trace_id={trace_id}"
                    )
                    await self._send_callback_task(record_id, merchant_id, ext_data, trace_id)
                else:
                    # 【严格逻辑】余额重试仍然失败，不发送回调，避免发送不完整数据
                    logger.warning(
                        f"[CALLBACK_NO_BALANCE_SKIP] 余额重试失败，跳过回调发送 | "
                        f"record_id={record_id} | trace_id={trace_id} | "
                        f"reason=未获取到真实金额"
                    )
                    # 记录到绑卡日志，便于后续处理
                    try:
                        from app.services.binding_log_service import BindingLogService
                        from app.models.binding_log import LogLevel

                        binding_log_service = BindingLogService(db)
                        await binding_log_service.log_system(
                            db=db,
                            card_record_id=str(record_id),
                            message="绑卡成功但未获取到真实金额，跳过回调发送",
                            log_level=LogLevel.WARNING,
                            details={
                                "trace_id": trace_id,
                                "reason": "余额获取重试失败",
                                "status": "绑卡成功但无回调",
                                "timestamp": datetime.now().isoformat()
                            }
                        )
                    except Exception as log_error:
                        logger.error(f"[CALLBACK_SKIP_LOG_ERROR] 记录跳过回调日志失败: {log_error}")

                    # 不发送回调，确保商户只收到包含真实金额的完整数据

                    # 【新增】安排后台异步重试，继续尝试获取余额，一旦成功就发送回调
                    try:
                        from app.services.async_balance_retry_service import async_balance_retry_service
                        await async_balance_retry_service.schedule_balance_retry(
                            record_id=record_id,
                            merchant_id=merchant_id,
                            ext_data=ext_data,
                            trace_id=trace_id
                        )
                        logger.info(
                            f"[CALLBACK_ASYNC_RETRY_SCHEDULED] 已安排后台异步余额重试 | "
                            f"record_id={record_id} | trace_id={trace_id}"
                        )
                    except Exception as async_retry_error:
                        logger.error(
                            f"[CALLBACK_ASYNC_RETRY_ERROR] 安排后台异步余额重试失败 | "
                            f"record_id={record_id} | error={async_retry_error}"
                        )

        except Exception as e:
            logger.error(
                f"[CALLBACK_CHECK_ERROR] 检查回调条件时发生异常 | "
                f"record_id={record_id} | error={str(e)}"
            )
            # 发生异常时仍尝试发送回调，避免回调丢失
            try:
                await self._send_callback_task(record_id, merchant_id, ext_data, trace_id)
            except Exception as callback_error:
                logger.error(
                    f"[CALLBACK_FALLBACK_ERROR] 回调发送失败 | "
                    f"record_id={record_id} | error={str(callback_error)}"
                )

    async def _retry_balance_sync(
        self,
        record_id: int,
        merchant_id: int,
        trace_id: str,
        max_retries: int = 3,  # 增加重试次数，因为不发送回调需要更努力获取余额
        retry_delay: float = 1.5  # 增加重试间隔，避免过于频繁的API调用
    ) -> bool:
        """
        同步重试获取余额

        Args:
            record_id: 记录ID
            merchant_id: 商户ID
            trace_id: 追踪ID
            max_retries: 最大重试次数
            retry_delay: 重试间隔（秒）

        Returns:
            bool: 是否成功获取余额
        """
        logger.info(
            f"[BALANCE_SYNC_RETRY_START] 开始同步重试获取余额 | "
            f"record_id={record_id} | trace_id={trace_id} | max_retries={max_retries}"
        )

        for attempt in range(max_retries):
            try:
                # 使用异步余额重试服务的核心逻辑
                from app.services.async_balance_retry_service import async_balance_retry_service

                # 尝试获取余额
                success = await async_balance_retry_service._try_fetch_balance(
                    record_id=record_id,
                    merchant_id=merchant_id,
                    trace_id=trace_id
                )

                if success:
                    logger.info(
                        f"[BALANCE_SYNC_RETRY_SUCCESS] 同步重试获取余额成功 | "
                        f"record_id={record_id} | trace_id={trace_id} | attempt={attempt + 1}"
                    )
                    return True
                else:
                    logger.warning(
                        f"[BALANCE_SYNC_RETRY_ATTEMPT_FAILED] 同步重试获取余额失败 | "
                        f"record_id={record_id} | trace_id={trace_id} | attempt={attempt + 1}/{max_retries}"
                    )

                    # 如果不是最后一次尝试，等待一段时间再重试
                    if attempt < max_retries - 1:
                        await asyncio.sleep(retry_delay)

            except Exception as e:
                logger.error(
                    f"[BALANCE_SYNC_RETRY_EXCEPTION] 同步重试获取余额异常 | "
                    f"record_id={record_id} | trace_id={trace_id} | attempt={attempt + 1} | error={e}"
                )

                # 如果不是最后一次尝试，等待一段时间再重试
                if attempt < max_retries - 1:
                    await asyncio.sleep(retry_delay)

        logger.warning(
            f"[BALANCE_SYNC_RETRY_FAILED] 同步重试获取余额最终失败 | "
            f"record_id={record_id} | trace_id={trace_id} | max_retries={max_retries}"
        )
        return False
