import { http } from "../request";
import { API_URLS } from "./config";

const { ck_monitor } = API_URLS;

/**
 * CK监控相关API
 */
export const ckMonitorApi = {
  /**
   * 获取CK负载均衡状态
   * @param {Object} params - 查询参数
   * @param {number} params.merchant_id - 商户ID（可选）
   * @returns {Promise} API响应
   */
  getStatus(params = {}) {
    return http.get(ck_monitor.STATUS, { params }).then((res) => {
      return res.data || res;
    });
  },

  /**
   * 获取CK服务健康状态
   * @returns {Promise} API响应
   */
  getHealth() {
    return http.get(ck_monitor.HEALTH).then((res) => {
      return res.data || res;
    });
  },

  /**
   * 测试CK负载均衡功能
   * @param {Object} params - 测试参数
   * @param {number} params.merchant_id - 商户ID（可选）
   * @param {number} params.test_rounds - 测试轮数（默认10）
   * @returns {Promise} API响应
   */
  testLoadBalance(params = {}) {
    return http
      .post(ck_monitor.TEST_LOAD_BALANCE, null, { params })
      .then((res) => {
        return res.data || res;
      });
  },

  /**
   * 同步CK数据到Redis (已废弃)
   * @deprecated Redis同步功能已移除，SimplifiedCKService使用数据库直连
   * @param {Object} params - 同步参数
   * @param {number} params.merchant_id - 商户ID（可选）
   * @returns {Promise} API响应
   */
  syncRedis(params = {}) {
    console.warn("syncRedis API已废弃：SimplifiedCKService不需要Redis同步");
    return Promise.resolve({
      status: "success",
      message: "SimplifiedCKService不需要Redis同步，使用数据库直连",
      note: "Redis同步功能已移除",
    });
  },

  /**
   * 获取CK使用统计
   * @param {Object} params - 查询参数
   * @param {number} params.merchant_id - 商户ID（可选）
   * @param {number} params.days - 统计天数（默认7天）
   * @returns {Promise} API响应
   */
  getStatistics(params = {}) {
    return http.get(ck_monitor.STATISTICS, { params }).then((res) => {
      return res.data || res;
    });
  },
};
