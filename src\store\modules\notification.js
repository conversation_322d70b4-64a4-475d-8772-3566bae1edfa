import { defineStore } from "pinia";
import { notificationApi } from "@/api/modules/notification";

export const useNotificationStore = defineStore("notification", {
  state: () => ({
    notifications: [],
    unreadCount: 0,
    loading: false,
    error: null,
  }),

  getters: {
    allNotifications: (state) => state.notifications,
    hasUnread: (state) => state.unreadCount > 0,
    isLoading: (state) => state.loading,
  },

  actions: {
    // 获取通知列表
    async fetchNotifications(params) {
      this.loading = true;
      try {
        const data = await notificationApi.getList(params);

        // 现在后端返回的格式已经是正确的分页格式
        let responseData = data;

        // 确保通知项目是数组
        if (responseData.items && Array.isArray(responseData.items)) {
          // 处理每个通知的日期格式和状态字段
          this.notifications = responseData.items.map((item) => {
            // 确保created_at字段存在
            if (!item.created_at && item.createdAt) {
              item.created_at = item.createdAt;
            } else if (!item.created_at) {
              item.created_at = new Date().toISOString();
            }

            // 确保createdAt字段存在
            if (!item.createdAt && item.created_at) {
              item.createdAt = item.created_at;
            }

            // 统一处理read字段：将后端的status字段转换为前端的read布尔字段
            // 注意：后端返回的status是大写的 'READ' 或 'UNREAD'
            if (item.status !== undefined) {
              item.read = item.status.toLowerCase() === "read";
            } else if (item.read === undefined) {
              item.read = false; // 默认为未读
            }

            // 确保状态字段一致性，统一使用小写
            if (
              item.read &&
              item.status &&
              item.status.toLowerCase() !== "read"
            ) {
              item.status = "read";
            } else if (
              !item.read &&
              item.status &&
              item.status.toLowerCase() !== "unread"
            ) {
              item.status = "unread";
            }

            console.log("处理后的通知项:", item);
            return item;
          });
        } else {
          console.warn("通知数据格式不正确:", responseData);
          this.notifications = [];
        }

        // 如果有未读数量，也更新
        if (responseData.unread_count !== undefined) {
          this.unreadCount = responseData.unread_count;
        }

        return data;
      } catch (error) {
        console.error("获取通知失败:", error);
        this.error = error.message;
        return null;
      } finally {
        this.loading = false;
      }
    },

    // 获取未读通知数量
    async fetchUnreadCount() {
      try {
        const response = await notificationApi.getUnreadCount();
        let count = 0;

        // 处理可能的嵌套结构
        if (response && response.unread_count !== undefined) {
          count = response.unread_count;
        } else if (
          response &&
          response.data &&
          response.data.unread_count !== undefined
        ) {
          count = response.data.unread_count;
        } else if (response && response.count !== undefined) {
          count = response.count;
        } else if (
          response &&
          response.data &&
          response.data.count !== undefined
        ) {
          count = response.data.count;
        }

        this.unreadCount = count;
        console.log("未读通知数量:", count);
        return count;
      } catch (error) {
        this.error = error.message;
        return 0;
      }
    },

    // 标记通知为已读
    async markAsRead(id) {
      try {
        await notificationApi.markAsRead(id);
        this.notifications = this.notifications.map((item) =>
          item.id === id ? { ...item, read: true, status: "read" } : item
        );
        this.unreadCount = Math.max(0, this.unreadCount - 1);
        return true;
      } catch (error) {
        this.error = error.message;
        return false;
      }
    },

    // 标记通知为未读
    async markAsUnread(id) {
      try {
        await notificationApi.markAsUnread(id);
        // 更新本地状态
        this.notifications = this.notifications.map((item) =>
          item.id === id ? { ...item, read: false, status: "unread" } : item
        );
        this.unreadCount = this.unreadCount + 1;
        return true;
      } catch (error) {
        this.error = error.message;
        return false;
      }
    },

    // 标记所有通知为已读
    async markAllAsRead() {
      try {
        await notificationApi.markAllAsRead();
        // 更新所有通知状态为已读
        this.notifications = this.notifications.map((item) => ({
          ...item,
          read: true,
          status: "read",
        }));
        this.unreadCount = 0;
        return true;
      } catch (error) {
        this.error = error.message;
        return false;
      }
    },

    // 创建通知
    async createNotification(data) {
      this.loading = true;
      try {
        const notification = await notificationApi.create(data);
        this.notifications = [notification, ...this.notifications];
        return notification;
      } catch (error) {
        this.error = error.message;
        return null;
      } finally {
        this.loading = false;
      }
    },

    // 删除通知
    async deleteNotification(id) {
      try {
        await notificationApi.delete(id);
        this.notifications = this.notifications.filter(
          (item) => item.id !== id
        );
        return true;
      } catch (error) {
        this.error = error.message;
        return false;
      }
    },

    // 重置状态
    resetState() {
      this.notifications = [];
      this.unreadCount = 0;
      this.loading = false;
      this.error = null;
    },
  },
});
