#!/usr/bin/env python3
"""
诊断回调时序问题的深度分析工具
"""
import sys
import asyncio
import uuid
from pathlib import Path
from datetime import datetime

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from app.db.session import SessionLocal
from app.crud import card as card_crud
from app.core.logging import get_logger

logger = get_logger("callback_timing_diagnosis")


async def diagnose_record_visibility():
    """诊断记录可见性问题"""
    print("🔍 诊断记录可见性问题...")
    
    # 使用最近创建的记录ID
    recent_record_id = "1e461906-1ffb-4d99-9fc8-************"
    
    print(f"   目标记录ID: {recent_record_id}")
    
    # 测试1: 使用不同的数据库会话查询
    print("\n1. 测试不同数据库会话的查询结果...")
    
    sessions_results = []
    for i in range(3):
        try:
            with SessionLocal() as db:
                # 设置不同的事务隔离级别
                if i == 0:
                    db.execute("SET SESSION TRANSACTION ISOLATION LEVEL READ COMMITTED")
                    isolation = "READ COMMITTED"
                elif i == 1:
                    db.execute("SET SESSION TRANSACTION ISOLATION LEVEL READ UNCOMMITTED")
                    isolation = "READ UNCOMMITTED"
                else:
                    db.execute("SET SESSION TRANSACTION ISOLATION LEVEL REPEATABLE READ")
                    isolation = "REPEATABLE READ"
                
                # 查询记录
                record = card_crud.get(db, recent_record_id)
                result = {
                    "session": i + 1,
                    "isolation": isolation,
                    "found": record is not None,
                    "status": record.status if record else None,
                    "callback_status": record.callback_status if record else None
                }
                sessions_results.append(result)
                print(f"   会话{i+1} ({isolation}): {'找到' if record else '未找到'}")
                
        except Exception as e:
            print(f"   会话{i+1} 查询失败: {e}")
            sessions_results.append({
                "session": i + 1,
                "isolation": isolation,
                "error": str(e)
            })
    
    # 测试2: 直接SQL查询
    print("\n2. 直接SQL查询验证...")
    try:
        with SessionLocal() as db:
            from sqlalchemy import text
            
            # 查询记录是否存在
            result = db.execute(text(
                "SELECT id, status, callback_status, created_at FROM card_records WHERE id = :record_id"
            ), {"record_id": recent_record_id}).fetchone()
            
            if result:
                print(f"   ✅ SQL直接查询找到记录:")
                print(f"      ID: {result[0]}")
                print(f"      状态: {result[1]}")
                print(f"      回调状态: {result[2]}")
                print(f"      创建时间: {result[3]}")
            else:
                print("   ❌ SQL直接查询未找到记录")
                
    except Exception as e:
        print(f"   ❌ SQL查询失败: {e}")
    
    # 测试3: 检查数据库连接配置
    print("\n3. 检查数据库连接配置...")
    try:
        with SessionLocal() as db:
            # 检查当前事务隔离级别
            isolation_result = db.execute(text("SELECT @@transaction_isolation")).fetchone()
            print(f"   当前事务隔离级别: {isolation_result[0] if isolation_result else 'Unknown'}")
            
            # 检查自动提交状态
            autocommit_result = db.execute(text("SELECT @@autocommit")).fetchone()
            print(f"   自动提交状态: {autocommit_result[0] if autocommit_result else 'Unknown'}")
            
            # 检查连接ID
            connection_id = db.execute(text("SELECT CONNECTION_ID()")).fetchone()
            print(f"   连接ID: {connection_id[0] if connection_id else 'Unknown'}")
            
    except Exception as e:
        print(f"   ❌ 配置检查失败: {e}")
    
    return sessions_results


async def test_record_creation_and_query():
    """测试记录创建和查询的时序"""
    print("\n🧪 测试记录创建和查询时序...")
    
    # 创建一个测试记录
    test_record_data = {
        "id": str(uuid.uuid4()),
        "merchant_id": 2,
        "card_number": "2326000000TEST",
        "card_password": "123456",
        "amount": 10000,
        "status": "success",
        "callback_status": "pending",
        "ip_address": "127.0.0.1",
        "trace_id": "test-timing-diagnosis"
    }
    
    print(f"   创建测试记录: {test_record_data['id']}")
    
    try:
        # 会话1: 创建记录
        with SessionLocal() as db1:
            from app.models.card_record import CardRecord
            
            record = CardRecord(**test_record_data)
            db1.add(record)
            db1.commit()
            print("   ✅ 记录创建成功")
        
        # 立即查询（模拟回调场景）
        print("   立即查询记录...")
        for attempt in range(5):
            with SessionLocal() as db2:
                db2.expire_all()
                found_record = card_crud.get(db2, test_record_data["id"])
                
                if found_record:
                    print(f"   ✅ 第{attempt + 1}次查询成功找到记录")
                    break
                else:
                    print(f"   ❌ 第{attempt + 1}次查询未找到记录")
                    if attempt < 4:
                        await asyncio.sleep(0.1)
        
        # 清理测试记录
        with SessionLocal() as db3:
            test_record = card_crud.get(db3, test_record_data["id"])
            if test_record:
                db3.delete(test_record)
                db3.commit()
                print("   🧹 测试记录已清理")
                
    except Exception as e:
        print(f"   ❌ 测试失败: {e}")


async def analyze_database_performance():
    """分析数据库性能"""
    print("\n📊 分析数据库性能...")
    
    try:
        with SessionLocal() as db:
            from sqlalchemy import text
            
            # 检查数据库版本
            version = db.execute(text("SELECT VERSION()")).fetchone()
            print(f"   数据库版本: {version[0] if version else 'Unknown'}")
            
            # 检查连接数
            connections = db.execute(text("SHOW STATUS LIKE 'Threads_connected'")).fetchone()
            print(f"   当前连接数: {connections[1] if connections else 'Unknown'}")
            
            # 检查慢查询
            slow_queries = db.execute(text("SHOW STATUS LIKE 'Slow_queries'")).fetchone()
            print(f"   慢查询数量: {slow_queries[1] if slow_queries else 'Unknown'}")
            
            # 检查表状态
            table_status = db.execute(text(
                "SELECT COUNT(*) as total_records, "
                "COUNT(CASE WHEN status = 'success' THEN 1 END) as success_records, "
                "COUNT(CASE WHEN callback_status = 'pending' THEN 1 END) as pending_callbacks "
                "FROM card_records"
            )).fetchone()
            
            if table_status:
                print(f"   总记录数: {table_status[0]}")
                print(f"   成功记录数: {table_status[1]}")
                print(f"   待回调记录数: {table_status[2]}")
                
    except Exception as e:
        print(f"   ❌ 性能分析失败: {e}")


async def main():
    """主函数"""
    print("=" * 60)
    print("回调时序问题深度诊断")
    print("=" * 60)
    
    try:
        # 诊断记录可见性
        await diagnose_record_visibility()
        
        # 测试记录创建和查询时序
        await test_record_creation_and_query()
        
        # 分析数据库性能
        await analyze_database_performance()
        
        print("\n" + "=" * 60)
        print("📋 诊断总结:")
        print("=" * 60)
        print("1. 检查不同事务隔离级别下的查询结果")
        print("2. 验证SQL直接查询是否能找到记录")
        print("3. 测试记录创建后的立即查询")
        print("4. 分析数据库性能指标")
        print("\n💡 建议:")
        print("- 如果SQL直接查询能找到记录，但CRUD查询找不到，可能是ORM缓存问题")
        print("- 如果不同隔离级别结果不同，需要调整数据库配置")
        print("- 如果创建后立即查询失败，需要增加等待时间或改进事务管理")
        
        return 0
        
    except Exception as e:
        print(f"\n💥 诊断过程中发生异常: {e}")
        return 1


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
