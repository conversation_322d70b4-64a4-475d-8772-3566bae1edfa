"""
过滤器工厂模块 - 用于统一处理查询过滤条件
"""
from typing import Any, Dict, Optional, Protocol
from sqlalchemy.orm import Query
from sqlalchemy import and_
from datetime import datetime
import logging

logger = logging.getLogger(__name__)


class FilterStrategy(Protocol):
    """过滤策略协议"""
    
    def apply(self, query: Query, value: Any) -> Query:
        """应用过滤条件"""
        ...


class LikeFilterStrategy:
    """模糊匹配过滤策略"""
    
    def __init__(self, field):
        self.field = field
    
    def apply(self, query: Query, value: Any) -> Query:
        if value:
            return query.filter(self.field.like(f"%{value}%"))
        return query


class EqualFilterStrategy:
    """精确匹配过滤策略"""
    
    def __init__(self, field):
        self.field = field
    
    def apply(self, query: Query, value: Any) -> Query:
        if value is not None:
            return query.filter(self.field == value)
        return query


class DateTimeRangeFilterStrategy:
    """日期时间范围过滤策略"""
    
    def __init__(self, field):
        self.field = field
    
    def apply_start_time(self, query: Query, start_time: datetime) -> Query:
        """应用开始时间过滤"""
        if start_time:
            try:
                return query.filter(self.field >= start_time)
            except Exception:
                # 如果字段是字符串类型，转换为字符串格式比较
                start_time_str = start_time.strftime("%Y-%m-%d %H:%M:%S")
                return query.filter(self.field >= start_time_str)
        return query
    
    def apply_end_time(self, query: Query, end_time: datetime) -> Query:
        """应用结束时间过滤"""
        if end_time:
            try:
                return query.filter(self.field <= end_time)
            except Exception:
                # 如果字段是字符串类型，转换为字符串格式比较
                end_time_str = end_time.strftime("%Y-%m-%d %H:%M:%S")
                return query.filter(self.field <= end_time_str)
        return query
    
    def apply_range(self, query: Query, start_time: datetime, end_time: datetime) -> Query:
        """应用时间范围过滤"""
        if start_time and end_time:
            try:
                return query.filter(and_(
                    self.field >= start_time,
                    self.field <= end_time
                ))
            except Exception:
                # 如果字段是字符串类型，转换为字符串格式比较
                start_time_str = start_time.strftime("%Y-%m-%d %H:%M:%S")
                end_time_str = end_time.strftime("%Y-%m-%d %H:%M:%S")
                return query.filter(and_(
                    self.field >= start_time_str,
                    self.field <= end_time_str
                ))
        elif start_time:
            return self.apply_start_time(query, start_time)
        elif end_time:
            return self.apply_end_time(query, end_time)
        return query


class FilterFactory:
    """过滤器工厂类"""
    
    def __init__(self):
        self.strategies = {}
    
    def register_strategy(self, filter_name: str, strategy: FilterStrategy):
        """注册过滤策略"""
        self.strategies[filter_name] = strategy
    
    def apply_filters(self, query: Query, filters: Dict[str, Any]) -> Query:
        """应用所有过滤条件"""
        if not filters:
            return query
            
        for filter_name, value in filters.items():
            if filter_name in self.strategies and value is not None:
                try:
                    query = self.strategies[filter_name].apply(query, value)
                except Exception as e:
                    logger.warning(f"应用过滤条件 {filter_name} 失败: {e}")
                    
        return query


class CardRecordFilterFactory(FilterFactory):
    """卡记录过滤器工厂"""
    
    def __init__(self, model):
        super().__init__()
        self.model = model
        self._setup_strategies()
    
    def _setup_strategies(self):
        """设置过滤策略"""
        # 模糊匹配策略
        self.register_strategy("card_number", LikeFilterStrategy(self.model.card_number))
        self.register_strategy("user_id", LikeFilterStrategy(self.model.user_id))
        
        # 精确匹配策略
        self.register_strategy("status", EqualFilterStrategy(self.model.status))
        self.register_strategy("merchant_id", EqualFilterStrategy(self.model.merchant_id))
        self.register_strategy("department_id", EqualFilterStrategy(self.model.department_id))
        self.register_strategy("walmart_ck_id", EqualFilterStrategy(self.model.walmart_ck_id))
    
    def apply_time_range_filters(self, query: Query, filters: Dict[str, Any]) -> Query:
        """应用时间范围过滤"""
        time_strategy = DateTimeRangeFilterStrategy(self.model.request_time)
        
        start_time = filters.get("start_time")
        end_time = filters.get("end_time")
        
        return time_strategy.apply_range(query, start_time, end_time)
    
    def apply_created_time_filters(self, query: Query, filters: Dict[str, Any]) -> Query:
        """应用创建时间范围过滤"""
        time_strategy = DateTimeRangeFilterStrategy(self.model.created_at)
        
        start_time = filters.get("start_time")
        end_time = filters.get("end_time")
        
        return time_strategy.apply_range(query, start_time, end_time)


def create_card_record_filter_factory(model):
    """创建卡记录过滤器工厂"""
    return CardRecordFilterFactory(model)
