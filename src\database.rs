// 数据库访问层
use crate::config::DatabaseConfig;
use crate::errors::{CallbackError, CallbackResult};
use crate::models::{<PERSON><PERSON><PERSON><PERSON>, Merchant, CallbackStatus, db_constants};
use sqlx::{MySqlPool, Row};
use std::time::Duration;
use tracing::{info, warn, error};

#[derive(Debu<PERSON>, <PERSON>lone)]
pub struct DatabaseManager {
    pool: MySqlPool,
}

impl DatabaseManager {
    pub async fn new(config: &DatabaseConfig) -> CallbackResult<Self> {
        info!("初始化数据库连接池...");
        
        let pool = sqlx::mysql::MySqlPoolOptions::new()
            .max_connections(config.maximum_connections)
            .min_connections(config.minimum_connections)
            .acquire_timeout(config.connect_timeout())
            .idle_timeout(config.idle_timeout())
            .connect(&config.connection_url)
            .await?;
            
        info!("数据库连接池初始化成功");
        
        Ok(Self { pool })
    }
    
    /// 根据记录ID获取卡记录
    pub async fn get_card_record(&self, record_id: &str) -> CallbackResult<Option<CardRecord>> {
        let record = sqlx::query_as::<_, CardRecord>(
            r#"
            SELECT
                id as record_id,
                merchant_id as merchant_identifier,
                merchant_order_id as order_identifier,
                card_number as card_num,
                status as current_status,
                callback_status as callback_state,
                amount as order_amount,
                created_at as creation_time,
                updated_at as modification_time
            FROM card_records
            WHERE id = ?
            "#
        )
        .bind(record_id)
        .fetch_optional(&self.pool)
        .await?;

        Ok(record)
    }
    
    /// 根据商户ID和订单ID获取卡记录
    pub async fn get_card_record_by_order(
        &self, 
        merchant_id: u64, 
        merchant_order_id: &str
    ) -> CallbackResult<Option<CardRecord>> {
        let record = sqlx::query_as!(
            CardRecord,
            r#"
            SELECT 
                id as "record_id",
                merchant_id as "merchant_identifier: u64", 
                merchant_order_id as "order_identifier",
                card_number as "card_num",
                status as "current_status",
                callback_status as "callback_state",
                amount as "order_amount: u64",
                created_at as "creation_time",
                updated_at as "modification_time"
            FROM card_records 
            WHERE merchant_id = ? AND merchant_order_id = ?
            "#,
            merchant_id,
            merchant_order_id
        )
        .fetch_optional(&self.pool)
        .await?;
        
        Ok(record)
    }
    
    /// 更新回调状态
    pub async fn update_callback_status(
        &self,
        record_id: &str,
        status: CallbackStatus
    ) -> CallbackResult<bool> {
        let result = sqlx::query!(
            "UPDATE card_records SET callback_status = ?, updated_at = NOW() WHERE id = ?",
            status.as_str(),
            record_id
        )
        .execute(&self.pool)
        .await?;
        
        Ok(result.rows_affected() > 0)
    }
    
    /// 获取商户信息
    pub async fn get_merchant(&self, merchant_id: u64) -> CallbackResult<Option<Merchant>> {
        let merchant = sqlx::query_as!(
            Merchant,
            r#"
            SELECT 
                id as "merchant_id: u64",
                name as "merchant_name",
                callback_url as "callback_endpoint"
            FROM merchants 
            WHERE id = ?
            "#,
            merchant_id
        )
        .fetch_optional(&self.pool)
        .await?;
        
        Ok(merchant)
    }
    
    /// 检查回调是否已成功
    pub async fn is_callback_successful(&self, record_id: &str) -> CallbackResult<bool> {
        let row = sqlx::query!(
            "SELECT callback_status FROM card_records WHERE id = ?",
            record_id
        )
        .fetch_optional(&self.pool)
        .await?;
        
        match row {
            Some(record) => Ok(record.callback_status == db_constants::CALLBACK_STATUS_SUCCESS),
            None => Ok(false),
        }
    }
    
    /// 获取需要重试的回调记录
    pub async fn get_failed_callbacks(&self, limit: u32) -> CallbackResult<Vec<CardRecord>> {
        let records = sqlx::query_as!(
            CardRecord,
            r#"
            SELECT 
                id as "record_id",
                merchant_id as "merchant_identifier: u64", 
                merchant_order_id as "order_identifier",
                card_number as "card_num",
                status as "current_status",
                callback_status as "callback_state",
                amount as "order_amount: u64",
                created_at as "creation_time",
                updated_at as "modification_time"
            FROM card_records 
            WHERE callback_status = ? 
            AND updated_at < DATE_SUB(NOW(), INTERVAL 5 MINUTE)
            ORDER BY updated_at ASC
            LIMIT ?
            "#,
            db_constants::CALLBACK_STATUS_FAILED,
            limit
        )
        .fetch_all(&self.pool)
        .await?;
        
        Ok(records)
    }
    
    /// 健康检查
    pub async fn health_check(&self) -> CallbackResult<bool> {
        let result = sqlx::query("SELECT 1 as health")
            .fetch_one(&self.pool)
            .await;
            
        match result {
            Ok(_) => Ok(true),
            Err(e) => {
                error!("数据库健康检查失败: {}", e);
                Ok(false)
            }
        }
    }
    
    /// 获取连接池状态
    pub fn pool_status(&self) -> (u32, u32) {
        (self.pool.size(), self.pool.num_idle())
    }
    
    /// 关闭连接池
    pub async fn close(&self) {
        info!("关闭数据库连接池...");
        self.pool.close().await;
        info!("数据库连接池已关闭");
    }
}

// 数据库事务支持
impl DatabaseManager {
    /// 在事务中更新回调状态
    pub async fn update_callback_status_tx(
        &self,
        record_id: &str,
        old_status: CallbackStatus,
        new_status: CallbackStatus
    ) -> CallbackResult<bool> {
        let mut tx = self.pool.begin().await?;
        
        // 检查当前状态
        let current_status = sqlx::query!(
            "SELECT callback_status FROM card_records WHERE id = ? FOR UPDATE",
            record_id
        )
        .fetch_optional(&mut *tx)
        .await?;
        
        match current_status {
            Some(record) if record.callback_status == old_status.as_str() => {
                // 状态匹配，执行更新
                let result = sqlx::query!(
                    "UPDATE card_records SET callback_status = ?, updated_at = NOW() WHERE id = ?",
                    new_status.as_str(),
                    record_id
                )
                .execute(&mut *tx)
                .await?;
                
                tx.commit().await?;
                Ok(result.rows_affected() > 0)
            }
            Some(record) => {
                // 状态不匹配，回滚事务
                tx.rollback().await?;
                warn!("回调状态不匹配: 期望 {}, 实际 {}", old_status.as_str(), record.callback_status);
                Ok(false)
            }
            None => {
                // 记录不存在
                tx.rollback().await?;
                warn!("记录不存在: {}", record_id);
                Ok(false)
            }
        }
    }
}
