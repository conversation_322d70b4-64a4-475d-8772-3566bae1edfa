"""
回调监控服务
提供回调性能监控和统计功能
"""
import asyncio
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional
from dataclasses import dataclass, asdict
from collections import defaultdict, deque

from sqlalchemy.orm import Session
from sqlalchemy import func, and_

from app.core.logging import get_logger
from app.models.card_record import CardRecord, CallbackStatus
from app.db.session import SessionLocal

logger = get_logger("callback_monitor")


@dataclass
class CallbackMetrics:
    """回调指标数据类"""
    total_callbacks: int = 0
    successful_callbacks: int = 0
    failed_callbacks: int = 0
    pending_callbacks: int = 0
    success_rate: float = 0.0
    average_retry_count: float = 0.0
    max_retry_count: int = 0
    total_processing_time: float = 0.0
    average_processing_time: float = 0.0


@dataclass
class CallbackPerformanceStats:
    """回调性能统计"""
    timestamp: datetime
    metrics: CallbackMetrics
    period_minutes: int = 5


class CallbackMonitorService:
    """回调监控服务"""
    
    def __init__(self, max_history_size: int = 288):  # 24小时的5分钟间隔数据
        self.max_history_size = max_history_size
        self.performance_history: deque = deque(maxlen=max_history_size)
        self.real_time_metrics = defaultdict(int)
        self.monitoring_active = False
        self._monitor_task: Optional[asyncio.Task] = None
    
    async def start_monitoring(self, interval_seconds: int = 300):  # 5分钟间隔
        """启动监控"""
        if self.monitoring_active:
            logger.warning("监控已经在运行中")
            return
        
        self.monitoring_active = True
        self._monitor_task = asyncio.create_task(
            self._monitoring_loop(interval_seconds)
        )
        logger.info(f"回调监控服务已启动，监控间隔: {interval_seconds}秒")
    
    async def stop_monitoring(self):
        """停止监控"""
        self.monitoring_active = False
        if self._monitor_task:
            self._monitor_task.cancel()
            try:
                await self._monitor_task
            except asyncio.CancelledError:
                pass
        logger.info("回调监控服务已停止")
    
    async def _monitoring_loop(self, interval_seconds: int):
        """监控循环"""
        while self.monitoring_active:
            try:
                await self._collect_metrics()
                await asyncio.sleep(interval_seconds)
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"监控循环异常: {e}")
                await asyncio.sleep(interval_seconds)
    
    async def _collect_metrics(self):
        """收集指标数据"""
        try:
            with SessionLocal() as db:
                # 计算过去5分钟的指标
                end_time = datetime.now()
                start_time = end_time - timedelta(minutes=5)
                
                metrics = await self._calculate_period_metrics(db, start_time, end_time)
                
                # 保存到历史记录
                stats = CallbackPerformanceStats(
                    timestamp=end_time,
                    metrics=metrics,
                    period_minutes=5
                )
                self.performance_history.append(stats)
                
                # 记录关键指标
                if metrics.total_callbacks > 0:
                    logger.info(
                        f"回调监控 - 总数: {metrics.total_callbacks}, "
                        f"成功率: {metrics.success_rate:.2f}%, "
                        f"平均重试: {metrics.average_retry_count:.2f}"
                    )
                
        except Exception as e:
            logger.error(f"收集指标异常: {e}")
    
    async def _calculate_period_metrics(
        self, db: Session, start_time: datetime, end_time: datetime
    ) -> CallbackMetrics:
        """计算指定时间段的指标"""
        # 查询时间段内的回调记录
        query = db.query(CardRecord).filter(
            and_(
                CardRecord.callback_time >= start_time,
                CardRecord.callback_time <= end_time,
                CardRecord.callback_status.isnot(None)
            )
        )
        
        records = query.all()
        
        if not records:
            return CallbackMetrics()
        
        # 计算指标
        total_callbacks = len(records)
        successful_callbacks = sum(
            1 for r in records if r.callback_status == CallbackStatus.SUCCESS
        )
        failed_callbacks = sum(
            1 for r in records if r.callback_status == CallbackStatus.FAILED
        )
        pending_callbacks = sum(
            1 for r in records if r.callback_status == CallbackStatus.PENDING
        )
        
        success_rate = (successful_callbacks / total_callbacks * 100) if total_callbacks > 0 else 0
        
        # 计算重试统计
        retry_counts = [r.retry_count or 0 for r in records]
        average_retry_count = sum(retry_counts) / len(retry_counts) if retry_counts else 0
        max_retry_count = max(retry_counts) if retry_counts else 0
        
        # 计算处理时间（如果有相关字段）
        processing_times = []
        for record in records:
            if record.callback_time and record.created_at:
                processing_time = (record.callback_time - record.created_at).total_seconds()
                processing_times.append(processing_time)
        
        total_processing_time = sum(processing_times)
        average_processing_time = (
            total_processing_time / len(processing_times) if processing_times else 0
        )
        
        return CallbackMetrics(
            total_callbacks=total_callbacks,
            successful_callbacks=successful_callbacks,
            failed_callbacks=failed_callbacks,
            pending_callbacks=pending_callbacks,
            success_rate=success_rate,
            average_retry_count=average_retry_count,
            max_retry_count=max_retry_count,
            total_processing_time=total_processing_time,
            average_processing_time=average_processing_time,
        )
    
    def get_current_metrics(self) -> Optional[CallbackMetrics]:
        """获取当前指标"""
        if not self.performance_history:
            return None
        return self.performance_history[-1].metrics
    
    def get_metrics_history(self, hours: int = 24) -> List[CallbackPerformanceStats]:
        """获取指定小时数的历史指标"""
        cutoff_time = datetime.now() - timedelta(hours=hours)
        return [
            stats for stats in self.performance_history
            if stats.timestamp >= cutoff_time
        ]
    
    def get_performance_summary(self, hours: int = 24) -> Dict[str, Any]:
        """获取性能摘要"""
        history = self.get_metrics_history(hours)
        if not history:
            return {"error": "没有可用的历史数据"}
        
        # 计算汇总统计
        total_callbacks = sum(stats.metrics.total_callbacks for stats in history)
        total_successful = sum(stats.metrics.successful_callbacks for stats in history)
        total_failed = sum(stats.metrics.failed_callbacks for stats in history)
        
        overall_success_rate = (
            (total_successful / total_callbacks * 100) if total_callbacks > 0 else 0
        )
        
        # 计算平均指标
        avg_retry_count = sum(
            stats.metrics.average_retry_count for stats in history
        ) / len(history)
        
        avg_processing_time = sum(
            stats.metrics.average_processing_time for stats in history
        ) / len(history)
        
        # 找出峰值
        peak_callbacks = max(stats.metrics.total_callbacks for stats in history)
        min_success_rate = min(stats.metrics.success_rate for stats in history)
        max_retry_count = max(stats.metrics.max_retry_count for stats in history)
        
        return {
            "period_hours": hours,
            "total_callbacks": total_callbacks,
            "total_successful": total_successful,
            "total_failed": total_failed,
            "overall_success_rate": round(overall_success_rate, 2),
            "average_retry_count": round(avg_retry_count, 2),
            "average_processing_time": round(avg_processing_time, 2),
            "peak_callbacks_per_period": peak_callbacks,
            "min_success_rate": round(min_success_rate, 2),
            "max_retry_count": max_retry_count,
            "data_points": len(history),
        }
    
    async def get_real_time_stats(self) -> Dict[str, Any]:
        """获取实时统计"""
        try:
            with SessionLocal() as db:
                # 查询当前待处理的回调
                pending_count = db.query(CardRecord).filter(
                    CardRecord.callback_status == CallbackStatus.PENDING
                ).count()
                
                # 查询最近1小时的统计
                one_hour_ago = datetime.now() - timedelta(hours=1)
                recent_metrics = await self._calculate_period_metrics(
                    db, one_hour_ago, datetime.now()
                )
                
                return {
                    "pending_callbacks": pending_count,
                    "last_hour": asdict(recent_metrics),
                    "monitoring_active": self.monitoring_active,
                    "history_size": len(self.performance_history),
                }
        except Exception as e:
            logger.error(f"获取实时统计异常: {e}")
            return {"error": str(e)}
    
    def record_callback_event(self, event_type: str, details: Dict[str, Any] = None):
        """记录回调事件（用于实时监控）"""
        self.real_time_metrics[event_type] += 1
        if details:
            logger.debug(f"回调事件: {event_type}, 详情: {details}")
    
    async def check_performance_alerts(self) -> List[Dict[str, Any]]:
        """检查性能告警"""
        alerts = []
        current_metrics = self.get_current_metrics()
        
        if not current_metrics:
            return alerts
        
        # 成功率告警
        if current_metrics.success_rate < 90 and current_metrics.total_callbacks > 10:
            alerts.append({
                "type": "low_success_rate",
                "severity": "warning",
                "message": f"回调成功率过低: {current_metrics.success_rate:.2f}%",
                "value": current_metrics.success_rate,
                "threshold": 90,
            })
        
        # 重试次数告警
        if current_metrics.average_retry_count > 2:
            alerts.append({
                "type": "high_retry_rate",
                "severity": "warning",
                "message": f"平均重试次数过高: {current_metrics.average_retry_count:.2f}",
                "value": current_metrics.average_retry_count,
                "threshold": 2,
            })
        
        # 处理时间告警
        if current_metrics.average_processing_time > 300:  # 5分钟
            alerts.append({
                "type": "slow_processing",
                "severity": "warning",
                "message": f"平均处理时间过长: {current_metrics.average_processing_time:.2f}秒",
                "value": current_metrics.average_processing_time,
                "threshold": 300,
            })
        
        return alerts


# 创建全局监控实例
callback_monitor = CallbackMonitorService()
