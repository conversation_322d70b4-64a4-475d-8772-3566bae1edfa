<template>
    <div class="security-container">
        <div class="page-header">
            <h2>安全设置</h2>
            <p>管理您的账户安全设置，包括密码和双因子认证</p>
        </div>

        <div class="security-sections">
            <!-- 密码安全 -->
            <el-card class="security-card">
                <template #header>
                    <div class="card-header">
                        <el-icon><Lock /></el-icon>
                        <span>密码安全</span>
                    </div>
                </template>
                
                <div class="security-item">
                    <div class="item-info">
                        <h4>登录密码</h4>
                        <p>定期更换密码可以提高账户安全性</p>
                    </div>
                    <div class="item-action">
                        <el-button @click="showChangePassword = true">修改密码</el-button>
                    </div>
                </div>
            </el-card>

            <!-- 双因子认证 -->
            <el-card class="security-card">
                <template #header>
                    <div class="card-header">
                        <el-icon><Key /></el-icon>
                        <span>双因子认证</span>
                        <el-tag v-if="totpStatus.enabled" type="success" size="small">已启用</el-tag>
                        <el-tag v-else-if="totpStatus.is_required" type="warning" size="small">必须启用</el-tag>
                        <el-tag v-else type="info" size="small">未启用</el-tag>
                    </div>
                </template>
                
                <div class="security-item">
                    <div class="item-info">
                        <h4>Google Authenticator</h4>
                        <p v-if="!totpStatus.enabled">
                            启用双因子认证可以大大提高账户安全性，即使密码泄露也能保护您的账户
                        </p>
                        <p v-else>
                            双因子认证已启用，登录时需要提供验证码
                        </p>
                        
                        <!-- 状态信息 -->
                        <div v-if="totpStatus.enabled" class="totp-info">
                            <div class="info-item">
                                <span class="label">设置时间：</span>
                                <span>{{ formatDate(totpStatus.setup_at) }}</span>
                            </div>
                            <div class="info-item">
                                <span class="label">最后使用：</span>
                                <span>{{ formatDate(totpStatus.last_used_at) || '从未使用' }}</span>
                            </div>
                            <div class="info-item">
                                <span class="label">剩余备用码：</span>
                                <span>{{ totpStatus.backup_codes_remaining }} 个</span>
                            </div>
                        </div>

                        <!-- 宽限期提醒 -->
                        <div v-if="totpStatus.is_required && !totpStatus.enabled && totpStatus.grace_period_expires" 
                             class="grace-period-warning">
                            <el-alert
                                title="需要启用双因子认证"
                                :description="`您的角色要求启用双因子认证，宽限期将于 ${formatDate(totpStatus.grace_period_expires)} 到期`"
                                type="warning"
                                show-icon
                                :closable="false"
                            />
                        </div>
                    </div>
                    
                    <div class="item-action">
                        <el-button 
                            v-if="!totpStatus.enabled" 
                            type="primary"
                            @click="$router.push('/security/totp-setup')"
                        >
                            启用双因子认证
                        </el-button>
                        <div v-else class="totp-actions">
                            <el-button @click="showDisableTotp = true" type="danger">
                                禁用双因子认证
                            </el-button>
                            <el-button @click="showTotpTest = true">
                                测试验证码
                            </el-button>
                        </div>
                    </div>
                </div>
            </el-card>

            <!-- 登录历史 -->
            <el-card class="security-card">
                <template #header>
                    <div class="card-header">
                        <el-icon><Clock /></el-icon>
                        <span>登录历史</span>
                    </div>
                </template>
                
                <div class="security-item">
                    <div class="item-info">
                        <h4>最近登录记录</h4>
                        <p>查看您的账户登录历史，及时发现异常登录</p>
                    </div>
                    <div class="item-action">
                        <el-button @click="showLoginHistory = true">查看历史</el-button>
                    </div>
                </div>
            </el-card>
        </div>

        <!-- 修改密码对话框 -->
        <el-dialog v-model="showChangePassword" title="修改密码" width="400px">
            <el-form ref="passwordFormRef" :model="passwordForm" :rules="passwordRules" label-width="100px">
                <el-form-item label="当前密码" prop="currentPassword">
                    <el-input v-model="passwordForm.currentPassword" type="password" show-password />
                </el-form-item>
                <el-form-item label="新密码" prop="newPassword">
                    <el-input v-model="passwordForm.newPassword" type="password" show-password />
                </el-form-item>
                <el-form-item label="确认密码" prop="confirmPassword">
                    <el-input v-model="passwordForm.confirmPassword" type="password" show-password />
                </el-form-item>
            </el-form>
            
            <template #footer>
                <el-button @click="showChangePassword = false">取消</el-button>
                <el-button type="primary" :loading="passwordLoading" @click="changePassword">
                    确认修改
                </el-button>
            </template>
        </el-dialog>

        <!-- 禁用TOTP对话框 -->
        <el-dialog v-model="showDisableTotp" title="禁用双因子认证" width="400px">
            <el-alert
                title="警告"
                description="禁用双因子认证会降低您的账户安全性，请谨慎操作"
                type="warning"
                show-icon
                :closable="false"
                style="margin-bottom: 20px"
            />
            
            <el-form ref="disableTotpFormRef" :model="disableTotpForm" :rules="disableTotpRules" label-width="100px">
                <el-form-item label="当前密码" prop="password">
                    <el-input v-model="disableTotpForm.password" type="password" show-password />
                </el-form-item>
                <el-form-item label="验证码" prop="code">
                    <el-input 
                        v-model="disableTotpForm.code" 
                        placeholder="输入6位验证码或备用码"
                        maxlength="8"
                    />
                </el-form-item>
            </el-form>
            
            <template #footer>
                <el-button @click="showDisableTotp = false">取消</el-button>
                <el-button type="danger" :loading="disableTotpLoading" @click="disableTotp">
                    确认禁用
                </el-button>
            </template>
        </el-dialog>

        <!-- 测试TOTP对话框 -->
        <el-dialog v-model="showTotpTest" title="测试验证码" width="300px">
            <el-form ref="testTotpFormRef" :model="testTotpForm" :rules="testTotpRules">
                <el-form-item prop="code">
                    <el-input 
                        v-model="testTotpForm.code" 
                        placeholder="请输入6位验证码"
                        maxlength="6"
                        @keyup.enter="testTotp"
                    />
                </el-form-item>
            </el-form>
            
            <template #footer>
                <el-button @click="showTotpTest = false">取消</el-button>
                <el-button type="primary" :loading="testTotpLoading" @click="testTotp">
                    验证
                </el-button>
            </template>
        </el-dialog>
    </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Lock, Key, Clock } from '@element-plus/icons-vue'
import { totpApi } from '@/api/modules/totp'
import { userApi } from '@/api/modules/user'

// 响应式数据
const totpStatus = ref({
    enabled: false,
    is_required: false,
    setup_at: null,
    last_used_at: null,
    backup_codes_remaining: 0,
    grace_period_expires: null
})

// 对话框显示状态
const showChangePassword = ref(false)
const showDisableTotp = ref(false)
const showTotpTest = ref(false)
const showLoginHistory = ref(false)

// 加载状态
const passwordLoading = ref(false)
const disableTotpLoading = ref(false)
const testTotpLoading = ref(false)

// 修改密码表单
const passwordFormRef = ref(null)
const passwordForm = reactive({
    currentPassword: '',
    newPassword: '',
    confirmPassword: ''
})

const passwordRules = {
    currentPassword: [
        { required: true, message: '请输入当前密码', trigger: 'blur' }
    ],
    newPassword: [
        { required: true, message: '请输入新密码', trigger: 'blur' },
        { min: 6, message: '密码长度不能少于6位', trigger: 'blur' }
    ],
    confirmPassword: [
        { required: true, message: '请确认新密码', trigger: 'blur' },
        {
            validator: (rule, value, callback) => {
                if (value !== passwordForm.newPassword) {
                    callback(new Error('两次输入的密码不一致'))
                } else {
                    callback()
                }
            },
            trigger: 'blur'
        }
    ]
}

// 禁用TOTP表单
const disableTotpFormRef = ref(null)
const disableTotpForm = reactive({
    password: '',
    code: ''
})

const disableTotpRules = {
    password: [
        { required: true, message: '请输入当前密码', trigger: 'blur' }
    ],
    code: [
        { required: true, message: '请输入验证码', trigger: 'blur' }
    ]
}

// 测试TOTP表单
const testTotpFormRef = ref(null)
const testTotpForm = reactive({
    code: ''
})

const testTotpRules = {
    code: [
        { required: true, message: '请输入验证码', trigger: 'blur' },
        { len: 6, message: '验证码必须是6位数字', trigger: 'blur' }
    ]
}

// 获取TOTP状态
const fetchTotpStatus = async () => {
    try {
        const status = await totpApi.getStatus()
        totpStatus.value = status
    } catch (error) {
        console.error('获取TOTP状态失败:', error)
    }
}

// 修改密码
const changePassword = async () => {
    try {
        await passwordFormRef.value.validate()
        passwordLoading.value = true
        
        await userApi.changePassword(passwordForm)
        
        ElMessage.success('密码修改成功')
        showChangePassword.value = false
        
        // 重置表单
        Object.assign(passwordForm, {
            currentPassword: '',
            newPassword: '',
            confirmPassword: ''
        })
    } catch (error) {
        ElMessage.error('修改密码失败：' + error.message)
    } finally {
        passwordLoading.value = false
    }
}

// 禁用TOTP
const disableTotp = async () => {
    try {
        await disableTotpFormRef.value.validate()
        disableTotpLoading.value = true
        
        await totpApi.disable(disableTotpForm)
        
        ElMessage.success('双因子认证已禁用')
        showDisableTotp.value = false
        
        // 重新获取状态
        await fetchTotpStatus()
        
        // 重置表单
        Object.assign(disableTotpForm, {
            password: '',
            code: ''
        })
    } catch (error) {
        ElMessage.error('禁用失败：' + error.message)
    } finally {
        disableTotpLoading.value = false
    }
}

// 测试TOTP
const testTotp = async () => {
    try {
        await testTotpFormRef.value.validate()
        testTotpLoading.value = true
        
        const result = await totpApi.verify(testTotpForm)
        
        if (result.success) {
            ElMessage.success('验证码正确')
        } else {
            ElMessage.error('验证码错误')
        }
        
        showTotpTest.value = false
        testTotpForm.code = ''
    } catch (error) {
        ElMessage.error('验证失败：' + error.message)
    } finally {
        testTotpLoading.value = false
    }
}

// 格式化日期
const formatDate = (dateString) => {
    if (!dateString) return null
    return new Date(dateString).toLocaleString('zh-CN')
}

// 页面加载时获取TOTP状态
onMounted(() => {
    fetchTotpStatus()
})
</script>

<style lang="scss" scoped>
.security-container {
    max-width: 800px;
    margin: 0 auto;
    padding: 20px;
}

.page-header {
    margin-bottom: 30px;
    
    h2 {
        margin: 0 0 8px;
        color: #303133;
    }
    
    p {
        margin: 0;
        color: #909399;
    }
}

.security-sections {
    .security-card {
        margin-bottom: 20px;
        
        .card-header {
            display: flex;
            align-items: center;
            gap: 8px;
            
            .el-icon {
                color: #409eff;
            }
            
            span {
                font-weight: 600;
            }
        }
    }
}

.security-item {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    
    .item-info {
        flex: 1;
        
        h4 {
            margin: 0 0 8px;
            color: #303133;
        }
        
        p {
            margin: 0 0 12px;
            color: #606266;
            line-height: 1.5;
        }
    }
    
    .item-action {
        margin-left: 20px;
        
        .totp-actions {
            display: flex;
            gap: 8px;
        }
    }
}

.totp-info {
    margin-top: 12px;
    padding: 12px;
    background: #f5f7fa;
    border-radius: 4px;
    
    .info-item {
        display: flex;
        margin-bottom: 4px;
        
        &:last-child {
            margin-bottom: 0;
        }
        
        .label {
            min-width: 80px;
            color: #909399;
            font-size: 14px;
        }
        
        span:last-child {
            color: #606266;
            font-size: 14px;
        }
    }
}

.grace-period-warning {
    margin-top: 12px;
}
</style>
