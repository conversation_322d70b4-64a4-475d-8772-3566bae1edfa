# Telegram机器人统计数据权限修改

## 🎯 **需求变更**

根据您的要求，修改机器人统计数据的权限逻辑：

### ✅ **新的权限规则**

1. **群组绑定成功后，群里所有人都可以查询统计数据**（无需身份验证）
2. **数据范围根据群组绑定的层级决定**：
   - **绑定到部门**: 统计该部门及所有子部门的数据（递归包含所有层级）
   - **绑定到商户**: 统计该商户的所有数据

## 🔧 **技术实现**

### 1. 权限验证修改

#### 修改前 ❌
```python
async def verify_permissions(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
    """验证统计查询权限"""
    chat_id = update.effective_chat.id
    user_id = update.effective_user.id
    
    # 验证群组已绑定
    group = await self.verify_group_bound(chat_id)

    # 验证用户已验证  ← 这里需要移除
    telegram_user = await self.verify_user_verified(user_id)
    await self.verify_user_group_access(telegram_user, group)

    return group
```

#### 修改后 ✅
```python
async def verify_permissions(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
    """验证统计查询权限 - 群组绑定成功后所有群成员都可以查询"""
    chat_id = update.effective_chat.id
    user_id = update.effective_user.id
    
    # 验证群组已绑定
    group = await self.verify_group_bound(chat_id)
    
    # 新的权限逻辑：群组绑定成功后，所有群成员都可以查询统计数据
    # 无需验证用户身份，只需要群组绑定成功即可
    self.logger.info(f"群组 {chat_id} 已绑定，用户 {user_id} 可以查询统计数据")
    
    return group
```

### 2. 部门层级查询支持

#### 新增递归查询函数
```python
async def _get_department_and_children_ids(db: Session, department_id: int) -> List[int]:
    """获取部门及其所有子部门的ID列表（递归）"""
    try:
        from sqlalchemy import text
        
        # 使用递归CTE查询获取部门及其所有子部门
        sql = text("""
            WITH RECURSIVE department_tree AS (
                -- 基础查询：指定部门
                SELECT id, parent_id, merchant_id
                FROM departments 
                WHERE id = :department_id
                
                UNION ALL
                
                -- 递归查询：子部门的子部门
                SELECT d.id, d.parent_id, d.merchant_id
                FROM departments d
                INNER JOIN department_tree dt ON d.parent_id = dt.id
            )
            SELECT id FROM department_tree
        """)
        
        result = db.execute(sql, {'department_id': department_id})
        department_ids = [row.id for row in result.fetchall()]
        
        return department_ids
        
    except Exception as e:
        logger.error(f"获取部门层级ID失败: {e}")
        # 如果递归查询失败，至少返回原部门ID
        return [department_id]
```

#### 修改统计查询逻辑
```python
# 修改前 ❌
if department_id:
    query = query.filter(CardRecord.department_id == department_id)

# 修改后 ✅
if department_id:
    # 获取部门及其所有子部门的ID列表
    department_ids = await _get_department_and_children_ids(db, department_id)
    if department_ids:
        query = query.filter(CardRecord.department_id.in_(department_ids))
    else:
        # 如果没有找到部门，只查询指定部门
        query = query.filter(CardRecord.department_id == department_id)
```

## 📊 **数据范围示例**

### 场景1：群组绑定到商户
```
创建绑定令牌时：
- 选择商户：ABC公司
- 选择部门：（不选择）

群组绑定后的数据范围：
✅ ABC公司的所有数据
✅ 包含ABC公司下所有部门的数据
✅ 不限制部门层级
```

### 场景2：群组绑定到部门
```
创建绑定令牌时：
- 选择商户：ABC公司
- 选择部门：技术部

群组绑定后的数据范围：
✅ 技术部的数据
✅ 技术部下所有子部门的数据（如：前端组、后端组、测试组）
✅ 子部门的子部门数据（如：前端组下的React小组、Vue小组）
✅ 递归包含所有层级的子部门
```

### 场景3：多层级部门结构
```
部门结构：
ABC公司
├── 技术部 (ID: 100)
│   ├── 前端组 (ID: 101)
│   │   ├── React小组 (ID: 102)
│   │   └── Vue小组 (ID: 103)
│   ├── 后端组 (ID: 104)
│   │   ├── Java小组 (ID: 105)
│   │   └── Python小组 (ID: 106)
│   └── 测试组 (ID: 107)
└── 运营部 (ID: 200)

如果群组绑定到"技术部"：
查询数据时会包含部门ID: [100, 101, 102, 103, 104, 105, 106, 107]
即技术部及其所有子部门的数据
```

## 🎯 **用户体验**

### 修改前 ❌
```
用户: 今日数据
机器人: ❌ 身份验证需要 - 您需要先完成身份验证才能使用此功能

用户: /verify
机器人: 🚀 验证申请已提交！请等待管理员审核

管理员审核通过后：
用户: 今日数据
机器人: ✅ 显示统计数据
```

### 修改后 ✅
```
群组绑定成功后：
用户: 今日数据
机器人: ✅ 直接显示统计数据（无需身份验证）

任何群成员: CK今日
机器人: ✅ 直接显示CK统计数据

任何群成员: 昨日数据
机器人: ✅ 直接显示昨日统计数据
```

## 🔒 **安全保障**

### 1. 群组级别控制
- ✅ **只有已绑定的群组才能查询数据**
- ✅ **未绑定群组无法访问任何统计功能**
- ✅ **群组绑定状态实时验证**

### 2. 数据范围控制
- ✅ **严格按照群组绑定的商户/部门限制数据范围**
- ✅ **无法跨商户查询数据**
- ✅ **部门数据严格按照层级结构限制**

### 3. 功能范围控制
- ✅ **只能查询统计数据，无法进行其他操作**
- ✅ **无法访问敏感的管理功能**
- ✅ **无法修改系统配置**

## 📋 **修改的文件**

### 1. 统计处理器权限验证
- `app/telegram_bot/command_handlers/stats_handler.py`
- `app/telegram_bot/command_handlers/ck_stats_handler.py`

### 2. 统计数据查询逻辑
- `app/api/v1/endpoints/telegram_statistics.py`

### 3. 部门层级查询支持
- 新增递归CTE查询支持
- 支持无限层级的子部门查询

## 🚀 **效果总结**

### ✅ **用户体验提升**
- **无需身份验证** - 群组绑定后即可使用
- **即时响应** - 发送关键词立即获得统计数据
- **降低使用门槛** - 所有群成员都可以使用

### ✅ **数据安全保障**
- **群组级别隔离** - 只能查询本群组绑定的数据
- **商户级别隔离** - 无法跨商户访问数据
- **部门层级控制** - 严格按照部门结构限制数据范围

### ✅ **功能完整性**
- **支持所有统计类型** - 今日、昨日、本周、本月等
- **支持CK统计** - CK今日、CK昨日等
- **支持部门层级** - 递归查询所有子部门数据

现在机器人的统计功能更加便民，同时保持了严格的数据安全控制！🎉
