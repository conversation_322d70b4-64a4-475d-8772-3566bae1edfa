"""
测试商户管理员权限配置
验证商户管理员能够访问角色列表和用户管理功能，同时确保数据隔离
"""
import pytest
from fastapi.testclient import TestClient
from sqlalchemy.orm import Session

from app.main import app
from app.models.user import User
from app.models.role import Role
from app.models.merchant import Merchant
from test.conftest import get_test_db, create_test_user, create_test_merchant


class TestMerchantAdminPermissions:
    """商户管理员权限测试类"""

    def setup_method(self):
        """每个测试方法前的设置"""
        self.client = TestClient(app)
        self.db = next(get_test_db())
        
        # 创建测试商户A
        self.merchant_a = create_test_merchant(self.db, "test_merchant_a")
        
        # 创建测试商户B
        self.merchant_b = create_test_merchant(self.db, "test_merchant_b")
        
        # 创建商户A的管理员
        self.merchant_admin_a = create_test_user(
            self.db,
            username="merchant_admin_a",
            role_codes=["merchant_admin"],
            merchant_id=self.merchant_a.id,
            is_superuser=False
        )
        
        # 创建商户B的管理员
        self.merchant_admin_b = create_test_user(
            self.db,
            username="merchant_admin_b",
            role_codes=["merchant_admin"],
            merchant_id=self.merchant_b.id,
            is_superuser=False
        )
        
        # 创建商户A的普通用户
        self.user_a = create_test_user(
            self.db,
            username="user_a",
            role_codes=["ck_supplier"],
            merchant_id=self.merchant_a.id,
            is_superuser=False
        )
        
        # 创建商户B的普通用户
        self.user_b = create_test_user(
            self.db,
            username="user_b",
            role_codes=["ck_supplier"],
            merchant_id=self.merchant_b.id,
            is_superuser=False
        )

    def teardown_method(self):
        """每个测试方法后的清理"""
        self.db.close()

    def _login_user(self, username: str, password: str = "testpass123") -> str:
        """登录用户并返回token"""
        login_data = {"username": username, "password": password}
        login_response = self.client.post("/api/v1/auth/login", data=login_data)
        assert login_response.status_code == 200
        return login_response.json()["access_token"]

    def test_merchant_admin_can_access_roles_api(self):
        """测试商户管理员可以访问角色列表API"""
        # 商户A管理员登录
        token = self._login_user("merchant_admin_a")
        headers = {"Authorization": f"Bearer {token}"}

        # 访问角色列表
        response = self.client.get("/api/v1/roles", headers=headers)
        assert response.status_code == 200
        
        data = response.json()
        items = data.get('items', [])
        
        # 验证可以获取角色列表
        assert len(items) > 0, "商户管理员应该能够获取角色列表"
        
        # 验证超级管理员角色被过滤掉
        super_admin_roles = [role for role in items if role.get('code') == 'super_admin']
        assert len(super_admin_roles) == 0, "超级管理员角色应该被过滤掉"
        
        # 验证包含其他角色
        role_codes = [role.get('code') for role in items]
        assert 'merchant_admin' in role_codes, "应该包含商户管理员角色"
        assert 'ck_supplier' in role_codes, "应该包含CK供应商角色"

    def test_merchant_admin_can_access_users_api(self):
        """测试商户管理员可以访问用户列表API"""
        # 商户A管理员登录
        token = self._login_user("merchant_admin_a")
        headers = {"Authorization": f"Bearer {token}"}

        # 访问用户列表
        response = self.client.get("/api/v1/users", headers=headers)
        assert response.status_code == 200
        
        data = response.json()
        items = data.get('items', [])
        
        # 验证可以获取用户列表
        assert len(items) > 0, "商户管理员应该能够获取用户列表"

    def test_merchant_admin_data_isolation_users(self):
        """测试商户管理员用户数据隔离"""
        # 商户A管理员登录
        token = self._login_user("merchant_admin_a")
        headers = {"Authorization": f"Bearer {token}"}

        # 访问用户列表
        response = self.client.get("/api/v1/users", headers=headers)
        assert response.status_code == 200
        
        data = response.json()
        items = data.get('items', [])
        
        # 验证只能看到自己商户的用户
        merchant_ids = set(user.get('merchant_id') for user in items if user.get('merchant_id'))
        assert len(merchant_ids) <= 1, "商户管理员应该只能看到自己商户的用户"
        
        if merchant_ids:
            assert self.merchant_a.id in merchant_ids, "应该能看到自己商户的用户"
            assert self.merchant_b.id not in merchant_ids, "不应该看到其他商户的用户"

    def test_merchant_admin_can_create_user_in_own_merchant(self):
        """测试商户管理员可以在自己商户内创建用户"""
        # 商户A管理员登录
        token = self._login_user("merchant_admin_a")
        headers = {"Authorization": f"Bearer {token}"}

        # 创建用户数据
        user_data = {
            "username": "new_user_merchant_a",
            "password": "testpass123",
            "full_name": "新用户A",
            "email": "<EMAIL>",
            "merchant_id": self.merchant_a.id,
            "is_active": True
        }

        # 创建用户
        response = self.client.post("/api/v1/users", json=user_data, headers=headers)
        assert response.status_code == 200
        
        created_user = response.json()
        assert created_user.get('username') == "new_user_merchant_a"
        assert created_user.get('merchant_id') == self.merchant_a.id

    def test_merchant_admin_cannot_create_user_in_other_merchant(self):
        """测试商户管理员不能在其他商户内创建用户"""
        # 商户A管理员登录
        token = self._login_user("merchant_admin_a")
        headers = {"Authorization": f"Bearer {token}"}

        # 尝试在商户B创建用户
        user_data = {
            "username": "new_user_merchant_b",
            "password": "testpass123",
            "full_name": "新用户B",
            "email": "<EMAIL>",
            "merchant_id": self.merchant_b.id,  # 不同的商户ID
            "is_active": True
        }

        # 创建用户应该失败
        response = self.client.post("/api/v1/users", json=user_data, headers=headers)
        assert response.status_code == 403 or response.status_code == 400
        
        # 验证错误信息
        if response.status_code == 400:
            error_detail = response.json().get('detail', '')
            assert '商户' in error_detail or 'merchant' in error_detail.lower()

    def test_merchant_admin_cannot_access_other_merchant_user_details(self):
        """测试商户管理员不能访问其他商户用户的详情"""
        # 商户A管理员登录
        token = self._login_user("merchant_admin_a")
        headers = {"Authorization": f"Bearer {token}"}

        # 尝试访问商户B用户的详情
        response = self.client.get(f"/api/v1/users/{self.user_b.id}", headers=headers)
        assert response.status_code == 403 or response.status_code == 404

    def test_merchant_admin_can_access_own_merchant_user_details(self):
        """测试商户管理员可以访问自己商户用户的详情"""
        # 商户A管理员登录
        token = self._login_user("merchant_admin_a")
        headers = {"Authorization": f"Bearer {token}"}

        # 访问自己商户用户的详情
        response = self.client.get(f"/api/v1/users/{self.user_a.id}", headers=headers)
        assert response.status_code == 200
        
        user_data = response.json()
        assert user_data.get('id') == self.user_a.id
        assert user_data.get('merchant_id') == self.merchant_a.id

    def test_merchant_admin_role_assignment_functionality(self):
        """测试商户管理员的角色分配功能"""
        # 商户A管理员登录
        token = self._login_user("merchant_admin_a")
        headers = {"Authorization": f"Bearer {token}"}

        # 首先获取角色列表，确保可以获取到角色信息
        roles_response = self.client.get("/api/v1/roles", headers=headers)
        assert roles_response.status_code == 200
        
        roles_data = roles_response.json()
        available_roles = roles_data.get('items', [])
        
        # 验证有可用的角色用于分配
        assert len(available_roles) > 0, "应该有可用的角色用于分配"
        
        # 验证角色列表不包含超级管理员
        role_codes = [role.get('code') for role in available_roles]
        assert 'super_admin' not in role_codes, "角色列表不应包含超级管理员"
        assert 'merchant_admin' in role_codes or 'ck_supplier' in role_codes, "应该包含可分配的角色"

    def test_different_merchant_admins_isolation(self):
        """测试不同商户管理员之间的数据隔离"""
        # 商户A管理员登录
        token_a = self._login_user("merchant_admin_a")
        headers_a = {"Authorization": f"Bearer {token_a}"}

        # 商户B管理员登录
        token_b = self._login_user("merchant_admin_b")
        headers_b = {"Authorization": f"Bearer {token_b}"}

        # 获取商户A管理员看到的用户列表
        response_a = self.client.get("/api/v1/users", headers=headers_a)
        assert response_a.status_code == 200
        users_a = response_a.json().get('items', [])

        # 获取商户B管理员看到的用户列表
        response_b = self.client.get("/api/v1/users", headers=headers_b)
        assert response_b.status_code == 200
        users_b = response_b.json().get('items', [])

        # 验证两个管理员看到的用户列表不同
        user_ids_a = set(user.get('id') for user in users_a)
        user_ids_b = set(user.get('id') for user in users_b)
        
        # 应该没有交集（除非有共享用户，但在我们的测试中不应该有）
        common_users = user_ids_a.intersection(user_ids_b)
        assert len(common_users) == 0, "不同商户的管理员不应该看到相同的用户"
