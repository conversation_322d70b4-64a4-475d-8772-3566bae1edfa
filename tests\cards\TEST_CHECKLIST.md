# 绑卡测试用例检查清单

## 📋 测试前检查清单

在运行绑卡测试之前，请确保以下条件已满足：

### 1. 系统环境检查
- [ ] 后端服务正在运行 (`http://localhost:20000`)
- [ ] 数据库服务正常连接
- [ ] 所有依赖包已安装
- [ ] Python环境正常 (Python 3.8+)

### 2. 测试账号检查
- [ ] 超级管理员账号可用 (`admin` / `7c222fb2927d828af22f592134e8932480637c0d`)
- [ ] 商户管理员账号可用 (`test1` / `12345678`)
- [ ] 账号权限配置正确

### 3. 测试数据检查
- [ ] 至少有一个可用的商户
- [ ] 至少有一个可用的部门
- [ ] 商户配置了API密钥和密钥
- [ ] 数据库表结构正确

### 4. 网络连接检查
- [ ] API服务可访问
- [ ] 网络延迟正常
- [ ] 防火墙设置正确

## 🧪 测试执行清单

### 快速测试 (5分钟)
```bash
python test/cards/quick_bind_test.py
```

**预期结果**:
- [ ] 所有5个基础测试通过
- [ ] 成功率100%
- [ ] 无异常错误

### 完整测试套件 (15-30分钟)
```bash
python test/cards/run_all_bind_tests.py
```

**预期结果**:
- [ ] 所有30个测试用例通过
- [ ] 各模块测试成功率≥90%
- [ ] 生成测试报告文件

### 单独模块测试

#### 1. 绑卡API测试
```bash
python test/cards/test_bind_card_api.py
```
- [ ] 9个测试用例全部通过
- [ ] 签名验证测试正常
- [ ] 参数验证测试正常

#### 2. 绑卡管理API测试
```bash
python test/cards/test_card_management_api.py
```
- [ ] 6个测试用例全部通过
- [ ] 权限控制测试正常
- [ ] 数据隔离测试正常

#### 3. 批量绑卡API测试
```bash
python test/cards/test_batch_bind_api.py
```
- [ ] 6个测试用例全部通过
- [ ] 批量操作测试正常
- [ ] 分页查询测试正常

#### 4. 绑卡CRUD测试
```bash
python test/cards/test_cards_crud.py
```
- [ ] 4个测试用例全部通过
- [ ] CRUD操作测试正常
- [ ] 统计功能测试正常

#### 5. 统计API测试 (pytest)
```bash
pytest test/cards/test_statistics_api.py
```
- [ ] 8个测试用例全部通过
- [ ] 权限隔离测试正常
- [ ] 数据过滤测试正常

## 🔍 测试结果验证清单

### 成功标准
- [ ] 总体成功率 ≥ 95%
- [ ] 无严重错误或异常
- [ ] 权限控制测试全部通过
- [ ] 数据隔离测试全部通过
- [ ] API签名验证测试全部通过

### 测试报告检查
- [ ] 生成了JSON格式的测试报告
- [ ] 报告包含详细的统计信息
- [ ] 失败测试有明确的错误信息
- [ ] 测试执行时间合理

### 功能验证
- [ ] 绑卡API能正常接收和处理请求
- [ ] 权限控制机制工作正常
- [ ] 数据隔离机制工作正常
- [ ] 批量操作功能正常
- [ ] 统计功能数据准确

## ❌ 常见问题排查清单

### 连接问题
如果出现连接失败：
- [ ] 检查后端服务是否启动
- [ ] 检查端口20000是否被占用
- [ ] 检查防火墙设置
- [ ] 检查网络连接

### 认证问题
如果出现认证失败：
- [ ] 检查测试账号密码是否正确
- [ ] 检查用户权限配置
- [ ] 检查JWT token生成
- [ ] 检查数据库用户数据

### 权限问题
如果出现权限错误：
- [ ] 检查用户角色配置
- [ ] 检查权限表数据
- [ ] 检查商户部门关联
- [ ] 检查数据隔离配置

### 数据问题
如果出现数据错误：
- [ ] 检查数据库连接
- [ ] 检查表结构是否正确
- [ ] 检查测试数据是否存在
- [ ] 检查数据约束条件

### API问题
如果出现API错误：
- [ ] 检查API路由配置
- [ ] 检查请求参数格式
- [ ] 检查响应数据格式
- [ ] 检查错误处理逻辑

## 📊 性能基准清单

### 响应时间基准
- [ ] 单个绑卡请求 < 2秒
- [ ] 批量查询(100条) < 5秒
- [ ] 统计查询 < 3秒
- [ ] 权限验证 < 1秒

### 并发性能基准
- [ ] 支持10个并发绑卡请求
- [ ] 支持50个并发查询请求
- [ ] 无内存泄漏
- [ ] 无数据库连接泄漏

## 🔧 维护检查清单

### 代码质量
- [ ] 所有文件通过语法检查
- [ ] 代码符合规范要求
- [ ] 注释完整清晰
- [ ] 无硬编码配置

### 文档完整性
- [ ] README.md 文档完整
- [ ] 测试用例有详细说明
- [ ] 错误处理有说明
- [ ] 使用示例清晰

### 可维护性
- [ ] 测试用例相互独立
- [ ] 易于添加新测试
- [ ] 易于修改现有测试
- [ ] 配置参数可调整

## ✅ 最终验收清单

### 功能完整性
- [ ] 所有绑卡核心功能已测试
- [ ] 所有API接口已覆盖
- [ ] 所有权限场景已验证
- [ ] 所有错误场景已测试

### 质量标准
- [ ] 测试覆盖率100%
- [ ] 代码质量符合规范
- [ ] 文档完整详细
- [ ] 性能满足要求

### 交付标准
- [ ] 所有测试文件已创建
- [ ] 所有文档已完成
- [ ] 测试可正常运行
- [ ] 结果符合预期

## 📞 支持联系

如果在测试过程中遇到问题：

1. **查看文档**: 先查看 `README.md` 和相关文档
2. **检查日志**: 查看测试输出和错误信息
3. **验证环境**: 按照本清单检查环境配置
4. **重新测试**: 修复问题后重新运行测试

---

**注意**: 请在每次重要更新后重新运行完整测试套件，确保系统稳定性。
