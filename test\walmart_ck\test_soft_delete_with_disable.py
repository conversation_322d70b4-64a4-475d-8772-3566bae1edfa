#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试CK软删除时同时禁用功能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

import pytest
from sqlalchemy.orm import Session
from app.database import get_db
from app.models.walmart_ck import WalmartCK
from app.models.user import User
from app.models.merchant import Merchant
from app.models.department import Department
from app.services.walmart_ck_service_new import WalmartCKService
from app.crud.walmart_ck import walmart_ck as walmart_ck_crud
import uuid

class TestSoftDeleteWithDisable:
    """测试CK软删除时同时禁用功能"""
    
    @pytest.fixture(autouse=True)
    def setup(self):
        """测试设置"""
        self.db = next(get_db())
        self.ck_service = WalmartCKService(self.db)
        
        # 获取测试用户
        self.admin_user = self.db.query(User).filter(User.username == "admin").first()
        assert self.admin_user is not None, "找不到admin用户"
        
        # 获取测试商户和部门
        self.test_merchant = self.db.query(Merchant).first()
        self.test_department = self.db.query(Department).first()
        
        assert self.test_merchant is not None, "找不到测试商户"
        assert self.test_department is not None, "找不到测试部门"
        
        self.test_ck_ids = []
        
        yield
        
        # 清理测试数据
        self.cleanup_test_data()
        self.db.close()
    
    def create_test_ck(self, suffix="", active=True):
        """创建测试CK"""
        ck_data = {
            "sign": f"test_disable_{uuid.uuid4().hex[:8]}{suffix}@token#signature#26",
            "daily_limit": 100,
            "hourly_limit": 50,
            "active": active,
            "description": f"软删除禁用测试CK{suffix}",
            "merchant_id": self.test_merchant.id,
            "department_id": self.test_department.id,
            "created_by": self.admin_user.id
        }
        
        ck = WalmartCK(**ck_data)
        self.db.add(ck)
        self.db.commit()
        self.db.refresh(ck)
        
        self.test_ck_ids.append(ck.id)
        return ck
    
    def test_soft_delete_disables_ck(self):
        """测试软删除时同时禁用CK"""
        print("\n=== 测试软删除时同时禁用CK ===")
        
        # 1. 创建一个启用的CK
        ck = self.create_test_ck("_active", active=True)
        
        # 验证初始状态
        assert ck.active is True, "CK应该是启用状态"
        assert ck.is_deleted is False, "CK应该未被删除"
        
        print(f"创建CK {ck.id}，初始状态: active={ck.active}, is_deleted={ck.is_deleted}")
        
        # 2. 执行软删除
        success = self.ck_service.delete_walmart_ck(ck.id, self.admin_user)
        assert success, "软删除应该成功"
        
        # 3. 刷新CK对象，检查状态变化
        self.db.refresh(ck)
        
        # 验证软删除后的状态
        assert ck.is_deleted is True, "CK应该被标记为已删除"
        assert ck.active is False, "CK应该被禁用"
        
        print(f"软删除后状态: active={ck.active}, is_deleted={ck.is_deleted}")
        print("✅ 软删除时正确地同时禁用了CK")
    
    def test_deleted_ck_not_available_for_binding(self):
        """测试已删除的CK不会被绑卡逻辑选中"""
        print("\n=== 测试已删除CK不会被绑卡逻辑选中 ===")
        
        # 1. 创建两个CK
        ck1 = self.create_test_ck("_1", active=True)
        ck2 = self.create_test_ck("_2", active=True)
        
        # 2. 验证删除前可以选择到CK
        available_ck_before = self.ck_service.get_available_ck(
            merchant_id=self.test_merchant.id,
            department_id=self.test_department.id
        )
        assert available_ck_before is not None, "删除前应该有可用的CK"
        print(f"删除前可用CK: {available_ck_before.id}")
        
        # 3. 软删除第一个CK
        success = self.ck_service.delete_walmart_ck(ck1.id, self.admin_user)
        assert success, "软删除应该成功"
        
        # 4. 验证删除后的CK选择逻辑
        available_ck_after = self.ck_service.get_available_ck(
            merchant_id=self.test_merchant.id,
            department_id=self.test_department.id
        )
        
        # 应该仍然有可用CK（ck2），但不应该是被删除的ck1
        if available_ck_after:
            assert available_ck_after.id != ck1.id, "已删除的CK不应该被选中"
            assert available_ck_after.id == ck2.id, "应该选择未删除的CK"
            print(f"删除后可用CK: {available_ck_after.id} (正确排除了已删除的CK {ck1.id})")
        
        # 5. 使用CRUD层方法验证
        crud_available_ck = walmart_ck_crud.get_available_ck_for_binding(
            db=self.db,
            merchant_id=self.test_merchant.id,
            department_id=self.test_department.id
        )
        
        if crud_available_ck:
            assert crud_available_ck.id != ck1.id, "CRUD层也不应该选择已删除的CK"
            print(f"CRUD层选择的CK: {crud_available_ck.id}")
        
        print("✅ 已删除的CK正确地被绑卡逻辑排除")
    
    def test_deleted_ck_not_in_active_list(self):
        """测试已删除的CK不在活跃CK列表中"""
        print("\n=== 测试已删除CK不在活跃CK列表中 ===")
        
        # 1. 创建CK
        ck = self.create_test_ck("_active_list", active=True)
        
        # 2. 获取删除前的活跃CK列表
        active_cks_before = walmart_ck_crud.get_active_cks_for_merchant(
            db=self.db,
            merchant_id=self.test_merchant.id
        )
        
        # 验证CK在列表中
        ck_ids_before = [ck_item.id for ck_item in active_cks_before]
        assert ck.id in ck_ids_before, "CK应该在活跃列表中"
        print(f"删除前活跃CK数量: {len(active_cks_before)}")
        
        # 3. 软删除CK
        success = self.ck_service.delete_walmart_ck(ck.id, self.admin_user)
        assert success, "软删除应该成功"
        
        # 4. 获取删除后的活跃CK列表
        active_cks_after = walmart_ck_crud.get_active_cks_for_merchant(
            db=self.db,
            merchant_id=self.test_merchant.id
        )
        
        # 验证CK不在列表中
        ck_ids_after = [ck_item.id for ck_item in active_cks_after]
        assert ck.id not in ck_ids_after, "已删除的CK不应该在活跃列表中"
        print(f"删除后活跃CK数量: {len(active_cks_after)}")
        print("✅ 已删除的CK正确地从活跃列表中移除")
    
    def test_is_ck_available_checks_both_conditions(self):
        """测试_is_ck_available方法同时检查active和is_deleted"""
        print("\n=== 测试_is_ck_available方法检查逻辑 ===")
        
        # 1. 创建CK
        ck = self.create_test_ck("_availability", active=True)
        
        # 2. 验证初始状态可用
        is_available_initial = self.ck_service._is_ck_available(ck)
        assert is_available_initial is True, "初始状态应该可用"
        print(f"初始状态可用性: {is_available_initial}")
        
        # 3. 只设置is_deleted=True，保持active=True
        ck.is_deleted = True
        self.db.commit()
        self.db.refresh(ck)
        
        is_available_deleted = self.ck_service._is_ck_available(ck)
        assert is_available_deleted is False, "已删除的CK应该不可用"
        print(f"仅删除后可用性: {is_available_deleted}")
        
        # 4. 恢复is_deleted=False，设置active=False
        ck.is_deleted = False
        ck.active = False
        self.db.commit()
        self.db.refresh(ck)
        
        is_available_inactive = self.ck_service._is_ck_available(ck)
        assert is_available_inactive is False, "非活跃的CK应该不可用"
        print(f"仅禁用后可用性: {is_available_inactive}")
        
        # 5. 同时设置is_deleted=True和active=False
        ck.is_deleted = True
        ck.active = False
        self.db.commit()
        self.db.refresh(ck)
        
        is_available_both = self.ck_service._is_ck_available(ck)
        assert is_available_both is False, "既删除又禁用的CK应该不可用"
        print(f"删除且禁用后可用性: {is_available_both}")
        
        print("✅ _is_ck_available方法正确检查了所有条件")
    
    def test_double_delete_prevention(self):
        """测试防止重复删除"""
        print("\n=== 测试防止重复删除 ===")
        
        # 1. 创建CK
        ck = self.create_test_ck("_double_delete", active=True)
        
        # 2. 第一次删除
        success1 = self.ck_service.delete_walmart_ck(ck.id, self.admin_user)
        assert success1 is True, "第一次删除应该成功"
        print("第一次删除成功")
        
        # 3. 第二次删除（应该被阻止）
        success2 = self.ck_service.delete_walmart_ck(ck.id, self.admin_user)
        assert success2 is False, "第二次删除应该被阻止"
        print("第二次删除被正确阻止")
        
        print("✅ 重复删除防护机制正常工作")
    
    def cleanup_test_data(self):
        """清理测试数据"""
        try:
            # 删除测试CK
            for ck_id in self.test_ck_ids:
                ck = self.db.query(WalmartCK).filter(WalmartCK.id == ck_id).first()
                if ck:
                    self.db.delete(ck)
            
            self.db.commit()
            print("✅ 测试数据清理完成")
        except Exception as e:
            self.db.rollback()
            print(f"⚠️ 测试数据清理失败: {e}")

if __name__ == "__main__":
    # 运行测试
    test = TestSoftDeleteWithDisable()
    test.setup()
    
    try:
        test.test_soft_delete_disables_ck()
        test.test_deleted_ck_not_available_for_binding()
        test.test_deleted_ck_not_in_active_list()
        test.test_is_ck_available_checks_both_conditions()
        test.test_double_delete_prevention()
        print("\n🎉 所有测试通过！")
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        raise
    finally:
        test.cleanup_test_data()
