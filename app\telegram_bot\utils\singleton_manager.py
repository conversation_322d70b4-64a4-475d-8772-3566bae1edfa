"""
Telegram机器人单例管理器
防止多实例冲突
"""

import os
import sys
import time
import atexit
from pathlib import Path
from typing import Optional

from app.core.logging import get_logger

logger = get_logger(__name__)


class SingletonManager:
    """单例管理器 - 使用简单的文件锁机制"""

    def __init__(self, name: str = "telegram_bot"):
        self.name = name
        self.lock_file_path = Path(f".{name}_singleton.lock")
        self.acquired = False
        self.pid = os.getpid()

    def acquire(self) -> bool:
        """获取单例锁"""
        try:
            # 检查是否已有锁文件
            if self.lock_file_path.exists():
                # 检查现有锁是否有效
                if self._is_lock_valid():
                    logger.warning(f"⚠️  单例锁已被其他实例持有: {self.name}")
                    return False
                else:
                    # 清理无效锁
                    logger.info("清理无效的锁文件...")
                    self._remove_lock_file()

            # 创建新锁文件
            with open(self.lock_file_path, 'w') as f:
                f.write(f"{self.pid}\n")
                f.write(f"{time.time()}\n")
                f.flush()

            self.acquired = True

            # 注册退出时清理
            atexit.register(self.release)

            logger.info(f"✅ 获取单例锁成功: {self.name} (PID: {self.pid})")
            return True

        except Exception as e:
            logger.error(f"获取单例锁失败: {e}")
            return False

    def _is_lock_valid(self) -> bool:
        """检查锁文件是否有效"""
        try:
            lock_info = self.get_lock_info()
            if not lock_info:
                return False

            # 检查进程是否还在运行
            return self.is_process_running(lock_info["pid"])

        except Exception:
            return False

    def release(self):
        """释放单例锁"""
        if self.acquired:
            try:
                self._remove_lock_file()
                self.acquired = False
                logger.info(f"✅ 释放单例锁: {self.name}")

            except Exception as e:
                logger.warning(f"释放单例锁时出错: {e}")

    def _remove_lock_file(self):
        """删除锁文件"""
        try:
            if self.lock_file_path.exists():
                self.lock_file_path.unlink()
        except Exception as e:
            logger.warning(f"删除锁文件失败: {e}")

    def get_lock_info(self) -> Optional[dict]:
        """获取锁信息"""
        if not self.lock_file_path.exists():
            return None

        try:
            with open(self.lock_file_path, 'r') as f:
                lines = f.readlines()
                if len(lines) >= 2:
                    pid = int(lines[0].strip())
                    timestamp = float(lines[1].strip())
                    return {
                        "pid": pid,
                        "timestamp": timestamp,
                        "age_seconds": time.time() - timestamp
                    }
        except Exception as e:
            logger.warning(f"读取锁信息失败: {e}")

        return None

    def is_process_running(self, pid: int) -> bool:
        """检查进程是否还在运行"""
        try:
            if sys.platform == "win32":
                import subprocess
                result = subprocess.run(
                    ['tasklist', '/FI', f'PID eq {pid}'],
                    capture_output=True, text=True, timeout=5
                )
                return str(pid) in result.stdout
            else:
                os.kill(pid, 0)
                return True
        except Exception:
            return False
    
    def force_cleanup(self) -> bool:
        """强制清理死锁"""
        lock_info = self.get_lock_info()
        if not lock_info:
            logger.info("没有发现锁文件")
            return True
        
        pid = lock_info["pid"]
        age = lock_info["age_seconds"]
        
        logger.info(f"发现锁文件: PID={pid}, 年龄={age:.1f}秒")
        
        # 检查进程是否还在运行
        if not self.is_process_running(pid):
            logger.info(f"进程 {pid} 已不存在，清理死锁")
            self._remove_lock_file()
            return True
        
        # 如果锁太老（超过1小时），可能是死锁
        if age > 3600:
            logger.warning(f"锁文件过老 ({age:.1f}秒)，可能是死锁")
            response = input("是否强制清理? (y/N): ")
            if response.lower() in ['y', 'yes']:
                self._remove_lock_file()
                return True
        
        logger.info(f"进程 {pid} 仍在运行，无法清理")
        return False
    
    def __enter__(self):
        """上下文管理器入口"""
        if not self.acquire():
            raise RuntimeError(f"无法获取单例锁: {self.name}")
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """上下文管理器出口"""
        self.release()
        return False  # 不抑制异常


# 全局单例管理器实例
_telegram_bot_singleton = SingletonManager("telegram_bot")


def acquire_bot_singleton() -> bool:
    """获取机器人单例锁"""
    return _telegram_bot_singleton.acquire()


def release_bot_singleton():
    """释放机器人单例锁"""
    _telegram_bot_singleton.release()


def get_bot_singleton_info() -> Optional[dict]:
    """获取机器人单例信息"""
    return _telegram_bot_singleton.get_lock_info()


def force_cleanup_bot_singleton() -> bool:
    """强制清理机器人单例锁"""
    return _telegram_bot_singleton.force_cleanup()


def with_bot_singleton():
    """装饰器：确保函数在单例保护下运行"""
    def decorator(func):
        def wrapper(*args, **kwargs):
            with _telegram_bot_singleton:
                return func(*args, **kwargs)
        return wrapper
    return decorator
