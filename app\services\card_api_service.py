"""
绑卡记录API服务类
负责处理绑卡记录相关的API业务逻辑
"""
from typing import List, Optional, Any, Dict
from sqlalchemy.orm import Session
from fastapi import HTTPException, status
from datetime import datetime

from app.models.user import User
from app.models.merchant import Merchant
from app.models.department import Department
from app.models.walmart_ck import WalmartCK
from app.schemas.card_record import (
    CardRecordCreate,
    CardRecordUpdate
)
from app.services.card_record_service import CardRecordService
from app.services.permission_service import PermissionService
from app.services.security_service import SecurityService
from app.core.logging import get_logger


class CardAPIService:
    """绑卡记录API业务逻辑服务类"""

    def __init__(self, db: Session):
        self.db = db
        self.card_service = CardRecordService(db)
        self.permission_service = PermissionService(db)
        self.security_service = SecurityService(db)
        self.logger = get_logger("card_api_service")

    def _check_permission(self, current_user: User, permission: str) -> None:
        """检查用户权限"""
        has_permission = self.permission_service.check_user_permission(
            current_user, permission
        )
        if not has_permission:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail=f"没有{permission}权限",
            )

    def _determine_target_merchant_id(
        self,
        current_user: User,
        merchant_id: Optional[int]
    ) -> Optional[int]:
        """确定目标商户ID"""
        if not current_user.is_superuser:
            # 非超级管理员只能查看自己商户的记录
            return current_user.merchant_id
        else:
            # 超级管理员可以指定商户ID查看特定商户数据，
            # 或者不指定商户ID查看所有商户数据
            return merchant_id

    def _apply_data_isolation_to_join_query(self, query, current_user: User):
        """
        对JOIN查询应用数据隔离规则

        修复：使用与统计查询相同的数据隔离逻辑，确保列表查询和统计查询的数据范围一致
        通过创建一个临时的单表查询来应用完整的数据隔离逻辑，然后将条件应用到JOIN查询

        Args:
            query: SQLAlchemy查询对象（包含JOIN）
            current_user: 当前用户

        Returns:
            query: 应用隔离规则后的查询对象
        """
        if not current_user:
            return query

        # 超级管理员可以访问所有数据
        if current_user.is_superuser:
            return query

        # 创建一个临时的单表查询来获取数据隔离条件
        # 这样可以重用完整的权限检查逻辑，确保与统计查询一致
        temp_query = self.db.query(self.card_service.model)
        temp_query = self.card_service.apply_data_isolation(temp_query, current_user)

        # 提取WHERE条件并应用到JOIN查询
        # 获取临时查询的WHERE子句
        where_clause = temp_query.whereclause
        if where_clause is not None:
            query = query.filter(where_clause)

        return query

    def get_card_records(
        self,
        current_user: User,
        page: int = 1,
        page_size: int = 20,
        merchant_id: Optional[int] = None,
        department_id: Optional[int] = None,
        status_filter: Optional[str] = None,
        card_number: Optional[str] = None,
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None,
    ) -> Dict[str, Any]:
        """
        获取绑卡记录列表

        Args:
            current_user: 当前用户
            page: 页码
            page_size: 每页数量
            merchant_id: 商户ID
            department_id: 部门ID
            status_filter: 状态过滤
            card_number: 卡号搜索
            start_date: 开始日期
            end_date: 结束日期

        Returns:
            Dict[str, Any]: 绑卡记录列表数据
        """
        # 权限检查
        self._check_permission(current_user, "api:cards:read")

        # 【安全修复】：强制商户隔离检查
        if not current_user.is_superuser:
            if not current_user.merchant_id:
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail="用户没有商户信息，无法访问绑卡数据",
                )

            # 如果指定了merchant_id参数，检查是否匹配用户的商户
            if merchant_id and merchant_id != current_user.merchant_id:
                self.logger.warning(f"[SECURITY] 用户 {current_user.id} 尝试访问其他商户 {merchant_id} 的绑卡数据")
                self.security_service.log_security_violation(
                    current_user,
                    "CROSS_MERCHANT_CARD_ACCESS_ATTEMPT",
                    f"尝试访问其他商户的绑卡数据",
                    {
                        "user_merchant_id": current_user.merchant_id,
                        "target_merchant_id": merchant_id,
                        "operation": "read",
                        "resource_type": "card_record"
                    }
                )
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail="无权访问其他商户的绑卡数据",
                )

        # 确定目标商户ID
        target_merchant_id = self._determine_target_merchant_id(current_user, merchant_id)

        # 构建查询条件，使用 JOIN 来获取商户、部门和CK信息
        query = self.db.query(
            self.card_service.model,
            Merchant.name.label('merchant_name'),
            Department.name.label('department_name'),
            WalmartCK.description.label('ck_description')
        ).outerjoin(
            Merchant, self.card_service.model.merchant_id == Merchant.id
        ).outerjoin(
            Department, self.card_service.model.department_id == Department.id
        ).outerjoin(
            WalmartCK, self.card_service.model.walmart_ck_id == WalmartCK.id
        )

        # 应用数据隔离（针对JOIN查询）
        query = self._apply_data_isolation_to_join_query(query, current_user)

        # 商户过滤
        if target_merchant_id:
            query = query.filter(self.card_service.model.merchant_id == target_merchant_id)

        # 部门过滤
        if department_id:
            query = query.filter(self.card_service.model.department_id == department_id)

        # 状态过滤
        if status_filter:
            query = query.filter(self.card_service.model.status == status_filter)

        # 卡号搜索
        if card_number:
            query = query.filter(self.card_service.model.card_number.like(f"%{card_number}%"))

        # 时间范围过滤
        if start_date:
            query = query.filter(self.card_service.model.created_at >= start_date)
        if end_date:
            query = query.filter(self.card_service.model.created_at <= end_date)

        # 分页
        total = query.count()
        results = query.order_by(
            self.card_service.model.created_at.desc()
        ).offset((page - 1) * page_size).limit(page_size).all()

        # 构建返回数据，包含商户、部门和CK备注信息
        items = []
        for record, merchant_name, department_name, ck_description in results:
            record_dict = record.to_dict()
            record_dict['merchant_name'] = merchant_name
            record_dict['department_name'] = department_name
            record_dict['ck_description'] = ck_description
            items.append(record_dict)

        return {
            "items": items,
            "total": total,
            "page": page,
            "page_size": page_size,
            "pages": (total + page_size - 1) // page_size,
        }

    def create_card_record(
        self,
        card_in: CardRecordCreate,
        current_user: User
    ) -> Dict[str, Any]:
        """
        创建绑卡记录

        Args:
            card_in: 绑卡记录创建数据
            current_user: 当前用户

        Returns:
            Dict[str, Any]: 创建的绑卡记录
        """
        # 权限检查
        self._check_permission(current_user, "api:cards:create")

        # 创建绑卡记录
        card_record = self.card_service.create_card_record(card_in, current_user)

        return card_record.to_dict()

    def batch_create_card_records(
        self,
        batch_data: List[CardRecordCreate],
        current_user: User
    ) -> Dict[str, Any]:
        """
        批量创建绑卡记录

        Args:
            batch_data: 批量创建数据
            current_user: 当前用户

        Returns:
            Dict[str, Any]: 批量创建结果
        """
        # 权限检查
        self._check_permission(current_user, "api:cards:create")

        # 批量创建绑卡记录
        result = self.card_service.batch_create_cards(
            batch_data,
            current_user
        )

        return result

    def get_card_record(self, card_id: str, current_user: User) -> Dict[str, Any]:
        """
        获取绑卡记录详情

        Args:
            card_id: 绑卡记录ID
            current_user: 当前用户

        Returns:
            Dict[str, Any]: 绑卡记录详情

        Raises:
            HTTPException: 如果记录不存在或无权限访问
        """
        # 权限检查
        self._check_permission(current_user, "api:cards:read")

        # 获取绑卡记录（应用数据隔离）
        card_record = self.card_service.get_with_isolation(card_id, current_user)
        if not card_record:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="绑卡记录不存在或无权限访问"
            )

        return card_record.to_dict()

    def update_card_record(
        self,
        card_id: str,
        card_in: CardRecordUpdate,
        current_user: User
    ) -> Dict[str, Any]:
        """
        更新绑卡记录信息

        Args:
            card_id: 绑卡记录ID
            card_in: 更新数据
            current_user: 当前用户

        Returns:
            Dict[str, Any]: 更新后的绑卡记录

        Raises:
            HTTPException: 如果记录不存在或无权限访问
        """
        # 权限检查
        self._check_permission(current_user, "api:cards:update")

        # 更新绑卡记录
        card_record = self.card_service.update(card_id, card_in, current_user)
        if not card_record:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="绑卡记录不存在或无权限访问"
            )

        return card_record.to_dict()

    async def bind_card(
        self,
        card_id: str,
        walmart_ck_id: int,
        current_user: User
    ) -> Dict[str, Any]:
        """
        执行绑卡操作

        Args:
            card_id: 绑卡记录ID
            walmart_ck_id: 沃尔玛CK ID
            current_user: 当前用户

        Returns:
            Dict[str, Any]: 绑卡结果
        """
        # 权限检查
        self._check_permission(current_user, "api:card-bind:create")

        # 【安全修复】：验证绑卡记录访问权限
        if not self.security_service.validate_card_access(current_user, card_id, "bind"):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="无权限绑定该卡记录",
            )

        # 执行绑卡
        result = await self.card_service.bind_card(card_id, walmart_ck_id, current_user)

        return result

    async def batch_bind_cards(
        self,
        card_ids: List[str],
        current_user: User
    ) -> Dict[str, Any]:
        """
        批量绑卡操作

        Args:
            card_ids: 绑卡记录ID列表
            current_user: 当前用户

        Returns:
            Dict[str, Any]: 批量绑卡结果
        """
        # 权限检查
        self._check_permission(current_user, "api:card-bind:create")

        # 【安全修复】：验证所有绑卡记录的访问权限
        for card_id in card_ids:
            if not self.security_service.validate_card_access(current_user, card_id, "batch_bind"):
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail=f"无权限绑定卡记录 {card_id}",
                )

        # 批量绑卡
        result = await self.card_service.batch_bind_cards(card_ids, current_user)

        return result

    def get_card_statistics(
        self,
        merchant_id: int,
        current_user: User,
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None,
    ) -> Dict[str, Any]:
        """
        获取绑卡统计信息

        Args:
            merchant_id: 商户ID
            current_user: 当前用户
            start_date: 开始日期
            end_date: 结束日期

        Returns:
            Dict[str, Any]: 统计信息
        """
        # 权限检查
        self._check_permission(current_user, "api:cards:read")

        # 获取统计信息
        statistics = self.card_service.get_card_statistics(
            merchant_id, current_user, start_date, end_date
        )

        return statistics

    def get_card_statistics_auto(
        self,
        current_user: User,
        page: int = 1,
        page_size: int = 20,
        card_number: Optional[str] = None,
        status_filter: Optional[str] = None,
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None,
        merchant_id: Optional[int] = None,
    ) -> Dict[str, Any]:
        """
        获取绑卡统计信息（自动权限判断）

        根据用户权限自动确定数据范围：
        - 超级管理员：可查看所有商户统计（可通过merchant_id参数指定特定商户）
        - 商户管理员：只能查看自己商户的统计
        - 商户CK供应商：只能查看自己部门的统计

        Args:
            current_user: 当前用户
            page: 页码
            page_size: 每页数量
            card_number: 卡号搜索
            status_filter: 状态过滤
            start_date: 开始日期
            end_date: 结束日期
            merchant_id: 商户ID（超级管理员可指定）

        Returns:
            Dict[str, Any]: 统计信息和分页数据
        """
        # 权限检查
        self._check_permission(current_user, "api:cards:read")

        # 获取统计信息和列表数据
        statistics_data = self.card_service.get_card_statistics_auto(
            current_user=current_user,
            page=page,
            page_size=page_size,
            card_number=card_number,
            status_filter=status_filter,
            start_date=start_date,
            end_date=end_date,
            merchant_id=merchant_id,
        )

        return statistics_data

    async def retry_card_record(
        self,
        card_id: str,
        current_user: User
    ) -> Dict[str, Any]:
        """
        重试失败的绑卡记录

        Args:
            card_id: 绑卡记录ID
            current_user: 当前用户

        Returns:
            Dict[str, Any]: 重试结果

        Raises:
            HTTPException: 如果记录不存在、无权限访问或不允许重试
        """
        # 权限检查
        self._check_permission(current_user, "api:cards:retry")

        # 执行重试
        result = await self.card_service.retry_single_card(card_id, current_user)

        return result
