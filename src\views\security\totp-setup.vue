<template>
    <div class="totp-setup-container">
        <el-card class="setup-card">
            <template #header>
                <div class="card-header">
                    <h3>设置双因子认证</h3>
                    <p class="subtitle">使用Google Authenticator或其他TOTP应用增强账户安全</p>
                </div>
            </template>

            <!-- 步骤指示器 -->
            <el-steps :active="currentStep" finish-status="success" class="setup-steps">
                <el-step title="生成密钥" description="系统生成专用密钥"></el-step>
                <el-step title="扫描二维码" description="使用认证应用扫描"></el-step>
                <el-step title="验证设置" description="输入验证码确认"></el-step>
            </el-steps>

            <!-- 步骤1: 生成密钥 -->
            <div v-if="currentStep === 0" class="step-content">
                <div class="step-description">
                    <h4>第一步：生成专用密钥</h4>
                    <p>点击下方按钮为您的账户生成专用的双因子认证密钥</p>
                </div>
                <div class="step-actions">
                    <el-button type="primary" :loading="loading" @click="generateSecret">
                        生成密钥
                    </el-button>
                </div>
            </div>

            <!-- 步骤2: 扫描二维码 -->
            <div v-if="currentStep === 1" class="step-content">
                <div class="step-description">
                    <h4>第二步：扫描二维码</h4>
                    <p>使用Google Authenticator或其他TOTP应用扫描下方二维码</p>
                </div>

                <div class="qr-code-section">
                    <div class="qr-code-container">
                        <img v-if="qrCodeUrl" :src="qrCodeUrl" alt="TOTP二维码" class="qr-code" />
                        <el-skeleton v-else :rows="1" animated />
                    </div>
                    
                    <div class="manual-entry">
                        <h5>手动输入密钥</h5>
                        <p>如果无法扫描二维码，可以手动输入以下密钥：</p>
                        <el-input
                            v-model="manualKey"
                            readonly
                            class="manual-key-input"
                        >
                            <template #append>
                                <el-button @click="copyToClipboard(manualKey)">复制</el-button>
                            </template>
                        </el-input>
                    </div>
                </div>

                <div class="backup-codes-section">
                    <h5>备用恢复码</h5>
                    <p class="warning-text">请妥善保存以下备用恢复码，当您无法使用认证应用时可以使用这些代码：</p>
                    <div class="backup-codes">
                        <div v-for="(code, index) in backupCodes" :key="index" class="backup-code">
                            {{ code }}
                        </div>
                    </div>
                    <el-button @click="downloadBackupCodes" type="text">下载备用码</el-button>
                </div>

                <div class="step-actions">
                    <el-button @click="currentStep = 0">上一步</el-button>
                    <el-button type="primary" @click="currentStep = 2">下一步</el-button>
                </div>
            </div>

            <!-- 步骤3: 验证设置 -->
            <div v-if="currentStep === 2" class="step-content">
                <div class="step-description">
                    <h4>第三步：验证设置</h4>
                    <p>请输入认证应用中显示的6位验证码以完成设置</p>
                </div>

                <div class="verification-section">
                    <el-form ref="verifyFormRef" :model="verifyForm" :rules="verifyRules">
                        <el-form-item prop="code">
                            <el-input
                                v-model="verifyForm.code"
                                placeholder="请输入6位验证码"
                                maxlength="6"
                                class="verify-input"
                                @keyup.enter="enableTotp"
                            />
                        </el-form-item>
                    </el-form>
                </div>

                <div class="step-actions">
                    <el-button @click="currentStep = 1">上一步</el-button>
                    <el-button type="primary" :loading="verifyLoading" @click="enableTotp">
                        完成设置
                    </el-button>
                </div>
            </div>

            <!-- 设置成功 -->
            <div v-if="currentStep === 3" class="step-content success-content">
                <div class="success-icon">
                    <el-icon size="64" color="#67C23A"><SuccessFilled /></el-icon>
                </div>
                <h4>双因子认证设置成功！</h4>
                <p>您的账户现在受到双因子认证保护，登录时需要提供验证码。</p>
                <div class="step-actions">
                    <el-button type="primary" @click="$router.push('/security')">
                        返回安全设置
                    </el-button>
                </div>
            </div>
        </el-card>
    </div>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { SuccessFilled } from '@element-plus/icons-vue'
import { totpApi } from '@/api/modules/totp'

const currentStep = ref(0)
const loading = ref(false)
const verifyLoading = ref(false)

// 设置数据
const qrCodeUrl = ref('')
const manualKey = ref('')
const backupCodes = ref([])

// 验证表单
const verifyFormRef = ref(null)
const verifyForm = reactive({
    code: ''
})

const verifyRules = {
    code: [
        { required: true, message: '请输入验证码', trigger: 'blur' },
        { len: 6, message: '验证码必须是6位数字', trigger: 'blur' },
        { pattern: /^\d{6}$/, message: '验证码只能包含数字', trigger: 'blur' }
    ]
}

// 生成密钥
const generateSecret = async () => {
    try {
        loading.value = true
        const response = await totpApi.setup()
        
        qrCodeUrl.value = response.qr_code_url
        manualKey.value = response.manual_entry_key
        backupCodes.value = response.backup_codes
        
        currentStep.value = 1
        ElMessage.success('密钥生成成功')
    } catch (error) {
        ElMessage.error('生成密钥失败：' + error.message)
    } finally {
        loading.value = false
    }
}

// 启用TOTP
const enableTotp = async () => {
    try {
        await verifyFormRef.value.validate()
        verifyLoading.value = true
        
        await totpApi.enable({ code: verifyForm.code })
        
        currentStep.value = 3
        ElMessage.success('双因子认证设置成功')
    } catch (error) {
        if (error.fields) {
            // 表单验证错误
            return
        }
        ElMessage.error('设置失败：' + error.message)
    } finally {
        verifyLoading.value = false
    }
}

// 复制到剪贴板
const copyToClipboard = async (text) => {
    try {
        await navigator.clipboard.writeText(text)
        ElMessage.success('已复制到剪贴板')
    } catch (error) {
        ElMessage.error('复制失败')
    }
}

// 下载备用码
const downloadBackupCodes = () => {
    const content = backupCodes.value.join('\n')
    const blob = new Blob([content], { type: 'text/plain' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = 'totp-backup-codes.txt'
    a.click()
    URL.revokeObjectURL(url)
    ElMessage.success('备用码已下载')
}
</script>

<style lang="scss" scoped>
.totp-setup-container {
    max-width: 800px;
    margin: 0 auto;
    padding: 20px;
}

.setup-card {
    .card-header {
        text-align: center;
        
        h3 {
            margin: 0 0 8px;
            color: #303133;
        }
        
        .subtitle {
            margin: 0;
            color: #909399;
            font-size: 14px;
        }
    }
}

.setup-steps {
    margin: 30px 0;
}

.step-content {
    min-height: 400px;
    padding: 20px 0;
    
    .step-description {
        text-align: center;
        margin-bottom: 30px;
        
        h4 {
            margin: 0 0 10px;
            color: #303133;
        }
        
        p {
            margin: 0;
            color: #606266;
        }
    }
    
    .step-actions {
        text-align: center;
        margin-top: 30px;
        
        .el-button + .el-button {
            margin-left: 12px;
        }
    }
}

.qr-code-section {
    text-align: center;
    margin-bottom: 30px;
    
    .qr-code-container {
        margin-bottom: 20px;
        
        .qr-code {
            max-width: 200px;
            border: 1px solid #dcdfe6;
            border-radius: 4px;
        }
    }
    
    .manual-entry {
        max-width: 400px;
        margin: 0 auto;
        
        h5 {
            margin: 0 0 8px;
            color: #303133;
        }
        
        p {
            margin: 0 0 12px;
            color: #606266;
            font-size: 14px;
        }
        
        .manual-key-input {
            font-family: monospace;
        }
    }
}

.backup-codes-section {
    max-width: 500px;
    margin: 30px auto;
    padding: 20px;
    background: #fdf6ec;
    border: 1px solid #f5dab1;
    border-radius: 4px;
    
    h5 {
        margin: 0 0 8px;
        color: #e6a23c;
    }
    
    .warning-text {
        margin: 0 0 15px;
        color: #e6a23c;
        font-size: 14px;
    }
    
    .backup-codes {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        gap: 8px;
        margin-bottom: 15px;
        
        .backup-code {
            padding: 8px;
            background: white;
            border: 1px solid #f5dab1;
            border-radius: 4px;
            font-family: monospace;
            text-align: center;
        }
    }
}

.verification-section {
    max-width: 300px;
    margin: 0 auto;
    
    .verify-input {
        text-align: center;
        font-size: 18px;
        font-family: monospace;
    }
}

.success-content {
    text-align: center;
    
    .success-icon {
        margin-bottom: 20px;
    }
    
    h4 {
        margin: 0 0 10px;
        color: #67C23A;
    }
    
    p {
        margin: 0 0 30px;
        color: #606266;
    }
}
</style>
