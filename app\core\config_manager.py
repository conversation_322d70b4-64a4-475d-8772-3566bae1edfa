"""
沃尔玛API配置管理器

提供统一的API配置管理，包括：
- API基础URL的动态获取
- 配置缓存机制
- Fallback默认配置
- 异步配置获取支持
"""

import asyncio
from typing import Optional, Dict, Any
from sqlalchemy.orm import Session

from app.core.logging import get_logger
from app.services.walmart_server import WalmartServerService

logger = get_logger("config_manager")


class WalmartAPIConfigManager:
    """沃尔玛API配置管理器"""
    
    # 默认配置
    DEFAULT_CONFIG = {
        "api_base_url": "https://apicard.swiftpass.cn",
        "referer": "https://servicewechat.com/wx81d3e1fe4c2e11b4/168/page-frame.html",
        "timeout": 30,
        "retry_count": 3,
    }
    
    # 内存缓存
    _cache: Dict[str, Any] = {}
    _cache_ttl: int = 300  # 5分钟缓存
    _last_update: Optional[float] = None
    
    @classmethod
    async def get_api_base_url(cls, db: Optional[Session] = None) -> str:
        """
        获取API基础URL
        
        Args:
            db: 数据库会话（可选）
            
        Returns:
            str: API基础URL
        """
        try:
            # 尝试从缓存获取
            cached_url = cls._get_from_cache("api_base_url")
            if cached_url:
                return cached_url
            
            # 从数据库获取
            if db:
                service = WalmartServerService(db)
                api_url = await service.get_api_url()
                
                if api_url:
                    # 缓存结果
                    cls._set_cache("api_base_url", api_url)
                    logger.info(f"从数据库获取API URL: {api_url}")
                    return api_url
            
            # 使用默认配置
            default_url = cls.DEFAULT_CONFIG["api_base_url"]
            logger.warning(f"使用默认API URL: {default_url}")
            return default_url
            
        except Exception as e:
            logger.error(f"获取API URL失败: {e}")
            return cls.DEFAULT_CONFIG["api_base_url"]
    
    @classmethod
    async def get_referer(cls, db: Optional[Session] = None) -> str:
        """
        获取Referer配置（固定值）

        Args:
            db: 数据库会话（可选，已忽略）

        Returns:
            str: 固定的Referer值
        """
        # 直接返回固定的Referer地址，不再从数据库或缓存获取
        return cls.DEFAULT_CONFIG["referer"]
    
    @classmethod
    async def get_full_config(cls, db: Optional[Session] = None) -> Dict[str, Any]:
        """
        获取完整配置
        
        Args:
            db: 数据库会话（可选）
            
        Returns:
            Dict[str, Any]: 完整配置字典
        """
        try:
            config = cls.DEFAULT_CONFIG.copy()
            
            # 获取动态配置
            api_url = await cls.get_api_base_url(db)
            referer = await cls.get_referer()
            
            config.update({
                "api_base_url": api_url,
                "referer": referer,
            })
            
            return config
            
        except Exception as e:
            logger.error(f"获取完整配置失败: {e}")
            return cls.DEFAULT_CONFIG.copy()
    
    @classmethod
    def _get_from_cache(cls, key: str) -> Optional[Any]:
        """从缓存获取值"""
        import time
        
        if not cls._cache or not cls._last_update:
            return None
        
        # 检查缓存是否过期
        if time.time() - cls._last_update > cls._cache_ttl:
            cls._cache.clear()
            cls._last_update = None
            return None
        
        return cls._cache.get(key)
    
    @classmethod
    def _set_cache(cls, key: str, value: Any) -> None:
        """设置缓存值"""
        import time
        
        cls._cache[key] = value
        cls._last_update = time.time()
    
    @classmethod
    def clear_cache(cls) -> None:
        """清除缓存"""
        cls._cache.clear()
        cls._last_update = None
        logger.info("配置缓存已清除")
    
    @classmethod
    def get_default_config(cls) -> Dict[str, Any]:
        """获取默认配置"""
        return cls.DEFAULT_CONFIG.copy()


# 便捷函数
async def get_walmart_api_config(db: Optional[Session] = None) -> Dict[str, Any]:
    """
    获取沃尔玛API配置的便捷函数
    
    Args:
        db: 数据库会话（可选）
        
    Returns:
        Dict[str, Any]: API配置
    """
    return await WalmartAPIConfigManager.get_full_config(db)


async def get_walmart_api_base_url(db: Optional[Session] = None) -> str:
    """
    获取沃尔玛API基础URL的便捷函数
    
    Args:
        db: 数据库会话（可选）
        
    Returns:
        str: API基础URL
    """
    return await WalmartAPIConfigManager.get_api_base_url(db)
