import axios from 'axios'
import { ElMessage } from 'element-plus'
import { useUserStore } from '@/store/modules/user'
import { API_PREFIX } from './modules/config'

// 获取API配置
const API_CONFIG = {
    baseURL: window.APP_CONFIG?.API_BASE_URL,
    timeout: window.APP_CONFIG?.API_TIMEOUT
}

// 创建 axios 实例
const request = axios.create({
    baseURL: API_CONFIG.baseURL,
    timeout: API_CONFIG.timeout
})

// 请求拦截器
request.interceptors.request.use(
    config => {
        const userStore = useUserStore()
        if (userStore.token) {
            config.headers['Authorization'] = `Bearer ${userStore.token}`
        }

        return config
    },
    error => {
        console.error('请求错误:', error)
        return Promise.reject(error)
    }
)

// 响应拦截器
request.interceptors.response.use(
    response => {
        const res = response.data

        // 如果是下载文件，直接返回
        if (response.config.responseType === 'blob') {
            return response
        }

        // 如果响应是字符串，尝试解析为 JSON
        if (typeof res === 'string') {
            try {
                return JSON.parse(res)
            } catch (e) {
                console.error('响应数据解析失败:', e)
                return res
            }
        }

        // 如果响应成功但业务状态码不是0，显示错误信息
        if (res.code !== undefined && res.code !== 0) {
            ElMessage({
                message: res.message || '请求失败',
                type: 'error',
                duration: 5 * 1000
            })
            return Promise.reject(new Error(res.message || '请求失败'))
        }
        return res
    },
    error => {
        console.error('响应错误:', error)
        const message = error.response?.data?.message || error.message || '请求失败'
        ElMessage({
            message,
            type: 'error',
            duration: 5 * 1000
        })
        return Promise.reject(error)
    }
)

// 封装 HTTP 方法 - 统一添加API前缀
export const http = {
    get: (url, config = {}) => request.get(API_PREFIX + url, config),
    post: (url, data = {}, config = {}) => request.post(API_PREFIX + url, data, config),
    put: (url, data = {}, config = {}) => request.put(API_PREFIX + url, data, config),
    delete: (url, config = {}) => request.delete(API_PREFIX + url, config),
    patch: (url, data = {}, config = {}) => request.patch(API_PREFIX + url, data, config)
}

// 导出 axios 实例
export default request
