#!/usr/bin/env python3
"""
通知中心权限测试
测试所有角色对通知中心API的访问权限和数据隔离
"""

import pytest
import requests
import json
from typing import Dict, Any


class TestNotificationPermissions:
    """通知中心权限测试类"""
    
    BASE_URL = "http://localhost:8000"
    
    def setup_class(self):
        """测试类初始化"""
        self.tokens = {}
        self.user_info = {}
        
        # 测试用户信息
        self.test_users = {
            'admin': {'username': 'admin', 'password': '7c222fb2927d828af22f592134e8932480637c0d'},
            'test1': {'username': 'test1', 'password': '12345678'},  # 商户管理员
            'test2': {'username': 'test2', 'password': '12345678'},  # CK供应商
        }
        
        # 为所有测试用户获取token
        for user_key, user_data in self.test_users.items():
            token = self._login_user(user_data['username'], user_data['password'])
            if token:
                self.tokens[user_key] = token
                self.user_info[user_key] = self._get_user_info(token)
    
    def _login_user(self, username: str, password: str) -> str:
        """用户登录获取token"""
        try:
            response = requests.post(
                f"{self.BASE_URL}/api/v1/auth/login",
                data={
                    'username': username,
                    'password': password
                }
            )
            
            if response.status_code == 200:
                data = response.json()
                return data.get('access_token')
            else:
                print(f"登录失败 {username}: {response.status_code} - {response.text}")
                return None
                
        except Exception as e:
            print(f"登录异常 {username}: {e}")
            return None
    
    def _get_user_info(self, token: str) -> Dict[str, Any]:
        """获取用户信息"""
        try:
            headers = {'Authorization': f'Bearer {token}'}
            response = requests.get(f"{self.BASE_URL}/api/v1/users/me", headers=headers)
            
            if response.status_code == 200:
                return response.json()
            else:
                return {}
                
        except Exception as e:
            print(f"获取用户信息异常: {e}")
            return {}
    
    def _make_request(self, method: str, endpoint: str, token: str, **kwargs) -> requests.Response:
        """发送HTTP请求"""
        headers = {'Authorization': f'Bearer {token}'}
        url = f"{self.BASE_URL}{endpoint}"
        
        return requests.request(method, url, headers=headers, **kwargs)
    
    def test_notification_api_access(self):
        """测试通知API访问权限"""
        print("\n=== 测试通知API访问权限 ===")
        
        for user_key, token in self.tokens.items():
            if not token:
                continue
                
            print(f"\n测试用户: {user_key}")
            
            # 测试获取通知列表
            response = self._make_request('GET', '/api/v1/notifications', token)
            print(f"  获取通知列表: {response.status_code}")
            
            if response.status_code == 200:
                data = response.json()
                total = data.get('total', 0)
                print(f"  可访问通知数量: {total}")
                
                # 验证数据隔离
                if user_key == 'admin':
                    # 超级管理员应该能看到所有通知
                    assert total >= 0, "超级管理员应该能看到通知"
                elif user_key == 'test1':
                    # 商户管理员应该能看到商户级通知
                    assert total >= 0, "商户管理员应该能看到商户级通知"
                elif user_key == 'test2':
                    # CK供应商应该能看到部门级通知
                    assert total >= 0, "CK供应商应该能看到部门级通知"
            else:
                pytest.fail(f"用户 {user_key} 无法访问通知API: {response.status_code}")
    
    def test_notification_unread_count(self):
        """测试未读通知数量API"""
        print("\n=== 测试未读通知数量API ===")
        
        for user_key, token in self.tokens.items():
            if not token:
                continue
                
            print(f"\n测试用户: {user_key}")
            
            response = self._make_request('GET', '/api/v1/notifications/unread-count', token)
            print(f"  获取未读数量: {response.status_code}")
            
            if response.status_code == 200:
                data = response.json()
                unread_count = data.get('unread_count', 0)
                print(f"  未读通知数量: {unread_count}")
                assert unread_count >= 0, "未读通知数量应该大于等于0"
            else:
                pytest.fail(f"用户 {user_key} 无法获取未读通知数量: {response.status_code}")
    
    def test_notification_data_isolation(self):
        """测试通知数据隔离"""
        print("\n=== 测试通知数据隔离 ===")
        
        # 获取各用户的通知数据
        user_notifications = {}
        
        for user_key, token in self.tokens.items():
            if not token:
                continue
                
            response = self._make_request('GET', '/api/v1/notifications', token)
            if response.status_code == 200:
                data = response.json()
                user_notifications[user_key] = {
                    'total': data.get('total', 0),
                    'items': data.get('items', [])
                }
        
        # 验证数据隔离规则
        if 'admin' in user_notifications and 'test1' in user_notifications:
            admin_total = user_notifications['admin']['total']
            test1_total = user_notifications['test1']['total']
            
            # 超级管理员应该能看到更多或相等的通知
            assert admin_total >= test1_total, "超级管理员应该能看到更多或相等的通知"
            print(f"  数据隔离验证: admin({admin_total}) >= test1({test1_total}) ✓")
        
        if 'test1' in user_notifications and 'test2' in user_notifications:
            test1_total = user_notifications['test1']['total']
            test2_total = user_notifications['test2']['total']
            
            # 商户管理员应该能看到更多或相等的通知（商户级 >= 部门级）
            assert test1_total >= test2_total, "商户管理员应该能看到更多或相等的通知"
            print(f"  数据隔离验证: test1({test1_total}) >= test2({test2_total}) ✓")
    
    def test_notification_operations(self):
        """测试通知操作权限"""
        print("\n=== 测试通知操作权限 ===")
        
        for user_key, token in self.tokens.items():
            if not token:
                continue
                
            print(f"\n测试用户: {user_key}")
            
            # 获取用户的通知列表
            response = self._make_request('GET', '/api/v1/notifications', token)
            if response.status_code != 200:
                continue
                
            data = response.json()
            notifications = data.get('items', [])
            
            if notifications:
                # 测试标记已读操作
                notification_id = notifications[0]['id']
                response = self._make_request(
                    'POST', 
                    f'/api/v1/notifications/{notification_id}/read', 
                    token
                )
                print(f"  标记通知已读: {response.status_code}")
                
                # 应该成功或者通知已经是已读状态
                assert response.status_code in [200, 400], f"标记已读操作失败: {response.status_code}"
            
            # 测试全部已读操作
            response = self._make_request('POST', '/api/v1/notifications/read-all', token)
            print(f"  全部标记已读: {response.status_code}")
            assert response.status_code == 200, f"全部标记已读操作失败: {response.status_code}"
    
    def test_user_menu_access(self):
        """测试用户菜单访问"""
        print("\n=== 测试用户菜单访问 ===")
        
        for user_key, token in self.tokens.items():
            if not token:
                continue
                
            print(f"\n测试用户: {user_key}")
            
            response = self._make_request('GET', '/api/v1/menus/user-menus', token)
            print(f"  获取用户菜单: {response.status_code}")
            
            if response.status_code == 200:
                menus = response.json()
                
                # 检查是否包含通知中心菜单
                has_notification_menu = False
                for menu in menus:
                    if menu.get('code') == 'notification':
                        has_notification_menu = True
                        break
                
                print(f"  包含通知中心菜单: {has_notification_menu}")
                assert has_notification_menu, f"用户 {user_key} 应该能看到通知中心菜单"
            else:
                pytest.fail(f"用户 {user_key} 无法获取菜单: {response.status_code}")


def run_tests():
    """运行测试"""
    print("沃尔玛绑卡系统 - 通知中心权限测试")
    print("=" * 50)
    
    test_instance = TestNotificationPermissions()
    test_instance.setup_class()
    
    try:
        # 运行所有测试
        test_instance.test_notification_api_access()
        test_instance.test_notification_unread_count()
        test_instance.test_notification_data_isolation()
        test_instance.test_notification_operations()
        test_instance.test_user_menu_access()
        
        print("\n" + "=" * 50)
        print("✅ 所有测试通过！通知中心权限配置正确！")
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        raise


if __name__ == "__main__":
    run_tests()
