package model

import (
	"time"
)

// MessageType 消息类型枚举
type MessageType string

const (
	MessageTypeBindCard       MessageType = "bind_card"
	MessageTypeBalanceQuery   MessageType = "balance_query"
	MessageTypeCallback       MessageType = "callback"
	MessageTypeRetry          MessageType = "retry"
)

// GenericQueueMessage 通用队列消息结构
type GenericQueueMessage struct {
	ID          string                 `json:"id"`
	Type        MessageType            `json:"type"`
	Data        map[string]interface{} `json:"data"`
	Timestamp   time.Time              `json:"timestamp"`
	RetryCount  int                    `json:"retry_count"`
	MaxRetries  int                    `json:"max_retries"`
	Priority    int                    `json:"priority"`
	TraceID     string                 `json:"trace_id"`
	CorrelationID string               `json:"correlation_id"`
}

// BindCardMessage 绑卡消息
type BindCardMessage struct {
	RecordID        string  `json:"record_id"`
	CardNumber      string  `json:"card_number"`
	CardPassword    string  `json:"card_password"`
	MerchantID      int     `json:"merchant_id"`
	DepartmentID    *int    `json:"department_id,omitempty"`
	Amount          int     `json:"amount"`
	MerchantOrderID string  `json:"merchant_order_id"`
	CallbackURL     string  `json:"callback_url,omitempty"`
	ExtData         string  `json:"ext_data,omitempty"`
	ClientIP        string  `json:"client_ip"`
	UserAgent       string  `json:"user_agent"`
	TraceID         string  `json:"trace_id"`
	RequestID       string  `json:"request_id"`
	Debug           bool    `json:"debug"`
	IsTestMode      bool    `json:"is_test_mode"`
	RetryCount      int     `json:"retry_count"`
	MaxRetries      int     `json:"max_retries"`
	CreatedAt       time.Time `json:"created_at"`
}

// BalanceQueryMessage 金额查询消息
type BalanceQueryMessage struct {
	RecordID        string    `json:"record_id"`
	CardNumber      string    `json:"card_number"`
	CardPassword    string    `json:"card_password"`
	WalmartCKID     int       `json:"walmart_ck_id"`
	CKSign          string    `json:"ck_sign"`
	MerchantID      int       `json:"merchant_id"`
	DepartmentID    int       `json:"department_id"`
	Amount          int       `json:"amount"`
	MerchantOrderID string    `json:"merchant_order_id"`
	CallbackURL     string    `json:"callback_url,omitempty"`
	ExtData         string    `json:"ext_data,omitempty"`
	TraceID         string    `json:"trace_id"`
	RequestID       string    `json:"request_id"`
	RetryCount      int       `json:"retry_count"`
	MaxRetries      int       `json:"max_retries"`
	CreatedAt       time.Time `json:"created_at"`
	// 绑卡成功的相关信息
	BindResult      map[string]interface{} `json:"bind_result,omitempty"`
}

// CallbackMessage 回调消息
type CallbackMessage struct {
	RecordID        string                 `json:"record_id"`
	MerchantID      int                    `json:"merchant_id"`
	MerchantOrderID string                 `json:"merchant_order_id"`
	CallbackURL     string                 `json:"callback_url"`
	Status          string                 `json:"status"` // success, failed
	Amount          int                    `json:"amount"`
	ActualAmount    *int                   `json:"actual_amount,omitempty"`
	CardNumber      string                 `json:"card_number"`
	ExtData         string                 `json:"ext_data,omitempty"`
	TraceID         string                 `json:"trace_id"`
	RequestID       string                 `json:"request_id"`
	ErrorMessage    string                 `json:"error_message,omitempty"`
	ErrorCode       string                 `json:"error_code,omitempty"`
	RetryCount      int                    `json:"retry_count"`
	MaxRetries      int                    `json:"max_retries"`
	CreatedAt       time.Time              `json:"created_at"`
	CompletedAt     *time.Time             `json:"completed_at,omitempty"`
	// 额外的回调数据
	CallbackData    map[string]interface{} `json:"callback_data,omitempty"`
}

// RetryMessage 重试消息
type RetryMessage struct {
	OriginalMessageType MessageType            `json:"original_message_type"`
	OriginalMessage     map[string]interface{} `json:"original_message"`
	RetryReason         string                 `json:"retry_reason"`
	RetryStrategy       string                 `json:"retry_strategy"` // immediate, exponential_backoff, ck_switch
	NextRetryAt         time.Time              `json:"next_retry_at"`
	RetryCount          int                    `json:"retry_count"`
	MaxRetries          int                    `json:"max_retries"`
	TraceID             string                 `json:"trace_id"`
	RecordID            string                 `json:"record_id"`
	CreatedAt           time.Time              `json:"created_at"`
	// CK切换相关
	ShouldSwitchCK      bool                   `json:"should_switch_ck"`
	CurrentCKID         *int                   `json:"current_ck_id,omitempty"`
	FailedCKIDs         []int                  `json:"failed_ck_ids,omitempty"`
}

// MessageStatus 消息状态
type MessageStatus string

const (
	MessageStatusPending    MessageStatus = "pending"
	MessageStatusProcessing MessageStatus = "processing"
	MessageStatusSuccess    MessageStatus = "success"
	MessageStatusFailed     MessageStatus = "failed"
	MessageStatusRetrying   MessageStatus = "retrying"
	MessageStatusExpired    MessageStatus = "expired"
)

// ProcessingResult 处理结果
type ProcessingResult struct {
	Success      bool                   `json:"success"`
	Status       MessageStatus          `json:"status"`
	Message      string                 `json:"message"`
	ErrorCode    string                 `json:"error_code,omitempty"`
	Data         map[string]interface{} `json:"data,omitempty"`
	ShouldRetry  bool                   `json:"should_retry"`
	RetryDelay   time.Duration          `json:"retry_delay,omitempty"`
	ShouldSwitchCK bool                 `json:"should_switch_ck"`
	ProcessedAt  time.Time              `json:"processed_at"`
}

// RetryStrategy 重试策略
type RetryStrategy struct {
	MaxAttempts        int           `json:"max_attempts"`
	InitialDelay       time.Duration `json:"initial_delay"`
	MaxDelay           time.Duration `json:"max_delay"`
	BackoffMultiplier  float64       `json:"backoff_multiplier"`
	RetryableErrors    []string      `json:"retryable_errors"`
	CKSwitchErrors     []string      `json:"ck_switch_errors"`
	NonRetryableErrors []string      `json:"non_retryable_errors"`
}

// QueueConfig 队列配置
type QueueConfig struct {
	Name         string `json:"name"`
	PrefetchCount int   `json:"prefetch_count"`
	Concurrency  int   `json:"concurrency"`
	Durable      bool  `json:"durable"`
	AutoDelete   bool  `json:"auto_delete"`
	Exclusive    bool  `json:"exclusive"`
}
