#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单的软删除功能测试脚本
"""

import requests
import json
import uuid

BASE_URL = "http://localhost:20000/api/v1"

def login():
    """登录获取token"""
    login_data = {
        'username': 'admin',
        'password': '7c222fb2927d828af22f592134e8932480637c0d'
    }
    
    response = requests.post(
        f"{BASE_URL}/auth/login",
        data=login_data,  # 使用data而不是json
        headers={'Content-Type': 'application/x-www-form-urlencoded'}
    )
    
    print(f"登录状态: {response.status_code}")
    if response.status_code == 200:
        token = response.json()['data']['access_token']
        print("✅ 登录成功")
        return token
    else:
        print(f"❌ 登录失败: {response.text}")
        return None

def create_test_ck(token):
    """创建测试CK"""
    test_data = {
        "sign": f"test_soft_delete_{uuid.uuid4().hex[:8]}@token#signature#26",
        "daily_limit": 100,
        "hourly_limit": 50,
        "active": 1,
        "description": "软删除测试CK",
        "merchant_id": 1,
        "department_id": 1
    }
    
    headers = {'Authorization': f'Bearer {token}'}
    response = requests.post(f"{BASE_URL}/walmart-ck", json=test_data, headers=headers)
    
    print(f"创建CK状态: {response.status_code}")
    if response.status_code == 200:
        response_data = response.json()
        # 处理嵌套的响应格式
        if 'data' in response_data and 'data' in response_data['data']:
            ck_id = response_data['data']['data']['id']
        else:
            ck_id = response_data['data']['id']
        print(f"✅ 创建CK成功，ID: {ck_id}")
        return ck_id
    else:
        print(f"❌ 创建CK失败: {response.text}")
        return None

def get_ck_list(token):
    """获取CK列表"""
    headers = {'Authorization': f'Bearer {token}'}
    response = requests.get(f"{BASE_URL}/walmart-ck", headers=headers)
    
    print(f"获取CK列表状态: {response.status_code}")
    if response.status_code == 200:
        data = response.json()['data']
        items = data.get('items', [])
        print(f"✅ 获取CK列表成功，共 {len(items)} 个CK")
        return items
    else:
        print(f"❌ 获取CK列表失败: {response.text}")
        return []

def delete_ck(token, ck_id):
    """软删除CK"""
    headers = {'Authorization': f'Bearer {token}'}
    response = requests.delete(f"{BASE_URL}/walmart-ck/{ck_id}", headers=headers)
    
    print(f"删除CK状态: {response.status_code}")
    if response.status_code == 200:
        print(f"✅ 软删除CK成功，ID: {ck_id}")
        return True
    else:
        print(f"❌ 软删除CK失败: {response.text}")
        return False

def get_ck_detail(token, ck_id):
    """获取CK详情"""
    headers = {'Authorization': f'Bearer {token}'}
    response = requests.get(f"{BASE_URL}/walmart-ck/{ck_id}", headers=headers)
    
    print(f"获取CK详情状态: {response.status_code}")
    if response.status_code == 200:
        print(f"✅ 获取CK详情成功")
        return response.json()['data']
    elif response.status_code == 404:
        print(f"✅ CK已被软删除，无法获取详情（符合预期）")
        return None
    else:
        print(f"❌ 获取CK详情失败: {response.text}")
        return None

def test_soft_delete():
    """测试软删除功能"""
    print("🧪 开始软删除功能测试")
    print("="*50)
    
    # 1. 登录
    token = login()
    if not token:
        return False
    
    # 2. 获取删除前的CK列表
    print("\n--- 获取删除前的CK列表 ---")
    before_list = get_ck_list(token)
    before_count = len(before_list)
    
    # 3. 创建测试CK
    print("\n--- 创建测试CK ---")
    ck_id = create_test_ck(token)
    if not ck_id:
        return False
    
    # 4. 验证CK创建成功
    print("\n--- 验证CK创建成功 ---")
    after_create_list = get_ck_list(token)
    after_create_count = len(after_create_list)
    
    if after_create_count == before_count + 1:
        print("✅ CK创建后列表数量正确")
    else:
        print(f"❌ CK创建后列表数量不正确，期望: {before_count + 1}，实际: {after_create_count}")
    
    # 5. 软删除CK
    print("\n--- 执行软删除 ---")
    delete_success = delete_ck(token, ck_id)
    if not delete_success:
        return False
    
    # 6. 验证软删除后CK在列表中不可见
    print("\n--- 验证软删除后列表 ---")
    after_delete_list = get_ck_list(token)
    after_delete_count = len(after_delete_list)
    
    if after_delete_count == before_count:
        print("✅ 软删除后CK列表数量正确（已删除CK不在列表中）")
    else:
        print(f"❌ 软删除后CK列表数量不正确，期望: {before_count}，实际: {after_delete_count}")
    
    # 检查已删除的CK是否在列表中
    deleted_ck_in_list = any(item.get('id') == ck_id for item in after_delete_list)
    if not deleted_ck_in_list:
        print("✅ 已删除的CK正确地从列表中隐藏")
    else:
        print("❌ 已删除的CK仍在列表中可见")
    
    # 7. 验证无法获取已删除CK的详情
    print("\n--- 验证已删除CK详情不可访问 ---")
    get_ck_detail(token, ck_id)
    
    # 8. 检查CK是否有is_deleted字段
    print("\n--- 检查CK数据结构 ---")
    if after_create_list:
        first_ck = after_create_list[0]
        if 'is_deleted' in first_ck:
            print("✅ CK数据包含is_deleted字段")
        else:
            print("⚠️ CK数据不包含is_deleted字段（可能在响应中被过滤）")
    
    print("\n" + "="*50)
    print("🎉 软删除功能测试完成")
    return True

if __name__ == "__main__":
    test_soft_delete()
