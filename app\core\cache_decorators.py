"""
缓存装饰器
提供自动缓存功能，集成性能监控
"""

import functools
import hashlib
import json
import logging
from typing import Any, Callable, Optional, Union

from app.services.cache_service import cache_service
from app.core.performance_monitor import performance_monitor

logger = logging.getLogger(__name__)


def cache_result(
    ttl: int = 3600,
    key_prefix: str = "",
    key_generator: Optional[Callable] = None,
    condition: Optional[Callable] = None,
    monitor: bool = True
):
    """
    缓存函数结果装饰器
    
    Args:
        ttl: 缓存过期时间（秒）
        key_prefix: 缓存键前缀
        key_generator: 自定义键生成函数
        condition: 缓存条件函数，返回True时才缓存
        monitor: 是否启用性能监控
    """
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            # 生成缓存键
            if key_generator:
                cache_key = key_generator(*args, **kwargs)
            else:
                cache_key = _generate_cache_key(func, key_prefix, *args, **kwargs)
            
            # 尝试从缓存获取
            cached_result = cache_service.get(cache_key)
            if cached_result is not None:
                if monitor:
                    performance_monitor.record_cache_hit()
                logger.debug(f"缓存命中: {cache_key}")
                return cached_result
            
            if monitor:
                performance_monitor.record_cache_miss()
            
            # 执行函数
            result = func(*args, **kwargs)
            
            # 检查缓存条件
            if condition is None or condition(result):
                # 缓存结果
                cache_service.set(cache_key, result, ttl)
                logger.debug(f"缓存设置: {cache_key}")
            
            return result
        return wrapper
    return decorator


def cache_user_data(ttl: int = 1800):
    """
    用户数据缓存装饰器（30分钟）
    """
    def key_generator(*args, **kwargs):
        user_id = None
        if args and hasattr(args[0], 'id'):
            user_id = args[0].id
        elif 'user_id' in kwargs:
            user_id = kwargs['user_id']
        elif len(args) > 1:
            user_id = args[1]
        
        if user_id:
            return f"user_data:{user_id}:{_hash_args(*args, **kwargs)}"
        return f"user_data:{_hash_args(*args, **kwargs)}"
    
    return cache_result(ttl=ttl, key_generator=key_generator)


def cache_merchant_data(ttl: int = 3600):
    """
    商户数据缓存装饰器（1小时）
    """
    def key_generator(*args, **kwargs):
        merchant_id = None
        if 'merchant_id' in kwargs:
            merchant_id = kwargs['merchant_id']
        elif len(args) > 0 and isinstance(args[0], int):
            merchant_id = args[0]
        
        if merchant_id:
            return f"merchant_data:{merchant_id}:{_hash_args(*args, **kwargs)}"
        return f"merchant_data:{_hash_args(*args, **kwargs)}"
    
    return cache_result(ttl=ttl, key_generator=key_generator)


def cache_statistics(ttl: int = 600):
    """
    统计数据缓存装饰器（10分钟）
    """
    def key_generator(*args, **kwargs):
        return f"stats:{_hash_args(*args, **kwargs)}"
    
    return cache_result(ttl=ttl, key_generator=key_generator)


def cache_ck_data(ttl: int = 300):
    """
    CK数据缓存装饰器（5分钟）
    """
    def key_generator(*args, **kwargs):
        ck_id = None
        if 'ck_id' in kwargs:
            ck_id = kwargs['ck_id']
        elif len(args) > 0 and isinstance(args[0], int):
            ck_id = args[0]
        
        if ck_id:
            return f"ck_data:{ck_id}:{_hash_args(*args, **kwargs)}"
        return f"ck_data:{_hash_args(*args, **kwargs)}"
    
    return cache_result(ttl=ttl, key_generator=key_generator)


def invalidate_cache(pattern: str):
    """
    缓存失效装饰器
    在函数执行后清除匹配的缓存
    """
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            result = func(*args, **kwargs)
            
            # 清除缓存
            try:
                if '*' in pattern:
                    # 模式匹配清除
                    cache_service.invalidate_statistics_cache(pattern)
                else:
                    # 精确清除
                    cache_service.delete(pattern)
                logger.debug(f"缓存失效: {pattern}")
            except Exception as e:
                logger.error(f"缓存失效失败: {e}")
            
            return result
        return wrapper
    return decorator


def invalidate_user_cache(user_id_param: str = 'user_id'):
    """
    用户缓存失效装饰器
    """
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            result = func(*args, **kwargs)
            
            # 获取用户ID
            user_id = kwargs.get(user_id_param)
            if not user_id and args:
                if hasattr(args[0], 'id'):
                    user_id = args[0].id
                elif len(args) > 1:
                    user_id = args[1]
            
            if user_id:
                cache_service.invalidate_user_cache(user_id)
                logger.debug(f"用户缓存失效: {user_id}")
            
            return result
        return wrapper
    return decorator


def invalidate_merchant_cache(merchant_id_param: str = 'merchant_id'):
    """
    商户缓存失效装饰器
    """
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            result = func(*args, **kwargs)
            
            # 获取商户ID
            merchant_id = kwargs.get(merchant_id_param)
            if not merchant_id and args:
                merchant_id = args[0] if isinstance(args[0], int) else None
            
            if merchant_id:
                cache_service.invalidate_merchant_cache(merchant_id)
                logger.debug(f"商户缓存失效: {merchant_id}")
            
            return result
        return wrapper
    return decorator


def _generate_cache_key(func: Callable, prefix: str, *args, **kwargs) -> str:
    """生成缓存键"""
    func_name = f"{func.__module__}.{func.__name__}"
    args_hash = _hash_args(*args, **kwargs)
    
    if prefix:
        return f"{prefix}:{func_name}:{args_hash}"
    return f"{func_name}:{args_hash}"


def _hash_args(*args, **kwargs) -> str:
    """对参数进行哈希"""
    # 过滤掉不可序列化的参数
    serializable_args = []
    for arg in args:
        if _is_serializable(arg):
            serializable_args.append(arg)
        else:
            serializable_args.append(str(type(arg)))
    
    serializable_kwargs = {}
    for key, value in kwargs.items():
        if _is_serializable(value):
            serializable_kwargs[key] = value
        else:
            serializable_kwargs[key] = str(type(value))
    
    # 创建哈希
    content = json.dumps({
        'args': serializable_args,
        'kwargs': serializable_kwargs
    }, sort_keys=True, default=str)
    
    return hashlib.md5(content.encode()).hexdigest()[:16]


def _is_serializable(obj) -> bool:
    """检查对象是否可序列化"""
    try:
        json.dumps(obj, default=str)
        return True
    except (TypeError, ValueError):
        return False


# 便捷的缓存装饰器组合
def cache_hot_data(ttl: int = 3600):
    """热点数据缓存（1小时）"""
    return cache_result(ttl=ttl, key_prefix="hot_data")


def cache_cold_data(ttl: int = 7200):
    """冷数据缓存（2小时）"""
    return cache_result(ttl=ttl, key_prefix="cold_data")


def cache_config_data(ttl: int = 86400):
    """配置数据缓存（24小时）"""
    return cache_result(ttl=ttl, key_prefix="config")
