"""
用户管理API端点 - 重构版本
基于服务层架构，代码简洁清晰
"""

from typing import Any, Optional, List
from fastapi import APIRouter, Depends, HTTPException, Query, Path, status, Body
from sqlalchemy.orm import Session

from app.schemas.user import (
    UserListResponse,
    User as UserSchema,
    UserCreate,
    UserUpdate,
    UserStatusUpdate,
)
from app.api import deps
from app.models.user import User
from app.services.user_api_service import UserAPIService
from app.services.user_permission_service import UserPermissionService
from app.services.permission_service import PermissionService
from app.core.logging import get_logger

logger = get_logger(__name__)
router = APIRouter()


def _check_permission(db: Session, current_user: User, permission: str):
    """检查用户权限的辅助函数"""
    perm_service = PermissionService(db)
    has_permission = perm_service.check_user_permission(current_user, permission)
    if not has_permission:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail=f"没有{permission}权限",
        )


@router.get("", response_model=UserListResponse)
async def read_users(
    db: Session = Depends(deps.get_db),
    page: int = Query(1, ge=1, description="页码，从1开始"),
    page_size: int = Query(10, ge=1, le=100, description="每页记录数，最大100"),
    current_user: User = Depends(deps.get_current_active_user),
    merchant_id: Optional[int] = None,
    role: Optional[str] = None,
    is_active: Optional[bool] = None,
    username: Optional[str] = None,
):
    """
    获取用户列表

    权限要求: "api:users:read"
    """
    _check_permission(db, current_user, "api:users:read")
    
    user_api_service = UserAPIService(db)
    return user_api_service.get_users_with_filters(
        current_user=current_user,
        page=page,
        page_size=page_size,
        merchant_id=merchant_id,
        role=role,
        is_active=is_active,
        username=username,
    )


@router.post("", response_model=UserSchema)
async def create_user(
    *,
    db: Session = Depends(deps.get_db),
    user_in: UserCreate,
    current_user: User = Depends(deps.get_current_active_user),
):
    """
    创建新用户
    
    权限要求: "user:create"
    """
    _check_permission(db, current_user, "user:create")
    
    user_api_service = UserAPIService(db)
    return user_api_service.create_user(user_in, current_user)


@router.get("/me", response_model=UserSchema)
def read_user_me(
    current_user: User = Depends(deps.get_current_active_user),
    db: Session = Depends(deps.get_db),
) -> Any:
    """
    获取当前登录用户信息，包含权限、数据范围和菜单

    个人信息获取是基本权利，所有登录用户都可以访问，无需特殊权限
    """
    # 移除权限检查 - 获取个人信息是所有登录用户的基本权利
    user_permission_service = UserPermissionService(db)
    return user_permission_service.get_user_me_info(current_user)


@router.get("/{user_id}", response_model=UserSchema)
async def read_user(
    *,
    db: Session = Depends(deps.get_db),
    user_id: int = Path(..., gt=0),
    current_user: User = Depends(deps.get_current_active_user),
):
    """
    获取用户详情

    权限要求: "api:users:read"（查看自己的信息不需要权限）
    """
    if current_user.id != user_id:
        _check_permission(db, current_user, "api:users:read")
    
    user_api_service = UserAPIService(db)
    return user_api_service.get_user_detail(user_id, current_user)


@router.put("/{user_id}", response_model=UserSchema)
async def update_user(
    *,
    db: Session = Depends(deps.get_db),
    user_id: int = Path(..., gt=0),
    user_in: UserUpdate,
    current_user: User = Depends(deps.get_current_active_user),
):
    """
    更新用户信息
    
    权限要求: "user:edit"（编辑自己的信息不需要权限）
    """
    if current_user.id != user_id:
        _check_permission(db, current_user, "user:edit")
    
    user_api_service = UserAPIService(db)
    return user_api_service.update_user(user_id, user_in, current_user)


@router.delete("/{user_id}", response_model=UserSchema)
async def delete_user(
    *,
    db: Session = Depends(deps.get_db),
    user_id: int = Path(..., gt=0),
    current_user: User = Depends(deps.get_current_active_user),
):
    """
    删除用户
    
    权限要求: "user:delete"
    """
    _check_permission(db, current_user, "user:delete")
    
    user_api_service = UserAPIService(db)
    return user_api_service.delete_user(user_id, current_user)


@router.patch("/{user_id}/status", response_model=UserSchema)
async def update_user_status(
    *,
    db: Session = Depends(deps.get_db),
    user_id: int = Path(..., gt=0, description="用户ID"),
    status_in: UserStatusUpdate = Body(...),
    current_user: User = Depends(deps.get_current_active_user),
):
    """
    更新用户状态 (激活/禁用)
    
    权限要求: "user:status"
    """
    _check_permission(db, current_user, "user:status")
    
    user_api_service = UserAPIService(db)
    return user_api_service.update_user_status(user_id, status_in, current_user)


@router.post("/{user_id}/reset-password", response_model=UserSchema)
async def reset_user_password(
    *,
    db: Session = Depends(deps.get_db),
    user_id: int = Path(..., gt=0),
    new_password: str,
    current_user: User = Depends(deps.get_current_active_user),
):
    """
    重置用户密码
    
    权限要求: "user:reset_password"（重置自己的密码不需要权限）
    """
    if current_user.id != user_id:
        _check_permission(db, current_user, "user:reset_password")
    
    user_api_service = UserAPIService(db)
    return user_api_service.reset_user_password(user_id, new_password, current_user)


@router.put("/me", response_model=UserSchema)
def update_user_me(
    *,
    db: Session = Depends(deps.get_db),
    user_in: UserUpdate,
    current_user: User = Depends(deps.get_current_active_user),
) -> Any:
    """
    更新自己的用户信息

    权限要求:
    - "api:users:update-me": 更新个人信息权限
    """
    # 权限检查
    from app.services.permission_service import PermissionService
    permission_service = PermissionService(db)
    has_permission = permission_service.check_user_permission(
        current_user, "api:users:update-me"
    )
    if not has_permission:
        from fastapi import HTTPException, status
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="没有更新个人信息的权限",
        )

    user_api_service = UserAPIService(db)
    return user_api_service.update_user(current_user.id, user_in, current_user)


@router.get("/me/permissions", response_model=dict)
def get_my_permissions(
    current_user: User = Depends(deps.get_current_active_user),
    db: Session = Depends(deps.get_db),
) -> Any:
    """
    获取当前用户的所有权限代码

    权限要求:
    - "api:users:permissions": 查看个人权限信息
    """
    # 权限检查
    from app.services.permission_service import PermissionService
    permission_service = PermissionService(db)
    has_permission = permission_service.check_user_permission(
        current_user, "api:users:permissions"
    )
    if not has_permission:
        from fastapi import HTTPException, status
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="没有查看个人权限信息的权限",
        )

    user_permission_service = UserPermissionService(db)
    return user_permission_service.get_user_permissions(current_user)


@router.get("/{user_id}/roles", response_model=dict)
async def get_user_roles(
    *,
    db: Session = Depends(deps.get_db),
    user_id: int = Path(..., gt=0),
    current_user: User = Depends(deps.get_current_active_user),
):
    """
    获取用户角色列表

    权限要求: "api:users:read"（查看自己的角色不需要权限）
    """
    if current_user.id != user_id:
        _check_permission(db, current_user, "api:users:read")
    
    user_permission_service = UserPermissionService(db)
    return user_permission_service.get_user_roles(user_id, current_user)


@router.post("/{user_id}/roles", response_model=dict)
async def assign_roles_to_user(
    *,
    db: Session = Depends(deps.get_db),
    user_id: int = Path(..., gt=0),
    role_ids: list[int] = Body(..., description="角色ID列表"),
    current_user: User = Depends(deps.get_current_active_user),
):
    """
    为用户分配角色
    
    权限要求: "user:role"
    """
    _check_permission(db, current_user, "user:role")
    
    user_permission_service = UserPermissionService(db)
    return user_permission_service.assign_roles_to_user(user_id, role_ids, current_user)


@router.put("/{user_id}/roles", response_model=dict)
async def update_user_roles(
    *,
    db: Session = Depends(deps.get_db),
    user_id: int = Path(..., gt=0),
    role_ids: list[int] = Body(..., description="角色ID列表"),
    current_user: User = Depends(deps.get_current_active_user),
):
    """
    更新用户角色（PUT方式）
    
    权限要求: "user:role"
    """
    _check_permission(db, current_user, "user:role")
    
    user_permission_service = UserPermissionService(db)
    return user_permission_service.update_user_roles(user_id, role_ids, current_user)
