"""
双因子认证API端点
"""

from typing import Any
from fastapi import APIRouter, Depends, HTTPException, Request, status
from sqlalchemy.orm import Session

from app import schemas
from app.api import deps
from app.models.user import User
from app.services.totp_service import TOTPService
from app.schemas.totp import (
    TOTPSetupRequest, TOTPSetupResponse, TOTPVerifyRequest, TOTPVerifyResponse,
    TOTPEnableRequest, TOTPDisableRequest, TOTPStatusResponse,
    BackupCodeUseRequest
)
import logging

logger = logging.getLogger(__name__)

router = APIRouter()


def get_client_ip(request: Request) -> str:
    """获取客户端IP地址"""
    forwarded = request.headers.get("X-Forwarded-For")
    if forwarded:
        return forwarded.split(",")[0].strip()
    return request.client.host


@router.get("/status", response_model=TOTPStatusResponse)
def get_totp_status(
    current_user: User = Depends(deps.get_current_user),
    db: Session = Depends(deps.get_db)
) -> Any:
    """
    获取当前用户的TOTP状态
    """
    try:
        totp_service = TOTPService(db)
        return totp_service.get_totp_status(current_user)
    except Exception as e:
        logger.error(f"获取TOTP状态失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取TOTP状态失败"
        )


@router.post("/setup", response_model=TOTPSetupResponse)
def setup_totp(
    request: Request,
    setup_request: TOTPSetupRequest,
    current_user: User = Depends(deps.get_current_user),
    db: Session = Depends(deps.get_db)
) -> Any:
    """
    设置TOTP双因子认证
    """
    try:
        if current_user.totp_enabled:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="用户已启用双因子认证"
            )
        
        totp_service = TOTPService(db)
        client_ip = get_client_ip(request)
        
        return totp_service.setup_totp(current_user, client_ip)
        
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        logger.error(f"设置TOTP失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="设置TOTP失败"
        )


@router.post("/verify", response_model=TOTPVerifyResponse)
def verify_totp(
    request: Request,
    verify_request: TOTPVerifyRequest,
    current_user: User = Depends(deps.get_current_user),
    db: Session = Depends(deps.get_db)
) -> Any:
    """
    验证TOTP验证码
    """
    try:
        totp_service = TOTPService(db)
        client_ip = get_client_ip(request)
        
        return totp_service.verify_totp(current_user, verify_request.code, client_ip)
        
    except Exception as e:
        logger.error(f"验证TOTP失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="验证TOTP失败"
        )


@router.post("/enable", response_model=schemas.MessageResponse)
def enable_totp(
    request: Request,
    enable_request: TOTPEnableRequest,
    current_user: User = Depends(deps.get_current_user),
    db: Session = Depends(deps.get_db)
) -> Any:
    """
    启用TOTP双因子认证
    """
    try:
        if current_user.totp_enabled:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="用户已启用双因子认证"
            )
        
        totp_service = TOTPService(db)
        client_ip = get_client_ip(request)
        
        success = totp_service.enable_totp(current_user, enable_request.code, client_ip)
        
        if success:
            return schemas.MessageResponse(message="双因子认证启用成功")
        else:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="验证码错误，启用失败"
            )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"启用TOTP失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="启用TOTP失败"
        )


@router.post("/disable", response_model=schemas.MessageResponse)
def disable_totp(
    request: Request,
    disable_request: TOTPDisableRequest,
    current_user: User = Depends(deps.get_current_user),
    db: Session = Depends(deps.get_db)
) -> Any:
    """
    禁用TOTP双因子认证
    """
    try:
        totp_service = TOTPService(db)
        client_ip = get_client_ip(request)
        
        success = totp_service.disable_totp(
            current_user, 
            disable_request.password, 
            disable_request.code,
            client_ip
        )
        
        if success:
            return schemas.MessageResponse(message="双因子认证禁用成功")
        else:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="密码或验证码错误，禁用失败"
            )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"禁用TOTP失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="禁用TOTP失败"
        )


@router.post("/backup-code/verify", response_model=TOTPVerifyResponse)
def verify_backup_code(
    request: Request,
    backup_request: BackupCodeUseRequest,
    current_user: User = Depends(deps.get_current_user),
    db: Session = Depends(deps.get_db)
) -> Any:
    """
    验证备用恢复码
    """
    try:
        totp_service = TOTPService(db)
        client_ip = get_client_ip(request)
        
        success = totp_service._verify_backup_code(current_user, backup_request.backup_code)
        
        if success:
            # 记录日志
            totp_service._log_totp_action(current_user.id, "backup_used", True, client_ip)
            return TOTPVerifyResponse(success=True, message="备用码验证成功")
        else:
            totp_service._log_totp_action(current_user.id, "backup_used", False, client_ip, "备用码错误")
            return TOTPVerifyResponse(success=False, message="备用码错误")
        
    except Exception as e:
        logger.error(f"验证备用码失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="验证备用码失败"
        )


@router.get("/required", response_model=dict)
def check_totp_required(
    current_user: User = Depends(deps.get_current_user),
    db: Session = Depends(deps.get_db)
) -> Any:
    """
    检查用户是否需要启用TOTP
    """
    try:
        totp_service = TOTPService(db)
        is_required, in_grace_period = totp_service.check_totp_required(current_user)
        
        return {
            "required": is_required,
            "in_grace_period": in_grace_period,
            "enabled": current_user.totp_enabled or False
        }
        
    except Exception as e:
        logger.error(f"检查TOTP要求失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="检查TOTP要求失败"
        )
