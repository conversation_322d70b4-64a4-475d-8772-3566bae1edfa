/**
 * 金额转换工具
 * 用于在用户界面的"元"和后端API的"分"之间进行转换
 */

/**
 * 将元转换为分
 * @param {number|string} yuan - 元金额（支持小数）
 * @returns {number} 分金额（整数）
 */
export function yuanToFen(yuan) {
  if (yuan === null || yuan === undefined || yuan === "") {
    return null;
  }

  const yuanNumber = parseFloat(yuan);
  if (isNaN(yuanNumber)) {
    return null;
  }

  // 转换为分并四舍五入到整数
  return Math.round(yuanNumber * 100);
}

/**
 * 将分转换为元
 * @param {number} fen - 分金额（整数）
 * @returns {number} 元金额（保留两位小数）
 */
export function fenToYuan(fen) {
  if (fen === null || fen === undefined) {
    return null;
  }

  const fenNumber = parseInt(fen);
  if (isNaN(fenNumber)) {
    return null;
  }

  // 转换为元并保留两位小数
  return (fenNumber / 100).toFixed(2);
}

/**
 * 验证元金额格式
 * @param {number|string} yuan - 元金额
 * @param {number} minYuan - 最小金额（元），默认10元
 * @returns {object} 验证结果 {isValid: boolean, message: string}
 */
export function validateYuanAmount(yuan, minYuan = 10) {
  if (yuan === null || yuan === undefined || yuan === "") {
    return {
      isValid: false,
      message: "请输入金额",
    };
  }

  const yuanNumber = parseFloat(yuan);
  if (isNaN(yuanNumber)) {
    return {
      isValid: false,
      message: "金额必须是数字",
    };
  }

  if (yuanNumber < minYuan) {
    return {
      isValid: false,
      message: `金额必须大于等于${minYuan}元`,
    };
  }

  // 检查小数位数（最多2位）
  const decimalPlaces = (yuanNumber.toString().split(".")[1] || "").length;
  if (decimalPlaces > 2) {
    return {
      isValid: false,
      message: "金额最多支持2位小数",
    };
  }

  return {
    isValid: true,
    message: "",
  };
}

/**
 * 格式化显示金额（元）
 * @param {number|string} yuan - 元金额
 * @returns {string} 格式化后的金额字符串
 */
export function formatYuanDisplay(yuan) {
  if (yuan === null || yuan === undefined || yuan === "") {
    return "0.00";
  }

  const yuanNumber = parseFloat(yuan);
  if (isNaN(yuanNumber)) {
    return "0.00";
  }

  return yuanNumber.toFixed(2);
}

/**
 * 解析批量数据中的金额（统一按元格式处理）
 * @param {string} amountStr - 金额字符串（元单位）
 * @returns {object} 解析结果 {amount: number|null, isYuan: boolean}
 */
export function parseBatchAmount(amountStr) {
  if (!amountStr || amountStr.trim() === "") {
    return { amount: null, isYuan: false };
  }

  const amount = parseFloat(amountStr.trim());
  if (isNaN(amount)) {
    return { amount: null, isYuan: false };
  }

  // 统一按元格式处理，转换为分
  // 移除分格式支持，避免格式识别歧义
  return { amount: yuanToFen(amount), isYuan: true };
}
