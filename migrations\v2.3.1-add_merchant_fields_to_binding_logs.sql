-- ========================================
-- 绑卡日志表安全修复：添加商户和部门字段
-- 修复跨商户数据泄露漏洞
-- ========================================

-- 添加merchant_id和department_id字段到binding_logs表
ALTER TABLE `binding_logs` 
ADD COLUMN `merchant_id` bigint NULL COMMENT '商户ID（用于数据隔离）' AFTER `card_record_id`,
ADD COLUMN `department_id` bigint NULL COMMENT '部门ID（用于数据隔离）' AFTER `merchant_id`;

-- 添加索引
ALTER TABLE `binding_logs` 
ADD KEY `idx_binding_logs_merchant_id` (`merchant_id`),
ADD KEY `idx_binding_logs_department_id` (`department_id`),
ADD KEY `idx_binding_logs_merchant_dept` (`merchant_id`, `department_id`);

-- 添加外键约束
ALTER TABLE `binding_logs`
ADD CONSTRAINT `fk_binding_logs_merchant` FOREIGN KEY (`merchant_id`) REFERENCES `merchants` (`id`) ON DELETE CASCADE,
ADD CONSTRAINT `fk_binding_logs_department` FOREIGN KEY (`department_id`) REFERENCES `departments` (`id`) ON DELETE SET NULL;

-- 从关联的card_records表回填merchant_id和department_id数据
UPDATE `binding_logs` bl
INNER JOIN `card_records` cr ON bl.card_record_id = cr.id
SET bl.merchant_id = cr.merchant_id, bl.department_id = cr.department_id
WHERE bl.merchant_id IS NULL;

-- 验证数据完整性
SELECT 
    COUNT(*) as total_logs,
    COUNT(merchant_id) as logs_with_merchant,
    COUNT(department_id) as logs_with_department
FROM binding_logs;

-- 显示修复结果
SELECT 
    '绑卡日志表安全修复完成' as status,
    'merchant_id和department_id字段已添加' as description,
    '数据隔离机制已启用' as security_status;
