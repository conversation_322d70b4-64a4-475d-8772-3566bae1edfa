<template>
  <div class="binding-timeline">
    <div class="timeline-header">
      <h3>绑卡执行时间线</h3>
      <div class="timeline-actions">
        <el-button type="primary" size="small" @click="refreshTimeline" :loading="loading">
          <el-icon>
            <Refresh />
          </el-icon>
          刷新
        </el-button>
        <el-button type="info" size="small" @click="showPerformanceAnalysis" :loading="performanceLoading">
          <el-icon>
            <TrendCharts />
          </el-icon>
          性能分析
        </el-button>
      </div>
    </div>

    <!-- 总体统计 -->
    <div v-if="timelineData" class="timeline-summary">
      <el-row :gutter="16">
        <el-col :span="6">
          <el-statistic title="总处理时间" :value="totalDurationSeconds" suffix="秒" :precision="2" />
        </el-col>
        <el-col :span="6">
          <el-statistic title="执行步骤" :value="timelineData.steps?.length || 0" suffix="个" />
        </el-col>
        <el-col :span="6">
          <el-statistic title="成功步骤" :value="successStepsCount" suffix="个" />
        </el-col>
        <el-col :span="6">
          <el-statistic title="失败步骤" :value="failedStepsCount" suffix="个" />
        </el-col>
      </el-row>
    </div>

    <!-- 加载状态 -->
    <div v-if="loading" class="loading-container">
      <el-skeleton :rows="5" animated />
    </div>

    <!-- 空状态 -->
    <div v-else-if="!timelineData || !timelineData.steps?.length" class="empty-container">
      <el-empty description="暂无时间线数据" />
    </div>

    <!-- 时间线内容 -->
    <div v-else class="timeline-container">
      <el-timeline>
        <el-timeline-item v-for="(step, index) in timelineData.steps" :key="step.step_name"
          :timestamp="formatTimestamp(step.start_time)" :type="getStepType(step.status)"
          :icon="getStepIcon(step.status)" :color="getStepColor(step.status)"
          :size="step.status === 'failed' ? 'large' : 'normal'">
          <el-card class="timeline-step-card" :class="{ 'is-failed': step.status === 'failed' }">
            <template #header>
              <div class="step-header">
                <div class="step-title">
                  <span class="step-name">{{ step.step_name }}</span>
                  <el-tag :type="getStepTagType(step.status)" size="small" class="step-status-tag">
                    {{ getStepStatusText(step.status) }}
                  </el-tag>
                </div>
                <div class="step-timing">
                  <el-tag size="small" type="info">
                    耗时: {{ step.duration_ms ? (step.duration_ms / 1000).toFixed(2) : '0.00' }}秒
                  </el-tag>
                </div>
              </div>
            </template>

            <div class="step-content">
              <!-- 时间信息 -->
              <div class="time-info">
                <div class="time-item">
                  <span class="time-label">开始时间:</span>
                  <span class="time-value">{{ formatDateTime(step.start_time) }}</span>
                </div>
                <div class="time-item" v-if="step.end_time">
                  <span class="time-label">结束时间:</span>
                  <span class="time-value">{{ formatDateTime(step.end_time) }}</span>
                </div>
              </div>

              <!-- 请求数据 -->
              <div v-if="step.request_data" class="data-section">
                <div class="section-header" @click="toggleSection('request_' + index)">
                  <el-icon>
                    <Document />
                  </el-icon>
                  <span>请求数据</span>
                  <el-icon class="toggle-icon" :class="{ 'is-active': expandedSections['request_' + index] }">
                    <ArrowDown />
                  </el-icon>
                </div>
                <div v-show="expandedSections['request_' + index]" class="section-content">
                  <pre class="json-content">{{ formatJson(step.request_data) }}</pre>
                </div>
              </div>

              <!-- 响应数据 -->
              <div v-if="step.response_data" class="data-section">
                <div class="section-header" @click="toggleSection('response_' + index)">
                  <el-icon>
                    <Document />
                  </el-icon>
                  <span>响应数据</span>
                  <el-icon class="toggle-icon" :class="{ 'is-active': expandedSections['response_' + index] }">
                    <ArrowDown />
                  </el-icon>
                </div>
                <div v-show="expandedSections['response_' + index]" class="section-content">
                  <pre class="json-content">{{ formatJson(step.response_data) }}</pre>
                </div>
              </div>

              <!-- 错误信息 -->
              <div v-if="step.error_message" class="error-section">
                <el-alert :title="step.error_message" type="error" :closable="false" show-icon />
              </div>
            </div>
          </el-card>
        </el-timeline-item>
      </el-timeline>
    </div>

    <!-- 性能分析对话框 -->
    <el-dialog v-model="performanceDialogVisible" title="绑卡性能分析" width="800px" :close-on-click-modal="false">
      <div v-if="performanceData" class="performance-analysis">
        <!-- 性能概览 -->
        <div class="performance-overview">
          <h4>性能概览</h4>
          <el-row :gutter="16">
            <el-col :span="8">
              <el-statistic title="总耗时"
                :value="performanceData.timeline?.total_duration_ms ? (performanceData.timeline.total_duration_ms / 1000) : 0"
                suffix="秒" :precision="2" />
            </el-col>
            <el-col :span="8">
              <el-statistic title="最慢步骤"
                :value="performanceData.slowest_step?.duration_ms ? (performanceData.slowest_step.duration_ms / 1000) : 0"
                suffix="秒" :precision="2" />
            </el-col>
            <el-col :span="8">
              <el-statistic title="平均步骤耗时" :value="performanceData.average_step_duration" suffix="秒" :precision="2" />
            </el-col>
          </el-row>
        </div>

        <!-- 瓶颈分析 -->
        <div v-if="performanceData.bottlenecks?.length" class="bottleneck-analysis">
          <h4>性能瓶颈</h4>
          <el-table :data="performanceData.bottlenecks" style="width: 100%">
            <el-table-column prop="step_name" label="步骤名称" />
            <el-table-column prop="duration_ms" label="耗时(秒)" :formatter="formatDurationMs" />
            <el-table-column prop="severity" label="严重程度" />
          </el-table>
        </div>

        <!-- 建议 -->
        <div v-if="performanceData.recommendations?.length" class="recommendations">
          <h4>优化建议</h4>
          <ul>
            <li v-for="recommendation in performanceData.recommendations" :key="recommendation">
              {{ recommendation }}
            </li>
          </ul>
        </div>
      </div>

      <template #footer>
        <el-button @click="performanceDialogVisible = false">关闭</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, defineProps, defineEmits, watch } from 'vue'
import { ElMessage } from 'element-plus'
import {
  Refresh,
  TrendCharts,
  Document,
  ArrowDown,
  SuccessFilled,
  WarningFilled,
  CircleCloseFilled,
  Clock
} from '@element-plus/icons-vue'
import { bindingLogsApi } from '@/api/modules/bindingLogs'
import { formatDateTime } from '@/utils/dateUtils'

// Props
const props = defineProps({
  cardRecordId: {
    type: [String, Number],
    required: true
  }
})

// Emits
const emits = defineEmits(['timeline-loaded'])

// 响应式数据
const loading = ref(false)
const performanceLoading = ref(false)
const timelineData = ref(null)
const performanceData = ref(null)
const performanceDialogVisible = ref(false)
const expandedSections = ref({})

// 计算属性
const totalDurationSeconds = computed(() => {
  // 将毫秒转换为秒
  return timelineData.value?.total_duration_ms ? (timelineData.value.total_duration_ms / 1000) : 0
})

const successStepsCount = computed(() => {
  return timelineData.value?.steps?.filter(step => step.status === 'success').length || 0
})

const failedStepsCount = computed(() => {
  return timelineData.value?.steps?.filter(step => step.status === 'failed').length || 0
})

// 方法
const refreshTimeline = async () => {
  if (!props.cardRecordId) return

  loading.value = true
  try {
    timelineData.value = await bindingLogsApi.getTimeline(props.cardRecordId)
    console.log('Timeline API返回数据:', timelineData.value)
    console.log('Total duration (ms):', timelineData.value?.total_duration_ms)
    console.log('Total duration (seconds):', timelineData.value?.total_duration_ms ? (timelineData.value.total_duration_ms / 1000) : 0)
    emits('timeline-loaded', timelineData.value)
  } catch (error) {
    console.error('获取时间线数据失败:', error)
    ElMessage.error('获取时间线数据失败')
  } finally {
    loading.value = false
  }
}

const showPerformanceAnalysis = async () => {
  if (!props.cardRecordId) return

  performanceLoading.value = true
  try {
    performanceData.value = await bindingLogsApi.getPerformanceAnalysis(props.cardRecordId)
    console.log('Performance API返回数据:', performanceData.value)
    performanceDialogVisible.value = true
  } catch (error) {
    console.error('获取性能分析数据失败:', error)
    ElMessage.error('获取性能分析数据失败')
  } finally {
    performanceLoading.value = false
  }
}

const toggleSection = (sectionKey) => {
  expandedSections.value[sectionKey] = !expandedSections.value[sectionKey]
}

const formatTimestamp = (timestamp) => {
  if (!timestamp) return ''
  return new Date(timestamp).toLocaleTimeString('zh-CN', {
    hour12: false,
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  })
}

const formatJson = (data) => {
  if (!data) return ''
  try {
    return JSON.stringify(typeof data === 'string' ? JSON.parse(data) : data, null, 2)
  } catch {
    return data
  }
}

const formatDuration = (row, column, cellValue) => {
  return cellValue?.toFixed(2) || '0.00'
}

const formatDurationMs = (row, column, cellValue) => {
  return cellValue ? (cellValue / 1000).toFixed(2) : '0.00'
}

const formatPercentage = (row, column, cellValue) => {
  return `${(cellValue * 100).toFixed(1)}%`
}

const getStepType = (status) => {
  const typeMap = {
    'success': 'success',
    'failed': 'danger',
    'running': 'warning',
    'pending': 'info'
  }
  return typeMap[status] || 'info'
}

const getStepIcon = (status) => {
  const iconMap = {
    'success': SuccessFilled,
    'failed': CircleCloseFilled,
    'running': Clock,
    'pending': Clock
  }
  return iconMap[status] || Clock
}

const getStepColor = (status) => {
  const colorMap = {
    'success': '#67c23a',
    'failed': '#f56c6c',
    'running': '#e6a23c',
    'pending': '#909399'
  }
  return colorMap[status] || '#909399'
}

const getStepTagType = (status) => {
  const tagTypeMap = {
    'success': 'success',
    'failed': 'danger',
    'running': 'warning',
    'pending': 'info'
  }
  return tagTypeMap[status] || 'info'
}

const getStepStatusText = (status) => {
  const statusTextMap = {
    'success': '成功',
    'failed': '失败',
    'running': '执行中',
    'pending': '等待中'
  }
  return statusTextMap[status] || '未知'
}

// 监听 cardRecordId 变化，自动刷新数据
watch(() => props.cardRecordId, (newCardRecordId, oldCardRecordId) => {
  if (newCardRecordId && newCardRecordId !== oldCardRecordId) {
    refreshTimeline()
  }
}, { immediate: false })

// 暴露方法给父组件调用
defineExpose({
  refreshTimeline
})

// 生命周期
onMounted(() => {
  refreshTimeline()
})
</script>

<style scoped>
.binding-timeline {
  padding: 16px;
}

.timeline-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 12px;
  border-bottom: 1px solid #ebeef5;
}

.timeline-header h3 {
  margin: 0;
  color: #303133;
  font-size: 18px;
  font-weight: 600;
}

.timeline-actions {
  display: flex;
  gap: 8px;
}

.timeline-summary {
  background: #f8f9fa;
  padding: 20px;
  border-radius: 8px;
  margin-bottom: 20px;
}

.loading-container,
.empty-container {
  padding: 40px 20px;
  text-align: center;
}

.timeline-container {
  margin-top: 20px;
}

.timeline-step-card {
  margin-bottom: 16px;
  border-radius: 8px;
  transition: all 0.3s ease;
}

.timeline-step-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.timeline-step-card.is-failed {
  border-left: 4px solid #f56c6c;
}

.step-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.step-title {
  display: flex;
  align-items: center;
  gap: 12px;
}

.step-name {
  font-weight: 600;
  color: #303133;
  font-size: 16px;
}

.step-status-tag {
  margin-left: 8px;
}

.step-timing {
  display: flex;
  align-items: center;
  gap: 8px;
}

.step-content {
  margin-top: 16px;
}

.time-info {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
  margin-bottom: 16px;
  padding: 12px;
  background: #fafafa;
  border-radius: 6px;
}

.time-item {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.time-label {
  font-size: 12px;
  color: #909399;
  font-weight: 500;
}

.time-value {
  font-size: 14px;
  color: #303133;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
}

.data-section {
  margin-bottom: 12px;
}

.section-header {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  background: #f5f7fa;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.2s;
  user-select: none;
}

.section-header:hover {
  background: #e4e7ed;
}

.toggle-icon {
  margin-left: auto;
  transition: transform 0.3s ease;
}

.toggle-icon.is-active {
  transform: rotate(180deg);
}

.section-content {
  margin-top: 8px;
  padding: 12px;
  background: #fafafa;
  border-radius: 4px;
  border: 1px solid #e4e7ed;
}

.json-content {
  margin: 0;
  padding: 0;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 12px;
  line-height: 1.5;
  color: #2c3e50;
  white-space: pre-wrap;
  word-break: break-all;
  max-height: 300px;
  overflow-y: auto;
}

.error-section {
  margin-top: 12px;
}

.performance-analysis {
  padding: 16px 0;
}

.performance-overview {
  margin-bottom: 24px;
}

.performance-overview h4 {
  margin: 0 0 16px 0;
  color: #303133;
  font-size: 16px;
  font-weight: 600;
}

.bottleneck-analysis {
  margin-bottom: 24px;
}

.bottleneck-analysis h4 {
  margin: 0 0 16px 0;
  color: #303133;
  font-size: 16px;
  font-weight: 600;
}

.recommendations h4 {
  margin: 0 0 16px 0;
  color: #303133;
  font-size: 16px;
  font-weight: 600;
}

.recommendations ul {
  margin: 0;
  padding-left: 20px;
}

.recommendations li {
  margin-bottom: 8px;
  color: #606266;
  line-height: 1.6;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .timeline-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }

  .time-info {
    grid-template-columns: 1fr;
  }

  .step-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
}
</style>
