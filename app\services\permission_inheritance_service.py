"""
权限继承显示服务
显示角色通过不同途径获得的权限，包括直接权限、继承权限等
"""

from typing import List, Dict, Any, Optional, Set
from sqlalchemy.orm import Session, selectinload
from sqlalchemy import text

from app.models.user import User
from app.models.role import Role
from app.models.permission import Permission
from app.core.logging import get_logger


class PermissionInheritanceService:
    """权限继承显示服务"""

    def __init__(self, db: Session):
        self.db = db
        self.logger = get_logger("permission_inheritance")

    def get_user_permission_inheritance(self, user: User) -> Dict[str, Any]:
        """
        获取用户的权限继承信息
        
        Returns:
            Dict包含:
            - direct_permissions: 用户直接拥有的权限
            - role_permissions: 通过角色获得的权限
            - inherited_permissions: 继承的权限
            - all_permissions: 所有权限的汇总
            - permission_sources: 每个权限的来源信息
        """
        try:
            result = {
                "user_id": user.id,
                "username": user.username,
                "is_superuser": user.is_superuser,
                "direct_permissions": [],
                "role_permissions": {},
                "inherited_permissions": [],
                "all_permissions": [],
                "permission_sources": {}
            }

            # 超级管理员拥有所有权限
            if user.is_superuser:
                all_permissions = self.db.query(Permission).filter(
                    Permission.is_enabled == True
                ).all()
                
                result["all_permissions"] = [p.code for p in all_permissions]
                result["permission_sources"] = {
                    p.code: {
                        "sources": ["superuser"],
                        "description": "超级管理员拥有所有权限"
                    }
                    for p in all_permissions
                }
                return result

            # 获取用户直接权限
            direct_permissions = self._get_user_direct_permissions(user.id)
            result["direct_permissions"] = [p.code for p in direct_permissions]

            # 获取用户角色权限
            role_permissions = self._get_user_role_permissions(user.id)
            result["role_permissions"] = role_permissions

            # 构建权限来源信息
            all_permission_codes = set()
            permission_sources = {}

            # 处理直接权限
            for perm in direct_permissions:
                all_permission_codes.add(perm.code)
                permission_sources[perm.code] = {
                    "sources": ["direct"],
                    "description": "用户直接拥有的权限",
                    "permission_name": perm.name
                }

            # 处理角色权限
            for role_code, permissions in role_permissions.items():
                for perm_code in permissions:
                    all_permission_codes.add(perm_code)
                    if perm_code not in permission_sources:
                        permission_sources[perm_code] = {
                            "sources": [],
                            "roles": [],
                            "description": "通过角色获得的权限"
                        }
                    
                    if "role" not in permission_sources[perm_code]["sources"]:
                        permission_sources[perm_code]["sources"].append("role")
                    
                    if "roles" not in permission_sources[perm_code]:
                        permission_sources[perm_code]["roles"] = []
                    permission_sources[perm_code]["roles"].append(role_code)

            result["all_permissions"] = list(all_permission_codes)
            result["permission_sources"] = permission_sources

            return result

        except Exception as e:
            self.logger.error(f"获取用户权限继承信息失败: {e}")
            raise

    def get_role_permission_inheritance(self, role_id: int) -> Dict[str, Any]:
        """
        获取角色的权限继承信息
        
        Returns:
            Dict包含:
            - role_info: 角色基本信息
            - direct_permissions: 角色直接拥有的权限
            - inherited_permissions: 继承的权限（如果有父角色）
            - all_permissions: 所有权限的汇总
            - permission_details: 权限详细信息
        """
        try:
            # 获取角色信息
            role = self.db.query(Role).options(
                selectinload(Role.permissions)
            ).filter(Role.id == role_id).first()

            if not role:
                raise ValueError(f"角色 {role_id} 不存在")

            result = {
                "role_id": role.id,
                "role_name": role.name,
                "role_code": role.code,
                "direct_permissions": [],
                "inherited_permissions": [],
                "all_permissions": [],
                "permission_details": {}
            }

            # 获取角色直接权限
            direct_permissions = list(role.permissions)
            result["direct_permissions"] = [p.code for p in direct_permissions]

            # 构建权限详细信息
            permission_details = {}
            for perm in direct_permissions:
                permission_details[perm.code] = {
                    "id": perm.id,
                    "name": perm.name,
                    "description": perm.description,
                    "resource_type": perm.resource_type,
                    "resource_path": perm.resource_path,
                    "is_enabled": perm.is_enabled,
                    "source": "direct"
                }

            result["all_permissions"] = result["direct_permissions"]
            result["permission_details"] = permission_details

            return result

        except Exception as e:
            self.logger.error(f"获取角色权限继承信息失败: {e}")
            raise

    def get_permission_usage_info(self, permission_code: str) -> Dict[str, Any]:
        """
        获取权限的使用信息
        
        Returns:
            Dict包含:
            - permission_info: 权限基本信息
            - roles_with_permission: 拥有该权限的角色列表
            - users_with_permission: 拥有该权限的用户列表（直接或通过角色）
            - usage_statistics: 使用统计信息
        """
        try:
            # 获取权限信息
            permission = self.db.query(Permission).filter(
                Permission.code == permission_code
            ).first()

            if not permission:
                raise ValueError(f"权限 {permission_code} 不存在")

            result = {
                "permission_info": {
                    "id": permission.id,
                    "code": permission.code,
                    "name": permission.name,
                    "description": permission.description,
                    "resource_type": permission.resource_type,
                    "resource_path": permission.resource_path,
                    "is_enabled": permission.is_enabled
                },
                "roles_with_permission": [],
                "users_with_permission": [],
                "usage_statistics": {}
            }

            # 获取拥有该权限的角色
            roles_with_permission = self._get_roles_with_permission(permission.id)
            result["roles_with_permission"] = [
                {
                    "id": role.id,
                    "name": role.name,
                    "code": role.code,
                    "is_enabled": role.is_enabled
                }
                for role in roles_with_permission
            ]

            # 获取拥有该权限的用户（直接或通过角色）
            users_with_permission = self._get_users_with_permission(permission.id)
            result["users_with_permission"] = [
                {
                    "id": user.id,
                    "username": user.username,
                    "email": user.email,
                    "is_active": user.is_active,
                    "source": user_info["source"]
                }
                for user, user_info in users_with_permission
            ]

            # 统计信息
            result["usage_statistics"] = {
                "total_roles": len(result["roles_with_permission"]),
                "total_users": len(result["users_with_permission"]),
                "active_users": len([u for u in result["users_with_permission"] if u["is_active"]])
            }

            return result

        except Exception as e:
            self.logger.error(f"获取权限使用信息失败: {e}")
            raise

    def _get_user_direct_permissions(self, user_id: int) -> List[Permission]:
        """获取用户直接权限"""
        sql = text("""
            SELECT p.*
            FROM permissions p
            INNER JOIN user_permissions up ON p.id = up.permission_id
            WHERE up.user_id = :user_id AND p.is_enabled = 1
        """)
        
        result = self.db.execute(sql, {"user_id": user_id})
        permission_ids = [row[0] for row in result]
        
        if not permission_ids:
            return []
        
        return self.db.query(Permission).filter(
            Permission.id.in_(permission_ids)
        ).all()

    def _get_user_role_permissions(self, user_id: int) -> Dict[str, List[str]]:
        """获取用户通过角色获得的权限"""
        sql = text("""
            SELECT r.code as role_code, r.name as role_name, p.code as permission_code
            FROM permissions p
            INNER JOIN role_permissions rp ON p.id = rp.permission_id
            INNER JOIN roles r ON rp.role_id = r.id
            INNER JOIN user_roles ur ON r.id = ur.role_id
            WHERE ur.user_id = :user_id AND p.is_enabled = 1 AND r.is_enabled = 1
            ORDER BY r.code, p.code
        """)
        
        result = self.db.execute(sql, {"user_id": user_id})
        
        role_permissions = {}
        for row in result:
            role_code = row.role_code
            permission_code = row.permission_code
            
            if role_code not in role_permissions:
                role_permissions[role_code] = []
            role_permissions[role_code].append(permission_code)
        
        return role_permissions

    def _get_roles_with_permission(self, permission_id: int) -> List[Role]:
        """获取拥有指定权限的角色列表"""
        sql = text("""
            SELECT r.*
            FROM roles r
            INNER JOIN role_permissions rp ON r.id = rp.role_id
            WHERE rp.permission_id = :permission_id AND r.is_enabled = 1
        """)
        
        result = self.db.execute(sql, {"permission_id": permission_id})
        role_ids = [row[0] for row in result]
        
        if not role_ids:
            return []
        
        return self.db.query(Role).filter(Role.id.in_(role_ids)).all()

    def _get_users_with_permission(self, permission_id: int) -> List[tuple]:
        """获取拥有指定权限的用户列表（包括来源信息）"""
        # 直接权限的用户
        direct_sql = text("""
            SELECT u.*, 'direct' as source
            FROM users u
            INNER JOIN user_permissions up ON u.id = up.user_id
            WHERE up.permission_id = :permission_id AND u.is_active = 1
        """)
        
        # 通过角色获得权限的用户
        role_sql = text("""
            SELECT DISTINCT u.*, 'role' as source
            FROM users u
            INNER JOIN user_roles ur ON u.id = ur.user_id
            INNER JOIN role_permissions rp ON ur.role_id = rp.role_id
            WHERE rp.permission_id = :permission_id AND u.is_active = 1
        """)
        
        users_with_permission = []
        
        # 获取直接权限用户
        direct_result = self.db.execute(direct_sql, {"permission_id": permission_id})
        for row in direct_result:
            user = self.db.query(User).filter(User.id == row[0]).first()
            if user:
                users_with_permission.append((user, {"source": "direct"}))
        
        # 获取角色权限用户
        role_result = self.db.execute(role_sql, {"permission_id": permission_id})
        existing_user_ids = {user.id for user, _ in users_with_permission}
        
        for row in role_result:
            user_id = row[0]
            if user_id not in existing_user_ids:
                user = self.db.query(User).filter(User.id == user_id).first()
                if user:
                    users_with_permission.append((user, {"source": "role"}))
                    existing_user_ids.add(user_id)
        
        return users_with_permission
