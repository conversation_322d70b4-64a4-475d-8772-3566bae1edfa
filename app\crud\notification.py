"""
通知管理CRUD操作
"""
from typing import List, Optional, Dict, Any, Tuple
from sqlalchemy.orm import Session
from sqlalchemy import desc, and_, func
from datetime import datetime, date

from app.crud.base import CRUDBase
from app.models.notification import Notification, NotificationType, NotificationStatus
from app.schemas.notification import NotificationCreate, NotificationUpdate
from app.core.query_filters import StandardQueryFilter


class CRUDNotification(CRUDBase[Notification, NotificationCreate, NotificationUpdate]):
    """通知CRUD操作类"""

    def create_notification(
        self,
        db: Session,
        *,
        user_id: int,
        title: str,
        content: str,
        notification_type: NotificationType = NotificationType.SYSTEM,
        extra_data: Optional[Dict[str, Any]] = None,
    ) -> Notification:
        """创建通知"""
        notification_data = {
            "user_id": user_id,
            "title": title,
            "content": content,
            "type": notification_type,
            "status": NotificationStatus.UNREAD,
            "extra_data": extra_data,
        }
        
        db_obj = self.model(**notification_data)
        db.add(db_obj)
        db.commit()
        db.refresh(db_obj)
        return db_obj

    def get_by_user(
        self,
        db: Session,
        user_id: int,
        *,
        skip: int = 0,
        limit: int = 100,
        status: Optional[NotificationStatus] = None,
        notification_type: Optional[NotificationType] = None,
    ) -> List[Notification]:
        """获取用户的通知列表"""
        query = db.query(self.model).filter(self.model.user_id == user_id)
        
        if status:
            query = query.filter(self.model.status == status)
        if notification_type:
            query = query.filter(self.model.type == notification_type)
        
        return query.order_by(desc(self.model.created_at)).offset(skip).limit(limit).all()

    def get_unread_count(self, db: Session, user_id: int) -> int:
        """获取用户未读通知数量"""
        return db.query(self.model).filter(
            and_(
                self.model.user_id == user_id,
                self.model.status == NotificationStatus.UNREAD,
            )
        ).count()

    def mark_as_read(self, db: Session, notification_id: int, user_id: int) -> Optional[Notification]:
        """标记通知为已读"""
        notification = db.query(self.model).filter(
            and_(
                self.model.id == notification_id,
                self.model.user_id == user_id,
            )
        ).first()
        
        if notification:
            notification.status = NotificationStatus.READ
            notification.read_at = datetime.now()
            db.commit()
            db.refresh(notification)
            return notification
        return None

    def mark_all_as_read(self, db: Session, user_id: int) -> int:
        """标记用户所有通知为已读"""
        count = db.query(self.model).filter(
            and_(
                self.model.user_id == user_id,
                self.model.status == NotificationStatus.UNREAD,
            )
        ).update({
            "status": NotificationStatus.READ,
            "read_at": datetime.now(),
        })
        
        db.commit()
        return count

    def delete_notification(self, db: Session, notification_id: int, user_id: int) -> bool:
        """删除通知"""
        notification = db.query(self.model).filter(
            and_(
                self.model.id == notification_id,
                self.model.user_id == user_id,
            )
        ).first()
        
        if notification:
            db.delete(notification)
            db.commit()
            return True
        return False

    def get_multi_with_filters_and_count(
        self,
        db: Session,
        *,
        skip: int = 0,
        limit: int = 100,
        filters: Optional[Dict[str, Any]] = None,
    ) -> Tuple[List[Notification], int]:
        """根据过滤条件获取通知列表和总数"""
        query = db.query(self.model)

        # 使用通用过滤器处理基本过滤条件
        basic_filters = self._extract_basic_filters(filters)
        filter_handler = StandardQueryFilter(self.model)
        query = filter_handler.apply_filters(query, basic_filters)

        # 处理特殊过滤条件
        query = self._apply_special_filters(query, filters)

        # 获取总数
        total = query.count()

        # 获取分页数据
        notifications = query.order_by(desc(self.model.created_at)).offset(skip).limit(limit).all()

        return notifications, total

    def _extract_basic_filters(self, filters: Optional[Dict[str, Any]]) -> Dict[str, Any]:
        """提取基本过滤条件"""
        if not filters:
            return {}

        basic_filter_keys = ['user_id', 'status', 'type']
        return {key: value for key, value in filters.items() if key in basic_filter_keys}

    def _apply_special_filters(self, query, filters: Optional[Dict[str, Any]]):
        """应用特殊过滤条件"""
        if not filters:
            return query

        # 标题和内容模糊查询
        query = self._apply_text_filters(query, filters)

        # 时间范围过滤
        query = self._apply_date_filters(query, filters)

        return query

    def _apply_text_filters(self, query, filters: Dict[str, Any]):
        """应用文本过滤条件"""
        if "title" in filters and filters["title"]:
            query = query.filter(self.model.title.like(f"%{filters['title']}%"))
        if "content" in filters and filters["content"]:
            query = query.filter(self.model.content.like(f"%{filters['content']}%"))
        return query

    def _apply_date_filters(self, query, filters: Dict[str, Any]):
        """应用日期过滤条件"""
        if "start_date" in filters and filters["start_date"]:
            query = query.filter(func.date(self.model.created_at) >= filters["start_date"])
        if "end_date" in filters and filters["end_date"]:
            query = query.filter(func.date(self.model.created_at) <= filters["end_date"])
        return query

    def cleanup_old_notifications(self, db: Session, days: int = 30) -> int:
        """清理旧的已读通知"""
        from datetime import timedelta
        
        cutoff_date = datetime.now() - timedelta(days=days)
        
        count = db.query(self.model).filter(
            and_(
                self.model.status == NotificationStatus.READ,
                self.model.read_at < cutoff_date,
            )
        ).count()
        
        db.query(self.model).filter(
            and_(
                self.model.status == NotificationStatus.READ,
                self.model.read_at < cutoff_date,
            )
        ).delete()
        
        db.commit()
        return count

    def broadcast_notification(
        self,
        db: Session,
        *,
        user_ids: List[int],
        title: str,
        content: str,
        notification_type: NotificationType = NotificationType.SYSTEM,
        extra_data: Optional[Dict[str, Any]] = None,
    ) -> List[Notification]:
        """广播通知给多个用户 - 优化版本，使用批量插入避免N+1查询"""
        if not user_ids:
            return []

        # 批量创建通知对象
        notifications = []
        current_time = datetime.now()

        for user_id in user_ids:
            notification_data = {
                "user_id": user_id,
                "title": title,
                "content": content,
                "type": notification_type,
                "status": NotificationStatus.UNREAD,
                "extra_data": extra_data,
                "created_at": current_time,
                "updated_at": current_time,
            }
            notification = self.model(**notification_data)
            notifications.append(notification)

        # 批量插入到数据库
        db.add_all(notifications)
        db.commit()

        # 刷新所有对象以获取ID
        for notification in notifications:
            db.refresh(notification)

        return notifications

    def get_statistics(
        self,
        db: Session,
        *,
        start_date: Optional[date] = None,
        end_date: Optional[date] = None,
        user_id: Optional[int] = None,
    ) -> Dict[str, Any]:
        """获取通知统计信息 - 优化版本，使用单次聚合查询避免N+1查询"""
        # 构建基础查询
        base_query = db.query(self.model)

        if user_id:
            base_query = base_query.filter(self.model.user_id == user_id)
        if start_date:
            base_query = base_query.filter(func.date(self.model.created_at) >= start_date)
        if end_date:
            base_query = base_query.filter(func.date(self.model.created_at) <= end_date)

        # 使用单次聚合查询获取所有统计信息
        stats_query = base_query.with_entities(
            func.count(self.model.id).label('total_count'),
            func.sum(func.case((self.model.status == NotificationStatus.UNREAD, 1), else_=0)).label('unread_count'),
            func.sum(func.case((self.model.status == NotificationStatus.READ, 1), else_=0)).label('read_count'),
            # 按类型统计
            func.sum(func.case((self.model.type == NotificationType.SYSTEM, 1), else_=0)).label('system_count'),
            func.sum(func.case((self.model.type == NotificationType.BINDING_SUCCESS, 1), else_=0)).label('binding_success_count'),
            func.sum(func.case((self.model.type == NotificationType.BINDING_FAILED, 1), else_=0)).label('binding_failed_count'),
            func.sum(func.case((self.model.type == NotificationType.MERCHANT_NOTICE, 1), else_=0)).label('merchant_notice_count'),
        ).first()

        # 构建类型统计字典
        type_stats = {
            NotificationType.SYSTEM.value: stats_query.system_count or 0,
            NotificationType.BINDING_SUCCESS.value: stats_query.binding_success_count or 0,
            NotificationType.BINDING_FAILED.value: stats_query.binding_failed_count or 0,
            NotificationType.MERCHANT_NOTICE.value: stats_query.merchant_notice_count or 0,
        }

        return {
            "total_count": stats_query.total_count or 0,
            "unread_count": stats_query.unread_count or 0,
            "read_count": stats_query.read_count or 0,
            "type_stats": type_stats,
            "start_date": start_date,
            "end_date": end_date,
        }


# 创建全局实例
notification = CRUDNotification(Notification)
