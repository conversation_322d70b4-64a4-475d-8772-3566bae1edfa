import { http } from '@/api/request'
import { API_URLS } from './config'

const { AUTH } = API_URLS

export const authApi = {
    /**
     * 检查用户TOTP状态（登录前预检查）
     */
    async checkTotpStatus(username) {
        try {
            const response = await http.post(AUTH.CHECK_TOTP, { username })
            return response.data || response
        } catch (error) {
            console.error('检查TOTP状态失败:', error)
            // 出错时返回安全的默认值
            return {
                totp_enabled: false,
                totp_required: false
            }
        }
    },

    /**
     * 用户登录
     */
    async login(data) {
        const formData = new URLSearchParams()
        formData.append('username', data.username)
        formData.append('password', data.password)

        const headers = {
            'Content-Type': 'application/x-www-form-urlencoded'
        }

        // 如果提供了TOTP验证码，添加到请求头
        if (data.totpCode) {
            headers['X-TOTP-Code'] = data.totpCode
        }

        try {
            const response = await http.post(AUTH.LOGIN, formData, { headers })
            // 后端返回格式：{code: 0, data: {access_token: "...", token_type: "bearer"}, message: "..."}
            // 前端需要的是data字段的内容
            return response.data || response;
        } catch (error) {
            // 检查是否是需要TOTP的错误
            if (error.response?.status === 400 && error.response?.data?.detail?.includes('双因子认证码')) {
                throw new Error('totp_required')
            }
            throw new Error('登录失败：' + error.message)
        }
    },

    /**
     * 用户登出
     */
    logout() {
        return http.post(AUTH.LOGOUT)
    },

    /**
     * 获取当前用户信息
     */
    getUserInfo() {
        return http.get(AUTH.USER_INFO).then(res => {
            // 统一处理响应格式
            return res.data || res
        })
    },

    /**
     * 获取用户菜单
     */
    getUserMenus() {
        return http.get('/menus/user-menus').then(res => {
            return res.data || res
        })
    },

    /**
     * 获取用户权限列表
     */
    getUserPermissions() {
        return http.get(AUTH.PERMISSIONS).then(res => {
            return res.data || res
        })
    },

    /**
     * 刷新token
     */
    refreshToken() {
        return http.post(AUTH.REFRESH_TOKEN)
    }
}
