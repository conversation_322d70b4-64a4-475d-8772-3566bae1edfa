import { defineStore } from "pinia";
import { walmartCKApi } from "@/api/modules/walmartCK";

export const usewalmartCKStore = defineStore("walmartCK", {
  state: () => ({
    configList: [],
    currentConfig: null,
    loading: false,
    error: null,
    pagination: { total: 0 },
  }),

  getters: {
    getConfigList: (state) => state.configList,
    getCurrentConfig: (state) => state.currentConfig,
    isLoading: (state) => state.loading,
  },

  actions: {
    // 转换后端数据格式为前端格式
    transformDataFormat(item) {
      if (!item) return item;

      return {
        ...item,
        // 转换下划线格式为驼峰格式
        totalLimit: item.total_limit,
        bindCount: item.bind_count,
        lastBindTime: item.last_bind_time,
        createdBy: item.created_by,
        merchantId: item.merchant_id,
        departmentId: item.department_id,
        createdAt: item.created_at,
        updatedAt: item.updated_at,
        createTime: item.created_at,
        updateTime: item.updated_at,
        // 新增字段：商户和部门名称
        merchantName: item.merchant_name,
        departmentName: item.department_name,
        // 新增字段：创建者信息
        creatorUsername: item.creator_username,
        creatorName: item.creator_name,
        // 新增真实金额字段
        actualAmount: (item.actual_amount / 100).toFixed(2),
      };
    },

    // 获取用户配置列表（现在包含统计数据）
    async fetchConfigList(params) {
      this.loading = true;
      try {
        const data = await walmartCKApi.getList(params);
        // 转换数据格式
        const transformedItems = (data.items || []).map((item) =>
          this.transformDataFormat(item)
        );
        this.configList = transformedItems;
        this.pagination = { total: data.total || 0 };

        // 返回完整数据，包括统计信息
        return {
          ...data,
          items: transformedItems,
          // 保持统计数据原样返回，供页面组件使用
          statistics: data.statistics,
        };
      } catch (error) {
        this.error = error.message;
        return null;
      } finally {
        this.loading = false;
      }
    },

    // 获取用户配置详情
    async fetchConfigDetail(id) {
      this.loading = true;
      try {
        const data = await walmartCKApi.getDetail(id);
        const transformedData = this.transformDataFormat(data);
        if (id === this.currentConfig?.id) {
          this.currentConfig = transformedData;
        }
        return transformedData;
      } catch (error) {
        this.error = error.message;
        return null;
      } finally {
        this.loading = false;
      }
    },

    // 创建用户配置
    async createConfig(configData) {
      this.loading = true;
      try {
        const data = await walmartCKApi.create(configData);
        const transformedData = this.transformDataFormat(data);
        this.configList = [...this.configList, transformedData];
        return transformedData;
      } catch (error) {
        this.error = error.message;
        return null;
      } finally {
        this.loading = false;
      }
    },

    // 批量创建用户配置
    async batchCreateConfig(batchData) {
      this.loading = true;
      try {
        const response = await walmartCKApi.batchCreate(batchData);

        // 处理批量创建结果
        if (response && response.data) {
          const result = response.data;

          // 转换成功创建的数据格式
          const transformedItems = result.success_items.map((item) =>
            this.transformDataFormat(item)
          );

          // 更新配置列表
          this.configList = [...this.configList, ...transformedItems];

          return {
            success: true,
            data: result,
            message: response.message || `成功创建${result.success_count}个CK`,
          };
        } else {
          throw new Error("批量创建响应格式错误");
        }
      } catch (error) {
        this.error = error.message;
        return {
          success: false,
          message: error.message || "批量创建失败",
        };
      } finally {
        this.loading = false;
      }
    },

    // 更新用户配置
    async updateConfig(id, configData) {
      this.loading = true;
      try {
        const data = await walmartCKApi.update(id, configData);
        const transformedData = this.transformDataFormat(data);
        if (id === this.currentConfig?.id) {
          this.currentConfig = transformedData;
        }
        this.configList = this.configList.map((item) =>
          item.id === id ? transformedData : item
        );
        return transformedData;
      } catch (error) {
        this.error = error.message;
        return null;
      } finally {
        this.loading = false;
      }
    },

    // 删除用户配置
    async deleteConfig(id) {
      this.loading = true;
      try {
        await walmartCKApi.delete(id);
        if (id === this.currentConfig?.id) {
          this.currentConfig = null;
        }
        this.configList = this.configList.filter((item) => item.id !== id);
        return true;
      } catch (error) {
        this.error = error.message;
        return false;
      } finally {
        this.loading = false;
      }
    },

    // 批量删除用户配置
    async batchDeleteConfigs(ckIds) {
      this.loading = true;
      try {
        const result = await walmartCKApi.batchDelete(ckIds);

        // 从列表中移除成功删除的配置
        if (result.success_items && result.success_items.length > 0) {
          this.configList = this.configList.filter(
            (item) => !result.success_items.includes(item.id)
          );

          // 如果当前配置被删除，清空当前配置
          if (
            this.currentConfig &&
            result.success_items.includes(this.currentConfig.id)
          ) {
            this.currentConfig = null;
          }
        }

        return result;
      } catch (error) {
        this.error = error.message;
        throw error;
      } finally {
        this.loading = false;
      }
    },

    // 批量启用用户配置
    async batchEnableConfigs(ckIds) {
      this.loading = true;
      try {
        const result = await walmartCKApi.batchEnable(ckIds);

        // 更新列表中成功启用的配置状态
        if (result.success_items && result.success_items.length > 0) {
          this.configList = this.configList.map((item) => {
            if (result.success_items.includes(item.id)) {
              return { ...item, active: true };
            }
            return item;
          });

          // 如果当前配置被启用，更新当前配置状态
          if (
            this.currentConfig &&
            result.success_items.includes(this.currentConfig.id)
          ) {
            this.currentConfig = { ...this.currentConfig, active: true };
          }
        }

        return result;
      } catch (error) {
        this.error = error.message;
        throw error;
      } finally {
        this.loading = false;
      }
    },

    // 批量禁用用户配置
    async batchDisableConfigs(ckIds) {
      this.loading = true;
      try {
        const result = await walmartCKApi.batchDisable(ckIds);

        // 更新列表中成功禁用的配置状态
        if (result.success_items && result.success_items.length > 0) {
          this.configList = this.configList.map((item) => {
            if (result.success_items.includes(item.id)) {
              return { ...item, active: false };
            }
            return item;
          });

          // 如果当前配置被禁用，更新当前配置状态
          if (
            this.currentConfig &&
            result.success_items.includes(this.currentConfig.id)
          ) {
            this.currentConfig = { ...this.currentConfig, active: false };
          }
        }

        return result;
      } catch (error) {
        this.error = error.message;
        throw error;
      } finally {
        this.loading = false;
      }
    },

    // 设置当前配置
    setCurrentConfig(config) {
      this.currentConfig = config;
    },

    // 同步CK到Redis缓存
    async syncToRedis(params = {}) {
      this.loading = true;
      try {
        const response = await walmartCKApi.syncToRedis(params);
        return response;
      } catch (error) {
        this.error = error.message;
        throw error;
      } finally {
        this.loading = false;
      }
    },

    // 重置状态
    resetState() {
      this.configList = [];
      this.currentConfig = null;
      this.loading = false;
      this.error = null;
    },
  },
});
