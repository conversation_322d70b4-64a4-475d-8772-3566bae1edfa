"""
仪表盘分析服务类
负责处理仪表盘相关的高级分析功能
"""
from typing import Any, Dict, List, Optional, Tuple, Union
from datetime import datetime, timedelta, date
from decimal import Decimal
from sqlalchemy import func, case, and_, or_, text
from sqlalchemy.orm import Session

from app.models.user import User
from app.models.merchant import Merchant
from app.models.card_record import CardRecord
from app.core.logging import get_logger
from app.core.errors import BusinessException, ErrorCode


class DashboardAnalyticsService:
    """仪表盘分析业务逻辑服务类"""

    def __init__(self, db: Session):
        self.db = db
        self.logger = get_logger("dashboard_analytics_service")

    def _safe_float(self, value: Union[int, float, Decimal, None], default: float = 0.0) -> float:
        """
        安全地将数值转换为 float 类型，处理 Decimal、None 等情况
        """
        if value is None:
            return default

        try:
            if isinstance(value, Decimal):
                return float(value)
            elif isinstance(value, (int, float)):
                return float(value)
            else:
                return float(value)
        except (ValueError, TypeError, OverflowError) as e:
            self.logger.warning(f"数值转换失败: {value} -> {e}, 使用默认值: {default}")
            return default

    def _safe_int(self, value: Union[int, float, Decimal, None], default: int = 0) -> int:
        """
        安全地将数值转换为 int 类型
        """
        if value is None:
            return default

        try:
            if isinstance(value, Decimal):
                return int(value)
            elif isinstance(value, (int, float)):
                return int(value)
            else:
                return int(float(value))
        except (ValueError, TypeError, OverflowError) as e:
            self.logger.warning(f"整数转换失败: {value} -> {e}, 使用默认值: {default}")
            return default
    
    def _calculate_time_range(self, time_range: str) -> Tuple[date, date, date]:
        """计算时间范围"""
        # 修复时区问题：使用上海时区的当前日期
        from app.utils.time_utils import get_current_time
        today = get_current_time().date()
        if time_range == "today":
            start_date = today
        elif time_range == "week":
            start_date = today - timedelta(days=7)
        elif time_range == "month":
            start_date = today - timedelta(days=30)
        elif time_range == "quarter":
            start_date = today - timedelta(days=90)
        else:
            start_date = today - timedelta(days=7)  # 默认一周

        # 计算上一个时间段（用于计算增长率）
        time_delta = (today - start_date).days
        previous_start_date = start_date - timedelta(days=time_delta)

        return today, start_date, previous_start_date

    def _get_merchants_by_user_role(self, current_user: User) -> List[Merchant]:
        """根据用户角色获取商户列表"""
        if current_user.is_platform_user():
            # 平台用户可以查看所有商户
            return self.db.query(Merchant).filter(Merchant.status == True).all()
        else:
            # 商家用户只能查看自己的商户
            return self.db.query(Merchant).filter(
                Merchant.id == current_user.merchant_id,
                Merchant.status == True
            ).all()

    def _get_merchant_statistics(self, merchant: Merchant, start_date: date, today: date) -> Dict[str, Any]:
        """获取单个商户的统计数据 - 优化版本，使用单次聚合查询避免N+1查询"""
        # 使用单次聚合查询获取所有统计信息
        stats = (
            self.db.query(
                func.count(CardRecord.id).label('total_records'),
                func.sum(func.case((CardRecord.status == "success", 1), else_=0)).label('success_records'),
                func.avg(func.case((CardRecord.status == "success", CardRecord.process_time), else_=None)).label('avg_time')
            )
            .filter(
                CardRecord.merchant_id == merchant.id,
                func.date(CardRecord.created_at) >= start_date,
                func.date(CardRecord.created_at) <= today,
            )
            .first()
        )

        # 使用安全转换函数处理可能的 Decimal 类型
        total_records = self._safe_int(stats.total_records, 0)
        success_records = self._safe_int(stats.success_records, 0)
        success_rate = (success_records / total_records * 100) if total_records > 0 else 0.0
        avg_time = self._safe_float(stats.avg_time, 0.0)

        return {
            "total_records": total_records,
            "success_records": success_records,
            "success_rate": success_rate,
            "avg_time": avg_time
        }

    def _calculate_growth_rate(self, merchant: Merchant, current_total: int,
                              previous_start_date: date, start_date: date) -> float:
        """计算增长率"""
        previous_total = (
            self.db.query(func.count(CardRecord.id))
            .filter(
                CardRecord.merchant_id == merchant.id,
                func.date(CardRecord.created_at) >= previous_start_date,
                func.date(CardRecord.created_at) < start_date,
            )
            .scalar() or 0
        )

        if previous_total > 0:
            return ((current_total - previous_total) / previous_total) * 100
        else:
            return 100 if current_total > 0 else 0

    def _sort_merchants_data(self, merchants_data: List[Dict], sort_by: str) -> List[Dict]:
        """对商户数据进行排序"""
        if sort_by == "total":
            merchants_data.sort(key=lambda x: x["total"], reverse=True)
        elif sort_by == "successRate":
            merchants_data.sort(key=lambda x: x["successRate"], reverse=True)
        elif sort_by == "avgTime":
            merchants_data.sort(key=lambda x: x["avgTime"])  # 时间越短越好，升序排序
        elif sort_by == "growthRate":
            merchants_data.sort(key=lambda x: x["growthRate"], reverse=True)

        return merchants_data

    def get_merchant_activity(
        self,
        current_user: User,
        time_range: str = "week",
        sort_by: str = "total",
    ) -> Dict[str, Any]:
        """
        获取商户活跃度数据

        Args:
            current_user: 当前用户
            time_range: 时间范围 (today/week/month/quarter)
            sort_by: 排序方式 (total/successRate/avgTime/growthRate)

        Returns:
            Dict[str, Any]: 商户活跃度数据
        """
        try:
            # 计算时间范围
            today, start_date, previous_start_date = self._calculate_time_range(time_range)

            # 获取商户列表
            merchants = self._get_merchants_by_user_role(current_user)

            # 批量获取商户数据，避免N+1查询
            merchants_data = self._get_batch_merchant_statistics(
                merchants, start_date, today, previous_start_date
            )

            # 排序
            merchants_data = self._sort_merchants_data(merchants_data, sort_by)

            return {"merchants": merchants_data, "timeRange": time_range}

        except Exception as e:
            self.logger.error(f"获取商户活跃度数据失败: {str(e)}", exc_info=True)
            raise BusinessException(
                message=f"获取商户活跃度数据失败: {str(e)}", code=ErrorCode.SYSTEM_ERROR
            )

    def _get_trend_time_config(self, time_range: str) -> Tuple[date, str]:
        """获取趋势分析的时间配置"""
        today = datetime.now().date()

        time_configs = {
            "week": (today - timedelta(days=7), "%Y-%m-%d"),
            "month": (today - timedelta(days=30), "%Y-%m-%d"),
            "quarter": (today - timedelta(days=90), "%Y-%W"),
            "year": (today - timedelta(days=365), "%Y-%m"),
        }

        return time_configs.get(time_range, (today - timedelta(days=7), "%Y-%m-%d"))

    def _build_trend_query(self, current_user: User, start_date: date, today: date, date_format: str):
        """构建趋势数据查询"""
        query = self.db.query(CardRecord)
        if not current_user.is_platform_user():
            query = query.filter(CardRecord.merchant_id == current_user.merchant_id)

        return (
            query.filter(
                func.date(CardRecord.created_at) >= start_date,
                func.date(CardRecord.created_at) <= today,
            )
            .with_entities(
                func.date_format(CardRecord.created_at, date_format).label("date_group"),
                func.count().label("total_count"),
                func.sum(case((CardRecord.status == "success", 1), else_=0)).label("success_count"),
                func.sum(case((CardRecord.status == "failed", 1), else_=0)).label("failed_count"),
            )
            .group_by("date_group")
            .order_by("date_group")
            .all()
        )

    def _format_trend_data(self, trend_data) -> List[Dict[str, Any]]:
        """格式化趋势数据"""
        formatted_data = []
        for data in trend_data:
            success_rate = (
                (data.success_count / data.total_count * 100)
                if data.total_count > 0
                else 0
            )
            formatted_data.append({
                "date": data.date_group,
                "total": data.total_count,
                "success": data.success_count,
                "failed": data.failed_count,
                "successRate": round(success_rate, 2),
            })
        return formatted_data

    def _calculate_trend_summary(self, formatted_data: List[Dict[str, Any]]) -> Dict[str, Any]:
        """计算趋势数据摘要"""
        if not formatted_data:
            return {
                "totalRecords": 0,
                "totalSuccess": 0,
                "totalFailed": 0,
                "avgSuccessRate": 0,
            }

        return {
            "totalRecords": sum(d["total"] for d in formatted_data),
            "totalSuccess": sum(d["success"] for d in formatted_data),
            "totalFailed": sum(d["failed"] for d in formatted_data),
            "avgSuccessRate": round(
                sum(d["successRate"] for d in formatted_data) / len(formatted_data), 2
            ),
        }

    def get_bind_trend(
        self,
        current_user: User,
        time_range: str = "week",
    ) -> Dict[str, Any]:
        """
        获取绑卡趋势数据

        Args:
            current_user: 当前用户
            time_range: 时间范围 (week/month/quarter/year)

        Returns:
            Dict[str, Any]: 绑卡趋势数据
        """
        try:
            today = datetime.now().date()

            # 获取时间配置
            start_date, date_format = self._get_trend_time_config(time_range)

            # 构建查询并获取数据
            trend_data = self._build_trend_query(current_user, start_date, today, date_format)

            # 格式化数据
            formatted_data = self._format_trend_data(trend_data)

            # 计算摘要
            summary = self._calculate_trend_summary(formatted_data)

            return {
                "trendData": formatted_data,
                "timeRange": time_range,
                "summary": summary,
            }

        except Exception as e:
            self.logger.error(f"获取绑卡趋势数据失败: {str(e)}", exc_info=True)
            raise BusinessException(
                message=f"获取绑卡趋势数据失败: {str(e)}", code=ErrorCode.SYSTEM_ERROR
            )

    def _get_batch_merchant_statistics(
        self,
        merchants: List,
        start_date: date,
        today: date,
        previous_start_date: date
    ) -> List[Dict[str, Any]]:
        """批量获取商户统计数据，避免N+1查询"""
        if not merchants:
            return []

        merchant_ids = [merchant.id for merchant in merchants]

        # 批量查询当前期间的统计数据
        current_stats = self._batch_query_merchant_stats(merchant_ids, start_date, today)

        # 批量查询上一期间的统计数据（用于计算增长率）
        previous_stats = self._batch_query_merchant_stats(merchant_ids, previous_start_date, start_date)

        # 构建结果
        merchants_data = []
        for merchant in merchants:
            current_stat = current_stats.get(merchant.id, {
                "total_records": 0, "success_records": 0, "success_rate": 0, "avg_time": 0
            })
            previous_total = previous_stats.get(merchant.id, {}).get("total_records", 0)

            # 计算增长率
            growth_rate = self._calculate_growth_rate_from_totals(
                current_stat["total_records"], previous_total
            )

            merchants_data.append({
                "name": merchant.name,
                "total": current_stat["total_records"],
                "successRate": round(current_stat["success_rate"], 1),
                "avgTime": round(current_stat["avg_time"], 1),
                "growthRate": round(growth_rate, 1),
            })

        return merchants_data

    def _batch_query_merchant_stats(self, merchant_ids: List[int], start_date: date, end_date: date) -> Dict[int, Dict]:
        """批量查询商户统计数据"""
        from sqlalchemy import func, case

        stats_query = (
            self.db.query(
                CardRecord.merchant_id,
                func.count().label("total_records"),
                func.sum(case((CardRecord.status == "success", 1), else_=0)).label("success_records"),
                func.avg(CardRecord.process_time).label("avg_time")
            )
            .filter(
                CardRecord.merchant_id.in_(merchant_ids),
                func.date(CardRecord.created_at) >= start_date,
                func.date(CardRecord.created_at) <= end_date,
            )
            .group_by(CardRecord.merchant_id)
            .all()
        )

        # 转换为字典格式
        result = {}
        for stat in stats_query:
            success_rate = (stat.success_records / stat.total_records * 100) if stat.total_records > 0 else 0
            avg_time = float(stat.avg_time) if stat.avg_time is not None else 0

            result[stat.merchant_id] = {
                "total_records": stat.total_records,
                "success_records": stat.success_records,
                "success_rate": success_rate,
                "avg_time": avg_time
            }

        return result

    def _calculate_growth_rate_from_totals(self, current_total: int, previous_total: int) -> float:
        """根据总数计算增长率"""
        if previous_total == 0:
            return 100.0 if current_total > 0 else 0.0
        return ((current_total - previous_total) / previous_total) * 100

    def _get_failure_analysis_time_range(self, time_range: str) -> date:
        """获取失败分析的时间范围"""
        today = datetime.now().date()

        time_ranges = {
            "today": today,
            "week": today - timedelta(days=7),
            "month": today - timedelta(days=30),
        }

        return time_ranges.get(time_range, today - timedelta(days=7))

    def _get_failed_records(self, current_user: User, start_date: date, today: date):
        """获取失败记录"""
        query = self.db.query(CardRecord).filter(CardRecord.status == "failed")
        if not current_user.is_platform_user():
            query = query.filter(CardRecord.merchant_id == current_user.merchant_id)

        return query.filter(
            func.date(CardRecord.created_at) >= start_date,
            func.date(CardRecord.created_at) <= today,
        ).all()

    def _classify_failure_type(self, error_msg: str) -> str:
        """分类失败类型"""
        if not error_msg:
            return "其他错误"

        error_msg_lower = error_msg.lower()

        if "网络" in error_msg or "timeout" in error_msg_lower:
            return "网络超时"
        elif "余额" in error_msg or "balance" in error_msg_lower:
            return "余额不足"
        elif "验证" in error_msg or "auth" in error_msg_lower:
            return "验证失败"
        else:
            return "其他错误"

    def _analyze_failure_patterns(self, failed_records) -> Tuple[Dict[str, int], List[int]]:
        """分析失败模式 - 优化版本，减少循环处理"""
        failure_types = {}
        failure_times = [0] * 24  # 24小时

        # 批量处理失败记录，减少单独处理的开销
        for record in failed_records:
            # 分析失败类型
            failure_type = self._classify_failure_type(record.error_message)
            failure_types[failure_type] = failure_types.get(failure_type, 0) + 1

            # 统计失败时间分布
            hour = record.created_at.hour
            failure_times[hour] += 1

        return failure_types, failure_times

    def _format_failure_type_data(self, failure_types: Dict[str, int], total_failures: int) -> List[Dict[str, Any]]:
        """格式化失败类型数据"""
        if total_failures == 0:
            return []

        failure_type_data = [
            {"type": k, "count": v, "percentage": round(v / total_failures * 100, 1)}
            for k, v in failure_types.items()
        ]
        failure_type_data.sort(key=lambda x: x["count"], reverse=True)
        return failure_type_data

    def _create_failure_summary(self, failure_type_data: List[Dict], failure_times: List[int]) -> Dict[str, Any]:
        """创建失败分析摘要"""
        total_failures = sum(item["count"] for item in failure_type_data)
        top_failure = failure_type_data[0]["type"] if failure_type_data else "无"

        hours = [f"{i:02d}:00" for i in range(24)]
        peak_hour = hours[failure_times.index(max(failure_times))] if any(failure_times) else "无"

        return {
            "totalFailures": total_failures,
            "topFailure": top_failure,
            "peakHour": peak_hour,
        }

    def get_failure_analysis(
        self,
        current_user: User,
        time_range: str = "week",
    ) -> Dict[str, Any]:
        """
        获取失败分析数据 - 优化版本，使用聚合查询避免N+1查询

        Args:
            current_user: 当前用户
            time_range: 时间范围 (today/week/month)

        Returns:
            Dict[str, Any]: 失败分析数据
        """
        try:
            today = datetime.now().date()

            # 获取时间范围
            start_date = self._get_failure_analysis_time_range(time_range)

            # 使用聚合查询获取失败时间分布
            failure_times = self._get_failure_time_distribution(current_user, start_date, today)

            # 获取失败记录用于类型分析（只获取必要字段）
            failed_records = self._get_failed_records_for_analysis(current_user, start_date, today)

            # 分析失败类型
            failure_types = self._analyze_failure_types_optimized(failed_records)

            # 格式化失败类型数据
            failure_type_data = self._format_failure_type_data(failure_types, len(failed_records))

            # 创建摘要
            summary = self._create_failure_summary(failure_type_data, failure_times)

            # 格式化失败时间数据
            hours = [f"{i:02d}:00" for i in range(24)]

            return {
                "failureTypes": failure_type_data,
                "failureTimes": {"hours": hours, "counts": failure_times},
                "summary": summary,
            }

        except Exception as e:
            self.logger.error(f"获取失败分析数据失败: {str(e)}", exc_info=True)
            raise BusinessException(
                message=f"获取失败分析数据失败: {str(e)}", code=ErrorCode.SYSTEM_ERROR
            )

    def _get_failure_time_distribution(self, current_user: User, start_date: date, today: date) -> List[int]:
        """使用聚合查询获取失败时间分布 - 避免N+1查询"""
        query = self.db.query(CardRecord).filter(CardRecord.status == "failed")
        if not current_user.is_platform_user():
            query = query.filter(CardRecord.merchant_id == current_user.merchant_id)

        # 使用聚合查询获取每小时的失败次数
        hourly_failures = query.filter(
            func.date(CardRecord.created_at) >= start_date,
            func.date(CardRecord.created_at) <= today,
        ).with_entities(
            func.hour(CardRecord.created_at).label('hour'),
            func.count(CardRecord.id).label('count')
        ).group_by(func.hour(CardRecord.created_at)).all()

        # 构建24小时完整数据
        failure_times = [0] * 24
        for hour_stat in hourly_failures:
            failure_times[hour_stat.hour] = hour_stat.count

        return failure_times

    def _get_failed_records_for_analysis(self, current_user: User, start_date: date, today: date):
        """获取失败记录用于类型分析（只获取必要字段）- 避免N+1查询"""
        query = self.db.query(CardRecord.error_message).filter(CardRecord.status == "failed")
        if not current_user.is_platform_user():
            query = query.filter(CardRecord.merchant_id == current_user.merchant_id)

        return query.filter(
            func.date(CardRecord.created_at) >= start_date,
            func.date(CardRecord.created_at) <= today,
        ).all()

    def _analyze_failure_types_optimized(self, failed_records) -> Dict[str, int]:
        """优化的失败类型分析 - 只处理错误消息"""
        failure_types = {}

        for record in failed_records:
            failure_type = self._classify_failure_type(record.error_message)
            failure_types[failure_type] = failure_types.get(failure_type, 0) + 1

        return failure_types
