#!/usr/bin/env python3
"""
CK计数监控工具
定期检查CK的bind_count与实际绑卡记录是否一致
"""

import asyncio
import sys
import os
from datetime import datetime, timedelta
from typing import Dict, List, Tuple

# 添加项目路径
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.insert(0, project_root)

from app.api.deps import get_db
from app.models.walmart_ck import WalmartCK
from app.models.card_record import CardRecord
from app.core.logging import get_logger

logger = get_logger("ck_count_monitor")


class CKCountMonitor:
    """CK计数监控器"""
    
    def __init__(self):
        self.db = next(get_db())
        
    def __enter__(self):
        return self
        
    def __exit__(self, exc_type, exc_val, exc_tb):
        if self.db:
            self.db.close()
    
    async def check_all_merchants(self, auto_fix: bool = False) -> Dict:
        """检查所有商户的CK计数一致性"""
        print(f"🔍 检查所有商户的CK计数一致性")
        print(f"自动修复: {'是' if auto_fix else '否'}")
        print("="*80)
        
        # 获取所有活跃的商户
        merchants = self.db.query(WalmartCK.merchant_id).distinct().all()
        merchant_ids = [m[0] for m in merchants]
        
        print(f"📊 找到 {len(merchant_ids)} 个商户: {merchant_ids}")
        
        results = {
            'total_merchants': len(merchant_ids),
            'total_cks': 0,
            'inconsistent_cks': 0,
            'fixed_cks': 0,
            'details': []
        }
        
        for merchant_id in merchant_ids:
            merchant_result = await self._check_merchant_cks(merchant_id, auto_fix)
            results['details'].append(merchant_result)
            results['total_cks'] += merchant_result['total_cks']
            results['inconsistent_cks'] += merchant_result['inconsistent_cks']
            results['fixed_cks'] += merchant_result['fixed_cks']
        
        # 总结报告
        await self._generate_summary_report(results)
        
        return results
    
    async def _check_merchant_cks(self, merchant_id: int, auto_fix: bool = False) -> Dict:
        """检查单个商户的CK计数"""
        print(f"\n🏪 检查商户 {merchant_id}")
        print("-"*60)
        
        # 获取商户的所有CK
        cks = self.db.query(WalmartCK).filter(
            WalmartCK.merchant_id == merchant_id,
            WalmartCK.is_deleted == False
        ).all()
        
        result = {
            'merchant_id': merchant_id,
            'total_cks': len(cks),
            'inconsistent_cks': 0,
            'fixed_cks': 0,
            'ck_details': []
        }
        
        print(f"  找到 {len(cks)} 个CK")
        
        for ck in cks:
            ck_result = await self._check_single_ck(ck, auto_fix)
            result['ck_details'].append(ck_result)
            
            if not ck_result['is_consistent']:
                result['inconsistent_cks'] += 1
                
            if ck_result['was_fixed']:
                result['fixed_cks'] += 1
        
        # 商户级别总结
        if result['inconsistent_cks'] == 0:
            print(f"  ✅ 商户 {merchant_id} 所有CK计数一致")
        else:
            print(f"  ⚠️ 商户 {merchant_id} 有 {result['inconsistent_cks']} 个CK计数不一致")
            if auto_fix:
                print(f"  🔧 已修复 {result['fixed_cks']} 个CK")
        
        return result
    
    async def _check_single_ck(self, ck: WalmartCK, auto_fix: bool = False) -> Dict:
        """检查单个CK的计数"""
        # 计算实际的成功绑卡次数
        actual_count = self.db.query(CardRecord).filter(
            CardRecord.walmart_ck_id == ck.id,
            CardRecord.status == 'success'
        ).count()
        
        current_count = ck.bind_count
        is_consistent = (actual_count == current_count)
        difference = current_count - actual_count
        
        result = {
            'ck_id': ck.id,
            'current_count': current_count,
            'actual_count': actual_count,
            'difference': difference,
            'is_consistent': is_consistent,
            'was_fixed': False
        }
        
        if is_consistent:
            print(f"    CK {ck.id}: ✅ 一致 (bind_count={current_count})")
        else:
            print(f"    CK {ck.id}: ❌ 不一致 bind_count={current_count}, 实际={actual_count}, 差异={difference}")
            
            if auto_fix:
                try:
                    ck.bind_count = actual_count
                    self.db.commit()
                    result['was_fixed'] = True
                    print(f"      🔧 已修复: bind_count更新为 {actual_count}")
                except Exception as e:
                    self.db.rollback()
                    print(f"      ❌ 修复失败: {e}")
        
        return result
    
    async def _generate_summary_report(self, results: Dict):
        """生成总结报告"""
        print(f"\n" + "="*80)
        print("📊 CK计数一致性检查报告")
        print("="*80)
        
        print(f"总体统计:")
        print(f"  检查的商户数: {results['total_merchants']}")
        print(f"  检查的CK总数: {results['total_cks']}")
        print(f"  不一致的CK数: {results['inconsistent_cks']}")
        print(f"  修复的CK数: {results['fixed_cks']}")
        
        if results['inconsistent_cks'] == 0:
            print(f"\n✅ 所有CK计数都一致，系统状态良好！")
        else:
            consistency_rate = (results['total_cks'] - results['inconsistent_cks']) / results['total_cks'] * 100
            print(f"\n⚠️ 发现数据不一致问题")
            print(f"  一致性比率: {consistency_rate:.1f}%")
            
            if results['fixed_cks'] > 0:
                print(f"  ✅ 已修复 {results['fixed_cks']} 个问题")
            
            if results['inconsistent_cks'] > results['fixed_cks']:
                remaining = results['inconsistent_cks'] - results['fixed_cks']
                print(f"  ⚠️ 还有 {remaining} 个问题需要手动处理")
        
        # 详细报告
        print(f"\n📋 详细报告:")
        for detail in results['details']:
            merchant_id = detail['merchant_id']
            if detail['inconsistent_cks'] > 0:
                print(f"  商户 {merchant_id}: {detail['inconsistent_cks']}/{detail['total_cks']} 个CK不一致")
                
                for ck_detail in detail['ck_details']:
                    if not ck_detail['is_consistent']:
                        status = "已修复" if ck_detail['was_fixed'] else "待修复"
                        print(f"    CK {ck_detail['ck_id']}: 差异{ck_detail['difference']:+d} ({status})")
    
    async def schedule_monitoring(self, interval_hours: int = 6):
        """定期监控"""
        print(f"🕐 启动定期监控，间隔: {interval_hours} 小时")
        
        while True:
            try:
                print(f"\n⏰ {datetime.now().strftime('%Y-%m-%d %H:%M:%S')} - 开始定期检查")
                
                results = await self.check_all_merchants(auto_fix=True)
                
                if results['inconsistent_cks'] > 0:
                    logger.warning(f"发现 {results['inconsistent_cks']} 个CK计数不一致，已自动修复 {results['fixed_cks']} 个")
                else:
                    logger.info("所有CK计数一致，系统状态良好")
                
                print(f"⏰ 下次检查时间: {(datetime.now() + timedelta(hours=interval_hours)).strftime('%Y-%m-%d %H:%M:%S')}")
                
                # 等待下次检查
                await asyncio.sleep(interval_hours * 3600)
                
            except Exception as e:
                logger.error(f"定期监控出错: {e}")
                print(f"❌ 监控出错: {e}")
                # 出错后等待1小时再重试
                await asyncio.sleep(3600)
    
    async def export_inconsistency_report(self, output_file: str = None):
        """导出不一致报告"""
        if not output_file:
            output_file = f"ck_inconsistency_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
        
        print(f"📄 导出不一致报告到: {output_file}")
        
        results = await self.check_all_merchants(auto_fix=False)
        
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write("CK计数不一致报告\n")
            f.write("="*50 + "\n")
            f.write(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
            
            f.write(f"总体统计:\n")
            f.write(f"  检查的商户数: {results['total_merchants']}\n")
            f.write(f"  检查的CK总数: {results['total_cks']}\n")
            f.write(f"  不一致的CK数: {results['inconsistent_cks']}\n\n")
            
            f.write("详细问题列表:\n")
            for detail in results['details']:
                if detail['inconsistent_cks'] > 0:
                    f.write(f"\n商户 {detail['merchant_id']}:\n")
                    for ck_detail in detail['ck_details']:
                        if not ck_detail['is_consistent']:
                            f.write(f"  CK {ck_detail['ck_id']}: "
                                   f"当前={ck_detail['current_count']}, "
                                   f"实际={ck_detail['actual_count']}, "
                                   f"差异={ck_detail['difference']:+d}\n")
        
        print(f"✅ 报告已导出到: {output_file}")


async def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description="CK计数监控工具")
    parser.add_argument("--check", action="store_true", help="检查所有CK计数一致性")
    parser.add_argument("--fix", action="store_true", help="自动修复不一致的计数")
    parser.add_argument("--monitor", type=int, metavar="HOURS", help="启动定期监控（指定间隔小时数）")
    parser.add_argument("--export", type=str, metavar="FILE", nargs='?', const="auto", help="导出不一致报告")
    
    args = parser.parse_args()
    
    if not any([args.check, args.monitor, args.export]):
        parser.print_help()
        return
    
    with CKCountMonitor() as monitor:
        if args.check:
            await monitor.check_all_merchants(auto_fix=args.fix)
        
        if args.monitor:
            await monitor.schedule_monitoring(interval_hours=args.monitor)
        
        if args.export:
            output_file = None if args.export == "auto" else args.export
            await monitor.export_inconsistency_report(output_file)
    
    print(f"\n🎉 监控完成！")


if __name__ == "__main__":
    asyncio.run(main())
