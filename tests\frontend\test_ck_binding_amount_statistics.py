"""
CK绑卡金额统计前端功能测试

使用Playwright进行前端自动化测试
测试内容：
1. 统计卡片显示
2. 筛选器功能
3. 数据更新
4. 权限控制
"""

import pytest
from playwright.sync_api import Page, expect
from test.base_playwright_test import BasePlaywrightTest


class TestCKBindingAmountStatisticsFrontend(BasePlaywrightTest):
    """CK绑卡金额统计前端测试类"""

    def test_statistics_cards_display_super_admin(self, page: Page):
        """测试超级管理员查看统计卡片显示"""
        # 登录超级管理员
        self.login_as_super_admin(page)
        
        # 导航到CK管理页面
        page.goto(f"{self.base_url}/#/walmart")
        page.wait_for_load_state('networkidle')
        
        # 验证统计卡片区域存在
        statistics_section = page.locator('.statistics-section')
        expect(statistics_section).to_be_visible()
        
        # 验证四个统计卡片都存在
        statistics_cards = page.locator('.statistics-card')
        expect(statistics_cards).to_have_count(4)
        
        # 验证每个统计卡片的内容
        card_labels = ['总CK数量', '成功绑卡数', '成功率', '实际绑卡金额']
        for i, label in enumerate(card_labels):
            card = statistics_cards.nth(i)
            expect(card.locator('.statistics-label')).to_contain_text(label)
            expect(card.locator('.statistics-value')).to_be_visible()

    def test_department_filter_functionality(self, page: Page):
        """测试部门筛选功能"""
        # 登录超级管理员
        self.login_as_super_admin(page)
        
        # 导航到CK管理页面
        page.goto(f"{self.base_url}/#/walmart")
        page.wait_for_load_state('networkidle')
        
        # 等待部门选择器加载
        department_select = page.locator('el-select').first()
        expect(department_select).to_be_visible()
        
        # 点击部门选择器
        department_select.click()
        
        # 等待选项加载
        page.wait_for_timeout(1000)
        
        # 检查是否有部门选项
        options = page.locator('.el-select-dropdown__item')
        if options.count() > 1:  # 除了"全部部门"选项
            # 选择第一个具体部门
            options.nth(1).click()
            
            # 等待统计数据更新
            page.wait_for_timeout(2000)
            
            # 验证统计数据已更新（通过检查loading状态）
            statistics_section = page.locator('.statistics-section')
            expect(statistics_section).to_be_visible()

    def test_date_range_filter_functionality(self, page: Page):
        """测试日期范围筛选功能"""
        # 登录超级管理员
        self.login_as_super_admin(page)
        
        # 导航到CK管理页面
        page.goto(f"{self.base_url}/#/walmart")
        page.wait_for_load_state('networkidle')
        
        # 找到日期范围选择器
        date_picker = page.locator('.el-date-editor')
        expect(date_picker).to_be_visible()
        
        # 点击日期选择器
        date_picker.click()
        
        # 等待日期选择面板出现
        page.wait_for_timeout(1000)
        
        # 选择日期范围（这里简化处理，实际可以选择具体日期）
        # 由于日期选择器的复杂性，这里主要验证组件存在和可交互
        page.keyboard.press('Escape')  # 关闭日期选择器

    def test_refresh_statistics_button(self, page: Page):
        """测试刷新统计按钮功能"""
        # 登录超级管理员
        self.login_as_super_admin(page)
        
        # 导航到CK管理页面
        page.goto(f"{self.base_url}/#/walmart")
        page.wait_for_load_state('networkidle')
        
        # 找到刷新统计按钮
        refresh_button = page.locator('text=刷新统计')
        expect(refresh_button).to_be_visible()
        
        # 点击刷新按钮
        refresh_button.click()
        
        # 验证loading状态（如果有的话）
        page.wait_for_timeout(1000)
        
        # 验证统计卡片仍然可见
        statistics_cards = page.locator('.statistics-card')
        expect(statistics_cards).to_have_count(4)

    def test_statistics_data_format(self, page: Page):
        """测试统计数据格式显示"""
        # 登录超级管理员
        self.login_as_super_admin(page)
        
        # 导航到CK管理页面
        page.goto(f"{self.base_url}/#/walmart")
        page.wait_for_load_state('networkidle')
        
        # 等待统计数据加载
        page.wait_for_timeout(3000)
        
        # 验证金额格式（应该以¥开头）
        amount_card = page.locator('.statistics-card').nth(3)  # 实际绑卡金额卡片
        amount_value = amount_card.locator('.statistics-value')
        expect(amount_value).to_be_visible()
        
        # 验证成功率格式（应该以%结尾）
        rate_card = page.locator('.statistics-card').nth(2)  # 成功率卡片
        rate_value = rate_card.locator('.statistics-value')
        expect(rate_value).to_be_visible()

    def test_merchant_admin_data_isolation(self, page: Page):
        """测试商户管理员数据隔离"""
        # 登录商户管理员
        self.login_as_merchant_admin(page)
        
        # 导航到CK管理页面
        page.goto(f"{self.base_url}/#/walmart")
        page.wait_for_load_state('networkidle')
        
        # 验证统计卡片显示
        statistics_section = page.locator('.statistics-section')
        expect(statistics_section).to_be_visible()
        
        # 验证统计卡片存在
        statistics_cards = page.locator('.statistics-card')
        expect(statistics_cards).to_have_count(4)
        
        # 商户管理员应该只能看到自己商户的数据
        # 这里主要验证界面正常显示，具体数据隔离在后端API测试中验证

    def test_filter_section_visibility(self, page: Page):
        """测试筛选区域可见性"""
        # 登录超级管理员
        self.login_as_super_admin(page)
        
        # 导航到CK管理页面
        page.goto(f"{self.base_url}/#/walmart")
        page.wait_for_load_state('networkidle')
        
        # 验证筛选区域存在
        filter_card = page.locator('.el-card').first()
        expect(filter_card).to_be_visible()
        
        # 验证筛选条件标题
        filter_header = page.locator('text=筛选条件')
        expect(filter_header).to_be_visible()
        
        # 验证部门筛选标签
        department_label = page.locator('text=部门筛选')
        expect(department_label).to_be_visible()
        
        # 验证日期范围标签
        date_label = page.locator('text=日期范围')
        expect(date_label).to_be_visible()

    def test_statistics_cards_hover_effect(self, page: Page):
        """测试统计卡片悬停效果"""
        # 登录超级管理员
        self.login_as_super_admin(page)
        
        # 导航到CK管理页面
        page.goto(f"{self.base_url}/#/walmart")
        page.wait_for_load_state('networkidle')
        
        # 获取第一个统计卡片
        first_card = page.locator('.statistics-card').first()
        expect(first_card).to_be_visible()
        
        # 悬停在卡片上
        first_card.hover()
        
        # 验证卡片仍然可见（悬停效果主要是CSS变化）
        expect(first_card).to_be_visible()

    def test_responsive_layout(self, page: Page):
        """测试响应式布局"""
        # 登录超级管理员
        self.login_as_super_admin(page)
        
        # 导航到CK管理页面
        page.goto(f"{self.base_url}/#/walmart")
        page.wait_for_load_state('networkidle')
        
        # 测试不同屏幕尺寸
        page.set_viewport_size({"width": 1200, "height": 800})
        page.wait_for_timeout(1000)
        
        # 验证统计卡片在大屏幕上正常显示
        statistics_cards = page.locator('.statistics-card')
        expect(statistics_cards).to_have_count(4)
        
        # 测试中等屏幕尺寸
        page.set_viewport_size({"width": 768, "height": 600})
        page.wait_for_timeout(1000)
        
        # 验证统计卡片仍然可见
        expect(statistics_cards).to_have_count(4)


if __name__ == '__main__':
    # 运行测试
    pytest.main([__file__, '-v'])
