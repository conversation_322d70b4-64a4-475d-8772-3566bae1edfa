/* 全局样式 */
body {
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
}

#app {
  width: 100%;
  height: 100vh;
}

/* 修复Element Plus表单标签宽度问题 */
.el-form-item__label {
  min-width: 0 !important;
}

/* 确保内联表单的标签有合适的宽度 */
.el-form--inline .el-form-item__label {
  min-width: auto !important;
  width: auto !important;
}

/* 防止表单项标签宽度计算错误 */
.el-form-item__label[style*="width: 0px"] {
  width: auto !important;
  min-width: 60px !important;
}