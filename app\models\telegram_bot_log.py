from sqlalchemy import Column, String, BigInteger, Integer, Text, ForeignKey, JSON
from sqlalchemy.orm import relationship
from enum import Enum as PyEnum

from app.models.base import BaseModel, TimestampMixin


class LogStatus(PyEnum):
    """执行状态枚举"""
    SUCCESS = "success"
    ERROR = "error"
    PERMISSION_DENIED = "permission_denied"


class TelegramBotLog(BaseModel, TimestampMixin):
    """机器人操作日志表"""

    __tablename__ = "telegram_bot_logs"

    # 基础信息
    chat_id = Column(
        BigInteger, 
        nullable=False,
        index=True,
        comment="群组ID"
    )
    user_id = Column(
        BigInteger, 
        nullable=False,
        index=True,
        comment="Telegram用户ID"
    )
    command = Column(
        String(100), 
        nullable=False,
        index=True,
        comment="执行的命令"
    )

    # 请求和响应数据
    request_data = Column(
        JSON, 
        nullable=True, 
        comment="请求数据"
    )
    response_data = Column(
        JSON, 
        nullable=True, 
        comment="响应数据"
    )

    # 执行信息
    execution_time = Column(
        Integer, 
        nullable=True, 
        comment="执行时间（毫秒）"
    )
    status = Column(
        String(20),
        nullable=False,
        index=True,
        comment="执行状态"
    )
    error_message = Column(
        Text, 
        nullable=True, 
        comment="错误信息"
    )

    # 关联的群组和用户
    group_id = Column(
        BigInteger, 
        ForeignKey("telegram_groups.id", ondelete="SET NULL"), 
        nullable=True,
        index=True,
        comment="关联的群组记录ID"
    )
    telegram_user_id = Column(
        BigInteger, 
        ForeignKey("telegram_users.id", ondelete="SET NULL"), 
        nullable=True,
        index=True,
        comment="关联的Telegram用户记录ID"
    )

    # 关联关系
    group = relationship(
        "TelegramGroup", 
        back_populates="bot_logs",
        foreign_keys=[group_id]
    )
    telegram_user = relationship(
        "TelegramUser", 
        back_populates="bot_logs",
        foreign_keys=[telegram_user_id]
    )

    def to_dict(self):
        """转换为字典"""
        return {
            "id": self.id,
            "chat_id": self.chat_id,
            "user_id": self.user_id,
            "command": self.command,
            "request_data": self.request_data,
            "response_data": self.response_data,
            "execution_time": self.execution_time,
            "status": self.status,
            "error_message": self.error_message,
            "group_id": self.group_id,
            "telegram_user_id": self.telegram_user_id,
            "created_at": self.created_at.isoformat() if self.created_at else None,
        }

    def is_success(self):
        """检查是否执行成功"""
        return self.status == LogStatus.SUCCESS.value

    def is_error(self):
        """检查是否执行错误"""
        return self.status == LogStatus.ERROR.value

    def is_permission_denied(self):
        """检查是否权限被拒绝"""
        return self.status == LogStatus.PERMISSION_DENIED.value

    @classmethod
    def create_log(cls, db_session, chat_id, user_id, command, status, **kwargs):
        """创建日志记录

        增强版本，解决外键约束失败问题：
        1. 严格验证外键ID的有效性
        2. 处理会话缓存和事务隔离问题
        3. 提供多层次的容错机制
        """
        import logging
        logger = logging.getLogger(__name__)

        try:
            # 获取外键ID
            group_id = kwargs.get('group_id')
            telegram_user_id = kwargs.get('telegram_user_id')

            # 刷新会话状态，确保获取最新数据
            try:
                # 如果有未提交的事务，先提交
                if db_session.dirty or db_session.new or db_session.deleted:
                    db_session.flush()
            except Exception as flush_error:
                logger.warning(f"刷新会话状态失败: {flush_error}")
                db_session.rollback()

            # 严格验证group_id的有效性
            if group_id is not None:
                try:
                    from app.models.telegram_group import TelegramGroup
                    # 使用独立查询验证，不依赖缓存
                    exists = db_session.execute(
                        db_session.query(TelegramGroup.id).filter_by(id=group_id).exists().select()
                    ).scalar()
                    if not exists:
                        logger.warning(f"群组ID {group_id} 不存在，设置为None")
                        group_id = None
                except Exception as group_check_error:
                    logger.warning(f"验证群组ID失败: {group_check_error}")
                    group_id = None

            # 严格验证telegram_user_id的有效性
            if telegram_user_id is not None:
                try:
                    from app.models.telegram_user import TelegramUser
                    # 使用独立查询验证，不依赖缓存
                    exists = db_session.execute(
                        db_session.query(TelegramUser.id).filter_by(id=telegram_user_id).exists().select()
                    ).scalar()
                    if not exists:
                        logger.warning(f"用户ID {telegram_user_id} 不存在，设置为None")
                        telegram_user_id = None
                except Exception as user_check_error:
                    logger.warning(f"验证用户ID失败: {user_check_error}")
                    telegram_user_id = None

            # 创建日志记录
            log = cls(
                chat_id=chat_id,
                user_id=user_id,
                command=command,
                status=status,
                request_data=kwargs.get('request_data'),
                response_data=kwargs.get('response_data'),
                execution_time=kwargs.get('execution_time'),
                error_message=kwargs.get('error_message'),
                group_id=group_id,
                telegram_user_id=telegram_user_id
            )
            db_session.add(log)
            db_session.commit()

            logger.debug(f"成功创建日志记录: chat_id={chat_id}, group_id={group_id}, telegram_user_id={telegram_user_id}")
            return log

        except Exception as create_error:
            db_session.rollback()
            logger.warning(f"创建日志记录失败，尝试不使用外键关联: {create_error}")

            # 容错机制：如果仍然失败，尝试不使用外键关联
            try:
                log = cls(
                    chat_id=chat_id,
                    user_id=user_id,
                    command=command,
                    status=status,
                    request_data=kwargs.get('request_data'),
                    response_data=kwargs.get('response_data'),
                    execution_time=kwargs.get('execution_time'),
                    error_message=kwargs.get('error_message'),
                    group_id=None,
                    telegram_user_id=None
                )
                db_session.add(log)
                db_session.commit()

                logger.info(f"使用容错机制成功创建日志记录: chat_id={chat_id}")
                return log

            except Exception as final_error:
                db_session.rollback()
                logger.error(f"日志记录创建完全失败: {final_error}")
                raise final_error

    @classmethod
    def log_success(cls, db_session, chat_id, user_id, command, execution_time=None, **kwargs):
        """记录成功日志"""
        return cls.create_log(
            db_session, chat_id, user_id, command, LogStatus.SUCCESS.value,
            execution_time=execution_time, **kwargs
        )

    @classmethod
    def log_error(cls, db_session, chat_id, user_id, command, error_message, execution_time=None, **kwargs):
        """记录错误日志"""
        return cls.create_log(
            db_session, chat_id, user_id, command, LogStatus.ERROR.value,
            error_message=error_message, execution_time=execution_time, **kwargs
        )

    @classmethod
    def log_permission_denied(cls, db_session, chat_id, user_id, command, error_message=None, **kwargs):
        """记录权限拒绝日志"""
        return cls.create_log(
            db_session, chat_id, user_id, command, LogStatus.PERMISSION_DENIED.value,
            error_message=error_message or "权限不足", **kwargs
        )

    @classmethod
    def get_user_command_count(cls, db_session, user_id, command=None, hours=24):
        """获取用户命令执行次数"""
        from datetime import timedelta
        from app.models.base import local_now
        
        start_time = local_now() - timedelta(hours=hours)
        query = db_session.query(cls).filter(
            cls.user_id == user_id,
            cls.created_at >= start_time
        )
        
        if command:
            query = query.filter(cls.command == command)
        
        return query.count()

    @classmethod
    def get_group_command_count(cls, db_session, chat_id, command=None, hours=24):
        """获取群组命令执行次数"""
        from datetime import timedelta
        from app.models.base import local_now
        
        start_time = local_now() - timedelta(hours=hours)
        query = db_session.query(cls).filter(
            cls.chat_id == chat_id,
            cls.created_at >= start_time
        )
        
        if command:
            query = query.filter(cls.command == command)
        
        return query.count()

    @classmethod
    def get_error_rate(cls, db_session, chat_id=None, user_id=None, hours=24):
        """获取错误率"""
        from datetime import timedelta
        from app.models.base import local_now
        
        start_time = local_now() - timedelta(hours=hours)
        query = db_session.query(cls).filter(cls.created_at >= start_time)
        
        if chat_id:
            query = query.filter(cls.chat_id == chat_id)
        if user_id:
            query = query.filter(cls.user_id == user_id)
        
        total_count = query.count()
        if total_count == 0:
            return 0.0
        
        error_count = query.filter(cls.status == LogStatus.ERROR).count()
        return error_count / total_count

    @classmethod
    def cleanup_old_logs(cls, db_session, days=30):
        """清理旧日志"""
        from datetime import timedelta
        from app.models.base import local_now
        
        cutoff_time = local_now() - timedelta(days=days)
        deleted_count = db_session.query(cls).filter(cls.created_at < cutoff_time).delete()
        db_session.commit()
        return deleted_count

    def mask_sensitive_data(self):
        """脱敏敏感数据"""
        if self.request_data:
            self.request_data = self._mask_dict_data(self.request_data)
        if self.response_data:
            self.response_data = self._mask_dict_data(self.response_data)

    def _mask_dict_data(self, data):
        """脱敏字典数据"""
        if not isinstance(data, dict):
            return data
        
        masked_data = data.copy()
        sensitive_keys = ['token', 'password', 'secret', 'key', 'card_number', 'phone']
        
        for key, value in masked_data.items():
            if any(sensitive_key in key.lower() for sensitive_key in sensitive_keys):
                if isinstance(value, str) and len(value) > 4:
                    masked_data[key] = value[:2] + '*' * (len(value) - 4) + value[-2:]
                else:
                    masked_data[key] = '***'
            elif isinstance(value, dict):
                masked_data[key] = self._mask_dict_data(value)
        
        return masked_data
