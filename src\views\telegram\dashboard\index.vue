<template>
  <div class="telegram-dashboard">
    <!-- 机器人状态卡片 -->
    <el-row :gutter="20" class="status-cards">
      <el-col :span="6">
        <el-card class="status-card">
          <div class="status-content">
            <div class="status-icon">
              <el-icon :size="32" :color="botStatusColor">
                <component :is="botStatusIcon" />
              </el-icon>
            </div>
            <div class="status-info">
              <div class="status-title">机器人状态</div>
              <div class="status-value" :style="{ color: botStatusColor }">
                {{ botStatusText }}
              </div>
            </div>
            <div class="status-actions">
              <el-button v-if="!telegramStore.isBotOnline" type="success" size="small" :loading="actionLoading"
                @click="handleStartBot">
                启动
              </el-button>
              <el-button v-else type="danger" size="small" :loading="actionLoading" @click="handleStopBot">
                停止
              </el-button>
              <el-button type="warning" size="small" :loading="actionLoading" @click="handleRestartBot">
                重启
              </el-button>
            </div>
          </div>
        </el-card>
      </el-col>

      <el-col :span="6">
        <el-card class="status-card">
          <div class="status-content">
            <div class="status-icon">
              <el-icon :size="32" color="#409EFF">
                <ChatDotRound />
              </el-icon>
            </div>
            <div class="status-info">
              <div class="status-title">活跃群组</div>
              <div class="status-value">{{ telegramStore.activeGroupsCount }}</div>
            </div>
          </div>
        </el-card>
      </el-col>

      <el-col :span="6">
        <el-card class="status-card">
          <div class="status-content">
            <div class="status-icon">
              <el-icon :size="32" color="#67C23A">
                <User />
              </el-icon>
            </div>
            <div class="status-info">
              <div class="status-title">活跃用户</div>
              <div class="status-value">{{ telegramStore.statistics.active_users }}</div>
            </div>
          </div>
        </el-card>
      </el-col>

      <el-col :span="6">
        <el-card class="status-card">
          <div class="status-content">
            <div class="status-icon">
              <el-icon :size="32" color="#E6A23C">
                <ChatLineRound />
              </el-icon>
            </div>
            <div class="status-info">
              <div class="status-title">今日命令</div>
              <div class="status-value">{{ telegramStore.todayCommandsCount }}</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 机器人信息 -->
    <el-row :gutter="20" class="info-section">
      <el-col :span="12">
        <el-card>
          <template #header>
            <div class="card-header">
              <span>机器人信息</span>
              <el-button type="primary" size="small" :loading="telegramStore.loading.botInfo" @click="refreshBotInfo">
                刷新
              </el-button>
            </div>
          </template>

          <div v-loading="telegramStore.loading.botInfo" class="bot-info">
            <el-descriptions :column="1" border>
              <el-descriptions-item label="机器人ID">
                {{ telegramStore.botInfo.id || '-' }}
              </el-descriptions-item>
              <el-descriptions-item label="用户名">
                @{{ telegramStore.botInfo.username || '-' }}
              </el-descriptions-item>
              <el-descriptions-item label="显示名称">
                {{ telegramStore.botInfo.first_name || '-' }}
              </el-descriptions-item>
              <el-descriptions-item label="可加入群组">
                <el-tag :type="telegramStore.botInfo.can_join_groups ? 'success' : 'danger'">
                  {{ telegramStore.botInfo.can_join_groups ? '是' : '否' }}
                </el-tag>
              </el-descriptions-item>
              <el-descriptions-item label="可读取群组消息">
                <el-tag :type="telegramStore.botInfo.can_read_all_group_messages ? 'success' : 'danger'">
                  {{ telegramStore.botInfo.can_read_all_group_messages ? '是' : '否' }}
                </el-tag>
              </el-descriptions-item>
              <el-descriptions-item label="支持内联查询">
                <el-tag :type="telegramStore.botInfo.supports_inline_queries ? 'success' : 'danger'">
                  {{ telegramStore.botInfo.supports_inline_queries ? '是' : '否' }}
                </el-tag>
              </el-descriptions-item>
            </el-descriptions>
          </div>
        </el-card>
      </el-col>

      <el-col :span="12">
        <el-card>
          <template #header>
            <div class="card-header">
              <span>统计概览</span>
              <el-button type="primary" size="small" :loading="telegramStore.loading.statistics"
                @click="refreshStatistics">
                刷新
              </el-button>
            </div>
          </template>

          <div v-loading="telegramStore.loading.statistics" class="statistics-overview">
            <el-row :gutter="16">
              <el-col :span="12">
                <div class="stat-item">
                  <div class="stat-label">总群组数</div>
                  <div class="stat-value">{{ telegramStore.statistics.total_groups }}</div>
                </div>
              </el-col>
              <el-col :span="12">
                <div class="stat-item">
                  <div class="stat-label">总用户数</div>
                  <div class="stat-value">{{ telegramStore.statistics.total_users }}</div>
                </div>
              </el-col>
              <el-col :span="12">
                <div class="stat-item">
                  <div class="stat-label">今日消息数</div>
                  <div class="stat-value">{{ telegramStore.statistics.messages_today }}</div>
                </div>
              </el-col>
              <el-col :span="12">
                <div class="stat-item">
                  <div class="stat-label">成功率</div>
                  <div class="stat-value">{{ telegramStore.successRatePercent }}%</div>
                </div>
              </el-col>
            </el-row>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 快速操作 -->
    <el-row :gutter="20" class="quick-actions">
      <el-col :span="24">
        <el-card>
          <template #header>
            <span>快速操作</span>
          </template>

          <div class="action-buttons">
            <el-button type="primary" @click="$router.push('/telegram/config')">
              <el-icon>
                <Setting />
              </el-icon>
              配置管理
            </el-button>
            <el-button type="success" @click="$router.push('/telegram/groups')">
              <el-icon>
                <ChatDotRound />
              </el-icon>
              群组管理
            </el-button>
            <el-button type="info" @click="$router.push('/telegram/users')">
              <el-icon>
                <User />
              </el-icon>
              用户管理
            </el-button>
            <el-button type="warning" @click="$router.push('/telegram/statistics')">
              <el-icon>
                <DataAnalysis />
              </el-icon>
              统计分析
            </el-button>
            <el-button @click="$router.push('/telegram/logs')">
              <el-icon>
                <Document />
              </el-icon>
              日志查看
            </el-button>
            <el-button type="danger" @click="handleClearCache">
              <el-icon>
                <Delete />
              </el-icon>
              清除缓存
            </el-button>
          </div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  ChatDotRound,
  User,
  ChatLineRound,
  Setting,
  DataAnalysis,
  Document,
  Delete,
  CircleCheck,
  CircleClose,
  Warning
} from '@element-plus/icons-vue'
import { useTelegramStore } from '@/store/modules/telegram'

const telegramStore = useTelegramStore()
const actionLoading = ref(false)

// 机器人状态相关计算属性
const botStatusIcon = computed(() => {
  if (telegramStore.isBotOnline) return CircleCheck
  if (telegramStore.botStatus.error) return Warning
  return CircleClose
})

const botStatusColor = computed(() => {
  if (telegramStore.isBotOnline) return '#67C23A'
  if (telegramStore.botStatus.error) return '#E6A23C'
  return '#F56C6C'
})

const botStatusText = computed(() => {
  if (telegramStore.isBotOnline) return '在线'
  if (telegramStore.botStatus.error) return '异常'
  return '离线'
})

// 启动机器人
const handleStartBot = async () => {
  try {
    actionLoading.value = true
    await telegramStore.startBot()
    ElMessage.success('机器人启动成功')
  } catch (error) {
    ElMessage.error('启动失败：' + error.message)
  } finally {
    actionLoading.value = false
  }
}

// 停止机器人
const handleStopBot = async () => {
  try {
    await ElMessageBox.confirm('确定要停止机器人服务吗？', '确认操作', {
      type: 'warning'
    })

    actionLoading.value = true
    await telegramStore.stopBot()
    ElMessage.success('机器人已停止')
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('停止失败：' + error.message)
    }
  } finally {
    actionLoading.value = false
  }
}

// 重启机器人
const handleRestartBot = async () => {
  try {
    await ElMessageBox.confirm('确定要重启机器人服务吗？', '确认操作', {
      type: 'warning'
    })

    actionLoading.value = true
    await telegramStore.restartBot()
    ElMessage.success('机器人重启成功')
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('重启失败：' + error.message)
    }
  } finally {
    actionLoading.value = false
  }
}

// 刷新机器人信息
const refreshBotInfo = async () => {
  try {
    await telegramStore.fetchBotInfo()
    ElMessage.success('机器人信息已刷新')
  } catch (error) {
    ElMessage.error('刷新失败：' + error.message)
  }
}

// 刷新统计数据
const refreshStatistics = async () => {
  try {
    await telegramStore.fetchStatistics()
    ElMessage.success('统计数据已刷新')
  } catch (error) {
    ElMessage.error('刷新失败：' + error.message)
  }
}

// 清除缓存
const handleClearCache = async () => {
  try {
    await ElMessageBox.confirm('确定要清除所有缓存吗？', '确认操作', {
      type: 'warning'
    })

    await telegramStore.clearCache()
    ElMessage.success('缓存已清除')
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('清除缓存失败：' + error.message)
    }
  }
}

// 初始化数据
onMounted(async () => {
  try {
    await Promise.all([
      telegramStore.fetchBotStatus(),
      telegramStore.fetchBotInfo(),
      telegramStore.fetchStatistics(),
      telegramStore.fetchGroups({ page: 1, page_size: 10 })
    ])
  } catch (error) {
    console.error('初始化数据失败:', error)
  }
})
</script>

<style scoped>
.telegram-dashboard {
  padding: 20px;
}

.status-cards {
  margin-bottom: 20px;
}

.status-card {
  height: 120px;
}

.status-content {
  display: flex;
  align-items: center;
  height: 100%;
}

.status-icon {
  margin-right: 16px;
}

.status-info {
  flex: 1;
}

.status-title {
  font-size: 14px;
  color: #666;
  margin-bottom: 8px;
}

.status-value {
  font-size: 24px;
  font-weight: bold;
}

.status-actions {
  display: flex;
  flex-direction: column;
  gap: 8px;
  align-items: flex-start;
}

.status-actions .el-button {
  width: 52px !important;
  margin: 0 !important;
  padding: 5px 12px !important;
  border-width: 1px !important;
  box-sizing: border-box !important;
  display: block !important;
}

.info-section {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: between;
  align-items: center;
}

.statistics-overview {
  padding: 10px 0;
}

.stat-item {
  text-align: center;
  padding: 16px 0;
}

.stat-label {
  font-size: 14px;
  color: #666;
  margin-bottom: 8px;
}

.stat-value {
  font-size: 20px;
  font-weight: bold;
  color: #409EFF;
}

.quick-actions {
  margin-bottom: 20px;
}

.action-buttons {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
}

.action-buttons .el-button {
  display: flex;
  align-items: center;
  gap: 6px;
}
</style>
