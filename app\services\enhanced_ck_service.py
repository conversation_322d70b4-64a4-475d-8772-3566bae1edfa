"""
增强的CK获取服务

集成部门权重算法和开关控制功能，提供统一的CK获取接口
"""

import logging
from typing import Optional, List, Dict, Any
from sqlalchemy.orm import Session

from app.models.walmart_ck import WalmartCK
from app.services.department_weight_service import DepartmentWeightService


logger = logging.getLogger(__name__)


class EnhancedCKService:
    """增强的CK获取服务"""
    
    def __init__(self, db: Session):
        self.db = db
        self.logger = logger
        self.weight_service = DepartmentWeightService(db)
    
    async def get_optimal_ck(
        self,
        merchant_id: int,
        department_id: Optional[int] = None,
        exclude_ck_ids: Optional[List[int]] = None,
        use_weight_algorithm: bool = True,
        validate_ck: bool = True
    ) -> Optional[WalmartCK]:
        """
        获取最优CK - 集成权重算法和开关控制
        
        Args:
            merchant_id: 商户ID（必须）
            department_id: 指定部门ID（可选，如果指定则直接从该部门获取）
            exclude_ck_ids: 排除的CK ID列表
            use_weight_algorithm: 是否使用权重算法（默认True）
            validate_ck: 是否验证CK有效性（默认True）
            
        Returns:
            Optional[WalmartCK]: 最优的CK对象或None
        """
        try:
            # 严格验证商户ID
            if not merchant_id:
                self.logger.error("商户ID不能为空，严格禁止跨商户CK使用")
                return None
            
            self.logger.info(
                f"开始获取最优CK: merchant_id={merchant_id}, "
                f"department_id={department_id}, use_weight={use_weight_algorithm}"
            )
            
            # 如果指定了部门ID，直接从该部门获取CK
            if department_id:
                return await self._get_ck_from_specific_department(
                    merchant_id, department_id, exclude_ck_ids, validate_ck
                )
            
            # 使用权重算法或传统算法
            if use_weight_algorithm:
                return await self._get_ck_with_weight_algorithm(
                    merchant_id, exclude_ck_ids, validate_ck
                )
            else:
                return await self._get_ck_traditional_method(
                    merchant_id, exclude_ck_ids, validate_ck
                )
                
        except Exception as e:
            self.logger.error(f"获取最优CK失败: {e}")
            return None
    
    async def _get_ck_from_specific_department(
        self,
        merchant_id: int,
        department_id: int,
        exclude_ck_ids: Optional[List[int]] = None,
        validate_ck: bool = True
    ) -> Optional[WalmartCK]:
        """从指定部门获取CK"""
        try:
            # 首先检查部门是否启用绑卡
            from app.models.department import Department
            
            department = self.db.query(Department).filter(
                Department.id == department_id,
                Department.merchant_id == merchant_id
            ).first()
            
            if not department:
                self.logger.error(f"部门 {department_id} 不存在或不属于商户 {merchant_id}")
                return None
            
            if not department.status:
                self.logger.warning(f"部门 {department.name} 已禁用")
                return None
            
            if not department.enable_binding:
                self.logger.warning(f"部门 {department.name} 已关闭进单开关")
                return None
            
            if department.binding_weight <= 0:
                self.logger.warning(f"部门 {department.name} 权重为0，不参与绑卡")
                return None
            
            # 从该部门获取可用CK
            ck = self.weight_service.get_ck_from_selected_department(
                merchant_id, department_id, exclude_ck_ids
            )
            
            if ck and validate_ck:
                # 这里可以添加CK验证逻辑
                pass
            
            return ck
            
        except Exception as e:
            self.logger.error(f"从指定部门 {department_id} 获取CK失败: {e}")
            return None
    
    async def _get_ck_with_weight_algorithm(
        self,
        merchant_id: int,
        exclude_ck_ids: Optional[List[int]] = None,
        validate_ck: bool = True
    ) -> Optional[WalmartCK]:
        """使用权重算法获取CK"""
        try:
            # 使用权重算法选择部门
            selected_department = self.weight_service.select_department_by_weight(merchant_id)
            
            if not selected_department:
                self.logger.warning(f"商户 {merchant_id} 没有可用的部门")
                return None
            
            # 从选中的部门获取CK
            ck = self.weight_service.get_ck_from_selected_department(
                merchant_id, selected_department['id'], exclude_ck_ids
            )
            
            if ck and validate_ck:
                # 这里可以添加CK验证逻辑
                pass
            
            if ck:
                self.logger.info(
                    f"权重算法成功获取CK: CK_ID={ck.id}, "
                    f"部门={selected_department['name']}, "
                    f"权重={selected_department['binding_weight']}"
                )

                # 记录CK选择日志
                try:
                    from app.services.department_operation_log_service import DepartmentOperationLogService

                    log_service = DepartmentOperationLogService(self.db)
                    log_service.log_ck_selection_with_weight(
                        merchant_id=merchant_id,
                        selected_department=selected_department,
                        selected_ck_id=ck.id,
                        weight_context={
                            "algorithm_type": "weighted_random",
                            "total_available_departments": 1,  # 这里可以传入更多上下文
                            "selection_method": "weight_algorithm"
                        }
                    )
                except Exception as log_error:
                    self.logger.warning(f"记录CK选择日志失败: {log_error}")
            
            return ck
            
        except Exception as e:
            self.logger.error(f"使用权重算法获取CK失败: {e}")
            return None
    
    async def _get_ck_traditional_method(
        self,
        merchant_id: int,
        exclude_ck_ids: Optional[List[int]] = None,
        validate_ck: bool = True
    ) -> Optional[WalmartCK]:
        """使用传统方法获取CK（负载均衡）"""
        try:
            from app.models.department import Department
            
            # 构建查询，包含部门开关过滤
            query = self.db.query(WalmartCK).join(
                Department, WalmartCK.department_id == Department.id
            ).filter(
                WalmartCK.merchant_id == merchant_id,
                WalmartCK.active == True,
                WalmartCK.bind_count < WalmartCK.total_limit,
                WalmartCK.is_deleted == False,
                # 部门开关过滤
                Department.status == True,
                Department.enable_binding == True,
                Department.binding_weight > 0
            )
            
            if exclude_ck_ids:
                query = query.filter(~WalmartCK.id.in_(exclude_ck_ids))
            
            # 按使用次数升序排列，选择负载最低的CK
            ck = query.order_by(WalmartCK.bind_count.asc()).first()
            
            if ck and validate_ck:
                # 这里可以添加CK验证逻辑
                pass
            
            if ck:
                self.logger.info(f"传统方法获取CK: CK_ID={ck.id}, 使用次数={ck.bind_count}")
            
            return ck
            
        except Exception as e:
            self.logger.error(f"使用传统方法获取CK失败: {e}")
            return None
    
    def get_merchant_ck_status(self, merchant_id: int) -> Dict[str, Any]:
        """
        获取商户CK状态概览
        
        Args:
            merchant_id: 商户ID
            
        Returns:
            Dict: CK状态概览
        """
        try:
            # 获取权重分配统计
            weight_stats = self.weight_service.get_weight_distribution_stats(merchant_id)
            
            # 获取权重配置验证结果
            validation_result = self.weight_service.validate_weight_configuration(merchant_id)
            
            # 统计总CK信息
            from app.models.department import Department
            
            total_cks = self.db.query(WalmartCK).join(
                Department, WalmartCK.department_id == Department.id
            ).filter(
                WalmartCK.merchant_id == merchant_id,
                WalmartCK.is_deleted == False
            ).count()
            
            active_cks = self.db.query(WalmartCK).join(
                Department, WalmartCK.department_id == Department.id
            ).filter(
                WalmartCK.merchant_id == merchant_id,
                WalmartCK.active == True,
                WalmartCK.is_deleted == False,
                Department.status == True,
                Department.enable_binding == True,
                Department.binding_weight > 0
            ).count()
            
            available_cks = self.db.query(WalmartCK).join(
                Department, WalmartCK.department_id == Department.id
            ).filter(
                WalmartCK.merchant_id == merchant_id,
                WalmartCK.active == True,
                (
                    (WalmartCK.total_limit.is_(None)) |
                    (WalmartCK.bind_count < WalmartCK.total_limit)
                ),
                WalmartCK.is_deleted == False,
                Department.status == True,
                Department.enable_binding == True,
                Department.binding_weight > 0
            ).count()
            
            return {
                'merchant_id': merchant_id,
                'total_cks': total_cks,
                'active_cks': active_cks,
                'available_cks': available_cks,
                'weight_stats': weight_stats,
                'validation_result': validation_result,
                'can_bind_cards': available_cks > 0 and validation_result['is_valid']
            }
            
        except Exception as e:
            self.logger.error(f"获取商户CK状态失败: {e}")
            return {
                'merchant_id': merchant_id,
                'total_cks': 0,
                'active_cks': 0,
                'available_cks': 0,
                'weight_stats': {'total_departments': 0, 'total_weight': 0, 'departments': []},
                'validation_result': {'is_valid': False, 'issues': ['获取状态失败'], 'warnings': []},
                'can_bind_cards': False
            }
    
    async def test_weight_algorithm(
        self, 
        merchant_id: int, 
        test_count: int = 100
    ) -> Dict[str, Any]:
        """
        测试权重算法的分配效果
        
        Args:
            merchant_id: 商户ID
            test_count: 测试次数
            
        Returns:
            Dict: 测试结果统计
        """
        try:
            self.logger.info(f"开始测试权重算法，商户ID: {merchant_id}, 测试次数: {test_count}")
            department_selection_count = {}

            for i in range(test_count):
                selected_dept = self.weight_service.select_department_by_weight(merchant_id)
                if i == 0:  # 只在第一次记录调试信息
                    self.logger.info(f"第一次选择结果: {selected_dept}")
                if selected_dept:
                    dept_id = selected_dept['id']
                    dept_name = selected_dept['name']
                    if dept_id not in department_selection_count:
                        department_selection_count[dept_id] = {
                            'name': dept_name,
                            'weight': selected_dept['binding_weight'],
                            'count': 0
                        }
                    department_selection_count[dept_id]['count'] += 1
            
            # 计算实际分配比例
            for dept_id, info in department_selection_count.items():
                info['actual_percentage'] = round((info['count'] / test_count) * 100, 2)
            
            # 获取理论权重分配
            weight_stats = self.weight_service.get_weight_distribution_stats(merchant_id)
            theoretical_distribution = {
                dept['id']: dept['weight_percentage'] 
                for dept in weight_stats['departments']
            }
            
            return {
                'test_count': test_count,
                'department_selection_count': department_selection_count,
                'theoretical_distribution': theoretical_distribution,
                'total_departments': len(department_selection_count)
            }
            
        except Exception as e:
            self.logger.error(f"测试权重算法失败: {e}")
            return {
                'test_count': 0,
                'department_selection_count': {},
                'theoretical_distribution': {},
                'total_departments': 0
            }
