from typing import Any, Dict, Optional, Type, Union
from fastapi import FastAPI, Request, status
from fastapi.exceptions import RequestValidationError
from fastapi.responses import JSONResponse
from pydantic import ValidationError
from starlette.exceptions import HTTPException as StarletteHTTPException

from app.schemas.response import error_response


# 自定义业务异常
class BusinessException(Exception):
    """
    业务异常类
    用于处理业务逻辑中的错误情况
    """

    def __init__(
        self,
        message: str = "业务处理异常",
        code: int = 10000,
        data: Optional[Any] = None,
        status_code: int = status.HTTP_400_BAD_REQUEST,
    ):
        self.message = message
        self.code = code
        self.data = data
        self.status_code = status_code
        super().__init__(self.message)


# 业务错误码定义
class ErrorCode:
    """业务错误码定义"""

    # 通用错误 (1-999)
    SYSTEM_ERROR = 1
    PARAMS_ERROR = 2
    UNAUTHORIZED = 3
    FORBIDDEN = 4
    NOT_FOUND = 5
    METHOD_NOT_ALLOWED = 6
    TIMEOUT = 7
    TOO_MANY_REQUESTS = 8
    BUSINESS_ERROR = 9

    # 认证相关错误 (1000-1999)
    INVALID_TOKEN = 1000
    TOKEN_EXPIRED = 1001
    INVALID_CREDENTIALS = 1002
    USER_NOT_FOUND = 1003
    USER_INACTIVE = 1004
    PASSWORD_EXPIRED = 1005

    # 商户相关错误 (2000-2999)
    MERCHANT_NOT_FOUND = 2000
    MERCHANT_INACTIVE = 2001
    MERCHANT_API_KEY_INVALID = 2002
    MERCHANT_API_RATE_LIMIT = 2003
    MERCHANT_IP_RESTRICTED = 2004
    MERCHANT_CODE_EXISTS = 2005

    # 绑卡相关错误 (3000-3999)
    CARD_NOT_FOUND = 3000
    CARD_EXPIRED = 3001
    CARD_INVALID = 3002
    CARD_BIND_LIMIT = 3003
    CARD_ALREADY_BOUND = 3004


# 异常处理器辅助函数
def _create_business_exception_handler():
    """创建业务异常处理器"""
    async def business_exception_handler(
        request: Request, exc: BusinessException
    ) -> JSONResponse:
        """业务异常处理器"""
        return JSONResponse(
            status_code=exc.status_code,
            content=error_response(
                code=exc.code,
                message=exc.message,
                data=exc.data,
                http_status_code=exc.status_code,
            ),
        )
    return business_exception_handler


def _create_validation_exception_handler():
    """创建验证异常处理器"""
    async def validation_exception_handler(
        request: Request, exc: ValidationError
    ) -> JSONResponse:
        """验证错误处理器"""
        return JSONResponse(
            status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
            content=error_response(
                code=ErrorCode.PARAMS_ERROR,
                message="数据验证错误",
                data=exc.errors(),
                http_status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
            ),
        )
    return validation_exception_handler


def _create_request_validation_exception_handler():
    """创建请求验证异常处理器"""
    async def request_validation_exception_handler(
        request: Request, exc: RequestValidationError
    ) -> JSONResponse:
        """请求验证错误处理器"""
        return JSONResponse(
            status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
            content=error_response(
                code=ErrorCode.PARAMS_ERROR,
                message="请求参数错误",
                data=exc.errors(),
                http_status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
            ),
        )
    return request_validation_exception_handler


def _get_http_error_code_mapping():
    """获取HTTP错误码映射"""
    return {
        status.HTTP_401_UNAUTHORIZED: ErrorCode.UNAUTHORIZED,
        status.HTTP_403_FORBIDDEN: ErrorCode.FORBIDDEN,
        status.HTTP_404_NOT_FOUND: ErrorCode.NOT_FOUND,
        status.HTTP_405_METHOD_NOT_ALLOWED: ErrorCode.METHOD_NOT_ALLOWED,
        status.HTTP_429_TOO_MANY_REQUESTS: ErrorCode.TOO_MANY_REQUESTS,
    }


def _create_http_exception_handler():
    """创建HTTP异常处理器"""
    async def http_exception_handler(
        request: Request, exc: StarletteHTTPException
    ) -> JSONResponse:
        """HTTP异常处理器"""
        error_code_map = _get_http_error_code_mapping()
        error_code = error_code_map.get(exc.status_code, ErrorCode.SYSTEM_ERROR)

        return JSONResponse(
            status_code=exc.status_code,
            content=error_response(
                code=error_code, message=exc.detail, http_status_code=exc.status_code
            ),
        )
    return http_exception_handler


def _log_exception_details(request: Request, exc: Exception):
    """记录异常详细信息"""
    import traceback
    import logging

    # 获取应用logger
    logger = logging.getLogger("app.api")

    # 记录详细的异常信息
    error_msg = f"未捕获的异常: {str(exc)}"
    request_details = f"URL: {request.url.path}, Method: {request.method}"

    # 获取请求头并移除敏感信息
    headers = dict(request.headers)
    if "authorization" in headers:
        headers["authorization"] = "********"

    logger.error(
        f"{error_msg}\n"
        f"请求信息: {request_details}\n"
        f"请求头: {headers}\n"
        f"异常详情: {exc.__class__.__name__}"
    )

    # 记录完整的异常堆栈
    logger.error(
        "".join(traceback.format_exception(type(exc), exc, exc.__traceback__))
    )

    # 控制台也打印错误（开发环境有用）
    traceback.print_exc()


def _create_general_exception_handler():
    """创建通用异常处理器"""
    async def general_exception_handler(
        request: Request, exc: Exception
    ) -> JSONResponse:
        """通用异常处理器"""
        # 记录异常详情
        _log_exception_details(request, exc)

        return JSONResponse(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            content=error_response(
                code=ErrorCode.SYSTEM_ERROR,
                message="系统内部错误",
                http_status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            ),
        )
    return general_exception_handler


def setup_exception_handlers(app: FastAPI) -> None:
    """
    设置全局异常处理器

    Args:
        app: FastAPI应用实例
    """
    # 注册各种异常处理器
    app.add_exception_handler(BusinessException, _create_business_exception_handler())
    app.add_exception_handler(ValidationError, _create_validation_exception_handler())
    app.add_exception_handler(RequestValidationError, _create_request_validation_exception_handler())
    app.add_exception_handler(StarletteHTTPException, _create_http_exception_handler())
    app.add_exception_handler(Exception, _create_general_exception_handler())
