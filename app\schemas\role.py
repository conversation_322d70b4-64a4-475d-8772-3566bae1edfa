from typing import List, Optional
from pydantic import BaseModel, Field
from datetime import datetime


class RoleBase(BaseModel):
    """角色基础模型"""

    name: str = Field(..., description="角色名称", max_length=50)
    code: str = Field(..., description="角色代码", max_length=50)
    description: Optional[str] = Field(None, description="角色描述")
    is_enabled: bool = Field(True, description="是否启用")
    is_system: bool = Field(False, description="是否系统内置角色")
    sort_order: int = Field(0, description="排序号")
    remark: Optional[str] = Field(None, description="备注")


class RoleCreate(RoleBase):
    """创建角色请求模型"""

    permission_ids: Optional[List[int]] = Field(None, description="权限ID列表")
    menu_ids: Optional[List[int]] = Field(None, description="菜单ID列表")


class RoleUpdate(BaseModel):
    """更新角色请求模型"""

    name: Optional[str] = Field(None, description="角色名称", max_length=50)
    code: Optional[str] = Field(None, description="角色代码", max_length=50)
    description: Optional[str] = Field(None, description="角色描述")
    is_enabled: Optional[bool] = Field(None, description="是否启用")
    is_system: Optional[bool] = Field(None, description="是否系统内置角色")
    sort_order: Optional[int] = Field(None, description="排序号")
    remark: Optional[str] = Field(None, description="备注")
    permission_ids: Optional[List[int]] = Field(None, description="权限ID列表")
    menu_ids: Optional[List[int]] = Field(None, description="菜单ID列表")


class RoleInDB(RoleBase):
    """数据库角色模型"""

    id: int
    created_by: Optional[int] = None
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


class RoleResponse(RoleInDB):
    """角色响应模型"""

    permissions: Optional[List["PermissionResponse"]] = None
    menus: Optional[List["MenuResponse"]] = None
    user_count: Optional[int] = None


class RolePermissions(BaseModel):
    """角色权限分配模型"""

    role_id: int = Field(..., description="角色ID")
    permission_ids: List[int] = Field(..., description="权限ID列表")


class RoleMenus(BaseModel):
    """角色菜单分配模型"""

    role_id: int = Field(..., description="角色ID")
    menu_ids: List[int] = Field(..., description="菜单ID列表")


class RoleUsers(BaseModel):
    """角色用户分配模型"""

    role_id: int = Field(..., description="角色ID")
    user_ids: List[int] = Field(..., description="用户ID列表")


class RoleQuery(BaseModel):
    """角色查询模型"""

    name: Optional[str] = Field(None, description="角色名称（模糊查询）")
    code: Optional[str] = Field(None, description="角色代码（模糊查询）")
    is_enabled: Optional[bool] = Field(None, description="是否启用")
    is_system: Optional[bool] = Field(None, description="是否系统角色")
    include_permissions: bool = Field(False, description="是否包含权限信息")
    include_menus: bool = Field(False, description="是否包含菜单信息")


# 避免循环导入，在文件末尾定义
from app.schemas.permission import PermissionResponse
from app.schemas.menu import MenuResponse

RoleResponse.model_rebuild()
