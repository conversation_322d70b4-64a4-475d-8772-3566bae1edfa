FROM docker.1ms.run/python:3.12.10-slim
WORKDIR /app

# 设置apt源为阿里云源
RUN echo "deb https://mirrors.aliyun.com/debian/ bullseye main contrib non-free" > /etc/apt/sources.list
RUN echo "deb https://mirrors.aliyun.com/debian/ bullseye-updates main contrib non-free" >> /etc/apt/sources.list

# 安装系统依赖
RUN apt-get update && \
    apt-get install -y --no-install-recommends \
    gcc \
    libc6-dev \
    libffi-dev \
    libssl-dev \
    make \
    upx \
    && rm -rf /var/lib/apt/lists/*

# 复制并安装Python依赖
COPY requirements.txt .
RUN pip install --no-cache-dir -i https://mirrors.aliyun.com/pypi/simple/ --trusted-host mirrors.aliyun.com -r requirements.txt

# 显式安装cryptography和aio-pika
RUN pip install -i https://mirrors.aliyun.com/pypi/simple/ --trusted-host mirrors.aliyun.com cryptography aio-pika

# 安装PyInstaller
RUN pip install -i https://mirrors.aliyun.com/pypi/simple/ --trusted-host mirrors.aliyun.com pyinstaller

# 复制应用程序代码
COPY . .

# 检查目录结构
RUN echo "项目目录结构:" && ls -la
RUN echo "app目录内容:" && ls -la app
RUN echo "config.yaml是否存在:" && ls -la config.yaml || echo "config.yaml不存在"

# 使用PyInstaller打包
RUN echo "开始执行PyInstaller"
RUN pyinstaller --noconfirm --clean --onefile \
    --name walmart-bind-card-server-linux \
    --add-data "config.yaml:." \
    --upx-dir=/usr/bin/ \
    --hidden-import=passlib.handlers.bcrypt \
    --hidden-import=cryptography \
    --hidden-import=cryptography.hazmat.backends.openssl \
    --hidden-import=cryptography.fernet \
    --hidden-import=aio_pika \
    --hidden-import=aio_pika.abc \
    --hidden-import=aio_pika.pool \
    --hidden-import=aio_pika.message \
    --hidden-import=aio_pika.exchange \
    --hidden-import=aio_pika.channel \
    --hidden-import=aio_pika.robust \
    --hidden-import=aiormq \
    --hidden-import=aiomysql \
    --hidden-import=pamqp \
    --hidden-import=yarl \
    --hidden-import=multidict \
    --hidden-import=uvicorn \
    --hidden-import=uvicorn.main \
    --hidden-import=uvicorn.lifespan \
    --hidden-import=uvicorn.lifespan.on \
    --hidden-import=uvicorn.lifespan.off \
    --hidden-import=uvicorn.protocols \
    --hidden-import=uvicorn.protocols.http \
    --hidden-import=uvicorn.protocols.http.auto \
    --hidden-import=uvicorn.protocols.websockets \
    --hidden-import=uvicorn.protocols.websockets.auto \
    --hidden-import=uvicorn.logging \
    --collect-all aio_pika \
    --collect-all uvicorn \
    app/main.py

# 检查打包结果
RUN ls -la /app/dist/
