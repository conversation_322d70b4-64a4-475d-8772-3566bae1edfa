#!/usr/bin/env python3
"""
Telegram Webhook 清理工具
用于解决 "Conflict: terminated by other getUpdates request" 错误
"""

import asyncio
import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from app.core.logging import get_logger
from app.telegram_bot.config import get_bot_config
from telegram.ext import Application

logger = get_logger(__name__)


async def clear_webhook():
    """清理Telegram webhook设置"""
    try:
        logger.info("正在清理Telegram webhook设置...")
        
        # 获取配置
        config = get_bot_config()
        if not config.bot_token:
            logger.error("Bot token 未配置，无法清理webhook")
            return False
        
        # 创建临时应用实例
        application = Application.builder().token(config.bot_token).build()
        
        # 初始化应用
        await application.initialize()
        
        try:
            # 删除webhook
            logger.info("正在删除webhook...")
            await application.bot.delete_webhook(drop_pending_updates=True)
            logger.info("✅ Webhook已成功删除")
            
            # 获取当前webhook信息
            webhook_info = await application.bot.get_webhook_info()
            if webhook_info.url:
                logger.warning(f"⚠️  Webhook仍然存在: {webhook_info.url}")
                return False
            else:
                logger.info("✅ 确认webhook已完全清除")
                
        except Exception as e:
            logger.error(f"删除webhook时出错: {e}")
            return False
        
        finally:
            # 关闭应用
            await application.shutdown()
        
        return True
        
    except Exception as e:
        logger.error(f"清理webhook失败: {e}")
        return False


async def get_webhook_info():
    """获取当前webhook信息"""
    try:
        logger.info("正在获取webhook信息...")
        
        # 获取配置
        config = get_bot_config()
        if not config.bot_token:
            logger.error("Bot token 未配置")
            return None
        
        # 创建临时应用实例
        application = Application.builder().token(config.bot_token).build()
        
        # 初始化应用
        await application.initialize()
        
        try:
            # 获取webhook信息
            webhook_info = await application.bot.get_webhook_info()
            
            info = {
                "url": webhook_info.url,
                "has_custom_certificate": webhook_info.has_custom_certificate,
                "pending_update_count": webhook_info.pending_update_count,
                "last_error_date": webhook_info.last_error_date,
                "last_error_message": webhook_info.last_error_message,
                "max_connections": webhook_info.max_connections,
                "allowed_updates": webhook_info.allowed_updates
            }
            
            return info
            
        finally:
            # 关闭应用
            await application.shutdown()
        
    except Exception as e:
        logger.error(f"获取webhook信息失败: {e}")
        return None


async def test_bot_connection():
    """测试机器人连接"""
    try:
        logger.info("正在测试机器人连接...")
        
        # 获取配置
        config = get_bot_config()
        if not config.bot_token:
            logger.error("Bot token 未配置")
            return False
        
        # 创建临时应用实例
        application = Application.builder().token(config.bot_token).build()
        
        # 初始化应用
        await application.initialize()
        
        try:
            # 获取机器人信息
            bot_info = await application.bot.get_me()
            logger.info(f"✅ 机器人连接正常: @{bot_info.username} ({bot_info.first_name})")
            return True
            
        finally:
            # 关闭应用
            await application.shutdown()
        
    except Exception as e:
        logger.error(f"机器人连接测试失败: {e}")
        return False


async def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description="Telegram Webhook 清理工具")
    parser.add_argument("--info", action="store_true", help="显示当前webhook信息")
    parser.add_argument("--clear", action="store_true", help="清理webhook设置")
    parser.add_argument("--test", action="store_true", help="测试机器人连接")
    parser.add_argument("--all", action="store_true", help="执行所有操作")
    
    args = parser.parse_args()
    
    if not any([args.info, args.clear, args.test, args.all]):
        parser.print_help()
        return
    
    logger.info("Telegram Webhook 清理工具启动")
    
    # 测试连接
    if args.test or args.all:
        logger.info("=" * 50)
        logger.info("测试机器人连接")
        logger.info("=" * 50)
        connection_ok = await test_bot_connection()
        if not connection_ok:
            logger.error("机器人连接失败，请检查token配置")
            return
    
    # 显示webhook信息
    if args.info or args.all:
        logger.info("=" * 50)
        logger.info("当前Webhook信息")
        logger.info("=" * 50)
        webhook_info = await get_webhook_info()
        if webhook_info:
            if webhook_info["url"]:
                logger.info(f"Webhook URL: {webhook_info['url']}")
                logger.info(f"待处理更新数: {webhook_info['pending_update_count']}")
                logger.info(f"最大连接数: {webhook_info['max_connections']}")
                if webhook_info["last_error_message"]:
                    logger.warning(f"最后错误: {webhook_info['last_error_message']}")
            else:
                logger.info("✅ 当前没有设置webhook")
    
    # 清理webhook
    if args.clear or args.all:
        logger.info("=" * 50)
        logger.info("清理Webhook设置")
        logger.info("=" * 50)
        success = await clear_webhook()
        if success:
            logger.info("✅ Webhook清理完成")
        else:
            logger.error("❌ Webhook清理失败")
    
    logger.info("操作完成")


if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        logger.info("操作被用户中断")
    except Exception as e:
        logger.error(f"程序执行失败: {e}")
        sys.exit(1)
