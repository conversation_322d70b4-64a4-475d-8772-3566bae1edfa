#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
沃尔玛绑卡系统登录页面TOTP状态管理测试

测试场景：
1. 用户输入启用了双因子认证的账号，页面显示三个输入框
2. 用户切换到未启用双因子认证的账号，页面应显示两个输入框
3. 验证状态切换的正确性和用户体验
"""

import asyncio
import time
from playwright.async_api import async_playwright, expect

class LoginTotpStateTest:
    def __init__(self):
        self.base_url = "http://localhost:20000"
        self.browser = None
        self.page = None
        
    async def setup(self):
        """初始化浏览器和页面"""
        playwright = await async_playwright().start()
        self.browser = await playwright.chromium.launch(headless=False, slow_mo=1000)
        self.page = await self.browser.new_page()
        
        # 设置视口大小
        await self.page.set_viewport_size({"width": 1280, "height": 720})
        
    async def teardown(self):
        """清理资源"""
        if self.browser:
            await self.browser.close()
            
    async def navigate_to_login(self):
        """导航到登录页面"""
        print("📍 导航到登录页面...")
        await self.page.goto(f"{self.base_url}/#/login")
        await self.page.wait_for_load_state('networkidle')
        
        # 等待登录表单加载
        await self.page.wait_for_selector('form.login-form')
        print("✅ 登录页面加载完成")
        
    async def test_totp_enabled_user_state(self):
        """测试启用TOTP用户的状态显示"""
        print("\n🧪 测试场景1: 测试TOTP状态检查功能")

        # 首先测试一个不存在的用户名，应该不显示TOTP输入框
        username_input = self.page.locator('input[placeholder="请输入用户名"]')
        await username_input.fill('nonexistent_user')

        # 触发失焦事件
        await username_input.blur()

        # 等待TOTP状态检查完成
        print("⏳ 等待TOTP状态检查...")
        await self.page.wait_for_timeout(3000)

        # 检查TOTP输入框不应该显示
        totp_input = self.page.locator('input[placeholder="请输入6位验证码"]')

        try:
            await expect(totp_input).to_be_hidden(timeout=2000)
            print("✅ 不存在用户不显示TOTP输入框")
        except Exception as e:
            print(f"⚠️  不存在用户的TOTP状态检查: {e}")

        # 现在测试admin用户（根据数据库默认情况，应该未启用TOTP）
        await username_input.clear()
        await username_input.fill('admin')
        await username_input.blur()
        await self.page.wait_for_timeout(3000)

        try:
            await expect(totp_input).to_be_hidden(timeout=2000)
            print("✅ admin用户（未启用TOTP）不显示TOTP输入框")
            return True
        except Exception as e:
            print(f"❌ admin用户TOTP状态检查异常: {e}")
            return False
            
    async def test_totp_disabled_user_state(self):
        """测试未启用TOTP用户的状态显示"""
        print("\n🧪 测试场景2: 切换到未启用TOTP的用户名")
        
        # 清空用户名输入框
        username_input = self.page.locator('input[placeholder="请输入用户名"]')
        await username_input.clear()
        
        # 输入未启用TOTP的用户名（假设test1未启用TOTP）
        await username_input.fill('test1')
        
        # 触发失焦事件
        await username_input.blur()
        
        # 等待TOTP状态检查完成
        print("⏳ 等待TOTP状态检查...")
        await self.page.wait_for_timeout(2000)
        
        # 检查TOTP输入框是否被隐藏
        totp_input = self.page.locator('input[placeholder="请输入6位验证码"]')
        
        try:
            await expect(totp_input).to_be_hidden(timeout=5000)
            print("✅ TOTP输入框正确隐藏")
            return True
        except Exception as e:
            print(f"❌ TOTP输入框仍然显示: {e}")
            return False
            
    async def test_state_switching(self):
        """测试状态切换的完整流程"""
        print("\n🧪 测试场景3: 完整的状态切换流程")
        
        username_input = self.page.locator('input[placeholder="请输入用户名"]')
        totp_input = self.page.locator('input[placeholder="请输入6位验证码"]')
        
        # 步骤1: 输入启用TOTP的用户名
        print("📝 步骤1: 输入启用TOTP的用户名 (admin)")
        await username_input.clear()
        await username_input.fill('admin')
        await username_input.blur()
        await self.page.wait_for_timeout(2000)
        
        # 验证TOTP输入框显示
        try:
            await expect(totp_input).to_be_visible(timeout=5000)
            print("✅ TOTP输入框正确显示")
        except:
            print("❌ TOTP输入框未显示")
            return False
            
        # 步骤2: 切换到未启用TOTP的用户名
        print("📝 步骤2: 切换到未启用TOTP的用户名 (test1)")
        await username_input.clear()
        await username_input.fill('test1')
        await username_input.blur()
        await self.page.wait_for_timeout(2000)
        
        # 验证TOTP输入框隐藏
        try:
            await expect(totp_input).to_be_hidden(timeout=5000)
            print("✅ TOTP输入框正确隐藏")
        except:
            print("❌ TOTP输入框仍然显示")
            return False
            
        # 步骤3: 再次切换回启用TOTP的用户名
        print("📝 步骤3: 再次切换回启用TOTP的用户名 (admin)")
        await username_input.clear()
        await username_input.fill('admin')
        await username_input.blur()
        await self.page.wait_for_timeout(2000)
        
        # 验证TOTP输入框再次显示
        try:
            await expect(totp_input).to_be_visible(timeout=5000)
            print("✅ TOTP输入框再次正确显示")
            return True
        except:
            print("❌ TOTP输入框未再次显示")
            return False
            
    async def test_clear_button_behavior(self):
        """测试清空按钮的行为"""
        print("\n🧪 测试场景4: 清空按钮行为")
        
        username_input = self.page.locator('input[placeholder="请输入用户名"]')
        totp_input = self.page.locator('input[placeholder="请输入6位验证码"]')
        
        # 输入启用TOTP的用户名
        await username_input.fill('admin')
        await username_input.blur()
        await self.page.wait_for_timeout(2000)
        
        # 验证TOTP输入框显示
        try:
            await expect(totp_input).to_be_visible(timeout=5000)
            print("✅ TOTP输入框显示")
        except:
            print("❌ TOTP输入框未显示")
            return False
            
        # 点击清空按钮
        clear_button = username_input.locator('..').locator('.el-input__suffix .el-input__clear')
        await clear_button.click()
        
        # 验证TOTP输入框隐藏
        try:
            await expect(totp_input).to_be_hidden(timeout=5000)
            print("✅ 清空用户名后TOTP输入框正确隐藏")
            return True
        except:
            print("❌ 清空用户名后TOTP输入框仍然显示")
            return False
            
    async def run_all_tests(self):
        """运行所有测试"""
        print("🚀 开始沃尔玛绑卡系统登录页面TOTP状态管理测试")
        print("=" * 60)
        
        try:
            await self.setup()
            await self.navigate_to_login()
            
            # 运行测试场景
            test_results = []
            
            # 测试1: TOTP启用用户状态
            result1 = await self.test_totp_enabled_user_state()
            test_results.append(("TOTP启用用户状态", result1))
            
            # 测试2: TOTP未启用用户状态
            result2 = await self.test_totp_disabled_user_state()
            test_results.append(("TOTP未启用用户状态", result2))
            
            # 测试3: 状态切换流程
            result3 = await self.test_state_switching()
            test_results.append(("状态切换流程", result3))
            
            # 测试4: 清空按钮行为
            result4 = await self.test_clear_button_behavior()
            test_results.append(("清空按钮行为", result4))
            
            # 输出测试结果
            print("\n" + "=" * 60)
            print("📊 测试结果汇总:")
            print("=" * 60)
            
            passed = 0
            total = len(test_results)
            
            for test_name, result in test_results:
                status = "✅ 通过" if result else "❌ 失败"
                print(f"{test_name}: {status}")
                if result:
                    passed += 1
                    
            print(f"\n总计: {passed}/{total} 个测试通过")
            
            if passed == total:
                print("🎉 所有测试通过！TOTP状态管理修复成功！")
            else:
                print("⚠️  部分测试失败，需要进一步检查")
                
        except Exception as e:
            print(f"❌ 测试执行失败: {e}")
        finally:
            await self.teardown()

async def main():
    """主函数"""
    test = LoginTotpStateTest()
    await test.run_all_tests()

if __name__ == "__main__":
    asyncio.run(main())
