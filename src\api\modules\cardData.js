import { http } from '@/api/request'
import { API_URLS, replaceUrlParams } from './config'

const { CARD_DATA } = API_URLS

/**
 * 卡数据相关API
 */
export const cardDataApi = {
    // 获取卡数据列表
    getList(params = {}) {
        return http.get(CARD_DATA.LIST, { params }).then(res => {
            // 统一处理响应格式，后端返回 {success: true, data: {...}, message: "..."}
            return res.data || res
        }).catch(err => {
            return {
                data: [],
                total: 0
            }
        })
    },

    /**
     * 获取卡记录的敏感信息
     * @param {number} cardId 卡记录 ID
     * @returns {Promise<object>} API 响应数据
     */
    getSensitiveInfo(cardId) {
        // 注意：直接拼接 URL，因为 config.js 中没有专门为此定义的 key
        const url = `${CARD_DATA.LIST}/${cardId}/sensitive`
        return http.get(url).then(res => res.data || res)
    },

    // 获取卡数据详情
    getDetail(id) {
        const url = replaceUrlParams(CARD_DATA.DETAIL, { id })
        return http.get(url).then(res => res.data || res)
    },

    // 获取卡数据统计
    getStatistics(params = {}) {
        return http.get(CARD_DATA.STATISTICS, { params }).then(res => res.data || res)
    },

    // 获取今日统计
    getTodayStatistics(params = {}) {
        return http.get(CARD_DATA.TODAY_STATISTICS, { params }).then(res => res.data)
    },

    // 获取历史记录
    getHistory(params = {}) {
        return http.get(CARD_DATA.HISTORY, { params }).then(res => res.data)
    },

    // 导出卡数据
    exportData(params = {}) {
        // 导出通常需要特殊处理，例如设置 responseType 为 blob
        return http.get(CARD_DATA.EXPORT, { params, responseType: 'blob' })
    },

    // 重试单张卡
    retry(id) {
        const url = replaceUrlParams(CARD_DATA.RETRY, { id })
        return http.post(url).then(res => res.data)
    },

    // 批量重试卡
    batchRetry(ids) {
        return http.post(CARD_DATA.BATCH_RETRY, { ids }).then(res => res.data)
    },

    // 同步单张卡金额
    syncAmount(id, forceUpdate = false) {
        const url = `${CARD_DATA.LIST}/${id}/sync-amount`
        return http.post(url, null, { params: { force_update: forceUpdate } }).then(res => res.data)
    },

    // 批量同步卡金额
    batchSyncAmount(cardIds, forceUpdate = false) {
        const url = `${CARD_DATA.LIST}/batch/sync-amount`
        return http.post(url, { card_ids: cardIds, force_update: forceUpdate }).then(res => res.data)
    },

    // 手动触发单张卡回调
    triggerCallback(id, forceCallback = false) {
        const url = `${CARD_DATA.LIST}/${id}/trigger-callback`
        return http.post(url, null, { params: { force_callback: forceCallback } }).then(res => res.data)
    },

    // 批量触发回调
    batchTriggerCallback(cardIds, forceCallback = false) {
        const url = `${CARD_DATA.LIST}/batch/trigger-callback`
        return http.post(url, { card_ids: cardIds, force_callback: forceCallback }).then(res => res.data)
    },

    // 绑卡接口
    bindCard(data, config) {
        return http.post(CARD_DATA.BIND, data, config).then(res => res.data)
    },

    // 失败原因分析 (接口)
    getFailureAnalysis(params = {}) {
        // 接口路径为 /cards/failure-analysis
        return http.get('/cards/failure-analysis', { params }).then(res => res.data);
    },

    // 成功率按时间 (接口)
    getSuccessRateByTime(params = {}) {
        // 接口路径为 /cards/success-rate-by-time
        return http.get('/cards/success-rate-by-time', { params }).then(res => res.data);
    }
}

export default cardDataApi 