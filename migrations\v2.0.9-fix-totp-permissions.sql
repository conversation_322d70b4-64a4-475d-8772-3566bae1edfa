-- =====================================================
-- 沃尔玛绑卡系统TOTP权限修复脚本（生产环境安全版本）
-- 
-- 问题：商户管理员和CK供应商无法访问TOTP功能
-- 原因：TOTP权限只分配给了超级管理员
-- 解决：为所有角色分配TOTP相关权限
-- 
-- 安全机制：
-- 1. 检查数据库初始化状态，避免在生产环境重复执行
-- 2. 使用条件执行，只在首次初始化或明确需要修复时执行
-- 3. 添加执行标记，防止重复执行
-- =====================================================

-- 设置连接字符集
SET NAMES utf8mb4 COLLATE utf8mb4_unicode_ci;
SET time_zone = '+08:00';

USE `walmart_card_db`;

-- 创建迁移日志表（如果不存在）
CREATE TABLE IF NOT EXISTS `migration_logs` (
    `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '日志ID',
    `migration_name` varchar(100) NOT NULL COMMENT '迁移名称',
    `status` varchar(20) NOT NULL DEFAULT 'started' COMMENT '状态',
    `message` text NULL COMMENT '消息',
    `data_summary` text NULL COMMENT '数据摘要',
    `created_at` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '创建时间',
    `completed_at` datetime(3) NULL COMMENT '完成时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_migration_logs_name` (`migration_name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='迁移日志表';

-- 检查是否已经执行过此修复脚本
SET @totp_permissions_fix_exists = (SELECT COUNT(*) FROM `migration_logs` WHERE `migration_name` = 'totp_permissions_fix_v2.1.0');

-- 只在未执行过的情况下进行TOTP权限修复
SET @should_fix_totp = (@totp_permissions_fix_exists = 0);

-- 记录修复开始
INSERT IGNORE INTO `migration_logs` (`migration_name`, `status`, `message`, `created_at`) 
VALUES ('totp_permissions_fix_v2.1.0', 'started', '开始修复TOTP权限配置', NOW(3));

-- 1. 检查当前TOTP权限分配情况（仅在执行修复时显示）
SET @sql_check_current = IF(@should_fix_totp = 1, 
    'SELECT ''=== 当前TOTP权限分配情况 ==='' as info; SELECT r.name as role_name, r.code as role_code, COUNT(p.id) as totp_permission_count FROM roles r LEFT JOIN role_permissions rp ON r.id = rp.role_id LEFT JOIN permissions p ON rp.permission_id = p.id AND p.code LIKE ''api:totp%'' WHERE r.code IN (''super_admin'', ''merchant_admin'', ''ck_supplier'') GROUP BY r.id, r.name, r.code ORDER BY r.code;',
    'SELECT ''TOTP权限检查跳过 - 已执行过修复'' as info;'
);

PREPARE stmt_check_current FROM @sql_check_current;
EXECUTE stmt_check_current;
DEALLOCATE PREPARE stmt_check_current;

-- 2. 为商户管理员分配TOTP权限（仅在需要时执行）
SET @sql_merchant_permissions = IF(@should_fix_totp = 1, 
    'INSERT IGNORE INTO role_permissions (role_id, permission_id) SELECT r.id, p.id FROM roles r, permissions p WHERE r.code = ''merchant_admin'' AND p.code IN (''menu:system:security'', ''menu:system:security:totp'', ''api:/api/v1/totp'', ''api:totp:status'', ''api:totp:setup'', ''api:totp:verify'', ''api:totp:enable'', ''api:totp:disable'', ''api:totp:backup-verify'', ''api:totp:required'');',
    'SELECT ''跳过商户管理员TOTP权限分配 - 已执行过修复'' as status;'
);

PREPARE stmt_merchant_permissions FROM @sql_merchant_permissions;
EXECUTE stmt_merchant_permissions;
DEALLOCATE PREPARE stmt_merchant_permissions;

-- 3. 为CK供应商分配TOTP权限（仅在需要时执行）
SET @sql_ck_permissions = IF(@should_fix_totp = 1, 
    'INSERT IGNORE INTO role_permissions (role_id, permission_id) SELECT r.id, p.id FROM roles r, permissions p WHERE r.code = ''ck_supplier'' AND p.code IN (''menu:system:security'', ''menu:system:security:totp'', ''api:/api/v1/totp'', ''api:totp:status'', ''api:totp:setup'', ''api:totp:verify'', ''api:totp:enable'', ''api:totp:disable'', ''api:totp:backup-verify'', ''api:totp:required'');',
    'SELECT ''跳过CK供应商TOTP权限分配 - 已执行过修复'' as status;'
);

PREPARE stmt_ck_permissions FROM @sql_ck_permissions;
EXECUTE stmt_ck_permissions;
DEALLOCATE PREPARE stmt_ck_permissions;

-- 4. 为商户管理员分配安全设置菜单（仅在需要时执行）
SET @sql_merchant_menus = IF(@should_fix_totp = 1, 
    'INSERT IGNORE INTO role_menus (role_id, menu_id) SELECT r.id, m.id FROM roles r, menus m WHERE r.code = ''merchant_admin'' AND m.code IN (''system:security'', ''system:security:totp'');',
    'SELECT ''跳过商户管理员菜单分配 - 已执行过修复'' as status;'
);

PREPARE stmt_merchant_menus FROM @sql_merchant_menus;
EXECUTE stmt_merchant_menus;
DEALLOCATE PREPARE stmt_merchant_menus;

-- 5. 为CK供应商分配安全设置菜单（仅在需要时执行）
SET @sql_ck_menus = IF(@should_fix_totp = 1, 
    'INSERT IGNORE INTO role_menus (role_id, menu_id) SELECT r.id, m.id FROM roles r, menus m WHERE r.code = ''ck_supplier'' AND m.code IN (''system:security'', ''system:security:totp'');',
    'SELECT ''跳过CK供应商菜单分配 - 已执行过修复'' as status;'
);

PREPARE stmt_ck_menus FROM @sql_ck_menus;
EXECUTE stmt_ck_menus;
DEALLOCATE PREPARE stmt_ck_menus;

-- 6. 验证修复结果（仅在执行修复时显示）
SET @sql_verify_results = IF(@should_fix_totp = 1, 
    'SELECT ''=== 修复后TOTP权限分配情况 ==='' as info; SELECT r.name as role_name, r.code as role_code, COUNT(p.id) as totp_permission_count FROM roles r LEFT JOIN role_permissions rp ON r.id = rp.role_id LEFT JOIN permissions p ON rp.permission_id = p.id AND p.code LIKE ''api:totp%'' WHERE r.code IN (''super_admin'', ''merchant_admin'', ''ck_supplier'') GROUP BY r.id, r.name, r.code ORDER BY r.code;',
    'SELECT ''TOTP权限验证跳过 - 已执行过修复'' as info;'
);

PREPARE stmt_verify_results FROM @sql_verify_results;
EXECUTE stmt_verify_results;
DEALLOCATE PREPARE stmt_verify_results;

-- 7. 检查安全设置菜单分配情况（仅在执行修复时显示）
SET @sql_check_menus = IF(@should_fix_totp = 1, 
    'SELECT ''=== 安全设置菜单分配情况 ==='' as info; SELECT r.name as role_name, r.code as role_code, m.name as menu_name, m.code as menu_code FROM roles r JOIN role_menus rm ON r.id = rm.role_id JOIN menus m ON rm.menu_id = m.id WHERE r.code IN (''super_admin'', ''merchant_admin'', ''ck_supplier'') AND m.code LIKE ''system:security%'' ORDER BY r.code, m.code;',
    'SELECT ''菜单分配检查跳过 - 已执行过修复'' as info;'
);

PREPARE stmt_check_menus FROM @sql_check_menus;
EXECUTE stmt_check_menus;
DEALLOCATE PREPARE stmt_check_menus;

-- 8. 验证权限配置是否正确（仅在执行修复时验证）
SET @sql_verify_config = IF(@should_fix_totp = 1,
    'SELECT ''=== 权限配置验证 ==='' as info; SELECT CASE WHEN COUNT(*) >= 7 THEN ''✓ 商户管理员TOTP权限配置正确'' ELSE CONCAT(''✗ 商户管理员TOTP权限配置错误，当前数量: '', COUNT(*)) END as merchant_admin_totp_check FROM role_permissions rp JOIN roles r ON rp.role_id = r.id JOIN permissions p ON rp.permission_id = p.id WHERE r.code = ''merchant_admin'' AND p.code LIKE ''api:totp%''; SELECT CASE WHEN COUNT(*) >= 7 THEN ''✓ CK供应商TOTP权限配置正确'' ELSE CONCAT(''✗ CK供应商TOTP权限配置错误，当前数量: '', COUNT(*)) END as ck_supplier_totp_check FROM role_permissions rp JOIN roles r ON rp.role_id = r.id JOIN permissions p ON rp.permission_id = p.id WHERE r.code = ''ck_supplier'' AND p.code LIKE ''api:totp%'';',
    'SELECT ''权限配置验证跳过 - 已执行过修复'' as info;'
);

PREPARE stmt_verify_config FROM @sql_verify_config;
EXECUTE stmt_verify_config;
DEALLOCATE PREPARE stmt_verify_config;

-- 9. 创建财务角色用于测试权限控制（仅在需要时执行）
SET @sql_create_finance = IF(@should_fix_totp = 1,
    'INSERT IGNORE INTO roles (name, code, description, is_enabled, is_system, sort_order, created_at, updated_at) VALUES (''财务管理员'', ''finance_admin'', ''财务管理员，负责财务相关功能，无安全设置权限'', 1, 0, 4, NOW(3), NOW(3)); INSERT IGNORE INTO role_permissions (role_id, permission_id) SELECT r.id, p.id FROM roles r, permissions p WHERE r.code = ''finance_admin'' AND p.code IN (''menu:dashboard'', ''menu:cards'', ''menu:notification'', ''api:/api/v1/dashboard'', ''api:/api/v1/cards'', ''api:/api/v1/notifications'', ''api:/api/v1/menus'', ''api:menus:user-menus'', ''data:merchant:own''); INSERT IGNORE INTO role_menus (role_id, menu_id) SELECT r.id, m.id FROM roles r, menus m WHERE r.code = ''finance_admin'' AND m.code IN (''dashboard'', ''cards'', ''notification'');',
    'SELECT ''跳过财务角色创建 - 已执行过修复'' as status;'
);

PREPARE stmt_create_finance FROM @sql_create_finance;
EXECUTE stmt_create_finance;
DEALLOCATE PREPARE stmt_create_finance;

-- 10. 记录修复完成
UPDATE `migration_logs`
SET `status` = 'completed',
    `message` = 'TOTP权限配置修复完成',
    `completed_at` = NOW(3),
    `data_summary` = JSON_OBJECT(
        'fix_executed', @should_fix_totp,
        'merchant_admin_totp_permissions', (SELECT COUNT(*) FROM role_permissions rp JOIN roles r ON rp.role_id = r.id JOIN permissions p ON rp.permission_id = p.id WHERE r.code = 'merchant_admin' AND p.code LIKE 'api:totp%'),
        'ck_supplier_totp_permissions', (SELECT COUNT(*) FROM role_permissions rp JOIN roles r ON rp.role_id = r.id JOIN permissions p ON rp.permission_id = p.id WHERE r.code = 'ck_supplier' AND p.code LIKE 'api:totp%'),
        'finance_role_created', (SELECT COUNT(*) FROM roles WHERE code = 'finance_admin'),
        'completion_timestamp', NOW(3)
    )
WHERE `migration_name` = 'totp_permissions_fix_v2.1.0';

-- 11. 显示修复完成信息
SET @sql_completion_info = IF(@should_fix_totp = 1,
    'SELECT ''=== TOTP权限修复完成 ==='' as info, ''TOTP功能现在对所有角色开放：'' as description, ''- 超级管理员：强制启用，无宽限期'' as super_admin_policy, ''- 商户管理员：可选启用，7天宽限期'' as merchant_admin_policy, ''- CK供应商：可选启用，7天宽限期'' as ck_supplier_policy, ''- 财务管理员：无安全设置权限（用于测试权限控制）'' as finance_admin_policy;',
    'SELECT ''TOTP权限修复信息跳过 - 已执行过修复'' as info;'
);

PREPARE stmt_completion_info FROM @sql_completion_info;
EXECUTE stmt_completion_info;
DEALLOCATE PREPARE stmt_completion_info;

-- 12. 显示修复结果摘要
SELECT
    '=== TOTP权限修复脚本执行完成 ===' as summary,
    CASE
        WHEN @should_fix_totp = 1 THEN '✓ TOTP权限修复已执行'
        ELSE '⚠ TOTP权限修复已跳过（之前已执行）'
    END as execution_status,
    NOW(3) as completion_time;
