"""
Telegram群组管理API端点
"""

import secrets
import string
from typing import List, Optional
from datetime import datetime, timedelta
from fastapi import APIRouter, Depends, HTTPException, Query, status
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_
from pydantic import BaseModel, Field, field_validator

from app.api.deps import get_db, get_current_user
from app.models.user import User
from app.models.telegram_group import TelegramGroup, BindStatus, ChatType
from app.models.merchant import Merchant
from app.models.department import Department
from app.models.base import local_now
from app.schemas.response import BaseResponse, PageResponse
from app.core.logging import get_logger

logger = get_logger(__name__)

router = APIRouter()


class CreateBindTokenRequest(BaseModel):
    """创建绑定令牌请求"""
    merchant_id: int = Field(..., description="商户ID")
    department_id: Optional[int] = Field(None, description="部门ID")
    remark: Optional[str] = Field(None, description="备注信息")
    expire_hours: int = Field(24, description="令牌有效期（小时）")

    @field_validator('department_id', mode='before')
    @classmethod
    def validate_department_id(cls, v):
        """处理部门ID验证，将空字符串转换为None"""
        if v == '' or v == 0:
            return None
        return v


@router.post("/create-bind-token")
async def create_bind_token(
    request: CreateBindTokenRequest,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    创建群组绑定令牌
    """
    try:
        # 检查权限：仅允许超级管理员
        if not current_user.is_superuser:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="仅超级管理员可以管理Telegram机器人"
            )
        
        # 验证商户权限
        if not current_user.can_access_merchant_data(request.merchant_id):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="无权限访问该商户"
            )

        # 验证商户存在
        merchant = db.query(Merchant).filter_by(id=request.merchant_id).first()
        if not merchant:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="商户不存在"
            )

        # 验证部门（如果提供）
        department = None
        if request.department_id:
            department = db.query(Department).filter_by(
                id=request.department_id,
                merchant_id=request.merchant_id
            ).first()
            if not department:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="部门不存在"
                )
        
        # 生成绑定令牌
        bind_token = _generate_bind_token()

        # 生成唯一的临时chat_id（使用负数避免与真实chat_id冲突）
        import time
        temp_chat_id = -int(time.time() * 1000000) % 9223372036854775807  # 确保在BIGINT范围内

        # 创建群组绑定记录
        telegram_group = TelegramGroup(
            chat_id=temp_chat_id,  # 临时唯一值，绑定时更新为真实chat_id
            chat_title="待绑定",
            chat_type=ChatType.GROUP,
            merchant_id=request.merchant_id,
            department_id=request.department_id,
            remark=request.remark,
            bind_token=bind_token,
            bind_status=BindStatus.PENDING,
            bind_user_id=current_user.id
        )

        db.add(telegram_group)
        db.commit()
        db.refresh(telegram_group)

        # 计算过期时间
        expire_time = local_now() + timedelta(hours=request.expire_hours)
        
        return {
                "bind_token": bind_token,
                "bind_command": f"/bind {bind_token}",
                "expire_time": expire_time.isoformat(),
                "merchant_name": merchant.name,
                "department_name": department.name if department else None,
                "instructions": f"请在Telegram群组中发送以下命令完成绑定：\n/bind {bind_token}"
            }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"创建绑定令牌失败: {e}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="创建绑定令牌失败"
        )


# 添加不带斜杠的路由以避免307重定向
@router.get("")
@router.get("/")
async def list_telegram_groups(
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(20, ge=1, le=100, description="每页数量"),
    status_filter: Optional[str] = Query(None, description="状态过滤"),
    bind_status: Optional[str] = Query(None, description="绑定状态（前端兼容参数）"),
    chat_title: Optional[str] = Query(None, description="群组名称筛选"),
    merchant_id: Optional[int] = Query(None, description="商户ID"),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    查询群组列表
    """
    try:
        # 检查权限：仅允许超级管理员
        if not current_user.is_superuser:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="仅超级管理员可以管理Telegram机器人"
            )

        # 构建查询
        query = db.query(TelegramGroup)

        # 商户过滤
        if merchant_id:
            query = query.filter(TelegramGroup.merchant_id == merchant_id)

        # 状态过滤（支持两种参数名）
        status_value = status_filter or bind_status
        if status_value:
            # BindStatus是常量类，不是枚举类，需要验证值是否有效
            if status_value not in BindStatus.get_all_values():
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail=f"无效的状态值: {status_value}，有效值为: {', '.join(BindStatus.get_all_values())}"
                )
            query = query.filter(TelegramGroup.bind_status == status_value)

        # 群组名称过滤
        if chat_title:
            query = query.filter(TelegramGroup.chat_title.ilike(f"%{chat_title}%"))
        
        # 计算总数
        total = query.count()
        
        # 分页查询
        groups = query.offset((page - 1) * page_size).limit(page_size).all()
        
        # 转换为字典，对于管理界面包含敏感信息（绑定令牌）
        groups_data = [group.to_dict(include_sensitive=True) for group in groups]

        # 计算总页数
        pages = (total + page_size - 1) // page_size

        return {
                "items": groups_data,
                "total": total,
                "page": page,
                "page_size": page_size,
                "pages": pages
            }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"查询群组列表失败: {e}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="查询群组列表失败"
        )


@router.get("/{group_id}")
async def get_telegram_group(
    group_id: int,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    获取群组详情
    """
    try:
        # 检查权限
        if not current_user.has_permission("api:telegram:bind"):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="权限不足"
            )
        
        # 查询群组
        group = db.query(TelegramGroup).filter_by(id=group_id).first()
        if not group:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="群组不存在"
            )
        
        # 验证访问权限
        if not current_user.can_access_merchant_data(group.merchant_id):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="无权限访问该群组"
            )
        
        # 对于管理界面，包含敏感信息（绑定令牌）
        return group.to_dict(include_sensitive=True)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取群组详情失败: {e}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取群组详情失败"
        )


@router.post("/{group_id}/reset-token")
async def reset_bind_token(
    group_id: int,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    重置绑定令牌状态，允许重新绑定
    """
    try:
        # 检查权限：仅允许超级管理员
        if not current_user.is_superuser:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="仅超级管理员可以管理Telegram机器人"
            )

        # 查找群组记录
        telegram_group = db.query(TelegramGroup).filter_by(id=group_id).first()
        if not telegram_group:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="群组记录不存在"
            )

        # 验证商户权限
        if not current_user.can_access_merchant_data(telegram_group.merchant_id):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="无权限访问该商户"
            )

        # 检查当前状态是否可以重置
        if telegram_group.bind_status == BindStatus.ACTIVE:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="已绑定的群组无法重置令牌，请先解绑"
            )

        # 重置令牌状态
        old_status = telegram_group.bind_status
        telegram_group.bind_status = BindStatus.PENDING
        telegram_group.bind_time = None
        telegram_group.bind_user_id = None

        # 如果需要，可以生成新的令牌
        # telegram_group.bind_token = _generate_bind_token()

        db.commit()
        db.refresh(telegram_group)

        return {
            "message": "令牌状态重置成功",
            "group_id": group_id,
            "old_status": old_status,
            "new_status": telegram_group.bind_status,
            "bind_token": telegram_group.bind_token,
            "merchant_name": telegram_group.merchant.name if telegram_group.merchant else None
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"重置令牌失败: {e}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="重置令牌失败"
        )


@router.delete("/{group_id}/unbind")
async def unbind_telegram_group(
    group_id: int,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    解绑群组
    """
    try:
        # 检查权限：仅允许超级管理员
        if not current_user.is_superuser:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="仅超级管理员可以管理Telegram机器人"
            )

        # 查询群组
        group = db.query(TelegramGroup).filter_by(id=group_id).first()
        if not group:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="群组不存在"
            )

        # 验证访问权限
        if not current_user.can_access_merchant_data(group.merchant_id):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="无权限操作该群组"
            )

        # 保存群组信息用于响应
        merchant_name = group.merchant.name if group.merchant else "未知"
        chat_title = group.chat_title or "未知群组"

        # 删除群组绑定
        db.delete(group)
        db.commit()

        # 清理会话缓存，确保删除操作立即生效
        # 这是解决Telegram机器人日志记录外键约束问题的关键
        db.expunge_all()

        logger.info(f"群组删除成功: ID={group.id}, chat_id={group.chat_id}, 商户={merchant_name}")

        return {
            "success": True,
            "message": f"群组「{chat_title}」已成功解绑",
            "merchant_name": merchant_name
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"解绑群组失败: {e}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="解绑群组失败"
        )


@router.delete("/{group_id}")
async def delete_telegram_group(
    group_id: int,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    删除群组绑定（兼容性保留）
    """
    try:
        # 检查权限：仅允许超级管理员
        if not current_user.is_superuser:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="仅超级管理员可以管理Telegram机器人"
            )

        # 查询群组
        group = db.query(TelegramGroup).filter_by(id=group_id).first()
        if not group:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="群组不存在"
            )

        # 验证访问权限
        if not current_user.can_access_merchant_data(group.merchant_id):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="无权限操作该群组"
            )

        # 删除群组
        db.delete(group)
        db.commit()

        return {"deleted": True}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"删除群组失败: {e}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="删除群组失败"
        )


def _generate_bind_token() -> str:
    """生成绑定令牌"""
    # 生成32位随机字符串
    alphabet = string.ascii_letters + string.digits
    token = ''.join(secrets.choice(alphabet) for _ in range(32))
    return f"tg_bind_{token}"
