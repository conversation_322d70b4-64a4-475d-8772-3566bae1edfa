"""
双因子认证相关的数据模型
"""

from sqlalchemy import Column, String, Integer, BigInteger, Boolean, Text, ForeignKey, Index
from sqlalchemy.orm import relationship
from app.models.base import BaseModel, TimestampMixin


class TOTPLog(BaseModel, TimestampMixin):
    """双因子认证操作日志模型"""

    __tablename__ = "totp_logs"

    id = Column(BigInteger, primary_key=True, index=True, autoincrement=True, comment="主键ID")
    user_id = Column(BigInteger, ForeignKey("users.id"), nullable=False, comment="用户ID")
    action = Column(String(50), nullable=False, comment="操作类型：setup/verify/disable/backup_used")
    success = Column(Boolean, nullable=False, comment="操作是否成功")
    ip_address = Column(String(64), nullable=True, comment="操作IP地址")
    user_agent = Column(Text, nullable=True, comment="用户代理")
    error_message = Column(String(255), nullable=True, comment="错误信息")

    # 关联关系
    user = relationship("User", back_populates="totp_logs")

    # 索引
    __table_args__ = (
        Index('idx_totp_logs_user_id', 'user_id'),
        Index('idx_totp_logs_action', 'action'),
        Index('idx_totp_logs_created_at', 'created_at'),
    )


class TOTPPolicy(BaseModel, TimestampMixin):
    """双因子认证策略模型"""

    __tablename__ = "totp_policies"

    id = Column(BigInteger, primary_key=True, index=True, autoincrement=True, comment="主键ID")
    role_name = Column(String(50), nullable=False, unique=True, comment="角色名称")
    is_required = Column(Boolean, default=False, comment="是否强制启用")
    grace_period_days = Column(Integer, default=7, comment="宽限期天数")
    backup_codes_count = Column(Integer, default=10, comment="备用码数量")

    # 索引
    __table_args__ = (
        Index('uk_totp_policies_role', 'role_name', unique=True),
    )
