# 沃尔玛绑卡网关 - 统一构建脚本
# 支持生产环境和开发环境的一键构建

param(
    [Parameter(Mandatory=$false)]
    [ValidateSet("Production", "Development", "Both")]
    [string]$Mode = "Development",

    [string]$Version = "v2.0.0",
    [switch]$Clean = $false,
    [switch]$SkipTests = $false,
    [switch]$Verbose = $false,
    [switch]$Help = $false
)

function Write-Info($message) { Write-Host "[INFO] $message" -ForegroundColor Green }
function Write-Success($message) { Write-Host "[SUCCESS] $message" -ForegroundColor Cyan }
function Write-Warning($message) { Write-Host "[WARN] $message" -ForegroundColor Yellow }
function Write-Error($message) { Write-Host "[ERROR] $message" -ForegroundColor Red }
function Write-Debug($message) { if ($Verbose) { Write-Host "[DEBUG] $message" -ForegroundColor Gray } }

function Show-Help {
    Write-Host "沃尔玛绑卡网关 - 统一构建脚本" -ForegroundColor Cyan
    Write-Host ""
    Write-Host "用法:" -ForegroundColor Yellow
    Write-Host "  .\build.ps1 [选项]"
    Write-Host ""
    Write-Host "构建模式:" -ForegroundColor Yellow
    Write-Host "  -Mode Production    构建生产环境（防反编译Linux可执行文件）"
    Write-Host "  -Mode Development   构建开发环境（Docker容器，支持热重载）"
    Write-Host "  -Mode Both          同时构建生产和开发环境"
    Write-Host ""
    Write-Host "选项:" -ForegroundColor Yellow
    Write-Host "  -Version <版本>     指定版本号（默认: v2.0.0）"
    Write-Host "  -Clean              清理旧的构建文件和容器"
    Write-Host "  -SkipTests          跳过测试"
    Write-Host "  -Verbose            显示详细输出"
    Write-Host "  -Help               显示此帮助信息"
    Write-Host ""
    Write-Host "示例:" -ForegroundColor Yellow
    Write-Host "  .\build.ps1                           # 开发环境构建"
    Write-Host "  .\build.ps1 -Mode Production          # 生产环境构建"
    Write-Host "  .\build.ps1 -Mode Both -Clean         # 清理并构建两个环境"
    Write-Host "  .\build.ps1 -Mode Production -Version v2.1.0  # 指定版本的生产构建"
    Write-Host ""
    Write-Host "环境说明:" -ForegroundColor Yellow
    Write-Host "  生产环境: 生成防反编译的Linux可执行文件，适用于Ubuntu 24.04服务器"
    Write-Host "  开发环境: 本地Docker容器，支持热重载和调试，连接外部服务"
}

function Main {
    if ($Help) {
        Show-Help
        return 0
    }

    Write-Host "=========================================" -ForegroundColor Magenta
    Write-Host "沃尔玛绑卡网关 - 统一构建系统" -ForegroundColor Magenta
    Write-Host "双环境支持 | 一键构建 | 智能选择" -ForegroundColor Magenta
    Write-Host "=========================================" -ForegroundColor Magenta
    Write-Output ""

    Write-Info "构建模式: $Mode"
    Write-Info "版本: $Version"
    if ($Clean) { Write-Info "清理模式: 启用" }
    if ($SkipTests) { Write-Info "跳过测试: 是" }
    Write-Output ""

    # 检查基础环境
    if (!(CheckEnvironment)) { return 1 }

    # 执行构建
    $success = $true

    switch ($Mode) {
        "Production" {
            $success = BuildProduction
        }
        "Development" {
            $success = BuildDevelopment
        }
        "Both" {
            Write-Info "=== 构建开发环境 ==="
            $devSuccess = BuildDevelopment

            Write-Output ""
            Write-Info "=== 构建生产环境 ==="
            $prodSuccess = BuildProduction

            $success = $devSuccess -and $prodSuccess
        }
    }

    if ($success) {
        Write-Output ""
        Write-Success "🎉 构建完成！"
        ShowSummary
        return 0
    } else {
        Write-Error "构建失败，请检查错误信息"
        return 1
    }
}

function CheckEnvironment {
    Write-Info "检查构建环境..."

    # 检查Go环境
    if (!(Get-Command go -ErrorAction SilentlyContinue)) {
        Write-Error "Go未安装，请先安装Go 1.21+"
        return $false
    }

    $goVersion = go version
    Write-Debug "Go版本: $goVersion"

    # 检查项目文件
    if (!(Test-Path "main.go")) {
        Write-Error "未找到main.go文件，请在项目根目录运行此脚本"
        return $false
    }

    if (!(Test-Path "go.mod")) {
        Write-Error "未找到go.mod文件，请在项目根目录运行此脚本"
        return $false
    }

    Write-Success "环境检查通过"
    return $true
}

function BuildProduction {
    Write-Info "开始生产环境构建..."

    if (!(Test-Path "build-production.ps1")) {
        Write-Error "未找到生产环境构建脚本: build-production.ps1"
        return $false
    }

    # 构建参数
    $buildArgs = @("-Version", $Version)
    if ($SkipTests) { $buildArgs += "-SkipTests" }
    if ($Verbose) { $buildArgs += "-Verbose" }

    try {
        & ".\build-production.ps1" @buildArgs

        if ($LASTEXITCODE -eq 0) {
            Write-Success "生产环境构建成功"
            return $true
        } else {
            Write-Error "生产环境构建失败"
            return $false
        }
    } catch {
        Write-Error "生产环境构建异常: $($_.Exception.Message)"
        return $false
    }
}

function BuildDevelopment {
    Write-Info "开始开发环境构建..."

    if (!(Test-Path "build-docker.ps1")) {
        Write-Error "未找到开发环境构建脚本: build-docker.ps1"
        return $false
    }

    # 构建参数
    $buildArgs = @()
    if ($Clean) { $buildArgs += "-Clean" }
    if ($Verbose) { $buildArgs += "-Verbose" }

    try {
        & ".\build-docker.ps1" @buildArgs

        if ($LASTEXITCODE -eq 0) {
            Write-Success "开发环境构建成功"
            return $true
        } else {
            Write-Error "开发环境构建失败"
            return $false
        }
    } catch {
        Write-Error "开发环境构建异常: $($_.Exception.Message)"
        return $false
    }
}

function ShowSummary {
    Write-Output ""
    Write-Host "📋 构建摘要" -ForegroundColor Cyan
    Write-Output ""

    switch ($Mode) {
        "Production" {
            Write-Info "✅ 生产环境构建完成"
            Write-Info "   📁 部署包位置: dist/"
            Write-Info "   🐧 目标平台: Linux AMD64"
            Write-Info "   🛡️ 安全特性: 防反编译保护"
            Write-Info "   📖 部署说明: 查看 dist/ 目录中的 README.md"
        }
        "Development" {
            Write-Info "✅ 开发环境构建完成"
            Write-Info "   🐳 运行方式: Docker容器"
            Write-Info "   🔥 热重载: 已启用"
            Write-Info "   🌐 访问地址: http://localhost:21000"
            Write-Info "   📊 监控地址: http://localhost:9090"
        }
        "Both" {
            Write-Info "✅ 双环境构建完成"
            Write-Info "   🐳 开发环境: http://localhost:21000"
            Write-Info "   📁 生产包: dist/ 目录"
            Write-Info "   🔄 切换环境: 使用不同的构建脚本"
        }
    }

    Write-Output ""
    Write-Info "📚 相关文档:"
    Write-Info "   构建指南: BUILD_GUIDE.md"
    Write-Info "   Docker部署: DOCKER_DEPLOYMENT.md"
    Write-Info "   API文档: API_COMPATIBILITY.md"

    Write-Output ""
    Write-Info "🔧 常用命令:"
    if ($Mode -eq "Development" -or $Mode -eq "Both") {
        Write-Info "   查看日志: docker-compose logs -f"
        Write-Info "   重启服务: docker-compose restart"
        Write-Info "   停止服务: docker-compose down"
    }
    if ($Mode -eq "Production" -or $Mode -eq "Both") {
        Write-Info "   安全验证: .\verify-security.ps1"
        Write-Info "   部署测试: 上传 dist/ 到Linux服务器"
    }
}

# 执行主函数
try {
    $result = Main
    exit $result
} catch {
    Write-Error "构建过程中发生错误: $($_.Exception.Message)"
    exit 1
}
