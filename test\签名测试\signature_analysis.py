#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
微信小程序签名算法分析脚本
基于抓包数据分析和计算正确的签名算法
"""

import json
import hmac
import hashlib
import base64
from collections import OrderedDict

# 抓包数据1: getuserinfo
test_case_1 = {
    "timestamp": "1751174204050",
    "nonce": "24d4d7ffa", 
    "expected_signature": "74B1193DDB5AE4AC76247A2F456CC13C411D936C48C284A5C3DE8FFFC9CDB119",
    "body": {
        "currentPage": 0,
        "pageSize": 0,
        "sign": "6b95be8d25574dad856cdf7939bfe469@61fa608e14c37c20fde47c700ec90414"
    }
}

# 抓包数据2: bingcard
test_case_2 = {
    "timestamp": "1751174220771",
    "nonce": "1158f8500",
    "expected_signature": "107BAE44C39F6878E2CB2D7F832DE9BDFA77364208514F627473B8B920B3113A",
    "body": {
        "cardNo": "2326992090536890765",
        "cardPwd": "811911", 
        "currentPage": 0,
        "pageSize": 0,
        "sign": "6b95be8d25574dad856cdf7939bfe469@61fa608e14c37c20fde47c700ec90414",
        "storeId": "",
        "userPhone": ""
    }
}

# 已知参数
walmart_sign = "6b95be8d25574dad856cdf7939bfe469@61fa608e14c37c20fde47c700ec90414"
wechat_key = "zB0ZnBVP+iBBQjV0zEi0yA=="
version = 45

def sort_object_by_keys(obj):
    """按键名对对象进行排序"""
    if isinstance(obj, dict):
        return OrderedDict(sorted(obj.items()))
    return obj

def test_signature_algorithm(test_case, algorithm_name, sign_string, key):
    """测试签名算法"""
    try:
        # 计算HMAC-SHA256签名
        message = sign_string.encode("utf-8")
        signature = hmac.new(key, message, hashlib.sha256).hexdigest().upper()
        
        # 检查是否匹配
        is_match = signature == test_case["expected_signature"]
        
        print(f"\n算法: {algorithm_name}")
        print(f"签名字符串: {sign_string}")
        print(f"密钥长度: {len(key)} bytes")
        print(f"计算签名: {signature}")
        print(f"期望签名: {test_case['expected_signature']}")
        print(f"匹配结果: {'✓ 匹配' if is_match else '✗ 不匹配'}")
        
        return is_match
        
    except Exception as e:
        print(f"\n算法: {algorithm_name}")
        print(f"错误: {e}")
        return False

def analyze_signature(test_case, case_name):
    """分析签名算法"""
    print(f"\n{'='*60}")
    print(f"分析案例: {case_name}")
    print(f"时间戳: {test_case['timestamp']}")
    print(f"随机数: {test_case['nonce']}")
    print(f"期望签名: {test_case['expected_signature']}")
    print(f"{'='*60}")

    # 准备JSON数据
    sorted_body = sort_object_by_keys(test_case["body"])
    json_str = json.dumps(sorted_body, separators=(",", ":"))
    json_str_with_space = json.dumps(sorted_body, separators=(", ", ": "))

    print(f"JSON字符串(无空格): {json_str}")
    print(f"JSON字符串(有空格): {json_str_with_space}")

    # 准备不同的密钥格式
    keys_to_test = [
        ("wechat_key原始", wechat_key.encode("utf-8")),
        ("wechat_key_base64解码", base64.b64decode(wechat_key)),
        ("walmart_sign原始", walmart_sign.encode("utf-8")),
        ("walmart_sign前32字符", walmart_sign[:32].encode("utf-8")),
        ("walmart_sign后32字符", walmart_sign[-32:].encode("utf-8")),
        ("walmart_sign@前部分", walmart_sign.split("@")[0].encode("utf-8")),
        ("walmart_sign@后部分", walmart_sign.split("@")[1].encode("utf-8")),
        # 添加更多可能的密钥格式
        ("version+wechat_key", (str(version) + wechat_key).encode("utf-8")),
        ("wechat_key+version", (wechat_key + str(version)).encode("utf-8")),
        ("version字符串", str(version).encode("utf-8")),
        ("固定字符串test", b"test"),
        ("固定字符串key", b"key"),
        ("固定字符串secret", b"secret"),
    ]
    
    # 测试不同的签名字符串构建方式
    sign_patterns = [
        ("timestamp + nonce + JSON", test_case["timestamp"] + test_case["nonce"] + json_str),
        ("nonce + timestamp + JSON", test_case["nonce"] + test_case["timestamp"] + json_str),
        ("JSON + timestamp + nonce", json_str + test_case["timestamp"] + test_case["nonce"]),
        ("timestamp + JSON + nonce", test_case["timestamp"] + json_str + test_case["nonce"]),
        ("nonce + JSON + timestamp", test_case["nonce"] + json_str + test_case["timestamp"]),
        ("JSON + nonce + timestamp", json_str + test_case["nonce"] + test_case["timestamp"]),

        # 使用有空格的JSON
        ("timestamp + nonce + JSON(空格)", test_case["timestamp"] + test_case["nonce"] + json_str_with_space),
        ("nonce + timestamp + JSON(空格)", test_case["nonce"] + test_case["timestamp"] + json_str_with_space),

        # 使用分隔符
        ("timestamp|nonce|JSON", test_case["timestamp"] + "|" + test_case["nonce"] + "|" + json_str),
        ("timestamp&nonce&JSON", test_case["timestamp"] + "&" + test_case["nonce"] + "&" + json_str),
        ("timestamp_nonce_JSON", test_case["timestamp"] + "_" + test_case["nonce"] + "_" + json_str),

        # 只使用部分参数
        ("仅JSON", json_str),
        ("仅timestamp + nonce", test_case["timestamp"] + test_case["nonce"]),
        ("仅nonce + timestamp", test_case["nonce"] + test_case["timestamp"]),

        # 添加版本号的组合
        ("version + timestamp + nonce + JSON", str(version) + test_case["timestamp"] + test_case["nonce"] + json_str),
        ("timestamp + version + nonce + JSON", test_case["timestamp"] + str(version) + test_case["nonce"] + json_str),
        ("timestamp + nonce + version + JSON", test_case["timestamp"] + test_case["nonce"] + str(version) + json_str),
        ("timestamp + nonce + JSON + version", test_case["timestamp"] + test_case["nonce"] + json_str + str(version)),

        # 尝试不包含JSON的组合
        ("timestamp + nonce + version", test_case["timestamp"] + test_case["nonce"] + str(version)),
        ("version + timestamp + nonce", str(version) + test_case["timestamp"] + test_case["nonce"]),
        ("nonce + version + timestamp", test_case["nonce"] + str(version) + test_case["timestamp"]),

        # 尝试其他可能的组合
        ("sv3 + timestamp + nonce", "3" + test_case["timestamp"] + test_case["nonce"]),
        ("timestamp + nonce + sv3", test_case["timestamp"] + test_case["nonce"] + "3"),
        ("xweb_xhr1 + timestamp + nonce", "1" + test_case["timestamp"] + test_case["nonce"]),
    ]
    
    found_match = False
    
    # 遍历所有组合
    for key_name, key in keys_to_test:
        for pattern_name, sign_string in sign_patterns:
            algorithm_name = f"{pattern_name} + {key_name}"
            if test_signature_algorithm(test_case, algorithm_name, sign_string, key):
                found_match = True
                print(f"\n🎉 找到匹配的算法!")
                print(f"算法: {algorithm_name}")
                print(f"签名字符串: {sign_string}")
                print(f"密钥: {key_name}")
                return True
    
    if not found_match:
        print(f"\n❌ 未找到匹配的签名算法")
    
    return found_match

def main():
    """主函数"""
    print("微信小程序签名算法分析")
    print(f"沃尔玛签名: {walmart_sign}")
    print(f"微信密钥: {wechat_key}")
    print(f"版本号: {version}")
    
    # 分析两个测试案例
    result1 = analyze_signature(test_case_1, "getUserInfo接口")
    result2 = analyze_signature(test_case_2, "bindCard接口")
    
    print(f"\n{'='*60}")
    print("分析总结:")
    print(f"案例1 (getUserInfo): {'✓ 成功' if result1 else '✗ 失败'}")
    print(f"案例2 (bindCard): {'✓ 成功' if result2 else '✗ 失败'}")
    
    if result1 and result2:
        print("🎉 成功找到通用的签名算法!")
    elif result1 or result2:
        print("⚠️ 只有部分案例匹配，可能需要进一步分析")
    else:
        print("❌ 未找到匹配的签名算法，可能需要尝试其他方法")

if __name__ == "__main__":
    main()
