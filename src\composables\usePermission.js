import { computed } from 'vue'
import { useUserStore } from '@/store/modules/user'
import { usePermissionStore } from '@/store/modules/permission'

/**
 * 权限检查组合式函数 - 修复版本
 * 基于后端全动态权限系统，支持完整的数据权限检查
 */
export function usePermission() {
  const userStore = useUserStore()
  const permissionStore = usePermissionStore()

  // 用户信息
  const userInfo = computed(() => userStore.userInfo)
  const userRoles = computed(() => userInfo.value?.roles || [])
  const isSuperuser = computed(() => userInfo.value?.is_superuser || false)
  const merchantId = computed(() => userInfo.value?.merchant_id)
  const departmentId = computed(() => userInfo.value?.department_id)

  // 权限检查函数
  const hasPermission = (permissionCode) => {
    // 超级管理员拥有所有权限
    if (isSuperuser.value) {
      return true
    }

    // 检查用户权限列表
    const userPermissions = userInfo.value?.permissions || []
    return userPermissions.includes(permissionCode)
  }

  // 角色检查函数
  const hasRole = (roleCode) => {
    if (typeof roleCode === 'string') {
      return userRoles.value.some(role => role.code === roleCode)
    }
    
    if (Array.isArray(roleCode)) {
      return roleCode.some(code => userRoles.value.some(role => role.code === code))
    }
    
    return false
  }

  // 菜单权限检查
  const hasMenuPermission = (menuCode) => {
    // 超级管理员拥有所有菜单权限
    if (isSuperuser.value) {
      return true
    }

    // 检查用户菜单列表
    const userMenus = userInfo.value?.menus || []
    return userMenus.includes(menuCode)
  }

  // ===== 修复后的数据权限检查函数 =====

  /**
   * 商户数据权限检查 - 修复版本
   * 支持 data:merchant:all 和 data:merchant:own 权限
   */
  const canAccessMerchantData = (targetMerchantId) => {
    // 超级管理员可以访问所有商户数据
    if (isSuperuser.value) {
      return true
    }

    // 检查是否有访问所有商户数据的权限
    if (hasPermission('data:merchant:all')) {
      return true
    }

    // 检查是否有访问本商户数据的权限，且商户ID匹配
    if (hasPermission('data:merchant:own')) {
      return merchantId.value === targetMerchantId
    }

    return false
  }

  /**
   * 部门数据权限检查 - 新增功能
   * 支持 data:department:all、data:department:own、data:department:tree 权限
   */
  const canAccessDepartmentData = (targetDepartmentId) => {
    // 超级管理员可以访问所有部门数据
    if (isSuperuser.value) {
      return true
    }

    // 检查是否有访问所有部门数据的权限
    if (hasPermission('data:department:all')) {
      return true
    }

    // 检查是否有访问本部门数据的权限
    if (hasPermission('data:department:own')) {
      return departmentId.value === targetDepartmentId
    }

    // 检查是否有访问本部门及下级部门数据的权限
    if (hasPermission('data:department:tree')) {
      // 这里需要调用API获取部门层级关系
      // 暂时简化为只检查本部门
      return departmentId.value === targetDepartmentId
    }

    return false
  }

  /**
   * 用户数据权限检查 - 修复版本
   * 支持 data:user:all、data:user:own、data:user:department 权限
   */
  const canAccessUserData = (targetUserId) => {
    // 超级管理员可以访问所有用户数据
    if (isSuperuser.value) {
      return true
    }

    // 检查是否有访问所有用户数据的权限
    if (hasPermission('data:user:all')) {
      return true
    }

    // 检查是否只能访问本人数据
    if (hasPermission('data:user:own')) {
      return userInfo.value?.id === targetUserId
    }

    // 检查是否可以访问本部门用户数据
    if (hasPermission('data:user:department')) {
      // 这里需要获取目标用户的部门信息进行比较
      // 暂时返回 false，实际使用时需要调用API获取用户部门信息
      return false
    }

    return false
  }

  /**
   * 检查是否可以对指定商户执行操作
   * 结合商户数据权限和操作权限
   */
  const canOperateOnMerchant = (targetMerchantId, operation) => {
    // 首先检查是否有商户数据访问权限
    if (!canAccessMerchantData(targetMerchantId)) {
      return false
    }

    // 然后检查是否有对应的操作权限
    const operationPermission = `api:merchants:${operation}`
    return hasPermission(operationPermission)
  }

  /**
   * 检查是否可以对指定部门执行操作
   */
  const canOperateOnDepartment = (targetDepartmentId, operation) => {
    if (!canAccessDepartmentData(targetDepartmentId)) {
      return false
    }

    const operationPermission = `api:departments:${operation}`
    return hasPermission(operationPermission)
  }

  /**
   * 检查是否可以对指定用户执行操作
   */
  const canOperateOnUser = (targetUserId, operation) => {
    if (!canAccessUserData(targetUserId)) {
      return false
    }

    const operationPermission = `api:users:${operation}`
    return hasPermission(operationPermission)
  }

  // ===== 原有功能保持不变 =====

  // 操作权限检查
  const canCreate = (resource) => {
    if (isSuperuser.value) return true
    return hasPermission(`api:${resource}:create`)
  }

  const canEdit = (resource) => {
    if (isSuperuser.value) return true
    return hasPermission(`api:${resource}:update`)
  }

  const canDelete = (resource) => {
    if (isSuperuser.value) return true
    return hasPermission(`api:${resource}:delete`)
  }

  const canView = (resource) => {
    if (isSuperuser.value) return true
    return hasPermission(`api:${resource}:read`)
  }

  // 特定角色权限检查
  const isSuperAdmin = computed(() => hasRole('super_admin'))
  const isMerchantAdmin = computed(() => hasRole('merchant_admin'))
  const isMerchantOperator = computed(() => hasRole('merchant_operator'))
  const isCkSupplier = computed(() => hasRole('ck_supplier'))

  // 页面访问权限检查
  const canAccessPage = (pageCode) => {
    // 检查菜单权限
    if (hasMenuPermission(pageCode)) {
      return true
    }

    // 检查对应的API权限
    const apiPermission = `api:/api/v1/${pageCode}`
    return hasPermission(apiPermission)
  }

  // 按钮显示权限检查
  const showButton = (buttonCode) => {
    // 超级管理员显示所有按钮
    if (isSuperuser.value) {
      return true
    }

    // 检查具体的按钮权限
    return hasPermission(`button:${buttonCode}`)
  }

  // 权限降级处理 - 当用户缺少某些权限时的替代方案
  const getPermissionFallback = (requiredPermission, fallbackOptions = {}) => {
    // 如果有权限，返回正常状态
    if (hasPermission(requiredPermission)) {
      return {
        hasPermission: true,
        canProceed: true,
        message: null,
        fallbackData: null
      }
    }

    // 根据权限类型提供不同的降级方案
    if (requiredPermission === 'api:departments:read') {
      // 部门查询权限降级
      if (!isSuperuser.value && departmentId.value) {
        return {
          hasPermission: false,
          canProceed: true,
          message: '已自动选择您的当前部门',
          fallbackData: [{
            id: departmentId.value,
            name: userInfo.value?.department_name || '当前部门',
            merchant_id: merchantId.value
          }]
        }
      }

      return {
        hasPermission: false,
        canProceed: false,
        message: '您没有部门查询权限，请联系管理员',
        fallbackData: []
      }
    }

    // 默认降级方案
    return {
      hasPermission: false,
      canProceed: false,
      message: `您没有 ${requiredPermission} 权限`,
      fallbackData: null
    }
  }

  // 检查是否可以执行特定操作（带降级支持）
  const canPerformAction = (action, context = {}) => {
    const actionPermissions = {
      'create_ck': ['api:walmart-ck:create'],
      'edit_ck': ['api:walmart-ck:update'],
      'view_departments': ['api:departments:read'],
      'view_merchants': ['api:merchants:read']
    }

    const requiredPermissions = actionPermissions[action] || []

    // 检查是否有所有必需权限
    const hasAllPermissions = requiredPermissions.every(permission => hasPermission(permission))

    if (hasAllPermissions) {
      return {
        canPerform: true,
        missingPermissions: [],
        suggestions: []
      }
    }

    // 分析缺失的权限并提供建议
    const missingPermissions = requiredPermissions.filter(permission => !hasPermission(permission))
    const suggestions = []

    // 为特定操作提供建议
    if (action === 'create_ck' && missingPermissions.includes('api:departments:read')) {
      if (!isSuperuser.value && departmentId.value) {
        suggestions.push('系统将自动使用您的当前部门')
      } else {
        suggestions.push('请联系管理员分配部门查询权限，或指定默认部门')
      }
    }

    return {
      canPerform: false,
      missingPermissions,
      suggestions
    }
  }

  return {
    // 基础权限检查
    hasPermission,
    hasRole,
    hasMenuPermission,
    
    // 数据权限检查 - 修复和新增
    canAccessMerchantData,
    canAccessDepartmentData,
    canAccessUserData,
    
    // 操作权限检查 - 新增
    canOperateOnMerchant,
    canOperateOnDepartment,
    canOperateOnUser,

    // 权限降级和建议 - 新增
    getPermissionFallback,
    canPerformAction,

    // 原有功能
    canCreate,
    canEdit,
    canDelete,
    canView,
    canAccessPage,
    showButton,
    
    // 角色检查
    isSuperAdmin,
    isMerchantAdmin,
    isMerchantOperator,
    isCkSupplier,
    
    // 用户信息
    userInfo,
    userRoles,
    isSuperuser,
    merchantId,
    departmentId
  }
}

/**
 * 权限指令
 */
export const vPermission = {
  mounted(el, binding) {
    const { hasPermission } = usePermission()
    const permission = binding.value

    if (!hasPermission(permission)) {
      el.style.display = 'none'
    }
  },
  
  updated(el, binding) {
    const { hasPermission } = usePermission()
    const permission = binding.value

    if (!hasPermission(permission)) {
      el.style.display = 'none'
    } else {
      el.style.display = ''
    }
  }
}

/**
 * 角色指令
 */
export const vRole = {
  mounted(el, binding) {
    const { hasRole } = usePermission()
    const role = binding.value

    if (!hasRole(role)) {
      el.style.display = 'none'
    }
  },

  updated(el, binding) {
    const { hasRole } = usePermission()
    const role = binding.value

    if (!hasRole(role)) {
      el.style.display = 'none'
    } else {
      el.style.display = ''
    }
  }
}