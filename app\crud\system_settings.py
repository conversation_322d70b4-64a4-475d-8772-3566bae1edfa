"""
系统设置CRUD操作
"""
from typing import List, Optional, Dict, Any, Tuple
from sqlalchemy.orm import Session
from sqlalchemy import desc, and_, or_

from app.crud.base import CRUDBase
from app.models.system_settings import SystemSettings
from app.schemas.system_settings import SystemSettingsCreate, SystemSettingsUpdate


class CRUDSystemSettings(CRUDBase[SystemSettings, SystemSettingsCreate, SystemSettingsUpdate]):
    """系统设置CRUD操作类"""

    def get_by_key(self, db: Session, key: str) -> Optional[SystemSettings]:
        """根据键名获取设置"""
        return db.query(self.model).filter(self.model.key == key).first()

    def get_value(self, db: Session, key: str, default: Optional[str] = None) -> Optional[str]:
        """获取设置值"""
        setting = self.get_by_key(db, key)
        if setting and setting.is_enabled:
            return setting.value
        return default

    def set_value(self, db: Session, key: str, value: str, description: Optional[str] = None) -> SystemSettings:
        """设置值（如果不存在则创建）"""
        setting = self.get_by_key(db, key)
        
        if setting:
            # 更新现有设置
            setting.value = value
            if description:
                setting.description = description
            db.commit()
            db.refresh(setting)
            return setting
        else:
            # 创建新设置
            setting_data = {
                "key": key,
                "value": value,
                "description": description,
                "is_enabled": True,
                "is_system": False,
            }
            db_obj = self.model(**setting_data)
            db.add(db_obj)
            db.commit()
            db.refresh(db_obj)
            return db_obj

    def get_enabled_settings(self, db: Session) -> List[SystemSettings]:
        """获取所有启用的设置"""
        return db.query(self.model).filter(self.model.is_enabled == True).all()

    def get_system_settings(self, db: Session) -> List[SystemSettings]:
        """获取所有系统内置设置"""
        return db.query(self.model).filter(self.model.is_system == True).all()

    def get_custom_settings(self, db: Session) -> List[SystemSettings]:
        """获取所有自定义设置"""
        return db.query(self.model).filter(self.model.is_system == False).all()

    def get_multi_with_filters_and_count(
        self,
        db: Session,
        *,
        skip: int = 0,
        limit: int = 100,
        filters: Optional[Dict[str, Any]] = None,
    ) -> Tuple[List[SystemSettings], int]:
        """根据过滤条件获取设置列表和总数"""
        query = db.query(self.model)
        
        if filters:
            # 键名过滤
            if "key" in filters and filters["key"]:
                query = query.filter(self.model.key.like(f"%{filters['key']}%"))
            
            # 启用状态过滤
            if "is_enabled" in filters:
                query = query.filter(self.model.is_enabled == filters["is_enabled"])
            
            # 系统设置过滤
            if "is_system" in filters:
                query = query.filter(self.model.is_system == filters["is_system"])
            
            # 描述过滤
            if "description" in filters and filters["description"]:
                query = query.filter(self.model.description.like(f"%{filters['description']}%"))
        
        # 获取总数
        total = query.count()
        
        # 获取分页数据
        settings = query.order_by(self.model.key).offset(skip).limit(limit).all()
        
        return settings, total

    def enable_setting(self, db: Session, key: str) -> Optional[SystemSettings]:
        """启用设置"""
        setting = self.get_by_key(db, key)
        if setting:
            setting.is_enabled = True
            db.commit()
            db.refresh(setting)
            return setting
        return None

    def disable_setting(self, db: Session, key: str) -> Optional[SystemSettings]:
        """禁用设置"""
        setting = self.get_by_key(db, key)
        if setting:
            setting.is_enabled = False
            db.commit()
            db.refresh(setting)
            return setting
        return None

    def delete_custom_setting(self, db: Session, key: str) -> bool:
        """删除自定义设置（不能删除系统设置）"""
        setting = self.get_by_key(db, key)
        if setting and not setting.is_system:
            db.delete(setting)
            db.commit()
            return True
        return False

    def get_settings_dict(self, db: Session, enabled_only: bool = True) -> Dict[str, str]:
        """获取设置字典"""
        query = db.query(self.model)
        
        if enabled_only:
            query = query.filter(self.model.is_enabled == True)
        
        settings = query.all()
        return {setting.key: setting.value for setting in settings if setting.value is not None}

    def bulk_update(self, db: Session, settings_data: Dict[str, str]) -> List[SystemSettings]:
        """批量更新设置"""
        updated_settings = []
        
        for key, value in settings_data.items():
            setting = self.set_value(db, key, value)
            updated_settings.append(setting)
        
        return updated_settings

    def init_default_settings(self, db: Session) -> List[SystemSettings]:
        """初始化默认系统设置"""
        default_settings = [
            {
                "key": "system.daily_bind_limit",
                "value": "10000",
                "description": "系统每日绑卡限制",
                "is_system": True,
            },
            {
                "key": "system.api_rate_limit",
                "value": "60",
                "description": "API速率限制(次/分钟)",
                "is_system": True,
            },
            {
                "key": "system.max_retry_times",
                "value": "3",
                "description": "最大重试次数",
                "is_system": True,
            },
            {
                "key": "system.bind_timeout_seconds",
                "value": "30",
                "description": "绑卡超时时间(秒)",
                "is_system": True,
            },
            {
                "key": "system.log_retention_days",
                "value": "90",
                "description": "日志保留天数",
                "is_system": True,
            },
            {
                "key": "system.enable_ip_whitelist",
                "value": "true",
                "description": "是否启用IP白名单",
                "is_system": True,
            },
            {
                "key": "system.enable_security_audit",
                "value": "true",
                "description": "是否启用安全审计",
                "is_system": True,
            },
            {
                "key": "system.maintenance_mode",
                "value": "false",
                "description": "维护模式",
                "is_system": True,
            },
        ]
        
        created_settings = []
        
        for setting_data in default_settings:
            existing = self.get_by_key(db, setting_data["key"])
            if not existing:
                db_obj = self.model(**setting_data)
                db.add(db_obj)
                created_settings.append(db_obj)
        
        if created_settings:
            db.commit()
            for setting in created_settings:
                db.refresh(setting)
        
        return created_settings


# 创建全局实例
system_settings = CRUDSystemSettings(SystemSettings)
