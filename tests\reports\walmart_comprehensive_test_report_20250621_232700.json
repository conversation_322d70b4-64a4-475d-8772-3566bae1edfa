{"summary": {"total_tests": 19, "passed_tests": 11, "failed_tests": 8, "success_rate": 57.89473684210527, "total_duration": 33.75444579124451, "start_time": "2025-06-21T23:26:26.994438", "end_time": "2025-06-21T23:27:00.748883", "overall_success": false}, "module_stats": {"认证模块测试": {"total": 14, "passed": 11, "failed": 3, "success_rate": 78.57142857142857, "duration": 3.6518635749816895, "success": false}, "用户CRUD测试": {"total": 0, "passed": 0, "failed": 0, "success_rate": 0, "duration": 2.316225051879883, "success": true}, "商户CRUD测试": {"total": 0, "passed": 0, "failed": 0, "success_rate": 0, "duration": 2.301447629928589, "success": true}, "部门CRUD测试": {"total": 0, "passed": 0, "failed": 0, "success_rate": 0, "duration": 2.305678367614746, "success": true}, "角色权限测试": {"total": 0, "passed": 0, "failed": 0, "success_rate": 0, "duration": 2.3248062133789062, "success": true}, "卡记录管理测试": {"total": 1, "passed": 0, "failed": 1, "success_rate": 0.0, "duration": 2.339798927307129, "success": false}, "沃尔玛CK管理测试": {"total": 1, "passed": 0, "failed": 1, "success_rate": 0.0, "duration": 2.3037619590759277, "success": false}, "绑定日志测试": {"total": 1, "passed": 0, "failed": 1, "success_rate": 0.0, "duration": 2.3073203563690186, "success": false}, "通知管理测试": {"total": 1, "passed": 0, "failed": 1, "success_rate": 0.0, "duration": 2.3203134536743164, "success": false}, "仪表盘测试": {"total": 1, "passed": 0, "failed": 1, "success_rate": 0.0, "duration": 2.2934024333953857, "success": false}, "API安全测试": {"total": 0, "passed": 0, "failed": 0, "success_rate": 0, "duration": 2.3022286891937256, "success": true}, "跨边界数据操作测试": {"total": 0, "passed": 0, "failed": 0, "success_rate": 0, "duration": 2.307579755783081, "success": true}, "部门层级权限测试": {"total": 0, "passed": 0, "failed": 0, "success_rate": 0, "duration": 2.304335355758667, "success": true}, "数据隔离测试": {"total": 0, "passed": 0, "failed": 0, "success_rate": 0, "duration": 2.3651223182678223, "success": true}}, "module_summaries": [{"module_name": "认证模块测试", "module_path": "test.auth.test_auth", "success": false, "test_count": 14, "passed_count": 11, "failed_count": 3, "duration": 3.6518635749816895, "error_message": "", "timestamp": 1750519590.6473029}, {"module_name": "用户CRUD测试", "module_path": "test.users.test_users_crud", "success": true, "test_count": 0, "passed_count": 0, "failed_count": 0, "duration": 2.316225051879883, "error_message": "", "timestamp": 1750519592.9645276}, {"module_name": "商户CRUD测试", "module_path": "test.merchants.test_merchants_crud", "success": true, "test_count": 0, "passed_count": 0, "failed_count": 0, "duration": 2.301447629928589, "error_message": "", "timestamp": 1750519595.266974}, {"module_name": "部门CRUD测试", "module_path": "test.departments.test_departments_crud", "success": true, "test_count": 0, "passed_count": 0, "failed_count": 0, "duration": 2.305678367614746, "error_message": "", "timestamp": 1750519597.5736518}, {"module_name": "角色权限测试", "module_path": "test.roles.test_roles_permissions", "success": true, "test_count": 0, "passed_count": 0, "failed_count": 0, "duration": 2.3248062133789062, "error_message": "", "timestamp": 1750519599.898458}, {"module_name": "卡记录管理测试", "module_path": "test.cards.test_cards_crud", "success": false, "test_count": 1, "passed_count": 0, "failed_count": 1, "duration": 2.339798927307129, "error_message": "", "timestamp": 1750519602.2393148}, {"module_name": "沃尔玛CK管理测试", "module_path": "test.walmart_ck.test_walmart_ck_crud", "success": false, "test_count": 1, "passed_count": 0, "failed_count": 1, "duration": 2.3037619590759277, "error_message": "", "timestamp": 1750519604.5450742}, {"module_name": "绑定日志测试", "module_path": "test.binding_logs.test_binding_logs", "success": false, "test_count": 1, "passed_count": 0, "failed_count": 1, "duration": 2.3073203563690186, "error_message": "", "timestamp": 1750519606.8523946}, {"module_name": "通知管理测试", "module_path": "test.notifications.test_notifications_crud", "success": false, "test_count": 1, "passed_count": 0, "failed_count": 1, "duration": 2.3203134536743164, "error_message": "", "timestamp": **********.172708}, {"module_name": "仪表盘测试", "module_path": "test.dashboard.test_dashboard", "success": false, "test_count": 1, "passed_count": 0, "failed_count": 1, "duration": 2.2934024333953857, "error_message": "", "timestamp": **********.4661105}, {"module_name": "API安全测试", "module_path": "test.security.test_api_security", "success": true, "test_count": 0, "passed_count": 0, "failed_count": 0, "duration": 2.3022286891937256, "error_message": "", "timestamp": 1750519613.7693384}, {"module_name": "跨边界数据操作测试", "module_path": "test.security.test_cross_boundary_operations", "success": true, "test_count": 0, "passed_count": 0, "failed_count": 0, "duration": 2.307579755783081, "error_message": "", "timestamp": 1750519616.0779192}, {"module_name": "部门层级权限测试", "module_path": "test.security.test_department_hierarchy_permissions", "success": true, "test_count": 0, "passed_count": 0, "failed_count": 0, "duration": 2.304335355758667, "error_message": "", "timestamp": 1750519618.3832548}, {"module_name": "数据隔离测试", "module_path": "test.security.test_data_isolation", "success": true, "test_count": 0, "passed_count": 0, "failed_count": 0, "duration": 2.3651223182678223, "error_message": "", "timestamp": 1750519620.7488835}], "all_test_results": [{"test_name": "super_admin_登录", "success": true, "message": "用户 admin 登录成功", "details": {"username": "admin", "token_length": 141}, "timestamp": 1750519589.3071783, "datetime": "2025-06-21T23:26:29.307178", "module": "认证模块测试", "module_path": "test.auth.test_auth"}, {"test_name": "merchant_admin_登录", "success": false, "message": "用户 test1 登录失败", "details": {}, "timestamp": 1750519589.3186967, "datetime": "2025-06-21T23:26:29.318696", "module": "认证模块测试", "module_path": "test.auth.test_auth"}, {"test_name": "merchant_admin_b_登录", "success": false, "message": "用户 test_merchant_b 登录失败", "details": {}, "timestamp": 1750519589.3306975, "datetime": "2025-06-21T23:26:29.330697", "module": "认证模块测试", "module_path": "test.auth.test_auth"}, {"test_name": "无效登录拒绝_invalid_user", "success": true, "message": "正确拒绝无效登录: invalid_user", "details": {}, "timestamp": 1750519589.342701, "datetime": "2025-06-21T23:26:29.342701", "module": "认证模块测试", "module_path": "test.auth.test_auth"}, {"test_name": "无效登录拒绝_admin", "success": true, "message": "正确拒绝无效登录: admin", "details": {}, "timestamp": 1750519589.5937438, "datetime": "2025-06-21T23:26:29.593744", "module": "认证模块测试", "module_path": "test.auth.test_auth"}, {"test_name": "无效登录拒绝_", "success": true, "message": "正确拒绝无效登录: ", "details": {}, "timestamp": 1750519589.6042576, "datetime": "2025-06-21T23:26:29.604257", "module": "认证模块测试", "module_path": "test.auth.test_auth"}, {"test_name": "无效登录拒绝_test1", "success": true, "message": "正确拒绝无效登录: test1", "details": {}, "timestamp": 1750519589.6174667, "datetime": "2025-06-21T23:26:29.617466", "module": "认证模块测试", "module_path": "test.auth.test_auth"}, {"test_name": "有效Token验证", "success": true, "message": "有效Token验证成功", "details": {}, "timestamp": 1750519589.6324687, "datetime": "2025-06-21T23:26:29.632468", "module": "认证模块测试", "module_path": "test.auth.test_auth"}, {"test_name": "无效Token拒绝", "success": true, "message": "正确拒绝无效Token", "details": {}, "timestamp": 1750519589.636469, "datetime": "2025-06-21T23:26:29.636469", "module": "认证模块测试", "module_path": "test.auth.test_auth"}, {"test_name": "登出测试前置条件", "success": false, "message": "无法获取测试token", "details": {}, "timestamp": 1750519589.647471, "datetime": "2025-06-21T23:26:29.647470", "module": "认证模块测试", "module_path": "test.auth.test_auth"}, {"test_name": "SQL注入防护_' OR '1'='", "success": true, "message": "正确阻止SQL注入尝试", "details": {}, "timestamp": 1750519589.8987548, "datetime": "2025-06-21T23:26:29.898754", "module": "认证模块测试", "module_path": "test.auth.test_auth"}, {"test_name": "SQL注入防护_'; DROP TA", "success": true, "message": "正确阻止SQL注入尝试", "details": {}, "timestamp": 1750519590.1526372, "datetime": "2025-06-21T23:26:30.152637", "module": "认证模块测试", "module_path": "test.auth.test_auth"}, {"test_name": "SQL注入防护_admin'--", "success": true, "message": "正确阻止SQL注入尝试", "details": {}, "timestamp": 1750519590.3996985, "datetime": "2025-06-21T23:26:30.399698", "module": "认证模块测试", "module_path": "test.auth.test_auth"}, {"test_name": "SQL注入防护_' UNION SE", "success": true, "message": "正确阻止SQL注入尝试", "details": {}, "timestamp": 1750519590.6463072, "datetime": "2025-06-21T23:26:30.646307", "module": "认证模块测试", "module_path": "test.auth.test_auth"}, {"test_name": "测试环境设置", "success": false, "message": "测试环境设置失败: 无法获取商户token", "details": {}, "timestamp": 1750519602.2383053, "datetime": "2025-06-21T23:26:42.238305", "module": "卡记录管理测试", "module_path": "test.cards.test_cards_crud"}, {"test_name": "测试环境设置", "success": false, "message": "测试环境设置失败: 无法获取商户token", "details": {}, "timestamp": 1750519604.543074, "datetime": "2025-06-21T23:26:44.543073", "module": "沃尔玛CK管理测试", "module_path": "test.walmart_ck.test_walmart_ck_crud"}, {"test_name": "测试环境设置", "success": false, "message": "测试环境设置失败: 无法获取商户token", "details": {}, "timestamp": 1750519606.851394, "datetime": "2025-06-21T23:26:46.851394", "module": "绑定日志测试", "module_path": "test.binding_logs.test_binding_logs"}, {"test_name": "测试环境设置", "success": false, "message": "测试环境设置失败: 无法获取商户token", "details": {}, "timestamp": **********.1711907, "datetime": "2025-06-21T23:26:49.171190", "module": "通知管理测试", "module_path": "test.notifications.test_notifications_crud"}, {"test_name": "测试环境设置", "success": false, "message": "测试环境设置失败: 无法获取商户token", "details": {}, "timestamp": **********.4661105, "datetime": "2025-06-21T23:26:51.466110", "module": "仪表盘测试", "module_path": "test.dashboard.test_dashboard"}], "failed_test_details": [{"test_name": "merchant_admin_登录", "success": false, "message": "用户 test1 登录失败", "details": {}, "timestamp": 1750519589.3186967, "datetime": "2025-06-21T23:26:29.318696", "module": "认证模块测试", "module_path": "test.auth.test_auth"}, {"test_name": "merchant_admin_b_登录", "success": false, "message": "用户 test_merchant_b 登录失败", "details": {}, "timestamp": 1750519589.3306975, "datetime": "2025-06-21T23:26:29.330697", "module": "认证模块测试", "module_path": "test.auth.test_auth"}, {"test_name": "登出测试前置条件", "success": false, "message": "无法获取测试token", "details": {}, "timestamp": 1750519589.647471, "datetime": "2025-06-21T23:26:29.647470", "module": "认证模块测试", "module_path": "test.auth.test_auth"}, {"test_name": "测试环境设置", "success": false, "message": "测试环境设置失败: 无法获取商户token", "details": {}, "timestamp": 1750519602.2383053, "datetime": "2025-06-21T23:26:42.238305", "module": "卡记录管理测试", "module_path": "test.cards.test_cards_crud"}, {"test_name": "测试环境设置", "success": false, "message": "测试环境设置失败: 无法获取商户token", "details": {}, "timestamp": 1750519604.543074, "datetime": "2025-06-21T23:26:44.543073", "module": "沃尔玛CK管理测试", "module_path": "test.walmart_ck.test_walmart_ck_crud"}, {"test_name": "测试环境设置", "success": false, "message": "测试环境设置失败: 无法获取商户token", "details": {}, "timestamp": 1750519606.851394, "datetime": "2025-06-21T23:26:46.851394", "module": "绑定日志测试", "module_path": "test.binding_logs.test_binding_logs"}, {"test_name": "测试环境设置", "success": false, "message": "测试环境设置失败: 无法获取商户token", "details": {}, "timestamp": **********.1711907, "datetime": "2025-06-21T23:26:49.171190", "module": "通知管理测试", "module_path": "test.notifications.test_notifications_crud"}, {"test_name": "测试环境设置", "success": false, "message": "测试环境设置失败: 无法获取商户token", "details": {}, "timestamp": **********.4661105, "datetime": "2025-06-21T23:26:51.466110", "module": "仪表盘测试", "module_path": "test.dashboard.test_dashboard"}], "test_environment": {"server_url": "http://localhost:20000", "test_accounts": ["admin", "test1", "test_merchant_b"], "python_version": "3.12.3 (tags/v3.12.3:f6650f9, Apr  9 2024, 14:05:25) [MSC v.1938 64 bit (AMD64)]", "test_framework": "Custom Walmart Test Suite v1.0"}}