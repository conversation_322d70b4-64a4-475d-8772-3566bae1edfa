walmart-gateway-windows-secure.exe
# Python
**/__pycache__
**/*.pyc
**/*.pyo
**/*.pyd
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Virtual Environment
**/.venv
venv/
ENV/
env/
.venv/

# IDE
*.iml
.idea/
.vscode/
*.swp
*.swo
*~

# Environment Variables
.env
.env.local
.env.production
.env.staging

# Logs
**/*.log
logs/
*.log.*

# Database
*.db
*.sqlite
*.sqlite3

# Cache
.cache/
.pytest_cache/
.coverage
htmlcov/

# Temporary files
temp/
tmp/
*.tmp
*.temp

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Docker
.dockerignore

# Build artifacts
/dist_linux/walmart-bind-card-server-linux

# Secrets and sensitive data
secrets/*.txt
config.yaml
!config.yaml.example

# SSL certificates
nginx/ssl/*.crt
nginx/ssl/*.key
nginx/ssl/*.pem

# Backup files
backups/
*.backup
*.bak

# Data directories
data/
mysql_data/
redis_data/

# Nginx logs
nginx/logs/

# Test coverage
.coverage
coverage.xml
*.cover
.hypothesis/

# Jupyter Notebook
.ipynb_checkpoints

# pyenv
.python-version

# pipenv
Pipfile.lock

# PEP 582
__pypackages__/

# Celery
celerybeat-schedule
celerybeat.pid

# SageMath parsed files
*.sage.py

# Spyder project settings
.spyderproject
.spyproject

# Rope project settings
.ropeproject

# mkdocs documentation
/site

# mypy
.mypy_cache/
.dmypy.json
dmypy.json
walmart-gateway-secure
