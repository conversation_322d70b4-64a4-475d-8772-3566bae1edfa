from typing import Any, Dict, List, Optional, Union
from sqlalchemy.orm import Session

from app.crud.base import CRUDBase
from app.models.role import Role
from app.schemas.role import RoleCreate, RoleUpdate


class CRUDRole(CRUDBase[Role, RoleCreate, RoleUpdate]):
    """Role的CRUD操作类"""

    def get_by_code(self, db: Session, code: str) -> Optional[Role]:
        """通过角色代码获取角色"""
        return db.query(Role).filter(Role.code == code).first()

    def get_enabled_roles(self, db: Session) -> List[Role]:
        """获取所有启用的角色"""
        return db.query(Role).filter(Role.is_enabled == True).all()

    def get_system_roles(self, db: Session) -> List[Role]:
        """获取所有系统内置角色"""
        return db.query(Role).filter(Role.is_system == True).all()

    def get_custom_roles(self, db: Session) -> List[Role]:
        """获取所有自定义角色（非系统内置）"""
        return db.query(Role).filter(Role.is_system == False).all()

    def get_by_data_scope(self, db: Session, data_scope: str) -> List[Role]:
        """获取指定数据范围的角色"""
        return db.query(Role).filter(Role.data_scope == data_scope).all()

    def assign_permissions(self, db: Session, role_id: int, permission_ids: List[int]) -> Role:
        """为角色分配权限"""
        from app.models.permission import Permission
        
        role = self.get(db, id=role_id)
        if not role:
            return None
        
        permissions = db.query(Permission).filter(Permission.id.in_(permission_ids)).all()
        role.permissions = permissions
        
        db.commit()
        db.refresh(role)
        
        return role

    def assign_menus(self, db: Session, role_id: int, menu_ids: List[int]) -> Role:
        """为角色分配菜单"""
        from app.models.menu import Menu
        
        role = self.get(db, id=role_id)
        if not role:
            return None
        
        menus = db.query(Menu).filter(Menu.id.in_(menu_ids)).all()
        role.menus = menus
        
        db.commit()
        db.refresh(role)
        
        return role

    def assign_users(self, db: Session, role_id: int, user_ids: List[int]) -> Role:
        """为角色分配用户"""
        from app.models.user import User
        
        role = self.get(db, id=role_id)
        if not role:
            return None
        
        users = db.query(User).filter(User.id.in_(user_ids)).all()
        role.users = users
        
        db.commit()
        db.refresh(role)
        
        return role


role = CRUDRole(Role)
