<template>
    <div class="security-audit">
        <el-card class="box-card">
            <template #header>
                <div class="card-header">
                    <span>安全审计</span>
                    <div class="header-actions">
                        <el-button type="primary" @click="exportAuditLogs">导出审计日志</el-button>
                    </div>
                </div>
            </template>

            <!-- 租户上下文提示 -->
            <div v-if="merchantStore.isMerchantMode" class="merchant-context-tip">
                <el-alert type="info" :closable="false">
                    当前显示租户 "{{ merchantStore.currentMerchantName }}" 的安全审计记录
                    <el-button type="primary" link @click="viewAllAuditLogs">查看所有记录</el-button>
                </el-alert>
            </div>

            <!-- 筛选区域 -->
            <el-form :inline="true" :model="searchForm" class="search-form">
                <el-form-item label="风险级别">
                    <el-select v-model="searchForm.riskLevel" placeholder="请选择风险级别" clearable>
                        <el-option label="低风险" value="low" />
                        <el-option label="中风险" value="medium" />
                        <el-option label="高风险" value="high" />
                        <el-option label="严重风险" value="critical" />
                    </el-select>
                </el-form-item>
                <el-form-item label="操作类型">
                    <el-select v-model="searchForm.operationType" placeholder="请选择操作类型" clearable>
                        <el-option label="敏感数据访问" value="DATA_ACCESS" />
                        <el-option label="权限变更" value="PERMISSION_CHANGE" />
                        <el-option label="系统配置修改" value="CONFIG_CHANGE" />
                        <el-option label="认证操作" value="AUTHENTICATION" />
                        <el-option label="API调用" value="API_CALL" />
                        <el-option label="会话操作" value="SESSION" />
                        <el-option label="资源使用" value="RESOURCE" />
                        <el-option label="外部API" value="EXTERNAL_API" />
                        <el-option label="批量操作" value="BATCH" />
                        <el-option label="异常行为" value="ABNORMAL_BEHAVIOR" />
                    </el-select>
                </el-form-item>
                <el-form-item label="时间范围">
                    <el-date-picker v-model="searchForm.timeRange" type="daterange" range-separator="至"
                        start-placeholder="开始日期" end-placeholder="结束日期" value-format="YYYY-MM-DD" />
                </el-form-item>
                <el-form-item label="事件类别">
                    <el-select v-model="searchForm.category" placeholder="请选择事件类别" clearable>
                        <el-option label="认证" value="AUTHENTICATION" />
                        <el-option label="数据访问" value="DATA_ACCESS" />
                        <el-option label="配置" value="CONFIG" />
                        <el-option label="权限" value="PERMISSION" />
                        <el-option label="会话" value="SESSION" />
                        <el-option label="资源" value="RESOURCE" />
                        <el-option label="外部API" value="EXTERNAL_API" />
                        <el-option label="安全" value="SECURITY" />
                    </el-select>
                </el-form-item>
                <el-form-item label="操作用户">
                    <el-input v-model="searchForm.username" placeholder="输入用户名" clearable />
                </el-form-item>
                <div class="search-form-row">
                    <el-form-item label="影响范围">
                        <el-select v-model="searchForm.impactSeverity" placeholder="选择影响严重性" clearable>
                            <el-option label="低" value="LOW" />
                            <el-option label="中" value="MEDIUM" />
                            <el-option label="高" value="HIGH" />
                        </el-select>
                    </el-form-item>
                    <el-form-item label="资源类型">
                        <el-select v-model="searchForm.resourceType" placeholder="选择资源类型" clearable>
                            <el-option label="数据库" value="database" />
                            <el-option label="文件系统" value="file_system" />
                            <el-option label="用户权限" value="user_permissions" />
                            <el-option label="系统配置" value="system_config" />
                            <el-option label="敏感数据" value="sensitive_data" />
                        </el-select>
                    </el-form-item>
                    <el-form-item label="设备类型">
                        <el-select v-model="searchForm.deviceType" placeholder="选择设备类型" clearable>
                            <el-option label="PC" value="desktop" />
                            <el-option label="移动设备" value="mobile" />
                            <el-option label="平板" value="tablet" />
                        </el-select>
                    </el-form-item>
                </div>
                <div class="search-form-actions">
                    <el-form-item>
                        <el-button type="primary" @click="handleSearch">搜索</el-button>
                        <el-button @click="resetSearch">重置</el-button>
                        <el-button link @click="toggleAdvancedSearch">
                            {{ showAdvancedSearch ? '收起高级搜索' : '展开高级搜索' }}
                            <el-icon>
                                <ArrowDown v-if="!showAdvancedSearch" />
                                <ArrowUp v-else />
                            </el-icon>
                        </el-button>
                    </el-form-item>
                </div>
            </el-form>

            <!-- 审计日志表格 -->
            <el-table :data="auditLogs" style="width: 100%" border v-loading="loading"
                :row-class-name="getRowClassName">
                <el-table-column prop="id" label="ID" width="60" />
                <el-table-column prop="timestamp" label="时间" width="160" />
                <el-table-column prop="riskLevel" label="风险级别" width="100">
                    <template #default="scope">
                        <el-tag :type="getRiskLevelType(scope.row.riskLevel)" effect="dark">
                            {{ formatRiskLevel(scope.row.riskLevel) }}
                        </el-tag>
                    </template>
                </el-table-column>
                <el-table-column prop="operationType" label="操作类型" width="120" />
                <el-table-column prop="username" label="操作用户" width="120" />
                <el-table-column prop="merchantName" label="相关租户" width="120" v-if="!merchantStore.isMerchantMode" />
                <el-table-column prop="ip" label="IP地址" width="120" />
                <el-table-column prop="description" label="操作描述" min-width="250" show-overflow-tooltip />
                <el-table-column label="操作" width="80" fixed="right">
                    <template #default="scope">
                        <el-button type="primary" size="small" @click="viewAuditDetail(scope.row)" text>
                            详情
                        </el-button>
                    </template>
                </el-table-column>
            </el-table>

            <!-- 分页 -->
            <div class="pagination-container">
                <el-pagination v-model:current-page="currentPage" v-model:page-size="pageSize"
                    :page-sizes="[10, 20, 50, 100]" layout="total, sizes, prev, pager, next, jumper" :total="totalCount"
                    @update:page-size="handleSizeChange" @update:current-page="handleCurrentChange" />
            </div>
        </el-card>

        <!-- 审计详情对话框 -->
        <el-dialog v-model="dialogVisible" title="安全审计详情" width="70%">
            <div v-if="currentAudit" class="audit-detail">
                <el-descriptions :column="2" border>
                    <el-descriptions-item label="事件ID">{{ currentAudit.id }}</el-descriptions-item>
                    <el-descriptions-item label="时间">{{ currentAudit.timestamp }}</el-descriptions-item>
                    <el-descriptions-item label="风险级别">
                        <el-tag :type="getRiskLevelType(currentAudit.riskLevel)" effect="dark">
                            {{ formatRiskLevel(currentAudit.riskLevel) }}
                        </el-tag>
                    </el-descriptions-item>
                    <el-descriptions-item label="操作类型">{{ currentAudit.operationType }}</el-descriptions-item>
                    <el-descriptions-item label="操作用户">{{ currentAudit.username }}</el-descriptions-item>
                    <el-descriptions-item label="IP地址">
                        {{ currentAudit.ip }}
                        <el-tag size="small" v-if="currentAudit.location">{{ currentAudit.location }}</el-tag>
                    </el-descriptions-item>
                    <el-descriptions-item label="相关租户" v-if="currentAudit.merchantName">{{ currentAudit.merchantName
                        }}</el-descriptions-item>
                    <el-descriptions-item label="请求方法" :span="currentAudit.merchantName ? 1 : 2">{{ currentAudit.method
                        }}</el-descriptions-item>
                    <el-descriptions-item label="用户代理" :span="2">{{ currentAudit.userAgent }}</el-descriptions-item>
                    <el-descriptions-item label="操作描述" :span="2">{{ currentAudit.description }}</el-descriptions-item>

                    <!-- 新增：显示设备信息 -->
                    <el-descriptions-item label="设备信息" :span="2" v-if="currentAudit.deviceInfo">
                        <div class="device-info">
                            <el-tag size="small" type="info">{{ currentAudit.deviceInfo.os }} {{
                                currentAudit.deviceInfo.osVersion }}</el-tag>
                            <el-tag size="small" type="info">{{ currentAudit.deviceInfo.browser }} {{
                                currentAudit.deviceInfo.browserVersion }}</el-tag>
                            <el-tag size="small" type="info">{{ currentAudit.deviceInfo.deviceType }}</el-tag>
                        </div>
                    </el-descriptions-item>

                    <!-- 新增：显示关联ID -->
                    <el-descriptions-item label="关联ID" v-if="currentAudit.correlationId">
                        {{ currentAudit.correlationId }}
                        <el-button type="primary" link size="small"
                            @click="findRelatedEvents(currentAudit.correlationId)">
                            查找相关事件
                        </el-button>
                    </el-descriptions-item>

                    <!-- 新增：显示类别 -->
                    <el-descriptions-item label="事件类别" v-if="currentAudit.category">
                        <el-tag>{{ currentAudit.category }}</el-tag>
                    </el-descriptions-item>

                    <!-- 新增：显示影响范围 -->
                    <el-descriptions-item label="影响范围" :span="2" v-if="currentAudit.impactScope">
                        <div class="impact-scope">
                            <div v-if="currentAudit.impactScope.recordCount">
                                <strong>影响记录数：</strong> {{ currentAudit.impactScope.recordCount }}
                            </div>
                            <div
                                v-if="currentAudit.impactScope.resourceTypes && currentAudit.impactScope.resourceTypes.length">
                                <strong>资源类型：</strong>
                                <el-tag v-for="(type, index) in currentAudit.impactScope.resourceTypes" :key="index"
                                    size="small" class="resource-tag">
                                    {{ type }}
                                </el-tag>
                            </div>
                            <div v-if="currentAudit.impactScope.severity">
                                <strong>影响严重性：</strong>
                                <el-tag :type="getRiskLevelType(currentAudit.impactScope.severity)" size="small">
                                    {{ formatRiskLevel(currentAudit.impactScope.severity) }}
                                </el-tag>
                            </div>
                        </div>
                    </el-descriptions-item>

                    <!-- 新增：显示风险因素 -->
                    <el-descriptions-item label="风险因素" :span="2"
                        v-if="currentAudit.riskFactors && currentAudit.riskFactors.length">
                        <el-tag v-for="(factor, index) in currentAudit.riskFactors" :key="index" type="danger"
                            class="risk-factor-tag">
                            {{ factor }}
                        </el-tag>
                    </el-descriptions-item>
                </el-descriptions>

                <div class="detail-section" v-if="currentAudit.requestData">
                    <h4>请求数据</h4>
                    <pre>{{ formatJson(currentAudit.requestData) }}</pre>
                </div>

                <div class="detail-section" v-if="currentAudit.responseData">
                    <h4>响应数据</h4>
                    <pre>{{ formatJson(currentAudit.responseData) }}</pre>
                </div>

                <div class="detail-section" v-if="currentAudit.changes && currentAudit.changes.length">
                    <h4>数据变更详情</h4>
                    <el-table :data="currentAudit.changes" border>
                        <el-table-column prop="field" label="字段" width="150" />
                        <el-table-column prop="oldValue" label="原始值" />
                        <el-table-column prop="newValue" label="新值" />
                        <el-table-column label="变更类型" width="100">
                            <template #default="scope">
                                <el-tag size="small" :type="getChangeTypeTag(scope.row.type)">
                                    {{ formatChangeType(scope.row.type) }}
                                </el-tag>
                            </template>
                        </el-table-column>
                        <el-table-column label="敏感字段" width="100" align="center">
                            <template #default="scope">
                                <el-tag v-if="scope.row.sensitiveField" size="small" type="danger">是</el-tag>
                                <span v-else>否</span>
                            </template>
                        </el-table-column>
                    </el-table>
                </div>

                <div class="detail-section" v-if="currentAudit.similarEvents && currentAudit.similarEvents.length">
                    <h4>相关事件</h4>
                    <el-table :data="currentAudit.similarEvents" border>
                        <el-table-column prop="id" label="事件ID" width="80" />
                        <el-table-column prop="timestamp" label="时间" width="160" />
                        <el-table-column prop="operationType" label="操作类型" width="120" />
                        <el-table-column prop="description" label="描述" />
                        <el-table-column label="操作" width="80">
                            <template #default="scope">
                                <el-button type="primary" size="small" @click="viewAuditDetail(scope.row)" text>
                                    详情
                                </el-button>
                            </template>
                        </el-table-column>
                    </el-table>
                </div>

                <!-- 新增：关联事件列表 -->
                <div class="detail-section" v-if="relatedEvents && relatedEvents.length">
                    <h4>关联事件 ({{ relatedEventsTotalCount || relatedEvents.length }})</h4>
                    <el-table :data="relatedEvents" border>
                        <el-table-column prop="id" label="事件ID" width="100" />
                        <el-table-column label="时间" width="160">
                            <template #default="scope">
                                {{ formatTimeWithDiff(scope.row.timestamp, scope.row.timeDiff) }}
                            </template>
                        </el-table-column>
                        <el-table-column prop="operationType" label="操作类型" width="150" />
                        <el-table-column prop="username" label="操作用户" width="120" />
                        <el-table-column label="操作" width="80">
                            <template #default="scope">
                                <el-button type="primary" size="small" @click="viewRelatedEvent(scope.row.id)" text>
                                    详情
                                </el-button>
                            </template>
                        </el-table-column>
                    </el-table>
                </div>
            </div>
        </el-dialog>
    </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useMerchantStore } from '../../store/merchant.js'
import { useMerchantFilter } from '../../composables/useMerchantFilter.js'
import { correlateEvents } from '../../utils/securityAudit.js' // 导入事件关联函数
import { ArrowDown, ArrowUp } from '@element-plus/icons-vue'

// 获取租户Store
const merchantStore = useMerchantStore()

// 搜索表单
const searchForm = reactive({
    riskLevel: '',
    operationType: '',
    timeRange: [],
    category: '',
    username: '',
    impactSeverity: '',
    resourceType: '',
    deviceType: ''
})

// 高级搜索控制
const showAdvancedSearch = ref(false)

// 切换高级搜索显示
function toggleAdvancedSearch() {
    showAdvancedSearch.value = !showAdvancedSearch.value
}

// 使用租户过滤器
const { merchantQueryParams, fetchMerchantData } = useMerchantFilter({
    fetchData: fetchAuditLogs,
    queryParams: computed(() => searchForm),
    merchantKey: 'merchantId'
})

// 表格数据
const auditLogs = ref([])
const loading = ref(false)
const currentPage = ref(1)
const pageSize = ref(10)
const totalCount = ref(0)

// 详情对话框
const dialogVisible = ref(false)
const currentAudit = ref(null)

// 新增：关联事件数据
const relatedEvents = ref([])
const relatedEventsTotalCount = ref(0)

// 监听租户变化
watch(() => merchantStore.currentMerchant, () => {
    if (auditLogs.value.length > 0) {
        currentPage.value = 1 // 切换租户时重置到第一页
        fetchAuditLogs()
    }
}, { immediate: false })

// 组件挂载时获取审计日志
onMounted(() => {
    fetchMerchantData()
})

// 获取审计日志
function fetchAuditLogs(params = {}) {
    loading.value = true

    // 合并查询参数
    const queryParams = { ...params }

    // 添加分页参数
    queryParams.page = currentPage.value
    queryParams.pageSize = pageSize.value

    // 处理时间范围
    if (queryParams.timeRange && queryParams.timeRange.length === 2) {
        queryParams.startDate = queryParams.timeRange[0]
        queryParams.endDate = queryParams.timeRange[1]
        delete queryParams.timeRange
    }

    // 模拟API请求
    return new Promise(resolve => {
        setTimeout(() => {
            // 生成模拟数据
            const mockLogs = []
            const operationTypes = ['DATA_ACCESS', 'PERMISSION_CHANGE', 'CONFIG_CHANGE', 'AUTHENTICATION', 'API_CALL']
            const riskLevels = ['low', 'medium', 'high', 'critical']
            const usernames = ['admin', 'john.doe', 'support', 'merchant_admin']
            const merchants = [
                { id: 1, name: '沃尔玛超市' },
                { id: 2, name: '沃尔玛商城' },
                { id: 3, name: '沃尔玛便利店' },
                { id: 4, name: '山姆会员店' }
            ]
            const methods = ['GET', 'POST', 'PUT', 'DELETE']

            for (let i = 1; i <= 100; i++) {
                const opTypeIndex = Math.floor(Math.random() * operationTypes.length)
                const riskIndex = Math.floor(Math.random() * riskLevels.length)
                const usernameIndex = Math.floor(Math.random() * usernames.length)
                const merchantIndex = Math.floor(Math.random() * merchants.length)
                const methodIndex = Math.floor(Math.random() * methods.length)
                const merchant = merchants[merchantIndex]

                // 创建审计记录
                const audit = {
                    id: 1000 + i,
                    timestamp: new Date(Date.now() - Math.floor(Math.random() * 30) * 86400000).toISOString().replace('T', ' ').substring(0, 19),
                    riskLevel: riskLevels[riskIndex],
                    operationType: operationTypes[opTypeIndex],
                    username: usernames[usernameIndex],
                    ip: `192.168.${Math.floor(Math.random() * 255)}.${Math.floor(Math.random() * 255)}`,
                    location: Math.random() > 0.5 ? '北京' : (Math.random() > 0.5 ? '上海' : '广州'),
                    method: `${methods[methodIndex]} /api/v1/${operationTypes[opTypeIndex].toLowerCase()}`,
                    merchantId: merchant.id,
                    merchantName: merchant.name,
                    description: generateDescription(operationTypes[opTypeIndex], usernames[usernameIndex], merchant.name),
                    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
                    requestData: generateRequestData(operationTypes[opTypeIndex]),
                    responseData: generateResponseData(operationTypes[opTypeIndex]),
                    changes: generateChanges(operationTypes[opTypeIndex]),
                    similarEvents: i % 5 === 0 ? generateSimilarEvents(i) : [],
                    deviceInfo: {
                        os: 'Windows 10',
                        osVersion: '22H2',
                        browser: 'Chrome',
                        browserVersion: '91.0.4472.124',
                        deviceType: 'PC'
                    },
                    correlationId: `CORR-${i}`,
                    category: Math.random() > 0.5 ? '数据访问' : '权限变更',
                    impactScope: {
                        recordCount: Math.floor(Math.random() * 1000),
                        resourceTypes: ['数据库', '文件系统', '网络'],
                        severity: riskLevels[Math.floor(Math.random() * riskLevels.length)]
                    },
                    riskFactors: ['数据泄露风险', '权限管理不善', '系统配置错误']
                }

                mockLogs.push(audit)
            }

            // 应用租户过滤
            let filteredLogs = [...mockLogs]

            // 按租户ID过滤
            if (queryParams.merchantId) {
                filteredLogs = filteredLogs.filter(log => log.merchantId === queryParams.merchantId)
            }

            // 按风险级别过滤
            if (queryParams.riskLevel) {
                filteredLogs = filteredLogs.filter(log => log.riskLevel === queryParams.riskLevel)
            }

            // 按操作类型过滤
            if (queryParams.operationType) {
                filteredLogs = filteredLogs.filter(log => log.operationType === queryParams.operationType)
            }

            // 按时间范围过滤
            if (queryParams.startDate && queryParams.endDate) {
                const startDate = new Date(queryParams.startDate)
                const endDate = new Date(queryParams.endDate)
                endDate.setHours(23, 59, 59, 999) // 设置为当天结束

                filteredLogs = filteredLogs.filter(log => {
                    const logDate = new Date(log.timestamp)
                    return logDate >= startDate && logDate <= endDate
                })
            }

            // 计算总数
            totalCount.value = filteredLogs.length

            // 应用分页
            const startIndex = (queryParams.page - 1) * queryParams.pageSize
            const endIndex = startIndex + queryParams.pageSize

            auditLogs.value = filteredLogs.slice(startIndex, endIndex)
            loading.value = false

            resolve({
                items: auditLogs.value,
                total: totalCount.value
            })
        }, 500)
    })
}

// 生成描述
function generateDescription(operationType, username, merchantName) {
    const descriptions = {
        'DATA_ACCESS': [
            `用户 ${username} 访问了租户 ${merchantName} 的敏感数据`,
            `用户 ${username} 导出了租户 ${merchantName} 的客户信息`,
            `用户 ${username} 批量查询了租户 ${merchantName} 的交易记录`
        ],
        'PERMISSION_CHANGE': [
            `用户 ${username} 修改了租户 ${merchantName} 的权限设置`,
            `用户 ${username} 为租户 ${merchantName} 添加了新角色`,
            `用户 ${username} 更新了租户 ${merchantName} 的API访问权限`
        ],
        'CONFIG_CHANGE': [
            `用户 ${username} 修改了租户 ${merchantName} 的系统配置`,
            `用户 ${username} 更新了租户 ${merchantName} 的通知设置`,
            `用户 ${username} 变更了租户 ${merchantName} 的安全策略`
        ],
        'AUTHENTICATION': [
            `用户 ${username} 尝试登录租户 ${merchantName} 账户`,
            `用户 ${username} 修改了租户 ${merchantName} 的密码`,
            `用户 ${username} 重置了租户 ${merchantName} 的API密钥`
        ],
        'API_CALL': [
            `用户 ${username} 调用了租户 ${merchantName} 的绑卡API`,
            `用户 ${username} 请求了租户 ${merchantName} 的数据同步接口`,
            `用户 ${username} 访问了租户 ${merchantName} 的批量处理API`
        ]
    }

    const options = descriptions[operationType] || [`用户 ${username} 对租户 ${merchantName} 执行了操作`]
    return options[Math.floor(Math.random() * options.length)]
}

// 生成请求数据
function generateRequestData(operationType) {
    const data = {
        'DATA_ACCESS': {
            filters: {
                dateRange: ['2023-01-01', '2023-12-31'],
                limit: 1000,
                fields: ['id', 'name', 'amount', 'createTime']
            }
        },
        'PERMISSION_CHANGE': {
            roleId: 5,
            permissions: ['read', 'write', 'delete'],
            scope: 'department'
        },
        'CONFIG_CHANGE': {
            settings: {
                notificationEnabled: true,
                apiTimeout: 30,
                maxRetries: 3
            }
        },
        'AUTHENTICATION': {
            username: 'john.doe',
            tokenValidityHours: 24,
            deviceInfo: {
                browser: 'Chrome',
                os: 'Windows 10'
            }
        },
        'API_CALL': {
            cardInfo: {
                cardType: 'GIFT',
                amount: 100,
                currency: 'CNY'
            }
        }
    }

    return data[operationType] || { data: 'Sample request data' }
}

// 生成响应数据
function generateResponseData(operationType) {
    const status = Math.random() > 0.2 ? 'success' : 'error'
    const errorMessage = 'Operation failed due to insufficient permissions'

    if (status === 'error') {
        return {
            status,
            code: 403,
            message: errorMessage
        }
    }

    const data = {
        'DATA_ACCESS': {
            status: 'success',
            count: Math.floor(Math.random() * 1000),
            page: 1,
            pageSize: 50,
            data: '[... 数据已省略 ...]'
        },
        'PERMISSION_CHANGE': {
            status: 'success',
            updated: true,
            timestamp: new Date().toISOString()
        },
        'CONFIG_CHANGE': {
            status: 'success',
            updated: true,
            requiresRestart: Math.random() > 0.5
        },
        'AUTHENTICATION': {
            status: 'success',
            token: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...',
            expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString()
        },
        'API_CALL': {
            status: 'success',
            transactionId: `TX-${Date.now()}`,
            processedAt: new Date().toISOString()
        }
    }

    return data[operationType] || { status: 'success', message: 'Operation completed successfully' }
}

// 生成变更记录
function generateChanges(operationType) {
    if (['PERMISSION_CHANGE', 'CONFIG_CHANGE'].includes(operationType)) {
        const changes = []

        if (operationType === 'PERMISSION_CHANGE') {
            changes.push(
                { field: 'permissions.canExport', oldValue: 'false', newValue: 'true' },
                { field: 'permissions.dataAccess', oldValue: 'department', newValue: 'all' }
            )
        } else {
            changes.push(
                { field: 'config.apiTimeout', oldValue: '15', newValue: '30' },
                { field: 'config.notificationEnabled', oldValue: 'false', newValue: 'true' }
            )
        }

        return changes
    }

    return []
}

// 生成相似事件
function generateSimilarEvents(baseId) {
    const events = []

    for (let i = 1; i <= 3; i++) {
        events.push({
            id: 2000 + baseId + i,
            timestamp: new Date(Date.now() - i * 30000).toISOString().replace('T', ' ').substring(0, 19),
            operationType: 'AUTHENTICATION',
            description: '用户尝试登录系统'
        })
    }

    return events
}

// 格式化风险级别
function formatRiskLevel(level) {
    const map = {
        'low': '低风险',
        'medium': '中风险',
        'high': '高风险',
        'critical': '严重风险'
    }
    return map[level] || level
}

// 获取风险级别对应的标签类型
function getRiskLevelType(level) {
    const map = {
        'low': 'info',
        'medium': 'warning',
        'high': 'danger',
        'critical': 'danger'
    }
    return map[level] || 'info'
}

// 获取行的类名（基于风险级别）
function getRowClassName({ row }) {
    if (row.riskLevel === 'critical') {
        return 'critical-risk-row'
    }
    return ''
}

// 格式化JSON数据
function formatJson(json) {
    if (!json) return ''
    try {
        return JSON.stringify(json, null, 2)
    } catch (e) {
        return JSON.stringify(json)
    }
}

// 查看审计详情
function viewAuditDetail(audit) {
    currentAudit.value = audit
    dialogVisible.value = true
}

// 搜索操作
function handleSearch() {
    currentPage.value = 1
    fetchMerchantData()
}

// 重置搜索
function resetSearch() {
    searchForm.riskLevel = ''
    searchForm.operationType = ''
    searchForm.timeRange = []
    searchForm.category = ''
    searchForm.username = ''
    searchForm.impactSeverity = ''
    searchForm.resourceType = ''
    searchForm.deviceType = ''
    handleSearch()
}

// 导出审计日志
function exportAuditLogs() {
    const merchantTag = merchantStore.isMerchantMode ? ` - ${merchantStore.currentMerchantName}` : ''
    ElMessageBox.confirm(
        `确定要导出安全审计日志${merchantTag}吗？`,
        '导出确认',
        {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
        }
    )
        .then(() => {
            ElMessage.success(`安全审计日志导出已开始，完成后将通知您下载`)
        })
        .catch(() => {
            // 用户取消操作
        })
}

// 查看所有安全审计记录
function viewAllAuditLogs() {
    merchantStore.switchMerchant(null)
}

// 分页操作
function handleSizeChange(val) {
    pageSize.value = val
    fetchMerchantData()
}

function handleCurrentChange(val) {
    currentPage.value = val
    fetchMerchantData()
}

// 新增：查找关联事件
function findRelatedEvents(correlationId) {
    // 使用correlateEvents函数模拟
    const result = correlateEvents(correlationId, {
        timeWindow: 10 * 60 * 1000, // 10分钟
        maxEvents: 10
    })

    // 如果没有关联事件，可能是因为在前端无法访问服务器端存储的事件
    // 为了演示，生成模拟数据
    if (!result.events.length) {
        const mockRelatedEvents = generateMockRelatedEvents(correlationId)
        relatedEvents.value = mockRelatedEvents
        relatedEventsTotalCount.value = mockRelatedEvents.length + Math.floor(Math.random() * 10)
    } else {
        relatedEvents.value = result.events
        relatedEventsTotalCount.value = result.totalFound
    }
}

// 生成模拟关联事件
function generateMockRelatedEvents(correlationId) {
    const events = []
    const baseTime = Date.now()
    const operationTypes = ['LOGIN_ATTEMPT', 'DATA_ACCESS', 'PERMISSION_CHANGE', 'CONFIG_CHANGE']
    const usernames = ['admin', 'john.doe', 'support', 'merchant_admin']

    for (let i = 1; i <= 5; i++) {
        // 生成前后2分钟内的随机时间戳
        const timeDiff = (Math.random() * 240000) - 120000 // -2分钟到+2分钟
        const timestamp = baseTime + timeDiff

        events.push({
            id: `evt-${Date.now()}-${i}`,
            timestamp: timestamp,
            timeDiff: timeDiff,
            operationType: operationTypes[Math.floor(Math.random() * operationTypes.length)],
            username: usernames[Math.floor(Math.random() * usernames.length)]
        })
    }

    // 按时间差排序
    return events.sort((a, b) => Math.abs(a.timeDiff) - Math.abs(b.timeDiff))
}

// 查看关联事件
function viewRelatedEvent(eventId) {
    // 实际项目中应该通过API获取事件详情
    // 这里简单处理：如果是模拟数据，就创建一个模拟的事件详情
    const event = relatedEvents.value.find(e => e.id === eventId)
    if (!event) return

    const mockDetails = {
        id: event.id,
        timestamp: new Date(event.timestamp).toISOString().replace('T', ' ').substring(0, 19),
        riskLevel: ['low', 'medium', 'high'][Math.floor(Math.random() * 3)],
        operationType: event.operationType,
        username: event.username,
        ip: `192.168.${Math.floor(Math.random() * 255)}.${Math.floor(Math.random() * 255)}`,
        location: Math.random() > 0.5 ? '北京' : '上海',
        method: `${['GET', 'POST', 'PUT'][Math.floor(Math.random() * 3)]} /api/v1/${event.operationType.toLowerCase()}`,
        description: `用户 ${event.username} 执行了 ${event.operationType} 操作`,
        userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
        correlationId: event.id
    }

    // 显示事件详情
    currentAudit.value = mockDetails
}

// 格式化带时间差的时间
function formatTimeWithDiff(timestamp, diff) {
    const date = new Date(timestamp)
    const formattedTime = date.toISOString().replace('T', ' ').substring(0, 19)

    if (diff === undefined) return formattedTime

    const diffSeconds = Math.abs(Math.round(diff / 1000))
    const sign = diff >= 0 ? '+' : '-'

    if (diffSeconds < 60) {
        return `${formattedTime} (${sign}${diffSeconds}秒)`
    } else if (diffSeconds < 3600) {
        return `${formattedTime} (${sign}${Math.floor(diffSeconds / 60)}分钟)`
    } else {
        return `${formattedTime} (${sign}${Math.floor(diffSeconds / 3600)}小时${Math.floor((diffSeconds % 3600) / 60)}分钟)`
    }
}

// 获取变更类型对应的标签类型
function getChangeTypeTag(type) {
    const map = {
        'added': 'success',
        'deleted': 'danger',
        'changed': 'warning'
    }
    return map[type] || 'info'
}

// 格式化变更类型
function formatChangeType(type) {
    const map = {
        'added': '新增',
        'deleted': '删除',
        'changed': '修改'
    }
    return map[type] || type
}
</script>

<style scoped>
.security-audit {
    width: 100%;
}

.card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.header-actions {
    display: flex;
    gap: 10px;
}

.search-form {
    margin-bottom: 20px;
}

.merchant-context-tip {
    margin-bottom: 15px;
}

.pagination-container {
    margin-top: 20px;
    display: flex;
    justify-content: flex-end;
}

.audit-detail {
    margin-top: 10px;
}

.detail-section {
    margin-top: 20px;
    margin-bottom: 20px;
}

.detail-section h4 {
    margin-bottom: 10px;
    font-weight: 500;
    color: #606266;
}

pre {
    background-color: #f5f7fa;
    padding: 10px;
    border-radius: 4px;
    max-height: 200px;
    overflow: auto;
    font-size: 12px;
    margin: 0;
}

:deep(.critical-risk-row) {
    background-color: rgba(245, 108, 108, 0.1);
}

/* 新增样式 */
.device-info {
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
}

.impact-scope {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.resource-tag {
    margin-right: 5px;
}

.risk-factor-tag {
    margin: 0 5px 5px 0;
}

.search-form-row {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    margin-top: 10px;
    transition: all 0.3s;
    max-height: v-bind(showAdvancedSearch ? '500px' : '0');
    overflow: hidden;
    opacity: v-bind(showAdvancedSearch ? 1 : 0);
}

.search-form-actions {
    width: 100%;
    display: flex;
    justify-content: flex-end;
    margin-top: 10px;
}
</style>