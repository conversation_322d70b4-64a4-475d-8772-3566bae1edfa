"""
Telegram机器人向后兼容性测试
确保新的配置系统不会破坏现有功能
"""

import pytest
from sqlalchemy.orm import Session

from app.services.telegram_config_service import TelegramConfigService
from app.telegram_bot.services.permission_service import TelegramPermissionService
from app.models.telegram_group import TelegramGroup, BindStatus, ChatType
from app.models.telegram_user import TelegramUser, VerificationStatus
from app.models.merchant import Merchant
from app.models.user import User


class TestBackwardCompatibility:
    """测试向后兼容性"""
    
    def test_existing_groups_without_settings(self, db: Session, test_merchant: Merchant):
        """测试没有配置的现有群组能正常工作"""
        config_service = TelegramConfigService(db)
        
        # 创建没有settings的群组（模拟现有数据）
        group = TelegramGroup(
            chat_id=123456789,
            chat_title="现有群组",
            chat_type=ChatType.GROUP,
            merchant_id=test_merchant.id,
            bind_token="existing_token",
            bind_status=BindStatus.ACTIVE,
            settings=None  # 现有群组可能没有配置
        )
        db.add(group)
        db.commit()
        
        # 获取有效配置应该返回默认值
        effective_settings = config_service.get_effective_group_settings(group)
        
        # 验证默认配置被正确应用
        assert effective_settings is not None
        assert "permissions" in effective_settings
        assert "display_settings" in effective_settings
        assert effective_settings["permissions"]["allow_all_members"] is False
        assert effective_settings["permissions"]["require_user_verification"] is True
    
    def test_existing_users_without_telegram_settings(self, db: Session, test_user: User):
        """测试没有Telegram设置的现有用户"""
        permission_service = TelegramPermissionService(db)
        
        # 创建没有特殊设置的Telegram用户
        telegram_user = TelegramUser(
            telegram_user_id=123456789,
            telegram_username="existing_user",
            system_user_id=test_user.id,
            verification_status=VerificationStatus.VERIFIED,
            settings=None  # 现有用户可能没有设置
        )
        db.add(telegram_user)
        db.commit()
        
        # 用户验证应该正常工作
        result = await permission_service.verify_user_permissions(123456789)
        assert result.telegram_user_id == 123456789
        assert result.verification_status == VerificationStatus.VERIFIED
    
    def test_default_permission_behavior(self, db: Session, test_merchant: Merchant, test_user: User):
        """测试默认权限行为保持不变"""
        permission_service = TelegramPermissionService(db)
        
        # 创建标准群组和用户
        group = TelegramGroup(
            chat_id=123456789,
            chat_title="标准群组",
            chat_type=ChatType.GROUP,
            merchant_id=test_merchant.id,
            bind_token="standard_token",
            bind_status=BindStatus.ACTIVE
        )
        db.add(group)
        
        telegram_user = TelegramUser(
            telegram_user_id=123456789,
            telegram_username="standard_user",
            system_user_id=test_user.id,
            verification_status=VerificationStatus.VERIFIED
        )
        db.add(telegram_user)
        db.commit()
        
        # 基本权限检查应该正常工作
        result = await permission_service.check_user_group_access(
            user_id=123456789,
            chat_id=123456789
        )
        
        assert result["group_bound"] is True
        assert result["user_verified"] is True
        # 数据权限检查取决于test_user的实际权限
    
    def test_legacy_command_permissions(self, db: Session, test_merchant: Merchant, test_user: User):
        """测试传统命令权限仍然有效"""
        permission_service = TelegramPermissionService(db)
        
        # 创建群组和用户
        group = TelegramGroup(
            chat_id=123456789,
            chat_title="传统群组",
            chat_type=ChatType.GROUP,
            merchant_id=test_merchant.id,
            bind_token="legacy_token",
            bind_status=BindStatus.ACTIVE
        )
        db.add(group)
        
        telegram_user = TelegramUser(
            telegram_user_id=123456789,
            telegram_username="legacy_user",
            system_user_id=test_user.id,
            verification_status=VerificationStatus.VERIFIED
        )
        db.add(telegram_user)
        db.commit()
        
        # 测试传统命令权限检查
        try:
            user, group_obj = await permission_service.verify_command_permissions(
                user_id=123456789,
                chat_id=123456789,
                command="/help"  # 公共命令应该总是可用
            )
            # /help 命令不需要用户验证
            assert group_obj.id == group.id
        except Exception as e:
            # 如果有异常，确保是预期的权限异常，而不是系统错误
            assert "权限" in str(e) or "permission" in str(e).lower()
    
    def test_migration_from_hardcoded_to_database(self, db: Session, test_merchant: Merchant):
        """测试从硬编码配置到数据库配置的迁移"""
        config_service = TelegramConfigService(db)
        
        # 模拟迁移前的状态：群组没有配置
        group = TelegramGroup(
            chat_id=123456789,
            chat_title="迁移群组",
            chat_type=ChatType.GROUP,
            merchant_id=test_merchant.id,
            bind_token="migration_token",
            bind_status=BindStatus.ACTIVE,
            settings=None
        )
        db.add(group)
        db.commit()
        
        # 获取默认配置
        default_settings = config_service.get_effective_group_settings(group)
        
        # 验证默认配置包含所有必要字段
        required_sections = ["permissions", "display_settings", "notification_settings", "advanced_settings"]
        for section in required_sections:
            assert section in default_settings
        
        # 验证关键权限配置
        permissions = default_settings["permissions"]
        assert "allow_all_members" in permissions
        assert "require_user_verification" in permissions
        assert "admin_only_commands" in permissions
        assert "rate_limit" in permissions
        assert "query_permissions" in permissions
        
        # 验证频率限制配置
        rate_limit = permissions["rate_limit"]
        assert "commands_per_minute" in rate_limit
        assert "queries_per_hour" in rate_limit
        assert isinstance(rate_limit["commands_per_minute"], int)
        assert isinstance(rate_limit["queries_per_hour"], int)
    
    def test_gradual_migration_support(self, db: Session, test_merchant: Merchant):
        """测试渐进式迁移支持"""
        config_service = TelegramConfigService(db)
        
        # 创建部分配置的群组（模拟渐进式迁移）
        group = TelegramGroup(
            chat_id=123456789,
            chat_title="部分配置群组",
            chat_type=ChatType.GROUP,
            merchant_id=test_merchant.id,
            bind_token="partial_token",
            bind_status=BindStatus.ACTIVE,
            settings={
                "permissions": {
                    "rate_limit": {
                        "commands_per_minute": 5  # 只配置了部分设置
                    }
                }
                # 缺少其他配置节
            }
        )
        db.add(group)
        db.commit()
        
        # 获取有效配置
        effective_settings = config_service.get_effective_group_settings(group)
        
        # 验证部分配置被保留
        assert effective_settings["permissions"]["rate_limit"]["commands_per_minute"] == 5
        
        # 验证缺失的配置被默认值填充
        assert "queries_per_hour" in effective_settings["permissions"]["rate_limit"]
        assert "display_settings" in effective_settings
        assert "notification_settings" in effective_settings
        
        # 验证配置结构完整性
        assert effective_settings["permissions"]["allow_all_members"] is False
        assert effective_settings["display_settings"]["show_amount"] is True
    
    def test_api_endpoint_compatibility(self, db: Session, test_merchant: Merchant):
        """测试API端点的向后兼容性"""
        # 这个测试验证新的配置API不会破坏现有的API结构
        config_service = TelegramConfigService(db)
        
        # 创建群组
        group = TelegramGroup(
            chat_id=123456789,
            chat_title="API测试群组",
            chat_type=ChatType.GROUP,
            merchant_id=test_merchant.id,
            bind_token="api_token",
            bind_status=BindStatus.ACTIVE
        )
        db.add(group)
        db.commit()
        
        # 测试配置服务的基本方法
        group_settings = config_service.get_group_settings(group)
        merchant_settings = config_service.get_merchant_settings(test_merchant.id)
        global_settings = config_service.get_global_default_settings()
        effective_settings = config_service.get_effective_group_settings(group)
        
        # 验证返回的数据结构
        assert isinstance(group_settings, dict)
        assert isinstance(merchant_settings, dict)
        assert isinstance(global_settings, dict)
        assert isinstance(effective_settings, dict)
        
        # 验证配置验证功能
        is_valid, errors = config_service.validate_settings({
            "permissions": {
                "allow_all_members": True
            }
        })
        assert is_valid is True
        assert len(errors) == 0
    
    def test_error_handling_compatibility(self, db: Session):
        """测试错误处理的向后兼容性"""
        config_service = TelegramConfigService(db)
        permission_service = TelegramPermissionService(db)
        
        # 测试不存在的群组
        try:
            await permission_service.verify_group_permissions(999999999)
            assert False, "应该抛出GroupNotBoundError"
        except Exception as e:
            assert "群组未绑定" in str(e) or "not bound" in str(e).lower()
        
        # 测试不存在的用户
        try:
            await permission_service.verify_user_permissions(999999999)
            assert False, "应该抛出UserNotVerifiedError"
        except Exception as e:
            assert "用户未验证" in str(e) or "not verified" in str(e).lower()
        
        # 测试无效配置
        is_valid, errors = config_service.validate_settings("invalid_config")
        assert is_valid is False
        assert len(errors) > 0
