# Telegram机器人生产环境配置
# 请根据实际环境调整配置参数

# 基础配置
bot:
  # 机器人令牌（从环境变量获取）
  token: ${TELEGRAM_BOT_TOKEN}
  
  # 运行模式：polling（轮询）或 webhook（Webhook）
  mode: webhook
  
  # Webhook配置（仅在webhook模式下使用）
  webhook:
    url: ${TELEGRAM_WEBHOOK_URL}
    port: 8443
    cert_path: /path/to/cert.pem
    key_path: /path/to/private.key
    
  # 轮询配置（仅在polling模式下使用）
  polling:
    timeout: 30
    interval: 1

# 安全配置
security:
  # 最大失败尝试次数
  max_failed_attempts: 5
  
  # 账户锁定时长（分钟）
  lockout_duration_minutes: 30
  
  # 会话超时时长（分钟）
  session_timeout_minutes: 60
  
  # 是否启用IP白名单验证
  enable_ip_whitelist: true
  
  # 是否启用TOTP双因子认证
  enable_totp: true
  
  # 是否启用异常行为检测
  enable_anomaly_detection: true
  
  # 命令频率限制
  rate_limits:
    commands_per_minute: 20
    queries_per_hour: 100
    
  # 可信邮箱域名（用于自动验证）
  auto_verify_domains:
    - company.com
    - subsidiary.com

# 权限配置
permissions:
  # 默认权限模板
  default_template: employee
  
  # 分级管理配置
  hierarchical_approval:
    enabled: true
    levels:
      - name: department
        description: 部门级审批
        permissions:
          - api:telegram:user:approve
          - api:telegram:config:read
      - name: super_admin
        description: 超级管理员
        permissions:
          - "*"
  
  # 权限继承配置
  inheritance:
    enabled: true
    # 基于组织架构自动分配权限
    auto_assign_by_department: true

# LDAP/AD集成配置
ldap:
  # 是否启用LDAP集成
  enabled: true
  
  # LDAP服务器配置
  server: ldap.company.com
  port: 389
  use_ssl: false
  
  # 绑定账户
  bind_dn: CN=telegram-bot,OU=Service Accounts,DC=company,DC=com
  bind_password: ${LDAP_BIND_PASSWORD}
  
  # 搜索基础DN
  base_dn: OU=Users,DC=company,DC=com
  
  # 用户过滤器
  user_filter: (&(objectClass=person)(!(userAccountControl:1.2.840.113556.1.4.803:=2)))
  
  # 字段映射
  field_mapping:
    username: sAMAccountName
    email: mail
    first_name: givenName
    last_name: sn
    employee_id: employeeID
    department: department
    title: title
    manager: manager
  
  # 同步配置
  sync:
    # 自动同步间隔（小时）
    auto_sync_interval: 24
    
    # 是否强制更新已存在用户
    force_update: false
    
    # 是否自动处理离职用户
    handle_departed_users: true

# 审计配置
audit:
  # 是否启用详细审计
  detailed_logging: true
  
  # 审计日志保留天数
  retention_days: 90
  
  # 需要审计的事件类型
  logged_events:
    - user_verification
    - permission_change
    - security_event
    - command_execution
    - configuration_change
  
  # 实时监控配置
  monitoring:
    enabled: true
    
    # 告警阈值
    alert_thresholds:
      failed_logins_per_hour: 10
      permission_changes_per_day: 50
      security_events_per_hour: 5
    
    # 告警通知方式
    notifications:
      email:
        enabled: true
        recipients:
          - <EMAIL>
          - <EMAIL>
      
      webhook:
        enabled: false
        url: https://hooks.slack.com/services/...

# 数据库配置
database:
  # 连接池配置
  pool_size: 20
  max_overflow: 30
  pool_timeout: 30
  
  # 查询超时（秒）
  query_timeout: 30

# 缓存配置
cache:
  # 缓存类型：redis 或 memory
  type: redis
  
  # Redis配置
  redis:
    host: ${REDIS_HOST:localhost}
    port: ${REDIS_PORT:6379}
    password: ${REDIS_PASSWORD}
    db: 0
    
  # 缓存TTL配置（秒）
  ttl:
    user_permissions: 3600
    group_settings: 1800
    ldap_data: 7200

# 监控配置
monitoring:
  # Prometheus指标
  prometheus:
    enabled: true
    port: 9090
    
  # 健康检查
  health_check:
    enabled: true
    endpoint: /health
    
  # 性能监控
  performance:
    enabled: true
    slow_query_threshold: 1000  # 毫秒

# 日志配置
logging:
  # 日志级别
  level: INFO
  
  # 日志格式
  format: json
  
  # 日志文件配置
  file:
    enabled: true
    path: /var/log/telegram-bot/
    max_size: 100MB
    backup_count: 10
  
  # 远程日志配置
  remote:
    enabled: false
    endpoint: https://logs.company.com/api/v1/logs

# 通知配置
notifications:
  # 用户验证通知
  user_verification:
    enabled: true
    template: user_verification_template
    
  # 权限变更通知
  permission_change:
    enabled: true
    template: permission_change_template
    
  # 安全事件通知
  security_event:
    enabled: true
    template: security_event_template

# 备份配置
backup:
  # 配置备份
  config_backup:
    enabled: true
    interval: daily
    retention: 30
    
  # 数据备份
  data_backup:
    enabled: true
    interval: daily
    retention: 7

# 部署配置
deployment:
  # 环境标识
  environment: production
  
  # 版本信息
  version: ${APP_VERSION}
  
  # 集群配置
  cluster:
    enabled: false
    node_id: ${NODE_ID}
    
  # 负载均衡
  load_balancer:
    enabled: false
    health_check_path: /health

# 功能开关
feature_flags:
  # 新功能开关
  enable_advanced_analytics: true
  enable_ai_assistance: false
  enable_multi_language: false
  
  # 实验性功能
  experimental:
    enable_voice_commands: false
    enable_file_sharing: false
