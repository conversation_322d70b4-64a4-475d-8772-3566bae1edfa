"""
回调监控API接口
"""
from typing import Dict, Any, List
from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session

from app.api import deps
from app.models.user import User
from app.services.callback_monitor_service import callback_monitor
from app.services.optimized_callback_service import optimized_callback_service
from app.services.integrated_callback_manager import integrated_callback_manager
from app.services.callback_service_adapter import callback_service_adapter
from app.core.logging import get_logger

logger = get_logger("callback_monitor_api")

router = APIRouter()


@router.get("/stats/current", response_model=Dict[str, Any])
async def get_current_callback_stats(
    current_user: User = Depends(deps.get_current_active_user),
):
    """
    获取当前回调统计信息
    
    权限要求:
    - "callback:monitor": 回调监控权限
    """
    try:
        stats = await callback_monitor.get_real_time_stats()
        return {
            "success": True,
            "data": stats,
            "message": "获取当前统计成功"
        }
    except Exception as e:
        logger.error(f"获取当前统计失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取统计失败: {str(e)}")


@router.get("/stats/summary", response_model=Dict[str, Any])
async def get_callback_performance_summary(
    hours: int = Query(24, ge=1, le=168, description="统计时间范围（小时）"),
    current_user: User = Depends(deps.get_current_active_user),
):
    """
    获取回调性能摘要
    
    权限要求:
    - "callback:monitor": 回调监控权限
    """
    try:
        summary = callback_monitor.get_performance_summary(hours)
        return {
            "success": True,
            "data": summary,
            "message": f"获取{hours}小时性能摘要成功"
        }
    except Exception as e:
        logger.error(f"获取性能摘要失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取摘要失败: {str(e)}")


@router.get("/stats/history", response_model=Dict[str, Any])
async def get_callback_metrics_history(
    hours: int = Query(24, ge=1, le=168, description="历史数据时间范围（小时）"),
    current_user: User = Depends(deps.get_current_active_user),
):
    """
    获取回调指标历史数据
    
    权限要求:
    - "callback:monitor": 回调监控权限
    """
    try:
        history = callback_monitor.get_metrics_history(hours)
        
        # 转换为API友好的格式
        history_data = []
        for stats in history:
            history_data.append({
                "timestamp": stats.timestamp.isoformat(),
                "period_minutes": stats.period_minutes,
                "metrics": {
                    "total_callbacks": stats.metrics.total_callbacks,
                    "successful_callbacks": stats.metrics.successful_callbacks,
                    "failed_callbacks": stats.metrics.failed_callbacks,
                    "pending_callbacks": stats.metrics.pending_callbacks,
                    "success_rate": round(stats.metrics.success_rate, 2),
                    "average_retry_count": round(stats.metrics.average_retry_count, 2),
                    "max_retry_count": stats.metrics.max_retry_count,
                    "average_processing_time": round(stats.metrics.average_processing_time, 2),
                }
            })
        
        return {
            "success": True,
            "data": {
                "history": history_data,
                "total_points": len(history_data),
                "time_range_hours": hours,
            },
            "message": f"获取{hours}小时历史数据成功"
        }
    except Exception as e:
        logger.error(f"获取历史数据失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取历史数据失败: {str(e)}")


@router.get("/alerts", response_model=Dict[str, Any])
async def get_callback_performance_alerts(
    current_user: User = Depends(deps.get_current_active_user),
):
    """
    获取回调性能告警
    
    权限要求:
    - "callback:monitor": 回调监控权限
    """
    try:
        alerts = await callback_monitor.check_performance_alerts()
        return {
            "success": True,
            "data": {
                "alerts": alerts,
                "alert_count": len(alerts),
                "has_alerts": len(alerts) > 0,
            },
            "message": "获取性能告警成功"
        }
    except Exception as e:
        logger.error(f"获取性能告警失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取告警失败: {str(e)}")


@router.post("/monitoring/start", response_model=Dict[str, Any])
async def start_callback_monitoring(
    interval_seconds: int = Query(300, ge=60, le=3600, description="监控间隔（秒）"),
    current_user: User = Depends(deps.get_current_active_user),
):
    """
    启动回调监控
    
    权限要求:
    - "callback:admin": 回调管理权限
    """
    try:
        await callback_monitor.start_monitoring(interval_seconds)
        return {
            "success": True,
            "data": {
                "monitoring_active": True,
                "interval_seconds": interval_seconds,
            },
            "message": f"回调监控已启动，间隔{interval_seconds}秒"
        }
    except Exception as e:
        logger.error(f"启动监控失败: {e}")
        raise HTTPException(status_code=500, detail=f"启动监控失败: {str(e)}")


@router.post("/monitoring/stop", response_model=Dict[str, Any])
async def stop_callback_monitoring(
    current_user: User = Depends(deps.get_current_active_user),
):
    """
    停止回调监控
    
    权限要求:
    - "callback:admin": 回调管理权限
    """
    try:
        await callback_monitor.stop_monitoring()
        return {
            "success": True,
            "data": {
                "monitoring_active": False,
            },
            "message": "回调监控已停止"
        }
    except Exception as e:
        logger.error(f"停止监控失败: {e}")
        raise HTTPException(status_code=500, detail=f"停止监控失败: {str(e)}")


@router.get("/health", response_model=Dict[str, Any])
async def get_callback_service_health():
    """
    获取回调服务健康状态

    无需权限验证的健康检查接口
    """
    try:
        current_metrics = callback_monitor.get_current_metrics()

        health_status = {
            "service": "callback_service",
            "status": "healthy",
            "monitoring_active": callback_monitor.monitoring_active,
            "timestamp": callback_monitor.performance_history[-1].timestamp.isoformat() if callback_monitor.performance_history else None,
            "metrics_available": current_metrics is not None,
            # 添加集成服务状态
            "integrated_manager": integrated_callback_manager.get_status(),
            "service_adapter": callback_service_adapter.get_status(),
        }

        if current_metrics:
            health_status.update({
                "current_success_rate": round(current_metrics.success_rate, 2),
                "current_total_callbacks": current_metrics.total_callbacks,
            })

        return {
            "success": True,
            "data": health_status,
            "message": "回调服务健康检查成功"
        }
    except Exception as e:
        logger.error(f"健康检查失败: {e}")
        return {
            "success": False,
            "data": {
                "service": "callback_service",
                "status": "unhealthy",
                "error": str(e),
            },
            "message": "回调服务健康检查失败"
        }


@router.post("/test/batch", response_model=Dict[str, Any])
async def test_callback_batch_processing(
    batch_size: int = Query(10, ge=1, le=100, description="批处理大小"),
    current_user: User = Depends(deps.get_current_active_user),
    db: Session = Depends(deps.get_db),
):
    """
    测试回调批处理功能
    
    权限要求:
    - "callback:admin": 回调管理权限
    """
    try:
        # 创建测试数据
        test_batch = []
        for i in range(batch_size):
            test_batch.append({
                "record_id": f"test-{i}",
                "merchant_id": 1,
                "retry_count": 0,
                "ext_data": None,
                "trace_id": f"test-trace-{i}",
            })
        
        # 执行批处理测试
        await optimized_callback_service.process_callback_batch(db, test_batch)
        
        return {
            "success": True,
            "data": {
                "batch_size": batch_size,
                "test_completed": True,
            },
            "message": f"批处理测试完成，处理了{batch_size}个测试回调"
        }
    except Exception as e:
        logger.error(f"批处理测试失败: {e}")
        raise HTTPException(status_code=500, detail=f"批处理测试失败: {str(e)}")
