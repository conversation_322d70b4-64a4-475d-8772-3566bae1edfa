"""
性能分析工具
用于分析测试结果的性能指标和统计数据
"""

import time
import statistics
from typing import Dict, List, Any, Optional, Tuple
from collections import defaultdict, Counter
from dataclasses import dataclass
import logging

logger = logging.getLogger(__name__)


@dataclass
class PerformanceMetrics:
    """性能指标数据类"""
    total_requests: int = 0
    successful_requests: int = 0
    failed_requests: int = 0
    total_duration: float = 0.0
    response_times: List[float] = None
    error_types: Dict[str, int] = None
    ck_usage_distribution: Dict[int, int] = None
    department_distribution: Dict[int, int] = None
    
    def __post_init__(self):
        if self.response_times is None:
            self.response_times = []
        if self.error_types is None:
            self.error_types = defaultdict(int)
        if self.ck_usage_distribution is None:
            self.ck_usage_distribution = defaultdict(int)
        if self.department_distribution is None:
            self.department_distribution = defaultdict(int)
    
    @property
    def success_rate(self) -> float:
        """成功率"""
        return self.successful_requests / self.total_requests if self.total_requests > 0 else 0.0
    
    @property
    def average_response_time(self) -> float:
        """平均响应时间"""
        return statistics.mean(self.response_times) if self.response_times else 0.0
    
    @property
    def median_response_time(self) -> float:
        """响应时间中位数"""
        return statistics.median(self.response_times) if self.response_times else 0.0
    
    @property
    def p95_response_time(self) -> float:
        """95百分位响应时间"""
        if not self.response_times:
            return 0.0
        sorted_times = sorted(self.response_times)
        index = int(len(sorted_times) * 0.95)
        return sorted_times[index] if index < len(sorted_times) else sorted_times[-1]
    
    @property
    def p99_response_time(self) -> float:
        """99百分位响应时间"""
        if not self.response_times:
            return 0.0
        sorted_times = sorted(self.response_times)
        index = int(len(sorted_times) * 0.99)
        return sorted_times[index] if index < len(sorted_times) else sorted_times[-1]
    
    @property
    def requests_per_second(self) -> float:
        """每秒请求数"""
        return self.total_requests / self.total_duration if self.total_duration > 0 else 0.0


class PerformanceAnalyzer:
    """性能分析器"""
    
    def __init__(self):
        """初始化性能分析器"""
        self.metrics = PerformanceMetrics()
        self.start_time: Optional[float] = None
        self.end_time: Optional[float] = None
    
    def start_analysis(self):
        """开始性能分析"""
        self.start_time = time.time()
        logger.info("开始性能分析...")
    
    def end_analysis(self):
        """结束性能分析"""
        self.end_time = time.time()
        if self.start_time:
            self.metrics.total_duration = self.end_time - self.start_time
        logger.info(f"性能分析结束，总耗时: {self.metrics.total_duration:.2f}秒")
    
    def record_request(
        self,
        success: bool,
        response_time: float,
        ck_id: Optional[int] = None,
        department_id: Optional[int] = None,
        error_type: Optional[str] = None
    ):
        """
        记录单个请求的性能数据
        
        Args:
            success: 请求是否成功
            response_time: 响应时间
            ck_id: 使用的CK ID
            department_id: 部门ID
            error_type: 错误类型（如果失败）
        """
        self.metrics.total_requests += 1
        self.metrics.response_times.append(response_time)
        
        if success:
            self.metrics.successful_requests += 1
            if ck_id:
                self.metrics.ck_usage_distribution[ck_id] += 1
            if department_id:
                self.metrics.department_distribution[department_id] += 1
        else:
            self.metrics.failed_requests += 1
            if error_type:
                self.metrics.error_types[error_type] += 1
    
    def analyze_concurrent_results(self, results: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        分析并发测试结果
        
        Args:
            results: 并发测试结果列表
            
        Returns:
            Dict: 分析结果
        """
        logger.info(f"开始分析 {len(results)} 个并发测试结果...")
        
        # 重置指标
        self.metrics = PerformanceMetrics()
        
        # 处理每个结果
        for result in results:
            success = result.get('success', False)
            response_time = result.get('response_time', 0.0)
            ck_id = result.get('ck_id')
            department_id = result.get('department_id')
            error_type = result.get('error_type')
            
            self.record_request(success, response_time, ck_id, department_id, error_type)
        
        # 生成分析报告
        analysis_result = {
            'basic_metrics': self._get_basic_metrics(),
            'response_time_analysis': self._analyze_response_times(),
            'load_balance_analysis': self._analyze_load_balance(),
            'error_analysis': self._analyze_errors(),
            'performance_grade': self._calculate_performance_grade()
        }
        
        logger.info("并发测试结果分析完成")
        return analysis_result
    
    def _get_basic_metrics(self) -> Dict[str, Any]:
        """获取基础指标"""
        return {
            'total_requests': self.metrics.total_requests,
            'successful_requests': self.metrics.successful_requests,
            'failed_requests': self.metrics.failed_requests,
            'success_rate': self.metrics.success_rate,
            'requests_per_second': self.metrics.requests_per_second,
            'total_duration': self.metrics.total_duration
        }
    
    def _analyze_response_times(self) -> Dict[str, Any]:
        """分析响应时间"""
        if not self.metrics.response_times:
            return {}
        
        return {
            'average': self.metrics.average_response_time,
            'median': self.metrics.median_response_time,
            'min': min(self.metrics.response_times),
            'max': max(self.metrics.response_times),
            'p95': self.metrics.p95_response_time,
            'p99': self.metrics.p99_response_time,
            'std_dev': statistics.stdev(self.metrics.response_times) if len(self.metrics.response_times) > 1 else 0.0
        }
    
    def _analyze_load_balance(self) -> Dict[str, Any]:
        """分析负载均衡效果"""
        ck_analysis = self._analyze_distribution(
            dict(self.metrics.ck_usage_distribution), 
            "CK"
        )
        
        dept_analysis = self._analyze_distribution(
            dict(self.metrics.department_distribution), 
            "部门"
        )
        
        return {
            'ck_load_balance': ck_analysis,
            'department_load_balance': dept_analysis
        }
    
    def _analyze_distribution(self, distribution: Dict[int, int], name: str) -> Dict[str, Any]:
        """分析分布情况"""
        if not distribution:
            return {'balance_score': 0.0, 'distribution': {}}
        
        values = list(distribution.values())
        total_requests = sum(values)
        
        # 计算负载均衡分数（基于标准差）
        if len(values) > 1:
            mean_value = statistics.mean(values)
            std_dev = statistics.stdev(values)
            # 负载均衡分数：标准差越小，分数越高
            balance_score = max(0.0, 1.0 - (std_dev / mean_value)) if mean_value > 0 else 0.0
        else:
            balance_score = 1.0
        
        # 计算分布比例
        distribution_ratios = {
            str(key): (value / total_requests) * 100 
            for key, value in distribution.items()
        }
        
        return {
            'balance_score': balance_score,
            'distribution': distribution,
            'distribution_ratios': distribution_ratios,
            'min_usage': min(values),
            'max_usage': max(values),
            'avg_usage': statistics.mean(values),
            'total_items': len(distribution)
        }
    
    def _analyze_errors(self) -> Dict[str, Any]:
        """分析错误情况"""
        if not self.metrics.error_types:
            return {'error_rate': 0.0, 'error_distribution': {}}
        
        total_errors = sum(self.metrics.error_types.values())
        error_rate = total_errors / self.metrics.total_requests if self.metrics.total_requests > 0 else 0.0
        
        # 计算错误分布比例
        error_distribution = {
            error_type: (count / total_errors) * 100
            for error_type, count in self.metrics.error_types.items()
        }
        
        return {
            'error_rate': error_rate,
            'total_errors': total_errors,
            'error_distribution': error_distribution,
            'error_types': dict(self.metrics.error_types)
        }
    
    def _calculate_performance_grade(self) -> Dict[str, Any]:
        """计算性能等级"""
        # 定义性能阈值
        thresholds = {
            'success_rate': {'excellent': 0.95, 'good': 0.90, 'fair': 0.80},
            'avg_response_time': {'excellent': 0.2, 'good': 0.5, 'fair': 1.0},
            'p95_response_time': {'excellent': 0.5, 'good': 1.0, 'fair': 2.0}
        }
        
        # 计算各项得分
        scores = {}
        
        # 成功率得分
        success_rate = self.metrics.success_rate
        if success_rate >= thresholds['success_rate']['excellent']:
            scores['success_rate'] = 'A'
        elif success_rate >= thresholds['success_rate']['good']:
            scores['success_rate'] = 'B'
        elif success_rate >= thresholds['success_rate']['fair']:
            scores['success_rate'] = 'C'
        else:
            scores['success_rate'] = 'D'
        
        # 平均响应时间得分
        avg_time = self.metrics.average_response_time
        if avg_time <= thresholds['avg_response_time']['excellent']:
            scores['avg_response_time'] = 'A'
        elif avg_time <= thresholds['avg_response_time']['good']:
            scores['avg_response_time'] = 'B'
        elif avg_time <= thresholds['avg_response_time']['fair']:
            scores['avg_response_time'] = 'C'
        else:
            scores['avg_response_time'] = 'D'
        
        # P95响应时间得分
        p95_time = self.metrics.p95_response_time
        if p95_time <= thresholds['p95_response_time']['excellent']:
            scores['p95_response_time'] = 'A'
        elif p95_time <= thresholds['p95_response_time']['good']:
            scores['p95_response_time'] = 'B'
        elif p95_time <= thresholds['p95_response_time']['fair']:
            scores['p95_response_time'] = 'C'
        else:
            scores['p95_response_time'] = 'D'
        
        # 计算总体等级
        grade_values = {'A': 4, 'B': 3, 'C': 2, 'D': 1}
        avg_score = statistics.mean([grade_values[score] for score in scores.values()])
        
        if avg_score >= 3.5:
            overall_grade = 'A'
        elif avg_score >= 2.5:
            overall_grade = 'B'
        elif avg_score >= 1.5:
            overall_grade = 'C'
        else:
            overall_grade = 'D'
        
        return {
            'individual_scores': scores,
            'overall_grade': overall_grade,
            'performance_summary': self._get_performance_summary(overall_grade)
        }
    
    def _get_performance_summary(self, grade: str) -> str:
        """获取性能总结"""
        summaries = {
            'A': '性能优秀，系统运行稳定，响应迅速',
            'B': '性能良好，系统运行正常，响应较快',
            'C': '性能一般，系统基本可用，响应稍慢',
            'D': '性能较差，系统存在问题，需要优化'
        }
        return summaries.get(grade, '性能评估异常')
    
    def verify_weight_distribution(
        self, 
        expected_weights: Dict[int, float], 
        tolerance: float = 0.1
    ) -> Dict[str, Any]:
        """
        验证权重分配是否符合预期
        
        Args:
            expected_weights: 期望的权重分配 {department_id: expected_ratio}
            tolerance: 容差范围
            
        Returns:
            Dict: 验证结果
        """
        if not self.metrics.department_distribution:
            return {
                'passed': False,
                'reason': '没有部门分配数据',
                'details': {}
            }
        
        total_requests = sum(self.metrics.department_distribution.values())
        verification_results = {}
        all_passed = True
        
        for dept_id, expected_ratio in expected_weights.items():
            actual_count = self.metrics.department_distribution.get(dept_id, 0)
            actual_ratio = actual_count / total_requests if total_requests > 0 else 0.0
            
            deviation = abs(actual_ratio - expected_ratio)
            passed = deviation <= tolerance
            
            if not passed:
                all_passed = False
            
            verification_results[dept_id] = {
                'expected_ratio': expected_ratio,
                'actual_ratio': actual_ratio,
                'deviation': deviation,
                'passed': passed,
                'actual_count': actual_count
            }
        
        return {
            'passed': all_passed,
            'tolerance': tolerance,
            'details': verification_results,
            'summary': {
                'total_departments': len(verification_results),
                'passed_departments': sum(1 for r in verification_results.values() if r['passed']),
                'max_deviation': max(r['deviation'] for r in verification_results.values()) if verification_results else 0.0
            }
        }
    
    def generate_report(self) -> str:
        """生成性能分析报告"""
        if not self.metrics.total_requests:
            return "没有可分析的数据"
        
        report_lines = [
            "=" * 60,
            "沃尔玛绑卡系统性能分析报告",
            "=" * 60,
            "",
            "基础指标:",
            f"  总请求数: {self.metrics.total_requests}",
            f"  成功请求: {self.metrics.successful_requests}",
            f"  失败请求: {self.metrics.failed_requests}",
            f"  成功率: {self.metrics.success_rate:.2%}",
            f"  总耗时: {self.metrics.total_duration:.2f}秒",
            f"  QPS: {self.metrics.requests_per_second:.2f}",
            "",
            "响应时间分析:",
            f"  平均响应时间: {self.metrics.average_response_time:.3f}秒",
            f"  中位数响应时间: {self.metrics.median_response_time:.3f}秒",
            f"  P95响应时间: {self.metrics.p95_response_time:.3f}秒",
            f"  P99响应时间: {self.metrics.p99_response_time:.3f}秒",
        ]
        
        if self.metrics.ck_usage_distribution:
            report_lines.extend([
                "",
                "CK使用分布:",
            ])
            for ck_id, count in sorted(self.metrics.ck_usage_distribution.items()):
                ratio = count / self.metrics.successful_requests * 100
                report_lines.append(f"  CK {ck_id}: {count}次 ({ratio:.1f}%)")
        
        if self.metrics.department_distribution:
            report_lines.extend([
                "",
                "部门分配分布:",
            ])
            for dept_id, count in sorted(self.metrics.department_distribution.items()):
                ratio = count / self.metrics.successful_requests * 100
                report_lines.append(f"  部门 {dept_id}: {count}次 ({ratio:.1f}%)")
        
        if self.metrics.error_types:
            report_lines.extend([
                "",
                "错误类型分布:",
            ])
            for error_type, count in sorted(self.metrics.error_types.items()):
                ratio = count / self.metrics.failed_requests * 100
                report_lines.append(f"  {error_type}: {count}次 ({ratio:.1f}%)")
        
        report_lines.extend([
            "",
            "=" * 60
        ])
        
        return "\n".join(report_lines)