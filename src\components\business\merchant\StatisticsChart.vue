<template>
  <div class="statistics-container">
    <!-- 状态分布卡片 -->
    <el-row :gutter="20" class="mb-4">
      <el-col :span="6">
        <el-card shadow="hover" class="stat-card">
          <template #header>
            <div class="stat-header">
              <span>总绑卡数</span>
            </div>
          </template>
          <div class="stat-value">{{ statistics.total_records || 0 }}</div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card shadow="hover" class="stat-card">
          <template #header>
            <div class="stat-header">
              <span>成功数</span>
            </div>
          </template>
          <div class="stat-value success">{{ statistics.success_records || 0 }}</div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card shadow="hover" class="stat-card">
          <template #header>
            <div class="stat-header">
              <span>失败数</span>
            </div>
          </template>
          <div class="stat-value error">{{ statistics.failed_records || 0 }}</div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card shadow="hover" class="stat-card">
          <template #header>
            <div class="stat-header">
              <span>待处理数</span>
            </div>
          </template>
          <div class="stat-value warning">{{ statistics.pending_records || 0 }}</div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 状态分布表格 -->
    <el-row :gutter="20" class="mb-4">
      <el-col :span="12">
        <el-card class="full-width">
          <template #header>
            <div class="card-header">
              <span>绑卡状态分布</span>
            </div>
          </template>
          <el-table :data="statusDistribution" style="width: 100%">
            <el-table-column prop="name" label="状态" />
            <el-table-column prop="value" label="数量" />
            <el-table-column prop="percentage" label="占比">
              <template #default="scope">
                <div class="percentage-cell">
                  <el-progress
                    :percentage="scope.row.percentage"
                    :color="scope.row.color"
                  />
                </div>
              </template>
            </el-table-column>
          </el-table>
        </el-card>
      </el-col>
      <el-col :span="12">
        <el-card class="full-width">
          <template #header>
            <div class="card-header">
              <span>近期绑卡数据</span>
            </div>
          </template>
          <el-table :data="recentData" style="width: 100%">
            <el-table-column prop="name" label="日期" />
            <el-table-column prop="value" label="绑卡数量" />
            <el-table-column prop="trend" label="趋势">
              <template #default="scope">
                <el-tag :type="scope.row.trend === 'up' ? 'success' : 'danger'">
                  {{ scope.row.trend === 'up' ? '上升' : '下降' }}
                </el-tag>
              </template>
            </el-table-column>
          </el-table>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script setup>
import { computed } from 'vue';

const props = defineProps({
  statistics: {
    type: Object,
    required: true,
    default: () => ({
      total_records: 0,
      success_records: 0,
      failed_records: 0,
      pending_records: 0,
      today_records: 0,
      yesterday_records: 0
    })
  }
});

// 计算状态分布数据
const statusDistribution = computed(() => {
  const total = props.statistics.total_records || 1; // 避免除以0
  const successRecords = props.statistics.success_records || 0;
  const failedRecords = props.statistics.failed_records || 0;
  const pendingRecords = props.statistics.pending_records || 0;

  return [
    {
      name: '成功',
      value: successRecords,
      percentage: total > 0 ? Math.round((successRecords / total) * 100) : 0,
      color: '#67c23a'
    },
    {
      name: '失败',
      value: failedRecords,
      percentage: total > 0 ? Math.round((failedRecords / total) * 100) : 0,
      color: '#f56c6c'
    },
    {
      name: '待处理',
      value: pendingRecords,
      percentage: total > 0 ? Math.round((pendingRecords / total) * 100) : 0,
      color: '#e6a23c'
    }
  ];
});

// 计算近期数据
const recentData = computed(() => {
  const todayRecords = props.statistics.today_records || 0;
  const yesterdayRecords = props.statistics.yesterday_records || 0;

  return [
    {
      name: '今日',
      value: todayRecords,
      trend: todayRecords >= yesterdayRecords ? 'up' : 'down'
    },
    {
      name: '昨日',
      value: yesterdayRecords,
      trend: yesterdayRecords > 0 ? 'up' : 'down'
    }
  ];
});
</script>

<style scoped>
.statistics-container {
  padding: 20px;
}

.mb-4 {
  margin-bottom: 16px;
}

.stat-card {
  height: 100%;
}

.stat-header {
  display: flex;
  align-items: center;
  gap: 8px;
}

.stat-value {
  font-size: 24px;
  font-weight: bold;
  text-align: center;
  color: #409EFF;
}

.stat-value.success {
  color: #67C23A;
}

.stat-value.error {
  color: #F56C6C;
}

.stat-value.warning {
  color: #E6A23C;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.full-width {
  width: 100%;
}

.percentage-cell {
  width: 100%;
  padding-right: 20px;
}
</style>