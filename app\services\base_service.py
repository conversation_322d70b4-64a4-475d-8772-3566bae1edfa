"""
基础服务类 - 提供通用的服务功能
"""

from typing import List, Optional, Dict, Any, Type, TypeVar, Generic, Union
from sqlalchemy.orm import Session
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import and_, or_, desc, asc, select, func
from pydantic import BaseModel
import logging
from datetime import datetime

from app.models.base import BaseModel as DBBaseModel
from app.models.user import User

logger = logging.getLogger(__name__)

# 泛型类型变量
ModelType = TypeVar("ModelType", bound=DBBaseModel)
CreateSchemaType = TypeVar("CreateSchemaType", bound=BaseModel)
UpdateSchemaType = TypeVar("UpdateSchemaType", bound=BaseModel)


class BaseService(Generic[ModelType, CreateSchemaType, UpdateSchemaType]):
    """基础服务类，提供通用的CRUD操作和权限控制"""

    def __init__(self, model: Type[ModelType], db: Union[Session, AsyncSession]):
        """
        初始化基础服务

        Args:
            model: 数据模型类
            db: 数据库会话（支持同步和异步）
        """
        self.model = model
        self.db = db
        self.is_async = isinstance(db, AsyncSession)
        self.logger = logger

    def get(self, id: Any) -> Optional[ModelType]:
        """
        根据ID获取单个对象（同步版本）

        Args:
            id: 对象ID

        Returns:
            Optional[ModelType]: 对象实例或None
        """
        if self.is_async:
            raise RuntimeError("Use get_async() for AsyncSession")

        try:
            return self.db.query(self.model).filter(self.model.id == id).first()
        except Exception as e:
            self.logger.error(f"获取{self.model.__name__}失败: {e}")
            return None

    async def get_async(self, id: Any) -> Optional[ModelType]:
        """
        根据ID获取单个对象（异步版本）

        Args:
            id: 对象ID

        Returns:
            Optional[ModelType]: 对象实例或None
        """
        if not self.is_async:
            raise RuntimeError("Use get() for sync Session")

        try:
            stmt = select(self.model).filter(self.model.id == id)
            result = await self.db.execute(stmt)
            return result.scalar_one_or_none()
        except Exception as e:
            self.logger.error(f"获取{self.model.__name__}失败: {e}")
            return None

    def get_multi(
        self,
        skip: int = 0,
        limit: int = 100,
        filters: Optional[Dict[str, Any]] = None,
        order_by: Optional[str] = None,
        order_desc: bool = False
    ) -> List[ModelType]:
        """
        获取多个对象
        
        Args:
            skip: 跳过的记录数
            limit: 限制返回的记录数
            filters: 过滤条件字典
            order_by: 排序字段
            order_desc: 是否降序排列
            
        Returns:
            List[ModelType]: 对象列表
        """
        try:
            query = self.db.query(self.model)
            
            # 应用过滤条件
            if filters:
                for field, value in filters.items():
                    if hasattr(self.model, field) and value is not None:
                        query = query.filter(getattr(self.model, field) == value)
            
            # 应用排序
            if order_by and hasattr(self.model, order_by):
                order_field = getattr(self.model, order_by)
                if order_desc:
                    query = query.order_by(desc(order_field))
                else:
                    query = query.order_by(asc(order_field))
            
            return query.offset(skip).limit(limit).all()
        except Exception as e:
            self.logger.error(f"获取{self.model.__name__}列表失败: {e}")
            return []

    def count(self, filters: Optional[Dict[str, Any]] = None) -> int:
        """
        统计记录数量
        
        Args:
            filters: 过滤条件字典
            
        Returns:
            int: 记录数量
        """
        try:
            query = self.db.query(self.model)
            
            # 应用过滤条件
            if filters:
                for field, value in filters.items():
                    if hasattr(self.model, field) and value is not None:
                        query = query.filter(getattr(self.model, field) == value)
            
            return query.count()
        except Exception as e:
            self.logger.error(f"统计{self.model.__name__}数量失败: {e}")
            return 0

    def create(self, obj_in: CreateSchemaType, **kwargs) -> Optional[ModelType]:
        """
        创建新对象
        
        Args:
            obj_in: 创建对象的数据
            **kwargs: 额外的字段值
            
        Returns:
            Optional[ModelType]: 创建的对象实例或None
        """
        try:
            obj_data = obj_in.dict() if hasattr(obj_in, 'dict') else obj_in
            obj_data.update(kwargs)
            
            db_obj = self.model(**obj_data)
            self.db.add(db_obj)
            self.db.commit()
            self.db.refresh(db_obj)
            return db_obj
        except Exception as e:
            self.db.rollback()
            self.logger.error(f"创建{self.model.__name__}失败: {e}")
            return None

    def update(
        self,
        db_obj: ModelType,
        obj_in: UpdateSchemaType,
        **kwargs
    ) -> Optional[ModelType]:
        """
        更新对象
        
        Args:
            db_obj: 数据库中的对象实例
            obj_in: 更新数据
            **kwargs: 额外的字段值
            
        Returns:
            Optional[ModelType]: 更新后的对象实例或None
        """
        try:
            obj_data = obj_in.dict(exclude_unset=True) if hasattr(obj_in, 'dict') else obj_in
            obj_data.update(kwargs)
            
            for field, value in obj_data.items():
                if hasattr(db_obj, field):
                    setattr(db_obj, field, value)
            
            # 更新时间戳
            if hasattr(db_obj, 'updated_at'):
                db_obj.updated_at = datetime.utcnow()
            
            self.db.commit()
            self.db.refresh(db_obj)
            return db_obj
        except Exception as e:
            self.db.rollback()
            self.logger.error(f"更新{self.model.__name__}失败: {e}")
            return None

    def delete(self, id: Any) -> bool:
        """
        删除对象
        
        Args:
            id: 对象ID
            
        Returns:
            bool: 是否删除成功
        """
        try:
            obj = self.db.query(self.model).filter(self.model.id == id).first()
            if obj:
                self.db.delete(obj)
                self.db.commit()
                return True
            return False
        except Exception as e:
            self.db.rollback()
            self.logger.error(f"删除{self.model.__name__}失败: {e}")
            return False

    def apply_data_isolation(self, query, current_user: User):
        """
        应用数据隔离规则 - 支持用户级、部门级、商户级权限
        修复权限优先级问题：使用统一的权限范围判断，避免严格权限覆盖宽松权限

        Args:
            query: SQLAlchemy查询对象
            current_user: 当前用户

        Returns:
            query: 应用隔离规则后的查询对象
        """
        self.logger.info(f"[DEBUG] apply_data_isolation - 用户ID: {current_user.id if current_user else 'None'}")

        if not current_user:
            self.logger.info(f"[DEBUG] 用户为空，返回原查询")
            return query

        # 超级管理员可以访问所有数据
        if current_user.is_superuser:
            self.logger.info(f"[DEBUG] 超级管理员，返回原查询")
            return query

        # 获取权限服务实例和认证服务实例
        from app.services.permission_service import PermissionService
        from app.core.auth import AuthService
        permission_service = PermissionService(self.db)
        auth_service = AuthService()

        # 使用统一的权限范围判断，确保权限优先级正确
        data_scope = auth_service.get_user_data_scope(current_user, self.db)
        self.logger.info(f"[DEBUG] 确定的数据权限范围: {data_scope}")

        # 根据数据权限范围应用相应的过滤逻辑
        if data_scope == "all":
            # 可以访问所有数据，不添加任何过滤条件
            self.logger.info(f"[DEBUG] 全局权限，不添加过滤条件")
            pass
        elif data_scope == "merchant":
            # 商户级数据隔离
            self.logger.info(f"[DEBUG] 商户权限，添加商户过滤")
            if hasattr(self.model, 'merchant_id'):
                if current_user.merchant_id:
                    query = query.filter(self.model.merchant_id == current_user.merchant_id)
                    self.logger.info(f"[DEBUG] 添加商户过滤: merchant_id = {current_user.merchant_id}")
                else:
                    query = query.filter(self.model.merchant_id.is_(None))
                    self.logger.info(f"[DEBUG] 添加商户过滤: merchant_id IS NULL")
        elif data_scope == "department_sub":
            # 本部门及子部门数据
            self.logger.info(f"[DEBUG] 本部门及子部门权限")
            if hasattr(self.model, 'department_id'):
                accessible_dept_ids = self._get_cached_accessible_departments(current_user)
                self.logger.info(f"[DEBUG] 可访问部门: {accessible_dept_ids}")
                if accessible_dept_ids:
                    query = query.filter(self.model.department_id.in_(accessible_dept_ids))
                    self.logger.info(f"[DEBUG] 添加部门过滤: department_id IN {accessible_dept_ids}")
                elif current_user.department_id:
                    query = query.filter(self.model.department_id == current_user.department_id)
                    self.logger.info(f"[DEBUG] 添加单部门过滤: department_id = {current_user.department_id}")
            # 同时应用商户级隔离
            if hasattr(self.model, 'merchant_id'):
                if current_user.merchant_id:
                    query = query.filter(self.model.merchant_id == current_user.merchant_id)
                    self.logger.info(f"[DEBUG] 添加商户过滤: merchant_id = {current_user.merchant_id}")
                else:
                    query = query.filter(self.model.merchant_id.is_(None))
                    self.logger.info(f"[DEBUG] 添加商户过滤: merchant_id IS NULL")
        elif data_scope == "department":
            # 本部门数据
            if hasattr(self.model, 'department_id'):
                if current_user.department_id:
                    query = query.filter(self.model.department_id == current_user.department_id)
            # 同时应用商户级隔离
            if hasattr(self.model, 'merchant_id'):
                if current_user.merchant_id:
                    query = query.filter(self.model.merchant_id == current_user.merchant_id)
                else:
                    query = query.filter(self.model.merchant_id.is_(None))
        elif data_scope == "self":
            # 本人数据 - 只能访问自己创建的数据
            self.logger.info(f"[DEBUG] 本人权限")
            if hasattr(self.model, 'created_by'):
                query = query.filter(self.model.created_by == current_user.id)
                self.logger.info(f"[DEBUG] 添加创建者过滤: created_by = {current_user.id}")
            # 同时应用商户级隔离
            if hasattr(self.model, 'merchant_id'):
                if current_user.merchant_id:
                    query = query.filter(self.model.merchant_id == current_user.merchant_id)
                    self.logger.info(f"[DEBUG] 添加商户过滤: merchant_id = {current_user.merchant_id}")
                else:
                    query = query.filter(self.model.merchant_id.is_(None))
                    self.logger.info(f"[DEBUG] 添加商户过滤: merchant_id IS NULL")
        else:
            # 默认情况：应用最严格的隔离
            self.logger.info(f"[DEBUG] 默认权限（最严格隔离）")
            if hasattr(self.model, 'created_by'):
                query = query.filter(self.model.created_by == current_user.id)
                self.logger.info(f"[DEBUG] 添加创建者过滤: created_by = {current_user.id}")
            elif hasattr(self.model, 'department_id') and current_user.department_id:
                query = query.filter(self.model.department_id == current_user.department_id)
                self.logger.info(f"[DEBUG] 添加部门过滤: department_id = {current_user.department_id}")
            # 同时应用商户级隔离
            if hasattr(self.model, 'merchant_id'):
                if current_user.merchant_id:
                    query = query.filter(self.model.merchant_id == current_user.merchant_id)
                    self.logger.info(f"[DEBUG] 添加商户过滤: merchant_id = {current_user.merchant_id}")
                else:
                    query = query.filter(self.model.merchant_id.is_(None))
                    self.logger.info(f"[DEBUG] 添加商户过滤: merchant_id IS NULL")

        self.logger.info(f"[DEBUG] 数据隔离完成，最终查询: {str(query)}")
        return query

    def get_with_isolation(self, id: Any, current_user: User) -> Optional[ModelType]:
        """
        根据ID获取对象（应用数据隔离）

        Args:
            id: 对象ID
            current_user: 当前用户

        Returns:
            Optional[ModelType]: 对象实例或None
        """
        try:
            query = self.db.query(self.model).filter(self.model.id == id)
            query = self.apply_data_isolation(query, current_user)
            return query.first()
        except Exception as e:
            self.logger.error(f"获取{self.model.__name__}失败: {e}")
            return None

    def get_multi_with_isolation(
        self,
        current_user: User,
        skip: int = 0,
        limit: int = 100,
        filters: Optional[Dict[str, Any]] = None,
        order_by: Optional[str] = None,
        order_desc: bool = False
    ) -> List[ModelType]:
        """
        获取多个对象（应用数据隔离）

        Args:
            current_user: 当前用户
            skip: 跳过的记录数
            limit: 限制返回的记录数
            filters: 过滤条件字典
            order_by: 排序字段
            order_desc: 是否降序排列

        Returns:
            List[ModelType]: 对象列表
        """
        try:
            query = self.db.query(self.model)

            # 应用数据隔离
            query = self.apply_data_isolation(query, current_user)

            # 应用过滤条件
            if filters:
                for field, value in filters.items():
                    if hasattr(self.model, field) and value is not None:
                        query = query.filter(getattr(self.model, field) == value)

            # 应用排序
            if order_by and hasattr(self.model, order_by):
                order_field = getattr(self.model, order_by)
                if order_desc:
                    query = query.order_by(desc(order_field))
                else:
                    query = query.order_by(asc(order_field))

            return query.offset(skip).limit(limit).all()
        except Exception as e:
            self.logger.error(f"获取{self.model.__name__}列表失败: {e}")
            return []

    def count_with_isolation(
        self,
        current_user: User,
        filters: Optional[Dict[str, Any]] = None
    ) -> int:
        """
        统计记录数量（应用数据隔离）

        Args:
            current_user: 当前用户
            filters: 过滤条件字典

        Returns:
            int: 记录数量
        """
        try:
            query = self.db.query(self.model)

            # 应用数据隔离
            query = self.apply_data_isolation(query, current_user)

            # 应用过滤条件
            if filters:
                for field, value in filters.items():
                    if hasattr(self.model, field) and value is not None:
                        query = query.filter(getattr(self.model, field) == value)

            return query.count()
        except Exception as e:
            self.logger.error(f"统计{self.model.__name__}数量失败: {e}")
            return 0

    def search(
        self,
        search_term: str,
        search_fields: List[str],
        current_user: Optional[User] = None,
        skip: int = 0,
        limit: int = 100
    ) -> List[ModelType]:
        """
        搜索功能

        Args:
            search_term: 搜索关键词
            search_fields: 搜索字段列表
            current_user: 当前用户（用于数据隔离）
            skip: 跳过的记录数
            limit: 限制返回的记录数

        Returns:
            List[ModelType]: 搜索结果列表
        """
        try:
            query = self.db.query(self.model)

            # 应用数据隔离
            if current_user:
                query = self.apply_data_isolation(query, current_user)

            # 构建搜索条件
            if search_term and search_fields:
                search_conditions = []
                for field in search_fields:
                    if hasattr(self.model, field):
                        field_attr = getattr(self.model, field)
                        search_conditions.append(field_attr.like(f"%{search_term}%"))

                if search_conditions:
                    query = query.filter(or_(*search_conditions))

            return query.offset(skip).limit(limit).all()
        except Exception as e:
            self.logger.error(f"搜索{self.model.__name__}失败: {e}")
            return []

    def _get_cached_accessible_departments(self, current_user: User) -> List[int]:
        """获取用户可访问的部门ID列表（带缓存）"""
        # 使用简单的实例级缓存，避免重复查询
        cache_key = f"accessible_departments_{current_user.id}"
        if not hasattr(self, '_cache'):
            self._cache = {}

        if cache_key not in self._cache:
            try:
                self.logger.info(f"[DEBUG] 缓存未命中，调用权限服务获取可访问部门 - 用户ID: {current_user.id}")
                # 这里应该调用权限服务获取可访问的部门
                from app.core.auth import auth_service
                accessible_dept_ids = auth_service.get_user_accessible_departments(current_user, self.db)
                self._cache[cache_key] = accessible_dept_ids
                self.logger.info(f"[DEBUG] 权限服务返回的可访问部门: {accessible_dept_ids}")
            except Exception as e:
                self.logger.error(f"获取用户可访问部门失败: {e}")
                self._cache[cache_key] = []
        else:
            self.logger.info(f"[DEBUG] 使用缓存的可访问部门 - 用户ID: {current_user.id}, 部门: {self._cache[cache_key]}")

        return self._cache[cache_key]
