package model

import (
	"time"
)

// Merchant 商户模型 - 与Python系统数据库表结构保持一致
type Merchant struct {
	ID                  int64     `gorm:"column:id;primaryKey;autoIncrement" json:"id"`
	Name                string    `gorm:"column:name;type:varchar(100);not null" json:"name"`
	Code                string    `gorm:"column:code;type:varchar(50);uniqueIndex" json:"code"`
	APIKey              string    `gorm:"column:api_key;type:varchar(64);not null;uniqueIndex" json:"api_key"`
	APISecret           string    `gorm:"column:api_secret;type:varchar(128);not null" json:"api_secret"`
	Status              bool      `gorm:"column:status;type:tinyint(1);not null;default:1" json:"status"`
	CallbackURL         *string   `gorm:"column:callback_url;type:varchar(255)" json:"callback_url"`
	AllowedIPs          *string   `gorm:"column:allowed_ips;type:text" json:"allowed_ips"`
	DailyLimit          int       `gorm:"column:daily_limit;type:int;not null;default:10000" json:"daily_limit"`
	HourlyLimit         int       `gorm:"column:hourly_limit;type:int;not null;default:1000" json:"hourly_limit"`
	ConcurrencyLimit    int       `gorm:"column:concurrency_limit;type:int;not null;default:100" json:"concurrency_limit"`
	Priority            int       `gorm:"column:priority;type:int;not null;default:0" json:"priority"`
	RequestTimeout      int       `gorm:"column:request_timeout;type:int;not null;default:30" json:"request_timeout"`
	RetryCount          int       `gorm:"column:retry_count;type:int;not null;default:3" json:"retry_count"`
	ContactName         *string   `gorm:"column:contact_name;type:varchar(100)" json:"contact_name"`
	ContactPhone        *string   `gorm:"column:contact_phone;type:varchar(20)" json:"contact_phone"`
	ContactEmail        *string   `gorm:"column:contact_email;type:varchar(100)" json:"contact_email"`
	CreatedBy           *int64    `gorm:"column:created_by;type:bigint" json:"created_by"`
	Remark              *string   `gorm:"column:remark;type:text" json:"remark"`
	CustomConfig        *string   `gorm:"column:custom_config;type:json" json:"custom_config"`
	APIKeyUpdatedAt     *time.Time `gorm:"column:api_key_updated_at;type:datetime" json:"api_key_updated_at"`
	APISecretUpdatedAt  *time.Time `gorm:"column:api_secret_updated_at;type:datetime" json:"api_secret_updated_at"`
	CreatedAt           time.Time `gorm:"column:created_at;type:datetime;not null" json:"created_at"`
	UpdatedAt           time.Time `gorm:"column:updated_at;type:datetime;not null" json:"updated_at"`
}

// TableName 指定表名
func (Merchant) TableName() string {
	return "merchants"
}

// IsActive 检查商户是否激活
func (m *Merchant) IsActive() bool {
	return m.Status
}

// GetAPISecret 获取API密钥（用于签名验证）
func (m *Merchant) GetAPISecret() string {
	return m.APISecret
}

// MerchantRepository 商户仓储接口
type MerchantRepository interface {
	GetByAPIKey(apiKey string) (*Merchant, error)
	GetByID(id int64) (*Merchant, error)
	GetByCode(code string) (*Merchant, error)
}

// MerchantService 商户服务接口
type MerchantService interface {
	GetByAPIKey(apiKey string) (*Merchant, error)
	ValidateAPIKey(apiKey string) (*Merchant, error)
}
