from typing import Any
from sqlalchemy.ext.declarative import as_declarative, declared_attr
from sqlalchemy import Column, DateTime
from datetime import datetime
from zoneinfo import ZoneInfo

# 使用上海时区
TARGET_TZ = ZoneInfo("Asia/Shanghai")


def local_now():
    """获取当前目标时区的时间"""
    return datetime.now(TARGET_TZ)


class TimestampMixin:
    """
    为模型添加创建时间和更新时间字段的 Mixin 类
    """

    created_at = Column(
        DateTime(timezone=True), default=local_now, nullable=False, comment="创建时间"
    )
    updated_at = Column(
        DateTime(timezone=True),
        default=local_now,
        onupdate=local_now,
        nullable=False,
        comment="更新时间",
    )


@as_declarative()
class Base:
    """
    SQLAlchemy模型的基类。

    所有模型都继承自这个类，它提供了一个自动生成的表名（小写模型类名）和id列。
    """

    id: Any
    __name__: str

    # 生成表名为小写的类名
    @declared_attr
    def __tablename__(cls) -> str:
        return cls.__name__.lower()
