#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
沃尔玛绑卡系统CK软删除功能综合测试
验证所有改进：统计数据一致性 + 软删除时同时禁用
"""

import requests
import json
import uuid
import time

BASE_URL = "http://localhost:20000/api/v1"

class ComprehensiveSoftDeleteTest:
    def __init__(self):
        self.admin_token = None
        self.test_ck_ids = []
        
    def login(self):
        """登录获取token"""
        login_data = {
            'username': 'admin',
            'password': '7c222fb2927d828af22f592134e8932480637c0d'
        }
        
        response = requests.post(
            f"{BASE_URL}/auth/login",
            data=login_data,
            headers={'Content-Type': 'application/x-www-form-urlencoded'}
        )
        
        if response.status_code == 200:
            self.admin_token = response.json()['data']['access_token']
            print("✅ 登录成功")
            return True
        else:
            print(f"❌ 登录失败: {response.text}")
            return False
    
    def create_test_ck(self, suffix=""):
        """创建测试CK"""
        test_data = {
            "sign": f"comprehensive_test_{uuid.uuid4().hex[:8]}{suffix}@token#signature#26",
            "daily_limit": 100,
            "hourly_limit": 50,
            "active": 1,
            "description": f"综合测试CK{suffix}",
            "merchant_id": 1,
            "department_id": 1
        }
        
        headers = {'Authorization': f'Bearer {self.admin_token}'}
        response = requests.post(f"{BASE_URL}/walmart-ck", json=test_data, headers=headers)
        
        if response.status_code == 200:
            response_data = response.json()
            if 'data' in response_data and 'data' in response_data['data']:
                ck_id = response_data['data']['data']['id']
            else:
                ck_id = response_data['data']['id']
            print(f"✅ 创建CK成功，ID: {ck_id}")
            self.test_ck_ids.append(ck_id)
            return ck_id
        else:
            print(f"❌ 创建CK失败: {response.text}")
            return None
    
    def get_statistics(self):
        """获取统计数据"""
        headers = {'Authorization': f'Bearer {self.admin_token}'}
        
        # 获取CK统计
        ck_stats_response = requests.get(f"{BASE_URL}/walmart-ck/statistics/1", headers=headers)
        
        # 获取绑卡金额统计
        amount_stats_response = requests.get(f"{BASE_URL}/walmart-ck/binding-amount-statistics", headers=headers)
        
        ck_stats = None
        amount_stats = None
        
        if ck_stats_response.status_code == 200:
            ck_stats = ck_stats_response.json()['data']
        
        if amount_stats_response.status_code == 200:
            amount_stats = amount_stats_response.json()['data']
        
        return ck_stats, amount_stats
    
    def get_ck_list(self):
        """获取CK列表"""
        headers = {'Authorization': f'Bearer {self.admin_token}'}
        response = requests.get(f"{BASE_URL}/walmart-ck", headers=headers)
        
        if response.status_code == 200:
            data = response.json()['data']
            if isinstance(data, dict) and 'items' in data:
                return data['items']
            elif isinstance(data, list):
                return data
            else:
                return []
        else:
            return []
    
    def delete_ck(self, ck_id):
        """删除CK"""
        headers = {'Authorization': f'Bearer {self.admin_token}'}
        response = requests.delete(f"{BASE_URL}/walmart-ck/{ck_id}", headers=headers)
        
        if response.status_code == 200:
            print(f"✅ 删除CK成功，ID: {ck_id}")
            return True
        else:
            print(f"❌ 删除CK失败，ID: {ck_id}")
            return False
    
    def test_statistics_consistency(self):
        """测试统计数据一致性"""
        print("\n=== 测试统计数据一致性 ===")
        
        # 1. 获取初始统计
        initial_ck_stats, initial_amount_stats = self.get_statistics()
        if not initial_ck_stats or not initial_amount_stats:
            print("❌ 无法获取初始统计数据")
            return False
        
        initial_success_count = initial_ck_stats.get('actual_success_count', 0)
        initial_total_amount = initial_amount_stats.get('summary', {}).get('total_actual_amount', 0)
        initial_total_records = initial_amount_stats.get('summary', {}).get('total_success', 0)
        
        print(f"初始统计 - 成功数: {initial_success_count}, 金额: {initial_total_amount}, 记录数: {initial_total_records}")
        
        # 2. 创建测试CK
        test_ck = self.create_test_ck("_stats_test")
        if not test_ck:
            return False
        
        # 3. 删除测试CK
        if not self.delete_ck(test_ck):
            return False
        
        time.sleep(2)
        
        # 4. 获取删除后统计
        after_ck_stats, after_amount_stats = self.get_statistics()
        if not after_ck_stats or not after_amount_stats:
            print("❌ 无法获取删除后统计数据")
            return False
        
        after_success_count = after_ck_stats.get('actual_success_count', 0)
        after_total_amount = after_amount_stats.get('summary', {}).get('total_actual_amount', 0)
        after_total_records = after_amount_stats.get('summary', {}).get('total_success', 0)
        
        print(f"删除后统计 - 成功数: {after_success_count}, 金额: {after_total_amount}, 记录数: {after_total_records}")
        
        # 5. 验证一致性
        if (initial_success_count == after_success_count and 
            initial_total_amount == after_total_amount and 
            initial_total_records == after_total_records):
            print("✅ 统计数据一致性验证通过")
            return True
        else:
            print("❌ 统计数据一致性验证失败")
            return False
    
    def test_soft_delete_with_disable(self):
        """测试软删除时同时禁用"""
        print("\n=== 测试软删除时同时禁用 ===")
        
        # 1. 创建测试CK
        test_ck = self.create_test_ck("_disable_test")
        if not test_ck:
            return False
        
        # 2. 验证CK在列表中
        ck_list_before = self.get_ck_list()
        ck_ids_before = [item.get('id') for item in ck_list_before]
        
        if test_ck not in ck_ids_before:
            print(f"❌ CK {test_ck} 不在创建后的列表中")
            return False
        
        print(f"✅ CK {test_ck} 在创建后的列表中")
        
        # 3. 删除CK
        if not self.delete_ck(test_ck):
            return False
        
        time.sleep(1)
        
        # 4. 验证CK不在列表中
        ck_list_after = self.get_ck_list()
        ck_ids_after = [item.get('id') for item in ck_list_after]
        
        if test_ck in ck_ids_after:
            print(f"❌ CK {test_ck} 仍在删除后的列表中")
            return False
        
        print(f"✅ CK {test_ck} 正确地从列表中移除")
        
        # 5. 验证无法访问详情
        headers = {'Authorization': f'Bearer {self.admin_token}'}
        detail_response = requests.get(f"{BASE_URL}/walmart-ck/{test_ck}", headers=headers)
        
        if detail_response.status_code == 404:
            print(f"✅ 已删除CK {test_ck} 详情正确返回404")
            return True
        else:
            print(f"❌ 已删除CK {test_ck} 详情仍可访问")
            return False
    
    def test_binding_logic_protection(self):
        """测试绑卡逻辑保护"""
        print("\n=== 测试绑卡逻辑保护 ===")
        
        # 创建两个CK用于测试
        ck1 = self.create_test_ck("_bind_1")
        ck2 = self.create_test_ck("_bind_2")
        
        if not ck1 or not ck2:
            return False
        
        # 删除第一个CK
        if not self.delete_ck(ck1):
            return False
        
        # 这里应该测试绑卡逻辑不会选择已删除的CK
        # 由于没有直接的绑卡选择API，我们通过列表验证
        ck_list = self.get_ck_list()
        ck_ids = [item.get('id') for item in ck_list]
        
        if ck1 not in ck_ids and ck2 in ck_ids:
            print("✅ 绑卡逻辑保护验证通过：已删除CK不在可用列表中")
            return True
        else:
            print("❌ 绑卡逻辑保护验证失败")
            return False
    
    def cleanup(self):
        """清理测试数据"""
        print("\n=== 清理测试数据 ===")
        for ck_id in self.test_ck_ids:
            try:
                self.delete_ck(ck_id)
            except:
                pass
        print("✅ 测试数据清理完成")
    
    def run_comprehensive_test(self):
        """运行综合测试"""
        print("🧪 沃尔玛绑卡系统CK软删除功能综合测试")
        print("="*70)
        
        # 登录
        if not self.login():
            return False
        
        success_count = 0
        total_tests = 3
        
        # 测试1：统计数据一致性
        if self.test_statistics_consistency():
            success_count += 1
        
        # 测试2：软删除时同时禁用
        if self.test_soft_delete_with_disable():
            success_count += 1
        
        # 测试3：绑卡逻辑保护
        if self.test_binding_logic_protection():
            success_count += 1
        
        # 清理
        self.cleanup()
        
        # 结果
        print(f"\n=== 测试结果 ===")
        print(f"通过测试: {success_count}/{total_tests}")
        
        if success_count == total_tests:
            print("🎉 所有测试通过！CK软删除功能完全正常")
            print("✅ 统计数据一致性：历史绑卡数据不受CK删除影响")
            print("✅ 软删除同时禁用：已删除CK不会被绑卡逻辑选中")
            print("✅ 绑卡逻辑保护：完全排除已删除CK")
            return True
        else:
            print("❌ 部分测试失败，需要检查实现")
            return False

if __name__ == "__main__":
    test = ComprehensiveSoftDeleteTest()
    test.run_comprehensive_test()
