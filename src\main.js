import { createApp } from 'vue'
import './style.css'
import App from './App.vue'
import ElementPlus from 'element-plus'
import 'element-plus/dist/index.css'
import zhCn from 'element-plus/es/locale/lang/zh-cn'
import router from './router'
import pinia from './store'
import merchantSwitchListener from './utils/merchantSwitchListener'
import { vPermission, vRole } from './composables/usePermission'

async function initializeApp() {
    const app = createApp(App)

    app.use(ElementPlus, {
        locale: zhCn,
        // 全局配置，避免表单宽度为0的问题
        size: 'default',
        zIndex: 3000,
    })
    app.use(pinia)
    app.use(router)

    // 注册全局权限指令
    app.directive('permission', vPermission)
    app.directive('role', vRole)

    // 启动全局商家切换监听器
    merchantSwitchListener.startListening()

    // 挂载应用到 DOM
    app.mount('#app')
}

initializeApp()
