from typing import Dict, Any, Optional, Union

from sqlalchemy.orm import Session
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select
from app.core.logging import get_logger
from app.core.walmart_api import WalmartAPI
from app.models.walmart_ck import WalmartCK

# 创建日志记录器
logger = get_logger("amount_validation_service")


class AmountValidationService:
    """专门处理金额验证的服务"""
    
    def __init__(self):
        pass
    
    def _create_error_result(self, error_message: str, update_fields: Dict = None) -> Dict[str, Any]:
        """创建错误结果"""
        return {
            "success": False,
            "error_message": error_message,
            "update_fields": update_fields or {}
        }

    def _create_success_result(self, update_fields: Dict[str, Any]) -> Dict[str, Any]:
        """创建成功结果"""
        return {
            "success": True,
            "error_message": None,
            "update_fields": update_fields
        }

    def _prepare_update_fields(self, balance_data: Dict[str, Any], actual_amount: int = None) -> Dict[str, Any]:
        """准备更新字段"""
        update_fields = {
            "balance": balance_data["balance"],
            "cardBalance": balance_data["cardBalance"],
            "balanceCnt": balance_data["balanceCnt"],
        }

        if actual_amount is not None:
            update_fields["actual_amount"] = actual_amount

        # 兼容旧逻辑，在response_data中也保存金额信息
        update_fields["response_data"] = {
            "balance": balance_data["balance"],
            "cardBalance": balance_data["cardBalance"],
            "balanceCnt": balance_data["balanceCnt"],
        }

        return update_fields

    def _handle_amount_retrieval_failure(self, update_fields: Dict[str, Any]) -> Dict[str, Any]:
        """处理金额获取失败的情况"""
        error_message = "绑卡失败：无法获取卡的实际金额，请检查卡是否有效或重新绑卡"
        update_fields["response_data"] = {
            **update_fields.get("response_data", {}),
            "error_detail": {
                "code": "AMOUNT_RETRIEVAL_FAILED",
                "message": "无法获取卡的实际金额"
            }
        }
        return self._create_error_result(error_message, update_fields)

    async def validate_card_amount(
        self, db: Union[Session, AsyncSession], record, result: Dict[str, Any], debug: bool = False
    ) -> Dict[str, Any]:
        """
        验证卡金额

        Args:
            db: 数据库会话
            record: 卡记录
            result: API调用结果
            debug: 调试模式，为True时使用模拟数据

        Returns:
            Dict containing:
            - success: bool
            - error_message: str or None
            - update_fields: Dict[str, Any]
        """
        try:
            # 检查数据库会话类型
            is_async = isinstance(db, AsyncSession)

            # 检查是否为debug模式
            if debug:
                logger.info(f"[TEST_MODE] 验证卡金额（使用模拟数据） | record_id={record.id}")
                return await self._create_mock_amount_validation_result(record)

            # 获取实际卡金额
            balance_info = await self._get_card_balance(db, record, result, is_async)
            if not balance_info["success"]:
                return self._create_error_result(
                    balance_info["error_message"],
                    balance_info["update_fields"]
                )

            # 计算实际金额
            actual_amount = self._calculate_actual_amount(balance_info["balance_data"])

            # 准备更新字段
            update_fields = self._prepare_update_fields(balance_info["balance_data"], actual_amount)

            # 检查是否成功获取金额
            if actual_amount is None:
                return self._handle_amount_retrieval_failure(update_fields)

            # 检查是否为金额同步场景（不需要验证金额匹配）
            is_amount_sync = result.get("data", {}) == {}  # 金额同步时data为空

            if is_amount_sync:
                # 金额同步场景：只更新金额信息，不验证匹配
                logger.info(
                    f"[AMOUNT_SYNC_MODE] 金额同步模式，跳过金额匹配验证 | record_id={record.id} | "
                    f"actual_amount={actual_amount}"
                )
            else:
                # 正常绑卡场景：验证金额是否匹配
                validation_result = self._validate_amount_match(record, actual_amount)
                if not validation_result["success"]:
                    return self._create_error_result(
                        validation_result["error_message"],
                        {**update_fields, **validation_result["update_fields"]}
                    )

            logger.info(
                f"[AMOUNT_VALIDATION_SUCCESS] 金额验证成功 | record_id={record.id} | "
                f"actual_amount={actual_amount} | sync_mode={is_amount_sync}"
            )

            return self._create_success_result(update_fields)

        except Exception as e:
            logger.error(
                f"[AMOUNT_VALIDATION_ERROR] 金额验证异常 | record_id={record.id} | error={str(e)}"
            )
            return self._create_error_result(
                f"绑卡失败：获取卡金额过程中发生异常 - {str(e)}",
                {
                    "response_data": {
                        "error_detail": {
                            "code": "AMOUNT_RETRIEVAL_EXCEPTION",
                            "message": f"获取卡金额异常: {str(e)}",
                            "exception": str(e)
                        }
                    }
                }
            )
    
    def _validate_walmart_ck_id(self, result: Dict[str, Any], record) -> str:
        """验证并获取walmart_ck_id"""
        walmart_ck_id = result.get("walmart_ck_id")
        if not walmart_ck_id:
            logger.error(f"[BALANCE_ERROR] 绑卡结果中没有walmart_ck_id | record_id={record.id}")
            raise ValueError("缺少walmart_ck_id，无法获取卡金额")
        return walmart_ck_id

    async def _get_walmart_ck_config(self, db: Union[Session, AsyncSession], walmart_ck_id: str, record, is_async: bool = False):
        """获取沃尔玛CK配置（支持异步和同步会话）"""
        try:
            ck_id = int(walmart_ck_id)

            if is_async:
                # 异步查询
                stmt = select(WalmartCK).where(WalmartCK.id == ck_id)
                result = await db.execute(stmt)
                walmart_ck = result.scalar_one_or_none()
            else:
                # 同步查询
                walmart_ck = db.query(WalmartCK).filter(WalmartCK.id == ck_id).first()

            if not walmart_ck:
                logger.error(
                    f"[BALANCE_ERROR] 找不到绑卡成功的walmart_ck | "
                    f"record_id={record.id} | walmart_ck_id={walmart_ck_id}"
                )
                raise ValueError(f"找不到walmart_ck {walmart_ck_id}")

            logger.info(
                f"[BALANCE_INFO] 使用绑卡成功的walmart_ck | "
                f"record_id={record.id} | walmart_ck_id={walmart_ck_id}"
            )
            return walmart_ck

        except ValueError:
            # 重新抛出业务逻辑错误
            raise
        except Exception as e:
            logger.error(
                f"[BALANCE_ERROR] 获取walmart_ck配置异常 | "
                f"record_id={record.id} | walmart_ck_id={walmart_ck_id} | error={str(e)}"
            )
            raise ValueError(f"获取walmart_ck配置失败: {str(e)}")

    def _create_walmart_api(self, walmart_ck, record) -> WalmartAPI:
        """创建沃尔玛API实例"""
        api_params = self._prepare_api_request_params(walmart_ck)
        if not api_params:
            logger.error(
                f"[BALANCE_ERROR] 准备API参数失败 | "
                f"record_id={record.id} | walmart_ck_id={walmart_ck.id}"
            )
            raise ValueError("准备API参数失败")

        # 使用遗留方法创建API实例以保持兼容性
        return WalmartAPI.create_legacy(
            base_url="https://apicard.swiftpass.cn",
            encryption_key=api_params["secret_key"],
            token=None,
            version=api_params["api_version"],
            sign=api_params["sign"]
        )

    def _fetch_card_balance(self, walmart_api: WalmartAPI, record) -> Dict[str, Any]:
        """获取卡余额"""
        logger.info(
            f"[BALANCE_INFO] 开始获取卡余额 | "
            f"record_id={record.id} | card_number={record.card_number[:6]}***"
        )
        card_balance_info = walmart_api.get_card_balance_sync(record.card_number)

        # 提取金额字段
        balance = card_balance_info.get("balance")
        cardBalance = card_balance_info.get("cardBalance")
        balanceCnt = card_balance_info.get("balanceCnt")

        logger.info(
            f"[BALANCE_SUCCESS] 获取卡余额成功 | record_id={record.id} | card_number={record.card_number} |"
            f"balance={balance} | cardBalance={cardBalance} | balanceCnt={balanceCnt}"
        )

        return {
            "balance": balance,
            "cardBalance": cardBalance,
            "balanceCnt": balanceCnt,
        }

    async def _get_card_balance(
        self, db: Union[Session, AsyncSession], record, result: Dict[str, Any], is_async: bool = False
    ) -> Dict[str, Any]:
        """获取卡余额信息"""
        try:
            # 验证并获取walmart_ck_id
            walmart_ck_id = self._validate_walmart_ck_id(result, record)

            # 获取沃尔玛CK配置
            walmart_ck = await self._get_walmart_ck_config(db, walmart_ck_id, record, is_async)

            # 创建API实例
            walmart_api = self._create_walmart_api(walmart_ck, record)

            # 获取卡余额
            balance_data = self._fetch_card_balance(walmart_api, record)

            return {
                "success": True,
                "error_message": None,
                "balance_data": balance_data,
                "update_fields": {}
            }

        except Exception as e:
            logger.error(
                f"[BALANCE_ERROR] 获取卡余额异常 | record_id={record.id} | error={str(e)}"
            )
            return {
                "success": False,
                "error_message": f"获取卡余额异常: {str(e)}",
                "update_fields": {}
            }
    
    def _prepare_api_request_params(self, walmart_ck) -> Optional[Dict[str, Any]]:
        """准备API请求参数"""
        user_sign_raw = walmart_ck.sign
        sign_parts = user_sign_raw.split("#")
        if len(sign_parts) < 3:
            logger.error(f"沃尔玛CK {walmart_ck.id} 签名格式错误: {user_sign_raw}")
            return None
        
        return {
            "sign": sign_parts[0],
            "secret_key": sign_parts[1],  # 保持原名，在创建API时映射
            "api_version": sign_parts[2],  # 保持原名，在创建API时映射
        }
    
    def _calculate_actual_amount(self, balance_data: Dict[str, Any]) -> Optional[int]:
        """计算实际金额（分）"""
        balance = balance_data.get("balance") # 单位元
        cardBalance = balance_data.get("cardBalance") # 单位分
        balanceCnt = balance_data.get("balanceCnt")#单位元
        
        # 优先cardBalance （单位分），其次balance（单位元）*100，最后balanceCnt（单位元）  *100
        if cardBalance is not None:
            try:
                return int(float(cardBalance)) # 这个字段的单位是分，所以不能乘以100，否则会出错，因为实际金额是分
            except Exception as e:
                logger.error(f"[AMOUNT_ERROR] 转换cardBalance失败 | cardBalance={cardBalance} | error={str(e)}")
        
        if balance is not None:
            try:
                return int(float(balance) * 100)
            except Exception as e:
                logger.error(f"[AMOUNT_ERROR] 转换balance失败 | balance={balance} | error={str(e)}")
        
        if balanceCnt is not None:
            try:
                return int(float(balanceCnt) * 100)
            except Exception as e:
                logger.error(f"[AMOUNT_ERROR] 转换balanceCnt失败 | balanceCnt={balanceCnt} | error={str(e)}")
        
        return None
    
    def _validate_amount_match(self, record, actual_amount: int) -> Dict[str, Any]:
        """验证金额是否匹配"""
        merchant_amount = record.amount  # 商家请求的金额（分）
        
        logger.info(
            f"[AMOUNT_CHECK] 检查金额匹配 | record_id={record.id} | "
            f"merchant_amount={merchant_amount} | actual_amount={actual_amount}"
        )
        
        # 如果实际金额小于商家请求金额，则绑卡失败
        if actual_amount < merchant_amount:
            error_msg = (
                f"绑卡失败：卡金额不足【商家请求金额：{merchant_amount/100:.2f}元，"
                f"实际卡金额：{actual_amount/100:.2f}元】"
            )
            logger.warning(
                f"[AMOUNT_MISMATCH] 卡金额不足，绑卡失败 | record_id={record.id} | "
                f"merchant_amount={merchant_amount} | actual_amount={actual_amount}"
            )
            return {
                "success": False,
                "error_message": error_msg,
                "update_fields": {
                    "response_data": {
                        "error_detail": {
                            "code": "AMOUNT_MISMATCH",
                            "message": error_msg,
                            "requested_amount": merchant_amount,
                            "actual_amount": actual_amount
                        }
                    }
                }
            }
        
        logger.info(
            f"[AMOUNT_MATCH] 金额验证通过 | record_id={record.id} | "
            f"merchant_amount={merchant_amount} | actual_amount={actual_amount}"
        )
        
        return {
            "success": True,
            "error_message": None,
            "update_fields": {}
        }

    async def _create_mock_amount_validation_result(self, record) -> Dict[str, Any]:
        """创建模拟金额验证结果，用于测试模式"""
        # 使用用户输入的金额作为实际金额
        actual_amount = record.amount  # 单位：分
        balance_yuan = actual_amount / 100  # 转换为元

        # 创建模拟的余额数据
        mock_balance_data = {
            "balance": str(balance_yuan),  # 元
            "cardBalance": str(actual_amount),  # 分
            "balanceCnt": str(balance_yuan)  # 元
        }

        # 准备更新字段
        update_fields = self._prepare_update_fields(mock_balance_data, actual_amount)

        logger.info(
            f"[TEST_MODE] 金额验证通过（模拟数据） | record_id={record.id} | "
            f"merchant_amount={record.amount} | actual_amount={actual_amount} | "
            f"balance={mock_balance_data['balance']}元"
        )

        return {
            "success": True,
            "error_message": None,
            "update_fields": update_fields
        }
