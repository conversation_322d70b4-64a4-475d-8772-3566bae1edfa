from fastapi import APIRouter

from app.api.v1.endpoints.auth import router as auth_router
from app.api.v1.endpoints.users_new import router as users_router

# 权限相关路由 - 重新启用
from app.api.v1.endpoints.role import router as roles_router
from app.api.v1.endpoints.permissions import router as permissions_router
from app.api.v1.endpoints.menu import router as menus_router
from app.api.v1.endpoints.merchants_new import router as merchants_router
from app.api.v1.endpoints.departments import router as departments_router
from app.api.v1.endpoints.notification import router as notification_router
from app.api.v1.endpoints.bind_card import router as bind_card_router

from app.api.v1.endpoints.card import router as card_router
from app.api.v1.endpoints.system import router as system_router
from app.api.v1.endpoints.dashboard import router as dashboard_router
from app.api.v1.endpoints.walmart_server import (
    router as walmart_server_config_router,
)
from app.api.v1.endpoints.walmart_ck_new import (
    router as walmart_ck_router,
)
from app.api.v1.endpoints.notification_config import (
    router as notification_config_router,
)
from app.api.v1.endpoints.permission_advanced import (
    router as permission_advanced_router,
)
from app.api.v1.endpoints.telegram import (
    router as telegram_router,
)
from app.api.v1.endpoints.telegram_config_fixed import (
    router as telegram_config_router,
)
from app.api.v1.endpoints.telegram_groups import (
    router as telegram_groups_router,
)
from app.api.v1.endpoints.telegram_statistics import (
    router as telegram_statistics_router,
)
from app.api.v1.endpoints.telegram_users import (
    router as telegram_users_router,
)
from app.api.v1.endpoints.telegram_logs import (
    router as telegram_logs_router,
)
from app.api.v1.endpoints.totp import router as totp_router
from app.api.v1.endpoints.system_settings import router as system_settings_router
from app.api.v1.endpoints.ck_monitor import router as ck_monitor_router
from app.api.v1.endpoints.reconciliation import router as reconciliation_router

# 权限依赖已修复的路由 - 重新启用
from app.api.v1.endpoints.binding_logs import router as binding_logs_router
from app.api.v1.endpoints.recovery import router as recovery_router
from app.api.v1.endpoints.public import router as public_router

# 回调监控路由
from app.api.v1.endpoints.callback_monitor import router as callback_monitor_router

# 权限测试路由 - 暂时禁用，需要修复权限服务导入
# from app.api.v1.endpoints.permission_test import router as permission_test_router

api_router = APIRouter()


# 添加健康检查路由
@api_router.get("/health")
def health_check():
    """健康检查接口"""
    return {"status": "ok"}


# 注册各个模块的路由
api_router.include_router(auth_router, prefix="/auth", tags=["认证"])

# 临时用户路由已移除，使用完整的用户管理路由

# 移除临时用户路由，使用完整的用户路由
# api_router.include_router(users_temp_router, prefix="/users", tags=["用户"])
api_router.include_router(users_router, prefix="/users", tags=["用户"])

# 权限相关路由 - 重新启用
api_router.include_router(roles_router, prefix="/roles", tags=["角色"])
api_router.include_router(permissions_router, prefix="/permissions", tags=["权限"])
api_router.include_router(menus_router, prefix="/menus", tags=["菜单"])
api_router.include_router(merchants_router, prefix="/merchants", tags=["商家"])
api_router.include_router(departments_router, prefix="/departments", tags=["部门"])
api_router.include_router(notification_router, prefix="/notifications", tags=["通知"])
api_router.include_router(bind_card_router, prefix="/card-bind", tags=["绑卡"])
api_router.include_router(card_router, prefix="/cards", tags=["卡记录"])
api_router.include_router(
    walmart_server_config_router, prefix="/walmart-server", tags=["沃尔玛服务器配置"]
)
api_router.include_router(
    walmart_ck_router, prefix="/walmart-ck", tags=["沃尔玛CK配置"]
)
api_router.include_router(dashboard_router, prefix="/dashboard", tags=["仪表盘"])
api_router.include_router(system_router, prefix="/system", tags=["系统"])
api_router.include_router(
    notification_config_router, prefix="/notification-configs", tags=["通知配置"]
)
api_router.include_router(
    permission_advanced_router, prefix="/permissions-advanced", tags=["高级权限管理"]
)

# 权限依赖已修复的路由 - 重新启用
api_router.include_router(
    binding_logs_router, prefix="/binding-logs", tags=["绑卡日志"]
)
api_router.include_router(recovery_router, prefix="/recovery", tags=["恢复处理"])
api_router.include_router(public_router, prefix="/public", tags=["公共接口"])

# 双因子认证路由
api_router.include_router(totp_router, prefix="/totp", tags=["双因子认证"])

# 系统设置路由
api_router.include_router(system_settings_router, prefix="/system-settings", tags=["系统设置"])

# CK负载均衡监控路由
api_router.include_router(ck_monitor_router, prefix="/ck-monitor", tags=["CK负载均衡监控"])

# 对账台路由
api_router.include_router(reconciliation_router, prefix="/reconciliation", tags=["对账台"])

# 回调监控路由
api_router.include_router(callback_monitor_router, prefix="/callback-monitor", tags=["回调监控"])

# Telegram机器人路由
api_router.include_router(telegram_router, prefix="/telegram", tags=["Telegram机器人"])
api_router.include_router(telegram_config_router, prefix="/telegram/config", tags=["Telegram配置管理"])
api_router.include_router(telegram_groups_router, prefix="/telegram/groups", tags=["Telegram群组管理"])
api_router.include_router(telegram_statistics_router, prefix="/telegram", tags=["Telegram统计查询"])
api_router.include_router(telegram_users_router, prefix="/telegram/users", tags=["Telegram用户管理"])
api_router.include_router(telegram_logs_router, prefix="/telegram", tags=["Telegram日志管理"])

# 权限测试路由 - 暂时禁用，需要修复权限服务导入
# api_router.include_router(
#     permission_test_router, prefix="/permission-test", tags=["权限测试"]
# )
