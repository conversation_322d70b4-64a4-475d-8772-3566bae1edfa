from typing import Generator, Optional, List, Union
from functools import wraps
from fastapi import (
    Depends,
    HTTPException,
    Security,
    status,
    Header,
    Request,
    Path,
    Query,
)
from fastapi.security import OAuth2PasswordBearer
from jose import jwt
from pydantic import ValidationError
from sqlalchemy.orm import Session
import time
import redis
from redis.exceptions import RedisError

from app.core.config import settings
from app.core.security import verify_password
from app import models, schemas, crud
from app.db.session import SessionLocal
from app.models.merchant import Merchant
from app.models.user import User
# 移除权限服务导入，避免循环导入

from app.core.logging import get_logger

logger = get_logger("api.deps")

# OAuth2认证
reusable_oauth2 = OAuth2PasswordBearer(
    tokenUrl=f"{settings.API_V1_STR}/auth/login"
)

# Redis客户端
redis_client = redis.Redis(
    host=settings.REDIS_HOST,
    port=settings.REDIS_PORT,
    db=settings.REDIS_DB,
    password=settings.REDIS_PASSWORD,
    decode_responses=True,
)


def get_db() -> Generator:
    """获取数据库会话"""
    try:
        db = SessionLocal()
        yield db
    finally:
        db.close()


def get_current_user(
    db: Session = Depends(get_db), token: str = Depends(reusable_oauth2)
) -> User:
    """获取当前用户"""
    try:
        payload = jwt.decode(
            token, settings.SECRET_KEY, algorithms=[settings.ALGORITHM]
        )
        token_data = schemas.TokenPayload(**payload)
    except (jwt.JWTError, ValidationError) as e:
        logger.error(f"JWT验证失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="无法验证凭证",
        )
    user = crud.user.get(db, id=token_data.sub)
    if not user:
        raise HTTPException(status_code=404, detail="用户不存在")
    return user


def get_current_active_user(
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db),
) -> User:
    """获取当前活跃用户"""
    logger.info(f"获取当前活跃用户: {current_user.username}")
    user_with_perms = crud.user.get_user_with_permissions(db, current_user.id)
    if not user_with_perms:
        logger.error(f"Could not reload user {current_user.id} with permissions.")
        raise HTTPException(status_code=500, detail="无法加载用户信息")

    if not crud.user.is_active(user_with_perms):
        logger.warning(f"用户未激活: {user_with_perms.username}")
        raise HTTPException(status_code=400, detail="用户未激活")

    # 检查用户所属商家状态（超级管理员除外）
    if user_with_perms.merchant_id and not user_with_perms.is_superuser:
        merchant = crud.merchant.get(db, id=user_with_perms.merchant_id)
        if not merchant:
            logger.warning(f"用户所属商家不存在: {user_with_perms.username}")
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="用户所属商家不存在"
            )
        if not merchant.status:
            logger.warning(f"用户所属商家已被禁用: {user_with_perms.username}")
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="所属商家已被禁用"
            )

    return user_with_perms


def get_current_active_superuser(
    current_user: User = Depends(get_current_active_user),
) -> User:
    """获取当前超级管理员用户"""
    if not crud.user.is_superuser(current_user):
        raise HTTPException(status_code=400, detail="用户不是超级管理员")
    return current_user


def get_current_active_platform_user(
    current_user: User = Depends(get_current_user),
) -> User:
    """获取当前平台用户（超级管理员或平台管理员）

    Args:
        current_user: 当前用户

    Returns:
        User: 平台用户

    Raises:
        HTTPException: 权限异常
    """
    if not current_user.is_platform_user():
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="权限不足",
        )
    return current_user


def get_current_merchant_admin(
    current_user: User = Depends(get_current_user),
) -> User:
    """获取当前商家管理员

    Args:
        current_user: 当前用户

    Returns:
        User: 商家管理员

    Raises:
        HTTPException: 权限异常
    """
    # 检查用户是否有商户管理权限（基于动态权限系统）
    if not current_user.is_platform_user():
        # 检查用户是否有商户管理员角色
        has_merchant_admin_role = False
        if hasattr(current_user, 'roles') and current_user.roles:
            for role in current_user.roles:
                if role.code == 'merchant_admin' or role.data_scope in ['merchant', 'all']:
                    has_merchant_admin_role = True
                    break

        if not has_merchant_admin_role:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="权限不足",
            )
    return current_user


def get_current_merchant(
    current_user: User = Depends(get_current_user), db: Session = Depends(get_db)
) -> Merchant:
    """获取当前商家

    Args:
        current_user: 当前用户
        db: 数据库会话

    Returns:
        Merchant: 当前商家

    Raises:
        HTTPException: 商家不存在或权限异常
    """
    # 平台用户可以指定商家ID
    # 商家用户只能访问自己的商家
    if not current_user.merchant_id and not current_user.is_platform_user():
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="用户未关联商家",
        )

    merchant = crud.merchant.get(db, id=current_user.merchant_id)
    if not merchant:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="商家不存在",
        )

    return merchant


def check_merchant_permission(
    merchant_id: Optional[int] = None,
    current_user: User = Depends(get_current_user),
) -> None:
    """检查用户是否有权限访问指定商家

    Args:
        merchant_id: 商家ID
        current_user: 当前用户

    Raises:
        HTTPException: 权限异常
    """
    # 超级管理员和平台管理员可以访问任何商家
    if current_user.is_platform_user():
        return

    # 商家用户只能访问自己的商家
    if merchant_id and merchant_id != current_user.merchant_id:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="无权访问该商家数据",
        )


def api_key_auth(
    api_key: str = Header(...),
    timestamp: str = Header(...),
    nonce: str = Header(...),
    signature: str = Header(...),
    request: Request = None,
    db: Session = Depends(get_db),
) -> Merchant:
    """API密钥认证

    Args:
        api_key: API密钥
        timestamp: 时间戳
        nonce: 随机字符串
        signature: 签名
        request: 请求对象
        db: 数据库会话

    Returns:
        models.Merchant: 商家

    Raises:
        HTTPException: 认证异常
    """
    merchant = crud.merchant.get_by_api_key(db, api_key=api_key)
    if not merchant:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="无效的API密钥",
        )

    if not merchant.status:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="商家已禁用",
        )

    # 检查时间戳
    try:
        ts = int(timestamp)
        # 修复时区问题：使用统一的时间戳获取方法
        from app.utils.time_utils import get_current_timestamp
        now = int(get_current_timestamp())
        if abs(now - ts) > 300:  # 5分钟有效期
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="时间戳已过期",
            )
    except ValueError:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="无效的时间戳",
        )

    # 检查nonce防重放
    nonce_key = f"api:nonce:{api_key}:{nonce}"
    try:
        if redis_client.exists(nonce_key):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="重复的请求",
            )
        redis_client.setex(nonce_key, 300, "1")  # 5分钟过期
    except RedisError:
        # Redis错误不阻止请求，但应记录日志
        pass

    # 获取请求体并验证签名
    # 这里需要根据实际请求内容实现签名验证
    # data = await request.json()
    # if not merchant.validate_api_auth(api_key, signature, timestamp, nonce, data):
    #     raise HTTPException(
    #         status_code=status.HTTP_401_UNAUTHORIZED,
    #         detail="签名验证失败",
    #     )

    # 限流检查
    rate_key = f"rate:merchant:{merchant.id}"
    try:
        current = redis_client.incr(rate_key)
        if current == 1:
            redis_client.expire(rate_key, 60)  # 1分钟过期

        if current > settings.RATE_LIMIT_PER_MINUTE:
            raise HTTPException(
                status_code=status.HTTP_429_TOO_MANY_REQUESTS,
                detail="请求过于频繁，请稍后再试",
            )
    except RedisError:
        # Redis错误不阻止请求，但应记录日志
        pass

    return merchant


def require_permission(
    permission_code: str,
    resource_param: str = None,
    merchant_param: str = None,
):
    """权限检查装饰器"""

    def decorator(func):
        @wraps(func)
        async def check_permission(
            current_user: User = Depends(get_current_active_user),
            db: Session = Depends(get_db),
            **kwargs,
        ):
            # 使用新的权限服务
            from app.core.auth import auth_service

            # 获取资源ID和商家ID
            resource_id = kwargs.get(resource_param) if resource_param else None
            merchant_id = kwargs.get(merchant_param) if merchant_param else None

            # 检查权限
            has_permission = auth_service.check_permission(
                current_user,
                permission_code,
                resource_id=resource_id,
                merchant_id=merchant_id,
            )

            if not has_permission:
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail=f"权限不足，需要权限: {permission_code}",
                )

            return await func(current_user=current_user, db=db, **kwargs)

        return check_permission

    return decorator


def require_permissions(
    *permission_codes, require_all=True, resource_param=None, merchant_param=None
):
    """增强的权限检查装饰器 - 支持多权限检查

    Args:
        permission_codes: 权限代码列表
        require_all: 是否要求拥有所有权限
        resource_param: 资源ID参数名
        merchant_param: 商家ID参数名

    Examples:
        @router.get("/users")
        @require_permissions("user:view")
        def get_users():
            ...

        @router.put("/merchants/{merchant_id}")
        @require_permissions("merchant:edit", merchant_param="merchant_id")
        def update_merchant(merchant_id: int):
            ...
    """

    def decorator(func):
        # 保存权限代码到函数属性，供中间件使用
        setattr(func, "_permissions", list(permission_codes))

        @wraps(func)
        async def wrapper(*args, **kwargs):
            # 从请求中获取路径参数
            request = None
            for arg in args:
                if isinstance(arg, Request):
                    request = arg
                    break

            # 获取当前用户和数据库会话
            db = kwargs.get("db")
            if db is None:
                db = next(get_db())

            current_user = kwargs.get("current_user")
            if current_user is None:
                token = None
                if request:
                    auth_header = request.headers.get("Authorization")
                    if auth_header and auth_header.startswith("Bearer "):
                        token = auth_header.split(" ")[1]
                if not token:
                    token = next(reusable_oauth2(request))
                current_user = get_current_user(db, token)

            # 确保用户已激活
            if not current_user.is_active:
                raise HTTPException(status_code=400, detail="用户未激活")

            # 获取资源ID和商家ID
            resource_id = kwargs.get(resource_param) if resource_param else None

            merchant_id = kwargs.get(merchant_param) if merchant_param else None
            if merchant_id is None and current_user.merchant_id:
                merchant_id = current_user.merchant_id

            # 使用新的权限服务检查权限
            from app.core.auth import auth_service

            for code in permission_codes:
                has_permission = auth_service.check_permission(
                    user=current_user,
                    permission=code,
                    resource_id=resource_id,
                    merchant_id=merchant_id,
                )

                if require_all and not has_permission:
                    raise HTTPException(
                        status_code=status.HTTP_403_FORBIDDEN,
                        detail=f"没有执行此操作的权限: {code}",
                    )
                elif not require_all and has_permission:
                    break
            else:
                if not require_all:
                    raise HTTPException(
                        status_code=status.HTTP_403_FORBIDDEN,
                        detail="没有执行此操作的权限",
                    )

            # 将当前用户和数据库会话添加到kwargs中
            if "current_user" not in kwargs:
                kwargs["current_user"] = current_user
            if "db" not in kwargs:
                kwargs["db"] = db

            return await func(*args, **kwargs)

        return wrapper

    return decorator


def check_merchant_access(permission_code: str):
    """
    Dependency factory for checking merchant access.

    Args:
        permission_code: The required permission code (e.g., 'merchant:view', 'merchant:edit').

    Returns:
        A dependency function that checks access and returns the merchant object.
    """

    def _check_access(
        merchant_id: int = Path(
            ..., description="The ID of the merchant to access", ge=1
        ),
        db: Session = Depends(get_db),
        current_user: models.User = Depends(get_current_active_user),
    ) -> models.Merchant:
        """Inner dependency function injected by FastAPI."""
        logger.debug(
            f"Checking access for user {current_user.id} ({current_user.username}) to merchant {merchant_id} with permission {permission_code}"
        )

        # 1. Check if the user has the basic permission using the new CRUD method
        # The user object from get_current_active_user should have permissions preloaded
        if not crud.user.has_permission(current_user, permission_code):
            logger.warning(f"User {current_user.id} lacks permission {permission_code}")
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail=f"没有权限执行操作: {permission_code}",
            )

        # 2. Get the merchant
        merchant = crud.merchant.get(db=db, id=merchant_id)
        if not merchant:
            logger.warning(f"Merchant with ID {merchant_id} not found.")
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="商家不存在",
            )

        # 3. Apply data scope rules using the new CRUD method
        # Pass user_id explicitly to the CRUD method
        user_data_scope = crud.user.get_user_data_scope(
            db, user_id=current_user.id, permission_code=permission_code
        )
        logger.debug(
            f"User {current_user.id} data scope for {permission_code}: {user_data_scope}"
        )

        if user_data_scope == "all":
            logger.debug(
                f"User {current_user.id} has 'all' data scope. Access granted to merchant {merchant_id}."
            )
            return merchant
        elif user_data_scope == "merchant":
            if current_user.merchant_id == merchant_id:
                logger.debug(
                    f"User {current_user.id} has 'merchant' data scope and belongs to merchant {merchant_id}. Access granted."
                )
                return merchant
            else:
                logger.warning(
                    f"User {current_user.id} has 'merchant' scope but tried to access merchant {merchant_id} (belongs to {current_user.merchant_id})"
                )
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail="只能访问自己所属商家的数据",
                )
        elif user_data_scope == "own":
            logger.warning(
                f"User {current_user.id} has 'own' data scope, which is not sufficient to access merchant {merchant_id} directly."
            )
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="数据范围不足，无法访问该商家",
            )
        else:  # No specific scope found or invalid scope
            logger.error(
                f"User {current_user.id} has an invalid or missing data scope ('{user_data_scope}') for permission {permission_code}. Denying access to merchant {merchant_id}."
            )
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="无法确定数据范围，访问被拒绝",
            )

    return _check_access


def get_current_user_with_permission(permission_code: str):
    """获取具有特定权限的当前用户

    Args:
        permission_code: 权限代码

    Returns:
        依赖函数
    """

    async def _get_user_with_permission(
        current_user: User = Depends(get_current_active_user),
        db: Session = Depends(get_db),
    ) -> User:
        # 使用新的权限服务
        from app.core.auth import auth_service

        # 检查用户权限
        has_permission = auth_service.check_permission(
            current_user,
            permission_code,
        )

        if not has_permission:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="权限不足",
            )

        return current_user

    return _get_user_with_permission
