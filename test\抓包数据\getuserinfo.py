# 沃尔玛签名
walmart_sign = "6b95be8d25574dad856cdf7939bfe469@61fa608e14c37c20fde47c700ec90414"
# 微信加密KEY
wechat_key = "zB0ZnBVP+iBBQjV0zEi0yA=="
# 版本号
version = 45

# 请求头
header = {
    "sv": "3",
    "nonce": "24d4d7ffa",
    "timestamp": "1751174204050",
    "signature": "74B1193DDB5AE4AC76247A2F456CC13C411D936C48C284A5C3DE8FFFC9CDB119",
    "xweb_xhr": "1",
    "version": "45",
    "Sec-Fetch-Site": "cross-site",
    "Sec-Fetch-Mode": "cors",
    "Sec-Fetch-Dest": "empty",
    "Referer": "https://servicewechat.com/wx81d3e1fe4c2e11b4/175/page-frame.html",
    "Accept-Language": "zh-C<PERSON>,zh;q=0.9"
}
# 请求体
body={
    "currentPage": 0,
    "pageSize": 0,
    "sign": "6b95be8d25574dad856cdf7939bfe469@61fa608e14c37c20fde47c700ec90414"
}
# 成功响应体
# response_body={
#     "logId": "YYtFTnf4",
#     "status": true,
#     "error": {
#         "errorcode": 1,
#         "message": null,
#         "redirect": null,
#         "validators": null
#     },
#     "data": {
#         "cardCount": 4,
#         "nickName": "微信用户",
#         "headImg": "https://thirdwx.qlogo.cn/mmopen/vi_32/POgEwh4mIHO4nibH0KlMECNjjGxQUq24ZEaGT4poC6icRiccVGKSyXwibcPq4BWmiaIGuG1icwxaQX6grC9VemZoJ8rg/132",
#         "upcardOrderUrl": "https://vpay.upcard.com.cn/vcweixin/commercial/walm/gotoQuery%3FopenId%3Dxxx%26company%3Dwalm"
#     }
# }
