#!/usr/bin/env python3
"""
测试重试按钮卡密码修复

验证重试按钮现在能正确处理卡密码的问题
"""

import pytest
import uuid
from unittest.mock import patch, AsyncMock
from datetime import datetime

from app.services.card_record_service import CardRecordService
from app.services.binding_service import binding_service
from app.models.card_record import CardRecord, CardStatus
from app.models.user import User
from app.core.security import encrypt_sensitive_data


class TestRetryPasswordFix:
    """测试重试按钮卡密码修复"""

    @pytest.fixture
    def card_record_service(self, db_session):
        """创建卡记录服务实例"""
        return CardRecordService(db_session)

    @pytest.fixture
    def test_card_record(self, db_session, test_merchant):
        """创建测试卡记录"""
        test_password = "123456"
        encrypted_password = encrypt_sensitive_data(test_password)
        
        card_record = CardRecord(
            id=str(uuid.uuid4()),
            merchant_id=test_merchant.id,
            card_number="1234567890123456",
            card_password=encrypted_password,  # 加密存储
            status=CardStatus.FAILED,
            merchant_order_id="TEST_ORDER_001",
            amount=10000,
            retry_count=0,
            request_id=str(uuid.uuid4()),
            trace_id=str(uuid.uuid4()),
            request_data={"test": "data"},
            error_message="CK失效导致的绑卡失败",
            created_at=datetime.utcnow(),
            updated_at=datetime.utcnow()
        )
        
        db_session.add(card_record)
        db_session.commit()
        db_session.refresh(card_record)
        
        return card_record

    @pytest.fixture
    def merchant_user(self, db_session, test_merchant):
        """创建商户用户"""
        user = User(
            username="merchant_user",
            email="<EMAIL>",
            hashed_password="hashed_password",
            merchant_id=test_merchant.id,
            is_active=True
        )
        db_session.add(user)
        db_session.commit()
        db_session.refresh(user)
        return user

    @patch('app.utils.queue_producer.send_bind_card_task')
    @pytest.mark.asyncio
    async def test_retry_queue_message_format(
        self, 
        mock_send_task, 
        card_record_service, 
        test_card_record, 
        merchant_user
    ):
        """测试重试时队列消息格式是否正确"""
        mock_send_task.return_value = None
        
        # 执行重试
        result = await card_record_service.retry_single_card(
            test_card_record.id, 
            merchant_user
        )
        
        # 验证重试成功
        assert result['success'] is True
        assert '重试成功' in result['message']
        
        # 验证队列消息被发送
        mock_send_task.assert_called_once()
        
        # 获取发送的消息
        sent_message = mock_send_task.call_args[0][0]
        
        # 验证消息格式
        assert sent_message['record_id'] == test_card_record.id
        assert sent_message['merchant_id'] == test_card_record.merchant_id
        assert sent_message['is_recovery'] is True  # 🔧 关键修复：必须为True
        assert sent_message['retry_operation'] is True
        assert 'recovery_attempt' in sent_message
        assert sent_message['recovery_attempt'] == 1  # 重试次数应该增加

    def test_queue_data_parsing_with_recovery_flag(self):
        """测试队列数据解析时恢复标志的处理"""
        # 模拟修复后的队列消息
        queue_message = {
            "record_id": "test-record-id",
            "merchant_id": 1,
            "trace_id": "test-trace-id",
            "is_recovery": True,  # 🔧 修复：设置恢复标志
            "retry_operation": True,
            "recovery_attempt": 1,
        }
        
        # 解析队列数据
        queue_data = binding_service._parse_queue_data(queue_message)
        
        # 验证解析结果
        assert queue_data is not None
        assert queue_data.record_id == "test-record-id"
        assert queue_data.merchant_id == 1
        assert queue_data.is_recovery is True  # 🔧 关键：恢复标志必须为True
        assert queue_data.recovery_attempt == 1
        assert queue_data.unencrypted_card_password is None  # 队列中没有密码

    def test_queue_data_parsing_old_format(self):
        """测试旧格式队列消息（修复前的格式）"""
        # 模拟修复前的队列消息（有问题的格式）
        old_queue_message = {
            "record_id": "test-record-id",
            "merchant_id": 1,
            "trace_id": "test-trace-id",
            "retry_operation": True,
            # 缺少 is_recovery: True - 这是问题所在
        }
        
        # 解析队列数据
        queue_data = binding_service._parse_queue_data(old_queue_message)
        
        # 验证解析结果
        assert queue_data is not None
        assert queue_data.is_recovery is False  # 默认为False，这会导致问题
        assert queue_data.unencrypted_card_password is None

    @pytest.mark.asyncio
    async def test_recovery_logic_with_encrypted_password(self, db_session, test_card_record):
        """测试恢复逻辑能否正确解密密码"""
        # 模拟队列数据（密码为空，但设置了恢复标志）
        queue_message = {
            "record_id": test_card_record.id,
            "merchant_id": test_card_record.merchant_id,
            "trace_id": test_card_record.trace_id,
            "is_recovery": True,  # 🔧 关键：设置恢复标志
            "recovery_attempt": 1,
        }
        
        queue_data = binding_service._parse_queue_data(queue_message)
        
        # 验证初始状态
        assert queue_data.unencrypted_card_password is None
        assert queue_data.is_recovery is True
        
        # 调用恢复逻辑
        recovered_password = await binding_service._handle_recovery_logic(
            db_session, queue_data
        )
        
        # 验证密码恢复成功
        assert recovered_password is not None
        assert recovered_password == "123456"  # 原始密码

    @pytest.mark.asyncio
    async def test_recovery_logic_without_recovery_flag(self, db_session, test_card_record):
        """测试没有恢复标志时的行为（模拟修复前的问题）"""
        # 模拟修复前的队列数据（没有恢复标志）
        queue_message = {
            "record_id": test_card_record.id,
            "merchant_id": test_card_record.merchant_id,
            "trace_id": test_card_record.trace_id,
            "retry_operation": True,
            # 缺少 is_recovery: True
        }
        
        queue_data = binding_service._parse_queue_data(queue_message)
        
        # 验证初始状态
        assert queue_data.unencrypted_card_password is None
        assert queue_data.is_recovery is False  # 这会导致问题
        
        # 在没有恢复标志的情况下，不会调用恢复逻辑
        # 这模拟了修复前的问题场景

    def test_message_format_comparison(self):
        """对比修复前后的消息格式"""
        # 修复前的格式（有问题）
        old_format = {
            "record_id": "test-id",
            "merchant_id": 1,
            "trace_id": "test-trace",
            "retry_operation": True,
        }
        
        # 修复后的格式（正确）
        new_format = {
            "record_id": "test-id",
            "merchant_id": 1,
            "trace_id": "test-trace",
            "is_recovery": True,  # 🔧 新增：恢复标志
            "retry_operation": True,
            "recovery_attempt": 1,  # 🔧 新增：重试次数
        }
        
        # 解析两种格式
        old_queue_data = binding_service._parse_queue_data(old_format)
        new_queue_data = binding_service._parse_queue_data(new_format)
        
        # 验证差异
        assert old_queue_data.is_recovery is False  # 旧格式：不会触发恢复逻辑
        assert new_queue_data.is_recovery is True   # 新格式：会触发恢复逻辑
        
        # 这个差异就是修复的关键所在
        assert old_queue_data.is_recovery != new_queue_data.is_recovery

    @patch('app.utils.queue_producer.send_bind_card_task')
    @pytest.mark.asyncio
    async def test_retry_card_status_update(
        self, 
        mock_send_task, 
        card_record_service, 
        test_card_record, 
        merchant_user,
        db_session
    ):
        """测试重试时卡状态更新"""
        mock_send_task.return_value = None
        
        # 记录原始状态
        original_status = test_card_record.status
        original_retry_count = test_card_record.retry_count
        
        # 执行重试
        await card_record_service.retry_single_card(
            test_card_record.id, 
            merchant_user
        )
        
        # 刷新记录
        db_session.refresh(test_card_record)
        
        # 验证状态更新
        assert test_card_record.status == CardStatus.PENDING  # 重置为pending
        assert test_card_record.retry_count == original_retry_count + 1  # 重试次数增加
        assert test_card_record.error_message is None  # 错误信息清空
        assert test_card_record.department_id is None  # 重置为NULL
        assert test_card_record.walmart_ck_id is None  # 重置为NULL
