package redis

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"github.com/redis/go-redis/v9"

	"walmart-bind-card-gateway/internal/config"
	"walmart-bind-card-gateway/internal/model"
)

// Client Redis客户端接口
type Client interface {
	// 基础操作
	Ping(ctx context.Context) error
	Close() error
	
	// 单个操作
	SetCardStatus(ctx context.Context, status *model.RedisCardStatus) error
	GetCardStatus(ctx context.Context, requestID string) (*model.RedisCardStatus, error)
	DeleteCardStatus(ctx context.Context, requestID string) error
	
	// 批量操作
	BatchSetCardStatus(ctx context.Context, statuses []*model.RedisCardStatus) error
	BatchSetCardStatusFromRequests(ctx context.Context, requests []*model.InternalBindRequest) error
	BatchGetCardStatus(ctx context.Context, requestIDs []string) (map[string]*model.RedisCardStatus, error)
	BatchDeleteCardStatus(ctx context.Context, requestIDs []string) error
	
	// 管道操作
	Pipeline() redis.Pipeliner
	
	// 统计操作
	GetStats(ctx context.Context) map[string]interface{}
}

// redisClient Redis客户端实现
type redisClient struct {
	client *redis.Client
	config *config.RedisConfig
}

// NewRedisClient 创建Redis客户端
func NewRedisClient(cfg *config.RedisConfig) (Client, error) {
	// Redis客户端配置
	options := &redis.Options{
		Addr:         cfg.GetRedisAddr(),
		Password:     cfg.Password,
		DB:           cfg.DB,
		PoolSize:     cfg.PoolSize,
		MinIdleConns: cfg.MinIdleConns,
		MaxRetries:   cfg.MaxRetries,
		DialTimeout:  cfg.DialTimeout,
		ReadTimeout:  cfg.ReadTimeout,
		WriteTimeout: cfg.WriteTimeout,
		PoolTimeout:  cfg.PoolTimeout,
	}
	
	// 创建客户端
	client := redis.NewClient(options)
	
	// 测试连接
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()
	
	if err := client.Ping(ctx).Err(); err != nil {
		return nil, fmt.Errorf("Redis连接测试失败: %w", err)
	}
	
	return &redisClient{
		client: client,
		config: cfg,
	}, nil
}

// Ping 测试连接
func (r *redisClient) Ping(ctx context.Context) error {
	return r.client.Ping(ctx).Err()
}

// Close 关闭连接
func (r *redisClient) Close() error {
	return r.client.Close()
}

// SetCardStatus 设置卡状态
func (r *redisClient) SetCardStatus(ctx context.Context, status *model.RedisCardStatus) error {
	key := status.GetRedisKey()
	
	// 序列化数据
	data, err := json.Marshal(status)
	if err != nil {
		return fmt.Errorf("序列化卡状态失败: %w", err)
	}
	
	// 设置TTL
	ttl := time.Duration(status.TTL) * time.Second
	if ttl <= 0 {
		ttl = 24 * time.Hour // 默认24小时
	}
	
	return r.client.Set(ctx, key, data, ttl).Err()
}

// GetCardStatus 获取卡状态
func (r *redisClient) GetCardStatus(ctx context.Context, requestID string) (*model.RedisCardStatus, error) {
	key := "card_status:" + requestID
	
	data, err := r.client.Get(ctx, key).Result()
	if err != nil {
		if err == redis.Nil {
			return nil, nil // 不存在
		}
		return nil, fmt.Errorf("获取卡状态失败: %w", err)
	}
	
	var status model.RedisCardStatus
	if err := json.Unmarshal([]byte(data), &status); err != nil {
		return nil, fmt.Errorf("反序列化卡状态失败: %w", err)
	}
	
	return &status, nil
}

// DeleteCardStatus 删除卡状态
func (r *redisClient) DeleteCardStatus(ctx context.Context, requestID string) error {
	key := "card_status:" + requestID
	return r.client.Del(ctx, key).Err()
}

// BatchSetCardStatus 批量设置卡状态
func (r *redisClient) BatchSetCardStatus(ctx context.Context, statuses []*model.RedisCardStatus) error {
	if len(statuses) == 0 {
		return nil
	}
	
	// 使用管道批量操作
	pipe := r.client.Pipeline()
	
	for _, status := range statuses {
		key := status.GetRedisKey()
		
		// 序列化数据
		data, err := json.Marshal(status)
		if err != nil {
			return fmt.Errorf("序列化卡状态失败: %w", err)
		}
		
		// 设置TTL
		ttl := time.Duration(status.TTL) * time.Second
		if ttl <= 0 {
			ttl = 24 * time.Hour // 默认24小时
		}
		
		pipe.Set(ctx, key, data, ttl)
	}
	
	// 执行管道
	_, err := pipe.Exec(ctx)
	return err
}

// BatchGetCardStatus 批量获取卡状态
func (r *redisClient) BatchGetCardStatus(ctx context.Context, requestIDs []string) (map[string]*model.RedisCardStatus, error) {
	if len(requestIDs) == 0 {
		return make(map[string]*model.RedisCardStatus), nil
	}
	
	// 构建键列表
	keys := make([]string, len(requestIDs))
	for i, requestID := range requestIDs {
		keys[i] = "card_status:" + requestID
	}
	
	// 批量获取
	results, err := r.client.MGet(ctx, keys...).Result()
	if err != nil {
		return nil, fmt.Errorf("批量获取卡状态失败: %w", err)
	}
	
	// 解析结果
	statusMap := make(map[string]*model.RedisCardStatus)
	for i, result := range results {
		if result == nil {
			continue // 键不存在
		}
		
		data, ok := result.(string)
		if !ok {
			continue // 数据类型错误
		}
		
		var status model.RedisCardStatus
		if err := json.Unmarshal([]byte(data), &status); err != nil {
			continue // 反序列化失败，跳过
		}
		
		statusMap[requestIDs[i]] = &status
	}
	
	return statusMap, nil
}

// BatchDeleteCardStatus 批量删除卡状态
func (r *redisClient) BatchDeleteCardStatus(ctx context.Context, requestIDs []string) error {
	if len(requestIDs) == 0 {
		return nil
	}
	
	// 构建键列表
	keys := make([]string, len(requestIDs))
	for i, requestID := range requestIDs {
		keys[i] = "card_status:" + requestID
	}
	
	return r.client.Del(ctx, keys...).Err()
}

// Pipeline 获取管道
func (r *redisClient) Pipeline() redis.Pipeliner {
	return r.client.Pipeline()
}

// GetStats 获取统计信息
func (r *redisClient) GetStats(ctx context.Context) map[string]interface{} {
	stats := make(map[string]interface{})
	
	// 连接池统计
	poolStats := r.client.PoolStats()
	stats["pool_stats"] = map[string]interface{}{
		"hits":         poolStats.Hits,
		"misses":       poolStats.Misses,
		"timeouts":     poolStats.Timeouts,
		"total_conns":  poolStats.TotalConns,
		"idle_conns":   poolStats.IdleConns,
		"stale_conns":  poolStats.StaleConns,
	}
	
	// Redis信息
	info, err := r.client.Info(ctx, "memory", "stats").Result()
	if err == nil {
		stats["redis_info"] = info
	}
	
	// 键统计
	cardStatusKeys, err := r.client.Keys(ctx, "card_status:*").Result()
	if err == nil {
		stats["card_status_keys_count"] = len(cardStatusKeys)
	}
	
	return stats
}

// BatchSetCardStatusFromRequests 从请求批量设置卡状态
func (r *redisClient) BatchSetCardStatusFromRequests(ctx context.Context, requests []*model.InternalBindRequest) error {
	if len(requests) == 0 {
		return nil
	}
	
	// 转换为Redis状态
	statuses := make([]*model.RedisCardStatus, len(requests))
	for i, req := range requests {
		statuses[i] = &model.RedisCardStatus{
			RequestID:    req.RequestID,
			Status:       model.StatusPending,
			MerchantCode: req.MerchantCode,
			CreatedAt:    req.ReceivedAt,
			TTL:          24 * 3600, // 24小时
		}
	}
	
	return r.BatchSetCardStatus(ctx, statuses)
}

// UpdateCardStatus 更新卡状态
func (r *redisClient) UpdateCardStatus(ctx context.Context, requestID, status string) error {
	// 先获取现有状态
	existingStatus, err := r.GetCardStatus(ctx, requestID)
	if err != nil {
		return err
	}
	
	if existingStatus == nil {
		// 如果不存在，创建新的状态
		newStatus := &model.RedisCardStatus{
			RequestID: requestID,
			Status:    status,
			CreatedAt: time.Now(),
			TTL:       24 * 3600, // 24小时
		}
		return r.SetCardStatus(ctx, newStatus)
	}
	
	// 更新状态
	existingStatus.Status = status
	return r.SetCardStatus(ctx, existingStatus)
}

// BatchUpdateCardStatus 批量更新卡状态
func (r *redisClient) BatchUpdateCardStatus(ctx context.Context, updates map[string]string) error {
	if len(updates) == 0 {
		return nil
	}
	
	// 获取所有请求ID
	requestIDs := make([]string, 0, len(updates))
	for requestID := range updates {
		requestIDs = append(requestIDs, requestID)
	}
	
	// 批量获取现有状态
	existingStatuses, err := r.BatchGetCardStatus(ctx, requestIDs)
	if err != nil {
		return err
	}
	
	// 准备更新的状态
	statusesToUpdate := make([]*model.RedisCardStatus, 0, len(updates))
	
	for requestID, newStatus := range updates {
		if existingStatus, exists := existingStatuses[requestID]; exists {
			// 更新现有状态
			existingStatus.Status = newStatus
			statusesToUpdate = append(statusesToUpdate, existingStatus)
		} else {
			// 创建新状态
			newStatusObj := &model.RedisCardStatus{
				RequestID: requestID,
				Status:    newStatus,
				CreatedAt: time.Now(),
				TTL:       24 * 3600, // 24小时
			}
			statusesToUpdate = append(statusesToUpdate, newStatusObj)
		}
	}
	
	// 批量设置状态
	return r.BatchSetCardStatus(ctx, statusesToUpdate)
}
