<template>
  <el-dialog
    title="权重算法测试结果"
    v-model="visible"
    width="900px"
    :before-close="handleClose"
  >
    <div class="test-results" v-if="testResult">
      <!-- 测试概览 -->
      <div class="overview-section">
        <h4>测试概览</h4>
        <div class="overview-cards">
          <div class="overview-card">
            <div class="card-value">{{ testResult.test_count }}</div>
            <div class="card-label">测试次数</div>
          </div>
          <div class="overview-card">
            <div class="card-value">{{ testResult.total_departments }}</div>
            <div class="card-label">参与部门</div>
          </div>
          <div class="overview-card">
            <div class="card-value">{{ accuracyScore }}%</div>
            <div class="card-label">准确度</div>
          </div>
        </div>
      </div>

      <!-- 分配结果对比 -->
      <div class="comparison-section">
        <h4>理论vs实际分配对比</h4>
        <el-table 
          :data="comparisonData" 
          border 
          size="small"
          :default-sort="{ prop: 'theoreticalPercentage', order: 'descending' }"
        >
          <el-table-column prop="departmentName" label="部门名称" width="150" />
          <el-table-column prop="weight" label="权重" width="80" align="center" />
          <el-table-column label="理论分配" width="120" align="center">
            <template #default="{ row }">
              <span class="percentage theoretical">{{ row.theoreticalPercentage }}%</span>
            </template>
          </el-table-column>
          <el-table-column label="实际分配" width="120" align="center">
            <template #default="{ row }">
              <span class="percentage actual">{{ row.actualPercentage }}%</span>
            </template>
          </el-table-column>
          <el-table-column label="偏差" width="100" align="center">
            <template #default="{ row }">
              <span 
                :class="getDeviationClass(row.deviation)"
                class="deviation"
              >
                {{ row.deviation > 0 ? '+' : '' }}{{ row.deviation }}%
              </span>
            </template>
          </el-table-column>
          <el-table-column label="选中次数" width="100" align="center">
            <template #default="{ row }">
              {{ row.selectionCount }} / {{ testResult.test_count }}
            </template>
          </el-table-column>
          <el-table-column label="准确度评价" width="120" align="center">
            <template #default="{ row }">
              <el-tag 
                :type="getAccuracyTagType(Math.abs(row.deviation))"
                size="small"
              >
                {{ getAccuracyText(Math.abs(row.deviation)) }}
              </el-tag>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <!-- 可视化图表 -->
      <div class="chart-section">
        <h4>分配比例可视化</h4>
        <div class="chart-container">
          <div class="chart-legend">
            <div class="legend-item">
              <span class="legend-color theoretical"></span>
              <span>理论分配</span>
            </div>
            <div class="legend-item">
              <span class="legend-color actual"></span>
              <span>实际分配</span>
            </div>
          </div>
          <div class="chart-bars">
            <div 
              v-for="item in comparisonData" 
              :key="item.departmentId"
              class="chart-bar-group"
            >
              <div class="bar-label">{{ item.departmentName }}</div>
              <div class="bars">
                <div class="bar-container">
                  <div 
                    class="bar theoretical"
                    :style="{ width: item.theoreticalPercentage + '%' }"
                  ></div>
                  <span class="bar-text">{{ item.theoreticalPercentage }}%</span>
                </div>
                <div class="bar-container">
                  <div 
                    class="bar actual"
                    :style="{ width: item.actualPercentage + '%' }"
                  ></div>
                  <span class="bar-text">{{ item.actualPercentage }}%</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 测试建议 -->
      <div class="suggestions-section">
        <h4>测试建议</h4>
        <div class="suggestions">
          <el-alert
            v-for="suggestion in suggestions"
            :key="suggestion.type"
            :title="suggestion.title"
            :description="suggestion.description"
            :type="suggestion.type"
            :closable="false"
            style="margin-bottom: 10px"
          />
        </div>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
        <el-button type="primary" @click="runNewTest">重新测试</el-button>
        <el-button type="info" @click="exportResults">导出结果</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script>
export default {
  name: 'WeightAlgorithmTestDialog',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    testResult: {
      type: Object,
      default: null
    }
  },
  computed: {
    comparisonData() {
      if (!this.testResult || !this.testResult.department_selection_count) {
        return []
      }

      const selectionCount = this.testResult.department_selection_count
      const theoretical = this.testResult.theoretical_distribution || {}
      
      return Object.keys(selectionCount).map(deptId => {
        const deptInfo = selectionCount[deptId]
        const theoreticalPercentage = theoretical[deptId] || 0
        const actualPercentage = deptInfo.actual_percentage || 0
        const deviation = Number((actualPercentage - theoreticalPercentage).toFixed(1))
        
        return {
          departmentId: parseInt(deptId),
          departmentName: deptInfo.name || `部门${deptId}`,
          weight: deptInfo.weight || 0,
          theoreticalPercentage: Number(theoreticalPercentage.toFixed(1)),
          actualPercentage: Number(actualPercentage.toFixed(1)),
          deviation,
          selectionCount: deptInfo.count || 0
        }
      }).sort((a, b) => b.theoreticalPercentage - a.theoreticalPercentage)
    },
    
    accuracyScore() {
      if (!this.comparisonData.length) return 0
      
      const totalDeviation = this.comparisonData.reduce((sum, item) => {
        return sum + Math.abs(item.deviation)
      }, 0)
      
      const averageDeviation = totalDeviation / this.comparisonData.length
      const accuracy = Math.max(0, 100 - averageDeviation * 2)
      
      return Number(accuracy.toFixed(1))
    },
    
    suggestions() {
      const suggestions = []
      
      if (this.accuracyScore >= 90) {
        suggestions.push({
          type: 'success',
          title: '权重算法表现优秀',
          description: '实际分配与理论分配高度一致，权重配置合理。'
        })
      } else if (this.accuracyScore >= 70) {
        suggestions.push({
          type: 'warning',
          title: '权重算法表现良好',
          description: '存在轻微偏差，可以考虑增加测试次数或微调权重配置。'
        })
      } else {
        suggestions.push({
          type: 'error',
          title: '权重算法需要优化',
          description: '实际分配与理论分配偏差较大，建议检查权重配置或算法实现。'
        })
      }
      
      // 检查是否有部门权重过高
      const highWeightDepts = this.comparisonData.filter(item => item.theoreticalPercentage > 80)
      if (highWeightDepts.length > 0) {
        suggestions.push({
          type: 'warning',
          title: '权重分配不均',
          description: `部门"${highWeightDepts[0].departmentName}"权重占比过高(${highWeightDepts[0].theoreticalPercentage}%)，建议适当调整权重分配。`
        })
      }
      
      // 检查是否有部门从未被选中
      const neverSelectedDepts = this.comparisonData.filter(item => item.selectionCount === 0)
      if (neverSelectedDepts.length > 0) {
        suggestions.push({
          type: 'error',
          title: '部门未参与分配',
          description: `部门"${neverSelectedDepts[0].departmentName}"在测试中从未被选中，请检查该部门的CK可用性。`
        })
      }
      
      return suggestions
    }
  },
  methods: {
    getDeviationClass(deviation) {
      const abs = Math.abs(deviation)
      if (abs <= 2) return 'deviation-good'
      if (abs <= 5) return 'deviation-warning'
      return 'deviation-error'
    },
    
    getAccuracyTagType(deviation) {
      if (deviation <= 2) return 'success'
      if (deviation <= 5) return 'warning'
      return 'danger'
    },
    
    getAccuracyText(deviation) {
      if (deviation <= 2) return '优秀'
      if (deviation <= 5) return '良好'
      return '需优化'
    },
    
    handleClose() {
      this.$emit('update:visible', false)
    },
    
    runNewTest() {
      this.$emit('run-new-test')
    },
    
    exportResults() {
      // 导出测试结果为CSV或JSON
      const data = {
        testOverview: {
          testCount: this.testResult.test_count,
          totalDepartments: this.testResult.total_departments,
          accuracyScore: this.accuracyScore
        },
        comparisonData: this.comparisonData,
        suggestions: this.suggestions
      }
      
      const blob = new Blob([JSON.stringify(data, null, 2)], {
        type: 'application/json'
      })
      
      const url = URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.href = url
      a.download = `weight-algorithm-test-${new Date().getTime()}.json`
      a.click()
      URL.revokeObjectURL(url)
      
      this.$message.success('测试结果已导出')
    }
  }
}
</script>

<style scoped>
.test-results {
  .overview-section, .comparison-section, .chart-section, .suggestions-section {
    margin-bottom: 30px;
    
    h4 {
      margin-bottom: 15px;
      color: #303133;
      font-size: 16px;
      font-weight: 600;
      border-bottom: 2px solid #409EFF;
      padding-bottom: 5px;
    }
  }
  
  .overview-cards {
    display: flex;
    gap: 20px;
    
    .overview-card {
      flex: 1;
      text-align: center;
      padding: 20px;
      background: #f5f7fa;
      border-radius: 8px;
      border: 1px solid #EBEEF5;
      
      .card-value {
        font-size: 28px;
        font-weight: 600;
        color: #409EFF;
        margin-bottom: 5px;
      }
      
      .card-label {
        color: #606266;
        font-size: 14px;
      }
    }
  }
  
  .percentage {
    font-weight: 600;
    
    &.theoretical {
      color: #409EFF;
    }
    
    &.actual {
      color: #67C23A;
    }
  }
  
  .deviation {
    font-weight: 600;
    
    &.deviation-good {
      color: #67C23A;
    }
    
    &.deviation-warning {
      color: #E6A23C;
    }
    
    &.deviation-error {
      color: #F56C6C;
    }
  }
  
  .chart-container {
    .chart-legend {
      display: flex;
      gap: 20px;
      margin-bottom: 20px;
      justify-content: center;
      
      .legend-item {
        display: flex;
        align-items: center;
        gap: 5px;
        
        .legend-color {
          width: 16px;
          height: 16px;
          border-radius: 2px;
          
          &.theoretical {
            background: #409EFF;
          }
          
          &.actual {
            background: #67C23A;
          }
        }
      }
    }
    
    .chart-bars {
      .chart-bar-group {
        margin-bottom: 15px;
        
        .bar-label {
          font-weight: 500;
          margin-bottom: 5px;
          color: #303133;
        }
        
        .bars {
          .bar-container {
            display: flex;
            align-items: center;
            margin-bottom: 5px;
            height: 20px;
            
            .bar {
              height: 100%;
              border-radius: 2px;
              min-width: 2px;
              transition: width 0.3s;
              
              &.theoretical {
                background: #409EFF;
              }
              
              &.actual {
                background: #67C23A;
              }
            }
            
            .bar-text {
              margin-left: 10px;
              font-size: 12px;
              color: #606266;
              min-width: 40px;
            }
          }
        }
      }
    }
  }
  
  .suggestions {
    max-height: 200px;
    overflow-y: auto;
  }
}

.dialog-footer {
  text-align: right;
}
</style>
