import { http } from "@/api/request";
import { API_URLS, replaceUrlParams } from "./config";

const { USER } = API_URLS;
/**
 * 用户管理相关API
 * 注意：登录/登出/获取当前用户信息等认证相关功能已移至 auth.js
 */
export const userApi = {
  // 获取用户信息
  getProfile() {
    return http
      .get(USER.PROFILE)
      .then((res) => res.data || res)
      .catch((error) => {
        console.error("获取用户信息失败:", error);
        throw new Error("获取用户信息失败：" + error.message);
      });
  },

  // 获取用户列表
  async getList(params) {
    try {
      // 过滤掉undefined和空字符串参数
      const filteredParams = Object.fromEntries(
        Object.entries(params || {}).filter(
          ([key, value]) =>
            value !== undefined && value !== null && value !== ""
        )
      );
      const response = await http.get(USER.LIST, { params: filteredParams });
      // 统一处理响应格式
      return response.data || response;
    } catch (error) {
      throw new Error("获取用户列表失败：" + error.message);
    }
  },

  // 获取用户详情
  async getDetail(id) {
    try {
      const url = replaceUrlParams(USER.DETAIL, { id });
      const response = await http.get(url);
      return response.data;
    } catch (error) {
      throw new Error("获取用户详情失败：" + error.message);
    }
  },

  // 创建用户
  async create(data) {
    try {
      const response = await http.post(USER.CREATE, data);
      return response.data;
    } catch (error) {
      throw new Error("创建用户失败：" + error.message);
    }
  },

  // 更新用户
  async update(id, data) {
    try {
      const url = replaceUrlParams(USER.UPDATE, { id });
      const response = await http.put(url, data);
      return response.data;
    } catch (error) {
      throw new Error("更新用户失败：" + error.message);
    }
  },

  // 删除用户
  async delete(id) {
    try {
      const url = replaceUrlParams(USER.DELETE, { id });
      const response = await http.delete(url);
      return response.data;
    } catch (error) {
      throw new Error("删除用户失败：" + error.message);
    }
  },

  // 批量删除用户
  async batchDelete(ids) {
    try {
      const response = await http.post(USER.BATCH_DELETE, { ids });
      return response.data;
    } catch (error) {
      throw new Error("批量删除用户失败：" + error.message);
    }
  },

  // 重置用户密码
  async resetPassword(id, newPassword) {
    try {
      const url = replaceUrlParams(USER.RESET_PASSWORD, { id });
      const response = await http.post(url, {
        new_password: newPassword,
      });
      return response.data;
    } catch (error) {
      throw new Error("重置密码失败：" + error.message);
    }
  },

  // 修改密码
  async updatePassword(data) {
    try {
      const response = await http.post(USER.UPDATE_PASSWORD, data);
      return response.data;
    } catch (error) {
      throw new Error("修改密码失败：" + error.message);
    }
  },

  // 获取用户权限
  async getPermissions() {
    try {
      const response = await http.get(USER.PERMISSIONS);
      return response.data;
    } catch (error) {
      throw new Error("获取权限失败：" + error.message);
    }
  },

  // 激活/禁用用户
  async activateUser(id, isActive) {
    try {
      const url = replaceUrlParams(USER.STATUS, { id });
      const response = await http.patch(url, {
        is_active: isActive,
      });
      return response.data;
    } catch (error) {
      throw new Error("操作失败：" + error.message);
    }
  },

  // 获取用户角色
  async getUserRoles(userId) {
    try {
      const response = await http.get(`/users/${userId}/roles`);
      return response.data;
    } catch (error) {
      throw new Error("获取用户角色失败：" + error.message);
    }
  },

  // 更新用户角色
  async updateUserRoles(userId, roleIds) {
    try {
      const response = await http.put(`/users/${userId}/roles`, roleIds);
      return response.data;
    } catch (error) {
      throw new Error("更新用户角色失败：" + error.message);
    }
  },

  // 为用户分配角色
  async assignRoleToUser(userId, roleId) {
    try {
      const response = await http.post(`/users/${userId}/roles/${roleId}`);
      return response.data;
    } catch (error) {
      throw new Error("分配角色失败：" + error.message);
    }
  },

  // 移除用户角色
  async removeRoleFromUser(userId, roleId) {
    try {
      const response = await http.delete(`/users/${userId}/roles/${roleId}`);
      return response.data;
    } catch (error) {
      throw new Error("移除角色失败：" + error.message);
    }
  },

  // 更新当前用户信息（个人资料）
  async updateUserInfo(data) {
    try {
      const response = await http.put(USER.ME, data);
      return response.data;
    } catch (error) {
      throw new Error("更新用户信息失败：" + error.message);
    }
  },

  // 修改密码
  async changePassword(data) {
    try {
      const response = await http.post(USER.CHANGE_PASSWORD, data);
      return response.data;
    } catch (error) {
      throw new Error("修改密码失败：" + error.message);
    }
  },

  // 搜索用户
  async searchUsers(params) {
    try {
      const response = await http.get(USER.SEARCH, { params });
      return response.data;
    } catch (error) {
      throw new Error("搜索用户失败：" + error.message);
    }
  },

  // 注意：获取当前用户权限和菜单的功能已移至 auth.js
  // 如需使用，请导入 authApi.getUserPermissions() 和 authApi.getUserMenus()
};
