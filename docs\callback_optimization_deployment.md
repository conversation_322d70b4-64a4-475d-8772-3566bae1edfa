# 沃尔玛绑卡系统回调优化部署指南

## 概述

本文档描述了如何在生产环境（Docker容器）中部署优化的回调系统。所有优化都已集成到主服务中，无需额外的独立服务。

## 架构变更

### 优化前架构
```
FastAPI应用 → 原始回调服务 → HTTP请求
```

### 优化后架构
```
FastAPI应用 → 集成回调管理器 → 优化回调服务 → HTTP连接池
                ↓
            回调监控服务 → 性能指标收集
```

## 核心组件

### 1. IntegratedCallbackManager
- **功能**: 管理所有回调相关服务的生命周期
- **集成方式**: 在FastAPI应用启动时自动初始化
- **位置**: `app/services/integrated_callback_manager.py`

### 2. OptimizedCallbackService
- **功能**: 提供高性能的回调处理
- **特性**: HTTP连接池复用、批处理支持、独立数据库会话
- **位置**: `app/services/optimized_callback_service.py`

### 3. CallbackMonitorService
- **功能**: 实时性能监控和指标收集
- **特性**: 自动告警、历史数据存储、性能分析
- **位置**: `app/services/callback_monitor_service.py`

### 4. CallbackServiceAdapter
- **功能**: 提供平滑迁移和兼容性支持
- **特性**: 自动回退、服务切换、状态监控
- **位置**: `app/services/callback_service_adapter.py`

## 配置更新

### 1. config.yaml配置
```yaml
# 消息队列配置
rabbitmq:
  host: localhost
  port: 5672
  user: walmart_card
  password: your_rabbitmq_password
  vhost: /walmart_card
  # 性能优化配置
  consumer_prefetch_count: 50
  callback_consumer_prefetch_count: 100

# 性能配置
performance:
  # 数据库连接池 - 针对高并发优化
  db_pool_size: 50
  db_pool_max_overflow: 100
  db_pool_timeout: 30
  db_pool_recycle: 3600
  db_pool_pre_ping: true
  
  # Redis连接池
  redis_pool_size: 10
  redis_pool_timeout: 5
  
  # 缓存配置
  cache_default_timeout: 300
  cache_user_timeout: 1800
  cache_merchant_timeout: 3600
```

### 2. 环境变量（Docker）
```bash
# 数据库配置
DB_HOST=mysql_container
DB_PORT=3306
DB_USER=walmart_card
DB_PASSWORD=your_password
DB_NAME=walmart_bind_card

# RabbitMQ配置
RABBITMQ_HOST=rabbitmq_container
RABBITMQ_PORT=5672
RABBITMQ_USER=walmart_card
RABBITMQ_PASS=your_password
RABBITMQ_VHOST=/walmart_card

# 性能配置
RABBITMQ_CONSUMER_PREFETCH_COUNT=50
RABBITMQ_CALLBACK_CONSUMER_PREFETCH_COUNT=100
```

## 部署步骤

### 1. 更新配置文件
```bash
# 复制配置模板
cp config.yaml.example config.yaml

# 根据生产环境调整配置
vim config.yaml
```

### 2. 构建Docker镜像
```bash
# 构建包含优化的新镜像
docker build -t walmart-bind-card:optimized .
```

### 3. 更新Docker Compose
```yaml
version: '3.8'
services:
  app:
    image: walmart-bind-card:optimized
    environment:
      - DB_HOST=mysql
      - RABBITMQ_HOST=rabbitmq
      - RABBITMQ_CONSUMER_PREFETCH_COUNT=50
      - RABBITMQ_CALLBACK_CONSUMER_PREFETCH_COUNT=100
    depends_on:
      - mysql
      - rabbitmq
    
  mysql:
    image: mysql:8.0
    environment:
      MYSQL_ROOT_PASSWORD: root_password
      MYSQL_DATABASE: walmart_bind_card
    
  rabbitmq:
    image: rabbitmq:3-management
    environment:
      RABBITMQ_DEFAULT_USER: walmart_card
      RABBITMQ_DEFAULT_PASS: your_password
```

### 4. 启动服务
```bash
# 启动所有服务
docker-compose up -d

# 检查服务状态
docker-compose ps

# 查看应用日志
docker-compose logs -f app
```

## 监控和验证

### 1. 健康检查
```bash
# 基本健康检查
curl http://localhost:8000/health

# 回调服务健康检查
curl http://localhost:8000/api/v1/callback-monitor/health
```

### 2. 性能监控
```bash
# 获取当前统计
curl -H "Authorization: Bearer YOUR_TOKEN" \
  http://localhost:8000/api/v1/callback-monitor/stats/current

# 获取性能摘要
curl -H "Authorization: Bearer YOUR_TOKEN" \
  http://localhost:8000/api/v1/callback-monitor/stats/summary?hours=24

# 检查告警
curl -H "Authorization: Bearer YOUR_TOKEN" \
  http://localhost:8000/api/v1/callback-monitor/alerts
```

### 3. 性能测试
```bash
# 运行性能测试（如果有测试脚本）
python scripts/callback_performance_test.py --concurrent 100 --total 1000
```

## 故障排除

### 1. 常见问题

#### 问题：回调服务启动失败
```bash
# 检查日志
docker-compose logs app | grep callback

# 检查配置
docker-compose exec app cat config.yaml

# 检查环境变量
docker-compose exec app env | grep RABBITMQ
```

#### 问题：数据库连接池耗尽
```bash
# 检查连接池状态
curl http://localhost:8000/api/v1/callback-monitor/health

# 调整连接池配置
# 在config.yaml中增加db_pool_size和db_pool_max_overflow
```

#### 问题：RabbitMQ消息积压
```bash
# 检查队列状态
docker-compose exec rabbitmq rabbitmqctl list_queues

# 增加消费者预取数量
# 在config.yaml中调整callback_consumer_prefetch_count
```

### 2. 日志分析
```bash
# 查看回调相关日志
docker-compose logs app | grep -E "(callback|CALLBACK)"

# 查看性能监控日志
docker-compose logs app | grep "回调监控"

# 查看错误日志
docker-compose logs app | grep -E "(ERROR|CRITICAL)"
```

### 3. 性能调优

#### 数据库优化
```yaml
performance:
  db_pool_size: 100        # 根据并发需求调整
  db_pool_max_overflow: 200 # 峰值时的额外连接
  db_pool_timeout: 60      # 连接超时时间
```

#### RabbitMQ优化
```yaml
rabbitmq:
  consumer_prefetch_count: 100          # 绑卡队列预取
  callback_consumer_prefetch_count: 200 # 回调队列预取
```

## 回滚方案

如果优化版本出现问题，可以快速回滚：

### 1. 服务级回滚
```python
# 在应用中切换到原有服务
from app.services.callback_service_adapter import callback_service_adapter
callback_service_adapter.switch_to_optimized(False)
```

### 2. 配置级回滚
```yaml
# 恢复原有配置值
rabbitmq:
  consumer_prefetch_count: 10

performance:
  db_pool_size: 20
  db_pool_max_overflow: 30
```

### 3. 镜像级回滚
```bash
# 回滚到之前的镜像版本
docker-compose down
docker-compose up -d --scale app=0
docker tag walmart-bind-card:previous walmart-bind-card:current
docker-compose up -d
```

## 性能预期

基于优化配置，预期性能提升：

| 指标 | 优化前 | 优化后 | 提升 |
|------|--------|--------|------|
| 并发处理能力 | 10/批次 | 100/批次 | 10倍 |
| 成功率 | 60% | >95% | 58%提升 |
| 平均响应时间 | 21537ms | <5000ms | 75%减少 |
| 数据库连接 | 30个 | 150个 | 5倍 |

## 联系支持

如果在部署过程中遇到问题，请：

1. 检查日志文件
2. 验证配置文件
3. 运行健康检查
4. 查看监控指标
5. 联系技术支持团队
