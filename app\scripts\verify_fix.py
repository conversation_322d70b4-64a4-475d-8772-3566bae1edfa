#!/usr/bin/env python3
"""
修复效果验证脚本
验证CK计数修复是否成功，以及原子性绑卡服务是否正常工作
"""

import sys
import os
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from sqlalchemy.orm import Session
from app.db.session import SessionLocal
from app.models.walmart_ck import WalmartCK
from app.models.card_record import CardRecord
from app.core.logging import get_logger

logger = get_logger(__name__)


def verify_ck_count_consistency():
    """验证CK计数一致性"""
    logger.info("🔍 验证CK计数一致性...")
    
    db = SessionLocal()
    try:
        # 获取所有活跃的CK
        cks = db.query(WalmartCK).filter(
            WalmartCK.is_deleted == False
        ).all()
        
        inconsistent_count = 0
        total_checked = len(cks)
        
        for ck in cks:
            # 计算实际绑卡成功次数
            actual_count = db.query(CardRecord).filter(
                CardRecord.walmart_ck_id == ck.id,
                CardRecord.status == 'success'
            ).count()
            
            current_count = ck.bind_count
            
            if actual_count != current_count:
                inconsistent_count += 1
                logger.warning(
                    f"❌ CK {ck.id}: bind_count={current_count}, 实际成功={actual_count}, "
                    f"差异={actual_count - current_count}"
                )
            else:
                logger.debug(f"✅ CK {ck.id}: 计数一致 ({current_count})")
        
        logger.info(f"📊 检查结果: 总共检查 {total_checked} 个CK，发现 {inconsistent_count} 个不一致")
        
        if inconsistent_count == 0:
            logger.info("🎉 所有CK计数都是一致的！")
            return True
        else:
            logger.warning(f"⚠️  发现 {inconsistent_count} 个CK计数不一致")
            return False
            
    except Exception as e:
        logger.error(f"验证过程中发生错误: {e}")
        return False
    finally:
        db.close()


def check_atomic_service_availability():
    """检查原子性绑卡服务是否可用"""
    logger.info("🔍 检查原子性绑卡服务可用性...")
    
    try:
        from app.services.atomic_binding_service import AtomicBindingService, SafeBindingWrapper
        logger.info("✅ AtomicBindingService 导入成功")
        
        # 尝试创建服务实例
        db = SessionLocal()
        try:
            atomic_service = AtomicBindingService(db)
            safe_wrapper = SafeBindingWrapper(db)
            logger.info("✅ 原子性绑卡服务实例创建成功")
            return True
        finally:
            db.close()
            
    except ImportError as e:
        logger.error(f"❌ 原子性绑卡服务导入失败: {e}")
        return False
    except Exception as e:
        logger.error(f"❌ 原子性绑卡服务创建失败: {e}")
        return False


def check_service_integration():
    """检查服务集成情况"""
    logger.info("🔍 检查服务集成情况...")
    
    try:
        # 检查card_record_service是否使用了原子性服务
        from app.services.card_record_service import CardRecordService
        
        # 读取源码检查是否包含原子性服务的导入
        import inspect
        source = inspect.getsource(CardRecordService._update_bind_result)
        
        if 'atomic_binding_service' in source or 'SafeBindingWrapper' in source:
            logger.info("✅ CardRecordService 已集成原子性绑卡服务")
            return True
        else:
            logger.warning("⚠️  CardRecordService 可能未完全集成原子性绑卡服务")
            return False
            
    except Exception as e:
        logger.error(f"检查服务集成时发生错误: {e}")
        return False


def generate_summary_report():
    """生成汇总报告"""
    logger.info("=" * 80)
    logger.info("📊 CK计数修复效果验证报告")
    logger.info("=" * 80)
    logger.info(f"验证时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 执行各项检查
    checks = [
        ("CK计数一致性", verify_ck_count_consistency),
        ("原子性服务可用性", check_atomic_service_availability),
        ("服务集成情况", check_service_integration)
    ]
    
    results = {}
    all_passed = True
    
    for check_name, check_func in checks:
        logger.info(f"\n🔍 {check_name}检查:")
        try:
            result = check_func()
            results[check_name] = result
            if not result:
                all_passed = False
        except Exception as e:
            logger.error(f"❌ {check_name}检查失败: {e}")
            results[check_name] = False
            all_passed = False
    
    # 生成总结
    logger.info("\n" + "=" * 80)
    logger.info("📋 检查结果汇总:")
    
    for check_name, result in results.items():
        status = "✅ 通过" if result else "❌ 失败"
        logger.info(f"  {check_name}: {status}")
    
    if all_passed:
        logger.info("\n🎉 所有检查都通过了！CK计数修复成功。")
        logger.info("💡 建议:")
        logger.info("  1. 定期运行监控脚本: python app/scripts/monitor_ck_consistency.py --once")
        logger.info("  2. 在生产环境中启用定期监控")
    else:
        logger.warning("\n⚠️  部分检查未通过，需要进一步处理。")
        logger.info("💡 建议:")
        logger.info("  1. 运行修复脚本: python app/scripts/fix_ck_count_consistency.py --auto")
        logger.info("  2. 检查原子性绑卡服务的集成情况")
        logger.info("  3. 验证绑卡流程是否正确使用了新的服务")
    
    logger.info("=" * 80)
    
    return all_passed


def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description="CK计数修复效果验证工具")
    parser.add_argument("--consistency-only", action="store_true", help="只检查一致性")
    parser.add_argument("--service-only", action="store_true", help="只检查服务可用性")
    
    args = parser.parse_args()
    
    try:
        if args.consistency_only:
            success = verify_ck_count_consistency()
        elif args.service_only:
            success = check_atomic_service_availability()
        else:
            success = generate_summary_report()
        
        if success:
            sys.exit(0)
        else:
            sys.exit(1)
            
    except Exception as e:
        logger.error(f"验证失败: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
