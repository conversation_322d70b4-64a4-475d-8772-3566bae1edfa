#!/usr/bin/env python3
"""
双因子认证功能综合测试脚本
"""

import os
import sys
import subprocess
import time
import requests
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))


class TOTPTestRunner:
    """TOTP测试运行器"""

    def __init__(self):
        self.base_url = "http://localhost:2000"
        self.api_url = "http://localhost:8000"
        self.project_root = project_root

    def check_server_status(self):
        """检查服务器状态"""
        print("🔍 检查服务器状态...")
        
        # 检查后端API服务器
        try:
            response = requests.get(f"{self.api_url}/api/v1/health", timeout=5)
            if response.status_code == 200:
                print("✅ 后端API服务器运行正常")
            else:
                print(f"❌ 后端API服务器响应异常: {response.status_code}")
                return False
        except requests.exceptions.RequestException as e:
            print(f"❌ 无法连接到后端API服务器: {e}")
            return False

        # 检查前端服务器
        try:
            response = requests.get(self.base_url, timeout=5)
            if response.status_code == 200:
                print("✅ 前端服务器运行正常")
            else:
                print(f"❌ 前端服务器响应异常: {response.status_code}")
                return False
        except requests.exceptions.RequestException as e:
            print(f"❌ 无法连接到前端服务器: {e}")
            return False

        return True

    def check_dependencies(self):
        """检查依赖项"""
        print("🔍 检查依赖项...")
        
        required_packages = ['pyotp', 'qrcode', 'cryptography', 'playwright']
        missing_packages = []
        
        for package in required_packages:
            try:
                __import__(package)
                print(f"✅ {package} 已安装")
            except ImportError:
                print(f"❌ {package} 未安装")
                missing_packages.append(package)
        
        if missing_packages:
            print(f"\n请安装缺失的依赖项:")
            print(f"pip install {' '.join(missing_packages)}")
            return False
        
        return True

    def run_backend_tests(self):
        """运行后端测试"""
        print("\n🧪 运行后端API测试...")
        
        test_file = self.project_root / "test" / "totp" / "test_totp_api.py"
        if not test_file.exists():
            print(f"❌ 测试文件不存在: {test_file}")
            return False
        
        try:
            result = subprocess.run([
                sys.executable, "-m", "pytest", 
                str(test_file), 
                "-v", 
                "--tb=short"
            ], cwd=self.project_root, capture_output=True, text=True)
            
            print("后端测试输出:")
            print(result.stdout)
            if result.stderr:
                print("错误输出:")
                print(result.stderr)
            
            if result.returncode == 0:
                print("✅ 后端API测试通过")
                return True
            else:
                print("❌ 后端API测试失败")
                return False
                
        except Exception as e:
            print(f"❌ 运行后端测试时出错: {e}")
            return False

    def run_frontend_tests(self):
        """运行前端测试"""
        print("\n🧪 运行前端功能测试...")
        
        test_file = self.project_root / "test" / "totp" / "test_totp_frontend.py"
        if not test_file.exists():
            print(f"❌ 测试文件不存在: {test_file}")
            return False
        
        try:
            result = subprocess.run([
                sys.executable, str(test_file)
            ], cwd=self.project_root, capture_output=True, text=True)
            
            print("前端测试输出:")
            print(result.stdout)
            if result.stderr:
                print("错误输出:")
                print(result.stderr)
            
            if result.returncode == 0:
                print("✅ 前端功能测试通过")
                return True
            else:
                print("❌ 前端功能测试失败")
                return False
                
        except Exception as e:
            print(f"❌ 运行前端测试时出错: {e}")
            return False

    def test_totp_service_unit(self):
        """测试TOTP服务单元功能"""
        print("\n🧪 测试TOTP服务单元功能...")
        
        try:
            from app.services.totp_service import TOTPService
            from app.db.session import SessionLocal
            from app.models.user import User
            import pyotp
            
            db = SessionLocal()
            
            # 创建测试用户
            test_user = User(
                username="totp_unit_test",
                hashed_password="test_hash",
                email="<EMAIL>"
            )
            db.add(test_user)
            db.commit()
            
            try:
                totp_service = TOTPService(db)
                
                # 测试密钥生成
                setup_result = totp_service.setup_totp(test_user)
                assert setup_result.secret
                assert setup_result.qr_code_url
                assert len(setup_result.backup_codes) == 10
                print("✅ TOTP设置功能正常")
                
                # 测试验证码验证
                totp = pyotp.TOTP(setup_result.secret)
                valid_code = totp.now()
                verify_result = totp_service.verify_totp(test_user, valid_code)
                assert verify_result.success
                print("✅ TOTP验证功能正常")
                
                # 测试启用功能
                enable_result = totp_service.enable_totp(test_user, valid_code)
                assert enable_result
                print("✅ TOTP启用功能正常")
                
                # 测试状态获取
                status = totp_service.get_totp_status(test_user)
                assert status.enabled
                print("✅ TOTP状态获取功能正常")
                
                print("✅ TOTP服务单元测试通过")
                return True
                
            finally:
                # 清理测试数据
                db.delete(test_user)
                db.commit()
                db.close()
                
        except Exception as e:
            print(f"❌ TOTP服务单元测试失败: {e}")
            return False

    def generate_test_report(self, results):
        """生成测试报告"""
        print("\n" + "="*60)
        print("📊 双因子认证功能测试报告")
        print("="*60)
        
        total_tests = len(results)
        passed_tests = sum(1 for result in results.values() if result)
        
        for test_name, result in results.items():
            status = "✅ 通过" if result else "❌ 失败"
            print(f"{test_name}: {status}")
        
        print(f"\n总计: {passed_tests}/{total_tests} 个测试通过")
        print(f"成功率: {(passed_tests/total_tests)*100:.1f}%")
        
        if passed_tests == total_tests:
            print("\n🎉 所有测试通过！双因子认证功能正常工作。")
        else:
            print(f"\n⚠️ 有 {total_tests - passed_tests} 个测试失败，请检查相关功能。")
        
        return passed_tests == total_tests

    def run_all_tests(self):
        """运行所有测试"""
        print("🚀 开始双因子认证功能综合测试")
        print("="*60)
        
        results = {}
        
        # 1. 检查依赖项
        results["依赖项检查"] = self.check_dependencies()
        if not results["依赖项检查"]:
            print("❌ 依赖项检查失败，停止测试")
            return False
        
        # 2. 检查服务器状态
        results["服务器状态检查"] = self.check_server_status()
        if not results["服务器状态检查"]:
            print("❌ 服务器状态检查失败，请确保服务器正在运行")
            return False
        
        # 3. TOTP服务单元测试
        results["TOTP服务单元测试"] = self.test_totp_service_unit()
        
        # 4. 后端API测试
        results["后端API测试"] = self.run_backend_tests()
        
        # 5. 前端功能测试
        results["前端功能测试"] = self.run_frontend_tests()
        
        # 生成测试报告
        return self.generate_test_report(results)


def main():
    """主函数"""
    runner = TOTPTestRunner()
    success = runner.run_all_tests()
    
    if success:
        print("\n✅ 双因子认证功能测试完成，所有功能正常！")
        return 0
    else:
        print("\n❌ 双因子认证功能测试发现问题，请检查失败的测试项。")
        return 1


if __name__ == "__main__":
    exit(main())
