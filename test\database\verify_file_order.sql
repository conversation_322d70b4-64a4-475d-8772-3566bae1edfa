-- ========================================
-- MySQL初始化脚本文件顺序验证脚本
-- 
-- 目的：验证文件命名冲突已解决，执行顺序正确
-- 使用方法：在测试环境中执行此脚本
-- ========================================

-- 设置连接字符集
SET NAMES utf8mb4 COLLATE utf8mb4_unicode_ci;
SET time_zone = '+08:00';

USE `walmart_card_db`;

-- ========================================
-- 1. 检查迁移日志表和执行状态
-- ========================================
SELECT 
    '=== 文件执行顺序验证 ===' as test_section;

-- 检查迁移日志表是否存在
SELECT 
    CASE 
        WHEN COUNT(*) > 0 THEN '✓ migration_logs表存在'
        ELSE '✗ migration_logs表不存在'
    END as migration_table_check
FROM information_schema.tables 
WHERE table_schema = 'walmart_card_db' 
AND table_name = 'migration_logs';

-- ========================================
-- 2. 检查关键修复脚本执行状态
-- ========================================
SELECT 
    '=== 关键修复脚本执行状态 ===' as test_section;

-- 检查所有安全修复和升级脚本的执行状态
SELECT 
    migration_name,
    status,
    message,
    created_at,
    completed_at,
    CASE 
        WHEN status = 'completed' THEN '✓ 已完成'
        WHEN status = 'started' THEN '⚠ 进行中'
        ELSE '✗ 异常状态'
    END as status_check
FROM migration_logs 
WHERE migration_name IN (
    'merchant_admin_permissions_fix_v2.1.0',      -- 09-fix_merchant_admin_permissions.sql
    'special_menu_cleanup_v2.1.0',                -- 04-init-data.sql
    'duplicate_merchant_menu_fix_v2.1.0',         -- 08-fix-duplicate-merchant-menu.sql
    'primary_key_upgrade_v2.1.0',                 -- 10-upgrade-primary-keys.sql
    'totp_permissions_fix_v2.1.0'                 -- 12-fix-totp-permissions.sql
)
ORDER BY created_at;

-- ========================================
-- 3. 验证主键升级效果
-- ========================================
SELECT 
    '=== 主键升级效果验证 ===' as test_section;

-- 检查主键类型升级情况
SELECT 
    '主键类型统计' as check_type,
    COUNT(*) as total_primary_keys,
    SUM(CASE WHEN DATA_TYPE = 'bigint' THEN 1 ELSE 0 END) as bigint_keys,
    SUM(CASE WHEN DATA_TYPE = 'int' THEN 1 ELSE 0 END) as int_keys,
    CASE 
        WHEN SUM(CASE WHEN DATA_TYPE = 'int' THEN 1 ELSE 0 END) = 0 
        THEN '✓ 所有主键已升级为BIGINT'
        ELSE CONCAT('⚠ 仍有 ', SUM(CASE WHEN DATA_TYPE = 'int' THEN 1 ELSE 0 END), ' 个INT类型主键')
    END as upgrade_status
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = 'walmart_card_db' 
    AND COLUMN_NAME = 'id' 
    AND COLUMN_KEY = 'PRI';

-- 显示具体的主键类型情况
SELECT 
    '主键类型详情' as check_type,
    TABLE_NAME,
    COLUMN_NAME,
    DATA_TYPE,
    CASE 
        WHEN DATA_TYPE = 'bigint' THEN '✓ 已升级'
        WHEN DATA_TYPE = 'int' THEN '⚠ 需要升级'
        ELSE '? 未知类型'
    END as status
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = 'walmart_card_db' 
    AND COLUMN_NAME = 'id' 
    AND COLUMN_KEY = 'PRI'
ORDER BY TABLE_NAME;

-- ========================================
-- 4. 验证TOTP权限配置
-- ========================================
SELECT 
    '=== TOTP权限配置验证 ===' as test_section;

-- 检查各角色的TOTP权限数量
SELECT 
    'TOTP权限分配统计' as check_type,
    r.name as role_name,
    r.code as role_code,
    COUNT(p.id) as totp_permission_count,
    CASE 
        WHEN r.code = 'super_admin' AND COUNT(p.id) >= 7 THEN '✓ 超管权限正常'
        WHEN r.code = 'merchant_admin' AND COUNT(p.id) >= 7 THEN '✓ 商户管理员权限正常'
        WHEN r.code = 'ck_supplier' AND COUNT(p.id) >= 7 THEN '✓ CK供应商权限正常'
        WHEN r.code = 'finance_admin' AND COUNT(p.id) = 0 THEN '✓ 财务管理员权限正常（无安全权限）'
        ELSE '⚠ 权限配置异常'
    END as permission_status
FROM roles r 
LEFT JOIN role_permissions rp ON r.id = rp.role_id 
LEFT JOIN permissions p ON rp.permission_id = p.id AND p.code LIKE 'api:totp%'
WHERE r.code IN ('super_admin', 'merchant_admin', 'ck_supplier', 'finance_admin')
GROUP BY r.id, r.name, r.code 
ORDER BY r.code;

-- ========================================
-- 5. 验证菜单清理效果
-- ========================================
SELECT 
    '=== 菜单清理效果验证 ===' as test_section;

-- 检查特殊页面菜单清理情况
SELECT 
    '特殊页面菜单检查' as check_type,
    COUNT(*) as remaining_count,
    CASE 
        WHEN COUNT(*) = 0 THEN '✓ 特殊页面菜单已清理'
        ELSE CONCAT('⚠ 仍有 ', COUNT(*), ' 个特殊页面菜单')
    END as cleanup_status
FROM menus 
WHERE code IN ('error:404', 'error:403', 'login');

-- 检查重复商户菜单清理情况
SELECT 
    '重复商户菜单检查' as check_type,
    COUNT(*) as remaining_count,
    CASE 
        WHEN COUNT(*) = 0 THEN '✓ 重复商户菜单已清理'
        ELSE CONCAT('⚠ 仍有 ', COUNT(*), ' 个重复商户菜单')
    END as cleanup_status
FROM menus 
WHERE code = 'system:merchant';

-- ========================================
-- 6. 文件执行顺序验证总结
-- ========================================
SELECT 
    '=== 文件执行顺序验证总结 ===' as test_section;

-- 综合验证结果
SELECT 
    '文件顺序验证结果' as verification_type,
    CASE 
        WHEN (SELECT COUNT(*) FROM migration_logs WHERE status = 'completed') >= 5
        AND (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_SCHEMA = 'walmart_card_db' AND COLUMN_NAME = 'id' AND COLUMN_KEY = 'PRI' AND DATA_TYPE = 'int') = 0
        AND (SELECT COUNT(*) FROM menus WHERE code IN ('error:404', 'error:403', 'login', 'system:merchant')) = 0
        THEN '✓ 所有验证项目通过，文件执行顺序正确'
        ELSE '⚠ 部分验证项目未通过，请检查具体问题'
    END as overall_status;

-- 显示执行顺序摘要
SELECT 
    '执行顺序摘要' as summary_type,
    '文件命名冲突已解决' as naming_conflict,
    '主键升级在权限修复之前执行' as execution_order,
    '所有修复脚本都有安全机制' as safety_mechanism,
    '迁移日志完整记录执行状态' as logging_system;

-- ========================================
-- 7. 建议和下一步
-- ========================================
SELECT 
    '=== 验证完成建议 ===' as test_section;

SELECT 
    '部署建议' as recommendation_type,
    CASE 
        WHEN (SELECT COUNT(*) FROM migration_logs WHERE status = 'completed') >= 5
        THEN '✓ 文件顺序验证通过，可以安全部署'
        ELSE '⚠ 请解决验证中发现的问题后再部署'
    END as deployment_recommendation;

-- ========================================
-- 验证脚本执行完成
-- ========================================
SELECT 
    '=== 文件顺序验证完成 ===' as final_message,
    '文件命名冲突已解决，执行顺序正确' as resolution_status,
    NOW(3) as verification_completion_time;
