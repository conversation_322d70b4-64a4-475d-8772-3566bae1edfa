#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查系统中的权限列表
"""

import sys
import os
import json

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(__file__)))

from test.conftest import TestBase


def check_permissions():
    """检查系统中的权限列表"""
    api_test = TestBase()
    
    # 管理员登录
    admin_token = api_test.login("admin", "7c222fb2927d828af22f592134e8932480637c0d")
    if not admin_token:
        print("❌ 管理员登录失败")
        return
    
    print("✅ 管理员登录成功")
    
    # 获取所有权限
    print("\n=== 获取权限列表 ===")
    status_code, response = api_test.make_request("GET", "/permissions?page_size=100", admin_token)
    
    if status_code == 200:
        data = response.get("data", {})
        permissions = data.get("items", [])
        total = data.get("total", 0)
        
        print(f"总权限数: {total}")
        print(f"当前页权限数: {len(permissions)}")
        
        # 按类型分组显示权限
        menu_permissions = []
        api_permissions = []
        data_permissions = []
        other_permissions = []
        
        for perm in permissions:
            code = perm.get("code", "")
            name = perm.get("name", "")
            resource_type = perm.get("resource_type", "")
            
            if code.startswith("menu:"):
                menu_permissions.append(perm)
            elif code.startswith("api:"):
                api_permissions.append(perm)
            elif code.startswith("data:"):
                data_permissions.append(perm)
            else:
                other_permissions.append(perm)
        
        print(f"\n📋 菜单权限 ({len(menu_permissions)} 个):")
        for perm in menu_permissions[:10]:  # 只显示前10个
            print(f"  {perm['code']} - {perm['name']}")
        if len(menu_permissions) > 10:
            print(f"  ... 还有 {len(menu_permissions) - 10} 个菜单权限")
        
        print(f"\n🔌 API权限 ({len(api_permissions)} 个):")
        for perm in api_permissions[:10]:  # 只显示前10个
            print(f"  {perm['code']} - {perm['name']}")
        if len(api_permissions) > 10:
            print(f"  ... 还有 {len(api_permissions) - 10} 个API权限")
        
        print(f"\n🔒 数据权限 ({len(data_permissions)} 个):")
        for perm in data_permissions:
            print(f"  {perm['code']} - {perm['name']}")
        
        print(f"\n🔧 其他权限 ({len(other_permissions)} 个):")
        for perm in other_permissions:
            print(f"  {perm['code']} - {perm['name']}")
        
        # 如果有多页，获取下一页
        pages = data.get("pages", 1)
        if pages > 1:
            print(f"\n⚠️ 还有 {pages - 1} 页权限数据，需要分页获取")
            
            # 获取第二页
            status_code2, response2 = api_test.make_request("GET", "/permissions?page=2&page_size=100", admin_token)
            if status_code2 == 200:
                data2 = response2.get("data", {})
                permissions2 = data2.get("items", [])
                print(f"第二页权限数: {len(permissions2)}")
                
                for perm in permissions2:
                    code = perm.get("code", "")
                    name = perm.get("name", "")
                    print(f"  {code} - {name}")
    
    else:
        print(f"❌ 获取权限列表失败: {response}")


if __name__ == "__main__":
    check_permissions()
