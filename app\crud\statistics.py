"""
统计分析CRUD操作 - 专门用于复杂的数据统计和分析
"""
from typing import List, Optional, Dict, Any, Tuple
from sqlalchemy.orm import Session
from sqlalchemy import desc, and_, func, text
from datetime import datetime, date, timedelta

from app.models.card_record import CardRecord
from app.models.walmart_ck import WalmartCK
from app.models.binding_log import BindingLog
from app.models.merchant import Merchant
from app.models.department import Department


class CRUDStatistics:
    """统计分析CRUD操作类"""

    def get_ck_comprehensive_statistics(
        self,
        db: Session,
        walmart_ck_id: int,
        start_date: Optional[date] = None,
        end_date: Optional[date] = None,
    ) -> Dict[str, Any]:
        """获取CK的综合统计信息"""
        # 基础查询
        query = db.query(CardRecord).filter(CardRecord.walmart_ck_id == walmart_ck_id)
        
        if start_date:
            query = query.filter(func.date(CardRecord.created_at) >= start_date)
        if end_date:
            query = query.filter(func.date(CardRecord.created_at) <= end_date)
        
        # 获取CK基本信息
        ck_info = db.query(WalmartCK).filter(WalmartCK.id == walmart_ck_id).first()
        
        # 统计各种状态
        total_count = query.count()
        success_count = query.filter(CardRecord.status == 'success').count()
        failed_count = query.filter(CardRecord.status == 'failed').count()
        pending_count = query.filter(CardRecord.status == 'pending').count()
        processing_count = query.filter(CardRecord.status == 'processing').count()
        
        # 计算成功率
        success_rate = (success_count / total_count * 100) if total_count > 0 else 0
        
        # 平均处理时间
        avg_process_time = query.filter(CardRecord.process_time.isnot(None)).with_entities(
            func.avg(CardRecord.process_time)
        ).scalar() or 0
        
        # 平均重试次数
        avg_retry_count = query.with_entities(func.avg(CardRecord.retry_count)).scalar() or 0
        
        # 今日统计 - 修复时区问题
        from app.utils.time_utils import get_current_time
        today = get_current_time().date()
        today_query = query.filter(func.date(CardRecord.created_at) == today)
        today_total = today_query.count()
        today_success = today_query.filter(CardRecord.status == 'success').count()
        
        return {
            "ck_info": {
                "id": ck_info.id if ck_info else None,
                "sign": ck_info.sign[:50] + "..." if ck_info and len(ck_info.sign) > 50 else (ck_info.sign if ck_info else None),
                "description": ck_info.description if ck_info else None,
                "daily_limit": ck_info.daily_limit if ck_info else None,
                "active": ck_info.active if ck_info else None,
            },
            "overall_stats": {
                "total_count": total_count,
                "success_count": success_count,
                "failed_count": failed_count,
                "pending_count": pending_count,
                "processing_count": processing_count,
                "success_rate": round(success_rate, 2),
                "avg_process_time": round(avg_process_time, 2),
                "avg_retry_count": round(avg_retry_count, 2),
            },
            "today_stats": {
                "total_count": today_total,
                "success_count": today_success,
                "remaining_quota": (ck_info.daily_limit - today_success) if ck_info else 0,
            },
            "period": {
                "start_date": start_date,
                "end_date": end_date,
            }
        }

    def get_merchant_statistics(
        self,
        db: Session,
        merchant_id: int,
        start_date: Optional[date] = None,
        end_date: Optional[date] = None,
    ) -> Dict[str, Any]:
        """获取商户统计信息"""
        # 获取商户信息
        merchant_info = self._get_merchant_info(db, merchant_id)

        # 构建基础查询
        base_query = self._build_merchant_base_query(db, merchant_id, start_date, end_date)

        # 获取各项统计数据
        overall_stats = self._get_merchant_overall_stats(base_query)
        department_stats = self._get_merchant_department_stats(db, merchant_id, start_date, end_date)
        ck_stats = self._get_merchant_ck_stats(db, merchant_id, start_date, end_date)

        return {
            "merchant_info": merchant_info,
            "overall_stats": overall_stats,
            "department_stats": department_stats,
            "ck_stats": ck_stats,
        }

    def _get_merchant_info(self, db: Session, merchant_id: int) -> Dict[str, Any]:
        """获取商户信息"""
        merchant_info = db.query(Merchant).filter(Merchant.id == merchant_id).first()
        return {
            "id": merchant_info.id if merchant_info else None,
            "name": merchant_info.name if merchant_info else None,
            "daily_limit": merchant_info.daily_limit if merchant_info else None,
        }

    def _build_merchant_base_query(
        self, db: Session, merchant_id: int, start_date: Optional[date], end_date: Optional[date]
    ):
        """构建商户基础查询"""
        query = db.query(CardRecord).filter(CardRecord.merchant_id == merchant_id)

        if start_date:
            query = query.filter(func.date(CardRecord.created_at) >= start_date)
        if end_date:
            query = query.filter(func.date(CardRecord.created_at) <= end_date)

        return query

    def _get_merchant_overall_stats(self, query) -> Dict[str, Any]:
        """获取商户总体统计"""
        total_count = query.count()
        success_count = query.filter(CardRecord.status == 'success').count()
        failed_count = query.filter(CardRecord.status == 'failed').count()

        return {
            "total_count": total_count,
            "success_count": success_count,
            "failed_count": failed_count,
            "success_rate": round((success_count / total_count * 100) if total_count > 0 else 0, 2),
        }

    def _get_merchant_department_stats(
        self, db: Session, merchant_id: int, start_date: Optional[date], end_date: Optional[date]
    ) -> List[Dict[str, Any]]:
        """获取商户部门统计"""
        department_stats = db.query(
            Department.id,
            Department.name,
            func.count(CardRecord.id).label('total'),
            func.sum(func.case((CardRecord.status == 'success', 1), else_=0)).label('success'),
        ).join(
            CardRecord, Department.id == CardRecord.department_id
        ).filter(
            Department.merchant_id == merchant_id
        )

        if start_date:
            department_stats = department_stats.filter(func.date(CardRecord.created_at) >= start_date)
        if end_date:
            department_stats = department_stats.filter(func.date(CardRecord.created_at) <= end_date)

        department_stats = department_stats.group_by(Department.id, Department.name).all()

        return [
            {
                "department_id": row.id,
                "department_name": row.name,
                "total_count": row.total,
                "success_count": row.success,
                "success_rate": round((row.success / row.total * 100) if row.total > 0 else 0, 2),
            }
            for row in department_stats
        ]

    def _get_merchant_ck_stats(
        self, db: Session, merchant_id: int, start_date: Optional[date], end_date: Optional[date]
    ) -> List[Dict[str, Any]]:
        """获取商户CK统计 - 使用LEFT JOIN确保历史数据完整性"""
        ck_stats = db.query(
            WalmartCK.id,
            WalmartCK.description,
            func.count(CardRecord.id).label('total'),
            func.sum(func.case((CardRecord.status == 'success', 1), else_=0)).label('success'),
        ).outerjoin(
            CardRecord, WalmartCK.id == CardRecord.walmart_ck_id
        ).filter(
            WalmartCK.merchant_id == merchant_id
        )

        if start_date:
            ck_stats = ck_stats.filter(func.date(CardRecord.created_at) >= start_date)
        if end_date:
            ck_stats = ck_stats.filter(func.date(CardRecord.created_at) <= end_date)

        ck_stats = ck_stats.group_by(WalmartCK.id, WalmartCK.description).all()

        return [
            {
                "ck_id": row.id,
                "ck_description": row.description,
                "total_count": row.total,
                "success_count": row.success,
                "success_rate": round((row.success / row.total * 100) if row.total > 0 else 0, 2),
            }
            for row in ck_stats
        ]

    def get_system_overview(
        self,
        db: Session,
        start_date: Optional[date] = None,
        end_date: Optional[date] = None,
    ) -> Dict[str, Any]:
        """获取系统总览统计"""
        # 构建基础查询
        base_query = self._build_system_overview_query(db, start_date, end_date)

        # 获取各项统计数据
        overall_stats = self._get_system_overall_stats(base_query)
        merchant_stats = self._get_system_merchant_stats(db, start_date, end_date)
        ck_stats = self._get_system_ck_stats(db, start_date, end_date)

        return {
            "overall_stats": overall_stats,
            "top_merchants": merchant_stats,
            "top_cks": ck_stats,
        }

    def _build_system_overview_query(self, db: Session, start_date: Optional[date], end_date: Optional[date]):
        """构建系统总览基础查询"""
        query = db.query(CardRecord)

        if start_date:
            query = query.filter(func.date(CardRecord.created_at) >= start_date)
        if end_date:
            query = query.filter(func.date(CardRecord.created_at) <= end_date)

        return query

    def _get_system_overall_stats(self, query) -> Dict[str, Any]:
        """获取系统总体统计 - 优化版本，使用单次聚合查询避免N+1查询"""
        # 使用单次聚合查询获取所有统计信息
        stats = query.with_entities(
            func.count(CardRecord.id).label('total_count'),
            func.sum(func.case((CardRecord.status == 'success', 1), else_=0)).label('success_count'),
            func.sum(func.case((CardRecord.status == 'failed', 1), else_=0)).label('failed_count'),
        ).first()

        total_count = stats.total_count or 0
        success_count = stats.success_count or 0
        failed_count = stats.failed_count or 0

        return {
            "total_count": total_count,
            "success_count": success_count,
            "failed_count": failed_count,
            "success_rate": round((success_count / total_count * 100) if total_count > 0 else 0, 2),
        }

    def _get_system_merchant_stats(self, db: Session, start_date: Optional[date], end_date: Optional[date]) -> List[Dict[str, Any]]:
        """获取系统商户统计"""
        merchant_stats = db.query(
            Merchant.id,
            Merchant.name,
            func.count(CardRecord.id).label('total'),
            func.sum(func.case((CardRecord.status == 'success', 1), else_=0)).label('success'),
        ).join(
            CardRecord, Merchant.id == CardRecord.merchant_id
        )

        if start_date:
            merchant_stats = merchant_stats.filter(func.date(CardRecord.created_at) >= start_date)
        if end_date:
            merchant_stats = merchant_stats.filter(func.date(CardRecord.created_at) <= end_date)

        merchant_stats = merchant_stats.group_by(Merchant.id, Merchant.name).order_by(
            desc(func.sum(func.case((CardRecord.status == 'success', 1), else_=0)))
        ).limit(10).all()

        return [
            {
                "merchant_id": row.id,
                "merchant_name": row.name,
                "total_count": row.total,
                "success_count": row.success,
                "success_rate": round((row.success / row.total * 100) if row.total > 0 else 0, 2),
            }
            for row in merchant_stats
        ]

    def _get_system_ck_stats(self, db: Session, start_date: Optional[date], end_date: Optional[date]) -> List[Dict[str, Any]]:
        """获取系统CK统计 - 使用LEFT JOIN确保历史数据完整性"""
        ck_ranking = db.query(
            WalmartCK.id,
            WalmartCK.description,
            func.count(CardRecord.id).label('total'),
            func.sum(func.case((CardRecord.status == 'success', 1), else_=0)).label('success'),
        ).outerjoin(
            CardRecord, WalmartCK.id == CardRecord.walmart_ck_id
        )

        if start_date:
            ck_ranking = ck_ranking.filter(func.date(CardRecord.created_at) >= start_date)
        if end_date:
            ck_ranking = ck_ranking.filter(func.date(CardRecord.created_at) <= end_date)

        ck_ranking = ck_ranking.group_by(WalmartCK.id, WalmartCK.description).order_by(
            desc(func.sum(func.case((CardRecord.status == 'success', 1), else_=0)))
        ).limit(10).all()

        return [
            {
                "ck_id": row.id,
                "ck_description": row.description,
                "total_count": row.total,
                "success_count": row.success,
                "success_rate": round((row.success / row.total * 100) if row.total > 0 else 0, 2),
            }
            for row in ck_ranking
        ]

    def get_daily_trend(
        self,
        db: Session,
        days: int = 30,
        merchant_id: Optional[int] = None,
        walmart_ck_id: Optional[int] = None,
    ) -> List[Dict[str, Any]]:
        """获取每日趋势数据"""
        # 修复时区问题：使用上海时区的当前日期
        from app.utils.time_utils import get_current_time
        end_date = get_current_time().date()
        start_date = end_date - timedelta(days=days-1)
        
        query = db.query(
            func.date(CardRecord.created_at).label('date'),
            func.count(CardRecord.id).label('total_count'),
            func.sum(func.case((CardRecord.status == 'success', 1), else_=0)).label('success_count'),
            func.sum(func.case((CardRecord.status == 'failed', 1), else_=0)).label('failed_count'),
        ).filter(
            func.date(CardRecord.created_at) >= start_date,
            func.date(CardRecord.created_at) <= end_date,
        )
        
        if merchant_id:
            query = query.filter(CardRecord.merchant_id == merchant_id)
        if walmart_ck_id:
            query = query.filter(CardRecord.walmart_ck_id == walmart_ck_id)
        
        query = query.group_by(func.date(CardRecord.created_at)).order_by(func.date(CardRecord.created_at))
        
        results = []
        for row in query.all():
            success_rate = (row.success_count / row.total_count * 100) if row.total_count > 0 else 0
            results.append({
                "date": row.date.isoformat(),
                "total_count": row.total_count,
                "success_count": row.success_count,
                "failed_count": row.failed_count,
                "success_rate": round(success_rate, 2),
            })
        
        return results


# 创建全局实例
statistics = CRUDStatistics()
