package queue

import (
	"context"
	"encoding/json"
	"fmt"
	"strings"
	"sync"
	"time"

	"github.com/rabbitmq/amqp091-go"

	"walmart-bind-card-gateway/internal/config"
	"walmart-bind-card-gateway/internal/model"
)

// Publisher 队列发布者接口
type Publisher interface {
	// 基础操作
	Close() error
	
	// 单个发布
	PublishBindTask(ctx context.Context, message *model.QueueMessage) error
	
	// 批量发布
	BatchPublishBindTasks(ctx context.Context, messages []*model.QueueMessage) error
	BatchPublishFromRequests(ctx context.Context, requests []*model.InternalBindRequest) error
	BatchPublishFromRequestsWithRecords(ctx context.Context, requests []*model.InternalBindRequest, records []*model.CardRecord) error
	
	// 统计信息
	GetStats() map[string]interface{}
}

// rabbitMQPublisher RabbitMQ发布者实现
type rabbitMQPublisher struct {
	config      *config.RabbitMQConfig
	connections []*amqp091.Connection
	channels    chan *amqp091.Channel
	mutex       sync.RWMutex
	closed      bool
	stats       *publisherStats
}

// publisherStats 发布者统计信息
type publisherStats struct {
	PublishedCount    int64 `json:"published_count"`
	BatchCount        int64 `json:"batch_count"`
	ErrorCount        int64 `json:"error_count"`
	ConnectionCount   int   `json:"connection_count"`
	AvailableChannels int   `json:"available_channels"`
	mutex             sync.RWMutex
}

// NewRabbitMQPublisher 创建RabbitMQ发布者
func NewRabbitMQPublisher(cfg *config.RabbitMQConfig) (Publisher, error) {
	publisher := &rabbitMQPublisher{
		config:   cfg,
		channels: make(chan *amqp091.Channel, cfg.ChannelPoolSize),
		stats:    &publisherStats{},
	}
	
	// 创建连接池
	if err := publisher.initConnections(); err != nil {
		return nil, fmt.Errorf("初始化连接池失败: %w", err)
	}
	
	// 创建通道池
	if err := publisher.initChannels(); err != nil {
		return nil, fmt.Errorf("初始化通道池失败: %w", err)
	}
	
	return publisher, nil
}

// initConnections 初始化连接池
func (p *rabbitMQPublisher) initConnections() error {
	p.connections = make([]*amqp091.Connection, 0, p.config.ConnectionPoolSize)

	// 获取连接URL
	url := p.config.GetRabbitMQURL()

	for i := 0; i < p.config.ConnectionPoolSize; i++ {
		conn, err := amqp091.Dial(url)
		if err != nil {
			// 关闭已创建的连接
			for _, existingConn := range p.connections {
				existingConn.Close()
			}
			return fmt.Errorf("创建连接失败: %w", err)
		}
		
		p.connections = append(p.connections, conn)
	}
	
	p.stats.ConnectionCount = len(p.connections)
	return nil
}

// initChannels 初始化通道池
func (p *rabbitMQPublisher) initChannels() error {
	channelsPerConn := p.config.ChannelPoolSize / p.config.ConnectionPoolSize
	if channelsPerConn == 0 {
		channelsPerConn = 1
	}
	
	for _, conn := range p.connections {
		for i := 0; i < channelsPerConn; i++ {
			channel, err := conn.Channel()
			if err != nil {
				return fmt.Errorf("创建通道失败: %w", err)
			}
			
			// 声明队列
			if err := p.declareQueues(channel); err != nil {
				return fmt.Errorf("声明队列失败: %w", err)
			}
			
			// 设置确认模式（如果启用）
			if p.config.ConfirmMode {
				if err := channel.Confirm(false); err != nil {
					return fmt.Errorf("设置确认模式失败: %w", err)
				}
			}
			
			p.channels <- channel
		}
	}
	
	p.stats.AvailableChannels = len(p.channels)
	return nil
}

// declareQueues 声明队列
func (p *rabbitMQPublisher) declareQueues(channel *amqp091.Channel) error {
	// 声明绑卡队列
	_, err := channel.QueueDeclare(
		p.config.Queues["bind_card"], // 队列名
		true,  // 持久化
		false, // 不自动删除
		false, // 不排他
		false, // 不等待
		nil,   // 参数
	)
	if err != nil {
		return fmt.Errorf("声明绑卡队列失败: %w", err)
	}
	
	// 声明回调队列
	if callbackQueue, exists := p.config.Queues["callback"]; exists {
		_, err := channel.QueueDeclare(
			callbackQueue, // 队列名
			true,  // 持久化
			false, // 不自动删除
			false, // 不排他
			false, // 不等待
			nil,   // 参数
		)
		if err != nil {
			return fmt.Errorf("声明回调队列失败: %w", err)
		}
	}
	
	return nil
}

// getChannel 获取通道
func (p *rabbitMQPublisher) getChannel() (*amqp091.Channel, error) {
	p.mutex.RLock()
	if p.closed {
		p.mutex.RUnlock()
		return nil, fmt.Errorf("发布者已关闭")
	}
	p.mutex.RUnlock()

	// 尝试获取通道，如果失败则重新初始化
	for retries := 0; retries < 3; retries++ {
		select {
		case channel := <-p.channels:
			// 检查通道是否有效
			if channel != nil && !channel.IsClosed() {
				return channel, nil
			}
			// 通道无效，关闭它并继续尝试
			if channel != nil {
				channel.Close()
			}
		case <-time.After(1 * time.Second):
			// 超时，尝试重新初始化连接池
			if err := p.reinitializeConnections(); err != nil {
				if retries == 2 { // 最后一次重试
					return nil, fmt.Errorf("重新初始化连接失败: %w", err)
				}
				continue
			}
		}
	}

	return nil, fmt.Errorf("获取有效通道失败")
}

// reinitializeConnections 重新初始化连接和通道池
func (p *rabbitMQPublisher) reinitializeConnections() error {
	p.mutex.Lock()
	defer p.mutex.Unlock()

	if p.closed {
		return fmt.Errorf("发布者已关闭")
	}

	// 关闭现有连接
	for _, conn := range p.connections {
		if conn != nil && !conn.IsClosed() {
			conn.Close()
		}
	}

	// 清空通道池
	for {
		select {
		case ch := <-p.channels:
			if ch != nil {
				ch.Close()
			}
		default:
			goto channelPoolCleared
		}
	}
channelPoolCleared:

	// 重新创建连接池
	if err := p.initConnections(); err != nil {
		return fmt.Errorf("重新初始化连接池失败: %w", err)
	}

	// 重新创建通道池
	if err := p.initChannels(); err != nil {
		return fmt.Errorf("重新初始化通道池失败: %w", err)
	}

	return nil
}

// returnChannel 归还通道
func (p *rabbitMQPublisher) returnChannel(channel *amqp091.Channel) {
	if channel == nil {
		return
	}

	// 检查通道是否有效
	if channel.IsClosed() {
		channel.Close()
		return
	}

	p.mutex.RLock()
	if p.closed {
		p.mutex.RUnlock()
		channel.Close()
		return
	}
	p.mutex.RUnlock()

	select {
	case p.channels <- channel:
		// 成功归还
	default:
		// 通道池满，关闭通道
		channel.Close()
	}
}

// Close 关闭发布者
func (p *rabbitMQPublisher) Close() error {
	p.mutex.Lock()
	defer p.mutex.Unlock()
	
	if p.closed {
		return nil
	}
	
	p.closed = true
	
	// 关闭所有通道
	close(p.channels)
	for channel := range p.channels {
		channel.Close()
	}
	
	// 关闭所有连接
	for _, conn := range p.connections {
		conn.Close()
	}
	
	return nil
}

// PublishBindTask 发布绑卡任务
func (p *rabbitMQPublisher) PublishBindTask(ctx context.Context, message *model.QueueMessage) error {
	channel, err := p.getChannel()
	if err != nil {
		p.incrementErrorCount()
		return err
	}
	defer p.returnChannel(channel)
	
	// 序列化消息
	body, err := json.Marshal(message)
	if err != nil {
		p.incrementErrorCount()
		return fmt.Errorf("序列化消息失败: %w", err)
	}
	
	// 发布消息
	err = channel.PublishWithContext(
		ctx,
		"",                               // 交换机
		p.config.Queues["bind_card"],     // 路由键
		false,                            // mandatory
		false,                            // immediate
		amqp091.Publishing{
			ContentType:  "application/json",
			Body:         body,
			DeliveryMode: amqp091.Persistent, // 持久化
			Timestamp:    time.Now(),
		},
	)
	
	if err != nil {
		p.incrementErrorCount()
		return fmt.Errorf("发布消息失败: %w", err)
	}
	
	p.incrementPublishedCount()
	return nil
}

// BatchPublishBindTasks 批量发布绑卡任务
func (p *rabbitMQPublisher) BatchPublishBindTasks(ctx context.Context, messages []*model.QueueMessage) error {
	if len(messages) == 0 {
		return nil
	}

	// 添加重试机制
	maxRetries := 3
	for retry := 0; retry < maxRetries; retry++ {
		channel, err := p.getChannel()
		if err != nil {
			p.incrementErrorCount()
			if retry == maxRetries-1 {
				return fmt.Errorf("获取通道失败，已重试%d次: %w", maxRetries, err)
			}
			time.Sleep(time.Duration(retry+1) * 100 * time.Millisecond)
			continue
		}

		// 批量发布
		publishErr := p.batchPublishWithChannel(ctx, channel, messages)
		p.returnChannel(channel)

		if publishErr == nil {
			p.incrementPublishedCount(int64(len(messages)))
			p.incrementBatchCount()
			return nil
		}

		// 如果是连接相关错误，重试
		if isConnectionError(publishErr) && retry < maxRetries-1 {
			time.Sleep(time.Duration(retry+1) * 200 * time.Millisecond)
			continue
		}

		p.incrementErrorCount()
		return fmt.Errorf("批量发布消息失败: %w", publishErr)
	}

	return fmt.Errorf("批量发布消息失败，已重试%d次", maxRetries)
}

// batchPublishWithChannel 使用指定通道批量发布消息
func (p *rabbitMQPublisher) batchPublishWithChannel(ctx context.Context, channel *amqp091.Channel, messages []*model.QueueMessage) error {
	for _, message := range messages {
		// 序列化消息
		body, err := json.Marshal(message)
		if err != nil {
			continue // 跳过错误消息
		}

		// 发布消息
		err = channel.PublishWithContext(
			ctx,
			"",                               // 交换机
			p.config.Queues["bind_card"],     // 路由键
			false,                            // mandatory
			false,                            // immediate
			amqp091.Publishing{
				ContentType:  "application/json",
				Body:         body,
				DeliveryMode: amqp091.Persistent, // 持久化
				Timestamp:    time.Now(),
			},
		)

		if err != nil {
			return err
		}
	}
	return nil
}

// isConnectionError 判断是否为连接相关错误
func isConnectionError(err error) bool {
	if err == nil {
		return false
	}
	errStr := err.Error()
	return strings.Contains(errStr, "connection") ||
		strings.Contains(errStr, "channel") ||
		strings.Contains(errStr, "not open") ||
		strings.Contains(errStr, "closed") ||
		strings.Contains(errStr, "EOF")
}

// BatchPublishFromRequests 从请求批量发布
func (p *rabbitMQPublisher) BatchPublishFromRequests(ctx context.Context, requests []*model.InternalBindRequest) error {
	if len(requests) == 0 {
		return nil
	}
	
	// 转换为队列消息（使用临时记录ID）
	messages := make([]*model.QueueMessage, len(requests))
	for i, req := range requests {
		message := &model.QueueMessage{}
		message.FromInternalRequest(req, "temp_record_id") // 使用临时ID
		messages[i] = message
	}
	
	return p.BatchPublishBindTasks(ctx, messages)
}

// BatchPublishFromRequestsWithRecords 从请求和记录批量发布（使用真实记录ID）
func (p *rabbitMQPublisher) BatchPublishFromRequestsWithRecords(ctx context.Context, requests []*model.InternalBindRequest, records []*model.CardRecord) error {
	if len(requests) == 0 || len(records) == 0 {
		return nil
	}

	if len(requests) != len(records) {
		return fmt.Errorf("请求数量与记录数量不匹配: %d != %d", len(requests), len(records))
	}

	// 转换为队列消息（使用真实记录ID）
	messages := make([]*model.QueueMessage, len(requests))
	for i, req := range requests {
		message := &model.QueueMessage{}
		message.FromInternalRequest(req, records[i].ID) // 使用真实记录ID
		messages[i] = message
	}

	return p.BatchPublishBindTasks(ctx, messages)
}

// GetStats 获取统计信息
func (p *rabbitMQPublisher) GetStats() map[string]interface{} {
	p.stats.mutex.RLock()
	defer p.stats.mutex.RUnlock()
	
	return map[string]interface{}{
		"published_count":    p.stats.PublishedCount,
		"batch_count":        p.stats.BatchCount,
		"error_count":        p.stats.ErrorCount,
		"connection_count":   p.stats.ConnectionCount,
		"available_channels": len(p.channels),
		"total_channels":     cap(p.channels),
	}
}

// incrementPublishedCount 增加发布计数
func (p *rabbitMQPublisher) incrementPublishedCount(count ...int64) {
	p.stats.mutex.Lock()
	defer p.stats.mutex.Unlock()
	
	if len(count) > 0 {
		p.stats.PublishedCount += count[0]
	} else {
		p.stats.PublishedCount++
	}
}

// incrementBatchCount 增加批量计数
func (p *rabbitMQPublisher) incrementBatchCount() {
	p.stats.mutex.Lock()
	defer p.stats.mutex.Unlock()
	
	p.stats.BatchCount++
}

// incrementErrorCount 增加错误计数
func (p *rabbitMQPublisher) incrementErrorCount() {
	p.stats.mutex.Lock()
	defer p.stats.mutex.Unlock()
	
	p.stats.ErrorCount++
}

// BatchPublishWithRetry 带重试的批量发布
func (p *rabbitMQPublisher) BatchPublishWithRetry(ctx context.Context, messages []*model.QueueMessage, maxRetries int) error {
	var lastErr error
	
	for i := 0; i < maxRetries; i++ {
		err := p.BatchPublishBindTasks(ctx, messages)
		if err == nil {
			return nil
		}
		
		lastErr = err
		
		// 等待一段时间后重试
		select {
		case <-ctx.Done():
			return ctx.Err()
		case <-time.After(time.Duration(i+1) * 100 * time.Millisecond):
			// 继续重试
		}
	}
	
	return lastErr
}

// HealthCheck 健康检查
func (p *rabbitMQPublisher) HealthCheck() error {
	p.mutex.RLock()
	if p.closed {
		p.mutex.RUnlock()
		return fmt.Errorf("发布者已关闭")
	}
	p.mutex.RUnlock()
	
	// 检查连接状态
	for i, conn := range p.connections {
		if conn.IsClosed() {
			return fmt.Errorf("连接 %d 已关闭", i)
		}
	}
	
	// 检查通道可用性
	if len(p.channels) == 0 {
		return fmt.Errorf("没有可用的通道")
	}
	
	return nil
}
