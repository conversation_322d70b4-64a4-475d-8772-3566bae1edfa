"""
测试Telegram Bot配置管理
"""

import pytest
import os
from unittest.mock import Mock, patch
from sqlalchemy.orm import Session

from app.telegram_bot.config import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Config<PERSON><PERSON><PERSON>, get_config_manager, get_bot_config
from app.models.telegram_bot_config import TelegramBotConfig, ConfigType


class TestBotConfig:
    """测试BotConfig类"""

    def test_default_config(self):
        """测试默认配置"""
        config = BotConfig()
        
        assert config.bot_token == ""
        assert config.webhook_url == ""
        assert config.webhook_secret == ""
        assert config.rate_limit_global == 1000
        assert config.rate_limit_group == 100
        assert config.rate_limit_user == 50
        assert config.bind_token_expire_hours == 24
        assert config.verification_token_expire_minutes == 30
        assert config.max_bind_attempts_per_day == 5
        assert config.enable_audit_log is True
        assert config.mask_sensitive_data is True
        assert config.default_timezone == "Asia/Shanghai"
        assert config.default_language == "zh-CN"

    def test_from_env(self):
        """测试从环境变量加载配置"""
        env_vars = {
            "TELEGRAM_BOT_TOKEN": "test_token_123",
            "WEBHOOK_URL": "https://example.com/webhook",
            "WEBHOOK_SECRET": "test_secret",
            "RATE_LIMIT_GLOBAL": "2000",
            "RATE_LIMIT_GROUP": "200",
            "RATE_LIMIT_USER": "100",
            "BIND_TOKEN_EXPIRE_HOURS": "48",
            "VERIFICATION_TOKEN_EXPIRE_MINUTES": "60",
            "MAX_BIND_ATTEMPTS_PER_DAY": "10",
            "ENABLE_AUDIT_LOG": "false",
            "MASK_SENSITIVE_DATA": "false",
            "DEFAULT_TIMEZONE": "UTC",
            "DEFAULT_LANGUAGE": "en-US"
        }
        
        with patch.dict(os.environ, env_vars):
            config = BotConfig.from_env()
            
            assert config.bot_token == "test_token_123"
            assert config.webhook_url == "https://example.com/webhook"
            assert config.webhook_secret == "test_secret"
            assert config.rate_limit_global == 2000
            assert config.rate_limit_group == 200
            assert config.rate_limit_user == 100
            assert config.bind_token_expire_hours == 48
            assert config.verification_token_expire_minutes == 60
            assert config.max_bind_attempts_per_day == 10
            assert config.enable_audit_log is False
            assert config.mask_sensitive_data is False
            assert config.default_timezone == "UTC"
            assert config.default_language == "en-US"

    def test_from_env_partial(self):
        """测试从环境变量部分加载配置"""
        env_vars = {
            "TELEGRAM_BOT_TOKEN": "test_token_123",
            "WEBHOOK_URL": "https://example.com/webhook"
        }
        
        with patch.dict(os.environ, env_vars, clear=True):
            config = BotConfig.from_env()
            
            assert config.bot_token == "test_token_123"
            assert config.webhook_url == "https://example.com/webhook"
            assert config.webhook_secret == ""  # 默认值
            assert config.rate_limit_global == 1000  # 默认值

    def test_from_database_success(self, db: Session):
        """测试从数据库成功加载配置"""
        # 创建测试配置数据
        test_configs = [
            TelegramBotConfig(
                config_key="bot_token",
                config_value="db_token_123",
                config_type=ConfigType.STRING
            ),
            TelegramBotConfig(
                config_key="webhook_url",
                config_value="https://db.example.com/webhook",
                config_type=ConfigType.STRING
            ),
            TelegramBotConfig(
                config_key="rate_limit_global",
                config_value="3000",
                config_type=ConfigType.NUMBER
            ),
            TelegramBotConfig(
                config_key="enable_audit_log",
                config_value="false",
                config_type=ConfigType.BOOLEAN
            )
        ]
        
        for config in test_configs:
            db.add(config)
        db.commit()
        
        # 测试从数据库加载
        config = BotConfig.from_database(db)
        
        assert config.bot_token == "db_token_123"
        assert config.webhook_url == "https://db.example.com/webhook"
        assert config.rate_limit_global == 3000
        assert config.enable_audit_log is False

    def test_from_database_fallback_to_env(self, db: Session):
        """测试数据库加载失败时回退到环境变量"""
        env_vars = {
            "TELEGRAM_BOT_TOKEN": "env_token_123",
            "WEBHOOK_URL": "https://env.example.com/webhook"
        }
        
        with patch.dict(os.environ, env_vars):
            with patch('telegram_bot.config.TelegramBotConfig.get_config') as mock_get_config:
                # 模拟数据库异常
                mock_get_config.side_effect = Exception("数据库连接失败")
                
                config = BotConfig.from_database(db)
                
                # 应该回退到环境变量
                assert config.bot_token == "env_token_123"
                assert config.webhook_url == "https://env.example.com/webhook"

    def test_validate_success(self):
        """测试配置验证成功"""
        config = BotConfig(
            bot_token="valid_token",
            webhook_url="https://example.com/webhook",
            rate_limit_global=1000
        )
        
        assert config.validate() is True

    def test_validate_missing_bot_token(self):
        """测试缺少bot_token的验证失败"""
        config = BotConfig(
            bot_token="",
            webhook_url="https://example.com/webhook"
        )
        
        assert config.validate() is False

    def test_validate_missing_webhook_url(self):
        """测试缺少webhook_url的验证失败"""
        config = BotConfig(
            bot_token="valid_token",
            webhook_url=""
        )
        
        assert config.validate() is False

    def test_validate_invalid_rate_limit(self):
        """测试无效频率限制的验证失败"""
        config = BotConfig(
            bot_token="valid_token",
            webhook_url="https://example.com/webhook",
            rate_limit_global=0
        )
        
        assert config.validate() is False

    def test_to_dict(self):
        """测试转换为字典"""
        config = BotConfig(
            bot_token="secret_token",
            webhook_url="https://example.com/webhook",
            webhook_secret="secret_key",
            rate_limit_global=1000
        )
        
        config_dict = config.to_dict()
        
        assert config_dict["bot_token"] == "***"  # 敏感信息被隐藏
        assert config_dict["webhook_url"] == "https://example.com/webhook"
        assert config_dict["webhook_secret"] == "***"  # 敏感信息被隐藏
        assert config_dict["rate_limit_global"] == 1000

    def test_to_dict_empty_secrets(self):
        """测试空密钥的字典转换"""
        config = BotConfig(
            bot_token="",
            webhook_secret=""
        )
        
        config_dict = config.to_dict()
        
        assert config_dict["bot_token"] == ""
        assert config_dict["webhook_secret"] == ""


class TestConfigManager:
    """测试ConfigManager类"""

    @pytest.fixture
    def config_manager(self, db: Session):
        """创建配置管理器"""
        return ConfigManager(db)

    def test_get_config_first_time(self, config_manager: ConfigManager):
        """测试首次获取配置"""
        with patch.object(BotConfig, 'from_database') as mock_from_db:
            mock_config = Mock(spec=BotConfig)
            mock_from_db.return_value = mock_config
            
            config = config_manager.get_config()
            
            assert config == mock_config
            assert config_manager._config == mock_config
            mock_from_db.assert_called_once()

    def test_get_config_cached(self, config_manager: ConfigManager):
        """测试获取缓存的配置"""
        mock_config = Mock(spec=BotConfig)
        config_manager._config = mock_config
        
        with patch.object(BotConfig, 'from_database') as mock_from_db:
            config = config_manager.get_config()
            
            assert config == mock_config
            mock_from_db.assert_not_called()  # 不应该重新加载

    def test_reload_config(self, config_manager: ConfigManager):
        """测试重新加载配置"""
        old_config = Mock(spec=BotConfig)
        new_config = Mock(spec=BotConfig)
        config_manager._config = old_config
        
        with patch.object(BotConfig, 'from_database') as mock_from_db:
            mock_from_db.return_value = new_config
            
            config = config_manager.reload_config()
            
            assert config == new_config
            assert config_manager._config == new_config
            mock_from_db.assert_called_once()

    def test_update_config_success(self, config_manager: ConfigManager, db: Session):
        """测试成功更新配置"""
        with patch.object(TelegramBotConfig, 'set_config') as mock_set_config:
            with patch.object(config_manager, 'reload_config') as mock_reload:
                result = config_manager.update_config("test_key", "test_value")
                
                assert result is True
                mock_set_config.assert_called_once_with(
                    db, "test_key", "test_value", "string"
                )
                mock_reload.assert_called_once()

    def test_update_config_boolean(self, config_manager: ConfigManager, db: Session):
        """测试更新布尔类型配置"""
        with patch.object(TelegramBotConfig, 'set_config') as mock_set_config:
            with patch.object(config_manager, 'reload_config'):
                config_manager.update_config("enable_feature", True)
                
                mock_set_config.assert_called_once_with(
                    db, "enable_feature", True, "boolean"
                )

    def test_update_config_number(self, config_manager: ConfigManager, db: Session):
        """测试更新数字类型配置"""
        with patch.object(TelegramBotConfig, 'set_config') as mock_set_config:
            with patch.object(config_manager, 'reload_config'):
                config_manager.update_config("rate_limit", 500)
                
                mock_set_config.assert_called_once_with(
                    db, "rate_limit", 500, "number"
                )

    def test_update_config_json(self, config_manager: ConfigManager, db: Session):
        """测试更新JSON类型配置"""
        with patch.object(TelegramBotConfig, 'set_config') as mock_set_config:
            with patch.object(config_manager, 'reload_config'):
                test_dict = {"key": "value"}
                config_manager.update_config("settings", test_dict)
                
                mock_set_config.assert_called_once_with(
                    db, "settings", test_dict, "json"
                )

    def test_update_config_exception(self, config_manager: ConfigManager):
        """测试更新配置异常"""
        with patch.object(TelegramBotConfig, 'set_config') as mock_set_config:
            mock_set_config.side_effect = Exception("数据库错误")
            
            result = config_manager.update_config("test_key", "test_value")
            
            assert result is False


class TestGlobalFunctions:
    """测试全局函数"""

    def test_get_config_manager(self, db: Session):
        """测试获取配置管理器"""
        # 清除全局实例
        import app.telegram_bot.config
        app.telegram_bot.config._config_manager = None
        
        manager1 = get_config_manager(db)
        manager2 = get_config_manager(db)
        
        assert manager1 is manager2  # 应该是同一个实例
        assert isinstance(manager1, ConfigManager)

    def test_get_bot_config(self, db: Session):
        """测试获取机器人配置"""
        with patch('telegram_bot.config.get_config_manager') as mock_get_manager:
            mock_manager = Mock(spec=ConfigManager)
            mock_config = Mock(spec=BotConfig)
            mock_manager.get_config.return_value = mock_config
            mock_get_manager.return_value = mock_manager
            
            config = get_bot_config(db)
            
            assert config == mock_config
            mock_get_manager.assert_called_once_with(db)
            mock_manager.get_config.assert_called_once()
