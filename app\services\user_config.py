from typing import Optional, List, Dict, Any
from datetime import datetime
from sqlalchemy.orm import Session
from sqlalchemy import or_
from app.models.walmart_ck import UserConfig
from app.schemas.walmart_ck import UserConfigBase, UserConfigUpdate


class UserConfigService:
    """用户配置服务"""

    def __init__(self, db: Session):
        self.db = db

    def get_user_configs(
        self, skip: int = 0, limit: int = 10, search: Optional[str] = None
    ) -> List[UserConfig]:
        """
        获取用户配置列表

        Args:
            skip: 跳过记录数
            limit: 返回记录数
            search: 搜索关键词

        Returns:
            List[UserConfig]: 用户配置列表
        """
        query = self.db.query(UserConfig)

        if search:
            query = query.filter(
                or_(
                    UserConfig.username.ilike(f"%{search}%"),
                    UserConfig.sign.ilike(f"%{search}%"),
                    UserConfig.description.ilike(f"%{search}%"),
                )
            )

        return (
            query.order_by(UserConfig.created_at.desc()).offset(skip).limit(limit).all()
        )

    def count_user_configs(self, search: Optional[str] = None) -> int:
        """
        获取用户配置总数

        Args:
            search: 搜索关键词

        Returns:
            int: 用户配置总数
        """
        query = self.db.query(UserConfig)

        if search:
            query = query.filter(
                or_(
                    UserConfig.username.ilike(f"%{search}%"),
                    UserConfig.sign.ilike(f"%{search}%"),
                    UserConfig.description.ilike(f"%{search}%"),
                )
            )

        return query.count()

    def get_user_config(self, user_id: str) -> Optional[UserConfig]:
        """
        获取用户配置

        Args:
            user_id: 用户ID

        Returns:
            Optional[UserConfig]: 用户配置信息
        """
        return self.db.query(UserConfig).filter(UserConfig.id == user_id).first()

    def get_user_config_by_sign(self, sign: str) -> Optional[UserConfig]:
        """
        根据签名获取用户配置

        Args:
            sign: 用户签名

        Returns:
            Optional[UserConfig]: 用户配置信息
        """
        return self.db.query(UserConfig).filter(UserConfig.sign == sign).first()

    def create_user_config(self, user_config_in: UserConfigBase) -> UserConfig:
        """
        创建用户配置

        Args:
            user_config_in: 用户配置信息

        Returns:
            UserConfig: 创建的用户配置
        """
        # 转换字段名称（驼峰命名转为下划线命名）
        user_config_data = user_config_in.dict()
        db_data = {
            "sign": user_config_data["sign"],
            "daily_limit": user_config_data["dailyLimit"],
            "hourly_limit": user_config_data["hourlyLimit"],
            "active": user_config_data["active"],
            "description": user_config_data["description"],
            "created_at": datetime.now(),
            "updated_at": datetime.now(),
        }

        db_user_config = UserConfig(**db_data)
        self.db.add(db_user_config)
        self.db.commit()
        self.db.refresh(db_user_config)
        return db_user_config

    def update_user_config(
        self, user_id: str, user_config_in: UserConfigUpdate
    ) -> Optional[UserConfig]:
        """
        更新用户配置

        Args:
            user_id: 用户ID
            user_config_in: 更新的配置信息

        Returns:
            Optional[UserConfig]: 更新后的用户配置
        """
        db_user_config = self.get_user_config(user_id)
        if not db_user_config:
            return None

        # 获取更新数据
        update_data = user_config_in.dict(exclude_unset=True)
        if "dailyLimit" in update_data:
            update_data["daily_limit"] = update_data.pop("dailyLimit")
        if "hourlyLimit" in update_data:
            update_data["hourly_limit"] = update_data.pop("hourlyLimit")

        # 更新时间
        update_data["updated_at"] = datetime.now()

        # 应用更新
        for key, value in update_data.items():
            setattr(db_user_config, key, value)

        self.db.commit()
        self.db.refresh(db_user_config)
        return db_user_config

    def delete_user_config(self, user_id: str) -> bool:
        """
        删除用户配置

        Args:
            user_id: 用户ID

        Returns:
            bool: 删除是否成功
        """
        db_user_config = self.get_user_config(user_id)
        if not db_user_config:
            return False

        self.db.delete(db_user_config)
        self.db.commit()
        return True

    def format_user_config_response(self, user_config: UserConfig) -> Dict[str, Any]:
        """
        格式化用户配置响应数据

        Args:
            user_config: 用户配置对象

        Returns:
            Dict[str, Any]: 格式化后的用户配置数据
        """
        return {
            "id": str(user_config.id),
            "sign": user_config.sign,
            "username": (
                user_config.sign.split("@")[0]
                if "@" in user_config.sign
                else user_config.sign
            ),
            "dailyLimit": user_config.daily_limit,
            "hourlyLimit": user_config.hourly_limit,
            "active": user_config.active,
            "description": user_config.description,
            "createdAt": user_config.created_at.isoformat(),
        }
