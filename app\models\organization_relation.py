from sqlalchemy import <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>ger, Foreign<PERSON>ey
from sqlalchemy.orm import relationship

from app.models.base import BaseModel, TimestampMixin


class OrganizationRelation(BaseModel, TimestampMixin):
    """组织架构关系表 - 用于存储部门间的层级关系"""

    __tablename__ = "organization_relations"

    id = Column(BigInteger, primary_key=True, index=True, autoincrement=True)
    ancestor_id = Column(BigInteger, ForeignKey("departments.id"), nullable=False, comment="祖先部门ID")
    descendant_id = Column(BigInteger, ForeignKey("departments.id"), nullable=False, comment="后代部门ID")
    level = Column(Integer, default=0, nullable=False, comment="层级差距：0表示自己，1表示直接子部门")

    # 关联关系
    ancestor = relationship(
        "Department",
        foreign_keys=[ancestor_id],
        back_populates="descendant_relations"
    )
    
    descendant = relationship(
        "Department", 
        foreign_keys=[descendant_id],
        back_populates="ancestor_relations"
    )

    def __repr__(self):
        return f"<OrganizationRelation(ancestor_id={self.ancestor_id}, descendant_id={self.descendant_id}, level={self.level})>"

    def to_dict(self) -> dict:
        """转换为字典格式"""
        return {
            "id": self.id,
            "ancestor_id": self.ancestor_id,
            "descendant_id": self.descendant_id,
            "level": self.level,
            "created_at": self.created_at.isoformat() if self.created_at else None,
        }

    @classmethod
    def create_relations_for_department(cls, db_session, department):
        """为新部门创建组织关系"""
        # 创建自己与自己的关系（level=0）
        self_relation = cls(
            ancestor_id=department.id,
            descendant_id=department.id,
            level=0
        )
        db_session.add(self_relation)
        
        # 如果有父部门，创建与所有祖先的关系
        if department.parent_id:
            # 获取父部门的所有祖先关系
            parent_relations = db_session.query(cls).filter(
                cls.descendant_id == department.parent_id
            ).all()
            
            for parent_relation in parent_relations:
                new_relation = cls(
                    ancestor_id=parent_relation.ancestor_id,
                    descendant_id=department.id,
                    level=parent_relation.level + 1
                )
                db_session.add(new_relation)

    @classmethod
    def delete_relations_for_department(cls, db_session, department_id):
        """删除部门的所有组织关系"""
        # 删除作为祖先的关系
        db_session.query(cls).filter(cls.ancestor_id == department_id).delete()
        # 删除作为后代的关系
        db_session.query(cls).filter(cls.descendant_id == department_id).delete()

    @classmethod
    def get_descendants(cls, db_session, ancestor_id, max_level=None):
        """获取指定部门的所有后代"""
        query = db_session.query(cls).filter(
            cls.ancestor_id == ancestor_id,
            cls.level > 0  # 排除自己
        )
        
        if max_level is not None:
            query = query.filter(cls.level <= max_level)
            
        return query.all()

    @classmethod
    def get_ancestors(cls, db_session, descendant_id, max_level=None):
        """获取指定部门的所有祖先"""
        query = db_session.query(cls).filter(
            cls.descendant_id == descendant_id,
            cls.level > 0  # 排除自己
        )
        
        if max_level is not None:
            query = query.filter(cls.level <= max_level)
            
        return query.all()

    @classmethod
    def get_direct_children(cls, db_session, parent_id):
        """获取直接子部门"""
        return db_session.query(cls).filter(
            cls.ancestor_id == parent_id,
            cls.level == 1
        ).all()

    @classmethod
    def get_direct_parent(cls, db_session, child_id):
        """获取直接父部门"""
        return db_session.query(cls).filter(
            cls.descendant_id == child_id,
            cls.level == 1
        ).first()

    @classmethod
    def is_ancestor_of(cls, db_session, ancestor_id, descendant_id):
        """检查是否为祖先关系"""
        relation = db_session.query(cls).filter(
            cls.ancestor_id == ancestor_id,
            cls.descendant_id == descendant_id,
            cls.level > 0
        ).first()
        return relation is not None

    @classmethod
    def get_department_tree(cls, db_session, root_id):
        """获取部门树形结构"""
        # 获取所有后代关系
        relations = cls.get_descendants(db_session, root_id)
        
        # 构建树形结构
        tree = {}
        for relation in relations:
            if relation.level == 1:  # 直接子部门
                tree[relation.descendant_id] = {
                    "id": relation.descendant_id,
                    "level": relation.level,
                    "children": []
                }
        
        return tree

    @classmethod
    def rebuild_relations_for_merchant(cls, db_session, merchant_id):
        """重建商户的所有组织关系"""
        from app.models.department import Department

        # 删除商户所有部门的关系
        departments = db_session.query(Department).filter(
            Department.merchant_id == merchant_id
        ).all()
        
        for dept in departments:
            cls.delete_relations_for_department(db_session, dept.id)
        
        # 重新创建关系
        for dept in departments:
            cls.create_relations_for_department(db_session, dept)
        
        db_session.commit()

    @classmethod
    def get_user_accessible_departments(cls, db_session, user_department_id, include_descendants=True, include_ancestors=False):
        """获取用户可访问的部门列表"""
        accessible_dept_ids = [user_department_id]
        
        if include_descendants:
            # 包含所有子部门
            descendants = cls.get_descendants(db_session, user_department_id)
            accessible_dept_ids.extend([rel.descendant_id for rel in descendants])
        
        if include_ancestors:
            # 包含所有父部门
            ancestors = cls.get_ancestors(db_session, user_department_id)
            accessible_dept_ids.extend([rel.ancestor_id for rel in ancestors])
        
        return list(set(accessible_dept_ids))  # 去重
