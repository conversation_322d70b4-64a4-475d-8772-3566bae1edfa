# Redis服务 - 独立部署到缓存服务器
services:
  redis:
    container_name: walmart-bind-card-redis
    image: docker.1ms.run/redis:7.4.2
    restart: always
    # 使用端口映射，确保外部可以访问
    ports:
      - "6379:6379"
    command: >
      redis-server 
      --requirepass ${REDIS_PASSWORD:-7c222fb2927d828af22f592134e8932480637c0d}
      --maxmemory 4gb
      --maxmemory-policy allkeys-lru
      --save 900 1
      --save 300 10
      --save 60 10000
      --appendonly yes
      --appendfsync everysec
      --tcp-keepalive 300
      --timeout 0
      --tcp-backlog 511
      --databases 16
    environment:
      - TZ=Asia/Shanghai
    volumes:
      - redis_data:/data
      - redis_logs:/var/log/redis
    healthcheck:
      test:
        [
          "CMD",
          "redis-cli",
          "-a",
          "${REDIS_PASSWORD:-7c222fb2927d828af22f592134e8932480637c0d}",
          "ping",
        ]
      interval: 10s
      timeout: 5s
      retries: 5
    # 资源限制 - Redis通常需要较多内存
    deploy:
      resources:
        limits:
          cpus: "2.0"
          memory: 6G
        reservations:
          cpus: "1.0"
          memory: 4G
    networks:
      - walmart-network

volumes:
  redis_data:
  redis_logs:

networks:
  walmart-network:
    driver: bridge
