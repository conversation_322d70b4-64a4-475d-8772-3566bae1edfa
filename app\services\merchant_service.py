"""
商户服务模块 - 提供商户管理的核心功能
"""

from typing import List, Optional, Dict, Any
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_
import secrets
import hashlib
import logging
from datetime import datetime

from app.services.base_service import BaseService
from app.models.merchant import Merchant
from app.models.user import User
from app.models.department import Department
from app.schemas.merchant import MerchantCreate, MerchantUpdate

logger = logging.getLogger(__name__)


class MerchantService(BaseService[Merchant, MerchantCreate, MerchantUpdate]):
    """商户服务类"""

    def __init__(self, db: Session):
        super().__init__(Merchant, db)

    def get_by_code(self, code: str) -> Optional[Merchant]:
        """
        根据商户代码获取商户
        
        Args:
            code: 商户代码
            
        Returns:
            Optional[Merchant]: 商户对象或None
        """
        try:
            return self.db.query(Merchant).filter(Merchant.code == code).first()
        except Exception as e:
            self.logger.error(f"根据代码获取商户失败: {e}")
            return None

    def get_by_api_key(self, api_key: str) -> Optional[Merchant]:
        """
        根据API密钥获取商户
        
        Args:
            api_key: API密钥
            
        Returns:
            Optional[Merchant]: 商户对象或None
        """
        try:
            return self.db.query(Merchant).filter(Merchant.api_key == api_key).first()
        except Exception as e:
            self.logger.error(f"根据API密钥获取商户失败: {e}")
            return None

    def apply_data_isolation(self, query, current_user: User):
        """
        【安全修复】重写基类方法，添加强制商户隔离
        应用数据隔离规则 - 支持用户级、部门级、商户级权限，确保商户数据的商户隔离

        Args:
            query: SQLAlchemy查询对象
            current_user: 当前用户

        Returns:
            query: 应用隔离规则后的查询对象
        """
        # 【安全修复】：强制商户隔离 - 除超级管理员外，所有用户只能访问自己的商户数据
        if not current_user.is_superuser:
            if not current_user.merchant_id:
                # 如果用户没有商户ID，返回空结果
                self.logger.warning(f"[SECURITY] 用户 {current_user.id} 没有商户ID，拒绝商户数据访问")
                query = query.filter(Merchant.id == -1)  # 强制返回空结果
                return query

            # 强制添加商户过滤，确保无法跨商户访问（用户只能看到自己的商户）
            query = query.filter(Merchant.id == current_user.merchant_id)
            self.logger.info(f"[SECURITY] 强制商户隔离：用户 {current_user.id} 只能访问自己的商户 {current_user.merchant_id}")

        # 然后应用基类的数据隔离逻辑
        return super().apply_data_isolation(query, current_user)

    def create_merchant(
        self,
        merchant_in: MerchantCreate,
        current_user: User
    ) -> Optional[Merchant]:
        """
        创建商户
        
        Args:
            merchant_in: 商户创建数据
            current_user: 当前操作用户
            
        Returns:
            Optional[Merchant]: 创建的商户对象或None
        """
        try:
            # 只有超级管理员可以创建商户
            if not current_user.is_superuser:
                raise ValueError("只有超级管理员可以创建商户")
            
            # 检查商户代码是否已存在
            if merchant_in.code and self.get_by_code(merchant_in.code):
                raise ValueError(f"商户代码 {merchant_in.code} 已存在")
            
            # 准备商户数据
            merchant_data = merchant_in.dict()
            
            # 生成商户代码（如果未提供）
            if not merchant_data.get('code'):
                merchant_data['code'] = self._generate_merchant_code(merchant_in.name)
            
            # 生成API密钥和密文
            api_key = self._generate_api_key()
            api_secret = self._generate_api_secret()
            
            merchant_data.update({
                'api_key': api_key,
                'api_secret': self._hash_api_secret(api_secret),
                'created_by': current_user.id,
                'api_key_updated_at': datetime.utcnow(),
                'api_secret_updated_at': datetime.utcnow()
            })
            
            # 创建商户
            db_merchant = Merchant(**merchant_data)
            self.db.add(db_merchant)
            self.db.commit()
            self.db.refresh(db_merchant)
            
            # 创建默认根部门
            self._create_root_department(db_merchant.id, current_user.id)
            
            return db_merchant
        except Exception as e:
            self.db.rollback()
            self.logger.error(f"创建商户失败: {e}")
            raise

    def update_merchant(
        self,
        merchant_id: int,
        merchant_in: MerchantUpdate,
        current_user: User
    ) -> Optional[Merchant]:
        """
        更新商户信息
        
        Args:
            merchant_id: 商户ID
            merchant_in: 更新数据
            current_user: 当前操作用户
            
        Returns:
            Optional[Merchant]: 更新后的商户对象或None
        """
        try:
            # 获取商户
            merchant = self.get(merchant_id)
            if not merchant:
                raise ValueError("商户不存在")
            
            # 权限检查：超级管理员或商户管理员
            if not current_user.is_superuser and current_user.merchant_id != merchant_id:
                raise ValueError("无权限修改该商户信息")
            
            # 检查商户代码唯一性
            if merchant_in.code and merchant_in.code != merchant.code:
                if self.get_by_code(merchant_in.code):
                    raise ValueError(f"商户代码 {merchant_in.code} 已存在")
            
            # 更新商户信息
            update_data = merchant_in.dict(exclude_unset=True)
            for field, value in update_data.items():
                if hasattr(merchant, field):
                    setattr(merchant, field, value)
            
            self.db.commit()
            self.db.refresh(merchant)
            return merchant
        except Exception as e:
            self.db.rollback()
            self.logger.error(f"更新商户失败: {e}")
            raise

    def delete_merchant(self, merchant_id: int, current_user: User) -> bool:
        """
        删除商户
        
        Args:
            merchant_id: 商户ID
            current_user: 当前操作用户
            
        Returns:
            bool: 是否删除成功
        """
        try:
            # 只有超级管理员可以删除商户
            if not current_user.is_superuser:
                raise ValueError("只有超级管理员可以删除商户")
            
            merchant = self.get(merchant_id)
            if not merchant:
                return False
            
            # 检查是否有关联的用户
            user_count = self.db.query(User).filter(User.merchant_id == merchant_id).count()
            if user_count > 0:
                raise ValueError(f"商户下还有 {user_count} 个用户，无法删除")
            
            # 检查是否有关联的部门
            dept_count = self.db.query(Department).filter(Department.merchant_id == merchant_id).count()
            if dept_count > 0:
                raise ValueError(f"商户下还有 {dept_count} 个部门，无法删除")
            
            self.db.delete(merchant)
            self.db.commit()
            return True
        except Exception as e:
            self.db.rollback()
            self.logger.error(f"删除商户失败: {e}")
            raise

    def regenerate_api_credentials(
        self,
        merchant_id: int,
        current_user: User
    ) -> Dict[str, str]:
        """
        重新生成API凭证
        
        Args:
            merchant_id: 商户ID
            current_user: 当前操作用户
            
        Returns:
            Dict[str, str]: 新的API凭证
        """
        try:
            # 获取商户
            merchant = self.get(merchant_id)
            if not merchant:
                raise ValueError("商户不存在")
            
            # 权限检查：超级管理员或商户管理员
            if not current_user.is_superuser and current_user.merchant_id != merchant_id:
                raise ValueError("无权限重新生成API凭证")
            
            # 生成新的API凭证
            new_api_key = self._generate_api_key()
            new_api_secret = self._generate_api_secret()
            
            # 更新商户信息
            merchant.api_key = new_api_key
            merchant.api_secret = self._hash_api_secret(new_api_secret)
            merchant.api_key_updated_at = datetime.utcnow()
            merchant.api_secret_updated_at = datetime.utcnow()
            
            self.db.commit()
            
            return {
                'api_key': new_api_key,
                'api_secret': new_api_secret  # 明文返回，仅此一次
            }
        except Exception as e:
            self.db.rollback()
            self.logger.error(f"重新生成API凭证失败: {e}")
            raise

    def verify_api_credentials(self, api_key: str, api_secret: str) -> Optional[Merchant]:
        """
        验证API凭证
        
        Args:
            api_key: API密钥
            api_secret: API密文
            
        Returns:
            Optional[Merchant]: 验证成功返回商户对象，否则返回None
        """
        try:
            merchant = self.get_by_api_key(api_key)
            if not merchant:
                return None
            
            # 验证API密文
            if not self._verify_api_secret(api_secret, merchant.api_secret):
                return None
            
            # 检查商户状态
            if not merchant.status:
                return None
            
            return merchant
        except Exception as e:
            self.logger.error(f"验证API凭证失败: {e}")
            return None

    def get_merchant_statistics(self, merchant_id: int, current_user: User) -> Dict[str, Any]:
        """
        获取商户统计信息

        Args:
            merchant_id: 商户ID
            current_user: 当前操作用户

        Returns:
            Dict[str, Any]: 统计信息
        """
        try:
            # 权限检查
            if not current_user.is_superuser and current_user.merchant_id != merchant_id:
                raise ValueError("无权限查看该商户统计信息")

            merchant = self.get(merchant_id)
            if not merchant:
                raise ValueError("商户不存在")

            # 使用专门的统计函数获取绑卡统计数据
            from app.services.merchant import generate_merchant_statistics
            card_stats = generate_merchant_statistics(self.db, merchant_id)

            # 统计用户数量
            user_count = self.db.query(User).filter(User.merchant_id == merchant_id).count()
            active_user_count = self.db.query(User).filter(
                User.merchant_id == merchant_id,
                User.is_active == True
            ).count()

            # 统计部门数量
            dept_count = self.db.query(Department).filter(Department.merchant_id == merchant_id).count()

            # 合并绑卡统计数据和基础信息
            return {
                'merchant_id': merchant_id,
                'merchant_name': merchant.name,
                'user_count': user_count,
                'active_user_count': active_user_count,
                'department_count': dept_count,
                'status': merchant.status,
                'created_at': merchant.created_at,
                'daily_limit': merchant.daily_limit,
                'hourly_limit': merchant.hourly_limit,
                # 绑卡统计数据
                'total_records': card_stats.total_records,
                'success_records': card_stats.success_records,
                'failed_records': card_stats.failed_records,
                'pending_records': card_stats.pending_records,
                'today_records': card_stats.today_records,
                'yesterday_records': card_stats.yesterday_records
            }
        except Exception as e:
            self.logger.error(f"获取商户统计信息失败: {e}")
            raise

    def search_merchants(
        self,
        search_term: str,
        current_user: User,
        skip: int = 0,
        limit: int = 100
    ) -> List[Merchant]:
        """
        搜索商户
        
        Args:
            search_term: 搜索关键词
            current_user: 当前用户
            skip: 跳过的记录数
            limit: 限制返回的记录数
            
        Returns:
            List[Merchant]: 商户列表
        """
        # 只有超级管理员可以搜索所有商户
        if not current_user.is_superuser:
            return []
        
        search_fields = ['name', 'code', 'contact_name', 'contact_email']
        return self.search(search_term, search_fields, current_user, skip, limit)

    def _generate_merchant_code(self, name: str) -> str:
        """
        生成商户代码
        
        Args:
            name: 商户名称
            
        Returns:
            str: 商户代码
        """
        # 简单的代码生成逻辑，可以根据需要调整
        import re
        import time
        
        # 提取中文或英文字符
        clean_name = re.sub(r'[^\w\u4e00-\u9fff]', '', name)
        if len(clean_name) > 10:
            clean_name = clean_name[:10]
        
        # 添加时间戳确保唯一性
        timestamp = str(int(time.time()))[-6:]
        return f"{clean_name}_{timestamp}".upper()

    def _generate_api_key(self) -> str:
        """生成API密钥"""
        return secrets.token_urlsafe(32)

    def _generate_api_secret(self) -> str:
        """生成API密文"""
        return secrets.token_urlsafe(64)

    def _hash_api_secret(self, api_secret: str) -> str:
        """对API密文进行哈希"""
        return hashlib.sha256(api_secret.encode()).hexdigest()

    def _verify_api_secret(self, api_secret: str, hashed_secret: str) -> bool:
        """验证API密文"""
        return hashlib.sha256(api_secret.encode()).hexdigest() == hashed_secret

    def get_merchant_users(
        self,
        merchant_id: int,
        current_user: User,
        skip: int = 0,
        limit: int = 100
    ) -> Optional[List[User]]:
        """
        获取商户用户列表

        Args:
            merchant_id: 商户ID
            current_user: 当前操作用户
            skip: 跳过的记录数
            limit: 限制返回的记录数

        Returns:
            Optional[List[User]]: 用户列表或None
        """
        try:
            # 权限检查：超级管理员或商户管理员
            if not current_user.is_superuser and current_user.merchant_id != merchant_id:
                raise ValueError("无权限查看该商户用户")

            # 检查商户是否存在
            merchant = self.get(merchant_id)
            if not merchant:
                return None

            # 获取商户用户列表
            users = self.db.query(User).filter(
                User.merchant_id == merchant_id
            ).offset(skip).limit(limit).all()

            return users
        except Exception as e:
            self.logger.error(f"获取商户用户列表失败: {e}")
            raise

    def regenerate_api_key(
        self,
        merchant_id: int,
        current_user: User
    ) -> Optional[Merchant]:
        """
        重新生成API密钥

        Args:
            merchant_id: 商户ID
            current_user: 当前操作用户

        Returns:
            Optional[Merchant]: 更新后的商户对象或None
        """
        try:
            # 获取商户
            merchant = self.get(merchant_id)
            if not merchant:
                raise ValueError("商户不存在")

            # 权限检查：超级管理员或商户管理员
            if not current_user.is_superuser and current_user.merchant_id != merchant_id:
                raise ValueError("无权限重新生成API密钥")

            # 生成新的API密钥和密文
            new_api_key = self._generate_api_key()
            new_api_secret = self._generate_api_secret()

            # 更新商户信息
            merchant.api_key = new_api_key
            merchant.api_secret = self._hash_api_secret(new_api_secret)
            merchant.api_key_updated_at = datetime.utcnow()

            self.db.commit()
            self.db.refresh(merchant)

            # 临时存储明文密文用于返回（仅此一次）
            merchant.api_secret = new_api_secret

            return merchant
        except Exception as e:
            self.db.rollback()
            self.logger.error(f"重新生成API密钥失败: {e}")
            raise

    def _create_root_department(self, merchant_id: int, created_by: int) -> Optional[Department]:
        """
        为商户创建根部门

        Args:
            merchant_id: 商户ID
            created_by: 创建者ID

        Returns:
            Optional[Department]: 创建的根部门或None
        """
        try:
            root_dept = Department(
                merchant_id=merchant_id,
                name="管理部门",
                code="ROOT",
                description="商户管理部门",
                status=True,
                parent_id=None,
                level=1,
                path=f"/{merchant_id}/",
                sort_order=0,
                created_by=created_by
            )
            self.db.add(root_dept)
            self.db.commit()
            self.db.refresh(root_dept)
            return root_dept
        except Exception as e:
            self.db.rollback()
            self.logger.error(f"创建根部门失败: {e}")
            return None
