<template>
    <div class="failure-analysis-chart">
        <el-card shadow="hover" class="chart-card">
            <template #header>
                <div class="card-header">
                    <span>绑卡失败原因分析</span>
                    <div class="chart-controls">
                        <el-date-picker v-model="dateRange" type="daterange" range-separator="至"
                            start-placeholder="开始日期" end-placeholder="结束日期" value-format="YYYY-MM-DD"
                            :disabled-date="disabledDate" @change="handleDateChange" />
                        <div class="chart-actions">
                            <el-radio-group v-model="chartType" size="small" @change="switchChartType">
                                <el-radio-button value="pie">饼图</el-radio-button>
                                <el-radio-button value="bar">柱状图</el-radio-button>
                            </el-radio-group>
                        </div>
                        <el-button type="primary" size="small" icon="Refresh" @click="refreshData"></el-button>
                    </div>
                </div>
            </template>

            <div class="chart-wrapper" v-loading="loading">
                <div ref="chartRef" class="chart-container"></div>
            </div>

            <div class="chart-summary" v-if="!loading">
                <p class="summary-title">失败分析摘要：</p>
                <div class="summary-content">
                    <p>总失败数：<span class="important">{{ totalFailures }}</span> 次</p>
                    <p>主要失败原因：<span class="important">{{ mainReason }}</span> (占比 {{ mainReasonPercentage }}%)</p>
                    <p>相比上周期变化：<span :class="[changeClass]">{{ changeRatio }}</span></p>
                </div>
            </div>
        </el-card>
    </div>
</template>

<script setup>
import { ref, onMounted, onBeforeUnmount } from 'vue'
import * as echarts from 'echarts/core'
import { BarChart, PieChart } from 'echarts/charts'
import {
    TitleComponent,
    TooltipComponent,
    LegendComponent,
    GridComponent,
    ToolboxComponent
} from 'echarts/components'
import { CanvasRenderer } from 'echarts/renderers'
import { ElMessage } from 'element-plus'
import { cardDataApi } from '@/api/modules/cardDataApi'

// 注册ECharts必须的组件
echarts.use([
    BarChart,
    PieChart,
    TitleComponent,
    TooltipComponent,
    LegendComponent,
    GridComponent,
    ToolboxComponent,
    CanvasRenderer
])

const props = defineProps({
    merchantId: {
        type: [String, Number],
        default: null
    }
})

// 图表状态
const loading = ref(false)
const chartRef = ref(null)
const chart = ref(null)
const chartType = ref('pie')
const dateRange = ref([])

// 数据分析结果
const totalFailures = ref(0)
const mainReason = ref('')
const mainReasonPercentage = ref(0)
const changeRatio = ref('-')
const changeClass = ref('')

// 禁用未来日期
const disabledDate = (time) => {
    return time.getTime() > Date.now()
}

// 获取失败原因数据
const fetchData = async () => {
    loading.value = true
    try {
        // 构建请求参数
        const params = {}

        // 如果选择了日期范围，添加到参数中
        if (dateRange.value && dateRange.value.length === 2) {
            params.startDate = dateRange.value[0]
            params.endDate = dateRange.value[1]
        }

        // 如果有商家ID，添加到参数中
        if (props.merchantId) {
            params.merchantId = props.merchantId
        }

        // 调用API获取失败原因数据
        const data = await cardDataApi.getFailureAnalysis(params)

        // 如果没有数据，使用模拟数据
        if (!data || !data.reasons || data.reasons.length === 0) {
            const mockData = generateMockData()
            updateChart(mockData)
            updateSummary(mockData)
        } else {
            updateChart(data)
            updateSummary(data)
        }
    } catch (error) {
        console.error('获取失败原因数据失败:', error)
        ElMessage.error('获取失败原因数据失败')
        // 出错时使用模拟数据
        const mockData = generateMockData()
        updateChart(mockData)
        updateSummary(mockData)
    } finally {
        loading.value = false
    }
}

// 生成模拟数据
const generateMockData = () => {
    const reasons = [
        { name: '卡号无效', value: Math.floor(Math.random() * 50) + 30 },
        { name: '余额不足', value: Math.floor(Math.random() * 40) + 20 },
        { name: 'API超时', value: Math.floor(Math.random() * 30) + 15 },
        { name: '用户取消', value: Math.floor(Math.random() * 20) + 10 },
        { name: '身份验证失败', value: Math.floor(Math.random() * 25) + 15 },
        { name: '系统错误', value: Math.floor(Math.random() * 15) + 5 },
        { name: '其他原因', value: Math.floor(Math.random() * 10) + 5 }
    ]

    // 按值从大到小排序
    reasons.sort((a, b) => b.value - a.value)

    // 计算总数
    const total = reasons.reduce((sum, item) => sum + item.value, 0)

    // 计算百分比
    reasons.forEach(item => {
        item.percentage = ((item.value / total) * 100).toFixed(2)
    })

    return {
        reasons,
        total,
        previousTotal: total * (0.8 + Math.random() * 0.4) // 模拟上一时期的数据，浮动正负20%
    }
}

// 初始化图表
const initChart = () => {
    if (!chartRef.value) return

    chart.value = echarts.init(chartRef.value)
    fetchData()

    // 监听窗口大小变化，重新调整图表大小
    window.addEventListener('resize', handleResize)
}

// 更新图表
const updateChart = (data) => {
    if (!chart.value) return

    if (chartType.value === 'pie') {
        const option = {
            tooltip: {
                trigger: 'item',
                formatter: '{a} <br/>{b}: {c} ({d}%)'
            },
            legend: {
                orient: 'vertical',
                right: 10,
                top: 'center',
                type: 'scroll'
            },
            series: [
                {
                    name: '失败原因',
                    type: 'pie',
                    radius: ['40%', '70%'],
                    avoidLabelOverlap: false,
                    itemStyle: {
                        borderRadius: 10,
                        borderColor: '#fff',
                        borderWidth: 2
                    },
                    label: {
                        show: false,
                        position: 'center'
                    },
                    emphasis: {
                        label: {
                            show: true,
                            fontSize: 20,
                            fontWeight: 'bold'
                        }
                    },
                    labelLine: {
                        show: false
                    },
                    data: data.reasons
                }
            ]
        }
        chart.value.setOption(option)
    } else {
        const option = {
            tooltip: {
                trigger: 'axis',
                axisPointer: {
                    type: 'shadow'
                }
            },
            grid: {
                left: '3%',
                right: '4%',
                bottom: '3%',
                containLabel: true
            },
            xAxis: {
                type: 'value'
            },
            yAxis: {
                type: 'category',
                data: data.reasons.map(item => item.name)
            },
            series: [
                {
                    name: '失败次数',
                    type: 'bar',
                    stack: 'total',
                    label: {
                        show: true,
                        position: 'right'
                    },
                    emphasis: {
                        focus: 'series'
                    },
                    data: data.reasons.map(item => item.value)
                }
            ]
        }
        chart.value.setOption(option)
    }
}

// 更新摘要信息
const updateSummary = (data) => {
    totalFailures.value = data.total
    if (data.reasons.length > 0) {
        mainReason.value = data.reasons[0].name
        mainReasonPercentage.value = data.reasons[0].percentage
    }

    // 计算变化率
    const changeValue = ((data.total - data.previousTotal) / data.previousTotal * 100).toFixed(2)
    changeRatio.value = changeValue > 0 ? `上升 ${changeValue}%` : `下降 ${Math.abs(changeValue)}%`
    changeClass.value = changeValue > 0 ? 'negative-change' : 'positive-change'
}

// 切换图表类型
const switchChartType = () => {
    fetchData()
}

// 处理日期变化
const handleDateChange = () => {
    fetchData()
}

// 刷新数据
const refreshData = () => {
    fetchData()
    ElMessage.success('数据已刷新')
}

// 调整图表大小
const handleResize = () => {
    chart.value?.resize()
}

onMounted(() => {
    initChart()
})

onBeforeUnmount(() => {
    window.removeEventListener('resize', handleResize)
    chart.value?.dispose()
})
</script>

<style scoped>
.failure-analysis-chart {
    margin-bottom: 20px;
}

.chart-card {
    width: 100%;
}

.card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.chart-controls {
    display: flex;
    align-items: center;
    gap: 10px;
}

.chart-wrapper {
    padding: 10px 0;
}

.chart-container {
    height: 400px;
    width: 100%;
}

.chart-summary {
    margin-top: 15px;
    background-color: #f5f7fa;
    padding: 10px 15px;
    border-radius: 4px;
}

.summary-title {
    font-weight: bold;
    margin-bottom: 10px;
    color: #606266;
}

.summary-content {
    display: flex;
    justify-content: space-between;
    flex-wrap: wrap;
}

.summary-content p {
    margin: 5px 0;
    flex: 1;
    min-width: 200px;
}

.important {
    font-weight: bold;
    color: #409EFF;
}

.positive-change {
    color: #67C23A;
}

.negative-change {
    color: #F56C6C;
}
</style>