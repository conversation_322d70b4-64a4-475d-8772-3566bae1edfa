-- ========================================
-- 01-沃尔玛绑卡系统数据库表结构设计
-- 设计原则：标准化、专业化、模块化
-- 执行顺序：第1步 - 创建数据库和表结构
-- ========================================

-- 设置连接字符集
SET NAMES utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 设置时区
SET time_zone = '+08:00';

-- 创建数据库并设置统一字符
CREATE DATABASE IF NOT EXISTS `walmart_card_db` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

USE `walmart_card_db`;

-- 确保所有表都使用统一的字符集和排序规则
ALTER DATABASE `walmart_card_db` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- ========================================
-- 1. 企业组织架构表
-- ========================================

-- 1.1 商户表（企业/公司表）
CREATE TABLE `merchants` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '商户ID',
    `name` varchar(100) NOT NULL COMMENT '商户名称',
    `code` varchar(50) NULL UNIQUE COMMENT '商户代码',
    `api_key` varchar(64) NOT NULL UNIQUE COMMENT 'API密钥',
    `api_secret` varchar(128) NOT NULL COMMENT 'API密钥密文',
    `status` tinyint(1) NOT NULL DEFAULT 1 COMMENT '状态：1启用，0禁用',
    `callback_url` varchar(255) NULL COMMENT '回调URL',
    `allowed_ips` text NULL COMMENT 'IP白名单',
    `daily_limit` int(11) NOT NULL DEFAULT 10000 COMMENT '每日绑卡上限',
    `hourly_limit` int(11) NOT NULL DEFAULT 1000 COMMENT '每小时绑卡上限',
    `concurrency_limit` int(11) NOT NULL DEFAULT 100 COMMENT '最大并发数',
    `priority` int(11) NOT NULL DEFAULT 0 COMMENT '处理优先级',
    `request_timeout` int(11) NOT NULL DEFAULT 30 COMMENT '请求超时时间',
    `retry_count` int(11) NOT NULL DEFAULT 3 COMMENT '重试次数',
    `contact_name` varchar(100) NULL COMMENT '联系人',
    `contact_phone` varchar(50) NULL COMMENT '联系电话',
    `contact_email` varchar(100) NULL COMMENT '联系邮箱',
    `remark` text NULL COMMENT '备注',
    `custom_config` text NULL COMMENT '自定义配置',
    `created_by` bigint NULL COMMENT '创建者ID',
    `api_key_updated_at` datetime(3) NULL COMMENT 'API密钥更新时间',
    `api_secret_updated_at` datetime(3) NULL COMMENT 'API密文更新时间',
    `created_at` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '创建时间',
    `updated_at` datetime(3) NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3) COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_merchants_name` (`name`),
    UNIQUE KEY `uk_merchants_code` (`code`),
    UNIQUE KEY `uk_merchants_api_key` (`api_key`),
    KEY `idx_merchants_status` (`status`),
    KEY `idx_merchants_created_at` (`created_at`)
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '商户表';

-- 1.2 部门表（支持多层级结构）
CREATE TABLE `departments` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '部门ID',
    `merchant_id` bigint NOT NULL COMMENT '所属商户ID',
    `name` varchar(100) NOT NULL COMMENT '部门名称',
    `code` varchar(50) NOT NULL COMMENT '部门代码',
    `description` text NULL COMMENT '部门描述',
    `status` tinyint(1) NOT NULL DEFAULT 1 COMMENT '状态：1启用，0禁用',
    `parent_id` bigint NULL COMMENT '父部门ID（支持多层级嵌套）',
    `level` int(11) NOT NULL DEFAULT 1 COMMENT '部门层级：1一级部门，2二级部门...',
    `path` varchar(500) NULL COMMENT '部门路径，如：/1/2/3/',
    `sort_order` int(11) NOT NULL DEFAULT 0 COMMENT '排序号',
    `manager_id` bigint NULL COMMENT '部门负责人ID',
    `manager_name` varchar(100) NULL COMMENT '部门负责人姓名',
    `manager_phone` varchar(50) NULL COMMENT '负责人电话',
    `manager_email` varchar(100) NULL COMMENT '负责人邮箱',
    `business_scope` text NULL COMMENT '业务范围',
    `custom_config` text NULL COMMENT '自定义配置JSON',
    `remark` text NULL COMMENT '备注',
    `created_by` bigint NULL COMMENT '创建者ID',
    `created_at` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '创建时间',
    `updated_at` datetime(3) NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3) COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_departments_merchant_code` (`merchant_id`, `code`),
    KEY `idx_departments_merchant_id` (`merchant_id`),
    KEY `idx_departments_parent_id` (`parent_id`),
    KEY `idx_departments_manager_id` (`manager_id`),
    KEY `idx_departments_status` (`status`),
    KEY `idx_departments_level` (`level`),
    KEY `idx_departments_path` (`path`),
    KEY `idx_departments_created_at` (`created_at`),
    CONSTRAINT `fk_departments_merchant` FOREIGN KEY (`merchant_id`) REFERENCES `merchants` (`id`) ON DELETE CASCADE,
    CONSTRAINT `fk_departments_parent` FOREIGN KEY (`parent_id`) REFERENCES `departments` (`id`) ON DELETE CASCADE
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '部门表（支持多层级结构）';

-- ========================================
-- 2. 权限管理表（RBAC模型）
-- ========================================

-- 2.1 角色表
CREATE TABLE `roles` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '角色ID',
    `name` varchar(50) NOT NULL COMMENT '角色名称',
    `code` varchar(50) NOT NULL UNIQUE COMMENT '角色代码',
    `description` text NULL COMMENT '角色描述',
    `is_enabled` tinyint(1) NOT NULL DEFAULT 1 COMMENT '是否启用',
    `is_system` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否系统内置角色',
    `sort_order` int(11) NOT NULL DEFAULT 0 COMMENT '排序号',
    `created_by` bigint NULL COMMENT '创建者ID',
    `remark` text NULL COMMENT '备注',
    `created_at` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '创建时间',
    `updated_at` datetime(3) NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3) COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_roles_code` (`code`),
    KEY `idx_roles_name` (`name`),
    KEY `idx_roles_is_enabled` (`is_enabled`),
    KEY `idx_roles_sort_order` (`sort_order`)
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '角色表';

-- 2.2 权限表
CREATE TABLE `permissions` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '权限ID',
    `code` varchar(100) NOT NULL UNIQUE COMMENT '权限代码，格式：module:action 或 api:/path',
    `name` varchar(100) NOT NULL COMMENT '权限名称',
    `description` text NULL COMMENT '权限描述',
    `resource_type` varchar(20) NOT NULL DEFAULT 'api' COMMENT '资源类型',
    `resource_path` varchar(200) NULL COMMENT '资源路径（菜单路径或API路径）',
    `is_enabled` tinyint(1) NOT NULL DEFAULT 1 COMMENT '是否启用',
    `sort_order` int(11) NOT NULL DEFAULT 0 COMMENT '排序号',
    `created_at` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '创建时间',
    `updated_at` datetime(3) NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3) COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_permissions_code` (`code`),
    KEY `idx_permissions_resource_type` (`resource_type`),
    KEY `idx_permissions_resource_path` (`resource_path`),
    KEY `idx_permissions_is_enabled` (`is_enabled`)
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '权限表';

-- 2.3 菜单表
CREATE TABLE `menus` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '菜单ID',
    `name` varchar(100) NOT NULL COMMENT '菜单名称',
    `code` varchar(50) NOT NULL UNIQUE COMMENT '菜单代码',
    `path` varchar(200) NULL COMMENT '菜单路径',
    `component` varchar(200) NULL COMMENT '组件路径',
    `icon` varchar(100) NULL COMMENT '图标',
    `parent_id` bigint NULL COMMENT '父菜单ID',
    `level` int(11) NOT NULL DEFAULT 1 COMMENT '菜单层级',
    `sort_order` int(11) NOT NULL DEFAULT 0 COMMENT '排序号',
    `is_visible` tinyint(1) NOT NULL DEFAULT 1 COMMENT '是否可见',
    `is_enabled` tinyint(1) NOT NULL DEFAULT 1 COMMENT '是否启用',
    `menu_type` varchar(20) NOT NULL DEFAULT 'menu' COMMENT '菜单类型',
    `description` text NULL COMMENT '菜单描述',
    `created_at` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '创建时间',
    `updated_at` datetime(3) NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3) COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_menus_code` (`code`),
    KEY `idx_menus_parent_id` (`parent_id`),
    KEY `idx_menus_level` (`level`),
    KEY `idx_menus_sort_order` (`sort_order`),
    KEY `idx_menus_is_visible` (`is_visible`),
    KEY `idx_menus_is_enabled` (`is_enabled`),
    CONSTRAINT `fk_menus_parent` FOREIGN KEY (`parent_id`) REFERENCES `menus` (`id`) ON DELETE CASCADE
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '菜单表';

-- ========================================
-- 3. 用户管理表
-- ========================================

-- 3.1 用户表
CREATE TABLE `users` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '用户ID',
    `username` varchar(50) NOT NULL UNIQUE COMMENT '用户名',
    `email` varchar(100) NULL UNIQUE COMMENT '邮箱',
    `hashed_password` varchar(200) NOT NULL COMMENT '密码哈希',
    `full_name` varchar(100) NULL COMMENT '姓名',
    `phone` varchar(20) NULL COMMENT '电话',
    `is_active` tinyint(1) NOT NULL DEFAULT 1 COMMENT '是否激活',
    `is_superuser` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否为超级用户',
    `merchant_id` bigint NULL COMMENT '所属商家ID（兼容字段）',
    `department_id` bigint NULL COMMENT '所属部门ID',
    `position` varchar(100) NULL COMMENT '职位',
    `api_key` varchar(64) NULL UNIQUE COMMENT 'API密钥',
    `api_secret` varchar(128) NULL COMMENT 'API密钥密文',
    `sign` varchar(128) NULL COMMENT '签名密钥',
    `daily_bind_limit` int(11) NOT NULL DEFAULT 100 COMMENT '每日绑卡数量限制',
    `hourly_bind_limit` int(11) NOT NULL DEFAULT 20 COMMENT '每小时绑卡数量限制',
    `last_login_ip` varchar(64) NULL COMMENT '最后登录IP',
    `last_login_time` varchar(64) NULL COMMENT '最后登录时间',
    `remark` text NULL COMMENT '备注',
    `created_at` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '创建时间',
    `updated_at` datetime(3) NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3) COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_users_username` (`username`),
    UNIQUE KEY `uk_users_email` (`email`),
    UNIQUE KEY `uk_users_api_key` (`api_key`),
    KEY `idx_users_merchant_id` (`merchant_id`),
    KEY `idx_users_department_id` (`department_id`),
    KEY `idx_users_is_active` (`is_active`),
    KEY `idx_users_is_superuser` (`is_superuser`),
    CONSTRAINT `fk_users_merchant` FOREIGN KEY (`merchant_id`) REFERENCES `merchants` (`id`) ON DELETE SET NULL
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '用户表';

-- 添加外键约束（在相关表创建后）
ALTER TABLE `users`
ADD CONSTRAINT `fk_users_department` FOREIGN KEY (`department_id`) REFERENCES `departments` (`id`) ON DELETE SET NULL;

ALTER TABLE `departments`
ADD CONSTRAINT `fk_departments_manager` FOREIGN KEY (`manager_id`) REFERENCES `users` (`id`) ON DELETE SET NULL;

ALTER TABLE `departments`
ADD CONSTRAINT `fk_departments_created_by` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`) ON DELETE SET NULL;

-- ========================================
-- 4. 关联关系表（多对多）
-- ========================================

-- 4.1 用户角色关联表
CREATE TABLE `user_roles` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '关联ID',
    `user_id` bigint NOT NULL COMMENT '用户ID',
    `role_id` bigint NOT NULL COMMENT '角色ID',
    `created_at` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '创建时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_user_role` (`user_id`, `role_id`),
    KEY `idx_user_roles_user` (`user_id`),
    KEY `idx_user_roles_role` (`role_id`),
    CONSTRAINT `fk_user_roles_user` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
    CONSTRAINT `fk_user_roles_role` FOREIGN KEY (`role_id`) REFERENCES `roles` (`id`) ON DELETE CASCADE
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '用户角色关联表';

-- 4.2 用户权限关联表
CREATE TABLE `user_permissions` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '关联ID',
    `user_id` bigint NOT NULL COMMENT '用户ID',
    `permission_id` bigint NOT NULL COMMENT '权限ID',
    `created_at` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '创建时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_user_permission` (`user_id`, `permission_id`),
    KEY `idx_user_permissions_user` (`user_id`),
    KEY `idx_user_permissions_permission` (`permission_id`),
    CONSTRAINT `fk_user_permissions_user` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
    CONSTRAINT `fk_user_permissions_permission` FOREIGN KEY (`permission_id`) REFERENCES `permissions` (`id`) ON DELETE CASCADE
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '用户权限关联表';

-- 4.3 角色权限关联表
CREATE TABLE `role_permissions` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '关联ID',
    `role_id` bigint NOT NULL COMMENT '角色ID',
    `permission_id` bigint NOT NULL COMMENT '权限ID',
    `created_at` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '创建时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_role_permission` (`role_id`, `permission_id`),
    KEY `idx_role_permissions_role` (`role_id`),
    KEY `idx_role_permissions_permission` (`permission_id`),
    CONSTRAINT `fk_role_permissions_role` FOREIGN KEY (`role_id`) REFERENCES `roles` (`id`) ON DELETE CASCADE,
    CONSTRAINT `fk_role_permissions_permission` FOREIGN KEY (`permission_id`) REFERENCES `permissions` (`id`) ON DELETE CASCADE
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '角色权限关联表';

-- 4.4 角色菜单关联表
CREATE TABLE `role_menus` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '关联ID',
    `role_id` bigint NOT NULL COMMENT '角色ID',
    `menu_id` bigint NOT NULL COMMENT '菜单ID',
    `created_at` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '创建时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_role_menu` (`role_id`, `menu_id`),
    KEY `idx_role_menus_role` (`role_id`),
    KEY `idx_role_menus_menu` (`menu_id`),
    CONSTRAINT `fk_role_menus_role` FOREIGN KEY (`role_id`) REFERENCES `roles` (`id`) ON DELETE CASCADE,
    CONSTRAINT `fk_role_menus_menu` FOREIGN KEY (`menu_id`) REFERENCES `menus` (`id`) ON DELETE CASCADE
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '角色菜单关联表';

-- 4.5 菜单权限关联表
CREATE TABLE `menu_permissions` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '关联ID',
    `menu_id` bigint NOT NULL COMMENT '菜单ID',
    `permission_id` bigint NOT NULL COMMENT '权限ID',
    `created_at` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '创建时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_menu_permission` (`menu_id`, `permission_id`),
    KEY `idx_menu_permissions_menu` (`menu_id`),
    KEY `idx_menu_permissions_permission` (`permission_id`),
    CONSTRAINT `fk_menu_permissions_menu` FOREIGN KEY (`menu_id`) REFERENCES `menus` (`id`) ON DELETE CASCADE,
    CONSTRAINT `fk_menu_permissions_permission` FOREIGN KEY (`permission_id`) REFERENCES `permissions` (`id`) ON DELETE CASCADE
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '菜单权限关联表';

-- ========================================
-- 5. 业务特定表（沃尔玛绑卡系统）
-- ========================================

-- 5.1 沃尔玛服务器配置表
CREATE TABLE `walmart_server` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '配置ID',
    `api_url` varchar(255) NOT NULL COMMENT 'API地址',
    `referer` varchar(500) NOT NULL DEFAULT 'https://servicewechat.com/wx81d3e1fe4c2e11b4/175/page-frame.html' COMMENT 'HTTP请求Referer头',
    `timeout` int(11) NOT NULL DEFAULT 30 COMMENT '请求超时时间(秒)',
    `retry_count` int(11) NOT NULL DEFAULT 3 COMMENT '重试次数',
    `daily_bind_limit` int(11) NOT NULL DEFAULT 1000 COMMENT '每日绑卡限制',
    `api_rate_limit` int(11) NOT NULL DEFAULT 60 COMMENT 'API速率限制(次/分钟)',
    `max_retry_times` int(11) NOT NULL DEFAULT 3 COMMENT '最大重试次数',
    `bind_timeout_seconds` int(11) NOT NULL DEFAULT 30 COMMENT '绑卡超时时间(秒)',
    `verification_code_expires` int(11) NOT NULL DEFAULT 300 COMMENT '验证码过期时间(秒)',
    `log_retention_days` int(11) NOT NULL DEFAULT 90 COMMENT '日志保留天数',
    `enable_ip_whitelist` tinyint(1) NOT NULL DEFAULT 1 COMMENT '是否启用IP白名单',
    `enable_security_audit` tinyint(1) NOT NULL DEFAULT 1 COMMENT '是否启用安全审计',
    `maintenance_mode` tinyint(1) NOT NULL DEFAULT 0 COMMENT '维护模式',
    `maintenance_message` text NULL COMMENT '维护模式消息',
    `is_active` tinyint(1) NOT NULL DEFAULT 1 COMMENT '是否激活',
    `extra_config` json NULL COMMENT '附加配置',
    `created_at` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '创建时间',
    `updated_at` datetime(3) NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3) COMMENT '更新时间',
    PRIMARY KEY (`id`),
    KEY `idx_walmart_server_is_active` (`is_active`),
    KEY `idx_walmart_server_maintenance_mode` (`maintenance_mode`),
    KEY `idx_walmart_server_created_at` (`created_at`)
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '沃尔玛服务器配置表';

-- 5.2 沃尔玛CK配置表
CREATE TABLE `walmart_ck` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `sign` text NOT NULL COMMENT '用户签名,格式：ck#token#wx_sign#version,如：25487f6f129649999ef6b1f269b2a1f5@d0e0e25d37720f856a3ba753089b1e47#mmb5Lz2g3i4ilzn/kHXpFg==#26',
    `total_limit` int(11) NOT NULL DEFAULT 20 COMMENT '总绑卡次数限制',
    `bind_count` int(11) NOT NULL DEFAULT 0 COMMENT '累计已绑卡数量',
    `last_bind_time` varchar(64) NULL COMMENT '最后绑卡时间',
    `active` tinyint(1) NOT NULL DEFAULT 1 COMMENT '是否启用',
    `description` varchar(255) NULL COMMENT '设置描述',
    `created_by` bigint NULL COMMENT '创建者用户ID',
    `merchant_id` bigint NOT NULL COMMENT '所属商户ID（数据隔离）',
    `department_id` bigint NOT NULL COMMENT '所属部门ID（数据隔离）',
    `is_deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否已删除：0未删除，1已删除',
    `created_at` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '创建时间',
    `updated_at` datetime(3) NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3) COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_walmart_ck_sign` (`sign` (255)),
    KEY `idx_walmart_ck_active` (`active`),
    KEY `idx_walmart_ck_created_by` (`created_by`),
    KEY `idx_walmart_ck_merchant_id` (`merchant_id`),
    KEY `idx_walmart_ck_department_id` (`department_id`),
    KEY `idx_walmart_ck_is_deleted` (`is_deleted`),
    KEY `idx_walmart_ck_active_deleted` (`active`, `is_deleted`),
    KEY `idx_walmart_ck_merchant_deleted` (`merchant_id`, `is_deleted`),
    KEY `idx_walmart_ck_created_at` (`created_at`),
    CONSTRAINT `fk_walmart_ck_created_by` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`) ON DELETE SET NULL,
    CONSTRAINT `fk_walmart_ck_merchant` FOREIGN KEY (`merchant_id`) REFERENCES `merchants` (`id`) ON DELETE CASCADE,
    CONSTRAINT `fk_walmart_ck_department` FOREIGN KEY (`department_id`) REFERENCES `departments` (`id`) ON DELETE CASCADE
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '沃尔玛CK配置表';

-- ========================================
-- 6. 绑卡业务表
-- ========================================

-- 6.1 卡记录表
CREATE TABLE `card_records` (
    `id` varchar(36) NOT NULL COMMENT '记录ID（UUID）',
    `merchant_id` bigint NOT NULL COMMENT '商家ID',
    `department_id` bigint NULL COMMENT '所属部门ID（绑卡成功后填入实际使用的CK所属部门）',
    `walmart_ck_id` bigint NULL COMMENT '使用的沃尔玛CK ID（绑卡成功后填入实际使用的CK ID）',
    `merchant_order_id` varchar(255) NOT NULL COMMENT '商家订单号',
    `amount` int(11) NOT NULL COMMENT '订单金额，单位：分',
    `actual_amount` int(11) NULL COMMENT '实际卡金额，单位：分',
    `balance` varchar(32) NULL COMMENT 'balance字段，单位元，原始格式',
    `cardBalance` varchar(32) NULL COMMENT 'cardBalance字段，单位元，原始格式',
    `balanceCnt` varchar(32) NULL COMMENT 'balanceCnt字段，单位元，原始格式',
    `card_number` varchar(50) NOT NULL COMMENT '卡号',
    `card_password` varchar(512) NULL COMMENT '卡密（加密存储）',
    `status` varchar(20) NOT NULL DEFAULT 'pending' COMMENT '状态',
    `request_id` varchar(64) NOT NULL UNIQUE COMMENT '请求ID',
    `trace_id` varchar(64) NULL COMMENT '追踪ID，用于跟踪整个处理流程',
    `request_data` json NOT NULL COMMENT '请求数据',
    `response_data` json NULL COMMENT '响应数据',
    `error_message` varchar(500) NULL COMMENT '错误信息',
    `retry_count` int(11) NOT NULL DEFAULT 0 COMMENT '重试次数',
    `process_time` float NULL COMMENT '处理时间(秒)',
    `ip_address` varchar(64) NULL COMMENT 'IP地址',
    `remark` varchar(500) NULL COMMENT '备注',
    `ext_data` varchar(512) NULL COMMENT '扩展数据，回调时原样返回',
    `callback_status` varchar(20) NOT NULL DEFAULT 'pending' COMMENT '回调状态',
    `callback_result` varchar(500) NULL COMMENT '回调结果描述',
    `callback_time` datetime(3) NULL COMMENT '回调时间',
    `created_at` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '创建时间',
    `updated_at` datetime(3) NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3) COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_card_records_request_id` (`request_id`),
    KEY `idx_card_records_merchant_id` (`merchant_id`),
    KEY `idx_card_records_department_id` (`department_id`),
    KEY `idx_card_records_walmart_ck_id` (`walmart_ck_id`),
    KEY `idx_card_records_merchant_order_id` (`merchant_order_id`),
    KEY `idx_card_records_card_number` (`card_number`),
    KEY `idx_card_records_status` (`status`),
    KEY `idx_card_records_trace_id` (`trace_id`),
    KEY `idx_card_records_callback_status` (`callback_status`),
    KEY `idx_card_records_created_at` (`created_at`),
    KEY `idx_card_records_ck_status` (`walmart_ck_id`, `status`),
    KEY `idx_card_records_ck_time` (`walmart_ck_id`, `created_at`),
    CONSTRAINT `fk_card_records_merchant` FOREIGN KEY (`merchant_id`) REFERENCES `merchants` (`id`) ON DELETE CASCADE,
    CONSTRAINT `fk_card_records_department` FOREIGN KEY (`department_id`) REFERENCES `departments` (`id`) ON DELETE SET NULL,
    CONSTRAINT `fk_card_records_walmart_ck` FOREIGN KEY (`walmart_ck_id`) REFERENCES `walmart_ck` (`id`) ON DELETE SET NULL
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '卡记录表';

-- 6.2 绑卡日志表
CREATE TABLE `binding_logs` (
    `id` varchar(36) NOT NULL COMMENT '日志ID（UUID）',
    `card_record_id` varchar(36) NOT NULL COMMENT '关联的卡记录ID',
    `log_type` varchar(20) NOT NULL DEFAULT 'system' COMMENT '日志类型',
    `log_level` varchar(20) NOT NULL DEFAULT 'info' COMMENT '日志级别',
    `message` text NOT NULL COMMENT '日志消息',
    `details` json NULL COMMENT '详细信息',
    `request_data` json NULL COMMENT '请求数据',
    `response_data` json NULL COMMENT '响应数据',
    `duration_ms` float NULL COMMENT '操作耗时(毫秒)',
    `attempt_number` varchar(10) NULL COMMENT '尝试序号',
    `walmart_ck_id` bigint NULL COMMENT '沃尔玛CKID（关键字段：用于统计CK绑卡成功数）',
    `ip_address` varchar(64) NULL COMMENT 'IP地址',
    `timestamp` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '日志时间戳',
    `created_at` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '创建时间',
    `updated_at` datetime(3) NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3) COMMENT '更新时间',
    PRIMARY KEY (`id`),
    KEY `idx_binding_logs_card_record_id` (`card_record_id`),
    KEY `idx_binding_logs_log_type` (`log_type`),
    KEY `idx_binding_logs_log_level` (`log_level`),
    KEY `idx_binding_logs_walmart_ck_id` (`walmart_ck_id`),
    KEY `idx_binding_logs_timestamp` (`timestamp`),
    KEY `idx_binding_logs_created_at` (`created_at`),
    CONSTRAINT `fk_binding_logs_card_record` FOREIGN KEY (`card_record_id`) REFERENCES `card_records` (`id`) ON DELETE CASCADE
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '绑卡日志表';

-- ========================================
-- 7. 组织架构扩展表
-- ========================================

-- 7.1 组织架构关系表（用于快速查询部门层级关系）
CREATE TABLE `organization_relations` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '关系ID',
    `ancestor_id` bigint NOT NULL COMMENT '祖先部门ID',
    `descendant_id` bigint NOT NULL COMMENT '后代部门ID',
    `level` int NOT NULL DEFAULT 0 COMMENT '层级差距：0表示自己，1表示直接子部门',
    `created_at` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '创建时间',
    `updated_at` datetime(3) NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3) COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_org_relations` (
        `ancestor_id`,
        `descendant_id`
    ),
    KEY `idx_org_relations_ancestor` (`ancestor_id`),
    KEY `idx_org_relations_descendant` (`descendant_id`),
    KEY `idx_org_relations_level` (`level`),
    CONSTRAINT `fk_org_relations_ancestor` FOREIGN KEY (`ancestor_id`) REFERENCES `departments` (`id`) ON DELETE CASCADE,
    CONSTRAINT `fk_org_relations_descendant` FOREIGN KEY (`descendant_id`) REFERENCES `departments` (`id`) ON DELETE CASCADE
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '组织架构关系表';

-- 7.2 用户组织关系表（基于商户+部门的多层级结构）
CREATE TABLE `user_organizations` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '关系ID',
    `user_id` bigint NOT NULL COMMENT '用户ID',
    `merchant_id` bigint NOT NULL COMMENT '商户ID',
    `department_id` bigint NULL COMMENT '部门ID（可为空，表示直属商户）',
    `position` varchar(100) NULL COMMENT '职位',
    `is_primary` tinyint(1) NOT NULL DEFAULT 1 COMMENT '是否主要组织关系',
    `status` tinyint(1) NOT NULL DEFAULT 1 COMMENT '状态：1启用，0禁用',
    `start_date` date NULL COMMENT '开始日期',
    `end_date` date NULL COMMENT '结束日期',
    `created_by` bigint NULL COMMENT '创建者ID',
    `created_at` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '创建时间',
    `updated_at` datetime(3) NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3) COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_user_org_primary` (`user_id`, `is_primary`),
    KEY `idx_user_org_user_id` (`user_id`),
    KEY `idx_user_org_merchant_id` (`merchant_id`),
    KEY `idx_user_org_department_id` (`department_id`),
    KEY `idx_user_org_status` (`status`),
    KEY `idx_user_org_merchant_dept` (
        `merchant_id`,
        `department_id`
    ),
    CONSTRAINT `fk_user_org_user` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
    CONSTRAINT `fk_user_org_merchant` FOREIGN KEY (`merchant_id`) REFERENCES `merchants` (`id`) ON DELETE CASCADE,
    CONSTRAINT `fk_user_org_department` FOREIGN KEY (`department_id`) REFERENCES `departments` (`id`) ON DELETE CASCADE
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '用户组织关系表（商户+部门二级结构）';

-- ========================================
-- 8. 系统配置表
-- ========================================

-- 8.1 系统设置表
CREATE TABLE `system_settings` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '设置ID',
    `key` varchar(100) NOT NULL UNIQUE COMMENT '设置键',
    `value` text NULL COMMENT '设置值',
    `description` varchar(255) NULL COMMENT '设置描述',
    `type` varchar(20) NOT NULL DEFAULT 'string' COMMENT '值类型',
    `is_public` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否公开（前端可访问）',
    `is_enabled` tinyint(1) NOT NULL DEFAULT 1 COMMENT '是否启用',
    `is_system` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否为系统内置设置',
    `created_at` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '创建时间',
    `updated_at` datetime(3) NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3) COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_system_settings_key` (`key`),
    KEY `idx_system_settings_is_public` (`is_public`)
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '系统设置表';

-- ========================================
-- 9. 通知系统表
-- ========================================

-- 9.1 通知表
CREATE TABLE `notifications` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '通知ID',
    `user_id` bigint NOT NULL COMMENT '接收用户ID',
    `title` varchar(255) NOT NULL COMMENT '通知标题',
    `content` text NOT NULL COMMENT '通知内容',
    `type` varchar(20) NOT NULL DEFAULT 'system' COMMENT '通知类型',
    `status` varchar(20) NOT NULL DEFAULT 'unread' COMMENT '通知状态',
    `link` varchar(255) NULL COMMENT '相关链接',
    `is_important` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否重要',
    `read_at` datetime(3) NULL COMMENT '阅读时间',
    `extra_data` json NULL COMMENT '额外数据',
    `created_at` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '创建时间',
    `updated_at` datetime(3) NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3) COMMENT '更新时间',
    PRIMARY KEY (`id`),
    KEY `idx_notifications_user_id` (`user_id`),
    KEY `idx_notifications_type` (`type`),
    KEY `idx_notifications_status` (`status`),
    KEY `idx_notifications_is_important` (`is_important`),
    KEY `idx_notifications_created_at` (`created_at`),
    CONSTRAINT `fk_notifications_user` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '通知表';

-- 9.2 通知配置表
CREATE TABLE `notification_configs` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '配置ID',
    `user_id` bigint NOT NULL COMMENT '用户ID',
    `type` varchar(20) NOT NULL COMMENT '通知类型',
    `enabled` tinyint(1) NOT NULL DEFAULT 1 COMMENT '是否启用',
    `email_enabled` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否启用邮件通知',
    `sms_enabled` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否启用短信通知',
    `created_at` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '创建时间',
    `updated_at` datetime(3) NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3) COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_notification_configs_user_type` (`user_id`, `type`),
    KEY `idx_notification_configs_user_id` (`user_id`),
    KEY `idx_notification_configs_type` (`type`),
    CONSTRAINT `fk_notification_configs_user` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '通知配置表';

-- ========================================
-- 10. 审计日志表
-- ========================================

-- 10.1 审计日志表
CREATE TABLE `audit_log` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '日志ID',
    `event_type` varchar(20) NOT NULL DEFAULT 'SYSTEM' COMMENT '事件类型',
    `level` varchar(20) NOT NULL DEFAULT 'INFO' COMMENT '事件级别',
    `user_id` bigint DEFAULT NULL COMMENT '用户ID',
    `operator_id` bigint DEFAULT NULL COMMENT '操作者ID',
    `merchant_id` bigint DEFAULT NULL COMMENT '商户ID',
    `resource_type` varchar(50) DEFAULT NULL COMMENT '资源类型',
    `resource_id` varchar(50) DEFAULT NULL COMMENT '资源ID',
    `action` varchar(50) DEFAULT NULL COMMENT '操作动作',
    `ip_address` varchar(45) DEFAULT NULL COMMENT 'IP地址',
    `user_agent` text DEFAULT NULL COMMENT '用户代理',
    `request_method` varchar(10) DEFAULT NULL COMMENT '请求方法',
    `request_path` varchar(500) DEFAULT NULL COMMENT '请求路径',
    `request_params` json DEFAULT NULL COMMENT '请求参数',
    `message` text NOT NULL COMMENT '事件描述',
    `details` json DEFAULT NULL COMMENT '详细信息',
    `trace_id` varchar(50) DEFAULT NULL COMMENT '追踪ID',
    `created_at` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '创建时间',
    `updated_at` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3) COMMENT '更新时间',
    PRIMARY KEY (`id`),
    KEY `idx_audit_log_user` (`user_id`),
    KEY `idx_audit_log_operator` (`operator_id`),
    KEY `idx_audit_log_merchant` (`merchant_id`),
    KEY `idx_audit_log_event_type` (`event_type`),
    KEY `idx_audit_log_level` (`level`),
    KEY `idx_audit_log_resource` (`resource_type`, `resource_id`),
    KEY `idx_audit_log_created_at` (`created_at`),
    KEY `idx_audit_log_trace_id` (`trace_id`),
    CONSTRAINT `fk_audit_log_user` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE SET NULL,
    CONSTRAINT `fk_audit_log_operator` FOREIGN KEY (`operator_id`) REFERENCES `users` (`id`) ON DELETE SET NULL,
    CONSTRAINT `fk_audit_log_merchant` FOREIGN KEY (`merchant_id`) REFERENCES `merchants` (`id`) ON DELETE SET NULL
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '审计日志表';

-- 为用户表添加谷歌验证器双因子认证字段
-- 执行时间：2025-06-28

-- 添加双因子认证相关字段
ALTER TABLE users
ADD COLUMN totp_secret VARCHAR(255) NULL COMMENT '谷歌验证器密钥(加密存储)',
ADD COLUMN totp_enabled BOOLEAN DEFAULT FALSE COMMENT '是否启用双因子认证',
ADD COLUMN totp_backup_codes TEXT NULL COMMENT '备用恢复码(JSON格式,加密存储)',
ADD COLUMN totp_last_used_at TIMESTAMP NULL COMMENT '最后使用TOTP的时间',
ADD COLUMN totp_setup_at TIMESTAMP NULL COMMENT 'TOTP设置时间';

-- 添加索引
CREATE INDEX idx_users_totp_enabled ON users(totp_enabled);
CREATE INDEX idx_users_totp_last_used ON users(totp_last_used_at);

-- 创建双因子认证日志表
CREATE TABLE totp_logs (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
    user_id BIGINT NOT NULL COMMENT '用户ID',
    action VARCHAR(50) NOT NULL COMMENT '操作类型：setup/verify/disable/backup_used',
    success BOOLEAN NOT NULL COMMENT '操作是否成功',
    ip_address VARCHAR(64) NULL COMMENT '操作IP地址',
    user_agent TEXT NULL COMMENT '用户代理',
    error_message VARCHAR(255) NULL COMMENT '错误信息',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    INDEX idx_totp_logs_user_id (user_id),
    INDEX idx_totp_logs_action (action),
    INDEX idx_totp_logs_created_at (created_at),
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
) COMMENT='双因子认证操作日志表';

-- 创建双因子认证策略表
CREATE TABLE totp_policies (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
    role_name VARCHAR(50) NOT NULL COMMENT '角色名称',
    is_required BOOLEAN DEFAULT FALSE COMMENT '是否强制启用',
    grace_period_days INT DEFAULT 7 COMMENT '宽限期天数',
    backup_codes_count INT DEFAULT 10 COMMENT '备用码数量',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    UNIQUE KEY uk_totp_policies_role (role_name)
) COMMENT='双因子认证策略表';

-- 插入默认策略
INSERT INTO totp_policies (role_name, is_required, grace_period_days, backup_codes_count) VALUES
('super_admin', TRUE, 0, 10),      -- 超级管理员强制启用，无宽限期
('merchant_admin', FALSE, 7, 10),  -- 商户管理员可选，7天宽限期
('ck_supplier', FALSE, 7, 8);      -- CK供应商可选，7天宽限期
