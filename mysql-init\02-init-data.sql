-- ========================================
-- 02-沃尔玛绑卡系统初始化数据（简化权限系统版本）
-- 执行顺序：第2步 - 插入基础数据
--
-- 数据初始化说明：
-- 1. 仅创建超级管理员账号，移除所有部门相关的初始化数据
-- 2. 部门数据将在实际使用时通过管理界面创建，确保系统灵活性
--
-- 权限系统重构说明：
-- 1. 简化角色系统：仅保留3个核心角色
--    - 超级管理员 (super_admin): 拥有系统最高权限，可访问所有功能模块
--    - 商户管理员 (merchant_admin): 管理所属商户范围内的数据，严格商户间数据隔离
--    - 商户CK供应商 (ck_supplier): 仅管理自己部门的CK资源，严格部门间数据隔离
--
-- 2. 权限分配原则：
--    - 遵循最小权限原则
--    - 严格数据隔离（商户间、部门间）
--    - 动态权限检查，禁止硬编码角色判断
--
-- 3. 菜单权限分配：
--    - 超级管理员：所有系统菜单
--    - 商户管理员：仪表盘、部门管理、绑卡数据、通知中心、绑卡操作、CK管理、沃尔玛服务器配置
--    - CK供应商：仅CK管理菜单
-- ========================================

-- 设置连接字符集（解决中文乱码问题）
SET NAMES utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 设置时区
SET time_zone = '+08:00';

USE `walmart_card_db`;

-- ========================================
-- 1. 初始化角色数据（简化版本 - 仅保留3个核心角色）
-- ========================================

-- 创建3个核心系统角色
INSERT IGNORE INTO `roles` (id,
    `name`, `code`, `description`, `is_enabled`, `is_system`,
    `sort_order`, `created_at`, `updated_at`
) VALUES
(1,'超级管理员', 'super_admin', '系统超级管理员，拥有所有权限，可访问所有功能模块', 1, 1, 1, NOW(3), NOW(3)),
(2,'商户管理员', 'merchant_admin', '商户管理员，可管理所属商户范围内的所有数据，严格商户间数据隔离', 1, 1, 2, NOW(3), NOW(3)),
(3,'商户CK供应商', 'ck_supplier', '商户CK供应商，只能管理自己部门的CK资源，严格部门间数据隔离', 1, 1, 3, NOW(3), NOW(3));


-- ========================================
-- 3. 初始化用户数据
-- ========================================

-- 创建超级管理员用户（密码：7c222fb2927d828af22f592134e8932480637c0d）
INSERT IGNORE INTO `users` (id,
    `username`, `email`, `hashed_password`, `full_name`,
    `is_active`, `is_superuser`, `daily_bind_limit`, `hourly_bind_limit`,
    `created_at`, `updated_at`
) VALUES
(1,'admin', '<EMAIL>', '$2b$12$zgPDshZvwEeRVhrYL78cMOVRfwCqrGloBcioV4umOyrYbrgebwwdG',
 '系统管理员', 1, 1, 10000, 1000, NOW(3), NOW(3));

-- ========================================
-- 4. 分配用户角色（基于简化的3角色系统）
-- ========================================

-- 分配超级管理员角色给admin用户
INSERT IGNORE INTO `user_roles` (`user_id`, `role_id`)
SELECT u.id, r.id
FROM `users` u, `roles` r
WHERE u.username = 'admin' AND r.code = 'super_admin';


-- ========================================
-- 5. 初始化菜单数据（基于3角色系统优化）
-- ========================================

-- 创建主菜单（基于3角色需求优化）
INSERT IGNORE INTO `menus` (
    `name`, `code`, `path`, `component`, `icon`, `parent_id`,
    `level`, `sort_order`, `is_visible`, `is_enabled`, `menu_type`,
    `description`, `created_at`, `updated_at`
) VALUES
-- 一级菜单
('仪表盘', 'dashboard', '/dashboard', 'Dashboard', 'dashboard', NULL, 1, 1, 1, 1, 'menu', '系统仪表盘', NOW(3), NOW(3)),
('系统管理', 'system', '/system', 'Layout', 'setting', NULL, 1, 2, 1, 1, 'menu', '系统管理模块（仅超级管理员）', NOW(3), NOW(3)),
('商家管理', 'merchant', '/merchant', 'Layout', 'shop', NULL, 1, 3, 1, 1, 'menu', '商家管理模块', NOW(3), NOW(3)),
('绑卡数据', 'cards', '/cards', 'CardRecords', 'credit-card', NULL, 1, 4, 1, 1, 'menu', '绑卡数据管理', NOW(3), NOW(3)),
('沃尔玛配置', 'walmart', '/walmart', 'Layout', 'global', NULL, 1, 5, 1, 1, 'menu', '沃尔玛相关配置', NOW(3), NOW(3)),
('通知中心', 'notification', '/notification', 'NotificationCenter', 'bell', NULL, 1, 6, 1, 1, 'menu', '通知中心', NOW(3), NOW(3)),
('绑卡操作', 'bind', '/bind', 'Layout', 'link', NULL, 1, 7, 1, 1, 'menu', '绑卡操作模块', NOW(3), NOW(3));

-- 二级菜单 - 系统管理（仅超级管理员可见）
INSERT IGNORE INTO `menus` (`name`, `code`, `path`, `component`, `icon`, `parent_id`, `level`, `sort_order`, `is_visible`, `is_enabled`, `menu_type`, `description`, `created_at`, `updated_at`) VALUES
('用户管理', 'system:user', '/system/user', 'UserManagement', 'user', 2, 2, 1, 1, 1, 'menu', '用户管理（仅超级管理员）', NOW(3), NOW(3)),
('角色管理', 'system:role', '/system/role', 'RoleManagement', 'team', 2, 2, 2, 1, 1, 'menu', '角色管理（仅超级管理员）', NOW(3), NOW(3)),
('权限管理', 'system:permission', '/system/permission', 'PermissionManagement', 'safety-certificate', 2, 2, 3, 1, 1, 'menu', '权限管理（仅超级管理员）', NOW(3), NOW(3)),
('菜单管理', 'system:menu', '/system/menu', 'MenuManagement', 'menu', 2, 2, 4, 1, 1, 'menu', '菜单管理（仅超级管理员）', NOW(3), NOW(3)),
('系统设置', 'system:settings', '/system/settings', 'SystemSettings', 'setting', 2, 2, 5, 1, 1, 'menu', '系统设置（仅超级管理员）', NOW(3), NOW(3)),
('安全设置', 'system:security', '/system/security', 'SecurityManagement', 'lock', 2, 2, 6, 0, 1, 'menu', '安全设置（已移至个人设置）', NOW(3), NOW(3)),
('恢复处理', 'system:recovery', '/system/recovery', 'RecoveryManagement', 'tool', 2, 2, 7, 1, 1, 'menu', '恢复处理（仅超级管理员）', NOW(3), NOW(3));

-- 二级菜单 - 商家管理
INSERT IGNORE INTO `menus` (`name`, `code`, `path`, `component`, `icon`, `parent_id`, `level`, `sort_order`, `is_visible`, `is_enabled`, `menu_type`, `description`, `created_at`, `updated_at`) VALUES
('商户列表', 'merchant:list', '/merchant/list', 'MerchantList', 'shop', 3, 2, 1, 1, 1, 'menu', '商户列表管理（仅超级管理员）', NOW(3), NOW(3)),
('部门管理', 'merchant:department', '/merchant/department', 'DepartmentManagement', 'apartment', 3, 2, 2, 1, 1, 'menu', '部门管理', NOW(3), NOW(3));

-- 二级菜单 - 沃尔玛配置
INSERT IGNORE INTO `menus` (`name`, `code`, `path`, `component`, `icon`, `parent_id`, `level`, `sort_order`, `is_visible`, `is_enabled`, `menu_type`, `description`, `created_at`, `updated_at`) VALUES
('沃尔玛API配置', 'walmart:walmart', '/walmart/walmart-server', 'WalmartServerConfig', 'api', 5, 2, 1, 0, 1, 'menu', '沃尔玛API服务器配置（隐藏菜单，系统级配置）', NOW(3), NOW(3)),
('CK管理', 'walmart:user', '/walmart/user', 'WalmartCKManagement', 'user-add', 5, 2, 2, 1, 1, 'menu', 'CK用户管理', NOW(3), NOW(3));

-- 二级菜单 - 绑卡操作（商户管理员可见）
INSERT IGNORE INTO `menus` (`name`, `code`, `path`, `component`, `icon`, `parent_id`, `level`, `sort_order`, `is_visible`, `is_enabled`, `menu_type`, `description`, `created_at`, `updated_at`) VALUES
('单卡绑定', 'bind:single', '/bind/single-bind', 'SingleBind', 'credit-card', 7, 2, 1, 1, 1, 'menu', '单卡绑定操作', NOW(3), NOW(3)),
('批量绑卡', 'bind:batch', '/bind/batch-bind', 'BatchBind', 'file-text', 7, 2, 2, 1, 1, 'menu', '批量绑卡操作', NOW(3), NOW(3));

-- 三级菜单 - 安全设置（已移至个人设置，不在导航菜单显示）
-- 先获取安全设置菜单的ID，然后插入子菜单
INSERT IGNORE INTO `menus` (`name`, `code`, `path`, `component`, `icon`, `parent_id`, `level`, `sort_order`, `is_visible`, `is_enabled`, `menu_type`, `description`, `created_at`, `updated_at`)
SELECT '谷歌验证器管理', 'system:security:totp', '/system/security/totp', 'TOTPManagement', 'shield', m.id, 3, 1, 0, 1, 'menu', '谷歌验证器双因子认证管理（已移至个人设置）', NOW(3), NOW(3)
FROM `menus` m WHERE m.code = 'system:security';

-- ========================================
-- 6. 初始化权限数据（简化版本 - 基于3角色系统）
-- ========================================


-- 插入核心菜单权限（基于实际菜单结构）
INSERT IGNORE INTO `permissions` (`code`, `name`, `description`, `resource_type`, `resource_path`, `is_enabled`, `sort_order`) VALUES
-- 菜单权限
('menu:dashboard', '仪表盘菜单', '访问仪表盘菜单', 'menu', '/dashboard', 1, 1),
('menu:system', '系统管理菜单', '访问系统管理菜单', 'menu', '/system', 1, 2),
('menu:system:user', '用户管理菜单', '访问用户管理菜单', 'menu', '/system/user', 1, 3),
('menu:system:role', '角色管理菜单', '访问角色管理菜单', 'menu', '/system/role', 1, 4),
('menu:system:permission', '权限管理菜单', '访问权限管理菜单', 'menu', '/system/permission', 1, 5),
('menu:system:menu', '菜单管理菜单', '访问菜单管理菜单', 'menu', '/system/menu', 1, 6),
('menu:system:settings', '系统设置菜单', '访问系统设置菜单', 'menu', '/system/settings', 1, 7),
('menu:system:security', '安全设置菜单', '访问安全设置菜单', 'menu', '/system/security', 1, 8),
('menu:system:security:totp', '谷歌验证器管理菜单', '访问谷歌验证器管理菜单', 'menu', '/system/security/totp', 1, 9),
('menu:system:recovery', '恢复处理菜单', '访问恢复处理菜单', 'menu', '/system/recovery', 1, 10),
('menu:merchant', '商家管理菜单', '访问商家管理菜单', 'menu', '/merchant', 1, 11),
('menu:merchant:list', '商户列表菜单', '访问商户列表菜单（仅超级管理员）', 'menu', '/merchant/list', 1, 12),
('menu:merchant:department', '部门管理菜单', '访问部门管理菜单', 'menu', '/merchant/department', 1, 13),
('menu:cards', '绑卡数据菜单', '访问绑卡数据菜单', 'menu', '/cards', 1, 14),
('menu:walmart', '沃尔玛配置菜单', '访问沃尔玛配置菜单', 'menu', '/walmart', 1, 15),
('menu:walmart:walmart', '沃尔玛API配置菜单', '访问沃尔玛API配置菜单', 'menu', '/walmart/walmart-server', 1, 16),
('menu:walmart:user', 'CK管理菜单', '访问CK管理菜单', 'menu', '/walmart/user', 1, 17),
('menu:notification', '通知中心菜单', '访问通知中心菜单', 'menu', '/notification', 1, 18),
('menu:bind', '绑卡操作菜单', '访问绑卡操作菜单', 'menu', '/bind', 1, 19),
('menu:bind:single', '单卡绑定菜单', '访问单卡绑定菜单', 'menu', '/bind/single-bind', 1, 20),
('menu:bind:batch', '批量绑卡菜单', '访问批量绑卡菜单', 'menu', '/bind/batch-bind', 1, 21);

-- 插入完整的API权限配置（基于实际API路径和系统需求）
INSERT IGNORE INTO `permissions` (`code`, `name`, `description`, `resource_type`, `resource_path`, `is_enabled`, `sort_order`) VALUES

-- ========================================
-- 1. 认证模块权限 (/auth)
-- ========================================
('api:/api/v1/auth', '认证API模块', '认证相关API模块权限', 'api', '/api/v1/auth', 1, 100),
('api:auth:login', '用户登录', '用户登录权限', 'api', '/api/v1/auth/login', 1, 101),
('api:auth:logout', '用户登出', '用户登出权限', 'api', '/api/v1/auth/logout', 1, 102),
('api:auth:refresh-token', '刷新令牌', '刷新访问令牌权限', 'api', '/api/v1/auth/refresh-token', 1, 103),
('api:auth:system-check', '系统检查', '系统状态检查权限', 'api', '/api/v1/auth/system-check', 1, 104),

-- ========================================
-- 2. 用户管理模块权限 (/users)
-- ========================================
('api:/api/v1/users', '用户管理API模块', '用户管理相关API模块权限', 'api', '/api/v1/users', 1, 110),
('api:users:read', '查看用户', '查看用户列表和详情权限', 'api', '/api/v1/users', 1, 111),
('api:users:create', '创建用户', '创建新用户权限', 'api', '/api/v1/users', 1, 112),
('api:users:update', '更新用户', '更新用户信息权限', 'api', '/api/v1/users', 1, 113),
('api:users:delete', '删除用户', '删除用户权限', 'api', '/api/v1/users', 1, 114),
('api:users:me', '获取个人信息', '获取当前用户个人信息权限', 'api', '/api/v1/users/me', 1, 115),
('api:users:assign-role', '分配角色', '为用户分配角色权限', 'api', '/api/v1/users', 1, 116),
('api:users:change-password', '修改密码', '修改用户密码权限', 'api', '/api/v1/users', 1, 117),
('api:users:reset-password', '重置密码', '重置用户密码权限', 'api', '/api/v1/users', 1, 118),

-- ========================================
-- 3. 角色管理模块权限 (/roles)
-- ========================================
('api:/api/v1/roles', '角色管理API模块', '角色管理相关API模块权限', 'api', '/api/v1/roles', 1, 120),
('api:roles:read', '查看角色', '查看角色列表和详情权限', 'api', '/api/v1/roles', 1, 121),
('api:roles:create', '创建角色', '创建新角色权限', 'api', '/api/v1/roles', 1, 122),
('api:roles:update', '更新角色', '更新角色信息权限', 'api', '/api/v1/roles', 1, 123),
('api:roles:delete', '删除角色', '删除角色权限', 'api', '/api/v1/roles', 1, 124),
('api:roles:assign-permissions', '分配权限', '为角色分配权限', 'api', '/api/v1/roles', 1, 125),
('api:roles:assign-menus', '分配菜单', '为角色分配菜单权限', 'api', '/api/v1/roles', 1, 126),

-- ========================================
-- 4. 权限管理模块权限 (/permissions)
-- ========================================
('api:/api/v1/permissions', '权限管理API模块', '权限管理相关API模块权限', 'api', '/api/v1/permissions', 1, 130),
('api:permissions:read', '查看权限', '查看权限列表和详情权限', 'api', '/api/v1/permissions', 1, 131),
('api:permissions:create', '创建权限', '创建新权限', 'api', '/api/v1/permissions', 1, 132),
('api:permissions:update', '更新权限', '更新权限信息', 'api', '/api/v1/permissions', 1, 133),
('api:permissions:delete', '删除权限', '删除权限', 'api', '/api/v1/permissions', 1, 134),

-- ========================================
-- 5. 高级权限管理模块权限 (/permissions-advanced)
-- ========================================
('api:/api/v1/permissions-advanced', '高级权限管理API模块', '高级权限管理相关API模块权限', 'api', '/api/v1/permissions-advanced', 1, 135),
('api:permissions-advanced:sync', '同步权限', '同步系统权限配置', 'api', '/api/v1/permissions-advanced', 1, 136),
('api:permissions-advanced:conflicts', '权限冲突检查', '检查权限冲突', 'api', '/api/v1/permissions-advanced', 1, 137),
('api:permissions-advanced:admin', '权限管理员', '高级权限管理员权限', 'api', '/api/v1/permissions-advanced', 1, 138),

-- ========================================
-- 6. 菜单管理模块权限 (/menus)
-- ========================================
('api:/api/v1/menus', '菜单管理API模块', '菜单管理相关API模块权限', 'api', '/api/v1/menus', 1, 140),
('api:menus:read', '查看菜单', '查看菜单列表和详情权限', 'api', '/api/v1/menus', 1, 141),
('api:menus:create', '创建菜单', '创建新菜单权限', 'api', '/api/v1/menus', 1, 142),
('api:menus:update', '更新菜单', '更新菜单信息权限', 'api', '/api/v1/menus', 1, 143),
('api:menus:delete', '删除菜单', '删除菜单权限', 'api', '/api/v1/menus', 1, 144),
('api:menus:user-menus', '获取用户菜单', '获取当前用户可访问的菜单权限', 'api', '/api/v1/menus/user-menus', 1, 145),
('api:menus:tree', '菜单树结构', '获取菜单树形结构权限', 'api', '/api/v1/menus/tree', 1, 146),

-- ========================================
-- 7. 商户管理模块权限 (/merchants)
-- ========================================
('api:/api/v1/merchants', '商户管理API模块', '商户管理相关API模块权限', 'api', '/api/v1/merchants', 1, 150),
('api:merchants:read', '查看商户', '查看商户列表和详情权限', 'api', '/api/v1/merchants', 1, 151),
('api:merchants:create', '创建商户', '创建新商户权限', 'api', '/api/v1/merchants', 1, 152),
('api:merchants:update', '更新商户', '更新商户信息权限', 'api', '/api/v1/merchants', 1, 153),
('api:merchants:delete', '删除商户', '删除商户权限', 'api', '/api/v1/merchants', 1, 154),
('api:merchants:config', '商户配置', '管理商户配置权限', 'api', '/api/v1/merchants', 1, 155),
('api:merchants:ip-whitelist', '商户IP白名单', '管理商户IP白名单权限', 'api', '/api/v1/merchants', 1, 156),

-- ========================================
-- 8. 部门管理模块权限 (/departments)
-- ========================================
('api:/api/v1/departments', '部门管理API模块', '部门管理相关API模块权限', 'api', '/api/v1/departments', 1, 160),
('api:departments:read', '查看部门', '查看部门列表和详情权限', 'api', '/api/v1/departments', 1, 161),
('api:departments:create', '创建部门', '创建新部门权限', 'api', '/api/v1/departments', 1, 162),
('api:departments:update', '更新部门', '更新部门信息权限', 'api', '/api/v1/departments', 1, 163),
('api:departments:delete', '删除部门', '删除部门权限', 'api', '/api/v1/departments', 1, 164),
('api:departments:tree', '部门树结构', '获取部门树形结构权限', 'api', '/api/v1/departments/tree', 1, 165),

-- ========================================
-- 9. 仪表盘模块权限 (/dashboard)
-- ========================================
('api:/api/v1/dashboard', '仪表盘API模块', '仪表盘数据API模块权限', 'api', '/api/v1/dashboard', 1, 170),
('api:dashboard:read', '查看仪表盘', '查看仪表盘数据权限', 'api', '/api/v1/dashboard', 1, 171),
('api:dashboard:statistics', '仪表盘统计', '查看仪表盘统计数据权限', 'api', '/api/v1/dashboard/statistics', 1, 172),
('api:dashboard:summary', '仪表盘摘要', '查看仪表盘摘要信息权限', 'api', '/api/v1/dashboard/summary', 1, 173),
('api:dashboard:amount-statistics', '金额统计', '查看绑卡金额统计权限', 'api', '/api/v1/dashboard/amount-statistics', 1, 174),
('api:dashboard:ck-efficiency', 'CK效率统计', '查看CK使用效率统计权限', 'api', '/api/v1/dashboard/ck-efficiency-statistics', 1, 175),
('api:/api/v1/departments', '部门管理API', '部门管理相关API', 'api', '/api/v1/departments', 1, 106),
('api:/api/v1/walmart-ck', 'CK管理API', 'CK用户管理相关API', 'api', '/api/v1/walmart-ck', 1, 107),
('api:/api/v1/cards', '绑卡记录API', '绑卡记录相关API', 'api', '/api/v1/cards', 1, 108),
('api:/api/v1/notifications', '通知API', '通知相关API', 'api', '/api/v1/notifications', 1, 109),
('api:/api/v1/walmart-server', '沃尔玛服务器API', '沃尔玛服务器配置API', 'api', '/api/v1/walmart-server', 1, 110),
('api:/api/v1/card-bind', '绑卡操作API', '绑卡操作相关API', 'api', '/api/v1/card-bind', 1, 111),

-- 详细的CRUD操作权限（基于最小权限原则）
('api:users:read', '查看用户', '查看用户信息的权限', 'api', '/api/v1/users', 1, 200),
('api:users:create', '创建用户', '创建新用户的权限', 'api', '/api/v1/users', 1, 201),
('api:users:update', '更新用户', '更新用户信息的权限', 'api', '/api/v1/users', 1, 202),
('api:users:delete', '删除用户', '删除用户的权限', 'api', '/api/v1/users', 1, 203),
('api:users:assign-role', '分配用户角色', '为用户分配角色的权限', 'api', '/api/v1/users', 1, 204),
('api:users:me', '获取个人信息', '获取当前登录用户个人信息的权限', 'api', '/api/v1/users/me', 1, 205),

('api:roles:read', '查看角色', '查看角色信息的权限', 'api', '/api/v1/roles', 1, 210),
('api:roles:create', '创建角色', '创建新角色的权限', 'api', '/api/v1/roles', 1, 211),
('api:roles:update', '更新角色', '更新角色信息的权限', 'api', '/api/v1/roles', 1, 212),
('api:roles:delete', '删除角色', '删除角色的权限', 'api', '/api/v1/roles', 1, 213),

('api:permissions:read', '查看权限', '查看权限信息的权限', 'api', '/api/v1/permissions', 1, 220),
('api:permissions:create', '创建权限', '创建新权限的权限', 'api', '/api/v1/permissions', 1, 221),
('api:permissions:update', '更新权限', '更新权限信息的权限', 'api', '/api/v1/permissions', 1, 222),
('api:permissions:delete', '删除权限', '删除权限的权限', 'api', '/api/v1/permissions', 1, 223),

('api:menus:read', '查看菜单', '查看菜单信息的权限', 'api', '/api/v1/menus', 1, 230),
('api:menus:create', '创建菜单', '创建新菜单的权限', 'api', '/api/v1/menus', 1, 231),
('api:menus:update', '更新菜单', '更新菜单信息的权限', 'api', '/api/v1/menus', 1, 232),
('api:menus:delete', '删除菜单', '删除菜单的权限', 'api', '/api/v1/menus', 1, 233),
('api:menus:user-menus', '获取用户菜单', '获取当前用户可访问菜单列表的权限', 'api', '/api/v1/menus/user-menus', 1, 234),

('api:merchants:read', '查看商户', '查看商户信息的权限', 'api', '/api/v1/merchants', 1, 240),
('api:merchants:create', '创建商户', '创建新商户的权限', 'api', '/api/v1/merchants', 1, 241),
('api:merchants:update', '更新商户', '更新商户信息的权限', 'api', '/api/v1/merchants', 1, 242),
('api:merchants:delete', '删除商户', '删除商户的权限', 'api', '/api/v1/merchants', 1, 243),

('api:departments:read', '查看部门', '查看部门信息的权限', 'api', '/api/v1/departments', 1, 250),
('api:departments:create', '创建部门', '创建新部门的权限', 'api', '/api/v1/departments', 1, 251),
('api:departments:update', '更新部门', '更新部门信息的权限', 'api', '/api/v1/departments', 1, 252),
('api:departments:delete', '删除部门', '删除部门的权限', 'api', '/api/v1/departments', 1, 253),

('api:walmart-ck:read', '查看CK', '查看CK信息的权限', 'api', '/api/v1/walmart-ck', 1, 260),
('api:walmart-ck:create', '创建CK', '创建新CK的权限', 'api', '/api/v1/walmart-ck', 1, 261),
('api:walmart-ck:update', '更新CK', '更新CK信息的权限', 'api', '/api/v1/walmart-ck', 1, 262),
('api:walmart-ck:delete', '删除CK', '删除CK的权限', 'api', '/api/v1/walmart-ck', 1, 263),

('api:cards:read', '查看绑卡记录', '查看绑卡记录的权限', 'api', '/api/v1/cards', 1, 270),
('api:cards:create', '创建绑卡记录', '创建新绑卡记录的权限', 'api', '/api/v1/cards', 1, 271),
('api:cards:retry', '重试绑卡记录', '重试失败绑卡记录的权限', 'api', '/api/v1/cards', 1, 272),

('api:notifications:read', '查看通知', '查看通知的权限', 'api', '/api/v1/notifications', 1, 280),
('api:notifications:create', '创建通知', '创建通知的权限', 'api', '/api/v1/notifications', 1, 281),
('api:notifications:update', '更新通知', '更新通知的权限', 'api', '/api/v1/notifications', 1, 282),

('api:walmart-server:read', '查看沃尔玛服务器配置', '查看沃尔玛服务器配置的权限', 'api', '/api/v1/walmart-server', 1, 290),
('api:walmart-server:update', '更新沃尔玛服务器配置', '更新沃尔玛服务器配置的权限', 'api', '/api/v1/walmart-server', 1, 291),

('api:card-bind:create', '执行绑卡操作', '执行绑卡操作的权限', 'api', '/api/v1/card-bind', 1, 300),

-- ========================================
-- 补充缺失的API权限
-- ========================================

-- 系统管理模块权限 (/system)
('api:/api/v1/system', '系统管理API模块', '系统管理相关API模块权限', 'api', '/api/v1/system', 1, 310),
('api:system:read', '查看系统信息', '查看系统参数和状态权限', 'api', '/api/v1/system', 1, 311),
('api:system:update', '更新系统配置', '更新系统参数配置权限', 'api', '/api/v1/system', 1, 312),
('api:system:public-params', '获取公开参数', '获取系统公开参数权限', 'api', '/api/v1/system/public-params', 1, 313),

-- 绑卡日志模块权限 (/binding-logs)
('api:/api/v1/binding-logs', '绑卡日志API模块', '绑卡日志相关API模块权限', 'api', '/api/v1/binding-logs', 1, 320),
('api:binding-logs:read', '查看绑卡日志', '查看绑卡日志权限', 'api', '/api/v1/binding-logs', 1, 321),

-- 数据恢复模块权限 (/recovery)
('api:/api/v1/recovery', '数据恢复API模块', '数据恢复相关API模块权限', 'api', '/api/v1/recovery', 1, 330),
('api:recovery:execute', '执行数据恢复', '执行数据恢复操作权限', 'api', '/api/v1/recovery', 1, 331),
('api:recovery:read', '查看恢复记录', '查看数据恢复记录权限', 'api', '/api/v1/recovery', 1, 332),

-- 公共接口权限 (/public)
('api:/api/v1/public', '公共API模块', '公共API模块权限', 'api', '/api/v1/public', 1, 340),
('api:public:read', '访问公共接口', '访问公共接口权限', 'api', '/api/v1/public', 1, 341),

-- 健康检查权限
('api:health:check', '健康检查', '系统健康检查权限', 'api', '/api/v1/health', 1, 350),

-- 补充详细操作权限
('api:cards:update', '更新卡记录', '更新卡记录信息权限', 'api', '/api/v1/cards', 1, 273),
('api:cards:delete', '删除卡记录', '删除卡记录权限', 'api', '/api/v1/cards', 1, 274),
('api:cards:batch-retry', '批量重试绑卡', '批量重试失败绑卡记录权限', 'api', '/api/v1/cards/batch-retry', 1, 275),
('api:cards:statistics', '卡记录统计', '查看卡记录统计数据权限', 'api', '/api/v1/cards/statistics', 1, 276),
('api:cards:export', '导出卡记录', '导出卡记录数据权限', 'api', '/api/v1/cards/export', 1, 277),
('api:cards:today-statistics', '今日统计', '查看今日卡记录统计权限', 'api', '/api/v1/cards/today-statistics', 1, 278),

('api:walmart-ck:test', '测试CK连接', '测试沃尔玛CK连接权限', 'api', '/api/v1/walmart-ck/test', 1, 264),
('api:walmart-ck:statistics', 'CK统计数据', '查看CK统计数据权限', 'api', '/api/v1/walmart-ck/statistics', 1, 265),
('api:walmart-ck:isolation-check', 'CK隔离检查', '检查CK数据隔离完整性权限', 'api', '/api/v1/walmart-ck/isolation-check', 1, 266),

('api:walmart-server:create', '创建服务器配置', '创建沃尔玛服务器配置权限', 'api', '/api/v1/walmart-server', 1, 292),
('api:walmart-server:delete', '删除服务器配置', '删除沃尔玛服务器配置权限', 'api', '/api/v1/walmart-server', 1, 293),
('api:walmart-server:test', '测试服务器连接', '测试沃尔玛服务器连接权限', 'api', '/api/v1/walmart-server/test', 1, 294),

('api:notifications:delete', '删除通知', '删除通知权限', 'api', '/api/v1/notifications', 1, 283),
('api:notifications:mark-read', '标记已读', '标记通知为已读权限', 'api', '/api/v1/notifications', 1, 284),
('api:notifications:mark-all-read', '全部标记已读', '标记所有通知为已读权限', 'api', '/api/v1/notifications/mark-all-read', 1, 285),

('api:notification-configs:read', '查看通知配置', '查看通知配置权限', 'api', '/api/v1/notification-configs', 1, 360),
('api:notification-configs:create', '创建通知配置', '创建通知配置权限', 'api', '/api/v1/notification-configs', 1, 361),
('api:notification-configs:update', '更新通知配置', '更新通知配置权限', 'api', '/api/v1/notification-configs', 1, 362),
('api:notification-configs:delete', '删除通知配置', '删除通知配置权限', 'api', '/api/v1/notification-configs', 1, 363),

('api:merchants:config', '商户配置管理', '管理商户配置权限', 'api', '/api/v1/merchants/config', 1, 244),
('api:merchants:ip-whitelist', '商户IP白名单', '管理商户IP白名单权限', 'api', '/api/v1/merchants/ip-whitelist', 1, 245),
('api:merchants:api-credentials', '商户API凭证', '获取商户API凭证权限', 'api', '/api/v1/merchants/api-credentials', 1, 246),
('api:merchants:verify-password', '验证商户密码', '验证商户密码获取API密文权限', 'api', '/api/v1/merchants/verify-password', 1, 247),
('api:merchants:reset-api-key', '重置API密钥', '重置商户API密钥权限', 'api', '/api/v1/merchants/reset-api-key', 1, 248),
('api:merchants:generate-api-key', '生成API密钥', '生成新的API密钥权限', 'api', '/api/v1/merchants/generate-api-key', 1, 249),

('api:departments:tree', '部门树结构', '获取部门树形结构权限', 'api', '/api/v1/departments/tree', 1, 254),

('api:menus:tree', '菜单树结构', '获取菜单树形结构权限', 'api', '/api/v1/menus/tree', 1, 235),

('api:roles:assign-permissions', '分配角色权限', '为角色分配权限', 'api', '/api/v1/roles/permissions', 1, 214),
('api:roles:assign-menus', '分配角色菜单', '为角色分配菜单权限', 'api', '/api/v1/roles/menus', 1, 215),

('api:users:change-password', '修改用户密码', '修改用户密码权限', 'api', '/api/v1/users/change-password', 1, 206),
('api:users:reset-password', '重置用户密码', '重置用户密码权限', 'api', '/api/v1/users/reset-password', 1, 207),

-- ========================================
-- 补充缺失的API模块权限
-- ========================================

-- 认证模块API权限 (/auth) - 补充缺失的权限
('api:/api/v1/auth', '认证API模块', '认证相关API模块权限', 'api', '/api/v1/auth', 1, 100),
('api:auth:login', '用户登录', '用户登录权限', 'api', '/api/v1/auth/login', 1, 101),
('api:auth:logout', '用户登出', '用户登出权限', 'api', '/api/v1/auth/logout', 1, 102),
('api:auth:refresh-token', '刷新令牌', '刷新访问令牌权限', 'api', '/api/v1/auth/refresh-token', 1, 103),
('api:auth:system-check', '系统检查', '系统状态检查权限', 'api', '/api/v1/auth/system-check', 1, 104),

-- 高级权限管理模块权限 (/permissions-advanced) - 补充缺失的权限
('api:/api/v1/permissions-advanced', '高级权限管理API模块', '高级权限管理相关API模块权限', 'api', '/api/v1/permissions-advanced', 1, 135),
('api:permissions-advanced:sync', '同步权限', '同步系统权限配置', 'api', '/api/v1/permissions-advanced', 1, 136),
('api:permissions-advanced:conflicts', '权限冲突检查', '检查权限冲突', 'api', '/api/v1/permissions-advanced', 1, 137),
('api:permissions-advanced:admin', '权限管理员', '高级权限管理员权限', 'api', '/api/v1/permissions-advanced', 1, 138),

-- 健康检查模块权限 (/health) - 新增
('api:/api/v1/health', '健康检查API模块', '健康检查相关API模块权限', 'api', '/api/v1/health', 1, 380),
('api:health:check', '健康检查', '系统健康检查权限', 'api', '/api/v1/health', 1, 381),

-- 通知配置模块权限 (/notification-configs) - 新增
('api:/api/v1/notification-configs', '通知配置API模块', '通知配置相关API模块权限', 'api', '/api/v1/notification-configs', 1, 230),

-- 补充缺失的详细操作权限
('api:auth:create', '认证创建', '认证相关创建操作权限', 'api', '/api/v1/auth', 1, 105),
('api:auth:read', '认证查询', '认证相关查询操作权限', 'api', '/api/v1/auth', 1, 106),
('api:auth:update', '认证更新', '认证相关更新操作权限', 'api', '/api/v1/auth', 1, 107),
('api:auth:delete', '认证删除', '认证相关删除操作权限', 'api', '/api/v1/auth', 1, 108),

('api:permissions-advanced:create', '高级权限创建', '高级权限管理创建操作', 'api', '/api/v1/permissions-advanced', 1, 139),
('api:permissions-advanced:read', '高级权限查询', '高级权限管理查询操作', 'api', '/api/v1/permissions-advanced', 1, 140),
('api:permissions-advanced:update', '高级权限更新', '高级权限管理更新操作', 'api', '/api/v1/permissions-advanced', 1, 141),
('api:permissions-advanced:delete', '高级权限删除', '高级权限管理删除操作', 'api', '/api/v1/permissions-advanced', 1, 142),

('api:system:create', '系统配置创建', '系统配置创建权限', 'api', '/api/v1/system', 1, 314),
('api:system:delete', '系统配置删除', '系统配置删除权限', 'api', '/api/v1/system', 1, 315),

('api:binding-logs:create', '绑卡日志创建', '绑卡日志创建权限', 'api', '/api/v1/binding-logs', 1, 322),
('api:binding-logs:update', '绑卡日志更新', '绑卡日志更新权限', 'api', '/api/v1/binding-logs', 1, 323),
('api:binding-logs:delete', '绑卡日志删除', '绑卡日志删除权限', 'api', '/api/v1/binding-logs', 1, 324),

('api:recovery:create', '数据恢复创建', '数据恢复创建权限', 'api', '/api/v1/recovery', 1, 333),
('api:recovery:update', '数据恢复更新', '数据恢复更新权限', 'api', '/api/v1/recovery', 1, 334),
('api:recovery:delete', '数据恢复删除', '数据恢复删除权限', 'api', '/api/v1/recovery', 1, 335),

('api:public:create', '公共接口创建', '公共接口创建权限', 'api', '/api/v1/public', 1, 342),
('api:public:update', '公共接口更新', '公共接口更新权限', 'api', '/api/v1/public', 1, 343),
('api:public:delete', '公共接口删除', '公共接口删除权限', 'api', '/api/v1/public', 1, 344),

('api:health:create', '健康检查创建', '健康检查创建权限', 'api', '/api/v1/health', 1, 382),
('api:health:read', '健康检查查询', '健康检查查询权限', 'api', '/api/v1/health', 1, 383),
('api:health:update', '健康检查更新', '健康检查更新权限', 'api', '/api/v1/health', 1, 384),
('api:health:delete', '健康检查删除', '健康检查删除权限', 'api', '/api/v1/health', 1, 385),

-- TOTP双因子认证模块权限 (/totp) - 新增
('api:/api/v1/totp', 'TOTP双因子认证API模块', 'TOTP双因子认证相关API模块权限', 'api', '/api/v1/totp', 1, 390),
('api:totp:status', '获取TOTP状态', '获取用户TOTP启用状态权限', 'api', '/api/v1/totp/status', 1, 391),
('api:totp:setup', '设置TOTP', '设置TOTP双因子认证权限', 'api', '/api/v1/totp/setup', 1, 392),
('api:totp:verify', '验证TOTP', '验证TOTP验证码权限', 'api', '/api/v1/totp/verify', 1, 393),
('api:totp:enable', '启用TOTP', '启用TOTP双因子认证权限', 'api', '/api/v1/totp/enable', 1, 394),
('api:totp:disable', '禁用TOTP', '禁用TOTP双因子认证权限', 'api', '/api/v1/totp/disable', 1, 395),
('api:totp:backup-verify', '备用码验证', '使用备用恢复码验证权限', 'api', '/api/v1/totp/backup-code/verify', 1, 396),
('api:totp:required', '检查TOTP要求', '检查是否需要TOTP验证权限', 'api', '/api/v1/totp/required', 1, 397),

-- 数据权限（用于配置角色的数据访问范围）
-- 【安全修复】：明确权限语义，防止跨商户数据泄露
('data:merchant:all', '所有商户数据', '【仅超级管理员】可以访问所有商户的数据', 'data', NULL, 1, 410),
('data:merchant:own', '本商户数据', '只能访问所属商户的数据', 'data', NULL, 1, 411),
('data:department:all', '本商户所有部门数据', '【商户范围内】可以访问本商户的所有部门数据', 'data', NULL, 1, 412),
('data:department:own', '本部门数据', '只能访问所属部门的数据', 'data', NULL, 1, 413),
('data:department:sub', '本部门及下级', '可以访问本部门及其下级部门的数据', 'data', NULL, 1, 414),
('data:user:all', '本商户所有用户数据', '【商户范围内】可以访问本商户的所有用户数据', 'data', NULL, 1, 415),
('data:user:own', '本人数据', '只能访问自己的数据', 'data', NULL, 1, 416);

-- ========================================
-- 7. 角色权限分配（基于3角色系统和最小权限原则）
-- ========================================
--
-- 权限分配说明：
-- 1. 超级管理员：拥有系统所有权限，可访问所有功能和数据
-- 2. 商户管理员：拥有商户范围内的管理权限，包括：
--    - 角色列表查询权限：可查看角色列表（已过滤超级管理员角色）
--    - 用户管理权限：可创建、编辑、删除和分配角色给商户内用户
--    - 严格商户间数据隔离：只能操作自己商户的数据
--    - 动态权限检查：所有权限检查都通过数据库配置，无硬编码
-- 3. CK供应商：仅拥有CK管理权限，严格部门间数据隔离
-- ========================================

-- 为超级管理员分配所有权限（系统最高权限）
INSERT IGNORE INTO `role_permissions` (`role_id`, `permission_id`)
SELECT r.id, p.id FROM `roles` r, `permissions` p
WHERE r.code = 'super_admin' AND p.is_enabled = 1;

-- 为商户管理员分配权限（商户范围内的管理权限，严格商户间数据隔离，不包含商户列表访问权限）
INSERT IGNORE INTO `role_permissions` (`role_id`, `permission_id`)
SELECT r.id, p.id FROM `roles` r, `permissions` p
WHERE r.code = 'merchant_admin' AND p.code IN (
    -- 菜单权限（不包含商户列表和沃尔玛API配置）
    'menu:dashboard', 'menu:merchant:department', 'menu:cards',
    'menu:walmart', 'menu:walmart:user', 'menu:notification',
    'menu:bind', 'menu:bind:single', 'menu:bind:batch', 'menu:system:user',

    -- API模块权限（包含角色管理API，用于用户管理页面的角色分配功能）
    'api:/api/v1/dashboard', 'api:/api/v1/users', 'api:/api/v1/roles', 'api:/api/v1/departments',
    'api:/api/v1/walmart-ck', 'api:/api/v1/cards', 'api:/api/v1/notifications', 'api:/api/v1/menus',
    'api:/api/v1/card-bind', 'api:/api/v1/binding-logs', 'api:/api/v1/notification-configs',
    'api:/api/v1/totp',

    -- 仪表盘权限
    'api:dashboard:read', 'api:dashboard:statistics', 'api:dashboard:summary',
    'api:dashboard:amount-statistics', 'api:dashboard:ck-efficiency',

    -- 用户管理权限：完整的CRUD操作，支持角色分配
    'api:users:read', 'api:users:create', 'api:users:update', 'api:users:delete',
    'api:users:assign-role', 'api:users:me', 'api:users:change-password', 'api:users:reset-password',

    -- 角色管理权限：查看角色列表（已过滤超级管理员），用于用户角色分配
    'api:roles:read', 'api:roles:assign-permissions', 'api:roles:assign-menus',

    -- 菜单权限：获取用户菜单（必需权限，用于前端菜单显示）
    'api:menus:user-menus', 'api:menus:read', 'api:menus:tree',

    -- 部门管理权限：完整的CRUD操作
    'api:departments:read', 'api:departments:create', 'api:departments:update', 'api:departments:delete', 'api:departments:tree',

    -- CK管理权限：完整的CRUD操作
    'api:walmart-ck:read', 'api:walmart-ck:create', 'api:walmart-ck:update', 'api:walmart-ck:delete',
    'api:walmart-ck:test', 'api:walmart-ck:statistics', 'api:walmart-ck:isolation-check',

    -- 绑卡记录权限：完整的CRUD操作和统计
    'api:cards:read', 'api:cards:create', 'api:cards:update', 'api:cards:delete', 'api:cards:retry',
    'api:cards:batch-retry', 'api:cards:statistics', 'api:cards:export', 'api:cards:today-statistics',

    -- 绑卡日志权限
    'api:binding-logs:read',

    -- 通知管理权限：查看、创建、更新和删除
    'api:notifications:read', 'api:notifications:create', 'api:notifications:update', 'api:notifications:delete',
    'api:notifications:mark-read', 'api:notifications:mark-all-read',

    -- 通知配置权限
    'api:notification-configs:read', 'api:notification-configs:create', 'api:notification-configs:update', 'api:notification-configs:delete',

    -- 绑卡操作权限：执行绑卡操作
    'api:card-bind:create', 'api:card-bind:batch', 'api:card-bind:validate',

    -- 商户配置权限（仅限自己商户）
    'api:merchants:update', 'api:merchants:config', 'api:merchants:ip-whitelist',
    'api:merchants:api-credentials', 'api:merchants:verify-password', 'api:merchants:reset-api-key',
    'api:merchants:generate-api-key',

    -- 系统管理权限（基础权限）
    'api:/api/v1/system', 'api:system:read', 'api:system:public-params',

    -- 健康检查权限
    'api:/api/v1/health', 'api:health:check', 'api:health:read',

    -- TOTP双因子认证权限（商户管理员可以管理自己的双因子认证）
    'api:totp:status', 'api:totp:setup', 'api:totp:verify', 'api:totp:enable',
    'api:totp:disable', 'api:totp:backup-verify', 'api:totp:required',

    -- 数据权限（商户管理员可以访问本商户的所有数据，严格商户间数据隔离）
    'data:merchant:own', 'data:department:all', 'data:user:all'
);

-- 为商户CK供应商分配权限（CK管理权限+通知中心权限，严格部门间数据隔离）
INSERT IGNORE INTO `role_permissions` (`role_id`, `permission_id`)
SELECT r.id, p.id FROM `roles` r, `permissions` p
WHERE r.code = 'ck_supplier' AND p.code IN (
    -- 菜单权限（CK管理相关+通知中心）
    'menu:walmart:user', 'menu:notification',

    -- API模块权限（CK相关+通知中心+TOTP）
    'api:/api/v1/walmart-ck', 'api:/api/v1/notifications', 'api:/api/v1/users', 'api:/api/v1/menus',
    'api:/api/v1/notification-configs', 'api:/api/v1/totp',

    -- CK管理权限（仅限自己部门的CK）
    'api:walmart-ck:read', 'api:walmart-ck:create', 'api:walmart-ck:update', 'api:walmart-ck:delete',
    'api:walmart-ck:test', 'api:walmart-ck:statistics',

    -- 通知管理权限（查看、创建、更新和删除，仅限自己部门）
    'api:notifications:read', 'api:notifications:create', 'api:notifications:update', 'api:notifications:delete',
    'api:notifications:mark-read', 'api:notifications:mark-all-read',

    -- 通知配置权限
    'api:notification-configs:read', 'api:notification-configs:create', 'api:notification-configs:update', 'api:notification-configs:delete',

    -- 个人信息权限（基础权限，所有用户都应该有）
    'api:users:me',

    -- 菜单权限：获取用户菜单（必需权限，用于前端菜单显示）
    'api:menus:user-menus',

    -- TOTP双因子认证权限（CK供应商可以管理自己的双因子认证）
    'api:totp:status', 'api:totp:setup', 'api:totp:verify', 'api:totp:enable',
    'api:totp:disable', 'api:totp:backup-verify', 'api:totp:required',

    -- 数据权限（CK供应商只能访问本部门数据）
    'data:department:own', 'data:user:own'
);

-- ========================================
-- 8. 角色菜单分配（基于3角色系统）
-- ========================================

-- 清理现有菜单分配

-- 为超级管理员分配所有菜单权限（系统最高权限）
INSERT IGNORE INTO `role_menus` (`role_id`, `menu_id`)
SELECT r.id, m.id
FROM `roles` r, `menus` m
WHERE r.code = 'super_admin' AND m.is_enabled = 1 AND m.is_visible = 1;

-- 为商户管理员分配指定菜单权限（商户管理范围，严格排除商户列表菜单和沃尔玛API配置）
INSERT IGNORE INTO `role_menus` (`role_id`, `menu_id`)
SELECT r.id, m.id
FROM `roles` r, `menus` m
WHERE r.code = 'merchant_admin'
AND m.code IN (
    'dashboard', 'merchant:department', 'cards',
    'walmart', 'walmart:user','system:user',
    'notification', 'bind', 'bind:single', 'bind:batch'
);

-- 为商户CK供应商分配指定菜单权限（CK管理+通知中心）
INSERT IGNORE INTO `role_menus` (`role_id`, `menu_id`)
SELECT r.id, m.id
FROM `roles` r, `menus` m
WHERE r.code = 'ck_supplier'
AND m.code IN (
    'walmart', 'walmart:user', 'notification'
);

-- ========================================
-- 9. TOTP权限补充分配（已移除 - TOTP现在是个人设置功能）
-- ========================================
-- TOTP功能已移至个人设置，所有登录用户都可访问，无需特殊权限配置

-- ========================================
-- 10. 部门数据初始化已移除
-- ========================================
--
-- 注意：部门相关的初始化数据已被移除，系统启动时仅创建超级管理员账号。
-- 部门数据将在实际使用时通过管理界面创建。
-- 这样可以确保系统的灵活性，避免预设不必要的组织结构。


-- ========================================
-- 10. 初始化系统配置
-- ========================================

-- 插入基本系统设置
INSERT IGNORE INTO `system_settings` (`key`, `value`, `description`, `type`, `is_public`) VALUES
('system_name', '沃尔玛绑卡系统', '系统名称', 'string', 1),
('system_version', '2.1.0', '系统版本', 'string', 1),
('max_bind_retry', '3', '最大绑卡重试次数', 'number', 0),
('bind_timeout', '30', '绑卡超时时间（秒）', 'number', 0),
('daily_bind_limit_default', '100', '默认每日绑卡限制', 'number', 0),
('hourly_bind_limit_default', '20', '默认每小时绑卡限制', 'number', 0),
('enable_audit_log', 'true', '是否启用审计日志', 'boolean', 0),
('enable_ip_whitelist', 'false', '是否启用IP白名单', 'boolean', 0),
('notification_retention_days', '30', '通知保留天数', 'number', 0),
('log_retention_days', '90', '日志保留天数', 'number', 0),
('ck_expire_minutes', '30', 'CK自动过期时间（分钟）', 'number', 0),
('ck_expire_check_enabled', 'true', '是否启用CK自动过期检测', 'boolean', 0),
('ck_expire_check_interval', '5', 'CK过期检测间隔（分钟）', 'number', 0);

-- ========================================
-- 11. 初始化沃尔玛服务器配置
-- ========================================

-- 插入默认的沃尔玛服务器配置
INSERT IGNORE INTO `walmart_server` (
    `api_url`, `referer`, `timeout`, `retry_count`, `daily_bind_limit`,
    `api_rate_limit`, `max_retry_times`, `bind_timeout_seconds`,
    `verification_code_expires`, `log_retention_days`,
    `enable_ip_whitelist`, `enable_security_audit`, `maintenance_mode`,
    `maintenance_message`, `is_active`, `extra_config`
) VALUES
('https://apicard.swiftpass.cn/app/card/mem/bind.json', 'https://servicewechat.com/wx81d3e1fe4c2e11b4/175/page-frame.html', 30, 3, 1000, 60, 3, 30, 300, 90, 1, 1, 0, NULL, 1, '{"server_name": "沃尔玛主服务器"}'),
('https://apicard.swiftpass.cn/app/card/mem/bind.json', 'https://servicewechat.com/wx81d3e1fe4c2e11b4/175/page-frame.html', 30, 3, 500, 30, 3, 30, 300, 90, 1, 1, 0, NULL, 0, '{"server_name": "沃尔玛备用服务器"}');

-- ========================================
-- 12. 安全清理特殊页面菜单和重复菜单（生产环境安全版本）
-- ========================================

-- 检查是否已经执行过特殊页面菜单清理
SET @special_menu_cleanup_exists = (SELECT COUNT(*) FROM `migration_logs` WHERE `migration_name` = 'special_menu_cleanup_v2.1.0');

-- 只在首次初始化或明确需要清理时执行
SET @should_cleanup_menus = (@special_menu_cleanup_exists = 0);

-- 记录清理开始
INSERT IGNORE INTO `migration_logs` (`migration_name`, `status`, `message`, `created_at`)
VALUES ('special_menu_cleanup_v2.1.0', 'started', '开始清理特殊页面菜单', NOW(3));

-- 安全删除404、403、login页面的菜单记录（仅在需要时执行）
-- 这些页面应该是路由级别的特殊页面，不应该在菜单系统中
SET @sql_cleanup_role_menus = IF(@should_cleanup_menus = 1,
    'DELETE rm FROM role_menus rm JOIN menus m ON rm.menu_id = m.id WHERE m.code IN (''error:404'', ''error:403'', ''login'');',
    'SELECT ''跳过角色菜单清理 - 已执行过清理'' as status;'
);

PREPARE stmt_cleanup_role_menus FROM @sql_cleanup_role_menus;
EXECUTE stmt_cleanup_role_menus;
DEALLOCATE PREPARE stmt_cleanup_role_menus;

SET @sql_cleanup_menus = IF(@should_cleanup_menus = 1,
    'DELETE FROM menus WHERE code IN (''error:404'', ''error:403'', ''login'');',
    'SELECT ''跳过菜单清理 - 已执行过清理'' as status;'
);

PREPARE stmt_cleanup_menus FROM @sql_cleanup_menus;
EXECUTE stmt_cleanup_menus;
DEALLOCATE PREPARE stmt_cleanup_menus;

-- 记录清理完成
UPDATE `migration_logs`
SET `status` = 'completed',
    `message` = '特殊页面菜单清理完成',
    `completed_at` = NOW(3),
    `data_summary` = JSON_OBJECT(
        'cleanup_executed', @should_cleanup_menus,
        'remaining_special_menus', (SELECT COUNT(*) FROM `menus` WHERE `code` IN ('error:404', 'error:403', 'login')),
        'completion_timestamp', NOW(3)
    )
WHERE `migration_name` = 'special_menu_cleanup_v2.1.0';


-- ========================================
-- 13. 数据初始化完成标记
-- ========================================

-- 创建迁移日志表（如果不存在）
CREATE TABLE IF NOT EXISTS `migration_logs` (
    `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '日志ID',
    `migration_name` varchar(100) NOT NULL COMMENT '迁移名称',
    `status` varchar(20) NOT NULL DEFAULT 'started' COMMENT '状态',
    `message` text NULL COMMENT '消息',
    `data_summary` text NULL COMMENT '数据摘要',
    `created_at` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '创建时间',
    `completed_at` datetime(3) NULL COMMENT '完成时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_migration_logs_name` (`migration_name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='迁移日志表';

-- 记录数据初始化完成（简化权限系统版本）
INSERT IGNORE INTO `migration_logs` (
    `migration_name`, `status`, `message`, `data_summary`, `created_at`, `completed_at`
)
SELECT
    'walmart_system_simplified_permission_initialization' as `migration_name`,
    'completed' as `status`,
    '沃尔玛绑卡系统简化权限系统初始化完成 - 3角色系统' as `message`,
    JSON_OBJECT(
        'merchants_count', (SELECT COUNT(*) FROM `merchants`),
        'users_count', (SELECT COUNT(*) FROM `users`),
        'roles_count', (SELECT COUNT(*) FROM `roles`),
        'menus_count', (SELECT COUNT(*) FROM `menus`),
        'permissions_count', (SELECT COUNT(*) FROM `permissions`),
        'departments_count', (SELECT COUNT(*) FROM `departments`),
        'role_permissions_count', (SELECT COUNT(*) FROM `role_permissions`),
        'role_menus_count', (SELECT COUNT(*) FROM `role_menus`),
        'permission_system_version', '3-role-simplified',
        'roles_list', JSON_ARRAY('super_admin', 'merchant_admin', 'ck_supplier'),
        'initialization_timestamp', NOW(3)
    ) as `data_summary`,
    NOW(3) as `created_at`,
    NOW(3) as `completed_at`
WHERE NOT EXISTS (
    SELECT 1 FROM `migration_logs`
    WHERE `migration_name` = 'walmart_system_simplified_permission_initialization'
);

-- ========================================
-- 14. 通知系统初始化数据
-- ========================================

-- 为超级管理员创建欢迎通知
INSERT IGNORE INTO `notifications` (
    `user_id`, `title`, `content`, `type`, `status`, `is_important`,
    `created_at`, `updated_at`
) VALUES
(1, '欢迎使用沃尔玛绑卡系统', '欢迎使用沃尔玛绑卡系统！您已成功登录超级管理员账户。', 'system', 'unread', 1, NOW(3), NOW(3)),
(1, '系统初始化完成', '沃尔玛绑卡系统已完成初始化，所有功能模块已就绪。', 'system', 'unread', 0, NOW(3), NOW(3));


-- 为所有用户创建默认通知配置
INSERT IGNORE INTO `notification_configs` (
    `user_id`, `type`, `enabled`, `email_enabled`, `sms_enabled`,
    `created_at`, `updated_at`
)
SELECT
    u.id,
    'system' as type,
    1 as enabled,
    0 as email_enabled,
    0 as sms_enabled,
    NOW(3) as created_at,
    NOW(3) as updated_at
FROM `users` u;

INSERT IGNORE INTO `notification_configs` (
    `user_id`, `type`, `enabled`, `email_enabled`, `sms_enabled`,
    `created_at`, `updated_at`
)
SELECT
    u.id,
    'card' as type,
    1 as enabled,
    0 as email_enabled,
    0 as sms_enabled,
    NOW(3) as created_at,
    NOW(3) as updated_at
FROM `users` u;

INSERT IGNORE INTO `notification_configs` (
    `user_id`, `type`, `enabled`, `email_enabled`, `sms_enabled`,
    `created_at`, `updated_at`
)
SELECT
    u.id,
    'merchant' as type,
    1 as enabled,
    0 as email_enabled,
    0 as sms_enabled,
    NOW(3) as created_at,
    NOW(3) as updated_at
FROM `users` u;

INSERT IGNORE INTO `notification_configs` (
    `user_id`, `type`, `enabled`, `email_enabled`, `sms_enabled`,
    `created_at`, `updated_at`
)
SELECT
    u.id,
    'user' as type,
    1 as enabled,
    0 as email_enabled,
    0 as sms_enabled,
    NOW(3) as created_at,
    NOW(3) as updated_at
FROM `users` u;

-- ========================================
-- 15. API权限完整性验证
-- ========================================

-- 创建权限验证视图，用于检查权限配置完整性
CREATE OR REPLACE VIEW `v_permission_coverage` AS
SELECT
    'API模块权限' as category,
    COUNT(*) as total_count,
    GROUP_CONCAT(DISTINCT SUBSTRING_INDEX(code, ':', 1) ORDER BY code) as items
FROM permissions
WHERE resource_type = 'api' AND code LIKE 'api:/api/v1/%'
UNION ALL
SELECT
    '详细操作权限' as category,
    COUNT(*) as total_count,
    GROUP_CONCAT(DISTINCT SUBSTRING_INDEX(code, ':', 2) ORDER BY code) as items
FROM permissions
WHERE resource_type = 'api' AND code LIKE 'api:%:%' AND code NOT LIKE 'api:/api/v1/%'
UNION ALL
SELECT
    '菜单权限' as category,
    COUNT(*) as total_count,
    GROUP_CONCAT(DISTINCT code ORDER BY code) as items
FROM permissions
WHERE resource_type = 'menu'
UNION ALL
SELECT
    '数据权限' as category,
    COUNT(*) as total_count,
    GROUP_CONCAT(DISTINCT code ORDER BY code) as items
FROM permissions
WHERE resource_type = 'data';

-- 创建角色权限分配统计视图
CREATE OR REPLACE VIEW `v_role_permission_stats` AS
SELECT
    r.name as role_name,
    r.code as role_code,
    COUNT(rp.permission_id) as permission_count,
    COUNT(CASE WHEN p.resource_type = 'menu' THEN 1 END) as menu_permissions,
    COUNT(CASE WHEN p.resource_type = 'api' THEN 1 END) as api_permissions,
    COUNT(CASE WHEN p.resource_type = 'data' THEN 1 END) as data_permissions
FROM roles r
LEFT JOIN role_permissions rp ON r.id = rp.role_id
LEFT JOIN permissions p ON rp.permission_id = p.id
GROUP BY r.id, r.name, r.code
ORDER BY r.id;

-- 插入权限配置完成标记
INSERT IGNORE INTO `migration_logs` (
    `migration_name`, `status`, `message`, `data_summary`, `created_at`, `completed_at`
)
SELECT
    'api_permission_enhancement_2024' as `migration_name`,
    'completed' as `status`,
    'API权限配置完善完成 - 补充所有缺失的API权限' as `message`,
    JSON_OBJECT(
        'total_permissions', (SELECT COUNT(*) FROM `permissions`),
        'api_permissions', (SELECT COUNT(*) FROM `permissions` WHERE resource_type = 'api'),
        'menu_permissions', (SELECT COUNT(*) FROM `permissions` WHERE resource_type = 'menu'),
        'data_permissions', (SELECT COUNT(*) FROM `permissions` WHERE resource_type = 'data'),
        'super_admin_permissions', (SELECT COUNT(*) FROM `role_permissions` rp JOIN `roles` r ON rp.role_id = r.id WHERE r.code = 'super_admin'),
        'merchant_admin_permissions', (SELECT COUNT(*) FROM `role_permissions` rp JOIN `roles` r ON rp.role_id = r.id WHERE r.code = 'merchant_admin'),
        'ck_supplier_permissions', (SELECT COUNT(*) FROM `role_permissions` rp JOIN `roles` r ON rp.role_id = r.id WHERE r.code = 'ck_supplier'),
        'enhancement_timestamp', NOW(3)
    ) as `data_summary`,
    NOW(3) as `created_at`,
    NOW(3) as `completed_at`
WHERE NOT EXISTS (
    SELECT 1 FROM `migration_logs`
    WHERE `migration_name` = 'api_permission_enhancement_2024'
);

-- ========================================
-- 沃尔玛绑卡系统数据初始化完成
-- ========================================