<template>
  <div class="enhanced-audit">
    <!-- 页面头部 -->
    <div class="page-header">
      <h1 class="page-title">增强安全审计</h1>
      <div class="header-actions">
        <el-button type="primary" :icon="Refresh" @click="refreshData" :loading="loading">
          刷新数据
        </el-button>
        <el-button type="success" :icon="Download" @click="exportAuditReport">
          导出报告
        </el-button>
      </div>
    </div>

    <!-- 审计概览卡片 -->
    <div class="overview-section">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-card shadow="hover" class="overview-card">
            <div class="card-content">
              <div class="card-icon success">
                <el-icon><DataAnalysis /></el-icon>
              </div>
              <div class="card-info">
                <div class="card-title">总事件数</div>
                <div class="card-value">{{ formatNumber(analytics.event_statistics?.total_events || 0) }}</div>
                <div class="card-desc">过去30天</div>
              </div>
            </div>
          </el-card>
        </el-col>
        
        <el-col :span="6">
          <el-card shadow="hover" class="overview-card">
            <div class="card-content">
              <div class="card-icon warning">
                <el-icon><Warning /></el-icon>
              </div>
              <div class="card-info">
                <div class="card-title">高风险事件</div>
                <div class="card-value">{{ analytics.risk_analysis?.high_risk_events || 0 }}</div>
                <div class="card-desc">风险率: {{ analytics.risk_analysis?.risk_ratio || 0 }}%</div>
              </div>
            </div>
          </el-card>
        </el-col>
        
        <el-col :span="6">
          <el-card shadow="hover" class="overview-card">
            <div class="card-content">
              <div class="card-icon danger">
                <el-icon><Monitor /></el-icon>
              </div>
              <div class="card-info">
                <div class="card-title">异常检测</div>
                <div class="card-value">{{ analytics.anomaly_detection?.total_anomalies || 0 }}</div>
                <div class="card-desc">需要关注</div>
              </div>
            </div>
          </el-card>
        </el-col>
        
        <el-col :span="6">
          <el-card shadow="hover" class="overview-card">
            <div class="card-content">
              <div class="card-icon primary">
                <el-icon><User /></el-icon>
              </div>
              <div class="card-info">
                <div class="card-title">活跃用户</div>
                <div class="card-value">{{ analytics.user_activity?.most_active_users?.length || 0 }}</div>
                <div class="card-desc">最近活跃</div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 审计分析图表 -->
    <div class="charts-section">
      <el-row :gutter="20">
        <!-- 事件趋势图 -->
        <el-col :span="12">
          <el-card shadow="never" class="chart-card">
            <template #header>
              <span>事件趋势分析</span>
            </template>
            <div ref="trendChart" class="chart-container" v-loading="chartLoading"></div>
          </el-card>
        </el-col>

        <!-- 风险分布图 -->
        <el-col :span="12">
          <el-card shadow="never" class="chart-card">
            <template #header>
              <span>风险级别分布</span>
            </template>
            <div ref="riskChart" class="chart-container" v-loading="chartLoading"></div>
          </el-card>
        </el-col>
      </el-row>

      <el-row :gutter="20" style="margin-top: 20px;">
        <!-- 用户活动热力图 -->
        <el-col :span="12">
          <el-card shadow="never" class="chart-card">
            <template #header>
              <span>用户活动热力图</span>
            </template>
            <div ref="activityChart" class="chart-container" v-loading="chartLoading"></div>
          </el-card>
        </el-col>

        <!-- 资源访问统计 -->
        <el-col :span="12">
          <el-card shadow="never" class="chart-card">
            <template #header>
              <span>资源访问统计</span>
            </template>
            <div ref="resourceChart" class="chart-container" v-loading="chartLoading"></div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 异常检测结果 -->
    <el-card shadow="never" style="margin-top: 20px;" v-if="analytics.anomaly_detection?.anomalies?.length > 0">
      <template #header>
        <div class="card-header">
          <span>异常检测结果</span>
          <el-tag type="danger" size="small">{{ analytics.anomaly_detection.total_anomalies }} 个异常</el-tag>
        </div>
      </template>

      <el-table :data="analytics.anomaly_detection.anomalies" style="width: 100%">
        <el-table-column prop="type" label="异常类型" width="150">
          <template #default="{ row }">
            <el-tag :type="getAnomalyType(row.type)" size="small">{{ getAnomalyLabel(row.type) }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="timestamp" label="发生时间" width="180">
          <template #default="{ row }">
            {{ formatTime(row.timestamp) }}
          </template>
        </el-table-column>
        <el-table-column prop="user_id" label="用户ID" width="100" />
        <el-table-column prop="details" label="详细信息" min-width="200">
          <template #default="{ row }">
            <span v-if="row.risk_score">风险分数: {{ row.risk_score }}</span>
            <span v-else-if="row.details">{{ Array.isArray(row.details) ? row.details.join(', ') : row.details }}</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="120">
          <template #default="{ row }">
            <el-button type="text" @click="viewAnomalyDetail(row)" size="small">查看详情</el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <!-- 最活跃用户 -->
    <el-card shadow="never" style="margin-top: 20px;">
      <template #header>
        <span>最活跃用户</span>
      </template>

      <el-table :data="analytics.user_activity?.most_active_users || []" style="width: 100%">
        <el-table-column prop="user_id" label="用户ID" width="100" />
        <el-table-column prop="activity_count" label="活动次数" width="120" />
        <el-table-column label="活动强度" width="200">
          <template #default="{ row }">
            <el-progress 
              :percentage="getActivityPercentage(row.activity_count)" 
              :color="getActivityColor(row.activity_count)"
            />
          </template>
        </el-table-column>
        <el-table-column label="操作" width="120">
          <template #default="{ row }">
            <el-button type="text" @click="viewUserActivity(row.user_id)" size="small">查看详情</el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <!-- 审计配置 -->
    <el-card shadow="never" style="margin-top: 20px;">
      <template #header>
        <div class="card-header">
          <span>审计配置</span>
          <el-button type="primary" size="small" @click="showConfigDialog">配置审计规则</el-button>
        </div>
      </template>

      <div class="audit-config">
        <el-row :gutter="20">
          <el-col :span="8">
            <div class="config-item">
              <span class="config-label">会话监控</span>
              <el-switch v-model="auditConfig.sessionMonitoring" @change="updateConfig" />
            </div>
          </el-col>
          <el-col :span="8">
            <div class="config-item">
              <span class="config-label">配置变更监控</span>
              <el-switch v-model="auditConfig.configChangeMonitoring" @change="updateConfig" />
            </div>
          </el-col>
          <el-col :span="8">
            <div class="config-item">
              <span class="config-label">数据访问监控</span>
              <el-switch v-model="auditConfig.dataAccessMonitoring" @change="updateConfig" />
            </div>
          </el-col>
        </el-row>
        
        <el-row :gutter="20" style="margin-top: 15px;">
          <el-col :span="8">
            <div class="config-item">
              <span class="config-label">资源使用监控</span>
              <el-switch v-model="auditConfig.resourceMonitoring" @change="updateConfig" />
            </div>
          </el-col>
          <el-col :span="8">
            <div class="config-item">
              <span class="config-label">第三方API监控</span>
              <el-switch v-model="auditConfig.apiMonitoring" @change="updateConfig" />
            </div>
          </el-col>
          <el-col :span="8">
            <div class="config-item">
              <span class="config-label">实时告警</span>
              <el-switch v-model="auditConfig.realTimeAlerts" @change="updateConfig" />
            </div>
          </el-col>
        </el-row>
      </div>
    </el-card>

    <!-- 异常详情对话框 -->
    <el-dialog v-model="anomalyDialogVisible" title="异常详情" width="600px">
      <div v-if="selectedAnomaly" class="anomaly-detail">
        <el-descriptions :column="1" border>
          <el-descriptions-item label="异常类型">{{ getAnomalyLabel(selectedAnomaly.type) }}</el-descriptions-item>
          <el-descriptions-item label="发生时间">{{ formatTime(selectedAnomaly.timestamp) }}</el-descriptions-item>
          <el-descriptions-item label="用户ID">{{ selectedAnomaly.user_id }}</el-descriptions-item>
          <el-descriptions-item label="风险分数" v-if="selectedAnomaly.risk_score">{{ selectedAnomaly.risk_score }}</el-descriptions-item>
          <el-descriptions-item label="详细信息">
            <pre>{{ JSON.stringify(selectedAnomaly.details, null, 2) }}</pre>
          </el-descriptions-item>
        </el-descriptions>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import { 
  Refresh, Download, DataAnalysis, Warning, Monitor, User 
} from '@element-plus/icons-vue'
import * as echarts from 'echarts'

// 响应式数据
const loading = ref(false)
const chartLoading = ref(false)
const anomalyDialogVisible = ref(false)
const selectedAnomaly = ref(null)

// 图表引用
const trendChart = ref()
const riskChart = ref()
const activityChart = ref()
const resourceChart = ref()

// 图表实例
let trendChartInstance = null
let riskChartInstance = null
let activityChartInstance = null
let resourceChartInstance = null

// 审计分析数据
const analytics = ref({})

// 审计配置
const auditConfig = reactive({
  sessionMonitoring: true,
  configChangeMonitoring: true,
  dataAccessMonitoring: true,
  resourceMonitoring: false,
  apiMonitoring: false,
  realTimeAlerts: true
})

// 工具方法
const formatNumber = (num) => {
  if (num >= 1000000) {
    return (num / 1000000).toFixed(1) + 'M'
  } else if (num >= 1000) {
    return (num / 1000).toFixed(1) + 'K'
  }
  return num.toString()
}

const formatTime = (timestamp) => {
  return new Date(timestamp).toLocaleString()
}

const getAnomalyType = (type) => {
  const types = {
    'session_anomaly': 'warning',
    'high_risk_access': 'danger',
    'config_change': 'info',
    'resource_abuse': 'danger'
  }
  return types[type] || 'info'
}

const getAnomalyLabel = (type) => {
  const labels = {
    'session_anomaly': '会话异常',
    'high_risk_access': '高风险访问',
    'config_change': '配置变更',
    'resource_abuse': '资源滥用'
  }
  return labels[type] || type
}

const getActivityPercentage = (count) => {
  const maxActivity = Math.max(...(analytics.value.user_activity?.most_active_users?.map(u => u.activity_count) || [1]))
  return Math.round((count / maxActivity) * 100)
}

const getActivityColor = (count) => {
  if (count > 1000) return '#F56C6C'
  if (count > 500) return '#E6A23C'
  if (count > 100) return '#409EFF'
  return '#67C23A'
}

// 数据获取方法
const fetchAuditAnalytics = async () => {
  try {
    loading.value = true
    // 模拟API调用
    const response = await new Promise(resolve => {
      setTimeout(() => {
        resolve({
          event_statistics: {
            total_events: 15420,
            by_event_type: {
              'ACCESS': 8500,
              'OPERATION': 4200,
              'SYSTEM': 2720
            },
            by_level: {
              'INFO': 12000,
              'WARNING': 2800,
              'ERROR': 520,
              'CRITICAL': 100
            }
          },
          risk_analysis: {
            high_risk_events: 620,
            total_events: 15420,
            risk_ratio: 4.02,
            risk_trend: [
              { date: '2024-01-01', high_risk_count: 45 },
              { date: '2024-01-02', high_risk_count: 52 },
              { date: '2024-01-03', high_risk_count: 38 },
              { date: '2024-01-04', high_risk_count: 67 },
              { date: '2024-01-05', high_risk_count: 71 },
              { date: '2024-01-06', high_risk_count: 59 },
              { date: '2024-01-07', high_risk_count: 63 }
            ]
          },
          user_activity: {
            most_active_users: [
              { user_id: 1001, activity_count: 1250 },
              { user_id: 1002, activity_count: 980 },
              { user_id: 1003, activity_count: 756 },
              { user_id: 1004, activity_count: 642 },
              { user_id: 1005, activity_count: 589 }
            ],
            hourly_distribution: {
              '0': 45, '1': 32, '2': 28, '3': 25, '4': 30, '5': 42,
              '6': 85, '7': 156, '8': 245, '9': 320, '10': 380, '11': 420,
              '12': 350, '13': 380, '14': 410, '15': 390, '16': 360, '17': 280,
              '18': 180, '19': 120, '20': 95, '21': 75, '22': 60, '23': 50
            }
          },
          resource_access: {
            by_resource_type: {
              'USER': 4500,
              'MERCHANT': 3200,
              'ROLE': 1800,
              'PERMISSION': 1200,
              'CONFIGURATION': 800
            },
            by_action: {
              'VIEW': 6800,
              'CREATE': 2100,
              'UPDATE': 1900,
              'DELETE': 450
            }
          },
          anomaly_detection: {
            total_anomalies: 23,
            anomalies: [
              {
                type: 'session_anomaly',
                timestamp: '2024-01-07T14:30:00Z',
                user_id: 1001,
                details: ['multiple_concurrent_sessions', 'extended_session_timeout']
              },
              {
                type: 'high_risk_access',
                timestamp: '2024-01-07T13:45:00Z',
                user_id: 1002,
                risk_score: 85
              },
              {
                type: 'session_anomaly',
                timestamp: '2024-01-07T12:20:00Z',
                user_id: 1003,
                details: ['multiple_concurrent_sessions']
              }
            ]
          },
          trends: {
            daily_statistics: [
              { date: '2024-01-01', event_count: 2100 },
              { date: '2024-01-02', event_count: 2250 },
              { date: '2024-01-03', event_count: 1980 },
              { date: '2024-01-04', event_count: 2400 },
              { date: '2024-01-05', event_count: 2350 },
              { date: '2024-01-06', event_count: 2180 },
              { date: '2024-01-07', event_count: 2160 }
            ],
            trend_direction: 'stable'
          }
        })
      }, 1000)
    })

    analytics.value = response
  } catch (error) {
    console.error('获取审计分析数据失败:', error)
    ElMessage.error('获取审计分析数据失败')
  } finally {
    loading.value = false
  }
}

// 图表初始化方法
const initTrendChart = () => {
  if (trendChartInstance && analytics.value.trends) {
    const data = analytics.value.trends.daily_statistics
    trendChartInstance.setOption({
      title: {
        text: '事件趋势分析',
        left: 'center'
      },
      tooltip: {
        trigger: 'axis'
      },
      xAxis: {
        type: 'category',
        data: data.map(item => item.date)
      },
      yAxis: {
        type: 'value',
        name: '事件数量'
      },
      series: [
        {
          type: 'line',
          data: data.map(item => item.event_count),
          smooth: true,
          itemStyle: { color: '#409EFF' },
          areaStyle: {
            color: {
              type: 'linear',
              x: 0, y: 0, x2: 0, y2: 1,
              colorStops: [
                { offset: 0, color: 'rgba(64, 158, 255, 0.3)' },
                { offset: 1, color: 'rgba(64, 158, 255, 0.1)' }
              ]
            }
          }
        }
      ]
    })
  }
}

const initRiskChart = () => {
  if (riskChartInstance && analytics.value.event_statistics) {
    const levelData = analytics.value.event_statistics.by_level
    riskChartInstance.setOption({
      title: {
        text: '风险级别分布',
        left: 'center'
      },
      tooltip: {
        trigger: 'item'
      },
      series: [
        {
          type: 'pie',
          radius: '60%',
          data: [
            { value: levelData.INFO, name: '信息', itemStyle: { color: '#67C23A' } },
            { value: levelData.WARNING, name: '警告', itemStyle: { color: '#E6A23C' } },
            { value: levelData.ERROR, name: '错误', itemStyle: { color: '#F56C6C' } },
            { value: levelData.CRITICAL, name: '严重', itemStyle: { color: '#909399' } }
          ],
          emphasis: {
            itemStyle: {
              shadowBlur: 10,
              shadowOffsetX: 0,
              shadowColor: 'rgba(0, 0, 0, 0.5)'
            }
          }
        }
      ]
    })
  }
}

const initActivityChart = () => {
  if (activityChartInstance && analytics.value.user_activity) {
    const hourlyData = analytics.value.user_activity.hourly_distribution
    const hours = Object.keys(hourlyData).map(h => h + ':00')
    const values = Object.values(hourlyData)

    activityChartInstance.setOption({
      title: {
        text: '用户活动热力图',
        left: 'center'
      },
      tooltip: {
        trigger: 'axis'
      },
      xAxis: {
        type: 'category',
        data: hours
      },
      yAxis: {
        type: 'value',
        name: '活动次数'
      },
      series: [
        {
          type: 'bar',
          data: values,
          itemStyle: {
            color: (params) => {
              const colors = ['#91cc75', '#fac858', '#ee6666', '#73c0de']
              return colors[Math.floor(params.dataIndex / 6) % colors.length]
            }
          }
        }
      ]
    })
  }
}

const initResourceChart = () => {
  if (resourceChartInstance && analytics.value.resource_access) {
    const resourceData = analytics.value.resource_access.by_resource_type

    resourceChartInstance.setOption({
      title: {
        text: '资源访问统计',
        left: 'center'
      },
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'shadow'
        }
      },
      xAxis: {
        type: 'category',
        data: Object.keys(resourceData)
      },
      yAxis: {
        type: 'value',
        name: '访问次数'
      },
      series: [
        {
          type: 'bar',
          data: Object.values(resourceData),
          itemStyle: {
            color: '#409EFF'
          }
        }
      ]
    })
  }
}

// 事件处理方法
const refreshData = async () => {
  await fetchAuditAnalytics()
  initTrendChart()
  initRiskChart()
  initActivityChart()
  initResourceChart()
}

const exportAuditReport = () => {
  const reportData = {
    timestamp: new Date().toISOString(),
    analytics: analytics.value,
    config: auditConfig
  }

  const blob = new Blob([JSON.stringify(reportData, null, 2)], { type: 'application/json' })
  const url = URL.createObjectURL(blob)
  const a = document.createElement('a')
  a.href = url
  a.download = `enhanced-audit-report-${new Date().toISOString().split('T')[0]}.json`
  a.click()
  URL.revokeObjectURL(url)
  ElMessage.success('审计报告已导出')
}

const viewAnomalyDetail = (anomaly) => {
  selectedAnomaly.value = anomaly
  anomalyDialogVisible.value = true
}

const viewUserActivity = (userId) => {
  // 跳转到用户详细活动页面
  ElMessage.info(`查看用户 ${userId} 的详细活动`)
}

const showConfigDialog = () => {
  ElMessage.info('审计规则配置功能开发中...')
}

const updateConfig = () => {
  ElMessage.success('审计配置已更新')
}

// 生命周期
onMounted(async () => {
  await nextTick()

  // 初始化图表
  if (trendChart.value) {
    trendChartInstance = echarts.init(trendChart.value)
  }
  if (riskChart.value) {
    riskChartInstance = echarts.init(riskChart.value)
  }
  if (activityChart.value) {
    activityChartInstance = echarts.init(activityChart.value)
  }
  if (resourceChart.value) {
    resourceChartInstance = echarts.init(resourceChart.value)
  }

  // 加载数据
  await refreshData()

  // 窗口大小变化时重新调整图表
  window.addEventListener('resize', () => {
    trendChartInstance?.resize()
    riskChartInstance?.resize()
    activityChartInstance?.resize()
    resourceChartInstance?.resize()
  })
})
</script>

<style scoped>
.enhanced-audit {
  padding: 20px;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  background: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.page-title {
  font-size: 24px;
  font-weight: bold;
  margin: 0;
  color: #333;
}

.header-actions {
  display: flex;
  gap: 10px;
}

.overview-section {
  margin-bottom: 20px;
}

.overview-card {
  height: 120px;
}

.card-content {
  display: flex;
  align-items: center;
  height: 100%;
}

.card-icon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 15px;
  font-size: 24px;
  color: white;
}

.card-icon.success {
  background: linear-gradient(135deg, #67C23A, #85CE61);
}

.card-icon.primary {
  background: linear-gradient(135deg, #409EFF, #66B1FF);
}

.card-icon.warning {
  background: linear-gradient(135deg, #E6A23C, #EEBE77);
}

.card-icon.danger {
  background: linear-gradient(135deg, #F56C6C, #F78989);
}

.card-info {
  flex: 1;
}

.card-title {
  font-size: 14px;
  color: #666;
  margin-bottom: 5px;
}

.card-value {
  font-size: 24px;
  font-weight: bold;
  margin-bottom: 5px;
  color: #333;
}

.card-desc {
  font-size: 12px;
  color: #999;
}

.charts-section {
  margin-bottom: 20px;
}

.chart-card {
  height: 400px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.chart-container {
  height: 320px;
  width: 100%;
}

.audit-config {
  padding: 10px 0;
}

.config-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 0;
}

.config-label {
  font-weight: 500;
  color: #333;
}

.anomaly-detail pre {
  background: #f5f5f5;
  padding: 10px;
  border-radius: 4px;
  font-size: 12px;
  max-height: 200px;
  overflow: auto;
}

:deep(.el-card__body) {
  padding: 20px;
}

:deep(.el-card__header) {
  padding: 15px 20px;
  border-bottom: 1px solid #f0f0f0;
}

:deep(.el-table) {
  font-size: 12px;
}

:deep(.el-descriptions__label) {
  font-weight: bold;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .overview-section .el-col {
    margin-bottom: 20px;
  }

  .charts-section .el-col {
    margin-bottom: 20px;
  }
}

@media (max-width: 768px) {
  .enhanced-audit {
    padding: 10px;
  }

  .page-header {
    flex-direction: column;
    gap: 15px;
    text-align: center;
  }

  .chart-card {
    height: 300px;
  }

  .chart-container {
    height: 220px;
  }
}
</style>
