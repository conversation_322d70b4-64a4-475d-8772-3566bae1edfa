from apscheduler.schedulers.asyncio import AsyncIOScheduler
from apscheduler.triggers.interval import IntervalTrigger

from app.core.logging import get_logger
from app.db.session import SessionLocal
from app.services.recovery_service import create_recovery_service
from app.services.ck_expire_service import create_ck_expire_service
from app.models.user import User

logger = get_logger("scheduler_service")

class SchedulerService:
    """调度服务 - 管理定时任务"""

    def __init__(self):
        """初始化调度服务"""
        self.scheduler = AsyncIOScheduler()
        self.initialized = False
        self.jobs = {}

    def initialize(self):
        """初始化调度器"""
        if self.initialized:
            return

        # 启动调度器
        self.scheduler.start()
        self.initialized = True
        logger.info("调度器已启动")

        # 添加默认任务
        self._add_default_jobs()

    def _add_default_jobs(self):
        """添加默认任务"""
        # 添加卡住请求恢复任务 - 每10分钟执行一次
        self.add_job(
            job_id="stuck_requests_recovery",
            func=self._run_stuck_requests_recovery,
            trigger=IntervalTrigger(minutes=10),
            replace_existing=True,
        )
        logger.info("已添加卡住请求恢复任务，每10分钟执行一次")

        # 添加CK过期检测任务 - 根据配置的间隔执行
        self.add_job(
            job_id="ck_expire_check",
            func=self._run_ck_expire_check,
            trigger=IntervalTrigger(minutes=5),  # 默认5分钟，实际间隔从配置读取
            replace_existing=True,
        )
        logger.info("已添加CK过期检测任务，每5分钟执行一次")

    def add_job(self, job_id: str, func, trigger, replace_existing: bool = True, **kwargs):
        """添加任务"""
        if not self.initialized:
            self.initialize()

        job = self.scheduler.add_job(
            func=func,
            trigger=trigger,
            id=job_id,
            replace_existing=replace_existing,
            **kwargs
        )
        self.jobs[job_id] = job
        return job

    def remove_job(self, job_id: str):
        """移除任务"""
        if not self.initialized:
            return

        if job_id in self.jobs:
            self.scheduler.remove_job(job_id)
            del self.jobs[job_id]
            return True
        return False

    def get_jobs(self):
        """获取所有任务"""
        if not self.initialized:
            return []

        return self.scheduler.get_jobs()

    def shutdown(self):
        """关闭调度器"""
        if not self.initialized:
            return

        try:
            self.scheduler.shutdown(wait=False)
            self.initialized = False
            self.jobs.clear()
            logger.info("调度器已关闭")
        except Exception as e:
            logger.error(f"关闭调度器时出错: {str(e)}", exc_info=True)

    async def _run_stuck_requests_recovery(self):
        """运行卡住请求恢复任务"""
        logger.info("开始执行卡住请求恢复任务")
        try:
            # 创建数据库会话
            db = SessionLocal()
            try:
                # 创建恢复服务实例
                recovery_service = create_recovery_service(db)

                # 创建系统用户（用于定时任务）
                system_user = self._create_system_user()

                # 执行恢复处理
                results = await recovery_service.batch_reprocess_stuck_requests(
                    current_user=system_user,
                    hours_threshold=24,  # 处理24小时内的请求
                    batch_size=50  # 每批处理50个请求
                )

                # 通知商家
                if results["total"] > 0:
                    await recovery_service.notify_merchants(system_user, results)

                logger.info(
                    f"卡住请求恢复任务完成: 总数={results['total']}, "
                    f"成功={results['success']}, 失败={results['failed']}"
                )
            finally:
                db.close()
        except Exception as e:
            logger.error(f"卡住请求恢复任务异常: {str(e)}", exc_info=True)

    async def _run_ck_expire_check(self):
        """运行CK过期检测任务"""
        logger.info("开始执行CK过期检测任务")
        try:
            # 创建数据库会话
            db = SessionLocal()
            try:
                # 创建CK过期服务实例
                ck_expire_service = create_ck_expire_service(db)

                # 获取配置信息
                config = ck_expire_service.get_expire_config()

                # 检查是否启用过期检测
                if not config['check_enabled']:
                    logger.info("CK过期检测已禁用，跳过执行")
                    return

                # 执行批量过期处理
                results = ck_expire_service.batch_expire_cks()

                # 记录处理结果
                if results['total_found'] > 0:
                    logger.info(
                        f"CK过期检测任务完成: 找到={results['total_found']}, "
                        f"处理成功={results['total_processed']}, "
                        f"处理失败={results['total_failed']}, "
                        f"过期时间={config['expire_minutes']}分钟"
                    )
                else:
                    logger.debug("CK过期检测任务完成: 未找到过期的CK")

                # 动态调整任务执行间隔
                self._update_ck_expire_job_interval(config['check_interval'])

            finally:
                db.close()
        except Exception as e:
            logger.error(f"CK过期检测任务异常: {str(e)}", exc_info=True)

    def _update_ck_expire_job_interval(self, interval_minutes: int):
        """
        动态更新CK过期检测任务的执行间隔

        Args:
            interval_minutes: 新的执行间隔（分钟）
        """
        try:
            if "ck_expire_check" in self.jobs:
                current_job = self.jobs["ck_expire_check"]
                current_interval = None

                # 获取当前间隔
                if hasattr(current_job.trigger, 'interval'):
                    current_interval = current_job.trigger.interval.total_seconds() / 60

                # 如果间隔发生变化，更新任务
                if current_interval != interval_minutes:
                    logger.info(
                        f"更新CK过期检测任务间隔: {current_interval}分钟 -> {interval_minutes}分钟"
                    )

                    # 重新添加任务（会替换现有任务）
                    self.add_job(
                        job_id="ck_expire_check",
                        func=self._run_ck_expire_check,
                        trigger=IntervalTrigger(minutes=interval_minutes),
                        replace_existing=True,
                    )
        except Exception as e:
            logger.error(f"更新CK过期检测任务间隔失败: {e}")

    def _create_system_user(self):
        """创建系统用户（用于定时任务）"""
        from unittest.mock import Mock

        # 创建一个模拟的系统用户，具有超级管理员权限
        system_user = Mock()
        system_user.id = 0
        system_user.username = "system_scheduler"
        system_user.is_superuser = True
        system_user.merchant_id = None
        system_user.department_id = None

        return system_user

# 创建服务实例
scheduler_service = SchedulerService()
