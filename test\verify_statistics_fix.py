#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证CK删除统计数据一致性修复效果的简化脚本
"""

import requests
import json
import uuid
import time

BASE_URL = "http://localhost:20000/api/v1"

def login():
    """登录获取token"""
    login_data = {
        'username': 'admin',
        'password': '7c222fb2927d828af22f592134e8932480637c0d'
    }
    
    response = requests.post(
        f"{BASE_URL}/auth/login",
        data=login_data,
        headers={'Content-Type': 'application/x-www-form-urlencoded'}
    )
    
    if response.status_code == 200:
        token = response.json()['data']['access_token']
        print("✅ 登录成功")
        return token
    else:
        print(f"❌ 登录失败: {response.text}")
        return None

def get_statistics(token):
    """获取统计数据"""
    headers = {'Authorization': f'Bearer {token}'}
    
    # 获取CK基础统计
    ck_stats_response = requests.get(f"{BASE_URL}/walmart-ck/statistics/1", headers=headers)
    
    # 获取绑卡金额统计
    amount_stats_response = requests.get(f"{BASE_URL}/walmart-ck/binding-amount-statistics", headers=headers)
    
    ck_stats = None
    amount_stats = None
    
    if ck_stats_response.status_code == 200:
        ck_stats = ck_stats_response.json()['data']
        print(f"CK统计获取成功")
    else:
        print(f"CK统计获取失败: {ck_stats_response.status_code}")
    
    if amount_stats_response.status_code == 200:
        amount_stats = amount_stats_response.json()['data']
        print(f"绑卡金额统计获取成功")
    else:
        print(f"绑卡金额统计获取失败: {amount_stats_response.status_code}")
    
    return ck_stats, amount_stats

def create_test_ck(token, suffix=""):
    """创建测试CK"""
    test_data = {
        "sign": f"verify_fix_{uuid.uuid4().hex[:8]}{suffix}@token#signature#26",
        "daily_limit": 100,
        "hourly_limit": 50,
        "active": 1,
        "description": f"验证修复测试CK{suffix}",
        "merchant_id": 1,
        "department_id": 1
    }
    
    headers = {'Authorization': f'Bearer {token}'}
    response = requests.post(f"{BASE_URL}/walmart-ck", json=test_data, headers=headers)
    
    if response.status_code == 200:
        response_data = response.json()
        if 'data' in response_data and 'data' in response_data['data']:
            ck_id = response_data['data']['data']['id']
        else:
            ck_id = response_data['data']['id']
        print(f"✅ 创建CK成功，ID: {ck_id}")
        return ck_id
    else:
        print(f"❌ 创建CK失败: {response.text}")
        return None

def delete_ck(token, ck_id):
    """删除CK"""
    headers = {'Authorization': f'Bearer {token}'}
    response = requests.delete(f"{BASE_URL}/walmart-ck/{ck_id}", headers=headers)
    
    if response.status_code == 200:
        print(f"✅ 删除CK成功，ID: {ck_id}")
        return True
    else:
        print(f"❌ 删除CK失败，ID: {ck_id}, 响应: {response.text}")
        return False

def main():
    """主测试流程"""
    print("🧪 验证CK删除统计数据一致性修复效果")
    print("="*60)
    
    # 1. 登录
    token = login()
    if not token:
        return False
    
    # 2. 获取初始统计数据
    print("\n--- 获取初始统计数据 ---")
    initial_ck_stats, initial_amount_stats = get_statistics(token)
    
    if not initial_ck_stats or not initial_amount_stats:
        print("❌ 无法获取初始统计数据")
        return False
    
    # 提取关键统计数据
    initial_success_count = initial_ck_stats.get('actual_success_count', 0)
    initial_total_amount = initial_amount_stats.get('summary', {}).get('total_actual_amount', 0)
    initial_total_records = initial_amount_stats.get('summary', {}).get('total_success', 0)
    initial_total_cks = initial_ck_stats.get('total_count', 0)
    
    print(f"初始数据:")
    print(f"  - CK总数: {initial_total_cks}")
    print(f"  - 绑卡成功数: {initial_success_count}")
    print(f"  - 绑卡金额: {initial_total_amount}分")
    print(f"  - 成功记录数: {initial_total_records}")
    
    # 3. 创建测试CK（不创建绑卡记录，只测试CK数量变化）
    print("\n--- 创建测试CK ---")
    test_ck_id = create_test_ck(token, "_verify")
    if not test_ck_id:
        return False
    
    # 4. 获取创建后统计数据
    print("\n--- 获取创建后统计数据 ---")
    after_create_ck_stats, after_create_amount_stats = get_statistics(token)
    
    if not after_create_ck_stats or not after_create_amount_stats:
        print("❌ 无法获取创建后统计数据")
        return False
    
    after_create_total_cks = after_create_ck_stats.get('total_count', 0)
    after_create_success_count = after_create_ck_stats.get('actual_success_count', 0)
    after_create_total_amount = after_create_amount_stats.get('summary', {}).get('total_actual_amount', 0)
    after_create_total_records = after_create_amount_stats.get('summary', {}).get('total_success', 0)
    
    print(f"创建后数据:")
    print(f"  - CK总数: {after_create_total_cks}")
    print(f"  - 绑卡成功数: {after_create_success_count}")
    print(f"  - 绑卡金额: {after_create_total_amount}分")
    print(f"  - 成功记录数: {after_create_total_records}")
    
    # 验证CK数量增加
    if after_create_total_cks == initial_total_cks + 1:
        print("✅ CK数量正确增加")
    else:
        print(f"❌ CK数量变化异常，期望{initial_total_cks + 1}，实际{after_create_total_cks}")
    
    # 5. 删除测试CK
    print("\n--- 删除测试CK ---")
    if not delete_ck(token, test_ck_id):
        return False
    
    # 等待数据处理
    time.sleep(2)
    
    # 6. 获取删除后统计数据
    print("\n--- 获取删除后统计数据 ---")
    after_delete_ck_stats, after_delete_amount_stats = get_statistics(token)
    
    if not after_delete_ck_stats or not after_delete_amount_stats:
        print("❌ 无法获取删除后统计数据")
        return False
    
    after_delete_total_cks = after_delete_ck_stats.get('total_count', 0)
    after_delete_success_count = after_delete_ck_stats.get('actual_success_count', 0)
    after_delete_total_amount = after_delete_amount_stats.get('summary', {}).get('total_actual_amount', 0)
    after_delete_total_records = after_delete_amount_stats.get('summary', {}).get('total_success', 0)
    
    print(f"删除后数据:")
    print(f"  - CK总数: {after_delete_total_cks}")
    print(f"  - 绑卡成功数: {after_delete_success_count}")
    print(f"  - 绑卡金额: {after_delete_total_amount}分")
    print(f"  - 成功记录数: {after_delete_total_records}")
    
    # 7. 验证修复效果
    print("\n--- 验证修复效果 ---")
    success = True
    
    # 验证CK数量正确减少
    if after_delete_total_cks == initial_total_cks:
        print("✅ CK数量正确恢复到初始值")
    else:
        print(f"❌ CK数量异常，期望{initial_total_cks}，实际{after_delete_total_cks}")
        success = False
    
    # 验证历史绑卡数据不受影响（关键测试）
    if after_delete_success_count == initial_success_count:
        print("✅ 绑卡成功数保持不变（修复生效）")
    else:
        print(f"❌ 绑卡成功数发生变化，初始{initial_success_count}，删除后{after_delete_success_count}")
        success = False
    
    if after_delete_total_amount == initial_total_amount:
        print("✅ 绑卡金额保持不变（修复生效）")
    else:
        print(f"❌ 绑卡金额发生变化，初始{initial_total_amount}，删除后{after_delete_total_amount}")
        success = False
    
    if after_delete_total_records == initial_total_records:
        print("✅ 成功记录数保持不变（修复生效）")
    else:
        print(f"❌ 成功记录数发生变化，初始{initial_total_records}，删除后{after_delete_total_records}")
        success = False
    
    # 8. 测试结果
    print("\n--- 测试结果 ---")
    if success:
        print("🎉 CK删除统计数据一致性修复验证通过！")
        print("✅ 历史绑卡数据不受CK删除影响")
        print("✅ 统计数据保持准确性")
    else:
        print("❌ 修复验证失败，仍存在统计数据一致性问题")
    
    return success

if __name__ == "__main__":
    main()
