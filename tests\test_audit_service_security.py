"""
测试AuditService数据隔离安全修复
验证审计服务的商户级数据隔离机制
"""

import pytest
from sqlalchemy.orm import Session
from app.models.user import User
from app.models.merchant import Merchant
from app.models.audit import AuditLog, AuditEventType, AuditLevel
from app.services.audit_service import AuditService


class TestAuditServiceSecurity:
    """测试AuditService安全修复"""

    @pytest.fixture
    def setup_test_data(self, db: Session):
        """设置测试数据"""
        # 创建两个商户
        merchant1 = Merchant(
            name="测试商户1",
            code="TEST_MERCHANT_1",
            api_key="test_key_1",
            api_secret="test_secret_1"
        )
        merchant2 = Merchant(
            name="测试商户2", 
            code="TEST_MERCHANT_2",
            api_key="test_key_2",
            api_secret="test_secret_2"
        )
        db.add_all([merchant1, merchant2])
        db.commit()
        db.refresh(merchant1)
        db.refresh(merchant2)

        # 创建用户
        user1 = User(
            username="test_user_1",
            email="<EMAIL>",
            hashed_password="hashed_password",
            merchant_id=merchant1.id,
            is_superuser=False
        )
        user2 = User(
            username="test_user_2",
            email="<EMAIL>", 
            hashed_password="hashed_password",
            merchant_id=merchant2.id,
            is_superuser=False
        )
        superuser = User(
            username="superuser",
            email="<EMAIL>",
            hashed_password="hashed_password",
            is_superuser=True
        )
        db.add_all([user1, user2, superuser])
        db.commit()
        db.refresh(user1)
        db.refresh(user2)
        db.refresh(superuser)

        # 创建审计日志
        audit1 = AuditLog(
            event_type=AuditEventType.OPERATION,
            level=AuditLevel.INFO,
            message="测试审计日志1",
            user_id=user1.id,
            merchant_id=merchant1.id
        )
        audit2 = AuditLog(
            event_type=AuditEventType.OPERATION,
            level=AuditLevel.INFO,
            message="测试审计日志2",
            user_id=user2.id,
            merchant_id=merchant2.id
        )
        db.add_all([audit1, audit2])
        db.commit()

        return {
            "merchant1": merchant1,
            "merchant2": merchant2,
            "user1": user1,
            "user2": user2,
            "superuser": superuser,
            "audit1": audit1,
            "audit2": audit2
        }

    def test_audit_service_inheritance(self, db: Session):
        """测试AuditService正确继承BaseService"""
        service = AuditService(db)
        
        # 验证继承关系
        from app.services.base_service import BaseService
        assert isinstance(service, BaseService)
        
        # 验证apply_data_isolation方法存在
        assert hasattr(service, 'apply_data_isolation')
        assert callable(getattr(service, 'apply_data_isolation'))

    def test_data_isolation_for_regular_users(self, db: Session, setup_test_data):
        """测试普通用户的数据隔离"""
        service = AuditService(db)
        user1 = setup_test_data["user1"]
        user2 = setup_test_data["user2"]

        # 用户1应该只能看到自己商户的审计日志
        query1 = db.query(AuditLog)
        isolated_query1 = service.apply_data_isolation(query1, user1)
        results1 = isolated_query1.all()
        
        assert len(results1) == 1, f"用户1应该只能看到1个审计日志，实际看到{len(results1)}个"
        assert results1[0].merchant_id == user1.merchant_id, "用户1应该只能看到自己商户的审计日志"

        # 用户2应该只能看到自己商户的审计日志
        query2 = db.query(AuditLog)
        isolated_query2 = service.apply_data_isolation(query2, user2)
        results2 = isolated_query2.all()
        
        assert len(results2) == 1, f"用户2应该只能看到1个审计日志，实际看到{len(results2)}个"
        assert results2[0].merchant_id == user2.merchant_id, "用户2应该只能看到自己商户的审计日志"

    def test_superuser_access(self, db: Session, setup_test_data):
        """测试超级管理员可以访问所有数据"""
        service = AuditService(db)
        superuser = setup_test_data["superuser"]

        query = db.query(AuditLog)
        isolated_query = service.apply_data_isolation(query, superuser)
        results = isolated_query.all()
        
        assert len(results) == 2, f"超级管理员应该能看到所有2个审计日志，实际看到{len(results)}个"

    def test_user_without_merchant_id(self, db: Session, setup_test_data):
        """测试没有商户ID的用户被拒绝访问"""
        service = AuditService(db)
        
        # 创建没有商户ID的用户
        user_no_merchant = User(
            username="no_merchant_user",
            email="<EMAIL>",
            hashed_password="hashed_password",
            is_superuser=False
        )
        db.add(user_no_merchant)
        db.commit()

        query = db.query(AuditLog)
        isolated_query = service.apply_data_isolation(query, user_no_merchant)
        results = isolated_query.all()
        
        assert len(results) == 0, "没有商户ID的用户应该看不到任何审计日志"

    def test_get_merchant_id_from_user(self, db: Session, setup_test_data):
        """测试从用户ID获取商户ID的辅助方法"""
        service = AuditService(db)
        user1 = setup_test_data["user1"]
        
        merchant_id = service._get_merchant_id_from_user(user1.id)
        assert merchant_id == user1.merchant_id, "应该返回正确的商户ID"
        
        # 测试不存在的用户
        merchant_id = service._get_merchant_id_from_user(99999)
        assert merchant_id is None, "不存在的用户应该返回None"
        
        # 测试None用户ID
        merchant_id = service._get_merchant_id_from_user(None)
        assert merchant_id is None, "None用户ID应该返回None"

    @pytest.mark.asyncio
    async def test_log_event_auto_merchant_id(self, db: Session, setup_test_data):
        """测试log_event方法自动填充merchant_id"""
        service = AuditService(db)
        user1 = setup_test_data["user1"]
        
        # 测试不提供merchant_id时自动从user_id获取
        audit = await service.log_event(
            db=db,
            event_type=AuditEventType.OPERATION,
            level=AuditLevel.INFO,
            message="测试自动填充商户ID",
            user_id=user1.id
        )
        
        assert audit.merchant_id == user1.merchant_id, "应该自动填充正确的商户ID"
        assert audit.user_id == user1.id, "应该保留用户ID"

    @pytest.mark.asyncio
    async def test_log_event_operator_merchant_id(self, db: Session, setup_test_data):
        """测试log_event方法从operator_id获取merchant_id"""
        service = AuditService(db)
        user1 = setup_test_data["user1"]
        user2 = setup_test_data["user2"]
        
        # 测试从operator_id获取merchant_id
        audit = await service.log_event(
            db=db,
            event_type=AuditEventType.OPERATION,
            level=AuditLevel.INFO,
            message="测试操作者商户ID",
            user_id=user2.id,  # 用户2
            operator_id=user1.id  # 操作者是用户1
        )
        
        # 应该优先使用user_id的商户ID
        assert audit.merchant_id == user2.merchant_id, "应该使用user_id的商户ID"

    @pytest.mark.asyncio
    async def test_log_event_explicit_merchant_id(self, db: Session, setup_test_data):
        """测试log_event方法显式提供merchant_id时不被覆盖"""
        service = AuditService(db)
        user1 = setup_test_data["user1"]
        merchant2 = setup_test_data["merchant2"]
        
        # 显式提供merchant_id
        audit = await service.log_event(
            db=db,
            event_type=AuditEventType.OPERATION,
            level=AuditLevel.INFO,
            message="测试显式商户ID",
            user_id=user1.id,
            merchant_id=merchant2.id  # 显式指定不同的商户ID
        )
        
        assert audit.merchant_id == merchant2.id, "应该保留显式提供的商户ID"
