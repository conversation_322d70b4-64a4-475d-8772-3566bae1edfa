"""
权限相关的 Pydantic 模型 - 重新设计版本
"""

from typing import Optional, List, Literal
from pydantic import BaseModel, Field
from datetime import datetime


# 使用常量类代替枚举
class ResourceTypeConstants:
    """资源类型常量"""

    MENU = "MENU"
    API = "API"
    DATA = "DATA"


# 资源类型定义为普通字符串类型（不限制具体值）
ResourceType = str


class PermissionBase(BaseModel):
    """权限基础模型"""

    code: str = Field(..., description="权限代码，格式：module:action 或 api:/path")
    name: str = Field(..., description="权限名称")
    description: Optional[str] = Field(None, description="权限描述")
    resource_type: ResourceType = Field(
        ResourceTypeConstants.API, description="资源类型"
    )
    resource_path: Optional[str] = Field(
        None, description="资源路径（菜单路径或API路径）"
    )
    is_enabled: bool = Field(True, description="是否启用")
    sort_order: int = Field(0, description="排序号")


class PermissionCreate(PermissionBase):
    """创建权限模型"""

    pass


class PermissionUpdate(BaseModel):
    """更新权限模型"""

    name: Optional[str] = Field(None, description="权限名称")
    description: Optional[str] = Field(None, description="权限描述")
    resource_type: Optional[ResourceType] = Field(None, description="资源类型")
    resource_path: Optional[str] = Field(None, description="资源路径")
    is_enabled: Optional[bool] = Field(None, description="是否启用")
    sort_order: Optional[int] = Field(None, description="排序号")


class PermissionInDB(PermissionBase):
    """数据库权限模型"""

    id: int
    created_at: datetime
    updated_at: Optional[datetime] = None

    class Config:
        from_attributes = True


class PermissionResponse(PermissionInDB):
    """权限响应模型"""

    roles: Optional[List["RoleSimple"]] = None
    menus: Optional[List["MenuSimple"]] = None


class PermissionQuery(BaseModel):
    """权限查询模型"""

    code: Optional[str] = Field(None, description="权限代码（模糊查询）")
    name: Optional[str] = Field(None, description="权限名称（模糊查询）")
    resource_type: Optional[ResourceType] = Field(None, description="资源类型")
    is_enabled: Optional[bool] = Field(None, description="是否启用")
    include_roles: bool = Field(False, description="是否包含角色信息")
    include_menus: bool = Field(False, description="是否包含菜单信息")


class UserPermissionCheck(BaseModel):
    """用户权限检查模型"""

    user_id: int
    permission_code: str
    resource_id: Optional[int] = None
    merchant_id: Optional[int] = None
    department_id: Optional[int] = None


class PermissionCheckResult(BaseModel):
    """权限检查结果模型"""

    has_permission: bool
    reason: Optional[str] = None
    resource_type: Optional[ResourceType] = None


class PermissionListResponse(BaseModel):
    """权限列表响应模型"""

    items: List[PermissionInDB]
    total: int
    page: int = 1
    size: int = 20
    pages: int = 1


class PermissionTreeNode(BaseModel):
    """权限树节点模型"""

    module: str
    module_name: str
    permissions: List[PermissionInDB]


class PermissionTree(BaseModel):
    """权限树模型"""

    tree: List[PermissionTreeNode]


class RolePermissionAssign(BaseModel):
    """角色权限分配模型"""

    role_id: int
    permission_codes: List[str]


class UserPermissionInfo(BaseModel):
    """用户权限信息模型"""

    user_id: int
    username: str
    role: str
    permissions: List[str]
    accessible_merchants: List[int] = []
    accessible_departments: List[int] = []


# 兼容性类定义（保持向后兼容）
class Permission(PermissionInDB):
    """权限模型（兼容性别名）"""

    pass


class PermissionWithRoles(PermissionInDB):
    """包含角色信息的权限模型"""

    roles: List[str] = Field([], description="拥有该权限的角色列表")


# 权限树形结构模型
class PermissionTreeResponse(BaseModel):
    """权限树形响应模型"""

    items: List[PermissionInDB]
    total: int


# 简单模型定义（避免循环导入）
class RoleSimple(BaseModel):
    """角色简单模型"""

    id: int
    name: str
    code: str


class MenuSimple(BaseModel):
    """菜单简单模型"""

    id: int
    name: str
    code: str


# 重建模型以解决前向引用
PermissionResponse.model_rebuild()
