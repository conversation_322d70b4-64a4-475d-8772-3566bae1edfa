#!/usr/bin/env python3
"""
修复历史绑卡记录的处理时间
批量更新process_time字段为正确的总处理时间
"""

import sys
import os
from pathlib import Path
from datetime import datetime, timedelta

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from app.db.session import SessionLocal
from app.models.card_record import CardRecord
from app.utils.time_utils import ensure_timezone
from app.core.logging import get_logger
from sqlalchemy import text

logger = get_logger("fix_process_time")

def analyze_records_to_fix():
    """分析需要修复的记录"""
    print("🔍 分析需要修复的记录")
    print("=" * 60)
    
    db = SessionLocal()
    try:
        # 查询所有已完成的记录
        records = db.query(CardRecord).filter(
            CardRecord.status.in_(['success', 'failed']),
            CardRecord.created_at.isnot(None),
            CardRecord.updated_at.isnot(None)
        ).all()
        
        print(f"📊 总记录数: {len(records)}")
        
        need_fix_records = []
        correct_records = []
        
        for record in records:
            stored_time = record.process_time or 0
            
            created_at_tz = ensure_timezone(record.created_at)
            updated_at_tz = ensure_timezone(record.updated_at)
            correct_time = (updated_at_tz - created_at_tz).total_seconds()
            
            # 如果差异超过1秒，认为需要修复
            if abs(stored_time - correct_time) > 1.0:
                need_fix_records.append({
                    'record': record,
                    'stored_time': stored_time,
                    'correct_time': correct_time,
                    'diff': abs(stored_time - correct_time)
                })
            else:
                correct_records.append(record)
        
        print(f"✅ 正确的记录: {len(correct_records)}")
        print(f"⚠️  需要修复的记录: {len(need_fix_records)}")
        print(f"📈 修复比例: {len(need_fix_records)/len(records)*100:.1f}%")
        
        if need_fix_records:
            # 显示最严重的几个案例
            need_fix_records.sort(key=lambda x: x['diff'], reverse=True)
            print(f"\n🔥 最严重的5个案例:")
            for i, item in enumerate(need_fix_records[:5], 1):
                record = item['record']
                print(f"  {i}. {record.id[:8]}... | 存储: {item['stored_time']:.2f}s | 正确: {item['correct_time']:.2f}s | 差异: {item['diff']:.2f}s")
        
        return need_fix_records
        
    except Exception as e:
        print(f"❌ 分析失败: {e}")
        logger.exception("分析记录失败")
        return []
    
    finally:
        db.close()

def fix_process_time_batch(need_fix_records, batch_size=50, dry_run=True):
    """批量修复处理时间"""
    print(f"\n🔧 批量修复处理时间 (dry_run={dry_run})")
    print("=" * 60)
    
    if not need_fix_records:
        print("✅ 没有需要修复的记录")
        return
    
    total_records = len(need_fix_records)
    print(f"📊 需要修复的记录总数: {total_records}")
    
    db = SessionLocal()
    try:
        fixed_count = 0
        
        for i in range(0, total_records, batch_size):
            batch = need_fix_records[i:i + batch_size]
            batch_num = i // batch_size + 1
            total_batches = (total_records + batch_size - 1) // batch_size
            
            print(f"\n📦 处理批次 {batch_num}/{total_batches} ({len(batch)} 条记录)")
            
            if not dry_run:
                # 开始事务
                db.begin()
            
            try:
                for item in batch:
                    record = item['record']
                    correct_time = item['correct_time']
                    
                    if dry_run:
                        print(f"  [DRY RUN] 将修复 {record.id[:8]}... | {item['stored_time']:.2f}s -> {correct_time:.2f}s")
                    else:
                        # 【修复】更新记录时保持原始的updated_at时间，避免自动更新
                        original_updated_at = record.updated_at
                        db.execute(
                            text("UPDATE card_records SET process_time = :process_time, updated_at = :updated_at WHERE id = :id"),
                            {
                                "process_time": correct_time,
                                "updated_at": original_updated_at,
                                "id": str(record.id)
                            }
                        )
                        print(f"  ✅ 已修复 {record.id[:8]}... | {item['stored_time']:.2f}s -> {correct_time:.2f}s")
                    
                    fixed_count += 1
                
                if not dry_run:
                    # 提交事务
                    db.commit()
                    print(f"  💾 批次 {batch_num} 提交成功")
                
            except Exception as e:
                if not dry_run:
                    db.rollback()
                    print(f"  ❌ 批次 {batch_num} 失败，已回滚: {e}")
                else:
                    print(f"  ❌ 批次 {batch_num} 模拟失败: {e}")
                raise
        
        print(f"\n✅ 修复完成: {fixed_count}/{total_records} 条记录")
        
    except Exception as e:
        print(f"❌ 批量修复失败: {e}")
        logger.exception("批量修复失败")
    
    finally:
        db.close()

def verify_fix_results():
    """验证修复结果"""
    print(f"\n🔍 验证修复结果")
    print("=" * 60)
    
    db = SessionLocal()
    try:
        # 重新分析
        records = db.query(CardRecord).filter(
            CardRecord.status.in_(['success', 'failed']),
            CardRecord.created_at.isnot(None),
            CardRecord.updated_at.isnot(None)
        ).all()
        
        need_fix_count = 0
        total_count = len(records)
        
        for record in records:
            stored_time = record.process_time or 0
            
            created_at_tz = ensure_timezone(record.created_at)
            updated_at_tz = ensure_timezone(record.updated_at)
            correct_time = (updated_at_tz - created_at_tz).total_seconds()
            
            if abs(stored_time - correct_time) > 1.0:
                need_fix_count += 1
        
        print(f"📊 总记录数: {total_count}")
        print(f"⚠️  仍需修复: {need_fix_count}")
        print(f"✅ 已修复: {total_count - need_fix_count}")
        print(f"📈 修复率: {(total_count - need_fix_count)/total_count*100:.1f}%")
        
        if need_fix_count == 0:
            print("🎉 所有记录都已正确修复！")
        
    except Exception as e:
        print(f"❌ 验证失败: {e}")
        logger.exception("验证修复结果失败")
    
    finally:
        db.close()

def main():
    """主函数"""
    print("🔧 历史绑卡记录处理时间修复工具")
    print("=" * 80)
    
    # 分析需要修复的记录
    need_fix_records = analyze_records_to_fix()
    
    if not need_fix_records:
        print("\n✅ 所有记录的处理时间都是正确的，无需修复")
        return
    
    # 询问用户是否要执行修复
    print(f"\n❓ 发现 {len(need_fix_records)} 条记录需要修复")
    print("选择操作:")
    print("1. 仅预览修复（dry run）")
    print("2. 执行实际修复")
    print("3. 退出")
    
    try:
        choice = input("\n请输入选择 (1/2/3): ").strip()
        
        if choice == "1":
            print("\n🔍 执行预览修复...")
            fix_process_time_batch(need_fix_records, dry_run=True)
        
        elif choice == "2":
            print("\n⚠️  即将执行实际修复，这将修改数据库数据")
            confirm = input("确认执行吗？(yes/no): ").strip().lower()
            
            if confirm == "yes":
                print("\n🔧 执行实际修复...")
                fix_process_time_batch(need_fix_records, dry_run=False)
                
                # 验证修复结果
                verify_fix_results()
            else:
                print("❌ 用户取消操作")
        
        elif choice == "3":
            print("👋 退出程序")
        
        else:
            print("❌ 无效选择")
    
    except KeyboardInterrupt:
        print("\n❌ 用户中断操作")
    except Exception as e:
        print(f"❌ 操作失败: {e}")

if __name__ == "__main__":
    main()
