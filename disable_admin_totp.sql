-- 禁用超级管理员(admin)的双因子认证
-- 执行时间：2025-06-28

-- 首先查看admin用户当前的TOTP状态
SELECT 
    username,
    totp_enabled,
    totp_secret IS NOT NULL as has_secret,
    totp_setup_at,
    totp_last_used_at
FROM users 
WHERE username = 'admin';

-- 禁用admin用户的TOTP并清除所有相关数据
UPDATE users 
SET 
    totp_enabled = FALSE,
    totp_secret = NULL,
    totp_backup_codes = NULL,
    totp_last_used_at = NULL,
    totp_setup_at = NULL
WHERE username = 'admin';

-- 验证更新结果
SELECT 
    username,
    totp_enabled,
    totp_secret IS NOT NULL as has_secret,
    totp_setup_at,
    totp_last_used_at
FROM users 
WHERE username = 'admin';

-- 可选：查看TOTP操作日志（不删除，仅查看）
SELECT 
    action,
    success,
    ip_address,
    created_at
FROM totp_logs 
WHERE user_id = (SELECT id FROM users WHERE username = 'admin')
ORDER BY created_at DESC 
LIMIT 10;
