from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession, async_sessionmaker

from app.core.config import settings

# 为MySQL添加支持
if settings.DB_TYPE == "mysql":
    # 确保已经安装了pymysql
    import pymysql

    pymysql.install_as_MySQLdb()

# 创建数据库引擎，添加字符集配置
# 从配置文件读取性能参数
performance_config = settings.yaml_config.get("performance", {})

engine_kwargs = {
    "pool_pre_ping": performance_config.get("db_pool_pre_ping", True),
    "pool_recycle": performance_config.get("db_pool_recycle", 3600),  # 1小时回收连接
    "pool_size": performance_config.get("db_pool_size", 100),  # 增加连接池大小
    "max_overflow": performance_config.get("db_pool_max_overflow", 200),  # 增加溢出连接
    "pool_timeout": performance_config.get("db_pool_timeout", 30),
    "echo": False,  # 生产环境关闭SQL日志
}

# 为MySQL添加特殊配置
if settings.DB_TYPE == "mysql":
    engine_kwargs.update({
        "connect_args": {
            "charset": "utf8mb4",
            "use_unicode": True,
            "init_command": "SET NAMES utf8mb4",  # 移除COLLATE，aiomysql不支持
            "connect_timeout": 60,
            "read_timeout": 60,
            "write_timeout": 60
        }
    })

engine = create_engine(settings.SQLALCHEMY_DATABASE_URI, **engine_kwargs)
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

# 创建异步数据库引擎和会话
# 将同步数据库URI转换为异步URI
async_database_uri = settings.SQLALCHEMY_DATABASE_URI.replace("mysql+pymysql://", "mysql+aiomysql://")

# 为异步引擎配置参数（移除同步特有的参数）
async_engine_kwargs = engine_kwargs.copy()
if "connect_args" in async_engine_kwargs:
    # 移除同步连接参数，保留基本配置
    async_connect_args = {
        "charset": "utf8mb4",
        "connect_timeout": 60,
        "autocommit": False,
    }
    async_engine_kwargs["connect_args"] = async_connect_args

async_engine = create_async_engine(async_database_uri, **async_engine_kwargs)
AsyncSessionLocal = async_sessionmaker(
    bind=async_engine,
    class_=AsyncSession,
    autocommit=False,
    autoflush=False,
    expire_on_commit=False
)
