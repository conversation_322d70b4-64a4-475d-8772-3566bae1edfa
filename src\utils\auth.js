// Token 相关常量
const TOKEN_KEY = 'walmart_token'
const TOKEN_EXPIRE_KEY = 'walmart_token_expire'
const USER_INFO_KEY = 'walmart_ck_info'

/**
 * Token 管理
 */
export const TokenManager = {
  // 获取 token
  getToken() {
    return localStorage.getItem(TOKEN_KEY)
  },

  // 设置 token 和过期时间
  setToken(token, expiresIn = 7200) {
    localStorage.setItem(TOKEN_KEY, token)
    const expireTime = new Date().getTime() + expiresIn * 1000
    localStorage.setItem(TOKEN_EXPIRE_KEY, expireTime.toString())
  },

  // 移除 token
  removeToken() {
    localStorage.removeItem(TOKEN_KEY)
    localStorage.removeItem(TOKEN_EXPIRE_KEY)
    localStorage.removeItem(USER_INFO_KEY)
    localStorage.clear();
    sessionStorage.clear();
  },

  // 获取用户信息
  getUserInfo() {
    const userInfo = localStorage.getItem(USER_INFO_KEY)
    return userInfo ? JSON.parse(userInfo) : null
  },

  // 设置用户信息
  setUserInfo(userInfo) {
    localStorage.setItem(USER_INFO_KEY, JSON.stringify(userInfo))
  },

  // 移除用户信息
  removeUserInfo() {
    localStorage.removeItem(USER_INFO_KEY)
  },

  // 检查 token 是否过期
  isTokenExpired() {
    const expireTime = localStorage.getItem(TOKEN_EXPIRE_KEY)
    if (!expireTime) return true
    return new Date().getTime() > parseInt(expireTime)
  },

  // 检查是否已登录
  isLoggedIn() {
    return !!this.getToken() && !this.isTokenExpired()
  },

  // 获取认证头信息
  getAuthHeader() {
    const token = this.getToken()
    return token ? { Authorization: `Bearer ${token}` } : {}
  }
}

// 导出便捷方法
export const {
  getToken,
  setToken,
  removeToken,
  isTokenExpired,
  isLoggedIn,
  getAuthHeader,
  getUserInfo,
  setUserInfo,
  removeUserInfo
} = TokenManager