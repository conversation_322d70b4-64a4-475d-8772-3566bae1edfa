import { http } from '@/api/request'
import { API_URLS, replaceUrlParams } from './config'

const { MERCHANT } = API_URLS

/**
 * 商家管理相关API
 */
export const merchantApi = {
    // 获取商家列表
    getList(params = {}) {
        return http.get(MERCHANT.LIST, { params }).then(response => {
            return response.data
        })
    },

    // 获取商家详情
    getDetail(id, params = {}) {
        const url = replaceUrlParams(MERCHANT.DETAIL, { id })
        return http.get(url, { params }).then(res => res.data)
    },

    // 创建商家
    create(data) {
        return http.post(MERCHANT.CREATE, data).then(res => res.data)
    },

    // 更新商家
    update(id, data) {
        const url = replaceUrlParams(MERCHANT.UPDATE, { id })
        return http.put(url, data).then(res => res.data)
    },

    // 删除商家
    delete(id) {
        const url = replaceUrlParams(MERCHANT.DELETE, { id })
        return http.delete(url).then(res => res.data)
    },

    // 切换商家状态
    toggleStatus(id, status) {
        const url = replaceUrlParams(MERCHANT.TOGGLE_STATUS, { id })
        return http.patch(url, { status }).then(res => res.data)
    },

    // 切换商家活动状态 (存在)
    toggleActivity(id, activity) {
        const url = replaceUrlParams(MERCHANT.TOGGLE_ACTIVITY, { id })
        return http.patch(url, { activity }).then(res => res.data)
    },

    // 重置商家 API Key
    resetApiKey(id) {
        const url = replaceUrlParams(MERCHANT.RESET_API_KEY, { id })
        return http.post(url).then(res => res.data)
    },

    // 获取商家统计信息
    getStatistics(id) {
        const url = replaceUrlParams(MERCHANT.STATS, { id })
        return http.get(url).then(res => res.data)
    },

    // 获取商家活跃度数据
    getActivity(params = {}) {
        return http.get(MERCHANT.ACTIVITY, { params }).then(res => res.data)
    },

    // 获取商家用户列表
    getUsers(id, params = {}) {
        const url = replaceUrlParams(MERCHANT.USERS, { id })
        return http.get(url, { params }).then(res => res.data)
    },

    // 获取商家IP白名单
    getIpWhitelist(id) {
        const url = replaceUrlParams(MERCHANT.IP_WHITELIST, { id })
        return http.get(url).then(res => res.data)
    },

    // 添加IP白名单
    createIpWhitelist(id, data) {
        const url = replaceUrlParams(MERCHANT.IP_WHITELIST, { id });
        return http.post(url, data).then(res => res.data)
    },

    // 更新IP白名单
    updateIpWhitelist(merchantId, ipId, data) {
        const url = replaceUrlParams(`${MERCHANT.IP_WHITELIST}/${ipId}`, { merchantId }); // API结构是 /merchants/:id/ip-whitelist/:ip_id
        return http.put(url, data).then(res => res.data)
    },

    // 删除IP白名单
    deleteIpWhitelist(merchantId, ipId) {
        const url = replaceUrlParams(`${MERCHANT.IP_WHITELIST}/${ipId}`, { merchantId });
        return http.delete(url).then(res => res.data)
    },

    // 获取API使用情况
    getApiUsage(id) {
        const url = replaceUrlParams(MERCHANT.API_USAGE, { id })
        return http.get(url).then(res => res.data)
    },

    // 验证密码以获取API Secret
    verifyPassword(id, data) {
        const url = replaceUrlParams(MERCHANT.VERIFY_PASSWORD, { id });
        return http.post(url, data);
    },

    // 生成 API Key (独立接口)
    generateApiKey() {
        // 注意: API_URLS 中没有明确定义此独立接口，路径为 /merchants/generate-api-key
        return http.get('/merchants/generate-api-key').then(res => res.data)
    },

    // 获取指定商家的API凭据
    getApiCredentials(id) {
        const url = replaceUrlParams('/merchants/:id/api-credentials', { id })
        return http.get(url).then(res => res.data)
    },

    // 获取当前用户所属商家的API凭据
    getCurrentMerchantApiCredentials() {
        return http.get('/merchants/current/api-credentials').then(res => res.data)
    },
}

export default merchantApi
