# 导入所有CRUD模块以保持向后兼容
import app.crud.user as user_module
import app.crud.merchant as merchant_module
import app.crud.card as card_module
import app.crud.ip_whitelist as ip_whitelist_module
import app.crud.walmart_server as walmart_server_config_module
import app.crud.permission as permission_module
import app.crud.menu as menu_module
import app.crud.role as role_module
import app.crud.department as department_module

# 直接导入具体的CRUD实例
from app.crud.user import user
from app.crud.merchant import merchant
from app.crud.card import card
from app.crud.ip_whitelist import ip_whitelist
from app.crud.walmart_server import walmart_server_config
from app.crud.permission import permission
from app.crud.menu import menu
from app.crud.role import role
from app.crud.card_record import card_record
from app.crud.binding_log import binding_log
from app.crud.walmart_ck import walmart_ck
from app.crud.department import department
from app.crud.notification_config import notification_config_crud
from app.crud.audit import audit_log
from app.crud.system_settings import system_settings
from app.crud.notification import notification

# 导出所有CRUD模块和实例
__all__ = [
    # 模块 - 向后兼容
    "user_module",
    "merchant_module",
    "card_module",
    "ip_whitelist_module",
    "walmart_server_config_module",
    "permission_module",
    "menu_module",
    "role_module",
    "department_module",
    # 实例 - 直接使用
    "user",
    "merchant",
    "card",
    "ip_whitelist",
    "walmart_server_config",
    "permission",
    "menu",
    "role",
    "card_record",
    "binding_log",
    "walmart_ck",
    "department",
    "notification_config_crud",
    "audit_log",
    "system_settings",
    "notification",
]
