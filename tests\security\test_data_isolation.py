#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据隔离测试
测试商户间、部门间的数据隔离功能
"""

import sys
import os
import time

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.dirname(__file__))))

from test.conftest import TestBase, get_test_accounts, format_test_result, print_test_summary

class DataIsolationTestSuite(TestBase):
    """数据隔离测试套件"""
    
    def __init__(self):
        super().__init__()
        self.results = []
        self.test_accounts = get_test_accounts()
        self.admin_token = None
        self.merchant_a_token = None
        self.merchant_b_token = None
    
    def setup(self):
        """测试前置设置"""
        print("=== 测试前置设置 ===")
        
        # 管理员登录
        self.admin_token = self.login("admin", "7c222fb2927d828af22f592134e8932480637c0d")
        if not self.admin_token:
            print("❌ 管理员登录失败")
            return False
        
        # 商户A管理员登录
        self.merchant_a_token = self.login("test1", "********")
        if not self.merchant_a_token:
            print("❌ 商户A管理员登录失败")
            return False
        
        # 商户B管理员登录（如果存在）
        self.merchant_b_token = self.login("test_merchant_b", "********")
        if not self.merchant_b_token:
            print("⚠️ 商户B管理员登录失败，将跳过部分测试")
        
        print("✅ 测试前置设置完成")
        return True
    
    def test_merchant_data_isolation(self):
        """测试商户数据隔离"""
        print("\n=== 测试商户数据隔离 ===")
        
        if not self.merchant_a_token:
            self.results.append(format_test_result(
                "商户数据隔离前置条件",
                False,
                "缺少商户A token"
            ))
            return
        
        # 测试商户A访问用户列表
        status_code_a, response_a = self.make_request("GET", "/users", self.merchant_a_token)
        
        if status_code_a == 200:
            users_a = response_a.get("data", {}).get("items", []) if isinstance(response_a.get("data"), dict) else response_a.get("items", [])
            
            # 检查商户A是否只能看到自己的用户
            merchant_a_users = [u for u in users_a if u.get("merchant_id") or True]  # 假设所有返回的用户都属于商户A
            
            self.results.append(format_test_result(
                "商户A用户数据隔离",
                True,
                f"商户A可以访问 {len(merchant_a_users)} 个用户（应只包含本商户用户）",
                {"user_count": len(merchant_a_users)}
            ))
            print(f"✅ 商户A可以访问 {len(merchant_a_users)} 个用户")
        elif status_code_a in [403, 401]:
            self.results.append(format_test_result(
                "商户A用户访问权限控制",
                True,
                "正确限制商户A访问用户列表"
            ))
            print("✅ 正确限制商户A访问用户列表")
        else:
            self.results.append(format_test_result(
                "商户A用户数据隔离",
                False,
                f"商户A访问用户列表失败，状态码: {status_code_a}"
            ))
            print(f"❌ 商户A访问用户列表失败，状态码: {status_code_a}")
        
        # 如果有商户B，测试商户B的数据隔离
        if self.merchant_b_token:
            status_code_b, response_b = self.make_request("GET", "/users", self.merchant_b_token)
            
            if status_code_b == 200:
                users_b = response_b.get("data", {}).get("items", []) if isinstance(response_b.get("data"), dict) else response_b.get("items", [])
                
                self.results.append(format_test_result(
                    "商户B用户数据隔离",
                    True,
                    f"商户B可以访问 {len(users_b)} 个用户（应只包含本商户用户）",
                    {"user_count": len(users_b)}
                ))
                print(f"✅ 商户B可以访问 {len(users_b)} 个用户")
                
                # 检查商户A和商户B是否看到不同的用户数据
                if status_code_a == 200 and len(users_a) != len(users_b):
                    self.results.append(format_test_result(
                        "商户间用户数据隔离验证",
                        True,
                        f"商户A和商户B看到不同的用户数据（A: {len(users_a)}, B: {len(users_b)}）"
                    ))
                    print(f"✅ 商户间用户数据隔离正常（A: {len(users_a)}, B: {len(users_b)}）")
                elif status_code_a == 200:
                    self.results.append(format_test_result(
                        "商户间用户数据隔离验证",
                        False,
                        f"商户A和商户B看到相同数量的用户数据，可能存在数据泄露"
                    ))
                    print(f"❌ 商户A和商户B看到相同数量的用户数据，可能存在数据泄露")
            elif status_code_b in [403, 401]:
                self.results.append(format_test_result(
                    "商户B用户访问权限控制",
                    True,
                    "正确限制商户B访问用户列表"
                ))
                print("✅ 正确限制商户B访问用户列表")
    
    def test_department_data_isolation(self):
        """测试部门数据隔离"""
        print("\n=== 测试部门数据隔离 ===")
        
        if not self.merchant_a_token:
            return
        
        # 测试商户A访问部门列表
        status_code, response = self.make_request("GET", "/departments", self.merchant_a_token)
        
        if status_code == 200:
            departments = response.get("data", {}).get("items", []) if isinstance(response.get("data"), dict) else response.get("items", [])
            
            self.results.append(format_test_result(
                "商户部门数据隔离",
                True,
                f"商户A可以访问 {len(departments)} 个部门（应只包含本商户部门）",
                {"department_count": len(departments)}
            ))
            print(f"✅ 商户A可以访问 {len(departments)} 个部门")
        elif status_code in [403, 401]:
            self.results.append(format_test_result(
                "商户部门访问权限控制",
                True,
                "正确限制商户访问部门列表"
            ))
            print("✅ 正确限制商户访问部门列表")
        else:
            self.results.append(format_test_result(
                "商户部门数据隔离",
                False,
                f"商户访问部门列表失败，状态码: {status_code}"
            ))
            print(f"❌ 商户访问部门列表失败，状态码: {status_code}")
    
    def test_merchant_list_isolation(self):
        """测试商户列表数据隔离"""
        print("\n=== 测试商户列表数据隔离 ===")
        
        if not self.merchant_a_token:
            return
        
        # 测试商户A访问商户列表（应该被拒绝或只能看到自己）
        status_code, response = self.make_request("GET", "/merchants", self.merchant_a_token)
        
        if status_code == 200:
            merchants = response.get("data", {}).get("items", []) if isinstance(response.get("data"), dict) else response.get("items", [])
            
            if len(merchants) <= 1:
                self.results.append(format_test_result(
                    "商户列表数据隔离",
                    True,
                    f"商户A正确只能看到 {len(merchants)} 个商户（自己）",
                    {"merchant_count": len(merchants)}
                ))
                print(f"✅ 商户A正确只能看到 {len(merchants)} 个商户")
            else:
                self.results.append(format_test_result(
                    "商户列表数据隔离",
                    False,
                    f"商户A不应该看到 {len(merchants)} 个商户，存在数据泄露风险"
                ))
                print(f"❌ 商户A不应该看到 {len(merchants)} 个商户")
        elif status_code in [403, 401]:
            self.results.append(format_test_result(
                "商户列表访问权限控制",
                True,
                "正确拒绝商户访问商户列表"
            ))
            print("✅ 正确拒绝商户访问商户列表")
        else:
            self.results.append(format_test_result(
                "商户列表数据隔离",
                False,
                f"商户访问商户列表失败，状态码: {status_code}"
            ))
            print(f"❌ 商户访问商户列表失败，状态码: {status_code}")
    
    def test_cross_merchant_access_prevention(self):
        """测试跨商户访问防护"""
        print("\n=== 测试跨商户访问防护 ===")
        
        if not self.admin_token:
            return
        
        # 先获取所有商户列表
        status_code, response = self.make_request("GET", "/merchants", self.admin_token)
        if status_code != 200:
            self.results.append(format_test_result(
                "跨商户访问防护前置条件",
                False,
                "无法获取商户列表"
            ))
            return
        
        merchants = response.get("data", {}).get("items", []) if isinstance(response.get("data"), dict) else response.get("items", [])
        
        if len(merchants) < 2:
            self.results.append(format_test_result(
                "跨商户访问防护前置条件",
                False,
                f"商户数量不足（{len(merchants)}），无法测试跨商户访问"
            ))
            return
        
        # 尝试让商户A访问其他商户的详情
        if self.merchant_a_token:
            for merchant in merchants:
                merchant_id = merchant.get("id")
                if not merchant_id:
                    continue
                
                status_code, response = self.make_request(
                    "GET", f"/merchants/{merchant_id}", self.merchant_a_token
                )
                
                if status_code in [403, 401]:
                    self.results.append(format_test_result(
                        f"跨商户访问防护_{merchant_id}",
                        True,
                        f"正确阻止商户A访问商户{merchant_id}的详情"
                    ))
                    print(f"✅ 正确阻止商户A访问商户{merchant_id}的详情")
                elif status_code == 200:
                    # 检查返回的是否是商户A自己的信息
                    merchant_detail = response.get("data", response)
                    if merchant_detail.get("id") == merchant_id:
                        # 如果是自己的商户信息，这是正常的
                        self.results.append(format_test_result(
                            f"商户自身信息访问_{merchant_id}",
                            True,
                            f"商户A正确访问自己的商户信息"
                        ))
                        print(f"✅ 商户A正确访问自己的商户信息")
                    else:
                        self.results.append(format_test_result(
                            f"跨商户访问防护_{merchant_id}",
                            False,
                            f"商户A不应该能访问其他商户{merchant_id}的详情"
                        ))
                        print(f"❌ 商户A不应该能访问其他商户{merchant_id}的详情")
                else:
                    self.results.append(format_test_result(
                        f"跨商户访问测试_{merchant_id}",
                        False,
                        f"访问商户{merchant_id}详情失败，状态码: {status_code}"
                    ))
                    print(f"⚠️ 访问商户{merchant_id}详情失败，状态码: {status_code}")
    
    def test_sensitive_data_filtering(self):
        """测试敏感数据过滤"""
        print("\n=== 测试敏感数据过滤 ===")
        
        if not self.merchant_a_token:
            return
        
        # 测试获取用户信息时是否过滤了敏感字段
        status_code, response = self.make_request("GET", "/auth/me", self.merchant_a_token)
        
        if status_code == 200:
            user_info = response.get("data", response)
            
            # 检查是否包含敏感字段
            sensitive_fields = ["password", "hashed_password", "api_secret"]
            found_sensitive = [field for field in sensitive_fields if field in user_info]
            
            if not found_sensitive:
                self.results.append(format_test_result(
                    "敏感数据过滤",
                    True,
                    "用户信息正确过滤了敏感字段"
                ))
                print("✅ 用户信息正确过滤了敏感字段")
            else:
                self.results.append(format_test_result(
                    "敏感数据过滤",
                    False,
                    f"用户信息包含敏感字段: {found_sensitive}"
                ))
                print(f"❌ 用户信息包含敏感字段: {found_sensitive}")
        else:
            self.results.append(format_test_result(
                "敏感数据过滤测试",
                False,
                f"获取用户信息失败，状态码: {status_code}"
            ))
            print(f"❌ 获取用户信息失败，状态码: {status_code}")
    
    def run_all_tests(self):
        """运行所有数据隔离测试"""
        print("🚀 开始数据隔离测试")
        print("="*60)
        
        start_time = time.time()
        
        # 前置设置
        if not self.setup():
            print("❌ 测试前置设置失败，跳过测试")
            return []
        
        # 运行所有测试
        self.test_merchant_data_isolation()
        self.test_department_data_isolation()
        self.test_merchant_list_isolation()
        self.test_cross_merchant_access_prevention()
        self.test_sensitive_data_filtering()
        
        end_time = time.time()
        duration = end_time - start_time
        
        # 打印测试摘要
        print_test_summary(self.results)
        print(f"测试耗时: {duration:.2f}秒")
        
        return self.results

def main():
    """主函数"""
    test_suite = DataIsolationTestSuite()
    results = test_suite.run_all_tests()
    
    # 判断测试是否通过
    failed_tests = [r for r in results if not r.get("success", False)]
    
    if not failed_tests:
        print("\n🎉 所有数据隔离测试通过！")
        return 0
    else:
        print(f"\n⚠️ 发现 {len(failed_tests)} 个问题需要修复")
        for test in failed_tests:
            print(f"  - {test['test_name']}: {test['message']}")
        return 1

if __name__ == "__main__":
    sys.exit(main())
