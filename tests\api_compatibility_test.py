"""
前后端API兼容性测试
测试前端API调用与后端接口的兼容性
"""
import pytest
import requests
import json
from typing import Dict, Any

# 测试配置
BASE_URL = "http://localhost:20000"
API_PREFIX = "/api/v1"

class APICompatibilityTester:
    """API兼容性测试器"""
    
    def __init__(self):
        self.base_url = BASE_URL
        self.api_prefix = API_PREFIX
        self.token = None
        self.session = requests.Session()
    
    def login(self, username: str = "admin", password: str = "7c222fb2927d828af22f592134e8932480637c0d"):
        """登录获取token"""
        login_data = {
            "username": username,
            "password": password
        }
        
        response = self.session.post(
            f"{self.base_url}{self.api_prefix}/auth/login",
            data=login_data,
            headers={"Content-Type": "application/x-www-form-urlencoded"}
        )
        
        if response.status_code == 200:
            data = response.json()
            self.token = data.get("access_token")
            self.session.headers.update({"Authorization": f"Bearer {self.token}"})
            return True
        return False
    
    def test_response_format(self, endpoint: str, method: str = "GET", data: Dict = None) -> Dict[str, Any]:
        """测试API响应格式"""
        url = f"{self.base_url}{self.api_prefix}{endpoint}"
        
        if method.upper() == "GET":
            response = self.session.get(url)
        elif method.upper() == "POST":
            response = self.session.post(url, json=data)
        elif method.upper() == "PUT":
            response = self.session.put(url, json=data)
        elif method.upper() == "DELETE":
            response = self.session.delete(url)
        else:
            raise ValueError(f"不支持的HTTP方法: {method}")
        
        result = {
            "endpoint": endpoint,
            "method": method,
            "status_code": response.status_code,
            "response_data": None,
            "is_valid_format": False,
            "errors": []
        }
        
        try:
            response_data = response.json()
            result["response_data"] = response_data
            
            # 检查统一响应格式
            if isinstance(response_data, dict):
                if "code" in response_data and "message" in response_data:
                    result["is_valid_format"] = True
                else:
                    result["errors"].append("响应缺少code或message字段")
            else:
                result["errors"].append("响应不是JSON对象格式")
                
        except json.JSONDecodeError:
            result["errors"].append("响应不是有效的JSON格式")
        
        return result


def test_auth_endpoints():
    """测试认证相关接口"""
    tester = APICompatibilityTester()
    
    # 测试登录接口
    login_result = tester.test_response_format("/auth/login", "POST", {
        "username": "admin",
        "password": "7c222fb2927d828af22f592134e8932480637c0d"
    })
    
    assert login_result["status_code"] == 200, f"登录接口失败: {login_result}"
    
    # 登录成功后测试其他接口
    if tester.login():
        # 测试获取用户信息
        user_info_result = tester.test_response_format("/users/me")
        assert user_info_result["status_code"] == 200, f"获取用户信息失败: {user_info_result}"
        
        # 测试获取用户菜单
        menu_result = tester.test_response_format("/menus/user-menus")
        print(f"菜单接口测试结果: {menu_result}")


def test_department_endpoints():
    """测试部门管理接口"""
    tester = APICompatibilityTester()
    
    if tester.login():
        # 测试获取部门列表
        dept_list_result = tester.test_response_format("/departments")
        assert dept_list_result["status_code"] == 200, f"获取部门列表失败: {dept_list_result}"
        assert dept_list_result["is_valid_format"], f"部门列表响应格式不正确: {dept_list_result}"
        
        # 测试获取部门树
        dept_tree_result = tester.test_response_format("/departments?is_tree=true")
        assert dept_tree_result["status_code"] == 200, f"获取部门树失败: {dept_tree_result}"


def test_walmart_ck_endpoints():
    """测试沃尔玛CK管理接口"""
    tester = APICompatibilityTester()
    
    if tester.login():
        # 测试获取沃尔玛CK列表
        ck_list_result = tester.test_response_format("/walmart-ck")
        assert ck_list_result["status_code"] == 200, f"获取沃尔玛CK列表失败: {ck_list_result}"
        assert ck_list_result["is_valid_format"], f"沃尔玛CK列表响应格式不正确: {ck_list_result}"


def test_card_endpoints():
    """测试绑卡记录接口"""
    tester = APICompatibilityTester()
    
    if tester.login():
        # 测试获取绑卡记录列表
        card_list_result = tester.test_response_format("/cards")
        assert card_list_result["status_code"] == 200, f"获取绑卡记录列表失败: {card_list_result}"
        assert card_list_result["is_valid_format"], f"绑卡记录列表响应格式不正确: {card_list_result}"


def test_user_endpoints():
    """测试用户管理接口"""
    tester = APICompatibilityTester()
    
    if tester.login():
        # 测试获取用户列表
        user_list_result = tester.test_response_format("/users")
        assert user_list_result["status_code"] == 200, f"获取用户列表失败: {user_list_result}"
        assert user_list_result["is_valid_format"], f"用户列表响应格式不正确: {user_list_result}"


if __name__ == "__main__":
    print("开始API兼容性测试...")
    
    try:
        test_auth_endpoints()
        print("✅ 认证接口测试通过")
        
        test_department_endpoints()
        print("✅ 部门管理接口测试通过")
        
        test_walmart_ck_endpoints()
        print("✅ 沃尔玛CK管理接口测试通过")
        
        test_card_endpoints()
        print("✅ 绑卡记录接口测试通过")
        
        test_user_endpoints()
        print("✅ 用户管理接口测试通过")
        
        print("\n🎉 所有API兼容性测试通过！")
        
    except Exception as e:
        print(f"❌ API兼容性测试失败: {str(e)}")
        raise
