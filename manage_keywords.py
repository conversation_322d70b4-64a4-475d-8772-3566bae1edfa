#!/usr/bin/env python3
"""
Telegram机器人关键词管理脚本
提供命令行界面来管理关键词
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app.telegram_bot.keywords import get_telegram_keywords, check_keyword_match


def show_help():
    """显示帮助信息"""
    print("🔧 Telegram机器人关键词管理工具")
    print("=" * 50)
    print("用法: python manage_keywords.py <命令> [参数]")
    print()
    print("命令:")
    print("  list                    - 显示所有关键词")
    print("  categories              - 按类别显示关键词")
    print("  stats                   - 显示统计信息")
    print("  test <消息>             - 测试消息是否匹配关键词")
    print("  add <关键词>            - 添加新关键词")
    print("  remove <关键词>         - 移除关键词")
    print("  help                    - 显示此帮助信息")
    print()
    print("示例:")
    print("  python manage_keywords.py list")
    print("  python manage_keywords.py test '查看状态'")
    print("  python manage_keywords.py add '新关键词'")


def list_keywords():
    """列出所有关键词"""
    keyword_manager = get_telegram_keywords()
    keywords = keyword_manager.get_keywords()
    
    print("📝 所有关键词列表:")
    print("=" * 50)
    for i, keyword in enumerate(keywords, 1):
        print(f"{i:2d}. '{keyword}'")
    print(f"\n总计: {len(keywords)} 个关键词")


def show_categories():
    """按类别显示关键词"""
    keyword_manager = get_telegram_keywords()
    categories = keyword_manager.get_keywords_by_category()
    
    print("📂 按类别显示关键词:")
    print("=" * 50)
    for category, keywords in categories.items():
        print(f"\n{category} ({len(keywords)} 个):")
        for keyword in keywords:
            print(f"  - '{keyword}'")


def show_stats():
    """显示统计信息"""
    keyword_manager = get_telegram_keywords()
    stats = keyword_manager.get_statistics()
    
    print("📊 关键词统计信息:")
    print("=" * 50)
    print(f"总关键词数: {stats['total_keywords']}")
    print("\n分类统计:")
    for category, count in stats['categories'].items():
        print(f"  {category}: {count} 个")


def test_message(message):
    """测试消息是否匹配关键词"""
    has_match, matched_keywords = check_keyword_match(message)
    
    print(f"🧪 测试消息: '{message}'")
    print("=" * 50)
    if has_match:
        print("✅ 匹配结果: 会触发机器人回复")
        print(f"匹配的关键词: {matched_keywords}")
    else:
        print("❌ 匹配结果: 不会触发机器人回复")
        print("未匹配任何关键词")


def add_keyword(keyword):
    """添加新关键词"""
    keyword_manager = get_telegram_keywords()
    
    print(f"➕ 添加关键词: '{keyword}'")
    print("=" * 50)
    
    result = keyword_manager.add_keyword(keyword)
    if result:
        print("✅ 添加成功")
        # 测试新关键词
        has_match, matched = check_keyword_match(f"测试{keyword}")
        if has_match:
            print(f"✅ 验证成功: 新关键词可以正常匹配")
        else:
            print(f"⚠️ 验证警告: 新关键词可能无法正常匹配")
    else:
        print("❌ 添加失败: 关键词已存在或无效")


def remove_keyword(keyword):
    """移除关键词"""
    keyword_manager = get_telegram_keywords()
    
    print(f"➖ 移除关键词: '{keyword}'")
    print("=" * 50)
    
    result = keyword_manager.remove_keyword(keyword)
    if result:
        print("✅ 移除成功")
        # 验证关键词已移除
        has_match, matched = check_keyword_match(f"测试{keyword}")
        if not has_match:
            print(f"✅ 验证成功: 关键词已完全移除")
        else:
            print(f"⚠️ 验证警告: 关键词可能仍然存在")
    else:
        print("❌ 移除失败: 关键词不存在")


def interactive_mode():
    """交互模式"""
    print("🔧 Telegram机器人关键词管理工具 - 交互模式")
    print("=" * 50)
    print("输入 'help' 查看命令，输入 'quit' 退出")
    print()
    
    while True:
        try:
            command = input(">>> ").strip()
            if not command:
                continue
                
            if command.lower() in ['quit', 'exit', 'q']:
                print("👋 再见！")
                break
            elif command.lower() == 'help':
                show_help()
            elif command.lower() == 'list':
                list_keywords()
            elif command.lower() == 'categories':
                show_categories()
            elif command.lower() == 'stats':
                show_stats()
            elif command.lower().startswith('test '):
                message = command[5:].strip()
                if message:
                    test_message(message)
                else:
                    print("❌ 请提供要测试的消息")
            elif command.lower().startswith('add '):
                keyword = command[4:].strip()
                if keyword:
                    add_keyword(keyword)
                else:
                    print("❌ 请提供要添加的关键词")
            elif command.lower().startswith('remove '):
                keyword = command[7:].strip()
                if keyword:
                    remove_keyword(keyword)
                else:
                    print("❌ 请提供要移除的关键词")
            else:
                print(f"❌ 未知命令: {command}")
                print("输入 'help' 查看可用命令")
            print()
        except KeyboardInterrupt:
            print("\n👋 再见！")
            break
        except Exception as e:
            print(f"❌ 错误: {e}")


def main():
    """主函数"""
    if len(sys.argv) < 2:
        # 没有参数，进入交互模式
        interactive_mode()
        return
    
    command = sys.argv[1].lower()
    
    if command == 'help':
        show_help()
    elif command == 'list':
        list_keywords()
    elif command == 'categories':
        show_categories()
    elif command == 'stats':
        show_stats()
    elif command == 'test':
        if len(sys.argv) < 3:
            print("❌ 请提供要测试的消息")
            print("用法: python manage_keywords.py test '消息内容'")
        else:
            message = ' '.join(sys.argv[2:])
            test_message(message)
    elif command == 'add':
        if len(sys.argv) < 3:
            print("❌ 请提供要添加的关键词")
            print("用法: python manage_keywords.py add '关键词'")
        else:
            keyword = ' '.join(sys.argv[2:])
            add_keyword(keyword)
    elif command == 'remove':
        if len(sys.argv) < 3:
            print("❌ 请提供要移除的关键词")
            print("用法: python manage_keywords.py remove '关键词'")
        else:
            keyword = ' '.join(sys.argv[2:])
            remove_keyword(keyword)
    else:
        print(f"❌ 未知命令: {command}")
        show_help()


if __name__ == "__main__":
    main()
