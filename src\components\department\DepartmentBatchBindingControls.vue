<template>
  <div class="batch-binding-controls">
    <el-dialog title="批量管理部门绑卡控制" :model-value="visible" @update:model-value="updateVisible" width="800px"
      :before-close="handleClose">
      <!-- 选择部门 -->
      <div class="section">
        <h4>选择部门</h4>
        <el-transfer v-model="selectedDepartmentIds" :data="departmentOptions" :titles="['可选部门', '已选部门']"
          :button-texts="['移除', '添加']" :format="{
            noChecked: '${total}',
            hasChecked: '${checked}/${total}'
          }" filterable filter-placeholder="搜索部门" style="text-align: left; display: inline-block">
          <template #default="{ option }">
            <span>{{ option.label }}</span>
            <span class="transfer-desc">
              (权重: {{ option.weight }}, CK: {{ option.ckCount }})
            </span>
          </template>
        </el-transfer>
      </div>

      <!-- 批量操作设置 -->
      <div class="section">
        <h4>批量操作设置</h4>
        <el-form :model="batchForm" label-width="120px">
          <el-form-item label="进单开关">
            <el-radio-group v-model="batchForm.enableBindingAction">
              <el-radio label="keep">保持不变</el-radio>
              <el-radio label="enable">全部启用</el-radio>
              <el-radio label="disable">全部禁用</el-radio>
            </el-radio-group>
          </el-form-item>

          <el-form-item label="权重设置">
            <el-radio-group v-model="batchForm.weightAction">
              <el-radio label="keep">保持不变</el-radio>
              <el-radio label="set">设置为固定值</el-radio>
              <el-radio label="multiply">按比例调整</el-radio>
            </el-radio-group>
          </el-form-item>

          <el-form-item v-if="batchForm.weightAction === 'set'" label="固定权重值">
            <el-input-number v-model="batchForm.fixedWeight" :min="0" :max="10000" :step="10" />
          </el-form-item>

          <el-form-item v-if="batchForm.weightAction === 'multiply'" label="调整比例">
            <el-input-number v-model="batchForm.weightMultiplier" :min="0.1" :max="10" :step="0.1" :precision="1" />
            <span class="form-tip">例如：1.5表示增加50%，0.8表示减少20%</span>
          </el-form-item>
        </el-form>
      </div>

      <!-- 预览变更 -->
      <div class="section" v-if="selectedDepartmentIds.length > 0">
        <h4>预览变更</h4>
        <el-table :data="previewData" size="small" max-height="300" border>
          <el-table-column prop="name" label="部门名称" width="150" />
          <el-table-column label="进单开关" width="120">
            <template #default="{ row }">
              <span :class="getChangeClass(row.enableBinding)">
                {{ row.enableBinding.new ? '启用' : '禁用' }}
              </span>
              <span v-if="row.enableBinding.changed" class="change-arrow">
                ({{ row.enableBinding.old ? '启用' : '禁用' }} → {{ row.enableBinding.new ? '启用' : '禁用' }})
              </span>
            </template>
          </el-table-column>
          <el-table-column label="权重" width="150">
            <template #default="{ row }">
              <span :class="getChangeClass(row.weight)">
                {{ row.weight.new }}
              </span>
              <span v-if="row.weight.changed" class="change-arrow">
                ({{ row.weight.old }} → {{ row.weight.new }})
              </span>
            </template>
          </el-table-column>
          <el-table-column prop="ckCount" label="CK数量" width="80" />
        </el-table>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="handleClose">取消</el-button>
          <el-button type="primary" @click="executeBatchUpdate" :loading="executing"
            :disabled="selectedDepartmentIds.length === 0 || !hasValidChanges">
            执行批量更新 ({{ selectedDepartmentIds.length }}个部门)
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import { departmentApi } from '@/api/modules/department'

export default {
  name: 'DepartmentBatchBindingControls',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    departments: {
      type: Array,
      default: () => []
    }
  },
  emits: ['update:visible', 'updated'],
  data() {
    return {
      selectedDepartmentIds: [],
      executing: false,
      batchForm: {
        enableBindingAction: 'keep',
        weightAction: 'keep',
        fixedWeight: 100,
        weightMultiplier: 1.0
      }
    }
  },
  computed: {
    departmentOptions() {
      return this.departments.map(dept => ({
        key: dept.id,
        label: dept.name,
        weight: dept.binding_weight || 100,
        ckCount: dept.available_ck_count || 0,
        enableBinding: dept.enable_binding !== false
      }))
    },

    previewData() {
      return this.selectedDepartmentIds.map(deptId => {
        const dept = this.departments.find(d => d.id === deptId)
        if (!dept) return null

        const oldEnableBinding = dept.enable_binding !== false
        const oldWeight = dept.binding_weight || 100

        let newEnableBinding = oldEnableBinding
        let newWeight = oldWeight

        // 计算新的开关状态
        if (this.batchForm.enableBindingAction === 'enable') {
          newEnableBinding = true
        } else if (this.batchForm.enableBindingAction === 'disable') {
          newEnableBinding = false
        }

        // 计算新的权重
        if (this.batchForm.weightAction === 'set') {
          newWeight = this.batchForm.fixedWeight
        } else if (this.batchForm.weightAction === 'multiply') {
          newWeight = Math.round(oldWeight * this.batchForm.weightMultiplier)
          newWeight = Math.max(0, Math.min(10000, newWeight)) // 限制范围
        }

        return {
          id: dept.id,
          name: dept.name,
          enableBinding: {
            old: oldEnableBinding,
            new: newEnableBinding,
            changed: oldEnableBinding !== newEnableBinding
          },
          weight: {
            old: oldWeight,
            new: newWeight,
            changed: oldWeight !== newWeight
          },
          ckCount: dept.available_ck_count || 0
        }
      }).filter(Boolean)
    },

    hasValidChanges() {
      return this.previewData.some(item =>
        item.enableBinding.changed || item.weight.changed
      )
    }
  },
  watch: {
    visible(newVal) {
      if (newVal) {
        this.resetForm()
      }
    }
  },
  methods: {
    resetForm() {
      this.selectedDepartmentIds = []
      this.batchForm = {
        enableBindingAction: 'keep',
        weightAction: 'keep',
        fixedWeight: 100,
        weightMultiplier: 1.0
      }
    },

    getChangeClass(changeInfo) {
      return {
        'text-success': changeInfo.changed && changeInfo.new > changeInfo.old,
        'text-warning': changeInfo.changed && changeInfo.new < changeInfo.old,
        'text-info': changeInfo.changed
      }
    },

    async executeBatchUpdate() {
      if (this.selectedDepartmentIds.length === 0) {
        this.$message.warning('请选择要更新的部门')
        return
      }

      if (!this.hasValidChanges) {
        this.$message.warning('没有检测到任何变更')
        return
      }

      try {
        this.executing = true

        // 构建批量更新数据
        const updateData = {
          department_ids: this.selectedDepartmentIds
        }

        // 添加开关设置
        if (this.batchForm.enableBindingAction !== 'keep') {
          updateData.enable_binding = this.batchForm.enableBindingAction === 'enable'
        }

        // 添加权重设置
        if (this.batchForm.weightAction === 'set') {
          updateData.binding_weight = this.batchForm.fixedWeight
        }
        // 注意：比例调整需要在后端实现，这里暂时不支持

        const response = await departmentApi.batchUpdateBindingControls(updateData)

        if (response.success) {
          this.$message.success(`成功更新 ${response.updated_count} 个部门的绑卡控制设置`)
          this.$emit('updated', response)
          this.handleClose()
        } else {
          this.$message.error('批量更新失败：' + response.message)
        }
      } catch (error) {
        this.$message.error('批量更新失败：' + error.message)
      } finally {
        this.executing = false
      }
    },

    updateVisible(value) {
      this.$emit('update:visible', value)
    },

    handleClose() {
      this.$emit('update:visible', false)
    }
  }
}
</script>

<style scoped>
.batch-binding-controls {
  .section {
    margin-bottom: 30px;

    h4 {
      margin-bottom: 15px;
      color: #303133;
      font-size: 14px;
      font-weight: 600;
    }
  }

  .transfer-desc {
    color: #909399;
    font-size: 12px;
    margin-left: 10px;
  }

  .form-tip {
    color: #909399;
    font-size: 12px;
    margin-left: 10px;
  }

  .change-arrow {
    color: #409EFF;
    font-size: 12px;
    margin-left: 5px;
  }

  .text-success {
    color: #67C23A;
  }

  .text-warning {
    color: #E6A23C;
  }

  .text-info {
    color: #409EFF;
  }

  .dialog-footer {
    text-align: right;
  }
}

:deep(.el-transfer-panel) {
  width: 300px;
}

:deep(.el-transfer-panel__list) {
  height: 300px;
}
</style>
