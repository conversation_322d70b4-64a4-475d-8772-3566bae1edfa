import aio_pika
import asyncio
import json
import time
from contextlib import asynccontextmanager
from app.services.binding_service import binding_service
from app.services.callback_service_adapter import callback_service_adapter  # 使用适配器
from app.db.session import AsyncSessionLocal
from app.core.config import settings
from app.core.logging import get_logger

logger = get_logger("queue_consumer")

# 添加并发控制，防止过多并发请求导致系统过载
# 从配置文件读取并发数量，默认值为1（保守设置避免死机）
MAX_CONCURRENT_BINDINGS = settings.yaml_config.get("business", {}).get("binding", {}).get("queue_max_concurrency", 1)
binding_semaphore = asyncio.Semaphore(MAX_CONCURRENT_BINDINGS)

# 添加性能监控
performance_stats = {
    "total_processed": 0,
    "total_errors": 0,
    "avg_processing_time": 0.0,
    "concurrent_count": 0
}

@asynccontextmanager
async def safe_db_session():
    """安全的数据库会话管理器，包含重试机制"""
    max_retries = 3
    for attempt in range(max_retries):
        try:
            async with AsyncSessionLocal() as db:
                yield db
                break
        except Exception as e:
            if attempt == max_retries - 1:
                logger.error(f"数据库会话创建失败，已重试{max_retries}次: {e}")
                raise
            logger.warning(f"数据库会话创建失败，第{attempt + 1}次重试: {e}")
            await asyncio.sleep(0.5 * (attempt + 1))  # 指数退避


async def process_bind_card_message(message: aio_pika.IncomingMessage):
    """处理绑卡消息，包含完整的并发安全和错误处理机制"""
    start_time = time.time()
    message_id = message.message_id or "unknown"

    # 使用信号量控制并发数量，防止系统过载
    async with binding_semaphore:
        performance_stats["concurrent_count"] += 1
        try:
            async with message.process(requeue=True):
                try:
                    # 解析消息数据
                    data = json.loads(message.body)
                    trace_id = data.get('trace_id', message_id)

                    logger.info(f"开始处理绑卡消息: trace_id={trace_id}, message_id={message_id}")

                    # 使用安全的数据库会话管理器
                    async with safe_db_session() as db:
                        await binding_service.process_from_queue(db, data)

                    # 更新性能统计
                    processing_time = time.time() - start_time
                    performance_stats["total_processed"] += 1
                    performance_stats["avg_processing_time"] = (
                        (performance_stats["avg_processing_time"] * (performance_stats["total_processed"] - 1) + processing_time)
                        / performance_stats["total_processed"]
                    )

                    logger.info(f"绑卡消息处理完成: trace_id={trace_id}, 耗时={processing_time:.2f}s")

                except json.JSONDecodeError as e:
                    logger.error(f"消息格式错误，丢弃消息: {e}")
                    # JSON解析错误不需要requeue
                    return
                except Exception as e:
                    performance_stats["total_errors"] += 1
                    processing_time = time.time() - start_time

                    # 判断是否为可重试的错误
                    if _is_retryable_error(e):
                        logger.warning(f"绑卡队列消息处理异常（将重试）: {e}, 耗时={processing_time:.2f}s")
                        raise  # 让消息requeue
                    else:
                        logger.error(f"绑卡队列消息处理失败（不可重试）: {e}, 耗时={processing_time:.2f}s")
                        # 不可重试的错误，记录到死信队列或日志
                        return
        finally:
            performance_stats["concurrent_count"] -= 1


def _is_retryable_error(error: Exception) -> bool:
    """判断错误是否可重试"""
    retryable_errors = [
        "connection", "timeout", "temporary", "网络", "连接", "超时", "临时"
    ]
    error_str = str(error).lower()
    return any(keyword in error_str for keyword in retryable_errors)


async def process_callback_message(message: aio_pika.IncomingMessage):
    async with message.process(requeue=True):
        try:
            data = json.loads(message.body)
            # 业务处理：使用回调服务适配器 - 使用异步数据库会话
            async with AsyncSessionLocal() as db:
                await callback_service_adapter.process_callback_from_queue(db, data)
        except Exception as e:
            logger.exception(f"回调队列消息处理异常: {e}")
            raise  # 让消息requeue


async def start_bind_card_consumer():
    """启动绑卡队列消费者"""
    # 检查绑卡队列消费者是否启用
    if not settings.BUSINESS_BINDING_ENABLED:
        logger.info("绑卡队列消费者已禁用，跳过启动")
        return

    logger.info("绑卡队列消费者功能已启用，正在启动...")
    connection = None
    try:
        logger.info(f"正在连接RabbitMQ: {settings.RABBITMQ_URL}")
        connection = await aio_pika.connect_robust(
            settings.RABBITMQ_URL,
            connection_attempts=3,
            retry_delay=5
        )
        channel = await connection.channel()
        await channel.set_qos(prefetch_count=settings.RABBITMQ_CONSUMER_PREFETCH_COUNT)
        queue = await channel.declare_queue(settings.RABBITMQ_QUEUE_BIND_CARD, durable=True)
        await queue.consume(process_bind_card_message, no_ack=False)
        logger.info(
            f"绑卡队列消费者已启动，prefetch_count={settings.RABBITMQ_CONSUMER_PREFETCH_COUNT}"
        )

        # 创建一个可以被取消的Future
        stop_future = asyncio.Future()
        try:
            await stop_future
        except asyncio.CancelledError:
            logger.info("绑卡队列消费者收到取消信号")
            raise

    except asyncio.CancelledError:
        logger.info("绑卡队列消费者被取消")
        raise
    except Exception as e:
        logger.error(f"绑卡队列消费者启动失败: {str(e)}")
        raise
    finally:
        if connection and not connection.is_closed:
            logger.info("正在关闭绑卡队列连接")
            await connection.close()


async def start_callback_consumer():
    """启动回调队列消费者"""
    logger.info("回调队列消费者功能已启用，正在启动...")
    connection = None
    try:
        logger.info(f"正在连接RabbitMQ: {settings.RABBITMQ_URL}")
        connection = await aio_pika.connect_robust(
            settings.RABBITMQ_URL,
            connection_attempts=3,
            retry_delay=5
        )
        channel = await connection.channel()
        # 使用专门的回调队列预取配置
        callback_prefetch = getattr(settings, 'RABBITMQ_CALLBACK_CONSUMER_PREFETCH_COUNT', 100)
        await channel.set_qos(prefetch_count=callback_prefetch)

        # 声明交换机
        exchange = await channel.declare_exchange(
            "callback_exchange", aio_pika.ExchangeType.DIRECT, durable=True
        )

        # 声明队列
        queue = await channel.declare_queue(settings.RABBITMQ_QUEUE_CALLBACK, durable=True)

        # 绑定队列到交换机
        await queue.bind(exchange, routing_key=settings.RABBITMQ_QUEUE_CALLBACK)

        # 消费消息
        await queue.consume(process_callback_message, no_ack=False)

        logger.info(
            f"回调队列消费者已启动，prefetch_count={settings.RABBITMQ_CONSUMER_PREFETCH_COUNT}"
        )

        # 创建一个可以被取消的Future
        stop_future = asyncio.Future()
        try:
            await stop_future
        except asyncio.CancelledError:
            logger.info("回调队列消费者收到取消信号")
            raise

    except asyncio.CancelledError:
        logger.info("回调队列消费者被取消")
        raise
    except Exception as e:
        logger.error(f"回调队列消费者启动失败: {str(e)}")
        raise
    finally:
        if connection and not connection.is_closed:
            logger.info("正在关闭回调队列连接")
            await connection.close()
