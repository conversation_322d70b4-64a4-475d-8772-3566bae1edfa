"""
部门编辑功能测试 - 专门测试parent_id更新功能
"""
import requests
import time
from test.conftest import TestBase, format_test_result, print_test_summary


class TestDepartmentEditParent(TestBase):
    """部门编辑parent_id功能测试类"""

    def __init__(self):
        super().__init__()
        self.results = []
        self.admin_token = None
        self.test_departments = []

    def setup(self):
        """测试前置设置"""
        print("🔧 开始测试前置设置...")

        # 登录超级管理员
        self.admin_token = self.login("admin", "7c222fb2927d828af22f592134e8932480637c0d")
        if not self.admin_token:
            print("❌ 超级管理员登录失败")
            return False

        print("✅ 超级管理员登录成功")

        # 获取现有部门列表
        status_code, response = self.make_request("GET", "/departments", self.admin_token)
        if status_code != 200:
            print("❌ 获取部门列表失败")
            return False

        departments_data = response.get("data", {})
        departments = departments_data.get("items", []) if isinstance(departments_data, dict) else response.get("items", [])

        if len(departments) < 2:
            print("❌ 测试需要至少2个部门，请先创建测试数据")
            return False

        # 选择测试部门
        self.test_departments = departments[:3]  # 取前3个部门进行测试
        print(f"✅ 找到 {len(self.test_departments)} 个测试部门")

        return True

    def test_update_department_parent_id_success(self):
        """测试成功更新部门的parent_id"""
        if len(self.test_departments) < 3:
            self.results.append(format_test_result(
                "更新部门parent_id",
                False,
                "测试部门数量不足"
            ))
            return

        # 选择要测试的部门
        target_dept = self.test_departments[2]  # 第三个部门
        new_parent = self.test_departments[1]   # 第二个部门作为新父部门

        # 记录原始状态
        original_parent_id = target_dept.get("parent_id")

        # 更新部门的parent_id
        update_data = {
            "parent_id": new_parent["id"]
        }

        status_code, response = self.make_request(
            "PUT",
            f"/departments/{target_dept['id']}",
            self.admin_token,
            data=update_data
        )

        if status_code == 200:
            updated_dept = response.get("data", response)

            # 验证parent_id是否更新成功
            if updated_dept.get("parent_id") == new_parent["id"]:
                self.results.append(format_test_result(
                    "更新部门parent_id",
                    True,
                    f"成功将部门 {target_dept['name']} 的上级部门更新为 {new_parent['name']}",
                    {
                        "department_id": target_dept["id"],
                        "old_parent_id": original_parent_id,
                        "new_parent_id": new_parent["id"]
                    }
                ))
                print(f"✅ 成功更新部门 {target_dept['name']} 的上级部门")

                # 恢复原始状态
                restore_data = {"parent_id": original_parent_id}
                self.make_request("PUT", f"/departments/{target_dept['id']}", self.admin_token, data=restore_data)
            else:
                self.results.append(format_test_result(
                    "更新部门parent_id",
                    False,
                    "parent_id更新后值不正确"
                ))
                print("❌ parent_id更新后值不正确")
        else:
            self.results.append(format_test_result(
                "更新部门parent_id",
                False,
                f"更新部门失败，状态码: {status_code}",
                {"response": response}
            ))
            print(f"❌ 更新部门失败，状态码: {status_code}")

    def test_update_department_with_other_fields(self):
        """测试同时更新parent_id和其他字段"""
        if len(self.test_departments) < 2:
            self.results.append(format_test_result(
                "同时更新多个字段",
                False,
                "测试部门数量不足"
            ))
            return

        target_dept = self.test_departments[0]
        new_parent = self.test_departments[1]

        # 记录原始状态
        original_name = target_dept.get("name")
        original_parent_id = target_dept.get("parent_id")
        original_description = target_dept.get("description")

        # 同时更新多个字段
        update_data = {
            "name": f"{original_name}_测试更新",
            "description": "测试同时更新多个字段",
            "parent_id": new_parent["id"]
        }

        status_code, response = self.make_request(
            "PUT",
            f"/departments/{target_dept['id']}",
            self.admin_token,
            data=update_data
        )

        if status_code == 200:
            updated_dept = response.get("data", response)

            # 验证所有字段都更新了
            name_updated = updated_dept.get("name") == update_data["name"]
            description_updated = updated_dept.get("description") == update_data["description"]
            parent_updated = updated_dept.get("parent_id") == update_data["parent_id"]

            if name_updated and description_updated and parent_updated:
                self.results.append(format_test_result(
                    "同时更新多个字段",
                    True,
                    "成功同时更新名称、描述和上级部门"
                ))
                print("✅ 成功同时更新多个字段")

                # 恢复原始状态
                restore_data = {
                    "name": original_name,
                    "description": original_description,
                    "parent_id": original_parent_id
                }
                self.make_request("PUT", f"/departments/{target_dept['id']}", self.admin_token, data=restore_data)
            else:
                self.results.append(format_test_result(
                    "同时更新多个字段",
                    False,
                    "部分字段更新失败"
                ))
                print("❌ 部分字段更新失败")
        else:
            self.results.append(format_test_result(
                "同时更新多个字段",
                False,
                f"更新失败，状态码: {status_code}"
            ))
            print(f"❌ 更新失败，状态码: {status_code}")

    def test_update_department_invalid_parent(self):
        """测试无效的父部门ID"""
        if not self.test_departments:
            self.results.append(format_test_result(
                "无效父部门ID测试",
                False,
                "测试部门数量不足"
            ))
            return

        target_dept = self.test_departments[0]

        # 使用不存在的父部门ID
        update_data = {
            "parent_id": 99999
        }

        status_code, response = self.make_request(
            "PUT",
            f"/departments/{target_dept['id']}",
            self.admin_token,
            data=update_data
        )

        # 应该返回错误状态码
        if status_code in [400, 404]:
            self.results.append(format_test_result(
                "无效父部门ID测试",
                True,
                "正确拒绝了无效的父部门ID"
            ))
            print("✅ 正确拒绝了无效的父部门ID")
        else:
            self.results.append(format_test_result(
                "无效父部门ID测试",
                False,
                f"应该拒绝无效父部门ID，但返回状态码: {status_code}"
            ))
            print(f"❌ 应该拒绝无效父部门ID，但返回状态码: {status_code}")

    def run_all_tests(self):
        """运行所有测试"""
        print("🚀 开始部门编辑parent_id功能测试")
        print("="*60)

        start_time = time.time()

        # 前置设置
        if not self.setup():
            print("❌ 测试前置设置失败，跳过测试")
            return []

        # 运行所有测试
        self.test_update_department_parent_id_success()
        self.test_update_department_with_other_fields()
        self.test_update_department_invalid_parent()

        end_time = time.time()
        duration = end_time - start_time

        # 打印测试摘要
        print_test_summary(self.results)
        print(f"测试耗时: {duration:.2f}秒")

        return self.results


if __name__ == "__main__":
    # 运行测试
    test = TestDepartmentEditParent()
    results = test.run_all_tests()
