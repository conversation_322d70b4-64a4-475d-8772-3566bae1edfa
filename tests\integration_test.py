#!/usr/bin/env python3
"""
沃尔玛绑卡系统集成测试
测试完整的绑卡流程和商户CK隔离机制
"""

import sys
import os
import uuid
import requests
import json
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from app.core.config import settings
from app.models.user import User
from app.models.merchant import Merchant
from app.models.department import Department
from app.models.walmart_ck import WalmartCK
from app.models.card_record import CardRecord


def create_session():
    """创建数据库会话"""
    engine = create_engine(settings.SQLALCHEMY_DATABASE_URI)
    SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
    return SessionLocal()


def get_auth_token(username="admin", password="7c222fb2927d828af22f592134e8932480637c0d"):
    """获取认证token"""
    login_url = "http://localhost:8000/api/v1/auth/login"
    login_data = {
        "username": username,
        "password": password
    }
    
    try:
        response = requests.post(login_url, json=login_data)
        if response.status_code == 200:
            result = response.json()
            return result.get("access_token")
        else:
            print(f"登录失败: {response.status_code} - {response.text}")
            return None
    except Exception as e:
        print(f"登录请求失败: {e}")
        return None


def test_api_merchant_isolation_check(token, merchant_id):
    """测试API商户隔离检查"""
    print(f"\n=== 测试API商户隔离检查 (商户ID: {merchant_id}) ===")
    
    url = f"http://localhost:8000/api/v1/walmart-ck/merchant/{merchant_id}/isolation-check"
    headers = {"Authorization": f"Bearer {token}"}
    
    try:
        response = requests.get(url, headers=headers)
        if response.status_code == 200:
            result = response.json()
            print(f"✅ API调用成功")
            print(f"检查结果: {json.dumps(result['data'], indent=2, ensure_ascii=False)}")
            return result['data']
        else:
            print(f"❌ API调用失败: {response.status_code} - {response.text}")
            return None
    except Exception as e:
        print(f"❌ API请求异常: {e}")
        return None


def test_api_ck_validation(token, ck_id, merchant_id):
    """测试API CK验证"""
    print(f"\n=== 测试API CK验证 (CK: {ck_id}, 商户: {merchant_id}) ===")
    
    url = "http://localhost:8000/api/v1/walmart-ck/validate-isolation"
    headers = {"Authorization": f"Bearer {token}"}
    params = {
        "ck_id": ck_id,
        "merchant_id": merchant_id
    }
    
    try:
        response = requests.post(url, headers=headers, params=params)
        if response.status_code == 200:
            result = response.json()
            print(f"✅ API调用成功")
            print(f"验证结果: {json.dumps(result['data'], indent=2, ensure_ascii=False)}")
            return result['data']
        else:
            print(f"❌ API调用失败: {response.status_code} - {response.text}")
            return None
    except Exception as e:
        print(f"❌ API请求异常: {e}")
        return None


def test_card_binding_with_ck_tracking(db):
    """测试绑卡流程中的CK追踪"""
    print("\n=== 测试绑卡流程CK追踪 ===")
    
    # 查找一个有CK的商户
    merchant_with_ck = db.query(Merchant).join(WalmartCK).first()
    if not merchant_with_ck:
        print("❌ 没有找到有CK的商户，跳过绑卡测试")
        return
    
    print(f"使用商户: {merchant_with_ck.id}({merchant_with_ck.name})")
    
    # 获取该商户的CK
    ck = db.query(WalmartCK).filter(
        WalmartCK.merchant_id == merchant_with_ck.id,
        WalmartCK.active == True
    ).first()
    
    if not ck:
        print("❌ 商户没有可用的CK，跳过绑卡测试")
        return
    
    print(f"使用CK: {ck.id}")
    
    # 创建测试卡记录
    test_card = CardRecord(
        id=str(uuid.uuid4()),
        merchant_id=merchant_with_ck.id,
        department_id=ck.department_id,
        walmart_ck_id=None,  # 初始为空，绑卡时应该填充
        merchant_order_id=f"INTEGRATION_TEST_{int(datetime.now().timestamp())}",
        amount=10000,
        card_number="1111222233334444",
        status='pending',
        request_id=str(uuid.uuid4()),
        request_data={"test": "integration_test"}
    )
    
    db.add(test_card)
    db.commit()
    
    print(f"创建测试卡记录: {test_card.id}")
    
    # 模拟绑卡成功，更新CK信息
    test_card.walmart_ck_id = ck.id
    test_card.status = 'success'
    db.commit()
    
    print(f"✅ 模拟绑卡成功，CK ID已记录: {test_card.walmart_ck_id}")
    
    # 验证CK记录是否正确
    updated_card = db.query(CardRecord).filter(CardRecord.id == test_card.id).first()
    if updated_card.walmart_ck_id == ck.id:
        print("✅ CK追踪记录正确")
    else:
        print("❌ CK追踪记录错误")
    
    # 清理测试数据
    db.delete(test_card)
    db.commit()
    print("✅ 测试数据已清理")


def test_cross_merchant_prevention(db):
    """测试跨商户操作防护"""
    print("\n=== 测试跨商户操作防护 ===")
    
    # 获取两个不同的商户
    merchants = db.query(Merchant).limit(2).all()
    if len(merchants) < 2:
        print("❌ 商户数量不足，跳过跨商户测试")
        return
    
    merchant1, merchant2 = merchants[0], merchants[1]
    print(f"商户1: {merchant1.id}({merchant1.name})")
    print(f"商户2: {merchant2.id}({merchant2.name})")
    
    # 获取商户1的CK
    ck1 = db.query(WalmartCK).filter(WalmartCK.merchant_id == merchant1.id).first()
    if not ck1:
        print("❌ 商户1没有CK，跳过测试")
        return
    
    print(f"商户1的CK: {ck1.id}")
    
    # 创建商户2的卡记录
    test_card = CardRecord(
        id=str(uuid.uuid4()),
        merchant_id=merchant2.id,
        department_id=None,
        walmart_ck_id=None,
        merchant_order_id=f"CROSS_MERCHANT_TEST_{int(datetime.now().timestamp())}",
        amount=10000,
        card_number="9999888877776666",
        status='pending',
        request_id=str(uuid.uuid4()),
        request_data={"test": "cross_merchant_test"}
    )
    
    db.add(test_card)
    db.commit()
    
    print(f"创建商户2的卡记录: {test_card.id}")
    
    # 尝试使用商户1的CK（应该被阻止）
    from app.services.card_record_service import CardRecordService
    from app.services.user_service import UserService
    
    # 获取商户2的用户
    user_service = UserService(db)
    merchant2_user = db.query(User).filter(User.merchant_id == merchant2.id).first()
    
    if not merchant2_user:
        print("❌ 商户2没有用户，跳过验证测试")
        db.delete(test_card)
        db.commit()
        return
    
    card_service = CardRecordService(db)
    
    try:
        # 尝试使用商户1的CK验证商户2的卡记录（应该失败）
        card_service._validate_card_and_ck(str(test_card.id), ck1.id, merchant2_user)
        print("❌ 跨商户CK使用验证应该失败但却通过了")
    except ValueError as e:
        if "严重安全违规" in str(e):
            print("✅ 跨商户CK使用被正确阻止")
        else:
            print(f"❌ 验证失败但错误信息不正确: {e}")
    
    # 清理测试数据
    db.delete(test_card)
    db.commit()
    print("✅ 测试数据已清理")


def test_ck_statistics_accuracy(db):
    """测试CK统计准确性"""
    print("\n=== 测试CK统计准确性 ===")
    
    from app.services.walmart_ck_service_new import WalmartCKService
    
    # 获取一个有CK的商户
    merchant_with_ck = db.query(Merchant).join(WalmartCK).first()
    if not merchant_with_ck:
        print("❌ 没有找到有CK的商户，跳过统计测试")
        return
    
    # 获取超级管理员用户
    admin_user = db.query(User).filter(User.username == "admin").first()
    if not admin_user:
        print("❌ 没有找到管理员用户，跳过统计测试")
        return
    
    ck_service = WalmartCKService(db)
    
    # 获取统计信息
    stats = ck_service.get_ck_statistics(merchant_with_ck.id, admin_user)
    
    print(f"商户 {merchant_with_ck.id} 的CK统计:")
    print(f"  总CK数: {stats['total_count']}")
    print(f"  活跃CK数: {stats['active_count']}")
    print(f"  实际成功绑卡数: {stats['actual_success_count']}")
    print(f"  CK成功详情: {stats['ck_success_details']}")
    
    # 验证统计数据的准确性
    actual_ck_count = db.query(WalmartCK).filter(WalmartCK.merchant_id == merchant_with_ck.id).count()
    actual_success_count = db.query(CardRecord).filter(
        CardRecord.merchant_id == merchant_with_ck.id,
        CardRecord.status == 'success',
        CardRecord.walmart_ck_id.isnot(None)
    ).count()
    
    if stats['total_count'] == actual_ck_count:
        print("✅ CK总数统计正确")
    else:
        print(f"❌ CK总数统计错误: 统计={stats['total_count']}, 实际={actual_ck_count}")
    
    if stats['actual_success_count'] == actual_success_count:
        print("✅ 成功绑卡数统计正确")
    else:
        print(f"❌ 成功绑卡数统计错误: 统计={stats['actual_success_count']}, 实际={actual_success_count}")


def main():
    """主测试函数"""
    print("开始沃尔玛绑卡系统集成测试")
    print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 创建数据库会话
    db = create_session()
    
    try:
        # 获取认证token
        print("\n=== 获取认证token ===")
        token = get_auth_token()
        if not token:
            print("❌ 无法获取认证token，跳过API测试")
        else:
            print("✅ 成功获取认证token")
        
        # 测试API接口
        if token:
            # 获取一个商户ID进行测试
            merchant = db.query(Merchant).first()
            if merchant:
                test_api_merchant_isolation_check(token, merchant.id)
                
                # 获取一个CK进行验证测试
                ck = db.query(WalmartCK).filter(WalmartCK.merchant_id == merchant.id).first()
                if ck:
                    test_api_ck_validation(token, ck.id, merchant.id)
                    # 测试跨商户验证
                    other_merchant = db.query(Merchant).filter(Merchant.id != merchant.id).first()
                    if other_merchant:
                        test_api_ck_validation(token, ck.id, other_merchant.id)
        
        # 测试数据库层面的功能
        test_card_binding_with_ck_tracking(db)
        test_cross_merchant_prevention(db)
        test_ck_statistics_accuracy(db)
        
        print(f"\n=== 集成测试总结 ===")
        print("🎉 集成测试完成！")
        print(f"测试完成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
    except Exception as e:
        print(f"❌ 集成测试过程中发生错误: {e}")
        raise
    finally:
        db.close()


if __name__ == "__main__":
    main()
