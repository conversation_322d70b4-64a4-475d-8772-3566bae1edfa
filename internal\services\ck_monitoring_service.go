package services

import (
	"context"
	"encoding/json"
	"fmt"
	"sync"
	"time"
	"walmart-bind-card-processor/internal/config"

	"github.com/go-redis/redis/v8"
	"go.uber.org/zap"
	"gorm.io/gorm"
)

// CKMetrics CK性能指标
type CKMetrics struct {
	CKID              uint          `json:"ck_id"`
	TotalRequests     int64         `json:"total_requests"`
	SuccessRequests   int64         `json:"success_requests"`
	FailureRequests   int64         `json:"failure_requests"`
	SuccessRate       float64       `json:"success_rate"`
	AvgResponseTime   time.Duration `json:"avg_response_time"`
	MaxResponseTime   time.Duration `json:"max_response_time"`
	MinResponseTime   time.Duration `json:"min_response_time"`
	LastRequestTime   time.Time     `json:"last_request_time"`
	ConsecutiveFailures int         `json:"consecutive_failures"`
	LastFailureTime   time.Time     `json:"last_failure_time"`
	LastFailureReason string        `json:"last_failure_reason"`
}

// CKOperationLog CK操作日志
type CKOperationLog struct {
	ID            string        `json:"id"`
	CKID          uint          `json:"ck_id"`
	Operation     string        `json:"operation"` // bind_card, get_balance, status_check
	TraceID       string        `json:"trace_id"`
	RecordID      string        `json:"record_id"`
	StartTime     time.Time     `json:"start_time"`
	EndTime       time.Time     `json:"end_time"`
	Duration      time.Duration `json:"duration"`
	Success       bool          `json:"success"`
	ErrorMessage  string        `json:"error_message,omitempty"`
	ErrorCode     string        `json:"error_code,omitempty"`
	RequestData   interface{}   `json:"request_data,omitempty"`
	ResponseData  interface{}   `json:"response_data,omitempty"`
	MerchantID    uint          `json:"merchant_id"`
	DepartmentID  uint          `json:"department_id"`
	CardNumber    string        `json:"card_number"` // 脱敏后的卡号
	Amount        int           `json:"amount"`
}

// CKMonitoringService CK监控服务
type CKMonitoringService struct {
	db     *gorm.DB
	redis  *redis.Client
	logger *zap.Logger
	
	// 指标缓存
	metricsCache map[uint]*CKMetrics
	metricsMutex sync.RWMutex
	
	// 配置
	config struct {
		MetricsRetention    time.Duration
		LogRetention        time.Duration
		AlertThresholds     AlertThresholds
		BatchSize           int
		FlushInterval       time.Duration
	}
	
	// 运行状态
	running bool
	stopCh  chan struct{}
	wg      sync.WaitGroup
	
	// 日志缓冲
	logBuffer   []*CKOperationLog
	bufferMutex sync.Mutex
}

// AlertThresholds 告警阈值配置
type AlertThresholds struct {
	SuccessRateThreshold      float64       // 成功率阈值
	ResponseTimeThreshold     time.Duration // 响应时间阈值
	ConsecutiveFailureThreshold int         // 连续失败次数阈值
	ErrorRateThreshold        float64       // 错误率阈值
}

// NewCKMonitoringService 创建CK监控服务
func NewCKMonitoringService(db *gorm.DB, redis *redis.Client, logger *zap.Logger, config *config.CKMonitoringConfig) *CKMonitoringService {
	service := &CKMonitoringService{
		db:           db,
		redis:        redis,
		logger:       logger,
		metricsCache: make(map[uint]*CKMetrics),
		logBuffer:    make([]*CKOperationLog, 0, config.BatchSize),
		stopCh:       make(chan struct{}),
	}

	// 使用配置文件中的值
	service.config.MetricsRetention = config.MetricsRetention
	service.config.LogRetention = config.LogRetention
	service.config.BatchSize = config.BatchSize
	service.config.FlushInterval = config.FlushInterval
	service.config.AlertThresholds = AlertThresholds{
		SuccessRateThreshold:        config.AlertThresholds.SuccessRate,
		ResponseTimeThreshold:       config.AlertThresholds.ResponseTime,
		ConsecutiveFailureThreshold: config.AlertThresholds.ConsecutiveFailures,
		ErrorRateThreshold:          config.AlertThresholds.ErrorRate,
	}

	return service
}

// Start 启动监控服务
func (s *CKMonitoringService) Start(ctx context.Context) error {
	if s.running {
		return fmt.Errorf("CK监控服务已在运行")
	}
	
	s.running = true
	
	// 启动日志刷新协程
	s.wg.Add(1)
	go s.logFlusher(ctx)
	
	// 启动指标计算协程
	s.wg.Add(1)
	go s.metricsCalculator(ctx)
	
	// 启动告警检查协程
	s.wg.Add(1)
	go s.alertChecker(ctx)
	
	s.logger.Info("CK监控服务启动成功")
	return nil
}

// Stop 停止监控服务
func (s *CKMonitoringService) Stop() {
	if !s.running {
		return
	}
	
	s.running = false
	close(s.stopCh)
	s.wg.Wait()
	
	// 刷新剩余日志
	s.flushLogs()
	
	s.logger.Info("CK监控服务已停止")
}

// RecordOperation 记录CK操作
func (s *CKMonitoringService) RecordOperation(log *CKOperationLog) {
	if !s.running {
		return
	}
	
	// 脱敏卡号
	if len(log.CardNumber) > 6 {
		log.CardNumber = log.CardNumber[:6] + "***"
	}
	
	// 计算持续时间
	if !log.EndTime.IsZero() && !log.StartTime.IsZero() {
		log.Duration = log.EndTime.Sub(log.StartTime)
	}
	
	// 添加到缓冲区
	s.bufferMutex.Lock()
	s.logBuffer = append(s.logBuffer, log)
	
	// 如果缓冲区满了，立即刷新
	if len(s.logBuffer) >= s.config.BatchSize {
		s.flushLogsUnsafe()
	}
	s.bufferMutex.Unlock()
	
	// 更新实时指标
	s.updateMetrics(log)
}

// StartOperation 开始记录操作
func (s *CKMonitoringService) StartOperation(ckID uint, operation, traceID, recordID string) *CKOperationLog {
	return &CKOperationLog{
		ID:        fmt.Sprintf("%s_%d_%d", traceID, ckID, time.Now().UnixNano()),
		CKID:      ckID,
		Operation: operation,
		TraceID:   traceID,
		RecordID:  recordID,
		StartTime: time.Now(),
	}
}

// FinishOperation 完成操作记录
func (s *CKMonitoringService) FinishOperation(log *CKOperationLog, success bool, errorMsg, errorCode string) {
	log.EndTime = time.Now()
	log.Success = success
	log.ErrorMessage = errorMsg
	log.ErrorCode = errorCode
	
	s.RecordOperation(log)
}

// GetCKMetrics 获取CK指标
func (s *CKMonitoringService) GetCKMetrics(ckID uint) *CKMetrics {
	s.metricsMutex.RLock()
	defer s.metricsMutex.RUnlock()
	
	if metrics, exists := s.metricsCache[ckID]; exists {
		return metrics
	}
	
	return &CKMetrics{
		CKID: ckID,
	}
}

// GetAllMetrics 获取所有CK指标
func (s *CKMonitoringService) GetAllMetrics() map[uint]*CKMetrics {
	s.metricsMutex.RLock()
	defer s.metricsMutex.RUnlock()
	
	result := make(map[uint]*CKMetrics)
	for ckID, metrics := range s.metricsCache {
		result[ckID] = metrics
	}
	
	return result
}

// updateMetrics 更新指标
func (s *CKMonitoringService) updateMetrics(log *CKOperationLog) {
	s.metricsMutex.Lock()
	defer s.metricsMutex.Unlock()
	
	metrics, exists := s.metricsCache[log.CKID]
	if !exists {
		metrics = &CKMetrics{
			CKID:            log.CKID,
			MinResponseTime: time.Hour, // 初始化为一个大值
		}
		s.metricsCache[log.CKID] = metrics
	}
	
	// 更新计数
	metrics.TotalRequests++
	if log.Success {
		metrics.SuccessRequests++
		metrics.ConsecutiveFailures = 0
	} else {
		metrics.FailureRequests++
		metrics.ConsecutiveFailures++
		metrics.LastFailureTime = log.EndTime
		metrics.LastFailureReason = log.ErrorMessage
	}
	
	// 更新成功率
	metrics.SuccessRate = float64(metrics.SuccessRequests) / float64(metrics.TotalRequests)
	
	// 更新响应时间
	if log.Duration > 0 {
		if metrics.TotalRequests == 1 {
			metrics.AvgResponseTime = log.Duration
			metrics.MaxResponseTime = log.Duration
			metrics.MinResponseTime = log.Duration
		} else {
			// 计算移动平均
			metrics.AvgResponseTime = time.Duration(
				(int64(metrics.AvgResponseTime)*(metrics.TotalRequests-1) + int64(log.Duration)) / metrics.TotalRequests,
			)
			
			if log.Duration > metrics.MaxResponseTime {
				metrics.MaxResponseTime = log.Duration
			}
			if log.Duration < metrics.MinResponseTime {
				metrics.MinResponseTime = log.Duration
			}
		}
	}
	
	metrics.LastRequestTime = log.EndTime
}

// flushLogs 刷新日志到数据库
func (s *CKMonitoringService) flushLogs() {
	s.bufferMutex.Lock()
	defer s.bufferMutex.Unlock()
	s.flushLogsUnsafe()
}

// flushLogsUnsafe 不安全的刷新日志（需要外部加锁）
func (s *CKMonitoringService) flushLogsUnsafe() {
	if len(s.logBuffer) == 0 {
		return
	}
	
	// 批量插入到数据库
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()
	
	for _, log := range s.logBuffer {
		logData, _ := json.Marshal(log)
		
		query := `
			INSERT INTO binding_logs (
				id, card_record_id, log_type, log_level, message, details,
				walmart_ck_id, duration_ms, timestamp, created_at, updated_at
			) VALUES (
				?, ?, 'operation', ?, ?, ?,
				?, ?, ?, NOW(3), NOW(3)
			)
		`
		
		level := "info"
		if !log.Success {
			level = "error"
		}
		
		message := fmt.Sprintf("CK操作: %s", log.Operation)
		if !log.Success {
			message += fmt.Sprintf(" - 失败: %s", log.ErrorMessage)
		}
		
		s.db.WithContext(ctx).Exec(query,
			log.ID, log.RecordID, level, message, string(logData),
			log.CKID, float64(log.Duration.Nanoseconds())/1e6, log.EndTime)
	}
	
	s.logger.Debug("刷新CK操作日志", zap.Int("count", len(s.logBuffer)))
	
	// 清空缓冲区
	s.logBuffer = s.logBuffer[:0]
}

// logFlusher 日志刷新协程
func (s *CKMonitoringService) logFlusher(ctx context.Context) {
	defer s.wg.Done()
	
	ticker := time.NewTicker(s.config.FlushInterval)
	defer ticker.Stop()
	
	for {
		select {
		case <-s.stopCh:
			return
		case <-ticker.C:
			s.flushLogs()
		}
	}
}

// metricsCalculator 指标计算协程
func (s *CKMonitoringService) metricsCalculator(ctx context.Context) {
	defer s.wg.Done()
	
	ticker := time.NewTicker(time.Minute)
	defer ticker.Stop()
	
	for {
		select {
		case <-s.stopCh:
			return
		case <-ticker.C:
			s.calculateAggregatedMetrics(ctx)
		}
	}
}

// calculateAggregatedMetrics 计算聚合指标
func (s *CKMonitoringService) calculateAggregatedMetrics(ctx context.Context) {
	// 这里可以添加更复杂的指标计算逻辑
	// 例如：计算过去1小时、24小时的指标
	s.logger.Debug("计算聚合指标")
}

// alertChecker 告警检查协程
func (s *CKMonitoringService) alertChecker(ctx context.Context) {
	defer s.wg.Done()
	
	ticker := time.NewTicker(time.Minute)
	defer ticker.Stop()
	
	for {
		select {
		case <-s.stopCh:
			return
		case <-ticker.C:
			s.checkAlerts(ctx)
		}
	}
}

// checkAlerts 检查告警
func (s *CKMonitoringService) checkAlerts(ctx context.Context) {
	s.metricsMutex.RLock()
	defer s.metricsMutex.RUnlock()
	
	for ckID, metrics := range s.metricsCache {
		// 检查成功率告警
		if metrics.SuccessRate < s.config.AlertThresholds.SuccessRateThreshold {
			s.triggerAlert(ckID, "success_rate_low", 
				fmt.Sprintf("CK %d 成功率过低: %.2f%%", ckID, metrics.SuccessRate*100))
		}
		
		// 检查响应时间告警
		if metrics.AvgResponseTime > s.config.AlertThresholds.ResponseTimeThreshold {
			s.triggerAlert(ckID, "response_time_high",
				fmt.Sprintf("CK %d 响应时间过长: %v", ckID, metrics.AvgResponseTime))
		}
		
		// 检查连续失败告警
		if metrics.ConsecutiveFailures >= s.config.AlertThresholds.ConsecutiveFailureThreshold {
			s.triggerAlert(ckID, "consecutive_failures",
				fmt.Sprintf("CK %d 连续失败 %d 次", ckID, metrics.ConsecutiveFailures))
		}
	}
}

// triggerAlert 触发告警
func (s *CKMonitoringService) triggerAlert(ckID uint, alertType, message string) {
	s.logger.Warn("CK告警触发",
		zap.Uint("ck_id", ckID),
		zap.String("alert_type", alertType),
		zap.String("message", message))
	
	// 这里可以集成告警系统，如发送邮件、短信、钉钉等
}
