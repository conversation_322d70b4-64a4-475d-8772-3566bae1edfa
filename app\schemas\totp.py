"""
双因子认证相关的Pydantic模式定义
"""

from typing import Optional, List
from pydantic import BaseModel, Field
from datetime import datetime


class TOTPSetupRequest(BaseModel):
    """TOTP设置请求"""
    pass  # 无需参数，系统自动生成密钥


class TOTPSetupResponse(BaseModel):
    """TOTP设置响应"""
    secret: str = Field(..., description="Base32编码的密钥")
    qr_code_url: str = Field(..., description="二维码URL")
    backup_codes: List[str] = Field(..., description="备用恢复码列表")
    manual_entry_key: str = Field(..., description="手动输入密钥")


class TOTPVerifyRequest(BaseModel):
    """TOTP验证请求"""
    code: str = Field(..., description="6位验证码", min_length=6, max_length=6)


class TOTPVerifyResponse(BaseModel):
    """TOTP验证响应"""
    success: bool = Field(..., description="验证是否成功")
    message: str = Field(..., description="响应消息")


class TOTPEnableRequest(BaseModel):
    """启用TOTP请求"""
    code: str = Field(..., description="6位验证码", min_length=6, max_length=6)


class TOTPDisableRequest(BaseModel):
    """禁用TOTP请求"""
    password: str = Field(..., description="用户密码")
    code: Optional[str] = Field(None, description="6位验证码或备用码")


class TOTPStatusResponse(BaseModel):
    """TOTP状态响应"""
    enabled: bool = Field(..., description="是否已启用")
    setup_at: Optional[datetime] = Field(None, description="设置时间")
    last_used_at: Optional[datetime] = Field(None, description="最后使用时间")
    backup_codes_remaining: int = Field(0, description="剩余备用码数量")
    is_required: bool = Field(False, description="是否强制要求")
    grace_period_expires: Optional[datetime] = Field(None, description="宽限期到期时间")


class BackupCodeUseRequest(BaseModel):
    """使用备用码请求"""
    backup_code: str = Field(..., description="备用恢复码", min_length=8, max_length=12)


class TOTPLoginRequest(BaseModel):
    """双因子认证登录请求"""
    username: str = Field(..., description="用户名")
    password: str = Field(..., description="密码")
    totp_code: Optional[str] = Field(None, description="TOTP验证码")
    backup_code: Optional[str] = Field(None, description="备用恢复码")


class TOTPPolicyResponse(BaseModel):
    """TOTP策略响应"""
    role_name: str = Field(..., description="角色名称")
    is_required: bool = Field(..., description="是否强制启用")
    grace_period_days: int = Field(..., description="宽限期天数")
    backup_codes_count: int = Field(..., description="备用码数量")


class TOTPLogResponse(BaseModel):
    """TOTP日志响应"""
    id: int = Field(..., description="日志ID")
    action: str = Field(..., description="操作类型")
    success: bool = Field(..., description="操作是否成功")
    ip_address: Optional[str] = Field(None, description="操作IP地址")
    error_message: Optional[str] = Field(None, description="错误信息")
    created_at: datetime = Field(..., description="创建时间")


class TOTPLogListResponse(BaseModel):
    """TOTP日志列表响应"""
    items: List[TOTPLogResponse]
    total: int
    page: int
    page_size: int
