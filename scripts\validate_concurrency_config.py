#!/usr/bin/env python3
"""
并发配置验证脚本
验证系统并发配置的合理性和一致性
"""

import yaml
import sys
import os
from pathlib import Path
from typing import Dict, List, Any

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from app.core.logging import get_logger

logger = get_logger(__name__)


class ConcurrencyConfigValidator:
    """并发配置验证器"""
    
    def __init__(self, config_path: str = "config.yaml"):
        self.config_path = config_path
        self.config = self._load_config()
        self.issues = []
        self.warnings = []
        self.recommendations = []
    
    def _load_config(self) -> Dict[str, Any]:
        """加载配置文件"""
        try:
            with open(self.config_path, 'r', encoding='utf-8') as f:
                return yaml.safe_load(f)
        except Exception as e:
            logger.error(f"加载配置文件失败: {e}")
            return {}
    
    def validate_all(self) -> bool:
        """执行所有验证"""
        print("🔍 开始验证并发配置...")
        print("=" * 60)
        
        # 验证各个组件的配置
        self._validate_business_config()
        self._validate_rabbitmq_config()
        self._validate_database_config()
        self._validate_redis_config()
        self._validate_performance_config()
        self._validate_config_consistency()
        
        # 输出结果
        self._print_results()
        
        return len(self.issues) == 0
    
    def _validate_business_config(self):
        """验证业务配置"""
        print("\n📋 1. 业务配置验证")
        print("-" * 40)
        
        business = self.config.get("business", {})
        binding = business.get("binding", {})
        
        if not business:
            self.issues.append("缺少 business 配置部分")
            print("  ❌ 缺少 business 配置部分")
            return
        
        if not binding:
            self.issues.append("缺少 business.binding 配置部分")
            print("  ❌ 缺少 business.binding 配置部分")
            return
        
        # 检查关键配置项
        queue_concurrency = binding.get("queue_max_concurrency", 0)
        max_concurrency = binding.get("max_concurrency", 0)
        
        if queue_concurrency <= 0:
            self.issues.append("queue_max_concurrency 必须大于0")
            print(f"  ❌ queue_max_concurrency={queue_concurrency} 无效")
        elif queue_concurrency > 20:
            self.warnings.append("queue_max_concurrency 过高，建议不超过20")
            print(f"  ⚠️  queue_max_concurrency={queue_concurrency} 可能过高")
        else:
            print(f"  ✅ queue_max_concurrency={queue_concurrency}")
        
        if max_concurrency <= 0:
            self.issues.append("max_concurrency 必须大于0")
            print(f"  ❌ max_concurrency={max_concurrency} 无效")
        else:
            print(f"  ✅ max_concurrency={max_concurrency}")
        
        # 检查超时配置
        api_timeout = binding.get("walmart_api_timeout", 30)
        if api_timeout < 10:
            self.warnings.append("walmart_api_timeout 过短，可能导致超时")
            print(f"  ⚠️  walmart_api_timeout={api_timeout}s 可能过短")
        else:
            print(f"  ✅ walmart_api_timeout={api_timeout}s")
    
    def _validate_rabbitmq_config(self):
        """验证RabbitMQ配置"""
        print("\n🐰 2. RabbitMQ配置验证")
        print("-" * 40)
        
        rabbitmq = self.config.get("rabbitmq", {})
        
        if not rabbitmq:
            self.issues.append("缺少 rabbitmq 配置部分")
            print("  ❌ 缺少 rabbitmq 配置部分")
            return
        
        # 检查预取配置
        consumer_prefetch = rabbitmq.get("consumer_prefetch_count", 10)
        callback_prefetch = rabbitmq.get("callback_consumer_prefetch_count", 10)
        
        business_concurrency = self.config.get("business", {}).get("binding", {}).get("queue_max_concurrency", 5)
        
        if consumer_prefetch > business_concurrency * 2:
            self.warnings.append("consumer_prefetch_count 过高，可能导致消息堆积")
            print(f"  ⚠️  consumer_prefetch_count={consumer_prefetch} 相对于并发数过高")
        else:
            print(f"  ✅ consumer_prefetch_count={consumer_prefetch}")
        
        print(f"  ✅ callback_consumer_prefetch_count={callback_prefetch}")
        
        # 检查连接池配置
        max_connections = rabbitmq.get("max_connections", 10)
        if max_connections < 5:
            self.warnings.append("max_connections 过低，可能影响性能")
            print(f"  ⚠️  max_connections={max_connections} 可能过低")
        else:
            print(f"  ✅ max_connections={max_connections}")
    
    def _validate_database_config(self):
        """验证数据库配置"""
        print("\n🗄️  3. 数据库配置验证")
        print("-" * 40)
        
        database = self.config.get("database", {})
        performance = self.config.get("performance", {})
        
        # 检查连接池配置
        pool_size = performance.get("db_pool_size", 100)
        max_overflow = performance.get("db_pool_max_overflow", 200)
        pool_timeout = performance.get("db_pool_timeout", 30)
        
        total_connections = pool_size + max_overflow
        business_concurrency = self.config.get("business", {}).get("binding", {}).get("queue_max_concurrency", 5)
        
        print(f"  ✅ db_pool_size={pool_size}")
        print(f"  ✅ db_pool_max_overflow={max_overflow}")
        print(f"  ✅ 总连接数={total_connections}")
        
        # 检查连接数是否足够
        recommended_connections = business_concurrency * 10  # 每个并发请求预留10个连接
        if total_connections < recommended_connections:
            self.warnings.append(f"数据库连接池可能不足，建议至少{recommended_connections}个连接")
            print(f"  ⚠️  连接池可能不足，建议至少{recommended_connections}个连接")
        
        if pool_timeout < 10:
            self.warnings.append("pool_timeout 过短，可能导致连接获取失败")
            print(f"  ⚠️  pool_timeout={pool_timeout}s 可能过短")
        else:
            print(f"  ✅ pool_timeout={pool_timeout}s")
    
    def _validate_redis_config(self):
        """验证Redis配置"""
        print("\n🔴 4. Redis配置验证")
        print("-" * 40)
        
        redis = self.config.get("redis", {})
        
        if not redis:
            self.issues.append("缺少 redis 配置部分")
            print("  ❌ 缺少 redis 配置部分")
            return
        
        pool_size = redis.get("pool_size", 50)
        min_idle = redis.get("min_idle_conns", 5)
        
        business_concurrency = self.config.get("business", {}).get("binding", {}).get("queue_max_concurrency", 5)
        
        if pool_size < business_concurrency * 2:
            self.warnings.append("Redis连接池可能不足")
            print(f"  ⚠️  pool_size={pool_size} 相对于并发数可能不足")
        else:
            print(f"  ✅ pool_size={pool_size}")
        
        print(f"  ✅ min_idle_conns={min_idle}")
    
    def _validate_performance_config(self):
        """验证性能配置"""
        print("\n⚡ 5. 性能配置验证")
        print("-" * 40)
        
        performance = self.config.get("performance", {})
        
        if not performance:
            self.warnings.append("缺少 performance 配置部分，将使用默认值")
            print("  ⚠️  缺少 performance 配置部分")
            return
        
        # 检查关键性能参数
        db_pool_recycle = performance.get("db_pool_recycle", 3600)
        cache_timeout = performance.get("cache_default_timeout", 300)
        
        print(f"  ✅ db_pool_recycle={db_pool_recycle}s")
        print(f"  ✅ cache_default_timeout={cache_timeout}s")
        
        if db_pool_recycle < 1800:  # 30分钟
            self.warnings.append("db_pool_recycle 过短，可能影响性能")
            print(f"  ⚠️  db_pool_recycle={db_pool_recycle}s 可能过短")
    
    def _validate_config_consistency(self):
        """验证配置一致性"""
        print("\n🔄 6. 配置一致性验证")
        print("-" * 40)
        
        # 获取各组件的并发配置
        queue_concurrency = self.config.get("business", {}).get("binding", {}).get("queue_max_concurrency", 5)
        api_concurrency = self.config.get("business", {}).get("binding", {}).get("max_concurrency", 10)
        rabbitmq_prefetch = self.config.get("rabbitmq", {}).get("consumer_prefetch_count", 10)
        
        # 检查配置是否协调
        if api_concurrency < queue_concurrency:
            self.warnings.append("API并发数小于队列并发数，可能导致瓶颈")
            print(f"  ⚠️  API并发数({api_concurrency}) < 队列并发数({queue_concurrency})")
        else:
            print(f"  ✅ 并发数配置协调: API={api_concurrency}, Queue={queue_concurrency}")
        
        if rabbitmq_prefetch < queue_concurrency:
            self.warnings.append("RabbitMQ预取数小于队列并发数")
            print(f"  ⚠️  RabbitMQ预取数({rabbitmq_prefetch}) < 队列并发数({queue_concurrency})")
        else:
            print(f"  ✅ RabbitMQ预取配置合理: {rabbitmq_prefetch}")
    
    def _print_results(self):
        """打印验证结果"""
        print("\n" + "=" * 60)
        print("📊 验证结果汇总")
        print("=" * 60)
        
        if not self.issues and not self.warnings:
            print("🎉 所有配置验证通过！")
            return
        
        if self.issues:
            print(f"\n❌ 发现 {len(self.issues)} 个严重问题:")
            for i, issue in enumerate(self.issues, 1):
                print(f"  {i}. {issue}")
        
        if self.warnings:
            print(f"\n⚠️  发现 {len(self.warnings)} 个警告:")
            for i, warning in enumerate(self.warnings, 1):
                print(f"  {i}. {warning}")
        
        # 提供建议
        print(f"\n💡 建议:")
        print("  1. 根据实际负载调整并发配置")
        print("  2. 监控系统资源使用情况")
        print("  3. 定期检查配置一致性")
        print("  4. 在生产环境中进行压力测试")
    
    def generate_recommended_config(self) -> Dict[str, Any]:
        """生成推荐配置"""
        return {
            "business": {
                "binding": {
                    "enabled": True,
                    "queue_max_concurrency": 1,  # 保守设置避免死机
                    "max_concurrency": 1,  # 保守设置避免死机
                    "walmart_api_timeout": 30,
                    "max_retry_attempts": 3,
                    "retry_delay_seconds": 2,
                    "ck_selection_timeout": 10,
                    "enable_ck_load_balance": True,
                    "enable_performance_monitoring": True,
                    "slow_request_threshold": 5
                }
            },
            "rabbitmq": {
                "consumer_prefetch_count": 5,
                "callback_consumer_prefetch_count": 10,
                "max_connections": 10,
                "max_channels_per_conn": 100
            },
            "performance": {
                "db_pool_size": 50,
                "db_pool_max_overflow": 100,
                "db_pool_timeout": 30,
                "db_pool_recycle": 3600,
                "redis_pool_size": 20,
                "cache_default_timeout": 300
            }
        }


def main():
    """主函数"""
    validator = ConcurrencyConfigValidator()
    
    success = validator.validate_all()
    
    if not success:
        print(f"\n🔧 推荐配置已生成，请参考调整")
        recommended = validator.generate_recommended_config()
        print("\n推荐配置片段:")
        print(yaml.dump(recommended, default_flow_style=False, allow_unicode=True))
    
    return 0 if success else 1


if __name__ == "__main__":
    sys.exit(main())
