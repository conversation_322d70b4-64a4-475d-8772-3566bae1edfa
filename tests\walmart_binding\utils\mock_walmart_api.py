"""
Mock沃尔玛API服务
用于测试时替代真实的沃尔玛API调用，避免IP封禁和真实数据影响
"""

import asyncio
import random
import time
import uuid
from typing import Dict, Any, Optional, List
from dataclasses import dataclass
from collections import defaultdict
import logging

logger = logging.getLogger(__name__)


@dataclass
class APICallStats:
    """API调用统计"""
    total_calls: int = 0
    success_calls: int = 0
    failed_calls: int = 0
    total_response_time: float = 0.0
    error_types: Dict[str, int] = None
    
    def __post_init__(self):
        if self.error_types is None:
            self.error_types = defaultdict(int)
    
    @property
    def success_rate(self) -> float:
        """成功率"""
        return self.success_calls / self.total_calls if self.total_calls > 0 else 0.0
    
    @property
    def average_response_time(self) -> float:
        """平均响应时间"""
        return self.total_response_time / self.total_calls if self.total_calls > 0 else 0.0


class MockWalmartAPI:
    """Mock沃尔玛API服务"""
    
    def __init__(
        self,
        success_rate: float = 0.7,
        delay_range: tuple = (0.05, 0.2),
        enable_random_errors: bool = True
    ):
        """
        初始化Mock API
        
        Args:
            success_rate: API成功率 (0.0-1.0)
            delay_range: 响应延迟范围 (min_seconds, max_seconds)
            enable_random_errors: 是否启用随机错误
        """
        self.success_rate = success_rate
        self.delay_range = delay_range
        self.enable_random_errors = enable_random_errors
        self.stats = APICallStats()
        self.call_history: List[Dict[str, Any]] = []
        
        # 预定义的错误类型和消息
        self.error_types = [
            {"code": "1001", "message": "卡号无效"},
            {"code": "1002", "message": "卡密码错误"},
            {"code": "1003", "message": "卡已被绑定"},
            {"code": "1004", "message": "系统繁忙，请稍后重试"},
            {"code": "1005", "message": "网络超时"},
            {"code": "1006", "message": "请先登录"},
            {"code": "1007", "message": "CK已失效"},
        ]
    
    async def bind_card(
        self,
        card_no: str,
        card_pwd: str,
        db: Optional[Any] = None,
        **kwargs
    ) -> 'MockResponse':
        """
        模拟绑卡API调用
        
        Args:
            card_no: 卡号
            card_pwd: 卡密码
            db: 数据库会话（Mock中不使用）
            **kwargs: 其他参数
            
        Returns:
            MockResponse: 模拟响应对象
        """
        call_start_time = time.time()
        
        # 模拟网络延迟
        delay = random.uniform(*self.delay_range)
        await asyncio.sleep(delay)
        
        # 更新统计
        self.stats.total_calls += 1
        
        # 决定是否成功
        is_success = random.random() < self.success_rate
        
        if is_success:
            response_data = self._generate_success_response(card_no)
            self.stats.success_calls += 1
        else:
            response_data = self._generate_error_response()
            self.stats.failed_calls += 1
        
        # 记录响应时间
        response_time = time.time() - call_start_time
        self.stats.total_response_time += response_time
        
        # 记录调用历史
        call_record = {
            "timestamp": call_start_time,
            "card_no": card_no[:6] + "***",  # 脱敏处理
            "success": is_success,
            "response_time": response_time,
            "response_data": response_data
        }
        self.call_history.append(call_record)
        
        logger.info(
            f"Mock API调用: card_no={card_no[:6]}***, "
            f"success={is_success}, response_time={response_time:.3f}s"
        )
        
        return MockResponse(response_data, is_success)
    
    def _generate_success_response(self, card_no: str) -> Dict[str, Any]:
        """生成成功响应"""
        return {
            "success": True,
            "error": "",
            "errorcode": "0",
            "logId": f"mock_log_{uuid.uuid4().hex[:12]}",
            "data": {
                "bind_result": "success",
                "card_no": card_no,
                "bind_time": int(time.time()),
                "amount": random.randint(1000, 50000),  # 模拟卡余额(分)
                "status": "active"
            }
        }
    
    def _generate_error_response(self) -> Dict[str, Any]:
        """生成错误响应"""
        error_info = random.choice(self.error_types)
        self.stats.error_types[error_info["code"]] += 1
        
        return {
            "success": False,
            "error": error_info["message"],
            "errorcode": error_info["code"],
            "logId": f"mock_log_{uuid.uuid4().hex[:12]}",
            "data": None
        }
    
    async def query_user(self, db: Optional[Any] = None) -> 'MockResponse':
        """
        模拟用户查询API（用于CK验证）
        
        Args:
            db: 数据库会话（Mock中不使用）
            
        Returns:
            MockResponse: 模拟响应对象
        """
        # 模拟延迟
        await asyncio.sleep(random.uniform(0.01, 0.05))
        
        # CK验证成功率通常较高
        is_valid = random.random() < 0.95
        
        if is_valid:
            response_data = {
                "success": True,
                "error": "",
                "errorcode": "0",
                "data": {
                    "user_info": {
                        "user_id": f"mock_user_{uuid.uuid4().hex[:8]}",
                        "status": "active",
                        "login_time": int(time.time())
                    }
                }
            }
        else:
            response_data = {
                "success": False,
                "error": "CK已失效，请重新登录",
                "errorcode": "1007",
                "data": None
            }
        
        return MockResponse(response_data, is_valid)
    
    def get_stats(self) -> Dict[str, Any]:
        """获取API调用统计信息"""
        return {
            "total_calls": self.stats.total_calls,
            "success_calls": self.stats.success_calls,
            "failed_calls": self.stats.failed_calls,
            "success_rate": self.stats.success_rate,
            "average_response_time": self.stats.average_response_time,
            "error_distribution": dict(self.stats.error_types),
            "call_history_count": len(self.call_history)
        }
    
    def reset_stats(self):
        """重置统计信息"""
        self.stats = APICallStats()
        self.call_history.clear()
    
    def get_recent_calls(self, limit: int = 10) -> List[Dict[str, Any]]:
        """获取最近的API调用记录"""
        return self.call_history[-limit:] if self.call_history else []


class MockResponse:
    """模拟HTTP响应对象"""
    
    def __init__(self, data: Dict[str, Any], success: bool = True):
        self.data = data
        self.success = success
        self.need_retry_with_new_user = not success and data.get("errorcode") == "1007"
    
    def json(self) -> Dict[str, Any]:
        """返回JSON数据"""
        return self.data
    
    @property
    def status_code(self) -> int:
        """HTTP状态码"""
        return 200 if self.success else 400


# 全局Mock API实例（单例模式）
_mock_api_instance: Optional[MockWalmartAPI] = None


def get_mock_api(
    success_rate: float = 0.7,
    delay_range: tuple = (0.05, 0.2),
    enable_random_errors: bool = True
) -> MockWalmartAPI:
    """
    获取Mock API实例（单例模式）
    
    Args:
        success_rate: API成功率
        delay_range: 响应延迟范围
        enable_random_errors: 是否启用随机错误
        
    Returns:
        MockWalmartAPI: Mock API实例
    """
    global _mock_api_instance
    
    if _mock_api_instance is None:
        _mock_api_instance = MockWalmartAPI(
            success_rate=success_rate,
            delay_range=delay_range,
            enable_random_errors=enable_random_errors
        )
    
    return _mock_api_instance


def reset_mock_api():
    """重置Mock API实例"""
    global _mock_api_instance
    if _mock_api_instance:
        _mock_api_instance.reset_stats()
    _mock_api_instance = None