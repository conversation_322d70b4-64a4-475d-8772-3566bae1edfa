# 数据库和ORM
sqlalchemy
pymysql
aiomysql  # 异步MySQL支持

# Web框架
fastapi
uvicorn
pydantic
pydantic-settings
email-validator
curl_cffi

# 定时任务
apscheduler

# 安全和认证
python-jose
passlib[bcrypt]>=1.7.4
bcrypt>=4.0.0,<4.2.0
pyjwt
python-multipart
cryptography

# 双因子认证
pyotp
qrcode[pil]
# 配置和工具
pyyaml
python-dotenv

# Redis支持（如使用Redis）
redis

# 消息队列支持
aio-pika  # RabbitMQ异步客户端

# 数据分析
pandas
openpyxl

# 中文处理
pypinyin

# Telegram Bot
python-telegram-bot[webhooks]>=20.0

# 测试
pytest
httpx
