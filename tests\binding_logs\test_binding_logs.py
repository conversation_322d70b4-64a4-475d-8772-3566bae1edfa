#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
绑定日志测试
测试绑定日志的查询和统计功能
"""

import sys
import os
import time
import json

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.dirname(__file__))))

from test.conftest import TestBase, get_test_accounts, format_test_result, print_test_summary

class BindingLogsTestSuite(TestBase):
    """绑定日志测试类"""
    
    def __init__(self):
        super().__init__()
        self.results = []
        self.test_accounts = get_test_accounts()
        self.admin_token = None
        self.merchant_token = None
        
    def setup_test_environment(self):
        """设置测试环境"""
        print("=== 设置绑定日志测试环境 ===")
        
        # 登录管理员账号
        self.admin_token = self.login(
            self.test_accounts["super_admin"]["username"],
            self.test_accounts["super_admin"]["password"]
        )
        
        # 登录商户账号
        self.merchant_token = self.login(
            self.test_accounts["merchant_admin"]["username"],
            self.test_accounts["merchant_admin"]["password"]
        )
        
        if not self.admin_token:
            raise Exception("无法获取管理员token")
        if not self.merchant_token:
            raise Exception("无法获取商户token")
            
        print("✅ 测试环境设置完成")
    
    def test_get_binding_logs_by_card_id(self):
        """测试根据卡ID获取绑定日志"""
        print("\n=== 测试根据卡ID获取绑定日志 ===")
        
        # 首先获取一个卡记录ID
        status_code, response = self.make_request("GET", "/cards", self.admin_token, params={"page": 1, "size": 1})
        
        if status_code == 200 and "data" in response:
            data = response["data"]
            items = data.get("items", []) if isinstance(data, dict) else data if isinstance(data, list) else []
            
            if items and len(items) > 0:
                card_id = items[0].get("id")
                if card_id:
                    # 测试管理员获取绑定日志
                    status_code, response = self.make_request("GET", f"/binding-logs/{card_id}", self.admin_token)
                    
                    if status_code == 200:
                        self.results.append(format_test_result(
                            "管理员获取绑定日志",
                            True,
                            f"管理员成功获取卡ID {card_id} 的绑定日志"
                        ))
                        print(f"✅ 管理员成功获取卡ID {card_id} 的绑定日志")
                        
                        # 检查响应格式
                        if "data" in response:
                            data = response["data"]
                            if isinstance(data, list):
                                print(f"   📊 找到 {len(data)} 条绑定日志")
                            elif isinstance(data, dict) and "items" in data:
                                print(f"   📊 找到 {len(data['items'])} 条绑定日志")
                    else:
                        self.results.append(format_test_result(
                            "管理员获取绑定日志",
                            False,
                            f"获取绑定日志失败，状态码: {status_code}"
                        ))
                        print(f"❌ 获取绑定日志失败，状态码: {status_code}")
                    
                    # 测试商户获取绑定日志（权限控制）
                    status_code, response = self.make_request("GET", f"/binding-logs/{card_id}", self.merchant_token)
                    
                    if status_code in [200, 403, 404]:  # 200表示有权限，403表示无权限，404表示不存在
                        self.results.append(format_test_result(
                            "商户获取绑定日志权限控制",
                            True,
                            f"商户获取绑定日志权限控制正常，状态码: {status_code}"
                        ))
                        print(f"✅ 商户获取绑定日志权限控制正常，状态码: {status_code}")
                    else:
                        self.results.append(format_test_result(
                            "商户获取绑定日志权限控制",
                            False,
                            f"商户获取绑定日志权限控制异常，状态码: {status_code}"
                        ))
                        print(f"❌ 商户获取绑定日志权限控制异常，状态码: {status_code}")
                else:
                    print("⚠️ 无法获取卡记录ID，跳过绑定日志测试")
            else:
                print("⚠️ 没有找到卡记录，跳过绑定日志测试")
        else:
            print("⚠️ 获取卡记录列表失败，跳过绑定日志测试")
    
    def test_binding_logs_data_format(self):
        """测试绑定日志数据格式"""
        print("\n=== 测试绑定日志数据格式 ===")
        
        # 获取一个卡记录ID进行测试
        status_code, response = self.make_request("GET", "/cards", self.admin_token, params={"page": 1, "size": 1})
        
        if status_code == 200 and "data" in response:
            data = response["data"]
            items = data.get("items", []) if isinstance(data, dict) else data if isinstance(data, list) else []
            
            if items and len(items) > 0:
                card_id = items[0].get("id")
                if card_id:
                    status_code, response = self.make_request("GET", f"/binding-logs/{card_id}", self.admin_token)
                    
                    if status_code == 200:
                        # 检查响应数据格式
                        logs_data = response.get("data", [])
                        if isinstance(logs_data, dict) and "items" in logs_data:
                            logs_data = logs_data["items"]
                        
                        if isinstance(logs_data, list):
                            if len(logs_data) > 0:
                                # 检查第一条日志的字段
                                first_log = logs_data[0]
                                expected_fields = ["id", "card_id", "status", "created_at"]
                                missing_fields = [field for field in expected_fields if field not in first_log]
                                
                                if not missing_fields:
                                    self.results.append(format_test_result(
                                        "绑定日志数据格式",
                                        True,
                                        "绑定日志数据格式正确"
                                    ))
                                    print("✅ 绑定日志数据格式正确")
                                else:
                                    self.results.append(format_test_result(
                                        "绑定日志数据格式",
                                        False,
                                        f"绑定日志缺少字段: {missing_fields}"
                                    ))
                                    print(f"❌ 绑定日志缺少字段: {missing_fields}")
                            else:
                                self.results.append(format_test_result(
                                    "绑定日志数据格式",
                                    True,
                                    "绑定日志为空，格式检查跳过"
                                ))
                                print("⚠️ 绑定日志为空，格式检查跳过")
                        else:
                            self.results.append(format_test_result(
                                "绑定日志数据格式",
                                False,
                                "绑定日志数据格式不正确，应该是数组"
                            ))
                            print("❌ 绑定日志数据格式不正确，应该是数组")
                    else:
                        print("⚠️ 无法获取绑定日志，跳过格式检查")
                else:
                    print("⚠️ 无法获取卡记录ID，跳过格式检查")
            else:
                print("⚠️ 没有找到卡记录，跳过格式检查")
        else:
            print("⚠️ 获取卡记录列表失败，跳过格式检查")
    
    def test_binding_logs_access_control(self):
        """测试绑定日志访问控制"""
        print("\n=== 测试绑定日志访问控制 ===")
        
        # 测试无效的卡ID
        invalid_card_id = 999999
        status_code, response = self.make_request("GET", f"/binding-logs/{invalid_card_id}", self.admin_token)
        
        if status_code in [404, 200]:  # 404表示不存在，200表示返回空列表
            self.results.append(format_test_result(
                "无效卡ID访问控制",
                True,
                f"无效卡ID访问控制正常，状态码: {status_code}"
            ))
            print(f"✅ 无效卡ID访问控制正常，状态码: {status_code}")
        else:
            self.results.append(format_test_result(
                "无效卡ID访问控制",
                False,
                f"无效卡ID访问控制异常，状态码: {status_code}"
            ))
            print(f"❌ 无效卡ID访问控制异常，状态码: {status_code}")
        
        # 测试未授权访问
        status_code, response = self.make_request("GET", f"/binding-logs/{invalid_card_id}", None)
        
        if status_code in [401, 403]:
            self.results.append(format_test_result(
                "未授权访问绑定日志",
                True,
                f"未授权访问正确被拒绝，状态码: {status_code}"
            ))
            print(f"✅ 未授权访问正确被拒绝，状态码: {status_code}")
        else:
            self.results.append(format_test_result(
                "未授权访问绑定日志",
                False,
                f"未授权访问应该被拒绝，状态码: {status_code}"
            ))
            print(f"❌ 未授权访问应该被拒绝，状态码: {status_code}")
    
    def test_binding_logs_performance(self):
        """测试绑定日志查询性能"""
        print("\n=== 测试绑定日志查询性能 ===")
        
        # 获取一个卡记录ID进行性能测试
        status_code, response = self.make_request("GET", "/cards", self.admin_token, params={"page": 1, "size": 1})
        
        if status_code == 200 and "data" in response:
            data = response["data"]
            items = data.get("items", []) if isinstance(data, dict) else data if isinstance(data, list) else []
            
            if items and len(items) > 0:
                card_id = items[0].get("id")
                if card_id:
                    # 测试查询性能
                    start_time = time.time()
                    status_code, response = self.make_request("GET", f"/binding-logs/{card_id}", self.admin_token)
                    end_time = time.time()
                    
                    query_time = end_time - start_time
                    
                    if status_code == 200 and query_time < 5.0:  # 5秒内完成查询
                        self.results.append(format_test_result(
                            "绑定日志查询性能",
                            True,
                            f"查询性能良好，耗时: {query_time:.2f}秒"
                        ))
                        print(f"✅ 查询性能良好，耗时: {query_time:.2f}秒")
                    elif status_code == 200:
                        self.results.append(format_test_result(
                            "绑定日志查询性能",
                            False,
                            f"查询性能较慢，耗时: {query_time:.2f}秒"
                        ))
                        print(f"⚠️ 查询性能较慢，耗时: {query_time:.2f}秒")
                    else:
                        self.results.append(format_test_result(
                            "绑定日志查询性能",
                            False,
                            f"查询失败，状态码: {status_code}"
                        ))
                        print(f"❌ 查询失败，状态码: {status_code}")
                else:
                    print("⚠️ 无法获取卡记录ID，跳过性能测试")
            else:
                print("⚠️ 没有找到卡记录，跳过性能测试")
        else:
            print("⚠️ 获取卡记录列表失败，跳过性能测试")
    
    def run_all_tests(self):
        """运行所有绑定日志测试"""
        print("🧪 开始绑定日志测试")
        print("="*60)
        
        start_time = time.time()
        
        try:
            # 设置测试环境
            self.setup_test_environment()
            
            # 运行测试
            self.test_get_binding_logs_by_card_id()
            self.test_binding_logs_data_format()
            self.test_binding_logs_access_control()
            self.test_binding_logs_performance()
            
        except Exception as e:
            self.results.append(format_test_result(
                "测试环境设置",
                False,
                f"测试环境设置失败: {str(e)}"
            ))
            print(f"❌ 测试环境设置失败: {str(e)}")
        
        end_time = time.time()
        duration = end_time - start_time
        
        # 打印测试摘要
        print_test_summary(self.results)
        print(f"测试耗时: {duration:.2f}秒")
        
        return self.results

def main():
    """主函数"""
    test = BindingLogsTestSuite()
    results = test.run_all_tests()
    
    # 判断测试是否通过
    failed_tests = [r for r in results if not r.get("success", False)]
    
    if not failed_tests:
        print("\n🎉 绑定日志测试全部通过！")
        return 0
    else:
        print(f"\n⚠️ 发现 {len(failed_tests)} 个问题需要修复")
        for test in failed_tests:
            print(f"  - {test['test_name']}: {test['message']}")
        return 1

if __name__ == "__main__":
    sys.exit(main())
