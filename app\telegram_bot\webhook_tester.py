"""
Telegram Webhook 配置测试工具
用于验证webhook配置是否正确
"""

import asyncio
import json
import hmac
import hashlib
from typing import Dict, Any, Optional
from urllib.parse import urlparse

try:
    import aiohttp
    HAS_AIOHTTP = True
except ImportError:
    HAS_AIOHTTP = False

from app.core.logging import get_logger
from .config import BotConfig

logger = get_logger(__name__)


class WebhookTester:
    """Webhook配置测试器"""
    
    def __init__(self, config: BotConfig):
        self.config = config
    
    def validate_webhook_url(self) -> Dict[str, Any]:
        """验证webhook URL配置"""
        result = {
            "valid": False,
            "errors": [],
            "warnings": [],
            "info": []
        }
        
        if not self.config.webhook_url:
            result["errors"].append("Webhook URL 未配置")
            return result
        
        # 检查URL格式
        try:
            parsed = urlparse(self.config.webhook_url)
            
            # 必须使用HTTPS
            if parsed.scheme != "https":
                result["errors"].append("Webhook URL 必须使用 HTTPS 协议")
            
            # 检查域名
            if not parsed.netloc:
                result["errors"].append("Webhook URL 缺少有效的域名")
            
            # 检查路径
            if not parsed.path or parsed.path == "/":
                result["warnings"].append("建议使用具体的路径，如 /api/v1/telegram/webhook")
            
            # 检查端口
            if parsed.port and parsed.port not in [443, 80, 88, 8443]:
                result["warnings"].append(f"端口 {parsed.port} 可能不被Telegram支持，建议使用 443, 80, 88, 或 8443")
            
            result["info"].append(f"域名: {parsed.netloc}")
            result["info"].append(f"路径: {parsed.path}")
            
        except Exception as e:
            result["errors"].append(f"URL格式错误: {e}")
            return result
        
        if not result["errors"]:
            result["valid"] = True
        
        return result
    
    def validate_webhook_secret(self) -> Dict[str, Any]:
        """验证webhook secret配置"""
        result = {
            "configured": bool(self.config.webhook_secret),
            "secure": False,
            "recommendations": []
        }
        
        if not self.config.webhook_secret:
            result["recommendations"].append("建议配置 webhook_secret 以提高安全性")
            return result
        
        secret = self.config.webhook_secret
        
        # 检查长度
        if len(secret) < 16:
            result["recommendations"].append("Secret 长度建议至少16个字符")
        elif len(secret) >= 32:
            result["secure"] = True
        
        # 检查复杂度
        has_upper = any(c.isupper() for c in secret)
        has_lower = any(c.islower() for c in secret)
        has_digit = any(c.isdigit() for c in secret)
        has_special = any(c in "!@#$%^&*()_+-=[]{}|;:,.<>?" for c in secret)
        
        complexity_score = sum([has_upper, has_lower, has_digit, has_special])
        
        if complexity_score < 3:
            result["recommendations"].append("建议使用包含大小写字母、数字和特殊字符的复杂密钥")
        
        if complexity_score >= 3 and len(secret) >= 16:
            result["secure"] = True
        
        return result
    
    def generate_test_signature(self, body: str) -> Optional[str]:
        """生成测试签名"""
        if not self.config.webhook_secret:
            return None
        
        try:
            signature = hmac.new(
                self.config.webhook_secret.encode(),
                body.encode(),
                hashlib.sha256
            ).hexdigest()
            return f"sha256={signature}"
        except Exception as e:
            logger.error(f"生成测试签名失败: {e}")
            return None
    
    async def test_webhook_endpoint(self) -> Dict[str, Any]:
        """测试webhook端点是否可访问"""
        result = {
            "accessible": False,
            "status_code": None,
            "response_time": None,
            "error": None,
            "details": {}
        }

        if not HAS_AIOHTTP:
            result["error"] = "aiohttp 模块未安装，无法测试端点"
            return result

        if not self.config.webhook_url:
            result["error"] = "Webhook URL 未配置"
            return result
        
        # 构造测试数据
        test_data = {
            "update_id": 999999999,
            "message": {
                "message_id": 1,
                "date": 1234567890,
                "chat": {"id": 123456789, "type": "private"},
                "from": {"id": 123456789, "is_bot": False, "first_name": "Test"},
                "text": "/test"
            }
        }
        
        test_body = json.dumps(test_data, separators=(',', ':'))
        test_signature = self.generate_test_signature(test_body)
        
        headers = {
            "Content-Type": "application/json",
            "User-Agent": "TelegramBot (like TwitterBot)"
        }
        
        if test_signature:
            headers["X-Telegram-Bot-Api-Secret-Token"] = test_signature
        
        try:
            import time
            start_time = time.time()
            
            async with aiohttp.ClientSession() as session:
                async with session.post(
                    self.config.webhook_url,
                    data=test_body,
                    headers=headers,
                    timeout=aiohttp.ClientTimeout(total=10)
                ) as response:
                    result["status_code"] = response.status
                    result["response_time"] = round((time.time() - start_time) * 1000, 2)
                    
                    # 读取响应内容
                    try:
                        response_text = await response.text()
                        result["details"]["response"] = response_text[:500]  # 限制长度
                    except:
                        pass
                    
                    # 检查响应状态
                    if response.status == 200:
                        result["accessible"] = True
                        result["details"]["message"] = "Webhook端点可正常访问"
                    elif response.status == 401:
                        result["details"]["message"] = "签名验证失败或需要身份验证"
                    elif response.status == 404:
                        result["details"]["message"] = "Webhook端点不存在"
                    elif response.status == 503:
                        result["details"]["message"] = "服务暂时不可用"
                    else:
                        result["details"]["message"] = f"服务器返回状态码: {response.status}"
        
        except asyncio.TimeoutError:
            result["error"] = "请求超时"
        except aiohttp.ClientError as e:
            result["error"] = f"网络错误: {e}"
        except Exception as e:
            result["error"] = f"测试失败: {e}"
        
        return result
    
    async def run_full_test(self) -> Dict[str, Any]:
        """运行完整的webhook配置测试"""
        logger.info("开始Telegram Webhook配置测试...")
        
        results = {
            "timestamp": asyncio.get_event_loop().time(),
            "url_validation": self.validate_webhook_url(),
            "secret_validation": self.validate_webhook_secret(),
            "endpoint_test": await self.test_webhook_endpoint(),
            "overall_status": "unknown",
            "recommendations": []
        }
        
        # 评估整体状态
        url_valid = results["url_validation"]["valid"]
        secret_secure = results["secret_validation"]["secure"]
        endpoint_accessible = results["endpoint_test"]["accessible"]
        
        if url_valid and endpoint_accessible:
            if secret_secure:
                results["overall_status"] = "excellent"
            else:
                results["overall_status"] = "good"
                results["recommendations"].append("建议配置更安全的webhook secret")
        elif url_valid:
            results["overall_status"] = "warning"
            results["recommendations"].append("Webhook端点无法访问，请检查服务器配置")
        else:
            results["overall_status"] = "error"
            results["recommendations"].append("请修复URL配置问题")
        
        # 添加通用建议
        if not results["secret_validation"]["configured"]:
            results["recommendations"].append("强烈建议配置webhook secret以提高安全性")
        
        logger.info(f"Webhook配置测试完成，整体状态: {results['overall_status']}")
        return results


async def test_webhook_config(config: BotConfig) -> Dict[str, Any]:
    """测试webhook配置的便捷函数"""
    tester = WebhookTester(config)
    return await tester.run_full_test()
