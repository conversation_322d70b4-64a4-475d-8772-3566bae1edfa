"""
测试改进后的恢复服务 - 验证权限检查和数据隔离功能
"""

import pytest
from datetime import datetime, timedelta
from unittest.mock import Mock, AsyncMock, patch
from sqlalchemy.orm import Session

from app.services.recovery_service import RecoveryService, create_recovery_service
from app.models.card_record import CardRecord, CardStatus
from app.models.user import User


class TestRecoveryServiceImproved:
    """测试改进后的恢复服务"""

    @pytest.fixture
    def mock_db(self):
        """模拟数据库会话"""
        return Mock(spec=Session)

    @pytest.fixture
    def recovery_service(self, mock_db):
        """创建恢复服务实例"""
        return RecoveryService(mock_db)

    @pytest.fixture
    def superuser(self):
        """创建超级管理员用户"""
        user = Mock(spec=User)
        user.id = 1
        user.username = "admin"
        user.is_superuser = True
        user.merchant_id = None
        user.department_id = None
        return user

    @pytest.fixture
    def merchant_user(self):
        """创建商户用户"""
        user = Mock(spec=User)
        user.id = 2
        user.username = "merchant_user"
        user.is_superuser = False
        user.merchant_id = 100
        user.department_id = 200
        return user

    @pytest.fixture
    def stuck_record(self):
        """创建卡住的记录"""
        record = Mock(spec=CardRecord)
        record.id = 1
        record.merchant_id = 100
        record.department_id = 200
        record.status = CardStatus.PENDING
        record.retry_count = 1
        record.trace_id = "test_trace_123"
        record.card_number = "1234567890123456"
        record.created_at = datetime.now() - timedelta(hours=2)
        record.updated_at = datetime.now() - timedelta(minutes=45)
        return record

    def test_service_initialization(self, mock_db):
        """测试服务初始化"""
        service = RecoveryService(mock_db)
        
        assert service.db == mock_db
        assert service.model == CardRecord
        assert service.pending_threshold_minutes == 30
        assert service.max_retry_count == 3

    def test_create_recovery_service_factory(self, mock_db):
        """测试工厂函数"""
        service = create_recovery_service(mock_db)
        
        assert isinstance(service, RecoveryService)
        assert service.db == mock_db

    def test_can_access_record_superuser(self, recovery_service, superuser, stuck_record):
        """测试超级管理员可以访问所有记录"""
        result = recovery_service._can_access_record(superuser, stuck_record)
        assert result is True

    def test_can_access_record_same_merchant(self, recovery_service, merchant_user, stuck_record):
        """测试同商户用户可以访问记录"""
        result = recovery_service._can_access_record(merchant_user, stuck_record)
        assert result is True

    def test_can_access_record_different_merchant(self, recovery_service, merchant_user, stuck_record):
        """测试不同商户用户不能访问记录"""
        merchant_user.merchant_id = 999  # 不同的商户ID
        result = recovery_service._can_access_record(merchant_user, stuck_record)
        assert result is False

    def test_can_notify_merchant_superuser(self, recovery_service, superuser):
        """测试超级管理员可以通知所有商户"""
        result = recovery_service._can_notify_merchant(superuser, 100)
        assert result is True

    def test_can_notify_merchant_same_merchant(self, recovery_service, merchant_user):
        """测试商户用户可以通知自己的商户"""
        result = recovery_service._can_notify_merchant(merchant_user, 100)
        assert result is True

    def test_can_notify_merchant_different_merchant(self, recovery_service, merchant_user):
        """测试商户用户不能通知其他商户"""
        result = recovery_service._can_notify_merchant(merchant_user, 999)
        assert result is False

    @pytest.mark.asyncio
    async def test_identify_stuck_requests_with_isolation(self, recovery_service, merchant_user):
        """测试识别卡住请求时应用数据隔离"""
        # 模拟查询结果
        mock_query = Mock()
        mock_query.filter.return_value = mock_query
        mock_query.all.return_value = []
        
        recovery_service.db.query.return_value = mock_query
        recovery_service.apply_data_isolation = Mock(return_value=mock_query)
        
        result = await recovery_service.identify_stuck_requests(merchant_user, 24)
        
        # 验证应用了数据隔离
        recovery_service.apply_data_isolation.assert_called_once_with(mock_query, merchant_user)
        assert result == []

    @pytest.mark.asyncio
    async def test_reprocess_request_permission_denied(self, recovery_service, merchant_user, stuck_record):
        """测试重新处理请求时权限被拒绝"""
        # 设置权限检查失败
        recovery_service._can_access_record = Mock(return_value=False)
        
        result = await recovery_service.reprocess_request(merchant_user, stuck_record)
        
        assert result is False
        recovery_service._can_access_record.assert_called_once_with(merchant_user, stuck_record)

    @pytest.mark.asyncio
    @patch('app.services.binding_log_service.binding_log_service.log_system')
    async def test_reprocess_request_success(self, mock_log_system, recovery_service, merchant_user, stuck_record):
        """测试成功重新处理请求"""
        # 设置权限检查通过
        recovery_service._can_access_record = Mock(return_value=True)
        recovery_service._resubmit_to_queue = AsyncMock(return_value=True)
        
        result = await recovery_service.reprocess_request(merchant_user, stuck_record)
        
        assert result is True
        assert stuck_record.retry_count == 2  # 原来是1，增加后是2
        assert stuck_record.status == 'pending'
        assert stuck_record.department_id is None
        assert stuck_record.walmart_ck_id is None
        
        # 验证记录了日志
        mock_log_system.assert_called_once()

    @pytest.mark.asyncio
    @patch('app.utils.queue_producer.send_bind_card_task')
    async def test_resubmit_to_queue_success(self, mock_send_task, recovery_service, merchant_user, stuck_record):
        """测试成功重新提交到队列"""
        mock_send_task.return_value = None
        
        result = await recovery_service._resubmit_to_queue(stuck_record, merchant_user)
        
        assert result is True
        mock_send_task.assert_called_once()
        
        # 验证消息格式
        call_args = mock_send_task.call_args[0][0]
        assert call_args["record_id"] == str(stuck_record.id)
        assert call_args["merchant_id"] == stuck_record.merchant_id
        assert call_args["is_recovery"] is True
        assert call_args["operator_id"] == merchant_user.id

    def test_update_configuration(self, recovery_service):
        """测试更新配置"""
        result = recovery_service.update_configuration(
            pending_threshold_minutes=45,
            max_retry_count=5
        )
        
        assert result["success"] is True
        assert recovery_service.pending_threshold_minutes == 45
        assert recovery_service.max_retry_count == 5
        assert "old_config" in result
        assert "new_config" in result

    @pytest.mark.asyncio
    async def test_get_recovery_statistics(self, recovery_service, merchant_user):
        """测试获取恢复统计信息"""
        # 模拟卡住的请求
        mock_records = [
            Mock(merchant_id=100, retry_count=1, created_at=datetime.now()),
            Mock(merchant_id=100, retry_count=2, created_at=datetime.now()),
        ]
        recovery_service.identify_stuck_requests = AsyncMock(return_value=mock_records)
        
        result = await recovery_service.get_recovery_statistics(merchant_user, 24)
        
        assert result["total_stuck"] == 2
        assert result["operator"] == merchant_user.username
        assert 100 in result["by_merchant"]
        assert result["by_merchant"][100] == 2
        assert 1 in result["by_retry_count"]
        assert 2 in result["by_retry_count"]


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
