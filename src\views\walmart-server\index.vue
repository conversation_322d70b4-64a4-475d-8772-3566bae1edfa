<script setup>
import { ref, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useWalmartServerStore } from '@/store/modules/walmartServer'

const apiConfigStore = useWalmartServerStore()
const loading = ref(false)
const formData = ref({
    apiUrl: 'https://apicard.swiftpass.cn/app/card/mem/bind.json',
})

const rules = {
    apiUrl: [
        { required: true, message: '请输入沃尔玛API地址', trigger: 'blur' },
        { type: 'url', message: '请输入有效的URL地址', trigger: 'blur' }
    ],
}

const formRef = ref(null)

// 获取API配置
const fetchApiConfig = async () => {
    loading.value = true
    try {
        await apiConfigStore.getWalmartServer()
        formData.value = {
            apiUrl: apiConfigStore.apiUrl,
        }
    } catch (error) {
        console.error('获取API配置失败', error)
        ElMessage.error('获取API配置失败')
    } finally {
        loading.value = false
    }
}

// 提交API配置
const submitForm = async () => {
    if (!formRef.value) return

    await formRef.value.validate(async (valid) => {
        if (valid) {
            try {
                loading.value = true
                const success = await apiConfigStore.updateConfig(formData.value)
                if (success) {
                    ElMessage.success('更新API配置成功')
                }
            } catch (error) {
                console.error('更新API配置失败', error)
            } finally {
                loading.value = false
            }
        } else {
            ElMessage.error('请正确填写所有必填字段')
        }
    })
}

// 重置表单
const resetForm = () => {
    ElMessageBox.confirm('确定要重置表单吗？所有未保存的修改将会丢失。', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
    }).then(() => {
        formData.value = {
            apiUrl: '',
        }
        ElMessage.info('表单已重置')
    }).catch(() => {
        // 取消重置
    })
}

onMounted(() => {
    fetchApiConfig()
})
</script>

<template>
    <div class="walmart-server">
        <el-card class="config-card">
            <template #header>
                <div class="card-header">
                    <span>沃尔玛API配置</span>
                    <el-button type="primary" @click="submitForm" :loading="loading">保存配置</el-button>
                </div>
            </template>

            <el-form ref="formRef" :model="formData" :rules="rules" label-width="140px" :disabled="loading">
                <!-- API配置 -->
                <el-divider>API配置</el-divider>
                <el-form-item label="API地址" prop="apiUrl">
                    <el-input v-model="formData.apiUrl" placeholder="请输入沃尔玛API地址" :disabled="!!formData.apiUrl" />
                </el-form-item>
            </el-form>
        </el-card>
    </div>
</template>

<style scoped>
.walmart-server {
    padding: 20px;
}

.config-card {
    max-width: 800px;
    margin: 0 auto;
}

.card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.el-divider {
    margin: 24px 0;
}

.el-form-item {
    margin-bottom: 22px;
}

.test-connection {
    margin-top: 30px;
    text-align: center;
}

.test-connection .el-button {
    margin: 0 10px;
}
</style>