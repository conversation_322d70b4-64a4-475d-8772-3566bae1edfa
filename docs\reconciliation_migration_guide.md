# 对账台功能数据库迁移指南

## 概述

本文档说明如何执行对账台功能的数据库迁移，包括菜单项和权限配置的添加。

## 迁移内容

### 1. 菜单项
- **主菜单**: 对账台 (`reconciliation`)
- **子菜单**:
  - 部门统计 (`reconciliation:department`)
  - CK明细 (`reconciliation:ck-details`)
  - 绑卡记录 (`reconciliation:records`)

### 2. 权限配置
- **菜单权限**: 4个菜单访问权限
- **数据权限**: 查看权限 (`reconciliation:view`) 和导出权限 (`reconciliation:export`)
- **API权限**: 6个API接口权限

### 3. 角色权限分配
- **超级管理员**: 所有对账台权限
- **商户管理员**: 查看和导出权限
- **操作员**: 仅查看权限

## 迁移文件

### 可用的迁移脚本

1. **v2.7.0-add-reconciliation-menus-permissions.sql** - 完整版迁移脚本
2. **v2.7.2-reconciliation-simple-migration.sql** - 简化版迁移脚本（推荐）

### 推荐使用简化版的原因

简化版迁移脚本解决了以下问题：
- 避免 `updated_at` 字段不存在的错误
- 使用 `INSERT IGNORE` 避免重复数据错误
- 动态检查表是否存在
- 更好的错误处理

## 执行迁移

### 方法1: 使用快速迁移脚本（推荐）

#### Linux/Unix/Mac:
```bash
# 设置数据库连接参数（可选）
export DB_HOST=localhost
export DB_PORT=3306
export DB_USER=root
export DB_PASSWORD=your_password
export DB_NAME=walmart_card_db

# 执行迁移
chmod +x scripts/quick_reconciliation_migration.sh
./scripts/quick_reconciliation_migration.sh
```

#### Windows:
```cmd
REM 设置数据库连接参数（可选）
set DB_HOST=localhost
set DB_PORT=3306
set DB_USER=root
set DB_PASSWORD=your_password
set DB_NAME=walmart_card_db

REM 执行迁移
scripts\quick_reconciliation_migration.bat
```

### 方法2: 直接执行SQL文件

```bash
mysql -h localhost -u root -p walmart_card_db < migrations/v2.7.2-reconciliation-simple-migration.sql
```

### 方法3: 使用Python迁移脚本

```bash
# 开发环境
python scripts/run_reconciliation_migration.py

# 生产环境
python scripts/run_reconciliation_migration.py --env production

# 试运行
python scripts/run_reconciliation_migration.py --dry-run
```

## 验证迁移

### 1. 使用测试脚本验证

```bash
python scripts/test_reconciliation_migration.py
```

### 2. 手动验证

#### 检查菜单创建
```sql
SELECT code, name, path, level 
FROM menus 
WHERE code LIKE 'reconciliation%' 
ORDER BY level, sort_order;
```

#### 检查权限创建
```sql
SELECT code, name, resource_type 
FROM permissions 
WHERE code LIKE '%reconciliation%' 
ORDER BY resource_type, code;
```

#### 检查角色权限分配
```sql
SELECT 
    r.name as '角色名称',
    COUNT(DISTINCT rm.menu_id) as '菜单权限数量',
    COUNT(DISTINCT rp.permission_id) as '功能权限数量'
FROM roles r
LEFT JOIN role_menus rm ON r.id = rm.role_id 
    AND rm.menu_id IN (SELECT id FROM menus WHERE code LIKE 'reconciliation%')
LEFT JOIN role_permissions rp ON r.id = rp.role_id 
    AND rp.permission_id IN (SELECT id FROM permissions WHERE code LIKE '%reconciliation%')
WHERE r.code IN ('super_admin', 'merchant_admin', 'operator')
GROUP BY r.id, r.name
ORDER BY COUNT(DISTINCT rp.permission_id) DESC;
```

## 预期结果

### 菜单数量
- 应该创建 **4个** 对账台菜单项

### 权限数量
- 应该创建 **12个** 对账台相关权限

### 角色权限分配
- **超级管理员**: 4个菜单权限 + 12个功能权限
- **商户管理员**: 4个菜单权限 + 10个功能权限
- **操作员**: 4个菜单权限 + 7个功能权限

## 常见问题

### 1. Column 'updated_at' cannot be null

**原因**: 数据库表中没有 `updated_at` 字段
**解决**: 使用简化版迁移脚本 `v2.7.2-reconciliation-simple-migration.sql`

### 2. Column 'role_id' cannot be null

**原因**: 某些角色不存在，导致角色ID为NULL
**解决**: 简化版脚本使用 `INSERT IGNORE` 和动态角色检查

### 3. Duplicate entry for key

**原因**: 重复执行迁移导致数据重复
**解决**: 脚本会自动清理重复数据后重新插入

### 4. Table 'menu_permissions' doesn't exist

**原因**: 某些项目可能没有菜单权限关联表
**解决**: 简化版脚本会动态检查表是否存在

## 回滚迁移

如果需要回滚迁移，可以执行以下SQL：

```sql
-- 删除角色权限关联
DELETE FROM role_permissions WHERE permission_id IN (
    SELECT id FROM permissions WHERE code LIKE '%reconciliation%'
);

-- 删除角色菜单关联
DELETE FROM role_menus WHERE menu_id IN (
    SELECT id FROM menus WHERE code LIKE 'reconciliation%'
);

-- 删除菜单权限关联（如果表存在）
DELETE FROM menu_permissions WHERE menu_id IN (
    SELECT id FROM menus WHERE code LIKE 'reconciliation%'
) OR permission_id IN (
    SELECT id FROM permissions WHERE code LIKE '%reconciliation%'
);

-- 删除权限
DELETE FROM permissions WHERE code LIKE '%reconciliation%';

-- 删除菜单
DELETE FROM menus WHERE code LIKE 'reconciliation%';
```

## 迁移后操作

1. **重启后端服务**: 确保新的权限配置生效
2. **刷新前端页面**: 加载新的菜单项
3. **测试功能**: 使用具有对账台权限的用户登录测试
4. **检查日志**: 确认没有权限相关错误

## 注意事项

1. **备份数据库**: 执行迁移前建议备份数据库
2. **测试环境先行**: 在生产环境执行前，先在测试环境验证
3. **权限检查**: 确认用户具有对应的对账台权限
4. **浏览器缓存**: 可能需要清除浏览器缓存以看到新菜单

## 技术支持

如果在迁移过程中遇到问题，请：

1. 检查数据库连接配置
2. 查看迁移脚本的输出日志
3. 运行测试脚本验证结果
4. 检查数据库表结构是否符合预期

迁移完成后，对账台功能应该对所有授权用户可见和可用。
