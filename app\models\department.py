from sqlalchemy import Column, String, Integer, BigInteger, <PERSON><PERSON>an, Text, ForeignKey, Date
from sqlalchemy.orm import relationship
from typing import List

from app.models.base import BaseModel, TimestampMixin


class Department(BaseModel, TimestampMixin):
    """部门模型"""

    __tablename__ = "departments"

    id = Column(BigInteger, primary_key=True, index=True, autoincrement=True)
    merchant_id = Column(BigInteger, ForeignKey("merchants.id"), nullable=False, comment="所属商户ID")
    name = Column(String(100), nullable=False, comment="部门名称")
    code = Column(String(50), nullable=True, comment="部门代码")
    description = Column(Text, nullable=True, comment="部门描述")
    status = Column(Boolean, default=True, nullable=False, comment="状态：1启用，0禁用")

    # 绑卡控制字段
    enable_binding = Column(Boolean, default=True, nullable=False, comment="进单开关：1启用绑卡，0禁用绑卡")
    binding_weight = Column(Integer, default=100, nullable=False, comment="进单权重：数值越大优先级越高，用于CK分配算法")

    # 层级关系
    parent_id = Column(BigInteger, ForeignKey("departments.id"), nullable=True, comment="父部门ID")
    level = Column(Integer, default=1, nullable=False, comment="部门层级：1一级部门，2二级部门...")
    path = Column(String(500), nullable=True, comment="部门路径，如：/1/2/3/")
    sort_order = Column(Integer, default=0, nullable=False, comment="排序号")

    # 负责人信息
    manager_id = Column(BigInteger, ForeignKey("users.id"), nullable=True, comment="部门负责人ID")
    manager_name = Column(String(100), nullable=True, comment="部门负责人姓名")
    manager_phone = Column(String(50), nullable=True, comment="负责人电话")
    manager_email = Column(String(100), nullable=True, comment="负责人邮箱")

    # 业务配置
    business_scope = Column(Text, nullable=True, comment="业务范围")
    custom_config = Column(Text, nullable=True, comment="自定义配置JSON")
    remark = Column(Text, nullable=True, comment="备注")
    created_by = Column(BigInteger, ForeignKey("users.id"), nullable=True, comment="创建者ID")

    # 关联关系
    merchant = relationship("Merchant", back_populates="departments")

    parent = relationship(
        "Department",
        remote_side=[id],
        back_populates="children"
    )
    
    children = relationship(
        "Department", 
        back_populates="parent",
        cascade="all, delete-orphan",
        lazy="dynamic"
    )
    
    manager = relationship(
        "User", 
        foreign_keys=[manager_id],
        post_update=True
    )
    
    creator = relationship(
        "User", 
        foreign_keys=[created_by],
        post_update=True
    )
    
    # 用户关系通过user_organizations表管理，不再直接关联
    
    user_organizations = relationship(
        "UserOrganization", 
        back_populates="department",
        cascade="all, delete-orphan",
        lazy="dynamic"
    )

    # 组织关系
    ancestor_relations = relationship(
        "OrganizationRelation",
        foreign_keys="[OrganizationRelation.descendant_id]",
        back_populates="descendant",
        cascade="all, delete-orphan"
    )
    
    descendant_relations = relationship(
        "OrganizationRelation",
        foreign_keys="[OrganizationRelation.ancestor_id]",
        back_populates="ancestor",
        cascade="all, delete-orphan"
    )

    # 新增：业务数据关联
    card_records = relationship("CardRecord", back_populates="department")
    walmart_cks = relationship("WalmartCK", back_populates="department")

    def __repr__(self):
        return f"<Department(id={self.id}, name='{self.name}', code='{self.code}', merchant_id={self.merchant_id})>"

    def to_dict(self, include_children: bool = False, include_users: bool = False) -> dict:
        """转换为字典格式"""
        data = {
            "id": self.id,
            "merchant_id": self.merchant_id,
            "name": self.name,
            "code": self.code,
            "description": self.description,
            "status": self.status,
            "enable_binding": self.enable_binding,
            "binding_weight": self.binding_weight,
            "parent_id": self.parent_id,
            "level": self.level,
            "path": self.path,
            "sort_order": self.sort_order,
            "manager_id": self.manager_id,
            "manager_name": self.manager_name,
            "manager_phone": self.manager_phone,
            "manager_email": self.manager_email,
            "business_scope": self.business_scope,
            "remark": self.remark,
            "created_by": self.created_by,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None,
        }
        
        if include_children:
            data["children"] = [child.to_dict() for child in self.children]
            
        if include_users:
            # 通过user_organizations获取用户信息
            user_orgs = self.user_organizations.filter_by(status=True).all()
            data["users"] = [org.user.to_dict() for org in user_orgs if org.user]
            data["user_count"] = len(data["users"])
            
        return data

    def get_tree(self, max_depth: int = 10) -> dict:
        """获取部门树形结构"""
        data = self.to_dict()
        
        if max_depth > 0:
            children = []
            for child in self.children.filter_by(status=True).order_by(Department.sort_order, Department.name):
                children.append(child.get_tree(max_depth - 1))
            data["children"] = children
            
        data["user_count"] = self.get_user_count()
        data["total_user_count"] = self.get_total_user_count()
        
        return data

    def get_user_count(self) -> int:
        """获取直属用户数量（通过user_organizations表）"""
        return self.user_organizations.filter_by(status=True).count()

    def get_total_user_count(self) -> int:
        """获取包含子部门的总用户数量"""
        count = self.get_user_count()
        for child in self.children:
            count += child.get_total_user_count()
        return count

    def get_all_children(self) -> List['Department']:
        """获取所有子部门（递归）"""
        children = []
        for child in self.children:
            children.append(child)
            children.extend(child.get_all_children())
        return children

    def get_all_children_ids(self) -> List[int]:
        """获取所有子部门ID（递归）"""
        return [child.id for child in self.get_all_children()]

    def get_ancestors(self) -> List['Department']:
        """获取所有父级部门"""
        ancestors = []
        current = self.parent
        while current:
            ancestors.append(current)
            current = current.parent
        return ancestors

    def get_ancestor_ids(self) -> List[int]:
        """获取所有父级部门ID"""
        return [ancestor.id for ancestor in self.get_ancestors()]

    def is_ancestor_of(self, department: 'Department') -> bool:
        """检查是否为指定部门的祖先"""
        return self.id in department.get_ancestor_ids()

    def is_descendant_of(self, department: 'Department') -> bool:
        """检查是否为指定部门的后代"""
        return department.id in self.get_ancestor_ids()

    def get_full_path(self) -> str:
        """获取完整路径名称"""
        ancestors = self.get_ancestors()
        ancestors.reverse()  # 从根到当前
        path_names = [ancestor.name for ancestor in ancestors]
        path_names.append(self.name)
        return " > ".join(path_names)

    def update_path(self):
        """更新部门路径"""
        if self.parent:
            self.path = f"{self.parent.path}{self.parent.id}/"
            self.level = self.parent.level + 1
        else:
            self.path = f"/{self.merchant_id}/"
            self.level = 1

    def can_delete(self) -> tuple[bool, str]:
        """检查是否可以删除"""
        if self.children.count() > 0:
            return False, "存在子部门，无法删除"

        if self.user_organizations.filter_by(status=True).count() > 0:
            return False, "存在关联用户，无法删除"

        if self.code == "ROOT":
            return False, "根部门无法删除"

        return True, ""

    @property
    def is_root(self) -> bool:
        """是否为根部门"""
        return self.parent_id is None or self.code == "ROOT"

    @property
    def is_leaf(self) -> bool:
        """是否为叶子部门"""
        return self.children.count() == 0

    @property
    def can_bind_cards(self) -> bool:
        """是否可以进行绑卡操作"""
        return self.status and self.enable_binding and self.binding_weight > 0

    def get_binding_priority_score(self) -> int:
        """获取绑卡优先级分数（用于权重算法）"""
        if not self.can_bind_cards:
            return 0
        return self.binding_weight

    def update_binding_controls(self, enable_binding: bool = None, binding_weight: int = None) -> dict:
        """更新绑卡控制设置并返回变更记录"""
        changes = {}

        if enable_binding is not None and enable_binding != self.enable_binding:
            changes['enable_binding'] = {
                'old': self.enable_binding,
                'new': enable_binding
            }
            self.enable_binding = enable_binding

        if binding_weight is not None and binding_weight != self.binding_weight:
            changes['binding_weight'] = {
                'old': self.binding_weight,
                'new': binding_weight
            }
            self.binding_weight = binding_weight

        return changes


