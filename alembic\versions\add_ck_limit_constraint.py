"""添加CK绑卡限制约束防止超卖

Revision ID: add_ck_limit_constraint
Revises: previous_revision
Create Date: 2024-01-01 00:00:00.000000

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'add_ck_limit_constraint'
down_revision = 'previous_revision'  # 替换为实际的上一个版本ID
branch_labels = None
depends_on = None


def upgrade():
    """添加CK绑卡限制约束"""
    
    # 1. 添加CHECK约束确保bind_count不超过total_limit
    op.execute("""
        ALTER TABLE walmart_ck 
        ADD CONSTRAINT ck_bind_count_limit 
        CHECK (bind_count <= total_limit)
    """)
    
    # 2. 添加CHECK约束确保bind_count不为负数
    op.execute("""
        ALTER TABLE walmart_ck 
        ADD CONSTRAINT ck_bind_count_non_negative 
        CHECK (bind_count >= 0)
    """)
    
    # 3. 添加CHECK约束确保total_limit为正数
    op.execute("""
        ALTER TABLE walmart_ck 
        ADD CONSTRAINT ck_total_limit_positive 
        CHECK (total_limit > 0)
    """)
    
    # 4. 创建索引优化查询性能
    op.create_index(
        'idx_walmart_ck_bind_status',
        'walmart_ck',
        ['merchant_id', 'active', 'bind_count', 'total_limit'],
        postgresql_where=sa.text('is_deleted = false')
    )
    
    # 5. 创建部分索引优化可用CK查询
    op.create_index(
        'idx_walmart_ck_available',
        'walmart_ck',
        ['merchant_id', 'department_id', 'bind_count'],
        postgresql_where=sa.text('active = true AND is_deleted = false AND bind_count < total_limit')
    )


def downgrade():
    """移除CK绑卡限制约束"""
    
    # 移除索引
    op.drop_index('idx_walmart_ck_available')
    op.drop_index('idx_walmart_ck_bind_status')
    
    # 移除约束
    op.execute("ALTER TABLE walmart_ck DROP CONSTRAINT IF EXISTS ck_total_limit_positive")
    op.execute("ALTER TABLE walmart_ck DROP CONSTRAINT IF EXISTS ck_bind_count_non_negative")
    op.execute("ALTER TABLE walmart_ck DROP CONSTRAINT IF EXISTS ck_bind_count_limit")
