# Go代码混淆脚本
param(
    [string]$SourceDir = ".",
    [string]$OutputDir = "obfuscated",
    [switch]$Backup = $true
)

function Write-Info($message) { Write-Host "[INFO] $message" -ForegroundColor Green }
function Write-Warning($message) { Write-Host "[WARN] $message" -ForegroundColor Yellow }
function Write-Error($message) { Write-Host "[ERROR] $message" -ForegroundColor Red }

function Main {
    Write-Host "Go代码混淆工具" -ForegroundColor Cyan
    Write-Host "===============" -ForegroundColor Cyan
    Write-Output ""
    
    # 检查garble工具
    if (!(CheckGarble)) {
        Write-Info "正在安装garble..."
        InstallGarble
    }
    
    # 备份源码
    if ($Backup) {
        BackupSource
    }
    
    # 执行混淆
    ObfuscateCode
    
    Write-Info "混淆完成！"
}

function CheckGarble {
    try {
        $garbleVersion = garble version 2>$null
        Write-Info "Garble已安装: $garbleVersion"
        return $true
    } catch {
        Write-Warning "Garble未安装"
        return $false
    }
}

function InstallGarble {
    try {
        Write-Info "安装garble混淆工具..."
        go install mvdan.cc/garble@latest
        Write-Info "Garble安装完成"
    } catch {
        Write-Error "Garble安装失败: $_"
        exit 1
    }
}

function BackupSource {
    $backupDir = "backup-$(Get-Date -Format 'yyyyMMdd-HHmmss')"
    Write-Info "备份源码到: $backupDir"
    
    Copy-Item -Recurse -Path $SourceDir -Destination $backupDir -Exclude @("*.exe", "dist", "backup-*", ".git")
    Write-Info "源码备份完成"
}

function ObfuscateCode {
    Write-Info "开始代码混淆..."
    
    # 设置环境变量
    $env:GOOS = "linux"
    $env:GOARCH = "amd64"
    $env:CGO_ENABLED = "0"
    
    # 混淆构建
    $garbleFlags = @(
        "-tiny"  # 最小化输出
        "-literals"  # 混淆字符串字面量
        "-seed=random"  # 随机种子
    )
    
    $buildCmd = "garble " + ($garbleFlags -join " ") + " build -ldflags='-w -s -buildid=' -trimpath -o walmart-gateway-obfuscated main.go"
    
    Write-Info "混淆命令: $buildCmd"
    Invoke-Expression $buildCmd
    
    if ($LASTEXITCODE -eq 0) {
        Write-Info "代码混淆成功！"
        
        # 显示文件信息
        if (Test-Path "walmart-gateway-obfuscated") {
            $fileInfo = Get-Item "walmart-gateway-obfuscated"
            Write-Info "混淆后文件大小: $([math]::Round($fileInfo.Length / 1MB, 2)) MB"
        }
    } else {
        Write-Error "代码混淆失败！"
    }
    
    # 恢复环境变量
    $env:GOOS = "windows"
    $env:GOARCH = "amd64"
    $env:CGO_ENABLED = "1"
}

# 执行主函数
Main
