<template>
  <el-card class="department-ranking-card" shadow="hover">
    <template #header>
      <div class="card-header">
        <span>部门绑卡业绩排名</span>
        <el-radio-group v-model="timeRange" size="small" @change="fetchData">
          <el-radio-button value="today">今日</el-radio-button>
          <el-radio-button value="week">本周</el-radio-button>
          <el-radio-button value="month">本月</el-radio-button>
        </el-radio-group>
      </div>
    </template>

    <div v-loading="loading" class="ranking-content">
      <div v-if="data.length > 0">
        <!-- 前三名特殊显示 -->
        <div class="top-three" v-if="data.length >= 3">
          <div class="podium">
            <!-- 第二名 -->
            <div class="podium-item second" v-if="data[1]">
              <div class="medal silver">2</div>
              <div class="dept-info">
                <div class="dept-name">{{ data[1].department_name }}</div>
                <div class="dept-amount">{{ formatAmount(data[1].success_amount_yuan) }}</div>
                <div class="dept-rate">成功率: {{ data[1].success_rate }}%</div>
              </div>
            </div>

            <!-- 第一名 -->
            <div class="podium-item first" v-if="data[0]">
              <div class="medal gold">1</div>
              <div class="dept-info">
                <div class="dept-name">{{ data[0].department_name }}</div>
                <div class="dept-amount">{{ formatAmount(data[0].success_amount_yuan) }}</div>
                <div class="dept-rate">成功率: {{ data[0].success_rate }}%</div>
              </div>
            </div>

            <!-- 第三名 -->
            <div class="podium-item third" v-if="data[2]">
              <div class="medal bronze">3</div>
              <div class="dept-info">
                <div class="dept-name">{{ data[2].department_name }}</div>
                <div class="dept-amount">{{ formatAmount(data[2].success_amount_yuan) }}</div>
                <div class="dept-rate">成功率: {{ data[2].success_rate }}%</div>
              </div>
            </div>
          </div>
        </div>

        <!-- 完整排名表格 -->
        <div class="ranking-table">
          <el-table :data="data" size="small" stripe>
            <el-table-column label="排名" width="60" align="center">
              <template #default="scope">
                <div class="rank-badge" :class="getRankClass(scope.row.rank)">
                  {{ scope.row.rank }}
                </div>
              </template>
            </el-table-column>
            <el-table-column prop="department_name" label="部门名称" show-overflow-tooltip />
            <el-table-column prop="department_code" label="部门代码" width="100" />
            <el-table-column prop="total_requests" label="总请求数" width="100" align="right" />
            <el-table-column prop="success_count" label="成功数" width="80" align="right" />
            <el-table-column label="成功率" width="120" align="center">
              <template #default="scope">
                <el-progress :percentage="scope.row.success_rate" :color="getSuccessRateColor(scope.row.success_rate)"
                  :format="() => `${scope.row.success_rate}%`" :stroke-width="6" />
              </template>
            </el-table-column>
            <el-table-column label="成功金额" align="right">
              <template #default="scope">
                {{ formatAmount(scope.row.success_amount_yuan) }}
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>

      <!-- 无数据提示 -->
      <div v-else class="no-data">
        <el-empty description="暂无部门数据" />
      </div>
    </div>
  </el-card>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { dashboardApi } from '@/api/modules/dashboard'
import { useUserStore } from '@/store/modules/user'
import { usePermissionStore } from '@/store/modules/permission'

const userStore = useUserStore()
const permissionStore = usePermissionStore()

const loading = ref(false)
const timeRange = ref('today')
const data = ref([])

const formatAmount = (amount) => {
  // 如果是字符串就不处理
  if (typeof amount === 'string') {
    return amount
  }
  return amount.toFixed(2)
}

const getSuccessRateColor = (rate) => {
  if (rate >= 90) return '#67C23A'
  if (rate >= 70) return '#E6A23C'
  return '#F56C6C'
}

const getRankClass = (rank) => {
  if (rank === 1) return 'gold'
  if (rank === 2) return 'silver'
  if (rank === 3) return 'bronze'
  return 'normal'
}

const fetchData = async () => {
  loading.value = true
  try {
    const params = {
      time_range: timeRange.value
    }

    const response = await dashboardApi.getDepartmentRanking(params)
    data.value = response.data || []
  } catch (error) {
    console.error('获取部门排名失败:', error)
    data.value = []
  } finally {
    loading.value = false
  }
}

onMounted(() => {
  // 只有商户管理员才显示部门排名
  if (!permissionStore.isSuperAdmin) {
    fetchData()
  }
})

// 暴露刷新方法给父组件
defineExpose({
  refresh: fetchData
})
</script>

<style scoped>
.department-ranking-card {
  margin-bottom: 16px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 16px;
  font-weight: 600;
}

.ranking-content {
  min-height: 300px;
}

.top-three {
  margin-bottom: 20px;
}

.podium {
  display: flex;
  justify-content: center;
  align-items: end;
  gap: 16px;
  padding: 20px;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-radius: 8px;
}

.podium-item {
  text-align: center;
  padding: 16px 12px;
  border-radius: 8px;
  background: white;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  min-width: 120px;
}

.podium-item.first {
  transform: scale(1.1);
  border: 2px solid #FFD700;
}

.podium-item.second {
  border: 2px solid #C0C0C0;
}

.podium-item.third {
  border: 2px solid #CD7F32;
}

.medal {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  color: white;
  margin: 0 auto 8px;
  font-size: 16px;
}

.medal.gold {
  background: linear-gradient(135deg, #FFD700, #FFA500);
}

.medal.silver {
  background: linear-gradient(135deg, #C0C0C0, #A0A0A0);
}

.medal.bronze {
  background: linear-gradient(135deg, #CD7F32, #B8860B);
}

.dept-info {
  font-size: 12px;
}

.dept-name {
  font-weight: 600;
  color: #303133;
  margin-bottom: 4px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.dept-amount {
  font-size: 14px;
  font-weight: bold;
  color: #67C23A;
  margin-bottom: 2px;
}

.dept-rate {
  color: #606266;
}

.ranking-table {
  margin-top: 20px;
}

.rank-badge {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: bold;
  color: white;
  margin: 0 auto;
}

.rank-badge.gold {
  background: #FFD700;
}

.rank-badge.silver {
  background: #C0C0C0;
}

.rank-badge.bronze {
  background: #CD7F32;
}

.rank-badge.normal {
  background: #909399;
}

.no-data {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
}

@media (max-width: 768px) {
  .podium {
    flex-direction: column;
    align-items: center;
  }

  .podium-item {
    min-width: 200px;
    margin-bottom: 12px;
  }

  .podium-item.first {
    transform: none;
    order: -1;
  }
}
</style>
