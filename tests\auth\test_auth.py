#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
认证模块测试
测试用户登录、登出、Token验证等功能
"""

import sys
import os
import time
from typing import List, Dict

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.dirname(__file__))))

from test.conftest import TestBase, get_test_accounts, format_test_result, print_test_summary

class AuthTestSuite(TestBase):
    """认证模块测试套件"""
    
    def __init__(self):
        super().__init__()
        self.results = []
        self.test_accounts = get_test_accounts()
    
    def test_valid_login(self):
        """测试有效用户登录"""
        print("\n=== 测试有效用户登录 ===")
        
        for account_type, account_info in self.test_accounts.items():
            username = account_info["username"]
            password = account_info["password"]
            
            print(f"测试 {account_type} 登录: {username}")
            
            # 执行登录
            token = self.login(username, password)
            
            if token:
                self.results.append(format_test_result(
                    f"{account_type}_登录",
                    True,
                    f"用户 {username} 登录成功",
                    {"username": username, "token_length": len(token)}
                ))
                print(f"✅ {username} 登录成功")
            else:
                self.results.append(format_test_result(
                    f"{account_type}_登录",
                    False,
                    f"用户 {username} 登录失败"
                ))
                print(f"❌ {username} 登录失败")
    
    def test_invalid_login(self):
        """测试无效用户登录"""
        print("\n=== 测试无效用户登录 ===")
        
        invalid_credentials = [
            {"username": "invalid_user", "password": "wrong_password"},
            {"username": "admin", "password": "wrong_password"},
            {"username": "", "password": ""},
            {"username": "test1", "password": ""},
        ]
        
        for cred in invalid_credentials:
            username = cred["username"]
            password = cred["password"]
            
            print(f"测试无效登录: {username}")
            
            token = self.login(username, password)
            
            if not token:
                self.results.append(format_test_result(
                    f"无效登录拒绝_{username}",
                    True,
                    f"正确拒绝无效登录: {username}"
                ))
                print(f"✅ 正确拒绝无效登录: {username}")
            else:
                self.results.append(format_test_result(
                    f"无效登录拒绝_{username}",
                    False,
                    f"错误允许无效登录: {username}"
                ))
                print(f"❌ 错误允许无效登录: {username}")
    
    def test_token_validation(self):
        """测试Token验证"""
        print("\n=== 测试Token验证 ===")
        
        # 使用有效token测试
        admin_token = self.tokens.get("admin")
        if admin_token:
            status_code, response = self.get_user_info(admin_token)
            
            if status_code == 200:
                self.results.append(format_test_result(
                    "有效Token验证",
                    True,
                    "有效Token验证成功"
                ))
                print("✅ 有效Token验证成功")
            else:
                self.results.append(format_test_result(
                    "有效Token验证",
                    False,
                    f"有效Token验证失败，状态码: {status_code}"
                ))
                print(f"❌ 有效Token验证失败，状态码: {status_code}")
        
        # 使用无效token测试
        invalid_token = "invalid_token_12345"
        status_code, response = self.get_user_info(invalid_token)
        
        if status_code in [401, 403]:
            self.results.append(format_test_result(
                "无效Token拒绝",
                True,
                "正确拒绝无效Token"
            ))
            print("✅ 正确拒绝无效Token")
        else:
            self.results.append(format_test_result(
                "无效Token拒绝",
                False,
                f"错误接受无效Token，状态码: {status_code}"
            ))
            print(f"❌ 错误接受无效Token，状态码: {status_code}")
    
    def test_logout(self):
        """测试登出功能"""
        print("\n=== 测试登出功能 ===")
        
        # 先登录获取token
        token = self.login("test1", "12345678")
        if not token:
            self.results.append(format_test_result(
                "登出测试前置条件",
                False,
                "无法获取测试token"
            ))
            return
        
        # 测试登出
        logout_success = self.logout(token)
        
        if logout_success:
            self.results.append(format_test_result(
                "用户登出",
                True,
                "用户登出成功"
            ))
            print("✅ 用户登出成功")
            
            # 验证登出后token是否失效
            status_code, response = self.get_user_info(token)
            if status_code in [401, 403]:
                self.results.append(format_test_result(
                    "登出后Token失效",
                    True,
                    "登出后Token正确失效"
                ))
                print("✅ 登出后Token正确失效")
            else:
                self.results.append(format_test_result(
                    "登出后Token失效",
                    False,
                    f"登出后Token仍然有效，状态码: {status_code}"
                ))
                print(f"❌ 登出后Token仍然有效，状态码: {status_code}")
        else:
            self.results.append(format_test_result(
                "用户登出",
                False,
                "用户登出失败"
            ))
            print("❌ 用户登出失败")
    
    def test_password_security(self):
        """测试密码安全性"""
        print("\n=== 测试密码安全性 ===")
        
        # 测试SQL注入尝试
        sql_injection_passwords = [
            "' OR '1'='1",
            "'; DROP TABLE users; --",
            "admin'--",
            "' UNION SELECT * FROM users --"
        ]
        
        for password in sql_injection_passwords:
            token = self.login("admin", password)
            
            if not token:
                self.results.append(format_test_result(
                    f"SQL注入防护_{password[:10]}",
                    True,
                    "正确阻止SQL注入尝试"
                ))
                print(f"✅ 正确阻止SQL注入尝试: {password[:20]}...")
            else:
                self.results.append(format_test_result(
                    f"SQL注入防护_{password[:10]}",
                    False,
                    "SQL注入防护失败"
                ))
                print(f"❌ SQL注入防护失败: {password[:20]}...")
    
    def run_all_tests(self):
        """运行所有认证测试"""
        print("开始认证模块测试")
        print("="*60)
        
        start_time = time.time()
        
        # 运行所有测试
        self.test_valid_login()
        self.test_invalid_login()
        self.test_token_validation()
        self.test_logout()
        self.test_password_security()
        
        end_time = time.time()
        duration = end_time - start_time
        
        # 打印测试摘要
        print_test_summary(self.results)
        print(f"测试耗时: {duration:.2f}秒")
        
        return self.results

def main():
    """主函数"""
    test_suite = AuthTestSuite()
    results = test_suite.run_all_tests()
    
    # 判断测试是否通过
    failed_tests = [r for r in results if not r.get("success", False)]
    
    if not failed_tests:
        print("\n所有认证测试通过！")
        return 0
    else:
        print(f"\n发现 {len(failed_tests)} 个问题需要修复")
        for test in failed_tests:
            print(f"  - {test['test_name']}: {test['message']}")
        return 1

if __name__ == "__main__":
    sys.exit(main())
