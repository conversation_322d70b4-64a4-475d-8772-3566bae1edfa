#!/usr/bin/env python3
"""
测试回调服务是否正常工作
"""
import sys
import asyncio
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from app.services.optimized_callback_service import optimized_callback_service
from app.services.callback_service_adapter import callback_service_adapter
from app.db.session import SessionLocal
from app.core.logging import get_logger

logger = get_logger("callback_service_test")


async def test_callback_service():
    """测试回调服务"""
    print("🧪 开始测试回调服务...")
    
    # 测试1: 检查服务实例
    print("1. 检查服务实例...")
    print(f"   OptimizedCallbackService: {optimized_callback_service}")
    print(f"   CallbackServiceAdapter: {callback_service_adapter}")
    print("   ✅ 服务实例创建成功")
    
    # 测试2: 检查HTTP客户端配置
    print("\n2. 检查HTTP客户端配置...")
    try:
        from app.services.optimized_callback_service import get_http_client_config
        config = get_http_client_config()
        print(f"   HTTP客户端配置: {config}")
        print("   ✅ HTTP客户端配置正常")
    except Exception as e:
        print(f"   ❌ HTTP客户端配置错误: {e}")
        return False
    
    # 测试3: 检查数据库连接
    print("\n3. 检查数据库连接...")
    try:
        with SessionLocal() as db:
            # 简单的数据库查询测试
            from sqlalchemy import text
            result = db.execute(text("SELECT 1")).fetchone()
            print(f"   数据库查询结果: {result}")
            print("   ✅ 数据库连接正常")
    except Exception as e:
        print(f"   ❌ 数据库连接错误: {e}")
        return False
    
    # 测试4: 测试适配器状态
    print("\n4. 检查适配器状态...")
    try:
        status = callback_service_adapter.get_status()
        print(f"   适配器状态: {status}")
        print("   ✅ 适配器状态正常")
    except Exception as e:
        print(f"   ❌ 适配器状态错误: {e}")
        return False
    
    # 测试5: 测试HTTP客户端创建
    print("\n5. 测试HTTP客户端创建...")
    try:
        async with optimized_callback_service.get_http_client() as client:
            print(f"   HTTP客户端类型: {type(client)}")
            print(f"   HTTP客户端状态: {'已关闭' if client.is_closed else '正常'}")
            print("   ✅ HTTP客户端创建成功")
    except Exception as e:
        print(f"   ❌ HTTP客户端创建失败: {e}")
        return False
    
    # 测试6: 测试数据库会话管理器
    print("\n6. 测试数据库会话管理器...")
    try:
        async with optimized_callback_service._get_db_session() as session:
            print(f"   数据库会话类型: {type(session)}")
            print("   ✅ 数据库会话管理器正常")
    except Exception as e:
        print(f"   ❌ 数据库会话管理器错误: {e}")
        return False
    
    print("\n🎉 所有测试通过！回调服务工作正常。")
    return True


async def test_callback_processing():
    """测试回调处理逻辑（模拟数据）"""
    print("\n🔄 测试回调处理逻辑...")
    
    # 模拟回调数据
    test_data = {
        "record_id": "00000000-0000-0000-0000-000000000000",  # 不存在的ID
        "merchant_id": 999,  # 不存在的商户ID
        "retry_count": 0,
        "ext_data": None,
        "trace_id": "test-trace-id"
    }
    
    try:
        with SessionLocal() as db:
            # 这应该会优雅地处理不存在的记录
            result = await callback_service_adapter.process_callback_from_queue(db, test_data)
            print(f"   处理结果: {result}")
            print("   ✅ 回调处理逻辑正常（优雅处理不存在的记录）")
    except Exception as e:
        print(f"   ⚠️  回调处理异常（预期行为）: {e}")
        print("   ✅ 异常处理正常")
    
    return True


async def main():
    """主函数"""
    print("=" * 60)
    print("回调服务测试")
    print("=" * 60)
    
    try:
        # 基础服务测试
        success1 = await test_callback_service()
        
        # 回调处理测试
        success2 = await test_callback_processing()
        
        if success1 and success2:
            print("\n✅ 所有测试通过！回调服务已准备就绪。")
            return 0
        else:
            print("\n❌ 部分测试失败，请检查配置。")
            return 1
            
    except Exception as e:
        print(f"\n💥 测试过程中发生异常: {e}")
        return 1
    finally:
        # 清理资源
        try:
            await optimized_callback_service.close()
            print("\n🧹 资源清理完成")
        except Exception as e:
            print(f"\n⚠️  资源清理异常: {e}")


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
