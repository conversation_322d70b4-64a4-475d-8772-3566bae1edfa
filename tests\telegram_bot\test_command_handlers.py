"""
测试Telegram命令处理器
"""

import pytest
from unittest.mock import Mock, AsyncMock, patch, MagicMock
from datetime import datetime, timedelta
from sqlalchemy.orm import Session
from telegram import Update, Message, Chat, User as TelegramUser
from telegram.ext import ContextTypes

from app.telegram_bot.command_handlers.base_handler import BaseCommandHandler
from app.telegram_bot.command_handlers.help_handler import HelpCommandHandler
from app.telegram_bot.command_handlers.stats_handler import StatsCommandHandler
from app.telegram_bot.config import BotConfig
from app.telegram_bot.rate_limiter import RateLimiter
from app.telegram_bot.exceptions import (
    PermissionError,
    GroupNotBoundError,
    UserNotVerifiedError,
    RateLimitError
)
from app.models.telegram_group import TelegramGroup, BindStatus, ChatType
from app.models.telegram_user import TelegramUser as TelegramUserModel, VerificationStatus
from app.models.user import User
from app.models.merchant import Merchant
from app.models.base import local_now


@pytest.fixture
def mock_config():
    """创建模拟配置"""
    config = Mock(spec=BotConfig)
    config.enable_audit_log = True
    config.mask_sensitive_data = True
    return config


@pytest.fixture
def mock_rate_limiter():
    """创建模拟频率限制器"""
    rate_limiter = Mock(spec=RateLimiter)
    rate_limiter.check_all_limits = Mock()
    return rate_limiter


@pytest.fixture
def test_merchant(db: Session):
    """创建测试商户"""
    merchant = Merchant(
        name="测试商户",
        code="TEST_MERCHANT",
        status="active"
    )
    db.add(merchant)
    db.commit()
    db.refresh(merchant)
    return merchant


@pytest.fixture
def test_telegram_group(db: Session, test_merchant: Merchant):
    """创建测试Telegram群组"""
    group = TelegramGroup(
        chat_id=-1001234567890,
        chat_title="测试群组",
        chat_type=ChatType.SUPERGROUP,
        merchant_id=test_merchant.id,
        bind_token="test_bind_token_123",
        bind_status=BindStatus.ACTIVE,
        bind_time=local_now()
    )
    db.add(group)
    db.commit()
    db.refresh(group)
    return group


@pytest.fixture
def test_system_user(db: Session):
    """创建测试系统用户"""
    user = User(
        username="testuser",
        email="<EMAIL>",
        password_hash="hashed_password",
        is_active=True,
        is_superuser=False
    )
    db.add(user)
    db.commit()
    db.refresh(user)
    return user


@pytest.fixture
def test_telegram_user_model(db: Session, test_system_user: User):
    """创建测试Telegram用户模型"""
    telegram_user = TelegramUserModel(
        telegram_user_id=123456789,
        telegram_username="testuser",
        telegram_first_name="Test",
        telegram_last_name="User",
        system_user_id=test_system_user.id,
        verification_status=VerificationStatus.VERIFIED,
        verification_time=local_now()
    )
    db.add(telegram_user)
    db.commit()
    db.refresh(telegram_user)
    return telegram_user


@pytest.fixture
def mock_update():
    """创建模拟Update对象"""
    update = Mock(spec=Update)
    
    # 模拟Chat
    chat = Mock(spec=Chat)
    chat.id = -1001234567890
    chat.type = "supergroup"
    chat.title = "测试群组"
    
    # 模拟User
    user = Mock(spec=TelegramUser)
    user.id = 123456789
    user.username = "testuser"
    user.first_name = "Test"
    user.last_name = "User"
    
    # 模拟Message
    message = Mock(spec=Message)
    message.text = "/help"
    message.reply_text = AsyncMock()
    
    # 设置关联
    update.effective_chat = chat
    update.effective_user = user
    update.message = message
    
    return update


@pytest.fixture
def mock_context():
    """创建模拟Context对象"""
    return Mock(spec=ContextTypes.DEFAULT_TYPE)


class TestHelpCommandHandler:
    """测试帮助命令处理器"""

    @pytest.fixture
    def help_handler(self, db: Session, mock_config, mock_rate_limiter):
        """创建帮助命令处理器"""
        return HelpCommandHandler(db, mock_config, mock_rate_limiter)

    @pytest.mark.asyncio
    async def test_execute_command_group_bound(
        self, 
        help_handler: HelpCommandHandler,
        mock_update,
        mock_context,
        test_telegram_group: TelegramGroup
    ):
        """测试已绑定群组的帮助命令"""
        result = await help_handler.execute_command(mock_update, mock_context)
        
        assert result is not None
        assert result["command"] == "help"
        assert result["group_bound"] is True
        
        # 验证发送了响应
        mock_update.message.reply_text.assert_called_once()
        call_args = mock_update.message.reply_text.call_args
        assert "可用命令" in call_args[0][0]
        assert "/stats" in call_args[0][0]

    @pytest.mark.asyncio
    async def test_execute_command_group_not_bound(
        self, 
        help_handler: HelpCommandHandler,
        mock_update,
        mock_context
    ):
        """测试未绑定群组的帮助命令"""
        # 使用不存在的群组ID
        mock_update.effective_chat.id = -1001111111111
        
        result = await help_handler.execute_command(mock_update, mock_context)
        
        assert result is not None
        assert result["command"] == "help"
        assert result["group_bound"] is False
        
        # 验证发送了响应
        mock_update.message.reply_text.assert_called_once()
        call_args = mock_update.message.reply_text.call_args
        assert "群组未绑定" in call_args[0][0]
        assert "/bind" in call_args[0][0]

    @pytest.mark.asyncio
    async def test_handle_start_command(
        self, 
        help_handler: HelpCommandHandler,
        mock_update,
        mock_context
    ):
        """测试start命令"""
        result = await help_handler.handle_start(mock_update, mock_context)
        
        assert result is not None
        assert result["command"] == "start"
        assert "Test User" in result["user"]
        
        # 验证发送了欢迎消息
        mock_update.message.reply_text.assert_called_once()
        call_args = mock_update.message.reply_text.call_args
        assert "欢迎" in call_args[0][0]
        assert "Test User" in call_args[0][0]

    @pytest.mark.asyncio
    async def test_handle_message_help_keywords(
        self, 
        help_handler: HelpCommandHandler,
        mock_update,
        mock_context,
        test_telegram_group: TelegramGroup
    ):
        """测试帮助关键词触发"""
        mock_update.message.text = "帮助"
        
        with patch.object(help_handler, 'execute_command', new_callable=AsyncMock) as mock_execute:
            await help_handler.handle_message(mock_update, mock_context)
            mock_execute.assert_called_once_with(mock_update, mock_context)

    @pytest.mark.asyncio
    async def test_handle_message_stats_keywords_bound_group(
        self, 
        help_handler: HelpCommandHandler,
        mock_update,
        mock_context,
        test_telegram_group: TelegramGroup
    ):
        """测试统计关键词触发（已绑定群组）"""
        mock_update.message.text = "今日统计"
        
        await help_handler.handle_message(mock_update, mock_context)
        
        # 验证发送了统计提示
        mock_update.message.reply_text.assert_called_once()
        call_args = mock_update.message.reply_text.call_args
        assert "统计查询提示" in call_args[0][0]
        assert "/stats" in call_args[0][0]

    @pytest.mark.asyncio
    async def test_handle_message_stats_keywords_unbound_group(
        self, 
        help_handler: HelpCommandHandler,
        mock_update,
        mock_context
    ):
        """测试统计关键词触发（未绑定群组）"""
        # 使用不存在的群组ID
        mock_update.effective_chat.id = -1001111111111
        mock_update.message.text = "今日统计"
        
        await help_handler.handle_message(mock_update, mock_context)
        
        # 验证发送了未绑定提示
        mock_update.message.reply_text.assert_called_once()
        call_args = mock_update.message.reply_text.call_args
        assert "群组未绑定" in call_args[0][0]
        assert "/bind" in call_args[0][0]

    def test_generate_help_text_bound(self, help_handler: HelpCommandHandler):
        """测试生成已绑定群组的帮助文本"""
        help_text = help_handler._generate_help_text(True)
        
        assert "可用命令" in help_text
        assert "/stats" in help_text
        assert "/stats_week" in help_text
        assert "/stats_month" in help_text
        assert "管理员命令" in help_text

    def test_generate_help_text_unbound(self, help_handler: HelpCommandHandler):
        """测试生成未绑定群组的帮助文本"""
        help_text = help_handler._generate_help_text(False)
        
        assert "群组未绑定" in help_text
        assert "绑定步骤" in help_text
        assert "/bind" in help_text
        assert "基础命令" in help_text


class TestBaseCommandHandler:
    """测试基础命令处理器"""

    class TestCommandHandler(BaseCommandHandler):
        """测试用的具体命令处理器"""
        
        def __init__(self, *args, **kwargs):
            super().__init__(*args, **kwargs)
            self.verify_permissions_called = False
            self.execute_command_called = False
            self.execute_result = {"test": "success"}
        
        async def verify_permissions(self, update, context):
            self.verify_permissions_called = True
        
        async def execute_command(self, update, context):
            self.execute_command_called = True
            return self.execute_result

    @pytest.fixture
    def test_handler(self, db: Session, mock_config, mock_rate_limiter):
        """创建测试命令处理器"""
        return self.TestCommandHandler(db, mock_config, mock_rate_limiter)

    @pytest.mark.asyncio
    async def test_handle_success(
        self, 
        test_handler,
        mock_update,
        mock_context
    ):
        """测试成功处理命令"""
        with patch.object(test_handler, 'log_success', new_callable=AsyncMock) as mock_log:
            result = await test_handler.handle(mock_update, mock_context)
            
            assert test_handler.verify_permissions_called
            assert test_handler.execute_command_called
            assert result == test_handler.execute_result
            mock_log.assert_called_once()

    @pytest.mark.asyncio
    async def test_handle_rate_limit_error(
        self, 
        test_handler,
        mock_update,
        mock_context,
        mock_rate_limiter
    ):
        """测试频率限制错误处理"""
        # 模拟频率限制异常
        mock_rate_limiter.check_all_limits.side_effect = RateLimitError("频率限制")
        
        with patch.object(test_handler, 'log_error', new_callable=AsyncMock) as mock_log:
            await test_handler.handle(mock_update, mock_context)
            
            # 验证发送了错误响应
            mock_update.message.reply_text.assert_called_once()
            call_args = mock_update.message.reply_text.call_args
            assert "频率限制" in call_args[0][0]
            
            # 验证记录了错误日志
            mock_log.assert_called_once()

    @pytest.mark.asyncio
    async def test_handle_permission_error(
        self, 
        test_handler,
        mock_update,
        mock_context
    ):
        """测试权限错误处理"""
        # 模拟权限异常
        async def mock_verify_permissions(update, context):
            raise PermissionError("权限不足")
        
        test_handler.verify_permissions = mock_verify_permissions
        
        with patch.object(test_handler, 'log_permission_denied', new_callable=AsyncMock) as mock_log:
            await test_handler.handle(mock_update, mock_context)
            
            # 验证发送了错误响应
            mock_update.message.reply_text.assert_called_once()
            call_args = mock_update.message.reply_text.call_args
            assert "权限不足" in call_args[0][0]
            
            # 验证记录了权限拒绝日志
            mock_log.assert_called_once()

    def test_get_user_display_name(self, test_handler, mock_update):
        """测试获取用户显示名称"""
        # 有名字和姓氏
        display_name = test_handler.get_user_display_name(mock_update)
        assert display_name == "Test User"
        
        # 只有名字
        mock_update.effective_user.last_name = None
        display_name = test_handler.get_user_display_name(mock_update)
        assert display_name == "Test"
        
        # 只有用户名
        mock_update.effective_user.first_name = None
        display_name = test_handler.get_user_display_name(mock_update)
        assert display_name == "@testuser"
        
        # 什么都没有
        mock_update.effective_user.username = None
        display_name = test_handler.get_user_display_name(mock_update)
        assert display_name == "User123456789"

    def test_format_amount(self, test_handler):
        """测试金额格式化"""
        assert test_handler.format_amount(10000) == "¥100.00"
        assert test_handler.format_amount(123456) == "¥1,234.56"
        assert test_handler.format_amount(0) == "¥0.00"

    def test_format_percentage(self, test_handler):
        """测试百分比格式化"""
        assert test_handler.format_percentage(85.5) == "85.5%"
        assert test_handler.format_percentage(100.0) == "100.0%"
        assert test_handler.format_percentage(0.0) == "0.0%"

    def test_mask_sensitive_data(self, test_handler):
        """测试敏感数据脱敏"""
        data = {
            "token": "abc123def456",
            "card_number": "1234567890123456",
            "normal_field": "normal_value",
            "nested": {
                "password": "secret123",
                "public": "public_value"
            }
        }
        
        masked = test_handler._mask_sensitive_data(data)
        
        assert masked["token"] == "ab**********56"
        assert masked["card_number"] == "12************56"
        assert masked["normal_field"] == "normal_value"
        assert masked["nested"]["password"] == "se*******23"
        assert masked["nested"]["public"] == "public_value"
