import { http } from '@/api/request'
import { API_URLS, replaceUrlParams } from './config'

const { NOTIFICATION } = API_URLS

/**
 * 通知相关API
 */
export const notificationApi = {
    // 获取通知列表
    getList(params) {
        return http.get(NOTIFICATION.LIST, { params }).then(res => res.data)
    },

    // 获取通知详情
    getDetail(id) {
        const url = replaceUrlParams(NOTIFICATION.DETAIL, { id })
        return http.get(url).then(res => res.data)
    },

    // 创建通知
    create(data) {
        return http.post(NOTIFICATION.CREATE, data).then(res => res.data)
    },

    // 更新通知
    update(id, data) {
        const url = replaceUrlParams(NOTIFICATION.UPDATE, { id })
        return http.put(url, data).then(res => res.data)
    },

    // 删除通知
    delete(id) {
        const url = replaceUrlParams(NOTIFICATION.DELETE, { id })
        return http.delete(url).then(res => res.data)
    },

    // 标记通知为已读
    markAsRead(id) {
        const url = replaceUrlParams(NOTIFICATION.READ, { id })
        return http.post(url).then(res => res.data)
    },

    // 标记通知为未读
    markAsUnread(id) {
        const url = replaceUrlParams(NOTIFICATION.UNREAD, { id })
        return http.post(url).then(res => res.data)
    },

    // 标记所有通知为已读
    markAllAsRead() {
        return http.put(NOTIFICATION.READ_ALL).then(res => res.data)
    },

    // 获取未读通知数量
    getUnreadCount() {
        return http.get(NOTIFICATION.UNREAD_COUNT).then(res => res.data)
    }
}

export default notificationApi
