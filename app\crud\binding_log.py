from typing import List, Optional, Dict, Any
from sqlalchemy.orm import Session
from sqlalchemy import desc, and_, func
from datetime import datetime, date

from app.crud.base import CRUDBase
from app.models.binding_log import BindingLog, LogType, LogLevel
from app.schemas.binding_log import BindingLogCreate, BindingLogUpdate


class CRUDBindingLog(CRUDBase[BindingLog, BindingLogCreate, BindingLogUpdate]):
    """绑卡日志CRUD操作 - 支持CK使用分析和问题排查"""

    def get_by_card_record_id(
        self, db: Session, card_record_id: str, skip: int = 0, limit: int = 100
    ) -> List[BindingLog]:
        """根据卡记录ID获取日志列表"""
        return (
            db.query(self.model)
            .filter(self.model.card_record_id == card_record_id)
            .order_by(self.model.timestamp.desc())  # 按时间倒序排序，最新的在前面
            .offset(skip)
            .limit(limit)
            .all()
        )

    def count_by_card_record_id(self, db: Session, card_record_id: str) -> int:
        """根据卡记录ID获取日志数量"""
        return (
            db.query(self.model)
            .filter(self.model.card_record_id == card_record_id)
            .count()
        )

    def create_log(
        self,
        db: Session,
        *,
        card_record_id: str,
        log_type: LogType,
        log_level: LogLevel,
        message: str,
        details: Optional[Dict[str, Any]] = None,
        request_data: Optional[Dict[str, Any]] = None,
        response_data: Optional[Dict[str, Any]] = None,
        duration_ms: Optional[float] = None,
        attempt_number: Optional[str] = None,
        walmart_ck_id: Optional[int] = None,
        ip_address: Optional[str] = None,
        # 【安全修复】添加商户和部门字段
        merchant_id: Optional[int] = None,
        department_id: Optional[int] = None
    ) -> BindingLog:
        """创建日志记录"""
        log_data = {
            "card_record_id": card_record_id,
            "log_type": log_type,
            "log_level": log_level,
            "message": message,
            "details": details,
            "request_data": request_data,
            "response_data": response_data,
            "duration_ms": duration_ms,
            "attempt_number": attempt_number,
            "walmart_ck_id": walmart_ck_id,
            "ip_address": ip_address,
            # 【安全修复】添加商户和部门字段
            "merchant_id": merchant_id,
            "department_id": department_id,
        }
        db_obj = BindingLog(**log_data)
        db.add(db_obj)

        # 检查是否为异步会话
        from sqlalchemy.ext.asyncio import AsyncSession
        if isinstance(db, AsyncSession):
            # 异步会话 - 不在这里提交，由调用方控制事务
            pass
        else:
            # 同步会话
            db.commit()
            db.refresh(db_obj)

        return db_obj

    def get_by_walmart_ck_id(
        self,
        db: Session,
        walmart_ck_id: int,
        *,
        skip: int = 0,
        limit: int = 100,
        log_type: Optional[str] = None,
        log_level: Optional[str] = None,
    ) -> List[BindingLog]:
        """获取某个CK的日志列表（用于CK使用分析）"""
        query = db.query(self.model).filter(self.model.walmart_ck_id == walmart_ck_id)

        if log_type:
            query = query.filter(self.model.log_type == log_type)
        if log_level:
            query = query.filter(self.model.log_level == log_level)

        return query.order_by(desc(self.model.timestamp)).offset(skip).limit(limit).all()

    def get_error_logs(
        self,
        db: Session,
        *,
        skip: int = 0,
        limit: int = 100,
        walmart_ck_id: Optional[int] = None,
        hours: int = 24,
    ) -> List[BindingLog]:
        """获取错误日志（用于问题排查）"""
        from datetime import timedelta

        start_time = datetime.now() - timedelta(hours=hours)

        query = db.query(self.model).filter(
            self.model.log_level == LogLevel.ERROR,
            self.model.timestamp >= start_time
        )

        if walmart_ck_id:
            query = query.filter(self.model.walmart_ck_id == walmart_ck_id)

        return query.order_by(desc(self.model.timestamp)).offset(skip).limit(limit).all()

    def get_performance_stats(
        self,
        db: Session,
        walmart_ck_id: Optional[int] = None,
        start_date: Optional[date] = None,
        end_date: Optional[date] = None,
    ) -> Dict[str, Any]:
        """获取性能统计信息"""
        query = db.query(self.model).filter(
            self.model.log_type == LogType.API_REQUEST,
            self.model.duration_ms.isnot(None)
        )

        if walmart_ck_id:
            query = query.filter(self.model.walmart_ck_id == walmart_ck_id)

        if start_date:
            query = query.filter(func.date(self.model.timestamp) >= start_date)
        if end_date:
            query = query.filter(func.date(self.model.timestamp) <= end_date)

        # 计算性能统计
        stats = query.with_entities(
            func.count(self.model.id).label('total_requests'),
            func.avg(self.model.duration_ms).label('avg_duration'),
            func.min(self.model.duration_ms).label('min_duration'),
            func.max(self.model.duration_ms).label('max_duration'),
        ).first()

        return {
            "walmart_ck_id": walmart_ck_id,
            "total_requests": stats.total_requests or 0,
            "avg_duration_ms": round(stats.avg_duration or 0, 2),
            "min_duration_ms": stats.min_duration or 0,
            "max_duration_ms": stats.max_duration or 0,
            "start_date": start_date,
            "end_date": end_date,
        }


binding_log = CRUDBindingLog(BindingLog)
