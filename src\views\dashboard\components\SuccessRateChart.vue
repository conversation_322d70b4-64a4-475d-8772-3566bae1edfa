<template>
  <el-card class="success-rate-chart-card" shadow="hover">
    <template #header>
      <div class="card-header">
        <span>绑卡成功率分析</span>
        <el-radio-group v-model="timeRange" size="small" @change="fetchData">
          <el-radio-button value="today">今日</el-radio-button>
          <el-radio-button value="week">本周</el-radio-button>
          <el-radio-button value="month">本月</el-radio-button>
        </el-radio-group>
      </div>
    </template>

    <div v-loading="loading" class="chart-content">
      <!-- 总体成功率显示 -->
      <div class="overall-rate">
        <el-statistic title="总体成功率" :value="data.overall_success_rate">
          <template #suffix>%</template>
        </el-statistic>
        <el-progress 
          :percentage="data.overall_success_rate" 
          :color="getSuccessRateColor(data.overall_success_rate)"
          :stroke-width="8"
          style="margin-top: 8px;"
        />
      </div>

      <!-- 小时分布图表 -->
      <div class="hourly-chart" v-if="data.hourly_breakdown && data.hourly_breakdown.length > 0">
        <h4>各时段成功率分布</h4>
        <div class="chart-container">
          <div 
            v-for="item in data.hourly_breakdown" 
            :key="item.hour"
            class="hour-item"
          >
            <div class="hour-label">{{ item.hour }}:00</div>
            <div class="hour-bar">
              <div 
                class="bar-fill" 
                :style="{ 
                  width: item.rate + '%', 
                  backgroundColor: getSuccessRateColor(item.rate) 
                }"
              ></div>
            </div>
            <div class="hour-rate">{{ item.rate }}%</div>
            <div class="hour-count">{{ item.success }}/{{ item.total }}</div>
          </div>
        </div>
      </div>

      <!-- 无数据提示 -->
      <div v-else class="no-data">
        <el-empty description="暂无数据" />
      </div>
    </div>
  </el-card>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { dashboardApi } from '@/api/modules/dashboard'
import { useUserStore } from '@/store/modules/user'
import { usePermissionStore } from '@/store/modules/permission'
import { useMerchantStore } from '@/store/modules/merchant'

const userStore = useUserStore()
const permissionStore = usePermissionStore()
const merchantStore = useMerchantStore()

const loading = ref(false)
const timeRange = ref('today')

const data = reactive({
  overall_success_rate: 0,
  total_requests: 0,
  total_success: 0,
  hourly_breakdown: []
})

const getSuccessRateColor = (rate) => {
  if (rate >= 90) return '#67C23A'
  if (rate >= 70) return '#E6A23C'
  return '#F56C6C'
}

const fetchData = async () => {
  loading.value = true
  try {
    const params = {
      time_range: timeRange.value
    }
    
    // 根据用户角色确定查询参数
    if (permissionStore.isSuperAdmin) {
      if (merchantStore.currentMerchantId) {
        params.merchant_id = merchantStore.currentMerchantId
      }
    } else {
      params.merchant_id = userStore.merchantId
    }

    const response = await dashboardApi.getSuccessRateStatistics(params)
    Object.assign(data, response.data)
  } catch (error) {
    console.error('获取成功率统计失败:', error)
  } finally {
    loading.value = false
  }
}

onMounted(() => {
  fetchData()
})

// 暴露刷新方法给父组件
defineExpose({
  refresh: fetchData
})
</script>

<style scoped>
.success-rate-chart-card {
  margin-bottom: 16px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 16px;
  font-weight: 600;
}

.chart-content {
  min-height: 300px;
}

.overall-rate {
  text-align: center;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
  margin-bottom: 20px;
}

.hourly-chart h4 {
  margin-bottom: 16px;
  color: #303133;
  font-size: 14px;
}

.chart-container {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 12px;
}

.hour-item {
  display: flex;
  align-items: center;
  padding: 8px;
  background: #fafafa;
  border-radius: 4px;
  font-size: 12px;
}

.hour-label {
  width: 40px;
  font-weight: 500;
  color: #606266;
}

.hour-bar {
  flex: 1;
  height: 8px;
  background: #e4e7ed;
  border-radius: 4px;
  margin: 0 8px;
  position: relative;
}

.bar-fill {
  height: 100%;
  border-radius: 4px;
  transition: width 0.3s ease;
}

.hour-rate {
  width: 40px;
  text-align: right;
  font-weight: 500;
  color: #303133;
}

.hour-count {
  width: 50px;
  text-align: right;
  color: #909399;
  font-size: 11px;
}

.no-data {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
}

@media (max-width: 768px) {
  .chart-container {
    grid-template-columns: 1fr;
  }
  
  .hour-item {
    font-size: 11px;
  }
}
</style>
