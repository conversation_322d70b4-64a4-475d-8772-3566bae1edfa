package services

import (
	"context"
	"fmt"

	"walmart-bind-card-processor/internal/config"
	"walmart-bind-card-processor/internal/database"
	"walmart-bind-card-processor/internal/logger"
	"walmart-bind-card-processor/internal/queue"

	"github.com/go-redis/redis/v8"
	"github.com/sirupsen/logrus"
	"go.uber.org/zap"
	"gorm.io/gorm"
)

// Container 服务容器，包含所有依赖的服务
type Container struct {
	// 基础组件
	Config       *config.Config
	DB           *gorm.DB
	Redis        *redis.Client
	QueueManager *queue.Manager
	Logger       *logrus.Logger
	ZapLogger    *zap.Logger

	// 业务服务
	CKManager            *CKManagerService
	AmountValidator      *AmountValidatorService
	PerformanceMonitor   *PerformanceMonitor
	CKWeightManager      *CKWeightManager
	RetryStrategy        *RetryStrategyService
	BindCardProcessor    *BindCardProcessor
	ServiceManager       *ServiceManager
}

// NewContainer 创建新的服务容器
func NewContainer(cfg *config.Config) (*Container, error) {
	// 创建logrus日志器
	logrusLogger := logger.New(cfg.Logging)

	// 创建zap日志器
	zapLogger, err := zap.NewProduction()
	if err != nil {
		return nil, fmt.Errorf("failed to create zap logger: %w", err)
	}

	// 初始化数据库连接
	db, err := initDatabase(cfg)
	if err != nil {
		return nil, err
	}

	// 初始化Redis连接
	redisClient, err := initRedis(cfg)
	if err != nil {
		return nil, err
	}

	// 初始化RabbitMQ队列管理器
	queueManager, err := queue.NewManager(cfg.RabbitMQ, logrusLogger)
	if err != nil {
		return nil, err
	}

	// 创建服务容器
	container := &Container{
		Config:       cfg,
		DB:           db,
		Redis:        redisClient,
		QueueManager: queueManager,
		Logger:       logrusLogger,
		ZapLogger:    zapLogger,
	}

	// 初始化业务服务
	if err := container.initServices(); err != nil {
		return nil, err
	}

	return container, nil
}

// initServices 初始化业务服务
func (c *Container) initServices() error {
	// 初始化CK管理服务（如果DB可用）
	if c.DB != nil {
		c.CKManager = NewCKManagerService(c.DB, c.Logger)
	}

	// 初始化金额验证服务（如果DB和Redis可用）
	if c.DB != nil && c.Redis != nil {
		c.AmountValidator = NewAmountValidatorService(c.DB, c.Redis, c.Logger, c.Config)
	}

	// 注意：API客户端不在这里初始化，因为需要从CK记录动态获取参数
	// 将在实际使用时根据选中的CK动态创建

	// 初始化其他服务（现在日志器类型已经修复）
	if c.DB != nil && c.Redis != nil && c.ZapLogger != nil {
		// 初始化性能监控
		c.PerformanceMonitor = NewPerformanceMonitor(c.Redis, c.ZapLogger)

		// 初始化CK权重管理器
		serviceFactory := NewServiceFactory(c.Config, c.DB, c.Redis, c.ZapLogger)
		c.CKWeightManager = serviceFactory.CreateCKWeightManager()

		// 初始化重试策略服务
		c.RetryStrategy = NewRetryStrategyService(c.Config, c.Logger, c.CKManager)

		// 初始化绑卡处理器
		var err error
		c.BindCardProcessor, err = NewBindCardProcessor(c.DB, c.Redis, c.ZapLogger, c.Config, c.QueueManager)
		if err != nil {
			return fmt.Errorf("failed to initialize BindCardProcessor: %w", err)
		}

		// 初始化服务管理器（包含连接监控和系统恢复服务）
		c.ServiceManager = NewServiceManager(c.DB, c.Redis, c.ZapLogger, c.Config)
	}

	return nil
}

// Close 关闭所有连接
func (c *Container) Close() error {
	var lastErr error

	// 关闭队列管理器
	if c.QueueManager != nil {
		if err := c.QueueManager.Close(); err != nil {
			c.Logger.Errorf("Failed to close queue manager: %v", err)
			lastErr = err
		}
	}

	// 关闭Redis连接
	if c.Redis != nil {
		if err := c.Redis.Close(); err != nil {
			c.Logger.Errorf("Failed to close Redis: %v", err)
			lastErr = err
		}
	}

	// 关闭数据库连接
	if c.DB != nil {
		if sqlDB, err := c.DB.DB(); err == nil {
			if err := sqlDB.Close(); err != nil {
				c.Logger.Errorf("Failed to close database: %v", err)
				lastErr = err
			}
		}
	}

	return lastErr
}

// initDatabase 初始化数据库连接
func initDatabase(cfg *config.Config) (*gorm.DB, error) {
	// 导入数据库包
	db, err := database.New(cfg.Database)
	if err != nil {
		return nil, fmt.Errorf("failed to initialize database: %w", err)
	}
	return db, nil
}

// initRedis 初始化Redis连接
func initRedis(cfg *config.Config) (*redis.Client, error) {
	rdb := redis.NewClient(&redis.Options{
		Addr:     fmt.Sprintf("%s:%d", cfg.Redis.Host, cfg.Redis.Port),
		Password: cfg.Redis.Password,
		DB:       cfg.Redis.DB,
	})

	// 测试连接
	ctx := context.Background()
	_, err := rdb.Ping(ctx).Result()
	if err != nil {
		return nil, fmt.Errorf("failed to connect to Redis: %w", err)
	}

	return rdb, nil
}
