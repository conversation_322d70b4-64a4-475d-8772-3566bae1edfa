from typing import Optional, List, Dict, Any
from pydantic import BaseModel, Field, EmailStr, field_validator
from datetime import datetime


# 共享属性
class MerchantBase(BaseModel):
    name: str = Field(..., description="商家名称", min_length=2, max_length=100)
    code: Optional[str] = Field(
        None,
        description="商家编码，不提供则自动生成",
        min_length=2,
        max_length=50,
        pattern=r"^[a-zA-Z0-9_-]+$",
    )

    @field_validator("code", mode="before")
    def empty_str_to_none(cls, v):
        if v == "":
            return None
        return v

    status: int = Field(1, description="状态：1启用，0禁用")
    callback_url: Optional[str] = Field(None, description="回调URL")
    allowed_ips: Optional[str] = Field(None, description="IP白名单，逗号分隔")
    daily_limit: int = Field(10000, description="每日绑卡上限")
    hourly_limit: int = Field(1000, description="每小时绑卡上限")
    concurrency_limit: int = Field(100, description="最大并发数")
    priority: int = Field(0, description="处理优先级，数字越大优先级越高")
    request_timeout: int = Field(30, description="请求超时时间(秒)")
    retry_count: int = Field(3, description="重试次数")
    contact_name: Optional[str] = Field(None, description="联系人")
    contact_phone: Optional[str] = Field(None, description="联系电话")
    contact_email: Optional[EmailStr] = Field(None, description="联系邮箱")
    created_by: Optional[int] = Field(None, description="创建者ID")

    @field_validator("contact_email", mode="before")
    def empty_email_to_none(cls, v):
        if v == "":
            return None
        return v

    remark: Optional[str] = Field(None, description="备注")
    custom_config: Optional[Dict[str, Any]] = Field(
        None, description="自定义配置，JSON格式"
    )

    class Config:
        from_attributes = True


# 查询商家列表返回的模型
class MerchantListResponse(MerchantBase):
    id: int = Field(..., description="ID")
    api_key_updated_at: Optional[datetime] = Field(None, description="API密钥更新时间")
    api_secret_updated_at: Optional[datetime] = Field(
        None, description="API密文更新时间"
    )
    created_at: datetime = Field(..., description="创建时间")
    updated_at: Optional[datetime] = Field(None, description="更新时间")


# 分页查询
class MerchantList(BaseModel):
    total: int = Field(..., description="总记录数")
    items: List[MerchantListResponse] = Field(..., description="商家列表")


# 创建商家时的模型
class MerchantCreate(MerchantBase):
    pass


# 更新商家时的模型
class MerchantUpdate(BaseModel):
    name: Optional[str] = Field(
        None, description="商家名称", min_length=2, max_length=100
    )
    code: Optional[str] = Field(
        None,
        description="商家编码",
        min_length=2,
        max_length=50,
        pattern=r"^[a-zA-Z0-9_-]+$",
    )
    status: Optional[int] = Field(None, description="状态：1启用，0禁用")
    callback_url: Optional[str] = Field(None, description="回调URL")
    allowed_ips: Optional[str] = Field(None, description="IP白名单，逗号分隔")
    daily_limit: Optional[int] = Field(None, description="每日绑卡上限")
    hourly_limit: Optional[int] = Field(None, description="每小时绑卡上限")
    concurrency_limit: Optional[int] = Field(None, description="最大并发数")
    priority: Optional[int] = Field(None, description="处理优先级，数字越大优先级越高")
    request_timeout: Optional[int] = Field(None, description="请求超时时间(秒)")
    retry_count: Optional[int] = Field(None, description="重试次数")
    contact_name: Optional[str] = Field(None, description="联系人")
    contact_phone: Optional[str] = Field(None, description="联系电话")
    contact_email: Optional[EmailStr] = Field(None, description="联系邮箱")
    remark: Optional[str] = Field(None, description="备注")
    custom_config: Optional[Dict[str, Any]] = Field(
        None, description="自定义配置，JSON格式"
    )

    class Config:
        from_attributes = True


# 更新商家状态
class MerchantStatusUpdate(BaseModel):
    status: int = Field(..., description="状态：1启用，0禁用")


# 数据库中的模型
class MerchantInDBBase(MerchantBase):
    id: int
    api_key: str
    created_at: datetime
    updated_at: Optional[datetime]

    class Config:
        from_attributes = True


# 数据库中完整的商家模型
class MerchantInDB(MerchantInDBBase):
    api_secret: str


# API响应模型
class Merchant(MerchantInDBBase):
    pass


# 不含敏感信息的模型（用于列表展示）
class MerchantPublic(MerchantInDBBase):
    pass


# 商家统计数据
class MerchantStatistics(BaseModel):
    total_records: int = Field(0, description="总绑卡记录数")
    success_records: int = Field(0, description="成功绑卡记录数")
    failed_records: int = Field(0, description="失败绑卡记录数")
    pending_records: int = Field(0, description="待处理绑卡记录数")
    today_records: int = Field(0, description="今日绑卡记录数")
    yesterday_records: int = Field(0, description="昨日绑卡记录数")


# API密钥响应
class ApiKeyResponse(BaseModel):
    api_key: str
    api_secret: str


# API密文响应
class ApiSecretResponse(BaseModel):
    success: bool = Field(..., description="是否成功")
    message: Optional[str] = Field(None, description="错误信息")
    api_secret: Optional[str] = Field(None, description="API密文")


# 密码验证
class PasswordVerify(BaseModel):
    password: str = Field(..., description="密码")


# API使用统计响应
class ApiUsageResponse(BaseModel):
    total: int = Field(..., description="总调用次数")
    today: int = Field(..., description="今日调用次数")
    avg_response_time: float = Field(..., description="平均响应时间(ms)")


# IP白名单基础模型
class IpWhitelistBase(BaseModel):
    ip: str = Field(..., description="IP地址，支持CIDR格式")
    description: str = Field(..., description="用途描述")

    class Config:
        from_attributes = True


# 创建IP白名单时的模型
class IpWhitelistCreate(IpWhitelistBase):
    pass


# 更新IP白名单时的模型
class IpWhitelistUpdate(IpWhitelistBase):
    pass


# IP白名单响应模型
class IpWhitelist(IpWhitelistBase):
    id: int = Field(..., description="ID")
    merchant_id: int = Field(..., description="商家ID")
    created_at: datetime = Field(..., description="创建时间")
    updated_at: Optional[datetime] = Field(None, description="更新时间")


# IP白名单列表响应
class IpWhitelistResponse(BaseModel):
    ip_list: List[IpWhitelist] = Field([], description="IP白名单列表")


# 商家API凭据响应
class MerchantApiCredentials(BaseModel):
    api_key: str = Field(..., description="API密钥")
    api_secret: str = Field(..., description="API密钥密文")
    merchant_code: str = Field(..., description="商家编码")
    merchant_name: str = Field(..., description="商家名称")
    merchant_id: int = Field(..., description="商家ID")

    class Config:
        from_attributes = True
