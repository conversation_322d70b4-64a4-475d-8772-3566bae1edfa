#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
部门层级权限测试
测试多层级部门结构的权限继承、数据隔离和访问控制
"""

import sys
import os
import time
import random
import string

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.dirname(__file__))))

from test.conftest import TestBase, get_test_accounts, format_test_result, print_test_summary

class DepartmentHierarchyPermissionsTestSuite(TestBase):
    """部门层级权限测试套件"""
    
    def __init__(self):
        super().__init__()
        self.results = []
        self.test_accounts = get_test_accounts()
        self.admin_token = None
        self.merchant_token = None
        self.created_departments = []
        self.created_users = []
        self.department_hierarchy = {}  # 存储部门层级关系
    
    def setup(self):
        """测试前置设置"""
        print("=== 测试前置设置 ===")
        
        # 管理员登录
        self.admin_token = self.login("admin", "7c222fb2927d828af22f592134e8932480637c0d")
        if not self.admin_token:
            print("❌ 管理员登录失败")
            return False
        
        # 商户管理员登录
        self.merchant_token = self.login("test1", "********")
        if not self.merchant_token:
            print("❌ 商户管理员登录失败")
            return False
        
        print("✅ 测试前置设置完成")
        return True
    
    def get_test_merchant_id(self):
        """获取测试用的商户ID"""
        if not self.admin_token:
            return None

        # 获取商户列表
        status_code, response = self.make_request("GET", "/merchants", self.admin_token)
        if status_code == 200:
            merchants_data = response.get("data", {})
            merchants = merchants_data.get("items", []) if isinstance(merchants_data, dict) else response.get("items", [])
            if merchants:
                return merchants[0].get("id")
        return None

    def create_test_department_hierarchy(self):
        """创建测试用的部门层级结构"""
        print("\n=== 创建测试部门层级结构 ===")

        if not self.admin_token:
            return False

        # 获取测试用的商户ID
        merchant_id = self.get_test_merchant_id()
        if not merchant_id:
            print("❌ 无法获取测试商户ID")
            return False

        # 创建根部门
        root_dept_data = {
            "name": f"测试根部门_{int(time.time())}",
            "code": f"ROOT_{int(time.time())}",
            "description": "测试根部门",
            "status": True,  # 使用布尔值
            "merchant_id": merchant_id
        }
        
        status_code, response = self.make_request(
            "POST", "/departments", self.admin_token, data=root_dept_data
        )
        
        if status_code not in [200, 201]:
            print(f"❌ 创建根部门失败，状态码: {status_code}")
            return False
        
        root_dept_id = response.get("data", {}).get("id") or response.get("id")
        if not root_dept_id:
            print("❌ 创建根部门成功但未返回ID")
            return False
        
        self.created_departments.append(root_dept_id)
        self.department_hierarchy["root"] = root_dept_id
        print(f"✅ 创建根部门成功: {root_dept_id}")
        
        # 创建子部门A
        child_a_data = {
            "name": f"测试子部门A_{int(time.time())}",
            "code": f"CHILD_A_{int(time.time())}",
            "description": "测试子部门A",
            "status": True,
            "parent_id": root_dept_id,
            "merchant_id": merchant_id
        }
        
        status_code, response = self.make_request(
            "POST", "/departments", self.admin_token, data=child_a_data
        )
        
        if status_code in [200, 201]:
            child_a_id = response.get("data", {}).get("id") or response.get("id")
            if child_a_id:
                self.created_departments.append(child_a_id)
                self.department_hierarchy["child_a"] = child_a_id
                print(f"✅ 创建子部门A成功: {child_a_id}")
        
        # 创建子部门B
        child_b_data = {
            "name": f"测试子部门B_{int(time.time())}",
            "code": f"CHILD_B_{int(time.time())}",
            "description": "测试子部门B",
            "status": True,
            "parent_id": root_dept_id,
            "merchant_id": merchant_id
        }
        
        status_code, response = self.make_request(
            "POST", "/departments", self.admin_token, data=child_b_data
        )
        
        if status_code in [200, 201]:
            child_b_id = response.get("data", {}).get("id") or response.get("id")
            if child_b_id:
                self.created_departments.append(child_b_id)
                self.department_hierarchy["child_b"] = child_b_id
                print(f"✅ 创建子部门B成功: {child_b_id}")
        
        # 创建孙部门（子部门A的子部门）
        if "child_a" in self.department_hierarchy:
            grandchild_data = {
                "name": f"测试孙部门_{int(time.time())}",
                "code": f"GRANDCHILD_{int(time.time())}",
                "description": "测试孙部门",
                "status": True,
                "parent_id": self.department_hierarchy["child_a"],
                "merchant_id": merchant_id
            }
            
            status_code, response = self.make_request(
                "POST", "/departments", self.admin_token, data=grandchild_data
            )
            
            if status_code in [200, 201]:
                grandchild_id = response.get("data", {}).get("id") or response.get("id")
                if grandchild_id:
                    self.created_departments.append(grandchild_id)
                    self.department_hierarchy["grandchild"] = grandchild_id
                    print(f"✅ 创建孙部门成功: {grandchild_id}")
        
        return len(self.department_hierarchy) >= 2
    
    def create_test_users_in_departments(self):
        """在不同部门创建测试用户"""
        print("\n=== 在不同部门创建测试用户 ===")
        
        if not self.admin_token or not self.department_hierarchy:
            return False
        
        # 为每个部门创建一个测试用户
        for dept_name, dept_id in self.department_hierarchy.items():
            user_data = {
                "username": f"test_user_{dept_name}_{int(time.time())}",
                "password": "Test123456!",
                "full_name": f"测试用户_{dept_name}",
                "email": f"test_{dept_name}_{int(time.time())}@example.com",
                "department_id": dept_id,
                "is_active": True
            }
            
            status_code, response = self.make_request(
                "POST", "/users", self.admin_token, data=user_data
            )
            
            if status_code in [200, 201]:
                user_id = response.get("data", {}).get("id") or response.get("id")
                if user_id:
                    self.created_users.append(user_id)
                    print(f"✅ 在部门{dept_name}创建用户成功: {user_id}")
            else:
                print(f"⚠️ 在部门{dept_name}创建用户失败，状态码: {status_code}")
        
        return len(self.created_users) > 0
    
    def test_parent_child_department_access(self):
        """测试父子部门访问权限"""
        print("\n=== 测试父子部门访问权限 ===")
        
        if not self.department_hierarchy:
            self.results.append(format_test_result(
                "父子部门访问测试前置条件",
                False,
                "缺少部门层级结构"
            ))
            return
        
        # 测试父部门用户是否能访问子部门数据
        root_dept_id = self.department_hierarchy.get("root")
        child_a_id = self.department_hierarchy.get("child_a")
        
        if root_dept_id and child_a_id:
            # 尝试修改子部门信息（模拟父部门用户操作）
            update_data = {
                "description": f"父部门修改子部门_{int(time.time())}"
            }
            
            status_code, response = self.make_request(
                "PUT", f"/departments/{child_a_id}", self.merchant_token, data=update_data
            )
            
            if status_code == 200:
                self.results.append(format_test_result(
                    "父部门访问子部门权限",
                    True,
                    "父部门用户可以修改子部门（符合层级权限）"
                ))
                print("✅ 父部门用户可以修改子部门（符合层级权限）")
            elif status_code in [403, 401]:
                self.results.append(format_test_result(
                    "父部门访问子部门权限",
                    False,
                    "父部门用户无法访问子部门（可能权限过严）"
                ))
                print("⚠️ 父部门用户无法访问子部门（可能权限过严）")
            else:
                self.results.append(format_test_result(
                    "父部门访问子部门测试",
                    True,
                    f"父部门访问子部门失败，状态码: {status_code}"
                ))
                print(f"✅ 父部门访问子部门失败，状态码: {status_code}")
    
    def test_sibling_department_isolation(self):
        """测试同级部门数据隔离"""
        print("\n=== 测试同级部门数据隔离 ===")
        
        child_a_id = self.department_hierarchy.get("child_a")
        child_b_id = self.department_hierarchy.get("child_b")
        
        if not (child_a_id and child_b_id):
            self.results.append(format_test_result(
                "同级部门隔离测试前置条件",
                False,
                "缺少同级部门"
            ))
            return
        
        # 模拟部门A用户尝试访问部门B的数据
        # 这里我们测试部门A用户是否能修改部门B的信息
        update_data = {
            "description": f"跨部门恶意修改_{int(time.time())}"
        }
        
        status_code, response = self.make_request(
            "PUT", f"/departments/{child_b_id}", self.merchant_token, data=update_data
        )
        
        if status_code in [403, 401]:
            self.results.append(format_test_result(
                "同级部门数据隔离",
                True,
                "正确阻止同级部门间的数据访问"
            ))
            print("✅ 正确阻止同级部门间的数据访问")
        elif status_code == 200:
            self.results.append(format_test_result(
                "同级部门数据隔离",
                False,
                "检测到同级部门数据泄露漏洞"
            ))
            print("🚨 检测到同级部门数据泄露漏洞")
        else:
            self.results.append(format_test_result(
                "同级部门数据隔离测试",
                True,
                f"同级部门访问被拒绝，状态码: {status_code}"
            ))
            print(f"✅ 同级部门访问被拒绝，状态码: {status_code}")
    
    def test_department_user_data_isolation(self):
        """测试部门用户数据隔离"""
        print("\n=== 测试部门用户数据隔离 ===")
        
        if not self.created_users:
            self.results.append(format_test_result(
                "部门用户数据隔离测试前置条件",
                False,
                "缺少测试用户"
            ))
            return
        
        # 测试用户是否只能看到自己部门的用户
        status_code, response = self.make_request("GET", "/users", self.merchant_token)
        
        if status_code == 200:
            users_data = response.get("data", {})
            users = users_data.get("items", []) if isinstance(users_data, dict) else response.get("items", [])
            
            # 检查返回的用户是否都属于正确的部门
            department_users = {}
            for user in users:
                dept_id = user.get("department_id")
                if dept_id:
                    if dept_id not in department_users:
                        department_users[dept_id] = []
                    department_users[dept_id].append(user.get("id"))
            
            if len(department_users) <= 1:
                self.results.append(format_test_result(
                    "部门用户数据隔离",
                    True,
                    f"用户只能看到单一部门的用户数据"
                ))
                print("✅ 用户只能看到单一部门的用户数据")
            else:
                self.results.append(format_test_result(
                    "部门用户数据隔离",
                    False,
                    f"用户可以看到多个部门的用户数据，可能存在数据泄露"
                ))
                print("⚠️ 用户可以看到多个部门的用户数据，可能存在数据泄露")
        elif status_code in [403, 401]:
            self.results.append(format_test_result(
                "部门用户数据访问权限",
                True,
                "正确限制用户访问用户列表"
            ))
            print("✅ 正确限制用户访问用户列表")
        else:
            self.results.append(format_test_result(
                "部门用户数据隔离测试",
                False,
                f"获取用户列表失败，状态码: {status_code}"
            ))
            print(f"❌ 获取用户列表失败，状态码: {status_code}")
    
    def test_department_hierarchy_deletion_cascade(self):
        """测试部门层级删除级联效应"""
        print("\n=== 测试部门层级删除级联效应 ===")
        
        if not self.department_hierarchy:
            return
        
        # 测试删除父部门是否会影响子部门
        root_dept_id = self.department_hierarchy.get("root")
        if not root_dept_id:
            return
        
        # 尝试删除根部门（应该被阻止或级联删除子部门）
        status_code, response = self.make_request(
            "DELETE", f"/departments/{root_dept_id}", self.admin_token
        )
        
        if status_code in [200, 204]:
            # 检查子部门是否也被删除
            child_a_id = self.department_hierarchy.get("child_a")
            if child_a_id:
                status_code, response = self.make_request(
                    "GET", f"/departments/{child_a_id}", self.admin_token
                )
                
                if status_code == 404:
                    self.results.append(format_test_result(
                        "部门级联删除",
                        True,
                        "删除父部门正确级联删除了子部门"
                    ))
                    print("✅ 删除父部门正确级联删除了子部门")
                elif status_code == 200:
                    self.results.append(format_test_result(
                        "部门级联删除",
                        False,
                        "删除父部门后子部门仍然存在，可能导致孤儿数据"
                    ))
                    print("⚠️ 删除父部门后子部门仍然存在，可能导致孤儿数据")
        elif status_code in [403, 400]:
            self.results.append(format_test_result(
                "部门删除保护",
                True,
                "正确阻止删除有子部门的父部门"
            ))
            print("✅ 正确阻止删除有子部门的父部门")
        else:
            self.results.append(format_test_result(
                "部门删除测试",
                True,
                f"删除部门失败，状态码: {status_code}"
            ))
            print(f"✅ 删除部门失败，状态码: {status_code}")
    
    def cleanup(self):
        """清理测试数据"""
        print("\n=== 清理测试数据 ===")
        
        if not self.admin_token:
            return
        
        # 清理创建的用户
        for user_id in self.created_users:
            status_code, _ = self.make_request("DELETE", f"/users/{user_id}", self.admin_token)
            if status_code in [200, 204]:
                print(f"✅ 清理测试用户: {user_id}")
            else:
                print(f"⚠️ 清理测试用户失败: {user_id}")
        
        # 清理创建的部门（从子部门开始删除）
        for dept_id in reversed(self.created_departments):
            status_code, _ = self.make_request("DELETE", f"/departments/{dept_id}", self.admin_token)
            if status_code in [200, 204]:
                print(f"✅ 清理测试部门: {dept_id}")
            else:
                print(f"⚠️ 清理测试部门失败: {dept_id}")
    
    def run_all_tests(self):
        """运行所有部门层级权限测试"""
        print("🚀 开始部门层级权限测试")
        print("="*60)
        
        start_time = time.time()
        
        # 前置设置
        if not self.setup():
            print("❌ 测试前置设置失败，跳过测试")
            return []
        
        try:
            # 创建测试数据
            if not self.create_test_department_hierarchy():
                print("❌ 创建测试部门层级失败，跳过测试")
                return []
            
            if not self.create_test_users_in_departments():
                print("⚠️ 创建测试用户失败，部分测试可能受影响")
            
            # 运行所有测试
            self.test_parent_child_department_access()
            self.test_sibling_department_isolation()
            self.test_department_user_data_isolation()
            self.test_department_hierarchy_deletion_cascade()
            
        finally:
            # 清理测试数据
            self.cleanup()
        
        end_time = time.time()
        duration = end_time - start_time
        
        # 打印测试摘要
        print_test_summary(self.results)
        print(f"测试耗时: {duration:.2f}秒")
        
        return self.results

def main():
    """主函数"""
    test_suite = DepartmentHierarchyPermissionsTestSuite()
    results = test_suite.run_all_tests()
    
    # 判断测试是否通过
    failed_tests = [r for r in results if not r.get("success", False)]
    
    if not failed_tests:
        print("\n🎉 所有部门层级权限测试通过！")
        return 0
    else:
        print(f"\n⚠️ 发现 {len(failed_tests)} 个问题需要修复")
        for test in failed_tests:
            print(f"  - {test['test_name']}: {test['message']}")
        return 1

if __name__ == "__main__":
    sys.exit(main())
