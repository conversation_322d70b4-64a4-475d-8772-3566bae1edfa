"""
异常行为检测服务
实现多种异常模式检测，包括账户接管、数据窃取、权限探测等
"""

import json
import hashlib
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime, timedelta
from collections import defaultdict, Counter
from sqlalchemy.orm import Session
from sqlalchemy import func, and_, or_, desc

from app.models.audit import AuditLog, AuditEventType, AuditLevel
from app.models.user import User
from app.core.logging import get_logger

logger = get_logger("anomaly_detection")


class AnomalyDetectionService:
    """异常行为检测服务"""

    def __init__(self, db: Session):
        self.db = db
        self.detection_rules = {
            "account_takeover": self._detect_account_takeover,
            "data_exfiltration": self._detect_data_exfiltration,
            "privilege_escalation": self._detect_privilege_escalation,
            "brute_force": self._detect_brute_force,
            "suspicious_login": self._detect_suspicious_login,
            "mass_data_access": self._detect_mass_data_access,
            "unusual_time_access": self._detect_unusual_time_access,
            "rapid_permission_changes": self._detect_rapid_permission_changes
        }

    async def detect_anomalies(
        self, 
        user_id: Optional[int] = None,
        time_window_hours: int = 24,
        detection_types: Optional[List[str]] = None
    ) -> Dict[str, Any]:
        """检测异常行为"""
        end_time = datetime.utcnow()
        start_time = end_time - timedelta(hours=time_window_hours)
        
        # 构建基础查询
        query = self.db.query(AuditLog).filter(
            AuditLog.created_at.between(start_time, end_time)
        )
        
        if user_id:
            query = query.filter(AuditLog.user_id == user_id)
        
        audit_logs = query.all()
        
        # 选择检测类型
        if detection_types is None:
            detection_types = list(self.detection_rules.keys())
        
        anomalies = []
        detection_results = {}
        
        for detection_type in detection_types:
            if detection_type in self.detection_rules:
                try:
                    result = await self.detection_rules[detection_type](audit_logs, start_time, end_time)
                    detection_results[detection_type] = result
                    if result.get("anomalies"):
                        anomalies.extend(result["anomalies"])
                except Exception as e:
                    logger.error(f"Detection {detection_type} failed: {e}")
                    detection_results[detection_type] = {"error": str(e)}
        
        return {
            "time_window": {
                "start_time": start_time.isoformat(),
                "end_time": end_time.isoformat(),
                "hours": time_window_hours
            },
            "total_anomalies": len(anomalies),
            "anomalies": anomalies,
            "detection_results": detection_results,
            "risk_score": self._calculate_overall_risk_score(anomalies)
        }

    async def _detect_account_takeover(
        self, 
        audit_logs: List[AuditLog], 
        start_time: datetime, 
        end_time: datetime
    ) -> Dict[str, Any]:
        """检测账户接管行为"""
        anomalies = []
        user_activities = defaultdict(list)
        
        # 按用户分组活动
        for log in audit_logs:
            if log.user_id:
                user_activities[log.user_id].append(log)
        
        for user_id, activities in user_activities.items():
            # 检测IP地址突变
            ip_changes = self._detect_ip_changes(activities)
            if ip_changes:
                anomalies.append({
                    "type": "account_takeover",
                    "subtype": "ip_change",
                    "user_id": user_id,
                    "severity": "high",
                    "details": ip_changes,
                    "timestamp": max(activity.created_at for activity in activities).isoformat()
                })
            
            # 检测用户代理突变
            ua_changes = self._detect_user_agent_changes(activities)
            if ua_changes:
                anomalies.append({
                    "type": "account_takeover",
                    "subtype": "user_agent_change",
                    "user_id": user_id,
                    "severity": "medium",
                    "details": ua_changes,
                    "timestamp": max(activity.created_at for activity in activities).isoformat()
                })
            
            # 检测行为模式突变
            behavior_changes = self._detect_behavior_pattern_changes(activities)
            if behavior_changes:
                anomalies.append({
                    "type": "account_takeover",
                    "subtype": "behavior_change",
                    "user_id": user_id,
                    "severity": "medium",
                    "details": behavior_changes,
                    "timestamp": max(activity.created_at for activity in activities).isoformat()
                })
        
        return {
            "anomalies": anomalies,
            "total_users_analyzed": len(user_activities),
            "detection_summary": {
                "ip_changes": len([a for a in anomalies if a["subtype"] == "ip_change"]),
                "ua_changes": len([a for a in anomalies if a["subtype"] == "user_agent_change"]),
                "behavior_changes": len([a for a in anomalies if a["subtype"] == "behavior_change"])
            }
        }

    async def _detect_data_exfiltration(
        self, 
        audit_logs: List[AuditLog], 
        start_time: datetime, 
        end_time: datetime
    ) -> Dict[str, Any]:
        """检测数据窃取模式"""
        anomalies = []
        
        # 检测大量数据访问
        bulk_access_logs = [
            log for log in audit_logs 
            if log.action and "VIEW" in log.action.upper() and 
            log.details and log.details.get("record_count", 0) > 100
        ]
        
        user_bulk_access = defaultdict(list)
        for log in bulk_access_logs:
            user_bulk_access[log.user_id].append(log)
        
        for user_id, logs in user_bulk_access.items():
            total_records = sum(log.details.get("record_count", 0) for log in logs)
            if total_records > 1000:  # 超过1000条记录
                anomalies.append({
                    "type": "data_exfiltration",
                    "subtype": "bulk_data_access",
                    "user_id": user_id,
                    "severity": "high",
                    "details": {
                        "total_records_accessed": total_records,
                        "access_count": len(logs),
                        "resource_types": list(set(log.resource_type for log in logs if log.resource_type))
                    },
                    "timestamp": max(log.created_at for log in logs).isoformat()
                })
        
        # 检测异常下载模式
        download_logs = [
            log for log in audit_logs 
            if log.action and "DOWNLOAD" in log.action.upper()
        ]
        
        user_downloads = defaultdict(list)
        for log in download_logs:
            user_downloads[log.user_id].append(log)
        
        for user_id, logs in user_downloads.items():
            if len(logs) > 20:  # 超过20次下载
                anomalies.append({
                    "type": "data_exfiltration",
                    "subtype": "excessive_downloads",
                    "user_id": user_id,
                    "severity": "medium",
                    "details": {
                        "download_count": len(logs),
                        "time_span_minutes": (max(log.created_at for log in logs) - 
                                            min(log.created_at for log in logs)).total_seconds() / 60
                    },
                    "timestamp": max(log.created_at for log in logs).isoformat()
                })
        
        return {
            "anomalies": anomalies,
            "detection_summary": {
                "bulk_access_users": len([a for a in anomalies if a["subtype"] == "bulk_data_access"]),
                "excessive_download_users": len([a for a in anomalies if a["subtype"] == "excessive_downloads"])
            }
        }

    async def _detect_privilege_escalation(
        self, 
        audit_logs: List[AuditLog], 
        start_time: datetime, 
        end_time: datetime
    ) -> Dict[str, Any]:
        """检测权限提升行为"""
        anomalies = []
        
        # 检测权限相关操作
        privilege_logs = [
            log for log in audit_logs 
            if log.resource_type in ["ROLE", "PERMISSION", "USER"] and 
            log.action in ["CREATE", "UPDATE", "DELETE"]
        ]
        
        user_privilege_ops = defaultdict(list)
        for log in privilege_logs:
            user_privilege_ops[log.user_id].append(log)
        
        for user_id, logs in user_privilege_ops.items():
            # 检测短时间内大量权限操作
            if len(logs) > 10:  # 超过10次权限操作
                time_span = (max(log.created_at for log in logs) - 
                           min(log.created_at for log in logs)).total_seconds() / 3600
                
                if time_span < 1:  # 1小时内
                    anomalies.append({
                        "type": "privilege_escalation",
                        "subtype": "rapid_privilege_changes",
                        "user_id": user_id,
                        "severity": "high",
                        "details": {
                            "operation_count": len(logs),
                            "time_span_hours": time_span,
                            "operations": [
                                {
                                    "action": log.action,
                                    "resource_type": log.resource_type,
                                    "resource_id": log.resource_id
                                } for log in logs
                            ]
                        },
                        "timestamp": max(log.created_at for log in logs).isoformat()
                    })
        
        return {
            "anomalies": anomalies,
            "detection_summary": {
                "rapid_privilege_changes": len(anomalies)
            }
        }

    async def _detect_brute_force(
        self,
        audit_logs: List[AuditLog],
        start_time: datetime,
        end_time: datetime
    ) -> Dict[str, Any]:
        """检测暴力破解攻击"""
        anomalies = []

        # 检测登录失败模式
        failed_login_logs = [
            log for log in audit_logs
            if log.action == "LOGIN_FAILED" or
            (log.level in [AuditLevel.ERROR, AuditLevel.WARNING] and "login" in log.message.lower())
        ]

        # 按IP地址分组
        ip_failed_attempts = defaultdict(list)
        for log in failed_login_logs:
            ip = log.ip_address if hasattr(log, 'ip_address') else 'unknown'
            ip_failed_attempts[ip].append(log)

        for ip, logs in ip_failed_attempts.items():
            if len(logs) > 10:  # 超过10次失败尝试
                time_span = (max(log.created_at for log in logs) -
                           min(log.created_at for log in logs)).total_seconds() / 60

                if time_span < 30:  # 30分钟内
                    anomalies.append({
                        "type": "brute_force",
                        "subtype": "login_brute_force",
                        "ip_address": ip,
                        "severity": "high",
                        "details": {
                            "failed_attempts": len(logs),
                            "time_span_minutes": time_span,
                            "targeted_users": list(set(log.user_id for log in logs if log.user_id))
                        },
                        "timestamp": max(log.created_at for log in logs).isoformat()
                    })

        return {
            "anomalies": anomalies,
            "detection_summary": {
                "brute_force_ips": len(anomalies)
            }
        }

    async def _detect_suspicious_login(
        self,
        audit_logs: List[AuditLog],
        start_time: datetime,
        end_time: datetime
    ) -> Dict[str, Any]:
        """检测可疑登录行为"""
        anomalies = []

        # 检测登录成功日志
        login_logs = [
            log for log in audit_logs
            if log.action == "LOGIN_SUCCESS" or "login" in log.message.lower()
        ]

        user_logins = defaultdict(list)
        for log in login_logs:
            if log.user_id:
                user_logins[log.user_id].append(log)

        for user_id, logs in user_logins.items():
            # 检测异常时间登录
            for log in logs:
                hour = log.created_at.hour
                if hour < 6 or hour > 22:  # 夜间登录
                    anomalies.append({
                        "type": "suspicious_login",
                        "subtype": "off_hours_login",
                        "user_id": user_id,
                        "severity": "medium",
                        "details": {
                            "login_hour": hour,
                            "login_time": log.created_at.isoformat()
                        },
                        "timestamp": log.created_at.isoformat()
                    })

            # 检测多地点登录
            if len(logs) > 1:
                ip_addresses = [getattr(log, 'ip_address', 'unknown') for log in logs]
                unique_ips = set(ip_addresses)
                if len(unique_ips) > 3:  # 超过3个不同IP
                    anomalies.append({
                        "type": "suspicious_login",
                        "subtype": "multiple_locations",
                        "user_id": user_id,
                        "severity": "medium",
                        "details": {
                            "unique_ip_count": len(unique_ips),
                            "ip_addresses": list(unique_ips),
                            "login_count": len(logs)
                        },
                        "timestamp": max(log.created_at for log in logs).isoformat()
                    })

        return {
            "anomalies": anomalies,
            "detection_summary": {
                "off_hours_logins": len([a for a in anomalies if a["subtype"] == "off_hours_login"]),
                "multiple_location_users": len([a for a in anomalies if a["subtype"] == "multiple_locations"])
            }
        }

    async def _detect_mass_data_access(
        self,
        audit_logs: List[AuditLog],
        start_time: datetime,
        end_time: datetime
    ) -> Dict[str, Any]:
        """检测大规模数据访问"""
        anomalies = []

        # 检测数据访问日志
        access_logs = [
            log for log in audit_logs
            if log.action and "VIEW" in log.action.upper()
        ]

        user_access = defaultdict(list)
        for log in access_logs:
            if log.user_id:
                user_access[log.user_id].append(log)

        for user_id, logs in user_access.items():
            # 检测访问频率异常
            if len(logs) > 100:  # 超过100次访问
                time_span = (max(log.created_at for log in logs) -
                           min(log.created_at for log in logs)).total_seconds() / 3600

                access_rate = len(logs) / max(time_span, 0.1)  # 每小时访问次数

                if access_rate > 50:  # 每小时超过50次访问
                    resource_types = Counter(log.resource_type for log in logs if log.resource_type)

                    anomalies.append({
                        "type": "mass_data_access",
                        "subtype": "high_frequency_access",
                        "user_id": user_id,
                        "severity": "high",
                        "details": {
                            "total_access_count": len(logs),
                            "access_rate_per_hour": round(access_rate, 2),
                            "time_span_hours": round(time_span, 2),
                            "resource_distribution": dict(resource_types.most_common(5))
                        },
                        "timestamp": max(log.created_at for log in logs).isoformat()
                    })

        return {
            "anomalies": anomalies,
            "detection_summary": {
                "high_frequency_users": len(anomalies)
            }
        }

    async def _detect_unusual_time_access(
        self,
        audit_logs: List[AuditLog],
        start_time: datetime,
        end_time: datetime
    ) -> Dict[str, Any]:
        """检测异常时间访问"""
        anomalies = []

        # 检测非工作时间的活动
        unusual_time_logs = [
            log for log in audit_logs
            if log.created_at.hour < 7 or log.created_at.hour > 19 or
            log.created_at.weekday() >= 5  # 周末
        ]

        user_unusual_access = defaultdict(list)
        for log in unusual_time_logs:
            if log.user_id:
                user_unusual_access[log.user_id].append(log)

        for user_id, logs in user_unusual_access.items():
            if len(logs) > 20:  # 超过20次异常时间访问
                anomalies.append({
                    "type": "unusual_time_access",
                    "subtype": "off_hours_activity",
                    "user_id": user_id,
                    "severity": "medium",
                    "details": {
                        "unusual_access_count": len(logs),
                        "time_distribution": self._analyze_time_distribution(logs),
                        "actions": list(set(log.action for log in logs if log.action))
                    },
                    "timestamp": max(log.created_at for log in logs).isoformat()
                })

        return {
            "anomalies": anomalies,
            "detection_summary": {
                "off_hours_users": len(anomalies)
            }
        }

    async def _detect_rapid_permission_changes(
        self,
        audit_logs: List[AuditLog],
        start_time: datetime,
        end_time: datetime
    ) -> Dict[str, Any]:
        """检测快速权限变更"""
        anomalies = []

        # 检测权限变更日志
        permission_logs = [
            log for log in audit_logs
            if log.resource_type in ["ROLE", "PERMISSION"] and
            log.action in ["CREATE", "UPDATE", "DELETE"]
        ]

        # 按时间窗口分析
        time_windows = self._create_time_windows(start_time, end_time, window_minutes=30)

        for window_start, window_end in time_windows:
            window_logs = [
                log for log in permission_logs
                if window_start <= log.created_at < window_end
            ]

            if len(window_logs) > 10:  # 30分钟内超过10次权限变更
                user_changes = defaultdict(int)
                for log in window_logs:
                    user_changes[log.user_id] += 1

                anomalies.append({
                    "type": "rapid_permission_changes",
                    "subtype": "burst_permission_changes",
                    "severity": "high",
                    "details": {
                        "window_start": window_start.isoformat(),
                        "window_end": window_end.isoformat(),
                        "total_changes": len(window_logs),
                        "user_changes": dict(user_changes),
                        "most_active_user": max(user_changes.items(), key=lambda x: x[1]) if user_changes else None
                    },
                    "timestamp": window_end.isoformat()
                })

        return {
            "anomalies": anomalies,
            "detection_summary": {
                "burst_periods": len(anomalies)
            }
        }

    # 辅助方法
    def _detect_ip_changes(self, activities: List[AuditLog]) -> Optional[Dict[str, Any]]:
        """检测IP地址变化"""
        ip_addresses = []
        for activity in activities:
            ip = getattr(activity, 'ip_address', None)
            if ip:
                ip_addresses.append((ip, activity.created_at))

        if len(set(ip[0] for ip in ip_addresses)) > 2:  # 超过2个不同IP
            return {
                "unique_ip_count": len(set(ip[0] for ip in ip_addresses)),
                "ip_timeline": [{"ip": ip, "timestamp": ts.isoformat()} for ip, ts in ip_addresses]
            }
        return None

    def _detect_user_agent_changes(self, activities: List[AuditLog]) -> Optional[Dict[str, Any]]:
        """检测用户代理变化"""
        user_agents = []
        for activity in activities:
            ua = getattr(activity, 'user_agent', None)
            if ua:
                user_agents.append((ua, activity.created_at))

        unique_uas = set(ua[0] for ua in user_agents)
        if len(unique_uas) > 2:  # 超过2个不同用户代理
            return {
                "unique_ua_count": len(unique_uas),
                "user_agents": list(unique_uas)
            }
        return None

    def _detect_behavior_pattern_changes(self, activities: List[AuditLog]) -> Optional[Dict[str, Any]]:
        """检测行为模式变化"""
        # 分析操作类型分布
        actions = [activity.action for activity in activities if activity.action]
        action_counts = Counter(actions)

        # 检测是否有异常的操作类型突增
        total_actions = len(actions)
        if total_actions > 10:
            for action, count in action_counts.items():
                if count / total_actions > 0.7:  # 某个操作占比超过70%
                    return {
                        "dominant_action": action,
                        "action_ratio": count / total_actions,
                        "total_actions": total_actions
                    }
        return None

    def _analyze_time_distribution(self, logs: List[AuditLog]) -> Dict[str, int]:
        """分析时间分布"""
        hour_distribution = defaultdict(int)
        weekday_distribution = defaultdict(int)

        for log in logs:
            hour_distribution[log.created_at.hour] += 1
            weekday_distribution[log.created_at.weekday()] += 1

        return {
            "by_hour": dict(hour_distribution),
            "by_weekday": dict(weekday_distribution)
        }

    def _create_time_windows(
        self,
        start_time: datetime,
        end_time: datetime,
        window_minutes: int = 30
    ) -> List[Tuple[datetime, datetime]]:
        """创建时间窗口"""
        windows = []
        current_time = start_time
        window_delta = timedelta(minutes=window_minutes)

        while current_time < end_time:
            window_end = min(current_time + window_delta, end_time)
            windows.append((current_time, window_end))
            current_time = window_end

        return windows

    def _calculate_overall_risk_score(self, anomalies: List[Dict[str, Any]]) -> int:
        """计算整体风险分数"""
        if not anomalies:
            return 0

        severity_weights = {
            "low": 1,
            "medium": 3,
            "high": 5,
            "critical": 8
        }

        total_score = 0
        for anomaly in anomalies:
            severity = anomaly.get("severity", "medium")
            weight = severity_weights.get(severity, 3)
            total_score += weight

        # 归一化到0-100分
        max_possible_score = len(anomalies) * 8  # 假设都是critical
        normalized_score = min(int((total_score / max_possible_score) * 100), 100)

        return normalized_score

    async def get_anomaly_statistics(
        self,
        days: int = 7
    ) -> Dict[str, Any]:
        """获取异常统计信息"""
        end_time = datetime.utcnow()
        start_time = end_time - timedelta(days=days)

        # 获取历史异常检测结果（这里需要实现异常结果的持久化）
        # 暂时返回模拟数据
        return {
            "time_range": {
                "start_time": start_time.isoformat(),
                "end_time": end_time.isoformat(),
                "days": days
            },
            "anomaly_trends": {
                "account_takeover": [2, 1, 3, 0, 1, 2, 1],
                "data_exfiltration": [1, 0, 2, 1, 0, 1, 3],
                "privilege_escalation": [0, 1, 0, 2, 1, 0, 1],
                "brute_force": [5, 3, 7, 2, 4, 6, 3]
            },
            "top_risk_users": [
                {"user_id": 1001, "risk_score": 85, "anomaly_count": 5},
                {"user_id": 1002, "risk_score": 72, "anomaly_count": 3},
                {"user_id": 1003, "risk_score": 68, "anomaly_count": 4}
            ],
            "detection_effectiveness": {
                "total_detections": 156,
                "false_positives": 12,
                "accuracy_rate": 92.3
            }
        }
