#!/bin/bash
set -e

echo "启动沃尔玛绑卡处理器..."
echo "======================================="

# 创建配置文件
if [ ! -f "/app/config.yaml" ]; then
    echo "使用配置模板创建配置文件..."
    cp /app/config.yaml.template /app/config.yaml
fi

# 使用环境变量覆盖配置文件中的数据库配置
if [ -n "$DATABASE_HOST" ]; then
    echo "覆盖数据库主机: $DATABASE_HOST"
    sed -i "s/host: \"localhost\"/host: \"$DATABASE_HOST\"/" /app/config.yaml
fi

if [ -n "$DATABASE_PORT" ]; then
    echo "覆盖数据库端口: $DATABASE_PORT"
    sed -i "s/port: 3306/port: $DATABASE_PORT/" /app/config.yaml
fi

if [ -n "$DATABASE_USER" ]; then
    echo "覆盖数据库用户: $DATABASE_USER"
    sed -i "s/user: \"root\"/user: \"$DATABASE_USER\"/" /app/config.yaml
fi

if [ -n "$DATABASE_PASSWORD" ]; then
    echo "覆盖数据库密码: [HIDDEN]"
    sed -i "s/password: \".*\"/password: \"$DATABASE_PASSWORD\"/" /app/config.yaml
fi

if [ -n "$DATABASE_DB_NAME" ]; then
    echo "覆盖数据库名称: $DATABASE_DB_NAME"
    sed -i "s/db_name: \"walmart_card_db\"/db_name: \"$DATABASE_DB_NAME\"/" /app/config.yaml
fi

# 使用环境变量覆盖Redis配置 - 需要更精确的匹配
if [ -n "$REDIS_HOST" ]; then
    echo "覆盖Redis主机: $REDIS_HOST"
    # 只替换redis部分的host配置
    sed -i '/^redis:/,/^[a-z]/ { /^  host:/ s/host: "localhost"/host: "'$REDIS_HOST'"/ }' /app/config.yaml
fi

if [ -n "$REDIS_PORT" ]; then
    echo "覆盖Redis端口: $REDIS_PORT"
    sed -i '/^redis:/,/^[a-z]/ { /^  port:/ s/port: 6379/port: '$REDIS_PORT'/ }' /app/config.yaml
fi

if [ -n "$REDIS_PASSWORD" ]; then
    echo "覆盖Redis密码: [HIDDEN]"
    sed -i '/^redis:/,/^[a-z]/ { /^  password:/ s/password: ".*"/password: "'$REDIS_PASSWORD'"/ }' /app/config.yaml
fi

if [ -n "$REDIS_DB" ]; then
    echo "覆盖Redis数据库: $REDIS_DB"
    sed -i '/^redis:/,/^[a-z]/ { /^  db:/ s/db: 0/db: '$REDIS_DB'/ }' /app/config.yaml
fi

# 使用环境变量覆盖RabbitMQ配置
if [ -n "$RABBITMQ_HOST" ]; then
    echo "覆盖RabbitMQ主机: $RABBITMQ_HOST"
    sed -i '/^rabbitmq:/,/^[a-z]/ { /^  host:/ s/host: localhost/host: '$RABBITMQ_HOST'/ }' /app/config.yaml
fi

if [ -n "$RABBITMQ_PORT" ]; then
    echo "覆盖RabbitMQ端口: $RABBITMQ_PORT"
    sed -i '/^rabbitmq:/,/^[a-z]/ { /^  port:/ s/port: 5672/port: '$RABBITMQ_PORT'/ }' /app/config.yaml
fi

if [ -n "$RABBITMQ_USER" ]; then
    echo "覆盖RabbitMQ用户: $RABBITMQ_USER"
    sed -i '/^rabbitmq:/,/^[a-z]/ { /^  user:/ s/user: walmart_card/user: '$RABBITMQ_USER'/ }' /app/config.yaml
fi

if [ -n "$RABBITMQ_PASSWORD" ]; then
    echo "覆盖RabbitMQ密码: [HIDDEN]"
    sed -i '/^rabbitmq:/,/^[a-z]/ { /^  password:/ s/password: .*/password: '$RABBITMQ_PASSWORD'/ }' /app/config.yaml
fi

if [ -n "$RABBITMQ_VHOST" ]; then
    echo "覆盖RabbitMQ虚拟主机: $RABBITMQ_VHOST"
    sed -i '/^rabbitmq:/,/^[a-z]/ { /^  vhost:/ s|vhost: /walmart_card|vhost: '$RABBITMQ_VHOST'| }' /app/config.yaml
fi

echo ""
echo "配置文件: /app/config.yaml"
echo "日志目录: /app/logs"
echo "时间: $(date)"
echo ""

# 显示最终的数据库和Redis配置（用于调试）
echo "最终配置信息："
echo "数据库主机: $(grep -A 10 '^database:' /app/config.yaml | grep 'host:' | head -1 | awk '{print $2}')"
echo "Redis主机: $(grep -A 10 '^redis:' /app/config.yaml | grep 'host:' | head -1 | awk '{print $2}')"
echo ""

# 启动应用
exec ./walmart-bind-card-processor
