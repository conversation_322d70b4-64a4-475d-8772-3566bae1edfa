"""
高级权限管理API
包括权限继承显示、冲突检测等功能
"""

from typing import List, Optional, Any, Dict
from fastapi import APIRouter, Depends, HTTPException, Query, Path, status
from sqlalchemy.orm import Session

from app.api import deps
from app.models.user import User
from app.services.permission_inheritance_service import PermissionInheritanceService
from app.services.permission_conflict_service import PermissionConflictService
from app.services.permission_service import PermissionService
from app.core.logging import get_logger

router = APIRouter()
logger = get_logger("permission_advanced_api")


@router.get("/inheritance/user/{user_id}", response_model=Dict[str, Any])
async def get_user_permission_inheritance(
    user_id: int = Path(..., gt=0),
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_active_user),
):
    """
    获取用户的权限继承信息
    
    权限要求:
    - "permission:view": 查看权限信息
    """
    try:
        # 权限检查
        permission_service = PermissionService(db)
        has_permission = permission_service.check_user_permission(
            current_user, "permission:view"
        )
        if not has_permission:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="没有查看权限信息的权限",
            )

        # 获取目标用户
        from app.models.user import User as UserModel
        target_user = db.query(UserModel).filter(UserModel.id == user_id).first()
        if not target_user:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="用户不存在",
            )

        # 数据权限检查：非超级管理员只能查看自己商户的用户
        if not current_user.is_superuser:
            if target_user.merchant_id != current_user.merchant_id:
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail="只能查看自己商户的用户权限信息",
                )

        # 获取权限继承信息
        inheritance_service = PermissionInheritanceService(db)
        inheritance_info = inheritance_service.get_user_permission_inheritance(target_user)

        return {
            "success": True,
            "data": inheritance_info,
            "message": "获取用户权限继承信息成功"
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取用户权限继承信息失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取用户权限继承信息失败: {str(e)}",
        )


@router.get("/inheritance/role/{role_id}", response_model=Dict[str, Any])
async def get_role_permission_inheritance(
    role_id: int = Path(..., gt=0),
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_active_user),
):
    """
    获取角色的权限继承信息
    
    权限要求:
    - "role:view": 查看角色信息
    """
    try:
        # 权限检查
        permission_service = PermissionService(db)
        has_permission = permission_service.check_user_permission(
            current_user, "role:view"
        )
        if not has_permission:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="没有查看角色信息的权限",
            )

        # 获取权限继承信息
        inheritance_service = PermissionInheritanceService(db)
        inheritance_info = inheritance_service.get_role_permission_inheritance(role_id)

        return {
            "success": True,
            "data": inheritance_info,
            "message": "获取角色权限继承信息成功"
        }

    except HTTPException:
        raise
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=str(e),
        )
    except Exception as e:
        logger.error(f"获取角色权限继承信息失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取角色权限继承信息失败: {str(e)}",
        )


@router.get("/usage/{permission_code}", response_model=Dict[str, Any])
async def get_permission_usage_info(
    permission_code: str = Path(...),
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_active_user),
):
    """
    获取权限的使用信息
    
    权限要求:
    - "permission:view": 查看权限信息
    """
    try:
        # 权限检查
        permission_service = PermissionService(db)
        has_permission = permission_service.check_user_permission(
            current_user, "permission:view"
        )
        if not has_permission:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="没有查看权限信息的权限",
            )

        # 获取权限使用信息
        inheritance_service = PermissionInheritanceService(db)
        usage_info = inheritance_service.get_permission_usage_info(permission_code)

        return {
            "success": True,
            "data": usage_info,
            "message": "获取权限使用信息成功"
        }

    except HTTPException:
        raise
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=str(e),
        )
    except Exception as e:
        logger.error(f"获取权限使用信息失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取权限使用信息失败: {str(e)}",
        )


@router.get("/conflicts/scan", response_model=Dict[str, Any])
async def scan_permission_conflicts(
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_active_user),
):
    """
    扫描权限冲突
    
    权限要求:
    - "permission:admin": 权限管理员权限
    """
    try:
        # 权限检查
        permission_service = PermissionService(db)
        has_permission = permission_service.check_user_permission(
            current_user, "permission:admin"
        )
        if not has_permission:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="没有权限管理员权限",
            )

        # 扫描权限冲突
        conflict_service = PermissionConflictService(db)
        conflict_result = conflict_service.detect_all_conflicts()

        return {
            "success": True,
            "data": conflict_result,
            "message": "权限冲突扫描完成"
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"权限冲突扫描失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"权限冲突扫描失败: {str(e)}",
        )


@router.post("/conflicts/{conflict_id}/resolve", response_model=Dict[str, Any])
async def resolve_permission_conflict(
    conflict_id: str = Path(...),
    resolution_action: str = Query(..., description="解决方案"),
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_active_user),
):
    """
    解决权限冲突
    
    权限要求:
    - "permission:admin": 权限管理员权限
    """
    try:
        # 权限检查
        permission_service = PermissionService(db)
        has_permission = permission_service.check_user_permission(
            current_user, "permission:admin"
        )
        if not has_permission:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="没有权限管理员权限",
            )

        # 解决冲突
        conflict_service = PermissionConflictService(db)
        result = conflict_service.resolve_conflict(conflict_id, resolution_action)

        if result["success"]:
            return {
                "success": True,
                "data": result,
                "message": "权限冲突解决成功"
            }
        else:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=result["message"],
            )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"解决权限冲突失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"解决权限冲突失败: {str(e)}",
        )


@router.post("/init-complete-permissions", response_model=Dict[str, Any])
async def init_complete_permissions(
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_active_user),
):
    """
    初始化完整的API权限数据
    
    权限要求:
    - "permission:admin": 权限管理员权限
    """
    try:
        # 权限检查
        permission_service = PermissionService(db)
        has_permission = permission_service.check_user_permission(
            current_user, "permission:admin"
        )
        if not has_permission:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="没有权限管理员权限",
            )

        # 执行权限初始化脚本
        import subprocess
        import sys
        import os

        # 获取项目根目录
        current_dir = os.path.dirname(__file__)
        project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(current_dir))))
        script_path = os.path.join(project_root, "scripts", "init_complete_api_permissions.py")
        
        result = subprocess.run(
            [sys.executable, script_path],
            capture_output=True,
            text=True,
            timeout=60
        )
        
        if result.returncode == 0:
            return {
                "success": True,
                "data": {
                    "output": result.stdout,
                    "return_code": result.returncode
                },
                "message": "完整权限数据初始化成功"
            }
        else:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"权限初始化失败: {result.stderr}",
            )

    except HTTPException:
        raise
    except subprocess.TimeoutExpired:
        raise HTTPException(
            status_code=status.HTTP_408_REQUEST_TIMEOUT,
            detail="权限初始化超时",
        )
    except Exception as e:
        logger.error(f"初始化完整权限数据失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"初始化完整权限数据失败: {str(e)}",
        )
