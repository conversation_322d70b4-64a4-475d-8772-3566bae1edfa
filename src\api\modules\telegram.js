import { http } from "../request";

/**
 * Telegram机器人相关API
 */
export const telegramApi = {
  // ==================== 机器人控制 ====================

  /**
   * 获取机器人状态
   */
  getBotStatus() {
    return http.get("/telegram/status").then((res) => res.data || res);
  },

  /**
   * 启动机器人服务
   */
  startBot() {
    return http.post("/telegram/start").then((res) => res.data || res);
  },

  /**
   * 停止机器人服务
   */
  stopBot() {
    return http.post("/telegram/stop").then((res) => res.data || res);
  },

  /**
   * 重启机器人服务
   */
  restartBot() {
    return http.post("/telegram/restart").then((res) => res.data || res);
  },

  /**
   * 重新加载关键词配置
   */
  reloadKeywords() {
    return http
      .post("/telegram/reload-keywords")
      .then((res) => res.data || res);
  },

  /**
   * 获取关键词配置
   */
  getKeywords() {
    return http.get("/telegram/keywords").then((res) => res.data || res);
  },

  /**
   * 测试关键词匹配
   */
  testKeywordMatch(messageText) {
    return http
      .post("/telegram/keywords/test", null, {
        params: { message_text: messageText },
      })
      .then((res) => res.data || res);
  },

  /**
   * 获取机器人信息
   */
  getBotInfo() {
    return http.get("/telegram/bot-info").then((res) => res.data || res);
  },

  // ==================== 配置管理 ====================

  /**
   * 获取全局配置
   */
  getGlobalConfig() {
    return http.get("/telegram/config/global").then((res) => res.data || res);
  },

  /**
   * 更新全局配置
   */
  updateGlobalConfig(data) {
    return http
      .put("/telegram/config/global", data)
      .then((res) => res.data || res);
  },

  /**
   * 获取商户配置
   */
  getMerchantConfig(merchantId) {
    return http
      .get(`/telegram/config/merchants/${merchantId}/telegram-settings`)
      .then((res) => res.data || res);
  },

  /**
   * 更新商户配置
   */
  updateMerchantConfig(merchantId, data) {
    return http
      .put(`/telegram/config/merchants/${merchantId}/telegram-settings`, data)
      .then((res) => res.data || res);
  },

  /**
   * 获取群组配置
   */
  getGroupConfig(groupId) {
    return http
      .get(`/telegram/config/groups/${groupId}/settings`)
      .then((res) => res.data || res);
  },

  /**
   * 更新群组配置
   */
  updateGroupConfig(groupId, data) {
    return http
      .put(`/telegram/config/groups/${groupId}/settings`, data)
      .then((res) => res.data || res);
  },

  /**
   * 验证配置
   */
  validateConfig(data) {
    return http
      .post("/telegram/config/validate-settings", data)
      .then((res) => res.data || res);
  },

  /**
   * 获取缓存统计
   */
  getCacheStats() {
    return http
      .get("/telegram/config/cache-stats")
      .then((res) => res.data || res);
  },

  /**
   * 清除缓存
   */
  clearCache() {
    return http
      .post("/telegram/config/clear-cache")
      .then((res) => res.data || res);
  },

  // ==================== 群组管理 ====================

  /**
   * 获取群组列表
   */
  getGroupList(params = {}) {
    return http
      .get("/telegram/groups", { params })
      .then((res) => res.data || res);
  },

  /**
   * 创建群组绑定令牌
   */
  createGroupBindToken(data) {
    return http
      .post("/telegram/groups/create-bind-token", data)
      .then((res) => res.data || res);
  },

  /**
   * 获取群组详情
   */
  getGroupDetail(groupId) {
    return http
      .get(`/telegram/groups/${groupId}`)
      .then((res) => res.data || res);
  },

  /**
   * 更新群组信息
   */
  updateGroup(groupId, data) {
    return http
      .put(`/telegram/groups/${groupId}`, data)
      .then((res) => res.data || res);
  },

  /**
   * 解绑群组
   */
  unbindGroup(groupId) {
    return http
      .delete(`/telegram/groups/${groupId}/unbind`)
      .then((res) => res.data || res);
  },

  /**
   * 重置绑定令牌
   */
  resetBindToken(groupId) {
    return http
      .post(`/telegram/groups/${groupId}/reset-token`)
      .then((res) => res.data || res);
  },

  /**
   * 获取群组统计
   */
  getGroupStats(groupId, params = {}) {
    return http
      .get(`/telegram/groups/${groupId}/stats`, { params })
      .then((res) => res.data || res);
  },

  // ==================== 用户管理 ====================

  /**
   * 获取Telegram用户列表
   */
  getTelegramUserList(params = {}) {
    return http
      .get("/telegram/users", { params })
      .then((res) => res.data || res);
  },

  /**
   * 获取用户详情
   */
  getTelegramUserDetail(userId) {
    return http.get(`/telegram/users/${userId}`).then((res) => res.data || res);
  },

  /**
   * 更新用户信息
   */
  updateTelegramUser(userId, data) {
    return http
      .put(`/telegram/users/${userId}`, data)
      .then((res) => res.data || res);
  },

  /**
   * 删除用户
   */
  deleteTelegramUser(userId) {
    return http
      .delete(`/telegram/users/${userId}`)
      .then((res) => res.data || res);
  },

  /**
   * 获取用户操作日志
   */
  getUserLogs(userId, params = {}) {
    return http
      .get(`/telegram/users/${userId}/logs`, { params })
      .then((res) => res.data || res);
  },

  /**
   * 验证Telegram用户身份
   */
  verifyTelegramUser(data) {
    return http
      .post("/telegram/users/verify", data)
      .then((res) => res.data || res);
  },

  /**
   * 拒绝Telegram用户验证申请
   */
  rejectTelegramUser(data) {
    return http
      .post("/telegram/users/reject", data)
      .then((res) => res.data || res);
  },

  /**
   * 刷新用户验证token
   */
  refreshVerificationToken(userId) {
    return http
      .post("/telegram/users/refresh-token", { user_id: userId })
      .then((res) => res.data || res);
  },

  /**
   * 调试用户信息
   */
  debugTelegramUser(userId) {
    return http
      .get(`/telegram/users/debug/${userId}`)
      .then((res) => res.data || res);
  },

  // ==================== 统计查询 ====================

  /**
   * 获取机器人统计数据
   */
  getBotStatistics(params = {}) {
    return http
      .get("/telegram/statistics", { params })
      .then((res) => res.data || res);
  },

  /**
   * 获取群组统计数据
   */
  getGroupStatistics(params = {}) {
    return http
      .get("/telegram/statistics/groups", { params })
      .then((res) => res.data || res);
  },

  /**
   * 获取用户统计数据
   */
  getUserStatistics(params = {}) {
    return http
      .get("/telegram/statistics/users", { params })
      .then((res) => res.data || res);
  },

  /**
   * 获取命令统计数据
   */
  getCommandStatistics(params = {}) {
    return http
      .get("/telegram/statistics/commands", { params })
      .then((res) => res.data || res);
  },

  /**
   * 获取图表数据
   */
  getChartData(chartType, params = {}) {
    return http
      .get("/telegram/statistics/charts", {
        params: { chart_type: chartType, ...params },
      })
      .then((res) => {
        // 后端返回格式：{code: 0, data: {categories: [...], data: [...]}, message: "..."}
        // 我们需要返回 res.data，即图表数据部分
        if (res && res.data) {
          return res.data;
        }
        return res;
      });
  },

  // ==================== 日志管理 ====================

  /**
   * 获取机器人日志
   */
  getBotLogs(params = {}) {
    return http
      .get("/telegram/logs", { params })
      .then((res) => res.data || res);
  },

  /**
   * 获取操作日志
   */
  getOperationLogs(params = {}) {
    return http
      .get("/telegram/logs/operations", { params })
      .then((res) => res.data || res);
  },

  /**
   * 获取错误日志
   */
  getErrorLogs(params = {}) {
    return http
      .get("/telegram/logs/errors", { params })
      .then((res) => res.data || res);
  },
};
