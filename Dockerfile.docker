# 高速构建版Dockerfile - 专为国内网络环境优化
# 使用阿里云镜像源，大幅提升构建速度

# 多阶段构建 - 构建阶段
ARG DOCKER_REGISTRY=docker.1ms.run
FROM ${DOCKER_REGISTRY}/golang:1.21-alpine AS builder

# 设置工作目录
WORKDIR /app

# 配置Alpine镜像源（阿里云）
RUN sed -i 's/dl-cdn.alpinelinux.org/mirrors.aliyun.com/g' /etc/apk/repositories

# 配置Go模块代理（多个国内源）
ENV GOPROXY=https://goproxy.cn,https://mirrors.aliyun.com/goproxy/,https://goproxy.io,direct
ENV GOSUMDB=sum.golang.google.cn
ENV GO111MODULE=on
ENV GONOPROXY=""
ENV GONOSUMDB=""
ENV GOPRIVATE=""

# 安装必要的包
RUN apk add --no-cache git ca-certificates tzdata upx

# 复制go mod文件
COPY go.mod go.sum ./

# 下载依赖（使用国内代理）
RUN go mod download

# 复制源代码
COPY . .

# 构建应用（安全编译）
RUN CGO_ENABLED=0 GOOS=linux GOARCH=amd64 go build \
    -ldflags='-w -s -extldflags "-static" -buildid=' \
    -trimpath \
    -a -installsuffix cgo \
    -o walmart-gateway \
    main.go

# 使用UPX压缩可执行文件
RUN upx --best --lzma walmart-gateway

# 运行阶段 - 使用阿里云Ubuntu镜像
ARG DOCKER_REGISTRY=registry.cn-hangzhou.aliyuncs.com
FROM ${DOCKER_REGISTRY}/library/ubuntu:24.04

# 设置非交互模式
ENV DEBIAN_FRONTEND=noninteractive

# 直接写入阿里云APT源配置
RUN echo "deb http://mirrors.aliyun.com/ubuntu/ noble main restricted universe multiverse" > /etc/apt/sources.list && \
    echo "deb http://mirrors.aliyun.com/ubuntu/ noble-updates main restricted universe multiverse" >> /etc/apt/sources.list && \
    echo "deb http://mirrors.aliyun.com/ubuntu/ noble-backports main restricted universe multiverse" >> /etc/apt/sources.list && \
    echo "deb http://mirrors.aliyun.com/ubuntu/ noble-security main restricted universe multiverse" >> /etc/apt/sources.list

# 更新包列表并安装必要的包
RUN apt-get update && apt-get install -y \
    ca-certificates \
    tzdata \
    curl \
    wget \
    && rm -rf /var/lib/apt/lists/*

# 设置时区
ENV TZ=Asia/Shanghai
RUN ln -snf /usr/share/zoneinfo/$TZ /etc/localtime && echo $TZ > /etc/timezone

# 创建非root用户
RUN groupadd -g 1001 appgroup && \
    useradd -u 1001 -g appgroup -m -s /bin/bash appuser

# 设置工作目录
WORKDIR /app

# 从构建阶段复制二进制文件
COPY --from=builder /app/walmart-gateway .

# 复制配置文件模板
COPY --from=builder /app/config.yaml ./config.yaml.template

# 创建日志目录
RUN mkdir -p /app/logs

# 设置文件权限
RUN chown -R appuser:appgroup /app

# 创建启动脚本
RUN echo '#!/bin/bash' > /app/start.sh && \
    echo 'set -e' >> /app/start.sh && \
    echo '' >> /app/start.sh && \
    echo '# 如果存在环境变量配置，则使用环境变量覆盖配置文件' >> /app/start.sh && \
    echo 'if [ ! -f "/app/config.yaml" ]; then' >> /app/start.sh && \
    echo '    echo "使用配置模板创建配置文件..."' >> /app/start.sh && \
    echo '    cp /app/config.yaml.template /app/config.yaml' >> /app/start.sh && \
    echo 'fi' >> /app/start.sh && \
    echo '' >> /app/start.sh && \
    echo '# 启动应用' >> /app/start.sh && \
    echo 'echo "启动沃尔玛绑卡网关..."' >> /app/start.sh && \
    echo 'echo "配置文件: /app/config.yaml"' >> /app/start.sh && \
    echo 'echo "日志目录: /app/logs"' >> /app/start.sh && \
    echo 'echo "时间: $(date)"' >> /app/start.sh && \
    echo 'echo ""' >> /app/start.sh && \
    echo '' >> /app/start.sh && \
    echo 'exec ./walmart-gateway' >> /app/start.sh && \
    chmod +x /app/start.sh && \
    chown appuser:appgroup /app/start.sh

# 切换到非root用户
USER appuser

# 暴露端口
EXPOSE 21000

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=30s --retries=3 \
    CMD curl -f http://localhost:21000/health || exit 1

# 启动应用
CMD ["/app/start.sh"]
