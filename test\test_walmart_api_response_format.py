"""
沃尔玛API响应格式测试
测试实际API响应格式的解析逻辑
"""

import pytest
from unittest.mock import Mock, patch, AsyncMock
from app.services.ck_validation_service import CKValidationService
from app.core.walmart_api import WalmartAPI
from app.models.walmart_ck import WalmartCK
from app.db.session import SessionLocal


class TestWalmartAPIResponseFormat:
    """沃尔玛API响应格式测试类"""

    @pytest.fixture
    def db_session(self):
        """数据库会话fixture"""
        db = SessionLocal()
        try:
            yield db
        finally:
            db.close()

    @pytest.fixture
    def sample_ck(self):
        """创建测试CK对象"""
        return WalmartCK(
            id=1,
            sign="test_sign_123",
            total_limit=20,
            bind_count=5,
            active=True,
            merchant_id=1,
            department_id=1,
            is_deleted=False
        )

    @pytest.mark.asyncio
    async def test_successful_response_parsing(self, db_session, sample_ck):
        """测试成功响应的解析"""
        validation_service = CKValidationService(db_session)
        
        # 实际成功响应格式
        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.json.return_value = {
            "logId": "YYtFTnf4",
            "status": True,
            "error": {
                "errorcode": 1,
                "message": None,
                "redirect": None,
                "validators": None
            },
            "data": {
                "cardCount": 4,
                "nickName": "微信用户",
                "headImg": "https://thirdwx.qlogo.cn/mmopen/vi_32/POgEwh4mIHO4nibH0KlMECNjjGxQUq24ZEaGT4poC6icRiccVGKSyXwibcPq4BWmiaIGuG1icwxaQX6grC9VemZoJ8rg/132",
                "upcardOrderUrl": "https://vpay.upcard.com.cn/vcweixin/commercial/walm/gotoQuery%3FopenId%3Dxxx%26company%3Dwalm"
            }
        }
        
        with patch('app.core.walmart_api.WalmartAPI.query_user', new_callable=AsyncMock) as mock_query:
            mock_query.return_value = mock_response
            
            result = await validation_service.validate_ck_availability(sample_ck)
            
            # 验证解析结果
            assert result["is_valid"] is True
            assert result["error_message"] is None
            assert result["error_code"] is None
            assert result["should_disable"] is False
            assert result["log_id"] == "YYtFTnf4"
            assert result["card_count"] == 4
            assert result["nick_name"] == "微信用户"

    @pytest.mark.asyncio
    async def test_failed_response_parsing(self, db_session, sample_ck):
        """测试失败响应的解析"""
        validation_service = CKValidationService(db_session)
        
        # 实际失败响应格式
        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.json.return_value = {
            "logId": "ERROR123",
            "status": False,
            "error": {
                "errorcode": 401,
                "message": "用户认证失败",
                "redirect": None,
                "validators": None
            },
            "data": None
        }
        
        with patch('app.core.walmart_api.WalmartAPI.query_user', new_callable=AsyncMock) as mock_query:
            mock_query.return_value = mock_response
            
            result = await validation_service.validate_ck_availability(sample_ck)
            
            # 验证解析结果
            assert result["is_valid"] is False
            assert result["error_message"] == "用户认证失败"
            assert result["error_code"] == "401"
            assert result["should_disable"] is True
            assert result["log_id"] == "ERROR123"

    @pytest.mark.asyncio
    async def test_critical_error_with_true_status(self, db_session, sample_ck):
        """测试status=true但有严重错误码的情况"""
        validation_service = CKValidationService(db_session)
        
        # status=true但errorcode=-1（严重错误）
        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.json.return_value = {
            "logId": "CRITICAL123",
            "status": True,
            "error": {
                "errorcode": -1,
                "message": "系统内部错误",
                "redirect": None,
                "validators": None
            },
            "data": None
        }
        
        with patch('app.core.walmart_api.WalmartAPI.query_user', new_callable=AsyncMock) as mock_query:
            mock_query.return_value = mock_response
            
            result = await validation_service.validate_ck_availability(sample_ck)
            
            # 验证解析结果
            assert result["is_valid"] is False
            assert result["error_message"] == "系统内部错误"
            assert result["error_code"] == "-1"
            assert result["should_disable"] is True
            assert result["log_id"] == "CRITICAL123"

    @pytest.mark.asyncio
    async def test_normal_errorcode_with_true_status(self, db_session, sample_ck):
        """测试status=true且errorcode=1的正常情况"""
        validation_service = CKValidationService(db_session)
        
        # status=true且errorcode=1（正常业务状态）
        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.json.return_value = {
            "logId": "NORMAL123",
            "status": True,
            "error": {
                "errorcode": 1,
                "message": None,
                "redirect": None,
                "validators": None
            },
            "data": {
                "cardCount": 2,
                "nickName": "正常用户",
                "headImg": "https://example.com/avatar.jpg",
                "upcardOrderUrl": "https://example.com/order"
            }
        }
        
        with patch('app.core.walmart_api.WalmartAPI.query_user', new_callable=AsyncMock) as mock_query:
            mock_query.return_value = mock_response
            
            result = await validation_service.validate_ck_availability(sample_ck)
            
            # 验证解析结果
            assert result["is_valid"] is True
            assert result["error_message"] is None
            assert result["error_code"] is None
            assert result["should_disable"] is False
            assert result["log_id"] == "NORMAL123"
            assert result["card_count"] == 2
            assert result["nick_name"] == "正常用户"

    def test_critical_error_code_detection(self, db_session):
        """测试严重错误码检测逻辑"""
        validation_service = CKValidationService(db_session)
        
        # 测试严重错误码
        assert validation_service._is_critical_error_code(-1) is True
        assert validation_service._is_critical_error_code(-999) is True
        assert validation_service._is_critical_error_code(401) is True
        assert validation_service._is_critical_error_code(403) is True
        assert validation_service._is_critical_error_code(404) is True
        assert validation_service._is_critical_error_code(500) is True
        
        # 测试正常错误码
        assert validation_service._is_critical_error_code(1) is False
        assert validation_service._is_critical_error_code(0) is False
        assert validation_service._is_critical_error_code(200) is False
        
        # 测试字符串错误码
        assert validation_service._is_critical_error_code("INVALID_SIGN") is True
        assert validation_service._is_critical_error_code("USER_NOT_FOUND") is True
        assert validation_service._is_critical_error_code("NORMAL_STATUS") is False
        
        # 测试None和无效值
        assert validation_service._is_critical_error_code(None) is False
        assert validation_service._is_critical_error_code("") is False

    def test_should_disable_ck_logic(self, db_session):
        """测试CK禁用判断逻辑"""
        validation_service = CKValidationService(db_session)
        
        # 测试严重错误码应该禁用
        assert validation_service._should_disable_ck(-1, "系统错误") is True
        assert validation_service._should_disable_ck(401, "未授权") is True
        assert validation_service._should_disable_ck("INVALID_SIGN", "签名无效") is True
        
        # 测试正常错误码不应该禁用
        assert validation_service._should_disable_ck(1, None) is False
        assert validation_service._should_disable_ck(0, "成功") is False
        
        # 测试错误消息关键词
        assert validation_service._should_disable_ck(1, "用户不存在") is True
        assert validation_service._should_disable_ck(1, "权限被拒绝") is True
        assert validation_service._should_disable_ck(1, "账户已禁用") is True
        assert validation_service._should_disable_ck(1, "正常业务消息") is False

    @pytest.mark.asyncio
    async def test_http_error_response(self, db_session, sample_ck):
        """测试HTTP错误响应"""
        validation_service = CKValidationService(db_session)
        
        # HTTP 500错误
        mock_response = Mock()
        mock_response.status_code = 500
        
        with patch('app.core.walmart_api.WalmartAPI.query_user', new_callable=AsyncMock) as mock_query:
            mock_query.return_value = mock_response
            
            result = await validation_service.validate_ck_availability(sample_ck)
            
            # 验证解析结果
            assert result["is_valid"] is False
            assert "HTTP请求失败" in result["error_message"]
            assert result["error_code"] == "HTTP_500"
            assert result["should_disable"] is True

    @pytest.mark.asyncio
    async def test_json_parse_error(self, db_session, sample_ck):
        """测试JSON解析错误"""
        validation_service = CKValidationService(db_session)
        
        # 无效JSON响应
        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.json.side_effect = ValueError("Invalid JSON")
        
        with patch('app.core.walmart_api.WalmartAPI.query_user', new_callable=AsyncMock) as mock_query:
            mock_query.return_value = mock_response
            
            result = await validation_service.validate_ck_availability(sample_ck)
            
            # 验证解析结果
            assert result["is_valid"] is False
            assert "响应解析异常" in result["error_message"]
            assert result["error_code"] == "PARSE_EXCEPTION"
            assert result["should_disable"] is True

    def test_walmart_api_response_logging(self):
        """测试沃尔玛API响应日志记录"""
        api = WalmartAPI(sign="test_sign")
        
        # 测试成功响应日志
        with patch('app.core.logging.get_logger') as mock_logger:
            mock_response = Mock()
            mock_response.json.return_value = {
                "logId": "TEST123",
                "status": True,
                "error": {"errorcode": 1, "message": None},
                "data": {"cardCount": 3, "nickName": "测试用户"}
            }
            
            # 这里我们只测试日志记录逻辑，不实际调用API
            # 实际测试中可以通过检查日志输出来验证


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
