from typing import Optional
from pydantic import BaseModel, Field
from datetime import datetime


class SystemSettingsBase(BaseModel):
    """系统设置基础模型"""

    key: str = Field(..., description="设置键名")
    value: str = Field(..., description="设置值")
    description: Optional[str] = Field(None, description="描述")

    class Config:
        from_attributes = True


class SystemSettingsCreate(SystemSettingsBase):
    """创建系统设置时的模型"""

    pass


class SystemSettingsUpdate(BaseModel):
    """更新系统设置时的模型"""

    value: Optional[str] = Field(None, description="设置值")
    description: Optional[str] = Field(None, description="描述")

    class Config:
        from_attributes = True


class SystemSettings(SystemSettingsBase):
    """API响应的系统设置模型"""

    id: int
    created_at: datetime
    updated_at: Optional[datetime] = None

    class Config:
        from_attributes = True
