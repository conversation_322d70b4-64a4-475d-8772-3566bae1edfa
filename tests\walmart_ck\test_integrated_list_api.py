#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试CK管理列表接口集成统计数据功能
"""

import sys
import os
import time
import json
from datetime import datetime, timedelta

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.dirname(__file__))))

from test.conftest import TestBase, get_test_accounts, format_test_result, print_test_summary


class IntegratedCKListAPITestSuite(TestBase):
    """测试集成的CK列表API"""

    def __init__(self):
        super().__init__()
        self.results = []
        self.test_accounts = get_test_accounts()
        self.admin_token = None
        self.merchant_token = None
        self.test_ck_ids = []  # 存储测试创建的CK ID，用于清理

    def setup_test_environment(self):
        """设置测试环境"""
        print("=== 设置CK列表集成API测试环境 ===")

        # 登录管理员账号
        self.admin_token = self.login(
            self.test_accounts["super_admin"]["username"],
            self.test_accounts["super_admin"]["password"]
        )

        # 登录商户账号
        self.merchant_token = self.login(
            self.test_accounts["merchant_admin"]["username"],
            self.test_accounts["merchant_admin"]["password"]
        )

        if not self.admin_token:
            raise Exception("无法获取管理员token")
        if not self.merchant_token:
            raise Exception("无法获取商户token")

        print(f"✓ 管理员登录成功")
        print(f"✓ 商户管理员登录成功")
    
    def _create_test_ck(self, token: str, sign: str, description: str = "测试CK") -> dict:
        """创建测试CK"""
        ck_data = {
            "sign": sign,
            "description": description,
            "hourly_limit": 100,
            "daily_limit": 1000,
            "active": True
        }

        response = self.post(
            "/walmart-cks",
            data=ck_data,
            token=token
        )

        if response and response.get("success"):
            ck_id = response["data"]["id"]
            self.test_ck_ids.append(ck_id)
            return response["data"]
        else:
            raise Exception(f"创建CK失败: {response}")

    def _create_test_card_record(self, token: str, ck_id: int, amount: float = 100.0) -> dict:
        """创建测试卡记录（通过绑卡API）"""
        card_data = {
            "card_number": f"test_card_{int(time.time() * 1000)}",
            "amount": amount
        }

        response = self.post(
            "/cards/bind",
            data=card_data,
            token=token
        )

        return response
    
    def test_list_with_statistics_basic(self):
        """测试基本的列表和统计数据获取"""
        print("\n--- 测试基本的列表和统计数据获取 ---")

        try:
            # 创建测试CK
            ck1 = self._create_test_ck(self.admin_token, "test_ck_1", "测试CK1")
            ck2 = self._create_test_ck(self.admin_token, "test_ck_2", "测试CK2")

            print(f"✓ 创建测试CK: {ck1['sign']}, {ck2['sign']}")

            # 调用集成的列表API
            response = self.get("/walmart-cks", token=self.admin_token, params={"page": 1, "page_size": 10})

            if not response or not response.get("success"):
                raise Exception(f"获取CK列表失败: {response}")

            data = response

            # 验证响应结构
            required_fields = ["items", "total", "page", "page_size", "pages", "statistics"]
            for field in required_fields:
                if field not in data:
                    raise Exception(f"响应缺少字段: {field}")

            # 验证统计数据结构
            stats = data["statistics"]
            if "summary" not in stats or "ck_details" not in stats:
                raise Exception("统计数据结构不正确")

            # 验证汇总统计
            summary = stats["summary"]
            required_summary_fields = ["total_cks", "total_records", "total_success", "success_rate", "total_request_amount", "total_actual_amount"]
            for field in required_summary_fields:
                if field not in summary:
                    raise Exception(f"汇总统计缺少字段: {field}")

            print(f"✓ 响应结构验证通过")
            print(f"✓ CK总数: {summary['total_cks']}")
            print(f"✓ 绑卡记录总数: {summary['total_records']}")
            print(f"✓ 成功率: {summary['success_rate']}%")

            return format_test_result(True, "基本列表和统计数据获取", "成功获取集成的列表和统计数据")

        except Exception as e:
            return format_test_result(False, "基本列表和统计数据获取", f"测试失败: {str(e)}")
    
    def test_time_filter_consistency(self):
        """测试时间过滤的一致性"""
        print("\n--- 测试时间过滤的一致性 ---")

        try:
            # 创建测试CK
            ck_today = self._create_test_ck(self.admin_token, "ck_today", "今天的CK")
            print(f"✓ 创建今天的CK: {ck_today['sign']}")

            # 获取今天的日期
            today = datetime.now().date()

            # 测试只查询今天的数据
            response = self.get(
                "/walmart-cks",
                token=self.admin_token,
                params={
                    "start_date": today.strftime("%Y-%m-%d"),
                    "end_date": today.strftime("%Y-%m-%d")
                }
            )

            if not response or not response.get("success"):
                raise Exception(f"时间过滤查询失败: {response}")

            data = response

            # 验证列表数据
            if "items" not in data:
                raise Exception("响应缺少items字段")

            # 验证统计数据
            if "statistics" not in data:
                raise Exception("响应缺少statistics字段")

            stats = data["statistics"]
            if "summary" not in stats:
                raise Exception("统计数据缺少summary字段")

            print(f"✓ 时间过滤查询成功")
            print(f"✓ 列表项数量: {len(data['items'])}")
            print(f"✓ 统计CK总数: {stats['summary']['total_cks']}")

            # 验证列表和统计数据的一致性
            list_count = len(data["items"])
            stats_count = stats["summary"]["total_cks"]

            if list_count != stats_count:
                raise Exception(f"列表数量({list_count})与统计数量({stats_count})不一致")

            print(f"✓ 列表和统计数据一致性验证通过")

            return format_test_result(True, "时间过滤一致性", "列表和统计数据在时间过滤下保持一致")

        except Exception as e:
            return format_test_result(False, "时间过滤一致性", f"测试失败: {str(e)}")
    
    def test_data_isolation_merchant_admin(self):
        """测试商户管理员的数据隔离"""
        print("\n--- 测试商户管理员的数据隔离 ---")

        try:
            # 使用超级管理员创建CK
            admin_ck = self._create_test_ck(self.admin_token, "admin_ck", "超管创建的CK")
            print(f"✓ 超级管理员创建CK: {admin_ck['sign']}")

            # 使用商户管理员创建CK
            merchant_ck = self._create_test_ck(self.merchant_token, "merchant_ck", "商户创建的CK")
            print(f"✓ 商户管理员创建CK: {merchant_ck['sign']}")

            # 使用商户管理员查询（应该只能看到自己商户的数据）
            response = self.get("/walmart-cks", token=self.merchant_token)

            if not response or not response.get("success"):
                raise Exception(f"商户管理员查询失败: {response}")

            data = response

            # 验证数据隔离
            merchant_items = [item for item in data["items"] if item["sign"] == "merchant_ck"]
            admin_items = [item for item in data["items"] if item["sign"] == "admin_ck"]

            if len(merchant_items) == 0:
                raise Exception("商户管理员看不到自己创建的CK")

            # 注意：根据权限设计，商户管理员可能能看到超管在其商户下创建的CK
            # 这里主要验证数据隔离逻辑是否正确

            # 验证统计数据的隔离
            stats = data["statistics"]
            if "summary" not in stats:
                raise Exception("统计数据缺少summary字段")

            print(f"✓ 商户管理员可见CK数量: {len(data['items'])}")
            print(f"✓ 统计CK总数: {stats['summary']['total_cks']}")

            # 验证列表和统计数据的一致性
            list_count = len(data["items"])
            stats_count = stats["summary"]["total_cks"]

            if list_count != stats_count:
                raise Exception(f"列表数量({list_count})与统计数量({stats_count})不一致")

            print(f"✓ 数据隔离验证通过")

            return format_test_result(True, "商户管理员数据隔离", "商户管理员只能看到有权限的数据，列表和统计保持一致")

        except Exception as e:
            return format_test_result(False, "商户管理员数据隔离", f"测试失败: {str(e)}")
    
    def test_empty_result_statistics(self):
        """测试空结果时的统计数据"""
        print("\n--- 测试空结果时的统计数据 ---")

        try:
            # 使用一个不存在数据的时间范围查询
            past_date = (datetime.now() - timedelta(days=365)).date()

            response = self.get(
                "/walmart-cks",
                token=self.merchant_token,
                params={
                    "start_date": past_date.strftime("%Y-%m-%d"),
                    "end_date": past_date.strftime("%Y-%m-%d")
                }
            )

            if not response or not response.get("success"):
                raise Exception(f"空结果查询失败: {response}")

            data = response

            # 验证空列表
            if len(data["items"]) != 0:
                print(f"警告: 预期空列表，但得到 {len(data['items'])} 项")

            # 验证空统计数据
            stats = data["statistics"]
            if "summary" not in stats:
                raise Exception("统计数据缺少summary字段")

            summary = stats["summary"]
            expected_zero_fields = ["total_cks", "total_records", "total_success", "success_rate", "total_request_amount", "total_actual_amount"]

            for field in expected_zero_fields:
                if field not in summary:
                    raise Exception(f"汇总统计缺少字段: {field}")
                if summary[field] != 0:
                    print(f"警告: {field} 预期为0，实际为 {summary[field]}")

            if "ck_details" not in stats:
                raise Exception("统计数据缺少ck_details字段")

            print(f"✓ 空结果验证通过")
            print(f"✓ 列表项数量: {len(data['items'])}")
            print(f"✓ 统计CK总数: {summary['total_cks']}")

            return format_test_result(True, "空结果统计数据", "空结果时统计数据结构正确")

        except Exception as e:
            return format_test_result(False, "空结果统计数据", f"测试失败: {str(e)}")

    def cleanup_test_data(self):
        """清理测试数据"""
        print("\n=== 清理测试数据 ===")

        for ck_id in self.test_ck_ids:
            try:
                response = self.delete(f"/walmart-cks/{ck_id}", token=self.admin_token)
                if response and response.get("success"):
                    print(f"✓ 删除CK {ck_id}")
                else:
                    print(f"✗ 删除CK {ck_id} 失败: {response}")
            except Exception as e:
                print(f"✗ 删除CK {ck_id} 异常: {str(e)}")

    def run_all_tests(self):
        """运行所有测试"""
        print("=" * 60)
        print("CK列表集成API测试套件")
        print("=" * 60)

        try:
            # 设置测试环境
            self.setup_test_environment()

            # 运行测试
            self.results.append(self.test_list_with_statistics_basic())
            self.results.append(self.test_time_filter_consistency())
            self.results.append(self.test_data_isolation_merchant_admin())
            self.results.append(self.test_empty_result_statistics())

        except Exception as e:
            print(f"✗ 测试环境设置失败: {str(e)}")
            return

        finally:
            # 清理测试数据
            self.cleanup_test_data()

        # 打印测试结果
        print_test_summary(self.results, "CK列表集成API测试")


if __name__ == "__main__":
    test_suite = IntegratedCKListAPITestSuite()
    test_suite.run_all_tests()
