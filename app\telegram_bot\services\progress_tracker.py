"""
Telegram机器人进度跟踪服务
提供用户操作进度跟踪和状态通知功能
"""

import asyncio
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from enum import Enum

from sqlalchemy.orm import Session
from telegram import Bot
from telegram.error import TelegramError


class ProgressStatus(Enum):
    """进度状态枚举"""
    PENDING = "pending"           # 等待中
    IN_PROGRESS = "in_progress"   # 进行中
    COMPLETED = "completed"       # 已完成
    FAILED = "failed"            # 失败
    CANCELLED = "cancelled"       # 已取消


class ProgressType(Enum):
    """进度类型枚举"""
    USER_VERIFICATION = "user_verification"     # 用户验证
    GROUP_BINDING = "group_binding"             # 群组绑定
    PERMISSION_UPDATE = "permission_update"     # 权限更新
    DATA_QUERY = "data_query"                   # 数据查询


class ProgressTracker:
    """进度跟踪器"""
    
    def __init__(self, bot: Bot, db: Session):
        self.bot = bot
        self.db = db
        self.logger = logging.getLogger(__name__)
        self._progress_cache: Dict[str, Dict[str, Any]] = {}
        
    async def create_progress(
        self,
        user_id: int,
        progress_type: ProgressType,
        title: str,
        description: str = "",
        estimated_duration: Optional[int] = None
    ) -> str:
        """
        创建新的进度跟踪
        
        Args:
            user_id: 用户ID
            progress_type: 进度类型
            title: 进度标题
            description: 进度描述
            estimated_duration: 预估完成时间（分钟）
            
        Returns:
            str: 进度ID
        """
        progress_id = f"{progress_type.value}_{user_id}_{int(datetime.now().timestamp())}"
        
        progress_data = {
            "id": progress_id,
            "user_id": user_id,
            "type": progress_type,
            "title": title,
            "description": description,
            "status": ProgressStatus.PENDING,
            "created_at": datetime.now(),
            "updated_at": datetime.now(),
            "estimated_duration": estimated_duration,
            "steps": [],
            "current_step": 0,
            "total_steps": 0
        }
        
        self._progress_cache[progress_id] = progress_data
        
        # 发送初始通知
        await self._send_progress_notification(progress_id, "创建了新的任务")

        return progress_id

    async def create_progress_silent(
        self,
        user_id: int,
        progress_type: ProgressType,
        title: str,
        description: str = "",
        estimated_duration: Optional[int] = None
    ) -> str:
        """
        创建新的进度跟踪（静默模式，不发送通知）

        Args:
            user_id: 用户ID
            progress_type: 进度类型
            title: 进度标题
            description: 进度描述
            estimated_duration: 预估完成时间（分钟）

        Returns:
            str: 进度ID
        """
        progress_id = f"{progress_type.value}_{user_id}_{int(datetime.now().timestamp())}"

        progress_data = {
            "id": progress_id,
            "user_id": user_id,
            "type": progress_type,
            "title": title,
            "description": description,
            "status": ProgressStatus.PENDING,
            "created_at": datetime.now(),
            "updated_at": datetime.now(),
            "estimated_duration": estimated_duration,
            "steps": [],
            "current_step": 0,
            "total_steps": 0,
            "silent_mode": True  # 标记为静默模式
        }

        self._progress_cache[progress_id] = progress_data

        # 静默模式不发送通知
        self.logger.info(f"创建静默进度跟踪: {progress_id} for user {user_id}")

        return progress_id
    
    async def add_step(self, progress_id: str, step_title: str, step_description: str = ""):
        """添加进度步骤"""
        if progress_id not in self._progress_cache:
            return
            
        progress = self._progress_cache[progress_id]
        progress["steps"].append({
            "title": step_title,
            "description": step_description,
            "status": ProgressStatus.PENDING,
            "started_at": None,
            "completed_at": None
        })
        progress["total_steps"] = len(progress["steps"])
        progress["updated_at"] = datetime.now()

    async def add_step_silent(self, progress_id: str, step_title: str, step_description: str = ""):
        """添加进度步骤（静默模式）"""
        if progress_id not in self._progress_cache:
            return

        progress = self._progress_cache[progress_id]
        progress["steps"].append({
            "title": step_title,
            "description": step_description,
            "status": ProgressStatus.PENDING,
            "started_at": None,
            "completed_at": None
        })
        progress["total_steps"] = len(progress["steps"])
        progress["updated_at"] = datetime.now()
        # 静默模式不发送通知
    
    async def start_step(self, progress_id: str, step_index: int):
        """开始执行指定步骤"""
        if progress_id not in self._progress_cache:
            return
            
        progress = self._progress_cache[progress_id]
        if step_index >= len(progress["steps"]):
            return
            
        # 更新步骤状态
        progress["steps"][step_index]["status"] = ProgressStatus.IN_PROGRESS
        progress["steps"][step_index]["started_at"] = datetime.now()
        progress["current_step"] = step_index
        progress["status"] = ProgressStatus.IN_PROGRESS
        progress["updated_at"] = datetime.now()
        
        # 发送进度通知
        step_title = progress["steps"][step_index]["title"]
        await self._send_progress_notification(
            progress_id,
            f"正在执行：{step_title}"
        )

    async def start_step_silent(self, progress_id: str, step_index: int):
        """开始执行指定步骤（静默模式）"""
        if progress_id not in self._progress_cache:
            return

        progress = self._progress_cache[progress_id]
        if step_index >= len(progress["steps"]):
            return

        # 更新步骤状态
        progress["steps"][step_index]["status"] = ProgressStatus.IN_PROGRESS
        progress["steps"][step_index]["started_at"] = datetime.now()
        progress["current_step"] = step_index
        progress["status"] = ProgressStatus.IN_PROGRESS
        progress["updated_at"] = datetime.now()

        # 静默模式不发送通知
        step_title = progress["steps"][step_index]["title"]
        self.logger.info(f"静默模式开始步骤: {step_title} (进度ID: {progress_id})")
    
    async def complete_step(self, progress_id: str, step_index: int, result: str = ""):
        """完成指定步骤"""
        if progress_id not in self._progress_cache:
            return
            
        progress = self._progress_cache[progress_id]
        if step_index >= len(progress["steps"]):
            return
            
        # 更新步骤状态
        progress["steps"][step_index]["status"] = ProgressStatus.COMPLETED
        progress["steps"][step_index]["completed_at"] = datetime.now()
        progress["steps"][step_index]["result"] = result
        progress["updated_at"] = datetime.now()
        
        # 检查是否所有步骤都完成
        completed_steps = sum(1 for step in progress["steps"] 
                            if step["status"] == ProgressStatus.COMPLETED)
        
        if completed_steps == progress["total_steps"]:
            progress["status"] = ProgressStatus.COMPLETED
            await self._send_completion_notification(progress_id)
        else:
            step_title = progress["steps"][step_index]["title"]
            await self._send_progress_notification(
                progress_id,
                f"已完成：{step_title}"
            )

    async def complete_step_silent(self, progress_id: str, step_index: int, result_message: str = ""):
        """完成指定步骤（静默模式）"""
        if progress_id not in self._progress_cache:
            return

        progress = self._progress_cache[progress_id]
        if step_index >= len(progress["steps"]):
            return

        # 更新步骤状态
        progress["steps"][step_index]["status"] = ProgressStatus.COMPLETED
        progress["steps"][step_index]["completed_at"] = datetime.now()
        progress["steps"][step_index]["result"] = result_message
        progress["updated_at"] = datetime.now()

        # 检查是否所有步骤都完成了
        completed_steps = sum(1 for step in progress["steps"] if step["status"] == ProgressStatus.COMPLETED)

        if completed_steps == progress["total_steps"]:
            progress["status"] = ProgressStatus.COMPLETED
            # 静默模式不发送完成通知
            self.logger.info(f"静默模式任务完成: {progress['title']} (进度ID: {progress_id})")
        else:
            step_title = progress["steps"][step_index]["title"]
            # 静默模式不发送进度通知
            self.logger.info(f"静默模式步骤完成: {step_title} (进度ID: {progress_id})")
    
    async def fail_progress(self, progress_id: str, error_message: str):
        """标记进度失败"""
        if progress_id not in self._progress_cache:
            return
            
        progress = self._progress_cache[progress_id]
        progress["status"] = ProgressStatus.FAILED
        progress["error_message"] = error_message
        progress["updated_at"] = datetime.now()
        
        await self._send_error_notification(progress_id, error_message)
    
    async def get_progress(self, progress_id: str) -> Optional[Dict[str, Any]]:
        """获取进度信息"""
        return self._progress_cache.get(progress_id)
    
    async def get_user_progress(self, user_id: int) -> List[Dict[str, Any]]:
        """获取用户的所有进度"""
        user_progress = []
        for progress in self._progress_cache.values():
            if progress["user_id"] == user_id:
                user_progress.append(progress)
        
        # 按创建时间排序
        user_progress.sort(key=lambda x: x["created_at"], reverse=True)
        return user_progress
    
    async def _send_progress_notification(self, progress_id: str, message: str):
        """发送进度通知"""
        try:
            progress = self._progress_cache.get(progress_id)
            if not progress:
                return

            # 检查是否为静默模式
            if progress.get("silent_mode", False):
                return

            user_id = progress["user_id"]
            title = progress["title"]
            
            notification_text = f"""📋 **任务进度更新**

🎯 **任务**：{title}
📝 **状态**：{message}

💡 输入 `/progress {progress_id}` 查看详细进度"""
            
            await self.bot.send_message(
                chat_id=user_id,
                text=notification_text,
                parse_mode='Markdown'
            )
            
        except TelegramError as e:
            # 如果是机器人无法主动发起对话的错误，记录为警告而不是错误
            if "bot can't initiate conversation with a user" in str(e):
                self.logger.warning(f"无法向用户 {user_id} 发送进度通知：用户尚未与机器人建立对话")
            else:
                self.logger.error(f"发送进度通知失败: {e}")
    
    async def _send_completion_notification(self, progress_id: str):
        """发送完成通知"""
        try:
            progress = self._progress_cache.get(progress_id)
            if not progress:
                return

            # 检查是否为静默模式
            if progress.get("silent_mode", False):
                return

            user_id = progress["user_id"]
            title = progress["title"]
            
            notification_text = f"""✅ **任务已完成**

🎯 **任务**：{title}
⏰ **完成时间**：{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

🎉 恭喜！您的任务已成功完成。"""
            
            await self.bot.send_message(
                chat_id=user_id,
                text=notification_text,
                parse_mode='Markdown'
            )
            
        except TelegramError as e:
            # 如果是机器人无法主动发起对话的错误，记录为警告而不是错误
            if "bot can't initiate conversation with a user" in str(e):
                self.logger.warning(f"无法向用户发送完成通知：用户尚未与机器人建立对话")
            else:
                self.logger.error(f"发送完成通知失败: {e}")
    
    async def _send_error_notification(self, progress_id: str, error_message: str):
        """发送错误通知"""
        try:
            progress = self._progress_cache.get(progress_id)
            if not progress:
                return

            # 检查是否为静默模式
            if progress.get("silent_mode", False):
                return

            user_id = progress["user_id"]
            title = progress["title"]
            
            notification_text = f"""❌ **任务执行失败**

🎯 **任务**：{title}
❗ **错误信息**：{error_message}

💡 **建议**：
• 请检查操作是否正确
• 如需帮助，请联系管理员
• 输入 `/help` 获取更多帮助"""
            
            await self.bot.send_message(
                chat_id=user_id,
                text=notification_text,
                parse_mode='Markdown'
            )
            
        except TelegramError as e:
            # 如果是机器人无法主动发起对话的错误，记录为警告而不是错误
            if "bot can't initiate conversation with a user" in str(e):
                self.logger.warning(f"无法向用户发送错误通知：用户尚未与机器人建立对话")
            else:
                self.logger.error(f"发送错误通知失败: {e}")
    
    async def cleanup_old_progress(self, days: int = 7):
        """清理旧的进度记录"""
        cutoff_time = datetime.now() - timedelta(days=days)
        
        to_remove = []
        for progress_id, progress in self._progress_cache.items():
            if progress["created_at"] < cutoff_time:
                to_remove.append(progress_id)
        
        for progress_id in to_remove:
            del self._progress_cache[progress_id]
        
        self.logger.info(f"清理了 {len(to_remove)} 个过期的进度记录")


# 创建全局进度跟踪器实例
progress_tracker = None

def get_progress_tracker(bot: Bot, db: Session) -> ProgressTracker:
    """获取进度跟踪器实例"""
    global progress_tracker
    if progress_tracker is None:
        progress_tracker = ProgressTracker(bot, db)
    return progress_tracker
