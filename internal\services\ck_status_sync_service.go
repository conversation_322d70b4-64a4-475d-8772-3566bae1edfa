package services

import (
	"context"
	"encoding/json"
	"fmt"
	"math/rand"
	"strings"
	"sync"
	"time"

	"walmart-bind-card-processor/internal/config"
	"walmart-bind-card-processor/internal/model"

	"github.com/go-redis/redis/v8"
	"go.uber.org/zap"
	"gorm.io/gorm"
)

// CKStatusEvent CK状态变更事件
type CKStatusEvent struct {
	CKID      uint      `json:"ck_id"`
	OldStatus string    `json:"old_status"`
	NewStatus string    `json:"new_status"`
	Reason    string    `json:"reason"`
	Timestamp time.Time `json:"timestamp"`
	TraceID   string    `json:"trace_id,omitempty"`
}

// CKStatusSyncService CK状态实时同步服务
type CKStatusSyncService struct {
	db     *gorm.DB
	redis  *redis.Client
	logger *zap.Logger
	
	// 状态缓存
	statusCache map[uint]*CKStatus
	cacheMutex  sync.RWMutex
	
	// 事件通道
	event<PERSON>han chan *CKStatusEvent
	
	// 配置
	config struct {
		CacheExpiry       time.Duration
		EventBufferSize   int
		SyncInterval      time.Duration
		FailureThreshold  int
		RecoveryInterval  time.Duration
	}
	
	// 运行状态
	running bool
	stopCh  chan struct{}
	wg      sync.WaitGroup
}

// CKStatus CK状态信息
type CKStatus struct {
	CKID            uint      `json:"ck_id"`
	Active          bool      `json:"active"`
	BindCount       int       `json:"bind_count"`
	TotalLimit      int       `json:"total_limit"`
	FailureCount    int       `json:"failure_count"`
	LastFailureTime time.Time `json:"last_failure_time"`
	LastUpdated     time.Time `json:"last_updated"`
	Version         int64     `json:"version"` // 乐观锁版本号
}

// NewCKStatusSyncService 创建CK状态同步服务
func NewCKStatusSyncService(db *gorm.DB, redis *redis.Client, logger *zap.Logger, config *config.StatusSyncConfig) *CKStatusSyncService {
	service := &CKStatusSyncService{
		db:          db,
		redis:       redis,
		logger:      logger,
		statusCache: make(map[uint]*CKStatus),
		eventChan:   make(chan *CKStatusEvent, config.EventBufferSize),
		stopCh:      make(chan struct{}),
	}

	// 使用配置文件中的值
	service.config.CacheExpiry = config.CacheExpiry
	service.config.EventBufferSize = config.EventBufferSize
	service.config.SyncInterval = config.SyncInterval
	service.config.FailureThreshold = config.FailureThreshold
	service.config.RecoveryInterval = config.RecoveryInterval

	return service
}

// Start 启动同步服务
func (s *CKStatusSyncService) Start(ctx context.Context) error {
	if s.running {
		return fmt.Errorf("CK状态同步服务已在运行")
	}
	
	s.running = true
	
	// 初始化缓存
	if err := s.initializeCache(ctx); err != nil {
		return fmt.Errorf("初始化CK状态缓存失败: %w", err)
	}
	
	// 启动事件处理协程
	s.wg.Add(1)
	go s.eventProcessor(ctx)
	
	// 启动定期同步协程
	s.wg.Add(1)
	go s.periodicSync(ctx)
	
	// 启动Redis订阅协程
	s.wg.Add(1)
	go s.redisSubscriber(ctx)
	
	s.logger.Info("CK状态同步服务启动成功")
	return nil
}

// Stop 停止同步服务
func (s *CKStatusSyncService) Stop() {
	if !s.running {
		return
	}
	
	s.running = false
	close(s.stopCh)
	s.wg.Wait()
	
	s.logger.Info("CK状态同步服务已停止")
}

// GetCKStatus 获取CK状态（实时查询，确保CK状态最新）
func (s *CKStatusSyncService) GetCKStatus(ctx context.Context, ckID uint) (*CKStatus, error) {
	// 🔧 关键修复：移除缓存，直接从数据库实时查询
	// 确保用户添加CK后状态立即可见
	return s.refreshCKStatus(ctx, ckID)
}

// UpdateCKStatus 更新CK状态
func (s *CKStatusSyncService) UpdateCKStatus(ctx context.Context, ckID uint, updates map[string]interface{}, reason string) error {
	// 获取当前状态
	currentStatus, err := s.GetCKStatus(ctx, ckID)
	if err != nil {
		return fmt.Errorf("获取当前CK状态失败: %w", err)
	}
	
	oldStatus := "active"
	if !currentStatus.Active {
		oldStatus = "inactive"
	}
	
	// 合并所有更新字段
	allUpdates := map[string]interface{}{
		"version":    currentStatus.Version + 1,
		"updated_at": time.Now(),
	}

	// 合并用户指定的更新字段
	for key, value := range updates {
		allUpdates[key] = value
	}

	// 使用乐观锁一次性更新所有字段
	result := s.db.WithContext(ctx).Model(&model.WalmartCK{}).
		Where("id = ? AND version = ?", ckID, currentStatus.Version).
		Updates(allUpdates)
	
	if result.Error != nil {
		return fmt.Errorf("更新CK状态失败: %w", result.Error)
	}
	
	if result.RowsAffected == 0 {
		return fmt.Errorf("CK状态更新冲突，请重试")
	}
	
	// 确定新状态
	newStatus := oldStatus
	if activeVal, ok := updates["active"]; ok {
		if active, ok := activeVal.(bool); ok {
			if active {
				newStatus = "active"
			} else {
				newStatus = "inactive"
			}
		}
	}
	
	// 发送状态变更事件
	event := &CKStatusEvent{
		CKID:      ckID,
		OldStatus: oldStatus,
		NewStatus: newStatus,
		Reason:    reason,
		Timestamp: time.Now(),
	}
	
	select {
	case s.eventChan <- event:
	default:
		s.logger.Warn("事件通道已满，丢弃CK状态变更事件", zap.Uint("ck_id", ckID))
	}
	
	// 刷新缓存
	_, err = s.refreshCKStatus(ctx, ckID)
	return err
}

// DisableCK 禁用CK
func (s *CKStatusSyncService) DisableCK(ctx context.Context, ckID uint, reason string) error {
	updates := map[string]interface{}{
		"active": false,
	}
	return s.UpdateCKStatus(ctx, ckID, updates, reason)
}

// DisableCKWithRetry 禁用CK（带内置重试机制）
func (s *CKStatusSyncService) DisableCKWithRetry(ctx context.Context, ckID uint, reason string, maxRetries int) error {
	if maxRetries <= 0 {
		maxRetries = 5
	}

	baseDelay := 50 * time.Millisecond

	for attempt := 0; attempt < maxRetries; attempt++ {
		err := s.DisableCK(ctx, ckID, reason)
		if err == nil {
			s.logger.Info("CK禁用成功",
				zap.Uint("ck_id", ckID),
				zap.Int("attempt", attempt+1))
			return nil
		}

		// 检查是否是版本冲突错误
		if strings.Contains(err.Error(), "CK状态更新冲突") && attempt < maxRetries-1 {
			// 随机化的指数退避
			jitter := time.Duration(rand.Intn(int(baseDelay.Milliseconds()/2))) * time.Millisecond
			delay := baseDelay*time.Duration(1<<attempt) + jitter

			s.logger.Warn("CK禁用冲突，准备重试",
				zap.Uint("ck_id", ckID),
				zap.Int("attempt", attempt+1),
				zap.Duration("delay", delay),
				zap.Error(err))

			select {
			case <-ctx.Done():
				return ctx.Err()
			case <-time.After(delay):
				continue
			}
		}

		// 非冲突错误或最后一次重试失败
		s.logger.Error("CK禁用失败",
			zap.Uint("ck_id", ckID),
			zap.Int("attempt", attempt+1),
			zap.Error(err))
		return err
	}

	return fmt.Errorf("禁用CK重试%d次后仍失败", maxRetries)
}

// EnableCK 启用CK
func (s *CKStatusSyncService) EnableCK(ctx context.Context, ckID uint, reason string) error {
	updates := map[string]interface{}{
		"active": true,
	}
	return s.UpdateCKStatus(ctx, ckID, updates, reason)
}

// IncrementBindCount 增加绑卡计数
func (s *CKStatusSyncService) IncrementBindCount(ctx context.Context, ckID uint, amount int) error {
	// 使用Redis原子操作增加计数
	key := fmt.Sprintf("ck:bind_count:%d", ckID)
	newCount, err := s.redis.IncrBy(ctx, key, int64(amount)).Result()
	if err != nil {
		return fmt.Errorf("redis增加绑卡计数失败: %w", err)
	}
	
	// 设置过期时间
	s.redis.Expire(ctx, key, time.Hour)
	
	// 异步更新数据库
	go func() {
		ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
		defer cancel()
		
		updates := map[string]interface{}{
			"bind_count": newCount,
		}
		if err := s.UpdateCKStatus(ctx, ckID, updates, fmt.Sprintf("绑卡计数增加%d", amount)); err != nil {
			s.logger.Error("异步更新绑卡计数失败", zap.Error(err))
		}
	}()
	
	return nil
}

// IsAvailable 检查CK是否可用
func (s *CKStatusSyncService) IsAvailable(ctx context.Context, ckID uint) (bool, error) {
	status, err := s.GetCKStatus(ctx, ckID)
	if err != nil {
		return false, err
	}
	
	// 检查基本可用性
	if !status.Active {
		return false, nil
	}
	
	// 检查绑卡限制
	if status.BindCount >= status.TotalLimit {
		return false, nil
	}
	
	// 检查故障阈值
	if status.FailureCount >= s.config.FailureThreshold {
		if time.Since(status.LastFailureTime) < s.config.RecoveryInterval {
			return false, nil
		}
	}
	
	return true, nil
}

// initializeCache 初始化状态缓存
func (s *CKStatusSyncService) initializeCache(ctx context.Context) error {
	var cks []model.WalmartCK
	// 🔧 关键修复：使用原生SQL查询，完全避免GORM可能的自动ORDER BY
	err := s.db.WithContext(ctx).Raw("SELECT * FROM walmart_ck WHERE is_deleted = ?", false).Scan(&cks).Error
	if err != nil {
		return fmt.Errorf("查询CK列表失败: %w", err)
	}
	
	s.cacheMutex.Lock()
	defer s.cacheMutex.Unlock()
	
	for _, ck := range cks {
		s.statusCache[ck.ID] = &CKStatus{
			CKID:        ck.ID,
			Active:      ck.Active,
			BindCount:   ck.BindCount,
			TotalLimit:  ck.TotalLimit,
			LastUpdated: time.Now(),
			Version:     ck.Version,
		}
	}
	
	s.logger.Info("CK状态缓存初始化完成", zap.Int("count", len(cks)))
	return nil
}

// refreshCKStatus 实时查询CK状态（无缓存）
func (s *CKStatusSyncService) refreshCKStatus(ctx context.Context, ckID uint) (*CKStatus, error) {
	var ck model.WalmartCK
	// 🔧 关键修复：使用原生SQL查询，完全避免GORM可能的自动ORDER BY
	err := s.db.WithContext(ctx).Raw("SELECT * FROM walmart_ck WHERE id = ? LIMIT 1", ckID).Scan(&ck).Error
	if err != nil {
		return nil, fmt.Errorf("查询CK信息失败: %w", err)
	}

	status := &CKStatus{
		CKID:        ck.ID,
		Active:      ck.Active,
		BindCount:   ck.BindCount,
		TotalLimit:  ck.TotalLimit,
		LastUpdated: time.Now(),
		Version:     ck.Version,
	}

	// 🔧 关键修复：移除缓存更新逻辑
	// 直接返回实时查询的状态，确保数据最新

	return status, nil
}

// eventProcessor 事件处理协程
func (s *CKStatusSyncService) eventProcessor(ctx context.Context) {
	defer s.wg.Done()

	for {
		select {
		case <-s.stopCh:
			return
		case event := <-s.eventChan:
			s.processStatusEvent(ctx, event)
		}
	}
}

// processStatusEvent 处理状态变更事件
func (s *CKStatusSyncService) processStatusEvent(ctx context.Context, event *CKStatusEvent) {
	// 记录到日志
	s.logger.Info("处理CK状态变更事件",
		zap.Uint("ck_id", event.CKID),
		zap.String("old_status", event.OldStatus),
		zap.String("new_status", event.NewStatus),
		zap.String("reason", event.Reason))

	// 发布到Redis
	eventData, _ := json.Marshal(event)
	channel := fmt.Sprintf("ck_status_change:%d", event.CKID)
	s.redis.Publish(ctx, channel, eventData)

	// 广播到所有实例
	s.redis.Publish(ctx, "ck_status_broadcast", eventData)
}

// periodicSync 定期同步协程
func (s *CKStatusSyncService) periodicSync(ctx context.Context) {
	defer s.wg.Done()

	ticker := time.NewTicker(s.config.SyncInterval)
	defer ticker.Stop()

	for {
		select {
		case <-s.stopCh:
			return
		case <-ticker.C:
			s.syncAllCKStatus(ctx)
		}
	}
}

// syncAllCKStatus 同步所有CK状态
func (s *CKStatusSyncService) syncAllCKStatus(ctx context.Context) {
	s.cacheMutex.RLock()
	ckIDs := make([]uint, 0, len(s.statusCache))
	for ckID := range s.statusCache {
		ckIDs = append(ckIDs, ckID)
	}
	s.cacheMutex.RUnlock()

	for _, ckID := range ckIDs {
		if _, err := s.refreshCKStatus(ctx, ckID); err != nil {
			s.logger.Error("同步CK状态失败", zap.Uint("ck_id", ckID), zap.Error(err))
		}
	}
}

// redisSubscriber Redis订阅协程
func (s *CKStatusSyncService) redisSubscriber(ctx context.Context) {
	defer s.wg.Done()

	pubsub := s.redis.Subscribe(ctx, "ck_status_broadcast")
	defer pubsub.Close()

	ch := pubsub.Channel()

	for {
		select {
		case <-s.stopCh:
			return
		case msg := <-ch:
			var event CKStatusEvent
			if err := json.Unmarshal([]byte(msg.Payload), &event); err != nil {
				s.logger.Error("解析CK状态事件失败", zap.Error(err))
				continue
			}

			// 刷新本地缓存
			s.refreshCKStatus(ctx, event.CKID)
		}
	}
}
