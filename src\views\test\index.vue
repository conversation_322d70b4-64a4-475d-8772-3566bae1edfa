<template>
  <div class="test-index-container">
    <div class="test-header">
      <h1>🧪 测试工具集合</h1>
      <p>系统功能测试和调试工具</p>
    </div>

    <div class="test-grid">
      <!-- 绑卡时间线测试 -->
      <el-card class="test-card" shadow="hover" @click="goToTimelineTest">
        <div class="test-card-content">
          <div class="test-icon">
            <el-icon size="48" color="#409eff">
              <TrendCharts />
            </el-icon>
          </div>
          <h3>绑卡时间线测试</h3>
          <p>测试新的绑卡执行时间线功能，查看每个步骤的详细执行时间</p>
          <div class="test-features">
            <el-tag size="small" type="info">步骤时间线</el-tag>
            <el-tag size="small" type="success">性能分析</el-tag>
            <el-tag size="small" type="warning">API测试</el-tag>
          </div>
        </div>
      </el-card>

      <!-- 并发绑卡测试 -->
      <el-card class="test-card" shadow="hover" @click="goToConcurrentTest">
        <div class="test-card-content">
          <div class="test-icon">
            <el-icon size="48" color="#67c23a">
              <Connection />
            </el-icon>
          </div>
          <h3>并发绑卡测试</h3>
          <p>测试系统在高并发情况下的绑卡处理能力</p>
          <div class="test-features">
            <el-tag size="small" type="success">并发测试</el-tag>
            <el-tag size="small" type="info">压力测试</el-tag>
          </div>
        </div>
      </el-card>

      <!-- 权限测试 -->
      <el-card class="test-card" shadow="hover" @click="goToPermissionTest">
        <div class="test-card-content">
          <div class="test-icon">
            <el-icon size="48" color="#e6a23c">
              <Lock />
            </el-icon>
          </div>
          <h3>权限测试</h3>
          <p>测试系统的权限控制和数据隔离功能</p>
          <div class="test-features">
            <el-tag size="small" type="warning">权限控制</el-tag>
            <el-tag size="small" type="info">数据隔离</el-tag>
          </div>
        </div>
      </el-card>

      <!-- API测试工具 -->
      <el-card class="test-card" shadow="hover" @click="goToApiTest">
        <div class="test-card-content">
          <div class="test-icon">
            <el-icon size="48" color="#f56c6c">
              <Tools />
            </el-icon>
          </div>
          <h3>API测试工具</h3>
          <p>通用的API接口测试工具，支持各种HTTP请求</p>
          <div class="test-features">
            <el-tag size="small" type="danger">API测试</el-tag>
            <el-tag size="small" type="info">HTTP请求</el-tag>
          </div>
        </div>
      </el-card>

      <!-- 对账台测试 -->
      <el-card class="test-card" shadow="hover" @click="goToReconciliationTest">
        <div class="test-card-content">
          <div class="test-icon">
            <el-icon size="48" color="#909399">
              <Money />
            </el-icon>
          </div>
          <h3>对账台测试</h3>
          <p>测试对账台的各种功能模块</p>
          <div class="test-features">
            <el-tag size="small" type="info">对账功能</el-tag>
            <el-tag size="small" type="success">数据统计</el-tag>
          </div>
        </div>
      </el-card>
    </div>

    <!-- 使用说明 -->
    <el-card class="usage-card">
      <template #header>
        <span>使用说明</span>
      </template>
      <div class="usage-content">
        <h4>测试工具说明：</h4>
        <ul>
          <li><strong>绑卡时间线测试</strong>：新功能测试，可以查看绑卡过程中每个步骤的详细执行时间和性能分析</li>
          <li><strong>并发绑卡测试</strong>：测试系统在高并发情况下的处理能力和稳定性</li>
          <li><strong>权限测试</strong>：验证系统的权限控制机制是否正常工作</li>
          <li><strong>API测试工具</strong>：通用的API接口测试工具，可以测试各种HTTP请求</li>
          <li><strong>对账台测试</strong>：测试对账台的各种功能模块</li>
        </ul>
        
        <h4>注意事项：</h4>
        <ul>
          <li>测试工具仅供开发和调试使用，请勿在生产环境中进行大量测试</li>
          <li>某些测试可能会产生实际的数据变更，请谨慎操作</li>
          <li>如果遇到问题，请联系技术支持</li>
        </ul>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { useRouter } from 'vue-router'
import { 
  TrendCharts, 
  Connection, 
  Lock, 
  Tools, 
  Money 
} from '@element-plus/icons-vue'

const router = useRouter()

// 导航方法
const goToTimelineTest = () => {
  router.push('/test/timeline')
}

const goToConcurrentTest = () => {
  router.push('/test/concurrent-binding')
}

const goToPermissionTest = () => {
  router.push('/test/permission')
}

const goToApiTest = () => {
  router.push('/tools/api-test')
}

const goToReconciliationTest = () => {
  router.push('/reconciliation-test')
}
</script>

<style scoped>
.test-index-container {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.test-header {
  text-align: center;
  margin-bottom: 40px;
}

.test-header h1 {
  color: #303133;
  margin-bottom: 8px;
  font-size: 32px;
}

.test-header p {
  color: #606266;
  font-size: 18px;
}

.test-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.test-card {
  cursor: pointer;
  transition: all 0.3s ease;
  border-radius: 12px;
}

.test-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.test-card-content {
  text-align: center;
  padding: 20px;
}

.test-icon {
  margin-bottom: 16px;
}

.test-card h3 {
  color: #303133;
  margin: 0 0 12px 0;
  font-size: 20px;
  font-weight: 600;
}

.test-card p {
  color: #606266;
  margin: 0 0 16px 0;
  line-height: 1.6;
  font-size: 14px;
}

.test-features {
  display: flex;
  justify-content: center;
  gap: 8px;
  flex-wrap: wrap;
}

.usage-card {
  border-radius: 12px;
}

.usage-content h4 {
  color: #303133;
  margin: 16px 0 8px 0;
  font-size: 16px;
  font-weight: 600;
}

.usage-content ul {
  margin: 8px 0 16px 0;
  padding-left: 20px;
}

.usage-content li {
  margin-bottom: 8px;
  color: #606266;
  line-height: 1.6;
}

.usage-content strong {
  color: #303133;
  font-weight: 600;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .test-grid {
    grid-template-columns: 1fr;
  }
  
  .test-header h1 {
    font-size: 24px;
  }
  
  .test-header p {
    font-size: 16px;
  }
}
</style>
