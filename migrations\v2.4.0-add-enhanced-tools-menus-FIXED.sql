-- ========================================
-- v2.4.0 - 添加增强工具菜单和权限配置 (修复版本)
-- 修复重复执行问题，添加清理逻辑
-- ========================================

-- 设置连接字符集
SET NAMES utf8mb4 COLLATE utf8mb4_unicode_ci;
SET time_zone = '+08:00';
USE `walmart_card_db`;

-- =====================================================
-- 1. 清理可能存在的重复数据
-- =====================================================

-- 清理迁移日志
DELETE FROM `migration_logs` WHERE `migration_name` = 'enhanced_tools_menus_v2.4.0';

-- 清理可能已存在的菜单和权限（防止重复执行错误）
DELETE FROM `role_menus` WHERE `menu_id` IN (
    SELECT id FROM `menus` WHERE code LIKE 'tools%'
);

DELETE FROM `role_permissions` WHERE `permission_id` IN (
    SELECT id FROM `permissions` WHERE code LIKE '%tools%'
);

DELETE FROM `permissions` WHERE code LIKE '%tools%';
DELETE FROM `menus` WHERE code LIKE 'tools%';

-- =====================================================
-- 2. 记录迁移开始
-- =====================================================

INSERT INTO `migration_logs` (
    `migration_name`, 
    `status`, 
    `message`, 
    `data_summary`, 
    `created_at`
) VALUES (
    'enhanced_tools_menus_v2.4.0',
    'started',
    '开始添加增强工具菜单和权限配置 (修复版本)',
    '{"version": "v2.4.0", "description": "添加6个新功能菜单"}',
    NOW(3)
);

-- =====================================================
-- 3. 添加工具管理主菜单
-- =====================================================

INSERT INTO `menus` (
    `name`, `code`, `path`, `component`, `icon`, `parent_id`,
    `level`, `sort_order`, `is_visible`, `is_enabled`, `menu_type`,
    `description`, `created_at`, `updated_at`
) VALUES (
    '工具管理', 'tools', '/tools', 'Layout', 'tools', NULL, 
    1, 10, 1, 1, 'menu', 
    '系统工具管理模块', 
    NOW(3), NOW(3)
);

-- 获取工具管理菜单ID
SET @tools_menu_id = (SELECT id FROM `menus` WHERE code = 'tools' LIMIT 1);

-- =====================================================
-- 4. 添加工具子菜单
-- =====================================================

INSERT INTO `menus` (
    `name`, `code`, `path`, `component`, `icon`, `parent_id`,
    `level`, `sort_order`, `is_visible`, `is_enabled`, `menu_type`,
    `description`, `created_at`, `updated_at`
) VALUES
('API测试工具', 'tools:api-test', '/tools/api-test', 'ApiTest', 'connection', @tools_menu_id, 2, 1, 1, 1, 'menu', 'API接口测试工具', NOW(3), NOW(3)),
('系统监控', 'tools:monitoring', '/tools/monitoring', 'SystemMonitoring', 'monitor', @tools_menu_id, 2, 2, 1, 1, 'menu', '系统监控仪表盘', NOW(3), NOW(3)),
('增强安全审计', 'tools:enhanced-audit', '/tools/enhanced-audit', 'EnhancedAudit', 'security-scan', @tools_menu_id, 2, 3, 1, 1, 'menu', '增强安全审计功能', NOW(3), NOW(3)),
('异常行为检测', 'tools:anomaly-detection', '/tools/anomaly-detection', 'AnomalyDetection', 'warning', @tools_menu_id, 2, 4, 1, 1, 'menu', '异常行为检测系统', NOW(3), NOW(3)),
('高级数据分析', 'tools:advanced-analytics', '/tools/advanced-analytics', 'AdvancedAnalytics', 'data-analysis', @tools_menu_id, 2, 5, 1, 1, 'menu', '高级数据分析功能', NOW(3), NOW(3)),
('批量数据操作', 'tools:batch-operations', '/tools/batch-operations', 'BatchOperations', 'upload', @tools_menu_id, 2, 6, 1, 1, 'menu', '批量数据操作工具', NOW(3), NOW(3));

-- =====================================================
-- 5. 添加权限配置
-- =====================================================

INSERT INTO `permissions` (
    `code`, `name`, `description`, `resource_type`, `resource_path`, 
    `is_enabled`, `sort_order`, `created_at`, `updated_at`
) VALUES
-- 菜单权限
('menu:tools', '工具管理菜单', '工具管理主菜单访问权限', 'menu', '/tools', 1, 100, NOW(3), NOW(3)),
('menu:tools:api-test', 'API测试工具菜单', 'API测试工具菜单访问权限', 'menu', '/tools/api-test', 1, 101, NOW(3), NOW(3)),
('menu:tools:monitoring', '系统监控菜单', '系统监控菜单访问权限', 'menu', '/tools/monitoring', 1, 102, NOW(3), NOW(3)),
('menu:tools:enhanced-audit', '增强安全审计菜单', '增强安全审计菜单访问权限', 'menu', '/tools/enhanced-audit', 1, 103, NOW(3), NOW(3)),
('menu:tools:anomaly-detection', '异常行为检测菜单', '异常行为检测菜单访问权限', 'menu', '/tools/anomaly-detection', 1, 104, NOW(3), NOW(3)),
('menu:tools:advanced-analytics', '高级数据分析菜单', '高级数据分析菜单访问权限', 'menu', '/tools/advanced-analytics', 1, 105, NOW(3), NOW(3)),
('menu:tools:batch-operations', '批量数据操作菜单', '批量数据操作菜单访问权限', 'menu', '/tools/batch-operations', 1, 106, NOW(3), NOW(3)),

-- API权限
('api:tools:api-test', 'API测试工具接口', 'API测试工具相关接口权限', 'api', '/api/v1/tools/api-test/*', 1, 200, NOW(3), NOW(3)),
('api:tools:monitoring', '系统监控接口', '系统监控相关接口权限', 'api', '/api/v1/tools/monitoring/*', 1, 201, NOW(3), NOW(3)),
('api:tools:enhanced-audit', '增强安全审计接口', '增强安全审计相关接口权限', 'api', '/api/v1/tools/enhanced-audit/*', 1, 202, NOW(3), NOW(3)),
('api:tools:anomaly-detection', '异常行为检测接口', '异常行为检测相关接口权限', 'api', '/api/v1/tools/anomaly-detection/*', 1, 203, NOW(3), NOW(3)),
('api:tools:advanced-analytics', '高级数据分析接口', '高级数据分析相关接口权限', 'api', '/api/v1/tools/advanced-analytics/*', 1, 204, NOW(3), NOW(3)),
('api:tools:batch-operations', '批量数据操作接口', '批量数据操作相关接口权限', 'api', '/api/v1/tools/batch-operations/*', 1, 205, NOW(3), NOW(3));

-- =====================================================
-- 6. 为超级管理员分配权限
-- =====================================================

-- 获取超级管理员角色ID
SET @super_admin_role_id = (SELECT id FROM `roles` WHERE code = 'super_admin' LIMIT 1);

-- 分配菜单权限
INSERT INTO `role_menus` (`role_id`, `menu_id`, `created_at`)
SELECT @super_admin_role_id, m.id, NOW(3)
FROM `menus` m
WHERE m.code LIKE 'tools%' AND m.is_enabled = 1 AND m.is_visible = 1;

-- 分配API权限
INSERT INTO `role_permissions` (`role_id`, `permission_id`, `created_at`)
SELECT @super_admin_role_id, p.id, NOW(3)
FROM `permissions` p
WHERE (p.code LIKE 'menu:tools%' OR p.code LIKE 'api:tools%');

-- =====================================================
-- 7. 验证和完成
-- =====================================================

-- 统计结果
SET @tools_menus_count = (SELECT COUNT(*) FROM `menus` WHERE code LIKE 'tools%' AND is_enabled = 1);
SET @tools_permissions_count = (SELECT COUNT(*) FROM `permissions` WHERE (code LIKE 'menu:tools%' OR code LIKE 'api:tools%') AND is_enabled = 1);
SET @super_admin_new_menus_count = (SELECT COUNT(*) FROM `role_menus` rm JOIN `menus` m ON rm.menu_id = m.id WHERE rm.role_id = @super_admin_role_id AND m.code LIKE 'tools%');

-- 更新迁移完成状态
UPDATE `migration_logs` 
SET 
    `status` = 'completed',
    `message` = '增强工具菜单和权限配置添加完成',
    `data_summary` = JSON_OBJECT(
        'version', 'v2.4.0',
        'tools_menus_created', @tools_menus_count,
        'tools_permissions_created', @tools_permissions_count,
        'super_admin_menus_assigned', @super_admin_new_menus_count
    ),
    `completed_at` = NOW(3)
WHERE `migration_name` = 'enhanced_tools_menus_v2.4.0';

-- 显示结果
SELECT
    '增强工具菜单迁移完成' as `migration_status`,
    @tools_menus_count as `tools_menus_created`,
    @tools_permissions_count as `tools_permissions_created`,
    @super_admin_new_menus_count as `super_admin_menus_assigned`,
    'v2.4.0' as `version`;

-- 显示新增菜单
SELECT 
    CONCAT(REPEAT('  ', m.level - 1), m.name) as '菜单名称',
    m.code as '菜单代码',
    m.path as '路径'
FROM menus m
WHERE m.code LIKE 'tools%'
ORDER BY m.level, m.sort_order;
