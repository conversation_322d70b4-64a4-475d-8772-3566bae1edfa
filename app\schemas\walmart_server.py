from typing import Optional, Dict, Any
from pydantic import BaseModel, HttpUrl


class WalmartServerBase(BaseModel):
    """WalmartServer基础模型"""

    api_url: str
    referer: str = "https://servicewechat.com/wx81d3e1fe4c2e11b4/175/page-frame.html"
    timeout: int
    retry_count: int
    daily_bind_limit: int
    api_rate_limit: int
    max_retry_times: int
    bind_timeout_seconds: int
    verification_code_expires: int
    log_retention_days: int
    enable_ip_whitelist: bool
    enable_security_audit: bool
    maintenance_mode: bool
    is_active: bool
    extra_config: Optional[Dict[str, Any]] = None


class WalmartServerCreate(WalmartServerBase):
    """创建WalmartServer时的模型"""

    pass


class WalmartServerUpdate(WalmartServerBase):
    """更新WalmartServer时的模型"""

    api_url: Optional[str] = None
    referer: Optional[str] = None
    timeout: Optional[int] = None
    retry_count: Optional[int] = None
    daily_bind_limit: Optional[int] = None
    api_rate_limit: Optional[int] = None
    max_retry_times: Optional[int] = None
    bind_timeout_seconds: Optional[int] = None
    verification_code_expires: Optional[int] = None
    log_retention_days: Optional[int] = None
    enable_ip_whitelist: Optional[bool] = None
    enable_security_audit: Optional[bool] = None
    maintenance_mode: Optional[bool] = None
    is_active: Optional[bool] = None


class WalmartServerInDB(WalmartServerBase):
    """数据库中的WalmartServer模型"""

    id: int

    class Config:
        from_attributes = True


class WalmartServer(WalmartServerInDB):
    """API响应的WalmartServer模型"""

    pass
