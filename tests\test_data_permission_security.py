"""
数据权限安全测试 - 验证跨商户数据泄露修复效果
"""

import pytest
from sqlalchemy.orm import Session
from fastapi.testclient import TestClient
from unittest.mock import Mock

from app.models.user import User
from app.models.merchant import Merchant
from app.models.department import Department
from app.models.walmart_ck import WalmartCK
from app.services.walmart_ck_service_new import WalmartCKService
from app.services.security_service import SecurityService
from app.core.auth import AuthService
from app.services.permission_service import PermissionService


class TestDataPermissionSecurity:
    """数据权限安全测试类"""

    @pytest.fixture
    def setup_test_data(self, db: Session):
        """设置测试数据"""
        # 创建两个商户
        merchant1 = Merchant(
            id=1,
            name="商户1",
            code="MERCHANT_1",
            api_key="test_key_1",
            api_secret="test_secret_1"
        )
        merchant2 = Merchant(
            id=2,
            name="商户2", 
            code="MERCHANT_2",
            api_key="test_key_2",
            api_secret="test_secret_2"
        )
        db.add_all([merchant1, merchant2])

        # 创建部门
        dept1 = Department(
            id=1,
            name="部门1",
            merchant_id=1
        )
        dept2 = Department(
            id=2,
            name="部门2",
            merchant_id=2
        )
        db.add_all([dept1, dept2])

        # 创建用户
        user1 = User(
            id=1,
            username="user1",
            email="<EMAIL>",
            hashed_password="hashed_password",
            merchant_id=1,
            department_id=1,
            is_superuser=False
        )
        user2 = User(
            id=2,
            username="user2",
            email="<EMAIL>",
            hashed_password="hashed_password",
            merchant_id=2,
            department_id=2,
            is_superuser=False
        )
        superuser = User(
            id=3,
            username="admin",
            email="<EMAIL>",
            hashed_password="hashed_password",
            merchant_id=None,
            department_id=None,
            is_superuser=True
        )
        db.add_all([user1, user2, superuser])

        # 创建CK数据
        ck1 = WalmartCK(
            id=1,
            sign="test_sign_1",
            merchant_id=1,
            department_id=1,
            created_by=1,
            total_limit=100,
            bind_count=0,
            active=True,
            is_deleted=False
        )
        ck2 = WalmartCK(
            id=2,
            sign="test_sign_2",
            merchant_id=2,
            department_id=2,
            created_by=2,
            total_limit=100,
            bind_count=0,
            active=True,
            is_deleted=False
        )
        db.add_all([ck1, ck2])

        db.commit()
        return {
            "merchant1": merchant1,
            "merchant2": merchant2,
            "user1": user1,
            "user2": user2,
            "superuser": superuser,
            "ck1": ck1,
            "ck2": ck2
        }

    def test_data_scope_fix(self, db: Session, setup_test_data):
        """测试数据权限范围修复"""
        auth_service = AuthService()
        user1 = setup_test_data["user1"]

        # 模拟用户有 data:department:all 权限
        mock_permission_service = Mock()
        mock_permission_service.get_user_data_permissions.return_value = ["data:department:all"]

        # 修复前：data:department:all 会返回 "all" 范围
        # 修复后：data:department:all 应该返回 "merchant" 范围
        with pytest.MonkeyPatch().context() as m:
            m.setattr("app.services.permission_service.PermissionService", lambda db: mock_permission_service)
            data_scope = auth_service.get_user_data_scope(user1, db)
            
        # 验证修复效果
        assert data_scope == "merchant", f"期望返回 'merchant'，实际返回 '{data_scope}'"

    def test_merchant_isolation_in_ck_service(self, db: Session, setup_test_data):
        """测试CK服务中的商户隔离"""
        ck_service = WalmartCKService(db)
        user1 = setup_test_data["user1"]  # 属于商户1
        user2 = setup_test_data["user2"]  # 属于商户2
        ck1 = setup_test_data["ck1"]      # 属于商户1
        ck2 = setup_test_data["ck2"]      # 属于商户2

        # 用户1应该能访问自己商户的CK
        result1 = ck_service.get_with_security_check(ck1.id, user1)
        assert result1 is not None, "用户应该能访问自己商户的CK"

        # 用户1不应该能访问其他商户的CK
        result2 = ck_service.get_with_security_check(ck2.id, user1)
        assert result2 is None, "用户不应该能访问其他商户的CK"

    def test_security_service_validation(self, db: Session, setup_test_data):
        """测试安全服务验证"""
        security_service = SecurityService(db)
        user1 = setup_test_data["user1"]
        user2 = setup_test_data["user2"]

        # 测试商户隔离验证
        # 用户1访问商户1的数据应该通过
        assert security_service.validate_merchant_isolation(user1, 1, "read", "walmart_ck")

        # 用户1访问商户2的数据应该失败
        assert not security_service.validate_merchant_isolation(user1, 2, "read", "walmart_ck")

        # 测试CK访问验证
        assert security_service.validate_ck_access(user1, 1, "read")  # 自己商户的CK
        assert not security_service.validate_ck_access(user1, 2, "read")  # 其他商户的CK

    def test_superuser_access(self, db: Session, setup_test_data):
        """测试超级管理员访问"""
        ck_service = WalmartCKService(db)
        security_service = SecurityService(db)
        superuser = setup_test_data["superuser"]

        # 超级管理员应该能访问所有CK
        assert ck_service.get_with_security_check(1, superuser) is not None
        assert ck_service.get_with_security_check(2, superuser) is not None

        # 超级管理员应该通过所有安全验证
        assert security_service.validate_merchant_isolation(superuser, 1, "read", "walmart_ck")
        assert security_service.validate_merchant_isolation(superuser, 2, "read", "walmart_ck")

    def test_data_isolation_in_queries(self, db: Session, setup_test_data):
        """测试查询中的数据隔离"""
        ck_service = WalmartCKService(db)
        user1 = setup_test_data["user1"]
        user2 = setup_test_data["user2"]

        # 用户1的查询应该只返回商户1的CK
        query1 = db.query(WalmartCK)
        isolated_query1 = ck_service.apply_data_isolation(query1, user1)
        results1 = isolated_query1.all()
        
        assert len(results1) == 1, f"用户1应该只能看到1个CK，实际看到{len(results1)}个"
        assert results1[0].merchant_id == 1, "用户1应该只能看到自己商户的CK"

        # 用户2的查询应该只返回商户2的CK
        query2 = db.query(WalmartCK)
        isolated_query2 = ck_service.apply_data_isolation(query2, user2)
        results2 = isolated_query2.all()
        
        assert len(results2) == 1, f"用户2应该只能看到1个CK，实际看到{len(results2)}个"
        assert results2[0].merchant_id == 2, "用户2应该只能看到自己商户的CK"

    def test_permission_escalation_detection(self, db: Session, setup_test_data):
        """测试权限提升检测"""
        security_service = SecurityService(db)
        user1 = setup_test_data["user1"]

        # 测试危险权限请求
        dangerous_permissions = ["data:merchant:all", "api:users:create"]
        result = security_service.check_permission_escalation(user1, dangerous_permissions)

        assert result["has_escalation"], "应该检测到权限提升尝试"
        assert len(result["escalation_attempts"]) == 2, "应该检测到2个危险权限"

    def test_audit_logging(self, db: Session, setup_test_data):
        """测试审计日志"""
        security_service = SecurityService(db)
        user1 = setup_test_data["user1"]

        # 记录安全违规
        security_service.log_security_violation(
            user1,
            "TEST_VIOLATION",
            "测试安全违规",
            {"test": "data"}
        )

        # 记录数据访问
        security_service.log_data_access(
            user1,
            "read",
            "walmart_ck",
            1,
            1
        )

        # 验证日志记录（这里可以查询audit_logs表验证）
        # 实际实现中可以添加更详细的验证逻辑

    def test_api_endpoint_security(self, client: TestClient, setup_test_data):
        """测试API端点安全性"""
        # 这里可以添加对实际API端点的安全测试
        # 例如测试用户是否能通过API访问其他商户的CK数据
        pass


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
