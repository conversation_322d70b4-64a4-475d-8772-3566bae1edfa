package repository

import (
	"context"
	"time"

	"walmart-bind-card-gateway/internal/model"
	"walmart-bind-card-gateway/pkg/database"
)

// CardRepository 卡记录仓储接口
type CardRepository interface {
	// 批量操作
	BatchCreate(ctx context.Context, requests []*model.InternalBindRequest) error
	BatchCreateRecords(ctx context.Context, records []*model.CardRecord) error
	BatchCreateAndReturn(ctx context.Context, requests []*model.InternalBindRequest) ([]*model.CardRecord, error)
	
	// 查询操作
	GetByRequestID(ctx context.Context, requestID string) (*model.CardRecord, error)
	GetByStatus(ctx context.Context, status string, limit int) ([]*model.CardRecord, error)
	GetByMerchantCode(ctx context.Context, merchantCode string, limit int) ([]*model.CardRecord, error)
	GetRecentRecords(ctx context.Context, hours int, limit int) ([]*model.CardRecord, error)
	
	// 更新操作
	UpdateStatus(ctx context.Context, requestID string, status string) error
	BatchUpdateStatus(ctx context.Context, updates map[string]string) error
	
	// 统计操作
	CountByStatus(ctx context.Context, status string) (int64, error)
	CountByMerchantCode(ctx context.Context, merchantCode string) (int64, error)
	CountByTimeRange(ctx context.Context, start, end time.Time) (int64, error)
	
	// 性能监控
	GetStats(ctx context.Context) map[string]interface{}
}

// cardRepository 卡记录仓储实现
type cardRepository struct {
	db database.DB
}

// NewCardRepository 创建卡记录仓储
func NewCardRepository(db database.DB) CardRepository {
	return &cardRepository{
		db: db,
	}
}

// BatchCreate 批量创建卡记录
func (r *cardRepository) BatchCreate(ctx context.Context, requests []*model.InternalBindRequest) error {
	return r.db.BatchInsertCardRecordsFromRequests(ctx, requests)
}

// BatchCreateRecords 批量创建卡记录
func (r *cardRepository) BatchCreateRecords(ctx context.Context, records []*model.CardRecord) error {
	return r.db.BatchInsertCardRecords(ctx, records)
}

// BatchCreateAndReturn 批量创建卡记录并返回创建的记录
func (r *cardRepository) BatchCreateAndReturn(ctx context.Context, requests []*model.InternalBindRequest) ([]*model.CardRecord, error) {
	return r.db.BatchInsertCardRecordsAndReturn(ctx, requests)
}

// GetByRequestID 根据请求ID获取卡记录
func (r *cardRepository) GetByRequestID(ctx context.Context, requestID string) (*model.CardRecord, error) {
	return r.db.GetCardRecord(ctx, requestID)
}

// GetByStatus 根据状态获取卡记录
func (r *cardRepository) GetByStatus(ctx context.Context, status string, limit int) ([]*model.CardRecord, error) {
	return r.db.GetCardRecordsByStatus(ctx, status, limit)
}

// GetByMerchantCode 根据商户代码获取卡记录
func (r *cardRepository) GetByMerchantCode(ctx context.Context, merchantCode string, limit int) ([]*model.CardRecord, error) {
	var records []*model.CardRecord
	err := r.db.GetDB().WithContext(ctx).
		Where("merchant_code = ?", merchantCode).
		Order("created_at DESC").
		Limit(limit).
		Find(&records).Error
	
	if err != nil {
		return nil, err
	}
	return records, nil
}

// GetRecentRecords 获取最近的记录
func (r *cardRepository) GetRecentRecords(ctx context.Context, hours int, limit int) ([]*model.CardRecord, error) {
	since := time.Now().Add(-time.Duration(hours) * time.Hour)
	
	var records []*model.CardRecord
	err := r.db.GetDB().WithContext(ctx).
		Where("created_at >= ?", since).
		Order("created_at DESC").
		Limit(limit).
		Find(&records).Error
	
	if err != nil {
		return nil, err
	}
	return records, nil
}

// UpdateStatus 更新状态
func (r *cardRepository) UpdateStatus(ctx context.Context, requestID string, status string) error {
	return r.db.UpdateCardStatus(ctx, requestID, status)
}

// BatchUpdateStatus 批量更新状态
func (r *cardRepository) BatchUpdateStatus(ctx context.Context, updates map[string]string) error {
	return r.db.BatchUpdateCardStatus(ctx, updates)
}

// CountByStatus 根据状态统计数量
func (r *cardRepository) CountByStatus(ctx context.Context, status string) (int64, error) {
	var count int64
	err := r.db.GetDB().WithContext(ctx).
		Model(&model.CardRecord{}).
		Where("status = ?", status).
		Count(&count).Error
	
	return count, err
}

// CountByMerchantCode 根据商户代码统计数量
func (r *cardRepository) CountByMerchantCode(ctx context.Context, merchantCode string) (int64, error) {
	var count int64
	err := r.db.GetDB().WithContext(ctx).
		Model(&model.CardRecord{}).
		Where("merchant_code = ?", merchantCode).
		Count(&count).Error
	
	return count, err
}

// CountByTimeRange 根据时间范围统计数量
func (r *cardRepository) CountByTimeRange(ctx context.Context, start, end time.Time) (int64, error) {
	var count int64
	err := r.db.GetDB().WithContext(ctx).
		Model(&model.CardRecord{}).
		Where("created_at BETWEEN ? AND ?", start, end).
		Count(&count).Error
	
	return count, err
}

// GetStats 获取统计信息
func (r *cardRepository) GetStats(ctx context.Context) map[string]interface{} {
	stats := make(map[string]interface{})
	
	// 总记录数
	var totalCount int64
	r.db.GetDB().WithContext(ctx).Model(&model.CardRecord{}).Count(&totalCount)
	stats["total_records"] = totalCount
	
	// 各状态统计
	statusCounts := make(map[string]int64)
	statuses := []string{
		model.StatusPending,
		model.StatusProcessing,
		model.StatusSuccess,
		model.StatusFailed,
		model.StatusTimeout,
	}
	
	for _, status := range statuses {
		count, _ := r.CountByStatus(ctx, status)
		statusCounts[status] = count
	}
	stats["status_counts"] = statusCounts
	
	// 今日统计
	today := time.Now().Truncate(24 * time.Hour)
	tomorrow := today.Add(24 * time.Hour)
	todayCount, _ := r.CountByTimeRange(ctx, today, tomorrow)
	stats["today_count"] = todayCount
	
	// 最近1小时统计
	oneHourAgo := time.Now().Add(-time.Hour)
	recentCount, _ := r.CountByTimeRange(ctx, oneHourAgo, time.Now())
	stats["recent_hour_count"] = recentCount
	
	return stats
}

// BatchCreateWithRetry 带重试的批量创建
func (r *cardRepository) BatchCreateWithRetry(ctx context.Context, requests []*model.InternalBindRequest, maxRetries int) error {
	var lastErr error
	
	for i := 0; i < maxRetries; i++ {
		err := r.BatchCreate(ctx, requests)
		if err == nil {
			return nil
		}
		
		lastErr = err
		
		// 等待一段时间后重试
		select {
		case <-ctx.Done():
			return ctx.Err()
		case <-time.After(time.Duration(i+1) * 100 * time.Millisecond):
			// 继续重试
		}
	}
	
	return lastErr
}

// GetPendingRecords 获取待处理记录
func (r *cardRepository) GetPendingRecords(ctx context.Context, limit int) ([]*model.CardRecord, error) {
	return r.GetByStatus(ctx, model.StatusPending, limit)
}

// GetFailedRecords 获取失败记录
func (r *cardRepository) GetFailedRecords(ctx context.Context, limit int) ([]*model.CardRecord, error) {
	return r.GetByStatus(ctx, model.StatusFailed, limit)
}

// MarkAsProcessing 标记为处理中
func (r *cardRepository) MarkAsProcessing(ctx context.Context, requestID string) error {
	return r.UpdateStatus(ctx, requestID, model.StatusProcessing)
}

// MarkAsSuccess 标记为成功
func (r *cardRepository) MarkAsSuccess(ctx context.Context, requestID string) error {
	return r.UpdateStatus(ctx, requestID, model.StatusSuccess)
}

// MarkAsFailed 标记为失败
func (r *cardRepository) MarkAsFailed(ctx context.Context, requestID string) error {
	return r.UpdateStatus(ctx, requestID, model.StatusFailed)
}

// MarkAsTimeout 标记为超时
func (r *cardRepository) MarkAsTimeout(ctx context.Context, requestID string) error {
	return r.UpdateStatus(ctx, requestID, model.StatusTimeout)
}
