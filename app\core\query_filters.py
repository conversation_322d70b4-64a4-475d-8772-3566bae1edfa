"""
通用查询过滤器
"""
from typing import Dict, Any, Optional, List
from sqlalchemy.orm import Query
from abc import ABC, abstractmethod


class FilterStrategy(ABC):
    """过滤策略基类"""

    @abstractmethod
    def can_handle(self, filter_name: str, filter_value: Any) -> bool:
        """判断是否能处理该过滤条件"""
        pass

    @abstractmethod
    def apply_filter(self, query: Query, model, filter_name: str, filter_value: Any) -> Query:
        """应用过滤条件"""
        pass


class InFilterStrategy(FilterStrategy):
    """IN过滤策略"""

    def can_handle(self, filter_name: str, filter_value: Any) -> bool:
        return filter_name.endswith('__in') and isinstance(filter_value, list)

    def apply_filter(self, query: Query, model, filter_name: str, filter_value: Any) -> Query:
        field_name = filter_name[:-4]
        field = getattr(model, field_name, None)
        if field:
            return query.filter(field.in_(filter_value))
        return query


class NotInFilterStrategy(FilterStrategy):
    """NOT IN过滤策略"""

    def can_handle(self, filter_name: str, filter_value: Any) -> bool:
        return filter_name.endswith('__not_in') and isinstance(filter_value, list)

    def apply_filter(self, query: Query, model, filter_name: str, filter_value: Any) -> Query:
        field_name = filter_name[:-8]
        field = getattr(model, field_name, None)
        if field:
            return query.filter(~field.in_(filter_value))
        return query


class LikeFilterStrategy(FilterStrategy):
    """LIKE过滤策略"""

    def can_handle(self, filter_name: str, filter_value: Any) -> bool:
        return filter_name.endswith('__like')

    def apply_filter(self, query: Query, model, filter_name: str, filter_value: Any) -> Query:
        field_name = filter_name[:-6]
        field = getattr(model, field_name, None)
        if field:
            return query.filter(field.like(f"%{filter_value}%"))
        return query


class ILikeFilterStrategy(FilterStrategy):
    """ILIKE过滤策略"""

    def can_handle(self, filter_name: str, filter_value: Any) -> bool:
        return filter_name.endswith('__ilike')

    def apply_filter(self, query: Query, model, filter_name: str, filter_value: Any) -> Query:
        field_name = filter_name[:-7]
        field = getattr(model, field_name, None)
        if field:
            return query.filter(field.ilike(f"%{filter_value}%"))
        return query


class IsNullFilterStrategy(FilterStrategy):
    """IS NULL过滤策略"""

    def can_handle(self, filter_name: str, filter_value: Any) -> bool:
        return filter_name.endswith('__is_null')

    def apply_filter(self, query: Query, model, filter_name: str, filter_value: Any) -> Query:
        field_name = filter_name[:-9]
        field = getattr(model, field_name, None)
        if field:
            if filter_value:
                return query.filter(field.is_(None))
            else:
                return query.filter(field.is_not(None))
        return query


class EqualFilterStrategy(FilterStrategy):
    """等值过滤策略"""

    def can_handle(self, filter_name: str, filter_value: Any) -> bool:
        return True  # 默认策略，处理所有其他情况

    def apply_filter(self, query: Query, model, filter_name: str, filter_value: Any) -> Query:
        field = getattr(model, filter_name, None)
        if field:
            if filter_value is None:
                return query.filter(field.is_(None))
            else:
                return query.filter(field == filter_value)
        return query


class BaseQueryFilter(ABC):
    """基础查询过滤器"""
    
    def __init__(self, model):
        self.model = model
    
    def apply_filters(self, query: Query, filters: Optional[Dict[str, Any]]) -> Query:
        """应用过滤条件"""
        if not filters:
            return query
        
        for filter_name, filter_value in filters.items():
            if filter_value is not None and filter_value != "":
                query = self._apply_single_filter(query, filter_name, filter_value)
        
        return query
    
    @abstractmethod
    def _apply_single_filter(self, query: Query, filter_name: str, filter_value: Any) -> Query:
        """应用单个过滤条件"""
        pass


class StandardQueryFilter(BaseQueryFilter):
    """标准查询过滤器"""

    def __init__(self, model, filter_mappings: Dict[str, str] = None):
        super().__init__(model)
        self.filter_mappings = filter_mappings or {}
        self.strategies = [
            InFilterStrategy(),
            NotInFilterStrategy(),
            LikeFilterStrategy(),
            ILikeFilterStrategy(),
            IsNullFilterStrategy(),
            EqualFilterStrategy(),  # 默认策略，放在最后
        ]

    def _apply_single_filter(self, query: Query, filter_name: str, filter_value: Any) -> Query:
        """应用单个过滤条件"""
        # 获取实际的字段名
        mapped_filter_name = self.filter_mappings.get(filter_name, filter_name)

        # 使用策略模式处理过滤条件
        for strategy in self.strategies:
            if strategy.can_handle(filter_name, filter_value):
                return strategy.apply_filter(query, self.model, mapped_filter_name, filter_value)

        return query


class PaginationHelper:
    """分页辅助类"""
    
    @staticmethod
    def apply_pagination(query: Query, skip: int = 0, limit: int = 100) -> Query:
        """应用分页"""
        return query.offset(skip).limit(limit)
    
    @staticmethod
    def get_total_count(query: Query) -> int:
        """获取总数"""
        return query.count()


class OrderingHelper:
    """排序辅助类"""
    
    @staticmethod
    def apply_ordering(query: Query, model, order_by: Optional[List[str]] = None) -> Query:
        """应用排序"""
        if not order_by:
            return query
        
        order_clauses = []
        for order_field in order_by:
            if order_field.startswith('-'):
                # 降序
                field_name = order_field[1:]
                field = getattr(model, field_name, None)
                if field:
                    order_clauses.append(field.desc())
            else:
                # 升序
                field = getattr(model, order_field, None)
                if field:
                    order_clauses.append(field.asc())
        
        if order_clauses:
            return query.order_by(*order_clauses)
        
        return query


class QueryBuilder:
    """查询构建器"""
    
    def __init__(self, model, filter_class=StandardQueryFilter):
        self.model = model
        self.filter = filter_class(model)
        self.pagination = PaginationHelper()
        self.ordering = OrderingHelper()
    
    def build_query(
        self,
        db_query: Query,
        filters: Optional[Dict[str, Any]] = None,
        order_by: Optional[List[str]] = None,
        skip: int = 0,
        limit: int = 100
    ) -> tuple[Query, int]:
        """构建完整查询"""
        # 应用过滤条件
        query = self.filter.apply_filters(db_query, filters)
        
        # 获取总数
        total = self.pagination.get_total_count(query)
        
        # 应用排序
        query = self.ordering.apply_ordering(query, self.model, order_by)
        
        # 应用分页
        query = self.pagination.apply_pagination(query, skip, limit)
        
        return query, total
