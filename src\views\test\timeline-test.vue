<template>
  <div class="timeline-test-container">
    <div class="test-header">
      <h1>🧪 绑卡时间线测试页面</h1>
      <p>测试新的绑卡执行时间线功能</p>
    </div>

    <!-- 测试输入 -->
    <el-card class="test-input-card">
      <template #header>
        <span>测试输入</span>
      </template>
      <el-form :model="testForm" label-width="120px">
        <el-form-item label="绑卡记录ID">
          <el-input 
            v-model="testForm.cardRecordId" 
            placeholder="请输入绑卡记录ID"
            style="width: 300px"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="loadTimeline" :loading="loading">
            加载时间线
          </el-button>
          <el-button @click="clearTimeline">
            清空
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- API 测试结果 -->
    <el-card v-if="apiTestResults.length" class="api-test-card">
      <template #header>
        <span>API 测试结果</span>
      </template>
      <el-collapse v-model="activeApiTest">
        <el-collapse-item 
          v-for="(result, index) in apiTestResults" 
          :key="index"
          :title="result.title"
          :name="index"
        >
          <div class="api-result">
            <div class="result-status">
              <el-tag :type="result.success ? 'success' : 'danger'">
                {{ result.success ? '成功' : '失败' }}
              </el-tag>
              <span class="result-time">{{ result.timestamp }}</span>
            </div>
            <div v-if="result.error" class="result-error">
              <el-alert type="error" :title="result.error" :closable="false" />
            </div>
            <div v-if="result.data" class="result-data">
              <h4>响应数据:</h4>
              <pre class="json-content">{{ JSON.stringify(result.data, null, 2) }}</pre>
            </div>
          </div>
        </el-collapse-item>
      </el-collapse>
    </el-card>

    <!-- 时间线组件测试 -->
    <el-card v-if="showTimeline" class="timeline-card">
      <template #header>
        <div class="timeline-card-header">
          <span>绑卡执行时间线</span>
          <el-button size="small" @click="showTimeline = false">关闭</el-button>
        </div>
      </template>
      <BindingTimeline 
        :card-record-id="testForm.cardRecordId" 
        @timeline-loaded="onTimelineLoaded"
      />
    </el-card>

    <!-- 使用说明 -->
    <el-card class="usage-card">
      <template #header>
        <span>使用说明</span>
      </template>
      <div class="usage-content">
        <h4>测试步骤：</h4>
        <ol>
          <li>输入一个有效的绑卡记录ID（可以从绑卡数据页面获取）</li>
          <li>点击"加载时间线"按钮</li>
          <li>查看API测试结果，确认后端接口正常</li>
          <li>查看时间线组件显示效果</li>
        </ol>
        
        <h4>功能特性：</h4>
        <ul>
          <li>📊 显示每个步骤的开始时间、结束时间和执行耗时</li>
          <li>🎯 提供总体统计信息（总耗时、步骤数、成功/失败数）</li>
          <li>📈 性能分析功能，识别瓶颈步骤</li>
          <li>🔍 可展开查看每个步骤的请求/响应数据</li>
          <li>⚡ 实时刷新功能</li>
        </ul>

        <h4>API 端点：</h4>
        <ul>
          <li><code>GET /api/v1/binding-logs/{card_record_id}/timeline</code> - 获取时间线数据</li>
          <li><code>GET /api/v1/binding-logs/{card_record_id}/performance</code> - 获取性能分析</li>
        </ul>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { ElMessage } from 'element-plus'
import BindingTimeline from '@/components/business/bindCardLog/BindingTimeline.vue'
import { bindingLogsApi } from '@/api/modules/bindingLogs'

// 响应式数据
const loading = ref(false)
const showTimeline = ref(false)
const apiTestResults = ref([])
const activeApiTest = ref([])

const testForm = reactive({
  cardRecordId: ''
})

// 方法
const loadTimeline = async () => {
  if (!testForm.cardRecordId) {
    ElMessage.warning('请输入绑卡记录ID')
    return
  }

  loading.value = true
  apiTestResults.value = []
  
  try {
    // 测试时间线API
    await testTimelineApi()
    
    // 测试性能分析API
    await testPerformanceApi()
    
    // 显示时间线组件
    showTimeline.value = true
    
    ElMessage.success('测试完成！')
  } catch (error) {
    console.error('测试失败:', error)
    ElMessage.error('测试失败')
  } finally {
    loading.value = false
  }
}

const testTimelineApi = async () => {
  const startTime = Date.now()
  try {
    const data = await bindingLogsApi.getTimeline(testForm.cardRecordId)
    const endTime = Date.now()
    
    apiTestResults.value.push({
      title: `时间线API测试 (耗时: ${endTime - startTime}ms)`,
      success: true,
      data: data,
      timestamp: new Date().toLocaleTimeString()
    })
  } catch (error) {
    apiTestResults.value.push({
      title: '时间线API测试',
      success: false,
      error: error.message || '请求失败',
      timestamp: new Date().toLocaleTimeString()
    })
    throw error
  }
}

const testPerformanceApi = async () => {
  const startTime = Date.now()
  try {
    const data = await bindingLogsApi.getPerformanceAnalysis(testForm.cardRecordId)
    const endTime = Date.now()
    
    apiTestResults.value.push({
      title: `性能分析API测试 (耗时: ${endTime - startTime}ms)`,
      success: true,
      data: data,
      timestamp: new Date().toLocaleTimeString()
    })
  } catch (error) {
    apiTestResults.value.push({
      title: '性能分析API测试',
      success: false,
      error: error.message || '请求失败',
      timestamp: new Date().toLocaleTimeString()
    })
    // 不抛出错误，因为性能分析是可选的
  }
}

const clearTimeline = () => {
  testForm.cardRecordId = ''
  showTimeline.value = false
  apiTestResults.value = []
  activeApiTest.value = []
}

const onTimelineLoaded = (timelineData) => {
  console.log('时间线数据加载完成:', timelineData)
  ElMessage.success(`时间线加载成功！共 ${timelineData.steps?.length || 0} 个步骤`)
}
</script>

<style scoped>
.timeline-test-container {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.test-header {
  text-align: center;
  margin-bottom: 30px;
}

.test-header h1 {
  color: #303133;
  margin-bottom: 8px;
}

.test-header p {
  color: #606266;
  font-size: 16px;
}

.test-input-card,
.api-test-card,
.timeline-card,
.usage-card {
  margin-bottom: 20px;
}

.timeline-card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.api-result {
  padding: 16px 0;
}

.result-status {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 12px;
}

.result-time {
  color: #909399;
  font-size: 12px;
}

.result-error {
  margin-bottom: 12px;
}

.result-data h4 {
  margin: 0 0 8px 0;
  color: #303133;
  font-size: 14px;
}

.json-content {
  background: #f5f7fa;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  padding: 12px;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 12px;
  line-height: 1.5;
  color: #2c3e50;
  white-space: pre-wrap;
  word-break: break-all;
  max-height: 400px;
  overflow-y: auto;
}

.usage-content h4 {
  color: #303133;
  margin: 16px 0 8px 0;
  font-size: 16px;
}

.usage-content ol,
.usage-content ul {
  margin: 8px 0 16px 0;
  padding-left: 20px;
}

.usage-content li {
  margin-bottom: 6px;
  color: #606266;
  line-height: 1.6;
}

.usage-content code {
  background: #f5f7fa;
  padding: 2px 6px;
  border-radius: 3px;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 12px;
  color: #e6a23c;
}
</style>
