"""
测试频率限制器
"""

import pytest
import time
from unittest.mock import Mock, patch
from collections import deque

from app.telegram_bot.rate_limiter import RateLimiter, RateLimit
from app.telegram_bot.config import BotConfig
from app.telegram_bot.exceptions import RateLimitError


@pytest.fixture
def mock_config():
    """创建模拟配置"""
    config = Mock(spec=BotConfig)
    config.rate_limit_global = 1000
    config.rate_limit_group = 100
    config.rate_limit_user = 50
    config.max_bind_attempts_per_day = 5
    return config


@pytest.fixture
def rate_limiter(mock_config):
    """创建频率限制器"""
    return RateLimiter(mock_config)


class TestRateLimit:
    """测试RateLimit数据类"""

    def test_rate_limit_creation(self):
        """测试创建频率限制配置"""
        limit = RateLimit(limit=100, window=3600)
        
        assert limit.limit == 100
        assert limit.window == 3600


class TestRateLimiter:
    """测试RateLimiter类"""

    def test_initialization(self, rate_limiter: RateLimiter, mock_config):
        """测试初始化"""
        assert rate_limiter.config == mock_config
        assert isinstance(rate_limiter.user_requests, dict)
        assert isinstance(rate_limiter.group_requests, dict)
        assert isinstance(rate_limiter.global_requests, deque)
        assert isinstance(rate_limiter.bind_attempts, dict)
        
        # 检查限制配置
        assert rate_limiter.limits["global"].limit == 1000
        assert rate_limiter.limits["group"].limit == 100
        assert rate_limiter.limits["user"].limit == 50
        assert rate_limiter.limits["bind"].limit == 5

    def test_clean_old_requests(self, rate_limiter: RateLimiter):
        """测试清理过期请求"""
        current_time = time.time()
        test_queue = deque([
            current_time - 7200,  # 2小时前（过期）
            current_time - 1800,  # 30分钟前（有效）
            current_time - 300,   # 5分钟前（有效）
            current_time          # 现在（有效）
        ])
        
        rate_limiter._clean_old_requests(test_queue, 3600)  # 1小时窗口
        
        # 应该只剩下3个有效请求
        assert len(test_queue) == 3
        assert test_queue[0] >= current_time - 3600

    def test_check_global_limit_success(self, rate_limiter: RateLimiter):
        """测试全局限制检查成功"""
        # 全局限制是1000次/小时，应该能通过
        result = rate_limiter.check_global_limit()
        
        assert result is True
        assert len(rate_limiter.global_requests) == 1

    def test_check_global_limit_exceeded(self, rate_limiter: RateLimiter):
        """测试全局限制超出"""
        # 填满全局请求队列
        current_time = time.time()
        for i in range(1000):
            rate_limiter.global_requests.append(current_time)
        
        result = rate_limiter.check_global_limit()
        
        assert result is False

    def test_check_user_limit_success(self, rate_limiter: RateLimiter):
        """测试用户限制检查成功"""
        user_id = 123456789
        
        result = rate_limiter.check_user_limit(user_id)
        
        assert result is True
        assert len(rate_limiter.user_requests[user_id]) == 1

    def test_check_user_limit_exceeded(self, rate_limiter: RateLimiter):
        """测试用户限制超出"""
        user_id = 123456789
        current_time = time.time()
        
        # 填满用户请求队列（50次/小时）
        for i in range(50):
            rate_limiter.user_requests[user_id].append(current_time)
        
        result = rate_limiter.check_user_limit(user_id)
        
        assert result is False

    def test_check_group_limit_success(self, rate_limiter: RateLimiter):
        """测试群组限制检查成功"""
        chat_id = -1001234567890
        
        result = rate_limiter.check_group_limit(chat_id)
        
        assert result is True
        assert len(rate_limiter.group_requests[chat_id]) == 1

    def test_check_group_limit_exceeded(self, rate_limiter: RateLimiter):
        """测试群组限制超出"""
        chat_id = -1001234567890
        current_time = time.time()
        
        # 填满群组请求队列（100次/小时）
        for i in range(100):
            rate_limiter.group_requests[chat_id].append(current_time)
        
        result = rate_limiter.check_group_limit(chat_id)
        
        assert result is False

    def test_check_bind_limit_success(self, rate_limiter: RateLimiter):
        """测试绑定限制检查成功"""
        user_id = 123456789
        
        result = rate_limiter.check_bind_limit(user_id)
        
        assert result is True
        assert len(rate_limiter.bind_attempts[user_id]) == 1

    def test_check_bind_limit_exceeded(self, rate_limiter: RateLimiter):
        """测试绑定限制超出"""
        user_id = 123456789
        current_time = time.time()
        
        # 填满绑定尝试队列（5次/天）
        for i in range(5):
            rate_limiter.bind_attempts[user_id].append(current_time)
        
        result = rate_limiter.check_bind_limit(user_id)
        
        assert result is False

    def test_check_all_limits_success(self, rate_limiter: RateLimiter):
        """测试所有限制检查成功"""
        user_id = 123456789
        chat_id = -1001234567890
        
        result = rate_limiter.check_all_limits(user_id, chat_id)
        
        assert result is True

    def test_check_all_limits_global_exceeded(self, rate_limiter: RateLimiter):
        """测试全局限制超出时的所有限制检查"""
        user_id = 123456789
        chat_id = -1001234567890
        
        # 填满全局请求队列
        current_time = time.time()
        for i in range(1000):
            rate_limiter.global_requests.append(current_time)
        
        with pytest.raises(RateLimitError) as exc_info:
            rate_limiter.check_all_limits(user_id, chat_id)
        
        assert "全局请求频率过高" in str(exc_info.value)

    def test_check_all_limits_user_exceeded(self, rate_limiter: RateLimiter):
        """测试用户限制超出时的所有限制检查"""
        user_id = 123456789
        chat_id = -1001234567890
        current_time = time.time()
        
        # 填满用户请求队列
        for i in range(50):
            rate_limiter.user_requests[user_id].append(current_time)
        
        with pytest.raises(RateLimitError) as exc_info:
            rate_limiter.check_all_limits(user_id, chat_id)
        
        assert "您的请求频率过高" in str(exc_info.value)

    def test_check_all_limits_group_exceeded(self, rate_limiter: RateLimiter):
        """测试群组限制超出时的所有限制检查"""
        user_id = 123456789
        chat_id = -1001234567890
        current_time = time.time()
        
        # 填满群组请求队列
        for i in range(100):
            rate_limiter.group_requests[chat_id].append(current_time)
        
        with pytest.raises(RateLimitError) as exc_info:
            rate_limiter.check_all_limits(user_id, chat_id)
        
        assert "群组请求频率过高" in str(exc_info.value)

    def test_check_all_limits_bind_command_exceeded(self, rate_limiter: RateLimiter):
        """测试绑定命令限制超出时的所有限制检查"""
        user_id = 123456789
        chat_id = -1001234567890
        current_time = time.time()
        
        # 填满绑定尝试队列
        for i in range(5):
            rate_limiter.bind_attempts[user_id].append(current_time)
        
        with pytest.raises(RateLimitError) as exc_info:
            rate_limiter.check_all_limits(user_id, chat_id, "/bind")
        
        assert "绑定尝试次数过多" in str(exc_info.value)

    def test_get_user_remaining_requests(self, rate_limiter: RateLimiter):
        """测试获取用户剩余请求次数"""
        user_id = 123456789
        
        # 初始状态应该有50次
        remaining = rate_limiter.get_user_remaining_requests(user_id)
        assert remaining == 50
        
        # 使用一些请求
        for i in range(10):
            rate_limiter.check_user_limit(user_id)
        
        remaining = rate_limiter.get_user_remaining_requests(user_id)
        assert remaining == 40

    def test_get_group_remaining_requests(self, rate_limiter: RateLimiter):
        """测试获取群组剩余请求次数"""
        chat_id = -1001234567890
        
        # 初始状态应该有100次
        remaining = rate_limiter.get_group_remaining_requests(chat_id)
        assert remaining == 100
        
        # 使用一些请求
        for i in range(20):
            rate_limiter.check_group_limit(chat_id)
        
        remaining = rate_limiter.get_group_remaining_requests(chat_id)
        assert remaining == 80

    def test_get_bind_remaining_attempts(self, rate_limiter: RateLimiter):
        """测试获取剩余绑定尝试次数"""
        user_id = 123456789
        
        # 初始状态应该有5次
        remaining = rate_limiter.get_bind_remaining_attempts(user_id)
        assert remaining == 5
        
        # 使用一些尝试
        for i in range(2):
            rate_limiter.check_bind_limit(user_id)
        
        remaining = rate_limiter.get_bind_remaining_attempts(user_id)
        assert remaining == 3

    def test_get_reset_time_global(self, rate_limiter: RateLimiter):
        """测试获取全局限制重置时间"""
        # 添加一个请求
        rate_limiter.check_global_limit()
        
        reset_time = rate_limiter.get_reset_time("global")
        
        # 应该接近3600秒（1小时）
        assert 3590 <= reset_time <= 3600

    def test_get_reset_time_user(self, rate_limiter: RateLimiter):
        """测试获取用户限制重置时间"""
        user_id = 123456789
        
        # 添加一个请求
        rate_limiter.check_user_limit(user_id)
        
        reset_time = rate_limiter.get_reset_time("user", user_id)
        
        # 应该接近3600秒（1小时）
        assert 3590 <= reset_time <= 3600

    def test_get_reset_time_empty_queue(self, rate_limiter: RateLimiter):
        """测试空队列的重置时间"""
        reset_time = rate_limiter.get_reset_time("user", 999999)
        
        assert reset_time == 0

    def test_update_config(self, rate_limiter: RateLimiter):
        """测试更新配置"""
        new_config = Mock(spec=BotConfig)
        new_config.rate_limit_global = 2000
        new_config.rate_limit_group = 200
        new_config.rate_limit_user = 100
        new_config.max_bind_attempts_per_day = 10
        
        rate_limiter.update_config(new_config)
        
        assert rate_limiter.config == new_config
        assert rate_limiter.limits["global"].limit == 2000
        assert rate_limiter.limits["group"].limit == 200
        assert rate_limiter.limits["user"].limit == 100
        assert rate_limiter.limits["bind"].limit == 10

    def test_reset_user_limits(self, rate_limiter: RateLimiter):
        """测试重置用户限制"""
        user_id = 123456789
        
        # 添加一些请求
        rate_limiter.check_user_limit(user_id)
        rate_limiter.check_bind_limit(user_id)
        
        assert len(rate_limiter.user_requests[user_id]) == 1
        assert len(rate_limiter.bind_attempts[user_id]) == 1
        
        # 重置用户限制
        rate_limiter.reset_user_limits(user_id)
        
        assert user_id not in rate_limiter.user_requests
        assert user_id not in rate_limiter.bind_attempts

    def test_reset_group_limits(self, rate_limiter: RateLimiter):
        """测试重置群组限制"""
        chat_id = -1001234567890
        
        # 添加一些请求
        rate_limiter.check_group_limit(chat_id)
        
        assert len(rate_limiter.group_requests[chat_id]) == 1
        
        # 重置群组限制
        rate_limiter.reset_group_limits(chat_id)
        
        assert chat_id not in rate_limiter.group_requests

    def test_reset_all_limits(self, rate_limiter: RateLimiter):
        """测试重置所有限制"""
        user_id = 123456789
        chat_id = -1001234567890
        
        # 添加一些请求
        rate_limiter.check_global_limit()
        rate_limiter.check_user_limit(user_id)
        rate_limiter.check_group_limit(chat_id)
        rate_limiter.check_bind_limit(user_id)
        
        assert len(rate_limiter.global_requests) == 1
        assert len(rate_limiter.user_requests) == 1
        assert len(rate_limiter.group_requests) == 1
        assert len(rate_limiter.bind_attempts) == 1
        
        # 重置所有限制
        rate_limiter.reset_all_limits()
        
        assert len(rate_limiter.global_requests) == 0
        assert len(rate_limiter.user_requests) == 0
        assert len(rate_limiter.group_requests) == 0
        assert len(rate_limiter.bind_attempts) == 0

    def test_get_statistics(self, rate_limiter: RateLimiter):
        """测试获取统计信息"""
        user_id = 123456789
        chat_id = -1001234567890
        
        # 添加一些请求
        rate_limiter.check_global_limit()
        rate_limiter.check_user_limit(user_id)
        rate_limiter.check_group_limit(chat_id)
        rate_limiter.check_bind_limit(user_id)
        
        stats = rate_limiter.get_statistics()
        
        assert stats["global_requests"] == 1
        assert stats["active_users"] == 1
        assert stats["active_groups"] == 1
        assert stats["bind_attempts"] == 1
        assert "limits" in stats
        assert stats["limits"]["global"]["limit"] == 1000
        assert stats["limits"]["user"]["limit"] == 50
