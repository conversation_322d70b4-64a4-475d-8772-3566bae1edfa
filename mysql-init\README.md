# MySQL 数据库初始化脚本

## 目录说明
此目录包含沃尔玛绑卡系统的核心数据库初始化脚本，用于全新部署时创建数据库结构和基础数据。

## 执行顺序
脚本按文件名数字顺序自动执行：

1. `00-charset-init.sql` - 字符集和时区初始化
2. `01-create-tables.sql` - 数据库表结构创建
3. `02-init-data.sql` - 基础数据初始化（角色、权限、菜单）
4. `03-performance-indexes.sql` - 性能优化索引创建

## 文件详情

### 00-charset-init.sql
- **用途**：设置数据库字符集为UTF8MB4，解决中文乱码问题
- **内容**：字符集配置、时区设置、SQL模式配置
- **重要性**：必须首先执行，确保后续脚本正确处理中文

### 01-create-tables.sql
- **用途**：创建所有数据库表结构
- **内容**：商户、部门、用户、角色、权限、菜单、CK、卡记录等表
- **特点**：使用BIGINT主键，支持高并发场景

### 02-init-data.sql
- **用途**：初始化系统基础数据
- **内容**：
  - 3个核心角色（超级管理员、商户管理员、CK供应商）
  - 完整的权限体系
  - 动态菜单配置
  - 超级管理员账号（admin）

### 03-performance-indexes.sql
- **用途**：创建性能优化索引
- **内容**：针对高频查询的复合索引
- **优化**：提升统计查询和数据隔离性能

## Docker 集成
这些脚本通过Docker的`/docker-entrypoint-initdb.d`机制自动执行：

```yaml
# docker-compose.yml
volumes:
  - ./mysql-init:/docker-entrypoint-initdb.d
```

## 安全特性
- **数据隔离**：严格的商户间、部门间数据隔离
- **权限控制**：动态权限系统，禁止硬编码权限
- **字符集安全**：UTF8MB4字符集，防止字符截断攻击
- **索引优化**：合理的索引设计，防止性能问题

## 初始账号
系统初始化后会创建超级管理员账号：
- **用户名**：admin
- **密码**：7c222fb2927d828af22f592134e8932480637c0d
- **权限**：系统最高权限

## 注意事项
1. **仅全新部署使用** - 这些脚本仅用于全新系统部署
2. **不可重复执行** - 包含CREATE TABLE语句，重复执行会报错
3. **升级使用migrations** - 系统升级请使用`migrations/`目录下的脚本
4. **生产环境安全** - 部署后立即修改默认密码

## 相关目录
- `migrations/` - 数据库升级脚本
- `test/database/` - 数据库测试脚本

## 故障排除
如果初始化失败：
1. 检查Docker容器日志
2. 确认MySQL服务正常启动
3. 验证字符集配置
4. 检查文件权限和路径