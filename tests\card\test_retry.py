"""
绑卡记录重试功能测试
"""
import pytest
import uuid
from datetime import datetime
from fastapi.testclient import TestClient
from sqlalchemy.orm import Session

from app.main import app
from app.models.card_record import CardRecord, CardStatus
from app.models.user import User
from app.models.merchant import Merchant
from app.models.department import Department
from app.models.walmart_ck import WalmartCK
from test.conftest import (
    get_test_db,
    create_test_user,
    create_test_merchant,
    create_test_department,
    create_test_walmart_ck,
    get_auth_headers
)

client = TestClient(app)


class TestCardRetry:
    """绑卡记录重试功能测试类"""

    def setup_method(self):
        """每个测试方法前的设置"""
        self.db = next(get_test_db())
        
        # 创建测试商户
        self.merchant = create_test_merchant(self.db, name="测试商户")
        
        # 创建测试部门
        self.department = create_test_department(
            self.db, 
            name="测试部门", 
            merchant_id=self.merchant.id
        )
        
        # 创建测试用户（商户管理员）
        self.merchant_admin = create_test_user(
            self.db,
            username="merchant_admin",
            email="<EMAIL>",
            merchant_id=self.merchant.id,
            department_id=self.department.id,
            role_codes=["merchant_admin"]
        )
        
        # 创建测试用户（CK供应商）
        self.ck_supplier = create_test_user(
            self.db,
            username="ck_supplier",
            email="<EMAIL>",
            merchant_id=self.merchant.id,
            department_id=self.department.id,
            role_codes=["ck_supplier"]
        )
        
        # 创建超级管理员
        self.super_admin = create_test_user(
            self.db,
            username="super_admin",
            email="<EMAIL>",
            is_superuser=True,
            role_codes=["super_admin"]
        )

    def teardown_method(self):
        """每个测试方法后的清理"""
        self.db.close()

    def create_test_card_record(
        self, 
        status: str = CardStatus.FAILED,
        error_message: str = None,
        response_data: dict = None
    ) -> CardRecord:
        """创建测试卡记录"""
        card_record = CardRecord(
            id=str(uuid.uuid4()),
            merchant_id=self.merchant.id,
            department_id=self.department.id,
            merchant_order_id=f"ORDER_{uuid.uuid4().hex[:8]}",
            amount=10000,  # 100元
            card_number="1234567890123456",
            card_password="encrypted_password",
            status=status,
            request_id=str(uuid.uuid4()),
            trace_id=str(uuid.uuid4()),
            request_data={"test": "data"},
            error_message=error_message,
            response_data=response_data,
            retry_count=0
        )
        self.db.add(card_record)
        self.db.commit()
        self.db.refresh(card_record)
        return card_record

    def test_retry_failed_card_success(self):
        """测试重试失败卡记录 - 成功场景（CK失效）"""
        # 创建CK失效的失败记录
        card = self.create_test_card_record(
            status=CardStatus.FAILED,
            error_message="需要登录",
            response_data={"need_retry_with_new_user": True}
        )
        
        # 商户管理员重试
        headers = get_auth_headers(self.merchant_admin)
        response = client.post(
            f"/api/v1/cards/{card.id}/retry",
            headers=headers
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert data["card_id"] == card.id
        assert data["status"] == "pending"
        assert data["retry_count"] == 1
        assert "重试成功" in data["message"]

    def test_retry_non_failed_card(self):
        """测试重试非失败状态的卡记录 - 应该失败"""
        # 创建成功状态的记录
        card = self.create_test_card_record(status=CardStatus.SUCCESS)
        
        headers = get_auth_headers(self.merchant_admin)
        response = client.post(
            f"/api/v1/cards/{card.id}/retry",
            headers=headers
        )
        
        assert response.status_code == 400
        assert "只能重试失败状态的记录" in response.json()["detail"]

    def test_retry_non_retryable_failure(self):
        """测试重试不可重试的失败记录 - 应该失败"""
        # 创建非CK失效的失败记录
        card = self.create_test_card_record(
            status=CardStatus.FAILED,
            error_message="卡号无效",
            response_data={"error_code": 400}
        )
        
        headers = get_auth_headers(self.merchant_admin)
        response = client.post(
            f"/api/v1/cards/{card.id}/retry",
            headers=headers
        )
        
        assert response.status_code == 400
        assert "不允许重试" in response.json()["detail"]

    def test_retry_card_permission_check(self):
        """测试重试卡记录的权限检查"""
        card = self.create_test_card_record(
            status=CardStatus.FAILED,
            error_message="需要登录"
        )
        
        # CK供应商没有重试权限
        headers = get_auth_headers(self.ck_supplier)
        response = client.post(
            f"/api/v1/cards/{card.id}/retry",
            headers=headers
        )
        
        assert response.status_code == 403
        assert "权限不足" in response.json()["detail"]

    def test_retry_card_data_isolation(self):
        """测试重试卡记录的数据隔离"""
        # 创建另一个商户的卡记录
        other_merchant = create_test_merchant(self.db, name="其他商户")
        other_department = create_test_department(
            self.db, 
            name="其他部门", 
            merchant_id=other_merchant.id
        )
        
        other_card = CardRecord(
            id=str(uuid.uuid4()),
            merchant_id=other_merchant.id,
            department_id=other_department.id,
            merchant_order_id=f"ORDER_{uuid.uuid4().hex[:8]}",
            amount=10000,
            card_number="9876543210987654",
            card_password="encrypted_password",
            status=CardStatus.FAILED,
            request_id=str(uuid.uuid4()),
            trace_id=str(uuid.uuid4()),
            request_data={"test": "data"},
            error_message="需要登录",
            retry_count=0
        )
        self.db.add(other_card)
        self.db.commit()
        
        # 商户管理员尝试重试其他商户的卡记录
        headers = get_auth_headers(self.merchant_admin)
        response = client.post(
            f"/api/v1/cards/{other_card.id}/retry",
            headers=headers
        )
        
        assert response.status_code == 400
        assert "不存在或无权限访问" in response.json()["detail"]

    def test_retry_nonexistent_card(self):
        """测试重试不存在的卡记录"""
        fake_id = str(uuid.uuid4())
        
        headers = get_auth_headers(self.merchant_admin)
        response = client.post(
            f"/api/v1/cards/{fake_id}/retry",
            headers=headers
        )
        
        assert response.status_code == 400
        assert "不存在或无权限访问" in response.json()["detail"]

    def test_super_admin_can_retry_any_card(self):
        """测试超级管理员可以重试任何卡记录"""
        card = self.create_test_card_record(
            status=CardStatus.FAILED,
            error_message="CK失效",
            response_data={"error_code": 203}
        )
        
        headers = get_auth_headers(self.super_admin)
        response = client.post(
            f"/api/v1/cards/{card.id}/retry",
            headers=headers
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True

    def test_retry_card_updates_retry_count(self):
        """测试重试卡记录会更新重试次数"""
        card = self.create_test_card_record(
            status=CardStatus.FAILED,
            error_message="session过期"
        )
        original_retry_count = card.retry_count
        
        headers = get_auth_headers(self.merchant_admin)
        response = client.post(
            f"/api/v1/cards/{card.id}/retry",
            headers=headers
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data["retry_count"] == original_retry_count + 1
        
        # 验证数据库中的记录也被更新
        self.db.refresh(card)
        assert card.retry_count == original_retry_count + 1
        assert card.status == CardStatus.PENDING
        assert card.error_message is None

    def test_retry_card_clears_previous_data(self):
        """测试重试卡记录会清除之前的绑卡数据"""
        card = self.create_test_card_record(
            status=CardStatus.FAILED,
            error_message="认证失败"
        )
        
        # 设置一些之前的绑卡数据
        card.walmart_ck_id = 123
        card.response_data = {"error": "auth failed"}
        card.process_time = 5.5
        self.db.commit()
        
        headers = get_auth_headers(self.merchant_admin)
        response = client.post(
            f"/api/v1/cards/{card.id}/retry",
            headers=headers
        )
        
        assert response.status_code == 200
        
        # 验证数据被清除
        self.db.refresh(card)
        assert card.walmart_ck_id is None
        assert card.response_data is None
        assert card.process_time is None
        assert card.error_message is None
