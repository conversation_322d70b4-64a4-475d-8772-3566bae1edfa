#!/usr/bin/env python3
"""
新仪表盘功能快速测试脚本

用于快速验证重新设计的仪表盘功能是否正常工作
"""

import sys
import os
import asyncio
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from sqlalchemy.orm import Session
from app.db.session import SessionLocal
from app.services.dashboard_statistics_service import DashboardStatisticsService
from app.models.user import User
from app.models.merchant import Merchant
from app.models.card_record import CardRecord, CardStatus
from app.models.department import Department
from app.models.walmart_ck import WalmartCK


def create_test_user(db: Session) -> User:
    """创建测试用户"""
    # 查找或创建测试商户
    merchant = db.query(Merchant).filter(Merchant.code == "TEST001").first()
    if not merchant:
        merchant = Merchant(
            name="测试商户",
            code="TEST001",
            api_key="test_key",
            api_secret="test_secret",
            status=True
        )
        db.add(merchant)
        db.commit()
        db.refresh(merchant)

    # 查找或创建测试用户
    user = db.query(User).filter(User.username == "test_merchant_admin").first()
    if not user:
        user = User(
            username="test_merchant_admin",
            email="<EMAIL>",
            hashed_password="hashed_password",
            merchant_id=merchant.id,
            is_active=True
        )
        db.add(user)
        db.commit()
        db.refresh(user)

    return user


def test_amount_statistics(db: Session, user: User):
    """测试绑卡金额统计"""
    print("🧪 测试绑卡金额统计...")

    service = DashboardStatisticsService(db)
    start_time, end_time = service._get_time_range("today")

    try:
        stats = service._get_amount_statistics(user.merchant_id, start_time, end_time)

        print(f"✅ 总请求数: {stats['total_requests']}")
        print(f"✅ 成功数量: {stats['success_count']}")
        print(f"✅ 失败数量: {stats['failed_count']}")
        print(f"✅ 总金额: {stats['total_amount_yuan']}元")
        print(f"✅ 成功金额: {stats['success_amount_yuan']}元")
        print(f"✅ 成功率: {stats['success_rate']}%")

        return True
    except Exception as e:
        print(f"❌ 绑卡金额统计测试失败: {e}")
        return False


def test_success_rate_statistics(db: Session, user: User):
    """测试绑卡成功率统计"""
    print("\n🧪 测试绑卡成功率统计...")

    service = DashboardStatisticsService(db)
    start_time, end_time = service._get_time_range("today")

    try:
        stats = service._get_success_rate_statistics(user.merchant_id, start_time, end_time)

        print(f"✅ 总体成功率: {stats['overall_success_rate']}%")
        print(f"✅ 总请求数: {stats['total_requests']}")
        print(f"✅ 总成功数: {stats['total_success']}")
        print(f"✅ 小时分布数据: {len(stats['hourly_breakdown'])}条")

        return True
    except Exception as e:
        print(f"❌ 绑卡成功率统计测试失败: {e}")
        return False


def test_ck_efficiency_statistics(db: Session, user: User):
    """测试CK使用效率统计"""
    print("\n🧪 测试CK使用效率统计...")

    service = DashboardStatisticsService(db)
    start_time, end_time = service._get_time_range("today")

    try:
        stats = service._get_ck_efficiency_statistics(user.merchant_id, start_time, end_time)

        print(f"✅ 总CK数量: {stats['total_ck_count']}")
        print(f"✅ 活跃CK数量: {stats['active_ck_count']}")
        print(f"✅ 总使用次数: {stats['total_usage']}")
        print(f"✅ 平均效率: {stats['average_efficiency']}%")
        print(f"✅ CK详情数量: {len(stats['ck_details'])}")

        return True
    except Exception as e:
        print(f"❌ CK使用效率统计测试失败: {e}")
        return False


def test_failure_statistics(db: Session, user: User):
    """测试异常/失败统计"""
    print("\n🧪 测试异常/失败统计...")

    service = DashboardStatisticsService(db)
    start_time, end_time = service._get_time_range("today")

    try:
        stats = service._get_failure_statistics(user.merchant_id, start_time, end_time)

        print(f"✅ 失败总数: {stats['total_failed_count']}")
        print(f"✅ 失败金额: {stats['total_failed_amount_yuan']}元")
        print(f"✅ 失败状态分布: {len(stats['failure_breakdown'])}种")
        print(f"✅ 错误原因数量: {len(stats['top_error_reasons'])}")

        return True
    except Exception as e:
        print(f"❌ 异常/失败统计测试失败: {e}")
        return False


def test_department_ranking(db: Session, user: User):
    """测试部门业绩排名"""
    print("\n🧪 测试部门业绩排名...")

    service = DashboardStatisticsService(db)
    start_time, end_time = service._get_time_range("today")

    try:
        ranking = service._get_department_ranking(user.merchant_id, start_time, end_time)

        print(f"✅ 部门排名数量: {len(ranking)}")

        if ranking:
            top_dept = ranking[0]
            print(f"✅ 第一名部门: {top_dept['department_name']}")
            print(f"✅ 成功金额: {top_dept['success_amount_yuan']}元")
            print(f"✅ 成功率: {top_dept['success_rate']}%")

        return True
    except Exception as e:
        print(f"❌ 部门业绩排名测试失败: {e}")
        return False


def test_new_dashboard_statistics(db: Session, user: User):
    """测试新版仪表盘统计"""
    print("\n🧪 测试新版仪表盘统计...")

    service = DashboardStatisticsService(db)

    try:
        stats = service.get_dashboard_statistics(user, user.merchant_id, "today")

        print(f"✅ 时间范围: {stats['time_range']}")
        print(f"✅ 包含金额统计: {'amount_statistics' in stats}")
        print(f"✅ 包含成功率统计: {'success_rate_statistics' in stats}")
        print(f"✅ 包含CK效率统计: {'ck_efficiency_statistics' in stats}")
        print(f"✅ 包含失败统计: {'failure_statistics' in stats}")
        print(f"✅ 包含部门排名: {'department_ranking' in stats}")
        print(f"✅ 生成时间: {stats['generated_at']}")

        return True
    except Exception as e:
        print(f"❌ 新版仪表盘统计测试失败: {e}")
        return False


def main():
    """主测试函数"""
    print("🚀 开始新仪表盘功能快速测试...")
    print("=" * 60)

    # 获取数据库连接
    db = SessionLocal()

    try:
        # 创建测试用户
        user = create_test_user(db)
        print(f"📝 使用测试用户: {user.username} (商户ID: {user.merchant_id})")

        # 运行所有测试
        tests = [
            test_amount_statistics,
            test_success_rate_statistics,
            test_ck_efficiency_statistics,
            test_failure_statistics,
            test_department_ranking,
            test_new_dashboard_statistics
        ]

        passed = 0
        total = len(tests)

        for test_func in tests:
            if test_func(db, user):
                passed += 1

        print("\n" + "=" * 60)
        print(f"📊 测试结果: {passed}/{total} 通过")

        if passed == total:
            print("🎉 所有测试通过！新仪表盘功能正常工作。")
            return True
        else:
            print(f"⚠️  有 {total - passed} 个测试失败，请检查相关功能。")
            return False

    except Exception as e:
        print(f"💥 测试过程中发生错误: {e}")
        return False
    finally:
        db.close()


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
