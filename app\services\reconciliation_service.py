"""
对账台业务逻辑服务类
负责处理部门统计、CK统计、绑卡记录查询等业务逻辑
"""
from typing import Optional, Any, Dict, Tuple, List
from sqlalchemy.orm import Session
from sqlalchemy import func, and_, or_, desc
from fastapi import HTTPException, status
from datetime import datetime, date, timedelta
import io
import pandas as pd

from app.models.user import User
from app.models.card_record import CardRecord, CardStatus
from app.models.department import Department
from app.models.walmart_ck import WalmartCK
from app.models.merchant import Merchant
from app.services.permission_service import PermissionService
from app.core.logging import get_logger

logger = get_logger(__name__)


class ReconciliationService:
    """对账台业务逻辑服务类"""

    def __init__(self, db: Session):
        self.db = db
        self.permission_service = PermissionService(db)
        self.logger = get_logger("reconciliation_service")

    def _check_permission(self, current_user: User, permission: str) -> None:
        """检查用户权限"""
        has_permission = self.permission_service.check_user_permission(
            current_user, permission
        )
        if not has_permission:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail=f"没有{permission}权限",
            )

    def _parse_time_range(self, time_range: str, start_date: Optional[date], end_date: Optional[date]) -> Tuple[datetime, datetime]:
        """解析时间范围"""
        now = datetime.now()
        today = now.date()
        
        if time_range == "custom" and start_date and end_date:
            return (
                datetime.combine(start_date, datetime.min.time()),
                datetime.combine(end_date, datetime.max.time())
            )
        elif time_range == "today":
            return (
                datetime.combine(today, datetime.min.time()),
                datetime.combine(today, datetime.max.time())
            )
        elif time_range == "yesterday":
            yesterday = today - timedelta(days=1)
            return (
                datetime.combine(yesterday, datetime.min.time()),
                datetime.combine(yesterday, datetime.max.time())
            )
        elif time_range == "week":
            # 本周（周一到今天）
            days_since_monday = today.weekday()
            monday = today - timedelta(days=days_since_monday)
            return (
                datetime.combine(monday, datetime.min.time()),
                datetime.combine(today, datetime.max.time())
            )
        elif time_range == "month":
            # 本月
            first_day = today.replace(day=1)
            return (
                datetime.combine(first_day, datetime.min.time()),
                datetime.combine(today, datetime.max.time())
            )
        else:
            # 默认今天
            return (
                datetime.combine(today, datetime.min.time()),
                datetime.combine(today, datetime.max.time())
            )

    def _apply_data_isolation(self, query, current_user: User):
        """应用数据隔离规则"""
        if current_user.is_superuser:
            return query
        
        # 非超级管理员只能查看自己商户的数据
        if current_user.merchant_id:
            query = query.filter(CardRecord.merchant_id == current_user.merchant_id)
        
        return query

    def _mask_card_number(self, card_number: str) -> str:
        """脱敏卡号"""
        if not card_number or len(card_number) <= 8:
            return card_number
        return card_number[:4] + "***" + card_number[-4:]

    def _mask_ck_sign(self, ck_sign: str) -> str:
        """脱敏CK标识"""
        if not ck_sign:
            return ck_sign
        
        # 提取用户部分进行脱敏
        parts = ck_sign.split('@')
        if len(parts) >= 2:
            user_part = parts[0]
            if len(user_part) > 8:
                masked_user = user_part[:4] + "***" + user_part[-4:]
                return masked_user + "@" + "***"
        
        return "user_***_sign"

    async def get_department_statistics(
        self,
        current_user: User,
        page: int = 1,
        page_size: int = 20,
        parent_department_id: Optional[int] = None,
        level: Optional[int] = None,
        merchant_id: Optional[int] = None,
        start_date: Optional[date] = None,
        end_date: Optional[date] = None,
        time_range: str = "today",
    ) -> Dict[str, Any]:
        """获取部门绑卡统计数据"""

        # 解析时间范围
        start_datetime, end_datetime = self._parse_time_range(time_range, start_date, end_date)

        # 构建部门查询
        dept_query = self.db.query(Department)

        # 应用数据隔离
        if not current_user.is_superuser and current_user.merchant_id:
            dept_query = dept_query.filter(Department.merchant_id == current_user.merchant_id)
        elif merchant_id:
            dept_query = dept_query.filter(Department.merchant_id == merchant_id)

        # 根据层级筛选部门
        if parent_department_id:
            dept_query = dept_query.filter(Department.parent_id == parent_department_id)
        else:
            # 查询一级部门（没有父部门的部门）
            dept_query = dept_query.filter(Department.parent_id.is_(None))

        if level:
            dept_query = dept_query.filter(Department.level == level)

        # 只查询启用的部门
        dept_query = dept_query.filter(Department.status == True)

        # 获取部门列表
        all_departments = dept_query.all()

        # 【修复】应用部门数据权限过滤
        departments = self._filter_departments_by_permission(all_departments, current_user)
        
        # 构建统计查询
        statistics = []
        for dept in departments:
            # 查询该部门及其子部门的绑卡统计
            dept_ids = [dept.id] + dept.get_all_children_ids()
            
            # 统计成功绑卡数据
            success_stats = self.db.query(
                func.count(CardRecord.id).label('success_count'),
                func.sum(CardRecord.actual_amount).label('success_amount')
            ).filter(
                and_(
                    CardRecord.department_id.in_(dept_ids),
                    CardRecord.status == CardStatus.SUCCESS,
                    CardRecord.actual_amount.isnot(None),
                    CardRecord.created_at >= start_datetime,
                    CardRecord.created_at <= end_datetime
                )
            ).first()
            
            # 获取商户信息
            merchant = self.db.query(Merchant).filter(Merchant.id == dept.merchant_id).first()
            
            statistics.append({
                "id": dept.id,
                "name": dept.name,
                "merchantName": merchant.name if merchant else "未知商户",
                "type": "department",
                "level": dept.level,
                "hasChildren": len(dept.get_all_children_ids()) > 0,
                "successCount": success_stats.success_count or 0,
                "successAmount": success_stats.success_amount or 0,
                "parentDepartment": dept.parent.name if dept.parent else None,
            })
        
        # 分页处理
        total = len(statistics)
        start_idx = (page - 1) * page_size
        end_idx = start_idx + page_size
        paginated_data = statistics[start_idx:end_idx]
        
        return {
            "data": paginated_data,
            "total": total,
            "page": page,
            "pageSize": page_size,
            "totalPages": (total + page_size - 1) // page_size
        }

    async def get_department_ck_statistics(
        self,
        current_user: User,
        department_id: int,
        page: int = 1,
        page_size: int = 20,
        start_date: Optional[date] = None,
        end_date: Optional[date] = None,
        time_range: str = "today",
        min_success_count: Optional[int] = 1,
        ck_status: str = "all",
        mask_sensitive_data: bool = True,
    ) -> Dict[str, Any]:
        """获取部门下CK的统计数据"""

        # 验证部门权限
        department = self.db.query(Department).filter(Department.id == department_id).first()
        if not department:
            raise HTTPException(status_code=404, detail="部门不存在")

        # 【修复】应用部门数据权限检查
        if not current_user.is_superuser:
            # 首先检查商户隔离
            if current_user.merchant_id != department.merchant_id:
                raise HTTPException(status_code=403, detail="无权限访问该部门数据")

            # 然后检查部门数据权限
            permission_service = PermissionService(self.db)
            if not permission_service.can_access_department_data(current_user, department_id):
                raise HTTPException(status_code=403, detail="无权限访问该部门数据")
        
        # 解析时间范围
        start_datetime, end_datetime = self._parse_time_range(time_range, start_date, end_date)
        
        # 获取部门及其子部门的所有CK
        dept_ids = [department_id] + department.get_all_children_ids()
        
        ck_query = self.db.query(WalmartCK).filter(
            WalmartCK.department_id.in_(dept_ids)
        )
        
        # CK状态筛选
        if ck_status == "active":
            ck_query = ck_query.filter(WalmartCK.active == True)
        elif ck_status == "inactive":
            ck_query = ck_query.filter(WalmartCK.active == False)
        
        cks = ck_query.all()
        
        # 统计每个CK的绑卡数据
        ck_statistics = []
        for ck in cks:
            # 统计该CK的成功绑卡数据
            success_stats = self.db.query(
                func.count(CardRecord.id).label('success_count'),
                func.sum(CardRecord.actual_amount).label('success_amount')
            ).filter(
                and_(
                    CardRecord.walmart_ck_id == ck.id,
                    CardRecord.status == CardStatus.SUCCESS,
                    CardRecord.actual_amount.isnot(None),
                    CardRecord.created_at >= start_datetime,
                    CardRecord.created_at <= end_datetime
                )
            ).first()
            
            success_count = success_stats.success_count or 0
            success_amount = success_stats.success_amount or 0
            
            # 应用最小成功笔数筛选
            if min_success_count and success_count < min_success_count:
                continue
            
            # 获取CK所属部门信息
            ck_dept = self.db.query(Department).filter(Department.id == ck.department_id).first()
            merchant = self.db.query(Merchant).filter(Merchant.id == ck.merchant_id).first()
            
            ck_statistics.append({
                "ckId": ck.id,
                "ckSign": self._mask_ck_sign(ck.sign) if mask_sensitive_data else ck.sign,
                "ckDescription": ck.description,
                "departmentName": ck_dept.name if ck_dept else "未知部门",
                "merchantName": merchant.name if merchant else "未知商户",
                "active": ck.active,
                "successCount": success_count,
                "successAmount": success_amount,
            })
        
        # 按成功金额降序排序
        ck_statistics.sort(key=lambda x: x["successAmount"], reverse=True)
        
        # 分页处理
        total = len(ck_statistics)
        start_idx = (page - 1) * page_size
        end_idx = start_idx + page_size
        paginated_data = ck_statistics[start_idx:end_idx]
        
        return {
            "data": paginated_data,
            "total": total,
            "page": page,
            "pageSize": page_size,
            "totalPages": (total + page_size - 1) // page_size
        }

    async def get_ck_binding_records(
        self,
        current_user: User,
        ck_id: int,
        page: int = 1,
        page_size: int = 20,
        start_date: Optional[date] = None,
        end_date: Optional[date] = None,
        time_range: str = "today",
        search_keyword: Optional[str] = None,
        mask_sensitive_data: bool = True,
    ) -> Dict[str, Any]:
        """获取CK的成功绑卡记录明细"""

        # 验证CK权限
        ck = self.db.query(WalmartCK).filter(WalmartCK.id == ck_id).first()
        if not ck:
            raise HTTPException(status_code=404, detail="CK不存在")

        # 数据隔离检查
        if not current_user.is_superuser and current_user.merchant_id != ck.merchant_id:
            raise HTTPException(status_code=403, detail="无权限访问该CK数据")

        # 解析时间范围
        start_datetime, end_datetime = self._parse_time_range(time_range, start_date, end_date)

        # 构建查询
        query = self.db.query(CardRecord).filter(
            and_(
                CardRecord.walmart_ck_id == ck_id,
                CardRecord.status == CardStatus.SUCCESS,
                CardRecord.actual_amount.isnot(None),
                CardRecord.created_at >= start_datetime,
                CardRecord.created_at <= end_datetime
            )
        )

        # 搜索筛选
        if search_keyword:
            search_filter = or_(
                CardRecord.merchant_order_id.ilike(f"%{search_keyword}%"),
                CardRecord.card_number.ilike(f"%{search_keyword}%")
            )
            query = query.filter(search_filter)

        # 按创建时间降序排序
        query = query.order_by(desc(CardRecord.created_at))

        # 分页
        total = query.count()
        records = query.offset((page - 1) * page_size).limit(page_size).all()

        # 格式化数据
        record_list = []
        for record in records:
            record_list.append({
                "id": str(record.id),
                "merchantOrderId": record.merchant_order_id,
                "cardNumber": self._mask_card_number(record.card_number) if mask_sensitive_data else record.card_number,
                "amount": record.amount,
                "actualAmount": record.actual_amount,
                "createdAt": record.created_at.isoformat() if record.created_at else None,
                "callbackStatus": record.callback_status,
            })

        return {
            "data": record_list,
            "total": total,
            "page": page,
            "pageSize": page_size,
            "totalPages": (total + page_size - 1) // page_size
        }

    async def export_department_statistics(
        self,
        current_user: User,
        parent_department_id: Optional[int] = None,
        level: Optional[int] = None,
        merchant_id: Optional[int] = None,
        start_date: Optional[date] = None,
        end_date: Optional[date] = None,
        time_range: str = "today",
    ) -> bytes:
        """导出部门统计数据为Excel"""

        # 获取所有数据（不分页）
        stats_data = await self.get_department_statistics(
            current_user=current_user,
            page=1,
            page_size=10000,  # 大数值获取所有数据
            parent_department_id=parent_department_id,
            level=level,
            merchant_id=merchant_id,
            start_date=start_date,
            end_date=end_date,
            time_range=time_range,
        )

        # 转换为DataFrame
        df_data = []
        for item in stats_data["data"]:
            df_data.append({
                "部门名称": item["name"],
                "商户名称": item["merchantName"],
                "上级部门": item.get("parentDepartment", ""),
                "成功笔数": item["successCount"],
                "成功金额(分)": item["successAmount"],
                "成功金额(元)": item["successAmount"] / 100 if item["successAmount"] else 0,
                "部门层级": item["level"],
                "是否有子部门": "是" if item["hasChildren"] else "否",
            })

        df = pd.DataFrame(df_data)

        # 生成Excel文件
        output = io.BytesIO()
        with pd.ExcelWriter(output, engine='openpyxl') as writer:
            df.to_excel(writer, sheet_name='部门统计', index=False)

        return output.getvalue()

    async def export_ck_statistics(
        self,
        current_user: User,
        department_id: int,
        start_date: Optional[date] = None,
        end_date: Optional[date] = None,
        time_range: str = "today",
        min_success_count: Optional[int] = 1,
        ck_status: str = "all",
    ) -> bytes:
        """导出CK统计数据为Excel"""

        # 获取所有数据（不分页，不脱敏）
        stats_data = await self.get_department_ck_statistics(
            current_user=current_user,
            department_id=department_id,
            page=1,
            page_size=10000,  # 大数值获取所有数据
            start_date=start_date,
            end_date=end_date,
            time_range=time_range,
            min_success_count=min_success_count,
            ck_status=ck_status,
            mask_sensitive_data=False,  # 导出时不脱敏
        )

        # 转换为DataFrame
        df_data = []
        for item in stats_data["data"]:
            df_data.append({
                "CK标识": item["ckSign"],
                "CK描述": item.get("ckDescription", ""),
                "部门名称": item["departmentName"],
                "商户名称": item["merchantName"],
                "状态": "启用" if item["active"] else "禁用",
                "成功笔数": item["successCount"],
                "成功金额(分)": item["successAmount"],
                "成功金额(元)": item["successAmount"] / 100 if item["successAmount"] else 0,
            })

        df = pd.DataFrame(df_data)

        # 生成Excel文件
        output = io.BytesIO()
        with pd.ExcelWriter(output, engine='openpyxl') as writer:
            df.to_excel(writer, sheet_name='CK统计', index=False)

        return output.getvalue()

    async def export_binding_records(
        self,
        current_user: User,
        ck_id: int,
        start_date: Optional[date] = None,
        end_date: Optional[date] = None,
        time_range: str = "today",
        search_keyword: Optional[str] = None,
    ) -> bytes:
        """导出绑卡记录为Excel"""

        # 获取所有数据（不分页，不脱敏）
        records_data = await self.get_ck_binding_records(
            current_user=current_user,
            ck_id=ck_id,
            page=1,
            page_size=10000,  # 大数值获取所有数据
            start_date=start_date,
            end_date=end_date,
            time_range=time_range,
            search_keyword=search_keyword,
            mask_sensitive_data=False,  # 导出时不脱敏
        )

        # 转换为DataFrame
        df_data = []
        for item in records_data["data"]:
            df_data.append({
                "订单号": item["merchantOrderId"],
                "卡号": item["cardNumber"],
                "订单金额(分)": item["amount"],
                "订单金额(元)": item["amount"] / 100 if item["amount"] else 0,
                "实际金额(分)": item["actualAmount"],
                "实际金额(元)": item["actualAmount"] / 100 if item["actualAmount"] else 0,
                "绑卡时间": item["createdAt"],
                "回调状态": item["callbackStatus"],
            })

        df = pd.DataFrame(df_data)

        # 生成Excel文件
        output = io.BytesIO()
        with pd.ExcelWriter(output, engine='openpyxl') as writer:
            df.to_excel(writer, sheet_name='绑卡记录', index=False)

        return output.getvalue()

    def _filter_departments_by_permission(self, departments: List[Department], current_user: User) -> List[Department]:
        """根据用户的部门数据权限过滤部门列表"""
        try:
            # 超级管理员可以访问所有部门
            if current_user.is_superuser:
                return departments

            from app.services.permission_service import PermissionService
            permission_service = PermissionService(self.db)

            # 检查用户的部门数据权限
            if permission_service.check_data_permission(current_user, 'data:department:all'):
                # 有访问本商户所有部门数据的权限
                return [dept for dept in departments if dept.merchant_id == current_user.merchant_id]
            elif permission_service.check_data_permission(current_user, 'data:department:sub'):
                # 有访问本部门及子部门数据的权限
                accessible_departments = []
                for dept in departments:
                    if permission_service.can_access_department_data(current_user, dept.id):
                        accessible_departments.append(dept)
                return accessible_departments
            elif permission_service.check_data_permission(current_user, 'data:department:own'):
                # 只有访问本部门数据的权限
                if hasattr(current_user, 'department_id') and current_user.department_id:
                    return [dept for dept in departments if dept.id == current_user.department_id]
                return []
            else:
                # 没有部门数据权限，返回空列表
                logger.warning(f"用户 {current_user.id} 没有部门数据权限，无法访问对账台数据")
                return []

        except Exception as e:
            logger.error(f"过滤部门权限失败: {e}")
            # 出错时返回空列表，确保安全
            return []
