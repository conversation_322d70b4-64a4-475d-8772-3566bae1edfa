"""
Telegram Bot 异常定义
"""


class TelegramBotError(Exception):
    """Telegram机器人基础异常"""
    pass


class BotServiceError(TelegramBotError):
    """机器人服务异常"""
    pass


class ConfigurationError(TelegramBotError):
    """配置错误异常"""
    pass


class AuthenticationError(TelegramBotError):
    """认证错误异常"""
    pass


class PermissionError(TelegramBotError):
    """权限错误异常"""
    pass


class RateLimitError(TelegramBotError):
    """频率限制异常"""
    pass


class GroupNotBoundError(TelegramBotError):
    """群组未绑定异常"""
    pass


class UserNotVerifiedError(TelegramBotError):
    """用户未验证异常"""
    pass


class InvalidTokenError(TelegramBotError):
    """无效令牌异常"""
    pass


class WebhookError(TelegramBotError):
    """Webhook异常"""
    pass


class CommandError(TelegramBotError):
    """命令处理异常"""
    pass


class DataAccessError(TelegramBotError):
    """数据访问异常"""
    pass
