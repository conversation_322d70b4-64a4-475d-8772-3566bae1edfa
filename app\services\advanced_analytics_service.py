"""
高级数据分析服务
提供多维度数据透视、趋势分析和预测、用户行为漏斗分析等功能
"""

import json
import numpy as np
import pandas as pd
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime, timedelta
from collections import defaultdict, Counter
from sqlalchemy.orm import Session
from sqlalchemy import func, and_, or_, desc, text

from app.models.audit import AuditLog
from app.models.user import User
from app.models.merchant import Merchant
from app.core.logging import get_logger

logger = get_logger("advanced_analytics")


class AdvancedAnalyticsService:
    """高级数据分析服务"""

    def __init__(self, db: Session):
        self.db = db

    async def get_multidimensional_analysis(
        self,
        dimensions: List[str],
        metrics: List[str],
        filters: Optional[Dict[str, Any]] = None,
        time_range: Optional[Tuple[datetime, datetime]] = None
    ) -> Dict[str, Any]:
        """多维度数据透视分析"""
        
        # 构建基础查询
        query = self.db.query(AuditLog)
        
        # 应用时间范围过滤
        if time_range:
            start_time, end_time = time_range
            query = query.filter(AuditLog.created_at.between(start_time, end_time))
        
        # 应用其他过滤条件
        if filters:
            for key, value in filters.items():
                if hasattr(AuditLog, key):
                    if isinstance(value, list):
                        query = query.filter(getattr(AuditLog, key).in_(value))
                    else:
                        query = query.filter(getattr(AuditLog, key) == value)
        
        logs = query.all()
        
        # 转换为DataFrame进行分析
        data = []
        for log in logs:
            row = {
                'user_id': log.user_id,
                'merchant_id': log.merchant_id,
                'event_type': log.event_type,
                'action': log.action,
                'resource_type': log.resource_type,
                'level': log.level,
                'created_at': log.created_at,
                'hour': log.created_at.hour,
                'weekday': log.created_at.weekday(),
                'date': log.created_at.date()
            }
            data.append(row)
        
        df = pd.DataFrame(data)
        
        if df.empty:
            return {"error": "No data found for the specified criteria"}
        
        # 执行多维度分析
        analysis_result = {}
        
        # 按维度分组统计
        for dimension in dimensions:
            if dimension in df.columns:
                dimension_analysis = self._analyze_dimension(df, dimension, metrics)
                analysis_result[dimension] = dimension_analysis
        
        # 交叉维度分析
        if len(dimensions) >= 2:
            cross_analysis = self._cross_dimension_analysis(df, dimensions[:2], metrics)
            analysis_result['cross_analysis'] = cross_analysis
        
        # 时间序列分析
        time_series = self._time_series_analysis(df, metrics)
        analysis_result['time_series'] = time_series
        
        return {
            "dimensions": dimensions,
            "metrics": metrics,
            "total_records": len(df),
            "analysis": analysis_result,
            "summary": self._generate_analysis_summary(analysis_result)
        }

    async def get_trend_analysis_and_prediction(
        self,
        metric: str,
        time_period: str = "daily",
        prediction_days: int = 7
    ) -> Dict[str, Any]:
        """趋势分析和预测"""
        
        # 获取历史数据
        end_time = datetime.utcnow()
        start_time = end_time - timedelta(days=30)  # 获取30天历史数据
        
        query = self.db.query(AuditLog).filter(
            AuditLog.created_at.between(start_time, end_time)
        )
        
        logs = query.all()
        
        # 按时间周期聚合数据
        time_series_data = self._aggregate_by_time_period(logs, time_period, metric)
        
        # 趋势分析
        trend_analysis = self._analyze_trend(time_series_data)
        
        # 预测
        predictions = self._predict_future_values(time_series_data, prediction_days)
        
        # 季节性分析
        seasonality = self._analyze_seasonality(time_series_data)
        
        return {
            "metric": metric,
            "time_period": time_period,
            "historical_data": time_series_data,
            "trend_analysis": trend_analysis,
            "predictions": predictions,
            "seasonality": seasonality,
            "insights": self._generate_trend_insights(trend_analysis, predictions)
        }

    async def get_user_behavior_funnel(
        self,
        funnel_steps: List[str],
        time_range: Optional[Tuple[datetime, datetime]] = None
    ) -> Dict[str, Any]:
        """用户行为漏斗分析"""
        
        # 构建查询
        query = self.db.query(AuditLog)
        
        if time_range:
            start_time, end_time = time_range
            query = query.filter(AuditLog.created_at.between(start_time, end_time))
        
        # 只获取相关的行为数据
        query = query.filter(AuditLog.action.in_(funnel_steps))
        logs = query.all()
        
        # 按用户分组分析行为路径
        user_journeys = defaultdict(list)
        for log in logs:
            if log.user_id:
                user_journeys[log.user_id].append({
                    'action': log.action,
                    'timestamp': log.created_at,
                    'resource_type': log.resource_type
                })
        
        # 排序用户行为
        for user_id in user_journeys:
            user_journeys[user_id].sort(key=lambda x: x['timestamp'])
        
        # 计算漏斗转化
        funnel_data = self._calculate_funnel_conversion(user_journeys, funnel_steps)
        
        # 用户路径分析
        path_analysis = self._analyze_user_paths(user_journeys, funnel_steps)
        
        # 流失点分析
        churn_analysis = self._analyze_churn_points(user_journeys, funnel_steps)
        
        return {
            "funnel_steps": funnel_steps,
            "total_users": len(user_journeys),
            "funnel_data": funnel_data,
            "conversion_rates": self._calculate_conversion_rates(funnel_data),
            "path_analysis": path_analysis,
            "churn_analysis": churn_analysis,
            "recommendations": self._generate_funnel_recommendations(funnel_data, churn_analysis)
        }

    async def get_cohort_analysis(
        self,
        cohort_type: str = "monthly",
        metric: str = "retention"
    ) -> Dict[str, Any]:
        """队列分析"""
        
        # 获取用户首次活动时间
        first_activity_query = self.db.query(
            AuditLog.user_id,
            func.min(AuditLog.created_at).label('first_activity')
        ).group_by(AuditLog.user_id)
        
        first_activities = {row.user_id: row.first_activity for row in first_activity_query.all()}
        
        # 获取所有用户活动
        all_activities = self.db.query(AuditLog).filter(
            AuditLog.user_id.in_(list(first_activities.keys()))
        ).all()
        
        # 构建队列数据
        cohort_data = self._build_cohort_data(all_activities, first_activities, cohort_type)
        
        # 计算队列指标
        cohort_metrics = self._calculate_cohort_metrics(cohort_data, metric)
        
        return {
            "cohort_type": cohort_type,
            "metric": metric,
            "cohort_data": cohort_data,
            "cohort_metrics": cohort_metrics,
            "insights": self._generate_cohort_insights(cohort_metrics)
        }

    async def get_advanced_segmentation(
        self,
        segmentation_criteria: Dict[str, Any]
    ) -> Dict[str, Any]:
        """高级用户分群分析"""
        
        # 获取用户活动数据
        query = self.db.query(AuditLog)
        
        # 应用时间范围
        if 'time_range' in segmentation_criteria:
            start_time, end_time = segmentation_criteria['time_range']
            query = query.filter(AuditLog.created_at.between(start_time, end_time))
        
        logs = query.all()
        
        # 计算用户特征
        user_features = self._calculate_user_features(logs)
        
        # 执行分群
        segments = self._perform_segmentation(user_features, segmentation_criteria)
        
        # 分群特征分析
        segment_analysis = self._analyze_segments(segments, user_features)
        
        return {
            "segmentation_criteria": segmentation_criteria,
            "total_users": len(user_features),
            "segments": segments,
            "segment_analysis": segment_analysis,
            "recommendations": self._generate_segmentation_recommendations(segment_analysis)
        }

    # 私有辅助方法
    def _analyze_dimension(self, df: pd.DataFrame, dimension: str, metrics: List[str]) -> Dict[str, Any]:
        """分析单个维度"""
        result = {}

        grouped = df.groupby(dimension)

        for metric in metrics:
            if metric == 'count':
                result[metric] = grouped.size().to_dict()
            elif metric == 'unique_users':
                result[metric] = grouped['user_id'].nunique().to_dict()
            elif metric == 'avg_per_user':
                user_counts = grouped['user_id'].value_counts()
                result[metric] = user_counts.groupby(level=0).mean().to_dict()

        return result

    def _cross_dimension_analysis(self, df: pd.DataFrame, dimensions: List[str], metrics: List[str]) -> Dict[str, Any]:
        """交叉维度分析"""
        result = {}

        grouped = df.groupby(dimensions)

        for metric in metrics:
            if metric == 'count':
                cross_table = grouped.size().unstack(fill_value=0)
                result[metric] = cross_table.to_dict()
            elif metric == 'unique_users':
                cross_table = grouped['user_id'].nunique().unstack(fill_value=0)
                result[metric] = cross_table.to_dict()

        return result

    def _time_series_analysis(self, df: pd.DataFrame, metrics: List[str]) -> Dict[str, Any]:
        """时间序列分析"""
        result = {}

        # 按日期分组
        daily_grouped = df.groupby('date')

        for metric in metrics:
            if metric == 'count':
                result[f'daily_{metric}'] = daily_grouped.size().to_dict()
            elif metric == 'unique_users':
                result[f'daily_{metric}'] = daily_grouped['user_id'].nunique().to_dict()

        # 按小时分组
        hourly_grouped = df.groupby('hour')
        for metric in metrics:
            if metric == 'count':
                result[f'hourly_{metric}'] = hourly_grouped.size().to_dict()

        return result

    def _generate_analysis_summary(self, analysis_result: Dict[str, Any]) -> Dict[str, Any]:
        """生成分析摘要"""
        summary = {
            "key_insights": [],
            "top_dimensions": {},
            "patterns": []
        }

        # 分析各维度的top值
        for dimension, data in analysis_result.items():
            if isinstance(data, dict) and 'count' in data:
                top_values = sorted(data['count'].items(), key=lambda x: x[1], reverse=True)[:5]
                summary["top_dimensions"][dimension] = top_values

        return summary

    def _aggregate_by_time_period(self, logs: List, time_period: str, metric: str) -> List[Dict[str, Any]]:
        """按时间周期聚合数据"""
        data = defaultdict(int)

        for log in logs:
            if time_period == "daily":
                key = log.created_at.date()
            elif time_period == "hourly":
                key = log.created_at.replace(minute=0, second=0, microsecond=0)
            elif time_period == "weekly":
                # 获取周的开始日期
                days_since_monday = log.created_at.weekday()
                key = (log.created_at.date() - timedelta(days=days_since_monday))
            else:
                key = log.created_at.date()

            data[key] += 1

        # 转换为列表格式
        result = []
        for date, count in sorted(data.items()):
            result.append({
                "date": date.isoformat() if hasattr(date, 'isoformat') else str(date),
                "value": count
            })

        return result

    def _analyze_trend(self, time_series_data: List[Dict[str, Any]]) -> Dict[str, Any]:
        """分析趋势"""
        if len(time_series_data) < 2:
            return {"trend": "insufficient_data"}

        values = [item["value"] for item in time_series_data]

        # 计算趋势方向
        if len(values) >= 3:
            recent_avg = np.mean(values[-3:])
            earlier_avg = np.mean(values[:-3]) if len(values) > 3 else values[0]

            if recent_avg > earlier_avg * 1.1:
                trend_direction = "increasing"
            elif recent_avg < earlier_avg * 0.9:
                trend_direction = "decreasing"
            else:
                trend_direction = "stable"
        else:
            trend_direction = "stable"

        # 计算变化率
        if len(values) >= 2:
            change_rate = ((values[-1] - values[0]) / max(values[0], 1)) * 100
        else:
            change_rate = 0

        # 计算波动性
        volatility = np.std(values) / max(np.mean(values), 1) if len(values) > 1 else 0

        return {
            "trend_direction": trend_direction,
            "change_rate": round(change_rate, 2),
            "volatility": round(volatility, 3),
            "min_value": min(values),
            "max_value": max(values),
            "avg_value": round(np.mean(values), 2)
        }

    def _predict_future_values(self, time_series_data: List[Dict[str, Any]], prediction_days: int) -> List[Dict[str, Any]]:
        """预测未来值（简单线性预测）"""
        if len(time_series_data) < 3:
            return []

        values = [item["value"] for item in time_series_data]

        # 简单线性回归预测
        x = np.arange(len(values))
        coeffs = np.polyfit(x, values, 1)

        predictions = []
        last_date = datetime.fromisoformat(time_series_data[-1]["date"])

        for i in range(1, prediction_days + 1):
            predicted_value = max(0, coeffs[0] * (len(values) + i - 1) + coeffs[1])
            future_date = last_date + timedelta(days=i)

            predictions.append({
                "date": future_date.date().isoformat(),
                "predicted_value": round(predicted_value, 2),
                "confidence": max(0.5, 1 - (i * 0.1))  # 简单的置信度计算
            })

        return predictions

    def _analyze_seasonality(self, time_series_data: List[Dict[str, Any]]) -> Dict[str, Any]:
        """分析季节性模式"""
        if len(time_series_data) < 7:
            return {"seasonality": "insufficient_data"}

        # 按星期几分组分析
        weekday_data = defaultdict(list)
        for item in time_series_data:
            date = datetime.fromisoformat(item["date"])
            weekday = date.weekday()
            weekday_data[weekday].append(item["value"])

        weekday_averages = {}
        for weekday, values in weekday_data.items():
            weekday_averages[weekday] = np.mean(values)

        # 检测周期性模式
        weekday_names = ["Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday", "Sunday"]
        weekly_pattern = []
        for i in range(7):
            weekly_pattern.append({
                "weekday": weekday_names[i],
                "average_value": round(weekday_averages.get(i, 0), 2)
            })

        return {
            "weekly_pattern": weekly_pattern,
            "has_weekly_seasonality": max(weekday_averages.values()) > min(weekday_averages.values()) * 1.5
        }

    def _generate_trend_insights(self, trend_analysis: Dict[str, Any], predictions: List[Dict[str, Any]]) -> List[str]:
        """生成趋势洞察"""
        insights = []

        if trend_analysis["trend_direction"] == "increasing":
            insights.append(f"数据呈上升趋势，变化率为 {trend_analysis['change_rate']}%")
        elif trend_analysis["trend_direction"] == "decreasing":
            insights.append(f"数据呈下降趋势，变化率为 {trend_analysis['change_rate']}%")
        else:
            insights.append("数据保持相对稳定")

        if trend_analysis["volatility"] > 0.5:
            insights.append("数据波动较大，需要关注异常情况")

        if predictions:
            avg_prediction = np.mean([p["predicted_value"] for p in predictions])
            insights.append(f"预测未来平均值约为 {avg_prediction:.2f}")

        return insights

    def _calculate_funnel_conversion(self, user_journeys: Dict, funnel_steps: List[str]) -> List[Dict[str, Any]]:
        """计算漏斗转化"""
        funnel_data = []

        for i, step in enumerate(funnel_steps):
            users_at_step = set()

            for user_id, journey in user_journeys.items():
                # 检查用户是否完成了当前步骤
                user_actions = [action['action'] for action in journey]
                if step in user_actions:
                    # 检查是否按顺序完成了前面的步骤
                    if i == 0 or all(prev_step in user_actions for prev_step in funnel_steps[:i]):
                        users_at_step.add(user_id)

            funnel_data.append({
                "step": step,
                "step_index": i,
                "users_count": len(users_at_step),
                "users": list(users_at_step)
            })

        return funnel_data

    def _calculate_conversion_rates(self, funnel_data: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """计算转化率"""
        conversion_rates = []

        for i, step_data in enumerate(funnel_data):
            if i == 0:
                conversion_rate = 100.0  # 第一步转化率为100%
            else:
                prev_users = funnel_data[i-1]["users_count"]
                current_users = step_data["users_count"]
                conversion_rate = (current_users / max(prev_users, 1)) * 100

            conversion_rates.append({
                "step": step_data["step"],
                "conversion_rate": round(conversion_rate, 2),
                "users_count": step_data["users_count"]
            })

        return conversion_rates

    def _analyze_user_paths(self, user_journeys: Dict, funnel_steps: List[str]) -> Dict[str, Any]:
        """分析用户路径"""
        path_patterns = defaultdict(int)

        for user_id, journey in user_journeys.items():
            # 提取用户的行为序列
            actions = [action['action'] for action in journey if action['action'] in funnel_steps]
            path = " -> ".join(actions[:5])  # 只取前5个步骤
            path_patterns[path] += 1

        # 获取最常见的路径
        common_paths = sorted(path_patterns.items(), key=lambda x: x[1], reverse=True)[:10]

        return {
            "total_unique_paths": len(path_patterns),
            "common_paths": [{"path": path, "count": count} for path, count in common_paths]
        }

    def _analyze_churn_points(self, user_journeys: Dict, funnel_steps: List[str]) -> Dict[str, Any]:
        """分析流失点"""
        step_completion = defaultdict(int)
        step_dropoff = defaultdict(int)

        for user_id, journey in user_journeys.items():
            user_actions = [action['action'] for action in journey]

            # 检查每个步骤的完成情况
            for i, step in enumerate(funnel_steps):
                if step in user_actions:
                    step_completion[step] += 1
                else:
                    # 如果用户完成了前面的步骤但没完成当前步骤，记录为流失
                    if i > 0 and all(prev_step in user_actions for prev_step in funnel_steps[:i]):
                        step_dropoff[step] += 1

        churn_analysis = []
        for step in funnel_steps:
            total_eligible = step_completion[step] + step_dropoff[step]
            churn_rate = (step_dropoff[step] / max(total_eligible, 1)) * 100

            churn_analysis.append({
                "step": step,
                "completed": step_completion[step],
                "dropped_off": step_dropoff[step],
                "churn_rate": round(churn_rate, 2)
            })

        return {
            "churn_by_step": churn_analysis,
            "highest_churn_step": max(churn_analysis, key=lambda x: x["churn_rate"])["step"] if churn_analysis else None
        }

    def _generate_funnel_recommendations(self, funnel_data: List[Dict[str, Any]], churn_analysis: Dict[str, Any]) -> List[str]:
        """生成漏斗优化建议"""
        recommendations = []

        # 分析转化率
        conversion_rates = self._calculate_conversion_rates(funnel_data)

        # 找出转化率最低的步骤
        lowest_conversion = min(conversion_rates[1:], key=lambda x: x["conversion_rate"]) if len(conversion_rates) > 1 else None

        if lowest_conversion and lowest_conversion["conversion_rate"] < 50:
            recommendations.append(f"步骤 '{lowest_conversion['step']}' 转化率较低 ({lowest_conversion['conversion_rate']}%)，建议优化用户体验")

        # 分析流失点
        if churn_analysis["highest_churn_step"]:
            recommendations.append(f"步骤 '{churn_analysis['highest_churn_step']}' 流失率最高，需要重点关注")

        # 总体建议
        total_conversion = (funnel_data[-1]["users_count"] / max(funnel_data[0]["users_count"], 1)) * 100
        if total_conversion < 20:
            recommendations.append("整体转化率较低，建议全面优化用户流程")

        return recommendations
