"""
部门数据安全测试 - 验证跨商户数据泄露修复效果
"""

import pytest
from sqlalchemy.orm import Session
from fastapi.testclient import TestClient
from unittest.mock import Mock

from app.models.user import User
from app.models.merchant import Merchant
from app.models.department import Department
from app.services.department_service_new import DepartmentService
from app.services.security_service import SecurityService
from app.core.auth import AuthService
from app.services.permission_service import PermissionService


class TestDepartmentSecurity:
    """部门数据安全测试类"""

    @pytest.fixture
    def setup_test_data(self, db: Session):
        """设置测试数据"""
        # 创建两个商户
        merchant1 = Merchant(
            id=1,
            name="商户1",
            code="MERCHANT_1",
            api_key="test_key_1",
            api_secret="test_secret_1"
        )
        merchant2 = Merchant(
            id=2,
            name="商户2", 
            code="MERCHANT_2",
            api_key="test_key_2",
            api_secret="test_secret_2"
        )
        db.add_all([merchant1, merchant2])

        # 创建部门
        dept1 = Department(
            id=1,
            name="部门1",
            code="DEPT_1",
            merchant_id=1,
            level=1,
            path="/1/",
            status=True
        )
        dept2 = Department(
            id=2,
            name="部门2",
            code="DEPT_2",
            merchant_id=2,
            level=1,
            path="/2/",
            status=True
        )
        db.add_all([dept1, dept2])

        # 创建用户
        user1 = User(
            id=1,
            username="user1",
            email="<EMAIL>",
            hashed_password="hashed_password",
            merchant_id=1,
            department_id=1,
            is_superuser=False
        )
        user2 = User(
            id=2,
            username="user2",
            email="<EMAIL>",
            hashed_password="hashed_password",
            merchant_id=2,
            department_id=2,
            is_superuser=False
        )
        superuser = User(
            id=3,
            username="admin",
            email="<EMAIL>",
            hashed_password="hashed_password",
            merchant_id=None,
            department_id=None,
            is_superuser=True
        )
        db.add_all([user1, user2, superuser])

        db.commit()
        return {
            "merchant1": merchant1,
            "merchant2": merchant2,
            "user1": user1,
            "user2": user2,
            "superuser": superuser,
            "dept1": dept1,
            "dept2": dept2
        }

    def test_department_merchant_isolation(self, db: Session, setup_test_data):
        """测试部门的商户隔离"""
        dept_service = DepartmentService(db)
        user1 = setup_test_data["user1"]  # 属于商户1
        user2 = setup_test_data["user2"]  # 属于商户2
        dept1 = setup_test_data["dept1"]  # 属于商户1
        dept2 = setup_test_data["dept2"]  # 属于商户2

        # 用户1应该能访问自己商户的部门
        result1 = dept_service.get_with_security_check(dept1.id, user1)
        assert result1 is not None, "用户应该能访问自己商户的部门"

        # 用户1不应该能访问其他商户的部门
        result2 = dept_service.get_with_security_check(dept2.id, user1)
        assert result2 is None, "用户不应该能访问其他商户的部门"

    def test_department_data_isolation_in_queries(self, db: Session, setup_test_data):
        """测试查询中的数据隔离"""
        dept_service = DepartmentService(db)
        user1 = setup_test_data["user1"]
        user2 = setup_test_data["user2"]

        # 用户1的查询应该只返回商户1的部门
        query1 = db.query(Department)
        isolated_query1 = dept_service.apply_data_isolation(query1, user1)
        results1 = isolated_query1.all()
        
        assert len(results1) == 1, f"用户1应该只能看到1个部门，实际看到{len(results1)}个"
        assert results1[0].merchant_id == 1, "用户1应该只能看到自己商户的部门"

        # 用户2的查询应该只返回商户2的部门
        query2 = db.query(Department)
        isolated_query2 = dept_service.apply_data_isolation(query2, user2)
        results2 = isolated_query2.all()
        
        assert len(results2) == 1, f"用户2应该只能看到1个部门，实际看到{len(results2)}个"
        assert results2[0].merchant_id == 2, "用户2应该只能看到自己商户的部门"

    def test_department_tree_security(self, db: Session, setup_test_data):
        """测试部门树形结构的安全性"""
        dept_service = DepartmentService(db)
        user1 = setup_test_data["user1"]

        # 用户1获取部门树应该只包含自己商户的部门
        tree = dept_service.get_department_tree(1, user1)
        assert len(tree) == 1, "部门树应该只包含用户商户的部门"
        assert tree[0]["merchant_id"] == 1, "部门树中的部门应该属于用户的商户"

        # 用户1尝试获取其他商户的部门树应该失败
        with pytest.raises(ValueError) as exc_info:
            dept_service.get_department_tree(2, user1)
        assert "无权限访问该商户的部门" in str(exc_info.value)

    def test_security_service_department_validation(self, db: Session, setup_test_data):
        """测试安全服务的部门验证"""
        security_service = SecurityService(db)
        user1 = setup_test_data["user1"]
        dept1 = setup_test_data["dept1"]
        dept2 = setup_test_data["dept2"]

        # 用户1访问自己商户的部门应该通过
        assert security_service.validate_department_access(user1, dept1.id, "read")

        # 用户1访问其他商户的部门应该失败
        assert not security_service.validate_department_access(user1, dept2.id, "read")

    def test_superuser_access(self, db: Session, setup_test_data):
        """测试超级管理员访问"""
        dept_service = DepartmentService(db)
        security_service = SecurityService(db)
        superuser = setup_test_data["superuser"]
        dept1 = setup_test_data["dept1"]
        dept2 = setup_test_data["dept2"]

        # 超级管理员应该能访问所有部门
        assert dept_service.get_with_security_check(dept1.id, superuser) is not None
        assert dept_service.get_with_security_check(dept2.id, superuser) is not None

        # 超级管理员应该通过所有安全验证
        assert security_service.validate_department_access(superuser, dept1.id, "read")
        assert security_service.validate_department_access(superuser, dept2.id, "read")

    def test_department_permission_filtering(self, db: Session, setup_test_data):
        """测试部门权限过滤"""
        dept_service = DepartmentService(db)
        user1 = setup_test_data["user1"]

        # 获取商户部门列表
        departments = dept_service.get_merchant_departments(1, user1)
        
        # 应该只返回用户商户的部门
        assert len(departments) == 1, "应该只返回用户商户的部门"
        assert departments[0].merchant_id == 1, "返回的部门应该属于用户的商户"

    def test_department_search_security(self, db: Session, setup_test_data):
        """测试部门搜索的安全性"""
        dept_service = DepartmentService(db)
        user1 = setup_test_data["user1"]

        # 搜索部门应该只返回用户商户的部门
        results = dept_service.search_departments(1, "部门", user1)
        assert len(results) == 1, "搜索结果应该只包含用户商户的部门"
        assert results[0].merchant_id == 1, "搜索结果应该属于用户的商户"

        # 用户1搜索其他商户的部门应该返回空结果
        results = dept_service.search_departments(2, "部门", user1)
        assert len(results) == 0, "用户不应该能搜索其他商户的部门"

    def test_department_statistics_security(self, db: Session, setup_test_data):
        """测试部门统计的安全性"""
        dept_service = DepartmentService(db)
        user1 = setup_test_data["user1"]
        dept1 = setup_test_data["dept1"]
        dept2 = setup_test_data["dept2"]

        # 用户1应该能获取自己商户部门的统计
        stats1 = dept_service.get_department_statistics(dept1.id, user1)
        assert stats1 is not None, "用户应该能获取自己商户部门的统计"

        # 用户1不应该能获取其他商户部门的统计
        with pytest.raises(ValueError) as exc_info:
            dept_service.get_department_statistics(dept2.id, user1)
        assert "不存在或无权限访问" in str(exc_info.value)

    def test_department_users_security(self, db: Session, setup_test_data):
        """测试部门用户查询的安全性"""
        dept_service = DepartmentService(db)
        user1 = setup_test_data["user1"]
        dept1 = setup_test_data["dept1"]
        dept2 = setup_test_data["dept2"]

        # 用户1应该能获取自己商户部门的用户
        users1 = dept_service.get_department_users(dept1.id, user1)
        assert isinstance(users1, list), "应该返回用户列表"

        # 用户1不应该能获取其他商户部门的用户
        users2 = dept_service.get_department_users(dept2.id, user1)
        assert len(users2) == 0, "用户不应该能获取其他商户部门的用户"


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
