#!/usr/bin/env python3
"""
CK计数一致性监控脚本
定期检查沃尔玛绑卡系统中CK bind_count与实际绑卡记录的一致性
"""

import asyncio
import sys
import os
import time
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from sqlalchemy.orm import Session
from app.db.session import SessionLocal
from app.models.walmart_ck import WalmartCK
from app.models.card_record import CardRecord
from app.core.logging import get_logger

logger = get_logger(__name__)


class CKConsistencyMonitor:
    """CK计数一致性监控器"""
    
    def __init__(self):
        self.db: Session = SessionLocal()
        self.alert_threshold = 5  # 不一致CK数量超过此值时发出警报
    
    def __del__(self):
        if hasattr(self, 'db'):
            self.db.close()
    
    async def run_monitor(self, interval_hours: int = 6, max_runs: Optional[int] = None):
        """
        运行持续监控
        
        Args:
            interval_hours: 检查间隔（小时）
            max_runs: 最大运行次数，None表示无限运行
        """
        logger.info(f"🔍 开始CK计数一致性监控，检查间隔: {interval_hours}小时")
        
        run_count = 0
        while True:
            try:
                run_count += 1
                logger.info(f"📊 第 {run_count} 次检查开始")
                
                # 执行检查
                result = await self.check_consistency()
                
                # 分析结果并发出警报
                await self._analyze_and_alert(result)
                
                # 检查是否达到最大运行次数
                if max_runs and run_count >= max_runs:
                    logger.info(f"达到最大运行次数 {max_runs}，监控结束")
                    break
                
                # 等待下次检查
                sleep_seconds = interval_hours * 3600
                logger.info(f"⏰ 等待 {interval_hours} 小时后进行下次检查...")
                await asyncio.sleep(sleep_seconds)
                
            except KeyboardInterrupt:
                logger.info("收到中断信号，停止监控")
                break
            except Exception as e:
                logger.error(f"监控过程中发生错误: {e}")
                # 等待一段时间后重试
                await asyncio.sleep(300)  # 5分钟后重试
    
    async def check_consistency(self) -> Dict[str, Any]:
        """检查CK计数一致性"""
        try:
            # 获取所有活跃的CK
            cks = self.db.query(WalmartCK).filter(
                WalmartCK.is_deleted == False
            ).all()
            
            inconsistent_cks = []
            total_checked = len(cks)
            
            for ck in cks:
                # 计算实际绑卡成功次数
                actual_count = self.db.query(CardRecord).filter(
                    CardRecord.walmart_ck_id == ck.id,
                    CardRecord.status == 'success'
                ).count()
                
                current_count = ck.bind_count
                
                if actual_count != current_count:
                    inconsistent_cks.append({
                        'ck_id': ck.id,
                        'merchant_id': ck.merchant_id,
                        'department_id': ck.department_id,
                        'current_count': current_count,
                        'actual_count': actual_count,
                        'difference': actual_count - current_count
                    })
            
            result = {
                'timestamp': datetime.now().isoformat(),
                'total_cks_checked': total_checked,
                'inconsistent_count': len(inconsistent_cks),
                'inconsistent_cks': inconsistent_cks,
                'is_healthy': len(inconsistent_cks) <= self.alert_threshold
            }
            
            logger.info(
                f"✅ 一致性检查完成: 检查了 {total_checked} 个CK, "
                f"发现 {len(inconsistent_cks)} 个不一致"
            )
            
            return result
            
        except Exception as e:
            logger.error(f"一致性检查失败: {e}")
            raise
    
    async def _analyze_and_alert(self, result: Dict[str, Any]):
        """分析检查结果并发出警报"""
        inconsistent_count = result['inconsistent_count']
        
        if inconsistent_count == 0:
            logger.info("🎉 所有CK计数都是一致的！")
            return
        
        # 记录不一致的详细信息
        logger.warning(f"⚠️  发现 {inconsistent_count} 个CK计数不一致:")
        
        for ck_info in result['inconsistent_cks']:
            logger.warning(
                f"  CK {ck_info['ck_id']}: {ck_info['current_count']} -> {ck_info['actual_count']} "
                f"(差异: {ck_info['difference']})"
            )
        
        # 如果超过警报阈值，发出警报
        if inconsistent_count > self.alert_threshold:
            await self._send_alert(result)
        
        # 提供修复建议
        logger.info("💡 修复建议:")
        logger.info("  运行修复脚本: python app/scripts/fix_ck_count_consistency.py --auto")
    
    async def _send_alert(self, result: Dict[str, Any]):
        """发送警报"""
        inconsistent_count = result['inconsistent_count']
        
        logger.error(f"🚨 CK计数一致性警报！")
        logger.error(f"发现 {inconsistent_count} 个CK计数不一致，超过警报阈值 {self.alert_threshold}")
        
        # 这里可以添加更多的警报机制，比如：
        # - 发送邮件
        # - 发送Telegram消息
        # - 写入警报日志文件
        # - 调用监控系统API
        
        # 示例：写入警报日志
        alert_message = {
            'timestamp': result['timestamp'],
            'alert_type': 'CK_COUNT_INCONSISTENCY',
            'severity': 'HIGH' if inconsistent_count > self.alert_threshold * 2 else 'MEDIUM',
            'inconsistent_count': inconsistent_count,
            'threshold': self.alert_threshold,
            'details': result['inconsistent_cks']
        }
        
        logger.error(f"警报详情: {alert_message}")
    
    async def generate_report(self) -> Dict[str, Any]:
        """生成详细的一致性报告"""
        try:
            result = await self.check_consistency()
            
            # 按商户分组统计
            merchant_stats = {}
            for ck_info in result['inconsistent_cks']:
                merchant_id = ck_info['merchant_id']
                if merchant_id not in merchant_stats:
                    merchant_stats[merchant_id] = {
                        'inconsistent_count': 0,
                        'total_difference': 0,
                        'cks': []
                    }
                
                merchant_stats[merchant_id]['inconsistent_count'] += 1
                merchant_stats[merchant_id]['total_difference'] += abs(ck_info['difference'])
                merchant_stats[merchant_id]['cks'].append(ck_info)
            
            report = {
                'summary': result,
                'merchant_breakdown': merchant_stats,
                'recommendations': self._generate_recommendations(result)
            }
            
            return report
            
        except Exception as e:
            logger.error(f"生成报告失败: {e}")
            raise
    
    def _generate_recommendations(self, result: Dict[str, Any]) -> List[str]:
        """生成修复建议"""
        recommendations = []
        
        inconsistent_count = result['inconsistent_count']
        
        if inconsistent_count == 0:
            recommendations.append("✅ 所有CK计数都是一致的，无需修复")
        else:
            recommendations.append(f"🔧 发现 {inconsistent_count} 个CK计数不一致，建议立即修复")
            recommendations.append("运行命令: python app/scripts/fix_ck_count_consistency.py --auto")
            
            if inconsistent_count > self.alert_threshold:
                recommendations.append("⚠️  不一致数量较多，建议检查绑卡流程是否存在问题")
                recommendations.append("考虑启用原子性绑卡服务以防止未来的不一致")
        
        return recommendations


async def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description="CK计数一致性监控工具")
    parser.add_argument("--interval", type=int, default=6, help="检查间隔（小时）")
    parser.add_argument("--max-runs", type=int, help="最大运行次数")
    parser.add_argument("--once", action="store_true", help="只运行一次检查")
    parser.add_argument("--report", action="store_true", help="生成详细报告")
    
    args = parser.parse_args()
    
    monitor = CKConsistencyMonitor()
    
    try:
        if args.report:
            # 生成详细报告
            report = await monitor.generate_report()
            print("=" * 80)
            print("📊 CK计数一致性详细报告")
            print("=" * 80)
            print(f"检查时间: {report['summary']['timestamp']}")
            print(f"检查的CK总数: {report['summary']['total_cks_checked']}")
            print(f"不一致的CK数: {report['summary']['inconsistent_count']}")
            print(f"系统健康状态: {'✅ 健康' if report['summary']['is_healthy'] else '⚠️  需要关注'}")
            
            if report['merchant_breakdown']:
                print("\n按商户统计:")
                for merchant_id, stats in report['merchant_breakdown'].items():
                    print(f"  商户 {merchant_id}: {stats['inconsistent_count']} 个不一致CK")
            
            print("\n修复建议:")
            for rec in report['recommendations']:
                print(f"  {rec}")
            
        elif args.once:
            # 只运行一次检查
            result = await monitor.check_consistency()
            await monitor._analyze_and_alert(result)
        else:
            # 持续监控
            await monitor.run_monitor(
                interval_hours=args.interval,
                max_runs=args.max_runs
            )
            
    except Exception as e:
        logger.error(f"监控失败: {e}")
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())
