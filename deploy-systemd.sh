#!/bin/bash

# Walmart 绑卡服务器 Systemd 部署脚本
# 作者: Walmart 绑卡系统开发团队
# 版本: 1.0

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 配置变量
SERVICE_NAME="walmart-bind-card"
SERVICE_USER="wem"
SERVICE_GROUP="wem"
WORK_DIR="/home/<USER>"
EXECUTABLE_NAME="walmart-bind-card-server-linux"
SERVICE_FILE="walmart-bind-card.service"
CONFIG_FILE="config.yaml"

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查是否以 root 权限运行
check_root() {
    if [[ $EUID -ne 0 ]]; then
        log_error "此脚本需要 root 权限运行"
        echo "请使用: sudo $0"
        exit 1
    fi
}

# 检查必要文件是否存在
check_files() {
    log_info "检查必要文件..."
    
    local missing_files=()
    
    if [[ ! -f "$EXECUTABLE_NAME" ]]; then
        missing_files+=("$EXECUTABLE_NAME")
    fi
    
    if [[ ! -f "$SERVICE_FILE" ]]; then
        missing_files+=("$SERVICE_FILE")
    fi
    
    if [[ ! -f "$CONFIG_FILE" ]]; then
        missing_files+=("$CONFIG_FILE")
    fi
    
    if [[ ${#missing_files[@]} -gt 0 ]]; then
        log_error "以下文件缺失:"
        for file in "${missing_files[@]}"; do
            echo "  - $file"
        done
        exit 1
    fi
    
    log_success "所有必要文件检查通过"
}

# 创建用户和组
create_user() {
    log_info "创建服务用户 $SERVICE_USER..."
    
    if id "$SERVICE_USER" &>/dev/null; then
        log_warning "用户 $SERVICE_USER 已存在"
    else
        useradd -r -s /bin/bash -d "$WORK_DIR" "$SERVICE_USER"
        log_success "用户 $SERVICE_USER 创建成功"
    fi
}

# 创建目录结构
create_directories() {
    log_info "创建目录结构..."
    
    mkdir -p "$WORK_DIR/logs"
    chown -R "$SERVICE_USER:$SERVICE_GROUP" "$WORK_DIR"
    chmod 755 "$WORK_DIR"
    
    log_success "目录结构创建完成"
}

# 复制文件
copy_files() {
    log_info "复制服务文件..."
    
    # 复制可执行文件
    cp "$EXECUTABLE_NAME" "$WORK_DIR/"
    chmod +x "$WORK_DIR/$EXECUTABLE_NAME"
    chown "$SERVICE_USER:$SERVICE_GROUP" "$WORK_DIR/$EXECUTABLE_NAME"
    
    # 复制配置文件
    cp "$CONFIG_FILE" "$WORK_DIR/"
    chown "$SERVICE_USER:$SERVICE_GROUP" "$WORK_DIR/$CONFIG_FILE"
    
    log_success "文件复制完成"
}

# 安装 systemd 服务
install_service() {
    log_info "安装 systemd 服务..."
    
    # 停止现有服务（如果存在）
    if systemctl is-active --quiet "$SERVICE_NAME"; then
        log_warning "停止现有服务..."
        systemctl stop "$SERVICE_NAME"
    fi
    
    # 复制服务文件
    cp "$SERVICE_FILE" "/etc/systemd/system/"
    
    # 重新加载 systemd
    systemctl daemon-reload
    
    # 启用服务
    systemctl enable "$SERVICE_NAME"
    
    log_success "systemd 服务安装完成"
}

# 检查依赖服务
check_dependencies() {
    log_info "检查依赖服务..."
    
    local dependencies=("mysql" "redis" "rabbitmq-server")
    local missing_deps=()
    
    for dep in "${dependencies[@]}"; do
        if ! systemctl is-enabled --quiet "$dep" 2>/dev/null; then
            missing_deps+=("$dep")
        fi
    done
    
    if [[ ${#missing_deps[@]} -gt 0 ]]; then
        log_warning "以下依赖服务未启用或未安装:"
        for dep in "${missing_deps[@]}"; do
            echo "  - $dep"
        done
        log_warning "请确保这些服务已正确安装和配置"
    else
        log_success "所有依赖服务检查通过"
    fi
}

# 启动服务
start_service() {
    log_info "启动服务..."
    
    systemctl start "$SERVICE_NAME"
    
    # 等待服务启动
    sleep 3
    
    if systemctl is-active --quiet "$SERVICE_NAME"; then
        log_success "服务启动成功"
    else
        log_error "服务启动失败"
        log_info "查看服务状态:"
        systemctl status "$SERVICE_NAME" --no-pager
        exit 1
    fi
}

# 验证部署
verify_deployment() {
    log_info "验证部署..."
    
    # 检查服务状态
    if systemctl is-active --quiet "$SERVICE_NAME"; then
        log_success "✅ 服务运行正常"
    else
        log_error "❌ 服务未运行"
        return 1
    fi
    
    # 检查端口监听
    if netstat -tlnp 2>/dev/null | grep -q ":20000 " || ss -tlnp 2>/dev/null | grep -q ":20000 "; then
        log_success "✅ 端口 20000 监听正常"
    else
        log_warning "⚠️  端口 20000 未监听，服务可能仍在启动中"
    fi
    
    # 检查日志
    log_info "最近的服务日志:"
    journalctl -u "$SERVICE_NAME" -n 5 --no-pager
}

# 显示管理命令
show_management_commands() {
    log_info "服务管理命令:"
    echo ""
    echo "启动服务:     sudo systemctl start $SERVICE_NAME"
    echo "停止服务:     sudo systemctl stop $SERVICE_NAME"
    echo "重启服务:     sudo systemctl restart $SERVICE_NAME"
    echo "查看状态:     sudo systemctl status $SERVICE_NAME"
    echo "查看日志:     sudo journalctl -u $SERVICE_NAME -f"
    echo "启用开机启动: sudo systemctl enable $SERVICE_NAME"
    echo "禁用开机启动: sudo systemctl disable $SERVICE_NAME"
    echo ""
}

# 主函数
main() {
    echo "========================================"
    echo "  Walmart 绑卡服务器 Systemd 部署脚本"
    echo "========================================"
    echo ""
    
    check_root
    check_files
    create_user
    create_directories
    copy_files
    install_service
    check_dependencies
    start_service
    verify_deployment
    
    echo ""
    log_success "🎉 部署完成!"
    echo ""
    show_management_commands
}

# 执行主函数
main "$@"
