import { defineStore } from 'pinia'
import {walmartServerApi} from '@/api/modules/walmartServer'

export const useWalmartServerStore = defineStore('WalmartServer', {
    state: () => ({
        apiUrl: [],
        currentConfig: null,
        loading: false,
        error: null
    }),

    getters: {
        getConfigList: (state) => state.apiUrl,
        getCurrentConfig: (state) => state.currentConfig,
        isLoading: (state) => state.loading
    },

    actions: {
        // 获取配置列表
        async getWalmartServer(params) {
            this.loading = true
            try {
                const data = await walmartServerApi.getList(params)
                this.apiUrl = data.apiUrl;
                return data;
            } catch (error) {
                this.error = error.message
                return null
            } finally {
                this.loading = false
            }
        },
        // 更新配置
        async updateConfig(configData) {
            this.loading = true
            try {
                const data = await walmartServerApi.update(configData)
                return data
            } catch (error) {
                this.error = error.message
                return null
            } finally {
                this.loading = false
            }
        },
        // 设置当前配置
        setCurrentConfig(config) {
            this.currentConfig = config
        },

        // 重置状态
        resetState() {
            this.configList = []
            this.currentConfig = null
            this.loading = false
            this.error = null
        }
    }
})