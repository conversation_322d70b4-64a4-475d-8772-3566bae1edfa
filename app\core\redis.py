import redis.asyncio as redis
from app.core.config import settings

_redis = None


async def get_redis():
    """获取Redis连接"""
    global _redis
    if _redis is None:
        # 构建Redis连接
        redis_url = f"redis://"

        # 如果有密码，添加密码
        if settings.REDIS_PASSWORD:
            redis_url += f":{settings.REDIS_PASSWORD}@"

        # 添加主机和端口
        redis_url += f"{settings.REDIS_HOST}:{settings.REDIS_PORT}/{settings.REDIS_DB}"

        _redis = redis.from_url(redis_url, encoding="utf-8", decode_responses=True)
    return _redis


async def close_redis_connection():
    """关闭Redis连接"""
    global _redis
    if _redis is not None:
        await _redis.close()
        _redis = None
