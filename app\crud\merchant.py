from typing import List, Optional, Dict, Any, Union
from sqlalchemy.orm import Session
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import func, and_, or_, select
from fastapi.encoders import jsonable_encoder
import datetime

from app.models.merchant import Merchant
from app.schemas.merchant import (
    MerchantCreate,
    MerchantUpdate,
    MerchantList,
    MerchantInDB,
)
from app.core.security import generate_api_key, generate_api_secret
from app.crud.base import CRUDBase
from app.core.logging import get_logger

logger = get_logger(__name__)


class CRUDMerchant(CRUDBase[Merchant, MerchantCreate, MerchantUpdate]):
    """商家CRUD操作类"""

    def get_merchant(self, db: Session, merchant_id: int) -> Optional[MerchantInDB]:
        """获取商家信息"""
        return self.get(db, id=merchant_id)

    def get_by_code(self, db: Session, *, code: str) -> Optional[Merchant]:
        """通过商家代码获取商家"""
        return db.query(self.model).filter(self.model.code == code).first()

    def get_by_name(self, db: Session, *, name: str) -> Optional[Merchant]:
        """通过商家名称获取商家"""
        return db.query(self.model).filter(self.model.name == name).first()

    def create(
        self, db: Session, *, obj_in: MerchantCreate, created_by: Optional[int] = None
    ) -> Merchant:
        """创建商家，自动生成API密钥和密文"""
        logger.debug(f"Creating merchant with data: {obj_in}, created by: {created_by}")
        api_key = generate_api_key()
        api_secret = generate_api_secret()
        # Ensure correct fields are passed to the model constructor
        # Use obj_in.model_dump() for Pydantic v2
        obj_in_data = obj_in.model_dump(exclude_unset=True)
        db_obj = self.model(
            **obj_in_data,
            api_key=api_key,
            api_secret=api_secret,
            created_by=created_by,
            api_key_updated_at=datetime.datetime.now(datetime.timezone.utc),
        )
        try:
            db.add(db_obj)
            db.commit()
            db.refresh(db_obj)
            logger.info(f"Merchant {db_obj.id} ({db_obj.name}) created successfully.")
            return db_obj
        except Exception as e:
            db.rollback()
            logger.error(f"Error creating merchant: {e}", exc_info=True)
            raise

    def update_status(
        self, db: Session, *, merchant_id: int, status: bool
    ) -> Optional[Merchant]:
        """更新商家状态"""
        db_obj = self.get(db=db, id=merchant_id)
        if not db_obj:
            return None
        logger.info(f"Updating status for merchant {merchant_id} to {status}")
        update_data = {"status": status}
        return super().update(db=db, db_obj=db_obj, obj_in=update_data)

    def reset_api_key(
        self, db: Session, *, merchant_id: int
    ) -> Optional[Dict[str, str]]:
        """重置API Key和Secret"""
        db_obj = self.get(db=db, id=merchant_id)
        if not db_obj:
            return None

        logger.info(f"Resetting API key for merchant {merchant_id}")
        new_api_key = generate_api_key()
        new_api_secret = generate_api_secret()
        update_data = {
            "api_key": new_api_key,
            "api_secret": new_api_secret,
            "api_key_updated_at": datetime.datetime.now(datetime.timezone.utc),
        }
        super().update(db=db, db_obj=db_obj, obj_in=update_data)
        return {"api_key": new_api_key, "api_secret": new_api_secret}

    def get_multi_by_filter(
        self,
        db: Session,
        *,
        skip: int = 0,
        limit: int = 100,
        search: Optional[str] = None,
        status: Optional[bool] = None,
    ) -> Dict[str, Any]:
        """根据过滤条件获取商家列表和总数"""
        query = db.query(self.model)

        if search:
            search_term = f"%{search}%"
            query = query.filter(
                or_(
                    self.model.name.ilike(search_term),
                    self.model.code.ilike(search_term),
                )
            )

        if status is not None:
            query = query.filter(self.model.status == status)

        total = query.count()
        items = query.offset(skip).limit(limit).all()

        logger.debug(f"Fetched {len(items)} merchants out of {total} total.")
        return {"total": total, "items": items}

    def get_current_time(self) -> datetime.datetime:
        """获取当前UTC时间"""
        return datetime.datetime.now(datetime.timezone.utc)


# 异步版本的merchant操作函数
async def get_merchant_async(db: AsyncSession, merchant_id: int) -> Optional[Merchant]:
    """异步获取商家信息"""
    stmt = select(Merchant).filter(Merchant.id == merchant_id)
    result = await db.execute(stmt)
    return result.scalar_one_or_none()


merchant = CRUDMerchant(Merchant)
