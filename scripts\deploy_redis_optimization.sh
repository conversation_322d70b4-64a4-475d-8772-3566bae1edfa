#!/bin/bash

# Redis CK优化方案部署脚本
# 用于自动化部署Redis优化方案

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 配置变量
REDIS_VERSION="7.0"
REDIS_PORT="6379"
REDIS_PASSWORD=""
REDIS_MAX_MEMORY="2gb"
PROJECT_ROOT=$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)
BACKUP_DIR="${PROJECT_ROOT}/backups/$(date +%Y%m%d_%H%M%S)"
REDIS_CONFIG_FILE="/etc/redis/redis.conf"
SYSTEMD_SERVICE_FILE="/etc/systemd/system/walmart-redis.service"

# 检查运行权限
check_permissions() {
    log_info "检查运行权限..."
    if [[ $EUID -eq 0 ]]; then
        log_warning "检测到以root用户运行，建议使用普通用户"
    fi
    
    # 检查Docker权限
    if ! docker ps >/dev/null 2>&1; then
        log_error "无法访问Docker，请确保Docker已安装且当前用户有权限"
        exit 1
    fi
    
    log_success "权限检查通过"
}

# 检查依赖
check_dependencies() {
    log_info "检查系统依赖..."
    
    local dependencies=("docker" "docker-compose" "python3" "pip3")
    local missing_deps=()
    
    for dep in "${dependencies[@]}"; do
        if ! command -v "$dep" &> /dev/null; then
            missing_deps+=("$dep")
        fi
    done
    
    if [ ${#missing_deps[@]} -ne 0 ]; then
        log_error "缺少以下依赖: ${missing_deps[*]}"
        log_info "请先安装缺少的依赖，然后重新运行脚本"
        exit 1
    fi
    
    log_success "依赖检查通过"
}

# 创建备份
create_backup() {
    log_info "创建系统备份..."
    
    mkdir -p "$BACKUP_DIR"
    
    # 备份数据库
    if command -v mysqldump &> /dev/null; then
        log_info "备份MySQL数据库..."
        mysqldump -u root -p walmart_card_db > "$BACKUP_DIR/database_backup.sql" 2>/dev/null || {
            log_warning "数据库备份失败，请手动备份"
        }
    fi
    
    # 备份配置文件
    if [ -f "${PROJECT_ROOT}/app/core/config.py" ]; then
        cp "${PROJECT_ROOT}/app/core/config.py" "$BACKUP_DIR/"
        log_success "配置文件已备份"
    fi
    
    # 备份现有服务文件
    if [ -f "${PROJECT_ROOT}/app/services/walmart_ck_service_new.py" ]; then
        cp "${PROJECT_ROOT}/app/services/walmart_ck_service_new.py" "$BACKUP_DIR/"
        log_success "服务文件已备份"
    fi
    
    log_success "备份完成，备份目录: $BACKUP_DIR"
}

# 安装Redis
install_redis() {
    log_info "安装Redis..."
    
    # 检查Redis是否已经运行
    if docker ps | grep -q walmart-redis; then
        log_warning "检测到Redis容器已在运行"
        read -p "是否要重新创建Redis容器? (y/N): " -n 1 -r
        echo
        if [[ $REPLY =~ ^[Yy]$ ]]; then
            docker stop walmart-redis || true
            docker rm walmart-redis || true
        else
            log_info "跳过Redis安装"
            return 0
        fi
    fi
    
    # 创建Redis数据目录
    sudo mkdir -p /var/lib/walmart-redis
    sudo chown -R 999:999 /var/lib/walmart-redis
    
    # 创建Redis配置文件
    cat > /tmp/redis.conf << EOF
# Redis配置文件 - 沃尔玛绑卡系统优化
port ${REDIS_PORT}
bind 0.0.0.0
protected-mode yes
timeout 300
keepalive 300

# 内存配置
maxmemory ${REDIS_MAX_MEMORY}
maxmemory-policy allkeys-lru

# 持久化配置
save 900 1
save 300 10
save 60 10000
stop-writes-on-bgsave-error yes
rdbcompression yes
rdbchecksum yes
dbfilename dump.rdb
dir /data

# AOF配置
appendonly yes
appendfilename "appendonly.aof"
appendfsync everysec
no-appendfsync-on-rewrite no
auto-aof-rewrite-percentage 100
auto-aof-rewrite-min-size 64mb

# 日志配置
loglevel notice
logfile /data/redis.log

# 客户端配置
maxclients 10000

# 慢查询日志
slowlog-log-slower-than 10000
slowlog-max-len 128

# 延迟监控
latency-monitor-threshold 100
EOF

    # 启动Redis容器
    log_info "启动Redis容器..."
    docker run -d \
        --name walmart-redis \
        --restart unless-stopped \
        -p ${REDIS_PORT}:6379 \
        -v /var/lib/walmart-redis:/data \
        -v /tmp/redis.conf:/usr/local/etc/redis/redis.conf \
        redis:${REDIS_VERSION}-alpine \
        redis-server /usr/local/etc/redis/redis.conf
    
    # 等待Redis启动
    log_info "等待Redis启动..."
    sleep 5
    
    # 测试Redis连接
    if docker exec walmart-redis redis-cli ping | grep -q PONG; then
        log_success "Redis安装并启动成功"
    else
        log_error "Redis启动失败"
        exit 1
    fi
}

# 安装Python依赖
install_python_dependencies() {
    log_info "安装Python依赖..."
    
    cd "$PROJECT_ROOT"
    
    # 检查虚拟环境
    if [ ! -d "venv" ]; then
        log_info "创建Python虚拟环境..."
        python3 -m venv venv
    fi
    
    # 激活虚拟环境
    source venv/bin/activate
    
    # 升级pip
    pip install --upgrade pip
    
    # 安装Redis相关依赖
    log_info "安装Redis依赖包..."
    pip install redis[hiredis] aioredis
    
    # 安装其他可能需要的依赖
    if [ -f "requirements.txt" ]; then
        pip install -r requirements.txt
    fi
    
    log_success "Python依赖安装完成"
}

# 部署代码更新
deploy_code_updates() {
    log_info "部署代码更新..."
    
    cd "$PROJECT_ROOT"
    
    # 检查必要的文件是否存在
    local required_files=(
        "app/services/redis_ck_service.py"
        "app/core/redis_config.py"
        "docs/redis_integration_guide.md"
    )
    
    for file in "${required_files[@]}"; do
        if [ ! -f "$file" ]; then
            log_error "缺少必要文件: $file"
            log_info "请确保所有Redis优化相关文件都已正确放置"
            exit 1
        fi
    done
    
    # 更新配置文件
    log_info "更新配置文件..."
    if ! grep -q "REDIS_URL" app/core/config.py; then
        cat >> app/core/config.py << EOF

# Redis配置 - 自动添加
REDIS_URL: str = "redis://localhost:${REDIS_PORT}/0"
REDIS_MAX_CONNECTIONS: int = 50
ENABLE_REDIS_CK_OPTIMIZATION: bool = True
CK_REDIS_FALLBACK_TO_DB: bool = True
EOF
        log_success "配置文件已更新"
    else
        log_info "配置文件已包含Redis配置，跳过更新"
    fi
    
    log_success "代码更新完成"
}

# 数据同步
sync_data_to_redis() {
    log_info "同步现有数据到Redis..."
    
    cd "$PROJECT_ROOT"
    source venv/bin/activate
    
    # 运行数据同步脚本
    if [ -f "scripts/sync_ck_to_redis.py" ]; then
        log_info "执行CK数据同步..."
        python scripts/sync_ck_to_redis.py
        log_success "数据同步完成"
    else
        log_warning "数据同步脚本不存在，请手动同步数据"
    fi
}

# 运行测试
run_tests() {
    log_info "运行集成测试..."
    
    cd "$PROJECT_ROOT"
    source venv/bin/activate
    
    # 运行Redis优化测试
    if [ -f "test/test_redis_ck_optimization.py" ]; then
        log_info "运行Redis优化测试..."
        python test/test_redis_ck_optimization.py
        
        if [ $? -eq 0 ]; then
            log_success "Redis优化测试通过"
        else
            log_error "Redis优化测试失败"
            return 1
        fi
    fi
    
    # 运行原有测试确保兼容性
    if [ -f "test/test_ck_limit_integration.py" ]; then
        log_info "运行兼容性测试..."
        python test/test_ck_limit_integration.py
        
        if [ $? -eq 0 ]; then
            log_success "兼容性测试通过"
        else
            log_warning "兼容性测试失败，但不影响Redis功能"
        fi
    fi
    
    log_success "测试完成"
}

# 创建监控脚本
create_monitoring() {
    log_info "创建监控脚本..."
    
    # 创建Redis健康检查脚本
    cat > "${PROJECT_ROOT}/scripts/redis_health_check.sh" << 'EOF'
#!/bin/bash

# Redis健康检查脚本

REDIS_HOST="localhost"
REDIS_PORT="6379"

# 检查Redis连接
if docker exec walmart-redis redis-cli -h $REDIS_HOST -p $REDIS_PORT ping | grep -q PONG; then
    echo "✅ Redis连接正常"
else
    echo "❌ Redis连接失败"
    exit 1
fi

# 检查内存使用
MEMORY_USAGE=$(docker exec walmart-redis redis-cli -h $REDIS_HOST -p $REDIS_PORT info memory | grep used_memory_human | cut -d: -f2 | tr -d '\r')
echo "📊 Redis内存使用: $MEMORY_USAGE"

# 检查连接数
CONNECTED_CLIENTS=$(docker exec walmart-redis redis-cli -h $REDIS_HOST -p $REDIS_PORT info clients | grep connected_clients | cut -d: -f2 | tr -d '\r')
echo "🔗 当前连接数: $CONNECTED_CLIENTS"

# 检查CK池状态
CK_POOLS=$(docker exec walmart-redis redis-cli -h $REDIS_HOST -p $REDIS_PORT keys "walmart:ck:pool:*" | wc -l)
echo "🏊 CK池数量: $CK_POOLS"

echo "✅ Redis健康检查完成"
EOF

    chmod +x "${PROJECT_ROOT}/scripts/redis_health_check.sh"
    
    # 创建性能监控脚本
    cat > "${PROJECT_ROOT}/scripts/redis_performance_monitor.py" << 'EOF'
#!/usr/bin/env python3
"""
Redis性能监控脚本
"""

import asyncio
import sys
import os

sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app.core.redis_config import redis_manager
from app.services.redis_ck_service import CKRedisMonitor

async def main():
    try:
        await redis_manager.initialize()
        redis_client = await redis_manager.get_client()
        
        monitor = CKRedisMonitor(redis_client)
        metrics = await monitor.get_performance_metrics()
        
        print("=== Redis性能监控报告 ===")
        print(f"Redis信息: {metrics.get('redis_info', {})}")
        print(f"CK池状态: {metrics.get('ck_pool_status', {})}")
        print(f"缓存命中率: {metrics.get('cache_hit_rates', {})}")
        print(f"锁统计: {metrics.get('lock_statistics', {})}")
        
    except Exception as e:
        print(f"监控失败: {e}")
    finally:
        await redis_manager.close()

if __name__ == "__main__":
    asyncio.run(main())
EOF

    chmod +x "${PROJECT_ROOT}/scripts/redis_performance_monitor.py"
    
    log_success "监控脚本创建完成"
}

# 创建systemd服务
create_systemd_service() {
    log_info "创建systemd服务..."
    
    # 创建Redis服务文件
    sudo tee $SYSTEMD_SERVICE_FILE > /dev/null << EOF
[Unit]
Description=Walmart Redis Service
After=docker.service
Requires=docker.service

[Service]
Type=oneshot
RemainAfterExit=yes
ExecStart=/usr/bin/docker start walmart-redis
ExecStop=/usr/bin/docker stop walmart-redis
TimeoutStartSec=0

[Install]
WantedBy=multi-user.target
EOF

    # 重新加载systemd并启用服务
    sudo systemctl daemon-reload
    sudo systemctl enable walmart-redis.service
    
    log_success "systemd服务创建完成"
}

# 部署完成后的验证
post_deployment_verification() {
    log_info "执行部署后验证..."
    
    # 检查Redis服务状态
    if docker ps | grep -q walmart-redis; then
        log_success "✅ Redis容器运行正常"
    else
        log_error "❌ Redis容器未运行"
        return 1
    fi
    
    # 检查Redis连接
    if docker exec walmart-redis redis-cli ping | grep -q PONG; then
        log_success "✅ Redis连接正常"
    else
        log_error "❌ Redis连接失败"
        return 1
    fi
    
    # 运行健康检查
    if [ -f "${PROJECT_ROOT}/scripts/redis_health_check.sh" ]; then
        "${PROJECT_ROOT}/scripts/redis_health_check.sh"
    fi
    
    log_success "部署验证完成"
}

# 显示部署总结
show_deployment_summary() {
    log_success "🎉 Redis CK优化方案部署完成！"
    echo
    echo "📋 部署总结:"
    echo "  • Redis服务: 已启动并运行在端口 $REDIS_PORT"
    echo "  • 数据同步: 已完成现有CK数据同步"
    echo "  • 监控脚本: 已创建健康检查和性能监控脚本"
    echo "  • 备份目录: $BACKUP_DIR"
    echo
    echo "🔧 后续操作:"
    echo "  • 重启应用服务以启用Redis优化"
    echo "  • 运行性能测试验证效果"
    echo "  • 设置定期监控任务"
    echo
    echo "📚 相关文档:"
    echo "  • 集成指南: docs/redis_integration_guide.md"
    echo "  • 性能分析: docs/redis_performance_analysis.md"
    echo "  • 设计文档: docs/redis_ck_optimization_design.md"
    echo
    echo "🚀 开始享受10倍性能提升吧！"
}

# 主函数
main() {
    echo "🚀 开始部署Redis CK优化方案..."
    echo
    
    # 执行部署步骤
    check_permissions
    check_dependencies
    create_backup
    install_redis
    install_python_dependencies
    deploy_code_updates
    sync_data_to_redis
    run_tests
    create_monitoring
    create_systemd_service
    post_deployment_verification
    show_deployment_summary
    
    log_success "🎉 部署完成！"
}

# 错误处理
trap 'log_error "部署过程中发生错误，请检查日志"; exit 1' ERR

# 运行主函数
main "$@"
