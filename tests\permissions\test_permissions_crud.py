"""
权限管理CRUD操作测试
"""
import pytest
from fastapi.testclient import TestClient
from sqlalchemy.orm import Session

from app.main import app
from app.models.permission import Permission
from app.models.user import User
from test.conftest import get_test_db, create_test_user, create_test_merchant


class TestPermissionsCRUD:
    """权限管理CRUD测试类"""

    def setup_method(self):
        """每个测试方法前的设置"""
        self.client = TestClient(app)
        self.db = next(get_test_db())
        
        # 创建测试商户
        self.test_merchant = create_test_merchant(self.db, "test_merchant_perm")
        
        # 创建超级管理员用户
        self.admin_user = create_test_user(
            self.db, 
            username="admin_perm_test",
            role_codes=["super_admin"],
            merchant_id=None,
            is_superuser=True
        )
        
        # 创建普通用户
        self.normal_user = create_test_user(
            self.db,
            username="normal_perm_test", 
            role_codes=["merchant_admin"],
            merchant_id=self.test_merchant.id
        )

    def teardown_method(self):
        """每个测试方法后的清理"""
        self.db.close()

    def test_create_permission_success(self):
        """测试创建权限成功"""
        # 使用超级管理员登录
        login_data = {"username": "admin_perm_test", "password": "testpass123"}
        login_response = self.client.post("/api/v1/auth/login", data=login_data)
        assert login_response.status_code == 200
        token = login_response.json()["access_token"]
        headers = {"Authorization": f"Bearer {token}"}

        # 创建权限
        permission_data = {
            "code": "test:create",
            "name": "测试创建权限",
            "description": "用于测试的创建权限",
            "resource_type": "api",
            "resource_path": "/api/v1/test",
            "is_enabled": True,
            "sort_order": 1
        }
        
        response = self.client.post(
            "/api/v1/permissions/",
            json=permission_data,
            headers=headers
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data["code"] == "test:create"
        assert data["name"] == "测试创建权限"

    def test_create_permission_duplicate_code(self):
        """测试创建重复权限代码"""
        # 使用超级管理员登录
        login_data = {"username": "admin_perm_test", "password": "testpass123"}
        login_response = self.client.post("/api/v1/auth/login", data=login_data)
        token = login_response.json()["access_token"]
        headers = {"Authorization": f"Bearer {token}"}

        # 创建第一个权限
        permission_data = {
            "code": "test:duplicate",
            "name": "测试重复权限",
            "resource_type": "api"
        }
        
        response1 = self.client.post(
            "/api/v1/permissions/",
            json=permission_data,
            headers=headers
        )
        assert response1.status_code == 200

        # 尝试创建相同代码的权限
        response2 = self.client.post(
            "/api/v1/permissions/",
            json=permission_data,
            headers=headers
        )
        assert response2.status_code == 400
        assert "权限代码已存在" in response2.json()["detail"]

    def test_get_permissions_list(self):
        """测试获取权限列表"""
        # 使用超级管理员登录
        login_data = {"username": "admin_perm_test", "password": "testpass123"}
        login_response = self.client.post("/api/v1/auth/login", data=login_data)
        token = login_response.json()["access_token"]
        headers = {"Authorization": f"Bearer {token}"}

        response = self.client.get("/api/v1/permissions/", headers=headers)
        assert response.status_code == 200
        data = response.json()
        assert "items" in data
        assert "total" in data

    def test_get_permission_detail(self):
        """测试获取权限详情"""
        # 使用超级管理员登录
        login_data = {"username": "admin_perm_test", "password": "testpass123"}
        login_response = self.client.post("/api/v1/auth/login", data=login_data)
        token = login_response.json()["access_token"]
        headers = {"Authorization": f"Bearer {token}"}

        # 先创建一个权限
        permission_data = {
            "code": "test:detail",
            "name": "测试详情权限",
            "resource_type": "api"
        }
        
        create_response = self.client.post(
            "/api/v1/permissions/",
            json=permission_data,
            headers=headers
        )
        permission_id = create_response.json()["id"]

        # 获取权限详情
        response = self.client.get(f"/api/v1/permissions/{permission_id}", headers=headers)
        assert response.status_code == 200
        data = response.json()
        assert data["code"] == "test:detail"

    def test_update_permission(self):
        """测试更新权限"""
        # 使用超级管理员登录
        login_data = {"username": "admin_perm_test", "password": "testpass123"}
        login_response = self.client.post("/api/v1/auth/login", data=login_data)
        token = login_response.json()["access_token"]
        headers = {"Authorization": f"Bearer {token}"}

        # 先创建一个权限
        permission_data = {
            "code": "test:update",
            "name": "测试更新权限",
            "resource_type": "api"
        }
        
        create_response = self.client.post(
            "/api/v1/permissions/",
            json=permission_data,
            headers=headers
        )
        permission_id = create_response.json()["id"]

        # 更新权限
        update_data = {
            "name": "更新后的权限名称",
            "description": "更新后的描述"
        }
        
        response = self.client.put(
            f"/api/v1/permissions/{permission_id}",
            json=update_data,
            headers=headers
        )
        assert response.status_code == 200
        data = response.json()
        assert data["name"] == "更新后的权限名称"

    def test_delete_permission(self):
        """测试删除权限"""
        # 使用超级管理员登录
        login_data = {"username": "admin_perm_test", "password": "testpass123"}
        login_response = self.client.post("/api/v1/auth/login", data=login_data)
        token = login_response.json()["access_token"]
        headers = {"Authorization": f"Bearer {token}"}

        # 先创建一个权限
        permission_data = {
            "code": "test:delete",
            "name": "测试删除权限",
            "resource_type": "api"
        }
        
        create_response = self.client.post(
            "/api/v1/permissions/",
            json=permission_data,
            headers=headers
        )
        permission_id = create_response.json()["id"]

        # 删除权限
        response = self.client.delete(f"/api/v1/permissions/{permission_id}", headers=headers)
        assert response.status_code == 200

        # 验证权限已删除
        get_response = self.client.get(f"/api/v1/permissions/{permission_id}", headers=headers)
        assert get_response.status_code == 404

    def test_batch_create_permissions(self):
        """测试批量创建权限"""
        # 使用超级管理员登录
        login_data = {"username": "admin_perm_test", "password": "testpass123"}
        login_response = self.client.post("/api/v1/auth/login", data=login_data)
        token = login_response.json()["access_token"]
        headers = {"Authorization": f"Bearer {token}"}

        # 批量创建权限
        permissions_data = [
            {
                "code": "test:batch1",
                "name": "批量测试权限1",
                "resource_type": "api"
            },
            {
                "code": "test:batch2", 
                "name": "批量测试权限2",
                "resource_type": "menu"
            }
        ]
        
        response = self.client.post(
            "/api/v1/permissions/batch",
            json=permissions_data,
            headers=headers
        )
        assert response.status_code == 200
        data = response.json()
        assert len(data["created"]) == 2

    def test_permission_access_denied_for_normal_user(self):
        """测试普通用户无权限访问权限管理"""
        # 使用普通用户登录
        login_data = {"username": "normal_perm_test", "password": "testpass123"}
        login_response = self.client.post("/api/v1/auth/login", data=login_data)
        token = login_response.json()["access_token"]
        headers = {"Authorization": f"Bearer {token}"}

        # 尝试获取权限列表
        response = self.client.get("/api/v1/permissions/", headers=headers)
        assert response.status_code == 403

        # 尝试创建权限
        permission_data = {
            "code": "test:forbidden",
            "name": "禁止访问权限",
            "resource_type": "api"
        }
        
        response = self.client.post(
            "/api/v1/permissions/",
            json=permission_data,
            headers=headers
        )
        assert response.status_code == 403
