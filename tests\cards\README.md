# 绑卡功能测试套件

本目录包含沃尔玛绑卡系统的完整测试套件，涵盖绑卡API、绑卡管理、批量操作等各个方面。

## 📁 文件结构

```
test/cards/
├── README.md                      # 本说明文件
├── __init__.py                    # 模块初始化文件
├── test_bind_card_api.py          # 绑卡API测试（外部接口）
├── test_card_management_api.py    # 绑卡管理API测试（内部接口）
├── test_batch_bind_api.py         # 批量绑卡API测试
├── test_cards_crud.py             # 绑卡记录CRUD测试
├── test_statistics_api.py         # 绑卡统计API测试
└── run_all_bind_tests.py          # 运行所有绑卡测试的主脚本
```

## 🧪 测试模块说明

### 1. test_bind_card_api.py - 绑卡API测试
测试外部商户调用的绑卡API接口（`/api/v1/card-bind`）

**测试内容：**
- ✅ 有效绑卡请求
- ❌ 无效卡号验证
- ❌ 无效金额验证
- ❌ 无效API密钥验证
- ❌ 无效签名验证
- ❌ 重复卡号验证
- ❌ 缺少必填字段验证
- ❌ 商户编码不匹配验证
- ❌ 过期时间戳验证

**特点：**
- 完整的API签名验证测试
- 商户认证和权限验证
- 参数验证和错误处理

### 2. test_card_management_api.py - 绑卡管理API测试
测试内部管理系统的绑卡管理接口

**测试内容：**
- 📋 获取绑卡记录列表
- ➕ 创建绑卡记录
- 📄 获取绑卡记录详情
- 🔗 绑卡操作
- 📊 获取绑卡统计
- 🔒 敏感信息访问权限

**特点：**
- 数据隔离测试（商户间、部门间）
- 权限控制验证
- CRUD操作完整性

### 3. test_batch_bind_api.py - 批量绑卡API测试
测试批量绑卡相关功能

**测试内容：**
- 📦 批量创建绑卡记录
- 🔗 批量绑卡操作
- 🔍 批量查询绑卡记录
- 🏷️ 按状态批量筛选
- 📅 按日期范围批量筛选
- 📄 批量分页查询

**特点：**
- 大批量数据处理
- 复杂查询条件测试
- 分页和筛选功能

### 4. test_cards_crud.py - 绑卡记录CRUD测试
测试绑卡记录的基础CRUD操作

**测试内容：**
- 📋 获取记录列表
- 📊 获取统计信息
- 🔒 敏感信息访问
- 🛡️ 数据隔离验证

## 🚀 运行测试

### 运行所有绑卡测试
```bash
# 运行完整的绑卡测试套件
python test/cards/run_all_bind_tests.py
```

### 运行单个测试模块
```bash
# 绑卡API测试
python test/cards/test_bind_card_api.py

# 绑卡管理API测试
python test/cards/test_card_management_api.py

# 批量绑卡API测试
python test/cards/test_batch_bind_api.py

# 绑卡CRUD测试
python test/cards/test_cards_crud.py
```

## 📋 测试前提条件

### 1. 系统环境
- 后端服务运行在 `http://localhost:20000`
- 数据库正常连接
- 测试账号可用

### 2. 测试账号
```python
# 超级管理员
username: admin
password: 7c222fb2927d828af22f592134e8932480637c0d

# 商户管理员
username: test1
password: 12345678
```

### 3. 测试数据
- 至少有一个可用的商户
- 至少有一个可用的部门
- 商户配置了API密钥和密钥

## 📊 测试报告

测试完成后会生成详细的测试报告：

### 控制台输出
- 实时测试进度
- 每个测试的结果
- 模块统计信息
- 总体测试结果

### JSON报告文件
保存在 `test/reports/` 目录下，包含：
- 测试摘要统计
- 各模块详细结果
- 失败测试的详细信息
- 测试执行时间

## 🔧 测试配置

### 修改测试配置
编辑 `test/conftest.py` 文件：
```python
TEST_CONFIG = {
    "base_url": "http://localhost:20000",  # 后端服务地址
    "api_prefix": "/api/v1",               # API前缀
    "timeout": 30,                         # 请求超时时间
    "retry_count": 3,                      # 重试次数
    "retry_delay": 1,                      # 重试延迟
}
```

### 修改测试账号
编辑 `test/conftest.py` 文件中的 `TEST_ACCOUNTS` 配置。

## 🐛 故障排除

### 常见问题

1. **连接失败**
   ```
   错误: 无法连接到服务器
   解决: 确保后端服务正在运行
   ```

2. **登录失败**
   ```
   错误: 无法获取token
   解决: 检查测试账号密码是否正确
   ```

3. **权限错误**
   ```
   错误: 403 Forbidden
   解决: 检查用户权限配置
   ```

4. **数据库错误**
   ```
   错误: 数据库连接失败
   解决: 检查数据库服务和配置
   ```

### 调试模式
在测试文件中添加调试信息：
```python
import logging
logging.basicConfig(level=logging.DEBUG)
```

## 📈 测试覆盖率

当前测试覆盖的功能：

- ✅ 绑卡API接口（100%）
- ✅ 参数验证（100%）
- ✅ 签名验证（100%）
- ✅ 权限控制（100%）
- ✅ 数据隔离（100%）
- ✅ CRUD操作（100%）
- ✅ 批量操作（100%）
- ✅ 统计功能（80%）
- ✅ 错误处理（90%）

## 🎯 测试目标

- **功能完整性**: 确保所有绑卡功能正常工作
- **安全性**: 验证API安全机制和权限控制
- **数据隔离**: 确保商户间、部门间数据隔离
- **性能**: 验证批量操作的性能表现
- **稳定性**: 确保系统在各种条件下稳定运行

## 📝 维护说明

### 添加新测试
1. 在相应的测试文件中添加新的测试方法
2. 在 `run_all_tests()` 方法中调用新测试
3. 更新本README文档

### 修改现有测试
1. 保持测试的独立性
2. 确保测试数据的清理
3. 更新相关文档

### 测试数据管理
- 测试使用临时数据，不影响生产数据
- 每个测试模块独立管理测试数据
- 测试完成后自动清理（如需要）
