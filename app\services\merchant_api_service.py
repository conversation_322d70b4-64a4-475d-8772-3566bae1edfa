"""
商户API服务类
负责处理商户相关的业务逻辑
"""
from typing import Any, Dict, List, Optional
from sqlalchemy.orm import Session
from fastapi import HTTPException, status

from app import crud, schemas
from app.models.user import User
from app.models.merchant import Merchant
from app.core.security import generate_api_key, generate_api_secret
from app.core.auth import auth_service
from app.core.logging import get_logger
from app.services import merchant as merchant_service


class MerchantAPIService:
    """商户API业务逻辑服务类"""
    
    def __init__(self, db: Session):
        self.db = db
        self.logger = get_logger("merchant_api_service")
    
    def get_merchants_list(
        self,
        current_user: User,
        skip: int = 0,
        limit: int = 100,
        search: Optional[str] = None,
        status: Optional[bool] = None,
    ) -> Dict[str, Any]:
        """
        获取商家列表
        
        Args:
            current_user: 当前用户
            skip: 跳过记录数
            limit: 限制记录数
            search: 搜索关键词
            status: 状态过滤
            
        Returns:
            Dict[str, Any]: 商家列表数据
        """
        self.logger.debug(
            f"获取商家列表: skip={skip}, limit={limit}, search={search}, status={status}"
        )

        try:
            # 根据用户角色确定查询范围（基于新的AO架构）
            if current_user.is_platform_user():
                # 超级管理员和平台管理员可以查看所有商家
                result = crud.merchant.get_multi_by_filter(
                    self.db, skip=skip, limit=limit, search=search, status=status
                )
            else:
                # 商家用户只能查看自己的商家（严格数据隔离）
                result = self._get_merchant_user_data(current_user, search, status)

            self.logger.debug(f"成功获取商家列表，共 {result['total']} 条记录")
            return result
        except Exception as e:
            self.logger.error(f"获取商家列表失败: {str(e)}", exc_info=True)
            raise

    def _get_merchant_user_data(self, current_user: User, search: Optional[str], status: Optional[bool]) -> Dict[str, Any]:
        """获取商家用户的数据"""
        if not current_user.merchant_id:
            return {"total": 0, "items": []}

        merchant = crud.merchant.get(self.db, id=current_user.merchant_id)
        if not merchant or (status is not None and merchant.status != status):
            return {"total": 0, "items": []}

        # 检查搜索条件
        if search:
            search_term = search.lower()
            if (search_term not in merchant.name.lower() and
                search_term not in (merchant.code or "").lower()):
                return {"total": 0, "items": []}

        return {"total": 1, "items": [merchant]}

    def _get_ip_whitelist_by_id(self, ip_id: int, merchant_id: int):
        """获取并验证IP白名单"""
        ip_whitelist = crud.ip_whitelist.get(self.db, id=ip_id)
        if not ip_whitelist or ip_whitelist.merchant_id != merchant_id:
            raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="IP白名单不存在")
        return ip_whitelist

    def create_merchant(
        self,
        merchant_in: schemas.MerchantCreate,
        current_user: User,
    ) -> schemas.Merchant:
        """
        创建新商家
        
        Args:
            merchant_in: 商家创建数据
            current_user: 当前用户
            
        Returns:
            schemas.Merchant: 创建的商家
        """
        # 调用服务层处理业务逻辑
        merchant = merchant_service.create_merchant(
            db=self.db, merchant_in=merchant_in, created_by=current_user.id
        )
        return merchant

    def generate_api_credentials(self) -> Dict[str, str]:
        """
        生成API密钥和密文
        
        Returns:
            Dict[str, str]: API密钥和密文
        """
        api_key = generate_api_key()
        api_secret = generate_api_secret()
        return {"api_key": api_key, "api_secret": api_secret}

    def check_merchant_access(self, current_user: User, merchant_id: int) -> Merchant:
        """
        检查商家访问权限
        
        Args:
            current_user: 当前用户
            merchant_id: 商家ID
            
        Returns:
            Merchant: 商家对象
            
        Raises:
            HTTPException: 如果商家不存在或无权限访问
        """
        # 检查商家是否存在
        merchant = crud.merchant.get(self.db, id=merchant_id)
        if not merchant:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="商家不存在",
            )

        # 数据权限检查：只能访问有权限的商家
        if not auth_service.can_access_merchant_data(current_user, merchant.id, self.db):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="没有权限访问该商家",
            )
        
        return merchant

    def get_ip_whitelist(self, current_user: User, merchant_id: int) -> Dict[str, Any]:
        """获取商家IP白名单"""
        self.check_merchant_access(current_user, merchant_id)
        ip_list = crud.ip_whitelist.get_by_merchant(self.db, merchant_id=merchant_id)
        return {"ip_list": ip_list}

    def create_ip_whitelist(self, current_user: User, merchant_id: int, ip_in: schemas.IpWhitelistCreate) -> schemas.IpWhitelist:
        """添加IP白名单"""
        self.check_merchant_access(current_user, merchant_id)

        # 检查IP是否已存在
        existing_ip = crud.ip_whitelist.get_by_ip_and_merchant(self.db, ip=ip_in.ip, merchant_id=merchant_id)
        if existing_ip:
            raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="IP已存在于白名单中")

        return crud.ip_whitelist.create_with_merchant(self.db, obj_in=ip_in, merchant_id=merchant_id)

    def update_ip_whitelist(self, current_user: User, merchant_id: int, ip_id: int, ip_in: schemas.IpWhitelistUpdate) -> schemas.IpWhitelist:
        """更新IP白名单"""
        self.check_merchant_access(current_user, merchant_id)

        # 获取并验证IP白名单
        ip_whitelist = self._get_ip_whitelist_by_id(ip_id, merchant_id)

        # 检查新IP是否已存在
        if ip_in.ip != ip_whitelist.ip:
            existing_ip = crud.ip_whitelist.get_by_ip_and_merchant(self.db, ip=ip_in.ip, merchant_id=merchant_id)
            if existing_ip and existing_ip.id != ip_id:
                raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="IP已存在于白名单中")

        return crud.ip_whitelist.update(self.db, db_obj=ip_whitelist, obj_in=ip_in)

    def delete_ip_whitelist(self, current_user: User, merchant_id: int, ip_id: int) -> schemas.IpWhitelist:
        """删除IP白名单"""
        self.check_merchant_access(current_user, merchant_id)
        self._get_ip_whitelist_by_id(ip_id, merchant_id)  # 验证存在性
        return crud.ip_whitelist.remove(self.db, id=ip_id)

    def verify_password_and_get_secret(
        self,
        current_user: User,
        merchant_id: int,
        password_in: schemas.PasswordVerify,
    ) -> Dict[str, Any]:
        """
        验证密码并返回API密文

        Args:
            current_user: 当前用户
            merchant_id: 商家ID
            password_in: 密码验证数据

        Returns:
            Dict[str, Any]: 验证结果和API密文
        """
        merchant = self.check_merchant_access(current_user, merchant_id)

        # 验证密码
        if not crud.user.verify_password(
            password_in.password, current_user.hashed_password
        ):
            return {"success": False, "message": "密码错误"}

        # 返回API密文
        return {"success": True, "api_secret": merchant.api_secret}

    def reset_api_key(self, current_user: User, merchant_id: int) -> Dict[str, str]:
        """重置商家API密钥"""
        merchant = self.check_merchant_access(current_user, merchant_id)

        # 生成新的API密钥和密文
        api_key = generate_api_key()
        api_secret = generate_api_secret()

        # 更新商家的API密钥和密文
        merchant_in = schemas.MerchantUpdate(
            api_key=api_key,
            api_secret=api_secret,
            api_key_updated_at=crud.merchant.get_current_time(),
        )
        crud.merchant.update(self.db, db_obj=merchant, obj_in=merchant_in)
        return {"api_key": api_key, "api_secret": api_secret}

    def get_api_usage(self, current_user: User, merchant_id: int) -> schemas.ApiUsageResponse:
        """获取商家API使用统计"""
        self.check_merchant_access(current_user, merchant_id)
        return merchant_service.get_api_usage_statistics(db=self.db, merchant_id=merchant_id)

    def update_merchant(
        self,
        merchant: Merchant,
        merchant_in: schemas.MerchantUpdate,
    ) -> schemas.Merchant:
        """
        更新商家信息

        Args:
            merchant: 商家对象
            merchant_in: 更新数据

        Returns:
            schemas.Merchant: 更新后的商家
        """
        self.logger.info(f"更新商户 {merchant.id}")

        if merchant_in.name and merchant_in.name != merchant.name:
            existing_merchant = crud.merchant.get_by_name(self.db, name=merchant_in.name)
            if existing_merchant and existing_merchant.id != merchant.id:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="商家名称已存在",
                )

        updated_merchant = crud.merchant.update(self.db, db_obj=merchant, obj_in=merchant_in)
        self.logger.info(f"Merchant {updated_merchant.id} updated successfully.")
        return updated_merchant

    def delete_merchant(self, merchant_id: int) -> None:
        """删除商家"""
        merchant = crud.merchant.get(self.db, id=merchant_id)
        if not merchant:
            raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="商家不存在")
        crud.merchant.remove(self.db, id=merchant_id)

    def update_merchant_status(self, merchant_id: int, status_in: schemas.MerchantStatusUpdate) -> schemas.Merchant:
        """更新商家状态"""
        merchant = crud.merchant.update_status(self.db, merchant_id=merchant_id, status=status_in.status)
        if not merchant:
            raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="商家不存在")
        return merchant

    def reset_merchant_api_key(self, merchant_id: int) -> Dict[str, str]:
        """重置商家API密钥和密文"""
        result = crud.merchant.reset_api_key(self.db, merchant_id=merchant_id)
        if not result:
            raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="商家不存在")
        return result

    def get_merchant_api_credentials(
        self,
        current_user: User,
        merchant_id: int,
    ) -> Dict[str, Any]:
        """
        获取商家API密钥和相关信息

        Args:
            current_user: 当前用户
            merchant_id: 商家ID

        Returns:
            Dict[str, Any]: API密钥和相关信息
        """
        merchant = self.check_merchant_access(current_user, merchant_id)

        return {
            "api_key": merchant.api_key,
            "api_secret": merchant.api_secret,
            "merchant_code": merchant.code,
            "merchant_name": merchant.name,
            "merchant_id": merchant.id
        }

    def get_current_merchant_api_credentials(
        self,
        current_user: User,
    ) -> Dict[str, Any]:
        """
        获取当前用户所属商家的API密钥和相关信息

        Args:
            current_user: 当前用户

        Returns:
            Dict[str, Any]: API密钥和相关信息

        Raises:
            HTTPException: 如果用户未关联商家或商家不存在
        """
        if not current_user.merchant_id:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="当前用户未关联任何商家",
            )

        merchant = crud.merchant.get(self.db, id=current_user.merchant_id)
        if not merchant:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="用户所属商家不存在",
            )

        return {
            "api_key": merchant.api_key,
            "api_secret": merchant.api_secret,
            "merchant_code": merchant.code,
            "merchant_name": merchant.name,
            "merchant_id": merchant.id
        }
