"""
CK负载均衡监控服务
用于实时监控CK使用情况和负载分布
"""

import asyncio
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
from collections import defaultdict, Counter
from sqlalchemy.orm import Session
from sqlalchemy import func

from app.db.session import SessionLocal
from app.models.walmart_ck import WalmartCK
from app.models.card_record import CardRecord
from app.core.redis import get_redis
from app.core.logging import get_logger

logger = get_logger("ck_load_balance_monitor")


class CKLoadBalanceMonitor:
    """CK负载均衡监控器"""
    
    def __init__(self):
        self.monitoring = False
        self.monitor_interval = 300  # 5分钟监控间隔
        self.alert_thresholds = {
            "max_usage_ratio": 0.7,  # 单个CK使用率超过70%告警
            "imbalance_ratio": 3.0,   # 最大使用次数超过最小使用次数3倍告警
            "redis_failure_count": 5   # Redis连续失败5次告警
        }
        
    async def start_monitoring(self, merchant_id: Optional[int] = None):
        """开始监控"""
        if self.monitoring:
            logger.warning("监控已在运行中")
            return
        
        self.monitoring = True
        logger.info(f"开始CK负载均衡监控，间隔: {self.monitor_interval}秒")
        
        try:
            while self.monitoring:
                await self._run_monitoring_cycle(merchant_id)
                await asyncio.sleep(self.monitor_interval)
        except Exception as e:
            logger.error(f"监控过程中发生错误: {e}")
        finally:
            self.monitoring = False
            logger.info("CK负载均衡监控已停止")
    
    def stop_monitoring(self):
        """停止监控"""
        self.monitoring = False
    
    async def _run_monitoring_cycle(self, merchant_id: Optional[int] = None):
        """运行一次监控周期"""
        try:
            db = SessionLocal()
            try:
                # 收集监控数据
                monitor_data = await self._collect_monitoring_data(db, merchant_id)
                
                # 分析数据并生成告警
                alerts = self._analyze_and_generate_alerts(monitor_data)
                
                # 记录监控结果
                self._log_monitoring_results(monitor_data, alerts)
                
                # 处理告警
                if alerts:
                    await self._handle_alerts(alerts, monitor_data)
                
            finally:
                db.close()
                
        except Exception as e:
            logger.error(f"监控周期执行失败: {e}")
    
    async def _collect_monitoring_data(self, db: Session, merchant_id: Optional[int] = None) -> Dict[str, Any]:
        """收集监控数据"""
        data = {
            "timestamp": datetime.now().isoformat(),
            "merchant_id": merchant_id,
            "ck_stats": {},
            "recent_usage": {},
            "redis_status": {},
            "service_status": {}
        }
        
        try:
            # CK统计信息
            query = db.query(WalmartCK).filter(WalmartCK.is_deleted == False)
            if merchant_id:
                query = query.filter(WalmartCK.merchant_id == merchant_id)

            cks = query.all()
            # 修复：确保正确检查active状态（支持布尔值和整数值）
            active_cks = [ck for ck in cks if (ck.active is True or ck.active == 1)]
            
            # 计算真正可用的CK（不仅active=True，还要检查是否达到限制）
            available_cks = []
            for ck in active_cks:
                # 检查是否达到总限制
                if ck.total_limit and ck.bind_count >= ck.total_limit:
                    continue
                available_cks.append(ck)

            data["ck_stats"] = {
                "total_count": len(cks),
                "active_count": len(active_cks),
                "available_count": len(available_cks),  # 新增：真正可用的CK数量
                "usage_distribution": self._calculate_usage_distribution(available_cks),
                "load_balance_score": self._calculate_load_balance_score(available_cks)
            }
            
            # 最近使用情况（过去1小时）
            since_time = datetime.now() - timedelta(hours=1)
            recent_query = db.query(CardRecord).filter(
                CardRecord.created_at >= since_time,
                CardRecord.walmart_ck_id.isnot(None)
            )
            
            if merchant_id:
                recent_query = recent_query.filter(CardRecord.merchant_id == merchant_id)
            
            recent_records = recent_query.all()
            data["recent_usage"] = self._analyze_recent_usage(recent_records)
            
            # Redis状态
            data["redis_status"] = await self._check_redis_status()
            
            # CK服务状态
            data["service_status"] = await self._check_service_status(db)
            
        except Exception as e:
            logger.error(f"监控数据收集失败: {e}")
            data["error"] = str(e)
        
        return data
    
    def _calculate_usage_distribution(self, cks: List[WalmartCK]) -> Dict[str, Any]:
        """计算使用分布"""
        if not cks:
            return {"min": 0, "max": 0, "avg": 0, "std": 0}
        
        usage_counts = [ck.bind_count for ck in cks]
        
        min_usage = min(usage_counts)
        max_usage = max(usage_counts)
        avg_usage = sum(usage_counts) / len(usage_counts)
        
        # 计算标准差
        variance = sum((x - avg_usage) ** 2 for x in usage_counts) / len(usage_counts)
        std_usage = variance ** 0.5
        
        return {
            "min": min_usage,
            "max": max_usage,
            "avg": avg_usage,
            "std": std_usage,
            "range": max_usage - min_usage,
            "coefficient_of_variation": std_usage / avg_usage if avg_usage > 0 else 0
        }
    
    def _calculate_load_balance_score(self, cks: List[WalmartCK]) -> float:
        """计算负载均衡分数（0-100，100为完全均衡）"""
        if not cks or len(cks) < 2:
            return 100.0
        
        usage_counts = [ck.bind_count for ck in cks]
        min_usage = min(usage_counts)
        max_usage = max(usage_counts)
        
        if max_usage == 0:
            return 100.0  # 都没有使用，认为是均衡的
        
        # 计算不均衡程度
        imbalance_ratio = max_usage / max(min_usage, 1)
        
        # 转换为分数（比例越接近1，分数越高）
        score = max(0, 100 - (imbalance_ratio - 1) * 20)
        return min(100, score)
    
    def _analyze_recent_usage(self, records: List[CardRecord]) -> Dict[str, Any]:
        """分析最近使用情况"""
        if not records:
            return {"total_bindings": 0, "ck_usage": {}, "most_used_ck": None}
        
        ck_usage = Counter(record.walmart_ck_id for record in records if record.walmart_ck_id)
        
        result = {
            "total_bindings": len(records),
            "unique_cks_used": len(ck_usage),
            "ck_usage": dict(ck_usage),
            "most_used_ck": None
        }
        
        if ck_usage:
            most_used_ck_id, most_used_count = ck_usage.most_common(1)[0]
            result["most_used_ck"] = {
                "ck_id": most_used_ck_id,
                "usage_count": most_used_count,
                "usage_percentage": (most_used_count / len(records)) * 100
            }
        
        return result
    
    async def _check_redis_status(self) -> Dict[str, Any]:
        """检查Redis状态"""
        status = {
            "connected": False,
            "response_time": None,
            "ck_keys_count": 0,
            "error": None
        }
        
        try:
            redis_client = await get_redis()
            
            # 测试连接和响应时间
            start_time = datetime.now()
            await redis_client.ping()
            end_time = datetime.now()
            
            status["connected"] = True
            status["response_time"] = (end_time - start_time).total_seconds() * 1000  # 毫秒
            
            # 统计CK相关键
            ck_keys = await redis_client.keys("walmart:ck:*")
            status["ck_keys_count"] = len(ck_keys)
            
        except Exception as e:
            status["error"] = str(e)
        
        return status
    
    async def _check_service_status(self, db: Session) -> Dict[str, Any]:
        """检查CK服务状态"""
        status = {
            "service_type": "unknown",
            "redis_enabled": False,
            "health_score": 0,
            "error": None
        }
        
        try:
            from app.services.redis_ck_wrapper import create_optimized_ck_service
            
            ck_service = create_optimized_ck_service(db)
            health_info = await ck_service.health_check()
            
            status.update(health_info)
            
            # 计算健康分数
            health_score = 0

            # 数据库连接是基础（50分）
            if health_info.get("database_connected"):
                health_score += 50

            # Redis启用并连接正常（30分）
            if health_info.get("redis_enabled") and health_info.get("redis_connected"):
                health_score += 30
            elif health_info.get("redis_enabled"):
                # Redis启用但连接异常（10分）
                health_score += 10
            else:
                # Redis未启用但数据库正常（20分）
                health_score += 20

            # 整体状态正常（20分）
            if health_info.get("overall_status") == "healthy":
                health_score += 20
            elif health_info.get("overall_status") == "degraded":
                health_score += 10

            status["health_score"] = health_score
            
        except Exception as e:
            status["error"] = str(e)
        
        return status
    
    def _analyze_and_generate_alerts(self, monitor_data: Dict[str, Any]) -> List[Dict[str, Any]]:
        """分析数据并生成告警"""
        alerts = []
        
        try:
            # 检查负载均衡分数
            ck_stats = monitor_data.get("ck_stats", {})
            load_balance_score = ck_stats.get("load_balance_score", 100)
            
            if load_balance_score < 60:
                alerts.append({
                    "type": "load_imbalance",
                    "severity": "high" if load_balance_score < 30 else "medium",
                    "message": f"负载均衡分数过低: {load_balance_score:.1f}/100",
                    "data": ck_stats
                })
            
            # 检查单个CK使用率
            recent_usage = monitor_data.get("recent_usage", {})
            most_used = recent_usage.get("most_used_ck")
            
            if most_used and most_used.get("usage_percentage", 0) > self.alert_thresholds["max_usage_ratio"] * 100:
                alerts.append({
                    "type": "high_ck_usage",
                    "severity": "high",
                    "message": f"CK {most_used['ck_id']} 使用率过高: {most_used['usage_percentage']:.1f}%",
                    "data": most_used
                })
            
            # 检查Redis状态
            redis_status = monitor_data.get("redis_status", {})
            if not redis_status.get("connected"):
                alerts.append({
                    "type": "redis_disconnected",
                    "severity": "high",
                    "message": "Redis连接失败",
                    "data": redis_status
                })
            elif redis_status.get("response_time", 0) > 1000:  # 响应时间超过1秒
                alerts.append({
                    "type": "redis_slow_response",
                    "severity": "medium",
                    "message": f"Redis响应时间过慢: {redis_status['response_time']:.1f}ms",
                    "data": redis_status
                })
            
            # 检查服务健康状态
            service_status = monitor_data.get("service_status", {})
            health_score = service_status.get("health_score", 0)
            
            if health_score < 70:
                alerts.append({
                    "type": "service_unhealthy",
                    "severity": "medium" if health_score > 50 else "high",
                    "message": f"CK服务健康分数过低: {health_score}/100",
                    "data": service_status
                })
            
        except Exception as e:
            logger.error(f"告警分析失败: {e}")
        
        return alerts
    
    def _log_monitoring_results(self, monitor_data: Dict[str, Any], alerts: List[Dict[str, Any]]):
        """记录监控结果"""
        ck_stats = monitor_data.get("ck_stats", {})
        recent_usage = monitor_data.get("recent_usage", {})
        
        logger.info(
            f"CK监控报告 | "
            f"活跃CK: {ck_stats.get('active_count', 0)}/{ck_stats.get('total_count', 0)} | "
            f"负载均衡分数: {ck_stats.get('load_balance_score', 0):.1f}/100 | "
            f"最近1小时绑卡: {recent_usage.get('total_bindings', 0)}次 | "
            f"使用CK数: {recent_usage.get('unique_cks_used', 0)} | "
            f"告警数: {len(alerts)}"
        )
        
        # 记录详细的使用分布
        usage_dist = ck_stats.get("usage_distribution", {})
        if usage_dist:
            logger.debug(
                f"CK使用分布 | "
                f"最小: {usage_dist.get('min', 0)} | "
                f"最大: {usage_dist.get('max', 0)} | "
                f"平均: {usage_dist.get('avg', 0):.1f} | "
                f"标准差: {usage_dist.get('std', 0):.1f}"
            )
    
    async def _handle_alerts(self, alerts: List[Dict[str, Any]], monitor_data: Dict[str, Any]):
        """处理告警"""
        for alert in alerts:
            severity = alert.get("severity", "low")
            message = alert.get("message", "未知告警")
            
            if severity == "high":
                logger.error(f"🚨 高级告警: {message}")
            elif severity == "medium":
                logger.warning(f"⚠️ 中级告警: {message}")
            else:
                logger.info(f"ℹ️ 低级告警: {message}")
            
            # 这里可以添加更多告警处理逻辑，比如：
            # - 发送邮件通知
            # - 调用Webhook
            # - 自动修复某些问题
            # - 记录到告警数据库


# 全局监控实例
ck_monitor = CKLoadBalanceMonitor()


async def start_ck_monitoring(merchant_id: Optional[int] = None):
    """启动CK监控"""
    await ck_monitor.start_monitoring(merchant_id)


def stop_ck_monitoring():
    """停止CK监控"""
    ck_monitor.stop_monitoring()


async def get_monitoring_status() -> Dict[str, Any]:
    """获取监控状态"""
    return {
        "monitoring": ck_monitor.monitoring,
        "interval": ck_monitor.monitor_interval,
        "thresholds": ck_monitor.alert_thresholds
    }
