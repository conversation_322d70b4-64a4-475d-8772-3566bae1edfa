"""
手动回调触发服务
提供手动触发绑卡回调的功能
"""

from datetime import datetime
from typing import Dict, Any

from sqlalchemy.orm import Session

from app.core.logging import get_logger
from app.crud import card as card_crud
from app.models.card_record import CardRecord, CardStatus, CallbackStatus
from app.models.user import User
from app.services.callback_service import CallbackService
from app.services.binding_log_service import BindingLogService
from app.models.binding_log import LogLevel
from app.utils.queue_producer import send_callback_task

logger = get_logger("manual_callback_service")


class ManualCallbackService:
    """手动回调触发服务"""

    def __init__(self, db: Session):
        self.db = db
        self.callback_service = CallbackService()

    def _get_binding_log_service(self) -> BindingLogService:
        """【安全修复】获取BindingLogService实例的辅助方法"""
        return BindingLogService(self.db)

    async def trigger_manual_callback(
        self,
        card_id: str,
        current_user: User,
        force_callback: bool = False
    ) -> Dict[str, Any]:
        """
        手动触发绑卡回调

        Args:
            card_id: 卡记录ID
            current_user: 当前操作用户
            force_callback: 是否强制回调（忽略回调状态检查）

        Returns:
            Dict[str, Any]: 回调触发结果
        """
        try:
            # 【安全修复】获取卡记录并应用数据隔离
            from app.services.card_record_service import CardRecordService
            card_service = CardRecordService(self.db)

            record = card_service.get_with_isolation(card_id, current_user)
            if not record:
                await self._log_callback_operation_rejected(
                    card_id, current_user, "RECORD_NOT_FOUND", "卡记录不存在或无权限访问"
                )
                return {
                    "success": False,
                    "error": "卡记录不存在或无权限访问",
                    "code": "RECORD_NOT_FOUND"
                }

            # 检查卡状态 - 只有已完成的记录才能触发回调（成功、失败、超时、取消等终态）
            if record.status in [CardStatus.PENDING, CardStatus.PROCESSING]:
                await self._log_callback_operation_rejected(
                    card_id, current_user, "INVALID_CARD_STATUS",
                    f"卡状态不符合要求，当前状态: {record.status}",
                    {"current_status": record.status}
                )
                return {
                    "success": False,
                    "error": "正在处理中的记录无法触发回调，请等待处理完成",
                    "code": "INVALID_STATUS",
                    "current_status": record.status
                }

            # 关键：检查回调状态
            current_callback_status = record.callback_status or CallbackStatus.PENDING

            if not force_callback and current_callback_status == CallbackStatus.SUCCESS:
                await self._log_callback_operation_rejected(
                    card_id, current_user, "CALLBACK_ALREADY_SUCCESS",
                    "回调已成功，拒绝重复触发",
                    {
                        "current_callback_status": current_callback_status,
                        "callback_time": record.callback_time.isoformat() if record.callback_time else None,
                        "callback_result": record.callback_result
                    }
                )
                return {
                    "success": False,
                    "error": "该记录回调已成功，如需重新回调请使用强制模式",
                    "code": "CALLBACK_ALREADY_SUCCESS",
                    "callback_status": current_callback_status,
                    "callback_time": record.callback_time.isoformat() if record.callback_time else None,
                    "callback_result": record.callback_result
                }

            # 获取商户信息
            merchant = record.merchant
            if not merchant:
                await self._log_callback_operation_rejected(
                    card_id, current_user, "MERCHANT_NOT_FOUND", "商户信息不存在"
                )
                return {
                    "success": False,
                    "error": "商户信息不存在",
                    "code": "MERCHANT_NOT_FOUND"
                }

            # 检查商户是否配置了回调URL
            if not merchant.callback_url:
                await self._log_callback_operation_rejected(
                    card_id, current_user, "NO_CALLBACK_URL", "商户未配置回调URL",
                    {"merchant_id": merchant.id, "merchant_name": merchant.name}
                )
                return {
                    "success": False,
                    "error": "商户未配置回调URL",
                    "code": "NO_CALLBACK_URL"
                }

            # 记录手动回调触发开始日志
            await self._log_manual_callback_start(record, current_user, force_callback, current_callback_status)

            # 发送回调任务到队列
            await send_callback_task({
                "record_id": str(record.id),
                "merchant_id": record.merchant_id,
                "retry_count": 0,  # 手动触发从0开始计数
                "ext_data": record.ext_data,
                "trace_id": record.trace_id,
                "is_manual": True  # 标记为手动触发
            })

            # 更新回调状态为待处理
            old_callback_status = record.callback_status
            old_callback_result = record.callback_result
            old_callback_time = record.callback_time

            record.callback_status = CallbackStatus.PENDING
            record.callback_time = None  # 清空之前的回调时间
            record.callback_result = f"手动触发回调，等待处理（操作人: {current_user.username}）"
            self.db.commit()

            # 记录成功日志
            await self._log_manual_callback_triggered(
                record, current_user, force_callback,
                old_callback_status, old_callback_result, old_callback_time
            )

            return {
                "success": True,
                "message": "手动回调已触发，已加入回调队列",
                "code": "CALLBACK_TRIGGERED",
                "card_id": card_id,
                "callback_url": merchant.callback_url,
                "trace_id": record.trace_id,
                "force_callback": force_callback,
                "previous_callback_status": old_callback_status
            }

        except Exception as e:
            self.db.rollback()
            logger.error(f"手动触发回调异常: {e}")

            # 记录异常日志
            if 'record' in locals():
                await self._log_manual_callback_exception(record, current_user, str(e))
            else:
                await self._log_callback_operation_rejected(
                    card_id, current_user, "CALLBACK_EXCEPTION", f"触发回调异常: {str(e)}"
                )

            return {
                "success": False,
                "error": f"触发回调过程中发生异常: {str(e)}",
                "code": "CALLBACK_EXCEPTION"
            }

    async def batch_trigger_manual_callback(
        self,
        card_ids: list,
        current_user: User,
        force_callback: bool = False
    ) -> Dict[str, Any]:
        """
        批量手动触发回调

        Args:
            card_ids: 卡记录ID列表
            current_user: 当前操作用户
            force_callback: 是否强制回调

        Returns:
            Dict[str, Any]: 批量回调触发结果
        """
        if not card_ids:
            return {
                "success": True,
                "message": "没有需要触发回调的记录",
                "results": []
            }

        results = []
        success_count = 0
        fail_count = 0

        for card_id in card_ids:
            try:
                result = await self.trigger_manual_callback(card_id, current_user, force_callback)
                results.append({
                    "card_id": card_id,
                    **result
                })
                
                if result["success"]:
                    success_count += 1
                else:
                    fail_count += 1

            except Exception as e:
                logger.error(f"批量触发回调 {card_id} 异常: {e}")
                results.append({
                    "card_id": card_id,
                    "success": False,
                    "error": str(e),
                    "code": "BATCH_CALLBACK_EXCEPTION"
                })
                fail_count += 1

        return {
            "success": True,
            "message": f"批量回调触发完成，成功: {success_count}，失败: {fail_count}",
            "total_count": len(card_ids),
            "success_count": success_count,
            "fail_count": fail_count,
            "results": results
        }

    async def get_callback_status(self, card_id: str) -> Dict[str, Any]:
        """
        获取回调状态信息

        Args:
            card_id: 卡记录ID

        Returns:
            Dict[str, Any]: 回调状态信息
        """
        try:
            record = card_crud.get(self.db, id=card_id)
            if not record:
                return {
                    "success": False,
                    "error": "卡记录不存在",
                    "code": "RECORD_NOT_FOUND"
                }

            return {
                "success": True,
                "data": {
                    "card_id": card_id,
                    "callback_status": record.callback_status,
                    "callback_result": record.callback_result,
                    "callback_time": record.callback_time.isoformat() if record.callback_time else None,
                    "merchant_callback_url": record.merchant.callback_url if record.merchant else None,
                    "trace_id": record.trace_id
                }
            }

        except Exception as e:
            logger.error(f"获取回调状态异常: {e}")
            return {
                "success": False,
                "error": f"获取回调状态失败: {str(e)}",
                "code": "GET_STATUS_EXCEPTION"
            }

    async def _log_manual_callback_start(self, record: CardRecord, user: User, force_callback: bool, current_callback_status: str):
        """记录手动回调开始日志"""
        try:
            action_type = "force_manual_callback_start" if force_callback else "manual_callback_start"
            message = f"用户 {user.username} {'强制' if force_callback else ''}手动触发回调"

            # 【安全修复】使用辅助方法获取BindingLogService实例
            binding_log_service = self._get_binding_log_service()
            await binding_log_service.log_system(
                db=self.db,
                card_record_id=str(record.id),
                message=message,
                log_level=LogLevel.INFO,
                details={
                    "action": action_type,
                    "operator_id": user.id,
                    "operator_username": user.username,
                    "force_callback": force_callback,
                    "current_callback_status": current_callback_status,
                    "previous_callback_result": record.callback_result,
                    "previous_callback_time": record.callback_time.isoformat() if record.callback_time else None,
                    "merchant_id": record.merchant_id,
                    "merchant_callback_url": record.merchant.callback_url if record.merchant else None,
                    "card_number": record.card_number[:6] + "***",
                    "timestamp": datetime.now().isoformat()
                }
            )
        except Exception as e:
            logger.error(f"记录手动回调开始日志失败: {e}")

    async def _log_manual_callback_triggered(self, record: CardRecord, user: User, force_callback: bool,
                                           old_callback_status: str, old_callback_result: str, old_callback_time):
        """记录手动回调触发成功日志"""
        try:
            action_type = "force_manual_callback_triggered" if force_callback else "manual_callback_triggered"
            message = f"手动回调{'（强制模式）' if force_callback else ''}触发成功，已加入回调队列"

            # 【安全修复】使用辅助方法获取BindingLogService实例
            binding_log_service = self._get_binding_log_service()
            await binding_log_service.log_system(
                db=self.db,
                card_record_id=str(record.id),
                message=message,
                log_level=LogLevel.INFO,
                details={
                    "action": action_type,
                    "operator_id": user.id,
                    "operator_username": user.username,
                    "force_callback": force_callback,
                    "old_callback_status": old_callback_status,
                    "old_callback_result": old_callback_result,
                    "old_callback_time": old_callback_time.isoformat() if old_callback_time else None,
                    "new_callback_status": CallbackStatus.PENDING,
                    "new_callback_result": record.callback_result,
                    "trace_id": record.trace_id,
                    "merchant_id": record.merchant_id,
                    "card_number": record.card_number[:6] + "***",
                    "timestamp": datetime.now().isoformat()
                }
            )
        except Exception as e:
            logger.error(f"记录手动回调触发日志失败: {e}")

    async def _log_callback_operation_rejected(self, card_id: str, user: User, reject_code: str,
                                             reject_reason: str, additional_details: Dict[str, Any] = None):
        """记录回调操作被拒绝的日志"""
        try:
            # 【安全修复】使用辅助方法获取BindingLogService实例
            binding_log_service = self._get_binding_log_service()
            await binding_log_service.log_system(
                db=self.db,
                card_record_id=card_id,
                message=f"用户 {user.username} 的手动回调请求被拒绝: {reject_reason}",
                log_level=LogLevel.WARNING,
                details={
                    "action": "manual_callback_rejected",
                    "operator_id": user.id,
                    "operator_username": user.username,
                    "reject_code": reject_code,
                    "reject_reason": reject_reason,
                    "card_id": card_id,
                    "timestamp": datetime.now().isoformat(),
                    **(additional_details or {})
                }
            )
        except Exception as e:
            logger.error(f"记录回调操作拒绝日志失败: {e}")

    async def _log_manual_callback_exception(self, record: CardRecord, user: User, exception_message: str):
        """记录手动回调异常日志"""
        try:
            # 【安全修复】使用辅助方法获取BindingLogService实例
            binding_log_service = self._get_binding_log_service()
            await binding_log_service.log_system(
                db=self.db,
                card_record_id=str(record.id),
                message=f"手动回调触发异常: {exception_message}",
                log_level=LogLevel.ERROR,
                details={
                    "action": "manual_callback_exception",
                    "operator_id": user.id,
                    "operator_username": user.username,
                    "exception": exception_message,
                    "timestamp": datetime.now().isoformat()
                }
            )
        except Exception as e:
            logger.error(f"记录手动回调异常日志失败: {e}")
