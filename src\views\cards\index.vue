<script setup>
import { ref, onMounted, reactive, computed, nextTick } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useCardDataStore } from '@/store/modules/cardData'
import useMerchantSwitch from '@/composables/useMerchantSwitch'
import { Download, QuestionFilled, TrendCharts, Document } from '@element-plus/icons-vue'
import { useRouter } from 'vue-router'
import LogTimeline from '@/components/business/bindCardLog/LogTimeline.vue'
import BindingTimeline from '@/components/business/bindCardLog/BindingTimeline.vue'
import { formatDateTime } from '@/utils/dateUtils'

const cardDataStore = useCardDataStore()
const loading = ref(false)
const searchForm = reactive({
  cardNumber: '',
  status: '',
  startTime: '',
  endTime: '',
  dateTimeRange: [] // 时间范围选择器的值
})

const pagination = ref({
  current: 1,
  pageSize: 10,
  total: 0
})

// 各种操作的loading状态
const retryLoading = ref(null)
const syncAmountLoading = ref(null)
const callbackLoading = ref(null)

// 绑卡详情对话框
const detailDialogVisible = ref(false)
const currentCardDetail = ref(null)
const showSensitiveInfo = ref(false)
const sensitiveLoading = ref(false)
const activeTabName = ref('basic') // 当前激活的标签页

// 时间线对话框
const timelineDialogVisible = ref(false)
const currentTimelineCard = ref(null)

// 日志对话框
const logsDialogVisible = ref(false)
const currentLogsCard = ref(null)

const router = useRouter()

// 计算统计数据
const totalBindings = computed(() =>
  (cardDataStore.cardStats?.totalSuccess || 0) + (cardDataStore.cardStats?.totalFailed || 0)
)
const todayBindings = computed(() =>
  (cardDataStore.cardStats?.todaySuccess || 0) + (cardDataStore.cardStats?.todayFailed || 0)
)

const formatTimestamp = (timestamp) => {
  if (!timestamp) return '-'
  return formatDateTime(timestamp)
}

// 获取绑卡记录数据
const fetchCardData = async (params = {}) => {
  loading.value = true
  try {
    const queryParams = {
      page: pagination.value.current,
      page_size: pagination.value.pageSize,
      ...searchForm,
      ...params
    }
    const response = await cardDataStore.fetchCardList(queryParams)
    pagination.value.total = response.total;
    // 获取统计数据 - 修复：传递完整的查询条件
    const statsParams = {
      ...searchForm,
      ...params
    }
    await cardDataStore.fetchCardStats(statsParams)
  } catch (error) {
    ElMessage.error('获取绑卡数据失败')
  } finally {
    loading.value = false
  }
}

// 使用商家切换监听器
const queryParams = computed(() => ({
  page: pagination.value.current,
  page_size: pagination.value.pageSize,
  ...searchForm
}))

const { fetchWithMerchantFilter } = useMerchantSwitch(
  fetchCardData,
  queryParams,
  'merchant_id'
)

// 处理表格页码变化
const handlePageChange = (current) => {
  pagination.value.current = current
  fetchWithMerchantFilter()
}

// 处理表格每页条数变化
const handleSizeChange = (size) => {
  pagination.value.pageSize = size
  pagination.value.current = 1
  fetchWithMerchantFilter()
}

// 处理搜索
const handleSearch = () => {
  pagination.value.current = 1
  fetchWithMerchantFilter()
}

// 重置搜索条件
const resetSearch = () => {
  // 重置除时间外的其他字段
  searchForm.cardNumber = ''
  searchForm.status = ''

  // 重置时间为当天
  setDefaultTimeRange()

  fetchWithMerchantFilter()
}

// 导出数据
const exportData = async () => {
  loading.value = true
  try {
    await cardDataStore.exportCardRecords()
    ElMessage.success('数据导出成功')
  } catch (error) {
    console.error('导出数据失败', error)
    ElMessage.error('导出数据失败')
  } finally {
    loading.value = false
  }
}

// 查看绑卡详情
const viewDetail = (row) => {
  currentCardDetail.value = row
  showSensitiveInfo.value = false
  activeTabName.value = 'basic' // 重置为基本信息标签页
  detailDialogVisible.value = true
}

// 查看绑卡日志
const viewLogs = (row) => {
  currentLogsCard.value = row
  logsDialogVisible.value = true
  // 等待对话框渲染完成后刷新日志数据
  nextTick(() => {
    if (quickLogTimelineRef.value && quickLogTimelineRef.value.refreshLogs) {
      quickLogTimelineRef.value.refreshLogs()
    } else {
      // 如果组件还没有准备好，稍后再试
      setTimeout(() => {
        if (quickLogTimelineRef.value && quickLogTimelineRef.value.refreshLogs) {
          quickLogTimelineRef.value.refreshLogs()
        }
      }, 100)
    }
  })
}

// 查看绑卡时间线
const viewTimeline = (row) => {
  currentTimelineCard.value = row
  timelineDialogVisible.value = true
  // 等待对话框渲染完成后刷新时间线数据
  nextTick(() => {
    if (bindingTimelineRef.value && bindingTimelineRef.value.refreshTimeline) {
      bindingTimelineRef.value.refreshTimeline()
    } else {
      // 如果组件还没有准备好，稍后再试
      setTimeout(() => {
        if (bindingTimelineRef.value && bindingTimelineRef.value.refreshTimeline) {
          bindingTimelineRef.value.refreshTimeline()
        }
      }, 100)
    }
  })
}

// 时间线加载完成回调
const onTimelineLoaded = (timelineData) => {
  console.log('时间线数据加载完成:', timelineData)
}

// LogTimeline 组件的引用
const logTimelineRef = ref(null)
// BindingTimeline 组件的引用
const bindingTimelineRef = ref(null)
// 快速日志组件的引用
const quickLogTimelineRef = ref(null)

// 处理标签页切换事件
const handleTabChange = (tabName) => {
  activeTabName.value = tabName
  // 当切换到操作日志标签页时，自动刷新日志数据
  if (tabName === 'logs' && logTimelineRef.value) {
    // 使用 nextTick 确保组件已经渲染完成
    nextTick(() => {
      logTimelineRef.value.refreshLogs()
    })
  }
  // 当切换到执行时间线标签页时，自动刷新时间线数据
  if (tabName === 'timeline' && bindingTimelineRef.value) {
    nextTick(() => {
      bindingTimelineRef.value.refreshTimeline()
    })
  }
}
// 删除绑卡记录
const deleteCard = (id) => {
  ElMessageBox.confirm('确定删除该绑卡记录吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async () => {
    await cardDataStore.deleteCard(id)
    ElMessage.success('删除成功')
    fetchWithMerchantFilter()
  }).catch(() => {
    ElMessage.info('已取消删除')
  })
}

// 重试绑卡记录
const retryCard = (row) => {
  // 防止重复点击
  if (retryLoading.value === row.id) {
    return
  }

  // 检查是否为CK配置相关错误
  const isCkConfigError = row.error_message && (
    row.error_message.includes('没有可用的沃尔玛CK配置') ||
    row.error_message.includes('没有可用的CK') ||
    row.error_message.includes('CK配置')
  )

  // 根据错误类型提供不同的警告信息
  let ckAvailabilityWarning = '⚠️ 如果该商户无可用CK，重试将会失败'
  if (isCkConfigError) {
    ckAvailabilityWarning = '⚠️ 检测到CK配置问题，重试前请确保商户有有效的CK配置'
  }

  // 根据状态确定显示信息
  const statusText = row.status === 'failed' ? '失败' : '待处理'
  let warningText = row.status === 'failed'
    ? '⚠️ 手动重试没有频率限制，但只有CK失效导致的失败才能重试成功'
    : '⚠️ 将重新提交该待处理记录到绑卡队列'

  if (isCkConfigError) {
    warningText = '⚠️ 该错误可能由CK配置问题导致，建议先检查CK配置'
  }

  ElMessageBox.confirm(
    `确定要重试卡号 ${row.card_number} 的绑卡操作吗？`,
    '重试确认',
    {
      confirmButtonText: '确定重试',
      cancelButtonText: '取消',
      type: 'warning',
      dangerouslyUseHTMLString: true,
      message: `
        <div>
          <p><strong>卡号：</strong>${row.card_number}</p>
          <p><strong>当前状态：</strong>${statusText}</p>
          <p><strong>重试次数：</strong>${row.retry_count || 0}</p>
          ${row.error_message ? `<p><strong>错误信息：</strong>${row.error_message}</p>` : ''}
          <br/>
          <p style="color: #E6A23C;">${warningText}</p>
          <p style="color: #F56C6C; font-size: 12px;">${ckAvailabilityWarning}</p>
        </div>
      `
    }
  ).then(async () => {
    // 设置loading状态，防止重复提交
    retryLoading.value = row.id
    try {
      const result = await cardDataStore.retryCard(row.id)
      ElMessage.success(result.message || '重试成功，已重新提交到绑卡队列')
      // 刷新列表数据
      await fetchWithMerchantFilter()
    } catch (error) {
      console.error('重试失败:', error)
      // 改进错误消息显示，特别处理CK配置相关错误
      let errorMessage = error.response?.data?.detail || error.message || '重试失败'

      // 特殊处理CK配置错误，提供更友好的提示
      if (errorMessage.includes('没有可用的CK配置')) {
        errorMessage = '该商户当前没有可用的CK配置，请联系管理员配置有效的CK后再重试'
      } else if (errorMessage.includes('CK配置')) {
        errorMessage = '检测到CK配置问题，请检查商户的CK配置是否正确'
      }

      ElMessage.error(errorMessage)
    } finally {
      // 确保loading状态被清除
      retryLoading.value = null
    }
  }).catch(() => {
    ElMessage.info('已取消重试')
  })
}

// 判断是否可以同步金额
const canSyncAmount = (row) => {
  // 必须是绑卡成功状态或金额获取失败状态
  if (row.status !== 'success' && row.status !== 'balance_failed') {
    return false
  }

  // 如果回调状态为成功且已有金额，则认为已经完全通过，禁止同步
  if (row.callback_status === 'success' && row.actual_amount !== null && row.actual_amount !== undefined) {
    return false
  }

  return true
}

// 获取同步金额按钮的提示信息
const getSyncAmountTooltip = (row) => {
  if (row.status !== 'success' && row.status !== 'balance_failed') {
    return '只能同步绑卡成功或金额获取失败的卡片金额'
  }

  if (row.callback_status === 'success' && row.actual_amount !== null && row.actual_amount !== undefined) {
    return '该卡片已完成回调且有金额信息，无需再次同步'
  }

  if (row.actual_amount !== null && row.actual_amount !== undefined) {
    return '重新同步金额'
  }

  if (row.status === 'balance_failed') {
    return '金额获取失败，点击重新获取金额'
  }

  return '同步金额'
}

// 同步卡片金额
const syncCardAmount = async (row, forceUpdate = false) => {
  // 防止重复点击
  if (syncAmountLoading.value === row.id) {
    return
  }

  // 检查是否可以同步
  if (!canSyncAmount(row)) {
    if (row.status !== 'success' && row.status !== 'balance_failed') {
      ElMessage.warning('只能同步绑卡成功或金额获取失败的卡片金额')
    } else if (row.callback_status === 'success' && row.actual_amount !== null && row.actual_amount !== undefined) {
      ElMessage.warning('该卡片已完成回调且有金额信息，无需再次同步')
    }
    return
  }

  // 如果已有金额且不强制更新，询问用户
  if (!forceUpdate && row.actual_amount !== null && row.actual_amount !== undefined) {
    try {
      await ElMessageBox.confirm(
        '该卡片已有金额信息，是否强制重新同步？',
        '同步确认',
        {
          confirmButtonText: '强制同步',
          cancelButtonText: '取消',
          type: 'warning'
        }
      )
      forceUpdate = true
    } catch {
      return
    }
  }

  syncAmountLoading.value = row.id
  try {
    const result = await cardDataStore.syncCardAmount(row.id, forceUpdate)
    if (result.success) {
      ElMessage.success(result.message || '金额同步成功')
      // 刷新列表数据
      await fetchWithMerchantFilter()
    } else {
      ElMessage.error(result.error || '金额同步失败')
    }
  } catch (error) {
    console.error('同步金额失败:', error)
    ElMessage.error(error.response?.data?.detail || error.message || '同步金额失败')
  } finally {
    syncAmountLoading.value = null
  }
}

// 判断是否可以显示回调按钮
const canShowCallbackButton = (row) => {
  // 正确的业务逻辑：只有回调失败或待处理的记录才显示回调按钮
  // 回调已成功的记录不显示按钮（除非需要强制重新回调功能）
  return row.callback_status === 'failed' || row.callback_status === 'pending' || !row.callback_status
}

// 触发手动回调
const triggerCallback = async (row, forceCallback = false) => {
  // 防止重复点击
  if (callbackLoading.value === row.id) {
    return
  }

  // 检查回调状态 - 只有回调失败或待处理的记录才能触发回调
  const currentCallbackStatus = row.callback_status || 'pending'
  if (currentCallbackStatus === 'success' && !forceCallback) {
    ElMessage.warning('该记录回调已成功，如需重新回调请使用强制模式')
    return
  }

  // 检查回调状态并处理确认逻辑
  if (!forceCallback && currentCallbackStatus === 'success') {
    // 回调已成功，需要用户确认是否强制重新触发
    try {
      await ElMessageBox.confirm(
        `该记录回调已成功，是否强制重新触发回调？`,
        '强制回调确认',
        {
          confirmButtonText: '强制重新回调',
          cancelButtonText: '取消',
          type: 'warning',
          dangerouslyUseHTMLString: true,
          message: `
            <div>
              <p><strong>卡号：</strong>${row.card_number}</p>
              <p><strong>当前回调状态：</strong><span style="color: #67c23a;">已成功</span></p>
              ${row.callback_time ? `<p><strong>回调时间：</strong>${formatDateTime(row.callback_time)}</p>` : ''}
              ${row.callback_result ? `<p><strong>回调结果：</strong>${row.callback_result}</p>` : ''}
              <br/>
              <p style="color: #E6A23C;">⚠️ 强制重新回调将重置当前的成功状态</p>
              <p style="color: #F56C6C; font-size: 12px;">⚠️ 请确认商户系统能够正确处理重复回调</p>
            </div>
          `
        }
      )
      forceCallback = true
    } catch {
      ElMessage.info('已取消回调操作')
      return
    }
  } else if (!forceCallback && (currentCallbackStatus === 'pending' || currentCallbackStatus === 'failed')) {
    // 回调待处理或失败，普通确认
    const statusText = currentCallbackStatus === 'pending' ? '待处理' : '失败'
    const statusColor = currentCallbackStatus === 'pending' ? '#909399' : '#f56c6c'

    // 根据卡状态确定回调类型
    const cardStatusText = row.status === 'success' ? '成功' :
      row.status === 'failed' ? '失败' :
        row.status === 'timeout' ? '超时' :
          row.status === 'cancelled' ? '取消' : row.status
    const cardStatusColor = row.status === 'success' ? '#67c23a' : '#f56c6c'

    try {
      await ElMessageBox.confirm(
        `确定要触发卡号 ${row.card_number} 的回调吗？`,
        '回调确认',
        {
          confirmButtonText: '确定触发',
          cancelButtonText: '取消',
          type: 'info',
          dangerouslyUseHTMLString: true,
          message: `
            <div>
              <p><strong>卡号：</strong>${row.card_number}</p>
              <p><strong>绑卡状态：</strong><span style="color: ${cardStatusColor};">${cardStatusText}</span></p>
              <p><strong>当前回调状态：</strong><span style="color: ${statusColor};">${statusText}</span></p>
              ${row.callback_time ? `<p><strong>上次回调时间：</strong>${formatDateTime(row.callback_time)}</p>` : ''}
              ${row.callback_result ? `<p><strong>回调结果：</strong>${row.callback_result}</p>` : ''}
              ${row.error_message && row.status !== 'success' ? `<p><strong>错误信息：</strong><span style="color: #f56c6c;">${row.error_message}</span></p>` : ''}
              <br/>
              <p style="color: #409EFF;">ℹ️ 将触发回调通知商户系统当前状态</p>
            </div>
          `
        }
      )
    } catch {
      ElMessage.info('已取消回调操作')
      return
    }
  } else if (!forceCallback) {
    // 其他情况（如首次触发回调）
    const cardStatusText = row.status === 'success' ? '成功' :
      row.status === 'failed' ? '失败' :
        row.status === 'timeout' ? '超时' :
          row.status === 'cancelled' ? '取消' : row.status
    const cardStatusColor = row.status === 'success' ? '#67c23a' : '#f56c6c'

    try {
      await ElMessageBox.confirm(
        `确定要触发卡号 ${row.card_number} 的回调吗？`,
        '回调确认',
        {
          confirmButtonText: '确定触发',
          cancelButtonText: '取消',
          type: 'info',
          dangerouslyUseHTMLString: true,
          message: `
            <div>
              <p><strong>卡号：</strong>${row.card_number}</p>
              <p><strong>绑卡状态：</strong><span style="color: ${cardStatusColor};">${cardStatusText}</span></p>
              ${row.error_message && row.status !== 'success' ? `<p><strong>错误信息：</strong><span style="color: #f56c6c;">${row.error_message}</span></p>` : ''}
              <br/>
              <p style="color: #409EFF;">ℹ️ 将触发回调通知商户系统当前状态</p>
            </div>
          `
        }
      )
    } catch {
      ElMessage.info('已取消回调操作')
      return
    }
  }

  callbackLoading.value = row.id
  try {
    const result = await cardDataStore.triggerCardCallback(row.id, forceCallback)
    if (result.success) {
      const successMessage = forceCallback
        ? '强制回调触发成功，已重新加入回调队列'
        : '回调触发成功，已加入回调队列'
      ElMessage.success(result.message || successMessage)
      // 刷新列表数据
      await fetchWithMerchantFilter()
    } else {
      // 处理特定的错误情况
      let errorMessage = result.error || '回调触发失败'

      if (result.code === 'CALLBACK_ALREADY_SUCCESS') {
        errorMessage = '该记录回调已成功，如需重新回调请点击"重新回调"按钮'
      } else if (result.code === 'NO_CALLBACK_URL') {
        errorMessage = '该商户未配置回调URL，无法触发回调'
      } else if (result.code === 'INVALID_STATUS') {
        errorMessage = '只能对绑卡成功的记录触发回调'
      }

      ElMessage.error(errorMessage)
    }
  } catch (error) {
    console.error('触发回调失败:', error)
    let errorMessage = error.response?.data?.detail || error.message || '触发回调失败'

    // 处理后端返回的特定错误
    if (errorMessage.includes('回调已成功')) {
      errorMessage = '该记录回调已成功，如需重新回调请使用强制模式'
    }

    ElMessage.error(errorMessage)
  } finally {
    callbackLoading.value = null
  }
}

// 获取回调按钮类型
const getCallbackButtonType = (callbackStatus) => {
  switch (callbackStatus) {
    case 'success':
      return 'warning'  // 已成功，显示为警告色表示重新操作
    case 'failed':
      return 'danger'   // 失败，显示为危险色
    case 'pending':
    default:
      return 'success'  // 待处理或其他，显示为成功色
  }
}

// 获取回调按钮文本
const getCallbackButtonText = (callbackStatus) => {
  switch (callbackStatus) {
    case 'success':
      return '重新回调'
    case 'failed':
      return '重试回调'
    case 'pending':
      return '重新触发'
    default:
      return '触发回调'
  }
}

// 获取回调按钮提示文本
const getCallbackButtonTitle = (row) => {
  const callbackStatus = row.callback_status

  switch (callbackStatus) {
    case 'failed':
      return '回调失败，点击重新触发回调'
    case 'pending':
      return '回调处理中，点击重新触发回调'
    default:
      return '触发回调到商户系统'
  }
}

// 查看敏感信息
const viewSensitiveInfo = async () => {
  if (!currentCardDetail.value) return

  sensitiveLoading.value = true
  try {
    const response = await cardDataStore.getCardRecordWithSensitive(currentCardDetail.value.id);
    if (response && response.card_password) {
      currentCardDetail.value.card_password = response.card_password
      showSensitiveInfo.value = true
      ElMessage.success('敏感信息获取成功')
    }
  } catch (error) {
    console.error('获取敏感信息失败', error)
    ElMessage.error(error?.response?.data?.detail || '获取敏感信息失败，可能没有足够权限')
  } finally {
    sensitiveLoading.value = false
  }
}

// 设置默认时间为当天
const setDefaultTimeRange = () => {
  const today = new Date()
  const startOfDay = new Date(today.getFullYear(), today.getMonth(), today.getDate(), 0, 0, 0)
  const endOfDay = new Date(today.getFullYear(), today.getMonth(), today.getDate(), 23, 59, 59)

  // 格式化为 YYYY-MM-DD HH:mm:ss
  const formatDate = (date) => {
    const year = date.getFullYear()
    const month = String(date.getMonth() + 1).padStart(2, '0')
    const day = String(date.getDate()).padStart(2, '0')
    const hours = String(date.getHours()).padStart(2, '0')
    const minutes = String(date.getMinutes()).padStart(2, '0')
    const seconds = String(date.getSeconds()).padStart(2, '0')
    return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`
  }

  const startTimeStr = formatDate(startOfDay)
  const endTimeStr = formatDate(endOfDay)

  searchForm.startTime = startTimeStr
  searchForm.endTime = endTimeStr
  searchForm.dateTimeRange = [startTimeStr, endTimeStr] // 设置时间选择器的值
}

// 组件挂载时设置默认时间并获取数据
onMounted(() => {
  setDefaultTimeRange()
  fetchWithMerchantFilter()
})
</script>

<template>
  <div class="card-data-container">
    <!-- 搜索表单 - 布局更紧凑 -->
    <el-card class="filter-card mb-4" shadow="never">
      <el-form :model="searchForm" :inline="true" label-position="left" label-width="60px">
        <el-form-item label="卡号">
          <el-input v-model="searchForm.cardNumber" placeholder="请输入卡号" clearable size="small" style="width: 180px;" />
        </el-form-item>
        <el-form-item label="状态">
          <el-select v-model="searchForm.status" placeholder="状态" clearable size="small" style="width: 120px;">
            <el-option label="成功" value="success" />
            <el-option label="失败" value="failed" />
            <el-option label="金额获取失败" value="balance_failed" />
            <el-option label="处理中" value="processing" />
            <el-option label="待处理" value="pending" />
          </el-select>
        </el-form-item>
        <el-form-item label="时间范围">
          <el-date-picker v-model="searchForm.dateTimeRange" type="datetimerange" size="small" range-separator="至"
            start-placeholder="开始时间" end-placeholder="结束时间" value-format="YYYY-MM-DD HH:mm:ss" style="width: 340px;"
            @change="(val) => {
              searchForm.startTime = val ? val[0] : '';
              searchForm.endTime = val ? val[1] : '';
            }" />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch" size="small">搜索</el-button>
          <el-button @click="resetSearch" size="small">重置</el-button>
          <el-button type="success" :icon="Download" @click="exportData" size="small">导出</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 统计数据 - 一行显示 -->
    <el-card class="stats-card mb-4" shadow="never">
      <el-row :gutter="16">
        <el-col :xs="12" :sm="12" :md="6" :lg="3">
          <el-statistic title="总绑卡数" :value="totalBindings" />
        </el-col>
        <el-col :xs="12" :sm="12" :md="6" :lg="3">
          <el-statistic title="总成功数" :value="cardDataStore.cardStats?.totalSuccess || 0">
            <template #suffix>
              <el-tag type="success" size="small" effect="plain">成功</el-tag>
            </template>
          </el-statistic>
        </el-col>
        <el-col :xs="12" :sm="12" :md="6" :lg="3">
          <el-statistic title="总失败数" :value="cardDataStore.cardStats?.totalFailed || 0">
            <template #suffix>
              <el-tag type="danger" size="small" effect="plain">失败</el-tag>
            </template>
          </el-statistic>
        </el-col>
        <el-col :xs="12" :sm="12" :md="6" :lg="3">
          <el-statistic title="成功率" :value="parseFloat((cardDataStore.cardStats?.successRate || 0).toFixed(2))"
            suffix="%" />
        </el-col>
        <el-col :xs="12" :sm="12" :md="6" :lg="3">
          <el-statistic title="今日绑卡数" :value="todayBindings" />
        </el-col>
        <el-col :xs="12" :sm="12" :md="6" :lg="3">
          <el-statistic title="今日成功数" :value="cardDataStore.cardStats?.todaySuccess || 0">
            <template #suffix>
              <el-tag type="success" size="small" effect="plain">成功</el-tag>
            </template>
          </el-statistic>
        </el-col>
        <el-col :xs="12" :sm="12" :md="6" :lg="3">
          <el-statistic title="今日失败数" :value="cardDataStore.cardStats?.todayFailed || 0">
            <template #suffix>
              <el-tag type="danger" size="small" effect="plain">失败</el-tag>
            </template>
          </el-statistic>
        </el-col>
        <el-col :xs="12" :sm="12" :md="6" :lg="3">
          <el-statistic title="今日成功率" :value="parseFloat((cardDataStore.cardStats?.todaySuccessRate || 0).toFixed(2))"
            suffix="%" />
        </el-col>
      </el-row>

      <!-- 金额统计行 -->
      <el-divider style="margin: 16px 0;" />
      <el-row :gutter="16">
        <el-col :xs="12" :sm="12" :md="6" :lg="3">
          <el-statistic title="总绑卡金额" :value="((cardDataStore.cardStats?.totalActualAmount || 0) / 100).toFixed(2)">
            <template #suffix>
              <el-tag type="primary" size="small" effect="plain">元</el-tag>
            </template>
          </el-statistic>
        </el-col>
        <el-col :xs="12" :sm="12" :md="6" :lg="3">
          <el-statistic title="总请求金额" :value="((cardDataStore.cardStats?.totalRequestAmount || 0) / 100).toFixed(2)">
            <template #suffix>
              <el-tag type="info" size="small" effect="plain">元</el-tag>
            </template>
          </el-statistic>
        </el-col>
        <el-col :xs="12" :sm="12" :md="6" :lg="3">
          <el-statistic title="今日绑卡金额" :value="((cardDataStore.cardStats?.todayActualAmount || 0) / 100).toFixed(2)">
            <template #suffix>
              <el-tag type="success" size="small" effect="plain">元</el-tag>
            </template>
          </el-statistic>
        </el-col>
        <el-col :xs="12" :sm="12" :md="6" :lg="3">
          <el-statistic title="今日请求金额" :value="((cardDataStore.cardStats?.todayRequestAmount || 0) / 100).toFixed(2)">
            <template #suffix>
              <el-tag type="warning" size="small" effect="plain">元</el-tag>
            </template>
          </el-statistic>
        </el-col>
      </el-row>
    </el-card>

    <!-- 绑卡记录表格 -->
    <el-card shadow="never">
      <el-table :data="cardDataStore.cardList" v-loading="loading" stripe>
        <el-table-column prop="card_number" label="卡号" min-width="180" show-overflow-tooltip />
        <el-table-column prop="merchant_name" label="所属商户" min-width="120" show-overflow-tooltip>
          <template #default="scope">
            <span v-if="scope.row.merchant_name">{{ scope.row.merchant_name }}</span>
            <span v-else class="text-gray-400">未知商户</span>
          </template>
        </el-table-column>
        <el-table-column prop="merchant_order_id" label="商户订单号" min-width="180" show-overflow-tooltip />
        <el-table-column prop="walmart_ck_id" label="CKID" min-width="100" show-overflow-tooltip />
        <el-table-column prop="ck_description" label="CK备注" min-width="200" show-overflow-tooltip />
        <el-table-column prop="amount" label="商户传入金额(元)" min-width="150" show-overflow-tooltip>
          <template #default="scope">
            {{ scope.row.amount ? (scope.row.amount / 100).toFixed(2) : '-' }}
          </template>
        </el-table-column>
        <el-table-column prop="actual_amount" label="真实金额(元)" min-width="150" show-overflow-tooltip>
          <template #default="scope">
            <span v-if="scope.row.actual_amount">
              <el-text type="success">
                {{ (scope.row.actual_amount / 100).toFixed(2) }}
              </el-text>
              <!-- 显示金额差异 -->
              <el-tag v-if="scope.row.amount && scope.row.actual_amount !== scope.row.amount"
                :type="scope.row.actual_amount > scope.row.amount ? 'success' : 'warning'" size="small"
                style="margin-left: 8px;">
                {{ scope.row.actual_amount > scope.row.amount ? '+' : '' }}{{
                  scope.row.actual_amount - scope.row.amount
                }}分
              </el-tag>
            </span>
            <span v-else-if="scope.row.status === 'success'" class="text-gray-400">
              <el-text type="info">未获取</el-text>
              <el-tooltip content="绑卡成功但未获取到真实金额，可点击同步金额按钮获取" placement="top">
                <el-icon style="margin-left: 4px; cursor: help;">
                  <QuestionFilled />
                </el-icon>
              </el-tooltip>
            </span>
            <span v-else-if="scope.row.status === 'balance_failed'" class="text-gray-400">
              <el-text type="warning">获取失败</el-text>
              <el-tooltip content="绑卡成功但金额获取失败，可点击同步金额按钮重新获取" placement="top">
                <el-icon style="margin-left: 4px; cursor: help;">
                  <QuestionFilled />
                </el-icon>
              </el-tooltip>
            </span>
            <span v-else class="text-gray-400">-</span>
          </template>
        </el-table-column>
        <el-table-column prop="status_name" label="状态" width="120" align="center">
          <template #default="scope">
            <el-tag :type="scope.row.status === 'success' ? 'success' :
              scope.row.status === 'failed' ? 'danger' :
                scope.row.status === 'balance_failed' ? 'warning' : 'info'" size="small">
              <!-- 状态显示映射 -->
              {{ scope.row.status === 'success' ? '成功' :
                scope.row.status === 'failed' ? '失败' :
                  scope.row.status === 'balance_failed' ? '金额获取失败' :
                    scope.row.status === 'pending' ? '待处理' :
                      scope.row.status === 'processing' ? '处理中' : scope.row.status }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="error_message" label="错误信息" min-width="200" show-overflow-tooltip />
        <el-table-column prop="process_time" label="处理耗时(秒)" width="120" align="center">
          <template #default="scope">
            {{ scope.row.process_time ? scope.row.process_time.toFixed(3) : '-' }}
          </template>
        </el-table-column>
        <el-table-column prop="created_at" label="创建时间" width="170" sortable>
          <template #default="scope">
            {{ formatTimestamp(scope.row.created_at) }}
          </template>
        </el-table-column>
        <el-table-column prop="updated_at" label="更新时间" width="170" sortable>
          <template #default="scope">
            {{ formatTimestamp(scope.row.updated_at) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" fixed="right" width="420" align="center">
          <template #default="scope">
            <el-button type="primary" link size="small" @click="viewDetail(scope.row)">查看详情</el-button>

            <!-- 日志按钮 -->
            <el-button type="info" link size="small" @click="viewLogs(scope.row)"
              :title="`查看绑卡ID ${scope.row.id} 的操作日志`">
              <el-icon>
                <Document />
              </el-icon>
              日志
            </el-button>

            <!-- 时间线按钮 -->
            <el-button type="success" link size="small" @click="viewTimeline(scope.row)"
              :title="`查看绑卡ID ${scope.row.id} 的执行时间线`">
              <el-icon>
                <TrendCharts />
              </el-icon>
              时间线
            </el-button>

            <!-- 重试按钮 -->
            <el-button v-if="scope.row.status === 'failed' || scope.row.status === 'pending'" type="warning" link
              size="small" @click="retryCard(scope.row)" :loading="retryLoading === scope.row.id">
              重试
            </el-button>

            <!-- 同步金额按钮 -->
            <el-button v-if="canSyncAmount(scope.row)" type="info" link size="small" @click="syncCardAmount(scope.row)"
              :loading="syncAmountLoading === scope.row.id" :title="getSyncAmountTooltip(scope.row)">
              {{ scope.row.actual_amount ? '重新同步' : '同步金额' }}
            </el-button>

            <!-- 已完成同步的提示按钮 - 已隐藏 -->
            <!--
            <el-button
              v-else-if="scope.row.status === 'success' && scope.row.callback_status === 'success' && scope.row.actual_amount"
              type="success" link size="small" disabled :title="getSyncAmountTooltip(scope.row)">
              已完成
            </el-button>
            -->

            <!-- 触发回调按钮 -->
            <el-button v-if="canShowCallbackButton(scope.row)" :type="getCallbackButtonType(scope.row.callback_status)"
              link size="small" @click="triggerCallback(scope.row)" :loading="callbackLoading === scope.row.id"
              :title="getCallbackButtonTitle(scope.row)">
              {{ getCallbackButtonText(scope.row.callback_status) }}
            </el-button>

            <!-- <el-button type="danger" link size="small" @click="deleteCard(scope.row.id)">删除</el-button> -->
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination v-model:current-page="pagination.current" v-model:page-size="pagination.pageSize"
          :page-sizes="[10, 20, 50, 100]" :total="pagination.total" layout="total, sizes, prev, pager, next, jumper"
          background @current-change="handlePageChange" @size-change="handleSizeChange" />
      </div>
    </el-card>

    <!-- 绑卡详情对话框 -->
    <el-dialog v-model="detailDialogVisible" title="绑卡详情" width="80%" :close-on-click-modal="false">
      <div v-if="currentCardDetail">
        <el-tabs v-model="activeTabName" @tab-change="handleTabChange">
          <el-tab-pane label="基本信息" name="basic">
            <el-descriptions :column="2" border size="small">
              <el-descriptions-item label="记录ID">{{ currentCardDetail.id }}</el-descriptions-item>
              <el-descriptions-item label="卡号">{{ currentCardDetail.card_number }}</el-descriptions-item>
              <el-descriptions-item label="所属商户">
                <span v-if="currentCardDetail.merchant_name">{{ currentCardDetail.merchant_name }}</span>
                <span v-else class="text-gray-400">未知商户</span>
              </el-descriptions-item>
              <el-descriptions-item label="所属部门">
                <span v-if="currentCardDetail.department_name">{{ currentCardDetail.department_name }}</span>
                <span v-else class="text-gray-400">无部门</span>
              </el-descriptions-item>
              <el-descriptions-item label="状态">
                <el-tag
                  :type="currentCardDetail.status === 'success' ? 'success' : currentCardDetail.status === 'failed' ? 'danger' : 'info'"
                  size="small">
                  {{ currentCardDetail.status_name || currentCardDetail.status }}
                </el-tag>
              </el-descriptions-item>
              <el-descriptions-item label="商户传入金额">
                <span v-if="currentCardDetail.amount">
                  {{ currentCardDetail.amount + '分（' + (currentCardDetail.amount / 100).toFixed(2) + '元）' }}
                </span>
                <span v-else>-</span>
              </el-descriptions-item>
              <el-descriptions-item label="真实金额">
                <span v-if="currentCardDetail.actual_amount">
                  <el-text type="success">
                    {{
                      currentCardDetail.actual_amount + '分（' + (currentCardDetail.actual_amount / 100).toFixed(2) + '元）'
                    }}
                  </el-text>
                  <!-- 显示金额差异 -->
                  <el-tag
                    v-if="currentCardDetail.amount && currentCardDetail.actual_amount !== currentCardDetail.amount"
                    :type="currentCardDetail.actual_amount > currentCardDetail.amount ? 'success' : 'warning'"
                    size="small" style="margin-left: 8px;">
                    差异：{{
                      currentCardDetail.actual_amount > currentCardDetail.amount ? '+' : ''
                    }}{{ currentCardDetail.actual_amount - currentCardDetail.amount }}分
                  </el-tag>
                </span>
                <span v-else-if="currentCardDetail.status === 'success'" class="text-gray-400">
                  <el-text type="info">未获取到真实金额</el-text>
                </span>
                <span v-else>-</span>
              </el-descriptions-item>
              <el-descriptions-item label="金额详细信息"
                v-if="currentCardDetail.balance || currentCardDetail.cardBalance || currentCardDetail.balanceCnt">
                <div style="font-size: 12px; color: #606266;">
                  <div v-if="currentCardDetail.balance">Balance: {{ currentCardDetail.balance }}元</div>
                  <div v-if="currentCardDetail.cardBalance">CardBalance: {{ currentCardDetail.cardBalance }}分</div>
                  <div v-if="currentCardDetail.balanceCnt">BalanceCnt: {{ currentCardDetail.balanceCnt }}元</div>
                </div>
              </el-descriptions-item>
              <el-descriptions-item label="创建时间">{{
                formatTimestamp(currentCardDetail.created_at)
              }}
              </el-descriptions-item>
              <el-descriptions-item label="更新时间">{{
                formatTimestamp(currentCardDetail.updated_at)
              }}
              </el-descriptions-item>
              <el-descriptions-item label="处理耗时(秒)">{{
                currentCardDetail.process_time ?
                  currentCardDetail.process_time.toFixed(3) : '-'
              }}
              </el-descriptions-item>
              <el-descriptions-item label="重试次数">{{ currentCardDetail.retry_count || 0 }}</el-descriptions-item>
              <el-descriptions-item label="请求IP">{{ currentCardDetail.ip_address || 'N/A' }}</el-descriptions-item>
              <el-descriptions-item label="请求ID">{{ currentCardDetail.request_id || 'N/A' }}</el-descriptions-item>
              <el-descriptions-item label="错误信息" :span="2" v-if="currentCardDetail.status === 'failed'">
                <el-alert type="error" :closable="false" show-icon>
                  {{ currentCardDetail.error_message || '无详细错误信息' }}
                </el-alert>
              </el-descriptions-item>
              <el-descriptions-item label="回调状态">
                <el-tag v-if="currentCardDetail.callback_status"
                  :type="currentCardDetail.callback_status === 'success' ? 'success' : currentCardDetail.callback_status_name === 'failed' ? 'danger' : 'warning'"
                  size="small" effect="light">
                  {{ currentCardDetail.callback_status_name }}
                </el-tag>
                <span v-else>-</span>
              </el-descriptions-item>
              <el-descriptions-item label="回调结果" :span="currentCardDetail.callback_status ? 1 : 2">
                <el-alert v-if="currentCardDetail.callback_result" :title="currentCardDetail.callback_result"
                  :type="currentCardDetail.callback_status === 'success' ? 'success' : 'error'" :closable="false"
                  show-icon style="padding: 6px 12px; margin: 0;" />
                <span v-else-if="currentCardDetail.callback_status">-</span>
                <span v-else>-</span>
              </el-descriptions-item>
              <el-descriptions-item label="卡密" :span="2">
                <span v-if="showSensitiveInfo">{{ currentCardDetail.card_password }}</span>
                <el-button v-else type="primary" link size="small" @click="viewSensitiveInfo"
                  :loading="sensitiveLoading">
                  点击查看
                </el-button>
              </el-descriptions-item>
              <el-descriptions-item label="请求数据" :span="2">
                <pre class="code-block">{{ JSON.stringify(currentCardDetail.request_data || {}, null, 2) }}</pre>
              </el-descriptions-item>
              <el-descriptions-item label="响应数据" :span="2">
                <pre class="code-block">{{ JSON.stringify(currentCardDetail.response_data || {}, null, 2) }}</pre>
              </el-descriptions-item>
              <el-descriptions-item label="备注" :span="2">
                {{ currentCardDetail.remark || '-' }}
              </el-descriptions-item>
            </el-descriptions>
          </el-tab-pane>
          <el-tab-pane label="操作日志" name="logs">
            <LogTimeline ref="logTimelineRef" :card-id="currentCardDetail.id" />
          </el-tab-pane>
          <el-tab-pane label="执行时间线" name="timeline">
            <BindingTimeline ref="bindingTimelineRef" :card-record-id="currentCardDetail.id" />
          </el-tab-pane>
        </el-tabs>
      </div>
      <template #footer>
        <el-button @click="detailDialogVisible = false">关闭</el-button>
      </template>
    </el-dialog>

    <!-- 时间线对话框 -->
    <el-dialog v-model="timelineDialogVisible" :title="`绑卡时间线 - ID: ${currentTimelineCard?.id || ''}`" width="90%"
      :close-on-click-modal="false" top="5vh">
      <div v-if="currentTimelineCard" class="timeline-dialog-content">
        <!-- 绑卡基本信息 -->
        <el-card class="card-info-summary" shadow="never">
          <div class="card-summary">
            <div class="summary-item">
              <span class="label">卡号:</span>
              <span class="value">{{ currentTimelineCard.card_number }}</span>
            </div>
            <div class="summary-item">
              <span class="label">状态:</span>
              <el-tag
                :type="currentTimelineCard.status === 'success' ? 'success' : currentTimelineCard.status === 'failed' ? 'danger' : 'info'"
                size="small">
                {{ currentTimelineCard.status_name || currentTimelineCard.status }}
              </el-tag>
            </div>
            <div class="summary-item">
              <span class="label">处理耗时:</span>
              <span class="value">{{ currentTimelineCard.process_time ? currentTimelineCard.process_time.toFixed(3) +
                '秒' :
                '-' }}</span>
            </div>
            <div class="summary-item">
              <span class="label">创建时间:</span>
              <span class="value">{{ formatTimestamp(currentTimelineCard.created_at) }}</span>
            </div>
          </div>
        </el-card>

        <!-- 时间线组件 -->
        <BindingTimeline ref="bindingTimelineRef" :card-record-id="currentTimelineCard.id"
          @timeline-loaded="onTimelineLoaded" />
      </div>
      <template #footer>
        <el-button @click="timelineDialogVisible = false">关闭</el-button>
        <el-button type="primary" @click="viewDetail(currentTimelineCard)">查看详情</el-button>
      </template>
    </el-dialog>

    <!-- 日志对话框 -->
    <el-dialog v-model="logsDialogVisible" :title="`操作日志 - ID: ${currentLogsCard?.id || ''}`" width="90%"
      :close-on-click-modal="false" top="5vh">
      <div v-if="currentLogsCard" class="logs-dialog-content">
        <!-- 绑卡基本信息 -->
        <el-card class="card-info-summary" shadow="never">
          <div class="card-summary">
            <div class="summary-item">
              <span class="label">卡号:</span>
              <span class="value">{{ currentLogsCard.card_number }}</span>
            </div>
            <div class="summary-item">
              <span class="label">状态:</span>
              <el-tag
                :type="currentLogsCard.status === 'success' ? 'success' : currentLogsCard.status === 'failed' ? 'danger' : 'info'"
                size="small">
                {{ currentLogsCard.status_name || currentLogsCard.status }}
              </el-tag>
            </div>
            <div class="summary-item">
              <span class="label">处理耗时:</span>
              <span class="value">{{ currentLogsCard.process_time ? currentLogsCard.process_time.toFixed(3) + '秒' : '-'
              }}</span>
            </div>
            <div class="summary-item">
              <span class="label">创建时间:</span>
              <span class="value">{{ formatTimestamp(currentLogsCard.created_at) }}</span>
            </div>
          </div>
        </el-card>

        <!-- 日志组件 -->
        <LogTimeline ref="quickLogTimelineRef" :card-id="currentLogsCard.id" />
      </div>
      <template #footer>
        <el-button @click="logsDialogVisible = false">关闭</el-button>
        <el-button type="primary" @click="viewDetail(currentLogsCard)">查看详情</el-button>
      </template>
    </el-dialog>

  </div>
</template>

<style scoped>
.card-data-container {
  padding: 20px;
}

.mb-4 {
  margin-bottom: 16px;
  /* Reduce margin */
}

.text-gray-400 {
  color: #9ca3af;
  font-style: italic;
}

/* Make filter card more compact */
.filter-card :deep(.el-form-item) {
  margin-bottom: 8px;
  /* Reduce bottom margin for form items */
  margin-right: 10px;
  /* Add some right margin */
}

.filter-card :deep(.el-form-item__label) {
  padding-right: 6px;
  /* Reduce padding after label */
}


/* Styles for the new stats card */
.stats-card :deep(.el-statistic__head) {
  color: #606266;
  /* Grey color for title */
  font-size: 13px;
  /* Slightly smaller title */
  margin-bottom: 4px;
}

.stats-card :deep(.el-statistic__content) {
  font-size: 20px;
  /* Smaller content font size */
  font-weight: bold;
}

.stats-card :deep(.el-tag) {
  margin-left: 6px;
  /* Space before tag suffix */
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}

.code-block {
  background: #f5f7fa;
  color: #606266;
  padding: 8px;
  border-radius: 4px;
  overflow: auto;
  margin: 0;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', 'Consolas', 'source-code-pro', monospace;
  font-size: 13px;
  line-height: 1.4;
  max-height: 200px;
}

.mx-2 {
  margin-left: 0.5rem;
  margin-right: 0.5rem;
}

/* Remove old stat card styles */
/*
.stat-card {
  text-align: center;
}

.stat-header {
  color: #909399;
  font-size: 14px;
}

.stat-value {
  font-size: 24px;
  font-weight: bold;
  margin-top: 10px;
}

.stat-value.success {
  color: #67c23a;
}

.stat-value.error {
  color: #f56c6c;
}
*/

/* 时间线对话框样式 */
.timeline-dialog-content {
  max-height: 70vh;
  overflow-y: auto;
}

/* 日志对话框样式 */
.logs-dialog-content {
  max-height: 70vh;
  overflow-y: auto;
}

.card-info-summary {
  margin-bottom: 20px;
  border: 1px solid #e4e7ed;
}

.card-summary {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
  padding: 8px 0;
}

.summary-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.summary-item .label {
  font-weight: 600;
  color: #606266;
  font-size: 14px;
  min-width: 80px;
}

.summary-item .value {
  color: #303133;
  font-size: 14px;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .card-summary {
    grid-template-columns: 1fr;
    gap: 12px;
  }

  .summary-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }

  .summary-item .label {
    min-width: auto;
  }
}
</style>