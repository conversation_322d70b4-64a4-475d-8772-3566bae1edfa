<template>
  <div class="bind-container">
    <el-card shadow="hover" class="bind-card">
      <template #header>
        <div class="card-header">
          <h2>单卡绑定</h2>
          <span>单卡绑定</span>
        </div>
      </template>

      <el-alert type="info" show-icon :closable="false" class="mb-5" title="此页面用于测试单卡绑卡接口，完全模拟商户调用方式，请确保参数符合接口文档要求" />

      <!-- 单卡绑定表单 -->
      <div class="bind-form">
        <el-row :gutter="24">
          <el-col :xs="24" :sm="12">
            <div class="form-item">
              <label class="form-label">
                API密钥 (api-key) <span class="required">*</span>
                <el-button type="primary" size="small" :loading="autoFillLoading"
                  @click="autoFillApiCredentials(userStore.isSuperAdmin && merchantStore.currentMerchant ? merchantStore.currentMerchant.id : null)"
                  style="margin-left: 8px;" :icon="Refresh">
                  {{ autoFillLoading ? '获取中...' : '自动填充' }}
                </el-button>
              </label>
              <el-input v-model="form.apiKey" placeholder="请输入API密钥" :prefix-icon="Key" clearable />
              <div class="error-message" v-if="errors.apiKey">{{ errors.apiKey }}</div>
            </div>
          </el-col>
          <el-col :xs="24" :sm="12">
            <div class="form-item">
              <label class="form-label">API密钥密文 (Secret) <span class="required">*</span></label>
              <form>
                <el-input v-model="form.apiSecret" placeholder="请输入API密钥密文" show-password :prefix-icon="Lock"
                  clearable />
              </form>
              <div class="error-message" v-if="errors.apiSecret">{{ errors.apiSecret }}</div>
            </div>
          </el-col>
        </el-row>

        <el-row :gutter="24">
          <el-col :xs="24" :sm="12">
            <div class="form-item">
              <label class="form-label">卡号 (card_number) <span class="required">*</span></label>
              <el-input v-model="form.cardNumber" placeholder="请输入卡号" :prefix-icon="CreditCard" clearable />
              <div class="error-message" v-if="errors.cardNumber">{{ errors.cardNumber }}</div>
            </div>
          </el-col>
          <el-col :xs="24" :sm="12">
            <div class="form-item">
              <label class="form-label">卡密 (card_password) <span class="required">*</span></label>
              <form>
                <el-input v-model="form.cardPassword" placeholder="请输入6位卡密" show-password :prefix-icon="Lock"
                  maxlength="6" show-word-limit clearable />
              </form>
              <div class="error-message" v-if="errors.cardPassword">{{ errors.cardPassword }}</div>
            </div>
          </el-col>
        </el-row>

        <el-row :gutter="24">
          <el-col :xs="24" :sm="12">
            <div class="form-item">
              <label class="form-label">商家编码 (merchant_code) <span class="required">*</span></label>
              <el-input v-model="form.merchantCode" placeholder="请输入商家编码" :prefix-icon="Shop" clearable />
              <div class="error-message" v-if="errors.merchantCode">{{ errors.merchantCode }}</div>
            </div>
          </el-col>
          <el-col :xs="24" :sm="12">
            <div class="form-item">
              <label class="form-label">扩展数据 (ext_data)</label>
              <el-input v-model="form.extData" placeholder="请输入扩展数据 (可选, 最多512字符)" :prefix-icon="Document" clearable
                maxlength="512" show-word-limit />
            </div>
          </el-col>
        </el-row>

        <el-row :gutter="24">
          <el-col :xs="24" :sm="12">
            <div class="form-item">
              <label class="form-label">
                商户订单号 (merchant_order_id)
                <el-button type="primary" size="small" :icon="Refresh"
                  @click="generateOrderIdManually('merchantOrderId', form.merchantCode)" style="margin-left: 8px;"
                  title="生成订单号">
                  生成
                </el-button>
              </label>
              <el-input v-model="form.merchantOrderId" placeholder="请输入商户订单号（留空将自动生成）" :prefix-icon="Document"
                @blur="handleOrderIdBlur('merchantOrderId', form.merchantCode)" clearable />
              <div class="order-id-hint" v-if="form.merchantOrderId && isSystemGeneratedOrderId(form.merchantOrderId)">
                <el-tag type="info" size="small">
                  <el-icon>
                    <InfoFilled />
                  </el-icon>
                  系统自动生成
                </el-tag>
                <span class="formatted-order-id">{{ formatOrderId(form.merchantOrderId) }}</span>
              </div>
              <div class="error-message" v-if="errors.merchantOrderId">{{ errors.merchantOrderId }}</div>
            </div>
          </el-col>
          <el-col :xs="24" :sm="12">
            <div class="form-item">
              <label class="form-label">订单金额 (amount) <span class="required">*</span></label>
              <el-input v-model="form.amount" placeholder="请输入金额（元），最小值10元" type="number" :prefix-icon="Money"
                step="0.01" min="10" clearable>
                <template #append>元</template>
              </el-input>
              <div class="error-message" v-if="errors.amount">{{ errors.amount }}</div>
              <div class="form-hint">支持小数点，如10元，提交时自动转换为1000分</div>
            </div>
          </el-col>
        </el-row>

        <el-divider />

        <div class="form-actions">
          <el-button type="primary" :loading="loading" @click="onSubmit" :icon="Position" size="large">
            发送请求
          </el-button>
          <el-button @click="resetForm" :icon="Refresh" size="large" type="default">
            重置表单
          </el-button>
          <el-button @click="$router.push('/bind/batch-bind')" :icon="Document" size="large" type="info">
            切换到批量绑卡
          </el-button>
        </div>
      </div>

      <el-divider v-if="requestInfo || result" content-position="center">请求与响应</el-divider>

      <el-row :gutter="20" v-if="requestInfo || result">
        <!-- Request Section -->
        <el-col :xs="24" :md="12" v-if="requestInfo" class="result-col">
          <el-card shadow="hover" class="result-card request-info">
            <template #header>
              <div class="section-header">
                <el-icon :size="18" style="margin-right: 8px;">
                  <Upload />
                </el-icon>
                <span>发送的请求</span>
              </div>
            </template>
            <el-descriptions :column="1" border size="small">
              <el-descriptions-item label="请求URL">
                <el-tag type="info" size="small">/api/v1/card-bind</el-tag>
              </el-descriptions-item>
              <el-descriptions-item label="请求方法">
                <el-tag type="primary" size="small">POST</el-tag>
              </el-descriptions-item>
              <el-descriptions-item label="请求头">
                <pre class="code-block">{{ JSON.stringify(requestInfo.headers, null, 2) }}</pre>
              </el-descriptions-item>
              <el-descriptions-item label="请求体">
                <pre class="code-block">{{ JSON.stringify(requestInfo.body, null, 2) }}</pre>
              </el-descriptions-item>
            </el-descriptions>
          </el-card>
        </el-col>

        <!-- Response Section -->
        <el-col :xs="24" :md="12" v-if="result" class="result-col">
          <el-card shadow="hover" class="result-card response-info">
            <template #header>
              <div class="section-header">
                <el-icon :size="18" style="margin-right: 8px;">
                  <Download />
                </el-icon>
                <span>收到的响应</span>
              </div>
            </template>
            <el-descriptions :column="1" border size="small">
              <el-descriptions-item label="状态码">
                <el-tag :type="result.status === 200 ? 'success' : 'danger'" size="small" effect="light">
                  {{ result.status }}
                </el-tag>
              </el-descriptions-item>
              <el-descriptions-item label="响应内容">
                <pre class="code-block response-data">{{ formatResponseData(result.data) }}</pre>
              </el-descriptions-item>
            </el-descriptions>
          </el-card>
        </el-col>
      </el-row>

    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, onUnmounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { cardDataApi } from '@/api/modules/cardData'
import { merchantApi } from '@/api/modules/merchant'
import { useUserStore } from '@/store/modules/user'
import { useMerchantStore } from '@/store/modules/merchant'
import merchantSwitchListener from '@/utils/merchantSwitchListener'
import CryptoJS from 'crypto-js'
import {
  Lock,
  Key,
  Document,
  CreditCard,
  Shop,
  Position,
  Refresh,
  Upload,
  Download,
  Money,
  InfoFilled
} from '@element-plus/icons-vue'
import {
  generateMerchantOrderId,
  isSystemGeneratedOrderId,
  formatOrderIdDisplay,
  validateOrderId
} from '@/utils/orderUtils'
import { yuanToFen, validateYuanAmount } from '@/utils/amountUtils'

// Store
const userStore = useUserStore()
const merchantStore = useMerchantStore()

const loading = ref(false)
const result = ref(null)
const requestInfo = ref(null)
const autoFillLoading = ref(false)

// 表单数据
const form = reactive({
  apiKey: '',
  apiSecret: '',
  cardNumber: '',
  cardPassword: '',
  merchantCode: '',
  extData: '',
  merchantOrderId: '',
  amount: ''
})

// 错误信息
const errors = reactive({
  apiKey: '',
  apiSecret: '',
  cardNumber: '',
  cardPassword: '',
  merchantCode: '',
  merchantOrderId: '',
  amount: ''
})

// API密钥自动填充功能
const autoFillApiCredentials = async (merchantId = null) => {
  try {
    autoFillLoading.value = true
    let apiCredentials = null

    if (userStore.isSuperAdmin && merchantId) {
      // 超级管理员获取指定商家的API密钥
      apiCredentials = await merchantApi.getApiCredentials(merchantId)
    } else {
      // 商家用户获取自己商家的API密钥
      apiCredentials = await merchantApi.getCurrentMerchantApiCredentials()
    }

    if (apiCredentials) {
      form.apiKey = apiCredentials.api_key || ''
      form.apiSecret = apiCredentials.api_secret || ''
      form.merchantCode = apiCredentials.merchant_code || ''

      ElMessage.success(`已自动填充 ${apiCredentials.merchant_name} 的API密钥信息`)
    }
  } catch (error) {
    console.error('获取API密钥失败:', error)
    ElMessage.error('获取API密钥失败: ' + (error.response?.data?.detail || error.message))
  } finally {
    autoFillLoading.value = false
  }
}

// 商家切换监听器
let unregisterMerchantSwitch = null

// 监听商家切换事件（仅超级管理员）
const handleMerchantSwitch = async () => {
  if (userStore.isSuperAdmin && merchantStore.currentMerchant) {
    await autoFillApiCredentials(merchantStore.currentMerchant.id)
  }
}

// 自定义表单验证
const validateForm = () => {
  let isValid = true

  // 重置所有错误
  Object.keys(errors).forEach(key => errors[key] = '')

  // API密钥
  if (!form.apiKey) {
    errors.apiKey = '请输入API密钥'
    isValid = false
  }

  // API密钥密文
  if (!form.apiSecret) {
    errors.apiSecret = '请输入API密钥密文'
    isValid = false
  }

  // 卡号
  if (!form.cardNumber) {
    errors.cardNumber = '请输入卡号'
    isValid = false
  }

  // 卡密
  if (!form.cardPassword) {
    errors.cardPassword = '请输入卡密'
    isValid = false
  } else if (form.cardPassword.length !== 6) {
    errors.cardPassword = '卡密必须是6位'
    isValid = false
  }

  // 商家编码
  if (!form.merchantCode) {
    errors.merchantCode = '请输入商家编码'
    isValid = false
  }

  // 商户订单号 - 使用新的自动生成逻辑
  if (!form.merchantOrderId || form.merchantOrderId.trim() === '') {
    form.merchantOrderId = generateMerchantOrderId('WM', form.merchantCode)
  }

  // 订单金额（用户输入元，验证后转换为分）
  const amountValidation = validateYuanAmount(form.amount, 10)
  if (!amountValidation.isValid) {
    errors.amount = amountValidation.message
    isValid = false
  }

  return isValid
}

// 优化的签名生成函数 - 与后端算法保持一致
const generateSignature = (data, secret, timestamp, nonce, method = 'POST', path = '/api/v1/card-bind') => {
  // 1. 规范化请求参数
  const normalizedData = {}

  // 只包含非空值，按键名排序
  Object.keys(data).sort().forEach(key => {
    if (data[key] !== null && data[key] !== undefined && data[key] !== '') {
      normalizedData[key] = data[key]
    }
  })

  // 2. 创建规范化的JSON字符串（与后端完全一致）
  // 使用与Python json.dumps(separators=(',', ':'), sort_keys=True, ensure_ascii=False)完全一致的方法
  // 重要：必须使用紧凑格式且不转义Unicode字符
  const jsonStr = JSON.stringify(normalizedData, Object.keys(normalizedData).sort())
    .replace(/\s+/g, '') // 移除所有空白字符
    .replace(/\\u[\da-f]{4}/gi, (match) => {
      // 将Unicode转义序列转换回原始字符（与ensure_ascii=False一致）
      return String.fromCharCode(parseInt(match.replace('\\u', ''), 16))
    })

  // 3. 构建签名字符串（使用竖线分隔，与后端一致）
  const signatureComponents = [
    method.toUpperCase(),
    path,
    timestamp,
    nonce,
    jsonStr,
    secret
  ]
  const signString = signatureComponents.join('|')

  console.log('签名字符串:', signString)

  // 4. 使用HMAC-SHA256生成签名并Base64编码（与后端一致）
  const hmacDigest = CryptoJS.HmacSHA256(signString, secret)
  const signature = CryptoJS.enc.Base64.stringify(hmacDigest)

  console.log('生成的签名:', signature)

  return signature
}

// 发送API请求的通用函数
const sendApiRequest = async (data, headers) => {
  try {
    const response = await cardDataApi.bindCard(data, { headers })
    return {
      status: 200,
      data: response
    }
  } catch (error) {
    console.error('API请求失败:', error)
    throw error
  }
}

const onSubmit = async () => {
  // 验证表单
  if (!validateForm()) {
    ElMessage.error('请完成表单必填项')
    return
  }

  try {
    loading.value = true

    // 生成时间戳和随机数（时间戳使用毫秒级，与后端一致）
    const timestamp = Date.now().toString()
    const nonce = Math.random().toString(36).substring(2, 15)

    // 准备请求数据（不包含timestamp和nonce，这些通过请求头传递）
    const requestData = {
      card_number: form.cardNumber,
      card_password: form.cardPassword,
      merchant_code: form.merchantCode,
      merchant_order_id: form.merchantOrderId,
      amount: yuanToFen(form.amount) // 将用户输入的元转换为分
    }

    // 如果有扩展数据，添加到请求中
    if (form.extData && form.extData.trim()) {
      requestData.ext_data = form.extData.trim()
    }

    // 生成签名（使用不包含timestamp和nonce的数据）
    const signature = generateSignature(requestData, form.apiSecret, timestamp, nonce)

    // 准备请求头
    const headers = {
      'Content-Type': 'application/json',
      'api-key': form.apiKey,
      'X-Timestamp': timestamp,
      'X-Nonce': nonce,
      'X-Signature': signature
    }

    // 保存请求信息用于显示
    requestInfo.value = {
      headers: headers,
      body: requestData
    }

    // 发送请求
    const response = await sendApiRequest(requestData, headers)
    result.value = response

    ElMessage.success('绑卡请求发送成功')
  } catch (error) {
    console.error('绑卡失败:', error)
    result.value = {
      status: error.response?.status || 500,
      data: error.response?.data || { error: error.message }
    }
    ElMessage.error('绑卡失败: ' + (error.response?.data?.detail || error.message))
  } finally {
    loading.value = false
  }
}

// 重置表单
const resetForm = () => {
  Object.keys(form).forEach(key => {
    form[key] = ''
  })
  Object.keys(errors).forEach(key => {
    errors[key] = ''
  })
  result.value = null
  requestInfo.value = null
  ElMessage.success('表单已重置')
}

// 格式化响应数据
const formatResponseData = (data) => {
  try {
    return JSON.stringify(data, null, 2)
  } catch (error) {
    return String(data)
  }
}

// 订单号相关方法
const handleOrderIdBlur = (fieldName, merchantCode = '') => {
  if (!form[fieldName] || form[fieldName].trim() === '') {
    form[fieldName] = generateMerchantOrderId('WM', merchantCode)
    ElMessage.success('已自动生成订单号')
  }
}

const generateOrderIdManually = (fieldName, merchantCode = '', force = false) => {
  if (force || !form[fieldName] || form[fieldName].trim() === '') {
    form[fieldName] = generateMerchantOrderId('WM', merchantCode)
    ElMessage.success('订单号已生成')
  } else {
    ElMessageBox.confirm('当前已有订单号，是否要重新生成？', '确认', {
      confirmButtonText: '重新生成',
      cancelButtonText: '取消',
      type: 'warning'
    }).then(() => {
      form[fieldName] = generateMerchantOrderId('WM', merchantCode)
      ElMessage.success('订单号已重新生成')
    }).catch(() => {
      // 用户取消，不做任何操作
    })
  }
}

const formatOrderId = (orderId) => {
  return formatOrderIdDisplay(orderId)
}

// 组件挂载时的初始化
onMounted(async () => {
  // 注册商家切换监听器
  if (userStore.isSuperAdmin) {
    unregisterMerchantSwitch = merchantSwitchListener.registerRefreshFunction(handleMerchantSwitch)
  }

  // 自动填充API密钥（如果是商家用户或超级管理员已选择商家）
  if (!userStore.isSuperAdmin || (userStore.isSuperAdmin && merchantStore.currentMerchant)) {
    await autoFillApiCredentials(userStore.isSuperAdmin && merchantStore.currentMerchant ? merchantStore.currentMerchant.id : null)
  }
})

// 组件卸载时清理
onUnmounted(() => {
  if (unregisterMerchantSwitch) {
    unregisterMerchantSwitch()
  }
})
</script>

<style scoped>
.bind-container {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.bind-card {
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-header h2 {
  margin: 0;
  font-size: 1.3rem;
  color: var(--el-text-color-primary);
  font-weight: 600;
}

.mb-5 {
  margin-bottom: 25px;
}

/* 自定义表单样式 */
.form-item {
  margin-bottom: 20px;
}

.form-label {
  display: block;
  margin-bottom: 8px;
  font-size: 14px;
  color: var(--el-text-color-regular);
  line-height: 1.5;
}

.required {
  color: #f56c6c;
  margin-right: 4px;
}

.error-message {
  color: #f56c6c;
  font-size: 12px;
  margin-top: 4px;
  line-height: 1;
}

.order-id-hint {
  margin-top: 8px;
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 12px;
  color: var(--el-text-color-secondary);
}

.formatted-order-id {
  font-family: 'Monaco', 'Menlo', 'Courier New', monospace;
  background: var(--el-fill-color-lighter);
  padding: 2px 6px;
  border-radius: 3px;
  font-size: 11px;
}

.form-actions {
  display: flex;
  justify-content: center;
  margin-top: 20px;
  padding-top: 10px;
}

.form-actions .el-button {
  min-width: 130px;
  margin: 0 10px;
}

.code-block {
  background: var(--el-fill-color-lighter);
  color: var(--el-text-color-regular);
  padding: 10px 15px;
  border: 1px solid var(--el-border-color-light);
  border-radius: 4px;
  overflow: auto;
  margin: 0;
  font-family: 'Monaco', 'Menlo', 'Courier New', monospace;
  font-size: 0.85rem;
  line-height: 1.6;
  max-height: 350px;
  white-space: pre-wrap;
  word-break: break-all;
}

.result-col {
  margin-bottom: 20px;
}

.result-card {
  height: 100%;
}

.section-header {
  display: flex;
  align-items: center;
  font-size: 1.1rem;
  font-weight: 500;
  color: var(--el-text-color-primary);
}

:deep(.el-descriptions__cell) {
  padding-top: 8px !important;
  padding-bottom: 8px !important;
}

:deep(.el-descriptions__label) {
  font-weight: normal;
  color: var(--el-text-color-secondary);
  background-color: var(--el-fill-color-light);
  width: 100px;
}

:deep(.el-descriptions__content) {
  padding: 8px 12px !important;
}

.response-data {
  color: #1f77b4;
}

.el-divider--horizontal {
  margin: 30px 0;
}

.form-hint {
  font-size: 12px;
  color: var(--el-text-color-secondary);
  margin-top: 4px;
  line-height: 1.4;
}
</style>
