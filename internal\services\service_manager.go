package services

import (
	"context"
	"fmt"
	"sync"
	"time"

	"walmart-bind-card-processor/internal/config"

	"github.com/go-redis/redis/v8"
	"go.uber.org/zap"
	"gorm.io/gorm"
)

// ServiceManager 服务管理器
type ServiceManager struct {
	db            *gorm.DB
	redis         *redis.Client
	logger        *zap.Logger
	config        *config.Config
	serviceFactory *ServiceFactory

	// 服务实例
	statusSyncService *CKStatusSyncService
	monitoringService *CKMonitoringService
	weightManager     *CKWeightManager
	preoccupationManager *CKPreoccupationManager
	connectionMonitor *ConnectionMonitor
	systemRecovery    *SystemRecoveryService

	// 运行状态
	running bool
	stopCh  chan struct{}
	wg      sync.WaitGroup
}

// NewServiceManager 创建服务管理器
func NewServiceManager(db *gorm.DB, redis *redis.Client, logger *zap.Logger, config *config.Config) *ServiceManager {
	serviceFactory := NewServiceFactory(config, db, redis, logger)

	return &ServiceManager{
		db:            db,
		redis:         redis,
		logger:        logger,
		config:        config,
		serviceFactory: serviceFactory,
		stopCh:        make(chan struct{}),
	}
}

// Start 启动所有服务
func (sm *ServiceManager) Start(ctx context.Context) error {
	if sm.running {
		return fmt.Errorf("服务管理器已在运行")
	}
	
	sm.running = true
	
	sm.logger.Info("开始启动CK管理服务...")

	// 1. 启动CK状态同步服务
	sm.statusSyncService = sm.serviceFactory.CreateCKStatusSyncService()
	if err := sm.statusSyncService.Start(ctx); err != nil {
		return fmt.Errorf("启动CK状态同步服务失败: %w", err)
	}
	sm.logger.Info("✅ CK状态同步服务启动成功")

	// 2. 启动CK监控服务
	sm.monitoringService = sm.serviceFactory.CreateCKMonitoringService()
	if err := sm.monitoringService.Start(ctx); err != nil {
		return fmt.Errorf("启动CK监控服务失败: %w", err)
	}
	sm.logger.Info("✅ CK监控服务启动成功")

	// 3. 初始化权重管理器
	sm.weightManager = sm.serviceFactory.CreateCKWeightManager()
	sm.logger.Info("✅ CK权重管理器初始化成功")

	// 4. 初始化预占用管理器
	sm.preoccupationManager = sm.serviceFactory.CreateCKPreoccupationManager()
	sm.logger.Info("✅ CK预占用管理器初始化成功")

	// 5. 启动连接池监控服务
	sm.connectionMonitor = sm.serviceFactory.CreateConnectionMonitor()
	sm.wg.Add(1)
	go sm.connectionMonitor.Start(ctx)
	sm.logger.Info("✅ 连接池监控服务启动成功")

	// 6. 启动系统恢复服务
	sm.systemRecovery = sm.serviceFactory.CreateSystemRecoveryService()
	sm.wg.Add(1)
	go sm.systemRecovery.Start(ctx)
	sm.logger.Info("✅ 系统恢复服务启动成功")

	// 7. 启动健康检查
	sm.wg.Add(1)
	go sm.healthChecker(ctx)

	// 8. 启动性能监控
	sm.wg.Add(1)
	go sm.performanceMonitor(ctx)
	
	sm.logger.Info("🚀 所有CK管理服务启动完成")
	return nil
}

// Stop 停止所有服务
func (sm *ServiceManager) Stop() {
	if !sm.running {
		return
	}
	
	sm.logger.Info("开始停止CK管理服务...")
	
	sm.running = false
	close(sm.stopCh)
	
	// 停止各个服务
	if sm.statusSyncService != nil {
		sm.statusSyncService.Stop()
		sm.logger.Info("✅ CK状态同步服务已停止")
	}

	if sm.monitoringService != nil {
		sm.monitoringService.Stop()
		sm.logger.Info("✅ CK监控服务已停止")
	}

	// 连接池监控和系统恢复服务通过context取消自动停止
	sm.logger.Info("✅ 连接池监控服务已停止")
	sm.logger.Info("✅ 系统恢复服务已停止")
	
	// 等待所有协程结束
	sm.wg.Wait()
	
	sm.logger.Info("🛑 所有CK管理服务已停止")
}

// GetStatusSyncService 获取状态同步服务
func (sm *ServiceManager) GetStatusSyncService() *CKStatusSyncService {
	return sm.statusSyncService
}

// GetMonitoringService 获取监控服务
func (sm *ServiceManager) GetMonitoringService() *CKMonitoringService {
	return sm.monitoringService
}

// GetWeightManager 获取权重管理器
func (sm *ServiceManager) GetWeightManager() *CKWeightManager {
	return sm.weightManager
}

// GetPreoccupationManager 获取预占用管理器
func (sm *ServiceManager) GetPreoccupationManager() *CKPreoccupationManager {
	return sm.preoccupationManager
}

// GetConnectionMonitor 获取连接池监控服务
func (sm *ServiceManager) GetConnectionMonitor() *ConnectionMonitor {
	return sm.connectionMonitor
}

// GetSystemRecoveryService 获取系统恢复服务
func (sm *ServiceManager) GetSystemRecoveryService() *SystemRecoveryService {
	return sm.systemRecovery
}

// healthChecker 健康检查协程
func (sm *ServiceManager) healthChecker(ctx context.Context) {
	defer sm.wg.Done()
	
	ticker := time.NewTicker(30 * time.Second)
	defer ticker.Stop()
	
	for {
		select {
		case <-sm.stopCh:
			return
		case <-ticker.C:
			sm.performHealthCheck(ctx)
		}
	}
}

// performHealthCheck 执行健康检查
func (sm *ServiceManager) performHealthCheck(ctx context.Context) {
	// 检查数据库连接
	if err := sm.checkDatabaseHealth(ctx); err != nil {
		sm.logger.Error("数据库健康检查失败", zap.Error(err))
	}
	
	// 检查Redis连接
	if err := sm.checkRedisHealth(ctx); err != nil {
		sm.logger.Error("Redis健康检查失败", zap.Error(err))
	}
	
	// 检查CK可用性
	if err := sm.checkCKAvailability(ctx); err != nil {
		sm.logger.Error("CK可用性检查失败", zap.Error(err))
	}
}

// checkDatabaseHealth 检查数据库健康状态
func (sm *ServiceManager) checkDatabaseHealth(ctx context.Context) error {
	sqlDB, err := sm.db.DB()
	if err != nil {
		return fmt.Errorf("获取数据库连接失败: %w", err)
	}
	
	if err := sqlDB.PingContext(ctx); err != nil {
		return fmt.Errorf("数据库ping失败: %w", err)
	}
	
	return nil
}

// checkRedisHealth 检查Redis健康状态
func (sm *ServiceManager) checkRedisHealth(ctx context.Context) error {
	_, err := sm.redis.Ping(ctx).Result()
	if err != nil {
		return fmt.Errorf("redis ping失败: %w", err)
	}
	
	return nil
}

// checkCKAvailability 检查CK可用性
func (sm *ServiceManager) checkCKAvailability(ctx context.Context) error {
	var count int64
	err := sm.db.WithContext(ctx).Table("walmart_ck").
		Where("active = ? AND is_deleted = ?", true, false).
		Count(&count).Error
	
	if err != nil {
		return fmt.Errorf("查询可用CK数量失败: %w", err)
	}
	
	if count == 0 {
		return fmt.Errorf("没有可用的CK")
	}
	
	sm.logger.Debug("CK可用性检查通过", zap.Int64("available_count", count))
	return nil
}

// performanceMonitor 性能监控协程
func (sm *ServiceManager) performanceMonitor(ctx context.Context) {
	defer sm.wg.Done()
	
	ticker := time.NewTicker(time.Minute)
	defer ticker.Stop()
	
	for {
		select {
		case <-sm.stopCh:
			return
		case <-ticker.C:
			sm.collectPerformanceMetrics(ctx)
		}
	}
}

// collectPerformanceMetrics 收集性能指标
func (sm *ServiceManager) collectPerformanceMetrics(ctx context.Context) {
	// 收集数据库连接池指标
	if sqlDB, err := sm.db.DB(); err == nil {
		stats := sqlDB.Stats()
		sm.logger.Debug("数据库连接池指标",
			zap.Int("open_connections", stats.OpenConnections),
			zap.Int("in_use", stats.InUse),
			zap.Int("idle", stats.Idle),
			zap.Int64("wait_count", stats.WaitCount),
			zap.Duration("wait_duration", stats.WaitDuration))
	}
	
	// 收集Redis连接池指标
	poolStats := sm.redis.PoolStats()
	sm.logger.Debug("Redis连接池指标",
		zap.Uint32("hits", poolStats.Hits),
		zap.Uint32("misses", poolStats.Misses),
		zap.Uint32("timeouts", poolStats.Timeouts),
		zap.Uint32("total_conns", poolStats.TotalConns),
		zap.Uint32("idle_conns", poolStats.IdleConns),
		zap.Uint32("stale_conns", poolStats.StaleConns))
	
	// 收集CK指标
	if sm.monitoringService != nil {
		metrics := sm.monitoringService.GetAllMetrics()
		totalCKs := len(metrics)
		activeCKs := 0
		totalSuccessRate := 0.0
		
		for _, metric := range metrics {
			if metric.TotalRequests > 0 {
				activeCKs++
				totalSuccessRate += metric.SuccessRate
			}
		}
		
		avgSuccessRate := 0.0
		if activeCKs > 0 {
			avgSuccessRate = totalSuccessRate / float64(activeCKs)
		}
		
		sm.logger.Info("CK性能指标汇总",
			zap.Int("total_cks", totalCKs),
			zap.Int("active_cks", activeCKs),
			zap.Float64("avg_success_rate", avgSuccessRate))
	}
}

// GetSystemStatus 获取系统状态
func (sm *ServiceManager) GetSystemStatus(ctx context.Context) map[string]interface{} {
	status := map[string]interface{}{
		"running": sm.running,
		"services": map[string]bool{
			"status_sync": sm.statusSyncService != nil,
			"monitoring":  sm.monitoringService != nil,
			"weight_manager": sm.weightManager != nil,
			"preoccupation_manager": sm.preoccupationManager != nil,
		},
	}
	
	// 添加健康检查结果
	status["health"] = map[string]bool{
		"database": sm.checkDatabaseHealth(ctx) == nil,
		"redis":    sm.checkRedisHealth(ctx) == nil,
		"ck_availability": sm.checkCKAvailability(ctx) == nil,
	}
	
	// 添加性能指标
	if sm.monitoringService != nil {
		metrics := sm.monitoringService.GetAllMetrics()
		status["metrics"] = map[string]interface{}{
			"total_cks": len(metrics),
			"active_cks": func() int {
				count := 0
				for _, m := range metrics {
					if m.TotalRequests > 0 {
						count++
					}
				}
				return count
			}(),
		}
	}
	
	return status
}
