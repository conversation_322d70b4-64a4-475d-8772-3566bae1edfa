-- ========================================
-- 00-字符集初始化脚本
-- 执行顺序：第0步 - 确保字符集正确设置
-- 目的：解决Docker MySQL中文乱码问题
-- ========================================

-- 设置会话字符集为UTF8MB4
SET NAMES utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 设置字符集相关变量
SET character_set_client = utf8mb4;
SET character_set_connection = utf8mb4;
SET character_set_database = utf8mb4;
SET character_set_results = utf8mb4;
SET character_set_server = utf8mb4;

-- 设置排序规则
SET collation_connection = utf8mb4_unicode_ci;
SET collation_database = utf8mb4_unicode_ci;
SET collation_server = utf8mb4_unicode_ci;

-- 设置时区
SET time_zone = '+08:00';

-- 设置SQL模式（确保兼容性）
SET sql_mode = 'STRICT_TRANS_TABLES,NO_ZERO_DATE,NO_ZERO_IN_DATE,ERROR_FOR_DIVISION_BY_ZERO';

-- 显示当前字符集设置（用于调试）
SELECT 
    @@character_set_client as client_charset,
    @@character_set_connection as connection_charset,
    @@character_set_database as database_charset,
    @@character_set_results as results_charset,
    @@character_set_server as server_charset,
    @@collation_connection as connection_collation,
    @@collation_database as database_collation,
    @@collation_server as server_collation,
    @@time_zone as timezone,
    '字符集初始化完成' as status;

-- ========================================
-- 字符集初始化完成
-- ========================================
