package model

import (
	"time"
)

// WalmartServer 沃尔玛API配置模型
// 严格按照数据库表字段定义，不允许修改字段类型和名称
type WalmartServer struct {
	// 基础字段 (对应BaseModel)
	ID        uint      `gorm:"primarykey" json:"id"`
	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`
	// 注意：数据库表中没有deleted_at字段，所以不使用gorm.DeletedAt

	// API基本配置
	APIURL  string `gorm:"column:api_url;type:varchar(255);not null;comment:API地址" json:"api_url"`
	Referer string `gorm:"column:referer;type:varchar(500);not null;default:https://servicewechat.com/wx81d3e1fe4c2e11b4/175/page-frame.html;comment:HTTP请求Referer头" json:"referer"`

	// 请求配置
	Timeout    int `gorm:"column:timeout;type:int;not null;default:30;comment:请求超时时间(秒)" json:"timeout"`
	RetryCount int `gorm:"column:retry_count;type:int;not null;default:3;comment:重试次数" json:"retry_count"`

	// 系统限制配置
	DailyBindLimit          int `gorm:"column:daily_bind_limit;type:int;not null;default:1000;comment:每日绑卡限制" json:"daily_bind_limit"`
	APIRateLimit            int `gorm:"column:api_rate_limit;type:int;not null;default:60;comment:API速率限制(次/分钟)" json:"api_rate_limit"`
	MaxRetryTimes           int `gorm:"column:max_retry_times;type:int;not null;default:3;comment:最大重试次数" json:"max_retry_times"`
	BindTimeoutSeconds      int `gorm:"column:bind_timeout_seconds;type:int;not null;default:30;comment:绑卡超时时间(秒)" json:"bind_timeout_seconds"`
	VerificationCodeExpires int `gorm:"column:verification_code_expires;type:int;not null;default:300;comment:验证码过期时间(秒)" json:"verification_code_expires"`
	LogRetentionDays        int `gorm:"column:log_retention_days;type:int;not null;default:90;comment:日志保留天数" json:"log_retention_days"`	// 安全配置
	EnableIPWhitelist   bool `gorm:"column:enable_ip_whitelist;type:boolean;not null;default:true;comment:启用IP白名单" json:"enable_ip_whitelist"`
	EnableSecurityAudit bool `gorm:"column:enable_security_audit;type:boolean;not null;default:true;comment:启用安全审计" json:"enable_security_audit"`

	// 维护模式
	MaintenanceMode    bool   `gorm:"column:maintenance_mode;type:boolean;not null;default:false;comment:维护模式" json:"maintenance_mode"`
	MaintenanceMessage string `gorm:"column:maintenance_message;type:text;comment:维护模式消息" json:"maintenance_message"`

	// 状态
	IsActive bool `gorm:"column:is_active;type:boolean;not null;default:true;comment:是否激活" json:"is_active"`

	// 附加配置
	ExtraConfig string `gorm:"column:extra_config;type:json;comment:附加配置" json:"extra_config"`
}

// TableName 指定表名
func (WalmartServer) TableName() string {
	return "walmart_server"
}

// IsMaintenanceMode 检查是否处于维护模式
func (ws *WalmartServer) IsMaintenanceMode() bool {
	return ws.MaintenanceMode
}

// IsActiveServer 检查服务器是否激活且非维护模式
func (ws *WalmartServer) IsActiveServer() bool {
	return ws.IsActive && !ws.MaintenanceMode
}

// GetAPIURL 获取API地址
func (ws *WalmartServer) GetAPIURL() string {
	return ws.APIURL
}

// GetReferer 获取Referer头
func (ws *WalmartServer) GetReferer() string {
	if ws.Referer == "" {
		return "https://servicewechat.com/wx81d3e1fe4c2e11b4/175/page-frame.html"
	}
	return ws.Referer
}