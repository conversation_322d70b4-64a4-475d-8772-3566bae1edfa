"""
CK防超卖测试
验证CK绑卡限制机制，确保绝对不会出现超卖现象
"""

import pytest
import asyncio
import time
from typing import List, Dict, Any
from sqlalchemy.orm import Session
from sqlalchemy.exc import IntegrityError

from app.models.walmart_ck import WalmartCK
from app.models.department import Department
from app.models.merchant import Merchant
from app.services.simplified_ck_service import SimplifiedCKService, AtomicCKUpdateService


class TestCKOversellPrevention:
    """CK防超卖测试类"""
    
    @pytest.fixture(autouse=True)
    def setup_test_data(self, db: Session):
        """设置测试数据"""
        # 创建测试商户
        merchant = Merchant(
            name="防超卖测试商户",
            code="OVERSELL_TEST_MERCHANT",
            status=True
        )
        db.add(merchant)
        db.flush()
        
        # 创建测试部门
        department = Department(
            merchant_id=merchant.id,
            name="防超卖测试部门",
            code="OVERSELL_TEST_DEPT",
            status=True,
            enable_binding=True,
            binding_weight=100
        )
        db.add(department)
        db.flush()
        
        # 创建接近限制的测试CK
        ck = WalmartCK(
            merchant_id=merchant.id,
            department_id=department.id,
            sign="oversell_test_ck@token#sign#version",
            total_limit=5,  # 小限制便于测试
            bind_count=0,
            active=True,
            is_deleted=False
        )
        db.add(ck)
        db.commit()
        
        self.merchant = merchant
        self.department = department
        self.ck = ck
    
    @pytest.mark.asyncio
    async def test_single_ck_exact_limit_no_oversell(self, db: Session):
        """测试单个CK达到精确限制时不会超卖"""
        print("\n=== 测试单个CK精确限制防超卖 ===")
        
        service = AtomicCKUpdateService(db)
        total_limit = self.ck.total_limit
        successful_bindings = 0
        
        # 尝试绑卡次数超过限制
        for i in range(total_limit + 3):  # 比限制多3次
            print(f"第{i+1}次绑卡尝试...")
            
            # 选择CK
            selected_ck = await service.atomic_ck_selection_and_reserve(
                merchant_id=self.merchant.id
            )
            
            if selected_ck:
                # 模拟绑卡成功
                await service.commit_ck_usage(selected_ck.id, success=True)
                successful_bindings += 1
                print(f"  ✅ 绑卡成功，总成功次数: {successful_bindings}")
            else:
                print(f"  ❌ 无可用CK，停止绑卡")
                break
        
        # 验证结果
        db.refresh(self.ck)
        print(f"\n最终结果:")
        print(f"  CK限制: {total_limit}")
        print(f"  实际绑卡: {self.ck.bind_count}")
        print(f"  成功次数: {successful_bindings}")
        print(f"  CK状态: {'禁用' if not self.ck.active else '启用'}")
        
        # 断言：绑卡次数不能超过限制
        assert self.ck.bind_count <= total_limit, f"CK超卖！bind_count({self.ck.bind_count}) > total_limit({total_limit})"
        assert self.ck.bind_count == total_limit, f"CK未达到预期限制: {self.ck.bind_count} != {total_limit}"
        assert not self.ck.active, "CK达到限制后应该被禁用"
        assert successful_bindings == total_limit, f"成功绑卡次数错误: {successful_bindings} != {total_limit}"
    
    @pytest.mark.asyncio
    async def test_concurrent_binding_no_oversell(self, db: Session):
        """测试高并发绑卡不会超卖"""
        print("\n=== 测试高并发绑卡防超卖 ===")
        
        # 设置CK接近限制
        self.ck.bind_count = self.ck.total_limit - 2  # 还能绑2张
        db.commit()
        
        service = AtomicCKUpdateService(db)
        concurrent_requests = 10  # 10个并发请求争抢2个名额
        
        async def single_binding_attempt(task_id: int):
            """单个绑卡尝试"""
            try:
                print(f"  任务{task_id}: 开始选择CK")
                
                # 选择CK
                selected_ck = await service.atomic_ck_selection_and_reserve(
                    merchant_id=self.merchant.id
                )
                
                if selected_ck:
                    print(f"  任务{task_id}: 选择CK成功，模拟绑卡...")
                    
                    # 模拟绑卡处理时间
                    await asyncio.sleep(0.01)
                    
                    # 提交绑卡成功
                    await service.commit_ck_usage(selected_ck.id, success=True)
                    print(f"  任务{task_id}: ✅ 绑卡成功")
                    return True
                else:
                    print(f"  任务{task_id}: ❌ 无可用CK")
                    return False
                    
            except Exception as e:
                print(f"  任务{task_id}: ❌ 异常: {e}")
                return False
        
        # 启动并发任务
        print(f"启动{concurrent_requests}个并发绑卡任务...")
        tasks = [single_binding_attempt(i) for i in range(concurrent_requests)]
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # 统计结果
        successful_count = sum(1 for result in results if result is True)
        failed_count = sum(1 for result in results if result is False)
        exception_count = sum(1 for result in results if isinstance(result, Exception))
        
        # 验证最终状态
        db.refresh(self.ck)
        
        print(f"\n并发测试结果:")
        print(f"  并发请求数: {concurrent_requests}")
        print(f"  成功绑卡数: {successful_count}")
        print(f"  失败请求数: {failed_count}")
        print(f"  异常请求数: {exception_count}")
        print(f"  最终bind_count: {self.ck.bind_count}")
        print(f"  CK限制: {self.ck.total_limit}")
        print(f"  CK状态: {'禁用' if not self.ck.active else '启用'}")
        
        # 断言：不能超卖
        assert self.ck.bind_count <= self.ck.total_limit, f"并发场景下CK超卖！"
        assert successful_count == 2, f"应该只有2次成功绑卡: {successful_count}"
        assert self.ck.bind_count == self.ck.total_limit, f"CK应该达到限制: {self.ck.bind_count}"
        assert not self.ck.active, "CK达到限制后应该被禁用"
    
    @pytest.mark.asyncio
    async def test_binding_failure_rollback_no_oversell(self, db: Session):
        """测试绑卡失败回滚不会导致超卖"""
        print("\n=== 测试绑卡失败回滚防超卖 ===")
        
        # 设置CK接近限制
        self.ck.bind_count = self.ck.total_limit - 1  # 还能绑1张
        db.commit()
        
        service = AtomicCKUpdateService(db)
        
        # 第一次绑卡：预占用但失败
        print("第1次绑卡：预占用但失败...")
        ck1 = await service.atomic_ck_selection_and_reserve(merchant_id=self.merchant.id)
        assert ck1 is not None, "应该能选择到CK"
        
        db.refresh(self.ck)
        print(f"  预占用后bind_count: {self.ck.bind_count}")
        assert self.ck.bind_count == self.ck.total_limit, "预占用后应该达到限制"
        
        # 模拟绑卡失败，回滚预占用
        await service.commit_ck_usage(ck1.id, success=False)
        
        db.refresh(self.ck)
        print(f"  回滚后bind_count: {self.ck.bind_count}")
        assert self.ck.bind_count == self.ck.total_limit - 1, "回滚后应该恢复到原来的计数"
        assert self.ck.active, "回滚后CK应该重新启用"
        
        # 第二次绑卡：成功
        print("第2次绑卡：成功...")
        ck2 = await service.atomic_ck_selection_and_reserve(merchant_id=self.merchant.id)
        assert ck2 is not None, "回滚后应该能再次选择CK"
        
        await service.commit_ck_usage(ck2.id, success=True)
        
        db.refresh(self.ck)
        print(f"  成功后bind_count: {self.ck.bind_count}")
        assert self.ck.bind_count == self.ck.total_limit, "成功后应该达到限制"
        assert not self.ck.active, "达到限制后应该被禁用"
        
        # 第三次绑卡：应该无法选择
        print("第3次绑卡：应该无法选择...")
        ck3 = await service.atomic_ck_selection_and_reserve(merchant_id=self.merchant.id)
        assert ck3 is None, "CK达到限制后不应该能选择"
        
        print("✅ 绑卡失败回滚测试通过")
    
    @pytest.mark.asyncio
    async def test_mixed_success_failure_no_oversell(self, db: Session):
        """测试成功失败混合场景不会超卖"""
        print("\n=== 测试混合成功失败场景防超卖 ===")
        
        service = AtomicCKUpdateService(db)
        total_limit = self.ck.total_limit
        
        # 场景：成功-失败-成功-失败-成功
        scenarios = [True, False, True, False, True]
        expected_final_count = sum(scenarios)  # 3次成功
        
        for i, should_succeed in enumerate(scenarios):
            print(f"第{i+1}次绑卡: {'成功' if should_succeed else '失败'}")
            
            # 选择CK
            selected_ck = await service.atomic_ck_selection_and_reserve(
                merchant_id=self.merchant.id
            )
            
            if selected_ck:
                # 提交结果
                await service.commit_ck_usage(selected_ck.id, success=should_succeed)
                
                db.refresh(self.ck)
                print(f"  当前bind_count: {self.ck.bind_count}")
            else:
                print(f"  无可用CK")
                break
        
        # 验证最终状态
        db.refresh(self.ck)
        print(f"\n最终结果:")
        print(f"  期望成功次数: {expected_final_count}")
        print(f"  实际bind_count: {self.ck.bind_count}")
        print(f"  CK限制: {total_limit}")
        
        assert self.ck.bind_count <= total_limit, "不能超过限制"
        assert self.ck.bind_count == expected_final_count, f"计数错误: {self.ck.bind_count} != {expected_final_count}"
        
        print("✅ 混合场景测试通过")
    
    def test_database_constraint_prevents_oversell(self, db: Session):
        """测试数据库约束防止超卖（如果已添加约束）"""
        print("\n=== 测试数据库约束防超卖 ===")
        
        try:
            # 尝试直接设置超过限制的bind_count
            self.ck.bind_count = self.ck.total_limit + 1
            db.commit()
            
            # 如果没有抛出异常，说明约束未生效
            print("⚠️  数据库约束未生效，建议添加CHECK约束")
            
            # 恢复正常值
            self.ck.bind_count = 0
            db.commit()
            
        except IntegrityError as e:
            # 约束生效，回滚事务
            db.rollback()
            print("✅ 数据库约束生效，成功防止超卖")
            print(f"  约束错误: {e}")
        
        except Exception as e:
            db.rollback()
            print(f"❌ 意外错误: {e}")


if __name__ == "__main__":
    pytest.main([__file__, "-v", "-s"])
