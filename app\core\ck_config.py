#!/usr/bin/env python3
"""
CK服务配置 - 控制是否使用简化CK服务
"""

import os
from typing import Optional


class CKServiceConfig:
    """CK服务配置类"""
    
    # 是否启用简化CK服务（默认启用）
    USE_SIMPLIFIED_CK_SERVICE: bool = os.getenv("USE_SIMPLIFIED_CK_SERVICE", "true").lower() == "true"
    
    # 是否启用Redis CK优化（默认禁用，因为我们要移除Redis依赖）
    ENABLE_REDIS_CK_OPTIMIZATION: bool = os.getenv("ENABLE_REDIS_CK_OPTIMIZATION", "false").lower() == "true"
    
    # CK选择超时时间（秒）
    CK_SELECTION_TIMEOUT: int = int(os.getenv("CK_SELECTION_TIMEOUT", "5"))
    
    # 负载均衡候选CK数量
    LOAD_BALANCE_CANDIDATE_COUNT: int = int(os.getenv("LOAD_BALANCE_CANDIDATE_COUNT", "3"))
    
    # 是否启用CK验证
    ENABLE_CK_VALIDATION: bool = os.getenv("ENABLE_CK_VALIDATION", "true").lower() == "true"
    
    # CK验证缓存时间（秒）
    CK_VALIDATION_CACHE_TTL: int = int(os.getenv("CK_VALIDATION_CACHE_TTL", "300"))
    
    @classmethod
    def get_config_summary(cls) -> dict:
        """获取配置摘要"""
        return {
            "use_simplified_ck_service": cls.USE_SIMPLIFIED_CK_SERVICE,
            "enable_redis_ck_optimization": cls.ENABLE_REDIS_CK_OPTIMIZATION,
            "ck_selection_timeout": cls.CK_SELECTION_TIMEOUT,
            "load_balance_candidate_count": cls.LOAD_BALANCE_CANDIDATE_COUNT,
            "enable_ck_validation": cls.ENABLE_CK_VALIDATION,
            "ck_validation_cache_ttl": cls.CK_VALIDATION_CACHE_TTL,
        }
    
    @classmethod
    def is_simplified_service_enabled(cls) -> bool:
        """检查是否启用简化CK服务"""
        return cls.USE_SIMPLIFIED_CK_SERVICE
    
    @classmethod
    def is_redis_optimization_enabled(cls) -> bool:
        """检查是否启用Redis优化"""
        return cls.ENABLE_REDIS_CK_OPTIMIZATION


# 全局配置实例
ck_config = CKServiceConfig()
