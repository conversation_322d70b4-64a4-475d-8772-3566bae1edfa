#!/usr/bin/env python3
"""
Telegram机器人自动修复工具
自动检测和解决冲突问题，无需手动干预
"""

import asyncio
import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from app.core.logging import get_logger

logger = get_logger(__name__)


async def auto_fix_telegram_conflicts():
    """自动修复Telegram机器人冲突"""
    import platform

    logger.info("🤖 Telegram机器人自动修复工具")
    logger.info(f"平台: {platform.system()} {platform.architecture()[0]}")
    logger.info("=" * 50)

    success_count = 0
    total_steps = 4
    
    # 步骤1: 检查和清理单例锁
    logger.info("步骤 1/4: 检查单例锁状态...")
    try:
        from app.telegram_bot.utils.simple_singleton import force_cleanup_bot_singleton, get_bot_singleton_info
        
        singleton_info = get_bot_singleton_info()
        if singleton_info:
            logger.info(f"发现单例锁: PID={singleton_info['pid']}")
            if force_cleanup_bot_singleton():
                logger.info("✅ 单例锁清理成功")
                success_count += 1
            else:
                logger.warning("⚠️  单例锁清理失败")
        else:
            logger.info("✅ 没有发现单例锁")
            success_count += 1
            
    except Exception as e:
        logger.error(f"❌ 单例锁检查失败: {e}")
    
    # 步骤2: 自动清理webhook
    logger.info("步骤 2/4: 自动清理webhook...")
    try:
        from app.telegram_bot.config import get_bot_config
        from telegram.ext import Application
        
        config = get_bot_config()
        if config.bot_token:
            application = Application.builder().token(config.bot_token).build()
            await application.initialize()
            
            try:
                await application.bot.delete_webhook(drop_pending_updates=True)
                await asyncio.sleep(1)
                
                webhook_info = await application.bot.get_webhook_info()
                if not webhook_info.url:
                    logger.info("✅ Webhook清理成功")
                    success_count += 1
                else:
                    logger.warning(f"⚠️  Webhook仍存在: {webhook_info.url}")
                    
            finally:
                await application.shutdown()
        else:
            logger.warning("⚠️  Bot token未配置，跳过webhook清理")
            
    except Exception as e:
        logger.error(f"❌ Webhook清理失败: {e}")
    
    # 步骤3: 测试API连接
    logger.info("步骤 3/4: 测试API连接...")
    try:
        from app.telegram_bot.config import get_bot_config
        from telegram.ext import Application
        
        config = get_bot_config()
        if config.bot_token:
            application = Application.builder().token(config.bot_token).build()
            await application.initialize()
            
            try:
                bot_info = await application.bot.get_me()
                logger.info(f"✅ API连接正常: @{bot_info.username}")
                success_count += 1
                
            finally:
                await application.shutdown()
        else:
            logger.warning("⚠️  Bot token未配置，跳过API测试")
            
    except Exception as e:
        logger.error(f"❌ API连接测试失败: {e}")
    
    # 步骤4: 智能启动建议
    logger.info("步骤 4/4: 生成启动建议...")
    try:
        from app.telegram_bot.config import get_bot_config
        config = get_bot_config()
        
        if config.webhook_url:
            logger.info("💡 建议使用Webhook模式:")
            logger.info(f"   - Webhook URL: {config.webhook_url}")
            logger.info("   - 优势: 无冲突、高效、适合生产环境")
        else:
            logger.info("💡 建议配置Webhook模式:")
            logger.info("   - 避免轮询冲突问题")
            logger.info("   - 更适合多环境部署")
            logger.info("   - 配置方法: 在Web界面设置Webhook URL")
        
        success_count += 1
        
    except Exception as e:
        logger.error(f"❌ 生成建议失败: {e}")
    
    # 总结
    logger.info("=" * 50)
    logger.info("🎯 自动修复结果")
    logger.info("=" * 50)
    
    if success_count == total_steps:
        logger.info("🎉 自动修复完成！所有步骤都成功")
        logger.info("💡 现在可以重新启动机器人服务")
        return True
    elif success_count >= total_steps // 2:
        logger.info(f"✅ 部分修复成功 ({success_count}/{total_steps})")
        logger.info("💡 建议重新启动机器人服务试试")
        return True
    else:
        logger.warning(f"⚠️  修复效果有限 ({success_count}/{total_steps})")
        logger.info("💡 建议:")
        logger.info("1. 检查bot token配置")
        logger.info("2. 确保网络连接正常")
        logger.info("3. 考虑使用不同的bot token")
        return False


async def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description="Telegram机器人自动修复工具")
    parser.add_argument("--verbose", "-v", action="store_true", help="详细输出")
    
    args = parser.parse_args()
    
    if args.verbose:
        import logging
        logging.getLogger().setLevel(logging.DEBUG)
    
    logger.info("🚀 启动自动修复...")
    
    try:
        success = await auto_fix_telegram_conflicts()
        
        if success:
            logger.info("✅ 自动修复完成")
            logger.info("🔄 建议现在重新启动应用")
            sys.exit(0)
        else:
            logger.warning("⚠️  自动修复部分成功")
            logger.info("📞 如需帮助，请查看日志或联系技术支持")
            sys.exit(1)
            
    except KeyboardInterrupt:
        logger.info("用户中断操作")
        sys.exit(1)
    except Exception as e:
        logger.error(f"自动修复失败: {e}")
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())
