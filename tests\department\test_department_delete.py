#!/usr/bin/env python3
"""
部门删除功能测试
测试删除部门时的各种约束条件
"""

import requests
from typing import Dict, Any

# 测试配置
BASE_URL = "http://localhost:20000"
API_BASE = f"{BASE_URL}/api/v1"


class TestDepartmentDelete:
    """部门删除测试类"""

    def setup_method(self):
        """测试前准备"""
        self.super_admin_token = None
        self.merchant_admin_token = None
        self.test_merchant_id = None
        self.created_department_ids = []
        self.created_user_ids = []

    def teardown_method(self):
        """测试后清理"""
        # 清理创建的用户
        if self.super_admin_token:
            for user_id in self.created_user_ids:
                try:
                    requests.delete(
                        f"{API_BASE}/users/{user_id}",
                        headers={"Authorization": f"Bearer {self.super_admin_token}"}
                    )
                except:
                    pass

        # 清理创建的部门
        if self.super_admin_token:
            for dept_id in self.created_department_ids:
                try:
                    requests.delete(
                        f"{API_BASE}/departments/{dept_id}",
                        headers={"Authorization": f"Bearer {self.super_admin_token}"}
                    )
                except:
                    pass

    def login_user(self, username: str, password: str) -> str:
        """用户登录获取token"""
        form_data = {
            "username": username,
            "password": password
        }
        response = requests.post(f"{API_BASE}/auth/login", data=form_data)
        print(f"登录响应: {response.status_code}, {response.text}")
        assert response.status_code == 200, f"登录失败: {response.text}"
        return response.json()["data"]["access_token"]

    def get_user_info(self, token: str) -> Dict[str, Any]:
        """获取用户信息"""
        response = requests.get(
            f"{API_BASE}/users/me",
            headers={"Authorization": f"Bearer {token}"}
        )
        assert response.status_code == 200, f"获取用户信息失败: {response.text}"
        return response.json()["data"]

    def create_test_department(self, token: str, merchant_id: int, name: str, code: str) -> Dict[str, Any]:
        """创建测试部门"""
        dept_data = {
            "merchant_id": merchant_id,
            "name": name,
            "code": code,
            "description": f"测试部门 - {name}"
        }

        response = requests.post(
            f"{API_BASE}/departments",
            headers={"Authorization": f"Bearer {token}"},
            json=dept_data
        )

        assert response.status_code == 200, f"创建部门失败: {response.text}"
        dept = response.json()["data"]
        self.created_department_ids.append(dept["id"])
        return dept

    def create_test_user(self, token: str, merchant_id: int, department_id: int, username: str) -> Dict[str, Any]:
        """创建测试用户"""
        user_data = {
            "username": username,
            "email": f"{username}@test.com",
            "password": "test123456",
            "full_name": f"测试用户-{username}",
            "merchant_id": merchant_id,
            "department_id": department_id,
            "is_active": True
        }

        response = requests.post(
            f"{API_BASE}/users",
            headers={"Authorization": f"Bearer {token}"},
            json=user_data
        )

        assert response.status_code == 200, f"创建用户失败: {response.text}"
        user = response.json()["data"]
        self.created_user_ids.append(user["id"])
        return user

    def test_delete_department_success(self):
        """测试成功删除部门"""
        # 1. 超级管理员登录
        self.super_admin_token = self.login_user("admin", "7c222fb2927d828af22f592134e8932480637c0d")

        # 2. 获取测试商户ID
        merchants_response = requests.get(
            f"{API_BASE}/merchants",
            headers={"Authorization": f"Bearer {self.super_admin_token}"}
        )
        assert merchants_response.status_code == 200
        merchants = merchants_response.json()["data"]["items"]
        assert len(merchants) > 0, "没有可用的测试商户"
        self.test_merchant_id = merchants[0]["id"]

        # 3. 创建测试部门
        test_dept = self.create_test_department(
            self.super_admin_token,
            self.test_merchant_id,
            "测试删除部门",
            "TEST_DELETE_SUCCESS"
        )

        # 4. 删除部门
        response = requests.delete(
            f"{API_BASE}/departments/{test_dept['id']}",
            headers={"Authorization": f"Bearer {self.super_admin_token}"}
        )

        print(f"删除部门响应: {response.status_code}, {response.text}")
        assert response.status_code == 200
        response_data = response.json()
        assert response_data["data"]["message"] == "删除部门成功"

        # 5. 验证部门已被删除
        get_response = requests.get(
            f"{API_BASE}/departments/{test_dept['id']}",
            headers={"Authorization": f"Bearer {self.super_admin_token}"}
        )
        assert get_response.status_code == 404

    def test_delete_department_with_children_fails(self):
        """测试删除有子部门的部门失败"""
        # 1. 超级管理员登录
        if not self.super_admin_token:
            self.super_admin_token = self.login_user("admin", "7c222fb2927d828af22f592134e8932480637c0d")

        # 2. 获取测试商户ID
        if not self.test_merchant_id:
            merchants_response = requests.get(
                f"{API_BASE}/merchants",
                headers={"Authorization": f"Bearer {self.super_admin_token}"}
            )
            assert merchants_response.status_code == 200
            merchants = merchants_response.json()["data"]["items"]
            self.test_merchant_id = merchants[0]["id"]

        # 3. 创建父部门
        parent_dept = self.create_test_department(
            self.super_admin_token,
            self.test_merchant_id,
            "父部门",
            "PARENT_DEPT"
        )

        # 4. 创建子部门
        child_dept_data = {
            "merchant_id": self.test_merchant_id,
            "parent_id": parent_dept["id"],
            "name": "子部门",
            "code": "CHILD_DEPT",
            "description": "子部门"
        }

        child_response = requests.post(
            f"{API_BASE}/departments",
            headers={"Authorization": f"Bearer {self.super_admin_token}"},
            json=child_dept_data
        )
        assert child_response.status_code == 200
        child_dept = child_response.json()["data"]
        self.created_department_ids.append(child_dept["id"])

        # 5. 尝试删除有子部门的父部门
        response = requests.delete(
            f"{API_BASE}/departments/{parent_dept['id']}",
            headers={"Authorization": f"Bearer {self.super_admin_token}"}
        )

        print(f"删除有子部门的部门响应: {response.status_code}, {response.text}")
        assert response.status_code == 500
        assert "存在子部门，无法删除" in response.json()["detail"]

    def test_delete_department_with_users_fails(self):
        """测试删除有关联用户的部门失败"""
        # 1. 超级管理员登录
        if not self.super_admin_token:
            self.super_admin_token = self.login_user("admin", "7c222fb2927d828af22f592134e8932480637c0d")

        # 2. 获取测试商户ID
        if not self.test_merchant_id:
            merchants_response = requests.get(
                f"{API_BASE}/merchants",
                headers={"Authorization": f"Bearer {self.super_admin_token}"}
            )
            assert merchants_response.status_code == 200
            merchants = merchants_response.json()["data"]["items"]
            self.test_merchant_id = merchants[0]["id"]

        # 3. 创建测试部门
        test_dept = self.create_test_department(
            self.super_admin_token,
            self.test_merchant_id,
            "有用户的部门",
            "DEPT_WITH_USERS"
        )

        # 4. 创建测试用户
        test_user = self.create_test_user(
            self.super_admin_token,
            self.test_merchant_id,
            test_dept["id"],
            "test_delete_user"
        )

        # 5. 尝试删除有用户的部门
        response = requests.delete(
            f"{API_BASE}/departments/{test_dept['id']}",
            headers={"Authorization": f"Bearer {self.super_admin_token}"}
        )

        print(f"删除有用户的部门响应: {response.status_code}, {response.text}")
        assert response.status_code == 500
        assert "存在关联用户，无法删除" in response.json()["detail"]

    def test_delete_root_department_fails(self):
        """测试删除根部门失败"""
        # 1. 超级管理员登录
        if not self.super_admin_token:
            self.super_admin_token = self.login_user("admin", "7c222fb2927d828af22f592134e8932480637c0d")

        # 2. 获取测试商户ID
        if not self.test_merchant_id:
            merchants_response = requests.get(
                f"{API_BASE}/merchants",
                headers={"Authorization": f"Bearer {self.super_admin_token}"}
            )
            assert merchants_response.status_code == 200
            merchants = merchants_response.json()["data"]["items"]
            self.test_merchant_id = merchants[0]["id"]

        # 3. 获取根部门
        departments_response = requests.get(
            f"{API_BASE}/departments",
            headers={"Authorization": f"Bearer {self.super_admin_token}"},
            params={"merchant_id": self.test_merchant_id}
        )
        assert departments_response.status_code == 200
        departments = departments_response.json()["data"]["items"]

        root_dept = None
        for dept in departments:
            if dept.get("code") == "ROOT":
                root_dept = dept
                break

        assert root_dept is not None, "找不到根部门"

        # 4. 尝试删除根部门
        response = requests.delete(
            f"{API_BASE}/departments/{root_dept['id']}",
            headers={"Authorization": f"Bearer {self.super_admin_token}"}
        )

        print(f"删除根部门响应: {response.status_code}, {response.text}")
        assert response.status_code == 500
        assert "根部门无法删除" in response.json()["detail"]

    def test_delete_nonexistent_department_fails(self):
        """测试删除不存在的部门失败"""
        # 1. 超级管理员登录
        if not self.super_admin_token:
            self.super_admin_token = self.login_user("admin", "7c222fb2927d828af22f592134e8932480637c0d")

        # 2. 尝试删除不存在的部门
        response = requests.delete(
            f"{API_BASE}/departments/99999",
            headers={"Authorization": f"Bearer {self.super_admin_token}"}
        )

        print(f"删除不存在部门响应: {response.status_code}, {response.text}")
        assert response.status_code == 404
        assert "部门不存在或无权限访问" in response.json()["detail"]


if __name__ == "__main__":
    # 运行测试
    test_instance = TestDepartmentDelete()

    try:
        test_instance.setup_method()

        print("=== 测试成功删除部门 ===")
        test_instance.test_delete_department_success()

        print("\n=== 测试删除有子部门的部门失败 ===")
        test_instance.test_delete_department_with_children_fails()

        print("\n=== 测试删除有用户的部门失败 ===")
        test_instance.test_delete_department_with_users_fails()

        print("\n=== 测试删除根部门失败 ===")
        test_instance.test_delete_root_department_fails()

        print("\n=== 测试删除不存在的部门失败 ===")
        test_instance.test_delete_nonexistent_department_fails()

        print("\n✅ 所有删除部门测试通过！")

    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        raise
    finally:
        test_instance.teardown_method()
