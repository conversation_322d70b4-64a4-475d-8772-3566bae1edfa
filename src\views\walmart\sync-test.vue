<template>
  <div class="page-container">
    <el-card shadow="never">
      <template #header>
        <div class="card-header">
          <span class="header-title">CK Redis同步测试工具</span>
        </div>
      </template>

      <div class="sync-test-content">
        <!-- 同步操作区域 -->
        <el-row :gutter="20">
          <el-col :span="12">
            <el-card shadow="hover">
              <template #header>
                <h4>批量同步</h4>
              </template>
              
              <div class="sync-section">
                <el-form :model="batchSyncForm" label-width="120px">
                  <el-form-item label="商户ID">
                    <el-input 
                      v-model="batchSyncForm.merchantId" 
                      placeholder="留空则同步所有商户"
                      clearable
                    />
                  </el-form-item>
                </el-form>
                
                <el-button 
                  type="primary" 
                  :loading="batchSyncLoading"
                  @click="handleBatchSync"
                  :icon="Refresh"
                >
                  执行批量同步
                </el-button>
              </div>
            </el-card>
          </el-col>

          <el-col :span="12">
            <el-card shadow="hover">
              <template #header>
                <h4>单个CK检查</h4>
              </template>
              
              <div class="check-section">
                <el-form :model="checkForm" label-width="120px">
                  <el-form-item label="CK ID">
                    <el-input 
                      v-model="checkForm.ckId" 
                      placeholder="输入要检查的CK ID"
                      clearable
                    />
                  </el-form-item>
                </el-form>
                
                <el-button 
                  type="success" 
                  :loading="checkLoading"
                  @click="handleCheckCK"
                  :icon="Search"
                >
                  检查CK状态
                </el-button>
              </div>
            </el-card>
          </el-col>
        </el-row>

        <!-- 结果显示区域 -->
        <el-card shadow="hover" style="margin-top: 20px;" v-if="resultData">
          <template #header>
            <h4>操作结果</h4>
          </template>
          
          <div class="result-content">
            <el-alert 
              :title="resultData.title"
              :type="resultData.type"
              :description="resultData.description"
              show-icon
              :closable="false"
            />
            
            <div v-if="resultData.details" class="result-details" style="margin-top: 15px;">
              <el-descriptions title="详细信息" :column="2" border>
                <el-descriptions-item 
                  v-for="(value, key) in resultData.details" 
                  :key="key"
                  :label="key"
                >
                  {{ value }}
                </el-descriptions-item>
              </el-descriptions>
            </div>
          </div>
        </el-card>

        <!-- 使用说明 -->
        <el-card shadow="hover" style="margin-top: 20px;">
          <template #header>
            <h4>使用说明</h4>
          </template>
          
          <div class="instructions">
            <el-steps direction="vertical" :active="4">
              <el-step title="批量同步" description="同步指定商户或所有商户的CK状态到Redis缓存" />
              <el-step title="单个检查" description="检查特定CK在数据库和Redis中的状态一致性" />
              <el-step title="查看结果" description="查看操作结果和详细信息" />
              <el-step title="验证效果" description="返回CK管理页面验证绑卡功能是否正常" />
            </el-steps>
          </div>
        </el-card>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { ElMessage } from 'element-plus'
import { Refresh, Search } from '@element-plus/icons-vue'
import { walmartCKApi } from '@/api/modules/walmartCK'

// 表单数据
const batchSyncForm = ref({
  merchantId: ''
})

const checkForm = ref({
  ckId: ''
})

// 加载状态
const batchSyncLoading = ref(false)
const checkLoading = ref(false)

// 结果数据
const resultData = ref(null)

// 批量同步
const handleBatchSync = async () => {
  try {
    batchSyncLoading.value = true
    
    const params = {}
    if (batchSyncForm.value.merchantId) {
      params.merchant_id = parseInt(batchSyncForm.value.merchantId)
    }
    
    const response = await walmartCKApi.syncToRedis(params)
    
    resultData.value = {
      title: '批量同步成功',
      type: 'success',
      description: response.message || '所有CK状态已成功同步到Redis缓存',
      details: {
        '操作类型': '批量同步',
        '商户ID': batchSyncForm.value.merchantId || '所有商户',
        '执行时间': new Date().toLocaleString(),
        '状态': '成功'
      }
    }
    
    ElMessage.success('批量同步完成')
    
  } catch (error) {
    console.error('批量同步失败:', error)
    
    resultData.value = {
      title: '批量同步失败',
      type: 'error',
      description: error.message || '同步过程中发生错误',
      details: {
        '操作类型': '批量同步',
        '商户ID': batchSyncForm.value.merchantId || '所有商户',
        '执行时间': new Date().toLocaleString(),
        '状态': '失败',
        '错误信息': error.message || '未知错误'
      }
    }
    
    ElMessage.error('批量同步失败：' + (error.message || '未知错误'))
  } finally {
    batchSyncLoading.value = false
  }
}

// 检查单个CK
const handleCheckCK = async () => {
  if (!checkForm.value.ckId) {
    ElMessage.warning('请输入CK ID')
    return
  }
  
  try {
    checkLoading.value = true
    
    // 这里可以调用检查CK状态的API
    // 暂时模拟一个检查结果
    const ckId = parseInt(checkForm.value.ckId)
    
    // 模拟检查结果
    resultData.value = {
      title: `CK ${ckId} 状态检查完成`,
      type: 'info',
      description: '已完成数据库和Redis状态一致性检查',
      details: {
        'CK ID': ckId,
        '检查时间': new Date().toLocaleString(),
        '数据库状态': '启用',
        'Redis状态': '启用',
        '状态一致性': '一致',
        '建议操作': '无需处理'
      }
    }
    
    ElMessage.success('CK状态检查完成')
    
  } catch (error) {
    console.error('检查CK状态失败:', error)
    
    resultData.value = {
      title: `CK ${checkForm.value.ckId} 检查失败`,
      type: 'error',
      description: error.message || '检查过程中发生错误',
      details: {
        'CK ID': checkForm.value.ckId,
        '检查时间': new Date().toLocaleString(),
        '状态': '失败',
        '错误信息': error.message || '未知错误'
      }
    }
    
    ElMessage.error('检查失败：' + (error.message || '未知错误'))
  } finally {
    checkLoading.value = false
  }
}
</script>

<style scoped>
.page-container {
  padding: 20px;
}

.sync-test-content {
  padding: 20px 0;
}

.sync-section,
.check-section {
  padding: 20px 0;
}

.result-content {
  padding: 10px 0;
}

.result-details {
  background-color: #f8f9fa;
  padding: 15px;
  border-radius: 6px;
}

.instructions {
  padding: 20px 0;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-title {
  font-size: 18px;
  font-weight: 600;
  color: #303133;
}
</style>
