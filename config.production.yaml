# 沃尔玛绑卡网关生产环境配置文件

# 服务器配置
server:
  port: 21000
  host: "0.0.0.0"
  mode: "release" # 生产模式
  read_timeout: "30s"
  write_timeout: "30s"
  idle_timeout: "60s"
  max_header_bytes: 1048576

# 并发配置 - 生产优化
concurrency:
  max_goroutines: 2000
  worker_pool_size: 10
  queue_buffer_size: 200
  batch_size: 20
  batch_timeout: "2s"
  graceful_shutdown_timeout: "30s"

# 数据库配置
database:
  type: "mysql"
  host: "**************"
  port: 3306
  user: "root"
  password: "7c222fb2927d828af22f592134e8932480637c0d"
  db_name: "walmart_card_db"
  charset: "utf8mb4"
  max_open_conns: 10
  max_idle_conns: 5
  conn_max_lifetime: "5m"
  conn_max_idle_time: "1m"
  query_timeout: "30s"
  exec_timeout: "30s"

# Redis配置
redis:
  host: "**************"
  port: 6379
  password: "7c222fb2927d828af22f592134e8932480637c0d"
  db: 0
  pool_size: 10
  min_idle_conns: 5
  dial_timeout: "5s"
  read_timeout: "3s"
  write_timeout: "3s"
  pool_timeout: "4s"
  idle_timeout: "5m"
  idle_check_frequency: "1m"

# RabbitMQ配置
rabbitmq:
  host: "**************"
  port: 5672
  user: "walmart_card"
  password: "7c222fb2927d828af22f592134e8932480637c0d"
  vhost: "/walmart_card"
  exchange: "walmart_card_exchange"
  queue: "card_bind_queue"
  routing_key: "card.bind"
  durable: true
  auto_delete: false
  exclusive: false
  no_wait: false

# 日志配置 - 生产环境优化（防止占满硬盘）
logging:
  level: "info"                    # 生产环境使用info级别
  format: "json"                   # JSON格式便于日志分析
  output: "/app/logs/walmart-gateway.log"  # 输出到文件
  max_size: 100                    # 单个日志文件最大100MB
  max_backups: 10                  # 保留10个备份文件
  max_age: 7                       # 日志文件保留7天
  compress: true                   # 压缩旧日志文件
  fields:
    service: "walmart-gateway"
    version: "v2.0.0"
    environment: "production"

# API配置
api:
  project_name: "沃尔玛绑卡网关"
  version: "v2.0.0"
  prefix: "/api/v1"
  jwt:
    secret_key: "01aTO25Fwsaw18700DEc70FH127ZHw001SiZV32AbBgO3n94i0G0xFm3vK24717b"
    expire_hours: 24
    algorithm: "HS256"
  rate_limit:
    enabled: true
    requests_per_minute: 2000
    burst_size: 200
    use_redis: true

# 性能配置
performance:
  http_client:
    timeout: "30s"
    dial_timeout: "10s"
    keep_alive: "30s"
    max_idle_conns: 100
    max_idle_conns_per_host: 10
    max_conns_per_host: 100
  memory:
    gc_percent: 100
    max_heap_size: "2GB"
  cache:
    default_ttl: "1h"
    user_ttl: "30m"
    merchant_ttl: "24h"
    max_size: 50000

# 监控配置
monitoring:
  enabled: true
  port: 9090
  path: "/metrics"
  health_check_path: "/health"

# 安全配置
security:
  cors:
    enabled: true
    allowed_origins: ["*"]
    allowed_methods: ["GET", "POST", "PUT", "DELETE", "OPTIONS"]
    allowed_headers: ["*"]
    allow_credentials: true
  rate_limiting:
    enabled: true
    requests_per_second: 100
    burst_size: 200
  request_timeout: "30s"

# 外部服务配置
external_services:
  walmart_api:
    base_url: "https://api.walmart.com"
    timeout: "30s"
    retry_count: 3
    retry_delay: "1s"
  card_provider:
    base_url: "https://card-provider.walmart.com"
    timeout: "10s"
    retry_count: 2
    retry_delay: "500ms"
