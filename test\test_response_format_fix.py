#!/usr/bin/env python3
"""
响应格式修正验证脚本
验证修正后的响应解析逻辑是否正确
"""

import sys
import os
import asyncio
from unittest.mock import Mock, patch, AsyncMock

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app.services.ck_validation_service import CKValidationService
from app.models.walmart_ck import WalmartCK
from app.db.session import SessionLocal


class ResponseFormatFixValidator:
    """响应格式修正验证器"""

    def __init__(self):
        self.test_cases = []
        self.results = []

    def create_test_ck(self) -> WalmartCK:
        """创建测试CK对象"""
        return WalmartCK(
            id=1,
            sign="test_sign_123",
            total_limit=20,
            bind_count=5,
            active=True,
            merchant_id=1,
            department_id=1,
            is_deleted=False
        )

    def define_test_cases(self):
        """定义测试用例"""
        self.test_cases = [
            {
                "name": "成功响应_正常errorcode",
                "description": "status=true且errorcode=1的正常成功响应",
                "response": {
                    "logId": "SUCCESS_001",
                    "status": True,
                    "error": {
                        "errorcode": 1,
                        "message": None,
                        "redirect": None,
                        "validators": None
                    },
                    "data": {
                        "cardCount": 4,
                        "nickName": "微信用户",
                        "headImg": "https://example.com/avatar.jpg",
                        "upcardOrderUrl": "https://example.com/order"
                    }
                },
                "expected": {
                    "is_valid": True,
                    "should_disable": False,
                    "log_id": "SUCCESS_001",
                    "card_count": 4,
                    "nick_name": "微信用户"
                }
            },
            {
                "name": "失败响应_status_false",
                "description": "status=false的失败响应",
                "response": {
                    "logId": "FAIL_001",
                    "status": False,
                    "error": {
                        "errorcode": 401,
                        "message": "用户认证失败",
                        "redirect": None,
                        "validators": None
                    },
                    "data": None
                },
                "expected": {
                    "is_valid": False,
                    "should_disable": True,
                    "error_code": "401",
                    "error_message": "用户认证失败",
                    "log_id": "FAIL_001"
                }
            },
            {
                "name": "严重错误_status_true_但_errorcode_严重",
                "description": "status=true但errorcode=-1的严重错误",
                "response": {
                    "logId": "CRITICAL_001",
                    "status": True,
                    "error": {
                        "errorcode": -1,
                        "message": "系统内部错误",
                        "redirect": None,
                        "validators": None
                    },
                    "data": None
                },
                "expected": {
                    "is_valid": False,
                    "should_disable": True,
                    "error_code": "-1",
                    "error_message": "系统内部错误",
                    "log_id": "CRITICAL_001"
                }
            },
            {
                "name": "成功响应_errorcode_0",
                "description": "status=true且errorcode=0的成功响应",
                "response": {
                    "logId": "SUCCESS_002",
                    "status": True,
                    "error": {
                        "errorcode": 0,
                        "message": None,
                        "redirect": None,
                        "validators": None
                    },
                    "data": {
                        "cardCount": 2,
                        "nickName": "测试用户",
                        "headImg": "https://example.com/test.jpg",
                        "upcardOrderUrl": "https://example.com/test"
                    }
                },
                "expected": {
                    "is_valid": True,
                    "should_disable": False,
                    "log_id": "SUCCESS_002",
                    "card_count": 2,
                    "nick_name": "测试用户"
                }
            },
            {
                "name": "权限错误_403",
                "description": "status=false且errorcode=403的权限错误",
                "response": {
                    "logId": "PERM_001",
                    "status": False,
                    "error": {
                        "errorcode": 403,
                        "message": "权限被拒绝",
                        "redirect": None,
                        "validators": None
                    },
                    "data": None
                },
                "expected": {
                    "is_valid": False,
                    "should_disable": True,
                    "error_code": "403",
                    "error_message": "权限被拒绝",
                    "log_id": "PERM_001"
                }
            },
            {
                "name": "用户不存在_404",
                "description": "status=true但errorcode=404的用户不存在错误",
                "response": {
                    "logId": "NOTFOUND_001",
                    "status": True,
                    "error": {
                        "errorcode": 404,
                        "message": "用户不存在",
                        "redirect": None,
                        "validators": None
                    },
                    "data": None
                },
                "expected": {
                    "is_valid": False,
                    "should_disable": True,
                    "error_code": "404",
                    "error_message": "用户不存在",
                    "log_id": "NOTFOUND_001"
                }
            }
        ]

    async def run_test_case(self, test_case: dict) -> dict:
        """运行单个测试用例"""
        try:
            db = SessionLocal()
            validation_service = CKValidationService(db)
            test_ck = self.create_test_ck()
            
            # 创建mock响应
            mock_response = Mock()
            mock_response.status_code = 200
            mock_response.json.return_value = test_case["response"]
            
            # 测试解析逻辑
            result = validation_service._parse_validation_response(mock_response, test_ck.id)
            
            # 验证结果
            expected = test_case["expected"]
            passed = True
            errors = []
            
            for key, expected_value in expected.items():
                if key not in result:
                    errors.append(f"缺少字段: {key}")
                    passed = False
                elif result[key] != expected_value:
                    errors.append(f"字段 {key} 不匹配: 期望 {expected_value}, 实际 {result[key]}")
                    passed = False
            
            db.close()
            
            return {
                "name": test_case["name"],
                "description": test_case["description"],
                "passed": passed,
                "errors": errors,
                "result": result,
                "expected": expected
            }
            
        except Exception as e:
            return {
                "name": test_case["name"],
                "description": test_case["description"],
                "passed": False,
                "errors": [f"测试异常: {str(e)}"],
                "result": None,
                "expected": test_case["expected"]
            }

    async def run_all_tests(self):
        """运行所有测试用例"""
        print("开始验证响应格式修正...")
        print("=" * 60)
        
        self.define_test_cases()
        
        for i, test_case in enumerate(self.test_cases):
            print(f"\n测试 {i+1}/{len(self.test_cases)}: {test_case['name']}")
            print(f"描述: {test_case['description']}")
            
            result = await self.run_test_case(test_case)
            self.results.append(result)
            
            if result["passed"]:
                print("✅ 通过")
            else:
                print("❌ 失败")
                for error in result["errors"]:
                    print(f"   - {error}")

    def generate_report(self):
        """生成测试报告"""
        total_tests = len(self.results)
        passed_tests = sum(1 for r in self.results if r["passed"])
        failed_tests = total_tests - passed_tests
        
        print("\n" + "=" * 60)
        print("测试报告")
        print("=" * 60)
        print(f"总测试数: {total_tests}")
        print(f"通过测试: {passed_tests}")
        print(f"失败测试: {failed_tests}")
        print(f"成功率: {passed_tests/total_tests*100:.1f}%" if total_tests > 0 else "0%")
        
        if failed_tests > 0:
            print("\n失败的测试:")
            for result in self.results:
                if not result["passed"]:
                    print(f"- {result['name']}: {', '.join(result['errors'])}")
        
        print("\n详细结果:")
        for result in self.results:
            status = "✅" if result["passed"] else "❌"
            print(f"{status} {result['name']}")
        
        return passed_tests == total_tests

    def test_error_code_detection(self):
        """测试错误码检测逻辑"""
        print("\n" + "=" * 60)
        print("测试错误码检测逻辑")
        print("=" * 60)
        
        db = SessionLocal()
        validation_service = CKValidationService(db)
        
        # 测试严重错误码检测
        critical_codes = [-1, -999, 401, 403, 404, 500, "INVALID_SIGN", "USER_NOT_FOUND"]
        normal_codes = [0, 1, 200, "NORMAL_STATUS", None]
        
        print("严重错误码测试:")
        for code in critical_codes:
            is_critical = validation_service._is_critical_error_code(code)
            status = "✅" if is_critical else "❌"
            print(f"{status} {code}: {is_critical}")
        
        print("\n正常错误码测试:")
        for code in normal_codes:
            is_critical = validation_service._is_critical_error_code(code)
            status = "✅" if not is_critical else "❌"
            print(f"{status} {code}: {not is_critical}")
        
        # 测试CK禁用判断
        print("\nCK禁用判断测试:")
        test_cases = [
            (401, "未授权", True),
            (-1, "系统错误", True),
            (1, None, False),
            (1, "用户不存在", True),
            (0, "成功", False),
        ]
        
        for error_code, error_message, should_disable in test_cases:
            result = validation_service._should_disable_ck(error_code, error_message)
            status = "✅" if result == should_disable else "❌"
            print(f"{status} ({error_code}, '{error_message}'): {result} (期望: {should_disable})")
        
        db.close()


async def main():
    """主函数"""
    validator = ResponseFormatFixValidator()
    
    # 运行响应格式测试
    await validator.run_all_tests()
    
    # 生成报告
    success = validator.generate_report()
    
    # 测试错误码检测逻辑
    validator.test_error_code_detection()
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 所有测试通过！响应格式修正验证成功。")
    else:
        print("⚠️  部分测试失败，请检查修正逻辑。")
    print("=" * 60)
    
    return 0 if success else 1


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
