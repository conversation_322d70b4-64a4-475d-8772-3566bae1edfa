<script setup>
import { ref, onMounted, computed, watch } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Search, Plus, Edit, Delete, Refresh, DataAnalysis, SuccessFilled, TrendCharts, Money, CircleCheck, Warning, CircleClose, ArrowUp, ArrowDown, Download } from '@element-plus/icons-vue'
import { usewalmartCKStore } from '@/store/modules/walmartCK'
import useMerchantSwitch from '@/composables/useMerchantSwitch'
import useMerchantFilter from '@/composables/useMerchantFilter'
// walmartCKApi 已移除 - Redis同步功能不再需要
import { departmentApi } from '@/api/modules/department'
import { ckMonitorApi } from '@/api/modules/ckMonitor'
import { walmartCKApi } from '@/api/modules/walmartCK'
import { usePermission } from '@/composables/usePermission'

const userConfigStore = usewalmartCKStore()
const router = useRouter()
const route = useRoute()
const loading = ref(false)
const search = ref('')

const pagination = ref({
  currentPage: 1,
  pageSize: 10
})

// 统计相关数据
const statistics = ref({
  summary: {
    total_cks: 0,
    total_records: 0,
    total_success: 0,
    success_rate: 0,
    total_request_amount: 0,
    total_actual_amount: 0,
    amount_difference: 0
  },
  ck_details: [],
  filters: {}
})

const statisticsLoading = ref(false)
const departmentOptions = ref([])
// syncLoading 已移除 - Redis同步功能不再需要

// CK监控相关数据
const monitorData = ref({
  status: null,
  health: null,
  alerts: [],
  summary: {}
})
const monitorLoading = ref(false)
const testLoading = ref(false)
const showMonitorPanel = ref(false)

// 权限检查
const { hasPermission } = usePermission()
const canViewMonitor = computed(() => hasPermission('api:ck-monitor:read'))
const canTestLoadBalance = computed(() => hasPermission('api:ck-monitor:test'))
const canSyncRedis = computed(() => hasPermission('api:ck-monitor:admin'))

// 统计筛选条件
const statisticsFilters = ref({
  departmentId: null,
  dateRange: null
})

// 批量操作相关状态
const selectedCKs = ref([])
const batchDeleteLoading = ref(false)
const batchEnableLoading = ref(false)
const batchDisableLoading = ref(false)

// 导出相关状态
const exportDialogVisible = ref(false)
const exportLoading = ref(false)
const exportForm = ref({
  format: 'csv',
  includeFilters: true
})


// 修复：使用本地分页数据而不是store中的分页数据
const paginationTotal = computed(() => pagination.value.total || 0)

const fetchUserConfigs = async (params = {}) => {
  loading.value = true
  try {
    const queryParams = {
      page: pagination.value.currentPage,
      page_size: pagination.value.pageSize,
      search: search.value || undefined,
      ...params
    }
    const response = await userConfigStore.fetchConfigList(queryParams);
    // 确保分页数据同步
    pagination.value.total = response.total || 0

    // 从集成接口获取统计数据
    if (response.statistics) {
      statistics.value = response.statistics
    }

  } catch (error) {
    console.error('获取绑卡用户配置列表失败', error)
    ElMessage.error('获取绑卡用户配置列表失败')
    // 错误时重置分页数据
    pagination.value.total = 0
  } finally {
    loading.value = false
  }
}

// 使用商家切换监听器
const queryParams = computed(() => ({
  page: pagination.value.currentPage,
  page_size: pagination.value.pageSize,
  search: search.value || undefined,
  // 添加筛选条件
  department_id: statisticsFilters.value.departmentId || undefined,
  start_date: statisticsFilters.value.dateRange?.[0] || undefined,
  end_date: statisticsFilters.value.dateRange?.[1] || undefined
}))

const { fetchWithMerchantFilter } = useMerchantSwitch(
  fetchUserConfigs,
  queryParams,
  'merchant_id'
)

// 使用商户过滤器获取当前商户信息
const { merchantQueryParams } = useMerchantFilter(queryParams, 'merchant_id')

watch(
  [() => pagination.value.currentPage, () => pagination.value.pageSize],
  ([newPage, newPageSize], [oldPage, oldPageSize]) => {
    if (newPage !== oldPage || newPageSize !== oldPageSize) {
      fetchWithMerchantFilter();
    }
  },
  { immediate: false }
);

watch(
  () => route.query.refresh,
  (newValue, oldValue) => {
    if (newValue && newValue !== oldValue) {
      fetchWithMerchantFilter();
    }
  },
  { immediate: false }
);

// 监听商户切换，重新加载部门选项
watch(
  () => merchantQueryParams.value.merchant_id,
  (newMerchantId, oldMerchantId) => {
    if (newMerchantId !== oldMerchantId) {
      // 重置部门筛选
      statisticsFilters.value.departmentId = null
      // 重新加载部门选项
      fetchDepartmentOptions()
      // 重新加载统计数据
      fetchStatistics()
    }
  },
  { immediate: false }
);


const handleSearch = () => {
  pagination.value.currentPage = 1
  fetchWithMerchantFilter()
}


const handleAdd = () => {
  router.push('/walmart/user/add')
}


const handleEdit = (row) => {
  router.push(`/walmart/user/edit/${row.id}`)
}


const confirmDelete = (id) => {
  ElMessageBox.confirm('此操作将永久删除该配置, 是否继续?', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async () => {
    loading.value = true
    try {
      const success = await userConfigStore.deleteConfig(id)
      if (success) {
        ElMessage.success('删除成功')
        fetchWithMerchantFilter()
      } else {
        ElMessage.error('删除失败')
      }
    } catch (error) {
      console.error('删除配置失败', error)
      ElMessage.error('删除配置失败')
    } finally {
      loading.value = false
    }
  }).catch(() => {
    console.log('取消删除')
  })
}

// 批量删除相关方法
const handleSelectionChange = (selection) => {
  selectedCKs.value = selection
}

const handleSelectAll = () => {
  if (selectedCKs.value.length === userConfigs.value.length) {
    // 当前是全选状态，执行取消全选
    selectedCKs.value = []
  } else {
    // 当前不是全选状态，执行全选
    selectedCKs.value = [...userConfigs.value]
  }
}

const isAllSelected = computed(() => {
  return userConfigs.value.length > 0 && selectedCKs.value.length === userConfigs.value.length
})

const isIndeterminate = computed(() => {
  return selectedCKs.value.length > 0 && selectedCKs.value.length < userConfigs.value.length
})

const handleBatchDelete = async () => {
  if (selectedCKs.value.length === 0) {
    ElMessage.warning('请先选择要删除的CK')
    return
  }

  const ckIds = selectedCKs.value.map(ck => ck.id)
  const ckCount = ckIds.length

  try {
    await ElMessageBox.confirm(
      `此操作将删除选中的 ${ckCount} 个CK配置，是否继续？`,
      '批量删除确认',
      {
        confirmButtonText: '确定删除',
        cancelButtonText: '取消',
        type: 'warning',
        dangerouslyUseHTMLString: true,
        message: `
          <div>
            <p>即将删除 <strong>${ckCount}</strong> 个CK配置：</p>
            <ul style="max-height: 200px; overflow-y: auto; margin: 10px 0;">
              ${selectedCKs.value.map(ck =>
          `<li>ID: ${ck.id} - ${ck.sign?.substring(0, 30)}${ck.sign?.length > 30 ? '...' : ''}</li>`
        ).join('')}
            </ul>
            <p style="color: #e6a23c; font-weight: bold;">⚠️ 此操作不可撤销，请确认！</p>
          </div>
        `
      }
    )

    batchDeleteLoading.value = true

    const result = await userConfigStore.batchDeleteConfigs(ckIds)

    // 显示删除结果
    if (result.success_count > 0) {
      ElMessage.success(
        `批量删除完成：成功删除 ${result.success_count} 个，失败 ${result.failed_count} 个`
      )
    }

    if (result.failed_count > 0) {
      // 显示失败详情
      const failedDetails = result.failed_items.map(item =>
        `ID ${item.ck_id}: ${item.error}`
      ).join('\n')

      ElMessageBox.alert(
        `删除失败的CK详情：\n${failedDetails}`,
        '部分删除失败',
        {
          confirmButtonText: '确定',
          type: 'warning'
        }
      )
    }

    // 清空选择并刷新列表
    selectedCKs.value = []
    fetchWithMerchantFilter()

  } catch (error) {
    if (error !== 'cancel') {
      console.error('批量删除失败:', error)
      ElMessage.error('批量删除失败：' + (error.message || '未知错误'))
    }
  } finally {
    batchDeleteLoading.value = false
  }
}

// 批量启用CK
const handleBatchEnable = async () => {
  if (selectedCKs.value.length === 0) {
    ElMessage.warning('请先选择要启用的CK')
    return
  }

  const ckIds = selectedCKs.value.map(ck => ck.id)
  const ckCount = ckIds.length
  const enabledCount = selectedCKs.value.filter(ck => ck.active).length
  const disabledCount = ckCount - enabledCount

  try {
    await ElMessageBox.confirm(
      `此操作将启用选中的 ${ckCount} 个CK配置，是否继续？`,
      '批量启用确认',
      {
        confirmButtonText: '确定启用',
        cancelButtonText: '取消',
        type: 'info',
        dangerouslyUseHTMLString: true,
        message: `
          <div>
            <p>即将启用 <strong>${ckCount}</strong> 个CK配置：</p>
            <ul style="max-height: 200px; overflow-y: auto; margin: 10px 0;">
              ${selectedCKs.value.map(ck =>
          `<li>ID: ${ck.id} - ${ck.sign?.substring(0, 30)}${ck.sign?.length > 30 ? '...' : ''}
                   <span style="color: ${ck.active ? '#67c23a' : '#f56c6c'};">(${ck.active ? '已启用' : '已禁用'})</span></li>`
        ).join('')}
            </ul>
            <p style="color: #409eff;">其中 ${enabledCount} 个已启用，${disabledCount} 个已禁用</p>
          </div>
        `
      }
    )

    batchEnableLoading.value = true

    const result = await userConfigStore.batchEnableConfigs(ckIds)

    // 显示启用结果
    if (result.success_count > 0) {
      ElMessage.success(
        `批量启用完成：成功启用 ${result.success_count} 个，失败 ${result.failed_count} 个`
      )
    }

    if (result.failed_count > 0) {
      // 显示失败详情
      const failedDetails = result.failed_items.map(item =>
        `ID ${item.ck_id}: ${item.error}`
      ).join('\n')

      ElMessageBox.alert(
        `启用失败的CK详情：\n${failedDetails}`,
        '部分启用失败',
        {
          confirmButtonText: '确定',
          type: 'warning'
        }
      )
    }

    // 清空选择并刷新列表
    selectedCKs.value = []
    fetchWithMerchantFilter()

  } catch (error) {
    if (error !== 'cancel') {
      console.error('批量启用失败:', error)
      ElMessage.error('批量启用失败：' + (error.message || '未知错误'))
    }
  } finally {
    batchEnableLoading.value = false
  }
}

// 批量禁用CK
const handleBatchDisable = async () => {
  if (selectedCKs.value.length === 0) {
    ElMessage.warning('请先选择要禁用的CK')
    return
  }

  const ckIds = selectedCKs.value.map(ck => ck.id)
  const ckCount = ckIds.length
  const enabledCount = selectedCKs.value.filter(ck => ck.active).length
  const disabledCount = ckCount - enabledCount

  try {
    await ElMessageBox.confirm(
      `此操作将禁用选中的 ${ckCount} 个CK配置，是否继续？`,
      '批量禁用确认',
      {
        confirmButtonText: '确定禁用',
        cancelButtonText: '取消',
        type: 'warning',
        dangerouslyUseHTMLString: true,
        message: `
          <div>
            <p>即将禁用 <strong>${ckCount}</strong> 个CK配置：</p>
            <ul style="max-height: 200px; overflow-y: auto; margin: 10px 0;">
              ${selectedCKs.value.map(ck =>
          `<li>ID: ${ck.id} - ${ck.sign?.substring(0, 30)}${ck.sign?.length > 30 ? '...' : ''}
                   <span style="color: ${ck.active ? '#67c23a' : '#f56c6c'};">(${ck.active ? '已启用' : '已禁用'})</span></li>`
        ).join('')}
            </ul>
            <p style="color: #e6a23c;">其中 ${enabledCount} 个已启用，${disabledCount} 个已禁用</p>
          </div>
        `
      }
    )

    batchDisableLoading.value = true

    const result = await userConfigStore.batchDisableConfigs(ckIds)

    // 显示禁用结果
    if (result.success_count > 0) {
      ElMessage.success(
        `批量禁用完成：成功禁用 ${result.success_count} 个，失败 ${result.failed_count} 个`
      )
    }

    if (result.failed_count > 0) {
      // 显示失败详情
      const failedDetails = result.failed_items.map(item =>
        `ID ${item.ck_id}: ${item.error}`
      ).join('\n')

      ElMessageBox.alert(
        `禁用失败的CK详情：\n${failedDetails}`,
        '部分禁用失败',
        {
          confirmButtonText: '确定',
          type: 'warning'
        }
      )
    }

    // 清空选择并刷新列表
    selectedCKs.value = []
    fetchWithMerchantFilter()

  } catch (error) {
    if (error !== 'cancel') {
      console.error('批量禁用失败:', error)
      ElMessage.error('批量禁用失败：' + (error.message || '未知错误'))
    }
  } finally {
    batchDisableLoading.value = false
  }
}

const userConfigs = computed(() => userConfigStore.configList)

// 创建者信息显示处理函数
const getCreatorDisplayText = (row) => {
  if (!row.creatorName && !row.creatorUsername) {
    return '-'
  }

  if (row.creatorName === '已删除' || row.creatorUsername === '已删除') {
    return '已删除'
  }

  if (row.creatorName === '***' || row.creatorUsername === '***') {
    return '***'
  }

  // 优先显示姓名，如果没有姓名则显示用户名
  return row.creatorName || row.creatorUsername || '-'
}

const getCreatorTextClass = (row) => {
  const text = getCreatorDisplayText(row)
  if (text === '-' || text === '已删除') {
    return 'text-gray-400 italic'
  }
  if (text === '***') {
    return 'text-gray-500'
  }
  return ''
}

// 查看CK统计
const handleViewStatistics = (row) => {
  router.push(`/walmart/user/statistics/${row.id}`)
}

// Redis同步功能已移除 - SimplifiedCKService使用数据库直连，无需缓存同步

// 格式化金额显示（分转元）
const formatAmount = (amount) => {
  if (!amount) return '0.00'
  const value = (amount / 100).toFixed(2)
  // 添加千分位分隔符
  return value.replace(/\B(?=(\d{3})+(?!\d))/g, ',')
}

// 格式化数字显示
const formatNumber = (num) => {
  if (!num) return '0'
  if (num >= 10000) {
    return (num / 10000).toFixed(1) + 'w'
  } else if (num >= 1000) {
    return (num / 1000).toFixed(1) + 'k'
  }
  return num.toString()
}

// 格式化签名显示 - 隐藏中间部分
const formatSignDisplay = (sign) => {
  if (!sign) return 'N/A'

  // 如果签名长度小于等于20，直接显示
  if (sign.length <= 20) {
    return sign
  }

  // 显示前10个字符 + ... + 后10个字符
  return sign.substring(0, 10) + '...' + sign.substring(sign.length - 10)
}

// 获取部门选项 - 基于API响应动态处理，不硬编码角色判断
const fetchDepartmentOptions = async () => {
  try {
    // 使用商户过滤参数获取部门列表API
    const params = {
      is_tree: false,
      ...merchantQueryParams.value
    }
    const response = await departmentApi.getList(params)
    departmentOptions.value = response.items || []
  } catch (error) {
    console.error('获取部门列表失败:', error)

    // 如果是403错误，说明用户没有访问部门列表的权限
    // 这通常意味着用户只能访问自己的部门，尝试使用专用API
    if (error.response?.status === 403) {
      console.warn('无权限访问部门列表，尝试获取用户所属部门')
      try {
        const response = await departmentApi.getMyDepartment()
        if (response && response.data) {
          departmentOptions.value = [response.data]
          // 自动选择用户所属部门
          statisticsFilters.value.departmentId = response.data.id
        } else {
          departmentOptions.value = []
          console.warn('用户未分配部门')
        }
      } catch (fallbackError) {
        console.error('获取用户部门失败:', fallbackError)
        departmentOptions.value = []
      }
    } else {
      // 其他错误，清空部门选项
      departmentOptions.value = []
    }
  }
}

// 获取统计数据
const fetchStatistics = async () => {
  // 统计数据现在通过 fetchUserConfigs 集成获取，无需单独调用
  // 这个方法保留用于兼容性，实际上会触发列表数据刷新
  fetchWithMerchantFilter()
}

// 处理筛选条件变化
const handleStatisticsFilterChange = () => {
  // 重置分页到第一页
  pagination.value.currentPage = 1
  // 同时刷新统计数据和CK列表数据
  fetchStatistics()
  fetchWithMerchantFilter()
}

// 刷新统计数据和CK列表
const refreshStatistics = () => {
  // 同时刷新统计数据和CK列表数据
  fetchStatistics()
  fetchWithMerchantFilter()
}

// CK监控相关方法
const fetchMonitorStatus = async () => {
  try {
    monitorLoading.value = true

    const params = {}
    if (merchantQueryParams.value.merchant_id) {
      params.merchant_id = merchantQueryParams.value.merchant_id
    }

    const [statusResponse, healthResponse] = await Promise.all([
      ckMonitorApi.getStatus(params),
      ckMonitorApi.getHealth()
    ])

    monitorData.value = {
      status: statusResponse.data,
      health: healthResponse.health,
      alerts: statusResponse.alerts || [],
      summary: statusResponse.summary || {}
    }

  } catch (error) {
    console.error('获取监控状态失败:', error)
    ElMessage.error('获取监控状态失败：' + (error.message || '未知错误'))
  } finally {
    monitorLoading.value = false
  }
}

const handleTestLoadBalance = async () => {
  try {
    await ElMessageBox.confirm(
      '此操作将测试CK负载均衡功能，进行多轮CK选择测试。是否继续？',
      '负载均衡测试',
      {
        confirmButtonText: '开始测试',
        cancelButtonText: '取消',
        type: 'info'
      }
    )

    testLoading.value = true

    const params = {
      test_rounds: 10
    }
    if (merchantQueryParams.value.merchant_id) {
      params.merchant_id = merchantQueryParams.value.merchant_id
    }

    const response = await ckMonitorApi.testLoadBalance(params)

    ElMessage.success('负载均衡测试完成')

    // 显示测试结果
    const testResults = response.test_results
    const diagnosis = testResults.diagnosis || {}
    const deptStats = diagnosis.department_stats || {}
    const ckStats = diagnosis.ck_stats || {}

    const message = `
📊 负载均衡测试结果：
- 测试轮数：${testResults.test_rounds}
- 使用的不同CK数量：${testResults.unique_ck_count}
- 负载均衡分数：${testResults.balance_score.toFixed(1)}
- 是否均衡：${testResults.is_balanced ? '是' : '否'}

📋 系统状态：
- 部门状态：${deptStats.binding_enabled_departments || 0}/${deptStats.total_departments || 0} 个启用绑卡
- CK状态：${ckStats.available_cks || 0}/${ckStats.total_cks || 0} 个可用
- 诊断摘要：${response.diagnosis_summary || '无'}

💡 建议：${response.recommendation}
    `.trim()

    ElMessageBox.alert(message, '负载均衡测试结果', {
      confirmButtonText: '确定',
      type: testResults.is_balanced ? 'success' : 'warning'
    })

    // 刷新监控数据
    fetchMonitorStatus()

  } catch (error) {
    if (error !== 'cancel') {
      console.error('负载均衡测试失败:', error)
      ElMessage.error('测试失败：' + (error.message || '未知错误'))
    }
  } finally {
    testLoading.value = false
  }
}

const toggleMonitorPanel = () => {
  showMonitorPanel.value = !showMonitorPanel.value
  if (showMonitorPanel.value && !monitorData.value.status) {
    fetchMonitorStatus()
  }
}

// 导出相关方法
const handleExport = () => {
  exportDialogVisible.value = true
}

const executeExport = async () => {
  try {
    exportLoading.value = true

    // 构建导出参数
    const exportParams = {
      format: exportForm.value.format,
      merchant_id: merchantQueryParams.value.merchant_id
    }

    // 如果包含筛选条件，添加当前筛选参数
    if (exportForm.value.includeFilters) {
      if (statisticsFilters.value.departmentId) {
        exportParams.department_id = statisticsFilters.value.departmentId
      }
      if (statisticsFilters.value.dateRange?.[0]) {
        exportParams.start_date = statisticsFilters.value.dateRange[0]
      }
      if (statisticsFilters.value.dateRange?.[1]) {
        exportParams.end_date = statisticsFilters.value.dateRange[1]
      }
    }

    // 调用导出API
    const result = await walmartCKApi.exportData(exportParams)

    // 创建下载链接
    const url = window.URL.createObjectURL(result.blob)
    const link = document.createElement('a')
    link.href = url
    link.download = result.filename
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    window.URL.revokeObjectURL(url)

    ElMessage.success('导出成功')
    exportDialogVisible.value = false

  } catch (error) {
    console.error('导出失败:', error)
    ElMessage.error('导出失败：' + (error.message || '未知错误'))
  } finally {
    exportLoading.value = false
  }
}

// 获取部门名称的辅助方法
const getDepartmentName = (departmentId) => {
  const department = departmentOptions.value.find(dept => dept.id === departmentId)
  return department ? department.name : '未知部门'
}

onMounted(() => {
  fetchWithMerchantFilter()
  fetchDepartmentOptions()
  fetchStatistics()
})
</script>

<template>
  <div class="page-container walmart-ck-config-list">
    <!-- 统计卡片区域 -->
    <div class="statistics-section" style="margin-bottom: 12px;">
      <!-- 筛选器 -->
      <el-card shadow="never" style="margin-bottom: 8px;">
        <template #header>
          <div class="filter-header">
            <span class="header-title">筛选条件</span>
          </div>
        </template>
        <el-row :gutter="16">
          <el-col :span="6">
            <el-form-item label="部门筛选">
              <el-select v-model="statisticsFilters.departmentId" placeholder="选择部门" clearable
                @change="handleStatisticsFilterChange" style="width: 100%;">
                <el-option label="全部部门" :value="null" />
                <el-option v-for="dept in departmentOptions" :key="dept.id" :label="dept.name" :value="dept.id" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="日期范围">
              <el-date-picker v-model="statisticsFilters.dateRange" type="daterange" range-separator="至"
                start-placeholder="开始日期" end-placeholder="结束日期" format="YYYY-MM-DD" value-format="YYYY-MM-DD"
                @change="handleStatisticsFilterChange" style="width: 100%;" />
            </el-form-item>
          </el-col>
          <el-col :span="4">
            <el-button type="primary" @click="refreshStatistics" :loading="statisticsLoading">
              刷新统计
            </el-button>
          </el-col>
        </el-row>
      </el-card>

      <!-- 统计数据 - 简洁文字版 -->
      <el-card shadow="never" style="margin-bottom: 16px;" v-loading="statisticsLoading">
        <el-row :gutter="16">
          <!--<el-col :xs="12" :sm="8" :md="6" :lg="4">
            <el-statistic title="CK总数" :value="statistics.summary?.total_ck_count || 0" />
          </el-col>-->
          <el-col :xs="12" :sm="8" :md="6" :lg="4">
            <el-statistic title="可用CK" :value="statistics.summary?.available_ck_count || 0">
              <template #suffix>
                <el-tag type="success" size="small" effect="plain">可用</el-tag>
              </template>
            </el-statistic>
          </el-col>
          <el-col :xs="12" :sm="8" :md="6" :lg="4">
            <el-statistic title="已过期CK" :value="statistics.summary?.expired_ck_count || 0">
              <template #suffix>
                <el-tag type="warning" size="small" effect="plain">过期</el-tag>
              </template>
            </el-statistic>
          </el-col>
          <el-col :xs="12" :sm="8" :md="6" :lg="4">
            <el-statistic title="已删除CK" :value="statistics.summary?.deleted_ck_count || 0">
              <template #suffix>
                <el-tag type="info" size="small" effect="plain">删除</el-tag>
              </template>
            </el-statistic>
          </el-col>
          <el-col :xs="12" :sm="8" :md="6" :lg="4">
            <el-statistic title="成功绑卡" :value="statistics.summary?.total_success || 0">
              <template #suffix>
                <el-tag type="success" size="small" effect="plain">成功</el-tag>
              </template>
            </el-statistic>
          </el-col>
          <el-col :xs="12" :sm="8" :md="6" :lg="4">
            <el-statistic title="绑卡金额" :value="formatAmount(statistics.summary?.total_actual_amount || 0)">
              <template #suffix>
                <el-tag type="primary" size="small" effect="plain">元</el-tag>
              </template>
            </el-statistic>
          </el-col>
        </el-row>
      </el-card>
    </div>

    <!-- CK监控面板 - 仅有权限用户可见 -->
    <div v-if="canViewMonitor" class="monitor-section" style="margin-bottom: 12px;">
      <el-card shadow="never">
        <template #header>
          <div class="card-header">
            <span class="header-title">CK负载均衡监控</span>
            <el-space wrap>
              <el-button type="primary" :icon="showMonitorPanel ? 'ArrowUp' : 'ArrowDown'" @click="toggleMonitorPanel"
                size="small">
                {{ showMonitorPanel ? '收起监控' : '展开监控' }}
              </el-button>
              <el-button v-if="showMonitorPanel" type="success" icon="Refresh" @click="fetchMonitorStatus"
                :loading="monitorLoading" size="small">
                刷新状态
              </el-button>
              <el-button v-if="showMonitorPanel && canTestLoadBalance" type="warning" icon="DataAnalysis"
                @click="handleTestLoadBalance" :loading="testLoading" size="small">
                负载均衡测试
              </el-button>
            </el-space>
          </div>
        </template>

        <div v-if="showMonitorPanel" v-loading="monitorLoading">
          <!-- 告警信息 -->
          <div v-if="monitorData.alerts && monitorData.alerts.length > 0" style="margin-bottom: 16px;">
            <el-alert v-for="(alert, index) in monitorData.alerts" :key="index" :title="alert.title"
              :description="alert.description"
              :type="alert.severity === 'high' ? 'error' : alert.severity === 'medium' ? 'warning' : 'info'"
              :closable="false" style="margin-bottom: 8px;" />
          </div>

          <!-- 监控状态概览 -->
          <el-row :gutter="16" style="margin-bottom: 16px;">
            <el-col :xs="12" :sm="6" :md="6" :lg="6">
              <el-statistic title="负载均衡分数" :value="monitorData.summary?.load_balance_score || 0">
                <template #suffix>
                  <el-tag :type="(monitorData.summary?.load_balance_score || 0) > 80 ? 'success' :
                    (monitorData.summary?.load_balance_score || 0) > 60 ? 'warning' : 'danger'" size="small"
                    effect="plain">
                    分
                  </el-tag>
                </template>
              </el-statistic>
            </el-col>
            <el-col :xs="12" :sm="6" :md="6" :lg="6">
              <el-statistic title="服务健康分数" :value="monitorData.summary?.service_health_score || 0">
                <template #suffix>
                  <el-tag :type="(monitorData.summary?.service_health_score || 0) > 80 ? 'success' :
                    (monitorData.summary?.service_health_score || 0) > 60 ? 'warning' : 'danger'" size="small"
                    effect="plain">
                    分
                  </el-tag>
                </template>
              </el-statistic>
            </el-col>
            <el-col :xs="12" :sm="6" :md="6" :lg="6">
              <el-statistic title="告警数量" :value="monitorData.summary?.total_alerts || 0">
                <template #suffix>
                  <el-tag :type="(monitorData.summary?.total_alerts || 0) === 0 ? 'success' : 'warning'" size="small"
                    effect="plain">
                    个
                  </el-tag>
                </template>
              </el-statistic>
            </el-col>
            <el-col :xs="12" :sm="6" :md="6" :lg="6">
              <el-statistic title="高危告警" :value="monitorData.summary?.high_severity_alerts || 0">
                <template #suffix>
                  <el-tag :type="(monitorData.summary?.high_severity_alerts || 0) === 0 ? 'success' : 'danger'"
                    size="small" effect="plain">
                    个
                  </el-tag>
                </template>
              </el-statistic>
            </el-col>
          </el-row>

          <!-- 详细状态信息 -->
          <el-row :gutter="16" v-if="monitorData.status || monitorData.health">
            <el-col :span="12">
              <el-card shadow="hover" style="height: 200px;">
                <template #header>
                  <span>负载均衡状态</span>
                </template>
                <div v-if="monitorData.status">
                  <p><strong>服务类型:</strong>
                    <el-tag type="success" size="small">
                      SimplifiedCKService
                    </el-tag>
                  </p>
                  <p><strong>可用CK数量:</strong> {{ monitorData.status.ck_stats?.available_count || 0 }}</p>
                  <p><strong>总CK数量:</strong> {{ monitorData.status.ck_stats?.total_count || 0 }}</p>
                  <p><strong>负载均衡分数:</strong> {{ monitorData.status.ck_stats?.load_balance_score || 0 }}</p>
                </div>
                <div v-else>
                  <el-empty description="暂无负载均衡状态数据" :image-size="60" />
                </div>
              </el-card>
            </el-col>
            <el-col :span="12">
              <el-card shadow="hover" style="height: 200px;">
                <template #header>
                  <span>服务健康状态</span>
                </template>
                <div v-if="monitorData.health">
                  <p><strong>服务状态:</strong>
                    <el-tag :type="monitorData.health.status === 'healthy' ? 'success' : 'danger'" size="small">
                      {{ monitorData.health.status === 'healthy' ? '健康' : '异常' }}
                    </el-tag>
                  </p>
                  <p><strong>数据库连接:</strong>
                    <el-tag :type="monitorData.health.database_connected ? 'success' : 'danger'" size="small">
                      {{ monitorData.health.database_connected ? '正常' : '异常' }}
                    </el-tag>
                  </p>
                  <p><strong>服务类型:</strong>
                    <el-tag type="info" size="small">
                      {{ monitorData.health.service_type || 'simplified_ck_service' }}
                    </el-tag>
                  </p>
                  <p><strong>Redis状态:</strong>
                    <el-tag type="warning" size="small">
                      已禁用 (使用数据库直连)
                    </el-tag>
                  </p>
                </div>
                <div v-else>
                  <el-empty description="暂无服务健康状态数据" :image-size="60" />
                </div>
              </el-card>
            </el-col>
          </el-row>
        </div>
      </el-card>
    </div>

    <el-card shadow="never">
      <template #header>
        <div class="card-header">
          <div class="header-left">
            <span class="header-title">绑卡用户</span>
            <el-checkbox v-if="userConfigs.length > 0" :model-value="isAllSelected" :indeterminate="isIndeterminate"
              @change="handleSelectAll" style="margin-left: 16px;">
              全选 ({{ selectedCKs.length }}/{{ userConfigs.length }})
            </el-checkbox>
          </div>
          <el-space wrap>
            <el-input v-model="search" placeholder="搜索用户签名或描述" clearable @keyup.enter="handleSearch"
              style="width: 250px;">
              <template #prefix>
                <el-icon>
                  <Search />
                </el-icon>
              </template>
            </el-input>
            <el-button :icon="Search" @click="handleSearch" size="small">搜索</el-button>
            <el-button type="primary" :icon="Plus" @click="handleAdd" size="small">新增cookie</el-button>
            <el-button type="success" :icon="Download" @click="handleExport" size="small">导出数据</el-button>
            <el-button type="success" :icon="CircleCheck" @click="handleBatchEnable"
              :disabled="selectedCKs.length === 0" :loading="batchEnableLoading" size="small">
              批量启用 ({{ selectedCKs.length }})
            </el-button>
            <el-button type="warning" :icon="CircleClose" @click="handleBatchDisable"
              :disabled="selectedCKs.length === 0" :loading="batchDisableLoading" size="small">
              批量禁用 ({{ selectedCKs.length }})
            </el-button>
            <el-button type="danger" :icon="Delete" @click="handleBatchDelete" :disabled="selectedCKs.length === 0"
              :loading="batchDeleteLoading" size="small">
              批量删除 ({{ selectedCKs.length }})
            </el-button>
            <!-- Redis同步功能已移除，使用SimplifiedCKService不需要缓存同步 -->
            <!-- <el-tooltip content="同步测试工具" placement="top">
              <el-button type="info" @click="handleGoToSyncTest">测试工具</el-button>
            </el-tooltip> -->
            <el-tooltip content="刷新列表" placement="top">
              <el-button :icon="Refresh" circle @click="fetchWithMerchantFilter"></el-button>
            </el-tooltip>
          </el-space>
        </div>
      </template>

      <el-table :data="userConfigs" v-loading="loading" border stripe row-key="id" style="width: 100%"
        @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column type="index" label="序号" width="60" align="center" />
        <el-table-column label="id" width="100" align="center" sortable prop="id" />
        <el-table-column label="状态" width="90" align="center" sortable prop="active">
          <template #default="scope">
            <el-tag :type="scope.row.active ? 'success' : 'danger'" effect="light" round>
              <el-icon :size="14" style="vertical-align: middle; margin-right: 4px;">
                <component :is="scope.row.active ? 'CircleCheck' : 'CircleClose'" />
              </el-icon>
              {{ scope.row.active ? '启用' : '禁用' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="sign" label="用户签名 (Sign)" min-width="250" show-overflow-tooltip sortable>
          <template #default="scope">
            <el-tooltip :content="scope.row.sign ?? '无签名'" placement="top">
              <span class="sign-display">
                {{ formatSignDisplay(scope.row.sign) }}
              </span>
            </el-tooltip>
          </template>
        </el-table-column>
        <el-table-column prop="description" label="描述" min-width="180" show-overflow-tooltip />
        <el-table-column prop="merchantName" label="所属商户" width="140" align="center" show-overflow-tooltip sortable>
          <template #default="scope">
            <el-tooltip :content="scope.row.merchantName || '未知商户'" placement="top">
              <span>{{ scope.row.merchantName || '未知商户' }}</span>
            </el-tooltip>
          </template>
        </el-table-column>
        <el-table-column prop="departmentName" label="所属部门" width="140" align="center" show-overflow-tooltip sortable>
          <template #default="scope">
            <el-tooltip :content="scope.row.departmentName || '未知部门'" placement="top">
              <span>{{ scope.row.departmentName || '未知部门' }}</span>
            </el-tooltip>
          </template>
        </el-table-column>
        <el-table-column prop="creatorName" label="创建者" width="120" align="center" show-overflow-tooltip sortable>
          <template #default="scope">
            <el-tooltip :content="getCreatorDisplayText(scope.row)" placement="top">
              <span :class="getCreatorTextClass(scope.row)">
                {{ getCreatorDisplayText(scope.row) }}
              </span>
            </el-tooltip>
          </template>
        </el-table-column>
        <!-- <el-table-column prop="dailyLimit" label="每日限额" width="110" align="center" sortable /> -->
        <el-table-column prop="actualAmount" label="真实金额(元)" width="180" align="center" sortable />
        <el-table-column prop="bindCount" label="已绑卡数量" width="180" align="center" sortable />
        <el-table-column prop="createTime" label="创建时间" width="180" show-overflow-tooltip sortable>
          <template #default="scope">
            <el-tooltip :content="scope.row.createTime" placement="top">
              <span>{{ scope.row.createTime }}</span>
            </el-tooltip>
          </template>
        </el-table-column>
        <el-table-column prop="updateTime" label="更新时间" width="180" show-overflow-tooltip sortable>
          <template #default="scope">
            <el-tooltip :content="scope.row.updateTime" placement="top">
              <span>{{ scope.row.updateTime }}</span>
            </el-tooltip>
          </template>
        </el-table-column>
        <el-table-column label="操作" fixed="right" width="220" align="center">
          <template #default="scope">
            <el-tooltip content="查看统计" placement="top">
              <el-button type="success" :icon="DataAnalysis" size="small" circle
                @click="handleViewStatistics(scope.row)"></el-button>
            </el-tooltip>
            <el-tooltip content="编辑" placement="top">
              <el-button type="primary" :icon="Edit" size="small" circle @click="handleEdit(scope.row)"></el-button>
            </el-tooltip>
            <el-tooltip content="删除" placement="top">
              <el-button type="danger" :icon="Delete" size="small" circle
                @click="confirmDelete(scope.row.id)"></el-button>
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>

      <!-- Pagination -->
      <div class="pagination-container">
        <el-pagination v-model:current-page="pagination.currentPage" v-model:page-size="pagination.pageSize"
          :page-sizes="[10, 20, 50, 100]" :total="paginationTotal" layout="total, sizes, prev, pager, next, jumper"
          background @current-change="fetchWithMerchantFilter"
          @size-change="() => { pagination.currentPage = 1; fetchWithMerchantFilter(); }" />
      </div>
    </el-card>

    <!-- 导出对话框 -->
    <el-dialog v-model="exportDialogVisible" title="导出CK数据" width="500px" :close-on-click-modal="false">
      <el-form :model="exportForm" label-width="120px">
        <el-form-item label="导出格式">
          <el-radio-group v-model="exportForm.format">
            <el-radio value="csv">CSV格式</el-radio>
            <el-radio value="json">JSON格式</el-radio>
          </el-radio-group>
        </el-form-item>

        <el-form-item label="筛选条件">
          <el-checkbox v-model="exportForm.includeFilters">
            包含当前筛选条件
          </el-checkbox>
          <div v-if="exportForm.includeFilters" style="margin-top: 8px; font-size: 12px; color: #909399;">
            <div v-if="statisticsFilters.departmentId">部门筛选: {{ getDepartmentName(statisticsFilters.departmentId) }}
            </div>
            <div v-if="statisticsFilters.dateRange">时间范围: {{ statisticsFilters.dateRange[0] }} 至 {{
              statisticsFilters.dateRange[1] }}</div>
            <div v-if="!statisticsFilters.departmentId && !statisticsFilters.dateRange">当前无筛选条件</div>
          </div>
        </el-form-item>
      </el-form>

      <template #footer>
        <el-button @click="exportDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="executeExport" :loading="exportLoading">
          {{ exportLoading ? '导出中...' : '确认导出' }}
        </el-button>
      </template>
    </el-dialog>

  </div>
</template>

<style scoped>
.page-container {
  padding: 8px 20px 20px 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-left {
  display: flex;
  align-items: center;
}

.header-title {
  font-size: 1.1rem;
  font-weight: 600;
  color: var(--el-text-color-primary);
}

.el-space {
  gap: 10px !important;
  /* Adjust spacing */
}

.sign-display {
  cursor: help;
  /* white-space: nowrap; */
  /* Removed to allow wrapping within tooltip boundaries if needed */
  /* overflow: hidden; */
  /* Managed by show-overflow-tooltip */
  /* text-overflow: ellipsis; */
  /* Managed by show-overflow-tooltip */
  display: inline-block;
  /* Needed for ellipsis to work */
  max-width: 100%;
  /* Ensure it doesn't overflow the cell */
}

.pagination-container {
  margin-top: 25px;
  display: flex;
  justify-content: flex-end;
  padding: 10px 0;
}

/* Style for action buttons */
.el-table-column .el-button {
  margin: 0 4px;
}

/* Ensure tooltip content is readable */
.el-tooltip__popper {
  max-width: 400px;
  line-height: 1.6;
}

/* Add some hover effect to table rows */
:deep(.el-table__row) {
  transition: background-color 0.2s ease;
}

:deep(.el-table__row:hover) {
  background-color: var(--el-fill-color-light) !important;
}

/* Adjust tag style */
.el-tag {
  border: none;
  /* Remove border for a cleaner look */
}

/* 创建者信息样式 */
.text-gray-400 {
  color: #9ca3af;
}

.text-gray-500 {
  color: #6b7280;
}

.italic {
  font-style: italic;
}

/* 统计数据样式 - 简洁设计 */
.statistics-section {
  margin-bottom: 24px;
}

/* 统计数据卡片样式 */
.statistics-section :deep(.el-statistic__head) {
  color: #606266;
  font-size: 14px;
  margin-bottom: 8px;
  font-weight: 500;
}

.statistics-section :deep(.el-statistic__content) {
  font-size: 24px;
  font-weight: 600;
  color: #303133;
}

.statistics-section :deep(.el-tag) {
  margin-left: 8px;
}

.filter-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
}




.el-form-item {
  margin-bottom: 0;
}

/* 监控面板样式 */
.monitor-section {
  margin-bottom: 24px;
}

.monitor-section :deep(.el-statistic__head) {
  color: #606266;
  font-size: 14px;
  margin-bottom: 8px;
  font-weight: 500;
}

.monitor-section :deep(.el-statistic__content) {
  font-size: 20px;
  font-weight: 600;
  color: #303133;
}

.monitor-section :deep(.el-tag) {
  margin-left: 8px;
}

.monitor-section .el-card {
  border: 1px solid #e4e7ed;
}

.monitor-section .el-card:hover {
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}
</style>
