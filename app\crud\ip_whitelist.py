from typing import List, Optional
from sqlalchemy.orm import Session

from app.models.ip_whitelist import Ip<PERSON>hitelist
from app.schemas.merchant import IpWhitelistCreate, IpWhitelistUpdate
from app.crud.base import CRUDBase


def get(db: Session, id: int) -> Optional[IpWhitelist]:
    """获取单个IP白名单记录"""
    return db.query(IpWhitelist).filter(IpWhitelist.id == id).first()


def get_by_merchant(db: Session, merchant_id: int) -> List[IpWhitelist]:
    """获取商家的所有IP白名单"""
    return db.query(IpWhitelist).filter(IpWhitelist.merchant_id == merchant_id).all()


def get_by_ip_and_merchant(
    db: Session, ip: str, merchant_id: int
) -> Optional[IpWhitelist]:
    """根据IP和商家ID获取IP白名单记录"""
    return (
        db.query(IpWhitelist)
        .filter(IpWhitelist.ip == ip, IpWhitelist.merchant_id == merchant_id)
        .first()
    )


def create_with_merchant(
    db: Session, obj_in: IpWhitelistCreate, merchant_id: int
) -> IpWhitelist:
    """创建IP白名单记录"""
    db_obj = IpWhitelist(
        merchant_id=merchant_id,
        ip=obj_in.ip,
        description=obj_in.description,
    )
    db.add(db_obj)
    db.commit()
    db.refresh(db_obj)
    return db_obj


def update(db: Session, db_obj: IpWhitelist, obj_in: IpWhitelistUpdate) -> IpWhitelist:
    """更新IP白名单记录"""
    update_data = obj_in.dict(exclude_unset=True)
    for field, value in update_data.items():
        setattr(db_obj, field, value)
    db.add(db_obj)
    db.commit()
    db.refresh(db_obj)
    return db_obj


def remove(db: Session, id: int) -> IpWhitelist:
    """删除IP白名单记录"""
    obj = db.query(IpWhitelist).get(id)
    db.delete(obj)
    db.commit()
    return obj


class CRUDIpWhitelist(CRUDBase[IpWhitelist, IpWhitelistCreate, IpWhitelistUpdate]):
    """IP白名单CRUD操作类"""

    def get_by_merchant(self, db: Session, merchant_id: int) -> List[IpWhitelist]:
        """获取商家的所有IP白名单"""
        return get_by_merchant(db, merchant_id)

    def get_by_ip_and_merchant(
        self, db: Session, ip: str, merchant_id: int
    ) -> Optional[IpWhitelist]:
        """根据IP和商家ID获取IP白名单记录"""
        return get_by_ip_and_merchant(db, ip, merchant_id)

    def create_with_merchant(
        self, db: Session, obj_in: IpWhitelistCreate, merchant_id: int
    ) -> IpWhitelist:
        """创建IP白名单记录"""
        return create_with_merchant(db, obj_in, merchant_id)


# 创建CRUD实例
ip_whitelist = CRUDIpWhitelist(IpWhitelist)
