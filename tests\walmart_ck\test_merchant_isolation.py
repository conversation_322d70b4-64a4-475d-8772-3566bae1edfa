"""
测试沃尔玛CK商户隔离机制
"""

import pytest
import uuid
from datetime import datetime
from sqlalchemy.orm import Session

from app.models.user import User
from app.models.merchant import Merchant
from app.models.department import Department
from app.models.walmart_ck import WalmartCK
from app.models.card_record import CardRecord
from app.services.walmart_ck_service_new import WalmartCKService
from app.services.card_record_service import CardRecordService


class TestMerchantCKIsolation:
    """测试商户CK隔离机制"""

    @pytest.fixture
    def setup_test_data(self, db: Session):
        """设置测试数据"""
        # 创建两个商户
        merchant1 = Merchant(
            id=1001,
            name="测试商户1",
            code="TEST_MERCHANT_1",
            contact_person="联系人1",
            contact_phone="13800000001"
        )
        merchant2 = Merchant(
            id=1002,
            name="测试商户2", 
            code="TEST_MERCHANT_2",
            contact_person="联系人2",
            contact_phone="13800000002"
        )
        db.add_all([merchant1, merchant2])

        # 创建部门
        dept1 = Department(
            id=2001,
            name="商户1部门1",
            merchant_id=1001,
            parent_id=None
        )
        dept2 = Department(
            id=2002,
            name="商户2部门1",
            merchant_id=1002,
            parent_id=None
        )
        db.add_all([dept1, dept2])

        # 创建用户
        user1 = User(
            id=3001,
            username="merchant1_user",
            email="<EMAIL>",
            hashed_password="hashed_password",
            merchant_id=1001,
            department_id=2001
        )
        user2 = User(
            id=3002,
            username="merchant2_user",
            email="<EMAIL>",
            hashed_password="hashed_password",
            merchant_id=1002,
            department_id=2002
        )
        db.add_all([user1, user2])

        # 创建CK
        ck1 = WalmartCK(
            id=4001,
            sign="merchant1_ck_sign",
            merchant_id=1001,
            department_id=2001,
            daily_limit=100,
            hourly_limit=10,
            active=True,
            created_by=3001
        )
        ck2 = WalmartCK(
            id=4002,
            sign="merchant2_ck_sign",
            merchant_id=1002,
            department_id=2002,
            daily_limit=100,
            hourly_limit=10,
            active=True,
            created_by=3002
        )
        db.add_all([ck1, ck2])

        db.commit()

        return {
            'merchant1': merchant1,
            'merchant2': merchant2,
            'dept1': dept1,
            'dept2': dept2,
            'user1': user1,
            'user2': user2,
            'ck1': ck1,
            'ck2': ck2
        }

    def test_ck_merchant_isolation_validation(self, db: Session, setup_test_data):
        """测试CK商户隔离验证"""
        data = setup_test_data
        ck_service = WalmartCKService(db)

        # 测试正确的商户CK关联
        assert ck_service.validate_ck_merchant_isolation(4001, 1001) == True
        assert ck_service.validate_ck_merchant_isolation(4002, 1002) == True

        # 测试跨商户CK使用（应该失败）
        assert ck_service.validate_ck_merchant_isolation(4001, 1002) == False
        assert ck_service.validate_ck_merchant_isolation(4002, 1001) == False

    def test_get_available_ck_merchant_isolation(self, db: Session, setup_test_data):
        """测试获取可用CK时的商户隔离"""
        data = setup_test_data
        ck_service = WalmartCKService(db)

        # 商户1应该只能获取到自己的CK
        ck1 = ck_service.get_available_ck(1001, 2001)
        assert ck1 is not None
        assert ck1.id == 4001
        assert ck1.merchant_id == 1001

        # 商户2应该只能获取到自己的CK
        ck2 = ck_service.get_available_ck(1002, 2002)
        assert ck2 is not None
        assert ck2.id == 4002
        assert ck2.merchant_id == 1002

        # 商户1不应该能获取到商户2的CK
        ck_cross = ck_service.get_available_ck(1001, 2002)
        assert ck_cross is None or ck_cross.merchant_id == 1001

    def test_card_record_ck_validation(self, db: Session, setup_test_data):
        """测试绑卡记录的CK验证"""
        data = setup_test_data
        card_service = CardRecordService(db)

        # 创建绑卡记录
        card_record = CardRecord(
            id=str(uuid.uuid4()),
            merchant_id=1001,
            department_id=2001,
            walmart_ck_id=None,
            merchant_order_id="TEST_ORDER_001",
            amount=10000,
            card_number="1234567890123456",
            status='pending',
            request_id=str(uuid.uuid4()),
            request_data={},
            created_by=3001
        )
        db.add(card_record)
        db.commit()

        # 测试使用正确商户的CK绑卡（应该成功）
        try:
            result = card_service.bind_card(str(card_record.id), 4001, data['user1'])
            # 这里应该成功，不抛出异常
        except ValueError as e:
            pytest.fail(f"使用正确商户CK绑卡失败: {e}")

        # 重置卡状态
        card_record.status = 'pending'
        card_record.walmart_ck_id = None
        db.commit()

        # 测试使用其他商户的CK绑卡（应该失败）
        with pytest.raises(ValueError, match="严重安全违规"):
            card_service.bind_card(str(card_record.id), 4002, data['user1'])

    def test_merchant_isolation_integrity_check(self, db: Session, setup_test_data):
        """测试商户隔离完整性检查"""
        data = setup_test_data
        ck_service = WalmartCKService(db)

        # 检查商户1的隔离完整性
        result1 = ck_service.check_merchant_isolation_integrity(1001)
        assert result1['merchant_id'] == 1001
        assert result1['is_clean'] == True
        assert result1['issues_count'] == 0

        # 检查商户2的隔离完整性
        result2 = ck_service.check_merchant_isolation_integrity(1002)
        assert result2['merchant_id'] == 1002
        assert result2['is_clean'] == True
        assert result2['issues_count'] == 0

    def test_ck_statistics_with_success_tracking(self, db: Session, setup_test_data):
        """测试CK统计信息包含绑卡成功追踪"""
        data = setup_test_data
        ck_service = WalmartCKService(db)

        # 创建一些成功的绑卡记录
        success_record = CardRecord(
            id=str(uuid.uuid4()),
            merchant_id=1001,
            department_id=2001,
            walmart_ck_id=4001,
            merchant_order_id="SUCCESS_ORDER_001",
            amount=10000,
            card_number="1111222233334444",
            status='success',
            request_id=str(uuid.uuid4()),
            request_data={},
            created_by=3001
        )
        db.add(success_record)
        db.commit()

        # 获取统计信息
        stats = ck_service.get_ck_statistics(1001, data['user1'])
        
        assert stats['merchant_id'] == 1001
        assert stats['total_count'] == 1
        assert stats['active_count'] == 1
        assert stats['actual_success_count'] == 1
        assert 4001 in stats['ck_success_details']
        assert stats['ck_success_details'][4001] == 1

    def test_batch_bind_merchant_isolation(self, db: Session, setup_test_data):
        """测试批量绑卡的商户隔离"""
        data = setup_test_data
        card_service = CardRecordService(db)

        # 创建多个绑卡记录
        card_records = []
        for i in range(3):
            card_record = CardRecord(
                id=str(uuid.uuid4()),
                merchant_id=1001,
                department_id=2001,
                walmart_ck_id=None,
                merchant_order_id=f"BATCH_ORDER_{i:03d}",
                amount=10000,
                card_number=f"1234567890123{i:03d}",
                status='pending',
                request_id=str(uuid.uuid4()),
                request_data={},
                created_by=3001
            )
            card_records.append(card_record)
            db.add(card_record)
        
        db.commit()

        # 批量绑卡
        card_ids = [str(card.id) for card in card_records]
        result = card_service.batch_bind_cards(card_ids, data['user1'])

        # 验证所有绑卡都使用了正确商户的CK
        for card_id in card_ids:
            card = db.query(CardRecord).filter(CardRecord.id == card_id).first()
            if card.walmart_ck_id:
                ck = db.query(WalmartCK).filter(WalmartCK.id == card.walmart_ck_id).first()
                assert ck.merchant_id == 1001, f"卡记录 {card_id} 使用了错误商户的CK"
