#!/usr/bin/env python3
"""
Redis CK池修复工具
用于检查和修复Redis中CK池的状态问题
"""

import os
import sys
import asyncio
import time
from typing import Dict, List, Any, Optional

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.insert(0, project_root)

from app.core.database import get_db
from app.models.walmart_ck import WalmartCK
from app.core.logging import get_logger
from app.core.redis import get_redis

logger = get_logger("redis_ck_pool_fix")


class RedisCKPoolFixer:
    """Redis CK池修复器"""
    
    def __init__(self):
        self.db = next(get_db())
        self.redis_client = None
        
    async def __aenter__(self):
        """异步上下文管理器入口"""
        try:
            self.redis_client = await get_redis()
        except Exception as e:
            logger.error(f"Redis连接失败: {e}")
            raise
        return self
        
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器出口"""
        if self.db:
            self.db.close()
        if self.redis_client:
            await self.redis_client.close()
    
    async def diagnose_and_fix(self, merchant_id: Optional[int] = None) -> Dict[str, Any]:
        """诊断并修复Redis CK池问题"""
        logger.info("开始Redis CK池诊断和修复")
        
        fix_result = {
            "timestamp": time.time(),
            "merchant_id": merchant_id,
            "diagnosis": {},
            "fixes_applied": [],
            "final_status": {},
            "success": False
        }
        
        try:
            # 1. 诊断当前状态
            fix_result["diagnosis"] = await self._diagnose_pool_status(merchant_id)
            
            # 2. 应用修复
            fixes = await self._apply_fixes(merchant_id, fix_result["diagnosis"])
            fix_result["fixes_applied"] = fixes
            
            # 3. 验证修复结果
            fix_result["final_status"] = await self._diagnose_pool_status(merchant_id)
            
            # 4. 判断修复是否成功
            fix_result["success"] = self._evaluate_fix_success(
                fix_result["diagnosis"], 
                fix_result["final_status"]
            )
            
        except Exception as e:
            logger.error(f"Redis CK池修复失败: {e}")
            fix_result["error"] = str(e)
        
        return fix_result
    
    async def _diagnose_pool_status(self, merchant_id: Optional[int]) -> Dict[str, Any]:
        """诊断池状态"""
        diagnosis = {
            "database_cks": {},
            "redis_pools": {},
            "inconsistencies": [],
            "issues": []
        }
        
        try:
            # 1. 检查数据库中的CK
            diagnosis["database_cks"] = await self._check_database_cks(merchant_id)
            
            # 2. 检查Redis池
            diagnosis["redis_pools"] = await self._check_redis_pools(merchant_id)
            
            # 3. 发现不一致性
            diagnosis["inconsistencies"] = self._find_inconsistencies(
                diagnosis["database_cks"], 
                diagnosis["redis_pools"]
            )
            
            # 4. 识别问题
            diagnosis["issues"] = self._identify_issues(diagnosis)
            
        except Exception as e:
            logger.error(f"池状态诊断失败: {e}")
            diagnosis["error"] = str(e)
        
        return diagnosis
    
    async def _check_database_cks(self, merchant_id: Optional[int]) -> Dict[str, Any]:
        """检查数据库中的CK"""
        db_status = {
            "total_count": 0,
            "active_count": 0,
            "merchants": {},
            "ck_details": []
        }
        
        try:
            query = self.db.query(WalmartCK).filter(WalmartCK.is_deleted == False)
            if merchant_id:
                query = query.filter(WalmartCK.merchant_id == merchant_id)
            
            all_cks = query.all()
            db_status["total_count"] = len(all_cks)
            
            for ck in all_cks:
                if ck.active:
                    db_status["active_count"] += 1
                
                # 按商户分组
                if ck.merchant_id not in db_status["merchants"]:
                    db_status["merchants"][ck.merchant_id] = {
                        "total": 0,
                        "active": 0,
                        "ck_ids": []
                    }
                
                db_status["merchants"][ck.merchant_id]["total"] += 1
                db_status["merchants"][ck.merchant_id]["ck_ids"].append(ck.id)
                
                if ck.active:
                    db_status["merchants"][ck.merchant_id]["active"] += 1
                
                # CK详情
                db_status["ck_details"].append({
                    "id": ck.id,
                    "merchant_id": ck.merchant_id,
                    "department_id": ck.department_id,
                    "active": ck.active,
                    "bind_count": ck.bind_count,
                    "total_limit": ck.total_limit
                })
        
        except Exception as e:
            logger.error(f"数据库CK检查失败: {e}")
            db_status["error"] = str(e)
        
        return db_status
    
    async def _check_redis_pools(self, merchant_id: Optional[int]) -> Dict[str, Any]:
        """检查Redis池"""
        redis_status = {
            "pools_found": [],
            "pool_details": {},
            "orphaned_status_keys": [],
            "orphaned_locks": []
        }
        
        try:
            # 1. 查找所有CK池
            pool_pattern = "walmart:ck:pool:*"
            if merchant_id:
                pool_pattern = f"walmart:ck:pool:{merchant_id}"
            
            async for key in self.redis_client.scan_iter(match=pool_pattern):
                key_str = key.decode() if isinstance(key, bytes) else key
                redis_status["pools_found"].append(key_str)
                
                # 获取池详情
                pool_merchant_id = key_str.split(':')[-1]
                pool_size = await self.redis_client.zcard(key_str)
                members = await self.redis_client.zrange(key_str, 0, -1, withscores=True)
                
                redis_status["pool_details"][pool_merchant_id] = {
                    "pool_key": key_str,
                    "size": pool_size,
                    "members": {}
                }
                
                for member, score in members:
                    ck_id = member.decode() if isinstance(member, bytes) else str(member)
                    redis_status["pool_details"][pool_merchant_id]["members"][ck_id] = score
            
            # 2. 查找孤立的状态键
            status_pattern = "walmart:ck:status:*"
            async for key in self.redis_client.scan_iter(match=status_pattern):
                key_str = key.decode() if isinstance(key, bytes) else key
                ck_id = key_str.split(':')[-1]
                
                # 检查这个CK是否在任何池中
                found_in_pool = False
                for pool_info in redis_status["pool_details"].values():
                    if ck_id in pool_info["members"]:
                        found_in_pool = True
                        break
                
                if not found_in_pool:
                    redis_status["orphaned_status_keys"].append(key_str)
            
            # 3. 查找孤立的锁
            lock_pattern = "walmart:ck:lock:*"
            async for key in self.redis_client.scan_iter(match=lock_pattern):
                key_str = key.decode() if isinstance(key, bytes) else key
                redis_status["orphaned_locks"].append(key_str)
        
        except Exception as e:
            logger.error(f"Redis池检查失败: {e}")
            redis_status["error"] = str(e)
        
        return redis_status
    
    def _find_inconsistencies(self, db_status: Dict, redis_status: Dict) -> List[Dict[str, Any]]:
        """发现不一致性"""
        inconsistencies = []
        
        try:
            # 检查每个商户的CK
            for merchant_id, merchant_info in db_status.get("merchants", {}).items():
                merchant_id_str = str(merchant_id)
                
                # 检查Redis池是否存在
                if merchant_id_str not in redis_status.get("pool_details", {}):
                    if merchant_info["active"] > 0:
                        inconsistencies.append({
                            "type": "missing_redis_pool",
                            "merchant_id": merchant_id,
                            "description": f"商户 {merchant_id} 有 {merchant_info['active']} 个活跃CK但Redis池不存在"
                        })
                    continue
                
                # 检查CK数量一致性
                redis_pool = redis_status["pool_details"][merchant_id_str]
                redis_ck_count = len(redis_pool["members"])
                db_active_count = merchant_info["active"]
                
                if redis_ck_count != db_active_count:
                    inconsistencies.append({
                        "type": "ck_count_mismatch",
                        "merchant_id": merchant_id,
                        "description": f"商户 {merchant_id} Redis池有 {redis_ck_count} 个CK，数据库有 {db_active_count} 个活跃CK"
                    })
                
                # 检查具体CK是否匹配
                db_ck_ids = set(str(ck_id) for ck_id in merchant_info["ck_ids"])
                redis_ck_ids = set(redis_pool["members"].keys())
                
                missing_in_redis = db_ck_ids - redis_ck_ids
                extra_in_redis = redis_ck_ids - db_ck_ids
                
                if missing_in_redis:
                    inconsistencies.append({
                        "type": "missing_cks_in_redis",
                        "merchant_id": merchant_id,
                        "ck_ids": list(missing_in_redis),
                        "description": f"商户 {merchant_id} 这些CK在数据库中存在但Redis池中缺失: {missing_in_redis}"
                    })
                
                if extra_in_redis:
                    inconsistencies.append({
                        "type": "extra_cks_in_redis",
                        "merchant_id": merchant_id,
                        "ck_ids": list(extra_in_redis),
                        "description": f"商户 {merchant_id} 这些CK在Redis池中存在但数据库中缺失: {extra_in_redis}"
                    })
        
        except Exception as e:
            logger.error(f"不一致性检查失败: {e}")
            inconsistencies.append({
                "type": "check_error",
                "description": f"不一致性检查失败: {e}"
            })
        
        return inconsistencies
    
    def _identify_issues(self, diagnosis: Dict) -> List[str]:
        """识别问题"""
        issues = []
        
        try:
            # 检查基本问题
            if diagnosis.get("database_cks", {}).get("active_count", 0) == 0:
                issues.append("没有活跃的CK")
            
            if not diagnosis.get("redis_pools", {}).get("pools_found"):
                issues.append("没有找到Redis CK池")
            
            # 检查不一致性
            inconsistencies = diagnosis.get("inconsistencies", [])
            if inconsistencies:
                issues.append(f"发现 {len(inconsistencies)} 个数据不一致问题")
            
            # 检查孤立数据
            orphaned_status = len(diagnosis.get("redis_pools", {}).get("orphaned_status_keys", []))
            if orphaned_status > 0:
                issues.append(f"发现 {orphaned_status} 个孤立的状态键")
            
            orphaned_locks = len(diagnosis.get("redis_pools", {}).get("orphaned_locks", []))
            if orphaned_locks > 0:
                issues.append(f"发现 {orphaned_locks} 个孤立的锁")
        
        except Exception as e:
            logger.error(f"问题识别失败: {e}")
            issues.append(f"问题识别失败: {e}")
        
        return issues
    
    async def _apply_fixes(self, merchant_id: Optional[int], diagnosis: Dict) -> List[str]:
        """应用修复"""
        fixes_applied = []
        
        try:
            # 1. 清理孤立的锁
            orphaned_locks = diagnosis.get("redis_pools", {}).get("orphaned_locks", [])
            if orphaned_locks:
                for lock_key in orphaned_locks:
                    await self.redis_client.delete(lock_key)
                fixes_applied.append(f"清理了 {len(orphaned_locks)} 个孤立的锁")
            
            # 2. 重建缺失的Redis池
            inconsistencies = diagnosis.get("inconsistencies", [])
            for inconsistency in inconsistencies:
                if inconsistency["type"] == "missing_redis_pool":
                    await self._rebuild_merchant_pool(inconsistency["merchant_id"])
                    fixes_applied.append(f"重建商户 {inconsistency['merchant_id']} 的Redis池")
                
                elif inconsistency["type"] in ["ck_count_mismatch", "missing_cks_in_redis", "extra_cks_in_redis"]:
                    await self._rebuild_merchant_pool(inconsistency["merchant_id"])
                    fixes_applied.append(f"修复商户 {inconsistency['merchant_id']} 的CK不一致问题")
            
            # 3. 清理孤立的状态键
            orphaned_status = diagnosis.get("redis_pools", {}).get("orphaned_status_keys", [])
            if orphaned_status:
                for status_key in orphaned_status:
                    await self.redis_client.delete(status_key)
                fixes_applied.append(f"清理了 {len(orphaned_status)} 个孤立的状态键")
        
        except Exception as e:
            logger.error(f"应用修复失败: {e}")
            fixes_applied.append(f"修复失败: {e}")
        
        return fixes_applied
    
    async def _rebuild_merchant_pool(self, merchant_id: int):
        """重建商户的Redis池"""
        try:
            # 获取商户的所有活跃CK
            active_cks = self.db.query(WalmartCK).filter(
                WalmartCK.merchant_id == merchant_id,
                WalmartCK.active == True,
                WalmartCK.is_deleted == False
            ).all()
            
            if not active_cks:
                logger.warning(f"商户 {merchant_id} 没有活跃的CK")
                return
            
            # 清理旧的池数据
            pool_key = f"walmart:ck:pool:{merchant_id}"
            await self.redis_client.delete(pool_key)
            
            # 重建池
            pool_data = {}
            for ck in active_cks:
                # 添加到池
                pool_data[str(ck.id)] = ck.bind_count
                
                # 更新状态
                status_key = f"walmart:ck:status:{ck.id}"
                await self.redis_client.hmset(status_key, {
                    'bind_count': ck.bind_count,
                    'pending_count': 0,
                    'total_limit': ck.total_limit,
                    'active': 1,
                    'is_deleted': 0,
                    'last_bind_time': ck.last_bind_time or ""
                })
                await self.redis_client.expire(status_key, 1800)  # 30分钟过期
            
            # 批量添加到池
            if pool_data:
                await self.redis_client.zadd(pool_key, pool_data)
                logger.info(f"重建商户 {merchant_id} 的Redis池，包含 {len(pool_data)} 个CK")
        
        except Exception as e:
            logger.error(f"重建商户 {merchant_id} Redis池失败: {e}")
            raise
    
    def _evaluate_fix_success(self, before: Dict, after: Dict) -> bool:
        """评估修复是否成功"""
        try:
            before_issues = len(before.get("issues", []))
            after_issues = len(after.get("issues", []))
            
            before_inconsistencies = len(before.get("inconsistencies", []))
            after_inconsistencies = len(after.get("inconsistencies", []))
            
            # 如果问题和不一致性都减少了，认为修复成功
            return (after_issues < before_issues) and (after_inconsistencies < before_inconsistencies)
        
        except Exception as e:
            logger.error(f"修复成功评估失败: {e}")
            return False


async def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description="Redis CK池修复工具")
    parser.add_argument("--merchant-id", type=int, help="指定商户ID")
    parser.add_argument("--verbose", action="store_true", help="详细输出")
    
    args = parser.parse_args()
    
    if args.verbose:
        import logging
        logging.getLogger().setLevel(logging.DEBUG)
    
    async with RedisCKPoolFixer() as fixer:
        results = await fixer.diagnose_and_fix(args.merchant_id)
        
        print("\n" + "="*80)
        print("Redis CK池修复结果")
        print("="*80)
        
        print(f"修复商户: {results['merchant_id'] or '全部'}")
        print(f"修复成功: {'✅' if results['success'] else '❌'}")
        
        # 诊断结果
        diagnosis = results.get('diagnosis', {})
        print(f"\n修复前状态:")
        print(f"  - 数据库活跃CK数: {diagnosis.get('database_cks', {}).get('active_count', 0)}")
        print(f"  - Redis池数量: {len(diagnosis.get('redis_pools', {}).get('pools_found', []))}")
        print(f"  - 发现问题数: {len(diagnosis.get('issues', []))}")
        print(f"  - 不一致性数: {len(diagnosis.get('inconsistencies', []))}")
        
        # 应用的修复
        fixes = results.get('fixes_applied', [])
        if fixes:
            print(f"\n应用的修复:")
            for fix in fixes:
                print(f"  - {fix}")
        
        # 最终状态
        final_status = results.get('final_status', {})
        print(f"\n修复后状态:")
        print(f"  - 数据库活跃CK数: {final_status.get('database_cks', {}).get('active_count', 0)}")
        print(f"  - Redis池数量: {len(final_status.get('redis_pools', {}).get('pools_found', []))}")
        print(f"  - 剩余问题数: {len(final_status.get('issues', []))}")
        print(f"  - 剩余不一致性数: {len(final_status.get('inconsistencies', []))}")
        
        print("\n" + "="*80)


if __name__ == "__main__":
    asyncio.run(main())
