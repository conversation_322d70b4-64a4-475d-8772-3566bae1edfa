-- ========================================
-- 沃尔玛CK导出权限迁移脚本
-- 为CK数据导出接口添加权限配置
-- ========================================

USE `walmart_card_db`;

-- ========================================
-- 1. 记录迁移开始
-- ========================================

INSERT INTO `migration_logs` (`migration_name`, `status`, `message`, `created_at`)
VALUES ('v2.5.0-add-walmart-ck-export-permission', 'started', '开始执行沃尔玛CK导出权限迁移', NOW());

-- ========================================
-- 2. 添加导出权限
-- ========================================

-- 添加沃尔玛CK导出权限
INSERT IGNORE INTO `permissions` (
    `code`,
    `name`,
    `description`,
    `resource_type`,
    `resource_path`,
    `is_enabled`,
    `sort_order`
) VALUES (
    'api:walmart-ck:export',
    '导出沃尔玛CK数据',
    '允许导出沃尔玛CK数据为CSV或JSON格式，支持筛选条件',
    'api',
    '/api/v1/walmart-ck/export',
    1,
    1050
);

-- ========================================
-- 3. 为角色分配权限
-- ========================================

-- 为超级管理员分配导出权限
INSERT IGNORE INTO `role_permissions` (`role_id`, `permission_id`)
SELECT r.id, p.id 
FROM `roles` r, `permissions` p
WHERE r.code = 'super_admin' 
AND p.code = 'api:walmart-ck:export';

-- 为商户管理员分配导出权限
INSERT IGNORE INTO `role_permissions` (`role_id`, `permission_id`)
SELECT r.id, p.id 
FROM `roles` r, `permissions` p
WHERE r.code = 'merchant_admin' 
AND p.code = 'api:walmart-ck:export';

-- 为CK供应商分配导出权限
INSERT IGNORE INTO `role_permissions` (`role_id`, `permission_id`)
SELECT r.id, p.id 
FROM `roles` r, `permissions` p
WHERE r.code = 'ck_supplier' 
AND p.code = 'api:walmart-ck:export';

-- ========================================
-- 4. 验证权限配置
-- ========================================

-- 检查导出权限是否正确插入
SELECT
    'CK导出权限检查' as check_type,
    COUNT(*) as permission_count,
    CASE
        WHEN COUNT(*) >= 1 THEN '✓ CK导出权限配置完整'
        ELSE '⚠ CK导出权限配置不完整'
    END as status
FROM `permissions`
WHERE code = 'api:walmart-ck:export';

-- 检查超级管理员权限分配
SELECT
    '超级管理员CK导出权限' as check_type,
    COUNT(*) as assigned_permissions,
    CASE
        WHEN COUNT(*) >= 1 THEN '✓ 超级管理员权限分配完整'
        ELSE '⚠ 超级管理员权限分配不完整'
    END as status
FROM `role_permissions` rp
JOIN `roles` r ON rp.role_id = r.id
JOIN `permissions` p ON rp.permission_id = p.id
WHERE r.code = 'super_admin'
AND p.code = 'api:walmart-ck:export';

-- 检查商户管理员权限分配
SELECT
    '商户管理员CK导出权限' as check_type,
    COUNT(*) as assigned_permissions,
    CASE
        WHEN COUNT(*) >= 1 THEN '✓ 商户管理员权限分配完整'
        ELSE '⚠ 商户管理员权限分配不完整'
    END as status
FROM `role_permissions` rp
JOIN `roles` r ON rp.role_id = r.id
JOIN `permissions` p ON rp.permission_id = p.id
WHERE r.code = 'merchant_admin'
AND p.code = 'api:walmart-ck:export';

-- 检查CK供应商权限分配
SELECT
    'CK供应商CK导出权限' as check_type,
    COUNT(*) as assigned_permissions,
    CASE
        WHEN COUNT(*) >= 1 THEN '✓ CK供应商权限分配完整'
        ELSE '⚠ CK供应商权限分配不完整'
    END as status
FROM `role_permissions` rp
JOIN `roles` r ON rp.role_id = r.id
JOIN `permissions` p ON rp.permission_id = p.id
WHERE r.code = 'ck_supplier'
AND p.code = 'api:walmart-ck:export';

-- ========================================
-- 5. 显示权限分配结果
-- ========================================

SELECT 
    '=== CK导出权限分配结果 ===' as summary,
    r.name as role_name,
    r.code as role_code,
    p.name as permission_name,
    p.code as permission_code
FROM `role_permissions` rp
JOIN `roles` r ON rp.role_id = r.id
JOIN `permissions` p ON rp.permission_id = p.id
WHERE p.code = 'api:walmart-ck:export'
ORDER BY r.code;

-- ========================================
-- 6. 显示当前所有沃尔玛CK相关权限
-- ========================================
SELECT 
    '=== 沃尔玛CK权限列表 ===' as summary,
    p.code as permission_code,
    p.name as permission_name,
    p.sort_order as sort_order,
    GROUP_CONCAT(r.name ORDER BY r.name SEPARATOR ', ') as assigned_roles
FROM `permissions` p
LEFT JOIN `role_permissions` rp ON p.id = rp.permission_id
LEFT JOIN `roles` r ON rp.role_id = r.id
WHERE p.code LIKE '%walmart-ck%'
GROUP BY p.id, p.code, p.name, p.sort_order
ORDER BY p.sort_order;

-- ========================================
-- 7. 记录迁移完成
-- ========================================

UPDATE `migration_logs` 
SET `status` = 'completed', 
    `message` = '沃尔玛CK导出权限迁移执行完成',
    `completed_at` = NOW()
WHERE `migration_name` = 'v2.5.0-add-walmart-ck-export-permission' 
AND `status` = 'started';

-- ========================================
-- 8. 迁移完成提示
-- ========================================

SELECT 
    '🎉 迁移完成' as status,
    '沃尔玛CK导出权限已成功配置' as message,
    'api:walmart-ck:export' as new_permission,
    '超级管理员、商户管理员、CK供应商' as assigned_roles;
