-- ========================================
-- 沃尔玛绑卡系统权限架构调整：将TOTP功能移至个人设置
-- 版本：v2.1.0
-- 日期：2025-06-28
-- 描述：将谷歌验证器管理从系统菜单移至个人设置，移除相关权限验证
-- ========================================

-- 1. 更新安全设置菜单为不可见（从左侧导航菜单中隐藏）
UPDATE `menus` 
SET `is_visible` = 0, 
    `description` = '安全设置（已移至个人设置）',
    `updated_at` = NOW(3)
WHERE `code` = 'system:security';

-- 2. 更新谷歌验证器管理菜单为不可见
UPDATE `menus` 
SET `is_visible` = 0, 
    `description` = '谷歌验证器双因子认证管理（已移至个人设置）',
    `updated_at` = NOW(3)
WHERE `code` = 'system:security:totp';

-- 3. 移除商户管理员的安全设置菜单权限（因为现在是个人设置）
DELETE rm FROM `role_menus` rm
JOIN `roles` r ON rm.role_id = r.id
JOIN `menus` m ON rm.menu_id = m.id
WHERE r.code = 'merchant_admin' 
AND m.code IN ('system:security', 'system:security:totp');

-- 4. 移除CK供应商的安全设置菜单权限
DELETE rm FROM `role_menus` rm
JOIN `roles` r ON rm.role_id = r.id
JOIN `menus` m ON rm.menu_id = m.id
WHERE r.code = 'ck_supplier' 
AND m.code IN ('system:security', 'system:security:totp');

-- 5. 移除商户管理员的TOTP API权限（因为现在通过中间件排除路径处理）
DELETE rp FROM `role_permissions` rp
JOIN `roles` r ON rp.role_id = r.id
JOIN `permissions` p ON rp.permission_id = p.id
WHERE r.code = 'merchant_admin' 
AND p.code IN (
    'menu:system:security', 'menu:system:security:totp',
    'api:/api/v1/totp', 'api:totp:status', 'api:totp:setup', 'api:totp:verify',
    'api:totp:enable', 'api:totp:disable', 'api:totp:backup-verify', 'api:totp:required'
);

-- 6. 移除CK供应商的TOTP API权限
DELETE rp FROM `role_permissions` rp
JOIN `roles` r ON rp.role_id = r.id
JOIN `permissions` p ON rp.permission_id = p.id
WHERE r.code = 'ck_supplier' 
AND p.code IN (
    'menu:system:security', 'menu:system:security:totp',
    'api:/api/v1/totp', 'api:totp:status', 'api:totp:setup', 'api:totp:verify',
    'api:totp:enable', 'api:totp:disable', 'api:totp:backup-verify', 'api:totp:required'
);

-- 7. 验证更新结果
SELECT 
    '菜单可见性更新' as check_type,
    m.name,
    m.code,
    m.is_visible,
    m.description
FROM `menus` m 
WHERE m.code IN ('system:security', 'system:security:totp');

-- 8. 验证权限清理结果
SELECT 
    '权限清理验证' as check_type,
    COUNT(*) as remaining_permissions
FROM `role_permissions` rp
JOIN `roles` r ON rp.role_id = r.id
JOIN `permissions` p ON rp.permission_id = p.id
WHERE r.code IN ('merchant_admin', 'ck_supplier')
AND p.code LIKE '%security%';

-- 9. 验证菜单权限清理结果
SELECT 
    '菜单权限清理验证' as check_type,
    COUNT(*) as remaining_menu_permissions
FROM `role_menus` rm
JOIN `roles` r ON rm.role_id = r.id
JOIN `menus` m ON rm.menu_id = m.id
WHERE r.code IN ('merchant_admin', 'ck_supplier')
AND m.code LIKE '%security%';

-- ========================================
-- 迁移说明
-- ========================================
-- 1. 安全设置菜单项已从左侧导航菜单中隐藏
-- 2. TOTP功能现在通过个人设置页面访问（/security/index）
-- 3. 所有登录用户都可以访问TOTP功能，无需特殊权限
-- 4. 保持JWT身份验证要求
-- 5. 数据隔离原则：用户只能管理自己的安全设置
-- ========================================
