import logging
import uuid
from datetime import datetime
from typing import Any, Dict

from fastapi import (
    APIRouter,
    Depends,
    HTTPException,
    status,
    Header,
    Request,
)
from sqlalchemy.orm import Session

from app.api import deps
from app.api.deps import get_db
from app.core.security import verify_api_sign, encrypt_sensitive_data
from app.crud import card as card_crud
from app.crud import merchant as merchant_crud
from app.models.card_record import CardStatus, CardRecord
from app.models.user import User
from app.schemas.card import (
    BindCardParams,
)
from app.utils.ip import is_ip_allowed
from app.utils.queue_producer import send_bind_card_task
from app.models import Merchant

router = APIRouter()
logger = logging.getLogger(__name__)


async def _validate_card_not_exists(db: Session, card_number: str, allow_retry: bool = False):
    """
    验证卡号是否已存在

    Args:
        db: 数据库会话
        card_number: 卡号
        allow_retry: 是否允许重试（对于失败状态的卡）
    """
    card = db.query(CardRecord).filter(CardRecord.card_number == card_number).first()
    if card:
        # 如果允许重试且卡状态为失败，则允许重新绑卡
        if allow_retry and card.status in ['failed', 'error']:
            logger.info(f"卡号 {card_number} 已存在但状态为 {card.status}，允许重试")
            return card  # 返回现有卡记录用于重试
        else:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="卡号已存在, 请勿重复请求",
            )
    return None


async def _validate_merchant_and_auth(db: Session, api_key: str, card_data: BindCardParams) -> Merchant:
    """验证商户和认证信息"""
    # 根据API Key找到对应的商户
    merchant = db.query(Merchant).filter(Merchant.api_key == api_key).first()
    if not merchant:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="无效的API密钥",
        )

    # 验证商户状态
    if not merchant.status:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="商户已禁用",
        )

    # 验证商户编码
    if card_data.merchant_code != merchant.code:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="商户编码与API密钥不匹配",
        )

    return merchant


async def _validate_ip_and_signature(
    request: Request,
    merchant: Merchant,
    card_data: BindCardParams,
    x_signature: str,
    x_timestamp: str,
    x_nonce: str
):
    """验证IP白名单和请求签名"""
    # 验证IP白名单
    client_ip = request.client.host
    if not is_ip_allowed(merchant.allowed_ips, client_ip):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="IP地址不在白名单中",
        )

    # 将 Pydantic 模型转换为字典用于签名验证
    request_body = card_data.model_dump()
    if not await verify_api_sign(
            data=request_body,
            secret_key=merchant.api_secret,
            signature=x_signature,
            timestamp=x_timestamp,
            nonce=x_nonce,
            method="POST",
            path="/api/v1/card-bind",
    ):
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="签名验证失败",
        )

    return request_body


def _prepare_card_data(card_data: BindCardParams, request_body: dict):
    """准备卡数据，包括加密和屏蔽敏感信息"""
    # 加密敏感数据用于存储
    encrypted_password = None
    if card_data.card_password:
        encrypted_password = encrypt_sensitive_data(card_data.card_password)

    # 准备请求体副本（用于存储，屏蔽密码）
    request_body_for_storage = request_body.copy()
    if request_body_for_storage.get("card_password"):
        request_body_for_storage["card_password"] = "******"

    return encrypted_password, request_body_for_storage


def _create_card_record(
    db: Session,
    card_data: BindCardParams,
    merchant: Merchant,
    request: Request,
    encrypted_password: str,
    request_body_for_storage: dict,
    request_id_str: str,
    trace_id: str
):
    """
    创建卡记录

    注意：department_id 和 walmart_ck_id 在创建时为 NULL，
    只有在绑卡成功后才会填入实际使用的CK信息
    """
    record_create_data = {
        "card_number": card_data.card_number,
        "card_password": encrypted_password,
        "merchant_id": merchant.id,
        "department_id": None,  # 创建时为NULL，绑卡成功后填入实际使用的CK所属部门
        "walmart_ck_id": None,  # 创建时为NULL，绑卡成功后填入实际使用的CK ID
        "ip_address": request.client.host,
        "request_id": request_id_str,
        "trace_id": trace_id,
        "status": CardStatus.PENDING,
        "request_data": request_body_for_storage,
        "ext_data": card_data.ext_data,
        "merchant_order_id": card_data.merchant_order_id,
        "amount": card_data.amount,
        "is_test_mode": card_data.debug or False,  # 设置测试模式标识
    }
    return card_crud.create(db, obj_in=record_create_data)


async def _send_bind_task(
    card_data: BindCardParams,
    merchant: Merchant,
    request: Request,
    record_id: str,
    request_id_str: str
):
    """发送绑卡任务到队列"""
    message = {
        "record_id": str(record_id),
        "request_id": request_id_str,
        "card_number": card_data.card_number,
        "card_password": card_data.card_password,
        "merchant_id": merchant.id,
        "client_ip": request.client.host,
        "ext_data": card_data.ext_data,
        "merchant_order_id": card_data.merchant_order_id,
        "amount": card_data.amount,
        "trace_id": request_id_str,
        "debug": card_data.debug or False,  # 传递debug标识
    }
    await send_bind_card_task(message)


@router.post("", response_model=Dict[str, Any])
async def bind_card(
        *,
        request: Request,
        db: Session = Depends(get_db),
        card_data: BindCardParams,
        x_timestamp: str = Header(..., alias="X-Timestamp"),
        x_nonce: str = Header(..., alias="X-Nonce"),
        x_signature: str = Header(..., alias="X-Signature"),
        api_key: str = Header(..., alias="api-key"),
) -> Any:
    """
    绑定卡

    接口说明：
    - 需要在请求头中提供API密钥和签名信息
    - API密钥通过api-key请求头传递
    - 签名信息通过X-Timestamp、X-Nonce、X-Signature请求头传递
    """
    # 判断卡号是否是2326开头，如果不是直接返回暂不支持该类型卡
    if not card_data.card_number.startswith('2326'):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="暂不支持该类型卡",
        )

    # 1. 验证卡号是否已存在（允许失败状态的卡重试）
    existing_card = await _validate_card_not_exists(db, card_data.card_number, allow_retry=True)

    # 2. 验证商户和认证信息
    merchant = await _validate_merchant_and_auth(db, api_key, card_data)

    # 3. 验证IP和签名
    request_body = await _validate_ip_and_signature(request, merchant, card_data, x_signature, x_timestamp, x_nonce)

    # 4. 准备卡数据
    encrypted_password, request_body_for_storage = _prepare_card_data(card_data, request_body)

    # 5. 生成ID
    request_id_str = str(uuid.uuid4())
    trace_id = request_id_str

    # 6. 创建或更新卡记录
    if existing_card:
        # 重试现有卡记录
        record = existing_card
        # 重置卡记录状态为待处理
        record.status = 'pending'
        record.error_message = None
        record.retry_count = (record.retry_count or 0) + 1
        record.request_data = request_body_for_storage
        record.trace_id = trace_id
        db.commit()
        logger.info(f"重试卡号 {card_data.card_number}，重试次数: {record.retry_count}")
    else:
        # 创建新卡记录
        record = _create_card_record(
            db, card_data, merchant, request, encrypted_password,
            request_body_for_storage, request_id_str, trace_id
        )

    # 7. 发送绑卡任务
    await _send_bind_task(card_data, merchant, request, record.id, request_id_str)

    return {
        "recordId": str(record.id),
        "requestId": request_id_str,
        "status": "processing",
        "merchantOrderId": card_data.merchant_order_id,
        "amount": card_data.amount,
    }
