<template>
  <div class="breadcrumb-demo">
    <h2>面包屑导航组件演示</h2>
    
    <div class="demo-section">
      <h3>1. 基础用法 - 对账台主页面</h3>
      <BreadcrumbNavigation 
        :department-path="[]"
        current-page="一级部门"
        :filters="sampleFilters"
        @navigate="handleNavigate"
      />
    </div>
    
    <div class="demo-section">
      <h3>2. 二级部门页面</h3>
      <BreadcrumbNavigation 
        :department-path="secondLevelPath"
        current-page="二级部门"
        :filters="sampleFilters"
        @navigate="handleNavigate"
      />
    </div>
    
    <div class="demo-section">
      <h3>3. 三级部门页面</h3>
      <BreadcrumbNavigation 
        :department-path="thirdLevelPath"
        current-page="三级部门"
        :filters="sampleFilters"
        @navigate="handleNavigate"
      />
    </div>
    
    <div class="demo-section">
      <h3>4. <PERSON><PERSON>明细页面</h3>
      <BreadcrumbNavigation 
        :department-path="thirdLevelPath"
        :filters="sampleFilters"
        :extra-items="[
          { name: '部门A', clickable: false },
          { name: '<PERSON><PERSON>明细统计', clickable: false }
        ]"
        :show-current-page="false"
        @navigate="handleNavigate"
      />
    </div>
    
    <div class="demo-section">
      <h3>5. 绑卡记录页面</h3>
      <BreadcrumbNavigation 
        :department-path="thirdLevelPath"
        :filters="sampleFilters"
        :extra-items="[
          { name: 'CK明细 (user_***_sign03)', clickable: true, onClick: () => alert('返回CK明细') },
          { name: '成功绑卡记录', clickable: false }
        ]"
        :show-current-page="false"
        @navigate="handleNavigate"
      />
    </div>
    
    <div class="demo-section">
      <h3>导航事件日志</h3>
      <div class="log-container">
        <div v-for="(log, index) in navigationLogs" :key="index" class="log-item">
          {{ log }}
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import BreadcrumbNavigation from './BreadcrumbNavigation.vue'

// 示例数据
const sampleFilters = {
  timeRange: 'today',
  startDate: '2024-01-15',
  endDate: '2024-01-15',
  merchantId: '1',
  viewLevel: 'department'
}

const secondLevelPath = [
  { id: '1', name: '1级部门1', level: 1 }
]

const thirdLevelPath = [
  { id: '1', name: '1级部门1', level: 1 },
  { id: '11', name: '2级部门1', level: 2 }
]

// 导航日志
const navigationLogs = ref([])

// 处理导航事件
const handleNavigate = (routeInfo) => {
  const logMessage = `导航到: ${routeInfo.name}, 查询参数: ${JSON.stringify(routeInfo.query)}`
  navigationLogs.value.unshift(logMessage)
  
  // 限制日志数量
  if (navigationLogs.value.length > 10) {
    navigationLogs.value = navigationLogs.value.slice(0, 10)
  }
  
  console.log('Navigation:', routeInfo)
}
</script>

<style scoped>
.breadcrumb-demo {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.demo-section {
  margin-bottom: 30px;
  padding: 20px;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  background: #fff;
}

.demo-section h3 {
  margin: 0 0 15px 0;
  color: #303133;
  font-size: 16px;
  font-weight: 600;
}

.log-container {
  max-height: 300px;
  overflow-y: auto;
  background: #f5f7fa;
  border-radius: 4px;
  padding: 10px;
}

.log-item {
  padding: 5px 0;
  border-bottom: 1px solid #e4e7ed;
  font-size: 12px;
  color: #606266;
  word-break: break-all;
}

.log-item:last-child {
  border-bottom: none;
}
</style>
