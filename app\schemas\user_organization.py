from typing import Optional, List, Dict, Any
from pydantic import BaseModel, Field
from datetime import datetime, date


# 用户组织关系基础模型
class UserOrganizationBase(BaseModel):
    user_id: int = Field(..., description="用户ID")
    merchant_id: int = Field(..., description="商户ID")
    department_id: Optional[int] = Field(None, description="部门ID")
    position: Optional[str] = Field(None, description="职位")
    is_primary: bool = Field(True, description="是否主要组织关系")
    status: bool = Field(True, description="状态：1启用，0禁用")
    start_date: Optional[date] = Field(None, description="开始日期")
    end_date: Optional[date] = Field(None, description="结束日期")

    class Config:
        from_attributes = True


# 创建用户组织关系请求模型
class UserOrganizationCreate(UserOrganizationBase):
    pass


# 更新用户组织关系请求模型
class UserOrganizationUpdate(BaseModel):
    merchant_id: Optional[int] = Field(None, description="商户ID")
    department_id: Optional[int] = Field(None, description="部门ID")
    position: Optional[str] = Field(None, description="职位")
    is_primary: Optional[bool] = Field(None, description="是否主要组织关系")
    status: Optional[bool] = Field(None, description="状态：1启用，0禁用")
    start_date: Optional[date] = Field(None, description="开始日期")
    end_date: Optional[date] = Field(None, description="结束日期")

    class Config:
        from_attributes = True


# 数据库中的用户组织关系模型
class UserOrganizationInDB(UserOrganizationBase):
    id: int
    created_by: Optional[int]
    created_at: datetime
    updated_at: Optional[datetime]

    class Config:
        from_attributes = True


# API响应模型
class UserOrganization(UserOrganizationInDB):
    pass


# 用户组织关系详情模型（包含关联信息）
class UserOrganizationDetail(UserOrganization):
    user_name: Optional[str] = Field(None, description="用户姓名")
    user_username: Optional[str] = Field(None, description="用户名")
    merchant_name: Optional[str] = Field(None, description="商户名称")
    merchant_code: Optional[str] = Field(None, description="商户代码")
    department_name: Optional[str] = Field(None, description="部门名称")
    department_code: Optional[str] = Field(None, description="部门代码")
    department_full_path: Optional[str] = Field(None, description="部门完整路径")
    is_active: bool = Field(False, description="关系是否有效")
    is_expired: bool = Field(False, description="关系是否已过期")
    is_future: bool = Field(False, description="关系是否为未来生效")
    full_position_path: Optional[str] = Field(None, description="完整职位路径")


# 用户组织关系列表响应模型
class UserOrganizationListResponse(BaseModel):
    items: List[UserOrganizationDetail]
    total: int
    page: int = 1
    page_size: int = 100


# 用户转移请求
class UserTransferRequest(BaseModel):
    user_id: int = Field(..., description="用户ID")
    target_department_id: int = Field(..., description="目标部门ID")
    position: Optional[str] = Field(None, description="新职位")
    end_current: bool = Field(True, description="是否结束当前组织关系")
    start_date: Optional[date] = Field(None, description="开始日期，默认为今天")
    remark: Optional[str] = Field(None, description="转移备注")


# 用户转移响应
class UserTransferResponse(BaseModel):
    success: bool = Field(..., description="是否成功")
    message: str = Field(..., description="操作消息")
    old_organization: Optional[UserOrganizationDetail] = Field(None, description="原组织关系")
    new_organization: Optional[UserOrganizationDetail] = Field(None, description="新组织关系")


# 批量用户转移请求
class BatchUserTransferRequest(BaseModel):
    user_ids: List[int] = Field(..., description="用户ID列表")
    target_department_id: int = Field(..., description="目标部门ID")
    position: Optional[str] = Field(None, description="新职位")
    end_current: bool = Field(True, description="是否结束当前组织关系")
    start_date: Optional[date] = Field(None, description="开始日期，默认为今天")


# 批量用户转移响应
class BatchUserTransferResponse(BaseModel):
    total_count: int = Field(0, description="总数量")
    success_count: int = Field(0, description="成功数量")
    failed_count: int = Field(0, description="失败数量")
    failed_items: List[Dict[str, Any]] = Field(default_factory=list, description="失败项目详情")
    successful_transfers: List[UserTransferResponse] = Field(default_factory=list, description="成功转移列表")


# 设置主要组织关系请求
class SetPrimaryOrganizationRequest(BaseModel):
    user_id: int = Field(..., description="用户ID")
    organization_id: int = Field(..., description="组织关系ID")


# 延长组织关系请求
class ExtendOrganizationRequest(BaseModel):
    organization_id: int = Field(..., description="组织关系ID")
    new_end_date: Optional[date] = Field(None, description="新的结束日期，None表示永久")


# 终止组织关系请求
class TerminateOrganizationRequest(BaseModel):
    organization_id: int = Field(..., description="组织关系ID")
    end_date: Optional[date] = Field(None, description="结束日期，默认为今天")
    reason: Optional[str] = Field(None, description="终止原因")


# 用户组织关系统计
class UserOrganizationStatistics(BaseModel):
    total_relations: int = Field(0, description="总关系数")
    active_relations: int = Field(0, description="活跃关系数")
    expired_relations: int = Field(0, description="过期关系数")
    future_relations: int = Field(0, description="未来关系数")
    primary_relations: int = Field(0, description="主要关系数")
    merchants_count: int = Field(0, description="涉及商户数")
    departments_count: int = Field(0, description="涉及部门数")


# 组织关系历史记录
class OrganizationHistory(BaseModel):
    id: int
    user_id: int
    user_name: str
    merchant_name: str
    department_name: Optional[str]
    position: Optional[str]
    start_date: Optional[date]
    end_date: Optional[date]
    is_primary: bool
    status: bool
    created_at: datetime
    updated_at: Optional[datetime]

    class Config:
        from_attributes = True


# 用户组织关系历史响应
class UserOrganizationHistoryResponse(BaseModel):
    user_id: int
    user_name: str
    current_organization: Optional[UserOrganizationDetail]
    history: List[OrganizationHistory]
    total_history_count: int


# 组织架构查询参数
class OrganizationQueryParams(BaseModel):
    merchant_id: Optional[int] = Field(None, description="商户ID")
    department_id: Optional[int] = Field(None, description="部门ID")
    user_id: Optional[int] = Field(None, description="用户ID")
    position: Optional[str] = Field(None, description="职位")
    is_primary: Optional[bool] = Field(None, description="是否主要关系")
    status: Optional[bool] = Field(None, description="状态")
    active_only: bool = Field(True, description="仅查询有效关系")
    include_expired: bool = Field(False, description="包含过期关系")
    include_future: bool = Field(False, description="包含未来关系")
    start_date_from: Optional[date] = Field(None, description="开始日期范围-起始")
    start_date_to: Optional[date] = Field(None, description="开始日期范围-结束")
    end_date_from: Optional[date] = Field(None, description="结束日期范围-起始")
    end_date_to: Optional[date] = Field(None, description="结束日期范围-结束")


# 组织架构权限检查
class OrganizationPermissionCheck(BaseModel):
    can_view: bool = Field(False, description="是否可以查看")
    can_edit: bool = Field(False, description="是否可以编辑")
    can_delete: bool = Field(False, description="是否可以删除")
    can_transfer: bool = Field(False, description="是否可以转移用户")
    can_set_primary: bool = Field(False, description="是否可以设置主要关系")
    accessible_merchants: List[int] = Field(default_factory=list, description="可访问的商户ID列表")
    accessible_departments: List[int] = Field(default_factory=list, description="可访问的部门ID列表")


# 组织架构导入请求
class OrganizationImportRequest(BaseModel):
    organizations: List[UserOrganizationCreate] = Field(..., description="组织关系列表")
    overwrite_existing: bool = Field(False, description="是否覆盖已存在的关系")
    set_as_primary: bool = Field(False, description="是否设置为主要关系")


# 组织架构导入响应
class OrganizationImportResponse(BaseModel):
    total_count: int = Field(0, description="总数量")
    success_count: int = Field(0, description="成功数量")
    failed_count: int = Field(0, description="失败数量")
    skipped_count: int = Field(0, description="跳过数量")
    failed_items: List[Dict[str, Any]] = Field(default_factory=list, description="失败项目详情")
    created_organizations: List[UserOrganization] = Field(default_factory=list, description="创建的组织关系列表")


# 用户可访问的组织范围
class UserAccessibleScope(BaseModel):
    user_id: int
    merchants: List[Dict[str, Any]] = Field(default_factory=list, description="可访问的商户")
    departments: List[Dict[str, Any]] = Field(default_factory=list, description="可访问的部门")
    scope_type: str = Field(..., description="权限范围类型：platform, merchant, department, own")
    primary_organization: Optional[UserOrganizationDetail] = Field(None, description="主要组织关系")
