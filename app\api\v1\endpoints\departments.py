"""
部门管理API - 基于DepartmentService的完整实现
"""
from typing import List, Optional, Any, Dict
from fastapi import APIRouter, Depends, HTTPException, Query, status
from sqlalchemy.orm import Session

from app.api import deps
from app.models.user import User
from app.schemas.department import (
    DepartmentCreate,
    DepartmentUpdate,
    DepartmentInDB,
    DepartmentListResponse,
    DepartmentStatistics,
    DepartmentTree,
    DepartmentBindingControls,
    DepartmentBindingControlsUpdate,
    DepartmentBindingStatus,
    DepartmentBindingControlsBatch,
    DepartmentBindingControlsResponse,
)
from app.services.department_service_new import DepartmentService
from app.services.permission_service import PermissionService
from app.services.security_service import SecurityService
from app.core.exceptions import BusinessException
from app.core.logging import get_logger

router = APIRouter()
logger = get_logger("departments_api")


class DepartmentQueryParams:
    """部门查询参数封装"""
    def __init__(
        self,
        merchant_id: Optional[int] = None,
        parent_id: Optional[int] = None,
        dept_status: Optional[bool] = None,
        name: Optional[str] = None,
        page: int = 1,
        page_size: int = 10,
        is_tree: bool = False
    ):
        self.merchant_id = merchant_id
        self.parent_id = parent_id
        self.dept_status = dept_status
        self.name = name
        self.page = page
        self.page_size = page_size
        self.is_tree = is_tree


class DepartmentQueryBuilder:
    """部门查询构建器"""

    def __init__(self, dept_service: DepartmentService, permission_service: PermissionService):
        self.dept_service = dept_service
        self.permission_service = permission_service
        self.db = dept_service.db

    def build_query_result(
        self,
        params: DepartmentQueryParams,
        current_user: User,
        target_merchant_id: Optional[int]
    ) -> Dict[str, Any]:
        """构建查询结果"""
        if target_merchant_id is None:
            # 超级管理员没有指定商户ID时，返回提示信息
            return {
                "items": [],
                "total": 0,
                "page": params.page,
                "page_size": params.page_size,
                "pages": 0,
                "message": "请选择商户以查看部门列表"
            }

        if params.name:
            return self._search_departments(params, current_user, target_merchant_id)
        else:
            return self._list_departments(params, current_user, target_merchant_id)

    def _search_departments(
        self, params: DepartmentQueryParams, current_user: User, target_merchant_id: int
    ) -> Dict[str, Any]:
        """搜索部门"""
        departments = self.dept_service.search_departments(
            target_merchant_id, params.name, current_user,
            skip=(params.page - 1) * params.page_size,
            limit=params.page_size
        )
        total = len(departments)
        return self._build_response(departments, total, params)

    def _list_departments(
        self, params: DepartmentQueryParams, current_user: User, target_merchant_id: int
    ) -> Dict[str, Any]:
        """列出部门"""
        all_departments = self.dept_service.get_merchant_departments(
            target_merchant_id, current_user, include_children=True
        )

        # 应用过滤条件
        filtered_departments = self._apply_filters(all_departments, params)

        # 分页
        total = len(filtered_departments)
        start = (params.page - 1) * params.page_size
        end = start + params.page_size
        departments = filtered_departments[start:end]

        return self._build_response(departments, total, params)

    def _apply_filters(self, departments: List, params: DepartmentQueryParams) -> List:
        """应用过滤条件"""
        filtered_departments = []
        for dept in departments:
            if params.parent_id is not None and dept.parent_id != params.parent_id:
                continue
            if params.dept_status is not None and dept.status != params.dept_status:
                continue
            filtered_departments.append(dept)
        return filtered_departments

    def _build_response(self, departments: List, total: int, params: DepartmentQueryParams) -> Dict[str, Any]:
        """构建响应数据"""
        if params.is_tree:
            tree_data = self.dept_service.build_department_tree(departments)
            # 直接返回数据，让中间件处理统一格式
            return {
                "tree": tree_data,
                "total": total,
                "page": params.page,
                "page_size": params.page_size,
                "pages": (total + params.page_size - 1) // params.page_size,
            }
        else:
            # 构建包含关联信息的部门数据
            items = []
            for dept in departments:
                dept_dict = dept.to_dict()

                # 添加商户名称
                if dept.merchant:
                    dept_dict["merchantName"] = dept.merchant.name
                else:
                    dept_dict["merchantName"] = "未知"

                # 添加上级部门名称
                if dept.parent:
                    dept_dict["parentName"] = dept.parent.name
                else:
                    dept_dict["parentName"] = "无"

                # 添加CK统计信息
                dept_dict.update(self._get_department_ck_stats(dept.id))

                items.append(dept_dict)

            # 直接返回数据，让中间件处理统一格式
            return {
                "items": items,
                "total": total,
                "page": params.page,
                "page_size": params.page_size,
                "pages": (total + params.page_size - 1) // params.page_size,
            }

    def _get_department_ck_stats(self, department_id: int) -> Dict[str, int]:
        """获取部门CK统计信息"""
        try:
            from app.models.walmart_ck import WalmartCK

            # 总CK数量
            total_ck_count = self.db.query(WalmartCK).filter(
                WalmartCK.department_id == department_id,
                WalmartCK.is_deleted == False
            ).count()

            # 可用CK数量
            available_ck_count = self.db.query(WalmartCK).filter(
                WalmartCK.department_id == department_id,
                WalmartCK.active == True,
                (
                    (WalmartCK.total_limit.is_(None)) |
                    (WalmartCK.bind_count < WalmartCK.total_limit)
                ),
                WalmartCK.is_deleted == False
            ).count()

            return {
                "total_ck_count": total_ck_count,
                "available_ck_count": available_ck_count
            }

        except Exception as e:
            logger.error(f"获取部门{department_id}的CK统计失败: {e}")
            return {
                "total_ck_count": 0,
                "available_ck_count": 0
            }


@router.get("", response_model=Dict[str, Any])
async def read_departments(
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_active_user),
    merchant_id: Optional[int] = Query(None, description="商户ID"),
    parent_id: Optional[int] = Query(None, description="父部门ID"),
    dept_status: Optional[bool] = Query(None, description="状态过滤"),
    name: Optional[str] = Query(None, description="部门名称搜索"),
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(10, ge=1, le=100, description="每页记录数"),
    is_tree: bool = Query(False, description="是否返回树形结构")
):
    """
    获取部门列表

    权限要求:
    - 超级管理员：可以查看所有商户的部门
    - 商户管理员：只能查看自己商户的部门
    - CK供应商：禁止访问部门列表API（应使用专用API获取自己的部门信息）
    """
    try:
        # 初始化服务
        dept_service = DepartmentService(db)
        permission_service = PermissionService(db)

        # 🔒 安全修复：CK供应商角色权限检查
        # CK供应商不应该访问部门列表API，应该使用专用的API获取自己的部门信息
        if _is_ck_supplier(current_user):
            logger.warning(f"CK供应商用户 {current_user.username} 尝试访问部门列表API，已拒绝")
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="CK供应商无权访问部门列表，请使用专用API获取部门信息",
            )

        # 权限检查
        has_permission = permission_service.check_user_permission(
            current_user, "api:departments:read"
        )
        if not has_permission:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="没有查看部门的权限",
            )

        # 【安全修复】：强制商户隔离检查
        security_service = SecurityService(db)
        if not current_user.is_superuser:
            if not current_user.merchant_id:
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail="用户没有商户信息，无法访问部门数据",
                )

            # 如果指定了merchant_id参数，检查是否匹配用户的商户
            if merchant_id and merchant_id != current_user.merchant_id:
                logger.warning(f"[SECURITY] 用户 {current_user.id} 尝试访问其他商户 {merchant_id} 的部门数据")
                security_service.log_security_violation(
                    current_user,
                    "CROSS_MERCHANT_DEPARTMENT_ACCESS_ATTEMPT",
                    f"尝试访问其他商户的部门数据",
                    {
                        "user_merchant_id": current_user.merchant_id,
                        "target_merchant_id": merchant_id,
                        "operation": "read",
                        "resource_type": "department"
                    }
                )
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail="无权访问其他商户的部门数据",
                )

        # 确定目标商户ID
        target_merchant_id = _determine_target_merchant_id(current_user, merchant_id)

        # 如果请求树形结构
        if is_tree:
            if target_merchant_id is None:
                # 超级管理员没有指定商户ID时，返回空的树形结构
                # 因为部门必须属于特定商户，无法跨商户显示树形结构
                return {
                    "items": [],
                    "total": 0,
                    "page": page,
                    "page_size": page_size,
                    "pages": 0,
                    "message": "请选择商户以查看部门树形结构"
                }

            tree_data = dept_service.get_department_tree(
                target_merchant_id, current_user, parent_id
            )
            # 返回字典格式，让中间件处理统一格式
            return {
                "items": tree_data,
                "total": len(tree_data),
                "page": page,
                "page_size": page_size,
                "pages": 1
            }

        # 构建查询参数
        query_params = DepartmentQueryParams(
            merchant_id=target_merchant_id,
            parent_id=parent_id,
            dept_status=dept_status,
            name=name,
            page=page,
            page_size=page_size,
            is_tree=is_tree
        )

        # 使用查询构建器处理查询
        query_builder = DepartmentQueryBuilder(dept_service, permission_service)
        return query_builder.build_query_result(query_params, current_user, target_merchant_id)

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取部门列表失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取部门列表失败: {str(e)}"
        )


def _is_ck_supplier(current_user: User) -> bool:
    """检查用户是否为CK供应商角色"""
    try:
        # 检查用户是否有CK供应商角色
        if hasattr(current_user, 'roles') and current_user.roles:
            for role in current_user.roles:
                if role.code == 'ck_supplier':
                    return True
        return False
    except Exception as e:
        logger.error(f"检查CK供应商角色失败: {e}")
        return False


def _determine_target_merchant_id(current_user: User, merchant_id: Optional[int]) -> Optional[int]:
    """确定目标商户ID"""
    if not current_user.is_superuser:
        # 非超级管理员只能查看自己商户的部门
        return current_user.merchant_id
    else:
        # 超级管理员可以指定商户ID查看特定商户的部门，
        # 或者不指定商户ID查看所有商户的部门
        return merchant_id


@router.post("", response_model=Dict[str, Any])
async def create_department(
    *,
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_active_user),
    department_in: DepartmentCreate
):
    """
    创建部门

    权限要求:
    - "department:create": 创建部门权限

    权限逻辑:
    - 超级管理员：必须在请求中指定merchant_id，可以为任意商户创建部门
    - 商户管理员/其他角色：自动使用当前用户的merchant_id，不需要在请求中传递
    """
    try:
        # 初始化服务
        dept_service = DepartmentService(db)
        permission_service = PermissionService(db)

        # 权限检查
        has_permission = permission_service.check_user_permission(
            current_user, "api:departments:create"
        )
        if not has_permission:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="没有创建部门的权限",
            )

        # 根据用户角色处理merchant_id
        if current_user.is_superuser:
            # 超级管理员必须指定merchant_id
            if department_in.merchant_id is None:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="超级管理员创建部门时必须指定商户ID",
                )
        else:
            # 非超级管理员使用自己的merchant_id
            if current_user.merchant_id is None:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="当前用户未关联商户，无法创建部门",
                )
            # 强制设置为当前用户的merchant_id，忽略请求中的值
            department_in.merchant_id = current_user.merchant_id

        # 创建部门
        department = dept_service.create_department(department_in, current_user)

        # 直接返回数据，让中间件处理统一格式
        return department.to_dict()

    except HTTPException:
        raise
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        logger.error(f"创建部门失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"创建部门失败: {str(e)}"
        )


@router.get("/check-name", response_model=Dict[str, Any])
async def check_department_name(
    name: str = Query(..., description="部门名称"),
    merchant_id: Optional[int] = Query(None, description="商户ID"),
    parent_id: Optional[int] = Query(None, description="父部门ID"),
    exclude_id: Optional[int] = Query(None, description="排除的部门ID（用于编辑时排除自己）"),
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_active_user),
):
    """
    检查部门名称是否重复

    权限要求:
    - 需要有部门相关权限
    """
    try:
        # 初始化服务
        dept_service = DepartmentService(db)

        # 确定目标商户ID
        target_merchant_id = _determine_target_merchant_id(current_user, merchant_id)
        if target_merchant_id is None:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="无法确定目标商户"
            )

        # 检查是否存在同名部门
        existing_dept = dept_service.get_by_name_and_parent(
            target_merchant_id, name, parent_id, exclude_id
        )

        return {
            "exists": existing_dept is not None,
            "department_id": existing_dept.id if existing_dept else None,
            "message": "部门名称可用" if not existing_dept else "同级部门中已存在此名称"
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"检查部门名称失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"检查部门名称失败: {str(e)}"
        )


# ========================================
# 具体路径的路由必须在参数化路径之前定义
# ========================================

@router.get("/binding-weight-stats", response_model=Dict[str, Any])
async def get_binding_weight_statistics(
    merchant_id: Optional[int] = Query(None, description="商户ID"),
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_active_user)
):
    """
    获取绑卡权重分配统计

    权限要求:
    - "api:departments:read": 查看部门权限
    """
    try:
        # 确定目标商户ID
        target_merchant_id = _determine_target_merchant_id(current_user, merchant_id)

        if target_merchant_id is None:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="请指定商户ID"
            )

        # 使用权重服务获取统计信息
        from app.services.department_weight_service import DepartmentWeightService

        weight_service = DepartmentWeightService(db)
        weight_stats = weight_service.get_weight_distribution_stats(target_merchant_id)
        validation_result = weight_service.validate_weight_configuration(target_merchant_id)

        return {
            "merchant_id": target_merchant_id,
            "weight_distribution": weight_stats,
            "validation_result": validation_result,
            "summary": {
                "total_departments": weight_stats['total_departments'],
                "total_weight": weight_stats['total_weight'],
                "is_valid_configuration": validation_result['is_valid'],
                "has_warnings": len(validation_result['warnings']) > 0
            }
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取绑卡权重统计失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取绑卡权重统计失败: {str(e)}"
        )


@router.get("/my-department", response_model=Dict[str, Any])
async def get_my_department(
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_active_user),
):
    """
    获取当前用户所属部门信息（CK供应商专用API）

    权限要求:
    - 仅限CK供应商角色使用
    - 返回用户所属的部门信息

    安全说明:
    - 此API专为CK供应商设计，避免其访问完整的部门列表
    - 严格限制只能获取自己所属的部门信息
    """
    try:
        # 初始化服务
        dept_service = DepartmentService(db)

        # 🔒 安全检查：仅允许CK供应商访问
        if not _is_ck_supplier(current_user):
            logger.warning(f"非CK供应商用户 {current_user.username} 尝试访问CK专用API")
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="此API仅限CK供应商使用",
            )

        # 获取用户所属部门
        user_department = dept_service.get_user_department(current_user.id)

        if not user_department:
            return {
                "success": True,
                "data": None,
                "message": "用户未分配部门"
            }

        # 返回部门信息（仅返回必要字段）
        return {
            "success": True,
            "data": {
                "id": user_department.id,
                "name": user_department.name,
                "code": user_department.code,
                "merchant_id": user_department.merchant_id,
                "parent_id": user_department.parent_id,
                "status": user_department.status,
                "description": user_department.description
            },
            "message": "获取部门信息成功"
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取用户部门信息失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取用户部门信息失败: {str(e)}"
        )


@router.post("/batch-binding-controls", response_model=DepartmentBindingControlsResponse)
async def batch_update_department_binding_controls(
    batch_update: DepartmentBindingControlsBatch,
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_active_user)
):
    """
    批量更新部门绑卡控制设置

    权限要求:
    - "api:departments:update": 编辑部门权限
    """
    try:
        dept_service = DepartmentService(db)

        updated_count = 0
        all_changes = {}

        for department_id in batch_update.department_ids:
            # 获取部门信息（包含权限检查）
            department = dept_service.get_with_isolation(department_id, current_user)
            if not department:
                logger.warning(f"跳过部门 {department_id}：不存在或无权限访问")
                continue

            # 更新绑卡控制设置
            changes = department.update_binding_controls(
                enable_binding=batch_update.enable_binding,
                binding_weight=batch_update.binding_weight
            )

            if changes:
                all_changes[str(department_id)] = {
                    'department_name': department.name,
                    'changes': changes
                }
                updated_count += 1

        # 保存所有更改
        if updated_count > 0:
            db.commit()

            # 记录详细操作日志
            from app.services.department_operation_log_service import DepartmentOperationLogService

            log_service = DepartmentOperationLogService(db)
            log_service.log_batch_operation(
                operation_type="batch_update_binding_controls",
                department_ids=batch_update.department_ids,
                operation_data=batch_update.model_dump(),
                operator=current_user,
                results={
                    "updated_count": updated_count,
                    "changes": all_changes
                },
                additional_context={
                    "api_endpoint": "POST /departments/batch-binding-controls"
                }
            )

        return DepartmentBindingControlsResponse(
            success=True,
            message=f"成功更新 {updated_count} 个部门的绑卡控制设置",
            updated_count=updated_count,
            changes=all_changes
        )

    except HTTPException:
        raise
    except Exception as e:
        db.rollback()
        logger.error(f"批量更新部门绑卡控制设置失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"批量更新部门绑卡控制设置失败: {str(e)}"
        )


@router.post("/test-weight-algorithm", response_model=Dict[str, Any])
async def test_weight_algorithm(
    merchant_id: Optional[int] = Query(None, description="商户ID"),
    test_count: int = Query(100, ge=10, le=1000, description="测试次数"),
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_active_user)
):
    """
    测试权重算法效果

    权限要求:
    - "api:departments:read": 查看部门权限
    """
    try:
        # 确定目标商户ID
        target_merchant_id = _determine_target_merchant_id(current_user, merchant_id)

        if target_merchant_id is None:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="请指定商户ID"
            )

        # 使用增强CK服务进行测试
        from app.services.enhanced_ck_service import EnhancedCKService

        ck_service = EnhancedCKService(db)
        test_result = await ck_service.test_weight_algorithm(
            merchant_id=target_merchant_id,
            test_count=test_count
        )

        # 记录测试操作日志
        from app.services.department_operation_log_service import DepartmentOperationLogService

        log_service = DepartmentOperationLogService(db)
        log_service.log_weight_algorithm_test(
            merchant_id=target_merchant_id,
            test_parameters={"test_count": test_count},
            test_results=test_result,
            operator=current_user,
            additional_context={
                "api_endpoint": "POST /departments/test-weight-algorithm"
            }
        )

        return {
            "merchant_id": target_merchant_id,
            "test_parameters": {
                "test_count": test_count
            },
            "test_result": test_result,
            "summary": {
                "total_departments": test_result.get('total_departments', 0),
                "test_completed": test_result.get('test_count', 0),
                "algorithm_accuracy": test_result.get('accuracy_score', 0)
            }
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"测试权重算法失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"测试权重算法失败: {str(e)}"
        )


@router.get("/{department_id}", response_model=Dict[str, Any])
async def read_department(
    department_id: int,
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_active_user),
):
    """
    获取部门详情

    权限要求:
    - "department:view": 查看部门权限
    """
    try:
        # 初始化服务
        dept_service = DepartmentService(db)
        permission_service = PermissionService(db)

        # 权限检查
        has_permission = permission_service.check_user_permission(
            current_user, "api:departments:read"
        )
        if not has_permission:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="没有查看部门的权限",
            )

        # 获取部门（应用数据隔离）
        department = dept_service.get_with_isolation(department_id, current_user)
        if not department:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="部门不存在或无权限访问"
            )

        # 获取部门详情（包含关联信息）
        department_detail = dept_service.get_department_detail(department_id, current_user)

        # 直接返回数据，让中间件处理统一格式
        return department_detail

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取部门详情失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取部门详情失败: {str(e)}"
        )


@router.get("/{department_id}/statistics", response_model=Dict[str, Any])
async def get_department_statistics(
    *,
    db: Session = Depends(deps.get_db),
    department_id: int,
    current_user: User = Depends(deps.get_current_active_user),
):
    """
    获取部门统计信息

    权限要求:
    - "department:read": 查看部门权限
    """
    try:
        # 初始化服务
        dept_service = DepartmentService(db)
        permission_service = PermissionService(db)

        # 权限检查
        has_permission = permission_service.check_user_permission(
            current_user, "api:departments:read"
        )
        if not has_permission:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="没有查看部门统计的权限",
            )

        # 获取部门详情（验证权限）
        department = dept_service.get_with_isolation(department_id, current_user)
        if not department:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="部门不存在或无权限访问"
            )

        # 获取部门统计信息
        statistics = dept_service.get_department_statistics(department_id, current_user)

        # 直接返回数据，让中间件处理统一格式
        return statistics

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取部门统计失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取部门统计失败: {str(e)}"
        )


@router.put("/{department_id}", response_model=Dict[str, Any])
async def update_department(
    department_id: int,
    *,
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_active_user),
    department_in: DepartmentUpdate
):
    """
    更新部门信息

    权限要求:
    - "department:update": 更新部门权限
    """
    try:
        # 初始化服务
        dept_service = DepartmentService(db)
        permission_service = PermissionService(db)

        # 权限检查
        has_permission = permission_service.check_user_permission(
            current_user, "api:departments:update"
        )
        if not has_permission:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="没有更新部门的权限",
            )

        # 更新部门
        department = dept_service.update_department(department_id, department_in, current_user)
        if not department:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="部门不存在或无权限访问"
            )

        # 直接返回数据，让中间件处理统一格式
        return department.to_dict()

    except HTTPException:
        raise
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        logger.error(f"更新部门失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"更新部门失败: {str(e)}"
        )


@router.delete("/{department_id}", response_model=Dict[str, Any])
async def delete_department(
    department_id: int,
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_active_user),
):
    """
    删除部门

    权限要求:
    - "department:delete": 删除部门权限
    """
    try:
        # 初始化服务
        dept_service = DepartmentService(db)
        permission_service = PermissionService(db)

        # 权限检查
        has_permission = permission_service.check_user_permission(
            current_user, "api:departments:delete"
        )
        if not has_permission:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="没有删除部门的权限",
            )

        # 删除部门
        success = dept_service.delete_department(department_id, current_user)
        if not success:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="部门不存在或无权限访问"
            )

        # 直接返回数据，让中间件处理统一格式
        return {"message": "删除部门成功"}

    except HTTPException:
        raise
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        logger.error(f"删除部门失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"删除部门失败: {str(e)}"
        )


@router.post("/{department_id}/move", response_model=Dict[str, Any])
async def move_department(
    department_id: int,
    *,
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_active_user),
    new_parent_id: Optional[int] = None
):
    """
    移动部门到新的父部门下

    权限要求:
    - "department:update": 更新部门权限
    """
    try:
        # 初始化服务
        dept_service = DepartmentService(db)
        permission_service = PermissionService(db)

        # 权限检查
        has_permission = permission_service.check_user_permission(
            current_user, "api:departments:update"
        )
        if not has_permission:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="没有移动部门的权限",
            )

        # 移动部门
        department = dept_service.move_department(department_id, new_parent_id, current_user)
        if not department:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="部门不存在或无权限访问"
            )

        # 直接返回数据，让中间件处理统一格式
        return department.to_dict()

    except HTTPException:
        raise
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        logger.error(f"移动部门失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"移动部门失败: {str(e)}"
        )


@router.get("/{department_id}/users", response_model=Dict[str, Any])
async def get_department_users(
    department_id: int,
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_active_user),
    include_children: bool = Query(False, description="是否包含子部门用户")
):
    """
    获取部门用户列表

    权限要求:
    - "department:view": 查看部门权限
    """
    try:
        # 初始化服务
        dept_service = DepartmentService(db)
        permission_service = PermissionService(db)

        # 权限检查
        has_permission = permission_service.check_user_permission(
            current_user, "api:departments:read"
        )
        if not has_permission:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="没有查看部门的权限",
            )

        # 获取部门用户
        users = dept_service.get_department_users(department_id, current_user, include_children)

        # 直接返回数据，让中间件处理统一格式
        return {
            "items": [user.to_dict() for user in users],
            "total": len(users)
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取部门用户失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取部门用户失败: {str(e)}"
        )



# ========================================
# 部门绑卡控制相关API端点
# ========================================

@router.get("/{department_id}/binding-status", response_model=DepartmentBindingStatus)
async def get_department_binding_status(
    department_id: int,
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_active_user)
):
    """
    获取部门绑卡状态

    权限要求:
    - "api:departments:read": 查看部门权限
    """
    try:
        dept_service = DepartmentService(db)

        # 获取部门信息（包含权限检查）
        department = dept_service.get_with_isolation(department_id, current_user)
        if not department:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="部门不存在或无权限访问"
            )

        # 统计CK信息
        from app.models.walmart_ck import WalmartCK

        total_ck_count = db.query(WalmartCK).filter(
            WalmartCK.department_id == department_id,
            WalmartCK.is_deleted == False
        ).count()

        available_ck_count = db.query(WalmartCK).filter(
            WalmartCK.department_id == department_id,
            WalmartCK.active == True,
            (
                (WalmartCK.total_limit.is_(None)) |
                (WalmartCK.bind_count < WalmartCK.total_limit)
            ),
            WalmartCK.is_deleted == False
        ).count()

        return DepartmentBindingStatus(
            id=department.id,
            name=department.name,
            merchant_id=department.merchant_id,
            enable_binding=department.enable_binding,
            binding_weight=department.binding_weight,
            can_bind_cards=department.can_bind_cards,
            available_ck_count=available_ck_count,
            total_ck_count=total_ck_count
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取部门绑卡状态失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取部门绑卡状态失败: {str(e)}"
        )


@router.put("/{department_id}/binding-controls", response_model=DepartmentBindingControlsResponse)
async def update_department_binding_controls(
    department_id: int,
    controls_update: DepartmentBindingControlsUpdate,
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_active_user)
):
    """
    更新部门绑卡控制设置

    权限要求:
    - "api:departments:update": 编辑部门权限
    """
    try:
        dept_service = DepartmentService(db)

        # 获取部门信息（包含权限检查）
        department = dept_service.get_with_isolation(department_id, current_user)
        if not department:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="部门不存在或无权限访问"
            )

        # 更新绑卡控制设置
        changes = department.update_binding_controls(
            enable_binding=controls_update.enable_binding,
            binding_weight=controls_update.binding_weight
        )

        if not changes:
            return DepartmentBindingControlsResponse(
                success=True,
                message="没有需要更新的设置",
                updated_count=0,
                changes={}
            )

        # 保存更改
        db.commit()

        # 记录详细操作日志
        from app.services.department_operation_log_service import DepartmentOperationLogService

        log_service = DepartmentOperationLogService(db)
        log_service.log_binding_controls_change(
            department=department,
            changes=changes,
            operator=current_user,
            operation_type="update_binding_controls",
            additional_context={
                "api_endpoint": f"PUT /departments/{department_id}/binding-controls",
                "request_data": controls_update.model_dump()
            }
        )

        return DepartmentBindingControlsResponse(
            success=True,
            message="绑卡控制设置更新成功",
            updated_count=1,
            changes=changes
        )

    except HTTPException:
        raise
    except Exception as e:
        db.rollback()
        logger.error(f"更新部门绑卡控制设置失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"更新部门绑卡控制设置失败: {str(e)}"
        )





