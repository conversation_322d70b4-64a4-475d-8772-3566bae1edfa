#!/usr/bin/env python3
"""
分页功能修复验证测试脚本

测试所有模块的分页功能是否正确工作：
1. 用户管理分页
2. 商户管理分页
3. CK管理分页
4. 绑卡记录分页
5. 通知中心分页
6. 部门管理分页
"""

import asyncio
import sys
import os
import requests
import json
from typing import Dict, Any, Optional
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

class PaginationTester:
    """分页功能测试器"""
    
    def __init__(self, base_url: str = "http://localhost:8000"):
        self.base_url = base_url
        self.admin_token = None
        self.merchant_token = None
        self.test_results = []
        
    def login(self, username: str, password: str) -> Optional[str]:
        """登录获取token"""
        try:
            response = requests.post(
                f"{self.base_url}/api/v1/auth/login",
                data={"username": username, "password": password},
                headers={"Content-Type": "application/x-www-form-urlencoded"}
            )
            if response.status_code == 200:
                data = response.json()
                return data.get("data", {}).get("access_token")
            else:
                print(f"登录失败: {response.status_code} - {response.text}")
                return None
        except Exception as e:
            print(f"登录异常: {e}")
            return None
    
    def make_request(self, method: str, endpoint: str, token: str, params: Dict = None) -> tuple:
        """发送HTTP请求"""
        try:
            headers = {"Authorization": f"Bearer {token}"}
            url = f"{self.base_url}/api/v1{endpoint}"
            
            if method.upper() == "GET":
                response = requests.get(url, headers=headers, params=params)
            else:
                response = requests.request(method, url, headers=headers, json=params)
                
            return response.status_code, response.json() if response.content else {}
        except Exception as e:
            return 500, {"error": str(e)}
    
    def test_pagination_module(self, module_name: str, endpoint: str, token: str, 
                             test_page_sizes: list = [10, 20, 50, 100]) -> Dict[str, Any]:
        """测试单个模块的分页功能"""
        print(f"\n=== 测试 {module_name} 分页功能 ===")
        
        results = {
            "module": module_name,
            "endpoint": endpoint,
            "tests": [],
            "success": True,
            "errors": []
        }
        
        for page_size in test_page_sizes:
            print(f"  测试每页 {page_size} 条数据...")
            
            # 测试第一页
            status_code, response = self.make_request(
                "GET", endpoint, token, 
                {"page": 1, "page_size": page_size}
            )
            
            if status_code != 200:
                error_msg = f"请求失败: {status_code}"
                print(f"    ❌ {error_msg}")
                results["errors"].append(error_msg)
                results["success"] = False
                continue
                
            data = response.get("data", {})
            total = data.get("total", 0)
            items = data.get("items", [])
            page = data.get("page", 1)
            returned_page_size = data.get("page_size", page_size)
            pages = data.get("pages", 0)
            
            # 验证分页数据
            test_result = {
                "page_size": page_size,
                "total": total,
                "returned_items": len(items),
                "expected_items": min(page_size, total),
                "pages": pages,
                "calculated_pages": (total + page_size - 1) // page_size if total > 0 else 0,
                "page": page,
                "success": True,
                "issues": []
            }
            
            # 检查返回的数据条数是否正确
            expected_items = min(page_size, total)
            if len(items) != expected_items:
                issue = f"返回数据条数错误: 期望 {expected_items}, 实际 {len(items)}"
                test_result["issues"].append(issue)
                test_result["success"] = False
                print(f"    ❌ {issue}")
            
            # 检查页数计算是否正确
            calculated_pages = (total + page_size - 1) // page_size if total > 0 else 0
            if pages != calculated_pages:
                issue = f"总页数计算错误: 期望 {calculated_pages}, 实际 {pages}"
                test_result["issues"].append(issue)
                test_result["success"] = False
                print(f"    ❌ {issue}")
            
            # 检查page_size是否正确返回
            if returned_page_size != page_size:
                issue = f"page_size返回错误: 期望 {page_size}, 实际 {returned_page_size}"
                test_result["issues"].append(issue)
                test_result["success"] = False
                print(f"    ❌ {issue}")
            
            if test_result["success"]:
                print(f"    ✅ 每页 {page_size} 条: 总计 {total} 条, {pages} 页, 返回 {len(items)} 条")
            
            results["tests"].append(test_result)
            
            if not test_result["success"]:
                results["success"] = False
        
        return results
    
    def test_page_navigation(self, module_name: str, endpoint: str, token: str) -> Dict[str, Any]:
        """测试翻页功能"""
        print(f"\n=== 测试 {module_name} 翻页功能 ===")
        
        # 先获取总数据量
        status_code, response = self.make_request("GET", endpoint, token, {"page": 1, "page_size": 10})
        if status_code != 200:
            return {"success": False, "error": "无法获取数据"}
        
        data = response.get("data", {})
        total = data.get("total", 0)
        
        if total <= 10:
            print(f"  数据量太少 ({total} 条), 跳过翻页测试")
            return {"success": True, "skipped": True, "reason": "数据量不足"}
        
        # 测试第二页
        status_code, response = self.make_request("GET", endpoint, token, {"page": 2, "page_size": 10})
        if status_code != 200:
            return {"success": False, "error": "第二页请求失败"}
        
        data = response.get("data", {})
        items = data.get("items", [])
        page = data.get("page", 0)
        
        if page != 2:
            return {"success": False, "error": f"页码错误: 期望 2, 实际 {page}"}
        
        expected_items = min(10, total - 10)
        if len(items) != expected_items:
            return {"success": False, "error": f"第二页数据条数错误: 期望 {expected_items}, 实际 {len(items)}"}
        
        print(f"  ✅ 翻页功能正常: 第二页返回 {len(items)} 条数据")
        return {"success": True}
    
    def run_all_tests(self):
        """运行所有分页测试"""
        print("🧪 开始分页功能修复验证测试")
        print("=" * 60)
        
        # 登录获取token
        print("正在登录...")
        self.admin_token = self.login("admin", "7c222fb2927d828af22f592134e8932480637c0d")
        if not self.admin_token:
            print("❌ 管理员登录失败")
            return

        print("✅ 管理员登录成功")

        # 只使用管理员token进行测试，因为管理员有所有权限
        self.merchant_token = self.admin_token
        
        print("✅ 所有登录成功")
        
        # 测试模块列表
        test_modules = [
            ("用户管理", "/users", self.admin_token),
            ("商户管理", "/merchants", self.admin_token),
            ("CK管理", "/walmart-ck", self.admin_token),
            ("绑卡记录", "/cards", self.admin_token),
            ("通知中心", "/notifications", self.admin_token),
            ("部门管理", "/departments", self.admin_token),
        ]
        
        all_success = True
        
        for module_name, endpoint, token in test_modules:
            try:
                # 测试基本分页功能
                result = self.test_pagination_module(module_name, endpoint, token)
                self.test_results.append(result)
                
                if not result["success"]:
                    all_success = False
                
                # 测试翻页功能
                nav_result = self.test_page_navigation(module_name, endpoint, token)
                if not nav_result.get("success", False):
                    all_success = False
                    
            except Exception as e:
                print(f"❌ {module_name} 测试异常: {e}")
                all_success = False
        
        # 打印测试总结
        self.print_test_summary(all_success)
    
    def print_test_summary(self, all_success: bool):
        """打印测试总结"""
        print("\n" + "=" * 60)
        print("📊 分页功能测试总结")
        print("=" * 60)
        
        for result in self.test_results:
            module = result["module"]
            success = result["success"]
            status = "✅ 通过" if success else "❌ 失败"
            print(f"{module:12} | {status}")
            
            if not success:
                for error in result["errors"]:
                    print(f"             | 错误: {error}")
                
                for test in result["tests"]:
                    if not test["success"]:
                        for issue in test["issues"]:
                            print(f"             | 问题: {issue}")
        
        print("=" * 60)
        if all_success:
            print("🎉 所有分页功能测试通过！")
        else:
            print("⚠️  部分分页功能存在问题，需要进一步修复")

if __name__ == "__main__":
    tester = PaginationTester()
    tester.run_all_tests()
