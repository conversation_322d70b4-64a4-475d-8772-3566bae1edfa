<template>
  <el-dialog title="部门绑卡控制设置" :model-value="visible" @update:model-value="updateVisible" width="900px"
    :before-close="handleClose">
    <div class="department-binding-controls">
      <!-- 部门绑卡状态卡片 -->
      <el-card class="status-card" shadow="hover">
        <template #header>
          <div class="card-header">
            <span class="title">部门绑卡控制</span>
            <el-button type="primary" size="small" @click="refreshStatus" :loading="loading">
              刷新状态
            </el-button>
          </div>
        </template>

        <div v-if="bindingStatus" class="status-content">
          <div class="status-row">
            <div class="status-item">
              <span class="label">部门名称：</span>
              <span class="value">{{ bindingStatus.name }}</span>
            </div>
            <div class="status-item">
              <span class="label">绑卡状态：</span>
              <el-tag :type="bindingStatus.can_bind_cards ? 'success' : 'danger'" size="small">
                {{ bindingStatus.can_bind_cards ? '可绑卡' : '不可绑卡' }}
              </el-tag>
            </div>
          </div>

          <div class="status-row">
            <div class="status-item">
              <span class="label">进单开关：</span>
              <el-switch v-model="bindingStatus.enable_binding" @change="handleSwitchChange" :loading="switchLoading"
                active-text="启用" inactive-text="禁用" />
            </div>
            <div class="status-item">
              <span class="label">进单权重：</span>
              <el-input-number v-model="bindingStatus.binding_weight" :min="0" :max="10000" :step="10" size="small"
                style="width: 120px" @change="handleWeightChange" />
            </div>
          </div>

          <div class="status-row">
            <div class="status-item">
              <span class="label">可用CK数量：</span>
              <span class="value highlight">{{ bindingStatus.available_ck_count }}</span>
            </div>
            <div class="status-item">
              <span class="label">总CK数量：</span>
              <span class="value">{{ bindingStatus.total_ck_count }}</span>
            </div>
          </div>
        </div>

        <div v-else class="loading-content">
          <el-skeleton :rows="3" animated />
        </div>
      </el-card>

      <!-- 权重统计图表 -->
      <el-card class="chart-card" shadow="hover" v-if="showWeightStats">
        <template #header>
          <div class="card-header">
            <span class="title">权重分配统计</span>
            <el-button type="text" size="small" @click="toggleWeightStats">
              {{ showWeightStats ? '隐藏' : '显示' }}
            </el-button>
          </div>
        </template>

        <div class="weight-stats" v-if="weightStats">
          <div class="stats-summary">
            <div class="summary-item">
              <span class="number">{{ weightStats.total_departments }}</span>
              <span class="label">启用部门</span>
            </div>
            <div class="summary-item">
              <span class="number">{{ weightStats.total_weight }}</span>
              <span class="label">总权重</span>
            </div>
          </div>

          <div class="departments-list">
            <div v-for="dept in weightStats.departments" :key="dept.id" class="department-item"
              :class="{ 'current': dept.id === departmentId }">
              <div class="dept-info">
                <span class="dept-name">{{ dept.name }}</span>
                <span class="dept-weight">权重: {{ dept.binding_weight }}</span>
              </div>
              <div class="dept-stats">
                <span class="percentage">{{ dept.weight_percentage }}%</span>
                <div class="progress-bar">
                  <div class="progress-fill" :style="{ width: dept.weight_percentage + '%' }"></div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </el-card>

      <!-- 其他操作按钮 -->
      <div class="action-buttons">
        <el-button @click="resetChanges" :disabled="!hasChanges">
          重置
        </el-button>
        <el-button type="info" @click="testWeightAlgorithm">
          测试权重算法
        </el-button>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
        <el-button type="primary" @click="saveChanges" :loading="saveLoading" :disabled="!hasChanges">
          保存更改
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script>
import { departmentApi } from '@/api/modules/department'

export default {
  name: 'DepartmentBindingControls',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    departmentId: {
      type: Number,
      required: true
    }
  },
  emits: ['update:visible', 'updated'],
  data() {
    return {
      loading: false,
      switchLoading: false,
      saveLoading: false,
      bindingStatus: null,
      originalStatus: null,
      weightStats: null,
      showWeightStats: false,
      hasChanges: false
    }
  },
  mounted() {
    this.loadBindingStatus()
  },
  watch: {
    departmentId: {
      handler() {
        this.loadBindingStatus()
      },
      immediate: true
    }
  },
  methods: {
    async loadBindingStatus() {
      try {
        this.loading = true
        const response = await departmentApi.getBindingStatus(this.departmentId)
        this.bindingStatus = response
        this.originalStatus = JSON.parse(JSON.stringify(response))
        this.hasChanges = false
      } catch (error) {
        this.$message.error('获取部门绑卡状态失败：' + error.message)
      } finally {
        this.loading = false
      }
    },

    async refreshStatus() {
      await this.loadBindingStatus()
      if (this.showWeightStats) {
        await this.loadWeightStats()
      }
      this.$message.success('状态已刷新')
    },

    async handleSwitchChange() {
      this.switchLoading = true
      try {
        // 立即更新UI，但不保存到服务器
        this.checkChanges()
        this.$message.info('请点击"保存更改"按钮确认修改')
      } finally {
        this.switchLoading = false
      }
    },

    handleWeightChange() {
      this.checkChanges()
      this.$message.info('请点击"保存更改"按钮确认修改')
    },

    checkChanges() {
      this.hasChanges = (
        this.bindingStatus.enable_binding !== this.originalStatus.enable_binding ||
        this.bindingStatus.binding_weight !== this.originalStatus.binding_weight
      )
    },

    async saveChanges() {
      try {
        this.saveLoading = true

        const updateData = {
          enable_binding: this.bindingStatus.enable_binding,
          binding_weight: this.bindingStatus.binding_weight
        }

        const response = await departmentApi.updateBindingControls(
          this.departmentId,
          updateData
        )

        if (response.success) {
          this.$message.success('绑卡控制设置保存成功')
          this.originalStatus = JSON.parse(JSON.stringify(this.bindingStatus))
          this.hasChanges = false

          // 刷新状态
          await this.loadBindingStatus()

          // 触发父组件更新
          this.$emit('updated', this.bindingStatus)
        } else {
          this.$message.error('保存失败：' + response.message)
        }
      } catch (error) {
        this.$message.error('保存失败：' + error.message)
      } finally {
        this.saveLoading = false
      }
    },

    resetChanges() {
      this.bindingStatus = JSON.parse(JSON.stringify(this.originalStatus))
      this.hasChanges = false
      this.$message.info('已重置为原始值')
    },

    async toggleWeightStats() {
      this.showWeightStats = !this.showWeightStats
      if (this.showWeightStats && !this.weightStats) {
        await this.loadWeightStats()
      }
    },

    async loadWeightStats() {
      try {
        // 检查是否有部门绑卡状态信息
        if (!this.bindingStatus || !this.bindingStatus.merchant_id) {
          this.$message.error('无法获取商户信息，请刷新页面重试')
          return
        }

        const response = await departmentApi.getBindingWeightStats({
          merchant_id: this.bindingStatus.merchant_id
        })
        this.weightStats = response.weight_distribution
      } catch (error) {
        this.$message.error('获取权重统计失败：' + error.message)
      }
    },

    async testWeightAlgorithm() {
      try {
        // 检查是否有部门绑卡状态信息
        if (!this.bindingStatus || !this.bindingStatus.merchant_id) {
          this.$message.error('无法获取商户信息，请刷新页面重试')
          return
        }

        const response = await departmentApi.testWeightAlgorithm({
          merchant_id: this.bindingStatus.merchant_id,
          test_count: 100
        })

        this.$alert(
          `测试完成！共进行 ${response.test_result.test_count} 次测试，涉及 ${response.test_result.total_departments} 个部门。`,
          '权重算法测试结果',
          {
            confirmButtonText: '查看详情',
            callback: () => {
              this.showTestResults(response.test_result)
            }
          }
        )
      } catch (error) {
        this.$message.error('测试权重算法失败：' + error.message)
      }
    },

    showTestResults(testResult) {
      // 这里可以打开一个详细的测试结果对话框
      console.log('测试结果:', testResult)
      this.$message.info('详细结果已输出到控制台')
    },

    updateVisible(value) {
      this.$emit('update:visible', value)
    },

    handleClose() {
      this.$emit('update:visible', false)
    }
  }
}
</script>

<style scoped>
.department-binding-controls {
  padding: 20px;
}

.status-card,
.chart-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.title {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.status-content {
  padding: 10px 0;
}

.status-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 15px;
}

.status-item {
  display: flex;
  align-items: center;
  flex: 1;
}

.label {
  color: #606266;
  margin-right: 8px;
  min-width: 80px;
}

.value {
  color: #303133;
  font-weight: 500;
}

.value.highlight {
  color: #409EFF;
  font-weight: 600;
}

.loading-content {
  padding: 20px 0;
}

.weight-stats {
  padding: 10px 0;
}

.stats-summary {
  display: flex;
  justify-content: space-around;
  margin-bottom: 20px;
  padding: 15px;
  background: #f5f7fa;
  border-radius: 6px;
}

.summary-item {
  text-align: center;
}

.summary-item .number {
  display: block;
  font-size: 24px;
  font-weight: 600;
  color: #409EFF;
  margin-bottom: 5px;
}

.summary-item .label {
  color: #606266;
  font-size: 12px;
}

.departments-list {
  max-height: 300px;
  overflow-y: auto;
}

.department-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px;
  border: 1px solid #EBEEF5;
  border-radius: 4px;
  margin-bottom: 8px;
  transition: all 0.3s;
}

.department-item:hover {
  border-color: #409EFF;
  background: #f0f9ff;
}

.department-item.current {
  border-color: #409EFF;
  background: #ecf5ff;
}

.dept-info {
  flex: 1;
}

.dept-name {
  font-weight: 500;
  color: #303133;
  margin-right: 10px;
}

.dept-weight {
  color: #909399;
  font-size: 12px;
}

.dept-stats {
  display: flex;
  align-items: center;
  min-width: 120px;
}

.percentage {
  color: #409EFF;
  font-weight: 600;
  margin-right: 10px;
  min-width: 40px;
  text-align: right;
}

.progress-bar {
  width: 60px;
  height: 6px;
  background: #EBEEF5;
  border-radius: 3px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: #409EFF;
  transition: width 0.3s;
}

.action-buttons {
  display: flex;
  justify-content: center;
  gap: 10px;
  padding: 20px 0;
}
</style>
