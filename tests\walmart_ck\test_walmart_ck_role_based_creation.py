"""
沃尔玛CK角色权限创建测试
测试不同角色用户创建CK的权限和数据隔离
"""

import sys
import os
import json
import requests
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from test.base_test import BaseAPITest, format_test_result


class WalmartCKRoleBasedCreationTest(BaseAPITest):
    """沃尔玛CK角色权限创建测试类"""

    def __init__(self):
        super().__init__()
        self.test_name = "沃尔玛CK角色权限创建测试"
        self.results = []

    def generate_test_ck_data(self, suffix=""):
        """生成测试CK数据"""
        timestamp = datetime.now().strftime("%Y%m%d%H%M%S")
        return {
            "sign": f"25487f6f129649999ef6b1f269b2a1f{suffix}@d0e0e25d37720f856a3ba753089b1e47#mmb5Lz2g3i4ilzn/kHXpFg==#26",
            "merchant_id": 1,  # 将被后端根据用户角色调整
            "department_id": 1,  # 将被后端根据用户角色调整
            "daily_limit": 20,
            "hourly_limit": 10,
            "active": 1,
            "description": f"测试CK-{timestamp}{suffix}"
        }

    def test_super_admin_create_ck(self):
        """测试超级管理员创建CK"""
        print("\n=== 测试超级管理员创建CK ===")

        # 生成测试数据
        test_data = self.generate_test_ck_data("_admin")
        
        # 超级管理员必须提供merchant_id和department_id
        test_data["merchant_id"] = 1
        test_data["department_id"] = 1

        # 测试超级管理员创建CK
        status_code, response = self.make_request("POST", "/walmart-ck", self.admin_token, data=test_data)

        if status_code == 200 or status_code == 201:
            self.results.append(format_test_result(
                "超级管理员创建沃尔玛CK",
                True,
                "超级管理员成功创建沃尔玛CK"
            ))
            print("✅ 超级管理员成功创建沃尔玛CK")
            
            # 保存创建的CK ID用于后续清理
            if response and 'data' in response and 'id' in response['data']:
                self.created_ck_ids.append(response['data']['id'])
        else:
            self.results.append(format_test_result(
                "超级管理员创建沃尔玛CK",
                False,
                f"超级管理员创建沃尔玛CK失败，状态码: {status_code}, 响应: {response}"
            ))
            print(f"❌ 超级管理员创建沃尔玛CK失败，状态码: {status_code}")

    def test_super_admin_create_ck_without_merchant(self):
        """测试超级管理员创建CK时不提供merchant_id和department_id"""
        print("\n=== 测试超级管理员创建CK时缺少必填字段 ===")

        # 生成测试数据，但不提供merchant_id和department_id
        test_data = self.generate_test_ck_data("_admin_no_merchant")
        test_data.pop("merchant_id", None)
        test_data.pop("department_id", None)

        # 测试超级管理员创建CK（应该失败）
        status_code, response = self.make_request("POST", "/walmart-ck", self.admin_token, data=test_data)

        if status_code == 400:
            self.results.append(format_test_result(
                "超级管理员创建CK缺少必填字段验证",
                True,
                "正确拒绝了缺少merchant_id和department_id的请求"
            ))
            print("✅ 正确拒绝了缺少merchant_id和department_id的请求")
        else:
            self.results.append(format_test_result(
                "超级管理员创建CK缺少必填字段验证",
                False,
                f"应该返回400错误，实际状态码: {status_code}"
            ))
            print(f"❌ 应该返回400错误，实际状态码: {status_code}")

    def test_merchant_admin_create_ck(self):
        """测试商户管理员创建CK"""
        print("\n=== 测试商户管理员创建CK ===")

        # 生成测试数据
        test_data = self.generate_test_ck_data("_merchant")
        
        # 商户管理员提供的merchant_id和department_id会被后端忽略，使用用户自己的
        test_data["merchant_id"] = 999  # 这个值会被后端忽略
        test_data["department_id"] = 999  # 这个值会被后端忽略

        # 测试商户管理员创建CK
        status_code, response = self.make_request("POST", "/walmart-ck", self.merchant_token, data=test_data)

        if status_code == 200 or status_code == 201:
            self.results.append(format_test_result(
                "商户管理员创建沃尔玛CK",
                True,
                "商户管理员成功创建沃尔玛CK"
            ))
            print("✅ 商户管理员成功创建沃尔玛CK")
            
            # 验证创建的CK使用的是用户自己的merchant_id和department_id
            if response and 'data' in response:
                ck_data = response['data']
                if 'merchant_id' in ck_data and 'department_id' in ck_data:
                    print(f"   创建的CK merchant_id: {ck_data['merchant_id']}, department_id: {ck_data['department_id']}")
                
                # 保存创建的CK ID用于后续清理
                if 'id' in ck_data:
                    self.created_ck_ids.append(ck_data['id'])
        else:
            self.results.append(format_test_result(
                "商户管理员创建沃尔玛CK",
                False,
                f"商户管理员创建沃尔玛CK失败，状态码: {status_code}, 响应: {response}"
            ))
            print(f"❌ 商户管理员创建沃尔玛CK失败，状态码: {status_code}")

    def test_ck_supplier_create_ck(self):
        """测试CK供应商创建CK"""
        print("\n=== 测试CK供应商创建CK ===")

        # 生成测试数据
        test_data = self.generate_test_ck_data("_supplier")
        
        # CK供应商提供的merchant_id和department_id会被后端忽略，使用用户自己的
        test_data["merchant_id"] = 999  # 这个值会被后端忽略
        test_data["department_id"] = 999  # 这个值会被后端忽略

        # 测试CK供应商创建CK
        status_code, response = self.make_request("POST", "/walmart-ck", self.ck_supplier_token, data=test_data)

        if status_code == 200 or status_code == 201:
            self.results.append(format_test_result(
                "CK供应商创建沃尔玛CK",
                True,
                "CK供应商成功创建沃尔玛CK"
            ))
            print("✅ CK供应商成功创建沃尔玛CK")
            
            # 验证创建的CK使用的是用户自己的merchant_id和department_id
            if response and 'data' in response:
                ck_data = response['data']
                if 'merchant_id' in ck_data and 'department_id' in ck_data:
                    print(f"   创建的CK merchant_id: {ck_data['merchant_id']}, department_id: {ck_data['department_id']}")
                
                # 保存创建的CK ID用于后续清理
                if 'id' in ck_data:
                    self.created_ck_ids.append(ck_data['id'])
        else:
            self.results.append(format_test_result(
                "CK供应商创建沃尔玛CK",
                False,
                f"CK供应商创建沃尔玛CK失败，状态码: {status_code}, 响应: {response}"
            ))
            print(f"❌ CK供应商创建沃尔玛CK失败，状态码: {status_code}")

    def test_data_isolation(self):
        """测试数据隔离"""
        print("\n=== 测试数据隔离 ===")

        # 商户管理员获取CK列表，应该只能看到自己商户的CK
        status_code, response = self.make_request("GET", "/walmart-ck", self.merchant_token)

        if status_code == 200:
            if response and 'data' in response and 'items' in response['data']:
                items = response['data']['items']
                # 验证所有CK都属于当前用户的商户
                all_belong_to_merchant = all(
                    item.get('merchant_id') == 1 for item in items  # 假设测试商户ID为1
                )
                
                if all_belong_to_merchant:
                    self.results.append(format_test_result(
                        "商户数据隔离验证",
                        True,
                        f"商户管理员只能看到自己商户的{len(items)}个CK"
                    ))
                    print(f"✅ 商户管理员只能看到自己商户的{len(items)}个CK")
                else:
                    self.results.append(format_test_result(
                        "商户数据隔离验证",
                        False,
                        "发现跨商户数据泄露"
                    ))
                    print("❌ 发现跨商户数据泄露")
            else:
                self.results.append(format_test_result(
                    "商户数据隔离验证",
                    True,
                    "商户管理员CK列表为空"
                ))
                print("✅ 商户管理员CK列表为空")
        else:
            self.results.append(format_test_result(
                "商户数据隔离验证",
                False,
                f"获取CK列表失败，状态码: {status_code}"
            ))
            print(f"❌ 获取CK列表失败，状态码: {status_code}")

    def cleanup_created_cks(self):
        """清理测试创建的CK"""
        print("\n=== 清理测试数据 ===")
        
        for ck_id in self.created_ck_ids:
            try:
                status_code, _ = self.make_request("DELETE", f"/walmart-ck/{ck_id}", self.admin_token)
                if status_code in [200, 204]:
                    print(f"✅ 成功删除测试CK {ck_id}")
                else:
                    print(f"⚠️ 删除测试CK {ck_id} 失败，状态码: {status_code}")
            except Exception as e:
                print(f"⚠️ 删除测试CK {ck_id} 异常: {e}")

    def run_all_tests(self):
        """运行所有测试"""
        print(f"\n{'='*50}")
        print(f"开始执行: {self.test_name}")
        print(f"{'='*50}")

        # 初始化创建的CK ID列表
        self.created_ck_ids = []

        try:
            # 执行各项测试
            self.test_super_admin_create_ck()
            self.test_super_admin_create_ck_without_merchant()
            self.test_merchant_admin_create_ck()
            self.test_ck_supplier_create_ck()
            self.test_data_isolation()

        finally:
            # 清理测试数据
            self.cleanup_created_cks()

        # 输出测试结果
        self.print_test_results()
        return self.results


if __name__ == "__main__":
    test = WalmartCKRoleBasedCreationTest()
    results = test.run_all_tests()
    
    # 统计测试结果
    total_tests = len(results)
    passed_tests = sum(1 for result in results if result["status"] == "PASS")
    
    print(f"\n{'='*50}")
    print(f"测试完成: {passed_tests}/{total_tests} 通过")
    print(f"{'='*50}")
    
    # 如果有测试失败，退出码为1
    if passed_tests < total_tests:
        sys.exit(1)
