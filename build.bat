@echo off
REM Walmart绑卡回调通知服务构建脚本 (Windows)

echo 🦀 开始构建 Walmart 绑卡回调通知服务 (Rust版本)

REM 检查Rust环境
where cargo >nul 2>nul
if %ERRORLEVEL% NEQ 0 (
    echo ❌ 错误: 未找到 Cargo，请先安装 Rust
    exit /b 1
)

echo ✅ Rust 环境检查通过

REM 清理之前的构建
echo 🧹 清理之前的构建...
cargo clean

REM 检查代码格式
echo 📝 检查代码格式...
where rustfmt >nul 2>nul
if %ERRORLEVEL% EQU 0 (
    cargo fmt --check
    if %ERRORLEVEL% NEQ 0 (
        echo ⚠️  代码格式不符合标准，正在自动格式化...
        cargo fmt
    )
)

REM 运行 Clippy 检查
echo 🔍 运行 Clippy 代码检查...
where clippy >nul 2>nul
if %ERRORLEVEL% EQU 0 (
    cargo clippy -- -D warnings
    if %ERRORLEVEL% NEQ 0 (
        echo ⚠️  Clippy 检查发现问题，请修复后重试
    )
)

REM 编译检查
echo 🔧 编译检查...
cargo check
if %ERRORLEVEL% NEQ 0 (
    echo ❌ 编译检查失败
    exit /b 1
)

REM 运行测试
echo 🧪 运行测试...
cargo test
if %ERRORLEVEL% NEQ 0 (
    echo ❌ 测试失败
    exit /b 1
)

REM 构建 Debug 版本
echo 🏗️  构建 Debug 版本...
cargo build
if %ERRORLEVEL% NEQ 0 (
    echo ❌ Debug 构建失败
    exit /b 1
)

REM 构建 Release 版本
echo 🚀 构建 Release 版本...
cargo build --release
if %ERRORLEVEL% NEQ 0 (
    echo ❌ Release 构建失败
    exit /b 1
)

echo ✅ 构建完成！
echo.
echo 📦 构建产物:
echo   Debug:   .\target\debug\walmart-bind-card-notify.exe
echo   Release: .\target\release\walmart-bind-card-notify.exe
echo.
echo 🚀 运行方式:
echo   开发环境: cargo run
echo   生产环境: .\target\release\walmart-bind-card-notify.exe
echo.
echo ⚙️  配置文件: config.toml
echo 📚 文档: README.md

pause
