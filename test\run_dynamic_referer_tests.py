#!/usr/bin/env python3
"""
运行动态Referer配置功能测试

使用方法:
    python test/run_dynamic_referer_tests.py
"""

import sys
import os
import subprocess
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

def run_tests():
    """运行动态Referer测试"""
    print("=" * 60)
    print("开始运行动态Referer配置功能测试")
    print("=" * 60)
    
    test_file = project_root / "test" / "test_dynamic_referer.py"
    
    if not test_file.exists():
        print(f"错误: 测试文件不存在: {test_file}")
        return False
    
    try:
        # 运行pytest
        cmd = [
            sys.executable, "-m", "pytest", 
            str(test_file), 
            "-v",  # 详细输出
            "--tb=short",  # 简短的traceback
            "--no-header",  # 不显示header
            "-x"  # 遇到第一个失败就停止
        ]
        
        print(f"执行命令: {' '.join(cmd)}")
        print("-" * 60)
        
        result = subprocess.run(cmd, cwd=project_root, capture_output=False)
        
        print("-" * 60)
        if result.returncode == 0:
            print("✅ 所有测试通过!")
            return True
        else:
            print("❌ 测试失败!")
            return False
            
    except Exception as e:
        print(f"运行测试时出错: {e}")
        return False

def check_dependencies():
    """检查测试依赖"""
    print("检查测试依赖...")
    
    required_modules = [
        'pytest',
        'pytest-asyncio',
        'sqlalchemy',
        'redis'
    ]
    
    missing_modules = []
    
    for module in required_modules:
        try:
            __import__(module.replace('-', '_'))
            print(f"✅ {module}")
        except ImportError:
            missing_modules.append(module)
            print(f"❌ {module}")
    
    if missing_modules:
        print(f"\n缺少依赖模块: {', '.join(missing_modules)}")
        print("请安装缺少的模块:")
        print(f"pip install {' '.join(missing_modules)}")
        return False
    
    print("所有依赖模块已安装")
    return True

def main():
    """主函数"""
    print("动态Referer配置功能测试运行器")
    print("=" * 60)
    
    # 检查依赖
    if not check_dependencies():
        sys.exit(1)
    
    print()
    
    # 运行测试
    success = run_tests()
    
    print("\n" + "=" * 60)
    if success:
        print("测试完成: 所有测试通过 ✅")
        sys.exit(0)
    else:
        print("测试完成: 存在失败的测试 ❌")
        sys.exit(1)

if __name__ == "__main__":
    main()
