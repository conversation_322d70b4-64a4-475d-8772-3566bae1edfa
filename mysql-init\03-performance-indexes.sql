-- ========================================
-- 性能优化索引脚本
-- 用于提升查询性能，减少N+1查询问题
-- ========================================

USE `walmart_card_db`;

-- ========================================
-- 1. 卡记录表性能索引
-- ========================================

-- 复合索引：商户状态时间（用于商户级统计查询）
CREATE INDEX `idx_card_records_merchant_status_time`
ON `card_records` (`merchant_id`, `status`, `created_at`);

-- 复合索引：CK+状态时间（用于CK级统计查询）
CREATE INDEX `idx_card_records_ck_status_time`
ON `card_records` (`walmart_ck_id`, `status`, `created_at`);

-- 复合索引：部门状态（用于部门级数据隔离）
CREATE INDEX `idx_card_records_dept_status`
ON `card_records` (`department_id`, `status`);

-- 复合索引：商户部门+时间（用于多维度查询）
CREATE INDEX `idx_card_records_merchant_dept_time`
ON `card_records` (`merchant_id`, `department_id`, `created_at`);

-- 复合索引：状态时间（用于全局状态统计）
CREATE INDEX `idx_card_records_status_time`
ON `card_records` (`status`, `created_at`);

-- 复合索引：回调状态时间（用于回调处理）
CREATE INDEX `idx_card_records_callback_status_time`
ON `card_records` (`callback_status`, `created_at`);

-- ========================================
-- 2. 绑卡日志表性能索引
-- ========================================

-- 复合索引：CK+日志类型+时间（用于CK日志分析）
CREATE INDEX `idx_binding_logs_ck_type_time`
ON `binding_logs` (`walmart_ck_id`, `log_type`, `timestamp`);

-- 复合索引：卡记录+日志级别（用于错误日志查询）
CREATE INDEX `idx_binding_logs_record_level`
ON `binding_logs` (`card_record_id`, `log_level`);

-- 复合索引：日志类型级别+时间（用于系统监控）
CREATE INDEX `idx_binding_logs_type_level_time`
ON `binding_logs` (`log_type`, `log_level`, `timestamp`);

-- ========================================
-- 3. 用户权限表性能索引
-- ========================================

-- 复合索引：用户角色（用于权限查询优化）
CREATE INDEX `idx_user_roles_user_role`
ON `user_roles` (`user_id`, `role_id`);

-- 复合索引：用户权限（用于直接权限查询）
CREATE INDEX `idx_user_permissions_user_perm`
ON `user_permissions` (`user_id`, `permission_id`);

-- 复合索引：角色权限（用于角色权限查询）
CREATE INDEX `idx_role_permissions_role_perm`
ON `role_permissions` (`role_id`, `permission_id`);

-- 复合索引：角色菜单（用于菜单权限查询）
CREATE INDEX `idx_role_menus_role_menu`
ON `role_menus` (`role_id`, `menu_id`);

-- ========================================
-- 4. 沃尔玛CK表性能索引
-- ========================================

-- 复合索引：商户状态活跃状态（用于CK选择）
CREATE INDEX `idx_walmart_ck_merchant_status_active`
ON `walmart_ck` (`merchant_id`, `active`);

-- 复合索引：部门状态（用于部门级CK管理）
CREATE INDEX `idx_walmart_ck_dept_status`
ON `walmart_ck` (`department_id`, `active`);

-- 复合索引：创建者商户（用于权限控制）
CREATE INDEX `idx_walmart_ck_creator_merchant`
ON `walmart_ck` (`created_by`, `merchant_id`);

-- 复合索引：状态使用次数（用于CK选择算法）
CREATE INDEX `idx_walmart_ck_status_usage`
ON `walmart_ck` (`active`, `bind_count`);

-- ========================================
-- 5. 用户表性能索引
-- ========================================

-- 复合索引：商户部门+状态（用于组织架构查询）
CREATE INDEX `idx_users_merchant_dept_active`
ON `users` (`merchant_id`, `department_id`, `is_active`);

-- 复合索引：部门职位（用于部门人员管理）
CREATE INDEX `idx_users_dept_position`
ON `users` (`department_id`, `position`);

-- 复合索引：状态超级用户（用于用户管理）
CREATE INDEX `idx_users_active_super`
ON `users` (`is_active`, `is_superuser`);

-- ========================================
-- 6. 部门表性能索引
-- ========================================

-- 复合索引：商户父部门层级（用于组织架构查询）
CREATE INDEX `idx_departments_merchant_parent_level`
ON `departments` (`merchant_id`, `parent_id`, `level`);

-- 复合索引：路径状态（用于路径查询）
CREATE INDEX `idx_departments_path_status`
ON `departments` (`path`, `status`);

-- 复合索引：负责人+商户（用于负责人查询）
CREATE INDEX `idx_departments_manager_merchant`
ON `departments` (`manager_id`, `merchant_id`);

-- ========================================
-- 7. 商户表性能索引
-- ========================================

-- 复合索引：状态创建时间（用于商户管理）
CREATE INDEX `idx_merchants_status_created`
ON `merchants` (`status`, `created_at`);

-- 复合索引：创建者状态（用于权限控制）
CREATE INDEX `idx_merchants_creator_status`
ON `merchants` (`created_by`, `status`);

-- ========================================
-- 8. 菜单表性能索引
-- ========================================

-- 复合索引：父菜单+层级+排序（用于菜单树构建）
CREATE INDEX `idx_menus_parent_level_sort`
ON `menus` (`parent_id`, `level`, `sort_order`);

-- 复合索引：可见启用+类型（用于菜单过滤）
CREATE INDEX `idx_menus_visible_enabled_type`
ON `menus` (`is_visible`, `is_enabled`, `menu_type`);

-- ========================================
-- 9. 权限表性能索引
-- ========================================

-- 复合索引：资源类型路径（用于权限匹配）
CREATE INDEX `idx_permissions_type_path`
ON `permissions` (`resource_type`, `resource_path`);

-- 复合索引：启用资源类型（用于权限过滤）
CREATE INDEX `idx_permissions_enabled_type`
ON `permissions` (`is_enabled`, `resource_type`);

-- ========================================
-- 10. 系统设置表性能索引
-- ========================================

-- 复合索引：公开+类型（用于前端配置查询）
CREATE INDEX `idx_system_settings_public_type` 
ON `system_settings` (`is_public`, `type`);

-- ========================================
-- 索引使用说明
-- ========================================

/*
索引设计原则：
1. 高频查询字段优先建索引
2. 复合索引按查询频率和选择性排序
3. 避免过多索引影响写入性能
4. 定期监控索引使用情况

性能提升预期：
1. 商户级查询：响应时间减少60-80%
2. CK统计查询：响应时间减少70-90%
3. 权限查询：响应时间减少50-70%
4. 组织架构查询：响应时间减少60-80%

监控建议：
1. 使用 EXPLAIN 分析查询计划
2. 监控慢查询日志
3. 定期检查索引使用率
4. 根据业务变化调整索引策略
*/

-- ========================================
-- 索引创建完成提示
-- ========================================

SELECT 'Performance indexes created successfully!' AS status;
