package services

import (
	"context"
	"encoding/json"
	"fmt"
	"runtime"
	"sync"
	"sync/atomic"
	"time"

	"github.com/go-redis/redis/v8"
	"go.uber.org/zap"
	"gorm.io/gorm"
)

// PerformanceMetrics 性能指标
type PerformanceMetrics struct {
	// 权重选择指标
	WeightSelectionCount    int64         `json:"weight_selection_count"`
	WeightSelectionLatency  time.Duration `json:"weight_selection_latency"`
	WeightCacheHitRate      float64       `json:"weight_cache_hit_rate"`
	
	// CK预占用指标
	PreoccupationCount      int64         `json:"preoccupation_count"`
	PreoccupationLatency    time.Duration `json:"preoccupation_latency"`
	PreoccupationSuccessRate float64      `json:"preoccupation_success_rate"`
	
	// 绑卡处理指标
	BindCardCount           int64         `json:"bind_card_count"`
	BindCardLatency         time.Duration `json:"bind_card_latency"`
	BindCardSuccessRate     float64       `json:"bind_card_success_rate"`
	
	// 系统指标
	ActiveCKCount           int64         `json:"active_ck_count"`
	AvailableCKCount        int64         `json:"available_ck_count"`
	CircuitBreakerOpenCount int64         `json:"circuit_breaker_open_count"`

	// 新增系统资源指标
	GoroutineCount          int           `json:"goroutine_count"`
	MemoryUsageMB           uint64        `json:"memory_usage_mb"`
	GCPauseTimeMS           float64       `json:"gc_pause_time_ms"`

	// 数据库连接池指标
	DBOpenConnections       int           `json:"db_open_connections"`
	DBInUseConnections      int           `json:"db_in_use_connections"`
	DBIdleConnections       int           `json:"db_idle_connections"`
	DBMaxOpenConnections    int           `json:"db_max_open_connections"`
	DBWaitCount             int64         `json:"db_wait_count"`

	// Redis连接池指标
	RedisPoolSize           int           `json:"redis_pool_size"`
	RedisIdleConnections    int           `json:"redis_idle_connections"`
	RedisTotalConnections   int           `json:"redis_total_connections"`

	// 时间戳
	Timestamp               int64         `json:"timestamp"`
}

// PerformanceCounter 性能计数器
type PerformanceCounter struct {
	Count     int64
	TotalTime int64 // 纳秒
	Errors    int64
	mu        sync.RWMutex
}

// Add 添加一次操作记录
func (pc *PerformanceCounter) Add(duration time.Duration, isError bool) {
	pc.mu.Lock()
	defer pc.mu.Unlock()
	
	atomic.AddInt64(&pc.Count, 1)
	atomic.AddInt64(&pc.TotalTime, int64(duration))
	if isError {
		atomic.AddInt64(&pc.Errors, 1)
	}
}

// GetStats 获取统计信息
func (pc *PerformanceCounter) GetStats() (count int64, avgLatency time.Duration, errorRate float64) {
	pc.mu.RLock()
	defer pc.mu.RUnlock()
	
	count = atomic.LoadInt64(&pc.Count)
	totalTime := atomic.LoadInt64(&pc.TotalTime)
	errors := atomic.LoadInt64(&pc.Errors)
	
	if count > 0 {
		avgLatency = time.Duration(totalTime / count)
		errorRate = float64(errors) / float64(count)
	}
	
	return
}

// Reset 重置计数器
func (pc *PerformanceCounter) Reset() {
	pc.mu.Lock()
	defer pc.mu.Unlock()
	
	atomic.StoreInt64(&pc.Count, 0)
	atomic.StoreInt64(&pc.TotalTime, 0)
	atomic.StoreInt64(&pc.Errors, 0)
}

// PerformanceMonitor 性能监控器
type PerformanceMonitor struct {
	redis  *redis.Client
	logger *zap.Logger
	
	// 性能计数器
	weightSelection *PerformanceCounter
	preoccupation   *PerformanceCounter
	bindCard        *PerformanceCounter
	
	// 缓存命中率统计
	cacheHits   int64
	cacheMisses int64
	
	// 熔断器统计
	circuitBreakerOpens int64
	
	// 配置
	reportInterval time.Duration
	retentionDays  int
}

// NewPerformanceMonitor 创建性能监控器
func NewPerformanceMonitor(redis *redis.Client, logger *zap.Logger) *PerformanceMonitor {
	monitor := &PerformanceMonitor{
		redis:           redis,
		logger:          logger,
		weightSelection: &PerformanceCounter{},
		preoccupation:   &PerformanceCounter{},
		bindCard:        &PerformanceCounter{},
		reportInterval:  1 * time.Minute,
		retentionDays:   7,
	}
	
	// 启动后台报告任务
	go monitor.startReportWorker()
	
	return monitor
}

// RecordWeightSelection 记录权重选择操作
func (pm *PerformanceMonitor) RecordWeightSelection(duration time.Duration, isError bool, cacheHit bool) {
	pm.weightSelection.Add(duration, isError)
	
	if cacheHit {
		atomic.AddInt64(&pm.cacheHits, 1)
	} else {
		atomic.AddInt64(&pm.cacheMisses, 1)
	}
}

// RecordPreoccupation 记录预占用操作
func (pm *PerformanceMonitor) RecordPreoccupation(duration time.Duration, isError bool) {
	pm.preoccupation.Add(duration, isError)
}

// RecordBindCard 记录绑卡操作
func (pm *PerformanceMonitor) RecordBindCard(duration time.Duration, isError bool) {
	pm.bindCard.Add(duration, isError)
}

// RecordCircuitBreakerOpen 记录熔断器开启
func (pm *PerformanceMonitor) RecordCircuitBreakerOpen() {
	atomic.AddInt64(&pm.circuitBreakerOpens, 1)
}

// GetCurrentMetrics 获取当前性能指标
func (pm *PerformanceMonitor) GetCurrentMetrics() *PerformanceMetrics {
	// 权重选择指标
	wsCount, wsLatency, _ := pm.weightSelection.GetStats()
	
	// 预占用指标
	preCount, preLatency, preErrorRate := pm.preoccupation.GetStats()
	
	// 绑卡指标
	bcCount, bcLatency, bcErrorRate := pm.bindCard.GetStats()
	
	// 缓存命中率
	hits := atomic.LoadInt64(&pm.cacheHits)
	misses := atomic.LoadInt64(&pm.cacheMisses)
	var cacheHitRate float64
	if hits+misses > 0 {
		cacheHitRate = float64(hits) / float64(hits+misses)
	}
	
	metrics := &PerformanceMetrics{
		WeightSelectionCount:    wsCount,
		WeightSelectionLatency:  wsLatency,
		WeightCacheHitRate:      cacheHitRate,

		PreoccupationCount:      preCount,
		PreoccupationLatency:    preLatency,
		PreoccupationSuccessRate: 1.0 - preErrorRate,

		BindCardCount:           bcCount,
		BindCardLatency:         bcLatency,
		BindCardSuccessRate:     1.0 - bcErrorRate,

		CircuitBreakerOpenCount: atomic.LoadInt64(&pm.circuitBreakerOpens),
		Timestamp:               time.Now().Unix(),
	}

	// 收集系统资源指标
	pm.collectSystemMetrics(metrics)

	// 收集Redis连接池指标
	pm.collectRedisMetrics(metrics)

	return metrics
}

// GetEnhancedMetrics 获取增强的性能指标（包含数据库指标）
func (pm *PerformanceMonitor) GetEnhancedMetrics(db *gorm.DB) *PerformanceMetrics {
	// 获取基础指标
	metrics := pm.GetCurrentMetrics()

	// 收集数据库连接池指标
	if db != nil {
		pm.collectDatabaseMetrics(metrics, db)
	}

	return metrics
}

// startReportWorker 启动性能报告工作器
func (pm *PerformanceMonitor) startReportWorker() {
	ticker := time.NewTicker(pm.reportInterval)
	defer ticker.Stop()
	
	for range ticker.C {
		metrics := pm.GetCurrentMetrics()
		
		// 保存到Redis
		if err := pm.saveMetricsToRedis(metrics); err != nil {
			pm.logger.Error("保存性能指标失败", zap.Error(err))
		}
		
		// 记录日志
		pm.logger.Info("性能指标报告",
			zap.Int64("weight_selection_count", metrics.WeightSelectionCount),
			zap.Duration("weight_selection_latency", metrics.WeightSelectionLatency),
			zap.Float64("cache_hit_rate", metrics.WeightCacheHitRate),
			zap.Int64("preoccupation_count", metrics.PreoccupationCount),
			zap.Duration("preoccupation_latency", metrics.PreoccupationLatency),
			zap.Float64("preoccupation_success_rate", metrics.PreoccupationSuccessRate),
			zap.Int64("bind_card_count", metrics.BindCardCount),
			zap.Duration("bind_card_latency", metrics.BindCardLatency),
			zap.Float64("bind_card_success_rate", metrics.BindCardSuccessRate))
		
		// 重置计数器（为下一个周期准备）
		pm.resetCounters()
		
		// 清理过期数据
		go pm.cleanupExpiredMetrics()
	}
}

// saveMetricsToRedis 保存指标到Redis
func (pm *PerformanceMonitor) saveMetricsToRedis(metrics *PerformanceMetrics) error {
	ctx := context.Background()
	
	// 序列化指标
	data, err := json.Marshal(metrics)
	if err != nil {
		return err
	}
	
	// 保存到时间序列
	key := "performance_metrics"
	score := float64(metrics.Timestamp)
	
	// 使用ZADD保存到有序集合
	err = pm.redis.ZAdd(ctx, key, &redis.Z{
		Score:  score,
		Member: string(data),
	}).Err()
	
	if err != nil {
		return err
	}
	
	// 设置过期时间
	pm.redis.Expire(ctx, key, time.Duration(pm.retentionDays)*24*time.Hour)
	
	return nil
}

// GetHistoricalMetrics 获取历史性能指标
func (pm *PerformanceMonitor) GetHistoricalMetrics(startTime, endTime int64) ([]*PerformanceMetrics, error) {
	ctx := context.Background()
	
	// 从Redis获取指定时间范围的数据
	results, err := pm.redis.ZRangeByScore(ctx, "performance_metrics", &redis.ZRangeBy{
		Min: fmt.Sprintf("%d", startTime),
		Max: fmt.Sprintf("%d", endTime),
	}).Result()
	
	if err != nil {
		return nil, err
	}
	
	var metrics []*PerformanceMetrics
	for _, result := range results {
		var metric PerformanceMetrics
		if err := json.Unmarshal([]byte(result), &metric); err != nil {
			pm.logger.Error("反序列化性能指标失败", zap.Error(err))
			continue
		}
		metrics = append(metrics, &metric)
	}
	
	return metrics, nil
}

// GetPerformanceSummary 获取性能摘要
func (pm *PerformanceMonitor) GetPerformanceSummary(hours int) (*PerformanceSummary, error) {
	endTime := time.Now().Unix()
	startTime := endTime - int64(hours*3600)
	
	metrics, err := pm.GetHistoricalMetrics(startTime, endTime)
	if err != nil {
		return nil, err
	}
	
	if len(metrics) == 0 {
		return &PerformanceSummary{}, nil
	}
	
	summary := &PerformanceSummary{
		TimeRange: fmt.Sprintf("%d小时", hours),
		StartTime: startTime,
		EndTime:   endTime,
	}
	
	// 计算平均值和总计
	var totalWeightSelections, totalPreoccupations, totalBindCards int64
	var totalWeightLatency, totalPreLatency, totalBindLatency time.Duration
	var totalCacheHitRate, totalPreSuccessRate, totalBindSuccessRate float64
	
	for _, metric := range metrics {
		totalWeightSelections += metric.WeightSelectionCount
		totalPreoccupations += metric.PreoccupationCount
		totalBindCards += metric.BindCardCount
		
		totalWeightLatency += metric.WeightSelectionLatency
		totalPreLatency += metric.PreoccupationLatency
		totalBindLatency += metric.BindCardLatency
		
		totalCacheHitRate += metric.WeightCacheHitRate
		totalPreSuccessRate += metric.PreoccupationSuccessRate
		totalBindSuccessRate += metric.BindCardSuccessRate
	}
	
	count := len(metrics)
	summary.TotalWeightSelections = totalWeightSelections
	summary.TotalPreoccupations = totalPreoccupations
	summary.TotalBindCards = totalBindCards
	
	if count > 0 {
		summary.AvgWeightLatency = totalWeightLatency / time.Duration(count)
		summary.AvgPreoccupationLatency = totalPreLatency / time.Duration(count)
		summary.AvgBindCardLatency = totalBindLatency / time.Duration(count)
		
		summary.AvgCacheHitRate = totalCacheHitRate / float64(count)
		summary.AvgPreoccupationSuccessRate = totalPreSuccessRate / float64(count)
		summary.AvgBindCardSuccessRate = totalBindSuccessRate / float64(count)
	}
	
	return summary, nil
}

// resetCounters 重置计数器
func (pm *PerformanceMonitor) resetCounters() {
	pm.weightSelection.Reset()
	pm.preoccupation.Reset()
	pm.bindCard.Reset()
	
	atomic.StoreInt64(&pm.cacheHits, 0)
	atomic.StoreInt64(&pm.cacheMisses, 0)
	atomic.StoreInt64(&pm.circuitBreakerOpens, 0)
}

// cleanupExpiredMetrics 清理过期指标
func (pm *PerformanceMonitor) cleanupExpiredMetrics() {
	ctx := context.Background()
	
	// 删除超过保留期的数据
	expireTime := time.Now().AddDate(0, 0, -pm.retentionDays).Unix()
	
	err := pm.redis.ZRemRangeByScore(ctx, "performance_metrics", "0", fmt.Sprintf("%d", expireTime)).Err()
	if err != nil {
		pm.logger.Error("清理过期性能指标失败", zap.Error(err))
	}
}

// PerformanceSummary 性能摘要
type PerformanceSummary struct {
	TimeRange                    string        `json:"time_range"`
	StartTime                    int64         `json:"start_time"`
	EndTime                      int64         `json:"end_time"`
	TotalWeightSelections        int64         `json:"total_weight_selections"`
	TotalPreoccupations          int64         `json:"total_preoccupations"`
	TotalBindCards               int64         `json:"total_bind_cards"`
	AvgWeightLatency             time.Duration `json:"avg_weight_latency"`
	AvgPreoccupationLatency      time.Duration `json:"avg_preoccupation_latency"`
	AvgBindCardLatency           time.Duration `json:"avg_bind_card_latency"`
	AvgCacheHitRate              float64       `json:"avg_cache_hit_rate"`
	AvgPreoccupationSuccessRate  float64       `json:"avg_preoccupation_success_rate"`
	AvgBindCardSuccessRate       float64       `json:"avg_bind_card_success_rate"`
}

// collectSystemMetrics 收集系统资源指标
func (pm *PerformanceMonitor) collectSystemMetrics(metrics *PerformanceMetrics) {
	// 收集协程数量
	metrics.GoroutineCount = runtime.NumGoroutine()

	// 收集内存使用情况
	var m runtime.MemStats
	runtime.ReadMemStats(&m)
	metrics.MemoryUsageMB = m.Alloc / 1024 / 1024 // 转换为MB

	// 收集GC暂停时间
	if len(m.PauseNs) > 0 {
		metrics.GCPauseTimeMS = float64(m.PauseNs[(m.NumGC+255)%256]) / 1e6 // 转换为毫秒
	}
}

// collectDatabaseMetrics 收集数据库连接池指标
func (pm *PerformanceMonitor) collectDatabaseMetrics(metrics *PerformanceMetrics, db *gorm.DB) {
	if db != nil {
		if sqlDB, err := db.DB(); err == nil {
			stats := sqlDB.Stats()
			metrics.DBOpenConnections = stats.OpenConnections
			metrics.DBInUseConnections = stats.InUse
			metrics.DBIdleConnections = stats.Idle
			metrics.DBMaxOpenConnections = stats.MaxOpenConnections
			metrics.DBWaitCount = stats.WaitCount
		}
	}
}

// CheckSystemHealth 检查系统健康状态
func (pm *PerformanceMonitor) CheckSystemHealth(db *gorm.DB) map[string]interface{} {
	metrics := pm.GetEnhancedMetrics(db)

	status := "healthy"
	alerts := []string{}

	// 检查协程数量
	if metrics.GoroutineCount > 2000 {
		status = "degraded"
		alerts = append(alerts, fmt.Sprintf("协程数量过高: %d", metrics.GoroutineCount))
	}

	// 检查内存使用
	if metrics.MemoryUsageMB > 2048 {
		status = "degraded"
		alerts = append(alerts, fmt.Sprintf("内存使用过高: %d MB", metrics.MemoryUsageMB))
	}

	// 检查数据库连接池
	if metrics.DBMaxOpenConnections > 0 {
		usage := float64(metrics.DBInUseConnections) / float64(metrics.DBMaxOpenConnections)
		if usage > 0.9 {
			status = "degraded"
			alerts = append(alerts, fmt.Sprintf("数据库连接池使用率过高: %.1f%%", usage*100))
		}
	}

	// 检查绑卡成功率
	if metrics.BindCardSuccessRate < 0.95 && metrics.BindCardCount > 10 {
		status = "degraded"
		alerts = append(alerts, fmt.Sprintf("绑卡成功率过低: %.1f%%", metrics.BindCardSuccessRate*100))
	}

	return map[string]interface{}{
		"status": status,
		"alerts": alerts,
		"metrics": metrics,
	}
}



// collectRedisMetrics 收集Redis连接池指标
func (pm *PerformanceMonitor) collectRedisMetrics(metrics *PerformanceMetrics) {
	if pm.redis != nil {
		poolStats := pm.redis.PoolStats()
		metrics.RedisPoolSize = int(poolStats.TotalConns)
		metrics.RedisIdleConnections = int(poolStats.IdleConns)
		metrics.RedisTotalConnections = int(poolStats.TotalConns)
	}
}
