import { defineStore } from 'pinia'
import { bindingLogsApi } from '@/api/modules/bindingLogs'

export const useBindingLogsStore = defineStore('bindingLogs', {
    state: () => ({
        logs: [],
        currentLog: null,
        loading: false,
        error: null,
        pagination: { total: 0, page: 1, pageSize: 20 }
    }),

    getters: {
        getLogs: (state) => state.logs,
        getCurrentLog: (state) => state.currentLog,
        isLoading: (state) => state.loading,
        getPagination: (state) => state.pagination
    },

    actions: {
        // 获取卡记录的绑定日志
        async fetchLogsByCardId(cardId, params = {}) {
            this.loading = true
            try {
                const response = await bindingLogsApi.getLogsByCardId(cardId, {
                    page: params.page || this.pagination.page,
                    page_size: params.pageSize || this.pagination.pageSize,
                    ...params
                })
                
                this.logs = response.items || []
                this.pagination = {
                    total: response.total || 0,
                    page: response.page || 1,
                    pageSize: response.pageSize || 20
                }
                return response
            } catch (error) {
                this.error = error.message
                console.error('获取绑定日志失败:', error)
                return null
            } finally {
                this.loading = false
            }
        },

        // 设置当前日志
        setCurrentLog(log) {
            this.currentLog = log
        },

        // 重置状态
        resetState() {
            this.logs = []
            this.currentLog = null
            this.loading = false
            this.error = null
            this.pagination = { total: 0, page: 1, pageSize: 20 }
        }
    }
})
