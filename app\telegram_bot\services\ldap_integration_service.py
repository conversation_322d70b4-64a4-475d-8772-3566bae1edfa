"""
LDAP/AD集成服务
用于自动同步用户信息和权限
"""

import ldap3
from typing import Dict, List, Optional, Any
from datetime import datetime, timedelta
from sqlalchemy.orm import Session
from sqlalchemy import and_

from app.models.user import User
from app.models.telegram_user import TelegramUser
from app.models.role import Role
from app.models.department import Department
from app.models.audit import AuditLog, AuditEventType, AuditLevel
from app.core.logging import get_logger
from ..config import BotConfig

logger = get_logger(__name__)


class LDAPIntegrationService:
    """LDAP集成服务"""
    
    def __init__(self, db: Session, config: BotConfig):
        self.db = db
        self.config = config
        
        # LDAP配置
        self.ldap_server = config.get('ldap.server')
        self.ldap_port = config.get('ldap.port', 389)
        self.ldap_use_ssl = config.get('ldap.use_ssl', False)
        self.ldap_bind_dn = config.get('ldap.bind_dn')
        self.ldap_bind_password = config.get('ldap.bind_password')
        self.ldap_base_dn = config.get('ldap.base_dn')
        self.ldap_user_filter = config.get('ldap.user_filter', '(objectClass=person)')
        
        # 字段映射
        self.field_mapping = {
            'username': config.get('ldap.field_mapping.username', 'sAMAccountName'),
            'email': config.get('ldap.field_mapping.email', 'mail'),
            'first_name': config.get('ldap.field_mapping.first_name', 'givenName'),
            'last_name': config.get('ldap.field_mapping.last_name', 'sn'),
            'employee_id': config.get('ldap.field_mapping.employee_id', 'employeeID'),
            'department': config.get('ldap.field_mapping.department', 'department'),
            'title': config.get('ldap.field_mapping.title', 'title'),
            'manager': config.get('ldap.field_mapping.manager', 'manager')
        }
    
    def test_connection(self) -> Dict[str, Any]:
        """
        测试LDAP连接
        
        Returns:
            Dict: 连接测试结果
        """
        try:
            server = ldap3.Server(
                self.ldap_server,
                port=self.ldap_port,
                use_ssl=self.ldap_use_ssl,
                get_info=ldap3.ALL
            )
            
            conn = ldap3.Connection(
                server,
                user=self.ldap_bind_dn,
                password=self.ldap_bind_password,
                auto_bind=True
            )
            
            # 测试搜索
            conn.search(
                search_base=self.ldap_base_dn,
                search_filter='(objectClass=*)',
                search_scope=ldap3.BASE,
                attributes=['*']
            )
            
            conn.unbind()
            
            return {
                "success": True,
                "message": "LDAP连接测试成功",
                "server_info": str(server.info)
            }
            
        except Exception as e:
            logger.error(f"LDAP连接测试失败: {e}")
            return {
                "success": False,
                "message": f"LDAP连接测试失败: {str(e)}"
            }
    
    def sync_users_from_ldap(self, force_update: bool = False) -> Dict[str, Any]:
        """
        从LDAP同步用户信息
        
        Args:
            force_update: 是否强制更新已存在的用户
            
        Returns:
            Dict: 同步结果
        """
        result = {
            "success": False,
            "users_processed": 0,
            "users_created": 0,
            "users_updated": 0,
            "users_deactivated": 0,
            "errors": []
        }
        
        try:
            # 建立LDAP连接
            server = ldap3.Server(
                self.ldap_server,
                port=self.ldap_port,
                use_ssl=self.ldap_use_ssl
            )
            
            conn = ldap3.Connection(
                server,
                user=self.ldap_bind_dn,
                password=self.ldap_bind_password,
                auto_bind=True
            )
            
            # 搜索用户
            attributes = list(self.field_mapping.values()) + ['objectClass', 'whenChanged']
            
            conn.search(
                search_base=self.ldap_base_dn,
                search_filter=self.ldap_user_filter,
                search_scope=ldap3.SUBTREE,
                attributes=attributes
            )
            
            ldap_users = {}
            for entry in conn.entries:
                user_data = self._extract_user_data(entry)
                if user_data and user_data.get('username'):
                    ldap_users[user_data['username']] = user_data
                    result["users_processed"] += 1
            
            conn.unbind()
            
            # 同步到数据库
            sync_stats = self._sync_users_to_database(ldap_users, force_update)
            result.update(sync_stats)
            
            # 处理离职用户（在LDAP中不存在但在数据库中存在的用户）
            deactivated_count = self._handle_departed_users(ldap_users.keys())
            result["users_deactivated"] = deactivated_count
            
            result["success"] = True
            
            # 记录同步日志
            self._log_sync_event("user_sync_completed", result)
            
            logger.info(f"LDAP用户同步完成: {result}")
            
        except Exception as e:
            logger.error(f"LDAP用户同步失败: {e}")
            result["errors"].append(str(e))
        
        return result
    
    def sync_user_permissions(self, username: str) -> Dict[str, Any]:
        """
        同步单个用户的权限
        
        Args:
            username: 用户名
            
        Returns:
            Dict: 同步结果
        """
        try:
            # 从LDAP获取用户信息
            user_data = self._get_user_from_ldap(username)
            if not user_data:
                return {
                    "success": False,
                    "message": "用户在LDAP中不存在"
                }
            
            # 获取数据库中的用户
            user = self.db.query(User).filter_by(username=username).first()
            if not user:
                return {
                    "success": False,
                    "message": "用户在数据库中不存在"
                }
            
            # 同步角色和权限
            roles_updated = self._sync_user_roles(user, user_data)
            
            # 同步部门信息
            department_updated = self._sync_user_department(user, user_data)
            
            self.db.commit()
            
            # 记录同步日志
            self._log_sync_event("user_permission_sync", {
                "username": username,
                "roles_updated": roles_updated,
                "department_updated": department_updated
            })
            
            return {
                "success": True,
                "message": "用户权限同步成功",
                "roles_updated": roles_updated,
                "department_updated": department_updated
            }
            
        except Exception as e:
            logger.error(f"同步用户权限失败: {e}")
            self.db.rollback()
            return {
                "success": False,
                "message": f"同步失败: {str(e)}"
            }
    
    def auto_verify_telegram_user(self, telegram_user_id: int) -> Dict[str, Any]:
        """
        基于LDAP信息自动验证Telegram用户
        
        Args:
            telegram_user_id: Telegram用户ID
            
        Returns:
            Dict: 验证结果
        """
        try:
            telegram_user = self.db.query(TelegramUser).filter_by(
                telegram_user_id=telegram_user_id
            ).first()
            
            if not telegram_user:
                return {
                    "success": False,
                    "message": "Telegram用户不存在"
                }
            
            # 尝试通过用户名匹配
            if telegram_user.telegram_username:
                ldap_user = self._get_user_from_ldap(telegram_user.telegram_username)
                if ldap_user:
                    system_user = self._find_or_create_system_user(ldap_user)
                    if system_user:
                        telegram_user.verify_with_system_user(system_user.id)
                        self.db.commit()
                        
                        self._log_sync_event("auto_verification_success", {
                            "telegram_user_id": telegram_user_id,
                            "system_user_id": system_user.id,
                            "matched_by": "username"
                        })
                        
                        return {
                            "success": True,
                            "message": "自动验证成功",
                            "system_user_id": system_user.id
                        }
            
            # 尝试通过姓名匹配
            if telegram_user.telegram_first_name or telegram_user.telegram_last_name:
                ldap_user = self._find_user_by_name(
                    telegram_user.telegram_first_name,
                    telegram_user.telegram_last_name
                )
                if ldap_user:
                    system_user = self._find_or_create_system_user(ldap_user)
                    if system_user:
                        telegram_user.verify_with_system_user(system_user.id)
                        self.db.commit()
                        
                        self._log_sync_event("auto_verification_success", {
                            "telegram_user_id": telegram_user_id,
                            "system_user_id": system_user.id,
                            "matched_by": "name"
                        })
                        
                        return {
                            "success": True,
                            "message": "自动验证成功（通过姓名匹配）",
                            "system_user_id": system_user.id
                        }
            
            return {
                "success": False,
                "message": "无法在LDAP中找到匹配的用户"
            }
            
        except Exception as e:
            logger.error(f"自动验证失败: {e}")
            self.db.rollback()
            return {
                "success": False,
                "message": f"自动验证失败: {str(e)}"
            }
    
    def _extract_user_data(self, ldap_entry) -> Optional[Dict[str, Any]]:
        """从LDAP条目提取用户数据"""
        try:
            user_data = {}
            
            for field, ldap_attr in self.field_mapping.items():
                value = getattr(ldap_entry, ldap_attr, None)
                if value:
                    user_data[field] = str(value) if not isinstance(value, str) else value
            
            # 提取其他有用信息
            user_data['dn'] = str(ldap_entry.entry_dn)
            user_data['object_classes'] = [str(oc) for oc in ldap_entry.objectClass]
            
            if hasattr(ldap_entry, 'whenChanged'):
                user_data['last_modified'] = ldap_entry.whenChanged.value
            
            return user_data
            
        except Exception as e:
            logger.error(f"提取用户数据失败: {e}")
            return None
    
    def _sync_users_to_database(self, ldap_users: Dict[str, Dict], force_update: bool) -> Dict[str, int]:
        """将LDAP用户同步到数据库"""
        stats = {
            "users_created": 0,
            "users_updated": 0
        }
        
        for username, user_data in ldap_users.items():
            try:
                existing_user = self.db.query(User).filter_by(username=username).first()
                
                if existing_user:
                    if force_update or self._should_update_user(existing_user, user_data):
                        self._update_user_from_ldap(existing_user, user_data)
                        stats["users_updated"] += 1
                else:
                    self._create_user_from_ldap(user_data)
                    stats["users_created"] += 1
                    
            except Exception as e:
                logger.error(f"同步用户 {username} 失败: {e}")
        
        self.db.commit()
        return stats
    
    def _should_update_user(self, user: User, ldap_data: Dict[str, Any]) -> bool:
        """判断是否需要更新用户"""
        # 检查关键字段是否有变化
        if user.email != ldap_data.get('email'):
            return True
        if user.first_name != ldap_data.get('first_name'):
            return True
        if user.last_name != ldap_data.get('last_name'):
            return True
        
        return False
    
    def _update_user_from_ldap(self, user: User, ldap_data: Dict[str, Any]):
        """从LDAP数据更新用户"""
        user.email = ldap_data.get('email', user.email)
        user.first_name = ldap_data.get('first_name', user.first_name)
        user.last_name = ldap_data.get('last_name', user.last_name)
        user.employee_id = ldap_data.get('employee_id', user.employee_id)
        
        # 更新其他字段...
    
    def _create_user_from_ldap(self, ldap_data: Dict[str, Any]) -> User:
        """从LDAP数据创建用户"""
        user = User(
            username=ldap_data['username'],
            email=ldap_data.get('email'),
            first_name=ldap_data.get('first_name'),
            last_name=ldap_data.get('last_name'),
            employee_id=ldap_data.get('employee_id'),
            is_active=True,
            is_ldap_user=True
        )
        
        self.db.add(user)
        return user
    
    def _handle_departed_users(self, active_usernames: List[str]) -> int:
        """处理离职用户"""
        # 查找在数据库中但不在LDAP中的用户
        departed_users = self.db.query(User).filter(
            and_(
                User.is_ldap_user == True,
                User.is_active == True,
                ~User.username.in_(active_usernames)
            )
        ).all()
        
        deactivated_count = 0
        for user in departed_users:
            user.is_active = False
            # 同时处理相关的Telegram用户
            telegram_users = self.db.query(TelegramUser).filter_by(
                system_user_id=user.id
            ).all()
            
            for telegram_user in telegram_users:
                telegram_user.verification_status = "expired"
            
            deactivated_count += 1
        
        self.db.commit()
        return deactivated_count
    
    def _get_user_from_ldap(self, username: str) -> Optional[Dict[str, Any]]:
        """从LDAP获取单个用户信息"""
        # 实现LDAP查询逻辑
        pass
    
    def _find_user_by_name(self, first_name: str, last_name: str) -> Optional[Dict[str, Any]]:
        """通过姓名在LDAP中查找用户"""
        # 实现姓名匹配逻辑
        pass
    
    def _find_or_create_system_user(self, ldap_data: Dict[str, Any]) -> Optional[User]:
        """查找或创建系统用户"""
        # 实现用户查找/创建逻辑
        pass
    
    def _sync_user_roles(self, user: User, ldap_data: Dict[str, Any]) -> bool:
        """同步用户角色"""
        # 实现角色同步逻辑
        return False
    
    def _sync_user_department(self, user: User, ldap_data: Dict[str, Any]) -> bool:
        """同步用户部门"""
        # 实现部门同步逻辑
        return False
    
    def _log_sync_event(self, event_type: str, details: Dict[str, Any]):
        """记录同步事件"""
        try:
            audit_log = AuditLog(
                event_type=AuditEventType.SYSTEM.value,
                level=AuditLevel.INFO.value,
                resource_type="ldap_sync",
                action=event_type,
                details=details
            )
            self.db.add(audit_log)
            self.db.commit()
            
        except Exception as e:
            logger.error(f"记录同步事件失败: {e}")
