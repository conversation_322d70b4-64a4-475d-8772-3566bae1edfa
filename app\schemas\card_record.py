from typing import Optional, List, Dict, Any
from pydantic import BaseModel, Field
from datetime import datetime


class CardStatus:
    """卡状态常量类"""
    PENDING = "pending"
    PROCESSING = "processing"
    SUCCESS = "success"
    FAILED = "failed"
    TIMEOUT = "timeout"
    CANCELLED = "cancelled"
    BALANCE_FAILED = "balance_failed"  # 绑卡成功但获取余额失败

    @classmethod
    def get_all_values(cls):
        """获取所有状态值"""
        return [cls.PENDING, cls.PROCESSING, cls.SUCCESS, cls.FAILED, cls.TIMEOUT, cls.CANCELLED, cls.BALANCE_FAILED]


class CallbackStatus:
    """回调状态常量类"""
    PENDING = "pending"
    SUCCESS = "success"
    FAILED = "failed"

    @classmethod
    def get_all_values(cls):
        """获取所有状态值"""
        return [cls.PENDING, cls.SUCCESS, cls.FAILED]


# 基础模型
class CardRecordBase(BaseModel):
    merchant_order_id: str = Field(..., description="商家订单号", max_length=255)
    amount: int = Field(..., description="订单金额，单位：分", ge=1)
    card_number: str = Field(..., description="卡号", max_length=50)
    card_password: Optional[str] = Field(None, description="卡密", max_length=512)
    remark: Optional[str] = Field(None, description="备注", max_length=500)
    ext_data: Optional[str] = Field(None, description="扩展数据，回调时原样返回", max_length=512)

    class Config:
        from_attributes = True


# 创建卡记录
class CardRecordCreate(CardRecordBase):
    merchant_id: int = Field(..., description="商家ID")
    department_id: Optional[int] = Field(None, description="所属部门ID（绑卡成功后填入）")


# 更新卡记录
class CardRecordUpdate(BaseModel):
    status: Optional[str] = Field(None, description="状态")
    actual_amount: Optional[int] = Field(None, description="实际卡金额，单位：分")
    balance: Optional[str] = Field(None, description="balance字段", max_length=32)
    cardBalance: Optional[str] = Field(None, description="cardBalance字段", max_length=32)
    balanceCnt: Optional[str] = Field(None, description="balanceCnt字段", max_length=32)
    error_message: Optional[str] = Field(None, description="错误信息", max_length=500)
    callback_status: Optional[str] = Field(None, description="回调状态")
    callback_result: Optional[str] = Field(None, description="回调结果描述", max_length=500)

    class Config:
        from_attributes = True


# 卡记录响应
class CardRecord(CardRecordBase):
    id: str = Field(..., description="记录ID（UUID）")
    merchant_id: int = Field(..., description="商家ID")
    department_id: Optional[int] = Field(None, description="所属部门ID（绑卡成功后填入）")
    walmart_ck_id: Optional[int] = Field(None, description="使用的沃尔玛CK ID（绑卡成功后填入）")
    actual_amount: Optional[int] = Field(None, description="实际卡金额，单位：分")
    balance: Optional[str] = Field(None, description="balance字段")
    cardBalance: Optional[str] = Field(None, description="cardBalance字段")
    balanceCnt: Optional[str] = Field(None, description="balanceCnt字段")
    status: str = Field(..., description="状态")
    request_id: str = Field(..., description="请求ID")
    trace_id: Optional[str] = Field(None, description="追踪ID")
    request_data: Dict[str, Any] = Field(..., description="请求数据")
    response_data: Optional[Dict[str, Any]] = Field(None, description="响应数据")
    error_message: Optional[str] = Field(None, description="错误信息")
    retry_count: int = Field(0, description="重试次数")
    process_time: Optional[float] = Field(None, description="处理时间(秒)")
    ip_address: Optional[str] = Field(None, description="IP地址")
    callback_status: str = Field(..., description="回调状态")
    callback_result: Optional[str] = Field(None, description="回调结果描述")
    callback_time: Optional[datetime] = Field(None, description="回调时间")
    created_at: datetime = Field(..., description="创建时间")
    updated_at: Optional[datetime] = Field(None, description="更新时间")


# 卡记录列表
class CardRecordList(BaseModel):
    total: int = Field(..., description="总记录数")
    items: List[CardRecord] = Field(..., description="卡记录列表")


# 卡记录查询参数
class CardRecordQuery(BaseModel):
    merchant_id: Optional[int] = Field(None, description="商家ID")
    department_id: Optional[int] = Field(None, description="部门ID")
    status: Optional[str] = Field(None, description="状态")
    card_number: Optional[str] = Field(None, description="卡号")
    merchant_order_id: Optional[str] = Field(None, description="商家订单号")
    start_date: Optional[datetime] = Field(None, description="开始时间")
    end_date: Optional[datetime] = Field(None, description="结束时间")
    page: int = Field(1, description="页码", ge=1)
    page_size: int = Field(20, description="每页数量", ge=1, le=100)

    class Config:
        from_attributes = True


# 卡记录统计
class CardRecordStatistics(BaseModel):
    total_count: int = Field(0, description="总记录数")
    success_count: int = Field(0, description="成功数量")
    failed_count: int = Field(0, description="失败数量")
    pending_count: int = Field(0, description="待处理数量")
    processing_count: int = Field(0, description="处理中数量")
    total_amount: int = Field(0, description="总金额（分）")
    success_amount: int = Field(0, description="成功金额（分）")
    success_rate: float = Field(0.0, description="成功率")

    class Config:
        from_attributes = True
