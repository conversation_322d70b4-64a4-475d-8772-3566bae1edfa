#!/usr/bin/env python3
"""
Telegram Bot 主启动文件

独立运行的Telegram机器人服务
"""

import asyncio
import logging
import signal
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from app.core.logging import setup_logging, get_logger
from app.telegram_bot.services.bot_service import TelegramBotService
from app.telegram_bot.health_check import HealthCheckServer
from app.telegram_bot.metrics import MetricsServer

# 设置日志
setup_logging()
logger = get_logger(__name__)

class TelegramBotApp:
    """Telegram机器人应用主类"""
    
    def __init__(self):
        self.bot_service = None
        self.health_server = None
        self.metrics_server = None
        self.shutdown_event = asyncio.Event()
    
    async def startup(self):
        """启动应用"""
        try:
            logger.info("正在启动Telegram机器人服务...")
            
            # 初始化机器人服务
            self.bot_service = TelegramBotService()
            if not await self.bot_service.initialize():
                raise RuntimeError("机器人服务初始化失败")
            
            # 启动机器人服务
            if not await self.bot_service.start():
                raise RuntimeError("机器人服务启动失败")
            
            # 启动健康检查服务器
            self.health_server = HealthCheckServer(self.bot_service)
            await self.health_server.start()
            
            # 启动指标服务器
            self.metrics_server = MetricsServer(self.bot_service)
            await self.metrics_server.start()
            
            logger.info("Telegram机器人服务启动成功")
            
        except Exception as e:
            logger.error(f"启动失败: {e}", exc_info=True)
            await self.shutdown()
            raise
    
    async def shutdown(self):
        """关闭应用"""
        logger.info("正在关闭Telegram机器人服务...")
        
        # 设置关闭事件
        self.shutdown_event.set()
        
        # 关闭各个服务
        if self.metrics_server:
            await self.metrics_server.stop()
        
        if self.health_server:
            await self.health_server.stop()
        
        if self.bot_service:
            await self.bot_service.stop()
        
        logger.info("Telegram机器人服务已关闭")
    
    async def run(self):
        """运行应用"""
        # 设置信号处理
        def signal_handler(signum, frame):
            logger.info(f"收到信号 {signum}，准备关闭...")
            asyncio.create_task(self.shutdown())
        
        signal.signal(signal.SIGINT, signal_handler)
        signal.signal(signal.SIGTERM, signal_handler)
        
        try:
            # 启动应用
            await self.startup()
            
            # 等待关闭信号
            await self.shutdown_event.wait()
            
        except KeyboardInterrupt:
            logger.info("收到键盘中断信号")
        except Exception as e:
            logger.error(f"运行时错误: {e}", exc_info=True)
        finally:
            await self.shutdown()

async def main():
    """主函数"""
    app = TelegramBotApp()
    await app.run()

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        logger.info("程序被用户中断")
    except Exception as e:
        logger.error(f"程序异常退出: {e}", exc_info=True)
        sys.exit(1)
