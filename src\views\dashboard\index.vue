<template>
  <div class="dashboard-container">
    <!-- 核心业务统计 - 绑卡金额统计 -->
    <AmountStatistics ref="amountStatsRef" />

    <!-- 第二行：成功率分析和CK效率统计 -->
    <el-row :gutter="20" class="mb-4">
      <el-col :xs="24" :lg="12">
        <SuccessRateChart ref="successRateRef" />
      </el-col>
      <el-col :xs="24" :lg="12">
        <CkEfficiencyStats ref="ckEfficiencyRef" />
      </el-col>
    </el-row>

    <!-- 第三行：失败分析 -->
    <FailureAnalysis ref="failureAnalysisRef" />

    <!-- 第四行：部门排名（仅商户管理员可见） -->
    <DepartmentRanking ref="departmentRankingRef" v-if="!permissionStore.isSuperAdmin && userStore.merchantId" />
  </div>
</template>

<script setup>
import { ref, onMounted, watch, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { Shop, Money, TrendCharts, View } from '@element-plus/icons-vue'
import { useRouter } from 'vue-router'
import { useMerchantStore } from '@/store/modules/merchant'
import { useUserStore } from '@/store/modules/user'
import { usePermissionStore } from '@/store/modules/permission'

// 导入新的组件
import AmountStatistics from './components/AmountStatistics.vue'
import SuccessRateChart from './components/SuccessRateChart.vue'
import CkEfficiencyStats from './components/CkEfficiencyStats.vue'
import FailureAnalysis from './components/FailureAnalysis.vue'
import DepartmentRanking from './components/DepartmentRanking.vue'

// Store实例和Router
const router = useRouter()
const merchantStore = useMerchantStore()
const userStore = useUserStore()
const permissionStore = usePermissionStore()

// 组件引用
const amountStatsRef = ref(null)
const successRateRef = ref(null)
const ckEfficiencyRef = ref(null)
const failureAnalysisRef = ref(null)
const departmentRankingRef = ref(null)

// 方法
const refreshAllComponents = () => {
  // 刷新所有组件数据
  amountStatsRef.value?.refresh()
  successRateRef.value?.refresh()
  ckEfficiencyRef.value?.refresh()
  failureAnalysisRef.value?.refresh()
  departmentRankingRef.value?.refresh()
}

// 对账台导航方法
const goToReconciliation = () => {
  router.push({ name: 'Reconciliation' })
}

const goToReconciliationTest = () => {
  router.push({ name: 'ReconciliationTest' })
}

// 监听全局商户切换
watch(() => merchantStore.currentMerchant, () => {
  // 当全局商户发生变化时，刷新所有组件
  refreshAllComponents()
}, { immediate: true })

onMounted(() => {
  // 页面加载时刷新一次数据
  refreshAllComponents()
})
</script>

<style scoped>
.dashboard-container {
  padding: 4px 20px 20px 20px;
  background: #f5f7fa;
  min-height: 100vh;
}

/* 对账台入口卡片样式 */
.reconciliation-entry-card {
  border: none;
  transition: all 0.3s ease;
}

.reconciliation-entry-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.reconciliation-entry {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px 0;
}

.entry-content {
  display: flex;
  align-items: center;
  gap: 16px;
}

.entry-icon {
  width: 64px;
  height: 64px;
  border-radius: 12px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
}

.entry-info h3 {
  margin: 0 0 4px 0;
  font-size: 18px;
  font-weight: 600;
  color: #303133;
}

.entry-info p {
  margin: 0;
  font-size: 14px;
  color: #606266;
}

.entry-actions {
  display: flex;
  gap: 12px;
}



.mb-4 {
  margin-bottom: 16px;
}

.mr-1 {
  margin-right: 4px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .dashboard-container {
    padding: 12px;
  }
}

@media (max-width: 480px) {
  .dashboard-container {
    padding: 8px;
  }
}
</style>