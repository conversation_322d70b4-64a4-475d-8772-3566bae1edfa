#!/usr/bin/env python3
"""
Redis配置和连接管理
"""

import redis.asyncio as redis
from redis.asyncio import ConnectionPool
from typing import Optional
import asyncio
from app.core.config import settings
from app.core.logging import get_logger

logger = get_logger("redis_config")


class RedisConfig:
    """Redis配置类 - 使用现有系统配置"""

    # 使用现有的Redis配置
    REDIS_HOST = settings.REDIS_HOST
    REDIS_PORT = settings.REDIS_PORT
    REDIS_DB = settings.REDIS_DB
    REDIS_PASSWORD = settings.REDIS_PASSWORD

    # 构建Redis URL
    @classmethod
    def get_redis_url(cls) -> str:
        """构建Redis连接URL"""
        redis_url = f"redis://"

        # 如果有密码，添加密码
        if cls.REDIS_PASSWORD:
            redis_url += f":{cls.REDIS_PASSWORD}@"

        # 添加主机和端口
        redis_url += f"{cls.REDIS_HOST}:{cls.REDIS_PORT}/{cls.REDIS_DB}"

        return redis_url

    # 连接池配置
    REDIS_MAX_CONNECTIONS = getattr(settings, 'REDIS_MAX_CONNECTIONS', 50)
    REDIS_RETRY_ON_TIMEOUT = getattr(settings, 'REDIS_RETRY_ON_TIMEOUT', True)
    REDIS_DECODE_RESPONSES = getattr(settings, 'REDIS_DECODE_RESPONSES', True)

    # Redis集群配置（可选）
    REDIS_CLUSTER_NODES = getattr(settings, 'REDIS_CLUSTER_NODES', [])
    REDIS_USE_CLUSTER = getattr(settings, 'REDIS_USE_CLUSTER', False)

    # Redis哨兵配置（可选）
    REDIS_SENTINEL_HOSTS = getattr(settings, 'REDIS_SENTINEL_HOSTS', [])
    REDIS_SENTINEL_SERVICE = getattr(settings, 'REDIS_SENTINEL_SERVICE', 'walmart-redis')
    REDIS_USE_SENTINEL = getattr(settings, 'REDIS_USE_SENTINEL', False)
    
    # CK相关配置
    CK_LOCK_TTL = 30  # CK锁定时间（秒）
    CK_VALIDATION_CACHE_TTL = 300  # 验证缓存时间（秒）
    CK_STATUS_TTL = 1800  # CK状态缓存时间（秒）
    CK_STATS_TTL = 3600  # 统计数据缓存时间（秒）
    
    # 性能配置
    MAX_LOAD_RATIO = 0.8  # CK最大负载比例
    RECOVERY_INTERVAL = 300  # 恢复服务运行间隔（秒）
    SYNC_INTERVAL = 60  # 数据同步间隔（秒）


class RedisManager:
    """Redis连接管理器"""
    
    def __init__(self):
        self.pool: Optional[ConnectionPool] = None
        self.redis: Optional[redis.Redis] = None
        self.cluster: Optional[redis.RedisCluster] = None
        self.sentinel: Optional[redis.Sentinel] = None
        self._initialized = False
    
    async def initialize(self):
        """初始化Redis连接"""
        if self._initialized:
            return
        
        try:
            if RedisConfig.REDIS_USE_CLUSTER and RedisConfig.REDIS_CLUSTER_NODES:
                await self._init_cluster()
            elif RedisConfig.REDIS_USE_SENTINEL and RedisConfig.REDIS_SENTINEL_HOSTS:
                await self._init_sentinel()
            else:
                await self._init_single()
            
            self._initialized = True
            logger.info("Redis连接初始化成功")
            
        except Exception as e:
            logger.error(f"Redis连接初始化失败: {e}")
            raise
    
    async def _init_single(self):
        """初始化单机Redis连接"""
        redis_url = RedisConfig.get_redis_url()

        self.pool = ConnectionPool.from_url(
            redis_url,
            max_connections=RedisConfig.REDIS_MAX_CONNECTIONS,
            retry_on_timeout=RedisConfig.REDIS_RETRY_ON_TIMEOUT,
            decode_responses=RedisConfig.REDIS_DECODE_RESPONSES
        )
        self.redis = redis.Redis(connection_pool=self.pool)

        # 测试连接
        await self.redis.ping()
        logger.info(f"单机Redis连接成功: {RedisConfig.REDIS_HOST}:{RedisConfig.REDIS_PORT}/{RedisConfig.REDIS_DB}")
    
    async def _init_cluster(self):
        """初始化Redis集群连接"""
        self.cluster = redis.RedisCluster(
            startup_nodes=RedisConfig.REDIS_CLUSTER_NODES,
            decode_responses=RedisConfig.REDIS_DECODE_RESPONSES,
            skip_full_coverage_check=True,
            max_connections_per_node=RedisConfig.REDIS_MAX_CONNECTIONS // len(RedisConfig.REDIS_CLUSTER_NODES),
            retry_on_timeout=RedisConfig.REDIS_RETRY_ON_TIMEOUT
        )
        
        # 测试连接
        await self.cluster.ping()
        logger.info(f"Redis集群连接成功，节点数: {len(RedisConfig.REDIS_CLUSTER_NODES)}")
    
    async def _init_sentinel(self):
        """初始化Redis哨兵连接"""
        self.sentinel = redis.Sentinel(
            RedisConfig.REDIS_SENTINEL_HOSTS,
            decode_responses=RedisConfig.REDIS_DECODE_RESPONSES
        )
        
        # 获取主节点连接
        self.redis = self.sentinel.master_for(
            RedisConfig.REDIS_SENTINEL_SERVICE,
            socket_timeout=0.1,
            retry_on_timeout=RedisConfig.REDIS_RETRY_ON_TIMEOUT
        )
        
        # 测试连接
        await self.redis.ping()
        logger.info(f"Redis哨兵连接成功，服务: {RedisConfig.REDIS_SENTINEL_SERVICE}")
    
    async def get_client(self) -> redis.Redis:
        """获取Redis客户端"""
        if not self._initialized:
            await self.initialize()
        
        if self.cluster:
            return self.cluster
        elif self.redis:
            return self.redis
        else:
            raise RuntimeError("Redis客户端未初始化")
    
    async def close(self):
        """关闭Redis连接"""
        try:
            if self.redis:
                await self.redis.close()
            if self.cluster:
                await self.cluster.close()
            if self.pool:
                await self.pool.disconnect()
            
            self._initialized = False
            logger.info("Redis连接已关闭")
            
        except Exception as e:
            logger.error(f"关闭Redis连接失败: {e}")
    
    async def health_check(self) -> bool:
        """健康检查"""
        try:
            client = await self.get_client()
            await client.ping()
            return True
        except Exception as e:
            logger.error(f"Redis健康检查失败: {e}")
            return False


class RedisKeyManager:
    """Redis Key管理器"""
    
    @staticmethod
    def ck_pool_key(merchant_id: int) -> str:
        """商户CK池Key"""
        return f"walmart:ck:pool:{merchant_id}"
    
    @staticmethod
    def ck_dept_pool_key(merchant_id: int, department_id: int) -> str:
        """部门CK池Key"""
        return f"walmart:ck:dept:{merchant_id}:{department_id}"
    
    @staticmethod
    def ck_status_key(ck_id: int) -> str:
        """CK状态Key"""
        return f"walmart:ck:status:{ck_id}"
    
    @staticmethod
    def ck_lock_key(ck_id: int) -> str:
        """CK锁Key"""
        return f"walmart:ck:lock:{ck_id}"
    
    @staticmethod
    def ck_validation_key(ck_id: int) -> str:
        """CK验证缓存Key"""
        return f"walmart:ck:validation:{ck_id}"
    
    @staticmethod
    def ck_stats_key(merchant_id: int) -> str:
        """CK统计Key"""
        return f"walmart:ck:stats:{merchant_id}"
    
    @staticmethod
    def get_all_pool_keys() -> str:
        """获取所有池Key的模式"""
        return "walmart:ck:pool:*"
    
    @staticmethod
    def get_all_status_keys() -> str:
        """获取所有状态Key的模式"""
        return "walmart:ck:status:*"
    
    @staticmethod
    def get_all_lock_keys() -> str:
        """获取所有锁Key的模式"""
        return "walmart:ck:lock:*"
    
    @staticmethod
    def get_all_validation_keys() -> str:
        """获取所有验证缓存Key的模式"""
        return "walmart:ck:validation:*"


class RedisHealthMonitor:
    """Redis健康监控"""
    
    def __init__(self, redis_manager: RedisManager):
        self.redis_manager = redis_manager
        self.monitoring = False
    
    async def start_monitoring(self, interval: int = 30):
        """开始健康监控"""
        self.monitoring = True
        
        while self.monitoring:
            try:
                is_healthy = await self.redis_manager.health_check()
                
                if not is_healthy:
                    logger.error("Redis健康检查失败，尝试重新连接...")
                    await self.redis_manager.close()
                    await self.redis_manager.initialize()
                
                await asyncio.sleep(interval)
                
            except Exception as e:
                logger.error(f"Redis监控异常: {e}")
                await asyncio.sleep(interval)
    
    def stop_monitoring(self):
        """停止健康监控"""
        self.monitoring = False


# 全局Redis管理器实例
redis_manager = RedisManager()


async def get_redis_client() -> redis.Redis:
    """获取Redis客户端的便捷函数"""
    return await redis_manager.get_client()


async def init_redis():
    """初始化Redis连接的便捷函数"""
    await redis_manager.initialize()


async def close_redis():
    """关闭Redis连接的便捷函数"""
    await redis_manager.close()


# 依赖注入函数
async def get_redis_dependency():
    """FastAPI依赖注入函数"""
    try:
        client = await get_redis_client()
        yield client
    except Exception as e:
        logger.error(f"Redis依赖注入失败: {e}")
        raise


class RedisConnectionError(Exception):
    """Redis连接错误"""
    pass


class RedisOperationError(Exception):
    """Redis操作错误"""
    pass
