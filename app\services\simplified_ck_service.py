#!/usr/bin/env python3
"""
简化的CK选择服务 - 替换复杂的Redis机制
解决生产环境中所有绑卡请求使用同一个CK的负载均衡问题
"""

import secrets
import time
from typing import Optional, List, Dict, Any, Union
from sqlalchemy.orm import Session
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import and_, func, select

from app.models.walmart_ck import WalmartCK
from app.models.department import Department
from app.core.logging import get_logger
from app.utils.time_utils import get_current_time


class SimplifiedCKService:
    """
    简化的CK选择服务
    
    设计原则：
    1. 移除Redis缓存，直接使用数据库查询
    2. 使用数据库行锁确保并发安全
    3. 改进随机算法确保真正的负载均衡
    4. 保持商户和部门级别数据隔离
    """
    
    def __init__(self, db: Union[Session, AsyncSession], monitoring_service=None):
        self.db = db
        self.is_async = isinstance(db, AsyncSession)
        self.logger = get_logger("simplified_ck_service")
        self.monitoring_service = monitoring_service

    def _build_ck_base_query(self, merchant_id: int, department_id: Optional[int] = None):
        """
        构建CK选择的基础查询条件
        确保所有CK选择都遵循统一的条件：
        1. 必须是未删除的 (is_deleted == False)
        2. 必须是启用的 (active == True)
        3. CK所在部门必须是启用的 (Department.status == True)
        4. CK所在部门必须是允许进单的 (Department.enable_binding == True)
        5. CK所在部门的进单权重必须大于0 (Department.binding_weight > 0)
        6. CK未达到使用限制 (bind_count < total_limit)
        """
        if self.is_async:
            # 异步查询使用select语句
            stmt = select(WalmartCK).join(
                Department, WalmartCK.department_id == Department.id
            ).filter(
                # CK基本条件
                WalmartCK.merchant_id == merchant_id,
                WalmartCK.active == True,
                WalmartCK.bind_count < WalmartCK.total_limit,
                WalmartCK.is_deleted == False,
                # 部门状态条件
                Department.status == True,
                Department.enable_binding == True,
                Department.binding_weight > 0
            )

            if department_id:
                stmt = stmt.filter(WalmartCK.department_id == department_id)

            return stmt
        else:
            # 同步查询使用query方法
            query = self.db.query(WalmartCK).join(
                Department, WalmartCK.department_id == Department.id
            ).filter(
                # CK基本条件
                WalmartCK.merchant_id == merchant_id,
                WalmartCK.active == True,
                WalmartCK.bind_count < WalmartCK.total_limit,
                WalmartCK.is_deleted == False,
                # 部门状态条件
                Department.status == True,
                Department.enable_binding == True,
                Department.binding_weight > 0
            )

            if department_id:
                query = query.filter(WalmartCK.department_id == department_id)

            return query
    
    async def get_available_ck(
        self, 
        merchant_id: int, 
        department_id: Optional[int] = None,
        exclude_ids: Optional[List[int]] = None,
        bind_context: Optional[Dict[str, Any]] = None
    ) -> Optional[WalmartCK]:
        """
        获取可用CK - 简化版本，确保负载均衡
        
        Args:
            merchant_id: 商户ID（必须）
            department_id: 部门ID（可选）
            exclude_ids: 排除的CK ID列表
            bind_context: 绑卡上下文信息（用于日志）
        
        Returns:
            Optional[WalmartCK]: 可用的CK对象或None
        """
        start_time = time.time()
        ck = None
        success = False

        try:
            # 严格验证商户ID
            if not merchant_id:
                self.logger.error("商户ID不能为空，严格禁止跨商户CK使用")
                return None
            
            # 记录请求信息
            context_info = ""
            if bind_context:
                context_info = (
                    f" | record_id={bind_context.get('record_id')} | "
                    f"trace_id={bind_context.get('trace_id')}"
                )
            
            self.logger.info(
                f"开始CK选择: merchant_id={merchant_id}, "
                f"department_id={department_id}{context_info}"
            )
            
            # 如果指定了部门ID，直接从该部门获取
            if department_id:
                ck = await self._get_ck_from_department(
                    merchant_id, department_id, exclude_ids
                )
                if ck:
                    success = True
                    self.logger.info(
                        f"从指定部门选择CK: ck_id={ck.id}, "
                        f"department_id={department_id}, "
                        f"bind_count={ck.bind_count}/{ck.total_limit}{context_info}"
                    )
                    return ck
            
            # 使用权重算法选择部门和CK
            ck = await self._select_with_weight_algorithm(merchant_id, exclude_ids)
            
            if ck:
                success = True
                self.logger.info(
                    f"权重算法选择CK: ck_id={ck.id}, "
                    f"department_id={ck.department_id}, "
                    f"bind_count={ck.bind_count}/{ck.total_limit}{context_info}"
                )
            else:
                self.logger.warning(
                    f"未找到可用CK: merchant_id={merchant_id}{context_info}"
                )

            return ck
            
        except Exception as e:
            error_msg = f"CK选择失败: {e}"
            if bind_context:
                error_msg += (
                    f" | record_id={bind_context.get('record_id')} | "
                    f"trace_id={bind_context.get('trace_id')}"
                )
            self.logger.error(error_msg)
            return None
        finally:
            # 记录性能指标（如果有监控服务）
            response_time = (time.time() - start_time) * 1000  # 转换为毫秒
            if self.monitoring_service:
                self.monitoring_service.record_request(
                    success=success,
                    response_time=response_time,
                    ck_id=ck.id if ck else None,
                    department_id=ck.department_id if ck else department_id
                )
    
    async def _select_with_weight_algorithm(
        self,
        merchant_id: int,
        exclude_ids: Optional[List[int]] = None
    ) -> Optional[WalmartCK]:
        """使用权重算法选择CK - 修复版本：只从有可用CK的部门中选择"""
        try:
            # 1. 获取启用绑卡的部门
            if self.is_async:
                dept_stmt = select(Department).filter(
                    Department.merchant_id == merchant_id,
                    Department.status == True,
                    Department.enable_binding == True,
                    Department.binding_weight > 0
                )
                result = await self.db.execute(dept_stmt)
                departments = result.scalars().all()
            else:
                departments = self.db.query(Department).filter(
                    Department.merchant_id == merchant_id,
                    Department.status == True,
                    Department.enable_binding == True,
                    Department.binding_weight > 0
                ).all()

            if not departments:
                self.logger.warning(f"商户 {merchant_id} 没有启用绑卡的部门")
                return None

            # 2. 【关键修复】过滤出有可用CK的部门
            departments_with_ck = []
            for dept in departments:
                # 检查该部门是否有可用CK
                if self.is_async:
                    ck_stmt = select(WalmartCK).filter(
                        WalmartCK.merchant_id == merchant_id,
                        WalmartCK.department_id == dept.id,
                        WalmartCK.active == True,
                        WalmartCK.bind_count < WalmartCK.total_limit,
                        WalmartCK.is_deleted == False
                    ).limit(1)
                    result = await self.db.execute(ck_stmt)
                    has_available_ck = result.scalar_one_or_none() is not None
                else:
                    has_available_ck = self.db.query(WalmartCK).filter(
                        WalmartCK.merchant_id == merchant_id,
                        WalmartCK.department_id == dept.id,
                        WalmartCK.active == True,
                        WalmartCK.bind_count < WalmartCK.total_limit,
                        WalmartCK.is_deleted == False
                    ).first() is not None

                if has_available_ck:
                    departments_with_ck.append(dept)
                else:
                    self.logger.debug(f"部门 {dept.id}({dept.name}) 没有可用CK，跳过")

            if not departments_with_ck:
                self.logger.warning(f"商户 {merchant_id} 没有部门有可用CK")
                return None

            # 3. 从有CK的部门中使用权重算法选择
            selected_dept = self._select_department_by_weight(departments_with_ck)

            self.logger.debug(
                f"权重算法选择部门: {selected_dept.name} (ID: {selected_dept.id}, "
                f"权重: {selected_dept.binding_weight})"
            )

            # 4. 从选中部门获取负载最低的CK
            return await self._get_least_loaded_ck_from_department(
                merchant_id, selected_dept.id, exclude_ids
            )
            
        except Exception as e:
            self.logger.error(f"权重算法选择失败: {e}")
            return None
    
    def _select_department_by_weight(self, departments: List[Department]) -> Department:
        """
        改进的权重选择算法
        使用cryptographically secure random确保真正的随机性
        """
        if not departments:
            return None

        if len(departments) == 1:
            return departments[0]

        total_weight = sum(dept.binding_weight for dept in departments)

        if total_weight == 0:
            # 如果所有部门权重都为0，随机选择
            self.logger.warning("所有部门权重为0，使用随机选择")
            return secrets.choice(departments)

        # 使用secrets模块生成安全的随机数
        random_value = secrets.randbelow(total_weight)

        current_weight = 0
        for dept in departments:
            current_weight += dept.binding_weight
            if random_value < current_weight:
                self.logger.debug(
                    f"权重算法选择部门: {dept.name} (权重: {dept.binding_weight}/{total_weight})"
                )
                return dept

        # 回退选择（理论上不应该到达这里）
        return departments[-1]
    
    async def _get_ck_from_department(
        self,
        merchant_id: int,
        department_id: int,
        exclude_ids: Optional[List[int]] = None
    ) -> Optional[WalmartCK]:
        """从指定部门获取CK - 修复并发安全问题"""
        try:
            # 使用原子性CK选择服务确保并发安全
            atomic_service = AtomicCKUpdateService(self.db)

            # 原子性选择并预占用CK
            ck = await atomic_service.atomic_ck_selection_and_reserve(
                merchant_id=merchant_id,
                department_id=department_id,
                exclude_ids=exclude_ids
            )

            if ck:
                self.logger.info(
                    f"原子性选择CK成功: ck_id={ck.id}, "
                    f"department_id={department_id}, "
                    f"new_bind_count={ck.bind_count}"
                )

            return ck

        except Exception as e:
            self.logger.error(f"从部门 {department_id} 获取CK失败: {e}")
            return None
    
    async def _get_least_loaded_ck_from_department(
        self, 
        merchant_id: int, 
        department_id: int, 
        exclude_ids: Optional[List[int]] = None
    ) -> Optional[WalmartCK]:
        """从指定部门获取负载最低的CK"""
        return await self._get_ck_from_department(merchant_id, department_id, exclude_ids)
    
    async def get_ck_statistics(self, merchant_id: int) -> Dict[str, Any]:
        """获取CK统计信息，用于监控负载均衡效果"""
        try:
            if self.is_async:
                # 异步查询
                from sqlalchemy import func

                # 基础统计
                total_stmt = select(func.count(WalmartCK.id)).filter(
                    WalmartCK.merchant_id == merchant_id,
                    WalmartCK.is_deleted == False
                )
                result = await self.db.execute(total_stmt)
                total_cks = result.scalar()

                active_stmt = select(func.count(WalmartCK.id)).filter(
                    WalmartCK.merchant_id == merchant_id,
                    WalmartCK.active == True,
                    WalmartCK.is_deleted == False
                )
                result = await self.db.execute(active_stmt)
                active_cks = result.scalar()

                available_stmt = select(func.count(WalmartCK.id)).filter(
                    WalmartCK.merchant_id == merchant_id,
                    WalmartCK.active == True,
                    WalmartCK.bind_count < WalmartCK.total_limit,
                    WalmartCK.is_deleted == False
                )
                result = await self.db.execute(available_stmt)
                available_cks = result.scalar()

                # 负载分布统计
                load_stmt = select(
                    WalmartCK.id,
                    WalmartCK.bind_count,
                    WalmartCK.total_limit,
                    WalmartCK.department_id
                ).filter(
                    WalmartCK.merchant_id == merchant_id,
                    WalmartCK.active == True,
                    WalmartCK.is_deleted == False
                )
                result = await self.db.execute(load_stmt)
                load_distribution = result.all()
            else:
                # 同步查询
                total_cks = self.db.query(WalmartCK).filter(
                    WalmartCK.merchant_id == merchant_id,
                    WalmartCK.is_deleted == False
                ).count()

                active_cks = self.db.query(WalmartCK).filter(
                    WalmartCK.merchant_id == merchant_id,
                    WalmartCK.active == True,
                    WalmartCK.is_deleted == False
                ).count()

                available_cks = self.db.query(WalmartCK).filter(
                    WalmartCK.merchant_id == merchant_id,
                    WalmartCK.active == True,
                    WalmartCK.bind_count < WalmartCK.total_limit,
                    WalmartCK.is_deleted == False
                ).count()

                # 负载分布统计
                load_distribution = self.db.query(
                    WalmartCK.id,
                    WalmartCK.bind_count,
                    WalmartCK.total_limit,
                    WalmartCK.department_id
                ).filter(
                    WalmartCK.merchant_id == merchant_id,
                    WalmartCK.active == True,
                    WalmartCK.is_deleted == False
                ).all()

            return {
                "total_cks": total_cks,
                "active_cks": active_cks,
                "available_cks": available_cks,
                "load_distribution": [
                    {
                        "ck_id": ck.id,
                        "bind_count": ck.bind_count,
                        "total_limit": ck.total_limit,
                        "usage_rate": ck.bind_count / ck.total_limit if ck.total_limit > 0 else 0,
                        "department_id": ck.department_id
                    }
                    for ck in load_distribution
                ]
            }

        except Exception as e:
            self.logger.error(f"获取CK统计信息失败: {e}")
            return {}

    async def commit_ck_usage(self, ck_id: int, success: bool):
        """
        提交CK使用结果 - 委托给AtomicCKUpdateService
        为了保持接口一致性，SimplifiedCKService也提供此方法

        Args:
            ck_id: CK ID
            success: 绑卡是否成功
        """
        # 委托给AtomicCKUpdateService处理
        atomic_service = AtomicCKUpdateService(self.db)
        await atomic_service.commit_ck_usage(ck_id, success)


class AtomicCKUpdateService:
    """
    原子性CK更新服务
    确保CK使用计数的并发安全性
    """

    def __init__(self, db: Union[Session, AsyncSession]):
        self.db = db
        self.is_async = isinstance(db, AsyncSession)
        self.logger = get_logger("atomic_ck_update")
    
    async def atomic_ck_selection_and_reserve(
        self,
        merchant_id: int,
        department_id: Optional[int] = None,
        exclude_ids: Optional[List[int]] = None
    ) -> Optional[WalmartCK]:
        """
        原子性CK选择和预占用 - 防超卖版本
        在选择CK时立即预占用一个绑卡名额，确保不会超卖
        """
        max_retries = 3
        retry_count = 0

        while retry_count < max_retries:
            try:
                # 使用统一的CK选择条件
                simplified_service = SimplifiedCKService(self.db)
                stmt = simplified_service._build_ck_base_query(merchant_id, department_id)

                if exclude_ids:
                    if self.is_async:
                        stmt = stmt.filter(~WalmartCK.id.in_(exclude_ids))
                    else:
                        stmt = stmt.filter(~WalmartCK.id.in_(exclude_ids))

                # 【关键修复】使用行锁并在同一事务中预占用
                # 这样确保选择和预占用是原子性的，防止超卖
                if self.is_async:
                    stmt = stmt.with_for_update().order_by(WalmartCK.bind_count.asc()).limit(3)
                    result = await self.db.execute(stmt)
                    candidates = result.scalars().all()
                else:
                    candidates = stmt.with_for_update().order_by(WalmartCK.bind_count.asc()).limit(3).all()

                if not candidates:
                    self.logger.warning(f"没有可用的CK: merchant_id={merchant_id}, department_id={department_id}")
                    return None

                # 从候选CK中选择一个可以预占用的CK - 使用随机选择实现负载均衡
                available_candidates = []
                for candidate in candidates:
                    # 【防超卖检查】确保预占用后不会超过限制
                    if candidate.bind_count + 1 <= candidate.total_limit:
                        available_candidates.append(candidate)

                selected_ck = None
                if available_candidates:
                    # 【负载均衡改进】从可用候选中随机选择，而不是总选第一个
                    selected_ck = secrets.choice(available_candidates)
                    self.logger.debug(
                        f"从{len(available_candidates)}个候选CK中随机选择: {selected_ck.id}"
                    )

                if not selected_ck:
                    self.logger.warning(f"所有候选CK都已达到限制，重试选择")
                    retry_count += 1
                    continue

                # 【原子性预占用】立即增加计数，防止其他请求选择同一个CK
                original_count = selected_ck.bind_count
                selected_ck.bind_count += 1

                # 检查是否达到限制，自动禁用
                if selected_ck.bind_count >= selected_ck.total_limit:
                    selected_ck.active = False
                    self.logger.info(f"CK {selected_ck.id} 预占用后达到限制，自动禁用")

                # 立即提交预占用，确保其他请求看到最新状态
                if self.is_async:
                    await self.db.flush()
                else:
                    self.db.flush()

                self.logger.info(
                    f"原子性预占用CK: ck_id={selected_ck.id}, "
                    f"bind_count={original_count} -> {selected_ck.bind_count}/{selected_ck.total_limit}, "
                    f"active={selected_ck.active}"
                )

                return selected_ck

            except Exception as e:
                retry_count += 1
                self.logger.error(f"原子性CK选择失败 (重试 {retry_count}/{max_retries}): {e}")
                if self.is_async:
                    await self.db.rollback()
                else:
                    self.db.rollback()

                if retry_count >= max_retries:
                    return None

                # 短暂等待后重试
                import asyncio
                await asyncio.sleep(0.01 * retry_count)  # 递增等待时间

        return None
    
    async def commit_ck_usage(self, ck_id: int, success: bool):
        """
        提交CK使用结果 - 防超卖版本
        处理预占用后的绑卡结果，成功时确认使用，失败时回滚预占用

        Args:
            ck_id: CK ID
            success: 绑卡是否成功
        """
        try:
            # 使用行锁获取CK，确保并发安全
            if self.is_async:
                stmt = select(WalmartCK).filter(
                    WalmartCK.id == ck_id
                ).with_for_update()
                result = await self.db.execute(stmt)
                ck = result.scalar_one_or_none()
            else:
                ck = self.db.query(WalmartCK).filter(
                    WalmartCK.id == ck_id
                ).with_for_update().first()

            if not ck:
                self.logger.error(f"CK不存在: {ck_id}")
                return

            if success:
                # 绑卡成功：确认预占用，更新最后使用时间
                ck.last_bind_time = get_current_time().isoformat()

                self.logger.info(
                    f"绑卡成功，确认CK预占用: ck_id={ck_id}, "
                    f"bind_count={ck.bind_count}/{ck.total_limit}"
                )

                # 确保CK状态正确（如果达到限制应该被禁用）
                if ck.bind_count >= ck.total_limit and ck.active:
                    ck.active = False
                    self.logger.info(f"CK {ck_id} 达到限制({ck.total_limit})，确认禁用")
            else:
                # 【关键】绑卡失败：回滚预占用的计数
                if ck.bind_count > 0:
                    original_count = ck.bind_count
                    ck.bind_count -= 1

                    # 如果回滚后CK可以重新启用
                    if ck.bind_count < ck.total_limit and not ck.active:
                        ck.active = True
                        self.logger.info(f"CK {ck_id} 回滚后重新启用")

                    self.logger.info(
                        f"绑卡失败，回滚CK预占用: ck_id={ck_id}, "
                        f"bind_count={original_count} -> {ck.bind_count}"
                    )
                else:
                    self.logger.warning(f"CK {ck_id} bind_count已为0，无法回滚")

            if self.is_async:
                await self.db.commit()
            else:
                self.db.commit()

        except Exception as e:
            self.logger.error(f"提交CK使用结果失败: {e}")
            if self.is_async:
                await self.db.rollback()
            else:
                self.db.rollback()
