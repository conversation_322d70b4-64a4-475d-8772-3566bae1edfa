import { http } from '@/api/request'
import { API_URLS } from './config'

const { SYSTEM } = API_URLS

/**
 * 系统管理相关 API
 */
export const systemApi = {
    // 获取公开系统参数
    getPublicSystemParams() {
        return http.get(SYSTEM.PUBLIC_PARAMS).then(res => res.data)
    },

    // 获取系统参数
    getSystemParams() {
        return http.get(SYSTEM.SYSTEM_PARAMS).then(res => res.data)
    },

    // 更新系统参数
    updateSystemParams(params) {
        return http.put(SYSTEM.UPDATE_PARAMS, params).then(res => res.data)
    },

    // 获取系统状态
    getSystemStatus() {
        return http.get(SYSTEM.SYSTEM_STATUS).then(res => res.data)
    },

    // 获取系统日志
    getSystemLogs(params) {
        return http.get(SYSTEM.SYSTEM_LOGS, { params }).then(res => res.data)
    },

    // 清理系统日志
    clearSystemLogs() {
        return http.delete(SYSTEM.SYSTEM_LOGS).then(res => res.data)
    }
}

export default systemApi