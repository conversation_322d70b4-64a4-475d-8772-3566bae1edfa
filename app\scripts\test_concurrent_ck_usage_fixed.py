#!/usr/bin/env python3
"""
修复后的并发CK使用安全性测试
使用正确的并发测试方法验证record_ck_usage的线程安全性
"""

import asyncio
import sys
import os
import time
import threading
from concurrent.futures import ThreadPoolExecutor, as_completed
from typing import List, Dict

# 添加项目路径
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.insert(0, project_root)

from app.db.session import SessionLocal
from app.models.walmart_ck import WalmartCK
from app.services.walmart_ck_service_new import WalmartCKService
from app.core.logging import get_logger

logger = get_logger("test_concurrent_ck_usage_fixed")


class FixedConcurrentCKUsageTester:
    """修复后的并发CK使用测试器"""
    
    def __init__(self):
        pass
    
    def get_ck_state(self, ck_id: int) -> Dict:
        """获取CK状态（使用独立连接）"""
        db = SessionLocal()
        try:
            ck = db.query(WalmartCK).filter(WalmartCK.id == ck_id).first()
            if not ck:
                return None
            
            return {
                'ck_id': ck.id,
                'bind_count': ck.bind_count,
                'total_limit': ck.total_limit,
                'active': ck.active,
                'merchant_id': ck.merchant_id,
                'last_bind_time': ck.last_bind_time
            }
        finally:
            db.close()
    
    def single_ck_usage_call(self, ck_id: int, call_id: int) -> Dict:
        """单个CK使用调用（线程安全）"""
        db = SessionLocal()
        try:
            ck_service = WalmartCKService(db)
            
            start_time = time.time()
            result = ck_service.record_ck_usage(ck_id, True)
            duration = time.time() - start_time
            
            return {
                'call_id': call_id,
                'success': result,
                'duration': duration,
                'timestamp': time.time(),
                'thread_id': threading.current_thread().ident
            }
        except Exception as e:
            return {
                'call_id': call_id,
                'success': False,
                'error': str(e),
                'duration': 0,
                'timestamp': time.time(),
                'thread_id': threading.current_thread().ident
            }
        finally:
            db.close()
    
    def test_concurrent_ck_usage_fixed(
        self, 
        ck_id: int, 
        concurrent_count: int = 20,
        test_rounds: int = 2
    ):
        """修复后的并发CK使用测试"""
        print(f"🧪 修复后的并发CK使用安全性测试")
        print(f"CK ID: {ck_id}")
        print(f"并发数: {concurrent_count}")
        print(f"测试轮数: {test_rounds}")
        print("="*60)
        
        # 获取初始状态
        initial_state = self.get_ck_state(ck_id)
        if not initial_state:
            print(f"❌ CK {ck_id} 不存在")
            return
        
        print(f"📊 初始状态:")
        print(f"  bind_count: {initial_state['bind_count']}")
        print(f"  total_limit: {initial_state['total_limit']}")
        print(f"  active: {initial_state['active']}")
        
        total_expected_increments = 0
        
        for round_num in range(test_rounds):
            print(f"\n🎯 第 {round_num + 1} 轮测试:")
            
            # 记录轮次开始前的状态
            round_start_state = self.get_ck_state(ck_id)
            round_start_count = round_start_state['bind_count']
            
            # 执行并发测试
            start_time = time.time()
            results = self._execute_concurrent_usage_fixed(ck_id, concurrent_count)
            duration = time.time() - start_time
            
            # 等待一小段时间确保所有事务都已提交
            time.sleep(0.1)
            
            # 获取轮次结束后的状态
            round_end_state = self.get_ck_state(ck_id)
            round_end_count = round_end_state['bind_count']
            
            # 分析结果
            successful_calls = sum(1 for r in results if r['success'])
            failed_calls = len(results) - successful_calls
            actual_increment = round_end_count - round_start_count
            total_expected_increments += successful_calls
            
            print(f"  执行时间: {duration:.3f}秒")
            print(f"  成功调用: {successful_calls}")
            print(f"  失败调用: {failed_calls}")
            print(f"  QPS: {len(results)/duration:.1f}")
            print(f"  轮次开始bind_count: {round_start_count}")
            print(f"  轮次结束bind_count: {round_end_count}")
            print(f"  期望增量: {successful_calls}")
            print(f"  实际增量: {actual_increment}")
            
            if actual_increment == successful_calls:
                print(f"  ✅ 本轮数据一致性验证通过")
            else:
                print(f"  ❌ 本轮数据不一致！差异: {actual_increment - successful_calls}")
                
                # 详细分析失败情况
                if failed_calls > 0:
                    print(f"  📋 失败调用详情:")
                    for result in results:
                        if not result['success']:
                            print(f"    - 调用{result['call_id']}: {result.get('error', 'Unknown error')}")
            
            # 短暂休息
            time.sleep(0.2)
        
        # 最终验证
        self._final_verification_fixed(ck_id, initial_state, total_expected_increments)
    
    def _execute_concurrent_usage_fixed(self, ck_id: int, concurrent_count: int) -> List[Dict]:
        """执行并发CK使用（修复版本）"""
        results = []
        
        # 使用线程池执行并发调用
        with ThreadPoolExecutor(max_workers=min(concurrent_count, 50)) as executor:
            # 提交所有任务
            future_to_call_id = {
                executor.submit(self.single_ck_usage_call, ck_id, i): i 
                for i in range(concurrent_count)
            }
            
            # 等待所有任务完成
            for future in as_completed(future_to_call_id, timeout=30):
                try:
                    result = future.result()
                    results.append(result)
                except Exception as e:
                    call_id = future_to_call_id[future]
                    results.append({
                        'call_id': call_id,
                        'success': False,
                        'error': f"Future exception: {str(e)}",
                        'duration': 0,
                        'timestamp': time.time(),
                        'thread_id': None
                    })
        
        # 按call_id排序结果
        results.sort(key=lambda x: x['call_id'])
        return results
    
    def _final_verification_fixed(self, ck_id: int, initial_state: Dict, expected_increments: int):
        """最终验证（修复版本）"""
        print(f"\n📊 最终验证:")
        print("-"*40)
        
        # 等待确保所有事务都已完成
        time.sleep(0.5)
        
        final_state = self.get_ck_state(ck_id)
        
        expected_final_count = initial_state['bind_count'] + expected_increments
        actual_final_count = final_state['bind_count']
        
        print(f"初始bind_count: {initial_state['bind_count']}")
        print(f"期望增量: {expected_increments}")
        print(f"期望最终值: {expected_final_count}")
        print(f"实际最终值: {actual_final_count}")
        
        if actual_final_count == expected_final_count:
            print(f"✅ 并发安全性测试通过！")
            print(f"✅ 数据完全一致，record_ck_usage方法是线程安全的")
        else:
            difference = actual_final_count - expected_final_count
            print(f"❌ 并发安全性测试失败！")
            print(f"❌ 数据不一致，差异: {difference}")
            
            if difference > 0:
                print(f"⚠️ 可能存在重复计数问题")
            else:
                print(f"⚠️ 可能存在丢失计数问题")
        
        # 检查CK状态变化
        if initial_state['active'] != final_state['active']:
            print(f"🔄 CK状态变化: {initial_state['active']} -> {final_state['active']}")
            if not final_state['active']:
                print(f"🔒 CK已自动禁用（达到限制）")
    
    def test_sequential_baseline(self, ck_id: int, count: int = 10):
        """测试顺序调用作为基准"""
        print(f"\n📏 顺序调用基准测试:")
        print("-"*40)
        
        initial_state = self.get_ck_state(ck_id)
        initial_count = initial_state['bind_count']
        
        print(f"  初始bind_count: {initial_count}")
        print(f"  执行{count}次顺序调用...")
        
        successful_calls = 0
        start_time = time.time()
        
        for i in range(count):
            result = self.single_ck_usage_call(ck_id, i)
            if result['success']:
                successful_calls += 1
        
        duration = time.time() - start_time
        final_state = self.get_ck_state(ck_id)
        final_count = final_state['bind_count']
        
        print(f"  执行时间: {duration:.3f}秒")
        print(f"  成功调用: {successful_calls}")
        print(f"  最终bind_count: {final_count}")
        print(f"  实际增量: {final_count - initial_count}")
        
        if final_count - initial_count == successful_calls:
            print(f"  ✅ 顺序调用基准测试通过")
        else:
            print(f"  ❌ 顺序调用基准测试失败")


def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description="修复后的并发CK使用安全性测试")
    parser.add_argument("--ck-id", type=int, default=17, help="测试的CK ID")
    parser.add_argument("--concurrent", type=int, default=15, help="并发数")
    parser.add_argument("--rounds", type=int, default=2, help="测试轮数")
    parser.add_argument("--baseline", type=int, default=5, help="基准测试次数")
    
    args = parser.parse_args()
    
    tester = FixedConcurrentCKUsageTester()
    
    # 基准测试
    tester.test_sequential_baseline(args.ck_id, args.baseline)
    
    # 并发测试
    tester.test_concurrent_ck_usage_fixed(
        ck_id=args.ck_id,
        concurrent_count=args.concurrent,
        test_rounds=args.rounds
    )
    
    print(f"\n🎉 测试完成！")
    print(f"\n💡 如果测试通过，说明:")
    print(f"  ✅ record_ck_usage方法是线程安全的")
    print(f"  ✅ 使用了正确的行锁机制")
    print(f"  ✅ 数据一致性得到保障")
    print(f"  ✅ 可以安全地在高并发环境下使用")


if __name__ == "__main__":
    main()
