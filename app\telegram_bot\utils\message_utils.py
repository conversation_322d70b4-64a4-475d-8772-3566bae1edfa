"""
Telegram消息格式化工具
"""

import re
from typing import Any, Dict


def escape_markdown_v2(text: str) -> str:
    """
    转义MarkdownV2格式的特殊字符
    
    MarkdownV2需要转义的字符: _*[]()~`>#+-=|{}.!
    """
    if not text:
        return ""
    
    # 需要转义的字符
    escape_chars = r'_*[]()~`>#+-=|{}.!'
    
    # 转义每个特殊字符
    for char in escape_chars:
        text = text.replace(char, f'\\{char}')
    
    return text


def escape_html(text: str) -> str:
    """
    转义HTML格式的特殊字符
    """
    if not text:
        return ""
    
    # HTML需要转义的字符
    text = text.replace('&', '&amp;')
    text = text.replace('<', '&lt;')
    text = text.replace('>', '&gt;')
    text = text.replace('"', '&quot;')
    text = text.replace("'", '&#x27;')
    
    return text


def safe_format_number(value: Any, default: int = 0) -> str:
    """
    安全格式化数字，避免None值
    """
    try:
        if value is None:
            return str(default)
        return f"{int(value):,}"
    except (ValueError, TypeError):
        return str(default)


def safe_format_percentage(value: Any, default: float = 0.0, decimals: int = 2) -> str:
    """
    安全格式化百分比
    """
    try:
        if value is None:
            return f"{default:.{decimals}f}%"
        return f"{float(value):.{decimals}f}%"
    except (ValueError, TypeError):
        return f"{default:.{decimals}f}%"


def safe_format_ratio(numerator: Any, denominator: Any, default_text: str = "无限制") -> str:
    """
    安全格式化比例，处理无限制情况
    """
    try:
        if denominator is None or denominator == 0:
            return default_text
        if numerator is None:
            numerator = 0
        return f"{int(numerator)}/{int(denominator)}"
    except (ValueError, TypeError):
        return default_text


def format_ck_statistics_html(group, stats: Dict[str, Any]) -> str:
    """
    使用HTML格式化CK统计消息，避免Markdown解析问题
    """
    try:
        # 安全获取商户和部门名称
        merchant_name = escape_html(group.merchant.name if group.merchant else "未知商户")
        department_name = escape_html(group.department.name if group.department else "全部门")
        
        bind_stats = stats.get("bind_stats", {})
        
        # 使用HTML格式
        message = f"""🔧 <b>CK统计概览</b>

🏢 <b>商户</b>：{merchant_name}
🏬 <b>部门</b>：{department_name}

📊 <b>CK状态统计</b>：
• 总CK数量：{safe_format_number(stats.get('total_count', 0))} 个
• 启用CK：{safe_format_number(stats.get('active_count', 0))} 个
• 禁用CK：{safe_format_number(stats.get('inactive_count', 0))} 个
• 可用CK：{safe_format_number(stats.get('available_count', 0))} 个
• 已达限制：{safe_format_number(stats.get('expired_count', 0))} 个

⚡ <b>使用效率</b>：
• 总绑卡次数：{safe_format_number(bind_stats.get('total_bind_count', 0))} 次
• 成功绑卡：{safe_format_number(bind_stats.get('success_bind_count', 0))} 次
• 失败绑卡：{safe_format_number(bind_stats.get('failed_bind_count', 0))} 次
• 成功率：{safe_format_percentage(bind_stats.get('success_rate', 0))}"""
        
        # 添加CK详情
        ck_details = stats.get("ck_details", [])
        if ck_details:
            message += "\n\n🔍 <b>CK详情</b>（前10个）："
            for ck in ck_details:
                masked_sign = escape_html(str(ck.get('masked_sign', 'CK***')))
                status = escape_html(str(ck.get('status', '未知')))
                ratio = safe_format_ratio(ck.get('bind_count'), ck.get('total_limit'))
                message += f"\n• {masked_sign} - {status} ({ratio})"
        
        # 添加时间和提示
        from app.models.base import local_now
        current_time = local_now().strftime('%Y-%m-%d %H:%M:%S')
        message += f"\n\n🕐 <b>更新时间</b>：{current_time}"
        message += f"\n\n💡 <b>提示</b>：输入 <code>CK今日</code> 查看今日使用统计"
        
        return message
        
    except Exception as e:
        return f"❌ CK统计数据格式化失败：{escape_html(str(e))}"


def format_ck_usage_html(group, stats: Dict[str, Any], period_name: str) -> str:
    """
    使用HTML格式化CK使用统计消息
    """
    try:
        # 安全获取商户和部门名称
        merchant_name = escape_html(group.merchant.name if group.merchant else "未知商户")
        department_name = escape_html(group.department.name if group.department else "全部门")
        period_name = escape_html(period_name)
        period = escape_html(str(stats.get('period', '未知')))
        
        # 使用HTML格式
        message = f"""📈 <b>CK使用统计 - {period_name}</b>

🏢 <b>商户</b>：{merchant_name}
🏬 <b>部门</b>：{department_name}
📅 <b>时间段</b>：{period}

📊 <b>使用统计</b>：
• 总使用次数：{safe_format_number(stats.get('total_usage', 0))} 次
• 成功次数：{safe_format_number(stats.get('success_usage', 0))} 次
• 失败次数：{safe_format_number(stats.get('failed_usage', 0))} 次
• 成功率：{safe_format_percentage(stats.get('success_rate', 0))}

🔧 <b>CK活跃度</b>：
• 活跃CK数：{safe_format_number(stats.get('active_ck_count', 0))} 个
• 已使用CK：{safe_format_number(stats.get('used_ck_count', 0))} 个"""
        
        # 计算使用率
        active_count = stats.get('active_ck_count', 1)
        used_count = stats.get('used_ck_count', 0)
        if active_count > 0:
            usage_rate = (used_count / active_count) * 100
            message += f"\n• 使用率：{safe_format_percentage(usage_rate, decimals=1)}"
        else:
            message += f"\n• 使用率：0.0%"
        
        # 添加热门CK
        top_cks = stats.get("top_cks", [])
        if top_cks:
            message += "\n\n🏆 <b>使用量排行</b>："
            for i, ck in enumerate(top_cks, 1):
                masked_sign = escape_html(str(ck.get('masked_sign', 'CK***')))
                total_usage = safe_format_number(ck.get('total_usage', 0))
                success_rate = safe_format_percentage(ck.get('success_rate', 0), decimals=1)
                message += f"\n{i}. {masked_sign} - {total_usage}次 ({success_rate}成功率)"
        
        # 添加时间
        from app.models.base import local_now
        current_time = local_now().strftime('%Y-%m-%d %H:%M:%S')
        message += f"\n\n🕐 <b>更新时间</b>：{current_time}"
        
        return message
        
    except Exception as e:
        return f"❌ {escape_html(period_name)}CK使用统计格式化失败：{escape_html(str(e))}"


def format_plain_text(group, stats: Dict[str, Any]) -> str:
    """
    使用纯文本格式化CK统计消息，完全避免解析问题
    """
    try:
        merchant_name = group.merchant.name if group.merchant else "未知商户"
        department_name = group.department.name if group.department else "全部门"
        
        bind_stats = stats.get("bind_stats", {})
        
        message = f"""🔧 CK统计概览

🏢 商户：{merchant_name}
🏬 部门：{department_name}

📊 CK状态统计：
• 总CK数量：{safe_format_number(stats.get('total_count', 0))} 个
• 启用CK：{safe_format_number(stats.get('active_count', 0))} 个
• 禁用CK：{safe_format_number(stats.get('inactive_count', 0))} 个
• 可用CK：{safe_format_number(stats.get('available_count', 0))} 个
• 已达限制：{safe_format_number(stats.get('expired_count', 0))} 个

⚡ 使用效率：
• 总绑卡次数：{safe_format_number(bind_stats.get('total_bind_count', 0))} 次
• 成功绑卡：{safe_format_number(bind_stats.get('success_bind_count', 0))} 次
• 失败绑卡：{safe_format_number(bind_stats.get('failed_bind_count', 0))} 次
• 成功率：{safe_format_percentage(bind_stats.get('success_rate', 0))}"""
        
        # 添加CK详情
        ck_details = stats.get("ck_details", [])
        if ck_details:
            message += "\n\n🔍 CK详情（前10个）："
            for ck in ck_details:
                masked_sign = str(ck.get('masked_sign', 'CK***'))
                status = str(ck.get('status', '未知'))
                ratio = safe_format_ratio(ck.get('bind_count'), ck.get('total_limit'))
                message += f"\n• {masked_sign} - {status} ({ratio})"
        
        # 添加时间和提示
        from app.models.base import local_now
        current_time = local_now().strftime('%Y-%m-%d %H:%M:%S')
        message += f"\n\n🕐 更新时间：{current_time}"
        message += f"\n\n💡 提示：输入 CK今日 查看今日使用统计"
        
        return message
        
    except Exception as e:
        return f"❌ CK统计数据格式化失败：{str(e)}"
