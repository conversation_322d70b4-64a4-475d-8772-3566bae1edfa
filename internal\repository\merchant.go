package repository

import (
	"fmt"
	"strings"
	"time"
	"walmart-bind-card-gateway/internal/model"

	"gorm.io/gorm"
)

// merchantRepository 商户仓储实现
type merchantRepository struct {
	db *gorm.DB
}

// NewMerchantRepository 创建商户仓储
func NewMerchantRepository(db *gorm.DB) model.MerchantRepository {
	return &merchantRepository{
		db: db,
	}
}

// GetByAPIKey 根据API密钥获取商户（带重试机制）
func (r *merchantRepository) GetByAPIKey(apiKey string) (*model.Merchant, error) {
	var merchant model.Merchant

	// 重试机制：最多重试3次
	maxRetries := 3
	for i := 0; i < maxRetries; i++ {
		err := r.db.Where("api_key = ? AND status = ?", apiKey, true).First(&merchant).Error
		if err != nil {
			if err == gorm.ErrRecordNotFound {
				return nil, fmt.Errorf("商户不存在或已禁用")
			}

			// 检查是否是连接数过多错误
			if strings.Contains(err.Error(), "Too many connections") {
				if i < maxRetries-1 {
					// 等待一段时间后重试
					time.Sleep(time.Duration(i+1) * 100 * time.Millisecond)
					continue
				}
			}

			return nil, fmt.Errorf("查询商户失败: %w", err)
		}
		return &merchant, nil
	}

	return nil, fmt.Errorf("查询商户失败: 重试次数已用完")
}

// GetByID 根据ID获取商户
func (r *merchantRepository) GetByID(id int64) (*model.Merchant, error) {
	var merchant model.Merchant
	
	err := r.db.Where("id = ?", id).First(&merchant).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("商户不存在")
		}
		return nil, fmt.Errorf("查询商户失败: %w", err)
	}
	
	return &merchant, nil
}

// GetByCode 根据商户代码获取商户
func (r *merchantRepository) GetByCode(code string) (*model.Merchant, error) {
	var merchant model.Merchant
	
	err := r.db.Where("code = ? AND status = ?", code, true).First(&merchant).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("商户不存在或已禁用")
		}
		return nil, fmt.Errorf("查询商户失败: %w", err)
	}
	
	return &merchant, nil
}
