<template>
  <div class="telegram-users">
    <!-- 搜索和操作栏 -->
    <el-card class="search-card">
      <el-form :model="searchForm" inline>
        <el-form-item label="用户名">
          <el-input v-model="searchForm.telegram_username" placeholder="请输入Telegram用户名" clearable
            @keyup.enter="handleSearch" />
        </el-form-item>

        <el-form-item label="验证状态">
          <el-select style="width: 100px;" v-model="searchForm.verification_status" placeholder="请选择状态" clearable>
            <el-option label="待验证" value="pending" />
            <el-option label="已验证" value="verified" />
            <el-option label="验证失败" value="failed" />
            <el-option label="已拒绝" value="rejected" />
          </el-select>
        </el-form-item>

        <el-form-item label="关联用户">
          <el-input v-model="searchForm.system_username" placeholder="请输入系统用户名" clearable @keyup.enter="handleSearch" />
        </el-form-item>

        <el-form-item>
          <el-button type="primary" @click="handleSearch">
            <el-icon>
              <Search />
            </el-icon>
            搜索
          </el-button>
          <el-button @click="handleReset">
            <el-icon>
              <Refresh />
            </el-icon>
            重置
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 用户列表 -->
    <el-card>
      <template #header>
        <div class="card-header">
          <span>Telegram用户列表</span>
          <el-button type="primary" size="small" :loading="telegramStore.telegramUsersLoading" @click="handleRefresh">
            刷新
          </el-button>
        </div>
      </template>

      <el-table :data="telegramStore.telegramUsers" v-loading="telegramStore.telegramUsersLoading" stripe border>
        <el-table-column prop="telegram_user_id" label="Telegram ID" width="120" />

        <el-table-column label="用户信息" min-width="200">
          <template #default="{ row }">
            <div class="user-info">
              <div class="user-name">
                <span v-if="row.telegram_first_name">{{ row.telegram_first_name }}</span>
                <span v-if="row.telegram_last_name">{{ row.telegram_last_name }}</span>
              </div>
              <div class="username">@{{ row.telegram_username || '未设置' }}</div>
            </div>
          </template>
        </el-table-column>

        <el-table-column label="关联系统用户" width="150">
          <template #default="{ row }">
            {{ row.system_username || '-' }}
          </template>
        </el-table-column>

        <el-table-column prop="verification_status" label="验证状态" width="100">
          <template #default="{ row }">
            <el-tag :type="getVerificationStatusTagType(row.verification_status)">
              {{ getVerificationStatusText(row.verification_status) }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column prop="verification_time" label="验证时间" width="160">
          <template #default="{ row }">
            {{ row.verification_time ? formatDateTime(row.verification_time) : '-' }}
          </template>
        </el-table-column>

        <!-- 调试：显示token列 -->
        <el-table-column label="验证Token" width="200">
          <template #default="{ row }">
            <div style="font-family: monospace; font-size: 12px;">
              {{ row.verification_token || '-' }}
            </div>
            <div v-if="row.verification_token" style="font-size: 10px; color: #999;">
              长度: {{ row.verification_token.length }}
            </div>
          </template>
        </el-table-column>

        <el-table-column prop="last_active_time" label="最后活跃" width="160">
          <template #default="{ row }">
            {{ row.last_active_time ? formatDateTime(row.last_active_time) : '-' }}
          </template>
        </el-table-column>

        <el-table-column prop="created_at" label="创建时间" width="160">
          <template #default="{ row }">
            {{ formatDateTime(row.created_at) }}
          </template>
        </el-table-column>

        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <el-button type="primary" size="small" @click="handleViewDetail(row)">
              详情
            </el-button>
            <el-button v-if="row.verification_status === 'pending'" type="success" size="small"
              @click="handleVerify(row)">
              验证
            </el-button>
            <el-button type="danger" size="small" @click="handleDelete(row)">
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-wrapper">
        <el-pagination v-model:current-page="pagination.page" v-model:page-size="pagination.pageSize"
          :total="telegramStore.telegramUsersTotal" :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper" @size-change="handleSizeChange"
          @current-change="handleCurrentChange" />
      </div>
    </el-card>

    <!-- 用户详情对话框 -->
    <el-dialog v-model="showDetailDialog" title="用户详情" width="800px">
      <div v-if="selectedUser" class="user-detail">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="Telegram ID">
            {{ selectedUser.telegram_user_id }}
          </el-descriptions-item>
          <el-descriptions-item label="用户名">
            @{{ selectedUser.telegram_username || '未设置' }}
          </el-descriptions-item>
          <el-descriptions-item label="名字">
            {{ selectedUser.telegram_first_name || '-' }}
          </el-descriptions-item>
          <el-descriptions-item label="姓氏">
            {{ selectedUser.telegram_last_name || '-' }}
          </el-descriptions-item>
          <el-descriptions-item label="关联系统用户">
            {{ selectedUser.system_username || '-' }}
          </el-descriptions-item>
          <el-descriptions-item label="验证状态">
            <el-tag :type="getVerificationStatusTagType(selectedUser.verification_status)">
              {{ getVerificationStatusText(selectedUser.verification_status) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="验证时间">
            {{ selectedUser.verification_time ? formatDateTime(selectedUser.verification_time) : '-' }}
          </el-descriptions-item>
          <el-descriptions-item label="最后活跃">
            {{ selectedUser.last_active_time ? formatDateTime(selectedUser.last_active_time) : '-' }}
          </el-descriptions-item>
          <el-descriptions-item label="创建时间">
            {{ formatDateTime(selectedUser.created_at) }}
          </el-descriptions-item>
          <el-descriptions-item label="更新时间">
            {{ formatDateTime(selectedUser.updated_at) }}
          </el-descriptions-item>
        </el-descriptions>

        <!-- 用户设置 -->
        <div v-if="selectedUser.settings" class="user-settings">
          <h4>用户设置</h4>
          <el-descriptions :column="1" size="small">
            <el-descriptions-item v-for="(value, key) in selectedUser.settings" :key="key" :label="key">
              {{ value }}
            </el-descriptions-item>
          </el-descriptions>
        </div>

        <!-- 操作日志 -->
        <div class="user-logs">
          <h4>最近操作</h4>
          <el-table :data="userLogs" v-loading="logsLoading" size="small" max-height="300">
            <el-table-column prop="command" label="命令" width="100" />
            <el-table-column prop="message" label="内容" min-width="200" />
            <el-table-column prop="status" label="状态" width="80">
              <template #default="{ row }">
                <el-tag :type="row.status === 'success' ? 'success' : 'danger'" size="small">
                  {{ row.status === 'success' ? '成功' : '失败' }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="created_at" label="时间" width="160">
              <template #default="{ row }">
                {{ formatDateTime(row.created_at) }}
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>

      <template #footer>
        <el-button @click="showDetailDialog = false">关闭</el-button>
      </template>
    </el-dialog>

    <!-- 用户验证对话框 -->
    <el-dialog v-model="showVerifyDialog" title="用户验证" width="500px">
      <div v-if="verifyUser" class="verify-content">
        <el-alert title="确认验证用户" type="info" show-icon :closable="false" />

        <div class="verify-info">
          <p><strong>用户：</strong>{{ verifyUser.telegram_first_name }} {{ verifyUser.telegram_last_name }}</p>
          <p><strong>用户名：</strong>@{{ verifyUser.telegram_username }}</p>
          <p><strong>Telegram ID：</strong>{{ verifyUser.telegram_user_id }}</p>
        </div>

        <el-form :model="verifyForm" :rules="verifyFormRules" ref="verifyFormRef" label-width="120px">
          <el-form-item label="验证Token" prop="verification_token">
            <el-input v-model="verifyForm.verification_token" placeholder="请输入验证token" clearable style="width: 100%">
              <template #prepend>
                <el-icon>
                  <Key />
                </el-icon>
              </template>
              <template #append>
                <el-button @click="handleRefreshToken" :loading="refreshTokenLoading" size="small">
                  刷新
                </el-button>
              </template>
            </el-input>
            <div class="form-tip">
              <el-text size="small" type="info">
                验证令牌用于确认用户身份，通常由系统自动生成。如果token已过期，请点击"刷新"按钮重新生成。
              </el-text>
              <el-button type="info" size="small" text @click="handleDebugUser" style="margin-left: 8px;">
                调试信息
              </el-button>
            </div>
          </el-form-item>

          <el-form-item label="关联系统用户" prop="system_user_id">
            <el-select v-model="verifyForm.system_user_id" placeholder="请选择系统用户" filterable style="width: 100%">
              <el-option v-for="user in systemUsers" :key="user.id" :label="user.username" :value="user.id" />
            </el-select>
          </el-form-item>

          <el-form-item label="备注">
            <el-input v-model="verifyForm.remark" type="textarea" :rows="3" placeholder="请输入验证备注（可选）" />
          </el-form-item>
        </el-form>
      </div>

      <template #footer>
        <el-button @click="showVerifyDialog = false">取消</el-button>
        <el-button type="success" :loading="verifyLoading" @click="handleConfirmVerify">
          确认验证
        </el-button>
        <el-button type="danger" :loading="verifyLoading" @click="handleRejectVerify">
          拒绝验证
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Search, Refresh, Key } from '@element-plus/icons-vue'
import { useTelegramStore } from '@/store/modules/telegram'
import { telegramApi, userApi } from '@/api'

const telegramStore = useTelegramStore()

// 搜索表单
const searchForm = reactive({
  telegram_username: '',
  verification_status: '',
  system_username: ''
})

// 分页
const pagination = reactive({
  page: 1,
  pageSize: 20
})

// 对话框状态
const showDetailDialog = ref(false)
const showVerifyDialog = ref(false)
const logsLoading = ref(false)
const verifyLoading = ref(false)
const refreshTokenLoading = ref(false)

// 选中的用户
const selectedUser = ref(null)
const verifyUser = ref(null)
const userLogs = ref([])
const systemUsers = ref([])

// 验证表单
const verifyForm = reactive({
  verification_token: '',
  system_user_id: '',
  remark: ''
})

// 表单引用
const verifyFormRef = ref(null)

// 验证表单规则
const verifyFormRules = reactive({
  verification_token: [
    { required: true, message: '请输入验证token', trigger: 'blur' },
    { min: 10, message: '验证token长度不能少于10位', trigger: 'blur' }
  ],
  system_user_id: [
    { required: true, message: '请选择关联系统用户', trigger: 'change' }
  ]
})

// 获取验证状态标签类型
const getVerificationStatusTagType = (status) => {
  const typeMap = {
    'pending': 'warning',
    'verified': 'success',
    'failed': 'danger',
    'rejected': 'info'
  }
  return typeMap[status] || 'info'
}

// 获取验证状态文本
const getVerificationStatusText = (status) => {
  const textMap = {
    'pending': '待验证',
    'verified': '已验证',
    'failed': '验证失败',
    'rejected': '已拒绝'
  }
  return textMap[status] || status
}

// 格式化日期时间
const formatDateTime = (dateTime) => {
  if (!dateTime) return '-'
  return new Date(dateTime).toLocaleString('zh-CN')
}

// 搜索
const handleSearch = () => {
  pagination.page = 1
  loadUsers()
}

// 重置搜索
const handleReset = () => {
  Object.assign(searchForm, {
    telegram_username: '',
    verification_status: '',
    system_username: ''
  })
  pagination.page = 1
  loadUsers()
}

// 刷新
const handleRefresh = () => {
  loadUsers()
}

// 分页变化
const handleSizeChange = (size) => {
  pagination.pageSize = size
  pagination.page = 1
  loadUsers()
}

const handleCurrentChange = (page) => {
  pagination.page = page
  loadUsers()
}

// 查看详情
const handleViewDetail = async (row) => {
  selectedUser.value = row
  showDetailDialog.value = true

  // 加载用户操作日志
  try {
    logsLoading.value = true
    const response = await telegramApi.getUserLogs(row.id, { page: 1, page_size: 20 })
    userLogs.value = response.data?.items || response.items || []
  } catch (error) {
    ElMessage.error('获取用户日志失败：' + error.message)
  } finally {
    logsLoading.value = false
  }
}

// 验证用户
const handleVerify = async (row) => {
  verifyUser.value = row
  showVerifyDialog.value = true

  // 调试：输出用户数据
  console.log('打开验证对话框，用户数据:', row)
  console.log('用户verification_token:', row.verification_token)

  // 重置表单并设置默认值
  Object.assign(verifyForm, {
    verification_token: row.verification_token || '',
    system_user_id: '',
    remark: ''
  })

  console.log('表单初始化后的verification_token:', verifyForm.verification_token)

  // 清除表单验证状态
  if (verifyFormRef.value) {
    verifyFormRef.value.clearValidate()
  }

  // 加载系统用户列表（如果还没有加载）
  if (systemUsers.value.length === 0) {
    await loadSystemUsers()
  }
}

// 确认验证
const handleConfirmVerify = async () => {
  // 表单验证
  if (!verifyFormRef.value) return

  try {
    const valid = await verifyFormRef.value.validate()
    if (!valid) return
  } catch (error) {
    ElMessage.warning('请完善表单信息')
    return
  }

  try {
    verifyLoading.value = true

    // 记录调试信息
    console.log('提交验证请求:', {
      verification_token: verifyForm.verification_token,
      system_user_id: verifyForm.system_user_id,
      remark: verifyForm.remark
    })

    await telegramApi.verifyTelegramUser({
      verification_token: verifyForm.verification_token,
      system_user_id: verifyForm.system_user_id,
      remark: verifyForm.remark
    })

    showVerifyDialog.value = false
    ElMessage.success('用户验证成功')
    loadUsers()
  } catch (error) {
    console.error('验证失败详情:', error)

    // 提供更详细的错误信息
    let errorMessage = '验证失败'
    if (error.response?.data?.detail) {
      errorMessage += '：' + error.response.data.detail
    } else if (error.response?.status === 500) {
      errorMessage += '：服务器内部错误，请检查token是否有效或已过期'
    } else if (error.message) {
      errorMessage += '：' + error.message
    }

    ElMessage.error(errorMessage)
  } finally {
    verifyLoading.value = false
  }
}

// 刷新验证token
const handleRefreshToken = async () => {
  if (!verifyUser.value) return

  try {
    refreshTokenLoading.value = true

    const response = await telegramApi.refreshVerificationToken(verifyUser.value.id)

    // 更新表单中的token
    verifyForm.verification_token = response.new_token

    // 更新用户数据中的token（用于下次打开对话框）
    verifyUser.value.verification_token = response.new_token

    ElMessage.success('验证token已刷新')

  } catch (error) {
    console.error('刷新token失败:', error)

    let errorMessage = '刷新token失败'
    if (error.response?.data?.detail) {
      errorMessage += '：' + error.response.data.detail
    } else if (error.message) {
      errorMessage += '：' + error.message
    }

    ElMessage.error(errorMessage)
  } finally {
    refreshTokenLoading.value = false
  }
}

// 调试用户信息
const handleDebugUser = async () => {
  if (!verifyUser.value) return

  try {
    const debugInfo = await telegramApi.debugTelegramUser(verifyUser.value.id)

    // 在控制台输出详细信息
    console.log('=== 用户调试信息 ===')
    console.log('用户信息:', debugInfo.user_info)
    console.log('Token分析:', debugInfo.token_analysis)
    console.log('待验证用户数量:', debugInfo.pending_users_count)
    console.log('待验证用户列表:', debugInfo.pending_users)

    // 显示简化的调试信息
    const message = `
调试信息：
- 用户ID: ${debugInfo.user_info.id}
- Telegram用户ID: ${debugInfo.user_info.telegram_user_id}
- 验证状态: ${debugInfo.user_info.verification_status}
- Token: ${debugInfo.user_info.verification_token ? debugInfo.user_info.verification_token.substring(0, 8) + '...' : '无'}
- 创建时间: ${debugInfo.user_info.created_at}
- 相同Token用户数: ${debugInfo.token_analysis.users_with_same_token}
- 待验证用户总数: ${debugInfo.pending_users_count}

详细信息已输出到浏览器控制台，请按F12查看。
    `

    ElMessageBox.alert(message, '调试信息', {
      confirmButtonText: '确定',
      type: 'info'
    })

  } catch (error) {
    console.error('获取调试信息失败:', error)
    ElMessage.error('获取调试信息失败：' + (error.response?.data?.detail || error.message))
  }
}

// 拒绝验证
const handleRejectVerify = async () => {
  try {
    verifyLoading.value = true

    // 使用表单中的token而不是用户记录中的token
    const tokenToUse = verifyForm.verification_token || verifyUser.value.verification_token

    console.log('拒绝验证请求:', {
      verification_token: tokenToUse,
      reason: verifyForm.remark
    })

    await telegramApi.rejectTelegramUser({
      verification_token: tokenToUse,
      reason: verifyForm.remark
    })

    showVerifyDialog.value = false
    ElMessage.success('已拒绝用户验证')
    loadUsers()
  } catch (error) {
    console.error('拒绝验证失败详情:', error)

    let errorMessage = '拒绝验证失败'
    if (error.response?.data?.detail) {
      errorMessage += '：' + error.response.data.detail
    } else if (error.message) {
      errorMessage += '：' + error.message
    }

    ElMessage.error(errorMessage)
  } finally {
    verifyLoading.value = false
  }
}

// 删除用户
const handleDelete = async (row) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除用户 "${row.telegram_username || row.telegram_first_name}" 吗？`,
      '确认删除',
      { type: 'warning' }
    )

    await telegramApi.deleteTelegramUser(row.id)
    ElMessage.success('用户删除成功')
    loadUsers()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败：' + error.message)
    }
  }
}

// 加载用户列表
const loadUsers = async () => {
  try {
    const params = {
      page: pagination.page,
      page_size: pagination.pageSize,
      ...searchForm
    }

    // 过滤空值
    Object.keys(params).forEach(key => {
      if (params[key] === '' || params[key] === null || params[key] === undefined) {
        delete params[key]
      }
    })

    await telegramStore.fetchTelegramUsers(params)
  } catch (error) {
    ElMessage.error('加载用户列表失败：' + error.message)
  }
}

// 加载系统用户列表
const loadSystemUsers = async () => {
  try {
    const response = await userApi.getList({
      page: 1,
      page_size: 100,
      is_active: true  // 只获取活跃用户
    })
    systemUsers.value = response.items || response.data || []
  } catch (error) {
    console.error('加载系统用户失败:', error)
    ElMessage.error('加载系统用户失败：' + error.message)
    systemUsers.value = []
  }
}

// 初始化
onMounted(async () => {
  try {
    await Promise.all([
      loadUsers(),
      loadSystemUsers()
    ])
  } catch (error) {
    console.error('初始化失败:', error)
  }
})
</script>

<style scoped>
.telegram-users {
  padding: 20px;
}

.search-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.user-info {
  display: flex;
  flex-direction: column;
}

.user-name {
  font-weight: bold;
  margin-bottom: 4px;
}

.username {
  font-size: 12px;
  color: #999;
}

.pagination-wrapper {
  margin-top: 20px;
  text-align: right;
}

.user-detail {
  padding: 20px 0;
}

.user-settings,
.user-logs {
  margin-top: 24px;
}

.user-settings h4,
.user-logs h4 {
  margin: 0 0 16px 0;
  color: #333;
}

.verify-content {
  padding: 20px 0;
}

.verify-info {
  margin: 16px 0;
  padding: 16px;
  background: #f5f5f5;
  border-radius: 4px;
}

.verify-info p {
  margin: 8px 0;
}

.form-tip {
  margin-top: 4px;
}
</style>
