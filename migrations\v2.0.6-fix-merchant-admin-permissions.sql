-- =====================================================
-- 沃尔玛绑卡系统商户管理员权限修复脚本（生产环境安全版本）
--
-- 问题：商户管理员数据显示不一致
-- 原因：列表查询和统计查询使用了不同的数据隔离逻辑
-- 解决：确保商户管理员有正确的数据权限配置
--
-- 安全机制：
-- 1. 检查数据库初始化状态，避免在生产环境重复执行危险操作
-- 2. 使用条件执行，只在首次初始化或明确需要修复时执行
-- 3. 添加执行标记，防止重复执行
-- =====================================================

-- 设置连接字符集
SET NAMES utf8mb4 COLLATE utf8mb4_unicode_ci;
SET time_zone = '+08:00';

USE `walmart_card_db`;

-- 创建迁移日志表（如果不存在）
CREATE TABLE IF NOT EXISTS `migration_logs` (
    `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '日志ID',
    `migration_name` varchar(100) NOT NULL COMMENT '迁移名称',
    `status` varchar(20) NOT NULL DEFAULT 'started' COMMENT '状态',
    `message` text NULL COMMENT '消息',
    `data_summary` text NULL COMMENT '数据摘要',
    `created_at` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '创建时间',
    `completed_at` datetime(3) NULL COMMENT '完成时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_migration_logs_name` (`migration_name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='迁移日志表';

-- 检查是否已经执行过此修复脚本
SET @migration_exists = (SELECT COUNT(*) FROM `migration_logs` WHERE `migration_name` = 'merchant_admin_permissions_fix_v2.1.0');

-- 只在未执行过的情况下进行权限修复
SET @should_execute = (@migration_exists = 0);

-- 记录修复开始
INSERT IGNORE INTO `migration_logs` (`migration_name`, `status`, `message`, `created_at`)
VALUES ('merchant_admin_permissions_fix_v2.1.0', 'started', '开始修复商户管理员权限配置', NOW(3));

-- 1. 安全的商户管理员权限清理（仅在需要时执行）
SET @sql_cleanup_merchant = IF(@should_execute = 1,
    'DELETE FROM role_permissions WHERE role_id = (SELECT id FROM roles WHERE code = ''merchant_admin'') AND permission_id IN (SELECT id FROM permissions WHERE code IN (''data:user:own'', ''data:user:department'', ''data:user:all'', ''data:department:own'', ''data:department:all'', ''data:merchant:own'', ''data:merchant:all''));',
    'SELECT ''跳过商户管理员权限清理 - 已执行过修复'' as status;'
);

PREPARE stmt_cleanup_merchant FROM @sql_cleanup_merchant;
EXECUTE stmt_cleanup_merchant;
DEALLOCATE PREPARE stmt_cleanup_merchant;

-- 2. 为商户管理员分配正确的数据权限（仅在需要时执行）
-- 商户管理员应该能够：
-- - 访问本商户的所有数据（data:merchant:own）
-- - 访问本商户下所有部门的数据（data:department:all）
-- - 访问本商户下所有用户的数据（data:user:all）
SET @sql_assign_merchant = IF(@should_execute = 1,
    'INSERT IGNORE INTO role_permissions (role_id, permission_id, created_at) SELECT r.id, p.id, NOW(3) FROM roles r, permissions p WHERE r.code = ''merchant_admin'' AND p.code IN (''data:merchant:own'', ''data:department:all'', ''data:user:all'');',
    'SELECT ''跳过商户管理员权限分配 - 已执行过修复'' as status;'
);

PREPARE stmt_assign_merchant FROM @sql_assign_merchant;
EXECUTE stmt_assign_merchant;
DEALLOCATE PREPARE stmt_assign_merchant;

-- 3. 验证商户管理员权限配置（仅在执行修复时显示）
SET @sql_verify_merchant = IF(@should_execute = 1,
    'SELECT ''=== 商户管理员数据权限配置 ==='' as info, r.name as role_name, p.code as permission_code, p.name as permission_name FROM roles r JOIN role_permissions rp ON r.id = rp.role_id JOIN permissions p ON rp.permission_id = p.id WHERE r.code = ''merchant_admin'' AND p.code LIKE ''data:%'' ORDER BY p.code;',
    'SELECT ''商户管理员权限验证跳过 - 已执行过修复'' as info;'
);

PREPARE stmt_verify_merchant FROM @sql_verify_merchant;
EXECUTE stmt_verify_merchant;
DEALLOCATE PREPARE stmt_verify_merchant;

-- 4. 安全的CK供应商权限清理（仅在需要时执行）
SET @sql_cleanup_ck = IF(@should_execute = 1,
    'DELETE FROM role_permissions WHERE role_id = (SELECT id FROM roles WHERE code = ''ck_supplier'') AND permission_id IN (SELECT id FROM permissions WHERE code IN (''data:user:own'', ''data:user:department'', ''data:user:all'', ''data:department:own'', ''data:department:all'', ''data:merchant:own'', ''data:merchant:all''));',
    'SELECT ''跳过CK供应商权限清理 - 已执行过修复'' as status;'
);

PREPARE stmt_cleanup_ck FROM @sql_cleanup_ck;
EXECUTE stmt_cleanup_ck;
DEALLOCATE PREPARE stmt_cleanup_ck;

-- 5. 为CK供应商分配正确的数据权限（仅在需要时执行）
-- CK供应商应该只能：
-- - 访问本部门的数据（data:department:own）
-- - 访问本人的数据（data:user:own）
SET @sql_assign_ck = IF(@should_execute = 1,
    'INSERT IGNORE INTO role_permissions (role_id, permission_id, created_at) SELECT r.id, p.id, NOW(3) FROM roles r, permissions p WHERE r.code = ''ck_supplier'' AND p.code IN (''data:department:own'', ''data:user:own'');',
    'SELECT ''跳过CK供应商权限分配 - 已执行过修复'' as status;'
);

PREPARE stmt_assign_ck FROM @sql_assign_ck;
EXECUTE stmt_assign_ck;
DEALLOCATE PREPARE stmt_assign_ck;

-- 6. 验证CK供应商权限配置（仅在执行修复时显示）
SET @sql_verify_ck = IF(@should_execute = 1,
    'SELECT ''=== CK供应商数据权限配置 ==='' as info, r.name as role_name, p.code as permission_code, p.name as permission_name FROM roles r JOIN role_permissions rp ON r.id = rp.role_id JOIN permissions p ON rp.permission_id = p.id WHERE r.code = ''ck_supplier'' AND p.code LIKE ''data:%'' ORDER BY p.code;',
    'SELECT ''CK供应商权限验证跳过 - 已执行过修复'' as info;'
);

PREPARE stmt_verify_ck FROM @sql_verify_ck;
EXECUTE stmt_verify_ck;
DEALLOCATE PREPARE stmt_verify_ck;

-- 7. 验证权限配置是否正确（仅在执行修复时验证）
SET @sql_final_check = IF(@should_execute = 1,
    'SELECT ''=== 权限配置验证 ==='' as info; SELECT CASE WHEN COUNT(*) = 3 THEN ''✓ 商户管理员权限配置正确'' ELSE ''✗ 商户管理员权限配置错误'' END as merchant_admin_check FROM role_permissions rp JOIN roles r ON rp.role_id = r.id JOIN permissions p ON rp.permission_id = p.id WHERE r.code = ''merchant_admin'' AND p.code IN (''data:merchant:own'', ''data:department:all'', ''data:user:all''); SELECT CASE WHEN COUNT(*) = 2 THEN ''✓ CK供应商权限配置正确'' ELSE ''✗ CK供应商权限配置错误'' END as ck_supplier_check FROM role_permissions rp JOIN roles r ON rp.role_id = r.id JOIN permissions p ON rp.permission_id = p.id WHERE r.code = ''ck_supplier'' AND p.code IN (''data:department:own'', ''data:user:own'');',
    'SELECT ''权限配置验证跳过 - 已执行过修复'' as info;'
);

PREPARE stmt_final_check FROM @sql_final_check;
EXECUTE stmt_final_check;
DEALLOCATE PREPARE stmt_final_check;

-- 8. 显示修复后的完整权限配置（仅在执行修复时显示）
SET @sql_show_config = IF(@should_execute = 1,
    'SELECT ''=== 修复后的完整数据权限配置 ==='' as info; SELECT r.name as role_name, r.code as role_code, p.code as permission_code, p.name as permission_name, p.description as permission_description FROM roles r JOIN role_permissions rp ON r.id = rp.role_id JOIN permissions p ON rp.permission_id = p.id WHERE r.code IN (''merchant_admin'', ''ck_supplier'') AND p.code LIKE ''data:%'' ORDER BY r.code, p.code;',
    'SELECT ''权限配置显示跳过 - 已执行过修复'' as info;'
);

PREPARE stmt_show_config FROM @sql_show_config;
EXECUTE stmt_show_config;
DEALLOCATE PREPARE stmt_show_config;

-- 9. 记录修复完成
UPDATE `migration_logs`
SET `status` = 'completed',
    `message` = '商户管理员和CK供应商权限配置修复完成',
    `completed_at` = NOW(3),
    `data_summary` = JSON_OBJECT(
        'merchant_admin_permissions', (SELECT COUNT(*) FROM role_permissions rp JOIN roles r ON rp.role_id = r.id WHERE r.code = 'merchant_admin'),
        'ck_supplier_permissions', (SELECT COUNT(*) FROM role_permissions rp JOIN roles r ON rp.role_id = r.id WHERE r.code = 'ck_supplier'),
        'fix_executed', @should_execute,
        'completion_timestamp', NOW(3)
    )
WHERE `migration_name` = 'merchant_admin_permissions_fix_v2.1.0';

-- 10. 显示修复结果摘要
SELECT
    '=== 权限修复脚本执行完成 ===' as summary,
    CASE
        WHEN @should_execute = 1 THEN '✓ 权限修复已执行'
        ELSE '⚠ 权限修复已跳过（之前已执行）'
    END as execution_status,
    NOW(3) as completion_time;
