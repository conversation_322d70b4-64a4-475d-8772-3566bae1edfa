"""
Telegram权限管理API端点
支持分级管理、批量操作、权限模板等功能
"""

from typing import List, Optional, Dict, Any
from fastapi import APIRouter, Depends, HTTPException, status, Query
from pydantic import BaseModel
from sqlalchemy.orm import Session

from app.core.deps import get_db, get_current_user
from app.models.user import User
from app.models.telegram_user import TelegramUser
from app.models.telegram_permission_template import TelegramPermissionTemplate
from app.telegram_bot.services.enhanced_verification_service import EnhancedVerificationService
from app.telegram_bot.services.permission_template_service import PermissionTemplateService
from app.telegram_bot.config import get_bot_config

router = APIRouter()


class BatchVerificationRequest(BaseModel):
    """批量验证请求"""
    verification_requests: List[Dict[str, Any]]
    approval_level: str = "department"


class PermissionTemplateRequest(BaseModel):
    """权限模板请求"""
    template_name: str
    template_code: str
    description: Optional[str] = None
    settings: Dict[str, Any]


class BatchTemplateApplicationRequest(BaseModel):
    """批量应用模板请求"""
    user_ids: List[int]
    template_id: int


@router.post("/users/batch-verify")
async def batch_verify_users(
    request: BatchVerificationRequest,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    批量验证用户
    """
    try:
        # 检查权限
        if not current_user.has_permission("api:telegram:user:approve"):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="没有用户审批权限"
            )
        
        # 创建验证服务
        config = get_bot_config(db)
        verification_service = EnhancedVerificationService(db, config)
        
        # 执行批量验证
        results = await verification_service.batch_verify_users(
            request.verification_requests,
            current_user.id
        )
        
        return {
            "success": True,
            "message": f"批量验证完成，成功: {results['success_count']}, 失败: {results['failed_count']}",
            "data": results
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"批量验证失败: {str(e)}"
        )


@router.get("/users/pending-approvals")
async def get_pending_approvals(
    page: int = Query(1, ge=1),
    page_size: int = Query(20, ge=1, le=100),
    department_id: Optional[int] = Query(None),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    获取待审批用户列表（支持分级管理）
    """
    try:
        # 检查权限
        if not current_user.has_permission("api:telegram:user:approve"):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="没有用户审批权限"
            )
        
        # 构建查询
        query = db.query(TelegramUser).filter_by(
            verification_status="pending"
        )
        
        # 非超级管理员只能看到有权限管理的用户
        if not current_user.is_superuser:
            # 这里需要根据部门权限过滤
            # 具体实现取决于您的组织架构设计
            if department_id:
                # 检查是否有权限管理该部门
                if not current_user.can_manage_department(department_id):
                    raise HTTPException(
                        status_code=status.HTTP_403_FORBIDDEN,
                        detail="没有权限管理该部门用户"
                    )
        
        if department_id:
            # 这里需要关联查询，获取指定部门的用户
            pass
        
        # 分页
        total = query.count()
        users = query.offset((page - 1) * page_size).limit(page_size).all()
        
        return {
            "success": True,
            "data": {
                "items": [
                    {
                        "id": user.id,
                        "telegram_user_id": user.telegram_user_id,
                        "telegram_username": user.telegram_username,
                        "telegram_first_name": user.telegram_first_name,
                        "telegram_last_name": user.telegram_last_name,
                        "verification_token": user.verification_token,
                        "created_at": user.created_at,
                        "can_approve": current_user.is_superuser or 
                                     current_user.has_permission("api:telegram:user:approve")
                    }
                    for user in users
                ],
                "total": total,
                "page": page,
                "page_size": page_size,
                "pages": (total + page_size - 1) // page_size
            }
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取待审批用户失败: {str(e)}"
        )


@router.get("/permission-templates")
async def list_permission_templates(
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    获取权限模板列表
    """
    try:
        # 检查权限
        if not current_user.has_permission("api:telegram:config:read"):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="没有配置查看权限"
            )
        
        templates = db.query(TelegramPermissionTemplate).filter_by(
            is_active=True
        ).all()
        
        return {
            "success": True,
            "data": [
                {
                    "id": template.id,
                    "template_name": template.template_name,
                    "template_code": template.template_code,
                    "description": template.description,
                    "settings": template.settings,
                    "is_system": template.is_system,
                    "created_at": template.created_at
                }
                for template in templates
            ]
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取权限模板失败: {str(e)}"
        )


@router.post("/permission-templates")
async def create_permission_template(
    request: PermissionTemplateRequest,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    创建权限模板
    """
    try:
        # 检查权限
        if not current_user.has_permission("api:telegram:config:write"):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="没有配置修改权限"
            )
        
        template_service = PermissionTemplateService(db)
        
        template = template_service.create_custom_template(
            {
                "template_name": request.template_name,
                "template_code": request.template_code,
                "description": request.description,
                "settings": request.settings
            },
            current_user.id
        )
        
        return {
            "success": True,
            "message": "权限模板创建成功",
            "data": {
                "id": template.id,
                "template_name": template.template_name,
                "template_code": template.template_code
            }
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"创建权限模板失败: {str(e)}"
        )


@router.post("/permission-templates/batch-apply")
async def batch_apply_permission_template(
    request: BatchTemplateApplicationRequest,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    批量应用权限模板
    """
    try:
        # 检查权限
        if not current_user.has_permission("api:telegram:user:manage"):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="没有用户管理权限"
            )
        
        template_service = PermissionTemplateService(db)
        
        results = template_service.batch_apply_template(
            request.user_ids,
            request.template_id,
            current_user.id
        )
        
        return {
            "success": True,
            "message": f"批量应用完成，成功: {results['success_count']}, 失败: {results['failed_count']}",
            "data": results
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"批量应用权限模板失败: {str(e)}"
        )


@router.get("/users/{user_id}/permissions")
async def get_user_permissions(
    user_id: int,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    获取用户有效权限配置
    """
    try:
        # 检查权限
        if not current_user.has_permission("api:telegram:user:read"):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="没有用户查看权限"
            )
        
        telegram_user = db.query(TelegramUser).filter_by(id=user_id).first()
        if not telegram_user:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="用户不存在"
            )
        
        template_service = PermissionTemplateService(db)
        permissions = template_service.get_user_effective_permissions(telegram_user)
        
        return {
            "success": True,
            "data": {
                "user_id": user_id,
                "telegram_user_id": telegram_user.telegram_user_id,
                "permissions": permissions,
                "system_user": {
                    "id": telegram_user.system_user.id if telegram_user.system_user else None,
                    "username": telegram_user.system_user.username if telegram_user.system_user else None,
                    "roles": [role.name for role in telegram_user.system_user.roles] if telegram_user.system_user else []
                } if telegram_user.system_user else None
            }
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取用户权限失败: {str(e)}"
        )
