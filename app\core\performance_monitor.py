"""
性能监控模块 - 用于监控数据库查询性能和N+1查询检测
"""
import time
import logging
from typing import Dict, List, Any, Optional, Callable
from functools import wraps
from contextlib import contextmanager
from sqlalchemy.orm import Session
from sqlalchemy.engine import Engine
from sqlalchemy import event
import threading
from collections import defaultdict, deque
from datetime import datetime, timedelta

logger = logging.getLogger(__name__)


class QueryPerformanceMonitor:
    """查询性能监控器 - 增强版本"""

    def __init__(self, max_history: int = 1000):
        self.max_history = max_history
        self.query_history = deque(maxlen=max_history)
        self.slow_query_threshold = 1.0  # 慢查询阈值（秒）
        self.n_plus_1_threshold = 5  # N+1查询检测阈值
        self._lock = threading.Lock()
        self._request_queries = threading.local()

        # 新增监控指标
        self.cache_hit_count = 0
        self.cache_miss_count = 0
        self.total_requests = 0
        self.error_count = 0

    def record_cache_hit(self):
        """记录缓存命中"""
        with self._lock:
            self.cache_hit_count += 1

    def record_cache_miss(self):
        """记录缓存未命中"""
        with self._lock:
            self.cache_miss_count += 1

    def record_request(self):
        """记录请求"""
        with self._lock:
            self.total_requests += 1

    def record_error(self):
        """记录错误"""
        with self._lock:
            self.error_count += 1

    def get_cache_stats(self) -> Dict[str, Any]:
        """获取缓存统计"""
        total_cache_requests = self.cache_hit_count + self.cache_miss_count
        hit_rate = (self.cache_hit_count / total_cache_requests * 100) if total_cache_requests > 0 else 0

        return {
            'cache_hits': self.cache_hit_count,
            'cache_misses': self.cache_miss_count,
            'cache_hit_rate': round(hit_rate, 2),
            'total_requests': self.total_requests,
            'error_count': self.error_count,
            'error_rate': round(self.error_count / self.total_requests * 100, 2) if self.total_requests > 0 else 0
        }
        
    def start_request_tracking(self):
        """开始请求级别的查询跟踪"""
        self._request_queries.queries = []
        self._request_queries.start_time = time.time()
        
    def end_request_tracking(self) -> Dict[str, Any]:
        """结束请求级别的查询跟踪并返回统计信息"""
        if not hasattr(self._request_queries, 'queries'):
            return {}
            
        queries = self._request_queries.queries
        total_time = time.time() - self._request_queries.start_time
        
        # 分析查询模式
        analysis = self._analyze_queries(queries)
        
        # 清理
        delattr(self._request_queries, 'queries')
        delattr(self._request_queries, 'start_time')
        
        return {
            'total_queries': len(queries),
            'total_time': total_time,
            'slow_queries': analysis['slow_queries'],
            'potential_n_plus_1': analysis['potential_n_plus_1'],
            'duplicate_queries': analysis['duplicate_queries'],
        }
        
    def record_query(self, query: str, duration: float, params: Optional[Dict] = None):
        """记录查询信息"""
        query_info = {
            'query': query,
            'duration': duration,
            'params': params,
            'timestamp': datetime.now(),
        }
        
        with self._lock:
            self.query_history.append(query_info)
            
        # 记录到请求级别跟踪
        if hasattr(self._request_queries, 'queries'):
            self._request_queries.queries.append(query_info)
            
        # 检查慢查询
        if duration > self.slow_query_threshold:
            logger.warning(f"慢查询检测: {duration:.3f}s - {query[:100]}...")
            
    def _analyze_queries(self, queries: List[Dict]) -> Dict[str, Any]:
        """分析查询模式"""
        slow_queries = [q for q in queries if q['duration'] > self.slow_query_threshold]
        
        # 检测重复查询
        query_counts = defaultdict(int)
        for q in queries:
            # 简化查询字符串用于比较
            simplified = self._simplify_query(q['query'])
            query_counts[simplified] += 1
            
        duplicate_queries = {q: count for q, count in query_counts.items() if count > 1}
        
        # 检测潜在的N+1查询
        potential_n_plus_1 = []
        for simplified, count in query_counts.items():
            if count >= self.n_plus_1_threshold:
                potential_n_plus_1.append({
                    'query': simplified,
                    'count': count,
                    'severity': 'high' if count > 10 else 'medium'
                })
                
        return {
            'slow_queries': slow_queries,
            'duplicate_queries': duplicate_queries,
            'potential_n_plus_1': potential_n_plus_1,
        }
        
    def _simplify_query(self, query: str) -> str:
        """简化查询字符串用于模式匹配"""
        # 移除参数值，保留查询结构
        import re
        # 替换数字参数
        query = re.sub(r'\b\d+\b', '?', query)
        # 替换字符串参数
        query = re.sub(r"'[^']*'", "'?'", query)
        # 替换IN子句中的多个值
        query = re.sub(r'IN\s*\([^)]+\)', 'IN (?)', query, flags=re.IGNORECASE)
        return query.strip()
        
    def get_performance_summary(self, hours: int = 1) -> Dict[str, Any]:
        """获取性能摘要"""
        cutoff_time = datetime.now() - timedelta(hours=hours)
        
        with self._lock:
            recent_queries = [
                q for q in self.query_history 
                if q['timestamp'] > cutoff_time
            ]
            
        if not recent_queries:
            return {}
            
        total_queries = len(recent_queries)
        total_duration = sum(q['duration'] for q in recent_queries)
        avg_duration = total_duration / total_queries
        slow_queries = [q for q in recent_queries if q['duration'] > self.slow_query_threshold]
        
        return {
            'total_queries': total_queries,
            'total_duration': total_duration,
            'avg_duration': avg_duration,
            'slow_query_count': len(slow_queries),
            'slow_query_rate': len(slow_queries) / total_queries * 100,
        }


# 全局性能监控器实例
performance_monitor = QueryPerformanceMonitor()


def setup_sqlalchemy_monitoring(engine: Engine):
    """设置SQLAlchemy查询监控"""
    
    @event.listens_for(engine, "before_cursor_execute")
    def receive_before_cursor_execute(conn, cursor, statement, parameters, context, executemany):
        context._query_start_time = time.time()
        
    @event.listens_for(engine, "after_cursor_execute")
    def receive_after_cursor_execute(conn, cursor, statement, parameters, context, executemany):
        duration = time.time() - context._query_start_time
        performance_monitor.record_query(statement, duration, parameters)


@contextmanager
def monitor_request_performance():
    """请求性能监控上下文管理器"""
    performance_monitor.start_request_tracking()
    try:
        yield
    finally:
        stats = performance_monitor.end_request_tracking()
        if stats.get('potential_n_plus_1'):
            logger.warning(f"检测到潜在N+1查询: {stats}")


def performance_test(name: str = None):
    """性能测试装饰器"""
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        def wrapper(*args, **kwargs):
            test_name = name or f"{func.__module__}.{func.__name__}"
            start_time = time.time()
            
            with monitor_request_performance():
                result = func(*args, **kwargs)
                
            duration = time.time() - start_time
            stats = performance_monitor.end_request_tracking()
            
            logger.info(f"性能测试 [{test_name}]: {duration:.3f}s, 查询数: {stats.get('total_queries', 0)}")
            
            if stats.get('potential_n_plus_1'):
                logger.warning(f"N+1查询警告 [{test_name}]: {stats['potential_n_plus_1']}")
                
            return result
        return wrapper
    return decorator


class PerformanceProfiler:
    """性能分析器"""
    
    def __init__(self):
        self.profiles = {}
        
    def profile_function(self, func_name: str):
        """函数性能分析装饰器"""
        def decorator(func: Callable) -> Callable:
            @wraps(func)
            def wrapper(*args, **kwargs):
                start_time = time.time()
                result = func(*args, **kwargs)
                duration = time.time() - start_time
                
                if func_name not in self.profiles:
                    self.profiles[func_name] = []
                    
                self.profiles[func_name].append(duration)
                
                # 保持最近100次调用的记录
                if len(self.profiles[func_name]) > 100:
                    self.profiles[func_name] = self.profiles[func_name][-100:]
                    
                return result
            return wrapper
        return decorator
        
    def get_profile_stats(self, func_name: str) -> Dict[str, float]:
        """获取函数性能统计"""
        if func_name not in self.profiles:
            return {}
            
        durations = self.profiles[func_name]
        return {
            'count': len(durations),
            'total': sum(durations),
            'avg': sum(durations) / len(durations),
            'min': min(durations),
            'max': max(durations),
        }


# 全局性能分析器实例
profiler = PerformanceProfiler()
