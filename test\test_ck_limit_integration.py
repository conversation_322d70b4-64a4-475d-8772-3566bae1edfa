#!/usr/bin/env python3
"""
CK总次数限制功能集成测试
测试完整的CK管理机制修改
"""

import sys
import os
import asyncio
import json
from datetime import datetime
from typing import Dict, Any, List

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app.db.session import SessionLocal
from app.models.walmart_ck import WalmartCK
from app.models.merchants import Merchant
from app.models.departments import Department
from app.models.users import User
from app.services.walmart_ck_service_new import WalmartCKService
from app.services.ck_validation_service import CKValidationService
from app.core.logging import get_logger

logger = get_logger("ck_limit_integration_test")


class CKLimitIntegrationTest:
    """CK总次数限制功能集成测试类"""

    def __init__(self):
        self.db = SessionLocal()
        self.test_data = {}
        self.test_results = []

    def __enter__(self):
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        self.cleanup()
        self.db.close()

    def cleanup(self):
        """清理测试数据"""
        try:
            # 删除测试创建的CK
            if 'test_cks' in self.test_data:
                for ck_id in self.test_data['test_cks']:
                    ck = self.db.query(WalmartCK).filter(WalmartCK.id == ck_id).first()
                    if ck:
                        self.db.delete(ck)

            # 删除测试用户
            if 'test_user' in self.test_data:
                user = self.db.query(User).filter(User.id == self.test_data['test_user']).first()
                if user:
                    self.db.delete(user)

            # 删除测试部门
            if 'test_department' in self.test_data:
                dept = self.db.query(Department).filter(Department.id == self.test_data['test_department']).first()
                if dept:
                    self.db.delete(dept)

            # 删除测试商户
            if 'test_merchant' in self.test_data:
                merchant = self.db.query(Merchant).filter(Merchant.id == self.test_data['test_merchant']).first()
                if merchant:
                    self.db.delete(merchant)

            self.db.commit()
            logger.info("测试数据清理完成")

        except Exception as e:
            self.db.rollback()
            logger.error(f"清理测试数据失败: {e}")

    def create_test_data(self):
        """创建测试数据"""
        try:
            # 创建测试商户
            merchant = Merchant(
                name="CK限制测试商户",
                code="CK_LIMIT_TEST",
                status="active"
            )
            self.db.add(merchant)
            self.db.commit()
            self.db.refresh(merchant)
            self.test_data['test_merchant'] = merchant.id

            # 创建测试部门
            department = Department(
                name="CK限制测试部门",
                code="CK_LIMIT_DEPT",
                merchant_id=merchant.id,
                status="active"
            )
            self.db.add(department)
            self.db.commit()
            self.db.refresh(department)
            self.test_data['test_department'] = department.id

            # 创建测试CK
            test_cks = []
            for i in range(5):
                ck = WalmartCK(
                    sign=f"test_limit_ck_{i}_{datetime.now().timestamp()}",
                    total_limit=10,  # 设置较小的限制便于测试
                    bind_count=i * 2,  # 不同的初始使用次数
                    active=True,
                    merchant_id=merchant.id,
                    department_id=department.id,
                    is_deleted=False,
                    description=f"CK限制测试_{i}"
                )
                self.db.add(ck)
                test_cks.append(ck)

            self.db.commit()
            
            # 刷新并保存CK ID
            self.test_data['test_cks'] = []
            for ck in test_cks:
                self.db.refresh(ck)
                self.test_data['test_cks'].append(ck.id)

            logger.info(f"创建测试数据完成: 商户{merchant.id}, 部门{department.id}, CK数量{len(test_cks)}")
            return True

        except Exception as e:
            self.db.rollback()
            logger.error(f"创建测试数据失败: {e}")
            return False

    def test_total_limit_enforcement(self):
        """测试总次数限制的执行"""
        logger.info("开始测试总次数限制执行...")
        
        try:
            ck_service = WalmartCKService(self.db)
            test_results = []

            for ck_id in self.test_data['test_cks']:
                ck = self.db.query(WalmartCK).filter(WalmartCK.id == ck_id).first()
                initial_count = ck.bind_count
                initial_active = ck.active

                # 模拟绑卡直到达到限制
                while ck.bind_count < ck.total_limit:
                    result = ck_service.record_ck_usage(ck_id, True)
                    if not result:
                        break
                    self.db.refresh(ck)

                # 验证CK状态
                final_active = ck.active
                final_count = ck.bind_count

                test_result = {
                    "ck_id": ck_id,
                    "initial_count": initial_count,
                    "final_count": final_count,
                    "total_limit": ck.total_limit,
                    "initial_active": initial_active,
                    "final_active": final_active,
                    "auto_disabled": final_count >= ck.total_limit and not final_active
                }
                test_results.append(test_result)

                logger.info(f"CK {ck_id}: {initial_count}->{final_count}/{ck.total_limit}, 活跃状态: {initial_active}->{final_active}")

            # 验证所有CK都正确处理了限制
            all_passed = all(r["auto_disabled"] for r in test_results if r["final_count"] >= r["total_limit"])
            
            self.test_results.append({
                "test_name": "total_limit_enforcement",
                "passed": all_passed,
                "details": test_results
            })

            logger.info(f"总次数限制测试完成，结果: {'通过' if all_passed else '失败'}")
            return all_passed

        except Exception as e:
            logger.error(f"总次数限制测试失败: {e}")
            self.test_results.append({
                "test_name": "total_limit_enforcement",
                "passed": False,
                "error": str(e)
            })
            return False

    async def test_ck_validation_integration(self):
        """测试CK验证集成功能"""
        logger.info("开始测试CK验证集成功能...")
        
        try:
            validation_service = CKValidationService(self.db)
            ck_service = WalmartCKService(self.db)

            # 获取测试CK
            test_cks = []
            for ck_id in self.test_data['test_cks']:
                ck = self.db.query(WalmartCK).filter(WalmartCK.id == ck_id).first()
                if ck and ck.active:
                    test_cks.append(ck)

            if not test_cks:
                logger.warning("没有可用的测试CK")
                return False

            # 测试单个CK验证（模拟）
            # 注意：这里我们模拟验证过程，因为实际API调用需要真实的CK
            mock_validation_results = []
            for i, ck in enumerate(test_cks):
                # 模拟不同的验证结果（使用实际API响应格式）
                if i % 2 == 0:  # 偶数索引的CK验证成功
                    mock_result = {
                        "is_valid": True,
                        "error_message": None,
                        "error_code": None,
                        "should_disable": False,
                        "log_id": f"SUCCESS_{i}",
                        "card_count": i + 1,
                        "nick_name": f"测试用户{i}"
                    }
                else:  # 奇数索引的CK验证失败
                    mock_result = {
                        "is_valid": False,
                        "error_message": "模拟验证失败",
                        "error_code": "401",
                        "should_disable": True,
                        "log_id": f"FAIL_{i}"
                    }
                    # 手动禁用CK以模拟验证失败的处理
                    ck.active = False
                    self.db.commit()

                mock_validation_results.append({
                    "ck_id": ck.id,
                    "validation_result": mock_result
                })

            # 测试带验证的CK选择
            selected_ck = await ck_service.get_available_ck(
                merchant_id=self.test_data['test_merchant'],
                department_id=self.test_data['test_department'],
                validate_ck=False  # 暂时关闭验证，因为我们使用模拟数据
            )

            # 验证选择结果
            selection_success = selected_ck is not None and selected_ck.active

            self.test_results.append({
                "test_name": "ck_validation_integration",
                "passed": selection_success,
                "details": {
                    "validation_results": mock_validation_results,
                    "selected_ck_id": selected_ck.id if selected_ck else None,
                    "selected_ck_active": selected_ck.active if selected_ck else None
                }
            })

            logger.info(f"CK验证集成测试完成，结果: {'通过' if selection_success else '失败'}")
            return selection_success

        except Exception as e:
            logger.error(f"CK验证集成测试失败: {e}")
            self.test_results.append({
                "test_name": "ck_validation_integration",
                "passed": False,
                "error": str(e)
            })
            return False

    def test_ck_availability_check(self):
        """测试CK可用性检查逻辑"""
        logger.info("开始测试CK可用性检查...")
        
        try:
            ck_service = WalmartCKService(self.db)
            test_results = []

            for ck_id in self.test_data['test_cks']:
                ck = self.db.query(WalmartCK).filter(WalmartCK.id == ck_id).first()
                
                # 测试正常状态
                normal_available = ck_service._is_ck_available(ck)
                
                # 测试软删除状态
                ck.is_deleted = True
                deleted_available = ck_service._is_ck_available(ck)
                ck.is_deleted = False
                
                # 测试禁用状态
                ck.active = False
                disabled_available = ck_service._is_ck_available(ck)
                ck.active = True
                
                # 测试达到限制状态
                original_count = ck.bind_count
                ck.bind_count = ck.total_limit
                limit_reached_available = ck_service._is_ck_available(ck)
                ck.bind_count = original_count

                test_result = {
                    "ck_id": ck_id,
                    "normal_available": normal_available,
                    "deleted_available": deleted_available,
                    "disabled_available": disabled_available,
                    "limit_reached_available": limit_reached_available,
                    "all_checks_correct": (
                        normal_available and 
                        not deleted_available and 
                        not disabled_available and 
                        not limit_reached_available
                    )
                }
                test_results.append(test_result)

            all_passed = all(r["all_checks_correct"] for r in test_results)
            
            self.test_results.append({
                "test_name": "ck_availability_check",
                "passed": all_passed,
                "details": test_results
            })

            logger.info(f"CK可用性检查测试完成，结果: {'通过' if all_passed else '失败'}")
            return all_passed

        except Exception as e:
            logger.error(f"CK可用性检查测试失败: {e}")
            self.test_results.append({
                "test_name": "ck_availability_check",
                "passed": False,
                "error": str(e)
            })
            return False

    def generate_test_report(self):
        """生成测试报告"""
        total_tests = len(self.test_results)
        passed_tests = sum(1 for r in self.test_results if r["passed"])
        
        report = {
            "test_summary": {
                "total_tests": total_tests,
                "passed_tests": passed_tests,
                "failed_tests": total_tests - passed_tests,
                "success_rate": f"{passed_tests/total_tests*100:.1f}%" if total_tests > 0 else "0%"
            },
            "test_details": self.test_results,
            "test_data": self.test_data,
            "timestamp": datetime.now().isoformat()
        }
        
        return report

    async def run_all_tests(self):
        """运行所有测试"""
        logger.info("开始CK总次数限制功能集成测试...")
        
        # 创建测试数据
        if not self.create_test_data():
            logger.error("创建测试数据失败，终止测试")
            return False

        # 运行各项测试
        tests = [
            ("CK可用性检查", self.test_ck_availability_check),
            ("总次数限制执行", self.test_total_limit_enforcement),
            ("CK验证集成", self.test_ck_validation_integration),
        ]

        for test_name, test_func in tests:
            logger.info(f"执行测试: {test_name}")
            try:
                if asyncio.iscoroutinefunction(test_func):
                    await test_func()
                else:
                    test_func()
            except Exception as e:
                logger.error(f"测试 {test_name} 执行异常: {e}")

        # 生成测试报告
        report = self.generate_test_report()
        
        # 输出测试结果
        print("\n" + "="*60)
        print("CK总次数限制功能集成测试报告")
        print("="*60)
        print(f"总测试数: {report['test_summary']['total_tests']}")
        print(f"通过测试: {report['test_summary']['passed_tests']}")
        print(f"失败测试: {report['test_summary']['failed_tests']}")
        print(f"成功率: {report['test_summary']['success_rate']}")
        print("="*60)
        
        for result in report['test_details']:
            status = "✅ 通过" if result['passed'] else "❌ 失败"
            print(f"{result['test_name']}: {status}")
            if not result['passed'] and 'error' in result:
                print(f"  错误: {result['error']}")
        
        print("="*60)
        
        # 保存详细报告
        report_file = f"ck_limit_test_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2)
        
        logger.info(f"详细测试报告已保存到: {report_file}")
        
        return report['test_summary']['failed_tests'] == 0


async def main():
    """主函数"""
    with CKLimitIntegrationTest() as test:
        success = await test.run_all_tests()
        return 0 if success else 1


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
