#!/usr/bin/env python3
"""
Redis CK优化方案并发安全性测试
验证100个并发请求的唯一性和负载均衡效果
"""

import sys
import os
import asyncio
import json
import time
import uuid
from datetime import datetime
from typing import Dict, Any, List
from collections import defaultdict, Counter

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app.db.session import SessionLocal
from app.models.walmart_ck import WalmartCK
from app.models.merchant import Merchant
from app.models.department import Department
from app.services.redis_ck_service_improved import ImprovedRedisOptimizedCKService
from app.core.redis import get_redis
from app.core.logging import get_logger

logger = get_logger("concurrent_safety_test")


class ConcurrentSafetyTest:
    """并发安全性测试类"""
    
    def __init__(self):
        self.db = SessionLocal()
        self.test_data = {}
        self.test_results = []
    
    async def __aenter__(self):
        # 初始化Redis连接
        self.redis_client = await get_redis()
        self.redis_service = ImprovedRedisOptimizedCKService(self.db, self.redis_client)
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        await self.cleanup()
        self.db.close()
    
    async def cleanup(self):
        """清理测试数据"""
        try:
            # 清理Redis数据
            patterns = [
                "walmart:ck:pool:*",
                "walmart:ck:status:*",
                "walmart:ck:lock:*",
                "walmart:ck:validation:*",
                "walmart:ck:dept:*",
                "walmart:ck:selection_stats:*"
            ]
            
            for pattern in patterns:
                keys = []
                async for key in self.redis_client.scan_iter(match=pattern):
                    keys.append(key)
                
                if keys:
                    await self.redis_client.delete(*keys)
            
            # 清理数据库测试数据
            if 'test_cks' in self.test_data:
                for ck_id in self.test_data['test_cks']:
                    ck = self.db.query(WalmartCK).filter(WalmartCK.id == ck_id).first()
                    if ck:
                        self.db.delete(ck)
            
            if 'test_department' in self.test_data:
                dept = self.db.query(Department).filter(Department.id == self.test_data['test_department']).first()
                if dept:
                    self.db.delete(dept)
            
            if 'test_merchant' in self.test_data:
                merchant = self.db.query(Merchant).filter(Merchant.id == self.test_data['test_merchant']).first()
                if merchant:
                    self.db.delete(merchant)
            
            self.db.commit()
            logger.info("测试数据清理完成")
            
        except Exception as e:
            self.db.rollback()
            logger.error(f"清理测试数据失败: {e}")
    
    async def create_test_data(self):
        """创建测试数据"""
        try:
            # 创建测试商户
            merchant = Merchant(
                name="并发安全测试商户",
                code="CONCURRENT_SAFETY_TEST",
                status="active"
            )
            self.db.add(merchant)
            self.db.commit()
            self.db.refresh(merchant)
            self.test_data['test_merchant'] = merchant.id
            
            # 创建测试部门
            department = Department(
                name="并发安全测试部门",
                code="CONCURRENT_SAFETY_DEPT",
                merchant_id=merchant.id,
                status="active"
            )
            self.db.add(department)
            self.db.commit()
            self.db.refresh(department)
            self.test_data['test_department'] = department.id
            
            # 创建30个测试CK（足够多的CK用于负载均衡测试）
            test_cks = []
            for i in range(30):
                ck = WalmartCK(
                    sign=f"concurrent_test_ck_{i}_{datetime.now().timestamp()}",
                    total_limit=200,  # 设置较大的限制
                    bind_count=0,     # 初始使用次数为0
                    active=True,
                    merchant_id=merchant.id,
                    department_id=department.id,
                    is_deleted=False,
                    description=f"并发安全测试CK_{i}"
                )
                self.db.add(ck)
                test_cks.append(ck)
            
            self.db.commit()
            
            # 刷新并保存CK ID
            self.test_data['test_cks'] = []
            for ck in test_cks:
                self.db.refresh(ck)
                self.test_data['test_cks'].append(ck.id)
            
            # 初始化Redis CK池
            await self.redis_service.sync_service.initialize_merchant_ck_pool(merchant.id)
            
            logger.info(f"创建测试数据完成: 商户{merchant.id}, 部门{department.id}, CK数量{len(test_cks)}")
            return True
            
        except Exception as e:
            self.db.rollback()
            logger.error(f"创建测试数据失败: {e}")
            return False
    
    async def test_concurrent_uniqueness(self, concurrent_count: int = 100):
        """测试并发唯一性 - 核心测试"""
        logger.info(f"开始并发唯一性测试，并发数: {concurrent_count}")
        
        start_time = time.time()
        results = []
        
        async def single_request():
            """单个CK选择请求"""
            request_id = str(uuid.uuid4())
            request_start = time.time()
            
            try:
                ck = await self.redis_service.get_available_ck_optimized(
                    self.test_data['test_merchant'],
                    self.test_data['test_department'],
                    request_id
                )
                
                request_time = time.time() - request_start
                
                if ck:
                    # 模拟绑卡处理
                    await asyncio.sleep(0.001)  # 1ms处理时间
                    
                    # 记录使用
                    await self.redis_service.record_ck_usage_optimized(
                        ck.id,
                        self.test_data['test_merchant'],
                        self.test_data['test_department'],
                        True,
                        getattr(ck, '_redis_request_id', request_id)
                    )
                    
                    return {
                        'success': True,
                        'ck_id': ck.id,
                        'request_id': request_id,
                        'response_time': request_time
                    }
                else:
                    return {
                        'success': False,
                        'ck_id': None,
                        'request_id': request_id,
                        'response_time': request_time
                    }
                    
            except Exception as e:
                return {
                    'success': False,
                    'error': str(e),
                    'request_id': request_id,
                    'response_time': time.time() - request_start
                }
        
        # 并发执行请求
        tasks = [single_request() for _ in range(concurrent_count)]
        results = await asyncio.gather(*tasks)
        
        total_time = time.time() - start_time
        
        # 分析结果
        successful_requests = [r for r in results if r['success']]
        failed_requests = [r for r in results if not r['success']]
        
        # 检查唯一性 - 这是最关键的测试
        selected_ck_ids = [r['ck_id'] for r in successful_requests]
        unique_ck_ids = set(selected_ck_ids)
        
        # 检查是否有重复选择（这应该永远不会发生）
        duplicate_count = len(selected_ck_ids) - len(unique_ck_ids)
        
        # 分析CK分布
        ck_distribution = Counter(selected_ck_ids)
        
        # 计算负载分布方差
        if ck_distribution:
            usage_values = list(ck_distribution.values())
            mean_usage = sum(usage_values) / len(usage_values)
            variance = sum((x - mean_usage) ** 2 for x in usage_values) / len(usage_values)
            uniformity_score = max(0, 100 - (variance / mean_usage * 10)) if mean_usage > 0 else 0
        else:
            variance = 0
            uniformity_score = 0
        
        # 响应时间分析
        response_times = [r['response_time'] for r in results]
        avg_response_time = sum(response_times) / len(response_times)
        max_response_time = max(response_times)
        min_response_time = min(response_times)
        
        test_result = {
            'test_name': 'concurrent_uniqueness',
            'concurrent_requests': concurrent_count,
            'total_time': total_time,
            'successful_requests': len(successful_requests),
            'failed_requests': len(failed_requests),
            'success_rate': len(successful_requests) / concurrent_count * 100,
            
            # 唯一性检查 - 最重要的指标
            'selected_ck_count': len(selected_ck_ids),
            'unique_ck_count': len(unique_ck_ids),
            'duplicate_selections': duplicate_count,  # 应该为0
            'uniqueness_guaranteed': duplicate_count == 0,  # 应该为True
            
            # 负载均衡分析
            'ck_distribution': dict(ck_distribution),
            'unique_cks_used': len(ck_distribution),
            'load_variance': variance,
            'uniformity_score': uniformity_score,
            
            # 性能分析
            'avg_response_time': avg_response_time,
            'max_response_time': max_response_time,
            'min_response_time': min_response_time,
            'qps': concurrent_count / total_time,
            
            # 测试通过条件
            'passed': duplicate_count == 0 and len(successful_requests) > concurrent_count * 0.9
        }
        
        self.test_results.append(test_result)
        
        # 输出关键结果
        logger.info(f"并发唯一性测试完成:")
        logger.info(f"  并发请求数: {concurrent_count}")
        logger.info(f"  成功请求数: {len(successful_requests)}")
        logger.info(f"  选择的CK数: {len(selected_ck_ids)}")
        logger.info(f"  唯一CK数: {len(unique_ck_ids)}")
        logger.info(f"  重复选择数: {duplicate_count} (应该为0)")
        logger.info(f"  唯一性保证: {'✅ 是' if duplicate_count == 0 else '❌ 否'}")
        logger.info(f"  负载均匀性: {uniformity_score:.1f}%")
        logger.info(f"  平均响应时间: {avg_response_time*1000:.1f}ms")
        
        return test_result
    
    async def test_stress_concurrent(self, concurrent_count: int = 500):
        """压力测试 - 更高并发"""
        logger.info(f"开始压力并发测试，并发数: {concurrent_count}")
        
        # 重置CK状态
        for ck_id in self.test_data['test_cks']:
            ck = self.db.query(WalmartCK).filter(WalmartCK.id == ck_id).first()
            if ck:
                ck.bind_count = 0
                ck.active = True
        self.db.commit()
        
        # 重新初始化Redis池
        await self.redis_service.sync_service.initialize_merchant_ck_pool(
            self.test_data['test_merchant']
        )
        
        # 执行压力测试
        result = await self.test_concurrent_uniqueness(concurrent_count)
        result['test_name'] = 'stress_concurrent'
        
        return result
    
    async def test_load_balance_analysis(self):
        """负载均衡深度分析"""
        logger.info("开始负载均衡深度分析...")
        
        try:
            # 获取Redis统计数据
            stats = await self.redis_service.get_performance_metrics(
                self.test_data['test_merchant']
            )
            
            # 分析选择位置分布
            selection_stats = stats.get('selection_statistics', {})
            
            test_result = {
                'test_name': 'load_balance_analysis',
                'redis_statistics': stats,
                'analysis': {
                    'total_selections': selection_stats.get('total_selections', 0),
                    'unique_cks_used': selection_stats.get('unique_cks_used', 0),
                    'uniformity_score': selection_stats.get('uniformity_score', 0),
                    'load_variance': selection_stats.get('load_variance', 0)
                },
                'passed': selection_stats.get('uniformity_score', 0) > 70  # 期望均匀性>70%
            }
            
            self.test_results.append(test_result)
            
            logger.info(f"负载均衡分析完成 - 均匀性: {selection_stats.get('uniformity_score', 0):.1f}%")
            
            return test_result
            
        except Exception as e:
            logger.error(f"负载均衡分析失败: {e}")
            return {'test_name': 'load_balance_analysis', 'passed': False, 'error': str(e)}
    
    def generate_test_report(self):
        """生成测试报告"""
        total_tests = len(self.test_results)
        passed_tests = sum(1 for r in self.test_results if r.get('passed', False))
        
        # 汇总关键指标
        uniqueness_tests = [r for r in self.test_results if 'uniqueness_guaranteed' in r]
        all_unique = all(r.get('uniqueness_guaranteed', False) for r in uniqueness_tests)
        
        report = {
            "test_summary": {
                "total_tests": total_tests,
                "passed_tests": passed_tests,
                "failed_tests": total_tests - passed_tests,
                "success_rate": f"{passed_tests/total_tests*100:.1f}%" if total_tests > 0 else "0%",
                "uniqueness_guaranteed": all_unique
            },
            "concurrent_safety_analysis": {
                "max_concurrent_tested": max([r.get('concurrent_requests', 0) for r in self.test_results]),
                "total_duplicate_selections": sum([r.get('duplicate_selections', 0) for r in self.test_results]),
                "average_uniformity_score": sum([r.get('uniformity_score', 0) for r in self.test_results]) / len(self.test_results) if self.test_results else 0
            },
            "test_details": self.test_results,
            "test_data": self.test_data,
            "timestamp": datetime.now().isoformat()
        }
        
        return report
    
    async def run_all_tests(self):
        """运行所有并发安全性测试"""
        logger.info("开始Redis CK并发安全性测试...")
        
        # 创建测试数据
        if not await self.create_test_data():
            logger.error("创建测试数据失败，终止测试")
            return False
        
        # 运行各项测试
        tests = [
            ("并发唯一性测试(100并发)", lambda: self.test_concurrent_uniqueness(100)),
            ("并发唯一性测试(200并发)", lambda: self.test_concurrent_uniqueness(200)),
            ("压力并发测试(500并发)", lambda: self.test_stress_concurrent(500)),
            ("负载均衡深度分析", self.test_load_balance_analysis),
        ]
        
        for test_name, test_func in tests:
            logger.info(f"执行测试: {test_name}")
            try:
                await test_func()
            except Exception as e:
                logger.error(f"测试 {test_name} 执行异常: {e}")
        
        # 生成测试报告
        report = self.generate_test_report()
        
        # 输出测试结果
        print("\n" + "="*80)
        print("Redis CK并发安全性测试报告")
        print("="*80)
        print(f"总测试数: {report['test_summary']['total_tests']}")
        print(f"通过测试: {report['test_summary']['passed_tests']}")
        print(f"失败测试: {report['test_summary']['failed_tests']}")
        print(f"成功率: {report['test_summary']['success_rate']}")
        print(f"并发唯一性保证: {'✅ 是' if report['test_summary']['uniqueness_guaranteed'] else '❌ 否'}")
        print(f"最大并发测试: {report['concurrent_safety_analysis']['max_concurrent_tested']}")
        print(f"总重复选择数: {report['concurrent_safety_analysis']['total_duplicate_selections']} (应该为0)")
        print(f"平均负载均匀性: {report['concurrent_safety_analysis']['average_uniformity_score']:.1f}%")
        print("="*80)
        
        for result in report['test_details']:
            status = "✅ 通过" if result.get('passed', False) else "❌ 失败"
            print(f"{result['test_name']}: {status}")
            
            if 'uniqueness_guaranteed' in result:
                print(f"  唯一性保证: {'✅' if result['uniqueness_guaranteed'] else '❌'}")
                print(f"  重复选择: {result.get('duplicate_selections', 0)}")
                print(f"  负载均匀性: {result.get('uniformity_score', 0):.1f}%")
        
        print("="*80)
        
        # 保存详细报告
        report_file = f"concurrent_safety_test_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2)
        
        logger.info(f"详细测试报告已保存到: {report_file}")
        
        return report['test_summary']['failed_tests'] == 0 and report['test_summary']['uniqueness_guaranteed']


async def main():
    """主函数"""
    async with ConcurrentSafetyTest() as test:
        success = await test.run_all_tests()
        return 0 if success else 1


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
