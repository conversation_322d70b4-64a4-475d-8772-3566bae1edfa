"""
测试核心服务的数据隔离安全修复
验证UserService和MerchantService的商户级数据隔离机制
"""

import pytest
from sqlalchemy.orm import Session
from app.models.user import User
from app.models.merchant import Merchant
from app.models.department import Department
from app.services.user_service import UserService
from app.services.merchant_service import MerchantService


class TestCoreServicesSecurity:
    """测试核心服务安全修复"""

    @pytest.fixture
    def setup_test_data(self, db: Session):
        """设置测试数据"""
        # 创建两个商户
        merchant1 = Merchant(
            name="测试商户1",
            code="TEST_MERCHANT_1",
            api_key="test_key_1",
            api_secret="test_secret_1"
        )
        merchant2 = Merchant(
            name="测试商户2", 
            code="TEST_MERCHANT_2",
            api_key="test_key_2",
            api_secret="test_secret_2"
        )
        db.add_all([merchant1, merchant2])
        db.commit()
        db.refresh(merchant1)
        db.refresh(merchant2)

        # 创建部门
        dept1 = Department(
            name="测试部门1",
            merchant_id=merchant1.id,
            code="DEPT_1"
        )
        dept2 = Department(
            name="测试部门2",
            merchant_id=merchant2.id,
            code="DEPT_2"
        )
        db.add_all([dept1, dept2])
        db.commit()
        db.refresh(dept1)
        db.refresh(dept2)

        # 创建用户
        user1 = User(
            username="test_user_1",
            email="<EMAIL>",
            hashed_password="hashed_password",
            merchant_id=merchant1.id,
            department_id=dept1.id,
            is_superuser=False
        )
        user2 = User(
            username="test_user_2",
            email="<EMAIL>", 
            hashed_password="hashed_password",
            merchant_id=merchant2.id,
            department_id=dept2.id,
            is_superuser=False
        )
        user3 = User(
            username="test_user_3",
            email="<EMAIL>", 
            hashed_password="hashed_password",
            merchant_id=merchant1.id,
            department_id=dept1.id,
            is_superuser=False
        )
        superuser = User(
            username="superuser",
            email="<EMAIL>",
            hashed_password="hashed_password",
            is_superuser=True
        )
        db.add_all([user1, user2, user3, superuser])
        db.commit()
        db.refresh(user1)
        db.refresh(user2)
        db.refresh(user3)
        db.refresh(superuser)

        return {
            "merchant1": merchant1,
            "merchant2": merchant2,
            "dept1": dept1,
            "dept2": dept2,
            "user1": user1,
            "user2": user2,
            "user3": user3,
            "superuser": superuser
        }

    def test_user_service_data_isolation(self, db: Session, setup_test_data):
        """测试UserService的数据隔离"""
        user_service = UserService(db)
        user1 = setup_test_data["user1"]
        user2 = setup_test_data["user2"]
        superuser = setup_test_data["superuser"]

        # 用户1应该只能看到自己商户的用户
        query1 = db.query(User)
        isolated_query1 = user_service.apply_data_isolation(query1, user1)
        users1 = isolated_query1.all()
        
        # 验证用户1只能看到商户1的用户（user1和user3）
        user_ids1 = [user.id for user in users1]
        assert user1.id in user_ids1, "用户1应该能看到自己"
        assert setup_test_data["user3"].id in user_ids1, "用户1应该能看到同商户的其他用户"
        assert user2.id not in user_ids1, "用户1不应该能看到其他商户的用户"

        # 用户2应该只能看到自己商户的用户
        query2 = db.query(User)
        isolated_query2 = user_service.apply_data_isolation(query2, user2)
        users2 = isolated_query2.all()
        
        # 验证用户2只能看到商户2的用户（只有user2）
        user_ids2 = [user.id for user in users2]
        assert user2.id in user_ids2, "用户2应该能看到自己"
        assert user1.id not in user_ids2, "用户2不应该能看到其他商户的用户"
        assert setup_test_data["user3"].id not in user_ids2, "用户2不应该能看到其他商户的用户"

        # 超级管理员应该能看到所有用户
        query_super = db.query(User)
        isolated_query_super = user_service.apply_data_isolation(query_super, superuser)
        users_super = isolated_query_super.all()
        
        assert len(users_super) >= 4, "超级管理员应该能看到所有用户"

    def test_merchant_service_data_isolation(self, db: Session, setup_test_data):
        """测试MerchantService的数据隔离"""
        merchant_service = MerchantService(db)
        user1 = setup_test_data["user1"]
        user2 = setup_test_data["user2"]
        superuser = setup_test_data["superuser"]

        # 用户1应该只能看到自己的商户
        query1 = db.query(Merchant)
        isolated_query1 = merchant_service.apply_data_isolation(query1, user1)
        merchants1 = isolated_query1.all()
        
        # 验证用户1只能看到自己的商户
        merchant_ids1 = [merchant.id for merchant in merchants1]
        assert setup_test_data["merchant1"].id in merchant_ids1, "用户1应该能看到自己的商户"
        assert setup_test_data["merchant2"].id not in merchant_ids1, "用户1不应该能看到其他商户"
        assert len(merchants1) == 1, "用户1应该只能看到1个商户"

        # 用户2应该只能看到自己的商户
        query2 = db.query(Merchant)
        isolated_query2 = merchant_service.apply_data_isolation(query2, user2)
        merchants2 = isolated_query2.all()
        
        # 验证用户2只能看到自己的商户
        merchant_ids2 = [merchant.id for merchant in merchants2]
        assert setup_test_data["merchant2"].id in merchant_ids2, "用户2应该能看到自己的商户"
        assert setup_test_data["merchant1"].id not in merchant_ids2, "用户2不应该能看到其他商户"
        assert len(merchants2) == 1, "用户2应该只能看到1个商户"

        # 超级管理员应该能看到所有商户
        query_super = db.query(Merchant)
        isolated_query_super = merchant_service.apply_data_isolation(query_super, superuser)
        merchants_super = isolated_query_super.all()
        
        assert len(merchants_super) >= 2, "超级管理员应该能看到所有商户"

    def test_user_without_merchant_id(self, db: Session, setup_test_data):
        """测试没有商户ID的用户"""
        user_service = UserService(db)
        merchant_service = MerchantService(db)
        
        # 创建没有商户ID的用户
        user_no_merchant = User(
            username="no_merchant_user",
            email="<EMAIL>",
            hashed_password="hashed_password",
            is_superuser=False
        )
        db.add(user_no_merchant)
        db.commit()

        # 测试UserService
        query_user = db.query(User)
        isolated_query_user = user_service.apply_data_isolation(query_user, user_no_merchant)
        users = isolated_query_user.all()
        assert len(users) == 0, "没有商户ID的用户应该看不到任何用户数据"

        # 测试MerchantService
        query_merchant = db.query(Merchant)
        isolated_query_merchant = merchant_service.apply_data_isolation(query_merchant, user_no_merchant)
        merchants = isolated_query_merchant.all()
        assert len(merchants) == 0, "没有商户ID的用户应该看不到任何商户数据"

    def test_service_methods_with_isolation(self, db: Session, setup_test_data):
        """测试服务方法是否正确应用数据隔离"""
        user_service = UserService(db)
        merchant_service = MerchantService(db)
        user1 = setup_test_data["user1"]
        user2 = setup_test_data["user2"]

        # 测试UserService的get_with_isolation方法
        # 用户1应该能获取自己商户的用户
        user3 = user_service.get_with_isolation(setup_test_data["user3"].id, user1)
        assert user3 is not None, "用户1应该能获取同商户的其他用户"

        # 用户1不应该能获取其他商户的用户
        user2_result = user_service.get_with_isolation(user2.id, user1)
        assert user2_result is None, "用户1不应该能获取其他商户的用户"

        # 测试MerchantService的get_with_isolation方法
        # 用户1应该能获取自己的商户
        merchant1 = merchant_service.get_with_isolation(setup_test_data["merchant1"].id, user1)
        assert merchant1 is not None, "用户1应该能获取自己的商户"

        # 用户1不应该能获取其他商户
        merchant2_result = merchant_service.get_with_isolation(setup_test_data["merchant2"].id, user1)
        assert merchant2_result is None, "用户1不应该能获取其他商户"
