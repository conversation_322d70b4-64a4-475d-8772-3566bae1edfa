"""
测试动态Referer配置功能

测试内容：
1. 数据库表结构验证
2. 数据模型验证
3. 服务层Referer获取功能
4. WalmartAPI动态Referer功能
5. API端点Referer管理功能
6. Fallback机制测试
"""

import pytest
import asyncio
from unittest.mock import AsyncMock, patch, MagicMock
from sqlalchemy.orm import Session
from sqlalchemy import text

from app.core.walmart_api import WalmartAPI
from app.services.walmart_server import WalmartServerService
from app.models.walmart_server import WalmartServer
from app.schemas.walmart_server import WalmartServerCreate, WalmartServerUpdate
from app.core.redis import get_redis


class TestDynamicReferer:
    """动态Referer配置测试类"""

    @pytest.fixture
    def db_session(self):
        """数据库会话fixture"""
        # 这里应该返回测试数据库会话
        # 实际实现需要根据项目的测试配置来调整
        pass

    @pytest.fixture
    def walmart_server_service(self, db_session):
        """WalmartServerService fixture"""
        return WalmartServerService(db_session)

    def test_database_schema_has_referer_field(self, db_session):
        """测试数据库表是否包含referer字段"""
        # 检查walmart_server表是否有referer字段
        result = db_session.execute(
            text("DESCRIBE walmart_server")
        ).fetchall()
        
        field_names = [row[0] for row in result]
        assert 'referer' in field_names, "walmart_server表缺少referer字段"

    def test_walmart_server_model_has_referer(self):
        """测试WalmartServer模型是否包含referer字段"""
        # 检查模型是否有referer属性
        assert hasattr(WalmartServer, 'referer'), "WalmartServer模型缺少referer字段"

    def test_walmart_server_schema_has_referer(self):
        """测试WalmartServer schema是否包含referer字段"""
        # 测试创建schema
        create_data = {
            'api_url': 'https://test.com',
            'referer': 'https://test-referer.com',
            'timeout': 30,
            'retry_count': 3,
            'daily_bind_limit': 1000,
            'api_rate_limit': 60,
            'max_retry_times': 3,
            'bind_timeout_seconds': 30,
            'verification_code_expires': 300,
            'log_retention_days': 90,
            'enable_ip_whitelist': True,
            'enable_security_audit': True,
            'maintenance_mode': False,
            'is_active': True,
        }
        
        schema = WalmartServerCreate(**create_data)
        assert schema.referer == 'https://test-referer.com'

        # 测试更新schema
        update_data = {
            'api_url': 'https://updated.com',
            'referer': 'https://updated-referer.com'
        }
        
        update_schema = WalmartServerUpdate(**update_data)
        assert update_schema.referer == 'https://updated-referer.com'

    @pytest.mark.asyncio
    async def test_walmart_server_service_get_referer(self, walmart_server_service):
        """测试WalmartServerService的get_referer方法（现在返回固定值）"""
        referer = await walmart_server_service.get_referer()
        # 应该返回固定的Referer值
        expected_referer = "https://servicewechat.com/wx81d3e1fe4c2e11b4/168/page-frame.html"
        assert referer == expected_referer

    @pytest.mark.asyncio
    async def test_walmart_server_service_get_referer_fallback(self, walmart_server_service):
        """测试WalmartServerService的get_referer方法（现在直接返回固定值）"""
        referer = await walmart_server_service.get_referer()

        # 应该返回固定的Referer值
        expected_referer = "https://servicewechat.com/wx81d3e1fe4c2e11b4/168/page-frame.html"
        assert referer == expected_referer

    @pytest.mark.asyncio
    async def test_walmart_api_get_referer_from_config(self):
        """测试WalmartAPI的get_referer_from_config静态方法"""
        with patch('app.core.redis.get_redis') as mock_get_redis:
            # 模拟Redis客户端
            mock_redis = AsyncMock()
            mock_get_redis.return_value = mock_redis
            
            # 测试从Redis获取成功
            mock_redis.get.return_value = "https://dynamic-referer.com"
            
            referer = await WalmartAPI.get_referer_from_config()
            assert referer == "https://dynamic-referer.com"
            mock_redis.get.assert_called_once_with("walmart:referer")

    @pytest.mark.asyncio
    async def test_walmart_api_get_referer_fallback(self):
        """测试WalmartAPI的get_referer_from_config fallback机制"""
        with patch('app.core.redis.get_redis') as mock_get_redis:
            # 模拟Redis客户端
            mock_redis = AsyncMock()
            mock_get_redis.return_value = mock_redis
            
            # 模拟Redis中没有缓存
            mock_redis.get.return_value = None
            
            referer = await WalmartAPI.get_referer_from_config()
            
            # 应该返回默认值
            expected_default = "https://servicewechat.com/wx81d3e1fe4c2e11b4/168/page-frame.html"
            assert referer == expected_default
            
            # 应该缓存默认值
            mock_redis.set.assert_called_once_with("walmart:referer", expected_default, ex=86400)

    @pytest.mark.asyncio
    async def test_walmart_api_get_referer_exception_handling(self):
        """测试WalmartAPI的get_referer_from_config异常处理"""
        with patch('app.core.redis.get_redis') as mock_get_redis:
            # 模拟Redis连接异常
            mock_get_redis.side_effect = Exception("Redis connection failed")
            
            referer = await WalmartAPI.get_referer_from_config()
            
            # 应该返回默认值
            expected_default = "https://servicewechat.com/wx81d3e1fe4c2e11b4/168/page-frame.html"
            assert referer == expected_default

    def test_walmart_api_prepare_headers_with_custom_referer(self):
        """测试WalmartAPI的_prepare_headers方法使用自定义Referer"""
        api = WalmartAPI(
            sign="test_sign",
            secret_key="test_key",
            api_version="29"
        )
        
        request_body = {"test": "data"}
        custom_referer = "https://custom-referer.com"
        
        headers = api._prepare_headers(request_body, referer=custom_referer)
        
        assert headers["Referer"] == custom_referer

    def test_walmart_api_prepare_headers_with_instance_referer(self):
        """测试WalmartAPI的_prepare_headers方法使用实例Referer"""
        instance_referer = "https://instance-referer.com"
        api = WalmartAPI(
            sign="test_sign",
            secret_key="test_key",
            api_version="29",
            referer=instance_referer
        )
        
        request_body = {"test": "data"}
        
        headers = api._prepare_headers(request_body)
        
        assert headers["Referer"] == instance_referer

    def test_walmart_api_prepare_headers_default_referer(self):
        """测试WalmartAPI的_prepare_headers方法使用默认Referer"""
        api = WalmartAPI(
            sign="test_sign",
            secret_key="test_key",
            api_version="29"
        )
        
        request_body = {"test": "data"}
        
        headers = api._prepare_headers(request_body)
        
        expected_default = "https://servicewechat.com/wx81d3e1fe4c2e11b4/168/page-frame.html"
        assert headers["Referer"] == expected_default

    @pytest.mark.asyncio
    async def test_walmart_api_request_async_uses_dynamic_referer(self):
        """测试WalmartAPI的request_async方法使用动态Referer"""
        api = WalmartAPI(
            sign="test_sign",
            secret_key="test_key",
            api_version="29"
        )
        
        with patch.object(api, 'get_referer_from_config') as mock_get_referer:
            mock_get_referer.return_value = "https://dynamic-referer.com"
            
            with patch('curl_cffi.requests.post') as mock_post:
                # 模拟成功响应
                mock_response = MagicMock()
                mock_response.json.return_value = {"status": True}
                mock_post.return_value = mock_response
                
                await api.request_async("/test", {"test": "data"})
                
                # 验证get_referer_from_config被调用
                mock_get_referer.assert_called_once()
                
                # 验证请求被发送
                mock_post.assert_called_once()
                
                # 验证请求头包含动态Referer
                call_args = mock_post.call_args
                headers = call_args[1]['headers']
                assert headers['Referer'] == "https://dynamic-referer.com"


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
