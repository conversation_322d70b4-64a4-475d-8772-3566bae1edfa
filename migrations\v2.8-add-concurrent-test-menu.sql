-- ========================================
-- 添加并发测试功能的完整迁移脚本
-- 包括菜单配置和数据库字段修改
-- 版本: v2.8
-- ========================================

-- 开始事务
START TRANSACTION;

-- ========================================
-- 1. 添加 is_test_mode 字段到 card_records 表
-- ========================================

-- 检查字段是否已存在，如果不存在则添加
SET @column_exists = (
    SELECT COUNT(*)
    FROM INFORMATION_SCHEMA.COLUMNS
    WHERE TABLE_SCHEMA = DATABASE()
    AND TABLE_NAME = 'card_records'
    AND COLUMN_NAME = 'is_test_mode'
);

-- 添加字段（如果不存在）
SET @sql = IF(@column_exists = 0,
    'ALTER TABLE `card_records` ADD COLUMN `is_test_mode` BOOLEAN NOT NULL DEFAULT FALSE COMMENT "是否为测试模式，用于并发测试时标识测试数据"',
    'SELECT "字段 is_test_mode 已存在，跳过添加" as message'
);

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 为现有记录设置默认值（如果字段是新添加的）
UPDATE `card_records` SET `is_test_mode` = FALSE WHERE `is_test_mode` IS NULL;

-- ========================================
-- 2. 添加并发测试菜单配置
-- ========================================

-- 查找绑卡操作菜单的ID
SET @bind_menu_id = (SELECT id FROM menus WHERE code = 'bind' LIMIT 1);

-- 添加并发测试子菜单
INSERT IGNORE INTO `menus` (
    `name`, `code`, `path`, `component`, `icon`, `parent_id`,
    `level`, `sort_order`, `is_visible`, `is_enabled`, `menu_type`,
    `description`, `created_at`, `updated_at`
) VALUES (
    '并发测试', 
    'bind:concurrent-test', 
    '/test/concurrent-binding', 
    'ConcurrentBinding', 
    'monitor', 
    @bind_menu_id, 
    2, 
    3, 
    1, 
    1, 
    'menu', 
    '并发绑卡测试（仅开发/测试环境）', 
    NOW(3), 
    NOW(3)
);

-- 添加并发测试菜单权限
INSERT IGNORE INTO `permissions` (
    `code`, `name`, `description`, `resource_type`, `resource_path`, `is_enabled`, `sort_order`
) VALUES (
    'menu:bind:concurrent-test', 
    '并发测试菜单', 
    '访问并发绑卡测试菜单', 
    'menu', 
    '/test/concurrent-binding', 
    1, 
    22
);

-- 为超级管理员角色分配并发测试菜单权限
INSERT IGNORE INTO `role_menus` (`role_id`, `menu_id`)
SELECT r.id, m.id
FROM `roles` r, `menus` m
WHERE r.code = 'super_admin' AND m.code = 'bind:concurrent-test';

-- 为商户管理员角色分配并发测试菜单权限（可选，根据需要开启）
INSERT IGNORE INTO `role_menus` (`role_id`, `menu_id`)
SELECT r.id, m.id
FROM `roles` r, `menus` m
WHERE r.code = 'merchant_admin' AND m.code = 'bind:concurrent-test';

-- 为超级管理员角色分配并发测试菜单权限到role_permissions表
INSERT IGNORE INTO `role_permissions` (`role_id`, `permission_id`)
SELECT r.id, p.id
FROM `roles` r, `permissions` p
WHERE r.code = 'super_admin' AND p.code = 'menu:bind:concurrent-test';

-- 为商户管理员角色分配并发测试菜单权限到role_permissions表（可选）
INSERT IGNORE INTO `role_permissions` (`role_id`, `permission_id`)
SELECT r.id, p.id
FROM `roles` r, `permissions` p
WHERE r.code = 'merchant_admin' AND p.code = 'menu:bind:concurrent-test';

-- 验证插入结果
SELECT 
    m.name as menu_name,
    m.code as menu_code,
    m.path as menu_path,
    p.name as permission_name,
    p.code as permission_code
FROM menus m
LEFT JOIN permissions p ON p.code = CONCAT('menu:', m.code)
WHERE m.code = 'bind:concurrent-test';

-- 验证角色菜单分配
SELECT 
    r.name as role_name,
    r.code as role_code,
    m.name as menu_name,
    m.code as menu_code
FROM role_menus rm
JOIN roles r ON rm.role_id = r.id
JOIN menus m ON rm.menu_id = m.id
WHERE m.code = 'bind:concurrent-test';

-- 验证角色权限分配
SELECT
    r.name as role_name,
    r.code as role_code,
    p.name as permission_name,
    p.code as permission_code
FROM role_permissions rp
JOIN roles r ON rp.role_id = r.id
JOIN permissions p ON rp.permission_id = p.id
WHERE p.code = 'menu:bind:concurrent-test';

-- ========================================
-- 3. 验证数据库字段修改
-- ========================================

-- 验证 is_test_mode 字段是否添加成功
SELECT
    'is_test_mode字段验证' as check_type,
    COLUMN_NAME,
    DATA_TYPE,
    IS_NULLABLE,
    COLUMN_DEFAULT,
    COLUMN_COMMENT
FROM INFORMATION_SCHEMA.COLUMNS
WHERE TABLE_SCHEMA = DATABASE()
AND TABLE_NAME = 'card_records'
AND COLUMN_NAME = 'is_test_mode';

-- 提交事务
COMMIT;

-- ========================================
-- 使用说明
-- ========================================

/*
迁移完成后的使用说明：

1. 数据库字段修改：
   - card_records 表新增 is_test_mode 字段
   - 用于标识测试模式下的绑卡记录
   - 默认值为 FALSE

2. 菜单配置：
   - 新增"并发测试"菜单项
   - 位置：绑卡操作 > 并发测试
   - 路径：/test/concurrent-binding

3. 权限分配：
   - 超级管理员：默认有访问权限
   - 商户管理员：默认有访问权限

4. 重新登录系统以刷新菜单缓存

如需回滚此迁移，请执行：

-- 回滚菜单配置
DELETE rm FROM role_menus rm
JOIN menus m ON rm.menu_id = m.id
WHERE m.code = 'bind:concurrent-test';

DELETE rp FROM role_permissions rp
JOIN permissions p ON rp.permission_id = p.id
WHERE p.code = 'menu:bind:concurrent-test';

DELETE FROM permissions WHERE code = 'menu:bind:concurrent-test';
DELETE FROM menus WHERE code = 'bind:concurrent-test';

-- 回滚数据库字段（谨慎操作，会丢失测试标识数据）
-- ALTER TABLE card_records DROP COLUMN is_test_mode;
*/
