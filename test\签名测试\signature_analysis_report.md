# 微信小程序签名算法分析报告

## 概述

本报告分析了沃尔玛微信小程序的签名算法，基于抓包数据尝试逆向工程签名生成逻辑。

## 抓包数据分析

### 案例1: getUserInfo接口
- **时间戳**: 1751174204050
- **随机数**: 24d4d7ffa
- **期望签名**: 74B1193DDB5AE4AC76247A2F456CC13C411D936C48C284A5C3DE8FFFC9CDB119
- **请求体**: `{"currentPage":0,"pageSize":0,"sign":"6b95be8d25574dad856cdf7939bfe469@61fa608e14c37c20fde47c700ec90414"}`

### 案例2: bindCard接口
- **时间戳**: 1751174220771
- **随机数**: 1158f8500
- **期望签名**: 107BAE44C39F6878E2CB2D7F832DE9BDFA77364208514F627473B8B920B3113A
- **请求体**: `{"cardNo":"2326992090536890765","cardPwd":"811911","currentPage":0,"pageSize":0,"sign":"6b95be8d25574dad856cdf7939bfe469@61fa608e14c37c20fde47c700ec90414","storeId":"","userPhone":""}`

### 已知参数
- **沃尔玛签名**: 6b95be8d25574dad856cdf7939bfe469@61fa608e14c37c20fde47c700ec90414
- **微信加密KEY**: zB0ZnBVP+iBBQjV0zEi0yA==
- **版本号**: 45

## 尝试的签名算法

### 1. 基础HMAC-SHA256算法
测试了以下组合：
- `timestamp + nonce + JSON`
- `nonce + timestamp + JSON`
- `JSON + timestamp + nonce`
- 各种分隔符组合（|, &, _, 等）

### 2. 不同的密钥格式
- wechat_key原始字符串
- wechat_key base64解码
- walmart_sign完整字符串
- walmart_sign前32字符
- walmart_sign后32字符
- walmart_sign@符号前后部分

### 3. 包含版本号的组合
- `version + timestamp + nonce + JSON`
- `timestamp + version + nonce + JSON`
- `timestamp + nonce + version + JSON`
- `timestamp + nonce + JSON + version`

### 4. 其他哈希算法
- MD5
- SHA1
- SHA224
- SHA256
- SHA384
- SHA512

### 5. 特殊组合
- 包含头部信息（sv=3, version=45）
- URL编码的JSON
- 不同的JSON格式（有空格、无空格、不排序）
- 查询字符串格式

## 分析结果

**所有尝试的算法都未能匹配抓包数据中的签名。**

## 发现的关键信息

### 1. WebAssembly实现
在微信小程序源代码中发现了以下关键函数：
```javascript
exports.calculate_signature = function(e, n) {
    var r = E(e, d.__wbindgen_malloc, d.__wbindgen_realloc),
        t = B,
        o = E(n, d.__wbindgen_malloc, d.__wbindgen_realloc),
        i = B;
    return m(d.calculate_signature(r, t, o, i))
}
```

这表明签名算法是通过WebAssembly实现的，这解释了为什么常规的JavaScript签名算法无法匹配。

### 2. HMAC-SHA256相关代码
源代码中确实包含HMAC-SHA256相关的实现，但具体的签名逻辑被封装在WebAssembly模块中。

## 结论

1. **签名算法复杂性**: 微信小程序使用了WebAssembly实现的签名算法，这使得逆向工程变得非常困难。

2. **常规方法无效**: 传统的HMAC-SHA256组合无法生成匹配的签名，说明算法包含了额外的处理逻辑。

3. **可能的实现方式**:
   - 使用了自定义的加密算法
   - 包含了额外的参数或处理步骤
   - 可能使用了多轮哈希或其他复杂的加密操作

## 建议的解决方案

### 方案1: WebAssembly逆向分析
1. 提取微信小程序中的WebAssembly模块
2. 使用WebAssembly反编译工具分析二进制代码
3. 理解`calculate_signature`函数的具体实现

### 方案2: 动态分析
1. 在微信开发者工具中调试小程序
2. Hook `calculate_signature`函数的输入输出
3. 分析函数调用时的参数和返回值

### 方案3: 网络层拦截
1. 使用代理工具拦截微信小程序的网络请求
2. 分析完整的请求头和请求体
3. 寻找可能遗漏的参数

### 方案4: 替代方案
1. 如果签名算法过于复杂，考虑使用其他方式
2. 直接使用微信小程序的网络请求能力
3. 或者寻找官方API接口

## 技术细节

### 测试的签名字符串格式示例
```
1751174204050 + 24d4d7ffa + {"currentPage":0,"pageSize":0,"sign":"6b95be8d25574dad856cdf7939bfe469@61fa608e14c37c20fde47c700ec90414"}
```

### 测试的密钥格式示例
```
wechat_key原始: zB0ZnBVP+iBBQjV0zEi0yA==
wechat_key解码: [16字节二进制数据]
walmart_sign前部分: 6b95be8d25574dad856cdf7939bfe469
walmart_sign后部分: 61fa608e14c37c20fde47c700ec90414
```

## 下一步行动

1. **优先级1**: 尝试提取和分析WebAssembly模块
2. **优先级2**: 使用微信开发者工具进行动态调试
3. **优先级3**: 寻找是否有其他可用的API接口
4. **优先级4**: 考虑使用模拟器或自动化工具直接操作微信小程序

## 附录

### 相关文件
- `signature_analysis.py`: 基础签名算法测试
- `advanced_signature_analysis.py`: 高级签名算法测试
- `bingcard.py`: bindCard接口抓包数据
- `getuserinfo.py`: getUserInfo接口抓包数据

### 微信小程序信息
- **AppID**: wx81d3e1fe4c2e11b4
- **版本**: 175
- **源代码路径**: D:\Software\Tools\Idea\ReverseTool\wxxcx\wx81d3e1fe4c2e11b4\175\
