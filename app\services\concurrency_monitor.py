"""
并发监控和诊断服务
用于监控系统并发性能和诊断并发问题
"""

import asyncio
import time
from typing import Dict, Any
from dataclasses import dataclass, asdict
from collections import defaultdict, deque
from contextlib import asynccontextmanager

# 尝试导入psutil，如果失败则使用模拟值
try:
    import psutil
    PSUTIL_AVAILABLE = True
except ImportError:
    PSUTIL_AVAILABLE = False

from app.core.logging import get_logger
from app.core.redis import get_redis
from app.db.session import AsyncSessionLocal

logger = get_logger(__name__)


@dataclass
class ConcurrencyMetrics:
    """并发指标数据类"""
    timestamp: float
    active_connections: int
    queue_size: int
    processing_count: int
    avg_response_time: float
    error_rate: float
    memory_usage_mb: float
    cpu_usage_percent: float


@dataclass
class DeadlockInfo:
    """死锁信息数据类"""
    timestamp: float
    trace_id: str
    operation: str
    duration: float
    error_message: str


class ConcurrencyMonitor:
    """并发监控器"""

    def __init__(self):
        self.redis_client = None
        self.metrics_history = deque(maxlen=1000)  # 保留最近1000个指标点
        self.deadlock_history = deque(maxlen=100)  # 保留最近100个死锁记录
        self.active_operations = {}  # 活跃操作追踪
        self.performance_counters = defaultdict(int)
        self._monitoring_enabled = True

    async def _get_redis_client(self):
        """获取Redis客户端"""
        if self.redis_client is None:
            try:
                self.redis_client = await get_redis()
            except Exception as e:
                logger.warning(f"Redis连接失败: {e}")
                self.redis_client = None
        return self.redis_client
        
    async def start_monitoring(self):
        """启动监控任务"""
        if not self._monitoring_enabled:
            return
            
        logger.info("启动并发监控服务")
        
        # 启动后台监控任务
        asyncio.create_task(self._collect_metrics_loop())
        asyncio.create_task(self._detect_deadlocks_loop())
        asyncio.create_task(self._cleanup_stale_operations_loop())
    
    async def _collect_metrics_loop(self):
        """指标收集循环"""
        while self._monitoring_enabled:
            try:
                metrics = await self._collect_current_metrics()
                self.metrics_history.append(metrics)
                
                # 检查是否有异常指标
                await self._check_anomalies(metrics)
                
            except Exception as e:
                logger.error(f"指标收集失败: {e}")
            
            await asyncio.sleep(5)  # 每5秒收集一次
    
    async def _collect_current_metrics(self) -> ConcurrencyMetrics:
        """收集当前指标"""
        try:
            # 数据库连接数
            active_connections = await self._get_db_connection_count()
            
            # 队列大小
            queue_size = await self._get_queue_size()
            
            # 处理中的请求数
            processing_count = len(self.active_operations)
            
            # 平均响应时间
            avg_response_time = await self._calculate_avg_response_time()
            
            # 错误率
            error_rate = await self._calculate_error_rate()
            
            # 系统资源使用情况
            if PSUTIL_AVAILABLE:
                memory_usage = psutil.virtual_memory().used / 1024 / 1024  # MB
                cpu_usage = psutil.cpu_percent()
            else:
                memory_usage = 0.0  # 模拟值
                cpu_usage = 0.0  # 模拟值
            
            return ConcurrencyMetrics(
                timestamp=time.time(),
                active_connections=active_connections,
                queue_size=queue_size,
                processing_count=processing_count,
                avg_response_time=avg_response_time,
                error_rate=error_rate,
                memory_usage_mb=memory_usage,
                cpu_usage_percent=cpu_usage
            )
            
        except Exception as e:
            logger.error(f"收集指标失败: {e}")
            return ConcurrencyMetrics(
                timestamp=time.time(),
                active_connections=0,
                queue_size=0,
                processing_count=0,
                avg_response_time=0.0,
                error_rate=0.0,
                memory_usage_mb=0.0,
                cpu_usage_percent=0.0
            )
    
    async def _get_db_connection_count(self) -> int:
        """获取数据库连接数"""
        try:
            async with AsyncSessionLocal() as db:
                # 这里可以执行查询来获取连接池状态
                # 简化实现，返回估算值
                return len(self.active_operations)
        except:
            return 0
    
    async def _get_queue_size(self) -> int:
        """获取队列大小"""
        try:
            redis_client = await self._get_redis_client()
            if redis_client:
                # 从Redis获取队列长度信息
                queue_info = await redis_client.get("queue:bind_card:size")
                return int(queue_info) if queue_info else 0
            return 0
        except:
            return 0
    
    async def _calculate_avg_response_time(self) -> float:
        """计算平均响应时间"""
        if not self.metrics_history:
            return 0.0
        
        recent_metrics = list(self.metrics_history)[-10:]  # 最近10个指标点
        if not recent_metrics:
            return 0.0
        
        total_time = sum(m.avg_response_time for m in recent_metrics)
        return total_time / len(recent_metrics)
    
    async def _calculate_error_rate(self) -> float:
        """计算错误率"""
        total_requests = self.performance_counters.get('total_requests', 0)
        total_errors = self.performance_counters.get('total_errors', 0)
        
        if total_requests == 0:
            return 0.0
        
        return (total_errors / total_requests) * 100
    
    async def _check_anomalies(self, metrics: ConcurrencyMetrics):
        """检查异常指标"""
        # 高CPU使用率告警
        if metrics.cpu_usage_percent > 80:
            logger.warning(f"CPU使用率过高: {metrics.cpu_usage_percent:.1f}%")
        
        # 高内存使用率告警
        if metrics.memory_usage_mb > 1024:  # 1GB
            logger.warning(f"内存使用率过高: {metrics.memory_usage_mb:.1f}MB")
        
        # 响应时间过长告警
        if metrics.avg_response_time > 10:  # 10秒
            logger.warning(f"平均响应时间过长: {metrics.avg_response_time:.2f}s")
        
        # 错误率过高告警
        if metrics.error_rate > 10:  # 10%
            logger.warning(f"错误率过高: {metrics.error_rate:.1f}%")
    
    async def _detect_deadlocks_loop(self):
        """死锁检测循环"""
        while self._monitoring_enabled:
            try:
                await self._detect_potential_deadlocks()
            except Exception as e:
                logger.error(f"死锁检测失败: {e}")
            
            await asyncio.sleep(10)  # 每10秒检测一次
    
    async def _detect_potential_deadlocks(self):
        """检测潜在死锁"""
        current_time = time.time()
        deadlock_threshold = 30  # 30秒超时阈值
        
        for trace_id, operation_info in list(self.active_operations.items()):
            duration = current_time - operation_info['start_time']
            
            if duration > deadlock_threshold:
                deadlock_info = DeadlockInfo(
                    timestamp=current_time,
                    trace_id=trace_id,
                    operation=operation_info['operation'],
                    duration=duration,
                    error_message=f"操作超时，可能存在死锁"
                )
                
                self.deadlock_history.append(deadlock_info)
                logger.error(f"检测到潜在死锁: {asdict(deadlock_info)}")
                
                # 清理超时操作
                del self.active_operations[trace_id]
    
    async def _cleanup_stale_operations_loop(self):
        """清理过期操作循环"""
        while self._monitoring_enabled:
            try:
                current_time = time.time()
                stale_threshold = 300  # 5分钟
                
                stale_operations = [
                    trace_id for trace_id, info in self.active_operations.items()
                    if current_time - info['start_time'] > stale_threshold
                ]
                
                for trace_id in stale_operations:
                    del self.active_operations[trace_id]
                    logger.debug(f"清理过期操作: {trace_id}")
                    
            except Exception as e:
                logger.error(f"清理过期操作失败: {e}")
            
            await asyncio.sleep(60)  # 每分钟清理一次
    
    @asynccontextmanager
    async def track_operation(self, trace_id: str, operation: str):
        """追踪操作上下文管理器"""
        start_time = time.time()
        
        # 记录操作开始
        self.active_operations[trace_id] = {
            'operation': operation,
            'start_time': start_time
        }
        
        try:
            yield
            
            # 操作成功完成
            duration = time.time() - start_time
            self.performance_counters['total_requests'] += 1
            
            if duration > 5:  # 慢操作记录
                logger.info(f"慢操作完成: {operation}, trace_id={trace_id}, 耗时={duration:.2f}s")
                
        except Exception as e:
            # 操作失败
            duration = time.time() - start_time
            self.performance_counters['total_requests'] += 1
            self.performance_counters['total_errors'] += 1
            
            logger.error(f"操作失败: {operation}, trace_id={trace_id}, 耗时={duration:.2f}s, 错误={e}")
            raise
            
        finally:
            # 清理操作记录
            self.active_operations.pop(trace_id, None)
    
    def get_current_status(self) -> Dict[str, Any]:
        """获取当前状态"""
        latest_metrics = self.metrics_history[-1] if self.metrics_history else None
        
        return {
            "monitoring_enabled": self._monitoring_enabled,
            "active_operations_count": len(self.active_operations),
            "metrics_history_size": len(self.metrics_history),
            "deadlock_history_size": len(self.deadlock_history),
            "latest_metrics": asdict(latest_metrics) if latest_metrics else None,
            "performance_counters": dict(self.performance_counters),
            "recent_deadlocks": [asdict(d) for d in list(self.deadlock_history)[-5:]]
        }
    
    def stop_monitoring(self):
        """停止监控"""
        self._monitoring_enabled = False
        logger.info("并发监控服务已停止")


# 全局监控实例
concurrency_monitor = ConcurrencyMonitor()
