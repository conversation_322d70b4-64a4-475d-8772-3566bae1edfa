FROM docker.1ms.run/debian:stable-slim

WORKDIR /app

# 设置apt源为阿里云源
RUN echo "deb https://mirrors.aliyun.com/debian/ bullseye main contrib non-free" > /etc/apt/sources.list && \
    echo "deb https://mirrors.aliyun.com/debian/ bullseye-updates main contrib non-free" >> /etc/apt/sources.list && \
    apt-get update && apt-get install

# 安装curl用于健康检查
RUN apt-get update && apt-get install -y curl && rm -rf /var/lib/apt/lists/*

# 复制可执行文件
COPY dist_linux/walmart-bind-card-server-linux .

# 复制配置文件
COPY config.yaml .

# 设置环境变量
ENV DATABASE_HOST=db \
    DATABASE_PORT=3306 \
    DATABASE_USER=root \
    DATABASE_PASSWORD=7c222fb2927d828af22f592134e8932480637c0d \
    DATABASE_NAME=walmart_card_db \
    REDIS_HOST=redis \
    REDIS_PORT=6379 \
    REDIS_PASSWORD=7c222fb2927d828af22f592134e8932480637c0d \
    RABBITMQ_HOST=rabbitmq \
    RABBITMQ_PORT=5672 \
    RABBITMQ_USER=walmart_card \
    RABBITMQ_PASSWORD=7c222fb2927d828af22f592134e8932480637c0d \
    RABBITMQ_VHOST=/walmart_card

# 确保可执行文件有执行权限
RUN chmod +x /app/walmart-bind-card-server-linux

# 暴露服务端口
EXPOSE 20000

# 健康检查
HEALTHCHECK --interval=30s --timeout=3s \
    CMD curl -f http://localhost:20000/health || exit 1

# 启动命令 - 直接运行可执行文件
CMD ["./walmart-bind-card-server-linux"]
