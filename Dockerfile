# 沃尔玛绑卡处理器 - 使用预编译文件的Docker镜像
# 直接使用本地编译好的 walmart-bind-card-processor 文件

# 使用轻量级Ubuntu基础镜像
ARG DOCKER_REGISTRY=docker.1ms.run
FROM ${DOCKER_REGISTRY}/ubuntu:24.04

# 设置非交互模式
ENV DEBIAN_FRONTEND=noninteractive

# 直接写入阿里云APT源配置
RUN echo "deb http://mirrors.aliyun.com/ubuntu/ noble main restricted universe multiverse" > /etc/apt/sources.list && \
    echo "deb http://mirrors.aliyun.com/ubuntu/ noble-updates main restricted universe multiverse" >> /etc/apt/sources.list && \
    echo "deb http://mirrors.aliyun.com/ubuntu/ noble-backports main restricted universe multiverse" >> /etc/apt/sources.list && \
    echo "deb http://mirrors.aliyun.com/ubuntu/ noble-security main restricted universe multiverse" >> /etc/apt/sources.list

# 更新包列表并安装必要的包
RUN apt-get update && apt-get install -y \
    ca-certificates \
    tzdata \
    curl \
    wget \
    && rm -rf /var/lib/apt/lists/*

# 设置时区
ENV TZ=Asia/Shanghai
RUN ln -snf /usr/share/zoneinfo/$TZ /etc/localtime && echo $TZ > /etc/timezone

# 创建非root用户
RUN groupadd -g 1001 appgroup && \
    useradd -u 1001 -g appgroup -m -s /bin/bash appuser

# 设置工作目录
WORKDIR /app

# 复制预编译的二进制文件（从本地）
# 注意：这里使用本地编译好的文件
COPY walmart-bind-card-processor ./walmart-bind-card-processor

# 复制配置文件模板
COPY config.yaml ./config.yaml.template

# 创建日志目录
RUN mkdir -p /app/logs

# 设置文件权限
RUN chmod +x /app/walmart-bind-card-processor && \
    chown -R appuser:appgroup /app

# 创建启动脚本
COPY start.sh /app/start.sh
RUN chmod +x /app/start.sh && \
    chown appuser:appgroup /app/start.sh

# 切换到非root用户
USER appuser

# 暴露端口
EXPOSE 22000

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=30s --retries=3 \
    CMD curl -f http://localhost:22000/health || exit 1

# 启动应用
CMD ["/app/start.sh"]
