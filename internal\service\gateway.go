package service

import (
	"context"
	"fmt"
	"sync"
	"sync/atomic"
	"time"

	"github.com/google/uuid"
	"go.uber.org/zap"

	"walmart-bind-card-gateway/internal/config"
	"walmart-bind-card-gateway/internal/model"
	"walmart-bind-card-gateway/internal/repository"
	"walmart-bind-card-gateway/pkg/queue"
	"walmart-bind-card-gateway/pkg/redis"
)

// GatewayService 网关服务接口
type GatewayService interface {
	// 生命周期管理
	Start() error
	Stop() error
	
	// 请求处理
	SubmitRequest(req *model.InternalBindRequest) error
	GenerateRequestID() string
	
	// 限流检查
	CheckRateLimit(clientIP string) bool
	
	// 监控指标
	GetMetrics() map[string]interface{}
	RecordLatency(operation string, duration time.Duration)
}

// HighThroughputGateway 高吞吐量网关实现
type HighThroughputGateway struct {
	// 配置
	config *config.Config
	logger *zap.Logger
	
	// 依赖服务
	cardRepo  repository.CardRepository
	redisClient redis.Client
	mqPublisher queue.Publisher
	
	// 批量处理
	requestChan   chan *model.InternalBindRequest
	batchBuffer   []*model.InternalBindRequest
	batchMutex    sync.Mutex
	
	// 控制
	ctx    context.Context
	cancel context.CancelFunc
	wg     sync.WaitGroup
	
	// 监控指标
	metrics *GatewayMetrics
	
	// 限流器
	rateLimiter *RateLimiter
}

// GatewayMetrics 网关指标
type GatewayMetrics struct {
	RequestsReceived    int64 `json:"requests_received"`
	RequestsDropped     int64 `json:"requests_dropped"`
	BatchesProcessed    int64 `json:"batches_processed"`
	DatabaseWrites      int64 `json:"database_writes"`
	RedisWrites         int64 `json:"redis_writes"`
	QueuePublishes      int64 `json:"queue_publishes"`
	DatabaseErrors      int64 `json:"database_errors"`
	RedisErrors         int64 `json:"redis_errors"`
	QueueErrors         int64 `json:"queue_errors"`
	
	// 延迟统计
	LatencyStats map[string]*LatencyStats `json:"latency_stats"`
	mutex        sync.RWMutex
}

// LatencyStats 延迟统计
type LatencyStats struct {
	Count    int64         `json:"count"`
	Total    time.Duration `json:"total"`
	Min      time.Duration `json:"min"`
	Max      time.Duration `json:"max"`
	Average  time.Duration `json:"average"`
}

// RateLimiter 简单的内存限流器
type RateLimiter struct {
	requests map[string]*ClientRequests
	mutex    sync.RWMutex
	limit    int
	window   time.Duration
}

// ClientRequests 客户端请求记录
type ClientRequests struct {
	Count     int
	Window    time.Time
	mutex     sync.Mutex
}

// NewHighThroughputGateway 创建高吞吐量网关
func NewHighThroughputGateway(
	cfg *config.Config,
	logger *zap.Logger,
	cardRepo repository.CardRepository,
	redisClient redis.Client,
	mqPublisher queue.Publisher,
) *HighThroughputGateway {
	ctx, cancel := context.WithCancel(context.Background())
	
	gateway := &HighThroughputGateway{
		config:      cfg,
		logger:      logger,
		cardRepo:    cardRepo,
		redisClient: redisClient,
		mqPublisher: mqPublisher,
		requestChan: make(chan *model.InternalBindRequest, cfg.Concurrency.QueueBufferSize),
		batchBuffer: make([]*model.InternalBindRequest, 0, cfg.Concurrency.BatchSize),
		ctx:         ctx,
		cancel:      cancel,
		metrics:     NewGatewayMetrics(),
		rateLimiter: NewRateLimiter(cfg.API.RateLimit.RequestsPerMinute, time.Minute),
	}
	
	return gateway
}

// NewGatewayMetrics 创建网关指标
func NewGatewayMetrics() *GatewayMetrics {
	return &GatewayMetrics{
		LatencyStats: make(map[string]*LatencyStats),
	}
}

// NewRateLimiter 创建限流器
func NewRateLimiter(limit int, window time.Duration) *RateLimiter {
	return &RateLimiter{
		requests: make(map[string]*ClientRequests),
		limit:    limit,
		window:   window,
	}
}

// Start 启动网关服务
func (g *HighThroughputGateway) Start() error {
	g.logger.Info("启动高吞吐量网关服务")
	
	// 启动批量处理器
	g.wg.Add(1)
	go g.batchProcessor()
	
	// 启动定时刷新器
	g.wg.Add(1)
	go g.timeoutFlusher()
	
	// 启动指标清理器
	g.wg.Add(1)
	go g.metricsCleanup()
	
	g.logger.Info("高吞吐量网关服务启动完成")
	return nil
}

// Stop 停止网关服务
func (g *HighThroughputGateway) Stop() error {
	g.logger.Info("停止高吞吐量网关服务")
	
	g.cancel()
	close(g.requestChan)
	
	// 等待所有goroutine完成
	done := make(chan struct{})
	go func() {
		g.wg.Wait()
		close(done)
	}()
	
	// 等待优雅关闭或超时
	select {
	case <-done:
		g.logger.Info("网关服务优雅关闭完成")
	case <-time.After(g.config.Concurrency.GracefulShutdownTimeout):
		g.logger.Warn("网关服务关闭超时")
	}
	
	return nil
}

// SubmitRequest 提交请求
func (g *HighThroughputGateway) SubmitRequest(req *model.InternalBindRequest) error {
	select {
	case g.requestChan <- req:
		atomic.AddInt64(&g.metrics.RequestsReceived, 1)
		return nil
	default:
		atomic.AddInt64(&g.metrics.RequestsDropped, 1)
		return fmt.Errorf("请求队列已满")
	}
}

// GenerateRequestID 生成请求ID
func (g *HighThroughputGateway) GenerateRequestID() string {
	return uuid.New().String()
}

// CheckRateLimit 检查限流
func (g *HighThroughputGateway) CheckRateLimit(clientIP string) bool {
	return g.rateLimiter.Allow(clientIP)
}

// batchProcessor 批量处理器
func (g *HighThroughputGateway) batchProcessor() {
	defer g.wg.Done()
	
	for {
		select {
		case req, ok := <-g.requestChan:
			if !ok {
				// 通道关闭，处理剩余数据
				g.flushBatch()
				return
			}
			
			g.batchMutex.Lock()
			g.batchBuffer = append(g.batchBuffer, req)
			
			// 检查是否达到批量大小
			if len(g.batchBuffer) >= g.config.Concurrency.BatchSize {
				g.processBatch()
			}
			g.batchMutex.Unlock()
			
		case <-g.ctx.Done():
			g.flushBatch()
			return
		}
	}
}

// timeoutFlusher 定时刷新器
func (g *HighThroughputGateway) timeoutFlusher() {
	defer g.wg.Done()
	
	ticker := time.NewTicker(g.config.Concurrency.BatchTimeout)
	defer ticker.Stop()
	
	for {
		select {
		case <-ticker.C:
			g.batchMutex.Lock()
			if len(g.batchBuffer) > 0 {
				g.processBatch()
			}
			g.batchMutex.Unlock()
			
		case <-g.ctx.Done():
			return
		}
	}
}

// processBatch 处理批量数据
func (g *HighThroughputGateway) processBatch() {
	if len(g.batchBuffer) == 0 {
		return
	}
	
	batch := make([]*model.InternalBindRequest, len(g.batchBuffer))
	copy(batch, g.batchBuffer)
	g.batchBuffer = g.batchBuffer[:0] // 清空缓冲区
	
	// 异步处理批量数据
	go g.processBatchAsync(batch)
}

// processBatchAsync 异步处理批量数据
func (g *HighThroughputGateway) processBatchAsync(batch []*model.InternalBindRequest) {
	startTime := time.Now()
	
	// 1. 先批量写入数据库获取记录ID
	dbStart := time.Now()
	records, err := g.cardRepo.BatchCreateAndReturn(g.ctx, batch)
	if err != nil {
		atomic.AddInt64(&g.metrics.DatabaseErrors, 1)
		g.logger.Error("批量写入数据库失败", zap.Error(err))
		return
	}
	atomic.AddInt64(&g.metrics.DatabaseWrites, int64(len(batch)))
	g.RecordLatency("database_write", time.Since(dbStart))

	// 2. 并行处理Redis和队列操作
	var wg sync.WaitGroup
	wg.Add(2)

	// 2.1 批量写入Redis
	go func() {
		defer wg.Done()
		redisStart := time.Now()

		if err := g.redisClient.BatchSetCardStatusFromRequests(g.ctx, batch); err != nil {
			atomic.AddInt64(&g.metrics.RedisErrors, 1)
			g.logger.Error("批量写入Redis失败", zap.Error(err))
		} else {
			atomic.AddInt64(&g.metrics.RedisWrites, int64(len(batch)))
			g.RecordLatency("redis_write", time.Since(redisStart))
		}
	}()

	// 2.2 批量发送到队列（使用记录ID）
	go func() {
		defer wg.Done()
		queueStart := time.Now()

		if err := g.mqPublisher.BatchPublishFromRequestsWithRecords(g.ctx, batch, records); err != nil {
			atomic.AddInt64(&g.metrics.QueueErrors, 1)
			g.logger.Error("批量发送到队列失败", zap.Error(err))
		} else {
			atomic.AddInt64(&g.metrics.QueuePublishes, int64(len(batch)))
			g.RecordLatency("queue_publish", time.Since(queueStart))
		}
	}()

	wg.Wait()
	
	// 记录批量处理时间和计数
	atomic.AddInt64(&g.metrics.BatchesProcessed, 1)
	g.RecordLatency("batch_process", time.Since(startTime))
	
	g.logger.Debug("批量处理完成",
		zap.Int("batch_size", len(batch)),
		zap.Duration("duration", time.Since(startTime)),
	)
}

// flushBatch 刷新批量数据
func (g *HighThroughputGateway) flushBatch() {
	g.batchMutex.Lock()
	defer g.batchMutex.Unlock()
	
	if len(g.batchBuffer) > 0 {
		g.processBatch()
	}
}

// Allow 检查是否允许请求
func (r *RateLimiter) Allow(clientIP string) bool {
	r.mutex.Lock()
	defer r.mutex.Unlock()
	
	now := time.Now()
	
	clientReq, exists := r.requests[clientIP]
	if !exists {
		r.requests[clientIP] = &ClientRequests{
			Count:  1,
			Window: now,
		}
		return true
	}
	
	clientReq.mutex.Lock()
	defer clientReq.mutex.Unlock()
	
	// 检查时间窗口
	if now.Sub(clientReq.Window) > r.window {
		// 重置窗口
		clientReq.Count = 1
		clientReq.Window = now
		return true
	}
	
	// 检查限制
	if clientReq.Count >= r.limit {
		return false
	}
	
	clientReq.Count++
	return true
}

// metricsCleanup 指标清理
func (g *HighThroughputGateway) metricsCleanup() {
	defer g.wg.Done()
	
	ticker := time.NewTicker(5 * time.Minute)
	defer ticker.Stop()
	
	for {
		select {
		case <-ticker.C:
			// 清理限流器中的过期记录
			g.rateLimiter.cleanup()
			
		case <-g.ctx.Done():
			return
		}
	}
}

// cleanup 清理过期的限流记录
func (r *RateLimiter) cleanup() {
	r.mutex.Lock()
	defer r.mutex.Unlock()
	
	now := time.Now()
	for clientIP, clientReq := range r.requests {
		if now.Sub(clientReq.Window) > r.window*2 {
			delete(r.requests, clientIP)
		}
	}
}

// GetMetrics 获取监控指标
func (g *HighThroughputGateway) GetMetrics() map[string]interface{} {
	g.metrics.mutex.RLock()
	defer g.metrics.mutex.RUnlock()
	
	return map[string]interface{}{
		"requests_received":  atomic.LoadInt64(&g.metrics.RequestsReceived),
		"requests_dropped":   atomic.LoadInt64(&g.metrics.RequestsDropped),
		"batches_processed":  atomic.LoadInt64(&g.metrics.BatchesProcessed),
		"database_writes":    atomic.LoadInt64(&g.metrics.DatabaseWrites),
		"redis_writes":       atomic.LoadInt64(&g.metrics.RedisWrites),
		"queue_publishes":    atomic.LoadInt64(&g.metrics.QueuePublishes),
		"database_errors":    atomic.LoadInt64(&g.metrics.DatabaseErrors),
		"redis_errors":       atomic.LoadInt64(&g.metrics.RedisErrors),
		"queue_errors":       atomic.LoadInt64(&g.metrics.QueueErrors),
		"latency_stats":      g.metrics.LatencyStats,
		"queue_buffer_size":  len(g.requestChan),
		"queue_buffer_cap":   cap(g.requestChan),
	}
}

// RecordLatency 记录延迟
func (g *HighThroughputGateway) RecordLatency(operation string, duration time.Duration) {
	g.metrics.mutex.Lock()
	defer g.metrics.mutex.Unlock()
	
	stats, exists := g.metrics.LatencyStats[operation]
	if !exists {
		stats = &LatencyStats{
			Min: duration,
			Max: duration,
		}
		g.metrics.LatencyStats[operation] = stats
	}
	
	stats.Count++
	stats.Total += duration
	stats.Average = stats.Total / time.Duration(stats.Count)
	
	if duration < stats.Min {
		stats.Min = duration
	}
	if duration > stats.Max {
		stats.Max = duration
	}
}
