#!/usr/bin/env python3
"""
CK供应商登录跳转测试
"""

import requests
import json
from datetime import datetime


class CKSupplierLoginRedirectTest:
    """CK供应商登录跳转测试类"""

    def __init__(self):
        self.base_url = "http://localhost:8000"
        self.session = requests.Session()
        self.test_results = []

    def log_result(self, test_name, success, message, details=None):
        """记录测试结果"""
        result = {
            "test_name": test_name,
            "success": success,
            "message": message,
            "details": details,
            "timestamp": datetime.now().isoformat()
        }
        self.test_results.append(result)
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"{status} {test_name}: {message}")
        if details:
            print(f"   详情: {details}")

    def test_test2_user_login(self):
        """测试test2用户登录"""
        try:
            # 登录test2用户
            login_data = {
                "username": "test2",
                "password": "12345678"
            }

            response = self.session.post(
                f"{self.base_url}/api/v1/auth/login",
                data=login_data
            )

            if response.status_code == 200:
                data = response.json()
                # 处理新的响应格式：{code: 0, data: {access_token: "...", token_type: "bearer"}, message: "操作成功"}
                token = data.get("data", {}).get("access_token") or data.get("access_token")

                if token:
                    self.log_result(
                        "test2用户登录",
                        True,
                        "登录成功",
                        f"获得访问令牌: {token[:20]}..."
                    )
                    return token
                else:
                    self.log_result(
                        "test2用户登录",
                        False,
                        "登录响应中没有访问令牌",
                        data
                    )
                    return None
            else:
                self.log_result(
                    "test2用户登录",
                    False,
                    f"登录失败，状态码: {response.status_code}",
                    response.text
                )
                return None

        except Exception as e:
            self.log_result(
                "test2用户登录",
                False,
                f"登录异常: {str(e)}"
            )
            return None

    def test_user_info(self, token):
        """测试获取用户信息"""
        try:
            headers = {"Authorization": f"Bearer {token}"}
            response = self.session.get(
                f"{self.base_url}/api/v1/users/me",
                headers=headers
            )

            if response.status_code == 200:
                response_data = response.json()
                # 处理新的响应格式
                user_info = response_data.get("data", response_data)

                # 验证用户信息
                expected_fields = ["id", "username", "roles", "permissions", "menus"]
                missing_fields = [field for field in expected_fields if field not in user_info]

                if missing_fields:
                    self.log_result(
                        "用户信息获取",
                        False,
                        f"用户信息缺少字段: {missing_fields}",
                        user_info
                    )
                    return None

                # 验证角色
                roles = user_info.get("roles", [])
                has_ck_supplier_role = any(role.get("code") == "ck_supplier" for role in roles)

                if has_ck_supplier_role:
                    self.log_result(
                        "用户角色验证",
                        True,
                        "test2用户具有CK供应商角色",
                        f"角色列表: {[role.get('name') for role in roles]}"
                    )
                else:
                    self.log_result(
                        "用户角色验证",
                        False,
                        "test2用户没有CK供应商角色",
                        f"角色列表: {[role.get('name') for role in roles]}"
                    )

                # 验证菜单权限
                menus = user_info.get("menus", [])
                expected_menus = ["walmart", "walmart:user", "notification"]
                has_required_menus = all(menu in menus for menu in expected_menus)

                if has_required_menus:
                    self.log_result(
                        "菜单权限验证",
                        True,
                        "CK供应商具有正确的菜单权限",
                        f"菜单列表: {menus}"
                    )
                else:
                    missing_menus = [menu for menu in expected_menus if menu not in menus]
                    self.log_result(
                        "菜单权限验证",
                        False,
                        f"缺少菜单权限: {missing_menus}",
                        f"当前菜单: {menus}"
                    )

                return user_info

            else:
                self.log_result(
                    "用户信息获取",
                    False,
                    f"获取用户信息失败，状态码: {response.status_code}",
                    response.text
                )
                return None

        except Exception as e:
            self.log_result(
                "用户信息获取",
                False,
                f"获取用户信息异常: {str(e)}"
            )
            return None

    def test_ck_management_access(self, token):
        """测试CK管理页面访问权限"""
        try:
            headers = {"Authorization": f"Bearer {token}"}
            response = self.session.get(
                f"{self.base_url}/api/v1/walmart-ck",
                headers=headers
            )

            if response.status_code == 200:
                self.log_result(
                    "CK管理API访问",
                    True,
                    "可以正常访问CK管理API",
                    f"响应状态: {response.status_code}"
                )
                return True
            else:
                self.log_result(
                    "CK管理API访问",
                    False,
                    f"无法访问CK管理API，状态码: {response.status_code}",
                    response.text
                )
                return False

        except Exception as e:
            self.log_result(
                "CK管理API访问",
                False,
                f"访问CK管理API异常: {str(e)}"
            )
            return False

    def test_notification_access(self, token):
        """测试通知中心访问权限"""
        try:
            headers = {"Authorization": f"Bearer {token}"}
            response = self.session.get(
                f"{self.base_url}/api/v1/notifications",
                headers=headers
            )

            if response.status_code == 200:
                self.log_result(
                    "通知中心API访问",
                    True,
                    "可以正常访问通知中心API",
                    f"响应状态: {response.status_code}"
                )
                return True
            else:
                self.log_result(
                    "通知中心API访问",
                    False,
                    f"无法访问通知中心API，状态码: {response.status_code}",
                    response.text
                )
                return False

        except Exception as e:
            self.log_result(
                "通知中心API访问",
                False,
                f"访问通知中心API异常: {str(e)}"
            )
            return False

    def test_restricted_access(self, token):
        """测试受限页面访问（应该被拒绝）"""
        restricted_apis = [
            ("/api/v1/merchants", "商户管理API"),
            ("/api/v1/users", "用户管理API"),
            ("/api/v1/roles", "角色管理API"),
            ("/api/v1/walmart-server", "沃尔玛服务器配置API")
        ]

        headers = {"Authorization": f"Bearer {token}"}

        for api_path, api_name in restricted_apis:
            try:
                response = self.session.get(
                    f"{self.base_url}{api_path}",
                    headers=headers
                )

                if response.status_code in [401, 403]:
                    self.log_result(
                        f"权限隔离验证-{api_name}",
                        True,
                        f"正确拒绝访问{api_name}",
                        f"状态码: {response.status_code}"
                    )
                else:
                    self.log_result(
                        f"权限隔离验证-{api_name}",
                        False,
                        f"权限隔离失败，不应该能访问{api_name}",
                        f"状态码: {response.status_code}"
                    )

            except Exception as e:
                self.log_result(
                    f"权限隔离验证-{api_name}",
                    False,
                    f"测试{api_name}访问异常: {str(e)}"
                )

    def run_all_tests(self):
        """运行所有测试"""
        print("=" * 60)
        print("CK供应商登录跳转测试开始")
        print("=" * 60)

        # 1. 测试登录
        token = self.test_test2_user_login()
        if not token:
            print("\n❌ 登录失败，无法继续后续测试")
            return

        # 2. 测试用户信息
        user_info = self.test_user_info(token)

        # 3. 测试允许的API访问
        self.test_ck_management_access(token)
        self.test_notification_access(token)

        # 4. 测试受限API访问
        self.test_restricted_access(token)

        # 5. 生成测试报告
        self.generate_report()

    def generate_report(self):
        """生成测试报告"""
        print("\n" + "=" * 60)
        print("测试结果汇总")
        print("=" * 60)

        total_tests = len(self.test_results)
        passed_tests = sum(1 for result in self.test_results if result["success"])
        failed_tests = total_tests - passed_tests

        print(f"总测试数: {total_tests}")
        print(f"通过: {passed_tests}")
        print(f"失败: {failed_tests}")
        print(f"成功率: {passed_tests/total_tests*100:.1f}%")

        if failed_tests > 0:
            print("\n失败的测试:")
            for result in self.test_results:
                if not result["success"]:
                    print(f"  - {result['test_name']}: {result['message']}")

        # 保存详细报告
        report_file = f"test/reports/ck_supplier_login_test_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        try:
            import os
            os.makedirs("test/reports", exist_ok=True)
            with open(report_file, 'w', encoding='utf-8') as f:
                json.dump(self.test_results, f, ensure_ascii=False, indent=2)
            print(f"\n详细报告已保存到: {report_file}")
        except Exception as e:
            print(f"\n保存报告失败: {e}")


if __name__ == "__main__":
    test = CKSupplierLoginRedirectTest()
    test.run_all_tests()
