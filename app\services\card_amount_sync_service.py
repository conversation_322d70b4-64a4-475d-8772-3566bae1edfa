"""
卡片金额同步服务
专门处理卡片真实金额的获取和更新
"""

import uuid
from datetime import datetime
from typing import Dict, Any, Optional

from sqlalchemy.orm import Session

from app.core.logging import get_logger
from app.crud import card as card_crud
from app.models.card_record import CardRecord, CardStatus
from app.services.amount_validation_service import AmountValidationService
from app.services.binding_log_service import BindingLogService
from app.models.binding_log import LogLevel, LogType

logger = get_logger("card_amount_sync_service")


class CardAmountSyncService:
    """卡片金额同步服务"""

    def __init__(self, db: Session):
        self.db = db
        self.amount_service = AmountValidationService()

    def _get_binding_log_service(self) -> BindingLogService:
        """【安全修复】获取BindingLogService实例的辅助方法"""
        return BindingLogService(self.db)

    async def sync_card_amount(
        self,
        card_id: str,
        force_update: bool = False
    ) -> Dict[str, Any]:
        """
        同步卡片真实金额

        Args:
            card_id: 卡记录ID
            force_update: 是否强制更新（即使已有金额信息）

        Returns:
            Dict[str, Any]: 同步结果
        """
        try:
            # 获取卡记录
            record = card_crud.get(self.db, id=card_id)
            if not record:
                return {
                    "success": False,
                    "error": "卡记录不存在",
                    "code": "RECORD_NOT_FOUND"
                }

            # 检查卡状态
            if record.status != CardStatus.SUCCESS:
                return {
                    "success": False,
                    "error": "只能同步绑卡成功的卡片金额",
                    "code": "INVALID_STATUS",
                    "current_status": record.status
                }

            # 检查是否已经完全通过（绑卡成功 + 回调成功 + 有金额）
            from app.models.card_record import CallbackStatus
            if (not force_update and
                record.actual_amount is not None and
                record.callback_status == CallbackStatus.SUCCESS):
                return {
                    "success": False,
                    "error": "该卡片已完成回调且有金额信息，无需再次同步",
                    "code": "ALREADY_COMPLETED",
                    "actual_amount": record.actual_amount,
                    "callback_status": record.callback_status
                }

            # 检查是否需要更新（仅有金额但未完成回调的情况）
            if not force_update and record.actual_amount is not None:
                return {
                    "success": True,
                    "message": "卡片金额已存在，无需更新",
                    "code": "AMOUNT_EXISTS",
                    "actual_amount": record.actual_amount
                }

            # 记录开始同步日志
            await self._log_sync_start(record)

            # 检查记录中是否有walmart_ck_id（关键修复）
            if not record.walmart_ck_id:
                error_msg = "记录中缺少walmart_ck_id，无法同步金额。请确保该卡片已成功绑卡并保存了CK信息。"
                logger.error(f"[SYNC_ERROR] {error_msg} | record_id={record.id}")
                await self._log_sync_failure(record, error_msg)
                return {
                    "success": False,
                    "error": error_msg,
                    "code": "MISSING_WALMART_CK_ID"
                }

            # 构建包含walmart_ck_id的结果数据（修复关键问题）
            sync_result = {
                "success": True,
                "walmart_ck_id": record.walmart_ck_id,  # 从记录中获取
                "data": {}  # 金额数据将通过API获取
            }

            # 执行金额验证和更新
            amount_result = await self.amount_service.validate_card_amount(
                self.db, record, sync_result
            )

            if amount_result["success"]:
                # 更新记录
                update_data = amount_result["update_fields"]
                for field, value in update_data.items():
                    setattr(record, field, value)
                
                self.db.commit()

                # 记录成功日志
                await self._log_sync_success(record, update_data)

                return {
                    "success": True,
                    "message": "卡片金额同步成功",
                    "code": "SYNC_SUCCESS",
                    "actual_amount": getattr(record, 'actual_amount', None),
                    "balance_info": {
                        "balance": getattr(record, 'balance', None),
                        "cardBalance": getattr(record, 'cardBalance', None),
                        "balanceCnt": getattr(record, 'balanceCnt', None)
                    }
                }
            else:
                # 记录失败日志
                await self._log_sync_failure(record, amount_result["error_message"])

                return {
                    "success": False,
                    "error": amount_result["error_message"],
                    "code": "AMOUNT_FETCH_FAILED"
                }

        except Exception as e:
            self.db.rollback()
            logger.error(f"卡片金额同步异常: {e}")
            
            # 记录异常日志
            if 'record' in locals():
                await self._log_sync_exception(record, str(e))

            return {
                "success": False,
                "error": f"金额同步过程中发生异常: {str(e)}",
                "code": "SYNC_EXCEPTION"
            }

    async def batch_sync_card_amounts(
        self,
        card_ids: list,
        force_update: bool = False
    ) -> Dict[str, Any]:
        """
        批量同步卡片金额

        Args:
            card_ids: 卡记录ID列表
            force_update: 是否强制更新

        Returns:
            Dict[str, Any]: 批量同步结果
        """
        if not card_ids:
            return {
                "success": True,
                "message": "没有需要同步的卡片",
                "results": []
            }

        results = []
        success_count = 0
        fail_count = 0

        for card_id in card_ids:
            try:
                result = await self.sync_card_amount(card_id, force_update)
                results.append({
                    "card_id": card_id,
                    **result
                })
                
                if result["success"]:
                    success_count += 1
                else:
                    fail_count += 1

            except Exception as e:
                logger.error(f"批量同步卡片 {card_id} 异常: {e}")
                results.append({
                    "card_id": card_id,
                    "success": False,
                    "error": str(e),
                    "code": "BATCH_SYNC_EXCEPTION"
                })
                fail_count += 1

        return {
            "success": True,
            "message": f"批量同步完成，成功: {success_count}，失败: {fail_count}",
            "total_count": len(card_ids),
            "success_count": success_count,
            "fail_count": fail_count,
            "results": results
        }

    async def _log_sync_start(self, record: CardRecord):
        """记录同步开始日志"""
        try:
            # 【安全修复】使用辅助方法获取BindingLogService实例
            binding_log_service = self._get_binding_log_service()
            await binding_log_service.log_system(
                db=self.db,
                card_record_id=str(record.id),
                message="开始同步卡片金额",
                log_level=LogLevel.INFO,
                details={
                    "action": "amount_sync_start",
                    "card_number": record.card_number[:6] + "***",
                    "current_amount": record.actual_amount,
                    "timestamp": datetime.now().isoformat()
                }
            )
        except Exception as e:
            logger.error(f"记录同步开始日志失败: {e}")

    async def _log_sync_success(self, record: CardRecord, update_data: Dict[str, Any]):
        """记录同步成功日志"""
        try:
            # 【安全修复】使用辅助方法获取BindingLogService实例
            binding_log_service = self._get_binding_log_service()
            await binding_log_service.log_system(
                db=self.db,
                card_record_id=str(record.id),
                message="卡片金额同步成功",
                log_level=LogLevel.INFO,
                details={
                    "action": "amount_sync_success",
                    "card_number": record.card_number[:6] + "***",
                    "updated_fields": list(update_data.keys()),
                    "actual_amount": getattr(record, 'actual_amount', None),
                    "timestamp": datetime.now().isoformat()
                }
            )
        except Exception as e:
            logger.error(f"记录同步成功日志失败: {e}")

    async def _log_sync_failure(self, record: CardRecord, error_message: str):
        """记录同步失败日志"""
        try:
            # 【安全修复】使用辅助方法获取BindingLogService实例
            binding_log_service = self._get_binding_log_service()
            await binding_log_service.log_system(
                db=self.db,
                card_record_id=str(record.id),
                message=f"卡片金额同步失败: {error_message}",
                log_level=LogLevel.WARNING,
                details={
                    "action": "amount_sync_failure",
                    "card_number": record.card_number[:6] + "***",
                    "error": error_message,
                    "timestamp": datetime.now().isoformat()
                }
            )
        except Exception as e:
            logger.error(f"记录同步失败日志失败: {e}")

    async def _log_sync_exception(self, record: CardRecord, exception_message: str):
        """记录同步异常日志"""
        try:
            # 【安全修复】使用辅助方法获取BindingLogService实例
            binding_log_service = self._get_binding_log_service()
            await binding_log_service.log_system(
                db=self.db,
                card_record_id=str(record.id),
                message=f"卡片金额同步异常: {exception_message}",
                log_level=LogLevel.ERROR,
                details={
                    "action": "amount_sync_exception",
                    "card_number": record.card_number[:6] + "***",
                    "exception": exception_message,
                    "timestamp": datetime.now().isoformat()
                }
            )
        except Exception as e:
            logger.error(f"记录同步异常日志失败: {e}")
