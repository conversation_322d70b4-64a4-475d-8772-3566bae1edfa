<template>
  <div class="api-key-manager">
    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <span>API密钥管理</span>
          <el-button type="warning" @click="resetApiKey">重置API密钥</el-button>
        </div>
      </template>

      <el-descriptions :column="1" border>
        <el-descriptions-item label="API密钥 (Key)">
          <div class="key-display">
            <span>{{ maskApiKey(merchant?.api_key) }}</span>
            <el-button type="primary" size="small" link
                       @click="handleCopy(merchant?.api_key)">复制
            </el-button>
          </div>
        </el-descriptions-item>
        <el-descriptions-item label="API密文 (Secret)">
          <div class="key-display">
            <span>***************************************</span>
            <el-button type="warning" size="small" link @click="showSecretDialog">查看密文</el-button>
          </div>
        </el-descriptions-item>
        <el-descriptions-item label="创建时间">
          {{ merchant?.api_key_created_at ? formatDate(merchant.api_key_created_at) : '-' }}
        </el-descriptions-item>
        <el-descriptions-item label="上次重置时间">
          {{ merchant?.api_key_updated_at ? formatDate(merchant.api_key_updated_at) : '-' }}
        </el-descriptions-item>
      </el-descriptions>

      <el-alert title="安全提示" type="warning"
                description="API密钥和密文非常重要，请妥善保管。一旦泄露，可能导致数据安全问题。重置API密钥会使原有密钥立即失效。"
                :closable="false" show-icon style="margin-top: 20px;"/>

      <el-divider content-position="left">API调用统计</el-divider>

      <el-row :gutter="20" class="usage-stats" v-loading="loading">
        <el-col :span="8">
          <el-statistic title="总调用次数" :value="apiUsage.total || 0">
            <template #suffix>
              <span>次</span>
            </template>
          </el-statistic>
        </el-col>
        <el-col :span="8">
          <el-statistic title="今日调用" :value="apiUsage.today || 0">
            <template #suffix>
              <span>次</span>
            </template>
          </el-statistic>
        </el-col>
        <el-col :span="8">
          <el-statistic title="平均响应时间" :value="apiUsage.avg_response_time || 0">
            <template #suffix>
              <span>ms</span>
            </template>
          </el-statistic>
        </el-col>
      </el-row>
    </el-card>

    <!-- API密文查看对话框 -->
    <el-dialog v-model="secretDialogVisible" title="API密文" width="40%" @closed="adminPassword = ''">
      <div class="password-verification" v-if="!secretVerified">
        <el-form @submit.prevent="verifyPassword">
          <p>出于安全考虑，请输入您的管理员密码以查看API密文</p>
          <el-form-item label-width="0">
            <el-input v-model="adminPassword" type="password" placeholder="管理员密码" show-password autofocus/>
          </el-form-item>
          <div class="dialog-footer">
            <el-button @click="secretDialogVisible = false">取消</el-button>
            <el-button type="primary" @click="verifyPassword" :loading="verifying" native-type="submit">
              验证
            </el-button>
          </div>
        </el-form>
      </div>

      <div class="secret-display" v-else>
        <el-alert title="安全警告" type="error"
                  description="这是最后一次显示完整密文，请立即保存。关闭此窗口后，密文将无法再次完整显示。"
                  :closable="false" show-icon style="margin-bottom: 20px;"/>

        <el-input v-model="displayedSecret" readonly :rows="2" type="textarea"/>

        <div class="dialog-footer">
          <el-button type="success" @click="handleCopy(displayedSecret)">
            复制密文
          </el-button>
          <el-button @click="secretDialogVisible = false">关闭</el-button>
        </div>
      </div>
    </el-dialog>

    <!-- 重置API密钥确认对话框 -->
    <el-dialog v-model="resetDialogVisible" title="重置API密钥" width="40%">
      <div class="reset-confirmation">
        <el-alert title="警告" type="error"
                  description="重置API密钥将使当前密钥立即失效，所有使用当前密钥的应用需要更新配置。此操作不可撤销！"
                  :closable="false" show-icon style="margin-bottom: 20px;"/>

        <p>请输入"RESET"确认操作:</p>
        <el-input v-model="resetConfirmText" placeholder="请输入RESET"/>
      </div>

      <template #footer>
                <span class="dialog-footer">
                    <el-button @click="resetDialogVisible = false">取消</el-button>
                    <el-button type="danger" @click="confirmResetApiKey" :disabled="resetConfirmText !== 'RESET'"
                               :loading="resetting">
                        确认重置
                    </el-button>
                </span>
      </template>
    </el-dialog>


  </div>
</template>

<script setup>
import {ref, reactive, onMounted} from 'vue'
import {ElMessage, ElMessageBox} from 'element-plus'
import {DocumentCopy} from '@element-plus/icons-vue'
import {merchantApi} from '@/api/modules/merchant'
import {formatDateTime} from '@/utils/dateUtils'
import {copyToClipboard} from '@/utils/clipboard'

const props = defineProps({
  merchant: {
    type: Object,
    required: false,
    default: null
  },
  merchantId: {
    type: [String, Number],
    required: true
  }
})

const emit = defineEmits(['api-key-reset'])

// 数据
const loading = ref(false)
const secretDialogVisible = ref(false)
const resetDialogVisible = ref(false)
const secretVerified = ref(false)
const adminPassword = ref('')
const displayedSecret = ref('')
const resetConfirmText = ref('')
const verifying = ref(false)
const resetting = ref(false)

// API使用统计
const apiUsage = reactive({
  total: 0,
  today: 0,
  avg_response_time: 0
})

// 掩码显示API密钥
const maskApiKey = (key) => {
  if (!key) return '******'
  if (key.length <= 8) return key
  return key.slice(0, 4) + '********' + key.slice(-4)
}

// 格式化日期
const formatDate = (dateString) => {
  if (!dateString) return '-'
  try {
    return formatDateTime(dateString)
  } catch (e) {
    return dateString
  }
}

// 复制到剪贴板
const handleCopy = (text) => {
  copyToClipboard(text)
}

// 显示密文对话框
const showSecretDialog = () => {
  secretDialogVisible.value = true
  secretVerified.value = false
  adminPassword.value = ''
}

// 验证管理员密码
const verifyPassword = async () => {
  if (!adminPassword.value) {
    ElMessage.warning('请输入密码')
    return
  }

  verifying.value = true
  try {
    // 调用API验证密码并获取API密文
    const result = await merchantApi.verifyPassword(props.merchantId, {
      password: adminPassword.value
    })
    // 获取实际的响应数据
    const response = result.data || result

    // 检查业务逻辑是否成功
    if (!response.success) {
      ElMessage.error(response.message || '密码验证失败')
      return
    }

    secretVerified.value = true
    displayedSecret.value = response.api_secret || ''
    ElMessage.success('验证成功')
  } catch (error) {
    console.error('验证密码失败:', error)
    ElMessage.error(error.response?.data?.message || '验证失败，请重试')
  } finally {
    verifying.value = false
  }
}

// 重置API密钥对话框
const resetApiKey = () => {
  resetDialogVisible.value = true
  resetConfirmText.value = ''
}



// 确认重置API密钥
const confirmResetApiKey = async () => {
  if (resetConfirmText.value !== 'RESET') {
    return
  }

  resetting.value = true
  try {
    // 调用API重置API密钥
    const data = await merchantApi.resetApiKey(props.merchantId)

    resetDialogVisible.value = false
    ElMessage.success('API密钥已重置')

    // 通知父组件API密钥已重置
    emit('api-key-reset', {
      api_key: data.api_key,
      api_secret: data.api_secret,
      api_key_updated_at: new Date().toISOString()
    })
  } catch (error) {
    console.error('重置API密钥失败:', error)
    ElMessage.error(error.response?.data?.message || '重置API密钥失败，请重试')
  } finally {
    resetting.value = false
  }
}



// 获取API使用统计
const fetchApiUsage = async () => {
  loading.value = true
  try {
    // API 返回的数据结构可能需要调整
    const response = await merchantApi.getApiUsage(props.merchantId)
    apiUsage.total = response.total || 0
    apiUsage.today = response.today || 0
    apiUsage.avg_response_time = response.avg_response_time || 0
  } catch (error) {
    console.error('获取API使用统计失败:', error)
    ElMessage.error('获取API使用统计失败: ' + (error.message || '请稍后重试'))
    apiUsage.total = 0
    apiUsage.today = 0
    apiUsage.avg_response_time = 0
  } finally {
    loading.value = false
  }
}

onMounted(() => {
  fetchApiUsage()
})
</script>

<style scoped>
.api-key-manager {
  margin-top: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.key-display {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.usage-stats {
  margin-top: 20px;
}

.dialog-footer {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

.password-verification,
.secret-display,
.reset-confirmation {
  padding: 10px 0;
}

.password-verification p,
.reset-confirmation p {
  margin-bottom: 15px;
  color: #606266;
}


</style>