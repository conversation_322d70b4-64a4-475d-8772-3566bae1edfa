"""
增强的错误处理服务
提供友好的错误消息和恢复指导
"""

from enum import Enum
from typing import Dict, List, Optional, Tuple
from telegram import Update, InlineKeyboardButton, InlineKeyboardMarkup
from telegram.ext import ContextTypes

from app.core.logging import get_logger

logger = get_logger(__name__)


class ErrorSeverity(Enum):
    """错误严重程度"""
    INFO = "info"           # 信息提示
    WARNING = "warning"     # 警告
    ERROR = "error"         # 错误
    CRITICAL = "critical"   # 严重错误


class ErrorCategory(Enum):
    """错误分类"""
    AUTHENTICATION = "authentication"   # 认证相关
    AUTHORIZATION = "authorization"     # 授权相关
    VALIDATION = "validation"           # 验证相关
    SYSTEM = "system"                   # 系统相关
    NETWORK = "network"                 # 网络相关
    USER_INPUT = "user_input"           # 用户输入相关


class EnhancedErrorHandler:
    """增强的错误处理器"""
    
    def __init__(self, config=None):
        self.config = config
        self.logger = logger
        
        # 错误消息模板
        self.error_templates = {
            "user_not_verified": {
                "severity": ErrorSeverity.WARNING,
                "category": ErrorCategory.AUTHENTICATION,
                "title": "🔐 身份验证需要",
                "description": "您需要先完成身份验证才能使用此功能",
                "solutions": [
                    "输入 `/verify` 开始身份验证流程",
                    "将验证令牌发送给管理员",
                    "等待管理员审核通过"
                ],
                "examples": ["/verify"],
                "estimated_time": "5-30分钟",
                "why_needed": "为了保护数据安全，确保只有授权人员可以访问敏感信息"
            },
            "group_not_bound": {
                "severity": ErrorSeverity.WARNING,
                "category": ErrorCategory.AUTHORIZATION,
                "title": "🔗 群组绑定需要",
                "description": "当前群组尚未绑定到商户系统",
                "solutions": [
                    "联系群组管理员获取绑定令牌",
                    "管理员在群组中输入 `/bind <令牌>`",
                    "等待绑定完成确认"
                ],
                "examples": ["/bind tg_bind_xxxxxxxxx"],
                "estimated_time": "5-10分钟",
                "why_needed": "群组绑定后才能查询对应商户的数据"
            },
            "invalid_token": {
                "severity": ErrorSeverity.ERROR,
                "category": ErrorCategory.VALIDATION,
                "title": "❌ 令牌无效",
                "description": "您提供的令牌无效或已过期",
                "solutions": [
                    "检查令牌是否完整复制",
                    "联系管理员获取新的令牌",
                    "确认令牌未过期（通常24小时有效）"
                ],
                "examples": ["/bind tg_bind_WOAGd547pnWHxsHquj2yfAOWyghqT9Hx"],
                "estimated_time": "2-5分钟",
                "why_needed": "令牌是安全验证的重要凭证"
            },
            "permission_denied": {
                "severity": ErrorSeverity.ERROR,
                "category": ErrorCategory.AUTHORIZATION,
                "title": "🚫 权限不足",
                "description": "您没有执行此操作的权限",
                "solutions": [
                    "检查您的账户权限状态",
                    "联系管理员申请相应权限",
                    "确认您已完成身份验证"
                ],
                "examples": ["/status"],
                "estimated_time": "5-15分钟",
                "why_needed": "不同操作需要不同级别的权限"
            },
            "rate_limit_exceeded": {
                "severity": ErrorSeverity.WARNING,
                "category": ErrorCategory.SYSTEM,
                "title": "⏱️ 操作频率限制",
                "description": "您的操作过于频繁，请稍后再试",
                "solutions": [
                    "等待1-2分钟后重试",
                    "避免短时间内重复操作",
                    "如有紧急需求，联系管理员"
                ],
                "examples": [],
                "estimated_time": "1-2分钟",
                "why_needed": "防止系统过载，保证服务稳定"
            },
            "verification_failed": {
                "severity": ErrorSeverity.ERROR,
                "category": ErrorCategory.AUTHENTICATION,
                "title": "❌ 验证申请失败",
                "description": "身份验证申请过程中发生错误",
                "solutions": [
                    "检查网络连接是否正常",
                    "稍后重试验证申请",
                    "联系管理员获取技术支持"
                ],
                "examples": ["/verify"],
                "estimated_time": "2-5分钟",
                "why_needed": "系统需要处理验证申请并生成安全令牌"
            },
            "verification_token_expired": {
                "severity": ErrorSeverity.WARNING,
                "category": ErrorCategory.AUTHENTICATION,
                "title": "⏰ 验证令牌已过期",
                "description": "您的验证令牌已过期，需要重新申请",
                "solutions": [
                    "输入 `/verify` 重新申请验证",
                    "及时联系管理员完成审核",
                    "注意令牌有效期限制"
                ],
                "examples": ["/verify"],
                "estimated_time": "5-30分钟",
                "why_needed": "令牌过期是为了保证安全性"
            },
            "verification_incorrect_usage": {
                "severity": ErrorSeverity.INFO,
                "category": ErrorCategory.USER_INPUT,
                "title": "💡 验证命令使用说明",
                "description": "验证命令的使用方式不正确",
                "solutions": [
                    "输入 `/verify`（不带参数）申请验证",
                    "将系统生成的验证令牌发送给管理员",
                    "等待管理员在后台完成审核"
                ],
                "examples": ["/verify"],
                "estimated_time": "1分钟",
                "why_needed": "正确使用命令才能完成验证流程"
            },
            "system_error": {
                "severity": ErrorSeverity.CRITICAL,
                "category": ErrorCategory.SYSTEM,
                "title": "⚠️ 系统错误",
                "description": "系统遇到内部错误，我们正在处理",
                "solutions": [
                    "稍后重试操作",
                    "如问题持续，联系技术支持",
                    "提供错误发生的具体时间"
                ],
                "examples": [],
                "estimated_time": "5-30分钟",
                "why_needed": "系统维护和故障恢复需要时间"
            }
        }
    
    def format_error_message(
        self, 
        error_type: str, 
        context: Optional[Dict] = None,
        user_name: str = "朋友"
    ) -> Tuple[str, Optional[InlineKeyboardMarkup]]:
        """格式化错误消息"""
        
        template = self.error_templates.get(error_type)
        if not template:
            return self._get_generic_error_message(error_type, user_name)
        
        # 构建错误消息
        message = f"""{template['title']} - {user_name}

{template['description']}

📋 **解决方案**：
"""
        
        # 添加解决步骤
        for i, solution in enumerate(template['solutions'], 1):
            message += f"{i}. {solution}\n"
        
        # 添加示例（如果有）
        if template['examples']:
            message += f"\n📝 **命令示例**：\n"
            for example in template['examples']:
                message += f"`{example}`\n"
        
        # 添加预期时间
        if template['estimated_time']:
            message += f"\n⏱️ **预计解决时间**：{template['estimated_time']}"
        
        # 添加说明（如果有）
        if template['why_needed']:
            message += f"\n\n💡 **为什么需要这样做？**\n{template['why_needed']}"
        
        # 添加联系信息
        message += f"\n\n📞 **需要帮助？** 输入 `/help` 或联系管理员"
        
        # 创建快捷操作按钮
        keyboard = self._create_error_keyboard(error_type, template)
        
        return message, keyboard
    
    def _create_error_keyboard(
        self, 
        error_type: str, 
        template: Dict
    ) -> Optional[InlineKeyboardMarkup]:
        """创建错误处理的快捷按钮"""
        
        buttons = []
        
        if error_type == "user_not_verified":
            buttons = [
                [InlineKeyboardButton("🔐 开始验证", callback_data="start_verify")],
                [InlineKeyboardButton("📊 查看状态", callback_data="check_status")],
                [InlineKeyboardButton("📞 联系管理员", callback_data="contact_admin")]
            ]
        elif error_type == "group_not_bound":
            buttons = [
                [InlineKeyboardButton("📊 查看状态", callback_data="check_status")],
                [InlineKeyboardButton("📞 联系管理员", callback_data="contact_admin")],
                [InlineKeyboardButton("❓ 绑定帮助", callback_data="binding_help")]
            ]
        elif error_type in ["invalid_token", "permission_denied"]:
            buttons = [
                [InlineKeyboardButton("📊 查看状态", callback_data="check_status")],
                [InlineKeyboardButton("📞 联系管理员", callback_data="contact_admin")],
                [InlineKeyboardButton("❓ 查看帮助", callback_data="show_help")]
            ]
        elif error_type == "rate_limit_exceeded":
            buttons = [
                [InlineKeyboardButton("⏱️ 1分钟后提醒我", callback_data="remind_later")],
                [InlineKeyboardButton("📊 查看状态", callback_data="check_status")]
            ]
        else:
            buttons = [
                [InlineKeyboardButton("🔄 重试", callback_data="retry_operation")],
                [InlineKeyboardButton("📞 联系技术支持", callback_data="contact_support")],
                [InlineKeyboardButton("❓ 查看帮助", callback_data="show_help")]
            ]
        
        return InlineKeyboardMarkup(buttons) if buttons else None
    
    def _get_generic_error_message(
        self, 
        error_type: str, 
        user_name: str
    ) -> Tuple[str, Optional[InlineKeyboardMarkup]]:
        """获取通用错误消息"""
        
        message = f"""❌ **遇到问题了 - {user_name}**

系统遇到了一个问题：{error_type}

🔧 **建议操作**：
1. 输入 `/status` 查看当前状态
2. 输入 `/help` 获取详细帮助
3. 如问题持续，请联系管理员

💡 **常见解决方案**：
• 身份验证问题 → 输入 `/verify` 开始验证
• 群组绑定问题 → 联系管理员获取绑定令牌
• 权限问题 → 检查账户状态

📞 **需要人工协助？** 联系管理员获取支持"""

        keyboard = InlineKeyboardMarkup([
            [InlineKeyboardButton("📊 查看状态", callback_data="check_status")],
            [InlineKeyboardButton("📞 联系管理员", callback_data="contact_admin")],
            [InlineKeyboardButton("❓ 查看帮助", callback_data="show_help")]
        ])
        
        return message, keyboard
    
    def get_recovery_suggestions(self, error_type: str) -> List[str]:
        """获取错误恢复建议"""
        
        template = self.error_templates.get(error_type)
        if template:
            return template['solutions']
        
        return [
            "检查输入是否正确",
            "稍后重试操作",
            "联系管理员获取帮助"
        ]
    
    def is_recoverable_error(self, error_type: str) -> bool:
        """判断错误是否可恢复"""
        
        non_recoverable = ["system_error", "critical_failure"]
        return error_type not in non_recoverable
