# 🔧 GO 版本沃尔玛 API 测试程序 - 仅测试余额查询

## 📋 测试目的

验证修复后的 GO 版本沃尔玛 API 客户端的余额查询功能，特别是验证 Cookie jar 会话状态管理是否正常工作。

## 🎯 测试内容

### 测试 1：余额查询（核心测试）

- **核心验证**：直接查询指定卡号的余额
- **验证目标**：Cookie jar 是否正确处理会话状态
- **预期结果**：能够正常获取余额，不出现"请先去登录"错误

### 测试 2：用户信息查询

- 验证用户信息 API 是否正常工作
- 检查返回的用户数据

## 🛠️ 技术修复

**修复前问题**：

- GO 版本使用标准`http.Client`，每次请求独立
- 绑卡成功后的 Cookie/会话信息没有传递给余额查询
- 导致余额查询被服务器认为是"未登录"状态

**修复方案**：

```go
// 🔧 创建Cookie jar来自动处理会话状态
jar, err := cookiejar.New(nil)
httpClient: &http.Client{
    Jar: jar, // 🔧 启用Cookie自动管理
}
```

**预期效果**：

- ✅ 绑卡成功后，服务器设置的 Cookie 会自动保存
- ✅ 余额查询时，Cookie 会自动发送，维持会话状态
- ✅ 与 Python 版本的`curl_cffi.requests`行为一致

## 🚀 使用方法

### 编译（已完成）

```bash
go build -o test_balance_only.exe test_balance_only.go
```

### 运行测试

```bash
./test_balance_only.exe
```

## 📊 测试结果解读

### ✅ 成功情况

```
✅ 余额查询测试通过！
✅ 余额查询成功 - 会话状态管理正常工作
🎉 Cookie jar修复生效，GO版本现在与Python版本行为一致！
```

### ❌ 失败情况

```
❌ 余额查询失败
🚨 仍然出现'请先去登录'错误，会话状态管理修复可能不完整
🔧 可能需要进一步调试会话状态管理
```

## 🔍 调试信息

测试程序会输出详细的调试信息：

- API 调用的成功/失败状态
- 返回的错误消息
- 原始 API 响应数据（JSON 格式）
- 具体的余额、卡片信息等

## 📝 测试参数

使用与 Python 测试文件相同的参数：

- **卡号**：`2326992090701193061`
- **API 配置**：与 Python 版本完全一致

## 🎯 关键验证点

1. **余额查询成功**：验证 Cookie jar 会话管理修复
2. **不再出现"请先去登录"**：确认问题已解决
3. **与 Python 版本行为一致**：确保完全兼容
4. **用户信息查询正常**：验证整体 API 客户端功能

如果测试通过，说明会话状态管理修复成功，可以部署到生产环境！
