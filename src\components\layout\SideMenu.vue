<template>
  <div class="side-menu">
    <el-menu :default-active="activeMenu" :collapse="collapse" :unique-opened="false" background-color="#304156"
      text-color="#bfcbd9" active-text-color="#ffffff" router class="el-menu-vertical">
      <!-- 递归渲染菜单项 -->
      <menu-item v-for="menu in menuList" :key="menu.code" :menu="menu" :collapse="collapse" />
    </el-menu>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue'
import { useRoute } from 'vue-router'
import { useSystemStore } from '@/store/modules/system'
import { ElMessage } from 'element-plus'
import MenuItem from './MenuItem.vue'

const props = defineProps({
  collapse: {
    type: Boolean,
    default: false
  }
})

const route = useRoute()
const systemStore = useSystemStore()

// 菜单数据
const menuList = ref([])
const loading = ref(false)

// 当前激活的菜单
const activeMenu = computed(() => {
  return route.path
})

// 获取用户菜单
const fetchUserMenus = async () => {
  if (loading.value) return

  loading.value = true
  try {
    const menus = await systemStore.fetchUserMenus()

    // 直接使用后端返回的菜单树
    menuList.value = menus || []

  } catch (error) {
    console.error('获取用户菜单失败:', error)
    ElMessage.error('获取菜单失败，请刷新页面重试')
    menuList.value = []
  } finally {
    loading.value = false
  }
}

// 不再需要构建菜单树结构，直接使用后端返回的菜单树

// 组件挂载时获取菜单
onMounted(() => {
  fetchUserMenus()
})

// 监听路由变化，确保菜单状态正确
watch(() => route.path, () => {
  // 路由变化时可以在这里做一些处理
}, { immediate: true })
</script>

<style lang="scss" scoped>
.side-menu {
  height: 100%;

  .el-menu-vertical {
    border-right: none;
    height: 100%;
    width: 100%;

    &:not(.el-menu--collapse) {
      width: 220px;
    }
  }
}

// 菜单项样式
:deep(.el-menu-item) {
  &:hover {
    background-color: #263445 !important;
  }

  &.is-active {
    background-color: #409EFF !important;
    color: #ffffff !important;
  }
}

:deep(.el-sub-menu__title) {
  &:hover {
    background-color: #263445 !important;
  }
}

:deep(.el-sub-menu .el-menu-item) {
  background-color: #1f2d3d !important;

  &:hover {
    background-color: #001528 !important;
  }

  &.is-active {
    background-color: #409EFF !important;
    color: #ffffff !important;
  }
}
</style>
