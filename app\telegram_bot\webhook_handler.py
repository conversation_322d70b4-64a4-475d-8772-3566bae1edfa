"""
Webhook 处理器
"""

import hmac
import hashlib
from typing import Dict, Any, Optional
from telegram.ext import Application

from app.core.logging import get_logger
from .config import BotConfig
from .exceptions import WebhookError

logger = get_logger(__name__)


class WebhookHandler:
    """Webhook处理器"""
    
    def __init__(self, application: Application, config: BotConfig):
        self.application = application
        self.config = config
        
    async def setup_webhook(self) -> bool:
        """设置Webhook"""
        try:
            if not self.config.webhook_url:
                logger.warning("Webhook URL 未配置，跳过 Webhook 设置")
                return True  # 返回 True 表示跳过设置，不是错误
            
            # 设置Webhook
            await self.application.bot.set_webhook(
                url=self.config.webhook_url,
                secret_token=self.config.webhook_secret if self.config.webhook_secret else None,
                max_connections=100,
                allowed_updates=[
                    "message",
                    "edited_message", 
                    "channel_post",
                    "edited_channel_post",
                    "callback_query",
                    "my_chat_member",
                    "chat_member"
                ]
            )
            
            logger.info(f"Webhook 设置成功: {self.config.webhook_url}")
            return True
            
        except Exception as e:
            logger.error(f"设置 Webhook 失败: {e}")
            return False
    
    async def remove_webhook(self) -> bool:
        """删除Webhook"""
        try:
            await self.application.bot.delete_webhook()
            logger.info("Webhook 删除成功")
            return True
            
        except Exception as e:
            logger.error(f"删除 Webhook 失败: {e}")
            return False
    
    def verify_webhook_signature(self, body: bytes, signature: str) -> bool:
        """验证Webhook签名"""
        if not self.config.webhook_secret:
            # 如果没有配置密钥，跳过验证
            return True
        
        try:
            # 计算期望的签名
            expected_signature = hmac.new(
                self.config.webhook_secret.encode(),
                body,
                hashlib.sha256
            ).hexdigest()
            
            # 比较签名
            return hmac.compare_digest(f"sha256={expected_signature}", signature)
            
        except Exception as e:
            logger.error(f"验证Webhook签名失败: {e}")
            return False
    
    async def process_webhook(self, update_data: Dict[str, Any], signature: Optional[str] = None) -> bool:
        """处理Webhook请求"""
        try:
            # 验证签名（如果提供）
            if signature:
                import json
                body = json.dumps(update_data, separators=(',', ':')).encode()
                if not self.verify_webhook_signature(body, signature):
                    raise WebhookError("Webhook签名验证失败")

            # 预过滤更新类型，只处理我们关心的更新
            if not self._should_process_update(update_data):
                logger.debug(f"跳过不需要处理的更新类型: {list(update_data.keys())}")
                return True  # 返回True表示成功处理（虽然是跳过）

            # 处理更新
            from telegram import Update
            update = Update.de_json(update_data, self.application.bot)
            if not update:
                logger.warning("无效的更新数据")
                return False

            # 异步处理更新
            await self.application.process_update(update)
            return True

        except Exception as e:
            logger.error(f"处理Webhook失败: {e}")
            return False

    def _should_process_update(self, update_data: Dict[str, Any]) -> bool:
        """判断是否应该处理这个更新

        只处理以下类型的更新：
        1. message - 普通消息（包括命令）
        2. callback_query - 按钮回调
        3. my_chat_member - 机器人被添加/移除群组
        4. chat_member - 群组成员变化（用于权限检查）
        """
        # 定义我们关心的更新类型
        relevant_update_types = {
            'message',
            'callback_query',
            'my_chat_member',
            'chat_member'
        }

        # 检查更新数据中是否包含我们关心的类型
        update_keys = set(update_data.keys()) - {'update_id'}  # 排除update_id

        # 如果包含我们关心的更新类型，则处理
        return bool(relevant_update_types.intersection(update_keys))
    
    async def get_webhook_info(self) -> Optional[Dict[str, Any]]:
        """获取Webhook信息"""
        try:
            webhook_info = await self.application.bot.get_webhook_info()
            return {
                "url": webhook_info.url,
                "has_custom_certificate": webhook_info.has_custom_certificate,
                "pending_update_count": webhook_info.pending_update_count,
                "last_error_date": webhook_info.last_error_date,
                "last_error_message": webhook_info.last_error_message,
                "max_connections": webhook_info.max_connections,
                "allowed_updates": webhook_info.allowed_updates
            }
            
        except Exception as e:
            logger.error(f"获取Webhook信息失败: {e}")
            return None
    
    def update_config(self, config: BotConfig):
        """更新配置"""
        self.config = config
        logger.info("Webhook处理器配置已更新")
    
    def validate_webhook_config(self) -> bool:
        """验证Webhook配置"""
        if not self.config.webhook_url:
            logger.error("Webhook URL 未配置")
            return False
        
        if not self.config.webhook_url.startswith("https://"):
            logger.error("Webhook URL 必须使用HTTPS")
            return False
        
        return True
