from typing import Optional
from pydantic import BaseModel, Field
from datetime import datetime


class SecurityAudit(BaseModel):
    """安全审计日志模型"""

    id: int = Field(..., description="审计ID")
    user_id: int = Field(..., description="用户ID")
    action: str = Field(..., description="操作类型")
    resource: str = Field(..., description="资源")
    resource_id: Optional[str] = Field(None, description="资源ID")
    ip_address: str = Field(..., description="IP地址")
    user_agent: Optional[str] = Field(None, description="用户代理")
    details: Optional[dict] = Field(None, description="详细信息")
    created_at: datetime = Field(..., description="创建时间")

    class Config:
        from_attributes = True


class SecurityAuditCreate(BaseModel):
    """创建安全审计日志时的模型"""

    user_id: int = Field(..., description="用户ID")
    action: str = Field(..., description="操作类型")
    resource: str = Field(..., description="资源")
    resource_id: Optional[str] = Field(None, description="资源ID")
    ip_address: str = Field(..., description="IP地址")
    user_agent: Optional[str] = Field(None, description="用户代理")
    details: Optional[dict] = Field(None, description="详细信息")

    class Config:
        from_attributes = True
