"""
Docker环境兼容性检查和配置
确保所有优化在容器环境中正常工作
"""
import os
import sys
import asyncio
from typing import Dict, Any, Optional
from pathlib import Path

from app.core.logging import get_logger
from app.core.config import settings

logger = get_logger("docker_compatibility")


class DockerCompatibilityChecker:
    """Docker环境兼容性检查器"""
    
    def __init__(self):
        self.is_docker = self._detect_docker_environment()
        self.is_compiled = self._detect_compiled_environment()
    
    def _detect_docker_environment(self) -> bool:
        """检测是否在Docker容器中运行"""
        # 检查常见的Docker环境标识
        docker_indicators = [
            os.path.exists('/.dockerenv'),
            os.path.exists('/proc/1/cgroup') and self._check_cgroup_docker(),
            os.environ.get('DOCKER_CONTAINER') == 'true',
            os.environ.get('container') is not None,
        ]
        
        return any(docker_indicators)
    
    def _check_cgroup_docker(self) -> bool:
        """检查cgroup是否包含docker标识"""
        try:
            with open('/proc/1/cgroup', 'r') as f:
                content = f.read()
                return 'docker' in content or 'containerd' in content
        except (FileNotFoundError, PermissionError):
            return False
    
    def _detect_compiled_environment(self) -> bool:
        """检测是否是编译后的可执行文件"""
        return getattr(sys, 'frozen', False) or hasattr(sys, '_MEIPASS')
    
    def get_environment_info(self) -> Dict[str, Any]:
        """获取环境信息"""
        return {
            "is_docker": self.is_docker,
            "is_compiled": self.is_compiled,
            "python_version": sys.version,
            "platform": sys.platform,
            "executable": sys.executable,
            "working_directory": os.getcwd(),
            "environment_variables": {
                "DOCKER_CONTAINER": os.environ.get('DOCKER_CONTAINER'),
                "container": os.environ.get('container'),
                "HOSTNAME": os.environ.get('HOSTNAME'),
                "PATH": os.environ.get('PATH', '')[:200] + "..." if len(os.environ.get('PATH', '')) > 200 else os.environ.get('PATH', ''),
            }
        }
    
    def validate_callback_service_compatibility(self) -> Dict[str, Any]:
        """验证回调服务在当前环境中的兼容性"""
        issues = []
        recommendations = []
        
        # 检查网络配置
        if self.is_docker:
            # Docker环境特定检查
            if not os.environ.get('RABBITMQ_HOST'):
                issues.append("Docker环境中未设置RABBITMQ_HOST环境变量")
                recommendations.append("设置RABBITMQ_HOST环境变量指向RabbitMQ服务")
            
            # 检查数据库连接配置
            if not os.environ.get('DB_HOST'):
                issues.append("Docker环境中未设置DB_HOST环境变量")
                recommendations.append("设置DB_HOST环境变量指向数据库服务")
        
        # 检查配置文件
        config_file = Path("config.yaml")
        if not config_file.exists():
            issues.append("配置文件config.yaml不存在")
            recommendations.append("确保config.yaml文件存在并包含正确的配置")
        
        # 检查日志目录
        logs_dir = Path("logs")
        if not logs_dir.exists():
            try:
                logs_dir.mkdir(parents=True, exist_ok=True)
                logger.info("创建日志目录: logs/")
            except Exception as e:
                issues.append(f"无法创建日志目录: {e}")
                recommendations.append("确保应用有权限创建日志目录")
        
        return {
            "compatible": len(issues) == 0,
            "issues": issues,
            "recommendations": recommendations,
            "environment": self.get_environment_info()
        }
    
    async def test_async_compatibility(self) -> Dict[str, Any]:
        """测试异步功能兼容性"""
        test_results = {}
        
        # 测试asyncio事件循环
        try:
            loop = asyncio.get_event_loop()
            test_results["event_loop"] = {
                "available": True,
                "running": loop.is_running(),
                "type": str(type(loop))
            }
        except Exception as e:
            test_results["event_loop"] = {
                "available": False,
                "error": str(e)
            }
        
        # 测试任务创建
        try:
            async def test_task():
                await asyncio.sleep(0.001)
                return "success"
            
            task = asyncio.create_task(test_task())
            result = await task
            test_results["task_creation"] = {
                "available": True,
                "result": result
            }
        except Exception as e:
            test_results["task_creation"] = {
                "available": False,
                "error": str(e)
            }
        
        # 测试HTTP客户端
        try:
            import httpx
            async with httpx.AsyncClient() as client:
                # 简单的连接测试（不实际发送请求）
                test_results["http_client"] = {
                    "available": True,
                    "client_type": str(type(client))
                }
        except Exception as e:
            test_results["http_client"] = {
                "available": False,
                "error": str(e)
            }
        
        return test_results
    
    def get_optimization_recommendations(self) -> Dict[str, Any]:
        """获取针对当前环境的优化建议"""
        recommendations = {
            "database": [],
            "rabbitmq": [],
            "http_client": [],
            "monitoring": []
        }
        
        if self.is_docker:
            # Docker环境优化建议
            recommendations["database"].extend([
                "使用连接池以减少数据库连接开销",
                "设置合适的连接超时时间",
                "考虑使用数据库连接代理"
            ])
            
            recommendations["rabbitmq"].extend([
                "增加消费者预取数量以提高吞吐量",
                "使用持久化队列确保消息不丢失",
                "配置合适的心跳间隔"
            ])
            
            recommendations["http_client"].extend([
                "使用连接池复用HTTP连接",
                "设置合适的超时时间",
                "启用HTTP/2支持（如果可用）"
            ])
            
            recommendations["monitoring"].extend([
                "启用详细的性能监控",
                "配置日志轮转避免磁盘空间问题",
                "使用健康检查端点"
            ])
        
        if self.is_compiled:
            # 编译环境特定建议
            recommendations["monitoring"].extend([
                "使用结构化日志格式",
                "避免动态导入模块",
                "预编译正则表达式"
            ])
        
        return recommendations


# 创建全局检查器实例
docker_compatibility_checker = DockerCompatibilityChecker()


async def validate_environment_on_startup():
    """在应用启动时验证环境兼容性"""
    logger.info("开始环境兼容性检查...")
    
    # 基本兼容性检查
    compatibility_result = docker_compatibility_checker.validate_callback_service_compatibility()
    
    if compatibility_result["compatible"]:
        logger.info("环境兼容性检查通过")
    else:
        logger.warning("发现环境兼容性问题:")
        for issue in compatibility_result["issues"]:
            logger.warning(f"  - {issue}")
        
        logger.info("建议:")
        for rec in compatibility_result["recommendations"]:
            logger.info(f"  - {rec}")
    
    # 异步功能测试
    async_test_result = await docker_compatibility_checker.test_async_compatibility()
    
    for test_name, result in async_test_result.items():
        if result.get("available"):
            logger.info(f"异步功能测试通过: {test_name}")
        else:
            logger.error(f"异步功能测试失败: {test_name} - {result.get('error')}")
    
    # 输出优化建议
    optimization_recs = docker_compatibility_checker.get_optimization_recommendations()
    logger.info("环境优化建议:")
    for category, recs in optimization_recs.items():
        if recs:
            logger.info(f"  {category}:")
            for rec in recs:
                logger.info(f"    - {rec}")
    
    return compatibility_result
