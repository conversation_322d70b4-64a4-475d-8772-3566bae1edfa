#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
高级微信小程序签名算法分析脚本
尝试更多可能的签名算法组合
"""

import json
import hmac
import hashlib
import base64
import urllib.parse
from collections import OrderedDict

# 抓包数据1: getuserinfo
test_case_1 = {
    "timestamp": "1751174204050",
    "nonce": "24d4d7ffa", 
    "expected_signature": "74B1193DDB5AE4AC76247A2F456CC13C411D936C48C284A5C3DE8FFFC9CDB119",
    "body": {
        "currentPage": 0,
        "pageSize": 0,
        "sign": "6b95be8d25574dad856cdf7939bfe469@61fa608e14c37c20fde47c700ec90414"
    },
    "headers": {
        "sv": "3",
        "version": "45"
    }
}

# 抓包数据2: bingcard
test_case_2 = {
    "timestamp": "1751174220771",
    "nonce": "1158f8500",
    "expected_signature": "107BAE44C39F6878E2CB2D7F832DE9BDFA77364208514F627473B8B920B3113A",
    "body": {
        "cardNo": "2326992090536890765",
        "cardPwd": "811911", 
        "currentPage": 0,
        "pageSize": 0,
        "sign": "6b95be8d25574dad856cdf7939bfe469@61fa608e14c37c20fde47c700ec90414",
        "storeId": "",
        "userPhone": ""
    },
    "headers": {
        "sv": "3",
        "version": "45"
    }
}

# 已知参数
walmart_sign = "6b95be8d25574dad856cdf7939bfe469@61fa608e14c37c20fde47c700ec90414"
wechat_key = "zB0ZnBVP+iBBQjV0zEi0yA=="
version = 45

def test_signature_algorithm(test_case, algorithm_name, sign_string, key):
    """测试签名算法"""
    try:
        # 计算HMAC-SHA256签名
        message = sign_string.encode("utf-8")
        signature = hmac.new(key, message, hashlib.sha256).hexdigest().upper()
        
        # 检查是否匹配
        is_match = signature == test_case["expected_signature"]
        
        if is_match:
            print(f"\n🎉 找到匹配的算法!")
            print(f"算法: {algorithm_name}")
            print(f"签名字符串: {sign_string}")
            print(f"密钥: {key.hex() if isinstance(key, bytes) else key}")
            print(f"计算签名: {signature}")
            print(f"期望签名: {test_case['expected_signature']}")
        
        return is_match
        
    except Exception as e:
        return False

def try_different_hash_algorithms(test_case, sign_string, key):
    """尝试不同的哈希算法"""
    algorithms = [
        ("MD5", hashlib.md5),
        ("SHA1", hashlib.sha1),
        ("SHA224", hashlib.sha224),
        ("SHA256", hashlib.sha256),
        ("SHA384", hashlib.sha384),
        ("SHA512", hashlib.sha512),
    ]
    
    for algo_name, algo_func in algorithms:
        try:
            # HMAC
            signature = hmac.new(key, sign_string.encode("utf-8"), algo_func).hexdigest().upper()
            if signature == test_case["expected_signature"]:
                print(f"\n🎉 找到匹配的算法: HMAC-{algo_name}")
                print(f"签名字符串: {sign_string}")
                print(f"密钥: {key.hex() if isinstance(key, bytes) else key}")
                return True
            
            # 直接哈希
            signature = algo_func((sign_string + key.decode('utf-8', errors='ignore')).encode("utf-8")).hexdigest().upper()
            if signature == test_case["expected_signature"]:
                print(f"\n🎉 找到匹配的算法: {algo_name} 直接哈希")
                print(f"签名字符串: {sign_string}")
                print(f"密钥: {key.hex() if isinstance(key, bytes) else key}")
                return True
                
        except Exception:
            continue
    
    return False

def analyze_advanced_signature(test_case, case_name):
    """高级签名分析"""
    print(f"\n{'='*60}")
    print(f"高级分析案例: {case_name}")
    print(f"时间戳: {test_case['timestamp']}")
    print(f"随机数: {test_case['nonce']}")
    print(f"期望签名: {test_case['expected_signature']}")
    print(f"{'='*60}")
    
    # 准备JSON数据
    sorted_body = OrderedDict(sorted(test_case["body"].items()))
    json_str = json.dumps(sorted_body, separators=(",", ":"))
    
    # 尝试不同的密钥
    keys_to_test = [
        ("wechat_key_base64解码", base64.b64decode(wechat_key)),
        ("wechat_key原始", wechat_key.encode("utf-8")),
        ("walmart_sign@前部分", walmart_sign.split("@")[0].encode("utf-8")),
        ("walmart_sign@后部分", walmart_sign.split("@")[1].encode("utf-8")),
    ]
    
    # 尝试更多签名字符串组合
    sign_patterns = [
        # 基础组合
        f"{test_case['timestamp']}{test_case['nonce']}{json_str}",
        f"{test_case['nonce']}{test_case['timestamp']}{json_str}",
        
        # 包含头部信息
        f"3{test_case['timestamp']}{test_case['nonce']}{json_str}",
        f"{test_case['timestamp']}{test_case['nonce']}3{json_str}",
        f"45{test_case['timestamp']}{test_case['nonce']}{json_str}",
        f"{test_case['timestamp']}{test_case['nonce']}45{json_str}",
        
        # URL编码的JSON
        f"{test_case['timestamp']}{test_case['nonce']}{urllib.parse.quote(json_str)}",
        
        # 不同的分隔符
        f"{test_case['timestamp']}#{test_case['nonce']}#{json_str}",
        f"{test_case['timestamp']}:{test_case['nonce']}:{json_str}",
        f"{test_case['timestamp']};{test_case['nonce']};{json_str}",
        
        # 只使用部分数据
        f"{test_case['timestamp']}{test_case['nonce']}",
        f"{test_case['nonce']}{test_case['timestamp']}",
        json_str,
        
        # 包含其他可能的参数
        f"1{test_case['timestamp']}{test_case['nonce']}{json_str}",  # xweb_xhr
        f"{test_case['timestamp']}{test_case['nonce']}{json_str}1",
        
        # 尝试不同的JSON格式
        json.dumps(sorted_body, separators=(", ", ": ")),  # 有空格
        json.dumps(sorted_body, sort_keys=False, separators=(",", ":")),  # 不排序
        
        # 尝试原始body字符串（可能不是JSON）
        str(test_case["body"]),
        
        # 尝试查询字符串格式
        "&".join([f"{k}={v}" for k, v in sorted_body.items()]),
        
        # 尝试包含walmart_sign
        f"{test_case['timestamp']}{test_case['nonce']}{walmart_sign}",
        f"{walmart_sign}{test_case['timestamp']}{test_case['nonce']}",
    ]
    
    print(f"尝试 {len(sign_patterns)} 种签名字符串格式...")
    
    for key_name, key in keys_to_test:
        print(f"\n测试密钥: {key_name}")
        
        for i, sign_string in enumerate(sign_patterns):
            # 尝试不同的哈希算法
            if try_different_hash_algorithms(test_case, sign_string, key):
                return True
            
            # 显示进度
            if (i + 1) % 5 == 0:
                print(f"  已测试 {i + 1}/{len(sign_patterns)} 种格式...")
    
    # 尝试一些特殊的组合
    print(f"\n尝试特殊组合...")
    
    # 可能的特殊密钥
    special_keys = [
        b"",  # 空密钥
        b"wx81d3e1fe4c2e11b4",  # 微信小程序ID
        b"walmart",
        b"signature",
        b"secret",
        walmart_sign.encode("utf-8"),
        (walmart_sign + wechat_key).encode("utf-8"),
        (wechat_key + walmart_sign).encode("utf-8"),
    ]
    
    for key in special_keys:
        for sign_string in sign_patterns[:5]:  # 只测试前5种格式
            if try_different_hash_algorithms(test_case, sign_string, key):
                return True
    
    return False

def main():
    """主函数"""
    print("高级微信小程序签名算法分析")
    print(f"沃尔玛签名: {walmart_sign}")
    print(f"微信密钥: {wechat_key}")
    print(f"版本号: {version}")
    
    # 分析两个测试案例
    result1 = analyze_advanced_signature(test_case_1, "getUserInfo接口")
    if not result1:
        result2 = analyze_advanced_signature(test_case_2, "bindCard接口")
    else:
        result2 = True  # 如果第一个成功了，假设第二个也会成功
    
    print(f"\n{'='*60}")
    print("分析总结:")
    print(f"案例1 (getUserInfo): {'✓ 成功' if result1 else '✗ 失败'}")
    print(f"案例2 (bindCard): {'✓ 成功' if result2 else '✗ 失败'}")
    
    if result1 or result2:
        print("🎉 找到了可能的签名算法!")
    else:
        print("❌ 仍未找到匹配的签名算法")
        print("可能需要:")
        print("1. 检查是否有其他隐藏参数")
        print("2. 确认签名算法是否使用了WebAssembly")
        print("3. 分析微信小程序的具体实现")

if __name__ == "__main__":
    main()
