"""
Telegram权限验证服务
"""

from typing import Optional, List, Dict, Any
from sqlalchemy.orm import Session

from app.models.telegram_group import TelegramGroup, BindStatus
from app.models.telegram_user import TelegramUser, VerificationStatus
from app.models.user import User
from app.services.telegram_config_service import TelegramConfigService
from app.core.logging import get_logger
from ..exceptions import (
    PermissionError,
    GroupNotBoundError,
    UserNotVerifiedError,
    AuthenticationError,
    RateLimitError
)

logger = get_logger(__name__)


class TelegramPermissionService:
    """Telegram权限验证服务"""

    def __init__(self, db_session: Session):
        self.db = db_session
        self.logger = logger
        self.config_service = TelegramConfigService(db_session)
        self._rate_limit_cache = {}  # 简单的内存缓存，实际应用中应使用Redis
    
    async def verify_group_permissions(self, chat_id: int) -> TelegramGroup:
        """
        验证群组级权限
        
        Args:
            chat_id: Telegram群组ID
            
        Returns:
            TelegramGroup: 群组对象
            
        Raises:
            GroupNotBoundError: 群组未绑定或状态异常
        """
        group = self.db.query(TelegramGroup).filter_by(chat_id=chat_id).first()
        
        if not group:
            raise GroupNotBoundError("群组未绑定，请先使用 /bind 命令绑定群组")
        
        if group.bind_status != BindStatus.ACTIVE:
            status_messages = {
                BindStatus.PENDING: "群组绑定待确认",
                BindStatus.SUSPENDED: "群组已被暂停使用"
            }
            message = status_messages.get(group.bind_status, "群组状态异常")
            raise GroupNotBoundError(f"{message}，请联系管理员")
        
        # 更新最后活跃时间
        group.update_last_active()
        self.db.commit()
        
        return group
    
    async def verify_user_permissions(self, user_id: int) -> TelegramUser:
        """
        验证用户级权限
        
        Args:
            user_id: Telegram用户ID
            
        Returns:
            TelegramUser: Telegram用户对象
            
        Raises:
            UserNotVerifiedError: 用户未验证
            AuthenticationError: 认证失败
        """
        telegram_user = self.db.query(TelegramUser).filter_by(
            telegram_user_id=user_id
        ).first()
        
        if not telegram_user:
            raise UserNotVerifiedError(
                "用户未验证，请先使用 /verify <验证码> 完成身份验证"
            )
        
        if telegram_user.verification_status != VerificationStatus.VERIFIED.value:
            status_messages = {
                VerificationStatus.PENDING.value: "用户验证待确认",
                VerificationStatus.EXPIRED.value: "用户验证已过期"
            }
            message = status_messages.get(
                telegram_user.verification_status,
                "用户验证状态异常"
            )
            raise UserNotVerifiedError(f"{message}，请重新验证")
        
        if not telegram_user.system_user_id:
            raise AuthenticationError("用户未关联系统账户")
        
        # 更新最后活跃时间
        telegram_user.update_last_active()
        self.db.commit()
        
        return telegram_user
    
    async def verify_data_permissions(
        self, 
        telegram_user: TelegramUser, 
        group: TelegramGroup
    ) -> bool:
        """
        验证数据级权限
        
        Args:
            telegram_user: Telegram用户对象
            group: 群组对象
            
        Returns:
            bool: 是否有权限
            
        Raises:
            PermissionError: 权限不足
        """
        if not telegram_user.system_user:
            raise PermissionError("用户系统账户不存在")
        
        system_user = telegram_user.system_user
        
        # 超级管理员拥有所有权限
        if system_user.is_superuser:
            return True
        
        # 检查商户权限
        if not system_user.can_access_merchant_data(group.merchant_id):
            raise PermissionError("您没有权限访问该商户的数据")
        
        # 检查部门权限（如果群组绑定了部门）
        if group.department_id:
            if not system_user.can_access_department_data_new(
                group.department_id, 
                group.merchant_id
            ):
                raise PermissionError("您没有权限访问该部门的数据")
        
        return True
    
    async def verify_command_permissions(
        self, 
        user_id: int, 
        chat_id: int, 
        command: str
    ) -> tuple[TelegramUser, TelegramGroup]:
        """
        验证命令执行权限
        
        Args:
            user_id: Telegram用户ID
            chat_id: 群组ID
            command: 命令名称
            
        Returns:
            tuple: (TelegramUser, TelegramGroup)
            
        Raises:
            各种权限异常
        """
        # 验证群组权限
        group = await self.verify_group_permissions(chat_id)
        
        # 对于某些命令，不需要用户验证
        public_commands = ["/help", "/start"]
        if command in public_commands:
            return None, group
        
        # 验证用户权限
        telegram_user = await self.verify_user_permissions(user_id)
        
        # 验证数据权限
        await self.verify_data_permissions(telegram_user, group)
        
        # 验证特定命令权限
        await self._verify_specific_command_permissions(
            telegram_user, group, command
        )
        
        return telegram_user, group
    
    async def _verify_specific_command_permissions(
        self,
        telegram_user: TelegramUser,
        group: TelegramGroup,
        command: str
    ):
        """验证特定命令权限（基于动态配置）"""
        system_user = telegram_user.system_user

        # 获取有效的群组配置
        effective_settings = self.config_service.get_effective_group_settings(group)
        permissions = effective_settings.get('permissions', {})

        # 检查频率限制
        await self._check_rate_limit(telegram_user.telegram_user_id, command, permissions)

        # 检查管理员命令权限
        admin_commands = permissions.get('admin_only_commands', ['/bind', '/unbind', '/settings'])
        if command in admin_commands:
            if not system_user.has_permission("api:telegram:bind"):
                raise PermissionError("您没有权限执行管理员命令")

        # 检查查询权限
        query_permissions = permissions.get('query_permissions', {})
        if command == "/stats" and not query_permissions.get('daily_stats', True):
            raise PermissionError("日统计查询已被禁用")
        elif command == "/stats_week" and not query_permissions.get('weekly_stats', True):
            raise PermissionError("周统计查询已被禁用")
        elif command == "/stats_month" and not query_permissions.get('monthly_stats', True):
            raise PermissionError("月统计查询已被禁用")
        elif command == "/stats_custom" and not query_permissions.get('custom_range', False):
            raise PermissionError("自定义时间范围查询已被禁用")

        # 检查详细数据权限
        if command.endswith("_detail") and not query_permissions.get('detailed_data', False):
            raise PermissionError("详细数据查询已被禁用")

        # 统计查询权限检查
        stats_commands = ["/stats", "/stats_week", "/stats_month", "/stats_custom"]
        if command in stats_commands:
            if not system_user.has_permission("api:telegram:stats"):
                raise PermissionError("您没有权限查询统计数据")

    async def _check_rate_limit(self, user_id: int, command: str, permissions: dict):
        """检查频率限制"""
        import time

        rate_limit = permissions.get('rate_limit', {})
        commands_per_minute = rate_limit.get('commands_per_minute', 10)
        queries_per_hour = rate_limit.get('queries_per_hour', 100)

        current_time = time.time()
        user_key = f"user_{user_id}"

        # 初始化用户缓存
        if user_key not in self._rate_limit_cache:
            self._rate_limit_cache[user_key] = {
                'commands': [],
                'queries': []
            }

        user_cache = self._rate_limit_cache[user_key]

        # 清理过期记录
        minute_ago = current_time - 60
        hour_ago = current_time - 3600
        user_cache['commands'] = [t for t in user_cache['commands'] if t > minute_ago]
        user_cache['queries'] = [t for t in user_cache['queries'] if t > hour_ago]

        # 检查命令频率限制
        if len(user_cache['commands']) >= commands_per_minute:
            raise RateLimitError(f"命令频率过高，每分钟最多 {commands_per_minute} 次")

        # 检查查询频率限制（针对统计命令）
        stats_commands = ["/stats", "/stats_week", "/stats_month", "/stats_custom"]
        if command in stats_commands:
            if len(user_cache['queries']) >= queries_per_hour:
                raise RateLimitError(f"查询频率过高，每小时最多 {queries_per_hour} 次")
            user_cache['queries'].append(current_time)

        # 记录命令
        user_cache['commands'].append(current_time)

    async def check_user_permissions_with_config(
        self,
        user_id: int,
        chat_id: int,
        command: str = None
    ) -> Dict[str, Any]:
        """
        基于配置检查用户权限（增强版）

        Args:
            user_id: Telegram用户ID
            chat_id: 群组ID
            command: 命令名称（可选）

        Returns:
            Dict: 详细的权限检查结果
        """
        result = {
            "has_access": False,
            "group_bound": False,
            "user_verified": False,
            "data_permission": False,
            "command_permission": False,
            "rate_limit_ok": False,
            "effective_settings": None,
            "error_message": None,
            "permission_details": {}
        }

        try:
            # 检查群组绑定
            group = await self.verify_group_permissions(chat_id)
            result["group_bound"] = True

            # 获取有效配置
            effective_settings = self.config_service.get_effective_group_settings(group)
            result["effective_settings"] = effective_settings
            permissions = effective_settings.get('permissions', {})

            # 检查是否允许所有成员
            if permissions.get('allow_all_members', False):
                result["user_verified"] = True
                result["data_permission"] = True
                result["command_permission"] = True
                result["rate_limit_ok"] = True
                result["has_access"] = True
                result["permission_details"]["allow_all_members"] = True
                return result

            # 检查用户验证
            if permissions.get('require_user_verification', True):
                telegram_user = await self.verify_user_permissions(user_id)
                result["user_verified"] = True

                # 检查数据权限
                await self.verify_data_permissions(telegram_user, group)
                result["data_permission"] = True
            else:
                result["user_verified"] = True
                result["data_permission"] = True
                result["permission_details"]["skip_user_verification"] = True

            # 检查命令权限
            if command:
                try:
                    await self._verify_specific_command_permissions(telegram_user, group, command)
                    result["command_permission"] = True
                    result["rate_limit_ok"] = True
                except RateLimitError as e:
                    result["error_message"] = str(e)
                    result["rate_limit_ok"] = False
                    return result
                except PermissionError as e:
                    result["error_message"] = str(e)
                    result["command_permission"] = False
                    return result
            else:
                result["command_permission"] = True
                result["rate_limit_ok"] = True

            result["has_access"] = True

        except (GroupNotBoundError, UserNotVerifiedError, PermissionError) as e:
            result["error_message"] = str(e)
        except Exception as e:
            self.logger.error(f"权限检查失败: {e}")
            result["error_message"] = "权限检查失败"

        return result

    async def get_user_command_permissions(
        self,
        user_id: int,
        chat_id: int
    ) -> Dict[str, Any]:
        """
        获取用户在群组中的命令权限详情

        Args:
            user_id: Telegram用户ID
            chat_id: 群组ID

        Returns:
            Dict: 命令权限详情
        """
        try:
            group = await self.verify_group_permissions(chat_id)
            effective_settings = self.config_service.get_effective_group_settings(group)
            permissions = effective_settings.get('permissions', {})
            query_permissions = permissions.get('query_permissions', {})

            # 检查用户权限
            try:
                telegram_user = await self.verify_user_permissions(user_id)
                await self.verify_data_permissions(telegram_user, group)
                system_user = telegram_user.system_user

                is_admin = system_user.has_permission("api:telegram:bind")
                has_stats_permission = system_user.has_permission("api:telegram:stats")
            except:
                is_admin = False
                has_stats_permission = False

            return {
                "group_id": group.id,
                "chat_id": chat_id,
                "user_permissions": {
                    "is_admin": is_admin,
                    "has_stats_permission": has_stats_permission,
                    "allow_all_members": permissions.get('allow_all_members', False),
                    "require_verification": permissions.get('require_user_verification', True)
                },
                "command_permissions": {
                    "admin_commands": {
                        "allowed": is_admin,
                        "commands": permissions.get('admin_only_commands', ['/bind', '/unbind', '/settings'])
                    },
                    "query_commands": {
                        "daily_stats": query_permissions.get('daily_stats', True) and has_stats_permission,
                        "weekly_stats": query_permissions.get('weekly_stats', True) and has_stats_permission,
                        "monthly_stats": query_permissions.get('monthly_stats', True) and has_stats_permission,
                        "custom_range": query_permissions.get('custom_range', False) and has_stats_permission,
                        "detailed_data": query_permissions.get('detailed_data', False) and has_stats_permission
                    }
                },
                "rate_limits": permissions.get('rate_limit', {
                    'commands_per_minute': 10,
                    'queries_per_hour': 100
                }),
                "effective_settings": effective_settings
            }

        except Exception as e:
            self.logger.error(f"获取用户命令权限失败: {e}")
            return {
                "error": str(e),
                "user_permissions": {},
                "command_permissions": {},
                "rate_limits": {},
                "effective_settings": {}
            }

    async def validate_group_settings_change(
        self,
        group: TelegramGroup,
        new_settings: dict,
        user_id: int
    ) -> Dict[str, Any]:
        """
        验证群组设置变更的权限

        Args:
            group: 群组对象
            new_settings: 新的设置
            user_id: 操作用户ID

        Returns:
            Dict: 验证结果
        """
        try:
            # 检查用户权限
            telegram_user = self.db.query(TelegramUser).filter_by(
                telegram_user_id=user_id
            ).first()

            if not telegram_user or not telegram_user.system_user:
                return {
                    "allowed": False,
                    "reason": "用户未验证或未关联系统账户"
                }

            system_user = telegram_user.system_user

            # 检查基本权限
            if not system_user.has_permission("api:telegram:config:write"):
                return {
                    "allowed": False,
                    "reason": "用户没有配置修改权限"
                }

            # 检查商户权限
            if not system_user.can_access_merchant_data(group.merchant_id):
                return {
                    "allowed": False,
                    "reason": "用户没有权限访问该商户"
                }

            # 验证设置有效性
            is_valid, errors = self.config_service.validate_settings(new_settings)
            if not is_valid:
                return {
                    "allowed": False,
                    "reason": f"设置验证失败: {'; '.join(errors)}"
                }

            return {
                "allowed": True,
                "reason": "权限验证通过"
            }

        except Exception as e:
            self.logger.error(f"验证群组设置变更权限失败: {e}")
            return {
                "allowed": False,
                "reason": f"权限验证失败: {str(e)}"
            }

    async def check_user_group_access(
        self, 
        user_id: int, 
        chat_id: int
    ) -> Dict[str, Any]:
        """
        检查用户对群组的访问权限（不抛出异常）
        
        Args:
            user_id: Telegram用户ID
            chat_id: 群组ID
            
        Returns:
            Dict: 权限检查结果
        """
        result = {
            "has_access": False,
            "group_bound": False,
            "user_verified": False,
            "data_permission": False,
            "error_message": None
        }
        
        try:
            # 检查群组绑定
            group = await self.verify_group_permissions(chat_id)
            result["group_bound"] = True
            
            # 检查用户验证
            telegram_user = await self.verify_user_permissions(user_id)
            result["user_verified"] = True
            
            # 检查数据权限
            await self.verify_data_permissions(telegram_user, group)
            result["data_permission"] = True
            result["has_access"] = True
            
        except (GroupNotBoundError, UserNotVerifiedError, PermissionError) as e:
            result["error_message"] = str(e)
        except Exception as e:
            self.logger.error(f"权限检查失败: {e}")
            result["error_message"] = "权限检查失败"
        
        return result
    
    async def get_user_accessible_groups(self, user_id: int) -> List[TelegramGroup]:
        """
        获取用户可访问的群组列表
        
        Args:
            user_id: Telegram用户ID
            
        Returns:
            List[TelegramGroup]: 可访问的群组列表
        """
        try:
            telegram_user = await self.verify_user_permissions(user_id)
            system_user = telegram_user.system_user
            
            if system_user.is_superuser:
                # 超级管理员可以访问所有群组
                return self.db.query(TelegramGroup).filter_by(
                    bind_status=BindStatus.ACTIVE
                ).all()
            
            # 获取用户可访问的商户
            accessible_merchants = system_user.get_accessible_merchant_ids()
            if not accessible_merchants:
                return []
            
            # 查询可访问的群组
            groups = self.db.query(TelegramGroup).filter(
                TelegramGroup.bind_status == BindStatus.ACTIVE,
                TelegramGroup.merchant_id.in_(accessible_merchants)
            ).all()
            
            # 进一步过滤部门权限
            accessible_groups = []
            for group in groups:
                if group.department_id:
                    if system_user.can_access_department_data_new(
                        group.department_id, 
                        group.merchant_id
                    ):
                        accessible_groups.append(group)
                else:
                    accessible_groups.append(group)
            
            return accessible_groups
            
        except Exception as e:
            self.logger.error(f"获取可访问群组失败: {e}")
            return []
    
    async def is_group_admin(self, user_id: int, chat_id: int) -> bool:
        """
        检查用户是否为群组管理员
        
        Args:
            user_id: Telegram用户ID
            chat_id: 群组ID
            
        Returns:
            bool: 是否为管理员
        """
        try:
            telegram_user = await self.verify_user_permissions(user_id)
            system_user = telegram_user.system_user
            
            # 超级管理员
            if system_user.is_superuser:
                return True
            
            # 检查是否有绑定权限
            return system_user.has_permission("api:telegram:bind")
            
        except Exception:
            return False
