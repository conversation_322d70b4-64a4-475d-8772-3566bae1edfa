// 服务器地址
export const SERVER_URL = window.APP_CONFIG?.API_BASE_URL;
// API 路径前缀
export const API_PREFIX = window.APP_CONFIG?.API_PATH;

// API 接口配置
export const API_URLS = {
  // 商家相关接口
  MERCHANT: {
    LIST: "/merchants",
    DETAIL: "/merchants/:id",
    CREATE: "/merchants",
    UPDATE: "/merchants/:id",
    DELETE: "/merchants/:id",
    TOGGLE_STATUS: "/merchants/:id/status",
    TOGGLE_ACTIVITY: "/merchants/:id/activity",
    RESET_API_KEY: "/merchants/:id/reset-api-key",
    STATS: "/merchants/:id/statistics",
    ACTIVITY: "/merchants/activity",
    USERS: "/merchants/:id/users",
    IP_WHITELIST: "/merchants/:id/ip-whitelist",
    API_USAGE: "/merchants/:id/api-usage",
    VERIFY_PASSWORD: "/merchants/:id/verify-password",
  },

  // 仪表盘相关接口
  DASHBOARD: {
    STATISTICS: "/dashboard/statistics",
    STATISTICS_LEGACY: "/dashboard/statistics-legacy",
    AMOUNT_STATISTICS: "/dashboard/amount-statistics",
    SUCCESS_RATE_STATISTICS: "/dashboard/success-rate-statistics",
    CK_EFFICIENCY_STATISTICS: "/dashboard/ck-efficiency-statistics",
    FAILURE_STATISTICS: "/dashboard/failure-statistics",
    DEPARTMENT_RANKING: "/dashboard/department-ranking",
    MERCHANT_ACTIVITY: "/dashboard/merchant-activity",
    BIND_TREND: "/dashboard/bind-trend/:period",
    SYSTEM_STATUS: "/dashboard/system-status",
    TODAY_STATS: "/dashboard/today-stats",
    HOURLY_DISTRIBUTION: "/dashboard/hourly-distribution",
    MERCHANT_RANKING: "/dashboard/merchant-ranking",
    TIME_DISTRIBUTION: "/dashboard/time-distribution",
    TREND: "/dashboard/trend",
    SUMMARY: "/dashboard/summary",
  },

  // 卡数据相关接口
  CARD_DATA: {
    LIST: "/cards",
    DETAIL: "/cards/:id",
    STATISTICS: "/cards/statistics",
    TODAY_STATISTICS: "/cards/today-statistics",
    HISTORY: "/cards/history",
    EXPORT: "/cards/export",
    BIND: "/card-bind",
    RETRY: "/cards/:id/retry",
    BATCH_RETRY: "/cards/batch-retry",
  },

  // 绑卡日志相关接口
  BINDING_LOGS: {
    BY_CARD_ID: "/binding-logs/:cardId",
    TIMELINE: "/binding-logs/:cardId/timeline",
    PERFORMANCE: "/binding-logs/:cardId/performance",
  },

  // 对账台相关接口
  RECONCILIATION: {
    DEPARTMENT_STATISTICS: "/reconciliation/departments/statistics",
    CK_STATISTICS: "/reconciliation/departments/:departmentId/ck-statistics",
    BINDING_RECORDS: "/reconciliation/ck/:ckId/records",
    EXPORT_DEPARTMENTS: "/reconciliation/export/departments",
    EXPORT_CK: "/reconciliation/export/ck/:departmentId",
    EXPORT_RECORDS: "/reconciliation/export/records/:ckId",
  },

  // 认证相关接口
  AUTH: {
    LOGIN: "/auth/login",
    LOGOUT: "/auth/logout",
    CHECK_TOTP: "/auth/check-totp",
    USER_INFO: "/users/me",
    PERMISSIONS: "/auth/permissions",
    REFRESH_TOKEN: "/auth/refresh-token",
  },

  // 用户管理相关接口（不包含认证功能，认证功能在AUTH中）
  USER: {
    LIST: "/users",
    DETAIL: "/users/:id",
    CREATE: "/users",
    UPDATE: "/users/:id",
    DELETE: "/users/:id",
    PROFILE: "/users/profile",
    BATCH_DELETE: "/users/batch",
    RESET_PASSWORD: "/users/:id/reset-password",
    UPDATE_PASSWORD: "/users/update-password",
    PERMISSIONS: "/users/permissions",
    STATUS: "/users/:id/status",
    ME: "/users/me", // 用于更新个人资料
    CHANGE_PASSWORD: "/users/change-password",
    SEARCH: "/users/search",
  },

  // 权限相关接口
  PERMISSION: {
    LIST: "/permissions/",
    DETAIL: "/permissions/:id",
    CREATE: "/permissions",
    UPDATE: "/permissions/:id",
    DELETE: "/permissions/:id",
  },

  // 角色相关接口
  ROLE: {
    LIST: "/roles",
    DETAIL: "/roles/:id",
    CREATE: "/roles",
    UPDATE: "/roles/:id",
    DELETE: "/roles/:id",
    PERMISSIONS: "/roles/:id/permissions",
    MENUS: "/roles/:id/menus",
    DATA_PERMISSIONS: "/roles/:id/data-permissions",
    ALL_PERMISSIONS: "/permissions",
    ALL_MENUS: "/menus",
    USERS: "/roles/:id/users",
    ADD_USERS: "/roles/:id/users",
    REMOVE_USER: "/roles/:id/users/:user_id",
  },

  // 菜单相关接口
  MENU: {
    LIST: "/menus/",
    DETAIL: "/menus/:id",
    CREATE: "/menus",
    UPDATE: "/menus/:id",
    DELETE: "/menus/:id",
    TREE: "/menus/tree",
    USER_MENUS: "/menus/user-menus", // 修复：使用专门的用户菜单接口
    ASSIGN_PERMISSIONS: "/menus/:id/permissions",
  },

  // 系统相关接口
  SYSTEM: {
    PUBLIC_PARAMS: "/system/public-params",
    SYSTEM_PARAMS: "/system/params",
    UPDATE_PARAMS: "/system/params",
    SYSTEM_STATUS: "/system/status",
    SYSTEM_LOGS: "/system/logs",
  },

  // 系统设置相关接口
  SYSTEM_SETTINGS: {
    LIST: "/system-settings",
    DETAIL: "/system-settings/:key",
    CREATE: "/system-settings",
    UPDATE: "/system-settings/:key",
    DELETE: "/system-settings/:key",
    CK_EXPIRE_CONFIG: "/system-settings/ck-expire/config",
  },

  // 通知相关接口
  NOTIFICATION: {
    LIST: "/notifications",
    DETAIL: "/notifications/:id",
    CREATE: "/notifications",
    UPDATE: "/notifications/:id",
    DELETE: "/notifications/:id",
    READ: "/notifications/:id/read",
    UNREAD: "/notifications/:id/unread",
    READ_ALL: "/notifications/read-all",
    UNREAD_COUNT: "/notifications/unread-count",
  },

  // 沃尔玛配置相关接口
  WALMART_CONFIG: {
    LIST: "/walmart-server",
    UPDATE: "/walmart-server",
  },

  // 沃尔玛CK配置相关接口
  walmart_ck: {
    LIST: "/walmart-ck",
    DETAIL: "/walmart-ck/:id",
    CREATE: "/walmart-ck",
    BATCH_CREATE: "/walmart-ck/batch",
    UPDATE: "/walmart-ck/:id",
    DELETE: "/walmart-ck/:id",
    BATCH_DELETE: "/walmart-ck/batch-delete",
    ENABLE: "/walmart-ck/:id/enable",
    DISABLE: "/walmart-ck/:id/disable",
    BATCH_ENABLE: "/walmart-ck/batch-enable",
    BATCH_DISABLE: "/walmart-ck/batch-disable",
    SYNC_REDIS: "/walmart-ck/sync-redis",
    STATISTICS: "/walmart-ck/statistics/:merchant_id",
    SINGLE_STATISTICS: "/walmart-ck/:id/statistics",
    DAILY_TREND: "/walmart-ck/:id/daily-trend",
    FAILURE_ANALYSIS: "/walmart-ck/:id/failure-analysis",
    BINDING_AMOUNT_STATISTICS: "/walmart-ck/binding-amount-statistics",
    EXPORT: "/walmart-ck/export",
  },

  // 通知配置相关接口
  NOTIFICATION_CONFIG: {
    GET: "/notification-configs",
    UPDATE: "/notification-configs",
    TEST: "/notification-configs/test",
  },

  // CK监控相关接口
  ck_monitor: {
    STATUS: "/ck-monitor/status",
    HEALTH: "/ck-monitor/health",
    TEST_LOAD_BALANCE: "/ck-monitor/test-load-balance",
    SYNC_REDIS: "/ck-monitor/sync-redis",
    STATISTICS: "/ck-monitor/statistics",
  },

  // 恢复处理相关接口
  RECOVERY: {
    STUCK_REQUESTS: "/recovery/stuck-requests",
  },

  // 部门管理相关接口（新的AO架构）
  DEPARTMENT: {
    LIST: "/departments",
    DETAIL: "/departments/:id",
    CREATE: "/departments",
    UPDATE: "/departments/:id",
    DELETE: "/departments/:id",
    TREE: "/departments",
    CHILDREN: "/departments/:id/children",
    MOVE: "/departments/:id/move",
    MY_DEPARTMENT: "/departments/my-department",
    // 绑卡控制相关接口
    BINDING_STATUS: "/departments/:id/binding-status",
    BINDING_CONTROLS: "/departments/:id/binding-controls",
    BATCH_BINDING_CONTROLS: "/departments/batch-binding-controls",
    BINDING_WEIGHT_STATS: "/departments/binding-weight-stats",
    TEST_WEIGHT_ALGORITHM: "/departments/test-weight-algorithm",
  },
};

// 替换 URL 中的参数
export const replaceUrlParams = (url, params = {}) => {
  let result = url;
  Object.keys(params).forEach((key) => {
    result = result.replace(`:${key}`, params[key]);
  });
  return result;
};
