"""
频率限制器
"""

import time
from typing import Dict, Optional
from collections import defaultdict, deque
from dataclasses import dataclass

from app.core.logging import get_logger
from .config import BotConfig
from .exceptions import RateLimitError

logger = get_logger(__name__)


@dataclass
class RateLimit:
    """频率限制配置"""
    limit: int  # 限制次数
    window: int  # 时间窗口（秒）


class RateLimiter:
    """频率限制器"""
    
    def __init__(self, config: BotConfig):
        self.config = config
        
        # 用户级别限制
        self.user_requests: Dict[int, deque] = defaultdict(deque)
        
        # 群组级别限制
        self.group_requests: Dict[int, deque] = defaultdict(deque)
        
        # 全局限制
        self.global_requests: deque = deque()
        
        # 绑定尝试限制
        self.bind_attempts: Dict[int, deque] = defaultdict(deque)
        
        # 配置限制规则
        self.limits = {
            "global": RateLimit(config.rate_limit_global, 3600),  # 每小时
            "group": RateLimit(config.rate_limit_group, 3600),    # 每小时
            "user": RateLimit(config.rate_limit_user, 3600),      # 每小时
            "bind": RateLimit(config.max_bind_attempts_per_day, 86400)  # 每天
        }
    
    def _clean_old_requests(self, request_queue: deque, window: int):
        """清理过期的请求记录"""
        current_time = time.time()
        while request_queue and current_time - request_queue[0] > window:
            request_queue.popleft()
    
    def check_global_limit(self) -> bool:
        """检查全局频率限制"""
        limit = self.limits["global"]
        self._clean_old_requests(self.global_requests, limit.window)
        
        if len(self.global_requests) >= limit.limit:
            logger.warning(f"全局频率限制触发: {len(self.global_requests)}/{limit.limit}")
            return False
        
        self.global_requests.append(time.time())
        return True
    
    def check_user_limit(self, user_id: int) -> bool:
        """检查用户频率限制"""
        limit = self.limits["user"]
        user_queue = self.user_requests[user_id]
        self._clean_old_requests(user_queue, limit.window)
        
        if len(user_queue) >= limit.limit:
            logger.warning(f"用户 {user_id} 频率限制触发: {len(user_queue)}/{limit.limit}")
            return False
        
        user_queue.append(time.time())
        return True
    
    def check_group_limit(self, chat_id: int) -> bool:
        """检查群组频率限制"""
        limit = self.limits["group"]
        group_queue = self.group_requests[chat_id]
        self._clean_old_requests(group_queue, limit.window)
        
        if len(group_queue) >= limit.limit:
            logger.warning(f"群组 {chat_id} 频率限制触发: {len(group_queue)}/{limit.limit}")
            return False
        
        group_queue.append(time.time())
        return True
    
    def check_bind_limit(self, user_id: int) -> bool:
        """检查绑定尝试频率限制"""
        limit = self.limits["bind"]
        bind_queue = self.bind_attempts[user_id]
        self._clean_old_requests(bind_queue, limit.window)
        
        if len(bind_queue) >= limit.limit:
            logger.warning(f"用户 {user_id} 绑定尝试频率限制触发: {len(bind_queue)}/{limit.limit}")
            return False
        
        bind_queue.append(time.time())
        return True
    
    def check_all_limits(self, user_id: int, chat_id: int, command: str = None) -> bool:
        """检查所有频率限制"""
        # 检查全局限制
        if not self.check_global_limit():
            raise RateLimitError("全局请求频率过高，请稍后再试")
        
        # 检查用户限制
        if not self.check_user_limit(user_id):
            raise RateLimitError("您的请求频率过高，请稍后再试")
        
        # 检查群组限制
        if not self.check_group_limit(chat_id):
            raise RateLimitError("群组请求频率过高，请稍后再试")
        
        # 如果是绑定命令，检查绑定限制
        if command and command.startswith("/bind"):
            if not self.check_bind_limit(user_id):
                raise RateLimitError("绑定尝试次数过多，请明天再试")
        
        return True
    
    def get_user_remaining_requests(self, user_id: int) -> int:
        """获取用户剩余请求次数"""
        limit = self.limits["user"]
        user_queue = self.user_requests[user_id]
        self._clean_old_requests(user_queue, limit.window)
        return max(0, limit.limit - len(user_queue))
    
    def get_group_remaining_requests(self, chat_id: int) -> int:
        """获取群组剩余请求次数"""
        limit = self.limits["group"]
        group_queue = self.group_requests[chat_id]
        self._clean_old_requests(group_queue, limit.window)
        return max(0, limit.limit - len(group_queue))
    
    def get_bind_remaining_attempts(self, user_id: int) -> int:
        """获取剩余绑定尝试次数"""
        limit = self.limits["bind"]
        bind_queue = self.bind_attempts[user_id]
        self._clean_old_requests(bind_queue, limit.window)
        return max(0, limit.limit - len(bind_queue))
    
    def get_reset_time(self, limit_type: str, identifier: Optional[int] = None) -> int:
        """获取限制重置时间（秒）"""
        limit = self.limits.get(limit_type)
        if not limit:
            return 0
        
        if limit_type == "global":
            queue = self.global_requests
        elif limit_type == "user" and identifier:
            queue = self.user_requests[identifier]
        elif limit_type == "group" and identifier:
            queue = self.group_requests[identifier]
        elif limit_type == "bind" and identifier:
            queue = self.bind_attempts[identifier]
        else:
            return 0
        
        if not queue:
            return 0
        
        # 计算最早请求的重置时间
        earliest_request = queue[0]
        reset_time = earliest_request + limit.window
        return max(0, int(reset_time - time.time()))
    
    def update_config(self, config: BotConfig):
        """更新配置"""
        self.config = config
        self.limits = {
            "global": RateLimit(config.rate_limit_global, 3600),
            "group": RateLimit(config.rate_limit_group, 3600),
            "user": RateLimit(config.rate_limit_user, 3600),
            "bind": RateLimit(config.max_bind_attempts_per_day, 86400)
        }
        logger.info("频率限制器配置已更新")
    
    def reset_user_limits(self, user_id: int):
        """重置用户限制"""
        if user_id in self.user_requests:
            del self.user_requests[user_id]
        if user_id in self.bind_attempts:
            del self.bind_attempts[user_id]
        logger.info(f"已重置用户 {user_id} 的频率限制")
    
    def reset_group_limits(self, chat_id: int):
        """重置群组限制"""
        if chat_id in self.group_requests:
            del self.group_requests[chat_id]
        logger.info(f"已重置群组 {chat_id} 的频率限制")
    
    def reset_all_limits(self):
        """重置所有限制"""
        self.user_requests.clear()
        self.group_requests.clear()
        self.global_requests.clear()
        self.bind_attempts.clear()
        logger.info("已重置所有频率限制")
    
    def get_statistics(self) -> Dict[str, any]:
        """获取统计信息"""
        return {
            "global_requests": len(self.global_requests),
            "active_users": len(self.user_requests),
            "active_groups": len(self.group_requests),
            "bind_attempts": len(self.bind_attempts),
            "limits": {
                "global": self.limits["global"].__dict__,
                "group": self.limits["group"].__dict__,
                "user": self.limits["user"].__dict__,
                "bind": self.limits["bind"].__dict__
            }
        }
