Write-Host "正在清理Vite缓存..." -ForegroundColor Green

# 删除node_modules/.vite目录
if (Test-Path "node_modules\.vite") {
    Write-Host "删除 node_modules\.vite 目录..." -ForegroundColor Yellow
    Remove-Item -Recurse -Force "node_modules\.vite"
    Write-Host "node_modules\.vite 目录已删除" -ForegroundColor Green
} else {
    Write-Host "node_modules\.vite 目录不存在" -ForegroundColor Gray
}

# 删除dist目录
if (Test-Path "dist") {
    Write-Host "删除 dist 目录..." -ForegroundColor Yellow
    Remove-Item -Recurse -Force "dist"
    Write-Host "dist 目录已删除" -ForegroundColor Green
} else {
    Write-Host "dist 目录不存在" -ForegroundColor Gray
}

Write-Host "缓存清理完成！" -ForegroundColor Green
Write-Host "请重新运行 npm run dev 启动开发服务器" -ForegroundColor Cyan
Read-Host "按任意键继续..."
