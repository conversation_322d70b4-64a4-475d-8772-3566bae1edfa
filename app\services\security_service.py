"""
安全服务 - 提供数据隔离验证、安全审计和权限检查功能
"""

from typing import Optional, Dict, Any, List
from sqlalchemy.orm import Session
from sqlalchemy import text
import logging
from datetime import datetime

from app.models.user import User
from app.models.audit import AuditLog
from app.core.logging import get_logger


class SecurityService:
    """安全服务类 - 负责数据隔离验证和安全审计"""

    def __init__(self, db: Session):
        self.db = db
        self.logger = get_logger("security_service")

    def validate_merchant_isolation(
        self,
        current_user: User,
        target_merchant_id: Optional[int],
        operation: str,
        resource_type: str = "unknown"
    ) -> bool:
        """
        验证商户隔离 - 确保用户只能访问自己商户的数据

        Args:
            current_user: 当前用户
            target_merchant_id: 目标商户ID
            operation: 操作类型（如：read, create, update, delete）
            resource_type: 资源类型（如：walmart_ck, user, department）

        Returns:
            bool: 是否通过验证
        """
        try:
            # 超级管理员可以访问所有数据
            if current_user.is_superuser:
                self.logger.info(f"[SECURITY] 超级管理员 {current_user.id} 访问商户 {target_merchant_id} 的 {resource_type}")
                return True

            # 检查用户是否有商户信息
            if not current_user.merchant_id:
                self.log_security_violation(
                    current_user,
                    "MERCHANT_ISOLATION_VIOLATION",
                    f"用户没有商户信息，尝试{operation} {resource_type}",
                    {"target_merchant_id": target_merchant_id}
                )
                return False

            # 检查商户ID是否匹配
            if target_merchant_id and target_merchant_id != current_user.merchant_id:
                self.log_security_violation(
                    current_user,
                    "CROSS_MERCHANT_ACCESS_ATTEMPT",
                    f"用户尝试跨商户{operation} {resource_type}",
                    {
                        "user_merchant_id": current_user.merchant_id,
                        "target_merchant_id": target_merchant_id,
                        "operation": operation,
                        "resource_type": resource_type
                    }
                )
                return False

            return True

        except Exception as e:
            self.logger.error(f"商户隔离验证失败: {e}")
            return False

    def validate_ck_access(
        self,
        current_user: User,
        ck_id: int,
        operation: str = "access"
    ) -> bool:
        """
        验证CK访问权限 - 确保用户只能访问自己商户的CK

        Args:
            current_user: 当前用户
            ck_id: CK ID
            operation: 操作类型

        Returns:
            bool: 是否通过验证
        """
        try:
            # 超级管理员可以访问所有CK
            if current_user.is_superuser:
                return True

            # 查询CK的商户信息
            sql = text("""
                SELECT merchant_id 
                FROM walmart_ck 
                WHERE id = :ck_id AND is_deleted = 0
            """)
            result = self.db.execute(sql, {"ck_id": ck_id}).first()

            if not result:
                self.log_security_violation(
                    current_user,
                    "CK_NOT_FOUND",
                    f"尝试{operation}不存在的CK",
                    {"ck_id": ck_id}
                )
                return False

            ck_merchant_id = result.merchant_id

            # 验证商户隔离
            return self.validate_merchant_isolation(
                current_user,
                ck_merchant_id,
                operation,
                "walmart_ck"
            )

        except Exception as e:
            self.logger.error(f"CK访问权限验证失败: {e}")
            return False

    def validate_card_access(
        self,
        current_user: User,
        card_id: str,
        operation: str = "access"
    ) -> bool:
        """
        验证绑卡记录访问权限 - 确保用户只能访问自己商户的绑卡记录

        Args:
            current_user: 当前用户
            card_id: 绑卡记录ID
            operation: 操作类型

        Returns:
            bool: 是否通过验证
        """
        try:
            # 超级管理员可以访问所有绑卡记录
            if current_user.is_superuser:
                return True

            # 查询绑卡记录的商户信息
            sql = text("""
                SELECT merchant_id
                FROM card_records
                WHERE id = :card_id
            """)
            result = self.db.execute(sql, {"card_id": card_id}).first()

            if not result:
                self.log_security_violation(
                    current_user,
                    "CARD_NOT_FOUND",
                    f"尝试{operation}不存在的绑卡记录",
                    {"card_id": card_id}
                )
                return False

            card_merchant_id = result.merchant_id

            # 验证商户隔离
            return self.validate_merchant_isolation(
                current_user,
                card_merchant_id,
                operation,
                "card_record"
            )

        except Exception as e:
            self.logger.error(f"绑卡记录访问权限验证失败: {e}")
            return False

    def validate_department_access(
        self,
        current_user: User,
        department_id: int,
        operation: str = "access"
    ) -> bool:
        """
        验证部门访问权限 - 确保用户只能访问自己商户的部门

        Args:
            current_user: 当前用户
            department_id: 部门ID
            operation: 操作类型

        Returns:
            bool: 是否通过验证
        """
        try:
            # 超级管理员可以访问所有部门
            if current_user.is_superuser:
                return True

            # 查询部门的商户信息
            sql = text("""
                SELECT merchant_id
                FROM departments
                WHERE id = :department_id
            """)
            result = self.db.execute(sql, {"department_id": department_id}).first()

            if not result:
                self.log_security_violation(
                    current_user,
                    "DEPARTMENT_NOT_FOUND",
                    f"尝试{operation}不存在的部门",
                    {"department_id": department_id}
                )
                return False

            dept_merchant_id = result.merchant_id

            # 验证商户隔离
            return self.validate_merchant_isolation(
                current_user,
                dept_merchant_id,
                operation,
                "department"
            )

        except Exception as e:
            self.logger.error(f"部门访问权限验证失败: {e}")
            return False

    def log_security_violation(
        self,
        user: User,
        violation_type: str,
        description: str,
        details: Optional[Dict[str, Any]] = None
    ):
        """
        记录安全违规事件

        Args:
            user: 用户对象
            violation_type: 违规类型
            description: 描述
            details: 详细信息
        """
        try:
            # 记录到审计日志
            audit_log = AuditLog(
                user_id=user.id,
                username=user.username,
                action="SECURITY_VIOLATION",
                resource_type="SECURITY",
                resource_id=None,
                details={
                    "violation_type": violation_type,
                    "description": description,
                    "user_merchant_id": getattr(user, 'merchant_id', None),
                    "user_department_id": getattr(user, 'department_id', None),
                    "timestamp": datetime.utcnow().isoformat(),
                    **(details or {})
                },
                ip_address=None,  # 可以从请求中获取
                user_agent=None   # 可以从请求中获取
            )
            
            self.db.add(audit_log)
            self.db.commit()

            # 记录到日志
            self.logger.warning(f"[SECURITY_VIOLATION] {violation_type}: {description} - 用户: {user.id}, 详情: {details}")

        except Exception as e:
            self.logger.error(f"记录安全违规事件失败: {e}")
            self.db.rollback()

    def log_data_access(
        self,
        user: User,
        operation: str,
        resource_type: str,
        resource_id: Optional[int] = None,
        merchant_id: Optional[int] = None,
        details: Optional[Dict[str, Any]] = None
    ):
        """
        记录数据访问事件

        Args:
            user: 用户对象
            operation: 操作类型
            resource_type: 资源类型
            resource_id: 资源ID
            merchant_id: 商户ID
            details: 详细信息
        """
        try:
            audit_log = AuditLog(
                user_id=user.id,
                username=user.username,
                action=f"DATA_ACCESS_{operation.upper()}",
                resource_type=resource_type.upper(),
                resource_id=resource_id,
                details={
                    "operation": operation,
                    "merchant_id": merchant_id,
                    "user_merchant_id": getattr(user, 'merchant_id', None),
                    "timestamp": datetime.utcnow().isoformat(),
                    **(details or {})
                },
                ip_address=None,
                user_agent=None
            )
            
            self.db.add(audit_log)
            self.db.commit()

        except Exception as e:
            self.logger.error(f"记录数据访问事件失败: {e}")
            self.db.rollback()

    def check_permission_escalation(
        self,
        user: User,
        requested_permissions: List[str]
    ) -> Dict[str, Any]:
        """
        检查权限提升尝试

        Args:
            user: 用户对象
            requested_permissions: 请求的权限列表

        Returns:
            Dict: 检查结果
        """
        try:
            dangerous_permissions = [
                'data:merchant:all',
                'api:users:create',
                'api:roles:update',
                'api:permissions:update'
            ]

            escalation_attempts = []
            for perm in requested_permissions:
                if perm in dangerous_permissions and not user.is_superuser:
                    escalation_attempts.append(perm)

            if escalation_attempts:
                self.log_security_violation(
                    user,
                    "PERMISSION_ESCALATION_ATTEMPT",
                    f"尝试获取危险权限: {escalation_attempts}",
                    {"requested_permissions": requested_permissions}
                )

            return {
                "has_escalation": len(escalation_attempts) > 0,
                "escalation_attempts": escalation_attempts,
                "safe_permissions": [p for p in requested_permissions if p not in escalation_attempts]
            }

        except Exception as e:
            self.logger.error(f"权限提升检查失败: {e}")
            return {
                "has_escalation": False,
                "escalation_attempts": [],
                "safe_permissions": requested_permissions
            }

    def get_security_summary(self, user: User) -> Dict[str, Any]:
        """
        获取用户安全摘要

        Args:
            user: 用户对象

        Returns:
            Dict: 安全摘要
        """
        try:
            # 获取最近的安全违规记录
            recent_violations_sql = text("""
                SELECT COUNT(*) as count
                FROM audit_logs 
                WHERE user_id = :user_id 
                AND action = 'SECURITY_VIOLATION'
                AND created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)
            """)
            violations_result = self.db.execute(recent_violations_sql, {"user_id": user.id}).first()

            return {
                "user_id": user.id,
                "merchant_id": getattr(user, 'merchant_id', None),
                "is_superuser": user.is_superuser,
                "recent_violations": violations_result.count if violations_result else 0,
                "last_check": datetime.utcnow().isoformat()
            }

        except Exception as e:
            self.logger.error(f"获取安全摘要失败: {e}")
            return {
                "user_id": user.id,
                "error": str(e)
            }
