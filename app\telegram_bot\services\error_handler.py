"""
Telegram机器人错误处理服务
提供分类错误处理和友好的用户提示
"""

from enum import Enum
from typing import Dict, Any, Optional
from app.core.logging import get_logger

logger = get_logger(__name__)


class ErrorType(Enum):
    """错误类型枚举"""
    # 权限相关错误
    PERMISSION_DENIED = "permission_denied"
    USER_NOT_VERIFIED = "user_not_verified"
    GROUP_NOT_BOUND = "group_not_bound"

    # 验证相关错误
    INVALID_TOKEN = "invalid_token"
    TOKEN_EXPIRED = "token_expired"
    VERIFICATION_FAILED = "verification_failed"

    # 频率限制错误
    RATE_LIMIT_EXCEEDED = "rate_limit_exceeded"
    COMMAND_LIMIT = "command_limit"
    QUERY_LIMIT = "query_limit"

    # 数据相关错误
    DATA_NOT_FOUND = "data_not_found"
    INVALID_PARAMETER = "invalid_parameter"
    DATA_ACCESS_DENIED = "data_access_denied"

    # 系统错误
    SYSTEM_ERROR = "system_error"
    SERVICE_UNAVAILABLE = "service_unavailable"
    DATABASE_ERROR = "database_error"
    NETWORK_ERROR = "network_error"
    UNKNOWN_ERROR = "unknown_error"


class TelegramErrorHandler:
    """Telegram机器人错误处理器 - 重构版本，委托给增强错误处理器"""

    def __init__(self):
        self.logger = get_logger(__name__)
        # 委托给新的增强错误处理器
        from .enhanced_error_handler import EnhancedErrorHandler
        self.enhanced_handler = EnhancedErrorHandler()

    # 错误类型映射到新的错误处理器
    ERROR_TYPE_MAPPING = {
        ErrorType.USER_NOT_VERIFIED: "user_not_verified",
        ErrorType.GROUP_NOT_BOUND: "group_not_bound",
        ErrorType.PERMISSION_DENIED: "permission_denied",
        ErrorType.INVALID_TOKEN: "invalid_token",
        ErrorType.TOKEN_EXPIRED: "verification_token_expired",
        ErrorType.VERIFICATION_FAILED: "verification_failed",
        ErrorType.RATE_LIMIT_EXCEEDED: "rate_limit_exceeded",
        ErrorType.SYSTEM_ERROR: "system_error",
        ErrorType.UNKNOWN_ERROR: "unknown"
    }




    def get_error_message(
        self,
        error_type: ErrorType,
        context: Optional[Dict[str, Any]] = None
    ) -> str:
        """
        获取格式化的错误消息 - 委托给增强错误处理器

        Args:
            error_type: 错误类型
            context: 错误上下文信息

        Returns:
            str: 格式化的错误消息
        """
        try:
            # 映射到新的错误类型
            new_error_type = self.ERROR_TYPE_MAPPING.get(error_type, "unknown")

            # 委托给增强错误处理器
            user_name = context.get("user_name", "朋友") if context else "朋友"
            error_message, _ = self.enhanced_handler.format_error_message(
                new_error_type, context, user_name
            )

            return error_message

        except Exception as e:
            self.logger.error(f"获取错误消息失败: {e}")
            return "❌ 系统遇到错误，请稍后再试。"
    

    
    def is_recoverable(self, error_type: ErrorType) -> bool:
        """
        检查错误是否可恢复 - 委托给增强错误处理器

        Args:
            error_type: 错误类型

        Returns:
            bool: 是否可恢复
        """
        # 映射到新的错误类型
        new_error_type = self.ERROR_TYPE_MAPPING.get(error_type, "unknown")
        return self.enhanced_handler.is_recoverable_error(new_error_type)
    
    def get_suggestions(self, error_type: ErrorType) -> list:
        """
        获取错误解决建议 - 委托给增强错误处理器

        Args:
            error_type: 错误类型

        Returns:
            list: 解决建议列表
        """
        # 映射到新的错误类型
        new_error_type = self.ERROR_TYPE_MAPPING.get(error_type, "unknown")
        return self.enhanced_handler.get_recovery_suggestions(new_error_type)
    
    def get_help_commands(self, error_type: ErrorType) -> list:
        """
        获取错误相关的帮助命令 - 返回通用帮助命令

        Args:
            error_type: 错误类型

        Returns:
            list: 相关帮助命令列表
        """
        # 返回通用的帮助命令，具体的帮助通过增强错误处理器的按钮提供
        return ["/help", "/status"]

    def get_error_with_suggestions(
        self,
        error_type: ErrorType,
        context: Optional[Dict[str, Any]] = None,
        include_help_commands: bool = True
    ) -> str:
        """
        获取包含建议的完整错误消息

        Args:
            error_type: 错误类型
            context: 错误上下文信息
            include_help_commands: 是否包含帮助命令

        Returns:
            str: 完整的错误消息
        """
        try:
            message = self.get_error_message(error_type, context)

            if include_help_commands:
                help_commands = self.get_help_commands(error_type)
                if help_commands:
                    commands_text = " 或 ".join([f"`{cmd}`" for cmd in help_commands])
                    message += f"\n\n🆘 **快速帮助**：输入 {commands_text}"

            return message

        except Exception as e:
            self.logger.error(f"获取完整错误消息失败: {e}")
            return self.get_error_message(ErrorType.UNKNOWN_ERROR)

    def log_error(
        self,
        error_type: ErrorType,
        original_error: Exception,
        context: Optional[Dict[str, Any]] = None
    ):
        """
        记录错误日志

        Args:
            error_type: 错误类型
            original_error: 原始异常
            context: 错误上下文
        """
        try:
            log_data = {
                "error_type": error_type.value,
                "error_message": str(original_error),
                "context": context or {},
                "recoverable": self.is_recoverable(error_type),
                "suggestions": self.get_suggestions(error_type)
            }

            self.logger.error(f"Telegram机器人错误: {log_data}", exc_info=True)

        except Exception as e:
            self.logger.error(f"记录错误日志失败: {e}")


# 创建全局错误处理器实例
telegram_error_handler = TelegramErrorHandler()
