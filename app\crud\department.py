"""
部门CRUD操作 - 基于新的AO架构
"""
from typing import List, Optional, Dict, Any, Tuple
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_, func

from app.crud.base import CRUDBase
from app.models.department import Department
from app.schemas.department import DepartmentCreate, DepartmentUpdate


class CRUDDepartment(CRUDBase[Department, DepartmentCreate, DepartmentUpdate]):
    """部门CRUD操作类"""

    def get_by_merchant_and_code(
        self, db: Session, *, merchant_id: int, code: str
    ) -> Optional[Department]:
        """
        根据商户ID和部门代码获取部门
        
        Args:
            db: 数据库会话
            merchant_id: 商户ID
            code: 部门代码
            
        Returns:
            Optional[Department]: 部门对象或None
        """
        return db.query(self.model).filter(
            and_(
                self.model.merchant_id == merchant_id,
                self.model.code == code
            )
        ).first()

    def get_by_merchant(
        self, 
        db: Session, 
        merchant_id: int, 
        *, 
        skip: int = 0, 
        limit: int = 100,
        status: Optional[bool] = None
    ) -> List[Department]:
        """
        获取指定商户的部门列表
        
        Args:
            db: 数据库会话
            merchant_id: 商户ID
            skip: 跳过记录数
            limit: 限制记录数
            status: 状态过滤
            
        Returns:
            List[Department]: 部门列表
        """
        query = db.query(self.model).filter(self.model.merchant_id == merchant_id)
        
        if status is not None:
            query = query.filter(self.model.status == status)
            
        return query.offset(skip).limit(limit).all()

    def get_children(
        self, 
        db: Session, 
        parent_id: int, 
        *, 
        recursive: bool = False
    ) -> List[Department]:
        """
        获取子部门列表
        
        Args:
            db: 数据库会话
            parent_id: 父部门ID
            recursive: 是否递归获取所有后代部门
            
        Returns:
            List[Department]: 子部门列表
        """
        if not recursive:
            # 只获取直接子部门
            return db.query(self.model).filter(
                self.model.parent_id == parent_id
            ).order_by(self.model.sort_order).all()
        else:
            # 递归获取所有后代部门
            # 使用CTE（公共表表达式）进行递归查询
            from sqlalchemy import text
            
            sql = text("""
                WITH RECURSIVE department_tree AS (
                    -- 基础查询：直接子部门
                    SELECT id, merchant_id, name, code, parent_id, level, path, sort_order
                    FROM departments 
                    WHERE parent_id = :parent_id
                    
                    UNION ALL
                    
                    -- 递归查询：子部门的子部门
                    SELECT d.id, d.merchant_id, d.name, d.code, d.parent_id, d.level, d.path, d.sort_order
                    FROM departments d
                    INNER JOIN department_tree dt ON d.parent_id = dt.id
                )
                SELECT * FROM department_tree ORDER BY level, sort_order
            """)
            
            result = db.execute(sql, {"parent_id": parent_id})
            
            # 将结果转换为Department对象
            departments = []
            for row in result:
                dept = db.query(self.model).filter(self.model.id == row.id).first()
                if dept:
                    departments.append(dept)
            
            return departments

    def get_root_departments(
        self, 
        db: Session, 
        merchant_id: int
    ) -> List[Department]:
        """
        获取商户的根部门列表
        
        Args:
            db: 数据库会话
            merchant_id: 商户ID
            
        Returns:
            List[Department]: 根部门列表
        """
        return db.query(self.model).filter(
            and_(
                self.model.merchant_id == merchant_id,
                self.model.parent_id.is_(None)
            )
        ).order_by(self.model.sort_order).all()

    def get_department_path(self, db: Session, department_id: int) -> List[Department]:
        """
        获取部门的完整路径（从根部门到当前部门）
        
        Args:
            db: 数据库会话
            department_id: 部门ID
            
        Returns:
            List[Department]: 部门路径列表
        """
        department = self.get(db, id=department_id)
        if not department:
            return []
        
        path_departments = []
        current_dept = department
        
        # 向上遍历到根部门
        while current_dept:
            path_departments.insert(0, current_dept)  # 插入到开头
            if current_dept.parent_id:
                current_dept = self.get(db, id=current_dept.parent_id)
            else:
                break
        
        return path_departments

    def get_multi_with_filters_and_count(
        self,
        db: Session,
        *,
        skip: int = 0,
        limit: int = 100,
        filters: Optional[Dict[str, Any]] = None
    ) -> Tuple[List[Department], int]:
        """
        根据过滤条件获取部门列表和总数

        Args:
            db: 数据库会话
            skip: 跳过记录数
            limit: 限制记录数
            filters: 过滤条件字典

        Returns:
            Tuple[List[Department], int]: (部门列表, 总数)
        """
        query = db.query(self.model)

        # 应用过滤条件
        query = self._apply_department_filters(query, filters)

        # 获取总数
        total = query.count()

        # 获取分页数据
        departments = self._get_ordered_departments(query, skip, limit)

        return departments, total

    def _apply_department_filters(self, query, filters: Optional[Dict[str, Any]]):
        """应用部门过滤条件"""
        if not filters:
            return query

        # 基础过滤条件
        query = self._apply_basic_filters(query, filters)

        # 搜索过滤条件
        query = self._apply_search_filters(query, filters)

        # 列表过滤条件
        query = self._apply_list_filters(query, filters)

        return query

    def _apply_basic_filters(self, query, filters: Dict[str, Any]):
        """应用基础过滤条件"""
        if "merchant_id" in filters:
            query = query.filter(self.model.merchant_id == filters["merchant_id"])

        if "parent_id" in filters:
            if filters["parent_id"] is None:
                query = query.filter(self.model.parent_id.is_(None))
            else:
                query = query.filter(self.model.parent_id == filters["parent_id"])

        if "status" in filters:
            query = query.filter(self.model.status == filters["status"])

        if "level" in filters:
            query = query.filter(self.model.level == filters["level"])

        return query

    def _apply_search_filters(self, query, filters: Dict[str, Any]):
        """应用搜索过滤条件"""
        if "name" in filters and filters["name"]:
            query = query.filter(self.model.name.like(f"%{filters['name']}%"))

        if "code" in filters and filters["code"]:
            query = query.filter(self.model.code.like(f"%{filters['code']}%"))

        return query

    def _apply_list_filters(self, query, filters: Dict[str, Any]):
        """应用列表过滤条件"""
        if "id__in" in filters and filters["id__in"]:
            query = query.filter(self.model.id.in_(filters["id__in"]))

        if "id__not_in" in filters and filters["id__not_in"]:
            query = query.filter(~self.model.id.in_(filters["id__not_in"]))

        return query

    def _get_ordered_departments(self, query, skip: int, limit: int):
        """获取排序后的部门数据"""
        return query.order_by(
            self.model.level,
            self.model.sort_order,
            self.model.created_at
        ).offset(skip).limit(limit).all()

    def update_department_path(self, db: Session, department: Department):
        """
        更新部门路径
        
        Args:
            db: 数据库会话
            department: 部门对象
        """
        if department.parent_id:
            parent = self.get(db, id=department.parent_id)
            if parent:
                department.level = parent.level + 1
                department.path = f"{parent.path}{parent.id}/"
            else:
                # 父部门不存在，设为根部门
                department.level = 1
                department.path = f"/m{department.merchant_id}/"
        else:
            # 根部门
            department.level = 1
            department.path = f"/m{department.merchant_id}/"
        
        db.commit()

    def move_department(
        self, 
        db: Session, 
        department_id: int, 
        new_parent_id: Optional[int]
    ) -> Department:
        """
        移动部门到新的父部门下
        
        Args:
            db: 数据库会话
            department_id: 要移动的部门ID
            new_parent_id: 新的父部门ID（None表示移动到根级别）
            
        Returns:
            Department: 更新后的部门对象
        """
        department = self.get(db, id=department_id)
        if not department:
            raise ValueError("部门不存在")
        
        # 检查新父部门是否存在且属于同一商户
        if new_parent_id:
            new_parent = self.get(db, id=new_parent_id)
            if not new_parent:
                raise ValueError("新父部门不存在")
            if new_parent.merchant_id != department.merchant_id:
                raise ValueError("不能移动到其他商户的部门下")
            
            # 检查是否会形成循环引用
            if self._would_create_cycle(db, department_id, new_parent_id):
                raise ValueError("移动操作会形成循环引用")
        
        # 更新父部门
        department.parent_id = new_parent_id
        
        # 更新路径和层级
        self.update_department_path(db, department)
        
        # 递归更新所有子部门的路径和层级
        self._update_children_paths(db, department)
        
        return department

    def _would_create_cycle(
        self, 
        db: Session, 
        department_id: int, 
        new_parent_id: int
    ) -> bool:
        """
        检查移动操作是否会创建循环引用
        
        Args:
            db: 数据库会话
            department_id: 要移动的部门ID
            new_parent_id: 新的父部门ID
            
        Returns:
            bool: 是否会创建循环引用
        """
        # 检查新父部门是否是当前部门的后代
        current_id = new_parent_id
        while current_id:
            if current_id == department_id:
                return True
            
            parent_dept = self.get(db, id=current_id)
            if parent_dept:
                current_id = parent_dept.parent_id
            else:
                break
        
        return False

    def _update_children_paths(self, db: Session, parent_department: Department):
        """
        递归更新子部门的路径和层级
        
        Args:
            db: 数据库会话
            parent_department: 父部门对象
        """
        children = self.get_children(db, parent_department.id)
        
        for child in children:
            child.level = parent_department.level + 1
            child.path = f"{parent_department.path}{parent_department.id}/"
            db.commit()
            
            # 递归更新子部门的子部门
            self._update_children_paths(db, child)


# 创建全局实例
department = CRUDDepartment(Department)
