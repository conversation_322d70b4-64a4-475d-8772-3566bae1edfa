/**
 * 订单号生成工具
 * 用于沃尔玛绑卡系统的商户订单号自动生成
 */

/**
 * 生成唯一的商户订单号
 * @param {string} prefix - 订单号前缀，默认为 'WM'
 * @param {string} merchantCode - 商户代码（可选）
 * @returns {string} 生成的订单号
 */
export function generateMerchantOrderId(prefix = 'WM', merchantCode = '') {
  // 获取当前时间戳（毫秒）
  const timestamp = Date.now()
  
  // 生成日期部分（YYYYMMDD格式）
  const date = new Date(timestamp)
  const dateStr = date.getFullYear().toString() + 
                  (date.getMonth() + 1).toString().padStart(2, '0') + 
                  date.getDate().toString().padStart(2, '0')
  
  // 生成时间部分（HHMMSS格式）
  const timeStr = date.getHours().toString().padStart(2, '0') + 
                  date.getMinutes().toString().padStart(2, '0') + 
                  date.getSeconds().toString().padStart(2, '0')
  
  // 生成随机数（6位）
  const randomNum = Math.floor(Math.random() * 1000000).toString().padStart(6, '0')
  
  // 生成随机字母（3位大写字母）
  const randomLetters = Array.from({length: 3}, () => 
    String.fromCharCode(65 + Math.floor(Math.random() * 26))
  ).join('')
  
  // 构建订单号
  let orderId = `${prefix}${dateStr}${timeStr}${randomNum}${randomLetters}`
  
  // 如果提供了商户代码，添加商户代码的哈希值（取前4位）
  if (merchantCode && merchantCode.trim()) {
    const merchantHash = hashCode(merchantCode.trim()).toString(16).substring(0, 4).toUpperCase()
    orderId = `${prefix}${merchantHash}${dateStr}${timeStr}${randomNum}${randomLetters}`
  }
  
  return orderId
}

/**
 * 简单的字符串哈希函数
 * @param {string} str - 要哈希的字符串
 * @returns {number} 哈希值
 */
function hashCode(str) {
  let hash = 0
  if (str.length === 0) return hash
  for (let i = 0; i < str.length; i++) {
    const char = str.charCodeAt(i)
    hash = ((hash << 5) - hash) + char
    hash = hash & hash // 转换为32位整数
  }
  return Math.abs(hash)
}

/**
 * 验证订单号格式是否有效
 * @param {string} orderId - 订单号
 * @returns {boolean} 是否有效
 */
export function validateOrderId(orderId) {
  if (!orderId || typeof orderId !== 'string') {
    return false
  }
  
  // 基本长度检查（至少10位）
  if (orderId.length < 10) {
    return false
  }
  
  // 检查是否包含非法字符（只允许字母、数字、下划线、连字符）
  const validPattern = /^[A-Za-z0-9_-]+$/
  return validPattern.test(orderId)
}

/**
 * 格式化订单号显示
 * @param {string} orderId - 订单号
 * @returns {string} 格式化后的订单号
 */
export function formatOrderIdDisplay(orderId) {
  if (!orderId) return ''
  
  // 如果是系统生成的订单号，进行格式化显示
  if (orderId.startsWith('WM') && orderId.length >= 20) {
    // WM + 商户哈希(4) + 日期(8) + 时间(6) + 随机数(6) + 字母(3)
    const parts = []
    let index = 0
    
    // 前缀
    parts.push(orderId.substring(index, index + 2))
    index += 2
    
    // 商户哈希（如果存在）
    if (orderId.length >= 27) {
      parts.push(orderId.substring(index, index + 4))
      index += 4
    }
    
    // 日期
    parts.push(orderId.substring(index, index + 8))
    index += 8
    
    // 时间
    parts.push(orderId.substring(index, index + 6))
    index += 6
    
    // 其余部分
    if (index < orderId.length) {
      parts.push(orderId.substring(index))
    }
    
    return parts.join('-')
  }
  
  return orderId
}

/**
 * 检查订单号是否为系统自动生成
 * @param {string} orderId - 订单号
 * @returns {boolean} 是否为系统生成
 */
export function isSystemGeneratedOrderId(orderId) {
  return orderId && (
    orderId.startsWith('WM') || 
    orderId.startsWith('SYS_AUTO_GEN_ORDER_')
  )
}

/**
 * 创建订单号输入框的混合对象
 * 提供统一的订单号自动生成功能
 */
export const orderIdMixin = {
  methods: {
    /**
     * 自动生成订单号（如果字段为空）
     * @param {string} fieldName - 表单字段名
     * @param {string} merchantCode - 商户代码
     */
    autoGenerateOrderId(fieldName, merchantCode = '') {
      if (!this[fieldName] || this[fieldName].trim() === '') {
        this[fieldName] = generateMerchantOrderId('WM', merchantCode)
        
        // 显示提示信息
        if (this.$message) {
          this.$message.success('已自动生成订单号')
        }
      }
    },

    /**
     * 处理订单号字段失焦事件
     * @param {string} fieldName - 表单字段名
     * @param {string} merchantCode - 商户代码
     */
    handleOrderIdBlur(fieldName, merchantCode = '') {
      this.autoGenerateOrderId(fieldName, merchantCode)
    },

    /**
     * 手动触发订单号生成
     * @param {string} fieldName - 表单字段名
     * @param {string} merchantCode - 商户代码
     * @param {boolean} force - 是否强制生成（覆盖现有值）
     */
    generateOrderIdManually(fieldName, merchantCode = '', force = false) {
      if (force || !this[fieldName] || this[fieldName].trim() === '') {
        this[fieldName] = generateMerchantOrderId('WM', merchantCode)
        
        if (this.$message) {
          this.$message.success('订单号已生成')
        }
      } else {
        if (this.$confirm) {
          this.$confirm('当前已有订单号，是否要重新生成？', '确认', {
            confirmButtonText: '重新生成',
            cancelButtonText: '取消',
            type: 'warning'
          }).then(() => {
            this[fieldName] = generateMerchantOrderId('WM', merchantCode)
            if (this.$message) {
              this.$message.success('订单号已重新生成')
            }
          }).catch(() => {
            // 用户取消，不做任何操作
          })
        }
      }
    },

    /**
     * 验证订单号格式
     * @param {string} orderId - 订单号
     * @returns {boolean} 是否有效
     */
    validateOrderIdFormat(orderId) {
      return validateOrderId(orderId)
    },

    /**
     * 格式化订单号显示
     * @param {string} orderId - 订单号
     * @returns {string} 格式化后的订单号
     */
    formatOrderId(orderId) {
      return formatOrderIdDisplay(orderId)
    }
  }
}
