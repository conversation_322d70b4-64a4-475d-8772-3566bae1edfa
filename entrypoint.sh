#!/bin/bash
# 设置环境变量
export PYTHONPATH=/app

# 确保配置目录存在
if [ -d "./config" ]; then
  echo "使用当前目录中的配置"
else
  echo "配置目录不存在，创建空配置目录"
  mkdir -p ./config
fi

# 等待数据库服务启动
echo "等待数据库服务启动..."
sleep 10

# 检查数据库连接并确保初始化正确
echo "检查数据库初始化状态..."
if [ -f "/app/scripts/ensure_init_sql.py" ]; then
    echo "执行数据库初始化检查..."
    python /app/scripts/ensure_init_sql.py
    if [ $? -ne 0 ]; then
        echo "数据库初始化检查失败，但继续启动应用（应用启动时会自动初始化）"
    else
        echo "数据库初始化检查通过"
    fi
else
    echo "数据库初始化检查脚本不存在，跳过检查"
fi

# 运行可执行文件
echo "启动应用服务..."
./walmart-bind-card-server "$@"