import { defineStore } from 'pinia'
import { cardDataApi } from '@/api/modules/cardData'

export const useCardDataStore = defineStore('cardData', {
    state: () => ({
        cardList: [],
        cardStats: null,
        currentCard: null,
        loading: false,
        error: null
    }),

    getters: {
        getCardList: (state) => state.cardList,
        getCardStats: (state) => state.cardStats,
        getCurrentCard: (state) => state.currentCard,
        isLoading: (state) => state.loading
    },

    actions: {
        // 获取卡列表
        async fetchCardList(params) {
            this.loading = true
            try {
                const response = await cardDataApi.getList(params)
                this.cardList = response.items || []
                return response
            } catch (error) {
                this.error = error.message
                return null
            } finally {
                this.loading = false
            }
        },

        // 获取卡详情
        async fetchCardDetail(id) {
            this.loading = true
            try {
                const data = await cardDataApi.getDetail(id)
                if (id === this.currentCard?.id) {
                    this.currentCard = data
                }
                return data
            } catch (error) {
                this.error = error.message
                return null
            } finally {
                this.loading = false
            }
        },

        // 删除卡
        async deleteCard(id) {
            this.loading = true
            try {
                await cardDataApi.delete(id)
                if (id === this.currentCard?.id) {
                    this.currentCard = null
                }
                this.cardList = this.cardList.filter(item => item.id !== id)
                return true
            } catch (error) {
                this.error = error.message
                return false
            } finally {
                this.loading = false
            }
        },

        // 重试单个卡记录
        async retryCard(id) {
            this.loading = true
            try {
                const result = await cardDataApi.retry(id)
                // 更新列表中对应的卡记录状态
                const cardIndex = this.cardList.findIndex(card => card.id === id)
                if (cardIndex !== -1) {
                    this.cardList[cardIndex].status = result.status || 'pending'
                    this.cardList[cardIndex].retry_count = result.retry_count || 0
                }
                return result
            } catch (error) {
                this.error = error.message
                throw error
            } finally {
                this.loading = false
            }
        },

        // 同步单个卡片金额
        async syncCardAmount(id, forceUpdate = false) {
            this.loading = true
            try {
                const result = await cardDataApi.syncAmount(id, forceUpdate)
                // 更新列表中对应的卡记录金额信息
                if (result.success && result.actual_amount !== undefined) {
                    const cardIndex = this.cardList.findIndex(card => card.id === id)
                    if (cardIndex !== -1) {
                        this.cardList[cardIndex].actual_amount = result.actual_amount
                        if (result.balance_info) {
                            this.cardList[cardIndex].balance = result.balance_info.balance
                            this.cardList[cardIndex].cardBalance = result.balance_info.cardBalance
                            this.cardList[cardIndex].balanceCnt = result.balance_info.balanceCnt
                        }
                    }
                }
                return result
            } catch (error) {
                this.error = error.message
                throw error
            } finally {
                this.loading = false
            }
        },

        // 批量同步卡片金额
        async batchSyncCardAmount(cardIds, forceUpdate = false) {
            this.loading = true
            try {
                const result = await cardDataApi.batchSyncAmount(cardIds, forceUpdate)
                // 批量更新成功的记录
                if (result.success && result.results) {
                    result.results.forEach(item => {
                        if (item.success && item.actual_amount !== undefined) {
                            const cardIndex = this.cardList.findIndex(card => card.id === item.card_id)
                            if (cardIndex !== -1) {
                                this.cardList[cardIndex].actual_amount = item.actual_amount
                                if (item.balance_info) {
                                    this.cardList[cardIndex].balance = item.balance_info.balance
                                    this.cardList[cardIndex].cardBalance = item.balance_info.cardBalance
                                    this.cardList[cardIndex].balanceCnt = item.balance_info.balanceCnt
                                }
                            }
                        }
                    })
                }
                return result
            } catch (error) {
                this.error = error.message
                throw error
            } finally {
                this.loading = false
            }
        },

        // 手动触发单个卡片回调
        async triggerCardCallback(id, forceCallback = false) {
            this.loading = true
            try {
                const result = await cardDataApi.triggerCallback(id, forceCallback)
                // 更新列表中对应的卡记录回调状态
                if (result.success) {
                    const cardIndex = this.cardList.findIndex(card => card.id === id)
                    if (cardIndex !== -1) {
                        // 记录之前的状态用于日志
                        const previousStatus = this.cardList[cardIndex].callback_status

                        // 更新为新的状态
                        this.cardList[cardIndex].callback_status = 'pending'
                        this.cardList[cardIndex].callback_result = forceCallback
                            ? '强制手动触发回调，等待处理'
                            : '手动触发回调，等待处理'
                        this.cardList[cardIndex].callback_time = null

                        // 如果是强制回调，在结果中记录之前的状态
                        if (forceCallback && previousStatus === 'success') {
                            this.cardList[cardIndex].callback_result += `（覆盖之前的成功状态）`
                        }
                    }
                }
                return result
            } catch (error) {
                this.error = error.message
                throw error
            } finally {
                this.loading = false
            }
        },

        // 批量触发回调
        async batchTriggerCallback(cardIds, forceCallback = false) {
            this.loading = true
            try {
                const result = await cardDataApi.batchTriggerCallback(cardIds, forceCallback)
                // 批量更新成功的记录
                if (result.success && result.results) {
                    result.results.forEach(item => {
                        if (item.success) {
                            const cardIndex = this.cardList.findIndex(card => card.id === item.card_id)
                            if (cardIndex !== -1) {
                                // 记录之前的状态
                                const previousStatus = this.cardList[cardIndex].callback_status

                                // 更新为新的状态
                                this.cardList[cardIndex].callback_status = 'pending'
                                this.cardList[cardIndex].callback_result = forceCallback
                                    ? '批量强制触发回调，等待处理'
                                    : '批量触发回调，等待处理'
                                this.cardList[cardIndex].callback_time = null

                                // 如果是强制回调且之前是成功状态，添加说明
                                if (forceCallback && previousStatus === 'success') {
                                    this.cardList[cardIndex].callback_result += `（覆盖之前的成功状态）`
                                }
                            }
                        }
                    })
                }
                return result
            } catch (error) {
                this.error = error.message
                throw error
            } finally {
                this.loading = false
            }
        },

        // 获取卡统计数据
        async fetchCardStats(params) {
            this.loading = true
            try {
                const response = await cardDataApi.getStatistics(params)
                // 转换后端返回的字段名到前端期望的字段名
                if (response) {
                    this.cardStats = {
                        // 总数据
                        totalSuccess: parseInt(response.success_count) || 0,
                        totalFailed: parseInt(response.failed_count) || 0,
                        totalPending: parseInt(response.pending_count) || 0,
                        totalBinding: parseInt(response.binding_count) || 0,
                        totalCount: parseInt(response.total_count) || 0,
                        successRate: parseFloat(response.success_rate) || 0,

                        // 今日数据
                        todaySuccess: parseInt(response.today_success) || 0,
                        todayFailed: parseInt(response.today_total) - parseInt(response.today_success) || 0,
                        todayTotal: parseInt(response.today_total) || 0,
                        todaySuccessRate: parseFloat(response.today_success_rate) || 0,

                        // 金额统计数据（新增）
                        totalRequestAmount: parseInt(response.total_request_amount) || 0,
                        totalActualAmount: parseInt(response.total_actual_amount) || 0,
                        successRequestAmount: parseInt(response.success_request_amount) || 0,
                        todayRequestAmount: parseInt(response.today_request_amount) || 0,
                        todayActualAmount: parseInt(response.today_actual_amount) || 0,
                        todaySuccessRequestAmount: parseInt(response.today_success_request_amount) || 0,

                        // 其他信息
                        currentUserMerchantId: response.current_user_merchant_id,
                        currentUserDepartmentId: response.current_user_department_id,
                        isSuperuser: response.is_superuser
                    }
                } else {
                    this.cardStats = null
                }
                return response
            } catch (error) {
                this.error = error.message
                return null
            } finally {
                this.loading = false
            }
        },

        // 设置当前卡
        setCurrentCard(card) {
            this.currentCard = card
        },

        // 重置状态
        resetState() {
            this.cardList = []
            this.cardStats = null
            this.currentCard = null
            this.loading = false
            this.error = null
        },

        /**
         * 获取卡记录的敏感信息
         * @param {number} cardId - 卡记录 ID
         * @returns {Promise<object>} 包含敏感信息的卡记录数据
         */
        async getCardRecordWithSensitive(cardId) {
            this.loading = true;
            try {
                const data = await cardDataApi.getSensitiveInfo(cardId);
                return data;
            } catch (error) {
                console.error('获取卡记录敏感信息失败:', error);
                this.error = error.message || '获取敏感信息失败';
                throw error;
            } finally {
                this.loading = false;
            }
        },
    }
})