import { defineStore } from "pinia";
import { authApi } from "@/api/modules/auth";
import { userApi } from "@/api/modules/user";
import { TokenManager } from "@/utils/auth";
import { usePermissionStore } from "./permission";

// 用户角色常量
export const UserRole = {
  SUPER_ADMIN: "super_admin",
  PLATFORM_ADMIN: "platform_admin",
  MERCHANT_ADMIN: "merchant_admin",
  MERCHANT_OPERATOR: "merchant_operator",
  CK_SUPPLIER: "ck_supplier",
  API_USER: "api_user",
};

export const useUserStore = defineStore("user", {
  state: () => ({
    token: TokenManager.getToken() || "",
    userInfo: TokenManager.getUserInfo() || null,
  }),

  getters: {
    isLogin: (state) => !!state.token && !TokenManager.isTokenExpired(),
    hasUserInfo: (state) => {
      // 检查用户信息是否完整
      return (
        !!state.userInfo && !!state.userInfo.username && !!state.userInfo.id
      );
    },
    userName: (state) =>
      state.userInfo?.full_name || state.userInfo?.username || "未知",

    // 用户ID - 用于localStorage键名生成
    userId: (state) => state.userInfo?.id,

    // 角色判断
    isSuperAdmin: (state) => state.userInfo?.role === UserRole.SUPER_ADMIN,
    isPlatformAdmin: (state) =>
      state.userInfo?.role === UserRole.PLATFORM_ADMIN,
    isMerchantAdmin: (state) =>
      state.userInfo?.role === UserRole.MERCHANT_ADMIN,

    // 商户相关
    merchantId: (state) => state.userInfo?.merchant_id,

    // 部门相关
    departmentId: (state) => state.userInfo?.department_id,
  },

  actions: {
    // 登录
    async login(loginData) {
      try {
        // 1. 先清理之前的状态（防止缓存干扰）
        await this.clearAllState();

        // 2. 调用登录接口（支持TOTP）
        const { access_token, expires_in } = await authApi.login(loginData);

        // 3. 设置token
        this.token = access_token;
        TokenManager.setToken(access_token, expires_in);

        // 4. 获取用户信息
        const userInfoSuccess = await this.getUserInfo();
        if (!userInfoSuccess) {
          throw new Error("获取用户信息失败");
        }

        // 5. 初始化系统数据（菜单等）
        try {
          const { useSystemStore } = await import("./system");
          const systemStore = useSystemStore();
          if (systemStore.fetchUserMenus) {
            await systemStore.fetchUserMenus();
          }
        } catch (error) {
          console.log("系统Store不存在或无fetchUserMenus方法");
        }

        console.log("登录成功，用户信息和菜单已加载");
        return true;
      } catch (error) {
        console.error("登录失败:", error);
        // 检查是否是需要TOTP的错误
        if (error.message === "totp_required") {
          return "totp_required";
        }
        // 登录失败时清理状态
        await this.clearAllState();
        return false;
      }
    },

    // 登出
    async logout() {
      try {
        // 先调用后端登出接口
        await authApi.logout();
      } catch (error) {
        console.error("后端登出失败:", error);
        // 即使后端登出失败，也要清理前端状态
      }

      // 清理前端状态（无论后端是否成功）
      await this.clearAllState();
      return true;
    },

    // 清理所有前端状态
    async clearAllState() {
      try {
        // 1. 清理用户状态
        this.resetState();

        // 2. 清理token
        TokenManager.removeToken();

        // 3. 重置权限状态
        const permissionStore = usePermissionStore();
        await permissionStore.resetPermission();

        // 4. 重置系统状态（包括菜单缓存）
        try {
          const { useSystemStore } = await import("./system");
          const systemStore = useSystemStore();
          if (systemStore.resetState) {
            systemStore.resetState();
          }
        } catch (error) {
          console.log("系统Store不存在或无resetState方法");
        }

        // 5. 重置其他相关store
        try {
          const { useMerchantStore } = await import("./merchant");
          const merchantStore = useMerchantStore();
          if (merchantStore.resetState) {
            merchantStore.resetState();
          }
        } catch (error) {
          console.log("商户Store不存在或无resetState方法");
        }

        console.log("所有前端状态已清理");
      } catch (error) {
        console.error("清理前端状态时发生错误:", error);
      }
    },

    // 获取用户信息
    async getUserInfo() {
      try {
        const response = await authApi.getUserInfo();

        // 处理嵌套的响应格式：{code: 0, data: {success: true, data: {...}, message: "..."}, message: "..."}
        let userInfo = response;

        // 如果有嵌套的data结构，提取最内层的data
        if (response.data && response.data.data) {
          userInfo = response.data.data;
        } else if (response.data) {
          userInfo = response.data;
        }

        if (!userInfo || !userInfo.username) {
          throw new Error("用户信息格式无效: " + JSON.stringify(userInfo));
        }

        // 处理角色信息 - 从roles数组中提取主要角色
        if (
          userInfo.roles &&
          Array.isArray(userInfo.roles) &&
          userInfo.roles.length > 0
        ) {
          // 使用第一个角色作为主要角色（保持向后兼容）
          userInfo.role = userInfo.roles[0].code;
          userInfo.role_name = userInfo.roles[0].name;
        }

        this.userInfo = userInfo;
        // 将用户信息存储到本地
        TokenManager.setUserInfo(userInfo);

        // 设置权限信息到权限Store
        const permissionStore = usePermissionStore();
        permissionStore.setUserPermission(userInfo);

        return true;
      } catch (error) {
        console.error("获取用户信息失败:", error);
        console.error("错误详情:", {
          message: error.message,
          response: error.response?.data,
          status: error.response?.status,
        });
        this.userInfo = null;
        return false;
      }
    },

    // 更新用户信息
    async updateUserInfo(userData) {
      try {
        await userApi.updateUserInfo(userData);
        this.userInfo = { ...this.userInfo, ...userData };
        // 更新本地存储的用户信息
        TokenManager.setUserInfo(this.userInfo);
        return true;
      } catch (error) {
        console.error("更新用户信息失败:", error);
        return false;
      }
    },

    // 修改密码
    async changePassword(passwordData) {
      try {
        await userApi.changePassword(passwordData);
        return true;
      } catch (error) {
        console.error("修改密码失败:", error);
        return false;
      }
    },

    // 重置状态
    resetState() {
      this.token = "";
      this.userInfo = null;
      // 清理本地存储的用户信息
      TokenManager.removeUserInfo();
    },
  },
});
