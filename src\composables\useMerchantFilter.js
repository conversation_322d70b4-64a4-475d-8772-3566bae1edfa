import { computed } from 'vue'
import { useMerchantStore } from '@/store/modules/merchant'

/**
 * 商家过滤器组合式函数
 * @param {Object} queryParams 查询参数对象 (reactive或ref对象)
 * @param {String} merchantKey 商家ID在查询参数中的键名，默认为'merchant_id'
 * @returns {Object} 包含商家过滤的查询参数
 */
export default function useMerchantFilter(queryParams, merchantKey = 'merchant_id') {
    const merchantStore = useMerchantStore()

    // 根据当前商家生成查询参数
    const merchantQueryParams = computed(() => {
        const params = { ...queryParams.value || {} }

        // 如果有选择商家，添加商家过滤
        if (merchantStore.currentMerchant) {
            params[merchantKey] = merchantStore.currentMerchantId
        }

        return params
    })

    return {
        merchantQueryParams,
        currentMerchant: computed(() => merchantStore.currentMerchant),
        currentMerchantId: computed(() => merchantStore.currentMerchantId)
    }
}

// 添加命名导出，便于导入时使用 { useMerchantFilter }
export { useMerchantFilter } 