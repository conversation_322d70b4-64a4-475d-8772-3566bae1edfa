"""
Telegram日志管理API接口
"""

from typing import Optional, Dict, Any, List
from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session
from sqlalchemy import or_
from datetime import datetime, timedelta
from app.api.deps import get_db, get_current_user
from app.models.user import User
from app.models.telegram_group import TelegramGroup
from app.models.telegram_bot_log import TelegramBotLog, LogStatus
from app.schemas.response import BaseResponse
from app.core.logging import get_logger

logger = get_logger(__name__)
from app.core.logging import get_logger

logger = get_logger(__name__)
router = APIRouter()


@router.get("/users/{user_id}/logs")
async def get_user_logs(
    user_id: int,
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(10, ge=1, le=100, description="每页数量"),
    group_id: Optional[int] = Query(None, description="群组ID"),
    log_type: Optional[str] = Query(None, description="日志类型"),
    start_time: Optional[str] = Query(None, description="开始时间"),
    end_time: Optional[str] = Query(None, description="结束时间"),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    获取特定用户的日志
    """
    try:
        # 检查权限：仅允许超级管理员
        if not current_user.is_superuser:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="仅超级管理员可以查看用户日志"
            )

        # 如果指定了group_id，验证群组存在且有权限访问
        if group_id:
            group = db.query(TelegramGroup).filter_by(id=group_id).first()
            if not group:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="群组不存在"
                )

            if not current_user.can_access_merchant_data(group.merchant_id):
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail="无权限访问该群组"
                )

        # 从数据库查询用户日志数据
        query = db.query(TelegramBotLog)

        # 用户ID过滤
        if user_id:
            query = query.filter(TelegramBotLog.user_id == user_id)

        # 群组ID过滤
        if group_id:
            query = query.filter(TelegramBotLog.chat_id == group_id)

        # 时间范围过滤
        if start_time and end_time:
            try:
                start_date = datetime.fromisoformat(start_time.replace('Z', '+00:00'))
                end_date = datetime.fromisoformat(end_time.replace('Z', '+00:00'))
                query = query.filter(
                    TelegramBotLog.created_at >= start_date,
                    TelegramBotLog.created_at <= end_date
                )
            except ValueError:
                pass

        # 日志类型过滤
        if log_type:
            if log_type == "command":
                query = query.filter(TelegramBotLog.command.isnot(None))
            elif log_type == "operation":
                query = query.filter(TelegramBotLog.command.is_(None))

        # 权限过滤
        if not current_user.is_superuser:
            accessible_merchant_ids = current_user.get_accessible_merchant_ids()
            if accessible_merchant_ids:
                query = query.join(TelegramGroup, TelegramBotLog.chat_id == TelegramGroup.chat_id).filter(
                    TelegramGroup.merchant_id.in_(accessible_merchant_ids)
                )

        # 获取总数
        total = query.count()

        # 分页查询
        offset = (page - 1) * page_size
        bot_logs = query.order_by(TelegramBotLog.created_at.desc()).offset(offset).limit(page_size).all()

        # 格式化日志数据
        logs = []
        for log in bot_logs:
            # 从response_data中提取消息
            response_message = ""
            if log.response_data and isinstance(log.response_data, dict):
                response_message = log.response_data.get("message", "")

            logs.append({
                "id": log.id,
                "timestamp": log.created_at.isoformat() if log.created_at else None,
                "user_id": str(log.user_id) if log.user_id else None,
                "group_id": str(log.chat_id) if log.chat_id else None,
                "command": log.command or "operation",
                "status": log.status,
                "message": response_message or log.error_message or "无消息",
                "response_time": log.execution_time or 0,
                "details": {
                    "command": log.command,
                    "request_data": log.request_data,
                    "response_data": log.response_data,
                    "error_message": log.error_message
                }
            })

        return {
            "logs": logs,
            "pagination": {
                "page": page,
                "page_size": page_size,
                "total": total,
                "pages": (total + page_size - 1) // page_size
            },
            "filters": {
                "user_id": user_id,
                "group_id": group_id,
                "log_type": log_type,
                "start_time": start_time,
                "end_time": end_time
            }
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取用户日志失败: {e}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取用户日志失败"
        )


@router.get("/logs")
async def get_telegram_logs(
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(20, ge=1, le=100, description="每页数量"),
    log_type: Optional[str] = Query(None, description="日志类型"),
    log_level: Optional[str] = Query(None, description="日志级别"),
    status: Optional[str] = Query(None, description="状态"),
    user_id: Optional[str] = Query(None, description="用户ID"),
    group_id: Optional[str] = Query(None, description="群组ID"),
    start_time: Optional[str] = Query(None, description="开始时间"),
    end_time: Optional[str] = Query(None, description="结束时间"),
    keyword: Optional[str] = Query(None, description="关键词搜索"),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    获取Telegram日志列表
    """
    try:
        # 检查权限：仅允许超级管理员
        if not current_user.is_superuser:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="仅超级管理员可以查看Telegram日志"
            )
        
        # 从数据库查询真实的日志数据
        query = db.query(TelegramBotLog)

        # 时间范围过滤
        if start_time and end_time:
            try:
                start_date = datetime.fromisoformat(start_time.replace('Z', '+00:00'))
                end_date = datetime.fromisoformat(end_time.replace('Z', '+00:00'))
                query = query.filter(
                    TelegramBotLog.created_at >= start_date,
                    TelegramBotLog.created_at <= end_date
                )
            except ValueError:
                pass  # 忽略无效的时间格式

        # 用户ID过滤
        if user_id:
            query = query.filter(TelegramBotLog.user_id == user_id)

        # 群组ID过滤
        if group_id:
            query = query.filter(TelegramBotLog.chat_id == group_id)

        # 状态过滤
        if status:
            query = query.filter(TelegramBotLog.status == status)

        # 日志类型过滤
        if log_type:
            if log_type == "command":
                query = query.filter(TelegramBotLog.command.isnot(None))
            elif log_type == "operation":
                query = query.filter(TelegramBotLog.command.is_(None))

        # 日志级别过滤
        if log_level:
            if log_level == "error":
                query = query.filter(TelegramBotLog.status == LogStatus.ERROR.value)
            elif log_level == "info":
                query = query.filter(TelegramBotLog.status != LogStatus.ERROR.value)

        # 关键词搜索
        if keyword:
            query = query.filter(
                or_(
                    TelegramBotLog.command.contains(keyword),
                    TelegramBotLog.error_message.contains(keyword)
                )
            )

        # 权限过滤：非超级管理员只能看到自己有权限的商户数据
        if not current_user.is_superuser:
            accessible_merchant_ids = current_user.get_accessible_merchant_ids()
            if accessible_merchant_ids:
                # 通过群组关联过滤
                query = query.join(TelegramGroup, TelegramBotLog.chat_id == TelegramGroup.chat_id).filter(
                    TelegramGroup.merchant_id.in_(accessible_merchant_ids)
                )

        # 获取总数
        total = query.count()

        # 分页查询
        offset = (page - 1) * page_size
        bot_logs = query.order_by(TelegramBotLog.created_at.desc()).offset(offset).limit(page_size).all()

        # 格式化日志数据
        logs = []
        for log in bot_logs:
            # 从response_data中提取消息
            response_message = ""
            if log.response_data and isinstance(log.response_data, dict):
                response_message = log.response_data.get("message", "")

            logs.append({
                "id": log.id,
                "timestamp": log.created_at.isoformat() if log.created_at else None,
                "log_type": "command" if log.command else "operation",
                "log_level": "error" if log.status == LogStatus.ERROR.value else "info",
                "status": log.status,
                "user_id": str(log.user_id) if log.user_id else None,
                "group_id": str(log.chat_id) if log.chat_id else None,
                "operation": log.command or "unknown",
                "message": response_message or log.error_message or "无消息",
                "details": {
                    "command": log.command,
                    "request_data": log.request_data,
                    "response_data": log.response_data,
                    "error_message": log.error_message,
                    "execution_time": log.execution_time
                }
            })

        
        return BaseResponse(
            success=True,
            data={
                "items": logs,
                "total": total,
                "page": page,
                "page_size": page_size,
                "pages": (total + page_size - 1) // page_size
            },
            message="获取日志列表成功"
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取日志列表失败: {e}")
        raise HTTPException(status_code=500, detail="获取日志列表失败")


@router.get("/logs/operations")
async def get_operation_logs(
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(20, ge=1, le=100, description="每页数量"),
    operation: Optional[str] = Query(None, description="操作类型"),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    获取操作日志
    """
    try:
        # 检查权限：仅允许超级管理员
        if not current_user.is_superuser:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="仅超级管理员可以查看操作日志"
            )
        
        # 查询操作日志（非命令类型的日志）
        query = db.query(TelegramBotLog).filter(TelegramBotLog.command.is_(None))

        # 操作类型过滤
        if operation:
            query = query.filter(TelegramBotLog.command.contains(operation))

        # 权限过滤
        if not current_user.is_superuser:
            accessible_merchant_ids = current_user.get_accessible_merchant_ids()
            if accessible_merchant_ids:
                query = query.join(TelegramGroup, TelegramBotLog.chat_id == TelegramGroup.chat_id).filter(
                    TelegramGroup.merchant_id.in_(accessible_merchant_ids)
                )

        # 获取总数
        total = query.count()

        # 分页查询
        offset = (page - 1) * page_size
        bot_logs = query.order_by(TelegramBotLog.created_at.desc()).offset(offset).limit(page_size).all()

        # 格式化日志数据
        logs = []
        for log in bot_logs:
            # 从response_data中提取消息
            response_message = ""
            if log.response_data and isinstance(log.response_data, dict):
                response_message = log.response_data.get("message", "")

            logs.append({
                "id": log.id,
                "timestamp": log.created_at.isoformat() if log.created_at else None,
                "operation": "operation",
                "user_id": str(log.user_id) if log.user_id else None,
                "status": log.status,
                "message": response_message or log.error_message or "无消息",
                "details": {
                    "request_data": log.request_data,
                    "response_data": log.response_data,
                    "error_message": log.error_message,
                    "execution_time": log.execution_time
                }
            })
        
        return BaseResponse(
            success=True,
            data={
                "items": logs,
                "total": total,
                "page": page,
                "page_size": page_size
            },
            message="获取操作日志成功"
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取操作日志失败: {e}")
        raise HTTPException(status_code=500, detail="获取操作日志失败")


@router.get("/logs/errors")
async def get_error_logs(
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(20, ge=1, le=100, description="每页数量"),
    error_type: Optional[str] = Query(None, description="错误类型"),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    获取错误日志
    """
    try:
        # 检查权限：仅允许超级管理员
        if not current_user.is_superuser:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="仅超级管理员可以查看错误日志"
            )
        
        # 查询错误日志
        query = db.query(TelegramBotLog).filter(TelegramBotLog.status == LogStatus.ERROR.value)

        # 错误类型过滤
        if error_type:
            query = query.filter(TelegramBotLog.error_message.contains(error_type))

        # 权限过滤
        if not current_user.is_superuser:
            accessible_merchant_ids = current_user.get_accessible_merchant_ids()
            if accessible_merchant_ids:
                query = query.join(TelegramGroup, TelegramBotLog.chat_id == TelegramGroup.chat_id).filter(
                    TelegramGroup.merchant_id.in_(accessible_merchant_ids)
                )

        # 获取总数
        total = query.count()

        # 分页查询
        offset = (page - 1) * page_size
        bot_logs = query.order_by(TelegramBotLog.created_at.desc()).offset(offset).limit(page_size).all()

        # 格式化日志数据
        logs = []
        for log in bot_logs:
            logs.append({
                "id": log.id,
                "timestamp": log.created_at.isoformat() if log.created_at else None,
                "error_type": "SYSTEM_ERROR",
                "user_id": str(log.user_id) if log.user_id else None,
                "message": log.error_message or "未知错误",
                "stack_trace": log.error_message or "",
                "details": {
                    "command": log.command,
                    "request_data": log.request_data,
                    "response_data": log.response_data,
                    "error_message": log.error_message,
                    "execution_time": log.execution_time
                }
            })
        
        return BaseResponse(
            success=True,
            data={
                "items": logs,
                "total": total,
                "page": page,
                "page_size": page_size
            },
            message="获取错误日志成功"
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取错误日志失败: {e}")
        raise HTTPException(status_code=500, detail="获取错误日志失败")


@router.post("/logs/clear")
async def clear_logs(
    log_type: Optional[str] = Query(None, description="要清理的日志类型"),
    days_to_keep: int = Query(30, ge=1, description="保留天数"),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    清理日志
    """
    try:
        # 检查权限：仅允许超级管理员
        if not current_user.is_superuser:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="仅超级管理员可以清理日志"
            )
        
        # 实现真实的日志清理逻辑
        logger.info(f"用户 {current_user.username} 请求清理日志，类型: {log_type}, 保留天数: {days_to_keep}")

        # 计算清理的截止时间
        cutoff_date = datetime.now() - timedelta(days=days_to_keep)

        # 构建清理查询
        query = db.query(TelegramBotLog).filter(TelegramBotLog.created_at < cutoff_date)

        # 根据日志类型过滤
        if log_type:
            if log_type == "command":
                query = query.filter(TelegramBotLog.command.isnot(None))
            elif log_type == "operation":
                query = query.filter(TelegramBotLog.command.is_(None))
            elif log_type == "error":
                query = query.filter(TelegramBotLog.status == LogStatus.ERROR.value)

        # 获取要清理的日志数量
        cleared_count = query.count()

        # 执行清理
        query.delete(synchronize_session=False)
        db.commit()

        logger.info(f"清理了 {cleared_count} 条日志记录")

        return BaseResponse(
            success=True,
            data={"cleared_count": cleared_count},
            message=f"日志清理成功，共清理 {cleared_count} 条记录"
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"清理日志失败: {e}")
        raise HTTPException(status_code=500, detail="清理日志失败")
