"""
绑卡记录服务模块 - 提供绑卡记录管理的核心功能
"""

from typing import List, Optional, Dict, Any
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_, func, desc, case
import logging
from datetime import datetime, timedelta
import uuid

from app.services.base_service import BaseService
from app.services.security_service import SecurityService
from app.models.card_record import CardRecord
from app.models.binding_log import BindingLog
from app.models.user import User
from app.models.merchant import Merchant
from app.models.department import Department
from app.models.walmart_ck import WalmartCK
from app.schemas.card_record import CardRecordCreate, CardRecordUpdate

logger = logging.getLogger(__name__)


class CardRecordService(BaseService[CardRecord, CardRecordCreate, CardRecordUpdate]):
    """绑卡记录服务类"""

    def __init__(self, db: Session):
        super().__init__(CardRecord, db)
        self.security_service = SecurityService(db)

    def create_card_record(
        self,
        card_in: CardRecordCreate,
        current_user: User
    ) -> Optional[CardRecord]:
        """
        创建绑卡记录

        Args:
            card_in: 绑卡记录创建数据
            current_user: 当前操作用户

        Returns:
            Optional[CardRecord]: 创建的绑卡记录对象或None
        """
        try:
            # 权限检查：超级管理员或商户用户
            self._check_merchant_permission(current_user, card_in.merchant_id)

            # 验证商户和部门
            self._validate_merchant_and_department(card_in)

            # 检查卡号是否已存在
            existing_card = self.db.query(CardRecord).filter(
                CardRecord.card_number == card_in.card_number,
                CardRecord.merchant_id == card_in.merchant_id
            ).first()
            if existing_card:
                raise ValueError(f"卡号 {card_in.card_number} 在该商户下已存在")

            # 创建绑卡记录
            card_data = card_in.dict()
            card_data.update({
                'id': str(uuid.uuid4()),
                'created_by': current_user.id,
                'status': 'pending'
            })

            db_card = CardRecord(**card_data)
            self.db.add(db_card)
            self.db.commit()
            self.db.refresh(db_card)
            return db_card
        except Exception as e:
            self.db.rollback()
            self.logger.error(f"创建绑卡记录失败: {e}")
            raise

    def batch_create_cards(
        self,
        cards_data: List[Dict[str, Any]],
        merchant_id: int,
        department_id: Optional[int],
        current_user: User
    ) -> Dict[str, Any]:
        """批量创建绑卡记录"""
        try:
            self._check_merchant_permission(current_user, merchant_id)

            success_count = 0
            fail_count = 0
            errors = []
            created_cards = []

            for i, card_data in enumerate(cards_data):
                try:
                    card_record = self._create_single_card_record(
                        card_data, merchant_id, department_id, current_user
                    )
                    self.db.add(card_record)
                    created_cards.append(card_record)
                    success_count += 1
                except Exception as e:
                    fail_count += 1
                    errors.append(f"第{i+1}行: {str(e)}")

            if created_cards:
                self.db.commit()
                for card in created_cards:
                    self.db.refresh(card)

            return {
                'success_count': success_count,
                'fail_count': fail_count,
                'errors': errors,
                'created_cards': [card.to_dict() for card in created_cards]
            }
        except Exception as e:
            self.db.rollback()
            self.logger.error(f"批量创建绑卡记录失败: {e}")
            raise

    async def bind_card(
        self,
        card_id: str,
        walmart_ck_id: int,
        current_user: User
    ) -> Dict[str, Any]:
        """
        执行绑卡操作

        Args:
            card_id: 卡记录ID
            walmart_ck_id: 沃尔玛CK ID
            current_user: 当前操作用户

        Returns:
            Dict[str, Any]: 绑卡结果

        Raises:
            ValueError: 当参数验证失败时
        """
        try:
            # 严格的参数验证
            if not card_id or not isinstance(card_id, str) or not card_id.strip():
                self.logger.error("绑卡失败: 卡记录ID参数无效 - 卡记录ID不能为空、None或空字符串")
                raise ValueError("卡记录ID不能为空、None或空字符串")

            if not isinstance(walmart_ck_id, int) or walmart_ck_id <= 0:
                self.logger.error("绑卡失败: 沃尔玛CK ID参数无效 - 沃尔玛CK ID必须是正整数")
                raise ValueError("沃尔玛CK ID必须是正整数")

            if not current_user:
                self.logger.error("绑卡失败: 当前用户参数无效 - 当前用户不能为空")
                raise ValueError("当前用户不能为空")

            # 清理参数
            card_id = card_id.strip()

            self.logger.info(f"开始绑卡操作（参数验证通过）: card_id={card_id}, walmart_ck_id={walmart_ck_id}, user_id={current_user.id}")

            # 验证卡记录和CK
            card = self._validate_card_and_ck(card_id, walmart_ck_id, current_user)

            # 更新卡状态为绑定中
            card.status = 'binding'
            # 注意：walmart_ck_id 和 department_id 将在绑卡成功后填入
            # 这里暂时不设置，等待绑卡成功后在 _update_bind_result 中设置
            card.bind_started_at = datetime.utcnow()
            self.db.commit()

            # 创建绑定日志
            binding_log = BindingLog(
                id=str(uuid.uuid4()),
                card_record_id=card_id,
                walmart_ck_id=walmart_ck_id,
                merchant_id=card.merchant_id,
                department_id=card.department_id,
                status='started',
                created_by=current_user.id
            )
            self.db.add(binding_log)
            self.db.commit()

            # TODO: 这里应该调用实际的绑卡API
            # 模拟绑卡结果
            bind_success = True  # 实际应该从API获取结果

            # 更新绑卡结果
            await self._update_bind_result(card, binding_log, walmart_ck_id, bind_success)

            self.db.commit()

            return {
                'success': bind_success,
                'card_id': card_id,
                'status': card.status,
                'message': '绑卡成功' if bind_success else '绑卡失败'
            }

        except Exception as e:
            self.db.rollback()
            self.logger.error(f"绑卡操作失败: {e}")
            raise

    async def batch_bind_cards(
        self,
        card_ids: List[str],
        current_user: User
    ) -> Dict[str, Any]:
        """批量绑卡 - 优化版本，避免N+1查询

        Args:
            card_ids: 卡记录ID列表
            current_user: 当前操作用户

        Returns:
            Dict[str, Any]: 批量绑卡结果

        Raises:
            ValueError: 当参数验证失败时
        """
        try:
            # 严格的参数验证
            if not isinstance(card_ids, list):
                self.logger.error("批量绑卡失败: 卡记录ID列表参数无效 - 必须是列表类型")
                raise ValueError("卡记录ID列表必须是列表类型")

            if not current_user:
                self.logger.error("批量绑卡失败: 当前用户参数无效 - 当前用户不能为空")
                raise ValueError("当前用户不能为空")

            # 验证列表中的每个ID
            if card_ids:
                for i, card_id in enumerate(card_ids):
                    if not card_id or not isinstance(card_id, str) or not card_id.strip():
                        self.logger.error(f"批量绑卡失败: 第{i+1}个卡记录ID参数无效 - 卡记录ID不能为空、None或空字符串")
                        raise ValueError(f"第{i+1}个卡记录ID不能为空、None或空字符串")

                # 清理参数（去除首尾空格）
                card_ids = [card_id.strip() for card_id in card_ids]

                # 去重
                original_count = len(card_ids)
                card_ids = list(set(card_ids))
                if len(card_ids) != original_count:
                    self.logger.warning(f"批量绑卡: 发现重复的卡记录ID，已去重 - 原始数量: {original_count}, 去重后数量: {len(card_ids)}")

            if not card_ids:
                self.logger.info("批量绑卡: 卡记录ID列表为空，返回空结果")
                return {
                    'success_count': 0,
                    'fail_count': 0,
                    'total_count': 0,
                    'results': []
                }

            self.logger.info(f"开始批量绑卡操作（参数验证通过）: 卡数量={len(card_ids)}, user_id={current_user.id}")

            # 批量获取卡记录，避免N+1查询
            cards = self._batch_get_cards_with_isolation(card_ids, current_user)

            # 批量获取可用CK，避免重复查询
            merchant_ids = list(set(card.merchant_id for card in cards))
            available_cks = self._batch_get_available_cks(merchant_ids)

            success_count = 0
            fail_count = 0
            results = []

            for card in cards:
                try:
                    # 从预加载的CK中选择
                    available_ck = available_cks.get(card.merchant_id)
                    if not available_ck:
                        results.append({
                            'card_id': card.id,
                            'success': False,
                            'message': '没有可用的CK'
                        })
                        fail_count += 1
                        continue

                    result = await self.bind_card(card.id, available_ck.id, current_user)
                    results.append(result)

                    if result.get('success'):
                        success_count += 1
                    else:
                        fail_count += 1

                except Exception as e:
                    results.append({
                        'card_id': card.id,
                        'success': False,
                        'message': str(e)
                    })
                    fail_count += 1

            # 处理不存在的卡ID
            existing_card_ids = {card.id for card in cards}
            for card_id in card_ids:
                if card_id not in existing_card_ids:
                    results.append({
                        'card_id': card_id,
                        'success': False,
                        'message': '卡记录不存在或无权限访问'
                    })
                    fail_count += 1

            return {
                'success_count': success_count,
                'fail_count': fail_count,
                'total_count': len(card_ids),
                'results': results
            }

        except Exception as e:
            self.logger.error(f"批量绑卡失败: {e}")
            raise

    def get_card_statistics(
        self,
        merchant_id: int,
        current_user: User,
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None
    ) -> Dict[str, Any]:
        """获取绑卡统计信息"""
        try:
            self._check_merchant_permission(current_user, merchant_id)

            query = self.db.query(CardRecord).filter(CardRecord.merchant_id == merchant_id)

            # 应用时间过滤
            if start_date:
                query = query.filter(CardRecord.created_at >= start_date)
            if end_date:
                query = query.filter(CardRecord.created_at <= end_date)

            # 统计数据
            stats = self._calculate_statistics(query)
            today_stats = self._calculate_today_statistics(query)

            return {
                'merchant_id': merchant_id,
                **stats,
                **today_stats
            }
        except Exception as e:
            self.logger.error(f"获取绑卡统计信息失败: {e}")
            raise

    def get_card_statistics_auto(
        self,
        current_user: User,
        page: int = 1,
        page_size: int = 20,
        card_number: Optional[str] = None,
        status_filter: Optional[str] = None,
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None,
        merchant_id: Optional[int] = None,
    ) -> Dict[str, Any]:
        """
        获取绑卡统计信息（自动权限判断）

        根据用户权限自动确定数据范围：
        - 超级管理员：可查看所有商户统计（可通过merchant_id参数指定特定商户）
        - 商户管理员：只能查看自己商户的统计
        - 商户CK供应商：只能查看自己部门的统计

        Args:
            current_user: 当前用户
            page: 页码
            page_size: 每页数量
            card_number: 卡号搜索
            status_filter: 状态过滤
            start_date: 开始日期
            end_date: 结束日期
            merchant_id: 商户ID（超级管理员可指定）

        Returns:
            Dict[str, Any]: 统计信息和分页数据
        """
        try:
            # 构建基础查询
            query = self.db.query(CardRecord)

            # 根据用户权限应用数据隔离
            if current_user.is_superuser:
                # 超级管理员：可查看所有数据或指定商户数据
                if merchant_id:
                    query = query.filter(CardRecord.merchant_id == merchant_id)
            else:
                # 非超级管理员：应用数据隔离
                query = self.apply_data_isolation(query, current_user)

            # 应用搜索过滤条件
            if card_number:
                query = query.filter(CardRecord.card_number.like(f"%{card_number}%"))

            if status_filter:
                query = query.filter(CardRecord.status == status_filter)

            if start_date:
                query = query.filter(CardRecord.created_at >= start_date)

            if end_date:
                query = query.filter(CardRecord.created_at <= end_date)

            # 计算统计数据
            stats = self._calculate_statistics(query)
            today_stats = self._calculate_today_statistics(query)

            # 获取分页数据
            total = query.count()

            # 获取分页数据（即使没有数据也返回空列表）
            items = query.order_by(desc(CardRecord.created_at)).offset((page - 1) * page_size).limit(page_size).all()

            # 转换为字典格式
            items_data = []
            for item in items:
                item_dict = item.to_dict()
                items_data.append(item_dict)

            return {
                # 统计数据
                **stats,
                **today_stats,
                # 分页数据
                # 'items': items_data,
                'total': total,
                'page': page,
                'page_size': page_size,
                'total_pages': (total + page_size - 1) // page_size,
                # 权限信息
                'current_user_merchant_id': current_user.merchant_id,
                'current_user_department_id': current_user.department_id,
                'is_superuser': current_user.is_superuser,
            }
        except Exception as e:
            self.logger.error(f"获取绑卡统计信息失败: {e}")
            raise

    def search_card_records(
        self,
        search_term: str,
        current_user: User,
        skip: int = 0,
        limit: int = 100,
        status: Optional[str] = None
    ) -> List[CardRecord]:
        """
        搜索绑卡记录

        Args:
            search_term: 搜索关键词
            current_user: 当前用户
            skip: 跳过的记录数
            limit: 限制返回的记录数
            status: 状态过滤

        Returns:
            List[CardRecord]: 绑卡记录列表
        """
        try:
            query = self.db.query(CardRecord)

            # 应用数据隔离
            query = self.apply_data_isolation(query, current_user)

            # 状态过滤
            if status:
                query = query.filter(CardRecord.status == status)

            # 搜索条件
            if search_term:
                query = query.filter(or_(
                    CardRecord.card_number.like(f"%{search_term}%"),
                    CardRecord.card_holder.like(f"%{search_term}%"),
                    CardRecord.remark.like(f"%{search_term}%")
                ))

            return query.order_by(desc(CardRecord.created_at)).offset(skip).limit(limit).all()
        except Exception as e:
            self.logger.error(f"搜索绑卡记录失败: {e}")
            return []

    async def retry_failed_cards(
        self,
        merchant_id: int,
        current_user: User,
        limit: int = 10
    ) -> Dict[str, Any]:
        """
        重试失败的绑卡记录

        Args:
            merchant_id: 商户ID
            current_user: 当前用户
            limit: 重试数量限制

        Returns:
            Dict[str, Any]: 重试结果
        """
        try:
            # 权限检查
            self._check_merchant_permission(current_user, merchant_id)

            # 获取失败的卡记录
            failed_cards = self.db.query(CardRecord).filter(
                CardRecord.merchant_id == merchant_id,
                CardRecord.status == 'failed'
            ).limit(limit).all()

            if not failed_cards:
                return {
                    'success_count': 0,
                    'fail_count': 0,
                    'message': '没有需要重试的失败记录'
                }

            # 重置状态并重新绑卡
            card_ids = self._reset_failed_cards(failed_cards)
            return await self.batch_bind_cards(card_ids, current_user)

        except Exception as e:
            self.db.rollback()
            self.logger.error(f"重试失败绑卡记录失败: {e}")
            raise

    def _check_merchant_permission(self, current_user: User, merchant_id: int):
        """检查商户权限"""
        if not current_user.is_superuser and current_user.merchant_id != merchant_id:
            raise ValueError("无权限操作该商户的数据")

    def _validate_merchant_and_department(self, card_in: CardRecordCreate):
        """验证商户和部门"""
        # 检查商户是否存在
        merchant = self.db.query(Merchant).filter(Merchant.id == card_in.merchant_id).first()
        if not merchant:
            raise ValueError("商户不存在")

        # 检查部门是否存在且属于同一商户
        if card_in.department_id:
            department = self.db.query(Department).filter(
                Department.id == card_in.department_id,
                Department.merchant_id == card_in.merchant_id
            ).first()
            if not department:
                raise ValueError("部门不存在或不属于该商户")

    def _validate_card_and_ck(self, card_id: str, walmart_ck_id: int, current_user: User) -> CardRecord:
        """验证卡记录和CK - 强化商户隔离验证"""
        # 获取卡记录
        card = self.get_with_isolation(card_id, current_user)
        if not card:
            raise ValueError("卡记录不存在或无权限访问")

        # 检查卡状态
        if card.status != 'pending':
            raise ValueError(f"卡状态为 {card.status}，无法执行绑卡操作")

        # 严格验证CK与商户的隔离关系
        from app.services.walmart_ck_service_new import WalmartCKService
        ck_service = WalmartCKService(self.db)

        # 使用专门的验证方法确保CK属于该商户
        if not ck_service.validate_ck_merchant_isolation(walmart_ck_id, card.merchant_id):
            raise ValueError(f"严重安全违规：CK {walmart_ck_id} 不属于商户 {card.merchant_id}")

        # 获取CK并验证可用性（过滤已删除的CK）
        walmart_ck = self.db.query(WalmartCK).filter(
            WalmartCK.id == walmart_ck_id,
            WalmartCK.merchant_id == card.merchant_id,  # 双重验证
            WalmartCK.is_deleted == False  # 过滤已删除的CK
        ).first()
        if not walmart_ck:
            raise ValueError("CK不存在、不属于该商户或已被删除")

        # 检查CK是否可用
        if not walmart_ck.active:
            raise ValueError("CK已被禁用")

        return card

    async def _update_bind_result(self, card: CardRecord, binding_log: BindingLog, walmart_ck_id: int, bind_success: bool):
        """更新绑卡结果 - 使用原子性绑卡服务确保数据一致性"""
        from app.services.atomic_binding_service import SafeBindingWrapper

        # 使用原子性绑卡包装器
        safe_wrapper = SafeBindingWrapper(self.db)

        # 准备API结果数据
        api_result = {
            'walmart_ck_id': walmart_ck_id,
            'bind_success': bind_success,
            'timestamp': datetime.utcnow().isoformat()
        }

        # 使用原子性操作更新绑卡结果
        success = await safe_wrapper.safe_update_bind_result(
            record_id=str(card.id),
            merchant_id=card.merchant_id,
            walmart_ck_id=walmart_ck_id,
            bind_success=bind_success,
            api_result=api_result
        )

        if not success:
            self.logger.error(f"原子性绑卡结果更新失败: card_id={card.id}, walmart_ck_id={walmart_ck_id}")
            raise Exception("绑卡结果更新失败，数据一致性保护")

        # 更新绑定日志状态
        if bind_success:
            binding_log.status = 'success'
            binding_log.completed_at = datetime.utcnow()
        else:
            binding_log.status = 'failed'
            binding_log.completed_at = datetime.utcnow()
            binding_log.error_message = '绑卡失败'

        self.logger.info(f"原子性绑卡结果更新成功: card_id={card.id}, success={bind_success}")

    def _reset_failed_cards(self, failed_cards: List[CardRecord]) -> List[str]:
        """重置失败卡片状态"""
        card_ids = []
        for card in failed_cards:
            card.status = 'pending'
            card.error_message = None
            card.bind_result = None
            card_ids.append(card.id)
        self.db.commit()
        return card_ids

    def _create_single_card_record(self, card_data: Dict[str, Any], merchant_id: int,
                                   department_id: Optional[int], current_user: User) -> CardRecord:
        """创建单个卡记录"""
        card_create_data = {
            'card_number': card_data.get('card_number'),
            'card_holder': card_data.get('card_holder'),
            'expiry_date': card_data.get('expiry_date'),
            'cvv': card_data.get('cvv'),
            'merchant_id': merchant_id,
            'department_id': department_id,
            'remark': card_data.get('remark', '')
        }
        return CardRecord(
            id=str(uuid.uuid4()),
            created_by=current_user.id,
            status='pending',
            **card_create_data
        )

    async def _process_single_bind(self, card_id: str, current_user: User) -> Dict[str, Any]:
        """处理单个绑卡 - 强化商户隔离验证"""
        card = self.get_with_isolation(card_id, current_user)
        if not card:
            return {
                'card_id': card_id,
                'success': False,
                'message': '卡记录不存在或无权限访问'
            }

        # 使用简化CK服务
        from app.services.simplified_ck_service import SimplifiedCKService
        ck_service = SimplifiedCKService(self.db)

        # 严格按商户ID获取可用CK，确保商户隔离（包含CK有效性验证）
        available_ck = await ck_service.get_available_ck(card.merchant_id, card.department_id)

        if not available_ck:
            return {
                'card_id': card_id,
                'success': False,
                'message': f'商户{card.merchant_id}没有可用的CK'
            }

        # 再次验证CK与商户的关联关系（双重保险）
        if not ck_service.validate_ck_merchant_isolation(available_ck.id, card.merchant_id):
            return {
                'card_id': card_id,
                'success': False,
                'message': f'CK隔离验证失败：CK {available_ck.id} 不属于商户 {card.merchant_id}'
            }

        return await self.bind_card(card_id, available_ck.id, current_user)

    def _batch_get_cards_with_isolation(self, card_ids: List[str], current_user: User) -> List[CardRecord]:
        """批量获取卡记录（应用数据隔离）- 避免N+1查询"""
        query = self.db.query(CardRecord).filter(CardRecord.id.in_(card_ids))
        query = self.apply_data_isolation(query, current_user)
        return query.all()

    def _batch_get_available_cks(self, merchant_ids: List[int]) -> Dict[int, Any]:
        """批量获取可用CK - 避免重复查询"""
        from app.services.walmart_ck_service_new import WalmartCKService
        ck_service = WalmartCKService(self.db)

        available_cks = {}
        for merchant_id in merchant_ids:
            ck = ck_service.get_available_ck(merchant_id)
            if ck:
                available_cks[merchant_id] = ck

        return available_cks

    def _calculate_statistics(self, query) -> Dict[str, Any]:
        """计算统计数据 - 优化版本，使用单次聚合查询避免N+1查询，包含金额统计"""
        # 使用单次聚合查询获取所有统计信息，包括金额统计
        stats = query.with_entities(
            func.count(CardRecord.id).label('total_count'),
            func.sum(case((CardRecord.status == 'success', 1), else_=0)).label('success_count'),
            func.sum(case((CardRecord.status == 'failed', 1), else_=0)).label('failed_count'),
            func.sum(case((CardRecord.status == 'pending', 1), else_=0)).label('pending_count'),
            func.sum(case((CardRecord.status == 'binding', 1), else_=0)).label('binding_count'),
            # 金额统计：请求金额总计（所有记录的amount字段）
            func.sum(CardRecord.amount).label('total_request_amount'),
            # 实际绑卡金额总计（成功记录的actual_amount字段）
            func.sum(case((CardRecord.status == 'success', CardRecord.actual_amount), else_=0)).label('total_actual_amount'),
            # 成功绑卡的请求金额总计（成功记录的amount字段）
            func.sum(case((CardRecord.status == 'success', CardRecord.amount), else_=0)).label('success_request_amount'),
        ).first()

        total_count = stats.total_count or 0
        success_count = stats.success_count or 0

        return {
            'total_count': total_count,
            'success_count': success_count,
            'failed_count': stats.failed_count or 0,
            'pending_count': stats.pending_count or 0,
            'binding_count': stats.binding_count or 0,
            'success_rate': round(success_count / total_count * 100, 2) if total_count > 0 else 0,
            # 金额统计（单位：分）
            'total_request_amount': stats.total_request_amount or 0,
            'total_actual_amount': stats.total_actual_amount or 0,
            'success_request_amount': stats.success_request_amount or 0,
        }

    def _calculate_today_statistics(self, query) -> Dict[str, Any]:
        """计算今日统计数据 - 优化版本，使用单次聚合查询避免N+1查询，包含金额统计"""
        # 修复时区问题：使用上海时区的当前日期，而不是UTC时间
        from app.utils.time_utils import get_current_time
        today = get_current_time().date()

        # 使用单次聚合查询获取今日统计，包括金额统计
        today_stats = query.filter(func.date(CardRecord.created_at) == today).with_entities(
            func.count(CardRecord.id).label('today_total'),
            func.sum(case((CardRecord.status == 'success', 1), else_=0)).label('today_success'),
            # 今日金额统计：请求金额总计
            func.sum(CardRecord.amount).label('today_request_amount'),
            # 今日实际绑卡金额总计（成功记录的actual_amount字段）
            func.sum(case((CardRecord.status == 'success', CardRecord.actual_amount), else_=0)).label('today_actual_amount'),
            # 今日成功绑卡的请求金额总计
            func.sum(case((CardRecord.status == 'success', CardRecord.amount), else_=0)).label('today_success_request_amount'),
        ).first()

        today_total = today_stats.today_total or 0
        today_success = today_stats.today_success or 0

        return {
            'today_total': today_total,
            'today_success': today_success,
            'today_success_rate': round(today_success / today_total * 100, 2) if today_total > 0 else 0,
            # 今日金额统计（单位：分）
            'today_request_amount': today_stats.today_request_amount or 0,
            'today_actual_amount': today_stats.today_actual_amount or 0,
            'today_success_request_amount': today_stats.today_success_request_amount or 0,
        }

    async def retry_single_card(
        self,
        card_id: str,
        current_user: User
    ) -> Dict[str, Any]:
        """
        重试单个失败的绑卡记录

        Args:
            card_id: 绑卡记录ID
            current_user: 当前用户

        Returns:
            Dict[str, Any]: 重试结果

        Raises:
            ValueError: 如果记录不存在、无权限访问或不允许重试
        """
        try:
            # 获取卡记录（应用数据隔离）
            card = self.get_with_isolation(card_id, current_user)
            if not card:
                raise ValueError("绑卡记录不存在或无权限访问")

            # 检查卡状态是否允许重试（支持failed和pending状态）
            if card.status not in ['failed', 'pending']:
                raise ValueError(f"只能重试失败或待处理状态的记录，当前状态：{card.status}")

            # 对于failed状态，检查是否允许重试（基于错误信息判断是否为CK失效）
            # 对于pending状态，直接允许重试（可能是长时间未处理的记录）
            if card.status == 'failed':
                if not self._is_retryable_failure(card):
                    raise ValueError("该失败记录不允许重试，可能是由于非CK失效原因导致的失败")

                # 特殊检查：如果是CK配置相关错误，需要更严格的检查
                if self._is_ck_config_error(card):
                    self.logger.info(f"检测到CK配置相关错误，进行严格的CK可用性检查")
                    # 对于CK配置错误，必须确保有可用CK才允许重试
                    pass  # CK检查逻辑在下面执行

            # 检查商户是否有可用的CK（提前检查，避免无意义的重试）
            available_ck_count = await self._check_merchant_available_ck_count(card.merchant_id)
            if available_ck_count == 0:
                self.logger.warning(f"商户 {card.merchant_id} 没有可用的CK，重试将会失败")
                # 对于明确的CK配置问题，建议阻止重试
                raise ValueError(f"商户当前没有可用的CK配置，请先配置有效的CK后再重试")

            # 记录原始状态用于日志
            original_status = card.status

            # 重置卡状态为pending，准备重新绑卡
            card.status = 'pending'
            card.error_message = None
            card.retry_count += 1
            card.updated_at = datetime.utcnow()

            # 清除之前的绑卡结果，重置为初始状态
            # 这些字段将在重新绑卡成功后填入新的值
            card.department_id = None  # 重置为NULL，等待重新绑卡时填入
            card.walmart_ck_id = None  # 重置为NULL，等待重新绑卡时填入
            card.response_data = None
            card.process_time = None

            self.db.commit()

            # 记录重试日志（区分不同状态的重试）
            self._log_retry_operation(card, current_user, original_status=original_status)

            # 重新提交到绑卡队列
            await self._resubmit_to_queue(card)

            self.logger.info(f"卡记录 {card_id} 重试成功，已重新提交到绑卡队列")

            # 构建更详细的响应消息
            message = f'重试成功，已重新提交到绑卡队列（第{card.retry_count}次重试）'
            if available_ck_count == 0:
                message += '，但该商户当前无可用CK，请检查CK配置'

            return {
                'success': True,
                'card_id': card_id,
                'status': card.status,
                'retry_count': card.retry_count,
                'original_status': original_status,
                'available_ck_count': available_ck_count,
                'message': message
            }

        except ValueError:
            raise
        except Exception as e:
            self.db.rollback()
            self.logger.error(f"重试绑卡记录失败: {e}")
            raise ValueError(f"重试操作失败: {str(e)}")

    async def _check_merchant_available_ck_count(self, merchant_id: int) -> int:
        """
        检查商户可用CK数量

        Args:
            merchant_id: 商户ID

        Returns:
            int: 可用CK数量
        """
        try:
            # 使用简化CK服务
            from app.services.simplified_ck_service import SimplifiedCKService
            from app.models.walmart_ck import WalmartCK

            walmart_ck_service = SimplifiedCKService(self.db)

            # 直接查询该商户的所有CK配置（过滤已删除的CK）
            merchant_cks = self.db.query(WalmartCK).filter(
                WalmartCK.merchant_id == merchant_id,
                WalmartCK.is_deleted == False
            ).all()

            if not merchant_cks:
                self.logger.warning(f"商户 {merchant_id} 没有配置任何CK")
                return 0

            self.logger.info(f"商户 {merchant_id} 共有 {len(merchant_cks)} 个CK配置")

            # 检查是否有可用的CK（使用异步调用）
            available_ck = await walmart_ck_service.get_available_ck(
                merchant_id=merchant_id,
                department_id=None  # 不限制部门
            )

            if available_ck:
                self.logger.info(f"商户 {merchant_id} 有可用CK: {available_ck.sign}")
                # 释放预占用
                await walmart_ck_service.commit_ck_usage(available_ck.id, False)
                return 1
            else:
                self.logger.warning(f"商户 {merchant_id} 虽有CK配置但当前无可用CK")
                return 0

        except Exception as e:
            self.logger.error(f"检查商户 {merchant_id} 可用CK失败: {e}")
            return 0  # 出错时返回0，表示没有可用CK

    def _is_ck_config_error(self, card: CardRecord) -> bool:
        """
        判断是否为CK配置相关错误

        Args:
            card: 卡记录

        Returns:
            bool: 是否为CK配置错误
        """
        if not card.error_message:
            return False

        ck_config_keywords = [
            "没有可用的沃尔玛CK配置",
            "没有可用的CK",
            "CK配置",
            "未找到可用的CK",
            "CK不可用"
        ]

        error_msg = card.error_message.lower()
        for keyword in ck_config_keywords:
            if keyword.lower() in error_msg:
                return True

        return False

    def _is_retryable_failure(self, card: CardRecord) -> bool:
        """
        判断失败记录是否允许重试

        手动重试功能应该没有频率限制，但主要针对CK失效导致的失败

        Args:
            card: 卡记录

        Returns:
            bool: 是否允许重试
        """
        if not card.error_message:
            # 如果没有错误信息，允许重试（可能是系统异常）
            return True

        # 扩展CK失效的关键词检查
        ck_invalid_keywords = [
            "需要登录",
            "请先去登录",
            "登录失效",
            "CK失效",
            "用户未登录",
            "session过期",
            "认证失败",
            "401",
            "403",
            "203",  # 沃尔玛API特定错误码
            "cookie失效",
            "登录状态失效",
            "身份验证失败",
            "未授权",
            "unauthorized"
        ]

        error_msg = card.error_message.lower()
        for keyword in ck_invalid_keywords:
            if keyword.lower() in error_msg:
                return True

        # 检查响应数据中是否有CK失效标识
        if card.response_data and isinstance(card.response_data, dict):
            response_data = card.response_data

            # 检查是否有need_retry_with_new_user标识
            if response_data.get("need_retry_with_new_user"):
                return True

            # 检查错误码
            error_code = response_data.get("error_code") or response_data.get("code")
            if error_code in [203, "203", 401, "401", 403, "403"]:
                return True

        # 对于手动重试，我们放宽限制，允许更多类型的失败进行重试
        # 但排除明确不应该重试的情况
        non_retryable_keywords = [
            "该电子卡已被其他用户",  # 卡已被绑定
            "卡号格式错误",
            "卡密错误",
            "卡片不存在",
            "余额不足",
            "卡片已过期",
            "卡片已冻结"
        ]

        for keyword in non_retryable_keywords:
            if keyword.lower() in error_msg:
                return False

        # 默认允许重试（手动重试没有频率限制）
        return True

    def _log_retry_operation(self, card: CardRecord, current_user: User, original_status: str = None):
        """记录重试操作日志"""
        try:
            # 【安全修复】使用BindingLogService类而不是全局实例
            from app.services.binding_log_service import BindingLogService
            from app.models.binding_log import LogLevel

            # 确定重试类型
            retry_type = "从失败状态重试" if original_status == 'failed' else "从待处理状态重试"

            # 异步记录日志（不阻塞主流程）
            import asyncio

            async def log_async():
                # 【安全修复】创建BindingLogService实例
                binding_log_service = BindingLogService(self.db)
                await binding_log_service.log_system(
                    db=self.db,
                    card_record_id=str(card.id),
                    message=f"手动重试操作 - {retry_type} - 用户: {current_user.username}",
                    log_level=LogLevel.INFO,
                    details={
                        "retry_count": card.retry_count,
                        "operator": current_user.username,
                        "operator_id": current_user.id,
                        "retry_time": datetime.utcnow().isoformat(),
                        "original_status": original_status,
                        "retry_type": retry_type,
                        "previous_error": card.error_message,
                    }
                )

            # 在事件循环中运行
            try:
                loop = asyncio.get_event_loop()
                if loop.is_running():
                    # 如果事件循环正在运行，创建任务
                    asyncio.create_task(log_async())
                else:
                    # 如果没有运行的事件循环，直接运行
                    loop.run_until_complete(log_async())
            except RuntimeError:
                # 如果没有事件循环，创建新的任务
                asyncio.create_task(log_async())

        except Exception as e:
            # 日志记录失败不应该影响主流程
            self.logger.warning(f"记录重试日志失败: {e}")

    async def _resubmit_to_queue(self, card: CardRecord):
        """重新提交到绑卡队列"""
        try:
            from app.utils.queue_producer import send_bind_card_task

            # 构建队列消息
            # 注意：重试操作需要设置 is_recovery=True，这样队列处理器会从数据库解密密码
            message = {
                "record_id": str(card.id),
                "merchant_id": card.merchant_id,
                "trace_id": card.trace_id or str(uuid.uuid4()),
                "is_recovery": True,  # 标记为恢复处理，队列处理器会从数据库获取并解密密码
                "retry_operation": True,  # 标识这是重试操作
                "recovery_attempt": card.retry_count,  # 重试次数
            }

            # 发送到绑卡队列
            await send_bind_card_task(message)

        except Exception as e:
            self.logger.error(f"重新提交到队列失败: {e}")
            raise ValueError(f"重新提交到绑卡队列失败: {str(e)}")

    def apply_data_isolation(self, query, current_user: User):
        """
        重写基类方法，添加强制商户隔离
        应用数据隔离规则 - 支持用户级、部门级、商户级权限，确保绑卡数据的商户隔离

        Args:
            query: SQLAlchemy查询对象
            current_user: 当前用户

        Returns:
            query: 应用隔离规则后的查询对象
        """
        # 【安全修复】：强制商户隔离 - 除超级管理员外，所有用户只能访问自己商户的绑卡数据
        if not current_user.is_superuser:
            if not current_user.merchant_id:
                # 如果用户没有商户ID，返回空结果
                self.logger.warning(f"[SECURITY] 用户 {current_user.id} 没有商户ID，拒绝绑卡数据访问")
                query = query.filter(CardRecord.id == 'impossible_id')  # 强制返回空结果
                return query

            # 强制添加商户过滤，确保无法跨商户访问
            query = query.filter(CardRecord.merchant_id == current_user.merchant_id)
            self.logger.info(f"[SECURITY] 强制商户隔离：用户 {current_user.id} 只能访问商户 {current_user.merchant_id} 的绑卡数据")

        # 然后应用基类的数据隔离逻辑
        return super().apply_data_isolation(query, current_user)

    def get_with_security_check(self, card_id: str, current_user: User) -> Optional[CardRecord]:
        """
        获取绑卡记录（带安全检查）

        Args:
            card_id: 绑卡记录ID
            current_user: 当前用户

        Returns:
            Optional[CardRecord]: 绑卡记录对象或None
        """
        try:
            # 使用安全服务验证访问权限
            if not self.security_service.validate_card_access(current_user, card_id, "read"):
                return None

            # 记录数据访问
            card = self.get_with_isolation(card_id, current_user)
            if card:
                self.security_service.log_data_access(
                    current_user,
                    "read",
                    "card_record",
                    card_id,
                    card.merchant_id
                )

            return card

        except Exception as e:
            self.logger.error(f"安全检查获取绑卡记录失败: {e}")
            return None
