package services

import (
	"context"
	"fmt"
	"time"

	"walmart-bind-card-processor/internal/database"

	"github.com/go-redis/redis/v8"
	"go.uber.org/zap"
)

// ConnectionMonitor 连接池监控服务
type ConnectionMonitor struct {
	redis  *redis.Client
	logger *zap.Logger
	
	// 监控配置
	checkInterval time.Duration
	alertThresholds ConnectionAlertThresholds
}

// ConnectionAlertThresholds 连接池告警阈值
type ConnectionAlertThresholds struct {
	DBUsageRate      float64 // 数据库连接池使用率告警阈值
	DBWaitCount      int64   // 数据库连接等待数告警阈值
	RedisUsageRate   float64 // Redis连接池使用率告警阈值
	ConsecutiveAlerts int    // 连续告警次数阈值
}

// ConnectionStats 连接统计信息
type ConnectionStats struct {
	Timestamp time.Time `json:"timestamp"`
	
	// 数据库连接池统计
	DB struct {
		MaxOpenConns   int     `json:"max_open_conns"`
		OpenConns      int     `json:"open_conns"`
		InUse          int     `json:"in_use"`
		Idle           int     `json:"idle"`
		UsageRate      float64 `json:"usage_rate"`
		WaitCount      int64   `json:"wait_count"`
		WaitDuration   string  `json:"wait_duration"`
		IsHighUsage    bool    `json:"is_high_usage"`
		IsWaiting      bool    `json:"is_waiting"`
	} `json:"database"`
	
	// Redis连接池统计
	Redis struct {
		PoolSize     int     `json:"pool_size"`
		IdleConns    int     `json:"idle_conns"`
		StaleConns   int     `json:"stale_conns"`
		TotalConns   int     `json:"total_conns"`
		UsageRate    float64 `json:"usage_rate"`
		Hits         uint64  `json:"hits"`
		Misses       uint64  `json:"misses"`
		Timeouts     uint64  `json:"timeouts"`
	} `json:"redis"`
	
	// 告警状态
	Alerts []string `json:"alerts"`
}

// NewConnectionMonitor 创建连接监控服务
func NewConnectionMonitor(redis *redis.Client, logger *zap.Logger) *ConnectionMonitor {
	return &ConnectionMonitor{
		redis:  redis,
		logger: logger,
		checkInterval: 30 * time.Second, // 30秒检查一次
		alertThresholds: ConnectionAlertThresholds{
			DBUsageRate:       80.0, // 数据库连接池使用率超过80%告警
			DBWaitCount:       10,   // 数据库连接等待超过10个告警
			RedisUsageRate:    85.0, // Redis连接池使用率超过85%告警
			ConsecutiveAlerts: 3,    // 连续3次告警才发送通知
		},
	}
}

// Start 启动连接监控
func (m *ConnectionMonitor) Start(ctx context.Context) {
	ticker := time.NewTicker(m.checkInterval)
	defer ticker.Stop()
	
	m.logger.Info("连接池监控服务启动", zap.Duration("check_interval", m.checkInterval))
	
	for {
		select {
		case <-ctx.Done():
			m.logger.Info("连接池监控服务停止")
			return
		case <-ticker.C:
			if err := m.checkConnections(ctx); err != nil {
				m.logger.Error("连接池检查失败", zap.Error(err))
			}
		}
	}
}

// checkConnections 检查连接池状态
func (m *ConnectionMonitor) checkConnections(ctx context.Context) error {
	stats := &ConnectionStats{
		Timestamp: time.Now(),
		Alerts:    make([]string, 0),
	}
	
	// 检查数据库连接池
	if err := m.checkDatabaseConnections(stats); err != nil {
		m.logger.Error("检查数据库连接池失败", zap.Error(err))
	}
	
	// 检查Redis连接池
	if err := m.checkRedisConnections(ctx, stats); err != nil {
		m.logger.Error("检查Redis连接池失败", zap.Error(err))
	}
	
	// 记录统计信息到Redis
	if err := m.recordStats(ctx, stats); err != nil {
		m.logger.Error("记录连接统计失败", zap.Error(err))
	}
	
	// 处理告警
	if len(stats.Alerts) > 0 {
		m.handleAlerts(stats)
	}
	
	return nil
}

// checkDatabaseConnections 检查数据库连接池
func (m *ConnectionMonitor) checkDatabaseConnections(stats *ConnectionStats) error {
	dbStats := database.GetConnectionStats()
	
	// 解析统计信息
	if status, ok := dbStats["status"].(string); ok && status != "connected" {
		stats.Alerts = append(stats.Alerts, "数据库连接异常: "+status)
		return nil
	}
	
	stats.DB.MaxOpenConns = getIntValue(dbStats, "max_open_conns")
	stats.DB.OpenConns = getIntValue(dbStats, "open_conns")
	stats.DB.InUse = getIntValue(dbStats, "in_use")
	stats.DB.Idle = getIntValue(dbStats, "idle")
	stats.DB.WaitCount = getInt64Value(dbStats, "wait_count")
	stats.DB.WaitDuration = getStringValue(dbStats, "wait_duration")
	stats.DB.IsHighUsage = getBoolValue(dbStats, "is_high_usage")
	stats.DB.IsWaiting = getBoolValue(dbStats, "is_connection_wait")
	
	// 计算使用率
	if stats.DB.MaxOpenConns > 0 {
		stats.DB.UsageRate = float64(stats.DB.InUse) / float64(stats.DB.MaxOpenConns) * 100
	}
	
	// 检查告警条件
	if stats.DB.UsageRate > m.alertThresholds.DBUsageRate {
		stats.Alerts = append(stats.Alerts, 
			fmt.Sprintf("数据库连接池使用率过高: %.1f%% (阈值: %.1f%%)", 
				stats.DB.UsageRate, m.alertThresholds.DBUsageRate))
	}
	
	if stats.DB.WaitCount > m.alertThresholds.DBWaitCount {
		stats.Alerts = append(stats.Alerts, 
			fmt.Sprintf("数据库连接等待过多: %d (阈值: %d)", 
				stats.DB.WaitCount, m.alertThresholds.DBWaitCount))
	}
	
	return nil
}

// checkRedisConnections 检查Redis连接池
func (m *ConnectionMonitor) checkRedisConnections(ctx context.Context, stats *ConnectionStats) error {
	poolStats := m.redis.PoolStats()
	
	stats.Redis.PoolSize = int(poolStats.TotalConns)
	stats.Redis.IdleConns = int(poolStats.IdleConns)
	stats.Redis.StaleConns = int(poolStats.StaleConns)
	stats.Redis.TotalConns = int(poolStats.TotalConns)
	stats.Redis.Hits = uint64(poolStats.Hits)
	stats.Redis.Misses = uint64(poolStats.Misses)
	stats.Redis.Timeouts = uint64(poolStats.Timeouts)
	
	// 计算使用率（假设最大连接数为配置的pool_size）
	maxConns := 300 // 从配置中获取，这里硬编码为示例
	if maxConns > 0 {
		activeConns := stats.Redis.TotalConns - stats.Redis.IdleConns
		stats.Redis.UsageRate = float64(activeConns) / float64(maxConns) * 100
	}
	
	// 检查告警条件
	if stats.Redis.UsageRate > m.alertThresholds.RedisUsageRate {
		stats.Alerts = append(stats.Alerts, 
			fmt.Sprintf("Redis连接池使用率过高: %.1f%% (阈值: %.1f%%)", 
				stats.Redis.UsageRate, m.alertThresholds.RedisUsageRate))
	}
	
	if stats.Redis.Timeouts > 0 {
		stats.Alerts = append(stats.Alerts, 
			fmt.Sprintf("Redis连接超时: %d次", stats.Redis.Timeouts))
	}
	
	return nil
}

// recordStats 记录统计信息到Redis
func (m *ConnectionMonitor) recordStats(ctx context.Context, stats *ConnectionStats) error {
	key := fmt.Sprintf("connection_stats:%d", stats.Timestamp.Unix())
	
	// 序列化统计信息
	data := map[string]interface{}{
		"timestamp": stats.Timestamp.Unix(),
		"db_usage_rate": stats.DB.UsageRate,
		"db_wait_count": stats.DB.WaitCount,
		"redis_usage_rate": stats.Redis.UsageRate,
		"alerts_count": len(stats.Alerts),
	}
	
	// 存储到Redis，保留1小时
	return m.redis.HMSet(ctx, key, data).Err()
}

// handleAlerts 处理告警
func (m *ConnectionMonitor) handleAlerts(stats *ConnectionStats) {
	for _, alert := range stats.Alerts {
		m.logger.Warn("连接池告警", 
			zap.String("alert", alert),
			zap.Time("timestamp", stats.Timestamp),
			zap.Float64("db_usage_rate", stats.DB.UsageRate),
			zap.Float64("redis_usage_rate", stats.Redis.UsageRate))
	}
}

// GetCurrentStats 获取当前连接统计
func (m *ConnectionMonitor) GetCurrentStats(ctx context.Context) (*ConnectionStats, error) {
	stats := &ConnectionStats{
		Timestamp: time.Now(),
		Alerts:    make([]string, 0),
	}
	
	if err := m.checkDatabaseConnections(stats); err != nil {
		return nil, err
	}
	
	if err := m.checkRedisConnections(ctx, stats); err != nil {
		return nil, err
	}
	
	return stats, nil
}

// 辅助函数
func getIntValue(data map[string]interface{}, key string) int {
	if val, ok := data[key].(int); ok {
		return val
	}
	return 0
}

func getInt64Value(data map[string]interface{}, key string) int64 {
	if val, ok := data[key].(int64); ok {
		return val
	}
	return 0
}

func getStringValue(data map[string]interface{}, key string) string {
	if val, ok := data[key].(string); ok {
		return val
	}
	return ""
}

func getBoolValue(data map[string]interface{}, key string) bool {
	if val, ok := data[key].(bool); ok {
		return val
	}
	return false
}
