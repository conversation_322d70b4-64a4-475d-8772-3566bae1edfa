# 沃尔玛绑卡系统 Telegram 机器人功能技术设计文档

## 文档信息

| 项目     | 信息                                           |
| -------- | ---------------------------------------------- |
| 文档标题 | 沃尔玛绑卡系统 Telegram 机器人功能技术设计文档 |
| 文档版本 | v2.0                                           |

---

## 1. 项目概述

### 1.1 功能需求

为沃尔玛绑卡系统新增 Telegram 机器人功能，让商户能够在自己的 Telegram 群组中通过关键字命令获取绑卡统计数据。

**核心功能需求**：

- 商户可以将机器人添加到自己的 Telegram 群组
- 通过特定关键字触发数据查询（例如：发送"绑卡数量"）
- 机器人自动回复该商户的绑卡统计数据到群组
- 支持多种统计维度：日统计、周统计、月统计、自定义时间范围
- 提供群组管理功能：绑定、解绑、状态查询

### 1.2 技术目标

**安全性目标**：

- 确保商户只能查看自己的数据，防止数据泄露
- 基于现有权限系统进行访问控制
- 实现完整的身份验证和授权机制

**性能目标**：

- 支持高并发群组查询（100+ 群组同时使用）
- 响应时间 < 3 秒
- 系统可用性 > 99.5%

**可维护性目标**：

- 与现有系统无缝集成，不影响现有业务
- 支持功能模块化扩展
- 完整的日志记录和监控体系

### 1.3 预期收益

**业务收益**：

- 提升商户使用体验，降低数据查询门槛
- 增强客户粘性，提供差异化服务
- 支持实时数据监控，提高运营效率

**技术收益**：

- 丰富系统生态，增加系统价值
- 提升团队技术能力
- 为后续 IM 集成奠定基础

---

## 2. 系统架构设计

### 2.1 整体架构图

```mermaid
graph TB
    subgraph "Telegram生态"
        TG[Telegram群组]
        TB[Telegram Bot API]
    end

    subgraph "机器人服务层"
        TBS[Telegram Bot Service]
        WH[Webhook处理器]
        CMD[命令处理器]
        AUTH[身份验证模块]
    end

    subgraph "现有系统"
        API[FastAPI应用]
        DB[(MySQL数据库)]
        PERM[权限服务]
        STATS[统计服务]
    end

    subgraph "基础设施"
        REDIS[(Redis缓存)]
        LOG[日志系统]
        MONITOR[监控告警]
    end

    TG --> TB
    TB --> WH
    WH --> CMD
    CMD --> AUTH
    AUTH --> PERM
    CMD --> API
    API --> STATS
    API --> DB
    TBS --> REDIS
    TBS --> LOG
    TBS --> MONITOR
```

### 2.2 服务部署方案

**微服务架构设计**：

```mermaid
graph LR
    subgraph "负载均衡层"
        LB[Nginx/HAProxy]
    end

    subgraph "应用服务层"
        API1[FastAPI实例1]
        API2[FastAPI实例2]
        BOT1[Telegram Bot实例1]
        BOT2[Telegram Bot实例2]
    end

    subgraph "数据层"
        DB[(MySQL主库)]
        DB_SLAVE[(MySQL从库)]
        REDIS[(Redis集群)]
    end

    LB --> API1
    LB --> API2
    LB --> BOT1
    LB --> BOT2

    API1 --> DB
    API2 --> DB
    BOT1 --> DB_SLAVE
    BOT2 --> DB_SLAVE

    API1 --> REDIS
    API2 --> REDIS
    BOT1 --> REDIS
    BOT2 --> REDIS
```

**部署特点**：

- **独立服务**：Telegram Bot 作为独立 Python 服务部署
- **共享资源**：复用现有 MySQL 数据库和 Redis 缓存
- **水平扩展**：支持多 Bot 实例负载均衡
- **故障隔离**：Bot 服务异常不影响主业务系统

### 2.3 技术栈选型

| 层级       | 技术选型             | 版本要求 | 选型理由                     |
| ---------- | -------------------- | -------- | ---------------------------- |
| 机器人框架 | python-telegram-bot  | v20+     | 功能完整，社区活跃，异步支持 |
| Web 框架   | FastAPI              | v0.100+  | 复用现有技术栈，性能优秀     |
| 数据库     | MySQL                | v8.0+    | 复用现有数据库，事务支持完善 |
| 缓存       | Redis                | v7.0+    | 复用现有缓存，性能优秀       |
| 容器化     | Docker               | v24+     | 标准化部署，环境一致性       |
| 监控       | Prometheus + Grafana | Latest   | 完整监控体系，可视化友好     |

---

## 3. 数据库设计

### 3.1 新增表结构

#### 3.1.1 Telegram 群组绑定表

```sql
-- Telegram群组绑定表
CREATE TABLE telegram_groups (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
    chat_id BIGINT NOT NULL UNIQUE COMMENT 'Telegram群组ID',
    chat_title VARCHAR(255) NOT NULL COMMENT '群组标题',
    chat_type ENUM('group', 'supergroup', 'channel') NOT NULL COMMENT '群组类型',
    merchant_id BIGINT NOT NULL COMMENT '绑定的商户ID',
    department_id BIGINT NULL COMMENT '绑定的部门ID（可选，用于部门级数据隔离）',
    bind_token VARCHAR(64) NOT NULL UNIQUE COMMENT '绑定令牌（用于安全绑定验证）',
    bind_status ENUM('pending', 'active', 'suspended') DEFAULT 'pending' COMMENT '绑定状态',
    bind_time DATETIME NULL COMMENT '绑定完成时间',
    bind_user_id BIGINT NULL COMMENT '执行绑定操作的用户ID',
    settings JSON NULL COMMENT '群组设置（通知开关、时区等）',
    last_active_time DATETIME NULL COMMENT '最后活跃时间',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',

    -- 外键约束
    FOREIGN KEY (merchant_id) REFERENCES merchants(id) ON DELETE CASCADE,
    FOREIGN KEY (department_id) REFERENCES departments(id) ON DELETE SET NULL,
    FOREIGN KEY (bind_user_id) REFERENCES users(id) ON DELETE SET NULL,

    -- 索引设计
    INDEX idx_merchant_id (merchant_id),
    INDEX idx_department_id (department_id),
    INDEX idx_bind_token (bind_token),
    INDEX idx_bind_status (bind_status),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
COMMENT='Telegram群组绑定表';
```

#### 3.1.2 Telegram 用户关联表

```sql
-- Telegram用户关联表
CREATE TABLE telegram_users (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
    telegram_user_id BIGINT NOT NULL UNIQUE COMMENT 'Telegram用户ID',
    telegram_username VARCHAR(255) NULL COMMENT 'Telegram用户名',
    telegram_first_name VARCHAR(255) NULL COMMENT 'Telegram名字',
    telegram_last_name VARCHAR(255) NULL COMMENT 'Telegram姓氏',
    system_user_id BIGINT NULL COMMENT '关联的系统用户ID',
    verification_token VARCHAR(64) NULL COMMENT '验证令牌',
    verification_status ENUM('pending', 'verified', 'expired') DEFAULT 'pending' COMMENT '验证状态',
    verification_time DATETIME NULL COMMENT '验证完成时间',
    last_active_time DATETIME NULL COMMENT '最后活跃时间',
    settings JSON NULL COMMENT '用户个人设置',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',

    -- 外键约束
    FOREIGN KEY (system_user_id) REFERENCES users(id) ON DELETE SET NULL,

    -- 索引设计
    INDEX idx_telegram_user_id (telegram_user_id),
    INDEX idx_system_user_id (system_user_id),
    INDEX idx_verification_token (verification_token),
    INDEX idx_verification_status (verification_status)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
COMMENT='Telegram用户关联表';
```

#### 3.1.3 机器人配置表

```sql
-- 机器人配置表
CREATE TABLE telegram_bot_configs (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
    config_key VARCHAR(100) NOT NULL UNIQUE COMMENT '配置键',
    config_value TEXT NOT NULL COMMENT '配置值',
    config_type ENUM('string', 'json', 'boolean', 'number') DEFAULT 'string' COMMENT '配置类型',
    description TEXT NULL COMMENT '配置描述',
    is_encrypted BOOLEAN DEFAULT FALSE COMMENT '是否加密存储',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',

    -- 索引设计
    INDEX idx_config_key (config_key)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
COMMENT='机器人配置表';
```

#### 3.1.4 机器人操作日志表

```sql
-- 机器人操作日志表
CREATE TABLE telegram_bot_logs (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
    chat_id BIGINT NOT NULL COMMENT '群组ID',
    user_id BIGINT NOT NULL COMMENT 'Telegram用户ID',
    command VARCHAR(100) NOT NULL COMMENT '执行的命令',
    request_data JSON NULL COMMENT '请求数据',
    response_data JSON NULL COMMENT '响应数据',
    execution_time INT NULL COMMENT '执行时间（毫秒）',
    status ENUM('success', 'error', 'permission_denied') NOT NULL COMMENT '执行状态',
    error_message TEXT NULL COMMENT '错误信息',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',

    -- 索引设计
    INDEX idx_chat_id (chat_id),
    INDEX idx_user_id (user_id),
    INDEX idx_command (command),
    INDEX idx_status (status),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
COMMENT='机器人操作日志表';
```

#### 3.1.5 Telegram 权限模板表

```sql
-- Telegram权限模板表
CREATE TABLE telegram_permission_templates (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
    template_name VARCHAR(100) NOT NULL COMMENT '模板名称',
    template_code VARCHAR(50) NOT NULL UNIQUE COMMENT '模板代码',
    description TEXT NULL COMMENT '模板描述',
    settings JSON NOT NULL COMMENT '权限配置模板',
    is_system BOOLEAN DEFAULT FALSE COMMENT '是否为系统模板',
    is_active BOOLEAN DEFAULT TRUE COMMENT '是否启用',
    created_by BIGINT NULL COMMENT '创建人ID',
    updated_by BIGINT NULL COMMENT '更新人ID',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',

    -- 外键约束
    FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL,
    FOREIGN KEY (updated_by) REFERENCES users(id) ON DELETE SET NULL,

    -- 索引设计
    INDEX idx_template_code (template_code),
    INDEX idx_is_system (is_system),
    INDEX idx_is_active (is_active),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
COMMENT='Telegram权限模板表';
```

#### 3.1.6 商户级 Telegram 配置表

```sql
-- 商户级Telegram配置表
CREATE TABLE merchant_telegram_settings (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
    merchant_id BIGINT NOT NULL UNIQUE COMMENT '商户ID',
    settings JSON NOT NULL COMMENT '商户级Telegram配置',
    is_active BOOLEAN DEFAULT TRUE COMMENT '是否启用',
    created_by BIGINT NULL COMMENT '创建人ID',
    updated_by BIGINT NULL COMMENT '更新人ID',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',

    -- 外键约束
    FOREIGN KEY (merchant_id) REFERENCES merchants(id) ON DELETE CASCADE,
    FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL,
    FOREIGN KEY (updated_by) REFERENCES users(id) ON DELETE SET NULL,

    -- 索引设计
    INDEX idx_merchant_id (merchant_id),
    INDEX idx_is_active (is_active),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
COMMENT='商户级Telegram配置表';
```

### 3.2 配置管理设计

#### 3.2.1 三级配置继承机制

系统采用三级配置继承机制，优先级从高到低：

1. **群组级配置** (`telegram_groups.settings`)
2. **商户级配置** (`merchant_telegram_settings.settings`)
3. **全局默认配置** (`telegram_bot_configs` 表中的全局配置)

**配置合并规则**：

- 高优先级配置覆盖低优先级配置
- 对象类型配置进行深度合并
- 数组类型配置完全替换

#### 3.2.2 配置结构规范

```json
{
  "permissions": {
    "allow_all_members": false,
    "require_user_verification": true,
    "admin_only_commands": ["/bind", "/unbind", "/settings"],
    "rate_limit": {
      "commands_per_minute": 10,
      "queries_per_hour": 100
    },
    "query_permissions": {
      "daily_stats": true,
      "weekly_stats": true,
      "monthly_stats": true,
      "custom_range": false,
      "detailed_data": false
    }
  },
  "display_settings": {
    "show_amount": true,
    "show_details": false,
    "decimal_places": 2,
    "timezone": "Asia/Shanghai",
    "language": "zh-CN",
    "currency_symbol": "¥",
    "date_format": "YYYY-MM-DD",
    "time_format": "HH:mm:ss"
  },
  "notification_settings": {
    "auto_notification": true,
    "welcome_message": true,
    "command_help": true,
    "auto_delete_commands": false,
    "error_notification": true,
    "daily_report": false,
    "daily_report_time": "09:00"
  },
  "advanced_settings": {
    "enable_cache": true,
    "cache_expire_minutes": 5,
    "enable_audit_log": true,
    "custom_commands": {},
    "webhook_settings": {
      "enable_webhook": false,
      "webhook_url": "",
      "webhook_secret": ""
    }
  }
}
```

### 3.3 权限扩展

```sql
-- 新增Telegram相关权限
INSERT INTO permissions (code, name, description, resource_type) VALUES
('api:telegram:bind', 'Telegram群组绑定', '绑定Telegram群组到商户', 'api'),
('api:telegram:unbind', 'Telegram群组解绑', '解绑Telegram群组', 'api'),
('api:telegram:config:read', 'Telegram配置查看', '查看Telegram机器人配置', 'api'),
('api:telegram:config:write', 'Telegram配置修改', '修改Telegram机器人配置', 'api'),
('api:telegram:template:manage', 'Telegram模板管理', '管理Telegram权限模板', 'api'),
('api:telegram:stats', 'Telegram统计查询', '通过Telegram查询统计数据', 'api'),
('menu:telegram', 'Telegram管理菜单', 'Telegram功能管理菜单', 'menu');

-- 为不同角色分配权限
-- 超级管理员：拥有所有Telegram权限
INSERT INTO role_permissions (role_id, permission_id)
SELECT r.id, p.id FROM roles r, permissions p
WHERE r.code = 'super_admin' AND p.code LIKE 'api:telegram:%';

-- 商户管理员：拥有绑定和统计查询权限
INSERT INTO role_permissions (role_id, permission_id)
SELECT r.id, p.id FROM roles r, permissions p
WHERE r.code = 'merchant_admin' AND p.code IN ('api:telegram:bind', 'api:telegram:unbind', 'api:telegram:stats');

-- CK供应商：只有统计查询权限
INSERT INTO role_permissions (role_id, permission_id)
SELECT r.id, p.id FROM roles r, permissions p
WHERE r.code = 'ck_supplier' AND p.code = 'api:telegram:stats';
```

### 3.3 数据库索引优化策略

**查询优化考虑**：

- `telegram_groups.chat_id`: 唯一索引，用于快速查找群组
- `telegram_groups.merchant_id`: 普通索引，用于商户数据隔离查询
- `telegram_users.telegram_user_id`: 唯一索引，用于用户身份验证
- `telegram_bot_logs.created_at`: 时间索引，用于日志查询和清理

**分区策略**（可选）：

```sql
-- 日志表按月分区（可选，数据量大时使用）
ALTER TABLE telegram_bot_logs PARTITION BY RANGE (YEAR(created_at) * 100 + MONTH(created_at)) (
    PARTITION p202501 VALUES LESS THAN (202502),
    PARTITION p202502 VALUES LESS THAN (202503),
    -- ... 更多分区
    PARTITION p_future VALUES LESS THAN MAXVALUE
);
```

---

## 4. API 接口规范

### 4.1 群组管理 API

#### 4.1.1 创建群组绑定令牌

**接口信息**：

- **URL**: `POST /api/v1/telegram/groups/bind-token`
- **权限**: `api:telegram:bind`
- **描述**: 创建群组绑定令牌，用于后续群组绑定验证

**请求参数**：

```json
{
  "merchant_id": 123,
  "department_id": 456, // 可选，不填则绑定到商户级别
  "description": "客服群组",
  "expire_hours": 24 // 令牌有效期，默认24小时
}
```

**响应格式**：

```json
{
  "code": 200,
  "message": "success",
  "data": {
    "bind_token": "tg_bind_abc123def456...",
    "bind_command": "/bind tg_bind_abc123def456...",
    "expire_time": "2025-01-08T12:00:00Z",
    "instructions": "请在Telegram群组中发送以下命令完成绑定：\n/bind tg_bind_abc123def456..."
  }
}
```

#### 4.1.2 群组列表查询

**接口信息**：

- **URL**: `GET /api/v1/telegram/groups`
- **权限**: `api:telegram:bind`
- **描述**: 查询当前用户有权限的群组列表

**请求参数**：

```
?page=1&page_size=20&status=active&merchant_id=123
```

**响应格式**：

```json
{
  "code": 200,
  "message": "success",
  "data": {
    "total": 50,
    "page": 1,
    "page_size": 20,
    "groups": [
      {
        "id": 1,
        "chat_id": -1001234567890,
        "chat_title": "客服群组",
        "chat_type": "supergroup",
        "merchant_id": 123,
        "merchant_name": "测试商户",
        "department_id": 456,
        "department_name": "客服部",
        "bind_status": "active",
        "bind_time": "2025-01-01T10:00:00Z",
        "last_active_time": "2025-01-07T15:30:00Z"
      }
    ]
  }
}
```

### 4.2 统计查询 API

#### 4.2.1 机器人统计查询

**接口信息**：

- **URL**: `POST /api/v1/telegram/statistics`
- **权限**: `api:telegram:stats`
- **描述**: 机器人专用统计查询接口，基于群组绑定信息自动进行权限控制

**请求参数**：

```json
{
  "chat_id": -1001234567890,
  "query_type": "daily_summary", // 查询类型：daily_summary, weekly_summary, monthly_summary, custom_range
  "start_date": "2025-01-01", // 自定义查询时使用
  "end_date": "2025-01-07", // 自定义查询时使用
  "include_details": false // 是否包含详细数据
}
```

**响应格式**：

```json
{
  "code": 200,
  "message": "success",
  "data": {
    "merchant_name": "测试商户",
    "department_name": "客服部",
    "query_period": "2025-01-07",
    "statistics": {
      "total_count": 100,
      "success_count": 85,
      "failed_count": 15,
      "pending_count": 0,
      "binding_count": 0,
      "success_rate": 85.0,
      "total_amount": 50000, // 单位：分
      "success_amount": 42500 // 单位：分
    },
    "query_time": "2025-01-07T12:00:00Z"
  }
}
```

### 4.3 配置管理 API

#### 4.3.1 获取群组配置

**接口信息**：

- **URL**: `GET /api/v1/telegram/config/groups/{group_id}/settings`
- **权限**: `api:telegram:config:read`
- **描述**: 获取群组的完整配置信息，包括有效配置和继承关系

**响应格式**：

```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "group_id": 1,
    "group_info": {
      "chat_id": -1001234567890,
      "chat_title": "客服群组",
      "merchant_id": 123,
      "department_id": 456,
      "bind_status": "active"
    },
    "group_settings": {
      "permissions": {
        "rate_limit": {
          "commands_per_minute": 5
        }
      }
    },
    "effective_settings": {
      "permissions": {
        "allow_all_members": false,
        "require_user_verification": true,
        "rate_limit": {
          "commands_per_minute": 5,
          "queries_per_hour": 100
        }
      }
    },
    "merchant_settings": {},
    "global_settings": {}
  }
}
```

#### 4.3.2 更新群组配置

**接口信息**：

- **URL**: `PUT /api/v1/telegram/config/groups/{group_id}/settings`
- **权限**: `api:telegram:config:write`
- **描述**: 更新群组级配置

**请求参数**：

```json
{
  "permissions": {
    "rate_limit": {
      "commands_per_minute": 5,
      "queries_per_hour": 50
    },
    "query_permissions": {
      "monthly_stats": false
    }
  },
  "display_settings": {
    "show_details": true
  }
}
```

#### 4.3.3 权限模板管理

**获取模板列表**：

- **URL**: `GET /api/v1/telegram/config/permission-templates`
- **权限**: `api:telegram:config:read`

**创建权限模板**：

- **URL**: `POST /api/v1/telegram/config/permission-templates`
- **权限**: `api:telegram:template:manage`

**请求参数**：

```json
{
  "template_name": "严格模式",
  "template_code": "strict_mode",
  "description": "适用于高安全要求的群组",
  "settings": {
    "permissions": {
      "allow_all_members": false,
      "require_user_verification": true,
      "rate_limit": {
        "commands_per_minute": 3,
        "queries_per_hour": 30
      }
    }
  }
}
```

**应用模板到群组**：

- **URL**: `POST /api/v1/telegram/config/groups/{group_id}/apply-template`
- **权限**: `api:telegram:config:write`

**请求参数**：

```json
{
  "template_code": "strict_mode"
}
```

#### 4.3.4 商户级配置管理

**获取商户配置**：

- **URL**: `GET /api/v1/telegram/config/merchants/{merchant_id}/telegram-settings`
- **权限**: `api:telegram:config:read`

**更新商户配置**：

- **URL**: `PUT /api/v1/telegram/config/merchants/{merchant_id}/telegram-settings`
- **权限**: `api:telegram:config:write`

#### 4.3.5 配置验证和工具

**验证配置**：

- **URL**: `POST /api/v1/telegram/config/validate-settings`
- **权限**: `api:telegram:config:read`

**缓存管理**：

- **URL**: `GET /api/v1/telegram/config/cache-stats` (获取缓存统计)
- **URL**: `POST /api/v1/telegram/config/clear-cache` (清除缓存)
- **权限**: `api:telegram:config:read` / `api:telegram:config:write`

### 4.4 错误码规范

| 错误码 | HTTP 状态码 | 错误信息                | 描述                  |
| ------ | ----------- | ----------------------- | --------------------- |
| 40001  | 400         | Invalid bind token      | 绑定令牌无效或已过期  |
| 40002  | 400         | Group already bound     | 群组已绑定到其他商户  |
| 40003  | 400         | Invalid chat type       | 不支持的群组类型      |
| 40101  | 401         | Authentication required | 需要身份验证          |
| 40301  | 403         | Permission denied       | 权限不足              |
| 40302  | 403         | Group not bound         | 群组未绑定            |
| 40303  | 403         | User not verified       | 用户未验证            |
| 40401  | 404         | Group not found         | 群组不存在            |
| 40402  | 404         | User not found          | 用户不存在            |
| 42901  | 429         | Rate limit exceeded     | 请求频率超限          |
| 50001  | 500         | Internal server error   | 服务器内部错误        |
| 50002  | 500         | Database error          | 数据库错误            |
| 50003  | 500         | Telegram API error      | Telegram API 调用失败 |

**标准错误响应格式**：

```json
{
  "code": 40001,
  "message": "Invalid bind token",
  "detail": "绑定令牌已过期，请重新生成",
  "timestamp": "2025-01-07T12:00:00Z",
  "request_id": "req_abc123def456"
}
```

---

## 5. 安全机制

### 5.1 身份验证流程

#### 5.1.1 群组绑定验证流程

```mermaid
sequenceDiagram
    participant U as 系统用户
    participant W as Web管理界面
    participant A as API服务
    participant T as Telegram群组
    participant B as Bot服务

    U->>W: 申请群组绑定
    W->>A: 生成绑定令牌
    A->>A: 创建绑定记录(pending状态)
    A->>W: 返回绑定令牌和指令
    W->>U: 显示绑定指令
    U->>T: 在群组中发送绑定指令
    T->>B: 转发绑定指令
    B->>A: 验证绑定令牌
    A->>A: 激活群组绑定
    A->>B: 返回绑定成功
    B->>T: 发送绑定成功消息
```

#### 5.1.2 用户身份验证流程

```mermaid
sequenceDiagram
    participant TU as Telegram用户
    participant B as Bot服务
    participant A as API服务
    participant SU as 系统用户

    TU->>B: 发送统计查询命令
    B->>A: 检查用户是否已验证
    A->>B: 返回未验证状态
    B->>TU: 发送验证指引
    SU->>A: 在Web界面生成验证码
    A->>SU: 返回验证码
    TU->>B: 发送验证码
    B->>A: 验证用户身份
    A->>A: 关联Telegram用户和系统用户
    A->>B: 返回验证成功
    B->>TU: 发送验证成功消息
```

### 5.2 权限控制策略

#### 5.2.1 多层权限验证

**第一层：群组级权限**

```python
def verify_group_permission(chat_id: int) -> GroupInfo:
    """验证群组是否已绑定且状态正常"""
    group = get_telegram_group_by_chat_id(chat_id)
    if not group:
        raise PermissionError("群组未绑定")
    if group.bind_status != 'active':
        raise PermissionError("群组状态异常")
    return group
```

**第二层：用户级权限**

```python
def verify_user_permission(telegram_user_id: int, group: GroupInfo) -> UserInfo:
    """验证用户是否有权限访问该群组数据"""
    telegram_user = get_telegram_user(telegram_user_id)
    if not telegram_user or not telegram_user.system_user_id:
        raise PermissionError("用户未验证")

    system_user = get_system_user(telegram_user.system_user_id)
    if not check_user_merchant_permission(system_user, group.merchant_id):
        raise PermissionError("用户无权限访问该商户数据")

    return telegram_user
```

**第三层：数据级权限**

```python
def apply_data_permission_filter(query, user: UserInfo, group: GroupInfo):
    """应用数据权限过滤"""
    if user.is_superuser:
        return query  # 超级管理员无限制

    # 商户级权限过滤
    query = query.filter(merchant_id=group.merchant_id)

    # 部门级权限过滤
    if group.department_id:
        if has_permission(user, 'data:department:sub'):
            # 可查看本部门及下级部门数据
            dept_ids = get_department_and_children_ids(group.department_id)
            query = query.filter(department_id.in_(dept_ids))
        elif has_permission(user, 'data:department:own'):
            # 只能查看本部门数据
            query = query.filter(department_id=group.department_id)

    return query
```

### 5.3 数据保护措施

#### 5.3.1 敏感数据脱敏

```python
def mask_sensitive_data(data: dict) -> dict:
    """敏感数据脱敏处理"""
    if 'card_number' in data:
        card_number = data['card_number']
        data['card_number'] = card_number[:6] + '*' * (len(card_number) - 10) + card_number[-4:]

    if 'phone' in data:
        phone = data['phone']
        data['phone'] = phone[:3] + '*' * 4 + phone[-4:]

    return data
```

#### 5.3.2 访问频率限制

```python
# 频率限制配置
RATE_LIMIT_CONFIG = {
    "commands_per_minute": 10,      # 每分钟最多10个命令
    "queries_per_hour": 100,        # 每小时最多100次查询
    "bind_attempts_per_day": 5,     # 每天最多5次绑定尝试
}

@rate_limit(key="telegram_user:{user_id}", limit=10, window=60)
def handle_command(user_id: int, command: str):
    """处理命令，带频率限制"""
    pass
```

#### 5.3.3 操作审计

```python
def audit_log(chat_id: int, user_id: int, command: str, result: dict):
    """记录操作审计日志"""
    log_entry = {
        "chat_id": chat_id,
        "user_id": user_id,
        "command": command,
        "request_data": mask_sensitive_data(result.get('request', {})),
        "response_data": mask_sensitive_data(result.get('response', {})),
        "execution_time": result.get('execution_time'),
        "status": result.get('status'),
        "error_message": result.get('error'),
        "created_at": datetime.now()
    }
    save_telegram_bot_log(log_entry)
```

---

## 6. 功能模块设计

### 6.1 命令系统设计

#### 6.1.1 命令列表

| 命令             | 功能       | 权限要求   | 参数              | 示例                                  |
| ---------------- | ---------- | ---------- | ----------------- | ------------------------------------- |
| `/bind <token>`  | 绑定群组   | 群组管理员 | 绑定令牌          | `/bind tg_bind_abc123...`             |
| `/unbind`        | 解绑群组   | 群组管理员 | 无                | `/unbind`                             |
| `/stats`         | 今日统计   | 已绑定群组 | 无                | `/stats`                              |
| `/stats_week`    | 本周统计   | 已绑定群组 | 无                | `/stats_week`                         |
| `/stats_month`   | 本月统计   | 已绑定群组 | 无                | `/stats_month`                        |
| `/stats_custom`  | 自定义统计 | 已绑定群组 | 开始日期 结束日期 | `/stats_custom 2025-01-01 2025-01-07` |
| `/help`          | 帮助信息   | 无         | 无                | `/help`                               |
| `/status`        | 群组状态   | 已绑定群组 | 无                | `/status`                             |
| `/settings`      | 群组设置   | 群组管理员 | 无                | `/settings`                           |
| `/verify <code>` | 用户验证   | 无         | 验证码            | `/verify 123456`                      |

#### 6.1.2 响应格式模板

**统计数据响应模板**：

```python
STATS_RESPONSE_TEMPLATE = """
📊 **{merchant_name} - 绑卡统计**
🏢 部门：{department_name}
📅 时间：{date_range}

📈 **总体数据**
• 总请求数：{total_count:,}
• 成功绑卡：{success_count:,} ({success_rate}%)
• 失败数量：{failed_count:,}
• 处理中：{pending_count:,}

💰 **金额统计**
• 请求总金额：¥{total_amount:,.2f}
• 成功金额：¥{success_amount:,.2f}

⏰ 查询时间：{query_time}
"""

ERROR_RESPONSE_TEMPLATE = """
❌ **操作失败**
错误类型：{error_type}
错误信息：{error_message}
建议操作：{suggestion}

如需帮助，请发送 /help 查看使用说明
"""

HELP_RESPONSE_TEMPLATE = """
🤖 **沃尔玛绑卡统计机器人**

📋 **可用命令**：
• `/stats` - 查看今日统计
• `/stats_week` - 查看本周统计
• `/stats_month` - 查看本月统计
• `/stats_custom 开始日期 结束日期` - 自定义时间统计
• `/status` - 查看群组绑定状态
• `/help` - 显示此帮助信息

🔐 **管理员命令**：
• `/bind <令牌>` - 绑定群组到商户
• `/unbind` - 解绑群组
• `/settings` - 群组设置

💡 **使用提示**：
1. 群组需要先绑定才能查询统计数据
2. 只能查看已授权商户的数据
3. 如遇问题请联系系统管理员

📞 **技术支持**：<EMAIL>
"""
```

#### 6.1.3 命令处理器架构

```python
class CommandHandler:
    """命令处理器基类"""

    def __init__(self, bot_service):
        self.bot_service = bot_service
        self.logger = logging.getLogger(__name__)

    async def handle(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """处理命令的通用流程"""
        start_time = time.time()
        chat_id = update.effective_chat.id
        user_id = update.effective_user.id
        command = update.message.text.split()[0]

        try:
            # 权限验证
            await self.verify_permissions(chat_id, user_id, command)

            # 执行具体命令
            result = await self.execute_command(update, context)

            # 记录成功日志
            execution_time = int((time.time() - start_time) * 1000)
            await self.audit_log(chat_id, user_id, command, {
                'status': 'success',
                'execution_time': execution_time,
                'response': result
            })

            return result

        except Exception as e:
            # 记录错误日志
            execution_time = int((time.time() - start_time) * 1000)
            await self.audit_log(chat_id, user_id, command, {
                'status': 'error',
                'execution_time': execution_time,
                'error': str(e)
            })

            # 发送错误响应
            await self.send_error_response(update, e)

    async def verify_permissions(self, chat_id: int, user_id: int, command: str):
        """验证权限"""
        raise NotImplementedError

    async def execute_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """执行具体命令"""
        raise NotImplementedError
```

### 6.2 配置管理设计

#### 6.2.1 配置层级结构

```yaml
# 全局配置
global:
  bot_token: "${TELEGRAM_BOT_TOKEN}"
  webhook_url: "${WEBHOOK_URL}"
  webhook_secret: "${WEBHOOK_SECRET}"
  rate_limit:
    global_limit: 1000 # 全局每小时限制
    group_limit: 100 # 单群组每小时限制
    user_limit: 50 # 单用户每小时限制

  # 安全配置
  security:
    bind_token_expire_hours: 24
    verification_token_expire_minutes: 30
    max_bind_attempts_per_day: 5
    enable_audit_log: true
    mask_sensitive_data: true

# 商户级配置
merchant:
  default_settings:
    auto_notification: true
    data_mask: true
    timezone: "Asia/Shanghai"
    language: "zh-CN"

  # 通知设置
  notification:
    daily_report_time: "09:00"
    error_alert_threshold: 10 # 错误数量超过10时告警
    success_rate_threshold: 90 # 成功率低于90%时告警

# 群组级配置
group:
  default_settings:
    welcome_message: true
    command_help: true
    auto_delete_commands: false

  # 统计显示配置
  statistics:
    show_amount: true
    show_details: false
    decimal_places: 2

  # 权限配置
  permissions:
    allow_all_members: false # 是否允许所有成员查询
    admin_only_commands: ["bind", "unbind", "settings"]
```

#### 6.2.2 配置管理接口

```python
class ConfigManager:
    """配置管理器"""

    def __init__(self, db_session):
        self.db = db_session
        self.cache = {}
        self.cache_expire = 300  # 5分钟缓存

    async def get_config(self, key: str, default=None):
        """获取配置值"""
        if key in self.cache:
            if time.time() - self.cache[key]['timestamp'] < self.cache_expire:
                return self.cache[key]['value']

        config = await self.db.query(TelegramBotConfig).filter_by(config_key=key).first()
        if config:
            value = self._parse_config_value(config.config_value, config.config_type)
            self.cache[key] = {'value': value, 'timestamp': time.time()}
            return value

        return default

    async def set_config(self, key: str, value, config_type: str = 'string'):
        """设置配置值"""
        config_value = self._serialize_config_value(value, config_type)

        config = await self.db.query(TelegramBotConfig).filter_by(config_key=key).first()
        if config:
            config.config_value = config_value
            config.config_type = config_type
        else:
            config = TelegramBotConfig(
                config_key=key,
                config_value=config_value,
                config_type=config_type
            )
            self.db.add(config)

        await self.db.commit()

        # 更新缓存
        parsed_value = self._parse_config_value(config_value, config_type)
        self.cache[key] = {'value': parsed_value, 'timestamp': time.time()}
```

### 6.3 监控日志设计

#### 6.3.1 监控指标定义

```python
# 业务监控指标
BUSINESS_METRICS = {
    "commands_per_minute": {
        "name": "每分钟命令数",
        "type": "counter",
        "labels": ["command", "status"]
    },
    "query_response_time": {
        "name": "查询响应时间",
        "type": "histogram",
        "buckets": [0.1, 0.5, 1.0, 2.0, 5.0, 10.0]
    },
    "active_groups": {
        "name": "活跃群组数",
        "type": "gauge"
    },
    "error_rate": {
        "name": "错误率",
        "type": "gauge",
        "labels": ["error_type"]
    }
}

# 技术监控指标
TECHNICAL_METRICS = {
    "webhook_requests": {
        "name": "Webhook请求数",
        "type": "counter",
        "labels": ["status"]
    },
    "database_connections": {
        "name": "数据库连接数",
        "type": "gauge"
    },
    "redis_operations": {
        "name": "Redis操作数",
        "type": "counter",
        "labels": ["operation", "status"]
    }
}
```

#### 6.3.2 日志格式规范

```python
# 结构化日志格式
LOG_FORMAT = {
    "timestamp": "2025-01-07T12:00:00.000Z",
    "level": "INFO",
    "service": "telegram-bot",
    "module": "command_handler",
    "chat_id": -1001234567890,
    "user_id": 123456789,
    "command": "/stats",
    "execution_time_ms": 150,
    "status": "success",
    "message": "统计查询成功",
    "data": {
        "merchant_id": 123,
        "department_id": 456,
        "query_type": "daily_summary"
    },
    "trace_id": "trace_abc123def456"
}

# 错误日志格式
ERROR_LOG_FORMAT = {
    "timestamp": "2025-01-07T12:00:00.000Z",
    "level": "ERROR",
    "service": "telegram-bot",
    "module": "permission_service",
    "chat_id": -1001234567890,
    "user_id": 123456789,
    "command": "/stats",
    "error_type": "PermissionError",
    "error_message": "用户无权限访问该商户数据",
    "stack_trace": "...",
    "trace_id": "trace_abc123def456"
}
```

---

## 7. 部署运维方案

### 7.1 Docker 配置

#### 7.1.1 Dockerfile

```dockerfile
# Dockerfile.telegram-bot
FROM python:3.11-slim

# 设置工作目录
WORKDIR /app

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    gcc \
    g++ \
    && rm -rf /var/lib/apt/lists/*

# 复制依赖文件
COPY requirements-telegram.txt .

# 安装Python依赖
RUN pip install --no-cache-dir -r requirements-telegram.txt

# 复制应用代码
COPY telegram_bot/ ./telegram_bot/
COPY app/models/ ./app/models/
COPY app/services/ ./app/services/
COPY app/core/ ./app/core/

# 复制配置文件
COPY config/ ./config/

# 复制启动脚本
COPY scripts/start-telegram-bot.sh .
RUN chmod +x start-telegram-bot.sh

# 创建日志目录
RUN mkdir -p /app/logs

# 设置环境变量
ENV PYTHONPATH=/app
ENV PYTHONUNBUFFERED=1

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD python -c "import requests; requests.get('http://localhost:8080/health')" || exit 1

# 暴露端口
EXPOSE 8080

# 启动命令
CMD ["./start-telegram-bot.sh"]
```

#### 7.1.2 Docker Compose 配置

```yaml
# docker-compose.telegram.yml
version: "3.8"

services:
  telegram-bot:
    build:
      context: .
      dockerfile: Dockerfile.telegram-bot
    container_name: walmart-telegram-bot
    environment:
      # 数据库配置
      - DATABASE_URL=${DATABASE_URL}
      - REDIS_URL=${REDIS_URL}

      # Telegram配置
      - TELEGRAM_BOT_TOKEN=${TELEGRAM_BOT_TOKEN}
      - WEBHOOK_URL=${WEBHOOK_URL}
      - WEBHOOK_SECRET=${WEBHOOK_SECRET}

      # 安全配置
      - ENCRYPTION_KEY=${ENCRYPTION_KEY}
      - JWT_SECRET_KEY=${JWT_SECRET_KEY}

      # 日志配置
      - LOG_LEVEL=${LOG_LEVEL:-INFO}
      - LOG_FILE=/app/logs/telegram-bot.log

      # 监控配置
      - PROMETHEUS_PORT=9090
      - HEALTH_CHECK_PORT=8080

    depends_on:
      - mysql
      - redis

    networks:
      - walmart-network

    restart: unless-stopped

    volumes:
      - ./logs:/app/logs
      - ./config:/app/config:ro

    ports:
      - "8080:8080" # 健康检查端口
      - "9090:9090" # Prometheus指标端口

    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

    logging:
      driver: "json-file"
      options:
        max-size: "100m"
        max-file: "5"

networks:
  walmart-network:
    external: true

volumes:
  telegram-bot-logs:
    driver: local
```

### 7.2 环境变量配置

#### 7.2.1 环境变量文件

```bash
# .env.telegram
# ===================
# Telegram Bot 配置
# ===================

# Telegram Bot Token（从 @BotFather 获取）
TELEGRAM_BOT_TOKEN=your_bot_token_here

# Webhook配置
WEBHOOK_URL=https://your-domain.com/telegram/webhook
WEBHOOK_SECRET=your_webhook_secret_key

# ===================
# 数据库配置
# ===================

# MySQL数据库连接（复用现有）
DATABASE_URL=mysql+pymysql://user:password@mysql:3306/walmart_db?charset=utf8mb4

# Redis连接（复用现有）
REDIS_URL=redis://redis:6379/0

# ===================
# 安全配置
# ===================

# 数据加密密钥
ENCRYPTION_KEY=your_32_character_encryption_key

# JWT密钥（复用现有）
JWT_SECRET_KEY=your_jwt_secret_key

# ===================
# 日志配置
# ===================

# 日志级别：DEBUG, INFO, WARNING, ERROR, CRITICAL
LOG_LEVEL=INFO

# 日志文件路径
LOG_FILE=/app/logs/telegram-bot.log

# 日志轮转配置
LOG_MAX_SIZE=100MB
LOG_BACKUP_COUNT=5

# ===================
# 监控配置
# ===================

# Prometheus指标端口
PROMETHEUS_PORT=9090

# 健康检查端口
HEALTH_CHECK_PORT=8080

# ===================
# 性能配置
# ===================

# 工作进程数
WORKER_PROCESSES=2

# 数据库连接池大小
DB_POOL_SIZE=10
DB_MAX_OVERFLOW=20

# Redis连接池大小
REDIS_POOL_SIZE=10

# ===================
# 功能开关
# ===================

# 是否启用调试模式
DEBUG_MODE=false

# 是否启用审计日志
ENABLE_AUDIT_LOG=true

# 是否启用敏感数据脱敏
ENABLE_DATA_MASKING=true

# 是否启用频率限制
ENABLE_RATE_LIMITING=true
```

### 7.3 监控告警配置

#### 7.3.1 Prometheus 配置

```yaml
# prometheus.yml
global:
  scrape_interval: 15s
  evaluation_interval: 15s

rule_files:
  - "telegram_bot_rules.yml"

scrape_configs:
  - job_name: "telegram-bot"
    static_configs:
      - targets: ["telegram-bot:9090"]
    scrape_interval: 10s
    metrics_path: /metrics

alerting:
  alertmanagers:
    - static_configs:
        - targets:
            - alertmanager:9093
```

#### 7.3.2 告警规则

```yaml
# telegram_bot_rules.yml
groups:
  - name: telegram_bot_alerts
    rules:
      # 高错误率告警
      - alert: TelegramBotHighErrorRate
        expr: rate(telegram_bot_commands_total{status="error"}[5m]) / rate(telegram_bot_commands_total[5m]) > 0.1
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "Telegram Bot错误率过高"
          description: "Telegram Bot在过去5分钟内错误率超过10%"

      # 响应时间过长告警
      - alert: TelegramBotSlowResponse
        expr: histogram_quantile(0.95, rate(telegram_bot_query_duration_seconds_bucket[5m])) > 5
        for: 3m
        labels:
          severity: warning
        annotations:
          summary: "Telegram Bot响应时间过长"
          description: "Telegram Bot 95%分位响应时间超过5秒"

      # 服务不可用告警
      - alert: TelegramBotDown
        expr: up{job="telegram-bot"} == 0
        for: 1m
        labels:
          severity: critical
        annotations:
          summary: "Telegram Bot服务不可用"
          description: "Telegram Bot服务已停止响应"

      # 数据库连接异常告警
      - alert: TelegramBotDatabaseError
        expr: rate(telegram_bot_database_errors_total[5m]) > 0.1
        for: 2m
        labels:
          severity: critical
        annotations:
          summary: "Telegram Bot数据库连接异常"
          description: "Telegram Bot数据库连接错误率过高"

  - name: telegram_bot_business_alerts
    rules:
      # 活跃群组数量异常下降
      - alert: TelegramBotActiveGroupsDown
        expr: telegram_bot_active_groups < 10
        for: 10m
        labels:
          severity: warning
        annotations:
          summary: "活跃群组数量异常下降"
          description: "活跃群组数量低于10个，可能存在问题"

      # 命令执行频率异常
      - alert: TelegramBotLowActivity
        expr: rate(telegram_bot_commands_total[1h]) < 0.1
        for: 30m
        labels:
          severity: info
        annotations:
          summary: "Telegram Bot活动频率较低"
          description: "过去1小时内命令执行频率低于正常水平"
```

#### 7.3.3 Grafana 仪表板配置

```json
{
  "dashboard": {
    "title": "Telegram Bot 监控仪表板",
    "panels": [
      {
        "title": "命令执行统计",
        "type": "stat",
        "targets": [
          {
            "expr": "rate(telegram_bot_commands_total[5m])",
            "legendFormat": "每秒命令数"
          }
        ]
      },
      {
        "title": "错误率趋势",
        "type": "graph",
        "targets": [
          {
            "expr": "rate(telegram_bot_commands_total{status=\"error\"}[5m]) / rate(telegram_bot_commands_total[5m])",
            "legendFormat": "错误率"
          }
        ]
      },
      {
        "title": "响应时间分布",
        "type": "heatmap",
        "targets": [
          {
            "expr": "rate(telegram_bot_query_duration_seconds_bucket[5m])",
            "legendFormat": "{{le}}"
          }
        ]
      },
      {
        "title": "活跃群组数量",
        "type": "stat",
        "targets": [
          {
            "expr": "telegram_bot_active_groups",
            "legendFormat": "活跃群组"
          }
        ]
      }
    ]
  }
}
```

### 7.4 部署脚本

#### 7.4.1 启动脚本

```bash
#!/bin/bash
# scripts/start-telegram-bot.sh

set -e

echo "Starting Telegram Bot Service..."

# 检查必要的环境变量
required_vars=(
    "TELEGRAM_BOT_TOKEN"
    "DATABASE_URL"
    "REDIS_URL"
)

for var in "${required_vars[@]}"; do
    if [ -z "${!var}" ]; then
        echo "Error: Environment variable $var is not set"
        exit 1
    fi
done

# 等待数据库就绪
echo "Waiting for database to be ready..."
python -c "
import time
import sys
from sqlalchemy import create_engine
from sqlalchemy.exc import OperationalError
import os

engine = create_engine(os.environ['DATABASE_URL'])
max_retries = 30
for i in range(max_retries):
    try:
        engine.execute('SELECT 1')
        print('Database is ready!')
        break
    except OperationalError:
        if i == max_retries - 1:
            print('Database is not ready after 30 attempts')
            sys.exit(1)
        print(f'Database not ready, retrying... ({i+1}/{max_retries})')
        time.sleep(2)
"

# 运行数据库迁移（如果需要）
echo "Running database migrations..."
python -m alembic upgrade head

# 启动应用
echo "Starting Telegram Bot application..."
exec python -m telegram_bot.main
```

#### 7.4.2 部署脚本

```bash
#!/bin/bash
# scripts/deploy.sh

set -e

ENVIRONMENT=${1:-production}
VERSION=${2:-latest}

echo "Deploying Telegram Bot to $ENVIRONMENT environment..."

# 构建镜像
echo "Building Docker image..."
docker build -t walmart-telegram-bot:$VERSION -f Dockerfile.telegram-bot .

# 标记镜像
docker tag walmart-telegram-bot:$VERSION walmart-telegram-bot:latest

# 停止现有服务
echo "Stopping existing services..."
docker-compose -f docker-compose.telegram.yml down

# 启动新服务
echo "Starting new services..."
docker-compose -f docker-compose.telegram.yml up -d

# 等待服务就绪
echo "Waiting for service to be ready..."
timeout 60 bash -c 'until curl -f http://localhost:8080/health; do sleep 2; done'

echo "Deployment completed successfully!"

# 运行健康检查
echo "Running post-deployment health checks..."
./scripts/health-check.sh

echo "All checks passed. Deployment successful!"
```

---

## 8. 开发实施计划

### 8.1 分阶段开发计划

#### 8.1.1 第一阶段：基础功能实现（预计 4 周）

**目标**：实现核心的群组绑定和基础统计查询功能

**任务清单**：

| 任务                      | 预计工时 | 负责人     | 依赖关系      |
| ------------------------- | -------- | ---------- | ------------- |
| 数据库表结构设计与创建    | 1 周     | 后端开发   | 无            |
| Telegram Bot 基础框架搭建 | 1 周     | 后端开发   | 数据库完成    |
| 群组绑定功能实现          | 1.5 周   | 后端开发   | Bot 框架完成  |
| 基础统计查询 API 开发     | 1 周     | 后端开发   | 群组绑定完成  |
| 权限验证机制实现          | 1.5 周   | 后端开发   | 统计 API 完成 |
| 基础命令处理器开发        | 1 周     | 后端开发   | 权限验证完成  |
| 单元测试编写              | 1 周     | 测试工程师 | 功能开发完成  |
| 集成测试                  | 0.5 周   | 测试工程师 | 单元测试完成  |

**里程碑**：

- ✅ 数据库结构完成
- ✅ 群组绑定功能可用
- ✅ 基础统计查询可用
- ✅ 权限控制正常工作
- ✅ 通过所有测试用例

#### 8.1.2 第二阶段：功能完善（预计 3 周）

**目标**：完善命令系统，增加高级统计功能和配置管理

**任务清单**：

| 任务                   | 预计工时 | 负责人     | 依赖关系     |
| ---------------------- | -------- | ---------- | ------------ |
| 完整命令系统实现       | 1.5 周   | 后端开发   | 第一阶段完成 |
| 高级统计功能开发       | 1 周     | 后端开发   | 命令系统完成 |
| 配置管理系统实现       | 1 周     | 后端开发   | 无           |
| Web 管理界面开发       | 2 周     | 前端开发   | 配置管理完成 |
| 错误处理和用户体验优化 | 1 周     | 后端开发   | 功能开发完成 |
| 性能优化               | 0.5 周   | 后端开发   | 功能开发完成 |
| 功能测试               | 1 周     | 测试工程师 | 功能开发完成 |

**里程碑**：

- ✅ 所有命令功能正常
- ✅ Web 管理界面可用
- ✅ 配置管理系统正常
- ✅ 性能满足要求
- ✅ 用户体验良好

#### 8.1.3 第三阶段：监控部署（预计 2 周）

**目标**：完善监控体系，准备生产环境部署

**任务清单**：

| 任务               | 预计工时 | 负责人     | 依赖关系        |
| ------------------ | -------- | ---------- | --------------- |
| 监控指标实现       | 1 周     | 后端开发   | 第二阶段完成    |
| 日志系统完善       | 0.5 周   | 后端开发   | 监控指标完成    |
| Docker 配置优化    | 0.5 周   | 运维工程师 | 无              |
| 部署脚本编写       | 0.5 周   | 运维工程师 | Docker 配置完成 |
| Grafana 仪表板配置 | 0.5 周   | 运维工程师 | 监控指标完成    |
| 告警规则配置       | 0.5 周   | 运维工程师 | 仪表板完成      |
| 生产环境部署测试   | 1 周     | 运维工程师 | 所有配置完成    |
| 压力测试           | 0.5 周   | 测试工程师 | 部署测试完成    |

**里程碑**：

- ✅ 监控体系完整
- ✅ 部署流程自动化
- ✅ 生产环境稳定运行
- ✅ 性能指标达标

### 8.2 技术风险评估与控制

#### 8.2.1 高风险项

| 风险项            | 风险等级 | 影响         | 概率 | 缓解措施                                                       |
| ----------------- | -------- | ------------ | ---- | -------------------------------------------------------------- |
| Telegram API 限制 | 高       | 功能受限     | 中   | 1. 详细研究 API 限制<br>2. 实现请求频率控制<br>3. 准备降级方案 |
| 数据权限复杂性    | 高       | 数据泄露风险 | 中   | 1. 详细设计权限模型<br>2. 多层验证机制<br>3. 全面测试覆盖      |
| 性能瓶颈          | 中       | 用户体验差   | 中   | 1. 早期性能测试<br>2. 缓存策略优化<br>3. 数据库查询优化        |

#### 8.2.2 中风险项

| 风险项         | 风险等级 | 影响         | 概率 | 缓解措施                                              |
| -------------- | -------- | ------------ | ---- | ----------------------------------------------------- |
| 第三方依赖问题 | 中       | 开发延期     | 低   | 1. 选择稳定的依赖库<br>2. 版本锁定<br>3. 备选方案准备 |
| 用户体验设计   | 中       | 用户接受度低 | 中   | 1. 早期用户调研<br>2. 原型验证<br>3. 迭代优化         |
| 部署复杂性     | 中       | 上线延期     | 低   | 1. 容器化部署<br>2. 自动化脚本<br>3. 分环境测试       |

### 8.3 质量保证计划

#### 8.3.1 测试策略

**单元测试**：

- 覆盖率要求：≥90%
- 重点测试：权限验证、数据处理、API 调用
- 工具：pytest, unittest.mock

**集成测试**：

- 测试范围：API 接口、数据库操作、外部服务调用
- 测试环境：独立的测试环境
- 自动化程度：100%

**端到端测试**：

- 测试场景：完整的用户操作流程
- 测试工具：自动化测试脚本
- 执行频率：每次发布前

#### 8.3.2 代码质量标准

**代码规范**：

- Python: PEP 8
- 代码审查：所有代码必须经过审查
- 静态分析：使用 pylint, mypy

**文档要求**：

- API 文档：使用 OpenAPI 规范
- 代码注释：关键逻辑必须有注释
- 用户文档：完整的使用说明

### 8.4 上线计划

#### 8.4.1 灰度发布策略

**第一阶段：内部测试**（1 周）

- 参与人员：开发团队、测试团队
- 测试内容：功能完整性、性能稳定性
- 成功标准：所有测试用例通过

**第二阶段：小范围试点**（2 周）

- 参与商户：2-3 个友好商户
- 群组数量：5-10 个群组
- 监控重点：功能可用性、用户反馈

**第三阶段：逐步推广**（4 周）

- 推广策略：每周增加 20%的商户
- 监控指标：错误率、响应时间、用户满意度
- 回滚准备：快速回滚机制

**第四阶段：全面上线**

- 条件：前三阶段无重大问题
- 监控：7×24 小时监控
- 支持：专门的技术支持团队

#### 8.4.2 回滚计划

**触发条件**：

- 错误率 > 5%
- 响应时间 > 10 秒
- 数据安全问题
- 用户投诉激增

**回滚步骤**：

1. 立即停止新用户接入
2. 切换到维护模式
3. 回滚到上一个稳定版本
4. 验证系统恢复正常
5. 通知相关人员

### 8.5 团队协作计划

#### 8.5.1 团队组成

| 角色       | 人数 | 主要职责                           |
| ---------- | ---- | ---------------------------------- |
| 项目经理   | 1    | 项目协调、进度管理、风险控制       |
| 后端开发   | 2    | 核心功能开发、API 设计、数据库设计 |
| 前端开发   | 1    | Web 管理界面开发、用户体验优化     |
| 测试工程师 | 1    | 测试用例设计、自动化测试、质量保证 |
| 运维工程师 | 1    | 部署配置、监控告警、性能优化       |
| 产品经理   | 1    | 需求分析、用户调研、产品设计       |

#### 8.5.2 协作流程

**开发流程**：

1. 需求分析 → 技术设计 → 开发实现 → 代码审查 → 测试验证 → 部署上线

**沟通机制**：

- 每日站会：同步进度、识别问题
- 周会：回顾进展、调整计划
- 里程碑评审：评估质量、决定下一步

**工具使用**：

- 项目管理：Jira/Trello
- 代码管理：Git + GitLab
- 文档协作：Confluence/Notion
- 沟通工具：Slack/企业微信

---

## 9. 总结

### 9.1 技术方案亮点

1. **安全可靠**：多层权限验证，严格的数据隔离，完整的审计日志
2. **架构合理**：微服务设计，与现有系统无缝集成，支持水平扩展
3. **功能完整**：涵盖群组管理、统计查询、配置管理、监控告警等全套功能
4. **易于维护**：标准化部署，完整的监控体系，自动化运维

### 9.2 预期效果

**业务价值**：

- 提升商户使用体验，降低数据查询门槛
- 增强客户粘性，提供差异化服务
- 支持实时数据监控，提高运营效率

**技术价值**：

- 丰富系统生态，增加系统价值
- 提升团队技术能力，积累 IM 集成经验
- 为后续功能扩展奠定基础

### 9.3 后续扩展方向

1. **多平台支持**：扩展到微信、钉钉等其他 IM 平台
2. **智能化功能**：增加数据分析、趋势预测等智能功能
3. **自动化运营**：实现自动报告、异常告警等自动化功能
4. **API 开放**：为第三方开发者提供 API 接口

### 9.4 实施建议

1. **分阶段实施**：按照计划分阶段推进，确保每个阶段质量
2. **重视安全**：数据安全是核心，必须严格把控
3. **用户体验**：关注用户反馈，持续优化体验
4. **监控完善**：建立完整的监控体系，确保系统稳定

---

**文档版本历史**：

- v1.0 (2025-01-07): 初始版本，完整技术设计方案

**联系方式**：

- 技术支持：<EMAIL>
- 项目经理：<EMAIL>

- v1.0 (2025-01-07): 初始版本，完整技术设计方案

**联系方式**：

- 技术支持：<EMAIL>
- 项目经理：<EMAIL>
