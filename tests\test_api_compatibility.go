package main

import (
	"bytes"
	"crypto/md5"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"sort"
	"strconv"
	"strings"
	"time"
)

// 测试用的绑卡请求
type TestBindCardRequest struct {
	CardNumber      string `json:"card_number"`
	CardPassword    string `json:"card_password"`
	MerchantCode    string `json:"merchant_code"`
	MerchantOrderID string `json:"merchant_order_id"`
	Amount          int    `json:"amount"`
	ExtData         string `json:"ext_data,omitempty"`
	Debug           bool   `json:"debug,omitempty"`
}

// 生成签名 - 与Python系统完全一致的算法
func generateSignature(requestData map[string]interface{}, secretKey, timestamp, nonce string) string {
	// 1. 添加timestamp和nonce到参数中
	params := make(map[string]string)
	
	// 转换请求数据为字符串
	for key, value := range requestData {
		if value != nil {
			params[key] = convertToString(value)
		}
	}
	
	// 添加timestamp和nonce
	params["timestamp"] = timestamp
	params["nonce"] = nonce

	// 2. 按key排序
	keys := make([]string, 0, len(params))
	for key := range params {
		keys = append(keys, key)
	}
	sort.Strings(keys)

	// 3. 构建签名字符串
	var parts []string
	for _, key := range keys {
		value := params[key]
		if value != "" { // 只包含非空值
			parts = append(parts, key+"="+value)
		}
	}

	signString := strings.Join(parts, "&")
	
	// 4. 拼接密钥
	signString += "&key=" + secretKey

	// 5. MD5加密并转大写
	hash := md5.Sum([]byte(signString))
	signature := strings.ToUpper(hex.EncodeToString(hash[:]))

	return signature
}

// 将值转换为字符串 - 与Python系统保持一致
func convertToString(value interface{}) string {
	switch v := value.(type) {
	case string:
		return v
	case int:
		return strconv.Itoa(v)
	case int64:
		return strconv.FormatInt(v, 10)
	case float64:
		// 检查是否为整数
		if v == float64(int64(v)) {
			return strconv.FormatInt(int64(v), 10)
		}
		return strconv.FormatFloat(v, 'f', -1, 64)
	case bool:
		if v {
			return "true"
		}
		return "false"
	default:
		// 对于复杂类型，转换为JSON字符串
		if jsonBytes, err := json.Marshal(v); err == nil {
			return string(jsonBytes)
		}
		return fmt.Sprintf("%v", v)
	}
}

// 测试绑卡API兼容性
func testBindCardAPI() {
	// 测试数据
	request := TestBindCardRequest{
		CardNumber:      "2326123456789012",
		CardPassword:    "123456",
		MerchantCode:    "WALMART_TEST",
		MerchantOrderID: "ORDER_" + strconv.FormatInt(time.Now().Unix(), 10),
		Amount:          10000, // 100元，单位分
		ExtData:         "test_ext_data",
		Debug:           true,
	}

	// 序列化请求数据
	requestBody, err := json.Marshal(request)
	if err != nil {
		fmt.Printf("序列化请求失败: %v\n", err)
		return
	}

	// 生成签名参数
	timestamp := strconv.FormatInt(time.Now().Unix(), 10)
	nonce := "test_nonce_" + timestamp
	secretKey := "test_secret_key"

	// 将请求体转换为map用于签名
	var requestMap map[string]interface{}
	json.Unmarshal(requestBody, &requestMap)

	// 生成签名
	signature := generateSignature(requestMap, secretKey, timestamp, nonce)

	// 打印签名信息用于调试
	fmt.Println("=== API兼容性测试 ===")
	fmt.Printf("请求体: %s\n", string(requestBody))
	fmt.Printf("时间戳: %s\n", timestamp)
	fmt.Printf("随机数: %s\n", nonce)
	fmt.Printf("签名: %s\n", signature)
	fmt.Println()

	// 创建HTTP请求
	req, err := http.NewRequest("POST", "http://localhost:20000/api/v1/card-bind", bytes.NewBuffer(requestBody))
	if err != nil {
		fmt.Printf("创建请求失败: %v\n", err)
		return
	}

	// 设置请求头 - 与Python系统完全一致
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("api-key", "test_api_key")
	req.Header.Set("X-Timestamp", timestamp)
	req.Header.Set("X-Nonce", nonce)
	req.Header.Set("X-Signature", signature)

	// 发送请求
	client := &http.Client{Timeout: 30 * time.Second}
	resp, err := client.Do(req)
	if err != nil {
		fmt.Printf("发送请求失败: %v\n", err)
		return
	}
	defer resp.Body.Close()

	// 读取响应
	responseBody, err := io.ReadAll(resp.Body)
	if err != nil {
		fmt.Printf("读取响应失败: %v\n", err)
		return
	}

	// 打印响应
	fmt.Printf("响应状态: %d\n", resp.StatusCode)
	fmt.Printf("响应头: %v\n", resp.Header)
	fmt.Printf("响应体: %s\n", string(responseBody))

	// 验证响应格式
	var response map[string]interface{}
	if err := json.Unmarshal(responseBody, &response); err != nil {
		fmt.Printf("响应格式错误: %v\n", err)
		return
	}

	// 检查必需的响应字段
	expectedFields := []string{"recordId", "requestId", "status", "merchantOrderId", "amount"}
	for _, field := range expectedFields {
		if _, exists := response[field]; !exists {
			fmt.Printf("缺少响应字段: %s\n", field)
		}
	}

	fmt.Println("=== 兼容性测试完成 ===")
}

// 测试签名算法兼容性
func testSignatureCompatibility() {
	fmt.Println("=== 签名算法兼容性测试 ===")

	// 测试数据
	testCases := []struct {
		name      string
		data      map[string]interface{}
		secretKey string
		timestamp string
		nonce     string
		expected  string // 如果知道Python系统的预期签名，可以在这里验证
	}{
		{
			name: "基本测试",
			data: map[string]interface{}{
				"card_number":       "2326123456789012",
				"card_password":     "123456",
				"merchant_code":     "WALMART_TEST",
				"merchant_order_id": "ORDER_123",
				"amount":            10000,
			},
			secretKey: "test_secret",
			timestamp: "1703123456",
			nonce:     "abc123",
		},
		{
			name: "包含扩展数据",
			data: map[string]interface{}{
				"card_number":       "2326123456789012",
				"card_password":     "123456",
				"merchant_code":     "WALMART_TEST",
				"merchant_order_id": "ORDER_123",
				"amount":            10000,
				"ext_data":          "test_ext",
				"debug":             true,
			},
			secretKey: "test_secret",
			timestamp: "1703123456",
			nonce:     "abc123",
		},
	}

	for _, tc := range testCases {
		fmt.Printf("测试用例: %s\n", tc.name)
		signature := generateSignature(tc.data, tc.secretKey, tc.timestamp, tc.nonce)
		fmt.Printf("生成的签名: %s\n", signature)
		
		if tc.expected != "" {
			if signature == tc.expected {
				fmt.Println("✓ 签名匹配")
			} else {
				fmt.Printf("✗ 签名不匹配，期望: %s\n", tc.expected)
			}
		}
		fmt.Println()
	}
}

func main() {
	fmt.Println("沃尔玛绑卡网关 - API兼容性测试工具")
	fmt.Println("=====================================")
	
	// 测试签名算法
	testSignatureCompatibility()
	
	// 测试API接口（需要服务器运行）
	fmt.Println("注意: 以下测试需要网关服务器运行在 localhost:20000")
	fmt.Print("是否继续API测试? (y/n): ")
	
	var input string
	fmt.Scanln(&input)
	if strings.ToLower(input) == "y" || strings.ToLower(input) == "yes" {
		testBindCardAPI()
	}
	
	fmt.Println("测试完成!")
}
