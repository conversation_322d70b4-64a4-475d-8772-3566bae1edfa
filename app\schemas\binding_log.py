from typing import Optional, Dict, Any, List
from pydantic import BaseModel, Field
from datetime import datetime


class BindingLogBase(BaseModel):
    """绑卡日志基础模型"""
    card_record_id: str = Field(..., description="关联的卡记录ID")
    # 【安全修复】添加商户和部门字段用于数据隔离
    merchant_id: Optional[int] = Field(None, description="商户ID（用于数据隔离）")
    department_id: Optional[int] = Field(None, description="部门ID（用于数据隔离）")
    log_type: str = Field(..., description="日志类型")
    log_level: str = Field(..., description="日志级别")
    message: str = Field(..., description="日志消息")
    details: Optional[Dict[str, Any]] = Field(None, description="详细信息")
    request_data: Optional[Dict[str, Any]] = Field(None, description="请求数据")
    response_data: Optional[Dict[str, Any]] = Field(None, description="响应数据")
    duration_ms: Optional[float] = Field(None, description="操作耗时(毫秒)")
    attempt_number: Optional[str] = Field(None, description="尝试序号")
    walmart_ck_id: Optional[int] = Field(None, description="沃尔玛CKID（关键字段：用于统计CK绑卡成功数）")
    ip_address: Optional[str] = Field(None, description="IP地址")
    timestamp: datetime = Field(default_factory=datetime.now, description="日志时间戳")

    class Config:
        from_attributes = True


class BindingLogCreate(BindingLogBase):
    """创建绑卡日志模型"""
    pass


class BindingLogUpdate(BaseModel):
    """更新绑卡日志模型"""
    log_level: Optional[str] = Field(None, description="日志级别")
    message: Optional[str] = Field(None, description="日志消息")
    details: Optional[Dict[str, Any]] = Field(None, description="详细信息")
    request_data: Optional[Dict[str, Any]] = Field(None, description="请求数据")
    response_data: Optional[Dict[str, Any]] = Field(None, description="响应数据")
    duration_ms: Optional[float] = Field(None, description="操作耗时(毫秒)")


class BindingLogInDB(BindingLogBase):
    """数据库中的绑卡日志模型"""
    id: str
    created_at: datetime
    updated_at: Optional[datetime] = None


class BindingLogOut(BaseModel):
    """返回给前端的绑卡日志模型"""
    id: str
    card_record_id: str
    # 【安全修复】添加商户和部门字段
    merchant_id: Optional[int] = None
    department_id: Optional[int] = None
    log_type: str
    log_level: str
    message: str
    details: Optional[Dict[str, Any]] = None
    request_data: Optional[Dict[str, Any]] = None
    response_data: Optional[Dict[str, Any]] = None
    duration_ms: Optional[float] = None
    attempt_number: Optional[str] = None
    walmart_ck_id: Optional[int] = None
    ip_address: Optional[str] = None
    timestamp: datetime
    created_at: datetime
    updated_at: Optional[datetime] = None

    class Config:
        from_attributes = True


class BindingLogPage(BaseModel):
    """绑卡日志分页响应"""
    items: List[BindingLogOut]
    total: int
    page: int
    pageSize: int
