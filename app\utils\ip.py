from typing import Optional
import ipaddress


def is_ip_allowed(allowed_ips: Optional[str], client_ip: str) -> bool:
    """
    检查IP是否在白名单中

    Args:
        allowed_ips: 允许的IP列表，以逗号分隔，支持IP段（CIDR格式）
        client_ip: 客户端IP

    Returns:
        bool: 是否允许访问
    """
    # 如果没有设置白名单，默认允许所有IP
    if not allowed_ips:
        return True

    try:
        # 转换客户端IP为IP地址对象
        client_ip_obj = ipaddress.ip_address(client_ip)

        # 遍历白名单
        for ip in allowed_ips.split(","):
            ip = ip.strip()
            if not ip:
                continue

            try:
                # 尝试作为网段解析
                if "/" in ip:
                    network = ipaddress.ip_network(ip, strict=False)
                    if client_ip_obj in network:
                        return True
                # 尝试作为单个IP解析
                else:
                    allowed_ip = ipaddress.ip_address(ip)
                    if client_ip_obj == allowed_ip:
                        return True
            except ValueError:
                continue

        return False
    except ValueError:
        return False
