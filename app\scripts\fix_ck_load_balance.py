#!/usr/bin/env python3
"""
沃尔玛绑卡系统CK负载均衡修复脚本
用于修复CK负载均衡问题并确保系统正常运行
"""

import asyncio
import sys
import os
from datetime import datetime
from typing import Dict, List, Any, Optional
from sqlalchemy.orm import Session
from sqlalchemy import func, text

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app.db.session import SessionLocal
from app.models.walmart_ck import WalmartCK
from app.core.redis import get_redis
from app.services.redis_ck_wrapper import create_optimized_ck_service
from app.services.redis_ck_service import CKDataSyncService
from app.core.logging import get_logger

logger = get_logger("ck_load_balance_fix")


class CKLoadBalanceFixer:
    """CK负载均衡修复工具"""
    
    def __init__(self):
        self.db: Optional[Session] = None
        self.redis_client = None
        
    async def __aenter__(self):
        """异步上下文管理器入口"""
        self.db = SessionLocal()
        try:
            self.redis_client = await get_redis()
        except Exception as e:
            logger.warning(f"Redis连接失败: {e}")
            self.redis_client = None
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器出口"""
        if self.db:
            self.db.close()
        if self.redis_client:
            try:
                await self.redis_client.close()
            except:
                pass
    
    async def fix_all_issues(self, merchant_id: Optional[int] = None, force: bool = False) -> Dict[str, Any]:
        """修复所有负载均衡问题"""
        logger.info("开始修复CK负载均衡问题...")
        
        fix_result = {
            "timestamp": datetime.now().isoformat(),
            "merchant_id": merchant_id,
            "steps_completed": [],
            "errors": [],
            "summary": {}
        }
        
        try:
            # 步骤1: 修复Redis连接和初始化
            step1_result = await self._fix_redis_initialization()
            fix_result["steps_completed"].append(("redis_initialization", step1_result))
            
            # 步骤2: 同步CK数据到Redis
            step2_result = await self._sync_ck_data_to_redis(merchant_id)
            fix_result["steps_completed"].append(("data_synchronization", step2_result))
            
            # 步骤3: 修复CK使用计数
            step3_result = await self._fix_ck_usage_counts(merchant_id)
            fix_result["steps_completed"].append(("usage_count_fix", step3_result))
            
            # 步骤4: 验证负载均衡
            step4_result = await self._verify_load_balance(merchant_id)
            fix_result["steps_completed"].append(("load_balance_verification", step4_result))
            
            # 步骤5: 清理和优化
            step5_result = await self._cleanup_and_optimize()
            fix_result["steps_completed"].append(("cleanup_optimization", step5_result))
            
            # 生成修复摘要
            fix_result["summary"] = self._generate_fix_summary(fix_result)
            
            logger.info("CK负载均衡修复完成")
            
        except Exception as e:
            error_msg = f"修复过程中发生错误: {e}"
            fix_result["errors"].append(error_msg)
            logger.error(error_msg)
        
        return fix_result
    
    async def _fix_redis_initialization(self) -> Dict[str, Any]:
        """修复Redis初始化问题"""
        result = {
            "success": False,
            "redis_connected": False,
            "service_initialized": False,
            "error": None
        }
        
        try:
            # 测试Redis连接
            if self.redis_client:
                await self.redis_client.ping()
                result["redis_connected"] = True
                logger.info("Redis连接正常")
            else:
                # 尝试重新建立Redis连接
                self.redis_client = await get_redis()
                await self.redis_client.ping()
                result["redis_connected"] = True
                logger.info("Redis连接已重新建立")
            
            # 测试CK服务初始化
            ck_service = create_optimized_ck_service(self.db)
            health_info = await ck_service.health_check()
            
            if health_info.get("redis_enabled") and health_info.get("redis_connection") == "healthy":
                result["service_initialized"] = True
                logger.info("CK服务Redis优化已启用")
            else:
                logger.warning("CK服务仍在使用数据库模式")
            
            result["success"] = result["redis_connected"]
            
        except Exception as e:
            result["error"] = str(e)
            logger.error(f"Redis初始化修复失败: {e}")
        
        return result
    
    async def _sync_ck_data_to_redis(self, merchant_id: Optional[int] = None) -> Dict[str, Any]:
        """同步CK数据到Redis"""
        result = {
            "success": False,
            "synced_merchants": [],
            "synced_ck_count": 0,
            "error": None
        }
        
        try:
            if not self.redis_client:
                result["error"] = "Redis连接不可用"
                return result
            
            sync_service = CKDataSyncService(self.redis_client, self.db)
            
            if merchant_id:
                # 同步指定商户
                await sync_service.initialize_merchant_ck_pool(merchant_id)
                result["synced_merchants"].append(merchant_id)
                
                # 统计同步的CK数量
                ck_count = self.db.query(WalmartCK).filter(
                    WalmartCK.merchant_id == merchant_id,
                    WalmartCK.is_deleted == False
                ).count()
                result["synced_ck_count"] = ck_count
                
                logger.info(f"已同步商户 {merchant_id} 的 {ck_count} 个CK到Redis")
            else:
                # 同步所有商户
                merchants = self.db.query(WalmartCK.merchant_id).filter(
                    WalmartCK.is_deleted == False
                ).distinct().all()
                
                for (mid,) in merchants:
                    await sync_service.initialize_merchant_ck_pool(mid)
                    result["synced_merchants"].append(mid)
                
                result["synced_ck_count"] = self.db.query(WalmartCK).filter(
                    WalmartCK.is_deleted == False
                ).count()
                
                logger.info(f"已同步 {len(merchants)} 个商户的 {result['synced_ck_count']} 个CK到Redis")
            
            result["success"] = True
            
        except Exception as e:
            result["error"] = str(e)
            logger.error(f"CK数据同步失败: {e}")
        
        return result
    
    async def _fix_ck_usage_counts(self, merchant_id: Optional[int] = None) -> Dict[str, Any]:
        """修复CK使用计数"""
        result = {
            "success": False,
            "fixed_ck_count": 0,
            "total_corrections": 0,
            "error": None
        }
        
        try:
            # 查询需要修复的CK
            query = self.db.query(WalmartCK).filter(WalmartCK.is_deleted == False)
            if merchant_id:
                query = query.filter(WalmartCK.merchant_id == merchant_id)
            
            cks = query.all()
            
            for ck in cks:
                # 从绑卡记录中重新计算实际使用次数
                from app.models.card_record import CardRecord
                actual_count = self.db.query(CardRecord).filter(
                    CardRecord.walmart_ck_id == ck.id,
                    CardRecord.status == 'success'
                ).count()
                
                if actual_count != ck.bind_count:
                    old_count = ck.bind_count
                    ck.bind_count = actual_count
                    result["total_corrections"] += abs(actual_count - old_count)
                    result["fixed_ck_count"] += 1
                    
                    logger.info(f"修复CK {ck.id} 使用计数: {old_count} -> {actual_count}")
            
            self.db.commit()
            result["success"] = True
            
            logger.info(f"使用计数修复完成: 修复了 {result['fixed_ck_count']} 个CK")
            
        except Exception as e:
            self.db.rollback()
            result["error"] = str(e)
            logger.error(f"CK使用计数修复失败: {e}")
        
        return result
    
    async def _verify_load_balance(self, merchant_id: Optional[int] = None) -> Dict[str, Any]:
        """验证负载均衡功能"""
        result = {
            "success": False,
            "test_rounds": 20,
            "selected_cks": [],
            "unique_ck_count": 0,
            "is_balanced": False,
            "error": None
        }
        
        try:
            if not merchant_id:
                # 找一个有CK的商户进行测试
                ck = self.db.query(WalmartCK).filter(
                    WalmartCK.active == True,
                    WalmartCK.is_deleted == False
                ).first()
                if ck:
                    merchant_id = ck.merchant_id
                else:
                    result["error"] = "没有找到可用的CK进行测试"
                    return result
            
            ck_service = create_optimized_ck_service(self.db)
            
            # 进行多轮负载均衡测试
            for i in range(result["test_rounds"]):
                ck = await ck_service.get_available_ck(merchant_id)
                if ck:
                    result["selected_cks"].append(ck.id)
                    # 模拟释放CK（避免影响实际计数）
                    await ck_service.record_ck_usage(ck.id, False)
                else:
                    result["selected_cks"].append(None)
            
            # 分析负载均衡效果
            valid_selections = [ck_id for ck_id in result["selected_cks"] if ck_id is not None]
            result["unique_ck_count"] = len(set(valid_selections))
            
            # 计算分布均匀度
            if valid_selections:
                from collections import Counter
                ck_counts = Counter(valid_selections)
                max_usage = max(ck_counts.values())
                min_usage = min(ck_counts.values())
                
                # 如果最大使用次数不超过最小使用次数的2倍，认为是均衡的
                result["is_balanced"] = (max_usage <= min_usage * 2) and (result["unique_ck_count"] > 1)
            
            result["success"] = True
            
            logger.info(f"负载均衡验证完成: 选择了 {result['unique_ck_count']} 个不同CK，均衡状态: {result['is_balanced']}")
            
        except Exception as e:
            result["error"] = str(e)
            logger.error(f"负载均衡验证失败: {e}")
        
        return result
    
    async def _cleanup_and_optimize(self) -> Dict[str, Any]:
        """清理和优化"""
        result = {
            "success": False,
            "cleaned_keys": 0,
            "optimized_pools": 0,
            "error": None
        }
        
        try:
            if not self.redis_client:
                result["error"] = "Redis连接不可用"
                return result
            
            # 清理过期的Redis键
            expired_keys = await self.redis_client.keys("walmart:ck:lock:*")
            for key in expired_keys:
                ttl = await self.redis_client.ttl(key)
                if ttl == -1:  # 没有过期时间的锁
                    await self.redis_client.delete(key)
                    result["cleaned_keys"] += 1
            
            # 优化CK池排序
            pool_keys = await self.redis_client.keys("walmart:ck:pool:*")
            for pool_key in pool_keys:
                # 重新排序池中的CK（按当前负载）
                await self.redis_client.delete(pool_key)
                result["optimized_pools"] += 1
            
            result["success"] = True
            
            logger.info(f"清理优化完成: 清理了 {result['cleaned_keys']} 个过期键，优化了 {result['optimized_pools']} 个CK池")
            
        except Exception as e:
            result["error"] = str(e)
            logger.error(f"清理优化失败: {e}")
        
        return result
    
    def _generate_fix_summary(self, fix_result: Dict[str, Any]) -> Dict[str, Any]:
        """生成修复摘要"""
        summary = {
            "total_steps": len(fix_result["steps_completed"]),
            "successful_steps": 0,
            "failed_steps": 0,
            "overall_success": True,
            "key_achievements": [],
            "remaining_issues": []
        }
        
        for step_name, step_result in fix_result["steps_completed"]:
            if step_result.get("success", False):
                summary["successful_steps"] += 1
            else:
                summary["failed_steps"] += 1
                summary["overall_success"] = False
                summary["remaining_issues"].append(f"{step_name}: {step_result.get('error', '未知错误')}")
        
        # 记录关键成就
        for step_name, step_result in fix_result["steps_completed"]:
            if step_name == "data_synchronization" and step_result.get("success"):
                summary["key_achievements"].append(f"同步了 {step_result.get('synced_ck_count', 0)} 个CK到Redis")
            elif step_name == "usage_count_fix" and step_result.get("success"):
                summary["key_achievements"].append(f"修复了 {step_result.get('fixed_ck_count', 0)} 个CK的使用计数")
            elif step_name == "load_balance_verification" and step_result.get("is_balanced"):
                summary["key_achievements"].append("负载均衡功能验证通过")
        
        return summary


async def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description="CK负载均衡修复工具")
    parser.add_argument("--merchant-id", type=int, help="指定商户ID进行修复")
    parser.add_argument("--force", action="store_true", help="强制执行修复（跳过确认）")
    parser.add_argument("--output", type=str, help="输出结果到文件")
    args = parser.parse_args()
    
    if not args.force:
        print("⚠️  警告：此操作将修复CK负载均衡系统，可能会影响正在进行的绑卡操作。")
        confirm = input("是否继续？(y/N): ")
        if confirm.lower() != 'y':
            print("操作已取消")
            return
    
    async with CKLoadBalanceFixer() as fixer:
        result = await fixer.fix_all_issues(args.merchant_id, args.force)
        
        # 输出结果
        print("\n" + "="*80)
        print("🔧 沃尔玛绑卡系统CK负载均衡修复报告")
        print("="*80)
        
        print(f"\n🕒 修复时间: {result['timestamp']}")
        if result['merchant_id']:
            print(f"🏪 商户ID: {result['merchant_id']}")
        
        summary = result.get('summary', {})
        print(f"\n📊 修复摘要:")
        print(f"  总步骤数: {summary.get('total_steps', 0)}")
        print(f"  成功步骤: {summary.get('successful_steps', 0)}")
        print(f"  失败步骤: {summary.get('failed_steps', 0)}")
        print(f"  整体状态: {'✅ 成功' if summary.get('overall_success') else '❌ 部分失败'}")
        
        # 关键成就
        achievements = summary.get('key_achievements', [])
        if achievements:
            print(f"\n🎉 关键成就:")
            for i, achievement in enumerate(achievements, 1):
                print(f"  {i}. {achievement}")
        
        # 剩余问题
        issues = summary.get('remaining_issues', [])
        if issues:
            print(f"\n⚠️ 剩余问题:")
            for i, issue in enumerate(issues, 1):
                print(f"  {i}. {issue}")
        
        # 详细步骤结果
        print(f"\n📋 详细步骤结果:")
        for step_name, step_result in result['steps_completed']:
            status = "✅" if step_result.get('success') else "❌"
            print(f"  {status} {step_name}")
            if step_result.get('error'):
                print(f"      错误: {step_result['error']}")
        
        # 保存到文件
        if args.output:
            import json
            with open(args.output, 'w', encoding='utf-8') as f:
                json.dump(result, f, ensure_ascii=False, indent=2)
            print(f"\n📄 详细结果已保存到: {args.output}")
        
        print("\n" + "="*80)
        
        if summary.get('overall_success'):
            print("🎉 修复完成！CK负载均衡系统应该已经恢复正常。")
            print("💡 建议运行诊断脚本验证修复效果。")
        else:
            print("⚠️ 修复过程中遇到问题，请检查错误信息并手动处理剩余问题。")


if __name__ == "__main__":
    asyncio.run(main())
