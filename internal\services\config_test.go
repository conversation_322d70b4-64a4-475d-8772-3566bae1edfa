package services

import (
	"testing"
	"time"

	"walmart-bind-card-processor/internal/config"
)

// TestConfigurationUsage 测试配置是否被正确使用
func TestConfigurationUsage(t *testing.T) {
	// 创建测试配置
	testConfig := &config.Config{
		CKManagement: config.CKManagementConfig{
			StatusSync: config.StatusSyncConfig{
				CacheExpiry:      10 * time.Minute,
				SyncInterval:     30 * time.Second,
				FailureThreshold: 5,
				RecoveryInterval: 60 * time.Second,
				EventBufferSize:  2000,
			},
			Monitoring: config.CKMonitoringConfig{
				MetricsRetention: 48 * time.Hour,
				LogRetention:     14 * 24 * time.Hour,
				BatchSize:        200,
				FlushInterval:    60 * time.Second,
				AlertThresholds: config.AlertThresholdsConfig{
					SuccessRate:         0.98,
					ResponseTime:        3 * time.Second,
					ConsecutiveFailures: 5,
					ErrorRate:           0.02,
				},
			},
			WeightAlgorithm: config.WeightAlgorithmConfig{
				CacheRefreshInterval:    2 * time.Minute,
				LockTimeout:            10 * time.Second,
				MaxRetries:             5,
				CircuitBreakerThreshold: 10,
			},
			Preoccupation: config.PreoccupationConfig{
				Timeout:         10 * time.Minute,
				CleanupInterval: 2 * time.Minute,
				MaxConcurrent:   2000,
			},
			DistributedLock: config.DistributedLockConfig{
				Timeout:       15 * time.Second,
				RetryInterval: 200 * time.Millisecond,
				MaxRetries:    5,
			},
		},
	}

	// 创建服务工厂
	serviceFactory := NewServiceFactory(testConfig, nil, nil, nil)

	// 验证配置是否被正确传递
	t.Run("StatusSyncConfig", func(t *testing.T) {
		// 这里我们验证配置结构是否正确
		statusConfig := &testConfig.CKManagement.StatusSync
		if statusConfig.CacheExpiry != 10*time.Minute {
			t.Errorf("Expected CacheExpiry to be 10m, got %v", statusConfig.CacheExpiry)
		}
		if statusConfig.EventBufferSize != 2000 {
			t.Errorf("Expected EventBufferSize to be 2000, got %d", statusConfig.EventBufferSize)
		}
	})

	t.Run("MonitoringConfig", func(t *testing.T) {
		monitoringConfig := &testConfig.CKManagement.Monitoring
		if monitoringConfig.BatchSize != 200 {
			t.Errorf("Expected BatchSize to be 200, got %d", monitoringConfig.BatchSize)
		}
		if monitoringConfig.AlertThresholds.SuccessRate != 0.98 {
			t.Errorf("Expected SuccessRate to be 0.98, got %f", monitoringConfig.AlertThresholds.SuccessRate)
		}
	})

	t.Run("WeightAlgorithmConfig", func(t *testing.T) {
		weightConfig := &testConfig.CKManagement.WeightAlgorithm
		if weightConfig.MaxRetries != 5 {
			t.Errorf("Expected MaxRetries to be 5, got %d", weightConfig.MaxRetries)
		}
		if weightConfig.CircuitBreakerThreshold != 10 {
			t.Errorf("Expected CircuitBreakerThreshold to be 10, got %d", weightConfig.CircuitBreakerThreshold)
		}
	})

	t.Run("ServiceFactory", func(t *testing.T) {
		if serviceFactory == nil {
			t.Error("ServiceFactory should not be nil")
		}
		if serviceFactory.config != testConfig {
			t.Error("ServiceFactory should use the provided config")
		}
	})
}

// TestConfigurationDefaults 测试配置默认值
func TestConfigurationDefaults(t *testing.T) {
	// 测试配置文件中的默认值是否合理
	defaultConfig := &config.CKManagementConfig{
		StatusSync: config.StatusSyncConfig{
			CacheExpiry:      5 * time.Minute,
			SyncInterval:     10 * time.Second,
			FailureThreshold: 3,
			RecoveryInterval: 30 * time.Second,
			EventBufferSize:  1000,
		},
		Monitoring: config.CKMonitoringConfig{
			MetricsRetention: 24 * time.Hour,
			LogRetention:     7 * 24 * time.Hour,
			BatchSize:        100,
			FlushInterval:    30 * time.Second,
			AlertThresholds: config.AlertThresholdsConfig{
				SuccessRate:         0.95,
				ResponseTime:        5 * time.Second,
				ConsecutiveFailures: 3,
				ErrorRate:           0.05,
			},
		},
	}

	// 验证默认值是否合理
	if defaultConfig.StatusSync.CacheExpiry < time.Minute {
		t.Error("CacheExpiry should be at least 1 minute")
	}
	if defaultConfig.Monitoring.AlertThresholds.SuccessRate < 0.9 {
		t.Error("SuccessRate threshold should be at least 90%")
	}
}
