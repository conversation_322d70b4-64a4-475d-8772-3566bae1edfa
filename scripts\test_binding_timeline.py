#!/usr/bin/env python3
"""
测试绑卡时间线功能
验证新的时间线API是否正常工作
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from app.db.session import SessionLocal
from app.models.card_record import CardRecord
from app.services.binding_timeline_service import BindingTimelineService
from app.core.logging import get_logger

logger = get_logger("test_timeline")

def test_binding_timeline():
    """测试绑卡时间线功能"""
    print("🔍 测试绑卡时间线功能")
    print("=" * 60)
    
    db = SessionLocal()
    try:
        # 查询最近的绑卡记录
        recent_records = db.query(CardRecord).filter(
            CardRecord.status.in_(['success', 'failed'])
        ).order_by(CardRecord.created_at.desc()).limit(5).all()
        
        if not recent_records:
            print("❌ 没有找到已完成的绑卡记录")
            return
        
        print(f"📊 找到 {len(recent_records)} 条已完成的绑卡记录")
        
        # 创建时间线服务
        timeline_service = BindingTimelineService(db)
        
        for i, record in enumerate(recent_records, 1):
            print(f"\n📝 测试记录 {i}: {str(record.id)[:8]}...")
            print(f"   状态: {record.status}")
            print(f"   创建时间: {record.created_at}")
            print(f"   更新时间: {record.updated_at}")
            
            try:
                # 获取时间线
                timeline = timeline_service.get_binding_timeline(str(record.id))
                
                print(f"   ✅ 时间线获取成功")
                print(f"   📊 总耗时: {timeline.total_duration_formatted}")
                print(f"   📊 步骤数量: {len(timeline.steps)}")
                print(f"   📊 整体状态: {timeline.overall_status}")
                
                # 显示前3个步骤
                print(f"   📋 前3个步骤:")
                for j, step in enumerate(timeline.steps[:3], 1):
                    duration_info = f" ({step.duration_formatted})" if step.duration_formatted else ""
                    print(f"      {j}. {step.step_name}{duration_info} - {step.status}")
                
                # 显示统计摘要
                summary = timeline.summary
                print(f"   📈 统计摘要:")
                print(f"      总步骤: {summary['total_steps']}")
                print(f"      已完成: {summary['completed_steps']}")
                print(f"      失败: {summary['failed_steps']}")
                print(f"      成功率: {summary['success_rate']}%")
                if summary.get('average_step_duration_ms'):
                    avg_duration_s = summary['average_step_duration_ms'] / 1000
                    print(f"      平均步骤耗时: {avg_duration_s:.2f}s")
                
            except Exception as e:
                print(f"   ❌ 时间线获取失败: {e}")
                logger.exception(f"获取记录 {record.id} 的时间线失败")
    
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        logger.exception("测试绑卡时间线失败")
    
    finally:
        db.close()

def test_performance_analysis():
    """测试性能分析功能"""
    print("\n🔍 测试性能分析功能")
    print("=" * 60)
    
    db = SessionLocal()
    try:
        # 查询一个有较多日志的记录
        record = db.query(CardRecord).filter(
            CardRecord.status.in_(['success', 'failed'])
        ).order_by(CardRecord.created_at.desc()).first()
        
        if not record:
            print("❌ 没有找到已完成的绑卡记录")
            return
        
        print(f"📝 测试记录: {str(record.id)[:8]}...")
        
        # 创建时间线服务
        timeline_service = BindingTimelineService(db)
        
        try:
            # 获取性能分析
            analysis = timeline_service.get_performance_analysis(str(record.id))
            
            print(f"✅ 性能分析获取成功")
            
            # 显示性能指标
            metrics = analysis.performance_metrics
            print(f"📊 性能指标:")
            print(f"   总步骤数: {metrics.get('total_steps', 'N/A')}")
            print(f"   有时间数据的步骤: {metrics.get('steps_with_timing', 'N/A')}")
            if 'avg_duration_ms' in metrics:
                print(f"   平均耗时: {metrics['avg_duration_ms']:.2f}ms")
            if 'max_duration_ms' in metrics:
                print(f"   最大耗时: {metrics['max_duration_ms']:.2f}ms")
            
            # 显示步骤类型统计
            if 'step_types' in metrics:
                print(f"📊 步骤类型统计:")
                for step_type_stat in metrics['step_types'][:3]:  # 显示前3个
                    print(f"   {step_type_stat['step_type']}: {step_type_stat['count']}次, "
                          f"平均{step_type_stat['average_duration_ms']:.2f}ms, "
                          f"成功率{step_type_stat['success_rate']:.1f}%")
            
            # 显示瓶颈
            bottlenecks = analysis.bottlenecks
            if bottlenecks:
                print(f"⚠️  性能瓶颈 ({len(bottlenecks)}个):")
                for bottleneck in bottlenecks[:3]:  # 显示前3个
                    print(f"   {bottleneck['step_name']}: {bottleneck['duration_formatted']} "
                          f"({bottleneck['severity']})")
            else:
                print(f"✅ 未发现明显的性能瓶颈")
            
            # 显示建议
            recommendations = analysis.recommendations
            print(f"💡 优化建议 ({len(recommendations)}条):")
            for i, rec in enumerate(recommendations[:3], 1):  # 显示前3条
                print(f"   {i}. {rec}")
                
        except Exception as e:
            print(f"❌ 性能分析失败: {e}")
            logger.exception(f"获取记录 {record.id} 的性能分析失败")
    
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        logger.exception("测试性能分析失败")
    
    finally:
        db.close()

def test_step_details():
    """测试步骤详情功能"""
    print("\n🔍 测试步骤详情功能")
    print("=" * 60)
    
    db = SessionLocal()
    try:
        # 查询一个记录
        record = db.query(CardRecord).filter(
            CardRecord.status.in_(['success', 'failed'])
        ).order_by(CardRecord.created_at.desc()).first()
        
        if not record:
            print("❌ 没有找到已完成的绑卡记录")
            return
        
        print(f"📝 测试记录: {str(record.id)[:8]}...")
        
        # 创建时间线服务
        timeline_service = BindingTimelineService(db)
        
        try:
            # 获取时间线
            timeline = timeline_service.get_binding_timeline(str(record.id))
            
            print(f"✅ 获取到 {len(timeline.steps)} 个步骤")
            
            # 详细显示每个步骤
            print(f"📋 步骤详情:")
            for i, step in enumerate(timeline.steps, 1):
                print(f"\n   步骤 {i}: {step.step_name}")
                print(f"      类型: {step.step_type}")
                print(f"      状态: {step.status}")
                print(f"      开始时间: {step.start_time}")
                if step.end_time:
                    print(f"      结束时间: {step.end_time}")
                if step.duration_formatted:
                    print(f"      耗时: {step.duration_formatted}")
                print(f"      消息: {step.message}")
                if step.attempt_number:
                    print(f"      尝试序号: {step.attempt_number}")
                if step.walmart_ck_id:
                    print(f"      使用CK: {step.walmart_ck_id}")
                if step.error_message:
                    print(f"      错误: {step.error_message}")
                
                # 只显示前5个步骤的详情
                if i >= 5:
                    remaining = len(timeline.steps) - 5
                    if remaining > 0:
                        print(f"\n   ... 还有 {remaining} 个步骤")
                    break
                
        except Exception as e:
            print(f"❌ 步骤详情获取失败: {e}")
            logger.exception(f"获取记录 {record.id} 的步骤详情失败")
    
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        logger.exception("测试步骤详情失败")
    
    finally:
        db.close()

def main():
    """主函数"""
    print("🔍 绑卡时间线功能测试")
    print("=" * 80)
    
    # 测试基本时间线功能
    test_binding_timeline()
    
    # 测试性能分析功能
    test_performance_analysis()
    
    # 测试步骤详情功能
    test_step_details()
    
    print("\n" + "=" * 80)
    print("🎉 测试完成！")

if __name__ == "__main__":
    main()
