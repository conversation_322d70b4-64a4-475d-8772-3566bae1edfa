@echo off
echo 启动沃尔玛绑卡处理器调试环境
echo ================================

REM 检查Go环境
go version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Go环境未安装或未配置
    echo 请先安装Go并配置环境变量
    pause
    exit /b 1
)

echo ✅ Go环境检查通过

REM 检查配置文件
if not exist config.yaml (
    echo ❌ 配置文件 config.yaml 不存在
    echo 请确保配置文件存在并正确配置
    pause
    exit /b 1
)

echo ✅ 配置文件存在

REM 更新依赖
echo 更新Go模块依赖...
go mod tidy

REM 检查编译
echo 检查代码编译...
go build -o temp_check.exe .
if %errorlevel% neq 0 (
    echo ❌ 代码编译失败，请检查代码错误
    pause
    exit /b 1
)

del temp_check.exe
echo ✅ 代码编译通过

echo.
echo 🚀 环境准备完成！
echo.
echo 现在你可以：
echo 1. 在VSCode中按 F5 开始调试
echo 2. 或者按 Ctrl+Shift+D 打开调试面板
echo 3. 选择 "启动沃尔玛绑卡处理器" 配置
echo.
echo 💡 调试技巧：
echo - 在 main.go 第102行设置断点查看消息处理
echo - 在 bind_card_processor.go 第124行设置断点查看绑卡逻辑
echo - 在 bind_card_processor.go 第189行设置断点查看状态更新
echo.

pause
