import uuid
from sqlalchemy import Column, String, BigInteger, Float, JSON, ForeignKey, DateTime, Text
from sqlalchemy.orm import relationship, Mapped, mapped_column
from datetime import datetime

from app.models.base import BaseModel, TimestampMixin, local_now


class LogLevel:
    """日志级别常量类"""
    DEBUG = "debug"
    INFO = "info"
    WARNING = "warning"
    ERROR = "error"
    CRITICAL = "critical"

    @classmethod
    def get_all_values(cls):
        """获取所有日志级别值"""
        return [cls.DEBUG, cls.INFO, cls.WARNING, cls.ERROR, cls.CRITICAL]


class LogType:
    """日志类型常量类"""
    SYSTEM = "system"
    API = "api"
    API_REQUEST = "api_request"
    API_RESPONSE = "api_response"
    WALMART_REQUEST = "walmart_request"
    WALMART_RESPONSE = "walmart_response"
    BIND_ATTEMPT = "bind_attempt"  # 新增：绑卡尝试结果日志类型
    ERROR = "error"
    RETRY = "retry"
    CALLBACK = "callback"
    STATUS_CHANGE = "status_change"

    @classmethod
    def get_all_values(cls):
        """获取所有日志类型值"""
        return [
            cls.SYSTEM, cls.API, cls.API_REQUEST, cls.API_RESPONSE,
            cls.WALMART_REQUEST, cls.WALMART_RESPONSE, cls.BIND_ATTEMPT,
            cls.ERROR, cls.RETRY, cls.CALLBACK, cls.STATUS_CHANGE
        ]


class BindingLog(BaseModel, TimestampMixin):
    """绑卡日志模型"""

    __tablename__ = "binding_logs"

    id: Mapped[uuid.UUID] = mapped_column(
        String(36),
        primary_key=True,
        default=uuid.uuid4,
        index=True,
        comment="日志ID（UUID）",
    )
    card_record_id = Column(
        String(36), ForeignKey("card_records.id"), nullable=False, comment="关联的卡记录ID"
    )
    # 【安全修复】添加商户和部门字段用于数据隔离
    merchant_id = Column(
        BigInteger, ForeignKey("merchants.id"), nullable=True, comment="商户ID（用于数据隔离）"
    )
    department_id = Column(
        BigInteger, ForeignKey("departments.id"), nullable=True, comment="部门ID（用于数据隔离）"
    )
    log_type = Column(
        String(20),
        nullable=False,
        default=LogType.SYSTEM,
        comment="日志类型",
    )
    log_level = Column(
        String(20),
        nullable=False,
        default=LogLevel.INFO,
        comment="日志级别",
    )
    message = Column(Text, nullable=False, comment="日志消息")
    details = Column(JSON, nullable=True, comment="详细信息")
    request_data = Column(JSON, nullable=True, comment="请求数据")
    response_data = Column(JSON, nullable=True, comment="响应数据")
    duration_ms = Column(Float, nullable=True, comment="操作耗时(毫秒)")
    attempt_number = Column(String(10), nullable=True, comment="尝试序号")
    walmart_ck_id = Column(BigInteger, ForeignKey("walmart_ck.id"), nullable=True, comment="沃尔玛CKID（关键字段：用于统计CK绑卡成功数）")
    ip_address = Column(String(64), nullable=True, comment="IP地址")
    timestamp = Column(DateTime(timezone=True), default=local_now, nullable=False, comment="日志时间戳")

    # 关联关系
    card_record = relationship("CardRecord", back_populates="binding_logs")
    walmart_ck = relationship("WalmartCK", back_populates="binding_logs")
    # 【安全修复】添加商户和部门关联关系
    merchant = relationship("Merchant", foreign_keys=[merchant_id])
    department = relationship("Department", foreign_keys=[department_id])

    def to_dict(self):
        """转换为字典"""
        return {
            "id": str(self.id),
            "card_record_id": self.card_record_id,
            "merchant_id": self.merchant_id,  # 【安全修复】添加商户ID
            "department_id": self.department_id,  # 【安全修复】添加部门ID
            "log_type": self.log_type,
            "log_level": self.log_level,
            "message": self.message,
            "details": self.details,
            "request_data": self.request_data,
            "response_data": self.response_data,
            "duration_ms": self.duration_ms,
            "attempt_number": self.attempt_number,
            "walmart_ck_id": self.walmart_ck_id,
            "ip_address": self.ip_address,
            "timestamp": self.timestamp.isoformat() if self.timestamp else None,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None,
        }
