# 集成测试

本目录包含系统各组件之间的集成测试。

## 测试文件说明

### `test_redis_ck_integration.py`
Redis CK优化集成测试，包括：
- Redis连接集成测试
- 配置加载集成测试
- CK服务创建集成测试
- CK选择和降级机制测试
- 性能对比集成测试
- 完整集成流程测试

## 运行测试

### 单独运行测试
```bash
# 运行Redis CK集成测试
python test/integration/test_redis_ck_integration.py
```

### 使用pytest运行
```bash
# 运行所有集成测试
pytest test/integration/ -v

# 运行特定集成测试
pytest test/integration/test_redis_ck_integration.py -v
```

### 使用测试运行器
```bash
# 运行集成测试
python test/run_redis_tests.py --integration
```

## 测试范围

集成测试验证以下功能的协同工作：
1. **配置系统**：验证配置正确加载和应用
2. **Redis连接**：验证Redis连接和降级机制
3. **CK服务**：验证CK选择服务的完整功能
4. **数据隔离**：验证商户和部门数据隔离
5. **性能优化**：验证Redis优化的实际效果

## 测试数据要求

集成测试需要以下测试数据：
- 至少一个测试商户
- 至少一个可用的CK配置
- 正常的数据库连接
- Redis服务（可选，有降级机制）

## 注意事项

1. **数据依赖**：集成测试依赖实际的数据库数据
2. **环境要求**：需要完整的运行环境（数据库、Redis等）
3. **降级测试**：即使Redis不可用，测试也应该通过（降级机制）
4. **性能测试**：性能对比可能因环境差异而有所不同
