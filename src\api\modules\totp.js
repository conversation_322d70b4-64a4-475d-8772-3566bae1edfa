import { http } from '@/api/request'

// TOTP API 接口配置
const TOTP_API = {
    STATUS: '/totp/status',
    SETUP: '/totp/setup',
    VERIFY: '/totp/verify',
    ENABLE: '/totp/enable',
    DISABLE: '/totp/disable',
    BACKUP_VERIFY: '/totp/backup-code/verify',
    REQUIRED: '/totp/required'
}

export const totpApi = {
    /**
     * 获取TOTP状态
     */
    async getStatus() {
        try {
            const response = await http.get(TOTP_API.STATUS)
            return response.data || response
        } catch (error) {
            throw new Error('获取TOTP状态失败：' + error.message)
        }
    },

    /**
     * 设置TOTP
     */
    async setup() {
        try {
            const response = await http.post(TOTP_API.SETUP, {})
            return response.data || response
        } catch (error) {
            throw new Error('设置TOTP失败：' + error.message)
        }
    },

    /**
     * 验证TOTP验证码
     */
    async verify(data) {
        try {
            const response = await http.post(TOTP_API.VERIFY, data)
            return response.data || response
        } catch (error) {
            throw new Error('验证TOTP失败：' + error.message)
        }
    },

    /**
     * 启用TOTP
     */
    async enable(data) {
        try {
            const response = await http.post(TOTP_API.ENABLE, data)
            return response.data || response
        } catch (error) {
            throw new Error('启用TOTP失败：' + error.message)
        }
    },

    /**
     * 禁用TOTP
     */
    async disable(data) {
        try {
            const response = await http.post(TOTP_API.DISABLE, data)
            return response.data || response
        } catch (error) {
            throw new Error('禁用TOTP失败：' + error.message)
        }
    },

    /**
     * 验证备用恢复码
     */
    async verifyBackupCode(data) {
        try {
            const response = await http.post(TOTP_API.BACKUP_VERIFY, data)
            return response.data || response
        } catch (error) {
            throw new Error('验证备用码失败：' + error.message)
        }
    },

    /**
     * 检查是否需要启用TOTP
     */
    async checkRequired() {
        try {
            const response = await http.get(TOTP_API.REQUIRED)
            return response.data || response
        } catch (error) {
            throw new Error('检查TOTP要求失败：' + error.message)
        }
    }
}
