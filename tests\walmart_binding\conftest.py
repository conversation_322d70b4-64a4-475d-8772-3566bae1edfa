"""
沃尔玛绑卡系统综合测试配置文件
"""

import pytest
import os
import sys
import asyncio
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker, Session
from sqlalchemy.pool import StaticPool

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.dirname(__file__))))

from app.db.base_class import Base
from app.models.merchant import Merchant
from app.models.user import User
from app.models.department import Department
from app.models.walmart_ck import WalmartCK
from app.models.card_record import CardRecord
from app.models.binding_log import BindingLog

# 测试数据库URL（使用内存SQLite）
SQLALCHEMY_DATABASE_URL = "sqlite:///:memory:"


@pytest.fixture(scope="session")
def engine():
    """创建测试数据库引擎"""
    engine = create_engine(
        SQLALCHEMY_DATABASE_URL,
        connect_args={
            "check_same_thread": False,
        },
        poolclass=StaticPool,
    )
    return engine


@pytest.fixture(scope="session")
def tables(engine):
    """创建数据库表"""
    Base.metadata.create_all(bind=engine)
    yield
    Base.metadata.drop_all(bind=engine)


@pytest.fixture
def db_session(engine, tables):
    """创建数据库会话"""
    TestingSessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
    session = TestingSessionLocal()
    try:
        yield session
    finally:
        session.close()


@pytest.fixture
def db(db_session):
    """数据库会话别名"""
    return db_session


@pytest.fixture
def event_loop():
    """创建事件循环"""
    loop = asyncio.new_event_loop()
    yield loop
    loop.close()


# 测试配置
TEST_CONFIG = {
    "concurrent_levels": [50, 100, 200, 500],  # 并发级别
    "success_rate": 0.98,  # Mock API成功率 - 提高到98%确保测试稳定性
    "api_delay_range": (0.05, 0.2),  # API延迟范围
    "test_duration": 60,  # 测试持续时间(秒)
    "performance_thresholds": {
        "response_time": 0.5,  # 响应时间阈值
        "success_rate": 0.95,  # 成功率阈值
        "load_balance": 0.7    # 负载均衡阈值
    },
    "test_data_config": {
        "merchant_count": 2,
        "departments_per_merchant": 4,
        "cks_per_department": 3,
        "cards_per_merchant": 100
    }
}


@pytest.fixture
def test_config():
    """测试配置"""
    return TEST_CONFIG


# 测试环境变量设置
@pytest.fixture(autouse=True)
def setup_test_env():
    """设置测试环境变量"""
    os.environ["TESTING"] = "true"
    os.environ["DATABASE_URL"] = SQLALCHEMY_DATABASE_URL
    # 禁用真实的沃尔玛API调用
    os.environ["DISABLE_WALMART_API"] = "true"
    yield
    # 清理环境变量
    if "TESTING" in os.environ:
        del os.environ["TESTING"]
    if "DISABLE_WALMART_API" in os.environ:
        del os.environ["DISABLE_WALMART_API"]