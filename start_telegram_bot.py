#!/usr/bin/env python3
"""
跨平台Telegram机器人启动脚本
自动检测平台并使用最佳配置启动
"""

import os
import sys
import platform
import asyncio
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from app.core.logging import get_logger

logger = get_logger(__name__)


def detect_platform():
    """检测运行平台"""
    system = platform.system().lower()
    
    platform_info = {
        "system": system,
        "is_windows": system == "windows",
        "is_linux": system == "linux",
        "is_macos": system == "darwin",
        "architecture": platform.architecture()[0],
        "python_version": platform.python_version(),
        "platform_details": platform.platform()
    }
    
    return platform_info


def check_dependencies():
    """检查依赖项"""
    logger.info("🔍 检查依赖项...")
    
    required_modules = [
        "telegram",
        "fastapi",
        "uvicorn",
        "sqlalchemy",
        "redis",
        "pydantic",
        "python-multipart"
    ]
    
    missing_modules = []
    
    for module in required_modules:
        try:
            __import__(module)
            logger.debug(f"   ✅ {module}")
        except ImportError:
            missing_modules.append(module)
            logger.warning(f"   ❌ {module}")
    
    if missing_modules:
        logger.error(f"缺少依赖项: {', '.join(missing_modules)}")
        logger.info("请运行: pip install -r requirements.txt")
        return False
    
    logger.info("✅ 所有依赖项检查通过")
    return True


async def check_telegram_config():
    """检查Telegram配置"""
    logger.info("🤖 检查Telegram配置...")
    
    try:
        from app.telegram_bot.config import get_bot_config
        
        config = get_bot_config()
        
        if not config.bot_token:
            logger.error("❌ Bot token 未配置")
            logger.info("请在配置文件中设置 telegram.bot_token")
            return False
        
        # 测试token有效性
        from telegram.ext import Application
        
        application = Application.builder().token(config.bot_token).build()
        await application.initialize()
        
        try:
            bot_info = await application.bot.get_me()
            logger.info(f"✅ Bot配置有效: @{bot_info.username} ({bot_info.first_name})")
            return True
        finally:
            await application.shutdown()
            
    except Exception as e:
        logger.error(f"❌ Telegram配置检查失败: {e}")
        return False


async def auto_fix_conflicts():
    """自动修复冲突"""
    logger.info("🔧 自动修复潜在冲突...")
    
    try:
        # 运行自动修复
        from auto_fix_telegram import auto_fix_telegram_conflicts
        
        success = await auto_fix_telegram_conflicts()
        if success:
            logger.info("✅ 自动修复完成")
        else:
            logger.warning("⚠️  自动修复部分成功")
        
        return success
        
    except Exception as e:
        logger.error(f"❌ 自动修复失败: {e}")
        return False


def start_application(platform_info):
    """启动应用"""
    logger.info("🚀 启动应用...")
    
    try:
        import uvicorn
        from app.main import app
        
        # 根据平台选择配置
        if platform_info["is_windows"]:
            # Windows配置
            logger.info("使用Windows优化配置")
            uvicorn.run(
                app,
                host="0.0.0.0",
                port=20000,
                log_level="info",
                access_log=True,
                reload=False,  # Windows下禁用热重载避免问题
                workers=1
            )
        else:
            # Linux/macOS配置
            logger.info("使用Unix优化配置")
            uvicorn.run(
                "app.main:app",
                host="0.0.0.0",
                port=20000,
                log_level="info",
                access_log=True,
                reload=True,  # Unix下启用热重载
                workers=1
            )
            
    except Exception as e:
        logger.error(f"❌ 启动应用失败: {e}")
        return False
    
    return True


async def main():
    """主函数"""
    logger.info("🌍 跨平台Telegram机器人启动器")
    logger.info("=" * 50)
    
    # 检测平台
    platform_info = detect_platform()
    logger.info(f"平台: {platform_info['system'].title()} {platform_info['architecture']}")
    logger.info(f"Python: {platform_info['python_version']}")
    
    # 检查依赖项
    if not check_dependencies():
        logger.error("❌ 依赖项检查失败")
        return False
    
    # 检查Telegram配置
    if not await check_telegram_config():
        logger.error("❌ Telegram配置检查失败")
        return False
    
    # 自动修复冲突
    await auto_fix_conflicts()
    
    # 启动应用
    logger.info("=" * 50)
    logger.info("🎯 所有检查通过，启动应用...")
    logger.info("=" * 50)
    
    return start_application(platform_info)


if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description="跨平台Telegram机器人启动器")
    parser.add_argument("--test", action="store_true", help="仅运行测试，不启动应用")
    parser.add_argument("--fix", action="store_true", help="仅运行自动修复")
    parser.add_argument("--verbose", "-v", action="store_true", help="详细输出")
    
    args = parser.parse_args()
    
    if args.verbose:
        import logging
        logging.getLogger().setLevel(logging.DEBUG)
    
    try:
        if args.test:
            # 仅运行测试
            logger.info("🧪 运行兼容性测试...")
            
            # 运行跨平台测试
            import subprocess
            result = subprocess.run([sys.executable, "test_cross_platform.py"], 
                                  capture_output=False)
            sys.exit(result.returncode)
            
        elif args.fix:
            # 仅运行修复
            async def fix_only():
                return await auto_fix_conflicts()
            
            success = asyncio.run(fix_only())
            sys.exit(0 if success else 1)
            
        else:
            # 正常启动
            success = asyncio.run(main())
            sys.exit(0 if success else 1)
            
    except KeyboardInterrupt:
        logger.info("用户中断启动")
        sys.exit(1)
    except Exception as e:
        logger.error(f"启动过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
