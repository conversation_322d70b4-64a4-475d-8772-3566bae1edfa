"""
权限检查器模块 - 重新设计版本
提供权限检查装饰器和依赖注入函数
"""

from functools import wraps
from typing import Optional, List, Callable, Any
from fastapi import HTTPException, status, Depends
from sqlalchemy.orm import Session

from app.models.user import User
from app.api.deps import get_current_active_user, get_db
from app.services.permission_service import permission_service
from app.schemas.permission import PermissionCheckResult
import logging

logger = logging.getLogger(__name__)


class PermissionDecoratorHelper:
    """权限装饰器辅助类"""

    @staticmethod
    def validate_decorator_params(current_user, db):
        """验证装饰器参数"""
        if not current_user or not db:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="权限检查装饰器使用错误：缺少current_user或db参数"
            )

    @staticmethod
    def extract_resource_params(kwargs, resource_param, merchant_param, department_param):
        """提取资源参数"""
        return {
            'resource_id': kwargs.get(resource_param) if resource_param else None,
            'merchant_id': kwargs.get(merchant_param) if merchant_param else None,
            'department_id': kwargs.get(department_param) if department_param else None,
        }

    @staticmethod
    def check_permission_and_raise(current_user, permission_code, db):
        """检查权限并在失败时抛出异常"""
        has_permission = permission_service.check_user_permission(
            user=current_user,
            permission_code=permission_code,
            db=db
        )

        if not has_permission:
            logger.warning(
                f"用户 {current_user.username} 权限检查失败: {permission_code}"
            )
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail=f"权限不足: 缺少权限 {permission_code}"
            )


def require_permission(
    permission_code: str,
    resource_param: Optional[str] = None,
    merchant_param: Optional[str] = None,
    department_param: Optional[str] = None
):
    """
    权限检查装饰器

    Args:
        permission_code: 权限代码，格式：module:action
        resource_param: 资源ID参数名
        merchant_param: 商户ID参数名
        department_param: 部门ID参数名
    """
    def decorator(func: Callable) -> Callable:
        import inspect
        helper = PermissionDecoratorHelper()

        # 检查函数是否是异步函数
        is_async = inspect.iscoroutinefunction(func)

        if is_async:
            @wraps(func)
            async def async_wrapper(*args, **kwargs):
                current_user = kwargs.get('current_user')
                db = kwargs.get('db')

                helper.validate_decorator_params(current_user, db)
                helper.extract_resource_params(kwargs, resource_param, merchant_param, department_param)
                helper.check_permission_and_raise(current_user, permission_code, db)

                return await func(*args, **kwargs)

            return async_wrapper
        else:
            @wraps(func)
            def sync_wrapper(*args, **kwargs):
                current_user = kwargs.get('current_user')
                db = kwargs.get('db')

                helper.validate_decorator_params(current_user, db)
                helper.extract_resource_params(kwargs, resource_param, merchant_param, department_param)
                helper.check_permission_and_raise(current_user, permission_code, db)

                return func(*args, **kwargs)

            return sync_wrapper
    return decorator


def _validate_permission_params(current_user, db):
    """验证权限检查所需参数"""
    if not current_user or not db:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="权限检查装饰器使用错误：缺少current_user或db参数"
        )


def _extract_resource_params(kwargs, resource_param, merchant_param, department_param):
    """提取资源参数"""
    return {
        'resource_id': kwargs.get(resource_param) if resource_param else None,
        'merchant_id': kwargs.get(merchant_param) if merchant_param else None,
        'department_id': kwargs.get(department_param) if department_param else None
    }


def _check_multiple_permissions(current_user, db, permission_codes):
    """检查多个权限"""
    permission_results = []
    for permission_code in permission_codes:
        has_permission = permission_service.check_user_permission(
            user=current_user,
            permission_code=permission_code,
            db=db
        )
        permission_results.append(has_permission)
    return permission_results


def _validate_permission_results(current_user, permission_codes, permission_results, require_all):
    """验证权限检查结果"""
    if require_all:
        # 需要所有权限
        failed_permissions = [
            permission_codes[i]
            for i, has_permission in enumerate(permission_results)
            if not has_permission
        ]
        if failed_permissions:
            logger.warning(
                f"用户 {current_user.username} 缺少权限: {failed_permissions}"
            )
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail=f"权限不足，缺少权限: {', '.join(failed_permissions)}"
            )
    else:
        # 需要任一权限
        if not any(permission_results):
            logger.warning(
                f"用户 {current_user.username} 没有任何所需权限: {permission_codes}"
            )
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail=f"权限不足，需要以下任一权限: {', '.join(permission_codes)}"
            )


def _perform_permission_check(current_user, db, permission_codes, require_all, kwargs,
                             resource_param, merchant_param, department_param):
    """执行权限检查的核心逻辑"""
    # 验证参数
    _validate_permission_params(current_user, db)

    # 提取资源参数
    resource_params = _extract_resource_params(kwargs, resource_param, merchant_param, department_param)

    # 检查多个权限
    permission_results = _check_multiple_permissions(current_user, db, permission_codes)

    # 验证权限结果
    _validate_permission_results(current_user, permission_codes, permission_results, require_all)


def require_permissions(
    *permission_codes: str,
    require_all: bool = True,
    resource_param: Optional[str] = None,
    merchant_param: Optional[str] = None,
    department_param: Optional[str] = None
):
    """
    多权限检查装饰器

    Args:
        permission_codes: 权限代码列表
        require_all: 是否要求拥有所有权限（True）还是任一权限（False）
        resource_param: 资源ID参数名
        merchant_param: 商户ID参数名
        department_param: 部门ID参数名
    """
    def decorator(func: Callable) -> Callable:
        import inspect

        # 检查函数是否是异步函数
        is_async = inspect.iscoroutinefunction(func)

        if is_async:
            @wraps(func)
            async def async_wrapper(*args, **kwargs):
                current_user = kwargs.get('current_user')
                db = kwargs.get('db')

                # 执行权限检查
                _perform_permission_check(
                    current_user, db, permission_codes, require_all, kwargs,
                    resource_param, merchant_param, department_param
                )

                # 权限检查通过，调用原异步函数
                return await func(*args, **kwargs)

            return async_wrapper
        else:
            @wraps(func)
            def sync_wrapper(*args, **kwargs):
                current_user = kwargs.get('current_user')
                db = kwargs.get('db')

                # 执行权限检查
                _perform_permission_check(
                    current_user, db, permission_codes, require_all, kwargs,
                    resource_param, merchant_param, department_param
                )

                # 权限检查通过，调用原同步函数
                return func(*args, **kwargs)

            return sync_wrapper
    return decorator


def check_merchant_access(merchant_id: Optional[int] = None):
    """
    商户访问权限检查依赖
    
    Args:
        merchant_id: 商户ID
        
    Returns:
        Callable: 依赖函数
    """
    def dependency(
        current_user: User = Depends(get_current_active_user),
        db: Session = Depends(get_db)
    ):
        if merchant_id is None:
            return True
        
        # 检查商户访问权限
        has_permission = permission_service.can_access_merchant_data(
            user=current_user,
            merchant_id=merchant_id,
            db=db
        )

        if not has_permission:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="无权访问该商户数据"
            )
        
        return True
    
    return dependency


def check_department_access(department_id: Optional[int] = None):
    """
    部门访问权限检查依赖
    
    Args:
        department_id: 部门ID
        
    Returns:
        Callable: 依赖函数
    """
    def dependency(
        current_user: User = Depends(get_current_active_user),
        db: Session = Depends(get_db)
    ):
        if department_id is None:
            return True
        
        # 检查部门访问权限
        has_permission = permission_service.can_access_department_data(
            user=current_user,
            department_id=department_id,
            db=db
        )

        if not has_permission:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="无权访问该部门数据"
            )
        
        return True
    
    return dependency


async def check_permission_dependency(
    permission_code: str,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
) -> PermissionCheckResult:
    """
    权限检查依赖函数
    
    Args:
        permission_code: 权限代码
        current_user: 当前用户
        db: 数据库会话
        
    Returns:
        PermissionCheckResult: 权限检查结果
    """
    has_permission = permission_service.check_user_permission(
        user=current_user,
        permission_code=permission_code,
        db=db
    )

    if not has_permission:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail=f"权限不足: 缺少权限 {permission_code}"
        )

    # 返回简单的权限检查结果
    from app.schemas.permission import PermissionCheckResult
    return PermissionCheckResult(
        has_permission=True,
        reason="权限检查通过"
    )
