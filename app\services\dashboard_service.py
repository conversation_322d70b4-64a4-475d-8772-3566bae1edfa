from sqlalchemy.orm import Session
from app import models
from app.schemas.dashboard import DashboardSummary
from typing import Optional


class DashboardService:
    def get_summary(self, db: Session, current_user: Optional[models.User] = None, merchant_id: Optional[int] = None) -> DashboardSummary:
        """Get dashboard summary statistics based on user permissions."""

        # 根据用户角色和传入的merchant_id确定数据范围
        if current_user and current_user.is_platform_user():
            # 平台用户可以看到全局统计数据或指定商户的数据
            if merchant_id:
                # 查看指定商户的数据
                user_count = db.query(models.User).filter(
                    models.User.merchant_id == merchant_id,
                    models.User.is_active == True
                ).count()

                # 只显示指定的商户（1个）
                merchant_count = 1

                try:
                    # 只统计指定商户的walmart用户配置
                    walmart_ck_count = db.query(models.WalmartCK).filter(
                        models.WalmartCK.merchant_id == merchant_id
                    ).count()
                except AttributeError:
                    walmart_ck_count = 0
            else:
                # 查看全局数据
                user_count = db.query(models.User).filter(models.User.is_active == True).count()

                try:
                    merchant_count = db.query(models.Merchant).count()
                except AttributeError:
                    print("Warning: Merchant model not found or accessible as models.Merchant")
                    merchant_count = 0

                try:
                    walmart_ck_count = db.query(models.WalmartCK).count()
                except AttributeError:
                    print("Warning: WalmartCK model not found or accessible as models.WalmartCK")
                    walmart_ck_count = 0
        else:
            # 商户用户只能看到自己商户的数据，忽略传入的merchant_id参数
            if current_user and current_user.merchant_id:
                # 只统计该商户下的用户数量
                user_count = db.query(models.User).filter(
                    models.User.merchant_id == current_user.merchant_id,
                    models.User.is_active == True
                ).count()

                # 只显示自己的商户（1个）
                merchant_count = 1

                try:
                    # 只统计该商户的walmart用户配置
                    walmart_ck_count = db.query(models.WalmartCK).filter(
                        models.WalmartCK.merchant_id == current_user.merchant_id
                    ).count()
                except AttributeError:
                    walmart_ck_count = 0
            else:
                # 没有商户ID的用户，不显示任何统计数据
                user_count = 0
                merchant_count = 0
                walmart_ck_count = 0

        summary_data = DashboardSummary(
            user_count=user_count,
            merchant_count=merchant_count,
            walmart_ck_count=walmart_ck_count,
        )
        return summary_data


dashboard_service = DashboardService()
