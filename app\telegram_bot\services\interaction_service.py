"""
Telegram机器人交互服务
提供用户确认、进度提示等交互功能
"""

import asyncio
import time
from typing import Dict, Any, Optional, Callable, List
from telegram import Update
from telegram.ext import ContextTypes

from app.core.logging import get_logger
from .message_formatter import MessageFormatter

logger = get_logger(__name__)


class InteractionService:
    """交互服务"""
    
    def __init__(self):
        self.logger = logger
        self.message_formatter = MessageFormatter()
        self.pending_confirmations: Dict[str, Dict[str, Any]] = {}
        self.progress_messages: Dict[str, Dict[str, Any]] = {}
    
    async def request_confirmation(
        self,
        update: Update,
        context: ContextTypes.DEFAULT_TYPE,
        action: str,
        details: Dict[str, Any],
        callback: Callable,
        timeout: int = 30,
        warning: str = ""
    ) -> bool:
        """
        请求用户确认操作
        
        Args:
            update: Telegram更新对象
            context: 上下文
            action: 操作名称
            details: 操作详情
            callback: 确认后的回调函数
            timeout: 超时时间（秒）
            warning: 警告信息
            
        Returns:
            bool: 是否确认
        """
        try:
            chat_id = update.effective_chat.id
            user_id = update.effective_user.id
            confirmation_key = f"{chat_id}_{user_id}_{int(time.time())}"
            
            # 生成确认消息
            confirmation_message = self.message_formatter.format_confirmation_message(
                action, details, warning
            )
            
            # 发送确认消息
            await update.message.reply_text(confirmation_message, parse_mode='Markdown')
            
            # 存储确认信息
            self.pending_confirmations[confirmation_key] = {
                "chat_id": chat_id,
                "user_id": user_id,
                "action": action,
                "details": details,
                "callback": callback,
                "timestamp": time.time(),
                "timeout": timeout
            }
            
            # 设置超时清理
            asyncio.create_task(self._cleanup_confirmation(confirmation_key, timeout))
            
            return True
            
        except Exception as e:
            self.logger.error(f"请求确认失败: {e}")
            return False
    
    async def handle_confirmation_response(
        self,
        update: Update,
        context: ContextTypes.DEFAULT_TYPE
    ) -> bool:
        """
        处理确认响应
        
        Args:
            update: Telegram更新对象
            context: 上下文
            
        Returns:
            bool: 是否处理了确认响应
        """
        try:
            if not update.message or not update.message.text:
                return False
            
            chat_id = update.effective_chat.id
            user_id = update.effective_user.id
            text = update.message.text.lower().strip()
            
            # 查找匹配的确认请求
            confirmation_key = None
            for key, confirmation in self.pending_confirmations.items():
                if (confirmation["chat_id"] == chat_id and 
                    confirmation["user_id"] == user_id):
                    confirmation_key = key
                    break
            
            if not confirmation_key:
                return False
            
            confirmation = self.pending_confirmations[confirmation_key]
            
            # 检查响应
            if text in ["确认", "yes", "y", "是", "ok", "确定"]:
                # 确认操作
                try:
                    await confirmation["callback"](update, context)
                    await update.message.reply_text(
                        f"✅ **操作确认**\n\n{confirmation['action']}已执行完成。",
                        parse_mode='Markdown'
                    )
                except Exception as e:
                    await update.message.reply_text(
                        f"❌ **操作失败**\n\n{confirmation['action']}执行失败：{str(e)}",
                        parse_mode='Markdown'
                    )
                
                # 清理确认记录
                del self.pending_confirmations[confirmation_key]
                return True
                
            elif text in ["取消", "no", "n", "否", "cancel", "取消操作"]:
                # 取消操作
                await update.message.reply_text(
                    f"❎ **操作取消**\n\n{confirmation['action']}已取消。",
                    parse_mode='Markdown'
                )
                
                # 清理确认记录
                del self.pending_confirmations[confirmation_key]
                return True
            
            return False
            
        except Exception as e:
            self.logger.error(f"处理确认响应失败: {e}")
            return False
    
    async def show_progress(
        self,
        update: Update,
        operation: str,
        progress: int,
        total: int,
        details: str = ""
    ) -> Optional[int]:
        """
        显示进度信息
        
        Args:
            update: Telegram更新对象
            operation: 操作名称
            progress: 当前进度
            total: 总数
            details: 详细信息
            
        Returns:
            Optional[int]: 消息ID
        """
        try:
            chat_id = update.effective_chat.id
            progress_key = f"{chat_id}_{operation}"
            
            # 生成进度消息
            progress_message = self.message_formatter.format_progress_message(
                operation, progress, total, details
            )
            
            # 检查是否已有进度消息
            if progress_key in self.progress_messages:
                # 更新现有消息
                try:
                    message_info = self.progress_messages[progress_key]
                    await update.get_bot().edit_message_text(
                        chat_id=chat_id,
                        message_id=message_info["message_id"],
                        text=progress_message,
                        parse_mode='Markdown'
                    )
                    return message_info["message_id"]
                except Exception:
                    # 如果编辑失败，发送新消息
                    pass
            
            # 发送新的进度消息
            message = await update.message.reply_text(progress_message, parse_mode='Markdown')
            
            # 存储消息信息
            self.progress_messages[progress_key] = {
                "message_id": message.message_id,
                "chat_id": chat_id,
                "operation": operation,
                "timestamp": time.time()
            }
            
            return message.message_id
            
        except Exception as e:
            self.logger.error(f"显示进度失败: {e}")
            return None
    
    async def complete_progress(
        self,
        update: Update,
        operation: str,
        success: bool,
        details: Dict[str, Any] = None,
        suggestions: List[str] = None
    ):
        """
        完成进度显示
        
        Args:
            update: Telegram更新对象
            operation: 操作名称
            success: 是否成功
            details: 结果详情
            suggestions: 建议操作
        """
        try:
            chat_id = update.effective_chat.id
            progress_key = f"{chat_id}_{operation}"
            
            # 生成结果消息
            result_message = self.message_formatter.format_operation_result(
                operation, success, details, suggestions
            )
            
            # 检查是否有进度消息需要更新
            if progress_key in self.progress_messages:
                try:
                    message_info = self.progress_messages[progress_key]
                    await update.get_bot().edit_message_text(
                        chat_id=chat_id,
                        message_id=message_info["message_id"],
                        text=result_message,
                        parse_mode='Markdown'
                    )
                    # 清理进度记录
                    del self.progress_messages[progress_key]
                    return
                except Exception:
                    # 如果编辑失败，发送新消息
                    pass
            
            # 发送新的结果消息
            await update.message.reply_text(result_message, parse_mode='Markdown')
            
        except Exception as e:
            self.logger.error(f"完成进度显示失败: {e}")
    
    async def _cleanup_confirmation(self, confirmation_key: str, timeout: int):
        """清理超时的确认请求"""
        try:
            await asyncio.sleep(timeout)
            
            if confirmation_key in self.pending_confirmations:
                confirmation = self.pending_confirmations[confirmation_key]
                
                # 发送超时消息（这里需要bot实例，暂时跳过）
                # 在实际使用中，可以通过其他方式发送超时通知
                
                # 清理确认记录
                del self.pending_confirmations[confirmation_key]
                self.logger.info(f"确认请求超时清理: {confirmation_key}")
                
        except Exception as e:
            self.logger.error(f"清理确认请求失败: {e}")
    
    def cleanup_old_records(self, max_age: int = 3600):
        """清理过期记录"""
        try:
            current_time = time.time()
            
            # 清理过期的确认请求
            expired_confirmations = [
                key for key, confirmation in self.pending_confirmations.items()
                if current_time - confirmation["timestamp"] > max_age
            ]
            for key in expired_confirmations:
                del self.pending_confirmations[key]
            
            # 清理过期的进度消息
            expired_progress = [
                key for key, progress in self.progress_messages.items()
                if current_time - progress["timestamp"] > max_age
            ]
            for key in expired_progress:
                del self.progress_messages[key]
            
            if expired_confirmations or expired_progress:
                self.logger.info(f"清理过期记录: 确认请求{len(expired_confirmations)}个, 进度消息{len(expired_progress)}个")
                
        except Exception as e:
            self.logger.error(f"清理过期记录失败: {e}")


# 创建全局交互服务实例
interaction_service = InteractionService()
