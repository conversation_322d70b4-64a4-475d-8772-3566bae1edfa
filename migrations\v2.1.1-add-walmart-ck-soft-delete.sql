-- ========================================
-- 沃尔玛绑卡系统 - CK软删除功能迁移脚本
-- 创建时间: 2025-06-28
-- 描述: 为walmart_ck表添加软删除功能支持
-- ========================================

-- 检查is_deleted字段是否已存在，如果不存在则添加
SET @column_exists = (
    SELECT COUNT(*)
    FROM INFORMATION_SCHEMA.COLUMNS
    WHERE TABLE_SCHEMA = DATABASE()
    AND TABLE_NAME = 'walmart_ck'
    AND COLUMN_NAME = 'is_deleted'
);

-- 添加is_deleted字段（如果不存在）
SET @sql = IF(@column_exists = 0,
    'ALTER TABLE `walmart_ck` ADD COLUMN `is_deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT ''是否已删除：0未删除，1已删除'' AFTER `department_id`',
    'SELECT ''Column is_deleted already exists'' as message'
);

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 添加索引（如果不存在）
-- 检查索引是否存在的函数
SET @index_exists_1 = (
    SELECT COUNT(*)
    FROM INFORMATION_SCHEMA.STATISTICS
    WHERE TABLE_SCHEMA = DATABASE()
    AND TABLE_NAME = 'walmart_ck'
    AND INDEX_NAME = 'idx_walmart_ck_is_deleted'
);

SET @index_exists_2 = (
    SELECT COUNT(*)
    FROM INFORMATION_SCHEMA.STATISTICS
    WHERE TABLE_SCHEMA = DATABASE()
    AND TABLE_NAME = 'walmart_ck'
    AND INDEX_NAME = 'idx_walmart_ck_active_deleted'
);

SET @index_exists_3 = (
    SELECT COUNT(*)
    FROM INFORMATION_SCHEMA.STATISTICS
    WHERE TABLE_SCHEMA = DATABASE()
    AND TABLE_NAME = 'walmart_ck'
    AND INDEX_NAME = 'idx_walmart_ck_merchant_deleted'
);

-- 添加单字段索引
SET @sql = IF(@index_exists_1 = 0,
    'ALTER TABLE `walmart_ck` ADD INDEX `idx_walmart_ck_is_deleted` (`is_deleted`)',
    'SELECT ''Index idx_walmart_ck_is_deleted already exists'' as message'
);

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 添加复合索引：active + is_deleted
SET @sql = IF(@index_exists_2 = 0,
    'ALTER TABLE `walmart_ck` ADD INDEX `idx_walmart_ck_active_deleted` (`active`, `is_deleted`)',
    'SELECT ''Index idx_walmart_ck_active_deleted already exists'' as message'
);

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 添加复合索引：merchant_id + is_deleted
SET @sql = IF(@index_exists_3 = 0,
    'ALTER TABLE `walmart_ck` ADD INDEX `idx_walmart_ck_merchant_deleted` (`merchant_id`, `is_deleted`)',
    'SELECT ''Index idx_walmart_ck_merchant_deleted already exists'' as message'
);

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 确保所有现有记录的is_deleted字段都设置为0（未删除）
UPDATE `walmart_ck` SET `is_deleted` = 0 WHERE `is_deleted` IS NULL;

-- 输出迁移完成信息
SELECT 'CK软删除功能迁移完成' as migration_status,
       COUNT(*) as total_ck_records,
       SUM(CASE WHEN is_deleted = 0 THEN 1 ELSE 0 END) as active_records,
       SUM(CASE WHEN is_deleted = 1 THEN 1 ELSE 0 END) as deleted_records
FROM `walmart_ck`;
