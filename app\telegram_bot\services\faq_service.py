"""
FAQ和帮助文档服务
提供常见问题解答和详细的使用说明
"""

from typing import Dict, List, Optional, Tuple
from telegram import InlineKeyboardButton, InlineKeyboardMarkup

from app.core.logging import get_logger

logger = get_logger(__name__)


class FAQService:
    """FAQ服务"""
    
    def __init__(self, config=None):
        self.config = config
        self.logger = logger
        
        # FAQ数据
        self.faqs = {
            "verification": {
                "title": "🔐 身份验证相关",
                "questions": [
                    {
                        "q": "为什么需要身份验证？",
                        "a": """身份验证是为了保护数据安全，确保只有授权人员可以访问敏感的绑卡统计信息。

🔒 **安全保障**：
• 防止未授权访问
• 保护商户数据隐私
• 建立操作审计记录

👤 **用户权益**：
• 确保数据准确性
• 提供个性化服务
• 建立信任关系"""
                    },
                    {
                        "q": "如何完成身份验证？",
                        "a": """完成身份验证只需3个简单步骤：

**步骤1：申请验证** ⏱️ 1分钟
• 在任意群组或私聊中输入：`/verify`
• 系统会生成您的专属验证令牌

**步骤2：联系管理员** ⏱️ 2分钟
• 将验证令牌发送给系统管理员
• 管理员联系方式：@admin_username

**步骤3：等待审核** ⏱️ 5-30分钟
• 管理员会在系统中审核您的申请
• 审核通过后您会收到通知

💡 **小贴士**：验证令牌有效期为30分钟，请及时发送给管理员。"""
                    },
                    {
                        "q": "验证需要多长时间？",
                        "a": """验证时间取决于管理员的响应速度：

⚡ **通常情况**：5-30分钟
🕐 **工作时间**：周一至周五 9:00-18:00，响应更快
🌙 **非工作时间**：可能需要等到下个工作日

📞 **加急处理**：
• 如有紧急需求，可直接联系管理员
• 电话：+86-xxx-xxxx-xxxx
• 邮箱：<EMAIL>

💡 **建议**：在工作时间申请验证，处理速度更快。"""
                    }
                ]
            },
            "binding": {
                "title": "🔗 群组绑定相关",
                "questions": [
                    {
                        "q": "什么是群组绑定？",
                        "a": """群组绑定是将Telegram群组与特定商户系统关联的过程。

🎯 **绑定目的**：
• 确定群组对应的商户数据
• 设置数据访问权限
• 建立群组与业务的关联

📊 **绑定后可以**：
• 查询该商户的绑卡统计
• 获取实时数据更新
• 使用所有统计功能

⚠️ **注意事项**：
• 只有群组管理员可以执行绑定
• 每个群组只能绑定一个商户
• 绑定后群组成员需要验证身份才能查询数据"""
                    },
                    {
                        "q": "如何绑定群组？",
                        "a": """群组绑定需要管理员权限，步骤如下：

**如果您是群组管理员**：
1. 联系系统管理员获取绑定令牌
2. 在群组中输入：`/bind <绑定令牌>`
3. 等待绑定完成确认

**如果您是普通成员**：
1. 联系群组管理员
2. 告知需要绑定群组到商户系统
3. 提供系统管理员联系方式
4. 等待管理员完成绑定

📝 **令牌格式示例**：
```
/bind tg_bind_WOAGd547pnWHxsHquj2yfAOWyghqT9Hx
```

💡 **小贴士**：绑定令牌有效期24小时，请及时使用。"""
                    },
                    {
                        "q": "绑定失败怎么办？",
                        "a": """绑定失败的常见原因和解决方案：

❌ **令牌无效或过期**
• 检查令牌是否完整复制
• 联系管理员获取新令牌
• 确认在24小时内使用

❌ **权限不足**
• 确认您是群组管理员
• 检查机器人是否已加入群组
• 确认机器人有发送消息权限

❌ **群组已绑定**
• 每个群组只能绑定一次
• 如需更换绑定，联系管理员解绑

🔧 **解决步骤**：
1. 输入 `/status` 查看当前状态
2. 重新获取绑定令牌
3. 确认操作权限
4. 联系技术支持"""
                    }
                ]
            },
            "usage": {
                "title": "📊 功能使用相关",
                "questions": [
                    {
                        "q": "有哪些查询命令？",
                        "a": """机器人提供多种数据查询命令：

📊 **基础查询**：
• `/stats` - 查看今日绑卡数据
• `/stats_week` - 查看本周数据
• `/stats_month` - 查看本月数据

📅 **自定义查询**：
• `/stats_custom 2025-01-01 2025-01-07` - 指定日期范围

⚙️ **状态查询**：
• `/status` - 查看群组和个人状态
• `/help` - 显示帮助信息

💡 **使用技巧**：
• 日期格式：年-月-日（如：2025-01-01）
• 可以随时输入命令获取最新数据
• 支持中文关键词，如"今日"、"本周"等"""
                    },
                    {
                        "q": "数据多久更新一次？",
                        "a": """数据更新频率说明：

⚡ **实时数据**：
• 绑卡操作后立即更新
• 查询时获取最新数据
• 无需等待定时刷新

📊 **统计汇总**：
• 每小时汇总一次
• 日统计：每日凌晨更新
• 周/月统计：每日更新

🔄 **手动刷新**：
• 重新发送查询命令
• 获取最新统计结果
• 无查询次数限制

💡 **最佳实践**：
• 重要决策前建议重新查询
• 定期查看趋势变化
• 关注异常数据波动"""
                    },
                    {
                        "q": "查询失败怎么办？",
                        "a": """查询失败的常见原因和解决方案：

❌ **身份未验证**
• 输入 `/verify` 开始验证
• 联系管理员完成审核
• 确认验证状态：`/status`

❌ **群组未绑定**
• 联系群组管理员
• 获取绑定令牌并执行绑定
• 确认绑定状态：`/status`

❌ **权限不足**
• 检查账户权限状态
• 联系管理员申请权限
• 确认数据访问范围

❌ **系统错误**
• 稍后重试查询
• 检查网络连接
• 联系技术支持

🔧 **快速诊断**：
1. 输入 `/status` 检查状态
2. 确认身份验证和群组绑定
3. 重试查询命令
4. 联系管理员获取帮助"""
                    }
                ]
            },
            "troubleshooting": {
                "title": "🔧 故障排除",
                "questions": [
                    {
                        "q": "机器人没有响应怎么办？",
                        "a": """机器人无响应的排查步骤：

🔍 **基础检查**：
• 确认机器人已加入群组
• 检查机器人是否被禁言
• 确认命令格式正确

⚙️ **权限检查**：
• 机器人需要发送消息权限
• 群组管理员可以检查权限设置
• 确认机器人未被移除

🔄 **重试操作**：
• 等待1-2分钟后重试
• 尝试在私聊中使用命令
• 重新启动Telegram应用

📞 **联系支持**：
• 如问题持续，联系技术支持
• 提供具体的错误信息
• 说明操作时间和群组信息"""
                    },
                    {
                        "q": "如何联系技术支持？",
                        "a": """多种方式联系技术支持：

📞 **联系方式**：
• **Telegram**：@admin_username
• **邮箱**：<EMAIL>
• **电话**：+86-xxx-xxxx-xxxx

⏰ **工作时间**：
• 周一至周五 9:00-18:00
• 节假日可能延迟响应
• 紧急问题可电话联系

📝 **联系时请提供**：
• 您的Telegram用户名
• 遇到的具体问题
• 错误发生的时间
• 相关的群组信息
• 已尝试的解决方法

💡 **获得更快响应**：
• 详细描述问题
• 提供错误截图
• 说明业务紧急程度"""
                    }
                ]
            }
        }
    
    def get_faq_categories(self) -> List[Tuple[str, str]]:
        """获取FAQ分类列表"""
        return [(key, value["title"]) for key, value in self.faqs.items()]
    
    def get_category_questions(self, category: str) -> Optional[Dict]:
        """获取指定分类的问题列表"""
        return self.faqs.get(category)
    
    def search_faq(self, keyword: str) -> List[Tuple[str, str, str]]:
        """搜索FAQ"""
        results = []
        keyword_lower = keyword.lower()
        
        for category_key, category_data in self.faqs.items():
            for qa in category_data["questions"]:
                if (keyword_lower in qa["q"].lower() or 
                    keyword_lower in qa["a"].lower()):
                    results.append((category_key, qa["q"], qa["a"]))
        
        return results
    
    def format_faq_list(self, category: str) -> Tuple[str, InlineKeyboardMarkup]:
        """格式化FAQ列表"""
        category_data = self.faqs.get(category)
        if not category_data:
            return "❌ 分类不存在", None
        
        message = f"""{category_data['title']}

📋 **常见问题**：

"""
        
        buttons = []
        for i, qa in enumerate(category_data["questions"]):
            message += f"{i+1}. {qa['q']}\n"
            buttons.append([InlineKeyboardButton(
                f"{i+1}. {qa['q']}", 
                callback_data=f"faq_{category}_{i}"
            )])
        
        buttons.append([InlineKeyboardButton("🔙 返回分类", callback_data="faq_categories")])
        
        return message, InlineKeyboardMarkup(buttons)
    
    def format_faq_answer(self, category: str, question_index: int) -> Tuple[str, InlineKeyboardMarkup]:
        """格式化FAQ答案"""
        category_data = self.faqs.get(category)
        if not category_data or question_index >= len(category_data["questions"]):
            return "❌ 问题不存在", None
        
        qa = category_data["questions"][question_index]
        
        message = f"""❓ **{qa['q']}**

{qa['a']}

---
💡 **还有其他问题？** 输入 `/help` 获取更多帮助"""
        
        keyboard = InlineKeyboardMarkup([
            [InlineKeyboardButton("🔙 返回问题列表", callback_data=f"faq_category_{category}")],
            [InlineKeyboardButton("📞 联系支持", callback_data="contact_support")]
        ])
        
        return message, keyboard
