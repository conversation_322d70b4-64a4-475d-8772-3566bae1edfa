#!/usr/bin/env python3
"""
CK计数一致性修复脚本
修复沃尔玛绑卡系统中CK bind_count与实际绑卡记录不一致的问题
"""

import asyncio
import sys
import os
from datetime import datetime
from typing import Dict, List, Any, Optional

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from sqlalchemy.orm import Session
from app.db.session import SessionLocal
from app.models.walmart_ck import WalmartCK
from app.models.card_record import CardRecord
from app.core.logging import get_logger

logger = get_logger(__name__)


class CKCountConsistencyFixer:
    """CK计数一致性修复器"""
    
    def __init__(self):
        self.db: Session = SessionLocal()
        self.stats = {
            'total_cks_checked': 0,
            'inconsistent_cks_found': 0,
            'cks_fixed': 0,
            'errors': 0,
            'details': []
        }
    
    def __del__(self):
        if hasattr(self, 'db'):
            self.db.close()
    
    async def run_fix(self, merchant_id: Optional[int] = None, dry_run: bool = False) -> Dict[str, Any]:
        """
        运行CK计数一致性修复
        
        Args:
            merchant_id: 指定商户ID，None表示修复所有商户
            dry_run: 是否为试运行模式（不实际修复）
            
        Returns:
            Dict[str, Any]: 修复结果统计
        """
        logger.info("🔧 开始CK计数一致性修复")
        logger.info(f"参数: merchant_id={merchant_id}, dry_run={dry_run}")
        
        try:
            # 1. 获取需要检查的CK列表
            cks_to_check = await self._get_cks_to_check(merchant_id)
            self.stats['total_cks_checked'] = len(cks_to_check)
            
            logger.info(f"📊 找到 {len(cks_to_check)} 个CK需要检查")
            
            # 2. 检查每个CK的计数一致性
            for ck in cks_to_check:
                await self._check_and_fix_ck(ck, dry_run)
            
            # 3. 生成修复报告
            await self._generate_report()
            
            return self.stats
            
        except Exception as e:
            logger.error(f"❌ CK计数一致性修复失败: {e}")
            self.stats['errors'] += 1
            raise
    
    async def _get_cks_to_check(self, merchant_id: Optional[int] = None) -> List[WalmartCK]:
        """获取需要检查的CK列表"""
        try:
            query = self.db.query(WalmartCK).filter(
                WalmartCK.is_deleted == False
            )
            
            if merchant_id:
                query = query.filter(WalmartCK.merchant_id == merchant_id)
            
            cks = query.all()
            logger.info(f"获取到 {len(cks)} 个CK进行检查")
            return cks
            
        except Exception as e:
            logger.error(f"获取CK列表失败: {e}")
            raise
    
    async def _check_and_fix_ck(self, ck: WalmartCK, dry_run: bool = False):
        """检查并修复单个CK的计数一致性"""
        try:
            # 计算实际的绑卡成功次数
            actual_count = self.db.query(CardRecord).filter(
                CardRecord.walmart_ck_id == ck.id,
                CardRecord.status == 'success'
            ).count()
            
            current_count = ck.bind_count
            
            # 检查是否一致
            if actual_count == current_count:
                logger.debug(f"✅ CK {ck.id}: 计数一致 (bind_count={current_count})")
                return
            
            # 发现不一致
            self.stats['inconsistent_cks_found'] += 1
            difference = actual_count - current_count
            
            detail = {
                'ck_id': ck.id,
                'merchant_id': ck.merchant_id,
                'department_id': ck.department_id,
                'current_count': current_count,
                'actual_count': actual_count,
                'difference': difference,
                'fixed': False
            }
            
            logger.warning(
                f"❌ CK {ck.id}: 计数不一致 | "
                f"当前bind_count={current_count}, 实际成功绑卡={actual_count}, 差异={difference}"
            )
            
            # 如果不是试运行，执行修复
            if not dry_run:
                try:
                    ck.bind_count = actual_count
                    self.db.commit()
                    
                    detail['fixed'] = True
                    self.stats['cks_fixed'] += 1
                    
                    logger.info(f"🔧 CK {ck.id}: 修复成功 | bind_count更新为 {actual_count}")
                    
                except Exception as fix_error:
                    self.db.rollback()
                    logger.error(f"修复CK {ck.id} 失败: {fix_error}")
                    self.stats['errors'] += 1
                    detail['error'] = str(fix_error)
            else:
                logger.info(f"🔍 试运行模式: CK {ck.id} 需要修复 (bind_count: {current_count} -> {actual_count})")
            
            self.stats['details'].append(detail)
            
        except Exception as e:
            logger.error(f"检查CK {ck.id} 时发生错误: {e}")
            self.stats['errors'] += 1
    
    async def _generate_report(self):
        """生成修复报告"""
        logger.info("=" * 80)
        logger.info("📊 CK计数一致性修复报告")
        logger.info("=" * 80)
        
        logger.info(f"检查的CK总数: {self.stats['total_cks_checked']}")
        logger.info(f"发现不一致的CK数: {self.stats['inconsistent_cks_found']}")
        logger.info(f"成功修复的CK数: {self.stats['cks_fixed']}")
        logger.info(f"修复过程中的错误数: {self.stats['errors']}")
        
        if self.stats['details']:
            logger.info("\n详细信息:")
            for detail in self.stats['details']:
                status = "✅ 已修复" if detail['fixed'] else "❌ 未修复"
                logger.info(
                    f"  CK {detail['ck_id']}: {detail['current_count']} -> {detail['actual_count']} "
                    f"(差异: {detail['difference']}) {status}"
                )
        
        logger.info("=" * 80)


async def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description="CK计数一致性修复工具")
    parser.add_argument("--merchant-id", type=int, help="指定商户ID")
    parser.add_argument("--dry-run", action="store_true", help="试运行模式，不实际修复")
    parser.add_argument("--auto", action="store_true", help="自动执行，不需要确认")
    
    args = parser.parse_args()
    
    if not args.auto:
        print("🔧 CK计数一致性修复工具")
        print("=" * 60)
        print("此工具将:")
        print("1. 检查所有CK的bind_count与实际绑卡成功记录是否一致")
        print("2. 修复发现的不一致问题")
        print("3. 生成详细的修复报告")
        print()
        
        if args.dry_run:
            print("⚠️  当前为试运行模式，不会实际修复数据")
        else:
            print("⚠️  此操作将直接修改数据库中的CK计数")
        
        response = input("是否继续? (y/N): ")
        if response.lower() not in ['y', 'yes']:
            print("操作已取消")
            return
    
    # 执行修复
    fixer = CKCountConsistencyFixer()
    try:
        result = await fixer.run_fix(
            merchant_id=args.merchant_id,
            dry_run=args.dry_run
        )
        
        if result['errors'] == 0:
            logger.info("🎉 修复完成！")
            sys.exit(0)
        else:
            logger.error("❌ 修复过程中遇到错误")
            sys.exit(1)
            
    except Exception as e:
        logger.error(f"修复失败: {e}")
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())
