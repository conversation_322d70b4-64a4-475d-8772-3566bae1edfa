<template>
  <div class="test-container">
    <el-card shadow="hover" class="test-card">
      <template #header>
        <div class="card-header">
          <h2>订单号生成测试</h2>
          <span>测试订单号自动生成功能</span>
        </div>
      </template>

      <div class="test-form">
        <el-row :gutter="24">
          <el-col :xs="24" :sm="12">
            <div class="form-item">
              <label class="form-label">商户代码</label>
              <el-input v-model="merchantCode" placeholder="请输入商户代码（可选）" clearable />
            </div>
          </el-col>
          <el-col :xs="24" :sm="12">
            <div class="form-item">
              <label class="form-label">订单号前缀</label>
              <el-input v-model="prefix" placeholder="请输入前缀（默认WM）" clearable />
            </div>
          </el-col>
        </el-row>

        <div class="form-item">
          <label class="form-label">
            生成的订单号
            <el-button
              type="primary"
              size="small"
              :icon="Refresh"
              @click="generateNewOrderId"
              style="margin-left: 8px;"
            >
              重新生成
            </el-button>
          </label>
          <el-input 
            v-model="generatedOrderId" 
            placeholder="点击生成按钮生成订单号" 
            readonly
          />
          <div class="order-info" v-if="generatedOrderId">
            <el-tag type="success" size="small">
              长度: {{ generatedOrderId.length }}
            </el-tag>
            <el-tag type="info" size="small" style="margin-left: 8px;">
              格式化: {{ formatOrderId(generatedOrderId) }}
            </el-tag>
            <el-tag 
              :type="isSystemGenerated ? 'warning' : 'primary'" 
              size="small" 
              style="margin-left: 8px;"
            >
              {{ isSystemGenerated ? '系统生成' : '用户输入' }}
            </el-tag>
          </div>
        </div>

        <div class="form-item">
          <label class="form-label">批量生成测试</label>
          <el-button type="success" @click="generateBatch" :icon="Document">
            生成10个订单号
          </el-button>
          <div class="batch-results" v-if="batchOrderIds.length > 0">
            <el-table :data="batchOrderIds" stripe style="width: 100%" max-height="300">
              <el-table-column prop="index" label="序号" width="60" />
              <el-table-column prop="orderId" label="订单号" width="200" />
              <el-table-column prop="formatted" label="格式化显示" width="250" />
              <el-table-column prop="length" label="长度" width="80" />
              <el-table-column prop="timestamp" label="生成时间" />
            </el-table>
          </div>
        </div>

        <div class="form-item">
          <label class="form-label">订单号验证测试</label>
          <el-input 
            v-model="testOrderId" 
            placeholder="请输入要验证的订单号" 
            clearable
          />
          <div class="validation-result" v-if="testOrderId">
            <el-tag 
              :type="isValidOrderId ? 'success' : 'danger'" 
              size="small"
            >
              {{ isValidOrderId ? '格式有效' : '格式无效' }}
            </el-tag>
            <el-tag 
              :type="isTestSystemGenerated ? 'warning' : 'info'" 
              size="small" 
              style="margin-left: 8px;"
            >
              {{ isTestSystemGenerated ? '系统生成格式' : '自定义格式' }}
            </el-tag>
          </div>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { Refresh, Document } from '@element-plus/icons-vue'
import { 
  generateMerchantOrderId, 
  isSystemGeneratedOrderId, 
  formatOrderIdDisplay,
  validateOrderId 
} from '@/utils/orderUtils'

// 响应式数据
const merchantCode = ref('')
const prefix = ref('WM')
const generatedOrderId = ref('')
const batchOrderIds = ref([])
const testOrderId = ref('')

// 计算属性
const isSystemGenerated = computed(() => {
  return isSystemGeneratedOrderId(generatedOrderId.value)
})

const isValidOrderId = computed(() => {
  return validateOrderId(testOrderId.value)
})

const isTestSystemGenerated = computed(() => {
  return isSystemGeneratedOrderId(testOrderId.value)
})

// 方法
const generateNewOrderId = () => {
  generatedOrderId.value = generateMerchantOrderId(
    prefix.value || 'WM', 
    merchantCode.value
  )
  ElMessage.success('订单号已生成')
}

const formatOrderId = (orderId) => {
  return formatOrderIdDisplay(orderId)
}

const generateBatch = () => {
  batchOrderIds.value = []
  const startTime = Date.now()
  
  for (let i = 1; i <= 10; i++) {
    const orderId = generateMerchantOrderId(
      prefix.value || 'WM', 
      merchantCode.value
    )
    
    batchOrderIds.value.push({
      index: i,
      orderId: orderId,
      formatted: formatOrderIdDisplay(orderId),
      length: orderId.length,
      timestamp: new Date().toLocaleTimeString()
    })
    
    // 添加小延迟确保时间戳不同
    if (i < 10) {
      // 使用同步延迟
      const now = Date.now()
      while (Date.now() - now < 1) {
        // 等待1毫秒
      }
    }
  }
  
  ElMessage.success(`批量生成完成，耗时 ${Date.now() - startTime}ms`)
}

// 初始化时生成一个订单号
generateNewOrderId()
</script>

<style scoped>
.test-container {
  padding: 20px;
  max-width: 1000px;
  margin: 0 auto;
}

.test-card {
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-header h2 {
  margin: 0;
  font-size: 1.3rem;
  color: var(--el-text-color-primary);
  font-weight: 600;
}

.form-item {
  margin-bottom: 24px;
}

.form-label {
  display: block;
  margin-bottom: 8px;
  font-size: 14px;
  color: var(--el-text-color-regular);
  line-height: 1.5;
}

.order-info {
  margin-top: 8px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.batch-results {
  margin-top: 16px;
}

.validation-result {
  margin-top: 8px;
  display: flex;
  align-items: center;
  gap: 8px;
}
</style>
