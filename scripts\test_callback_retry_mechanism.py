#!/usr/bin/env python3
"""
测试回调服务的智能重试机制
"""
import sys
import asyncio
import uuid
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from app.db.session import SessionLocal
from app.services.optimized_callback_service import optimized_callback_service
from app.core.logging import get_logger

logger = get_logger("callback_retry_test")


async def test_retry_mechanism():
    """测试智能重试机制"""
    print("🔄 测试智能重试机制...")
    
    # 测试1: 不存在的记录
    print("\n1. 测试不存在记录的重试机制...")
    test_record_id = str(uuid.uuid4())
    
    try:
        with SessionLocal() as db:
            result = await optimized_callback_service._validate_callback_prerequisites(
                db, uuid.UUID(test_record_id), 2, 0
            )
            
            if result == (None, None):
                print("   ✅ 智能重试机制正确处理了不存在的记录")
            else:
                print("   ❌ 重试机制返回了意外结果")
                return False
                
    except Exception as e:
        print(f"   ❌ 重试机制测试失败: {e}")
        return False
    
    # 测试2: 查询现有记录
    print("\n2. 测试查询现有记录...")
    try:
        with SessionLocal() as db:
            # 查询最新的一条记录
            from sqlalchemy import text
            result = db.execute(text(
                "SELECT id FROM card_records WHERE status = 'success' ORDER BY created_at DESC LIMIT 1"
            )).fetchone()
            
            if result:
                existing_record_id = result[0]
                print(f"   找到现有记录: {existing_record_id}")
                
                # 测试查询现有记录
                record, merchant = await optimized_callback_service._validate_callback_prerequisites(
                    db, uuid.UUID(existing_record_id), 2, 0
                )
                
                if record:
                    print(f"   ✅ 成功查询到记录: {record.id}")
                    print(f"   记录状态: {record.status}")
                    print(f"   回调状态: {record.callback_status}")
                else:
                    print("   ⚠️  记录存在但查询结果为空（可能是状态或商户问题）")
            else:
                print("   ⚠️  数据库中没有成功状态的记录")
                
    except Exception as e:
        print(f"   ❌ 查询现有记录失败: {e}")
        return False
    
    print("\n✅ 智能重试机制测试完成")
    return True


async def test_callback_processing_with_retry():
    """测试完整的回调处理流程（包含重试）"""
    print("\n🧪 测试完整回调处理流程...")
    
    # 模拟回调数据
    test_data = {
        "record_id": str(uuid.uuid4()),  # 不存在的记录
        "merchant_id": 2,
        "retry_count": 0,
        "ext_data": None,
        "trace_id": "test-retry-mechanism"
    }
    
    print(f"   测试记录ID: {test_data['record_id']}")
    
    try:
        with SessionLocal() as db:
            # 测试完整的回调处理流程
            result = await optimized_callback_service._process_single_callback(db, test_data)
            print(f"   处理结果: {result}")
            
            # 对于不存在的记录，应该返回 True（表示处理完成，不需要重试）
            if result is True:
                print("   ✅ 回调处理正确处理了不存在的记录")
            else:
                print("   ⚠️  回调处理返回了意外结果")
                
    except Exception as e:
        print(f"   ❌ 回调处理测试失败: {e}")
        return False
    
    return True


async def test_database_transaction_isolation():
    """测试数据库事务隔离问题"""
    print("\n🔍 测试数据库事务隔离...")
    
    try:
        # 创建两个独立的数据库会话
        with SessionLocal() as db1, SessionLocal() as db2:
            # 查询记录数量
            from sqlalchemy import text
            
            count1 = db1.execute(text("SELECT COUNT(*) FROM card_records")).fetchone()[0]
            count2 = db2.execute(text("SELECT COUNT(*) FROM card_records")).fetchone()[0]
            
            print(f"   会话1查询结果: {count1}")
            print(f"   会话2查询结果: {count2}")
            
            if count1 == count2:
                print("   ✅ 数据库事务隔离正常")
            else:
                print("   ⚠️  数据库事务隔离可能存在问题")
                
    except Exception as e:
        print(f"   ❌ 事务隔离测试失败: {e}")
        return False
    
    return True


async def main():
    """主函数"""
    print("=" * 60)
    print("回调服务智能重试机制测试")
    print("=" * 60)
    
    try:
        # 测试重试机制
        success1 = await test_retry_mechanism()
        
        # 测试完整回调处理
        success2 = await test_callback_processing_with_retry()
        
        # 测试数据库事务隔离
        success3 = await test_database_transaction_isolation()
        
        if success1 and success2 and success3:
            print("\n✅ 所有测试通过！智能重试机制工作正常。")
            print("\n📝 重试机制说明:")
            print("   - 当记录不存在时，会进行3次重试")
            print("   - 每次重试间隔递增：50ms, 100ms, 150ms")
            print("   - 这解决了数据库事务隔离导致的时序问题")
            print("   - 对于真正不存在的记录，会优雅地返回None")
            return 0
        else:
            print("\n❌ 部分测试失败，请检查重试机制。")
            return 1
            
    except Exception as e:
        print(f"\n💥 测试过程中发生异常: {e}")
        return 1
    finally:
        # 清理资源
        try:
            await optimized_callback_service.close()
            print("\n🧹 资源清理完成")
        except Exception as e:
            print(f"\n⚠️  资源清理异常: {e}")


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
