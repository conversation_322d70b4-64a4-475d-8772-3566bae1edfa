from sqlalchemy import Column, BigInteger, ForeignKey, Table, DateTime
from sqlalchemy.sql import func

from app.models.base import BaseModel


# 用户-角色多对多关系表
user_roles = Table(
    "user_roles",
    BaseModel.metadata,
    Column("id", BigInteger, primary_key=True, autoincrement=True, comment="关联ID"),
    Column("user_id", BigInteger, ForeignKey("users.id", ondelete="CASCADE"), nullable=False, comment="用户ID"),
    Column("role_id", BigInteger, ForeignKey("roles.id", ondelete="CASCADE"), nullable=False, comment="角色ID"),
    Column("created_at", DateTime(timezone=True), nullable=False, default=func.now(), comment="创建时间"),
)

# 用户-权限多对多关系表
user_permissions = Table(
    "user_permissions",
    BaseModel.metadata,
    Column("id", BigInteger, primary_key=True, autoincrement=True, comment="关联ID"),
    Column("user_id", BigInteger, ForeignKey("users.id", ondelete="CASCADE"), nullable=False, comment="用户ID"),
    Column("permission_id", BigInteger, ForeignKey("permissions.id", ondelete="CASCADE"), nullable=False, comment="权限ID"),
    Column("created_at", DateTime(timezone=True), nullable=False, default=func.now(), comment="创建时间"),
)

# 角色-权限多对多关系表（基于ID的外键关联）
role_permissions = Table(
    "role_permissions",
    BaseModel.metadata,
    Column("id", BigInteger, primary_key=True, autoincrement=True, comment="关联ID"),
    Column("role_id", BigInteger, ForeignKey("roles.id", ondelete="CASCADE"), nullable=False, comment="角色ID"),
    Column("permission_id", BigInteger, ForeignKey("permissions.id", ondelete="CASCADE"), nullable=False, comment="权限ID"),
    Column("created_at", DateTime(timezone=True), nullable=False, default=func.now(), comment="创建时间"),
)

# 角色-菜单多对多关系表（基于ID的外键关联）
role_menus = Table(
    "role_menus",
    BaseModel.metadata,
    Column("id", BigInteger, primary_key=True, autoincrement=True, comment="关联ID"),
    Column("role_id", BigInteger, ForeignKey("roles.id", ondelete="CASCADE"), nullable=False, comment="角色ID"),
    Column("menu_id", BigInteger, ForeignKey("menus.id", ondelete="CASCADE"), nullable=False, comment="菜单ID"),
    Column("created_at", DateTime(timezone=True), nullable=False, default=func.now(), comment="创建时间"),
)

# 菜单-权限多对多关系表
menu_permissions = Table(
    "menu_permissions",
    BaseModel.metadata,
    Column("id", BigInteger, primary_key=True, autoincrement=True, comment="关联ID"),
    Column("menu_id", BigInteger, ForeignKey("menus.id", ondelete="CASCADE"), nullable=False, comment="菜单ID"),
    Column("permission_id", BigInteger, ForeignKey("permissions.id", ondelete="CASCADE"), nullable=False, comment="权限ID"),
    Column("created_at", DateTime(timezone=True), nullable=False, default=func.now(), comment="创建时间"),
)
