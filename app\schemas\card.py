from typing import Optional, Dict, Any, List
from datetime import datetime
from pydantic import BaseModel, Field, field_validator
import uuid

from app.models import CardStatus
from app.models.card_record import CallbackStatus


# 绑卡需要的参数
class BindCardParams(BaseModel):
    card_number: str = Field(..., description="卡号", min_length=1, max_length=50)
    card_password: str = Field(..., description="卡密码", min_length=1, max_length=50)
    merchant_code: str = Field(..., description="商家编码", min_length=1, max_length=50)
    merchant_order_id: str = Field(..., description="商户订单号", min_length=1, max_length=255)
    amount: int = Field(
        ..., ge=100, description="订单金额，单位：分，必须大于等于100 (1元)"
    )
    ext_data: Optional[str] = Field(
        None, description="扩展数据，回调时原样返回", max_length=512
    )
    debug: Optional[bool] = Field(
        False, description="调试模式，为true时跳过真实API调用，使用模拟数据进行并发测试"
    )

    @field_validator('card_number')
    @classmethod
    def validate_card_number(cls, v):
        """验证卡号"""
        if not v or not isinstance(v, str):
            raise ValueError("卡号不能为空")

        v = v.strip()
        if not v:
            raise ValueError("卡号不能为空字符串")

        # 可以添加更多卡号格式验证
        if len(v) < 6:
            raise ValueError("卡号长度不能少于6位")

        return v

    @field_validator('card_password')
    @classmethod
    def validate_card_password(cls, v):
        """验证卡密码"""
        if not v or not isinstance(v, str):
            raise ValueError("卡密码不能为空")

        v = v.strip()
        if not v:
            raise ValueError("卡密码不能为空字符串")

        return v

    @field_validator('merchant_code')
    @classmethod
    def validate_merchant_code(cls, v):
        """验证商户编码"""
        if not v or not isinstance(v, str):
            raise ValueError("商户编码不能为空")

        v = v.strip()
        if not v:
            raise ValueError("商户编码不能为空字符串")

        return v

    @field_validator('merchant_order_id')
    @classmethod
    def validate_merchant_order_id(cls, v):
        """验证商户订单号"""
        if not v or not isinstance(v, str):
            raise ValueError("商户订单号不能为空")

        v = v.strip()
        if not v:
            raise ValueError("商户订单号不能为空字符串")

        return v


# 基础卡记录模型
class CardRecordBase(BaseModel):
    card_number: str = Field(..., description="卡号")
    card_password: Optional[str] = Field(None, description="卡密码")
    merchant_order_id: str = Field(..., description="商户订单号")
    amount: int = Field(..., description="订单金额，单位：分")
    actual_amount: Optional[int] = Field(None, description="实际卡金额，单位：分")


# 创建卡记录请求
class CardRecordCreate(CardRecordBase):
    # 可选的额外字段
    merchant_id: Optional[int] = Field(None, description="商家ID")
    user_id: Optional[str] = Field(None, description="用户ID")
    request_id: str = Field(..., description="请求ID")
    trace_id: Optional[str] = Field(None, description="追踪ID")
    status: str = Field(default=CardStatus.PENDING, description="状态")
    request_data: Dict[str, Any] = Field(default_factory=dict, description="请求数据")
    ext_data: Optional[str] = Field(None, description="扩展数据")
    ip_address: Optional[str] = Field(None, description="IP地址")
    # 新增真实金额字段
    balance: Optional[str] = Field(None, description="balance字段，单位元，原始格式")
    cardBalance: Optional[str] = Field(
        None, description="cardBalance字段，单位元，原始格式"
    )
    balanceCnt: Optional[str] = Field(
        None, description="balanceCnt字段，单位元，原始格式"
    )

    @field_validator("balance", "cardBalance", "balanceCnt", mode="before")
    @classmethod
    def float_to_str(cls, v):
        if v is None:
            return v
        return str(v)


# 更新卡记录状态请求
class CardRecordUpdate(BaseModel):
    status: Optional[str] = Field(None, description="绑定状态")
    process_time: Optional[float] = Field(None, description="处理时间(秒)")
    error_message: Optional[str] = Field(None, description="错误信息")
    response_data: Optional[Dict[str, Any]] = Field(None, description="响应数据")
    actual_amount: Optional[int] = Field(None, description="实际卡金额，单位：分")
    # 新增CK相关字段（关键修复）
    walmart_ck_id: Optional[int] = Field(None, description="使用的沃尔玛CK ID（绑卡成功后填入）")
    department_id: Optional[int] = Field(None, description="所属部门ID（绑卡成功后填入）")
    # 新增真实金额字段
    balance: Optional[str] = Field(None, description="balance字段，单位元，原始格式")
    cardBalance: Optional[str] = Field(
        None, description="cardBalance字段，单位元，原始格式"
    )
    balanceCnt: Optional[str] = Field(
        None, description="balanceCnt字段，单位元，原始格式"
    )

    @field_validator("balance", "cardBalance", "balanceCnt", mode="before")
    @classmethod
    def float_to_str(cls, v):
        if v is None:
            return v
        return str(v)


# 卡记录过滤条件
class CardRecordFilter(BaseModel):
    card_number: Optional[str] = Field(None, description="卡号")
    user_id: Optional[str] = Field(None, description="用户ID")
    status: Optional[str] = Field(None, description="状态")
    start_time: Optional[datetime] = Field(None, description="开始时间")
    end_time: Optional[datetime] = Field(None, description="结束时间")
    merchant_id: Optional[int] = Field(None, description="商家ID")

    @field_validator("status", mode="before")
    @classmethod
    def validate_status(cls, v):
        if not v or v == "" or v == "null" or v == "undefined":
            return None
        # 确保状态值有效
        try:
            if v in CardStatus.get_all_values():
                return v
            # 如果不是有效的状态值，返回None
            return None
        except Exception:
            return None

    @field_validator("start_time", "end_time", mode="before")
    @classmethod
    def validate_datetime(cls, v):
        if not v or v == "" or v == "null" or v == "undefined":
            return None
        # 如果已经是datetime对象，直接返回
        if isinstance(v, datetime):
            return v
        # 尝试解析字符串为datetime
        try:
            # 尝试多种格式
            formats = ["%Y-%m-%d %H:%M:%S", "%Y-%m-%d", "%Y/%m/%d %H:%M:%S", "%Y/%m/%d"]
            for fmt in formats:
                try:
                    return datetime.strptime(v, fmt)
                except ValueError:
                    continue
            # 如果所有格式都失败，返回None
            return None
        except Exception:
            return None


# 返回给前端的卡记录
class CardRecordOut(BaseModel):
    id: uuid.UUID
    merchant_order_id: str
    amount: int
    actual_amount: Optional[int] = None
    cardNumber: str
    status: str
    requestTime: datetime
    processTime: Optional[datetime] = None
    failReason: Optional[str] = None
    requestParams: Optional[Dict[str, Any]] = None
    responseData: Optional[Dict[str, Any]] = None
    merchantId: int
    callback_status: str
    callback_result: Optional[str] = None
    createdAt: datetime
    updatedAt: datetime

    class Config:
        from_attributes = True


# 统计数据响应
class HourlyData(BaseModel):
    """小时数据"""

    hour: str
    count: int


class CardStatistics(BaseModel):
    """卡记录统计数据"""

    total_count: int = 0
    success_count: int = 0
    failed_count: int = 0
    pending_count: int = 0
    success_rate: float = 0.0
    hourly_data: List[HourlyData] = []

    class Config:
        json_schema_extra = {
            "example": {
                "total_count": 100,
                "success_count": 80,
                "failed_count": 15,
                "pending_count": 5,
                "success_rate": 80.0,
                "hourly_data": [
                    {"hour": "00:00", "count": 5},
                    {"hour": "01:00", "count": 3},
                    # ... other hours
                ],
            }
        }


# 分页响应
class CardRecordPage(BaseModel):
    items: List[CardRecordOut]
    total: int
    page: int
    pageSize: int
