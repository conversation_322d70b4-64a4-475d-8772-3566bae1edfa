"""
部门创建权限测试
测试不同角色用户创建部门的权限逻辑
"""
import pytest
import requests
import json
from typing import Dict, Any

# 测试配置
BASE_URL = "http://localhost:20000"
API_BASE = f"{BASE_URL}/api/v1"

class TestDepartmentCreatePermission:
    """部门创建权限测试类"""

    def setup_method(self):
        """测试前准备"""
        self.super_admin_token = None
        self.merchant_admin_token = None
        self.test_merchant_id = None
        self.created_department_ids = []

    def teardown_method(self):
        """测试后清理"""
        # 清理创建的部门
        if self.super_admin_token:
            for dept_id in self.created_department_ids:
                try:
                    requests.delete(
                        f"{API_BASE}/departments/{dept_id}",
                        headers={"Authorization": f"Bearer {self.super_admin_token}"}
                    )
                except:
                    pass

    def login_user(self, username: str, password: str) -> str:
        """用户登录获取token"""
        # 使用form-data格式，符合OAuth2PasswordRequestForm要求
        form_data = {
            "username": username,
            "password": password
        }
        response = requests.post(f"{API_BASE}/auth/login", data=form_data)
        print(f"登录响应: {response.status_code}, {response.text}")
        assert response.status_code == 200, f"登录失败: {response.text}"
        return response.json()["data"]["access_token"]

    def get_user_info(self, token: str) -> Dict[str, Any]:
        """获取用户信息"""
        response = requests.get(
            f"{API_BASE}/users/me",
            headers={"Authorization": f"Bearer {token}"}
        )
        assert response.status_code == 200, f"获取用户信息失败: {response.text}"
        return response.json()["data"]

    def test_super_admin_create_department_with_merchant_id(self):
        """测试超级管理员创建部门（指定merchant_id）"""
        # 1. 超级管理员登录
        self.super_admin_token = self.login_user("admin", "7c222fb2927d828af22f592134e8932480637c0d")

        # 2. 获取测试商户ID
        merchants_response = requests.get(
            f"{API_BASE}/merchants",
            headers={"Authorization": f"Bearer {self.super_admin_token}"}
        )
        assert merchants_response.status_code == 200
        merchants = merchants_response.json()["data"]["items"]
        assert len(merchants) > 0, "没有可用的测试商户"
        self.test_merchant_id = merchants[0]["id"]

        # 3. 创建部门（指定merchant_id）
        dept_data = {
            "merchant_id": self.test_merchant_id,
            "name": "测试部门-超管创建",
            "code": f"TEST_SUPER_{self.test_merchant_id}",
            "description": "超级管理员创建的测试部门"
        }

        response = requests.post(
            f"{API_BASE}/departments",
            headers={"Authorization": f"Bearer {self.super_admin_token}"},
            json=dept_data
        )

        print(f"超级管理员创建部门响应: {response.status_code}, {response.text}")
        assert response.status_code == 200, f"超级管理员创建部门失败: {response.text}"

        dept = response.json()["data"]
        self.created_department_ids.append(dept["id"])
        assert dept["merchant_id"] == self.test_merchant_id
        assert dept["name"] == "测试部门-超管创建"

    def test_super_admin_create_department_without_merchant_id(self):
        """测试超级管理员创建部门（不指定merchant_id，应该失败）"""
        # 1. 超级管理员登录
        if not self.super_admin_token:
            self.super_admin_token = self.login_user("admin", "7c222fb2927d828af22f592134e8932480637c0d")

        # 2. 创建部门（不指定merchant_id）
        dept_data = {
            "name": "测试部门-无商户ID",
            "code": "TEST_NO_MERCHANT",
            "description": "不指定商户ID的测试部门"
        }

        response = requests.post(
            f"{API_BASE}/departments",
            headers={"Authorization": f"Bearer {self.super_admin_token}"},
            json=dept_data
        )

        print(f"超级管理员不指定merchant_id创建部门响应: {response.status_code}, {response.text}")
        assert response.status_code == 400, "超级管理员不指定merchant_id应该失败"
        assert "必须指定商户ID" in response.json()["message"]

    def test_merchant_admin_create_department(self):
        """测试商户管理员创建部门（自动使用自己的merchant_id）"""
        # 1. 商户管理员登录
        self.merchant_admin_token = self.login_user("test1", "12345678")

        # 2. 获取商户管理员信息
        user_info = self.get_user_info(self.merchant_admin_token)
        merchant_id = user_info["merchant_id"]
        assert merchant_id is not None, "商户管理员必须关联商户"

        # 3. 创建部门（不指定merchant_id）
        dept_data = {
            "name": "测试部门-商户管理员创建",
            "code": f"TEST_MERCHANT_{merchant_id}",
            "description": "商户管理员创建的测试部门"
        }

        response = requests.post(
            f"{API_BASE}/departments",
            headers={"Authorization": f"Bearer {self.merchant_admin_token}"},
            json=dept_data
        )

        print(f"商户管理员创建部门响应: {response.status_code}, {response.text}")
        assert response.status_code == 200, f"商户管理员创建部门失败: {response.text}"

        dept = response.json()["data"]
        self.created_department_ids.append(dept["id"])
        assert dept["merchant_id"] == merchant_id
        assert dept["name"] == "测试部门-商户管理员创建"

    def test_merchant_admin_create_department_with_wrong_merchant_id(self):
        """测试商户管理员创建部门（指定错误的merchant_id，应该被忽略）"""
        # 1. 商户管理员登录
        if not self.merchant_admin_token:
            self.merchant_admin_token = self.login_user("test1", "12345678")

        # 2. 获取商户管理员信息
        user_info = self.get_user_info(self.merchant_admin_token)
        merchant_id = user_info["merchant_id"]

        # 3. 创建部门（指定错误的merchant_id）
        dept_data = {
            "merchant_id": 99999,  # 错误的merchant_id
            "name": "测试部门-错误商户ID",
            "code": f"TEST_WRONG_{merchant_id}",
            "description": "指定错误商户ID的测试部门"
        }

        response = requests.post(
            f"{API_BASE}/departments",
            headers={"Authorization": f"Bearer {self.merchant_admin_token}"},
            json=dept_data
        )

        print(f"商户管理员指定错误merchant_id创建部门响应: {response.status_code}, {response.text}")
        assert response.status_code == 200, f"商户管理员创建部门失败: {response.text}"

        # 验证实际使用的是正确的merchant_id
        dept = response.json()["data"]
        self.created_department_ids.append(dept["id"])
        assert dept["merchant_id"] == merchant_id, "应该使用商户管理员自己的merchant_id"


if __name__ == "__main__":
    # 运行测试
    test_instance = TestDepartmentCreatePermission()

    try:
        test_instance.setup_method()

        print("=== 测试超级管理员创建部门（指定merchant_id） ===")
        test_instance.test_super_admin_create_department_with_merchant_id()

        print("\n=== 测试超级管理员创建部门（不指定merchant_id） ===")
        test_instance.test_super_admin_create_department_without_merchant_id()

        print("\n=== 测试商户管理员创建部门 ===")
        test_instance.test_merchant_admin_create_department()

        print("\n=== 测试商户管理员创建部门（指定错误merchant_id） ===")
        test_instance.test_merchant_admin_create_department_with_wrong_merchant_id()

        print("\n✅ 所有测试通过！")

    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        raise
    finally:
        test_instance.teardown_method()
