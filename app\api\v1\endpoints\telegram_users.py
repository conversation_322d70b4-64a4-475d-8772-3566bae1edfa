"""
Telegram用户管理API端点
"""

from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, Query, status
from sqlalchemy.orm import Session
from pydantic import BaseModel

from app.api.deps import get_db, get_current_user
from app.models.user import User
from app.models.telegram_user import TelegramUser, VerificationStatus
from app.models.base import local_now
from app.core.logging import get_logger
from app.telegram_bot.services.user_verification_service import UserVerificationService
from app.telegram_bot.config import get_bot_config

logger = get_logger(__name__)

router = APIRouter()


class VerifyUserRequest(BaseModel):
    """用户验证请求"""
    verification_token: str
    system_user_id: int


class RejectUserRequest(BaseModel):
    """拒绝用户验证请求"""
    verification_token: str
    reason: Optional[str] = None


class RefreshTokenRequest(BaseModel):
    """刷新验证token请求"""
    user_id: int


@router.get("")
@router.get("/")
async def list_telegram_users(
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(20, ge=1, le=100, description="每页数量"),
    status_filter: Optional[str] = Query(None, description="状态过滤"),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    查询Telegram用户列表
    """
    try:
        # 检查权限：超级管理员或有Telegram管理权限的用户
        if not (current_user.is_superuser or
                current_user.has_permission("api:telegram:user:read")):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="没有Telegram用户管理权限"
            )
        
        # 构建查询
        query = db.query(TelegramUser)
        
        # 状态过滤
        if status_filter:
            try:
                verification_status = VerificationStatus(status_filter)
                query = query.filter(TelegramUser.verification_status == verification_status)
            except ValueError:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="无效的状态值"
                )
        
        # 计算总数
        total = query.count()
        
        # 分页查询
        users = query.offset((page - 1) * page_size).limit(page_size).all()
        
        # 转换为字典，超级管理员可以看到token
        include_token = current_user.is_superuser
        users_data = [user.to_dict(include_token=include_token) for user in users]

        return {
                "total": total,
                "page": page,
                "page_size": page_size,
                "users": users_data
            }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"查询Telegram用户列表失败: {e}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="查询用户列表失败"
        )


@router.get("/{user_id}")
async def get_telegram_user(
    user_id: int,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    获取Telegram用户详情
    """
    try:
        # 检查权限
        if not current_user.has_permission("api:telegram:config"):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="权限不足"
            )

        # 查询用户
        telegram_user = db.query(TelegramUser).filter_by(id=user_id).first()
        if not telegram_user:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="用户不存在"
            )

        # 超级管理员可以看到token
        include_token = current_user.is_superuser
        return telegram_user.to_dict(include_token=include_token)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取Telegram用户详情失败: {e}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取用户详情失败"
        )


@router.post("/verify")
async def verify_telegram_user(
    request: VerifyUserRequest,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    验证Telegram用户身份
    """
    try:
        # 检查权限：仅允许超级管理员
        if not current_user.is_superuser:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="仅超级管理员可以管理Telegram机器人"
            )

        # 验证系统用户存在
        system_user = db.query(User).filter_by(id=request.system_user_id).first()
        if not system_user:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="系统用户不存在"
            )

        # 调试：检查token是否存在于数据库中
        logger.info(f"尝试验证token: {request.verification_token[:8]}...")

        # 查找具有此token的用户（不限制状态）
        telegram_user_any_status = db.query(TelegramUser).filter_by(
            verification_token=request.verification_token
        ).first()

        if telegram_user_any_status:
            logger.info(f"找到token对应的用户，状态: {telegram_user_any_status.verification_status}")
        else:
            logger.warning(f"未找到token对应的用户")
            # 查找所有待验证的用户
            pending_users = db.query(TelegramUser).filter_by(
                verification_status=VerificationStatus.PENDING
            ).all()
            logger.info(f"当前有 {len(pending_users)} 个待验证用户")
            for user in pending_users:
                if user.verification_token:
                    logger.info(f"待验证用户token: {user.verification_token[:8]}...")

        # 创建验证服务
        config = get_bot_config(db)
        verification_service = UserVerificationService(db, config)

        # 在验证前先获取用户信息（因为验证后token会被清除）
        telegram_user_for_notification = db.query(TelegramUser).filter_by(
            verification_token=request.verification_token
        ).first()

        if not telegram_user_for_notification:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="验证申请不存在或已处理"
            )

        # 执行验证
        success = await verification_service.verify_user_with_token(
            request.verification_token,
            request.system_user_id
        )

        if not success:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="验证失败"
            )

        # 发送验证成功通知给用户（使用用户ID而不是token）
        await _send_verification_notification_by_user_id(
            db, telegram_user_for_notification.id, True, system_user.username
        )

        return {
                "verified": True,
                "system_user_id": request.system_user_id,
                "system_username": system_user.username
            }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"验证Telegram用户失败: {e}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="验证用户失败"
        )


@router.post("/reject")
async def reject_telegram_user_verification(
    request: RejectUserRequest,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    拒绝Telegram用户验证申请
    """
    try:
        # 检查权限：仅允许超级管理员
        if not current_user.is_superuser:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="仅超级管理员可以管理Telegram机器人"
            )

        # 查找待验证的用户
        telegram_user = db.query(TelegramUser).filter_by(
            verification_token=request.verification_token,
            verification_status=VerificationStatus.PENDING
        ).first()

        if not telegram_user:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="验证申请不存在或已处理"
            )

        # 保存用户ID用于通知（在token被清除前）
        user_id_for_notification = telegram_user.id

        # 拒绝验证
        telegram_user.verification_status = VerificationStatus.EXPIRED
        telegram_user.verification_token = None
        db.commit()

        # 发送拒绝通知给用户（使用用户ID）
        await _send_verification_notification_by_user_id(
            db, user_id_for_notification, False
        )

        logger.info(f"拒绝用户 {telegram_user.telegram_user_id} 的验证申请")

        return {
            "rejected": True,
            "telegram_user_id": telegram_user.telegram_user_id,
            "message": "验证申请已拒绝"
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"拒绝Telegram用户验证失败: {e}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="拒绝验证失败"
        )


@router.delete("/{user_id}/verification")
async def revoke_user_verification(
    user_id: int,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    撤销用户验证
    """
    try:
        # 检查权限
        if not current_user.has_permission("api:telegram:config"):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="权限不足"
            )
        
        # 查询用户
        telegram_user = db.query(TelegramUser).filter_by(id=user_id).first()
        if not telegram_user:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="用户不存在"
            )
        
        # 创建验证服务
        config = get_bot_config(db)
        verification_service = UserVerificationService(db, config)
        
        # 撤销验证
        success = await verification_service.revoke_user_verification(
            telegram_user.telegram_user_id
        )
        
        if not success:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="撤销验证失败"
            )
        
        return {"revoked": True}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"撤销用户验证失败: {e}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="撤销验证失败"
        )


@router.get("/verification/statistics")
async def get_verification_statistics(
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    获取验证统计信息
    """
    try:
        # 检查权限
        if not current_user.has_permission("api:telegram:config"):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="权限不足"
            )
        
        # 创建验证服务
        config = get_bot_config(db)
        verification_service = UserVerificationService(db, config)
        
        # 获取统计信息
        statistics = await verification_service.get_verification_statistics()
        
        return statistics
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取验证统计失败: {e}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取统计信息失败"
        )


@router.post("/cleanup-expired")
async def cleanup_expired_verifications(
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    清理过期的验证请求
    """
    try:
        # 检查权限
        if not current_user.has_permission("api:telegram:config"):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="权限不足"
            )
        
        # 创建验证服务
        config = get_bot_config(db)
        verification_service = UserVerificationService(db, config)
        
        # 清理过期验证
        cleaned_count = await verification_service.cleanup_expired_verifications()
        
        return {"cleaned_count": cleaned_count}

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"清理过期验证失败: {e}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="清理失败"
        )


@router.post("/refresh-token")
async def refresh_verification_token(
    request: RefreshTokenRequest,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    刷新用户验证token
    """
    try:
        # 检查权限：仅允许超级管理员
        if not current_user.is_superuser:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="仅超级管理员可以管理Telegram机器人"
            )

        # 查找用户
        telegram_user = db.query(TelegramUser).filter_by(id=request.user_id).first()
        if not telegram_user:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="用户不存在"
            )

        # 只能为待验证状态的用户刷新token
        if telegram_user.verification_status != VerificationStatus.PENDING:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"只能为待验证状态的用户刷新token，当前状态：{telegram_user.verification_status}"
            )

        # 生成新的验证token
        new_token = telegram_user.generate_verification_token()
        db.commit()

        logger.info(f"为用户 {telegram_user.telegram_user_id} 刷新验证token")

        return {
            "user_id": request.user_id,
            "new_token": new_token,
            "message": "验证token已刷新"
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"刷新验证token失败: {e}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="刷新token失败"
        )


@router.get("/debug/{user_id}")
async def debug_telegram_user(
    user_id: int,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    调试Telegram用户信息（仅超级管理员）
    """
    try:
        # 检查权限：仅允许超级管理员
        if not current_user.is_superuser:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="仅超级管理员可以使用调试功能"
            )

        # 查找用户
        telegram_user = db.query(TelegramUser).filter_by(id=user_id).first()
        if not telegram_user:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="用户不存在"
            )

        # 查找所有相关的token记录
        all_users_with_same_token = []
        if telegram_user.verification_token:
            all_users_with_same_token = db.query(TelegramUser).filter_by(
                verification_token=telegram_user.verification_token
            ).all()

        # 查找所有待验证的用户
        all_pending_users = db.query(TelegramUser).filter_by(
            verification_status=VerificationStatus.PENDING
        ).all()

        return {
            "user_info": {
                "id": telegram_user.id,
                "telegram_user_id": telegram_user.telegram_user_id,
                "telegram_username": telegram_user.telegram_username,
                "verification_status": telegram_user.verification_status,
                "verification_token": telegram_user.verification_token,
                "verification_time": telegram_user.verification_time.isoformat() if telegram_user.verification_time else None,
                "created_at": telegram_user.created_at.isoformat() if telegram_user.created_at else None,
                "updated_at": telegram_user.updated_at.isoformat() if telegram_user.updated_at else None,
                "system_user_id": telegram_user.system_user_id
            },
            "token_analysis": {
                "users_with_same_token": len(all_users_with_same_token),
                "same_token_users": [
                    {
                        "id": u.id,
                        "telegram_user_id": u.telegram_user_id,
                        "status": u.verification_status
                    } for u in all_users_with_same_token
                ]
            },
            "pending_users_count": len(all_pending_users),
            "pending_users": [
                {
                    "id": u.id,
                    "telegram_user_id": u.telegram_user_id,
                    "token": u.verification_token[:8] + "..." if u.verification_token else None,
                    "created_at": u.created_at.isoformat() if u.created_at else None
                } for u in all_pending_users
            ]
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"调试用户信息失败: {e}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="调试失败"
        )


async def _send_verification_notification(
    db: Session,
    verification_token: str,
    is_approved: bool,
    system_username: str = None
):
    """
    发送验证结果通知给Telegram用户
    """
    try:
        # 查找用户
        telegram_user = db.query(TelegramUser).filter_by(
            verification_token=verification_token
        ).first()

        if not telegram_user:
            logger.warning(f"未找到验证令牌对应的用户: {verification_token}")
            return

        # 获取机器人服务
        from app.telegram_bot.services.bot_service import get_bot_service
        bot_service = get_bot_service()

        if not bot_service or not bot_service.is_running():
            logger.warning("机器人服务未运行，无法发送通知")
            return

        # 构造通知消息
        if is_approved:
            message = f"""🎉 **身份验证成功！**

恭喜 {telegram_user.get_display_name()}！您的身份验证已通过审核。

📋 **验证信息**：
👤 **关联账户**：{system_username}
🕐 **验证时间**：{local_now().strftime('%Y-%m-%d %H:%M:%S')}
🆔 **用户ID**：{telegram_user.telegram_user_id}

🚀 **您现在可以使用的功能**：

📊 **数据查询**：
• `/stats` - 查看今日绑卡数据
• `/stats_week` - 查看本周数据
• `/stats_month` - 查看本月数据
• `/stats_custom 开始日期 结束日期` - 自定义时间范围

🔍 **状态查询**：
• `/status` - 查看当前状态和群组信息
• `/help` - 获取详细帮助信息

⚙️ **群组管理**（如果您是管理员）：
• `/bind <绑定令牌>` - 绑定群组到商户
• `/settings` - 群组设置管理

💡 **使用提示**：
• 在群组中使用命令可以让所有成员看到数据
• 私聊机器人可以获得个性化帮助
• 遇到问题时，机器人会提供具体的解决建议

🎯 **立即开始**：输入 `/stats` 查看今日数据！"""
        else:
            message = """❌ **身份验证被拒绝**

很抱歉，您的身份验证申请被管理员拒绝。

如有疑问，请联系系统管理员。
您可以重新申请验证：`/verify <新验证码>`"""

        # 发送通知（带交互式按钮）
        if is_approved:
            # 为验证成功添加快捷按钮
            from telegram import InlineKeyboardButton, InlineKeyboardMarkup

            keyboard = InlineKeyboardMarkup([
                [
                    InlineKeyboardButton("📊 查看今日数据", callback_data="stats_today"),
                    InlineKeyboardButton("📈 本周数据", callback_data="stats_week")
                ],
                [
                    InlineKeyboardButton("🔍 查看状态", callback_data="check_status"),
                    InlineKeyboardButton("❓ 获取帮助", callback_data="show_help")
                ],
                [
                    InlineKeyboardButton("📞 联系管理员", callback_data="contact_admin")
                ]
            ])

            await bot_service.send_message(
                chat_id=telegram_user.telegram_user_id,
                text=message,
                parse_mode='Markdown',
                reply_markup=keyboard
            )
        else:
            # 拒绝验证的消息不需要按钮
            await bot_service.send_message(
                chat_id=telegram_user.telegram_user_id,
                text=message,
                parse_mode='Markdown'
            )

        logger.info(f"已发送验证通知给用户 {telegram_user.telegram_user_id}")

        # 发送成功后，如果是验证成功，还要发送使用指南和个性化欢迎
        if is_approved:
            await _send_usage_guide(telegram_user, bot_service)
            await _send_personalized_welcome(telegram_user, bot_service, system_username)

    except Exception as e:
        logger.error(f"发送验证通知失败: {e}", exc_info=True)

        # 尝试发送简化版本的通知
        try:
            simple_message = "✅ 验证成功！" if is_approved else "❌ 验证被拒绝"
            simple_message += f"\n\n详细信息请输入 /help 查看。"

            await bot_service.send_message(
                chat_id=telegram_user.telegram_user_id,
                text=simple_message
            )
            logger.info(f"已发送简化版通知给用户 {telegram_user.telegram_user_id}")

        except Exception as retry_error:
            logger.error(f"发送简化版通知也失败: {retry_error}", exc_info=True)


async def _send_usage_guide(telegram_user: TelegramUser, bot_service):
    """
    发送详细的使用指南
    """
    try:
        # 延迟3秒发送，避免消息过于密集
        import asyncio
        await asyncio.sleep(3)

        guide_message = """📚 **详细使用指南**

🎯 **快速上手**：

**1. 数据查询命令**
• `/stats` - 今日绑卡数据（最常用）
• `/stats_week` - 本周数据汇总
• `/stats_month` - 本月数据汇总
• `/stats_custom 2025-01-01 2025-01-07` - 自定义日期范围

**2. 状态查询命令**
• `/status` - 查看个人和群组状态
• `/help` - 随时获取帮助

**3. 群组功能**（如果您是管理员）
• 将机器人添加到群组
• 使用 `/bind <令牌>` 绑定群组到商户
• 群组成员都可以查看数据

💡 **使用技巧**：

🔸 **命令格式**：所有命令都以 `/` 开头
🔸 **日期格式**：使用 YYYY-MM-DD 格式（如：2025-01-15）
🔸 **群组 vs 私聊**：群组中查询数据所有人可见，私聊查询只有您能看到
🔸 **错误处理**：如果命令失败，机器人会告诉您具体原因和解决方法

🚀 **现在就试试**：
输入 `/stats` 查看今日数据，开始您的数据查询之旅！

❓ 有问题随时输入 `/help` 或联系管理员。"""

        from telegram import InlineKeyboardButton, InlineKeyboardMarkup

        guide_keyboard = InlineKeyboardMarkup([
            [
                InlineKeyboardButton("📊 立即查看今日数据", callback_data="stats_today")
            ],
            [
                InlineKeyboardButton("📖 查看完整帮助", callback_data="show_help"),
                InlineKeyboardButton("🔍 检查状态", callback_data="check_status")
            ]
        ])

        await bot_service.send_message(
            chat_id=telegram_user.telegram_user_id,
            text=guide_message,
            parse_mode='Markdown',
            reply_markup=guide_keyboard
        )

        logger.info(f"已发送使用指南给用户 {telegram_user.telegram_user_id}")

    except Exception as e:
        logger.error(f"发送使用指南失败: {e}", exc_info=True)


async def _send_personalized_welcome(telegram_user: TelegramUser, bot_service, system_username: str):
    """
    发送个性化欢迎消息
    """
    try:
        # 延迟6秒发送，确保消息顺序
        import asyncio
        await asyncio.sleep(6)

        user_name = telegram_user.get_display_name()

        welcome_message = f"""🌟 **欢迎加入，{user_name}！**

您已成功完成身份验证，现在是我们团队的正式成员了！

🎊 **您的专属信息**：
👤 **显示名称**：{user_name}
🔗 **关联账户**：{system_username}
🆔 **Telegram ID**：{telegram_user.telegram_user_id}
📅 **加入时间**：{local_now().strftime('%Y年%m月%d日')}

🎯 **接下来您可以**：

**立即体验**：
• 输入 `/stats` 查看今日绑卡数据
• 加入工作群组，与团队协作
• 设置个人偏好和通知

**探索功能**：
• 数据查询和分析
• 实时统计监控
• 自定义报表生成

**获得支持**：
• 随时输入 `/help` 获取帮助
• 遇到问题可联系管理员
• 查看使用技巧和最佳实践

💡 **小贴士**：建议先试试 `/stats` 命令，感受一下数据查询的便捷性！

🚀 **开始您的数据之旅吧！**"""

        from telegram import InlineKeyboardButton, InlineKeyboardMarkup

        welcome_keyboard = InlineKeyboardMarkup([
            [
                InlineKeyboardButton("🎯 开始数据查询", callback_data="stats_today")
            ],
            [
                InlineKeyboardButton("📋 查看完整功能", callback_data="show_help"),
                InlineKeyboardButton("⚙️ 个人设置", callback_data="user_settings")
            ],
            [
                InlineKeyboardButton("👥 联系团队", callback_data="contact_admin")
            ]
        ])

        await bot_service.send_message(
            chat_id=telegram_user.telegram_user_id,
            text=welcome_message,
            parse_mode='Markdown',
            reply_markup=welcome_keyboard
        )

        logger.info(f"已发送个性化欢迎消息给用户 {telegram_user.telegram_user_id}")

    except Exception as e:
        logger.error(f"发送个性化欢迎消息失败: {e}", exc_info=True)


async def _send_verification_notification_by_user_id(
    db: Session,
    user_id: int,
    is_approved: bool,
    system_username: str = None
):
    """
    通过用户ID发送验证结果通知给Telegram用户
    """
    try:
        # 查找用户
        telegram_user = db.query(TelegramUser).filter_by(id=user_id).first()

        if not telegram_user:
            logger.warning(f"未找到ID为 {user_id} 的用户")
            return

        # 获取机器人服务
        from app.telegram_bot.services.bot_service import get_bot_service
        bot_service = get_bot_service()

        if not bot_service or not bot_service.is_running():
            logger.warning("机器人服务未运行，无法发送通知")
            return

        # 构造通知消息
        if is_approved:
            message = f"""🎉 **身份验证成功！**

恭喜 {telegram_user.get_display_name()}！您的身份验证已通过审核。

📋 **验证信息**：
👤 **关联账户**：{system_username}
🕐 **验证时间**：{local_now().strftime('%Y-%m-%d %H:%M:%S')}
🆔 **用户ID**：{telegram_user.telegram_user_id}

🚀 **您现在可以使用的功能**：

📊 **数据查询**：
• `/stats` - 查看今日绑卡数据
• `/stats_week` - 查看本周数据
• `/stats_month` - 查看本月数据
• `/stats_custom 开始日期 结束日期` - 自定义时间范围

🔍 **状态查询**：
• `/status` - 查看当前状态和群组信息
• `/help` - 获取详细帮助信息

⚙️ **群组管理**（如果您是管理员）：
• `/bind <绑定令牌>` - 绑定群组到商户
• `/settings` - 群组设置管理

💡 **使用提示**：
• 在群组中使用命令可以让所有成员看到数据
• 私聊机器人可以获得个性化帮助
• 遇到问题时，机器人会提供具体的解决建议

🎯 **立即开始**：输入 `/stats` 查看今日数据！"""
        else:
            message = """❌ **身份验证被拒绝**

很抱歉，您的身份验证申请被管理员拒绝。

如有疑问，请联系系统管理员。
您可以重新申请验证：`/verify <新验证码>`"""

        # 发送通知（带交互式按钮）
        if is_approved:
            # 为验证成功添加快捷按钮
            from telegram import InlineKeyboardButton, InlineKeyboardMarkup

            keyboard = InlineKeyboardMarkup([
                [
                    InlineKeyboardButton("📊 查看今日数据", callback_data="stats_today"),
                    InlineKeyboardButton("📈 本周数据", callback_data="stats_week")
                ],
                [
                    InlineKeyboardButton("🔍 查看状态", callback_data="check_status"),
                    InlineKeyboardButton("❓ 获取帮助", callback_data="show_help")
                ],
                [
                    InlineKeyboardButton("📞 联系管理员", callback_data="contact_admin")
                ]
            ])

            await bot_service.send_message(
                chat_id=telegram_user.telegram_user_id,
                text=message,
                parse_mode='Markdown',
                reply_markup=keyboard
            )
        else:
            # 拒绝验证的消息不需要按钮
            await bot_service.send_message(
                chat_id=telegram_user.telegram_user_id,
                text=message,
                parse_mode='Markdown'
            )

        logger.info(f"已发送验证通知给用户 {telegram_user.telegram_user_id}")

        # 发送成功后，如果是验证成功，还要发送使用指南和个性化欢迎
        if is_approved:
            await _send_usage_guide(telegram_user, bot_service)
            await _send_personalized_welcome(telegram_user, bot_service, system_username)

    except Exception as e:
        logger.error(f"发送验证通知失败: {e}", exc_info=True)

        # 尝试发送简化版本的通知
        try:
            simple_message = "✅ 验证成功！" if is_approved else "❌ 验证被拒绝"
            simple_message += f"\n\n详细信息请输入 /help 查看。"

            await bot_service.send_message(
                chat_id=telegram_user.telegram_user_id,
                text=simple_message
            )
            logger.info(f"已发送简化版通知给用户 {telegram_user.telegram_user_id}")

        except Exception as retry_error:
            logger.error(f"发送简化版通知也失败: {retry_error}", exc_info=True)


@router.delete("/{user_id}")
async def delete_telegram_user(
    user_id: int,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    删除Telegram用户
    """
    try:
        # 检查权限：仅允许超级管理员
        if not current_user.is_superuser:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="仅超级管理员可以删除Telegram用户"
            )

        # 查找用户
        telegram_user = db.query(TelegramUser).filter_by(id=user_id).first()
        if not telegram_user:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="用户不存在"
            )

        # 记录删除信息
        telegram_user_id_for_log = telegram_user.telegram_user_id
        logger.info(f"删除Telegram用户: ID={user_id}, Telegram用户ID={telegram_user_id_for_log}")

        # 删除用户
        db.delete(telegram_user)
        db.commit()

        # 清理会话缓存，确保删除操作立即生效
        # 这是解决Telegram机器人日志记录外键约束问题的关键
        db.expunge_all()

        logger.info(f"Telegram用户删除成功: ID={user_id}, Telegram用户ID={telegram_user_id_for_log}")

        return {
            "message": "用户删除成功",
            "user_id": user_id,
            "telegram_user_id": telegram_user_id_for_log
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"删除Telegram用户失败: {e}", exc_info=True)
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="删除用户失败"
        )
