[Unit]
Description=Walmart Card Binding Server
After=network.target network-online.target mysql.service redis.service rabbitmq-server.service
Wants=network-online.target
Requires=mysql.service redis.service rabbitmq-server.service

[Service]
Type=simple
User=root
WorkingDirectory=/home/<USER>
Environment=PYTHONPATH=/home/<USER>
Environment=PYTHONUNBUFFERED=1
Environment=ENV=production
Environment=TZ=Asia/Shanghai

# 可执行文件路径
ExecStart=/home/<USER>/walmart-bind-card-server-linux

# 优雅停止配置
ExecStop=/bin/kill -TERM $MAINPID
ExecReload=/bin/kill -HUP $MAINPID
TimeoutStartSec=60
TimeoutStopSec=30

# 自动重启配置
Restart=always
RestartSec=10
StartLimitInterval=300
StartLimitBurst=5

# 安全配置
NoNewPrivileges=true
PrivateTmp=true
ProtectSystem=strict
ProtectHome=true
ReadWritePaths=/home/<USER>/logs /home/<USER>/config.yaml
ProtectKernelTunables=true
ProtectKernelModules=true
ProtectControlGroups=true

# 资源限制
LimitNOFILE=65536
LimitNPROC=4096

# 标准输出和错误输出
StandardOutput=journal
StandardError=journal
SyslogIdentifier=walmart-bind-card

# 进程管理
KillMode=mixed
KillSignal=SIGTERM

[Install]
WantedBy=multi-user.target