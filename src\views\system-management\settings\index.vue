<template>
  <div class="settings-container">
    <el-card class="settings-card" shadow="never">
      <el-tabs tab-position="left" style="min-height: 400px;">
        <el-tab-pane label="基本设置">
          <template #label>
            <span class="tab-label">
              <el-icon>
                <Setting />
              </el-icon> 基本设置
            </span>
          </template>
          <el-form :model="settings" label-width="120px" label-position="right">
            <h3>基本信息</h3>
            <el-form-item label="系统名称" prop="systemName">
              <el-input v-model="settings.systemName" placeholder="请输入系统名称" />
            </el-form-item>
            <el-form-item label="系统描述" prop="systemDescription">
              <el-input v-model="settings.systemDescription" type="textarea" :rows="3" placeholder="请输入系统描述" />
            </el-form-item>
          </el-form>
        </el-tab-pane>

        <el-tab-pane label="安全设置">
          <template #label>
            <span class="tab-label">
              <el-icon>
                <Lock />
              </el-icon> 安全设置
            </span>
          </template>
          <el-form :model="settings" label-width="120px" label-position="right">
            <h3>密码策略</h3>
            <el-form-item label="最小密码长度" prop="minPasswordLength">
              <el-input-number v-model="settings.minPasswordLength" :min="6" :max="32" />
            </el-form-item>
            <el-form-item label="密码复杂度" prop="passwordComplexity">
              <el-select v-model="settings.passwordComplexity" placeholder="选择密码复杂度">
                <el-option label="低 (任意字符)" value="low" />
                <el-option label="中 (字母+数字)" value="medium" />
                <el-option label="高 (大小写+数字+特殊符)" value="high" />
              </el-select>
            </el-form-item>
            <h3>登录安全</h3>
            <el-form-item label="登录失败锁定" prop="loginLockEnabled">
              <el-switch v-model="settings.loginLockEnabled" />
            </el-form-item>
            <el-form-item label="锁定阈值(次)" v-if="settings.loginLockEnabled" prop="loginLockThreshold">
              <el-input-number v-model="settings.loginLockThreshold" :min="3" :max="10" />
            </el-form-item>
            <el-form-item label="锁定时长(分钟)" v-if="settings.loginLockEnabled" prop="loginLockDuration">
              <el-input-number v-model="settings.loginLockDuration" :min="5" :max="60" />
            </el-form-item>
          </el-form>
        </el-tab-pane>

        <el-tab-pane label="通知设置">
          <template #label>
            <span class="tab-label">
              <el-icon>
                <Bell />
              </el-icon> 通知设置
            </span>
          </template>
          <el-form :model="settings" label-width="120px" label-position="right">
            <h3>邮件通知</h3>
            <el-form-item label="启用邮件通知" prop="emailNotificationEnabled">
              <el-switch v-model="settings.emailNotificationEnabled" />
            </el-form-item>
            <template v-if="settings.emailNotificationEnabled">
              <el-form-item label="SMTP服务器" prop="smtpServer">
                <el-input v-model="settings.smtpServer" placeholder="例如 smtp.example.com" />
              </el-form-item>
              <el-form-item label="SMTP端口" prop="smtpPort">
                <el-input-number v-model="settings.smtpPort" :min="1" :max="65535" />
              </el-form-item>
              <el-form-item label="SMTP用户" prop="smtpUsername">
                <el-input v-model="settings.smtpUsername" placeholder="邮箱账号" />
              </el-form-item>
              <el-form-item label="SMTP密码/授权码" prop="smtpPassword">
                <el-input v-model="settings.smtpPassword" type="password" show-password />
              </el-form-item>
              <el-form-item label="发件人邮箱" prop="smtpSender">
                <el-input v-model="settings.smtpSender" placeholder="需与SMTP用户匹配" />
              </el-form-item>
            </template>
          </el-form>
        </el-tab-pane>

        <el-tab-pane label="CK管理">
          <template #label>
            <span class="tab-label">
              <el-icon>
                <Tools />
              </el-icon> CK管理
            </span>
          </template>
          <el-form :model="ckSettings" label-width="140px" label-position="right">
            <h3>CK自动过期设置</h3>
            <el-form-item label="启用自动过期" prop="checkEnabled">
              <el-switch v-model="ckSettings.checkEnabled" />
              <el-tooltip content="启用后系统将自动检测并处理过期的CK" placement="top">
                <el-icon style="margin-left: 4px; cursor: help;">
                  <QuestionFilled />
                </el-icon>
              </el-tooltip>
            </el-form-item>
            <el-form-item label="过期时间(分钟)" prop="expireMinutes" v-if="ckSettings.checkEnabled">
              <el-input-number
                v-model="ckSettings.expireMinutes"
                :min="1"
                :max="1440"
                :step="1"
                placeholder="1-1440分钟"
              />
              <span style="margin-left: 8px; color: #909399; font-size: 12px;">
                CK创建后多长时间自动过期（1分钟-24小时）
              </span>
            </el-form-item>
            <el-form-item label="检测间隔(分钟)" prop="checkInterval" v-if="ckSettings.checkEnabled">
              <el-input-number
                v-model="ckSettings.checkInterval"
                :min="1"
                :max="60"
                :step="1"
                placeholder="1-60分钟"
              />
              <span style="margin-left: 8px; color: #909399; font-size: 12px;">
                系统检测过期CK的频率（1分钟-1小时）
              </span>
            </el-form-item>
            <el-alert
              title="注意事项"
              type="warning"
              :closable="false"
              style="margin-top: 16px;"
            >
              <ul style="margin: 0; padding-left: 20px;">
                <li>过期的CK将被自动标记为删除状态，不会被绑卡逻辑选中使用</li>
                <li>过期处理采用软删除机制，历史数据仍会保留用于统计</li>
                <li>建议根据实际业务需求合理设置过期时间</li>
                <li>检测间隔过短可能影响系统性能，建议设置为5分钟以上</li>
              </ul>
            </el-alert>
          </el-form>
        </el-tab-pane>

        <el-tab-pane label="其他设置">
          <template #label>
            <span class="tab-label">
              <el-icon>
                <Tools />
              </el-icon> 其他设置
            </span>
          </template>
          <el-form :model="settings" label-width="120px" label-position="right">
            <h3>开发者选项</h3>
            <el-form-item label="调试模式" prop="debugMode">
              <el-switch v-model="settings.debugMode" />
              <el-tooltip content="启用后将输出更详细的日志和错误信息" placement="top">
                <el-icon style="margin-left: 4px; cursor: help;">
                  <QuestionFilled />
                </el-icon>
              </el-tooltip>
            </el-form-item>
            <el-form-item label="日志级别" prop="logLevel">
              <el-select v-model="settings.logLevel" placeholder="选择日志级别">
                <el-option label="DEBUG" value="debug" />
                <el-option label="INFO" value="info" />
                <el-option label="WARNING" value="warning" />
                <el-option label="ERROR" value="error" />
              </el-select>
            </el-form-item>
          </el-form>
        </el-tab-pane>
      </el-tabs>

      <!-- Common Save/Reset Buttons outside tabs but inside card -->
      <div class="form-actions">
        <el-button type="primary" @click="saveSettings" :loading="saving">保存设置</el-button>
        <el-button @click="resetSettings">重置设置</el-button>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Setting, Lock, Bell, Tools, QuestionFilled } from '@element-plus/icons-vue' // Import icons
import { systemSettingsApi } from '@/api'

// Define saving state
const saving = ref(false)

// 设置数据
const settings = ref({
  // 基本设置
  systemName: '沃尔玛绑卡系统',
  systemDescription: '沃尔玛绑卡系统管理后台',

  // 安全设置
  minPasswordLength: 8,
  passwordComplexity: 'medium',
  loginLockEnabled: true,
  loginLockThreshold: 5,

  // 通知设置
  emailNotificationEnabled: false,
  smtpServer: '',
  smtpPort: 587,
  smtpUsername: '',
  smtpPassword: '',

  // 其他设置
  debugMode: false,
  logLevel: 'info'
})

// CK设置数据
const ckSettings = ref({
  checkEnabled: true,
  expireMinutes: 30,
  checkInterval: 5
})

// 获取CK过期配置
const loadCkExpireConfig = async () => {
  try {
    const response = await systemSettingsApi.getCkExpireConfig()
    if (response && response.config) {
      const config = response.config
      ckSettings.value = {
        checkEnabled: config.check_enabled,
        expireMinutes: config.expire_minutes,
        checkInterval: config.check_interval
      }
    }
  } catch (error) {
    console.error('获取CK过期配置失败:', error)
    ElMessage.error('获取CK过期配置失败')
  }
}

// 保存CK过期配置
const saveCkExpireConfig = async () => {
  try {
    const configData = {
      expire_minutes: ckSettings.value.expireMinutes,
      check_enabled: ckSettings.value.checkEnabled,
      check_interval: ckSettings.value.checkInterval
    }

    await systemSettingsApi.updateCkExpireConfig(configData)
    ElMessage.success('CK过期配置保存成功')
  } catch (error) {
    console.error('保存CK过期配置失败:', error)
    ElMessage.error('保存CK过期配置失败')
    throw error
  }
}

// 保存设置
const saveSettings = async () => {
  saving.value = true // Set saving to true when starting
  try {
    // 保存CK过期配置
    await saveCkExpireConfig()

    // TODO: 保存其他设置
    await new Promise(resolve => setTimeout(resolve, 500)); // Simulate API call
    ElMessage.success('设置保存成功')
  } catch (error) {
    console.error('保存设置失败:', error)
    ElMessage.error('保存设置失败')
  } finally {
    saving.value = false // Set saving back to false after completion/error
  }
}

// 重置设置
const resetSettings = async () => {
  try {
    await ElMessageBox.confirm('确定要重置所有设置吗？此操作不可撤销。', '确认重置', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })

    // 重置CK设置为默认值
    ckSettings.value = {
      checkEnabled: true,
      expireMinutes: 30,
      checkInterval: 5
    }

    // TODO: 重置其他设置为默认值
    settings.value = {
      systemName: '沃尔玛绑卡系统',
      systemDescription: '沃尔玛绑卡系统管理后台',
      minPasswordLength: 8,
      passwordComplexity: 'medium',
      loginLockEnabled: true,
      loginLockThreshold: 5,
      emailNotificationEnabled: false,
      smtpServer: '',
      smtpPort: 587,
      smtpUsername: '',
      smtpPassword: '',
      debugMode: false,
      logLevel: 'info'
    }

    ElMessage.success('设置已重置为默认值')
  } catch (error) {
    // 用户取消操作
    if (error !== 'cancel') {
      console.error('重置设置失败:', error)
      ElMessage.error('重置设置失败')
    }
  }
}

// 页面加载时获取配置
onMounted(() => {
  loadCkExpireConfig()
})
</script>

<style scoped>
.settings-container {
  padding: 20px;
}

.settings-card {
  /* Allow card to take more width if needed */
}

/* Style for tab labels */
.tab-label {
  display: inline-flex;
  align-items: center;
}

.tab-label .el-icon {
  margin-right: 8px;
}

/* Common form styles */
.el-form {
  padding: 20px;
  padding-top: 0;
}

h3 {
  margin-top: 0;
  margin-bottom: 20px;
  font-size: 16px;
  color: #303133;
  border-left: 3px solid #409eff;
  padding-left: 10px;
}

.el-form-item {
  margin-bottom: 18px;
  /* Adjust spacing */
}

/* Action buttons at the bottom */
.form-actions {
  text-align: center;
  /* Center buttons */
  margin-top: 30px;
  padding-top: 20px;
  border-top: 1px solid #ebeef5;
  /* Add a separator line */
}

/* Remove old styles */
/*
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.el-divider {
  margin: 24px 0;
}
*/
</style>