"""
沃尔玛CK管理功能综合测试脚本
运行所有相关的测试用例
"""

import sys
import os
import asyncio
import subprocess
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))


def run_backend_tests():
    """运行后端API测试"""
    print(f"\n{'='*60}")
    print("开始执行后端API测试")
    print(f"{'='*60}")
    
    test_files = [
        "test/walmart_ck/test_walmart_ck_role_based_creation.py",
        "test/walmart_ck/test_walmart_ck_crud.py",
    ]
    
    results = []
    
    for test_file in test_files:
        if os.path.exists(test_file):
            print(f"\n执行测试文件: {test_file}")
            try:
                result = subprocess.run([
                    sys.executable, test_file
                ], capture_output=True, text=True, timeout=300)
                
                if result.returncode == 0:
                    print(f"✅ {test_file} 测试通过")
                    results.append({"file": test_file, "status": "PASS", "output": result.stdout})
                else:
                    print(f"❌ {test_file} 测试失败")
                    print(f"错误输出: {result.stderr}")
                    results.append({"file": test_file, "status": "FAIL", "output": result.stderr})
                    
            except subprocess.TimeoutExpired:
                print(f"⏰ {test_file} 测试超时")
                results.append({"file": test_file, "status": "TIMEOUT", "output": "测试超时"})
            except Exception as e:
                print(f"❌ {test_file} 执行异常: {e}")
                results.append({"file": test_file, "status": "ERROR", "output": str(e)})
        else:
            print(f"⚠️ 测试文件不存在: {test_file}")
            results.append({"file": test_file, "status": "NOT_FOUND", "output": "文件不存在"})
    
    return results


async def run_frontend_tests():
    """运行前端测试"""
    print(f"\n{'='*60}")
    print("开始执行前端自动化测试")
    print(f"{'='*60}")
    
    try:
        # 导入前端测试模块
        from test.walmart_ck.test_walmart_ck_frontend import WalmartCKFrontendTest
        
        # 创建测试实例
        test = WalmartCKFrontendTest()
        
        # 运行测试
        results = await test.run_all_tests()
        
        return results
        
    except ImportError as e:
        print(f"❌ 无法导入前端测试模块: {e}")
        return [{"test_name": "前端测试导入", "status": "FAIL", "message": f"导入失败: {e}"}]
    except Exception as e:
        print(f"❌ 前端测试执行异常: {e}")
        return [{"test_name": "前端测试执行", "status": "FAIL", "message": f"执行异常: {e}"}]


def check_prerequisites():
    """检查测试前置条件"""
    print(f"\n{'='*60}")
    print("检查测试前置条件")
    print(f"{'='*60}")
    
    checks = []
    
    # 检查服务器是否运行
    try:
        import requests
        response = requests.get("http://localhost:20000/api/v1/health", timeout=5)
        if response.status_code == 200:
            print("✅ 后端服务器运行正常")
            checks.append({"name": "后端服务器", "status": "OK"})
        else:
            print(f"❌ 后端服务器响应异常: {response.status_code}")
            checks.append({"name": "后端服务器", "status": "FAIL"})
    except Exception as e:
        print(f"❌ 无法连接后端服务器: {e}")
        checks.append({"name": "后端服务器", "status": "FAIL"})
    
    # 检查前端服务器是否运行
    try:
        import requests
        response = requests.get("http://localhost:2000", timeout=5)
        if response.status_code == 200:
            print("✅ 前端服务器运行正常")
            checks.append({"name": "前端服务器", "status": "OK"})
        else:
            print(f"❌ 前端服务器响应异常: {response.status_code}")
            checks.append({"name": "前端服务器", "status": "FAIL"})
    except Exception as e:
        print(f"❌ 无法连接前端服务器: {e}")
        checks.append({"name": "前端服务器", "status": "FAIL"})
    
    # 检查数据库连接
    try:
        import pymysql
        connection = pymysql.connect(
            host='localhost',
            port=3306,
            user='root',
            password='7c222fb2927d828af22f592134e8932480637c0d',
            database='walmart_card_db',
            connect_timeout=5
        )
        connection.close()
        print("✅ 数据库连接正常")
        checks.append({"name": "数据库连接", "status": "OK"})
    except Exception as e:
        print(f"❌ 数据库连接失败: {e}")
        checks.append({"name": "数据库连接", "status": "FAIL"})
    
    # 检查必要的Python包
    required_packages = ["requests", "playwright", "pymysql"]
    for package in required_packages:
        try:
            __import__(package)
            print(f"✅ {package} 包已安装")
            checks.append({"name": f"{package}包", "status": "OK"})
        except ImportError:
            print(f"❌ {package} 包未安装")
            checks.append({"name": f"{package}包", "status": "FAIL"})
    
    return checks


def generate_test_report(backend_results, frontend_results, prerequisites):
    """生成测试报告"""
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    report_file = f"test/reports/walmart_ck_test_report_{timestamp}.md"
    
    # 确保报告目录存在
    os.makedirs(os.path.dirname(report_file), exist_ok=True)
    
    with open(report_file, 'w', encoding='utf-8') as f:
        f.write(f"# 沃尔玛CK管理功能测试报告\n\n")
        f.write(f"**测试时间:** {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
        
        # 前置条件检查
        f.write("## 前置条件检查\n\n")
        for check in prerequisites:
            status_icon = "✅" if check["status"] == "OK" else "❌"
            f.write(f"- {status_icon} {check['name']}: {check['status']}\n")
        f.write("\n")
        
        # 后端测试结果
        f.write("## 后端API测试结果\n\n")
        backend_passed = sum(1 for r in backend_results if r["status"] == "PASS")
        backend_total = len(backend_results)
        f.write(f"**总计:** {backend_passed}/{backend_total} 通过\n\n")
        
        for result in backend_results:
            status_icon = "✅" if result["status"] == "PASS" else "❌"
            f.write(f"### {status_icon} {os.path.basename(result['file'])}\n")
            f.write(f"**状态:** {result['status']}\n\n")
            if result["output"]:
                f.write("**输出:**\n```\n")
                f.write(result["output"][:1000])  # 限制输出长度
                if len(result["output"]) > 1000:
                    f.write("\n... (输出已截断)")
                f.write("\n```\n\n")
        
        # 前端测试结果
        f.write("## 前端自动化测试结果\n\n")
        frontend_passed = sum(1 for r in frontend_results if r["status"] == "PASS")
        frontend_total = len(frontend_results)
        f.write(f"**总计:** {frontend_passed}/{frontend_total} 通过\n\n")
        
        for result in frontend_results:
            status_icon = "✅" if result["status"] == "PASS" else "❌"
            f.write(f"- {status_icon} {result['test_name']}: {result['message']}\n")
        f.write("\n")
        
        # 总结
        total_passed = backend_passed + frontend_passed
        total_tests = backend_total + frontend_total
        f.write("## 测试总结\n\n")
        f.write(f"**总体通过率:** {total_passed}/{total_tests} ({total_passed/total_tests*100:.1f}%)\n\n")
        
        if total_passed == total_tests:
            f.write("🎉 **所有测试通过！**\n")
        else:
            f.write("⚠️ **存在测试失败，请检查具体问题。**\n")
    
    print(f"\n📊 测试报告已生成: {report_file}")
    return report_file


async def main():
    """主函数"""
    print(f"\n{'='*60}")
    print("沃尔玛CK管理功能综合测试")
    print(f"{'='*60}")
    
    # 检查前置条件
    prerequisites = check_prerequisites()
    
    # 检查关键前置条件是否满足
    critical_checks = ["后端服务器", "前端服务器", "数据库连接"]
    failed_critical = [c for c in prerequisites if c["name"] in critical_checks and c["status"] != "OK"]
    
    if failed_critical:
        print(f"\n❌ 关键前置条件检查失败，无法继续测试:")
        for check in failed_critical:
            print(f"   - {check['name']}: {check['status']}")
        return
    
    # 运行后端测试
    backend_results = run_backend_tests()
    
    # 运行前端测试
    frontend_results = await run_frontend_tests()
    
    # 生成测试报告
    report_file = generate_test_report(backend_results, frontend_results, prerequisites)
    
    # 输出最终结果
    backend_passed = sum(1 for r in backend_results if r["status"] == "PASS")
    backend_total = len(backend_results)
    frontend_passed = sum(1 for r in frontend_results if r["status"] == "PASS")
    frontend_total = len(frontend_results)
    total_passed = backend_passed + frontend_passed
    total_tests = backend_total + frontend_total
    
    print(f"\n{'='*60}")
    print("测试完成总结")
    print(f"{'='*60}")
    print(f"后端测试: {backend_passed}/{backend_total} 通过")
    print(f"前端测试: {frontend_passed}/{frontend_total} 通过")
    print(f"总体结果: {total_passed}/{total_tests} 通过 ({total_passed/total_tests*100:.1f}%)")
    print(f"测试报告: {report_file}")
    
    if total_passed == total_tests:
        print("\n🎉 所有测试通过！")
        return 0
    else:
        print("\n⚠️ 存在测试失败，请检查具体问题。")
        return 1


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
