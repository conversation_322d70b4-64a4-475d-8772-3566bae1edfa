import hmac
import hashlib
import time
import random
import string
from typing import Dict, Any, Optional
from sqlalchemy.orm import Session
from datetime import datetime, timedelta

from app.crud.merchant import merchant as merchant_crud
from app.models.merchant import Merchant
from app.models import CardRecord
from app.schemas.merchant import MerchantStatistics
from fastapi import HTTPException, status
from app import crud, schemas
from app.core.security import generate_api_key, generate_api_secret
from sqlalchemy import select

from app.utils import helpers


def generate_merchant_statistics(db: Session, merchant_id: int) -> MerchantStatistics:
    """生成商家统计数据"""
    # 获取商家
    merchant = merchant_crud.get(db, id=merchant_id)
    if not merchant:
        return MerchantStatistics(
            total_records=0,
            success_records=0,
            failed_records=0,
            pending_records=0,
            today_records=0,
            yesterday_records=0,
        )

    # 计算统计数据
    total_query = db.query(CardRecord).filter(CardRecord.merchant_id == merchant_id)

    # 总记录数
    total_records = total_query.count()

    # 成功记录数
    success_records = total_query.filter(CardRecord.status == "success").count()

    # 失败记录数
    failed_records = total_query.filter(CardRecord.status == "failed").count()

    # 待处理记录数
    pending_records = total_query.filter(CardRecord.status == "pending").count()

    # 今日记录
    today = datetime.now().date()
    today_start = datetime.combine(today, datetime.min.time())
    today_end = datetime.combine(today, datetime.max.time())
    today_records = total_query.filter(
        CardRecord.created_at >= today_start, CardRecord.created_at <= today_end
    ).count()

    # 昨日记录
    yesterday = today - timedelta(days=1)
    yesterday_start = datetime.combine(yesterday, datetime.min.time())
    yesterday_end = datetime.combine(yesterday, datetime.max.time())
    yesterday_records = total_query.filter(
        CardRecord.created_at >= yesterday_start, CardRecord.created_at <= yesterday_end
    ).count()

    return MerchantStatistics(
        total_records=total_records,
        success_records=success_records,
        failed_records=failed_records,
        pending_records=pending_records,
        today_records=today_records,
        yesterday_records=yesterday_records,
    )


def verify_merchant_signature(
    merchant: Merchant,
    api_key: str,
    signature: str,
    timestamp: str,
    nonce: str,
    data: Dict[str, Any],
) -> bool:
    """验证商家API签名"""
    if merchant.api_key != api_key:
        return False

    # 检查时间戳是否在有效期内（5分钟）
    try:
        ts = int(timestamp)
        # 修复时区问题：使用统一的时间戳获取方法
        from app.utils.time_utils import get_current_timestamp
        current_ts = int(get_current_timestamp())
        if abs(current_ts - ts) > 300:  # 5分钟有效期
            return False
    except (ValueError, TypeError):
        return False

    # 构建签名字符串
    # 1. 将所有参数按键排序并拼接
    sorted_data = dict(sorted(data.items()))
    data_str = "&".join([f"{k}={v}" for k, v in sorted_data.items()])

    # 2. 拼接时间戳和随机字符串
    message = f"{data_str}&timestamp={timestamp}&nonce={nonce}"

    # 3. 使用 HMAC-SHA256 计算签名
    expected_signature = hmac.new(
        merchant.api_secret.encode(), message.encode(), hashlib.sha256
    ).hexdigest()

    # 4. 比较签名
    return hmac.compare_digest(signature, expected_signature)


def generate_access_token(merchant: Merchant) -> Dict[str, Any]:
    """生成商家访问令牌"""
    # 生成随机token
    token = "".join(random.choices(string.ascii_letters + string.digits, k=32))

    # 计算过期时间（24小时）
    expires_at = int(time.time()) + 86400

    # 返回token信息
    return {"access_token": token, "token_type": "bearer", "expires_at": expires_at}


def check_ip_whitelist(merchant: Merchant, client_ip: str) -> bool:
    """检查IP是否在白名单中"""
    if not merchant.allowed_ips:
        return True  # 没有设置白名单则允许所有IP

    whitelist = [ip.strip() for ip in merchant.allowed_ips.split(",")]
    return client_ip in whitelist


def get_api_usage_statistics(db: Session, merchant_id: int) -> Dict[str, Any]:
    """获取商家API使用统计

    Args:
        db: 数据库会话
        merchant_id: 商家ID

    Returns:
        Dict[str, Any]: API使用统计数据
    """
    # 获取商家统计数据
    stats = generate_merchant_statistics(db, merchant_id)

    # 计算API使用统计
    total = stats.success_records + stats.failed_records
    today = stats.today_records

    # 模拟平均响应时间（实际应用中应该从数据库中计算）
    avg_response_time = round(random.uniform(100, 500), 2)

    return {"total": total, "today": today, "avg_response_time": avg_response_time}


def create_merchant(
    db: Session, merchant_in: schemas.MerchantCreate, created_by: Optional[int] = None
) -> Merchant:
    """
    创建新商家的业务逻辑

    Args:
        db: 数据库会话
        merchant_in: 商家创建数据
        created_by: 创建者ID

    Returns:
        Merchant: 创建的商家对象

    Raises:
        HTTPException: 当商家编码或名称已存在时
    """
    # 检查用户是否输入了商家编码，如果没有输入就自动生成
    if not merchant_in.code:
        merchant_in.code = helpers.generate_merchant_code(merchant_in.name)
        attempt = 0
        while crud.merchant.get_by_code(db=db, code=merchant_in.code) and attempt < 10:
            merchant_in.code = helpers.generate_merchant_code(merchant_in.name)
            attempt += 1

        # 如果尝试10次后仍然存在冲突，则抛出异常
        if attempt >= 10:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="无法生成唯一的商家编码，请手动指定",
            )
    # 检查商家编码是否已存在
    if crud.merchant.get_by_code(db=db, code=merchant_in.code):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="商家编码已存在",
        )

    # 检查商家名称是否已存在
    stmt = select(Merchant).where(Merchant.name == merchant_in.name)
    existing_merchant = db.execute(stmt).scalar_one_or_none()
    if existing_merchant:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="商家名称已存在",
        )

    try:
        # 创建商家 - crud.merchant.create handles API key generation
        merchant = crud.merchant.create(
            db=db, obj_in=merchant_in, created_by=created_by
        )

        # 这里可以添加其他业务逻辑，如创建默认配置、发送通知等

        return merchant
    except Exception as e:
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"创建商家失败: {str(e)}",
        )
