<template>
  <div class="menu-management">
    <div class="search-section">
      <div class="search-form">
        <div class="form-item">
          <label>菜单名称</label>
          <el-input
            v-model="searchForm.title"
            placeholder="请输入菜单名称"
            clearable
            style="width: 200px"
          />
        </div>
        <div class="form-item">
          <label>状态</label>
          <el-select
            v-model="searchForm.status"
            placeholder="状态"
            clearable
            style="width: 120px"
          >
            <el-option label="全部" value="" />
            <el-option label="启用" value="1" />
            <el-option label="禁用" value="0" />
          </el-select>
        </div>
        <div class="actions">
          <el-button type="primary" @click="handleSearch">
            <el-icon><Search /></el-icon>
            搜索
          </el-button>
          <el-button @click="handleReset">重置</el-button>
        </div>
        <div class="toolbar">
          <el-button type="primary" @click="handleAdd">
            <el-icon><Plus /></el-icon>
            新增菜单
          </el-button>
          <el-button @click="handleExpandAll">
            <el-icon><Expand /></el-icon>
            展开全部
          </el-button>
          <el-button @click="handleCollapseAll">
            <el-icon><Fold /></el-icon>
            收起全部
          </el-button>
        </div>
      </div>
    </div>

    <el-divider />

    <div class="table-section">
      <el-table
        ref="tableRef"
        :data="tableData"
        style="width: 100%"
        v-loading="loading"
        row-key="id"
        :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
        :default-expand-all="false"
      >
        <el-table-column prop="title" label="菜单名称" width="200">
          <template #default="{ row }">
            <div class="menu-title">
              <el-icon v-if="row.icon" class="menu-icon">
                <component :is="row.icon" />
              </el-icon>
              <span>{{ row.title }}</span>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="path" label="路径" />
        <el-table-column prop="component" label="组件" />
        <el-table-column prop="permission_code" label="权限标识" />
        <el-table-column prop="sort_order" label="排序" width="80" />
        <el-table-column prop="status" label="状态" width="80">
          <template #default="{ row }">
            <el-tag :type="row.status === 1 ? 'success' : 'danger'">
              {{ row.status === 1 ? '启用' : '禁用' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="260" fixed="right">
          <template #default="{ row }">
            <div class="action-buttons">
              <el-button type="primary" size="small" @click="handleEdit(row)">
                编辑
              </el-button>
              <el-button type="success" size="small" @click="handleAddChild(row)">
                新增子菜单
              </el-button>
              <el-button type="danger" size="small" @click="handleDelete(row)">
                删除
              </el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 菜单表单对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="dialogTitle"
      width="800px"
    >
      <el-form
        ref="formRef"
        :model="form"
        :rules="rules"
        label-width="120px"
      >
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="菜单名称" prop="title">
              <el-input v-model="form.title" placeholder="请输入菜单名称" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="菜单编码" prop="code">
              <el-input v-model="form.code" placeholder="请输入菜单编码" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="父级菜单" prop="parent_id">
              <el-tree-select
                v-model="form.parent_id"
                :data="menuTreeData"
                :props="{ label: 'title', value: 'id' }"
                placeholder="请选择父级菜单"
                clearable
                check-strictly
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="菜单类型" prop="type">
              <el-radio-group v-model="form.type">
                <el-radio :value="1">目录</el-radio>
                <el-radio :value="2">菜单</el-radio>
                <el-radio :value="3">按钮</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="路由路径" prop="path">
              <el-input v-model="form.path" placeholder="请输入路由路径" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="组件路径" prop="component">
              <el-input v-model="form.component" placeholder="请输入组件路径" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="权限标识" prop="permission_code">
              <el-input v-model="form.permission_code" placeholder="请输入权限标识" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="菜单图标" prop="icon">
              <el-input v-model="form.icon" placeholder="请输入图标名称" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="排序" prop="sort_order">
              <el-input-number v-model="form.sort_order" :min="0" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="状态" prop="status">
              <el-radio-group v-model="form.status">
                <el-radio :value="1">启用</el-radio>
                <el-radio :value="0">禁用</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="是否隐藏" prop="hidden">
          <el-switch v-model="form.hidden" />
        </el-form-item>

        <el-form-item label="备注" prop="remark">
          <el-input
            v-model="form.remark"
            type="textarea"
            :rows="3"
            placeholder="请输入备注"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleSubmit">确定</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Search, Plus, Expand, Fold } from '@element-plus/icons-vue'
import { menuApi } from '@/api/modules/menu'

// 响应式数据
const loading = ref(false)
const tableData = ref([])
const menuTreeData = ref([])
const dialogVisible = ref(false)
const dialogTitle = ref('')
const formRef = ref()
const tableRef = ref()

// 搜索表单
const searchForm = reactive({
  title: '',
  status: ''
})

// 菜单表单
const form = reactive({
  id: null,
  title: '',
  code: '',
  parent_id: null,
  type: 2,
  path: '',
  component: '',
  permission_code: '',
  icon: '',
  sort_order: 0,
  status: 1,
  hidden: false,
  remark: ''
})

// 表单验证规则
const rules = {
  title: [
    { required: true, message: '请输入菜单名称', trigger: 'blur' }
  ],
  code: [
    { required: true, message: '请输入菜单编码', trigger: 'blur' }
  ]
}

// 方法
const fetchData = async () => {
  loading.value = true
  try {
    // 调用API获取菜单列表，只获取可见菜单
    const response = await menuApi.getList({
      ...searchForm,
      visible_only: true
    })

    // 转换数据格式以适配前端表格
    tableData.value = response.map(menu => ({
      id: menu.id,
      title: menu.name,
      code: menu.code,
      parent_id: menu.parent_id,
      type: menu.type === 'menu' ? 2 : 1,
      path: menu.path,
      component: menu.component || '',
      permission_code: menu.code,
      icon: menu.icon,
      sort_order: menu.sort_order,
      status: menu.is_enabled ? 1 : 0,
      hidden: !menu.is_visible
    }))

    console.log('菜单数据:', tableData.value)

    // 构建树形数据
    menuTreeData.value = buildMenuTree([...tableData.value])
  } catch (error) {
    ElMessage.error('获取菜单列表失败')
  } finally {
    loading.value = false
  }
}

const buildMenuTree = (data) => {
  // 递归构建树形结构
  const tree = []
  const map = {}

  data.forEach(item => {
    map[item.id] = { ...item, children: [] }
  })

  data.forEach(item => {
    if (item.parent_id) {
      if (map[item.parent_id]) {
        map[item.parent_id].children.push(map[item.id])
      }
    } else {
      tree.push(map[item.id])
    }
  })

  return tree
}

const handleSearch = () => {
  fetchData()
}

const handleReset = () => {
  Object.assign(searchForm, {
    title: '',
    status: ''
  })
  handleSearch()
}

const handleAdd = () => {
  dialogTitle.value = '新增菜单'
  Object.assign(form, {
    id: null,
    title: '',
    code: '',
    parent_id: null,
    type: 2,
    path: '',
    component: '',
    permission_code: '',
    icon: '',
    sort_order: 0,
    status: 1,
    hidden: false,
    remark: ''
  })
  dialogVisible.value = true
}

const handleEdit = (row) => {
  dialogTitle.value = '编辑菜单'
  Object.assign(form, { ...row })
  dialogVisible.value = true
}

const handleAddChild = (row) => {
  dialogTitle.value = '新增子菜单'
  Object.assign(form, {
    id: null,
    title: '',
    code: '',
    parent_id: row.id,
    type: 2,
    path: '',
    component: '',
    permission_code: '',
    icon: '',
    sort_order: 0,
    status: 1,
    hidden: false,
    remark: ''
  })
  dialogVisible.value = true
}

const handleDelete = async (row) => {
  try {
    await ElMessageBox.confirm('确定要删除该菜单吗？', '提示', {
      type: 'warning'
    })

    // TODO: 调用API删除菜单
    // await menuApi.delete(row.id)

    ElMessage.success('删除成功')
    fetchData()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败')
    }
  }
}

const handleSubmit = async () => {
  try {
    await formRef.value.validate()

    // TODO: 调用API保存菜单
    // if (form.id) {
    //   await menuApi.update(form.id, form)
    // } else {
    //   await menuApi.create(form)
    // }

    ElMessage.success('保存成功')
    dialogVisible.value = false
    fetchData()
  } catch (error) {
    if (error !== false) {
      ElMessage.error('保存失败')
    }
  }
}

const handleExpandAll = () => {
  // 展开所有节点
  const expandAll = (data) => {
    data.forEach(item => {
      tableRef.value.toggleRowExpansion(item, true)
      if (item.children && item.children.length > 0) {
        expandAll(item.children)
      }
    })
  }
  expandAll(tableData.value)
}

const handleCollapseAll = () => {
  // 收起所有节点
  const collapseAll = (data) => {
    data.forEach(item => {
      tableRef.value.toggleRowExpansion(item, false)
      if (item.children && item.children.length > 0) {
        collapseAll(item.children)
      }
    })
  }
  collapseAll(tableData.value)
}

// 生命周期
onMounted(() => {
  fetchData()
})
</script>

<style scoped>
.menu-management {
  padding: 20px;
}

.search-section {
  background: #fff;
  padding: 20px;
  border-radius: 8px;
  margin-bottom: 20px;
}

.search-form {
  display: flex;
  align-items: center;
  gap: 20px;
  flex-wrap: wrap;
}

.form-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.form-item label {
  white-space: nowrap;
  font-weight: 500;
}

.actions {
  display: flex;
  gap: 10px;
}

.toolbar {
  margin-left: auto;
  display: flex;
  gap: 10px;
}

.table-section {
  background: #fff;
  padding: 20px;
  border-radius: 8px;
}

.menu-title {
  display: flex;
  align-items: center;
  gap: 8px;
}

.menu-icon {
  font-size: 16px;
}

.action-buttons {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}
</style>
