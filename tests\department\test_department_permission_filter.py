#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试部门权限过滤功能
验证部门筛选接口的数据权限控制是否正确工作
"""

import sys
import os
import time
import random
import string

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.dirname(__file__))))

from test.conftest import TestBase, get_test_accounts, format_test_result, print_test_summary


class DepartmentPermissionFilterTestSuite(TestBase):
    """部门权限过滤测试套件"""
    
    def __init__(self):
        super().__init__()
        self.results = []
        self.test_accounts = get_test_accounts()
        self.admin_token = None
        self.test_merchant_id = None
        self.test_users = []
        self.test_roles = []
    
    def setup(self):
        """测试前置设置"""
        print("=== 测试前置设置 ===")
        
        # 管理员登录
        self.admin_token = self.login("admin", "7c222fb2927d828af22f592134e8932480637c0d")
        if not self.admin_token:
            print("❌ 管理员登录失败")
            return False
        
        print("✅ 测试前置设置完成")
        return True
    
    def create_test_role_with_permission(self, role_name: str, permission_code: str):
        """创建具有特定数据权限的测试角色"""
        timestamp = int(time.time())
        random_suffix = ''.join(random.choices(string.ascii_lowercase, k=4))
        
        # 创建角色
        role_data = {
            "name": f"{role_name}_{timestamp}",
            "code": f"{role_name.upper()}_{timestamp}_{random_suffix}",
            "description": f"测试角色_{role_name}",
            "is_enabled": True
        }
        
        status_code, response = self.make_request("POST", "/roles", self.admin_token, data=role_data)
        if status_code not in [200, 201]:
            print(f"❌ 创建角色失败: {response}")
            return None
        
        role_id = response.get("data", {}).get("id") or response.get("id")
        if not role_id:
            print(f"❌ 创建角色成功但未返回角色ID")
            return None
        
        self.test_roles.append(role_id)
        
        # 获取权限ID
        status_code, response = self.make_request("GET", "/permissions", self.admin_token)
        if status_code != 200:
            print(f"❌ 获取权限列表失败")
            return None
        
        permissions = response.get("data", {}).get("items", []) or response.get("items", [])
        permission_id = None
        for perm in permissions:
            if perm.get("code") == permission_code:
                permission_id = perm.get("id")
                break
        
        if not permission_id:
            print(f"❌ 找不到权限: {permission_code}")
            return None
        
        # 分配权限给角色
        perm_data = {"permission_ids": [permission_id]}
        status_code, response = self.make_request(
            "PUT", f"/roles/{role_id}/permissions", self.admin_token, data=perm_data
        )
        
        if status_code != 200:
            print(f"❌ 分配权限失败: {response}")
            return None
        
        return role_id
    
    def create_test_user_with_role(self, username: str, role_id: int, department_id: int):
        """创建具有特定角色的测试用户"""
        timestamp = int(time.time())
        
        # 创建用户
        user_data = {
            "username": f"{username}_{timestamp}",
            "email": f"{username}_{timestamp}@test.com",
            "password": "password123",
            "merchant_id": self.test_merchant_id,
            "department_id": department_id,
            "is_active": True
        }
        
        status_code, response = self.make_request("POST", "/users", self.admin_token, data=user_data)
        if status_code not in [200, 201]:
            print(f"❌ 创建用户失败: {response}")
            return None
        
        user_id = response.get("data", {}).get("id") or response.get("id")
        if not user_id:
            print(f"❌ 创建用户成功但未返回用户ID")
            return None
        
        self.test_users.append(user_id)
        
        # 分配角色给用户
        role_data = {"role_ids": [role_id]}
        status_code, response = self.make_request(
            "PUT", f"/users/{user_id}/roles", self.admin_token, data=role_data
        )
        
        if status_code != 200:
            print(f"❌ 分配角色失败: {response}")
            return None
        
        # 用户登录获取token
        user_token = self.login(user_data["username"], user_data["password"])
        if not user_token:
            print(f"❌ 测试用户登录失败")
            return None
        
        return {
            "user_id": user_id,
            "username": user_data["username"],
            "token": user_token,
            "department_id": department_id
        }
    
    def get_test_merchant_and_departments(self):
        """获取测试商户和部门数据"""
        # 获取商户列表
        status_code, response = self.make_request("GET", "/merchants", self.admin_token)
        if status_code != 200:
            print(f"❌ 获取商户列表失败")
            return None
        
        merchants = response.get("data", {}).get("items", []) or response.get("items", [])
        if not merchants:
            print(f"❌ 没有找到测试商户")
            return None
        
        self.test_merchant_id = merchants[0]["id"]
        
        # 获取部门列表
        params = {"merchant_id": self.test_merchant_id}
        status_code, response = self.make_request("GET", "/departments", self.admin_token, params=params)
        if status_code != 200:
            print(f"❌ 获取部门列表失败")
            return None
        
        departments = response.get("data", {}).get("items", []) or response.get("items", [])
        if len(departments) < 3:
            print(f"❌ 部门数量不足，需要至少3个部门进行测试")
            return None
        
        # 找到有层级关系的部门
        root_dept = None
        child_dept = None
        sibling_dept = None
        
        for dept in departments:
            if dept.get("parent_id") is None:
                root_dept = dept
            elif dept.get("parent_id") and not child_dept:
                child_dept = dept
            elif dept.get("parent_id") and child_dept and dept["id"] != child_dept["id"]:
                sibling_dept = dept
                break
        
        if not all([root_dept, child_dept, sibling_dept]):
            print(f"❌ 找不到合适的部门层级结构")
            return None
        
        return {
            "merchant_id": self.test_merchant_id,
            "departments": {
                "root": root_dept,
                "child": child_dept,
                "sibling": sibling_dept,
                "all": departments
            }
        }
    
    def test_department_filter_with_sub_permission(self):
        """测试具有本部门及子部门数据权限的用户部门筛选"""
        print("\n=== 测试本部门及子部门数据权限的部门筛选 ===")
        
        # 获取测试数据
        test_data = self.get_test_merchant_and_departments()
        if not test_data:
            self.results.append(format_test_result(
                "部门筛选测试前置条件",
                False,
                "无法获取测试数据"
            ))
            return
        
        # 创建具有本部门及子部门数据权限的角色
        role_id = self.create_test_role_with_permission("sub_dept_role", "data:department:sub")
        if not role_id:
            self.results.append(format_test_result(
                "创建子部门权限角色",
                False,
                "无法创建测试角色"
            ))
            return
        
        # 创建测试用户（分配到有子部门的部门）
        parent_dept = test_data["departments"]["child"]  # 使用child作为父部门
        test_user = self.create_test_user_with_role("test_sub_user", role_id, parent_dept["id"])
        if not test_user:
            self.results.append(format_test_result(
                "创建子部门权限用户",
                False,
                "无法创建测试用户"
            ))
            return
        
        # 测试用户获取部门列表
        params = {"merchant_id": self.test_merchant_id}
        status_code, response = self.make_request("GET", "/departments", test_user["token"], params=params)
        
        if status_code == 200:
            departments = response.get("data", {}).get("items", []) or response.get("items", [])
            dept_names = [dept["name"] for dept in departments]
            
            # 验证用户只能看到自己部门及其子部门
            user_dept_name = parent_dept["name"]
            
            # 检查是否包含用户所属部门
            if user_dept_name in dept_names:
                self.results.append(format_test_result(
                    "子部门权限用户能看到自己部门",
                    True,
                    f"用户能正确看到自己所属部门: {user_dept_name}"
                ))
                print(f"✅ 用户能正确看到自己所属部门: {user_dept_name}")
            else:
                self.results.append(format_test_result(
                    "子部门权限用户能看到自己部门",
                    False,
                    f"用户无法看到自己所属部门: {user_dept_name}"
                ))
                print(f"❌ 用户无法看到自己所属部门: {user_dept_name}")
            
            # 检查是否正确过滤了其他部门
            all_dept_count = len(test_data["departments"]["all"])
            filtered_dept_count = len(departments)
            
            if filtered_dept_count < all_dept_count:
                self.results.append(format_test_result(
                    "子部门权限正确过滤部门",
                    True,
                    f"正确过滤部门列表: 总数{all_dept_count} -> 过滤后{filtered_dept_count}"
                ))
                print(f"✅ 正确过滤部门列表: 总数{all_dept_count} -> 过滤后{filtered_dept_count}")
            else:
                self.results.append(format_test_result(
                    "子部门权限正确过滤部门",
                    False,
                    f"未正确过滤部门列表: 总数{all_dept_count} -> 过滤后{filtered_dept_count}"
                ))
                print(f"❌ 未正确过滤部门列表: 总数{all_dept_count} -> 过滤后{filtered_dept_count}")
        
        else:
            self.results.append(format_test_result(
                "子部门权限用户获取部门列表",
                False,
                f"获取部门列表失败，状态码: {status_code}"
            ))
            print(f"❌ 获取部门列表失败，状态码: {status_code}")
    
    def test_department_filter_with_own_permission(self):
        """测试具有本部门数据权限的用户部门筛选"""
        print("\n=== 测试本部门数据权限的部门筛选 ===")
        
        # 获取测试数据
        test_data = self.get_test_merchant_and_departments()
        if not test_data:
            return
        
        # 创建具有本部门数据权限的角色
        role_id = self.create_test_role_with_permission("own_dept_role", "data:department:own")
        if not role_id:
            return
        
        # 创建测试用户
        test_dept = test_data["departments"]["child"]
        test_user = self.create_test_user_with_role("test_own_user", role_id, test_dept["id"])
        if not test_user:
            return
        
        # 测试用户获取部门列表
        params = {"merchant_id": self.test_merchant_id}
        status_code, response = self.make_request("GET", "/departments", test_user["token"], params=params)
        
        if status_code == 200:
            departments = response.get("data", {}).get("items", []) or response.get("items", [])
            
            # 应该只能看到自己所属的部门
            if len(departments) == 1 and departments[0]["id"] == test_dept["id"]:
                self.results.append(format_test_result(
                    "本部门权限用户只能看到自己部门",
                    True,
                    f"用户正确只能看到自己部门: {test_dept['name']}"
                ))
                print(f"✅ 用户正确只能看到自己部门: {test_dept['name']}")
            else:
                self.results.append(format_test_result(
                    "本部门权限用户只能看到自己部门",
                    False,
                    f"用户看到了{len(departments)}个部门，应该只能看到1个"
                ))
                print(f"❌ 用户看到了{len(departments)}个部门，应该只能看到1个")
        else:
            self.results.append(format_test_result(
                "本部门权限用户获取部门列表",
                False,
                f"获取部门列表失败，状态码: {status_code}"
            ))
            print(f"❌ 获取部门列表失败，状态码: {status_code}")

    def cleanup(self):
        """清理测试数据"""
        print("\n=== 清理测试数据 ===")

        # 清理测试用户
        for user_id in self.test_users[:]:
            status_code, _ = self.make_request("DELETE", f"/users/{user_id}", self.admin_token)
            if status_code in [200, 204]:
                print(f"✅ 清理测试用户: {user_id}")
                self.test_users.remove(user_id)
            else:
                print(f"⚠️ 清理测试用户失败: {user_id}")

        # 清理测试角色
        for role_id in self.test_roles[:]:
            status_code, _ = self.make_request("DELETE", f"/roles/{role_id}", self.admin_token)
            if status_code in [200, 204]:
                print(f"✅ 清理测试角色: {role_id}")
                self.test_roles.remove(role_id)
            else:
                print(f"⚠️ 清理测试角色失败: {role_id}")

    def run_all_tests(self):
        """运行所有部门权限过滤测试"""
        print("🚀 开始部门权限过滤测试")
        print("="*60)

        start_time = time.time()

        # 前置设置
        if not self.setup():
            print("❌ 测试前置设置失败，跳过测试")
            return []

        try:
            # 运行所有测试
            self.test_department_filter_with_sub_permission()
            self.test_department_filter_with_own_permission()
        finally:
            # 清理测试数据
            self.cleanup()

        end_time = time.time()
        duration = end_time - start_time

        # 打印测试摘要
        print_test_summary(self.results)
        print(f"测试耗时: {duration:.2f}秒")

        return self.results


def main():
    """主函数"""
    test_suite = DepartmentPermissionFilterTestSuite()
    results = test_suite.run_all_tests()

    # 判断测试是否通过
    failed_tests = [r for r in results if not r.get("success", False)]

    if not failed_tests:
        print("\n🎉 所有部门权限过滤测试通过！")
        return 0
    else:
        print(f"\n⚠️ 发现 {len(failed_tests)} 个问题需要修复")
        for test in failed_tests:
            print(f"  - {test['test_name']}: {test['message']}")
        return 1


if __name__ == "__main__":
    sys.exit(main())
