# 沃尔玛绑卡网关 - Docker开发环境构建脚本
# 专门用于本地Windows Docker开发环境
param(
    [switch]$Clean = $false,
    [switch]$NoBuild = $false,
    [switch]$Verbose = $false,
    [switch]$HotReload = $true,    # 默认启用热重载
    [switch]$DevMode = $true       # 默认启用开发模式
)

function Write-Info($message) { Write-Host "[INFO] $message" -ForegroundColor Green }
function Write-Success($message) { Write-Host "[SUCCESS] $message" -ForegroundColor Cyan }
function Write-Warning($message) { Write-Host "[WARN] $message" -ForegroundColor Yellow }
function Write-Error($message) { Write-Host "[ERROR] $message" -ForegroundColor Red }
function Write-Debug($message) { if ($Verbose) { Write-Host "[DEBUG] $message" -ForegroundColor Gray } }

function Main {
    Write-Host "=========================================" -ForegroundColor Blue
    Write-Host "沃尔玛绑卡网关 - Docker开发环境构建" -ForegroundColor Blue
    Write-Host "本地开发 | 快速构建 | Docker容器" -ForegroundColor Blue
    Write-Host "=========================================" -ForegroundColor Blue
    Write-Output ""
    
    $reloadStatus = if ($HotReload) { "启用" } else { "禁用" }
    $modeStatus = if ($DevMode) { "开发模式" } else { "生产模式" }
    Write-Info "热重载: $reloadStatus"
    Write-Info "运行模式: $modeStatus"
    Write-Output ""
    
    # 检查Docker环境
    if (!(CheckDockerEnvironment)) { return 1 }
    
    # 清理（如果需要）
    if ($Clean) {
        CleanupDocker
    }
    
    # 准备开发环境
    if (!(PrepareDevEnvironment)) { return 1 }
    
    # 构建Docker镜像
    if (!$NoBuild) {
        if (!(BuildDockerImage)) { return 1 }
    }
    
    # 启动服务
    if (!(StartServices)) { return 1 }
    
    # 验证服务
    VerifyServices
    
    Write-Success "Docker开发环境构建完成！"
    ShowServiceInfo
    return 0
}

function CheckDockerEnvironment {
    Write-Info "检查Docker环境..."
    
    # 检查Docker是否安装
    if (!(Get-Command docker -ErrorAction SilentlyContinue)) {
        Write-Error "Docker未安装，请先安装Docker Desktop"
        return $false
    }
    
    # 检查Docker是否运行
    try {
        docker info > $null 2>&1
        if ($LASTEXITCODE -ne 0) {
            Write-Error "Docker未运行，请启动Docker Desktop"
            return $false
        }
    } catch {
        Write-Error "无法连接到Docker，请检查Docker状态"
        return $false
    }
    
    # 检查docker-compose
    if (!(Get-Command docker-compose -ErrorAction SilentlyContinue)) {
        Write-Error "docker-compose未安装"
        return $false
    }
    
    Write-Success "Docker环境检查通过"
    return $true
}

function CleanupDocker {
    Write-Info "清理Docker环境..."
    
    # 停止并删除容器
    Write-Info "停止现有容器..."
    docker-compose down --remove-orphans 2>$null
    
    # 删除相关容器
    $containers = docker ps -a --filter "name=walmart-gateway" --format "{{.ID}}" 2>$null
    if ($containers) {
        Write-Info "删除旧容器..."
        $containers | ForEach-Object { docker rm -f $_ 2>$null }
    }
    
    # 删除相关镜像
    $images = docker images --filter "reference=walmart-gateway*" --format "{{.ID}}" 2>$null
    if ($images) {
        Write-Info "删除旧镜像..."
        $images | ForEach-Object { docker rmi -f $_ 2>$null }
    }
    
    # 清理悬空镜像
    Write-Info "清理悬空镜像..."
    docker image prune -f 2>$null
    
    Write-Success "Docker环境清理完成"
}

function PrepareDevEnvironment {
    Write-Info "准备开发环境..."
    
    # 检查并创建环境变量文件
    if (!(Test-Path ".env")) {
        if (Test-Path ".env.example") {
            Write-Info "复制环境变量模板..."
            Copy-Item ".env.example" ".env"
            Write-Warning "请编辑 .env 文件配置外部服务地址"
        } else {
            Write-Warning "未找到环境变量配置文件"
            return $false
        }
    }
    
    # 如果启用开发模式，修改环境变量
    if ($DevMode) {
        Write-Info "配置开发模式环境变量..."
        $envContent = Get-Content ".env"
        $envContent = $envContent -replace "GIN_MODE=release", "GIN_MODE=debug"
        $envContent = $envContent -replace "LOG_LEVEL=info", "LOG_LEVEL=debug"
        $envContent | Set-Content ".env"
    }
    
    # 创建开发用的docker-compose覆盖文件
    if ($HotReload) {
        CreateHotReloadConfig
    }
    
    # 确保日志目录存在
    if (!(Test-Path "logs")) {
        New-Item -ItemType Directory -Path "logs" | Out-Null
        Write-Info "创建日志目录: logs/"
    }
    
    Write-Success "开发环境准备完成"
    return $true
}

function CreateHotReloadConfig {
    Write-Info "创建热重载配置..."
    
    $hotReloadConfig = @"
version: "3.8"

services:
  walmart-gateway:
    volumes:
      # 挂载源代码以支持热重载
      - .:/app/src:ro
      # 挂载Go模块缓存
      - go-mod-cache:/go/pkg/mod
      # 挂载构建缓存
      - go-build-cache:/root/.cache/go-build
    environment:
      # 开发模式环境变量
      - GIN_MODE=debug
      - LOG_LEVEL=debug
      - HOT_RELOAD=true
    command: >
      sh -c "
        echo '启动开发模式...' &&
        cd /app/src &&
        go mod download &&
        echo '安装热重载工具...' &&
        go install github.com/cosmtrek/air@latest &&
        echo '启动热重载服务...' &&
        air -c .air.toml
      "

volumes:
  go-mod-cache:
  go-build-cache:
"@
    
    $hotReloadConfig | Out-File -FilePath "docker-compose.dev.yml" -Encoding UTF8
    Write-Success "热重载配置已创建: docker-compose.dev.yml"
}

function BuildDockerImage {
    Write-Info "构建Docker镜像..."
    
    # 构建参数
    $buildArgs = @()
    
    # 设置镜像源（提高构建速度）
    $dockerRegistry = $env:DOCKER_REGISTRY
    if (!$dockerRegistry) {
        $dockerRegistry = "docker.1ms.run"
    }
    $buildArgs += "--build-arg", "DOCKER_REGISTRY=$dockerRegistry"
    
    # 开发模式构建参数
    if ($DevMode) {
        $buildArgs += "--target", "development"
    }
    
    # 执行构建
    Write-Info "开始构建镜像（使用镜像源: $dockerRegistry）..."
    
    $composeFiles = @("-f", "docker-compose.yml")
    if ($HotReload -and (Test-Path "docker-compose.dev.yml")) {
        $composeFiles += @("-f", "docker-compose.dev.yml")
    }
    
    $buildCmd = @("docker-compose") + $composeFiles + @("build") + $buildArgs
    
    if ($Verbose) {
        $buildCmd += "--progress=plain"
    }
    
    & $buildCmd[0] $buildCmd[1..($buildCmd.Length-1)]
    
    if ($LASTEXITCODE -ne 0) {
        Write-Error "Docker镜像构建失败"
        return $false
    }
    
    Write-Success "Docker镜像构建完成"
    return $true
}

function StartServices {
    Write-Info "启动服务..."
    
    $composeFiles = @("-f", "docker-compose.yml")
    if ($HotReload -and (Test-Path "docker-compose.dev.yml")) {
        $composeFiles += @("-f", "docker-compose.dev.yml")
        Write-Info "使用热重载配置启动服务"
    }
    
    $startCmd = @("docker-compose") + $composeFiles + @("up", "-d")
    
    & $startCmd[0] $startCmd[1..($startCmd.Length-1)]
    
    if ($LASTEXITCODE -ne 0) {
        Write-Error "服务启动失败"
        return $false
    }
    
    # 等待服务启动
    Write-Info "等待服务启动..."
    Start-Sleep -Seconds 10
    
    Write-Success "服务启动完成"
    return $true
}

function VerifyServices {
    Write-Info "验证服务状态..."
    
    # 检查容器状态
    $containerStatus = docker-compose ps --format "table {{.Name}}\t{{.State}}\t{{.Ports}}"
    Write-Info "容器状态:"
    $containerStatus | ForEach-Object { Write-Info "  $_" }
    
    # 健康检查
    Write-Info "执行健康检查..."
    $healthUrl = "http://localhost:21000/health"
    
    try {
        $response = Invoke-RestMethod -Uri $healthUrl -TimeoutSec 10
        Write-Success "健康检查通过: $healthUrl"
        Write-Debug "响应: $($response | ConvertTo-Json)"
    } catch {
        Write-Warning "健康检查失败: $($_.Exception.Message)"
        Write-Info "请检查服务日志: docker-compose logs"
    }
}

function ShowServiceInfo {
    Write-Output ""
    Write-Host "🎉 Docker开发环境已就绪！" -ForegroundColor Green
    Write-Output ""
    
    Write-Info "📋 服务信息:"
    Write-Info "   网关地址: http://localhost:21000"
    Write-Info "   健康检查: http://localhost:21000/health"
    Write-Info "   监控端口: http://localhost:9090"
    
    if ($HotReload) {
        Write-Output ""
        Write-Info "🔥 热重载已启用:"
        Write-Info "   修改代码后服务将自动重启"
        Write-Info "   查看重载日志: docker-compose logs -f walmart-gateway"
    }
    
    Write-Output ""
    Write-Info "📋 常用命令:"
    Write-Info "   查看日志: docker-compose logs -f"
    Write-Info "   查看状态: docker-compose ps"
    Write-Info "   重启服务: docker-compose restart"
    Write-Info "   停止服务: docker-compose down"
    
    Write-Output ""
    Write-Info "🔧 开发建议:"
    Write-Info "   1. 修改代码后查看热重载日志确认重启"
    Write-Info "   2. 使用 docker-compose logs -f 查看实时日志"
    Write-Info "   3. 确保外部服务（MySQL、Redis、RabbitMQ）正常运行"
    Write-Info "   4. 使用 .env 文件配置不同的开发环境"
    
    Write-Output ""
    Write-Success "🎉 Docker开发环境已就绪！"
}

# 执行主函数
try {
    $result = Main
    exit $result
} catch {
    Write-Error "构建过程中发生错误: $($_.Exception.Message)"
    exit 1
}
