"""
命令处理器注册表
"""

from typing import List, Dict, Any
from telegram.ext import <PERSON><PERSON><PERSON><PERSON>, MessageHandler, CallbackQueryHandler, filters
from sqlalchemy.orm import Session

from app.core.logging import get_logger
from ..config import BotConfig
from ..rate_limiter import RateLimiter
from .help_handler import HelpCommandHandler
from .bind_handler import BindCommandHandler
from .stats_handler import StatsCommandHandler
from .ck_stats_handler import CKStatsCommandHandler
from .callback_handler import CallbackQueryHandler as CallbackHandler

logger = get_logger(__name__)


class CommandHandlerRegistry:
    """命令处理器注册表"""
    
    def __init__(self, db_session: Session, config: BotConfig, rate_limiter: RateLimiter):
        self.db = db_session
        self.config = config
        self.rate_limiter = rate_limiter
        self.handlers: Dict[str, Any] = {}
        
        # 初始化命令处理器
        self._initialize_handlers()
    
    def _initialize_handlers(self):
        """初始化所有命令处理器"""
        # 帮助命令处理器
        help_handler = HelpCommandHandler(self.db, self.config, self.rate_limiter)
        self.handlers['help'] = CommandHandler('help', help_handler.handle)
        self.handlers['start'] = CommandHandler('start', help_handler.handle)
        
        # 绑定命令处理器
        bind_handler = BindCommandHandler(self.db, self.config, self.rate_limiter)
        self.handlers['bind'] = CommandHandler('bind', bind_handler.handle)
        self.handlers['unbind'] = CommandHandler('unbind', bind_handler.handle_unbind)
        self.handlers['verify'] = CommandHandler('verify', bind_handler.handle_verify)
        self.handlers['status'] = CommandHandler('status', bind_handler.handle_status)
        
        # 统计命令处理器
        stats_handler = StatsCommandHandler(self.db, self.config, self.rate_limiter)
        self.handlers['stats'] = CommandHandler('stats', stats_handler.handle)
        self.handlers['stats_week'] = CommandHandler('stats_week', stats_handler.handle_week)
        self.handlers['stats_month'] = CommandHandler('stats_month', stats_handler.handle_month)
        self.handlers['stats_custom'] = CommandHandler('stats_custom', stats_handler.handle_custom)

        # CK统计命令处理器
        ck_stats_handler = CKStatsCommandHandler(self.db, self.config, self.rate_limiter)
        self.handlers['ck_stats'] = CommandHandler('ck_stats', ck_stats_handler.handle)
        self.handlers['ck'] = CommandHandler('ck', ck_stats_handler.handle)
        self.handlers['ck_today'] = CommandHandler('ck_today', ck_stats_handler.handle_today)
        self.handlers['ck_week'] = CommandHandler('ck_week', ck_stats_handler.handle_week)
        self.handlers['ck_month'] = CommandHandler('ck_month', ck_stats_handler.handle_month)
        
        # 设置命令处理器（管理员专用）
        self.handlers['settings'] = CommandHandler('settings', bind_handler.handle_settings)
        
        # 消息处理器（只处理特定情况的非命令消息）
        # 1. 私聊消息
        # 2. 群组中@机器人的消息
        # 3. 包含特定关键词的消息
        self.handlers['message'] = MessageHandler(
            filters.TEXT & ~filters.COMMAND & (
                filters.ChatType.PRIVATE |  # 私聊消息
                self._create_mention_filter() |  # @机器人的消息
                self._create_keyword_filter()  # 包含特定关键词的消息
            ),
            help_handler.handle_message
        )

        # 回调查询处理器（处理按钮点击）
        callback_handler = CallbackHandler(self.db, self.config, self.rate_limiter)
        self.handlers['callback_query'] = CallbackQueryHandler(
            callback_handler.handle_callback_query
        )

        logger.info(f"已初始化 {len(self.handlers)} 个命令处理器")

    def _create_mention_filter(self):
        """创建@机器人过滤器"""
        def mention_filter(message):
            """检查消息是否@了机器人"""
            if not message.text or not message.entities:
                return False

            # 检查消息实体中是否有mention类型
            for entity in message.entities:
                if entity.type == 'mention':
                    # 这里我们暂时接受所有@mention，在handler中再做具体的机器人用户名检查
                    # 因为在过滤器阶段我们还没有bot实例来获取用户名
                    return True

            return False

        return filters.BaseFilter(mention_filter)

    def _create_keyword_filter(self):
        """创建关键词过滤器，只有包含特定关键词的消息才会被处理"""
        from ..keywords import check_keyword_match

        def keyword_filter(message):
            """检查消息是否包含关键词"""
            if not message.text:
                return False

            # 使用统一的关键词检查函数
            has_match, matched_keywords = check_keyword_match(message.text)
            return has_match

        return filters.BaseFilter(keyword_filter)

    def get_all_handlers(self) -> List[Any]:
        """获取所有处理器"""
        return list(self.handlers.values())
    
    def get_handler(self, command: str) -> Any:
        """获取指定命令的处理器"""
        return self.handlers.get(command)
    
    def get_command_list(self) -> List[Dict[str, str]]:
        """获取命令列表"""
        commands = [
            {
                "command": "help",
                "description": "显示帮助信息",
                "usage": "/help",
                "permission": "public"
            },
            {
                "command": "start", 
                "description": "开始使用机器人",
                "usage": "/start",
                "permission": "public"
            },
            {
                "command": "bind",
                "description": "绑定群组到商户",
                "usage": "/bind <绑定令牌>",
                "permission": "admin"
            },
            {
                "command": "unbind",
                "description": "解绑群组",
                "usage": "/unbind",
                "permission": "admin"
            },
            {
                "command": "verify",
                "description": "验证用户身份",
                "usage": "/verify <验证码>",
                "permission": "user"
            },
            {
                "command": "status",
                "description": "查看群组状态",
                "usage": "/status",
                "permission": "bound_group"
            },
            {
                "command": "stats",
                "description": "查看今日统计",
                "usage": "/stats",
                "permission": "bound_group"
            },
            {
                "command": "stats_week",
                "description": "查看本周统计",
                "usage": "/stats_week",
                "permission": "bound_group"
            },
            {
                "command": "stats_month",
                "description": "查看本月统计",
                "usage": "/stats_month",
                "permission": "bound_group"
            },
            {
                "command": "stats_custom",
                "description": "自定义时间统计",
                "usage": "/stats_custom YYYY-MM-DD YYYY-MM-DD",
                "permission": "bound_group"
            },
            {
                "command": "ck_stats",
                "description": "查看CK统计概览",
                "usage": "/ck_stats 或 /ck",
                "permission": "bound_group"
            },
            {
                "command": "ck_today",
                "description": "查看今日CK使用统计",
                "usage": "/ck_today",
                "permission": "bound_group"
            },
            {
                "command": "ck_week",
                "description": "查看本周CK使用统计",
                "usage": "/ck_week",
                "permission": "bound_group"
            },
            {
                "command": "ck_month",
                "description": "查看本月CK使用统计",
                "usage": "/ck_month",
                "permission": "bound_group"
            },
            {
                "command": "settings",
                "description": "群组设置",
                "usage": "/settings",
                "permission": "admin"
            }
        ]
        return commands
    
    def update_config(self, config: BotConfig):
        """更新配置"""
        self.config = config
        # 重新初始化处理器
        self._initialize_handlers()
        logger.info("命令处理器配置已更新")
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取统计信息"""
        return {
            "total_handlers": len(self.handlers),
            "command_handlers": len([h for h in self.handlers.values() if isinstance(h, CommandHandler)]),
            "message_handlers": len([h for h in self.handlers.values() if isinstance(h, MessageHandler)]),
            "available_commands": [cmd["command"] for cmd in self.get_command_list()]
        }
