"""
帮助命令处理器 - 清理后的版本
"""

from telegram import Update
from telegram.ext import ContextTypes

from .base_handler import BaseCommandHandler
from ..services.user_guide_service import UserGuideService, GuideType
from ..services.enhanced_error_handler import EnhancedErrorHandler


class HelpCommandHandler(BaseCommandHandler):
    """帮助命令处理器"""

    def __init__(self, db, config, rate_limiter):
        super().__init__(db, config, rate_limiter)

        # 初始化增强服务
        self.user_guide_service = UserGuideService(db, config)
        self.enhanced_error_handler = EnhancedErrorHandler(config)

    async def verify_permissions(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """帮助命令无需特殊权限验证"""
        pass
    
    async def execute_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """执行帮助命令"""
        try:
            # 使用智能引导服务获取个性化帮助
            help_text, keyboard = await self.user_guide_service.get_smart_guide(
                update, context, GuideType.WELCOME
            )
            
            await self.send_response(
                update,
                help_text,
                context=context,
                reply_markup=keyboard,
                parse_mode='Markdown'
            )
            
            # 分析用户状态用于日志记录
            user_state = await self.user_guide_service.analyze_user_state(update)
            
            return {
                "command": "help", 
                "user_state": user_state.value,
                "guide_provided": True
            }
            
        except Exception as e:
            self.logger.error(f"执行帮助命令失败: {e}")
            
            # 使用备用帮助信息
            fallback_text = """❌ **系统暂时无法提供个性化帮助**

请尝试以下基础操作：
• `/help` 或 `帮助` - 查看帮助信息
• `/status` 或 `状态` - 查看当前状态
• `/verify` 或 `验证` - 开始身份验证

📊 **数据查询**：
• `CK今日` 或 `ck统计` - 查看CK统计
• `今日数据` 或 `统计` - 查看统计数据

如需人工协助，请联系管理员。"""
            
            await self.send_response(update, fallback_text, parse_mode='Markdown')
            
            return {"command": "help", "error": str(e)}
    
    async def handle_start(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """处理 /start 命令 - 使用自动化服务"""
        try:
            # 使用自动化服务处理首次交互
            from ..services.automation_service import AutomationService
            automation_service = AutomationService(self.db, self.config)

            # 检查是否为首次交互，如果是则自动发送欢迎信息
            handled = await automation_service.handle_first_interaction(update, context)

            if not handled:
                # 如果不是首次交互，使用标准帮助
                return await self.execute_command(update, context)

            return {"command": "start", "first_interaction": True}

        except Exception as e:
            self.logger.error(f"处理start命令失败: {e}")
            # 回退到标准帮助
            return await self.execute_command(update, context)

    async def _should_respond_to_message(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> bool:
        """判断是否应该响应这条消息

        响应条件：
        1. 私聊消息 - 总是响应
        2. 群组中@机器人的消息 - 响应
        3. 群组中包含特定关键词的消息 - 响应
        4. 其他群组消息 - 不响应
        """
        try:
            chat = update.effective_chat
            message = update.message

            if not message or not message.text:
                return False

            # 1. 私聊消息总是响应
            if chat.type == 'private':
                return True

            # 2. 检查是否@了机器人
            if message.entities:
                bot_username = context.bot.username if context.bot else None
                for entity in message.entities:
                    if entity.type == 'mention':
                        # 提取@的用户名
                        mention_text = message.text[entity.offset:entity.offset + entity.length]
                        if bot_username and f"@{bot_username}" == mention_text:
                            return True

            # 3. 检查是否包含特定关键词（只在群组中检查）
            if chat.type in ['group', 'supergroup']:
                from ..keywords import check_keyword_match

                # 使用统一的关键词检查函数
                has_match, matched_keywords = check_keyword_match(message.text)
                if has_match:
                    self.logger.debug(f"群组消息关键词匹配: '{message.text}' -> {matched_keywords}")
                    return True

            # 4. 其他情况不响应
            return False

        except Exception as e:
            self.logger.error(f"判断是否响应消息时发生错误: {e}")
            # 出错时保守处理，不响应
            return False

    async def handle_message(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """处理非命令消息 - 提供智能帮助

        只在以下情况下响应：
        1. 私聊消息
        2. 群组中@机器人的消息
        3. 包含特定关键词的消息
        """
        try:
            # 检查消息类型和上下文
            chat = update.effective_chat
            message = update.message

            if not message or not message.text:
                return

            # 首先尝试自动收集群成员信息（在群组中）
            if chat.type in ['group', 'supergroup']:
                await self._try_collect_member_info(update, context)

            # 检查是否应该响应这条消息
            should_respond = await self._should_respond_to_message(update, context)
            if not should_respond:
                # 在群组中对于不需要响应的消息，直接返回，不做任何处理
                return

            # 分析用户消息内容，提供相关帮助
            message_text = message.text.lower()

            # 记录非命令消息的处理
            self.logger.info(f"处理非命令消息: chat_id={chat.id}, chat_type={chat.type}, text='{message_text[:50]}...'")

            # 使用统一的关键词路由器处理所有关键词
            from ..keywords import check_keyword_match
            has_match, matched_keywords = check_keyword_match(message.text)

            if has_match:
                # 有关键词匹配，使用关键词路由器处理
                self.logger.info(f"消息包含关键词 {matched_keywords}，使用关键词路由器处理")

                from .keyword_router import get_keyword_router
                keyword_router = get_keyword_router(self.db, self.config, self.rate_limiter)

                # 路由到对应的处理方法
                result = await keyword_router.route_keyword(update, context, matched_keywords)

                if result is not None:
                    return result

            # 如果关键词路由器没有处理成功，提供帮助信息
            else:
                await self._provide_contextual_help(update, context)

        except Exception as e:
            self.logger.error(f"处理消息失败: {e}")
            # 提供基础帮助
            await self.send_response(
                update,
                """抱歉，我无法理解您的消息。

💡 **您可以尝试**：
• 输入 `帮助` 或 `/help` - 获取完整帮助
• 输入 `CK今日` 或 `ck统计` - 查看CK统计
• 输入 `状态` 或 `/status` - 查看当前状态
• 输入 `今日数据` - 查看统计数据

🔍 **支持自然语言**：如 "ck今日统计"、"查看数据" 等"""
            )
            return {"command": "message_help", "error": str(e)}

    async def _try_collect_member_info(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """尝试自动收集群成员信息"""
        try:
            from ..services.member_collection_service import get_member_collection_service

            # 获取成员收集服务
            collection_service = get_member_collection_service(self.db, self.config)

            # 尝试收集成员信息
            collected = await collection_service.collect_member_from_message(update, context)

            if collected:
                self.logger.info(f"成功自动收集群成员信息: user_id={update.effective_user.id}")

        except Exception as e:
            # 收集失败不影响正常消息处理
            self.logger.debug(f"自动收集群成员信息失败: {e}")
