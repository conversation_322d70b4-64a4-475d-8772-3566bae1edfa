"""
Playwright测试基类
提供浏览器自动化测试的基础功能
"""

import asyncio
from playwright.async_api import async_playwright, <PERSON><PERSON><PERSON>, BrowserContext, Page


class BasePlaywrightTest:
    """Playwright测试基类"""

    def __init__(self):
        self.playwright = None
        self.browser: Browser = None
        self.context: BrowserContext = None
        self.page: Page = None
        self.base_url = "http://localhost:2000"
        
        # 测试账号信息
        self.admin_credentials = {
            "username": "admin",
            "password": "7c222fb2927d828af22f592134e8932480637c0d"
        }
        
        self.merchant_credentials = {
            "username": "test1",
            "password": "12345678"
        }

    async def setup(self):
        """设置浏览器环境"""
        self.playwright = await async_playwright().start()
        
        # 启动浏览器
        self.browser = await self.playwright.chromium.launch(
            headless=False,  # 设置为False以便观察测试过程
            slow_mo=500,     # 减慢操作速度以便观察
        )
        
        # 创建浏览器上下文
        self.context = await self.browser.new_context(
            viewport={"width": 1280, "height": 720},
            locale="zh-CN",
        )
        
        # 创建页面
        self.page = await self.context.new_page()
        
        # 设置默认超时时间
        self.page.set_default_timeout(30000)  # 30秒

    async def cleanup(self):
        """清理资源"""
        if self.page:
            await self.page.close()
        if self.context:
            await self.context.close()
        if self.browser:
            await self.browser.close()
        if self.playwright:
            await self.playwright.stop()

    async def login_as_admin(self):
        """以超级管理员身份登录"""
        await self.page.goto(f"{self.base_url}/#/login")
        await self.page.wait_for_load_state('networkidle')
        
        # 填写登录表单
        await self.page.fill('input[placeholder*="用户名"]', self.admin_credentials["username"])
        await self.page.fill('input[placeholder*="密码"]', self.admin_credentials["password"])
        
        # 点击登录按钮
        await self.page.click('button:has-text("登录")')
        
        # 等待登录完成
        await self.page.wait_for_url("**/dashboard", timeout=10000)
        await self.page.wait_for_load_state('networkidle')
        
        print("✅ 超级管理员登录成功")

    async def login_as_merchant(self):
        """以商户管理员身份登录"""
        await self.page.goto(f"{self.base_url}/#/login")
        await self.page.wait_for_load_state('networkidle')
        
        # 填写登录表单
        await self.page.fill('input[placeholder*="用户名"]', self.merchant_credentials["username"])
        await self.page.fill('input[placeholder*="密码"]', self.merchant_credentials["password"])
        
        # 点击登录按钮
        await self.page.click('button:has-text("登录")')
        
        # 等待登录完成
        await self.page.wait_for_url("**/dashboard", timeout=10000)
        await self.page.wait_for_load_state('networkidle')
        
        print("✅ 商户管理员登录成功")

    async def logout(self):
        """退出登录"""
        try:
            # 点击用户头像或退出按钮
            await self.page.click('.user-avatar, .logout-btn, button:has-text("退出")')
            await self.page.wait_for_timeout(1000)
            
            # 确认退出
            confirm_btn = self.page.locator('button:has-text("确定"), button:has-text("退出")')
            if await confirm_btn.count() > 0:
                await confirm_btn.click()
            
            # 等待跳转到登录页
            await self.page.wait_for_url("**/login", timeout=5000)
            print("✅ 退出登录成功")
            
        except Exception as e:
            print(f"⚠️ 退出登录失败: {e}")

    async def wait_for_element(self, selector: str, timeout: int = 10000):
        """等待元素出现"""
        try:
            await self.page.wait_for_selector(selector, timeout=timeout)
            return True
        except Exception:
            return False

    async def wait_for_text(self, text: str, timeout: int = 10000):
        """等待文本出现"""
        try:
            await self.page.wait_for_selector(f'text="{text}"', timeout=timeout)
            return True
        except Exception:
            return False

    async def take_screenshot(self, name: str = None):
        """截图"""
        if not name:
            from datetime import datetime
            name = f"screenshot_{datetime.now().strftime('%Y%m%d_%H%M%S')}.png"
        
        screenshot_path = f"test/screenshots/{name}"
        await self.page.screenshot(path=screenshot_path)
        print(f"📸 截图已保存: {screenshot_path}")
        return screenshot_path

    async def fill_form_field(self, selector: str, value: str):
        """填写表单字段"""
        try:
            await self.page.fill(selector, value)
            return True
        except Exception as e:
            print(f"❌ 填写字段失败 {selector}: {e}")
            return False

    async def click_element(self, selector: str):
        """点击元素"""
        try:
            await self.page.click(selector)
            return True
        except Exception as e:
            print(f"❌ 点击元素失败 {selector}: {e}")
            return False

    async def select_option(self, selector: str, value: str):
        """选择下拉选项"""
        try:
            await self.page.select_option(selector, value)
            return True
        except Exception as e:
            print(f"❌ 选择选项失败 {selector}: {e}")
            return False

    async def get_element_text(self, selector: str):
        """获取元素文本"""
        try:
            return await self.page.text_content(selector)
        except Exception as e:
            print(f"❌ 获取元素文本失败 {selector}: {e}")
            return None

    async def get_element_value(self, selector: str):
        """获取输入框值"""
        try:
            return await self.page.input_value(selector)
        except Exception as e:
            print(f"❌ 获取输入框值失败 {selector}: {e}")
            return None

    async def is_element_visible(self, selector: str):
        """检查元素是否可见"""
        try:
            return await self.page.is_visible(selector)
        except Exception:
            return False

    async def is_element_enabled(self, selector: str):
        """检查元素是否启用"""
        try:
            return await self.page.is_enabled(selector)
        except Exception:
            return False

    async def wait_for_navigation(self, timeout: int = 10000):
        """等待页面导航完成"""
        try:
            await self.page.wait_for_load_state('networkidle', timeout=timeout)
            return True
        except Exception:
            return False

    async def handle_dialog(self, accept: bool = True, text: str = None):
        """处理对话框"""
        def dialog_handler(dialog):
            if text:
                dialog.accept(text)
            elif accept:
                dialog.accept()
            else:
                dialog.dismiss()
        
        self.page.on("dialog", dialog_handler)

    async def wait_for_request(self, url_pattern: str, timeout: int = 10000):
        """等待特定请求"""
        try:
            async with self.page.expect_request(url_pattern, timeout=timeout) as request_info:
                pass
            return request_info.value
        except Exception:
            return None

    async def wait_for_response(self, url_pattern: str, timeout: int = 10000):
        """等待特定响应"""
        try:
            async with self.page.expect_response(url_pattern, timeout=timeout) as response_info:
                pass
            return response_info.value
        except Exception:
            return None
