[{"test_name": "创建角色_test_merchant_admin", "success": true, "message": "成功创建角色: 测试商户管理员 (ID: 23)", "details": {}, "timestamp": 1750521577.4241374, "datetime": "2025-06-21T23:59:37.424137"}, {"test_name": "创建角色_test_department_admin", "success": true, "message": "成功创建角色: 测试部门管理员 (ID: 24)", "details": {}, "timestamp": 1750521577.4643571, "datetime": "2025-06-21T23:59:37.464357"}, {"test_name": "创建角色_test_normal_user", "success": true, "message": "成功创建角色: 测试普通用户 (ID: 25)", "details": {}, "timestamp": 1750521577.5028596, "datetime": "2025-06-21T23:59:37.502859"}, {"test_name": "分配权限_test_merchant_admin", "success": true, "message": "成功为角色 测试商户管理员 分配 10 个权限", "details": {}, "timestamp": 1750521577.6183982, "datetime": "2025-06-21T23:59:37.618398"}, {"test_name": "分配权限_test_department_admin", "success": true, "message": "成功为角色 测试部门管理员 分配 6 个权限", "details": {}, "timestamp": 1750521577.6745827, "datetime": "2025-06-21T23:59:37.674582"}, {"test_name": "分配权限_test_normal_user", "success": true, "message": "成功为角色 测试普通用户 分配 3 个权限", "details": {}, "timestamp": 1750521577.7351518, "datetime": "2025-06-21T23:59:37.735151"}, {"test_name": "创建商户_test_merchant_a_1750521574", "success": true, "message": "成功创建商户: 测试商户A有限公司_1750521574 (ID: 7)", "details": {}, "timestamp": 1750521577.8189957, "datetime": "2025-06-21T23:59:37.818995"}, {"test_name": "创建商户_test_merchant_b_1750521574", "success": true, "message": "成功创建商户: 测试商户B有限公司_1750521574 (ID: 8)", "details": {}, "timestamp": 1750521577.8865979, "datetime": "2025-06-21T23:59:37.886597"}, {"test_name": "创建部门_tech_dept_a_1750521574", "success": true, "message": "成功创建部门: 测试商户A-技术部_1750521574 (ID: 11)", "details": {}, "timestamp": 1750521577.973206, "datetime": "2025-06-21T23:59:37.973206"}, {"test_name": "创建部门_ops_dept_a_1750521574", "success": true, "message": "成功创建部门: 测试商户A-运营部_1750521574 (ID: 12)", "details": {}, "timestamp": 1750521578.036536, "datetime": "2025-06-21T23:59:38.036536"}, {"test_name": "创建部门_tech_dept_b_1750521574", "success": true, "message": "成功创建部门: 测试商户B-技术部_1750521574 (ID: 13)", "details": {}, "timestamp": 1750521578.1047676, "datetime": "2025-06-21T23:59:38.104767"}, {"test_name": "创建用户_test_merchant_a_admin_1750521574", "success": true, "message": "成功创建用户: test_merchant_a_admin_1750521574 (ID: 7)", "details": {}, "timestamp": 1750521578.4733706, "datetime": "2025-06-21T23:59:38.473370"}, {"test_name": "创建用户_test_merchant_b_admin_1750521574", "success": true, "message": "成功创建用户: test_merchant_b_admin_1750521574 (ID: 8)", "details": {}, "timestamp": 1750521578.8363378, "datetime": "2025-06-21T23:59:38.836337"}, {"test_name": "创建用户_test_dept_a_admin_1750521574", "success": true, "message": "成功创建用户: test_dept_a_admin_1750521574 (ID: 9)", "details": {}, "timestamp": 1750521579.1821358, "datetime": "2025-06-21T23:59:39.182135"}, {"test_name": "创建用户_test_normal_user_a_1750521574", "success": true, "message": "成功创建用户: test_normal_user_a_1750521574 (ID: 10)", "details": {}, "timestamp": 1750521579.5524077, "datetime": "2025-06-21T23:59:39.552407"}, {"test_name": "登录验证_test_merchant_a_admin_1750521574", "success": true, "message": "用户 test_merchant_a_admin_1750521574 登录成功，获取用户信息正常", "details": {}, "timestamp": 1750521579.855578, "datetime": "2025-06-21T23:59:39.855577"}, {"test_name": "登录验证_test_merchant_b_admin_1750521574", "success": true, "message": "用户 test_merchant_b_admin_1750521574 登录成功，获取用户信息正常", "details": {}, "timestamp": 1750521580.1545818, "datetime": "2025-06-21T23:59:40.154581"}, {"test_name": "登录验证_test_dept_a_admin_1750521574", "success": true, "message": "用户 test_dept_a_admin_1750521574 登录成功，获取用户信息正常", "details": {}, "timestamp": 1750521580.4599411, "datetime": "2025-06-21T23:59:40.459941"}, {"test_name": "登录验证_test_normal_user_a_1750521574", "success": true, "message": "用户 test_normal_user_a_1750521574 登录成功，获取用户信息正常", "details": {}, "timestamp": 1750521580.7714798, "datetime": "2025-06-21T23:59:40.771479"}, {"test_name": "API权限_test_merchant_a_admin_1750521574_用户列表", "success": false, "message": "用户 test_merchant_a_admin_1750521574 被拒绝访问 用户列表，但应该有权限", "details": {}, "timestamp": 1750521580.8104696, "datetime": "2025-06-21T23:59:40.810469"}, {"test_name": "API权限_test_merchant_a_admin_1750521574_部门列表", "success": false, "message": "用户 test_merchant_a_admin_1750521574 被拒绝访问 部门列表，但应该有权限", "details": {}, "timestamp": 1750521580.8459089, "datetime": "2025-06-21T23:59:40.845909"}, {"test_name": "API权限_test_merchant_a_admin_1750521574_角色列表", "success": false, "message": "用户 test_merchant_a_admin_1750521574 被拒绝访问 角色列表，但应该有权限", "details": {}, "timestamp": 1750521580.884284, "datetime": "2025-06-21T23:59:40.884283"}, {"test_name": "API权限_test_merchant_a_admin_1750521574_用户菜单", "success": false, "message": "用户 test_merchant_a_admin_1750521574 被拒绝访问 用户菜单，但应该有权限", "details": {}, "timestamp": 1750521580.9090366, "datetime": "2025-06-21T23:59:40.909036"}, {"test_name": "API权限拒绝_test_merchant_a_admin_1750521574_商户列表", "success": true, "message": "用户 test_merchant_a_admin_1750521574 正确被拒绝访问 商户列表", "details": {}, "timestamp": 1750521580.9220974, "datetime": "2025-06-21T23:59:40.922097"}, {"test_name": "API权限拒绝_test_merchant_a_admin_1750521574_权限列表", "success": true, "message": "用户 test_merchant_a_admin_1750521574 正确被拒绝访问 权限列表", "details": {}, "timestamp": 1750521580.943097, "datetime": "2025-06-21T23:59:40.943097"}, {"test_name": "API权限_test_merchant_b_admin_1750521574_用户列表", "success": false, "message": "用户 test_merchant_b_admin_1750521574 被拒绝访问 用户列表，但应该有权限", "details": {}, "timestamp": 1750521580.977607, "datetime": "2025-06-21T23:59:40.977606"}, {"test_name": "API权限_test_merchant_b_admin_1750521574_部门列表", "success": false, "message": "用户 test_merchant_b_admin_1750521574 被拒绝访问 部门列表，但应该有权限", "details": {}, "timestamp": 1750521581.019022, "datetime": "2025-06-21T23:59:41.019022"}, {"test_name": "API权限_test_merchant_b_admin_1750521574_角色列表", "success": false, "message": "用户 test_merchant_b_admin_1750521574 被拒绝访问 角色列表，但应该有权限", "details": {}, "timestamp": 1750521581.056072, "datetime": "2025-06-21T23:59:41.056072"}, {"test_name": "API权限_test_merchant_b_admin_1750521574_用户菜单", "success": false, "message": "用户 test_merchant_b_admin_1750521574 被拒绝访问 用户菜单，但应该有权限", "details": {}, "timestamp": 1750521581.0857508, "datetime": "2025-06-21T23:59:41.085750"}, {"test_name": "API权限拒绝_test_merchant_b_admin_1750521574_商户列表", "success": true, "message": "用户 test_merchant_b_admin_1750521574 正确被拒绝访问 商户列表", "details": {}, "timestamp": 1750521581.1057951, "datetime": "2025-06-21T23:59:41.105795"}, {"test_name": "API权限拒绝_test_merchant_b_admin_1750521574_权限列表", "success": true, "message": "用户 test_merchant_b_admin_1750521574 正确被拒绝访问 权限列表", "details": {}, "timestamp": 1750521581.1220994, "datetime": "2025-06-21T23:59:41.122099"}, {"test_name": "API权限_test_dept_a_admin_1750521574_用户列表", "success": false, "message": "用户 test_dept_a_admin_1750521574 被拒绝访问 用户列表，但应该有权限", "details": {}, "timestamp": 1750521581.156172, "datetime": "2025-06-21T23:59:41.156172"}, {"test_name": "API权限_test_dept_a_admin_1750521574_用户菜单", "success": false, "message": "用户 test_dept_a_admin_1750521574 被拒绝访问 用户菜单，但应该有权限", "details": {}, "timestamp": 1750521581.1838078, "datetime": "2025-06-21T23:59:41.183807"}, {"test_name": "API权限拒绝_test_dept_a_admin_1750521574_部门列表", "success": true, "message": "用户 test_dept_a_admin_1750521574 正确被拒绝访问 部门列表", "details": {}, "timestamp": 1750521581.2005258, "datetime": "2025-06-21T23:59:41.200525"}, {"test_name": "API权限拒绝_test_dept_a_admin_1750521574_角色列表", "success": true, "message": "用户 test_dept_a_admin_1750521574 正确被拒绝访问 角色列表", "details": {}, "timestamp": 1750521581.2161307, "datetime": "2025-06-21T23:59:41.216130"}, {"test_name": "API权限拒绝_test_dept_a_admin_1750521574_商户列表", "success": true, "message": "用户 test_dept_a_admin_1750521574 正确被拒绝访问 商户列表", "details": {}, "timestamp": 1750521581.233073, "datetime": "2025-06-21T23:59:41.233072"}, {"test_name": "API权限_test_normal_user_a_1750521574_用户菜单", "success": false, "message": "用户 test_normal_user_a_1750521574 被拒绝访问 用户菜单，但应该有权限", "details": {}, "timestamp": 1750521581.2548954, "datetime": "2025-06-21T23:59:41.254895"}, {"test_name": "API权限拒绝_test_normal_user_a_1750521574_用户列表", "success": true, "message": "用户 test_normal_user_a_1750521574 正确被拒绝访问 用户列表", "details": {}, "timestamp": 1750521581.2741423, "datetime": "2025-06-21T23:59:41.274142"}, {"test_name": "API权限拒绝_test_normal_user_a_1750521574_部门列表", "success": true, "message": "用户 test_normal_user_a_1750521574 正确被拒绝访问 部门列表", "details": {}, "timestamp": 1750521581.2882118, "datetime": "2025-06-21T23:59:41.288211"}, {"test_name": "API权限拒绝_test_normal_user_a_1750521574_角色列表", "success": true, "message": "用户 test_normal_user_a_1750521574 正确被拒绝访问 角色列表", "details": {}, "timestamp": 1750521581.3081434, "datetime": "2025-06-21T23:59:41.308143"}, {"test_name": "用户数据隔离_test_merchant_a_admin_1750521574", "success": true, "message": "用户 test_merchant_a_admin_1750521574 正确被拒绝访问用户列表", "details": {}, "timestamp": 1750521581.3473961, "datetime": "2025-06-21T23:59:41.347396"}, {"test_name": "部门数据隔离_test_merchant_a_admin_1750521574", "success": true, "message": "用户 test_merchant_a_admin_1750521574 正确被拒绝访问部门列表", "details": {}, "timestamp": 1750521581.3853025, "datetime": "2025-06-21T23:59:41.385302"}, {"test_name": "用户数据隔离_test_dept_a_admin_1750521574", "success": true, "message": "用户 test_dept_a_admin_1750521574 正确被拒绝访问用户列表", "details": {}, "timestamp": 1750521581.4208658, "datetime": "2025-06-21T23:59:41.420865"}, {"test_name": "部门数据隔离_test_dept_a_admin_1750521574", "success": true, "message": "用户 test_dept_a_admin_1750521574 正确被拒绝访问部门列表", "details": {}, "timestamp": 1750521581.4391637, "datetime": "2025-06-21T23:59:41.439163"}, {"test_name": "用户数据隔离_test_normal_user_a_1750521574", "success": true, "message": "用户 test_normal_user_a_1750521574 正确被拒绝访问用户列表", "details": {}, "timestamp": 1750521581.4530041, "datetime": "2025-06-21T23:59:41.453004"}, {"test_name": "部门数据隔离_test_normal_user_a_1750521574", "success": true, "message": "用户 test_normal_user_a_1750521574 正确被拒绝访问部门列表", "details": {}, "timestamp": 1750521581.4701629, "datetime": "2025-06-21T23:59:41.470163"}]