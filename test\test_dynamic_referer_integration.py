"""
动态Referer配置功能集成测试

测试整个功能的端到端流程：
1. 数据库配置更新
2. 缓存同步
3. API调用使用动态Referer
4. 前端API接口测试
"""

import pytest
import asyncio
import json
from unittest.mock import AsyncMock, patch, MagicMock
from fastapi.testclient import TestClient
from sqlalchemy.orm import Session

from app.main import app
from app.core.walmart_api import WalmartAPI
from app.services.walmart_server import WalmartServerService
from app.models.walmart_server import WalmartServer


class TestDynamicRefererIntegration:
    """动态Referer配置集成测试类"""

    @pytest.fixture
    def client(self):
        """测试客户端"""
        return TestClient(app)

    @pytest.fixture
    def mock_db_session(self):
        """模拟数据库会话"""
        return MagicMock(spec=Session)

    @pytest.fixture
    def mock_walmart_server_config(self):
        """模拟沃尔玛服务器配置"""
        config = MagicMock(spec=WalmartServer)
        config.id = 1
        config.api_url = "https://apicard.swiftpass.cn/app/card/mem/bind.json"
        config.referer = "https://servicewechat.com/wx81d3e1fe4c2e11b4/175/page-frame.html"
        config.is_active = True
        return config

    @pytest.mark.asyncio
    async def test_end_to_end_referer_update_flow(self, mock_db_session, mock_walmart_server_config):
        """测试端到端的Referer更新流程"""
        
        # 1. 创建服务实例
        service = WalmartServerService(mock_db_session)
        
        # 2. 模拟数据库查询返回配置
        mock_db_session.query.return_value.filter.return_value.first.return_value = mock_walmart_server_config
        
        with patch('app.core.redis.get_redis') as mock_get_redis:
            # 3. 模拟Redis客户端
            mock_redis = AsyncMock()
            mock_get_redis.return_value = mock_redis
            
            # 4. 模拟Redis中没有缓存，需要从数据库获取
            mock_redis.get.return_value = None
            
            # 5. 获取Referer配置
            referer = await service.get_referer()
            
            # 6. 验证返回正确的Referer
            assert referer == "https://servicewechat.com/wx81d3e1fe4c2e11b4/175/page-frame.html"
            
            # 7. 验证Redis缓存被设置
            mock_redis.set.assert_called_once_with(
                "walmart:referer", 
                "https://servicewechat.com/wx81d3e1fe4c2e11b4/175/page-frame.html", 
                ex=86400
            )

    @pytest.mark.asyncio
    async def test_walmart_api_uses_dynamic_referer_in_real_request(self):
        """测试WalmartAPI在真实请求中使用动态Referer"""
        
        # 创建API实例
        api = WalmartAPI(
            sign="test_sign@test_key",
            secret_key="test_secret",
            api_version="29"
        )
        
        with patch('app.core.redis.get_redis') as mock_get_redis:
            # 模拟Redis返回动态Referer
            mock_redis = AsyncMock()
            mock_redis.get.return_value = "https://servicewechat.com/wx81d3e1fe4c2e11b4/175/page-frame.html"
            mock_get_redis.return_value = mock_redis
            
            with patch('curl_cffi.requests.post') as mock_post:
                # 模拟HTTP响应
                mock_response = MagicMock()
                mock_response.json.return_value = {
                    "status": True,
                    "data": {"message": "绑卡成功"}
                }
                mock_post.return_value = mock_response
                
                # 调用异步绑卡方法
                response = await api.bind_card_async("1234567890", "password123")
                
                # 验证请求被发送
                mock_post.assert_called_once()
                
                # 获取请求参数
                call_args = mock_post.call_args
                headers = call_args[1]['headers']
                
                # 验证使用了动态Referer
                assert headers['Referer'] == "https://servicewechat.com/wx81d3e1fe4c2e11b4/175/page-frame.html"
                
                # 验证其他必要的头部信息
                assert headers['Host'] == "apicard.swiftpass.cn"
                assert headers['Content-Type'] == "application/json"
                assert 'timestamp' in headers
                assert 'signature' in headers
                assert 'nonce' in headers

    def test_api_endpoint_get_walmart_config_includes_referer(self, client):
        """测试API端点返回包含Referer的配置"""
        
        with patch('app.api.deps.get_current_active_superuser') as mock_auth:
            # 模拟超级管理员用户
            mock_user = MagicMock()
            mock_user.id = 1
            mock_user.username = "admin"
            mock_auth.return_value = mock_user
            
            with patch('app.api.v1.endpoints.walmart_server.WalmartServerService') as mock_service_class:
                # 模拟服务实例
                mock_service = AsyncMock()
                mock_service_class.return_value = mock_service
                
                # 模拟服务方法返回值
                mock_service.get_api_url.return_value = "https://apicard.swiftpass.cn/app/card/mem/bind.json"
                mock_service.get_referer.return_value = "https://servicewechat.com/wx81d3e1fe4c2e11b4/175/page-frame.html"
                
                # 发送GET请求
                response = client.get(
                    "/api/v1/walmart-server",
                    headers={"Authorization": "Bearer test_token"}
                )
                
                # 验证响应
                assert response.status_code == 200
                data = response.json()
                
                # 验证响应包含apiUrl和referer
                assert "apiUrl" in data
                assert "referer" in data
                assert data["apiUrl"] == "https://apicard.swiftpass.cn/app/card/mem/bind.json"
                assert data["referer"] == "https://servicewechat.com/wx81d3e1fe4c2e11b4/175/page-frame.html"

    def test_api_endpoint_update_walmart_config_with_referer(self, client):
        """测试API端点更新包含Referer的配置"""
        
        with patch('app.api.deps.get_current_active_superuser') as mock_auth:
            # 模拟超级管理员用户
            mock_user = MagicMock()
            mock_user.id = 1
            mock_user.username = "admin"
            mock_auth.return_value = mock_user
            
            with patch('app.api.v1.endpoints.walmart_server.WalmartServerService') as mock_service_class:
                # 模拟服务实例
                mock_service = AsyncMock()
                mock_service_class.return_value = mock_service
                
                # 模拟现有配置
                mock_config = MagicMock()
                mock_config.id = 1
                mock_config.api_url = "https://updated-api.com"
                mock_config.referer = "https://updated-referer.com"
                mock_service.get_active_config.return_value = mock_config
                mock_service.update_server_config.return_value = mock_config
                
                # 准备更新数据
                update_data = {
                    "apiUrl": "https://updated-api.com",
                    "referer": "https://updated-referer.com"
                }
                
                # 发送PUT请求
                response = client.put(
                    "/api/v1/walmart-server",
                    json=update_data,
                    headers={"Authorization": "Bearer test_token"}
                )
                
                # 验证响应
                assert response.status_code == 200
                data = response.json()
                
                # 验证响应包含更新后的配置
                assert data["apiUrl"] == "https://updated-api.com"
                assert data["referer"] == "https://updated-referer.com"
                
                # 验证服务方法被正确调用
                mock_service.update_server_config.assert_called_once()

    @pytest.mark.asyncio
    async def test_cache_invalidation_and_refresh(self, mock_db_session, mock_walmart_server_config):
        """测试缓存失效和刷新机制"""
        
        service = WalmartServerService(mock_db_session)
        
        # 模拟数据库查询
        mock_db_session.query.return_value.filter.return_value.first.return_value = mock_walmart_server_config
        
        with patch('app.core.redis.get_redis') as mock_get_redis:
            mock_redis = AsyncMock()
            mock_get_redis.return_value = mock_redis
            
            # 1. 测试缓存刷新
            await service.refresh_redis_cache()
            
            # 验证缓存被删除
            expected_calls = [
                mock_redis.delete("walmart:api_url"),
                mock_redis.delete("walmart:referer")
            ]
            mock_redis.delete.assert_has_calls(expected_calls, any_order=True)
            
            # 验证新配置被缓存
            mock_redis.set.assert_any_call(
                "walmart:api_url", 
                mock_walmart_server_config.api_url, 
                ex=86400
            )
            mock_redis.set.assert_any_call(
                "walmart:referer", 
                mock_walmart_server_config.referer, 
                ex=86400
            )

    @pytest.mark.asyncio
    async def test_fallback_mechanism_when_redis_fails(self, mock_db_session, mock_walmart_server_config):
        """测试Redis失败时的fallback机制"""
        
        service = WalmartServerService(mock_db_session)
        
        # 模拟数据库查询
        mock_db_session.query.return_value.filter.return_value.first.return_value = mock_walmart_server_config
        
        with patch('app.core.redis.get_redis') as mock_get_redis:
            # 模拟Redis连接失败
            mock_get_redis.side_effect = Exception("Redis connection failed")
            
            # 测试WalmartAPI的fallback
            referer = await WalmartAPI.get_referer_from_config()
            
            # 应该返回默认值
            expected_default = "https://servicewechat.com/wx81d3e1fe4c2e11b4/168/page-frame.html"
            assert referer == expected_default

    def test_database_migration_compatibility(self):
        """测试数据库迁移兼容性"""
        # 这个测试验证新的referer字段不会破坏现有的数据结构
        
        # 模拟旧的配置数据（没有referer字段）
        old_config_data = {
            'api_url': 'https://apicard.swiftpass.cn/app/card/mem/bind.json',
            'timeout': 30,
            'retry_count': 3,
            'daily_bind_limit': 1000,
            'api_rate_limit': 60,
            'max_retry_times': 3,
            'bind_timeout_seconds': 30,
            'verification_code_expires': 300,
            'log_retention_days': 90,
            'enable_ip_whitelist': True,
            'enable_security_audit': True,
            'maintenance_mode': False,
            'is_active': True,
        }
        
        # 验证可以创建配置（使用默认referer）
        from app.schemas.walmart_server import WalmartServerCreate
        
        config = WalmartServerCreate(**old_config_data)
        
        # 验证默认referer被设置
        expected_default = "https://servicewechat.com/wx81d3e1fe4c2e11b4/168/page-frame.html"
        assert config.referer == expected_default


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
