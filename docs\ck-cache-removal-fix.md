# Go绑卡模块CK缓存移除修复

## 🚨 **严重业务逻辑问题**

用户发现了一个**关键的业务逻辑缺陷**：**CK使用了缓存机制，导致用户添加新CK后无法立即使用**。

### **问题场景**
```
1. 用户添加了10个新的CK
2. 立即有绑卡请求进来
3. 由于缓存机制，系统获取不到刚添加的CK
4. 返回"商户没有可用CK"错误
5. 但实际上数据库中是有CK的！
```

**用户原话**：
> "CK的千万不能用缓存，比如用户添加了10个CK以后请求马上进来了，这个时候一定要有CK可以使用，因为用户是先添加的CK"

## 🔍 **发现的缓存问题**

### **1. CKWeightManager - 部门权重缓存**
```go
// 问题代码：权重数据被缓存5分钟
type CKWeightManager struct {
    weightCache     map[uint][]*DepartmentWeight // 缓存部门权重
    cacheExpiry     time.Duration                // 5分钟缓存
    lastCacheUpdate map[uint]time.Time
}

// 问题方法：优先使用缓存
func (m *CKWeightManager) getDepartmentWeights(ctx context.Context, merchantID uint) ([]*DepartmentWeight, error) {
    // 检查缓存是否有效
    if exists && hasUpdate && time.Since(lastUpdate) < m.cacheExpiry {
        return cached, nil  // ❌ 返回缓存数据，新CK不可见
    }
}
```

**影响**：新添加的CK不会立即参与部门权重计算，导致"没有可用CK"错误。

### **2. CKStatusSyncService - CK状态缓存**
```go
// 问题代码：CK状态被缓存
type CKStatusSyncService struct {
    statusCache map[uint]*CKStatus  // 缓存CK状态
    config struct {
        CacheExpiry time.Duration   // 缓存过期时间
    }
}

// 问题方法：优先使用缓存
func (s *CKStatusSyncService) GetCKStatus(ctx context.Context, ckID uint) (*CKStatus, error) {
    if status, exists := s.statusCache[ckID]; exists {
        if time.Since(status.LastUpdated) < s.config.CacheExpiry {
            return status, nil  // ❌ 返回缓存状态，新CK状态不可见
        }
    }
}
```

**影响**：新添加的CK状态不会立即可见，影响CK可用性判断。

## 🛠️ **修复方案**

### **核心原则**
- **实时性优先**：CK数据必须实时查询，不能使用缓存
- **数据一致性**：确保用户添加CK后立即可用
- **业务逻辑正确**：避免因缓存导致的"假性无CK"错误

### **修复1：移除部门权重缓存**

#### **修复前（有缓存）**
```go
func (m *CKWeightManager) getDepartmentWeights(ctx context.Context, merchantID uint) ([]*DepartmentWeight, error) {
    // 先尝试从缓存获取
    m.cacheMutex.RLock()
    cached, exists := m.weightCache[merchantID]
    lastUpdate, hasUpdate := m.lastCacheUpdate[merchantID]
    m.cacheMutex.RUnlock()
    
    // 检查缓存是否有效
    if exists && hasUpdate && time.Since(lastUpdate) < m.cacheExpiry {
        m.logger.Debug("使用缓存的权重数据", zap.Uint("merchant_id", merchantID))
        return cached, nil  // ❌ 使用缓存，新CK不可见
    }
    
    return m.refreshDepartmentWeights(ctx, merchantID)
}
```

#### **修复后（无缓存）**
```go
func (m *CKWeightManager) getDepartmentWeights(ctx context.Context, merchantID uint) ([]*DepartmentWeight, error) {
    // 🔧 关键修复：移除缓存，直接从数据库实时查询
    // 确保用户添加CK后立即可用
    m.logger.Debug("实时查询权重数据（确保CK数据最新）", zap.Uint("merchant_id", merchantID))
    return m.refreshDepartmentWeights(ctx, merchantID)  // ✅ 直接实时查询
}
```

### **修复2：移除CK状态缓存**

#### **修复前（有缓存）**
```go
func (s *CKStatusSyncService) GetCKStatus(ctx context.Context, ckID uint) (*CKStatus, error) {
    // 先从缓存获取
    s.cacheMutex.RLock()
    if status, exists := s.statusCache[ckID]; exists {
        if time.Since(status.LastUpdated) < s.config.CacheExpiry {
            s.cacheMutex.RUnlock()
            return status, nil  // ❌ 使用缓存，新CK状态不可见
        }
    }
    s.cacheMutex.RUnlock()
    
    return s.refreshCKStatus(ctx, ckID)
}
```

#### **修复后（无缓存）**
```go
func (s *CKStatusSyncService) GetCKStatus(ctx context.Context, ckID uint) (*CKStatus, error) {
    // 🔧 关键修复：移除缓存，直接从数据库实时查询
    // 确保用户添加CK后状态立即可见
    return s.refreshCKStatus(ctx, ckID)  // ✅ 直接实时查询
}
```

### **修复3：移除缓存更新逻辑**

#### **部门权重刷新方法**
```go
// 修复前：更新缓存
m.cacheMutex.Lock()
m.weightCache[merchantID] = weights
m.lastCacheUpdate[merchantID] = time.Now()
m.cacheMutex.Unlock()

// 修复后：移除缓存更新
// 🔧 关键修复：移除缓存更新逻辑
// 确保每次都从数据库获取最新的CK数据
```

#### **CK状态刷新方法**
```go
// 修复前：更新缓存
s.cacheMutex.Lock()
s.statusCache[ckID] = status
s.cacheMutex.Unlock()

// 修复后：移除缓存更新
// 🔧 关键修复：移除缓存更新逻辑
// 直接返回实时查询的状态，确保数据最新
```

## ✅ **修复效果**

### **修复前（缓存导致问题）**
```
用户添加10个CK → 立即绑卡请求 → 缓存中无新CK → "商户没有可用CK" ❌
```

### **修复后（实时查询）**
```
用户添加10个CK → 立即绑卡请求 → 实时查询数据库 → 获取到新CK → 绑卡成功 ✅
```

### **预期日志变化**
```json
// 修复前
{"msg":"使用缓存的权重数据","merchant_id":4}

// 修复后  
{"msg":"实时查询权重数据（确保CK数据最新）","merchant_id":4}
{"msg":"权重数据实时查询完成（无缓存，确保CK数据最新）","departments_with_ck":10}
```

## 🎯 **业务价值**

### **解决的关键问题**
- ✅ **数据实时性**：用户添加CK后立即可用
- ✅ **业务逻辑正确**：避免"假性无CK"错误
- ✅ **用户体验**：消除添加CK后需要等待的问题
- ✅ **系统可靠性**：确保CK资源的实时可用性

### **性能考虑**
- **查询频率**：每次绑卡请求都会实时查询数据库
- **性能影响**：轻微增加数据库查询负载
- **权衡结果**：**业务正确性 > 性能优化**

## 📁 **修复文件**

- `internal/services/ck_weight_manager.go` - 移除部门权重缓存
- `internal/services/ck_status_sync_service.go` - 移除CK状态缓存
- `docs/ck-cache-removal-fix.md` - 本修复文档

## 🚀 **验证建议**

1. **实时性测试**：
   - 添加新CK
   - 立即发起绑卡请求
   - 验证新CK能被立即使用

2. **日志验证**：
   - 确认日志显示"实时查询"而不是"使用缓存"
   - 验证CK数量统计包含新添加的CK

3. **性能监控**：
   - 监控数据库查询频率变化
   - 确保性能在可接受范围内

## 🔄 **设计原则**

### **CK数据处理原则**
1. **实时性第一**：CK相关数据必须实时查询
2. **缓存谨慎使用**：只有不影响业务逻辑的数据才能缓存
3. **用户体验优先**：确保用户操作的即时生效
4. **数据一致性**：避免缓存导致的数据不一致

### **可以缓存的数据**
- ✅ 系统配置信息（很少变更）
- ✅ 静态参考数据（如地区代码）
- ✅ 统计报表数据（允许延迟）

### **不能缓存的数据**
- ❌ CK列表和状态
- ❌ 部门CK分配信息
- ❌ CK可用性状态
- ❌ 任何影响绑卡业务的实时数据

这个修复确保了Go绑卡模块的CK数据实时性，解决了用户添加CK后无法立即使用的严重业务问题！
