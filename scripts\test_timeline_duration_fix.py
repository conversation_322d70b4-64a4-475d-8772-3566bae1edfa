#!/usr/bin/env python3
"""
测试时间线总处理时间修复

验证timeline API返回的数据是否包含正确的total_duration字段
"""

import sys
import os

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

try:
    from sqlalchemy.orm import Session
    from app.database import SessionLocal
    from app.models.card_record import CardRecord
    from app.services.binding_timeline_service import BindingTimelineService
    print("✅ 模块导入成功")
except ImportError as e:
    print(f"❌ 模块导入失败: {e}")
    print(f"项目根目录: {project_root}")
    print(f"Python路径: {sys.path}")
    sys.exit(1)


def test_timeline_duration_fix():
    """测试时间线总处理时间修复"""
    print("🔧 测试时间线总处理时间修复")
    print("=" * 50)
    
    db = SessionLocal()
    try:
        # 查询一个已完成的绑卡记录
        record = db.query(CardRecord).filter(
            CardRecord.status.in_(['success', 'failed'])
        ).order_by(CardRecord.created_at.desc()).first()
        
        if not record:
            print("❌ 没有找到已完成的绑卡记录")
            return
        
        print(f"📝 测试记录: {str(record.id)[:8]}...")
        print(f"📊 记录状态: {record.status}")
        print(f"📊 记录处理耗时: {record.process_time}秒")
        print()
        
        # 创建时间线服务
        timeline_service = BindingTimelineService(db)
        
        try:
            # 获取时间线
            timeline = timeline_service.get_binding_timeline(str(record.id))
            
            print("✅ 时间线API调用成功")
            print()
            
            # 检查返回的字段
            print("📋 返回字段检查:")
            print(f"   total_duration (秒): {timeline.total_duration}")
            print(f"   total_duration_ms (毫秒): {timeline.total_duration_ms}")
            print(f"   total_duration_formatted: {timeline.total_duration_formatted}")
            print()
            
            # 验证数据一致性
            print("🔍 数据一致性验证:")
            
            # 检查秒和毫秒的转换是否正确
            if timeline.total_duration is not None and timeline.total_duration_ms is not None:
                expected_seconds = timeline.total_duration_ms / 1000
                actual_seconds = timeline.total_duration
                
                if abs(expected_seconds - actual_seconds) < 0.001:  # 允许微小的浮点误差
                    print(f"   ✅ 秒/毫秒转换正确: {actual_seconds}秒 = {timeline.total_duration_ms}毫秒")
                else:
                    print(f"   ❌ 秒/毫秒转换错误: {actual_seconds}秒 ≠ {expected_seconds}秒")
            else:
                print(f"   ⚠️  时间字段为空: total_duration={timeline.total_duration}, total_duration_ms={timeline.total_duration_ms}")
            
            # 检查是否解决了显示为0的问题
            if timeline.total_duration is not None and timeline.total_duration > 0:
                print(f"   ✅ 总处理时间不为0: {timeline.total_duration}秒")
            else:
                print(f"   ❌ 总处理时间仍为0或空: {timeline.total_duration}")
            
            # 与记录的process_time对比
            if record.process_time is not None:
                if timeline.total_duration is not None:
                    diff = abs(timeline.total_duration - record.process_time)
                    if diff < 1.0:  # 允许1秒的差异
                        print(f"   ✅ 与记录process_time一致: timeline={timeline.total_duration}秒, record={record.process_time}秒")
                    else:
                        print(f"   ⚠️  与记录process_time有差异: timeline={timeline.total_duration}秒, record={record.process_time}秒, 差异={diff:.2f}秒")
                else:
                    print(f"   ❌ timeline时间为空，但记录process_time={record.process_time}秒")
            
            print()
            print("📊 步骤统计:")
            print(f"   步骤总数: {len(timeline.steps)}")
            print(f"   开始时间: {timeline.start_time}")
            print(f"   结束时间: {timeline.end_time}")
            
            # 显示前3个步骤
            if timeline.steps:
                print(f"   前3个步骤:")
                for i, step in enumerate(timeline.steps[:3], 1):
                    duration_info = f" ({step.duration_formatted})" if step.duration_formatted else ""
                    print(f"      {i}. {step.step_name}{duration_info} - {step.status}")
            
        except Exception as e:
            print(f"❌ 获取时间线失败: {str(e)}")
            import traceback
            traceback.print_exc()
            
    finally:
        db.close()


def test_multiple_records():
    """测试多个记录的时间线"""
    print("\n🔧 测试多个记录的时间线")
    print("=" * 50)
    
    db = SessionLocal()
    try:
        # 查询最近的5个记录
        records = db.query(CardRecord).filter(
            CardRecord.status.in_(['success', 'failed'])
        ).order_by(CardRecord.created_at.desc()).limit(5).all()
        
        if not records:
            print("❌ 没有找到已完成的绑卡记录")
            return
        
        timeline_service = BindingTimelineService(db)
        
        print(f"📝 测试 {len(records)} 个记录:")
        print()
        
        success_count = 0
        zero_duration_count = 0
        
        for i, record in enumerate(records, 1):
            try:
                timeline = timeline_service.get_binding_timeline(str(record.id))
                
                status = "✅" if timeline.total_duration and timeline.total_duration > 0 else "❌"
                duration_display = f"{timeline.total_duration:.2f}秒" if timeline.total_duration else "0或空"
                
                print(f"   {i}. {str(record.id)[:8]}... | {status} | {duration_display} | {record.status}")
                
                if timeline.total_duration and timeline.total_duration > 0:
                    success_count += 1
                else:
                    zero_duration_count += 1
                    
            except Exception as e:
                print(f"   {i}. {str(record.id)[:8]}... | ❌ | 错误: {str(e)} | {record.status}")
        
        print()
        print(f"📊 测试结果统计:")
        print(f"   成功显示时间: {success_count}/{len(records)} ({success_count/len(records)*100:.1f}%)")
        print(f"   时间为0或空: {zero_duration_count}/{len(records)} ({zero_duration_count/len(records)*100:.1f}%)")
        
        if zero_duration_count == 0:
            print("   🎉 所有记录的时间线都正确显示了总处理时间！")
        else:
            print(f"   ⚠️  仍有 {zero_duration_count} 个记录的时间线显示为0")
            
    finally:
        db.close()


if __name__ == "__main__":
    print("🚀 开始测试时间线总处理时间修复")
    print()
    
    # 测试单个记录
    test_timeline_duration_fix()
    
    # 测试多个记录
    test_multiple_records()
    
    print()
    print("✅ 测试完成！")
    print()
    print("💡 如果修复成功，您应该看到:")
    print("   - total_duration字段不为0")
    print("   - total_duration和total_duration_ms转换正确")
    print("   - 与记录的process_time基本一致")
    print()
    print("🔧 前端修复:")
    print("   现在前端可以直接使用 timelineData.total_duration 字段")
    print("   无需再进行毫秒到秒的转换")
