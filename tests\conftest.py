"""
测试配置文件
提供全局测试配置、fixtures和工具函数
"""

import os
import sys
import json
import time
import requests
from typing import Dict, Any, Optional, Tuple
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(__file__)))

# 测试配置
TEST_CONFIG = {
    "base_url": "http://localhost:20000",
    "api_prefix": "/api/v1",
    "timeout": 30,
    "retry_count": 3,
    "retry_delay": 1,
    "encoding": "utf-8"
}

# 测试账号配置
TEST_ACCOUNTS = {
    "super_admin": {
        "username": "admin",
        "password": "7c222fb2927d828af22f592134e8932480637c0d",
        "description": "超级管理员账号"
    },
    "merchant_admin": {
        "username": "test1", 
        "password": "********",
        "description": "商户管理员账号"
    },
    "merchant_admin_b": {
        "username": "test_merchant_b",
        "password": "********", 
        "description": "商户B管理员账号"
    }
}

class TestBase:
    """测试基类，提供通用的测试方法"""
    
    def __init__(self):
        self.base_url = TEST_CONFIG["base_url"]
        self.api_prefix = TEST_CONFIG["api_prefix"]
        self.timeout = TEST_CONFIG["timeout"]
        self.session = requests.Session()
        self.tokens = {}
        
    def make_request(self, method: str, endpoint: str, token: str = None,
                    data: Dict = None, params: Dict = None, form_data: bool = False) -> Tuple[int, Dict]:
        """发送HTTP请求"""
        url = f"{self.base_url}{self.api_prefix}{endpoint}"
        headers = {}

        if token:
            headers["Authorization"] = f"Bearer {token}"

        # 根据是否是表单数据设置不同的Content-Type和数据格式
        if form_data:
            headers["Content-Type"] = "application/x-www-form-urlencoded"
            request_data = data
        else:
            headers["Content-Type"] = "application/json"
            request_data = None

        try:
            if form_data:
                response = self.session.request(
                    method=method,
                    url=url,
                    headers=headers,
                    data=data,
                    params=params,
                    timeout=self.timeout
                )
            else:
                response = self.session.request(
                    method=method,
                    url=url,
                    headers=headers,
                    json=data,
                    params=params,
                    timeout=self.timeout
                )

            try:
                response_data = response.json()
            except:
                response_data = {"message": response.text}

            return response.status_code, response_data

        except Exception as e:
            return 0, {"error": str(e)}
    
    def login(self, username: str, password: str) -> Optional[str]:
        """用户登录并返回token"""
        login_data = {"username": username, "password": password}
        status_code, response = self.make_request("POST", "/auth/login", data=login_data, form_data=True)

        if status_code == 200:
            # 检查响应格式，可能是直接返回token，也可能在data字段中
            token = None
            if "access_token" in response:
                token = response["access_token"]
            elif "data" in response and isinstance(response["data"], dict) and "access_token" in response["data"]:
                token = response["data"]["access_token"]

            if token:
                self.tokens[username] = token
                return token
        return None
    
    def logout(self, token: str) -> bool:
        """用户登出"""
        status_code, _ = self.make_request("POST", "/auth/logout", token)
        return status_code == 200
    
    def get_user_info(self, token: str) -> Tuple[int, Dict]:
        """获取当前用户信息"""
        return self.make_request("GET", "/auth/me", token)

def get_test_config() -> Dict[str, Any]:
    """获取测试配置"""
    return TEST_CONFIG.copy()

def get_test_accounts() -> Dict[str, Dict[str, str]]:
    """获取测试账号配置"""
    return TEST_ACCOUNTS.copy()

def format_test_result(test_name: str, success: bool, message: str = "", 
                      details: Dict = None) -> Dict:
    """格式化测试结果"""
    return {
        "test_name": test_name,
        "success": success,
        "message": message,
        "details": details or {},
        "timestamp": time.time(),
        "datetime": datetime.now().isoformat()
    }

def save_test_report(results: list, filename: str = None) -> str:
    """保存测试报告"""
    if not filename:
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"test_report_{timestamp}.json"
    
    report_path = os.path.join("test", "reports", filename)
    os.makedirs(os.path.dirname(report_path), exist_ok=True)
    
    with open(report_path, 'w', encoding='utf-8') as f:
        json.dump(results, f, ensure_ascii=False, indent=2)
    
    return report_path

def print_test_summary(results: list):
    """打印测试摘要"""
    total = len(results)
    passed = sum(1 for r in results if r.get("success", False))
    failed = total - passed
    success_rate = (passed / total * 100) if total > 0 else 0
    
    print(f"\n{'='*60}")
    print(f"测试摘要")
    print(f"{'='*60}")
    print(f"总测试数: {total}")
    print(f"通过数: {passed}")
    print(f"失败数: {failed}")
    print(f"成功率: {success_rate:.1f}%")
    print(f"{'='*60}")
