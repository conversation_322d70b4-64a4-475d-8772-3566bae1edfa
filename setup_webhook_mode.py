#!/usr/bin/env python3
"""
设置Telegram机器人Webhook模式
避免轮询冲突问题
"""

import asyncio
import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from app.core.logging import get_logger

logger = get_logger(__name__)


async def setup_webhook_mode():
    """设置Webhook模式"""
    logger.info("🌐 设置Telegram机器人Webhook模式")
    logger.info("=" * 50)
    
    # 获取当前配置
    try:
        from app.telegram_bot.config import get_bot_config
        config = get_bot_config()
        
        if not config.bot_token:
            logger.error("❌ Bot token 未配置")
            return False
        
        logger.info(f"当前配置:")
        logger.info(f"  Bot Token: {'已配置' if config.bot_token else '未配置'}")
        logger.info(f"  Webhook URL: {config.webhook_url or '未配置'}")
        logger.info(f"  Webhook Secret: {'已配置' if config.webhook_secret else '未配置'}")
        
    except Exception as e:
        logger.error(f"❌ 获取配置失败: {e}")
        return False
    
    # 如果没有配置webhook URL，提供配置建议
    if not config.webhook_url:
        logger.info("\n📝 Webhook配置建议:")
        logger.info("1. 本地开发环境:")
        logger.info("   - 使用ngrok: https://your-ngrok-url.ngrok.io/api/v1/telegram/webhook")
        logger.info("   - 或使用localtunnel等工具")
        logger.info("2. 生产环境:")
        logger.info("   - 使用域名: https://your-domain.com/api/v1/telegram/webhook")
        logger.info("3. 配置方法:")
        logger.info("   - 在Web界面的Telegram配置页面设置")
        logger.info("   - 或在config/telegram_bot.yaml中配置")
        
        webhook_url = input("\n请输入Webhook URL (留空跳过): ").strip()
        if not webhook_url:
            logger.info("跳过Webhook设置")
            return False
        
        # 验证URL格式
        if not webhook_url.startswith("https://"):
            logger.error("❌ Webhook URL必须使用HTTPS")
            return False
        
        # 这里可以添加更新配置的逻辑
        logger.info(f"✅ 将使用Webhook URL: {webhook_url}")
        
        # 生成随机secret
        import secrets
        webhook_secret = secrets.token_hex(32)
        logger.info("✅ 已生成Webhook Secret")
        
        # 提示用户如何配置
        logger.info("\n📋 请按以下步骤配置:")
        logger.info("1. 在Web界面进入 系统管理 -> Telegram配置")
        logger.info(f"2. 设置Webhook URL: {webhook_url}")
        logger.info(f"3. 设置Webhook Secret: {webhook_secret}")
        logger.info("4. 保存配置")
        logger.info("5. 重启机器人服务")
        
        return True
    
    # 如果已配置webhook，尝试设置
    try:
        from telegram.ext import Application
        
        application = Application.builder().token(config.bot_token).build()
        await application.initialize()
        
        try:
            # 设置webhook
            logger.info(f"正在设置Webhook: {config.webhook_url}")
            
            await application.bot.set_webhook(
                url=config.webhook_url,
                secret_token=config.webhook_secret if config.webhook_secret else None,
                max_connections=100,
                allowed_updates=[
                    "message",
                    "edited_message",
                    "channel_post", 
                    "edited_channel_post",
                    "callback_query",
                    "my_chat_member",
                    "chat_member"
                ]
            )
            
            # 验证设置
            webhook_info = await application.bot.get_webhook_info()
            if webhook_info.url == config.webhook_url:
                logger.info("✅ Webhook设置成功")
                logger.info(f"   URL: {webhook_info.url}")
                logger.info(f"   最大连接数: {webhook_info.max_connections}")
                logger.info(f"   待处理更新: {webhook_info.pending_update_count}")
                
                if webhook_info.last_error_message:
                    logger.warning(f"⚠️  最后错误: {webhook_info.last_error_message}")
                
                return True
            else:
                logger.error("❌ Webhook设置失败")
                return False
                
        finally:
            await application.shutdown()
            
    except Exception as e:
        logger.error(f"❌ 设置Webhook失败: {e}")
        return False


async def test_webhook_endpoint():
    """测试Webhook端点"""
    logger.info("🧪 测试Webhook端点...")
    
    try:
        from app.telegram_bot.config import get_bot_config
        config = get_bot_config()
        
        if not config.webhook_url:
            logger.warning("⚠️  Webhook URL未配置，跳过测试")
            return False
        
        import aiohttp
        import json
        
        # 构造测试数据
        test_data = {
            "update_id": 999999999,
            "message": {
                "message_id": 1,
                "date": 1234567890,
                "chat": {"id": 123456789, "type": "private"},
                "from": {"id": 123456789, "is_bot": False, "first_name": "Test"},
                "text": "/test"
            }
        }
        
        headers = {
            "Content-Type": "application/json",
            "User-Agent": "TelegramBot (like TwitterBot)"
        }
        
        if config.webhook_secret:
            import hmac
            import hashlib
            
            body = json.dumps(test_data, separators=(',', ':'))
            signature = hmac.new(
                config.webhook_secret.encode(),
                body.encode(),
                hashlib.sha256
            ).hexdigest()
            headers["X-Telegram-Bot-Api-Secret-Token"] = f"sha256={signature}"
        
        async with aiohttp.ClientSession() as session:
            async with session.post(
                config.webhook_url,
                json=test_data,
                headers=headers,
                timeout=aiohttp.ClientTimeout(total=10)
            ) as response:
                if response.status == 200:
                    logger.info("✅ Webhook端点测试成功")
                    return True
                else:
                    logger.error(f"❌ Webhook端点测试失败: HTTP {response.status}")
                    response_text = await response.text()
                    logger.error(f"响应: {response_text[:200]}")
                    return False
                    
    except Exception as e:
        logger.error(f"❌ 测试Webhook端点失败: {e}")
        return False


async def main():
    """主函数"""
    logger.info("🌐 Telegram机器人Webhook模式设置工具")
    logger.info("=" * 60)
    
    # 设置webhook
    webhook_success = await setup_webhook_mode()
    
    if webhook_success:
        # 测试端点
        logger.info("\n" + "=" * 50)
        endpoint_success = await test_webhook_endpoint()
        
        logger.info("\n" + "=" * 50)
        logger.info("📋 设置完成")
        
        if endpoint_success:
            logger.info("🎉 Webhook模式设置成功！")
            logger.info("💡 优势:")
            logger.info("  - 不会有轮询冲突问题")
            logger.info("  - 更高效的消息处理")
            logger.info("  - 适合生产环境")
        else:
            logger.warning("⚠️  Webhook设置成功，但端点测试失败")
            logger.info("请检查:")
            logger.info("  - 服务器是否正在运行")
            logger.info("  - URL是否可以从外网访问")
            logger.info("  - 防火墙设置")
    else:
        logger.error("❌ Webhook模式设置失败")
        logger.info("建议:")
        logger.info("1. 检查配置")
        logger.info("2. 使用ngrok等工具暴露本地服务")
        logger.info("3. 或继续使用轮询模式（解决冲突后）")


if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        logger.info("操作被用户中断")
    except Exception as e:
        logger.error(f"程序执行失败: {e}")
        sys.exit(1)
