/**
 * 日期工具
 * 提供日期格式化和处理功能
 */

/**
 * 格式化日期时间
 * @param {Date|string|number} date 日期对象、日期字符串或时间戳
 * @param {string} format 格式化模板，默认为 'YYYY-MM-DD HH:mm:ss'
 * @returns {string} 格式化后的日期字符串
 */
export function formatDateTime(date, format = 'YYYY-MM-DD HH:mm:ss') {
    // 如果未传入日期，使用当前日期
    if (!date) {
        date = new Date();
    }

    // 如果传入的是字符串或数字，转换为日期对象
    if (typeof date === 'string' || typeof date === 'number') {
        // 处理带有时区信息的ISO格式日期字符串
        date = new Date(date);
    }

    // 如果转换失败，返回空字符串
    if (isNaN(date.getTime())) {
        console.error('无效的日期:', date);
        return '';
    }

    // 获取当前时区的年月日时分秒
    const year = date.getFullYear();
    const month = date.getMonth() + 1;
    const day = date.getDate();
    const hours = date.getHours();
    const minutes = date.getMinutes();
    const seconds = date.getSeconds();
    const milliseconds = date.getMilliseconds();

    // 替换格式化模板中的占位符
    return format
        .replace(/YYYY/g, year.toString())
        .replace(/YY/g, year.toString().slice(2))
        .replace(/MM/g, month.toString().padStart(2, '0'))
        .replace(/M/g, month.toString())
        .replace(/DD/g, day.toString().padStart(2, '0'))
        .replace(/D/g, day.toString())
        .replace(/HH/g, hours.toString().padStart(2, '0'))
        .replace(/H/g, hours.toString())
        .replace(/hh/g, (hours % 12 || 12).toString().padStart(2, '0'))
        .replace(/h/g, (hours % 12 || 12).toString())
        .replace(/mm/g, minutes.toString().padStart(2, '0'))
        .replace(/m/g, minutes.toString())
        .replace(/ss/g, seconds.toString().padStart(2, '0'))
        .replace(/s/g, seconds.toString())
        .replace(/SSS/g, milliseconds.toString().padStart(3, '0'))
        .replace(/A/g, hours < 12 ? 'AM' : 'PM')
        .replace(/a/g, hours < 12 ? 'am' : 'pm');
}

/**
 * 获取相对时间描述（如：刚刚、xx分钟前、xx小时前等）
 * @param {Date|string|number} date 日期对象、日期字符串或时间戳
 * @returns {string} 相对时间描述
 */
export function getRelativeTime(date) {
    if (!date) return '';

    // 如果传入的是字符串或数字，转换为日期对象
    if (typeof date === 'string' || typeof date === 'number') {
        date = new Date(date);
    }

    // 如果转换失败，返回空字符串
    if (isNaN(date.getTime())) {
        console.error('无效的日期:', date);
        return '';
    }

    const now = new Date();
    const diff = Math.floor((now - date) / 1000); // 差异秒数

    if (diff < 0) {
        return formatDateTime(date);
    } else if (diff < 60) {
        return '刚刚';
    } else if (diff < 3600) {
        return `${Math.floor(diff / 60)}分钟前`;
    } else if (diff < 86400) {
        return `${Math.floor(diff / 3600)}小时前`;
    } else if (diff < 604800) {
        return `${Math.floor(diff / 86400)}天前`;
    } else if (diff < 2592000) {
        return `${Math.floor(diff / 604800)}周前`;
    } else if (diff < 31536000) {
        return `${Math.floor(diff / 2592000)}个月前`;
    } else {
        return `${Math.floor(diff / 31536000)}年前`;
    }
}

/**
 * 获取两个日期之间的天数
 * @param {Date|string|number} startDate 开始日期
 * @param {Date|string|number} endDate 结束日期
 * @returns {number} 天数差
 */
export function getDaysDiff(startDate, endDate) {
    // 转换为日期对象
    if (typeof startDate === 'string' || typeof startDate === 'number') {
        startDate = new Date(startDate);
    }
    if (typeof endDate === 'string' || typeof endDate === 'number') {
        endDate = new Date(endDate);
    }

    // 如果转换失败，返回0
    if (isNaN(startDate.getTime()) || isNaN(endDate.getTime())) {
        console.error('无效的日期:', startDate, endDate);
        return 0;
    }

    // 重置为当天的0点0分0秒，忽略时间部分
    const start = new Date(startDate.getFullYear(), startDate.getMonth(), startDate.getDate());
    const end = new Date(endDate.getFullYear(), endDate.getMonth(), endDate.getDate());

    // 计算毫秒差并转换为天数
    return Math.round((end - start) / (1000 * 60 * 60 * 24));
}