#!/usr/bin/env python3
"""
诊断CK使用记录问题
深入分析为什么record_ck_usage返回True但数据没有更新
"""

import asyncio
import sys
import os
import time
from sqlalchemy.orm import sessionmaker
from sqlalchemy import text

# 添加项目路径
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.insert(0, project_root)

from app.api.deps import get_db
from app.models.walmart_ck import WalmartCK
from app.services.walmart_ck_service_new import WalmartCKService
from app.db.session import SessionLocal
from app.core.logging import get_logger

logger = get_logger("diagnose_ck_usage")


class CKUsageDiagnoser:
    """CK使用记录问题诊断器"""
    
    def __init__(self):
        self.db = next(get_db())
        
    def __enter__(self):
        return self
        
    def __exit__(self, exc_type, exc_val, exc_tb):
        if self.db:
            self.db.close()
    
    async def diagnose_ck_usage_issue(self, ck_id: int):
        """诊断CK使用记录问题"""
        print(f"🔍 诊断CK {ck_id} 使用记录问题")
        print("="*60)
        
        # 1. 检查CK基本信息
        await self._check_ck_basic_info(ck_id)
        
        # 2. 测试单次调用
        await self._test_single_call(ck_id)
        
        # 3. 测试事务行为
        await self._test_transaction_behavior(ck_id)
        
        # 4. 测试行锁行为
        await self._test_row_lock_behavior(ck_id)
        
        # 5. 测试数据库连接
        await self._test_database_connection(ck_id)
    
    async def _check_ck_basic_info(self, ck_id: int):
        """检查CK基本信息"""
        print(f"\n1. 📊 CK基本信息检查:")
        print("-"*40)
        
        try:
            ck = self.db.query(WalmartCK).filter(WalmartCK.id == ck_id).first()
            if not ck:
                print(f"❌ CK {ck_id} 不存在")
                return
            
            print(f"  CK ID: {ck.id}")
            print(f"  商户ID: {ck.merchant_id}")
            print(f"  bind_count: {ck.bind_count}")
            print(f"  total_limit: {ck.total_limit}")
            print(f"  active: {ck.active}")
            print(f"  is_deleted: {ck.is_deleted}")
            print(f"  last_bind_time: {ck.last_bind_time}")
            
        except Exception as e:
            print(f"❌ 检查CK基本信息失败: {e}")
    
    async def _test_single_call(self, ck_id: int):
        """测试单次调用"""
        print(f"\n2. 🎯 单次调用测试:")
        print("-"*40)
        
        try:
            # 获取初始状态
            initial_ck = self.db.query(WalmartCK).filter(WalmartCK.id == ck_id).first()
            initial_count = initial_ck.bind_count
            
            print(f"  初始bind_count: {initial_count}")
            
            # 执行单次调用
            ck_service = WalmartCKService(self.db)
            result = ck_service.record_ck_usage(ck_id, True)
            
            print(f"  调用结果: {result}")
            
            # 检查最终状态
            self.db.refresh(initial_ck)
            final_count = initial_ck.bind_count
            
            print(f"  最终bind_count: {final_count}")
            print(f"  变化: {final_count - initial_count}")
            
            if result and final_count == initial_count + 1:
                print(f"  ✅ 单次调用正常")
            else:
                print(f"  ❌ 单次调用异常")
                
        except Exception as e:
            print(f"❌ 单次调用测试失败: {e}")
            import traceback
            traceback.print_exc()
    
    async def _test_transaction_behavior(self, ck_id: int):
        """测试事务行为"""
        print(f"\n3. 🔄 事务行为测试:")
        print("-"*40)
        
        try:
            # 创建新的数据库会话
            db_session = SessionLocal()
            
            try:
                # 获取初始状态
                initial_ck = db_session.query(WalmartCK).filter(WalmartCK.id == ck_id).first()
                initial_count = initial_ck.bind_count
                
                print(f"  初始bind_count: {initial_count}")
                
                # 手动执行更新
                initial_ck.bind_count += 1
                print(f"  手动增加bind_count")
                
                # 提交事务
                db_session.commit()
                print(f"  事务已提交")
                
                # 检查结果
                db_session.refresh(initial_ck)
                final_count = initial_ck.bind_count
                
                print(f"  最终bind_count: {final_count}")
                print(f"  变化: {final_count - initial_count}")
                
                if final_count == initial_count + 1:
                    print(f"  ✅ 事务行为正常")
                else:
                    print(f"  ❌ 事务行为异常")
                    
            finally:
                db_session.close()
                
        except Exception as e:
            print(f"❌ 事务行为测试失败: {e}")
            import traceback
            traceback.print_exc()
    
    async def _test_row_lock_behavior(self, ck_id: int):
        """测试行锁行为"""
        print(f"\n4. 🔒 行锁行为测试:")
        print("-"*40)
        
        try:
            # 创建新的数据库会话
            db_session = SessionLocal()
            
            try:
                # 使用行锁获取记录
                print(f"  使用with_for_update()获取记录...")
                ck = db_session.query(WalmartCK).filter(
                    WalmartCK.id == ck_id
                ).with_for_update().first()
                
                if not ck:
                    print(f"  ❌ 无法获取CK记录")
                    return
                
                print(f"  ✅ 成功获取行锁")
                print(f"  当前bind_count: {ck.bind_count}")
                
                # 更新记录
                old_count = ck.bind_count
                ck.bind_count += 1
                
                print(f"  更新bind_count: {old_count} -> {ck.bind_count}")
                
                # 提交事务
                db_session.commit()
                print(f"  事务已提交")
                
                # 验证更新
                db_session.refresh(ck)
                print(f"  验证bind_count: {ck.bind_count}")
                
                if ck.bind_count == old_count + 1:
                    print(f"  ✅ 行锁更新成功")
                else:
                    print(f"  ❌ 行锁更新失败")
                    
            finally:
                db_session.close()
                
        except Exception as e:
            print(f"❌ 行锁行为测试失败: {e}")
            import traceback
            traceback.print_exc()
    
    async def _test_database_connection(self, ck_id: int):
        """测试数据库连接"""
        print(f"\n5. 🔌 数据库连接测试:")
        print("-"*40)
        
        try:
            # 测试原始SQL
            result = self.db.execute(
                text("SELECT bind_count FROM walmart_ck WHERE id = :ck_id"),
                {"ck_id": ck_id}
            ).fetchone()
            
            if result:
                print(f"  原始SQL查询bind_count: {result[0]}")
            else:
                print(f"  ❌ 原始SQL查询失败")
            
            # 测试更新SQL
            initial_count = result[0] if result else 0
            
            update_result = self.db.execute(
                text("UPDATE walmart_ck SET bind_count = bind_count + 1 WHERE id = :ck_id"),
                {"ck_id": ck_id}
            )
            
            print(f"  更新SQL影响行数: {update_result.rowcount}")
            
            # 提交事务
            self.db.commit()
            print(f"  事务已提交")
            
            # 验证更新
            final_result = self.db.execute(
                text("SELECT bind_count FROM walmart_ck WHERE id = :ck_id"),
                {"ck_id": ck_id}
            ).fetchone()
            
            if final_result:
                final_count = final_result[0]
                print(f"  更新后bind_count: {final_count}")
                print(f"  变化: {final_count - initial_count}")
                
                if final_count == initial_count + 1:
                    print(f"  ✅ 原始SQL更新成功")
                else:
                    print(f"  ❌ 原始SQL更新失败")
            
        except Exception as e:
            print(f"❌ 数据库连接测试失败: {e}")
            import traceback
            traceback.print_exc()
    
    async def test_service_method_step_by_step(self, ck_id: int):
        """逐步测试服务方法"""
        print(f"\n🔬 逐步测试服务方法:")
        print("="*60)
        
        try:
            ck_service = WalmartCKService(self.db)
            
            print(f"1. 创建服务实例: ✅")
            
            # 获取CK（不使用行锁）
            ck = self.db.query(WalmartCK).filter(WalmartCK.id == ck_id).first()
            if not ck:
                print(f"2. 获取CK: ❌ CK不存在")
                return
            
            print(f"2. 获取CK: ✅ (bind_count={ck.bind_count})")
            
            # 使用行锁获取CK
            ck_locked = self.db.query(WalmartCK).filter(
                WalmartCK.id == ck_id
            ).with_for_update().first()
            
            print(f"3. 获取行锁: ✅")
            
            # 更新bind_count
            old_count = ck_locked.bind_count
            ck_locked.bind_count += 1
            
            print(f"4. 更新bind_count: {old_count} -> {ck_locked.bind_count}")
            
            # 提交事务
            self.db.commit()
            print(f"5. 提交事务: ✅")
            
            # 验证结果
            self.db.refresh(ck_locked)
            print(f"6. 验证结果: bind_count={ck_locked.bind_count}")
            
            if ck_locked.bind_count == old_count + 1:
                print(f"✅ 手动步骤执行成功")
            else:
                print(f"❌ 手动步骤执行失败")
            
        except Exception as e:
            print(f"❌ 逐步测试失败: {e}")
            import traceback
            traceback.print_exc()


async def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description="CK使用记录问题诊断")
    parser.add_argument("--ck-id", type=int, default=17, help="诊断的CK ID")
    
    args = parser.parse_args()
    
    with CKUsageDiagnoser() as diagnoser:
        await diagnoser.diagnose_ck_usage_issue(args.ck_id)
        await diagnoser.test_service_method_step_by_step(args.ck_id)
    
    print(f"\n🎉 诊断完成！")


if __name__ == "__main__":
    asyncio.run(main())
