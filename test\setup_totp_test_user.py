#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
为测试用户设置TOTP双因子认证

这个脚本将为admin用户启用TOTP，以便测试登录页面的状态切换功能
"""

import requests
import json
import pyotp
import qrcode
from io import BytesIO
import base64

class TOTPTestSetup:
    def __init__(self):
        self.base_url = "http://localhost:20000"
        self.session = requests.Session()
        
    def login_user(self, username, password):
        """登录用户"""
        print(f"🔐 登录用户: {username}")
        
        # 准备登录数据
        login_data = {
            'username': username,
            'password': password
        }
        
        # 发送登录请求
        response = self.session.post(
            f"{self.base_url}/api/v1/auth/login",
            data=login_data,
            headers={'Content-Type': 'application/x-www-form-urlencoded'}
        )
        
        if response.status_code == 200:
            result = response.json()
            if result.get('code') == 0:
                token = result['data']['access_token']
                self.session.headers.update({'Authorization': f'Bearer {token}'})
                print(f"✅ 登录成功")
                return True
            else:
                print(f"❌ 登录失败: {result.get('message')}")
                return False
        else:
            print(f"❌ 登录请求失败: {response.status_code}")
            return False
            
    def check_totp_status(self):
        """检查当前用户的TOTP状态"""
        print("📊 检查TOTP状态...")
        
        response = self.session.get(f"{self.base_url}/api/v1/totp/status")
        
        if response.status_code == 200:
            result = response.json()
            if result.get('code') == 0:
                status = result['data']
                print(f"TOTP启用状态: {status.get('enabled', False)}")
                print(f"是否必需: {status.get('is_required', False)}")
                return status
            else:
                print(f"❌ 获取TOTP状态失败: {result.get('message')}")
                return None
        else:
            print(f"❌ TOTP状态请求失败: {response.status_code}")
            return None
            
    def setup_totp(self):
        """设置TOTP"""
        print("🔧 设置TOTP...")
        
        response = self.session.post(f"{self.base_url}/api/v1/totp/setup", json={})
        
        if response.status_code == 200:
            result = response.json()
            if result.get('code') == 0:
                setup_data = result['data']
                print(f"✅ TOTP设置成功")
                print(f"密钥: {setup_data['secret']}")
                print(f"手动输入密钥: {setup_data['manual_entry_key']}")
                print(f"备用码数量: {len(setup_data.get('backup_codes', []))}")
                return setup_data
            else:
                print(f"❌ TOTP设置失败: {result.get('message')}")
                return None
        else:
            print(f"❌ TOTP设置请求失败: {response.status_code}")
            return None
            
    def enable_totp(self, secret):
        """启用TOTP"""
        print("🔓 启用TOTP...")
        
        # 生成当前的TOTP验证码
        totp = pyotp.TOTP(secret)
        current_code = totp.now()
        print(f"当前验证码: {current_code}")
        
        # 启用TOTP
        response = self.session.post(
            f"{self.base_url}/api/v1/totp/enable",
            json={'code': current_code}
        )
        
        if response.status_code == 200:
            result = response.json()
            if result.get('code') == 0:
                print(f"✅ TOTP启用成功: {result.get('message')}")
                return True
            else:
                print(f"❌ TOTP启用失败: {result.get('message')}")
                return False
        else:
            print(f"❌ TOTP启用请求失败: {response.status_code}")
            return False
            
    def disable_totp(self, password):
        """禁用TOTP"""
        print("🔒 禁用TOTP...")
        
        response = self.session.post(
            f"{self.base_url}/api/v1/totp/disable",
            json={'password': password}
        )
        
        if response.status_code == 200:
            result = response.json()
            if result.get('code') == 0:
                print(f"✅ TOTP禁用成功: {result.get('message')}")
                return True
            else:
                print(f"❌ TOTP禁用失败: {result.get('message')}")
                return False
        else:
            print(f"❌ TOTP禁用请求失败: {response.status_code}")
            return False
            
    def test_totp_check_api(self, username):
        """测试TOTP状态检查API"""
        print(f"🧪 测试用户 {username} 的TOTP状态检查API...")
        
        # 不需要认证的API
        response = requests.post(
            f"{self.base_url}/api/v1/auth/check-totp",
            json={'username': username},
            headers={'Content-Type': 'application/json'}
        )
        
        if response.status_code == 200:
            result = response.json()
            if result.get('code') == 0:
                totp_status = result['data']
                print(f"✅ {username} TOTP状态: 启用={totp_status.get('totp_enabled', False)}")
                return totp_status.get('totp_enabled', False)
            else:
                print(f"❌ API错误: {result.get('message')}")
                return False
        else:
            print(f"❌ 请求失败: {response.status_code}")
            return False
            
    def setup_admin_totp(self):
        """为admin用户设置TOTP"""
        print("🚀 开始为admin用户设置TOTP")
        print("=" * 50)
        
        # 1. 登录admin用户
        if not self.login_user("admin", "7c222fb2927d828af22f592134e8932480637c0d"):
            return False
            
        # 2. 检查当前TOTP状态
        current_status = self.check_totp_status()
        if current_status and current_status.get('enabled', False):
            print("⚠️  admin用户已经启用了TOTP")
            
            # 询问是否要重新设置
            choice = input("是否要禁用并重新设置TOTP? (y/n): ").lower().strip()
            if choice == 'y':
                if not self.disable_totp("7c222fb2927d828af22f592134e8932480637c0d"):
                    return False
            else:
                print("✅ 保持当前TOTP设置")
                return True
                
        # 3. 设置TOTP
        setup_data = self.setup_totp()
        if not setup_data:
            return False
            
        # 4. 启用TOTP
        if not self.enable_totp(setup_data['secret']):
            return False
            
        # 5. 验证设置结果
        final_status = self.check_totp_status()
        if final_status and final_status.get('enabled', False):
            print("🎉 admin用户TOTP设置完成！")
            
            # 6. 测试检查API
            print("\n" + "=" * 50)
            print("🧪 测试TOTP状态检查API")
            self.test_totp_check_api("admin")
            self.test_totp_check_api("test1")
            
            return True
        else:
            print("❌ TOTP设置验证失败")
            return False
            
    def run(self):
        """运行设置流程"""
        try:
            success = self.setup_admin_totp()
            if success:
                print("\n✅ TOTP测试环境设置完成！")
                print("现在可以运行登录页面状态切换测试了。")
            else:
                print("\n❌ TOTP测试环境设置失败！")
        except Exception as e:
            print(f"\n❌ 设置过程中发生错误: {e}")

def main():
    """主函数"""
    setup = TOTPTestSetup()
    setup.run()

if __name__ == "__main__":
    main()
