from typing import Any, Dict, Optional
from enum import Enum


class ErrorCode(Enum):
    """错误代码枚举"""

    # 通用错误
    SUCCESS = 200
    BAD_REQUEST = 400
    UNAUTHORIZED = 401
    FORBIDDEN = 403
    NOT_FOUND = 404
    INTERNAL_ERROR = 500

    # 业务错误
    INVALID_PARAMETER = 40001
    RESOURCE_NOT_FOUND = 40002
    DUPLICATE_RESOURCE = 40003
    RESOURCE_CONFLICT = 40004
    OPERATION_FAILED = 40005

    # 权限错误
    PERMISSION_DENIED = 40301
    INSUFFICIENT_PRIVILEGES = 40302
    ACCESS_DENIED = 40303

    # 数据错误
    DATA_VALIDATION_ERROR = 42201
    DATA_INTEGRITY_ERROR = 42202
    DATA_CONSTRAINT_ERROR = 42203


class BusinessException(Exception):
    """业务异常基类"""

    def __init__(
        self,
        message: str,
        code: ErrorCode = ErrorCode.BAD_REQUEST,
        data: Optional[Dict[str, Any]] = None
    ):
        self.message = message
        self.code = code.value if isinstance(code, ErrorCode) else code
        self.error_code = code
        self.data = data or {}

        # 根据错误代码设置HTTP状态码
        self.status_code = self._get_status_code_from_error_code(code)
        super().__init__(message)

    def _get_status_code_from_error_code(self, error_code: ErrorCode) -> int:
        """根据错误代码获取HTTP状态码"""
        status_code_mapping = {
            ErrorCode.BAD_REQUEST: 400,
            ErrorCode.UNAUTHORIZED: 401,
            ErrorCode.FORBIDDEN: 403,
            ErrorCode.RESOURCE_NOT_FOUND: 404,
            ErrorCode.DATA_VALIDATION_ERROR: 422,
            ErrorCode.INTERNAL_ERROR: 500,
        }
        return status_code_mapping.get(error_code, 500)


class PermissionDeniedException(BusinessException):
    """权限拒绝异常"""

    def __init__(self, message: str = "没有权限执行此操作"):
        super().__init__(message=message, code=ErrorCode.FORBIDDEN)


class NotFoundException(BusinessException):
    """资源不存在异常"""

    def __init__(self, message: str = "资源不存在"):
        super().__init__(message=message, code=ErrorCode.RESOURCE_NOT_FOUND)


class ValidationException(BusinessException):
    """数据验证异常"""

    def __init__(self, message: str):
        super().__init__(message=message, code=ErrorCode.DATA_VALIDATION_ERROR)


class AuthenticationException(BusinessException):
    """认证异常"""

    def __init__(self, message: str = "认证失败"):
        super().__init__(message=message, code=ErrorCode.UNAUTHORIZED)


class SystemException(BusinessException):
    """系统异常"""

    def __init__(self, message: str = "系统错误"):
        super().__init__(message=message, code=ErrorCode.INTERNAL_ERROR)
