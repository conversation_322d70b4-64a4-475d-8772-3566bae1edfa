package logger

import (
	"fmt"
	"time"
	"walmart-bind-card-gateway/internal/config"

	"go.uber.org/zap"
	"go.uber.org/zap/zapcore"
)

// Logger 日志接口
type Logger interface {
	Debug(msg string, fields ...zap.Field)
	Info(msg string, fields ...zap.Field)
	Warn(msg string, fields ...zap.Field)
	Error(msg string, fields ...zap.Field)
	Fatal(msg string, fields ...zap.Field)
	
	With(fields ...zap.Field) Logger
	Sync() error
}

// zapLogger zap日志实现
type zapLogger struct {
	logger *zap.Logger
}

// NewLogger 创建新的日志器
func NewLogger(cfg config.LoggingConfig) (Logger, error) {
	// 构建zap配置
	zapConfig := buildZapConfig(cfg)
	
	// 创建logger
	logger, err := zapConfig.Build(
		zap.AddCallerSkip(1), // 跳过wrapper层
		zap.AddStacktrace(zapcore.ErrorLevel),
	)
	if err != nil {
		return nil, fmt.Errorf("创建logger失败: %w", err)
	}
	
	// 添加服务字段
	if cfg.Fields != nil {
		fields := make([]zap.Field, 0, len(cfg.Fields))
		for key, value := range cfg.Fields {
			fields = append(fields, zap.String(key, value))
		}
		logger = logger.With(fields...)
	}
	
	return &zapLogger{logger: logger}, nil
}

// buildZapConfig 构建zap配置
func buildZapConfig(cfg config.LoggingConfig) zap.Config {
	var zapConfig zap.Config
	
	// 根据格式选择配置
	if cfg.Format == "json" {
		zapConfig = zap.NewProductionConfig()
		zapConfig.EncoderConfig.TimeKey = "timestamp"
		zapConfig.EncoderConfig.EncodeTime = zapcore.ISO8601TimeEncoder
	} else {
		zapConfig = zap.NewDevelopmentConfig()
		zapConfig.EncoderConfig.EncodeTime = zapcore.ISO8601TimeEncoder
		zapConfig.EncoderConfig.EncodeLevel = zapcore.CapitalColorLevelEncoder
	}
	
	// 设置日志级别
	level, err := zapcore.ParseLevel(cfg.Level)
	if err != nil {
		level = zapcore.InfoLevel
	}
	zapConfig.Level = zap.NewAtomicLevelAt(level)
	
	// 设置输出
	if cfg.Output == "stdout" {
		zapConfig.OutputPaths = []string{"stdout"}
	} else if cfg.Output == "stderr" {
		zapConfig.OutputPaths = []string{"stderr"}
	} else {
		// 文件输出
		zapConfig.OutputPaths = []string{cfg.Output}
	}
	
	// 错误输出
	zapConfig.ErrorOutputPaths = []string{"stderr"}
	
	// 禁用采样以确保所有日志都被记录
	zapConfig.Sampling = nil
	
	return zapConfig
}

// Debug 记录调试日志
func (l *zapLogger) Debug(msg string, fields ...zap.Field) {
	l.logger.Debug(msg, fields...)
}

// Info 记录信息日志
func (l *zapLogger) Info(msg string, fields ...zap.Field) {
	l.logger.Info(msg, fields...)
}

// Warn 记录警告日志
func (l *zapLogger) Warn(msg string, fields ...zap.Field) {
	l.logger.Warn(msg, fields...)
}

// Error 记录错误日志
func (l *zapLogger) Error(msg string, fields ...zap.Field) {
	l.logger.Error(msg, fields...)
}

// Fatal 记录致命错误日志并退出
func (l *zapLogger) Fatal(msg string, fields ...zap.Field) {
	l.logger.Fatal(msg, fields...)
}

// With 添加字段
func (l *zapLogger) With(fields ...zap.Field) Logger {
	return &zapLogger{logger: l.logger.With(fields...)}
}

// Sync 同步日志
func (l *zapLogger) Sync() error {
	return l.logger.Sync()
}

// RequestLogger 请求日志中间件
type RequestLogger struct {
	logger Logger
}

// NewRequestLogger 创建请求日志中间件
func NewRequestLogger(logger Logger) *RequestLogger {
	return &RequestLogger{logger: logger}
}

// LogRequest 记录请求日志
func (rl *RequestLogger) LogRequest(
	method, path, clientIP, userAgent string,
	statusCode int,
	latency time.Duration,
	requestSize, responseSize int64,
) {
	fields := []zap.Field{
		zap.String("method", method),
		zap.String("path", path),
		zap.String("client_ip", clientIP),
		zap.String("user_agent", userAgent),
		zap.Int("status_code", statusCode),
		zap.Duration("latency", latency),
		zap.Int64("request_size", requestSize),
		zap.Int64("response_size", responseSize),
	}
	
	// 根据状态码选择日志级别
	if statusCode >= 500 {
		rl.logger.Error("HTTP请求", fields...)
	} else if statusCode >= 400 {
		rl.logger.Warn("HTTP请求", fields...)
	} else {
		rl.logger.Info("HTTP请求", fields...)
	}
}

// PerformanceLogger 性能日志记录器
type PerformanceLogger struct {
	logger Logger
}

// NewPerformanceLogger 创建性能日志记录器
func NewPerformanceLogger(logger Logger) *PerformanceLogger {
	return &PerformanceLogger{logger: logger}
}

// LogBatchProcess 记录批量处理日志
func (pl *PerformanceLogger) LogBatchProcess(
	batchSize int,
	duration time.Duration,
	dbLatency, redisLatency, queueLatency time.Duration,
) {
	pl.logger.Info("批量处理完成",
		zap.Int("batch_size", batchSize),
		zap.Duration("total_duration", duration),
		zap.Duration("db_latency", dbLatency),
		zap.Duration("redis_latency", redisLatency),
		zap.Duration("queue_latency", queueLatency),
	)
}

// LogDatabaseOperation 记录数据库操作日志
func (pl *PerformanceLogger) LogDatabaseOperation(
	operation string,
	duration time.Duration,
	recordCount int,
	err error,
) {
	fields := []zap.Field{
		zap.String("operation", operation),
		zap.Duration("duration", duration),
		zap.Int("record_count", recordCount),
	}
	
	if err != nil {
		fields = append(fields, zap.Error(err))
		pl.logger.Error("数据库操作失败", fields...)
	} else {
		pl.logger.Debug("数据库操作完成", fields...)
	}
}

// LogRedisOperation 记录Redis操作日志
func (pl *PerformanceLogger) LogRedisOperation(
	operation string,
	duration time.Duration,
	keyCount int,
	err error,
) {
	fields := []zap.Field{
		zap.String("operation", operation),
		zap.Duration("duration", duration),
		zap.Int("key_count", keyCount),
	}
	
	if err != nil {
		fields = append(fields, zap.Error(err))
		pl.logger.Error("Redis操作失败", fields...)
	} else {
		pl.logger.Debug("Redis操作完成", fields...)
	}
}

// LogQueueOperation 记录队列操作日志
func (pl *PerformanceLogger) LogQueueOperation(
	operation string,
	duration time.Duration,
	messageCount int,
	err error,
) {
	fields := []zap.Field{
		zap.String("operation", operation),
		zap.Duration("duration", duration),
		zap.Int("message_count", messageCount),
	}
	
	if err != nil {
		fields = append(fields, zap.Error(err))
		pl.logger.Error("队列操作失败", fields...)
	} else {
		pl.logger.Debug("队列操作完成", fields...)
	}
}

// ErrorLogger 错误日志记录器
type ErrorLogger struct {
	logger Logger
}

// NewErrorLogger 创建错误日志记录器
func NewErrorLogger(logger Logger) *ErrorLogger {
	return &ErrorLogger{logger: logger}
}

// LogPanic 记录panic日志
func (el *ErrorLogger) LogPanic(recovered interface{}, stack []byte) {
	el.logger.Error("系统panic",
		zap.Any("recovered", recovered),
		zap.ByteString("stack", stack),
	)
}

// LogBusinessError 记录业务错误日志
func (el *ErrorLogger) LogBusinessError(
	operation string,
	requestID string,
	err error,
	context map[string]interface{},
) {
	fields := []zap.Field{
		zap.String("operation", operation),
		zap.String("request_id", requestID),
		zap.Error(err),
	}
	
	// 添加上下文信息
	for key, value := range context {
		fields = append(fields, zap.Any(key, value))
	}
	
	el.logger.Error("业务错误", fields...)
}

// LogSystemError 记录系统错误日志
func (el *ErrorLogger) LogSystemError(
	component string,
	err error,
	context map[string]interface{},
) {
	fields := []zap.Field{
		zap.String("component", component),
		zap.Error(err),
	}
	
	// 添加上下文信息
	for key, value := range context {
		fields = append(fields, zap.Any(key, value))
	}
	
	el.logger.Error("系统错误", fields...)
}

// GetDefaultLogger 获取默认日志器
func GetDefaultLogger() Logger {
	logger, _ := zap.NewProduction()
	return &zapLogger{logger: logger}
}

// SetGlobalLogger 设置全局日志器
var globalLogger Logger

func SetGlobalLogger(logger Logger) {
	globalLogger = logger
}

// GetGlobalLogger 获取全局日志器
func GetGlobalLogger() Logger {
	if globalLogger == nil {
		globalLogger = GetDefaultLogger()
	}
	return globalLogger
}

// 便捷函数
func Debug(msg string, fields ...zap.Field) {
	GetGlobalLogger().Debug(msg, fields...)
}

func Info(msg string, fields ...zap.Field) {
	GetGlobalLogger().Info(msg, fields...)
}

func Warn(msg string, fields ...zap.Field) {
	GetGlobalLogger().Warn(msg, fields...)
}

func Error(msg string, fields ...zap.Field) {
	GetGlobalLogger().Error(msg, fields...)
}

func Fatal(msg string, fields ...zap.Field) {
	GetGlobalLogger().Fatal(msg, fields...)
}
