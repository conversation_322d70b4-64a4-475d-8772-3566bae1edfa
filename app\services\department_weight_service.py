"""
部门权重算法服务

实现基于部门权重的CK分配算法，支持：
1. 加权随机选择算法
2. 部门开关状态检查
3. CK可用性验证
4. 权重分配统计
"""

import random
import logging
from typing import List, Optional, Dict, Any, Tuple
from sqlalchemy.orm import Session
from sqlalchemy import func, case

from app.models.department import Department
from app.models.walmart_ck import WalmartCK


logger = logging.getLogger(__name__)


class DepartmentWeightService:
    """部门权重算法服务"""
    
    def __init__(self, db: Session):
        self.db = db
        self.logger = logger
    
    def get_available_departments_with_weights(
        self, 
        merchant_id: int
    ) -> List[Dict[str, Any]]:
        """
        获取商户下所有可用的部门及其权重信息
        
        Args:
            merchant_id: 商户ID
            
        Returns:
            List[Dict]: 部门权重信息列表
        """
        try:
            self.logger.info(f"开始查询商户 {merchant_id} 的可用部门权重信息")

            # 查询启用绑卡的部门及其CK统计
            departments = self.db.query(
                Department.id,
                Department.name,
                Department.binding_weight,
                func.count(WalmartCK.id).label('total_ck_count'),
                func.sum(
                    case(
                        (WalmartCK.active == True, 1),
                        else_=0
                    )
                ).label('active_ck_count'),
                func.sum(
                    case(
                        (
                            (WalmartCK.active == True) &
                            (
                                (WalmartCK.total_limit.is_(None)) |
                                (WalmartCK.bind_count < WalmartCK.total_limit)
                            ) &
                            (WalmartCK.is_deleted == False), 1
                        ),
                        else_=0
                    )
                ).label('available_ck_count')
            ).outerjoin(
                WalmartCK, Department.id == WalmartCK.department_id
            ).filter(
                Department.merchant_id == merchant_id,
                Department.status == True,
                Department.enable_binding == True,
                Department.binding_weight > 0
            ).group_by(
                Department.id, Department.name, Department.binding_weight
            ).all()

            self.logger.info(f"查询到 {len(departments)} 个部门")

            result = []
            for dept in departments:
                result.append({
                    'id': dept.id,
                    'name': dept.name,
                    'binding_weight': dept.binding_weight,
                    'total_ck_count': dept.total_ck_count or 0,
                    'active_ck_count': dept.active_ck_count or 0,
                    'available_ck_count': dept.available_ck_count or 0,
                    'can_bind': (dept.available_ck_count or 0) > 0
                })
            
            return result
            
        except Exception as e:
            self.logger.error(f"获取部门权重信息失败: {e}")
            return []
    
    def select_department_by_weight(
        self, 
        merchant_id: int,
        exclude_department_ids: Optional[List[int]] = None
    ) -> Optional[Dict[str, Any]]:
        """
        基于权重算法选择部门
        
        Args:
            merchant_id: 商户ID
            exclude_department_ids: 排除的部门ID列表
            
        Returns:
            Optional[Dict]: 选中的部门信息，如果没有可用部门则返回None
        """
        try:
            self.logger.info(f"开始基于权重选择部门，商户ID: {merchant_id}")
            # 获取所有可用部门
            available_departments = self.get_available_departments_with_weights(merchant_id)
            self.logger.info(f"获取到 {len(available_departments)} 个可用部门")
            
            # 过滤掉排除的部门和没有可用CK的部门
            if exclude_department_ids:
                available_departments = [
                    dept for dept in available_departments 
                    if dept['id'] not in exclude_department_ids and dept['can_bind']
                ]
            else:
                available_departments = [
                    dept for dept in available_departments 
                    if dept['can_bind']
                ]

            self.logger.info(f"过滤后可绑卡的部门数量: {len(available_departments)}")
            for dept in available_departments:
                self.logger.info(f"可绑卡部门: {dept['name']} (ID: {dept['id']}, 权重: {dept['binding_weight']}, 可用CK: {dept['available_ck_count']})")

            if not available_departments:
                self.logger.warning(f"商户 {merchant_id} 没有可用的部门进行绑卡")
                return None
            
            # 如果只有一个部门，直接返回
            if len(available_departments) == 1:
                selected = available_departments[0]
                self.logger.info(f"只有一个可用部门，直接选择: {selected['name']} (权重: {selected['binding_weight']})")
                return selected
            
            # 计算权重总和
            total_weight = sum(dept['binding_weight'] for dept in available_departments)
            
            if total_weight <= 0:
                self.logger.warning(f"商户 {merchant_id} 所有部门权重总和为0")
                return None
            
            # 【修复】使用cryptographically secure random确保真正的随机性
            import secrets
            random_value = secrets.randbelow(total_weight) + 1
            current_weight = 0

            for dept in available_departments:
                current_weight += dept['binding_weight']
                if random_value <= current_weight:
                    self.logger.info(
                        f"权重算法选择部门: {dept['name']} "
                        f"(权重: {dept['binding_weight']}/{total_weight}, "
                        f"随机值: {random_value}, 累计权重: {current_weight})"
                    )
                    return dept
            
            # 理论上不应该到达这里，但作为保险返回第一个部门
            selected = available_departments[0]
            self.logger.warning(f"权重算法异常，返回第一个部门: {selected['name']}")
            return selected
            
        except Exception as e:
            self.logger.error(f"基于权重选择部门失败: {e}")
            return None
    
    def get_ck_from_selected_department(
        self, 
        merchant_id: int, 
        department_id: int,
        exclude_ck_ids: Optional[List[int]] = None
    ) -> Optional[WalmartCK]:
        """
        从选中的部门获取可用CK
        
        Args:
            merchant_id: 商户ID
            department_id: 部门ID
            exclude_ck_ids: 排除的CK ID列表
            
        Returns:
            Optional[WalmartCK]: 可用的CK对象
        """
        try:
            query = self.db.query(WalmartCK).filter(
                WalmartCK.merchant_id == merchant_id,
                WalmartCK.department_id == department_id,
                WalmartCK.active == True,
                WalmartCK.bind_count < WalmartCK.total_limit,
                WalmartCK.is_deleted == False
            )
            
            if exclude_ck_ids:
                query = query.filter(~WalmartCK.id.in_(exclude_ck_ids))
            
            # 按使用次数升序排列，选择负载最低的CK
            ck = query.order_by(WalmartCK.bind_count.asc()).first()
            
            if ck:
                self.logger.info(
                    f"从部门 {department_id} 选择CK {ck.id} "
                    f"(使用次数: {ck.bind_count}/{ck.total_limit})"
                )
            else:
                self.logger.warning(f"部门 {department_id} 没有可用的CK")
            
            return ck
            
        except Exception as e:
            self.logger.error(f"从部门 {department_id} 获取CK失败: {e}")
            return None
    
    def get_weight_distribution_stats(self, merchant_id: int) -> Dict[str, Any]:
        """
        获取权重分配统计信息
        
        Args:
            merchant_id: 商户ID
            
        Returns:
            Dict: 权重分配统计信息
        """
        try:
            departments = self.get_available_departments_with_weights(merchant_id)
            
            if not departments:
                return {
                    'total_departments': 0,
                    'total_weight': 0,
                    'departments': []
                }
            
            total_weight = sum(dept['binding_weight'] for dept in departments)
            
            # 计算每个部门的权重占比
            for dept in departments:
                if total_weight > 0:
                    dept['weight_percentage'] = round(
                        (dept['binding_weight'] / total_weight) * 100, 2
                    )
                else:
                    dept['weight_percentage'] = 0
            
            return {
                'total_departments': len(departments),
                'total_weight': total_weight,
                'departments': departments
            }
            
        except Exception as e:
            self.logger.error(f"获取权重分配统计失败: {e}")
            return {
                'total_departments': 0,
                'total_weight': 0,
                'departments': []
            }
    
    def validate_weight_configuration(self, merchant_id: int) -> Dict[str, Any]:
        """
        验证权重配置的合理性
        
        Args:
            merchant_id: 商户ID
            
        Returns:
            Dict: 验证结果
        """
        try:
            departments = self.get_available_departments_with_weights(merchant_id)
            
            issues = []
            warnings = []
            
            if not departments:
                issues.append("没有启用绑卡的部门")
            else:
                # 检查是否有部门没有可用CK
                no_ck_departments = [
                    dept for dept in departments 
                    if dept['available_ck_count'] == 0
                ]
                
                if no_ck_departments:
                    warnings.append(
                        f"以下部门没有可用CK: {', '.join([d['name'] for d in no_ck_departments])}"
                    )
                
                # 检查权重分配是否合理
                total_weight = sum(dept['binding_weight'] for dept in departments)
                if total_weight == 0:
                    issues.append("所有部门权重总和为0")
                
                # 检查是否有权重过高的部门
                for dept in departments:
                    if total_weight > 0:
                        percentage = (dept['binding_weight'] / total_weight) * 100
                        if percentage > 90:
                            warnings.append(
                                f"部门 {dept['name']} 权重占比过高 ({percentage:.1f}%)"
                            )
            
            return {
                'is_valid': len(issues) == 0,
                'issues': issues,
                'warnings': warnings,
                'departments_count': len(departments)
            }
            
        except Exception as e:
            self.logger.error(f"验证权重配置失败: {e}")
            return {
                'is_valid': False,
                'issues': [f"验证失败: {str(e)}"],
                'warnings': [],
                'departments_count': 0
            }
