"""
测试批量操作API的数据隔离安全修复
验证批量创建、批量同步、批量回调等操作的商户级数据隔离机制
"""

import pytest
from sqlalchemy.orm import Session
from app.models.user import User
from app.models.merchant import Merchant
from app.models.department import Department
from app.models.card_record import CardRecord, CardStatus
from app.models.walmart_ck import WalmartCK
from app.services.walmart_ck_service_new import WalmartCKService
from app.services.card_api_service import CardAPIService
from app.schemas.walmart_ck import WalmartCKBatchCreate
from app.schemas.card_record import CardRecordCreate


class TestBatchOperationsSecurity:
    """测试批量操作安全修复"""

    @pytest.fixture
    def setup_test_data(self, db: Session):
        """设置测试数据"""
        # 创建两个商户
        merchant1 = Merchant(
            name="测试商户1",
            code="TEST_MERCHANT_1",
            api_key="test_key_1",
            api_secret="test_secret_1"
        )
        merchant2 = Merchant(
            name="测试商户2", 
            code="TEST_MERCHANT_2",
            api_key="test_key_2",
            api_secret="test_secret_2"
        )
        db.add_all([merchant1, merchant2])
        db.commit()
        db.refresh(merchant1)
        db.refresh(merchant2)

        # 创建部门
        dept1 = Department(
            name="测试部门1",
            merchant_id=merchant1.id,
            code="DEPT_1"
        )
        dept2 = Department(
            name="测试部门2",
            merchant_id=merchant2.id,
            code="DEPT_2"
        )
        db.add_all([dept1, dept2])
        db.commit()
        db.refresh(dept1)
        db.refresh(dept2)

        # 创建用户
        user1 = User(
            username="test_user_1",
            email="<EMAIL>",
            hashed_password="hashed_password",
            merchant_id=merchant1.id,
            department_id=dept1.id,
            is_superuser=False
        )
        user2 = User(
            username="test_user_2",
            email="<EMAIL>", 
            hashed_password="hashed_password",
            merchant_id=merchant2.id,
            department_id=dept2.id,
            is_superuser=False
        )
        superuser = User(
            username="superuser",
            email="<EMAIL>",
            hashed_password="hashed_password",
            is_superuser=True
        )
        db.add_all([user1, user2, superuser])
        db.commit()
        db.refresh(user1)
        db.refresh(user2)
        db.refresh(superuser)

        return {
            "merchant1": merchant1,
            "merchant2": merchant2,
            "dept1": dept1,
            "dept2": dept2,
            "user1": user1,
            "user2": user2,
            "superuser": superuser
        }

    def test_batch_create_walmart_ck_security(self, db: Session, setup_test_data):
        """测试批量创建沃尔玛CK的安全性"""
        ck_service = WalmartCKService(db)
        user1 = setup_test_data["user1"]
        user2 = setup_test_data["user2"]

        # 用户1尝试为自己的商户创建CK（应该成功）
        batch_data1 = WalmartCKBatchCreate(
            signs=["test_ck_1", "test_ck_2"],
            merchant_id=user1.merchant_id,
            department_id=user1.department_id,
            total_limit=100,
            active=True,
            description="测试CK"
        )
        
        result1 = ck_service.batch_create_walmart_ck(batch_data1, user1)
        assert result1["success_count"] == 2, "用户1应该能为自己的商户创建CK"

        # 用户1尝试为其他商户创建CK（应该失败）
        batch_data2 = WalmartCKBatchCreate(
            signs=["test_ck_3", "test_ck_4"],
            merchant_id=user2.merchant_id,  # 尝试为用户2的商户创建
            department_id=user2.department_id,
            total_limit=100,
            active=True,
            description="测试CK"
        )
        
        with pytest.raises(ValueError, match="只能为自己所属的商户创建CK"):
            ck_service.batch_create_walmart_ck(batch_data2, user1)

    def test_batch_create_card_records_security(self, db: Session, setup_test_data):
        """测试批量创建绑卡记录的安全性"""
        card_api_service = CardAPIService(db)
        user1 = setup_test_data["user1"]

        # 创建批量绑卡记录数据
        batch_data = [
            CardRecordCreate(
                merchant_order_id=f"ORDER_{i}",
                card_number=f"123456789012345{i}",
                amount=10000,
                merchant_id=user1.merchant_id,
                department_id=user1.department_id
            ) for i in range(3)
        ]

        # 用户1应该能创建自己商户的绑卡记录
        result = card_api_service.batch_create_card_records(batch_data, user1)
        assert result["success_count"] == 3, "用户1应该能创建自己商户的绑卡记录"

    def test_batch_sync_card_amount_security(self, db: Session, setup_test_data):
        """测试批量同步卡片金额的安全性"""
        user1 = setup_test_data["user1"]
        user2 = setup_test_data["user2"]

        # 创建两个商户的卡记录
        card1 = CardRecord(
            id="card_1",
            card_number="1234567890123456",
            merchant_id=user1.merchant_id,
            department_id=user1.department_id,
            status=CardStatus.SUCCESS,
            amount=10000
        )
        card2 = CardRecord(
            id="card_2",
            card_number="****************",
            merchant_id=user2.merchant_id,
            department_id=user2.department_id,
            status=CardStatus.SUCCESS,
            amount=20000
        )
        db.add_all([card1, card2])
        db.commit()

        # 测试API端点的安全检查（模拟API调用）
        from app.services.card_record_service import CardRecordService
        card_service = CardRecordService(db)
        
        # 用户1应该只能访问自己的卡记录
        accessible_cards = card_service._batch_get_cards_with_isolation(["card_1", "card_2"], user1)
        accessible_card_ids = [card.id for card in accessible_cards]
        
        assert "card_1" in accessible_card_ids, "用户1应该能访问自己的卡记录"
        assert "card_2" not in accessible_card_ids, "用户1不应该能访问其他商户的卡记录"

    def test_manual_callback_service_security(self, db: Session, setup_test_data):
        """测试手动回调服务的安全性"""
        from app.services.manual_callback_service import ManualCallbackService
        
        user1 = setup_test_data["user1"]
        user2 = setup_test_data["user2"]

        # 创建两个商户的卡记录
        card1 = CardRecord(
            id="card_1",
            card_number="1234567890123456",
            merchant_id=user1.merchant_id,
            department_id=user1.department_id,
            status=CardStatus.SUCCESS,
            amount=10000
        )
        card2 = CardRecord(
            id="card_2",
            card_number="****************",
            merchant_id=user2.merchant_id,
            department_id=user2.department_id,
            status=CardStatus.SUCCESS,
            amount=20000
        )
        db.add_all([card1, card2])
        db.commit()

        callback_service = ManualCallbackService(db)

        # 用户1应该能触发自己商户的卡记录回调
        result1 = await callback_service.trigger_manual_callback("card_1", user1, False)
        assert result1["success"] == True, "用户1应该能触发自己商户的卡记录回调"

        # 用户1不应该能触发其他商户的卡记录回调
        result2 = await callback_service.trigger_manual_callback("card_2", user1, False)
        assert result2["success"] == False, "用户1不应该能触发其他商户的卡记录回调"
        assert "不存在或无权限访问" in result2["error"], "应该返回权限错误"

    def test_superuser_batch_operations(self, db: Session, setup_test_data):
        """测试超级管理员的批量操作权限"""
        superuser = setup_test_data["superuser"]
        user1 = setup_test_data["user1"]
        user2 = setup_test_data["user2"]

        # 创建两个商户的卡记录
        card1 = CardRecord(
            id="card_1",
            card_number="1234567890123456",
            merchant_id=user1.merchant_id,
            status=CardStatus.SUCCESS,
            amount=10000
        )
        card2 = CardRecord(
            id="card_2",
            card_number="****************",
            merchant_id=user2.merchant_id,
            status=CardStatus.SUCCESS,
            amount=20000
        )
        db.add_all([card1, card2])
        db.commit()

        # 超级管理员应该能访问所有商户的卡记录
        from app.services.card_record_service import CardRecordService
        card_service = CardRecordService(db)
        
        accessible_cards = card_service._batch_get_cards_with_isolation(["card_1", "card_2"], superuser)
        accessible_card_ids = [card.id for card in accessible_cards]
        
        assert len(accessible_card_ids) == 2, "超级管理员应该能访问所有商户的卡记录"
        assert "card_1" in accessible_card_ids, "超级管理员应该能访问商户1的卡记录"
        assert "card_2" in accessible_card_ids, "超级管理员应该能访问商户2的卡记录"
