import asyncio
import logging
import os
import sys
import traceback
from contextlib import asynccontextmanager

# 添加项目根目录到Python路径，确保可以导入app模块
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(current_dir)
if project_root not in sys.path:
    sys.path.insert(0, project_root)

from fastapi import FastAPI, Request
from fastapi.exceptions import RequestValidationError
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.trustedhost import TrustedHostMiddleware
from fastapi.responses import JSONResponse
from starlette.exceptions import HTTPException as StarletteHTTPException

# 导入修复脚本解决 bcrypt 警告问题
try:
    from app.utils import bcrypt_fix  # noqa: F401
    logging.info("bcrypt兼容性修复已加载")
except ImportError:
    logging.warning("未能导入 bcrypt 警告修复脚本，可能会看到 bcrypt 版本警告")

from app.api.v1.router import api_router
from app.core.config import settings
from app.db.session import SessionLocal
from sqlalchemy import text
# 移除 init_db 导入，现在只使用 init.sql 初始化数据
from app.core.middleware import setup_middlewares
from app.api.errors import setup_exception_handlers
from app.schemas.response import CustomJSONResponse
from app.core.logging import setup_logging, get_logger
from app.services.queue_consumer import start_bind_card_consumer
from app.services.scheduler_service import scheduler_service
from app.services.integrated_callback_manager import integrated_callback_manager
from app.utils.docker_compatibility import validate_environment_on_startup

# 设置日志
setup_logging(log_level="DEBUG")
logger = get_logger("main")

# 确保日志目录存在
logs_dir = os.path.join(
    os.path.dirname(os.path.dirname(os.path.abspath(__file__))), "logs"
)
if not os.path.exists(logs_dir):
    os.makedirs(logs_dir)

# 配置日志
log_file = os.path.join(logs_dir, "app.log")
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    handlers=[
        # 控制台处理器
        logging.StreamHandler(),
        # 文件处理器
        logging.FileHandler(log_file, encoding="utf-8"),
    ],
)

# 为API请求设置专门的日志器
api_logger = logging.getLogger("app.api")
api_logger.setLevel(logging.DEBUG)  # 降低API日志级别，捕获更多信息

# 添加专门的API日志文件
api_log_file = os.path.join(logs_dir, "api.log")
api_file_handler = logging.FileHandler(api_log_file, encoding="utf-8")
api_file_handler.setFormatter(
    logging.Formatter("%(asctime)s - %(name)s - %(levelname)s - %(message)s")
)
api_logger.addHandler(api_file_handler)

# 为认证模块设置专门的日志器
auth_logger = logging.getLogger("app.api.auth")
auth_logger.setLevel(logging.DEBUG)  # 降低认证日志级别，捕获更多信息

# 添加专门的认证日志文件
auth_log_file = os.path.join(logs_dir, "auth.log")
auth_file_handler = logging.FileHandler(auth_log_file, encoding="utf-8")
auth_file_handler.setFormatter(
    logging.Formatter("%(asctime)s - %(name)s - %(levelname)s - %(message)s")
)
auth_logger.addHandler(auth_file_handler)

logger = logging.getLogger(__name__)


@asynccontextmanager
async def lifespan(app: FastAPI):
    """
    应用生命周期管理
    """
    # 启动事件 (之前的 startup_event)
    logger.info("应用启动中...")

    # 存储后台任务的列表
    background_tasks = []

    try:
        # 数据库初始化现在通过 init.sql 文件在 MySQL 容器启动时自动完成
        # 这里只需要验证数据库连接
        db = SessionLocal()
        try:
            # 简单的数据库连接测试
            db.execute(text("SELECT 1"))
            logger.info("数据库连接验证成功")
        except Exception as e:
            logger.error(f"数据库连接验证失败: {str(e)}", exc_info=True)
            raise e
        finally:
            db.close()
            logger.info("数据库连接已关闭")
    except Exception as e:
        logger.error(f"应用启动失败: {str(e)}", exc_info=True)
        raise e

    # 环境兼容性检查
    logger.info("开始环境兼容性检查...")
    try:
        await validate_environment_on_startup()
        logger.info("环境兼容性检查完成")
    except Exception as e:
        logger.warning(f"环境兼容性检查失败: {e}")

    logger.info("应用启动完成")

    # 启动绑卡队列消费者（根据配置决定是否启动）
    if settings.BUSINESS_BINDING_ENABLED:
        try:
            bind_card_task = asyncio.create_task(start_bind_card_consumer())
            background_tasks.append(bind_card_task)
            logger.info("绑卡队列消费者任务已创建")
        except Exception as e:
            logger.warning(f"启动绑卡队列消费者失败: {str(e)}")
    else:
        logger.info("绑卡队列消费者已禁用，跳过启动")

    # 启动集成回调服务管理器（替代原有的回调队列消费者）
    try:
        if settings.BUSINESS_CALLBACK_ENABLED:
            await integrated_callback_manager.startup()
            logger.info("集成回调服务管理器启动成功")
        else:
            logger.info("集成回调服务管理器已禁用，跳过启动")
    except Exception as e:
        logger.error(f"集成回调服务管理器启动失败: {e}")
        # 不抛出异常，让应用继续启动，但记录错误

    # CK选择已改为使用数据库，不再使用Redis
    logger.info("CK选择服务使用数据库模式（已禁用Redis）")

    # 初始化并启动调度器
    logger.info("正在初始化调度器...")
    try:
        scheduler_service.initialize()
        logger.info("调度器初始化完成")
    except Exception as e:
        logger.warning(f"调度器初始化失败: {str(e)}")

    # 初始化并启动Telegram机器人服务
    logger.info("正在初始化Telegram机器人服务...")
    telegram_bot_started = False
    try:
        from app.telegram_bot.services.bot_service import initialize_bot_service, start_bot_service
        if await initialize_bot_service():
            if await start_bot_service():
                telegram_bot_started = True
                logger.info("Telegram机器人服务启动成功")
            else:
                logger.warning("Telegram机器人服务启动失败")
        else:
            logger.warning("Telegram机器人服务初始化失败")
    except Exception as e:
        logger.warning(f"Telegram机器人服务启动异常: {str(e)}")

    yield  # 这里是应用运行时

    # 关闭事件 (之前的 shutdown_event)
    logger.info("应用关闭中...")

    # 取消所有后台任务
    for task in background_tasks:
        if not task.done():
            logger.info(f"正在取消任务: {task.get_name()}")
            task.cancel()
            try:
                await task
            except asyncio.CancelledError:
                logger.info(f"任务已取消: {task.get_name()}")
            except Exception as e:
                logger.error(f"取消任务时出错: {str(e)}")

    # 关闭集成回调服务管理器
    try:
        await integrated_callback_manager.shutdown()
        logger.info("集成回调服务管理器已关闭")
    except Exception as e:
        logger.warning(f"关闭集成回调服务管理器时出错: {str(e)}")

    # 关闭Telegram机器人服务
    try:
        from app.telegram_bot.services.bot_service import stop_bot_service
        if await stop_bot_service():
            logger.info("Telegram机器人服务已关闭")
        else:
            logger.warning("Telegram机器人服务关闭失败")
    except Exception as e:
        logger.warning(f"关闭Telegram机器人服务时出错: {str(e)}")

    # 关闭调度器
    try:
        scheduler_service.shutdown()
        logger.info("调度器已关闭")
    except Exception as e:
        logger.warning(f"关闭调度器时出错: {str(e)}")

    logger.info("应用已关闭")


def create_app() -> FastAPI:
    """
    创建 FastAPI 应用实例
    """
    logger.info("正在创建应用实例...")

    app = FastAPI(
        title=settings.PROJECT_NAME,
        openapi_url=f"{settings.API_V1_STR}/openapi.json",
        docs_url="/docs",  # 启用 Swagger UI
        redoc_url="/redoc",  # 启用 ReDoc
        lifespan=lifespan,
        version="1.0.0",
        default_response_class=CustomJSONResponse,  # 设置默认响应类
        # 添加OAuth2安全配置
        swagger_ui_oauth2_redirect_url="/docs/oauth2-redirect",
    )

    logger.info("设置异常处理器...")
    # 设置异常处理器
    setup_exception_handlers(app)

    logger.info("设置CORS中间件...")
    # 设置CORS
    app.add_middleware(
        CORSMiddleware,
        allow_origins=settings.BACKEND_CORS_ORIGINS,
        allow_credentials=settings.CORS_ALLOW_CREDENTIALS,
        allow_methods=settings.CORS_ALLOW_METHODS,
        allow_headers=settings.CORS_ALLOW_HEADERS,
    )

    logger.info("设置可信主机中间件...")
    # 添加可信主机中间件
    app.add_middleware(
        TrustedHostMiddleware,
        allowed_hosts=settings.ALLOWED_HOSTS,
    )

    logger.info("设置统一响应格式中间件...")
    # 设置统一响应格式中间件
    setup_middlewares(app)
    logger.info("添加响应处理中间件...")
    logger.info("注册API路由...")
    # 包含 API 路由
    app.include_router(api_router, prefix=settings.API_V1_STR)

    # 自定义OpenAPI配置以支持OAuth2
    def custom_openapi():
        if app.openapi_schema:
            return app.openapi_schema

        from fastapi.openapi.utils import get_openapi

        openapi_schema = get_openapi(
            title=app.title,
            version=app.version,
            openapi_version=app.openapi_version,
            description=app.description,
            routes=app.routes,
        )

        # 添加OAuth2安全方案
        openapi_schema["components"]["securitySchemes"] = {
            "OAuth2PasswordBearer": {
                "type": "oauth2",
                "flows": {
                    "password": {
                        "tokenUrl": f"{settings.API_V1_STR}/auth/login",
                        "scopes": {}
                    }
                }
            },
            "HTTPBearer": {
                "type": "http",
                "scheme": "bearer",
                "bearerFormat": "JWT"
            }
        }

        # 为所有需要认证的端点添加安全要求
        for path_item in openapi_schema["paths"].values():
            for operation in path_item.values():
                if isinstance(operation, dict) and "security" not in operation:
                    # 检查是否是需要认证的端点（排除登录和公共端点）
                    operation_id = operation.get("operationId", "")
                    if (operation_id and
                        "login" not in operation_id.lower() and
                        "health" not in operation_id.lower() and
                        "system_check" not in operation_id.lower() and
                        "public" not in operation_id.lower()):
                        operation["security"] = [
                            {"HTTPBearer": []},
                            {"OAuth2PasswordBearer": []}
                        ]

        app.openapi_schema = openapi_schema
        return app.openapi_schema

    app.openapi = custom_openapi

    # 添加全局异常处理器
    @app.exception_handler(Exception)
    async def global_exception_handler(request: Request, exc: Exception):
        """处理所有未捕获的异常"""
        logger.error(f"全局异常处理器被触发: {str(exc)}")
        error_msg = f"未捕获的异常: {str(exc)}"
        error_detail = "".join(
            traceback.format_exception(type(exc), exc, exc.__traceback__)
        )
        logger.error(f"{error_msg}\n{error_detail}")
        logger.error(f"请求路径: {request.url.path}")

        return JSONResponse(
            status_code=500,
            content={"code": 1, "message": "服务器内部错误", "data": None},
        )

    @app.exception_handler(StarletteHTTPException)
    async def http_exception_handler(request: Request, exc: StarletteHTTPException):
        """处理HTTP异常"""
        logger.error(f"HTTP异常: {exc.status_code} - {exc.detail}")
        logger.error(f"请求路径: {request.url.path}")

        return JSONResponse(
            status_code=exc.status_code,
            content={"code": 1, "message": exc.detail, "data": None},
        )

    @app.exception_handler(RequestValidationError)
    async def validation_exception_handler(
        request: Request, exc: RequestValidationError
    ):
        """处理请求验证错误"""
        error_detail = str(exc)
        logger.error(f"请求验证错误: {error_detail}")
        logger.error(f"请求路径: {request.url.path}")

        return JSONResponse(
            status_code=422,
            content={
                "code": 1,
                "message": "请求参数验证失败",
                "data": {"errors": exc.errors()},
            },
        )

    # 获取数据库会话依赖
    def get_db():
        db = SessionLocal()
        try:
            yield db
        finally:
            db.close()

    # 健康检查端点
    @app.get("/health")
    def health_check():
        return {"status": "ok"}

    logger.info("应用实例创建完成")
    return app  # 确保返回应用实例


# 创建应用实例
app = create_app()

if __name__ == "__main__":
    import uvicorn
    import sys
    import os

    try:
        # 添加项目根目录到Python路径
        ROOT_DIR = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
        sys.path.insert(0, ROOT_DIR)

        # 检测是否是打包环境
        is_packaged = getattr(sys, 'frozen', False)

        # 启动应用
        logger.info("正在启动服务器...")

        if is_packaged:
            # 在打包环境中，直接使用uvicorn.run()函数
            uvicorn.run(
                app,  # 直接传递应用实例而不是字符串
                host="0.0.0.0",
                port=20000,
                log_level="info"
            )
        else:
            # 在开发环境中，使用模块路径启动并启用热重载
            uvicorn.run(
                "app.main:app",
                host="0.0.0.0",
                port=20000,
                reload=True,
                log_level="info",
                access_log=True,
            )
    except Exception as e:
        logger.error(f"服务器启动失败: {str(e)}", exc_info=True)
        sys.exit(1)