"""
智能引导服务
提供交互式用户引导和智能帮助功能
"""

import logging
from typing import Dict, List, Optional, Any, Tuple
from enum import Enum
from datetime import datetime

from telegram import Update, InlineKeyboardButton, InlineKeyboardMarkup
from telegram.ext import ContextTypes
from sqlalchemy.orm import Session

from app.models.telegram_user import TelegramUser
from app.models.telegram_group import TelegramGroup


class GuideType(Enum):
    """引导类型"""
    ONBOARDING = "onboarding"           # 新手引导
    VERIFICATION = "verification"       # 身份验证引导
    QUERY_HELP = "query_help"          # 查询帮助
    ERROR_RECOVERY = "error_recovery"   # 错误恢复
    FEATURE_DISCOVERY = "feature_discovery"  # 功能发现


class UserContext(Enum):
    """用户上下文状态"""
    NEW_USER = "new_user"               # 新用户
    UNBOUND_GROUP = "unbound_group"     # 未绑定群组
    UNVERIFIED_USER = "unverified_user" # 未验证用户
    VERIFIED_USER = "verified_user"     # 已验证用户
    EXPERIENCED_USER = "experienced_user"  # 经验用户


class SmartGuide:
    """智能引导服务"""
    
    def __init__(self, db: Session):
        self.db = db
        self.logger = logging.getLogger(__name__)
        
        # 引导模板
        self.guide_templates = {
            GuideType.ONBOARDING: self._get_onboarding_guide,
            GuideType.VERIFICATION: self._get_verification_guide,
            GuideType.QUERY_HELP: self._get_query_help_guide,
            GuideType.ERROR_RECOVERY: self._get_error_recovery_guide,
            GuideType.FEATURE_DISCOVERY: self._get_feature_discovery_guide
        }
    
    async def get_smart_help(
        self, 
        update: Update, 
        context: ContextTypes.DEFAULT_TYPE,
        guide_type: Optional[GuideType] = None
    ) -> Tuple[str, Optional[InlineKeyboardMarkup]]:
        """
        获取智能帮助内容
        
        Args:
            update: Telegram更新对象
            context: 上下文对象
            guide_type: 指定的引导类型
            
        Returns:
            Tuple[str, Optional[InlineKeyboardMarkup]]: 帮助文本和键盘
        """
        try:
            # 分析用户上下文
            user_context = await self._analyze_user_context(update)
            
            # 确定引导类型
            if guide_type is None:
                guide_type = self._determine_guide_type(user_context)
            
            # 获取引导内容
            guide_func = self.guide_templates.get(guide_type)
            if guide_func:
                return await guide_func(update, context, user_context)
            else:
                return self._get_default_help()
                
        except Exception as e:
            self.logger.error(f"获取智能帮助失败: {e}")
            return self._get_error_help()
    
    async def _analyze_user_context(self, update: Update) -> UserContext:
        """分析用户上下文状态"""
        chat_id = update.effective_chat.id
        user_id = update.effective_user.id
        
        # 检查群组绑定状态
        group = self.db.query(TelegramGroup).filter(
            TelegramGroup.chat_id == chat_id
        ).first()
        
        if not group or not group.is_active():
            return UserContext.UNBOUND_GROUP
        
        # 检查用户验证状态
        telegram_user = self.db.query(TelegramUser).filter(
            TelegramUser.telegram_user_id == user_id
        ).first()
        
        if not telegram_user:
            return UserContext.NEW_USER
        
        if not telegram_user.is_verified():
            return UserContext.UNVERIFIED_USER
        
        # 检查用户经验水平（基于使用次数）
        if telegram_user.command_count and telegram_user.command_count > 50:
            return UserContext.EXPERIENCED_USER
        
        return UserContext.VERIFIED_USER
    
    def _determine_guide_type(self, user_context: UserContext) -> GuideType:
        """根据用户上下文确定引导类型"""
        context_guide_map = {
            UserContext.NEW_USER: GuideType.ONBOARDING,
            UserContext.UNBOUND_GROUP: GuideType.ONBOARDING,
            UserContext.UNVERIFIED_USER: GuideType.VERIFICATION,
            UserContext.VERIFIED_USER: GuideType.QUERY_HELP,
            UserContext.EXPERIENCED_USER: GuideType.FEATURE_DISCOVERY
        }
        
        return context_guide_map.get(user_context, GuideType.ONBOARDING)
    
    async def _get_onboarding_guide(
        self, 
        update: Update, 
        context: ContextTypes.DEFAULT_TYPE, 
        user_context: UserContext
    ) -> Tuple[str, InlineKeyboardMarkup]:
        """获取新手引导"""
        
        if user_context == UserContext.UNBOUND_GROUP:
            text = """🎯 **欢迎使用沃尔玛绑卡统计机器人！**

看起来这是您第一次使用，让我来帮您快速上手：

📋 **第一步：绑定群组**
当前群组还没有绑定到商户系统，需要先完成绑定。

🔧 **您需要做什么**：
1. 联系您的系统管理员
2. 获取群组绑定令牌
3. 使用令牌绑定群组

💡 **小贴士**：只有群组管理员可以执行绑定操作"""

            keyboard = [
                [InlineKeyboardButton("📞 如何联系管理员", callback_data="guide_contact_admin")],
                [InlineKeyboardButton("🔗 了解绑定流程", callback_data="guide_binding_process")],
                [InlineKeyboardButton("❓ 常见问题", callback_data="guide_faq")]
            ]
        else:
            text = """🎯 **欢迎使用沃尔玛绑卡统计机器人！**

我是您的智能助手，可以帮您：

📊 **查询绑卡数据**
• 今日、本周、本月统计
• 自定义时间范围查询
• 详细数据分析

⚙️ **管理群组设置**
• 用户权限管理
• 群组配置调整

🆘 **获取帮助支持**
• 智能问题解答
• 操作指导

💡 **接下来您可以**："""

            keyboard = [
                [InlineKeyboardButton("🚀 开始身份验证", callback_data="guide_start_verification")],
                [InlineKeyboardButton("📊 了解查询功能", callback_data="guide_query_features")],
                [InlineKeyboardButton("🎓 查看使用教程", callback_data="guide_tutorial")]
            ]
        
        return text, InlineKeyboardMarkup(keyboard)
    
    async def _get_verification_guide(
        self, 
        update: Update, 
        context: ContextTypes.DEFAULT_TYPE, 
        user_context: UserContext
    ) -> Tuple[str, InlineKeyboardMarkup]:
        """获取身份验证引导"""
        
        text = """🔐 **身份验证指南**

群组已成功绑定，现在需要验证您的身份才能使用查询功能。

📋 **验证步骤**：
1️⃣ 点击下方"开始验证"按钮
2️⃣ 系统生成您的专属验证令牌
3️⃣ 将令牌发送给管理员
4️⃣ 等待管理员审核通过

⏱️ **预计时间**：通常在30分钟内完成

🔒 **为什么需要验证**：
• 保护数据安全
• 确保授权访问
• 建立用户档案"""

        keyboard = [
            [InlineKeyboardButton("🚀 开始身份验证", callback_data="action_start_verify")],
            [InlineKeyboardButton("📞 联系管理员", callback_data="guide_contact_admin")],
            [InlineKeyboardButton("❓ 验证问题解答", callback_data="guide_verification_faq")],
            [InlineKeyboardButton("📋 查看验证状态", callback_data="action_check_status")]
        ]
        
        return text, InlineKeyboardMarkup(keyboard)
    
    async def _get_query_help_guide(
        self, 
        update: Update, 
        context: ContextTypes.DEFAULT_TYPE, 
        user_context: UserContext
    ) -> Tuple[str, InlineKeyboardMarkup]:
        """获取查询帮助引导"""
        
        text = """📊 **数据查询指南**

恭喜！您已经可以使用所有查询功能了。

🎯 **快速查询**：
• 今日数据：`/stats`
• 本周数据：`/stats_week`
• 本月数据：`/stats_month`

📅 **自定义查询**：
• 指定时间范围：`/stats_custom 开始日期 结束日期`
• 日期格式：YYYY-MM-DD

💡 **使用技巧**：
• 命令不区分大小写
• 可以随时输入 `/help` 获取帮助
• 支持自然语言查询"""

        keyboard = [
            [
                InlineKeyboardButton("📊 今日数据", callback_data="action_stats_today"),
                InlineKeyboardButton("📈 本周数据", callback_data="action_stats_week")
            ],
            [
                InlineKeyboardButton("📅 自定义查询", callback_data="guide_custom_query"),
                InlineKeyboardButton("💡 查询技巧", callback_data="guide_query_tips")
            ],
            [InlineKeyboardButton("🔍 高级功能", callback_data="guide_advanced_features")]
        ]
        
        return text, InlineKeyboardMarkup(keyboard)
    
    async def _get_error_recovery_guide(
        self, 
        update: Update, 
        context: ContextTypes.DEFAULT_TYPE, 
        user_context: UserContext
    ) -> Tuple[str, InlineKeyboardMarkup]:
        """获取错误恢复引导"""
        
        text = """🔧 **遇到问题了？别担心！**

我来帮您快速解决问题：

🔍 **常见问题快速修复**：
• 命令无响应 → 检查命令格式
• 权限不足 → 确认身份验证状态
• 数据查询失败 → 检查日期格式

🆘 **获取帮助**：
• 智能诊断问题
• 一键重置状态
• 联系技术支持"""

        keyboard = [
            [InlineKeyboardButton("🔍 智能诊断", callback_data="action_smart_diagnosis")],
            [InlineKeyboardButton("🔄 重置状态", callback_data="action_reset_status")],
            [InlineKeyboardButton("📞 联系支持", callback_data="guide_contact_support")],
            [InlineKeyboardButton("📚 查看文档", callback_data="guide_documentation")]
        ]
        
        return text, InlineKeyboardMarkup(keyboard)
    
    async def _get_feature_discovery_guide(
        self, 
        update: Update, 
        context: ContextTypes.DEFAULT_TYPE, 
        user_context: UserContext
    ) -> Tuple[str, InlineKeyboardMarkup]:
        """获取功能发现引导"""
        
        text = """🚀 **发现更多功能**

看起来您已经是经验用户了！探索一些高级功能：

⚡ **高级查询**：
• 数据对比分析
• 趋势预测
• 自定义报表

🔧 **管理功能**：
• 群组设置管理
• 用户权限配置
• 自动化规则

🎯 **效率提升**：
• 快捷命令
• 批量操作
• 智能提醒"""

        keyboard = [
            [
                InlineKeyboardButton("📈 高级分析", callback_data="guide_advanced_analytics"),
                InlineKeyboardButton("⚙️ 管理功能", callback_data="guide_admin_features")
            ],
            [
                InlineKeyboardButton("⚡ 快捷操作", callback_data="guide_shortcuts"),
                InlineKeyboardButton("🤖 自动化", callback_data="guide_automation")
            ],
            [InlineKeyboardButton("💡 最佳实践", callback_data="guide_best_practices")]
        ]
        
        return text, InlineKeyboardMarkup(keyboard)
    
    def _get_default_help(self) -> Tuple[str, Optional[InlineKeyboardMarkup]]:
        """获取默认帮助"""
        text = """🤖 **沃尔玛绑卡统计机器人**

我可以帮助您查询绑卡统计数据和管理群组设置。

📋 **基础命令**：
• `/help` - 显示帮助信息
• `/status` - 查看状态
• `/stats` - 查看统计数据

💡 **需要更多帮助？**
输入 `/help` 获取详细指导"""

        return text, None
    
    def _get_error_help(self) -> Tuple[str, Optional[InlineKeyboardMarkup]]:
        """获取错误帮助"""
        text = """❌ **抱歉，获取帮助信息时出现错误**

请尝试：
• 输入 `/help` 重新获取帮助
• 联系系统管理员
• 稍后再试

📞 **技术支持**：如有问题请联系管理员"""

        return text, None


# 创建全局智能引导实例
smart_guide = None

def get_smart_guide(db: Session) -> SmartGuide:
    """获取智能引导实例"""
    global smart_guide
    if smart_guide is None:
        smart_guide = SmartGuide(db)
    return smart_guide
