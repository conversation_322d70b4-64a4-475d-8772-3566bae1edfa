#!/usr/bin/env python3
"""
强制清理Telegram机器人冲突的工具
彻底解决 "Conflict: terminated by other getUpdates request" 错误
"""

import asyncio
import sys
import os
import subprocess
import time
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from app.core.logging import get_logger

logger = get_logger(__name__)


def kill_python_processes():
    """强制终止所有相关的Python进程"""
    logger.info("🔥 强制终止所有相关Python进程...")
    
    try:
        # 获取所有Python进程
        result = subprocess.run([
            'wmic', 'process', 'where', 'name="python.exe"', 
            'get', 'ProcessId,CommandLine'
        ], capture_output=True, text=True, timeout=10)
        
        if result.returncode == 0:
            lines = result.stdout.strip().split('\n')
            processes_to_kill = []
            
            for line in lines:
                if 'walmart-bind-card-server' in line and 'ProcessId' not in line:
                    # 提取进程ID
                    parts = line.strip().split()
                    if parts:
                        try:
                            pid = int(parts[-1])
                            processes_to_kill.append(pid)
                        except (ValueError, IndexError):
                            continue
            
            # 终止进程
            killed_count = 0
            for pid in processes_to_kill:
                try:
                    subprocess.run(['taskkill', '/F', '/PID', str(pid)], 
                                 capture_output=True, timeout=5)
                    logger.info(f"✅ 已终止进程 PID: {pid}")
                    killed_count += 1
                except Exception as e:
                    logger.warning(f"⚠️  终止进程 {pid} 失败: {e}")
            
            if killed_count > 0:
                logger.info(f"✅ 共终止了 {killed_count} 个进程")
                time.sleep(3)  # 等待进程完全终止
            else:
                logger.info("ℹ️  没有发现需要终止的进程")
                
    except Exception as e:
        logger.error(f"❌ 终止进程失败: {e}")


async def force_clear_webhook():
    """强制清除webhook"""
    logger.info("🧹 强制清除Telegram webhook...")
    
    try:
        from app.telegram_bot.config import get_bot_config
        from telegram.ext import Application
        
        config = get_bot_config()
        if not config.bot_token:
            logger.error("❌ Bot token 未配置")
            return False
        
        # 创建应用实例
        application = Application.builder().token(config.bot_token).build()
        await application.initialize()
        
        try:
            # 多次尝试删除webhook
            for attempt in range(3):
                logger.info(f"尝试删除webhook (第{attempt + 1}次)...")
                
                try:
                    await application.bot.delete_webhook(drop_pending_updates=True)
                    await asyncio.sleep(2)
                    
                    # 验证是否删除成功
                    webhook_info = await application.bot.get_webhook_info()
                    if not webhook_info.url:
                        logger.info("✅ Webhook删除成功")
                        return True
                    else:
                        logger.warning(f"⚠️  Webhook仍存在: {webhook_info.url}")
                        
                except Exception as e:
                    logger.warning(f"⚠️  删除webhook失败 (尝试{attempt + 1}): {e}")
                
                if attempt < 2:
                    await asyncio.sleep(5)
            
            logger.error("❌ 无法删除webhook")
            return False
            
        finally:
            await application.shutdown()
            
    except Exception as e:
        logger.error(f"❌ 强制清除webhook失败: {e}")
        return False


def clear_pid_files():
    """清理所有PID文件"""
    logger.info("🗑️  清理PID文件...")
    
    pid_patterns = [
        "*.pid",
        ".telegram_*",
        "telegram_bot.pid",
        "bot_service.pid"
    ]
    
    cleaned_files = []
    for pattern in pid_patterns:
        for pid_file in Path(".").glob(pattern):
            try:
                pid_file.unlink()
                cleaned_files.append(str(pid_file))
            except Exception as e:
                logger.warning(f"删除文件失败 {pid_file}: {e}")
    
    if cleaned_files:
        logger.info(f"✅ 清理了文件: {', '.join(cleaned_files)}")
    else:
        logger.info("ℹ️  没有发现需要清理的PID文件")


async def wait_for_telegram_api():
    """等待Telegram API状态稳定"""
    logger.info("⏳ 等待Telegram API状态稳定...")
    
    try:
        from app.telegram_bot.config import get_bot_config
        from telegram.ext import Application
        
        config = get_bot_config()
        if not config.bot_token:
            return False
        
        # 等待一段时间让Telegram服务器状态稳定
        for i in range(30, 0, -1):
            print(f"\r等待 {i} 秒...", end="", flush=True)
            await asyncio.sleep(1)
        print()
        
        # 测试连接
        application = Application.builder().token(config.bot_token).build()
        await application.initialize()
        
        try:
            bot_info = await application.bot.get_me()
            logger.info(f"✅ API连接正常: @{bot_info.username}")
            return True
        finally:
            await application.shutdown()
            
    except Exception as e:
        logger.error(f"❌ API连接测试失败: {e}")
        return False


async def main():
    """主函数"""
    logger.info("🚨 Telegram机器人冲突强制清理工具")
    logger.info("=" * 60)
    logger.warning("⚠️  此工具将强制终止所有相关进程！")
    
    # 确认操作
    response = input("是否继续? (输入 'YES' 确认): ")
    if response != 'YES':
        logger.info("操作已取消")
        return
    
    # 步骤1: 强制终止进程
    logger.info("\n步骤 1/5: 强制终止进程")
    kill_python_processes()
    
    # 步骤2: 清理PID文件
    logger.info("\n步骤 2/5: 清理PID文件")
    clear_pid_files()
    
    # 步骤3: 强制清除webhook
    logger.info("\n步骤 3/5: 强制清除webhook")
    webhook_cleared = await force_clear_webhook()
    
    # 步骤4: 等待API稳定
    logger.info("\n步骤 4/5: 等待Telegram API稳定")
    api_ready = await wait_for_telegram_api()
    
    # 步骤5: 建议下一步操作
    logger.info("\n步骤 5/5: 完成清理")
    logger.info("=" * 60)
    
    if webhook_cleared and api_ready:
        logger.info("🎉 清理完成！")
        logger.info("💡 建议的下一步操作:")
        logger.info("1. 关闭所有IDE和调试器")
        logger.info("2. 等待2-3分钟")
        logger.info("3. 重新启动应用")
        logger.info("4. 或者考虑使用Webhook模式避免轮询冲突")
    else:
        logger.warning("⚠️  清理过程中遇到问题")
        logger.info("建议:")
        logger.info("1. 检查网络连接")
        logger.info("2. 验证bot token配置")
        logger.info("3. 考虑更换bot token")
        logger.info("4. 使用Webhook模式")


if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        logger.info("操作被用户中断")
    except Exception as e:
        logger.error(f"程序执行失败: {e}")
        sys.exit(1)
