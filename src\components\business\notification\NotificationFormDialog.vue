<template>
    <el-dialog
        v-model="dialogVisible"
        :title="isEdit ? '编辑通知' : '创建通知'"
        width="600px"
        :before-close="handleClose"
    >
        <el-form
            ref="formRef"
            :model="form"
            :rules="rules"
            label-width="100px"
            label-position="left"
        >
            <el-form-item label="接收用户" prop="user_id">
                <el-select
                    v-model="form.user_id"
                    placeholder="请选择接收用户"
                    filterable
                    remote
                    :remote-method="searchUsers"
                    :loading="userLoading"
                    style="width: 100%"
                >
                    <el-option
                        v-for="user in userOptions"
                        :key="user.id"
                        :label="`${user.full_name || user.username} (${user.username})`"
                        :value="user.id"
                    />
                </el-select>
            </el-form-item>

            <el-form-item label="通知标题" prop="title">
                <el-input
                    v-model="form.title"
                    placeholder="请输入通知标题"
                    maxlength="255"
                    show-word-limit
                />
            </el-form-item>

            <el-form-item label="通知内容" prop="content">
                <el-input
                    v-model="form.content"
                    type="textarea"
                    placeholder="请输入通知内容"
                    :rows="4"
                    maxlength="1000"
                    show-word-limit
                />
            </el-form-item>

            <el-form-item label="通知类型" prop="type">
                <el-select v-model="form.type" placeholder="请选择通知类型" style="width: 100%">
                    <el-option label="系统通知" value="SYSTEM" />
                    <el-option label="业务通知" value="BUSINESS" />
                    <el-option label="卡片通知" value="CARD" />
                    <el-option label="商户通知" value="MERCHANT" />
                    <el-option label="用户通知" value="USER" />
                </el-select>
            </el-form-item>

            <el-form-item label="通知状态" prop="status">
                <el-select v-model="form.status" placeholder="请选择通知状态" style="width: 100%">
                    <el-option label="未读" value="UNREAD" />
                    <el-option label="已读" value="READ" />
                </el-select>
            </el-form-item>

            <el-form-item label="相关链接" prop="link">
                <el-input
                    v-model="form.link"
                    placeholder="请输入相关链接（可选）"
                    maxlength="255"
                />
            </el-form-item>

            <el-form-item label="重要程度" prop="is_important">
                <el-switch
                    v-model="form.is_important"
                    active-text="重要"
                    inactive-text="普通"
                />
            </el-form-item>
        </el-form>

        <template #footer>
            <div class="dialog-footer">
                <el-button @click="handleClose">取消</el-button>
                <el-button type="primary" @click="handleSubmit" :loading="submitting">
                    {{ isEdit ? '更新' : '创建' }}
                </el-button>
            </div>
        </template>
    </el-dialog>
</template>

<script setup>
import { ref, computed, watch, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import { notificationApi } from '@/api/modules/notification'
import { userApi } from '@/api/modules/user'

const props = defineProps({
    modelValue: {
        type: Boolean,
        default: false
    },
    notification: {
        type: Object,
        default: null
    }
})

const emit = defineEmits(['update:modelValue', 'success'])

const formRef = ref(null)
const submitting = ref(false)
const userLoading = ref(false)
const userOptions = ref([])

// 对话框显示状态
const dialogVisible = computed({
    get: () => props.modelValue,
    set: (value) => emit('update:modelValue', value)
})

// 是否为编辑模式
const isEdit = computed(() => !!props.notification?.id)

// 表单数据
const form = ref({
    user_id: null,
    title: '',
    content: '',
    type: 'SYSTEM',
    status: 'UNREAD',
    link: '',
    is_important: false
})

// 表单验证规则
const rules = {
    user_id: [
        { required: true, message: '请选择接收用户', trigger: 'change' }
    ],
    title: [
        { required: true, message: '请输入通知标题', trigger: 'blur' },
        { min: 1, max: 255, message: '标题长度应在 1 到 255 个字符之间', trigger: 'blur' }
    ],
    content: [
        { required: true, message: '请输入通知内容', trigger: 'blur' },
        { min: 1, max: 1000, message: '内容长度应在 1 到 1000 个字符之间', trigger: 'blur' }
    ],
    type: [
        { required: true, message: '请选择通知类型', trigger: 'change' }
    ],
    status: [
        { required: true, message: '请选择通知状态', trigger: 'change' }
    ]
}

// 重置表单
const resetForm = () => {
    form.value = {
        user_id: null,
        title: '',
        content: '',
        type: 'SYSTEM',
        status: 'UNREAD',
        link: '',
        is_important: false
    }
    userOptions.value = []
    nextTick(() => {
        formRef.value?.clearValidate()
    })
}

// 监听通知数据变化，初始化表单
watch(() => props.notification, (newVal) => {
    if (newVal) {
        // 编辑模式，填充表单数据
        form.value = {
            user_id: newVal.user_id,
            title: newVal.title,
            content: newVal.content,
            type: newVal.type || 'SYSTEM',
            status: newVal.status || 'UNREAD',
            link: newVal.link || '',
            is_important: newVal.is_important || false
        }

        // 如果有用户ID，加载用户信息
        if (newVal.user_id) {
            loadUserInfo(newVal.user_id)
        }
    } else {
        // 创建模式，重置表单
        resetForm()
    }
}, { immediate: true })

// 搜索用户
const searchUsers = async (query) => {
    if (!query) {
        userOptions.value = []
        return
    }
    
    userLoading.value = true
    try {
        const response = await userApi.getList({
            page: 1,
            page_size: 20,
            search: query
        })
        userOptions.value = response.items || []
    } catch (error) {
        console.error('搜索用户失败:', error)
        ElMessage.error('搜索用户失败')
    } finally {
        userLoading.value = false
    }
}

// 加载用户信息
const loadUserInfo = async (userId) => {
    try {
        const user = await userApi.getDetail(userId)
        userOptions.value = [user]
    } catch (error) {
        console.error('加载用户信息失败:', error)
    }
}

// 提交表单
const handleSubmit = async () => {
    if (!formRef.value) return
    
    try {
        const valid = await formRef.value.validate()
        if (!valid) return
        
        submitting.value = true
        
        if (isEdit.value) {
            // 更新通知
            await notificationApi.update(props.notification.id, form.value)
            ElMessage.success('更新通知成功')
        } else {
            // 创建通知
            await notificationApi.create(form.value)
            ElMessage.success('创建通知成功')
        }
        
        emit('success')
    } catch (error) {
        console.error('操作失败:', error)
        ElMessage.error(error.message || '操作失败')
    } finally {
        submitting.value = false
    }
}

// 关闭对话框
const handleClose = () => {
    dialogVisible.value = false
}
</script>

<style lang="scss" scoped>
.dialog-footer {
    text-align: right;
}

:deep(.el-form-item__label) {
    font-weight: 500;
}

:deep(.el-textarea__inner) {
    resize: vertical;
}
</style>
