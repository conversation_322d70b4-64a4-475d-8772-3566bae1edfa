#!/usr/bin/env python3
"""
调试CK选择过程
详细记录每次选择的过程和结果
"""

import asyncio
import sys
import os
from collections import Counter

# 添加项目路径
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.insert(0, project_root)

from app.api.deps import get_db
from app.models.walmart_ck import WalmartCK
from app.services.redis_ck_wrapper import create_optimized_ck_service
from app.core.logging import get_logger
from app.core.redis import get_redis

logger = get_logger("debug_ck_selection")


async def debug_ck_selection_process(merchant_id=2, rounds=10):
    """调试CK选择过程"""
    print(f"开始调试CK选择过程，商户ID: {merchant_id}, 测试轮数: {rounds}")
    print("="*80)
    
    # 获取数据库连接
    db = next(get_db())
    redis_client = None
    
    try:
        # 连接Redis
        redis_client = await get_redis()
        
        # 检查数据库中的CK状态
        available_cks = db.query(WalmartCK).filter(
            WalmartCK.merchant_id == merchant_id,
            WalmartCK.active == True,
            WalmartCK.is_deleted == False
        ).all()
        
        print(f"数据库中商户 {merchant_id} 的可用CK:")
        for ck in available_cks:
            print(f"  CK {ck.id}: bind_count={ck.bind_count}, total_limit={ck.total_limit}, department_id={ck.department_id}")
        
        # 检查Redis池状态
        pool_key = f"walmart:ck:pool:{merchant_id}"
        pool_exists = await redis_client.exists(pool_key)
        
        print(f"\nRedis池状态:")
        print(f"  池键: {pool_key}")
        print(f"  池存在: {bool(pool_exists)}")
        
        if pool_exists:
            pool_size = await redis_client.zcard(pool_key)
            members = await redis_client.zrange(pool_key, 0, -1, withscores=True)
            print(f"  池大小: {pool_size}")
            print(f"  池成员:")
            for member, score in members:
                ck_id = member.decode() if isinstance(member, bytes) else str(member)
                print(f"    CK {ck_id}: 分数={score}")
                
                # 检查CK状态
                status_key = f"walmart:ck:status:{ck_id}"
                status = await redis_client.hmget(status_key, 
                    'active', 'is_deleted', 'total_limit', 'pending_count', 'bind_count')
                print(f"      状态: active={status[0]}, is_deleted={status[1]}, "
                      f"total_limit={status[2]}, pending_count={status[3]}, bind_count={status[4]}")
        
        # 创建CK服务
        ck_service = create_optimized_ck_service(db)
        
        print(f"\n开始 {rounds} 轮CK选择测试:")
        print("-"*80)
        
        selected_cks = []
        for i in range(rounds):
            print(f"\n第 {i+1} 轮:")
            
            # 选择前检查池状态
            if pool_exists:
                members_before = await redis_client.zrange(pool_key, 0, -1, withscores=True)
                print(f"  选择前池状态: {[(m.decode() if isinstance(m, bytes) else str(m), s) for m, s in members_before]}")
            
            # 选择CK
            ck = await ck_service.get_available_ck(merchant_id)
            
            if ck:
                selected_cks.append(ck.id)
                print(f"  ✅ 选择了CK {ck.id}")
                
                # 检查选择后的池状态
                if pool_exists:
                    members_after = await redis_client.zrange(pool_key, 0, -1, withscores=True)
                    print(f"  选择后池状态: {[(m.decode() if isinstance(m, bytes) else str(m), s) for m, s in members_after]}")
                
                # 立即释放CK
                try:
                    await ck_service.record_ck_usage(ck_id=ck.id, success=False)
                    print(f"  ✅ 已释放CK {ck.id}")
                    
                    # 检查释放后的池状态
                    if pool_exists:
                        members_released = await redis_client.zrange(pool_key, 0, -1, withscores=True)
                        print(f"  释放后池状态: {[(m.decode() if isinstance(m, bytes) else str(m), s) for m, s in members_released]}")
                        
                except Exception as e:
                    print(f"  ❌ 释放CK {ck.id} 失败: {e}")
            else:
                selected_cks.append(None)
                print(f"  ❌ 未找到可用CK")
            
            # 小延迟
            await asyncio.sleep(0.1)
        
        # 分析结果
        print("\n" + "="*80)
        print("测试结果分析:")
        print("="*80)
        
        valid_selections = [ck_id for ck_id in selected_cks if ck_id is not None]
        unique_count = len(set(valid_selections))
        distribution = dict(Counter(valid_selections))
        
        print(f"总测试轮数: {rounds}")
        print(f"成功选择次数: {len(valid_selections)}")
        print(f"选择的不同CK数: {unique_count}")
        print(f"选择序列: {selected_cks}")
        print(f"分布情况: {distribution}")
        
        # 判断负载均衡效果
        if len(available_cks) == 1:
            print("✅ 只有一个CK，选择同一个是正常的")
        elif unique_count > 1:
            print("✅ 负载均衡正常，选择了多个不同的CK")
            
            # 计算均衡分数
            if len(distribution) > 1:
                distribution_values = list(distribution.values())
                max_count = max(distribution_values)
                min_count = min(distribution_values)
                balance_score = (1 - (max_count - min_count) / max_count) * 100
                print(f"均衡分数: {balance_score:.1f}/100")
        else:
            print("❌ 负载均衡异常，只选择了一个CK")
            print("可能的原因:")
            print("  1. Redis池状态不正确")
            print("  2. Lua脚本逻辑问题")
            print("  3. CK状态检查过于严格")
            print("  4. 锁机制干扰")
        
    except Exception as e:
        print(f"❌ 调试失败: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        db.close()
        if redis_client:
            await redis_client.aclose()


async def check_redis_pool_details(merchant_id=2):
    """检查Redis池的详细状态"""
    print(f"检查商户 {merchant_id} 的Redis池详细状态")
    print("="*60)
    
    redis_client = None
    try:
        redis_client = await get_redis()
        
        # 检查主池
        pool_key = f"walmart:ck:pool:{merchant_id}"
        pool_exists = await redis_client.exists(pool_key)
        
        print(f"主池 {pool_key}:")
        print(f"  存在: {bool(pool_exists)}")
        
        if pool_exists:
            # 获取所有成员和分数
            members = await redis_client.zrange(pool_key, 0, -1, withscores=True)
            print(f"  成员数: {len(members)}")
            
            for member, score in members:
                ck_id = member.decode() if isinstance(member, bytes) else str(member)
                print(f"\n  CK {ck_id} (分数: {score}):")
                
                # 检查状态键
                status_key = f"walmart:ck:status:{ck_id}"
                status_exists = await redis_client.exists(status_key)
                print(f"    状态键存在: {bool(status_exists)}")
                
                if status_exists:
                    status = await redis_client.hgetall(status_key)
                    for key, value in status.items():
                        key_str = key.decode() if isinstance(key, bytes) else key
                        value_str = value.decode() if isinstance(value, bytes) else value
                        print(f"    {key_str}: {value_str}")
                
                # 检查锁
                lock_key = f"walmart:ck:lock:{ck_id}"
                lock_exists = await redis_client.exists(lock_key)
                print(f"    锁存在: {bool(lock_exists)}")
                if lock_exists:
                    lock_ttl = await redis_client.ttl(lock_key)
                    print(f"    锁TTL: {lock_ttl}秒")
        
        # 检查部门池
        print(f"\n部门池:")
        dept_pattern = f"walmart:ck:dept:{merchant_id}:*"
        dept_keys = []
        async for key in redis_client.scan_iter(match=dept_pattern):
            key_str = key.decode() if isinstance(key, bytes) else key
            dept_keys.append(key_str)
        
        print(f"  找到 {len(dept_keys)} 个部门池")
        for dept_key in dept_keys:
            dept_size = await redis_client.zcard(dept_key)
            print(f"  {dept_key}: {dept_size} 个成员")
    
    except Exception as e:
        print(f"❌ 检查失败: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        if redis_client:
            await redis_client.aclose()


async def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description="调试CK选择过程")
    parser.add_argument("--merchant-id", type=int, default=2, help="商户ID")
    parser.add_argument("--rounds", type=int, default=10, help="测试轮数")
    parser.add_argument("--check-pool", action="store_true", help="检查Redis池状态")
    
    args = parser.parse_args()
    
    if args.check_pool:
        await check_redis_pool_details(args.merchant_id)
        print("\n" + "="*80 + "\n")
    
    await debug_ck_selection_process(args.merchant_id, args.rounds)


if __name__ == "__main__":
    asyncio.run(main())
