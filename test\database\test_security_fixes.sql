-- ========================================
-- MySQL初始化脚本安全修复验证测试
-- 
-- 目的：验证安全修复是否正确工作
-- 使用方法：在测试环境中执行此脚本
-- ========================================

-- 设置连接字符集
SET NAMES utf8mb4 COLLATE utf8mb4_unicode_ci;
SET time_zone = '+08:00';

USE `walmart_card_db`;

-- ========================================
-- 1. 检查迁移日志表是否存在
-- ========================================
SELECT 
    '=== 迁移日志表检查 ===' as test_section;

SELECT 
    CASE 
        WHEN COUNT(*) > 0 THEN '✓ migration_logs表存在'
        ELSE '✗ migration_logs表不存在'
    END as migration_table_check
FROM information_schema.tables 
WHERE table_schema = 'walmart_card_db' 
AND table_name = 'migration_logs';

-- ========================================
-- 2. 检查安全修复执行状态
-- ========================================
SELECT 
    '=== 安全修复执行状态检查 ===' as test_section;

-- 检查所有修复脚本的执行状态
SELECT 
    migration_name,
    status,
    message,
    created_at,
    completed_at,
    CASE 
        WHEN status = 'completed' THEN '✓ 已完成'
        WHEN status = 'started' THEN '⚠ 进行中'
        ELSE '✗ 异常状态'
    END as status_check
FROM migration_logs 
WHERE migration_name IN (
    'merchant_admin_permissions_fix_v2.1.0',
    'special_menu_cleanup_v2.1.0', 
    'duplicate_merchant_menu_fix_v2.1.0'
)
ORDER BY created_at;

-- ========================================
-- 3. 验证权限修复效果
-- ========================================
SELECT 
    '=== 权限修复效果验证 ===' as test_section;

-- 检查商户管理员权限配置
SELECT 
    '商户管理员权限配置' as check_type,
    r.name as role_name,
    p.code as permission_code,
    p.name as permission_name
FROM roles r
JOIN role_permissions rp ON r.id = rp.role_id
JOIN permissions p ON rp.permission_id = p.id
WHERE r.code = 'merchant_admin' 
AND p.code LIKE 'data:%'
ORDER BY p.code;

-- 检查CK供应商权限配置
SELECT 
    'CK供应商权限配置' as check_type,
    r.name as role_name,
    p.code as permission_code,
    p.name as permission_name
FROM roles r
JOIN role_permissions rp ON r.id = rp.role_id
JOIN permissions p ON rp.permission_id = p.id
WHERE r.code = 'ck_supplier' 
AND p.code LIKE 'data:%'
ORDER BY p.code;

-- ========================================
-- 4. 验证菜单清理效果
-- ========================================
SELECT 
    '=== 菜单清理效果验证 ===' as test_section;

-- 检查特殊页面菜单是否已清理
SELECT 
    '特殊页面菜单检查' as check_type,
    COUNT(*) as remaining_count,
    CASE 
        WHEN COUNT(*) = 0 THEN '✓ 特殊页面菜单已清理'
        ELSE '⚠ 仍有特殊页面菜单存在'
    END as cleanup_status
FROM menus 
WHERE code IN ('error:404', 'error:403', 'login');

-- 检查重复商户菜单是否已清理
SELECT 
    '重复商户菜单检查' as check_type,
    COUNT(*) as remaining_count,
    CASE 
        WHEN COUNT(*) = 0 THEN '✓ 重复商户菜单已清理'
        ELSE '⚠ 仍有重复商户菜单存在'
    END as cleanup_status
FROM menus 
WHERE code = 'system:merchant';

-- ========================================
-- 5. 验证幂等性（重复执行测试）
-- ========================================
SELECT 
    '=== 幂等性验证测试 ===' as test_section;

-- 记录当前状态
SET @before_merchant_permissions = (
    SELECT COUNT(*) 
    FROM role_permissions rp 
    JOIN roles r ON rp.role_id = r.id 
    WHERE r.code = 'merchant_admin'
);

SET @before_ck_permissions = (
    SELECT COUNT(*) 
    FROM role_permissions rp 
    JOIN roles r ON rp.role_id = r.id 
    WHERE r.code = 'ck_supplier'
);

SET @before_special_menus = (
    SELECT COUNT(*) 
    FROM menus 
    WHERE code IN ('error:404', 'error:403', 'login')
);

-- 显示当前状态
SELECT 
    '重复执行前状态' as test_phase,
    @before_merchant_permissions as merchant_permissions,
    @before_ck_permissions as ck_permissions,
    @before_special_menus as special_menus;

-- ========================================
-- 6. 安全性验证总结
-- ========================================
SELECT 
    '=== 安全性验证总结 ===' as test_section;

-- 综合安全检查
SELECT 
    '安全修复综合评估' as evaluation_type,
    CASE 
        WHEN (SELECT COUNT(*) FROM migration_logs WHERE status = 'completed') >= 3 
        THEN '✓ 所有安全修复已完成'
        ELSE '⚠ 部分安全修复未完成'
    END as overall_status,
    (SELECT COUNT(*) FROM migration_logs WHERE status = 'completed') as completed_fixes,
    (SELECT COUNT(*) FROM migration_logs WHERE status != 'completed') as pending_fixes;

-- 数据完整性检查
SELECT 
    '数据完整性检查' as check_type,
    CASE 
        WHEN (SELECT COUNT(*) FROM roles WHERE code IN ('merchant_admin', 'ck_supplier')) = 2
        AND (SELECT COUNT(*) FROM permissions WHERE code LIKE 'data:%') > 0
        THEN '✓ 核心数据完整'
        ELSE '✗ 数据完整性异常'
    END as integrity_status;

-- ========================================
-- 7. 建议和下一步
-- ========================================
SELECT 
    '=== 测试完成建议 ===' as test_section;

SELECT 
    '测试建议' as recommendation_type,
    CASE 
        WHEN (SELECT COUNT(*) FROM migration_logs WHERE status = 'completed') >= 3
        THEN '✓ 安全修复验证通过，可以部署到生产环境'
        ELSE '⚠ 请检查未完成的修复项目，确保所有安全措施都已实施'
    END as deployment_recommendation;

-- 显示详细的修复摘要
SELECT 
    '修复摘要' as summary_type,
    migration_name,
    status,
    JSON_EXTRACT(data_summary, '$.fix_executed') as fix_executed,
    JSON_EXTRACT(data_summary, '$.cleanup_executed') as cleanup_executed,
    completed_at
FROM migration_logs 
WHERE migration_name LIKE '%fix%' OR migration_name LIKE '%cleanup%'
ORDER BY completed_at;

-- ========================================
-- 测试脚本执行完成
-- ========================================
SELECT 
    '=== 安全修复验证测试完成 ===' as final_message,
    NOW(3) as test_completion_time;
