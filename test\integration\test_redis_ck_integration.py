#!/usr/bin/env python3
"""
Redis CK优化集成测试
验证Redis连接、CK选择功能和降级机制的完整集成
"""

import asyncio
import sys
import os
import pytest
from typing import Dict, Any

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.insert(0, project_root)

try:
    from app.db.session import SessionLocal
    from app.core.config import settings
    from app.services.redis_ck_wrapper import create_optimized_ck_service
    from app.models.merchant import Merchant
    from app.models.walmart_ck import WalmartCK
    from app.core.logging import get_logger
except ImportError as e:
    print(f"❌ 导入模块失败: {e}")
    sys.exit(1)

logger = get_logger("test_redis_ck_integration")


class TestRedisCKIntegration:
    """Redis CK优化集成测试类"""
    
    @pytest.mark.asyncio
    async def test_redis_connection(self):
        """测试Redis连接"""
        try:
            from app.core.redis import get_redis
            redis_client = await get_redis()
            await redis_client.ping()
            logger.info("Redis连接正常")
            return True
        except Exception as e:
            logger.warning(f"Redis连接失败: {e}")
            # Redis连接失败不应该导致测试失败，因为有降级机制
            pytest.skip(f"Redis不可用，跳过连接测试: {e}")

    def test_config_loading(self):
        """测试配置加载"""
        redis_optimization = getattr(settings, 'ENABLE_REDIS_CK_OPTIMIZATION', None)
        fallback_db = getattr(settings, 'CK_REDIS_FALLBACK_TO_DB', None)
        max_connections = getattr(settings, 'REDIS_MAX_CONNECTIONS', None)
        
        assert redis_optimization is not None, "ENABLE_REDIS_CK_OPTIMIZATION配置未找到"
        assert fallback_db is not None, "CK_REDIS_FALLBACK_TO_DB配置未找到"
        assert max_connections is not None, "REDIS_MAX_CONNECTIONS配置未找到"
        
        logger.info(f"配置加载成功: Redis优化={redis_optimization}, 降级={fallback_db}, 最大连接={max_connections}")
        return True

    @pytest.mark.asyncio
    async def test_ck_service_creation(self):
        """测试CK服务创建"""
        db = SessionLocal()
        
        try:
            # 创建优化的CK服务
            ck_service = create_optimized_ck_service(db)
            
            assert ck_service is not None, "CK服务创建失败"
            
            # 测试健康检查
            health_result = await ck_service.health_check()
            
            assert isinstance(health_result, dict), "健康检查结果应该是字典"
            assert "redis_enabled" in health_result, "健康检查结果缺少redis_enabled"
            assert "database_service" in health_result, "健康检查结果缺少database_service"
            
            logger.info(f"CK服务创建成功，健康检查结果: {health_result}")
            
            return True, health_result
            
        finally:
            db.close()

    @pytest.mark.asyncio
    async def test_ck_selection_with_fallback(self):
        """测试CK选择和降级机制"""
        db = SessionLocal()
        
        try:
            # 创建优化的CK服务
            ck_service = create_optimized_ck_service(db)
            
            # 查找一个测试商户ID
            test_merchant = db.query(Merchant).first()
            
            if not test_merchant:
                pytest.skip("没有找到测试商户，跳过CK选择测试")
            
            logger.info(f"使用测试商户: {test_merchant.name} (ID: {test_merchant.id})")
            
            # 测试CK选择
            available_ck = await ck_service.get_available_ck(
                merchant_id=test_merchant.id,
                department_id=None
            )
            
            if available_ck:
                logger.info(f"成功选择CK: {available_ck.id} (sign: {available_ck.sign})")
                
                # 测试CK隔离验证
                isolation_valid = ck_service.validate_ck_merchant_isolation(
                    available_ck.id, test_merchant.id
                )
                assert isolation_valid, "CK隔离验证失败"
                logger.info("CK隔离验证通过")
                
            else:
                logger.info("没有找到可用的CK（这可能是正常的）")
            
            return True
            
        finally:
            db.close()

    @pytest.mark.asyncio
    async def test_performance_comparison(self):
        """测试性能对比"""
        db = SessionLocal()
        
        try:
            import time
            from app.services.walmart_ck_service_new import WalmartCKService
            
            # 查找测试商户
            test_merchant = db.query(Merchant).first()
            
            if not test_merchant:
                pytest.skip("没有找到测试商户，跳过性能测试")
            
            # 测试传统方法
            traditional_service = WalmartCKService(db)
            start_time = time.time()
            for _ in range(3):
                await traditional_service.get_available_ck(test_merchant.id)
            traditional_time = time.time() - start_time
            
            # 测试Redis优化方法
            optimized_service = create_optimized_ck_service(db)
            start_time = time.time()
            for _ in range(3):
                await optimized_service.get_available_ck(test_merchant.id)
            optimized_time = time.time() - start_time
            
            logger.info(f"性能对比结果:")
            logger.info(f"  传统方法: {traditional_time:.3f}秒 (3次调用)")
            logger.info(f"  Redis优化: {optimized_time:.3f}秒 (3次调用)")
            
            # 基本断言
            assert traditional_time >= 0, "传统方法执行时间异常"
            assert optimized_time >= 0, "Redis优化方法执行时间异常"
            
            if optimized_time < traditional_time:
                improvement = ((traditional_time - optimized_time) / traditional_time) * 100
                logger.info(f"  性能提升: {improvement:.1f}%")
            else:
                logger.info("  性能未显著提升（可能是因为Redis未启用或数据量小）")
            
            return True
            
        finally:
            db.close()

    @pytest.mark.asyncio
    async def test_full_integration(self):
        """完整集成测试"""
        # 执行所有子测试
        config_result = self.test_config_loading()
        assert config_result, "配置加载测试失败"
        
        try:
            redis_result = await self.test_redis_connection()
        except pytest.skip.Exception:
            redis_result = False
            logger.info("Redis连接测试跳过")
        
        service_result, health_data = await self.test_ck_service_creation()
        assert service_result, "CK服务创建测试失败"
        
        selection_result = await self.test_ck_selection_with_fallback()
        assert selection_result, "CK选择测试失败"
        
        try:
            performance_result = await self.test_performance_comparison()
            assert performance_result, "性能对比测试失败"
        except pytest.skip.Exception:
            performance_result = False
            logger.info("性能对比测试跳过")
        
        logger.info("完整集成测试通过")
        return True


# 独立测试函数（兼容旧版本调用）
async def test_redis_connection():
    """测试Redis连接"""
    test_instance = TestRedisCKIntegration()
    return await test_instance.test_redis_connection()


def test_config_loading():
    """测试配置加载"""
    test_instance = TestRedisCKIntegration()
    return test_instance.test_config_loading()


async def test_ck_service_creation():
    """测试CK服务创建"""
    test_instance = TestRedisCKIntegration()
    return await test_instance.test_ck_service_creation()


async def test_ck_selection_with_fallback():
    """测试CK选择和降级机制"""
    test_instance = TestRedisCKIntegration()
    return await test_instance.test_ck_selection_with_fallback()


async def test_performance_comparison():
    """测试性能对比"""
    test_instance = TestRedisCKIntegration()
    return await test_instance.test_performance_comparison()


async def main():
    """主测试函数（用于直接运行）"""
    print("🚀 开始Redis CK优化集成测试")
    print("=" * 50)
    
    # 测试步骤
    tests = [
        ("配置加载", test_config_loading),
        ("Redis连接", test_redis_connection),
        ("CK服务创建", test_ck_service_creation),
        ("CK选择和降级", test_ck_selection_with_fallback),
        ("性能对比", test_performance_comparison),
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        print(f"\n📋 执行测试: {test_name}")
        try:
            if asyncio.iscoroutinefunction(test_func):
                result = await test_func()
            else:
                result = test_func()
            
            if isinstance(result, tuple):
                success, data = result
                results[test_name] = {"success": success, "data": data}
            else:
                results[test_name] = {"success": result}
                
            print(f"✅ {test_name}: 通过")
        except Exception as e:
            print(f"❌ 测试 {test_name} 失败: {e}")
            results[test_name] = {"success": False, "error": str(e)}
    
    # 输出总结
    print("\n" + "=" * 50)
    print("📊 测试结果总结:")
    
    success_count = 0
    for test_name, result in results.items():
        status = "✅ 通过" if result["success"] else "❌ 失败"
        print(f"   {test_name}: {status}")
        if result["success"]:
            success_count += 1
    
    print(f"\n🎯 总体结果: {success_count}/{len(tests)} 项测试通过")
    
    if success_count == len(tests):
        print("🎉 Redis CK优化集成测试全部通过！")
    elif success_count >= len(tests) - 1:
        print("⚠️ Redis CK优化集成测试基本通过，有少量问题需要关注")
    else:
        print("❌ Redis CK优化集成测试存在问题，需要检查配置和环境")
    
    return results


if __name__ == "__main__":
    # 运行测试
    results = asyncio.run(main())
