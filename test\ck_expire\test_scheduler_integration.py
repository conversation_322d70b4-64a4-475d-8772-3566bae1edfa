"""
定时任务集成测试用例

测试内容：
1. CK过期检测定时任务
2. 任务调度器集成
3. 动态间隔调整
4. 错误处理和恢复
5. 任务执行日志
"""

import pytest
import asyncio
from datetime import datetime, timedelta
from unittest.mock import patch, MagicMock, AsyncMock
from sqlalchemy.orm import Session

from app.services.scheduler_service import SchedulerService
from app.services.ck_expire_service import create_ck_expire_service
from app.models.walmart_ck import WalmartCK
from app.models.system_settings import SystemSettings
from app.crud.system_settings import system_settings
from app.db.session import SessionLocal


class TestSchedulerIntegration:
    """定时任务集成测试类"""

    @pytest.fixture
    def db_session(self):
        """创建测试数据库会话"""
        db = SessionLocal()
        try:
            yield db
        finally:
            db.close()

    @pytest.fixture
    def scheduler_service(self):
        """创建调度器服务实例"""
        service = SchedulerService()
        yield service
        # 清理：停止调度器
        if service.initialized and service.scheduler.running:
            service.scheduler.shutdown()

    @pytest.fixture
    def test_expired_cks(self, db_session):
        """创建测试过期CK数据"""
        expired_cks = []
        
        for i in range(3):
            ck = WalmartCK(
                sign=f"expired_test_ck_{i}",
                daily_limit=20,
                hourly_limit=20,
                merchant_id=1,
                department_id=1,
                active=True,
                is_deleted=False,
                created_at=datetime.now() - timedelta(minutes=60)  # 1小时前创建
            )
            expired_cks.append(ck)
            db_session.add(ck)
        
        db_session.commit()
        return expired_cks

    def test_scheduler_initialization(self, scheduler_service):
        """测试调度器初始化"""
        assert not scheduler_service.initialized
        
        scheduler_service.initialize()
        
        assert scheduler_service.initialized
        assert scheduler_service.scheduler.running
        assert "ck_expire_check" in scheduler_service.jobs

    def test_add_ck_expire_job(self, scheduler_service):
        """测试添加CK过期检测任务"""
        scheduler_service.initialize()
        
        # 验证任务已添加
        jobs = scheduler_service.get_jobs()
        ck_expire_job = None
        
        for job in jobs:
            if job.id == "ck_expire_check":
                ck_expire_job = job
                break
        
        assert ck_expire_job is not None
        assert ck_expire_job.id == "ck_expire_check"

    @pytest.mark.asyncio
    async def test_ck_expire_check_execution(self, scheduler_service, test_expired_cks, db_session):
        """测试CK过期检测任务执行"""
        # 设置过期配置
        system_settings.set_value(db_session, 'ck_expire_minutes', '30')
        system_settings.set_value(db_session, 'ck_expire_check_enabled', 'true')
        
        scheduler_service.initialize()
        
        # 手动执行过期检测任务
        await scheduler_service._run_ck_expire_check()
        
        # 验证过期CK被处理
        for ck in test_expired_cks:
            db_session.refresh(ck)
            assert ck.is_deleted is True
            assert ck.active is False

    @pytest.mark.asyncio
    async def test_ck_expire_check_disabled(self, scheduler_service, test_expired_cks, db_session):
        """测试禁用过期检测时的行为"""
        # 禁用过期检测
        system_settings.set_value(db_session, 'ck_expire_check_enabled', 'false')
        
        scheduler_service.initialize()
        
        # 手动执行过期检测任务
        await scheduler_service._run_ck_expire_check()
        
        # 验证CK未被处理
        for ck in test_expired_cks:
            db_session.refresh(ck)
            assert ck.is_deleted is False
            assert ck.active is True

    def test_update_job_interval(self, scheduler_service):
        """测试动态更新任务间隔"""
        scheduler_service.initialize()
        
        # 获取初始任务
        initial_job = scheduler_service.jobs.get("ck_expire_check")
        assert initial_job is not None
        
        # 更新间隔
        scheduler_service._update_ck_expire_job_interval(15)
        
        # 验证任务已更新
        updated_job = scheduler_service.jobs.get("ck_expire_check")
        assert updated_job is not None
        # 注意：由于任务被替换，对象引用会改变
        assert updated_job.trigger.interval.total_seconds() == 15 * 60

    @pytest.mark.asyncio
    async def test_error_handling_in_task(self, scheduler_service, db_session):
        """测试任务执行中的错误处理"""
        scheduler_service.initialize()
        
        # 模拟数据库错误
        with patch('app.db.session.SessionLocal') as mock_session:
            mock_session.side_effect = Exception("数据库连接失败")
            
            # 执行任务不应该抛出异常
            try:
                await scheduler_service._run_ck_expire_check()
            except Exception as e:
                pytest.fail(f"任务执行不应该抛出异常: {e}")

    def test_job_management(self, scheduler_service):
        """测试任务管理功能"""
        scheduler_service.initialize()
        
        # 验证任务存在
        assert "ck_expire_check" in scheduler_service.jobs
        
        # 移除任务
        result = scheduler_service.remove_job("ck_expire_check")
        assert result is True
        assert "ck_expire_check" not in scheduler_service.jobs
        
        # 尝试移除不存在的任务
        result = scheduler_service.remove_job("non_existent_job")
        assert result is False

    @pytest.mark.asyncio
    async def test_concurrent_task_execution(self, scheduler_service, db_session):
        """测试并发任务执行"""
        scheduler_service.initialize()
        
        # 创建多个并发任务
        tasks = []
        for _ in range(3):
            task = asyncio.create_task(scheduler_service._run_ck_expire_check())
            tasks.append(task)
        
        # 等待所有任务完成
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # 验证所有任务都成功完成（没有异常）
        for result in results:
            assert not isinstance(result, Exception)

    def test_scheduler_shutdown(self, scheduler_service):
        """测试调度器关闭"""
        scheduler_service.initialize()
        assert scheduler_service.scheduler.running
        
        # 关闭调度器
        scheduler_service.scheduler.shutdown()
        assert not scheduler_service.scheduler.running


class TestCKExpireServiceSchedulerIntegration:
    """CK过期服务与调度器集成测试"""

    @pytest.fixture
    def db_session(self):
        """创建测试数据库会话"""
        db = SessionLocal()
        try:
            yield db
        finally:
            db.close()

    @pytest.mark.asyncio
    async def test_full_integration_flow(self, db_session):
        """测试完整集成流程"""
        # 1. 创建过期CK
        expired_ck = WalmartCK(
            sign="integration_test_ck",
            daily_limit=20,
            hourly_limit=20,
            merchant_id=1,
            department_id=1,
            active=True,
            is_deleted=False,
            created_at=datetime.now() - timedelta(minutes=60)
        )
        db_session.add(expired_ck)
        db_session.commit()
        
        # 2. 设置过期配置
        system_settings.set_value(db_session, 'ck_expire_minutes', '30')
        system_settings.set_value(db_session, 'ck_expire_check_enabled', 'true')
        system_settings.set_value(db_session, 'ck_expire_check_interval', '5')
        
        # 3. 创建服务并执行检测
        ck_expire_service = create_ck_expire_service(db_session)
        result = ck_expire_service.batch_expire_cks()
        
        # 4. 验证结果
        assert result['total_found'] == 1
        assert result['total_processed'] == 1
        assert result['total_failed'] == 0
        
        # 5. 验证CK状态
        db_session.refresh(expired_ck)
        assert expired_ck.is_deleted is True
        assert expired_ck.active is False

    @pytest.mark.asyncio
    async def test_configuration_change_impact(self, db_session):
        """测试配置变更对任务执行的影响"""
        # 创建过期CK
        expired_ck = WalmartCK(
            sign="config_test_ck",
            daily_limit=20,
            hourly_limit=20,
            merchant_id=1,
            department_id=1,
            active=True,
            is_deleted=False,
            created_at=datetime.now() - timedelta(minutes=60)
        )
        db_session.add(expired_ck)
        db_session.commit()
        
        # 设置较长的过期时间（不会过期）
        system_settings.set_value(db_session, 'ck_expire_minutes', '120')
        system_settings.set_value(db_session, 'ck_expire_check_enabled', 'true')
        
        ck_expire_service = create_ck_expire_service(db_session)
        result = ck_expire_service.batch_expire_cks()
        
        # 验证CK未过期
        assert result['total_found'] == 0
        db_session.refresh(expired_ck)
        assert expired_ck.is_deleted is False
        
        # 更改配置为较短的过期时间
        system_settings.set_value(db_session, 'ck_expire_minutes', '30')
        
        result = ck_expire_service.batch_expire_cks()
        
        # 验证CK现在过期了
        assert result['total_found'] == 1
        assert result['total_processed'] == 1
        db_session.refresh(expired_ck)
        assert expired_ck.is_deleted is True

    def test_performance_with_large_dataset(self, db_session):
        """测试大数据集的性能"""
        # 创建大量CK数据
        cks = []
        for i in range(100):
            ck = WalmartCK(
                sign=f"perf_test_ck_{i}",
                daily_limit=20,
                hourly_limit=20,
                merchant_id=1,
                department_id=1,
                active=True,
                is_deleted=False,
                created_at=datetime.now() - timedelta(minutes=60 if i % 2 == 0 else 10)
            )
            cks.append(ck)
            db_session.add(ck)
        
        db_session.commit()
        
        # 设置过期配置
        system_settings.set_value(db_session, 'ck_expire_minutes', '30')
        system_settings.set_value(db_session, 'ck_expire_check_enabled', 'true')
        
        # 执行批量处理并测量时间
        import time
        start_time = time.time()
        
        ck_expire_service = create_ck_expire_service(db_session)
        result = ck_expire_service.batch_expire_cks()
        
        end_time = time.time()
        execution_time = end_time - start_time
        
        # 验证结果
        assert result['total_found'] == 50  # 一半的CK过期
        assert result['total_processed'] == 50
        assert execution_time < 10  # 应该在10秒内完成
        
        print(f"处理100个CK用时: {execution_time:.2f}秒")


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
