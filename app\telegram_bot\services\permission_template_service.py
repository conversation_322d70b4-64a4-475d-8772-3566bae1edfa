"""
Telegram权限模板服务
用于管理和应用权限模板，简化权限配置
"""

from typing import Dict, List, Optional, Any
from sqlalchemy.orm import Session
from sqlalchemy import and_

from app.models.telegram_permission_template import TelegramPermissionTemplate
from app.models.telegram_user import TelegramUser
from app.models.user import User
from app.models.role import Role
from app.models.audit import AuditLog, AuditEventType, AuditLevel
from app.core.logging import get_logger

logger = get_logger(__name__)


class PermissionTemplateService:
    """权限模板服务"""
    
    def __init__(self, db: Session):
        self.db = db
    
    def create_default_templates(self) -> List[TelegramPermissionTemplate]:
        """创建默认权限模板"""
        default_templates = [
            {
                "template_name": "普通员工",
                "template_code": "employee",
                "description": "普通员工权限模板，只能查看基础信息",
                "settings": {
                    "permissions": {
                        "allowed_commands": ["/help", "/status", "/query"],
                        "admin_only_commands": [],
                        "rate_limits": {
                            "commands_per_minute": 10,
                            "queries_per_hour": 50
                        }
                    },
                    "display_settings": {
                        "show_sensitive_data": False,
                        "max_results_per_query": 20
                    },
                    "notification_settings": {
                        "receive_alerts": False,
                        "receive_reports": False
                    }
                },
                "is_system": True
            },
            {
                "template_name": "部门主管",
                "template_code": "department_manager",
                "description": "部门主管权限模板，可以管理本部门数据",
                "settings": {
                    "permissions": {
                        "allowed_commands": ["/help", "/status", "/query", "/report", "/export"],
                        "admin_only_commands": [],
                        "rate_limits": {
                            "commands_per_minute": 20,
                            "queries_per_hour": 200
                        }
                    },
                    "display_settings": {
                        "show_sensitive_data": True,
                        "max_results_per_query": 100
                    },
                    "notification_settings": {
                        "receive_alerts": True,
                        "receive_reports": True
                    }
                },
                "is_system": True
            },
            {
                "template_name": "系统管理员",
                "template_code": "system_admin",
                "description": "系统管理员权限模板，拥有完整权限",
                "settings": {
                    "permissions": {
                        "allowed_commands": ["*"],  # 所有命令
                        "admin_only_commands": ["/bind", "/unbind", "/settings", "/config"],
                        "rate_limits": {
                            "commands_per_minute": 100,
                            "queries_per_hour": 1000
                        }
                    },
                    "display_settings": {
                        "show_sensitive_data": True,
                        "max_results_per_query": 500
                    },
                    "notification_settings": {
                        "receive_alerts": True,
                        "receive_reports": True,
                        "receive_system_notifications": True
                    }
                },
                "is_system": True
            }
        ]
        
        created_templates = []
        for template_data in default_templates:
            # 检查是否已存在
            existing = self.db.query(TelegramPermissionTemplate).filter_by(
                template_code=template_data["template_code"]
            ).first()
            
            if not existing:
                template = TelegramPermissionTemplate(**template_data)
                self.db.add(template)
                created_templates.append(template)
        
        self.db.commit()
        return created_templates
    
    def get_template_by_role(self, role_name: str) -> Optional[TelegramPermissionTemplate]:
        """根据角色名称获取推荐的权限模板"""
        role_template_mapping = {
            "employee": "employee",
            "manager": "department_manager",
            "admin": "system_admin",
            "super_admin": "system_admin"
        }
        
        template_code = role_template_mapping.get(role_name.lower())
        if template_code:
            return self.db.query(TelegramPermissionTemplate).filter_by(
                template_code=template_code,
                is_active=True
            ).first()
        
        return None
    
    def apply_template_to_user(
        self, 
        telegram_user_id: int, 
        template_id: int,
        applied_by: int
    ) -> bool:
        """
        将权限模板应用到用户
        
        Args:
            telegram_user_id: Telegram用户ID
            template_id: 模板ID
            applied_by: 操作人ID
            
        Returns:
            bool: 是否应用成功
        """
        try:
            # 获取用户和模板
            telegram_user = self.db.query(TelegramUser).filter_by(
                id=telegram_user_id
            ).first()
            
            if not telegram_user:
                raise ValueError("Telegram用户不存在")
            
            template = self.db.query(TelegramPermissionTemplate).filter_by(
                id=template_id,
                is_active=True
            ).first()
            
            if not template:
                raise ValueError("权限模板不存在或已禁用")
            
            # 应用模板设置
            # 这里可以根据模板设置更新用户的权限配置
            # 例如：更新用户角色、权限等
            
            # 记录审计日志
            self._log_template_application(
                telegram_user_id, template_id, applied_by
            )
            
            logger.info(f"权限模板 {template.template_name} 已应用到用户 {telegram_user.telegram_user_id}")
            return True
            
        except Exception as e:
            logger.error(f"应用权限模板失败: {e}")
            self.db.rollback()
            raise
    
    def batch_apply_template(
        self, 
        user_ids: List[int], 
        template_id: int,
        applied_by: int
    ) -> Dict[str, Any]:
        """
        批量应用权限模板
        
        Args:
            user_ids: 用户ID列表
            template_id: 模板ID
            applied_by: 操作人ID
            
        Returns:
            Dict: 批量操作结果
        """
        results = {
            "success_count": 0,
            "failed_count": 0,
            "errors": []
        }
        
        for user_id in user_ids:
            try:
                self.apply_template_to_user(user_id, template_id, applied_by)
                results["success_count"] += 1
                
            except Exception as e:
                results["failed_count"] += 1
                results["errors"].append({
                    "user_id": user_id,
                    "error": str(e)
                })
        
        return results
    
    def create_custom_template(
        self, 
        template_data: Dict[str, Any],
        created_by: int
    ) -> TelegramPermissionTemplate:
        """
        创建自定义权限模板
        
        Args:
            template_data: 模板数据
            created_by: 创建人ID
            
        Returns:
            TelegramPermissionTemplate: 创建的模板
        """
        try:
            # 验证模板数据
            self._validate_template_data(template_data)
            
            template = TelegramPermissionTemplate(
                template_name=template_data["template_name"],
                template_code=template_data["template_code"],
                description=template_data.get("description"),
                settings=template_data["settings"],
                is_system=False,
                created_by=created_by
            )
            
            self.db.add(template)
            self.db.commit()
            
            logger.info(f"创建自定义权限模板: {template.template_name}")
            return template
            
        except Exception as e:
            logger.error(f"创建权限模板失败: {e}")
            self.db.rollback()
            raise
    
    def _validate_template_data(self, template_data: Dict[str, Any]):
        """验证模板数据格式"""
        required_fields = ["template_name", "template_code", "settings"]
        for field in required_fields:
            if field not in template_data:
                raise ValueError(f"缺少必需字段: {field}")
        
        # 验证设置结构
        settings = template_data["settings"]
        if not isinstance(settings, dict):
            raise ValueError("settings必须是字典类型")
        
        # 验证权限设置
        if "permissions" in settings:
            permissions = settings["permissions"]
            if "allowed_commands" not in permissions:
                raise ValueError("权限设置中缺少allowed_commands")
    
    def _log_template_application(
        self, 
        telegram_user_id: int, 
        template_id: int, 
        applied_by: int
    ):
        """记录模板应用日志"""
        try:
            audit_log = AuditLog(
                event_type=AuditEventType.PERMISSION_CHANGE.value,
                level=AuditLevel.INFO.value,
                resource_type="telegram_user",
                resource_id=str(telegram_user_id),
                action="apply_permission_template",
                details={
                    "template_id": template_id,
                    "applied_by": applied_by
                },
                user_id=applied_by
            )
            self.db.add(audit_log)
            self.db.commit()
            
        except Exception as e:
            logger.error(f"记录模板应用日志失败: {e}")
    
    def get_user_effective_permissions(
        self, 
        telegram_user: TelegramUser
    ) -> Dict[str, Any]:
        """
        获取用户的有效权限配置
        
        Args:
            telegram_user: Telegram用户对象
            
        Returns:
            Dict: 有效权限配置
        """
        if not telegram_user.system_user:
            return self._get_default_permissions()
        
        # 根据用户角色获取权限模板
        user_roles = telegram_user.system_user.roles
        if not user_roles:
            return self._get_default_permissions()
        
        # 获取最高权限的角色对应的模板
        highest_role = max(user_roles, key=lambda r: self._get_role_priority(r.name))
        template = self.get_template_by_role(highest_role.name)
        
        if template:
            return template.settings
        
        return self._get_default_permissions()
    
    def _get_role_priority(self, role_name: str) -> int:
        """获取角色优先级"""
        priority_map = {
            "super_admin": 100,
            "admin": 80,
            "manager": 60,
            "employee": 40
        }
        return priority_map.get(role_name.lower(), 0)
    
    def _get_default_permissions(self) -> Dict[str, Any]:
        """获取默认权限配置"""
        return {
            "permissions": {
                "allowed_commands": ["/help"],
                "admin_only_commands": [],
                "rate_limits": {
                    "commands_per_minute": 5,
                    "queries_per_hour": 10
                }
            },
            "display_settings": {
                "show_sensitive_data": False,
                "max_results_per_query": 10
            },
            "notification_settings": {
                "receive_alerts": False,
                "receive_reports": False
            }
        }
