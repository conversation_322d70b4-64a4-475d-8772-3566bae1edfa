#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
跨边界数据操作测试
全面测试跨商户、跨部门、跨角色的数据操作权限控制
"""

import sys
import os
import time
import random
import string

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.dirname(__file__))))

from test.conftest import TestBase, get_test_accounts, format_test_result, print_test_summary

class CrossBoundaryOperationsTestSuite(TestBase):
    """跨边界数据操作测试套件"""
    
    def __init__(self):
        super().__init__()
        self.results = []
        self.test_accounts = get_test_accounts()
        self.admin_token = None
        self.merchant_a_token = None
        self.merchant_b_token = None
        self.created_test_data = {
            "users": [],
            "merchants": [],
            "departments": []
        }
    
    def setup(self):
        """测试前置设置"""
        print("=== 测试前置设置 ===")
        
        # 管理员登录
        self.admin_token = self.login("admin", "7c222fb2927d828af22f592134e8932480637c0d")
        if not self.admin_token:
            print("❌ 管理员登录失败")
            return False
        
        # 商户A管理员登录
        self.merchant_a_token = self.login("test1", "********")
        if not self.merchant_a_token:
            print("❌ 商户A管理员登录失败")
            return False
        
        # 商户B管理员登录
        self.merchant_b_token = self.login("test_merchant_b", "********")
        if not self.merchant_b_token:
            print("⚠️ 商户B管理员登录失败，将跳过部分测试")
        
        print("✅ 测试前置设置完成")
        return True
    
    def generate_test_data(self, prefix: str, data_type: str) -> dict:
        """生成测试数据"""
        timestamp = int(time.time())
        random_suffix = ''.join(random.choices(string.ascii_lowercase, k=4))
        
        if data_type == "user":
            return {
                "username": f"{prefix}_user_{timestamp}_{random_suffix}",
                "password": "Test123456!",
                "full_name": f"测试用户_{prefix}_{timestamp}",
                "email": f"test_{prefix}_{timestamp}@example.com",
                "is_active": True
            }
        elif data_type == "merchant":
            return {
                "name": f"{prefix}_商户_{timestamp}",
                "code": f"{prefix.upper()}_{timestamp}_{random_suffix}",
                "api_key": f"api_key_{timestamp}_{random_suffix}",
                "api_secret": f"api_secret_{timestamp}_{random_suffix}",
                "status": 1,
                "daily_limit": 1000,
                "hourly_limit": 100,
                "contact_name": f"联系人_{timestamp}",
                "contact_phone": "13800138000",
                "contact_email": f"contact_{timestamp}@example.com"
            }
        elif data_type == "department":
            return {
                "name": f"{prefix}_部门_{timestamp}",
                "code": f"{prefix.upper()}_{timestamp}_{random_suffix}",
                "description": f"测试部门_{prefix}_{timestamp}",
                "status": True,  # 使用布尔值
                "manager_name": f"部门经理_{timestamp}",
                "manager_phone": "13800138000",
                "manager_email": f"manager_{timestamp}@example.com",
                "merchant_id": 1  # 默认使用商户ID 1，实际使用时应该动态获取
            }
    
    def test_cross_merchant_create_operations(self):
        """测试跨商户CREATE操作"""
        print("\n=== 测试跨商户CREATE操作 ===")
        
        if not self.merchant_a_token:
            return
        
        # 测试商户A用户尝试创建用户（应该被限制到自己的商户）
        test_user = self.generate_test_data("cross_merchant", "user")
        status_code, response = self.make_request(
            "POST", "/users", self.merchant_a_token, data=test_user
        )
        
        if status_code in [403, 401]:
            self.results.append(format_test_result(
                "跨商户创建用户权限控制",
                True,
                "正确阻止商户用户创建用户"
            ))
            print("✅ 正确阻止商户用户创建用户")
        elif status_code in [200, 201]:
            # 如果允许创建，检查是否只能在自己的商户下创建
            user_data = response.get("data", response)
            created_user_id = user_data.get("id")
            if created_user_id:
                self.created_test_data["users"].append(created_user_id)
                
                # 验证创建的用户是否属于正确的商户
                self.results.append(format_test_result(
                    "跨商户创建用户商户归属验证",
                    True,
                    "商户用户创建用户成功，需验证商户归属"
                ))
                print("✅ 商户用户创建用户成功，需验证商户归属")
        else:
            self.results.append(format_test_result(
                "跨商户创建用户",
                False,
                f"创建用户失败，状态码: {status_code}"
            ))
            print(f"❌ 创建用户失败，状态码: {status_code}")
        
        # 测试商户A用户尝试创建商户（应该被拒绝）
        test_merchant = self.generate_test_data("cross_merchant", "merchant")
        status_code, response = self.make_request(
            "POST", "/merchants", self.merchant_a_token, data=test_merchant
        )
        
        if status_code in [403, 401]:
            self.results.append(format_test_result(
                "跨商户创建商户权限控制",
                True,
                "正确阻止商户用户创建商户"
            ))
            print("✅ 正确阻止商户用户创建商户")
        else:
            self.results.append(format_test_result(
                "跨商户创建商户权限控制",
                False,
                f"商户用户不应该能创建商户，状态码: {status_code}"
            ))
            print(f"❌ 商户用户不应该能创建商户，状态码: {status_code}")
    
    def test_cross_merchant_update_operations(self):
        """测试跨商户UPDATE操作"""
        print("\n=== 测试跨商户UPDATE操作 ===")
        
        if not self.admin_token or not self.merchant_a_token:
            return
        
        # 先获取所有用户列表（使用管理员权限）
        status_code, response = self.make_request("GET", "/users", self.admin_token)
        if status_code != 200:
            self.results.append(format_test_result(
                "跨商户UPDATE测试前置条件",
                False,
                "无法获取用户列表"
            ))
            return
        
        users_data = response.get("data", {})
        users = users_data.get("items", []) if isinstance(users_data, dict) else response.get("items", [])
        
        if not users:
            self.results.append(format_test_result(
                "跨商户UPDATE测试前置条件",
                False,
                "用户列表为空"
            ))
            return
        
        # 尝试让商户A用户修改其他用户的信息
        for user in users[:3]:  # 只测试前3个用户
            user_id = user.get("id")
            if not user_id:
                continue
            
            update_data = {
                "full_name": f"恶意修改_{int(time.time())}",
                "email": f"hacked_{int(time.time())}@evil.com"
            }
            
            status_code, response = self.make_request(
                "PUT", f"/users/{user_id}", self.merchant_a_token, data=update_data
            )
            
            if status_code in [403, 401, 404]:
                self.results.append(format_test_result(
                    f"跨商户修改用户权限控制_{user_id}",
                    True,
                    f"正确阻止商户用户修改用户{user_id}"
                ))
                print(f"✅ 正确阻止商户用户修改用户{user_id}")
            elif status_code == 200:
                # 如果修改成功，检查是否只能修改自己商户的用户
                self.results.append(format_test_result(
                    f"跨商户修改用户权限验证_{user_id}",
                    False,
                    f"商户用户可能修改了其他商户的用户{user_id}"
                ))
                print(f"⚠️ 商户用户可能修改了其他商户的用户{user_id}")
            else:
                self.results.append(format_test_result(
                    f"跨商户修改用户测试_{user_id}",
                    True,
                    f"修改用户{user_id}失败，状态码: {status_code}"
                ))
                print(f"✅ 修改用户{user_id}失败，状态码: {status_code}")
    
    def test_cross_merchant_delete_operations(self):
        """测试跨商户DELETE操作"""
        print("\n=== 测试跨商户DELETE操作 ===")
        
        if not self.admin_token or not self.merchant_a_token:
            return
        
        # 先获取所有商户列表（使用管理员权限）
        status_code, response = self.make_request("GET", "/merchants", self.admin_token)
        if status_code != 200:
            self.results.append(format_test_result(
                "跨商户DELETE测试前置条件",
                False,
                "无法获取商户列表"
            ))
            return
        
        merchants_data = response.get("data", {})
        merchants = merchants_data.get("items", []) if isinstance(merchants_data, dict) else response.get("items", [])
        
        if len(merchants) < 2:
            self.results.append(format_test_result(
                "跨商户DELETE测试前置条件",
                False,
                f"商户数量不足（{len(merchants)}），无法测试跨商户删除"
            ))
            return
        
        # 尝试让商户A用户删除其他商户
        for merchant in merchants[:2]:  # 只测试前2个商户
            merchant_id = merchant.get("id")
            if not merchant_id:
                continue
            
            status_code, response = self.make_request(
                "DELETE", f"/merchants/{merchant_id}", self.merchant_a_token
            )
            
            if status_code in [403, 401, 404]:
                self.results.append(format_test_result(
                    f"跨商户删除商户权限控制_{merchant_id}",
                    True,
                    f"正确阻止商户用户删除商户{merchant_id}"
                ))
                print(f"✅ 正确阻止商户用户删除商户{merchant_id}")
            elif status_code in [200, 204]:
                self.results.append(format_test_result(
                    f"跨商户删除商户权限控制_{merchant_id}",
                    False,
                    f"商户用户不应该能删除商户{merchant_id}"
                ))
                print(f"❌ 商户用户不应该能删除商户{merchant_id}")
            else:
                self.results.append(format_test_result(
                    f"跨商户删除商户测试_{merchant_id}",
                    True,
                    f"删除商户{merchant_id}失败，状态码: {status_code}"
                ))
                print(f"✅ 删除商户{merchant_id}失败，状态码: {status_code}")
    
    def test_cross_department_operations(self):
        """测试跨部门数据操作"""
        print("\n=== 测试跨部门数据操作 ===")
        
        if not self.merchant_a_token:
            return
        
        # 获取部门列表
        status_code, response = self.make_request("GET", "/departments", self.merchant_a_token)
        
        if status_code == 200:
            departments_data = response.get("data", {})
            departments = departments_data.get("items", []) if isinstance(departments_data, dict) else response.get("items", [])
            
            if len(departments) >= 2:
                # 测试跨部门访问
                for i, dept in enumerate(departments[:2]):
                    dept_id = dept.get("id")
                    if not dept_id:
                        continue
                    
                    # 尝试修改其他部门信息
                    update_data = {
                        "name": f"恶意修改部门_{int(time.time())}",
                        "description": "跨部门恶意修改"
                    }
                    
                    status_code, response = self.make_request(
                        "PUT", f"/departments/{dept_id}", self.merchant_a_token, data=update_data
                    )
                    
                    if status_code in [403, 401]:
                        self.results.append(format_test_result(
                            f"跨部门修改权限控制_{dept_id}",
                            True,
                            f"正确阻止跨部门修改部门{dept_id}"
                        ))
                        print(f"✅ 正确阻止跨部门修改部门{dept_id}")
                    elif status_code == 200:
                        self.results.append(format_test_result(
                            f"跨部门修改权限验证_{dept_id}",
                            False,
                            f"可能存在跨部门修改漏洞，部门{dept_id}"
                        ))
                        print(f"⚠️ 可能存在跨部门修改漏洞，部门{dept_id}")
                    else:
                        self.results.append(format_test_result(
                            f"跨部门修改测试_{dept_id}",
                            True,
                            f"修改部门{dept_id}失败，状态码: {status_code}"
                        ))
                        print(f"✅ 修改部门{dept_id}失败，状态码: {status_code}")
            else:
                self.results.append(format_test_result(
                    "跨部门测试前置条件",
                    False,
                    f"部门数量不足（{len(departments)}），无法测试跨部门操作"
                ))
                print(f"⚠️ 部门数量不足（{len(departments)}），无法测试跨部门操作")
        elif status_code in [403, 401]:
            self.results.append(format_test_result(
                "跨部门测试权限控制",
                True,
                "正确限制商户访问部门列表"
            ))
            print("✅ 正确限制商户访问部门列表")
        else:
            self.results.append(format_test_result(
                "跨部门测试前置条件",
                False,
                f"获取部门列表失败，状态码: {status_code}"
            ))
            print(f"❌ 获取部门列表失败，状态码: {status_code}")
    
    def test_role_privilege_escalation(self):
        """测试角色权限提升攻击"""
        print("\n=== 测试角色权限提升攻击 ===")
        
        if not self.merchant_a_token:
            return
        
        # 测试商户用户尝试执行管理员操作
        admin_operations = [
            ("POST", "/roles", {"name": "恶意角色", "code": "evil_role", "description": "权限提升测试"}),
            ("POST", "/permissions", {"code": "evil:all", "name": "恶意权限", "description": "权限提升测试"}),
            ("PUT", "/system/params", {"key": "evil_param", "value": "hacked"}),
        ]
        
        for method, endpoint, data in admin_operations:
            status_code, response = self.make_request(method, endpoint, self.merchant_a_token, data=data)
            
            if status_code in [403, 401, 404]:
                self.results.append(format_test_result(
                    f"权限提升防护_{method}_{endpoint}",
                    True,
                    f"正确阻止权限提升攻击: {method} {endpoint}"
                ))
                print(f"✅ 正确阻止权限提升攻击: {method} {endpoint}")
            elif status_code in [200, 201]:
                self.results.append(format_test_result(
                    f"权限提升防护_{method}_{endpoint}",
                    False,
                    f"检测到权限提升漏洞: {method} {endpoint}"
                ))
                print(f"🚨 检测到权限提升漏洞: {method} {endpoint}")
            else:
                self.results.append(format_test_result(
                    f"权限提升测试_{method}_{endpoint}",
                    True,
                    f"权限提升攻击失败，状态码: {status_code}"
                ))
                print(f"✅ 权限提升攻击失败，状态码: {status_code}")
    
    def cleanup(self):
        """清理测试数据"""
        print("\n=== 清理测试数据 ===")
        
        if not self.admin_token:
            return
        
        # 清理创建的用户
        for user_id in self.created_test_data["users"]:
            status_code, _ = self.make_request("DELETE", f"/users/{user_id}", self.admin_token)
            if status_code in [200, 204]:
                print(f"✅ 清理测试用户: {user_id}")
            else:
                print(f"⚠️ 清理测试用户失败: {user_id}")
        
        # 清理创建的商户
        for merchant_id in self.created_test_data["merchants"]:
            status_code, _ = self.make_request("DELETE", f"/merchants/{merchant_id}", self.admin_token)
            if status_code in [200, 204]:
                print(f"✅ 清理测试商户: {merchant_id}")
            else:
                print(f"⚠️ 清理测试商户失败: {merchant_id}")
        
        # 清理创建的部门
        for dept_id in self.created_test_data["departments"]:
            status_code, _ = self.make_request("DELETE", f"/departments/{dept_id}", self.admin_token)
            if status_code in [200, 204]:
                print(f"✅ 清理测试部门: {dept_id}")
            else:
                print(f"⚠️ 清理测试部门失败: {dept_id}")
    
    def run_all_tests(self):
        """运行所有跨边界数据操作测试"""
        print("🚀 开始跨边界数据操作测试")
        print("="*60)
        
        start_time = time.time()
        
        # 前置设置
        if not self.setup():
            print("❌ 测试前置设置失败，跳过测试")
            return []
        
        try:
            # 运行所有测试
            self.test_cross_merchant_create_operations()
            self.test_cross_merchant_update_operations()
            self.test_cross_merchant_delete_operations()
            self.test_cross_department_operations()
            self.test_role_privilege_escalation()
        finally:
            # 清理测试数据
            self.cleanup()
        
        end_time = time.time()
        duration = end_time - start_time
        
        # 打印测试摘要
        print_test_summary(self.results)
        print(f"测试耗时: {duration:.2f}秒")
        
        return self.results

def main():
    """主函数"""
    test_suite = CrossBoundaryOperationsTestSuite()
    results = test_suite.run_all_tests()
    
    # 判断测试是否通过
    failed_tests = [r for r in results if not r.get("success", False)]
    
    if not failed_tests:
        print("\n🎉 所有跨边界数据操作测试通过！")
        return 0
    else:
        print(f"\n⚠️ 发现 {len(failed_tests)} 个问题需要修复")
        for test in failed_tests:
            print(f"  - {test['test_name']}: {test['message']}")
        return 1

if __name__ == "__main__":
    sys.exit(main())
