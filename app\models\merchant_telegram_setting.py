"""
商户级Telegram配置模型
"""

from sqlalchemy import Column, BigInteger, JSON, Boolean, ForeignKey, DateTime, Text
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func

from app.models.base import BaseModel, TimestampMixin


class MerchantTelegramSetting(BaseModel, TimestampMixin):
    """商户级Telegram配置表"""
    
    __tablename__ = "merchant_telegram_settings"
    
    # 基础字段
    merchant_id = Column(
        BigInteger,
        ForeignKey("merchants.id", ondelete="CASCADE"),
        nullable=False,
        unique=True,
        index=True,
        comment="商户ID"
    )
    settings = Column(
        JSON,
        nullable=False,
        comment="商户级Telegram配置"
    )
    is_active = Column(
        Boolean,
        default=True,
        index=True,
        comment="是否启用"
    )
    
    # 操作人员字段
    created_by = Column(
        BigInteger,
        ForeignKey("users.id", ondelete="SET NULL"),
        nullable=True,
        comment="创建人ID"
    )
    updated_by = Column(
        BigInteger,
        ForeignKey("users.id", ondelete="SET NULL"),
        nullable=True,
        comment="更新人ID"
    )
    
    # 关联关系
    merchant = relationship("Merchant", back_populates="telegram_setting")
    creator = relationship("User", foreign_keys=[created_by])
    updater = relationship("User", foreign_keys=[updated_by])
    
    def __repr__(self):
        return f"<MerchantTelegramSetting(id={self.id}, merchant_id={self.merchant_id}, is_active={self.is_active})>"
    
    def get_default_settings(self) -> dict:
        """获取默认配置"""
        return {
            "permissions": {
                "allow_all_members": False,
                "require_user_verification": True,
                "admin_only_commands": ["bind", "unbind", "settings"],
                "rate_limit": {
                    "commands_per_minute": 10,
                    "queries_per_hour": 100
                },
                "query_permissions": {
                    "daily_stats": True,
                    "weekly_stats": True,
                    "monthly_stats": True,
                    "custom_range": False,
                    "detailed_data": False
                }
            },
            "display_settings": {
                "show_amount": True,
                "show_details": False,
                "decimal_places": 2,
                "timezone": "Asia/Shanghai",
                "language": "zh-CN",
                "currency_symbol": "¥",
                "date_format": "YYYY-MM-DD",
                "time_format": "HH:mm:ss"
            },
            "notification_settings": {
                "auto_notification": True,
                "welcome_message": True,
                "command_help": True,
                "auto_delete_commands": False,
                "error_notification": True,
                "daily_report": False,
                "daily_report_time": "09:00"
            },
            "advanced_settings": {
                "enable_cache": True,
                "cache_expire_minutes": 5,
                "enable_audit_log": True,
                "custom_commands": {},
                "webhook_settings": {
                    "enable_webhook": False,
                    "webhook_url": "",
                    "webhook_secret": ""
                }
            }
        }
    
    def get_effective_settings(self) -> dict:
        """获取生效的配置（合并默认配置）"""
        default_settings = self.get_default_settings()
        current_settings = self.settings or {}
        
        return self._deep_merge_settings([default_settings, current_settings])
    
    def get_permission_config(self) -> dict:
        """获取权限配置"""
        settings = self.get_effective_settings()
        return settings.get('permissions', {})
    
    def get_display_config(self) -> dict:
        """获取显示配置"""
        settings = self.get_effective_settings()
        return settings.get('display_settings', {})
    
    def get_notification_config(self) -> dict:
        """获取通知配置"""
        settings = self.get_effective_settings()
        return settings.get('notification_settings', {})
    
    def get_advanced_config(self) -> dict:
        """获取高级配置"""
        settings = self.get_effective_settings()
        return settings.get('advanced_settings', {})
    
    def update_settings(self, new_settings: dict) -> dict:
        """更新配置"""
        current_settings = self.settings or {}
        merged_settings = self._deep_merge_settings([current_settings, new_settings])
        self.settings = merged_settings
        return merged_settings
    
    def update_permission_settings(self, new_permissions: dict) -> dict:
        """更新权限配置"""
        current_settings = self.settings or {}
        if 'permissions' not in current_settings:
            current_settings['permissions'] = {}
        
        current_settings['permissions'].update(new_permissions)
        self.settings = current_settings
        return current_settings
    
    def update_display_settings(self, new_display: dict) -> dict:
        """更新显示配置"""
        current_settings = self.settings or {}
        if 'display_settings' not in current_settings:
            current_settings['display_settings'] = {}
        
        current_settings['display_settings'].update(new_display)
        self.settings = current_settings
        return current_settings
    
    def is_permission_allowed(self, permission_key: str) -> bool:
        """检查权限是否被允许"""
        permissions = self.get_permission_config()
        return permissions.get(permission_key, False)
    
    def get_rate_limit(self, limit_type: str) -> int:
        """获取频率限制"""
        permissions = self.get_permission_config()
        rate_limit = permissions.get('rate_limit', {})
        
        default_limits = {
            'commands_per_minute': 10,
            'queries_per_hour': 100
        }
        
        return rate_limit.get(limit_type, default_limits.get(limit_type, 0))
    
    def is_query_allowed(self, query_type: str) -> bool:
        """检查查询类型是否被允许"""
        permissions = self.get_permission_config()
        query_permissions = permissions.get('query_permissions', {})
        return query_permissions.get(query_type, False)
    
    def validate_settings(self, settings: dict) -> tuple[bool, list]:
        """验证配置的有效性"""
        errors = []
        
        if not isinstance(settings, dict):
            errors.append("配置必须是字典格式")
            return False, errors
        
        # 验证权限配置
        if 'permissions' in settings:
            permission_errors = self._validate_permissions(settings['permissions'])
            errors.extend(permission_errors)
        
        # 验证显示配置
        if 'display_settings' in settings:
            display_errors = self._validate_display_settings(settings['display_settings'])
            errors.extend(display_errors)
        
        # 验证通知配置
        if 'notification_settings' in settings:
            notification_errors = self._validate_notification_settings(settings['notification_settings'])
            errors.extend(notification_errors)
        
        return len(errors) == 0, errors
    
    def _validate_permissions(self, permissions: dict) -> list:
        """验证权限配置"""
        errors = []
        
        # 验证布尔值字段
        bool_fields = ['allow_all_members', 'require_user_verification']
        for field in bool_fields:
            if field in permissions and not isinstance(permissions[field], bool):
                errors.append(f"权限配置 {field} 必须是布尔值")
        
        # 验证频率限制
        if 'rate_limit' in permissions:
            rate_limit = permissions['rate_limit']
            if 'commands_per_minute' in rate_limit:
                if not isinstance(rate_limit['commands_per_minute'], int) or rate_limit['commands_per_minute'] < 1 or rate_limit['commands_per_minute'] > 60:
                    errors.append("每分钟命令数必须是1-60之间的整数")
            
            if 'queries_per_hour' in rate_limit:
                if not isinstance(rate_limit['queries_per_hour'], int) or rate_limit['queries_per_hour'] < 1 or rate_limit['queries_per_hour'] > 1000:
                    errors.append("每小时查询数必须是1-1000之间的整数")
        
        return errors
    
    def _validate_display_settings(self, display: dict) -> list:
        """验证显示配置"""
        errors = []
        
        # 验证小数位数
        if 'decimal_places' in display:
            if not isinstance(display['decimal_places'], int) or display['decimal_places'] < 0 or display['decimal_places'] > 4:
                errors.append("小数位数必须是0-4之间的整数")
        
        # 验证语言代码
        if 'language' in display:
            supported_languages = ['zh-CN', 'zh-TW', 'en-US']
            if display['language'] not in supported_languages:
                errors.append(f"不支持的语言代码，支持的语言：{', '.join(supported_languages)}")
        
        return errors
    
    def _validate_notification_settings(self, notification: dict) -> list:
        """验证通知配置"""
        errors = []
        
        # 验证日报时间格式
        if 'daily_report_time' in notification:
            import re
            time_pattern = r'^([01]?[0-9]|2[0-3]):[0-5][0-9]$'
            if not re.match(time_pattern, notification['daily_report_time']):
                errors.append("日报时间格式必须为 HH:MM")
        
        return errors
    
    def _deep_merge_settings(self, settings_list: list) -> dict:
        """深度合并多个配置字典"""
        result = {}
        
        for settings in settings_list:
            if not settings:
                continue
                
            for key, value in settings.items():
                if key not in result:
                    result[key] = value
                elif isinstance(value, dict) and isinstance(result[key], dict):
                    result[key] = self._deep_merge_settings([result[key], value])
                else:
                    result[key] = value
        
        return result
    
    @classmethod
    def get_by_merchant_id(cls, db, merchant_id: int):
        """根据商户ID获取配置"""
        return db.query(cls).filter_by(merchant_id=merchant_id, is_active=True).first()
    
    @classmethod
    def create_default_for_merchant(cls, db, merchant_id: int, created_by: int = None):
        """为商户创建默认配置"""
        existing = cls.get_by_merchant_id(db, merchant_id)
        if existing:
            return existing
        
        setting = cls(
            merchant_id=merchant_id,
            settings=cls().get_default_settings(),
            created_by=created_by,
            updated_by=created_by
        )
        
        db.add(setting)
        db.commit()
        db.refresh(setting)
        
        return setting
