<template>
    <div class="bind-trend-chart">
        <el-card shadow="hover" class="chart-card">
            <template #header>
                <div class="card-header">
                    <span>绑卡趋势分析</span>
                    <div class="chart-controls">
                        <el-radio-group v-model="period" size="small" @change="changePeriod">
                            <el-radio-button value="day">日</el-radio-button>
                            <el-radio-button value="week">周</el-radio-button>
                            <el-radio-button value="month">月</el-radio-button>
                        </el-radio-group>
                        <el-button type="primary" size="small" icon="Refresh" @click="refreshData"></el-button>
                    </div>
                </div>
            </template>

            <div class="chart-wrapper" v-loading="loading">
                <div ref="chartRef" class="chart-container"></div>
            </div>
        </el-card>
    </div>
</template>

<script setup>
import { ref, onMounted, onBeforeUnmount, watch } from 'vue'
import * as echarts from 'echarts/core'
import { LineChart } from 'echarts/charts'
import {
    TitleComponent,
    TooltipComponent,
    GridComponent,
    LegendComponent,
    ToolboxComponent,
    DataZoomComponent
} from 'echarts/components'
import { CanvasRenderer } from 'echarts/renderers'
import { ElMessage } from 'element-plus'
import {dashboardApi} from '@/api/modules/dashboard'

// 注册ECharts必须的组件
echarts.use([
    LineChart,
    TitleComponent,
    TooltipComponent,
    GridComponent,
    LegendComponent,
    ToolboxComponent,
    DataZoomComponent,
    CanvasRenderer
])

const props = defineProps({
    merchantId: {
        type: [String, Number],
        default: null
    }
})

// 状态
const loading = ref(false)
const chartRef = ref(null)
const chart = ref(null)
const period = ref('day')

// 模拟数据
const getTrendData = () => {
    if (period.value === 'day') {
        // 模拟过去30天的数据
        const days = Array.from({ length: 30 }, (_, i) => {
            const date = new Date()
            date.setDate(date.getDate() - 29 + i)
            return date.toISOString().split('T')[0]
        })

        // 生成随机数据
        const successData = days.map(() => Math.floor(Math.random() * 200 + 300))
        const failData = days.map(() => Math.floor(Math.random() * 50 + 20))

        return {
            dates: days,
            success: successData,
            fail: failData
        }
    } else if (period.value === 'week') {
        // 模拟过去12周的数据
        const weeks = Array.from({ length: 12 }, (_, i) => `第${12 - i}周`)

        // 生成随机数据
        const successData = weeks.map(() => Math.floor(Math.random() * 1200 + 1800))
        const failData = weeks.map(() => Math.floor(Math.random() * 300 + 150))

        return {
            dates: weeks,
            success: successData,
            fail: failData
        }
    } else {
        // 模拟过去12个月的数据
        const months = ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月']

        // 生成随机数据
        const successData = months.map(() => Math.floor(Math.random() * 5000 + 8000))
        const failData = months.map(() => Math.floor(Math.random() * 1000 + 500))

        return {
            dates: months,
            success: successData,
            fail: failData
        }
    }
}

// 获取趋势数据
const fetchTrendData = async () => {
    loading.value = true
    try {
        // 构建请求参数
        const params = {
            time_range: period.value
        }

        // 如果有商家ID，添加到参数中
        if (props.merchantId) {
            params.merchantId = props.merchantId
        }

        // 调用API获取绑卡趋势数据
        const response = await dashboardApi.getBindTrend(period.value)

        if (response && response.dateList) {
            // 处理API返回的数据
            const data = {
                dates: response.dateList,
                success: response.successList,
                fail: response.failedList
            }
            updateChart(data)
        } else {
            // 如果没有数据，使用模拟数据
            const mockData = getTrendData()
            updateChart(mockData)
            console.warn('使用模拟数据，因为API返回的数据为空')
        }
    } catch (error) {
        console.error('获取趋势数据失败:', error)
        ElMessage.error('获取趋势数据失败')
        // 出错时使用模拟数据
        const mockData = getTrendData()
        updateChart(mockData)
    } finally {
        loading.value = false
    }
}

// 切换时间周期
const changePeriod = () => {
    fetchTrendData()
}

// 刷新数据
const refreshData = () => {
    fetchTrendData()
    ElMessage.success('数据已刷新')
}

// 初始化图表
const initChart = () => {
    if (!chartRef.value) return

    // 延迟初始化，确保 DOM 已经渲染完成
    setTimeout(() => {
        if (chart.value) {
            chart.value.dispose()
        }

        // 初始化图表实例
        chart.value = echarts.init(chartRef.value, null, {
            renderer: 'canvas',
            useDirtyRect: false
        })

        // 设置空白配置，避免未初始化数据时的错误
        chart.value.setOption({
            grid: {
                left: '3%',
                right: '4%',
                bottom: '15%',
                top: '15%',
                containLabel: true
            },
            xAxis: {
                type: 'category',
                data: []
            },
            yAxis: {
                type: 'value'
            },
            series: []
        })

        // 注册窗口大小变化事件
        window.addEventListener('resize', handleResize)

        // 获取数据并更新图表
        fetchTrendData()
    }, 100)
}

// 更新图表
const updateChart = (data) => {
    if (!chart.value) return

    // 清除之前的配置
    chart.value.clear()

    const option = {
        title: {
            text: '绑卡成功/失败趋势',
            left: 'center'
        },
        tooltip: {
            trigger: 'axis',
            axisPointer: {
                type: 'cross',
                label: {
                    backgroundColor: '#6a7985'
                }
            }
        },
        legend: {
            data: ['成功', '失败'],
            bottom: 10
        },
        toolbox: {
            feature: {
                saveAsImage: { title: '保存为图片' }
            }
        },
        grid: {
            left: '3%',
            right: '4%',
            bottom: '15%',
            top: '15%',
            containLabel: true
        },
        xAxis: {
            type: 'category',
            boundaryGap: true,
            data: data.dates,
            axisLine: {
                lineStyle: {
                    color: '#666'
                }
            },
            axisTick: {
                alignWithLabel: true
            },
            axisLabel: {
                formatter: function (value) {
                    if (period.value === 'day') {
                        return value.substring(5) // 只显示月-日
                    }
                    return value
                },
                interval: 'auto',
                rotate: 0
            }
        },
        yAxis: {
            type: 'value',
            scale: true,
            splitLine: {
                lineStyle: {
                    type: 'dashed',
                    color: '#DDD'
                }
            },
            axisLine: {
                show: true,
                lineStyle: {
                    color: '#666'
                }
            },
            axisTick: {
                show: true
            }
        },
        series: [
            {
                name: '成功',
                type: 'line',
                stack: 'Total',
                areaStyle: {
                    color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                        { offset: 0, color: 'rgba(103, 194, 58, 0.5)' },
                        { offset: 1, color: 'rgba(103, 194, 58, 0.1)' }
                    ])
                },
                emphasis: {
                    focus: 'series'
                },
                data: data.success,
                itemStyle: {
                    color: '#67c23a'
                },
                smooth: true,
                symbol: 'circle',
                symbolSize: 6,
                showSymbol: false,
                lineStyle: {
                    width: 2
                }
            },
            {
                name: '失败',
                type: 'line',
                stack: 'Total',
                areaStyle: {
                    color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                        { offset: 0, color: 'rgba(245, 108, 108, 0.5)' },
                        { offset: 1, color: 'rgba(245, 108, 108, 0.1)' }
                    ])
                },
                emphasis: {
                    focus: 'series'
                },
                data: data.fail,
                itemStyle: {
                    color: '#f56c6c'
                },
                smooth: true,
                symbol: 'circle',
                symbolSize: 6,
                showSymbol: false,
                lineStyle: {
                    width: 2
                }
            }
        ]
    }

    // 单独设置 dataZoom 组件，避免初始化时的问题
    chart.value.setOption(option)

    // 延迟添加 dataZoom 组件，确保坐标轴已经创建
    setTimeout(() => {
        chart.value.setOption({
            dataZoom: [
                {
                    type: 'slider',
                    show: true,
                    bottom: 10,
                    height: 20,
                    xAxisIndex: [0],
                    start: 0,
                    end: 100,
                    filterMode: 'filter',
                    zoomLock: false,
                    showDetail: true,
                    realtime: true,
                    handleIcon: 'path://M10.7,11.9v-1.3H9.3v1.3c-4.9,0.3-8.8,4.4-8.8,9.4c0,5,3.9,9.1,8.8,9.4v1.3h1.3v-1.3c4.9-0.3,8.8-4.4,8.8-9.4C19.5,16.3,15.6,12.2,10.7,11.9z M13.3,24.4H6.7V23h6.6V24.4z M13.3,19.6H6.7v-1.4h6.6V19.6z',
                    handleSize: '80%',
                    handleStyle: {
                        color: '#fff',
                        shadowBlur: 3,
                        shadowColor: 'rgba(0, 0, 0, 0.6)',
                        shadowOffsetX: 2,
                        shadowOffsetY: 2
                    }
                },
                {
                    type: 'inside',
                    xAxisIndex: [0],
                    start: 0,
                    end: 100
                }
            ]
        })
    }, 100)
}

// 节流函数，用于处理窗口大小变化
const throttle = (fn, delay) => {
    let timer = null
    return function () {
        const context = this
        const args = arguments
        if (timer) clearTimeout(timer)
        timer = setTimeout(() => {
            fn.apply(context, args)
        }, delay)
    }
}

// 更新 handleResize 函数，检查图表是否存在且有效
const handleResize = throttle(() => {
    if (chart.value && chart.value.isDisposed() === false) {
        try {
            chart.value.resize({
                animation: {
                    duration: 300
                }
            })
        } catch (error) {
            console.warn('图表调整大小错误:', error)
            // 如果调整大小失败，尝试重新初始化图表
            if (chartRef.value) {
                chart.value.dispose()
                chart.value = echarts.init(chartRef.value)
                updateChartOption()
            }
        }
    }
}, 100)

// 添加此方法以更新图表选项而不重新创建图表
const updateChartOption = () => {
    if (!chart.value || !chartData.value) return

    // 应用当前图表选项
    chart.value.setOption(createChartOption())
}

// 将选项创建提取到单独的方法中
const createChartOption = () => {
    // 你现有的选项创建逻辑
    return {
        // ... 现有选项
    }
}

// 更新窗口调整大小事件监听器
onMounted(() => {
    initChart()
    window.addEventListener('resize', handleResize)
})

// 组件卸载前
onBeforeUnmount(() => {
    window.removeEventListener('resize', handleResize)
    if (chart.value) {
        chart.value.dispose()
        chart.value = null
    }
})

// 监听属性变化
watch(
    () => props.merchantId,
    () => {
        fetchTrendData()
    }
)
</script>

<style scoped>
.bind-trend-chart {
    margin-bottom: 20px;
}

.chart-card {
    width: 100%;
}

.card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.chart-controls {
    display: flex;
    align-items: center;
    gap: 10px;
}

.chart-wrapper {
    padding: 10px 0;
}

.chart-container {
    height: 400px;
    width: 100%;
}
</style>