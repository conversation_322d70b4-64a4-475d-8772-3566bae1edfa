"""
测试CK统计功能修复 - 验证不同统计维度使用正确的时间过滤逻辑

测试场景：
1. 昨天创建今天删除的CK在选择"今天"筛选时，删除数量应该为1
2. 今天创建今天删除的CK在选择"今天"筛选时，总数和删除数量都应该为1
3. 绑卡统计应该基于绑卡发生的时间，而不是CK创建时间
"""

import pytest
from datetime import datetime, timedelta
from sqlalchemy.orm import Session

from app.models.walmart_ck import WalmartCK
from app.models.card_record import CardRecord
from app.models.user import User
from app.models.merchant import Merchant
from app.models.department import Department
from app.services.walmart_ck_service_new import WalmartCKService
from app.api.deps import get_db


class TestCKStatisticsFix:
    """CK统计功能修复测试类"""

    @pytest.fixture
    def db_session(self):
        """获取数据库会话"""
        db = next(get_db())
        try:
            yield db
        finally:
            db.close()

    @pytest.fixture
    def test_merchant(self, db_session: Session):
        """创建测试商户"""
        merchant = Merchant(
            name="测试商户",
            code="TEST_MERCHANT",
            contact_person="测试联系人",
            contact_phone="13800138000",
            is_active=True
        )
        db_session.add(merchant)
        db_session.commit()
        db_session.refresh(merchant)
        return merchant

    @pytest.fixture
    def test_department(self, db_session: Session, test_merchant):
        """创建测试部门"""
        department = Department(
            name="测试部门",
            code="TEST_DEPT",
            merchant_id=test_merchant.id,
            is_active=True
        )
        db_session.add(department)
        db_session.commit()
        db_session.refresh(department)
        return department

    @pytest.fixture
    def test_user(self, db_session: Session, test_merchant, test_department):
        """创建测试用户"""
        user = User(
            username="test_user",
            email="<EMAIL>",
            hashed_password="hashed_password",
            name="测试用户",
            merchant_id=test_merchant.id,
            department_id=test_department.id,
            is_active=True,
            is_superuser=False
        )
        db_session.add(user)
        db_session.commit()
        db_session.refresh(user)
        return user

    @pytest.fixture
    def ck_service(self, db_session: Session):
        """创建CK服务实例"""
        return WalmartCKService(db_session)

    def test_deletion_statistics_with_time_filter(
        self, 
        db_session: Session, 
        ck_service: WalmartCKService,
        test_user: User,
        test_merchant: Merchant,
        test_department: Department
    ):
        """
        测试删除统计的时间过滤逻辑
        
        场景：昨天创建的CK，今天删除，选择"今天"筛选时删除数量应该为1
        """
        # 1. 创建昨天的CK
        yesterday = datetime.now() - timedelta(days=1)
        today = datetime.now()
        
        ck_yesterday = WalmartCK(
            sign="test_ck_yesterday@token#sign#26",
            total_limit=100,
            active=True,
            merchant_id=test_merchant.id,
            department_id=test_department.id,
            created_by=test_user.id,
            created_at=yesterday,
            updated_at=yesterday,
            is_deleted=False
        )
        db_session.add(ck_yesterday)
        db_session.commit()
        db_session.refresh(ck_yesterday)

        # 2. 今天删除这个CK
        ck_yesterday.is_deleted = True
        ck_yesterday.updated_at = today
        db_session.commit()

        # 3. 创建今天的时间过滤条件
        today_start = today.replace(hour=0, minute=0, second=0, microsecond=0)
        today_end = today.replace(hour=23, minute=59, second=59, microsecond=999999)
        
        filters = {
            'start_time': today_start,
            'end_time': today_end
        }

        # 4. 获取统计数据
        statistics = ck_service._get_statistics_with_filters(test_user, filters)

        # 5. 验证结果
        assert statistics['summary']['total_ck_count'] == 0  # 今天创建的CK数量为0
        assert statistics['summary']['deleted_ck_count'] == 1  # 今天删除的CK数量为1

    def test_creation_and_deletion_same_day(
        self,
        db_session: Session,
        ck_service: WalmartCKService,
        test_user: User,
        test_merchant: Merchant,
        test_department: Department
    ):
        """
        测试同一天创建和删除的CK统计
        
        场景：今天创建今天删除的CK，选择"今天"筛选时，总数和删除数量都应该为1
        """
        # 1. 创建今天的CK
        today = datetime.now()
        
        ck_today = WalmartCK(
            sign="test_ck_today@token#sign#26",
            total_limit=100,
            active=True,
            merchant_id=test_merchant.id,
            department_id=test_department.id,
            created_by=test_user.id,
            created_at=today,
            updated_at=today,
            is_deleted=False
        )
        db_session.add(ck_today)
        db_session.commit()
        db_session.refresh(ck_today)

        # 2. 今天删除这个CK
        ck_today.is_deleted = True
        ck_today.updated_at = today
        db_session.commit()

        # 3. 创建今天的时间过滤条件
        today_start = today.replace(hour=0, minute=0, second=0, microsecond=0)
        today_end = today.replace(hour=23, minute=59, second=59, microsecond=999999)
        
        filters = {
            'start_time': today_start,
            'end_time': today_end
        }

        # 4. 获取统计数据
        statistics = ck_service._get_statistics_with_filters(test_user, filters)

        # 5. 验证结果
        assert statistics['summary']['total_ck_count'] == 1  # 今天创建的CK数量为1
        assert statistics['summary']['deleted_ck_count'] == 1  # 今天删除的CK数量为1

    def test_binding_statistics_independent_of_ck_deletion(
        self,
        db_session: Session,
        ck_service: WalmartCKService,
        test_user: User,
        test_merchant: Merchant,
        test_department: Department
    ):
        """
        测试绑卡统计独立于CK删除状态
        
        场景：CK被删除后，其历史绑卡记录仍应被统计
        """
        # 1. 创建CK
        yesterday = datetime.now() - timedelta(days=1)
        today = datetime.now()
        
        ck = WalmartCK(
            sign="test_ck_binding@token#sign#26",
            total_limit=100,
            active=True,
            merchant_id=test_merchant.id,
            department_id=test_department.id,
            created_by=test_user.id,
            created_at=yesterday,
            updated_at=yesterday,
            is_deleted=False
        )
        db_session.add(ck)
        db_session.commit()
        db_session.refresh(ck)

        # 2. 创建绑卡记录
        card_record = CardRecord(
            walmart_ck_id=ck.id,
            department_id=test_department.id,
            card_number="1234567890123456",
            amount=100.0,
            actual_amount=95.0,
            status="success",
            created_at=today,
            updated_at=today
        )
        db_session.add(card_record)
        db_session.commit()

        # 3. 删除CK
        ck.is_deleted = True
        ck.updated_at = today
        db_session.commit()

        # 4. 创建今天的时间过滤条件（基于绑卡时间）
        today_start = today.replace(hour=0, minute=0, second=0, microsecond=0)
        today_end = today.replace(hour=23, minute=59, second=59, microsecond=999999)
        
        filters = {
            'start_time': today_start,
            'end_time': today_end
        }

        # 5. 获取统计数据
        statistics = ck_service._get_statistics_with_filters(test_user, filters)

        # 6. 验证结果
        assert statistics['summary']['total_ck_count'] == 0  # 今天创建的CK数量为0
        assert statistics['summary']['deleted_ck_count'] == 1  # 今天删除的CK数量为1
        assert statistics['summary']['total_success'] == 1  # 成功绑卡数量为1（不受CK删除影响）
        assert statistics['summary']['total_actual_amount'] == 95.0  # 实际绑卡金额为95.0

    def teardown_method(self, method):
        """清理测试数据"""
        # 测试完成后清理数据
        pass
