<template>
  <div class="bind-container">
    <el-card shadow="hover" class="bind-card">
      <template #header>
        <div class="card-header">
          <h2>批量绑卡</h2>
          <span>批量绑卡</span>
        </div>
      </template>

      <el-alert type="info" show-icon :closable="false" class="mb-5" title="此页面用于测试批量绑卡接口，完全模拟商户调用方式，请确保参数符合接口文档要求" />

      <!-- 批量绑卡表单 -->
      <div class="bind-form batch-form">
        <!-- API密钥信息 -->
        <el-row :gutter="24">
          <el-col :xs="24" :sm="12">
            <div class="form-item">
              <label class="form-label">
                API密钥 (api-key) <span class="required">*</span>
                <el-button type="primary" size="small" :loading="autoFillLoading"
                  @click="autoFillApiCredentials(userStore.isSuperAdmin && merchantStore.currentMerchant ? merchantStore.currentMerchant.id : null)"
                  style="margin-left: 8px;" :icon="Refresh">
                  {{ autoFillLoading ? '获取中...' : '自动填充' }}
                </el-button>
              </label>
              <el-input v-model="batchForm.apiKey" placeholder="请输入API密钥" :prefix-icon="Key" clearable />
              <div class="error-message" v-if="batchErrors.apiKey">{{ batchErrors.apiKey }}</div>
            </div>
          </el-col>
          <el-col :xs="24" :sm="12">
            <div class="form-item">
              <label class="form-label">API密钥密文 (Secret) <span class="required">*</span></label>
              <form>
                <el-input v-model="batchForm.apiSecret" placeholder="请输入API密钥密文" show-password :prefix-icon="Lock"
                  clearable />
              </form>
              <div class="error-message" v-if="batchErrors.apiSecret">{{ batchErrors.apiSecret }}</div>
            </div>
          </el-col>
        </el-row>

        <el-row :gutter="24">
          <el-col :xs="24" :sm="12">
            <div class="form-item">
              <label class="form-label">商家编码 (merchant_code) <span class="required">*</span></label>
              <el-input v-model="batchForm.merchantCode" placeholder="请输入商家编码" :prefix-icon="Shop" clearable />
              <div class="error-message" v-if="batchErrors.merchantCode">{{ batchErrors.merchantCode }}</div>
            </div>
          </el-col>
          <!-- 默认金额输入框已移除 - 现在要求所有卡数据都必须包含金额信息 -->
        </el-row>

        <!-- 批量卡号数据 -->
        <div class="form-item">
          <label class="form-label">
            批量卡号数据 <span class="required">*</span>
            <el-tag type="info" size="small" style="margin-left: 8px;">支持两种格式</el-tag>
          </label>
          <el-input v-model="batchForm.cardData" type="textarea" :rows="10"
            placeholder="请输入批量卡号数据，每行一张卡&#10;格式1（推荐）：卡号,卡密,金额,商户订单号&#10;格式2（便捷）：卡号,卡密,金额（自动生成商户订单号）&#10;示例：&#10;1234567890123456,123456,10.00,ORDER001&#10;2345678901234567,234567,10.00&#10;注意：金额单位为元，最小值10元，支持小数点，所有卡都必须包含金额信息"
            clearable />
          <div class="error-message" v-if="batchErrors.cardData">{{ batchErrors.cardData }}</div>
          <div class="batch-info" v-if="batchForm.cardData">
            <el-tag type="success" size="small">
              检测到 {{ parseBatchData().length }} 张卡
            </el-tag>
            <el-tag type="info" size="small" style="margin-left: 8px;">
              有金额：{{parseBatchData().filter(card => card.amount).length}} 张
            </el-tag>
            <el-tag v-if="parseBatchData().filter(card => !card.amount).length > 0" type="danger" size="small"
              style="margin-left: 8px;">
              缺少金额：{{parseBatchData().filter(card => !card.amount).length}} 张
            </el-tag>
            <el-button type="text" size="small" style="margin-left: 8px;" @click="showPreview = !showPreview">
              {{ showPreview ? '隐藏预览' : '预览数据' }}
            </el-button>
          </div>

          <!-- 卡数据预览 -->
          <div v-if="showPreview && batchForm.cardData" class="card-preview">
            <el-table :data="parseBatchData()" stripe style="width: 100%" max-height="300" size="small">
              <el-table-column prop="lineNumber" label="行号" width="60" />
              <el-table-column prop="cardNumber" label="卡号" width="180" />
              <el-table-column prop="cardPassword" label="卡密" width="100" />
              <el-table-column label="金额" width="120">
                <template #default="scope">
                  <el-text v-if="scope.row.amount" type="success">{{ fenToYuan(scope.row.amount) }} 元</el-text>
                  <el-text v-else type="danger">缺少金额</el-text>
                </template>
              </el-table-column>
              <el-table-column prop="merchantOrderId" label="商户订单号" width="150" />
            </el-table>
          </div>
        </div>

        <el-divider />

        <div class="form-actions">
          <el-button type="primary" :loading="batchLoading" @click="onBatchSubmit" :icon="Position" size="large">
            批量绑卡
          </el-button>
          <el-button @click="resetBatchForm" :icon="Refresh" size="large" type="default">
            重置表单
          </el-button>
          <el-button @click="$router.push('/bind/single-bind')" :icon="CreditCard" size="large" type="info">
            切换到单卡绑卡
          </el-button>
        </div>
      </div>

      <!-- 批量绑卡结果 -->
      <el-row v-if="batchResults.length > 0">
        <el-col :span="24">
          <el-card shadow="hover" class="batch-results">
            <template #header>
              <div class="section-header">
                <el-icon :size="18" style="margin-right: 8px;">
                  <Document />
                </el-icon>
                <span>批量绑卡结果</span>
                <el-progress v-if="batchLoading" :percentage="batchProgress" style="margin-left: 20px; flex: 1;" />
              </div>
            </template>

            <el-table :data="batchResults" stripe style="width: 100%" max-height="400">
              <el-table-column prop="lineNumber" label="行号" width="80" />
              <el-table-column prop="cardNumber" label="卡号" width="180" />
              <el-table-column prop="cardPassword" label="卡密" width="100" />
              <el-table-column label="金额" width="100">
                <template #default="scope">
                  <el-text>{{ scope.row.amount }} 分</el-text>
                </template>
              </el-table-column>
              <el-table-column prop="merchantOrderId" label="商户订单号" width="150" />
              <el-table-column label="状态" width="100">
                <template #default="scope">
                  <el-tag :type="scope.row.status === 'success' ? 'success' : 'danger'" size="small">
                    {{ scope.row.status === 'success' ? '成功' : '失败' }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column label="结果" min-width="200">
                <template #default="scope">
                  <div v-if="scope.row.status === 'success'" class="success-result">
                    <pre class="result-text">{{ JSON.stringify(scope.row.result, null, 2) }}</pre>
                  </div>
                  <div v-else class="error-result">
                    <el-text type="danger">{{ scope.row.error }}</el-text>
                  </div>
                </template>
              </el-table-column>
            </el-table>

            <div class="batch-summary" style="margin-top: 16px;">
              <el-tag type="success" size="large">
                成功：{{batchResults.filter(r => r.status === 'success').length}} 张
              </el-tag>
              <el-tag type="danger" size="large" style="margin-left: 8px;">
                失败：{{batchResults.filter(r => r.status === 'error').length}} 张
              </el-tag>
              <el-tag type="info" size="large" style="margin-left: 8px;">
                总计：{{ batchResults.length }} 张
              </el-tag>
            </div>
          </el-card>
        </el-col>
      </el-row>

    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, onUnmounted } from 'vue'
import { ElMessage } from 'element-plus'
import { cardDataApi } from '@/api/modules/cardData'
import { merchantApi } from '@/api/modules/merchant'
import { useUserStore } from '@/store/modules/user'
import { useMerchantStore } from '@/store/modules/merchant'
import merchantSwitchListener from '@/utils/merchantSwitchListener'
import CryptoJS from 'crypto-js'
import {
  Lock,
  Key,
  Document,
  CreditCard,
  Shop,
  Position,
  Refresh,
  Money
} from '@element-plus/icons-vue'
import { generateMerchantOrderId } from '@/utils/orderUtils'
import { yuanToFen, fenToYuan, validateYuanAmount, parseBatchAmount } from '@/utils/amountUtils'

// Store
const userStore = useUserStore()
const merchantStore = useMerchantStore()

// 批量绑卡相关
const batchLoading = ref(false)
const batchResults = ref([])
const batchProgress = ref(0)
const autoFillLoading = ref(false)
const showPreview = ref(false)

// 批量绑卡表单数据
const batchForm = reactive({
  apiKey: '',
  apiSecret: '',
  merchantCode: '',
  cardData: ''
  // defaultAmount 已移除 - 现在要求所有卡数据都必须包含金额信息
})

// 批量绑卡错误信息
const batchErrors = reactive({
  apiKey: '',
  apiSecret: '',
  merchantCode: '',
  cardData: ''
  // defaultAmount 已移除 - 现在要求所有卡数据都必须包含金额信息
})

// API密钥自动填充功能
const autoFillApiCredentials = async (merchantId = null) => {
  try {
    autoFillLoading.value = true
    let apiCredentials = null

    if (userStore.isSuperAdmin && merchantId) {
      // 超级管理员获取指定商家的API密钥
      apiCredentials = await merchantApi.getApiCredentials(merchantId)
    } else {
      // 商家用户获取自己商家的API密钥
      apiCredentials = await merchantApi.getCurrentMerchantApiCredentials()
    }

    if (apiCredentials) {
      batchForm.apiKey = apiCredentials.api_key || ''
      batchForm.apiSecret = apiCredentials.api_secret || ''
      batchForm.merchantCode = apiCredentials.merchant_code || ''

      ElMessage.success(`已自动填充 ${apiCredentials.merchant_name} 的API密钥信息`)
    }
  } catch (error) {
    console.error('获取API密钥失败:', error)
    ElMessage.error('获取API密钥失败: ' + (error.response?.data?.detail || error.message))
  } finally {
    autoFillLoading.value = false
  }
}

// 商家切换监听器
let unregisterMerchantSwitch = null

// 监听商家切换事件（仅超级管理员）
const handleMerchantSwitch = async () => {
  if (userStore.isSuperAdmin && merchantStore.currentMerchant) {
    await autoFillApiCredentials(merchantStore.currentMerchant.id)
  }
}

// 解析批量数据
const parseBatchData = () => {
  if (!batchForm.cardData) return []

  const lines = batchForm.cardData.split('\n').filter(line => line.trim())
  const cards = []

  lines.forEach((line, index) => {
    const parts = line.trim().split(',')
    if (parts.length >= 3) {
      let card = {
        lineNumber: index + 1,
        cardNumber: parts[0].trim(),
        cardPassword: parts[1].trim(),
        amount: null, // 默认为null，表示使用默认金额
        merchantOrderId: ''
      }

      // 判断数据格式
      if (parts.length === 3) {
        // 3字段格式：需要判断第3个字段是金额还是商户订单号
        const thirdField = parts[2].trim()
        const amountResult = parseBatchAmount(thirdField)

        if (amountResult.amount !== null && amountResult.amount >= 1000) {
          // 格式2（便捷）：卡号,卡密,金额（自动生成商户订单号）
          card.amount = amountResult.amount // 已转换为分
          card.merchantOrderId = generateMerchantOrderId('WM', batchForm.merchantCode)
        } else {
          // 第3个字段不是有效金额，标记为缺少金额（需要用户修正）
          card.amount = null // 缺少有效金额
          card.merchantOrderId = thirdField
        }
      } else if (parts.length >= 4) {
        // 格式1（推荐）：卡号,卡密,金额,商户订单号
        const thirdField = parts[2].trim()
        const amountResult = parseBatchAmount(thirdField)

        if (amountResult.amount !== null && amountResult.amount >= 1000) {
          // 第3个字段是有效金额
          card.amount = amountResult.amount // 已转换为分
          card.merchantOrderId = parts[3].trim() || generateMerchantOrderId('WM', batchForm.merchantCode)
        } else {
          // 第3个字段不是有效金额，标记为缺少金额（需要用户修正）
          card.amount = null
          card.merchantOrderId = thirdField
        }
      }

      cards.push(card)
    }
  })

  return cards
}

// 批量绑卡表单验证
const validateBatchForm = () => {
  let isValid = true

  // 重置所有错误
  Object.keys(batchErrors).forEach(key => batchErrors[key] = '')

  // API密钥
  if (!batchForm.apiKey) {
    batchErrors.apiKey = '请输入API密钥'
    isValid = false
  }

  // API密钥密文
  if (!batchForm.apiSecret) {
    batchErrors.apiSecret = '请输入API密钥密文'
    isValid = false
  }

  // 商家编码
  if (!batchForm.merchantCode) {
    batchErrors.merchantCode = '请输入商家编码'
    isValid = false
  }

  // 验证所有卡数据都必须包含金额信息
  const cards = parseBatchData()
  const cardsWithoutAmount = cards.filter(card => !card.amount)

  if (cardsWithoutAmount.length > 0) {
    // 有卡缺少金额信息，给出明确提示
    batchErrors.cardData = `第 ${cardsWithoutAmount.map(c => c.lineNumber).join(', ')} 行缺少金额信息，请使用格式：卡号,卡密,金额 或 卡号,卡密,金额,订单号`
    isValid = false
  }

  // 批量卡号数据
  if (!batchForm.cardData || !batchForm.cardData.trim()) {
    batchErrors.cardData = '请输入批量卡号数据'
    isValid = false
  } else {
    if (cards.length === 0) {
      batchErrors.cardData = '请输入有效的卡号数据，支持格式：卡号,卡密,金额,商户订单号 或 卡号,卡密,金额'
      isValid = false
    } else {
      // 验证每张卡的金额（已转换为分，最小值1000分=10元）
      const invalidAmountCards = cards.filter(card => card.amount && card.amount < 1000)
      if (invalidAmountCards.length > 0) {
        batchErrors.cardData = `第 ${invalidAmountCards.map(c => c.lineNumber).join(', ')} 行的金额必须大于等于10元`
        isValid = false
      }
    }
  }

  return isValid
}

// 优化的签名生成函数 - 与后端算法保持一致
const generateSignature = (data, secret, timestamp, nonce, method = 'POST', path = '/api/v1/card-bind', debug = false) => {
  // 1. 规范化请求参数
  const normalizedData = {}

  // 只包含非空值，按键名排序
  Object.keys(data).sort().forEach(key => {
    if (data[key] !== null && data[key] !== undefined && data[key] !== '') {
      normalizedData[key] = data[key]
    }
  })

  // 2. 创建规范化的JSON字符串（与后端完全一致）
  // 使用与Python json.dumps(separators=(',', ':'), sort_keys=True, ensure_ascii=False)完全一致的方法
  // 重要：必须使用紧凑格式且不转义Unicode字符
  const jsonStr = JSON.stringify(normalizedData, Object.keys(normalizedData).sort())
    .replace(/\s+/g, '') // 移除所有空白字符
    .replace(/\\u[\da-f]{4}/gi, (match) => {
      // 将Unicode转义序列转换回原始字符（与ensure_ascii=False一致）
      return String.fromCharCode(parseInt(match.replace('\\u', ''), 16))
    })

  // 3. 构建签名字符串（使用竖线分隔，与后端一致）
  const signatureComponents = [
    method.toUpperCase(),
    path,
    timestamp,
    nonce,
    jsonStr,
    secret
  ]
  const signString = signatureComponents.join('|')

  // 4. 使用HMAC-SHA256生成签名并Base64编码（与后端一致）
  const hmacDigest = CryptoJS.HmacSHA256(signString, secret)
  const signature = CryptoJS.enc.Base64.stringify(hmacDigest)

  // 调试信息
  if (debug) {
    console.log('=== 签名调试信息 ===')
    console.log('原始数据:', data)
    console.log('规范化数据:', normalizedData)
    console.log('JSON字符串:', jsonStr)
    console.log('JSON字符串长度:', jsonStr.length)
    console.log('签名字符串:', signString)
    console.log('签名字符串长度:', signString.length)
    console.log('生成的签名:', signature)
    console.log('时间戳:', timestamp)
    console.log('随机数:', nonce)
    console.log('==================')
  }

  return signature
}

// 发送API请求的通用函数
const sendApiRequest = async (data, headers) => {
  try {
    const response = await cardDataApi.bindCard(data, { headers })
    return {
      status: 200,
      data: response
    }
  } catch (error) {
    console.error('API请求失败:', error)
    throw error
  }
}

// 批量绑卡提交
const onBatchSubmit = async () => {
  if (!validateBatchForm()) {
    ElMessage.error('请完成表单必填项')
    return
  }

  try {
    batchLoading.value = true
    batchResults.value = []
    batchProgress.value = 0

    const cards = parseBatchData()
    const totalCards = cards.length

    ElMessage.info(`开始批量绑卡，共 ${totalCards} 张卡`)

    for (let i = 0; i < cards.length; i++) {
      const card = cards[i]

      // 使用卡自身的金额（已转换为分）- 所有卡都必须包含金额信息
      const cardAmount = card.amount

      try {
        // 生成时间戳和随机数（确保唯一性）
        const timestamp = Date.now().toString()
        // 优化nonce生成：添加索引、时间戳和随机数，确保唯一性
        const nonce = `${Math.random().toString(36).substring(2, 15)}_${i}_${timestamp}_${Math.random().toString(36).substring(2, 8)}`

        // 如果用户没有输入商户订单号，使用新的自动生成逻辑
        if (!card.merchantOrderId || card.merchantOrderId.trim() === '') {
          card.merchantOrderId = generateMerchantOrderId('WM', batchForm.merchantCode)
        }

        // 准备请求数据（不包含timestamp和nonce，这些通过请求头传递）
        const requestData = {
          card_number: card.cardNumber,
          card_password: card.cardPassword,
          merchant_code: batchForm.merchantCode,
          merchant_order_id: card.merchantOrderId,
          amount: cardAmount
        }

        // 生成签名（使用不包含timestamp和nonce的数据）
        // 在开发环境启用调试模式，生产环境关闭
        // 对前3张卡启用调试，以便发现签名问题
        const isDebug = process.env.NODE_ENV === 'development' && i < 3
        const signature = generateSignature(requestData, batchForm.apiSecret, timestamp, nonce, 'POST', '/api/v1/card-bind', isDebug)

        // 准备请求头
        const headers = {
          'Content-Type': 'application/json',
          'api-key': batchForm.apiKey,
          'X-Timestamp': timestamp,
          'X-Nonce': nonce,
          'X-Signature': signature
        }

        // 发送请求
        const response = await sendApiRequest(requestData, headers)

        // 记录成功结果
        batchResults.value.push({
          ...card,
          amount: cardAmount, // 记录实际使用的金额
          status: 'success',
          result: response.data
        })

      } catch (error) {
        console.error(`第 ${card.lineNumber} 张卡绑卡失败:`, error)

        // 如果是签名验证失败，记录详细信息
        if (error.response?.data?.detail === '签名验证失败') {
          console.error('=== 签名验证失败详细信息 ===')
          console.error('卡号:', card.cardNumber)
          console.error('请求数据:', requestData)
          console.error('时间戳:', timestamp)
          console.error('随机数:', nonce)
          console.error('API密钥:', batchForm.apiKey)
          console.error('API密钥长度:', batchForm.apiSecret?.length)
          console.error('响应状态:', error.response?.status)
          console.error('响应数据:', error.response?.data)
          console.error('================================')
        }

        // 记录失败结果
        batchResults.value.push({
          ...card,
          amount: cardAmount, // 记录实际使用的金额
          status: 'error',
          error: error.response?.data?.detail || error.message,
          errorDetails: error.response?.data // 保存完整的错误响应
        })
      }

      // 更新进度
      batchProgress.value = Math.round(((i + 1) / totalCards) * 100)

      // 增加延迟避免请求过于频繁，确保时间戳和nonce唯一性
      if (i < cards.length - 1) {
        await new Promise(resolve => setTimeout(resolve, 500)) // 增加到500ms
      }
    }

    const successCount = batchResults.value.filter(r => r.status === 'success').length
    const failCount = batchResults.value.filter(r => r.status === 'error').length

    ElMessage.success(`批量绑卡完成！成功：${successCount} 张，失败：${failCount} 张`)

  } catch (error) {
    console.error('批量绑卡失败:', error)
    ElMessage.error('批量绑卡失败: ' + error.message)
  } finally {
    batchLoading.value = false
    batchProgress.value = 100
  }
}

// 重置批量表单
const resetBatchForm = () => {
  Object.keys(batchForm).forEach(key => {
    batchForm[key] = ''
  })
  Object.keys(batchErrors).forEach(key => {
    batchErrors[key] = ''
  })
  batchResults.value = []
  batchProgress.value = 0
  ElMessage.success('表单已重置')
}

// 组件挂载时的初始化
onMounted(async () => {
  // 注册商家切换监听器
  if (userStore.isSuperAdmin) {
    unregisterMerchantSwitch = merchantSwitchListener.registerRefreshFunction(handleMerchantSwitch)
  }

  // 自动填充API密钥（如果是商家用户或超级管理员已选择商家）
  if (!userStore.isSuperAdmin || (userStore.isSuperAdmin && merchantStore.currentMerchant)) {
    await autoFillApiCredentials(userStore.isSuperAdmin && merchantStore.currentMerchant ? merchantStore.currentMerchant.id : null)
  }
})

// 组件卸载时清理
onUnmounted(() => {
  if (unregisterMerchantSwitch) {
    unregisterMerchantSwitch()
  }
})
</script>

<style scoped>
.bind-container {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.bind-card {
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-header h2 {
  margin: 0;
  font-size: 1.3rem;
  color: var(--el-text-color-primary);
  font-weight: 600;
}

.mb-5 {
  margin-bottom: 25px;
}

/* 自定义表单样式 */
.form-item {
  margin-bottom: 24px;
}

.form-label {
  display: block;
  margin-bottom: 8px;
  font-size: 14px;
  color: var(--el-text-color-regular);
  line-height: 1.5;
}

.required {
  color: #f56c6c;
  margin-right: 4px;
}

.error-message {
  color: #f56c6c;
  font-size: 12px;
  margin-top: 4px;
  line-height: 1;
}

.form-actions {
  display: flex;
  justify-content: center;
  margin-top: 20px;
  padding-top: 10px;
}

.form-actions .el-button {
  min-width: 130px;
  margin: 0 10px;
}

.batch-info {
  margin-top: 8px;
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 8px;
}

.card-preview {
  margin-top: 16px;
  border: 1px solid var(--el-border-color-light);
  border-radius: 6px;
  padding: 12px;
  background-color: var(--el-bg-color-page);
}

.batch-results {
  margin-top: 20px;
}

.result-text {
  font-size: 12px;
  margin: 0;
  max-height: 100px;
  overflow-y: auto;
  background: var(--el-fill-color-lighter);
  padding: 8px;
  border-radius: 4px;
}

.error-result {
  color: var(--el-color-danger);
  font-size: 12px;
}

.success-result {
  color: var(--el-color-success);
}

.batch-summary {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 16px 0;
  border-top: 1px solid var(--el-border-color-light);
}

.section-header {
  display: flex;
  align-items: center;
  font-size: 1.1rem;
  font-weight: 500;
  color: var(--el-text-color-primary);
}

.el-divider--horizontal {
  margin: 30px 0;
}

.form-hint {
  font-size: 12px;
  color: var(--el-text-color-secondary);
  margin-top: 4px;
  line-height: 1.4;
}
</style>
