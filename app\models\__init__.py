# 基础模型
from app.models.base import BaseModel, TimestampMixin

# 核心模型
from app.models.user import User
from app.models.role import Role
from app.models.permission import Permission
from app.models.menu import Menu
from app.models.merchant import Merchant

# 新的组织架构模型
from app.models.department import Department
from app.models.organization_relation import OrganizationRelation
from app.models.user_organization import UserOrganization

# 业务模型
from app.models.card_record import CardRecord, CardStatus
from app.models.binding_log import BindingLog, LogLevel, LogType
from app.models.notification import Notification, NotificationType, NotificationStatus
from app.models.walmart_server import WalmartServer
from app.models.walmart_ck import WalmartCK

# Telegram机器人模型
from app.models.telegram_group import TelegramGroup, ChatType, BindStatus
from app.models.telegram_user import TelegramUser, VerificationStatus
from app.models.telegram_bot_config import TelegramBotConfig, ConfigType
from app.models.telegram_bot_log import TelegramBotLog, LogStatus
from app.models.merchant_telegram_setting import MerchantTelegramSetting

# 审计和安全相关模型
from app.models.system_settings import SystemSettings
from app.models.ip_whitelist import IpWhitelist
from app.models.audit import AuditLog, AuditEventType, AuditLevel
from app.models.migration_log import MigrationLog

# 双因子认证模型
from app.models.totp import TOTPLog, TOTPPolicy

# 导入关联表
from app.models.associations import (
    user_roles,
    user_permissions,
    role_permissions,
    role_menus,
    menu_permissions,
)

# 导出所有模型
__all__ = [
    # 基础模型
    "BaseModel",
    "TimestampMixin",
    # 核心模型
    "User",
    "Role",
    "Permission",
    "Menu",
    "Merchant",
    # 新的组织架构模型
    "Department",
    "OrganizationRelation",
    "UserOrganization",
    # 业务模型
    "CardRecord",
    "CardStatus",
    "BindingLog",
    "LogLevel",
    "LogType",
    "Notification",
    "NotificationType",
    "NotificationStatus",
    "WalmartServer",
    "WalmartCK",
    # Telegram机器人模型
    "TelegramGroup",
    "ChatType",
    "BindStatus",
    "TelegramUser",
    "VerificationStatus",
    "TelegramBotConfig",
    "ConfigType",
    "TelegramBotLog",
    "LogStatus",
    "MerchantTelegramSetting",
    # 审计和安全相关
    "SystemSettings",
    "IpWhitelist",
    "AuditLog",
    "AuditEventType",
    "AuditLevel",
    "MigrationLog",
    # 双因子认证
    "TOTPLog",
    "TOTPPolicy",
    # 关联表
    "user_roles",
    "user_permissions",
    "role_permissions",
    "role_menus",
    "menu_permissions",
]
