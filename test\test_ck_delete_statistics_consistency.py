#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
CK删除功能对统计数据一致性影响的验证测试
"""

import requests
import json
import uuid
import time
from datetime import datetime

BASE_URL = "http://localhost:20000/api/v1"

class CKDeleteStatisticsTest:
    def __init__(self):
        self.admin_token = None
        self.test_ck_ids = []
        self.test_card_records = []
        
    def login(self):
        """登录获取token"""
        login_data = {
            'username': 'admin',
            'password': '7c222fb2927d828af22f592134e8932480637c0d'
        }
        
        response = requests.post(
            f"{BASE_URL}/auth/login",
            data=login_data,
            headers={'Content-Type': 'application/x-www-form-urlencoded'}
        )
        
        if response.status_code == 200:
            self.admin_token = response.json()['data']['access_token']
            print("✅ 登录成功")
            return True
        else:
            print(f"❌ 登录失败: {response.text}")
            return False
    
    def create_test_ck(self, suffix=""):
        """创建测试CK"""
        test_data = {
            "sign": f"test_stats_{uuid.uuid4().hex[:8]}{suffix}@token#signature#26",
            "daily_limit": 100,
            "hourly_limit": 50,
            "active": 1,
            "description": f"统计测试CK{suffix}",
            "merchant_id": 1,
            "department_id": 1
        }
        
        headers = {'Authorization': f'Bearer {self.admin_token}'}
        response = requests.post(f"{BASE_URL}/walmart-ck", json=test_data, headers=headers)
        
        if response.status_code == 200:
            response_data = response.json()
            if 'data' in response_data and 'data' in response_data['data']:
                ck_id = response_data['data']['data']['id']
            else:
                ck_id = response_data['data']['id']
            print(f"✅ 创建CK成功，ID: {ck_id}")
            self.test_ck_ids.append(ck_id)
            return ck_id
        else:
            print(f"❌ 创建CK失败: {response.text}")
            return None
    
    def create_test_card_record(self, ck_id):
        """创建测试绑卡记录"""
        test_data = {
            "merchant_order_id": f"test_order_{uuid.uuid4().hex[:8]}",
            "amount": 1000,  # 10元
            "card_number": f"test_card_{uuid.uuid4().hex[:8]}",
            "card_password": "test_password",
            "walmart_ck_id": ck_id
        }
        
        headers = {'Authorization': f'Bearer {self.admin_token}'}
        response = requests.post(f"{BASE_URL}/card-records", json=test_data, headers=headers)
        
        if response.status_code == 200:
            response_data = response.json()
            record_id = response_data['data']['id']
            print(f"✅ 创建绑卡记录成功，ID: {record_id}")
            self.test_card_records.append(record_id)
            return record_id
        else:
            print(f"❌ 创建绑卡记录失败: {response.text}")
            return None
    
    def get_statistics(self):
        """获取统计数据"""
        headers = {'Authorization': f'Bearer {self.admin_token}'}
        
        # 获取CK统计
        ck_stats_response = requests.get(f"{BASE_URL}/walmart-ck/statistics/1", headers=headers)
        
        # 获取绑卡金额统计
        amount_stats_response = requests.get(f"{BASE_URL}/walmart-ck/binding-amount-statistics", headers=headers)
        
        ck_stats = None
        amount_stats = None
        
        if ck_stats_response.status_code == 200:
            ck_stats = ck_stats_response.json()['data']
        
        if amount_stats_response.status_code == 200:
            amount_stats = amount_stats_response.json()['data']
        
        return ck_stats, amount_stats
    
    def delete_ck(self, ck_id):
        """删除CK"""
        headers = {'Authorization': f'Bearer {self.admin_token}'}
        response = requests.delete(f"{BASE_URL}/walmart-ck/{ck_id}", headers=headers)
        
        if response.status_code == 200:
            print(f"✅ 删除CK成功，ID: {ck_id}")
            return True
        else:
            print(f"❌ 删除CK失败，ID: {ck_id}, 响应: {response.text}")
            return False
    
    def run_test(self):
        """运行完整测试"""
        print("🧪 开始CK删除统计一致性测试")
        print("="*60)
        
        # 1. 登录
        if not self.login():
            return False
        
        # 2. 获取初始统计数据
        print("\n--- 获取初始统计数据 ---")
        initial_ck_stats, initial_amount_stats = self.get_statistics()
        
        if initial_ck_stats:
            initial_success_count = initial_ck_stats.get('actual_success_count', 0)
            print(f"初始绑卡成功数: {initial_success_count}")
        
        if initial_amount_stats:
            initial_total_amount = initial_amount_stats.get('summary', {}).get('total_actual_amount', 0)
            initial_total_records = initial_amount_stats.get('summary', {}).get('total_success', 0)
            print(f"初始绑卡金额: {initial_total_amount}分")
            print(f"初始成功记录数: {initial_total_records}")
        
        # 3. 创建测试CK和绑卡记录
        print("\n--- 创建测试数据 ---")
        test_cks = []
        for i in range(3):
            ck_id = self.create_test_ck(f"_{i}")
            if ck_id:
                test_cks.append(ck_id)
                # 为每个CK创建一个成功的绑卡记录
                record_id = self.create_test_card_record(ck_id)
                if record_id:
                    # 模拟绑卡成功，更新记录状态
                    self.update_card_record_status(record_id, 'success', 1000)
        
        # 等待数据处理
        time.sleep(2)
        
        # 4. 获取创建后的统计数据
        print("\n--- 获取创建后统计数据 ---")
        after_create_ck_stats, after_create_amount_stats = self.get_statistics()
        
        if after_create_ck_stats:
            after_create_success_count = after_create_ck_stats.get('actual_success_count', 0)
            print(f"创建后绑卡成功数: {after_create_success_count}")
        
        if after_create_amount_stats:
            after_create_total_amount = after_create_amount_stats.get('summary', {}).get('total_actual_amount', 0)
            after_create_total_records = after_create_amount_stats.get('summary', {}).get('total_success', 0)
            print(f"创建后绑卡金额: {after_create_total_amount}分")
            print(f"创建后成功记录数: {after_create_total_records}")
        
        # 5. 删除部分CK
        print("\n--- 删除部分CK ---")
        deleted_cks = test_cks[:2]  # 删除前两个CK
        for ck_id in deleted_cks:
            self.delete_ck(ck_id)
        
        # 等待数据处理
        time.sleep(2)
        
        # 6. 获取删除后的统计数据
        print("\n--- 获取删除后统计数据 ---")
        after_delete_ck_stats, after_delete_amount_stats = self.get_statistics()
        
        if after_delete_ck_stats:
            after_delete_success_count = after_delete_ck_stats.get('actual_success_count', 0)
            print(f"删除后绑卡成功数: {after_delete_success_count}")
        
        if after_delete_amount_stats:
            after_delete_total_amount = after_delete_amount_stats.get('summary', {}).get('total_actual_amount', 0)
            after_delete_total_records = after_delete_amount_stats.get('summary', {}).get('total_success', 0)
            print(f"删除后绑卡金额: {after_delete_total_amount}分")
            print(f"删除后成功记录数: {after_delete_total_records}")
        
        # 7. 验证数据一致性
        print("\n--- 验证数据一致性 ---")
        success = True
        
        # 验证绑卡成功数一致性
        if after_create_ck_stats and after_delete_ck_stats:
            if after_create_success_count == after_delete_success_count:
                print("✅ 绑卡成功数一致性验证通过")
            else:
                print(f"❌ 绑卡成功数不一致：创建后{after_create_success_count}，删除后{after_delete_success_count}")
                success = False
        
        # 验证绑卡金额一致性
        if after_create_amount_stats and after_delete_amount_stats:
            if after_create_total_amount == after_delete_total_amount:
                print("✅ 绑卡金额一致性验证通过")
            else:
                print(f"❌ 绑卡金额不一致：创建后{after_create_total_amount}分，删除后{after_delete_total_amount}分")
                success = False
            
            if after_create_total_records == after_delete_total_records:
                print("✅ 成功记录数一致性验证通过")
            else:
                print(f"❌ 成功记录数不一致：创建后{after_create_total_records}，删除后{after_delete_total_records}")
                success = False
        
        # 8. 清理测试数据
        print("\n--- 清理测试数据 ---")
        self.cleanup()
        
        # 9. 测试结果
        print("\n--- 测试结果 ---")
        if success:
            print("🎉 CK删除统计一致性测试通过！")
        else:
            print("❌ CK删除统计一致性测试失败！")
        
        return success
    
    def update_card_record_status(self, record_id, status, actual_amount):
        """更新绑卡记录状态（模拟绑卡成功）"""
        # 这里需要直接操作数据库或调用内部API
        # 简化处理，假设记录已经是成功状态
        pass
    
    def cleanup(self):
        """清理测试数据"""
        # 删除剩余的测试CK
        remaining_cks = [ck_id for ck_id in self.test_ck_ids if ck_id not in []]
        for ck_id in remaining_cks:
            try:
                self.delete_ck(ck_id)
            except:
                pass

if __name__ == "__main__":
    test = CKDeleteStatisticsTest()
    test.run_test()
