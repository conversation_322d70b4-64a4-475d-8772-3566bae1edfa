"""
审计日志CRUD操作
"""
from typing import List, Optional, Dict, Any, Tuple
from sqlalchemy.orm import Session
from sqlalchemy import desc, and_, func
from datetime import datetime, date, timedelta

from app.crud.base import CRUDBase
from app.models.audit import AuditLog, AuditEventType, AuditLevel
from app.schemas.audit import AuditLogCreate, AuditLogUpdate
from app.core.query_filters import StandardQueryFilter


class CRUDAuditLog(CRUDBase[AuditLog, AuditLogCreate, AuditLogUpdate]):
    """审计日志CRUD操作类"""

    def create_audit_log(
        self,
        db: Session,
        *,
        event_type: AuditEventType,
        level: AuditLevel,
        user_id: Optional[int] = None,
        operator_id: Optional[int] = None,
        merchant_id: Optional[int] = None,
        resource_type: Optional[str] = None,
        resource_id: Optional[str] = None,
        action: str,
        ip_address: Optional[str] = None,
        user_agent: Optional[str] = None,
        request_method: Optional[str] = None,
        request_path: Optional[str] = None,
        request_params: Optional[Dict[str, Any]] = None,
        message: str,
        details: Optional[Dict[str, Any]] = None,
        trace_id: Optional[str] = None,
    ) -> AuditLog:
        """创建审计日志记录"""
        audit_data = {
            "event_type": event_type,
            "level": level,
            "user_id": user_id,
            "operator_id": operator_id,
            "merchant_id": merchant_id,
            "resource_type": resource_type,
            "resource_id": resource_id,
            "action": action,
            "ip_address": ip_address,
            "user_agent": user_agent,
            "request_method": request_method,
            "request_path": request_path,
            "request_params": request_params,
            "message": message,
            "details": details,
            "trace_id": trace_id,
        }
        
        db_obj = AuditLog(**audit_data)
        db.add(db_obj)
        db.commit()
        db.refresh(db_obj)
        return db_obj

    def get_by_user(
        self,
        db: Session,
        user_id: int,
        *,
        skip: int = 0,
        limit: int = 100,
        start_date: Optional[date] = None,
        end_date: Optional[date] = None,
    ) -> List[AuditLog]:
        """获取用户的审计日志"""
        query = db.query(self.model).filter(self.model.user_id == user_id)
        
        if start_date:
            query = query.filter(func.date(self.model.created_at) >= start_date)
        if end_date:
            query = query.filter(func.date(self.model.created_at) <= end_date)
        
        return query.order_by(desc(self.model.created_at)).offset(skip).limit(limit).all()

    def get_by_merchant(
        self,
        db: Session,
        merchant_id: int,
        *,
        skip: int = 0,
        limit: int = 100,
        event_type: Optional[AuditEventType] = None,
        level: Optional[AuditLevel] = None,
    ) -> List[AuditLog]:
        """获取商户的审计日志"""
        query = db.query(self.model).filter(self.model.merchant_id == merchant_id)
        
        if event_type:
            query = query.filter(self.model.event_type == event_type)
        if level:
            query = query.filter(self.model.level == level)
        
        return query.order_by(desc(self.model.created_at)).offset(skip).limit(limit).all()

    def get_security_events(
        self,
        db: Session,
        *,
        skip: int = 0,
        limit: int = 100,
        hours: int = 24,
    ) -> List[AuditLog]:
        """获取安全相关事件"""
        since_time = datetime.now() - timedelta(hours=hours)
        
        query = db.query(self.model).filter(
            and_(
                self.model.event_type.in_([
                    AuditEventType.LOGIN,
                    AuditEventType.LOGOUT,
                    AuditEventType.PERMISSION_DENIED,
                    AuditEventType.SECURITY_VIOLATION,
                ]),
                self.model.created_at >= since_time,
            )
        )
        
        return query.order_by(desc(self.model.created_at)).offset(skip).limit(limit).all()

    def get_multi_with_filters_and_count(
        self,
        db: Session,
        *,
        skip: int = 0,
        limit: int = 100,
        filters: Optional[Dict[str, Any]] = None,
    ) -> Tuple[List[AuditLog], int]:
        """根据过滤条件获取审计日志列表和总数"""
        query = db.query(self.model)

        # 使用通用过滤器处理基本过滤条件
        basic_filters = self._extract_basic_filters(filters)
        filter_handler = StandardQueryFilter(self.model)
        query = filter_handler.apply_filters(query, basic_filters)

        # 处理特殊过滤条件
        query = self._apply_special_filters(query, filters)

        # 获取总数
        total = query.count()

        # 获取分页数据
        logs = query.order_by(desc(self.model.created_at)).offset(skip).limit(limit).all()

        return logs, total

    def _extract_basic_filters(self, filters: Optional[Dict[str, Any]]) -> Dict[str, Any]:
        """提取基本过滤条件"""
        if not filters:
            return {}

        basic_filter_keys = [
            'user_id', 'merchant_id', 'event_type', 'level',
            'resource_type', 'ip_address'
        ]
        return {key: value for key, value in filters.items() if key in basic_filter_keys}

    def _apply_special_filters(self, query, filters: Optional[Dict[str, Any]]):
        """应用特殊过滤条件"""
        if not filters:
            return query

        # 操作模糊查询
        if "action" in filters:
            query = query.filter(self.model.action.like(f"%{filters['action']}%"))

        # 时间范围过滤
        query = self._apply_date_filters(query, filters)

        return query

    def _apply_date_filters(self, query, filters: Dict[str, Any]):
        """应用日期过滤条件"""
        if "start_date" in filters and filters["start_date"]:
            query = query.filter(func.date(self.model.created_at) >= filters["start_date"])
        if "end_date" in filters and filters["end_date"]:
            query = query.filter(func.date(self.model.created_at) <= filters["end_date"])
        return query

    def cleanup_old_logs(self, db: Session, days: int = 90) -> int:
        """清理旧的审计日志"""
        cutoff_date = datetime.now() - timedelta(days=days)
        
        count = db.query(self.model).filter(
            self.model.created_at < cutoff_date
        ).count()
        
        db.query(self.model).filter(
            self.model.created_at < cutoff_date
        ).delete()
        
        db.commit()
        return count

    def get_statistics(
        self,
        db: Session,
        *,
        start_date: Optional[date] = None,
        end_date: Optional[date] = None,
        merchant_id: Optional[int] = None,
    ) -> Dict[str, Any]:
        """获取审计日志统计信息 - 优化版本，使用单次聚合查询避免N+1查询"""
        # 构建基础查询
        base_query = db.query(self.model)

        if merchant_id:
            base_query = base_query.filter(self.model.merchant_id == merchant_id)
        if start_date:
            base_query = base_query.filter(func.date(self.model.created_at) >= start_date)
        if end_date:
            base_query = base_query.filter(func.date(self.model.created_at) <= end_date)

        # 使用单次聚合查询获取所有统计信息
        stats_query = base_query.with_entities(
            func.count(self.model.id).label('total_count'),
            # 按事件类型统计
            func.sum(func.case((self.model.event_type == AuditEventType.USER_LOGIN, 1), else_=0)).label('user_login_count'),
            func.sum(func.case((self.model.event_type == AuditEventType.USER_LOGOUT, 1), else_=0)).label('user_logout_count'),
            func.sum(func.case((self.model.event_type == AuditEventType.DATA_CREATE, 1), else_=0)).label('data_create_count'),
            func.sum(func.case((self.model.event_type == AuditEventType.DATA_UPDATE, 1), else_=0)).label('data_update_count'),
            func.sum(func.case((self.model.event_type == AuditEventType.DATA_DELETE, 1), else_=0)).label('data_delete_count'),
            func.sum(func.case((self.model.event_type == AuditEventType.PERMISSION_CHANGE, 1), else_=0)).label('permission_change_count'),
            func.sum(func.case((self.model.event_type == AuditEventType.SYSTEM_CONFIG, 1), else_=0)).label('system_config_count'),
            func.sum(func.case((self.model.event_type == AuditEventType.SECURITY_EVENT, 1), else_=0)).label('security_event_count'),
            func.sum(func.case((self.model.event_type == AuditEventType.API_ACCESS, 1), else_=0)).label('api_access_count'),
            func.sum(func.case((self.model.event_type == AuditEventType.CARD_BINDING, 1), else_=0)).label('card_binding_count'),
            # 按级别统计
            func.sum(func.case((self.model.level == AuditLevel.INFO, 1), else_=0)).label('info_count'),
            func.sum(func.case((self.model.level == AuditLevel.WARNING, 1), else_=0)).label('warning_count'),
            func.sum(func.case((self.model.level == AuditLevel.ERROR, 1), else_=0)).label('error_count'),
            func.sum(func.case((self.model.level == AuditLevel.CRITICAL, 1), else_=0)).label('critical_count'),
        ).first()

        # 构建事件类型统计字典
        event_stats = {
            AuditEventType.USER_LOGIN.value: stats_query.user_login_count or 0,
            AuditEventType.USER_LOGOUT.value: stats_query.user_logout_count or 0,
            AuditEventType.DATA_CREATE.value: stats_query.data_create_count or 0,
            AuditEventType.DATA_UPDATE.value: stats_query.data_update_count or 0,
            AuditEventType.DATA_DELETE.value: stats_query.data_delete_count or 0,
            AuditEventType.PERMISSION_CHANGE.value: stats_query.permission_change_count or 0,
            AuditEventType.SYSTEM_CONFIG.value: stats_query.system_config_count or 0,
            AuditEventType.SECURITY_EVENT.value: stats_query.security_event_count or 0,
            AuditEventType.API_ACCESS.value: stats_query.api_access_count or 0,
            AuditEventType.CARD_BINDING.value: stats_query.card_binding_count or 0,
        }

        # 构建级别统计字典
        level_stats = {
            AuditLevel.INFO.value: stats_query.info_count or 0,
            AuditLevel.WARNING.value: stats_query.warning_count or 0,
            AuditLevel.ERROR.value: stats_query.error_count or 0,
            AuditLevel.CRITICAL.value: stats_query.critical_count or 0,
        }

        return {
            "total_count": stats_query.total_count or 0,
            "event_type_stats": event_stats,
            "level_stats": level_stats,
            "start_date": start_date,
            "end_date": end_date,
        }


# 创建全局实例
audit_log = CRUDAuditLog(AuditLog)
