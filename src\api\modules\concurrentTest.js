import axios from "axios";
import CryptoJS from "crypto-js";

/**
 * 并发测试API模块
 * 专门用于处理并发绑卡测试相关的API调用
 */

/**
 * 生成API签名 - 与绑卡页面保持完全一致
 * @param {Object} data - 请求数据
 * @param {string} secret - 密钥
 * @param {string} timestamp - 时间戳
 * @param {string} nonce - 随机数
 * @param {string} method - HTTP方法
 * @param {string} path - API路径
 * @param {boolean} debug - 是否启用调试
 * @returns {string} 签名
 */
export const generateApiSignature = (
  data,
  secret,
  timestamp,
  nonce,
  method = "POST",
  path = "/api/v1/card-bind",
  debug = false
) => {
  // 1. 规范化请求参数
  const normalizedData = {};

  // 只包含非空值，按键名排序
  Object.keys(data)
    .sort()
    .forEach((key) => {
      if (data[key] !== null && data[key] !== undefined && data[key] !== "") {
        normalizedData[key] = data[key];
      }
    });

  // 2. 创建规范化的JSON字符串（与后端完全一致）
  // 使用与Python json.dumps(separators=(',', ':'), sort_keys=True, ensure_ascii=False)完全一致的方法
  // 重要：必须使用紧凑格式且不转义Unicode字符
  const jsonStr = JSON.stringify(
    normalizedData,
    Object.keys(normalizedData).sort()
  )
    .replace(/\s+/g, "") // 移除所有空白字符
    .replace(/\\u[\da-f]{4}/gi, (match) => {
      // 将Unicode转义序列转换回原始字符（与ensure_ascii=False一致）
      return String.fromCharCode(parseInt(match.replace("\\u", ""), 16));
    });

  // 3. 构建签名字符串（使用竖线分隔，与后端一致）
  const signatureComponents = [
    method.toUpperCase(),
    path,
    timestamp,
    nonce,
    jsonStr,
    secret,
  ];
  const signString = signatureComponents.join("|");

  // 4. 使用HMAC-SHA256生成签名并Base64编码（与后端一致）
  const hmacDigest = CryptoJS.HmacSHA256(signString, secret);
  const signature = CryptoJS.enc.Base64.stringify(hmacDigest);

  // 调试信息
  if (debug) {
    console.log("=== 并发测试签名调试信息 ===");
    console.log("原始数据:", data);
    console.log("规范化数据:", normalizedData);
    console.log("JSON字符串:", jsonStr);
    console.log("JSON字符串长度:", jsonStr.length);
    console.log("签名字符串:", signString);
    console.log("签名字符串长度:", signString.length);
    console.log("生成的签名:", signature);
    console.log("时间戳:", timestamp);
    console.log("随机数:", nonce);
    console.log("==============================");
  }

  return signature;
};

/**
 * 生成测试卡号
 * @param {string} prefix - 卡号前缀
 * @param {number} index - 索引
 * @returns {string} 卡号
 */
export const generateTestCardNumber = (prefix, index) => {
  const suffix = String(index).padStart(8, "0");
  return `${prefix}${suffix}`;
};

/**
 * 生成测试订单号
 * @param {number} index - 索引
 * @returns {string} 订单号
 */
export const generateTestOrderId = (index) => {
  const timestamp = Date.now();
  return `TEST_${timestamp}_${String(index).padStart(4, "0")}`;
};

/**
 * 发送单个绑卡请求
 * @param {Object} config - 请求配置
 * @returns {Promise<Object>} 请求结果
 */
export const sendSingleBindRequest = async (config) => {
  const {
    cardNumber,
    orderNumber,
    index,
    apiKey,
    secretKey,
    merchantCode,
    amount,
    debugMode,
    timeout,
  } = config;

  const startTime = Date.now();

  try {
    // 准备请求数据
    // 生成时间戳和随机数（确保并发请求的唯一性）
    const requestStartTime = Date.now();
    const timestamp = requestStartTime.toString();
    // 增强随机数生成：时间戳 + 索引 + 随机数，确保并发请求的唯一性
    const nonce = `${requestStartTime}_${index}_${Math.random()
      .toString(36)
      .substring(2, 15)}`;

    const requestData = {
      card_number: cardNumber,
      card_password: "123456", // 测试密码
      merchant_code: merchantCode,
      merchant_order_id: orderNumber,
      amount: amount,
      debug: debugMode, // 关键：debug标识
    };

    // 生成签名（在开发环境或前几个请求启用调试）
    const isDebug = process.env.NODE_ENV === "development" && index < 3;
    const signature = generateApiSignature(
      requestData,
      secretKey,
      timestamp,
      nonce,
      "POST",
      "/api/v1/card-bind",
      isDebug
    );

    // 准备请求头
    const headers = {
      "Content-Type": "application/json",
      "api-key": apiKey,
      "X-Timestamp": timestamp,
      "X-Nonce": nonce,
      "X-Signature": signature,
    };

    // 发送请求
    const response = await axios.post(
      `${window.APP_CONFIG?.API_BASE_URL}${window.APP_CONFIG?.API_PATH}/card-bind`,
      requestData,
      {
        headers,
        timeout: timeout || 30000,
      }
    );

    const endTime = Date.now();
    const responseTime = endTime - startTime;

    return {
      index: index + 1,
      cardNumber,
      orderNumber,
      success: true,
      responseTime,
      debugMode,
      timestamp: startTime,
      data: response.data,
      message: "请求成功",
      statusCode: response.status,
    };
  } catch (error) {
    const endTime = Date.now();
    const responseTime = endTime - startTime;

    // 如果是签名验证失败，记录详细信息
    if (error.response?.data?.detail === "签名验证失败") {
      console.error("=== 并发测试签名验证失败详细信息 ===");
      console.error("请求索引:", index);
      console.error("卡号:", cardNumber);
      console.error("订单号:", orderNumber);
      console.error("请求数据:", {
        card_number: cardNumber,
        card_password: "123456",
        merchant_code: merchantCode,
        merchant_order_id: orderNumber,
        amount: amount,
        debug: debugMode,
      });
      console.error("API密钥:", apiKey);
      console.error("密钥前8位:", secretKey?.substring(0, 8) + "***");
      console.error("商户编码:", merchantCode);
      console.error("==========================================");
    }

    return {
      index: index + 1,
      cardNumber,
      orderNumber,
      success: false,
      responseTime,
      debugMode,
      timestamp: startTime,
      error: error.response?.data?.detail || error.message || "请求失败",
      statusCode: error.response?.status || 0,
    };
  }
};

/**
 * 执行并发绑卡测试
 * @param {Object} testConfig - 测试配置
 * @param {Function} onProgress - 进度回调
 * @returns {Promise<Array>} 测试结果
 */
export const executeConcurrentTest = async (testConfig, onProgress) => {
  const {
    concurrentCount,
    debugMode,
    cardPrefix,
    amount,
    requestDelay,
    timeout,
    apiKey,
    secretKey,
    merchantCode,
  } = testConfig;

  console.log(
    `🚀 开始并发测试: ${concurrentCount}个请求, 延迟: ${requestDelay}ms, Debug模式: ${debugMode}`
  );

  const testStartTime = Date.now();

  // 生成所有请求配置（预先生成，确保真正并发）
  const requestConfigs = [];
  for (let i = 0; i < concurrentCount; i++) {
    const cardNumber = generateTestCardNumber(cardPrefix, i);
    const orderNumber = generateTestOrderId(i);

    requestConfigs.push({
      cardNumber,
      orderNumber,
      index: i,
      apiKey,
      secretKey,
      merchantCode,
      amount,
      debugMode,
      timeout,
    });
  }

  // 创建并发请求数组
  const requests = [];

  if (requestDelay > 0) {
    // 有延迟的并发：使用setTimeout但仍然是并发的
    console.log(`⏰ 使用延迟并发模式，每个请求间隔 ${requestDelay}ms`);

    for (let i = 0; i < concurrentCount; i++) {
      const delay = i * requestDelay;
      requests.push(
        new Promise((resolve) => {
          setTimeout(async () => {
            console.log(
              `📤 发起请求 ${i + 1}/${concurrentCount} (延迟 ${delay}ms)`
            );
            const result = await sendSingleBindRequest(requestConfigs[i]);
            if (onProgress) onProgress(result);
            resolve(result);
          }, delay);
        })
      );
    }
  } else {
    // 真正的并发：所有请求同时发起
    console.log(`⚡ 使用真并发模式，所有请求同时发起`);

    for (let i = 0; i < concurrentCount; i++) {
      requests.push(
        sendSingleBindRequest(requestConfigs[i])
          .then((result) => {
            console.log(
              `✅ 请求 ${i + 1}/${concurrentCount} 完成: ${
                result.success ? "成功" : "失败"
              } (${result.responseTime}ms)`
            );
            if (onProgress) onProgress(result);
            return result;
          })
          .catch((error) => {
            console.error(`❌ 请求 ${i + 1}/${concurrentCount} 异常:`, error);
            const errorResult = {
              index: i + 1,
              cardNumber: requestConfigs[i].cardNumber,
              orderNumber: requestConfigs[i].orderNumber,
              success: false,
              responseTime: 0,
              debugMode,
              timestamp: Date.now(),
              error: error.message || "请求异常",
              statusCode: 0,
            };
            if (onProgress) onProgress(errorResult);
            return errorResult;
          })
      );
    }
  }

  console.log(`🔄 开始执行 ${requests.length} 个并发请求...`);

  // 执行并发请求 - 这是关键的并发执行点
  const results = await Promise.allSettled(requests);

  const testEndTime = Date.now();
  const totalTestTime = testEndTime - testStartTime;

  console.log(`🎉 并发测试完成! 总耗时: ${totalTestTime}ms`);

  // 处理结果并添加并发性分析
  const processedResults = results.map((result, index) => {
    if (result.status === "fulfilled") {
      return result.value;
    } else {
      console.error(`❌ Promise ${index + 1} 被拒绝:`, result.reason);
      return {
        index: index + 1,
        cardNumber: generateTestCardNumber(cardPrefix, index),
        orderNumber: generateTestOrderId(index),
        success: false,
        responseTime: 0,
        debugMode,
        timestamp: Date.now(),
        error: result.reason?.message || "Promise被拒绝",
        statusCode: 0,
      };
    }
  });

  // 分析并发性（仅在无延迟模式下）
  if (requestDelay === 0 && processedResults.length > 1) {
    const timestamps = processedResults
      .map((r) => r.timestamp)
      .filter((t) => t > 0);
    if (timestamps.length > 1) {
      const minTimestamp = Math.min(...timestamps);
      const maxTimestamp = Math.max(...timestamps);
      const timeSpan = maxTimestamp - minTimestamp;

      console.log(`📊 并发性分析:`);
      console.log(`   请求时间跨度: ${timeSpan}ms`);
      console.log(
        `   ${timeSpan < 100 ? "✅ 真正的并发请求" : "⚠️ 可能存在串行化"}`
      );
    }
  }

  return processedResults;
};

/**
 * 计算测试统计信息
 * @param {Array} results - 测试结果
 * @param {number} totalTime - 总耗时（秒）
 * @returns {Object} 统计信息
 */
export const calculateTestStatistics = (results, totalTime) => {
  if (results.length === 0) {
    return {
      successCount: 0,
      failureCount: 0,
      successRate: 0,
      avgResponseTime: 0,
      minResponseTime: 0,
      maxResponseTime: 0,
      qps: 0,
      totalTime: 0,
    };
  }

  const successCount = results.filter((r) => r.success).length;
  const failureCount = results.length - successCount;
  const successRate = Math.round((successCount / results.length) * 100);

  const responseTimes = results.map((r) => r.responseTime);
  const avgResponseTime = Math.round(
    responseTimes.reduce((a, b) => a + b, 0) / responseTimes.length
  );
  const minResponseTime = Math.min(...responseTimes);
  const maxResponseTime = Math.max(...responseTimes);

  const qps =
    totalTime > 0 ? Math.round((results.length / totalTime) * 100) / 100 : 0;

  return {
    successCount,
    failureCount,
    successRate,
    avgResponseTime,
    minResponseTime,
    maxResponseTime,
    qps,
    totalTime: Math.round(totalTime),
  };
};

/**
 * 导出测试结果为CSV
 * @param {Array} results - 测试结果
 * @param {Object} statistics - 统计信息
 * @returns {string} CSV内容
 */
export const exportResultsToCSV = (results, statistics) => {
  const headers = [
    "序号",
    "卡号",
    "订单号",
    "状态",
    "响应时间(ms)",
    "模式",
    "时间",
    "状态码",
    "错误信息",
  ];

  const rows = results.map((result) => [
    result.index,
    result.cardNumber,
    result.orderNumber,
    result.success ? "成功" : "失败",
    result.responseTime,
    result.debugMode ? "TEST" : "PROD",
    new Date(result.timestamp).toLocaleString(),
    result.statusCode || "",
    result.error || result.message || "",
  ]);

  // 添加统计信息
  rows.push([]);
  rows.push(["统计信息"]);
  rows.push(["总请求数", results.length]);
  rows.push(["成功数", statistics.successCount]);
  rows.push(["失败数", statistics.failureCount]);
  rows.push(["成功率", `${statistics.successRate}%`]);
  rows.push(["平均响应时间", `${statistics.avgResponseTime}ms`]);
  rows.push(["最小响应时间", `${statistics.minResponseTime}ms`]);
  rows.push(["最大响应时间", `${statistics.maxResponseTime}ms`]);
  rows.push(["QPS", statistics.qps]);
  rows.push(["总耗时", `${statistics.totalTime}s`]);

  return [headers, ...rows].map((row) => row.join(",")).join("\n");
};

/**
 * 验证测试配置
 * @param {Object} config - 测试配置
 * @returns {Object} 验证结果
 */
export const validateTestConfig = (config) => {
  const errors = [];

  if (
    !config.concurrentCount ||
    config.concurrentCount < 1 ||
    config.concurrentCount > 100
  ) {
    errors.push("并发数量必须在1-100之间");
  }

  if (!config.cardPrefix || config.cardPrefix.length < 2) {
    errors.push("卡号前缀至少需要2个字符");
  }

  if (!config.amount || config.amount < 100) {
    errors.push("测试金额不能少于100分（1元）");
  }

  if (!config.apiKey) {
    errors.push("API密钥不能为空");
  }

  if (!config.secretKey) {
    errors.push("密钥不能为空");
  }

  if (!config.merchantCode) {
    errors.push("商户编码不能为空");
  }

  return {
    valid: errors.length === 0,
    errors,
  };
};

export default {
  generateApiSignature,
  generateTestCardNumber,
  generateTestOrderId,
  sendSingleBindRequest,
  executeConcurrentTest,
  calculateTestStatistics,
  exportResultsToCSV,
  validateTestConfig,
};
