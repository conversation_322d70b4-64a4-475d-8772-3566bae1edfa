from typing import Optional, List, Any
from pydantic import BaseModel, EmailStr, Field
from datetime import datetime


# 共享属性
class UserBase(BaseModel):
    email: Optional[EmailStr] = None
    username: Optional[str] = None
    full_name: Optional[str] = None
    phone: Optional[str] = None
    is_active: Optional[bool] = True
    merchant_id: Optional[int] = None
    department_id: Optional[int] = None
    remark: Optional[str] = None
    is_superuser: Optional[bool] = False
    # 移除role字段，改用roles多对多关系
    role_ids: Optional[List[int]] = None  # 角色ID列表


# 创建用户时需要的属性
class UserCreate(UserBase):
    username: str
    password: str
    email: Optional[str] = None


# 更新用户时可选的属性
class UserUpdate(UserBase):
    password: Optional[str] = None


# 数据库中的用户属性
class UserInDBBase(UserBase):
    id: int
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


# API响应中的用户
class User(UserInDBBase):
    last_login_ip: Optional[str] = None
    last_login_time: Optional[str] = None


# 数据库中完整的用户信息
class UserInDB(UserInDBBase):
    hashed_password: str
    api_key: Optional[str] = None
    api_secret: Optional[str] = None
    sign: Optional[str] = None
    daily_bind_limit: Optional[int] = None
    hourly_bind_limit: Optional[int] = None
    last_login_ip: Optional[str] = None
    last_login_time: Optional[str] = None


# 用于用户列表响应的模型
class UserListResponse(BaseModel):
    items: List[User]
    total: int
    page: int
    page_size: int


# 修改密码请求模型
class PasswordChange(BaseModel):
    current_password: str = Field(..., description="当前密码", alias="currentPassword")
    new_password: str = Field(..., description="新密码", min_length=6, alias="newPassword")

    class Config:
        from_attributes = True
        populate_by_name = True  # 允许使用字段名和别名


# Schema for merchant user list response
class MerchantUserListResponse(BaseModel):
    items: List[User]
    total: int


# Schema for updating user status
class UserStatusUpdate(BaseModel):
    is_active: bool = Field(..., description="用户是否激活")
