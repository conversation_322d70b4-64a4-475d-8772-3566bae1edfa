"""
AO架构数据库迁移脚本
"""
import logging
from sqlalchemy import text
from sqlalchemy.orm import Session
from sqlalchemy.exc import OperationalError

logger = logging.getLogger(__name__)


def migrate_ao_architecture(db: Session) -> bool:
    """
    执行AO架构数据库迁移
    
    Args:
        db: 数据库会话
        
    Returns:
        bool: 迁移是否成功
    """
    try:
        logger.info("开始执行AO架构数据库迁移...")
        
        # 1. 检查并添加users表的position字段
        try:
            db.execute(text("SELECT position FROM users LIMIT 1"))
            logger.info("users.position字段已存在")
        except OperationalError:
            logger.info("添加users.position字段...")
            db.execute(text("""
                ALTER TABLE users 
                ADD COLUMN position VARCHAR(100) NULL COMMENT '职位' 
                AFTER merchant_id
            """))
            db.commit()
            logger.info("users.position字段添加成功")
        
        # 2. 检查并创建departments表
        try:
            db.execute(text("SELECT COUNT(*) FROM departments LIMIT 1"))
            logger.info("departments表已存在")
        except OperationalError:
            logger.info("创建departments表...")
            db.execute(text("""
                CREATE TABLE IF NOT EXISTS `departments` (
                    `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '部门ID',
                    `merchant_id` int(11) NOT NULL COMMENT '所属商户ID',
                    `name` varchar(100) NOT NULL COMMENT '部门名称',
                    `code` varchar(50) NOT NULL COMMENT '部门代码',
                    `description` text NULL COMMENT '部门描述',
                    `status` tinyint(1) NOT NULL DEFAULT 1 COMMENT '状态：1启用，0禁用',

                    -- 层级关系（支持无限层级嵌套：商户→部门→子部门→...）
                    `parent_id` int(11) NULL COMMENT '父部门ID（支持多层级嵌套，NULL表示一级部门）',
                    `level` int(11) NOT NULL DEFAULT 1 COMMENT '部门层级：1一级部门，2二级部门，3三级部门...',
                    `path` varchar(500) NULL COMMENT '部门路径，如：/merchant_id/dept1_id/dept2_id/',
                    `sort_order` int(11) NOT NULL DEFAULT 0 COMMENT '排序号',

                    -- 负责人信息
                    `manager_id` int(11) NULL COMMENT '部门负责人ID',
                    `manager_name` varchar(100) NULL COMMENT '部门负责人姓名',
                    `manager_phone` varchar(50) NULL COMMENT '负责人电话',
                    `manager_email` varchar(100) NULL COMMENT '负责人邮箱',

                    -- 业务配置（继承商户配置）
                    `business_scope` text NULL COMMENT '业务范围',
                    `daily_limit` int(11) NULL COMMENT '每日绑卡上限（继承商户配置）',
                    `hourly_limit` int(11) NULL COMMENT '每小时绑卡上限（继承商户配置）',
                    `custom_config` text NULL COMMENT '自定义配置JSON',
                    `remark` text NULL COMMENT '备注',
                    `created_by` int(11) NULL COMMENT '创建者ID',

                    -- 时间戳
                    `created_at` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '创建时间',
                    `updated_at` datetime(3) NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3) COMMENT '更新时间',

                    PRIMARY KEY (`id`),
                    UNIQUE KEY `uk_departments_merchant_code` (`merchant_id`, `code`),
                    KEY `idx_departments_merchant_id` (`merchant_id`),
                    KEY `idx_departments_parent_id` (`parent_id`),
                    KEY `idx_departments_manager_id` (`manager_id`),
                    KEY `idx_departments_status` (`status`),
                    KEY `idx_departments_level` (`level`),
                    KEY `idx_departments_path` (`path`),
                    KEY `idx_departments_created_at` (`created_at`),
                    CONSTRAINT `fk_departments_merchant` FOREIGN KEY (`merchant_id`) REFERENCES `merchants` (`id`) ON DELETE CASCADE,
                    CONSTRAINT `fk_departments_parent` FOREIGN KEY (`parent_id`) REFERENCES `departments` (`id`) ON DELETE CASCADE,
                    CONSTRAINT `fk_departments_manager` FOREIGN KEY (`manager_id`) REFERENCES `users` (`id`) ON DELETE SET NULL
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='部门表（基于商户的多层级部门结构）'
            """))
            db.commit()
            logger.info("departments表创建成功")
        
        # 3. 检查并创建user_organizations表
        try:
            db.execute(text("SELECT COUNT(*) FROM user_organizations LIMIT 1"))
            logger.info("user_organizations表已存在")
        except OperationalError:
            logger.info("创建user_organizations表...")
            db.execute(text("""
                CREATE TABLE IF NOT EXISTS `user_organizations` (
                    `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '关系ID',
                    `user_id` int(11) NOT NULL COMMENT '用户ID',
                    `merchant_id` int(11) NOT NULL COMMENT '商户ID',
                    `department_id` int(11) NULL COMMENT '部门ID（可为空，表示直属商户）',
                    `position` varchar(100) NULL COMMENT '职位',
                    `is_primary` tinyint(1) NOT NULL DEFAULT 1 COMMENT '是否主要组织关系',
                    `status` tinyint(1) NOT NULL DEFAULT 1 COMMENT '状态：1启用，0禁用',
                    `start_date` date NULL COMMENT '开始日期',
                    `end_date` date NULL COMMENT '结束日期',
                    `created_by` int(11) NULL COMMENT '创建者ID',
                    `created_at` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '创建时间',
                    `updated_at` datetime(3) NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3) COMMENT '更新时间',

                    PRIMARY KEY (`id`),
                    UNIQUE KEY `uk_user_org_primary` (`user_id`, `is_primary`),
                    KEY `idx_user_org_user_id` (`user_id`),
                    KEY `idx_user_org_merchant_id` (`merchant_id`),
                    KEY `idx_user_org_department_id` (`department_id`),
                    KEY `idx_user_org_status` (`status`),
                    KEY `idx_user_org_merchant_dept` (`merchant_id`, `department_id`),
                    CONSTRAINT `fk_user_org_user` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
                    CONSTRAINT `fk_user_org_merchant` FOREIGN KEY (`merchant_id`) REFERENCES `merchants` (`id`) ON DELETE CASCADE,
                    CONSTRAINT `fk_user_org_department` FOREIGN KEY (`department_id`) REFERENCES `departments` (`id`) ON DELETE CASCADE
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户组织关系表（商户+部门二级结构）'
            """))
            db.commit()
            logger.info("user_organizations表创建成功")
        
        # 4. 检查并创建organization_relations表
        try:
            db.execute(text("SELECT COUNT(*) FROM organization_relations LIMIT 1"))
            logger.info("organization_relations表已存在")
        except OperationalError:
            logger.info("创建organization_relations表...")
            db.execute(text("""
                CREATE TABLE IF NOT EXISTS `organization_relations` (
                    `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '关系ID',
                    `ancestor_id` int(11) NOT NULL COMMENT '祖先部门ID',
                    `descendant_id` int(11) NOT NULL COMMENT '后代部门ID',
                    `level` int(11) NOT NULL DEFAULT 0 COMMENT '层级差距：0表示自己，1表示直接子部门',
                    `created_at` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '创建时间',

                    PRIMARY KEY (`id`),
                    UNIQUE KEY `uk_org_relations` (`ancestor_id`, `descendant_id`),
                    KEY `idx_org_relations_ancestor` (`ancestor_id`),
                    KEY `idx_org_relations_descendant` (`descendant_id`),
                    KEY `idx_org_relations_level` (`level`),
                    CONSTRAINT `fk_org_relations_ancestor` FOREIGN KEY (`ancestor_id`) REFERENCES `departments` (`id`) ON DELETE CASCADE,
                    CONSTRAINT `fk_org_relations_descendant` FOREIGN KEY (`descendant_id`) REFERENCES `departments` (`id`) ON DELETE CASCADE
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='组织关系表（部门层级关系）'
            """))
            db.commit()
            logger.info("organization_relations表创建成功")
        
        # 5. 为现有商户创建默认根部门
        logger.info("为现有商户创建默认根部门...")
        db.execute(text("""
            INSERT INTO `departments` (
                `merchant_id`, `name`, `code`, `description`, `status`,
                `parent_id`, `level`, `path`, `sort_order`,
                `manager_id`, `business_scope`, `daily_limit`, `hourly_limit`,
                `remark`, `created_by`, `created_at`, `updated_at`
            )
            SELECT
                m.`id` as `merchant_id`,
                CONCAT(m.`name`, '总部') as `name`,
                'ROOT' as `code`,
                CONCAT('商户【', m.`name`, '】的根部门，负责整体业务管理') as `description`,
                1 as `status`,
                NULL as `parent_id`,
                1 as `level`,
                CONCAT('/m', m.`id`, '/') as `path`,
                0 as `sort_order`,
                NULL as `manager_id`,
                '商户总部，负责整体业务管理和协调' as `business_scope`,
                COALESCE(m.`daily_limit`, 10000) as `daily_limit`,
                COALESCE(m.`hourly_limit`, 1000) as `hourly_limit`,
                '系统自动创建的商户根部门，支持多层级子部门' as `remark`,
                m.`created_by`,
                m.`created_at`,
                m.`updated_at`
            FROM `merchants` m
            WHERE NOT EXISTS (
                SELECT 1 FROM `departments` d
                WHERE d.`merchant_id` = m.`id` AND d.`code` = 'ROOT'
            )
        """))
        db.commit()
        logger.info("默认根部门创建完成")
        
        # 6. 为现有用户创建组织关系
        logger.info("为现有用户创建组织关系...")
        db.execute(text("""
            INSERT INTO `user_organizations` (
                `user_id`, `merchant_id`, `department_id`, `position`,
                `is_primary`, `status`, `start_date`, `created_by`,
                `created_at`, `updated_at`
            )
            SELECT
                u.`id` as `user_id`,
                u.`merchant_id`,
                d.`id` as `department_id`,
                u.`position`,
                1 as `is_primary`,
                1 as `status`,
                CURDATE() as `start_date`,
                u.`id` as `created_by`,
                NOW(3) as `created_at`,
                NOW(3) as `updated_at`
            FROM `users` u
            LEFT JOIN `departments` d ON d.`merchant_id` = u.`merchant_id` AND d.`code` = 'ROOT'
            WHERE u.`merchant_id` IS NOT NULL
            AND NOT EXISTS (
                SELECT 1 FROM `user_organizations` uo
                WHERE uo.`user_id` = u.`id` AND uo.`is_primary` = 1
            )
        """))
        db.commit()
        logger.info("用户组织关系创建完成")
        
        # 7. 创建部门自关联关系
        logger.info("创建部门自关联关系...")
        db.execute(text("""
            INSERT INTO `organization_relations` (
                `ancestor_id`, `descendant_id`, `level`, `created_at`
            )
            SELECT
                d.`id` as `ancestor_id`,
                d.`id` as `descendant_id`,
                0 as `level`,
                NOW(3) as `created_at`
            FROM `departments` d
            WHERE NOT EXISTS (
                SELECT 1 FROM `organization_relations` r
                WHERE r.`ancestor_id` = d.`id` AND r.`descendant_id` = d.`id`
            )
        """))
        db.commit()
        logger.info("部门自关联关系创建完成")
        
        logger.info("AO架构数据库迁移完成！")
        return True
        
    except Exception as e:
        logger.error(f"AO架构数据库迁移失败: {e}")
        db.rollback()
        return False


def check_migration_needed(db: Session) -> bool:
    """
    检查是否需要执行迁移
    
    Args:
        db: 数据库会话
        
    Returns:
        bool: 是否需要迁移
    """
    try:
        # 检查关键表是否存在
        db.execute(text("SELECT position FROM users LIMIT 1"))
        db.execute(text("SELECT COUNT(*) FROM departments LIMIT 1"))
        db.execute(text("SELECT COUNT(*) FROM user_organizations LIMIT 1"))
        db.execute(text("SELECT COUNT(*) FROM organization_relations LIMIT 1"))
        return False  # 所有表都存在，不需要迁移
    except OperationalError:
        return True  # 有表不存在，需要迁移
