import { onMounted, onUnmounted } from 'vue'
import merchantSwitchListener from '@/utils/merchantSwitchListener'
import useMerchantFilter from './useMerchantFilter'

/**
 * 商家切换组合式函数
 * 用于在页面中监听商家切换事件并自动刷新数据
 * @param {Function} fetchDataFunction 获取数据的函数
 * @param {Object} queryParams 查询参数对象 (reactive或ref对象)
 * @param {String} merchantKey 商家ID在查询参数中的键名，默认为'merchant_id'
 * @returns {Object} 包含商家过滤的查询参数和刷新函数
 */
export default function useMerchantSwitch(fetchDataFunction, queryParams, merchantKey = 'merchant_id') {
    const { merchantQueryParams, currentMerchant, currentMerchantId } = useMerchantFilter(queryParams, merchantKey)

    // 带商家过滤的数据获取函数
    const fetchWithMerchantFilter = async () => {
        return await fetchDataFunction(merchantQueryParams.value)
    }

    // 取消注册函数
    let unregister = null

    onMounted(() => {
        // 注册到全局商家切换监听器
        unregister = merchantSwitchListener.registerRefreshFunction(fetchWithMerchantFilter)
    })

    onUnmounted(() => {
        // 取消注册
        if (unregister) {
            unregister()
        }
    })

    return {
        merchantQueryParams,
        currentMerchant,
        currentMerchantId,
        fetchWithMerchantFilter
    }
}
