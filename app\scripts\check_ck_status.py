#!/usr/bin/env python3
"""
CK状态检查脚本

快速检查指定CK在数据库和Redis中的状态
"""

import asyncio
import sys
import os
from typing import Optional

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from sqlalchemy.orm import Session
from app.core.database import SessionLocal
from app.core.redis import get_redis
from app.models.walmart_ck import WalmartCK
from app.core.logging import get_logger

logger = get_logger("ck_status_check")


async def check_ck_status(ck_id: int) -> dict:
    """
    检查指定CK的状态
    
    Args:
        ck_id: CK ID
        
    Returns:
        dict: CK状态信息
    """
    db = SessionLocal()
    redis = await get_redis()
    
    try:
        # 查询数据库中的CK
        ck = db.query(WalmartCK).filter(WalmartCK.id == ck_id).first()
        
        if not ck:
            return {
                'ck_id': ck_id,
                'exists_in_db': False,
                'error': 'CK在数据库中不存在'
            }
        
        # 查询Redis中的状态
        status_key = f"walmart:ck:status:{ck_id}"
        redis_status = await redis.hgetall(status_key)
        
        # 检查商户池
        pool_key = f"walmart:ck:pool:{ck.merchant_id}"
        pool_score = await redis.zscore(pool_key, str(ck_id))
        
        # 检查部门池
        dept_pool_score = None
        if ck.department_id:
            dept_key = f"walmart:ck:dept:{ck.merchant_id}:{ck.department_id}"
            dept_pool_score = await redis.zscore(dept_key, str(ck_id))
        
        # 检查验证缓存
        validation_key = f"walmart:ck:validation:{ck_id}"
        validation_cache = await redis.hgetall(validation_key)
        
        result = {
            'ck_id': ck_id,
            'exists_in_db': True,
            'database': {
                'active': ck.active,
                'is_deleted': ck.is_deleted,
                'bind_count': ck.bind_count,
                'total_limit': ck.total_limit,
                'merchant_id': ck.merchant_id,
                'department_id': ck.department_id,
                'last_bind_time': ck.last_bind_time
            },
            'redis': {
                'status_exists': bool(redis_status),
                'status_data': redis_status,
                'in_merchant_pool': pool_score is not None,
                'merchant_pool_score': pool_score,
                'in_dept_pool': dept_pool_score is not None,
                'dept_pool_score': dept_pool_score,
                'validation_cache': validation_cache
            }
        }
        
        # 分析一致性
        if redis_status:
            redis_active = bool(int(redis_status.get('active', 0)))
            result['consistency'] = {
                'active_status_match': ck.active == redis_active,
                'should_be_in_pool': ck.active and not ck.is_deleted,
                'actually_in_pool': pool_score is not None,
                'pool_consistency': (ck.active and not ck.is_deleted) == (pool_score is not None)
            }
        else:
            result['consistency'] = {
                'redis_missing': True,
                'needs_sync': True
            }
        
        return result
        
    except Exception as e:
        logger.error(f"检查CK {ck_id} 状态失败: {e}")
        return {
            'ck_id': ck_id,
            'error': str(e)
        }
    finally:
        db.close()


async def fix_single_ck(ck_id: int) -> dict:
    """
    修复单个CK的Redis同步问题
    
    Args:
        ck_id: CK ID
        
    Returns:
        dict: 修复结果
    """
    db = SessionLocal()
    redis = await get_redis()
    
    try:
        from app.services.redis_ck_service import CKDataSyncService
        
        # 查询CK
        ck = db.query(WalmartCK).filter(WalmartCK.id == ck_id).first()
        
        if not ck:
            return {
                'ck_id': ck_id,
                'success': False,
                'error': 'CK在数据库中不存在'
            }
        
        # 同步到Redis
        sync_service = CKDataSyncService(redis, db)
        await sync_service.sync_ck_to_redis(ck)
        
        return {
            'ck_id': ck_id,
            'success': True,
            'message': f'CK {ck_id} 已成功同步到Redis'
        }
        
    except Exception as e:
        logger.error(f"修复CK {ck_id} 失败: {e}")
        return {
            'ck_id': ck_id,
            'success': False,
            'error': str(e)
        }
    finally:
        db.close()


def print_ck_status(status: dict):
    """打印CK状态信息"""
    print(f"\n=== CK {status['ck_id']} 状态检查 ===")
    
    if 'error' in status:
        print(f"❌ 错误: {status['error']}")
        return
    
    if not status['exists_in_db']:
        print("❌ CK在数据库中不存在")
        return
    
    # 数据库状态
    db_info = status['database']
    print(f"\n📊 数据库状态:")
    print(f"  活跃状态: {'✅ 启用' if db_info['active'] else '❌ 禁用'}")
    print(f"  是否删除: {'❌ 已删除' if db_info['is_deleted'] else '✅ 未删除'}")
    print(f"  绑卡次数: {db_info['bind_count']}/{db_info['total_limit']}")
    print(f"  商户ID: {db_info['merchant_id']}")
    print(f"  部门ID: {db_info['department_id']}")
    print(f"  最后绑卡时间: {db_info['last_bind_time'] or '无'}")
    
    # Redis状态
    redis_info = status['redis']
    print(f"\n🔄 Redis状态:")
    print(f"  状态数据存在: {'✅ 是' if redis_info['status_exists'] else '❌ 否'}")
    
    if redis_info['status_exists']:
        redis_data = redis_info['status_data']
        redis_active = bool(int(redis_data.get('active', 0)))
        print(f"  活跃状态: {'✅ 启用' if redis_active else '❌ 禁用'}")
        print(f"  绑卡次数: {redis_data.get('bind_count', 'N/A')}")
        print(f"  待处理数: {redis_data.get('pending_count', 'N/A')}")
    
    print(f"  在商户池中: {'✅ 是' if redis_info['in_merchant_pool'] else '❌ 否'}")
    if redis_info['merchant_pool_score'] is not None:
        print(f"  商户池分数: {redis_info['merchant_pool_score']}")
    
    print(f"  在部门池中: {'✅ 是' if redis_info['in_dept_pool'] else '❌ 否'}")
    if redis_info['dept_pool_score'] is not None:
        print(f"  部门池分数: {redis_info['dept_pool_score']}")
    
    # 一致性检查
    if 'consistency' in status:
        consistency = status['consistency']
        print(f"\n🔍 一致性检查:")
        
        if 'redis_missing' in consistency:
            print("  ❌ Redis数据缺失，需要同步")
        else:
            print(f"  活跃状态一致: {'✅ 是' if consistency['active_status_match'] else '❌ 否'}")
            print(f"  池状态一致: {'✅ 是' if consistency['pool_consistency'] else '❌ 否'}")
            
            if not consistency['active_status_match'] or not consistency['pool_consistency']:
                print("  ⚠️  发现不一致，建议执行同步修复")


async def main():
    """主函数"""
    if len(sys.argv) < 2:
        print("用法: python check_ck_status.py <ck_id> [fix]")
        print("  ck_id: 要检查的CK ID")
        print("  fix: 可选参数，如果提供则自动修复同步问题")
        sys.exit(1)
    
    try:
        ck_id = int(sys.argv[1])
        should_fix = len(sys.argv) > 2 and sys.argv[2].lower() == 'fix'
        
        # 检查状态
        status = await check_ck_status(ck_id)
        print_ck_status(status)
        
        # 如果需要修复
        if should_fix and 'error' not in status:
            print(f"\n🔧 正在修复CK {ck_id} 的同步问题...")
            fix_result = await fix_single_ck(ck_id)
            
            if fix_result['success']:
                print(f"✅ {fix_result['message']}")
                
                # 重新检查状态
                print(f"\n🔄 重新检查状态...")
                new_status = await check_ck_status(ck_id)
                print_ck_status(new_status)
            else:
                print(f"❌ 修复失败: {fix_result['error']}")
        
    except ValueError:
        print("❌ 错误: CK ID必须是数字")
        sys.exit(1)
    except Exception as e:
        print(f"❌ 执行失败: {e}")
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())
