#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
部门管理CRUD操作测试
测试部门的创建、读取、更新、删除功能
"""

import sys
import os
import time
import random
import string

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.dirname(__file__))))

from test.conftest import TestBase, get_test_accounts, format_test_result, print_test_summary

class DepartmentsCrudTestSuite(TestBase):
    """部门CRUD操作测试套件"""
    
    def __init__(self):
        super().__init__()
        self.results = []
        self.test_accounts = get_test_accounts()
        self.admin_token = None
        self.merchant_token = None
        self.created_departments = []  # 记录创建的测试部门，用于清理
    
    def setup(self):
        """测试前置设置"""
        print("=== 测试前置设置 ===")
        
        # 管理员登录
        self.admin_token = self.login("admin", "7c222fb2927d828af22f592134e8932480637c0d")
        if not self.admin_token:
            print("❌ 管理员登录失败")
            return False
        
        # 商户管理员登录
        self.merchant_token = self.login("test1", "********")
        if not self.merchant_token:
            print("❌ 商户管理员登录失败")
            return False
        
        print("✅ 测试前置设置完成")
        return True
    
    def generate_test_department_data(self, prefix: str = "test", merchant_id: int = None) -> dict:
        """生成测试部门数据"""
        timestamp = int(time.time())
        random_suffix = ''.join(random.choices(string.ascii_lowercase, k=4))

        data = {
            "name": f"{prefix}_部门_{timestamp}",
            "code": f"{prefix.upper()}_{timestamp}_{random_suffix}",
            "description": f"测试部门描述_{timestamp}",
            "status": True,  # 使用布尔值而不是整数
            "manager_name": f"部门经理_{timestamp}",
            "manager_phone": "***********",
            "manager_email": f"manager_{timestamp}@example.com",
            "business_scope": f"测试业务范围_{timestamp}",
            "remark": f"测试部门备注_{timestamp}"
        }

        # 如果提供了merchant_id，则添加到数据中
        if merchant_id:
            data["merchant_id"] = merchant_id

        return data
    
    def get_test_merchant_id(self):
        """获取测试用的商户ID"""
        if not self.admin_token:
            return None

        # 获取商户列表
        status_code, response = self.make_request("GET", "/merchants", self.admin_token)
        if status_code == 200:
            merchants_data = response.get("data", {})
            merchants = merchants_data.get("items", []) if isinstance(merchants_data, dict) else response.get("items", [])
            if merchants:
                return merchants[0].get("id")
        return None

    def test_create_department(self):
        """测试创建部门"""
        print("\n=== 测试创建部门 ===")

        if not self.admin_token:
            self.results.append(format_test_result(
                "创建部门前置条件",
                False,
                "缺少管理员token"
            ))
            return

        # 获取测试用的商户ID
        merchant_id = self.get_test_merchant_id()
        if not merchant_id:
            self.results.append(format_test_result(
                "创建部门前置条件",
                False,
                "无法获取测试商户ID"
            ))
            return

        # 测试管理员创建部门
        test_department = self.generate_test_department_data("admin_create", merchant_id)
        status_code, response = self.make_request(
            "POST", "/departments", self.admin_token, data=test_department
        )
        
        if status_code == 201 or status_code == 200:
            department_id = response.get("data", {}).get("id") or response.get("id")
            if department_id:
                self.created_departments.append(department_id)
                self.results.append(format_test_result(
                    "管理员创建部门",
                    True,
                    f"成功创建部门: {test_department['name']}",
                    {"department_id": department_id, "department_name": test_department['name']}
                ))
                print(f"✅ 管理员成功创建部门: {test_department['name']}")
            else:
                self.results.append(format_test_result(
                    "管理员创建部门",
                    False,
                    f"创建部门成功但未返回部门ID，状态码: {status_code}"
                ))
                print(f"⚠️ 创建部门成功但未返回部门ID，状态码: {status_code}")
        else:
            self.results.append(format_test_result(
                "管理员创建部门",
                False,
                f"创建部门失败，状态码: {status_code}，响应: {response}"
            ))
            print(f"❌ 管理员创建部门失败，状态码: {status_code}")
        
        # 测试商户管理员创建部门
        test_department_merchant = self.generate_test_department_data("merchant_create", merchant_id)
        status_code, response = self.make_request(
            "POST", "/departments", self.merchant_token, data=test_department_merchant
        )
        
        if status_code == 201 or status_code == 200:
            department_id = response.get("data", {}).get("id") or response.get("id")
            if department_id:
                self.created_departments.append(department_id)
                self.results.append(format_test_result(
                    "商户管理员创建部门",
                    True,
                    f"商户管理员成功创建部门: {test_department_merchant['name']}"
                ))
                print(f"✅ 商户管理员成功创建部门: {test_department_merchant['name']}")
            else:
                self.results.append(format_test_result(
                    "商户管理员创建部门",
                    False,
                    f"创建部门成功但未返回部门ID，状态码: {status_code}"
                ))
                print(f"⚠️ 创建部门成功但未返回部门ID，状态码: {status_code}")
        elif status_code in [403, 401]:
            self.results.append(format_test_result(
                "商户管理员创建部门权限控制",
                True,
                "正确限制商户管理员创建部门权限"
            ))
            print("✅ 正确限制商户管理员创建部门权限")
        else:
            self.results.append(format_test_result(
                "商户管理员创建部门",
                False,
                f"商户管理员创建部门失败，状态码: {status_code}"
            ))
            print(f"❌ 商户管理员创建部门失败，状态码: {status_code}")
    
    def _get_first_merchant_id(self):
        """获取第一个商户ID用于测试"""
        if not self.admin_token:
            return None

        try:
            status_code, response = self.make_request("GET", "/merchants", self.admin_token)
            if status_code == 200:
                merchants_data = response.get("data", response)
                if isinstance(merchants_data, dict):
                    merchants = merchants_data.get("items", [])
                elif isinstance(merchants_data, list):
                    merchants = merchants_data
                else:
                    merchants = []

                if merchants and len(merchants) > 0:
                    return merchants[0].get("id")
            return None
        except Exception as e:
            print(f"获取商户ID失败: {e}")
            return None

    def test_get_departments_list(self):
        """测试获取部门列表"""
        print("\n=== 测试获取部门列表 ===")

        # 获取商户ID用于超级管理员测试
        merchant_id = self._get_first_merchant_id()

        # 测试管理员获取部门列表
        if self.admin_token:
            params = {"merchant_id": merchant_id} if merchant_id else {}
            status_code, response = self.make_request("GET", "/departments", self.admin_token, params=params)
            
            if status_code == 200:
                departments_data = response.get("data", {})
                departments = departments_data.get("items", []) if isinstance(departments_data, dict) else response.get("items", [])
                
                self.results.append(format_test_result(
                    "管理员获取部门列表",
                    True,
                    f"成功获取 {len(departments)} 个部门",
                    {"department_count": len(departments)}
                ))
                print(f"✅ 管理员成功获取部门列表，共 {len(departments)} 个部门")
            else:
                self.results.append(format_test_result(
                    "管理员获取部门列表",
                    False,
                    f"获取部门列表失败，状态码: {status_code}"
                ))
                print(f"❌ 管理员获取部门列表失败，状态码: {status_code}")
        
        # 测试商户管理员获取部门列表
        if self.merchant_token:
            status_code, response = self.make_request("GET", "/departments", self.merchant_token)
            
            if status_code == 200:
                departments_data = response.get("data", {})
                departments = departments_data.get("items", []) if isinstance(departments_data, dict) else response.get("items", [])
                
                self.results.append(format_test_result(
                    "商户管理员获取部门列表",
                    True,
                    f"成功获取 {len(departments)} 个部门（应只包含本商户部门）",
                    {"department_count": len(departments)}
                ))
                print(f"✅ 商户管理员成功获取部门列表，共 {len(departments)} 个部门")
            elif status_code in [403, 401]:
                self.results.append(format_test_result(
                    "商户管理员部门列表权限控制",
                    True,
                    "正确限制商户管理员访问部门列表"
                ))
                print("✅ 正确限制商户管理员访问部门列表")
            else:
                self.results.append(format_test_result(
                    "商户管理员获取部门列表",
                    False,
                    f"获取部门列表失败，状态码: {status_code}"
                ))
                print(f"❌ 商户管理员获取部门列表失败，状态码: {status_code}")
    
    def test_get_department_detail(self):
        """测试获取部门详情"""
        print("\n=== 测试获取部门详情 ===")
        
        if not self.admin_token:
            return
        
        # 先获取部门列表，找一个部门ID
        merchant_id = self._get_first_merchant_id()
        params = {"merchant_id": merchant_id} if merchant_id else {}
        status_code, response = self.make_request("GET", "/departments", self.admin_token, params=params)
        if status_code != 200:
            self.results.append(format_test_result(
                "获取部门详情前置条件",
                False,
                "无法获取部门列表"
            ))
            return
        
        departments_data = response.get("data", {})
        departments = departments_data.get("items", []) if isinstance(departments_data, dict) else response.get("items", [])
        
        if not departments:
            self.results.append(format_test_result(
                "获取部门详情前置条件",
                False,
                "部门列表为空"
            ))
            return
        
        # 测试获取第一个部门的详情
        department_id = departments[0].get("id")
        if not department_id:
            self.results.append(format_test_result(
                "获取部门详情前置条件",
                False,
                "部门数据中缺少ID字段"
            ))
            return
        
        status_code, response = self.make_request("GET", f"/departments/{department_id}", self.admin_token)
        
        if status_code == 200:
            department_detail = response.get("data", response)
            self.results.append(format_test_result(
                "获取部门详情",
                True,
                f"成功获取部门详情: {department_detail.get('name', 'unknown')}",
                {"department_id": department_id}
            ))
            print(f"✅ 成功获取部门详情: {department_detail.get('name', 'unknown')}")
        else:
            self.results.append(format_test_result(
                "获取部门详情",
                False,
                f"获取部门详情失败，状态码: {status_code}"
            ))
            print(f"❌ 获取部门详情失败，状态码: {status_code}")
    
    def test_update_department(self):
        """测试更新部门"""
        print("\n=== 测试更新部门 ===")
        
        if not self.admin_token or not self.created_departments:
            self.results.append(format_test_result(
                "更新部门前置条件",
                False,
                "缺少管理员token或测试部门"
            ))
            return
        
        # 使用创建的测试部门进行更新测试
        department_id = self.created_departments[0]
        update_data = {
            "name": f"更新的测试部门_{int(time.time())}",
            "description": f"更新的部门描述_{int(time.time())}",
            "manager_name": f"更新的部门经理_{int(time.time())}"
        }
        
        status_code, response = self.make_request(
            "PUT", f"/departments/{department_id}", self.admin_token, data=update_data
        )
        
        if status_code == 200:
            self.results.append(format_test_result(
                "更新部门",
                True,
                f"成功更新部门: {department_id}",
                {"department_id": department_id, "update_data": update_data}
            ))
            print(f"✅ 成功更新部门: {department_id}")
        else:
            self.results.append(format_test_result(
                "更新部门",
                False,
                f"更新部门失败，状态码: {status_code}，响应: {response}"
            ))
            print(f"❌ 更新部门失败，状态码: {status_code}")
    
    def test_delete_department(self):
        """测试删除部门"""
        print("\n=== 测试删除部门 ===")
        
        if not self.admin_token or not self.created_departments:
            self.results.append(format_test_result(
                "删除部门前置条件",
                False,
                "缺少管理员token或测试部门"
            ))
            return
        
        # 删除创建的测试部门
        for department_id in self.created_departments[:]:  # 使用切片复制，避免在迭代时修改列表
            status_code, response = self.make_request(
                "DELETE", f"/departments/{department_id}", self.admin_token
            )
            
            if status_code in [200, 204]:
                self.results.append(format_test_result(
                    f"删除部门_{department_id}",
                    True,
                    f"成功删除部门: {department_id}"
                ))
                print(f"✅ 成功删除部门: {department_id}")
                self.created_departments.remove(department_id)
            else:
                self.results.append(format_test_result(
                    f"删除部门_{department_id}",
                    False,
                    f"删除部门失败，状态码: {status_code}"
                ))
                print(f"❌ 删除部门失败，状态码: {status_code}")
    
    def cleanup(self):
        """清理测试数据"""
        print("\n=== 清理测试数据 ===")
        
        if self.admin_token and self.created_departments:
            for department_id in self.created_departments[:]:
                status_code, _ = self.make_request(
                    "DELETE", f"/departments/{department_id}", self.admin_token
                )
                if status_code in [200, 204]:
                    print(f"✅ 清理测试部门: {department_id}")
                    self.created_departments.remove(department_id)
                else:
                    print(f"⚠️ 清理测试部门失败: {department_id}")
    
    def run_all_tests(self):
        """运行所有部门CRUD测试"""
        print("🚀 开始部门CRUD操作测试")
        print("="*60)
        
        start_time = time.time()
        
        # 前置设置
        if not self.setup():
            print("❌ 测试前置设置失败，跳过测试")
            return []
        
        try:
            # 运行所有测试
            self.test_create_department()
            self.test_get_departments_list()
            self.test_get_department_detail()
            self.test_update_department()
            self.test_delete_department()
        finally:
            # 清理测试数据
            self.cleanup()
        
        end_time = time.time()
        duration = end_time - start_time
        
        # 打印测试摘要
        print_test_summary(self.results)
        print(f"测试耗时: {duration:.2f}秒")
        
        return self.results

def main():
    """主函数"""
    test_suite = DepartmentsCrudTestSuite()
    results = test_suite.run_all_tests()
    
    # 判断测试是否通过
    failed_tests = [r for r in results if not r.get("success", False)]
    
    if not failed_tests:
        print("\n🎉 所有部门CRUD测试通过！")
        return 0
    else:
        print(f"\n⚠️ 发现 {len(failed_tests)} 个问题需要修复")
        for test in failed_tests:
            print(f"  - {test['test_name']}: {test['message']}")
        return 1

if __name__ == "__main__":
    sys.exit(main())
