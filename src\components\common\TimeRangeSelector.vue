<template>
  <div class="time-range-selector">
    <div class="time-range-group">
      <label class="time-range-label">时间范围：</label>
      <el-button-group>
        <el-button 
          :type="modelValue.timeRange === 'today' ? 'primary' : ''" 
          @click="setTimeRange('today')"
          size="small">
          今日
        </el-button>
        <el-button 
          :type="modelValue.timeRange === 'yesterday' ? 'primary' : ''" 
          @click="setTimeRange('yesterday')"
          size="small">
          昨日
        </el-button>
        <el-button 
          :type="modelValue.timeRange === 'week' ? 'primary' : ''" 
          @click="setTimeRange('week')"
          size="small">
          本周
        </el-button>
        <el-button 
          :type="modelValue.timeRange === 'month' ? 'primary' : ''" 
          @click="setTimeRange('month')"
          size="small">
          本月
        </el-button>
        <el-button 
          :type="modelValue.timeRange === 'custom' ? 'primary' : ''" 
          @click="setTimeRange('custom')"
          size="small">
          自定义
        </el-button>
      </el-button-group>
    </div>

    <div class="custom-date-group" v-if="modelValue.timeRange === 'custom'">
      <el-date-picker 
        :model-value="customDateRange" 
        @update:model-value="handleCustomDateChange"
        type="daterange" 
        range-separator="至" 
        start-placeholder="开始日期"
        end-placeholder="结束日期" 
        format="YYYY-MM-DD" 
        value-format="YYYY-MM-DD"
        style="width: 240px;" />
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue'

// Props
const props = defineProps({
  modelValue: {
    type: Object,
    required: true,
    default: () => ({
      timeRange: 'today',
      startDate: '',
      endDate: ''
    })
  }
})

// Emits
const emit = defineEmits(['update:modelValue', 'change'])

// 自定义日期范围
const customDateRange = ref([])

// 监听modelValue变化，同步自定义日期范围
watch(() => props.modelValue, (newValue) => {
  if (newValue.timeRange === 'custom' && newValue.startDate && newValue.endDate) {
    customDateRange.value = [newValue.startDate, newValue.endDate]
  } else if (newValue.timeRange !== 'custom') {
    customDateRange.value = []
  }
}, { immediate: true })

// 设置时间范围
const setTimeRange = (range) => {
  const newValue = {
    ...props.modelValue,
    timeRange: range
  }

  // 如果不是自定义范围，清空自定义日期
  if (range !== 'custom') {
    newValue.startDate = ''
    newValue.endDate = ''
    customDateRange.value = []
    
    // 根据快捷选项设置日期范围
    const today = new Date()
    const formatDate = (date) => {
      return date.toISOString().split('T')[0]
    }

    switch (range) {
      case 'today':
        newValue.startDate = formatDate(today)
        newValue.endDate = formatDate(today)
        break
      case 'yesterday':
        const yesterday = new Date(today)
        yesterday.setDate(yesterday.getDate() - 1)
        newValue.startDate = formatDate(yesterday)
        newValue.endDate = formatDate(yesterday)
        break
      case 'week':
        const weekStart = new Date(today)
        weekStart.setDate(today.getDate() - today.getDay())
        newValue.startDate = formatDate(weekStart)
        newValue.endDate = formatDate(today)
        break
      case 'month':
        const monthStart = new Date(today.getFullYear(), today.getMonth(), 1)
        newValue.startDate = formatDate(monthStart)
        newValue.endDate = formatDate(today)
        break
    }
  }

  emit('update:modelValue', newValue)
  emit('change', newValue)
}

// 处理自定义日期变化
const handleCustomDateChange = (dateRange) => {
  customDateRange.value = dateRange
  
  const newValue = {
    ...props.modelValue,
    startDate: dateRange && dateRange[0] ? dateRange[0] : '',
    endDate: dateRange && dateRange[1] ? dateRange[1] : ''
  }

  emit('update:modelValue', newValue)
  emit('change', newValue)
}
</script>

<style scoped>
.time-range-selector {
  display: flex;
  align-items: center;
  gap: 16px;
  flex-wrap: wrap;
}

.time-range-group {
  display: flex;
  align-items: center;
  gap: 8px;
}

.time-range-label {
  font-size: 14px;
  color: #606266;
  white-space: nowrap;
}

.custom-date-group {
  display: flex;
  align-items: center;
}

/* 时间快捷按钮组样式 */
.time-range-group .el-button-group .el-button {
  padding: 5px 12px;
  font-size: 12px;
  border-radius: 0;
}

.time-range-group .el-button-group .el-button:first-child {
  border-top-left-radius: 4px;
  border-bottom-left-radius: 4px;
}

.time-range-group .el-button-group .el-button:last-child {
  border-top-right-radius: 4px;
  border-bottom-right-radius: 4px;
}
</style>
