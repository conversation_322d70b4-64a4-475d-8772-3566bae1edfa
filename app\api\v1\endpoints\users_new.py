"""
用户管理API端点 - 基于UserService重构
"""

from typing import Any, Optional, List, Dict
from fastapi import APIRouter, Depends, HTTPException, Query, Path, status, Body
from sqlalchemy.orm import Session

from app.api import deps
from app.models.user import User
from app.schemas.user import (
    UserCreate,
    UserUpdate,
    UserStatusUpdate,
    PasswordChange,
)
from app.schemas.response import MessageResponse
from app.services.user_service import UserService
from app.core.exceptions import BusinessException, ErrorCode
from app.core.logging import get_logger

logger = get_logger(__name__)
router = APIRouter()


def user_to_dict(user: User) -> Dict[str, Any]:
    """将用户对象转换为字典格式"""
    # 获取商户名称
    merchant_name = None
    if user.merchant_id and hasattr(user, 'merchant') and user.merchant:
        merchant_name = user.merchant.name

    # 获取部门名称 - 优先从用户组织关系表获取
    department_name = None
    department_id = user.department_id  # 默认使用用户表的department_id

    # 尝试从用户组织关系表获取主要部门信息
    try:
        primary_org = user.get_primary_organization()
        if primary_org and primary_org.department_id:
            department_id = primary_org.department_id
            # 从数据库查询部门信息
            if hasattr(primary_org, 'department') and primary_org.department:
                department_name = primary_org.department.name
            else:
                # 如果关联关系没有加载，直接查询部门
                try:
                    from app.models.department import Department
                    from app.db.session import SessionLocal
                    db_session = SessionLocal()
                    dept = db_session.query(Department).filter(Department.id == primary_org.department_id).first()
                    if dept:
                        department_name = dept.name
                    db_session.close()
                except Exception:
                    pass
    except Exception as e:
        logger.error(f"获取用户组织关系失败: {e}")

    # 如果从组织关系表获取失败，回退到用户表的部门信息
    if not department_name and user.department_id and hasattr(user, 'department') and user.department:
        department_name = user.department.name

    return {
        "id": user.id,
        "username": user.username,
        "email": user.email,
        "full_name": user.full_name,
        "merchant_id": user.merchant_id,
        "merchant_name": merchant_name,
        "department_id": department_id,  # 使用从组织关系表获取的department_id
        "department_name": department_name,
        "is_active": user.is_active,
        "is_superuser": user.is_superuser,
        "phone": user.phone,
        "remark": user.remark,
        "created_at": user.created_at,
        "updated_at": user.updated_at,
        "last_login_ip": user.last_login_ip,
        "last_login_time": user.last_login_time,
        "roles": [{"id": r.id, "name": r.name, "code": r.code} for r in user.roles] if hasattr(user, 'roles') and user.roles else []
    }


def handle_user_exception(e: Exception, operation: str):
    """统一处理用户操作异常"""
    if isinstance(e, BusinessException):
        raise e
    elif isinstance(e, ValueError):
        raise BusinessException(message=str(e), code=ErrorCode.DATA_VALIDATION_ERROR)
    else:
        logger.error(f"{operation}失败: {e}")
        raise BusinessException(message=f"{operation}失败: {str(e)}", code=ErrorCode.INTERNAL_ERROR)


@router.get("", response_model=Dict[str, Any])
async def read_users(
    db: Session = Depends(deps.get_db),
    page: int = Query(1, ge=1, description="页码，从1开始"),
    page_size: int = Query(10, ge=1, le=100, description="每页记录数，最大100"),
    current_user: User = Depends(deps.get_current_active_user),
    merchant_id: Optional[int] = Query(None, description="商户ID筛选"),
    search: Optional[str] = Query(None, description="搜索关键词"),
    is_active: Optional[bool] = Query(None, description="用户状态筛选"),
):
    """
    获取用户列表

    权限要求:
    - "api:users:read": 查看用户列表
    - 数据隔离：商户用户只能查看自己商户的用户
    """
    try:
        # 检查权限 - 使用数据库中配置的权限代码，避免硬编码
        from app.services.permission_service import PermissionService
        permission_service = PermissionService(db)
        has_permission = permission_service.check_user_permission(current_user, "api:users:read")
        if not has_permission:
            raise BusinessException(
                message="没有查看用户列表的权限",
                code=ErrorCode.FORBIDDEN
            )

        # 初始化用户服务
        user_service = UserService(db)

        # 构建过滤条件
        filters = {}

        # 应用数据隔离过滤条件
        if not current_user.is_superuser:
            # 非超级管理员只能查看自己商户的用户
            filters["merchant_id"] = current_user.merchant_id
        elif merchant_id is not None:
            # 超级管理员可以指定商户ID过滤
            filters["merchant_id"] = merchant_id

        # 应用其他过滤条件
        if is_active is not None:
            filters["is_active"] = is_active

        if search:
            filters["username"] = search

        # 计算分页参数
        skip = (page - 1) * page_size

        # 使用CRUD层的正确方法获取用户列表和总数
        from app.crud.user import user as user_crud
        users, total = user_crud.get_multi_with_filters_and_count(
            db, skip=skip, limit=page_size, filters=filters
        )

        # 预加载关联关系以确保部门信息正确显示
        from sqlalchemy.orm import selectinload
        if users:
            user_ids = [u.id for u in users]
            users = (
                db.query(User)
                .options(
                    selectinload(User.merchant),
                    selectinload(User.department),
                    selectinload(User.roles)
                )
                .filter(User.id.in_(user_ids))
                .all()
            )

        # 转换为响应格式
        user_list = [user_to_dict(user) for user in users]

        # 直接返回数据，让中间件处理统一格式
        return {
            "items": user_list,
            "total": total,
            "page": page,
            "page_size": page_size,
            "pages": (total + page_size - 1) // page_size
        }

    except Exception as e:
        handle_user_exception(e, "获取用户列表")


@router.post("", response_model=Dict[str, Any])
async def create_user(
    *,
    db: Session = Depends(deps.get_db),
    user_in: UserCreate,
    current_user: User = Depends(deps.get_current_active_user),
):
    """
    创建新用户

    权限要求:
    - "api:users:create": 创建用户
    - 数据隔离：商户管理员只能创建自己商户的用户
    """
    try:
        # 检查权限 - 使用数据库中配置的权限代码，避免硬编码
        from app.services.permission_service import PermissionService
        permission_service = PermissionService(db)
        has_permission = permission_service.check_user_permission(current_user, "api:users:create")
        if not has_permission:
            raise BusinessException(
                message="没有创建用户的权限",
                code=ErrorCode.FORBIDDEN
            )

        # 数据权限检查：非超级管理员只能创建自己商户的用户
        if not current_user.is_superuser:
            if user_in.merchant_id != current_user.merchant_id:
                raise BusinessException(
                    message="只能创建自己商户的用户",
                    code=ErrorCode.FORBIDDEN
                )

        # 初始化用户服务并创建用户
        user_service = UserService(db)
        new_user = user_service.create_user(user_in, current_user)

        # 直接返回数据，让中间件处理统一格式
        return user_to_dict(new_user)

    except Exception as e:
        handle_user_exception(e, "创建用户")


@router.get("/me", response_model=Dict[str, Any])
def read_user_me(
    current_user: User = Depends(deps.get_current_active_user),
    db: Session = Depends(deps.get_db),
) -> Any:
    """
    获取当前登录用户信息，包含权限、数据范围和菜单。

    权限要求:
    - 基础权限：所有已认证用户都可以获取自己的个人信息
    - 无需额外权限检查，这是用户的基本权利
    """
    # 获取个人信息是基础权限，所有已认证用户都应该可以访问
    # 不需要额外的权限检查，因为这是用户的基本权利
    try:
        from app.services.menu_service import MenuService
        from sqlalchemy.orm import selectinload

        # 重新查询用户以确保加载所有关联关系
        full_user = (
            db.query(User)
            .options(
                selectinload(User.merchant),
                selectinload(User.department),
                selectinload(User.roles)
            )
            .filter(User.id == current_user.id)
            .first()
        )

        if not full_user:
            full_user = current_user

        # 初始化服务
        from app.services.permission_service import PermissionService
        permission_service = PermissionService(db)
        menu_service = MenuService()

        # 获取用户权限
        permissions = permission_service.get_user_permissions(full_user)

        # 获取用户菜单
        user_menus = menu_service.get_user_menus(db, full_user)
        # logger.info(f"用户 {full_user.username} 的菜单数据: {user_menus}")
        menu_codes = []

        def extract_menu_codes(menu_item):
            codes = []
            if menu_item.get('code'):
                codes.append(menu_item['code'])
                logger.debug(f"提取菜单代码: {menu_item['code']}")
            for child in menu_item.get('children', []):
                codes.extend(extract_menu_codes(child))
            return codes

        for menu in user_menus:
            if menu.get('code'):
                menu_codes.append(menu.get('code'))
                logger.debug(f"添加顶级菜单代码: {menu.get('code')}")
            menu_codes.extend(extract_menu_codes(menu))

        logger.info(f"用户 {full_user.username} 的所有菜单代码: {menu_codes}")

        # 获取用户角色信息
        user_roles = []
        if hasattr(full_user, 'roles') and full_user.roles:
            user_roles = [
                {
                    "id": role.id,
                    "name": role.name,
                    "code": role.code,
                    "data_scope": getattr(role, 'data_scope', 'self')
                }
                for role in full_user.roles
            ]

        # 构建返回数据
        user_data = user_to_dict(full_user)
        user_data.update({
            "permissions": permissions,
            "menus": list(set(menu_codes))  # 去重
        })

        # 直接返回数据，让中间件处理统一格式
        return user_data

    except Exception as e:
        logger.error(f"获取用户信息失败: {e}")
        # 返回基础用户信息
        # 返回基础用户信息，直接返回数据让中间件处理统一格式
        return {
            **user_to_dict(current_user),
            "permissions": ["dashboard:view"],
            "menus": ["dashboard"]
        }


@router.get("/{user_id}", response_model=Dict[str, Any])
async def read_user(
    *,
    db: Session = Depends(deps.get_db),
    user_id: int = Path(..., gt=0),
    current_user: User = Depends(deps.get_current_active_user),
):
    """
    获取用户详情

    权限要求:
    - "api:users:read": 查看用户详情
    - 数据隔离：只能查看有权限访问的用户
    """
    try:
        # 检查权限（查看自己的信息不需要权限）
        if current_user.id != user_id:
            from app.services.permission_service import PermissionService
            permission_service = PermissionService(db)
            has_permission = permission_service.check_user_permission(current_user, "api:users:read")
            if not has_permission:
                raise BusinessException(
                    message="没有查看用户详情的权限",
                    code=ErrorCode.FORBIDDEN
                )

        # 初始化用户服务
        user_service = UserService(db)

        # 获取用户（应用数据隔离）
        user = user_service.get_with_isolation(user_id, current_user)
        if not user:
            raise BusinessException(
                message="用户不存在或无权限访问",
                code=ErrorCode.NOT_FOUND
            )

        # 重新查询用户以确保加载所有关联关系
        from sqlalchemy.orm import selectinload
        user = (
            db.query(User)
            .options(
                selectinload(User.merchant),
                selectinload(User.department),
                selectinload(User.roles)
            )
            .filter(User.id == user_id)
            .first()
        )

        # 直接返回数据，让中间件处理统一格式
        return user_to_dict(user)

    except Exception as e:
        handle_user_exception(e, "获取用户详情")


@router.put("/{user_id}", response_model=Dict[str, Any])
async def update_user(
    *,
    db: Session = Depends(deps.get_db),
    user_id: int = Path(..., gt=0),
    user_in: UserUpdate,
    current_user: User = Depends(deps.get_current_active_user),
):
    """
    更新用户信息

    权限要求:
    - "api:users:update": 编辑用户信息
    - 数据隔离：只能编辑有权限访问的用户
    """
    try:
        # 检查权限（编辑自己的信息不需要权限）
        if current_user.id != user_id:
            from app.services.permission_service import PermissionService
            permission_service = PermissionService(db)
            has_permission = permission_service.check_user_permission(current_user, "api:users:update")
            if not has_permission:
                raise BusinessException(
                    message="没有编辑用户信息的权限",
                    code=ErrorCode.FORBIDDEN
                )

        # 初始化用户服务并更新用户
        user_service = UserService(db)
        updated_user = user_service.update_user(user_id, user_in, current_user)

        if not updated_user:
            raise BusinessException(
                message="用户不存在或无权限访问",
                code=ErrorCode.NOT_FOUND
            )

        # 重新查询用户以确保加载所有关联关系
        from sqlalchemy.orm import selectinload
        updated_user = (
            db.query(User)
            .options(
                selectinload(User.merchant),
                selectinload(User.department),
                selectinload(User.roles)
            )
            .filter(User.id == user_id)
            .first()
        )

        # 直接返回数据，让中间件处理统一格式
        return user_to_dict(updated_user)

    except Exception as e:
        handle_user_exception(e, "更新用户")


@router.patch("/{user_id}/status", response_model=Dict[str, Any])
async def update_user_status(
    *,
    db: Session = Depends(deps.get_db),
    user_id: int = Path(..., gt=0, description="用户ID"),
    status_in: UserStatusUpdate = Body(...),
    current_user: User = Depends(deps.get_current_active_user),
):
    """
    更新用户状态 (激活/禁用)

    权限要求:
    - "api:users:status": 更新用户状态
    - 数据隔离：只能修改有权限访问的用户状态
    """
    try:
        # 检查权限
        from app.services.permission_service import PermissionService
        permission_service = PermissionService(db)
        has_permission = permission_service.check_user_permission(current_user, "api:users:status")
        if not has_permission:
            raise BusinessException(
                message="没有修改用户状态的权限",
                code=ErrorCode.FORBIDDEN
            )

        # 使用UserAPIService来更新用户状态
        from app.services.user_api_service import UserAPIService
        user_api_service = UserAPIService(db)

        # 更新用户状态
        updated_user = user_api_service.update_user_status(user_id, status_in, current_user)

        if not updated_user:
            raise BusinessException(
                message="更新用户状态失败",
                code=ErrorCode.INTERNAL_ERROR
            )

        # 重新查询用户以确保加载所有关联关系
        from sqlalchemy.orm import selectinload
        updated_user = (
            db.query(User)
            .options(
                selectinload(User.merchant),
                selectinload(User.department),
                selectinload(User.roles)
            )
            .filter(User.id == user_id)
            .first()
        )

        # 直接返回数据，让中间件处理统一格式
        return user_to_dict(updated_user)

    except Exception as e:
        handle_user_exception(e, "更新用户状态")


@router.delete("/{user_id}", response_model=Dict[str, Any])
async def delete_user(
    *,
    db: Session = Depends(deps.get_db),
    user_id: int = Path(..., gt=0),
    current_user: User = Depends(deps.get_current_active_user),
):
    """
    删除用户

    权限要求:
    - "api:users:delete": 删除用户
    - 数据隔离：只能删除有权限访问的用户
    """
    try:
        # 检查权限 - 使用数据库中配置的权限代码，避免硬编码
        from app.services.permission_service import PermissionService
        permission_service = PermissionService(db)
        has_permission = permission_service.check_user_permission(current_user, "api:users:delete")
        if not has_permission:
            raise BusinessException(
                message="没有删除用户的权限",
                code=ErrorCode.FORBIDDEN
            )

        # 初始化用户服务并删除用户
        user_service = UserService(db)
        success = user_service.delete_user(user_id, current_user)

        if not success:
            raise BusinessException(
                message="用户不存在或无权限访问",
                code=ErrorCode.NOT_FOUND
            )

        # 直接返回数据，让中间件处理统一格式
        return {"user_id": user_id}

    except Exception as e:
        handle_user_exception(e, "删除用户")


@router.get("/{user_id}/roles", response_model=Dict[str, Any])
async def get_user_roles(
    *,
    db: Session = Depends(deps.get_db),
    user_id: int = Path(..., gt=0),
    current_user: User = Depends(deps.get_current_active_user),
):
    """
    获取用户角色列表

    权限要求:
    - "api:users:read": 查看用户角色
    """
    try:
        # 检查权限（查看自己的角色不需要权限）
        if current_user.id != user_id:
            from app.services.permission_service import PermissionService
            permission_service = PermissionService(db)
            has_permission = permission_service.check_user_permission(current_user, "api:users:read")
            if not has_permission:
                raise BusinessException(
                    message="没有查看用户角色的权限",
                    code=ErrorCode.FORBIDDEN
                )

        # 初始化用户服务
        user_service = UserService(db)

        # 获取用户角色
        roles = user_service.get_user_roles(user_id, current_user)

        role_list = [
            {
                "id": role.id,
                "name": role.name,
                "code": role.code,
                "description": role.description,
                "is_enabled": role.is_enabled
            }
            for role in roles
        ]

        # 直接返回数据，让中间件处理统一格式
        return {
            "user_id": user_id,
            "roles": role_list
        }

    except Exception as e:
        handle_user_exception(e, "获取用户角色")


@router.post("/{user_id}/roles/{role_id}", response_model=Dict[str, Any])
async def assign_role_to_user(
    *,
    db: Session = Depends(deps.get_db),
    user_id: int = Path(..., gt=0),
    role_id: int = Path(..., gt=0),
    current_user: User = Depends(deps.get_current_active_user),
):
    """
    为用户分配角色

    权限要求:
    - "api:users:assign-role": 管理用户角色
    """
    try:
        # 检查权限 - 使用数据库中配置的权限代码，避免硬编码
        from app.services.permission_service import PermissionService
        permission_service = PermissionService(db)
        has_permission = permission_service.check_user_permission(current_user, "api:users:assign-role")
        if not has_permission:
            raise BusinessException(
                message="没有分配用户角色的权限",
                code=ErrorCode.FORBIDDEN
            )

        # 初始化用户服务并分配角色
        user_service = UserService(db)
        success = user_service.assign_role(user_id, role_id, current_user)

        if not success:
            raise BusinessException(
                message="角色分配失败",
                code=ErrorCode.INTERNAL_ERROR
            )

        # 直接返回数据，让中间件处理统一格式
        return {"user_id": user_id, "role_id": role_id}

    except Exception as e:
        handle_user_exception(e, "分配角色")


@router.delete("/{user_id}/roles/{role_id}", response_model=Dict[str, Any])
async def remove_role_from_user(
    *,
    db: Session = Depends(deps.get_db),
    user_id: int = Path(..., gt=0),
    role_id: int = Path(..., gt=0),
    current_user: User = Depends(deps.get_current_active_user),
):
    """
    移除用户角色

    权限要求:
    - "api:users:assign-role": 管理用户角色
    """
    try:
        # 检查权限 - 使用数据库中配置的权限代码，避免硬编码
        from app.services.permission_service import PermissionService
        permission_service = PermissionService(db)
        has_permission = permission_service.check_user_permission(current_user, "api:users:assign-role")
        if not has_permission:
            raise BusinessException(
                message="没有移除用户角色的权限",
                code=ErrorCode.FORBIDDEN
            )

        # 初始化用户服务并移除角色
        user_service = UserService(db)
        success = user_service.remove_role(user_id, role_id, current_user)

        if not success:
            raise BusinessException(
                message="角色移除失败",
                code=ErrorCode.INTERNAL_ERROR
            )

        # 直接返回数据，让中间件处理统一格式
        return {"user_id": user_id, "role_id": role_id}

    except Exception as e:
        handle_user_exception(e, "移除角色")


@router.post("/change-password", response_model=MessageResponse)
def change_password(
    password_data: PasswordChange,
    current_user: User = Depends(deps.get_current_active_user),
    db: Session = Depends(deps.get_db)
) -> Any:
    """
    修改密码

    个人安全设置功能，所有登录用户都可以修改自己的密码，无需特殊权限。
    只能修改自己的密码，不能修改其他用户的密码。
    """
    try:
        from app.core.security import verify_password, get_password_hash

        # 验证当前密码
        if not verify_password(password_data.current_password, current_user.hashed_password):
            raise BusinessException(
                message="当前密码错误",
                code=ErrorCode.BAD_REQUEST
            )

        # 更新密码
        current_user.hashed_password = get_password_hash(password_data.new_password)
        db.commit()

        logger.info(f"用户修改密码成功: {current_user.username}")
        return MessageResponse(message="密码修改成功")

    except BusinessException:
        raise
    except Exception as e:
        logger.error(f"修改密码失败: {str(e)}")
        raise BusinessException(
            message="密码修改失败",
            code=ErrorCode.INTERNAL_ERROR
        )
