import re
import random
import string
from datetime import datetime
import hashlib
from app.core.config import settings


def generate_merchant_code(merchant_name, prefix=""):
    """
    根据商家名称生成可读且唯一的商家编码

    Args:
        merchant_name: 商家名称
        prefix: 可选前缀

    Returns:
        str: 生成的商家编码
    """
    # 添加时间戳（年月日时分秒，不含分隔符）
    timestamp = datetime.now().strftime("%Y%m%d%H%M%S")
    # 商户名称的hash编码
    name_part = hashlib.md5(merchant_name.encode()).hexdigest()[:4]
    # 添加4位随机字符（字母+数字）
    random_chars = "".join(random.choices(string.ascii_lowercase + string.digits, k=4))
    # 组合商户编码 时间戳+hash编码+随机字符
    code = f"{timestamp}-{name_part}-{random_chars}"
    # 加密商户编码，使用32位md5
    code = hashlib.md5(code.encode()).hexdigest()
    # 返回加密后的商户编码
    return code


def encrypt_sensitive_value(value: str) -> str:
    """加密敏感配置值"""
    from cryptography.fernet import Fernet

    f = Fernet(settings.SECRET_KEY.encode()[:32])
    return f.encrypt(value.encode()).decode()


def decrypt_sensitive_value(encrypted_value: str) -> str:
    """解密敏感配置值"""
    from cryptography.fernet import Fernet

    f = Fernet(settings.SECRET_KEY.encode()[:32])
    return f.decrypt(encrypted_value.encode()).decode()
