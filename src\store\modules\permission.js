import { defineStore } from "pinia";

/**
 * 简化权限Store - 只处理数据隔离和动态菜单
 */
export const usePermissionStore = defineStore("permission", {
  state: () => ({
    // 用户角色列表（动态）
    userRoles: [],
    // 是否为超级用户
    isSuperuser: false,
    // 用户所属商户ID
    merchantId: null,
    // 可访问的菜单列表
    accessibleMenus: [],
    // 路由是否已添加
    routesAdded: false,
  }),

  getters: {
    // 是否为超级管理员
    isSuperAdmin: (state) => state.isSuperuser,

    // 是否为商户管理员（基于角色动态判断）
    isMerchantAdmin: (state) => {
      return (
        state.userRoles &&
        state.userRoles.some(
          (role) =>
            role.code === "merchant_admin" || role.data_scope === "merchant"
        )
      );
    },

    // 检查是否可以访问指定商户的数据
    canAccessMerchantData: (state) => (targetMerchantId) => {
      // 超级管理员可以访问所有数据
      if (state.isSuperuser) {
        return true;
      }
      // 其他角色只能访问自己商户的数据
      return state.merchantId === targetMerchantId;
    },

    // 检查菜单权限
    hasMenuAccess: (state) => (menuCode) => {
      // 超级管理员可以访问所有菜单
      if (state.isSuperuser) {
        return true;
      }
      // 检查菜单是否在可访问列表中
      return state.accessibleMenus.includes(menuCode);
    },
  },

  actions: {
    // 设置用户权限信息
    setUserPermission(userInfo) {
      // 处理角色信息 - 支持新的角色数组格式
      this.userRoles = userInfo.roles || [];
      this.isSuperuser = userInfo.is_superuser || false;
      this.merchantId = userInfo.merchant_id;
      this.departmentId = userInfo.department_id;

      // 处理菜单权限 - 支持数组格式的菜单代码
      this.accessibleMenus = userInfo.menus || [];

      // 验证权限设置是否正确
      if (this.isSuperuser) {
        console.log("✅ 超级管理员权限设置成功");
      } else if (this.accessibleMenus.length > 0) {
        console.log(
          "✅ 普通用户菜单权限设置成功，菜单数量:",
          this.accessibleMenus.length
        );
      } else {
        console.warn("⚠️ 权限设置可能有问题：非超级管理员但菜单列表为空");
      }
    },

    // 生成动态路由（简化版）
    async generateRoutes() {
      try {
        // 简化版：不使用复杂的动态路由，直接返回空数组
        // 路由权限控制通过菜单显示来实现
        this.routesAdded = true;
        return [];
      } catch (error) {
        console.error("生成路由失败:", error);
        this.routesAdded = false;
        return [];
      }
    },

    // 根据菜单权限过滤路由
    filterRoutesByMenus(routes) {
      const result = [];

      routes.forEach((route) => {
        let shouldInclude = false;

        // 如果路由有权限代码，检查权限
        if (route.meta?.permissionCode) {
          // 简化权限检查：只检查基本的模块权限
          const permission = route.meta.permissionCode;
          const module = permission.split(":")[0]; // 获取模块名，如 'dashboard', 'system', 'merchant'
          shouldInclude = this.hasMenuAccess(module);
        } else {
          // 没有权限代码的路由（如重定向路由）直接添加
          shouldInclude = true;
        }

        if (shouldInclude) {
          const newRoute = { ...route };
          // 递归处理子路由
          if (newRoute.children) {
            newRoute.children = this.filterRoutesByMenus(newRoute.children);
            // 如果子路由被过滤完了，但父路由是重定向路由，仍然保留
            if (newRoute.children.length === 0 && !newRoute.redirect) {
              return; // 跳过这个路由
            }
          }
          result.push(newRoute);
        }
      });

      return result;
    },

    // 重置权限状态
    async resetPermission() {
      console.log("重置权限状态");
      this.userRoles = [];
      this.isSuperuser = false;
      this.merchantId = null;
      this.accessibleMenus = [];
      this.routesAdded = false;

      // 清理动态添加的路由
      await this.clearDynamicRoutes();
    },

    // 清理动态路由（简化版）
    async clearDynamicRoutes() {
      try {
        // 简化版：不需要复杂的路由清理
        console.log("动态路由已清理（简化版）");
      } catch (error) {
        console.error("清理动态路由失败:", error);
      }
    },
  },
});
