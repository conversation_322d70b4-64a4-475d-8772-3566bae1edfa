"""
日期时间处理工具
专门用于统计查询中的时间范围计算，确保时区处理一致性
"""

from datetime import datetime, date, time, timedelta
from typing import Tuple
from zoneinfo import ZoneInfo

# 使用上海时区
TARGET_TZ = ZoneInfo("Asia/Shanghai")


def get_date_time_range(target_date: date) -> <PERSON><PERSON>[datetime, datetime]:
    """
    获取指定日期的完整时间范围（00:00:00 到 23:59:59.999999）
    
    Args:
        target_date: 目标日期
        
    Returns:
        Tuple[datetime, datetime]: (开始时间, 结束时间)
        开始时间：target_date 00:00:00 (上海时区)
        结束时间：target_date 23:59:59.999999 (上海时区)
    """
    # 创建当日开始时间：00:00:00
    start_datetime = datetime.combine(target_date, time.min, TARGET_TZ)
    
    # 创建当日结束时间：23:59:59.999999
    end_datetime = datetime.combine(target_date, time.max, TARGET_TZ)
    
    return start_datetime, end_datetime


def get_date_range_for_query(start_date: date, end_date: date) -> <PERSON><PERSON>[datetime, datetime]:
    """
    获取日期范围的查询时间边界
    
    Args:
        start_date: 开始日期
        end_date: 结束日期
        
    Returns:
        Tuple[datetime, datetime]: (查询开始时间, 查询结束时间)
        查询开始时间：start_date 00:00:00 (上海时区)
        查询结束时间：end_date 23:59:59.999999 (上海时区)
    """
    # 开始日期的00:00:00
    query_start = datetime.combine(start_date, time.min, TARGET_TZ)
    
    # 结束日期的23:59:59.999999
    query_end = datetime.combine(end_date, time.max, TARGET_TZ)
    
    return query_start, query_end


def get_today_range() -> Tuple[datetime, datetime]:
    """
    获取今日的完整时间范围
    
    Returns:
        Tuple[datetime, datetime]: (今日开始时间, 今日结束时间)
    """
    today = datetime.now(TARGET_TZ).date()
    return get_date_time_range(today)


def get_yesterday_range() -> Tuple[datetime, datetime]:
    """
    获取昨日的完整时间范围
    
    Returns:
        Tuple[datetime, datetime]: (昨日开始时间, 昨日结束时间)
    """
    today = datetime.now(TARGET_TZ).date()
    yesterday = today - timedelta(days=1)
    return get_date_time_range(yesterday)


def is_today(target_date: date) -> bool:
    """
    检查指定日期是否为今日
    
    Args:
        target_date: 目标日期
        
    Returns:
        bool: 是否为今日
    """
    today = datetime.now(TARGET_TZ).date()
    return target_date == today


def get_current_shanghai_time() -> datetime:
    """
    获取当前上海时区时间
    
    Returns:
        datetime: 当前上海时区时间
    """
    return datetime.now(TARGET_TZ)


def get_current_shanghai_date() -> date:
    """
    获取当前上海时区日期
    
    Returns:
        date: 当前上海时区日期
    """
    return datetime.now(TARGET_TZ).date()


def format_datetime_for_log(dt: datetime) -> str:
    """
    格式化datetime用于日志记录
    
    Args:
        dt: 要格式化的datetime
        
    Returns:
        str: 格式化后的时间字符串
    """
    if dt.tzinfo:
        return dt.strftime("%Y-%m-%d %H:%M:%S %Z")
    else:
        return dt.strftime("%Y-%m-%d %H:%M:%S (无时区)")


def debug_time_range(start_date: date, end_date: date) -> dict:
    """
    调试时间范围计算，返回详细信息
    
    Args:
        start_date: 开始日期
        end_date: 结束日期
        
    Returns:
        dict: 包含详细时间信息的字典
    """
    query_start, query_end = get_date_range_for_query(start_date, end_date)
    current_time = get_current_shanghai_time()
    
    return {
        "input_start_date": start_date.isoformat(),
        "input_end_date": end_date.isoformat(),
        "query_start_datetime": format_datetime_for_log(query_start),
        "query_end_datetime": format_datetime_for_log(query_end),
        "current_shanghai_time": format_datetime_for_log(current_time),
        "is_today_query": is_today(start_date) and start_date == end_date,
        "timezone": str(TARGET_TZ)
    }


# 兼容性函数，保持与现有代码的兼容
def local_now() -> datetime:
    """
    获取当前目标时区的时间（兼容性函数）
    
    Returns:
        datetime: 当前上海时区时间
    """
    return get_current_shanghai_time()
