"""
修复bcrypt版本兼容性问题的工具模块
"""

import warnings
import sys
import logging

# 设置日志
logger = logging.getLogger(__name__)

def fix_bcrypt_compatibility():
    """
    修复bcrypt与passlib的兼容性问题

    新版本的bcrypt移除了__about__属性，但passlib仍在尝试访问它
    这个函数通过monkey patch的方式修复这个问题
    """
    try:
        import bcrypt

        # 检查是否已经有__about__属性
        if not hasattr(bcrypt, '__about__'):
            # 创建一个模拟的__about__对象
            class MockAbout:
                def __init__(self):
                    self.__version__ = getattr(bcrypt, '__version__', '4.1.3')

            # 将模拟对象添加到bcrypt模块
            bcrypt.__about__ = MockAbout()
            logger.info("bcrypt兼容性修复已应用")

        # 抑制相关的警告信息
        warnings.filterwarnings('ignore', message='.*error reading bcrypt version.*')
        warnings.filterwarnings('ignore', message='.*trapped.*error reading bcrypt version.*')

    except ImportError:
        # 如果bcrypt未安装，忽略
        logger.warning("bcrypt未安装，跳过兼容性修复")
    except Exception as e:
        # 如果修复失败，记录但不中断程序
        logger.warning(f"bcrypt兼容性修复失败: {e}")

# 在模块导入时自动执行修复
fix_bcrypt_compatibility()
