"""
通知Schema定义
"""
from typing import Optional, Dict, Any
from pydantic import BaseModel, Field
from datetime import datetime

# 通知相关常量已移至字符串类型


class NotificationBase(BaseModel):
    """通知基础模型"""

    user_id: int = Field(..., description="接收用户ID")
    title: str = Field(..., description="通知标题")
    content: str = Field(..., description="通知内容")
    type: str = Field("SYSTEM", description="通知类型")
    status: str = Field("UNREAD", description="通知状态")
    link: Optional[str] = Field(None, description="相关链接")
    is_important: bool = Field(False, description="是否重要")
    extra_data: Optional[Dict[str, Any]] = Field(None, description="额外数据")

    class Config:
        from_attributes = True


class NotificationCreate(NotificationBase):
    """创建通知时的模型"""
    pass


class NotificationUpdate(BaseModel):
    """更新通知时的模型"""

    title: Optional[str] = Field(None, description="通知标题")
    content: Optional[str] = Field(None, description="通知内容")
    type: Optional[str] = Field(None, description="通知类型")
    status: Optional[str] = Field(None, description="通知状态")
    link: Optional[str] = Field(None, description="相关链接")
    is_important: Optional[bool] = Field(None, description="是否重要")
    extra_data: Optional[Dict[str, Any]] = Field(None, description="额外数据")

    class Config:
        from_attributes = True


class NotificationInDB(NotificationBase):
    """数据库中的通知模型"""

    id: int = Field(..., description="通知ID")
    read_at: Optional[datetime] = Field(None, description="阅读时间")
    created_at: datetime = Field(..., description="创建时间")
    updated_at: Optional[datetime] = Field(None, description="更新时间")

    class Config:
        from_attributes = True


class Notification(NotificationInDB):
    """API响应的通知模型"""
    pass


class NotificationFilter(BaseModel):
    """通知过滤条件"""

    user_id: Optional[int] = Field(None, description="用户ID")
    status: Optional[str] = Field(None, description="通知状态")
    type: Optional[str] = Field(None, description="通知类型")
    title: Optional[str] = Field(None, description="标题搜索")
    content: Optional[str] = Field(None, description="内容搜索")
    is_important: Optional[bool] = Field(None, description="是否重要")
    start_date: Optional[datetime] = Field(None, description="开始时间")
    end_date: Optional[datetime] = Field(None, description="结束时间")

    class Config:
        from_attributes = True


class NotificationStatistics(BaseModel):
    """通知统计信息"""

    total_count: int = Field(..., description="总数量")
    unread_count: int = Field(..., description="未读数量")
    read_count: int = Field(..., description="已读数量")
    type_stats: Dict[str, int] = Field(..., description="按类型统计")
    start_date: Optional[datetime] = Field(None, description="统计开始时间")
    end_date: Optional[datetime] = Field(None, description="统计结束时间")

    class Config:
        from_attributes = True


class NotificationBroadcast(BaseModel):
    """广播通知模型"""

    user_ids: list[int] = Field(..., description="接收用户ID列表")
    title: str = Field(..., description="通知标题")
    content: str = Field(..., description="通知内容")
    type: str = Field("SYSTEM", description="通知类型")
    link: Optional[str] = Field(None, description="相关链接")
    is_important: bool = Field(False, description="是否重要")
    extra_data: Optional[Dict[str, Any]] = Field(None, description="额外数据")

    class Config:
        from_attributes = True


class NotificationMarkRead(BaseModel):
    """标记已读模型"""

    notification_ids: list[int] = Field(..., description="通知ID列表")

    class Config:
        from_attributes = True


class NotificationUnreadCount(BaseModel):
    """未读通知数量"""

    user_id: int = Field(..., description="用户ID")
    unread_count: int = Field(..., description="未读数量")

    class Config:
        from_attributes = True
