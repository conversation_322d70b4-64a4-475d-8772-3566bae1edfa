import { defineStore } from "pinia";
import { menuApi } from "@/api/modules/menu";
import { systemApi } from "@/api/modules/system";

export const useSystemStore = defineStore("system", {
  state: () => ({
    userMenus: [],
    hasGeneratedRoutes: false,
    settings: {},
    isLoading: false,
    error: null,
  }),

  getters: {
    hasMenus: (state) => state.userMenus && state.userMenus.length > 0,
  },

  actions: {
    // 获取用户菜单
    async fetchUserMenus() {
      this.isLoading = true;
      this.error = null;

      try {
        // 调用 API
        const response = await menuApi.getUserMenus();

        // 处理响应数据 - 支持多种格式
        let menus = [];

        if (Array.isArray(response)) {
          menus = response;
        } else if (response && Array.isArray(response.data)) {
          menus = response.data;
        } else if (
          response &&
          response.menus &&
          Array.isArray(response.menus)
        ) {
          menus = response.menus;
        } else {
          console.warn("菜单响应格式不符合预期:", response);
          menus = [];
        }

        // 直接使用后端返回的菜单数据，不再进行额外处理
        this.userMenus = menus;
        return menus;
      } catch (error) {
        console.error("获取用户菜单请求失败:", error);
        this.userMenus = [];
        throw error;
      } finally {
        this.isLoading = false;
      }
    },

    // 获取系统设置
    async fetchSettings() {
      this.isLoading = true;
      this.error = null;

      try {
        const response = await systemApi.getSystemParams();

        // 处理不同的响应格式
        if (response.data) {
          // 如果是标准响应格式
          if (response.data.code === 0 && response.data.data) {
            this.settings = response.data.data;
          } else if (typeof response.data === "object") {
            // 如果直接返回了对象
            this.settings = response.data;
          } else {
            throw new Error("无效的响应格式");
          }
        } else if (response) {
          // 如果直接返回了数据
          this.settings = response;
        } else {
          throw new Error("无效的响应格式");
        }

        return this.settings;
      } catch (error) {
        console.error("获取系统设置失败:", error);
        this.error = error.message || "网络错误";
        throw error;
      } finally {
        this.isLoading = false;
      }
    },

    // 重置状态
    resetState() {
      this.userMenus = [];
      this.hasGeneratedRoutes = false;
      this.settings = {};
      this.isLoading = false;
      this.error = null;
    },

    // 初始化系统 - 获取菜单和系统设置
    async initialize() {
      try {
        // 并行获取菜单和系统设置
        await Promise.all([this.fetchUserMenus()]);
        return { menus: this.userMenus, settings: this.settings };
      } catch (error) {
        console.error("系统初始化失败:", error);
        this.error = error.message || "系统初始化失败";
        throw error;
      }
    },

    // 新增：设置路由生成状态
    setRoutesGenerated(status) {
      this.hasGeneratedRoutes = status;
    },
  },
});
