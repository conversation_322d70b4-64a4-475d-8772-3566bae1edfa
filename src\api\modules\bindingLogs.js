import { http } from "@/api/request";
import { API_URLS } from "./config";

/**
 * 绑卡日志相关API
 */
export const bindingLogsApi = {
  /**
   * 获取卡记录的绑定日志
   * @param {string} cardRecordId 卡记录ID
   * @param {object} params 查询参数
   * @returns {Promise<object>} API响应数据
   */
  getLogsByCardId(cardRecordId, params = {}) {
    return http
      .get(`/binding-logs/${cardRecordId}`, { params })
      .then((res) => res.data);
  },

  /**
   * 获取绑卡时间线数据
   * @param {string} cardRecordId 卡记录ID
   * @returns {Promise<object>} 时间线数据
   */
  getTimeline(cardRecordId) {
    return http
      .get(`/binding-logs/${cardRecordId}/timeline`)
      .then((res) => res.data);
  },

  /**
   * 获取绑卡性能分析数据
   * @param {string} cardRecordId 卡记录ID
   * @returns {Promise<object>} 性能分析数据
   */
  getPerformanceAnalysis(cardRecordId) {
    return http
      .get(`/binding-logs/${cardRecordId}/performance`)
      .then((res) => res.data);
  },
};

export default bindingLogsApi;
