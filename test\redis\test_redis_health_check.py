#!/usr/bin/env python3
"""
Redis CK优化健康检查测试
验证系统集成后的健康状态和性能表现
"""

import asyncio
import sys
import os
import time
import pytest
from typing import Dict, Any

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.insert(0, project_root)

try:
    from app.db.session import SessionLocal
    from app.services.redis_ck_wrapper import create_optimized_ck_service
    from app.services.walmart_ck_service_new import WalmartCKService
    from app.models.merchant import Merchant
    from app.models.walmart_ck import WalmartCK
    from app.core.config import settings
    from app.core.logging import get_logger
except ImportError as e:
    print(f"❌ 导入模块失败: {e}")
    sys.exit(1)

logger = get_logger("test_redis_health_check")


class TestRedisHealthCheck:
    """Redis健康检查测试类"""
    
    @pytest.mark.asyncio
    async def test_redis_optimization_health_check(self):
        """Redis优化健康检查"""
        db = SessionLocal()
        
        try:
            # 创建优化服务
            ck_service = create_optimized_ck_service(db)
            
            # 执行健康检查
            health_result = await ck_service.health_check()
            
            assert isinstance(health_result, dict), "健康检查结果应该是字典"
            assert "redis_enabled" in health_result, "缺少redis_enabled字段"
            assert "database_service" in health_result, "缺少database_service字段"
            
            # 数据库服务必须可用
            assert health_result["database_service"] == "available", "数据库服务不可用"
            
            logger.info(f"健康检查结果: {health_result}")
            
            return health_result
            
        finally:
            db.close()

    @pytest.mark.asyncio
    async def test_performance_comparison(self):
        """性能对比测试"""
        db = SessionLocal()
        
        try:
            # 查找测试商户
            test_merchant = db.query(Merchant).first()
            if not test_merchant:
                pytest.skip("没有找到测试商户，跳过性能测试")
            
            logger.info(f"使用测试商户: {test_merchant.name} (ID: {test_merchant.id})")
            
            # 测试次数
            test_count = 5
            
            # 测试传统方法
            traditional_service = WalmartCKService(db)
            
            start_time = time.time()
            traditional_results = []
            for i in range(test_count):
                result = await traditional_service.get_available_ck(test_merchant.id)
                traditional_results.append(result is not None)
            traditional_time = time.time() - start_time
            
            # 测试Redis优化方法
            optimized_service = create_optimized_ck_service(db)
            
            start_time = time.time()
            optimized_results = []
            for i in range(test_count):
                result = await optimized_service.get_available_ck(test_merchant.id)
                optimized_results.append(result is not None)
            optimized_time = time.time() - start_time
            
            # 验证结果
            traditional_success = sum(traditional_results)
            optimized_success = sum(optimized_results)
            
            logger.info(f"传统方法: {traditional_time:.3f}秒, 成功{traditional_success}/{test_count}")
            logger.info(f"Redis优化: {optimized_time:.3f}秒, 成功{optimized_success}/{test_count}")
            
            # 基本断言
            assert traditional_time >= 0, "传统方法执行时间异常"
            assert optimized_time >= 0, "Redis优化方法执行时间异常"
            
            return {
                "traditional_time": traditional_time,
                "optimized_time": optimized_time,
                "traditional_success": traditional_success,
                "optimized_success": optimized_success,
                "test_count": test_count
            }
            
        finally:
            db.close()

    def test_system_status_check(self):
        """系统状态检查"""
        db = SessionLocal()
        
        try:
            # 检查商户数量
            merchant_count = db.query(Merchant).count()
            
            # 检查CK数量
            ck_count = db.query(WalmartCK).count()
            active_ck_count = db.query(WalmartCK).filter(WalmartCK.active == True).count()
            
            logger.info(f"商户数量: {merchant_count}")
            logger.info(f"CK总数: {ck_count}")
            logger.info(f"活跃CK数: {active_ck_count}")
            
            # 检查配置
            redis_optimization = getattr(settings, 'ENABLE_REDIS_CK_OPTIMIZATION', False)
            fallback_db = getattr(settings, 'CK_REDIS_FALLBACK_TO_DB', False)
            
            logger.info(f"Redis优化启用: {redis_optimization}")
            logger.info(f"降级机制启用: {fallback_db}")
            
            # 基本断言
            assert merchant_count >= 0, "商户数量异常"
            assert ck_count >= 0, "CK数量异常"
            assert active_ck_count >= 0, "活跃CK数量异常"
            
            return {
                "merchant_count": merchant_count,
                "ck_count": ck_count,
                "active_ck_count": active_ck_count,
                "redis_optimization": redis_optimization,
                "fallback_db": fallback_db
            }
            
        finally:
            db.close()

    @pytest.mark.asyncio
    async def test_integration_summary(self):
        """集成状态总结测试"""
        # 执行各项检查
        health_result = await self.test_redis_optimization_health_check()
        system_status = self.test_system_status_check()
        
        # 评估集成状态
        integration_success = (
            health_result and 
            health_result.get("database_service") == "available" and
            system_status and
            system_status.get("redis_optimization") is not None
        )
        
        logger.info(f"集成状态评估: {'成功' if integration_success else '需要检查'}")
        
        assert integration_success, "Redis CK优化集成状态检查失败"
        
        return {
            "health_result": health_result,
            "system_status": system_status,
            "integration_success": integration_success
        }


# 独立测试函数（兼容旧版本调用）
async def health_check_redis_optimization():
    """Redis优化健康检查"""
    test_instance = TestRedisHealthCheck()
    return await test_instance.test_redis_optimization_health_check()


async def performance_comparison():
    """性能对比测试"""
    test_instance = TestRedisHealthCheck()
    return await test_instance.test_performance_comparison()


def system_status_check():
    """系统状态检查"""
    test_instance = TestRedisHealthCheck()
    return test_instance.test_system_status_check()


async def integration_summary():
    """集成总结"""
    test_instance = TestRedisHealthCheck()
    return await test_instance.test_integration_summary()


async def main():
    """主函数（用于直接运行）"""
    print("🚀 Redis CK优化健康检查测试")
    print("=" * 60)
    
    # 执行各项检查
    try:
        print("🔍 执行健康检查...")
        health_result = await health_check_redis_optimization()
        
        print("🔍 执行系统状态检查...")
        system_status = system_status_check()
        
        print("🔍 执行性能对比...")
        try:
            performance_result = await performance_comparison()
        except Exception as e:
            print(f"⚠️ 性能测试跳过: {e}")
            performance_result = None
        
        print("🔍 执行集成总结...")
        summary_result = await integration_summary()
        
        # 最终状态评估
        print("\n" + "=" * 60)
        print("🎯 最终评估结果:")
        
        if (health_result and health_result.get("redis_enabled") and 
            health_result.get("database_service") == "available"):
            print("🎉 Redis CK优化健康检查完全成功！")
            return True
        elif health_result and health_result.get("database_service") == "available":
            print("⚠️ Redis CK优化部分成功，系统可正常运行")
            return True
        else:
            print("❌ 健康检查存在问题，需要进一步检查")
            return False
            
    except Exception as e:
        print(f"❌ 健康检查测试失败: {e}")
        return False


if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
