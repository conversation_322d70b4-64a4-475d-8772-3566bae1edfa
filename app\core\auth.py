"""
认证和权限服务模块 - 完全动态权限系统
"""
from typing import List, Optional, Dict, Any
from sqlalchemy.orm import Session, joinedload
from app.models.user import User

from app.crud.user import user as user_crud
from app.schemas.permission import PermissionCheckResult
import logging

logger = logging.getLogger(__name__)


class AuthService:
    """认证和权限服务 - 完全基于动态权限系统"""

    def __init__(self):
        pass

    def get_user_permissions(self, user: User, db: Session) -> List[str]:
        """获取用户权限列表"""
        try:
            from app.services.permission_service import PermissionService
            permission_service = PermissionService(db)
            return permission_service.get_user_permissions(user)
        except Exception as e:
            logger.error(f"获取用户权限失败: {e}")
            return []

    def get_user_data_scope(self, user: User, db: Session) -> str:
        """获取用户数据权限范围 - 基于数据权限配置"""
        try:
            logger.info(f"[DEBUG] 开始获取用户数据权限范围 - 用户ID: {user.id}, 用户名: {getattr(user, 'username', 'N/A')}")

            # 超级管理员拥有全部数据权限
            if user_crud.is_superuser(user):
                logger.info(f"[DEBUG] 用户是超级管理员，返回 'all' 范围")
                return "all"

            # 使用PermissionService获取用户数据权限
            from app.services.permission_service import PermissionService
            permission_service = PermissionService(db)
            data_permissions = permission_service.get_user_data_permissions(user)
            logger.info(f"[DEBUG] 用户数据权限列表: {data_permissions}")

            # 根据数据权限确定最大权限范围（按优先级从高到低）
            # 【安全修复】：严格区分全局权限和商户范围权限

            # 只有 data:merchant:all 才是真正的全局权限（仅超级管理员应该拥有）
            if 'data:merchant:all' in data_permissions:
                logger.warning(f"[SECURITY] 用户 {user.id} 拥有全局商户权限 data:merchant:all，返回 'all' 范围")
                return "all"

            # 商户范围内的权限（这些权限仅限于用户所属商户）
            elif 'data:merchant:own' in data_permissions:
                logger.info(f"[DEBUG] 用户有本商户权限，返回 'merchant' 范围")
                return "merchant"
            elif 'data:department:all' in data_permissions:
                # 【修复】：data:department:all 应该是商户范围内的所有部门，不是全局权限
                logger.info(f"[DEBUG] 用户有本商户所有部门权限，返回 'merchant' 范围")
                return "merchant"  # 修复：返回商户范围而不是全局范围
            elif 'data:user:all' in data_permissions:
                # 【修复】：data:user:all 应该是商户范围内的所有用户，不是全局权限
                logger.info(f"[DEBUG] 用户有本商户所有用户权限，返回 'merchant' 范围")
                return "merchant"  # 修复：返回商户范围而不是全局范围
            elif 'data:department:sub' in data_permissions:
                logger.info(f"[DEBUG] 用户有本部门及子部门权限，返回 'department_sub' 范围")
                return "department_sub"
            elif 'data:department:own' in data_permissions:
                logger.info(f"[DEBUG] 用户有本部门权限，返回 'department' 范围")
                return "department"
            elif 'data:user:own' in data_permissions:
                logger.info(f"[DEBUG] 用户有本人权限，返回 'self' 范围")
                return "self"
            else:
                logger.info(f"[DEBUG] 用户没有数据权限，返回默认 'self' 范围")
                return "self"

        except Exception as e:
            logger.error(f"获取用户数据范围失败: {e}")
            return "self"

    def _ensure_user_roles_loaded(self, user: User, db: Session) -> User:
        """确保用户角色关系已加载"""
        if not hasattr(user, 'roles') or not user.roles:
            # 重新查询用户以加载角色关系
            user_with_roles = db.query(User).options(joinedload(User.roles)).filter(User.id == user.id).first()
            if user_with_roles:
                return user_with_roles
        return user

    def _get_max_scope_from_roles(self, user: User) -> str:
        """从用户角色中获取最大权限范围"""
        if not hasattr(user, 'roles') or not user.roles:
            return "self"

        # 如果用户有多个角色，取权限范围最大的
        max_scope = "self"  # 默认最小权限
        scope_hierarchy = {"self": 1, "department": 2, "merchant": 3, "all": 4}

        for role in user.roles:
            if role.data_scope and scope_hierarchy.get(role.data_scope, 1) > scope_hierarchy.get(max_scope, 1):
                max_scope = role.data_scope

        return max_scope
    
    def can_access_merchant_data(self, user: User, merchant_id: int, db: Session) -> bool:
        """检查用户是否可以访问指定商户的数据 - 基于数据权限配置"""
        try:
            # 使用PermissionService进行权限检查
            from app.services.permission_service import PermissionService
            permission_service = PermissionService(db)
            return permission_service.can_access_merchant_data(user, merchant_id)
        except Exception as e:
            logger.error(f"检查商户数据访问权限失败: {e}")
            return False

    def can_access_department_data(self, user: User, department_id: int, db: Session) -> bool:
        """检查用户是否可以访问指定部门的数据 - 基于数据权限配置"""
        try:
            # 使用PermissionService进行权限检查
            from app.services.permission_service import PermissionService
            permission_service = PermissionService(db)
            return permission_service.can_access_department_data(user, department_id)
        except Exception as e:
            logger.error(f"检查部门数据访问权限失败: {e}")
            return False

    def get_permission_summary(self, user: User, db: Session) -> Dict[str, Any]:
        """获取用户权限摘要"""
        try:
            return {
                "user_id": user.id,
                "username": user.username,
                "role": user.role,
                "merchant_id": getattr(user, 'merchant_id', None),
                "department_id": getattr(user, 'department_id', None),
                "is_superuser": user_crud.is_superuser(user),
                "is_active": user.is_active,
                "data_scope": self.get_user_data_scope(user, db),
                "created_at": user.created_at.isoformat() if user.created_at else None
            }
        except Exception as e:
            logger.error(f"获取权限摘要失败: {e}")
            return {
                "user_id": user.id,
                "username": user.username,
                "role": user.role,
                "error": str(e)
            }

    def check_permission_detailed(
        self,
        user: User,
        permission_code: str,
        resource_id: Optional[int] = None,
        merchant_id: Optional[int] = None,
        department_id: Optional[int] = None,
        db: Optional[Session] = None
    ) -> PermissionCheckResult:
        """详细的权限检查，返回详细结果"""
        try:
            if not db:
                return PermissionCheckResult(
                    has_permission=user.is_superuser,
                    reason="需要传入数据库会话进行完整权限检查"
                )

            from app.services.permission_service import PermissionService
            permission_service = PermissionService(db)

            has_permission = permission_service.check_user_permission(user, permission_code)

            return PermissionCheckResult(
                has_permission=has_permission,
                reason="权限检查完成" if has_permission else f"缺少权限: {permission_code}"
            )
        except Exception as e:
            logger.error(f"详细权限检查失败: {e}")
            return PermissionCheckResult(
                has_permission=False,
                reason=f"权限检查失败: {str(e)}"
            )

    def get_user_accessible_merchants(self, user: User, db: Session) -> List[int]:
        """获取用户可访问的商户ID列表 - 完全动态权限获取"""
        try:
            # 超级管理员可以访问所有商户
            if user_crud.is_superuser(user):
                from app.crud.merchant import merchant as merchant_crud
                merchants = merchant_crud.get_multi(db)
                return [m.id for m in merchants]

            # 获取用户的数据权限范围
            data_scope = self.get_user_data_scope(user, db)

            # 根据数据权限范围返回可访问的商户
            if data_scope == "all":
                from app.crud.merchant import merchant as merchant_crud
                merchants = merchant_crud.get_multi(db)
                return [m.id for m in merchants]
            elif data_scope in ["merchant", "department", "department_sub", "self"]:
                # 只能访问自己的商户
                return [user.merchant_id] if hasattr(user, 'merchant_id') and user.merchant_id else []

            return []
        except Exception as e:
            logger.error(f"获取可访问商户列表失败: {e}")
            return []

    def get_user_accessible_departments(self, user: User, db: Session) -> List[int]:
        """获取用户可访问的部门ID列表 - 完全动态权限获取"""
        try:
            # 调试日志
            logger.info(f"[DEBUG] 获取用户可访问部门 - 用户ID: {user.id}, 用户名: {user.username}, 部门ID: {getattr(user, 'department_id', None)}")

            # 超级管理员可以访问所有部门
            if user_crud.is_superuser(user):
                logger.info(f"[DEBUG] 用户是超级管理员，返回所有部门")
                return self._get_all_department_ids(db)

            # 获取用户的数据权限范围
            data_scope = self.get_user_data_scope(user, db)
            logger.info(f"[DEBUG] 用户数据权限范围: {data_scope}")

            # 使用策略模式处理不同的权限范围
            accessible_depts = self._get_departments_by_scope(user, data_scope, db)
            logger.info(f"[DEBUG] 可访问部门ID列表: {accessible_depts}")

            return accessible_depts

        except Exception as e:
            logger.error(f"获取可访问部门列表失败: {e}")
            return []

    def _get_all_department_ids(self, db: Session) -> List[int]:
        """获取所有部门ID"""
        from app.crud.department import department as department_crud
        departments = department_crud.get_multi(db)
        return [d.id for d in departments]

    def _get_departments_by_scope(self, user: User, data_scope: str, db: Session) -> List[int]:
        """根据数据权限范围获取可访问的部门"""
        scope_handlers = {
            "all": lambda: self._get_all_department_ids(db),
            "merchant": lambda: self._get_merchant_department_ids(user, db),
            "department_sub": lambda: self._get_user_department_and_sub_ids(user, db),  # 新增：本部门及子部门
            "department": lambda: self._get_user_department_ids(user),
            "self": lambda: []
        }

        handler = scope_handlers.get(data_scope, lambda: [])
        return handler()

    def _get_merchant_department_ids(self, user: User, db: Session) -> List[int]:
        """获取用户商户下的所有部门ID"""
        if hasattr(user, 'merchant_id') and user.merchant_id:
            from app.crud.department import department as department_crud
            departments = department_crud.get_by_merchant(db, merchant_id=user.merchant_id)
            return [d.id for d in departments]
        return []

    def _get_user_department_ids(self, user: User) -> List[int]:
        """获取用户自己的部门ID"""
        return [user.department_id] if hasattr(user, 'department_id') and user.department_id else []

    def _get_user_department_and_sub_ids(self, user: User, db: Session) -> List[int]:
        """获取用户部门及其所有子部门的ID列表"""
        try:
            logger.info(f"[DEBUG] 开始获取用户部门及子部门 - 用户ID: {user.id}, 部门ID: {getattr(user, 'department_id', None)}")

            if not hasattr(user, 'department_id') or not user.department_id:
                logger.info(f"[DEBUG] 用户没有部门ID，返回空列表")
                return []

            # 首先包含用户自己的部门
            accessible_dept_ids = [user.department_id]
            logger.info(f"[DEBUG] 初始部门列表: {accessible_dept_ids}")

            # 查询用户部门的路径信息
            from sqlalchemy import text
            sql = text("SELECT path FROM departments WHERE id = :dept_id")
            result = db.execute(sql, {'dept_id': user.department_id}).fetchone()
            logger.info(f"[DEBUG] 用户部门路径查询结果: {result.path if result else None}")

            if result and result.path:
                user_dept_path = result.path
                # 查询所有子部门：path以"用户部门路径+用户部门ID/"开头的部门
                expected_prefix = f"{user_dept_path}{user.department_id}/"
                logger.info(f"[DEBUG] 子部门查询模式: {expected_prefix}%")

                sub_sql = text("""
                    SELECT id, name, path FROM departments
                    WHERE path LIKE :path_pattern
                    AND merchant_id = :merchant_id
                """)

                sub_results = db.execute(sub_sql, {
                    'path_pattern': f"{expected_prefix}%",
                    'merchant_id': user.merchant_id if hasattr(user, 'merchant_id') else None
                }).fetchall()

                logger.info(f"[DEBUG] 找到 {len(sub_results)} 个子部门")

                # 添加所有子部门ID
                for row in sub_results:
                    accessible_dept_ids.append(row.id)
                    logger.info(f"[DEBUG] 添加子部门: ID={row.id}, 名称={row.name}, 路径={row.path}")

            logger.info(f"[DEBUG] 最终可访问部门列表: {accessible_dept_ids}")
            return accessible_dept_ids

        except Exception as e:
            logger.error(f"获取用户部门及子部门ID失败: {e}")
            # 出错时至少返回用户自己的部门ID
            return [user.department_id] if hasattr(user, 'department_id') and user.department_id else []

    def check_menu_permission(self, db: Session, user: User, menu_code: str) -> bool:
        """检查用户是否有访问指定菜单的权限"""
        try:
            # 使用菜单服务检查权限
            from app.services.menu_service import menu_service
            return menu_service.check_menu_permission(db, user, menu_code)

        except Exception as e:
            logger.error(f"检查菜单权限失败: {e}")
            return False


# 创建全局实例
auth_service = AuthService()
