from typing import Optional
from sqlalchemy.orm import Session

from app.crud.base import CRUDBase
from app.models.walmart_server import WalmartServer
from app.schemas.walmart_server import (
    WalmartServerCreate,
    WalmartServerUpdate,
)


class CRUDWalmartServer(
    CRUDBase[WalmartServer, WalmartServerCreate, WalmartServerUpdate]
):
    """WalmartServer的CRUD操作类"""

    def get_first(self, db: Session) -> Optional[WalmartServer]:
        """获取第一条配置记录"""
        return db.query(WalmartServer).first()


walmart_server_config = CRUDWalmartServer(WalmartServer)
