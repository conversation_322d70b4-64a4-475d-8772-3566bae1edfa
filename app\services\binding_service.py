import uuid
from datetime import datetime
from typing import Dict, Any, Optional, <PERSON><PERSON>
from time import time

from sqlalchemy.orm import Session
from sqlalchemy.ext.asyncio import AsyncSession
from typing import Union
from app.core.logging import get_logger
from app.crud import card as card_crud
from app.crud.card import get_card_record_async
from app.models import CardStatus
from app.models.binding_log import LogLevel
from app.schemas.card import CardRecordCreate
from app.services.binding_log_service import BindingLogService
from app.services.binding_process_service import BindingProcessService
from app.utils.time_utils import (
    get_current_time,
    get_current_timestamp,
    datetime_to_isoformat,
    calculate_duration_ms
)

# 创建日志记录器
logger = get_logger("binding_service")


class BindingService:
    """
    绑卡服务，处理卡绑定的业务逻辑
    """

    def __init__(self):
        self.process_service = BindingProcessService()

    async def bind_card(
        self,
        db: Union[Session, AsyncSession],
        card_number: str,
        card_password: str,
        user_id: str,
        merchant_id: int,
        trace_id: Optional[str] = None,
        extra_params: Optional[Dict[str, Any]] = None,
    ) -> Tuple[bool, Dict[str, Any]]:
        """
        绑定卡 - 仅创建记录，不处理绑卡逻辑
        绑卡处理由消费者通过process_from_queue方法执行

        Args:
            db: 数据库会话
            card_number: 卡号
            card_password: 卡密码
            user_id: 用户ID
            merchant_id: 商家ID
            trace_id: 追踪ID
            extra_params: 额外参数

        Returns:
            Tuple[bool, Dict[str, Any]]: (是否成功, 结果数据)

        Raises:
            ValueError: 当参数验证失败时
        """
        start_time = get_current_timestamp()
        if not trace_id:
            trace_id = str(uuid.uuid4())

        # 严格的参数验证
        if not card_number or not isinstance(card_number, str) or not card_number.strip():
            logger.error(f"[BIND_INIT] 绑卡失败: 卡号参数无效 | trace_id={trace_id}")
            raise ValueError("卡号不能为空、None或空字符串")

        if not card_password or not isinstance(card_password, str) or not card_password.strip():
            logger.error(f"[BIND_INIT] 绑卡失败: 卡密码参数无效 | trace_id={trace_id}")
            raise ValueError("卡密码不能为空、None或空字符串")

        if not user_id or not isinstance(user_id, str) or not user_id.strip():
            logger.error(f"[BIND_INIT] 绑卡失败: 用户ID参数无效 | trace_id={trace_id}")
            raise ValueError("用户ID不能为空、None或空字符串")

        if not isinstance(merchant_id, int) or merchant_id <= 0:
            logger.error(f"[BIND_INIT] 绑卡失败: 商户ID参数无效 | trace_id={trace_id}")
            raise ValueError("商户ID必须是正整数")

        # 清理参数（去除首尾空格）
        card_number = card_number.strip()
        card_password = card_password.strip()
        user_id = user_id.strip()

        # 记录初始请求日志
        logger.info(
            f"[BIND_INIT] 收到绑卡请求（参数验证通过） | trace_id={trace_id} | card_number={card_number[:6]}*** | user_id={user_id} | merchant_id={merchant_id}"
        )

        # 创建绑卡记录
        create_data = CardRecordCreate(
            card_number=card_number,
            card_password=card_password,
            user_id=user_id,
            trace_id=trace_id,
            merchant_id=merchant_id,
            status=CardStatus.PENDING,  # 明确设置为PENDING状态
        )
        record = card_crud.create_card_record(db, create_data)

        # 记录创建记录的详细日志
        duration_ms = calculate_duration_ms(start_time)
        logger.info(
            f"[BIND_CREATED] 绑卡请求已创建 | record_id={record.id} | trace_id={trace_id} | duration_ms={duration_ms:.2f}"
        )

        # 记录系统日志到数据库
        # 【安全修复】创建BindingLogService实例
        binding_log_service = BindingLogService(db)
        await binding_log_service.log_system(
            db=db,
            card_record_id=str(record.id),
            message=f"绑卡请求已创建，等待队列处理",
            log_level=LogLevel.INFO,
            details={
                "trace_id": trace_id,
                "user_id": user_id,
                "merchant_id": merchant_id,
                "duration_ms": round(duration_ms, 2),
                "created_at": datetime_to_isoformat(get_current_time()),
            },
        )

        return True, {
            "recordId": record.id,
            "traceId": trace_id,
            "status": "pending",
        }

    async def _process_binding(
        self,
        db: Union[Session, AsyncSession],
        record_id: int,
        merchant_id: int,
        unencrypted_card_password: Optional[str],
        ext_data: Optional[str] = None,
        extra_params: Optional[Dict[str, Any]] = None,
        is_recovery: bool = False,
        recovery_attempt: int = 0,
        debug: bool = False,
    ):
        """
        处理绑卡请求 - 委托给 BindingProcessService

        Args:
            db: 数据库会话
            record_id: 记录ID
            merchant_id: 商家ID
            unencrypted_card_password: 未加密的卡密
            ext_data: 扩展数据
            extra_params: 额外参数
            is_recovery: 是否是恢复处理
            recovery_attempt: 恢复尝试次数
        """
        await self.process_service.process_binding_request(
            db=db,
            record_id=record_id,
            merchant_id=merchant_id,
            unencrypted_card_password=unencrypted_card_password,
            ext_data=ext_data,
            extra_params=extra_params,
            is_recovery=is_recovery,
            recovery_attempt=recovery_attempt,
            debug=debug,
        )






    async def process_from_queue(self, db: Union[Session, AsyncSession], data: Dict[str, Any]):
        """从队列中处理绑卡任务"""
        queue_start_time = time()

        # 解析队列数据
        queue_data = self._parse_queue_data(data)
        if not queue_data:
            return

        # 记录开始日志
        self._log_queue_start(queue_data)

        # 记录系统日志到数据库
        await self._log_queue_system(db, queue_data)

        # 处理绑卡请求
        try:
            # 处理恢复逻辑
            if queue_data.is_recovery:
                queue_data.unencrypted_card_password = await self._handle_recovery_logic(
                    db, queue_data
                )
                if queue_data.unencrypted_card_password is None:
                    return

            # 执行绑卡处理
            await self._process_binding(
                db,
                queue_data.record_id,
                queue_data.merchant_id,
                queue_data.unencrypted_card_password,
                queue_data.ext_data,
                queue_data.extra_params,
                is_recovery=queue_data.is_recovery,
                recovery_attempt=queue_data.recovery_attempt,
                debug=queue_data.debug,
            )

            # 记录完成日志
            self._log_queue_complete(queue_data, queue_start_time)

        except Exception as e:
            await self._handle_queue_exception(db, queue_data, e, queue_start_time)

    def _parse_queue_data(self, data: Dict[str, Any]):
        """解析队列数据"""
        from dataclasses import dataclass

        @dataclass
        class QueueData:
            record_id: str
            merchant_id: int
            trace_id: str
            client_ip: str
            unencrypted_card_password: str
            extra_params: dict
            ext_data: str
            card_number: str
            is_recovery: bool
            recovery_attempt: int
            debug: bool

        record_id = data.get("record_id")
        merchant_id = data.get("merchant_id")

        if not record_id or not merchant_id:
            logger.error(
                f"[QUEUE_CONSUME_ERROR] 无效的队列消息 | 缺少record_id或merchant_id | data={data}"
            )
            return None

        return QueueData(
            record_id=record_id,
            merchant_id=merchant_id,
            trace_id=data.get("trace_id", "unknown"),
            client_ip=data.get("client_ip", "unknown"),
            unencrypted_card_password=data.get("card_password"),
            extra_params=data.get("extra_params"),
            ext_data=data.get("ext_data"),
            card_number=data.get("card_number", "unknown"),
            is_recovery=data.get("is_recovery", False),
            recovery_attempt=data.get("recovery_attempt", 0),
            debug=data.get("debug", False)
        )

    def _log_queue_start(self, queue_data):
        """记录队列开始日志"""
        log_prefix = "[RECOVERY_CONSUME_START]" if queue_data.is_recovery else "[QUEUE_CONSUME_START]"
        test_prefix = "[TEST_MODE] " if queue_data.debug else ""
        recovery_info = f" | recovery_attempt={queue_data.recovery_attempt}" if queue_data.is_recovery else ""
        card_display = queue_data.card_number[:6] if queue_data.card_number != 'unknown' else 'unknown'

        logger.info(
            f"{test_prefix}{log_prefix} 从队列接收绑卡任务 | record_id={queue_data.record_id} | "
            f"trace_id={queue_data.trace_id} | merchant_id={queue_data.merchant_id} | "
            f"ip={queue_data.client_ip} | card_number={card_display}***{recovery_info}"
        )


    async def _log_queue_system(self, db: Union[Session, AsyncSession], queue_data):
        """记录系统日志到数据库"""
        try:
            message = "从队列接收到恢复处理任务" if queue_data.is_recovery else "从队列接收到绑卡任务"
            # 【安全修复】创建BindingLogService实例
            binding_log_service = BindingLogService(db)
            await binding_log_service.log_system(
                db=db,
                card_record_id=str(queue_data.record_id),
                message=message,
                log_level=LogLevel.INFO,
                details={
                    "trace_id": queue_data.trace_id,
                    "merchant_id": queue_data.merchant_id,
                    "client_ip": queue_data.client_ip,
                    "queue_received_at": datetime.now().isoformat(),
                    "has_card_password": queue_data.unencrypted_card_password is not None,
                    "has_ext_data": queue_data.ext_data is not None,
                    "is_recovery": queue_data.is_recovery,
                    "recovery_attempt": queue_data.recovery_attempt if queue_data.is_recovery else None,
                },
            )
        except Exception as e:
            logger.error(f"[QUEUE_LOG_ERROR] 记录队列消费日志失败 | error={str(e)}")

    async def _handle_recovery_logic(self, db: Union[Session, AsyncSession], queue_data):
        """处理恢复逻辑"""
        # 检查是否为异步会话，使用相应的方法
        if isinstance(db, AsyncSession):
            record = await get_card_record_async(db, queue_data.record_id, queue_data.merchant_id)
        else:
            record = card_crud.get_card_record(db, queue_data.record_id, queue_data.merchant_id)
        if not record:
            logger.error(f"[RECOVERY_ERROR] 恢复处理的记录不存在 | record_id={queue_data.record_id}")
            return None

        # 如果记录已经不是PENDING状态，则跳过处理
        if record.status != CardStatus.PENDING:
            logger.info(
                f"[RECOVERY_SKIP] 跳过非PENDING状态记录 | record_id={queue_data.record_id} | "
                f"current_status={record.status}"
            )
            # 【安全修复】创建BindingLogService实例
            binding_log_service = BindingLogService(db)
            await binding_log_service.log_system(
                db=db,
                card_record_id=str(queue_data.record_id),
                message=f"恢复处理跳过：记录状态已变更为{record.status}",
                log_level=LogLevel.INFO,
            )
            return None

        # 如果卡密为空但记录中有加密的卡密，尝试从记录中获取
        if queue_data.unencrypted_card_password is None and record.card_password:
            logger.info(
                f"[RECOVERY_INFO] 恢复处理从数据库获取卡密 | record_id={queue_data.record_id}"
            )
            try:
                from app.core.security import decrypt_sensitive_data
                unencrypted_password = decrypt_sensitive_data(record.card_password)
                logger.info(f"[RECOVERY_INFO] 成功从记录中获取并解密卡密 | record_id={queue_data.record_id}")
                return unencrypted_password
            except Exception as e:
                logger.error(
                    f"[RECOVERY_ERROR] 解密卡密失败 | record_id={queue_data.record_id} | error={str(e)}"
                )

        return queue_data.unencrypted_card_password

    def _log_queue_complete(self, queue_data, queue_start_time):
        """记录队列完成日志"""
        queue_duration_ms = (time() - queue_start_time) * 1000
        logger.info(
            f"[QUEUE_CONSUME_COMPLETE] 队列任务处理完成 | record_id={queue_data.record_id} | "
            f"trace_id={queue_data.trace_id} | duration_ms={queue_duration_ms:.2f}"
        )

    async def _handle_queue_exception(self, db: Union[Session, AsyncSession], queue_data, exception: Exception, queue_start_time):
        """处理队列异常"""
        queue_duration_ms = (time() - queue_start_time) * 1000
        logger.error(
            f"[QUEUE_CONSUME_ERROR] 队列任务处理异常 | record_id={queue_data.record_id} | "
            f"trace_id={queue_data.trace_id} | duration_ms={queue_duration_ms:.2f} | error={str(exception)}"
        )

        # 尝试记录到数据库
        try:
            # 【安全修复】创建BindingLogService实例
            binding_log_service = BindingLogService(db)
            await binding_log_service.log_error(
                db=db,
                card_record_id=str(queue_data.record_id),
                error_message=f"队列处理异常: {str(exception)}",
                details={
                    "trace_id": queue_data.trace_id,
                    "duration_ms": round(queue_duration_ms, 2),
                    "exception": str(exception),
                    "queue_data": {
                        "record_id": queue_data.record_id,
                        "merchant_id": queue_data.merchant_id,
                        "trace_id": queue_data.trace_id,
                        "is_recovery": queue_data.is_recovery,
                    },
                },
            )
        except Exception as log_error:
            logger.error(f"[QUEUE_LOG_ERROR] 记录队列异常日志失败 | error={str(log_error)}")


# 创建绑卡服务实例
binding_service = BindingService()
