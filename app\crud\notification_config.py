from typing import Optional
from sqlalchemy.orm import Session
from app.models.notification_config import NotificationConfig


class NotificationConfigCRUD:
    """通知配置 CRUD 操作"""

    def get_config(self, db: Session) -> Optional[NotificationConfig]:
        """获取通知配置（默认获取第一条记录）"""
        return db.query(NotificationConfig).first()

    def create_default_config(self, db: Session) -> NotificationConfig:
        """创建默认配置"""
        config = NotificationConfig(
            # 默认所有通知渠道都禁用
            wechat_enabled=False,
            dingtalk_enabled=False,
            telegram_enabled=False,
            email_enabled=False,
            # 默认告警阈值
            alert_threshold_warning=5,
            alert_threshold_error=3,
            alert_threshold_critical=1,
        )
        db.add(config)
        db.commit()
        db.refresh(config)
        return config

    def update_config(
        self, db: Session, config_id: int, update_data: dict
    ) -> Optional[NotificationConfig]:
        """更新通知配置"""
        config = (
            db.query(NotificationConfig)
            .filter(NotificationConfig.id == config_id)
            .first()
        )
        if not config:
            return None

        for key, value in update_data.items():
            if hasattr(config, key):
                setattr(config, key, value)

        db.commit()
        db.refresh(config)
        return config

    def get_or_create_config(self, db: Session) -> NotificationConfig:
        """获取配置，如果不存在则创建默认配置"""
        config = self.get_config(db)
        if not config:
            config = self.create_default_config(db)
        return config


notification_config_crud = NotificationConfigCRUD()
