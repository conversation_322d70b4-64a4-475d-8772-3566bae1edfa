<template>
  <!-- 如果有子菜单，渲染为子菜单 -->
  <el-sub-menu v-if="hasChildren" :index="getMenuPath(menu)" :key="menu.code || 'sub_' + menu.name">
    <template #title>
      <el-icon v-if="menu.icon">
        <component :is="getIconComponent(menu.icon)" />
      </el-icon>
      <span v-if="!collapse">{{ menu.name }}</span>
    </template>

    <!-- 递归渲染子菜单 -->
    <menu-item v-for="child in menu.children" :key="child.code || 'child_' + child.name" :menu="child"
      :collapse="collapse" />
  </el-sub-menu>

  <!-- 如果没有子菜单，渲染为普通菜单项 -->
  <el-menu-item v-else :index="getMenuPath(menu)" :key="menu.code || 'item_' + menu.name"
    @click="handleMenuClick(menu)">
    <el-icon v-if="menu.icon">
      <component :is="getIconComponent(menu.icon)" />
    </el-icon>
    <span v-if="!collapse">{{ menu.name }}</span>
  </el-menu-item>
</template>

<script setup>
import { computed } from 'vue'
import { useRouter } from 'vue-router'
import {
  DataBoard,
  Setting,
  OfficeBuilding,
  CreditCard,
  User,
  UserFilled,
  Key,
  Menu,
  Tools,
  Refresh,
  Document,
  Bell,
  Cpu,
  VideoPlay
} from '@element-plus/icons-vue'

const props = defineProps({
  menu: {
    type: Object,
    required: true
  },
  collapse: {
    type: Boolean,
    default: false
  }
})

const router = useRouter()

// 图标映射
const iconMap = {
  'el-icon-data-board': DataBoard,
  'el-icon-setting': Setting,
  'el-icon-office-building': OfficeBuilding,
  'el-icon-bank-card': CreditCard,
  'el-icon-user': User,
  'el-icon-user-solid': UserFilled,
  'el-icon-key': Key,
  'el-icon-menu': Menu,
  'el-icon-tools': Tools,
  'el-icon-refresh': Refresh,
  'el-icon-document': Document,
  'el-icon-bell': Bell,
  'el-icon-cpu': Cpu,
  'el-icon-video-play': VideoPlay
}

// 判断是否有子菜单
const hasChildren = computed(() => {
  return props.menu.children && props.menu.children.length > 0
})

// 获取图标组件
const getIconComponent = (iconName) => {
  return iconMap[iconName] || Setting
}

// 获取菜单路径
const getMenuPath = (menu) => {
  // 如果菜单有完整路径，直接使用
  if (menu.path && menu.path.startsWith('/')) {
    return menu.path
  }

  // 根据菜单代码构建路径 - 支持更多的动态菜单
  const pathMap = {
    'dashboard': '/dashboard',
    'cards': '/cards',
    'walmart:walmart': '/walmart/walmart-server',
    'walmart:user': '/walmart',
    'system:user': '/system/user',
    'system:role': '/system/role',
    'system:permission': '/system/permission',
    'system:menu': '/system/menu',
    'system:settings': '/system/settings',
    'system:recovery': '/system/recovery',
    'merchant:list': '/merchant/list',
    'merchant:department': '/merchant/department',
    'notification': '/notification',
    'bind:single': '/bind/single-bind',
    'bind:batch': '/bind/batch-bind',
    'single-bind': '/bind/single-bind',
    'batch-bind': '/bind/batch-bind'
  }

  // 如果在映射表中找到，使用映射的路径
  if (pathMap[menu.code]) {
    return pathMap[menu.code]
  }

  // 否则根据菜单代码生成路径
  // 处理带冒号的菜单代码，如 system:user -> /system/user
  if (menu.code && menu.code.includes(':')) {
    return '/' + menu.code.replace(':', '/')
  }

  return `/${menu.code || 'unknown'}`
}

// 处理菜单点击
const handleMenuClick = (menu) => {
  const path = getMenuPath(menu)
  console.log('点击菜单:', menu.name, '路径:', path)

  // 导航到对应路由
  if (path !== router.currentRoute.value.path) {
    router.push(path).catch(err => {
      console.warn('路由跳转警告:', err)
    })
  }
}
</script>

<style lang="scss" scoped>
// 菜单项样式已在父组件中定义</style>
