from typing import Any, Dict, Generic, List, Optional, Type, TypeVar, Union

from fastapi.encoders import jsonable_encoder
from pydantic import BaseModel
from sqlalchemy.orm import Session

from app.models.base import BaseModel

ModelType = TypeVar("ModelType", bound=BaseModel)
CreateSchemaType = TypeVar("CreateSchemaType", bound=BaseModel)
UpdateSchemaType = TypeVar("UpdateSchemaType", bound=BaseModel)


class CRUDBase(Generic[ModelType, CreateSchemaType, UpdateSchemaType]):
    """
    CRUD操作的基类，提供了标准的创建、读取、更新、删除操作。

    Attributes:
        model: SQLAlchemy模型类
    """

    def __init__(self, model: Type[ModelType]):
        """
        初始化CRUD对象。

        Args:
            model: 要操作的SQLAlchemy模型类
        """
        self.model = model

    def get(self, db: Session, id: Any) -> Optional[ModelType]:
        """
        通过ID获取模型实例。

        Args:
            db: 数据库会话
            id: 记录ID

        Returns:
            找到的记录，如果不存在则返回None
        """
        return db.query(self.model).filter(self.model.id == id).first()

    def get_by_api_key(self, db: Session, api_key: str) -> Optional[ModelType]:
        """
        通过API密钥获取模型实例（针对有API密钥的模型）。

        Args:
            db: 数据库会话
            api_key: API密钥

        Returns:
            找到的记录，如果不存在则返回None
        """
        return db.query(self.model).filter(self.model.api_key == api_key).first()

    def get_multi(
        self, db: Session, *, skip: int = 0, limit: int = 100
    ) -> List[ModelType]:
        """
        获取多条记录。

        Args:
            db: 数据库会话
            skip: 跳过的记录数
            limit: 返回的最大记录数

        Returns:
            记录列表
        """
        return db.query(self.model).offset(skip).limit(limit).all()

    def create(self, db: Session, *, obj_in: CreateSchemaType) -> ModelType:
        """
        创建新记录。

        Args:
            db: 数据库会话
            obj_in: 要创建的对象数据

        Returns:
            创建的记录
        """
        obj_in_data = jsonable_encoder(obj_in)
        db_obj = self.model(**obj_in_data)  # type: ignore
        db.add(db_obj)
        db.commit()
        db.refresh(db_obj)
        return db_obj

    def update(
        self,
        db: Session,
        *,
        db_obj: ModelType,
        obj_in: Union[UpdateSchemaType, Dict[str, Any]]
    ) -> ModelType:
        """
        更新记录。

        Args:
            db: 数据库会话
            db_obj: 要更新的数据库对象
            obj_in: 新数据

        Returns:
            更新后的记录
        """
        obj_data = jsonable_encoder(db_obj)
        if isinstance(obj_in, dict):
            update_data = obj_in
        else:
            update_data = obj_in.dict(exclude_unset=True)
        for field in obj_data:
            if field in update_data:
                setattr(db_obj, field, update_data[field])
        db.add(db_obj)
        db.commit()
        db.refresh(db_obj)
        return db_obj

    def remove(self, db: Session, *, id: int) -> ModelType:
        """
        删除记录。

        Args:
            db: 数据库会话
            id: 要删除的记录ID

        Returns:
            删除的记录
        """
        obj = db.query(self.model).get(id)
        db.delete(obj)
        db.commit()
        return obj
