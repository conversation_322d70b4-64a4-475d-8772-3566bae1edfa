# Air配置文件 - Go热重载工具
# 用于沃尔玛绑卡网关开发环境

root = "."
testdata_dir = "testdata"
tmp_dir = "tmp"

[build]
  # 构建命令
  cmd = "go build -o ./tmp/main ."
  # 构建后的可执行文件
  bin = "tmp/main"
  # 构建时包含的文件扩展名
  include_ext = ["go", "tpl", "tmpl", "html", "yaml", "yml", "json"]
  # 构建时排除的目录
  exclude_dir = ["assets", "tmp", "vendor", "testdata", "dist", "logs", ".git", ".vscode", ".idea"]
  # 构建时排除的文件
  exclude_file = []
  # 构建时排除的正则表达式
  exclude_regex = ["_test.go"]
  # 构建时排除的文件名
  exclude_unchanged = false
  # 构建延迟时间（毫秒）
  delay = 1000
  # 停止运行旧的二进制文件
  stop_on_root = false
  # 发送中断信号前的延迟时间（毫秒）
  send_interrupt = false
  # 发送终止信号前的延迟时间（毫秒）
  kill_delay = "0s"
  # 构建日志
  log = "build-errors.log"
  # 构建参数
  args_bin = []
  # 全屏输出
  full_bin = ""
  # 环境变量
  env = []

[color]
  # 自定义每个部分显示的颜色
  main = "magenta"
  watcher = "cyan"
  build = "yellow"
  runner = "green"

[log]
  # 显示日志时间
  time = true
  # 只显示主要日志
  main_only = false

[misc]
  # 删除每次构建的临时目录
  clean_on_exit = false

[screen]
  # 清屏
  clear_on_rebuild = false
  # 保持滚动
  keep_scroll = true
