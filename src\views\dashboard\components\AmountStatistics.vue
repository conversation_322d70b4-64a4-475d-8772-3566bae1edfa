<template>
  <el-card class="amount-statistics-card" shadow="hover">
    <template #header>
      <div class="card-header">
        <span>绑卡金额统计</span>
        <el-radio-group v-model="timeRange" size="small" @change="fetchData">
          <el-radio-button value="today">今日</el-radio-button>
          <el-radio-button value="week">本周</el-radio-button>
          <el-radio-button value="month">本月</el-radio-button>
        </el-radio-group>
      </div>
    </template>

    <div v-loading="loading" class="statistics-content">
      <!-- 第一行：基础统计 -->
      <el-row :gutter="16" class="mb-3">
        <el-col :xs="24" :sm="12" :md="6">
          <div class="stat-item">
            <div class="stat-value">{{ formatAmount(data.total_requested_amount_yuan || data.total_amount_yuan) }}</div>
            <div class="stat-label">总请求金额</div>
            <div class="stat-count">{{ data.total_requests }}笔</div>
          </div>
        </el-col>
        <el-col :xs="24" :sm="12" :md="6">
          <div class="stat-item success">
            <div class="stat-value">{{ formatAmount(data.success_actual_amount_yuan || data.success_amount_yuan) }}
            </div>
            <div class="stat-label">实际绑卡金额</div>
            <div class="stat-count">{{ data.success_count }}笔</div>
          </div>
        </el-col>
        <el-col :xs="24" :sm="12" :md="6">
          <div class="stat-item failed">
            <div class="stat-value">{{ formatAmount(data.failed_requested_amount_yuan || data.failed_amount_yuan) }}
            </div>
            <div class="stat-label">失败金额</div>
            <div class="stat-count">{{ data.failed_count }}笔</div>
          </div>
        </el-col>
        <el-col :xs="24" :sm="12" :md="6">
          <div class="stat-item rate">
            <div class="stat-value">{{ formatNumber(data.success_rate, 0).toFixed(2) }}%</div>
            <div class="stat-label">绑卡成功率</div>
            <el-progress :percentage="formatNumber(data.success_rate, 0)"
              :color="getSuccessRateColor(data.success_rate)" :show-text="false" :stroke-width="6"
              style="margin-top: 8px;" />
          </div>
        </el-col>
      </el-row>

      <!-- 第二行：金额分析（仅在有实际金额数据时显示） -->
      <el-row :gutter="16" v-if="hasActualAmountData">
        <el-col :xs="24" :sm="12" :md="6">
          <div class="stat-item analysis">
            <div class="stat-value">{{ formatAmount(data.success_requested_amount_yuan) }}</div>
            <div class="stat-label">成功请求金额</div>
            <div class="stat-count">原始提交金额</div>
          </div>
        </el-col>
        <el-col :xs="24" :sm="12" :md="6">
          <div class="stat-item analysis">
            <div class="stat-value">{{ formatAmount(Math.abs(Number(data.amount_difference_yuan) || 0)) }}</div>
            <div class="stat-label">金额差异</div>
            <div class="stat-count" :class="getDifferenceClass()">
              {{ getDifferenceText() }}
            </div>
          </div>
        </el-col>
        <el-col :xs="24" :sm="12" :md="6">
          <div class="stat-item analysis">
            <div class="stat-value">{{ formatNumber(data.amount_accuracy_rate, 0).toFixed(2) }}%</div>
            <div class="stat-label">金额准确率</div>
            <div class="stat-count">实际/请求比例</div>
          </div>
        </el-col>
        <el-col :xs="24" :sm="12" :md="6">
          <div class="stat-item analysis">
            <div class="stat-value">{{ getAverageAmount() }}</div>
            <div class="stat-label">平均绑卡金额</div>
            <div class="stat-count">单笔平均</div>
          </div>
        </el-col>
      </el-row>
    </div>
  </el-card>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from 'vue'
import { dashboardApi } from '@/api/modules/dashboard'
import { useUserStore } from '@/store/modules/user'
import { usePermissionStore } from '@/store/modules/permission'
import { useMerchantStore } from '@/store/modules/merchant'

const userStore = useUserStore()
const permissionStore = usePermissionStore()
const merchantStore = useMerchantStore()

const loading = ref(false)
const timeRange = ref('today')

const data = reactive({
  total_requests: 0,
  success_count: 0,
  failed_count: 0,
  success_rate: 0,

  // 请求金额数据（向后兼容）
  total_amount_yuan: 0,
  success_amount_yuan: 0,
  failed_amount_yuan: 0,

  // 新增：请求金额数据（明确命名）
  total_requested_amount_yuan: 0,
  success_requested_amount_yuan: 0,
  failed_requested_amount_yuan: 0,

  // 新增：实际金额数据
  success_actual_amount_yuan: 0,
  total_actual_amount_yuan: 0,

  // 新增：金额分析数据
  amount_difference_yuan: 0,
  amount_accuracy_rate: 0
})

const formatAmount = (amount) => {
  // 处理 null、undefined、空字符串等情况
  if (amount === null || amount === undefined || amount === '' || isNaN(amount)) {
    return '0.00'
  }

  // 确保转换为数字类型
  const numAmount = Number(amount)

  // 再次检查是否为有效数字
  if (isNaN(numAmount)) {
    return '0.00'
  }

  return numAmount.toFixed(2)
}

// 安全的数字格式化函数
const formatNumber = (value, defaultValue = 0) => {
  if (value === null || value === undefined || value === '' || isNaN(value)) {
    return defaultValue
  }
  const numValue = Number(value)
  return isNaN(numValue) ? defaultValue : numValue
}

const getSuccessRateColor = (rate) => {
  const numRate = formatNumber(rate, 0)
  if (numRate >= 90) return '#67C23A'
  if (numRate >= 70) return '#E6A23C'
  return '#F56C6C'
}

// 检查是否有实际金额数据
const hasActualAmountData = computed(() => {
  const actualAmount = formatNumber(data.success_actual_amount_yuan, 0)
  const difference = formatNumber(data.amount_difference_yuan, 0)
  return actualAmount > 0 || difference !== 0
})

// 获取金额差异的样式类
const getDifferenceClass = () => {
  const diff = formatNumber(data.amount_difference_yuan, 0)
  if (diff > 0) return 'positive-diff'
  if (diff < 0) return 'negative-diff'
  return 'neutral-diff'
}

// 获取金额差异的文本描述
const getDifferenceText = () => {
  const diff = formatNumber(data.amount_difference_yuan, 0)
  if (diff > 0) return '实际金额更高'
  if (diff < 0) return '实际金额更低'
  return '金额一致'
}

// 计算平均绑卡金额
const getAverageAmount = () => {
  const actualAmount = Number(data.success_actual_amount_yuan || data.success_amount_yuan || 0)
  const count = Number(data.success_count || 0)

  // 检查数据有效性
  if (count === 0 || isNaN(actualAmount) || isNaN(count)) {
    return '0.00'
  }

  return formatAmount(actualAmount / count)
}

const fetchData = async () => {
  loading.value = true
  try {
    const params = {
      time_range: timeRange.value
    }

    // 根据用户角色确定查询参数
    if (permissionStore.isSuperAdmin) {
      // 超级管理员可以查看选择的商户数据
      if (merchantStore.currentMerchantId) {
        params.merchant_id = merchantStore.currentMerchantId
      }
    } else {
      // 商户管理员只能查看自己商户的数据
      params.merchant_id = userStore.merchantId
    }

    const response = await dashboardApi.getAmountStatistics(params)
    Object.assign(data, response.data)
  } catch (error) {
    console.error('获取金额统计失败:', error)
  } finally {
    loading.value = false
  }
}

onMounted(() => {
  fetchData()
})

// 暴露刷新方法给父组件
defineExpose({
  refresh: fetchData
})
</script>

<style scoped>
.amount-statistics-card {
  margin-bottom: 16px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 16px;
  font-weight: 600;
}

.statistics-content {
  min-height: 120px;
}

.stat-item {
  text-align: center;
  padding: 16px 8px;
  border-radius: 8px;
  background: #f8f9fa;
  margin-bottom: 8px;
}

.stat-item.success {
  background: #f0f9ff;
  border-left: 4px solid #67C23A;
}

.stat-item.failed {
  background: #fef2f2;
  border-left: 4px solid #F56C6C;
}

.stat-item.rate {
  background: #f8f9ff;
  border-left: 4px solid #409EFF;
}

.stat-item.analysis {
  background: #fafafa;
  border-left: 4px solid #909399;
}

.mb-3 {
  margin-bottom: 16px;
}

.positive-diff {
  color: #67C23A;
}

.negative-diff {
  color: #F56C6C;
}

.neutral-diff {
  color: #909399;
}

.stat-value {
  font-size: 24px;
  font-weight: bold;
  color: #303133;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 14px;
  color: #606266;
  margin-bottom: 4px;
}

.stat-count {
  font-size: 12px;
  color: #909399;
}

@media (max-width: 768px) {
  .stat-item {
    margin-bottom: 12px;
  }

  .stat-value {
    font-size: 20px;
  }
}
</style>
