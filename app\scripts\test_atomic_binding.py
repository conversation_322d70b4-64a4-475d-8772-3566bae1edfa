#!/usr/bin/env python3
"""
原子性绑卡服务测试脚本
测试原子性绑卡服务是否能正确处理绑卡成功和失败的情况，确保数据一致性
"""

import asyncio
import sys
import os
import uuid
from datetime import datetime
from typing import Dict, Any, Optional

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from sqlalchemy.orm import Session
from app.db.session import SessionLocal
from app.models.walmart_ck import WalmartCK
from app.models.card_record import CardRecord
from app.services.atomic_binding_service import AtomicBindingService, SafeBindingWrapper
from app.core.logging import get_logger

logger = get_logger(__name__)


class AtomicBindingTester:
    """原子性绑卡服务测试器"""
    
    def __init__(self):
        self.db: Session = SessionLocal()
        self.test_results = []
    
    def __del__(self):
        if hasattr(self, 'db'):
            self.db.close()
    
    async def run_all_tests(self) -> Dict[str, Any]:
        """运行所有测试"""
        logger.info("🧪 开始原子性绑卡服务测试")
        
        tests = [
            ("测试绑卡成功场景", self._test_success_scenario),
            ("测试绑卡失败场景", self._test_failure_scenario),
            ("测试并发安全性", self._test_concurrent_safety),
            ("测试数据一致性", self._test_data_consistency),
            ("测试SafeBindingWrapper", self._test_safe_wrapper)
        ]
        
        passed = 0
        failed = 0
        
        for test_name, test_func in tests:
            try:
                logger.info(f"🔍 {test_name}")
                result = await test_func()
                
                if result['success']:
                    logger.info(f"✅ {test_name}: 通过")
                    passed += 1
                else:
                    logger.error(f"❌ {test_name}: 失败 - {result.get('error', '未知错误')}")
                    failed += 1
                
                self.test_results.append({
                    'name': test_name,
                    'success': result['success'],
                    'details': result
                })
                
            except Exception as e:
                logger.error(f"❌ {test_name}: 异常 - {e}")
                failed += 1
                self.test_results.append({
                    'name': test_name,
                    'success': False,
                    'error': str(e)
                })
        
        summary = {
            'total_tests': len(tests),
            'passed': passed,
            'failed': failed,
            'success_rate': passed / len(tests) * 100,
            'results': self.test_results
        }
        
        logger.info("=" * 80)
        logger.info("📊 测试结果汇总")
        logger.info("=" * 80)
        logger.info(f"总测试数: {summary['total_tests']}")
        logger.info(f"通过: {summary['passed']}")
        logger.info(f"失败: {summary['failed']}")
        logger.info(f"成功率: {summary['success_rate']:.1f}%")
        
        return summary
    
    async def _test_success_scenario(self) -> Dict[str, Any]:
        """测试绑卡成功场景"""
        try:
            # 创建测试数据
            test_ck = await self._create_test_ck()
            test_record = await self._create_test_record(test_ck.merchant_id)
            
            initial_bind_count = test_ck.bind_count
            
            # 准备API结果
            api_result = {
                'walmart_ck_id': test_ck.id,
                'success': True,
                'timestamp': datetime.now().isoformat()
            }
            
            # 使用原子性服务处理绑卡成功
            atomic_service = AtomicBindingService(self.db)
            success = await atomic_service.execute_atomic_binding(
                record_id=str(test_record.id),
                merchant_id=test_record.merchant_id,
                api_result=api_result,
                is_success=True
            )
            
            if not success:
                return {'success': False, 'error': '原子性绑卡操作返回失败'}
            
            # 验证结果
            self.db.refresh(test_record)
            self.db.refresh(test_ck)
            
            # 检查记录状态
            if test_record.status != 'success':
                return {'success': False, 'error': f'记录状态错误: {test_record.status}'}
            
            # 检查CK信息
            if test_record.walmart_ck_id != test_ck.id:
                return {'success': False, 'error': 'CK ID未正确设置'}
            
            # 检查bind_count
            if test_ck.bind_count != initial_bind_count + 1:
                return {'success': False, 'error': f'bind_count未正确更新: {test_ck.bind_count} vs {initial_bind_count + 1}'}
            
            # 清理测试数据
            await self._cleanup_test_data(test_record, test_ck)
            
            return {
                'success': True,
                'details': {
                    'initial_bind_count': initial_bind_count,
                    'final_bind_count': test_ck.bind_count,
                    'record_status': test_record.status
                }
            }
            
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    async def _test_failure_scenario(self) -> Dict[str, Any]:
        """测试绑卡失败场景"""
        try:
            # 创建测试数据
            test_ck = await self._create_test_ck()
            test_record = await self._create_test_record(test_ck.merchant_id)
            
            initial_bind_count = test_ck.bind_count
            
            # 准备API结果
            api_result = {
                'walmart_ck_id': test_ck.id,
                'success': False,
                'error': 'Test failure',
                'timestamp': datetime.now().isoformat()
            }
            
            # 使用原子性服务处理绑卡失败
            atomic_service = AtomicBindingService(self.db)
            success = await atomic_service.execute_atomic_binding(
                record_id=str(test_record.id),
                merchant_id=test_record.merchant_id,
                api_result=api_result,
                is_success=False
            )
            
            if not success:
                return {'success': False, 'error': '原子性绑卡操作返回失败'}
            
            # 验证结果
            self.db.refresh(test_record)
            self.db.refresh(test_ck)
            
            # 检查记录状态
            if test_record.status != 'failed':
                return {'success': False, 'error': f'记录状态错误: {test_record.status}'}
            
            # 检查bind_count（失败时不应该增加）
            if test_ck.bind_count != initial_bind_count:
                return {'success': False, 'error': f'bind_count不应该改变: {test_ck.bind_count} vs {initial_bind_count}'}
            
            # 清理测试数据
            await self._cleanup_test_data(test_record, test_ck)
            
            return {
                'success': True,
                'details': {
                    'initial_bind_count': initial_bind_count,
                    'final_bind_count': test_ck.bind_count,
                    'record_status': test_record.status
                }
            }
            
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    async def _test_concurrent_safety(self) -> Dict[str, Any]:
        """测试并发安全性"""
        try:
            # 创建测试数据
            test_ck = await self._create_test_ck()
            initial_bind_count = test_ck.bind_count
            
            # 创建多个并发任务
            tasks = []
            for i in range(5):
                test_record = await self._create_test_record(test_ck.merchant_id)
                api_result = {
                    'walmart_ck_id': test_ck.id,
                    'success': True,
                    'timestamp': datetime.now().isoformat()
                }
                
                task = self._atomic_binding_task(test_record.id, test_record.merchant_id, api_result)
                tasks.append(task)
            
            # 并发执行
            results = await asyncio.gather(*tasks, return_exceptions=True)
            
            # 检查结果
            successful_bindings = sum(1 for r in results if r is True)
            
            # 验证bind_count
            self.db.refresh(test_ck)
            expected_count = initial_bind_count + successful_bindings
            
            if test_ck.bind_count != expected_count:
                return {
                    'success': False, 
                    'error': f'并发安全性测试失败: bind_count={test_ck.bind_count}, expected={expected_count}'
                }
            
            return {
                'success': True,
                'details': {
                    'concurrent_tasks': len(tasks),
                    'successful_bindings': successful_bindings,
                    'final_bind_count': test_ck.bind_count
                }
            }
            
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    async def _test_data_consistency(self) -> Dict[str, Any]:
        """测试数据一致性"""
        try:
            # 创建测试数据
            test_ck = await self._create_test_ck()
            test_record = await self._create_test_record(test_ck.merchant_id)
            
            # 模拟绑卡成功
            api_result = {
                'walmart_ck_id': test_ck.id,
                'success': True,
                'timestamp': datetime.now().isoformat()
            }
            
            atomic_service = AtomicBindingService(self.db)
            success = await atomic_service.execute_atomic_binding(
                record_id=str(test_record.id),
                merchant_id=test_record.merchant_id,
                api_result=api_result,
                is_success=True
            )
            
            if not success:
                return {'success': False, 'error': '原子性绑卡操作失败'}
            
            # 验证数据一致性
            self.db.refresh(test_record)
            self.db.refresh(test_ck)
            
            # 检查记录中的CK信息与实际CK是否一致
            if test_record.walmart_ck_id != test_ck.id:
                return {'success': False, 'error': 'CK ID不一致'}
            
            if test_record.department_id != test_ck.department_id:
                return {'success': False, 'error': 'department_id不一致'}
            
            # 检查bind_count与实际成功记录数是否一致
            actual_success_count = self.db.query(CardRecord).filter(
                CardRecord.walmart_ck_id == test_ck.id,
                CardRecord.status == 'success'
            ).count()
            
            if test_ck.bind_count < actual_success_count:
                return {'success': False, 'error': f'bind_count小于实际成功记录数: {test_ck.bind_count} < {actual_success_count}'}
            
            return {'success': True, 'details': 'All consistency checks passed'}
            
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    async def _test_safe_wrapper(self) -> Dict[str, Any]:
        """测试SafeBindingWrapper"""
        try:
            # 创建测试数据
            test_ck = await self._create_test_ck()
            test_record = await self._create_test_record(test_ck.merchant_id)
            
            initial_bind_count = test_ck.bind_count
            
            # 使用SafeBindingWrapper
            safe_wrapper = SafeBindingWrapper(self.db)
            success = await safe_wrapper.safe_update_bind_result(
                record_id=str(test_record.id),
                merchant_id=test_record.merchant_id,
                walmart_ck_id=test_ck.id,
                bind_success=True
            )
            
            if not success:
                return {'success': False, 'error': 'SafeBindingWrapper操作失败'}
            
            # 验证结果
            self.db.refresh(test_record)
            self.db.refresh(test_ck)
            
            if test_record.status != 'success':
                return {'success': False, 'error': f'记录状态错误: {test_record.status}'}
            
            if test_ck.bind_count != initial_bind_count + 1:
                return {'success': False, 'error': f'bind_count未正确更新'}
            
            return {'success': True, 'details': 'SafeBindingWrapper test passed'}
            
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    async def _atomic_binding_task(self, record_id: str, merchant_id: int, api_result: Dict[str, Any]) -> bool:
        """原子性绑卡任务（用于并发测试）"""
        try:
            atomic_service = AtomicBindingService(self.db)
            return await atomic_service.execute_atomic_binding(
                record_id=str(record_id),
                merchant_id=merchant_id,
                api_result=api_result,
                is_success=True
            )
        except Exception:
            return False
    
    async def _create_test_ck(self) -> WalmartCK:
        """创建测试CK"""
        test_ck = WalmartCK(
            sign=f"test_ck_{uuid.uuid4().hex[:8]}",
            total_limit=100,
            bind_count=0,
            active=True,
            merchant_id=1,  # 假设存在merchant_id=1
            department_id=1,  # 假设存在department_id=1
            is_deleted=False
        )
        self.db.add(test_ck)
        self.db.commit()
        self.db.refresh(test_ck)
        return test_ck
    
    async def _create_test_record(self, merchant_id: int) -> CardRecord:
        """创建测试记录"""
        test_record = CardRecord(
            id=str(uuid.uuid4()),
            card_number=f"test_card_{uuid.uuid4().hex[:8]}",
            merchant_id=merchant_id,
            status='binding',
            amount=1000
        )
        self.db.add(test_record)
        self.db.commit()
        self.db.refresh(test_record)
        return test_record
    
    async def _cleanup_test_data(self, record: CardRecord, ck: WalmartCK):
        """清理测试数据"""
        try:
            self.db.delete(record)
            self.db.delete(ck)
            self.db.commit()
        except Exception as e:
            logger.warning(f"清理测试数据失败: {e}")
            self.db.rollback()


async def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description="原子性绑卡服务测试工具")
    parser.add_argument("--verbose", action="store_true", help="详细输出")
    
    args = parser.parse_args()
    
    if args.verbose:
        import logging
        logging.getLogger().setLevel(logging.DEBUG)
    
    tester = AtomicBindingTester()
    
    try:
        result = await tester.run_all_tests()
        
        if result['failed'] == 0:
            logger.info("🎉 所有测试通过！")
            sys.exit(0)
        else:
            logger.error(f"❌ {result['failed']} 个测试失败")
            sys.exit(1)
            
    except Exception as e:
        logger.error(f"测试失败: {e}")
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())
