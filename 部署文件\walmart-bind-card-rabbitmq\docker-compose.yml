# RabbitMQ服务 - 独立部署到消息队列服务器
services:
  rabbitmq:
    container_name: walmart-bind-card-rabbitmq
    image: docker.1ms.run/rabbitmq:3.12-management # 升级到最新稳定版本
    restart: always
    hostname: rabbitmq-server
    # 使用端口映射，确保外部可以访问
    ports:
      - "5672:5672" # AMQP端口
      - "15672:15672" # 管理界面端口
    environment:
      TZ: Asia/Shanghai
      RABBITMQ_DEFAULT_USER: ${RABBITMQ_USER:-walmart_card}
      RABBITMQ_DEFAULT_PASS: ${RABBITMQ_PASSWORD:-7c222fb2927d828af22f592134e8932480637c0d}
      RABBITMQ_DEFAULT_VHOST: ${RABBITMQ_VHOST:-/walmart_card}
      # 集群配置（如果需要）
      RABBITMQ_USE_LONGNAME: true
      RABBITMQ_NODENAME: rabbit@rabbitmq-server
      # 性能配置现在通过配置文件设置，不使用环境变量
    volumes:
      - rabbitmq_data:/var/lib/rabbitmq
      - rabbitmq_logs:/var/log/rabbitmq
      # 配置文件映射已注释，使用默认配置
      # - ./rabbitmq-conf/rabbitmq.conf:/etc/rabbitmq/rabbitmq.conf:ro
      # - ./rabbitmq-conf/definitions.json:/etc/rabbitmq/definitions.json:ro
    healthcheck:
      test: ["CMD", "rabbitmqctl", "status"]
      interval: 10s
      timeout: 5s
      retries: 5
    # 资源限制 - MQ服务器需要足够的CPU和内存
    deploy:
      resources:
        limits:
          cpus: "4.0"
          memory: 8G
        reservations:
          cpus: "2.0"
          memory: 4G
    networks:
      - walmart-network

volumes:
  rabbitmq_data:
  rabbitmq_logs:

networks:
  walmart-network:
    driver: bridge
