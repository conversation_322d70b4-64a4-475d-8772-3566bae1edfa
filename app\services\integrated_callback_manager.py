"""
集成的回调服务管理器
与主应用生命周期绑定，在Docker容器中运行
"""
import asyncio
from typing import Optional, List
from contextlib import asynccontextmanager

from app.core.logging import get_logger
from app.services.optimized_callback_service import optimized_callback_service
from app.services.callback_monitor_service import callback_monitor
from app.services.queue_consumer import start_callback_consumer

logger = get_logger("integrated_callback_manager")


class IntegratedCallbackManager:
    """集成的回调服务管理器"""
    
    def __init__(self):
        self.services: List[asyncio.Task] = []
        self.is_running = False
        self._shutdown_event = asyncio.Event()
    
    async def startup(self):
        """启动所有回调相关服务"""
        if self.is_running:
            logger.warning("回调服务管理器已经在运行中")
            return
        
        logger.info("启动集成回调服务管理器...")
        
        try:
            # 1. 启动回调监控服务
            logger.info("启动回调监控服务...")
            monitor_task = asyncio.create_task(
                self._run_monitor_service(),
                name="callback_monitor"
            )
            self.services.append(monitor_task)
            
            # 2. 启动回调队列消费者
            logger.info("启动回调队列消费者...")
            consumer_task = asyncio.create_task(
                self._run_callback_consumer(),
                name="callback_consumer"
            )
            self.services.append(consumer_task)
            
            self.is_running = True
            logger.info(f"集成回调服务管理器启动成功，共启动{len(self.services)}个服务")
            
        except Exception as e:
            logger.error(f"启动集成回调服务失败: {e}")
            await self.shutdown()
            raise
    
    async def shutdown(self):
        """关闭所有回调相关服务"""
        if not self.is_running:
            return
        
        logger.info("关闭集成回调服务管理器...")
        
        # 设置关闭事件
        self._shutdown_event.set()
        
        # 关闭监控服务
        try:
            await callback_monitor.stop_monitoring()
            logger.info("回调监控服务已关闭")
        except Exception as e:
            logger.error(f"关闭监控服务失败: {e}")
        
        # 关闭优化回调服务的HTTP客户端
        try:
            await optimized_callback_service.close()
            logger.info("回调HTTP客户端已关闭")
        except Exception as e:
            logger.error(f"关闭HTTP客户端失败: {e}")
        
        # 取消所有后台任务
        for task in self.services:
            if not task.done():
                logger.info(f"正在取消服务: {task.get_name()}")
                task.cancel()
                try:
                    await task
                except asyncio.CancelledError:
                    logger.info(f"服务已取消: {task.get_name()}")
                except Exception as e:
                    logger.error(f"取消服务{task.get_name()}时发生异常: {e}")
        
        self.services.clear()
        self.is_running = False
        logger.info("集成回调服务管理器已关闭")
    
    async def _run_monitor_service(self):
        """运行监控服务"""
        try:
            # 启动监控，5分钟间隔
            await callback_monitor.start_monitoring(interval_seconds=300)
        except asyncio.CancelledError:
            logger.info("监控服务被取消")
            raise
        except Exception as e:
            logger.error(f"监控服务运行异常: {e}")
            raise
    
    async def _run_callback_consumer(self):
        """运行回调队列消费者"""
        try:
            await start_callback_consumer()
        except asyncio.CancelledError:
            logger.info("回调队列消费者被取消")
            raise
        except Exception as e:
            logger.error(f"回调队列消费者运行异常: {e}")
            raise
    
    def get_status(self) -> dict:
        """获取服务状态"""
        return {
            "is_running": self.is_running,
            "services_count": len(self.services),
            "services": [
                {
                    "name": task.get_name(),
                    "done": task.done(),
                    "cancelled": task.cancelled() if hasattr(task, 'cancelled') else False
                }
                for task in self.services
            ],
            "monitor_active": callback_monitor.monitoring_active,
        }


# 创建全局实例
integrated_callback_manager = IntegratedCallbackManager()
