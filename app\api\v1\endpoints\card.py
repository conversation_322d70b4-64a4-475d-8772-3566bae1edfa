"""
绑卡记录管理API - 基于CardAPIService的完整实现
"""
from typing import List, Optional, Any, Dict
from fastapi import APIRouter, Depends, HTTPException, Query, status
from sqlalchemy.orm import Session
from datetime import datetime

from app.api import deps
from app.models.user import User
from app.schemas.card_record import (
    CardRecordCreate,
    CardRecordUpdate
)
from app.services.card_api_service import CardAPIService
from app.core.logging import get_logger

router = APIRouter()
logger = get_logger("card_records_api")


@router.get("", response_model=Dict[str, Any])
async def read_card_records(
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_active_user),
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(20, ge=1, le=100, description="每页数量"),
    merchant_id: Optional[int] = Query(None, description="商户ID"),
    department_id: Optional[int] = Query(None, description="部门ID"),
    status_filter: Optional[str] = Query(None, description="状态过滤"),
    # 支持前端status参数（兼容统计接口）
    status: Optional[str] = Query(None, description="状态过滤（前端兼容）"),
    card_number: Optional[str] = Query(None, description="卡号搜索"),
    # 支持前端驼峰命名参数
    cardNumber: Optional[str] = Query(None, description="卡号搜索（驼峰命名兼容）"),
    start_date: Optional[datetime] = Query(None, description="开始日期"),
    end_date: Optional[datetime] = Query(None, description="结束日期"),
    # 支持前端驼峰命名时间参数（使用字符串类型避免空字符串解析错误）
    startTime: Optional[str] = Query(None, description="开始时间（驼峰命名兼容）"),
    endTime: Optional[str] = Query(None, description="结束时间（驼峰命名兼容）"),
):
    """
    获取绑卡记录列表

    权限要求:
    - "card:view": 查看绑卡记录权限
    """
    try:
        # 处理前端驼峰命名和后端下划线命名的兼容性
        final_card_number = cardNumber or card_number
        final_status_filter = status or status_filter

        # 处理时间参数，支持字符串转换为datetime
        def parse_datetime(time_str: Optional[str]) -> Optional[datetime]:
            if not time_str or time_str.strip() == "":
                return None
            try:
                # 尝试解析ISO格式的时间字符串
                return datetime.fromisoformat(time_str.replace('Z', '+00:00'))
            except (ValueError, AttributeError):
                return None

        final_start_date = parse_datetime(startTime) or start_date
        final_end_date = parse_datetime(endTime) or end_date

        card_api_service = CardAPIService(db)
        data = card_api_service.get_card_records(
            current_user=current_user,
            page=page,
            page_size=page_size,
            merchant_id=merchant_id,
            department_id=department_id,
            status_filter=final_status_filter,
            card_number=final_card_number,
            start_date=final_start_date,
            end_date=final_end_date,
        )

        return data
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取绑卡记录列表失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取绑卡记录列表失败: {str(e)}"
        )


@router.post("", response_model=Dict[str, Any])
async def create_card_record(
    *,
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_active_user),
    card_in: CardRecordCreate
):
    """
    创建绑卡记录

    权限要求:
    - "card:create": 创建绑卡记录权限
    """
    try:
        card_api_service = CardAPIService(db)
        data = card_api_service.create_card_record(card_in, current_user)

        return data

    except HTTPException:
        raise
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        logger.error(f"创建绑卡记录失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"创建绑卡记录失败: {str(e)}"
        )


@router.post("/batch", response_model=Dict[str, Any])
async def batch_create_card_records(
    *,
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_active_user),
    batch_data: List[CardRecordCreate]
):
    """
    批量创建绑卡记录

    权限要求:
    - "card:create": 创建绑卡记录权限
    """
    try:
        card_api_service = CardAPIService(db)
        result = card_api_service.batch_create_card_records(batch_data, current_user)

        return result

    except HTTPException:
        raise
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        logger.error(f"批量创建绑卡记录失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"批量创建绑卡记录失败: {str(e)}"
        )


@router.get("/statistics", response_model=Dict[str, Any])
async def get_card_statistics_auto(
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_active_user),
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(20, ge=1, le=100, description="每页数量"),
    card_number: Optional[str] = Query(None, description="卡号搜索"),
    # 支持前端驼峰命名参数
    cardNumber: Optional[str] = Query(None, description="卡号搜索（驼峰命名兼容）"),
    status: Optional[str] = Query(None, description="状态过滤"),
    start_time: Optional[datetime] = Query(None, description="开始时间"),
    end_time: Optional[datetime] = Query(None, description="结束时间"),
    # 支持前端驼峰命名时间参数（使用字符串类型避免空字符串解析错误）
    startTime: Optional[str] = Query(None, description="开始时间（驼峰命名兼容）"),
    endTime: Optional[str] = Query(None, description="结束时间（驼峰命名兼容）"),
    merchant_id: Optional[int] = Query(None, description="商户ID（超级管理员可指定）"),
):
    """
    获取绑卡统计信息（自动权限判断）

    根据用户权限自动确定数据范围：
    - 超级管理员：可查看所有商户统计（可通过merchant_id参数指定特定商户）
    - 商户管理员：只能查看自己商户的统计
    - 商户CK供应商：只能查看自己部门的统计

    权限要求:
    - "card:view": 查看绑卡记录权限
    """
    try:
        # 处理前端驼峰命名和后端下划线命名的兼容性
        final_card_number = cardNumber or card_number

        # 处理时间参数，支持字符串转换为datetime
        def parse_datetime(time_str: Optional[str]) -> Optional[datetime]:
            if not time_str or time_str.strip() == "":
                return None
            try:
                # 尝试解析ISO格式的时间字符串
                return datetime.fromisoformat(time_str.replace('Z', '+00:00'))
            except (ValueError, AttributeError):
                return None

        final_start_time = parse_datetime(startTime) or start_time
        final_end_time = parse_datetime(endTime) or end_time

        card_api_service = CardAPIService(db)
        statistics = card_api_service.get_card_statistics_auto(
            current_user=current_user,
            page=page,
            page_size=page_size,
            card_number=final_card_number,
            status_filter=status,
            start_date=final_start_time,
            end_date=final_end_time,
            merchant_id=merchant_id,
        )

        return statistics

    except HTTPException:
        raise
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        logger.error(f"获取绑卡统计失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取绑卡统计失败: {str(e)}"
        )


@router.get("/statistics/{merchant_id}", response_model=Dict[str, Any])
async def get_card_statistics(
    merchant_id: int,
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_active_user),
    start_date: Optional[datetime] = Query(None, description="开始日期"),
    end_date: Optional[datetime] = Query(None, description="结束日期"),
):
    """
    获取绑卡统计信息

    权限要求:
    - "card:view": 查看绑卡记录权限
    """
    try:
        card_api_service = CardAPIService(db)
        statistics = card_api_service.get_card_statistics(
            merchant_id, current_user, start_date, end_date
        )

        return statistics

    except HTTPException:
        raise
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        logger.error(f"获取绑卡统计失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取绑卡统计失败: {str(e)}"
        )


@router.get("/{card_id}", response_model=Dict[str, Any])
async def read_card_record(
    card_id: str,
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_active_user),
):
    """
    获取绑卡记录详情

    权限要求:
    - "card:view": 查看绑卡记录权限
    """
    try:
        card_api_service = CardAPIService(db)
        data = card_api_service.get_card_record(card_id, current_user)

        return data

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取绑卡记录详情失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取绑卡记录详情失败: {str(e)}"
        )


@router.post("/{card_id}/retry", response_model=Dict[str, Any])
async def retry_card_record(
    card_id: str,
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_active_user),
):
    """
    重试失败或待处理的绑卡记录

    权限要求:
    - "card:retry": 重试绑卡记录权限

    重试规则:
    - 可以重试状态为"failed"或"pending"的记录
    - 对于failed状态：只有CK失效导致的失败才允许重试
    - 对于pending状态：直接允许重试（可能是长时间未处理的记录）
    - 重试时遵循商户CK隔离机制，确保只使用该商户自己的CK
    - 重试操作会记录使用的walmart_ck_id字段，以便进行绑卡成功追踪
    """
    try:
        card_api_service = CardAPIService(db)
        data = await card_api_service.retry_card_record(card_id, current_user)

        return data

    except HTTPException:
        raise
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        logger.error(f"重试绑卡记录失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"重试绑卡记录失败: {str(e)}"
        )


@router.post("/{card_id}/sync-amount", response_model=Dict[str, Any])
async def sync_card_amount(
    card_id: str,
    force_update: bool = False,
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_active_user),
):
    """
    同步卡片真实金额

    权限要求:
    - "card:sync_amount": 同步卡片金额权限

    功能说明:
    - 仅对绑卡成功的记录进行金额同步
    - 如果已有金额信息，默认不更新，除非设置force_update=true
    - 同步过程不影响绑卡成功状态
    """
    try:
        from app.services.card_amount_sync_service import CardAmountSyncService

        # 【安全修复】验证用户对卡记录的访问权限
        from app.services.card_record_service import CardRecordService
        card_service = CardRecordService(db)

        # 检查用户是否有权限访问该卡记录
        card_record = card_service.get_with_isolation(card_id, current_user)
        if not card_record:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="卡记录不存在或无权限访问"
            )

        sync_service = CardAmountSyncService(db)
        result = await sync_service.sync_card_amount(card_id, force_update)

        return result

    except Exception as e:
        logger.error(f"同步卡片金额失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"同步卡片金额失败: {str(e)}"
        )


@router.post("/{card_id}/trigger-callback", response_model=Dict[str, Any])
async def trigger_manual_callback(
    card_id: str,
    force_callback: bool = False,
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_active_user),
):
    """
    手动触发绑卡回调

    权限要求:
    - "card:trigger_callback": 触发回调权限

    功能说明:
    - 仅对绑卡成功的记录触发回调
    - 如果回调已成功，默认不重新触发，除非设置force_callback=true
    - 会将回调任务加入队列异步处理
    """
    try:
        from app.services.manual_callback_service import ManualCallbackService

        callback_service = ManualCallbackService(db)
        result = await callback_service.trigger_manual_callback(card_id, current_user, force_callback)

        return result

    except Exception as e:
        logger.error(f"触发手动回调失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"触发手动回调失败: {str(e)}"
        )


@router.post("/batch/sync-amount", response_model=Dict[str, Any])
async def batch_sync_card_amount(
    card_ids: List[str],
    force_update: bool = False,
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_active_user),
):
    """
    批量同步卡片真实金额

    权限要求:
    - "card:sync_amount": 同步卡片金额权限

    请求体:
    - card_ids: 卡记录ID列表
    - force_update: 是否强制更新
    """
    try:
        from app.services.card_amount_sync_service import CardAmountSyncService

        # 【安全修复】验证用户对所有卡记录的访问权限
        from app.services.card_record_service import CardRecordService
        card_service = CardRecordService(db)

        # 获取用户有权限访问的卡记录
        accessible_cards = card_service._batch_get_cards_with_isolation(card_ids, current_user)
        accessible_card_ids = [card.id for card in accessible_cards]

        # 检查是否有无权限访问的卡记录
        inaccessible_card_ids = set(card_ids) - set(accessible_card_ids)
        if inaccessible_card_ids:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail=f"无权限访问以下卡记录: {', '.join(inaccessible_card_ids)}"
            )

        sync_service = CardAmountSyncService(db)
        result = await sync_service.batch_sync_card_amounts(accessible_card_ids, force_update)

        return result

    except Exception as e:
        logger.error(f"批量同步卡片金额失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"批量同步卡片金额失败: {str(e)}"
        )


@router.post("/batch/trigger-callback", response_model=Dict[str, Any])
async def batch_trigger_manual_callback(
    card_ids: List[str],
    force_callback: bool = False,
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_active_user),
):
    """
    批量手动触发绑卡回调

    权限要求:
    - "card:trigger_callback": 触发回调权限

    请求体:
    - card_ids: 卡记录ID列表
    - force_callback: 是否强制回调
    """
    try:
        from app.services.manual_callback_service import ManualCallbackService

        callback_service = ManualCallbackService(db)
        result = await callback_service.batch_trigger_manual_callback(card_ids, current_user, force_callback)

        return result

    except Exception as e:
        logger.error(f"批量触发手动回调失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"批量触发手动回调失败: {str(e)}"
        )


@router.put("/{card_id}", response_model=Dict[str, Any])
async def update_card_record(
    card_id: str,
    *,
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_active_user),
    card_in: CardRecordUpdate
):
    """
    更新绑卡记录信息

    权限要求:
    - "card:update": 更新绑卡记录权限
    """
    try:
        card_api_service = CardAPIService(db)
        data = card_api_service.update_card_record(card_id, card_in, current_user)

        return data

    except HTTPException:
        raise
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        logger.error(f"更新绑卡记录失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"更新绑卡记录失败: {str(e)}"
        )


@router.post("/{card_id}/bind", response_model=Dict[str, Any])
async def bind_card(
    card_id: str,
    *,
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_active_user),
    walmart_ck_id: int
):
    """
    执行绑卡操作

    权限要求:
    - "card:bind": 绑卡操作权限
    """
    try:
        card_api_service = CardAPIService(db)
        result = await card_api_service.bind_card(card_id, walmart_ck_id, current_user)

        return result

    except HTTPException:
        raise
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        logger.error(f"绑卡操作失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"绑卡操作失败: {str(e)}"
        )


@router.post("/batch-bind", response_model=Dict[str, Any])
async def batch_bind_cards(
    *,
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_active_user),
    card_ids: List[str]
):
    """
    批量绑卡操作

    权限要求:
    - "card:bind": 绑卡操作权限
    """
    try:
        card_api_service = CardAPIService(db)
        result = await card_api_service.batch_bind_cards(card_ids, current_user)

        return result

    except HTTPException:
        raise
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        logger.error(f"批量绑卡失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"批量绑卡失败: {str(e)}"
        )


@router.get("/export")
async def export_card_records(
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_active_user),
    merchant_id: Optional[int] = Query(None, description="商户ID"),
    department_id: Optional[int] = Query(None, description="部门ID"),
    status_filter: Optional[str] = Query(None, description="状态过滤"),
    start_date: Optional[str] = Query(None, description="开始日期"),
    end_date: Optional[str] = Query(None, description="结束日期"),
    format: str = Query("csv", regex="^(csv|excel)$", description="导出格式"),
):
    """
    【安全修复】导出绑卡记录 - 应用严格的商户数据隔离

    权限要求:
    - api:cards:export

    安全措施:
    - 强制商户隔离：非超级管理员只能导出自己商户的数据
    - 数据脱敏：敏感信息在导出时进行脱敏处理
    - 操作审计：记录所有导出操作
    - 限制导出数量：防止大量数据导出影响系统性能
    """
    try:
        # 权限检查 - 使用简化的权限检查
        # TODO: 实现具体的导出权限检查逻辑

        # 【安全修复】强制商户隔离检查
        if not current_user.is_superuser:
            if not current_user.merchant_id:
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail="用户没有商户信息，无法导出数据",
                )

            # 如果指定了merchant_id参数，检查是否匹配用户的商户
            if merchant_id and merchant_id != current_user.merchant_id:
                logger.warning(f"[SECURITY] 用户 {current_user.id} 尝试导出其他商户 {merchant_id} 的数据")
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail="无权导出其他商户的数据",
                )

            # 强制使用用户的商户ID
            merchant_id = current_user.merchant_id

        # 使用CardAPIService获取数据（已有数据隔离）
        card_api_service = CardAPIService(db)

        # 获取导出数据（应用数据隔离）
        export_data = card_api_service.get_card_records(
            current_user=current_user,
            page=1,
            page_size=10000,  # 导出时使用较大的页面大小
            merchant_id=merchant_id,
            department_id=department_id,
            status_filter=status_filter,
            start_date=start_date,
            end_date=end_date,
        )

        # 记录导出操作
        from app.services.audit_service import AuditService
        audit_service = AuditService(db)
        await audit_service.log_operation(
            db=db,
            user_id=current_user.id,
            operation="EXPORT_CARD_RECORDS",
            resource_type="card_record",
            details={
                "merchant_id": merchant_id,
                "department_id": department_id,
                "status_filter": status_filter,
                "format": format,
                "record_count": export_data.get("total", 0)
            }
        )

        # 返回导出数据（前端处理文件生成）
        return {
            "success": True,
            "data": export_data,
            "format": format,
            "export_time": datetime.now().isoformat(),
            "record_count": export_data.get("total", 0)
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"导出绑卡记录失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"导出绑卡记录失败: {str(e)}"
        )