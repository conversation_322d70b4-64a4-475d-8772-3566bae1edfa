package model

import (
	"time"
)

// WalmartCK 沃尔玛CK模型
type WalmartCK struct {
	// 基础字段
	ID        uint      `gorm:"primarykey" json:"id"`
	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`
	// DeletedAt gorm.DeletedAt `gorm:"index" json:"deleted_at,omitempty"` // 注释掉：数据库中使用is_deleted字段而不是deleted_at

	// CK信息
	Sign        string `gorm:"column:sign;type:text;not null;comment:CK签名" json:"sign"`
	MerchantID  uint   `gorm:"column:merchant_id;not null;comment:商户ID" json:"merchant_id"`
	DepartmentID uint  `gorm:"column:department_id;not null;comment:部门ID" json:"department_id"`

	// 状态控制
	Active    bool `gorm:"column:active;default:true;comment:是否激活" json:"active"`
	IsDeleted bool `gorm:"column:is_deleted;default:false;comment:是否删除" json:"is_deleted"`

	// 使用统计
	BindCount      int     `gorm:"column:bind_count;default:0;comment:绑卡次数" json:"bind_count"`
	TotalLimit     int     `gorm:"column:total_limit;default:20;comment:总限制次数" json:"total_limit"`
	LastBindTime   *string `gorm:"column:last_bind_time;type:varchar(64);comment:最后绑卡时间" json:"last_bind_time"`

	// 乐观锁版本号
	Version        int64      `gorm:"column:version;default:0;comment:乐观锁版本号" json:"version"`

	// 兼容字段（保持与Python版本一致）
	DailyUsage  int    `gorm:"column:daily_usage;default:0;comment:今日使用次数" json:"daily_usage"`
	MaxDailyUse int    `gorm:"column:max_daily_use;default:20;comment:每日最大使用次数" json:"max_daily_use"`
	Priority    int    `gorm:"column:priority;default:1;comment:优先级" json:"priority"`
	// Status      string `gorm:"column:status;type:varchar(20);default:active;comment:状态" json:"status"` // 注释掉：数据库中不存在此字段
	Notes       string `gorm:"column:notes;type:text;comment:备注" json:"notes"`

	// 关联关系 - 注释掉避免循环引用问题
	// Department *Department `gorm:"foreignKey:DepartmentID" json:"department,omitempty"`
}

// TableName 指定表名
func (WalmartCK) TableName() string {
	return "walmart_ck"
}