"""
用户验证服务
"""

import secrets
import string
from datetime import datetime, timedelta
from typing import Optional, Dict, Any
from sqlalchemy.orm import Session

from app.models.telegram_user import TelegramUser, VerificationStatus
from app.models.user import User
from app.models.base import local_now
from app.core.logging import get_logger
from ..config import BotConfig
from ..exceptions import InvalidTokenError, UserNotVerifiedError

logger = get_logger(__name__)


class UserVerificationService:
    """用户验证服务"""
    
    def __init__(self, db_session: Session, config: BotConfig):
        self.db = db_session
        self.config = config
        self.logger = logger
    
    async def create_verification_request(
        self,
        telegram_user_id: int,
        telegram_username: Optional[str] = None,
        telegram_first_name: Optional[str] = None,
        telegram_last_name: Optional[str] = None,
        additional_info: Optional[dict] = None
    ) -> str:
        """
        创建用户验证请求

        Args:
            telegram_user_id: Telegram用户ID
            telegram_username: Telegram用户名
            telegram_first_name: Telegram名字
            telegram_last_name: Telegram姓氏
            additional_info: 额外信息（如群组信息、成员角色等）

        Returns:
            str: 验证令牌
        """
        try:
            # 查找或创建Telegram用户记录
            telegram_user = self.db.query(TelegramUser).filter_by(
                telegram_user_id=telegram_user_id
            ).first()
            
            if telegram_user:
                # 更新用户信息
                telegram_user.telegram_username = telegram_username
                telegram_user.telegram_first_name = telegram_first_name
                telegram_user.telegram_last_name = telegram_last_name
                
                # 如果已经验证，返回现有状态
                if telegram_user.verification_status == VerificationStatus.VERIFIED:
                    return "already_verified"
            else:
                # 创建新的Telegram用户记录
                telegram_user = TelegramUser(
                    telegram_user_id=telegram_user_id,
                    telegram_username=telegram_username,
                    telegram_first_name=telegram_first_name,
                    telegram_last_name=telegram_last_name,
                    verification_status=VerificationStatus.PENDING
                )
                self.db.add(telegram_user)
            
            # 生成验证令牌
            verification_token = telegram_user.generate_verification_token()
            
            self.db.commit()
            
            self.logger.info(f"为用户 {telegram_user_id} 创建验证请求")
            return verification_token
            
        except Exception as e:
            self.logger.error(f"创建验证请求失败: {e}")
            self.db.rollback()
            raise
    
    async def verify_user_with_token(
        self,
        verification_token: str,
        system_user_id: int,
        approver_id: int = None
    ) -> bool:
        """
        使用验证令牌验证用户

        Args:
            verification_token: 验证令牌
            system_user_id: 系统用户ID
            approver_id: 审批人ID

        Returns:
            bool: 验证是否成功
            
        Raises:
            InvalidTokenError: 令牌无效或过期
        """
        try:
            self.logger.info(f"开始验证token: {verification_token[:8]}...")

            # 首先查找具有此token的任何用户（不限制状态）
            telegram_user_any_status = self.db.query(TelegramUser).filter_by(
                verification_token=verification_token
            ).first()

            if not telegram_user_any_status:
                self.logger.warning(f"未找到token对应的用户: {verification_token[:8]}...")
                # 查找所有待验证的用户进行调试
                pending_users = self.db.query(TelegramUser).filter_by(
                    verification_status=VerificationStatus.PENDING
                ).all()
                self.logger.info(f"当前有 {len(pending_users)} 个待验证用户")
                for user in pending_users:
                    if user.verification_token:
                        self.logger.info(f"待验证用户 {user.id} token: {user.verification_token[:8]}...")
                raise InvalidTokenError("验证令牌无效：未找到对应的用户记录")

            self.logger.info(f"找到token对应的用户 {telegram_user_any_status.id}，状态: {telegram_user_any_status.verification_status}")

            # 检查用户状态
            if telegram_user_any_status.verification_status != VerificationStatus.PENDING.value:
                raise InvalidTokenError(f"用户状态不正确：当前状态为 {telegram_user_any_status.verification_status}，需要为 {VerificationStatus.PENDING.value}")

            # 直接使用找到的用户，避免重复查询
            telegram_user = telegram_user_any_status

            # 检查令牌是否过期
            if self._is_verification_token_expired(telegram_user):
                self.logger.warning(f"Token已过期，用户 {telegram_user.id} 创建时间: {telegram_user.created_at}")
                telegram_user.expire_verification()
                self.db.commit()
                raise InvalidTokenError("验证令牌已过期，请重新申请")
            
            # 验证系统用户存在
            system_user = self.db.query(User).filter_by(id=system_user_id).first()
            if not system_user:
                raise InvalidTokenError("系统用户不存在")
            
            # 检查是否已有其他Telegram用户绑定到此系统用户
            existing_binding = self.db.query(TelegramUser).filter_by(
                system_user_id=system_user_id,
                verification_status=VerificationStatus.VERIFIED
            ).first()
            
            if existing_binding and existing_binding.id != telegram_user.id:
                raise InvalidTokenError("该系统用户已绑定其他Telegram账户")
            
            # 执行验证
            telegram_user.verify_with_system_user(system_user_id)
            self.db.commit()
            
            self.logger.info(
                f"用户验证成功: Telegram用户 {telegram_user.telegram_user_id} "
                f"绑定到系统用户 {system_user_id}"
            )
            return True
            
        except InvalidTokenError:
            raise
        except Exception as e:
            self.logger.error(f"用户验证失败: {e}")
            self.db.rollback()
            raise InvalidTokenError("验证过程中发生错误")
    
    async def get_user_verification_status(
        self, 
        telegram_user_id: int
    ) -> Dict[str, Any]:
        """
        获取用户验证状态
        
        Args:
            telegram_user_id: Telegram用户ID
            
        Returns:
            Dict: 验证状态信息
        """
        telegram_user = self.db.query(TelegramUser).filter_by(
            telegram_user_id=telegram_user_id
        ).first()
        
        if not telegram_user:
            return {
                "exists": False,
                "verified": False,
                "status": "not_found",
                "message": "用户记录不存在"
            }
        
        status_info = {
            "exists": True,
            "verified": telegram_user.is_verified(),
            "status": telegram_user.verification_status.value,
            "verification_time": telegram_user.verification_time.isoformat() if telegram_user.verification_time else None,
            "system_user_id": telegram_user.system_user_id,
            "system_username": telegram_user.system_user.username if telegram_user.system_user else None
        }
        
        if telegram_user.verification_status == VerificationStatus.PENDING:
            if telegram_user.verification_token:
                if self._is_verification_token_expired(telegram_user):
                    status_info["message"] = "验证令牌已过期，请重新申请"
                else:
                    status_info["message"] = "等待验证确认"
            else:
                status_info["message"] = "请申请验证令牌"
        elif telegram_user.verification_status == VerificationStatus.VERIFIED:
            status_info["message"] = "用户已验证"
        elif telegram_user.verification_status == VerificationStatus.EXPIRED:
            status_info["message"] = "验证已过期，请重新验证"
        
        return status_info
    
    async def revoke_user_verification(self, telegram_user_id: int) -> bool:
        """
        撤销用户验证
        
        Args:
            telegram_user_id: Telegram用户ID
            
        Returns:
            bool: 是否成功撤销
        """
        try:
            telegram_user = self.db.query(TelegramUser).filter_by(
                telegram_user_id=telegram_user_id
            ).first()
            
            if not telegram_user:
                return False
            
            # 重置验证状态
            telegram_user.system_user_id = None
            telegram_user.verification_status = VerificationStatus.PENDING
            telegram_user.verification_time = None
            telegram_user.verification_token = None
            
            self.db.commit()
            
            self.logger.info(f"撤销用户 {telegram_user_id} 的验证")
            return True
            
        except Exception as e:
            self.logger.error(f"撤销用户验证失败: {e}")
            self.db.rollback()
            return False
    
    async def cleanup_expired_verifications(self) -> int:
        """
        清理过期的验证请求
        
        Returns:
            int: 清理的记录数
        """
        try:
            expire_time = local_now() - timedelta(
                minutes=self.config.verification_token_expire_minutes
            )
            
            # 查找过期的待验证记录
            expired_users = self.db.query(TelegramUser).filter(
                TelegramUser.verification_status == VerificationStatus.PENDING,
                TelegramUser.created_at < expire_time
            ).all()
            
            count = 0
            for user in expired_users:
                user.expire_verification()
                count += 1
            
            self.db.commit()
            
            if count > 0:
                self.logger.info(f"清理了 {count} 个过期的验证请求")
            
            return count
            
        except Exception as e:
            self.logger.error(f"清理过期验证请求失败: {e}")
            self.db.rollback()
            return 0
    
    def _is_verification_token_expired(self, telegram_user: TelegramUser) -> bool:
        """检查验证令牌是否过期"""
        if not telegram_user.created_at:
            return True

        # 使用统一的时间处理工具
        from app.utils.time_utils import is_expired

        expire_duration = timedelta(
            minutes=self.config.verification_token_expire_minutes
        )

        return is_expired(telegram_user.created_at, expire_duration)
    
    def _generate_verification_code(self) -> str:
        """生成6位数字验证码"""
        return ''.join(secrets.choice(string.digits) for _ in range(6))
    
    async def get_verification_statistics(self) -> Dict[str, Any]:
        """获取验证统计信息"""
        try:
            total_users = self.db.query(TelegramUser).count()
            verified_users = self.db.query(TelegramUser).filter_by(
                verification_status=VerificationStatus.VERIFIED
            ).count()
            pending_users = self.db.query(TelegramUser).filter_by(
                verification_status=VerificationStatus.PENDING
            ).count()
            expired_users = self.db.query(TelegramUser).filter_by(
                verification_status=VerificationStatus.EXPIRED
            ).count()
            
            return {
                "total_users": total_users,
                "verified_users": verified_users,
                "pending_users": pending_users,
                "expired_users": expired_users,
                "verification_rate": round(
                    (verified_users / total_users) * 100, 1
                ) if total_users > 0 else 0
            }
            
        except Exception as e:
            self.logger.error(f"获取验证统计失败: {e}")
            return {
                "total_users": 0,
                "verified_users": 0,
                "pending_users": 0,
                "expired_users": 0,
                "verification_rate": 0
            }
