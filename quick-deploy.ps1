# 快速部署脚本 - 使用预编译文件
param(
    [switch]$Force = $false
)

Write-Host "=========================================" -ForegroundColor Cyan
Write-Host "🚀 快速部署脚本" -ForegroundColor Cyan
Write-Host "=========================================" -ForegroundColor Cyan
Write-Host ""

# 检查预编译文件
$prebuiltFiles = @(
    "walmart-gateway-secure",
    "walmart-gateway-encrypted", 
    "walmart-gateway-encrypted-linux"
)

$foundFile = $null
foreach ($file in $prebuiltFiles) {
    if (Test-Path $file) {
        $fileInfo = Get-Item $file
        $ageHours = ((Get-Date) - $fileInfo.LastWriteTime).TotalHours
        
        if ($ageHours -lt 24 -or $Force) {
            Write-Host "✅ 发现预编译文件: $file" -ForegroundColor Green
            Write-Host "   大小: $([math]::Round($fileInfo.Length/1MB,1))MB" -ForegroundColor Gray
            Write-Host "   年龄: $([math]::Round($ageHours,1))小时" -ForegroundColor Gray
            $foundFile = $file
            break
        }
    }
}

if ($foundFile) {
    Write-Host ""
    Write-Host "🎯 使用预编译文件快速部署..." -ForegroundColor Yellow
    
    # 确保文件名正确
    if ($foundFile -ne "walmart-gateway-secure") {
        Write-Host "📋 重命名文件: $foundFile → walmart-gateway-secure" -ForegroundColor Cyan
        Copy-Item $foundFile "walmart-gateway-secure" -Force
    }
    
    # 使用预编译版本部署
    Write-Host "🐳 启动Docker容器（预编译版本）..." -ForegroundColor Cyan
    
    try {
        # 构建镜像
        docker-compose -f docker-compose.prebuilt.yml build
        if ($LASTEXITCODE -ne 0) {
            throw "Docker构建失败"
        }
        
        # 启动服务
        docker-compose -f docker-compose.prebuilt.yml up -d
        if ($LASTEXITCODE -ne 0) {
            throw "Docker启动失败"
        }
        
        Write-Host ""
        Write-Host "🎉 快速部署成功！" -ForegroundColor Green
        Write-Host ""
        Write-Host "📋 服务信息:" -ForegroundColor Cyan
        Write-Host "  主服务: http://localhost:21001" -ForegroundColor White
        Write-Host "  监控: http://localhost:9091/metrics" -ForegroundColor White
        Write-Host "  健康检查: http://localhost:21001/health" -ForegroundColor White
        Write-Host ""
        Write-Host "🔧 管理命令:" -ForegroundColor Cyan
        Write-Host "  查看状态: docker-compose -f docker-compose.prebuilt.yml ps" -ForegroundColor White
        Write-Host "  查看日志: docker-compose -f docker-compose.prebuilt.yml logs -f" -ForegroundColor White
        Write-Host "  停止服务: docker-compose -f docker-compose.prebuilt.yml down" -ForegroundColor White
        
    } catch {
        Write-Host "❌ 快速部署失败: $($_.Exception.Message)" -ForegroundColor Red
        Write-Host "💡 建议使用完整构建: docker-compose build && docker-compose up -d" -ForegroundColor Yellow
        exit 1
    }
    
} else {
    Write-Host "❌ 未找到可用的预编译文件" -ForegroundColor Red
    Write-Host ""
    Write-Host "💡 请先执行本地编译:" -ForegroundColor Yellow
    Write-Host "   .\simple-build.ps1" -ForegroundColor Cyan
    Write-Host ""
    Write-Host "💡 或使用完整Docker构建:" -ForegroundColor Yellow
    Write-Host "   docker-compose build && docker-compose up -d" -ForegroundColor Cyan
    exit 1
}
