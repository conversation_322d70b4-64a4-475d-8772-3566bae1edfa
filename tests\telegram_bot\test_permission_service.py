"""
测试Telegram权限验证服务
"""

import pytest
from unittest.mock import Mock, AsyncMock, patch
from datetime import datetime, timedelta
from sqlalchemy.orm import Session

from app.telegram_bot.services.permission_service import TelegramPermissionService
from app.telegram_bot.exceptions import (
    PermissionError,
    GroupNotBoundError,
    UserNotVerifiedError,
    AuthenticationError
)
from app.models.telegram_group import TelegramGroup, BindStatus, ChatType
from app.models.telegram_user import TelegramUser, VerificationStatus
from app.models.user import User
from app.models.merchant import Merchant
from app.models.department import Department
from app.models.base import local_now


@pytest.fixture
def permission_service(db: Session):
    """创建权限验证服务实例"""
    return TelegramPermissionService(db)


@pytest.fixture
def test_merchant(db: Session):
    """创建测试商户"""
    merchant = Merchant(
        name="测试商户",
        code="TEST_MERCHANT",
        status="active"
    )
    db.add(merchant)
    db.commit()
    db.refresh(merchant)
    return merchant


@pytest.fixture
def test_department(db: Session, test_merchant: Merchant):
    """创建测试部门"""
    department = Department(
        name="测试部门",
        merchant_id=test_merchant.id,
        parent_id=None
    )
    db.add(department)
    db.commit()
    db.refresh(department)
    return department


@pytest.fixture
def test_system_user(db: Session, test_merchant: Merchant):
    """创建测试系统用户"""
    user = User(
        username="testuser",
        email="<EMAIL>",
        password_hash="hashed_password",
        is_active=True,
        is_superuser=False
    )
    db.add(user)
    db.commit()
    db.refresh(user)
    return user


@pytest.fixture
def test_telegram_group(db: Session, test_merchant: Merchant, test_department: Department):
    """创建测试Telegram群组"""
    group = TelegramGroup(
        chat_id=-1001234567890,
        chat_title="测试群组",
        chat_type=ChatType.SUPERGROUP,
        merchant_id=test_merchant.id,
        department_id=test_department.id,
        bind_token="test_bind_token_123",
        bind_status=BindStatus.ACTIVE,
        bind_time=local_now()
    )
    db.add(group)
    db.commit()
    db.refresh(group)
    return group


@pytest.fixture
def test_telegram_user(db: Session, test_system_user: User):
    """创建测试Telegram用户"""
    telegram_user = TelegramUser(
        telegram_user_id=123456789,
        telegram_username="testuser",
        telegram_first_name="Test",
        telegram_last_name="User",
        system_user_id=test_system_user.id,
        verification_status=VerificationStatus.VERIFIED,
        verification_time=local_now()
    )
    db.add(telegram_user)
    db.commit()
    db.refresh(telegram_user)
    return telegram_user


class TestTelegramPermissionService:
    """测试TelegramPermissionService"""

    @pytest.mark.asyncio
    async def test_verify_group_permissions_success(
        self, 
        permission_service: TelegramPermissionService,
        test_telegram_group: TelegramGroup
    ):
        """测试群组权限验证成功"""
        result = await permission_service.verify_group_permissions(test_telegram_group.chat_id)
        
        assert result is not None
        assert result.id == test_telegram_group.id
        assert result.chat_id == test_telegram_group.chat_id
        assert result.bind_status == BindStatus.ACTIVE

    @pytest.mark.asyncio
    async def test_verify_group_permissions_not_bound(
        self, 
        permission_service: TelegramPermissionService
    ):
        """测试群组未绑定异常"""
        with pytest.raises(GroupNotBoundError) as exc_info:
            await permission_service.verify_group_permissions(-1001111111111)
        
        assert "群组未绑定" in str(exc_info.value)

    @pytest.mark.asyncio
    async def test_verify_group_permissions_pending_status(
        self, 
        permission_service: TelegramPermissionService,
        test_telegram_group: TelegramGroup,
        db: Session
    ):
        """测试群组待绑定状态异常"""
        # 修改群组状态为待绑定
        test_telegram_group.bind_status = BindStatus.PENDING
        db.commit()
        
        with pytest.raises(GroupNotBoundError) as exc_info:
            await permission_service.verify_group_permissions(test_telegram_group.chat_id)
        
        assert "群组绑定待确认" in str(exc_info.value)

    @pytest.mark.asyncio
    async def test_verify_group_permissions_suspended_status(
        self, 
        permission_service: TelegramPermissionService,
        test_telegram_group: TelegramGroup,
        db: Session
    ):
        """测试群组暂停状态异常"""
        # 修改群组状态为暂停
        test_telegram_group.bind_status = BindStatus.SUSPENDED
        db.commit()
        
        with pytest.raises(GroupNotBoundError) as exc_info:
            await permission_service.verify_group_permissions(test_telegram_group.chat_id)
        
        assert "群组已被暂停使用" in str(exc_info.value)

    @pytest.mark.asyncio
    async def test_verify_user_permissions_success(
        self, 
        permission_service: TelegramPermissionService,
        test_telegram_user: TelegramUser
    ):
        """测试用户权限验证成功"""
        result = await permission_service.verify_user_permissions(test_telegram_user.telegram_user_id)
        
        assert result is not None
        assert result.id == test_telegram_user.id
        assert result.telegram_user_id == test_telegram_user.telegram_user_id
        assert result.verification_status == VerificationStatus.VERIFIED

    @pytest.mark.asyncio
    async def test_verify_user_permissions_not_found(
        self, 
        permission_service: TelegramPermissionService
    ):
        """测试用户不存在异常"""
        with pytest.raises(UserNotVerifiedError) as exc_info:
            await permission_service.verify_user_permissions(999999999)
        
        assert "用户未验证" in str(exc_info.value)

    @pytest.mark.asyncio
    async def test_verify_user_permissions_pending_status(
        self, 
        permission_service: TelegramPermissionService,
        test_telegram_user: TelegramUser,
        db: Session
    ):
        """测试用户待验证状态异常"""
        # 修改用户状态为待验证
        test_telegram_user.verification_status = VerificationStatus.PENDING
        db.commit()
        
        with pytest.raises(UserNotVerifiedError) as exc_info:
            await permission_service.verify_user_permissions(test_telegram_user.telegram_user_id)
        
        assert "用户验证待确认" in str(exc_info.value)

    @pytest.mark.asyncio
    async def test_verify_user_permissions_no_system_user(
        self, 
        permission_service: TelegramPermissionService,
        test_telegram_user: TelegramUser,
        db: Session
    ):
        """测试用户未关联系统账户异常"""
        # 移除系统用户关联
        test_telegram_user.system_user_id = None
        db.commit()
        
        with pytest.raises(AuthenticationError) as exc_info:
            await permission_service.verify_user_permissions(test_telegram_user.telegram_user_id)
        
        assert "用户未关联系统账户" in str(exc_info.value)

    @pytest.mark.asyncio
    async def test_verify_data_permissions_superuser(
        self, 
        permission_service: TelegramPermissionService,
        test_telegram_user: TelegramUser,
        test_telegram_group: TelegramGroup,
        db: Session
    ):
        """测试超级管理员数据权限"""
        # 设置为超级管理员
        test_telegram_user.system_user.is_superuser = True
        db.commit()
        
        result = await permission_service.verify_data_permissions(
            test_telegram_user, 
            test_telegram_group
        )
        
        assert result is True

    @pytest.mark.asyncio
    async def test_verify_data_permissions_no_system_user(
        self, 
        permission_service: TelegramPermissionService,
        test_telegram_user: TelegramUser,
        test_telegram_group: TelegramGroup,
        db: Session
    ):
        """测试用户系统账户不存在异常"""
        # 移除系统用户关联
        test_telegram_user.system_user_id = None
        db.commit()
        
        with pytest.raises(PermissionError) as exc_info:
            await permission_service.verify_data_permissions(
                test_telegram_user, 
                test_telegram_group
            )
        
        assert "用户系统账户不存在" in str(exc_info.value)

    @pytest.mark.asyncio
    async def test_verify_command_permissions_public_command(
        self,
        permission_service: TelegramPermissionService,
        test_telegram_group: TelegramGroup
    ):
        """测试公共命令权限验证"""
        telegram_user, group = await permission_service.verify_command_permissions(
            123456789,
            test_telegram_group.chat_id,
            "/help"
        )

        assert telegram_user is None  # 公共命令不需要用户验证
        assert group is not None
        assert group.id == test_telegram_group.id

    @pytest.mark.asyncio
    async def test_verify_command_permissions_stats_command(
        self,
        permission_service: TelegramPermissionService,
        test_telegram_user: TelegramUser,
        test_telegram_group: TelegramGroup
    ):
        """测试统计命令权限验证"""
        # Mock系统用户权限检查
        with patch.object(test_telegram_user.system_user, 'has_permission', return_value=True):
            with patch.object(test_telegram_user.system_user, 'can_access_merchant_data', return_value=True):
                with patch.object(test_telegram_user.system_user, 'can_access_department_data_new', return_value=True):
                    telegram_user, group = await permission_service.verify_command_permissions(
                        test_telegram_user.telegram_user_id,
                        test_telegram_group.chat_id,
                        "/stats"
                    )

                    assert telegram_user is not None
                    assert telegram_user.id == test_telegram_user.id
                    assert group is not None
                    assert group.id == test_telegram_group.id

    @pytest.mark.asyncio
    async def test_check_user_group_access_success(
        self,
        permission_service: TelegramPermissionService,
        test_telegram_user: TelegramUser,
        test_telegram_group: TelegramGroup
    ):
        """测试用户群组访问权限检查成功"""
        # Mock系统用户权限检查
        with patch.object(test_telegram_user.system_user, 'can_access_merchant_data', return_value=True):
            with patch.object(test_telegram_user.system_user, 'can_access_department_data_new', return_value=True):
                result = await permission_service.check_user_group_access(
                    test_telegram_user.telegram_user_id,
                    test_telegram_group.chat_id
                )

                assert result["has_access"] is True
                assert result["group_bound"] is True
                assert result["user_verified"] is True
                assert result["data_permission"] is True
                assert result["error_message"] is None

    @pytest.mark.asyncio
    async def test_check_user_group_access_group_not_bound(
        self,
        permission_service: TelegramPermissionService,
        test_telegram_user: TelegramUser
    ):
        """测试群组未绑定的访问权限检查"""
        result = await permission_service.check_user_group_access(
            test_telegram_user.telegram_user_id,
            -1001111111111  # 不存在的群组
        )

        assert result["has_access"] is False
        assert result["group_bound"] is False
        assert result["user_verified"] is False
        assert result["data_permission"] is False
        assert "群组未绑定" in result["error_message"]

    @pytest.mark.asyncio
    async def test_get_user_accessible_groups_superuser(
        self,
        permission_service: TelegramPermissionService,
        test_telegram_user: TelegramUser,
        test_telegram_group: TelegramGroup,
        db: Session
    ):
        """测试超级管理员获取可访问群组"""
        # 设置为超级管理员
        test_telegram_user.system_user.is_superuser = True
        db.commit()

        groups = await permission_service.get_user_accessible_groups(
            test_telegram_user.telegram_user_id
        )

        assert len(groups) >= 1
        assert any(group.id == test_telegram_group.id for group in groups)

    @pytest.mark.asyncio
    async def test_get_user_accessible_groups_normal_user(
        self,
        permission_service: TelegramPermissionService,
        test_telegram_user: TelegramUser,
        test_telegram_group: TelegramGroup
    ):
        """测试普通用户获取可访问群组"""
        # Mock系统用户权限检查
        with patch.object(test_telegram_user.system_user, 'get_accessible_merchant_ids', return_value=[test_telegram_group.merchant_id]):
            with patch.object(test_telegram_user.system_user, 'can_access_department_data_new', return_value=True):
                groups = await permission_service.get_user_accessible_groups(
                    test_telegram_user.telegram_user_id
                )

                assert len(groups) >= 1
                assert any(group.id == test_telegram_group.id for group in groups)

    @pytest.mark.asyncio
    async def test_is_group_admin_superuser(
        self,
        permission_service: TelegramPermissionService,
        test_telegram_user: TelegramUser,
        test_telegram_group: TelegramGroup,
        db: Session
    ):
        """测试超级管理员群组管理权限"""
        # 设置为超级管理员
        test_telegram_user.system_user.is_superuser = True
        db.commit()

        is_admin = await permission_service.is_group_admin(
            test_telegram_user.telegram_user_id,
            test_telegram_group.chat_id
        )

        assert is_admin is True

    @pytest.mark.asyncio
    async def test_is_group_admin_with_permission(
        self,
        permission_service: TelegramPermissionService,
        test_telegram_user: TelegramUser,
        test_telegram_group: TelegramGroup
    ):
        """测试有绑定权限的用户群组管理权限"""
        # Mock权限检查
        with patch.object(test_telegram_user.system_user, 'has_permission', return_value=True):
            is_admin = await permission_service.is_group_admin(
                test_telegram_user.telegram_user_id,
                test_telegram_group.chat_id
            )

            assert is_admin is True

    @pytest.mark.asyncio
    async def test_is_group_admin_no_permission(
        self,
        permission_service: TelegramPermissionService,
        test_telegram_user: TelegramUser,
        test_telegram_group: TelegramGroup
    ):
        """测试无权限用户群组管理权限"""
        # Mock权限检查
        with patch.object(test_telegram_user.system_user, 'has_permission', return_value=False):
            is_admin = await permission_service.is_group_admin(
                test_telegram_user.telegram_user_id,
                test_telegram_group.chat_id
            )

            assert is_admin is False

    @pytest.mark.asyncio
    async def test_verify_specific_command_permissions_admin_command(
        self,
        permission_service: TelegramPermissionService,
        test_telegram_user: TelegramUser,
        test_telegram_group: TelegramGroup
    ):
        """测试管理员命令权限验证"""
        # Mock权限检查 - 有权限
        with patch.object(test_telegram_user.system_user, 'has_permission', return_value=True):
            # 不应该抛出异常
            await permission_service._verify_specific_command_permissions(
                test_telegram_user,
                test_telegram_group,
                "/bind"
            )

    @pytest.mark.asyncio
    async def test_verify_specific_command_permissions_admin_command_no_permission(
        self,
        permission_service: TelegramPermissionService,
        test_telegram_user: TelegramUser,
        test_telegram_group: TelegramGroup
    ):
        """测试管理员命令权限验证失败"""
        # Mock权限检查 - 无权限
        with patch.object(test_telegram_user.system_user, 'has_permission', return_value=False):
            with pytest.raises(PermissionError) as exc_info:
                await permission_service._verify_specific_command_permissions(
                    test_telegram_user,
                    test_telegram_group,
                    "/bind"
                )

            assert "您没有权限执行管理员命令" in str(exc_info.value)

    @pytest.mark.asyncio
    async def test_verify_specific_command_permissions_stats_command_no_permission(
        self,
        permission_service: TelegramPermissionService,
        test_telegram_user: TelegramUser,
        test_telegram_group: TelegramGroup
    ):
        """测试统计命令权限验证失败"""
        # Mock权限检查 - 无权限
        with patch.object(test_telegram_user.system_user, 'has_permission', return_value=False):
            with pytest.raises(PermissionError) as exc_info:
                await permission_service._verify_specific_command_permissions(
                    test_telegram_user,
                    test_telegram_group,
                    "/stats"
                )

            assert "您没有权限查询统计数据" in str(exc_info.value)
