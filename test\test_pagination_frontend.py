#!/usr/bin/env python3
"""
前端分页功能测试脚本

使用Playwright测试前端分页功能是否正确工作
"""

import asyncio
import sys
import os
from playwright.async_api import async_playwright

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

class FrontendPaginationTester:
    """前端分页功能测试器"""
    
    def __init__(self):
        self.base_url = "http://localhost:2000"
        self.page = None
        self.browser = None
        self.context = None
        
    async def setup(self):
        """设置测试环境"""
        playwright = await async_playwright().start()
        self.browser = await playwright.chromium.launch(headless=False)
        self.context = await self.browser.new_context()
        self.page = await self.context.new_page()
        
    async def login(self):
        """登录系统"""
        try:
            await self.page.goto(f"{self.base_url}/#/login")
            await self.page.wait_for_load_state('networkidle')
            
            # 填写登录信息
            await self.page.fill('input[placeholder="请输入用户名"]', 'admin')
            await self.page.fill('input[placeholder="请输入密码"]', '7c222fb2927d828af22f592134e8932480637c0d')
            
            # 点击登录按钮
            await self.page.click('button[type="submit"]')
            await self.page.wait_for_load_state('networkidle')
            
            # 等待跳转到首页
            await asyncio.sleep(3)
            
            # 检查是否登录成功
            current_url = self.page.url
            if "login" not in current_url:
                print("✅ 登录成功")
                return True
            else:
                print("❌ 登录失败")
                return False
                
        except Exception as e:
            print(f"❌ 登录异常: {e}")
            return False
    
    async def test_ck_management_pagination(self):
        """测试CK管理页面分页功能"""
        print("\n=== 测试CK管理页面分页功能 ===")
        
        try:
            # 导航到CK管理页面
            await self.page.goto(f"{self.base_url}/#/walmart-ck")
            await self.page.wait_for_load_state('networkidle')
            await asyncio.sleep(3)
            
            # 检查分页组件是否存在
            pagination_selector = '.el-pagination'
            pagination_exists = await self.page.locator(pagination_selector).count() > 0
            
            if not pagination_exists:
                print("  ❌ 没有找到分页组件")
                return False
            
            print("  ✅ 找到分页组件")
            
            # 获取总记录数
            total_text = await self.page.locator('.el-pagination__total').text_content()
            print(f"  📊 {total_text}")
            
            # 测试每页显示条数选择器
            page_size_selector = '.el-pagination__sizes .el-select'
            if await self.page.locator(page_size_selector).count() > 0:
                print("  ✅ 找到每页显示条数选择器")
                
                # 点击选择器
                await self.page.click(page_size_selector)
                await asyncio.sleep(1)
                
                # 选择100条每页
                option_100 = '.el-select-dropdown__item:has-text("100 条/页")'
                if await self.page.locator(option_100).count() > 0:
                    await self.page.click(option_100)
                    await asyncio.sleep(2)
                    
                    # 检查数据是否正确更新
                    table_rows = await self.page.locator('.el-table__body tr').count()
                    print(f"  📊 选择100条/页后，表格显示 {table_rows} 行数据")
                    
                    # 重新获取总记录数信息
                    total_text_after = await self.page.locator('.el-pagination__total').text_content()
                    print(f"  📊 更新后: {total_text_after}")
                    
                    if table_rows > 0:
                        print("  ✅ 每页显示条数功能正常")
                    else:
                        print("  ❌ 每页显示条数功能异常")
                        return False
                else:
                    print("  ⚠️  没有找到100条/页选项")
            else:
                print("  ⚠️  没有找到每页显示条数选择器")
            
            # 测试翻页功能
            page_buttons = self.page.locator('.el-pager .number')
            page_count = await page_buttons.count()
            
            if page_count > 1:
                print(f"  📊 找到 {page_count} 个页码按钮")
                
                # 点击第二页
                second_page_button = page_buttons.nth(1)
                await second_page_button.click()
                await asyncio.sleep(2)
                
                # 检查是否跳转到第二页
                active_page = await self.page.locator('.el-pager .number.is-active').text_content()
                if active_page == "2":
                    print("  ✅ 翻页功能正常")
                else:
                    print(f"  ❌ 翻页功能异常，当前页码: {active_page}")
                    return False
            else:
                print("  ⚠️  数据量不足，无法测试翻页功能")
            
            return True
            
        except Exception as e:
            print(f"  ❌ 测试异常: {e}")
            return False
    
    async def test_card_records_pagination(self):
        """测试绑卡记录页面分页功能"""
        print("\n=== 测试绑卡记录页面分页功能 ===")
        
        try:
            # 导航到绑卡记录页面
            await self.page.goto(f"{self.base_url}/#/cards")
            await self.page.wait_for_load_state('networkidle')
            await asyncio.sleep(3)
            
            # 检查分页组件是否存在
            pagination_selector = '.el-pagination'
            pagination_exists = await self.page.locator(pagination_selector).count() > 0
            
            if not pagination_exists:
                print("  ❌ 没有找到分页组件")
                return False
            
            print("  ✅ 找到分页组件")
            
            # 获取总记录数
            total_text = await self.page.locator('.el-pagination__total').text_content()
            print(f"  📊 {total_text}")
            
            # 测试每页显示条数选择器
            page_size_selector = '.el-pagination__sizes .el-select'
            if await self.page.locator(page_size_selector).count() > 0:
                print("  ✅ 找到每页显示条数选择器")
                
                # 点击选择器
                await self.page.click(page_size_selector)
                await asyncio.sleep(1)
                
                # 选择50条每页
                option_50 = '.el-select-dropdown__item:has-text("50 条/页")'
                if await self.page.locator(option_50).count() > 0:
                    await self.page.click(option_50)
                    await asyncio.sleep(2)
                    
                    # 检查数据是否正确更新
                    table_rows = await self.page.locator('.el-table__body tr').count()
                    print(f"  📊 选择50条/页后，表格显示 {table_rows} 行数据")
                    
                    if table_rows > 0:
                        print("  ✅ 每页显示条数功能正常")
                    else:
                        print("  ❌ 每页显示条数功能异常")
                        return False
                else:
                    print("  ⚠️  没有找到50条/页选项")
            
            return True
            
        except Exception as e:
            print(f"  ❌ 测试异常: {e}")
            return False
    
    async def test_users_pagination(self):
        """测试用户管理页面分页功能"""
        print("\n=== 测试用户管理页面分页功能 ===")
        
        try:
            # 导航到用户管理页面
            await self.page.goto(f"{self.base_url}/#/users")
            await self.page.wait_for_load_state('networkidle')
            await asyncio.sleep(3)
            
            # 检查分页组件是否存在
            pagination_selector = '.el-pagination'
            pagination_exists = await self.page.locator(pagination_selector).count() > 0
            
            if pagination_exists:
                print("  ✅ 找到分页组件")
                
                # 获取总记录数
                total_text = await self.page.locator('.el-pagination__total').text_content()
                print(f"  📊 {total_text}")
                
                return True
            else:
                print("  ⚠️  没有找到分页组件（可能数据量太少）")
                return True
            
        except Exception as e:
            print(f"  ❌ 测试异常: {e}")
            return False
    
    async def cleanup(self):
        """清理测试环境"""
        if self.browser:
            await self.browser.close()
    
    async def run_all_tests(self):
        """运行所有前端分页测试"""
        print("🧪 开始前端分页功能测试")
        print("=" * 60)
        
        try:
            await self.setup()
            
            # 登录
            if not await self.login():
                return
            
            # 运行测试
            tests = [
                ("CK管理", self.test_ck_management_pagination),
                ("绑卡记录", self.test_card_records_pagination),
                ("用户管理", self.test_users_pagination),
            ]
            
            all_success = True
            
            for test_name, test_func in tests:
                try:
                    success = await test_func()
                    if not success:
                        all_success = False
                except Exception as e:
                    print(f"❌ {test_name} 测试异常: {e}")
                    all_success = False
            
            # 打印测试总结
            print("\n" + "=" * 60)
            print("📊 前端分页功能测试总结")
            print("=" * 60)
            
            if all_success:
                print("🎉 所有前端分页功能测试通过！")
            else:
                print("⚠️  部分前端分页功能存在问题")
                
        finally:
            await self.cleanup()

async def main():
    tester = FrontendPaginationTester()
    await tester.run_all_tests()

if __name__ == "__main__":
    asyncio.run(main())
