"""
回调查询处理器 - 处理InlineKeyboardButton点击事件
"""

from telegram import Update
from telegram.ext import ContextTypes
from sqlalchemy.orm import Session

from app.core.logging import get_logger
from ..config import BotConfig
from ..rate_limiter import RateLimiter

logger = get_logger(__name__)


class CallbackQueryHandler:
    """回调查询处理器"""
    
    def __init__(self, db: Session, config: BotConfig, rate_limiter: RateLimiter):
        self.db = db
        self.config = config
        self.rate_limiter = rate_limiter
        self.logger = logger
    
    async def handle_callback_query(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """处理回调查询"""
        try:
            query = update.callback_query
            if not query:
                return
            
            # 确认回调查询
            await query.answer()
            
            callback_data = query.data
            user_id = update.effective_user.id
            chat_id = update.effective_chat.id
            
            self.logger.info(f"处理回调查询: {callback_data}, 用户: {user_id}")
            
            # 根据回调数据路由到相应的处理方法
            if callback_data == "stats_today":
                await self._handle_stats_today(update, context)
            elif callback_data == "stats_week":
                await self._handle_stats_week(update, context)
            elif callback_data == "stats_month":
                await self._handle_stats_month(update, context)
            elif callback_data == "check_status":
                await self._handle_check_status(update, context)
            elif callback_data == "show_help":
                await self._handle_show_help(update, context)
            elif callback_data == "contact_admin":
                await self._handle_contact_admin(update, context)
            elif callback_data == "start_verify":
                await self._handle_start_verify(update, context)
            elif callback_data == "user_settings":
                await self._handle_user_settings(update, context)
            else:
                await self._handle_unknown_callback(update, context)
                
        except Exception as e:
            self.logger.error(f"处理回调查询失败: {e}", exc_info=True)
            try:
                await query.answer("处理请求时发生错误，请稍后重试。", show_alert=True)
            except:
                pass
    
    async def _handle_stats_today(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """处理今日统计查询"""
        try:
            # 导入并调用统计处理器
            from .stats_handler import StatsCommandHandler
            stats_handler = StatsCommandHandler(self.db, self.config, self.rate_limiter)
            
            # 模拟命令消息
            await stats_handler.handle(update, context)
            
        except Exception as e:
            self.logger.error(f"处理今日统计失败: {e}")
            await self._send_error_message(update, "获取今日统计数据失败，请稍后重试。")
    
    async def _handle_stats_week(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """处理本周统计查询"""
        try:
            from .stats_handler import StatsCommandHandler
            stats_handler = StatsCommandHandler(self.db, self.config, self.rate_limiter)
            await stats_handler.handle_week(update, context)
            
        except Exception as e:
            self.logger.error(f"处理本周统计失败: {e}")
            await self._send_error_message(update, "获取本周统计数据失败，请稍后重试。")
    
    async def _handle_stats_month(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """处理本月统计查询"""
        try:
            from .stats_handler import StatsCommandHandler
            stats_handler = StatsCommandHandler(self.db, self.config, self.rate_limiter)
            await stats_handler.handle_month(update, context)
            
        except Exception as e:
            self.logger.error(f"处理本月统计失败: {e}")
            await self._send_error_message(update, "获取本月统计数据失败，请稍后重试。")
    
    async def _handle_check_status(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """处理状态查询"""
        try:
            from .bind_handler import BindCommandHandler
            bind_handler = BindCommandHandler(self.db, self.config, self.rate_limiter)
            await bind_handler.handle_status(update, context)
            
        except Exception as e:
            self.logger.error(f"处理状态查询失败: {e}")
            await self._send_error_message(update, "获取状态信息失败，请稍后重试。")
    
    async def _handle_show_help(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """处理帮助查询"""
        try:
            from .help_handler import HelpCommandHandler
            help_handler = HelpCommandHandler(self.db, self.config, self.rate_limiter)
            await help_handler.execute_command(update, context)

        except Exception as e:
            self.logger.error(f"处理帮助查询失败: {e}")
            await self._send_error_message(update, "获取帮助信息失败，请稍后重试。")
    
    async def _handle_contact_admin(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """处理联系管理员"""
        try:
            from ..services.user_guide_service import UserGuideService
            guide_service = UserGuideService(self.db, self.config)
            
            contact_info = guide_service.get_admin_contact_info()
            
            await context.bot.send_message(
                chat_id=update.effective_chat.id,
                text=contact_info,
                parse_mode='Markdown'
            )
            
        except Exception as e:
            self.logger.error(f"处理联系管理员失败: {e}")
            await self._send_error_message(update, "获取联系信息失败，请稍后重试。")
    
    async def _handle_start_verify(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """处理开始验证"""
        try:
            from .bind_handler import BindCommandHandler
            bind_handler = BindCommandHandler(self.db, self.config, self.rate_limiter)
            await bind_handler.handle_verify(update, context)
            
        except Exception as e:
            self.logger.error(f"处理开始验证失败: {e}")
            await self._send_error_message(update, "开始验证失败，请稍后重试。")
    
    async def _handle_user_settings(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """处理用户设置"""
        try:
            settings_message = """⚙️ **个人设置**

🔧 **可用设置**：
• 通知偏好设置
• 数据显示格式
• 时区设置
• 语言偏好

💡 **提示**：
目前设置功能正在开发中，敬请期待！

如需特殊设置，请联系管理员。"""

            from telegram import InlineKeyboardButton, InlineKeyboardMarkup
            
            keyboard = InlineKeyboardMarkup([
                [
                    InlineKeyboardButton("📊 查看数据", callback_data="stats_today"),
                    InlineKeyboardButton("🔍 查看状态", callback_data="check_status")
                ],
                [
                    InlineKeyboardButton("📞 联系管理员", callback_data="contact_admin")
                ]
            ])
            
            await context.bot.send_message(
                chat_id=update.effective_chat.id,
                text=settings_message,
                parse_mode='Markdown',
                reply_markup=keyboard
            )
            
        except Exception as e:
            self.logger.error(f"处理用户设置失败: {e}")
            await self._send_error_message(update, "获取设置信息失败，请稍后重试。")
    
    async def _handle_unknown_callback(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """处理未知回调"""
        self.logger.warning(f"收到未知回调数据: {update.callback_query.data}")
        await self._send_error_message(update, "未知的操作，请重新选择。")
    
    async def _send_error_message(self, update: Update, message: str):
        """发送错误消息"""
        try:
            await update.callback_query.answer(message, show_alert=True)
        except Exception as e:
            self.logger.error(f"发送错误消息失败: {e}")
