-- ========================================
-- 07-修复system_settings表结构
-- 执行顺序：第7步 - 添加缺失的字段
-- 
-- 功能说明：
-- 1. 添加is_enabled字段（是否启用）
-- 2. 添加is_system字段（是否为系统内置设置）
-- 3. 支持重复执行，不会重复添加字段
-- ========================================

-- 设置连接字符集
SET NAMES utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 设置时区
SET time_zone = '+08:00';

USE `walmart_card_db`;

-- ========================================
-- 1. 检查并添加缺失的字段
-- ========================================

-- 添加is_enabled字段（如果不存在）
SET @sql = (
    SELECT IF(
        COUNT(*) = 0,
        'ALTER TABLE `system_settings` ADD COLUMN `is_enabled` tinyint(1) NOT NULL DEFAULT 1 COMMENT ''是否启用'' AFTER `description`;',
        'SELECT ''is_enabled字段已存在'' as message;'
    )
    FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_SCHEMA = 'walmart_card_db' 
    AND TABLE_NAME = 'system_settings' 
    AND COLUMN_NAME = 'is_enabled'
);

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 添加is_system字段（如果不存在）
SET @sql = (
    SELECT IF(
        COUNT(*) = 0,
        'ALTER TABLE `system_settings` ADD COLUMN `is_system` tinyint(1) NOT NULL DEFAULT 0 COMMENT ''是否为系统内置设置'' AFTER `is_enabled`;',
        'SELECT ''is_system字段已存在'' as message;'
    )
    FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_SCHEMA = 'walmart_card_db' 
    AND TABLE_NAME = 'system_settings' 
    AND COLUMN_NAME = 'is_system'
);

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- ========================================
-- 2. 更新现有数据的默认值
-- ========================================

-- 确保所有现有记录都有正确的默认值
UPDATE `system_settings` 
SET `is_enabled` = 1 
WHERE `is_enabled` IS NULL;

UPDATE `system_settings` 
SET `is_system` = 0 
WHERE `is_system` IS NULL;

-- 将CK过期管理相关配置标记为系统内置设置
UPDATE `system_settings` 
SET `is_system` = 1 
WHERE `key` IN ('ck_expire_minutes', 'ck_expire_check_enabled', 'ck_expire_check_interval');

-- ========================================
-- 3. 添加相关索引
-- ========================================

-- 添加is_enabled字段的索引（如果不存在）
SET @sql = (
    SELECT IF(
        COUNT(*) = 0,
        'ALTER TABLE `system_settings` ADD INDEX `idx_system_settings_is_enabled` (`is_enabled`);',
        'SELECT ''is_enabled索引已存在'' as message;'
    )
    FROM INFORMATION_SCHEMA.STATISTICS 
    WHERE TABLE_SCHEMA = 'walmart_card_db' 
    AND TABLE_NAME = 'system_settings' 
    AND INDEX_NAME = 'idx_system_settings_is_enabled'
);

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 添加is_system字段的索引（如果不存在）
SET @sql = (
    SELECT IF(
        COUNT(*) = 0,
        'ALTER TABLE `system_settings` ADD INDEX `idx_system_settings_is_system` (`is_system`);',
        'SELECT ''is_system索引已存在'' as message;'
    )
    FROM INFORMATION_SCHEMA.STATISTICS 
    WHERE TABLE_SCHEMA = 'walmart_card_db' 
    AND TABLE_NAME = 'system_settings' 
    AND INDEX_NAME = 'idx_system_settings_is_system'
);

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- ========================================
-- 4. 验证表结构
-- ========================================

-- 查询表结构验证
SELECT 
    'system_settings_table_fixed' as `migration_name`,
    'completed' as `status`,
    '系统设置表结构修复完成' as `message`,
    JSON_OBJECT(
        'table_name', 'system_settings',
        'has_is_enabled', (
            SELECT COUNT(*) 
            FROM INFORMATION_SCHEMA.COLUMNS 
            WHERE TABLE_SCHEMA = 'walmart_card_db' 
            AND TABLE_NAME = 'system_settings' 
            AND COLUMN_NAME = 'is_enabled'
        ),
        'has_is_system', (
            SELECT COUNT(*) 
            FROM INFORMATION_SCHEMA.COLUMNS 
            WHERE TABLE_SCHEMA = 'walmart_card_db' 
            AND TABLE_NAME = 'system_settings' 
            AND COLUMN_NAME = 'is_system'
        ),
        'total_settings', (SELECT COUNT(*) FROM `system_settings`),
        'ck_expire_settings', (
            SELECT COUNT(*) 
            FROM `system_settings` 
            WHERE `key` LIKE 'ck_expire_%'
        ),
        'migration_timestamp', NOW(3)
    ) as `details`;

-- ========================================
-- 5. 显示当前表结构
-- ========================================

-- 显示完整的表结构
DESCRIBE `system_settings`;
