from typing import Optional, <PERSON>
from datetime import datetime
from sqlalchemy.orm import Session
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select
from app.models.walmart_server import WalmartServer
from app.schemas.walmart_server import (
    WalmartServerBase,
    WalmartServerUpdate,
)
from app.core.redis import get_redis
from app.utils.session_utils import SessionAdapter, safe_commit, safe_refresh


class WalmartServerService:
    def __init__(self, db: Union[Session, AsyncSession]):
        self.db = db
        self.adapter = SessionAdapter(db)

    async def get_active_config(self) -> Optional[WalmartServer]:
        """获取激活的配置（兼容同步和异步）"""
        if self.adapter.is_async:
            stmt = select(WalmartServer).filter(WalmartServer.is_active == True)
            result = await self.adapter.execute_async(stmt)
            return result.scalar_one_or_none()
        else:
            return (
                self.adapter.query(WalmartServer)
                .filter(WalmartServer.is_active == True)
                .first()
            )

    async def get_api_url(self) -> Optional[str]:
        """
        获取沃尔玛绑卡API地址，优先从Redis缓存中获取

        Returns:
            Optional[str]: API地址
        """
        redis = await get_redis()
        redis_key = "walmart:api_url"

        # 尝试从Redis获取
        api_url = await redis.get(redis_key)

        # 如果Redis中没有，则从数据库获取并缓存
        if not api_url:
            config = await self.get_active_config()

            if config and config.api_url:
                api_url = config.api_url
                # 缓存到Redis，设置过期时间为1天
                await redis.set(redis_key, api_url, ex=86400)

        return api_url

    async def get_referer(self) -> str:
        """
        获取沃尔玛API请求的Referer头（固定值）

        Returns:
            str: 固定的Referer头值
        """
        # 直接返回固定的Referer地址，不再从Redis或数据库获取
        return "https://servicewechat.com/wx81d3e1fe4c2e11b4/168/page-frame.html"

    async def create_server_config(
        self, config_in: WalmartServerBase
    ) -> WalmartServer:
        """
        创建服务器配置

        Args:
            config_in: 服务器配置数据

        Returns:
            WalmartServer: 创建的服务器配置
        """
        db_config = WalmartServer(**config_in.model_dump(exclude_unset=True))
        db_config.created_at = datetime.now()
        db_config.updated_at = datetime.now()

        self.adapter.add(db_config)

        if self.adapter.is_async:
            await self.adapter.commit_async()
            await self.adapter.refresh_async(db_config)
        else:
            self.adapter.commit_sync()
            self.adapter.refresh_sync(db_config)

        # 如果新创建的配置是激活状态，则更新Redis缓存（仅缓存API URL）
        if db_config.is_active and db_config.api_url:
            await self.update_redis_cache(db_config.api_url)

        return db_config

    async def update_server_config(
        self, config_id: int, config_in: WalmartServerUpdate
    ) -> Optional[WalmartServer]:
        """
        更新服务器配置

        Args:
            config_id: 配置ID
            config_in: 更新数据

        Returns:
            Optional[WalmartServer]: 更新后的配置
        """
        if self.adapter.is_async:
            # 异步版本
            stmt = select(WalmartServer).filter(WalmartServer.id == config_id)
            result = await self.adapter.execute_async(stmt)
            db_config = result.scalar_one_or_none()
        else:
            # 同步版本
            db_config = (
                self.adapter.query(WalmartServer)
                .filter(WalmartServer.id == config_id)
                .first()
            )

        if not db_config:
            return None

        update_data = config_in.model_dump(exclude_unset=True)
        update_data["updated_at"] = datetime.now()

        for key, value in update_data.items():
            setattr(db_config, key, value)

        if self.adapter.is_async:
            await self.adapter.commit_async()
            await self.adapter.refresh_async(db_config)
        else:
            self.adapter.commit_sync()
            self.adapter.refresh_sync(db_config)

        # 如果更新的配置是激活状态，则更新Redis缓存（仅缓存API URL）
        if db_config.is_active and db_config.api_url:
            await self.update_redis_cache(db_config.api_url)
        else:
            # 如果设置为非激活状态，可能需要清除缓存并从其他激活配置中获取
            await self.refresh_redis_cache()

        return db_config

    async def update_redis_cache(self, api_url: str) -> None:
        """
        更新Redis中的API配置缓存（仅缓存API URL，不再缓存Referer）

        Args:
            api_url: 要缓存的API URL
        """
        redis = await get_redis()

        # 缓存API URL
        await redis.set("walmart:api_url", api_url, ex=86400)

    async def refresh_redis_cache(self) -> None:
        """
        刷新Redis缓存，从数据库重新加载配置（仅缓存API URL，不再缓存Referer）
        """
        redis = await get_redis()

        # 先删除旧缓存
        await redis.delete("walmart:api_url")
        await redis.delete("walmart:referer")  # 清除旧的Referer缓存

        # 重新从数据库获取并缓存（仅缓存API URL）
        config = await self.get_active_config()

        if config and config.api_url:
            await redis.set("walmart:api_url", config.api_url, ex=86400)
