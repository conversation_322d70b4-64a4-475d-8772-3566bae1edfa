<script setup>
import { ref, reactive, watch } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { useUserStore } from '@/store/modules/user'
import { useMerchantStore } from '@/store/modules/merchant'
import { useSystemStore } from '@/store/modules/system'
import { User, Lock, Key, Loading } from '@element-plus/icons-vue'
import Logo from '@/components/common/Logo.vue'
import { hasMerchantReadPermission } from '@/utils/permission'
import { authApi } from '@/api/modules/auth'

const userStore = useUserStore()
const router = useRouter()
const merchantStore = useMerchantStore()
const systemStore = useSystemStore()

const loginForm = reactive({
    username: '',
    password: '',
    totpCode: ''  // 添加TOTP验证码字段
})

const showTotpInput = ref(false)  // 是否显示TOTP输入框
const isCheckingTotp = ref(false)  // 是否正在检查TOTP状态

const loginRules = {
    username: [
        { required: true, message: '请输入用户名', trigger: 'blur' },
        { min: 3, max: 20, message: '用户名长度应在 3 到 20 个字符之间', trigger: 'blur' }
    ],
    password: [
        { required: true, message: '请输入密码', trigger: 'blur' },
        { min: 6, max: 64, message: '密码长度应在 6 到 64 个字符之间', trigger: 'blur' }
    ],
    totpCode: [
        {
            validator: (rule, value, callback) => {
                if (showTotpInput.value && !value) {
                    callback(new Error('请输入验证码'))
                } else if (showTotpInput.value && value && !/^\d{6}$/.test(value)) {
                    callback(new Error('验证码必须是6位数字'))
                } else {
                    callback()
                }
            },
            trigger: 'blur'
        }
    ]
}

const loginFormRef = ref(null)
const loading = ref(false)

// 监听用户名变化，重置TOTP状态
watch(() => loginForm.username, (newUsername, oldUsername) => {
    // 只有当用户名真正改变时才重置状态
    if (newUsername !== oldUsername && oldUsername !== '') {
        console.log('用户名已更改，重置TOTP状态')
        resetTotpState()
    }
})

// 重置TOTP相关状态
const resetTotpState = () => {
    showTotpInput.value = false
    loginForm.totpCode = ''
    isCheckingTotp.value = false

    // 清除TOTP验证码的验证错误
    if (loginFormRef.value) {
        loginFormRef.value.clearValidate(['totpCode'])
    }
}

// 检查用户TOTP状态
const checkUserTotpStatus = async (username) => {
    if (!username || username.trim() === '') {
        resetTotpState()
        return false
    }

    try {
        isCheckingTotp.value = true
        console.log('检查用户TOTP状态:', username)

        const totpStatus = await authApi.checkTotpStatus(username)
        console.log('TOTP状态检查结果:', totpStatus)

        return totpStatus.totp_enabled || false
    } catch (error) {
        console.error('检查TOTP状态失败:', error)
        return false
    } finally {
        isCheckingTotp.value = false
    }
}

// 处理用户名输入框失焦事件
const handleUsernameBlur = async () => {
    const username = loginForm.username?.trim()
    if (!username) {
        resetTotpState()
        return
    }

    // 检查是否需要显示TOTP输入框
    const totpEnabled = await checkUserTotpStatus(username)
    if (totpEnabled && !showTotpInput.value) {
        showTotpInput.value = true
        console.log('用户已启用TOTP，自动显示验证码输入框')
    } else if (!totpEnabled && showTotpInput.value) {
        resetTotpState()
        console.log('用户未启用TOTP，隐藏验证码输入框')
    }
}

// 根据用户角色和菜单权限获取默认页面
const getDefaultPageForUser = async () => {
    try {
        const userInfo = userStore.userInfo
        if (!userInfo) {
            return '/dashboard'
        }

        // 获取用户菜单
        const userMenus = systemStore.userMenus
        if (!userMenus || userMenus.length === 0) {
            // 如果没有菜单数据，尝试重新获取
            try {
                await systemStore.fetchUserMenus()
            } catch (error) {
                console.warn('获取用户菜单失败，使用默认页面')
                return '/dashboard'
            }
        }

        // 根据角色决定默认页面
        if (userInfo.role === 'ck_supplier') {
            // CK供应商角色直接跳转到CK管理页面
            return '/walmart/user'
        } else if (userInfo.role === 'super_admin') {
            // 超级管理员跳转到仪表盘
            return '/dashboard'
        } else {
            // 其他角色（商户管理员、操作员）根据菜单权限决定
            // 优先级：仪表盘 > 绑卡数据 > 绑卡用户 > 其他
            const menuPaths = ['/dashboard', '/cards', '/walmart']
            for (const path of menuPaths) {
                if (hasMenuAccess(path, systemStore.userMenus)) {
                    return path
                }
            }

            // 如果以上都没有权限，返回第一个有权限的菜单
            const firstAccessibleMenu = findFirstAccessibleMenu(systemStore.userMenus)
            return firstAccessibleMenu || '/dashboard'
        }
    } catch (error) {
        console.error('获取默认页面失败:', error)
        return '/dashboard'
    }
}

// 检查是否有菜单访问权限
const hasMenuAccess = (path, menus) => {
    if (!menus || !Array.isArray(menus)) return false

    for (const menu of menus) {
        if (menu.path === path) {
            return true
        }
        if (menu.children && menu.children.length > 0) {
            if (hasMenuAccess(path, menu.children)) {
                return true
            }
        }
    }
    return false
}

// 查找第一个可访问的菜单
const findFirstAccessibleMenu = (menus) => {
    if (!menus || !Array.isArray(menus)) return null

    for (const menu of menus) {
        if (menu.path && menu.path !== '#') {
            return menu.path
        }
        if (menu.children && menu.children.length > 0) {
            const childPath = findFirstAccessibleMenu(menu.children)
            if (childPath) {
                return childPath
            }
        }
    }
    return null
}

const handleLogin = async () => {
    if (!loginFormRef.value) return

    try {
        // 1. 表单验证
        await loginFormRef.value.validate()
        loading.value = true

        console.log('开始登录流程...')

        // 2. 如果还没有显示TOTP输入框，先检查用户是否启用了TOTP
        if (!showTotpInput.value && loginForm.username && loginForm.password) {
            console.log('检查用户TOTP状态...')
            const totpEnabled = await checkUserTotpStatus(loginForm.username)

            if (totpEnabled) {
                console.log('用户已启用TOTP，显示验证码输入框')
                showTotpInput.value = true
                loading.value = false
                ElMessage.info('请输入双因子认证码')
                return
            } else {
                console.log('用户未启用TOTP，继续登录流程')
                // 确保TOTP状态被重置
                resetTotpState()
            }
        }

        // 3. 执行登录
        const success = await userStore.login(loginForm)
        if (!success) {
            // 检查是否需要双因子认证
            if (success === 'totp_required') {
                showTotpInput.value = true
                ElMessage.warning('请输入双因子认证码')
                return
            }
            ElMessage.error('登录失败，请检查用户名和密码')
            return
        }

        console.log('登录成功，准备跳转...')

        // 3. 基于权限初始化商户数据
        try {
            // 只有超级管理员或具有商户查询权限的用户才初始化商户数据
            const userInfo = userStore.userInfo

            if (hasMerchantReadPermission(userInfo)) {
                console.log('用户具有商户查询权限，初始化商户数据...')
                await merchantStore.initMerchant()
            } else {
                console.log('用户无商户查询权限，跳过商户数据初始化')
            }
        } catch (error) {
            console.warn('初始化商户数据失败:', error)
            // 商户数据初始化失败不影响登录流程
        }

        // 4. 显示成功消息
        ElMessage.success('登录成功')

        // 5. 延迟跳转，确保状态更新完成
        setTimeout(async () => {
            let redirectPath = router.currentRoute.value.query.redirect

            // 如果没有指定重定向路径，根据用户角色决定默认页面
            if (!redirectPath) {
                redirectPath = await getDefaultPageForUser()
            }

            console.log('跳转到:', redirectPath)
            router.replace(redirectPath)
        }, 100)

    } catch (error) {
        console.error('登录过程中发生错误:', error)
        ElMessage.error(error.message || '登录失败，请重试')
    } finally {
        loading.value = false
    }
}
</script>

<template>
    <div class="login-container">
        <div class="login-box">
            <div class="login-header">
                <Logo class="logo" />
                <h1>沃尔玛绑卡系统</h1>
                <p class="subtitle">管理员登录</p>
            </div>

            <el-form ref="loginFormRef" :model="loginForm" :rules="loginRules" class="login-form">
                <el-form-item prop="username">
                    <el-input
                        v-model="loginForm.username"
                        placeholder="请输入用户名"
                        :prefix-icon="User"
                        clearable
                        @keyup.enter="handleLogin"
                        @blur="handleUsernameBlur"
                        @clear="resetTotpState"
                    />
                </el-form-item>

                <el-form-item prop="password">
                    <el-input v-model="loginForm.password" type="password" placeholder="请输入密码" :prefix-icon="Lock"
                        show-password clearable @keyup.enter="handleLogin" />
                </el-form-item>

                <!-- TOTP状态检查提示 -->
                <div v-if="isCheckingTotp" class="totp-checking">
                    <el-icon class="is-loading"><Loading /></el-icon>
                    <span>正在检查双因子认证状态...</span>
                </div>

                <!-- 双因子认证码输入框 -->
                <el-form-item v-if="showTotpInput" prop="totpCode">
                    <el-input
                        v-model="loginForm.totpCode"
                        placeholder="请输入6位验证码"
                        maxlength="6"
                        clearable
                        @keyup.enter="handleLogin"
                    >
                        <template #prefix>
                            <el-icon><Key /></el-icon>
                        </template>
                    </el-input>
                    <div class="totp-help">
                        <small>请输入Google Authenticator中显示的6位验证码</small>
                    </div>
                </el-form-item>

                <el-form-item>
                    <el-button type="primary" :loading="loading" class="login-button" @click="handleLogin">
                        {{ loading ? '登录中...' : '登录' }}
                    </el-button>
                </el-form-item>
            </el-form>

            <div class="login-footer">
                <p>© {{ new Date().getFullYear() }} 沃尔玛绑卡系统. All rights reserved.</p>
            </div>
        </div>
    </div>
</template>

<style lang="scss" scoped>
.login-container {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 100vh;
    background: linear-gradient(135deg, #1e88e5 0%, #1565c0 100%);

    .login-box {
        width: 420px;
        padding: 40px;
        background: rgba(255, 255, 255, 0.95);
        border-radius: 12px;
        box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
        backdrop-filter: blur(10px);

        .login-header {
            text-align: center;
            margin-bottom: 40px;

            .logo {
                width: 80px;
                height: 80px;
                margin-bottom: 16px;
            }

            h1 {
                font-size: 28px;
                color: #2c3e50;
                margin: 0 0 8px;
                font-weight: 600;
            }

            .subtitle {
                font-size: 16px;
                color: #7f8c8d;
                margin: 0;
            }
        }

        .login-form {
            .el-input {
                height: 44px;

                :deep(.el-input__wrapper) {
                    padding: 0 15px;
                    background: #f5f7fa;
                    border: 1px solid #e4e7ed;
                    border-radius: 8px;
                    transition: all 0.3s ease;

                    &:hover,
                    &:focus {
                        border-color: #409eff;
                        background: #fff;
                    }
                }

                :deep(.el-input__inner) {
                    height: 42px;
                    font-size: 15px;
                }
            }

            .totp-checking {
                display: flex;
                align-items: center;
                justify-content: center;
                padding: 10px;
                color: #909399;
                font-size: 14px;

                .el-icon {
                    margin-right: 8px;
                    font-size: 16px;
                }
            }

            .totp-help {
                margin-top: 5px;
                text-align: center;

                small {
                    color: #909399;
                    font-size: 12px;
                }
            }

            .login-button {
                width: 100%;
                height: 44px;
                font-size: 16px;
                font-weight: 500;
                border-radius: 8px;
                background: #1e88e5;
                border: none;

                &:hover {
                    background: #1976d2;
                }

                &:active {
                    background: #1565c0;
                }
            }
        }

        .login-footer {
            text-align: center;
            margin-top: 24px;
            color: #7f8c8d;
            font-size: 14px;
        }
    }
}

// 响应式设计
@media screen and (max-width: 480px) {
    .login-container {
        padding: 20px;

        .login-box {
            width: 100%;
            padding: 30px 20px;
        }
    }
}
</style>