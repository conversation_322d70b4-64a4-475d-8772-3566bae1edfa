<template>
  <el-dialog
    v-model="dialogVisible"
    :title="isEdit ? '编辑部门' : '创建部门'"
    width="600px"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="120px"
      class="department-form"
    >
      <!-- 基本信息 -->
      <div class="form-section">
        <h4>基本信息</h4>

        <el-form-item label="商户" prop="merchantId" v-if="userStore.userInfo?.role === 'super_admin'">
          <el-select
            v-model="formData.merchantId"
            placeholder="选择商户"
            style="width: 100%"
            :disabled="isEdit"
          >
            <el-option
              v-for="merchant in merchants"
              :key="merchant.id"
              :label="merchant.name"
              :value="merchant.id"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="部门名称" prop="name">
          <el-input
            v-model="formData.name"
            placeholder="请输入部门名称"
            maxlength="100"
            show-word-limit
            @blur="checkNameDuplicate"
          />
          <div v-if="nameCheckMessage" :class="nameCheckMessageClass" style="font-size: 12px; margin-top: 4px;">
            {{ nameCheckMessage }}
          </div>
        </el-form-item>

        <el-form-item label="部门代码" prop="code" v-if="isEdit">
          <el-input
            v-model="formData.code"
            placeholder="系统自动生成"
            maxlength="50"
            show-word-limit
            readonly
          />
          <div class="el-form-item__info">部门代码由系统根据部门名称自动生成，创建后不可修改</div>
        </el-form-item>

        <el-form-item label="上级部门" prop="parentId">
          <el-cascader
            v-model="formData.parentId"
            :options="departmentOptions"
            :props="cascaderProps"
            placeholder="选择上级部门（可选）"
            style="width: 100%"
            clearable
            :show-all-levels="false"
            :disabled="isEdit && !!parentDepartment"
          />
        </el-form-item>

        <el-form-item label="部门描述" prop="description">
          <el-input
            v-model="formData.description"
            type="textarea"
            :rows="3"
            placeholder="请输入部门描述"
            maxlength="500"
            show-word-limit
          />
        </el-form-item>

        <el-form-item label="排序号" prop="sortOrder">
          <el-input-number
            v-model="formData.sortOrder"
            :min="0"
            :max="9999"
            placeholder="排序号"
            style="width: 100%"
          />
        </el-form-item>
      </div>

      <!-- 负责人信息 -->
      <div class="form-section">
        <h4>负责人信息</h4>

        <el-form-item label="负责人姓名" prop="managerName">
          <el-input
            v-model="formData.managerName"
            placeholder="请输入负责人姓名"
            maxlength="100"
          />
        </el-form-item>

        <el-form-item label="负责人电话" prop="managerPhone">
          <el-input
            v-model="formData.managerPhone"
            placeholder="请输入负责人电话"
            maxlength="50"
          />
        </el-form-item>

        <el-form-item label="负责人邮箱" prop="managerEmail">
          <el-input
            v-model="formData.managerEmail"
            placeholder="请输入负责人邮箱"
            maxlength="100"
          />
        </el-form-item>
      </div>

      <!-- 业务配置 -->
      <div class="form-section">
        <h4>业务配置</h4>

        <el-form-item label="业务范围" prop="businessScope">
          <el-input
            v-model="formData.businessScope"
            type="textarea"
            :rows="2"
            placeholder="请输入业务范围"
            maxlength="500"
            show-word-limit
          />
        </el-form-item>



        <el-form-item label="备注" prop="remark">
          <el-input
            v-model="formData.remark"
            type="textarea"
            :rows="2"
            placeholder="请输入备注"
            maxlength="500"
            show-word-limit
          />
        </el-form-item>
      </div>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="submitting">
          {{ isEdit ? '更新' : '创建' }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, computed, watch, onMounted, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import { useUserStore } from '@/store/modules/user'
import { departmentApi } from '@/api/modules/department'
import { merchantApi } from '@/api/modules/merchant'

// Props
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  department: {
    type: Object,
    default: null
  },
  parentDepartment: {
    type: Object,
    default: null
  },
  currentMerchantId: {
    type: Number,
    default: null
  }
})

// Emits
const emit = defineEmits(['update:visible', 'success'])

// Store
const userStore = useUserStore()

// 响应式数据
const formRef = ref()
const submitting = ref(false)
const merchants = ref([])
const departmentOptions = ref([])
const nameCheckMessage = ref('')
const nameCheckMessageClass = ref('')

// 表单数据
const formData = reactive({
  merchantId: null,
  name: '',
  code: '',
  description: '',
  parentId: null,
  managerName: '',
  managerPhone: '',
  managerEmail: '',
  businessScope: '',
  sortOrder: 0,
  remark: ''
})

// 异步验证部门名称是否重复
const validateDepartmentName = async (rule, value, callback) => {
  if (!value) {
    return callback()
  }

  try {
    const params = {
      name: value,
      merchant_id: formData.merchantId,
      parent_id: formData.parentId || null
    }

    // 编辑模式下排除自己
    if (isEdit.value && props.department?.id) {
      params.exclude_id = props.department.id
    }

    const result = await departmentApi.checkNameExists(params)

    if (result.exists) {
      callback(new Error(result.message || '同级部门中已存在此名称'))
    } else {
      callback()
    }
  } catch (error) {
    console.warn('检查部门名称重复失败:', error)
    // 网络错误时不阻止提交，只在控制台警告
    callback()
  }
}

// 表单验证规则
const formRules = computed(() => ({
  // 编辑模式下不验证merchantId，因为商户关系不允许修改
  // 创建模式下，只有超级管理员需要验证merchantId
  merchantId: (!isEdit.value && userStore.userInfo?.role === 'super_admin') ? [
    { required: true, message: '请选择商户', trigger: 'change' }
  ] : [],
  name: [
    { required: true, message: '请输入部门名称', trigger: 'blur' },
    { min: 2, max: 100, message: '部门名称长度在 2 到 100 个字符', trigger: 'blur' },
    { validator: validateDepartmentName, trigger: 'blur' }
  ],
  code: isEdit.value ? [
    { required: true, message: '部门代码不能为空', trigger: 'blur' },
    { min: 2, max: 50, message: '部门代码长度在 2 到 50 个字符', trigger: 'blur' },
    { pattern: /^[A-Z0-9_]+$/, message: '部门代码只能包含大写字母、数字和下划线', trigger: 'blur' }
  ] : [], // 创建时不验证code字段，因为是自动生成的
  managerEmail: [
    { type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' }
  ],
  managerPhone: [
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' }
  ]
}))

// 级联选择器配置
const cascaderProps = {
  value: 'id',
  label: 'name',
  children: 'children',
  checkStrictly: true,
  emitPath: false
}

// 计算属性
const dialogVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
})

const isEdit = computed(() => !!props.department)

// 监听器
watch(() => props.visible, (visible) => {
  if (visible) {
    // 先初始化表单为创建模式
    initForm()
    loadMerchants()
    loadDepartmentOptions()

    // 如果是编辑模式，在下一个tick中填充数据
    if (props.department) {
      nextTick(() => {
        fillEditData()
      })
    }
  }
}, { immediate: false })

watch(() => props.department, (department, oldDepartment) => {
  // 只有在对话框可见且department发生实际变化时才处理
  if (props.visible && department !== oldDepartment) {
    if (department) {
      // 编辑模式：填充部门数据
      fillEditData()
    } else {
      // 创建模式：重置表单
      initForm()
    }
  }
}, { immediate: false })

watch(() => props.parentDepartment, (parentDept) => {
  if (parentDept) {
    formData.parentId = parentDept.id
    // 后端返回的字段名是 merchant_id，需要兼容处理
    formData.merchantId = parentDept.merchantId || parentDept.merchant_id
    console.log('设置父部门信息:', {
      parentDept,
      parentId: parentDept.id,
      merchantId: formData.merchantId
    })
  }
})

// 方法
const initForm = () => {
  // 重置表单为创建模式的初始状态
  Object.assign(formData, {
    merchantId: props.currentMerchantId || userStore.userInfo?.merchantId || null,
    name: '',
    code: '',
    description: '',
    parentId: null,
    managerName: '',
    managerPhone: '',
    managerEmail: '',
    businessScope: '',
    sortOrder: 0,
    remark: ''
  })

  // 清除验证和名称检查消息
  if (formRef.value) {
    formRef.value.clearValidate()
  }
  nameCheckMessage.value = ''
  nameCheckMessageClass.value = ''
}

const fillEditData = () => {
  // 填充编辑模式的数据
  if (!props.department) return

  const department = props.department
  Object.assign(formData, {
    // 兼容后端返回的字段名：merchant_id 或 merchantId
    merchantId: department.merchantId || department.merchant_id,
    name: department.name,
    code: department.code,
    description: department.description || '',
    // 兼容后端返回的字段名：parent_id 或 parentId
    parentId: department.parentId || department.parent_id,
    // 兼容后端返回的字段名：manager_name 或 managerName
    managerName: department.managerName || department.manager_name || '',
    managerPhone: department.managerPhone || department.manager_phone || '',
    managerEmail: department.managerEmail || department.manager_email || '',
    businessScope: department.businessScope || department.business_scope || '',
    // 兼容后端返回的字段名：sort_order 或 sortOrder
    sortOrder: department.sortOrder || department.sort_order || 0,
    remark: department.remark || ''
  })

  // 清除验证错误
  if (formRef.value) {
    formRef.value.clearValidate()
  }
}

const loadMerchants = async () => {
  if (userStore.userInfo?.role === 'super_admin') {
    try {
      const response = await merchantApi.getList()
      // 商户API直接返回格式：{total: ..., items: [...]}
      merchants.value = response.items || []
    } catch (error) {
      console.error('加载商户列表失败：', error)
    }
  }
}

const loadDepartmentOptions = async () => {
  try {
    const params = {}

    // 优先使用传入的当前商户ID，其次使用表单中的商户ID
    const merchantId = props.currentMerchantId || formData.merchantId
    if (merchantId) {
      params.merchant_id = merchantId
    }

    const response = await departmentApi.getTree(params)
    console.log('部门树API响应:', response)
    console.log('response类型:', typeof response)
    console.log('response是否为数组:', Array.isArray(response))

    // 由于HTTP拦截器已经返回了response.data，所以response就是实际的数据
    let treeData = []
    if (response) {
      if (Array.isArray(response)) {
        // 直接是数组格式
        console.log('使用数组格式，数据长度:', response.length)
        treeData = response
      } else if (response.items) {
        // 包含items字段的格式
        console.log('使用items格式，数据长度:', response.items.length)
        treeData = response.items
      } else {
        console.log('未知的数据格式:', response)
        // 如果response是对象且有data属性，尝试使用data
        if (response.data) {
          if (Array.isArray(response.data)) {
            treeData = response.data
          } else if (response.data.items) {
            treeData = response.data.items
          }
        }
      }
    }

    console.log('解析后的树形数据:', treeData)
    departmentOptions.value = treeData
  } catch (error) {
    console.error('加载部门选项失败：', error)
  }
}

const checkNameDuplicate = async () => {
  if (!formData.name || !formData.merchantId) {
    nameCheckMessage.value = ''
    return
  }

  try {
    const params = {
      name: formData.name,
      merchant_id: formData.merchantId,
      parent_id: formData.parentId || null
    }

    // 编辑模式下排除自己
    if (isEdit.value && props.department?.id) {
      params.exclude_id = props.department.id
    }

    const result = await departmentApi.checkNameExists(params)

    if (result.exists) {
      nameCheckMessage.value = result.message || '同级部门中已存在此名称'
      nameCheckMessageClass.value = 'color: #f56c6c;' // 红色警告
    } else {
      nameCheckMessage.value = '部门名称可用'
      nameCheckMessageClass.value = 'color: #67c23a;' // 绿色提示
    }
  } catch (error) {
    console.warn('检查部门名称重复失败:', error)
    nameCheckMessage.value = ''
  }
}

const handleSubmit = async () => {
  if (!formRef.value) return

  try {
    await formRef.value.validate()

    submitting.value = true

    const submitData = { ...formData }

    // 处理空值
    Object.keys(submitData).forEach(key => {
      if (submitData[key] === '') {
        submitData[key] = null
      }
    })

    // 转换字段名：前端驼峰命名 -> 后端下划线命名
    const backendData = {
      name: submitData.name,
      description: submitData.description,
      parent_id: submitData.parentId,
      manager_name: submitData.managerName,
      manager_phone: submitData.managerPhone,
      manager_email: submitData.managerEmail,
      business_scope: submitData.businessScope,
      sort_order: submitData.sortOrder,
      remark: submitData.remark
    }

    // 编辑时才发送code字段（创建时由后端自动生成）
    if (isEdit.value) {
      backendData.code = submitData.code
    }

    // 只有超级管理员才需要传递merchant_id
    if (userStore.userInfo?.role === 'super_admin') {
      backendData.merchant_id = submitData.merchantId
    }

    if (isEdit.value) {
      await departmentApi.update(props.department.id, backendData)
      ElMessage.success('部门更新成功')
    } else {
      await departmentApi.create(backendData)
      ElMessage.success('部门创建成功')
    }

    emit('success')
    handleClose()
  } catch (error) {
    ElMessage.error(error.message || '操作失败')
  } finally {
    submitting.value = false
  }
}

const handleClose = () => {
  // 关闭对话框
  dialogVisible.value = false

  // 清理状态，确保下次打开时状态正确
  nextTick(() => {
    // 重置表单数据
    initForm()
  })
}

// 生命周期
onMounted(() => {
  if (userStore.userInfo?.role !== 'super_admin') {
    formData.merchantId = userStore.userInfo?.merchantId
  }
})
</script>

<style scoped>
.department-form {
  max-height: 60vh;
  overflow-y: auto;
}

.form-section {
  margin-bottom: 20px;
  padding-bottom: 20px;
  border-bottom: 1px solid #f0f0f0;
}

.form-section:last-child {
  border-bottom: none;
  margin-bottom: 0;
  padding-bottom: 0;
}

.form-section h4 {
  margin: 0 0 15px 0;
  color: #303133;
  font-size: 14px;
  font-weight: 600;
}

.dialog-footer {
  text-align: right;
}
</style>
