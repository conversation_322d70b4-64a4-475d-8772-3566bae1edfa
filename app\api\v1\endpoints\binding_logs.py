from typing import Any
from fastapi import APIRouter, Depends, HTTPException, Query, Path, status
from sqlalchemy.orm import Session

from app.api import deps
from app.models.user import User
from app.services.binding_log_service import BindingLogService
from app.services.binding_timeline_service import BindingTimelineService
from app.schemas.binding_log import BindingLogPage
from app.schemas.binding_timeline import BindingTimelineResponse, BindingPerformanceAnalysis
from app.crud import card as card_crud
from app.core.permission_checker import require_permission

router = APIRouter()


@router.get("/{card_record_id}", response_model=BindingLogPage)
@require_permission("api:binding-logs:read")
async def get_binding_logs(
    card_record_id: str = Path(..., description="卡记录ID"),
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_active_user),
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(20, ge=1, le=100, description="每页条数"),
) -> Any:
    """
    获取卡记录的绑定日志

    - **card_record_id**: 卡记录ID
    - **page**: 页码，从1开始
    - **page_size**: 每页条数，最大100

    返回:
    - 绑定日志列表
    - 总记录数
    - 当前页码
    - 每页条数
    """
    # 检查卡记录是否存在
    card_record = card_crud.get_card_record(db, card_record_id)
    if not card_record:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="卡记录不存在",
        )

    # 权限检查已通过装饰器完成，这里可以直接进行业务逻辑

    # 计算分页参数
    skip = (page - 1) * page_size

    # 【安全修复】创建BindingLogService实例
    binding_log_service = BindingLogService(db)

    # 获取日志列表
    logs = await binding_log_service.get_logs_by_card_record_id(
        db, card_record_id, skip, page_size
    )

    # 获取总记录数
    total = await binding_log_service.count_logs_by_card_record_id(db, card_record_id)

    return {
        "items": logs,
        "total": total,
        "page": page,
        "pageSize": page_size,
    }


@router.get("/{card_record_id}/timeline", response_model=BindingTimelineResponse)
@require_permission("api:binding-logs:read")
async def get_binding_timeline(
    card_record_id: str = Path(..., description="卡记录ID"),
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_active_user),
) -> Any:
    """
    获取绑卡记录的时间线详情

    显示每个步骤的执行开始时间、结束时间和执行耗时

    - **card_record_id**: 卡记录ID

    返回:
    - 完整的绑卡时间线，包括每个步骤的详细时间信息
    - 总耗时和各步骤耗时
    - 步骤执行状态和错误信息
    """
    # 检查卡记录是否存在
    card_record = card_crud.get_card_record(db, card_record_id)
    if not card_record:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="卡记录不存在",
        )

    try:
        # 创建时间线服务
        timeline_service = BindingTimelineService(db)

        # 获取时间线详情
        timeline = timeline_service.get_binding_timeline(card_record_id)

        return timeline

    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取绑卡时间线失败: {str(e)}"
        )


@router.get("/{card_record_id}/performance", response_model=BindingPerformanceAnalysis)
@require_permission("api:binding-logs:read")
async def get_binding_performance_analysis(
    card_record_id: str = Path(..., description="卡记录ID"),
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_active_user),
) -> Any:
    """
    获取绑卡记录的性能分析

    提供详细的性能指标、瓶颈分析和优化建议

    - **card_record_id**: 卡记录ID

    返回:
    - 完整的性能分析报告
    - 性能瓶颈识别
    - 优化建议
    """
    # 检查卡记录是否存在
    card_record = card_crud.get_card_record(db, card_record_id)
    if not card_record:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="卡记录不存在",
        )

    try:
        # 创建时间线服务
        timeline_service = BindingTimelineService(db)

        # 获取性能分析
        analysis = timeline_service.get_performance_analysis(card_record_id)

        return analysis

    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取性能分析失败: {str(e)}"
        )
