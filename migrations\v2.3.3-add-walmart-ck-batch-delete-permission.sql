-- ========================================
-- 添加沃尔玛CK批量删除权限
-- 描述: 为批量删除CK接口添加权限配置，确保权限控制完整性
-- ========================================

USE `walmart_card_db`;

-- ========================================
-- 1. 记录迁移开始
-- ========================================
INSERT INTO `migration_logs` (`migration_name`, `status`, `message`, `created_at`) VALUES
('walmart_ck_batch_delete_permission_v2.3.3', 'started', '开始添加沃尔玛CK批量删除权限', NOW(3));

-- ========================================
-- 2. 添加批量删除CK权限
-- ========================================

-- 插入批量删除CK权限（如果不存在）
INSERT IGNORE INTO `permissions` (`code`, `name`, `description`, `resource_type`, `resource_path`, `is_enabled`, `sort_order`, `created_at`, `updated_at`) VALUES
('api:walmart-ck:batch-delete', '批量删除沃尔玛CK', '批量删除沃尔玛CK权限，支持一次性删除多个CK', 'api', '/api/v1/walmart-ck/batch-delete', 1, 265, NOW(3), NOW(3));

-- ========================================
-- 3. 为超级管理员分配批量删除权限
-- ========================================

-- 获取超级管理员角色ID
SET @super_admin_role_id = (SELECT id FROM `roles` WHERE code = 'super_admin' LIMIT 1);

-- 为超级管理员分配批量删除权限
INSERT IGNORE INTO `role_permissions` (`role_id`, `permission_id`, `created_at`)
SELECT @super_admin_role_id, p.id, NOW(3)
FROM `permissions` p
WHERE p.code = 'api:walmart-ck:batch-delete' 
AND @super_admin_role_id IS NOT NULL;

-- ========================================
-- 4. 为商户管理员分配批量删除权限
-- ========================================

-- 获取商户管理员角色ID
SET @merchant_admin_role_id = (SELECT id FROM `roles` WHERE code = 'merchant_admin' LIMIT 1);

-- 为商户管理员分配批量删除权限
INSERT IGNORE INTO `role_permissions` (`role_id`, `permission_id`, `created_at`)
SELECT @merchant_admin_role_id, p.id, NOW(3)
FROM `permissions` p
WHERE p.code = 'api:walmart-ck:batch-delete' 
AND @merchant_admin_role_id IS NOT NULL;

-- ========================================
-- 5. 为CK供应商分配批量删除权限
-- ========================================

-- 获取CK供应商角色ID
SET @ck_supplier_role_id = (SELECT id FROM `roles` WHERE code = 'ck_supplier' LIMIT 1);

-- 为CK供应商分配批量删除权限
INSERT IGNORE INTO `role_permissions` (`role_id`, `permission_id`, `created_at`)
SELECT @ck_supplier_role_id, p.id, NOW(3)
FROM `permissions` p
WHERE p.code = 'api:walmart-ck:batch-delete' 
AND @ck_supplier_role_id IS NOT NULL;

-- ========================================
-- 6. 验证权限配置
-- ========================================

-- 检查批量删除权限是否正确插入
SELECT 
    '批量删除CK权限检查' as check_type,
    COUNT(*) as permission_count,
    CASE 
        WHEN COUNT(*) >= 1 THEN '✓ 批量删除CK权限配置完整'
        ELSE '⚠ 批量删除CK权限配置不完整'
    END as status
FROM `permissions` 
WHERE code = 'api:walmart-ck:batch-delete';

-- 检查超级管理员权限分配
SELECT 
    '超级管理员批量删除权限' as check_type,
    COUNT(*) as assigned_permissions,
    CASE 
        WHEN COUNT(*) >= 1 THEN '✓ 超级管理员权限分配完整'
        ELSE '⚠ 超级管理员权限分配不完整'
    END as status
FROM `role_permissions` rp
JOIN `roles` r ON rp.role_id = r.id
JOIN `permissions` p ON rp.permission_id = p.id
WHERE r.code = 'super_admin' 
AND p.code = 'api:walmart-ck:batch-delete';

-- 检查商户管理员权限分配
SELECT 
    '商户管理员批量删除权限' as check_type,
    COUNT(*) as assigned_permissions,
    CASE 
        WHEN COUNT(*) >= 1 THEN '✓ 商户管理员权限分配完整'
        ELSE '⚠ 商户管理员权限分配不完整'
    END as status
FROM `role_permissions` rp
JOIN `roles` r ON rp.role_id = r.id
JOIN `permissions` p ON rp.permission_id = p.id
WHERE r.code = 'merchant_admin' 
AND p.code = 'api:walmart-ck:batch-delete';

-- 检查CK供应商权限分配
SELECT 
    'CK供应商批量删除权限' as check_type,
    COUNT(*) as assigned_permissions,
    CASE 
        WHEN COUNT(*) >= 1 THEN '✓ CK供应商权限分配完整'
        ELSE '⚠ CK供应商权限分配不完整'
    END as status
FROM `role_permissions` rp
JOIN `roles` r ON rp.role_id = r.id
JOIN `permissions` p ON rp.permission_id = p.id
WHERE r.code = 'ck_supplier' 
AND p.code = 'api:walmart-ck:batch-delete';

-- ========================================
-- 7. 显示当前所有沃尔玛CK相关权限
-- ========================================
SELECT 
    '=== 沃尔玛CK权限列表 ===' as summary,
    p.code as permission_code,
    p.name as permission_name,
    p.sort_order as sort_order,
    GROUP_CONCAT(r.name ORDER BY r.name SEPARATOR ', ') as assigned_roles
FROM `permissions` p
LEFT JOIN `role_permissions` rp ON p.id = rp.permission_id
LEFT JOIN `roles` r ON rp.role_id = r.id
WHERE p.code LIKE '%walmart-ck%'
GROUP BY p.id, p.code, p.name, p.sort_order
ORDER BY p.sort_order;

-- ========================================
-- 8. 记录迁移完成
-- ========================================
UPDATE `migration_logs`
SET
    `status` = 'completed',
    `message` = '沃尔玛CK批量删除权限添加完成 - 权限已添加并分配给相应角色',
    `completed_at` = NOW(3),
    `data_summary` = JSON_OBJECT(
        'permission_added', (SELECT COUNT(*) FROM `permissions` WHERE code = 'api:walmart-ck:batch-delete'),
        'super_admin_assigned', (
            SELECT COUNT(*) FROM `role_permissions` rp
            JOIN `roles` r ON rp.role_id = r.id
            JOIN `permissions` p ON rp.permission_id = p.id
            WHERE r.code = 'super_admin' AND p.code = 'api:walmart-ck:batch-delete'
        ),
        'merchant_admin_assigned', (
            SELECT COUNT(*) FROM `role_permissions` rp
            JOIN `roles` r ON rp.role_id = r.id
            JOIN `permissions` p ON rp.permission_id = p.id
            WHERE r.code = 'merchant_admin' AND p.code = 'api:walmart-ck:batch-delete'
        ),
        'ck_supplier_assigned', (
            SELECT COUNT(*) FROM `role_permissions` rp
            JOIN `roles` r ON rp.role_id = r.id
            JOIN `permissions` p ON rp.permission_id = p.id
            WHERE r.code = 'ck_supplier' AND p.code = 'api:walmart-ck:batch-delete'
        ),
        'total_walmart_ck_permissions', (SELECT COUNT(*) FROM `permissions` WHERE code LIKE '%walmart-ck%'),
        'migration_timestamp', NOW(3)
    )
WHERE `migration_name` = 'walmart_ck_batch_delete_permission_v2.3.3';

-- ========================================
-- 9. 显示迁移结果摘要
-- ========================================
SELECT 
    '=== 批量删除CK权限迁移完成 ===' as summary,
    (SELECT COUNT(*) FROM `permissions` WHERE code = 'api:walmart-ck:batch-delete') as permission_created,
    (SELECT COUNT(*) FROM `role_permissions` rp
     JOIN `roles` r ON rp.role_id = r.id
     JOIN `permissions` p ON rp.permission_id = p.id
     WHERE r.code = 'super_admin' AND p.code = 'api:walmart-ck:batch-delete') as super_admin_assigned,
    (SELECT COUNT(*) FROM `role_permissions` rp
     JOIN `roles` r ON rp.role_id = r.id
     JOIN `permissions` p ON rp.permission_id = p.id
     WHERE r.code = 'merchant_admin' AND p.code = 'api:walmart-ck:batch-delete') as merchant_admin_assigned,
    (SELECT COUNT(*) FROM `role_permissions` rp
     JOIN `roles` r ON rp.role_id = r.id
     JOIN `permissions` p ON rp.permission_id = p.id
     WHERE r.code = 'ck_supplier' AND p.code = 'api:walmart-ck:batch-delete') as ck_supplier_assigned,
    (SELECT COUNT(*) FROM `permissions` WHERE code LIKE '%walmart-ck%') as total_walmart_ck_permissions;

-- ========================================
-- 10. 安全提示
-- ========================================
SELECT 
    '=== 安全提示 ===' as notice,
    '批量删除是敏感操作，请确保：' as reminder_1,
    '1. 只有授权用户才能执行批量删除' as reminder_2,
    '2. 删除操作有适当的审计日志' as reminder_3,
    '3. 实施软删除而非硬删除' as reminder_4,
    '4. 定期检查权限分配的合理性' as reminder_5;
