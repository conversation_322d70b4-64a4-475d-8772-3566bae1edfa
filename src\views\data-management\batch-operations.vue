<template>
  <div class="batch-operations">
    <!-- 页面头部 -->
    <div class="page-header">
      <h1 class="page-title">批量数据操作</h1>
      <div class="header-actions">
        <el-button type="primary" :icon="Upload" @click="showImportDialog">
          批量导入
        </el-button>
        <el-button type="success" :icon="Download" @click="showExportDialog">
          批量导出
        </el-button>
      </div>
    </div>

    <!-- 操作类型选择 -->
    <el-card shadow="never" style="margin-bottom: 20px;">
      <template #header>
        <span>操作配置</span>
      </template>
      
      <el-form :model="operationConfig" inline>
        <el-form-item label="数据类型">
          <el-select v-model="operationConfig.dataType" @change="handleDataTypeChange" style="width: 150px;">
            <el-option label="用户数据" value="user" />
            <el-option label="商户数据" value="merchant" />
            <el-option label="角色数据" value="role" />
          </el-select>
        </el-form-item>
        
        <el-form-item label="操作类型">
          <el-select v-model="operationConfig.operationType" @change="handleOperationTypeChange" style="width: 150px;">
            <el-option label="批量导入" value="import" />
            <el-option label="批量更新" value="update" />
            <el-option label="批量删除" value="delete" />
          </el-select>
        </el-form-item>
        
        <el-form-item label="文件格式" v-if="operationConfig.operationType === 'import'">
          <el-select v-model="operationConfig.fileFormat" style="width: 120px;">
            <el-option label="CSV" value="csv" />
            <el-option label="Excel" value="xlsx" />
            <el-option label="JSON" value="json" />
          </el-select>
        </el-form-item>
        
        <el-form-item>
          <el-button type="primary" @click="getTemplate" :icon="DocumentAdd">
            下载模板
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 文件上传区域 -->
    <el-card shadow="never" style="margin-bottom: 20px;" v-if="operationConfig.operationType === 'import'">
      <template #header>
        <span>文件上传</span>
      </template>
      
      <el-upload
        ref="uploadRef"
        class="upload-area"
        drag
        :auto-upload="false"
        :on-change="handleFileChange"
        :accept="getAcceptedFileTypes()"
        :limit="1"
        :on-exceed="handleExceed"
      >
        <el-icon class="el-icon--upload"><upload-filled /></el-icon>
        <div class="el-upload__text">
          将文件拖到此处，或<em>点击上传</em>
        </div>
        <template #tip>
          <div class="el-upload__tip">
            支持 {{ operationConfig.fileFormat.toUpperCase() }} 格式，文件大小不超过 10MB
          </div>
        </template>
      </el-upload>
      
      <div v-if="uploadedFile" class="file-info">
        <div class="file-details">
          <el-icon><Document /></el-icon>
          <span class="file-name">{{ uploadedFile.name }}</span>
          <span class="file-size">({{ formatFileSize(uploadedFile.size) }})</span>
          <el-button type="text" @click="removeFile" :icon="Delete">删除</el-button>
        </div>
        
        <div class="file-actions">
          <el-button type="warning" @click="validateFile" :loading="validating" :icon="Warning">
            验证数据
          </el-button>
          <el-button type="primary" @click="processFile" :loading="processing" :icon="Check" :disabled="!validationPassed">
            开始处理
          </el-button>
        </div>
      </div>
    </el-card>

    <!-- 批量更新配置 -->
    <el-card shadow="never" style="margin-bottom: 20px;" v-if="operationConfig.operationType === 'update'">
      <template #header>
        <span>更新配置</span>
      </template>
      
      <el-form :model="updateConfig" label-width="120px;">
        <el-form-item label="更新字段">
          <el-checkbox-group v-model="updateConfig.fields">
            <el-checkbox v-for="field in getUpdateFields()" :key="field.value" :label="field.value">
              {{ field.label }}
            </el-checkbox>
          </el-checkbox-group>
        </el-form-item>
        
        <el-form-item label="更新数据">
          <el-input
            v-model="updateConfig.jsonData"
            type="textarea"
            :rows="8"
            placeholder='[{"id": 1, "field": "value"}, ...]'
          />
          <div class="json-tools">
            <el-button type="text" @click="formatJson" size="small">格式化JSON</el-button>
            <el-button type="text" @click="validateJson" size="small">验证JSON</el-button>
          </div>
        </el-form-item>
        
        <el-form-item>
          <el-button type="primary" @click="executeBatchUpdate" :loading="processing">
            执行批量更新
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 批量删除配置 -->
    <el-card shadow="never" style="margin-bottom: 20px;" v-if="operationConfig.operationType === 'delete'">
      <template #header>
        <span>删除配置</span>
      </template>
      
      <el-form :model="deleteConfig" label-width="120px;">
        <el-form-item label="删除方式">
          <el-radio-group v-model="deleteConfig.deleteType">
            <el-radio label="soft">软删除（标记为非活跃）</el-radio>
            <el-radio label="hard">硬删除（从数据库删除）</el-radio>
          </el-radio-group>
        </el-form-item>
        
        <el-form-item label="记录ID">
          <el-input
            v-model="deleteConfig.idsText"
            type="textarea"
            :rows="4"
            placeholder="请输入要删除的记录ID，每行一个或用逗号分隔"
          />
        </el-form-item>
        
        <el-form-item>
          <el-button type="danger" @click="executeBatchDelete" :loading="processing">
            执行批量删除
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 验证结果 -->
    <el-card shadow="never" style="margin-bottom: 20px;" v-if="validationResult">
      <template #header>
        <div class="card-header">
          <span>验证结果</span>
          <el-tag :type="validationResult.is_valid ? 'success' : 'danger'">
            {{ validationResult.is_valid ? '验证通过' : '验证失败' }}
          </el-tag>
        </div>
      </template>
      
      <div class="validation-summary">
        <div class="summary-item">
          <span class="label">总记录数：</span>
          <span class="value">{{ validationResult.total_records }}</span>
        </div>
        <div class="summary-item" v-if="validationResult.estimated_time">
          <span class="label">预计耗时：</span>
          <span class="value">{{ validationResult.estimated_time }}</span>
        </div>
      </div>
      
      <!-- 验证错误 -->
      <div v-if="validationResult.validation_errors?.length > 0" class="validation-errors">
        <h4>验证错误：</h4>
        <el-alert
          v-for="(error, index) in validationResult.validation_errors"
          :key="index"
          :title="error.message"
          type="error"
          :closable="false"
          style="margin-bottom: 10px;"
        />
      </div>
      
      <!-- 验证警告 -->
      <div v-if="validationResult.validation_warnings?.length > 0" class="validation-warnings">
        <h4>验证警告：</h4>
        <el-alert
          v-for="(warning, index) in validationResult.validation_warnings"
          :key="index"
          :title="warning.message"
          type="warning"
          :closable="false"
          style="margin-bottom: 10px;"
        />
      </div>
    </el-card>

    <!-- 操作结果 -->
    <el-card shadow="never" v-if="operationResult">
      <template #header>
        <div class="card-header">
          <span>操作结果</span>
          <el-tag :type="getResultType(operationResult)">
            {{ getResultText(operationResult) }}
          </el-tag>
        </div>
      </template>
      
      <div class="operation-summary">
        <div class="summary-stats">
          <div class="stat-item success">
            <div class="stat-number">{{ operationResult.successful_records }}</div>
            <div class="stat-label">成功</div>
          </div>
          <div class="stat-item danger">
            <div class="stat-number">{{ operationResult.failed_records }}</div>
            <div class="stat-label">失败</div>
          </div>
          <div class="stat-item info">
            <div class="stat-number">{{ operationResult.total_records }}</div>
            <div class="stat-label">总计</div>
          </div>
        </div>
        
        <div class="operation-details">
          <div class="detail-item">
            <span class="label">操作ID：</span>
            <span class="value">{{ operationResult.operation_id }}</span>
          </div>
          <div class="detail-item">
            <span class="label">开始时间：</span>
            <span class="value">{{ formatTime(operationResult.started_at) }}</span>
          </div>
          <div class="detail-item" v-if="operationResult.completed_at">
            <span class="label">完成时间：</span>
            <span class="value">{{ formatTime(operationResult.completed_at) }}</span>
          </div>
        </div>
      </div>
      
      <!-- 错误详情 -->
      <div v-if="operationResult.errors?.length > 0" class="operation-errors">
        <el-divider content-position="left">错误详情</el-divider>
        <el-table :data="operationResult.errors" style="width: 100%" max-height="300">
          <el-table-column prop="row_number" label="行号" width="80" />
          <el-table-column prop="field" label="字段" width="120" />
          <el-table-column prop="value" label="值" width="150" show-overflow-tooltip />
          <el-table-column prop="error_message" label="错误信息" min-width="200" />
          <el-table-column prop="error_type" label="错误类型" width="120">
            <template #default="{ row }">
              <el-tag size="small" :type="getErrorTypeColor(row.error_type)">{{ row.error_type }}</el-tag>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </el-card>

    <!-- 导入对话框 -->
    <el-dialog v-model="importDialogVisible" title="批量导入" width="600px">
      <div class="import-dialog-content">
        <p>请按照以下步骤进行批量导入：</p>
        <ol>
          <li>选择数据类型和文件格式</li>
          <li>下载对应的数据模板</li>
          <li>填写数据并上传文件</li>
          <li>验证数据格式</li>
          <li>执行批量导入</li>
        </ol>
      </div>
      <template #footer>
        <el-button @click="importDialogVisible = false">关闭</el-button>
      </template>
    </el-dialog>

    <!-- 导出对话框 -->
    <el-dialog v-model="exportDialogVisible" title="批量导出" width="600px">
      <el-form :model="exportConfig" label-width="100px;">
        <el-form-item label="数据类型">
          <el-select v-model="exportConfig.dataType" style="width: 200px;">
            <el-option label="用户数据" value="user" />
            <el-option label="商户数据" value="merchant" />
            <el-option label="角色数据" value="role" />
          </el-select>
        </el-form-item>
        
        <el-form-item label="导出格式">
          <el-select v-model="exportConfig.format" style="width: 200px;">
            <el-option label="CSV" value="csv" />
            <el-option label="Excel" value="xlsx" />
            <el-option label="JSON" value="json" />
          </el-select>
        </el-form-item>
        
        <el-form-item label="导出字段">
          <el-checkbox-group v-model="exportConfig.fields">
            <el-checkbox v-for="field in getExportFields()" :key="field.value" :label="field.value">
              {{ field.label }}
            </el-checkbox>
          </el-checkbox-group>
        </el-form-item>
      </el-form>
      
      <template #footer>
        <el-button @click="exportDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="executeExport" :loading="exporting">导出数据</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { 
  Upload, Download, DocumentAdd, Warning, Check, Delete, Document, UploadFilled 
} from '@element-plus/icons-vue'

// 响应式数据
const validating = ref(false)
const processing = ref(false)
const exporting = ref(false)
const importDialogVisible = ref(false)
const exportDialogVisible = ref(false)
const uploadedFile = ref(null)
const validationResult = ref(null)
const operationResult = ref(null)
const validationPassed = ref(false)

// 配置数据
const operationConfig = reactive({
  dataType: 'user',
  operationType: 'import',
  fileFormat: 'csv'
})

const updateConfig = reactive({
  fields: [],
  jsonData: ''
})

const deleteConfig = reactive({
  deleteType: 'soft',
  idsText: ''
})

const exportConfig = reactive({
  dataType: 'user',
  format: 'csv',
  fields: []
})

// 工具方法
const formatFileSize = (bytes) => {
  if (bytes === 0) return '0 Bytes'
  const k = 1024
  const sizes = ['Bytes', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

const formatTime = (timestamp) => {
  return new Date(timestamp).toLocaleString()
}

const getAcceptedFileTypes = () => {
  const types = {
    'csv': '.csv',
    'xlsx': '.xlsx,.xls',
    'json': '.json'
  }
  return types[operationConfig.fileFormat] || '.csv'
}

const getResultType = (result) => {
  if (result.failed_records === 0) return 'success'
  if (result.successful_records === 0) return 'danger'
  return 'warning'
}

const getResultText = (result) => {
  if (result.failed_records === 0) return '全部成功'
  if (result.successful_records === 0) return '全部失败'
  return '部分成功'
}

const getErrorTypeColor = (errorType) => {
  const colors = {
    'required_field': 'danger',
    'format_error': 'warning',
    'duplicate_value': 'danger',
    'not_found': 'info',
    'system_error': 'danger'
  }
  return colors[errorType] || 'info'
}

// 字段配置方法
const getUpdateFields = () => {
  const fieldMaps = {
    'user': [
      { label: '真实姓名', value: 'full_name' },
      { label: '手机号', value: 'phone' },
      { label: '部门', value: 'department' },
      { label: '状态', value: 'is_active' }
    ],
    'merchant': [
      { label: '商户名称', value: 'name' },
      { label: '联系人', value: 'contact_person' },
      { label: '联系电话', value: 'contact_phone' },
      { label: '状态', value: 'is_active' }
    ],
    'role': [
      { label: '角色名称', value: 'name' },
      { label: '角色描述', value: 'description' },
      { label: '状态', value: 'status' }
    ]
  }
  return fieldMaps[operationConfig.dataType] || []
}

const getExportFields = () => {
  const fieldMaps = {
    'user': [
      { label: '用户ID', value: 'id' },
      { label: '用户名', value: 'username' },
      { label: '邮箱', value: 'email' },
      { label: '真实姓名', value: 'full_name' },
      { label: '手机号', value: 'phone' },
      { label: '部门', value: 'department' },
      { label: '状态', value: 'is_active' },
      { label: '创建时间', value: 'created_at' }
    ],
    'merchant': [
      { label: '商户ID', value: 'id' },
      { label: '商户名称', value: 'name' },
      { label: '商户编码', value: 'code' },
      { label: '联系人', value: 'contact_person' },
      { label: '联系电话', value: 'contact_phone' },
      { label: '状态', value: 'is_active' },
      { label: '创建时间', value: 'created_at' }
    ],
    'role': [
      { label: '角色ID', value: 'id' },
      { label: '角色名称', value: 'name' },
      { label: '角色编码', value: 'code' },
      { label: '角色描述', value: 'description' },
      { label: '状态', value: 'status' },
      { label: '创建时间', value: 'created_at' }
    ]
  }
  return fieldMaps[operationConfig.dataType] || []
}

// 事件处理方法
const handleDataTypeChange = () => {
  // 重置相关配置
  updateConfig.fields = []
  exportConfig.fields = []
  validationResult.value = null
  operationResult.value = null
}

const handleOperationTypeChange = () => {
  // 重置状态
  validationResult.value = null
  operationResult.value = null
  uploadedFile.value = null
  validationPassed.value = false
}

const handleFileChange = (file) => {
  uploadedFile.value = file
  validationResult.value = null
  operationResult.value = null
  validationPassed.value = false
}

const handleExceed = () => {
  ElMessage.warning('只能上传一个文件')
}

const removeFile = () => {
  uploadedFile.value = null
  validationResult.value = null
  operationResult.value = null
  validationPassed.value = false
}

// 核心功能方法
const getTemplate = async () => {
  try {
    // 模拟获取模板
    const templateData = await new Promise(resolve => {
      setTimeout(() => {
        const templates = {
          'user_import': [
            { username: 'example_user', email: '<EMAIL>', full_name: '示例用户', phone: '13800138000', department: 'IT部门', is_active: true }
          ],
          'user_update': [
            { id: 1, full_name: '更新姓名', department: '新部门', is_active: true }
          ],
          'merchant_import': [
            { name: '示例商户', code: 'MERCHANT001', contact_person: '联系人', contact_phone: '13900139000', is_active: true }
          ]
        }

        const key = `${operationConfig.dataType}_${operationConfig.operationType}`
        resolve(templates[key] || [])
      }, 500)
    })

    // 生成CSV内容
    if (templateData.length > 0) {
      const headers = Object.keys(templateData[0])
      const csvContent = [
        headers.join(','),
        ...templateData.map(row => headers.map(header => row[header]).join(','))
      ].join('\n')

      // 下载文件
      const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' })
      const link = document.createElement('a')
      link.href = URL.createObjectURL(blob)
      link.download = `${operationConfig.dataType}_${operationConfig.operationType}_template.csv`
      link.click()
      URL.revokeObjectURL(link.href)

      ElMessage.success('模板下载成功')
    } else {
      ElMessage.warning('暂无可用模板')
    }
  } catch (error) {
    console.error('获取模板失败:', error)
    ElMessage.error('获取模板失败')
  }
}

const validateFile = async () => {
  if (!uploadedFile.value) {
    ElMessage.warning('请先上传文件')
    return
  }

  try {
    validating.value = true

    // 模拟文件验证
    const result = await new Promise(resolve => {
      setTimeout(() => {
        resolve({
          is_valid: Math.random() > 0.3, // 70%概率验证通过
          total_records: Math.floor(Math.random() * 100) + 10,
          validation_errors: Math.random() > 0.7 ? [] : [
            { type: 'format_error', message: '第3行邮箱格式不正确' },
            { type: 'duplicate_value', message: '第5行用户名重复' }
          ],
          validation_warnings: [
            { type: 'format_warning', message: '第2行手机号格式可能不正确' }
          ],
          estimated_time: '约 30 秒'
        })
      }, 2000)
    })

    validationResult.value = result
    validationPassed.value = result.is_valid

    if (result.is_valid) {
      ElMessage.success('文件验证通过')
    } else {
      ElMessage.error('文件验证失败，请检查错误信息')
    }
  } catch (error) {
    console.error('文件验证失败:', error)
    ElMessage.error('文件验证失败')
  } finally {
    validating.value = false
  }
}

const processFile = async () => {
  if (!validationPassed.value) {
    ElMessage.warning('请先通过文件验证')
    return
  }

  try {
    processing.value = true

    // 模拟文件处理
    const result = await new Promise(resolve => {
      setTimeout(() => {
        const totalRecords = validationResult.value.total_records
        const successfulRecords = Math.floor(totalRecords * (0.8 + Math.random() * 0.2))
        const failedRecords = totalRecords - successfulRecords

        resolve({
          total_records: totalRecords,
          successful_records: successfulRecords,
          failed_records: failedRecords,
          errors: failedRecords > 0 ? [
            { row_number: 3, field: 'email', value: 'invalid-email', error_message: '邮箱格式不正确', error_type: 'format_error' },
            { row_number: 5, field: 'username', value: 'duplicate_user', error_message: '用户名已存在', error_type: 'duplicate_value' }
          ] : [],
          warnings: [],
          operation_id: `OP_${Date.now()}`,
          started_at: new Date().toISOString(),
          completed_at: new Date().toISOString()
        })
      }, 3000)
    })

    operationResult.value = result

    if (result.failed_records === 0) {
      ElMessage.success('批量操作完成，全部成功')
    } else if (result.successful_records === 0) {
      ElMessage.error('批量操作完成，全部失败')
    } else {
      ElMessage.warning('批量操作完成，部分成功')
    }
  } catch (error) {
    console.error('文件处理失败:', error)
    ElMessage.error('文件处理失败')
  } finally {
    processing.value = false
  }
}

const formatJson = () => {
  try {
    const parsed = JSON.parse(updateConfig.jsonData)
    updateConfig.jsonData = JSON.stringify(parsed, null, 2)
    ElMessage.success('JSON格式化成功')
  } catch (error) {
    ElMessage.error('JSON格式错误')
  }
}

const validateJson = () => {
  try {
    JSON.parse(updateConfig.jsonData)
    ElMessage.success('JSON格式正确')
  } catch (error) {
    ElMessage.error('JSON格式错误：' + error.message)
  }
}

const executeBatchUpdate = async () => {
  if (!updateConfig.jsonData.trim()) {
    ElMessage.warning('请输入更新数据')
    return
  }

  if (updateConfig.fields.length === 0) {
    ElMessage.warning('请选择要更新的字段')
    return
  }

  try {
    processing.value = true

    // 验证JSON格式
    const updateData = JSON.parse(updateConfig.jsonData)

    // 模拟批量更新
    const result = await new Promise(resolve => {
      setTimeout(() => {
        const totalRecords = updateData.length
        const successfulRecords = Math.floor(totalRecords * 0.9)
        const failedRecords = totalRecords - successfulRecords

        resolve({
          total_records: totalRecords,
          successful_records: successfulRecords,
          failed_records: failedRecords,
          errors: failedRecords > 0 ? [
            { row_number: 2, field: 'id', value: 999, error_message: '记录不存在', error_type: 'not_found' }
          ] : [],
          warnings: [],
          operation_id: `UPDATE_${Date.now()}`,
          started_at: new Date().toISOString(),
          completed_at: new Date().toISOString()
        })
      }, 2000)
    })

    operationResult.value = result
    ElMessage.success('批量更新完成')
  } catch (error) {
    ElMessage.error('批量更新失败：' + error.message)
  } finally {
    processing.value = false
  }
}

const executeBatchDelete = async () => {
  if (!deleteConfig.idsText.trim()) {
    ElMessage.warning('请输入要删除的记录ID')
    return
  }

  try {
    await ElMessageBox.confirm(
      `确定要执行批量${deleteConfig.deleteType === 'soft' ? '软' : '硬'}删除操作吗？`,
      '确认操作',
      { type: 'warning' }
    )

    processing.value = true

    // 解析ID列表
    const ids = deleteConfig.idsText
      .split(/[,\n]/)
      .map(id => id.trim())
      .filter(id => id)
      .map(id => parseInt(id))
      .filter(id => !isNaN(id))

    // 模拟批量删除
    const result = await new Promise(resolve => {
      setTimeout(() => {
        const totalRecords = ids.length
        const successfulRecords = Math.floor(totalRecords * 0.95)
        const failedRecords = totalRecords - successfulRecords

        resolve({
          total_records: totalRecords,
          successful_records: successfulRecords,
          failed_records: failedRecords,
          errors: failedRecords > 0 ? [
            { record_id: 999, error: '记录不存在', type: 'not_found' }
          ] : [],
          warnings: [],
          operation_id: `DELETE_${Date.now()}`,
          started_at: new Date().toISOString(),
          completed_at: new Date().toISOString()
        })
      }, 1500)
    })

    operationResult.value = result
    ElMessage.success('批量删除完成')
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('批量删除失败：' + error.message)
    }
  } finally {
    processing.value = false
  }
}

const showImportDialog = () => {
  importDialogVisible.value = true
}

const showExportDialog = () => {
  exportDialogVisible.value = true
  // 默认选择所有字段
  exportConfig.fields = getExportFields().map(field => field.value)
}

const executeExport = async () => {
  if (exportConfig.fields.length === 0) {
    ElMessage.warning('请选择要导出的字段')
    return
  }

  try {
    exporting.value = true

    // 模拟数据导出
    await new Promise(resolve => setTimeout(resolve, 2000))

    // 生成模拟数据并下载
    const mockData = Array.from({ length: 50 }, (_, i) => {
      const record = {}
      exportConfig.fields.forEach(field => {
        if (field === 'id') record[field] = i + 1
        else if (field === 'username') record[field] = `user${i + 1}`
        else if (field === 'email') record[field] = `user${i + 1}@example.com`
        else if (field === 'created_at') record[field] = new Date().toISOString()
        else record[field] = `${field}_value_${i + 1}`
      })
      return record
    })

    // 根据格式导出
    let content, mimeType, filename
    if (exportConfig.format === 'csv') {
      const headers = exportConfig.fields
      content = [
        headers.join(','),
        ...mockData.map(row => headers.map(header => row[header]).join(','))
      ].join('\n')
      mimeType = 'text/csv;charset=utf-8;'
      filename = `${exportConfig.dataType}_export.csv`
    } else if (exportConfig.format === 'json') {
      content = JSON.stringify(mockData, null, 2)
      mimeType = 'application/json;charset=utf-8;'
      filename = `${exportConfig.dataType}_export.json`
    }

    // 下载文件
    const blob = new Blob([content], { type: mimeType })
    const link = document.createElement('a')
    link.href = URL.createObjectURL(blob)
    link.download = filename
    link.click()
    URL.revokeObjectURL(link.href)

    exportDialogVisible.value = false
    ElMessage.success('数据导出成功')
  } catch (error) {
    console.error('数据导出失败:', error)
    ElMessage.error('数据导出失败')
  } finally {
    exporting.value = false
  }
}
</script>

<style scoped>
.batch-operations {
  padding: 20px;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  background: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.page-title {
  font-size: 24px;
  font-weight: bold;
  margin: 0;
  color: #333;
}

.header-actions {
  display: flex;
  gap: 10px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.upload-area {
  width: 100%;
  margin-bottom: 20px;
}

.file-info {
  margin-top: 20px;
  padding: 15px;
  background: #f8f9fa;
  border-radius: 4px;
}

.file-details {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
}

.file-name {
  margin-left: 8px;
  font-weight: bold;
}

.file-size {
  margin-left: 8px;
  color: #666;
  font-size: 12px;
}

.file-actions {
  display: flex;
  gap: 10px;
}

.json-tools {
  margin-top: 10px;
  text-align: right;
}

.validation-summary {
  margin-bottom: 20px;
}

.summary-item {
  display: inline-block;
  margin-right: 30px;
  margin-bottom: 10px;
}

.summary-item .label {
  font-weight: bold;
  color: #666;
}

.summary-item .value {
  color: #333;
}

.validation-errors, .validation-warnings {
  margin-top: 15px;
}

.validation-errors h4, .validation-warnings h4 {
  margin-bottom: 10px;
  color: #333;
}

.operation-summary {
  margin-bottom: 20px;
}

.summary-stats {
  display: flex;
  gap: 30px;
  margin-bottom: 20px;
}

.stat-item {
  text-align: center;
  padding: 15px;
  border-radius: 8px;
  min-width: 80px;
}

.stat-item.success {
  background: linear-gradient(135deg, #67C23A, #85CE61);
  color: white;
}

.stat-item.danger {
  background: linear-gradient(135deg, #F56C6C, #F78989);
  color: white;
}

.stat-item.info {
  background: linear-gradient(135deg, #409EFF, #66B1FF);
  color: white;
}

.stat-number {
  font-size: 24px;
  font-weight: bold;
  margin-bottom: 5px;
}

.stat-label {
  font-size: 12px;
}

.operation-details {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 15px;
}

.detail-item {
  display: flex;
  align-items: center;
}

.detail-item .label {
  font-weight: bold;
  margin-right: 8px;
  color: #666;
}

.detail-item .value {
  color: #333;
}

.operation-errors {
  margin-top: 20px;
}

.import-dialog-content {
  padding: 10px 0;
}

.import-dialog-content ol {
  padding-left: 20px;
}

.import-dialog-content li {
  margin-bottom: 8px;
  line-height: 1.5;
}

:deep(.el-card__body) {
  padding: 20px;
}

:deep(.el-card__header) {
  padding: 15px 20px;
  border-bottom: 1px solid #f0f0f0;
}

:deep(.el-upload-dragger) {
  width: 100%;
  height: 180px;
}

:deep(.el-checkbox-group) {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 10px;
}

:deep(.el-table) {
  font-size: 12px;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .summary-stats {
    flex-wrap: wrap;
  }

  .operation-details {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 768px) {
  .batch-operations {
    padding: 10px;
  }

  .page-header {
    flex-direction: column;
    gap: 15px;
    text-align: center;
  }

  .summary-stats {
    flex-direction: column;
    gap: 15px;
  }

  .stat-item {
    min-width: auto;
  }
}
</style>
