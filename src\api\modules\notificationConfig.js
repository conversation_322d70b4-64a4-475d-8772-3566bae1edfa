import { http } from '@/api/request'
import { API_URLS } from './config'

const { NOTIFICATION_CONFIG } = API_URLS

/**
 * 通知配置相关API
 */
export const notificationConfigApi = {
    // 获取通知配置
    getConfig() {
        return http.get(NOTIFICATION_CONFIG.GET)
    },

    // 更新通知配置
    updateConfig(data) {
        return http.put(NOTIFICATION_CONFIG.UPDATE, data)
    },

    // 测试通知配置
    testConfig(data) {
        return http.post(NOTIFICATION_CONFIG.TEST, data)
    },
}

export default notificationConfigApi 