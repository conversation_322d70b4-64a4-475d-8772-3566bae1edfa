#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
登录调试脚本
"""

import requests
import json

def test_login():
    url = "http://localhost:20000/api/v1/auth/login"
    headers = {"Content-Type": "application/x-www-form-urlencoded"}

    # 测试管理员登录 - 使用表单数据格式
    admin_data = {
        "username": "admin",
        "password": "7c222fb2927d828af22f592134e8932480637c0d"
    }

    try:
        print("测试管理员登录（表单格式）...")
        response = requests.post(url, headers=headers, data=admin_data, timeout=10)
        print(f"状态码: {response.status_code}")
        print(f"响应: {response.text}")

        if response.status_code == 200:
            data = response.json()
            # 检查不同的响应格式
            token = None
            if "access_token" in data:
                token = data["access_token"]
            elif "data" in data and isinstance(data["data"], dict) and "access_token" in data["data"]:
                token = data["data"]["access_token"]

            if token:
                print("✅ 管理员登录成功")
                print(f"Token: {token[:50]}...")
                return True
            else:
                print("❌ 管理员登录失败：响应中没有access_token")
                print(f"响应结构: {list(data.keys())}")
        else:
            print(f"❌ 管理员登录失败：状态码 {response.status_code}")

    except Exception as e:
        print(f"❌ 登录请求异常: {e}")

    return False

if __name__ == "__main__":
    test_login()
