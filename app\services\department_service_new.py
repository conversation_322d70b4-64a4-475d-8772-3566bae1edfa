"""
部门服务模块 - 提供部门管理的核心功能
"""

from typing import List, Optional, Dict, Any
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_
import logging
import re

from app.services.base_service import BaseService
from app.services.security_service import SecurityService
from app.models.department import Department
from app.models.user import User
from app.models.merchant import Merchant
from app.models.organization_relation import OrganizationRelation
from app.schemas.department import DepartmentCreate, DepartmentUpdate

try:
    from pypinyin import lazy_pinyin, Style
    PYPINYIN_AVAILABLE = True
except ImportError:
    PYPINYIN_AVAILABLE = False

logger = logging.getLogger(__name__)


class DepartmentService(BaseService[Department, DepartmentCreate, DepartmentUpdate]):
    """部门服务类"""

    def __init__(self, db: Session):
        super().__init__(Department, db)
        self.security_service = SecurityService(db)

    def _generate_department_code(self, name: str, merchant_id: int) -> str:
        """
        自动生成部门代码

        Args:
            name: 部门名称
            merchant_id: 商户ID

        Returns:
            str: 生成的部门代码
        """
        try:
            if PYPINYIN_AVAILABLE:
                # 使用pypinyin生成拼音首字母
                pinyin_list = lazy_pinyin(name, style=Style.FIRST_LETTER)
                base_code = ''.join(pinyin_list).upper()
            else:
                # 如果pypinyin不可用，使用简单的英文字母提取
                base_code = re.sub(r'[^A-Za-z]', '', name).upper()
                if not base_code:
                    # 如果没有英文字母，使用默认前缀
                    base_code = 'DEPT'

            # 限制代码长度，最多8个字符
            if len(base_code) > 8:
                base_code = base_code[:8]
            elif len(base_code) < 2:
                # 如果太短，补充默认后缀
                base_code = base_code + 'DEPT'
                base_code = base_code[:8]

            # 检查代码是否已存在，如果存在则添加数字后缀
            original_code = base_code
            counter = 1

            while self.get_by_code(merchant_id, base_code):
                # 为了保持代码长度合理，限制数字后缀
                if counter > 999:
                    raise ValueError("无法生成唯一的部门代码，请手动指定")

                # 计算数字后缀长度
                suffix = str(counter)
                max_base_length = 8 - len(suffix)

                if max_base_length < 2:
                    # 如果基础代码太长，截断它
                    truncated_base = original_code[:2]
                    base_code = truncated_base + suffix
                else:
                    base_code = original_code[:max_base_length] + suffix

                counter += 1

            return base_code

        except Exception as e:
            logger.error(f"生成部门代码失败: {e}")
            # 如果生成失败，使用时间戳作为后备方案
            import time
            return f"DEPT{int(time.time()) % 10000}"

    def get_by_code(self, merchant_id: int, code: str) -> Optional[Department]:
        """
        根据商户ID和部门代码获取部门

        Args:
            merchant_id: 商户ID
            code: 部门代码

        Returns:
            Optional[Department]: 部门对象或None
        """
        try:
            return self.db.query(Department).filter(
                Department.merchant_id == merchant_id,
                Department.code == code
            ).first()
        except Exception as e:
            self.logger.error(f"根据代码获取部门失败: {e}")
            return None

    def get_by_name_and_parent(
        self,
        merchant_id: int,
        name: str,
        parent_id: Optional[int] = None,
        exclude_id: Optional[int] = None
    ) -> Optional[Department]:
        """
        根据商户ID、部门名称和父部门ID获取部门（用于检查同级重复）

        Args:
            merchant_id: 商户ID
            name: 部门名称
            parent_id: 父部门ID，None表示根级部门
            exclude_id: 排除的部门ID（用于更新时排除自己）

        Returns:
            Optional[Department]: 部门对象或None
        """
        try:
            query = self.db.query(Department).filter(
                Department.merchant_id == merchant_id,
                Department.name == name
            )

            # 处理父部门ID的比较（包括NULL值）
            if parent_id is None:
                query = query.filter(Department.parent_id.is_(None))
            else:
                query = query.filter(Department.parent_id == parent_id)

            # 排除指定ID（用于更新时排除自己）
            if exclude_id is not None:
                query = query.filter(Department.id != exclude_id)

            return query.first()
        except Exception as e:
            self.logger.error(f"根据名称和父部门获取部门失败: {e}")
            return None

    def _check_department_name_uniqueness(
        self,
        merchant_id: int,
        name: str,
        parent_id: Optional[int] = None,
        exclude_id: Optional[int] = None
    ) -> None:
        """
        检查同级部门名称唯一性

        Args:
            merchant_id: 商户ID
            name: 部门名称
            parent_id: 父部门ID
            exclude_id: 排除的部门ID（用于更新时排除自己）

        Raises:
            ValueError: 如果存在同名部门
        """
        existing_dept = self.get_by_name_and_parent(
            merchant_id, name, parent_id, exclude_id
        )

        if existing_dept:
            if parent_id is None:
                raise ValueError(f"在根级别下已存在名为'{name}'的部门")
            else:
                # 获取父部门名称用于更友好的错误提示
                parent_dept = self.get(parent_id)
                parent_name = parent_dept.name if parent_dept else "未知部门"
                raise ValueError(f"在'{parent_name}'下已存在名为'{name}'的部门")

    def get_merchant_departments(
        self,
        merchant_id: int,
        current_user: User,
        include_children: bool = True
    ) -> List[Department]:
        """
        获取商户的所有部门 - 支持数据权限过滤

        Args:
            merchant_id: 商户ID
            current_user: 当前用户
            include_children: 是否包含子部门

        Returns:
            List[Department]: 根据用户数据权限过滤后的部门列表
        """
        try:
            from sqlalchemy.orm import joinedload

            # 基本权限检查
            if not current_user.is_superuser and current_user.merchant_id != merchant_id:
                raise ValueError("无权限访问该商户的部门")

            # 预加载商户和父部门信息
            query = self.db.query(Department).options(
                joinedload(Department.merchant),
                joinedload(Department.parent)
            ).filter(Department.merchant_id == merchant_id)

            if not include_children:
                # 只获取根部门
                query = query.filter(Department.parent_id.is_(None))

            all_departments = query.order_by(Department.level, Department.sort_order).all()

            # 根据用户数据权限过滤部门列表
            return self._filter_departments_by_data_permission(all_departments, current_user)

        except Exception as e:
            self.logger.error(f"获取商户部门失败: {e}")
            return []

    def _filter_departments_by_data_permission(
        self,
        departments: List[Department],
        current_user: User
    ) -> List[Department]:
        """
        根据用户数据权限过滤部门列表

        Args:
            departments: 原始部门列表
            current_user: 当前用户

        Returns:
            List[Department]: 过滤后的部门列表
        """
        try:
            # 超级管理员可以访问所有部门
            if current_user.is_superuser:
                return departments

            # 导入权限服务
            from app.services.permission_service import PermissionService
            permission_service = PermissionService(self.db)

            # 检查数据权限并过滤部门
            if permission_service.check_data_permission(current_user, 'data:department:all'):
                # 【安全修复】：有访问本商户所有部门数据的权限（不是全局权限）
                # 确保所有部门都属于用户的商户
                return [dept for dept in departments if dept.merchant_id == current_user.merchant_id]
            elif permission_service.check_data_permission(current_user, 'data:department:sub'):
                # 有访问本部门及子部门数据的权限
                return self._filter_accessible_departments(departments, current_user, permission_service)
            elif permission_service.check_data_permission(current_user, 'data:department:own'):
                # 只有访问本部门数据的权限
                if hasattr(current_user, 'department_id') and current_user.department_id:
                    return [dept for dept in departments if dept.id == current_user.department_id]
                return []
            else:
                # 没有部门数据权限
                return []

        except Exception as e:
            self.logger.error(f"过滤部门数据权限失败: {e}")
            # 出错时返回空列表，确保安全
            return []

    def _filter_accessible_departments(
        self,
        departments: List[Department],
        current_user: User,
        permission_service
    ) -> List[Department]:
        """
        过滤用户可访问的部门（本部门及子部门）

        Args:
            departments: 原始部门列表
            current_user: 当前用户
            permission_service: 权限服务实例

        Returns:
            List[Department]: 用户可访问的部门列表
        """
        try:
            if not hasattr(current_user, 'department_id') or not current_user.department_id:
                return []

            accessible_departments = []

            for dept in departments:
                # 检查是否是用户所属部门
                if dept.id == current_user.department_id:
                    accessible_departments.append(dept)
                    continue

                # 检查是否是用户部门的子部门
                if self._is_subdepartment(current_user.department_id, dept):
                    accessible_departments.append(dept)

            return accessible_departments

        except Exception as e:
            self.logger.error(f"过滤可访问部门失败: {e}")
            return []

    def _is_subdepartment(self, parent_dept_id: int, target_dept: Department) -> bool:
        """
        检查目标部门是否是指定部门的子部门

        Args:
            parent_dept_id: 父部门ID
            target_dept: 目标部门对象

        Returns:
            bool: 是否是子部门
        """
        try:
            # 通过path字段判断层级关系
            # path格式类似: "/1/2/3/" 表示部门层级路径
            if not target_dept.path:
                return False

            # 获取父部门的path
            from sqlalchemy import text
            sql = text("SELECT path FROM departments WHERE id = :dept_id")
            result = self.db.execute(sql, {'dept_id': parent_dept_id}).fetchone()

            if not result or not result.path:
                return False

            parent_path = result.path
            # 检查目标部门的path是否以父部门的path + 父部门ID开头
            expected_prefix = f"{parent_path}{parent_dept_id}/"
            return target_dept.path.startswith(expected_prefix)

        except Exception as e:
            self.logger.error(f"检查部门层级关系失败: {e}")
            return False

    def get_department_tree(
        self,
        merchant_id: int,
        current_user: User,
        parent_id: Optional[int] = None
    ) -> List[Dict[str, Any]]:
        """
        获取部门树形结构 - 支持数据权限过滤

        Args:
            merchant_id: 商户ID
            current_user: 当前用户
            parent_id: 父部门ID，None表示从根部门开始

        Returns:
            List[Dict[str, Any]]: 根据用户数据权限过滤后的部门树形结构
        """
        try:
            # 基本权限检查
            if not current_user.is_superuser and current_user.merchant_id != merchant_id:
                raise ValueError("无权限访问该商户的部门")

            # 获取所有部门，然后根据数据权限过滤
            all_departments = self.db.query(Department).filter(
                Department.merchant_id == merchant_id,
                Department.status == True
            ).order_by(Department.sort_order, Department.name).all()

            # 根据用户数据权限过滤部门列表
            accessible_departments = self._filter_departments_by_data_permission(all_departments, current_user)

            # 构建树形结构（只包含可访问的部门）
            return self._build_department_tree(accessible_departments, parent_id)

        except Exception as e:
            self.logger.error(f"获取部门树失败: {e}")
            return []

    def _build_department_tree(
        self,
        departments: List[Department],
        parent_id: Optional[int] = None
    ) -> List[Dict[str, Any]]:
        """
        构建部门树形结构 - 支持权限过滤后的部门列表

        Args:
            departments: 部门列表（可能是权限过滤后的）
            parent_id: 父部门ID，None表示从根部门开始

        Returns:
            List[Dict[str, Any]]: 树形结构
        """
        try:
            tree = []

            # 找到指定父部门下的直接子部门
            for dept in departments:
                if dept.parent_id == parent_id:
                    dept_dict = dept.to_dict()
                    # 递归获取子部门
                    dept_dict['children'] = self._build_department_tree(departments, dept.id)
                    tree.append(dept_dict)

            # 如果没有找到匹配的部门且parent_id为None，
            # 则查找所有在当前列表中没有父部门的部门作为根节点
            # 这种情况通常发生在权限过滤后，用户只能访问部分部门时
            if not tree and parent_id is None:
                dept_ids = {dept.id for dept in departments}
                for dept in departments:
                    # 如果部门的父部门不在可访问列表中，则将其作为根节点
                    if dept.parent_id is None or dept.parent_id not in dept_ids:
                        dept_dict = dept.to_dict()
                        # 递归获取子部门
                        dept_dict['children'] = self._build_department_tree(departments, dept.id)
                        tree.append(dept_dict)

            return tree

        except Exception as e:
            self.logger.error(f"构建部门树失败: {e}")
            return []

    def create_department(
        self,
        dept_in: DepartmentCreate,
        current_user: User
    ) -> Optional[Department]:
        """
        创建部门

        Args:
            dept_in: 部门创建数据
            current_user: 当前操作用户

        Returns:
            Optional[Department]: 创建的部门对象或None
        """
        try:
            # 确保merchant_id已设置（API层应该已经处理）
            if dept_in.merchant_id is None:
                raise ValueError("merchant_id不能为空")

            # 权限检查：非超级管理员只能在自己的商户下创建部门
            if not current_user.is_superuser and current_user.merchant_id != dept_in.merchant_id:
                raise ValueError("无权限在该商户下创建部门")

            # 检查商户是否存在
            merchant = self.db.query(Merchant).filter(Merchant.id == dept_in.merchant_id).first()
            if not merchant:
                raise ValueError("商户不存在")

            # 检查同级部门名称唯一性
            self._check_department_name_uniqueness(
                dept_in.merchant_id,
                dept_in.name,
                dept_in.parent_id
            )

            # 自动生成部门代码（如果未提供）
            if not dept_in.code:
                dept_in.code = self._generate_department_code(dept_in.name, dept_in.merchant_id)
            else:
                # 检查部门代码是否已存在
                if self.get_by_code(dept_in.merchant_id, dept_in.code):
                    raise ValueError(f"部门代码 {dept_in.code} 在该商户下已存在")

            # 获取父部门信息
            parent_dept = None
            if dept_in.parent_id:
                parent_dept = self.db.query(Department).filter(
                    Department.id == dept_in.parent_id,
                    Department.merchant_id == dept_in.merchant_id
                ).first()
                if not parent_dept:
                    raise ValueError("父部门不存在或不属于同一商户")

            # 准备部门数据
            dept_data = dept_in.model_dump()
            dept_data['created_by'] = current_user.id

            # 设置层级和路径
            if parent_dept:
                dept_data['level'] = parent_dept.level + 1
                dept_data['path'] = f"{parent_dept.path}{parent_dept.id}/"
            else:
                dept_data['level'] = 1
                dept_data['path'] = f"/{dept_in.merchant_id}/"

            # 创建部门
            db_dept = Department(**dept_data)
            self.db.add(db_dept)
            self.db.commit()
            self.db.refresh(db_dept)

            # 创建组织关系记录
            self._create_organization_relations(db_dept)

            return db_dept
        except Exception as e:
            self.db.rollback()
            self.logger.error(f"创建部门失败: {e}")
            raise

    def update_department(
        self,
        dept_id: int,
        dept_in: DepartmentUpdate,
        current_user: User
    ) -> Optional[Department]:
        """
        更新部门信息

        Args:
            dept_id: 部门ID
            dept_in: 更新数据
            current_user: 当前操作用户

        Returns:
            Optional[Department]: 更新后的部门对象或None
        """
        try:
            # 获取部门（应用数据隔离）
            dept = self.get_with_isolation(dept_id, current_user)
            if not dept:
                raise ValueError("部门不存在或无权限访问")

            # 不能修改根部门的parent_id
            if dept.code == "ROOT" and dept_in.parent_id is not None:
                raise ValueError("不能修改根部门的上级部门")

            # 检查是否需要更新parent_id
            parent_id_changed = False
            new_parent_id = dept_in.parent_id
            if hasattr(dept_in, 'parent_id') and dept_in.parent_id != dept.parent_id:
                parent_id_changed = True

                # 验证新的父部门
                if new_parent_id is not None:
                    new_parent = self.db.query(Department).filter(
                        Department.id == new_parent_id,
                        Department.merchant_id == dept.merchant_id
                    ).first()
                    if not new_parent:
                        raise ValueError("新的上级部门不存在或不属于同一商户")

                    # 检查是否会形成循环引用
                    if self._would_create_cycle(dept_id, new_parent_id):
                        raise ValueError("不能将部门移动到自己的子部门下")

            # 检查部门名称唯一性（如果名称发生变化）
            target_parent_id = new_parent_id if parent_id_changed else dept.parent_id
            if dept_in.name and dept_in.name != dept.name:
                self._check_department_name_uniqueness(
                    dept.merchant_id,
                    dept_in.name,
                    target_parent_id,  # 使用目标parent_id检查唯一性
                    dept_id  # 排除自己
                )

            # 检查部门代码唯一性
            if dept_in.code and dept_in.code != dept.code:
                if self.get_by_code(dept.merchant_id, dept_in.code):
                    raise ValueError(f"部门代码 {dept_in.code} 在该商户下已存在")

            # 更新部门信息（排除merchant_id，但允许parent_id）
            update_data = dept_in.model_dump(exclude_unset=True)
            old_path = dept.path
            old_level = dept.level

            for field, value in update_data.items():
                if hasattr(dept, field) and field not in ['merchant_id']:
                    setattr(dept, field, value)

            # 如果parent_id发生变化，需要重新计算层级和路径
            if parent_id_changed:
                if new_parent_id is not None:
                    new_parent = self.db.query(Department).filter(
                        Department.id == new_parent_id
                    ).first()
                    dept.level = new_parent.level + 1
                    dept.path = f"{new_parent.path}{new_parent.id}/"
                else:
                    # 移动到根级别
                    dept.level = 1
                    dept.path = f"/{dept.merchant_id}/"

                # 更新所有子部门的路径和层级
                self._update_children_paths(dept, old_path, old_level)

                # 重新创建组织关系
                self._recreate_organization_relations(dept)

            self.db.commit()
            self.db.refresh(dept)
            return dept
        except Exception as e:
            self.db.rollback()
            self.logger.error(f"更新部门失败: {e}")
            raise

    def move_department(
        self,
        dept_id: int,
        new_parent_id: Optional[int],
        current_user: User
    ) -> Optional[Department]:
        """
        移动部门到新的父部门下

        Args:
            dept_id: 部门ID
            new_parent_id: 新父部门ID，None表示移动到根级别
            current_user: 当前操作用户

        Returns:
            Optional[Department]: 移动后的部门对象或None
        """
        try:
            # 获取部门
            dept = self.get_with_isolation(dept_id, current_user)
            if not dept:
                raise ValueError("部门不存在或无权限访问")

            # 不能移动根部门
            if dept.code == "ROOT":
                raise ValueError("不能移动根部门")

            # 检查新父部门
            new_parent = None
            if new_parent_id:
                new_parent = self.db.query(Department).filter(
                    Department.id == new_parent_id,
                    Department.merchant_id == dept.merchant_id
                ).first()
                if not new_parent:
                    raise ValueError("新父部门不存在或不属于同一商户")

                # 不能移动到自己的子部门下
                if dept.is_ancestor_of(new_parent):
                    raise ValueError("不能移动到自己的子部门下")

            # 更新部门层级和路径
            old_path = dept.path
            old_level = dept.level

            if new_parent:
                dept.parent_id = new_parent.id
                dept.level = new_parent.level + 1
                dept.path = f"{new_parent.path}{new_parent.id}/"
            else:
                dept.parent_id = None
                dept.level = 1
                dept.path = f"/{dept.merchant_id}/"

            # 更新所有子部门的路径和层级
            self._update_children_paths(dept, old_path, old_level)

            # 重新创建组织关系
            self._recreate_organization_relations(dept)

            self.db.commit()
            self.db.refresh(dept)
            return dept
        except Exception as e:
            self.db.rollback()
            self.logger.error(f"移动部门失败: {e}")
            raise

    def delete_department(self, dept_id: int, current_user: User) -> bool:
        """
        删除部门

        Args:
            dept_id: 部门ID
            current_user: 当前操作用户

        Returns:
            bool: 是否删除成功
        """
        try:
            # 获取部门
            dept = self.get_with_isolation(dept_id, current_user)
            if not dept:
                return False

            # 检查是否可以删除
            can_delete, reason = dept.can_delete()
            if not can_delete:
                raise ValueError(reason)

            self.db.delete(dept)
            self.db.commit()
            return True
        except Exception as e:
            self.db.rollback()
            self.logger.error(f"删除部门失败: {e}")
            raise

    def get_department_users(
        self,
        dept_id: int,
        current_user: User,
        include_children: bool = False
    ) -> List[User]:
        """
        获取部门用户列表

        Args:
            dept_id: 部门ID
            current_user: 当前用户
            include_children: 是否包含子部门用户

        Returns:
            List[User]: 用户列表
        """
        try:
            # 获取部门
            dept = self.get_with_isolation(dept_id, current_user)
            if not dept:
                return []

            if include_children:
                # 获取部门及其所有子部门的用户
                dept_ids = [dept.id] + dept.get_all_children_ids()
                users = self.db.query(User).join(User.user_organizations).filter(
                    User.user_organizations.any(department_id__in=dept_ids),
                    User.is_active == True
                ).all()
            else:
                # 只获取当前部门的用户
                users = self.db.query(User).join(User.user_organizations).filter(
                    User.user_organizations.any(department_id=dept_id),
                    User.is_active == True
                ).all()

            return users
        except Exception as e:
            self.logger.error(f"获取部门用户失败: {e}")
            return []

    def search_departments(
        self,
        merchant_id: int,
        search_term: str,
        current_user: User,
        skip: int = 0,
        limit: int = 100
    ) -> List[Department]:
        """
        搜索部门 - 支持数据权限过滤

        Args:
            merchant_id: 商户ID
            search_term: 搜索关键词
            current_user: 当前用户
            skip: 跳过的记录数
            limit: 限制返回的记录数

        Returns:
            List[Department]: 根据用户数据权限过滤后的部门列表
        """
        try:
            # 基本权限检查
            if not current_user.is_superuser and current_user.merchant_id != merchant_id:
                return []

            query = self.db.query(Department).filter(Department.merchant_id == merchant_id)

            if search_term:
                query = query.filter(
                    or_(
                        Department.name.like(f"%{search_term}%"),
                        Department.code.like(f"%{search_term}%"),
                        Department.description.like(f"%{search_term}%")
                    )
                )

            all_departments = query.all()

            # 根据用户数据权限过滤部门列表
            filtered_departments = self._filter_departments_by_data_permission(all_departments, current_user)

            # 应用分页
            return filtered_departments[skip:skip + limit]

        except Exception as e:
            self.logger.error(f"搜索部门失败: {e}")
            return []

    def _create_organization_relations(self, dept: Department):
        """
        创建组织关系记录

        Args:
            dept: 部门对象
        """
        try:
            # 创建自己与自己的关系（level=0）
            self_relation = OrganizationRelation(
                ancestor_id=dept.id,
                descendant_id=dept.id,
                level=0
            )
            self.db.add(self_relation)

            # 创建与所有祖先的关系
            ancestors = dept.get_ancestors()
            for i, ancestor in enumerate(ancestors):
                relation = OrganizationRelation(
                    ancestor_id=ancestor.id,
                    descendant_id=dept.id,
                    level=i + 1
                )
                self.db.add(relation)

            self.db.commit()
        except Exception as e:
            self.db.rollback()
            self.logger.error(f"创建组织关系失败: {e}")

    def _recreate_organization_relations(self, dept: Department):
        """
        重新创建组织关系记录

        Args:
            dept: 部门对象
        """
        try:
            # 删除旧的关系记录
            self.db.query(OrganizationRelation).filter(
                or_(
                    OrganizationRelation.ancestor_id == dept.id,
                    OrganizationRelation.descendant_id == dept.id
                )
            ).delete()

            # 重新创建关系记录
            self._create_organization_relations(dept)

            # 为所有子部门重新创建关系记录
            children = dept.get_all_children()
            for child in children:
                self._recreate_organization_relations(child)
        except Exception as e:
            self.db.rollback()
            self.logger.error(f"重新创建组织关系失败: {e}")

    def _would_create_cycle(self, dept_id: int, new_parent_id: int) -> bool:
        """
        检查移动操作是否会创建循环引用

        Args:
            dept_id: 要移动的部门ID
            new_parent_id: 新的父部门ID

        Returns:
            bool: 是否会创建循环引用
        """
        try:
            # 检查新父部门是否是当前部门的后代
            current_id = new_parent_id
            while current_id:
                if current_id == dept_id:
                    return True

                parent_dept = self.db.query(Department).filter(
                    Department.id == current_id
                ).first()
                if parent_dept:
                    current_id = parent_dept.parent_id
                else:
                    break

            return False
        except Exception as e:
            self.logger.error(f"检查循环引用失败: {e}")
            return True  # 出错时保守处理，认为会产生循环引用

    def _update_children_paths(self, dept: Department, old_path: str, old_level: int):
        """
        更新子部门的路径和层级

        Args:
            dept: 父部门对象
            old_path: 旧路径
            old_level: 旧层级
        """
        try:
            level_diff = dept.level - old_level

            children = self.db.query(Department).filter(
                Department.path.like(f"{old_path}{dept.id}/%")
            ).all()

            for child in children:
                # 更新路径
                child.path = child.path.replace(old_path, dept.path, 1)
                # 更新层级
                child.level += level_diff
        except Exception as e:
            self.logger.error(f"更新子部门路径失败: {e}")

    def get_department_detail(self, dept_id: int, current_user: User) -> Dict[str, Any]:
        """
        获取部门详情（包含关联信息）

        Args:
            dept_id: 部门ID
            current_user: 当前用户

        Returns:
            Dict[str, Any]: 部门详情信息
        """
        try:
            from sqlalchemy.orm import joinedload
            from app.models.merchant import Merchant

            # 获取部门（包含关联信息）
            query = self.db.query(Department).options(
                joinedload(Department.merchant),
                joinedload(Department.parent)
            ).filter(Department.id == dept_id)

            # 应用数据隔离
            if not current_user.is_superuser:
                query = query.filter(Department.merchant_id == current_user.merchant_id)

            dept = query.first()
            if not dept:
                raise ValueError("部门不存在或无权限访问")

            # 获取基本信息
            detail = dept.to_dict()

            # 获取商户名称
            if dept.merchant:
                detail["merchantName"] = dept.merchant.name
            else:
                detail["merchantName"] = "未知"

            # 获取上级部门名称
            if dept.parent:
                detail["parentName"] = dept.parent.name
            else:
                detail["parentName"] = "无"

            return detail

        except Exception as e:
            self.logger.error(f"获取部门详情失败: {e}")
            raise ValueError(f"获取部门详情失败: {e}")

    def get_department_statistics(self, dept_id: int, current_user: User) -> Dict[str, Any]:
        """
        获取部门统计信息

        Args:
            dept_id: 部门ID
            current_user: 当前用户

        Returns:
            Dict[str, Any]: 统计信息
        """
        try:
            # 获取部门（应用数据隔离）
            dept = self.get_with_isolation(dept_id, current_user)
            if not dept:
                raise ValueError("部门不存在或无权限访问")

            from app.models.user import User as UserModel
            from app.models.card_record import CardRecord
            from sqlalchemy import func, and_
            from datetime import datetime, date

            # 获取部门用户统计
            total_users = self.db.query(UserModel).filter(
                UserModel.department_id == dept_id,
                UserModel.is_active == True
            ).count()

            active_users = self.db.query(UserModel).filter(
                UserModel.department_id == dept_id,
                UserModel.is_active == True
            ).count()  # 简化版本，可以根据需要添加更复杂的活跃用户判断逻辑

            # 获取子部门统计
            total_children = self.db.query(Department).filter(
                Department.parent_id == dept_id
            ).count()

            active_children = self.db.query(Department).filter(
                Department.parent_id == dept_id,
                Department.status == True
            ).count()

            # 获取绑卡记录统计
            card_stats = self.db.query(
                func.count(CardRecord.id).label('total'),
                func.sum(func.case((CardRecord.status == 'success', 1), else_=0)).label('success'),
                func.sum(func.case((CardRecord.status == 'failed', 1), else_=0)).label('failed')
            ).filter(CardRecord.department_id == dept_id).first()

            # 获取今日绑卡记录统计
            today = date.today()
            today_stats = self.db.query(
                func.count(CardRecord.id).label('today_total')
            ).filter(
                CardRecord.department_id == dept_id,
                func.date(CardRecord.created_at) == today
            ).first()

            return {
                "total_users": total_users or 0,
                "active_users": active_users or 0,
                "total_children": total_children or 0,
                "active_children": active_children or 0,
                "total_records": card_stats.total or 0 if card_stats else 0,
                "success_records": card_stats.success or 0 if card_stats else 0,
                "failed_records": card_stats.failed or 0 if card_stats else 0,
                "today_records": today_stats.today_total or 0 if today_stats else 0
            }

        except Exception as e:
            self.logger.error(f"获取部门统计失败: {e}")
            # 返回默认统计数据，避免前端报错
            return {
                "total_users": 0,
                "active_users": 0,
                "total_children": 0,
                "active_children": 0,
                "total_records": 0,
                "success_records": 0,
                "failed_records": 0,
                "today_records": 0
            }

    def apply_data_isolation(self, query, current_user: User):
        """
        重写基类方法，添加强制商户隔离
        应用数据隔离规则 - 支持用户级、部门级、商户级权限，确保部门数据的商户隔离

        Args:
            query: SQLAlchemy查询对象
            current_user: 当前用户

        Returns:
            query: 应用隔离规则后的查询对象
        """
        # 【安全修复】：强制商户隔离 - 除超级管理员外，所有用户只能访问自己商户的部门数据
        if not current_user.is_superuser:
            if not current_user.merchant_id:
                # 如果用户没有商户ID，返回空结果
                self.logger.warning(f"[SECURITY] 用户 {current_user.id} 没有商户ID，拒绝部门数据访问")
                query = query.filter(Department.id == -1)  # 强制返回空结果
                return query

            # 强制添加商户过滤，确保无法跨商户访问
            query = query.filter(Department.merchant_id == current_user.merchant_id)
            self.logger.info(f"[SECURITY] 强制商户隔离：用户 {current_user.id} 只能访问商户 {current_user.merchant_id} 的部门数据")

        # 然后应用基类的数据隔离逻辑
        return super().apply_data_isolation(query, current_user)

    def get_with_security_check(self, dept_id: int, current_user: User) -> Optional[Department]:
        """
        获取部门（带安全检查）

        Args:
            dept_id: 部门ID
            current_user: 当前用户

        Returns:
            Optional[Department]: 部门对象或None
        """
        try:
            # 使用安全服务验证访问权限
            if not self.security_service.validate_department_access(current_user, dept_id, "read"):
                return None

            # 记录数据访问
            dept = self.get_with_isolation(dept_id, current_user)
            if dept:
                self.security_service.log_data_access(
                    current_user,
                    "read",
                    "department",
                    dept_id,
                    dept.merchant_id
                )

            return dept

        except Exception as e:
            self.logger.error(f"安全检查获取部门失败: {e}")
            return None
