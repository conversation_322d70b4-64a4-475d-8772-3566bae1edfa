#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
清理测试数据脚本
"""

import sys
import os
import json

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(__file__)))

from test.conftest import TestBase


def cleanup_test_data():
    """清理所有测试数据"""
    api_test = TestBase()
    
    # 管理员登录
    admin_token = api_test.login("admin", "7c222fb2927d828af22f592134e8932480637c0d")
    if not admin_token:
        print("❌ 管理员登录失败")
        return
    
    print("✅ 管理员登录成功")
    
    # 清理测试用户
    print("\n=== 清理测试用户 ===")
    status_code, response = api_test.make_request("GET", "/users?page_size=100", admin_token)
    if status_code == 200:
        data = response.get("data", {})
        users = data.get("items", [])
        
        test_users = [u for u in users if u.get("username", "").startswith("test_")]
        print(f"找到 {len(test_users)} 个测试用户")
        
        for user in test_users:
            user_id = user.get("id")
            username = user.get("username")
            if user_id:
                delete_status, delete_response = api_test.make_request(
                    "DELETE", f"/users/{user_id}", admin_token
                )
                if delete_status == 200:
                    print(f"  ✅ 删除用户: {username}")
                else:
                    print(f"  ❌ 删除用户失败: {username}")
    
    # 清理测试部门
    print("\n=== 清理测试部门 ===")
    status_code, response = api_test.make_request("GET", "/departments?page_size=100", admin_token)
    if status_code == 200:
        data = response.get("data", {})
        departments = data.get("items", [])
        
        test_departments = [d for d in departments if d.get("name", "").startswith("测试")]
        print(f"找到 {len(test_departments)} 个测试部门")
        
        for dept in test_departments:
            dept_id = dept.get("id")
            dept_name = dept.get("name")
            if dept_id:
                delete_status, delete_response = api_test.make_request(
                    "DELETE", f"/departments/{dept_id}", admin_token
                )
                if delete_status == 200:
                    print(f"  ✅ 删除部门: {dept_name}")
                else:
                    print(f"  ❌ 删除部门失败: {dept_name}")
    
    # 清理测试商户
    print("\n=== 清理测试商户 ===")
    status_code, response = api_test.make_request("GET", "/merchants?page_size=100", admin_token)
    if status_code == 200:
        data = response.get("data", {})
        merchants = data.get("items", [])
        
        test_merchants = [m for m in merchants if m.get("name", "").startswith("测试")]
        print(f"找到 {len(test_merchants)} 个测试商户")
        
        for merchant in test_merchants:
            merchant_id = merchant.get("id")
            merchant_name = merchant.get("name")
            if merchant_id:
                delete_status, delete_response = api_test.make_request(
                    "DELETE", f"/merchants/{merchant_id}", admin_token
                )
                if delete_status == 200:
                    print(f"  ✅ 删除商户: {merchant_name}")
                else:
                    print(f"  ❌ 删除商户失败: {merchant_name}")
    
    # 清理测试角色
    print("\n=== 清理测试角色 ===")
    status_code, response = api_test.make_request("GET", "/roles?page_size=100", admin_token)
    print(f"获取角色列表状态码: {status_code}")
    print(f"响应: {json.dumps(response, indent=2, ensure_ascii=False)}")

    if status_code == 200:
        data = response.get("data", {})
        roles = data.get("items", [])
        print(f"总共找到 {len(roles)} 个角色")

        # 查找所有测试相关的角色
        test_roles = []
        for r in roles:
            name = r.get("name", "")
            code = r.get("code", "")
            if (name.startswith("测试") or code.startswith("test_") or
                name.startswith("调试") or code.startswith("debug_")):
                test_roles.append(r)
                print(f"  找到测试角色: {name} ({code}) - ID: {r.get('id')}")

        print(f"找到 {len(test_roles)} 个测试角色")

        for role in test_roles:
            role_id = role.get("id")
            role_name = role.get("name")
            role_code = role.get("code")
            if role_id:
                delete_status, delete_response = api_test.make_request(
                    "DELETE", f"/roles/{role_id}", admin_token
                )
                if delete_status == 200:
                    print(f"  ✅ 删除角色: {role_name} ({role_code})")
                else:
                    print(f"  ❌ 删除角色失败: {role_name} ({role_code}) - {delete_response.get('message', '未知错误')}")
    
    print("\n✅ 测试数据清理完成")


if __name__ == "__main__":
    cleanup_test_data()
