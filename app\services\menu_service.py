"""
菜单服务模块 - 重新设计版本
提供基于角色的动态菜单配置功能
"""

from typing import List, Dict, Any, Optional
from sqlalchemy.orm import Session
from sqlalchemy import text

from app.models.user import User
from app.models.menu import Menu
# 避免循环导入，在需要时动态导入
import logging

logger = logging.getLogger(__name__)


class MenuService:
    """菜单服务类"""
    
    def __init__(self):
        self.logger = logger
    
    def get_user_menus(self, db: Session, user: User) -> List[Dict[str, Any]]:
        """
        获取用户可访问的菜单列表 - 使用动态权限系统

        Args:
            db: 数据库会话
            user: 用户对象

        Returns:
            List[Dict]: 菜单树结构
        """
        try:
            # 使用动态权限服务获取菜单
            from app.services.permission_service import PermissionService
            permission_service = PermissionService(db)
            return permission_service.get_user_menus(user)

        except Exception as e:
            self.logger.error(f"获取用户菜单失败: {e}", exc_info=True)
            return self._get_default_menus()
    
    def _build_menu_tree(self, menus: List[Menu]) -> List[Dict[str, Any]]:
        """
        构建菜单树结构
        
        Args:
            menus: 菜单列表
            
        Returns:
            List[Dict]: 菜单树
        """
        # 将菜单转换为字典格式
        menu_dict = {}
        for menu in menus:
            menu_dict[menu.id] = {
                "id": menu.id,
                "name": menu.name,
                "code": menu.code,
                "path": menu.path,
                "redirect": menu.redirect,
                "icon": menu.icon,
                "type": menu.type,
                "is_visible": menu.is_visible,
                "sort_order": menu.sort_order,
                "parent_id": menu.parent_id,
                "children": []
            }
        
        # 构建树结构
        root_menus = []
        for menu_id, menu_data in menu_dict.items():
            parent_id = menu_data["parent_id"]
            if parent_id is None:
                # 根菜单
                root_menus.append(menu_data)
            elif parent_id in menu_dict:
                # 子菜单
                menu_dict[parent_id]["children"].append(menu_data)
        
        # 递归排序
        def sort_menus(menu_list):
            menu_list.sort(key=lambda x: (x["sort_order"], x["id"]))
            for menu in menu_list:
                if menu["children"]:
                    sort_menus(menu["children"])
        
        sort_menus(root_menus)
        return root_menus
    
    def _get_default_menus(self) -> List[Dict[str, Any]]:
        """获取默认菜单（仪表盘）"""
        return [
            {
                "id": 1,
                "name": "仪表盘",
                "code": "dashboard",
                "path": "/dashboard",
                "redirect": None,
                "icon": "el-icon-odometer",
                "type": "menu",
                "is_visible": True,
                "sort_order": 1,
                "parent_id": None,
                "children": []
            }
        ]
    
    def check_menu_permission(self, db: Session, user: User, menu_code: str) -> bool:
        """
        检查用户是否有访问指定菜单的权限 - 使用动态权限系统

        Args:
            db: 数据库会话
            user: 用户对象
            menu_code: 菜单代码

        Returns:
            bool: 是否有权限
        """
        try:
            # 使用动态权限服务检查菜单权限
            from app.services.permission_service import PermissionService
            permission_service = PermissionService(db)
            return permission_service.check_menu_permission(user, menu_code)
        except Exception as e:
            self.logger.error(f"检查菜单权限失败: {e}")
            return False
    
    def get_menu_by_code(self, db: Session, menu_code: str) -> Optional[Menu]:
        """
        根据菜单代码获取菜单
        
        Args:
            db: 数据库会话
            menu_code: 菜单代码
            
        Returns:
            Optional[Menu]: 菜单对象
        """
        return db.query(Menu).filter(Menu.code == menu_code).first()
    
    def create_menu(self, db: Session, menu_data: Dict[str, Any]) -> Menu:
        """
        创建菜单
        
        Args:
            db: 数据库会话
            menu_data: 菜单数据
            
        Returns:
            Menu: 创建的菜单对象
        """
        menu = Menu(**menu_data)
        db.add(menu)
        db.commit()
        db.refresh(menu)
        return menu
    
    def update_menu(self, db: Session, menu_id: int, menu_data: Dict[str, Any]) -> Optional[Menu]:
        """
        更新菜单
        
        Args:
            db: 数据库会话
            menu_id: 菜单ID
            menu_data: 菜单数据
            
        Returns:
            Optional[Menu]: 更新的菜单对象
        """
        menu = db.query(Menu).filter(Menu.id == menu_id).first()
        if not menu:
            return None
        
        for key, value in menu_data.items():
            if hasattr(menu, key):
                setattr(menu, key, value)
        
        db.commit()
        db.refresh(menu)
        return menu
    
    def delete_menu(self, db: Session, menu_id: int) -> bool:
        """
        删除菜单
        
        Args:
            db: 数据库会话
            menu_id: 菜单ID
            
        Returns:
            bool: 是否删除成功
        """
        menu = db.query(Menu).filter(Menu.id == menu_id).first()
        if not menu:
            return False
        
        # 检查是否有子菜单
        children = db.query(Menu).filter(Menu.parent_id == menu_id).count()
        if children > 0:
            raise ValueError("无法删除有子菜单的菜单")
        
        db.delete(menu)
        db.commit()
        return True
    
    def get_all_menus(self, db: Session) -> List[Menu]:
        """
        获取所有菜单 - 优化N+1查询

        Args:
            db: 数据库会话

        Returns:
            List[Menu]: 菜单列表
        """
        # 使用预加载获取菜单及其权限，避免N+1查询
        from sqlalchemy.orm import selectinload
        return (
            db.query(Menu)
            .options(selectinload(Menu.permissions))
            .order_by(Menu.sort_order, Menu.id)
            .all()
        )


# 创建全局菜单服务实例
menu_service = MenuService()
