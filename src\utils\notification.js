/**
 * 通知工具
 * 用于处理系统通知、警报和消息
 */

import { ElMessage as message, ElNotification as notification } from 'element-plus';
import { useNotificationStore } from '@/store/modules/notification';
import { h } from 'vue';
import { Warning, CircleCheck, InfoFilled, WarningFilled } from '@element-plus/icons-vue';
import router from '../router';

// 通知类型
export const NOTIFICATION_TYPES = {
    SUCCESS: 'success',
    INFO: 'info',
    WARNING: 'warning',
    ERROR: 'error',
    SECURITY_AUDIT: 'security_audit'
};

// 风险级别图标映射
const RISK_LEVEL_ICONS = {
    CRITICAL: h(WarningFilled, { style: { color: '#ff4d4f' } }),
    HIGH: h(Warning, { style: { color: '#fa8c16' } }),
    MEDIUM: h(Warning, { style: { color: '#faad14' } }),
    LOW: h(InfoFilled, { style: { color: '#1890ff' } }),
    INFO: h(CircleCheck, { style: { color: '#52c41a' } })
};

// 风险级别文本
const RISK_LEVEL_TEXT = {
    CRITICAL: '严重风险',
    HIGH: '高风险',
    MEDIUM: '中风险',
    LOW: '低风险',
    INFO: '信息'
};

/**
 * 显示消息提示
 * @param {string} type 消息类型 (success, error, info, warning)
 * @param {string} content 消息内容
 * @param {number} duration 显示时长
 */
export function showMessage(type, content, duration = 3) {
    if (Object.values(NOTIFICATION_TYPES).includes(type)) {
        message({
            type: type,
            message: content,
            duration: duration * 1000
        });
    } else {
        message({
            type: 'info',
            message: content,
            duration: duration * 1000
        });
    }
}

/**
 * 显示成功消息
 * @param {string} content 消息内容
 * @param {number} duration 显示时长
 */
export function showSuccess(content, duration = 3) {
    showMessage(NOTIFICATION_TYPES.SUCCESS, content, duration);
}

/**
 * 显示错误消息
 * @param {string} content 消息内容
 * @param {number} duration 显示时长
 */
export function showError(content, duration = 3) {
    showMessage(NOTIFICATION_TYPES.ERROR, content, duration);
}

/**
 * 显示警告消息
 * @param {string} content 消息内容
 * @param {number} duration 显示时长
 */
export function showWarning(content, duration = 3) {
    showMessage(NOTIFICATION_TYPES.WARNING, content, duration);
}

/**
 * 显示信息消息
 * @param {string} content 消息内容
 * @param {number} duration 显示时长
 */
export function showInfo(content, duration = 3) {
    showMessage(NOTIFICATION_TYPES.INFO, content, duration);
}

/**
 * 显示通知
 * @param {string} type 通知类型 (success, error, info, warning)
 * @param {string} title 通知标题
 * @param {string} description 通知描述
 * @param {number} duration 显示时长
 * @param {function} onClick 点击通知的回调函数
 */
export function showNotification(type, title, description, duration = 4.5, onClick = null) {
    notification({
        type: type,
        title: title,
        message: description,
        duration: duration * 1000,
        onClick: onClick
    });
}

/**
 * 添加安全审计通知
 * @param {Object} auditInfo 安全审计信息
 */
export function addSecurityAuditNotification(auditInfo) {
    const store = useNotificationStore();

    // 确定最高风险级别
    let highestRisk = 'INFO';
    const riskLevels = ['INFO', 'LOW', 'MEDIUM', 'HIGH', 'CRITICAL'];

    auditInfo.patterns.forEach(pattern => {
        const patternRiskIndex = riskLevels.indexOf(pattern.riskLevel);
        const highestRiskIndex = riskLevels.indexOf(highestRisk);

        if (patternRiskIndex > highestRiskIndex) {
            highestRisk = pattern.riskLevel;
        }
    });

    // 创建通知对象
    const notificationObj = {
        id: `security-audit-${Date.now()}`,
        type: NOTIFICATION_TYPES.SECURITY_AUDIT,
        title: getSecurityAlertTitle(highestRisk, auditInfo),
        description: getSecurityAlertDescription(auditInfo),
        timestamp: auditInfo.timestamp,
        read: false,
        riskLevel: highestRisk,
        auditInfo
    };

    // 添加到通知存储
    store.addNotification(notificationObj);

    // 显示通知提示
    showSecurityAlert(notificationObj);

    return notificationObj;
}

/**
 * 获取安全警报标题
 * @param {string} riskLevel 风险级别
 * @param {Object} auditInfo 审计信息
 * @returns {string} 警报标题
 */
function getSecurityAlertTitle(riskLevel, auditInfo) {
    const riskText = RISK_LEVEL_TEXT[riskLevel] || '安全警报';

    // 获取第一个检测到的模式
    const firstPattern = auditInfo.patterns[0];

    if (riskLevel === 'CRITICAL') {
        return `紧急 - ${riskText}: ${firstPattern.description}`;
    } else if (riskLevel === 'HIGH') {
        return `重要 - ${riskText}: ${firstPattern.description}`;
    } else {
        return `${riskText}: ${firstPattern.description}`;
    }
}

/**
 * 获取安全警报描述
 * @param {Object} auditInfo 审计信息
 * @returns {string} 警报描述
 */
function getSecurityAlertDescription(auditInfo) {
    const { username, eventType, operationType, timestamp } = auditInfo;
    const date = new Date(timestamp);
    const timeStr = `${date.getHours().toString().padStart(2, '0')}:${date.getMinutes().toString().padStart(2, '0')}:${date.getSeconds().toString().padStart(2, '0')}`;

    // 获取第一个检测到的模式
    const firstPattern = auditInfo.patterns[0];

    return `${timeStr} - 用户 ${username} ${getEventDescription(eventType, operationType)} - ${firstPattern.description}`;
}

/**
 * 获取事件描述
 * @param {string} eventType 事件类型
 * @param {string} operationType 操作类型
 * @returns {string} 事件描述
 */
function getEventDescription(eventType, operationType) {
    switch (eventType) {
        case 'LOGIN':
            return '登录系统';
        case 'LOGOUT':
            return '登出系统';
        case 'PASSWORD_CHANGE':
            return '尝试修改密码';
        case 'OPERATION':
            switch (operationType) {
                case 'CREATE':
                    return '创建资源';
                case 'READ':
                    return '读取资源';
                case 'UPDATE':
                    return '更新资源';
                case 'DELETE':
                    return '删除资源';
                case 'EXPORT':
                    return '导出数据';
                case 'IMPORT':
                    return '导入数据';
                case 'QUERY':
                    return '查询数据';
                default:
                    return `执行操作 (${operationType})`;
            }
        case 'ACCESS':
            return operationType === 'DENIED' ? '访问被拒绝' : '访问资源';
        case 'BATCH_OPERATION':
            return `执行批量${operationType === 'DELETE' ? '删除' : operationType === 'UPDATE' ? '更新' : '操作'}`;
        default:
            return `${eventType} (${operationType})`;
    }
}

/**
 * 显示安全警报通知
 * @param {Object} notificationObj 通知对象
 */
function showSecurityAlert(notificationObj) {
    const { riskLevel, title, description, auditInfo } = notificationObj;

    // 根据风险级别决定通知的类型和持续时间
    let notificationType = 'info';
    let duration = 4.5;

    switch (riskLevel) {
        case 'CRITICAL':
            notificationType = 'error';
            duration = 0; // 不自动关闭
            break;
        case 'HIGH':
            notificationType = 'error';
            duration = 10;
            break;
        case 'MEDIUM':
            notificationType = 'warning';
            duration = 8;
            break;
        case 'LOW':
            notificationType = 'info';
            duration = 6;
            break;
        default:
            notificationType = 'info';
            duration = 4.5;
    }

    // 显示通知
    notification({
        type: notificationType,
        title: title,
        message: description,
        duration: duration * 1000,
        icon: RISK_LEVEL_ICONS[riskLevel],
        onClick: () => {
            // 跳转到安全审计页面
            router.push({
                path: '/security-audit',
                query: {
                    id: notificationObj.id
                }
            });
        }
    });
} 