from sqlalchemy import Column, Integer, BigInteger, String, ForeignKey
from sqlalchemy.orm import relationship

from app.models.base import BaseModel, TimestampMixin


class IpWhitelist(BaseModel, TimestampMixin):
    """IP白名单模型"""

    __tablename__ = "ip_whitelist"

    merchant_id = Column(
        BigInteger, ForeignKey("merchants.id", ondelete="CASCADE"), nullable=False
    )
    ip = Column(String(50), nullable=False, index=True)
    description = Column(String(255), nullable=False)

    # 关联关系
    merchant = relationship("Merchant", back_populates="ip_whitelist")

    def __repr__(self):
        return (
            f"<IpWhitelist(id={self.id}, merchant_id={self.merchant_id}, ip={self.ip})>"
        )
