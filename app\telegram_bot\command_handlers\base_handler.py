"""
基础命令处理器
"""

import time
from abc import ABC, abstractmethod
from typing import Optional, Dict, Any
from telegram import Update
from telegram.ext import ContextTypes
from sqlalchemy.orm import Session

from app.core.logging import get_logger
from app.models.telegram_group import TelegramGroup
from app.models.telegram_user import TelegramUser
from app.models.telegram_bot_log import TelegramBotLog
from ..config import BotConfig
from ..rate_limiter import RateLimiter
from ..services.permission_service import TelegramPermissionService
from ..services.error_handler import telegram_error_handler, ErrorType
from ..exceptions import (
    PermissionError,
    GroupNotBoundError,
    UserNotVerifiedError,
    RateLimitError
)

logger = get_logger(__name__)


class BaseCommandHandler(ABC):
    """基础命令处理器"""
    
    def __init__(self, db_session: Session, config: BotConfig, rate_limiter: RateLimiter):
        self.db = db_session
        self.config = config
        self.rate_limiter = rate_limiter
        self.logger = logger
        self.permission_service = TelegramPermissionService(db_session)
    
    async def handle(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """处理命令的通用流程"""
        start_time = time.time()
        chat_id = update.effective_chat.id
        user_id = update.effective_user.id
        command = update.message.text.split()[0] if update.message else "unknown"
        
        try:
            # 频率限制检查
            self.rate_limiter.check_all_limits(user_id, chat_id, command)
            
            # 权限验证
            await self.verify_permissions(update, context)
            
            # 执行具体命令
            result = await self.execute_command(update, context)
            
            # 记录成功日志
            execution_time = int((time.time() - start_time) * 1000)
            await self.log_success(chat_id, user_id, command, execution_time, result)
            
            return result
            
        except RateLimitError as e:
            # 频率限制错误
            error_type = ErrorType.RATE_LIMIT_EXCEEDED
            if "命令频率" in str(e):
                error_type = ErrorType.COMMAND_LIMIT
            elif "查询频率" in str(e):
                error_type = ErrorType.QUERY_LIMIT

            # 使用增强的错误消息
            error_context = {
                "chat_id": chat_id,
                "user_id": user_id,
                "command": command,
                "user_name": self.get_user_display_name(update)
            }
            error_message = telegram_error_handler.get_error_with_suggestions(error_type, error_context)
            await self.send_error_response(update, error_message)
            telegram_error_handler.log_error(error_type, e, error_context)
            await self.log_error(chat_id, user_id, command, str(e), int((time.time() - start_time) * 1000))

        except PermissionError as e:
            # 权限不足错误 - 不发送额外的错误消息，因为具体的处理器已经发送了详细的引导信息
            error_context = {
                "chat_id": chat_id,
                "user_id": user_id,
                "command": command,
                "user_name": self.get_user_display_name(update),
                "permission_error": str(e)
            }
            # 只记录日志，不发送重复的错误消息
            telegram_error_handler.log_error(ErrorType.PERMISSION_DENIED, e, error_context)
            await self.log_permission_denied(chat_id, user_id, command, str(e))

        except GroupNotBoundError as e:
            # 群组未绑定错误
            error_context = {
                "chat_id": chat_id,
                "user_id": user_id,
                "command": command,
                "user_name": self.get_user_display_name(update)
            }
            error_message = telegram_error_handler.get_error_with_suggestions(ErrorType.GROUP_NOT_BOUND, error_context)
            await self.send_error_response(update, error_message)
            telegram_error_handler.log_error(ErrorType.GROUP_NOT_BOUND, e, error_context)
            await self.log_permission_denied(chat_id, user_id, command, str(e))

        except UserNotVerifiedError as e:
            # 用户未验证错误
            error_context = {
                "chat_id": chat_id,
                "user_id": user_id,
                "command": command,
                "user_name": self.get_user_display_name(update)
            }
            error_message = telegram_error_handler.get_error_with_suggestions(ErrorType.USER_NOT_VERIFIED, error_context)
            await self.send_error_response(update, error_message)
            telegram_error_handler.log_error(ErrorType.USER_NOT_VERIFIED, e, error_context)
            await self.log_permission_denied(chat_id, user_id, command, str(e))

        except Exception as e:
            # 其他未知错误
            error_context = {
                "chat_id": chat_id,
                "user_id": user_id,
                "command": command,
                "user_name": self.get_user_display_name(update),
                "error_details": str(e)
            }
            error_message = telegram_error_handler.get_error_with_suggestions(ErrorType.UNKNOWN_ERROR, error_context)
            await self.send_error_response(update, error_message)
            telegram_error_handler.log_error(ErrorType.UNKNOWN_ERROR, e, error_context)
            await self.log_error(chat_id, user_id, command, str(e), int((time.time() - start_time) * 1000))
    
    @abstractmethod
    async def verify_permissions(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """验证权限 - 子类必须实现"""
        pass
    
    @abstractmethod
    async def execute_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """执行具体命令 - 子类必须实现"""
        pass
    
    async def get_telegram_group(self, chat_id: int) -> Optional[TelegramGroup]:
        """获取Telegram群组信息"""
        return self.db.query(TelegramGroup).filter_by(chat_id=chat_id).first()
    
    async def get_telegram_user(self, user_id: int) -> Optional[TelegramUser]:
        """获取Telegram用户信息"""
        return self.db.query(TelegramUser).filter_by(telegram_user_id=user_id).first()
    
    async def verify_group_bound(self, chat_id: int) -> TelegramGroup:
        """验证群组已绑定"""
        group = await self.get_telegram_group(chat_id)
        if not group:
            raise GroupNotBoundError("群组未绑定，请先绑定群组")
        
        if not group.is_active():
            raise GroupNotBoundError("群组状态异常，无法使用")
        
        return group
    
    async def verify_user_verified(self, user_id: int) -> TelegramUser:
        """验证用户已验证"""
        telegram_user = await self.get_telegram_user(user_id)
        if not telegram_user or not telegram_user.is_verified():
            raise UserNotVerifiedError("用户未验证，请先完成身份验证")
        
        return telegram_user
    
    async def verify_user_group_access(self, telegram_user: TelegramUser, group: TelegramGroup):
        """验证用户对群组的访问权限"""
        if not telegram_user.can_access_group(group):
            raise PermissionError("您没有权限访问该群组的数据")

    def refresh_db_session(self):
        """
        刷新数据库会话，确保能读取到最新数据
        解决SQLAlchemy会话级别缓存导致的数据不实时问题
        """
        try:
            # 提交任何待处理的事务
            self.db.commit()
            # 清除会话中的所有对象，强制从数据库重新查询
            self.db.expire_all()
            self.logger.debug("数据库会话已刷新，确保数据实时性")
        except Exception as refresh_error:
            # 如果刷新失败，回滚事务并记录警告
            try:
                self.db.rollback()
            except Exception:
                pass
            self.logger.warning(f"数据库会话刷新失败: {refresh_error}")
            # 不抛出异常，允许查询继续进行

    async def _validate_and_get_foreign_keys(self, chat_id: int, user_id: int) -> tuple[Optional[int], Optional[int]]:
        """验证并获取有效的外键ID，确保引用的记录真实存在

        解决SQLAlchemy会话缓存和事务隔离导致的数据不一致问题
        """
        group_id = None
        telegram_user_id = None

        try:
            # 刷新数据库会话，清除可能的缓存数据
            # 这是解决删除操作后仍然能查询到已删除记录的关键
            try:
                self.db.commit()  # 提交任何待处理的事务
            except Exception:
                self.db.rollback()  # 如果提交失败，回滚事务

            # 清除会话中的所有对象，强制从数据库重新查询
            self.db.expunge_all()

            # 使用直接的数据库查询验证群组ID是否存在
            # 不依赖对象属性，直接查询主键确保数据的实时性
            group_exists = self.db.query(TelegramGroup.id).filter_by(chat_id=chat_id).first()
            if group_exists:
                group_id = group_exists[0]  # 获取ID值
                self.logger.debug(f"找到有效的群组记录: chat_id={chat_id}, group_id={group_id}")
            else:
                self.logger.debug(f"未找到群组记录: chat_id={chat_id}")

            # 使用直接的数据库查询验证Telegram用户ID是否存在
            user_exists = self.db.query(TelegramUser.id).filter_by(telegram_user_id=user_id).first()
            if user_exists:
                telegram_user_id = user_exists[0]  # 获取ID值
                self.logger.debug(f"找到有效的用户记录: telegram_user_id={user_id}, user_id={telegram_user_id}")
            else:
                self.logger.debug(f"未找到用户记录: telegram_user_id={user_id}")

        except Exception as e:
            self.logger.error(f"验证外键时发生错误: {e}", exc_info=True)
            # 确保数据库会话状态正常
            try:
                self.db.rollback()
            except Exception:
                pass
            # 发生错误时返回None，确保日志记录不会因外键问题失败

        self.logger.debug(f"外键验证结果: group_id={group_id}, telegram_user_id={telegram_user_id}")
        return group_id, telegram_user_id
    
    async def send_response(self, update: Update, text: str, context=None, **kwargs):
        """发送响应消息"""
        try:
            # 验证消息长度
            max_length = 4096  # Telegram消息最大长度
            if len(text) > max_length:
                self.logger.warning(f"消息长度超限 ({len(text)} > {max_length})，将截断")
                text = text[:max_length-50] + "\n\n... (消息过长，已截断)"

            # 检查是否是回调查询
            if update.callback_query:
                # 对于回调查询，发送新消息到聊天
                if context and hasattr(context, 'bot'):
                    await context.bot.send_message(
                        chat_id=update.effective_chat.id,
                        text=text,
                        **kwargs
                    )
                else:
                    # 如果没有 context，尝试编辑原消息
                    try:
                        await update.callback_query.edit_message_text(text, **kwargs)
                    except Exception:
                        # 如果编辑失败，发送新消息
                        await update.callback_query.message.reply_text(text, **kwargs)
            else:
                # 普通消息回复
                if update.message:
                    await update.message.reply_text(text, **kwargs)
                else:
                    self.logger.error("无法发送消息：update.message 为 None")

        except Exception as e:
            self.logger.error(f"发送消息失败: {e}")

            # 如果是解析错误，尝试发送纯文本版本
            if "parse entities" in str(e).lower() or "can't parse" in str(e).lower():
                self.logger.info("检测到解析错误，尝试发送纯文本版本")
                try:
                    # 移除所有格式化，发送纯文本
                    plain_text = self._strip_formatting(text)
                    if update.message:
                        await update.message.reply_text(plain_text)
                    elif update.callback_query:
                        if context and hasattr(context, 'bot'):
                            await context.bot.send_message(
                                chat_id=update.effective_chat.id,
                                text=plain_text
                            )
                except Exception as fallback_error:
                    self.logger.error(f"纯文本发送也失败: {fallback_error}")
                    # 最后的备用方案
                    try:
                        error_msg = "❌ 消息发送失败，请稍后再试"
                        if update.message:
                            await update.message.reply_text(error_msg)
                    except:
                        pass  # 如果连错误消息都发送不了，就放弃

    def _strip_formatting(self, text: str) -> str:
        """移除所有格式化标记，返回纯文本"""
        import re

        # 移除HTML标签
        text = re.sub(r'<[^>]+>', '', text)

        # 移除Markdown格式
        text = re.sub(r'\*\*([^*]+)\*\*', r'\1', text)  # 粗体
        text = re.sub(r'\*([^*]+)\*', r'\1', text)      # 斜体
        text = re.sub(r'`([^`]+)`', r'\1', text)        # 代码
        text = re.sub(r'_([^_]+)_', r'\1', text)        # 下划线

        # 移除转义字符
        text = re.sub(r'\\(.)', r'\1', text)

        return text
    
    async def send_error_response(self, update: Update, error_message: str):
        """发送错误响应"""
        await self.send_response(update, error_message)

    def get_user_display_name(self, update: Update) -> str:
        """获取用户显示名称"""
        try:
            user = update.effective_user
            if user:
                if user.first_name and user.last_name:
                    return f"{user.first_name} {user.last_name}"
                elif user.first_name:
                    return user.first_name
                elif user.username:
                    return f"@{user.username}"
                else:
                    return f"用户{user.id}"
            return "未知用户"
        except Exception:
            return "未知用户"
    
    async def log_success(self, chat_id: int, user_id: int, command: str, execution_time: int, result: Any = None):
        """记录成功日志"""
        if not self.config.enable_audit_log:
            return

        try:
            # 验证并获取有效的外键ID
            group_id, telegram_user_id = await self._validate_and_get_foreign_keys(chat_id, user_id)

            response_data = result if isinstance(result, dict) else {"success": True}
            if self.config.mask_sensitive_data and response_data:
                response_data = self._mask_sensitive_data(response_data)

            TelegramBotLog.log_success(
                self.db, chat_id, user_id, command, execution_time,
                group_id=group_id,
                telegram_user_id=telegram_user_id,
                response_data=response_data
            )
        except Exception as e:
            self.logger.error(f"记录成功日志失败: {e}")
    
    async def log_error(self, chat_id: int, user_id: int, command: str, error_message: str, execution_time: int):
        """记录错误日志"""
        if not self.config.enable_audit_log:
            return

        try:
            # 确保数据库会话状态正常
            if self.db.in_transaction() and self.db.is_active:
                try:
                    # 验证并获取有效的外键ID
                    group_id, telegram_user_id = await self._validate_and_get_foreign_keys(chat_id, user_id)

                    TelegramBotLog.log_error(
                        self.db, chat_id, user_id, command, error_message, execution_time,
                        group_id=group_id,
                        telegram_user_id=telegram_user_id
                    )
                except Exception as db_error:
                    # 如果数据库操作失败，回滚并重试
                    self.db.rollback()
                    self.logger.warning(f"数据库操作失败，已回滚: {db_error}")

                    # 重试记录日志，不使用外键关联
                    TelegramBotLog.log_error(
                        self.db, chat_id, user_id, command, error_message, execution_time,
                        group_id=None,
                        telegram_user_id=None
                    )
            else:
                # 会话状态异常，直接记录基本日志
                TelegramBotLog.log_error(
                    self.db, chat_id, user_id, command, error_message, execution_time,
                    group_id=None,
                    telegram_user_id=None
                )
        except Exception as e:
            self.logger.error(f"记录错误日志失败: {e}")
            # 确保会话状态正常
            try:
                self.db.rollback()
            except:
                pass
    
    async def log_permission_denied(self, chat_id: int, user_id: int, command: str, error_message: str):
        """记录权限拒绝日志"""
        if not self.config.enable_audit_log:
            return

        try:
            # 确保数据库会话状态正常
            if self.db.in_transaction() and self.db.is_active:
                try:
                    # 验证并获取有效的外键ID
                    group_id, telegram_user_id = await self._validate_and_get_foreign_keys(chat_id, user_id)

                    TelegramBotLog.log_permission_denied(
                        self.db, chat_id, user_id, command, error_message,
                        group_id=group_id,
                        telegram_user_id=telegram_user_id
                    )
                except Exception as db_error:
                    # 如果数据库操作失败，回滚并重试
                    self.db.rollback()
                    self.logger.warning(f"数据库操作失败，已回滚: {db_error}")

                    # 重试记录日志，不使用外键关联
                    TelegramBotLog.log_permission_denied(
                        self.db, chat_id, user_id, command, error_message,
                        group_id=None,
                        telegram_user_id=None
                    )
            else:
                # 会话状态异常，直接记录基本日志
                TelegramBotLog.log_permission_denied(
                    self.db, chat_id, user_id, command, error_message,
                    group_id=None,
                    telegram_user_id=None
                )
        except Exception as e:
            self.logger.error(f"记录权限拒绝日志失败: {e}")
            # 确保会话状态正常
            try:
                self.db.rollback()
            except:
                pass
    
    def _mask_sensitive_data(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """脱敏敏感数据"""
        if not isinstance(data, dict):
            return data
        
        masked_data = data.copy()
        sensitive_keys = ['token', 'password', 'secret', 'key', 'card_number', 'phone']
        
        for key, value in masked_data.items():
            if any(sensitive_key in key.lower() for sensitive_key in sensitive_keys):
                if isinstance(value, str) and len(value) > 4:
                    masked_data[key] = value[:2] + '*' * (len(value) - 4) + value[-2:]
                else:
                    masked_data[key] = '***'
            elif isinstance(value, dict):
                masked_data[key] = self._mask_sensitive_data(value)
        
        return masked_data
    
    def get_user_display_name(self, update: Update) -> str:
        """获取用户显示名称"""
        user = update.effective_user
        if user.first_name and user.last_name:
            return f"{user.first_name} {user.last_name}"
        elif user.first_name:
            return user.first_name
        elif user.username:
            return f"@{user.username}"
        else:
            return f"User{user.id}"
    
    def format_amount(self, amount_fen: int) -> str:
        """格式化金额（分转元）"""
        return f"¥{amount_fen / 100:,.2f}"
    
    def format_percentage(self, value: float) -> str:
        """格式化百分比"""
        return f"{value:.1f}%"
