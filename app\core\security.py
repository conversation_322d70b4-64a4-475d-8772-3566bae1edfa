from datetime import datetime, timedelta
from typing import Any, Optional, Union
from passlib.context import CryptContext
import secrets
import hashlib
import hmac
import base64
import json
import time
import asyncio
from app.core.config import settings
from app.core.logging import get_logger
from jose import jwt
from cryptography.fernet import Fernet
from fastapi import Depends, HTTPException, status
from fastapi.security import OAuth2PasswordBearer

from app.core.redis import get_redis


logger = get_logger("security")


# 密码哈希工具
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

# JWT相关配置
# ALGORITHM 现在从 settings 中获取

# 从 JWT SECRET_KEY 派生 Fernet 加密密钥
try:
    # 使用 SHA-256 哈希 SECRET_KEY
    hashed_key = hashlib.sha256(settings.SECRET_KEY.encode()).digest()
    # Base64 编码哈希值以满足 Fernet 要求
    derived_fernet_key = base64.urlsafe_b64encode(hashed_key)
    fernet = Fernet(derived_fernet_key)
except Exception as e:
    logger.error(f"无法从 SECRET_KEY 派生 Fernet 密钥: {e}", exc_info=True)
    raise ValueError("无法初始化加密服务")

# OAuth2密码承载令牌URL
oauth2_scheme = OAuth2PasswordBearer(tokenUrl="token")


def create_access_token(
    subject: Union[str, Any], expires_delta: Optional[timedelta] = None
) -> str:
    """创建访问令牌

    Args:
        subject: 令牌主题，通常是用户ID
        expires_delta: 过期时间

    Returns:
        str: JWT令牌
    """
    if expires_delta:
        expire = datetime.utcnow() + expires_delta
    else:
        expire = datetime.utcnow() + timedelta(
            minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES
        )

    to_encode = {"exp": expire, "sub": str(subject), "iat": datetime.utcnow()}
    encoded_jwt = jwt.encode(to_encode, settings.SECRET_KEY, algorithm=settings.ALGORITHM)
    return encoded_jwt


def verify_password(plain_password: str, hashed_password: str) -> bool:
    """验证密码

    Args:
        plain_password: 明文密码
        hashed_password: 哈希密码

    Returns:
        bool: 验证是否通过
    """
    return pwd_context.verify(plain_password, hashed_password)


def get_password_hash(password: str) -> str:
    """获取密码哈希

    Args:
        password: 明文密码

    Returns:
        str: 哈希密码
    """
    return pwd_context.hash(password)


def generate_api_key() -> str:
    """生成API密钥

    Returns:
        str: API密钥
    """
    return secrets.token_urlsafe(32)


def generate_api_secret() -> str:
    """生成API密钥对应的密文

    Returns:
        str: API密钥密文
    """
    return secrets.token_urlsafe(64)


def generate_sign(data: dict, secret_key: str) -> str:
    """生成API签名

    Args:
        data: 请求数据
        secret_key: 密钥

    Returns:
        str: 签名
    """
    # 按键排序
    sorted_data = {k: data[k] for k in sorted(data.keys())}

    # 转为JSON字符串
    data_str = json.dumps(sorted_data, separators=(",", ":"))

    # 使用HMAC-SHA256计算签名
    signature = hmac.new(
        secret_key.encode(), data_str.encode(), digestmod=hashlib.sha256
    ).digest()

    # Base64编码
    return base64.b64encode(signature).decode()


class ApiSignatureValidator:
    """API签名验证器"""

    def __init__(self):
        self.timestamp_tolerance_past = 300  # 5分钟
        self.timestamp_tolerance_future = 60  # 1分钟
        self.nonce_expire_seconds = 360  # 6分钟

    async def validate(
        self,
        data: dict,
        secret_key: str,
        signature: str,
        timestamp: str,
        nonce: str,
        method: str = "POST",
        path: str = "/api/v1/card-bind",
    ) -> bool:
        """验证API请求签名"""
        # 验证时间戳
        if not self._validate_timestamp(timestamp):
            return False

        # 验证nonce
        if not await self._validate_nonce(nonce, timestamp):
            return False

        # 验证签名
        return self._validate_signature(data, secret_key, signature, timestamp, nonce, method, path)

    def _validate_timestamp(self, timestamp: str) -> bool:
        """验证时间戳"""
        try:
            request_time = int(timestamp) / 1000  # 毫秒转秒
            # 修复时区问题：使用上海时区时间
            from app.utils.time_utils import get_current_time
            current_time = get_current_time().timestamp()

            # 检查时间戳是否在有效范围内
            if (current_time - request_time) > self.timestamp_tolerance_past:
                logger.error(f"Timestamp too old: Server={current_time}, Client={request_time}")
                return False

            if (request_time - current_time) > self.timestamp_tolerance_future:
                logger.error(f"Timestamp too far in future: Server={current_time}, Client={request_time}")
                return False

            return True
        except ValueError:
            logger.error("Invalid timestamp format")
            return False

    async def _validate_nonce(self, nonce: str, timestamp: str) -> bool:
        """验证nonce"""
        try:
            redis_client = await get_redis()
            nonce_key = f"nonce:{nonce}"

            # 使用 setnx 来原子性地检查和设置 nonce
            is_new_nonce = await redis_client.setnx(nonce_key, timestamp)

            if not is_new_nonce:
                logger.error(f"Nonce already used: {nonce}")
                return False

            # 设置 nonce 的过期时间
            await redis_client.expire(nonce_key, self.nonce_expire_seconds)
            logger.info(f"Nonce validated and stored: {nonce}")
            return True

        except Exception as e:
            logger.error(f"Redis error during nonce validation: {e}")
            # 宽松模式：Redis 故障时跳过检查
            return True

    def _validate_signature(
        self,
        data: dict,
        secret_key: str,
        signature: str,
        timestamp: str,
        nonce: str,
        method: str,
        path: str,
    ) -> bool:
        """验证签名"""
        # 规范化请求数据
        normalized_data = self._normalize_data(data)

        # 创建规范化的JSON字符串（与前端JavaScript保持一致，不转义Unicode）
        json_str = json.dumps(normalized_data, separators=(",", ":"), sort_keys=True, ensure_ascii=False)

        # 构建签名字符串
        signature_components = [method.upper(), path, timestamp, nonce, json_str, secret_key]
        sign_str = "|".join(signature_components)

        # 计算期望的签名
        expected_signature = self._compute_signature(sign_str, secret_key)

        # 比较签名
        is_valid = hmac.compare_digest(expected_signature, signature)
        if not is_valid:
            logger.error(
                f"签名验证失败详细信息:\n"
                f"原始数据: {data}\n"
                f"规范化数据: {normalized_data}\n"
                f"JSON字符串: {json_str}\n"
                f"JSON字符串长度: {len(json_str)}\n"
                f"签名字符串: {sign_str}\n"
                f"签名字符串长度: {len(sign_str)}\n"
                f"期望签名: {expected_signature}\n"
                f"实际签名: {signature}\n"
                f"时间戳: {timestamp}\n"
                f"随机数: {nonce}\n"
                f"方法: {method}\n"
                f"路径: {path}\n"
                f"密钥长度: {len(secret_key)}"
            )

        return is_valid

    def _normalize_data(self, data: dict) -> dict:
        """规范化请求数据，排除不参与签名校验的字段"""
        # 定义不参与签名校验的字段
        excluded_fields = {'debug'}

        normalized_data = {}
        for key in sorted(data.keys()):
            # 跳过排除的字段
            if key in excluded_fields:
                continue
            if data[key] is not None and data[key] != "":
                normalized_data[key] = data[key]
        return normalized_data

    def _compute_signature(self, sign_str: str, secret_key: str) -> str:
        """计算签名"""
        hmac_obj = hmac.new(
            key=secret_key.encode("utf-8"),
            msg=sign_str.encode("utf-8"),
            digestmod=hashlib.sha256,
        )
        return base64.b64encode(hmac_obj.digest()).decode("utf-8")


# 创建全局验证器实例
_signature_validator = ApiSignatureValidator()


async def verify_api_sign(  # Make the function async
    data: dict,
    secret_key: str,
    signature: str,
    timestamp: str,
    nonce: str,
    method: str = "POST",
    path: str = "/api/v1/card-bind",
) -> bool:
    """
    验证API请求签名 (异步)

    Args:
        data: 请求数据
        secret_key: API密钥
        signature: 请求签名
        timestamp: 时间戳
        nonce: 随机字符串
        method: HTTP请求方法，默认为POST
        path: 请求路径，默认为/api/v1/card-bind

    Returns:
        bool: 签名是否有效
    """
    return await _signature_validator.validate(
        data, secret_key, signature, timestamp, nonce, method, path
    )


def create_refresh_token(subject: str) -> str:
    """创建刷新令牌"""
    expire = datetime.utcnow() + timedelta(days=settings.REFRESH_TOKEN_EXPIRE_DAYS)
    to_encode = {"exp": expire, "sub": str(subject)}
    encoded_jwt = jwt.encode(
        to_encode, settings.SECRET_KEY, algorithm=settings.ALGORITHM
    )
    return encoded_jwt


def encrypt_sensitive_data(data: str) -> str:
    """加密敏感数据

    Args:
        data: 需要加密的数据

    Returns:
        str: 加密后的数据
    """
    return fernet.encrypt(data.encode()).decode()


def decrypt_sensitive_data(encrypted_data: str) -> str:
    """解密敏感数据

    Args:
        encrypted_data: 需要解密的数据

    Returns:
        str: 解密后的数据
    """
    return fernet.decrypt(encrypted_data.encode()).decode()


def get_current_user_id(token: str = Depends(oauth2_scheme)) -> int:
    """从令牌中获取当前用户ID

    Args:
        token: JWT令牌

    Returns:
        int: 用户ID

    Raises:
        HTTPException: 令牌无效或过期
    """
    try:
        payload = jwt.decode(token, settings.SECRET_KEY, algorithms=[settings.ALGORITHM])
        user_id = int(payload.get("sub"))
        if user_id is None:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Could not validate credentials",
            )
        return user_id
    except (jwt.JWTError, ValueError):
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Could not validate credentials",
        )


async def get_current_user_with_permission(
    token: str = Depends(oauth2_scheme),
    required_permission: Optional[str] = None,
    required_permissions: Optional[list[str]] = None,
) -> Any:
    """
    获取当前用户并检查权限

    Args:
        token: JWT令牌
        required_permission: 单个所需权限
        required_permissions: 多个所需权限

    Returns:
        Any: 用户对象

    Raises:
        HTTPException: 如果用户未认证或没有所需权限
    """
    # 验证令牌并获取用户ID
    user_id = _validate_token_and_get_user_id(token)

    # 获取并验证用户
    user = await _get_and_validate_user(user_id)

    # 验证商家状态
    await _validate_merchant_status(user)

    # 检查权限
    _check_user_permissions(user, required_permission, required_permissions)

    return user


def _validate_token_and_get_user_id(token: str) -> int:
    """验证令牌并获取用户ID"""
    try:
        payload = jwt.decode(token, settings.SECRET_KEY, algorithms=[settings.ALGORITHM])
        user_id = int(payload.get("sub"))
        if not user_id:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="无效的认证凭据",
            )
        return user_id
    except jwt.JWTError:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="无效的认证凭据",
        )


async def _get_and_validate_user(user_id: int):
    """获取并验证用户"""
    from app.crud.user import user_crud

    user = await user_crud.get(id=user_id)
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="用户不存在",
        )

    if not user.is_active:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="用户已被禁用",
        )

    return user


async def _validate_merchant_status(user):
    """验证商家状态"""
    if user.merchant_id and not user.is_superuser:
        from app.crud.merchant import merchant_crud
        merchant = await merchant_crud.get(id=user.merchant_id)
        if not merchant:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="用户所属商家不存在",
            )
        if not merchant.status:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="所属商家已被禁用",
            )


def _check_user_permissions(user, required_permission: Optional[str], required_permissions: Optional[list[str]]):
    """检查用户权限"""
    from app.services.permissions import check_permission, check_permissions

    if required_permission:
        check_permission(user, required_permission)
    elif required_permissions:
        check_permissions(user, required_permissions)
