from sqlalchemy import Column, String, BigInteger, DateTime, ForeignKey, JSON
from sqlalchemy.orm import relationship
from enum import Enum as PyEnum

from app.models.base import BaseModel, TimestampMixin


class VerificationStatus(PyEnum):
    """验证状态枚举"""
    PENDING = "pending"
    VERIFIED = "verified"
    EXPIRED = "expired"


class TelegramUser(BaseModel, TimestampMixin):
    """Telegram用户关联表"""

    __tablename__ = "telegram_users"

    # Telegram用户信息
    telegram_user_id = Column(
        BigInteger, 
        nullable=False, 
        unique=True,
        index=True,
        comment="Telegram用户ID"
    )
    telegram_username = Column(
        String(255), 
        nullable=True, 
        comment="Telegram用户名"
    )
    telegram_first_name = Column(
        String(255), 
        nullable=True, 
        comment="Telegram名字"
    )
    telegram_last_name = Column(
        String(255), 
        nullable=True, 
        comment="Telegram姓氏"
    )

    # 系统关联
    system_user_id = Column(
        BigInteger, 
        ForeignKey("users.id", ondelete="SET NULL"), 
        nullable=True,
        index=True,
        comment="关联的系统用户ID"
    )

    # 验证相关
    verification_token = Column(
        String(64), 
        nullable=True,
        index=True,
        comment="验证令牌"
    )
    verification_status = Column(
        String(20),
        nullable=False,
        default=VerificationStatus.PENDING.value,
        index=True,
        comment="验证状态"
    )
    verification_time = Column(
        DateTime(timezone=True), 
        nullable=True, 
        comment="验证完成时间"
    )

    # 活跃状态
    last_active_time = Column(
        DateTime(timezone=True), 
        nullable=True, 
        comment="最后活跃时间"
    )

    # 个人设置
    settings = Column(
        JSON, 
        nullable=True, 
        comment="用户个人设置"
    )

    # 关联关系
    system_user = relationship(
        "User", 
        foreign_keys=[system_user_id]
    )

    # 操作日志关联
    bot_logs = relationship(
        "TelegramBotLog", 
        back_populates="telegram_user",
        cascade="all, delete-orphan"
    )

    def to_dict(self, include_token=False):
        """转换为字典"""
        data = {
            "id": self.id,
            "telegram_user_id": self.telegram_user_id,
            "telegram_username": self.telegram_username,
            "telegram_first_name": self.telegram_first_name,
            "telegram_last_name": self.telegram_last_name,
            "telegram_full_name": self.get_full_name(),
            "system_user_id": self.system_user_id,
            "system_username": self.system_user.username if self.system_user else None,
            "verification_status": self.verification_status,
            "verification_time": self.verification_time.isoformat() if self.verification_time else None,
            "last_active_time": self.last_active_time.isoformat() if self.last_active_time else None,
            "settings": self.settings,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None,
        }

        # 只有在明确要求时才包含verification_token（用于管理员界面）
        if include_token:
            data["verification_token"] = self.verification_token

        return data

    def get_full_name(self):
        """获取完整姓名"""
        names = []
        if self.telegram_first_name:
            names.append(self.telegram_first_name)
        if self.telegram_last_name:
            names.append(self.telegram_last_name)
        return " ".join(names) if names else self.telegram_username or f"User{self.telegram_user_id}"

    def is_verified(self):
        """检查是否已验证"""
        return self.verification_status == VerificationStatus.VERIFIED.value and self.system_user_id is not None

    def can_access_group(self, group):
        """检查是否可以访问指定群组"""
        if not self.is_verified():
            return False
        
        if not self.system_user:
            return False
            
        # 检查商户权限
        if not self.system_user.can_access_merchant_data(group.merchant_id):
            return False
            
        # 检查部门权限（如果群组绑定了部门）
        if group.department_id:
            return self.system_user.can_access_department_data_new(group.department_id, group.merchant_id)
            
        return True

    def update_last_active(self):
        """更新最后活跃时间"""
        from app.models.base import local_now
        self.last_active_time = local_now()

    def get_default_settings(self):
        """获取默认设置"""
        return {
            "language": "zh-CN",
            "timezone": "Asia/Shanghai",
            "notification_enabled": True,
            "auto_delete_commands": False,
            "show_detailed_stats": False,
            "preferred_date_format": "YYYY-MM-DD",
            "preferred_time_format": "24h"
        }

    def merge_settings(self, new_settings):
        """合并设置"""
        current_settings = self.settings or self.get_default_settings()
        current_settings.update(new_settings)
        self.settings = current_settings
        return self.settings

    def generate_verification_token(self):
        """生成验证令牌"""
        import secrets
        import string
        alphabet = string.ascii_letters + string.digits
        self.verification_token = ''.join(secrets.choice(alphabet) for _ in range(32))
        self.verification_status = VerificationStatus.PENDING.value
        return self.verification_token

    def verify_with_system_user(self, system_user_id):
        """与系统用户关联并验证"""
        from app.models.base import local_now
        self.system_user_id = system_user_id
        self.verification_status = VerificationStatus.VERIFIED.value
        self.verification_time = local_now()
        self.verification_token = None  # 清除验证令牌

    def expire_verification(self):
        """使验证过期"""
        self.verification_status = VerificationStatus.EXPIRED.value
        self.verification_token = None

    def get_display_name(self):
        """获取用户显示名称"""
        if self.telegram_first_name and self.telegram_last_name:
            return f"{self.telegram_first_name} {self.telegram_last_name}"
        elif self.telegram_first_name:
            return self.telegram_first_name
        elif self.telegram_username:
            return f"@{self.telegram_username}"
        else:
            return f"用户{self.telegram_user_id}"

    def has_permission(self, permission_code):
        """检查用户是否有特定权限"""
        if not self.is_verified() or not self.system_user:
            return False
        return self.system_user.has_permission(permission_code)

    def get_accessible_merchants(self):
        """获取可访问的商户列表"""
        if not self.is_verified() or not self.system_user:
            return []
        return self.system_user.get_accessible_merchant_ids()

    def get_accessible_departments(self):
        """获取可访问的部门列表"""
        if not self.is_verified() or not self.system_user:
            return []
        return self.system_user.get_accessible_departments()
