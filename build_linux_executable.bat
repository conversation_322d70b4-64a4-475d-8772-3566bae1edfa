@echo off
setlocal

REM --- 配置 ---
set PROJECT_DIR=%~dp0
set DOCKERFILE=Dockerfile.build
set IMAGE_NAME=walmart-builder-image
set CONTAINER_NAME=walmart-builder-container
set OUTPUT_HOST_DIR=%PROJECT_DIR%dist_linux
set EXECUTABLE_NAME=walmart-bind-card-server-linux
REM --- 配置结束 ---

echo ================================================
echo  开始使用 Docker 构建 Linux 可执行文件
echo ================================================
echo.

REM 检查 Docker 是否正在运行
docker info > nul 2>&1
if errorlevel 1 (
    echo [错误] Docker Desktop 未运行或未配置为使用 Linux 容器。
    echo 请启动 Docker Desktop 并确保其在 Linux 容器模式下运行。
    goto :eof
) else (
    echo [信息] Docker 正在运行。
)
echo.

REM 切换到项目目录 (如果脚本不在项目根目录)
echo [信息] 切换到项目目录: %PROJECT_DIR%
cd /d "%PROJECT_DIR%"
if errorlevel 1 (
    echo [错误] 无法切换到项目目录: %PROJECT_DIR%
    goto :eof
)
echo.

REM 构建 Docker 镜像
echo [步骤 1/4] 构建 Docker 构建镜像 (%IMAGE_NAME%)...
docker build -f %DOCKERFILE% -t %IMAGE_NAME% .
if errorlevel 1 (
    echo [错误] Docker 镜像构建失败。请检查 %DOCKERFILE% 和 Docker 输出。
    goto :eof
)
echo [成功] Docker 镜像构建完成。
echo.

REM 创建输出目录
echo [步骤 2/4] 准备本地输出目录 (%OUTPUT_HOST_DIR%)...
if not exist "%OUTPUT_HOST_DIR%" (
    mkdir "%OUTPUT_HOST_DIR%"
    if errorlevel 1 (
        echo [错误] 无法创建输出目录: %OUTPUT_HOST_DIR%
        goto :eof
    )
    echo [信息] 输出目录已创建。
) else (
    echo [信息] 输出目录已存在。
)
echo.

REM 运行容器执行 PyInstaller (如果容器已存在则先删除)
echo [步骤 3/4] 运行 Docker 容器执行 PyInstaller 构建...
docker rm %CONTAINER_NAME% > nul 2>&1
docker run --name %CONTAINER_NAME% %IMAGE_NAME%
if errorlevel 1 (
    echo [错误] 运行 Docker 容器进行构建失败。请检查 Docker 输出。
    goto :eof
)
echo [成功] PyInstaller 构建在容器内完成。
echo.

REM 从容器复制构建好的可执行文件
echo [步骤 4/4] 从容器复制可执行文件到 %OUTPUT_HOST_DIR%...
docker cp "%CONTAINER_NAME%:/app/dist/%EXECUTABLE_NAME%" "%OUTPUT_HOST_DIR%/"
if errorlevel 1 (
    echo [错误] 无法从容器复制可执行文件。容器名: %CONTAINER_NAME%
    docker rm %CONTAINER_NAME% > nul 2>&1
    goto :eof
)
echo [成功] 可执行文件已复制到: %OUTPUT_HOST_DIR%\%EXECUTABLE_NAME%
echo.

REM 清理构建容器
echo [清理] 删除临时构建容器 (%CONTAINER_NAME%)...
docker rm %CONTAINER_NAME% > nul 2>&1
if errorlevel 1 (
    echo [警告] 未能删除构建容器 %CONTAINER_NAME%。您可以稍后手动删除。
) else (
    echo [信息] 临时构建容器已删除。
)
echo.

REM 可选：清理构建镜像
REM set /p cleanup_image="是否删除构建镜像 %IMAGE_NAME% (y/n)? "
REM if /i "%cleanup_image%"=="y" (
REM     echo [清理] 删除构建镜像 (%IMAGE_NAME%)...
REM     docker rmi %IMAGE_NAME%
REM )

echo ================================================
echo  构建过程完成!
echo ================================================
echo Linux 可执行文件位于: %OUTPUT_HOST_DIR%\%EXECUTABLE_NAME%
echo 请注意：此文件需要在与构建环境兼容的 Linux 系统上运行。

:eof
endlocal
pause
