"""
时间处理工具修复测试
验证时区感知datetime对象比较问题的修复
"""

import pytest
from datetime import datetime, timedelta
from zoneinfo import ZoneInfo

from app.utils.time_utils import (
    get_current_time,
    ensure_timezone,
    safe_datetime_compare,
    is_expired,
    TARGET_TZ
)


class TestTimeUtilsFix:
    """时间处理工具修复测试类"""
    
    def test_ensure_timezone_naive_datetime(self):
        """测试为无时区信息的datetime添加时区"""
        # 创建无时区信息的datetime
        naive_dt = datetime(2025, 1, 10, 15, 30, 0)
        
        # 添加时区信息
        aware_dt = ensure_timezone(naive_dt)
        
        # 验证结果
        assert aware_dt.tzinfo is not None
        assert aware_dt.tzinfo == TARGET_TZ
        assert aware_dt.year == 2025
        assert aware_dt.month == 1
        assert aware_dt.day == 10
        assert aware_dt.hour == 15
        assert aware_dt.minute == 30
        assert aware_dt.second == 0
    
    def test_ensure_timezone_aware_datetime(self):
        """测试已有时区信息的datetime保持不变"""
        # 创建有时区信息的datetime
        utc_tz = ZoneInfo("UTC")
        aware_dt = datetime(2025, 1, 10, 15, 30, 0, tzinfo=utc_tz)
        
        # 处理后应该保持原有时区
        result_dt = ensure_timezone(aware_dt)
        
        # 验证结果
        assert result_dt.tzinfo == utc_tz
        assert result_dt == aware_dt
    
    def test_safe_datetime_compare_both_naive(self):
        """测试两个无时区datetime的安全比较"""
        dt1 = datetime(2025, 1, 10, 16, 0, 0)  # 较晚时间
        dt2 = datetime(2025, 1, 10, 15, 0, 0)  # 较早时间
        
        # 安全比较
        result = safe_datetime_compare(dt1, dt2)
        
        # 验证结果
        assert result is True  # dt1 > dt2
    
    def test_safe_datetime_compare_both_aware(self):
        """测试两个有时区datetime的安全比较"""
        dt1 = datetime(2025, 1, 10, 16, 0, 0, tzinfo=TARGET_TZ)
        dt2 = datetime(2025, 1, 10, 15, 0, 0, tzinfo=TARGET_TZ)
        
        # 安全比较
        result = safe_datetime_compare(dt1, dt2)
        
        # 验证结果
        assert result is True  # dt1 > dt2
    
    def test_safe_datetime_compare_mixed(self):
        """测试混合时区datetime的安全比较"""
        # 一个有时区，一个无时区
        dt1 = datetime(2025, 1, 10, 16, 0, 0, tzinfo=TARGET_TZ)
        dt2 = datetime(2025, 1, 10, 15, 0, 0)  # 无时区
        
        # 安全比较
        result = safe_datetime_compare(dt1, dt2)
        
        # 验证结果
        assert result is True  # dt1 > dt2
    
    def test_is_expired_not_expired(self):
        """测试未过期的情况"""
        # 创建1小时前的时间
        created_time = get_current_time() - timedelta(hours=1)
        expire_duration = timedelta(hours=2)  # 2小时过期
        
        # 检查是否过期
        result = is_expired(created_time, expire_duration)
        
        # 验证结果
        assert result is False  # 未过期
    
    def test_is_expired_already_expired(self):
        """测试已过期的情况"""
        # 创建3小时前的时间
        created_time = get_current_time() - timedelta(hours=3)
        expire_duration = timedelta(hours=2)  # 2小时过期
        
        # 检查是否过期
        result = is_expired(created_time, expire_duration)
        
        # 验证结果
        assert result is True  # 已过期
    
    def test_is_expired_naive_datetime(self):
        """测试无时区datetime的过期检查"""
        # 创建无时区的过去时间
        naive_created_time = datetime.now() - timedelta(hours=3)
        expire_duration = timedelta(hours=2)
        
        # 检查是否过期
        result = is_expired(naive_created_time, expire_duration)
        
        # 验证结果
        assert result is True  # 已过期
    
    def test_is_expired_none_created_time(self):
        """测试创建时间为None的情况"""
        created_time = None
        expire_duration = timedelta(hours=2)
        
        # 检查是否过期
        result = is_expired(created_time, expire_duration)
        
        # 验证结果
        assert result is True  # 视为已过期
    
    def test_is_expired_edge_case_exactly_expired(self):
        """测试恰好过期的边界情况"""
        # 创建恰好过期的时间
        created_time = get_current_time() - timedelta(hours=2)
        expire_duration = timedelta(hours=2)
        
        # 检查是否过期（由于时间精度，可能有微小差异）
        result = is_expired(created_time, expire_duration)
        
        # 验证结果（应该是过期的，因为当前时间 > 过期时间）
        assert result is True
    
    def test_get_current_time_has_timezone(self):
        """测试获取当前时间包含时区信息"""
        current_time = get_current_time()
        
        # 验证结果
        assert current_time.tzinfo is not None
        assert current_time.tzinfo == TARGET_TZ
    
    def test_datetime_comparison_error_prevention(self):
        """测试防止datetime比较错误"""
        # 模拟数据库返回的可能无时区的datetime
        db_datetime = datetime(2025, 1, 10, 15, 0, 0)  # 无时区
        current_time = get_current_time()  # 有时区
        
        # 使用安全比较，不应该抛出异常
        try:
            result = safe_datetime_compare(current_time, db_datetime)
            # 如果没有异常，测试通过
            assert isinstance(result, bool)
        except TypeError:
            pytest.fail("safe_datetime_compare should not raise TypeError")
    
    def test_bind_token_expiry_simulation(self):
        """模拟绑定令牌过期检查"""
        # 模拟TelegramGroup的created_at字段（可能无时区）
        group_created_at = datetime(2025, 1, 10, 10, 0, 0)  # 无时区
        
        # 模拟配置的过期时间（24小时）
        expire_hours = 24
        expire_duration = timedelta(hours=expire_hours)
        
        # 使用修复后的is_expired函数
        is_token_expired = is_expired(group_created_at, expire_duration)
        
        # 验证不会抛出异常
        assert isinstance(is_token_expired, bool)
    
    def test_verification_token_expiry_simulation(self):
        """模拟验证令牌过期检查"""
        # 模拟TelegramUser的created_at字段（可能无时区）
        user_created_at = datetime(2025, 1, 10, 15, 0, 0)  # 无时区
        
        # 模拟配置的过期时间（30分钟）
        expire_minutes = 30
        expire_duration = timedelta(minutes=expire_minutes)
        
        # 使用修复后的is_expired函数
        is_token_expired = is_expired(user_created_at, expire_duration)
        
        # 验证不会抛出异常
        assert isinstance(is_token_expired, bool)


class TestTimeZoneConsistency:
    """时区一致性测试"""
    
    def test_all_time_functions_use_same_timezone(self):
        """测试所有时间函数使用相同的时区"""
        current_time = get_current_time()
        
        # 创建无时区datetime并添加时区
        naive_dt = datetime.now()
        aware_dt = ensure_timezone(naive_dt)
        
        # 验证时区一致性
        assert current_time.tzinfo == aware_dt.tzinfo == TARGET_TZ
    
    def test_database_datetime_compatibility(self):
        """测试数据库datetime兼容性"""
        # 模拟从数据库读取的各种datetime格式
        test_cases = [
            datetime(2025, 1, 10, 15, 0, 0),  # 无时区
            datetime(2025, 1, 10, 15, 0, 0, tzinfo=TARGET_TZ),  # 有时区
            datetime(2025, 1, 10, 15, 0, 0, tzinfo=ZoneInfo("UTC")),  # UTC时区
        ]
        
        current_time = get_current_time()
        
        for db_datetime in test_cases:
            # 确保所有情况都能安全比较
            try:
                result = safe_datetime_compare(current_time, db_datetime)
                assert isinstance(result, bool)
            except Exception as e:
                pytest.fail(f"Failed to compare {current_time} with {db_datetime}: {e}")


if __name__ == "__main__":
    # 运行测试
    pytest.main([__file__, "-v"])
