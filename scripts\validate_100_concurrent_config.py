#!/usr/bin/env python3
"""
验证配置是否能支持100个并发请求
"""
import sys
import os
from pathlib import Path
import yaml
from typing import Dict, Any, List, Tuple

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))


class ConcurrencyConfigValidator:
    """并发配置验证器"""
    
    def __init__(self, config_path: str = "config.yaml"):
        self.config_path = config_path
        self.config = self._load_config()
        self.issues = []
        self.recommendations = []
        self.warnings = []
    
    def _load_config(self) -> Dict[str, Any]:
        """加载配置文件"""
        try:
            with open(self.config_path, 'r', encoding='utf-8') as f:
                return yaml.safe_load(f)
        except FileNotFoundError:
            print(f"❌ 配置文件不存在: {self.config_path}")
            sys.exit(1)
        except yaml.YAMLError as e:
            print(f"❌ 配置文件格式错误: {e}")
            sys.exit(1)
    
    def validate_database_config(self) -> bool:
        """验证数据库配置"""
        print("🔍 验证数据库配置...")
        
        performance = self.config.get("performance", {})
        db_pool_size = performance.get("db_pool_size", 20)
        db_pool_max_overflow = performance.get("db_pool_max_overflow", 30)
        total_connections = db_pool_size + db_pool_max_overflow
        
        print(f"   数据库连接池大小: {db_pool_size}")
        print(f"   最大溢出连接数: {db_pool_max_overflow}")
        print(f"   总连接数: {total_connections}")
        
        if total_connections < 100:
            self.issues.append(f"数据库总连接数({total_connections})小于100，可能无法支持100并发")
            self.recommendations.append(f"建议将db_pool_size增加到60，db_pool_max_overflow增加到120")
            return False
        elif total_connections < 150:
            self.warnings.append(f"数据库总连接数({total_connections})刚好够用，建议增加到150以上")
        
        print("   ✅ 数据库配置满足100并发需求")
        return True
    
    def validate_rabbitmq_config(self) -> bool:
        """验证RabbitMQ配置"""
        print("🔍 验证RabbitMQ配置...")
        
        rabbitmq = self.config.get("rabbitmq", {})
        consumer_prefetch = rabbitmq.get("consumer_prefetch_count", 10)
        callback_prefetch = rabbitmq.get("callback_consumer_prefetch_count", 10)
        
        print(f"   绑卡队列预取数量: {consumer_prefetch}")
        print(f"   回调队列预取数量: {callback_prefetch}")
        
        if consumer_prefetch < 50:
            self.issues.append(f"绑卡队列预取数量({consumer_prefetch})过低，建议设置为50以上")
            return False
        
        if callback_prefetch < 100:
            self.issues.append(f"回调队列预取数量({callback_prefetch})过低，建议设置为100以上")
            return False
        
        print("   ✅ RabbitMQ配置满足100并发需求")
        return True
    
    def validate_http_client_config(self) -> bool:
        """验证HTTP客户端配置"""
        print("🔍 验证HTTP客户端配置...")
        
        performance = self.config.get("performance", {})
        http_client = performance.get("http_client", {})
        
        max_connections = http_client.get("max_connections", 50)
        max_keepalive = http_client.get("max_keepalive_connections", 20)
        timeout = http_client.get("timeout", 30)
        
        print(f"   HTTP最大连接数: {max_connections}")
        print(f"   HTTP保持连接数: {max_keepalive}")
        print(f"   HTTP超时时间: {timeout}秒")
        
        if max_connections < 100:
            self.issues.append(f"HTTP最大连接数({max_connections})过低，建议设置为200以上")
            return False
        
        if max_keepalive < 50:
            self.warnings.append(f"HTTP保持连接数({max_keepalive})较低，建议设置为100以上")
        
        if timeout > 15:
            self.warnings.append(f"HTTP超时时间({timeout}秒)较长，可能影响并发性能")
        
        print("   ✅ HTTP客户端配置基本满足需求")
        return True
    
    def validate_redis_config(self) -> bool:
        """验证Redis配置"""
        print("🔍 验证Redis配置...")
        
        performance = self.config.get("performance", {})
        redis_pool_size = performance.get("redis_pool_size", 10)
        redis_max_connections = performance.get("redis_max_connections", 50)
        
        print(f"   Redis连接池大小: {redis_pool_size}")
        print(f"   Redis最大连接数: {redis_max_connections}")
        
        if redis_pool_size < 20:
            self.warnings.append(f"Redis连接池大小({redis_pool_size})较小，建议增加到20以上")
        
        if redis_max_connections < 100:
            self.warnings.append(f"Redis最大连接数({redis_max_connections})较小，建议增加到100以上")
        
        print("   ✅ Redis配置基本满足需求")
        return True
    
    def validate_concurrency_limits(self) -> bool:
        """验证并发限制配置"""
        print("🔍 验证并发限制配置...")
        
        binding = self.config.get("binding", {})
        max_concurrency = binding.get("max_concurrency", 100)
        
        rate_limit = self.config.get("rate_limit", {})
        per_minute = rate_limit.get("per_minute", 60)
        
        print(f"   最大并发数: {max_concurrency}")
        print(f"   每分钟请求限制: {per_minute}")
        
        if max_concurrency < 100:
            self.issues.append(f"最大并发数({max_concurrency})小于100，无法支持100并发")
            return False
        
        if per_minute < 6000:  # 100并发 * 60秒
            self.warnings.append(f"每分钟请求限制({per_minute})可能限制100并发性能")
        
        print("   ✅ 并发限制配置满足需求")
        return True
    
    def validate_timeout_settings(self) -> bool:
        """验证超时设置"""
        print("🔍 验证超时设置...")
        
        binding = self.config.get("binding", {})
        walmart_api_timeout = binding.get("walmart_api_timeout", 30)
        callback_timeout = binding.get("callback_timeout", 10)
        
        performance = self.config.get("performance", {})
        db_pool_timeout = performance.get("db_pool_timeout", 30)
        
        print(f"   沃尔玛API超时: {walmart_api_timeout}秒")
        print(f"   回调超时: {callback_timeout}秒")
        print(f"   数据库连接超时: {db_pool_timeout}秒")
        
        if walmart_api_timeout > 30:
            self.warnings.append(f"沃尔玛API超时({walmart_api_timeout}秒)较长，可能影响并发性能")
        
        if callback_timeout > 15:
            self.warnings.append(f"回调超时({callback_timeout}秒)较长，可能影响回调性能")
        
        print("   ✅ 超时设置基本合理")
        return True
    
    def calculate_theoretical_capacity(self) -> Dict[str, int]:
        """计算理论并发容量"""
        print("📊 计算理论并发容量...")
        
        # 数据库容量
        performance = self.config.get("performance", {})
        db_capacity = performance.get("db_pool_size", 20) + performance.get("db_pool_max_overflow", 30)
        
        # RabbitMQ容量
        rabbitmq = self.config.get("rabbitmq", {})
        rabbitmq_capacity = rabbitmq.get("callback_consumer_prefetch_count", 10)
        
        # HTTP客户端容量
        http_capacity = performance.get("http_client", {}).get("max_connections", 50)
        
        # 系统配置容量
        binding = self.config.get("binding", {})
        system_capacity = binding.get("max_concurrency", 100)
        
        capacity = {
            "database": db_capacity,
            "rabbitmq": rabbitmq_capacity,
            "http_client": http_capacity,
            "system_limit": system_capacity,
            "bottleneck": min(db_capacity, rabbitmq_capacity, http_capacity, system_capacity)
        }
        
        print(f"   数据库容量: {capacity['database']}")
        print(f"   RabbitMQ容量: {capacity['rabbitmq']}")
        print(f"   HTTP客户端容量: {capacity['http_client']}")
        print(f"   系统限制: {capacity['system_limit']}")
        print(f"   瓶颈容量: {capacity['bottleneck']}")
        
        return capacity
    
    def run_validation(self) -> bool:
        """运行完整验证"""
        print("🚀 开始验证配置是否支持100并发...")
        print("=" * 60)
        
        validations = [
            self.validate_database_config,
            self.validate_rabbitmq_config,
            self.validate_http_client_config,
            self.validate_redis_config,
            self.validate_concurrency_limits,
            self.validate_timeout_settings,
        ]
        
        all_passed = True
        for validation in validations:
            if not validation():
                all_passed = False
            print()
        
        # 计算理论容量
        capacity = self.calculate_theoretical_capacity()
        print()
        
        # 输出结果
        print("=" * 60)
        print("📋 验证结果汇总:")
        print("=" * 60)
        
        if capacity["bottleneck"] >= 100:
            print("✅ 配置可以支持100并发请求")
        else:
            print(f"❌ 配置瓶颈容量为{capacity['bottleneck']}，无法支持100并发")
            all_passed = False
        
        if self.issues:
            print("\n🚨 严重问题:")
            for issue in self.issues:
                print(f"   - {issue}")
        
        if self.warnings:
            print("\n⚠️  警告:")
            for warning in self.warnings:
                print(f"   - {warning}")
        
        if self.recommendations:
            print("\n💡 建议:")
            for rec in self.recommendations:
                print(f"   - {rec}")
        
        print("\n" + "=" * 60)
        return all_passed and capacity["bottleneck"] >= 100


def main():
    """主函数"""
    validator = ConcurrencyConfigValidator()
    
    if validator.run_validation():
        print("🎉 配置验证通过！系统可以支持100并发请求。")
        sys.exit(0)
    else:
        print("❌ 配置验证失败！请根据建议调整配置。")
        sys.exit(1)


if __name__ == "__main__":
    main()
