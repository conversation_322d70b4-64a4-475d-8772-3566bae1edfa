<template>
  <div class="permission-management">
    <div class="search-section">
      <div class="search-form">
        <div class="form-item">
          <label>权限名称</label>
          <el-input
            v-model="searchForm.name"
            placeholder="请输入权限名称"
            clearable
            style="width: 200px"
          />
        </div>
        <div class="form-item">
          <label>权限编码</label>
          <el-input
            v-model="searchForm.code"
            placeholder="请输入权限编码"
            clearable
            style="width: 200px"
          />
        </div>
        <div class="form-item">
          <label>状态</label>
          <el-select
            v-model="searchForm.status"
            placeholder="状态"
            clearable
            style="width: 120px"
          >
            <el-option label="全部" value="" />
            <el-option label="启用" value="1" />
            <el-option label="禁用" value="0" />
          </el-select>
        </div>
        <div class="actions">
          <el-button type="primary" @click="handleSearch">
            <el-icon><Search /></el-icon>
            搜索
          </el-button>
          <el-button @click="handleReset">重置</el-button>
        </div>
        <div class="toolbar">
          <el-button type="primary" @click="handleAdd">
            <el-icon><Plus /></el-icon>
            新增权限
          </el-button>

        </div>
      </div>
    </div>

    <el-divider />

    <div class="table-section">
      <el-table
        ref="tableRef"
        :data="tableData"
        style="width: 100%"
        v-loading="loading"
      >
        <el-table-column prop="name" label="权限名称" width="200" />
        <el-table-column prop="code" label="权限编码" width="200" />
        <el-table-column prop="resource_type" label="资源类型" width="100">
          <template #default="{ row }">
            <el-tag :type="getTypeColor(row.resource_type)">
              {{ getTypeName(row.resource_type) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="resource_path" label="资源路径" width="200" />
        <el-table-column prop="description" label="描述" />
        <el-table-column prop="sort_order" label="排序" width="80" />
        <el-table-column prop="is_enabled" label="状态" width="80">
          <template #default="{ row }">
            <el-tag :type="row.is_enabled ? 'success' : 'danger'">
              {{ row.is_enabled ? '启用' : '禁用' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="created_at" label="创建时间" width="160" />
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <div class="action-buttons">
              <el-button type="primary" size="small" @click="handleEdit(row)">
                编辑
              </el-button>

              <el-button type="danger" size="small" @click="handleDelete(row)">
                删除
              </el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="pagination.current"
          v-model:page-size="pagination.pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          background
          @update:current-page="handlePageChange"
          @update:page-size="handleSizeChange"
        />
      </div>
    </div>

    <!-- 权限表单对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="dialogTitle"
      width="600px"
    >
      <el-form
        ref="formRef"
        :model="form"
        :rules="rules"
        label-width="120px"
      >
        <el-form-item label="权限名称" prop="name">
          <el-input v-model="form.name" placeholder="请输入权限名称" />
        </el-form-item>
        <el-form-item label="权限编码" prop="code">
          <el-input v-model="form.code" placeholder="请输入权限编码" />
        </el-form-item>

        <el-form-item label="资源类型" prop="resource_type">
          <el-select v-model="form.resource_type" placeholder="请选择资源类型">
            <el-option label="菜单权限" value="menu" />
            <el-option label="API权限" value="api" />
            <el-option label="数据权限" value="data" />
          </el-select>
        </el-form-item>
        <el-form-item label="资源路径" prop="resource_path">
          <el-input v-model="form.resource_path" placeholder="请输入资源路径" />
        </el-form-item>
        <el-form-item label="描述" prop="description">
          <el-input
            v-model="form.description"
            type="textarea"
            :rows="3"
            placeholder="请输入权限描述"
          />
        </el-form-item>
        <el-form-item label="排序" prop="sort_order">
          <el-input-number v-model="form.sort_order" :min="0" />
        </el-form-item>
        <el-form-item label="状态" prop="is_enabled">
          <el-radio-group v-model="form.is_enabled">
            <el-radio :value="true">启用</el-radio>
            <el-radio :value="false">禁用</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleSubmit">确定</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Search, Plus } from '@element-plus/icons-vue'
import { permissionApi } from '@/api/modules/permission'

// 响应式数据
const loading = ref(false)
const tableData = ref([])
const permissionTreeData = ref([])
const dialogVisible = ref(false)
const dialogTitle = ref('')
const formRef = ref()
const tableRef = ref()

// 分页数据
const pagination = reactive({
  current: 1,
  pageSize: 20,
  total: 0
})

// 搜索表单
const searchForm = reactive({
  name: '',
  code: '',
  resource_type: '',
  is_enabled: null
})

// 权限表单
const form = reactive({
  id: null,
  name: '',
  code: '',
  description: '',
  resource_type: 'api',
  resource_path: '',
  is_enabled: true,
  sort_order: 0
})

// 表单验证规则
const rules = {
  name: [
    { required: true, message: '请输入权限名称', trigger: 'blur' }
  ],
  code: [
    { required: true, message: '请输入权限编码', trigger: 'blur' }
  ]
}

// 方法
const getTypeName = (type) => {
  const typeMap = {
    'MENU': '菜单权限',
    'API': 'API权限',
    'DATA': '数据权限',
    'menu': '菜单权限',
    'api': 'API权限',
    'data': '数据权限'
  }
  return typeMap[type] || '未知'
}

const getTypeColor = (type) => {
  const colorMap = {
    'MENU': 'success',
    'API': 'primary',
    'DATA': 'warning',
    'menu': 'success',
    'api': 'primary',
    'data': 'warning'
  }
  return colorMap[type] || ''
}

const fetchData = async () => {
  loading.value = true
  try {
    // 构建查询参数，包含分页信息
    const params = {
      ...searchForm,
      page: pagination.current,
      page_size: pagination.pageSize
    }

    // 调用API获取权限列表
    const response = await permissionApi.getList(params)

    if (response && response.items) {
      tableData.value = response.items
      pagination.total = response.total || 0
      // 构建树形数据（权限通常是平铺的，不需要树形结构）
      permissionTreeData.value = response.items
    } else {
      tableData.value = []
      pagination.total = 0
      permissionTreeData.value = []
    }
  } catch (error) {
    console.error('获取权限列表失败:', error)
    ElMessage.error('获取权限列表失败: ' + (error.message || '未知错误'))
    tableData.value = []
    pagination.total = 0
    permissionTreeData.value = []
  } finally {
    loading.value = false
  }
}



const handleSearch = () => {
  pagination.current = 1
  fetchData()
}

const handleReset = () => {
  Object.assign(searchForm, {
    name: '',
    code: '',
    resource_type: '',
    is_enabled: null
  })
  handleSearch()
}

// 分页处理
const handlePageChange = (page) => {
  pagination.current = page
  fetchData()
}

const handleSizeChange = (size) => {
  pagination.pageSize = size
  pagination.current = 1
  fetchData()
}

const handleAdd = () => {
  dialogTitle.value = '新增权限'
  Object.assign(form, {
    id: null,
    name: '',
    code: '',
    description: '',
    resource_type: 'api',
    resource_path: '',
    is_enabled: true,
    sort_order: 0
  })
  dialogVisible.value = true
}

const handleEdit = (row) => {
  dialogTitle.value = '编辑权限'
  Object.assign(form, { ...row })
  dialogVisible.value = true
}

const handleDelete = async (row) => {
  try {
    await ElMessageBox.confirm('确定要删除该权限吗？', '提示', {
      type: 'warning'
    })

    // 调用API删除权限
    await permissionApi.delete(row.id)

    ElMessage.success('删除成功')
    fetchData()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除权限失败:', error)
      ElMessage.error('删除失败: ' + (error.message || '未知错误'))
    }
  }
}

const handleSubmit = async () => {
  try {
    await formRef.value.validate()

    // 调用API保存权限
    if (form.id) {
      await permissionApi.update(form.id, form)
    } else {
      await permissionApi.create(form)
    }

    ElMessage.success('保存成功')
    dialogVisible.value = false
    fetchData()
  } catch (error) {
    if (error !== false) {
      console.error('保存权限失败:', error)
      ElMessage.error('保存失败: ' + (error.message || '未知错误'))
    }
  }
}



// 生命周期
onMounted(() => {
  fetchData()
})
</script>

<style scoped>
.permission-management {
  padding: 20px;
}

.search-section {
  background: #fff;
  padding: 20px;
  border-radius: 8px;
  margin-bottom: 20px;
}

.search-form {
  display: flex;
  align-items: center;
  gap: 20px;
  flex-wrap: wrap;
}

.form-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.form-item label {
  white-space: nowrap;
  font-weight: 500;
}

.actions {
  display: flex;
  gap: 10px;
}

.toolbar {
  margin-left: auto;
  display: flex;
  gap: 10px;
}

.table-section {
  background: #fff;
  padding: 20px;
  border-radius: 8px;
}

.action-buttons {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.pagination-container {
  display: flex;
  justify-content: flex-end;
  margin-top: 20px;
}
</style>
