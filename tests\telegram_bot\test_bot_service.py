"""
测试Telegram Bot服务
"""

import pytest
from unittest.mock import Mock, AsyncMock, patch, MagicMock
import json
import hmac
import hashlib
from sqlalchemy.orm import Session
from telegram import Bot, Update
from telegram.ext import Application

from app.telegram_bot.services.bot_service import TelegramBotService
from app.telegram_bot.webhook_handler import WebhookHandler
from app.telegram_bot.config import BotConfig
from app.telegram_bot.exceptions import BotServiceError, ConfigurationError, WebhookError


@pytest.fixture
def mock_config():
    """创建模拟配置"""
    config = Mock(spec=BotConfig)
    config.bot_token = "123456:ABC-DEF1234ghIkl-zyx57W2v1u123ew11"
    config.webhook_url = "https://example.com/webhook"
    config.webhook_secret = "test_secret"
    config.validate.return_value = True
    return config


@pytest.fixture
def mock_application():
    """创建模拟Application"""
    app = Mock(spec=Application)
    app.bot = Mock(spec=Bot)
    app.initialize = AsyncMock()
    app.start = AsyncMock()
    app.stop = AsyncMock()
    app.shutdown = AsyncMock()
    app.add_handler = Mock()
    app.bot.set_webhook = AsyncMock()
    app.bot.delete_webhook = AsyncMock()
    return app


class TestTelegramBotService:
    """测试TelegramBotService"""

    @pytest.fixture
    def bot_service(self):
        """创建机器人服务实例"""
        return TelegramBotService()

    @pytest.mark.asyncio
    async def test_initialize_success(self, bot_service: TelegramBotService, db: Session):
        """测试成功初始化"""
        with patch('telegram_bot.bot_service.get_bot_config') as mock_get_config:
            with patch('telegram_bot.bot_service.Application.builder') as mock_builder:
                with patch('telegram_bot.bot_service.RateLimiter') as mock_rate_limiter:
                    with patch('telegram_bot.bot_service.CommandHandlerRegistry') as mock_registry:
                        with patch('telegram_bot.bot_service.WebhookHandler') as mock_webhook:
                            # 设置模拟
                            mock_config = Mock(spec=BotConfig)
                            mock_config.validate.return_value = True
                            mock_config.bot_token = "test_token"
                            mock_get_config.return_value = mock_config
                            
                            mock_app = Mock(spec=Application)
                            mock_builder.return_value.token.return_value.build.return_value = mock_app
                            
                            mock_command_registry = Mock()
                            mock_registry.return_value = mock_command_registry
                            
                            # 模拟注册处理器
                            with patch.object(bot_service, '_register_handlers', new_callable=AsyncMock):
                                result = await bot_service.initialize()
                                
                                assert result is True
                                assert bot_service.config == mock_config
                                assert bot_service.application == mock_app
                                assert bot_service.command_registry == mock_command_registry

    @pytest.mark.asyncio
    async def test_initialize_config_validation_failure(self, bot_service: TelegramBotService):
        """测试配置验证失败"""
        with patch('telegram_bot.bot_service.get_bot_config') as mock_get_config:
            mock_config = Mock(spec=BotConfig)
            mock_config.validate.return_value = False
            mock_get_config.return_value = mock_config
            
            result = await bot_service.initialize()
            
            assert result is False
            assert bot_service.config is None

    @pytest.mark.asyncio
    async def test_initialize_exception(self, bot_service: TelegramBotService):
        """测试初始化异常"""
        with patch('telegram_bot.bot_service.get_bot_config') as mock_get_config:
            mock_get_config.side_effect = Exception("配置加载失败")
            
            result = await bot_service.initialize()
            
            assert result is False

    @pytest.mark.asyncio
    async def test_register_handlers_success(self, bot_service: TelegramBotService):
        """测试成功注册处理器"""
        # 设置模拟对象
        mock_app = Mock(spec=Application)
        mock_registry = Mock()
        mock_handlers = [Mock(), Mock(), Mock()]
        mock_registry.get_all_handlers.return_value = mock_handlers
        
        bot_service.application = mock_app
        bot_service.command_registry = mock_registry
        
        await bot_service._register_handlers()
        
        # 验证所有处理器都被添加
        assert mock_app.add_handler.call_count == len(mock_handlers)
        for handler in mock_handlers:
            mock_app.add_handler.assert_any_call(handler)

    @pytest.mark.asyncio
    async def test_register_handlers_not_initialized(self, bot_service: TelegramBotService):
        """测试未初始化时注册处理器"""
        with pytest.raises(BotServiceError) as exc_info:
            await bot_service._register_handlers()
        
        assert "应用或命令注册器未初始化" in str(exc_info.value)

    @pytest.mark.asyncio
    async def test_start_success(self, bot_service: TelegramBotService):
        """测试成功启动服务"""
        # 设置模拟对象
        mock_app = Mock(spec=Application)
        mock_app.initialize = AsyncMock()
        mock_app.start = AsyncMock()
        
        mock_webhook = Mock()
        mock_webhook.setup_webhook = AsyncMock(return_value=True)
        
        bot_service.application = mock_app
        bot_service.webhook_handler = mock_webhook
        bot_service._running = False
        
        result = await bot_service.start()
        
        assert result is True
        assert bot_service._running is True
        mock_app.initialize.assert_called_once()
        mock_app.start.assert_called_once()
        mock_webhook.setup_webhook.assert_called_once()

    @pytest.mark.asyncio
    async def test_start_already_running(self, bot_service: TelegramBotService):
        """测试服务已在运行时启动"""
        bot_service._running = True
        
        result = await bot_service.start()
        
        assert result is True

    @pytest.mark.asyncio
    async def test_start_not_initialized(self, bot_service: TelegramBotService):
        """测试未初始化时启动服务"""
        bot_service.application = None
        bot_service._running = False
        
        result = await bot_service.start()
        
        assert result is False

    @pytest.mark.asyncio
    async def test_stop_success(self, bot_service: TelegramBotService):
        """测试成功停止服务"""
        # 设置模拟对象
        mock_app = Mock(spec=Application)
        mock_app.stop = AsyncMock()
        mock_app.shutdown = AsyncMock()
        
        mock_webhook = Mock()
        mock_webhook.remove_webhook = AsyncMock(return_value=True)
        
        bot_service.application = mock_app
        bot_service.webhook_handler = mock_webhook
        bot_service._running = True
        
        result = await bot_service.stop()
        
        assert result is True
        assert bot_service._running is False
        mock_app.stop.assert_called_once()
        mock_app.shutdown.assert_called_once()
        mock_webhook.remove_webhook.assert_called_once()

    @pytest.mark.asyncio
    async def test_stop_not_running(self, bot_service: TelegramBotService):
        """测试服务未运行时停止"""
        bot_service._running = False
        
        result = await bot_service.stop()
        
        assert result is True

    def test_is_running(self, bot_service: TelegramBotService):
        """测试运行状态检查"""
        bot_service._running = False
        assert bot_service.is_running() is False
        
        bot_service._running = True
        assert bot_service.is_running() is True

    @pytest.mark.asyncio
    async def test_get_bot_info(self, bot_service: TelegramBotService):
        """测试获取机器人信息"""
        mock_app = Mock(spec=Application)
        mock_bot = Mock(spec=Bot)
        mock_bot.get_me = AsyncMock(return_value=Mock(username="test_bot", first_name="Test Bot"))
        mock_app.bot = mock_bot
        
        bot_service.application = mock_app
        
        info = await bot_service.get_bot_info()
        
        assert info is not None
        mock_bot.get_me.assert_called_once()

    @pytest.mark.asyncio
    async def test_get_bot_info_not_initialized(self, bot_service: TelegramBotService):
        """测试未初始化时获取机器人信息"""
        bot_service.application = None
        
        info = await bot_service.get_bot_info()
        
        assert info is None


class TestWebhookHandler:
    """测试WebhookHandler"""

    @pytest.fixture
    def webhook_handler(self, mock_application, mock_config):
        """创建Webhook处理器"""
        return WebhookHandler(mock_application, mock_config)

    @pytest.mark.asyncio
    async def test_setup_webhook_success(
        self, 
        webhook_handler: WebhookHandler,
        mock_application,
        mock_config
    ):
        """测试成功设置Webhook"""
        result = await webhook_handler.setup_webhook()
        
        assert result is True
        mock_application.bot.set_webhook.assert_called_once()
        call_args = mock_application.bot.set_webhook.call_args
        assert call_args[1]['url'] == mock_config.webhook_url
        assert call_args[1]['secret_token'] == mock_config.webhook_secret

    @pytest.mark.asyncio
    async def test_setup_webhook_no_url(
        self, 
        webhook_handler: WebhookHandler,
        mock_config
    ):
        """测试未配置URL时设置Webhook"""
        mock_config.webhook_url = None
        
        result = await webhook_handler.setup_webhook()
        
        assert result is False

    @pytest.mark.asyncio
    async def test_setup_webhook_exception(
        self, 
        webhook_handler: WebhookHandler,
        mock_application
    ):
        """测试设置Webhook异常"""
        mock_application.bot.set_webhook.side_effect = Exception("设置失败")
        
        result = await webhook_handler.setup_webhook()
        
        assert result is False

    @pytest.mark.asyncio
    async def test_remove_webhook_success(
        self, 
        webhook_handler: WebhookHandler,
        mock_application
    ):
        """测试成功删除Webhook"""
        result = await webhook_handler.remove_webhook()
        
        assert result is True
        mock_application.bot.delete_webhook.assert_called_once()

    @pytest.mark.asyncio
    async def test_remove_webhook_exception(
        self, 
        webhook_handler: WebhookHandler,
        mock_application
    ):
        """测试删除Webhook异常"""
        mock_application.bot.delete_webhook.side_effect = Exception("删除失败")
        
        result = await webhook_handler.remove_webhook()
        
        assert result is False

    def test_verify_webhook_signature_success(
        self, 
        webhook_handler: WebhookHandler,
        mock_config
    ):
        """测试成功验证Webhook签名"""
        body = b'{"test": "data"}'
        expected_signature = hmac.new(
            mock_config.webhook_secret.encode(),
            body,
            hashlib.sha256
        ).hexdigest()
        signature = f"sha256={expected_signature}"
        
        result = webhook_handler.verify_webhook_signature(body, signature)
        
        assert result is True

    def test_verify_webhook_signature_invalid(
        self, 
        webhook_handler: WebhookHandler
    ):
        """测试无效Webhook签名"""
        body = b'{"test": "data"}'
        signature = "sha256=invalid_signature"
        
        result = webhook_handler.verify_webhook_signature(body, signature)
        
        assert result is False

    def test_verify_webhook_signature_no_secret(
        self, 
        webhook_handler: WebhookHandler,
        mock_config
    ):
        """测试未配置密钥时验证签名"""
        mock_config.webhook_secret = None
        body = b'{"test": "data"}'
        signature = "sha256=any_signature"
        
        result = webhook_handler.verify_webhook_signature(body, signature)
        
        assert result is True  # 跳过验证

    @pytest.mark.asyncio
    async def test_process_webhook_success(
        self, 
        webhook_handler: WebhookHandler,
        mock_application
    ):
        """测试成功处理Webhook"""
        update_data = {"update_id": 123, "message": {"text": "/help"}}
        
        with patch('telegram.Update.de_json') as mock_de_json:
            mock_update = Mock(spec=Update)
            mock_de_json.return_value = mock_update
            
            with patch.object(mock_application, 'process_update', new_callable=AsyncMock):
                result = await webhook_handler.process_webhook(update_data)
                
                assert result is True
                mock_de_json.assert_called_once_with(update_data, mock_application.bot)

    @pytest.mark.asyncio
    async def test_process_webhook_with_signature_verification(
        self, 
        webhook_handler: WebhookHandler,
        mock_config
    ):
        """测试带签名验证的Webhook处理"""
        update_data = {"update_id": 123, "message": {"text": "/help"}}
        body = json.dumps(update_data, separators=(',', ':')).encode()
        expected_signature = hmac.new(
            mock_config.webhook_secret.encode(),
            body,
            hashlib.sha256
        ).hexdigest()
        signature = f"sha256={expected_signature}"
        
        with patch('telegram.Update.de_json') as mock_de_json:
            mock_update = Mock(spec=Update)
            mock_de_json.return_value = mock_update
            
            with patch.object(webhook_handler.application, 'process_update', new_callable=AsyncMock):
                result = await webhook_handler.process_webhook(update_data, signature)
                
                assert result is True

    @pytest.mark.asyncio
    async def test_process_webhook_invalid_signature(
        self, 
        webhook_handler: WebhookHandler
    ):
        """测试无效签名的Webhook处理"""
        update_data = {"update_id": 123, "message": {"text": "/help"}}
        signature = "sha256=invalid_signature"
        
        with pytest.raises(WebhookError) as exc_info:
            await webhook_handler.process_webhook(update_data, signature)
        
        assert "Webhook签名验证失败" in str(exc_info.value)

    @pytest.mark.asyncio
    async def test_process_webhook_invalid_update(
        self, 
        webhook_handler: WebhookHandler
    ):
        """测试无效更新数据的Webhook处理"""
        update_data = {"invalid": "data"}
        
        with patch('telegram.Update.de_json') as mock_de_json:
            mock_de_json.return_value = None  # 无效更新
            
            result = await webhook_handler.process_webhook(update_data)
            
            assert result is False
