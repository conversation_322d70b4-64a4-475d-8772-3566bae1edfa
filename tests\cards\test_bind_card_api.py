#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
绑卡API测试用例
测试绑卡接口的各种场景，包括正常绑卡、参数验证、权限控制、签名验证等
"""

import sys
import os
import time
import json
import uuid
import hmac
import hashlib
import base64
from datetime import datetime
from typing import Dict, Any, Optional

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.dirname(__file__))))

from test.conftest import TestBase, get_test_accounts, format_test_result, print_test_summary


class BindCardAPITestSuite(TestBase):
    """绑卡API测试类"""

    def __init__(self):
        super().__init__()
        self.results = []
        self.test_accounts = get_test_accounts()
        self.admin_token = None
        self.merchant_token = None
        self.test_merchant = None

    def setup_test_environment(self):
        """设置测试环境"""
        print("=== 设置绑卡API测试环境 ===")

        # 登录管理员账号
        self.admin_token = self.login(
            self.test_accounts["super_admin"]["username"],
            self.test_accounts["super_admin"]["password"]
        )

        # 登录商户账号
        self.merchant_token = self.login(
            self.test_accounts["merchant_admin"]["username"],
            self.test_accounts["merchant_admin"]["password"]
        )

        if not self.admin_token:
            raise Exception("无法获取管理员token")
        if not self.merchant_token:
            raise Exception("无法获取商户token")

        # 获取测试商户信息
        self._get_test_merchant()

        print("✅ 测试环境设置完成")

    def _get_test_merchant(self):
        """获取测试商户信息"""
        try:
            status_code, response = self.make_request("GET", "/merchants", self.admin_token)
            if status_code == 200:
                merchants_data = response.get("data", response)
                if isinstance(merchants_data, dict):
                    merchants = merchants_data.get("items", [])
                elif isinstance(merchants_data, list):
                    merchants = merchants_data
                else:
                    merchants = []

                if merchants and len(merchants) > 0:
                    # 找到测试商户
                    for merchant in merchants:
                        if merchant.get("code") == "TEST_MERCHANT" or "test" in merchant.get("name", "").lower():
                            self.test_merchant = merchant
                            break

                    # 如果没找到测试商户，使用第一个
                    if not self.test_merchant and merchants:
                        self.test_merchant = merchants[0]

                    print(f"使用测试商户: {self.test_merchant.get('name')} (ID: {self.test_merchant.get('id')})")

        except Exception as e:
            print(f"获取商户信息失败: {e}")

    def _generate_signature(self, data: Dict[str, Any], secret: str, timestamp: str, nonce: str) -> str:
        """生成API签名"""
        try:
            # 1. 对数据按键排序
            sorted_data = dict(sorted(data.items()))

            # 2. 构建签名字符串：method|path|timestamp|nonce|sorted_params
            method = "POST"
            path = "/api/v1/card-bind"

            # 构建参数字符串
            params_str = "&".join([f"{k}={v}" for k, v in sorted_data.items()])

            # 构建完整签名字符串
            sign_string = f"{method}|{path}|{timestamp}|{nonce}|{params_str}"

            # 3. 使用HMAC-SHA256生成签名并Base64编码
            hmac_digest = hmac.new(
                secret.encode('utf-8'),
                sign_string.encode('utf-8'),
                hashlib.sha256
            ).digest()
            signature = base64.b64encode(hmac_digest).decode('utf-8')

            return signature

        except Exception as e:
            print(f"生成签名失败: {e}")
            return ""

    def _make_bind_request(self, card_data: Dict[str, Any], api_key: str = None,
                          api_secret: str = None, headers: Dict[str, str] = None) -> tuple:
        """发送绑卡请求"""
        if not api_key and self.test_merchant:
            api_key = self.test_merchant.get("api_key", "test_api_key")
        if not api_secret and self.test_merchant:
            api_secret = self.test_merchant.get("api_secret", "test_secret")

        # 生成时间戳和随机字符串
        timestamp = str(int(time.time() * 1000))
        nonce = str(uuid.uuid4()).replace("-", "")[:16]

        # 生成签名
        signature = self._generate_signature(card_data, api_secret, timestamp, nonce)

        # 构建请求头
        request_headers = {
            "Content-Type": "application/json",
            "api-key": api_key,
            "X-Timestamp": timestamp,
            "X-Nonce": nonce,
            "X-Signature": signature
        }

        if headers:
            request_headers.update(headers)

        # 发送请求
        url = f"{self.base_url}{self.api_prefix}/card-bind"
        try:
            response = self.session.post(
                url,
                json=card_data,
                headers=request_headers,
                timeout=self.timeout
            )

            try:
                response_data = response.json()
            except:
                response_data = {"message": response.text}

            return response.status_code, response_data

        except Exception as e:
            return 0, {"error": str(e)}

    def test_valid_bind_card_request(self):
        """测试有效的绑卡请求"""
        print("\n=== 测试有效的绑卡请求 ===")

        if not self.test_merchant:
            print("⚠️ 无法获取测试商户信息，跳过测试")
            return

        # 构建测试数据
        card_data = {
            "card_number": f"*************{int(time.time()) % 1000:03d}",
            "card_password": "123456",
            "merchant_code": self.test_merchant.get("code", "TEST_MERCHANT"),
            "merchant_order_id": f"ORDER_{int(time.time())}",
            "amount": 10000,  # 100元
            "ext_data": "test_bind_card"
        }

        status_code, response = self._make_bind_request(card_data)

        if status_code == 200:
            self.results.append(format_test_result(
                "有效绑卡请求",
                True,
                "绑卡请求成功提交",
                {"response": response}
            ))
            print("✅ 绑卡请求成功提交")
            print(f"   📋 记录ID: {response.get('recordId', 'N/A')}")
            print(f"   📋 请求ID: {response.get('requestId', 'N/A')}")
            print(f"   📋 状态: {response.get('status', 'N/A')}")
        else:
            self.results.append(format_test_result(
                "有效绑卡请求",
                False,
                f"绑卡请求失败，状态码: {status_code}",
                {"response": response}
            ))
            print(f"❌ 绑卡请求失败，状态码: {status_code}")
            print(f"   错误信息: {response.get('detail', response.get('message', 'Unknown error'))}")

    def test_invalid_card_number(self):
        """测试无效卡号"""
        print("\n=== 测试无效卡号 ===")

        if not self.test_merchant:
            print("⚠️ 无法获取测试商户信息，跳过测试")
            return

        # 测试空卡号
        card_data = {
            "card_number": "",
            "card_password": "123456",
            "merchant_code": self.test_merchant.get("code", "TEST_MERCHANT"),
            "merchant_order_id": f"ORDER_{int(time.time())}",
            "amount": 10000
        }

        status_code, response = self._make_bind_request(card_data)

        if status_code in [400, 422]:  # 参数验证错误
            self.results.append(format_test_result(
                "无效卡号验证",
                True,
                "正确拒绝了无效卡号",
                {"status_code": status_code}
            ))
            print("✅ 正确拒绝了无效卡号")
        else:
            self.results.append(format_test_result(
                "无效卡号验证",
                False,
                f"未正确验证卡号，状态码: {status_code}",
                {"response": response}
            ))
            print(f"❌ 未正确验证卡号，状态码: {status_code}")

    def test_invalid_amount(self):
        """测试无效金额"""
        print("\n=== 测试无效金额 ===")

        if not self.test_merchant:
            print("⚠️ 无法获取测试商户信息，跳过测试")
            return

        # 测试金额小于最小值
        card_data = {
            "card_number": f"*************{int(time.time()) % 1000:03d}",
            "card_password": "123456",
            "merchant_code": self.test_merchant.get("code", "TEST_MERCHANT"),
            "merchant_order_id": f"ORDER_{int(time.time())}",
            "amount": 50  # 小于最小值100
        }

        status_code, response = self._make_bind_request(card_data)

        if status_code in [400, 422]:  # 参数验证错误
            self.results.append(format_test_result(
                "无效金额验证",
                True,
                "正确拒绝了无效金额",
                {"status_code": status_code}
            ))
            print("✅ 正确拒绝了无效金额")
        else:
            self.results.append(format_test_result(
                "无效金额验证",
                False,
                f"未正确验证金额，状态码: {status_code}",
                {"response": response}
            ))
            print(f"❌ 未正确验证金额，状态码: {status_code}")

    def test_invalid_api_key(self):
        """测试无效API密钥"""
        print("\n=== 测试无效API密钥 ===")

        card_data = {
            "card_number": f"*************{int(time.time()) % 1000:03d}",
            "card_password": "123456",
            "merchant_code": "TEST_MERCHANT",
            "merchant_order_id": f"ORDER_{int(time.time())}",
            "amount": 10000
        }

        status_code, response = self._make_bind_request(
            card_data,
            api_key="invalid_api_key",
            api_secret="invalid_secret"
        )

        if status_code == 401:  # 未授权
            self.results.append(format_test_result(
                "无效API密钥验证",
                True,
                "正确拒绝了无效API密钥",
                {"status_code": status_code}
            ))
            print("✅ 正确拒绝了无效API密钥")
        else:
            self.results.append(format_test_result(
                "无效API密钥验证",
                False,
                f"未正确验证API密钥，状态码: {status_code}",
                {"response": response}
            ))
            print(f"❌ 未正确验证API密钥，状态码: {status_code}")

    def test_invalid_signature(self):
        """测试无效签名"""
        print("\n=== 测试无效签名 ===")

        if not self.test_merchant:
            print("⚠️ 无法获取测试商户信息，跳过测试")
            return

        card_data = {
            "card_number": f"*************{int(time.time()) % 1000:03d}",
            "card_password": "123456",
            "merchant_code": self.test_merchant.get("code", "TEST_MERCHANT"),
            "merchant_order_id": f"ORDER_{int(time.time())}",
            "amount": 10000
        }

        # 使用错误的签名
        status_code, response = self._make_bind_request(
            card_data,
            headers={"X-Signature": "invalid_signature"}
        )

        if status_code == 401:  # 签名验证失败
            self.results.append(format_test_result(
                "无效签名验证",
                True,
                "正确拒绝了无效签名",
                {"status_code": status_code}
            ))
            print("✅ 正确拒绝了无效签名")
        else:
            self.results.append(format_test_result(
                "无效签名验证",
                False,
                f"未正确验证签名，状态码: {status_code}",
                {"response": response}
            ))
            print(f"❌ 未正确验证签名，状态码: {status_code}")

    def test_duplicate_card_number(self):
        """测试重复卡号"""
        print("\n=== 测试重复卡号 ===")

        if not self.test_merchant:
            print("⚠️ 无法获取测试商户信息，跳过测试")
            return

        # 使用固定的卡号进行测试
        card_number = "*************999"

        card_data = {
            "card_number": card_number,
            "card_password": "123456",
            "merchant_code": self.test_merchant.get("code", "TEST_MERCHANT"),
            "merchant_order_id": f"ORDER_{int(time.time())}_1",
            "amount": 10000
        }

        # 第一次提交
        status_code1, response1 = self._make_bind_request(card_data)
        print(f"   第一次提交状态码: {status_code1}")

        # 第二次提交相同卡号
        card_data["merchant_order_id"] = f"ORDER_{int(time.time())}_2"
        status_code2, response2 = self._make_bind_request(card_data)
        print(f"   第二次提交状态码: {status_code2}")

        if status_code2 in [400, 409]:  # 冲突或参数错误
            self.results.append(format_test_result(
                "重复卡号验证",
                True,
                "正确拒绝了重复卡号",
                {"status_code": status_code2}
            ))
            print("✅ 正确拒绝了重复卡号")
        else:
            self.results.append(format_test_result(
                "重复卡号验证",
                False,
                f"未正确验证重复卡号，状态码: {status_code2}",
                {"response": response2}
            ))
            print(f"❌ 未正确验证重复卡号，状态码: {status_code2}")

    def test_missing_required_fields(self):
        """测试缺少必填字段"""
        print("\n=== 测试缺少必填字段 ===")

        if not self.test_merchant:
            print("⚠️ 无法获取测试商户信息，跳过测试")
            return

        # 测试缺少卡号
        card_data = {
            "card_password": "123456",
            "merchant_code": self.test_merchant.get("code", "TEST_MERCHANT"),
            "merchant_order_id": f"ORDER_{int(time.time())}",
            "amount": 10000
        }

        status_code, response = self._make_bind_request(card_data)

        if status_code in [400, 422]:  # 参数验证错误
            self.results.append(format_test_result(
                "缺少必填字段验证",
                True,
                "正确拒绝了缺少必填字段的请求",
                {"status_code": status_code}
            ))
            print("✅ 正确拒绝了缺少必填字段的请求")
        else:
            self.results.append(format_test_result(
                "缺少必填字段验证",
                False,
                f"未正确验证必填字段，状态码: {status_code}",
                {"response": response}
            ))
            print(f"❌ 未正确验证必填字段，状态码: {status_code}")

    def test_merchant_code_mismatch(self):
        """测试商户编码不匹配"""
        print("\n=== 测试商户编码不匹配 ===")

        if not self.test_merchant:
            print("⚠️ 无法获取测试商户信息，跳过测试")
            return

        card_data = {
            "card_number": f"*************{int(time.time()) % 1000:03d}",
            "card_password": "123456",
            "merchant_code": "WRONG_MERCHANT_CODE",  # 错误的商户编码
            "merchant_order_id": f"ORDER_{int(time.time())}",
            "amount": 10000
        }

        status_code, response = self._make_bind_request(card_data)

        if status_code == 400:  # 参数错误
            self.results.append(format_test_result(
                "商户编码不匹配验证",
                True,
                "正确拒绝了不匹配的商户编码",
                {"status_code": status_code}
            ))
            print("✅ 正确拒绝了不匹配的商户编码")
        else:
            self.results.append(format_test_result(
                "商户编码不匹配验证",
                False,
                f"未正确验证商户编码，状态码: {status_code}",
                {"response": response}
            ))
            print(f"❌ 未正确验证商户编码，状态码: {status_code}")

    def test_expired_timestamp(self):
        """测试过期时间戳"""
        print("\n=== 测试过期时间戳 ===")

        if not self.test_merchant:
            print("⚠️ 无法获取测试商户信息，跳过测试")
            return

        card_data = {
            "card_number": f"*************{int(time.time()) % 1000:03d}",
            "card_password": "123456",
            "merchant_code": self.test_merchant.get("code", "TEST_MERCHANT"),
            "merchant_order_id": f"ORDER_{int(time.time())}",
            "amount": 10000
        }

        # 使用过期的时间戳（10分钟前）
        expired_timestamp = str(int((time.time() - 600) * 1000))
        nonce = str(uuid.uuid4()).replace("-", "")[:16]
        api_secret = self.test_merchant.get("api_secret", "test_secret")

        signature = self._generate_signature(card_data, api_secret, expired_timestamp, nonce)

        headers = {
            "X-Timestamp": expired_timestamp,
            "X-Nonce": nonce,
            "X-Signature": signature
        }

        status_code, response = self._make_bind_request(card_data, headers=headers)

        if status_code == 401:  # 时间戳验证失败
            self.results.append(format_test_result(
                "过期时间戳验证",
                True,
                "正确拒绝了过期时间戳",
                {"status_code": status_code}
            ))
            print("✅ 正确拒绝了过期时间戳")
        else:
            self.results.append(format_test_result(
                "过期时间戳验证",
                False,
                f"未正确验证时间戳，状态码: {status_code}",
                {"response": response}
            ))
            print(f"❌ 未正确验证时间戳，状态码: {status_code}")

    def run_all_tests(self):
        """运行所有绑卡API测试"""
        print("🧪 开始绑卡API测试")
        print("="*60)

        start_time = time.time()

        try:
            # 设置测试环境
            self.setup_test_environment()

            # 运行测试
            self.test_valid_bind_card_request()
            self.test_invalid_card_number()
            self.test_invalid_amount()
            self.test_invalid_api_key()
            self.test_invalid_signature()
            self.test_duplicate_card_number()
            self.test_missing_required_fields()
            self.test_merchant_code_mismatch()
            self.test_expired_timestamp()

        except Exception as e:
            self.results.append(format_test_result(
                "测试环境设置",
                False,
                f"测试环境设置失败: {str(e)}"
            ))
            print(f"❌ 测试环境设置失败: {str(e)}")

        end_time = time.time()
        duration = end_time - start_time

        # 打印测试摘要
        print_test_summary(self.results)
        print(f"测试耗时: {duration:.2f}秒")

        return self.results


def main():
    """主函数"""
    test = BindCardAPITestSuite()
    results = test.run_all_tests()

    # 判断测试是否通过
    failed_tests = [r for r in results if not r.get("success", False)]

    if not failed_tests:
        print("\n🎉 绑卡API测试全部通过！")
        return 0
    else:
        print(f"\n⚠️ 发现 {len(failed_tests)} 个问题需要修复")
        for test in failed_tests:
            print(f"  - {test['test_name']}: {test['message']}")
        return 1


if __name__ == "__main__":
    sys.exit(main())
