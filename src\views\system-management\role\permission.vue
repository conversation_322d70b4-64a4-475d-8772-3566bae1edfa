<template>
  <div class="role-permission-page">
    <!-- 固定页面头部 -->
    <div class="page-header fixed-header">
      <div class="header-left">
        <el-button @click="goBack" :icon="ArrowLeft" circle></el-button>
        <span class="page-title">配置角色权限 - {{ getRoleDisplayName() }}</span>
      </div>
      <div class="header-actions">
        <el-button @click="goBack">取消</el-button>
        <el-button type="primary" @click="handleSaveAllPermissions" :loading="saving">
          保存权限配置
        </el-button>
      </div>
    </div>

    <!-- 内容区域，添加顶部间距以避免被固定头部遮挡 -->
    <div class="content-wrapper">
      <!-- 权限配置内容 -->
      <el-card class="permission-card" v-loading="permissionLoading">
        <div class="permission-config">
          <!-- 权限配置标签页 -->
          <el-tabs v-model="activePermissionTab" type="border-card">
            <!-- 菜单权限标签页 -->
            <el-tab-pane label="菜单权限" name="menu">
              <div class="config-section">
                <div class="section-header">
                  <h4>菜单权限配置</h4>
                  <p class="section-desc">选择角色可以访问的菜单项</p>
                </div>
                <div class="menu-tree">
                  <el-tree ref="menuTreeRef" :data="allMenus" :props="{ children: 'children', label: 'name' }"
                    show-checkbox node-key="id" :default-checked-keys="selectedMenus" :check-strictly="false"
                    class="permission-tree" @check="handleMenuCheck" />
                </div>
              </div>
            </el-tab-pane>

            <!-- API权限标签页 -->
            <el-tab-pane label="API权限" name="api">
              <div class="config-section">
                <div class="section-header">
                  <h4>API权限配置</h4>
                  <p class="section-desc">选择角色可以调用的API接口</p>
                </div>
                <div class="permission-search">
                  <el-input v-model="apiPermissionSearch" placeholder="搜索API权限..." prefix-icon="Search" clearable
                    @input="filterApiPermissions" />
                </div>
                <div class="permission-list">
                  <div v-for="group in filteredApiPermissionGroups" :key="group.module" class="permission-group">
                    <div class="group-header">
                      <el-checkbox :indeterminate="group.indeterminate" v-model="group.checkAll"
                        @change="handleApiGroupCheckAll(group)">
                        {{ group.module }}
                      </el-checkbox>
                    </div>
                    <div class="group-items">
                      <el-checkbox-group v-model="selectedApiPermissions" class="permission-checkbox-group">
                        <el-checkbox v-for="permission in group.permissions" :key="permission.id"
                          :value="permission.id">
                          <div class="permission-item">
                            <span class="permission-name">{{ permission.name }}</span>
                            <span class="permission-code">{{ permission.code }}</span>
                          </div>
                        </el-checkbox>
                      </el-checkbox-group>
                    </div>
                  </div>
                </div>
              </div>
            </el-tab-pane>

            <!-- 数据权限标签页 -->
            <el-tab-pane label="数据权限" name="data">
              <div class="config-section">
                <div class="section-header">
                  <h4>数据权限配置</h4>
                  <p class="section-desc">配置角色的数据访问范围</p>
                </div>
                <div class="data-permission-config">
                  <el-checkbox-group v-model="selectedDataPermissions" class="data-permission-group">
                    <div v-for="group in dataPermissionGroups" :key="group.type" class="data-group">
                      <div class="group-title">{{ group.title }}</div>
                      <div class="group-options">
                        <el-checkbox v-for="option in group.options" :key="option.id || option.code" :value="option.id">
                          <div class="data-option">
                            <span class="option-name">{{ option.name }}</span>
                            <span class="option-desc">{{ option.description }}</span>
                          </div>
                        </el-checkbox>
                      </div>
                    </div>
                  </el-checkbox-group>
                </div>
              </div>
            </el-tab-pane>
          </el-tabs>
        </div>
      </el-card>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, computed, watch, nextTick } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { ArrowLeft, Search } from '@element-plus/icons-vue'
import { roleApi } from '@/api/modules/role'
import { menuApi } from '@/api/modules/menu'
import { permissionApi } from '@/api/modules/permission'

const route = useRoute()
const router = useRouter()

// 获取路由参数
const roleId = computed(() => route.params.id)

// 响应式数据
const currentRole = ref(null)
const permissionLoading = ref(false)
const saving = ref(false)

// 权限配置标签页
const activePermissionTab = ref('menu')

// 菜单权限相关
const allMenus = ref([])
const selectedMenus = ref([])
const menuTreeRef = ref()

// API权限相关
const allApiPermissions = ref([])
const selectedApiPermissions = ref([])
const apiPermissionSearch = ref('')
const apiPermissionGroups = ref([])

// 数据权限相关
const selectedDataPermissions = ref([])
const allDataPermissions = ref([])
const dataPermissionGroups = ref([
  {
    type: 'merchant',
    title: '商户数据权限',
    options: [
      { id: null, code: 'data:merchant:all', name: '所有商户数据', description: '【仅超级管理员】可以访问所有商户的数据' },
      { id: null, code: 'data:merchant:own', name: '本商户数据', description: '只能访问所属商户的数据' }
    ]
  },
  {
    type: 'department',
    title: '部门数据权限',
    options: [
      { id: null, code: 'data:department:all', name: '本商户所有部门数据', description: '【商户范围内】可以访问本商户的所有部门数据' },
      { id: null, code: 'data:department:own', name: '本部门数据', description: '只能访问所属部门的数据' },
      { id: null, code: 'data:department:sub', name: '本部门及下级', description: '可以访问本部门及其下级部门的数据' }
    ]
  },
  {
    type: 'user',
    title: '用户数据权限',
    options: [
      { id: null, code: 'data:user:all', name: '本商户所有用户数据', description: '【商户范围内】可以访问本商户的所有用户数据' },
      { id: null, code: 'data:user:own', name: '本人数据', description: '只能访问自己的数据' }
    ]
  }
])

// 计算属性
const filteredApiPermissionGroups = computed(() => {
  if (!apiPermissionSearch.value) {
    return apiPermissionGroups.value
  }

  const searchTerm = apiPermissionSearch.value.toLowerCase()
  return apiPermissionGroups.value.map(group => ({
    ...group,
    permissions: group.permissions.filter(permission =>
      permission.name.toLowerCase().includes(searchTerm) ||
      permission.code.toLowerCase().includes(searchTerm)
    )
  })).filter(group => group.permissions.length > 0)
})

// 监听选中的API权限变化，更新分组的全选状态
watch(selectedApiPermissions, (newVal) => {
  updateApiGroupCheckStatus()
}, { deep: true })

// 返回角色列表
const goBack = () => {
  router.push('/system/role')
}

// 角色信息加载状态
const roleLoading = ref(true)
const roleLoadError = ref(false)

// 获取角色信息
const fetchRoleInfo = async () => {
  try {
    roleLoading.value = true
    roleLoadError.value = false

    const response = await roleApi.getDetail(roleId.value)

    // 正确解析API响应数据
    let roleData = null
    if (response && response.data) {
      // 标准格式：{success: true, data: {...}, message: "..."}
      roleData = response.data
    } else if (response && response.name) {
      // 直接返回角色对象格式
      roleData = response
    } else {
      throw new Error('未知的API响应格式')
    }

    if (roleData) {
      currentRole.value = roleData
    } else {
      throw new Error('无法解析角色数据')
    }
  } catch (error) {
    console.error('获取角色信息失败:', error)
    roleLoadError.value = true

    // 根据错误类型显示不同的错误信息
    if (error.response?.status === 404) {
      ElMessage.error('角色不存在')
    } else if (error.response?.status === 403) {
      ElMessage.error('没有权限访问该角色')
    } else {
      ElMessage.error('获取角色信息失败，请稍后重试')
    }

    // 延迟返回，给用户时间看到错误信息
    setTimeout(() => {
      goBack()
    }, 2000)
  } finally {
    roleLoading.value = false
  }
}

// 获取角色显示名称
const getRoleDisplayName = () => {
  if (roleLoading.value) {
    return '加载中...'
  }

  if (roleLoadError.value) {
    return '加载失败'
  }

  // 优先使用API返回的真实角色名称
  if (currentRole.value?.name) {
    return currentRole.value.name
  }

  // 只有在API调用失败且没有角色信息时才使用fallback
  return `角色${roleId.value}`
}

// 加载所有菜单
const loadAllMenus = async () => {
  try {
    const response = await menuApi.getList()
    allMenus.value = buildMenuTree(response || [])
  } catch (error) {
    console.error('获取菜单列表失败:', error)
    ElMessage.error('获取菜单列表失败')
    allMenus.value = []
  }
}

// 加载所有API权限
const loadAllApiPermissions = async () => {
  try {
    const response = await permissionApi.getList({
      resource_type: 'api',
      page: 1,
      page_size: 1000  // 增加页面大小以确保获取所有API权限
    })

    const permissions = response.items || response || []
    allApiPermissions.value = permissions
    groupApiPermissions(permissions)
  } catch (error) {
    console.error('获取API权限列表失败:', error)
    ElMessage.error('获取API权限列表失败')
    allApiPermissions.value = []
    apiPermissionGroups.value = []
  }
}

// 加载所有数据权限
const loadAllDataPermissions = async () => {
  try {
    const response = await permissionApi.getList({
      resource_type: 'data',
      page: 1,
      page_size: 1000  // 增加页面大小以确保获取所有数据权限
    })

    const permissions = response.items || response || []
    allDataPermissions.value = permissions

    // 更新数据权限组中的ID
    dataPermissionGroups.value.forEach(group => {
      group.options.forEach(option => {
        const permission = permissions.find(p => p.code === option.code)
        if (permission) {
          option.id = permission.id
        }
      })
    })
  } catch (error) {
    console.error('获取数据权限列表失败:', error)
    ElMessage.error('获取数据权限列表失败')
    allDataPermissions.value = []
  }
}

// 按模块分组API权限
const groupApiPermissions = (permissions) => {
  const groups = {}

  permissions.forEach(permission => {
    let module = 'other'

    // 处理模块级API权限：api:/api/v1/xxx
    if (permission.code.startsWith('api:/api/v1/')) {
      const parts = permission.code.replace('api:/api/v1/', '').split('/')
      module = parts[0] || 'other'
    }
    // 处理操作级API权限：api:module:action
    else if (permission.code.startsWith('api:') && permission.code.split(':').length === 3) {
      const parts = permission.code.split(':')
      module = parts[1] || 'other'
    }

    if (!groups[module]) {
      groups[module] = {
        module: module,
        permissions: [],
        checkAll: false,
        indeterminate: false
      }
    }

    groups[module].permissions.push(permission)
  })

  apiPermissionGroups.value = Object.values(groups)
}

// 构建菜单树
const buildMenuTree = (menus) => {
  const menuMap = new Map()
  const rootMenus = []

  menus.forEach(menu => {
    menuMap.set(menu.id, { ...menu, children: [] })
  })

  menus.forEach(menu => {
    const menuItem = menuMap.get(menu.id)
    if (menu.parent_id && menuMap.has(menu.parent_id)) {
      menuMap.get(menu.parent_id).children.push(menuItem)
    } else {
      rootMenus.push(menuItem)
    }
  })

  return rootMenus
}

// 加载角色权限
const loadRolePermissions = async () => {
  try {
    console.log('=== loadRolePermissions 开始执行 ===')
    console.log('开始并行加载角色权限...')

    await Promise.all([
      loadRoleMenus(),
      loadRoleApiPermissions(),
      loadRoleDataPermissions()
    ])

    console.log('所有角色权限加载完成')
    console.log('=== loadRolePermissions 执行完成 ===')
  } catch (error) {
    console.error('加载角色权限失败:', error)
    ElMessage.error('加载角色权限失败')
  }
}

// 加载角色菜单权限
const loadRoleMenus = async () => {
  try {
    const response = await roleApi.getMenus(roleId.value)
    let menuIds = []

    if (Array.isArray(response)) {
      menuIds = response.map(menu => menu.id || menu)
    } else if (response && response.items) {
      menuIds = response.items.map(menu => menu.id || menu)
    } else if (response && response.menus) {
      // 处理后端返回的 {role_id: x, menus: [...]} 格式
      menuIds = response.menus.map(menu => menu.id || menu)
    } else if (response && response.menu_ids) {
      menuIds = response.menu_ids
    }

    console.log('解析出的菜单IDs:', menuIds)
    selectedMenus.value = menuIds

    // 确保菜单树已渲染后设置选中状态
    await nextTick()
    if (menuTreeRef.value) {
      if (menuIds.length > 0) {
        console.log('设置菜单树选中状态:', menuIds)
        // 【修复】使用setCheckedKeys设置选中状态，确保只选中指定的节点
        menuTreeRef.value.setCheckedKeys(menuIds)

        // 验证设置结果
        const actualCheckedKeys = menuTreeRef.value.getCheckedKeys()
        const actualHalfCheckedKeys = menuTreeRef.value.getHalfCheckedKeys()
        console.log('设置后的实际选中状态:', {
          checkedKeys: actualCheckedKeys,
          halfCheckedKeys: actualHalfCheckedKeys
        })
      } else {
        console.log('没有菜单权限需要设置，清空选中状态')
        menuTreeRef.value.setCheckedKeys([])
      }
    }
  } catch (error) {
    console.error('获取角色菜单权限失败:', error)
    selectedMenus.value = []
  }
}

// 处理菜单选中变化
const handleMenuCheck = (data, checked) => {
  // 添加调试信息，便于问题排查
  if (menuTreeRef.value) {
    const checkedKeys = menuTreeRef.value.getCheckedKeys()
    const halfCheckedKeys = menuTreeRef.value.getHalfCheckedKeys()
    console.log('菜单选中变化:', {
      changedNode: { id: data.id, name: data.name, checked: checked.checkedKeys.includes(data.id) },
      allCheckedKeys: checkedKeys,
      halfCheckedKeys: halfCheckedKeys
    })
  }
}

// 加载角色API权限
const loadRoleApiPermissions = async () => {
  try {
    console.log('=== 开始加载角色API权限 ===')
    console.log('角色ID:', roleId.value)
    console.log('调用 roleApi.getPermissions...')

    const response = await roleApi.getPermissions(roleId.value)
    console.log('角色API权限响应:', response)

    let permissionIds = []

    if (Array.isArray(response)) {
      console.log('响应是数组格式')
      permissionIds = response.map(permission => permission.id || permission)
    } else if (response && response.items) {
      console.log('响应是items格式')
      permissionIds = response.items.map(permission => permission.id || permission)
    } else if (response && response.permission_ids) {
      console.log('响应是permission_ids格式')
      permissionIds = response.permission_ids
    } else if (response && response.data) {
      console.log('响应是data格式')
      console.log('response.data:', response.data)

      if (Array.isArray(response.data)) {
        console.log('data是数组格式')
        permissionIds = response.data.map(permission => permission.id || permission)
      } else if (response.data.items) {
        console.log('data.items格式')
        permissionIds = response.data.items.map(permission => permission.id || permission)
      } else if (response.data.permission_ids) {
        console.log('data.permission_ids格式')
        permissionIds = response.data.permission_ids
      } else if (response.data.permissions) {
        console.log('data.permissions格式')
        permissionIds = response.data.permissions.map(permission => permission.id || permission)
      } else {
        console.log('data格式未知:', response.data)
      }
    } else {
      console.log('响应格式未知:', response)
    }

    console.log('解析出的权限IDs:', permissionIds)
    console.log('所有API权限数量:', allApiPermissions.value.length)

    const filteredIds = permissionIds.filter(id =>
      allApiPermissions.value.some(permission => permission.id === id)
    )

    console.log('过滤后的权限IDs:', filteredIds)
    selectedApiPermissions.value = filteredIds
    console.log('设置selectedApiPermissions.value:', selectedApiPermissions.value)
    console.log('=== 角色API权限加载完成 ===')
  } catch (error) {
    console.error('获取角色API权限失败:', error)
    selectedApiPermissions.value = []
  }
}

// 加载角色数据权限
const loadRoleDataPermissions = async () => {
  try {
    console.log('=== 开始加载角色数据权限 ===')
    console.log('角色ID:', roleId.value)
    console.log('调用 roleApi.getDataPermissions...')

    const response = await roleApi.getDataPermissions(roleId.value)
    console.log('角色数据权限响应:', response)

    let dataPermissionIds = []
    if (response && response.data && response.data.data_permissions) {
      dataPermissionIds = response.data.data_permissions.map(p => p.id)
    } else if (response && Array.isArray(response)) {
      dataPermissionIds = response.map(p => p.id)
    } else if (response && response.data_permissions) {
      dataPermissionIds = response.data_permissions.map(p => p.id)
    }

    console.log('解析出的数据权限IDs:', dataPermissionIds)
    selectedDataPermissions.value = dataPermissionIds
    console.log('设置selectedDataPermissions.value:', selectedDataPermissions.value)
    console.log('=== 角色数据权限加载完成 ===')
  } catch (error) {
    console.error('获取角色数据权限失败:', error)
    selectedDataPermissions.value = []
  }
}

// 更新API分组的全选状态
const updateApiGroupCheckStatus = () => {
  apiPermissionGroups.value.forEach(group => {
    const selectedCount = group.permissions.filter(permission =>
      selectedApiPermissions.value.includes(permission.id)
    ).length

    group.checkAll = selectedCount === group.permissions.length
    group.indeterminate = selectedCount > 0 && selectedCount < group.permissions.length
  })
}

// 处理API分组全选
const handleApiGroupCheckAll = (group) => {
  if (group.checkAll) {
    // 全选：添加该分组的所有权限
    group.permissions.forEach(permission => {
      if (!selectedApiPermissions.value.includes(permission.id)) {
        selectedApiPermissions.value.push(permission.id)
      }
    })
  } else {
    // 取消全选：移除该分组的所有权限
    group.permissions.forEach(permission => {
      const index = selectedApiPermissions.value.indexOf(permission.id)
      if (index > -1) {
        selectedApiPermissions.value.splice(index, 1)
      }
    })
  }

  group.indeterminate = false
}

// 过滤API权限
const filterApiPermissions = () => {
  // 触发计算属性重新计算
}

// 保存所有权限配置
const handleSaveAllPermissions = async () => {
  console.log('=== 开始保存权限配置 ===')
  console.log('roleId.value:', roleId.value)
  console.log('route.params:', route.params)

  if (!roleId.value) {
    console.error('角色ID无效:', roleId.value)
    ElMessage.error('无效的角色ID')
    return
  }

  try {
    saving.value = true
    console.log('设置saving状态为true')

    console.log('保存权限配置，角色ID:', roleId.value)

    // 保存菜单权限
    if (menuTreeRef.value) {
      const checkedKeys = menuTreeRef.value.getCheckedKeys()
      const halfCheckedKeys = menuTreeRef.value.getHalfCheckedKeys()

      // 【修复】只保存完全选中的节点，不包含半选中的父节点
      // 半选中的父节点是因为子节点被选中而自动产生的，不应该被保存
      const selectedMenuIds = [...checkedKeys]

      console.log('完全选中的菜单:', checkedKeys)
      console.log('半选中的父节点:', halfCheckedKeys)
      console.log('实际保存的菜单权限:', selectedMenuIds)
      console.log('调用 roleApi.updateMenus...')
      await roleApi.updateMenus(roleId.value, selectedMenuIds)
      console.log('菜单权限保存成功')
    } else {
      console.log('menuTreeRef.value 为空，跳过菜单权限保存')
    }

    // 保存API权限
    const allPermissionIds = [...selectedApiPermissions.value]

    console.log('保存API权限:', allPermissionIds)
    if (allPermissionIds.length > 0) {
      console.log('调用 roleApi.updatePermissions...')
      await roleApi.updatePermissions(roleId.value, allPermissionIds)
      console.log('API权限保存成功')
    } else {
      console.log('没有API权限需要保存')
    }

    // 保存数据权限
    console.log('保存数据权限:', selectedDataPermissions.value)
    if (selectedDataPermissions.value.length > 0) {
      console.log('调用 roleApi.updateDataPermissions...')
      await roleApi.updateDataPermissions(roleId.value, {
        data_permission_ids: selectedDataPermissions.value
      })
      console.log('数据权限保存成功')
    } else {
      console.log('没有数据权限需要保存，清空数据权限')
      await roleApi.updateDataPermissions(roleId.value, {
        data_permission_ids: []
      })
    }

    console.log('所有权限保存完成')
    ElMessage.success('权限配置保存成功')
    goBack()
  } catch (error) {
    console.error('保存权限配置失败:', error)
    ElMessage.error('保存权限配置失败: ' + (error.message || '未知错误'))
  } finally {
    saving.value = false
    console.log('设置saving状态为false')
    console.log('=== 保存权限配置结束 ===')
  }
}

onMounted(async () => {
  console.log('=== onMounted 开始执行 ===')
  console.log('roleId.value:', roleId.value)

  if (!roleId.value) {
    console.error('无效的角色ID')
    ElMessage.error('无效的角色ID')
    goBack()
    return
  }

  permissionLoading.value = true
  try {
    console.log('开始加载基础数据...')
    await Promise.all([
      fetchRoleInfo(),
      loadAllMenus(),
      loadAllApiPermissions(),
      loadAllDataPermissions()
    ])
    console.log('基础数据加载完成')

    // 加载角色权限
    console.log('开始加载角色权限...')
    await loadRolePermissions()
    console.log('角色权限加载完成')
  } catch (error) {
    console.error('初始化失败:', error)
  } finally {
    permissionLoading.value = false
    console.log('=== onMounted 执行完成 ===')
  }
})
</script>

<style scoped>
.role-permission-page {
  padding: 0;
  min-height: 100vh;
}

/* 固定页面头部 - 只在主内容区域内固定 */
.page-header.fixed-header {
  position: sticky;
  top: 0;
  z-index: 100;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  background: #fff;
  border-bottom: 1px solid #e4e7ed;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  margin: -20px -20px 20px -20px;
  /* 抵消el-main的padding */
}

/* 内容区域 */
.content-wrapper {
  padding: 0;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 12px;
}

.page-title {
  font-size: 18px;
  font-weight: 600;
  color: #303133;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 400px;
}

.header-actions {
  display: flex;
  gap: 12px;
  flex-shrink: 0;
}

.permission-card {
  min-height: 600px;
  margin: 0;
}

.permission-config {
  padding: 20px 0;
}

.config-section {
  padding: 20px;
}

.section-header {
  margin-bottom: 20px;
}

.section-header h4 {
  margin: 0 0 8px 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.section-desc {
  margin: 0;
  color: #606266;
  font-size: 14px;
}

.menu-tree {
  max-height: 500px;
  overflow-y: auto;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  padding: 10px;
}

.permission-tree {
  width: 100%;
}

.permission-search {
  margin-bottom: 20px;
}

.permission-list {
  max-height: 500px;
  overflow-y: auto;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  padding: 15px;
}

.permission-checkbox-group {
  width: 100%;
}

.permission-group {
  margin-bottom: 20px;
  border: 1px solid #e4e7ed;
  border-radius: 6px;
  overflow: hidden;
}

.group-header {
  background: #f5f7fa;
  padding: 12px 16px;
  border-bottom: 1px solid #e4e7ed;
  font-weight: 600;
}

.group-items {
  padding: 12px 16px;
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 12px;
}

.permission-item {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.permission-name {
  font-weight: 500;
  color: #303133;
}

.permission-code {
  font-size: 12px;
  color: #909399;
  font-family: 'Courier New', monospace;
}

.data-permission-config {
  max-height: 500px;
  overflow-y: auto;
}

.data-permission-group {
  width: 100%;
}

.data-group {
  margin-bottom: 24px;
  border: 1px solid #e4e7ed;
  border-radius: 6px;
  overflow: hidden;
}

.group-title {
  background: #f5f7fa;
  padding: 12px 16px;
  font-weight: 600;
  color: #303133;
  border-bottom: 1px solid #e4e7ed;
}

.group-options {
  padding: 16px;
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.data-option {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.option-name {
  font-weight: 500;
  color: #303133;
}

.option-desc {
  font-size: 13px;
  color: #606266;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .page-header.fixed-header {
    padding: 12px 16px;
    flex-direction: column;
    gap: 12px;
    height: auto;
    margin: -20px -16px 20px -16px;
    /* 调整移动端的margin */
  }

  .header-left {
    width: 100%;
    justify-content: flex-start;
  }

  .page-title {
    font-size: 16px;
    max-width: none;
  }

  .header-actions {
    width: 100%;
    justify-content: flex-end;
  }
}

@media (max-width: 480px) {
  .page-header.fixed-header {
    padding: 10px 12px;
    margin: -20px -12px 20px -12px;
    /* 调整小屏幕的margin */
  }

  .page-title {
    font-size: 14px;
  }

  .header-actions {
    gap: 8px;
  }
}
</style>
