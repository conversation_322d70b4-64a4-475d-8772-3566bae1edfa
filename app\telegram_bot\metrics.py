"""
Telegram Bot 指标服务器
"""

import asyncio
import os
from datetime import datetime
from typing import Dict, Any

from aiohttp import web
from app.core.logging import get_logger

logger = get_logger(__name__)

class MetricsServer:
    """Prometheus指标服务器"""
    
    def __init__(self, bot_service):
        self.bot_service = bot_service
        self.app = None
        self.runner = None
        self.site = None
        self.port = int(os.getenv('PROMETHEUS_PORT', 9090))
        self.metrics = {
            'commands_processed_total': 0,
            'messages_sent_total': 0,
            'errors_total': 0,
            'active_groups': 0,
            'active_users': 0
        }
    
    async def start(self):
        """启动指标服务器"""
        try:
            self.app = web.Application()
            self.app.router.add_get('/metrics', self.get_metrics)
            
            self.runner = web.AppRunner(self.app)
            await self.runner.setup()
            
            self.site = web.TCPSite(self.runner, '0.0.0.0', self.port)
            await self.site.start()
            
            logger.info(f"指标服务器启动在端口 {self.port}")
            
        except Exception as e:
            logger.error(f"启动指标服务器失败: {e}", exc_info=True)
            raise
    
    async def stop(self):
        """停止指标服务器"""
        try:
            if self.site:
                await self.site.stop()
            if self.runner:
                await self.runner.cleanup()
            logger.info("指标服务器已停止")
        except Exception as e:
            logger.error(f"停止指标服务器失败: {e}", exc_info=True)
    
    async def get_metrics(self, request):
        """获取Prometheus格式的指标"""
        try:
            # 更新指标
            await self._update_metrics()
            
            # 生成Prometheus格式的指标
            metrics_text = self._format_prometheus_metrics()
            
            return web.Response(
                text=metrics_text,
                content_type='text/plain; version=0.0.4; charset=utf-8'
            )
            
        except Exception as e:
            logger.error(f"获取指标失败: {e}", exc_info=True)
            return web.Response(
                text="# Error getting metrics\n",
                content_type='text/plain',
                status=500
            )
    
    async def _update_metrics(self):
        """更新指标数据"""
        try:
            if self.bot_service:
                status = self.bot_service.get_status()
                # 这里可以从bot_service获取实际的指标数据
                # 暂时使用模拟数据
                pass
        except Exception as e:
            logger.error(f"更新指标失败: {e}", exc_info=True)
    
    def _format_prometheus_metrics(self) -> str:
        """格式化为Prometheus指标格式"""
        timestamp = int(datetime.utcnow().timestamp() * 1000)
        
        metrics_lines = [
            "# HELP telegram_bot_commands_processed_total Total number of commands processed",
            "# TYPE telegram_bot_commands_processed_total counter",
            f"telegram_bot_commands_processed_total {self.metrics['commands_processed_total']} {timestamp}",
            "",
            "# HELP telegram_bot_messages_sent_total Total number of messages sent",
            "# TYPE telegram_bot_messages_sent_total counter", 
            f"telegram_bot_messages_sent_total {self.metrics['messages_sent_total']} {timestamp}",
            "",
            "# HELP telegram_bot_errors_total Total number of errors",
            "# TYPE telegram_bot_errors_total counter",
            f"telegram_bot_errors_total {self.metrics['errors_total']} {timestamp}",
            "",
            "# HELP telegram_bot_active_groups Number of active groups",
            "# TYPE telegram_bot_active_groups gauge",
            f"telegram_bot_active_groups {self.metrics['active_groups']} {timestamp}",
            "",
            "# HELP telegram_bot_active_users Number of active users",
            "# TYPE telegram_bot_active_users gauge",
            f"telegram_bot_active_users {self.metrics['active_users']} {timestamp}",
            "",
            "# HELP telegram_bot_up Bot service status (1 = up, 0 = down)",
            "# TYPE telegram_bot_up gauge",
            f"telegram_bot_up {1 if self.bot_service and self.bot_service.is_running() else 0} {timestamp}",
            ""
        ]
        
        return "\n".join(metrics_lines)
    
    def increment_counter(self, metric_name: str, value: int = 1):
        """增加计数器指标"""
        if metric_name in self.metrics:
            self.metrics[metric_name] += value
    
    def set_gauge(self, metric_name: str, value: int):
        """设置仪表指标"""
        if metric_name in self.metrics:
            self.metrics[metric_name] = value
