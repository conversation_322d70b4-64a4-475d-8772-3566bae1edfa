package model

import (
	"time"

	"gorm.io/gorm"
)

// BindingLog 绑卡日志模型 - 与数据库表结构完全匹配
type BindingLog struct {
	// 基础字段
	ID        string    `gorm:"column:id;type:varchar(36);primarykey;comment:日志ID（UUID）" json:"id"`
	CreatedAt time.Time `gorm:"column:created_at;autoCreateTime;comment:创建时间" json:"created_at"`
	UpdatedAt time.Time `gorm:"column:updated_at;autoUpdateTime;comment:更新时间" json:"updated_at"`
	// 注意：数据库表中没有deleted_at字段，所以不使用gorm.DeletedAt

	// 关联字段
	CardRecordID string `gorm:"column:card_record_id;type:varchar(36);not null;comment:关联的卡记录ID" json:"card_record_id"`
	MerchantID   *uint  `gorm:"column:merchant_id;comment:商户ID（用于数据隔离）" json:"merchant_id"`
	DepartmentID *uint  `gorm:"column:department_id;comment:部门ID（用于数据隔离）" json:"department_id"`

	// 日志信息
	LogType      string  `gorm:"column:log_type;type:varchar(20);not null;default:system;comment:日志类型" json:"log_type"`
	LogLevel     string  `gorm:"column:log_level;type:varchar(20);not null;default:info;comment:日志级别" json:"log_level"`
	Message      string  `gorm:"column:message;type:text;not null;comment:日志消息" json:"message"`
	Details      *string `gorm:"column:details;type:json;comment:详细信息" json:"details"`
	RequestData  *string `gorm:"column:request_data;type:json;comment:请求数据" json:"request_data"`
	ResponseData *string `gorm:"column:response_data;type:json;comment:响应数据" json:"response_data"`

	// 性能和追踪字段
	DurationMS     *float64 `gorm:"column:duration_ms;comment:操作耗时(毫秒)" json:"duration_ms"`
	AttemptNumber  *string  `gorm:"column:attempt_number;type:varchar(10);comment:尝试序号" json:"attempt_number"`
	WalmartCKID    *uint    `gorm:"column:walmart_ck_id;comment:沃尔玛CKID（关键字段：用于统计CK绑卡成功数）" json:"walmart_ck_id"`
	IPAddress      *string  `gorm:"column:ip_address;type:varchar(64);comment:IP地址" json:"ip_address"`
	Timestamp      time.Time `gorm:"column:timestamp;not null;default:CURRENT_TIMESTAMP(3);comment:日志时间戳" json:"timestamp"`
}

// BeforeCreate GORM钩子，在创建前设置时间
func (bl *BindingLog) BeforeCreate(tx *gorm.DB) error {
	now := time.Now()
	if bl.CreatedAt.IsZero() {
		bl.CreatedAt = now
	}
	if bl.UpdatedAt.IsZero() {
		bl.UpdatedAt = now
	}
	return nil
}

// TableName 指定表名
func (BindingLog) TableName() string {
	return "binding_logs"
}