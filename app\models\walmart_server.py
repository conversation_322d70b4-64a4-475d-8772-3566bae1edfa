from sqlalchemy import Column, String, Integer, Boolean, Text, JSON
from app.models.base import BaseModel, TimestampMixin


class WalmartServer(BaseModel, TimestampMixin):
    """沃尔玛API配置模型"""

    __tablename__ = "walmart_server"

    # API基本配置
    api_url = Column(String(255), nullable=False, comment="API地址")
    referer = Column(
        String(500),
        nullable=False,
        default="https://servicewechat.com/wx81d3e1fe4c2e11b4/175/page-frame.html",
        comment="HTTP请求Referer头"
    )

    # 请求配置
    timeout = Column(Integer, default=30, nullable=False, comment="请求超时时间(秒)")
    retry_count = Column(Integer, default=3, nullable=False, comment="重试次数")

    # 系统限制配置
    daily_bind_limit = Column(
        Integer, default=1000, nullable=False, comment="每日绑卡限制"
    )
    api_rate_limit = Column(
        Integer, default=60, nullable=False, comment="API速率限制(次/分钟)"
    )
    max_retry_times = Column(Integer, default=3, nullable=False, comment="最大重试次数")
    bind_timeout_seconds = Column(
        Integer, default=30, nullable=False, comment="绑卡超时时间(秒)"
    )
    verification_code_expires = Column(
        Integer, default=300, nullable=False, comment="验证码过期时间(秒)"
    )
    log_retention_days = Column(
        Integer, default=90, nullable=False, comment="日志保留天数"
    )

    # 功能开关
    enable_ip_whitelist = Column(
        Boolean, default=True, nullable=False, comment="是否启用IP白名单"
    )
    enable_security_audit = Column(
        Boolean, default=True, nullable=False, comment="是否启用安全审计"
    )
    maintenance_mode = Column(
        Boolean, default=False, nullable=False, comment="维护模式"
    )
    maintenance_message = Column(Text, nullable=True, comment="维护模式消息")

    # 状态
    is_active = Column(Boolean, default=True, nullable=False, comment="是否激活")

    # 附加配置
    extra_config = Column(JSON, nullable=True, comment="附加配置")

    def to_dict(self, include_sensitive=False):
        """转换为字典

        Args:
            include_sensitive: 是否包含敏感信息

        Returns:
            dict: 配置字典
        """
        data = super().to_dict()

        # 屏蔽敏感信息
        if not include_sensitive:
            if data.get("secret_key"):
                data["secret_key"] = "******"

        return data
