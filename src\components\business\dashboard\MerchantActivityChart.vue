<template>
    <div class="merchant-activity-chart">
        <el-card shadow="hover" class="chart-card">
            <template #header>
                <div class="card-header">
                    <span>商家活跃度对比</span>
                    <div class="chart-controls">
                        <el-select v-model="timeRange" placeholder="时间范围" size="small" @change="refreshData">
                            <el-option label="今日" value="today" />
                            <el-option label="本周" value="week" />
                            <el-option label="本月" value="month" />
                            <el-option label="上季度" value="quarter" />
                        </el-select>
                        <el-select v-model="sortBy" placeholder="排序方式" size="small" @change="refreshData">
                            <el-option label="绑卡总量" value="total" />
                            <el-option label="成功率" value="successRate" />
                            <el-option label="平均处理时间" value="avgTime" />
                            <el-option label="增长率" value="growthRate" />
                        </el-select>
                        <el-tooltip content="刷新数据" placement="top">
                            <el-button type="primary" size="small" icon="Refresh" @click="refreshData"></el-button>
                        </el-tooltip>
                        <el-tooltip content="切换视图" placement="top">
                            <el-button type="info" size="small" icon="SetUp" @click="toggleView"></el-button>
                        </el-tooltip>
                    </div>
                </div>
            </template>

            <div class="chart-wrapper" v-loading="loading">
                <div ref="chartRef" class="chart-container"></div>
            </div>

            <div class="chart-summary" v-if="!loading">
                <div class="summary-table">
                    <el-table :data="tableData" style="width: 100%" size="small" max-height="200px">
                        <el-table-column prop="name" label="商家名称" width="150" />
                        <el-table-column prop="total" label="绑卡总量" width="100" sortable />
                        <el-table-column prop="successRate" label="成功率" width="100" sortable>
                            <template #default="scope">
                                {{ scope.row.successRate }}%
                            </template>
                        </el-table-column>
                        <el-table-column prop="avgTime" label="平均处理时间" width="120" sortable>
                            <template #default="scope">
                                {{ scope.row.avgTime }}s
                            </template>
                        </el-table-column>
                        <el-table-column prop="growthRate" label="增长率" sortable>
                            <template #default="scope">
                                <span :class="scope.row.growthRate >= 0 ? 'positive-change' : 'negative-change'">
                                    {{ scope.row.growthRate >= 0 ? '+' : '' }}{{ scope.row.growthRate }}%
                                </span>
                            </template>
                        </el-table-column>
                    </el-table>
                </div>
            </div>
        </el-card>
    </div>
</template>

<script setup>
import { ref, onMounted, onBeforeUnmount } from 'vue'
import * as echarts from 'echarts/core'
import { BarChart, LineChart } from 'echarts/charts'
import {
    TitleComponent,
    TooltipComponent,
    LegendComponent,
    GridComponent,
    ToolboxComponent,
    DataZoomComponent
} from 'echarts/components'
import { CanvasRenderer } from 'echarts/renderers'
import { ElMessage } from 'element-plus'
import { dashboardApi } from '@/api/modules/dashboardApi'

// 注册ECharts必须的组件
echarts.use([
    BarChart,
    LineChart,
    TitleComponent,
    TooltipComponent,
    LegendComponent,
    GridComponent,
    ToolboxComponent,
    DataZoomComponent,
    CanvasRenderer
])

// 图表状态
const loading = ref(false)
const chartRef = ref(null)
const chart = ref(null)
const timeRange = ref('week')
const sortBy = ref('total')
const viewMode = ref('bar') // 'bar' 或 'line'
const tableData = ref([])

// 获取商家活跃度数据
const fetchData = async () => {
    loading.value = true
    try {
        // 调用API获取商家活跃度数据
        const data = await dashboardApi.getMerchantActivity(timeRange.value, sortBy.value)

        // 如果没有数据，使用模拟数据
        if (!data || !data.merchants || data.merchants.length === 0) {
            const mockData = generateMockData()
            updateChart(mockData)
            tableData.value = mockData.merchants
        } else {
            updateChart(data)
            tableData.value = data.merchants
        }
    } catch (error) {
        console.error('获取商家活跃度数据失败:', error)
        ElMessage.error('获取商家活跃度数据失败')
        // 出错时使用模拟数据
        const mockData = generateMockData()
        updateChart(mockData)
        tableData.value = mockData.merchants
    } finally {
        loading.value = false
    }
}

// 生成模拟数据
const generateMockData = () => {
    const merchants = [
        {
            name: '京东商城',
            total: Math.floor(Math.random() * 500) + 800,
            successRate: (Math.random() * 20 + 75).toFixed(1),
            avgTime: (Math.random() * 2 + 1).toFixed(1),
            growthRate: (Math.random() * 40 - 10).toFixed(1)
        },
        {
            name: '淘宝网',
            total: Math.floor(Math.random() * 500) + 700,
            successRate: (Math.random() * 20 + 70).toFixed(1),
            avgTime: (Math.random() * 2 + 1.5).toFixed(1),
            growthRate: (Math.random() * 40 - 5).toFixed(1)
        },
        {
            name: '苏宁易购',
            total: Math.floor(Math.random() * 400) + 500,
            successRate: (Math.random() * 25 + 65).toFixed(1),
            avgTime: (Math.random() * 3 + 2).toFixed(1),
            growthRate: (Math.random() * 30 - 5).toFixed(1)
        },
        {
            name: '唯品会',
            total: Math.floor(Math.random() * 300) + 400,
            successRate: (Math.random() * 20 + 70).toFixed(1),
            avgTime: (Math.random() * 2 + 1.8).toFixed(1),
            growthRate: (Math.random() * 50 - 10).toFixed(1)
        },
        {
            name: '拼多多',
            total: Math.floor(Math.random() * 500) + 600,
            successRate: (Math.random() * 15 + 75).toFixed(1),
            avgTime: (Math.random() * 1.5 + 1.2).toFixed(1),
            growthRate: (Math.random() * 60).toFixed(1)
        },
        {
            name: '美团',
            total: Math.floor(Math.random() * 300) + 350,
            successRate: (Math.random() * 20 + 70).toFixed(1),
            avgTime: (Math.random() * 2.5 + 1.5).toFixed(1),
            growthRate: (Math.random() * 40 - 5).toFixed(1)
        },
        {
            name: '饿了么',
            total: Math.floor(Math.random() * 250) + 300,
            successRate: (Math.random() * 25 + 65).toFixed(1),
            avgTime: (Math.random() * 3 + 2).toFixed(1),
            growthRate: (Math.random() * 30 - 10).toFixed(1)
        },
        {
            name: '小红书',
            total: Math.floor(Math.random() * 200) + 250,
            successRate: (Math.random() * 20 + 70).toFixed(1),
            avgTime: (Math.random() * 2 + 1.8).toFixed(1),
            growthRate: (Math.random() * 70).toFixed(1)
        }
    ]

    // 按选择的排序方式排序
    if (sortBy.value === 'total') {
        merchants.sort((a, b) => b.total - a.total)
    } else if (sortBy.value === 'successRate') {
        merchants.sort((a, b) => b.successRate - a.successRate)
    } else if (sortBy.value === 'avgTime') {
        merchants.sort((a, b) => a.avgTime - b.avgTime)  // 时间越短越好，所以升序排序
    } else if (sortBy.value === 'growthRate') {
        merchants.sort((a, b) => b.growthRate - a.growthRate)
    }

    return {
        merchants: merchants,
        timeRange: timeRange.value
    }
}

// 初始化图表
const initChart = () => {
    if (!chartRef.value) return

    chart.value = echarts.init(chartRef.value)
    fetchData()

    // 监听窗口大小变化，重新调整图表大小
    window.addEventListener('resize', handleResize)
}

// 更新图表
const updateChart = (data) => {
    if (!chart.value) return

    const merchants = data.merchants
    const names = merchants.map(m => m.name)
    let seriesData = []

    if (sortBy.value === 'total') {
        seriesData = merchants.map(m => m.total)
    } else if (sortBy.value === 'successRate') {
        seriesData = merchants.map(m => m.successRate)
    } else if (sortBy.value === 'avgTime') {
        seriesData = merchants.map(m => m.avgTime)
    } else if (sortBy.value === 'growthRate') {
        seriesData = merchants.map(m => m.growthRate)
    }

    const option = {
        tooltip: {
            trigger: 'axis',
            axisPointer: {
                type: viewMode.value === 'bar' ? 'shadow' : 'line'
            }
        },
        grid: {
            left: '3%',
            right: '4%',
            bottom: '3%',
            containLabel: true
        },
        xAxis: {
            type: 'category',
            data: names,
            axisLabel: {
                interval: 0,
                rotate: 30
            }
        },
        yAxis: {
            type: 'value',
            name: getSortByLabel()
        },
        series: [
            {
                name: getSortByLabel(),
                type: viewMode.value,
                data: seriesData,
                itemStyle: {
                    color: function (params) {
                        // 根据不同的指标使用不同的颜色逻辑
                        if (sortBy.value === 'growthRate') {
                            return seriesData[params.dataIndex] >= 0 ? '#67C23A' : '#F56C6C'
                        }
                        // 默认颜色循环
                        const colorList = ['#409EFF', '#67C23A', '#E6A23C', '#F56C6C', '#909399', '#9B63CD', '#3ECBBC', '#F44336']
                        return colorList[params.dataIndex % colorList.length]
                    }
                },
                label: {
                    show: true,
                    position: 'top',
                    formatter: function (params) {
                        if (sortBy.value === 'successRate' || sortBy.value === 'growthRate') {
                            return params.value + '%'
                        } else if (sortBy.value === 'avgTime') {
                            return params.value + 's'
                        }
                        return params.value
                    }
                }
            }
        ],
        dataZoom: [
            {
                type: 'slider',
                show: merchants.length > 6,
                start: 0,
                end: 100
            }
        ]
    }

    chart.value.setOption(option)
}

// 获取排序指标的标签
const getSortByLabel = () => {
    switch (sortBy.value) {
        case 'total':
            return '绑卡总量'
        case 'successRate':
            return '成功率(%)'
        case 'avgTime':
            return '平均处理时间(s)'
        case 'growthRate':
            return '增长率(%)'
        default:
            return '绑卡总量'
    }
}

// 切换视图
const toggleView = () => {
    viewMode.value = viewMode.value === 'bar' ? 'line' : 'bar'
    fetchData()
}

// 刷新数据
const refreshData = () => {
    fetchData()
}

// 调整图表大小
const handleResize = () => {
    chart.value?.resize()
}

onMounted(() => {
    initChart()
})

onBeforeUnmount(() => {
    window.removeEventListener('resize', handleResize)
    chart.value?.dispose()
})
</script>

<style scoped>
.merchant-activity-chart {
    margin-bottom: 20px;
}

.chart-card {
    width: 100%;
}

.card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.chart-controls {
    display: flex;
    align-items: center;
    gap: 10px;
}

.chart-wrapper {
    padding: 10px 0;
}

.chart-container {
    height: 400px;
    width: 100%;
}

.chart-summary {
    margin-top: 15px;
}

.summary-table {
    width: 100%;
}

.positive-change {
    color: #67C23A;
}

.negative-change {
    color: #F56C6C;
}
</style>