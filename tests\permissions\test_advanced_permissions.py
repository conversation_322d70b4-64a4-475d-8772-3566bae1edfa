"""
高级权限功能测试
测试权限继承显示、冲突检测等功能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(__file__)))

from conftest import TestBase, get_test_accounts, format_test_result


class TestAdvancedPermissions:
    """高级权限功能测试类"""

    def __init__(self):
        self.test_base = TestBase()
        self.accounts = get_test_accounts()
        self.results = []

    def test_api_permissions_initialization(self):
        """测试API权限初始化"""
        print("\n=== 测试API权限初始化 ===")
        
        # 登录超级管理员
        admin_token = self.test_base.login(
            self.accounts["super_admin"]["username"],
            self.accounts["super_admin"]["password"]
        )
        
        if not admin_token:
            result = format_test_result(
                "API权限初始化", False, "超级管理员登录失败"
            )
            self.results.append(result)
            return result
        
        # 调用权限初始化API
        status_code, response = self.test_base.make_request(
            "POST", "/permissions-advanced/init-complete-permissions", admin_token
        )
        
        success = status_code == 200 and response.get("success", False)
        message = response.get("message", "未知错误") if not success else "API权限初始化成功"
        
        result = format_test_result(
            "API权限初始化", success, message, {
                "status_code": status_code,
                "response": response
            }
        )
        self.results.append(result)
        return result

    def test_permission_conflicts_scan(self):
        """测试权限冲突扫描"""
        print("\n=== 测试权限冲突扫描 ===")
        
        # 登录超级管理员
        admin_token = self.test_base.login(
            self.accounts["super_admin"]["username"],
            self.accounts["super_admin"]["password"]
        )
        
        if not admin_token:
            result = format_test_result(
                "权限冲突扫描", False, "超级管理员登录失败"
            )
            self.results.append(result)
            return result
        
        # 调用权限冲突扫描API
        status_code, response = self.test_base.make_request(
            "GET", "/permissions-advanced/conflicts/scan", admin_token
        )
        
        success = status_code == 200 and response.get("success", False)
        
        if success:
            conflict_data = response.get("data", {})
            total_conflicts = conflict_data.get("total_conflicts", 0)
            message = f"权限冲突扫描完成，发现 {total_conflicts} 个冲突"
        else:
            message = response.get("message", "权限冲突扫描失败")
        
        result = format_test_result(
            "权限冲突扫描", success, message, {
                "status_code": status_code,
                "response": response
            }
        )
        self.results.append(result)
        return result

    def test_permission_inheritance_api(self):
        """测试权限继承API"""
        print("\n=== 测试权限继承API ===")
        
        # 登录超级管理员
        admin_token = self.test_base.login(
            self.accounts["super_admin"]["username"],
            self.accounts["super_admin"]["password"]
        )
        
        if not admin_token:
            result = format_test_result(
                "权限继承API", False, "超级管理员登录失败"
            )
            self.results.append(result)
            return result
        
        # 测试获取角色权限继承信息（假设角色ID为2是商户管理员）
        status_code, response = self.test_base.make_request(
            "GET", "/permissions-advanced/inheritance/role/2", admin_token
        )
        
        success = status_code == 200 and response.get("success", False)
        
        if success:
            inheritance_data = response.get("data", {})
            role_name = inheritance_data.get("role_name", "未知")
            permissions_count = len(inheritance_data.get("direct_permissions", []))
            message = f"获取角色 {role_name} 权限继承信息成功，共 {permissions_count} 个权限"
        else:
            message = response.get("message", "获取权限继承信息失败")
        
        result = format_test_result(
            "权限继承API", success, message, {
                "status_code": status_code,
                "response": response
            }
        )
        self.results.append(result)
        return result

    def test_permission_usage_api(self):
        """测试权限使用信息API"""
        print("\n=== 测试权限使用信息API ===")
        
        # 登录超级管理员
        admin_token = self.test_base.login(
            self.accounts["super_admin"]["username"],
            self.accounts["super_admin"]["password"]
        )
        
        if not admin_token:
            result = format_test_result(
                "权限使用信息API", False, "超级管理员登录失败"
            )
            self.results.append(result)
            return result
        
        # 测试获取特定权限的使用信息
        permission_code = "api:users:list"
        status_code, response = self.test_base.make_request(
            "GET", f"/permissions-advanced/usage/{permission_code}", admin_token
        )
        
        success = status_code == 200 and response.get("success", False)
        
        if success:
            usage_data = response.get("data", {})
            stats = usage_data.get("usage_statistics", {})
            total_roles = stats.get("total_roles", 0)
            total_users = stats.get("total_users", 0)
            message = f"权限 {permission_code} 使用信息：{total_roles} 个角色，{total_users} 个用户"
        else:
            message = response.get("message", "获取权限使用信息失败")
        
        result = format_test_result(
            "权限使用信息API", success, message, {
                "status_code": status_code,
                "response": response
            }
        )
        self.results.append(result)
        return result

    def test_permissions_list_api(self):
        """测试权限列表API"""
        print("\n=== 测试权限列表API ===")
        
        # 登录超级管理员
        admin_token = self.test_base.login(
            self.accounts["super_admin"]["username"],
            self.accounts["super_admin"]["password"]
        )
        
        if not admin_token:
            result = format_test_result(
                "权限列表API", False, "超级管理员登录失败"
            )
            self.results.append(result)
            return result
        
        # 测试获取API权限列表
        status_code, response = self.test_base.make_request(
            "GET", "/permissions?resource_type=api&page_size=1000", admin_token
        )
        
        success = status_code == 200 and response.get("success", False)
        
        if success:
            data = response.get("data", {})
            total = data.get("total", 0)
            items = data.get("items", [])
            api_permissions = [item for item in items if item.get("resource_type") == "api"]
            message = f"获取权限列表成功，总计 {total} 个权限，其中 {len(api_permissions)} 个API权限"
        else:
            message = response.get("message", "获取权限列表失败")
        
        result = format_test_result(
            "权限列表API", success, message, {
                "status_code": status_code,
                "response": response
            }
        )
        self.results.append(result)
        return result

    def run_all_tests(self):
        """运行所有测试"""
        print("开始运行高级权限功能测试...")
        
        # 运行所有测试
        self.test_api_permissions_initialization()
        self.test_permission_conflicts_scan()
        self.test_permission_inheritance_api()
        self.test_permission_usage_api()
        self.test_permissions_list_api()
        
        return self.results


def main():
    """主函数"""
    test_runner = TestAdvancedPermissions()
    results = test_runner.run_all_tests()
    
    # 打印测试结果
    print(f"\n{'='*60}")
    print("高级权限功能测试结果")
    print(f"{'='*60}")
    
    for result in results:
        status = "✅ 通过" if result["success"] else "❌ 失败"
        print(f"{status} {result['test_name']}: {result['message']}")
    
    # 统计
    total = len(results)
    passed = sum(1 for r in results if r["success"])
    failed = total - passed
    success_rate = (passed / total * 100) if total > 0 else 0
    
    print(f"\n总测试数: {total}")
    print(f"通过数: {passed}")
    print(f"失败数: {failed}")
    print(f"成功率: {success_rate:.1f}%")
    
    return results


if __name__ == "__main__":
    main()
