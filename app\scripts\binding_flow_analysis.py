#!/usr/bin/env python3
"""
绑卡流程深度分析
分析绑卡流程中的数据一致性问题和潜在风险
"""

import asyncio
import sys
import os
from typing import Dict, List, Any
from datetime import datetime

# 添加项目路径
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.insert(0, project_root)

from app.api.deps import get_db
from app.models.walmart_ck import WalmartCK
from app.models.card_record import CardRecord
from app.core.logging import get_logger

logger = get_logger("binding_flow_analysis")


class BindingFlowAnalyzer:
    """绑卡流程分析器"""
    
    def __init__(self):
        self.db = next(get_db())
        
    def __enter__(self):
        return self
        
    def __exit__(self, exc_type, exc_val, exc_tb):
        if self.db:
            self.db.close()
    
    async def analyze_binding_flow_risks(self):
        """分析绑卡流程的数据一致性风险"""
        print(f"🔍 绑卡流程数据一致性风险分析")
        print("="*80)
        
        risks = []
        
        # 1. 分析事务边界问题
        transaction_risks = await self._analyze_transaction_boundaries()
        risks.extend(transaction_risks)
        
        # 2. 分析并发安全问题
        concurrency_risks = await self._analyze_concurrency_safety()
        risks.extend(concurrency_risks)
        
        # 3. 分析数据一致性问题
        consistency_risks = await self._analyze_data_consistency()
        risks.extend(consistency_risks)
        
        # 4. 分析异常处理问题
        exception_risks = await self._analyze_exception_handling()
        risks.extend(exception_risks)
        
        # 5. 分析CK使用统计问题
        ck_stats_risks = await self._analyze_ck_statistics()
        risks.extend(ck_stats_risks)
        
        # 生成风险报告
        await self._generate_risk_report(risks)
        
        return risks
    
    async def _analyze_transaction_boundaries(self) -> List[Dict]:
        """分析事务边界问题"""
        print(f"\n📊 1. 事务边界分析")
        print("-"*60)
        
        risks = []
        
        # 风险1：状态更新和CK使用记录分离
        risk1 = {
            'category': '事务边界',
            'level': 'HIGH',
            'title': '状态更新和CK使用记录不在同一事务',
            'description': '绑卡成功后，状态更新和CK使用统计记录在不同的事务中，可能导致数据不一致',
            'impact': '如果状态更新成功但CK统计失败，会导致CK计数不准确',
            'location': 'card_record_service.py:_update_bind_result',
            'solution': '将状态更新和CK使用记录放在同一个事务中'
        }
        risks.append(risk1)
        print(f"  ❌ {risk1['title']}")
        
        # 风险2：多次commit操作
        risk2 = {
            'category': '事务边界',
            'level': 'MEDIUM',
            'title': '绑卡流程中存在多次commit操作',
            'description': '在bind_card方法中，存在多次db.commit()调用，破坏了原子性',
            'impact': '如果中间某个步骤失败，前面的操作已经提交，无法回滚',
            'location': 'card_record_service.py:bind_card',
            'solution': '使用事务上下文管理器，确保原子性'
        }
        risks.append(risk2)
        print(f"  ⚠️ {risk2['title']}")
        
        return risks
    
    async def _analyze_concurrency_safety(self) -> List[Dict]:
        """分析并发安全问题"""
        print(f"\n🔄 2. 并发安全分析")
        print("-"*60)
        
        risks = []
        
        # 风险1：CK选择和状态更新的竞态条件
        risk1 = {
            'category': '并发安全',
            'level': 'HIGH',
            'title': 'CK选择和状态更新存在竞态条件',
            'description': 'Redis锁保护CK选择，但数据库状态更新没有相应保护',
            'impact': '多个请求可能同时更新同一个记录的状态',
            'location': 'binding_process_service.py',
            'solution': '使用数据库行锁或乐观锁保护状态更新'
        }
        risks.append(risk1)
        print(f"  ❌ {risk1['title']}")
        
        # 风险2：CK bind_count更新的并发问题
        risk2 = {
            'category': '并发安全',
            'level': 'HIGH',
            'title': 'CK bind_count更新存在并发问题',
            'description': 'record_ck_usage中的bind_count更新没有使用行锁',
            'impact': '高并发下可能导致bind_count计算错误',
            'location': 'walmart_ck_service_new.py:record_ck_usage',
            'solution': '使用with_for_update()行锁保护bind_count更新'
        }
        risks.append(risk2)
        print(f"  ❌ {risk2['title']}")
        
        return risks
    
    async def _analyze_data_consistency(self) -> List[Dict]:
        """分析数据一致性问题"""
        print(f"\n📋 3. 数据一致性分析")
        print("-"*60)
        
        risks = []
        
        # 检查实际的数据一致性
        inconsistencies = await self._check_data_inconsistencies()
        
        if inconsistencies:
            risk1 = {
                'category': '数据一致性',
                'level': 'CRITICAL',
                'title': '发现数据不一致问题',
                'description': f'发现 {len(inconsistencies)} 个数据不一致问题',
                'impact': '影响业务逻辑和统计准确性',
                'details': inconsistencies,
                'solution': '运行数据修复脚本'
            }
            risks.append(risk1)
            print(f"  🚨 {risk1['title']}: {len(inconsistencies)} 个问题")
        else:
            print(f"  ✅ 当前数据一致性良好")
        
        return risks
    
    async def _analyze_exception_handling(self) -> List[Dict]:
        """分析异常处理问题"""
        print(f"\n⚠️ 4. 异常处理分析")
        print("-"*60)
        
        risks = []
        
        # 风险1：部分异常处理不完整
        risk1 = {
            'category': '异常处理',
            'level': 'MEDIUM',
            'title': '部分异常处理可能掩盖问题',
            'description': 'CK使用统计失败时只记录日志，不影响主流程',
            'impact': '可能导致CK统计数据丢失而不被发现',
            'location': 'binding_process_service.py:_save_ck_information',
            'solution': '增加监控和告警机制'
        }
        risks.append(risk1)
        print(f"  ⚠️ {risk1['title']}")
        
        return risks
    
    async def _analyze_ck_statistics(self) -> List[Dict]:
        """分析CK使用统计问题"""
        print(f"\n📈 5. CK使用统计分析")
        print("-"*60)
        
        risks = []
        
        # 检查CK统计准确性
        ck_stats_issues = await self._check_ck_statistics_accuracy()
        
        if ck_stats_issues:
            risk1 = {
                'category': 'CK统计',
                'level': 'HIGH',
                'title': 'CK使用统计不准确',
                'description': f'发现 {len(ck_stats_issues)} 个CK统计问题',
                'impact': '影响负载均衡算法和CK管理',
                'details': ck_stats_issues,
                'solution': '运行CK统计修复脚本'
            }
            risks.append(risk1)
            print(f"  ❌ {risk1['title']}: {len(ck_stats_issues)} 个问题")
        else:
            print(f"  ✅ CK使用统计准确")
        
        return risks
    
    async def _check_data_inconsistencies(self) -> List[Dict]:
        """检查数据不一致问题"""
        inconsistencies = []
        
        try:
            # 检查状态为success但没有CK信息的记录
            records_without_ck = self.db.query(CardRecord).filter(
                CardRecord.status == 'success',
                CardRecord.walmart_ck_id.is_(None)
            ).all()
            
            if records_without_ck:
                inconsistencies.append({
                    'type': '成功记录缺少CK信息',
                    'count': len(records_without_ck),
                    'records': [r.id for r in records_without_ck[:5]]  # 只显示前5个
                })
            
            # 检查状态为success但CK不存在的记录
            invalid_ck_records = self.db.query(CardRecord).filter(
                CardRecord.status == 'success',
                CardRecord.walmart_ck_id.isnot(None)
            ).outerjoin(WalmartCK, CardRecord.walmart_ck_id == WalmartCK.id).filter(
                WalmartCK.id.is_(None)
            ).all()
            
            if invalid_ck_records:
                inconsistencies.append({
                    'type': '成功记录引用不存在的CK',
                    'count': len(invalid_ck_records),
                    'records': [r.id for r in invalid_ck_records[:5]]
                })
            
        except Exception as e:
            logger.error(f"检查数据一致性失败: {e}")
        
        return inconsistencies
    
    async def _check_ck_statistics_accuracy(self) -> List[Dict]:
        """检查CK统计准确性"""
        issues = []
        
        try:
            # 获取所有CK
            cks = self.db.query(WalmartCK).filter(WalmartCK.is_deleted == False).all()
            
            for ck in cks:
                # 计算实际成功绑卡次数
                actual_count = self.db.query(CardRecord).filter(
                    CardRecord.walmart_ck_id == ck.id,
                    CardRecord.status == 'success'
                ).count()
                
                # 比较with bind_count
                if actual_count != ck.bind_count:
                    issues.append({
                        'ck_id': ck.id,
                        'merchant_id': ck.merchant_id,
                        'bind_count': ck.bind_count,
                        'actual_count': actual_count,
                        'difference': ck.bind_count - actual_count
                    })
        
        except Exception as e:
            logger.error(f"检查CK统计准确性失败: {e}")
        
        return issues
    
    async def _generate_risk_report(self, risks: List[Dict]):
        """生成风险报告"""
        print(f"\n" + "="*80)
        print("📊 绑卡流程风险评估报告")
        print("="*80)
        
        # 按风险级别分类
        critical_risks = [r for r in risks if r.get('level') == 'CRITICAL']
        high_risks = [r for r in risks if r.get('level') == 'HIGH']
        medium_risks = [r for r in risks if r.get('level') == 'MEDIUM']
        
        print(f"风险统计:")
        print(f"  🚨 严重风险: {len(critical_risks)}")
        print(f"  ❌ 高风险: {len(high_risks)}")
        print(f"  ⚠️ 中风险: {len(medium_risks)}")
        print(f"  总计: {len(risks)}")
        
        # 详细风险列表
        if critical_risks:
            print(f"\n🚨 严重风险:")
            for risk in critical_risks:
                print(f"  - {risk['title']}")
                print(f"    影响: {risk['impact']}")
                print(f"    解决方案: {risk['solution']}")
        
        if high_risks:
            print(f"\n❌ 高风险:")
            for risk in high_risks:
                print(f"  - {risk['title']}")
                print(f"    影响: {risk['impact']}")
                print(f"    解决方案: {risk['solution']}")
        
        if medium_risks:
            print(f"\n⚠️ 中风险:")
            for risk in medium_risks:
                print(f"  - {risk['title']}")
                print(f"    解决方案: {risk['solution']}")
        
        # 优先级建议
        print(f"\n🎯 修复优先级建议:")
        if critical_risks:
            print(f"  1. 立即修复严重风险（影响数据完整性）")
        if high_risks:
            print(f"  2. 优先修复高风险（影响业务逻辑）")
        if medium_risks:
            print(f"  3. 计划修复中风险（提升系统稳定性）")
        
        if not risks:
            print(f"\n✅ 恭喜！未发现明显的数据一致性风险")


async def main():
    """主函数"""
    with BindingFlowAnalyzer() as analyzer:
        risks = await analyzer.analyze_binding_flow_risks()
        
        if risks:
            print(f"\n💡 建议:")
            print(f"  1. 立即运行数据一致性检查和修复")
            print(f"  2. 实施事务边界优化")
            print(f"  3. 加强并发控制机制")
            print(f"  4. 完善异常处理和监控")
        
    print(f"\n🎉 分析完成！")


if __name__ == "__main__":
    asyncio.run(main())
