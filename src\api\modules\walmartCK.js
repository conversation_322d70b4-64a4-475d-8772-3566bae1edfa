import { http } from "@/api/request";
import { API_URLS, replaceUrlParams } from "./config";

const { walmart_ck } = API_URLS;

/**
 * 沃尔玛CK配置相关API
 */
export const walmartCKApi = {
  // 获取用户配置列表
  getList(params = {}) {
    return http.get(walmart_ck.LIST, { params }).then((res) => {
      // 后端返回格式：{success: true, data: {...}, message: "..."}
      // 前端需要的是data字段的内容
      return res.data || res;
    });
  },

  // 获取用户配置详情
  getDetail(id) {
    const url = replaceUrlParams(walmart_ck.DETAIL, { id });
    return http.get(url).then((res) => {
      return res.data || res;
    });
  },

  // 创建用户配置
  create(data) {
    return http.post(walmart_ck.CREATE, data).then((res) => {
      return res.data || res;
    });
  },

  // 批量创建用户配置
  batchCreate(data) {
    return http.post(walmart_ck.BATCH_CREATE, data).then((res) => {
      return res.data || res;
    });
  },

  // 更新用户配置
  update(id, data) {
    const url = replaceUrlParams(walmart_ck.UPDATE, { id });
    return http.put(url, data).then((res) => {
      return res.data || res;
    });
  },

  // 删除用户配置
  delete(id) {
    const url = replaceUrlParams(walmart_ck.DELETE, { id }); // 修复：使用DELETE而不是UPDATE
    return http.delete(url).then((res) => {
      return res.data || res;
    });
  },

  // 批量删除用户配置
  batchDelete(ckIds) {
    return http.post(walmart_ck.BATCH_DELETE, { ck_ids: ckIds }).then((res) => {
      return res.data || res;
    });
  },

  // 批量启用用户配置
  batchEnable(ckIds) {
    return http.post(walmart_ck.BATCH_ENABLE, { ck_ids: ckIds }).then((res) => {
      return res.data || res;
    });
  },

  // 批量禁用用户配置
  batchDisable(ckIds) {
    return http
      .post(walmart_ck.BATCH_DISABLE, { ck_ids: ckIds })
      .then((res) => {
        return res.data || res;
      });
  },

  // 启用CK
  enable(id) {
    const url = replaceUrlParams(walmart_ck.ENABLE, { id });
    return http.post(url).then((res) => {
      return res.data || res;
    });
  },

  // 禁用CK
  disable(id) {
    const url = replaceUrlParams(walmart_ck.DISABLE, { id });
    return http.post(url).then((res) => {
      return res.data || res;
    });
  },

  // 同步CK到Redis
  syncToRedis(params = {}) {
    return http.post(walmart_ck.SYNC_REDIS, {}, { params }).then((res) => {
      return res.data || res;
    });
  },

  // 获取CK详细统计
  getStatistics(id, params = {}) {
    const url = replaceUrlParams(walmart_ck.SINGLE_STATISTICS, { id });
    return http.get(url, { params }).then((res) => {
      return res.data || res;
    });
  },

  // 获取CK每日趋势
  getDailyTrend(id, params = {}) {
    const url = replaceUrlParams(walmart_ck.DAILY_TREND, { id });
    return http.get(url, { params }).then((res) => {
      return res.data || res;
    });
  },

  // 获取CK失败原因分析
  getFailureAnalysis(id, params = {}) {
    const url = replaceUrlParams(walmart_ck.FAILURE_ANALYSIS, { id });
    return http.get(url, { params }).then((res) => {
      return res.data || res;
    });
  },

  // 获取CK绑卡金额统计
  getBindingAmountStatistics(params = {}) {
    return http
      .get(walmart_ck.BINDING_AMOUNT_STATISTICS, { params })
      .then((res) => {
        return res.data || res;
      });
  },

  // 验证单个CK有效性
  validateCK(ckId) {
    return http
      .post(`${walmart_ck.LIST}/validate-ck?ck_id=${ckId}`)
      .then((res) => {
        return res.data || res;
      });
  },

  // 批量验证CK有效性
  batchValidateCK(merchantId = null) {
    const params = merchantId ? `?merchant_id=${merchantId}` : "";
    return http
      .post(`${walmart_ck.LIST}/batch-validate-ck${params}`)
      .then((res) => {
        return res.data || res;
      });
  },

  // 获取CK验证统计信息
  getValidationStatistics(merchantId = null) {
    const params = merchantId ? `?merchant_id=${merchantId}` : "";
    return http
      .get(`${walmart_ck.LIST}/validation-statistics${params}`)
      .then((res) => {
        return res.data || res;
      });
  },

  // 导出CK数据
  exportData(params = {}) {
    // 构建查询参数
    const queryParams = {};
    if (params.format) queryParams.format = params.format;
    if (params.merchant_id) queryParams.merchant_id = params.merchant_id;
    if (params.department_id) queryParams.department_id = params.department_id;
    if (params.status !== undefined) queryParams.status = params.status;
    if (params.start_date) queryParams.start_date = params.start_date;
    if (params.end_date) queryParams.end_date = params.end_date;

    // 使用http实例进行请求，让axios处理响应类型
    return http
      .get(walmart_ck.EXPORT, {
        params: queryParams,
        responseType: "blob", // 重要：设置响应类型为blob
      })
      .then((response) => {
        // 获取文件名
        const contentDisposition = response.headers["content-disposition"];
        // 根据导出格式设置默认文件名
        const defaultExtension = params.format === "json" ? "json" : "csv";
        let filename = `walmart_ck_export.${defaultExtension}`;

        if (contentDisposition) {
          const filenameMatch = contentDisposition.match(/filename=(.+)/);
          if (filenameMatch) {
            filename = filenameMatch[1];
          }
        }

        return {
          blob: response.data,
          filename,
        };
      });
  },
};

export default walmartCKApi;
