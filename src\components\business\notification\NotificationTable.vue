<template>
  <div class="notification-table">
    <el-table :data="data" style="width: 100%">
      <el-table-column prop="title" label="标题" min-width="200">
        <template #default="{ row }">
          <div class="notification-title" :class="{ unread: row.status === 'unread' }">
            {{ row.title }}
          </div>
        </template>
      </el-table-column>
      
      <el-table-column prop="content" label="内容" min-width="300" show-overflow-tooltip />
      
      <el-table-column prop="type" label="类型" width="120">
        <template #default="{ row }">
          <el-tag :type="getTypeTag(row.type)">
            {{ getTypeLabel(row.type) }}
          </el-tag>
        </template>
      </el-table-column>
      
      <el-table-column prop="createTime" label="时间" width="180" />
      
      <el-table-column label="操作" width="120">
        <template #default="{ row }">
          <el-button 
            v-if="row.status === 'unread'"
            type="primary" 
            link 
            @click="handleMarkRead(row)"
          >
            标记已读
          </el-button>
          <el-button type="danger" link @click="handleDelete(row)">
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>

<script setup>
import { ElMessage, ElMessageBox } from 'element-plus'

const props = defineProps({
  data: {
    type: Array,
    required: true
  }
})

const getTypeTag = (type) => {
  const typeMap = {
    system: '',
    warning: 'warning',
    error: 'danger',
    success: 'success'
  }
  return typeMap[type] || 'info'
}

const getTypeLabel = (type) => {
  const labelMap = {
    system: '系统通知',
    warning: '警告',
    error: '错误',
    success: '成功'
  }
  return labelMap[type] || '其他'
}

const handleMarkRead = (row) => {
  // TODO: 实现标记已读逻辑
  ElMessage.success('已标记为已读')
}

const handleDelete = (row) => {
  ElMessageBox.confirm(
    '确定要删除这条通知吗？',
    '警告',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    }
  ).then(() => {
    // TODO: 实现删除通知逻辑
    ElMessage.success('删除成功')
  }).catch(() => {})
}
</script>

<style scoped>
.notification-title {
  font-weight: normal;
}
.notification-title.unread {
  font-weight: bold;
}
.notification-title.unread::before {
  content: '●';
  color: #409EFF;
  margin-right: 5px;
  font-size: 12px;
}
</style> 