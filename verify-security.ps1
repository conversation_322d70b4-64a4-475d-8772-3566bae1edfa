# 沃尔玛绑卡网关 - 安全构建验证脚本
# 验证生成的Linux可执行文件的安全性和防反编译特性
param(
    [string]$FilePath = "walmart-gateway-production",
    [switch]$Verbose = $false,
    [switch]$DetailedReport = $false,  # 生成详细报告
    [switch]$ExportReport = $false     # 导出验证报告
)

function Write-Info($message) { Write-Host "[INFO] $message" -ForegroundColor Green }
function Write-Success($message) { Write-Host "[SUCCESS] $message" -ForegroundColor Cyan }
function Write-Warning($message) { Write-Host "[WARN] $message" -ForegroundColor Yellow }
function Write-Error($message) { Write-Host "[ERROR] $message" -ForegroundColor Red }
function Write-Debug($message) { if ($Verbose) { Write-Host "[DEBUG] $message" -ForegroundColor Gray } }

function Main {
    Write-Host "=========================================" -ForegroundColor Magenta
    Write-Host "沃尔玛绑卡网关 - 安全构建验证" -ForegroundColor Magenta
    Write-Host "防反编译验证 | 安全特性检查" -ForegroundColor Magenta
    Write-Host "=========================================" -ForegroundColor Magenta
    Write-Output ""

    Write-Info "验证文件: $FilePath"
    if ($DetailedReport) { Write-Info "详细报告: 启用" }
    if ($ExportReport) { Write-Info "导出报告: 启用" }
    Write-Output ""

    # 检查文件存在性
    if (!(Test-Path $FilePath)) {
        Write-Error "文件不存在: $FilePath"
        Write-Info "请先运行 .\build-production.ps1 构建生产版本"
        return 1
    }

    # 初始化验证报告
    $global:SecurityReport = @{
        "FileName" = $FilePath
        "VerificationTime" = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
        "Tests" = @{}
        "Score" = 0
        "SecurityLevel" = ""
        "Recommendations" = @()
    }

    # 基本文件信息
    ShowFileInfo

    # 验证ELF格式
    if (!(VerifyELFFormat)) { return 1 }

    # 检查符号表
    CheckSymbolTable

    # 检查调试信息
    CheckDebugInfo

    # 检查字符串信息
    CheckStringInfo

    # 检查UPX压缩
    CheckUPXCompression

    # 反编译测试
    TestDecompilation

    # 高级安全检查
    if ($DetailedReport) {
        AdvancedSecurityChecks
    }

    # 安全评分
    CalculateSecurityScore

    # 导出报告
    if ($ExportReport) {
        ExportSecurityReport
    }

    Write-Success "安全验证完成！"
    return 0
}

function ShowFileInfo {
    Write-Info "=== 文件基本信息 ==="
    
    $fileInfo = Get-Item $FilePath
    $fileSizeMB = [math]::Round($fileInfo.Length / 1MB, 2)
    $fileSizeKB = [math]::Round($fileInfo.Length / 1KB, 2)
    
    Write-Info "文件名: $($fileInfo.Name)"
    Write-Info "文件大小: $fileSizeMB MB ($fileSizeKB KB)"
    Write-Info "创建时间: $($fileInfo.CreationTime)"
    Write-Info "修改时间: $($fileInfo.LastWriteTime)"
    
    # 计算文件哈希
    $hash = Get-FileHash $FilePath -Algorithm SHA256
    Write-Info "SHA256: $($hash.Hash)"
    
    Write-Output ""
}

function VerifyELFFormat {
    Write-Info "=== 验证ELF格式 ==="
    
    try {
        $fileBytes = [System.IO.File]::ReadAllBytes($FilePath)
        
        if ($fileBytes.Length -lt 16) {
            Write-Error "文件太小，不是有效的ELF文件"
            return $false
        }
        
        # 检查ELF魔数
        if ($fileBytes[0] -eq 0x7F -and $fileBytes[1] -eq 0x45 -and $fileBytes[2] -eq 0x4C -and $fileBytes[3] -eq 0x46) {
            Write-Success "✅ 确认为Linux ELF可执行文件"
            
            # 检查架构
            $class = $fileBytes[4]
            $data = $fileBytes[5]
            $machine = [BitConverter]::ToUInt16($fileBytes, 18)
            
            $archInfo = switch ($class) {
                1 { "32位" }
                2 { "64位" }
                default { "未知" }
            }
            
            $endianInfo = switch ($data) {
                1 { "小端序" }
                2 { "大端序" }
                default { "未知" }
            }
            
            Write-Info "架构: $archInfo $endianInfo"
            Write-Debug "机器类型: 0x$($machine.ToString('X4'))"
            
            return $true
        } else {
            Write-Error "不是有效的ELF文件"
            return $false
        }
    } catch {
        Write-Error "读取文件失败: $($_.Exception.Message)"
        return $false
    }
}

function CheckSymbolTable {
    Write-Info "=== 检查符号表 ==="
    
    # 尝试使用strings命令查找符号信息
    try {
        $fileContent = [System.IO.File]::ReadAllText($FilePath, [System.Text.Encoding]::ASCII)
        
        # 检查常见的Go符号
        $goSymbols = @(
            "runtime.main",
            "main.main",
            "go.buildinfo",
            "runtime.goexit",
            "runtime.newproc"
        )
        
        $foundSymbols = 0
        foreach ($symbol in $goSymbols) {
            if ($fileContent -match [regex]::Escape($symbol)) {
                $foundSymbols++
                Write-Debug "发现符号: $symbol"
            }
        }
        
        if ($foundSymbols -eq 0) {
            Write-Success "✅ 符号表已完全移除"
        } elseif ($foundSymbols -lt 3) {
            Write-Warning "⚠️ 部分符号仍然存在 ($foundSymbols/$($goSymbols.Count))"
        } else {
            Write-Warning "❌ 大量符号仍然存在 ($foundSymbols/$($goSymbols.Count))"
        }
        
    } catch {
        Write-Debug "符号表检查异常: $($_.Exception.Message)"
        Write-Info "无法读取符号信息（可能已被完全移除）"
    }
    
    Write-Output ""
}

function CheckDebugInfo {
    Write-Info "=== 检查调试信息 ==="
    
    try {
        $fileBytes = [System.IO.File]::ReadAllBytes($FilePath)
        $fileContent = [System.Text.Encoding]::ASCII.GetString($fileBytes)
        
        # 检查DWARF调试信息
        $debugSections = @(
            ".debug_info",
            ".debug_line",
            ".debug_frame",
            ".debug_abbrev",
            "DWARF"
        )
        
        $foundDebugInfo = 0
        foreach ($section in $debugSections) {
            if ($fileContent -match [regex]::Escape($section)) {
                $foundDebugInfo++
                Write-Debug "发现调试信息: $section"
            }
        }
        
        if ($foundDebugInfo -eq 0) {
            Write-Success "✅ 调试信息已完全移除"
        } else {
            Write-Warning "⚠️ 发现调试信息残留 ($foundDebugInfo 个)"
        }
        
    } catch {
        Write-Debug "调试信息检查异常: $($_.Exception.Message)"
        Write-Info "无法检查调试信息"
    }
    
    Write-Output ""
}

function CheckStringInfo {
    Write-Info "=== 检查字符串信息 ==="
    
    try {
        $fileBytes = [System.IO.File]::ReadAllBytes($FilePath)
        $fileContent = [System.Text.Encoding]::UTF8.GetString($fileBytes)
        
        # 检查敏感字符串
        $sensitiveStrings = @(
            "walmart",
            "gateway",
            "config.yaml",
            "main.go",
            "internal/",
            "pkg/"
        )
        
        $foundStrings = 0
        foreach ($str in $sensitiveStrings) {
            if ($fileContent -match [regex]::Escape($str)) {
                $foundStrings++
                Write-Debug "发现敏感字符串: $str"
            }
        }
        
        if ($foundStrings -eq 0) {
            Write-Success "✅ 敏感字符串已完全混淆"
        } elseif ($foundStrings -lt 3) {
            Write-Warning "⚠️ 部分敏感字符串仍然可见 ($foundStrings/$($sensitiveStrings.Count))"
        } else {
            Write-Warning "❌ 大量敏感字符串仍然可见 ($foundStrings/$($sensitiveStrings.Count))"
        }
        
    } catch {
        Write-Debug "字符串检查异常: $($_.Exception.Message)"
        Write-Info "无法检查字符串信息"
    }
    
    Write-Output ""
}

function CheckUPXCompression {
    Write-Info "=== 检查UPX压缩 ==="
    
    try {
        $fileBytes = [System.IO.File]::ReadAllBytes($FilePath)
        $fileContent = [System.Text.Encoding]::ASCII.GetString($fileBytes)
        
        # 检查UPX标识
        if ($fileContent -match "UPX!") {
            Write-Success "✅ 检测到UPX压缩保护"
            
            # 尝试获取UPX信息
            if (Get-Command upx -ErrorAction SilentlyContinue) {
                try {
                    $upxInfo = upx -l $FilePath 2>$null
                    if ($LASTEXITCODE -eq 0) {
                        Write-Info "UPX压缩信息:"
                        $upxInfo | ForEach-Object { Write-Debug "  $_" }
                    }
                } catch {
                    Write-Debug "无法获取UPX详细信息"
                }
            }
        } else {
            Write-Warning "⚠️ 未检测到UPX压缩"
            Write-Info "建议安装UPX以获得更好的保护"
        }
        
    } catch {
        Write-Debug "UPX检查异常: $($_.Exception.Message)"
        Write-Info "无法检查UPX压缩状态"
    }
    
    Write-Output ""
}

function TestDecompilation {
    Write-Info "=== 反编译测试 ==="
    
    Write-Info "模拟常见反编译工具测试..."
    
    # 测试1: 检查是否包含Go构建信息
    try {
        $fileContent = [System.IO.File]::ReadAllText($FilePath, [System.Text.Encoding]::ASCII)
        
        if ($fileContent -match "go1\.\d+") {
            Write-Warning "⚠️ 发现Go版本信息"
        } else {
            Write-Success "✅ Go版本信息已隐藏"
        }
        
        if ($fileContent -match "buildid") {
            Write-Warning "⚠️ 发现构建ID信息"
        } else {
            Write-Success "✅ 构建ID已移除"
        }
        
    } catch {
        Write-Debug "反编译测试异常: $($_.Exception.Message)"
    }
    
    # 测试2: 检查函数名混淆
    $functionPatterns = @(
        "main\.main",
        "main\..*",
        "runtime\..*"
    )
    
    $visibleFunctions = 0
    foreach ($pattern in $functionPatterns) {
        if ($fileContent -match $pattern) {
            $visibleFunctions++
        }
    }
    
    if ($visibleFunctions -eq 0) {
        Write-Success "✅ 函数名已完全混淆"
    } else {
        Write-Warning "⚠️ 部分函数名仍然可见"
    }
    
    Write-Output ""
}

function CalculateSecurityScore {
    Write-Info "=== 安全评分 ==="
    
    $score = 0
    $maxScore = 100
    
    # 基础分数
    $score += 20  # ELF格式正确
    
    # 符号表移除 (20分)
    try {
        $fileContent = [System.IO.File]::ReadAllText($FilePath, [System.Text.Encoding]::ASCII)
        $goSymbols = @("runtime.main", "main.main", "go.buildinfo")
        $foundSymbols = ($goSymbols | Where-Object { $fileContent -match [regex]::Escape($_) }).Count
        
        if ($foundSymbols -eq 0) {
            $score += 20
        } elseif ($foundSymbols -eq 1) {
            $score += 15
        } elseif ($foundSymbols -eq 2) {
            $score += 10
        } else {
            $score += 5
        }
    } catch {
        $score += 10  # 无法读取可能意味着更好的保护
    }
    
    # 调试信息移除 (20分)
    if ($fileContent -notmatch "\.debug_") {
        $score += 20
    } else {
        $score += 10
    }
    
    # 字符串混淆 (20分)
    $sensitiveStrings = @("walmart", "gateway", "config.yaml")
    $foundStrings = ($sensitiveStrings | Where-Object { $fileContent -match [regex]::Escape($_) }).Count
    
    if ($foundStrings -eq 0) {
        $score += 20
    } elseif ($foundStrings -eq 1) {
        $score += 15
    } else {
        $score += 10
    }
    
    # UPX压缩 (20分)
    if ($fileContent -match "UPX!") {
        $score += 20
    }
    
    # 显示评分
    $scoreColor = if ($score -ge 80) { "Green" } elseif ($score -ge 60) { "Yellow" } else { "Red" }
    
    Write-Host "安全评分: $score/$maxScore" -ForegroundColor $scoreColor
    
    if ($score -ge 80) {
        Write-Success "🛡️ 安全级别: 高 - 具有良好的反编译保护"
    } elseif ($score -ge 60) {
        Write-Warning "🔒 安全级别: 中 - 具有基本的反编译保护"
    } else {
        Write-Warning "⚠️ 安全级别: 低 - 建议重新构建并启用所有安全选项"
    }
    
    Write-Output ""
    Write-Info "建议:"
    Write-Info "1. 确保安装并使用garble进行代码混淆"
    Write-Info "2. 安装UPX并启用压缩保护"
    Write-Info "3. 使用 -ldflags='-w -s' 移除调试信息"
    Write-Info "4. 定期更新安全工具版本"

    # 更新全局报告
    $global:SecurityReport.Score = $score
    $global:SecurityReport.SecurityLevel = if ($score -ge 80) { "高" } elseif ($score -ge 60) { "中" } else { "低" }
}

function AdvancedSecurityChecks {
    Write-Info "=== 高级安全检查 ==="

    try {
        $fileBytes = [System.IO.File]::ReadAllBytes($FilePath)
        $fileContent = [System.Text.Encoding]::UTF8.GetString($fileBytes)

        # 检查Go运行时信息
        Write-Debug "检查Go运行时信息..."
        $goRuntimePatterns = @(
            "runtime\.",
            "go\.buildinfo",
            "go\.mod",
            "GOROOT",
            "GOPATH"
        )

        $runtimeInfoFound = 0
        foreach ($pattern in $goRuntimePatterns) {
            if ($fileContent -match $pattern) {
                $runtimeInfoFound++
                Write-Debug "发现运行时信息: $pattern"
            }
        }

        if ($runtimeInfoFound -eq 0) {
            Write-Success "✅ Go运行时信息已完全清除"
        } else {
            Write-Warning "⚠️ 发现 $runtimeInfoFound 个运行时信息残留"
        }

        # 检查包路径信息
        Write-Debug "检查包路径信息..."
        $packagePatterns = @(
            "github\.com/",
            "golang\.org/",
            "gopkg\.in/",
            "/go/pkg/mod/"
        )

        $packageInfoFound = 0
        foreach ($pattern in $packagePatterns) {
            if ($fileContent -match $pattern) {
                $packageInfoFound++
                Write-Debug "发现包路径: $pattern"
            }
        }

        if ($packageInfoFound -eq 0) {
            Write-Success "✅ 包路径信息已完全清除"
        } else {
            Write-Warning "⚠️ 发现 $packageInfoFound 个包路径残留"
        }

        # 更新报告
        $global:SecurityReport.Tests.AdvancedChecks = @{
            "GoRuntimeInfo" = $runtimeInfoFound
            "PackageInfo" = $packageInfoFound
        }

    } catch {
        Write-Debug "高级安全检查异常: $($_.Exception.Message)"
        Write-Warning "无法完成高级安全检查"
    }

    Write-Output ""
}

function ExportSecurityReport {
    Write-Info "=== 导出安全报告 ==="

    try {
        $reportFile = "$FilePath.security-report.json"

        # 导出JSON报告
        $global:SecurityReport | ConvertTo-Json -Depth 5 | Out-File -FilePath $reportFile -Encoding UTF8
        Write-Success "安全报告已导出: $reportFile"

        Write-Info "报告文件: $reportFile"

    } catch {
        Write-Error "导出报告失败: $($_.Exception.Message)"
    }

    Write-Output ""
}

# 显示帮助信息
function Show-Help {
    Write-Host "沃尔玛绑卡网关 - 安全构建验证脚本" -ForegroundColor Blue
    Write-Host ""
    Write-Host "用法:" -ForegroundColor Yellow
    Write-Host "  .\verify-security.ps1 [选项]"
    Write-Host ""
    Write-Host "选项:" -ForegroundColor Yellow
    Write-Host "  -FilePath <path>      指定要验证的文件路径（默认: walmart-gateway-production）"
    Write-Host "  -Verbose             显示详细验证信息"
    Write-Host "  -DetailedReport      启用详细报告模式"
    Write-Host "  -ExportReport        导出验证报告到JSON文件"
    Write-Host "  -Help                显示此帮助信息"
    Write-Host ""
    Write-Host "示例:" -ForegroundColor Yellow
    Write-Host "  .\verify-security.ps1                                    # 验证默认文件"
    Write-Host "  .\verify-security.ps1 -FilePath walmart-gateway-linux    # 验证指定文件"
    Write-Host "  .\verify-security.ps1 -Verbose -DetailedReport           # 详细验证"
    Write-Host "  .\verify-security.ps1 -ExportReport                      # 导出报告"
}

# 参数处理
if ($args -contains "-Help" -or $args -contains "--help" -or $args -contains "-h") {
    Show-Help
    exit 0
}

# 执行主函数
try {
    $result = Main
    exit $result
} catch {
    Write-Error "验证过程中发生错误: $($_.Exception.Message)"
    exit 1
}
