#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
沃尔玛绑卡系统 - 商户切换状态持久化测试

测试商户切换功能的localStorage持久化机制，包括：
1. 商户选择的保存和恢复
2. 页面刷新后的状态保持
3. 无效商户ID的处理
4. 权限控制（仅超级管理员可切换）
5. 异常情况的处理
"""

import asyncio
import json
import time
from playwright.async_api import async_playwright, expect
import pytest


class MerchantSwitchPersistenceTest:
    """商户切换状态持久化测试类"""
    
    def __init__(self):
        self.base_url = "http://localhost:2000"
        self.admin_credentials = {
            "username": "admin",
            "password": "7c222fb2927d828af22f592134e8932480637c0d"
        }
        self.merchant_credentials = {
            "username": "test1", 
            "password": "12345678"
        }
    
    async def setup_browser(self):
        """设置浏览器"""
        self.playwright = await async_playwright().start()
        self.browser = await self.playwright.chromium.launch(headless=False)
        self.context = await self.browser.new_context()
        self.page = await self.context.new_page()
    
    async def cleanup_browser(self):
        """清理浏览器"""
        await self.context.close()
        await self.browser.close()
        await self.playwright.stop()
    
    async def login_as_admin(self):
        """以超级管理员身份登录"""
        await self.page.goto(f"{self.base_url}/#/login")
        await self.page.wait_for_load_state('networkidle')
        
        # 填写登录信息
        await self.page.fill('input[placeholder="请输入用户名"]', self.admin_credentials["username"])
        await self.page.fill('input[placeholder="请输入密码"]', self.admin_credentials["password"])
        
        # 点击登录
        await self.page.click('button[type="submit"]')
        await self.page.wait_for_url(f"{self.base_url}/#/dashboard")
        
        print("✅ 超级管理员登录成功")
    
    async def login_as_merchant(self):
        """以商户管理员身份登录"""
        await self.page.goto(f"{self.base_url}/#/login")
        await self.page.wait_for_load_state('networkidle')
        
        # 填写登录信息
        await self.page.fill('input[placeholder="请输入用户名"]', self.merchant_credentials["username"])
        await self.page.fill('input[placeholder="请输入密码"]', self.merchant_credentials["password"])
        
        # 点击登录
        await self.page.click('button[type="submit"]')
        await self.page.wait_for_url(f"{self.base_url}/#/dashboard")
        
        print("✅ 商户管理员登录成功")
    
    async def test_admin_merchant_switcher_visibility(self):
        """测试超级管理员可以看到商户切换器"""
        await self.login_as_admin()
        
        # 检查商户切换器是否可见
        merchant_switcher = self.page.locator('.merchant-switcher')
        await expect(merchant_switcher).to_be_visible()
        
        print("✅ 超级管理员可以看到商户切换器")
    
    async def test_merchant_admin_no_switcher(self):
        """测试商户管理员看不到商户切换器"""
        await self.login_as_merchant()
        
        # 检查商户切换器是否不可见
        merchant_switcher = self.page.locator('.merchant-switcher')
        await expect(merchant_switcher).not_to_be_visible()
        
        print("✅ 商户管理员看不到商户切换器")
    
    async def test_merchant_selection_persistence(self):
        """测试商户选择的持久化"""
        await self.login_as_admin()
        
        # 点击商户切换器
        await self.page.click('.merchant-selector')
        await self.page.wait_for_timeout(500)
        
        # 选择一个商户（假设存在test1商户）
        merchant_item = self.page.locator('.merchant-item').filter(has_text='test1').first()
        if await merchant_item.count() > 0:
            await merchant_item.click()
            await self.page.wait_for_timeout(1000)
            
            # 检查localStorage中是否保存了商户选择
            current_merchant = await self.page.evaluate("""
                () => {
                    const userInfo = JSON.parse(localStorage.getItem('walmart_user_info') || '{}');
                    const userId = userInfo.id;
                    const key = userId ? `currentMerchant_${userId}` : 'currentMerchant';
                    return localStorage.getItem(key);
                }
            """)
            
            assert current_merchant is not None, "商户选择未保存到localStorage"
            merchant_data = json.loads(current_merchant)
            assert 'id' in merchant_data, "保存的商户数据格式无效"
            
            print(f"✅ 商户选择已保存到localStorage: {merchant_data.get('name', 'Unknown')}")
        else:
            print("⚠️ 未找到test1商户，跳过商户选择测试")
    
    async def test_page_refresh_persistence(self):
        """测试页面刷新后商户选择的保持"""
        await self.login_as_admin()
        
        # 选择商户
        await self.page.click('.merchant-selector')
        await self.page.wait_for_timeout(500)
        
        merchant_item = self.page.locator('.merchant-item').filter(has_text='test1').first()
        if await merchant_item.count() > 0:
            await merchant_item.click()
            await self.page.wait_for_timeout(1000)
            
            # 获取当前选择的商户名称
            current_merchant_name = await self.page.locator('.merchant-name').text_content()
            
            # 刷新页面
            await self.page.reload()
            await self.page.wait_for_load_state('networkidle')
            await self.page.wait_for_timeout(2000)  # 等待商户数据加载
            
            # 检查商户选择是否保持
            restored_merchant_name = await self.page.locator('.merchant-name').text_content()
            assert current_merchant_name == restored_merchant_name, f"页面刷新后商户选择未保持: {current_merchant_name} != {restored_merchant_name}"
            
            print(f"✅ 页面刷新后商户选择保持: {restored_merchant_name}")
        else:
            print("⚠️ 未找到test1商户，跳过页面刷新测试")
    
    async def test_invalid_merchant_cleanup(self):
        """测试无效商户ID的清理"""
        await self.login_as_admin()
        
        # 手动设置一个无效的商户ID到localStorage
        await self.page.evaluate("""
            () => {
                const userInfo = JSON.parse(localStorage.getItem('walmart_user_info') || '{}');
                const userId = userInfo.id;
                const key = userId ? `currentMerchant_${userId}` : 'currentMerchant';
                const invalidMerchant = {
                    id: 99999,
                    name: 'Invalid Merchant',
                    code: 'INVALID'
                };
                localStorage.setItem(key, JSON.stringify(invalidMerchant));
            }
        """)
        
        # 刷新页面，触发商户验证
        await self.page.reload()
        await self.page.wait_for_load_state('networkidle')
        await self.page.wait_for_timeout(3000)  # 等待验证完成
        
        # 检查是否切换到全局视图
        merchant_name = await self.page.locator('.merchant-name').text_content()
        assert '所有商家' in merchant_name or '全局视图' in merchant_name, f"无效商户未被清理: {merchant_name}"
        
        # 检查localStorage是否被清理
        current_merchant = await self.page.evaluate("""
            () => {
                const userInfo = JSON.parse(localStorage.getItem('walmart_user_info') || '{}');
                const userId = userInfo.id;
                const key = userId ? `currentMerchant_${userId}` : 'currentMerchant';
                return localStorage.getItem(key);
            }
        """)
        
        assert current_merchant is None, "无效商户数据未从localStorage中清理"
        
        print("✅ 无效商户ID已被正确清理")
    
    async def test_global_view_selection(self):
        """测试全局视图选择"""
        await self.login_as_admin()
        
        # 点击商户切换器
        await self.page.click('.merchant-selector')
        await self.page.wait_for_timeout(500)
        
        # 点击全局视图
        global_view = self.page.locator('.merchant-item').filter(has_text='全局视图').first()
        await global_view.click()
        await self.page.wait_for_timeout(1000)
        
        # 检查显示是否正确
        merchant_name = await self.page.locator('.merchant-name').text_content()
        assert '所有商家' in merchant_name or '全局视图' in merchant_name, f"全局视图选择失败: {merchant_name}"
        
        # 检查localStorage是否被清理
        current_merchant = await self.page.evaluate("""
            () => {
                const userInfo = JSON.parse(localStorage.getItem('walmart_user_info') || '{}');
                const userId = userInfo.id;
                const key = userId ? `currentMerchant_${userId}` : 'currentMerchant';
                return localStorage.getItem(key);
            }
        """)
        
        assert current_merchant is None, "全局视图选择后localStorage未被清理"
        
        print("✅ 全局视图选择功能正常")
    
    async def run_all_tests(self):
        """运行所有测试"""
        await self.setup_browser()
        
        try:
            print("🚀 开始商户切换状态持久化测试...")
            
            # 测试权限控制
            await self.test_admin_merchant_switcher_visibility()
            await self.test_merchant_admin_no_switcher()
            
            # 测试持久化功能
            await self.test_merchant_selection_persistence()
            await self.test_page_refresh_persistence()
            
            # 测试异常处理
            await self.test_invalid_merchant_cleanup()
            await self.test_global_view_selection()
            
            print("✅ 所有商户切换状态持久化测试通过！")
            
        except Exception as e:
            print(f"❌ 测试失败: {str(e)}")
            raise
        finally:
            await self.cleanup_browser()


async def main():
    """主函数"""
    test = MerchantSwitchPersistenceTest()
    await test.run_all_tests()


if __name__ == "__main__":
    asyncio.run(main())
