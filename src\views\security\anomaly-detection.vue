<template>
  <div class="anomaly-detection">
    <!-- 页面头部 -->
    <div class="page-header">
      <h1 class="page-title">异常行为检测</h1>
      <div class="header-actions">
        <el-button type="primary" :icon="Search" @click="runDetection" :loading="detecting">
          运行检测
        </el-button>
        <el-button type="success" :icon="Download" @click="exportReport">
          导出报告
        </el-button>
      </div>
    </div>

    <!-- 检测配置 -->
    <el-card shadow="never" style="margin-bottom: 20px;">
      <template #header>
        <span>检测配置</span>
      </template>
      
      <el-form :model="detectionConfig" inline>
        <el-form-item label="时间窗口">
          <el-select v-model="detectionConfig.timeWindow" style="width: 150px;">
            <el-option label="最近1小时" :value="1" />
            <el-option label="最近6小时" :value="6" />
            <el-option label="最近24小时" :value="24" />
            <el-option label="最近7天" :value="168" />
          </el-select>
        </el-form-item>
        
        <el-form-item label="检测类型">
          <el-select v-model="detectionConfig.detectionTypes" multiple style="width: 300px;">
            <el-option label="账户接管" value="account_takeover" />
            <el-option label="数据窃取" value="data_exfiltration" />
            <el-option label="权限提升" value="privilege_escalation" />
            <el-option label="暴力破解" value="brute_force" />
            <el-option label="可疑登录" value="suspicious_login" />
            <el-option label="大规模数据访问" value="mass_data_access" />
            <el-option label="异常时间访问" value="unusual_time_access" />
            <el-option label="快速权限变更" value="rapid_permission_changes" />
          </el-select>
        </el-form-item>
        
        <el-form-item label="目标用户">
          <el-input 
            v-model="detectionConfig.targetUser" 
            placeholder="留空检测所有用户" 
            style="width: 200px;"
            clearable
          />
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 检测结果概览 -->
    <div class="detection-overview" v-if="detectionResults">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-card shadow="hover" class="overview-card">
            <div class="card-content">
              <div class="card-icon danger">
                <el-icon><Warning /></el-icon>
              </div>
              <div class="card-info">
                <div class="card-title">总异常数</div>
                <div class="card-value">{{ detectionResults.total_anomalies || 0 }}</div>
                <div class="card-desc">检测到的异常行为</div>
              </div>
            </div>
          </el-card>
        </el-col>
        
        <el-col :span="6">
          <el-card shadow="hover" class="overview-card">
            <div class="card-content">
              <div class="card-icon warning">
                <el-icon><TrendCharts /></el-icon>
              </div>
              <div class="card-info">
                <div class="card-title">风险分数</div>
                <div class="card-value" :class="getRiskScoreClass(detectionResults.risk_score)">
                  {{ detectionResults.risk_score || 0 }}
                </div>
                <div class="card-desc">综合风险评估</div>
              </div>
            </div>
          </el-card>
        </el-col>
        
        <el-col :span="6">
          <el-card shadow="hover" class="overview-card">
            <div class="card-content">
              <div class="card-icon primary">
                <el-icon><Clock /></el-icon>
              </div>
              <div class="card-info">
                <div class="card-title">检测时间窗口</div>
                <div class="card-value">{{ detectionConfig.timeWindow }}h</div>
                <div class="card-desc">分析时间范围</div>
              </div>
            </div>
          </el-card>
        </el-col>
        
        <el-col :span="6">
          <el-card shadow="hover" class="overview-card">
            <div class="card-content">
              <div class="card-icon success">
                <el-icon><DataAnalysis /></el-icon>
              </div>
              <div class="card-info">
                <div class="card-title">检测类型</div>
                <div class="card-value">{{ detectionConfig.detectionTypes.length }}</div>
                <div class="card-desc">启用的检测规则</div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 异常详情表格 -->
    <el-card shadow="never" style="margin-top: 20px;" v-if="detectionResults?.anomalies?.length > 0">
      <template #header>
        <div class="card-header">
          <span>异常详情</span>
          <el-tag type="danger" size="small">{{ detectionResults.anomalies.length }} 个异常</el-tag>
        </div>
      </template>

      <el-table :data="detectionResults.anomalies" style="width: 100%" :default-sort="{prop: 'timestamp', order: 'descending'}">
        <el-table-column prop="type" label="异常类型" width="150">
          <template #default="{ row }">
            <el-tag :type="getAnomalyTypeColor(row.type)" size="small">{{ getAnomalyTypeLabel(row.type) }}</el-tag>
          </template>
        </el-table-column>
        
        <el-table-column prop="subtype" label="子类型" width="150">
          <template #default="{ row }">
            <span class="subtype-text">{{ getSubtypeLabel(row.subtype) }}</span>
          </template>
        </el-table-column>
        
        <el-table-column prop="severity" label="严重程度" width="100">
          <template #default="{ row }">
            <el-tag :type="getSeverityColor(row.severity)" size="small">{{ getSeverityLabel(row.severity) }}</el-tag>
          </template>
        </el-table-column>
        
        <el-table-column prop="user_id" label="用户ID" width="100" />
        
        <el-table-column prop="timestamp" label="发生时间" width="180" sortable>
          <template #default="{ row }">
            {{ formatTime(row.timestamp) }}
          </template>
        </el-table-column>
        
        <el-table-column prop="details" label="详细信息" min-width="200">
          <template #default="{ row }">
            <div class="details-summary">
              <span v-if="row.details.total_records_accessed">
                访问记录: {{ row.details.total_records_accessed }}
              </span>
              <span v-else-if="row.details.failed_attempts">
                失败尝试: {{ row.details.failed_attempts }}
              </span>
              <span v-else-if="row.details.operation_count">
                操作次数: {{ row.details.operation_count }}
              </span>
              <span v-else>
                {{ Object.keys(row.details).slice(0, 2).join(', ') }}
              </span>
            </div>
          </template>
        </el-table-column>
        
        <el-table-column label="操作" width="120" fixed="right">
          <template #default="{ row }">
            <el-button type="text" @click="viewAnomalyDetail(row)" size="small">查看详情</el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <!-- 检测统计图表 -->
    <div class="charts-section" v-if="detectionResults">
      <el-row :gutter="20" style="margin-top: 20px;">
        <!-- 异常类型分布 -->
        <el-col :span="12">
          <el-card shadow="never" class="chart-card">
            <template #header>
              <span>异常类型分布</span>
            </template>
            <div ref="typeDistributionChart" class="chart-container" v-loading="chartLoading"></div>
          </el-card>
        </el-col>

        <!-- 严重程度分布 -->
        <el-col :span="12">
          <el-card shadow="never" class="chart-card">
            <template #header>
              <span>严重程度分布</span>
            </template>
            <div ref="severityChart" class="chart-container" v-loading="chartLoading"></div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 无异常状态 -->
    <el-card shadow="never" style="margin-top: 20px;" v-if="detectionResults && detectionResults.total_anomalies === 0">
      <el-empty description="未检测到异常行为">
        <el-button type="primary" @click="runDetection">重新检测</el-button>
      </el-empty>
    </el-card>

    <!-- 异常详情对话框 -->
    <el-dialog v-model="anomalyDialogVisible" title="异常详情" width="800px">
      <div v-if="selectedAnomaly" class="anomaly-detail">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="异常类型">{{ getAnomalyTypeLabel(selectedAnomaly.type) }}</el-descriptions-item>
          <el-descriptions-item label="子类型">{{ getSubtypeLabel(selectedAnomaly.subtype) }}</el-descriptions-item>
          <el-descriptions-item label="严重程度">
            <el-tag :type="getSeverityColor(selectedAnomaly.severity)">{{ getSeverityLabel(selectedAnomaly.severity) }}</el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="用户ID">{{ selectedAnomaly.user_id || 'N/A' }}</el-descriptions-item>
          <el-descriptions-item label="发生时间" :span="2">{{ formatTime(selectedAnomaly.timestamp) }}</el-descriptions-item>
        </el-descriptions>
        
        <el-divider content-position="left">详细信息</el-divider>
        <div class="detail-content">
          <pre>{{ JSON.stringify(selectedAnomaly.details, null, 2) }}</pre>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import { 
  Search, Download, Warning, TrendCharts, Clock, DataAnalysis 
} from '@element-plus/icons-vue'
import * as echarts from 'echarts'

// 响应式数据
const detecting = ref(false)
const chartLoading = ref(false)
const anomalyDialogVisible = ref(false)
const selectedAnomaly = ref(null)
const detectionResults = ref(null)

// 图表引用
const typeDistributionChart = ref()
const severityChart = ref()

// 图表实例
let typeDistributionChartInstance = null
let severityChartInstance = null

// 检测配置
const detectionConfig = reactive({
  timeWindow: 24,
  detectionTypes: [
    'account_takeover',
    'data_exfiltration', 
    'privilege_escalation',
    'brute_force'
  ],
  targetUser: ''
})

// 工具方法
const formatTime = (timestamp) => {
  return new Date(timestamp).toLocaleString()
}

const getRiskScoreClass = (score) => {
  if (score >= 80) return 'risk-critical'
  if (score >= 60) return 'risk-high'
  if (score >= 40) return 'risk-medium'
  return 'risk-low'
}

const getAnomalyTypeColor = (type) => {
  const colors = {
    'account_takeover': 'danger',
    'data_exfiltration': 'danger',
    'privilege_escalation': 'warning',
    'brute_force': 'danger',
    'suspicious_login': 'warning',
    'mass_data_access': 'warning',
    'unusual_time_access': 'info',
    'rapid_permission_changes': 'warning'
  }
  return colors[type] || 'info'
}

const getAnomalyTypeLabel = (type) => {
  const labels = {
    'account_takeover': '账户接管',
    'data_exfiltration': '数据窃取',
    'privilege_escalation': '权限提升',
    'brute_force': '暴力破解',
    'suspicious_login': '可疑登录',
    'mass_data_access': '大规模数据访问',
    'unusual_time_access': '异常时间访问',
    'rapid_permission_changes': '快速权限变更'
  }
  return labels[type] || type
}

const getSubtypeLabel = (subtype) => {
  const labels = {
    'ip_change': 'IP地址变化',
    'user_agent_change': '用户代理变化',
    'behavior_change': '行为模式变化',
    'bulk_data_access': '批量数据访问',
    'excessive_downloads': '过度下载',
    'rapid_privilege_changes': '快速权限变更',
    'login_brute_force': '登录暴力破解',
    'off_hours_login': '非工作时间登录',
    'multiple_locations': '多地点登录',
    'high_frequency_access': '高频访问',
    'off_hours_activity': '非工作时间活动',
    'burst_permission_changes': '权限变更突发'
  }
  return labels[subtype] || subtype
}

const getSeverityColor = (severity) => {
  const colors = {
    'low': 'success',
    'medium': 'warning',
    'high': 'danger',
    'critical': 'danger'
  }
  return colors[severity] || 'info'
}

const getSeverityLabel = (severity) => {
  const labels = {
    'low': '低',
    'medium': '中',
    'high': '高',
    'critical': '严重'
  }
  return labels[severity] || severity
}

// 数据获取方法
const runDetection = async () => {
  try {
    detecting.value = true

    // 模拟API调用
    const response = await new Promise(resolve => {
      setTimeout(() => {
        resolve({
          time_window: {
            start_time: new Date(Date.now() - detectionConfig.timeWindow * 60 * 60 * 1000).toISOString(),
            end_time: new Date().toISOString(),
            hours: detectionConfig.timeWindow
          },
          total_anomalies: 8,
          risk_score: 72,
          anomalies: [
            {
              type: 'account_takeover',
              subtype: 'ip_change',
              user_id: 1001,
              severity: 'high',
              details: {
                unique_ip_count: 4,
                ip_timeline: [
                  { ip: '*************', timestamp: '2024-01-07T10:30:00Z' },
                  { ip: '*********', timestamp: '2024-01-07T11:15:00Z' },
                  { ip: '***********', timestamp: '2024-01-07T12:00:00Z' }
                ]
              },
              timestamp: '2024-01-07T12:00:00Z'
            },
            {
              type: 'data_exfiltration',
              subtype: 'bulk_data_access',
              user_id: 1002,
              severity: 'high',
              details: {
                total_records_accessed: 2500,
                access_count: 15,
                resource_types: ['USER', 'MERCHANT', 'TRANSACTION']
              },
              timestamp: '2024-01-07T11:45:00Z'
            },
            {
              type: 'brute_force',
              subtype: 'login_brute_force',
              ip_address: '************',
              severity: 'high',
              details: {
                failed_attempts: 25,
                time_span_minutes: 15,
                targeted_users: [1001, 1003, 1005]
              },
              timestamp: '2024-01-07T09:30:00Z'
            },
            {
              type: 'suspicious_login',
              subtype: 'off_hours_login',
              user_id: 1003,
              severity: 'medium',
              details: {
                login_hour: 2,
                login_time: '2024-01-07T02:15:00Z'
              },
              timestamp: '2024-01-07T02:15:00Z'
            },
            {
              type: 'mass_data_access',
              subtype: 'high_frequency_access',
              user_id: 1004,
              severity: 'high',
              details: {
                total_access_count: 150,
                access_rate_per_hour: 75,
                time_span_hours: 2,
                resource_distribution: {
                  'USER': 80,
                  'MERCHANT': 45,
                  'ROLE': 25
                }
              },
              timestamp: '2024-01-07T14:30:00Z'
            }
          ],
          detection_results: {
            account_takeover: {
              anomalies: 1,
              detection_summary: {
                ip_changes: 1,
                ua_changes: 0,
                behavior_changes: 0
              }
            },
            data_exfiltration: {
              anomalies: 1,
              detection_summary: {
                bulk_access_users: 1,
                excessive_download_users: 0
              }
            },
            brute_force: {
              anomalies: 1,
              detection_summary: {
                brute_force_ips: 1
              }
            }
          }
        })
      }, 2000)
    })

    detectionResults.value = response

    // 初始化图表
    await nextTick()
    initCharts()

    ElMessage.success('异常检测完成')
  } catch (error) {
    console.error('异常检测失败:', error)
    ElMessage.error('异常检测失败')
  } finally {
    detecting.value = false
  }
}

// 图表初始化方法
const initCharts = () => {
  initTypeDistributionChart()
  initSeverityChart()
}

const initTypeDistributionChart = () => {
  if (typeDistributionChartInstance && detectionResults.value) {
    const typeCount = {}
    detectionResults.value.anomalies.forEach(anomaly => {
      const label = getAnomalyTypeLabel(anomaly.type)
      typeCount[label] = (typeCount[label] || 0) + 1
    })

    typeDistributionChartInstance.setOption({
      title: {
        text: '异常类型分布',
        left: 'center'
      },
      tooltip: {
        trigger: 'item'
      },
      series: [
        {
          type: 'pie',
          radius: '60%',
          data: Object.entries(typeCount).map(([name, value]) => ({ name, value })),
          emphasis: {
            itemStyle: {
              shadowBlur: 10,
              shadowOffsetX: 0,
              shadowColor: 'rgba(0, 0, 0, 0.5)'
            }
          }
        }
      ]
    })
  }
}

const initSeverityChart = () => {
  if (severityChartInstance && detectionResults.value) {
    const severityCount = { low: 0, medium: 0, high: 0, critical: 0 }
    detectionResults.value.anomalies.forEach(anomaly => {
      severityCount[anomaly.severity] = (severityCount[anomaly.severity] || 0) + 1
    })

    severityChartInstance.setOption({
      title: {
        text: '严重程度分布',
        left: 'center'
      },
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'shadow'
        }
      },
      xAxis: {
        type: 'category',
        data: ['低', '中', '高', '严重']
      },
      yAxis: {
        type: 'value'
      },
      series: [
        {
          type: 'bar',
          data: [
            { value: severityCount.low, itemStyle: { color: '#67C23A' } },
            { value: severityCount.medium, itemStyle: { color: '#E6A23C' } },
            { value: severityCount.high, itemStyle: { color: '#F56C6C' } },
            { value: severityCount.critical, itemStyle: { color: '#909399' } }
          ]
        }
      ]
    })
  }
}

// 事件处理方法
const viewAnomalyDetail = (anomaly) => {
  selectedAnomaly.value = anomaly
  anomalyDialogVisible.value = true
}

const exportReport = () => {
  if (!detectionResults.value) {
    ElMessage.warning('请先运行检测')
    return
  }

  const reportData = {
    timestamp: new Date().toISOString(),
    detection_config: detectionConfig,
    results: detectionResults.value
  }

  const blob = new Blob([JSON.stringify(reportData, null, 2)], { type: 'application/json' })
  const url = URL.createObjectURL(blob)
  const a = document.createElement('a')
  a.href = url
  a.download = `anomaly-detection-report-${new Date().toISOString().split('T')[0]}.json`
  a.click()
  URL.revokeObjectURL(url)
  ElMessage.success('检测报告已导出')
}

// 生命周期
onMounted(async () => {
  await nextTick()

  // 初始化图表
  if (typeDistributionChart.value) {
    typeDistributionChartInstance = echarts.init(typeDistributionChart.value)
  }
  if (severityChart.value) {
    severityChartInstance = echarts.init(severityChart.value)
  }

  // 窗口大小变化时重新调整图表
  window.addEventListener('resize', () => {
    typeDistributionChartInstance?.resize()
    severityChartInstance?.resize()
  })
})
</script>

<style scoped>
.anomaly-detection {
  padding: 20px;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  background: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.page-title {
  font-size: 24px;
  font-weight: bold;
  margin: 0;
  color: #333;
}

.header-actions {
  display: flex;
  gap: 10px;
}

.detection-overview {
  margin-bottom: 20px;
}

.overview-card {
  height: 120px;
}

.card-content {
  display: flex;
  align-items: center;
  height: 100%;
}

.card-icon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 15px;
  font-size: 24px;
  color: white;
}

.card-icon.success {
  background: linear-gradient(135deg, #67C23A, #85CE61);
}

.card-icon.primary {
  background: linear-gradient(135deg, #409EFF, #66B1FF);
}

.card-icon.warning {
  background: linear-gradient(135deg, #E6A23C, #EEBE77);
}

.card-icon.danger {
  background: linear-gradient(135deg, #F56C6C, #F78989);
}

.card-info {
  flex: 1;
}

.card-title {
  font-size: 14px;
  color: #666;
  margin-bottom: 5px;
}

.card-value {
  font-size: 24px;
  font-weight: bold;
  margin-bottom: 5px;
  color: #333;
}

.card-value.risk-low {
  color: #67C23A;
}

.card-value.risk-medium {
  color: #E6A23C;
}

.card-value.risk-high {
  color: #F56C6C;
}

.card-value.risk-critical {
  color: #909399;
}

.card-desc {
  font-size: 12px;
  color: #999;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.subtype-text {
  font-size: 12px;
  color: #666;
}

.details-summary {
  font-size: 12px;
  color: #666;
}

.charts-section {
  margin-bottom: 20px;
}

.chart-card {
  height: 400px;
}

.chart-container {
  height: 320px;
  width: 100%;
}

.anomaly-detail .detail-content {
  margin-top: 15px;
}

.anomaly-detail pre {
  background: #f5f5f5;
  padding: 15px;
  border-radius: 4px;
  font-size: 12px;
  max-height: 300px;
  overflow: auto;
  white-space: pre-wrap;
  word-break: break-all;
}

:deep(.el-card__body) {
  padding: 20px;
}

:deep(.el-card__header) {
  padding: 15px 20px;
  border-bottom: 1px solid #f0f0f0;
}

:deep(.el-table) {
  font-size: 12px;
}

:deep(.el-descriptions__label) {
  font-weight: bold;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .detection-overview .el-col {
    margin-bottom: 20px;
  }

  .charts-section .el-col {
    margin-bottom: 20px;
  }
}

@media (max-width: 768px) {
  .anomaly-detection {
    padding: 10px;
  }

  .page-header {
    flex-direction: column;
    gap: 15px;
    text-align: center;
  }

  .chart-card {
    height: 300px;
  }

  .chart-container {
    height: 220px;
  }
}
</style>
