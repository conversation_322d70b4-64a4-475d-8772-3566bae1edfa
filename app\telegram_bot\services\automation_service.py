"""
自动化改进服务
实现用户首次交互自动引导、状态检测、智能提示等功能
"""

from typing import Dict, List, Optional, Tuple
from datetime import datetime, timedelta
from telegram import Update, InlineKeyboardButton, InlineKeyboardMarkup
from telegram.ext import ContextTypes
from sqlalchemy.orm import Session

from app.models.telegram_user import TelegramUser
from app.models.telegram_group import TelegramGroup
from app.core.logging import get_logger

logger = get_logger(__name__)


class AutomationService:
    """自动化服务"""
    
    def __init__(self, db: Session, config):
        self.db = db
        self.config = config
        self.logger = logger
        
        # 用户交互记录缓存（实际应用中可以使用Redis）
        self.user_interactions = {}
    
    async def handle_first_interaction(
        self, 
        update: Update, 
        context: ContextTypes.DEFAULT_TYPE
    ) -> bool:
        """处理用户首次交互"""
        try:
            user_id = update.effective_user.id
            chat_id = update.effective_chat.id
            
            # 检查是否为首次交互
            if not await self._is_first_interaction(user_id, chat_id):
                return False
            
            # 记录交互
            await self._record_interaction(user_id, chat_id)
            
            # 发送欢迎和引导信息
            await self._send_welcome_guide(update, context)
            
            return True
            
        except Exception as e:
            self.logger.error(f"处理首次交互失败: {e}")
            return False
    
    async def _is_first_interaction(self, user_id: int, chat_id: int) -> bool:
        """检查是否为首次交互"""
        try:
            # 检查用户是否在数据库中存在
            telegram_user = self.db.query(TelegramUser).filter_by(
                telegram_user_id=user_id
            ).first()
            
            # 如果用户不存在，则为首次交互
            if not telegram_user:
                return True
            
            # 检查用户是否在此群组中首次交互
            interaction_key = f"{user_id}_{chat_id}"
            if interaction_key not in self.user_interactions:
                # 检查最近是否有交互记录（可以从数据库或缓存中查询）
                return True
            
            return False
            
        except Exception as e:
            self.logger.error(f"检查首次交互失败: {e}")
            return False
    
    async def _record_interaction(self, user_id: int, chat_id: int):
        """记录用户交互"""
        interaction_key = f"{user_id}_{chat_id}"
        self.user_interactions[interaction_key] = {
            "first_interaction": datetime.now(),
            "interaction_count": 1
        }
    
    async def _send_welcome_guide(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """发送欢迎引导"""
        user_name = self._get_user_display_name(update)
        chat_type = update.effective_chat.type
        
        if chat_type == 'private':
            message = await self._get_private_welcome(user_name)
        else:
            message = await self._get_group_welcome(user_name, update)
        
        keyboard = self._get_welcome_keyboard()
        
        await context.bot.send_message(
            chat_id=update.effective_chat.id,
            text=message,
            reply_markup=keyboard,
            parse_mode='Markdown'
        )
    
    async def _get_private_welcome(self, user_name: str) -> str:
        """获取私聊欢迎信息"""
        return f"""👋 **欢迎，{user_name}！**

🤖 **沃尔玛绑卡统计机器人**

我是您的智能助手，可以帮您：
• 📊 查询绑卡统计数据
• 🔐 完成身份验证
• 🔗 协助群组绑定
• ❓ 提供使用指导

🚀 **快速开始**：

**第一步：身份验证** ⏱️ 5分钟
输入 `/verify` 开始身份验证流程

**第二步：加入群组** ⏱️ 2分钟
将机器人添加到您的工作群组

**第三步：绑定群组** ⏱️ 3分钟
在群组中完成绑定设置

💡 **小贴士**：完成验证后，您就可以在任何已绑定的群组中查询数据了！

📞 **需要帮助？** 输入 `/help` 获取详细指导"""
    
    async def _get_group_welcome(self, user_name: str, update: Update) -> str:
        """获取群组欢迎信息"""
        chat_title = update.effective_chat.title or "此群组"
        
        # 检查群组绑定状态
        chat_id = update.effective_chat.id
        telegram_group = self.db.query(TelegramGroup).filter_by(
            chat_id=chat_id
        ).first()
        
        if telegram_group and telegram_group.is_active():
            # 群组已绑定
            return f"""👋 **欢迎加入 "{chat_title}"，{user_name}！**

🎉 **好消息**：此群组已绑定到商户系统！

📋 **您需要做的**：
1. **身份验证** ⏱️ 5分钟
   • 输入 `/verify` 开始验证
   • 将验证令牌发送给管理员
   • 等待审核通过

2. **开始使用** ⏱️ 立即可用
   • 验证通过后输入 `/stats` 查看数据
   • 享受便捷的统计查询服务

💡 **立即开始**：输入 `/verify` 完成身份验证！

📞 **需要帮助？** 输入 `/help` 获取详细指导"""
        else:
            # 群组未绑定
            return f"""👋 **欢迎加入 "{chat_title}"，{user_name}！**

⚠️ **注意**：此群组尚未绑定到商户系统。

📋 **需要完成的步骤**：

**第一步：群组绑定** ⏱️ 5分钟
• 群组管理员联系系统管理员获取绑定令牌
• 管理员在群组中输入：`/bind <令牌>`

**第二步：身份验证** ⏱️ 5分钟
• 群组绑定后，输入 `/verify` 验证身份
• 完成验证后即可查询数据

💡 **如果您是群组管理员**：
请联系系统管理员获取绑定令牌

💡 **如果您是普通成员**：
请联系群组管理员完成绑定

📞 **需要帮助？** 输入 `/help` 获取详细指导"""
    
    def _get_welcome_keyboard(self) -> InlineKeyboardMarkup:
        """获取欢迎键盘"""
        return InlineKeyboardMarkup([
            [InlineKeyboardButton("🔐 开始验证", callback_data="start_verify")],
            [InlineKeyboardButton("📊 查看状态", callback_data="check_status")],
            [InlineKeyboardButton("❓ 查看帮助", callback_data="show_help")],
            [InlineKeyboardButton("📞 联系管理员", callback_data="contact_admin")]
        ])
    
    async def detect_user_intent(self, update: Update) -> Optional[str]:
        """智能检测用户意图"""
        try:
            message_text = update.message.text.lower() if update.message and update.message.text else ""
            
            # 定义意图关键词
            intent_keywords = {
                "verification": ["验证", "verify", "身份", "认证", "授权"],
                "binding": ["绑定", "bind", "绑卡", "连接", "关联"],
                "stats": ["统计", "数据", "绑卡", "今日", "本周", "本月", "查询"],
                "help": ["帮助", "help", "使用", "怎么", "如何", "指南"],
                "status": ["状态", "status", "情况", "当前"]
            }
            
            # 检测意图
            for intent, keywords in intent_keywords.items():
                if any(keyword in message_text for keyword in keywords):
                    return intent
            
            return None
            
        except Exception as e:
            self.logger.error(f"检测用户意图失败: {e}")
            return None
    
    async def provide_contextual_help(
        self, 
        update: Update, 
        context: ContextTypes.DEFAULT_TYPE,
        intent: str
    ) -> bool:
        """提供上下文相关的帮助"""
        try:
            user_name = self._get_user_display_name(update)
            
            help_messages = {
                "verification": f"""🔐 **身份验证帮助 - {user_name}**

看起来您想要进行身份验证！

📋 **快速验证步骤**：
1. 输入 `/verify` 申请验证
2. 将验证令牌发送给管理员
3. 等待审核通过

⏱️ **预计时间**：5-30分钟

🚀 **立即开始**：输入 `/verify`""",

                "binding": f"""🔗 **群组绑定帮助 - {user_name}**

看起来您想要绑定群组！

📋 **绑定步骤**：
• 如果您是管理员：联系系统管理员获取令牌，然后输入 `/bind <令牌>`
• 如果您是成员：联系群组管理员完成绑定

⏱️ **预计时间**：5-10分钟

💡 **需要详细指导**：输入 `/help`""",

                "stats": f"""📊 **数据查询帮助 - {user_name}**

看起来您想要查询统计数据！

📋 **查询命令**：
• `/stats` - 今日数据
• `/stats_week` - 本周数据
• `/stats_month` - 本月数据

⚠️ **注意**：需要先完成身份验证和群组绑定

🚀 **立即查询**：输入 `/stats`""",

                "help": f"""❓ **帮助指导 - {user_name}**

我来为您提供详细的使用指导！

📋 **主要功能**：
• 身份验证：`/verify`
• 群组绑定：`/bind <令牌>`
• 数据查询：`/stats`
• 状态查看：`/status`

🚀 **获取完整帮助**：输入 `/help`""",

                "status": f"""📊 **状态查询帮助 - {user_name}**

看起来您想要查看当前状态！

📋 **状态信息包括**：
• 身份验证状态
• 群组绑定状态
• 权限信息
• 使用统计

🚀 **立即查看**：输入 `/status`"""
            }
            
            message = help_messages.get(intent)
            if message:
                keyboard = InlineKeyboardMarkup([
                    [InlineKeyboardButton("📊 查看状态", callback_data="check_status")],
                    [InlineKeyboardButton("❓ 详细帮助", callback_data="show_help")]
                ])
                
                await context.bot.send_message(
                    chat_id=update.effective_chat.id,
                    text=message,
                    reply_markup=keyboard,
                    parse_mode='Markdown'
                )
                return True
            
            return False
            
        except Exception as e:
            self.logger.error(f"提供上下文帮助失败: {e}")
            return False
    
    def _get_user_display_name(self, update: Update) -> str:
        """获取用户显示名称"""
        user = update.effective_user
        if user.first_name and user.last_name:
            return f"{user.first_name} {user.last_name}"
        elif user.first_name:
            return user.first_name
        elif user.username:
            return f"@{user.username}"
        else:
            return "朋友"
    
    async def schedule_reminder(
        self, 
        user_id: int, 
        reminder_type: str, 
        delay_minutes: int = 60
    ):
        """安排提醒（简化实现，实际应用中可以使用任务队列）"""
        # 这里可以实现提醒逻辑，比如：
        # - 验证申请后1小时提醒联系管理员
        # - 绑定失败后提醒重试
        # - 长时间未使用提醒功能介绍
        pass
