/**
 * 存储工具
 * 提供对localStorage和sessionStorage的封装，支持JSON对象的存取
 */

/**
 * 获取localStorage中的值
 * @param {string} key 键名
 * @returns {any} 解析后的值，如果不存在或解析失败则返回null
 */
export function getLocalStorage(key) {
    try {
        const value = localStorage.getItem(key);
        return value ? JSON.parse(value) : null;
    } catch (error) {
        console.error(`获取localStorage失败 [${key}]:`, error);
        return null;
    }
}

/**
 * 设置localStorage的值
 * @param {string} key 键名
 * @param {any} value 要存储的值，会被转换为JSON字符串
 */
export function setLocalStorage(key, value) {
    try {
        localStorage.setItem(key, JSON.stringify(value));
    } catch (error) {
        console.error(`设置localStorage失败 [${key}]:`, error);
    }
}

/**
 * 删除localStorage中的值
 * @param {string} key 键名
 */
export function removeLocalStorage(key) {
    try {
        localStorage.removeItem(key);
    } catch (error) {
        console.error(`删除localStorage失败 [${key}]:`, error);
    }
}

/**
 * 获取sessionStorage中的值
 * @param {string} key 键名
 * @returns {any} 解析后的值，如果不存在或解析失败则返回null
 */
export function getSessionStorage(key) {
    try {
        const value = sessionStorage.getItem(key);
        return value ? JSON.parse(value) : null;
    } catch (error) {
        console.error(`获取sessionStorage失败 [${key}]:`, error);
        return null;
    }
}

/**
 * 设置sessionStorage的值
 * @param {string} key 键名
 * @param {any} value 要存储的值，会被转换为JSON字符串
 */
export function setSessionStorage(key, value) {
    try {
        sessionStorage.setItem(key, JSON.stringify(value));
    } catch (error) {
        console.error(`设置sessionStorage失败 [${key}]:`, error);
    }
}

/**
 * 删除sessionStorage中的值
 * @param {string} key 键名
 */
export function removeSessionStorage(key) {
    try {
        sessionStorage.removeItem(key);
    } catch (error) {
        console.error(`删除sessionStorage失败 [${key}]:`, error);
    }
}

/**
 * 清空所有sessionStorage
 */
export function clearSessionStorage() {
    try {
        sessionStorage.clear();
    } catch (error) {
        console.error('清空sessionStorage失败:', error);
    }
}

/**
 * 清空所有localStorage
 */
export function clearLocalStorage() {
    try {
        localStorage.clear();
    } catch (error) {
        console.error('清空localStorage失败:', error);
    }
} 