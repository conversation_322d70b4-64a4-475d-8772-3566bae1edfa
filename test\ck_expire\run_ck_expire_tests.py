#!/usr/bin/env python3
"""
CK自动过期管理功能测试运行脚本

功能说明：
1. 运行所有CK过期管理相关的测试用例
2. 生成测试报告
3. 验证功能完整性
4. 检查代码覆盖率
"""

import os
import sys
import subprocess
import time
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

def run_command(command, description):
    """
    运行命令并显示结果
    
    Args:
        command: 要执行的命令
        description: 命令描述
    """
    print(f"\n{'='*60}")
    print(f"执行: {description}")
    print(f"命令: {command}")
    print(f"{'='*60}")
    
    start_time = time.time()
    
    try:
        result = subprocess.run(
            command,
            shell=True,
            capture_output=True,
            text=True,
            cwd=project_root
        )
        
        end_time = time.time()
        duration = end_time - start_time
        
        print(f"执行时间: {duration:.2f}秒")
        
        if result.stdout:
            print("标准输出:")
            print(result.stdout)
        
        if result.stderr:
            print("错误输出:")
            print(result.stderr)
        
        if result.returncode == 0:
            print(f"✅ {description} - 成功")
            return True
        else:
            print(f"❌ {description} - 失败 (退出码: {result.returncode})")
            return False
            
    except Exception as e:
        print(f"❌ {description} - 异常: {str(e)}")
        return False

def check_dependencies():
    """检查测试依赖"""
    print("检查测试依赖...")
    
    required_packages = [
        'pytest',
        'pytest-asyncio',
        'pytest-cov',
        'fastapi',
        'sqlalchemy'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package.replace('-', '_'))
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        print(f"❌ 缺少依赖包: {', '.join(missing_packages)}")
        print("请运行: pip install " + " ".join(missing_packages))
        return False
    
    print("✅ 所有依赖包已安装")
    return True

def setup_test_environment():
    """设置测试环境"""
    print("设置测试环境...")
    
    # 设置环境变量
    os.environ['TESTING'] = 'true'
    os.environ['DATABASE_URL'] = 'sqlite:///./test.db'
    
    # 创建测试目录
    test_dir = project_root / 'test' / 'ck_expire'
    test_dir.mkdir(parents=True, exist_ok=True)
    
    print("✅ 测试环境设置完成")
    return True

def run_unit_tests():
    """运行单元测试"""
    test_files = [
        'test/ck_expire/test_ck_expire_service.py',
        'test/ck_expire/test_system_settings_api.py',
        'test/ck_expire/test_scheduler_integration.py'
    ]
    
    success_count = 0
    
    for test_file in test_files:
        if os.path.exists(project_root / test_file):
            command = f"python -m pytest {test_file} -v --tb=short"
            if run_command(command, f"运行 {test_file}"):
                success_count += 1
        else:
            print(f"⚠️  测试文件不存在: {test_file}")
    
    return success_count, len(test_files)

def run_coverage_test():
    """运行代码覆盖率测试"""
    coverage_command = (
        "python -m pytest test/ck_expire/ "
        "--cov=app.services.ck_expire_service "
        "--cov=app.api.v1.endpoints.system_settings "
        "--cov=app.services.scheduler_service "
        "--cov-report=html:htmlcov "
        "--cov-report=term-missing "
        "-v"
    )
    
    return run_command(coverage_command, "代码覆盖率测试")

def run_integration_tests():
    """运行集成测试"""
    integration_command = (
        "python -m pytest test/ck_expire/test_scheduler_integration.py "
        "-v --tb=short -k integration"
    )
    
    return run_command(integration_command, "集成测试")

def run_performance_tests():
    """运行性能测试"""
    performance_command = (
        "python -m pytest test/ck_expire/test_scheduler_integration.py "
        "-v --tb=short -k performance"
    )
    
    return run_command(performance_command, "性能测试")

def generate_test_report():
    """生成测试报告"""
    print("\n" + "="*60)
    print("生成测试报告")
    print("="*60)
    
    report_command = (
        "python -m pytest test/ck_expire/ "
        "--html=test_report.html "
        "--self-contained-html "
        "-v"
    )
    
    if run_command(report_command, "生成HTML测试报告"):
        print("📊 测试报告已生成: test_report.html")
        return True
    
    return False

def main():
    """主函数"""
    print("🚀 开始CK自动过期管理功能测试")
    print(f"项目根目录: {project_root}")
    
    # 检查依赖
    if not check_dependencies():
        sys.exit(1)
    
    # 设置测试环境
    if not setup_test_environment():
        sys.exit(1)
    
    # 运行测试
    test_results = []
    
    # 1. 单元测试
    print("\n🧪 运行单元测试")
    success_count, total_count = run_unit_tests()
    test_results.append(("单元测试", success_count, total_count))
    
    # 2. 集成测试
    print("\n🔗 运行集成测试")
    if run_integration_tests():
        test_results.append(("集成测试", 1, 1))
    else:
        test_results.append(("集成测试", 0, 1))
    
    # 3. 性能测试
    print("\n⚡ 运行性能测试")
    if run_performance_tests():
        test_results.append(("性能测试", 1, 1))
    else:
        test_results.append(("性能测试", 0, 1))
    
    # 4. 代码覆盖率测试
    print("\n📊 运行代码覆盖率测试")
    if run_coverage_test():
        test_results.append(("覆盖率测试", 1, 1))
    else:
        test_results.append(("覆盖率测试", 0, 1))
    
    # 5. 生成测试报告
    print("\n📋 生成测试报告")
    generate_test_report()
    
    # 输出总结
    print("\n" + "="*60)
    print("测试总结")
    print("="*60)
    
    total_success = 0
    total_tests = 0
    
    for test_type, success, total in test_results:
        status = "✅" if success == total else "❌"
        print(f"{status} {test_type}: {success}/{total}")
        total_success += success
        total_tests += total
    
    print(f"\n总体结果: {total_success}/{total_tests}")
    
    if total_success == total_tests:
        print("🎉 所有测试通过！CK自动过期管理功能验证成功！")
        sys.exit(0)
    else:
        print("⚠️  部分测试失败，请检查错误信息并修复问题。")
        sys.exit(1)

if __name__ == "__main__":
    main()
