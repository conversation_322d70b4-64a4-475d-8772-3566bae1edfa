package main

import (
	"context"
	"encoding/json"
	"fmt"
	"os"
	"strings"

	"walmart-bind-card-processor/pkg/walmart"

	"github.com/sirupsen/logrus"
)

func main() {
	// 设置日志
	logger := logrus.New()
	logger.SetLevel(logrus.InfoLevel)
	logger.SetFormatter(&logrus.TextFormatter{
		FullTimestamp: true,
	})

	// 初始化API客户端配置（与Python测试文件相同的参数）
	config := walmart.APIConfig{
		BaseURL:       "https://apicard.swiftpass.cn",
		EncryptionKey: "4yyQNRI5y6YGVBACQiQBUw==",
		Version:       "66",
		Sign:          "c51626c504ea4743a6bccd22f4b0fede@76de0f4887b153a338728b4892640b0d",
	}

	// 创建API客户端（使用修复后的版本，包含Cookie jar）
	client := walmart.NewAPIClient(config, logger)

	// 测试参数（与Python测试文件相同）
	cardNo := "2326992090701192393"

	fmt.Println("🔧 GO版本沃尔玛API测试 - 仅测试余额查询功能")
	fmt.Println(strings.Repeat("=", 60))

	// 测试：余额查询（验证Cookie jar会话管理）
	fmt.Println("\n💰 测试：余额查询（验证会话状态管理）")
	fmt.Println(strings.Repeat("-", 50))

	ctx := context.Background()
	balanceResult, err := client.GetCardBalance(ctx, cardNo, false)
	if err != nil {
		fmt.Printf("❌ 余额查询请求失败: %v\n", err)
		os.Exit(1)
	}

	fmt.Printf("🔍 余额查询结果:\n")
	fmt.Printf("  - 成功: %v\n", balanceResult.Success)
	fmt.Printf("  - 消息: %s\n", balanceResult.Message)
	
	if balanceResult.Success {
		fmt.Printf("  - 余额: %s\n", balanceResult.Balance)
		fmt.Printf("  - 卡余额: %s\n", balanceResult.CardBalance)
		fmt.Printf("  - 余额计数: %s\n", balanceResult.BalanceCnt)
		fmt.Printf("  - 卡片总数: %d\n", balanceResult.TotalCount)
		fmt.Printf("✅ 余额查询成功！会话状态管理正常工作！\n")
	} else {
		fmt.Printf("❌ 余额查询失败！\n")
		if balanceResult.Message == "请先去登录" {
			fmt.Printf("🚨 仍然出现'请先去登录'错误，说明会话状态管理修复可能不完整\n")
		}
	}

	if balanceResult.RawAPIResponse != nil {
		rawJSON, _ := json.MarshalIndent(balanceResult.RawAPIResponse, "  ", "  ")
		fmt.Printf("  - 原始响应: %s\n", string(rawJSON))
	}

	// 测试：用户信息查询
	fmt.Println("\n👤 测试：用户信息查询")
	fmt.Println(strings.Repeat("-", 30))

	userResult, err := client.QueryUserInfo(ctx, false)
	if err != nil {
		fmt.Printf("❌ 用户信息查询请求失败: %v\n", err)
	} else {
		fmt.Printf("🔍 用户信息查询结果:\n")
		fmt.Printf("  - 成功: %v\n", userResult.Success)
		fmt.Printf("  - 消息: %s\n", userResult.Message)
		
		if userResult.Success {
			fmt.Printf("  - 昵称: %s\n", userResult.NickName)
			fmt.Printf("  - 卡片数量: %d\n", userResult.CardCount)
			fmt.Printf("  - 头像: %s\n", userResult.HeadImg)
		}

		if userResult.RawAPIResponse != nil {
			rawJSON, _ := json.MarshalIndent(userResult.RawAPIResponse, "  ", "  ")
			fmt.Printf("  - 原始响应: %s\n", string(rawJSON))
		}
	}

	fmt.Println("\n🎯 测试总结")
	fmt.Println(strings.Repeat("=", 60))
	
	if balanceResult.Success {
		fmt.Println("✅ 余额查询测试通过！")
		fmt.Println("✅ 余额查询成功 - 会话状态管理正常工作")
		fmt.Println("🎉 Cookie jar修复生效，GO版本现在与Python版本行为一致！")
	} else {
		fmt.Println("❌ 余额查询失败")
		if balanceResult.Message == "请先去登录" {
			fmt.Println("🚨 仍然出现'请先去登录'错误，会话状态管理修复可能不完整")
		}
		fmt.Println("🔧 可能需要进一步调试会话状态管理")
	}
}
