package service

import (
	"fmt"
	"walmart-bind-card-gateway/internal/model"
)

// merchantService 商户服务实现
type merchantService struct {
	merchantRepo model.MerchantRepository
}

// NewMerchantService 创建商户服务
func NewMerchantService(merchantRepo model.MerchantRepository) model.MerchantService {
	return &merchantService{
		merchantRepo: merchantRepo,
	}
}

// GetByAPIKey 根据API密钥获取商户
func (s *merchantService) GetByAPIKey(apiKey string) (*model.Merchant, error) {
	if apiKey == "" {
		return nil, fmt.Errorf("API密钥不能为空")
	}
	
	return s.merchantRepo.GetByAPIKey(apiKey)
}

// ValidateAPIKey 验证API密钥并返回商户信息
func (s *merchantService) ValidateAPIKey(apiKey string) (*model.Merchant, error) {
	merchant, err := s.GetByAPIKey(apiKey)
	if err != nil {
		return nil, err
	}
	
	if !merchant.IsActive() {
		return nil, fmt.Errorf("商户已禁用")
	}
	
	return merchant, nil
}
