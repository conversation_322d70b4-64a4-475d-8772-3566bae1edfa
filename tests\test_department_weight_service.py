"""
部门权重服务单元测试

测试部门权重算法的正确性和性能
"""

import pytest
import asyncio
from unittest.mock import Mock, patch
from sqlalchemy.orm import Session

from app.services.department_weight_service import DepartmentWeightService
from app.services.enhanced_ck_service import EnhancedCKService
from app.models.department import Department
from app.models.walmart_ck import WalmartCK


class TestDepartmentWeightService:
    """部门权重服务测试类"""
    
    @pytest.fixture
    def mock_db(self):
        """模拟数据库会话"""
        return Mock(spec=Session)
    
    @pytest.fixture
    def weight_service(self, mock_db):
        """创建权重服务实例"""
        return DepartmentWeightService(mock_db)
    
    @pytest.fixture
    def sample_departments(self):
        """示例部门数据"""
        return [
            {
                'id': 1,
                'name': '部门A',
                'binding_weight': 100,
                'total_ck_count': 5,
                'active_ck_count': 4,
                'available_ck_count': 3,
                'can_bind': True
            },
            {
                'id': 2,
                'name': '部门B',
                'binding_weight': 50,
                'total_ck_count': 3,
                'active_ck_count': 2,
                'available_ck_count': 1,
                'can_bind': True
            },
            {
                'id': 3,
                'name': '部门C',
                'binding_weight': 150,
                'total_ck_count': 8,
                'active_ck_count': 6,
                'available_ck_count': 4,
                'can_bind': True
            }
        ]
    
    def test_get_available_departments_with_weights(self, weight_service, mock_db, sample_departments):
        """测试获取可用部门及权重信息"""
        # 模拟数据库查询结果
        mock_query_result = []
        for dept in sample_departments:
            mock_result = Mock()
            mock_result.id = dept['id']
            mock_result.name = dept['name']
            mock_result.binding_weight = dept['binding_weight']
            mock_result.total_ck_count = dept['total_ck_count']
            mock_result.active_ck_count = dept['active_ck_count']
            mock_result.available_ck_count = dept['available_ck_count']
            mock_query_result.append(mock_result)
        
        mock_db.query.return_value.outerjoin.return_value.filter.return_value.group_by.return_value.all.return_value = mock_query_result
        
        # 执行测试
        result = weight_service.get_available_departments_with_weights(merchant_id=1)
        
        # 验证结果
        assert len(result) == 3
        assert result[0]['id'] == 1
        assert result[0]['name'] == '部门A'
        assert result[0]['binding_weight'] == 100
        assert result[0]['can_bind'] == True
    
    def test_select_department_by_weight_single_department(self, weight_service):
        """测试只有一个部门时的权重选择"""
        with patch.object(weight_service, 'get_available_departments_with_weights') as mock_get_depts:
            mock_get_depts.return_value = [
                {
                    'id': 1,
                    'name': '部门A',
                    'binding_weight': 100,
                    'can_bind': True
                }
            ]
            
            result = weight_service.select_department_by_weight(merchant_id=1)
            
            assert result is not None
            assert result['id'] == 1
            assert result['name'] == '部门A'
    
    def test_select_department_by_weight_multiple_departments(self, weight_service):
        """测试多个部门的权重选择算法"""
        with patch.object(weight_service, 'get_available_departments_with_weights') as mock_get_depts:
            mock_get_depts.return_value = [
                {'id': 1, 'name': '部门A', 'binding_weight': 80, 'can_bind': True},
                {'id': 2, 'name': '部门B', 'binding_weight': 20, 'can_bind': True}
            ]
            
            # 进行多次测试以验证权重分配
            results = {}
            test_count = 1000
            
            for _ in range(test_count):
                result = weight_service.select_department_by_weight(merchant_id=1)
                if result:
                    dept_id = result['id']
                    results[dept_id] = results.get(dept_id, 0) + 1
            
            # 验证权重分配比例（允许一定误差）
            dept_a_percentage = (results.get(1, 0) / test_count) * 100
            dept_b_percentage = (results.get(2, 0) / test_count) * 100
            
            # 部门A应该约占80%，部门B约占20%（允许±10%误差）
            assert 70 <= dept_a_percentage <= 90
            assert 10 <= dept_b_percentage <= 30
    
    def test_select_department_by_weight_no_available_departments(self, weight_service):
        """测试没有可用部门时的情况"""
        with patch.object(weight_service, 'get_available_departments_with_weights') as mock_get_depts:
            mock_get_depts.return_value = []
            
            result = weight_service.select_department_by_weight(merchant_id=1)
            
            assert result is None
    
    def test_select_department_by_weight_with_exclusions(self, weight_service):
        """测试排除特定部门的权重选择"""
        with patch.object(weight_service, 'get_available_departments_with_weights') as mock_get_depts:
            mock_get_depts.return_value = [
                {'id': 1, 'name': '部门A', 'binding_weight': 100, 'can_bind': True},
                {'id': 2, 'name': '部门B', 'binding_weight': 50, 'can_bind': True},
                {'id': 3, 'name': '部门C', 'binding_weight': 25, 'can_bind': True}
            ]
            
            # 排除部门1
            result = weight_service.select_department_by_weight(
                merchant_id=1, 
                exclude_department_ids=[1]
            )
            
            assert result is not None
            assert result['id'] in [2, 3]  # 只能选择部门2或3
    
    def test_get_ck_from_selected_department(self, weight_service, mock_db):
        """测试从选中部门获取CK"""
        # 模拟CK查询结果
        mock_ck = Mock(spec=WalmartCK)
        mock_ck.id = 1
        mock_ck.bind_count = 5
        mock_ck.total_limit = 20
        
        mock_db.query.return_value.filter.return_value.order_by.return_value.first.return_value = mock_ck
        
        result = weight_service.get_ck_from_selected_department(
            merchant_id=1, 
            department_id=1
        )
        
        assert result is not None
        assert result.id == 1
        assert result.bind_count == 5
    
    def test_get_weight_distribution_stats(self, weight_service, sample_departments):
        """测试权重分配统计"""
        with patch.object(weight_service, 'get_available_departments_with_weights') as mock_get_depts:
            mock_get_depts.return_value = sample_departments
            
            result = weight_service.get_weight_distribution_stats(merchant_id=1)
            
            assert result['total_departments'] == 3
            assert result['total_weight'] == 300  # 100 + 50 + 150
            assert len(result['departments']) == 3
            
            # 验证权重百分比计算
            dept_a = next(d for d in result['departments'] if d['id'] == 1)
            assert dept_a['weight_percentage'] == 33.33  # 100/300 * 100
    
    def test_validate_weight_configuration_valid(self, weight_service, sample_departments):
        """测试有效权重配置的验证"""
        with patch.object(weight_service, 'get_available_departments_with_weights') as mock_get_depts:
            mock_get_depts.return_value = sample_departments
            
            result = weight_service.validate_weight_configuration(merchant_id=1)
            
            assert result['is_valid'] == True
            assert len(result['issues']) == 0
    
    def test_validate_weight_configuration_no_departments(self, weight_service):
        """测试没有部门时的权重配置验证"""
        with patch.object(weight_service, 'get_available_departments_with_weights') as mock_get_depts:
            mock_get_depts.return_value = []
            
            result = weight_service.validate_weight_configuration(merchant_id=1)
            
            assert result['is_valid'] == False
            assert "没有启用绑卡的部门" in result['issues']
    
    def test_validate_weight_configuration_no_available_ck(self, weight_service):
        """测试部门没有可用CK时的权重配置验证"""
        departments_no_ck = [
            {
                'id': 1,
                'name': '部门A',
                'binding_weight': 100,
                'available_ck_count': 0,
                'can_bind': False
            }
        ]
        
        with patch.object(weight_service, 'get_available_departments_with_weights') as mock_get_depts:
            mock_get_depts.return_value = departments_no_ck
            
            result = weight_service.validate_weight_configuration(merchant_id=1)
            
            assert len(result['warnings']) > 0
            assert "没有可用CK" in result['warnings'][0]


class TestEnhancedCKService:
    """增强CK服务测试类"""
    
    @pytest.fixture
    def mock_db(self):
        """模拟数据库会话"""
        return Mock(spec=Session)
    
    @pytest.fixture
    def ck_service(self, mock_db):
        """创建CK服务实例"""
        return EnhancedCKService(mock_db)
    
    @pytest.mark.asyncio
    async def test_get_optimal_ck_with_weight_algorithm(self, ck_service):
        """测试使用权重算法获取最优CK"""
        with patch.object(ck_service, '_get_ck_with_weight_algorithm') as mock_weight_method:
            mock_ck = Mock(spec=WalmartCK)
            mock_ck.id = 1
            mock_weight_method.return_value = mock_ck
            
            result = await ck_service.get_optimal_ck(
                merchant_id=1,
                use_weight_algorithm=True
            )
            
            assert result is not None
            assert result.id == 1
            mock_weight_method.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_get_optimal_ck_traditional_method(self, ck_service):
        """测试使用传统方法获取最优CK"""
        with patch.object(ck_service, '_get_ck_traditional_method') as mock_traditional_method:
            mock_ck = Mock(spec=WalmartCK)
            mock_ck.id = 2
            mock_traditional_method.return_value = mock_ck
            
            result = await ck_service.get_optimal_ck(
                merchant_id=1,
                use_weight_algorithm=False
            )
            
            assert result is not None
            assert result.id == 2
            mock_traditional_method.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_get_optimal_ck_specific_department(self, ck_service):
        """测试从指定部门获取CK"""
        with patch.object(ck_service, '_get_ck_from_specific_department') as mock_specific_method:
            mock_ck = Mock(spec=WalmartCK)
            mock_ck.id = 3
            mock_specific_method.return_value = mock_ck
            
            result = await ck_service.get_optimal_ck(
                merchant_id=1,
                department_id=1
            )
            
            assert result is not None
            assert result.id == 3
            mock_specific_method.assert_called_once_with(1, 1, None, True)
    
    @pytest.mark.asyncio
    async def test_test_weight_algorithm(self, ck_service):
        """测试权重算法测试功能"""
        with patch.object(ck_service.weight_service, 'select_department_by_weight') as mock_select:
            # 模拟权重选择结果
            mock_select.side_effect = [
                {'id': 1, 'name': '部门A', 'binding_weight': 80},
                {'id': 2, 'name': '部门B', 'binding_weight': 20},
                {'id': 1, 'name': '部门A', 'binding_weight': 80},
                {'id': 1, 'name': '部门A', 'binding_weight': 80},
                {'id': 2, 'name': '部门B', 'binding_weight': 20}
            ]
            
            with patch.object(ck_service.weight_service, 'get_weight_distribution_stats') as mock_stats:
                mock_stats.return_value = {
                    'departments': [
                        {'id': 1, 'weight_percentage': 80.0},
                        {'id': 2, 'weight_percentage': 20.0}
                    ]
                }
                
                result = await ck_service.test_weight_algorithm(merchant_id=1, test_count=5)
                
                assert result['test_count'] == 5
                assert result['total_departments'] == 2
                assert 1 in result['department_selection_count']
                assert 2 in result['department_selection_count']


if __name__ == '__main__':
    pytest.main([__file__, '-v'])
