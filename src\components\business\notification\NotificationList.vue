<template>
  <div class="notification-list">
    <!-- 筛选栏 -->
    <div class="filter-bar">
      <el-form :inline="true" :model="filterForm" class="filter-form">
        <el-form-item label="优先级">
          <el-select v-model="filterForm.priority" placeholder="全部" clearable>
            <el-option label="紧急" value="high" />
            <el-option label="重要" value="medium" />
            <el-option label="普通" value="low" />
          </el-select>
        </el-form-item>
        <el-form-item label="时间范围">
          <el-date-picker v-model="filterForm.timeRange" type="daterange" range-separator="至" start-placeholder="开始日期"
            end-placeholder="结束日期" value-format="YYYY-MM-DD" />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="applyFilter">搜索</el-button>
          <el-button @click="resetFilter">重置</el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 通知列表 -->
    <el-table :data="displayNotifications" style="width: 100%" v-loading="loading">
      <el-table-column type="expand">
        <template #default="props">
          <div class="notification-detail">
            <div class="detail-content">{{ props.row.content }}</div>
            <div v-if="props.row.actionText && props.row.relatedPath" class="detail-action">
              <el-button type="primary" size="small" @click="handleAction(props.row)">
                {{ props.row.actionText }}
              </el-button>
            </div>
          </div>
        </template>
      </el-table-column>

      <el-table-column width="60">
        <template #default="scope">
          <el-badge is-dot :hidden="isNotificationRead(scope.row)" class="read-badge">
            <el-icon :size="24" :color="getIconColor(scope.row.type)">
              <component :is="getIconForType(scope.row.type)" />
            </el-icon>
          </el-badge>
        </template>
      </el-table-column>

      <el-table-column prop="title" label="标题" min-width="200">
        <template #default="scope">
          <div class="notification-title-cell">
            <span>{{ scope.row.title }}</span>
            <el-tag v-if="scope.row.priority === 'high'" type="danger" size="small">紧急</el-tag>
            <el-tag v-else-if="scope.row.priority === 'medium'" type="warning" size="small">重要</el-tag>
          </div>
        </template>
      </el-table-column>

      <el-table-column prop="created_at" label="时间" width="180">
        <template #default="scope">
          {{ formatDateTime(scope.row.created_at || scope.row.createdAt) }}
        </template>
      </el-table-column>

      <el-table-column label="操作" width="250" fixed="right">
        <template #default="scope">
          <el-button v-if="!isNotificationRead(scope.row)" type="primary" size="small" @click.stop="markAsRead(scope.row.id)"
            text>标为已读</el-button>
          <el-button v-else type="info" size="small" @click.stop="markAsUnread(scope.row.id)" text>标为未读</el-button>

          <el-button v-if="canEdit" type="warning" size="small" @click.stop="handleEdit(scope.row)" text>编辑</el-button>
          <el-button v-if="canDelete" type="danger" size="small" @click.stop="handleDelete(scope.row)" text>删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <div class="pagination-container">
      <el-pagination v-model:current-page="currentPage" v-model:page-size="pageSize" :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper" :total="filteredNotifications.length"
        @update:page-size="handleSizeChange" @update:current-page="handleCurrentChange" />
    </div>
  </div>
</template>

<script setup>
import { ref, computed, reactive } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Bell, Warning, InfoFilled, CircleCheck } from '@element-plus/icons-vue'
import { useNotificationStore } from '@/store/modules/notification'
import { useUserStore } from '@/store/modules/user'
import { notificationApi } from '@/api/modules/notification'

const props = defineProps({
  notifications: {
    type: Array,
    required: true
  }
})

const emit = defineEmits(['edit', 'refresh'])

const router = useRouter()
const notificationStore = useNotificationStore()
const userStore = useUserStore()
const loading = ref(false)
const currentPage = ref(1)
const pageSize = ref(10)

// 权限检查
const canEdit = computed(() => {
    return userStore.isSuperAdmin || userStore.userInfo?.permissions?.includes('api:notifications:update')
})

const canDelete = computed(() => {
    return userStore.isSuperAdmin || userStore.userInfo?.permissions?.includes('api:notifications:delete')
})

// 筛选表单
const filterForm = reactive({
  priority: '',
  timeRange: []
})



// 判断通知是否已读 - 兼容多种状态字段格式
const isNotificationRead = (notification) => {
  // 优先使用 read 字段
  if (notification.read !== undefined) {
    return notification.read
  }
  // 其次使用 status 字段，注意后端返回的是大写状态
  if (notification.status !== undefined) {
    return notification.status.toLowerCase() === 'read'
  }
  // 默认为未读
  return false
}

// 根据通知类型获取图标
const getIconForType = (type) => {
  const icons = {
    'system': InfoFilled,
    'security': Warning,
    'business': Bell,
    'success': CircleCheck
  }
  return icons[type] || InfoFilled
}

// 根据通知类型获取图标颜色
const getIconColor = (type) => {
  const colors = {
    'system': '#909399',
    'security': '#F56C6C',
    'business': '#E6A23C',
    'success': '#67C23A'
  }
  return colors[type] || '#909399'
}

// 格式化日期时间
const formatDateTime = (timestamp) => {
  try {
    // 检查是否为有效字符串
    if (!timestamp || typeof timestamp !== 'string') {
      console.warn('无效的时间戳:', timestamp)
      return '无效日期'
    }

    console.log('原始时间戳:', timestamp, '类型:', typeof timestamp)

    // 处理带毫秒的ISO格式 "2025-06-14T15:48:08.505000"
    // 移除可能的毫秒部分，确保兼容性
    const cleanTimestamp = timestamp.replace(/\.\d+$/, '')
    console.log('清理后的时间戳:', cleanTimestamp)

    const date = new Date(cleanTimestamp)
    console.log('解析后的日期对象:', date, 'getTime():', date.getTime())

    // 检查日期是否有效
    if (isNaN(date.getTime())) {
      console.warn('无法解析日期:', timestamp)
      return '无效日期'
    }

    const year = date.getFullYear()
    const month = String(date.getMonth() + 1).padStart(2, '0')
    const day = String(date.getDate()).padStart(2, '0')
    const hours = String(date.getHours()).padStart(2, '0')
    const minutes = String(date.getMinutes()).padStart(2, '0')

    const formattedDate = `${year}-${month}-${day} ${hours}:${minutes}`
    console.log('格式化后的日期:', formattedDate)

    return formattedDate
  } catch (error) {
    console.error('日期格式化错误:', error, '时间戳:', timestamp)
    return '无效日期'
  }
}

// 过滤通知
const filteredNotifications = computed(() => {
  let result = [...props.notifications]

  // 按优先级过滤
  if (filterForm.priority) {
    result = result.filter(item => item.priority === filterForm.priority)
  }

  // 按时间范围过滤
  if (filterForm.timeRange?.length === 2) {
    const [startDate, endDate] = filterForm.timeRange
    result = result.filter(item => {
      try {
        const timestamp = item.created_at || item.createdAt
        if (!timestamp) return false
        const date = new Date(timestamp).toISOString().split('T')[0]
        return date >= startDate && date <= endDate
      } catch (error) {
        console.error('日期过滤错误:', error, item)
        return false
      }
    })
  }

  return result
})

// 当前页显示的通知
const displayNotifications = computed(() => {
  const startIndex = (currentPage.value - 1) * pageSize.value
  const endIndex = startIndex + pageSize.value
  return filteredNotifications.value.slice(startIndex, endIndex)
})

// 应用筛选条件
const applyFilter = () => {
  currentPage.value = 1
}

// 重置筛选条件
const resetFilter = () => {
  filterForm.priority = ''
  filterForm.timeRange = []
  currentPage.value = 1
}

// 标记为已读
const markAsRead = async (id) => {
  try {
    const success = await notificationStore.markAsRead(id)
    if (success) {
      ElMessage.success('已标记为已读')
      // 触发刷新以确保UI更新
      emit('refresh')
    } else {
      ElMessage.error('标记失败')
    }
  } catch (error) {
    console.error('标记已读失败:', error)
    ElMessage.error('标记失败')
  }
}

// 标记为未读
const markAsUnread = async (id) => {
  try {
    const success = await notificationStore.markAsUnread(id)
    if (success) {
      ElMessage.success('已标记为未读')
      // 触发刷新以确保UI更新
      emit('refresh')
    } else {
      ElMessage.error('标记失败')
    }
  } catch (error) {
    console.error('标记未读失败:', error)
    ElMessage.error('标记失败')
  }
}

// 处理通知相关操作
const handleAction = (notification) => {
  if (notification.relatedPath) {
    router.push(notification.relatedPath)
  }
}

// 分页操作
const handleSizeChange = (val) => {
  pageSize.value = val
  currentPage.value = 1
}

const handleCurrentChange = (val) => {
  currentPage.value = val
}

// 编辑通知
const handleEdit = (notification) => {
  emit('edit', notification)
}

// 删除通知
const handleDelete = async (notification) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除通知"${notification.title}"吗？`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )

    await notificationApi.delete(notification.id)
    ElMessage.success('删除通知成功')
    emit('refresh')
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除通知失败:', error)
      ElMessage.error(error.message || '删除通知失败')
    }
  }
}
</script>

<style scoped>
.notification-list {
  width: 100%;
}

.filter-bar {
  margin-bottom: 20px;
}

.filter-form {
  display: flex;
  flex-wrap: wrap;
}

.notification-detail {
  padding: 10px 20px;
}

.detail-content {
  margin-bottom: 15px;
  white-space: pre-line;
}

.detail-action {
  display: flex;
  justify-content: flex-end;
}

.notification-title-cell {
  display: flex;
  align-items: center;
  gap: 8px;
}

.notification-title-cell .el-tag {
  margin-left: auto;
}

.read-badge :deep(.el-badge__content) {
  top: 3px;
  right: 3px;
  transform: translateY(-50%) translateX(100%);
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}
</style>