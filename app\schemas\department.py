from typing import Optional, List, Dict, Any
from pydantic import BaseModel, Field, EmailStr, field_validator
from datetime import datetime


# 部门基础模型
class DepartmentBase(BaseModel):
    name: str = Field(..., description="部门名称")
    code: Optional[str] = Field(None, description="部门代码（创建时自动生成）")
    description: Optional[str] = Field(None, description="部门描述")
    status: bool = Field(True, description="状态，True为启用，False为禁用")

    # 绑卡控制字段
    enable_binding: bool = Field(True, description="进单开关，True为启用绑卡，False为禁用绑卡")
    binding_weight: int = Field(100, description="进单权重，数值越大优先级越高", ge=0, le=10000)

    # 层级关系
    parent_id: Optional[int] = Field(None, description="父部门ID")
    sort_order: int = Field(0, description="排序号")

    # 负责人信息
    manager_id: Optional[int] = Field(None, description="部门负责人ID")
    manager_name: Optional[str] = Field(None, description="部门负责人姓名")
    manager_phone: Optional[str] = Field(None, description="负责人电话")
    manager_email: Optional[EmailStr] = Field(None, description="负责人邮箱")

    # 业务配置
    business_scope: Optional[str] = Field(None, description="业务范围")
    custom_config: Optional[Dict[str, Any]] = Field(None, description="自定义配置JSON")
    remark: Optional[str] = Field(None, description="备注")

    @field_validator("manager_email", mode="before")
    def empty_email_to_none(cls, v):
        if v == "":
            return None
        return v

    class Config:
        from_attributes = True


# 创建部门请求模型
class DepartmentCreate(DepartmentBase):
    merchant_id: Optional[int] = Field(None, description="所属商户ID（超级管理员必填，其他角色自动设置）")


# 更新部门请求模型
class DepartmentUpdate(BaseModel):
    name: Optional[str] = Field(None, description="部门名称")
    code: Optional[str] = Field(None, description="部门代码")
    description: Optional[str] = Field(None, description="部门描述")
    status: Optional[bool] = Field(None, description="状态，True为启用，False为禁用")

    # 绑卡控制字段
    enable_binding: Optional[bool] = Field(None, description="进单开关，True为启用绑卡，False为禁用绑卡")
    binding_weight: Optional[int] = Field(None, description="进单权重，数值越大优先级越高", ge=0, le=10000)

    # 层级关系
    parent_id: Optional[int] = Field(None, description="父部门ID")
    sort_order: Optional[int] = Field(None, description="排序号")

    # 负责人信息
    manager_id: Optional[int] = Field(None, description="部门负责人ID")
    manager_name: Optional[str] = Field(None, description="部门负责人姓名")
    manager_phone: Optional[str] = Field(None, description="负责人电话")
    manager_email: Optional[EmailStr] = Field(None, description="负责人邮箱")

    # 业务配置
    business_scope: Optional[str] = Field(None, description="业务范围")
    custom_config: Optional[Dict[str, Any]] = Field(None, description="自定义配置JSON")
    remark: Optional[str] = Field(None, description="备注")

    @field_validator("manager_email", mode="before")
    def empty_email_to_none(cls, v):
        if v == "":
            return None
        return v

    class Config:
        from_attributes = True


# 数据库中的部门模型
class DepartmentInDB(DepartmentBase):
    id: int
    merchant_id: int
    level: int
    path: str
    created_by: Optional[int]
    created_at: datetime
    updated_at: Optional[datetime]

    class Config:
        from_attributes = True


# API响应模型
class Department(DepartmentInDB):
    pass


# 部门详情模型（包含统计信息）
class DepartmentDetail(Department):
    merchant_name: Optional[str] = Field(None, description="商户名称")
    parent_name: Optional[str] = Field(None, description="父部门名称")
    full_path: Optional[str] = Field(None, description="完整路径")
    user_count: int = Field(0, description="直属用户数")
    total_user_count: int = Field(0, description="总用户数（含子部门）")
    children_count: int = Field(0, description="子部门数")



# 部门树形结构
class DepartmentTree(Department):
    children: List['DepartmentTree'] = Field(default_factory=list, description="子部门")
    user_count: int = Field(0, description="直属用户数")
    total_user_count: int = Field(0, description="总用户数（含子部门）")

    class Config:
        from_attributes = True


# 部门列表响应模型
class DepartmentListResponse(BaseModel):
    items: List[Department]
    total: int
    page: int = 1
    page_size: int = 100


# 部门简要信息（用于下拉选择等）
class DepartmentBrief(BaseModel):
    id: int
    name: str
    code: str
    merchant_id: int
    parent_id: Optional[int]
    level: int
    status: bool
    full_path: Optional[str] = None

    class Config:
        from_attributes = True


# 部门移动请求
class DepartmentMoveRequest(BaseModel):
    target_parent_id: Optional[int] = Field(None, description="目标父部门ID，None表示移动到根级")
    sort_order: Optional[int] = Field(None, description="新的排序号")


# 部门状态更新
class DepartmentStatusUpdate(BaseModel):
    status: bool = Field(..., description="状态，True为启用，False为禁用")


# 部门负责人更新
class DepartmentManagerUpdate(BaseModel):
    manager_id: Optional[int] = Field(None, description="部门负责人ID")
    manager_name: Optional[str] = Field(None, description="部门负责人姓名")
    manager_phone: Optional[str] = Field(None, description="负责人电话")
    manager_email: Optional[EmailStr] = Field(None, description="负责人邮箱")

    @field_validator("manager_email", mode="before")
    def empty_email_to_none(cls, v):
        if v == "":
            return None
        return v


# 部门统计信息
class DepartmentStatistics(BaseModel):
    total_users: int = Field(0, description="总用户数")
    active_users: int = Field(0, description="活跃用户数")
    total_children: int = Field(0, description="子部门总数")
    active_children: int = Field(0, description="活跃子部门数")
    total_records: int = Field(0, description="总绑卡记录数")
    success_records: int = Field(0, description="成功绑卡记录数")
    failed_records: int = Field(0, description="失败绑卡记录数")
    today_records: int = Field(0, description="今日绑卡记录数")


# 部门层级路径
class DepartmentPath(BaseModel):
    id: int
    name: str
    code: str
    level: int

    class Config:
        from_attributes = True


# 部门层级路径列表
class DepartmentPathList(BaseModel):
    paths: List[DepartmentPath] = Field(default_factory=list, description="从根到当前部门的路径")
    current: DepartmentPath = Field(..., description="当前部门")


# 部门用户分配请求
class DepartmentUserAssignRequest(BaseModel):
    user_ids: List[int] = Field(..., description="用户ID列表")
    position: Optional[str] = Field(None, description="职位")
    is_transfer: bool = Field(False, description="是否为转移（结束原组织关系）")


# 部门批量操作请求
class DepartmentBatchRequest(BaseModel):
    department_ids: List[int] = Field(..., description="部门ID列表")
    action: str = Field(..., description="操作类型：enable, disable, delete")


# 部门批量操作响应
class DepartmentBatchResponse(BaseModel):
    success_count: int = Field(0, description="成功数量")
    failed_count: int = Field(0, description="失败数量")
    failed_items: List[Dict[str, Any]] = Field(default_factory=list, description="失败项目详情")


# 部门导入请求
class DepartmentImportRequest(BaseModel):
    departments: List[DepartmentCreate] = Field(..., description="部门列表")
    merchant_id: int = Field(..., description="商户ID")
    overwrite_existing: bool = Field(False, description="是否覆盖已存在的部门")


# 部门导入响应
class DepartmentImportResponse(BaseModel):
    total_count: int = Field(0, description="总数量")
    success_count: int = Field(0, description="成功数量")
    failed_count: int = Field(0, description="失败数量")
    skipped_count: int = Field(0, description="跳过数量")
    failed_items: List[Dict[str, Any]] = Field(default_factory=list, description="失败项目详情")
    created_departments: List[Department] = Field(default_factory=list, description="创建的部门列表")


# 部门权限检查响应
class DepartmentPermissionCheck(BaseModel):
    can_view: bool = Field(False, description="是否可以查看")
    can_edit: bool = Field(False, description="是否可以编辑")
    can_delete: bool = Field(False, description="是否可以删除")
    can_manage_users: bool = Field(False, description="是否可以管理用户")
    can_create_children: bool = Field(False, description="是否可以创建子部门")
    accessible_children: List[int] = Field(default_factory=list, description="可访问的子部门ID列表")


# 绑卡控制相关Schema
class DepartmentBindingControls(BaseModel):
    """部门绑卡控制设置"""
    enable_binding: bool = Field(..., description="进单开关")
    binding_weight: int = Field(..., description="进单权重", ge=0, le=10000)

    class Config:
        from_attributes = True


class DepartmentBindingControlsUpdate(BaseModel):
    """部门绑卡控制更新"""
    enable_binding: Optional[bool] = Field(None, description="进单开关")
    binding_weight: Optional[int] = Field(None, description="进单权重", ge=0, le=10000)

    class Config:
        from_attributes = True


class DepartmentBindingStatus(BaseModel):
    """部门绑卡状态"""
    id: int = Field(..., description="部门ID")
    name: str = Field(..., description="部门名称")
    merchant_id: int = Field(..., description="商户ID")
    enable_binding: bool = Field(..., description="进单开关")
    binding_weight: int = Field(..., description="进单权重")
    can_bind_cards: bool = Field(..., description="是否可以绑卡")
    available_ck_count: int = Field(0, description="可用CK数量")
    total_ck_count: int = Field(0, description="总CK数量")

    class Config:
        from_attributes = True


class DepartmentBindingControlsBatch(BaseModel):
    """批量部门绑卡控制更新"""
    department_ids: List[int] = Field(..., description="部门ID列表")
    enable_binding: Optional[bool] = Field(None, description="进单开关")
    binding_weight: Optional[int] = Field(None, description="进单权重", ge=0, le=10000)

    class Config:
        from_attributes = True


class DepartmentBindingControlsResponse(BaseModel):
    """部门绑卡控制操作响应"""
    success: bool = Field(..., description="操作是否成功")
    message: str = Field(..., description="操作消息")
    updated_count: int = Field(0, description="更新的部门数量")
    changes: Dict[str, Any] = Field(default_factory=dict, description="变更详情")

    class Config:
        from_attributes = True


# 修复DepartmentTree的前向引用
DepartmentTree.model_rebuild()
