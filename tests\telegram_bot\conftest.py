"""
Telegram Bot测试配置文件
"""

import pytest
import os
import sys
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker, Session
from sqlalchemy.pool import StaticPool

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.dirname(__file__))))

from app.db.base_class import Base
from app.models.merchant import Merchant
from app.models.user import User
from app.models.department import Department
from app.models.telegram_group import TelegramGroup
from app.models.telegram_user import TelegramUser
from app.models.telegram_bot_config import TelegramBotConfig
from app.models.telegram_bot_log import TelegramBotLog


# 测试数据库URL（使用内存SQLite）
SQLALCHEMY_DATABASE_URL = "sqlite:///:memory:"


@pytest.fixture(scope="session")
def engine():
    """创建测试数据库引擎"""
    engine = create_engine(
        SQLALCHEMY_DATABASE_URL,
        connect_args={
            "check_same_thread": False,
        },
        poolclass=StaticPool,
    )
    return engine


@pytest.fixture(scope="session")
def tables(engine):
    """创建数据库表"""
    Base.metadata.create_all(bind=engine)
    yield
    Base.metadata.drop_all(bind=engine)


@pytest.fixture
def db_session(engine, tables):
    """创建数据库会话"""
    TestingSessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
    session = TestingSessionLocal()
    try:
        yield session
    finally:
        session.close()


@pytest.fixture
def db(db_session):
    """数据库会话别名"""
    return db_session


@pytest.fixture
def test_merchant(db: Session):
    """创建测试商户"""
    merchant = Merchant(
        name="测试商户",
        code="TEST_MERCHANT",
        status="active",
        contact_person="测试联系人",
        contact_phone="13800138000",
        contact_email="<EMAIL>"
    )
    db.add(merchant)
    db.commit()
    db.refresh(merchant)
    return merchant


@pytest.fixture
def test_user(db: Session):
    """创建测试用户"""
    user = User(
        username="testuser",
        email="<EMAIL>",
        password_hash="hashed_password",
        is_active=True,
        is_superuser=False,
        full_name="Test User"
    )
    db.add(user)
    db.commit()
    db.refresh(user)
    return user


@pytest.fixture
def test_department(db: Session, test_merchant: Merchant):
    """创建测试部门"""
    department = Department(
        name="测试部门",
        merchant_id=test_merchant.id,
        parent_id=None,
        description="测试部门描述"
    )
    db.add(department)
    db.commit()
    db.refresh(department)
    return department


@pytest.fixture
def test_superuser(db: Session):
    """创建测试超级管理员"""
    user = User(
        username="superuser",
        email="<EMAIL>",
        password_hash="hashed_password",
        is_active=True,
        is_superuser=True,
        full_name="Super User"
    )
    db.add(user)
    db.commit()
    db.refresh(user)
    return user


@pytest.fixture
def test_telegram_group_pending(db: Session, test_merchant: Merchant):
    """创建待绑定状态的测试Telegram群组"""
    from app.models.telegram_group import BindStatus, ChatType
    
    group = TelegramGroup(
        chat_id=-1001111111111,
        chat_title="待绑定群组",
        chat_type=ChatType.SUPERGROUP,
        merchant_id=test_merchant.id,
        bind_token="pending_token_123",
        bind_status=BindStatus.PENDING
    )
    db.add(group)
    db.commit()
    db.refresh(group)
    return group


@pytest.fixture
def test_telegram_user_unverified(db: Session):
    """创建未验证的测试Telegram用户"""
    from app.models.telegram_user import VerificationStatus
    
    telegram_user = TelegramUser(
        telegram_user_id=987654321,
        telegram_username="unverified_user",
        telegram_first_name="Unverified",
        telegram_last_name="User",
        verification_status=VerificationStatus.PENDING
    )
    db.add(telegram_user)
    db.commit()
    db.refresh(telegram_user)
    return telegram_user


@pytest.fixture
def test_bot_config(db: Session):
    """创建测试机器人配置"""
    from app.models.telegram_bot_config import ConfigType
    
    configs = [
        TelegramBotConfig(
            config_key="bot_token",
            config_value="test_bot_token_123",
            config_type=ConfigType.STRING,
            description="测试机器人令牌"
        ),
        TelegramBotConfig(
            config_key="webhook_url",
            config_value="https://test.example.com/webhook",
            config_type=ConfigType.STRING,
            description="测试Webhook URL"
        ),
        TelegramBotConfig(
            config_key="rate_limit_global",
            config_value="1000",
            config_type=ConfigType.NUMBER,
            description="全局频率限制"
        ),
        TelegramBotConfig(
            config_key="enable_audit_log",
            config_value="true",
            config_type=ConfigType.BOOLEAN,
            description="启用审计日志"
        )
    ]
    
    for config in configs:
        db.add(config)
    db.commit()
    
    return configs


@pytest.fixture
def clean_db(db: Session):
    """清理数据库"""
    # 删除所有测试数据
    db.query(TelegramBotLog).delete()
    db.query(TelegramBotConfig).delete()
    db.query(TelegramUser).delete()
    db.query(TelegramGroup).delete()
    db.query(Department).delete()
    db.query(User).delete()
    db.query(Merchant).delete()
    db.commit()
    yield
    # 测试后再次清理
    db.query(TelegramBotLog).delete()
    db.query(TelegramBotConfig).delete()
    db.query(TelegramUser).delete()
    db.query(TelegramGroup).delete()
    db.query(Department).delete()
    db.query(User).delete()
    db.query(Merchant).delete()
    db.commit()


# Mock相关的fixtures
@pytest.fixture
def mock_telegram_update():
    """创建模拟的Telegram Update对象"""
    from unittest.mock import Mock
    from telegram import Update, Message, Chat, User as TelegramUser
    
    update = Mock(spec=Update)
    
    # 模拟Chat
    chat = Mock(spec=Chat)
    chat.id = -1001234567890
    chat.type = "supergroup"
    chat.title = "测试群组"
    
    # 模拟User
    user = Mock(spec=TelegramUser)
    user.id = 123456789
    user.username = "testuser"
    user.first_name = "Test"
    user.last_name = "User"
    
    # 模拟Message
    message = Mock(spec=Message)
    message.text = "/help"
    message.reply_text = Mock()
    
    # 设置关联
    update.effective_chat = chat
    update.effective_user = user
    update.message = message
    
    return update


@pytest.fixture
def mock_telegram_context():
    """创建模拟的Telegram Context对象"""
    from unittest.mock import Mock
    from telegram.ext import ContextTypes
    
    return Mock(spec=ContextTypes.DEFAULT_TYPE)


# 测试环境变量设置
@pytest.fixture(autouse=True)
def setup_test_env():
    """设置测试环境变量"""
    os.environ["TESTING"] = "true"
    os.environ["DATABASE_URL"] = SQLALCHEMY_DATABASE_URL
    yield
    # 清理环境变量
    if "TESTING" in os.environ:
        del os.environ["TESTING"]
