<template>
  <div class="api-test-container">
    <el-card shadow="never">
      <template #header>
        <div class="card-header">
          <span class="header-title">API测试工具</span>
          <el-space>
            <el-button type="primary" :icon="Plus" @click="addNewRequest">新建请求</el-button>
            <el-button type="success" :icon="FolderOpened" @click="loadTemplate">加载模板</el-button>
            <el-button type="warning" :icon="Download" @click="exportHistory">导出历史</el-button>
          </el-space>
        </div>
      </template>

      <el-row :gutter="20">
        <!-- 左侧：请求配置 -->
        <el-col :span="12">
          <el-card shadow="never" class="request-config">
            <template #header>
              <span>请求配置</span>
            </template>

            <!-- HTTP方法和URL -->
            <el-form :model="requestConfig" label-width="80px" size="default">
              <el-form-item label="请求方法">
                <el-select v-model="requestConfig.method" style="width: 120px">
                  <el-option label="GET" value="GET" />
                  <el-option label="POST" value="POST" />
                  <el-option label="PUT" value="PUT" />
                  <el-option label="DELETE" value="DELETE" />
                  <el-option label="PATCH" value="PATCH" />
                </el-select>
              </el-form-item>

              <el-form-item label="请求URL">
                <el-input 
                  v-model="requestConfig.url" 
                  placeholder="请输入API地址，如：/api/v1/users"
                  clearable
                >
                  <template #prepend>{{ baseURL }}</template>
                </el-input>
              </el-form-item>

              <!-- 环境选择 -->
              <el-form-item label="环境">
                <el-radio-group v-model="requestConfig.environment">
                  <el-radio label="development">开发环境</el-radio>
                  <el-radio label="production">生产环境</el-radio>
                </el-radio-group>
              </el-form-item>
            </el-form>

            <!-- 请求头配置 -->
            <el-divider content-position="left">请求头</el-divider>
            <div class="headers-section">
              <el-button type="text" :icon="Plus" @click="addHeader" size="small">添加请求头</el-button>
              <div v-for="(header, index) in requestConfig.headers" :key="index" class="header-item">
                <el-row :gutter="10">
                  <el-col :span="8">
                    <el-input v-model="header.key" placeholder="Header名称" size="small" />
                  </el-col>
                  <el-col :span="12">
                    <el-input v-model="header.value" placeholder="Header值" size="small" />
                  </el-col>
                  <el-col :span="4">
                    <el-button type="danger" :icon="Delete" @click="removeHeader(index)" size="small" />
                  </el-col>
                </el-row>
              </div>
            </div>

            <!-- 请求参数 -->
            <el-divider content-position="left">请求参数</el-divider>
            <el-tabs v-model="activeParamTab" type="border-card">
              <!-- Query参数 -->
              <el-tab-pane label="Query参数" name="query">
                <el-button type="text" :icon="Plus" @click="addQueryParam" size="small">添加参数</el-button>
                <div v-for="(param, index) in requestConfig.queryParams" :key="index" class="param-item">
                  <el-row :gutter="10">
                    <el-col :span="8">
                      <el-input v-model="param.key" placeholder="参数名" size="small" />
                    </el-col>
                    <el-col :span="12">
                      <el-input v-model="param.value" placeholder="参数值" size="small" />
                    </el-col>
                    <el-col :span="4">
                      <el-button type="danger" :icon="Delete" @click="removeQueryParam(index)" size="small" />
                    </el-col>
                  </el-row>
                </div>
              </el-tab-pane>

              <!-- Body参数 -->
              <el-tab-pane label="Body参数" name="body">
                <el-radio-group v-model="requestConfig.bodyType" style="margin-bottom: 10px;">
                  <el-radio label="json">JSON</el-radio>
                  <el-radio label="form">Form Data</el-radio>
                  <el-radio label="raw">Raw</el-radio>
                </el-radio-group>

                <!-- JSON编辑器 -->
                <div v-if="requestConfig.bodyType === 'json'">
                  <el-input
                    v-model="requestConfig.bodyJson"
                    type="textarea"
                    :rows="8"
                    placeholder='{"key": "value"}'
                  />
                  <div class="json-tools">
                    <el-button type="text" @click="formatJson" size="small">格式化JSON</el-button>
                    <el-button type="text" @click="validateJson" size="small">验证JSON</el-button>
                  </div>
                </div>

                <!-- Form Data -->
                <div v-else-if="requestConfig.bodyType === 'form'">
                  <el-button type="text" :icon="Plus" @click="addFormParam" size="small">添加字段</el-button>
                  <div v-for="(param, index) in requestConfig.formParams" :key="index" class="param-item">
                    <el-row :gutter="10">
                      <el-col :span="8">
                        <el-input v-model="param.key" placeholder="字段名" size="small" />
                      </el-col>
                      <el-col :span="12">
                        <el-input v-model="param.value" placeholder="字段值" size="small" />
                      </el-col>
                      <el-col :span="4">
                        <el-button type="danger" :icon="Delete" @click="removeFormParam(index)" size="small" />
                      </el-col>
                    </el-row>
                  </div>
                </div>

                <!-- Raw -->
                <div v-else-if="requestConfig.bodyType === 'raw'">
                  <el-input
                    v-model="requestConfig.bodyRaw"
                    type="textarea"
                    :rows="8"
                    placeholder="请输入原始数据"
                  />
                </div>
              </el-tab-pane>
            </el-tabs>

            <!-- 发送请求按钮 -->
            <div class="send-section">
              <el-button 
                type="primary" 
                :icon="Position" 
                @click="sendRequest" 
                :loading="loading"
                size="large"
              >
                发送请求
              </el-button>
              <el-button @click="saveAsTemplate" :icon="DocumentAdd">保存为模板</el-button>
            </div>
          </el-card>
        </el-col>

        <!-- 右侧：响应结果 -->
        <el-col :span="12">
          <el-card shadow="never" class="response-section">
            <template #header>
              <span>响应结果</span>
              <el-button v-if="response" type="text" :icon="DocumentCopy" @click="copyResponse">复制响应</el-button>
            </template>

            <div v-if="!response && !loading" class="no-response">
              <el-empty description="请发送请求查看响应结果" />
            </div>

            <div v-if="loading" class="loading-response">
              <el-skeleton :rows="5" animated />
            </div>

            <div v-if="response" class="response-content">
              <!-- 响应状态 -->
              <div class="response-status">
                <el-tag 
                  :type="getStatusType(response.status)" 
                  size="large"
                >
                  {{ response.status }} {{ response.statusText }}
                </el-tag>
                <span class="response-time">{{ response.responseTime }}ms</span>
              </div>

              <!-- 响应头 -->
              <el-divider content-position="left">响应头</el-divider>
              <div class="response-headers">
                <div v-for="(value, key) in response.headers" :key="key" class="header-row">
                  <strong>{{ key }}:</strong> {{ value }}
                </div>
              </div>

              <!-- 响应体 -->
              <el-divider content-position="left">响应体</el-divider>
              <div class="response-body">
                <el-tabs v-model="activeResponseTab" type="border-card">
                  <el-tab-pane label="格式化" name="formatted">
                    <pre class="json-content">{{ formatResponseBody(response.data) }}</pre>
                  </el-tab-pane>
                  <el-tab-pane label="原始" name="raw">
                    <pre class="raw-content">{{ response.rawData }}</pre>
                  </el-tab-pane>
                </el-tabs>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>

      <!-- 请求历史 -->
      <el-card shadow="never" style="margin-top: 20px;">
        <template #header>
          <div class="card-header">
            <span>请求历史</span>
            <el-button type="danger" :icon="Delete" @click="clearHistory" size="small">清空历史</el-button>
          </div>
        </template>

        <el-table :data="requestHistory" style="width: 100%" max-height="300">
          <el-table-column prop="timestamp" label="时间" width="180">
            <template #default="{ row }">
              {{ formatTime(row.timestamp) }}
            </template>
          </el-table-column>
          <el-table-column prop="method" label="方法" width="80">
            <template #default="{ row }">
              <el-tag :type="getMethodType(row.method)" size="small">{{ row.method }}</el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="url" label="URL" min-width="200" />
          <el-table-column prop="status" label="状态" width="100">
            <template #default="{ row }">
              <el-tag :type="getStatusType(row.status)" size="small">{{ row.status }}</el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="responseTime" label="响应时间" width="100">
            <template #default="{ row }">
              {{ row.responseTime }}ms
            </template>
          </el-table-column>
          <el-table-column label="操作" width="120">
            <template #default="{ row }">
              <el-button type="text" @click="loadFromHistory(row)" size="small">重新发送</el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-card>
    </el-card>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { 
  Plus, Delete, Position, DocumentAdd, DocumentCopy, 
  FolderOpened, Download 
} from '@element-plus/icons-vue'
import { http } from '@/api/request'

// 响应式数据
const loading = ref(false)
const activeParamTab = ref('query')
const activeResponseTab = ref('formatted')
const response = ref(null)
const requestHistory = ref([])

// 基础URL
const baseURL = computed(() => {
  return requestConfig.value.environment === 'development' 
    ? 'http://localhost:8000/api/v1' 
    : 'https://api.example.com/api/v1'
})

// 请求配置
const requestConfig = ref({
  method: 'GET',
  url: '',
  environment: 'development',
  headers: [
    { key: 'Content-Type', value: 'application/json' },
    { key: 'Authorization', value: 'Bearer ' + (localStorage.getItem('walmart_token') || '') }
  ],
  queryParams: [],
  bodyType: 'json',
  bodyJson: '{}',
  bodyRaw: '',
  formParams: []
})

// 方法实现
const addHeader = () => {
  requestConfig.value.headers.push({ key: '', value: '' })
}

const removeHeader = (index) => {
  requestConfig.value.headers.splice(index, 1)
}

const addQueryParam = () => {
  requestConfig.value.queryParams.push({ key: '', value: '' })
}

const removeQueryParam = (index) => {
  requestConfig.value.queryParams.splice(index, 1)
}

const addFormParam = () => {
  requestConfig.value.formParams.push({ key: '', value: '' })
}

const removeFormParam = (index) => {
  requestConfig.value.formParams.splice(index, 1)
}

const formatJson = () => {
  try {
    const parsed = JSON.parse(requestConfig.value.bodyJson)
    requestConfig.value.bodyJson = JSON.stringify(parsed, null, 2)
    ElMessage.success('JSON格式化成功')
  } catch (error) {
    ElMessage.error('JSON格式错误：' + error.message)
  }
}

const validateJson = () => {
  try {
    JSON.parse(requestConfig.value.bodyJson)
    ElMessage.success('JSON格式正确')
  } catch (error) {
    ElMessage.error('JSON格式错误：' + error.message)
  }
}

const sendRequest = async () => {
  if (!requestConfig.value.url) {
    ElMessage.error('请输入请求URL')
    return
  }

  loading.value = true
  const startTime = Date.now()

  try {
    // 构建请求配置
    const config = {
      method: requestConfig.value.method.toLowerCase(),
      url: requestConfig.value.url,
      headers: {}
    }

    // 添加请求头
    requestConfig.value.headers.forEach(header => {
      if (header.key && header.value) {
        config.headers[header.key] = header.value
      }
    })

    // 添加查询参数
    if (requestConfig.value.queryParams.length > 0) {
      const params = new URLSearchParams()
      requestConfig.value.queryParams.forEach(param => {
        if (param.key && param.value) {
          params.append(param.key, param.value)
        }
      })
      config.params = params
    }

    // 添加请求体
    if (['post', 'put', 'patch'].includes(config.method)) {
      if (requestConfig.value.bodyType === 'json') {
        try {
          config.data = JSON.parse(requestConfig.value.bodyJson)
        } catch (error) {
          ElMessage.error('JSON格式错误')
          loading.value = false
          return
        }
      } else if (requestConfig.value.bodyType === 'form') {
        const formData = new FormData()
        requestConfig.value.formParams.forEach(param => {
          if (param.key && param.value) {
            formData.append(param.key, param.value)
          }
        })
        config.data = formData
        config.headers['Content-Type'] = 'multipart/form-data'
      } else if (requestConfig.value.bodyType === 'raw') {
        config.data = requestConfig.value.bodyRaw
      }
    }

    // 发送请求
    const result = await http.request(config)
    const endTime = Date.now()
    const responseTime = endTime - startTime

    // 构建响应对象
    response.value = {
      status: result.status || 200,
      statusText: result.statusText || 'OK',
      headers: result.headers || {},
      data: result.data,
      rawData: JSON.stringify(result.data, null, 2),
      responseTime
    }

    // 添加到历史记录
    addToHistory({
      timestamp: new Date(),
      method: requestConfig.value.method,
      url: requestConfig.value.url,
      status: response.value.status,
      responseTime,
      config: JSON.parse(JSON.stringify(requestConfig.value))
    })

    ElMessage.success('请求发送成功')

  } catch (error) {
    const endTime = Date.now()
    const responseTime = endTime - startTime

    response.value = {
      status: error.response?.status || 0,
      statusText: error.response?.statusText || 'Error',
      headers: error.response?.headers || {},
      data: error.response?.data || { error: error.message },
      rawData: JSON.stringify(error.response?.data || { error: error.message }, null, 2),
      responseTime
    }

    // 添加到历史记录
    addToHistory({
      timestamp: new Date(),
      method: requestConfig.value.method,
      url: requestConfig.value.url,
      status: response.value.status,
      responseTime,
      config: JSON.parse(JSON.stringify(requestConfig.value))
    })

    ElMessage.error('请求失败：' + error.message)
  } finally {
    loading.value = false
  }
}

const addToHistory = (record) => {
  requestHistory.value.unshift(record)
  // 只保留最近50条记录
  if (requestHistory.value.length > 50) {
    requestHistory.value = requestHistory.value.slice(0, 50)
  }
  // 保存到本地存储
  localStorage.setItem('api_test_history', JSON.stringify(requestHistory.value))
}

const loadFromHistory = (record) => {
  requestConfig.value = JSON.parse(JSON.stringify(record.config))
  ElMessage.success('已加载历史请求配置')
}

const clearHistory = async () => {
  try {
    await ElMessageBox.confirm('确定要清空所有请求历史吗？', '确认操作', {
      type: 'warning'
    })
    requestHistory.value = []
    localStorage.removeItem('api_test_history')
    ElMessage.success('历史记录已清空')
  } catch {
    // 用户取消
  }
}

const copyResponse = () => {
  if (response.value) {
    navigator.clipboard.writeText(response.value.rawData)
    ElMessage.success('响应内容已复制到剪贴板')
  }
}

const formatResponseBody = (data) => {
  try {
    return JSON.stringify(data, null, 2)
  } catch {
    return data
  }
}

const getStatusType = (status) => {
  if (status >= 200 && status < 300) return 'success'
  if (status >= 300 && status < 400) return 'warning'
  if (status >= 400) return 'danger'
  return 'info'
}

const getMethodType = (method) => {
  const types = {
    'GET': 'success',
    'POST': 'primary',
    'PUT': 'warning',
    'DELETE': 'danger',
    'PATCH': 'info'
  }
  return types[method] || 'info'
}

const formatTime = (timestamp) => {
  return new Date(timestamp).toLocaleString()
}

const addNewRequest = () => {
  requestConfig.value = {
    method: 'GET',
    url: '',
    environment: 'development',
    headers: [
      { key: 'Content-Type', value: 'application/json' },
      { key: 'Authorization', value: 'Bearer ' + (localStorage.getItem('walmart_token') || '') }
    ],
    queryParams: [],
    bodyType: 'json',
    bodyJson: '{}',
    bodyRaw: '',
    formParams: []
  }
  response.value = null
  ElMessage.success('已创建新请求')
}

const saveAsTemplate = () => {
  const templates = JSON.parse(localStorage.getItem('api_test_templates') || '[]')
  const templateName = prompt('请输入模板名称：')
  if (templateName) {
    templates.push({
      name: templateName,
      config: JSON.parse(JSON.stringify(requestConfig.value)),
      createdAt: new Date()
    })
    localStorage.setItem('api_test_templates', JSON.stringify(templates))
    ElMessage.success('模板保存成功')
  }
}

const loadTemplate = () => {
  const templates = JSON.parse(localStorage.getItem('api_test_templates') || '[]')
  if (templates.length === 0) {
    ElMessage.info('暂无保存的模板')
    return
  }

  // 这里可以实现一个模板选择对话框
  ElMessage.info('模板加载功能开发中...')
}

const exportHistory = () => {
  const data = JSON.stringify(requestHistory.value, null, 2)
  const blob = new Blob([data], { type: 'application/json' })
  const url = URL.createObjectURL(blob)
  const a = document.createElement('a')
  a.href = url
  a.download = `api-test-history-${new Date().toISOString().split('T')[0]}.json`
  a.click()
  URL.revokeObjectURL(url)
  ElMessage.success('历史记录已导出')
}

// 组件挂载时加载历史记录
onMounted(() => {
  const savedHistory = localStorage.getItem('api_test_history')
  if (savedHistory) {
    try {
      requestHistory.value = JSON.parse(savedHistory)
    } catch (error) {
      console.error('加载历史记录失败:', error)
    }
  }
})
</script>

<style scoped>
.api-test-container {
  padding: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-title {
  font-size: 18px;
  font-weight: bold;
}

.header-item, .param-item {
  margin-bottom: 10px;
}

.headers-section, .params-section {
  margin-top: 10px;
}

.json-tools {
  margin-top: 10px;
  text-align: right;
}

.send-section {
  margin-top: 20px;
  text-align: center;
}

.no-response {
  text-align: center;
  padding: 40px 0;
}

.loading-response {
  padding: 20px;
}

.response-status {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.response-time {
  font-size: 14px;
  color: #666;
}

.response-headers {
  background: #f5f5f5;
  padding: 10px;
  border-radius: 4px;
  max-height: 200px;
  overflow-y: auto;
}

.header-row {
  margin-bottom: 5px;
  font-size: 12px;
}

.response-body {
  margin-top: 15px;
}

.json-content, .raw-content {
  background: #f5f5f5;
  padding: 15px;
  border-radius: 4px;
  max-height: 400px;
  overflow: auto;
  font-family: 'Courier New', monospace;
  font-size: 12px;
  line-height: 1.4;
  white-space: pre-wrap;
  word-break: break-all;
}

.request-config, .response-section {
  height: fit-content;
}

:deep(.el-card__body) {
  padding: 15px;
}

:deep(.el-divider__text) {
  font-weight: bold;
}
</style>
