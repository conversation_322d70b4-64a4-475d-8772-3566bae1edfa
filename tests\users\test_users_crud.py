#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
用户管理CRUD操作测试
测试用户的创建、读取、更新、删除功能
"""

import sys
import os
import time
import random
import string

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.dirname(__file__))))

from test.conftest import TestBase, get_test_accounts, format_test_result, print_test_summary

class UsersCrudTestSuite(TestBase):
    """用户CRUD操作测试套件"""
    
    def __init__(self):
        super().__init__()
        self.results = []
        self.test_accounts = get_test_accounts()
        self.admin_token = None
        self.merchant_token = None
        self.created_users = []  # 记录创建的测试用户，用于清理
    
    def setup(self):
        """测试前置设置"""
        print("=== 测试前置设置 ===")
        
        # 管理员登录
        self.admin_token = self.login("admin", "7c222fb2927d828af22f592134e8932480637c0d")
        if not self.admin_token:
            print("❌ 管理员登录失败")
            return False
        
        # 商户管理员登录
        self.merchant_token = self.login("test1", "********")
        if not self.merchant_token:
            print("❌ 商户管理员登录失败")
            return False
        
        print("✅ 测试前置设置完成")
        return True
    
    def generate_test_user_data(self, prefix: str = "test") -> dict:
        """生成测试用户数据"""
        timestamp = int(time.time())
        random_suffix = ''.join(random.choices(string.ascii_lowercase, k=4))
        
        return {
            "username": f"{prefix}_user_{timestamp}_{random_suffix}",
            "password": "Test123456!",
            "full_name": f"测试用户_{timestamp}",
            "email": f"test_{timestamp}_{random_suffix}@example.com",
            "is_active": True
        }
    
    def test_create_user(self):
        """测试创建用户"""
        print("\n=== 测试创建用户 ===")
        
        if not self.admin_token:
            self.results.append(format_test_result(
                "创建用户前置条件",
                False,
                "缺少管理员token"
            ))
            return
        
        # 测试管理员创建用户
        test_user = self.generate_test_user_data("admin_create")
        status_code, response = self.make_request(
            "POST", "/users", self.admin_token, data=test_user
        )
        
        if status_code == 201 or status_code == 200:
            user_id = response.get("data", {}).get("id") or response.get("id")
            if user_id:
                self.created_users.append(user_id)
                self.results.append(format_test_result(
                    "管理员创建用户",
                    True,
                    f"成功创建用户: {test_user['username']}",
                    {"user_id": user_id, "username": test_user['username']}
                ))
                print(f"✅ 管理员成功创建用户: {test_user['username']}")
            else:
                self.results.append(format_test_result(
                    "管理员创建用户",
                    False,
                    f"创建用户成功但未返回用户ID，状态码: {status_code}"
                ))
                print(f"⚠️ 创建用户成功但未返回用户ID，状态码: {status_code}")
        else:
            self.results.append(format_test_result(
                "管理员创建用户",
                False,
                f"创建用户失败，状态码: {status_code}，响应: {response}"
            ))
            print(f"❌ 管理员创建用户失败，状态码: {status_code}")
        
        # 测试商户管理员创建用户（应该被限制或只能创建本商户用户）
        test_user_merchant = self.generate_test_user_data("merchant_create")
        status_code, response = self.make_request(
            "POST", "/users", self.merchant_token, data=test_user_merchant
        )
        
        if status_code in [403, 401]:
            self.results.append(format_test_result(
                "商户管理员创建用户权限控制",
                True,
                "正确限制商户管理员创建用户权限"
            ))
            print("✅ 正确限制商户管理员创建用户权限")
        elif status_code in [200, 201]:
            user_id = response.get("data", {}).get("id") or response.get("id")
            if user_id:
                self.created_users.append(user_id)
            self.results.append(format_test_result(
                "商户管理员创建用户",
                True,
                f"商户管理员成功创建用户: {test_user_merchant['username']}"
            ))
            print(f"✅ 商户管理员成功创建用户: {test_user_merchant['username']}")
        else:
            self.results.append(format_test_result(
                "商户管理员创建用户",
                False,
                f"商户管理员创建用户失败，状态码: {status_code}"
            ))
            print(f"❌ 商户管理员创建用户失败，状态码: {status_code}")
    
    def test_get_users_list(self):
        """测试获取用户列表"""
        print("\n=== 测试获取用户列表 ===")
        
        # 测试管理员获取用户列表
        if self.admin_token:
            status_code, response = self.make_request("GET", "/users", self.admin_token)
            
            if status_code == 200:
                users_data = response.get("data", {})
                users = users_data.get("items", []) if isinstance(users_data, dict) else response.get("items", [])
                
                self.results.append(format_test_result(
                    "管理员获取用户列表",
                    True,
                    f"成功获取 {len(users)} 个用户",
                    {"user_count": len(users)}
                ))
                print(f"✅ 管理员成功获取用户列表，共 {len(users)} 个用户")
            else:
                self.results.append(format_test_result(
                    "管理员获取用户列表",
                    False,
                    f"获取用户列表失败，状态码: {status_code}"
                ))
                print(f"❌ 管理员获取用户列表失败，状态码: {status_code}")
        
        # 测试商户管理员获取用户列表
        if self.merchant_token:
            status_code, response = self.make_request("GET", "/users", self.merchant_token)
            
            if status_code == 200:
                users_data = response.get("data", {})
                users = users_data.get("items", []) if isinstance(users_data, dict) else response.get("items", [])
                
                self.results.append(format_test_result(
                    "商户管理员获取用户列表",
                    True,
                    f"成功获取 {len(users)} 个用户（应只包含本商户用户）",
                    {"user_count": len(users)}
                ))
                print(f"✅ 商户管理员成功获取用户列表，共 {len(users)} 个用户")
            elif status_code in [403, 401]:
                self.results.append(format_test_result(
                    "商户管理员用户列表权限控制",
                    True,
                    "正确限制商户管理员访问用户列表"
                ))
                print("✅ 正确限制商户管理员访问用户列表")
            else:
                self.results.append(format_test_result(
                    "商户管理员获取用户列表",
                    False,
                    f"获取用户列表失败，状态码: {status_code}"
                ))
                print(f"❌ 商户管理员获取用户列表失败，状态码: {status_code}")
    
    def test_get_user_detail(self):
        """测试获取用户详情"""
        print("\n=== 测试获取用户详情 ===")
        
        if not self.admin_token:
            return
        
        # 先获取用户列表，找一个用户ID
        status_code, response = self.make_request("GET", "/users", self.admin_token)
        if status_code != 200:
            self.results.append(format_test_result(
                "获取用户详情前置条件",
                False,
                "无法获取用户列表"
            ))
            return
        
        users_data = response.get("data", {})
        users = users_data.get("items", []) if isinstance(users_data, dict) else response.get("items", [])
        
        if not users:
            self.results.append(format_test_result(
                "获取用户详情前置条件",
                False,
                "用户列表为空"
            ))
            return
        
        # 测试获取第一个用户的详情
        user_id = users[0].get("id")
        if not user_id:
            self.results.append(format_test_result(
                "获取用户详情前置条件",
                False,
                "用户数据中缺少ID字段"
            ))
            return
        
        status_code, response = self.make_request("GET", f"/users/{user_id}", self.admin_token)
        
        if status_code == 200:
            user_detail = response.get("data", response)
            self.results.append(format_test_result(
                "获取用户详情",
                True,
                f"成功获取用户详情: {user_detail.get('username', 'unknown')}",
                {"user_id": user_id}
            ))
            print(f"✅ 成功获取用户详情: {user_detail.get('username', 'unknown')}")
        else:
            self.results.append(format_test_result(
                "获取用户详情",
                False,
                f"获取用户详情失败，状态码: {status_code}"
            ))
            print(f"❌ 获取用户详情失败，状态码: {status_code}")
    
    def test_update_user(self):
        """测试更新用户"""
        print("\n=== 测试更新用户 ===")
        
        if not self.admin_token or not self.created_users:
            self.results.append(format_test_result(
                "更新用户前置条件",
                False,
                "缺少管理员token或测试用户"
            ))
            return
        
        # 使用创建的测试用户进行更新测试
        user_id = self.created_users[0]
        update_data = {
            "full_name": f"更新的测试用户_{int(time.time())}",
            "email": f"updated_{int(time.time())}@example.com"
        }
        
        status_code, response = self.make_request(
            "PUT", f"/users/{user_id}", self.admin_token, data=update_data
        )
        
        if status_code == 200:
            self.results.append(format_test_result(
                "更新用户",
                True,
                f"成功更新用户: {user_id}",
                {"user_id": user_id, "update_data": update_data}
            ))
            print(f"✅ 成功更新用户: {user_id}")
        else:
            self.results.append(format_test_result(
                "更新用户",
                False,
                f"更新用户失败，状态码: {status_code}，响应: {response}"
            ))
            print(f"❌ 更新用户失败，状态码: {status_code}")
    
    def test_delete_user(self):
        """测试删除用户"""
        print("\n=== 测试删除用户 ===")
        
        if not self.admin_token or not self.created_users:
            self.results.append(format_test_result(
                "删除用户前置条件",
                False,
                "缺少管理员token或测试用户"
            ))
            return
        
        # 删除创建的测试用户
        for user_id in self.created_users[:]:  # 使用切片复制，避免在迭代时修改列表
            status_code, response = self.make_request(
                "DELETE", f"/users/{user_id}", self.admin_token
            )
            
            if status_code in [200, 204]:
                self.results.append(format_test_result(
                    f"删除用户_{user_id}",
                    True,
                    f"成功删除用户: {user_id}"
                ))
                print(f"✅ 成功删除用户: {user_id}")
                self.created_users.remove(user_id)
            else:
                self.results.append(format_test_result(
                    f"删除用户_{user_id}",
                    False,
                    f"删除用户失败，状态码: {status_code}"
                ))
                print(f"❌ 删除用户失败，状态码: {status_code}")
    
    def cleanup(self):
        """清理测试数据"""
        print("\n=== 清理测试数据 ===")
        
        if self.admin_token and self.created_users:
            for user_id in self.created_users[:]:
                status_code, _ = self.make_request(
                    "DELETE", f"/users/{user_id}", self.admin_token
                )
                if status_code in [200, 204]:
                    print(f"✅ 清理测试用户: {user_id}")
                    self.created_users.remove(user_id)
                else:
                    print(f"⚠️ 清理测试用户失败: {user_id}")
    
    def run_all_tests(self):
        """运行所有用户CRUD测试"""
        print("🚀 开始用户CRUD操作测试")
        print("="*60)
        
        start_time = time.time()
        
        # 前置设置
        if not self.setup():
            print("❌ 测试前置设置失败，跳过测试")
            return []
        
        try:
            # 运行所有测试
            self.test_create_user()
            self.test_get_users_list()
            self.test_get_user_detail()
            self.test_update_user()
            self.test_delete_user()
        finally:
            # 清理测试数据
            self.cleanup()
        
        end_time = time.time()
        duration = end_time - start_time
        
        # 打印测试摘要
        print_test_summary(self.results)
        print(f"测试耗时: {duration:.2f}秒")
        
        return self.results

def main():
    """主函数"""
    test_suite = UsersCrudTestSuite()
    results = test_suite.run_all_tests()
    
    # 判断测试是否通过
    failed_tests = [r for r in results if not r.get("success", False)]
    
    if not failed_tests:
        print("\n🎉 所有用户CRUD测试通过！")
        return 0
    else:
        print(f"\n⚠️ 发现 {len(failed_tests)} 个问题需要修复")
        for test in failed_tests:
            print(f"  - {test['test_name']}: {test['message']}")
        return 1

if __name__ == "__main__":
    sys.exit(main())
