# 对账台API接口文档

## 概述

对账台API提供了完整的绑卡数据统计和查询功能，支持部门层级统计、CK统计、绑卡记录查询以及数据导出等功能。

## 功能特性

- **部门层级统计**: 支持多级部门的绑卡统计数据查询
- **CK统计**: 查看部门下各个CK的绑卡统计信息
- **绑卡记录查询**: 查看具体CK的成功绑卡记录明细
- **数据导出**: 支持Excel格式的数据导出
- **权限控制**: 基于用户权限的数据隔离
- **时间筛选**: 支持多种时间范围筛选

## API接口列表

### 1. 部门统计接口

**GET** `/api/v1/reconciliation/departments/statistics`

获取各级部门的绑卡统计数据，支持层级钻取。

**请求参数:**
- `page` (int): 页码，默认1
- `page_size` (int): 每页数量，默认20，最大100
- `parent_department_id` (int, 可选): 父部门ID，为空时查询一级部门
- `level` (int, 可选): 部门层级
- `merchant_id` (int, 可选): 商户ID
- `start_date` (date, 可选): 开始日期
- `end_date` (date, 可选): 结束日期
- `time_range` (str): 时间范围，可选值：today/yesterday/week/month/custom

**响应示例:**
```json
{
  "data": [
    {
      "id": 1,
      "name": "一级部门1",
      "merchantName": "沃尔玛(中国)投资有限公司",
      "type": "department",
      "level": 1,
      "hasChildren": true,
      "successCount": 500,
      "successAmount": 50000000,
      "parentDepartment": null
    }
  ],
  "total": 10,
  "page": 1,
  "pageSize": 20,
  "totalPages": 1
}
```

### 2. CK统计接口

**GET** `/api/v1/reconciliation/departments/{department_id}/ck-statistics`

获取指定部门下CK的统计数据。

**请求参数:**
- `department_id` (int): 部门ID
- `page` (int): 页码，默认1
- `page_size` (int): 每页数量，默认20
- `start_date` (date, 可选): 开始日期
- `end_date` (date, 可选): 结束日期
- `time_range` (str): 时间范围
- `min_success_count` (int): 最小成功笔数筛选，默认1
- `ck_status` (str): CK状态筛选，可选值：all/active/inactive

**响应示例:**
```json
{
  "data": [
    {
      "ckId": 101,
      "ckSign": "user_***_sign01",
      "ckDescription": "华东区主力CK",
      "departmentName": "一级部门1",
      "merchantName": "沃尔玛(中国)投资有限公司",
      "active": true,
      "successCount": 45,
      "successAmount": 4500000
    }
  ],
  "total": 5,
  "page": 1,
  "pageSize": 20,
  "totalPages": 1
}
```

### 3. 绑卡记录接口

**GET** `/api/v1/reconciliation/ck/{ck_id}/records`

获取指定CK的成功绑卡记录明细。

**请求参数:**
- `ck_id` (int): CK ID
- `page` (int): 页码，默认1
- `page_size` (int): 每页数量，默认20
- `start_date` (date, 可选): 开始日期
- `end_date` (date, 可选): 结束日期
- `time_range` (str): 时间范围
- `search_keyword` (str, 可选): 搜索关键词（订单号/卡号）

**响应示例:**
```json
{
  "data": [
    {
      "id": "uuid-string",
      "merchantOrderId": "M2024010001",
      "cardNumber": "6234***3456",
      "amount": 100000,
      "actualAmount": 100000,
      "createdAt": "2024-01-15T14:30:25",
      "callbackStatus": "success"
    }
  ],
  "total": 100,
  "page": 1,
  "pageSize": 20,
  "totalPages": 5
}
```

### 4. 数据导出接口

#### 4.1 导出部门统计

**GET** `/api/v1/reconciliation/export/departments`

导出部门统计数据为Excel文件。

**请求参数:** 与部门统计接口相同

**响应:** Excel文件下载

#### 4.2 导出CK统计

**GET** `/api/v1/reconciliation/export/ck/{department_id}`

导出CK统计数据为Excel文件。

**请求参数:** 与CK统计接口相同

**响应:** Excel文件下载

#### 4.3 导出绑卡记录

**GET** `/api/v1/reconciliation/export/records/{ck_id}`

导出绑卡记录为Excel文件。

**请求参数:** 与绑卡记录接口相同

**响应:** Excel文件下载

## 权限要求

- **查看权限**: `reconciliation:view` - 查看对账台数据
- **导出权限**: `reconciliation:export` - 导出对账台数据

## 数据隔离

- 超级管理员可以查看所有商户的数据
- 普通用户只能查看自己所属商户的数据
- 部门用户只能查看有权限访问的部门数据

## 时间范围说明

- `today`: 今天
- `yesterday`: 昨天
- `week`: 本周（周一到今天）
- `month`: 本月
- `custom`: 自定义时间范围（需要提供start_date和end_date）

## 错误码说明

- `200`: 成功
- `400`: 请求参数错误
- `401`: 未授权
- `403`: 权限不足
- `404`: 资源不存在
- `422`: 参数验证失败
- `500`: 服务器内部错误

## 使用示例

### JavaScript/前端调用示例

```javascript
import { reconciliationApi } from '@/api'

// 获取部门统计
const getDepartmentStats = async () => {
  try {
    const response = await reconciliationApi.getDepartmentStatistics({
      page: 1,
      page_size: 20,
      time_range: 'today'
    })
    console.log('部门统计数据:', response.data)
  } catch (error) {
    console.error('获取失败:', error)
  }
}

// 导出部门统计
const exportDepartmentStats = async () => {
  try {
    await reconciliationApi.exportDepartmentStatistics({
      time_range: 'month'
    })
    // 文件会自动下载
  } catch (error) {
    console.error('导出失败:', error)
  }
}
```

### Python调用示例

```python
import requests

# 获取访问令牌
headers = {"Authorization": "Bearer your_access_token"}

# 获取部门统计
response = requests.get(
    "http://localhost:8000/api/v1/reconciliation/departments/statistics",
    headers=headers,
    params={
        "page": 1,
        "page_size": 20,
        "time_range": "today"
    }
)

if response.status_code == 200:
    data = response.json()
    print(f"获取到 {len(data['data'])} 条部门统计数据")
else:
    print(f"请求失败: {response.status_code}")
```

## 注意事项

1. 所有接口都需要有效的认证令牌
2. 金额单位为分，前端显示时需要除以100转换为元
3. 卡号和CK标识已进行脱敏处理
4. 导出功能会生成Excel文件，文件名包含时间戳
5. 大数据量查询建议使用分页，避免一次性查询过多数据
