from sqlalchemy import Column, Integer, BigInteger, DateTime, String, Index
from sqlalchemy.ext.declarative import declared_attr
from sqlalchemy.sql import func
from datetime import datetime
from zoneinfo import ZoneInfo

from app.db.base_class import Base

TARGET_TZ = ZoneInfo("Asia/Shanghai")


def local_now():
    """获取当前目标时区的时间"""
    return datetime.now(TARGET_TZ)


class MerchantMixin:
    """多商家基础Mixin，所有需要商家隔离的模型都应继承此类"""

    @declared_attr
    def merchant_id(cls):
        return Column(
            BigInteger, nullable=False, index=True, comment="商家ID，用于多商家数据隔离"
        )


class TimestampMixin:
    """时间戳混入类"""

    created_at = Column(
        DateTime(timezone=True),
        nullable=False,
        default=local_now,
        comment="创建时间",
    )
    updated_at = Column(
        DateTime(timezone=True),
        nullable=False,
        default=local_now,
        onupdate=local_now,
        comment="更新时间",
    )

    @declared_attr
    def __table_args__(cls):
        return (
            Index(f"ix_{cls.__tablename__}_created_at", "created_at"),
            Index(f"ix_{cls.__tablename__}_updated_at", "updated_at"),
        )


class BaseModel(Base):
    """所有模型的基类"""

    __abstract__ = True

    id = Column(BigInteger, primary_key=True, autoincrement=True, comment="主键")

    def to_dict(self):
        """将模型转换为字典"""
        return {c.name: getattr(self, c.name) for c in self.__table__.columns}


class MerchantModel(BaseModel, MerchantMixin, TimestampMixin):
    """商家模型基类，带有时间戳和商家ID"""

    __abstract__ = True

    @classmethod
    def get_merchant_column(cls):
        """获取商家ID列名，用于查询过滤"""
        return getattr(cls, "merchant_id")
