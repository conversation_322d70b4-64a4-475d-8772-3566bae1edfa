"""
CK选择机制监控服务
提供实时监控、性能分析和告警功能
"""

import time
import asyncio
from typing import Dict, List, Any, Optional
from dataclasses import dataclass, asdict
from collections import defaultdict, deque
from sqlalchemy.orm import Session
from sqlalchemy import func

from app.models.walmart_ck import WalmartCK
from app.models.department import Department
from app.core.logger import get_logger


@dataclass
class CKMetrics:
    """CK性能指标"""
    timestamp: float
    total_requests: int
    successful_requests: int
    failed_requests: int
    avg_response_time: float
    p95_response_time: float
    qps: float
    ck_usage_distribution: Dict[int, int]
    department_usage_distribution: Dict[int, int]
    active_ck_count: int
    exhausted_ck_count: int


@dataclass
class AlertRule:
    """告警规则"""
    name: str
    condition: str  # 'success_rate_low', 'response_time_high', 'ck_exhausted', etc.
    threshold: float
    duration: int  # 持续时间（秒）
    enabled: bool = True


class CKMonitoringService:
    """CK选择机制监控服务"""
    
    def __init__(self, db: Session):
        self.db = db
        self.logger = get_logger("ck_monitoring")
        
        # 性能数据缓存（最近1小时的数据）
        self.metrics_history: deque = deque(maxlen=3600)  # 1小时，每秒一个数据点
        
        # 实时统计
        self.current_stats = {
            'total_requests': 0,
            'successful_requests': 0,
            'failed_requests': 0,
            'response_times': deque(maxlen=1000),  # 最近1000个请求的响应时间
            'ck_usage_count': defaultdict(int),
            'department_usage_count': defaultdict(int),
            'start_time': time.time()
        }
        
        # 告警规则
        self.alert_rules = [
            AlertRule("成功率过低", "success_rate_low", 0.95, 60),
            AlertRule("响应时间过高", "response_time_high", 100.0, 30),  # 100ms
            AlertRule("CK耗尽", "ck_exhausted", 0.9, 10),  # 90%的CK被耗尽
            AlertRule("QPS异常", "qps_abnormal", 10.0, 60),  # QPS低于10
        ]
        
        # 告警状态
        self.alert_states = {}
        
        # 监控任务
        self.monitoring_task = None
    
    def record_request(
        self, 
        success: bool, 
        response_time: float, 
        ck_id: Optional[int] = None,
        department_id: Optional[int] = None
    ):
        """记录请求指标"""
        self.current_stats['total_requests'] += 1
        
        if success:
            self.current_stats['successful_requests'] += 1
            if ck_id:
                self.current_stats['ck_usage_count'][ck_id] += 1
            if department_id:
                self.current_stats['department_usage_count'][department_id] += 1
        else:
            self.current_stats['failed_requests'] += 1
        
        self.current_stats['response_times'].append(response_time)
    
    def get_current_metrics(self) -> CKMetrics:
        """获取当前性能指标"""
        now = time.time()
        elapsed_time = now - self.current_stats['start_time']
        
        # 计算QPS
        qps = self.current_stats['total_requests'] / elapsed_time if elapsed_time > 0 else 0
        
        # 计算响应时间指标
        response_times = list(self.current_stats['response_times'])
        if response_times:
            avg_response_time = sum(response_times) / len(response_times)
            sorted_times = sorted(response_times)
            p95_index = int(len(sorted_times) * 0.95)
            p95_response_time = sorted_times[p95_index] if p95_index < len(sorted_times) else 0
        else:
            avg_response_time = 0
            p95_response_time = 0
        
        # 获取CK状态
        ck_stats = self._get_ck_status()
        
        return CKMetrics(
            timestamp=now,
            total_requests=self.current_stats['total_requests'],
            successful_requests=self.current_stats['successful_requests'],
            failed_requests=self.current_stats['failed_requests'],
            avg_response_time=avg_response_time,
            p95_response_time=p95_response_time,
            qps=qps,
            ck_usage_distribution=dict(self.current_stats['ck_usage_count']),
            department_usage_distribution=dict(self.current_stats['department_usage_count']),
            active_ck_count=ck_stats['active_count'],
            exhausted_ck_count=ck_stats['exhausted_count']
        )
    
    def _get_ck_status(self) -> Dict[str, int]:
        """获取CK状态统计"""
        try:
            # 查询活跃CK数量
            active_count = self.db.query(WalmartCK).filter(
                WalmartCK.active == True,
                WalmartCK.is_deleted == False
            ).count()
            
            # 查询耗尽的CK数量（bind_count >= total_limit）
            exhausted_count = self.db.query(WalmartCK).filter(
                WalmartCK.bind_count >= WalmartCK.total_limit,
                WalmartCK.is_deleted == False
            ).count()
            
            return {
                'active_count': active_count,
                'exhausted_count': exhausted_count
            }
        except Exception as e:
            self.logger.error(f"获取CK状态失败: {e}")
            return {'active_count': 0, 'exhausted_count': 0}
    
    def check_alerts(self, metrics: CKMetrics):
        """检查告警条件"""
        for rule in self.alert_rules:
            if not rule.enabled:
                continue
            
            alert_triggered = False
            alert_message = ""
            
            if rule.condition == "success_rate_low":
                success_rate = metrics.successful_requests / metrics.total_requests if metrics.total_requests > 0 else 1
                if success_rate < rule.threshold:
                    alert_triggered = True
                    alert_message = f"成功率过低: {success_rate:.2%} < {rule.threshold:.2%}"
            
            elif rule.condition == "response_time_high":
                if metrics.p95_response_time > rule.threshold:
                    alert_triggered = True
                    alert_message = f"P95响应时间过高: {metrics.p95_response_time:.2f}ms > {rule.threshold}ms"
            
            elif rule.condition == "ck_exhausted":
                total_ck = metrics.active_ck_count + metrics.exhausted_ck_count
                if total_ck > 0:
                    exhausted_rate = metrics.exhausted_ck_count / total_ck
                    if exhausted_rate > rule.threshold:
                        alert_triggered = True
                        alert_message = f"CK耗尽率过高: {exhausted_rate:.2%} > {rule.threshold:.2%}"
            
            elif rule.condition == "qps_abnormal":
                if metrics.qps < rule.threshold:
                    alert_triggered = True
                    alert_message = f"QPS异常低: {metrics.qps:.2f} < {rule.threshold}"
            
            # 处理告警状态
            if alert_triggered:
                if rule.name not in self.alert_states:
                    self.alert_states[rule.name] = {
                        'start_time': time.time(),
                        'triggered': False
                    }
                
                # 检查是否达到持续时间
                if time.time() - self.alert_states[rule.name]['start_time'] >= rule.duration:
                    if not self.alert_states[rule.name]['triggered']:
                        self._send_alert(rule.name, alert_message, metrics)
                        self.alert_states[rule.name]['triggered'] = True
            else:
                # 清除告警状态
                if rule.name in self.alert_states:
                    if self.alert_states[rule.name]['triggered']:
                        self._send_recovery_alert(rule.name, metrics)
                    del self.alert_states[rule.name]
    
    def _send_alert(self, rule_name: str, message: str, metrics: CKMetrics):
        """发送告警"""
        self.logger.error(f"🚨 CK监控告警 - {rule_name}: {message}")
        self.logger.error(f"当前指标: QPS={metrics.qps:.2f}, "
                         f"成功率={metrics.successful_requests/metrics.total_requests:.2%}, "
                         f"P95响应时间={metrics.p95_response_time:.2f}ms")
        
        # 这里可以集成其他告警渠道（邮件、短信、钉钉等）
        # await self._send_to_alert_channels(rule_name, message, metrics)
    
    def _send_recovery_alert(self, rule_name: str, metrics: CKMetrics):
        """发送恢复告警"""
        self.logger.info(f"✅ CK监控恢复 - {rule_name}: 指标已恢复正常")
    
    async def start_monitoring(self, interval: int = 60):
        """启动监控任务"""
        if self.monitoring_task and not self.monitoring_task.done():
            self.logger.warning("监控任务已在运行")
            return
        
        self.monitoring_task = asyncio.create_task(self._monitoring_loop(interval))
        self.logger.info(f"CK监控服务已启动，监控间隔: {interval}秒")
    
    async def stop_monitoring(self):
        """停止监控任务"""
        if self.monitoring_task:
            self.monitoring_task.cancel()
            try:
                await self.monitoring_task
            except asyncio.CancelledError:
                pass
            self.logger.info("CK监控服务已停止")
    
    async def _monitoring_loop(self, interval: int):
        """监控循环"""
        while True:
            try:
                # 收集当前指标
                metrics = self.get_current_metrics()
                
                # 保存到历史记录
                self.metrics_history.append(metrics)
                
                # 检查告警
                self.check_alerts(metrics)
                
                # 记录监控日志
                if metrics.total_requests > 0:
                    success_rate = metrics.successful_requests / metrics.total_requests
                    self.logger.info(
                        f"CK监控报告: QPS={metrics.qps:.2f}, "
                        f"成功率={success_rate:.2%}, "
                        f"P95响应时间={metrics.p95_response_time:.2f}ms, "
                        f"活跃CK={metrics.active_ck_count}, "
                        f"耗尽CK={metrics.exhausted_ck_count}"
                    )
                
                await asyncio.sleep(interval)
                
            except Exception as e:
                self.logger.error(f"监控循环异常: {e}")
                await asyncio.sleep(interval)
    
    def get_metrics_history(self, duration: int = 3600) -> List[CKMetrics]:
        """获取历史指标（默认最近1小时）"""
        now = time.time()
        cutoff_time = now - duration
        
        return [
            metrics for metrics in self.metrics_history 
            if metrics.timestamp >= cutoff_time
        ]
    
    def get_performance_report(self) -> Dict[str, Any]:
        """生成性能报告"""
        current_metrics = self.get_current_metrics()
        history = self.get_metrics_history()
        
        if not history:
            return asdict(current_metrics)
        
        # 计算趋势
        qps_trend = [m.qps for m in history[-10:]]  # 最近10个数据点
        response_time_trend = [m.avg_response_time for m in history[-10:]]
        
        return {
            'current_metrics': asdict(current_metrics),
            'trends': {
                'qps_trend': qps_trend,
                'response_time_trend': response_time_trend
            },
            'summary': {
                'monitoring_duration': time.time() - self.current_stats['start_time'],
                'total_data_points': len(history),
                'alert_count': len(self.alert_states)
            }
        }
