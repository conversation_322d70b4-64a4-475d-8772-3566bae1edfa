#!/usr/bin/env python3
"""
快速测试商户CK隔离机制
"""

import sys
import os
import uuid
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from app.core.config import settings
from app.models.user import User
from app.models.merchant import Merchant
from app.models.department import Department
from app.models.walmart_ck import WalmartCK
from app.models.card_record import CardRecord
from app.services.walmart_ck_service_new import WalmartCKService
from app.services.card_record_service import CardRecordService


def create_test_session():
    """创建测试数据库会话"""
    engine = create_engine(settings.SQLALCHEMY_DATABASE_URI)
    SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
    return SessionLocal()


def setup_test_data(db):
    """设置测试数据"""
    print("设置测试数据...")

    # 清理可能存在的测试数据
    db.query(CardRecord).filter(CardRecord.merchant_order_id.like("ISOLATION_TEST_%")).delete()
    db.query(WalmartCK).filter(WalmartCK.sign.like("isolation_test_%")).delete()
    db.query(User).filter(User.username.like("isolation_test_%")).delete()
    db.query(Department).filter(Department.name.like("隔离测试%")).delete()
    db.query(Merchant).filter(Merchant.name.like("隔离测试%")).delete()
    db.commit()

    # 创建测试商户
    merchant1 = Merchant(
        name="隔离测试商户1",
        code="ISOLATION_TEST_MERCHANT_1",
        contact_name="测试联系人1",
        contact_phone="13800000001",
        api_key="test_api_key_1",
        api_secret="test_api_secret_1"
    )
    merchant2 = Merchant(
        name="隔离测试商户2",
        code="ISOLATION_TEST_MERCHANT_2",
        contact_name="测试联系人2",
        contact_phone="13800000002",
        api_key="test_api_key_2",
        api_secret="test_api_secret_2"
    )
    db.add_all([merchant1, merchant2])
    db.flush()

    # 创建测试部门
    dept1 = Department(
        name="隔离测试部门1",
        code="ISOLATION_TEST_DEPT_1",
        merchant_id=merchant1.id,
        parent_id=None
    )
    dept2 = Department(
        name="隔离测试部门2",
        code="ISOLATION_TEST_DEPT_2",
        merchant_id=merchant2.id,
        parent_id=None
    )
    db.add_all([dept1, dept2])
    db.flush()

    # 创建测试用户
    user1 = User(
        username="isolation_test_user1",
        email="<EMAIL>",
        hashed_password="test_password",
        merchant_id=merchant1.id,
        department_id=dept1.id
    )
    user2 = User(
        username="isolation_test_user2",
        email="<EMAIL>",
        hashed_password="test_password",
        merchant_id=merchant2.id,
        department_id=dept2.id
    )
    db.add_all([user1, user2])
    db.flush()

    # 创建测试CK
    ck1 = WalmartCK(
        sign="isolation_test_ck1_sign",
        merchant_id=merchant1.id,
        department_id=dept1.id,
        daily_limit=100,
        hourly_limit=10,
        active=True,
        created_by=user1.id
    )
    ck2 = WalmartCK(
        sign="isolation_test_ck2_sign",
        merchant_id=merchant2.id,
        department_id=dept2.id,
        daily_limit=100,
        hourly_limit=10,
        active=True,
        created_by=user2.id
    )
    db.add_all([ck1, ck2])
    db.commit()

    return {
        'merchant1': merchant1,
        'merchant2': merchant2,
        'dept1': dept1,
        'dept2': dept2,
        'user1': user1,
        'user2': user2,
        'ck1': ck1,
        'ck2': ck2
    }


def test_ck_merchant_isolation(db, test_data):
    """测试CK商户隔离"""
    print("\n=== 测试CK商户隔离 ===")

    ck_service = WalmartCKService(db)

    # 测试1: 正确的商户CK关联验证
    print("测试1: 正确的商户CK关联验证")
    result1 = ck_service.validate_ck_merchant_isolation(test_data['ck1'].id, test_data['merchant1'].id)
    print(f"CK1属于商户1: {result1}")
    assert result1 == True, "CK1应该属于商户1"

    result2 = ck_service.validate_ck_merchant_isolation(test_data['ck2'].id, test_data['merchant2'].id)
    print(f"CK2属于商户2: {result2}")
    assert result2 == True, "CK2应该属于商户2"

    # 测试2: 跨商户CK使用验证（应该失败）
    print("\n测试2: 跨商户CK使用验证")
    result3 = ck_service.validate_ck_merchant_isolation(test_data['ck1'].id, test_data['merchant2'].id)
    print(f"CK1属于商户2: {result3}")
    assert result3 == False, "CK1不应该属于商户2"

    result4 = ck_service.validate_ck_merchant_isolation(test_data['ck2'].id, test_data['merchant1'].id)
    print(f"CK2属于商户1: {result4}")
    assert result4 == False, "CK2不应该属于商户1"

    print("✅ CK商户隔离验证测试通过")


def test_get_available_ck_isolation(db, test_data):
    """测试获取可用CK的商户隔离"""
    print("\n=== 测试获取可用CK的商户隔离 ===")

    ck_service = WalmartCKService(db)

    # 测试1: 商户1应该只能获取到自己的CK
    print("测试1: 商户1获取可用CK")
    ck1 = ck_service.get_available_ck(test_data['merchant1'].id, test_data['dept1'].id)
    print(f"商户1获取到的CK: {ck1.id if ck1 else None}")
    assert ck1 is not None, "商户1应该能获取到CK"
    assert ck1.id == test_data['ck1'].id, "商户1应该获取到自己的CK"
    assert ck1.merchant_id == test_data['merchant1'].id, "获取到的CK应该属于商户1"

    # 测试2: 商户2应该只能获取到自己的CK
    print("测试2: 商户2获取可用CK")
    ck2 = ck_service.get_available_ck(test_data['merchant2'].id, test_data['dept2'].id)
    print(f"商户2获取到的CK: {ck2.id if ck2 else None}")
    assert ck2 is not None, "商户2应该能获取到CK"
    assert ck2.id == test_data['ck2'].id, "商户2应该获取到自己的CK"
    assert ck2.merchant_id == test_data['merchant2'].id, "获取到的CK应该属于商户2"

    print("✅ 获取可用CK隔离测试通过")


def test_card_binding_isolation(db, test_data):
    """测试绑卡的商户隔离"""
    print("\n=== 测试绑卡的商户隔离 ===")

    card_service = CardRecordService(db)

    # 创建测试绑卡记录
    card_record = CardRecord(
        id=str(uuid.uuid4()),
        merchant_id=test_data['merchant1'].id,
        department_id=test_data['dept1'].id,
        walmart_ck_id=None,
        merchant_order_id="ISOLATION_TEST_ORDER_001",
        amount=10000,
        card_number="1234567890123456",
        status='pending',
        request_id=str(uuid.uuid4()),
        request_data={}
    )
    db.add(card_record)
    db.commit()

    # 测试1: 使用正确商户的CK绑卡（应该成功验证）
    print("测试1: 使用正确商户的CK绑卡")
    try:
        # 这里只测试验证逻辑，不实际执行绑卡
        validated_card = card_service._validate_card_and_ck(
            str(card_record.id),
            test_data['ck1'].id,
            test_data['user1']
        )
        print("✅ 使用正确商户CK的验证通过")
    except ValueError as e:
        print(f"❌ 使用正确商户CK的验证失败: {e}")
        raise

    # 测试2: 使用其他商户的CK绑卡（应该失败）
    print("测试2: 使用其他商户的CK绑卡")
    try:
        validated_card = card_service._validate_card_and_ck(
            str(card_record.id),
            test_data['ck2'].id,
            test_data['user1']
        )
        print("❌ 使用其他商户CK的验证应该失败但却通过了")
        raise AssertionError("使用其他商户CK的验证应该失败")
    except ValueError as e:
        if "严重安全违规" in str(e):
            print("✅ 使用其他商户CK的验证正确失败")
        else:
            print(f"❌ 验证失败但错误信息不正确: {e}")
            raise

    print("✅ 绑卡商户隔离测试通过")


def test_isolation_integrity_check(db, test_data):
    """测试隔离完整性检查"""
    print("\n=== 测试隔离完整性检查 ===")

    ck_service = WalmartCKService(db)

    # 检查商户1的隔离完整性
    print("检查商户1的隔离完整性")
    result1 = ck_service.check_merchant_isolation_integrity(test_data['merchant1'].id)
    print(f"商户1隔离完整性: {result1}")
    assert result1['merchant_id'] == test_data['merchant1'].id
    assert result1['is_clean'] == True, "商户1的隔离应该是完整的"

    # 检查商户2的隔离完整性
    print("检查商户2的隔离完整性")
    result2 = ck_service.check_merchant_isolation_integrity(test_data['merchant2'].id)
    print(f"商户2隔离完整性: {result2}")
    assert result2['merchant_id'] == test_data['merchant2'].id
    assert result2['is_clean'] == True, "商户2的隔离应该是完整的"

    print("✅ 隔离完整性检查测试通过")


def cleanup_test_data(db):
    """清理测试数据"""
    print("\n清理测试数据...")

    db.query(CardRecord).filter(CardRecord.merchant_order_id.like("ISOLATION_TEST_%")).delete()
    db.query(WalmartCK).filter(WalmartCK.sign.like("isolation_test_%")).delete()
    db.query(User).filter(User.username.like("isolation_test_%")).delete()
    db.query(Department).filter(Department.name.like("隔离测试%")).delete()
    db.query(Merchant).filter(Merchant.name.like("隔离测试%")).delete()
    db.commit()

    print("✅ 测试数据清理完成")


def main():
    """主测试函数"""
    print("开始商户CK隔离机制快速测试")

    db = create_test_session()

    try:
        # 设置测试数据
        test_data = setup_test_data(db)

        # 执行测试
        test_ck_merchant_isolation(db, test_data)
        test_get_available_ck_isolation(db, test_data)
        test_card_binding_isolation(db, test_data)
        test_isolation_integrity_check(db, test_data)

        print("\n🎉 所有测试通过！商户CK隔离机制工作正常")

    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        raise
    finally:
        # 清理测试数据
        cleanup_test_data(db)
        db.close()


if __name__ == "__main__":
    main()
