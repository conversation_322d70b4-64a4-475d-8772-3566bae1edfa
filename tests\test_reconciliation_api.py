"""
对账台API接口单元测试
"""
import pytest
from fastapi.testclient import TestClient
from sqlalchemy.orm import Session
from datetime import date, datetime

from app.main import app
from app.database import get_db
from app.models.user import User
from app.models.merchant import Merchant
from app.models.department import Department
from app.models.walmart_ck import WalmartCK
from app.models.card_record import CardRecord, CardStatus
from app.core.security import create_access_token


class TestReconciliationAPI:
    """对账台API测试类"""
    
    @pytest.fixture
    def client(self):
        """创建测试客户端"""
        return TestClient(app)
    
    @pytest.fixture
    def db_session(self):
        """获取数据库会话"""
        db_gen = get_db()
        db = next(db_gen)
        try:
            yield db
        finally:
            db.close()
    
    @pytest.fixture
    def test_user(self, db_session: Session):
        """创建测试用户"""
        user = User(
            id=999,
            username="test_reconciliation_user",
            email="<EMAIL>",
            is_superuser=True,
            merchant_id=1
        )
        return user
    
    @pytest.fixture
    def auth_headers(self, test_user: User):
        """创建认证头"""
        access_token = create_access_token(subject=test_user.id)
        return {"Authorization": f"Bearer {access_token}"}
    
    def test_get_department_statistics(self, client: TestClient, auth_headers: dict):
        """测试获取部门统计数据"""
        response = client.get(
            "/api/v1/reconciliation/departments/statistics",
            headers=auth_headers,
            params={
                "page": 1,
                "page_size": 10,
                "time_range": "today"
            }
        )
        
        # 检查响应状态码
        assert response.status_code in [200, 403, 500]  # 可能因权限或数据问题返回不同状态码
        
        if response.status_code == 200:
            data = response.json()
            assert "data" in data
            assert "total" in data
            assert "page" in data
            assert "pageSize" in data
            assert isinstance(data["data"], list)
    
    def test_get_ck_statistics(self, client: TestClient, auth_headers: dict):
        """测试获取CK统计数据"""
        department_id = 1  # 假设存在部门ID为1的部门
        
        response = client.get(
            f"/api/v1/reconciliation/departments/{department_id}/ck-statistics",
            headers=auth_headers,
            params={
                "page": 1,
                "page_size": 10,
                "time_range": "today"
            }
        )
        
        # 检查响应状态码
        assert response.status_code in [200, 403, 404, 500]
        
        if response.status_code == 200:
            data = response.json()
            assert "data" in data
            assert "total" in data
            assert isinstance(data["data"], list)
    
    def test_get_binding_records(self, client: TestClient, auth_headers: dict):
        """测试获取绑卡记录"""
        ck_id = 1  # 假设存在CK ID为1的CK
        
        response = client.get(
            f"/api/v1/reconciliation/ck/{ck_id}/records",
            headers=auth_headers,
            params={
                "page": 1,
                "page_size": 10,
                "time_range": "today"
            }
        )
        
        # 检查响应状态码
        assert response.status_code in [200, 403, 404, 500]
        
        if response.status_code == 200:
            data = response.json()
            assert "data" in data
            assert "total" in data
            assert isinstance(data["data"], list)
    
    def test_export_department_statistics(self, client: TestClient, auth_headers: dict):
        """测试导出部门统计数据"""
        response = client.get(
            "/api/v1/reconciliation/export/departments",
            headers=auth_headers,
            params={
                "time_range": "today"
            }
        )
        
        # 检查响应状态码
        assert response.status_code in [200, 403, 500]
        
        if response.status_code == 200:
            # 检查响应头
            assert "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet" in response.headers.get("content-type", "")
            # 检查响应内容不为空
            assert len(response.content) > 0
    
    def test_export_ck_statistics(self, client: TestClient, auth_headers: dict):
        """测试导出CK统计数据"""
        department_id = 1
        
        response = client.get(
            f"/api/v1/reconciliation/export/ck/{department_id}",
            headers=auth_headers,
            params={
                "time_range": "today"
            }
        )
        
        # 检查响应状态码
        assert response.status_code in [200, 403, 404, 500]
        
        if response.status_code == 200:
            # 检查响应头
            assert "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet" in response.headers.get("content-type", "")
            # 检查响应内容不为空
            assert len(response.content) > 0
    
    def test_export_binding_records(self, client: TestClient, auth_headers: dict):
        """测试导出绑卡记录"""
        ck_id = 1
        
        response = client.get(
            f"/api/v1/reconciliation/export/records/{ck_id}",
            headers=auth_headers,
            params={
                "time_range": "today"
            }
        )
        
        # 检查响应状态码
        assert response.status_code in [200, 403, 404, 500]
        
        if response.status_code == 200:
            # 检查响应头
            assert "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet" in response.headers.get("content-type", "")
            # 检查响应内容不为空
            assert len(response.content) > 0
    
    def test_invalid_parameters(self, client: TestClient, auth_headers: dict):
        """测试无效参数"""
        # 测试无效的页码
        response = client.get(
            "/api/v1/reconciliation/departments/statistics",
            headers=auth_headers,
            params={
                "page": 0,  # 无效页码
                "page_size": 10,
                "time_range": "today"
            }
        )
        assert response.status_code == 422  # 参数验证错误
        
        # 测试无效的页面大小
        response = client.get(
            "/api/v1/reconciliation/departments/statistics",
            headers=auth_headers,
            params={
                "page": 1,
                "page_size": 1000,  # 超过最大限制
                "time_range": "today"
            }
        )
        assert response.status_code == 422  # 参数验证错误
    
    def test_unauthorized_access(self, client: TestClient):
        """测试未授权访问"""
        response = client.get("/api/v1/reconciliation/departments/statistics")
        assert response.status_code == 401  # 未授权


if __name__ == "__main__":
    # 运行测试
    pytest.main([__file__, "-v"])
