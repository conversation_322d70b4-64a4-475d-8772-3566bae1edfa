"""
权限检查装饰器集合
"""
from functools import wraps
from typing import Optional, Callable, Any
from fastapi import HTTPException, status
from sqlalchemy.orm import Session

from app.models.user import User
from app.core.exceptions import BusinessException, ErrorCode
from app.core.logging import logger


class PermissionChecker:
    """权限检查器"""
    
    @staticmethod
    def check_basic_permission(user: User, permission_code: str) -> bool:
        """检查基础权限"""
        # 这里应该调用实际的权限检查逻辑
        # 暂时简化实现
        return True
    
    @staticmethod
    def check_merchant_access(user: User, merchant_id: int) -> bool:
        """检查商户访问权限"""
        if user.is_platform_user():
            return True
        return user.merchant_id == merchant_id
    
    @staticmethod
    def check_department_access(user: User, department_id: int, db: Session) -> bool:
        """检查部门访问权限"""
        # 这里应该调用实际的部门权限检查逻辑
        # 暂时简化实现
        return True


def require_permission(permission_code: str):
    """权限检查装饰器"""
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        async def async_wrapper(*args, **kwargs):
            current_user = kwargs.get('current_user')
            if not current_user:
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="未认证用户"
                )
            
            if not PermissionChecker.check_basic_permission(current_user, permission_code):
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail=f"权限不足: 缺少权限 {permission_code}"
                )
            
            return await func(*args, **kwargs)
        
        @wraps(func)
        def sync_wrapper(*args, **kwargs):
            current_user = kwargs.get('current_user')
            if not current_user:
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="未认证用户"
                )
            
            if not PermissionChecker.check_basic_permission(current_user, permission_code):
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail=f"权限不足: 缺少权限 {permission_code}"
                )
            
            return func(*args, **kwargs)
        
        # 检查函数是否是异步的
        import inspect
        if inspect.iscoroutinefunction(func):
            return async_wrapper
        else:
            return sync_wrapper
    
    return decorator


def require_merchant_access(merchant_param: str = "merchant_id"):
    """商户访问权限检查装饰器"""
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        async def async_wrapper(*args, **kwargs):
            current_user = kwargs.get('current_user')
            merchant_id = kwargs.get(merchant_param)
            
            if not current_user:
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="未认证用户"
                )
            
            if merchant_id and not PermissionChecker.check_merchant_access(current_user, merchant_id):
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail="无权访问该商户数据"
                )
            
            return await func(*args, **kwargs)
        
        @wraps(func)
        def sync_wrapper(*args, **kwargs):
            current_user = kwargs.get('current_user')
            merchant_id = kwargs.get(merchant_param)
            
            if not current_user:
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="未认证用户"
                )
            
            if merchant_id and not PermissionChecker.check_merchant_access(current_user, merchant_id):
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail="无权访问该商户数据"
                )
            
            return func(*args, **kwargs)
        
        # 检查函数是否是异步的
        import inspect
        if inspect.iscoroutinefunction(func):
            return async_wrapper
        else:
            return sync_wrapper
    
    return decorator


class ValidationHelper:
    """数据验证辅助类"""
    
    @staticmethod
    def validate_required_fields(data: dict, required_fields: list) -> None:
        """验证必填字段"""
        missing_fields = [field for field in required_fields if field not in data or data[field] is None]
        if missing_fields:
            raise BusinessException(
                message=f"缺少必填字段: {', '.join(missing_fields)}",
                code=ErrorCode.INVALID_PARAMETER
            )
    
    @staticmethod
    def validate_field_length(data: dict, field_limits: dict) -> None:
        """验证字段长度"""
        for field, max_length in field_limits.items():
            if field in data and data[field] and len(str(data[field])) > max_length:
                raise BusinessException(
                    message=f"字段 {field} 长度超过限制 ({max_length})",
                    code=ErrorCode.INVALID_PARAMETER
                )
    
    @staticmethod
    def validate_enum_values(data: dict, enum_fields: dict) -> None:
        """验证枚举值"""
        for field, valid_values in enum_fields.items():
            if field in data and data[field] not in valid_values:
                raise BusinessException(
                    message=f"字段 {field} 的值无效，有效值: {', '.join(valid_values)}",
                    code=ErrorCode.INVALID_PARAMETER
                )


class ErrorHandler:
    """错误处理辅助类"""
    
    @staticmethod
    def handle_database_error(func: Callable) -> Callable:
        """数据库错误处理装饰器"""
        @wraps(func)
        async def async_wrapper(*args, **kwargs):
            try:
                return await func(*args, **kwargs)
            except Exception as e:
                logger.error(f"数据库操作错误: {str(e)}")
                raise BusinessException(
                    message="数据库操作失败",
                    code=ErrorCode.DATABASE_ERROR
                )
        
        @wraps(func)
        def sync_wrapper(*args, **kwargs):
            try:
                return func(*args, **kwargs)
            except Exception as e:
                logger.error(f"数据库操作错误: {str(e)}")
                raise BusinessException(
                    message="数据库操作失败",
                    code=ErrorCode.DATABASE_ERROR
                )
        
        # 检查函数是否是异步的
        import inspect
        if inspect.iscoroutinefunction(func):
            return async_wrapper
        else:
            return sync_wrapper
    
    @staticmethod
    def log_operation(operation_name: str):
        """操作日志装饰器"""
        def decorator(func: Callable) -> Callable:
            @wraps(func)
            async def async_wrapper(*args, **kwargs):
                logger.info(f"开始执行操作: {operation_name}")
                try:
                    result = await func(*args, **kwargs)
                    logger.info(f"操作成功完成: {operation_name}")
                    return result
                except Exception as e:
                    logger.error(f"操作失败: {operation_name}, 错误: {str(e)}")
                    raise
            
            @wraps(func)
            def sync_wrapper(*args, **kwargs):
                logger.info(f"开始执行操作: {operation_name}")
                try:
                    result = func(*args, **kwargs)
                    logger.info(f"操作成功完成: {operation_name}")
                    return result
                except Exception as e:
                    logger.error(f"操作失败: {operation_name}, 错误: {str(e)}")
                    raise
            
            # 检查函数是否是异步的
            import inspect
            if inspect.iscoroutinefunction(func):
                return async_wrapper
            else:
                return sync_wrapper
        
        return decorator
