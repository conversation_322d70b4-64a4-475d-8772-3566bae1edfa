[package]
name = "walmart-bind-card-notify"
version = "0.1.0"
edition = "2021"
authors = ["Walmart Team"]
description = "Walmart绑卡回调通知服务 - Rust版本"

[dependencies]
# 异步运行时
tokio = { version = "1.0", features = ["full"] }

# 数据库相关
sqlx = { version = "0.7", features = ["runtime-tokio-rustls", "mysql", "chrono", "uuid"] }

# HTTP客户端
reqwest = { version = "0.11", features = ["json"] }

# 序列化
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"

# Redis客户端
redis = { version = "0.24", features = ["tokio-comp"] }

# 消息队列
lapin = "2.3"

# 日志
tracing = "0.1"
tracing-subscriber = { version = "0.3", features = ["env-filter"] }

# 时间处理
chrono = { version = "0.4", features = ["serde"] }

# UUID
uuid = { version = "1.0", features = ["v4", "serde"] }

# 错误处理
anyhow = "1.0"
thiserror = "1.0"

# 配置
config = "0.14"

# 并发控制
tokio-util = "0.7"

# URL解析
url = "2.4"

# 加密哈希
sha2 = "0.10"

[profile.release]
# 生产环境优化
opt-level = 3
lto = true
codegen-units = 1
panic = "abort"
strip = true
