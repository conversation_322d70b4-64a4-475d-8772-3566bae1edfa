package model

import (
	"time"
)

// Department 部门模型
type Department struct {
	// 基础字段
	ID        uint      `gorm:"primarykey" json:"id"`
	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`
	// 注意：数据库表中没有deleted_at字段，所以不使用gorm.DeletedAt

	// 商户关联（放在前面，匹配数据库字段顺序）
	MerchantID uint `gorm:"column:merchant_id;not null;comment:商户ID" json:"merchant_id"`

	// 部门信息
	Name        string `gorm:"column:name;type:varchar(100);not null;comment:部门名称" json:"name"`
	Code        string `gorm:"column:code;type:varchar(50);not null;comment:部门代码" json:"code"`
	Description string `gorm:"column:description;type:text;comment:部门描述" json:"description"`
	Status      bool   `gorm:"column:status;default:true;comment:部门状态" json:"status"`

	// 绑卡控制字段
	EnableBinding  bool `gorm:"column:enable_binding;default:true;comment:进单开关" json:"enable_binding"`
	BindingWeight  int  `gorm:"column:binding_weight;default:100;comment:进单权重(0-10000)" json:"binding_weight"`

	// 层级结构
	ParentID    *uint  `gorm:"column:parent_id;comment:父部门ID" json:"parent_id"`
	Level       int    `gorm:"column:level;default:1;comment:部门层级" json:"level"`
	Path        string `gorm:"column:path;type:varchar(500);comment:部门路径" json:"path"`
	SortOrder   int    `gorm:"column:sort_order;default:0;comment:排序" json:"sort_order"`

	// 管理员信息
	ManagerID    *uint  `gorm:"column:manager_id;comment:部门经理ID" json:"manager_id"`
	ManagerName  string `gorm:"column:manager_name;type:varchar(100);comment:部门经理姓名" json:"manager_name"`
	ManagerPhone string `gorm:"column:manager_phone;type:varchar(50);comment:部门经理电话" json:"manager_phone"`
	ManagerEmail string `gorm:"column:manager_email;type:varchar(100);comment:部门经理邮箱" json:"manager_email"`

	// 业务配置
	BusinessScope  string `gorm:"column:business_scope;type:text;comment:业务范围" json:"business_scope"`
	CustomConfig   string `gorm:"column:custom_config;type:text;comment:自定义配置" json:"custom_config"`
	Remark         string `gorm:"column:remark;type:text;comment:备注" json:"remark"`
	CreatedBy      *uint  `gorm:"column:created_by;comment:创建者ID" json:"created_by"`



	// 关联关系 - 注释掉避免循环引用问题
	// WalmartCKs []WalmartCK `gorm:"foreignKey:DepartmentID" json:"walmart_cks,omitempty"`
}

// TableName 指定表名
func (Department) TableName() string {
	return "departments"
}