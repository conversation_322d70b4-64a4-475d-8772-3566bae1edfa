-- ========================================
-- 沃尔玛CK批量启用/禁用权限迁移脚本
-- 为CK批量启用和批量禁用接口添加权限配置
-- ========================================

USE `walmart_card_db`;

-- ========================================
-- 1. 记录迁移开始
-- ========================================

INSERT INTO `migration_logs` (`migration_name`, `status`, `message`, `created_at`)
VALUES ('v2.6.0-add-walmart-ck-batch-enable', 'started', '开始执行沃尔玛CK批量启用/禁用权限迁移', NOW());

-- ========================================
-- 2. 添加批量启用权限
-- ========================================

-- 添加沃尔玛CK批量启用权限
INSERT IGNORE INTO `permissions` (
    `code`,
    `name`,
    `description`,
    `resource_type`,
    `resource_path`,
    `is_enabled`,
    `sort_order`
) VALUES (
    'api:walmart-ck:batch-enable',
    '批量启用沃尔玛CK',
    '允许批量启用多个沃尔玛CK卡片，支持按条件筛选批量操作',
    'api',
    '/api/v1/walmart-ck/batch-enable',
    1,
    1060
);

-- ========================================
-- 3. 添加批量禁用权限
-- ========================================

-- 添加沃尔玛CK批量禁用权限
INSERT IGNORE INTO `permissions` (
    `code`,
    `name`,
    `description`,
    `resource_type`,
    `resource_path`,
    `is_enabled`,
    `sort_order`
) VALUES (
    'api:walmart-ck:batch-disable',
    '批量禁用沃尔玛CK',
    '允许批量禁用多个沃尔玛CK卡片，支持按条件筛选批量操作',
    'api',
    '/api/v1/walmart-ck/batch-disable',
    1,
    1070
);

-- ========================================
-- 4. 为角色分配批量启用权限
-- ========================================

-- 为超级管理员分配批量启用权限
INSERT IGNORE INTO `role_permissions` (`role_id`, `permission_id`)
SELECT r.id, p.id
FROM `roles` r, `permissions` p
WHERE r.code = 'super_admin'
AND p.code = 'api:walmart-ck:batch-enable';

-- 为商户管理员分配批量启用权限
INSERT IGNORE INTO `role_permissions` (`role_id`, `permission_id`)
SELECT r.id, p.id
FROM `roles` r, `permissions` p
WHERE r.code = 'merchant_admin'
AND p.code = 'api:walmart-ck:batch-enable';

-- 为CK供应商分配批量启用权限
INSERT IGNORE INTO `role_permissions` (`role_id`, `permission_id`)
SELECT r.id, p.id
FROM `roles` r, `permissions` p
WHERE r.code = 'ck_supplier'
AND p.code = 'api:walmart-ck:batch-enable';

-- ========================================
-- 5. 为角色分配批量禁用权限
-- ========================================

-- 为超级管理员分配批量禁用权限
INSERT IGNORE INTO `role_permissions` (`role_id`, `permission_id`)
SELECT r.id, p.id
FROM `roles` r, `permissions` p
WHERE r.code = 'super_admin'
AND p.code = 'api:walmart-ck:batch-disable';

-- 为商户管理员分配批量禁用权限
INSERT IGNORE INTO `role_permissions` (`role_id`, `permission_id`)
SELECT r.id, p.id
FROM `roles` r, `permissions` p
WHERE r.code = 'merchant_admin'
AND p.code = 'api:walmart-ck:batch-disable';

-- 为CK供应商分配批量禁用权限
INSERT IGNORE INTO `role_permissions` (`role_id`, `permission_id`)
SELECT r.id, p.id
FROM `roles` r, `permissions` p
WHERE r.code = 'ck_supplier'
AND p.code = 'api:walmart-ck:batch-disable';

-- ========================================
-- 6. 验证权限配置
-- ========================================

-- 检查批量启用权限是否正确插入
SELECT
    'CK批量启用权限检查' as check_type,
    COUNT(*) as permission_count,
    CASE
        WHEN COUNT(*) >= 1 THEN '✓ CK批量启用权限配置完整'
        ELSE '⚠ CK批量启用权限配置不完整'
    END as status
FROM `permissions`
WHERE code = 'api:walmart-ck:batch-enable';

-- 检查批量禁用权限是否正确插入
SELECT
    'CK批量禁用权限检查' as check_type,
    COUNT(*) as permission_count,
    CASE
        WHEN COUNT(*) >= 1 THEN '✓ CK批量禁用权限配置完整'
        ELSE '⚠ CK批量禁用权限配置不完整'
    END as status
FROM `permissions`
WHERE code = 'api:walmart-ck:batch-disable';

-- 检查超级管理员批量启用权限分配
SELECT
    '超级管理员CK批量启用权限' as check_type,
    COUNT(*) as assigned_permissions,
    CASE
        WHEN COUNT(*) >= 1 THEN '✓ 超级管理员批量启用权限分配完整'
        ELSE '⚠ 超级管理员批量启用权限分配不完整'
    END as status
FROM `role_permissions` rp
JOIN `roles` r ON rp.role_id = r.id
JOIN `permissions` p ON rp.permission_id = p.id
WHERE r.code = 'super_admin'
AND p.code = 'api:walmart-ck:batch-enable';

-- 检查超级管理员批量禁用权限分配
SELECT
    '超级管理员CK批量禁用权限' as check_type,
    COUNT(*) as assigned_permissions,
    CASE
        WHEN COUNT(*) >= 1 THEN '✓ 超级管理员批量禁用权限分配完整'
        ELSE '⚠ 超级管理员批量禁用权限分配不完整'
    END as status
FROM `role_permissions` rp
JOIN `roles` r ON rp.role_id = r.id
JOIN `permissions` p ON rp.permission_id = p.id
WHERE r.code = 'super_admin'
AND p.code = 'api:walmart-ck:batch-disable';

-- 检查商户管理员批量启用权限分配
SELECT
    '商户管理员CK批量启用权限' as check_type,
    COUNT(*) as assigned_permissions,
    CASE
        WHEN COUNT(*) >= 1 THEN '✓ 商户管理员批量启用权限分配完整'
        ELSE '⚠ 商户管理员批量启用权限分配不完整'
    END as status
FROM `role_permissions` rp
JOIN `roles` r ON rp.role_id = r.id
JOIN `permissions` p ON rp.permission_id = p.id
WHERE r.code = 'merchant_admin'
AND p.code = 'api:walmart-ck:batch-enable';

-- 检查商户管理员批量禁用权限分配
SELECT
    '商户管理员CK批量禁用权限' as check_type,
    COUNT(*) as assigned_permissions,
    CASE
        WHEN COUNT(*) >= 1 THEN '✓ 商户管理员批量禁用权限分配完整'
        ELSE '⚠ 商户管理员批量禁用权限分配不完整'
    END as status
FROM `role_permissions` rp
JOIN `roles` r ON rp.role_id = r.id
JOIN `permissions` p ON rp.permission_id = p.id
WHERE r.code = 'merchant_admin'
AND p.code = 'api:walmart-ck:batch-disable';

-- 检查CK供应商批量启用权限分配
SELECT
    'CK供应商CK批量启用权限' as check_type,
    COUNT(*) as assigned_permissions,
    CASE
        WHEN COUNT(*) >= 1 THEN '✓ CK供应商批量启用权限分配完整'
        ELSE '⚠ CK供应商批量启用权限分配不完整'
    END as status
FROM `role_permissions` rp
JOIN `roles` r ON rp.role_id = r.id
JOIN `permissions` p ON rp.permission_id = p.id
WHERE r.code = 'ck_supplier'
AND p.code = 'api:walmart-ck:batch-enable';

-- 检查CK供应商批量禁用权限分配
SELECT
    'CK供应商CK批量禁用权限' as check_type,
    COUNT(*) as assigned_permissions,
    CASE
        WHEN COUNT(*) >= 1 THEN '✓ CK供应商批量禁用权限分配完整'
        ELSE '⚠ CK供应商批量禁用权限分配不完整'
    END as status
FROM `role_permissions` rp
JOIN `roles` r ON rp.role_id = r.id
JOIN `permissions` p ON rp.permission_id = p.id
WHERE r.code = 'ck_supplier'
AND p.code = 'api:walmart-ck:batch-disable';

-- ========================================
-- 7. 显示权限分配结果
-- ========================================

-- 显示批量启用权限分配结果
SELECT
    '=== CK批量启用权限分配结果 ===' as summary,
    r.name as role_name,
    r.code as role_code,
    p.name as permission_name,
    p.code as permission_code
FROM `role_permissions` rp
JOIN `roles` r ON rp.role_id = r.id
JOIN `permissions` p ON rp.permission_id = p.id
WHERE p.code = 'api:walmart-ck:batch-enable'
ORDER BY r.code;

-- 显示批量禁用权限分配结果
SELECT
    '=== CK批量禁用权限分配结果 ===' as summary,
    r.name as role_name,
    r.code as role_code,
    p.name as permission_name,
    p.code as permission_code
FROM `role_permissions` rp
JOIN `roles` r ON rp.role_id = r.id
JOIN `permissions` p ON rp.permission_id = p.id
WHERE p.code = 'api:walmart-ck:batch-disable'
ORDER BY r.code;

-- ========================================
-- 8. 显示当前所有沃尔玛CK相关权限
-- ========================================
SELECT
    '=== 沃尔玛CK权限列表 ===' as summary,
    p.code as permission_code,
    p.name as permission_name,
    p.sort_order as sort_order,
    GROUP_CONCAT(r.name ORDER BY r.name SEPARATOR ', ') as assigned_roles
FROM `permissions` p
LEFT JOIN `role_permissions` rp ON p.id = rp.permission_id
LEFT JOIN `roles` r ON rp.role_id = r.id
WHERE p.code LIKE '%walmart-ck%'
GROUP BY p.id, p.code, p.name, p.sort_order
ORDER BY p.sort_order;

-- ========================================
-- 9. 记录迁移完成
-- ========================================

UPDATE `migration_logs`
SET `status` = 'completed',
    `message` = '沃尔玛CK批量启用/禁用权限迁移执行完成',
    `completed_at` = NOW()
WHERE `migration_name` = 'v2.6.0-add-walmart-ck-batch-enable'
AND `status` = 'started';

-- ========================================
-- 10. 迁移完成提示
-- ========================================

SELECT
    '🎉 迁移完成' as status,
    '沃尔玛CK批量启用/禁用权限已成功配置' as message,
    'api:walmart-ck:batch-enable, api:walmart-ck:batch-disable' as new_permissions,
    '超级管理员、商户管理员、CK供应商' as assigned_roles;