#!/usr/bin/env python3
"""
极限负载测试 - 200个并发请求
验证生产级CK服务在极高并发下的表现
"""

import asyncio
import sys
import os
import time
import statistics
from collections import Counter, defaultdict
from typing import Dict, List, Tuple
import concurrent.futures

# 添加项目路径
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.insert(0, project_root)

from app.api.deps import get_db
from app.models.walmart_ck import WalmartCK
from app.services.production_ck_service import ProductionCKService
from app.services.redis_ck_wrapper import create_optimized_ck_service
from app.core.logging import get_logger
from app.core.redis import get_redis

logger = get_logger("extreme_load_test")


class ExtremeLoadTester:
    """极限负载测试器"""
    
    def __init__(self):
        self.db = next(get_db())
        self.redis_client = None
        
    async def __aenter__(self):
        self.redis_client = await get_redis()
        return self
        
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self.db:
            self.db.close()
        if self.redis_client:
            await self.redis_client.aclose()
    
    async def run_extreme_load_test(
        self, 
        merchant_id: int = 2, 
        concurrent_requests: int = 200,
        test_rounds: int = 3
    ):
        """运行极限负载测试"""
        print(f"🚀 极限负载测试 - {concurrent_requests} 个并发请求")
        print(f"商户ID: {merchant_id}")
        print(f"测试轮数: {test_rounds}")
        print("="*80)
        
        # 检查可用CK
        available_cks = await self._check_available_cks(merchant_id)
        if len(available_cks) < 2:
            print("❌ 需要至少2个可用CK才能测试负载均衡")
            return
        
        # 清理测试环境
        await self._cleanup_test_environment(merchant_id)
        
        # 执行对比测试
        results = {
            'original_service': [],
            'production_service': []
        }
        
        # 测试原有服务
        print(f"\n🎯 测试原有服务 ({concurrent_requests} 并发)")
        print("-"*60)
        
        for round_num in range(test_rounds):
            print(f"第 {round_num + 1} 轮...")
            round_results = await self._test_original_service(
                merchant_id, concurrent_requests
            )
            results['original_service'].extend(round_results)
            
            # 重新激活CK
            await self._reactivate_cks(merchant_id)
            await asyncio.sleep(2)
        
        # 测试生产级服务
        print(f"\n🎯 测试生产级服务 ({concurrent_requests} 并发)")
        print("-"*60)
        
        for round_num in range(test_rounds):
            print(f"第 {round_num + 1} 轮...")
            round_results = await self._test_production_service(
                merchant_id, concurrent_requests
            )
            results['production_service'].extend(round_results)
            
            # 重新激活CK
            await self._reactivate_cks(merchant_id)
            await asyncio.sleep(2)
        
        # 综合分析
        await self._analyze_extreme_results(results, available_cks, concurrent_requests)
        
        return results
    
    async def _check_available_cks(self, merchant_id: int) -> List:
        """检查可用CK"""
        available_cks = self.db.query(WalmartCK).filter(
            WalmartCK.merchant_id == merchant_id,
            WalmartCK.active == True,
            WalmartCK.is_deleted == False
        ).all()
        
        print(f"📊 可用CK数量: {len(available_cks)}")
        for ck in available_cks:
            print(f"  CK {ck.id}: bind_count={ck.bind_count}, total_limit={ck.total_limit}")
        
        return available_cks
    
    async def _test_original_service(self, merchant_id: int, concurrent_count: int) -> List[Dict]:
        """测试原有服务"""
        
        async def single_request(request_id: int) -> Dict:
            start_time = time.time()
            
            try:
                ck_service = create_optimized_ck_service(self.db)
                ck = await ck_service.get_available_ck(merchant_id)
                selection_time = time.time() - start_time
                
                result = {
                    'request_id': request_id,
                    'ck_id': ck.id if ck else None,
                    'success': ck is not None,
                    'selection_time': selection_time,
                    'timestamp': time.time(),
                    'service_type': 'original'
                }
                
                if ck:
                    await ck_service.record_ck_usage(ck_id=ck.id, success=False)
                
                return result
                
            except Exception as e:
                return {
                    'request_id': request_id,
                    'ck_id': None,
                    'success': False,
                    'selection_time': time.time() - start_time,
                    'error': str(e),
                    'timestamp': time.time(),
                    'service_type': 'original'
                }
        
        # 执行并发请求
        tasks = [single_request(i) for i in range(concurrent_count)]
        start_time = time.time()
        results = await asyncio.gather(*tasks, return_exceptions=True)
        total_time = time.time() - start_time
        
        # 处理结果
        processed_results = []
        for result in results:
            if isinstance(result, Exception):
                processed_results.append({
                    'request_id': -1,
                    'ck_id': None,
                    'success': False,
                    'selection_time': 0,
                    'error': str(result),
                    'timestamp': time.time(),
                    'service_type': 'original'
                })
            else:
                processed_results.append(result)
        
        # 统计结果
        successful = [r for r in processed_results if r['success']]
        print(f"  成功: {len(successful)}/{concurrent_count} ({len(successful)/concurrent_count*100:.1f}%)")
        print(f"  总耗时: {total_time:.3f}秒")
        print(f"  QPS: {concurrent_count/total_time:.1f}")
        
        if successful:
            distribution = Counter(r['ck_id'] for r in successful)
            print(f"  CK分布: {dict(distribution)}")
        
        return processed_results
    
    async def _test_production_service(self, merchant_id: int, concurrent_count: int) -> List[Dict]:
        """测试生产级服务"""
        
        async def single_request(request_id: int) -> Dict:
            start_time = time.time()
            
            try:
                async with ProductionCKService(self.db) as ck_service:
                    ck = await ck_service.get_available_ck(merchant_id, f"extreme_req_{request_id}")
                    selection_time = time.time() - start_time
                    
                    result = {
                        'request_id': request_id,
                        'ck_id': ck.id if ck else None,
                        'success': ck is not None,
                        'selection_time': selection_time,
                        'timestamp': time.time(),
                        'service_type': 'production'
                    }
                    
                    if ck:
                        await ck_service.record_ck_usage(ck_id=ck.id, success=False)
                    
                    return result
                    
            except Exception as e:
                return {
                    'request_id': request_id,
                    'ck_id': None,
                    'success': False,
                    'selection_time': time.time() - start_time,
                    'error': str(e),
                    'timestamp': time.time(),
                    'service_type': 'production'
                }
        
        # 执行并发请求
        tasks = [single_request(i) for i in range(concurrent_count)]
        start_time = time.time()
        results = await asyncio.gather(*tasks, return_exceptions=True)
        total_time = time.time() - start_time
        
        # 处理结果
        processed_results = []
        for result in results:
            if isinstance(result, Exception):
                processed_results.append({
                    'request_id': -1,
                    'ck_id': None,
                    'success': False,
                    'selection_time': 0,
                    'error': str(result),
                    'timestamp': time.time(),
                    'service_type': 'production'
                })
            else:
                processed_results.append(result)
        
        # 统计结果
        successful = [r for r in processed_results if r['success']]
        print(f"  成功: {len(successful)}/{concurrent_count} ({len(successful)/concurrent_count*100:.1f}%)")
        print(f"  总耗时: {total_time:.3f}秒")
        print(f"  QPS: {concurrent_count/total_time:.1f}")
        
        if successful:
            distribution = Counter(r['ck_id'] for r in successful)
            print(f"  CK分布: {dict(distribution)}")
        
        return processed_results
    
    async def _analyze_extreme_results(self, results: Dict, available_cks: List, concurrent_count: int):
        """分析极限测试结果"""
        print(f"\n" + "="*80)
        print("📊 极限负载测试结果分析")
        print("="*80)
        
        original_results = [r for r in results['original_service'] if r['success']]
        production_results = [r for r in results['production_service'] if r['success']]
        
        # 基础对比
        print(f"🔥 {concurrent_count} 并发请求测试结果:")
        print(f"\n原有服务:")
        print(f"  总请求数: {len(results['original_service'])}")
        print(f"  成功请求数: {len(original_results)}")
        print(f"  成功率: {len(original_results)/len(results['original_service'])*100:.1f}%")
        
        print(f"\n生产级服务:")
        print(f"  总请求数: {len(results['production_service'])}")
        print(f"  成功请求数: {len(production_results)}")
        print(f"  成功率: {len(production_results)/len(results['production_service'])*100:.1f}%")
        
        # 负载均衡对比
        print(f"\n⚖️ 负载均衡对比:")
        
        if original_results:
            original_distribution = Counter(r['ck_id'] for r in original_results)
            original_unique = len(original_distribution)
            original_balance = self._calculate_balance_score(original_distribution)
            
            print(f"原有服务:")
            print(f"  使用的不同CK数: {original_unique}/{len(available_cks)}")
            print(f"  CK分布: {dict(original_distribution)}")
            print(f"  均衡分数: {original_balance:.1f}/100")
            
            # 检查是否所有请求使用同一CK
            if original_unique == 1:
                print(f"  🚨 严重问题：所有 {len(original_results)} 个请求都使用了同一个CK！")
                print(f"  🚨 生产环境风险：极高！")
        
        if production_results:
            production_distribution = Counter(r['ck_id'] for r in production_results)
            production_unique = len(production_distribution)
            production_balance = self._calculate_balance_score(production_distribution)
            
            print(f"\n生产级服务:")
            print(f"  使用的不同CK数: {production_unique}/{len(available_cks)}")
            print(f"  CK分布: {dict(production_distribution)}")
            print(f"  均衡分数: {production_balance:.1f}/100")
            
            if production_unique == len(available_cks):
                print(f"  ✅ 优秀：使用了所有可用CK")
            elif production_unique > 1:
                print(f"  ✅ 良好：负载分布在多个CK上")
            else:
                print(f"  ⚠️ 问题：只使用了一个CK")
        
        # 性能对比
        print(f"\n⏱️ 性能对比:")
        
        if original_results:
            original_times = [r['selection_time'] for r in original_results]
            original_avg = statistics.mean(original_times)
            original_p95 = statistics.quantiles(original_times, n=20)[18] if len(original_times) > 20 else max(original_times)
            
            print(f"原有服务:")
            print(f"  平均响应时间: {original_avg*1000:.1f}ms")
            print(f"  95%响应时间: {original_p95*1000:.1f}ms")
        
        if production_results:
            production_times = [r['selection_time'] for r in production_results]
            production_avg = statistics.mean(production_times)
            production_p95 = statistics.quantiles(production_times, n=20)[18] if len(production_times) > 20 else max(production_times)
            
            print(f"\n生产级服务:")
            print(f"  平均响应时间: {production_avg*1000:.1f}ms")
            print(f"  95%响应时间: {production_p95*1000:.1f}ms")
        
        # 改进幅度计算
        if original_results and production_results:
            success_improvement = (len(production_results) - len(original_results)) / len(original_results) * 100
            balance_improvement = production_balance - original_balance
            performance_improvement = (original_avg - production_avg) / original_avg * 100
            
            print(f"\n📈 改进幅度:")
            print(f"  成功率改进: {success_improvement:+.1f}%")
            print(f"  负载均衡改进: {balance_improvement:+.1f}分")
            print(f"  性能改进: {performance_improvement:+.1f}%")
        
        # 生产环境建议
        print(f"\n🎯 生产环境建议:")
        
        if production_results and len(production_results) > len(original_results):
            print(f"  ✅ 生产级服务在 {concurrent_count} 并发下表现更好")
        
        if production_results and production_balance > 80:
            print(f"  ✅ 生产级服务负载均衡优秀，可以承受高并发")
        elif production_balance > 60:
            print(f"  ✅ 生产级服务负载均衡良好，适合生产环境")
        else:
            print(f"  ⚠️ 负载均衡需要进一步优化")
        
        if original_results and original_unique == 1:
            print(f"  🚨 原有服务存在严重的单点风险，必须升级！")
        
        print(f"  💡 建议：在生产环境部署生产级服务以确保系统稳定性")
    
    def _calculate_balance_score(self, distribution: Counter) -> float:
        """计算负载均衡分数"""
        if len(distribution) <= 1:
            return 0.0 if len(distribution) == 1 else 100.0
        
        values = list(distribution.values())
        max_count = max(values)
        min_count = min(values)
        
        return (1 - (max_count - min_count) / max_count) * 100
    
    async def _cleanup_test_environment(self, merchant_id: int):
        """清理测试环境"""
        try:
            # 清理锁
            lock_pattern = f"walmart:ck:lock:*"
            async for key in self.redis_client.scan_iter(match=lock_pattern):
                await self.redis_client.delete(key)
            
            # 重置随机计数器
            random_key = f"walmart:ck:random_counter:{merchant_id}"
            await self.redis_client.delete(random_key)
            
            print("✅ 测试环境清理完成")
            
        except Exception as e:
            print(f"⚠️ 清理测试环境失败: {e}")
    
    async def _reactivate_cks(self, merchant_id: int):
        """重新激活被禁用的CK"""
        try:
            cks = self.db.query(WalmartCK).filter(
                WalmartCK.merchant_id == merchant_id,
                WalmartCK.is_deleted == False
            ).all()
            
            activated = 0
            for ck in cks:
                if not ck.active:
                    ck.active = True
                    activated += 1
            
            if activated > 0:
                self.db.commit()
                print(f"  重新激活了 {activated} 个CK")
            
        except Exception as e:
            self.db.rollback()
            print(f"⚠️ 重新激活CK失败: {e}")


async def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description="极限负载测试 - 200并发")
    parser.add_argument("--merchant-id", type=int, default=2, help="商户ID")
    parser.add_argument("--concurrent", type=int, default=200, help="并发请求数")
    parser.add_argument("--rounds", type=int, default=2, help="测试轮数")
    
    args = parser.parse_args()
    
    print(f"⚠️ 警告：即将进行 {args.concurrent} 个并发请求的极限测试")
    print(f"这可能会对系统造成较大压力，请确认测试环境")
    
    # 确认测试
    # confirm = input("是否继续？(y/N): ").strip().lower()
    # if confirm != 'y':
    #     print("测试已取消")
    #     return
    
    async with ExtremeLoadTester() as tester:
        await tester.run_extreme_load_test(
            merchant_id=args.merchant_id,
            concurrent_requests=args.concurrent,
            test_rounds=args.rounds
        )
    
    print(f"\n🎉 极限负载测试完成！")


if __name__ == "__main__":
    asyncio.run(main())
