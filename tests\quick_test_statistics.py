#!/usr/bin/env python3
"""
快速测试绑卡统计接口

用于验证新添加的绑卡统计接口是否正常工作
"""

import sys
import os
import requests
import json
from datetime import datetime

# 添加项目根目录到Python路径
ROOT_DIR = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, ROOT_DIR)

# 测试配置
BASE_URL = "http://localhost:20000"
API_BASE = f"{BASE_URL}/api/v1"

# 测试账号
ADMIN_CREDENTIALS = {
    "username": "admin",
    "password": "7c222fb2927d828af22f592134e8932480637c0d"
}

MERCHANT_CREDENTIALS = {
    "username": "test1",
    "password": "12345678"
}


def login(credentials):
    """登录获取token"""
    try:
        response = requests.post(
            f"{API_BASE}/auth/login",
            data=credentials,
            headers={"Content-Type": "application/x-www-form-urlencoded"}
        )

        if response.status_code == 200:
            data = response.json()
            # 处理不同的响应格式
            if data.get("code") == 0 and "data" in data:
                return data["data"]["access_token"]
            elif data.get("success") and "data" in data:
                return data["data"]["access_token"]
            else:
                print(f"登录失败: {data.get('message', '未知错误')}")
        else:
            print(f"登录请求失败: {response.status_code} - {response.text}")

    except Exception as e:
        print(f"登录异常: {e}")

    return None


def test_statistics_api(token, user_type="admin"):
    """测试统计接口"""
    headers = {"Authorization": f"Bearer {token}"}

    print(f"\n=== 测试 {user_type} 用户的统计接口 ===")

    # 测试1: 基础统计接口
    print("\n1. 测试基础统计接口 (/api/v1/cards/statistics)")
    try:
        response = requests.get(f"{API_BASE}/cards/statistics", headers=headers)
        print(f"状态码: {response.status_code}")

        if response.status_code == 200:
            data = response.json()
            print(f"响应成功: {data.get('success', False)}")
            if data.get("success"):
                stats = data.get("data", {})
                print(f"总记录数: {stats.get('total_count', 0)}")
                print(f"成功数: {stats.get('success_count', 0)}")
                print(f"失败数: {stats.get('failed_count', 0)}")
                print(f"成功率: {stats.get('success_rate', 0)}%")
                print(f"是否超管: {stats.get('is_superuser', False)}")
                print(f"用户商户ID: {stats.get('current_user_merchant_id', 'N/A')}")
            else:
                print(f"接口返回失败: {data.get('message', '未知错误')}")
        else:
            print(f"请求失败: {response.text}")

    except Exception as e:
        print(f"请求异常: {e}")

    # 测试2: 带分页的统计接口
    print("\n2. 测试带分页的统计接口")
    try:
        response = requests.get(
            f"{API_BASE}/cards/statistics?page=1&page_size=5",
            headers=headers
        )
        print(f"状态码: {response.status_code}")

        if response.status_code == 200:
            data = response.json()
            if data.get("success"):
                stats = data.get("data", {})
                print(f"分页信息: 第{stats.get('page', 0)}页，每页{stats.get('page_size', 0)}条")
                print(f"总页数: {stats.get('total_pages', 0)}")
                print(f"记录条数: {len(stats.get('items', []))}")
            else:
                print(f"接口返回失败: {data.get('message', '未知错误')}")
        else:
            print(f"请求失败: {response.text}")

    except Exception as e:
        print(f"请求异常: {e}")

    # 测试3: 带状态过滤的统计接口
    print("\n3. 测试带状态过滤的统计接口")
    try:
        response = requests.get(
            f"{API_BASE}/cards/statistics?status=success",
            headers=headers
        )
        print(f"状态码: {response.status_code}")

        if response.status_code == 200:
            data = response.json()
            if data.get("success"):
                stats = data.get("data", {})
                print(f"成功状态记录数: {stats.get('total_count', 0)}")
            else:
                print(f"接口返回失败: {data.get('message', '未知错误')}")
        else:
            print(f"请求失败: {response.text}")

    except Exception as e:
        print(f"请求异常: {e}")

    # 测试4: 向后兼容接口（仅超管测试）
    if user_type == "admin":
        print("\n4. 测试向后兼容接口 (/api/v1/cards/statistics/{merchant_id})")
        try:
            # 假设商户ID为1，实际应该从数据库获取
            response = requests.get(f"{API_BASE}/cards/statistics/1", headers=headers)
            print(f"状态码: {response.status_code}")

            if response.status_code == 200:
                data = response.json()
                if data.get("success"):
                    stats = data.get("data", {})
                    print(f"指定商户统计成功，商户ID: {stats.get('merchant_id', 'N/A')}")
                else:
                    print(f"接口返回失败: {data.get('message', '未知错误')}")
            else:
                print(f"请求失败: {response.text}")

        except Exception as e:
            print(f"请求异常: {e}")


def main():
    """主函数"""
    print("=== 绑卡统计接口快速测试 ===")
    print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"服务器地址: {BASE_URL}")

    # 测试超级管理员
    print("\n--- 测试超级管理员账号 ---")
    admin_token = login(ADMIN_CREDENTIALS)
    if admin_token:
        print("超级管理员登录成功")
        test_statistics_api(admin_token, "admin")
    else:
        print("超级管理员登录失败，跳过测试")

    # 测试商户账号
    print("\n--- 测试商户账号 ---")
    merchant_token = login(MERCHANT_CREDENTIALS)
    if merchant_token:
        print("商户账号登录成功")
        test_statistics_api(merchant_token, "merchant")
    else:
        print("商户账号登录失败，跳过测试")

    print("\n=== 测试完成 ===")


if __name__ == "__main__":
    main()
