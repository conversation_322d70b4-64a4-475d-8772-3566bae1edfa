# 密钥文件目录

此目录用于存储生产环境的敏感信息，如数据库密码、API 密钥等。

## 文件说明

### 必需文件

1. **mysql_root_password.txt** - MySQL root 用户密码
2. **mysql_password.txt** - MySQL 应用用户密码  
3. **redis_password.txt** - Redis 密码

### 创建密钥文件

```bash
# 生成强密码并保存到文件
echo "your_mysql_root_password" > mysql_root_password.txt
echo "your_mysql_user_password" > mysql_password.txt
echo "your_redis_password" > redis_password.txt

# 设置文件权限（仅所有者可读）
chmod 600 *.txt
```

### 密码要求

- 长度至少 16 个字符
- 包含大小写字母、数字和特殊字符
- 不包含空格或换行符
- 每个服务使用不同的密码

### 安全注意事项

1. **不要将密钥文件提交到版本控制系统**
2. **定期更换密码**
3. **使用强密码生成器**
4. **限制文件访问权限**
5. **备份密钥文件到安全位置**

### 示例密码生成

```bash
# 使用 openssl 生成随机密码
openssl rand -base64 32

# 使用 pwgen 生成密码（如果已安装）
pwgen -s 32 1

# 使用 Python 生成密码
python3 -c "import secrets, string; print(''.join(secrets.choice(string.ascii_letters + string.digits + '!@#$%^&*') for _ in range(32)))"
```

### 环境变量方式（可选）

如果不使用文件方式，也可以通过环境变量传递：

```bash
export MYSQL_ROOT_PASSWORD="your_mysql_root_password"
export MYSQL_PASSWORD="your_mysql_user_password"  
export REDIS_PASSWORD="your_redis_password"
```

然后在 docker-compose.yml 中使用：

```yaml
environment:
  MYSQL_ROOT_PASSWORD: ${MYSQL_ROOT_PASSWORD}
  MYSQL_PASSWORD: ${MYSQL_PASSWORD}
```
