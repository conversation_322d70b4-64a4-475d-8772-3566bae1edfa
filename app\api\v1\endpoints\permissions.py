"""
权限管理API端点
"""

from typing import Any, Optional, List, Dict
from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session

from app import crud, models, schemas
from app.api import deps
import logging

logger = logging.getLogger(__name__)

router = APIRouter()


@router.get("/", response_model=Dict[str, Any])
def get_permissions(
    db: Session = Depends(deps.get_db),
    page: int = Query(1, ge=1, description="页码，从1开始"),
    page_size: int = Query(20, ge=1, le=1000, description="每页记录数，最大1000"),
    name: Optional[str] = Query(None, description="权限名称搜索"),
    code: Optional[str] = Query(None, description="权限编码搜索"),
    resource_type: Optional[str] = Query(None, description="资源类型筛选"),
    is_enabled: Optional[bool] = Query(None, description="启用状态筛选"),
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """
    获取权限列表（支持搜索和筛选）
    """
    try:
        # 检查权限 - 需要权限管理查看权限
        from app.services.permission_service import PermissionService

        perm_service = PermissionService(db)
        has_permission = perm_service.check_user_permission(
            current_user, "permission:view"
        )
        if not has_permission:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN, detail="没有查看权限的权限"
            )

        # 构建查询条件
        query = db.query(models.Permission)

        if name:
            query = query.filter(models.Permission.name.contains(name))
        if code:
            query = query.filter(models.Permission.code.contains(code))
        if resource_type:
            # 对传入的资源类型进行大写转换
            query = query.filter(
                models.Permission.resource_type == resource_type.upper()
            )
        if is_enabled is not None:
            query = query.filter(models.Permission.is_enabled == is_enabled)

        # 获取总数
        total = query.count()

        # 分页查询
        skip = (page - 1) * page_size
        permissions = (
            query.order_by(models.Permission.sort_order, models.Permission.id)
            .offset(skip)
            .limit(page_size)
            .all()
        )

        # 数据转换 - 将小写的资源类型转换为大写，日期转为字符串
        processed_permissions = []
        for p in permissions:
            # 创建权限对象的副本
            permission_dict = {
                "id": p.id,
                "name": p.name,
                "code": p.code,
                "description": p.description,
                # 将资源类型转换为大写
                "resource_type": p.resource_type.upper() if p.resource_type else None,
                "resource_path": p.resource_path,
                "is_enabled": p.is_enabled,
                "sort_order": p.sort_order,
                # 将日期格式化为ISO字符串
                "created_at": p.created_at.isoformat() if p.created_at else None,
                "updated_at": p.updated_at.isoformat() if p.updated_at else None,
            }
            processed_permissions.append(permission_dict)

        # 返回统一的分页格式
        return {
            "items": processed_permissions,
            "total": total,
            "page": page,
            "page_size": page_size,
            "pages": (total + page_size - 1) // page_size,
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取权限列表失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="获取权限列表失败"
        )


@router.post("/", response_model=schemas.PermissionInDB)
def create_permission(
    *,
    db: Session = Depends(deps.get_db),
    permission_in: schemas.PermissionCreate,
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """
    创建权限
    """
    try:
        # 检查权限 - 需要权限管理创建权限
        from app.services.permission_service import PermissionService

        perm_service = PermissionService(db)
        has_permission = perm_service.check_user_permission(
            current_user, "permission:create"
        )
        if not has_permission:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN, detail="没有创建权限的权限"
            )

        # 检查权限代码是否已存在
        existing = crud.permission.get_by_code(db, code=permission_in.code)
        if existing:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST, detail="权限代码已存在"
            )

        permission = crud.permission.create(db=db, obj_in=permission_in)
        logger.info(f"用户 {current_user.username} 创建权限: {permission.code}")
        return permission
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"创建权限失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="创建权限失败"
        )


@router.get("/{permission_id}", response_model=schemas.PermissionInDB)
def get_permission(
    *,
    db: Session = Depends(deps.get_db),
    permission_id: int,
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """
    获取权限详情
    """
    try:
        # 检查权限 - 需要权限管理查看权限详情
        from app.services.permission_service import PermissionService

        perm_service = PermissionService(db)
        has_permission = perm_service.check_user_permission(
            current_user, "permission:view"
        )
        if not has_permission:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN, detail="没有查看权限的权限"
            )

        permission = crud.permission.get(db=db, id=permission_id)
        if not permission:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND, detail="权限不存在"
            )

        # 将小写的资源类型转换为大写，日期转为字符串
        permission_dict = {
            "id": permission.id,
            "name": permission.name,
            "code": permission.code,
            "description": permission.description,
            # 将资源类型转换为大写
            "resource_type": (
                permission.resource_type.upper() if permission.resource_type else None
            ),
            "resource_path": permission.resource_path,
            "is_enabled": permission.is_enabled,
            "sort_order": permission.sort_order,
            # 将日期格式化为ISO字符串
            "created_at": (
                permission.created_at.isoformat() if permission.created_at else None
            ),
            "updated_at": (
                permission.updated_at.isoformat() if permission.updated_at else None
            ),
        }

        return permission_dict
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取权限详情失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="获取权限详情失败"
        )


@router.put("/{permission_id}", response_model=schemas.PermissionInDB)
def update_permission(
    *,
    db: Session = Depends(deps.get_db),
    permission_id: int,
    permission_in: schemas.PermissionUpdate,
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """
    更新权限
    """
    try:
        # 检查权限 - 需要权限管理编辑权限
        from app.services.permission_service import PermissionService

        perm_service = PermissionService(db)
        has_permission = perm_service.check_user_permission(
            current_user, "permission:edit"
        )
        if not has_permission:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN, detail="没有编辑权限的权限"
            )

        permission = crud.permission.get(db=db, id=permission_id)
        if not permission:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND, detail="权限不存在"
            )

        permission = crud.permission.update(
            db=db, db_obj=permission, obj_in=permission_in
        )
        logger.info(f"用户 {current_user.username} 更新权限: {permission.code}")
        return permission
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"更新权限失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="更新权限失败"
        )


@router.delete("/{permission_id}")
def delete_permission(
    *,
    db: Session = Depends(deps.get_db),
    permission_id: int,
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """
    删除权限
    """
    try:
        # 检查权限 - 需要权限管理删除权限
        from app.services.permission_service import PermissionService

        perm_service = PermissionService(db)
        has_permission = perm_service.check_user_permission(
            current_user, "permission:delete"
        )
        if not has_permission:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN, detail="没有删除权限的权限"
            )

        permission = crud.permission.get(db=db, id=permission_id)
        if not permission:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND, detail="权限不存在"
            )

        # 检查是否为系统内置权限
        if hasattr(permission, "is_system") and permission.is_system:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST, detail="不能删除系统内置权限"
            )

        crud.permission.remove(db=db, id=permission_id)
        logger.info(f"用户 {current_user.username} 删除权限: {permission.code}")
        return {"message": "权限删除成功"}
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"删除权限失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="删除权限失败"
        )


@router.post("/batch", response_model=dict)
def batch_create_permissions(
    *,
    db: Session = Depends(deps.get_db),
    permissions_in: List[schemas.PermissionCreate],
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """
    批量创建权限
    """
    try:
        # 检查权限 - 需要权限管理创建权限
        from app.services.permission_service import PermissionService

        perm_service = PermissionService(db)
        has_permission = perm_service.check_user_permission(
            current_user, "permission:create"
        )
        if not has_permission:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN, detail="没有创建权限的权限"
            )

        created_permissions = []
        failed_permissions = []

        for permission_in in permissions_in:
            try:
                # 检查权限代码是否已存在
                existing = crud.permission.get_by_code(db, code=permission_in.code)
                if existing:
                    failed_permissions.append(
                        {"code": permission_in.code, "error": "权限代码已存在"}
                    )
                    continue

                permission = crud.permission.create(db=db, obj_in=permission_in)
                created_permissions.append(permission.code)
            except Exception as e:
                failed_permissions.append({"code": permission_in.code, "error": str(e)})

        logger.info(
            f"用户 {current_user.username} 批量创建权限: 成功 {len(created_permissions)}, 失败 {len(failed_permissions)}"
        )
        return {
            "message": f"批量创建完成: 成功 {len(created_permissions)}, 失败 {len(failed_permissions)}",
            "created": created_permissions,
            "failed": failed_permissions,
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"批量创建权限失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="批量创建权限失败"
        )


@router.delete("/batch", response_model=dict)
def batch_delete_permissions(
    *,
    db: Session = Depends(deps.get_db),
    permission_ids: List[int],
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """
    批量删除权限
    """
    try:
        # 检查权限 - 需要权限管理删除权限
        from app.services.permission_service import PermissionService

        perm_service = PermissionService(db)
        has_permission = perm_service.check_user_permission(
            current_user, "permission:delete"
        )
        if not has_permission:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN, detail="没有删除权限的权限"
            )

        deleted_permissions = []
        failed_permissions = []

        for permission_id in permission_ids:
            try:
                permission = crud.permission.get(db=db, id=permission_id)
                if not permission:
                    failed_permissions.append(
                        {"id": permission_id, "error": "权限不存在"}
                    )
                    continue

                # 检查是否为系统内置权限
                if hasattr(permission, "is_system") and permission.is_system:
                    failed_permissions.append(
                        {"id": permission_id, "error": "不能删除系统内置权限"}
                    )
                    continue

                crud.permission.remove(db=db, id=permission_id)
                deleted_permissions.append(permission.code)
            except Exception as e:
                failed_permissions.append({"id": permission_id, "error": str(e)})

        logger.info(
            f"用户 {current_user.username} 批量删除权限: 成功 {len(deleted_permissions)}, 失败 {len(failed_permissions)}"
        )
        return {
            "message": f"批量删除完成: 成功 {len(deleted_permissions)}, 失败 {len(failed_permissions)}",
            "deleted": deleted_permissions,
            "failed": failed_permissions,
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"批量删除权限失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="批量删除权限失败"
        )
