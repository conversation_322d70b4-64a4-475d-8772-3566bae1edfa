"""
CK自动过期管理服务

功能说明：
1. 检测过期的CK并自动禁用（不删除记录）
2. 支持动态配置过期时间
3. 遵循现有的数据隔离机制
4. 记录过期处理日志
5. 过期的CK只被禁用，保留记录用于统计和审计
"""

from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional
from sqlalchemy.orm import Session
from sqlalchemy import and_

from app.core.logging import get_logger
from app.models.walmart_ck import WalmartCK
from app.crud.system_settings import system_settings
from app.services.base_service import BaseService
from app.schemas.walmart_ck import WalmartCKCreate, WalmartCKUpdate

logger = get_logger("ck_expire_service")


class CKExpireService(BaseService[WalmartCK, WalmartCKCreate, WalmartCKUpdate]):
    """CK自动过期管理服务"""

    def __init__(self, db: Session):
        super().__init__(WalmartCK, db)

    def get_expire_config(self) -> Dict[str, Any]:
        """
        获取CK过期配置
        
        Returns:
            Dict[str, Any]: 过期配置信息
        """
        try:
            # 获取过期时间（分钟）
            expire_minutes_str = system_settings.get_value(
                self.db, 'ck_expire_minutes', '30'
            )
            expire_minutes = int(expire_minutes_str)
            
            # 获取是否启用过期检测
            check_enabled_str = system_settings.get_value(
                self.db, 'ck_expire_check_enabled', 'true'
            )
            check_enabled = check_enabled_str.lower() == 'true'
            
            # 获取检测间隔（分钟）
            check_interval_str = system_settings.get_value(
                self.db, 'ck_expire_check_interval', '5'
            )
            check_interval = int(check_interval_str)
            
            return {
                'expire_minutes': expire_minutes,
                'check_enabled': check_enabled,
                'check_interval': check_interval
            }
        except Exception as e:
            logger.error(f"获取CK过期配置失败: {e}")
            # 返回默认配置
            return {
                'expire_minutes': 30,
                'check_enabled': True,
                'check_interval': 5
            }

    def find_expired_cks(self, merchant_id: Optional[int] = None) -> List[WalmartCK]:
        """
        查找过期的CK
        
        Args:
            merchant_id: 可选的商户ID，如果提供则只检查该商户的CK
            
        Returns:
            List[WalmartCK]: 过期的CK列表
        """
        try:
            config = self.get_expire_config()
            
            # 如果未启用过期检测，返回空列表
            if not config['check_enabled']:
                logger.info("CK过期检测已禁用，跳过检测")
                return []
            
            # 计算过期时间点 - 修复时区问题
            expire_minutes = config['expire_minutes']
            from app.utils.time_utils import get_current_time
            expire_threshold = get_current_time() - timedelta(minutes=expire_minutes)
            
            # 构建查询条件
            query = self.db.query(WalmartCK).filter(
                and_(
                    WalmartCK.created_at < expire_threshold,  # 创建时间早于过期阈值
                    WalmartCK.is_deleted == False,  # 未被软删除
                    WalmartCK.active == True  # 仍处于活跃状态
                )
            )
            
            # 如果指定了商户ID，添加商户过滤条件
            if merchant_id:
                query = query.filter(WalmartCK.merchant_id == merchant_id)
            
            expired_cks = query.all()
            
            logger.info(
                f"找到 {len(expired_cks)} 个过期CK | "
                f"过期阈值: {expire_threshold} | "
                f"过期时间: {expire_minutes}分钟 | "
                f"商户ID: {merchant_id or '全部'}"
            )
            
            return expired_cks
            
        except Exception as e:
            logger.error(f"查找过期CK失败: {e}")
            return []

    def expire_ck(self, ck: WalmartCK, reason: str = "自动过期") -> bool:
        """
        将CK标记为过期（禁用但不删除）

        Args:
            ck: CK对象
            reason: 过期原因

        Returns:
            bool: 是否处理成功
        """
        try:
            # 只禁用CK，不进行软删除
            ck.active = False

            self.db.commit()

            logger.info(
                f"CK {ck.id} 已过期禁用 | "
                f"原因: {reason} | "
                f"创建时间: {ck.created_at} | "
                f"商户ID: {ck.merchant_id} | "
                f"部门ID: {ck.department_id} | "
                f"绑卡次数: {ck.bind_count} | "
                f"状态: 禁用但保留记录"
            )

            return True

        except Exception as e:
            self.db.rollback()
            logger.error(f"处理CK {ck.id} 过期失败: {e}")
            return False

    def batch_expire_cks(self, merchant_id: Optional[int] = None) -> Dict[str, Any]:
        """
        批量处理过期的CK
        
        Args:
            merchant_id: 可选的商户ID，如果提供则只处理该商户的CK
            
        Returns:
            Dict[str, Any]: 处理结果统计
        """
        try:
            config = self.get_expire_config()
            
            # 如果未启用过期检测，直接返回
            if not config['check_enabled']:
                return {
                    'total_found': 0,
                    'total_processed': 0,
                    'total_failed': 0,
                    'config': config,
                    'message': 'CK过期检测已禁用'
                }
            
            # 查找过期的CK
            expired_cks = self.find_expired_cks(merchant_id)
            
            if not expired_cks:
                return {
                    'total_found': 0,
                    'total_processed': 0,
                    'total_failed': 0,
                    'config': config,
                    'message': '未找到过期的CK'
                }
            
            # 批量处理过期CK
            processed_count = 0
            failed_count = 0
            processed_details = []
            
            for ck in expired_cks:
                if self.expire_ck(ck, "自动过期检测"):
                    processed_count += 1
                    processed_details.append({
                        'ck_id': ck.id,
                        'merchant_id': ck.merchant_id,
                        'department_id': ck.department_id,
                        'created_at': ck.created_at.isoformat(),
                        'bind_count': ck.bind_count
                    })
                else:
                    failed_count += 1
            
            result = {
                'total_found': len(expired_cks),
                'total_processed': processed_count,
                'total_failed': failed_count,
                'config': config,
                'processed_details': processed_details[:10],  # 只返回前10条详情
                'message': f'成功处理 {processed_count} 个过期CK，失败 {failed_count} 个'
            }
            
            logger.info(
                f"批量CK过期处理完成 | "
                f"找到: {len(expired_cks)} | "
                f"成功: {processed_count} | "
                f"失败: {failed_count} | "
                f"商户ID: {merchant_id or '全部'}"
            )
            
            return result
            
        except Exception as e:
            logger.error(f"批量处理过期CK失败: {e}")
            return {
                'total_found': 0,
                'total_processed': 0,
                'total_failed': 0,
                'config': self.get_expire_config(),
                'error': str(e),
                'message': f'批量处理过期CK异常: {str(e)}'
            }

    def get_expire_statistics(self, merchant_id: Optional[int] = None) -> Dict[str, Any]:
        """
        获取CK过期统计信息
        
        Args:
            merchant_id: 可选的商户ID
            
        Returns:
            Dict[str, Any]: 过期统计信息
        """
        try:
            config = self.get_expire_config()
            
            # 计算过期时间点 - 修复时区问题
            expire_minutes = config['expire_minutes']
            from app.utils.time_utils import get_current_time
            expire_threshold = get_current_time() - timedelta(minutes=expire_minutes)
            
            # 基础查询条件
            base_query = self.db.query(WalmartCK)
            if merchant_id:
                base_query = base_query.filter(WalmartCK.merchant_id == merchant_id)
            
            # 统计各种状态的CK数量
            total_count = base_query.count()

            # 活跃的CK（启用且未删除）
            active_count = base_query.filter(
                and_(WalmartCK.active == True, WalmartCK.is_deleted == False)
            ).count()

            # 待过期的CK（创建时间早于过期阈值但仍活跃且未删除）
            pending_expire_count = base_query.filter(
                and_(
                    WalmartCK.created_at < expire_threshold,
                    WalmartCK.is_deleted == False,
                    WalmartCK.active == True
                )
            ).count()

            # 已过期禁用的CK（创建时间早于过期阈值且已禁用但未删除）
            expired_disabled_count = base_query.filter(
                and_(
                    WalmartCK.created_at < expire_threshold,
                    WalmartCK.is_deleted == False,
                    WalmartCK.active == False
                )
            ).count()

            # 软删除的CK
            deleted_count = base_query.filter(WalmartCK.is_deleted == True).count()

            # 其他禁用的CK（未过期但被禁用的）
            other_disabled_count = base_query.filter(
                and_(
                    WalmartCK.created_at >= expire_threshold,
                    WalmartCK.is_deleted == False,
                    WalmartCK.active == False
                )
            ).count()

            return {
                'config': config,
                'expire_threshold': expire_threshold.isoformat(),
                'statistics': {
                    'total_count': total_count,
                    'active_count': active_count,
                    'pending_expire_count': pending_expire_count,
                    'expired_disabled_count': expired_disabled_count,
                    'other_disabled_count': other_disabled_count,
                    'deleted_count': deleted_count,
                    'total_disabled_count': expired_disabled_count + other_disabled_count
                },
                'merchant_id': merchant_id
            }
            
        except Exception as e:
            logger.error(f"获取CK过期统计失败: {e}")
            return {
                'config': self.get_expire_config(),
                'error': str(e),
                'statistics': {
                    'total_count': 0,
                    'active_count': 0,
                    'expired_count': 0,
                    'deleted_count': 0,
                    'inactive_count': 0
                }
            }


def create_ck_expire_service(db: Session) -> CKExpireService:
    """创建CK过期服务实例"""
    return CKExpireService(db)
