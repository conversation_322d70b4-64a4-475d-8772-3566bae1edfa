#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
新用户菜单权限测试
专门测试新创建用户的菜单权限获取功能
"""

import sys
import os
import time
import uuid

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.dirname(__file__))))

from test.conftest import TestBase, get_test_accounts, format_test_result, print_test_summary

class NewUserMenuAccessTestSuite(TestBase):
    """新用户菜单权限测试套件"""

    def __init__(self):
        super().__init__()
        self.results = []
        self.test_accounts = get_test_accounts()
        self.admin_token = None
        self.test_user_token = None
        self.test_user_id = None
        self.test_role_id = None

    def setup(self):
        """测试前置设置"""
        print("=== 测试前置设置 ===")

        # 管理员登录
        self.admin_token = self.login("admin", "7c222fb2927d828af22f592134e8932480637c0d")
        if not self.admin_token:
            print("❌ 管理员登录失败")
            return False

        print("✅ 测试前置设置完成")
        return True

    def test_create_user_with_merchant_admin_role(self):
        """测试创建用户并分配商户管理员角色"""
        print("\n=== 测试创建用户并分配商户管理员角色 ===")
        
        try:
            # 1. 获取商户管理员角色ID
            print("步骤1: 获取商户管理员角色ID")
            status_code, response = self.make_request("GET", "/roles", self.admin_token)
            
            if status_code != 200:
                self.results.append(format_test_result(
                    "获取角色列表",
                    False,
                    f"获取角色列表失败，状态码: {status_code}"
                ))
                return False
            
            roles_data = response.get("data", {})
            roles = roles_data.get("items", []) if isinstance(roles_data, dict) else response.get("items", [])
            
            merchant_admin_role = None
            for role in roles:
                if role.get("code") == "merchant_admin":
                    merchant_admin_role = role
                    break
            
            if not merchant_admin_role:
                self.results.append(format_test_result(
                    "查找商户管理员角色",
                    False,
                    "未找到商户管理员角色"
                ))
                return False
            
            merchant_admin_role_id = merchant_admin_role["id"]
            print(f"✅ 找到商户管理员角色，ID: {merchant_admin_role_id}")
            
            # 2. 创建测试用户
            print("步骤2: 创建测试用户")
            
            # 生成唯一的用户名
            unique_suffix = str(uuid.uuid4())[:8]
            test_username = f"test_menu_user_{unique_suffix}"
            
            user_data = {
                "username": test_username,
                "password": "test123456",
                "email": f"{test_username}@example.com",
                "full_name": "测试菜单权限用户",
                "is_active": True,
                "role_ids": [merchant_admin_role_id]
            }
            
            status_code, response = self.make_request("POST", "/users", self.admin_token, user_data)

            # 处理嵌套的响应格式
            if status_code == 200:
                if response.get("code") == 0 and "data" in response:
                    self.test_user_id = response["data"]["id"]
                    print(f"✅ 测试用户创建成功，ID: {self.test_user_id}, 用户名: {test_username}")
                elif response.get("success") and "data" in response:
                    self.test_user_id = response["data"]["id"]
                    print(f"✅ 测试用户创建成功，ID: {self.test_user_id}, 用户名: {test_username}")
                else:
                    self.results.append(format_test_result(
                        "创建测试用户",
                        False,
                        f"创建测试用户失败: {response}"
                    ))
                    return False
            else:
                self.results.append(format_test_result(
                    "创建测试用户",
                    False,
                    f"创建测试用户失败，状态码: {status_code}, 响应: {response}"
                ))
                return False
            
            # 3. 测试用户登录
            print("步骤3: 测试用户登录")
            
            self.test_user_token = self.login(test_username, "test123456")
            
            if self.test_user_token:
                print("✅ 测试用户登录成功")
            else:
                self.results.append(format_test_result(
                    "测试用户登录",
                    False,
                    "测试用户登录失败"
                ))
                return False
            
            # 4. 测试获取用户菜单
            print("步骤4: 测试获取用户菜单")
            
            status_code, response = self.make_request("GET", "/menus/user-menus", self.test_user_token)
            
            print(f"菜单请求状态码: {status_code}")
            print(f"菜单响应内容: {response}")
            
            if status_code == 200:
                menus = response.get("data", response.get("menus", []))
                self.results.append(format_test_result(
                    "新用户获取菜单权限",
                    True,
                    f"新用户成功获取菜单权限，菜单数量: {len(menus)}",
                    {"menu_count": len(menus), "menus": menus}
                ))
                print(f"✅ 新用户成功获取菜单权限，菜单数量: {len(menus)}")
                
                # 验证菜单内容
                menu_codes = []
                def extract_menu_codes(menu_list):
                    for menu in menu_list:
                        if isinstance(menu, dict):
                            menu_codes.append(menu.get("code", ""))
                            if "children" in menu and isinstance(menu["children"], list):
                                extract_menu_codes(menu["children"])
                
                extract_menu_codes(menus)
                print(f"获取到的菜单代码: {menu_codes}")
                
                # 检查是否包含商户管理员应该有的菜单
                expected_menus = ["dashboard", "merchant:department", "cards"]
                found_menus = [menu for menu in expected_menus if menu in menu_codes]
                
                if len(found_menus) >= 2:
                    self.results.append(format_test_result(
                        "新用户菜单内容验证",
                        True,
                        f"新用户正确获得商户管理员菜单: {found_menus}"
                    ))
                    print(f"✅ 新用户正确获得商户管理员菜单: {found_menus}")
                else:
                    self.results.append(format_test_result(
                        "新用户菜单内容验证",
                        False,
                        f"新用户菜单内容不完整，期望: {expected_menus}, 实际: {found_menus}"
                    ))
                    print(f"⚠️ 新用户菜单内容不完整，期望: {expected_menus}, 实际: {found_menus}")
                
                return True
                
            elif status_code == 403:
                self.results.append(format_test_result(
                    "新用户获取菜单权限",
                    False,
                    f"新用户菜单权限被拒绝: {response}"
                ))
                print("❌ 新用户菜单权限被拒绝 - 权限配置有问题！")
                return False
            else:
                self.results.append(format_test_result(
                    "新用户获取菜单权限",
                    False,
                    f"获取用户菜单失败，状态码: {status_code}, 响应: {response}"
                ))
                print(f"❌ 获取用户菜单失败，状态码: {status_code}")
                return False
                
        except Exception as e:
            self.results.append(format_test_result(
                "新用户菜单权限测试",
                False,
                f"测试过程中发生异常: {str(e)}"
            ))
            print(f"❌ 测试过程中发生异常: {e}")
            return False

    def cleanup(self):
        """清理测试数据"""
        print("\n=== 清理测试数据 ===")
        
        if self.test_user_id:
            status_code, response = self.make_request("DELETE", f"/users/{self.test_user_id}", self.admin_token)
            if status_code == 200:
                print("✅ 测试用户删除成功")
            else:
                print(f"❌ 删除测试用户失败: {response}")

    def run_all_tests(self):
        """运行所有测试"""
        print("🚀 开始新用户菜单权限测试")
        print("="*60)

        start_time = time.time()

        try:
            # 前置设置
            if not self.setup():
                print("❌ 测试前置设置失败，跳过测试")
                return []

            # 运行测试
            self.test_create_user_with_merchant_admin_role()

        finally:
            # 清理测试数据
            self.cleanup()

        end_time = time.time()
        duration = end_time - start_time

        # 打印测试摘要
        print_test_summary(self.results)
        print(f"测试耗时: {duration:.2f}秒")

        return self.results

def main():
    """主函数"""
    test_suite = NewUserMenuAccessTestSuite()
    results = test_suite.run_all_tests()

    # 判断测试是否通过
    failed_tests = [r for r in results if not r.get("success", False)]

    if not failed_tests:
        print("\n🎉 所有新用户菜单权限测试通过！")
        print("✅ 新用户创建和菜单权限获取功能正常工作")
        return 0
    else:
        print(f"\n⚠️ 发现 {len(failed_tests)} 个问题需要修复")
        for test in failed_tests:
            print(f"  - {test['test_name']}: {test['message']}")
        return 1

if __name__ == "__main__":
    sys.exit(main())
