"""
角色服务模块 - 提供角色管理的核心功能
"""

from typing import List, Optional, Dict, Any
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_
import logging

from app.services.base_service import BaseService
from app.models.role import Role
from app.models.user import User
from app.models.permission import Permission
from app.models.menu import Menu
from app.schemas.role import RoleCreate, RoleUpdate

logger = logging.getLogger(__name__)


class RoleService(BaseService[Role, RoleCreate, RoleUpdate]):
    """角色服务类"""

    def __init__(self, db: Session):
        super().__init__(Role, db)

    def get_by_code(self, code: str) -> Optional[Role]:
        """
        根据角色代码获取角色
        
        Args:
            code: 角色代码
            
        Returns:
            Optional[Role]: 角色对象或None
        """
        try:
            return self.db.query(Role).filter(Role.code == code).first()
        except Exception as e:
            self.logger.error(f"根据代码获取角色失败: {e}")
            return None

    def create_role(
        self,
        role_in: RoleCreate,
        current_user: User
    ) -> Optional[Role]:
        """
        创建角色

        Args:
            role_in: 角色创建数据
            current_user: 当前操作用户

        Returns:
            Optional[Role]: 创建的角色对象或None
        """
        try:
            # 只有超级管理员可以创建角色
            if not current_user.is_superuser:
                raise ValueError("只有超级管理员可以创建角色")

            # 检查角色代码是否已存在
            if self.get_by_code(role_in.code):
                raise ValueError(f"角色代码 {role_in.code} 已存在")

            # 准备角色数据，排除权限和菜单ID
            role_data = role_in.dict(exclude={'permission_ids', 'menu_ids'})
            role_data['created_by'] = current_user.id

            # 创建角色
            db_role = Role(**role_data)
            self.db.add(db_role)
            self.db.commit()
            self.db.refresh(db_role)

            # 分配权限（如果提供了权限ID列表）
            if role_in.permission_ids:
                self.batch_assign_permissions(db_role.id, role_in.permission_ids, current_user)

            # 分配菜单（如果提供了菜单ID列表）
            if role_in.menu_ids:
                self.batch_assign_menus(db_role.id, role_in.menu_ids, current_user)

            return db_role
        except Exception as e:
            self.db.rollback()
            self.logger.error(f"创建角色失败: {e}")
            raise

    def update_role(
        self,
        role_id: int,
        role_in: RoleUpdate,
        current_user: User
    ) -> Optional[Role]:
        """
        更新角色信息

        Args:
            role_id: 角色ID
            role_in: 更新数据
            current_user: 当前操作用户

        Returns:
            Optional[Role]: 更新后的角色对象或None
        """
        try:
            self._validate_update_permission(current_user)
            role = self._get_role_for_update(role_id)
            self._validate_role_update_constraints(role, role_in)
            self._apply_role_updates(role, role_in)

            self.db.commit()
            self.db.refresh(role)
            return role
        except Exception as e:
            self.db.rollback()
            self.logger.error(f"更新角色失败: {e}")
            raise

    def _validate_update_permission(self, current_user: User):
        """验证更新权限"""
        if not current_user.is_superuser:
            raise ValueError("只有超级管理员可以更新角色")

    def _get_role_for_update(self, role_id: int) -> Role:
        """获取要更新的角色"""
        role = self.get(role_id)
        if not role:
            raise ValueError("角色不存在")
        return role

    def _validate_role_update_constraints(self, role: Role, role_in: RoleUpdate):
        """验证角色更新约束"""
        self._check_system_role_constraints(role, role_in)
        self._check_code_uniqueness_constraint(role, role_in)

    def _check_system_role_constraints(self, role: Role, role_in: RoleUpdate):
        """检查系统角色约束"""
        if role.is_system and role_in.code and role_in.code != role.code:
            raise ValueError("不能修改系统内置角色的代码")

    def _check_code_uniqueness_constraint(self, role: Role, role_in: RoleUpdate):
        """检查代码唯一性约束"""
        if role_in.code and role_in.code != role.code:
            if self.get_by_code(role_in.code):
                raise ValueError(f"角色代码 {role_in.code} 已存在")

    def _apply_role_updates(self, role: Role, role_in: RoleUpdate):
        """应用角色更新"""
        update_data = role_in.dict(exclude_unset=True)
        for field, value in update_data.items():
            if hasattr(role, field):
                setattr(role, field, value)

    def delete_role(self, role_id: int, current_user: User) -> bool:
        """
        删除角色
        
        Args:
            role_id: 角色ID
            current_user: 当前操作用户
            
        Returns:
            bool: 是否删除成功
        """
        try:
            # 只有超级管理员可以删除角色
            if not current_user.is_superuser:
                raise ValueError("只有超级管理员可以删除角色")
            
            role = self.get(role_id)
            if not role:
                return False
            
            # 不能删除系统内置角色
            if role.is_system:
                raise ValueError("不能删除系统内置角色")
            
            # 检查是否有用户使用该角色
            user_count = self.db.query(User).filter(User.roles.any(id=role_id)).count()
            if user_count > 0:
                raise ValueError(f"还有 {user_count} 个用户使用该角色，无法删除")
            
            self.db.delete(role)
            self.db.commit()
            return True
        except Exception as e:
            self.db.rollback()
            self.logger.error(f"删除角色失败: {e}")
            raise

    def assign_permission(
        self,
        role_id: int,
        permission_id: int,
        current_user: User
    ) -> bool:
        """
        为角色分配权限
        
        Args:
            role_id: 角色ID
            permission_id: 权限ID
            current_user: 当前操作用户
            
        Returns:
            bool: 是否分配成功
        """
        try:
            # 只有超级管理员可以分配权限
            if not current_user.is_superuser:
                raise ValueError("只有超级管理员可以分配权限")
            
            role = self.get(role_id)
            if not role:
                raise ValueError("角色不存在")
            
            permission = self.db.query(Permission).filter(Permission.id == permission_id).first()
            if not permission:
                raise ValueError("权限不存在")
            
            # 检查是否已经分配了该权限
            if permission not in role.permissions:
                role.permissions.append(permission)
                self.db.commit()
            
            return True
        except Exception as e:
            self.db.rollback()
            self.logger.error(f"分配权限失败: {e}")
            raise

    def remove_permission(
        self,
        role_id: int,
        permission_id: int,
        current_user: User
    ) -> bool:
        """
        移除角色权限
        
        Args:
            role_id: 角色ID
            permission_id: 权限ID
            current_user: 当前操作用户
            
        Returns:
            bool: 是否移除成功
        """
        try:
            # 只有超级管理员可以移除权限
            if not current_user.is_superuser:
                raise ValueError("只有超级管理员可以移除权限")
            
            role = self.get(role_id)
            if not role:
                raise ValueError("角色不存在")
            
            permission = self.db.query(Permission).filter(Permission.id == permission_id).first()
            if not permission:
                raise ValueError("权限不存在")
            
            # 移除权限
            if permission in role.permissions:
                role.permissions.remove(permission)
                self.db.commit()
            
            return True
        except Exception as e:
            self.db.rollback()
            self.logger.error(f"移除权限失败: {e}")
            raise

    def assign_menu(
        self,
        role_id: int,
        menu_id: int,
        current_user: User
    ) -> bool:
        """
        为角色分配菜单
        
        Args:
            role_id: 角色ID
            menu_id: 菜单ID
            current_user: 当前操作用户
            
        Returns:
            bool: 是否分配成功
        """
        try:
            # 只有超级管理员可以分配菜单
            if not current_user.is_superuser:
                raise ValueError("只有超级管理员可以分配菜单")
            
            role = self.get(role_id)
            if not role:
                raise ValueError("角色不存在")
            
            menu = self.db.query(Menu).filter(Menu.id == menu_id).first()
            if not menu:
                raise ValueError("菜单不存在")
            
            # 检查是否已经分配了该菜单
            if menu not in role.menus:
                role.menus.append(menu)
                self.db.commit()
            
            return True
        except Exception as e:
            self.db.rollback()
            self.logger.error(f"分配菜单失败: {e}")
            raise

    def remove_menu(
        self,
        role_id: int,
        menu_id: int,
        current_user: User
    ) -> bool:
        """
        移除角色菜单
        
        Args:
            role_id: 角色ID
            menu_id: 菜单ID
            current_user: 当前操作用户
            
        Returns:
            bool: 是否移除成功
        """
        try:
            # 只有超级管理员可以移除菜单
            if not current_user.is_superuser:
                raise ValueError("只有超级管理员可以移除菜单")
            
            role = self.get(role_id)
            if not role:
                raise ValueError("角色不存在")
            
            menu = self.db.query(Menu).filter(Menu.id == menu_id).first()
            if not menu:
                raise ValueError("菜单不存在")
            
            # 移除菜单
            if menu in role.menus:
                role.menus.remove(menu)
                self.db.commit()
            
            return True
        except Exception as e:
            self.db.rollback()
            self.logger.error(f"移除菜单失败: {e}")
            raise

    def get_role_permissions(self, role_id: int, current_user: User) -> List[Permission]:
        """
        获取角色的权限列表 - 优化N+1查询

        Args:
            role_id: 角色ID
            current_user: 当前操作用户

        Returns:
            List[Permission]: 权限列表
        """
        try:
            # 使用预加载获取角色及其权限，避免N+1查询
            role = self._get_role_with_preloaded_permissions(role_id)
            if not role:
                return []

            return list(role.permissions) if role.permissions else []
        except Exception as e:
            self.logger.error(f"获取角色权限失败: {e}")
            return []

    def get_role_menus(self, role_id: int, current_user: User) -> List[Menu]:
        """
        获取角色的菜单列表 - 优化N+1查询

        Args:
            role_id: 角色ID
            current_user: 当前操作用户

        Returns:
            List[Menu]: 菜单列表
        """
        try:
            # 使用预加载获取角色及其菜单，避免N+1查询
            role = self._get_role_with_preloaded_menus(role_id)
            if not role:
                return []

            return list(role.menus) if role.menus else []
        except Exception as e:
            self.logger.error(f"获取角色菜单失败: {e}")
            return []

    def get_role_users(self, role_id: int, current_user: User) -> List[User]:
        """
        获取使用该角色的用户列表 - 优化N+1查询

        Args:
            role_id: 角色ID
            current_user: 当前操作用户

        Returns:
            List[User]: 用户列表
        """
        try:
            # 使用预加载获取角色及其用户，避免N+1查询
            role = self._get_role_with_preloaded_users(role_id)
            if not role:
                return []

            return list(role.users) if role.users else []
        except Exception as e:
            self.logger.error(f"获取角色用户失败: {e}")
            return []

    def batch_assign_permissions(
        self,
        role_id: int,
        permission_ids: List[int],
        current_user: User
    ) -> bool:
        """
        批量分配权限
        
        Args:
            role_id: 角色ID
            permission_ids: 权限ID列表
            current_user: 当前操作用户
            
        Returns:
            bool: 是否分配成功
        """
        try:
            # 只有超级管理员可以批量分配权限
            if not current_user.is_superuser:
                raise ValueError("只有超级管理员可以批量分配权限")
            
            role = self.get(role_id)
            if not role:
                raise ValueError("角色不存在")
            
            # 获取权限列表
            permissions = self.db.query(Permission).filter(Permission.id.in_(permission_ids)).all()
            if len(permissions) != len(permission_ids):
                raise ValueError("部分权限不存在")
            
            # 清空现有权限并分配新权限
            role.permissions.clear()
            role.permissions.extend(permissions)
            self.db.commit()
            
            return True
        except Exception as e:
            self.db.rollback()
            self.logger.error(f"批量分配权限失败: {e}")
            raise

    def batch_assign_menus(
        self,
        role_id: int,
        menu_ids: List[int],
        current_user: User
    ) -> bool:
        """
        批量分配菜单
        
        Args:
            role_id: 角色ID
            menu_ids: 菜单ID列表
            current_user: 当前操作用户
            
        Returns:
            bool: 是否分配成功
        """
        try:
            # 只有超级管理员可以批量分配菜单
            if not current_user.is_superuser:
                raise ValueError("只有超级管理员可以批量分配菜单")
            
            role = self.get(role_id)
            if not role:
                raise ValueError("角色不存在")
            
            # 获取菜单列表
            menus = self.db.query(Menu).filter(Menu.id.in_(menu_ids)).all()
            if len(menus) != len(menu_ids):
                raise ValueError("部分菜单不存在")
            
            # 清空现有菜单并分配新菜单
            role.menus.clear()
            role.menus.extend(menus)
            self.db.commit()
            
            return True
        except Exception as e:
            self.db.rollback()
            self.logger.error(f"批量分配菜单失败: {e}")
            raise

    def search_roles(
        self,
        search_term: str,
        current_user: User,
        skip: int = 0,
        limit: int = 100
    ) -> List[Role]:
        """
        搜索角色

        Args:
            search_term: 搜索关键词
            current_user: 当前用户
            skip: 跳过的记录数
            limit: 限制返回的记录数

        Returns:
            List[Role]: 角色列表
        """
        search_fields = ['name', 'code', 'description']
        return self.search(search_term, search_fields, current_user, skip, limit)

    def _get_role_with_preloaded_permissions(self, role_id: int) -> Optional[Role]:
        """获取角色及其预加载的权限，避免N+1查询"""
        try:
            from sqlalchemy.orm import selectinload
            return (
                self.db.query(Role)
                .options(selectinload(Role.permissions))
                .filter(Role.id == role_id)
                .first()
            )
        except Exception as e:
            self.logger.error(f"获取角色权限关联数据失败: {e}")
            return None

    def _get_role_with_preloaded_menus(self, role_id: int) -> Optional[Role]:
        """获取角色及其预加载的菜单，避免N+1查询"""
        try:
            from sqlalchemy.orm import selectinload
            return (
                self.db.query(Role)
                .options(selectinload(Role.menus))
                .filter(Role.id == role_id)
                .first()
            )
        except Exception as e:
            self.logger.error(f"获取角色菜单关联数据失败: {e}")
            return None

    def _get_role_with_preloaded_users(self, role_id: int) -> Optional[Role]:
        """获取角色及其预加载的用户，避免N+1查询"""
        try:
            from sqlalchemy.orm import selectinload
            return (
                self.db.query(Role)
                .options(selectinload(Role.users))
                .filter(Role.id == role_id)
                .first()
            )
        except Exception as e:
            self.logger.error(f"获取角色用户关联数据失败: {e}")
            return None
