from typing import Optional
from pydantic import BaseModel, EmailStr, HttpUrl


class NotificationConfigBase(BaseModel):
    """通知配置基础模型"""

    wechat_webhook_url: Optional[HttpUrl] = None
    dingtalk_webhook_url: Optional[HttpUrl] = None
    telegram_bot_token: Optional[str] = None
    telegram_chat_id: Optional[str] = None

    smtp_host: Optional[str] = None
    smtp_port: Optional[int] = None
    smtp_user: Optional[str] = None
    smtp_password: Optional[str] = None
    alert_email_from: Optional[EmailStr] = None
    alert_email_to: Optional[EmailStr] = None

    alert_threshold_warning: Optional[int] = None
    alert_threshold_error: Optional[int] = None
    alert_threshold_critical: Optional[int] = None

    wechat_enabled: Optional[bool] = None
    dingtalk_enabled: Optional[bool] = None
    telegram_enabled: Optional[bool] = None
    email_enabled: Optional[bool] = None


class NotificationConfigCreate(NotificationConfigBase):
    """创建通知配置"""

    pass


class NotificationConfigUpdate(NotificationConfigBase):
    """更新通知配置"""

    pass


class NotificationConfigInDB(NotificationConfigBase):
    """数据库中的通知配置"""

    id: int

    class Config:
        from_attributes = True
