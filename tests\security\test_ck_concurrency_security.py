"""
CK选择机制并发安全性测试
验证修复后的CK选择机制在高并发场景下的安全性和功能性
"""

import pytest
import asyncio
import time
import threading
from typing import List, Dict, Any
from unittest.mock import patch
from sqlalchemy.orm import Session
from concurrent.futures import ThreadPoolExecutor, as_completed

from app.models.walmart_ck import WalmartCK
from app.models.department import Department
from app.models.merchant import Merchant
from app.services.simplified_ck_service import SimplifiedCKService, AtomicCKUpdateService
from app.services.atomic_binding_service import AtomicBindingService
from tests.walmart_binding.conftest import TestDatabase


class TestCKConcurrencySecurity:
    """CK选择机制并发安全性测试类"""
    
    @pytest.fixture(autouse=True)
    def setup_test_data(self, db: Session):
        """设置测试数据"""
        # 创建测试商户
        merchant = Merchant(
            name="测试商户",
            code="TEST_MERCHANT",
            status=True
        )
        db.add(merchant)
        db.flush()
        
        # 创建测试部门
        departments = []
        for i in range(3):
            dept = Department(
                merchant_id=merchant.id,
                name=f"测试部门{i+1}",
                code=f"DEPT_{i+1}",
                status=True,
                enable_binding=True,
                binding_weight=100 + i * 50  # 不同权重：100, 150, 200
            )
            db.add(dept)
            departments.append(dept)
        
        db.flush()
        
        # 创建测试CK
        walmart_cks = []
        for dept in departments:
            for j in range(5):  # 每个部门5个CK
                ck = WalmartCK(
                    merchant_id=merchant.id,
                    department_id=dept.id,
                    sign=f"test_ck_{dept.id}_{j}@token#sign#version",
                    total_limit=20,
                    bind_count=0,
                    active=True,
                    is_deleted=False
                )
                db.add(ck)
                walmart_cks.append(ck)
        
        db.commit()
        
        self.merchant = merchant
        self.departments = departments
        self.walmart_cks = walmart_cks
    
    @pytest.mark.asyncio
    async def test_concurrent_ck_selection_no_oversell(self, db: Session):
        """测试高并发CK选择不会出现超卖问题"""
        print("\n=== 测试并发CK选择防超卖 ===")
        
        # 设置一个CK的限制为较小值以便测试
        test_ck = self.walmart_cks[0]
        test_ck.total_limit = 5
        test_ck.bind_count = 0
        db.commit()
        
        concurrent_requests = 20  # 20个并发请求争抢5个限制的CK
        results = []
        
        async def select_ck():
            """模拟CK选择"""
            service = SimplifiedCKService(db)
            ck = await service.get_available_ck(
                merchant_id=self.merchant.id,
                department_id=test_ck.department_id
            )
            return ck
        
        # 执行并发测试
        tasks = [select_ck() for _ in range(concurrent_requests)]
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # 统计成功选择的CK
        successful_selections = [r for r in results if isinstance(r, WalmartCK)]
        
        # 验证结果
        db.refresh(test_ck)
        print(f"CK限制: {test_ck.total_limit}")
        print(f"最终bind_count: {test_ck.bind_count}")
        print(f"成功选择次数: {len(successful_selections)}")
        print(f"CK是否被禁用: {not test_ck.active}")
        
        # 断言：bind_count不应超过total_limit
        assert test_ck.bind_count <= test_ck.total_limit, f"CK超卖！bind_count({test_ck.bind_count}) > total_limit({test_ck.total_limit})"
        
        # 断言：当达到限制时CK应该被自动禁用
        if test_ck.bind_count >= test_ck.total_limit:
            assert not test_ck.active, "CK达到限制但未被自动禁用"
    
    @pytest.mark.asyncio
    async def test_weight_algorithm_distribution(self, db: Session):
        """测试权重算法的分配效果"""
        print("\n=== 测试权重算法分配效果 ===")
        
        service = SimplifiedCKService(db)
        test_rounds = 1000
        department_selections = {dept.id: 0 for dept in self.departments}
        
        for _ in range(test_rounds):
            ck = await service.get_available_ck(merchant_id=self.merchant.id)
            if ck:
                department_selections[ck.department_id] += 1
        
        # 计算实际分配比例
        total_selections = sum(department_selections.values())
        actual_ratios = {
            dept_id: count / total_selections 
            for dept_id, count in department_selections.items()
        }
        
        # 计算期望比例（基于权重）
        total_weight = sum(dept.binding_weight for dept in self.departments)
        expected_ratios = {
            dept.id: dept.binding_weight / total_weight 
            for dept in self.departments
        }
        
        print("权重分配结果:")
        for dept in self.departments:
            dept_id = dept.id
            expected = expected_ratios[dept_id]
            actual = actual_ratios.get(dept_id, 0)
            deviation = abs(actual - expected)
            
            print(f"部门{dept.name}: 权重={dept.binding_weight}, "
                  f"期望比例={expected:.2%}, 实际比例={actual:.2%}, "
                  f"偏差={deviation:.2%}")
            
            # 允许5%的偏差
            assert deviation < 0.05, f"部门{dept.name}权重分配偏差过大: {deviation:.2%}"
    
    @pytest.mark.asyncio
    async def test_load_balancing_effectiveness(self, db: Session):
        """测试负载均衡效果"""
        print("\n=== 测试负载均衡效果 ===")
        
        service = SimplifiedCKService(db)
        test_rounds = 500
        ck_usage_count = {ck.id: 0 for ck in self.walmart_cks}
        
        # 模拟绑卡请求
        for _ in range(test_rounds):
            ck = await service.get_available_ck(merchant_id=self.merchant.id)
            if ck:
                ck_usage_count[ck.id] += 1
        
        # 分析负载分布
        usage_counts = list(ck_usage_count.values())
        non_zero_usage = [count for count in usage_counts if count > 0]
        
        if non_zero_usage:
            avg_usage = sum(non_zero_usage) / len(non_zero_usage)
            max_usage = max(non_zero_usage)
            min_usage = min(non_zero_usage)
            
            # 计算负载均衡分数（越接近1越均衡）
            balance_score = min_usage / max_usage if max_usage > 0 else 1
            
            print(f"负载均衡分析:")
            print(f"平均使用次数: {avg_usage:.2f}")
            print(f"最大使用次数: {max_usage}")
            print(f"最小使用次数: {min_usage}")
            print(f"负载均衡分数: {balance_score:.3f}")
            
            # 负载均衡分数应该大于0.7（相对均衡）
            assert balance_score > 0.7, f"负载均衡效果不佳: {balance_score:.3f}"
    
    @pytest.mark.asyncio
    async def test_recursive_depth_limit(self, db: Session):
        """测试递归深度限制"""
        print("\n=== 测试递归深度限制 ===")
        
        # 创建exclude_ids包含所有CK的情况
        all_ck_ids = [ck.id for ck in self.walmart_cks]
        
        from app.services.walmart_ck_service_new import WalmartCKServiceNew
        service = WalmartCKServiceNew(db)
        
        # 这应该触发递归深度限制
        ck = await service.get_available_ck(
            merchant_id=self.merchant.id,
            exclude_ids=all_ck_ids
        )
        
        # 应该返回None而不是无限递归
        assert ck is None, "递归深度限制未生效"
        print("✅ 递归深度限制正常工作")
    
    @pytest.mark.asyncio
    async def test_department_enable_binding_switch(self, db: Session):
        """测试部门绑卡开关功能"""
        print("\n=== 测试部门绑卡开关 ===")
        
        # 禁用第一个部门的绑卡
        test_dept = self.departments[0]
        test_dept.enable_binding = False
        db.commit()
        
        service = SimplifiedCKService(db)
        
        # 多次尝试获取CK，应该不会选择被禁用部门的CK
        for _ in range(50):
            ck = await service.get_available_ck(merchant_id=self.merchant.id)
            if ck:
                assert ck.department_id != test_dept.id, f"选择了被禁用部门的CK: {ck.department_id}"
        
        print("✅ 部门绑卡开关正常工作")
        
        # 恢复部门状态
        test_dept.enable_binding = True
        db.commit()


if __name__ == "__main__":
    pytest.main([__file__, "-v", "-s"])
