from typing import Any, Dict, Optional
from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session

from app.api import deps
from app.models.user import User
from app.models.walmart_server import WalmartServer
from app.api.deps import get_db, require_permissions
from app.schemas.response import success_response, error_response
from app.core.errors import BusinessException, ErrorCode
from app.schemas.system import SystemParams, PublicSystemParams

router = APIRouter()

# 全局系统参数实例（开发环境临时使用，生产环境应该使用数据库存储）
_system_params = SystemParams()
_public_params = PublicSystemParams()


# 系统参数接口 - 需要认证
@router.get("/params", response_model=SystemParams)
@require_permissions("system:config")
async def get_system_params(
    db: Session = Depends(get_db),
    current_user: User = Depends(deps.get_current_active_user),
) -> Any:
    """
    获取系统参数（需要认证）

    权限要求:
    - "system:config"
    """
    from app.services.system_service import SystemService

    # 调用service层获取系统参数
    config = await SystemService.get_system_params(db)

    if config:
        # 将数据库中的配置转换为系统参数模型
        return SystemParams(
            daily_bind_limit=config.daily_bind_limit,
            api_rate_limit=config.api_rate_limit,
            max_retry_times=config.max_retry_times,
            bind_timeout_seconds=config.bind_timeout_seconds,
            verification_code_expires=config.verification_code_expires,
            log_retention_days=config.log_retention_days,
            enable_ip_whitelist=config.enable_ip_whitelist,
            enable_security_audit=config.enable_security_audit,
            maintenance_mode=config.maintenance_mode,
            maintenance_message=config.maintenance_message,
        )
    # 如果数据库中没有配置，使用默认参数
    return _system_params


# 公开系统参数接口 - 无需认证
@router.get("/public-params", response_model=PublicSystemParams)
async def get_public_system_params(
    db: Session = Depends(get_db),
) -> Any:
    """
    获取公开系统参数（无需认证）
    """
    try:
        # 从数据库获取系统配置
        config = db.query(WalmartServer).first()
        if config:
            # 只返回公开参数
            return PublicSystemParams(
                api_timeout=15,  # 默认值，可以根据配置调整
                maintenance_mode=config.maintenance_mode,
                maintenance_message=(
                    config.maintenance_message if config.maintenance_mode else None
                ),
                version="1.0.0",  # 系统版本，可以从配置中读取
            )

        # 如果数据库中没有配置，返回默认公开参数
        return _public_params
    except Exception as e:
        # 捕获异常，返回默认参数
        return _public_params


@router.put("/params", response_model=SystemParams)
@require_permissions("system:config")
async def update_system_params(
    *,
    db: Session = Depends(get_db),
    params_in: SystemParams,
    current_user: User = Depends(deps.get_current_active_user),
) -> Any:
    """
    更新系统参数

    权限要求:
    - "system:config"
    """
    from app.services.system_service import SystemService

    try:
        # 调用service层更新系统参数
        config = await SystemService.update_system_params(db, params_in)

        # 同时更新内存中的参数(用于开发环境)
        global _system_params, _public_params
        _system_params = params_in

        # 更新公开参数
        _public_params = PublicSystemParams(
            api_timeout=15,  # 默认值，可以根据配置调整
            maintenance_mode=params_in.maintenance_mode,
            maintenance_message=(
                params_in.maintenance_message if params_in.maintenance_mode else None
            ),
            version="1.0.0",
        )

        return params_in
    except BusinessException as e:
        # 业务异常直接抛出
        raise e
    except Exception as e:
        # 其他异常转换为业务异常
        raise BusinessException(
            message=f"更新系统参数失败: {str(e)}", code=ErrorCode.SYSTEM_ERROR
        )


# 获取租户系统参数 (针对前端添加的API)
@router.get("/merchants/{merchant_id}/params", response_model=Dict[str, Any])
@require_permissions("merchant:view", merchant_param="merchant_id")
async def get_merchant_system_params(
    merchant_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(deps.get_current_active_user),
) -> Any:
    """
    获取商家系统参数

    注意: 用户如果拥有 merchant:manage 或 merchant:all 权限，自动继承 merchant:view 权限。

    权限要求:
    - "merchant:view"
    """
    from app.services.system_service import SystemService

    # 调用service层获取商家系统参数
    return await SystemService.get_merchant_system_params(db, merchant_id, current_user)


# 更新商家系统参数 (针对前端添加的API)
@router.put("/merchants/{merchant_id}/params", response_model=Dict[str, Any])
@require_permissions("merchant:edit", merchant_param="merchant_id")
async def update_merchant_system_params(
    merchant_id: int,
    params: Dict[str, Any],
    db: Session = Depends(get_db),
    current_user: User = Depends(deps.get_current_active_user),
) -> Any:
    """
    更新商家系统参数

    权限要求:
    - "merchant:edit"
    """
    from app.services.system_service import SystemService

    # 调用service层更新商家系统参数
    return await SystemService.update_merchant_system_params(
        db, merchant_id, params, current_user
    )


# 添加系统维护模式API
@router.post("/maintenance", response_model=Dict[str, Any])
@require_permissions("system:config", "system:maintenance", require_all=False)
def system_maintenance(
    *,
    db: Session = Depends(get_db),
    enable: bool,
    message: Optional[str] = None,
    current_user: User = Depends(deps.get_current_active_user),
) -> Any:
    """
    切换系统维护模式

    只需拥有system:config或system:maintenance权限中的任意一个即可操作。

    参数:
    - enable: 是否启用维护模式
    - message: 维护消息（可选）

    权限要求:
    - "system:config" 或
    - "system:maintenance"
    """
    from app.services.system_service import SystemService

    # 获取现有系统配置
    config = SystemService.get_system_params(db)

    # 如果配置不存在，创建默认配置
    if not config:
        from app.models.walmart_server import WalmartServer

        config = WalmartServer()
        db.add(config)

    # 更新配置
    config.maintenance_mode = enable
    if message is not None:
        config.maintenance_message = message

    # 保存配置
    db.commit()
    db.refresh(config)

    # 同步更新内存中的缓存
    global _system_params, _public_params
    _system_params.maintenance_mode = enable
    if message is not None:
        _system_params.maintenance_message = message

    _public_params.maintenance_mode = enable
    if message is not None:
        _public_params.maintenance_message = message

    return {
        "success": True,
        "maintenance_mode": enable,
        "maintenance_message": config.maintenance_message,
    }
