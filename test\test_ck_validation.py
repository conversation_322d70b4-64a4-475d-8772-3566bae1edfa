"""
CK验证功能测试用例
测试CK有效性验证、总次数限制等功能
"""

import pytest
import asyncio
from unittest.mock import Mock, patch, AsyncMock
from sqlalchemy.orm import Session
from app.services.ck_validation_service import CKValidationService
from app.services.walmart_ck_service_new import WalmartCKService
from app.models.walmart_ck import WalmartCK
from app.models.merchants import Merchant
from app.models.departments import Department
from app.db.session import SessionLocal


class TestCKValidation:
    """CK验证功能测试类"""

    @pytest.fixture
    def db_session(self):
        """数据库会话fixture"""
        db = SessionLocal()
        try:
            yield db
        finally:
            db.close()

    @pytest.fixture
    def sample_merchant(self, db_session):
        """创建测试商户"""
        merchant = Merchant(
            name="测试商户",
            code="TEST_MERCHANT",
            status="active"
        )
        db_session.add(merchant)
        db_session.commit()
        db_session.refresh(merchant)
        return merchant

    @pytest.fixture
    def sample_department(self, db_session, sample_merchant):
        """创建测试部门"""
        department = Department(
            name="测试部门",
            code="TEST_DEPT",
            merchant_id=sample_merchant.id,
            status="active"
        )
        db_session.add(department)
        db_session.commit()
        db_session.refresh(department)
        return department

    @pytest.fixture
    def sample_ck(self, db_session, sample_merchant, sample_department):
        """创建测试CK"""
        ck = WalmartCK(
            sign="test_sign_123",
            total_limit=20,
            bind_count=5,
            active=True,
            merchant_id=sample_merchant.id,
            department_id=sample_department.id,
            is_deleted=False
        )
        db_session.add(ck)
        db_session.commit()
        db_session.refresh(ck)
        return ck

    def test_ck_availability_check_basic(self, db_session, sample_ck):
        """测试基本的CK可用性检查"""
        ck_service = WalmartCKService(db_session)
        
        # 测试正常可用的CK
        assert ck_service._is_ck_available(sample_ck) is True
        
        # 测试已删除的CK
        sample_ck.is_deleted = True
        assert ck_service._is_ck_available(sample_ck) is False
        
        # 测试已禁用的CK
        sample_ck.is_deleted = False
        sample_ck.active = False
        assert ck_service._is_ck_available(sample_ck) is False
        
        # 测试达到限制的CK
        sample_ck.active = True
        sample_ck.bind_count = 20
        assert ck_service._is_ck_available(sample_ck) is False

    def test_ck_total_limit_enforcement(self, db_session, sample_ck):
        """测试总次数限制的执行"""
        ck_service = WalmartCKService(db_session)
        
        # 模拟绑卡成功，增加使用次数
        for i in range(15):  # 从5增加到20
            result = ck_service.record_ck_usage(sample_ck.id, True)
            assert result is True
            
        # 刷新CK状态
        db_session.refresh(sample_ck)
        
        # 验证CK已被自动禁用
        assert sample_ck.bind_count == 20
        assert sample_ck.active is False

    @pytest.mark.asyncio
    async def test_ck_validation_service_success(self, db_session, sample_ck):
        """测试CK验证服务 - 成功情况"""
        validation_service = CKValidationService(db_session)

        # Mock API响应 - 成功（实际格式）
        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.json.return_value = {
            "logId": "YYtFTnf4",
            "status": True,
            "error": {
                "errorcode": 1,
                "message": None,
                "redirect": None,
                "validators": None
            },
            "data": {
                "cardCount": 4,
                "nickName": "微信用户",
                "headImg": "https://thirdwx.qlogo.cn/mmopen/vi_32/POgEwh4mIHO4nibH0KlMECNjjGxQUq24ZEaGT4poC6icRiccVGKSyXwibcPq4BWmiaIGuG1icwxaQX6grC9VemZoJ8rg/132",
                "upcardOrderUrl": "https://vpay.upcard.com.cn/vcweixin/commercial/walm/gotoQuery%3FopenId%3Dxxx%26company%3Dwalm"
            }
        }

        with patch('app.core.walmart_api.WalmartAPI.query_user', new_callable=AsyncMock) as mock_query:
            mock_query.return_value = mock_response

            result = await validation_service.validate_ck_availability(sample_ck)

            assert result["is_valid"] is True
            assert result["error_message"] is None
            assert result["should_disable"] is False
            assert result["log_id"] == "YYtFTnf4"
            assert result["card_count"] == 4
            assert result["nick_name"] == "微信用户"

    @pytest.mark.asyncio
    async def test_ck_validation_service_failure(self, db_session, sample_ck):
        """测试CK验证服务 - 失败情况"""
        validation_service = CKValidationService(db_session)

        # Mock API响应 - 失败（实际格式）
        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.json.return_value = {
            "logId": "ABC123",
            "status": False,
            "error": {
                "errorcode": 401,
                "message": "签名无效",
                "redirect": None,
                "validators": None
            },
            "data": None
        }

        with patch('app.core.walmart_api.WalmartAPI.query_user', new_callable=AsyncMock) as mock_query:
            mock_query.return_value = mock_response

            result = await validation_service.validate_ck_availability(sample_ck)

            assert result["is_valid"] is False
            assert result["error_message"] == "签名无效"
            assert result["error_code"] == "401"
            assert result["should_disable"] is True
            assert result["log_id"] == "ABC123"

            # 验证CK已被禁用
            db_session.refresh(sample_ck)
            assert sample_ck.active is False

    @pytest.mark.asyncio
    async def test_ck_validation_service_success_with_normal_errorcode(self, db_session, sample_ck):
        """测试CK验证服务 - 成功但有正常错误码的情况"""
        validation_service = CKValidationService(db_session)

        # Mock API响应 - status=true但errorcode=1（正常业务状态）
        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.json.return_value = {
            "logId": "NORMAL123",
            "status": True,
            "error": {
                "errorcode": 1,
                "message": None,
                "redirect": None,
                "validators": None
            },
            "data": {
                "cardCount": 2,
                "nickName": "测试用户",
                "headImg": "https://example.com/avatar.jpg",
                "upcardOrderUrl": "https://example.com/order"
            }
        }

        with patch('app.core.walmart_api.WalmartAPI.query_user', new_callable=AsyncMock) as mock_query:
            mock_query.return_value = mock_response

            result = await validation_service.validate_ck_availability(sample_ck)

            assert result["is_valid"] is True
            assert result["error_message"] is None
            assert result["should_disable"] is False
            assert result["log_id"] == "NORMAL123"
            assert result["card_count"] == 2
            assert result["nick_name"] == "测试用户"

            # 验证CK未被禁用
            db_session.refresh(sample_ck)
            assert sample_ck.active is True

    @pytest.mark.asyncio
    async def test_ck_validation_service_critical_error_with_true_status(self, db_session, sample_ck):
        """测试CK验证服务 - status=true但有严重错误码的情况"""
        validation_service = CKValidationService(db_session)

        # Mock API响应 - status=true但errorcode=-1（严重错误）
        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.json.return_value = {
            "logId": "ERROR123",
            "status": True,
            "error": {
                "errorcode": -1,
                "message": "系统内部错误",
                "redirect": None,
                "validators": None
            },
            "data": None
        }

        with patch('app.core.walmart_api.WalmartAPI.query_user', new_callable=AsyncMock) as mock_query:
            mock_query.return_value = mock_response

            result = await validation_service.validate_ck_availability(sample_ck)

            assert result["is_valid"] is False
            assert result["error_message"] == "系统内部错误"
            assert result["error_code"] == "-1"
            assert result["should_disable"] is True
            assert result["log_id"] == "ERROR123"

            # 验证CK已被禁用
            db_session.refresh(sample_ck)
            assert sample_ck.active is False

    @pytest.mark.asyncio
    async def test_get_available_ck_with_validation(self, db_session, sample_merchant, sample_department):
        """测试带验证的CK选择"""
        ck_service = WalmartCKService(db_session)
        
        # 创建多个测试CK
        cks = []
        for i in range(3):
            ck = WalmartCK(
                sign=f"test_sign_{i}",
                total_limit=20,
                bind_count=i * 2,
                active=True,
                merchant_id=sample_merchant.id,
                department_id=sample_department.id,
                is_deleted=False
            )
            db_session.add(ck)
            cks.append(ck)
        
        db_session.commit()
        
        # Mock验证结果：第一个CK失效，第二个CK有效
        def mock_validation(ck):
            if ck.bind_count == 0:  # 第一个CK
                return {
                    "is_valid": False,
                    "error_message": "CK失效",
                    "error_code": "401",
                    "should_disable": True,
                    "log_id": "TEST_FAIL"
                }
            else:  # 其他CK
                return {
                    "is_valid": True,
                    "error_message": None,
                    "error_code": None,
                    "should_disable": False,
                    "log_id": "TEST_SUCCESS",
                    "card_count": 3,
                    "nick_name": "测试用户"
                }
        
        with patch('app.services.ck_validation_service.CKValidationService.validate_ck_availability', 
                   side_effect=lambda ck: asyncio.coroutine(lambda: mock_validation(ck))()) as mock_validate:
            
            selected_ck = await ck_service.get_available_ck(
                merchant_id=sample_merchant.id,
                department_id=sample_department.id,
                validate_ck=True
            )
            
            # 应该选择第二个CK（bind_count=2，第一个有效的）
            assert selected_ck is not None
            assert selected_ck.bind_count == 2

    @pytest.mark.asyncio
    async def test_batch_ck_validation(self, db_session, sample_merchant, sample_department):
        """测试批量CK验证"""
        validation_service = CKValidationService(db_session)
        
        # 创建多个测试CK
        cks = []
        for i in range(5):
            ck = WalmartCK(
                sign=f"batch_test_sign_{i}",
                total_limit=20,
                bind_count=i,
                active=True,
                merchant_id=sample_merchant.id,
                department_id=sample_department.id,
                is_deleted=False
            )
            db_session.add(ck)
            cks.append(ck)
        
        db_session.commit()
        
        # Mock验证结果：奇数索引的CK失效
        def mock_validation(ck):
            index = int(ck.sign.split('_')[-1])
            if index % 2 == 1:  # 奇数索引失效
                return {
                    "is_valid": False,
                    "error_message": "CK失效",
                    "error_code": "401",
                    "should_disable": True,
                    "log_id": f"BATCH_FAIL_{index}"
                }
            else:  # 偶数索引有效
                return {
                    "is_valid": True,
                    "error_message": None,
                    "error_code": None,
                    "should_disable": False,
                    "log_id": f"BATCH_SUCCESS_{index}",
                    "card_count": index + 1,
                    "nick_name": f"用户{index}"
                }
        
        with patch('app.services.ck_validation_service.CKValidationService.validate_ck_availability', 
                   side_effect=lambda ck: asyncio.coroutine(lambda: mock_validation(ck))()) as mock_validate:
            
            results = await validation_service.validate_multiple_cks(cks)
            
            # 验证结果
            assert len(results) == 5
            valid_count = sum(1 for r in results if r["is_valid"])
            invalid_count = sum(1 for r in results if not r["is_valid"])
            
            assert valid_count == 3  # 索引0,2,4有效
            assert invalid_count == 2  # 索引1,3无效

    def test_validation_statistics(self, db_session, sample_merchant, sample_department):
        """测试验证统计信息"""
        validation_service = CKValidationService(db_session)
        
        # 创建不同状态的CK
        # 活跃CK
        for i in range(3):
            ck = WalmartCK(
                sign=f"active_ck_{i}",
                total_limit=20,
                bind_count=i,
                active=True,
                merchant_id=sample_merchant.id,
                department_id=sample_department.id,
                is_deleted=False
            )
            db_session.add(ck)
        
        # 非活跃CK
        for i in range(2):
            ck = WalmartCK(
                sign=f"inactive_ck_{i}",
                total_limit=20,
                bind_count=i,
                active=False,
                merchant_id=sample_merchant.id,
                department_id=sample_department.id,
                is_deleted=False
            )
            db_session.add(ck)
        
        # 达到限制的CK
        ck = WalmartCK(
            sign="limit_reached_ck",
            total_limit=20,
            bind_count=20,
            active=False,
            merchant_id=sample_merchant.id,
            department_id=sample_department.id,
            is_deleted=False
        )
        db_session.add(ck)
        
        db_session.commit()
        
        # 获取统计信息
        stats = validation_service.get_validation_statistics()
        
        assert stats["total_cks"] == 6
        assert stats["active_cks"] == 3
        assert stats["inactive_cks"] == 3
        assert stats["reached_limit_cks"] == 1
        assert stats["availability_rate"] == 50.0


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
