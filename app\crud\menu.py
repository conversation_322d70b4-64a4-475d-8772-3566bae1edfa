from typing import Any, Dict, List, Optional, Union
from sqlalchemy.orm import Session

from app.crud.base import CRUDBase
from app.models.menu import Menu
from app.schemas.menu import MenuCreate, MenuUpdate


class CRUDMenu(CRUDBase[Menu, MenuCreate, MenuUpdate]):
    """Menu的CRUD操作类"""

    def get_by_code(self, db: Session, code: str) -> Optional[Menu]:
        """通过菜单代码获取菜单"""
        return db.query(Menu).filter(Menu.code == code).first()

    def get_root_menus(self, db: Session) -> List[Menu]:
        """获取所有根菜单（没有父菜单的菜单）"""
        return db.query(Menu).filter(Menu.parent_id.is_(None)).all()

    def get_children(self, db: Session, parent_id: int) -> List[Menu]:
        """获取指定菜单的所有子菜单"""
        return db.query(Menu).filter(Menu.parent_id == parent_id).all()

    def get_menu_tree(self, db: Session) -> List[Dict[str, Any]]:
        """获取菜单树结构 - 优化N+1查询"""
        # 一次性获取所有菜单，避免递归查询
        all_menus = self.get_all_with_preload(db)
        return self._build_menu_tree(all_menus)

    def get_visible_menus(self, db: Session) -> List[Menu]:
        """获取所有可见菜单"""
        return db.query(Menu).filter(Menu.is_visible == True).all()

    def get_enabled_menus(self, db: Session) -> List[Menu]:
        """获取所有启用的菜单"""
        return db.query(Menu).filter(Menu.is_enabled == True).all()

    def get_by_type(self, db: Session, menu_type: str) -> List[Menu]:
        """获取指定类型的菜单"""
        return db.query(Menu).filter(Menu.type == menu_type).all()

    def assign_permissions(self, db: Session, menu_id: int, permission_ids: List[int]) -> Menu:
        """为菜单分配权限"""
        from app.models.permission import Permission
        
        menu = self.get(db, id=menu_id)
        if not menu:
            return None
        
        permissions = db.query(Permission).filter(Permission.id.in_(permission_ids)).all()
        menu.permissions = permissions
        
        db.commit()
        db.refresh(menu)
        
        return menu

    def get_all_with_preload(self, db: Session) -> List[Menu]:
        """获取所有菜单及其预加载的关联数据"""
        from sqlalchemy.orm import selectinload
        return (
            db.query(Menu)
            .options(selectinload(Menu.permissions))
            .order_by(Menu.sort_order, Menu.id)
            .all()
        )

    def _build_menu_tree(self, menus: List[Menu]) -> List[Dict[str, Any]]:
        """构建菜单树结构，避免N+1查询"""
        # 将菜单转换为字典并建立索引
        menu_dict = {}
        for menu in menus:
            menu_dict[menu.id] = {
                "id": menu.id,
                "name": menu.name,
                "code": menu.code,
                "path": menu.path,
                "component": menu.component,
                "icon": menu.icon,
                "sort_order": menu.sort_order,
                "is_visible": menu.is_visible,
                "parent_id": menu.parent_id,
                "children": []
            }

        # 构建树结构
        root_menus = []
        for menu_id, menu_data in menu_dict.items():
            if menu_data["parent_id"] is None:
                root_menus.append(menu_data)
            elif menu_data["parent_id"] in menu_dict:
                menu_dict[menu_data["parent_id"]]["children"].append(menu_data)

        # 递归排序
        def sort_children(menu_list):
            menu_list.sort(key=lambda x: (x["sort_order"], x["id"]))
            for menu in menu_list:
                if menu["children"]:
                    sort_children(menu["children"])

        sort_children(root_menus)
        return root_menus


menu = CRUDMenu(Menu)
