-- 为用户表添加谷歌验证器双因子认证字段
-- 执行时间：2025-06-28

-- 添加双因子认证相关字段
ALTER TABLE users
ADD COLUMN totp_secret VARCHAR(255) NULL COMMENT '谷歌验证器密钥(加密存储)',
ADD COLUMN totp_enabled BOOLEAN DEFAULT FALSE COMMENT '是否启用双因子认证',
ADD COLUMN totp_backup_codes TEXT NULL COMMENT '备用恢复码(JSON格式,加密存储)',
ADD COLUMN totp_last_used_at TIMESTAMP NULL COMMENT '最后使用TOTP的时间',
ADD COLUMN totp_setup_at TIMESTAMP NULL COMMENT 'TOTP设置时间';

-- 添加索引
CREATE INDEX idx_users_totp_enabled ON users(totp_enabled);
CREATE INDEX idx_users_totp_last_used ON users(totp_last_used_at);

-- 创建双因子认证日志表
CREATE TABLE totp_logs (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
    user_id BIGINT NOT NULL COMMENT '用户ID',
    action VARCHAR(50) NOT NULL COMMENT '操作类型：setup/verify/disable/backup_used',
    success BOOLEAN NOT NULL COMMENT '操作是否成功',
    ip_address VARCHAR(64) NULL COMMENT '操作IP地址',
    user_agent TEXT NULL COMMENT '用户代理',
    error_message VARCHAR(255) NULL COMMENT '错误信息',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    INDEX idx_totp_logs_user_id (user_id),
    INDEX idx_totp_logs_action (action),
    INDEX idx_totp_logs_created_at (created_at),
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
) COMMENT='双因子认证操作日志表';

-- 创建双因子认证策略表
CREATE TABLE totp_policies (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
    role_name VARCHAR(50) NOT NULL COMMENT '角色名称',
    is_required BOOLEAN DEFAULT FALSE COMMENT '是否强制启用',
    grace_period_days INT DEFAULT 7 COMMENT '宽限期天数',
    backup_codes_count INT DEFAULT 10 COMMENT '备用码数量',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    UNIQUE KEY uk_totp_policies_role (role_name)
) COMMENT='双因子认证策略表';

-- 插入默认策略
INSERT INTO totp_policies (role_name, is_required, grace_period_days, backup_codes_count) VALUES
('super_admin', TRUE, 0, 10),      -- 超级管理员强制启用，无宽限期
('merchant_admin', FALSE, 7, 10),  -- 商户管理员可选，7天宽限期
('ck_supplier', FALSE, 7, 8);      -- CK供应商可选，7天宽限期
