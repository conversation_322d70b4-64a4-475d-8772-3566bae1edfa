from typing import Optional
from redis import Redis
from redis.exceptions import RedisError
from app.core.config import settings
import logging

logger = logging.getLogger(__name__)


class RedisManager:
    _instance = None
    _client = None

    def __new__(cls):
        if cls._instance is None:
            cls._instance = super(RedisManager, cls).__new__(cls)
        return cls._instance

    def __init__(self):
        if self._client is None:
            self._initialize_client()

    def _initialize_client(self):
        try:
            self._client = Redis(
                host=settings.REDIS_HOST,
                port=settings.REDIS_PORT,
                db=settings.REDIS_DB,
                password=settings.REDIS_PASSWORD,
                decode_responses=True,
            )
        except RedisError as e:
            logger.error(f"Redis connection error: {str(e)}")
            raise

    @property
    def client(self) -> Redis:
        return self._client

    def get_client(self) -> Redis:
        if not self._client:
            self._initialize_client()
        return self._client


redis_manager = RedisManager()
