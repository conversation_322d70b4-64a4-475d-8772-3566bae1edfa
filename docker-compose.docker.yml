version: "3.8"

services:
  # 沃尔玛绑卡网关 - 高速构建版（使用阿里云镜像源）
  walmart-gateway:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: walmart-gateway
    ports:
      - "${GATEWAY_PORT:-21001}:21000"
      - "${METRICS_PORT:-9091}:9090" # 监控端口
    environment:
      # 应用配置
      - GIN_MODE=${GIN_MODE:-release}
      - TZ=Asia/Shanghai

      # 数据库配置（连接外部MySQL）
      - DATABASE_HOST=${DB_HOST:-**************}
      - DATABASE_PORT=${DB_PORT:-3306}
      - DATABASE_USER=${DB_USER:-root}
      - DATABASE_PASSWORD=${DB_PASSWORD:-7c222fb2927d828af22f592134e8932480637c0d}
      - DATABASE_DB_NAME=${DB_NAME:-walmart_card_db}

      # Redis配置（连接外部Redis）
      - REDIS_HOST=${REDIS_HOST:-**************}
      - REDIS_PORT=${REDIS_PORT:-6379}
      - REDIS_PASSWORD=${REDIS_PASSWORD:-7c222fb2927d828af22f592134e8932480637c0d}
      - REDIS_DB=${REDIS_DB:-0}

      # RabbitMQ配置（连接外部RabbitMQ）
      - RABBITMQ_HOST=${RABBITMQ_HOST:-**************}
      - RABBITMQ_PORT=${RABBITMQ_PORT:-5672}
      - RABBITMQ_USER=${RABBITMQ_USER:-walmart_card}
      - RABBITMQ_PASSWORD=${RABBITMQ_PASSWORD:-7c222fb2927d828af22f592134e8932480637c0d}
      - RABBITMQ_VHOST=${RABBITMQ_VHOST:-/walmart_card}

      # 服务配置
      - SERVER_PORT=${SERVER_PORT:-21000}
      - SERVER_MODE=${SERVER_MODE:-release}

      # 日志配置
      - LOG_LEVEL=${LOG_LEVEL:-info}
      - LOG_FORMAT=${LOG_FORMAT:-json}

      # 监控配置
      - ENABLE_METRICS=${ENABLE_METRICS:-true}
      - METRICS_PATH=${METRICS_PATH:-/metrics}
      - HEALTH_CHECK_PATH=${HEALTH_CHECK_PATH:-/health}

      # 安全配置
      - JWT_SECRET=${JWT_SECRET:-01aTO25Fwsaw18700DEc70FH127ZHw001SiZV32AbBgO3n94i0G0xFm3vK24717b}
      - JWT_EXPIRE_HOURS=${JWT_EXPIRE_HOURS:-24}
      - API_RATE_LIMIT=${API_RATE_LIMIT:-1000}

      # 性能配置
      - DB_MAX_OPEN_CONNS=${DB_MAX_OPEN_CONNS:-10}
      - DB_MAX_IDLE_CONNS=${DB_MAX_IDLE_CONNS:-5}
      - DB_CONN_MAX_LIFETIME=${DB_CONN_MAX_LIFETIME:-3600}

    volumes:
      - ./logs:/app/logs
    
    restart: unless-stopped
    
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:21000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s

    # 资源限制
    deploy:
      resources:
        limits:
          cpus: '1.0'
          memory: 512M
        reservations:
          cpus: '0.5'
          memory: 256M

networks:
  default:
    name: walmart-gateway-network
