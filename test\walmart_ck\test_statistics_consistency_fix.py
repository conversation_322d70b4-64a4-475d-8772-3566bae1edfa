#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
CK删除功能统计数据一致性修复验证测试
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

import pytest
from sqlalchemy.orm import Session
from app.database import get_db
from app.models.walmart_ck import WalmartCK
from app.models.card_record import CardRecord
from app.models.user import User
from app.models.merchant import Merchant
from app.models.department import Department
from app.services.walmart_ck_service_new import WalmartCKService
from datetime import datetime
import uuid

class TestStatisticsConsistencyFix:
    """测试CK删除后统计数据一致性修复"""
    
    @pytest.fixture(autouse=True)
    def setup(self):
        """测试设置"""
        self.db = next(get_db())
        self.ck_service = WalmartCKService(self.db)
        
        # 获取测试用户
        self.admin_user = self.db.query(User).filter(User.username == "admin").first()
        assert self.admin_user is not None, "找不到admin用户"
        
        # 获取测试商户和部门
        self.test_merchant = self.db.query(Merchant).first()
        self.test_department = self.db.query(Department).first()
        
        assert self.test_merchant is not None, "找不到测试商户"
        assert self.test_department is not None, "找不到测试部门"
        
        self.test_ck_ids = []
        self.test_record_ids = []
        
        yield
        
        # 清理测试数据
        self.cleanup_test_data()
        self.db.close()
    
    def create_test_ck(self, suffix=""):
        """创建测试CK"""
        ck_data = {
            "sign": f"test_consistency_{uuid.uuid4().hex[:8]}{suffix}@token#signature#26",
            "daily_limit": 100,
            "hourly_limit": 50,
            "active": True,
            "description": f"统计一致性测试CK{suffix}",
            "merchant_id": self.test_merchant.id,
            "department_id": self.test_department.id,
            "created_by": self.admin_user.id
        }
        
        ck = WalmartCK(**ck_data)
        self.db.add(ck)
        self.db.commit()
        self.db.refresh(ck)
        
        self.test_ck_ids.append(ck.id)
        return ck
    
    def create_test_card_record(self, ck_id, amount=1000, status='success'):
        """创建测试绑卡记录"""
        record_data = {
            "id": str(uuid.uuid4()),
            "merchant_id": self.test_merchant.id,
            "department_id": self.test_department.id,
            "walmart_ck_id": ck_id,
            "merchant_order_id": f"test_order_{uuid.uuid4().hex[:8]}",
            "amount": amount,
            "actual_amount": amount if status == 'success' else None,
            "card_number": f"test_card_{uuid.uuid4().hex[:8]}",
            "status": status,
            "request_id": str(uuid.uuid4()),
            "trace_id": str(uuid.uuid4())
        }
        
        record = CardRecord(**record_data)
        self.db.add(record)
        self.db.commit()
        self.db.refresh(record)
        
        self.test_record_ids.append(record.id)
        return record
    
    def test_statistics_consistency_after_ck_deletion(self):
        """测试CK删除后统计数据一致性"""
        print("\n=== 测试CK删除后统计数据一致性 ===")
        
        # 1. 创建测试数据：3个CK，每个有2个成功绑卡记录
        test_cks = []
        test_records = []
        
        for i in range(3):
            ck = self.create_test_ck(f"_{i}")
            test_cks.append(ck)
            
            # 为每个CK创建2个成功绑卡记录
            for j in range(2):
                record = self.create_test_card_record(ck.id, amount=1000, status='success')
                test_records.append(record)
        
        print(f"创建了 {len(test_cks)} 个CK，{len(test_records)} 个绑卡记录")
        
        # 2. 获取删除前的统计数据
        before_ck_stats = self.ck_service.get_ck_statistics(
            self.test_merchant.id, self.admin_user
        )
        
        before_amount_stats = self.ck_service.get_ck_binding_amount_statistics(
            current_user=self.admin_user,
            merchant_id=self.test_merchant.id
        )
        
        before_success_count = before_ck_stats['actual_success_count']
        before_total_amount = before_amount_stats['summary']['total_actual_amount']
        before_total_records = before_amount_stats['summary']['total_success']
        
        print(f"删除前统计 - 成功数: {before_success_count}, 金额: {before_total_amount}, 记录数: {before_total_records}")
        
        # 3. 软删除前两个CK
        deleted_cks = test_cks[:2]
        for ck in deleted_cks:
            success = self.ck_service.delete_walmart_ck(ck.id, self.admin_user)
            assert success, f"删除CK {ck.id} 失败"
            print(f"已删除CK: {ck.id}")
        
        # 4. 获取删除后的统计数据
        after_ck_stats = self.ck_service.get_ck_statistics(
            self.test_merchant.id, self.admin_user
        )
        
        after_amount_stats = self.ck_service.get_ck_binding_amount_statistics(
            current_user=self.admin_user,
            merchant_id=self.test_merchant.id
        )
        
        after_success_count = after_ck_stats['actual_success_count']
        after_total_amount = after_amount_stats['summary']['total_actual_amount']
        after_total_records = after_amount_stats['summary']['total_success']
        
        print(f"删除后统计 - 成功数: {after_success_count}, 金额: {after_total_amount}, 记录数: {after_total_records}")
        
        # 5. 验证统计数据一致性
        # 历史绑卡数据应该保持不变
        assert before_success_count == after_success_count, \
            f"绑卡成功数不一致：删除前{before_success_count}，删除后{after_success_count}"
        
        assert before_total_amount == after_total_amount, \
            f"绑卡金额不一致：删除前{before_total_amount}，删除后{after_total_amount}"
        
        assert before_total_records == after_total_records, \
            f"成功记录数不一致：删除前{before_total_records}，删除后{after_total_records}"
        
        print("✅ 统计数据一致性验证通过")
        
        # 6. 验证CK数量统计正确减少
        # 注意：这里测试的是活跃CK数量，应该减少
        before_active_count = before_ck_stats['active_count']
        after_active_count = after_ck_stats['active_count']
        
        # 由于我们删除了2个CK，活跃CK数量应该减少（但这取决于初始数据）
        print(f"活跃CK数量 - 删除前: {before_active_count}, 删除后: {after_active_count}")
        
        # 7. 验证已删除CK不会出现在绑卡金额统计的CK详情中
        ck_details = after_amount_stats.get('ck_details', [])
        deleted_ck_ids = [ck.id for ck in deleted_cks]
        
        for detail in ck_details:
            ck_id = detail.get('ck_id')
            assert ck_id not in deleted_ck_ids, f"已删除的CK {ck_id} 仍出现在统计详情中"
        
        print("✅ 已删除CK正确地从详情列表中移除")
    
    def test_historical_binding_data_preservation(self):
        """测试历史绑卡数据保留"""
        print("\n=== 测试历史绑卡数据保留 ===")
        
        # 1. 创建CK和绑卡记录
        ck = self.create_test_ck("_historical")
        record1 = self.create_test_card_record(ck.id, amount=2000, status='success')
        record2 = self.create_test_card_record(ck.id, amount=3000, status='success')
        
        # 2. 验证绑卡记录存在
        records_before = self.db.query(CardRecord).filter(
            CardRecord.walmart_ck_id == ck.id,
            CardRecord.status == 'success'
        ).all()
        
        assert len(records_before) == 2, f"期望2个绑卡记录，实际{len(records_before)}个"
        total_amount_before = sum(r.actual_amount for r in records_before)
        
        print(f"删除前：{len(records_before)}个绑卡记录，总金额{total_amount_before}分")
        
        # 3. 删除CK
        success = self.ck_service.delete_walmart_ck(ck.id, self.admin_user)
        assert success, "删除CK失败"
        
        # 4. 验证绑卡记录仍然存在
        records_after = self.db.query(CardRecord).filter(
            CardRecord.walmart_ck_id == ck.id,
            CardRecord.status == 'success'
        ).all()
        
        assert len(records_after) == 2, f"删除CK后绑卡记录丢失，期望2个，实际{len(records_after)}个"
        total_amount_after = sum(r.actual_amount for r in records_after)
        
        assert total_amount_before == total_amount_after, \
            f"删除CK后绑卡金额变化：删除前{total_amount_before}，删除后{total_amount_after}"
        
        print(f"删除后：{len(records_after)}个绑卡记录，总金额{total_amount_after}分")
        print("✅ 历史绑卡数据完整保留")
    
    def cleanup_test_data(self):
        """清理测试数据"""
        try:
            # 删除测试绑卡记录
            for record_id in self.test_record_ids:
                record = self.db.query(CardRecord).filter(CardRecord.id == record_id).first()
                if record:
                    self.db.delete(record)
            
            # 删除测试CK
            for ck_id in self.test_ck_ids:
                ck = self.db.query(WalmartCK).filter(WalmartCK.id == ck_id).first()
                if ck:
                    self.db.delete(ck)
            
            self.db.commit()
            print("✅ 测试数据清理完成")
        except Exception as e:
            self.db.rollback()
            print(f"⚠️ 测试数据清理失败: {e}")

if __name__ == "__main__":
    # 运行测试
    test = TestStatisticsConsistencyFix()
    test.setup()
    
    try:
        test.test_statistics_consistency_after_ck_deletion()
        test.test_historical_binding_data_preservation()
        print("\n🎉 所有测试通过！")
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        raise
    finally:
        test.cleanup_test_data()
