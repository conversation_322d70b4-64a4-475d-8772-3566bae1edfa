import { http } from '@/api/request'
import { API_URLS, replaceUrlParams } from './config'

const { MENU } = API_URLS

/**
 * 菜单管理相关API
 */
export const menuApi = {
  // 获取菜单列表
  getList(params) {
    return http.get(MENU.LIST, { params }).then(res => res.data)
  },

  // 获取菜单详情
  getDetail(id) {
    const url = replaceUrlParams(MENU.DETAIL, { id })
    return http.get(url).then(res => res.data)
  },

  // 创建菜单
  create(data) {
    return http.post(MENU.CREATE, data).then(res => res.data)
  },

  // 更新菜单
  update(id, data) {
    const url = replaceUrlParams(MENU.UPDATE, { id })
    return http.put(url, data).then(res => res.data)
  },

  // 删除菜单
  delete(id) {
    const url = replaceUrlParams(MENU.DELETE, { id })
    return http.delete(url).then(res => res.data)
  },

  // 获取菜单树
  getMenuTree() {
    return http.get(MENU.LIST, { params: { is_tree: true } }).then(res => res.data)
  },

  // 获取当前用户的菜单
  getUserMenus() {
    return http.get(MENU.USER_MENUS).then(res => {
      // 处理嵌套的响应格式
      if (res.data && res.data.data) {
        return res.data.data
      } else if (res.data) {
        return res.data
      }
      return res
    })
  },

  // 为菜单分配权限
  assignPermissions: (menuId, permissionIds) => {
    const url = replaceUrlParams(MENU.ASSIGN_PERMISSIONS, { id: menuId })
    return http.post(url, { permission_ids: permissionIds }).then(res => res.data)
  }
}
