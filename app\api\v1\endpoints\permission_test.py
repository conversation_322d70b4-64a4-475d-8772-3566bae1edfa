"""
权限系统测试API端点
用于测试新的权限系统功能
"""

from typing import List, Dict, Any
from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session

from app.api.deps import get_current_active_user, get_db
from app.models.user import User
from app.core.auth import auth_service
from app.core.permission_checker import require_permission, require_permissions
from app.schemas.permission import (
    PermissionCheckResult, 
    UserPermissionInfo, 
    UserPermissionCheck
)

router = APIRouter()


@router.get("/user-permissions", response_model=Dict[str, Any])
async def get_current_user_permissions(
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """
    获取当前用户的权限信息
    """
    try:
        # 获取权限摘要
        permission_summary = auth_service.get_permission_summary(current_user)

        # 获取用户菜单
        user_menus = auth_service.get_user_menus(db, current_user)
        
        return {
            "user_info": permission_summary,
            "menus": user_menus,
            "message": "权限信息获取成功"
        }
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取权限信息失败: {str(e)}"
        )


@router.post("/check-permission", response_model=PermissionCheckResult)
async def check_permission(
    permission_check: UserPermissionCheck,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """
    检查指定权限
    """
    try:
        result = auth_service.check_permission_detailed(
            user=current_user,
            permission_code=permission_check.permission_code,
            resource_id=permission_check.resource_id,
            merchant_id=permission_check.merchant_id,
            department_id=permission_check.department_id
        )
        return result
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"权限检查失败: {str(e)}"
        )


@router.get("/test-dashboard")
@require_permission("dashboard:view")
async def test_dashboard_permission(
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """
    测试仪表盘权限
    """
    return {
        "message": "仪表盘权限检查通过",
        "user": current_user.username,
        "permission": "dashboard:view"
    }


@router.get("/test-merchant/{merchant_id}")
@require_permission("merchant:view", merchant_param="merchant_id")
async def test_merchant_permission(
    merchant_id: int,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """
    测试商户权限
    """
    return {
        "message": "商户权限检查通过",
        "user": current_user.username,
        "merchant_id": merchant_id,
        "permission": "merchant:view"
    }


@router.get("/test-user-management")
@require_permissions("user:view", "user:create", require_all=False)
async def test_user_management_permission(
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """
    测试用户管理权限（需要任一权限）
    """
    return {
        "message": "用户管理权限检查通过",
        "user": current_user.username,
        "permissions": ["user:view", "user:create"]
    }


@router.get("/test-admin-only")
@require_permission("system:config")
async def test_admin_only_permission(
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """
    测试管理员专用权限
    """
    return {
        "message": "管理员权限检查通过",
        "user": current_user.username,
        "permission": "system:config"
    }


@router.get("/test-ck-supplier")
@require_permission("walmart:view_own")
async def test_ck_supplier_permission(
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """
    测试CK供应商权限
    """
    return {
        "message": "CK供应商权限检查通过",
        "user": current_user.username,
        "permission": "walmart:view_own"
    }


@router.get("/data-scope-test")
async def test_data_scope(
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """
    测试数据权限范围
    """
    try:
        data_scope = auth_service.get_user_data_scope(current_user, db)
        accessible_merchants = auth_service.get_user_accessible_merchants(current_user, db)
        accessible_departments = auth_service.get_user_accessible_departments(current_user, db)

        return {
            "user": current_user.username,
            "role": current_user.role,
            "data_scope": data_scope,
            "accessible_merchants": accessible_merchants,
            "accessible_departments": accessible_departments,
            "can_access_merchant_1": auth_service.can_access_merchant_data(current_user, 1, db),
            "can_access_department_1": auth_service.can_access_department_data(current_user, 1, db)
        }
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"数据权限范围测试失败: {str(e)}"
        )


@router.get("/menu-test")
async def test_menu_access(
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """
    测试菜单访问权限
    """
    try:
        user_menus = auth_service.get_user_menus(db, current_user)

        # 测试特定菜单权限
        menu_tests = {
            "dashboard": auth_service.check_menu_permission(db, current_user, "dashboard"),
            "system": auth_service.check_menu_permission(db, current_user, "system"),
            "merchant": auth_service.check_menu_permission(db, current_user, "merchant"),
            "walmart": auth_service.check_menu_permission(db, current_user, "walmart"),
        }
        
        return {
            "user": current_user.username,
            "role": current_user.role,
            "available_menus": user_menus,
            "menu_permissions": menu_tests
        }
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"菜单权限测试失败: {str(e)}"
        )


@router.get("/permission-summary")
async def get_permission_summary(
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """
    获取权限系统摘要信息
    """
    try:
        summary = auth_service.get_permission_summary(current_user)
        return {
            "summary": summary,
            "system_info": {
                "permission_service": "new_permission_service",
                "menu_service": "new_menu_service",
                "auth_service": "new_auth_service"
            }
        }
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取权限摘要失败: {str(e)}"
        )
