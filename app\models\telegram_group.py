from sqlalchemy import Column, String, BigInteger, DateTime, ForeignKey, JSON
from sqlalchemy.orm import relationship


from app.models.base import BaseModel, TimestampMixin


class ChatType:
    """群组类型常量"""
    GROUP = "group"
    SUPERGROUP = "supergroup"
    CHANNEL = "channel"

    @classmethod
    def get_all_values(cls):
        """获取所有群组类型值"""
        return [cls.GROUP, cls.SUPERGROUP, cls.CHANNEL]


class BindStatus:
    """绑定状态常量"""
    PENDING = "pending"
    ACTIVE = "active"
    SUSPENDED = "suspended"
    FAILED = "failed"  # 绑定失败，可重试

    @classmethod
    def get_all_values(cls):
        """获取所有绑定状态值"""
        return [cls.PENDING, cls.ACTIVE, cls.SUSPENDED, cls.FAILED]

    @classmethod
    def get_retryable_values(cls):
        """获取可重试的绑定状态值"""
        return [cls.PENDING, cls.FAILED]


class TelegramGroup(BaseModel, TimestampMixin):
    """Telegram群组绑定表"""

    __tablename__ = "telegram_groups"

    # 基础信息
    chat_id = Column(
        BigInteger, 
        nullable=False, 
        unique=True, 
        index=True,
        comment="Telegram群组ID"
    )
    chat_title = Column(
        String(255), 
        nullable=False, 
        comment="群组标题"
    )
    chat_type = Column(
        String(20),
        nullable=False,
        comment="群组类型"
    )

    # 绑定关系
    merchant_id = Column(
        BigInteger, 
        ForeignKey("merchants.id", ondelete="CASCADE"), 
        nullable=False,
        index=True,
        comment="绑定的商户ID"
    )
    department_id = Column(
        BigInteger, 
        ForeignKey("departments.id", ondelete="SET NULL"), 
        nullable=True,
        index=True,
        comment="绑定的部门ID（可选，用于部门级数据隔离）"
    )
    remark = Column(
        String(255), 
        nullable=True,
        comment="备注"
    )

    # 绑定验证
    bind_token = Column(
        String(64), 
        nullable=False, 
        unique=True,
        index=True,
        comment="绑定令牌（用于安全绑定验证）"
    )
    bind_status = Column(
        String(20),
        nullable=False,
        default="pending",
        index=True,
        comment="绑定状态"
    )
    bind_time = Column(
        DateTime(timezone=True), 
        nullable=True, 
        comment="绑定完成时间"
    )
    bind_user_id = Column(
        BigInteger, 
        ForeignKey("users.id", ondelete="SET NULL"), 
        nullable=True,
        comment="执行绑定操作的用户ID"
    )

    # 配置和状态
    settings = Column(
        JSON, 
        nullable=True, 
        comment="群组设置（通知开关、时区等）"
    )
    last_active_time = Column(
        DateTime(timezone=True), 
        nullable=True, 
        comment="最后活跃时间"
    )

    # 关联关系
    merchant = relationship(
        "Merchant", 
        back_populates="telegram_groups",
        foreign_keys=[merchant_id]
    )
    department = relationship(
        "Department", 
        foreign_keys=[department_id]
    )
    bind_user = relationship(
        "User", 
        foreign_keys=[bind_user_id]
    )

    # 操作日志关联
    bot_logs = relationship(
        "TelegramBotLog", 
        back_populates="group",
        cascade="all, delete-orphan"
    )

    def to_dict(self, include_sensitive=False):
        """转换为字典

        Args:
            include_sensitive (bool): 是否包含敏感信息（如绑定令牌）
        """
        result = {
            "id": self.id,
            "chat_id": self.chat_id,
            "chat_title": self.chat_title,
            "chat_type": self.chat_type if self.chat_type else None,
            "merchant_id": self.merchant_id,
            "merchant_name": self.merchant.name if self.merchant else None,
            "department_id": self.department_id,
            "department_name": self.department.name if self.department else None,
            "remark": self.remark,
            "bind_status": self.bind_status if self.bind_status else None,
            "bind_time": self.bind_time.isoformat() if self.bind_time else None,
            "bind_user_id": self.bind_user_id,
            "bind_user_name": self.bind_user.username if self.bind_user else None,
            "settings": self.settings,
            "last_active_time": self.last_active_time.isoformat() if self.last_active_time else None,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None,
        }

        # 只在需要时包含敏感信息
        if include_sensitive:
            result["bind_token"] = self.bind_token

        return result

    def is_active(self):
        """检查群组是否处于活跃状态"""
        return self.bind_status == BindStatus.ACTIVE

    def can_query_stats(self):
        """检查是否可以查询统计数据"""
        return self.bind_status == BindStatus.ACTIVE

    def update_last_active(self):
        """更新最后活跃时间"""
        from app.models.base import local_now
        self.last_active_time = local_now()

    def get_default_settings(self):
        """获取默认设置"""
        return {
            "permissions": {
                "allow_all_members": False,
                "require_user_verification": True,
                "admin_only_commands": ["bind", "unbind", "settings"],
                "rate_limit": {
                    "commands_per_minute": 10,
                    "queries_per_hour": 100
                },
                "query_permissions": {
                    "daily_stats": True,
                    "weekly_stats": True,
                    "monthly_stats": True,
                    "custom_range": False,
                    "detailed_data": False
                }
            },
            "display_settings": {
                "show_amount": True,
                "show_details": False,
                "decimal_places": 2,
                "timezone": "Asia/Shanghai",
                "language": "zh-CN",
                "currency_symbol": "¥",
                "date_format": "YYYY-MM-DD",
                "time_format": "HH:mm:ss"
            },
            "notification_settings": {
                "auto_notification": True,
                "welcome_message": True,
                "command_help": True,
                "auto_delete_commands": False,
                "error_notification": True,
                "daily_report": False,
                "daily_report_time": "09:00"
            },
            "advanced_settings": {
                "enable_cache": True,
                "cache_expire_minutes": 5,
                "enable_audit_log": True,
                "custom_commands": {},
                "webhook_settings": {
                    "enable_webhook": False,
                    "webhook_url": "",
                    "webhook_secret": ""
                }
            }
        }

    def get_effective_settings(self) -> dict:
        """获取生效的配置（支持继承）"""
        from app.services.telegram_config_service import TelegramConfigService

        # 避免循环导入，在需要时才导入
        config_service = TelegramConfigService()
        return config_service.get_effective_group_settings(self)

    def get_permission_config(self) -> dict:
        """获取权限配置"""
        settings = self.get_effective_settings()
        return settings.get('permissions', {})

    def get_display_config(self) -> dict:
        """获取显示配置"""
        settings = self.get_effective_settings()
        return settings.get('display_settings', {})

    def get_notification_config(self) -> dict:
        """获取通知配置"""
        settings = self.get_effective_settings()
        return settings.get('notification_settings', {})

    def get_advanced_config(self) -> dict:
        """获取高级配置"""
        settings = self.get_effective_settings()
        return settings.get('advanced_settings', {})

    def is_member_allowed_to_query(self) -> bool:
        """检查是否允许所有成员查询"""
        permissions = self.get_permission_config()
        return permissions.get('allow_all_members', False)

    def requires_user_verification(self) -> bool:
        """检查是否需要用户验证"""
        permissions = self.get_permission_config()
        return permissions.get('require_user_verification', True)

    def is_command_allowed(self, command: str, is_admin: bool = False) -> bool:
        """检查命令是否被允许"""
        permissions = self.get_permission_config()
        admin_only_commands = permissions.get('admin_only_commands', [])

        if command in admin_only_commands:
            return is_admin

        return True

    def get_query_permissions(self) -> dict:
        """获取查询权限配置"""
        permissions = self.get_permission_config()
        return permissions.get('query_permissions', {})

    def is_query_allowed(self, query_type: str) -> bool:
        """检查查询类型是否被允许"""
        query_permissions = self.get_query_permissions()
        return query_permissions.get(query_type, False)

    def get_rate_limit(self, limit_type: str) -> int:
        """获取频率限制"""
        permissions = self.get_permission_config()
        rate_limit = permissions.get('rate_limit', {})

        default_limits = {
            'commands_per_minute': 10,
            'queries_per_hour': 100
        }

        return rate_limit.get(limit_type, default_limits.get(limit_type, 0))

    def merge_settings(self, new_settings):
        """合并设置"""
        current_settings = self.settings or {}
        merged_settings = self._deep_merge_settings([current_settings, new_settings])
        self.settings = merged_settings
        return self.settings

    def update_permission_settings(self, new_permissions: dict):
        """更新权限设置"""
        current_settings = self.settings or {}
        if 'permissions' not in current_settings:
            current_settings['permissions'] = {}

        current_settings['permissions'].update(new_permissions)
        self.settings = current_settings
        return self.settings

    def update_display_settings(self, new_display: dict):
        """更新显示设置"""
        current_settings = self.settings or {}
        if 'display_settings' not in current_settings:
            current_settings['display_settings'] = {}

        current_settings['display_settings'].update(new_display)
        self.settings = current_settings
        return self.settings

    def apply_permission_template(self, template_settings: dict):
        """应用权限模板"""
        return self.merge_settings(template_settings)

    def _deep_merge_settings(self, settings_list: list) -> dict:
        """深度合并多个配置字典"""
        result = {}

        for settings in settings_list:
            if not settings:
                continue

            for key, value in settings.items():
                if key not in result:
                    result[key] = value
                elif isinstance(value, dict) and isinstance(result[key], dict):
                    result[key] = self._deep_merge_settings([result[key], value])
                else:
                    result[key] = value

        return result
