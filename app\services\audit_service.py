import logging
from datetime import datetime, timedelta
from typing import Optional, Dict, Any, List, Tuple
from fastapi import Request
from sqlalchemy.orm import Session
from sqlalchemy import func

from app.models.audit import AuditLog, AuditEventType, AuditLevel
from app.models.user import User
from app.schemas.audit import AuditLogCreate, AuditLogUpdate
from app.services.base_service import BaseService
from app.core.config import settings
from app.utils.notification import send_alert
from app.core.exceptions import BusinessException

logger = logging.getLogger(__name__)


class AuditService(BaseService[AuditLog, AuditLogCreate, AuditLogUpdate]):
    """统一的审计服务 - 【安全修复】继承BaseService实现数据隔离"""

    def __init__(self, db: Session):
        super().__init__(AuditLog, db)
        self.alert_thresholds = {
            AuditLevel.WARNING: 10,  # 10分钟内超过10次警告触发告警
            AuditLevel.ERROR: 5,  # 10分钟内超过5次错误触发告警
            AuditLevel.CRITICAL: 1,  # 立即触发告警
        }

    def apply_data_isolation(self, query, current_user: User):
        """
        【安全修复】重写基类方法，添加强制商户隔离
        应用数据隔离规则 - 支持用户级、部门级、商户级权限，确保审计日志数据的商户隔离

        Args:
            query: SQLAlchemy查询对象
            current_user: 当前用户

        Returns:
            query: 应用隔离规则后的查询对象
        """
        # 【安全修复】：强制商户隔离 - 除超级管理员外，所有用户只能访问自己商户的审计日志
        if not current_user.is_superuser:
            if not current_user.merchant_id:
                # 如果用户没有商户ID，返回空结果
                self.logger.warning(f"[SECURITY] 用户 {current_user.id} 没有商户ID，拒绝审计日志访问")
                query = query.filter(AuditLog.id == -1)  # 强制返回空结果
                return query

            # 强制添加商户过滤，确保无法跨商户访问
            query = query.filter(AuditLog.merchant_id == current_user.merchant_id)
            self.logger.info(f"[SECURITY] 强制商户隔离：用户 {current_user.id} 只能访问商户 {current_user.merchant_id} 的审计日志")

        # 然后应用基类的数据隔离逻辑
        return super().apply_data_isolation(query, current_user)

    def _get_merchant_id_from_user(self, user_id: Optional[int]) -> Optional[int]:
        """
        【安全修复】从用户ID获取商户ID，确保审计日志正确关联商户

        Args:
            user_id: 用户ID

        Returns:
            Optional[int]: 商户ID
        """
        if not user_id:
            return None

        try:
            user = self.db.query(User).filter(User.id == user_id).first()
            if user:
                return user.merchant_id
            else:
                self.logger.warning(f"[SECURITY] 未找到用户 {user_id}，无法获取商户信息")
                return None
        except Exception as e:
            self.logger.error(f"[SECURITY] 获取用户商户信息失败: {e}")
            return None

    async def log_event(
        self,
        db: Session,
        event_type: AuditEventType,
        level: AuditLevel,
        message: str,
        request: Optional[Request] = None,
        user_id: Optional[int] = None,
        operator_id: Optional[int] = None,
        merchant_id: Optional[int] = None,
        resource_type: Optional[str] = None,
        resource_id: Optional[int] = None,
        action: Optional[str] = None,
        details: Optional[Dict[str, Any]] = None,
        trace_id: Optional[str] = None,
    ) -> AuditLog:
        """记录审计事件"""
        try:
            # 【安全修复】如果没有提供merchant_id，尝试从用户ID获取
            if merchant_id is None and user_id is not None:
                merchant_id = self._get_merchant_id_from_user(user_id)

            # 【安全修复】如果没有提供merchant_id，尝试从操作者ID获取
            if merchant_id is None and operator_id is not None:
                merchant_id = self._get_merchant_id_from_user(operator_id)

            # 创建审计日志
            audit = AuditLog(
                event_type=event_type,
                level=level,
                message=message,
                user_id=user_id,
                operator_id=operator_id,
                merchant_id=merchant_id,  # 【安全修复】自动填充的商户ID
                resource_type=resource_type,
                resource_id=resource_id,
                action=action,
                details=details,
                trace_id=trace_id,
            )

            # 如果有请求信息，记录请求相关信息
            if request:
                audit.ip_address = request.client.host
                audit.user_agent = request.headers.get("user-agent")
                audit.request_method = request.method
                audit.request_path = str(request.url.path)
                # 记录请求参数（注意敏感信息过滤）
                params = dict(request.query_params)
                if params:
                    # 过滤敏感信息
                    filtered_params = self._filter_sensitive_data(params)
                    audit.request_params = filtered_params

            # 保存到数据库
            db.add(audit)
            db.commit()
            db.refresh(audit)

            # 检查是否需要触发告警
            await self._check_alert_threshold(db, level, audit)

            return audit

        except Exception as e:
            db.rollback()
            logger.error(f"记录审计日志失败: {str(e)}", exc_info=True)
            raise BusinessException(f"记录审计日志失败: {str(e)}")

    async def log_access(
        self,
        db: Session,
        request: Request,
        user_id: Optional[int] = None,
        merchant_id: Optional[int] = None,
        trace_id: Optional[str] = None,
    ):
        """记录访问日志"""
        return await self.log_event(
            db=db,
            event_type=AuditEventType.ACCESS,
            level=AuditLevel.INFO,
            message=f"Access {request.method} {request.url.path}",
            request=request,
            user_id=user_id,
            merchant_id=merchant_id,
            trace_id=trace_id,
        )

    async def log_operation(
        self,
        db: Session,
        action: str,
        resource_type: str,
        resource_id: int,
        user_id: int,
        operator_id: Optional[int] = None,
        details: Optional[Dict[str, Any]] = None,
        request: Optional[Request] = None,
    ):
        """记录操作日志"""
        return await self.log_event(
            db=db,
            event_type=AuditEventType.OPERATION,
            level=AuditLevel.INFO,
            message=f"{action} {resource_type} {resource_id}",
            user_id=user_id,
            operator_id=operator_id,
            resource_type=resource_type,
            resource_id=resource_id,
            action=action,
            details=details,
            request=request,
        )

    async def log_auth_failure(
        self,
        db: Session,
        request: Request,
        reason: str,
        user_id: Optional[int] = None,
        merchant_id: Optional[int] = None,
    ):
        """记录认证失败"""
        return await self.log_event(
            db=db,
            event_type=AuditEventType.AUTH,
            level=AuditLevel.WARNING,
            message=f"Authentication failed: {reason}",
            request=request,
            user_id=user_id,
            merchant_id=merchant_id,
        )

    async def log_attack(
        self,
        db: Session,
        request: Request,
        attack_type: str,
        details: Dict[str, Any],
    ):
        """记录可疑攻击"""
        return await self.log_event(
            db=db,
            event_type=AuditEventType.ATTACK,
            level=AuditLevel.ERROR,
            message=f"Potential attack detected: {attack_type}",
            request=request,
            details=details,
        )

    async def get_audit_logs(
        self,
        db: Session,
        page: int = 1,
        page_size: int = 20,
        event_type: Optional[AuditEventType] = None,
        level: Optional[AuditLevel] = None,
        user_id: Optional[int] = None,
        operator_id: Optional[int] = None,
        resource_type: Optional[str] = None,
        action: Optional[str] = None,
        start_time: Optional[datetime] = None,
        end_time: Optional[datetime] = None,
    ) -> Tuple[List[AuditLog], int]:
        """获取审计日志"""
        query = db.query(AuditLog)

        # 应用过滤条件
        if event_type:
            query = query.filter(AuditLog.event_type == event_type)
        if level:
            query = query.filter(AuditLog.level == level)
        if user_id:
            query = query.filter(AuditLog.user_id == user_id)
        if operator_id:
            query = query.filter(AuditLog.operator_id == operator_id)
        if resource_type:
            query = query.filter(AuditLog.resource_type == resource_type)
        if action:
            query = query.filter(AuditLog.action == action)
        if start_time:
            query = query.filter(AuditLog.created_at >= start_time)
        if end_time:
            query = query.filter(AuditLog.created_at <= end_time)

        # 获取总数
        total = query.count()

        # 分页
        logs = (
            query.order_by(AuditLog.created_at.desc())
            .offset((page - 1) * page_size)
            .limit(page_size)
            .all()
        )

        return logs, total

    def _filter_sensitive_data(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """过滤敏感信息"""
        sensitive_fields = {
            "password",
            "card_password",
            "api_key",
            "api_secret",
            "token",
            "access_token",
            "refresh_token",
            "secret",
            "private_key",
        }

        filtered = {}
        for key, value in data.items():
            if key.lower() in sensitive_fields:
                filtered[key] = "***"
            else:
                filtered[key] = value
        return filtered

    async def _check_alert_threshold(
        self,
        db: Session,
        level: AuditLevel,
        current_event: AuditLog,
    ):
        """检查是否需要触发告警"""
        # Critical级别直接触发告警
        if level == AuditLevel.CRITICAL:
            await self._send_alert(current_event)
            return

        # 获取阈值
        threshold = self.alert_thresholds.get(level)
        if not threshold:
            return

        # 检查最近10分钟内的事件数量
        recent_count = (
            db.query(func.count(AuditLog.id))
            .filter(
                AuditLog.level == level,
                AuditLog.created_at >= datetime.utcnow() - timedelta(minutes=10),
            )
            .scalar()
        )

        # 如果超过阈值，触发告警
        if recent_count >= threshold:
            await self._send_alert(current_event, recent_count)

    async def _send_alert(
        self,
        event: AuditLog,
        event_count: Optional[int] = None,
    ):
        """发送安全告警"""
        alert_data = {
            "level": event.level,
            "event_type": event.event_type,
            "message": event.message,
            "ip_address": event.ip_address,
            "time": event.created_at.isoformat(),
            "event_count": event_count,
        }

        if event.user_id:
            alert_data["user_id"] = event.user_id
        if event.merchant_id:
            alert_data["merchant_id"] = event.merchant_id

        await send_alert(
            title=f"Security Alert: {event.level.upper()}",
            content=alert_data,
        )


# 【安全修复】移除全局实例化，改为在需要时创建实例
# 由于AuditService现在继承BaseService，需要db参数，不能全局实例化
# 使用时请通过 AuditService(db) 创建实例
