#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
卡记录管理CRUD测试
测试卡记录的查询、统计、敏感信息获取等功能
"""

import sys
import os
import time
import json

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.dirname(__file__))))

from test.conftest import TestBase, get_test_accounts, format_test_result, print_test_summary

class CardRecordsCRUDTestSuite(TestBase):
    """卡记录管理CRUD测试类"""
    
    def __init__(self):
        super().__init__()
        self.results = []
        self.test_accounts = get_test_accounts()
        self.admin_token = None
        self.merchant_token = None
        
    def setup_test_environment(self):
        """设置测试环境"""
        print("=== 设置卡记录测试环境 ===")
        
        # 登录管理员账号
        self.admin_token = self.login(
            self.test_accounts["super_admin"]["username"],
            self.test_accounts["super_admin"]["password"]
        )
        
        # 登录商户账号
        self.merchant_token = self.login(
            self.test_accounts["merchant_admin"]["username"],
            self.test_accounts["merchant_admin"]["password"]
        )
        
        if not self.admin_token:
            raise Exception("无法获取管理员token")
        if not self.merchant_token:
            raise Exception("无法获取商户token")
            
        print("✅ 测试环境设置完成")

    def _get_first_merchant_id(self):
        """获取第一个商户ID用于测试"""
        if not self.admin_token:
            return None

        try:
            status_code, response = self.make_request("GET", "/merchants", self.admin_token)
            if status_code == 200:
                merchants_data = response.get("data", response)
                if isinstance(merchants_data, dict):
                    merchants = merchants_data.get("items", [])
                elif isinstance(merchants_data, list):
                    merchants = merchants_data
                else:
                    merchants = []

                if merchants and len(merchants) > 0:
                    return merchants[0].get("id")
            return None
        except Exception as e:
            print(f"获取商户ID失败: {e}")
            return None

    def test_get_card_records_list(self):
        """测试获取卡记录列表"""
        print("\n=== 测试获取卡记录列表 ===")
        
        # 获取商户ID用于超级管理员测试
        merchant_id = self._get_first_merchant_id()

        # 测试管理员获取卡记录列表
        params = {"merchant_id": merchant_id} if merchant_id else {}
        status_code, response = self.make_request("GET", "/cards", self.admin_token, params=params)
        
        if status_code == 200:
            self.results.append(format_test_result(
                "管理员获取卡记录列表",
                True,
                "管理员成功获取卡记录列表"
            ))
            print("✅ 管理员成功获取卡记录列表")
            
            # 检查响应格式
            if "data" in response:
                data = response["data"]
                if isinstance(data, dict) and "items" in data:
                    print(f"   📊 找到 {len(data['items'])} 条卡记录")
                elif isinstance(data, list):
                    print(f"   📊 找到 {len(data)} 条卡记录")
        else:
            self.results.append(format_test_result(
                "管理员获取卡记录列表",
                False,
                f"获取卡记录列表失败，状态码: {status_code}"
            ))
            print(f"❌ 获取卡记录列表失败，状态码: {status_code}")
        
        # 测试商户获取卡记录列表（应该只能看到自己的数据）
        status_code, response = self.make_request("GET", "/cards", self.merchant_token)
        
        if status_code == 200:
            self.results.append(format_test_result(
                "商户获取卡记录列表",
                True,
                "商户成功获取卡记录列表"
            ))
            print("✅ 商户成功获取卡记录列表")
        else:
            self.results.append(format_test_result(
                "商户获取卡记录列表",
                False,
                f"商户获取卡记录列表失败，状态码: {status_code}"
            ))
            print(f"❌ 商户获取卡记录列表失败，状态码: {status_code}")
    
    def test_get_card_statistics(self):
        """测试获取卡记录统计"""
        print("\n=== 测试获取卡记录统计 ===")
        
        # 获取商户ID
        merchant_id = self._get_first_merchant_id()

        # 测试获取卡记录统计
        if merchant_id:
            status_code, response = self.make_request("GET", f"/cards/statistics/{merchant_id}", self.admin_token)
        else:
            print("⚠️ 无法获取商户ID，跳过统计测试")
            return
        
        if status_code == 200:
            self.results.append(format_test_result(
                "获取卡记录统计",
                True,
                "成功获取卡记录统计"
            ))
            print("✅ 成功获取卡记录统计")
        else:
            self.results.append(format_test_result(
                "获取卡记录统计",
                False,
                f"获取卡记录统计失败，状态码: {status_code}"
            ))
            print(f"❌ 获取卡记录统计失败，状态码: {status_code}")
        
        # 测试获取今日统计 (使用统计API的今日数据)
        if merchant_id:
            from datetime import datetime, date
            # 修复时区问题：使用上海时区的当前日期
            from app.utils.time_utils import get_current_time
            today = get_current_time().date()
            params = {
                "start_date": today.isoformat(),
                "end_date": today.isoformat()
            }
            status_code, response = self.make_request("GET", f"/cards/statistics/{merchant_id}", self.admin_token, params=params)
        else:
            print("⚠️ 无法获取商户ID，跳过今日统计测试")
            return
        
        if status_code == 200:
            self.results.append(format_test_result(
                "获取今日卡记录统计",
                True,
                "成功获取今日卡记录统计"
            ))
            print("✅ 成功获取今日卡记录统计")
        else:
            self.results.append(format_test_result(
                "获取今日卡记录统计",
                False,
                f"获取今日卡记录统计失败，状态码: {status_code}"
            ))
            print(f"❌ 获取今日卡记录统计失败，状态码: {status_code}")
    
    def test_card_sensitive_info_access(self):
        """测试卡记录敏感信息访问权限"""
        print("\n=== 测试卡记录敏感信息访问权限 ===")
        
        # 首先获取一个卡记录ID
        merchant_id = self._get_first_merchant_id()
        params = {"page": 1, "size": 1}
        if merchant_id:
            params["merchant_id"] = merchant_id
        status_code, response = self.make_request("GET", "/cards", self.admin_token, params=params)
        
        if status_code == 200 and "data" in response:
            data = response["data"]
            items = data.get("items", []) if isinstance(data, dict) else data if isinstance(data, list) else []
            
            if items and len(items) > 0:
                card_id = items[0].get("id")
                if card_id:
                    # 测试管理员访问敏感信息
                    status_code, response = self.make_request("GET", f"/cards/{card_id}/sensitive", self.admin_token)
                    
                    if status_code == 200:
                        self.results.append(format_test_result(
                            "管理员访问卡记录敏感信息",
                            True,
                            "管理员成功访问卡记录敏感信息"
                        ))
                        print("✅ 管理员成功访问卡记录敏感信息")
                    else:
                        self.results.append(format_test_result(
                            "管理员访问卡记录敏感信息",
                            False,
                            f"管理员访问敏感信息失败，状态码: {status_code}"
                        ))
                        print(f"❌ 管理员访问敏感信息失败，状态码: {status_code}")
                    
                    # 测试商户访问敏感信息（权限控制）
                    status_code, response = self.make_request("GET", f"/cards/{card_id}/sensitive", self.merchant_token)
                    
                    if status_code in [200, 403, 404]:  # 200表示有权限，403表示无权限，404表示不存在
                        self.results.append(format_test_result(
                            "商户访问卡记录敏感信息权限控制",
                            True,
                            f"商户访问敏感信息权限控制正常，状态码: {status_code}"
                        ))
                        print(f"✅ 商户访问敏感信息权限控制正常，状态码: {status_code}")
                    else:
                        self.results.append(format_test_result(
                            "商户访问卡记录敏感信息权限控制",
                            False,
                            f"商户访问敏感信息权限控制异常，状态码: {status_code}"
                        ))
                        print(f"❌ 商户访问敏感信息权限控制异常，状态码: {status_code}")
                else:
                    print("⚠️ 无法获取卡记录ID，跳过敏感信息访问测试")
            else:
                print("⚠️ 没有找到卡记录，跳过敏感信息访问测试")
        else:
            print("⚠️ 获取卡记录列表失败，跳过敏感信息访问测试")
    
    def test_card_data_isolation(self):
        """测试卡记录数据隔离"""
        print("\n=== 测试卡记录数据隔离 ===")
        
        # 获取管理员看到的卡记录数量
        merchant_id = self._get_first_merchant_id()
        params = {"merchant_id": merchant_id} if merchant_id else {}
        status_code, admin_response = self.make_request("GET", "/cards", self.admin_token, params=params)
        admin_count = 0
        
        if status_code == 200 and "data" in admin_response:
            data = admin_response["data"]
            if isinstance(data, dict) and "total" in data:
                admin_count = data["total"]
            elif isinstance(data, list):
                admin_count = len(data)
        
        # 获取商户看到的卡记录数量
        status_code, merchant_response = self.make_request("GET", "/cards", self.merchant_token)
        merchant_count = 0
        
        if status_code == 200 and "data" in merchant_response:
            data = merchant_response["data"]
            if isinstance(data, dict) and "total" in data:
                merchant_count = data["total"]
            elif isinstance(data, list):
                merchant_count = len(data)
        
        # 验证数据隔离
        if admin_count >= merchant_count:
            self.results.append(format_test_result(
                "卡记录数据隔离",
                True,
                f"数据隔离正常，管理员看到 {admin_count} 条，商户看到 {merchant_count} 条"
            ))
            print(f"✅ 数据隔离正常，管理员看到 {admin_count} 条，商户看到 {merchant_count} 条")
        else:
            self.results.append(format_test_result(
                "卡记录数据隔离",
                False,
                f"数据隔离异常，商户看到的数据比管理员多"
            ))
            print(f"❌ 数据隔离异常，商户看到的数据比管理员多")
    
    def run_all_tests(self):
        """运行所有卡记录测试"""
        print("🧪 开始卡记录管理CRUD测试")
        print("="*60)
        
        start_time = time.time()
        
        try:
            # 设置测试环境
            self.setup_test_environment()
            
            # 运行测试
            self.test_get_card_records_list()
            self.test_get_card_statistics()
            self.test_card_sensitive_info_access()
            self.test_card_data_isolation()
            
        except Exception as e:
            self.results.append(format_test_result(
                "测试环境设置",
                False,
                f"测试环境设置失败: {str(e)}"
            ))
            print(f"❌ 测试环境设置失败: {str(e)}")
        
        end_time = time.time()
        duration = end_time - start_time
        
        # 打印测试摘要
        print_test_summary(self.results)
        print(f"测试耗时: {duration:.2f}秒")
        
        return self.results

def main():
    """主函数"""
    test = CardRecordsCRUDTestSuite()
    results = test.run_all_tests()
    
    # 判断测试是否通过
    failed_tests = [r for r in results if not r.get("success", False)]
    
    if not failed_tests:
        print("\n🎉 卡记录管理测试全部通过！")
        return 0
    else:
        print(f"\n⚠️ 发现 {len(failed_tests)} 个问题需要修复")
        for test in failed_tests:
            print(f"  - {test['test_name']}: {test['message']}")
        return 1

if __name__ == "__main__":
    sys.exit(main())
