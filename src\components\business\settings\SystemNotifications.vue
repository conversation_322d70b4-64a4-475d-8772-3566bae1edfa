<template>
    <div class="system-notifications">
        <el-card class="box-card">
            <template #header>
                <div class="card-header">
                    <span>系统通知设置</span>
                    <div class="header-operations">
                        <el-button type="primary" @click="saveSettings">保存设置</el-button>
                        <el-button link v-if="merchantStore.isMerchantMode" @click="resetToGlobalSettings">重置为全局设置</el-button>
                    </div>
                </div>
            </template>

            <!-- 租户上下文提示 -->
            <div v-if="merchantStore.isMerchantMode" class="merchant-context-tip">
                <el-alert type="info" :closable="false">
                    正在编辑租户 "{{ merchantStore.currentMerchantName }}" 的通知设置
                </el-alert>
            </div>

            <el-form :model="notificationSettings" label-width="180px" :rules="rules" ref="formRef">
                <el-form-item label="开启系统通知" prop="enableNotifications">
                    <el-switch v-model="notificationSettings.enableNotifications" />
                </el-form-item>

                <el-form-item label="绑卡成功通知" prop="bindSuccessNotification">
                    <el-switch v-model="notificationSettings.bindSuccessNotification"
                        :disabled="!notificationSettings.enableNotifications" />
                </el-form-item>

                <el-form-item label="绑卡失败通知" prop="bindFailureNotification">
                    <el-switch v-model="notificationSettings.bindFailureNotification"
                        :disabled="!notificationSettings.enableNotifications" />
                </el-form-item>

                <el-form-item label="API异常通知" prop="apiExceptionNotification">
                    <el-switch v-model="notificationSettings.apiExceptionNotification"
                        :disabled="!notificationSettings.enableNotifications" />
                </el-form-item>

                <el-form-item label="商家配额警告" prop="quotaWarningNotification">
                    <el-switch v-model="notificationSettings.quotaWarningNotification"
                        :disabled="!notificationSettings.enableNotifications" />
                </el-form-item>

                <el-form-item label="配额警告阈值(%)" prop="quotaWarningThreshold"
                    v-if="notificationSettings.enableNotifications && notificationSettings.quotaWarningNotification">
                    <el-slider v-model="notificationSettings.quotaWarningThreshold" :min="50" :max="95" :step="5"
                        show-stops />
                </el-form-item>

                <el-form-item label="通知接收邮箱" prop="notificationEmail" v-if="notificationSettings.enableNotifications">
                    <el-input v-model="notificationSettings.notificationEmail" placeholder="<EMAIL>" />
                </el-form-item>

                <el-divider content-position="left">推送设置</el-divider>

                <el-form-item label="钉钉推送" prop="enableDingtalk">
                    <el-switch v-model="notificationSettings.enableDingtalk"
                        :disabled="!notificationSettings.enableNotifications" />
                </el-form-item>

                <el-form-item label="钉钉Webhook地址" prop="dingtalkWebhook"
                    v-if="notificationSettings.enableNotifications && notificationSettings.enableDingtalk">
                    <el-input v-model="notificationSettings.dingtalkWebhook" placeholder="请输入钉钉群机器人的Webhook地址" />
                </el-form-item>

                <el-form-item label="企业微信推送" prop="enableWecom">
                    <el-switch v-model="notificationSettings.enableWecom"
                        :disabled="!notificationSettings.enableNotifications" />
                </el-form-item>

                <el-form-item label="企业微信Webhook地址" prop="wecomWebhook"
                    v-if="notificationSettings.enableNotifications && notificationSettings.enableWecom">
                    <el-input v-model="notificationSettings.wecomWebhook" placeholder="请输入企业微信群机器人的Webhook地址" />
                </el-form-item>

                <el-divider content-position="left">通知模板</el-divider>

                <el-form-item label="绑卡成功模板" prop="bindSuccessTemplate"
                    v-if="notificationSettings.enableNotifications && notificationSettings.bindSuccessNotification">
                    <el-input v-model="notificationSettings.bindSuccessTemplate" type="textarea" :rows="3"
                        placeholder="使用 {{变量名}} 表示变量，如 {{merchantName}}、{{cardNumber}}等" />
                </el-form-item>

                <el-form-item label="绑卡失败模板" prop="bindFailureTemplate"
                    v-if="notificationSettings.enableNotifications && notificationSettings.bindFailureNotification">
                    <el-input v-model="notificationSettings.bindFailureTemplate" type="textarea" :rows="3"
                        placeholder="使用 {{变量名}} 表示变量，如 {{merchantName}}、{{failReason}}等" />
                </el-form-item>
            </el-form>

            <div v-if="notificationSettings.enableNotifications" class="test-notification">
                <el-divider content-position="left">测试通知</el-divider>
                <el-button type="primary" @click="sendTestNotification">发送测试通知</el-button>
                <div class="notification-preview">
                    <p class="preview-title">预览模板:</p>
                    <div class="preview-content">
                        <div v-if="notificationSettings.bindSuccessNotification" class="preview-item">
                            <h4>绑卡成功通知</h4>
                            <p>{{ renderTemplate(notificationSettings.bindSuccessTemplate) }}</p>
                        </div>
                    </div>
                </div>
            </div>
        </el-card>
    </div>
</template>

<script setup>
import { ref, reactive, onMounted, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useMerchantStore } from '../../store/merchant.js'
import axios from 'axios'
import { formatDateTime } from '@/utils/dateUtils'

// 获取租户Store
const merchantStore = useMerchantStore()

const formRef = ref(null)
const loading = ref(false)

// 全局设置和租户特定设置
const globalSettings = reactive({
    enableNotifications: true,
    bindSuccessNotification: true,
    bindFailureNotification: true,
    apiExceptionNotification: true,
    quotaWarningNotification: true,
    quotaWarningThreshold: 80,
    notificationEmail: '<EMAIL>',
    enableDingtalk: false,
    dingtalkWebhook: '',
    enableWecom: false,
    wecomWebhook: '',
    bindSuccessTemplate: '商家 {{merchantName}} 成功绑定沃尔玛卡，卡号: {{cardNumber}}，绑定时间: {{bindTime}}',
    bindFailureTemplate: '商家 {{merchantName}} 绑定沃尔玛卡失败，失败原因: {{failReason}}，时间: {{bindTime}}'
})

// 当前通知设置（会根据租户上下文变化）
const notificationSettings = reactive({ ...globalSettings })

// 租户特定设置缓存
const merchantSettingsCache = new Map()

// 验证规则
const rules = {
    notificationEmail: [
        { required: true, message: '请输入通知接收邮箱', trigger: 'blur' },
        { type: 'email', message: '请输入正确的邮箱格式', trigger: 'blur' }
    ],
    dingtalkWebhook: [
        { required: true, message: '请输入钉钉Webhook地址', trigger: 'blur' },
        { pattern: /^https:\/\/oapi\.dingtalk\.com\/robot\/send\?access_token=.+$/, message: '请输入正确的钉钉Webhook地址', trigger: 'blur' }
    ],
    wecomWebhook: [
        { required: true, message: '请输入企业微信Webhook地址', trigger: 'blur' },
        { pattern: /^https:\/\/qyapi\.weixin\.qq\.com\/cgi-bin\/webhook\/send\?key=.+$/, message: '请输入正确的企业微信Webhook地址', trigger: 'blur' }
    ],
    bindSuccessTemplate: [
        { required: true, message: '请输入绑卡成功通知模板', trigger: 'blur' }
    ],
    bindFailureTemplate: [
        { required: true, message: '请输入绑卡失败通知模板', trigger: 'blur' }
    ]
}

// 监听租户变化
watch(() => merchantStore.currentMerchant, async () => {
    // 保存当前设置到缓存
    saveToCacheIfNeeded()

    // 加载新租户的设置
    await loadSettingsForCurrentMerchant()
}, { immediate: false })

// 从当前租户中加载设置
async function loadSettingsForCurrentMerchant() {
    loading.value = true
    try {
        if (merchantStore.isMerchantMode) {
            // 尝试从缓存加载
            if (merchantSettingsCache.has(merchantStore.currentMerchantId)) {
                Object.assign(notificationSettings, merchantSettingsCache.get(merchantStore.currentMerchantId))
                loading.value = false
                return
            }

            // 模拟API调用获取租户特定设置
            setTimeout(() => {
                // 这里模拟从API获取特定租户的设置
                // 在实际应用中，应该调用API获取租户特定设置
                const merchantSpecificSettings = {
                    ...globalSettings,
                    // 修改一些值以区分
                    notificationEmail: `merchant-${merchantStore.currentMerchantId}@example.com`,
                    quotaWarningThreshold: 75,
                    bindSuccessTemplate: `租户 ${merchantStore.currentMerchantName} - 商家 {{merchantName}} 成功绑定沃尔玛卡，卡号: {{cardNumber}}，绑定时间: {{bindTime}}`
                }

                // 更新设置
                Object.assign(notificationSettings, merchantSpecificSettings)

                // 缓存设置
                merchantSettingsCache.set(merchantStore.currentMerchantId, { ...merchantSpecificSettings })

                loading.value = false
            }, 500)
        } else {
            // 全局视图，使用全局设置
            Object.assign(notificationSettings, globalSettings)
            loading.value = false
        }
    } catch (error) {
        console.error('加载通知设置失败:', error)
        ElMessage.error('加载通知设置失败')
        loading.value = false
    }
}

// 保存当前设置到缓存（如果是租户视图）
function saveToCacheIfNeeded() {
    if (merchantStore.isMerchantMode && merchantStore.currentMerchantId) {
        merchantSettingsCache.set(merchantStore.currentMerchantId, { ...notificationSettings })
    }
}

// 保存设置
const saveSettings = async () => {
    if (!formRef.value) return

    await formRef.value.validate(async (valid) => {
        if (valid) {
            loading.value = true
            try {
                // 这里添加API调用保存设置
                if (merchantStore.isMerchantMode) {
                    // 保存租户特定设置
                    await mockApiCall(`/api/v1/merchants/${merchantStore.currentMerchantId}/notification-settings`, notificationSettings)
                    // 更新缓存
                    merchantSettingsCache.set(merchantStore.currentMerchantId, { ...notificationSettings })
                    ElMessage.success(`租户 "${merchantStore.currentMerchantName}" 的通知设置已保存`)
                } else {
                    // 保存全局设置
                    await mockApiCall('/api/v1/notification-settings', notificationSettings)
                    // 更新全局设置
                    Object.assign(globalSettings, notificationSettings)
                    ElMessage.success('全局通知设置已保存')
                }
            } catch (error) {
                console.error('保存通知设置失败:', error)
                ElMessage.error('保存通知设置失败')
            } finally {
                loading.value = false
            }
        } else {
            ElMessage.error('请正确填写通知设置')
            return false
        }
    })
}

// 重置为全局设置
const resetToGlobalSettings = async () => {
    if (!merchantStore.isMerchantMode) return

    ElMessageBox.confirm(
        `确定要将租户 "${merchantStore.currentMerchantName}" 的通知设置重置为全局设置吗？`,
        '确认重置',
        {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
        }
    )
        .then(() => {
            // 重置为全局设置
            Object.assign(notificationSettings, globalSettings)

            // 更新缓存
            merchantSettingsCache.set(merchantStore.currentMerchantId, { ...globalSettings })

            ElMessage.success('已重置为全局设置')
        })
        .catch(() => {
            // 用户取消操作
        })
}

// 模拟API调用
function mockApiCall(url, data) {
    return new Promise((resolve) => {
        setTimeout(() => {
            resolve({ success: true, data })
        }, 500)
    })
}

// 渲染模板预览
const renderTemplate = (template) => {
    if (!template) return ''
    let result = template;

    // 通用变量
    result = result
        .replace('{{merchantName}}', '测试商家')
        .replace('{{cardNumber}}', '6012********3456')
        .replace('{{bindTime}}', formatDateTime(new Date()))
        .replace('{{failReason}}', '卡号无效')

    // 租户特定变量
    if (merchantStore.isMerchantMode) {
        result = result.replace('{{merchantName}}', merchantStore.currentMerchantName)
    }

    return result
}

// 发送测试通知
const sendTestNotification = () => {
    const target = merchantStore.isMerchantMode
        ? `租户 "${merchantStore.currentMerchantName}"`
        : "全局";

    ElMessage.success(`测试通知已发送至${target}配置的通知渠道`)
}

// 组件挂载时加载设置
onMounted(async () => {
    await loadSettingsForCurrentMerchant()
})
</script>

<style scoped>
.system-notifications {
    width: 100%;
}

.card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.header-operations {
    display: flex;
    gap: 10px;
}

.test-notification {
    margin-top: 20px;
}

.notification-preview {
    margin-top: 20px;
    padding: 15px;
    background-color: #f5f7fa;
    border-radius: 4px;
}

.preview-title {
    font-weight: bold;
    margin-bottom: 10px;
    color: #606266;
}

.preview-content {
    padding: 10px;
    background-color: white;
    border-radius: 4px;
    border: 1px solid #dcdfe6;
}

.preview-item {
    margin-bottom: 15px;
}

.preview-item h4 {
    margin-top: 0;
    color: #409EFF;
}

.preview-item p {
    white-space: pre-line;
    margin-bottom: 0;
}

.merchant-context-tip {
    margin-bottom: 15px;
}
</style>