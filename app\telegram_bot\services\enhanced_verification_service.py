"""
增强版Telegram用户验证服务
支持分级审批、自动验证、批量操作等生产环境功能
"""

import secrets
import string
from datetime import datetime, timedelta
from typing import Optional, Dict, Any, List
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_

from app.models.telegram_user import TelegramUser, VerificationStatus
from app.models.user import User
from app.models.department import Department
from app.models.audit import AuditLog, AuditEventType, AuditLevel
from app.models.base import local_now
from app.core.logging import get_logger
from ..config import BotConfig
from ..exceptions import InvalidTokenError, UserNotVerifiedError

logger = get_logger(__name__)


class EnhancedVerificationService:
    """增强版用户验证服务"""
    
    def __init__(self, db: Session, config: BotConfig):
        self.db = db
        self.config = config
        
    async def create_verification_request(
        self, 
        telegram_user_id: int,
        telegram_username: str = None,
        telegram_first_name: str = None,
        telegram_last_name: str = None,
        employee_id: str = None,
        email: str = None
    ) -> str:
        """
        创建验证请求（支持自动验证）
        
        Args:
            telegram_user_id: Telegram用户ID
            telegram_username: Telegram用户名
            telegram_first_name: Telegram名字
            telegram_last_name: Telegram姓氏
            employee_id: 员工ID（用于自动验证）
            email: 邮箱（用于自动验证）
            
        Returns:
            str: 验证令牌
        """
        try:
            # 检查用户是否已存在
            existing_user = self.db.query(TelegramUser).filter_by(
                telegram_user_id=telegram_user_id
            ).first()
            
            if existing_user:
                if existing_user.verification_status == VerificationStatus.VERIFIED.value:
                    raise ValueError("用户已验证")
                # 更新现有用户信息
                telegram_user = existing_user
            else:
                # 创建新用户
                telegram_user = TelegramUser(
                    telegram_user_id=telegram_user_id,
                    telegram_username=telegram_username,
                    telegram_first_name=telegram_first_name,
                    telegram_last_name=telegram_last_name
                )
                self.db.add(telegram_user)
            
            # 尝试自动验证
            auto_verified = await self._try_auto_verification(
                telegram_user, employee_id, email
            )
            
            if auto_verified:
                self.db.commit()
                await self._log_verification_event(
                    telegram_user.id, "auto_verified", 
                    {"employee_id": employee_id, "email": email}
                )
                return "AUTO_VERIFIED"
            
            # 生成验证令牌
            verification_token = telegram_user.generate_verification_token()
            
            self.db.commit()
            
            await self._log_verification_event(
                telegram_user.id, "verification_requested",
                {"employee_id": employee_id, "email": email}
            )
            
            logger.info(f"为用户 {telegram_user_id} 创建验证请求")
            return verification_token
            
        except Exception as e:
            logger.error(f"创建验证请求失败: {e}")
            self.db.rollback()
            raise
    
    async def _try_auto_verification(
        self, 
        telegram_user: TelegramUser, 
        employee_id: str = None, 
        email: str = None
    ) -> bool:
        """
        尝试自动验证用户
        
        Args:
            telegram_user: Telegram用户对象
            employee_id: 员工ID
            email: 邮箱
            
        Returns:
            bool: 是否自动验证成功
        """
        # 基于员工ID自动验证
        if employee_id:
            system_user = self.db.query(User).filter_by(
                employee_id=employee_id,
                is_active=True
            ).first()
            
            if system_user:
                telegram_user.verify_with_system_user(system_user.id)
                return True
        
        # 基于邮箱域名自动验证
        if email and self._is_trusted_email_domain(email):
            system_user = self.db.query(User).filter_by(
                email=email,
                is_active=True
            ).first()
            
            if system_user:
                telegram_user.verify_with_system_user(system_user.id)
                return True
        
        return False
    
    def _is_trusted_email_domain(self, email: str) -> bool:
        """检查是否为可信邮箱域名"""
        trusted_domains = self.config.get('auto_verify_domains', [])
        if not trusted_domains:
            return False
            
        domain = email.split('@')[-1].lower()
        return domain in trusted_domains
    
    async def verify_user_with_hierarchical_approval(
        self, 
        verification_token: str, 
        system_user_id: int,
        approver_id: int,
        approval_level: str = "department"
    ) -> bool:
        """
        分级审批验证用户
        
        Args:
            verification_token: 验证令牌
            system_user_id: 系统用户ID
            approver_id: 审批人ID
            approval_level: 审批级别（department/super_admin）
            
        Returns:
            bool: 验证是否成功
        """
        try:
            # 查找待验证用户
            telegram_user = self.db.query(TelegramUser).filter_by(
                verification_token=verification_token,
                verification_status=VerificationStatus.PENDING.value
            ).first()
            
            if not telegram_user:
                raise InvalidTokenError("验证令牌无效或已过期")
            
            # 验证系统用户存在
            system_user = self.db.query(User).filter_by(
                id=system_user_id,
                is_active=True
            ).first()
            
            if not system_user:
                raise ValueError("系统用户不存在或已禁用")
            
            # 验证审批权限
            approver = self.db.query(User).filter_by(id=approver_id).first()
            if not approver:
                raise ValueError("审批人不存在")
            
            # 检查审批权限
            if not await self._check_approval_permission(
                approver, system_user, approval_level
            ):
                raise ValueError("审批人没有权限审批此用户")
            
            # 执行验证
            telegram_user.verify_with_system_user(system_user_id)
            
            self.db.commit()
            
            await self._log_verification_event(
                telegram_user.id, "verified",
                {
                    "system_user_id": system_user_id,
                    "approver_id": approver_id,
                    "approval_level": approval_level
                }
            )
            
            logger.info(f"用户 {telegram_user.telegram_user_id} 验证成功")
            return True
            
        except Exception as e:
            logger.error(f"用户验证失败: {e}")
            self.db.rollback()
            raise
    
    async def _check_approval_permission(
        self, 
        approver: User, 
        target_user: User, 
        approval_level: str
    ) -> bool:
        """
        检查审批权限
        
        Args:
            approver: 审批人
            target_user: 目标用户
            approval_level: 审批级别
            
        Returns:
            bool: 是否有审批权限
        """
        # 超级管理员可以审批所有用户
        if approver.is_superuser:
            return True
        
        # 检查Telegram管理权限
        if not approver.has_permission("api:telegram:user:approve"):
            return False
        
        if approval_level == "department":
            # 部门级审批：只能审批同部门或下级部门用户
            return approver.can_manage_user_in_department(target_user)
        
        return False
    
    async def batch_verify_users(
        self, 
        verification_requests: List[Dict[str, Any]],
        approver_id: int
    ) -> Dict[str, Any]:
        """
        批量验证用户
        
        Args:
            verification_requests: 验证请求列表
            approver_id: 审批人ID
            
        Returns:
            Dict: 批量操作结果
        """
        results = {
            "success_count": 0,
            "failed_count": 0,
            "errors": []
        }
        
        for request in verification_requests:
            try:
                await self.verify_user_with_hierarchical_approval(
                    request["verification_token"],
                    request["system_user_id"],
                    approver_id,
                    request.get("approval_level", "department")
                )
                results["success_count"] += 1
                
            except Exception as e:
                results["failed_count"] += 1
                results["errors"].append({
                    "verification_token": request["verification_token"],
                    "error": str(e)
                })
        
        return results
    
    async def _log_verification_event(
        self, 
        telegram_user_id: int, 
        event_type: str, 
        details: Dict[str, Any]
    ):
        """记录验证事件到审计日志"""
        try:
            audit_log = AuditLog(
                event_type=AuditEventType.USER_VERIFICATION.value,
                level=AuditLevel.INFO.value,
                resource_type="telegram_user",
                resource_id=str(telegram_user_id),
                action=event_type,
                details=details,
                user_id=details.get("approver_id"),
                ip_address=details.get("ip_address"),
                user_agent=details.get("user_agent")
            )
            self.db.add(audit_log)
            self.db.commit()
            
        except Exception as e:
            logger.error(f"记录审计日志失败: {e}")
