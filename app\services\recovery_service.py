import logging
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional
from sqlalchemy.orm import Session
from sqlalchemy import and_

from app.models.card_record import CardRecord, CardStatus
from app.models.binding_log import LogLevel, LogType
from app.models.user import User
from app.services.binding_log_service import BindingLogService
from app.services.base_service import BaseService

from app.core.logging import get_logger

logger = get_logger("recovery_service")

class RecoveryService(BaseService):
    """恢复服务 - 处理卡在pending状态的绑卡请求，支持权限检查和数据隔离"""

    def __init__(self, db: Session):
        """初始化恢复服务"""
        super().__init__(CardRecord, db)
        self.pending_threshold_minutes = 30  # 默认30分钟视为卡住
        self.max_retry_count = 3  # 最大重试次数

    async def identify_stuck_requests(
        self, current_user: User, hours_threshold: int = 24
    ) -> List[CardRecord]:
        """
        识别卡住的请求（支持权限检查和数据隔离）

        Args:
            current_user: 当前用户
            hours_threshold: 查找多少小时内的请求，默认24小时

        Returns:
            List[CardRecord]: 卡住的请求列表
        """
        try:
            # 计算时间阈值
            time_threshold = datetime.now() - timedelta(hours=hours_threshold)
            pending_minutes_threshold = datetime.now() - timedelta(minutes=self.pending_threshold_minutes)

            # 构建基础查询
            query = self.db.query(CardRecord).filter(
                and_(
                    CardRecord.status == CardStatus.PENDING,
                    CardRecord.created_at >= time_threshold,
                    CardRecord.updated_at <= pending_minutes_threshold,
                    CardRecord.retry_count < self.max_retry_count
                )
            )

            # 应用数据隔离
            query = self.apply_data_isolation(query, current_user)

            stuck_requests = query.all()

            logger.info(f"用户 {current_user.username} 找到{len(stuck_requests)}个卡住的绑卡请求")
            return stuck_requests

        except Exception as e:
            logger.error(f"识别卡住请求失败: {str(e)}", exc_info=True)
            return []

    async def reprocess_request(self, current_user: User, record: CardRecord) -> bool:
        """
        重新处理单个卡住的请求（支持权限检查）

        Args:
            current_user: 当前用户
            record: 卡记录对象

        Returns:
            bool: 是否成功重新提交到队列
        """
        try:
            # 权限检查：验证用户是否可以访问该记录
            if not self._can_access_record(current_user, record):
                logger.warning(f"用户 {current_user.username} 无权限访问记录 {record.id}")
                return False

            # 增加重试计数
            record.retry_count += 1

            # 重置卡状态为pending，准备重新绑卡
            record.status = 'pending'
            record.error_message = None
            record.updated_at = datetime.now()

            # 清除之前的绑卡结果，重置为初始状态（遵循正确的业务逻辑）
            # 这些字段将在重新绑卡成功后填入新的值
            record.department_id = None  # 重置为NULL，等待重新绑卡时填入
            record.walmart_ck_id = None  # 重置为NULL，等待重新绑卡时填入
            record.response_data = None
            record.process_time = None

            # 记录重试日志
            # 【安全修复】创建BindingLogService实例
            binding_log_service = BindingLogService(self.db)
            await binding_log_service.log_system(
                db=self.db,
                card_record_id=str(record.id),
                message=f"恢复服务：重新处理卡住的请求 (第{record.retry_count}次重试) - 操作员: {current_user.username}",
                log_level=LogLevel.WARNING,
                details={
                    "recovery_time": datetime.now().isoformat(),
                    "original_created_at": record.created_at.isoformat(),
                    "stuck_duration_minutes": int((datetime.now() - record.updated_at).total_seconds() / 60),
                    "trace_id": record.trace_id,
                    "operator_id": current_user.id,
                    "operator_username": current_user.username,
                }
            )

            # 更新数据库
            self.db.add(record)
            self.db.commit()

            # 重新提交到绑卡队列
            success = await self._resubmit_to_queue(record, current_user)

            if success:
                logger.info(f"用户 {current_user.username} 已重新提交请求到队列: record_id={record.id}, trace_id={record.trace_id}")

            return success

        except Exception as e:
            logger.error(f"重新处理请求失败: record_id={record.id}, error={str(e)}", exc_info=True)
            # 记录错误日志
            # 【安全修复】创建BindingLogService实例
            binding_log_service = BindingLogService(self.db)
            await binding_log_service.log_error(
                db=self.db,
                card_record_id=str(record.id),
                error_message=f"恢复服务：重新处理请求失败: {str(e)}",
                details={
                    "trace_id": record.trace_id,
                    "operator_id": current_user.id,
                    "operator_username": current_user.username,
                }
            )
            return False

    def _can_access_record(self, current_user: User, record: CardRecord) -> bool:
        """
        检查用户是否可以访问指定的卡记录

        Args:
            current_user: 当前用户
            record: 卡记录对象

        Returns:
            bool: 是否可以访问
        """
        try:
            # 超级管理员可以访问所有记录
            if current_user.is_superuser:
                return True

            # 商户级数据隔离：用户只能访问自己商户的数据
            if record.merchant_id is not None and current_user.merchant_id is not None:
                if record.merchant_id != current_user.merchant_id:
                    return False
            elif record.merchant_id is not None and current_user.merchant_id is None:
                # 记录有商户ID但用户没有商户ID，不能访问
                return False
            elif record.merchant_id is None and current_user.merchant_id is not None:
                # 记录没有商户ID但用户有商户ID，不能访问
                return False

            # 部门级数据隔离：如果记录有部门ID，检查用户是否可以访问该部门
            if record.department_id is not None and current_user.department_id is not None:
                # 简化版本：用户只能访问自己部门的数据
                # 在实际应用中，可以通过权限服务检查更复杂的部门访问权限
                if record.department_id != current_user.department_id:
                    return False
            elif record.department_id is not None and current_user.department_id is None:
                # 记录有部门ID但用户没有部门ID，可以访问（商户级用户）
                pass
            elif record.department_id is None and current_user.department_id is not None:
                # 记录没有部门ID但用户有部门ID，可以访问（记录可能是商户级的）
                pass

            return True

        except Exception as e:
            logger.error(f"权限检查失败: {str(e)}", exc_info=True)
            return False

    async def _resubmit_to_queue(self, record: CardRecord, current_user: User) -> bool:
        """
        重新提交记录到绑卡队列（公共方法）

        Args:
            record: 卡记录对象
            current_user: 当前用户

        Returns:
            bool: 是否成功提交到队列
        """
        try:
            from app.utils.queue_producer import send_bind_card_task

            # 构建队列消息（使用简化的消息格式）
            # 注意：恢复服务不需要重新传递密码，队列处理器会从数据库获取
            message = {
                "record_id": str(record.id),
                "merchant_id": record.merchant_id,
                "trace_id": record.trace_id or str(record.id),
                "is_recovery": True,  # 标记为恢复处理
                "recovery_attempt": record.retry_count,
                "operator_id": current_user.id,
                "operator_username": current_user.username,
            }

            # 发送到队列
            await send_bind_card_task(message)
            return True

        except Exception as e:
            logger.error(f"重新提交到队列失败: record_id={record.id}, error={str(e)}", exc_info=True)
            return False

    async def batch_reprocess_stuck_requests(
        self, current_user: User, hours_threshold: int = 24, batch_size: int = 50
    ) -> Dict[str, Any]:
        """
        批量重新处理卡住的请求（支持权限检查和数据隔离）

        Args:
            current_user: 当前用户
            hours_threshold: 查找多少小时内的请求，默认24小时
            batch_size: 每批处理的请求数量，默认50

        Returns:
            Dict[str, Any]: 处理结果统计
        """
        try:
            # 识别卡住的请求
            stuck_requests = await self.identify_stuck_requests(current_user, hours_threshold)

            if not stuck_requests:
                logger.info(f"用户 {current_user.username} 没有找到需要恢复的卡住请求")
                return {
                    "total": 0,
                    "success": 0,
                    "failed": 0,
                    "details": [],
                    "operator": current_user.username
                }

            # 处理结果统计
            results = {
                "total": len(stuck_requests),
                "success": 0,
                "failed": 0,
                "details": [],
                "operator": current_user.username,
                "start_time": datetime.now().isoformat()
            }

            # 分批处理
            for i in range(0, len(stuck_requests), batch_size):
                batch = stuck_requests[i:i+batch_size]

                for record in batch:
                    success = await self.reprocess_request(current_user, record)

                    if success:
                        results["success"] += 1
                    else:
                        results["failed"] += 1

                    results["details"].append({
                        "record_id": str(record.id),
                        "merchant_id": record.merchant_id,
                        "card_number": f"{record.card_number[:6]}***" if record.card_number else "N/A",
                        "created_at": record.created_at.isoformat() if record.created_at else None,
                        "success": success,
                        "retry_count": record.retry_count
                    })

            results["end_time"] = datetime.now().isoformat()
            logger.info(f"用户 {current_user.username} 批量恢复处理完成: 总数={results['total']}, 成功={results['success']}, 失败={results['failed']}")
            return results

        except Exception as e:
            logger.error(f"批量恢复处理失败: {str(e)}", exc_info=True)
            return {
                "total": 0,
                "success": 0,
                "failed": 0,
                "details": [],
                "operator": current_user.username,
                "error": str(e)
            }

    async def notify_merchants(self, current_user: User, recovery_results: Dict[str, Any]) -> None:
        """
        通知相关商家他们的请求已被重新处理（支持权限检查）

        Args:
            current_user: 当前用户
            recovery_results: 恢复处理结果
        """
        try:
            # 按商家ID分组结果
            merchant_results = {}

            for detail in recovery_results["details"]:
                merchant_id = detail["merchant_id"]

                # 权限检查：用户只能通知自己有权限访问的商户
                if not self._can_notify_merchant(current_user, merchant_id):
                    logger.warning(f"用户 {current_user.username} 无权限通知商户 {merchant_id}")
                    continue

                if merchant_id not in merchant_results:
                    merchant_results[merchant_id] = {
                        "total": 0,
                        "success": 0,
                        "failed": 0,
                        "records": []
                    }

                merchant_results[merchant_id]["total"] += 1
                if detail["success"]:
                    merchant_results[merchant_id]["success"] += 1
                else:
                    merchant_results[merchant_id]["failed"] += 1

                merchant_results[merchant_id]["records"].append(detail)

            # 为每个商家创建通知
            for merchant_id, result in merchant_results.items():
                try:
                    # 这里可以实现具体的通知逻辑，如发送邮件、短信等
                    # 为简化示例，这里只记录日志
                    logger.info(f"用户 {current_user.username} 通知商家 {merchant_id}: 总数={result['total']}, 成功={result['success']}, 失败={result['failed']}")

                    # 记录系统日志
                    # 【安全修复】创建BindingLogService实例
                    binding_log_service = BindingLogService(self.db)
                    for record in result["records"]:
                        await binding_log_service.log_system(
                            db=self.db,
                            card_record_id=record["record_id"],
                            message=f"恢复服务：已通知商家关于重新处理的结果 - 操作员: {current_user.username}",
                            log_level=LogLevel.INFO,
                            details={
                                "notification_time": datetime.now().isoformat(),
                                "merchant_id": merchant_id,
                                "success": record["success"],
                                "operator_id": current_user.id,
                                "operator_username": current_user.username,
                            }
                        )
                except Exception as e:
                    logger.error(f"通知商家失败: merchant_id={merchant_id}, error={str(e)}", exc_info=True)

        except Exception as e:
            logger.error(f"通知商家处理失败: {str(e)}", exc_info=True)

    def _can_notify_merchant(self, current_user: User, merchant_id: int) -> bool:
        """
        检查用户是否可以通知指定商户

        Args:
            current_user: 当前用户
            merchant_id: 商户ID

        Returns:
            bool: 是否可以通知
        """
        try:
            # 超级管理员可以通知所有商户
            if current_user.is_superuser:
                return True

            # 商户用户只能通知自己的商户
            return current_user.merchant_id == merchant_id

        except Exception as e:
            logger.error(f"检查通知权限失败: {str(e)}", exc_info=True)
            return False

    async def get_recovery_statistics(self, current_user: User, hours_threshold: int = 24) -> Dict[str, Any]:
        """
        获取恢复统计信息（支持权限检查和数据隔离）

        Args:
            current_user: 当前用户
            hours_threshold: 查找多少小时内的请求，默认24小时

        Returns:
            Dict[str, Any]: 统计信息
        """
        try:
            # 识别卡住的请求
            stuck_requests = await self.identify_stuck_requests(current_user, hours_threshold)

            # 统计信息
            stats = {
                "total_stuck": len(stuck_requests),
                "by_merchant": {},
                "by_retry_count": {},
                "oldest_stuck_time": None,
                "newest_stuck_time": None,
                "operator": current_user.username,
                "query_time": datetime.now().isoformat()
            }

            if stuck_requests:
                # 按商户统计
                for record in stuck_requests:
                    merchant_id = record.merchant_id
                    if merchant_id not in stats["by_merchant"]:
                        stats["by_merchant"][merchant_id] = 0
                    stats["by_merchant"][merchant_id] += 1

                # 按重试次数统计
                for record in stuck_requests:
                    retry_count = record.retry_count
                    if retry_count not in stats["by_retry_count"]:
                        stats["by_retry_count"][retry_count] = 0
                    stats["by_retry_count"][retry_count] += 1

                # 时间统计
                created_times = [r.created_at for r in stuck_requests if r.created_at]
                if created_times:
                    stats["oldest_stuck_time"] = min(created_times).isoformat()
                    stats["newest_stuck_time"] = max(created_times).isoformat()

            return stats

        except Exception as e:
            logger.error(f"获取恢复统计信息失败: {str(e)}", exc_info=True)
            return {
                "total_stuck": 0,
                "by_merchant": {},
                "by_retry_count": {},
                "operator": current_user.username,
                "error": str(e)
            }

    def update_configuration(self, pending_threshold_minutes: int = None, max_retry_count: int = None) -> Dict[str, Any]:
        """
        更新恢复服务配置

        Args:
            pending_threshold_minutes: 新的pending阈值（分钟）
            max_retry_count: 新的最大重试次数

        Returns:
            Dict[str, Any]: 更新结果
        """
        try:
            old_config = {
                "pending_threshold_minutes": self.pending_threshold_minutes,
                "max_retry_count": self.max_retry_count
            }

            if pending_threshold_minutes is not None:
                self.pending_threshold_minutes = pending_threshold_minutes

            if max_retry_count is not None:
                self.max_retry_count = max_retry_count

            new_config = {
                "pending_threshold_minutes": self.pending_threshold_minutes,
                "max_retry_count": self.max_retry_count
            }

            logger.info(f"恢复服务配置已更新: {old_config} -> {new_config}")

            return {
                "success": True,
                "old_config": old_config,
                "new_config": new_config,
                "update_time": datetime.now().isoformat()
            }

        except Exception as e:
            logger.error(f"更新恢复服务配置失败: {str(e)}", exc_info=True)
            return {
                "success": False,
                "error": str(e)
            }


def create_recovery_service(db: Session) -> RecoveryService:
    """
    创建恢复服务实例的工厂函数

    Args:
        db: 数据库会话

    Returns:
        RecoveryService: 恢复服务实例
    """
    return RecoveryService(db)
