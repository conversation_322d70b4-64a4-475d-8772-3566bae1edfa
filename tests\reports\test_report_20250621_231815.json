{"timestamp": "20250621_231815", "total_modules": 12, "passed_modules": 0, "failed_modules": 12, "success_rate": 0.0, "duration": 9.504069805145264, "results": [{"module": "认证模块测试", "status": "FAIL", "message": "Traceback (most recent call last):\n  File \"D:\\Document\\Code\\YuXinYang\\python\\walmart\\walmart-bind-card-server\\test\\auth\\test_auth.py\", line 252, in <module>\n    sys.exit(main())\n             ^^^^^^\n  "}, {"module": "用户管理测试", "status": "FAIL", "message": "Traceback (most recent call last):\n  File \"D:\\Document\\Code\\YuXinYang\\python\\walmart\\walmart-bind-card-server\\test\\users\\test_users_crud.py\", line 389, in <module>\n    sys.exit(main())\n             ^^"}, {"module": "商户管理测试", "status": "FAIL", "message": "Traceback (most recent call last):\n  File \"D:\\Document\\Code\\YuXinYang\\python\\walmart\\walmart-bind-card-server\\test\\merchants\\test_merchants_crud.py\", line 394, in <module>\n    sys.exit(main())\n       "}, {"module": "部门管理测试", "status": "FAIL", "message": "Traceback (most recent call last):\n  File \"D:\\Document\\Code\\YuXinYang\\python\\walmart\\walmart-bind-card-server\\test\\departments\\test_departments_crud.py\", line 460, in <module>\n    sys.exit(main())\n   "}, {"module": "角色权限测试", "status": "FAIL", "message": "Traceback (most recent call last):\n  File \"D:\\Document\\Code\\YuXinYang\\python\\walmart\\walmart-bind-card-server\\test\\roles\\test_roles_permissions.py\", line 653, in <module>\n    sys.exit(main())\n        "}, {"module": "绑卡业务测试", "status": "FAIL", "message": "Traceback (most recent call last):\n  File \"D:\\Document\\Code\\YuXinYang\\python\\walmart\\walmart-bind-card-server\\test\\cards\\test_cards_crud.py\", line 339, in <module>\n    sys.exit(main())\n             ^^"}, {"module": "沃尔玛CK测试", "status": "FAIL", "message": "Traceback (most recent call last):\n  File \"D:\\Document\\Code\\YuXinYang\\python\\walmart\\walmart-bind-card-server\\test\\walmart_ck\\test_walmart_ck_crud.py\", line 406, in <module>\n    sys.exit(main())\n     "}, {"module": "通知管理测试", "status": "FAIL", "message": "Traceback (most recent call last):\n  File \"D:\\Document\\Code\\YuXinYang\\python\\walmart\\walmart-bind-card-server\\test\\notifications\\test_notifications_crud.py\", line 421, in <module>\n    sys.exit(main())"}, {"module": "仪表盘测试", "status": "FAIL", "message": "Traceback (most recent call last):\n  File \"D:\\Document\\Code\\YuXinYang\\python\\walmart\\walmart-bind-card-server\\test\\dashboard\\test_dashboard.py\", line 400, in <module>\n    sys.exit(main())\n            "}, {"module": "数据隔离测试", "status": "FAIL", "message": "Traceback (most recent call last):\n  File \"D:\\Document\\Code\\YuXinYang\\python\\walmart\\walmart-bind-card-server\\test\\security\\test_data_isolation.py\", line 367, in <module>\n    sys.exit(main())\n        "}, {"module": "API安全测试", "status": "FAIL", "message": "Traceback (most recent call last):\n  File \"D:\\Document\\Code\\YuXinYang\\python\\walmart\\walmart-bind-card-server\\test\\security\\test_api_security.py\", line 399, in <module>\n    sys.exit(main())\n          "}, {"module": "跨边界操作测试", "status": "FAIL", "message": "Traceback (most recent call last):\n  File \"D:\\Document\\Code\\YuXinYang\\python\\walmart\\walmart-bind-card-server\\test\\security\\test_cross_boundary_operations.py\", line 480, in <module>\n    sys.exit(main("}]}