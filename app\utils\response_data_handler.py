#!/usr/bin/env python3
"""
响应数据处理工具
用于处理沃尔玛API原始响应数据的大小限制和容错机制
"""

import json
import gzip
import base64
import os
from typing import Dict, Any, Optional, Tuple
from datetime import datetime

from app.core.logging import get_logger

logger = get_logger(__name__)

# 数据库字段大小限制（基于MySQL JSON类型）
# MySQL JSON类型理论上限制为4GB，但实际建议不超过16MB
MAX_JSON_SIZE = 16 * 1024 * 1024  # 16MB
MAX_SAFE_JSON_SIZE = 1 * 1024 * 1024  # 1MB 安全限制
MAX_COMPRESSED_SIZE = 512 * 1024  # 512KB 压缩后限制

# 关键字段优先级（数字越大优先级越高）
CRITICAL_FIELDS_PRIORITY = {
    "status": 100,
    "error": 90,
    "errorcode": 85,
    "logId": 80,
    "message": 75,
    "msg": 70,
    "code": 65,
    "data": 60,
    "timestamp": 55,
    "success": 50,
}

# 大文件存储目录
LARGE_RESPONSE_DIR = "logs/large_responses"


class ResponseDataHandler:
    """响应数据处理器"""
    
    def __init__(self):
        self.ensure_large_response_dir()
    
    def ensure_large_response_dir(self):
        """确保大文件存储目录存在"""
        try:
            os.makedirs(LARGE_RESPONSE_DIR, exist_ok=True)
        except Exception as e:
            logger.warning(f"创建大文件存储目录失败: {e}")
    
    def process_response_data(
        self, 
        response_data: Optional[Dict[str, Any]], 
        context: str = "unknown"
    ) -> Tuple[Optional[Dict[str, Any]], Optional[str]]:
        """
        处理响应数据，确保符合数据库存储要求
        
        Args:
            response_data: 原始响应数据
            context: 上下文信息，用于日志和文件命名
            
        Returns:
            Tuple[处理后的响应数据, 警告信息]
        """
        if response_data is None:
            return None, None
        
        try:
            # 1. 计算原始数据大小
            original_json = json.dumps(response_data, ensure_ascii=False, separators=(',', ':'))
            original_size = len(original_json.encode('utf-8'))
            
            logger.debug(f"原始响应数据大小: {original_size} bytes | context: {context}")
            
            # 2. 如果数据在安全范围内，直接返回
            if original_size <= MAX_SAFE_JSON_SIZE:
                return response_data, None
            
            # 3. 数据过大，需要处理
            logger.warning(f"响应数据过大 ({original_size} bytes)，开始处理 | context: {context}")
            
            # 4. 尝试压缩存储
            compressed_data, compression_warning = self._try_compression(response_data, context)
            if compressed_data:
                return compressed_data, compression_warning
            
            # 5. 压缩失败，尝试关键字段提取
            critical_data, extraction_warning = self._extract_critical_fields(response_data, context)
            if critical_data:
                return critical_data, extraction_warning
            
            # 6. 关键字段提取失败，尝试文件存储
            file_ref_data, file_warning = self._store_to_file(response_data, context)
            if file_ref_data:
                return file_ref_data, file_warning
            
            # 7. 所有方法都失败，返回最小化数据
            fallback_data = self._create_fallback_data(response_data, context)
            return fallback_data, "响应数据过大，已保存最小化版本"
            
        except Exception as e:
            logger.error(f"处理响应数据时发生错误: {e} | context: {context}")
            # 发生错误时返回最小化数据
            fallback_data = self._create_fallback_data(response_data, context)
            return fallback_data, f"处理响应数据时发生错误: {str(e)}"
    
    def _try_compression(
        self, 
        response_data: Dict[str, Any], 
        context: str
    ) -> Tuple[Optional[Dict[str, Any]], Optional[str]]:
        """尝试压缩数据"""
        try:
            # 压缩原始JSON
            original_json = json.dumps(response_data, ensure_ascii=False, separators=(',', ':'))
            compressed_bytes = gzip.compress(original_json.encode('utf-8'))
            
            # 检查压缩后大小
            if len(compressed_bytes) <= MAX_COMPRESSED_SIZE:
                # 压缩成功，创建压缩数据引用
                compressed_b64 = base64.b64encode(compressed_bytes).decode('ascii')
                
                compressed_data = {
                    "_compressed": True,
                    "_original_size": len(original_json.encode('utf-8')),
                    "_compressed_size": len(compressed_bytes),
                    "_compression_ratio": round(len(compressed_bytes) / len(original_json.encode('utf-8')), 3),
                    "_data": compressed_b64,
                    "_context": context,
                    "_timestamp": datetime.now().isoformat(),
                    # 保留关键字段用于快速查看
                    **self._extract_critical_fields_only(response_data)
                }
                
                warning = f"响应数据已压缩存储 (原始: {len(original_json.encode('utf-8'))} bytes, 压缩后: {len(compressed_bytes)} bytes)"
                logger.info(f"{warning} | context: {context}")
                return compressed_data, warning
            
            logger.debug(f"压缩后数据仍然过大 ({len(compressed_bytes)} bytes) | context: {context}")
            return None, None
            
        except Exception as e:
            logger.warning(f"数据压缩失败: {e} | context: {context}")
            return None, None
    
    def _extract_critical_fields(
        self, 
        response_data: Dict[str, Any], 
        context: str
    ) -> Tuple[Optional[Dict[str, Any]], Optional[str]]:
        """提取关键字段"""
        try:
            critical_data = self._extract_critical_fields_only(response_data)
            
            # 添加截断标记
            critical_data.update({
                "_truncated": True,
                "_original_keys": list(response_data.keys()),
                "_truncated_reason": "数据过大，仅保留关键字段",
                "_context": context,
                "_timestamp": datetime.now().isoformat()
            })
            
            # 检查关键字段数据大小
            critical_json = json.dumps(critical_data, ensure_ascii=False, separators=(',', ':'))
            critical_size = len(critical_json.encode('utf-8'))
            
            if critical_size <= MAX_SAFE_JSON_SIZE:
                warning = f"响应数据已截断，仅保留关键字段 (截断后: {critical_size} bytes)"
                logger.info(f"{warning} | context: {context}")
                return critical_data, warning
            
            logger.warning(f"关键字段数据仍然过大 ({critical_size} bytes) | context: {context}")
            return None, None
            
        except Exception as e:
            logger.warning(f"关键字段提取失败: {e} | context: {context}")
            return None, None
    
    def _extract_critical_fields_only(self, response_data: Dict[str, Any]) -> Dict[str, Any]:
        """仅提取关键字段"""
        critical_data = {}
        
        # 按优先级提取关键字段
        for field, priority in sorted(CRITICAL_FIELDS_PRIORITY.items(), key=lambda x: x[1], reverse=True):
            if field in response_data:
                critical_data[field] = response_data[field]
        
        return critical_data
    
    def _store_to_file(
        self, 
        response_data: Dict[str, Any], 
        context: str
    ) -> Tuple[Optional[Dict[str, Any]], Optional[str]]:
        """将数据存储到文件"""
        try:
            # 生成文件名
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S_%f")
            filename = f"response_{context}_{timestamp}.json"
            filepath = os.path.join(LARGE_RESPONSE_DIR, filename)
            
            # 写入文件
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(response_data, f, ensure_ascii=False, indent=2)
            
            # 创建文件引用数据
            file_ref_data = {
                "_stored_in_file": True,
                "_file_path": filepath,
                "_file_name": filename,
                "_context": context,
                "_timestamp": datetime.now().isoformat(),
                # 保留关键字段用于快速查看
                **self._extract_critical_fields_only(response_data)
            }
            
            warning = f"响应数据已存储到文件: {filepath}"
            logger.info(f"{warning} | context: {context}")
            return file_ref_data, warning
            
        except Exception as e:
            logger.error(f"文件存储失败: {e} | context: {context}")
            return None, None
    
    def _create_fallback_data(self, response_data: Dict[str, Any], context: str) -> Dict[str, Any]:
        """创建最小化的回退数据"""
        try:
            # 尝试提取最基本的信息
            fallback_data = {
                "_fallback": True,
                "_context": context,
                "_timestamp": datetime.now().isoformat(),
                "_original_type": type(response_data).__name__,
            }
            
            # 尝试添加最关键的字段
            if isinstance(response_data, dict):
                fallback_data["_original_keys"] = list(response_data.keys())[:10]  # 最多10个键名
                
                # 尝试保留最重要的字段
                for field in ["status", "error", "errorcode", "message"]:
                    if field in response_data:
                        try:
                            field_str = str(response_data[field])[:200]  # 限制长度
                            fallback_data[field] = field_str
                        except:
                            fallback_data[field] = "[无法序列化]"
            
            return fallback_data
            
        except Exception as e:
            logger.error(f"创建回退数据失败: {e} | context: {context}")
            return {
                "_fallback": True,
                "_error": str(e),
                "_context": context,
                "_timestamp": datetime.now().isoformat()
            }
    
    def decompress_response_data(self, compressed_data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """解压缩响应数据"""
        try:
            if not compressed_data.get("_compressed"):
                return compressed_data
            
            compressed_b64 = compressed_data.get("_data")
            if not compressed_b64:
                logger.warning("压缩数据中缺少_data字段")
                return compressed_data
            
            # 解压缩
            compressed_bytes = base64.b64decode(compressed_b64)
            original_json = gzip.decompress(compressed_bytes).decode('utf-8')
            original_data = json.loads(original_json)
            
            logger.info(f"成功解压缩响应数据 (原始大小: {compressed_data.get('_original_size')} bytes)")
            return original_data
            
        except Exception as e:
            logger.error(f"解压缩响应数据失败: {e}")
            return compressed_data
    
    def read_file_response_data(self, file_ref_data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """从文件读取响应数据"""
        try:
            if not file_ref_data.get("_stored_in_file"):
                return file_ref_data
            
            filepath = file_ref_data.get("_file_path")
            if not filepath or not os.path.exists(filepath):
                logger.warning(f"响应数据文件不存在: {filepath}")
                return file_ref_data
            
            # 读取文件
            with open(filepath, 'r', encoding='utf-8') as f:
                original_data = json.load(f)
            
            logger.info(f"成功从文件读取响应数据: {filepath}")
            return original_data
            
        except Exception as e:
            logger.error(f"从文件读取响应数据失败: {e}")
            return file_ref_data


# 全局实例
response_data_handler = ResponseDataHandler()
