"""
CK负载均衡监控API端点
"""

from datetime import datetime
from typing import Dict, Any, Optional
from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session

from app.api import deps
from app.models.user import User
from app.services.ck_load_balance_monitor import CKLoadBalanceMonitor
from app.services.simplified_ck_service import SimplifiedCKService
from app.core.logging import get_logger

logger = get_logger("ck_monitor_api")

router = APIRouter()


async def _collect_ck_diagnosis(db: Session, merchant_id: int) -> Dict[str, Any]:
    """
    收集CK诊断信息

    Args:
        db: 数据库会话
        merchant_id: 商户ID

    Returns:
        Dict: 诊断信息
    """
    from app.models.walmart_ck import WalmartCK
    from app.models.department import Department

    # 统计部门信息
    departments = db.query(Department).filter(
        Department.merchant_id == merchant_id
    ).all()

    dept_stats = {
        "total_departments": len(departments),
        "active_departments": len([d for d in departments if d.status]),
        "binding_enabled_departments": len([d for d in departments if d.status and d.enable_binding]),
        "departments_with_weight": len([d for d in departments if d.status and d.enable_binding and d.binding_weight > 0])
    }

    # 统计CK信息
    cks = db.query(WalmartCK).filter(
        WalmartCK.merchant_id == merchant_id,
        WalmartCK.is_deleted == False
    ).all()

    ck_stats = {
        "total_cks": len(cks),
        "active_cks": len([ck for ck in cks if ck.active]),
        "available_cks": len([ck for ck in cks if ck.active and ck.bind_count < ck.total_limit]),
        "exhausted_cks": len([ck for ck in cks if ck.bind_count >= ck.total_limit]),
        "disabled_cks": len([ck for ck in cks if not ck.active])
    }

    # 分析可用性
    availability_analysis = _analyze_ck_availability(departments, cks)

    return {
        "department_stats": dept_stats,
        "ck_stats": ck_stats,
        "availability_analysis": availability_analysis
    }


def _analyze_ck_availability(departments: list, cks: list) -> Dict[str, Any]:
    """
    分析CK可用性问题

    Args:
        departments: 部门列表
        cks: CK列表

    Returns:
        Dict: 可用性分析结果
    """
    issues = []
    issue_type = "none"

    # 检查是否有部门
    if not departments:
        issues.append("商户没有任何部门")
        issue_type = "configuration_error"

    # 检查是否有CK
    elif not cks:
        issues.append("商户没有任何CK")
        issue_type = "configuration_error"

    else:
        # 检查部门配置
        active_depts = [d for d in departments if d.status]
        binding_enabled_depts = [d for d in active_depts if d.enable_binding]
        weighted_depts = [d for d in binding_enabled_depts if d.binding_weight > 0]

        if not active_depts:
            issues.append("所有部门都被禁用")
            issue_type = "business_configuration"
        elif not binding_enabled_depts:
            issues.append("所有部门都被禁止绑卡")
            issue_type = "business_configuration"
        elif not weighted_depts:
            issues.append("所有部门的绑卡权重都为0")
            issue_type = "configuration_error"

        # 检查CK状态
        active_cks = [ck for ck in cks if ck.active]
        available_cks = [ck for ck in active_cks if ck.bind_count < ck.total_limit]

        if not active_cks:
            issues.append("所有CK都被禁用")
            issue_type = "configuration_error"
        elif not available_cks:
            issues.append("所有CK都已达到使用限制")
            issue_type = "capacity_limit"

        # 检查部门-CK匹配
        if binding_enabled_depts and available_cks:
            # 检查是否有可用部门有可用CK
            dept_ids = {d.id for d in binding_enabled_depts}
            available_ck_depts = {ck.department_id for ck in available_cks}

            if not dept_ids.intersection(available_ck_depts):
                issues.append("启用绑卡的部门没有可用的CK")
                issue_type = "configuration_mismatch"

    return {
        "issues": issues,
        "issue_type": issue_type,
        "has_issues": len(issues) > 0
    }


def _generate_smart_recommendation(test_results: Dict[str, Any]) -> str:
    """
    根据测试结果和诊断信息生成智能建议

    Args:
        test_results: 测试结果

    Returns:
        str: 智能建议
    """
    if test_results["is_balanced"]:
        return "✅ 负载均衡正常，CK分布均匀"

    diagnosis = test_results.get("diagnosis", {})
    availability = diagnosis.get("availability_analysis", {})
    issue_type = availability.get("issue_type", "unknown")
    issues = availability.get("issues", [])

    if issue_type == "business_configuration":
        if "所有部门都被禁止绑卡" in issues:
            return "ℹ️ 所有部门都被禁止绑卡，这是业务配置。如需测试负载均衡，请临时启用部门绑卡功能"
        elif "所有部门都被禁用" in issues:
            return "ℹ️ 所有部门都被禁用，这是业务配置。如需测试负载均衡，请临时启用部门"

    elif issue_type == "capacity_limit":
        return "⚠️ 所有CK都已达到使用限制，建议增加CK数量或提高现有CK的限制"

    elif issue_type == "configuration_error":
        if "所有CK都被禁用" in issues:
            return "❌ 所有CK都被禁用，请检查CK状态配置"
        elif "所有部门的绑卡权重都为0" in issues:
            return "❌ 所有部门的绑卡权重都为0，请设置部门权重"
        elif "商户没有任何CK" in issues:
            return "❌ 商户没有任何CK，请先添加CK"
        elif "商户没有任何部门" in issues:
            return "❌ 商户没有任何部门，请先创建部门"

    elif issue_type == "configuration_mismatch":
        return "❌ 启用绑卡的部门没有可用的CK，请检查部门和CK的配置匹配"

    # 如果有选择到CK但分布不均
    if test_results["unique_ck_count"] > 0:
        return f"⚠️ 负载分布不均匀，仅使用了 {test_results['unique_ck_count']} 个不同的CK，建议检查CK权重配置"

    return "❌ 无法选择到任何CK，请检查CK和部门配置"


def _generate_diagnosis_summary(diagnosis: Dict[str, Any]) -> str:
    """
    生成诊断摘要

    Args:
        diagnosis: 诊断信息

    Returns:
        str: 诊断摘要
    """
    dept_stats = diagnosis.get("department_stats", {})
    ck_stats = diagnosis.get("ck_stats", {})

    summary_parts = []

    # 部门摘要
    total_depts = dept_stats.get("total_departments", 0)
    enabled_depts = dept_stats.get("binding_enabled_departments", 0)
    summary_parts.append(f"部门: {enabled_depts}/{total_depts} 个启用绑卡")

    # CK摘要
    total_cks = ck_stats.get("total_cks", 0)
    available_cks = ck_stats.get("available_cks", 0)
    summary_parts.append(f"CK: {available_cks}/{total_cks} 个可用")

    return " | ".join(summary_parts)


@router.get("/status", response_model=Dict[str, Any])
async def get_ck_load_balance_status(
    *,
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_active_user),
    merchant_id: Optional[int] = Query(None, description="商户ID，不指定则查看所有商户")
):
    """
    获取CK负载均衡状态
    
    权限要求:
    - "api:ck-monitor:read": CK监控查看权限
    """
    try:
        # 权限检查
        from app.services.permission_service import PermissionService
        permission_service = PermissionService(db)
        
        if not permission_service.check_user_permission(current_user, "api:ck-monitor:read"):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="没有CK监控查看权限"
            )
        
        # 如果指定了商户ID，检查用户是否有权限访问该商户
        if merchant_id:
            if not permission_service.can_access_merchant_data(current_user, merchant_id):
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail="没有权限访问指定商户的数据"
                )
        
        # 创建监控实例并收集数据
        monitor = CKLoadBalanceMonitor()
        monitor_data = await monitor._collect_monitoring_data(db, merchant_id)
        
        # 生成告警
        alerts = monitor._analyze_and_generate_alerts(monitor_data)
        
        return {
            "status": "success",
            "data": monitor_data,
            "alerts": alerts,
            "summary": {
                "total_alerts": len(alerts),
                "high_severity_alerts": len([a for a in alerts if a.get("severity") == "high"]),
                "load_balance_score": monitor_data.get("ck_stats", {}).get("load_balance_score", 0),
                "service_health_score": monitor_data.get("service_status", {}).get("health_score", 0)
            }
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取CK负载均衡状态失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取状态失败: {str(e)}"
        )


@router.get("/health", response_model=Dict[str, Any])
async def get_ck_service_health(
    *,
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_active_user)
):
    """
    获取CK服务健康状态
    
    权限要求:
    - "api:ck-monitor:read": CK监控查看权限
    """
    try:
        # 权限检查
        from app.services.permission_service import PermissionService
        permission_service = PermissionService(db)
        
        if not permission_service.check_user_permission(current_user, "api:ck-monitor:read"):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="没有CK监控查看权限"
            )
        
        # 简化版本的健康检查（不需要实例化服务）
        health_info = {
            "service_type": "simplified_ck_service",
            "status": "healthy",
            "redis_enabled": False,
            "database_connected": True,
            "redis_connected": False  # 明确标识Redis已禁用
        }
        
        return {
            "status": "success",
            "health": health_info,
            "timestamp": str(datetime.now())
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取CK服务健康状态失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取健康状态失败: {str(e)}"
        )


@router.post("/test-load-balance", response_model=Dict[str, Any])
async def test_load_balance(
    *,
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_active_user),
    merchant_id: Optional[int] = Query(None, description="商户ID"),
    test_rounds: int = Query(10, description="测试轮数", ge=1, le=50)
):
    """
    测试CK负载均衡功能
    
    权限要求:
    - "api:ck-monitor:test": CK监控测试权限
    """
    try:
        # 权限检查
        from app.services.permission_service import PermissionService
        permission_service = PermissionService(db)
        
        if not permission_service.check_user_permission(current_user, "api:ck-monitor:test"):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="没有CK监控测试权限"
            )
        
        # 如果指定了商户ID，检查权限
        if merchant_id:
            if not permission_service.can_access_merchant_data(current_user, merchant_id):
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail="没有权限访问指定商户的数据"
                )
        
        # 如果没有指定商户ID，使用用户的商户ID
        if not merchant_id:
            if current_user.is_superuser:
                # 超级管理员需要指定商户ID
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="超级管理员需要指定商户ID"
                )
            elif current_user.merchant_id:
                merchant_id = current_user.merchant_id
            else:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="用户没有关联的商户"
                )
        
        # 【修复】增加详细诊断功能
        ck_service = SimplifiedCKService(db)

        # 收集诊断信息
        diagnosis = await _collect_ck_diagnosis(db, merchant_id)

        test_results = {
            "merchant_id": merchant_id,
            "test_rounds": test_rounds,
            "selected_cks": [],
            "unique_ck_count": 0,
            "distribution": {},
            "is_balanced": False,
            "balance_score": 0,
            "diagnosis": diagnosis  # 新增诊断信息
        }

        # 进行多轮CK选择测试
        for _ in range(test_rounds):
            ck = await ck_service.get_available_ck(merchant_id)
            if ck:
                test_results["selected_cks"].append(ck.id)
                # 【关键修复】测试完成后立即释放预占用，避免影响后续测试
                await ck_service.commit_ck_usage(ck.id, False)
            else:
                test_results["selected_cks"].append(None)
        
        # 分析测试结果
        valid_selections = [ck_id for ck_id in test_results["selected_cks"] if ck_id is not None]
        test_results["unique_ck_count"] = len(set(valid_selections))
        
        if valid_selections:
            from collections import Counter
            ck_counts = Counter(valid_selections)
            test_results["distribution"] = dict(ck_counts)
            
            # 计算负载均衡分数
            max_usage = max(ck_counts.values())
            min_usage = min(ck_counts.values())
            
            if min_usage > 0:
                imbalance_ratio = max_usage / min_usage
                test_results["balance_score"] = max(0, 100 - (imbalance_ratio - 1) * 20)
            else:
                test_results["balance_score"] = 0
            
            test_results["is_balanced"] = (
                test_results["unique_ck_count"] > 1 and 
                test_results["balance_score"] > 60
            )
        
        # 【修复】生成智能建议
        recommendation = _generate_smart_recommendation(test_results)

        return {
            "status": "success",
            "test_results": test_results,
            "recommendation": recommendation,
            "diagnosis_summary": _generate_diagnosis_summary(test_results["diagnosis"])
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"CK负载均衡测试失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"测试失败: {str(e)}"
        )


@router.post("/sync-redis", response_model=Dict[str, Any])
async def sync_ck_data_to_redis(
    *,
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_active_user),
    merchant_id: Optional[int] = Query(None, description="商户ID，不指定则同步所有商户")
):
    """
    同步CK数据到Redis
    
    权限要求:
    - "api:ck-monitor:admin": CK监控管理权限
    """
    try:
        # 权限检查
        from app.services.permission_service import PermissionService
        permission_service = PermissionService(db)
        
        if not permission_service.check_user_permission(current_user, "api:ck-monitor:admin"):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="没有CK监控管理权限"
            )
        
        # 简化版本不需要Redis同步
        from app.models.walmart_ck import WalmartCK
        query = db.query(WalmartCK).filter(WalmartCK.is_deleted == False)
        if merchant_id:
            query = query.filter(WalmartCK.merchant_id == merchant_id)

        ck_count = query.count()

        return {
            "status": "success",
            "message": f"简化CK服务不需要Redis同步，当前有 {ck_count} 个可用CK",
            "synced_count": ck_count,
            "merchant_id": merchant_id,
            "note": "使用简化CK服务，无需Redis同步"
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"同步CK数据到Redis失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"同步失败: {str(e)}"
        )


@router.get("/statistics", response_model=Dict[str, Any])
async def get_ck_statistics(
    *,
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_active_user),
    merchant_id: Optional[int] = Query(None, description="商户ID"),
    days: int = Query(7, description="统计天数", ge=1, le=30)
):
    """
    获取CK使用统计
    
    权限要求:
    - "api:ck-monitor:read": CK监控查看权限
    """
    try:
        # 权限检查
        from app.services.permission_service import PermissionService
        permission_service = PermissionService(db)
        
        if not permission_service.check_user_permission(current_user, "api:ck-monitor:read"):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="没有CK监控查看权限"
            )
        
        # 如果指定了商户ID，检查权限
        if merchant_id:
            if not permission_service.can_access_merchant_data(current_user, merchant_id):
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail="没有权限访问指定商户的数据"
                )
        
        from datetime import datetime, timedelta
        from app.models.walmart_ck import WalmartCK
        from app.models.card_record import CardRecord
        
        # 计算时间范围
        end_date = datetime.now()
        start_date = end_date - timedelta(days=days)
        
        # CK基础统计
        ck_query = db.query(WalmartCK).filter(WalmartCK.is_deleted == False)
        if merchant_id:
            ck_query = ck_query.filter(WalmartCK.merchant_id == merchant_id)
        
        cks = ck_query.all()
        active_cks = [ck for ck in cks if ck.active]
        
        # 绑卡记录统计
        record_query = db.query(CardRecord).filter(
            CardRecord.created_at >= start_date,
            CardRecord.walmart_ck_id.isnot(None)
        )
        if merchant_id:
            record_query = record_query.filter(CardRecord.merchant_id == merchant_id)
        
        records = record_query.all()
        
        # 按日期分组统计
        daily_stats = {}
        for record in records:
            date_key = record.created_at.date().isoformat()
            if date_key not in daily_stats:
                daily_stats[date_key] = {"total": 0, "success": 0, "ck_usage": {}}
            
            daily_stats[date_key]["total"] += 1
            if record.status == "success":
                daily_stats[date_key]["success"] += 1
            
            ck_id = record.walmart_ck_id
            daily_stats[date_key]["ck_usage"][ck_id] = daily_stats[date_key]["ck_usage"].get(ck_id, 0) + 1
        
        # CK使用排行
        from collections import Counter
        ck_usage_counter = Counter(record.walmart_ck_id for record in records if record.walmart_ck_id)
        top_used_cks = ck_usage_counter.most_common(10)
        
        return {
            "status": "success",
            "statistics": {
                "time_range": {
                    "start_date": start_date.isoformat(),
                    "end_date": end_date.isoformat(),
                    "days": days
                },
                "ck_summary": {
                    "total_cks": len(cks),
                    "active_cks": len(active_cks),
                    "inactive_cks": len(cks) - len(active_cks)
                },
                "binding_summary": {
                    "total_bindings": len(records),
                    "success_bindings": len([r for r in records if r.status == "success"]),
                    "success_rate": (len([r for r in records if r.status == "success"]) / len(records) * 100) if records else 0
                },
                "daily_stats": daily_stats,
                "top_used_cks": [{"ck_id": ck_id, "usage_count": count} for ck_id, count in top_used_cks],
                "load_balance_analysis": {
                    "unique_cks_used": len(ck_usage_counter),
                    "usage_distribution": dict(ck_usage_counter),
                    "most_used_ck": {
                        "ck_id": top_used_cks[0][0],
                        "usage_count": top_used_cks[0][1],
                        "usage_percentage": (top_used_cks[0][1] / len(records) * 100) if records and top_used_cks else 0
                    } if top_used_cks else None
                }
            }
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取CK统计失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取统计失败: {str(e)}"
        )
