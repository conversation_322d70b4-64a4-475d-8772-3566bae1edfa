# Telegram机器人Webhook生产环境配置指南

## 概述

本指南详细说明如何在生产环境中正确配置Telegram机器人的Webhook接口，确保安全性和可靠性。

## 1. Webhook接口安全配置

### 1.1 访问控制
- ✅ **已解决**：Webhook接口 `/api/v1/telegram/webhook` 已添加到权限中间件排除列表
- ✅ **已实现**：使用Telegram官方的secret token机制进行安全验证
- ✅ **已配置**：不需要JWT token验证，避免Telegram服务器调用失败

### 1.2 安全验证机制
```python
# 使用HMAC-SHA256签名验证
def verify_webhook_signature(self, body: bytes, signature: str) -> bool:
    expected_signature = hmac.new(
        self.config.webhook_secret.encode(),
        body,
        hashlib.sha256
    ).hexdigest()
    return hmac.compare_digest(f"sha256={expected_signature}", signature)
```

## 2. 生产环境配置步骤

### 2.1 域名和SSL配置
1. **域名要求**：
   - 必须使用有效的域名（不能是IP地址）
   - 必须支持HTTPS（SSL证书）
   - 建议使用标准端口：443, 80, 88, 8443

2. **SSL证书**：
   - 使用有效的SSL证书（Let's Encrypt或商业证书）
   - 确保证书链完整
   - 定期更新证书

### 2.2 Webhook URL配置
```yaml
# config/telegram_bot_production.yaml
bot:
  webhook:
    url: https://your-domain.com/api/v1/telegram/webhook
    secret: your-secure-secret-token
```

### 2.3 环境变量配置
```bash
# 生产环境环境变量
export TELEGRAM_BOT_TOKEN="your-bot-token"
export TELEGRAM_WEBHOOK_URL="https://your-domain.com/api/v1/telegram/webhook"
export TELEGRAM_WEBHOOK_SECRET="your-secure-secret-token"
```

## 3. 安全最佳实践

### 3.1 Secret Token生成
```bash
# 生成安全的secret token
openssl rand -hex 32
```

### 3.2 Secret Token要求
- 长度至少16个字符，建议32个字符以上
- 包含大小写字母、数字和特殊字符
- 定期更换（建议每3-6个月）
- 安全存储，不要硬编码在代码中

### 3.3 网络安全
- 使用防火墙限制访问
- 配置DDoS防护
- 监控异常请求
- 记录访问日志

## 4. 配置验证和测试

### 4.1 使用内置测试工具
```bash
# 通过API测试webhook配置
curl -X POST "https://your-domain.com/api/v1/telegram/webhook/test" \
  -H "Authorization: Bearer your-jwt-token"
```

### 4.2 测试项目
- ✅ URL格式验证
- ✅ HTTPS协议检查
- ✅ 域名可访问性
- ✅ Secret token安全性
- ✅ 端点响应测试

## 5. 常见问题和解决方案

### 5.1 Webhook设置失败
**问题**：Telegram返回"Webhook设置失败"
**解决方案**：
1. 检查URL是否使用HTTPS
2. 验证SSL证书有效性
3. 确认端口是否被支持
4. 检查防火墙设置

### 5.2 签名验证失败
**问题**：收到请求但签名验证失败
**解决方案**：
1. 检查secret token配置
2. 验证签名计算逻辑
3. 确认请求头格式正确

### 5.3 服务不可用
**问题**：Telegram显示服务不可用
**解决方案**：
1. 检查应用服务状态
2. 验证数据库连接
3. 查看应用日志

## 6. 监控和维护

### 6.1 日志监控
- 监控webhook请求频率
- 记录处理成功/失败率
- 跟踪响应时间

### 6.2 性能优化
- 使用异步处理
- 实现请求队列
- 配置合适的超时时间

### 6.3 故障恢复
- 实现健康检查
- 配置自动重启
- 准备回退方案

## 7. 部署检查清单

- [ ] SSL证书配置正确
- [ ] 域名解析正常
- [ ] Webhook URL可访问
- [ ] Secret token已配置
- [ ] 权限中间件排除列表已更新
- [ ] 签名验证逻辑已实现
- [ ] 日志记录已配置
- [ ] 监控告警已设置
- [ ] 备份恢复方案已准备

## 8. 联系和支持

如果在配置过程中遇到问题，请：
1. 查看应用日志
2. 使用内置测试工具
3. 检查Telegram官方文档
4. 联系技术支持团队
