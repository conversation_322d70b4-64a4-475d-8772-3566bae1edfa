"""
简单的单例管理器
使用PID文件方式，避免复杂的文件锁
"""

import os
import time
import atexit
from pathlib import Path
from typing import Optional

from app.core.logging import get_logger

logger = get_logger(__name__)


class SimpleSingletonManager:
    """简单单例管理器"""
    
    def __init__(self, name: str = "telegram_bot"):
        self.name = name
        # 使用更安全的临时目录路径
        import tempfile
        temp_dir = Path(tempfile.gettempdir())
        self.pid_file_path = temp_dir / f"{name}_{os.getuid() if hasattr(os, 'getuid') else 'user'}.pid"
        self.acquired = False
        self.current_pid = os.getpid()
    
    def acquire(self) -> bool:
        """获取单例锁"""
        try:
            # 检查现有PID文件
            if self.pid_file_path.exists():
                existing_info = self._read_pid_file()
                if existing_info:
                    existing_pid = existing_info.get("pid")
                    if existing_pid and self._is_process_running(existing_pid):
                        logger.warning(f"⚠️  检测到其他实例正在运行 (PID: {existing_pid})")
                        return False
                    else:
                        logger.info("清理无效的PID文件...")
                        self._remove_pid_file()
            
            # 创建新的PID文件
            self._write_pid_file()
            self.acquired = True
            
            # 注册退出清理
            atexit.register(self.release)
            
            logger.info(f"✅ 获取单例锁成功: {self.name} (PID: {self.current_pid})")
            return True
            
        except Exception as e:
            logger.error(f"获取单例锁失败: {e}")
            return False
    
    def release(self):
        """释放单例锁"""
        if self.acquired:
            try:
                self._remove_pid_file()
                self.acquired = False
                logger.info(f"✅ 释放单例锁: {self.name}")
            except Exception as e:
                logger.warning(f"释放单例锁时出错: {e}")
    
    def _write_pid_file(self):
        """写入PID文件"""
        with open(self.pid_file_path, 'w') as f:
            f.write(f"{self.current_pid}\n")
            f.write(f"{time.time()}\n")
            f.write(f"{self.name}\n")
    
    def _read_pid_file(self) -> Optional[dict]:
        """读取PID文件"""
        try:
            with open(self.pid_file_path, 'r') as f:
                lines = f.readlines()
                if len(lines) >= 2:
                    pid = int(lines[0].strip())
                    timestamp = float(lines[1].strip())
                    name = lines[2].strip() if len(lines) > 2 else self.name
                    return {
                        "pid": pid,
                        "timestamp": timestamp,
                        "name": name,
                        "age_seconds": time.time() - timestamp
                    }
        except Exception as e:
            logger.warning(f"读取PID文件失败: {e}")
        return None
    
    def _remove_pid_file(self):
        """删除PID文件"""
        try:
            if self.pid_file_path.exists():
                self.pid_file_path.unlink()
        except Exception as e:
            logger.warning(f"删除PID文件失败: {e}")
    
    def _is_process_running(self, pid: int) -> bool:
        """检查进程是否在运行（跨平台）"""
        try:
            if os.name == 'nt':  # Windows
                import subprocess
                try:
                    # 使用tasklist检查进程
                    result = subprocess.run(
                        ['tasklist', '/FI', f'PID eq {pid}'],
                        capture_output=True, text=True, timeout=10,
                        creationflags=subprocess.CREATE_NO_WINDOW if hasattr(subprocess, 'CREATE_NO_WINDOW') else 0
                    )
                    return str(pid) in result.stdout and result.returncode == 0
                except (subprocess.TimeoutExpired, FileNotFoundError):
                    # 如果tasklist不可用，尝试使用wmic
                    try:
                        result = subprocess.run(
                            ['wmic', 'process', 'where', f'ProcessId={pid}', 'get', 'ProcessId'],
                            capture_output=True, text=True, timeout=10,
                            creationflags=subprocess.CREATE_NO_WINDOW if hasattr(subprocess, 'CREATE_NO_WINDOW') else 0
                        )
                        return str(pid) in result.stdout and result.returncode == 0
                    except Exception:
                        return False
            else:  # Unix/Linux/macOS
                try:
                    # 使用os.kill(pid, 0)检查进程是否存在
                    os.kill(pid, 0)
                    return True
                except OSError as e:
                    # errno.ESRCH表示进程不存在
                    import errno
                    if e.errno == errno.ESRCH:
                        return False
                    # errno.EPERM表示权限不足，但进程存在
                    elif e.errno == errno.EPERM:
                        return True
                    else:
                        return False
        except Exception as e:
            logger.warning(f"检查进程状态失败 (PID: {pid}): {e}")
            return False
    
    def get_lock_info(self) -> Optional[dict]:
        """获取锁信息"""
        return self._read_pid_file()
    
    def force_cleanup(self) -> bool:
        """强制清理锁"""
        try:
            lock_info = self.get_lock_info()
            if not lock_info:
                logger.info("没有发现锁文件")
                return True
            
            pid = lock_info["pid"]
            age = lock_info["age_seconds"]
            
            logger.info(f"发现锁文件: PID={pid}, 年龄={age:.1f}秒")
            
            # 检查进程是否还在运行
            if not self._is_process_running(pid):
                logger.info(f"进程 {pid} 已不存在，清理锁文件")
                self._remove_pid_file()
                return True
            
            # 如果锁太老（超过1小时），可能是死锁
            if age > 3600:
                logger.warning(f"锁文件过老 ({age:.1f}秒)，强制清理")
                self._remove_pid_file()
                return True
            
            logger.info(f"进程 {pid} 仍在运行，无法清理")
            return False
            
        except Exception as e:
            logger.error(f"强制清理失败: {e}")
            return False
    
    def __enter__(self):
        """上下文管理器入口"""
        if not self.acquire():
            raise RuntimeError(f"无法获取单例锁: {self.name}")
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """上下文管理器出口"""
        self.release()
        return False


# 全局实例
_bot_singleton = SimpleSingletonManager("telegram_bot")


def acquire_bot_singleton() -> bool:
    """获取机器人单例锁"""
    return _bot_singleton.acquire()


def release_bot_singleton():
    """释放机器人单例锁"""
    _bot_singleton.release()


def get_bot_singleton_info() -> Optional[dict]:
    """获取机器人单例信息"""
    return _bot_singleton.get_lock_info()


def force_cleanup_bot_singleton() -> bool:
    """强制清理机器人单例锁"""
    return _bot_singleton.force_cleanup()


def is_bot_singleton_acquired() -> bool:
    """检查是否已获取单例锁"""
    return _bot_singleton.acquired
