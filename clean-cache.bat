@echo off
echo 正在清理Vite缓存...

REM 删除node_modules/.vite目录
if exist "node_modules\.vite" (
    echo 删除 node_modules\.vite 目录...
    rmdir /s /q "node_modules\.vite"
    echo node_modules\.vite 目录已删除
) else (
    echo node_modules\.vite 目录不存在
)

REM 删除dist目录
if exist "dist" (
    echo 删除 dist 目录...
    rmdir /s /q "dist"
    echo dist 目录已删除
) else (
    echo dist 目录不存在
)

echo 缓存清理完成！
echo 请重新运行 npm run dev 启动开发服务器
pause
