-- ========================================
-- 添加测试模式字段到 card_records 表
-- 版本: v2.8.1
-- 用途: 支持并发测试功能的数据标识
-- ========================================

-- 开始事务
START TRANSACTION;

-- 检查字段是否已存在
SET @column_exists = (
    SELECT COUNT(*)
    FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_SCHEMA = DATABASE()
    AND TABLE_NAME = 'card_records'
    AND COLUMN_NAME = 'is_test_mode'
);

-- 显示检查结果
SELECT 
    CASE 
        WHEN @column_exists > 0 THEN '字段 is_test_mode 已存在'
        ELSE '字段 is_test_mode 不存在，将添加'
    END as field_status;

-- 添加字段（如果不存在）
SET @sql = IF(@column_exists = 0,
    'ALTER TABLE `card_records` ADD COLUMN `is_test_mode` BOOLEAN NOT NULL DEFAULT FALSE COMMENT "是否为测试模式，用于并发测试时标识测试数据"',
    'SELECT "字段 is_test_mode 已存在，跳过添加" as message'
);

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 为现有记录设置默认值（确保没有NULL值）
UPDATE `card_records` SET `is_test_mode` = FALSE WHERE `is_test_mode` IS NULL;

-- 验证字段添加结果
SELECT 
    '字段验证' as check_type,
    COLUMN_NAME as field_name,
    DATA_TYPE as data_type,
    IS_NULLABLE as nullable,
    COLUMN_DEFAULT as default_value,
    COLUMN_COMMENT as comment
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE()
AND TABLE_NAME = 'card_records'
AND COLUMN_NAME = 'is_test_mode';

-- 检查现有记录的字段值分布
SELECT 
    '数据验证' as check_type,
    is_test_mode,
    COUNT(*) as record_count
FROM card_records 
GROUP BY is_test_mode;

-- 提交事务
COMMIT;

-- ========================================
-- 使用说明
-- ========================================

/*
字段说明：
- 字段名：is_test_mode
- 类型：BOOLEAN
- 默认值：FALSE
- 用途：标识记录是否为测试模式下创建

使用场景：
1. 并发测试时，所有测试记录将标记为 is_test_mode = TRUE
2. 生产数据统计时可以排除测试数据
3. 数据清理时可以批量删除测试数据

查询示例：
-- 查询所有测试记录
SELECT * FROM card_records WHERE is_test_mode = TRUE;

-- 查询生产记录（排除测试数据）
SELECT * FROM card_records WHERE is_test_mode = FALSE;

-- 统计生产数据（排除测试数据）
SELECT COUNT(*) FROM card_records WHERE is_test_mode = FALSE;

-- 清理测试数据（谨慎操作）
DELETE FROM card_records WHERE is_test_mode = TRUE;

回滚操作（谨慎执行）：
-- 删除字段（会丢失所有测试标识信息）
-- ALTER TABLE card_records DROP COLUMN is_test_mode;
*/

-- 显示完成信息
SELECT 
    '迁移完成' as status,
    'is_test_mode 字段已成功添加到 card_records 表' as message,
    NOW() as completed_at;
