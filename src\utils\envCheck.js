/**
 * 环境检测工具
 * 用于判断当前运行环境，控制测试功能的显示
 */

/**
 * 检查是否为开发环境
 * @returns {boolean}
 */
export const isDevelopment = () => {
  return process.env.NODE_ENV === 'development'
}

/**
 * 检查是否为测试环境
 * @returns {boolean}
 */
export const isTest = () => {
  return process.env.NODE_ENV === 'test'
}

/**
 * 检查是否为生产环境
 * @returns {boolean}
 */
export const isProduction = () => {
  return process.env.NODE_ENV === 'production'
}

/**
 * 检查是否允许显示测试功能
 * 在开发环境或测试环境中显示，生产环境中隐藏
 * @returns {boolean}
 */
export const isTestFeatureEnabled = () => {
  // 检查环境变量
  if (isDevelopment() || isTest()) {
    return true
  }
  
  // 检查URL参数（用于生产环境的特殊情况）
  const urlParams = new URLSearchParams(window.location.search)
  const enableTest = urlParams.get('enable_test')
  
  // 只有在URL中明确指定且值为特定字符串时才在生产环境启用
  if (isProduction() && enableTest === 'walmart_test_2024') {
    return true
  }
  
  // 检查localStorage中的测试模式标识（用于临时启用）
  const testModeEnabled = localStorage.getItem('walmart_test_mode_enabled')
  if (testModeEnabled === 'true') {
    return true
  }
  
  return false
}

/**
 * 获取当前环境名称
 * @returns {string}
 */
export const getEnvironmentName = () => {
  if (isDevelopment()) return '开发环境'
  if (isTest()) return '测试环境'
  if (isProduction()) return '生产环境'
  return '未知环境'
}

/**
 * 检查是否为本地开发环境
 * @returns {boolean}
 */
export const isLocalDevelopment = () => {
  return isDevelopment() && (
    window.location.hostname === 'localhost' ||
    window.location.hostname === '127.0.0.1' ||
    window.location.hostname.startsWith('192.168.') ||
    window.location.hostname.startsWith('10.') ||
    window.location.hostname.endsWith('.local')
  )
}

/**
 * 启用测试模式（临时）
 * 在localStorage中设置标识，页面刷新后生效
 */
export const enableTestMode = () => {
  localStorage.setItem('walmart_test_mode_enabled', 'true')
  console.log('测试模式已启用，请刷新页面')
}

/**
 * 禁用测试模式
 */
export const disableTestMode = () => {
  localStorage.removeItem('walmart_test_mode_enabled')
  console.log('测试模式已禁用，请刷新页面')
}

/**
 * 获取环境配置信息
 * @returns {Object}
 */
export const getEnvironmentInfo = () => {
  return {
    nodeEnv: process.env.NODE_ENV,
    isDevelopment: isDevelopment(),
    isTest: isTest(),
    isProduction: isProduction(),
    isLocalDevelopment: isLocalDevelopment(),
    isTestFeatureEnabled: isTestFeatureEnabled(),
    environmentName: getEnvironmentName(),
    hostname: window.location.hostname,
    apiBaseUrl: window.APP_CONFIG?.API_BASE_URL,
    buildTime: process.env.VUE_APP_BUILD_TIME || 'Unknown'
  }
}

/**
 * 在控制台输出环境信息（用于调试）
 */
export const logEnvironmentInfo = () => {
  const info = getEnvironmentInfo()
  console.group('🌍 环境信息')
  console.log('环境名称:', info.environmentName)
  console.log('Node环境:', info.nodeEnv)
  console.log('主机名:', info.hostname)
  console.log('API地址:', info.apiBaseUrl)
  console.log('本地开发:', info.isLocalDevelopment ? '是' : '否')
  console.log('测试功能:', info.isTestFeatureEnabled ? '启用' : '禁用')
  console.log('构建时间:', info.buildTime)
  console.groupEnd()
}

export default {
  isDevelopment,
  isTest,
  isProduction,
  isTestFeatureEnabled,
  getEnvironmentName,
  isLocalDevelopment,
  enableTestMode,
  disableTestMode,
  getEnvironmentInfo,
  logEnvironmentInfo
}
