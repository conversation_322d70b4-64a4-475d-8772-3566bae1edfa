"""
Telegram机器人关键词配置
统一管理所有关键词，避免在多个文件中重复维护
"""

from typing import List, Dict, Set, Optional
from app.core.logging import get_logger

logger = get_logger(__name__)


class TelegramKeywords:
    """Telegram机器人关键词管理类"""

    def __init__(self):
        """初始化关键词配置"""
        self._keyword_categories = self._load_keyword_categories()
        self._keywords = self._flatten_keywords()
        self._keyword_set = set(self._keywords)  # 用于快速查找

    def _load_keyword_categories(self) -> Dict[str, List[str]]:
        """加载分类关键词配置 - 统一管理所有关键词"""
        return {
            "状态查询": [
                "查看状态", "当前状态", "绑定状态", "群组状态", "机器人状态", "bot状态",
                "查询状态", "状态查询", "系统状态", "运行状态"
            ],
            "帮助相关": [
                "需要帮助", "help", "操作指南", "使用说明", "帮助文档",  "功能介绍", "命令列表"
            ],
            "绑定相关": [
                "绑定群组", "解绑群组", "绑定机器人", "解绑机器人", "群组绑定",
                "bind", "unbind", "绑定设置", "解除绑定", "绑定", "解绑"
            ],
            "统计相关": [
                # 基础统计关键词
                "今日数据","昨日数据","本周数据","本月数据",
                # CK相关统计 - 更全面的表达方式
                "今日CK", "今日ck", "今日Ck", "今日cK",
                "昨日CK", "昨日ck", "昨日Ck", "昨日cK",
                "本周CK", "本周ck", "本周Ck", "本周cK",
                "本月CK", "本月ck", "本月Ck", "本月cK",
            ],
            "错误相关": [
                "机器人错误", "功能故障", "无法工作", "操作失败",
            ]
        }

    def _flatten_keywords(self) -> List[str]:
        """将分类关键词展平为单一列表"""
        keywords = []
        for category_keywords in self._keyword_categories.values():
            keywords.extend(category_keywords)
        return keywords
    
    def get_keywords(self) -> List[str]:
        """获取所有关键词列表"""
        return self._keywords.copy()
    
    def get_keyword_set(self) -> Set[str]:
        """获取关键词集合（用于快速查找）"""
        return self._keyword_set.copy()
    
    def check_message(self, message_text: str) -> tuple[bool, List[str]]:
        """
        检查消息是否包含关键词
        
        Args:
            message_text: 消息文本
            
        Returns:
            tuple: (是否匹配, 匹配的关键词列表)
        """
        if not message_text:
            return False, []
        
        text = message_text.lower()
        matched_keywords = [keyword for keyword in self._keywords if keyword.lower() in text]
        has_match = len(matched_keywords) > 0
        
        # 记录调试日志
        if has_match:
            logger.debug(f"关键词匹配: '{message_text}' -> {matched_keywords}")
        else:
            logger.debug(f"关键词不匹配: '{message_text}'")
        
        return has_match, matched_keywords
    
    def add_keyword(self, keyword: str) -> bool:
        """
        添加新关键词
        
        Args:
            keyword: 要添加的关键词
            
        Returns:
            bool: 是否成功添加（如果已存在则返回False）
        """
        keyword = keyword.lower().strip()
        if keyword and keyword not in self._keyword_set:
            self._keywords.append(keyword)
            self._keyword_set.add(keyword)
            logger.info(f"添加新关键词: '{keyword}'")
            return True
        return False
    
    def remove_keyword(self, keyword: str) -> bool:
        """
        移除关键词
        
        Args:
            keyword: 要移除的关键词
            
        Returns:
            bool: 是否成功移除
        """
        keyword = keyword.lower().strip()
        if keyword in self._keyword_set:
            self._keywords.remove(keyword)
            self._keyword_set.remove(keyword)
            logger.info(f"移除关键词: '{keyword}'")
            return True
        return False
    
    def get_keywords_by_category(self) -> Dict[str, List[str]]:
        """
        按类别获取关键词

        Returns:
            dict: 按类别分组的关键词
        """
        return self._keyword_categories.copy()
    
    def reload_keywords(self):
        """重新加载关键词（用于动态更新）"""
        self._keyword_categories = self._load_keyword_categories()
        self._keywords = self._flatten_keywords()
        self._keyword_set = set(self._keywords)
        logger.info("关键词配置已重新加载")
    
    def get_statistics(self) -> Dict[str, any]:
        """
        获取关键词统计信息

        Returns:
            dict: 统计信息
        """
        categories = self.get_keywords_by_category()
        return {
            "total_keywords": len(self._keywords),
            "categories": {name: len(keywords) for name, keywords in categories.items()},
            "keyword_list": self._keywords
        }

    def add_keyword_to_category(self, category: str, keyword: str) -> bool:
        """
        向指定类别添加关键词

        Args:
            category: 类别名称
            keyword: 要添加的关键词

        Returns:
            bool: 是否成功添加
        """
        keyword = keyword.strip()
        if not keyword:
            return False

        if category not in self._keyword_categories:
            self._keyword_categories[category] = []

        if keyword not in self._keyword_categories[category]:
            self._keyword_categories[category].append(keyword)
            self._keywords = self._flatten_keywords()
            self._keyword_set = set(self._keywords)
            logger.info(f"向类别 '{category}' 添加关键词: '{keyword}'")
            return True
        return False

    def remove_keyword_from_category(self, category: str, keyword: str) -> bool:
        """
        从指定类别移除关键词

        Args:
            category: 类别名称
            keyword: 要移除的关键词

        Returns:
            bool: 是否成功移除
        """
        keyword = keyword.strip()
        if category in self._keyword_categories and keyword in self._keyword_categories[category]:
            self._keyword_categories[category].remove(keyword)
            self._keywords = self._flatten_keywords()
            self._keyword_set = set(self._keywords)
            logger.info(f"从类别 '{category}' 移除关键词: '{keyword}'")
            return True
        return False

    def get_category_for_keyword(self, keyword: str) -> Optional[str]:
        """
        获取关键词所属的类别

        Args:
            keyword: 关键词

        Returns:
            str: 类别名称，如果不存在则返回None
        """
        for category, keywords in self._keyword_categories.items():
            if keyword in keywords:
                return category
        return None


# 创建全局实例
telegram_keywords = TelegramKeywords()


def get_telegram_keywords() -> TelegramKeywords:
    """获取Telegram关键词管理实例"""
    return telegram_keywords


def check_keyword_match(message_text: str) -> tuple[bool, List[str]]:
    """
    便捷函数：检查消息是否匹配关键词
    
    Args:
        message_text: 消息文本
        
    Returns:
        tuple: (是否匹配, 匹配的关键词列表)
    """
    return telegram_keywords.check_message(message_text)


def get_all_keywords() -> List[str]:
    """
    便捷函数：获取所有关键词
    
    Returns:
        list: 关键词列表
    """
    return telegram_keywords.get_keywords()


# 向后兼容：提供旧的接口
def get_keywords() -> List[str]:
    """向后兼容的关键词获取函数"""
    return get_all_keywords()


if __name__ == "__main__":
    # 简单测试关键词匹配功能
    test_cases = ["ck今日", "CK今日", "ck今日统计", "查看CK今日数据"]
    print("=== 关键词匹配测试 ===")
    for test_text in test_cases:
        is_match, matched = check_keyword_match(test_text)
        print(f"'{test_text}' -> 匹配: {is_match}, 关键词: {matched}")
    print("=== 测试结束 ===")
