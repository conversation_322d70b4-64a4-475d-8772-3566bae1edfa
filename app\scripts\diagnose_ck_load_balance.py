#!/usr/bin/env python3
"""
沃尔玛绑卡系统CK负载均衡诊断脚本
用于检查和修复CK负载均衡问题
"""

import asyncio
import sys
import os
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
from sqlalchemy.orm import Session
from sqlalchemy import func, text

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app.db.session import SessionLocal
from app.models.walmart_ck import WalmartCK
from app.models.card_record import CardRecord
from app.core.redis import get_redis
from app.services.redis_ck_wrapper import create_optimized_ck_service, RedisHealthMonitor
from app.core.logging import get_logger

logger = get_logger("ck_load_balance_diagnosis")


class CKLoadBalanceDiagnostic:
    """CK负载均衡诊断工具"""
    
    def __init__(self):
        self.db: Optional[Session] = None
        self.redis_client = None
        
    async def __aenter__(self):
        """异步上下文管理器入口"""
        self.db = SessionLocal()
        try:
            self.redis_client = await get_redis()
        except Exception as e:
            logger.warning(f"Redis连接失败: {e}")
            self.redis_client = None
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器出口"""
        if self.db:
            self.db.close()
        if self.redis_client:
            try:
                await self.redis_client.close()
            except:
                pass
    
    async def run_full_diagnosis(self, merchant_id: Optional[int] = None) -> Dict[str, Any]:
        """运行完整诊断"""
        logger.info("开始CK负载均衡诊断...")
        
        diagnosis_result = {
            "timestamp": datetime.now().isoformat(),
            "merchant_id": merchant_id,
            "redis_status": await self._check_redis_status(),
            "ck_service_status": await self._check_ck_service_status(),
            "ck_distribution": await self._analyze_ck_distribution(merchant_id),
            "recent_binding_pattern": await self._analyze_recent_binding_pattern(merchant_id),
            "load_balance_test": await self._test_load_balance(merchant_id),
            "recommendations": []
        }
        
        # 生成建议
        diagnosis_result["recommendations"] = self._generate_recommendations(diagnosis_result)
        
        return diagnosis_result
    
    async def _check_redis_status(self) -> Dict[str, Any]:
        """检查Redis状态"""
        status = {
            "connected": False,
            "ping_success": False,
            "ck_keys_count": 0,
            "error": None
        }
        
        try:
            if self.redis_client:
                # 测试连接
                await self.redis_client.ping()
                status["connected"] = True
                status["ping_success"] = True
                
                # 检查CK相关的Redis键
                ck_keys = await self.redis_client.keys("walmart:ck:*")
                status["ck_keys_count"] = len(ck_keys)
                
                logger.info(f"Redis状态正常，找到 {len(ck_keys)} 个CK相关键")
            else:
                status["error"] = "Redis客户端未初始化"
                
        except Exception as e:
            status["error"] = str(e)
            logger.error(f"Redis状态检查失败: {e}")
        
        return status
    
    async def _check_ck_service_status(self) -> Dict[str, Any]:
        """检查CK服务状态"""
        status = {
            "service_type": "unknown",
            "redis_enabled": False,
            "redis_initialized": False,
            "health_check": None,
            "error": None
        }
        
        try:
            # 创建CK服务实例
            ck_service = create_optimized_ck_service(self.db)
            
            # 获取健康检查信息
            health_info = await ck_service.health_check()
            status.update(health_info)
            
            # 确定实际使用的服务类型
            if health_info.get("redis_enabled") and health_info.get("redis_initialized"):
                status["service_type"] = "redis_optimized"
            else:
                status["service_type"] = "database_fallback"
            
            logger.info(f"CK服务状态: {status['service_type']}")
            
        except Exception as e:
            status["error"] = str(e)
            logger.error(f"CK服务状态检查失败: {e}")
        
        return status
    
    async def _analyze_ck_distribution(self, merchant_id: Optional[int] = None) -> Dict[str, Any]:
        """分析CK分布情况"""
        distribution = {
            "total_ck_count": 0,
            "active_ck_count": 0,
            "bind_count_stats": {},
            "usage_distribution": [],
            "problematic_cks": []
        }
        
        try:
            # 构建查询
            query = self.db.query(WalmartCK).filter(WalmartCK.is_deleted == False)
            if merchant_id:
                query = query.filter(WalmartCK.merchant_id == merchant_id)
            
            cks = query.all()
            distribution["total_ck_count"] = len(cks)
            
            # 统计活跃CK
            active_cks = [ck for ck in cks if ck.active]
            distribution["active_ck_count"] = len(active_cks)
            
            # 分析bind_count分布
            bind_counts = [ck.bind_count for ck in active_cks]
            if bind_counts:
                distribution["bind_count_stats"] = {
                    "min": min(bind_counts),
                    "max": max(bind_counts),
                    "avg": sum(bind_counts) / len(bind_counts),
                    "total": sum(bind_counts)
                }
                
                # 检查使用分布
                usage_groups = {}
                for ck in active_cks:
                    count_range = f"{ck.bind_count//5*5}-{ck.bind_count//5*5+4}"
                    usage_groups[count_range] = usage_groups.get(count_range, 0) + 1
                
                distribution["usage_distribution"] = [
                    {"range": k, "count": v} for k, v in sorted(usage_groups.items())
                ]
                
                # 识别问题CK
                avg_count = distribution["bind_count_stats"]["avg"]
                for ck in active_cks:
                    if ck.bind_count > avg_count * 2:  # 使用次数超过平均值2倍
                        distribution["problematic_cks"].append({
                            "ck_id": ck.id,
                            "bind_count": ck.bind_count,
                            "total_limit": ck.total_limit,
                            "usage_ratio": ck.bind_count / ck.total_limit if ck.total_limit > 0 else 0
                        })
            
            logger.info(f"CK分布分析完成: 总数={distribution['total_ck_count']}, 活跃={distribution['active_ck_count']}")
            
        except Exception as e:
            distribution["error"] = str(e)
            logger.error(f"CK分布分析失败: {e}")
        
        return distribution
    
    async def _analyze_recent_binding_pattern(self, merchant_id: Optional[int] = None) -> Dict[str, Any]:
        """分析最近的绑卡模式"""
        pattern = {
            "time_range": "last_24_hours",
            "total_bindings": 0,
            "ck_usage_pattern": {},
            "most_used_ck": None,
            "binding_distribution": []
        }
        
        try:
            # 查询最近24小时的绑卡记录
            since_time = datetime.now() - timedelta(hours=24)
            
            query = self.db.query(CardRecord).filter(
                CardRecord.created_at >= since_time,
                CardRecord.walmart_ck_id.isnot(None)
            )
            
            if merchant_id:
                query = query.filter(CardRecord.merchant_id == merchant_id)
            
            recent_records = query.all()
            pattern["total_bindings"] = len(recent_records)
            
            # 统计CK使用模式
            ck_usage = {}
            for record in recent_records:
                ck_id = record.walmart_ck_id
                ck_usage[ck_id] = ck_usage.get(ck_id, 0) + 1
            
            pattern["ck_usage_pattern"] = ck_usage
            
            if ck_usage:
                # 找出使用最多的CK
                most_used_ck_id = max(ck_usage.keys(), key=lambda k: ck_usage[k])
                pattern["most_used_ck"] = {
                    "ck_id": most_used_ck_id,
                    "usage_count": ck_usage[most_used_ck_id],
                    "usage_percentage": (ck_usage[most_used_ck_id] / len(recent_records)) * 100
                }
                
                # 生成分布统计
                pattern["binding_distribution"] = [
                    {"ck_id": k, "count": v, "percentage": (v/len(recent_records))*100}
                    for k, v in sorted(ck_usage.items(), key=lambda x: x[1], reverse=True)
                ]
            
            logger.info(f"绑卡模式分析完成: 24小时内绑卡{pattern['total_bindings']}次")
            
        except Exception as e:
            pattern["error"] = str(e)
            logger.error(f"绑卡模式分析失败: {e}")
        
        return pattern
    
    async def _test_load_balance(self, merchant_id: Optional[int] = None) -> Dict[str, Any]:
        """测试负载均衡功能"""
        test_result = {
            "test_rounds": 20,  # 增加测试轮数
            "selected_cks": [],
            "unique_ck_count": 0,
            "is_balanced": False,
            "error": None,
            "available_ck_count": 0,
            "selection_details": []
        }

        try:
            if not merchant_id:
                # 如果没有指定商户，找一个有CK的商户
                ck = self.db.query(WalmartCK).filter(
                    WalmartCK.active == True,
                    WalmartCK.is_deleted == False
                ).first()
                if ck:
                    merchant_id = ck.merchant_id
                else:
                    test_result["error"] = "没有找到可用的CK进行测试"
                    return test_result

            # 检查可用CK数量
            available_cks = self.db.query(WalmartCK).filter(
                WalmartCK.merchant_id == merchant_id,
                WalmartCK.active == True,
                WalmartCK.is_deleted == False
            ).all()
            test_result["available_ck_count"] = len(available_cks)

            if test_result["available_ck_count"] == 0:
                test_result["error"] = f"商户 {merchant_id} 没有可用的CK"
                return test_result

            ck_service = create_optimized_ck_service(self.db)
            selected_ck_records = []  # 记录选择的CK以便释放

            # 进行多轮CK选择测试
            for i in range(test_result["test_rounds"]):
                logger.info(f"负载均衡测试第 {i+1} 轮")

                ck = await ck_service.get_available_ck(merchant_id)
                if ck:
                    test_result["selected_cks"].append(ck.id)
                    selected_ck_records.append({
                        'ck_id': ck.id,
                        'round': i + 1,
                        'bind_count': ck.bind_count
                    })

                    # 关键修复：立即释放CK以避免影响后续选择
                    try:
                        await ck_service.record_ck_usage(
                            ck_id=ck.id,
                            success=False  # 测试用途，不算成功绑卡
                        )
                        logger.debug(f"测试轮次 {i+1}: 选择CK {ck.id}，已释放")
                    except Exception as e:
                        logger.warning(f"释放CK {ck.id} 失败: {e}")

                else:
                    test_result["selected_cks"].append(None)
                    logger.warning(f"测试轮次 {i+1}: 未找到可用CK")

                # 添加小延迟避免锁冲突
                import asyncio
                await asyncio.sleep(0.1)

            # 记录选择详情
            test_result["selection_details"] = selected_ck_records

            # 分析结果
            valid_selections = [ck_id for ck_id in test_result["selected_cks"] if ck_id is not None]
            test_result["unique_ck_count"] = len(set(valid_selections))

            # 改进的均衡判断逻辑
            if test_result["available_ck_count"] == 1:
                # 只有一个CK时，选择同一个是正常的
                test_result["is_balanced"] = True
                test_result["balance_reason"] = "只有一个可用CK"
            elif test_result["unique_ck_count"] > 1:
                # 选择了多个不同的CK
                test_result["is_balanced"] = True
                test_result["balance_reason"] = f"选择了 {test_result['unique_ck_count']} 个不同的CK"
            else:
                # 有多个CK但只选择了一个
                test_result["is_balanced"] = False
                test_result["balance_reason"] = f"有 {test_result['available_ck_count']} 个可用CK但只选择了 {test_result['unique_ck_count']} 个"
            
            logger.info(f"负载均衡测试完成: 选择了{test_result['unique_ck_count']}个不同的CK")
            
        except Exception as e:
            test_result["error"] = str(e)
            logger.error(f"负载均衡测试失败: {e}")
        
        return test_result
    
    def _generate_recommendations(self, diagnosis_result: Dict[str, Any]) -> List[str]:
        """生成修复建议"""
        recommendations = []
        
        # Redis状态建议
        redis_status = diagnosis_result.get("redis_status", {})
        if not redis_status.get("connected"):
            recommendations.append("🔴 Redis连接失败，需要检查Redis服务状态和配置")
        elif redis_status.get("ck_keys_count", 0) == 0:
            recommendations.append("🟡 Redis中没有CK数据，需要同步数据库数据到Redis")
        
        # CK服务状态建议
        ck_service_status = diagnosis_result.get("ck_service_status", {})
        if ck_service_status.get("service_type") == "database_fallback":
            recommendations.append("🟡 系统运行在数据库降级模式，Redis优化未启用")
        
        # CK分布建议
        ck_distribution = diagnosis_result.get("ck_distribution", {})
        if ck_distribution.get("problematic_cks"):
            recommendations.append("🔴 发现负载不均衡的CK，需要重新分配负载")
        
        # 负载均衡测试建议
        load_balance_test = diagnosis_result.get("load_balance_test", {})
        if not load_balance_test.get("is_balanced"):
            recommendations.append("🔴 负载均衡测试失败，系统总是选择相同的CK")
        
        # 绑卡模式建议
        binding_pattern = diagnosis_result.get("recent_binding_pattern", {})
        most_used = binding_pattern.get("most_used_ck")
        if most_used and most_used.get("usage_percentage", 0) > 50:
            recommendations.append(f"🔴 单个CK使用率过高({most_used['usage_percentage']:.1f}%)，负载严重不均")
        
        if not recommendations:
            recommendations.append("✅ 系统状态正常，负载均衡功能运行良好")
        
        return recommendations


async def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description="CK负载均衡诊断工具")
    parser.add_argument("--merchant-id", type=int, help="指定商户ID进行诊断")
    parser.add_argument("--output", type=str, help="输出结果到文件")
    args = parser.parse_args()
    
    async with CKLoadBalanceDiagnostic() as diagnostic:
        result = await diagnostic.run_full_diagnosis(args.merchant_id)
        
        # 输出结果
        print("\n" + "="*80)
        print("🔍 沃尔玛绑卡系统CK负载均衡诊断报告")
        print("="*80)
        
        print(f"\n📊 诊断时间: {result['timestamp']}")
        if result['merchant_id']:
            print(f"🏪 商户ID: {result['merchant_id']}")
        
        # Redis状态
        redis_status = result['redis_status']
        print(f"\n🔗 Redis状态:")
        print(f"  连接状态: {'✅ 正常' if redis_status.get('connected') else '❌ 失败'}")
        if redis_status.get('error'):
            print(f"  错误信息: {redis_status['error']}")
        print(f"  CK相关键数量: {redis_status.get('ck_keys_count', 0)}")
        
        # CK服务状态
        ck_service = result['ck_service_status']
        print(f"\n⚙️ CK服务状态:")
        print(f"  服务类型: {ck_service.get('service_type', 'unknown')}")
        print(f"  Redis启用: {'✅' if ck_service.get('redis_enabled') else '❌'}")
        print(f"  Redis初始化: {'✅' if ck_service.get('redis_initialized') else '❌'}")
        
        # CK分布
        ck_dist = result['ck_distribution']
        print(f"\n📈 CK分布情况:")
        print(f"  总CK数量: {ck_dist.get('total_ck_count', 0)}")
        print(f"  活跃CK数量: {ck_dist.get('active_ck_count', 0)}")
        
        bind_stats = ck_dist.get('bind_count_stats', {})
        if bind_stats:
            print(f"  使用次数统计:")
            print(f"    最小值: {bind_stats.get('min', 0)}")
            print(f"    最大值: {bind_stats.get('max', 0)}")
            print(f"    平均值: {bind_stats.get('avg', 0):.2f}")
            print(f"    总计: {bind_stats.get('total', 0)}")
        
        # 负载均衡测试
        lb_test = result['load_balance_test']
        print(f"\n🎯 负载均衡测试:")
        print(f"  测试轮数: {lb_test.get('test_rounds', 0)}")
        print(f"  选择的不同CK数量: {lb_test.get('unique_ck_count', 0)}")
        print(f"  负载均衡状态: {'✅ 正常' if lb_test.get('is_balanced') else '❌ 失效'}")
        
        # 建议
        print(f"\n💡 修复建议:")
        for i, rec in enumerate(result['recommendations'], 1):
            print(f"  {i}. {rec}")
        
        # 保存到文件
        if args.output:
            import json
            with open(args.output, 'w', encoding='utf-8') as f:
                json.dump(result, f, ensure_ascii=False, indent=2)
            print(f"\n📄 详细结果已保存到: {args.output}")
        
        print("\n" + "="*80)


if __name__ == "__main__":
    asyncio.run(main())
