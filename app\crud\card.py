from typing import List, Optional, Dict, Any, Tuple
from datetime import datetime, date
from sqlalchemy.orm import Session
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import func, and_, or_, select

from app.models import CardRecord, CardStatus
from app.schemas.card import (
    CardRecordCreate,
    CardRecordUpdate,
    CardRecordFilter,
    CardStatistics,
)
from app.crud.base import CRUDBase


def create_card_record(db: Session, obj_in: CardRecordCreate) -> CardRecord:
    """
    创建卡记录

    Args:
        db: 数据库会话
        obj_in: 创建请求

    Returns:
        CardRecord: 创建的卡记录
    """
    # 创建卡记录对象
    db_obj = CardRecord(
        card_number=obj_in.card_number,
        card_password=obj_in.card_password,
        status=obj_in.status,  # 使用传入的状态
        request_id=obj_in.request_id,
        trace_id=obj_in.trace_id,  # 确保保存trace_id
        request_data=obj_in.request_data,
        merchant_id=obj_in.merchant_id,
        merchant_order_id=getattr(obj_in, 'merchant_order_id', ''),
        amount=getattr(obj_in, 'amount', 0),
        ext_data=getattr(obj_in, 'ext_data', None),
    )
    db.add(db_obj)
    db.commit()
    db.refresh(db_obj)
    return db_obj


def update_card_record(
    db: Session, db_obj: CardRecord, obj_in: CardRecordUpdate
) -> CardRecord:
    """
    更新卡记录

    Args:
        db: 数据库会话
        db_obj: 数据库对象
        obj_in: 更新请求

    Returns:
        CardRecord: 更新后的卡记录
    """
    update_data = obj_in.dict(exclude_unset=True)

    # 如果状态更新为成功或失败，且没有提供处理时间，则自动添加处理时间
    if "status" in update_data and update_data["status"] in [
        CardStatus.SUCCESS,
        CardStatus.FAILED,
    ]:
        if "process_time" not in update_data or update_data["process_time"] is None:
            update_data["process_time"] = datetime.now()

    for field in update_data:
        setattr(db_obj, field, update_data[field])

    db.add(db_obj)

    # 检查是否为异步会话
    from sqlalchemy.ext.asyncio import AsyncSession
    if isinstance(db, AsyncSession):
        # 异步会话 - 不在这里提交，由调用方控制事务
        pass
    else:
        # 同步会话
        db.commit()
        db.refresh(db_obj)

    return db_obj


def get_card_record(
    db: Session, record_id: str, merchant_id: Optional[int] = None
) -> Optional[CardRecord]:
    """
    获取卡记录

    Args:
        db: 数据库会话
        record_id: 记录ID (字符串形式的UUID)
        merchant_id: 商家ID（用于多租户隔离）

    Returns:
        Optional[CardRecord]: 卡记录
    """
    query = db.query(CardRecord).filter(CardRecord.id == record_id)

    if merchant_id is not None:
        query = query.filter(CardRecord.merchant_id == merchant_id)

    return query.first()


async def get_card_record_async(
    db: AsyncSession, record_id: str, merchant_id: Optional[int] = None
) -> Optional[CardRecord]:
    """
    异步获取卡记录

    Args:
        db: 异步数据库会话
        record_id: 记录ID (字符串形式的UUID)
        merchant_id: 商家ID（用于多租户隔离）

    Returns:
        Optional[CardRecord]: 卡记录
    """
    stmt = select(CardRecord).filter(CardRecord.id == record_id)

    if merchant_id is not None:
        stmt = stmt.filter(CardRecord.merchant_id == merchant_id)

    result = await db.execute(stmt)
    return result.scalar_one_or_none()


def get_card_record_by_trace_id(
    db: Session, trace_id: str, merchant_id: Optional[int] = None
) -> Optional[CardRecord]:
    """
    通过追踪ID获取卡记录

    Args:
        db: 数据库会话
        trace_id: 追踪ID
        merchant_id: 商家ID（用于多租户隔离）

    Returns:
        Optional[CardRecord]: 卡记录
    """
    query = db.query(CardRecord).filter(CardRecord.trace_id == trace_id)

    if merchant_id is not None:
        query = query.filter(CardRecord.merchant_id == merchant_id)

    return query.first()


def get_card_records(
    db: Session,
    filters: Optional[CardRecordFilter] = None,
    skip: int = 0,
    limit: int = 100,
    merchant_id: Optional[int] = None,
) -> List[CardRecord]:
    """
    获取卡记录列表 - 优化版本，避免N+1查询

    Args:
        db: 数据库会话
        filters: 过滤条件
        skip: 跳过记录数
        limit: 返回记录数
        merchant_id: 商家ID（用于多租户隔离）

    Returns:
        List[CardRecord]: 卡记录列表
    """
    from sqlalchemy.orm import selectinload

    # 使用预加载避免N+1查询
    query = db.query(CardRecord).options(
        selectinload(CardRecord.merchant),
        selectinload(CardRecord.department),
        selectinload(CardRecord.walmart_ck)
    )

    # 应用过滤条件
    if filters:
        query = _apply_filters(query, filters)

    # 应用商家ID过滤
    if merchant_id is not None:
        query = query.filter(CardRecord.merchant_id == merchant_id)

    # 按请求时间倒序
    query = query.order_by(CardRecord.created_at.desc())

    return query.offset(skip).limit(limit).all()


def count_card_records(
    db: Session,
    filters: Optional[CardRecordFilter] = None,
    merchant_id: Optional[int] = None,
) -> int:
    """
    统计卡记录数量

    Args:
        db: 数据库会话
        filters: 过滤条件
        merchant_id: 商家ID（用于多租户隔离）

    Returns:
        int: 记录数量
    """
    query = db.query(func.count(CardRecord.id))

    # 应用过滤条件
    if filters:
        query = _apply_filters(query, filters)

    # 应用商家ID过滤
    if merchant_id is not None:
        query = query.filter(CardRecord.merchant_id == merchant_id)

    return query.scalar()


def _get_base_conditions(merchant_id: Optional[int] = None) -> List:
    """获取基本查询条件"""
    base_condition = []
    if merchant_id is not None:
        base_condition.append(CardRecord.merchant_id == merchant_id)
    return base_condition


def _get_today_range() -> Tuple[datetime, datetime, str, str]:
    """获取今天的日期范围"""
    # 修复时区问题：使用上海时区的当前日期
    from app.utils.time_utils import get_current_time
    today = get_current_time().date()
    today_start = datetime(today.year, today.month, today.day, 0, 0, 0)
    today_end = datetime(today.year, today.month, today.day, 23, 59, 59)
    today_start_str = today_start.strftime("%Y-%m-%d %H:%M:%S")
    today_end_str = today_end.strftime("%Y-%m-%d %H:%M:%S")
    return today_start, today_end, today_start_str, today_end_str


def _count_by_status(db: Session, status: CardStatus, base_condition: List) -> int:
    """按状态统计记录数"""
    return (
        db.query(func.count(CardRecord.id))
        .filter(CardRecord.status == status, *base_condition)
        .scalar()
        or 0
    )


def _count_today_by_status(
    db: Session,
    status: CardStatus,
    today_start: datetime,
    today_end: datetime,
    today_start_str: str,
    today_end_str: str,
    base_condition: List
) -> int:
    """统计今日指定状态的记录数"""
    try:
        # 尝试使用datetime类型比较
        return (
            db.query(func.count(CardRecord.id))
            .filter(
                CardRecord.status == status,
                CardRecord.request_time.between(today_start, today_end),
                *base_condition,
            )
            .scalar()
            or 0
        )
    except Exception:
        # 如果失败，尝试使用字符串比较
        return (
            db.query(func.count(CardRecord.id))
            .filter(
                CardRecord.status == status,
                CardRecord.request_time >= today_start_str,
                CardRecord.request_time <= today_end_str,
                *base_condition,
            )
            .scalar()
            or 0
        )


def _get_hourly_data(db: Session, today: date, base_condition: List) -> List[Dict[str, Any]]:
    """获取小时分布数据 - 优化版本，使用单次聚合查询"""
    from sqlalchemy import func, extract

    # 今天的开始和结束时间
    today_start = datetime(today.year, today.month, today.day, 0, 0, 0)
    today_end = datetime(today.year, today.month, today.day, 23, 59, 59)

    try:
        # 使用单次聚合查询获取所有小时的数据
        hourly_stats = (
            db.query(
                extract('hour', CardRecord.created_at).label('hour'),
                func.count(CardRecord.id).label('count')
            )
            .filter(
                CardRecord.created_at.between(today_start, today_end),
                *base_condition
            )
            .group_by(extract('hour', CardRecord.created_at))
            .all()
        )

        # 创建小时数据字典
        hour_counts = {int(row.hour): row.count for row in hourly_stats}

    except Exception:
        # 如果聚合查询失败，使用备用方案
        hour_counts = {}
        for hour in range(24):
            hour_start = datetime(today.year, today.month, today.day, hour, 0, 0)
            hour_end = datetime(today.year, today.month, today.day, hour, 59, 59)

            hour_count = (
                db.query(func.count(CardRecord.id))
                .filter(
                    CardRecord.created_at.between(hour_start, hour_end),
                    *base_condition,
                )
                .scalar() or 0
            )
            hour_counts[hour] = hour_count

    # 构建完整的24小时数据
    hourly_data = []
    for hour in range(24):
        hour_str = f"{hour:02d}:00"
        count = hour_counts.get(hour, 0)
        hourly_data.append({"hour": hour_str, "count": count})

    return hourly_data


def get_card_statistics(
    db: Session, merchant_id: Optional[int] = None
) -> Dict[str, int]:
    """
    获取卡记录统计数据

    Args:
        db: 数据库会话
        merchant_id: 商家ID（用于多租户隔离）

    Returns:
        Dict[str, int]: 统计数据
    """
    # 获取基本查询条件
    base_condition = _get_base_conditions(merchant_id)

    # 获取今天的日期范围
    today_start, today_end, today_start_str, today_end_str = _get_today_range()
    # 修复时区问题：使用上海时区的当前日期
    from app.utils.time_utils import get_current_time
    today = get_current_time().date()

    # 统计各种状态的记录数
    total_success = _count_by_status(db, CardStatus.SUCCESS, base_condition)
    total_failed = _count_by_status(db, CardStatus.FAILED, base_condition)
    pending_count = _count_by_status(db, CardStatus.PROCESSING, base_condition)

    # 统计今日数据
    today_success = _count_today_by_status(
        db, CardStatus.SUCCESS, today_start, today_end,
        today_start_str, today_end_str, base_condition
    )
    today_failed = _count_today_by_status(
        db, CardStatus.FAILED, today_start, today_end,
        today_start_str, today_end_str, base_condition
    )

    # 获取小时分布数据
    hourly_data = _get_hourly_data(db, today, base_condition)

    return {
        "totalSuccess": total_success,
        "totalFailed": total_failed,
        "todaySuccess": today_success,
        "todayFailed": today_failed,
        "pendingCount": pending_count,
        "hourlyData": hourly_data,
    }


def _apply_filters(query, filters: CardRecordFilter):
    """应用过滤条件 - 使用过滤器工厂模式"""
    from app.core.filter_factory import create_card_record_filter_factory

    # 创建过滤器工厂
    filter_factory = create_card_record_filter_factory(CardRecord)

    # 转换过滤器对象为字典
    filter_dict = _convert_filter_to_dict(filters)

    # 应用基本过滤条件
    query = filter_factory.apply_filters(query, filter_dict)

    # 应用时间范围过滤
    query = filter_factory.apply_time_range_filters(query, filter_dict)

    return query


def _convert_filter_to_dict(filters: CardRecordFilter) -> dict:
    """将过滤器对象转换为字典"""
    if not filters:
        return {}

    return {
        "card_number": filters.card_number,
        "user_id": filters.user_id,
        "status": filters.status,
        "start_time": filters.start_time,
        "end_time": filters.end_time,
        "merchant_id": filters.merchant_id,
    }


class CRUDCard(CRUDBase[CardRecord, CardRecordCreate, CardRecordUpdate]):
    """卡记录CRUD操作类"""

    def get_card_record(
        self, db: Session, record_id: str, merchant_id: Optional[int] = None
    ) -> Optional[CardRecord]:
        """获取卡记录"""
        return get_card_record(db, record_id, merchant_id)

    def update_card_record(
        self, db: Session, db_obj: CardRecord, obj_in: CardRecordUpdate
    ) -> CardRecord:
        """更新卡记录"""
        return update_card_record(db, db_obj, obj_in)

    def get_by_trace_id(
        self, db: Session, trace_id: str, merchant_id: Optional[int] = None
    ) -> Optional[CardRecord]:
        """通过追踪ID获取卡记录"""
        return get_card_record_by_trace_id(db, trace_id, merchant_id)

    def get_records(
        self,
        db: Session,
        filters: Optional[CardRecordFilter] = None,
        skip: int = 0,
        limit: int = 100,
        merchant_id: Optional[int] = None,
    ) -> List[CardRecord]:
        """获取卡记录列表"""
        return get_card_records(db, filters, skip, limit, merchant_id)

    def count_records(
        self,
        db: Session,
        filters: Optional[CardRecordFilter] = None,
        merchant_id: Optional[int] = None,
    ) -> int:
        """计算卡记录数量"""
        return count_card_records(db, filters, merchant_id)

    def get_statistics(
        self, db: Session, merchant_id: Optional[int] = None
    ) -> Dict[str, int]:
        """获取卡记录统计数据"""
        return get_card_statistics(db, merchant_id)

    @staticmethod
    def get_statistics_optimized(
        db: Session, merchant_id: Optional[int] = None
    ) -> CardStatistics:
        """
        获取卡记录统计数据 - 优化版本，避免N+1查询

        Args:
            db: 数据库会话
            merchant_id: 商家ID，如果为None则统计所有商家的数据

        Returns:
            CardStatistics: 统计数据
        """
        from sqlalchemy import func, case

        # 使用单次聚合查询获取所有统计数据
        query = db.query(
            func.count(CardRecord.id).label('total_count'),
            func.sum(case((CardRecord.status == 'success', 1), else_=0)).label('success_count'),
            func.sum(case((CardRecord.status == 'failed', 1), else_=0)).label('failed_count'),
            func.sum(case((CardRecord.status == 'pending', 1), else_=0)).label('pending_count'),
        )

        if merchant_id is not None:
            query = query.filter(CardRecord.merchant_id == merchant_id)

        stats_result = query.first()

        total_count = stats_result.total_count or 0
        success_count = stats_result.success_count or 0
        failed_count = stats_result.failed_count or 0
        pending_count = stats_result.pending_count or 0

        # 获取小时数据（已优化）
        stats = get_card_statistics(db, merchant_id)
        hourly_data = []

        if "hourlyData" in stats:
            for item in stats["hourlyData"]:
                hourly_data.append({"hour": item["hour"], "count": item["count"]})

        return CardStatistics(
            total_count=total_count,
            success_count=success_count,
            failed_count=failed_count,
            pending_count=pending_count,
            success_rate=(
                round(success_count / total_count * 100, 2) if total_count > 0 else 0
            ),
            hourly_data=hourly_data,
        )


# 创建CRUD实例
card = CRUDCard(CardRecord)
