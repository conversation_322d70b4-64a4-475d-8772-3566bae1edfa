"""
Telegram机器人安全服务
提供多因子认证、IP白名单、异常检测等安全功能
"""

import hashlib
import time
from datetime import datetime, timedelta
from typing import Optional, Dict, Any, List
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_

from app.models.telegram_user import TelegramUser
from app.models.user import User
from app.models.ip_whitelist import <PERSON><PERSON><PERSON><PERSON>elist
from app.models.audit import AuditLog, AuditEventType, AuditLevel
from app.models.totp import TOTPLog
from app.services.totp_service import TOTPService
from app.core.logging import get_logger
from ..config import BotConfig

logger = get_logger(__name__)


class TelegramSecurityService:
    """Telegram安全服务"""
    
    def __init__(self, db: Session, config: BotConfig):
        self.db = db
        self.config = config
        self.totp_service = TOTPService(db)
        
        # 安全配置
        self.max_failed_attempts = config.get('security.max_failed_attempts', 5)
        self.lockout_duration = config.get('security.lockout_duration_minutes', 30)
        self.session_timeout = config.get('security.session_timeout_minutes', 60)
        
    async def verify_user_security(
        self, 
        telegram_user_id: int,
        ip_address: str = None,
        user_agent: str = None,
        require_totp: bool = True
    ) -> Dict[str, Any]:
        """
        综合安全验证
        
        Args:
            telegram_user_id: Telegram用户ID
            ip_address: 客户端IP地址
            user_agent: 用户代理
            require_totp: 是否需要TOTP验证
            
        Returns:
            Dict: 验证结果
        """
        result = {
            "verified": False,
            "requires_totp": False,
            "requires_ip_verification": False,
            "is_locked": False,
            "error_message": None
        }
        
        try:
            # 获取Telegram用户
            telegram_user = self.db.query(TelegramUser).filter_by(
                telegram_user_id=telegram_user_id
            ).first()
            
            if not telegram_user or not telegram_user.system_user:
                result["error_message"] = "用户未验证或未关联系统账户"
                return result
            
            system_user = telegram_user.system_user
            
            # 检查账户锁定状态
            if await self._is_account_locked(system_user.id):
                result["is_locked"] = True
                result["error_message"] = "账户已被锁定，请稍后再试"
                return result
            
            # 检查IP白名单
            if not await self._verify_ip_whitelist(system_user.id, ip_address):
                result["requires_ip_verification"] = True
                result["error_message"] = "IP地址未在白名单中"
                return result
            
            # 检查TOTP（如果启用）
            if require_totp and system_user.totp_enabled:
                result["requires_totp"] = True
                # TOTP验证需要在后续步骤中完成
                return result
            
            # 检查会话有效性
            if not await self._verify_session_validity(telegram_user_id):
                result["error_message"] = "会话已过期，请重新验证"
                return result
            
            # 记录成功访问
            await self._log_access_attempt(
                system_user.id, True, ip_address, user_agent
            )
            
            result["verified"] = True
            return result
            
        except Exception as e:
            logger.error(f"安全验证失败: {e}")
            result["error_message"] = "安全验证过程中发生错误"
            return result
    
    async def verify_totp_for_telegram(
        self, 
        telegram_user_id: int, 
        totp_code: str,
        ip_address: str = None
    ) -> bool:
        """
        为Telegram用户验证TOTP
        
        Args:
            telegram_user_id: Telegram用户ID
            totp_code: TOTP验证码
            ip_address: 客户端IP地址
            
        Returns:
            bool: 验证是否成功
        """
        try:
            telegram_user = self.db.query(TelegramUser).filter_by(
                telegram_user_id=telegram_user_id
            ).first()
            
            if not telegram_user or not telegram_user.system_user:
                return False
            
            system_user = telegram_user.system_user
            
            # 使用TOTP服务验证
            verify_result = self.totp_service.verify_totp(
                system_user, totp_code, ip_address
            )
            
            if verify_result.success:
                # 更新会话
                await self._update_user_session(telegram_user_id)
                
                # 记录成功验证
                await self._log_totp_verification(
                    system_user.id, True, ip_address
                )
                
                return True
            else:
                # 记录失败验证
                await self._log_totp_verification(
                    system_user.id, False, ip_address
                )
                
                # 检查是否需要锁定账户
                await self._check_and_lock_account(system_user.id)
                
                return False
                
        except Exception as e:
            logger.error(f"TOTP验证失败: {e}")
            return False
    
    async def add_ip_to_whitelist(
        self, 
        user_id: int, 
        ip_address: str,
        description: str = None,
        added_by: int = None
    ) -> bool:
        """
        添加IP到白名单
        
        Args:
            user_id: 用户ID
            ip_address: IP地址
            description: 描述
            added_by: 添加人ID
            
        Returns:
            bool: 是否添加成功
        """
        try:
            # 检查是否已存在
            existing = self.db.query(IpWhitelist).filter_by(
                user_id=user_id,
                ip_address=ip_address
            ).first()
            
            if existing:
                return True
            
            # 添加新的IP白名单记录
            whitelist_entry = IpWhitelist(
                user_id=user_id,
                ip_address=ip_address,
                description=description or f"Telegram机器人访问 - {datetime.now()}",
                is_active=True,
                created_by=added_by
            )
            
            self.db.add(whitelist_entry)
            self.db.commit()
            
            # 记录审计日志
            await self._log_security_event(
                user_id, "ip_whitelist_added",
                {"ip_address": ip_address, "added_by": added_by}
            )
            
            logger.info(f"为用户 {user_id} 添加IP白名单: {ip_address}")
            return True
            
        except Exception as e:
            logger.error(f"添加IP白名单失败: {e}")
            self.db.rollback()
            return False
    
    async def detect_anomalous_behavior(
        self, 
        telegram_user_id: int,
        command: str,
        ip_address: str = None
    ) -> Dict[str, Any]:
        """
        检测异常行为
        
        Args:
            telegram_user_id: Telegram用户ID
            command: 执行的命令
            ip_address: IP地址
            
        Returns:
            Dict: 检测结果
        """
        result = {
            "is_anomalous": False,
            "risk_level": "low",
            "reasons": [],
            "recommended_actions": []
        }
        
        try:
            # 检查命令频率
            if await self._check_command_frequency(telegram_user_id, command):
                result["is_anomalous"] = True
                result["risk_level"] = "medium"
                result["reasons"].append("命令执行频率异常")
                result["recommended_actions"].append("限制命令执行频率")
            
            # 检查IP地址变化
            if await self._check_ip_changes(telegram_user_id, ip_address):
                result["is_anomalous"] = True
                result["risk_level"] = "high"
                result["reasons"].append("IP地址频繁变化")
                result["recommended_actions"].append("要求重新验证身份")
            
            # 检查时间模式
            if await self._check_time_patterns(telegram_user_id):
                result["is_anomalous"] = True
                result["risk_level"] = "low"
                result["reasons"].append("非正常工作时间访问")
                result["recommended_actions"].append("记录异常访问时间")
            
            # 如果检测到异常，记录日志
            if result["is_anomalous"]:
                await self._log_anomaly_detection(
                    telegram_user_id, command, ip_address, result
                )
            
            return result
            
        except Exception as e:
            logger.error(f"异常行为检测失败: {e}")
            return result
    
    async def _is_account_locked(self, user_id: int) -> bool:
        """检查账户是否被锁定"""
        # 检查最近的失败登录次数
        recent_failures = self.db.query(AuditLog).filter(
            and_(
                AuditLog.user_id == user_id,
                AuditLog.action.in_(["login_failed", "totp_failed"]),
                AuditLog.created_at > datetime.now() - timedelta(minutes=self.lockout_duration)
            )
        ).count()
        
        return recent_failures >= self.max_failed_attempts
    
    async def _verify_ip_whitelist(self, user_id: int, ip_address: str) -> bool:
        """验证IP白名单"""
        if not ip_address:
            return True  # 如果没有IP信息，暂时允许
        
        # 检查是否在白名单中
        whitelist_entry = self.db.query(IpWhitelist).filter(
            and_(
                IpWhitelist.user_id == user_id,
                IpWhitelist.ip_address == ip_address,
                IpWhitelist.is_active == True
            )
        ).first()
        
        return whitelist_entry is not None
    
    async def _verify_session_validity(self, telegram_user_id: int) -> bool:
        """验证会话有效性"""
        # 这里可以实现会话管理逻辑
        # 例如：检查最后活动时间、会话令牌等
        return True  # 临时实现
    
    async def _update_user_session(self, telegram_user_id: int):
        """更新用户会话"""
        # 更新最后活动时间等会话信息
        pass
    
    async def _check_command_frequency(self, telegram_user_id: int, command: str) -> bool:
        """检查命令执行频率"""
        # 检查最近1分钟内的命令执行次数
        recent_commands = self.db.query(AuditLog).filter(
            and_(
                AuditLog.resource_id == str(telegram_user_id),
                AuditLog.action == "command_executed",
                AuditLog.details.contains({"command": command}),
                AuditLog.created_at > datetime.now() - timedelta(minutes=1)
            )
        ).count()
        
        return recent_commands > 10  # 1分钟内超过10次认为异常
    
    async def _check_ip_changes(self, telegram_user_id: int, ip_address: str) -> bool:
        """检查IP地址变化"""
        if not ip_address:
            return False
        
        # 检查最近1小时内使用的不同IP数量
        recent_ips = self.db.query(AuditLog.ip_address).filter(
            and_(
                AuditLog.resource_id == str(telegram_user_id),
                AuditLog.created_at > datetime.now() - timedelta(hours=1),
                AuditLog.ip_address.isnot(None)
            )
        ).distinct().count()
        
        return recent_ips > 3  # 1小时内超过3个不同IP认为异常
    
    async def _check_time_patterns(self, telegram_user_id: int) -> bool:
        """检查时间模式"""
        current_hour = datetime.now().hour
        # 非工作时间（晚上10点到早上6点）
        return current_hour >= 22 or current_hour <= 6
    
    async def _log_access_attempt(
        self, 
        user_id: int, 
        success: bool, 
        ip_address: str = None,
        user_agent: str = None
    ):
        """记录访问尝试"""
        await self._log_security_event(
            user_id,
            "access_attempt",
            {
                "success": success,
                "ip_address": ip_address,
                "user_agent": user_agent
            }
        )
    
    async def _log_totp_verification(
        self, 
        user_id: int, 
        success: bool, 
        ip_address: str = None
    ):
        """记录TOTP验证"""
        await self._log_security_event(
            user_id,
            "totp_verification",
            {
                "success": success,
                "ip_address": ip_address
            }
        )
    
    async def _log_anomaly_detection(
        self, 
        telegram_user_id: int, 
        command: str, 
        ip_address: str,
        detection_result: Dict[str, Any]
    ):
        """记录异常检测"""
        await self._log_security_event(
            None,  # 可能没有关联的系统用户ID
            "anomaly_detected",
            {
                "telegram_user_id": telegram_user_id,
                "command": command,
                "ip_address": ip_address,
                "detection_result": detection_result
            }
        )
    
    async def _log_security_event(
        self, 
        user_id: int, 
        event_type: str, 
        details: Dict[str, Any]
    ):
        """记录安全事件"""
        try:
            audit_log = AuditLog(
                event_type=AuditEventType.SECURITY.value,
                level=AuditLevel.WARNING.value if "failed" in event_type or "anomaly" in event_type else AuditLevel.INFO.value,
                resource_type="telegram_security",
                resource_id=str(user_id) if user_id else None,
                action=event_type,
                details=details,
                user_id=user_id,
                ip_address=details.get("ip_address"),
                user_agent=details.get("user_agent")
            )
            self.db.add(audit_log)
            self.db.commit()
            
        except Exception as e:
            logger.error(f"记录安全事件失败: {e}")
    
    async def _check_and_lock_account(self, user_id: int):
        """检查并锁定账户"""
        if await self._is_account_locked(user_id):
            # 这里可以实现账户锁定逻辑
            # 例如：禁用用户、发送通知等
            logger.warning(f"用户 {user_id} 因多次失败尝试被锁定")
            
            await self._log_security_event(
                user_id, "account_locked",
                {"reason": "多次失败尝试"}
            )
