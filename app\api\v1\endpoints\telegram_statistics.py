"""
Telegram机器人统计查询API端点
"""

from datetime import datetime, date, timedelta
from typing import Dict, Any, Optional, List
from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session
from sqlalchemy import func, case
from pydantic import BaseModel, Field

from app.api.deps import get_db, get_current_user
from app.models.user import User
from app.models.merchant import Merchant
from app.models.telegram_group import TelegramGroup, BindStatus
from app.models.telegram_user import TelegramUser
from app.models.card_record import CardRecord, CardStatus
from app.models.telegram_bot_log import TelegramBotLog, LogStatus
from app.models.department import Department
from app.models.base import local_now
from app.core.logging import get_logger

logger = get_logger(__name__)

router = APIRouter()


async def _get_department_and_children_ids(db: Session, department_id: int) -> List[int]:
    """获取部门及其所有子部门的ID列表（递归）"""
    try:
        from sqlalchemy import text

        # 使用递归CTE查询获取部门及其所有子部门
        sql = text("""
            WITH RECURSIVE department_tree AS (
                -- 基础查询：指定部门
                SELECT id, parent_id, merchant_id
                FROM departments
                WHERE id = :department_id

                UNION ALL

                -- 递归查询：子部门的子部门
                SELECT d.id, d.parent_id, d.merchant_id
                FROM departments d
                INNER JOIN department_tree dt ON d.parent_id = dt.id
            )
            SELECT id FROM department_tree
        """)

        result = db.execute(sql, {'department_id': department_id})
        department_ids = [row.id for row in result.fetchall()]

        logger.info(f"部门 {department_id} 及其子部门ID列表: {department_ids}")
        return department_ids

    except Exception as e:
        logger.error(f"获取部门层级ID失败: {e}")
        # 如果递归查询失败，至少返回原部门ID
        return [department_id]


def optional_int_query(default: Optional[int] = None, description: str = ""):
    """
    自定义的可选整数查询参数，正确处理空字符串
    """
    def _optional_int_query(value: Optional[str] = Query(default, description=description)):
        if value is None or value == "":
            return None
        try:
            return int(value)
        except (ValueError, TypeError):
            raise HTTPException(
                status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
                detail=f"Invalid integer value: {value}"
            )
    return _optional_int_query


class StatisticsRequest(BaseModel):
    """统计查询请求"""
    chat_id: int = Field(..., description="群组ID")
    query_type: str = Field(..., description="查询类型：daily_summary, weekly_summary, monthly_summary, custom_range")
    start_date: Optional[str] = Field(None, description="开始日期 (YYYY-MM-DD)")
    end_date: Optional[str] = Field(None, description="结束日期 (YYYY-MM-DD)")
    include_details: bool = Field(False, description="是否包含详细数据")


@router.get("/statistics")
async def get_telegram_statistics_admin(
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db),
    merchant_id: Optional[int] = Depends(optional_int_query(None, "商户ID")),
    department_id: Optional[int] = Depends(optional_int_query(None, "部门ID")),
    group_id: Optional[int] = Depends(optional_int_query(None, "群组ID")),
    query_type: str = Query("daily_summary", description="查询类型"),
    start_date: Optional[str] = Query(None, description="开始日期"),
    end_date: Optional[str] = Query(None, description="结束日期"),
    start_time: Optional[str] = Query(None, description="开始时间"),
    end_time: Optional[str] = Query(None, description="结束时间"),
    include_details: bool = Query(False, description="是否包含详细数据")
):
    """
    管理界面统计查询接口
    基于用户权限进行数据访问控制
    """
    try:
        # 检查权限
        if not current_user.has_permission("api:telegram:config"):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="权限不足"
            )

        # 如果提供了group_id，优先使用群组信息
        if group_id:
            # 查询群组信息
            group = db.query(TelegramGroup).filter_by(id=group_id).first()
            if not group:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="群组不存在"
                )

            # 验证访问权限
            if not current_user.can_access_merchant_data(group.merchant_id):
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail="无权限访问该群组"
                )

            # 使用群组的商户和部门信息
            merchant_id = group.merchant_id
            department_id = group.department_id

        # 验证商户权限
        if merchant_id and not current_user.can_access_merchant_data(merchant_id):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="无权限访问该商户"
            )

        # 如果没有指定商户，获取用户可访问的商户列表
        if not merchant_id:
            # 超级管理员可以访问所有商户，选择第一个商户进行统计
            if current_user.is_superuser:
                from app.crud.merchant import merchant as merchant_crud
                merchants = merchant_crud.get_multi(db)
                if not merchants:
                    return {
                            "total_count": 0,
                            "success_count": 0,
                            "failed_count": 0,
                            "pending_count": 0,
                            "binding_count": 0,
                            "success_rate": 0.0,
                            "total_amount": 0,
                            "success_amount": 0
                        }
                merchant_id = merchants[0].id
            else:
                accessible_merchants = current_user.get_accessible_merchant_ids()
                if not accessible_merchants:
                    return {
                            "total_count": 0,
                            "success_count": 0,
                            "failed_count": 0,
                            "pending_count": 0,
                            "binding_count": 0,
                            "success_rate": 0.0,
                            "total_amount": 0,
                            "success_amount": 0
                        }
                # 使用第一个可访问的商户
                merchant_id = accessible_merchants[0]

        # 构造请求对象，优先使用start_time/end_time，fallback到start_date/end_date
        request_data = StatisticsRequest(
            chat_id=0,  # 管理界面查询不需要chat_id
            query_type=query_type,
            start_date=start_time or start_date,
            end_date=end_time or end_date,
            include_details=include_details
        )

        # 根据查询类型确定日期范围
        start_date_obj, end_date_obj = _get_date_range(request_data)

        # 获取绑卡统计数据
        card_statistics = await _get_card_statistics(
            db,
            merchant_id,
            department_id,
            start_date_obj,
            end_date_obj,
            include_details
        )

        # 获取群组统计数据
        group_statistics = await _get_group_statistics(
            db,
            current_user,
            merchant_id,
            department_id,
            start_date_obj,
            end_date_obj
        )

        # 获取用户统计数据
        user_statistics = await _get_user_statistics(
            db,
            current_user,
            merchant_id,
            department_id,
            start_date_obj,
            end_date_obj
        )

        # 获取命令统计数据
        command_statistics = await _get_command_overview_statistics(
            db,
            current_user,
            merchant_id,
            department_id,
            start_date_obj,
            end_date_obj
        )

        # 合并所有统计数据
        combined_statistics = {
            **card_statistics,
            **group_statistics,
            **user_statistics,
            **command_statistics
        }

        return combined_statistics

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"管理界面统计查询失败: {e}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="统计查询失败"
        )


@router.get("/groups/{group_id}/stats")
async def get_group_statistics(
    group_id: int,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db),
    query_type: str = Query("daily_summary", description="查询类型"),
    start_date: Optional[str] = Query(None, description="开始日期"),
    end_date: Optional[str] = Query(None, description="结束日期"),
    include_details: bool = Query(False, description="是否包含详细数据")
):
    """
    获取特定群组的统计数据
    """
    try:
        # 检查权限
        if not current_user.has_permission("api:telegram:config"):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="权限不足"
            )

        # 查询群组
        group = db.query(TelegramGroup).filter_by(id=group_id).first()
        if not group:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="群组不存在"
            )

        # 验证访问权限
        if not current_user.can_access_merchant_data(group.merchant_id):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="无权限访问该群组"
            )

        # 构造请求对象
        request_data = StatisticsRequest(
            chat_id=group.chat_id,
            query_type=query_type,
            start_date=start_date,
            end_date=end_date,
            include_details=include_details
        )

        # 根据查询类型确定日期范围
        start_date_obj, end_date_obj = _get_date_range(request_data)

        # 获取统计数据
        statistics = await _get_card_statistics(
            db,
            group.merchant_id,
            group.department_id,
            start_date_obj,
            end_date_obj,
            include_details
        )

        return {
            "group_id": group_id,
            "group_name": group.chat_title,
            "merchant_name": group.merchant.name if group.merchant else "未知商户",
            "department_name": group.department.name if group.department else "全部部门",
            "query_period": _format_query_period(query_type, start_date_obj, end_date_obj),
            "statistics": statistics,
            "query_time": local_now().isoformat()
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取群组统计数据失败: {e}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取群组统计数据失败"
        )


@router.post("/statistics")
async def get_telegram_statistics_bot(
    request: StatisticsRequest,
    db: Session = Depends(get_db)
):
    """
    机器人专用统计查询接口
    基于群组绑定信息自动进行权限控制
    """
    try:
        # 验证群组绑定
        group = db.query(TelegramGroup).filter_by(
            chat_id=request.chat_id,
            bind_status=BindStatus.ACTIVE
        ).first()
        
        if not group:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="群组未绑定或状态异常"
            )
        
        # 更新群组最后活跃时间
        group.update_last_active()
        db.commit()
        
        # 根据查询类型确定日期范围
        start_date, end_date = _get_date_range(request)
        
        # 获取统计数据
        statistics = await _get_card_statistics(
            db, 
            group.merchant_id, 
            group.department_id,
            start_date, 
            end_date,
            request.include_details
        )
        
        # 构建响应数据
        response_data = {
            "merchant_name": group.merchant.name if group.merchant else "未知商户",
            "department_name": group.department.name if group.department else "全部部门",
            "query_period": _format_query_period(request.query_type, start_date, end_date),
            "statistics": statistics,
            "query_time": local_now().isoformat()
        }
        
        return response_data
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"统计查询失败: {e}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="统计查询失败"
        )


async def _get_card_statistics(
    db: Session,
    merchant_id: int,
    department_id: Optional[int],
    start_date: date,
    end_date: date,
    include_details: bool = False
) -> Dict[str, Any]:
    """获取卡记录统计数据 - 支持部门层级查询"""

    # 【修复】使用正确的时间范围计算
    from app.utils.datetime_utils import get_date_range_for_query, debug_time_range, is_today

    # 获取正确的时间范围
    query_start, query_end = get_date_range_for_query(start_date, end_date)

    # 记录调试信息（仅今日数据）
    if is_today(start_date) and start_date == end_date:
        debug_info = debug_time_range(start_date, end_date)
        logger.info(f"[STATS_TODAY_DEBUG] 今日统计查询时间范围: {debug_info}")

    # 构建基础查询 - 使用正确的datetime范围
    query = db.query(CardRecord).filter(
        CardRecord.merchant_id == merchant_id,
        CardRecord.created_at >= query_start,
        CardRecord.created_at <= query_end
    )

    # 部门过滤 - 支持递归查询子部门
    department_ids = None
    if department_id:
        # 获取部门及其所有子部门的ID列表
        department_ids = await _get_department_and_children_ids(db, department_id)
        if department_ids:
            query = query.filter(CardRecord.department_id.in_(department_ids))
        else:
            # 如果没有找到部门，只查询指定部门
            query = query.filter(CardRecord.department_id == department_id)
            department_ids = [department_id]

    # 获取总数统计
    total_count = query.count()

    # 按状态统计 - 使用相同的部门过滤逻辑和时间范围
    # 【修复数据一致性】使用actual_amount字段，与后台管理页面保持一致
    status_stats = db.query(
        CardRecord.status,
        func.count(CardRecord.id).label('count'),
        func.sum(case((CardRecord.status == CardStatus.SUCCESS, CardRecord.actual_amount), else_=CardRecord.amount)).label('total_amount')
    ).filter(
        CardRecord.merchant_id == merchant_id,
        CardRecord.created_at >= query_start,
        CardRecord.created_at <= query_end
    )

    # 使用相同的部门过滤逻辑
    if department_ids:
        status_stats = status_stats.filter(CardRecord.department_id.in_(department_ids))
    
    status_stats = status_stats.group_by(CardRecord.status).all()

    # 【修复总请求金额】单独计算总请求金额，与后台管理页面保持一致
    total_request_amount_query = db.query(
        func.sum(CardRecord.amount).label('total_request_amount')
    ).filter(
        CardRecord.merchant_id == merchant_id,
        CardRecord.created_at >= query_start,
        CardRecord.created_at <= query_end
    )

    # 使用相同的部门过滤逻辑
    if department_ids:
        total_request_amount_query = total_request_amount_query.filter(CardRecord.department_id.in_(department_ids))

    total_request_amount_result = total_request_amount_query.first()
    total_request_amount = max(0, total_request_amount_result.total_request_amount or 0)

    # 初始化统计数据，确保数据类型正确
    statistics = {
        "total_count": max(0, total_count or 0),  # 确保非负数
        "success_count": 0,
        "failed_count": 0,
        "pending_count": 0,
        "binding_count": 0,
        "success_rate": 0.0,
        "total_amount": total_request_amount,  # 使用单独计算的总请求金额
        "success_amount": 0
    }
    
    # 处理状态统计，确保数据有效性
    for status, count, amount in status_stats:
        # 确保数值有效性
        count = max(0, count or 0)
        amount = max(0, amount or 0)
        # 【修复】不再累加到total_amount，因为已经单独计算了总请求金额

        if status == CardStatus.SUCCESS:
            statistics["success_count"] = count
            statistics["success_amount"] = amount
        elif status == CardStatus.FAILED:
            statistics["failed_count"] = count
        elif status == CardStatus.PENDING:
            statistics["pending_count"] = count
        elif status == CardStatus.BINDING:
            statistics["binding_count"] = count
    
    # 计算成功率，确保在有效范围内
    if total_count > 0:
        success_rate = (statistics["success_count"] / total_count) * 100
        # 确保成功率在0-100%范围内
        success_rate = max(0.0, min(100.0, success_rate))
        statistics["success_rate"] = round(success_rate, 1)
    else:
        statistics["success_rate"] = 0.0
    
    # 如果需要详细数据
    if include_details:
        statistics["details"] = await _get_detailed_statistics(
            db, merchant_id, department_id, start_date, end_date
        )
    
    return statistics


async def _get_detailed_statistics(
    db: Session,
    merchant_id: int,
    department_id: Optional[int],
    start_date: date,
    end_date: date
) -> Dict[str, Any]:
    """获取详细统计数据"""
    
    # 按日期统计
    daily_stats_query = db.query(
        func.date(CardRecord.created_at).label('date'),
        func.count(CardRecord.id).label('count'),
        func.sum(func.case([(CardRecord.status == CardStatus.SUCCESS, 1)], else_=0)).label('success_count')
    ).filter(
        CardRecord.merchant_id == merchant_id,
        CardRecord.created_at >= start_date,
        CardRecord.created_at < end_date + timedelta(days=1)
    )
    
    if department_id:
        # 获取部门及其所有子部门的ID列表
        department_ids = await _get_department_and_children_ids(db, department_id)
        if department_ids:
            daily_stats_query = daily_stats_query.filter(CardRecord.department_id.in_(department_ids))
        else:
            daily_stats_query = daily_stats_query.filter(CardRecord.department_id == department_id)
    
    daily_stats = daily_stats_query.group_by(func.date(CardRecord.created_at)).all()
    
    # 按小时统计（仅当查询范围小于等于7天时）
    hourly_stats = []
    if (end_date - start_date).days <= 7:
        hourly_stats_query = db.query(
            func.hour(CardRecord.created_at).label('hour'),
            func.count(CardRecord.id).label('count')
        ).filter(
            CardRecord.merchant_id == merchant_id,
            CardRecord.created_at >= start_date,
            CardRecord.created_at < end_date + timedelta(days=1)
        )
        
        if department_id:
            hourly_stats_query = hourly_stats_query.filter(CardRecord.department_id == department_id)
        
        hourly_stats = hourly_stats_query.group_by(func.hour(CardRecord.created_at)).all()
    
    return {
        "daily_breakdown": [
            {
                "date": str(date_val),
                "total_count": count,
                "success_count": success_count,
                "success_rate": round((success_count / count) * 100, 1) if count > 0 else 0
            }
            for date_val, count, success_count in daily_stats
        ],
        "hourly_breakdown": [
            {
                "hour": hour,
                "count": count
            }
            for hour, count in hourly_stats
        ] if hourly_stats else []
    }


def _get_date_range(request: StatisticsRequest) -> tuple[date, date]:
    """根据查询类型获取日期范围"""
    today = local_now().date()
    
    if request.query_type == "daily_summary":
        return today, today
    
    elif request.query_type == "weekly_summary":
        # 本周（周一到周日）
        start_of_week = today - timedelta(days=today.weekday())
        end_of_week = start_of_week + timedelta(days=6)
        return start_of_week, end_of_week
    
    elif request.query_type == "monthly_summary":
        # 本月
        start_of_month = today.replace(day=1)
        if today.month == 12:
            end_of_month = today.replace(year=today.year + 1, month=1, day=1) - timedelta(days=1)
        else:
            end_of_month = today.replace(month=today.month + 1, day=1) - timedelta(days=1)
        return start_of_month, end_of_month
    
    elif request.query_type == "custom_range":
        if not request.start_date or not request.end_date:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="自定义查询需要提供开始和结束日期"
            )
        
        try:
            start_date = datetime.strptime(request.start_date, '%Y-%m-%d').date()
            end_date = datetime.strptime(request.end_date, '%Y-%m-%d').date()
            
            if start_date > end_date:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="开始日期不能晚于结束日期"
                )
            
            # 限制查询范围（最多90天）
            if (end_date - start_date).days > 90:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="查询时间范围不能超过90天"
                )
            
            return start_date, end_date
            
        except ValueError:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="日期格式错误，请使用 YYYY-MM-DD 格式"
            )
    
    else:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="不支持的查询类型"
        )


def _format_query_period(query_type: str, start_date: date, end_date: date) -> str:
    """格式化查询时间段"""
    if query_type == "daily_summary":
        return start_date.strftime('%Y年%m月%d日')
    elif query_type == "weekly_summary":
        return f"{start_date.strftime('%Y年%m月%d日')} 至 {end_date.strftime('%Y年%m月%d日')}"
    elif query_type == "monthly_summary":
        return start_date.strftime('%Y年%m月')
    elif query_type == "custom_range":
        return f"{start_date.strftime('%Y年%m月%d日')} 至 {end_date.strftime('%Y年%m月%d日')}"
    else:
        return "未知时间段"


async def _get_group_statistics(
    db: Session,
    current_user: User,
    merchant_id: int,
    department_id: Optional[int],
    start_date: date,
    end_date: date
) -> Dict[str, Any]:
    """获取群组统计数据"""

    # 构建群组查询
    query = db.query(TelegramGroup).filter(
        TelegramGroup.merchant_id == merchant_id
    )

    # 部门过滤
    if department_id:
        query = query.filter(TelegramGroup.department_id == department_id)

    # 权限过滤
    if not current_user.is_superuser:
        accessible_merchant_ids = current_user.get_accessible_merchant_ids()
        query = query.filter(TelegramGroup.merchant_id.in_(accessible_merchant_ids))

    # 获取总群组数
    total_groups = query.count()

    # 获取活跃群组数（绑定状态为活跃的群组）
    active_groups = query.filter(TelegramGroup.bind_status == BindStatus.ACTIVE).count()

    # 获取在指定时间范围内有活动的群组数
    active_in_period_query = query.filter(
        TelegramGroup.bind_status == BindStatus.ACTIVE,
        TelegramGroup.last_active_time >= start_date,
        TelegramGroup.last_active_time < end_date + timedelta(days=1)
    )
    active_groups_in_period = active_in_period_query.count()

    return {
        "total_groups": total_groups,
        "active_groups": active_groups,
        "active_groups_in_period": active_groups_in_period
    }


async def _get_user_statistics(
    db: Session,
    current_user: User,
    merchant_id: int,
    department_id: Optional[int],
    start_date: date,
    end_date: date
) -> Dict[str, Any]:
    """获取用户统计数据"""

    from app.models.telegram_user import TelegramUser, VerificationStatus

    # 构建用户查询 - 通过命令日志关联获取用户
    user_ids_query = db.query(TelegramBotLog.user_id).join(
        TelegramGroup, TelegramBotLog.chat_id == TelegramGroup.chat_id
    ).filter(
        TelegramGroup.merchant_id == merchant_id
    )

    # 部门过滤
    if department_id:
        user_ids_query = user_ids_query.filter(TelegramGroup.department_id == department_id)

    # 权限过滤
    if not current_user.is_superuser:
        accessible_merchant_ids = current_user.get_accessible_merchant_ids()
        user_ids_query = user_ids_query.filter(TelegramGroup.merchant_id.in_(accessible_merchant_ids))

    user_ids_query = user_ids_query.distinct()

    # 获取这些用户的详细信息
    query = db.query(TelegramUser).filter(
        TelegramUser.telegram_user_id.in_(user_ids_query)
    )

    # 获取总用户数
    total_users = query.count()

    # 获取已验证用户数
    verified_users = query.filter(
        TelegramUser.verification_status == VerificationStatus.VERIFIED
    ).count()

    # 获取在指定时间范围内活跃的用户数（有命令执行记录）
    active_users_query = db.query(TelegramBotLog.user_id).filter(
        TelegramBotLog.created_at >= start_date,
        TelegramBotLog.created_at < end_date + timedelta(days=1)
    ).join(
        TelegramGroup, TelegramBotLog.chat_id == TelegramGroup.chat_id
    ).filter(
        TelegramGroup.merchant_id == merchant_id
    )

    if department_id:
        active_users_query = active_users_query.filter(TelegramGroup.department_id == department_id)

    if not current_user.is_superuser:
        accessible_merchant_ids = current_user.get_accessible_merchant_ids()
        active_users_query = active_users_query.filter(TelegramGroup.merchant_id.in_(accessible_merchant_ids))

    active_users = active_users_query.distinct().count()

    return {
        "total_users": total_users,
        "verified_users": verified_users,
        "active_users": active_users
    }


async def _get_command_overview_statistics(
    db: Session,
    current_user: User,
    merchant_id: int,
    department_id: Optional[int],
    start_date: date,
    end_date: date
) -> Dict[str, Any]:
    """获取命令概览统计数据"""

    # 构建命令日志查询
    query = db.query(TelegramBotLog).filter(
        TelegramBotLog.created_at >= start_date,
        TelegramBotLog.created_at < end_date + timedelta(days=1)
    ).join(
        TelegramGroup, TelegramBotLog.chat_id == TelegramGroup.chat_id
    ).filter(
        TelegramGroup.merchant_id == merchant_id
    )

    # 部门过滤
    if department_id:
        query = query.filter(TelegramGroup.department_id == department_id)

    # 权限过滤
    if not current_user.is_superuser:
        accessible_merchant_ids = current_user.get_accessible_merchant_ids()
        query = query.filter(TelegramGroup.merchant_id.in_(accessible_merchant_ids))

    # 获取总命令数
    total_commands = query.count()

    # 获取成功命令数
    successful_commands = query.filter(
        TelegramBotLog.status == LogStatus.SUCCESS.value
    ).count()

    # 计算命令成功率，确保在有效范围内
    command_success_rate = 0.0
    if total_commands > 0:
        command_success_rate = (successful_commands / total_commands) * 100
        # 确保成功率在0-100%范围内
        command_success_rate = max(0.0, min(100.0, command_success_rate))

    return {
        "total_commands": total_commands,
        "successful_commands": successful_commands,
        "command_success_rate": round(command_success_rate, 1)
    }


@router.get("/statistics/commands")
async def get_command_statistics(
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(20, ge=1, le=100, description="每页数量"),
    group_id: Optional[int] = Depends(optional_int_query(None, "群组ID")),
    merchant_id: Optional[int] = Depends(optional_int_query(None, "商户ID")),
    start_time: Optional[str] = Query(None, description="开始时间"),
    end_time: Optional[str] = Query(None, description="结束时间"),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    获取命令统计数据
    """
    try:
        # 检查权限
        if not current_user.has_permission("api:telegram:config"):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="权限不足"
            )

        # 设置默认时间范围（最近7天）
        if not start_time or not end_time:
            end_date = local_now().date()
            start_date = end_date - timedelta(days=7)
        else:
            try:
                start_date = datetime.strptime(start_time, '%Y-%m-%d %H:%M:%S').date()
                end_date = datetime.strptime(end_time, '%Y-%m-%d %H:%M:%S').date()
            except ValueError:
                try:
                    start_date = datetime.strptime(start_time, '%Y-%m-%d').date()
                    end_date = datetime.strptime(end_time, '%Y-%m-%d').date()
                except ValueError:
                    raise HTTPException(
                        status_code=status.HTTP_400_BAD_REQUEST,
                        detail="时间格式错误，请使用 YYYY-MM-DD 或 YYYY-MM-DD HH:MM:SS 格式"
                    )

        # 检查用户权限
        if not current_user.is_superuser:
            accessible_merchant_ids = current_user.get_accessible_merchant_ids()
            if not accessible_merchant_ids:
                return {
                    "items": [],
                    "total": 0,
                    "page": page,
                    "page_size": page_size
                }

        # 验证商户权限
        if merchant_id and not current_user.is_superuser and not current_user.can_access_merchant_data(merchant_id):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="无权限访问该商户"
            )

        # 按命令分组统计
        command_stats_query = db.query(
            TelegramBotLog.command,
            func.count(TelegramBotLog.id).label('total_count'),
            func.sum(case((TelegramBotLog.status == LogStatus.SUCCESS.value, 1), else_=0)).label('success_count'),
            func.sum(case((TelegramBotLog.status == LogStatus.ERROR.value, 1), else_=0)).label('error_count'),
            func.sum(case((TelegramBotLog.status == LogStatus.PERMISSION_DENIED.value, 1), else_=0)).label('permission_denied_count'),
            func.avg(TelegramBotLog.execution_time).label('avg_execution_time'),
            func.max(TelegramBotLog.created_at).label('last_executed_at')
        )

        # 应用过滤条件
        command_stats_query = command_stats_query.filter(
            TelegramBotLog.created_at >= start_date,
            TelegramBotLog.created_at < end_date + timedelta(days=1)
        )

        # 加入群组表进行权限过滤
        command_stats_query = command_stats_query.join(TelegramGroup, TelegramBotLog.chat_id == TelegramGroup.chat_id)

        # 权限过滤
        if not current_user.is_superuser:
            accessible_merchant_ids = current_user.get_accessible_merchant_ids()
            command_stats_query = command_stats_query.filter(TelegramGroup.merchant_id.in_(accessible_merchant_ids))

        # 商户过滤
        if merchant_id:
            command_stats_query = command_stats_query.filter(TelegramGroup.merchant_id == merchant_id)

        # 群组过滤
        if group_id:
            command_stats_query = command_stats_query.filter(TelegramGroup.id == group_id)

        # 分组
        command_stats_query = command_stats_query.group_by(TelegramBotLog.command)

        # 获取总数（不同命令的数量）
        total_count = command_stats_query.count()

        # 分页和排序
        command_stats_query = command_stats_query.order_by(func.count(TelegramBotLog.id).desc())
        command_stats_query = command_stats_query.offset((page - 1) * page_size).limit(page_size)

        # 执行查询
        results = command_stats_query.all()

        # 格式化结果
        items = []
        for result in results:
            success_rate = 0.0
            if result.total_count > 0:
                success_rate = (result.success_count / result.total_count) * 100
                # 确保成功率在0-100%范围内
                success_rate = max(0.0, min(100.0, success_rate))
                success_rate = round(success_rate, 1)

            items.append({
                "command": result.command,
                "total_count": result.total_count,
                "success_count": result.success_count,
                "error_count": result.error_count,
                "permission_denied_count": result.permission_denied_count,
                "success_rate": success_rate,
                "avg_execution_time": round(result.avg_execution_time or 0, 1),
                "last_executed_at": result.last_executed_at.isoformat() if result.last_executed_at else None
            })

        return {
            "items": items,
            "total": total_count,
            "page": page,
            "page_size": page_size
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取命令统计数据失败: {e}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取命令统计数据失败"
        )


@router.get("/statistics/groups")
async def get_group_statistics_list(
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(20, ge=1, le=100, description="每页数量"),
    group_id: Optional[int] = Depends(optional_int_query(None, "群组ID")),
    merchant_id: Optional[int] = Depends(optional_int_query(None, "商户ID")),
    start_time: Optional[str] = Query(None, description="开始时间"),
    end_time: Optional[str] = Query(None, description="结束时间"),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    获取群组统计列表数据
    """
    try:
        # 检查权限
        if not current_user.has_permission("api:telegram:config"):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="权限不足"
            )

        # 解析时间范围
        if start_time and end_time:
            try:
                start_date = datetime.fromisoformat(start_time.replace('Z', '+00:00')).date()
                end_date = datetime.fromisoformat(end_time.replace('Z', '+00:00')).date()
            except ValueError:
                start_date = datetime.now().date() - timedelta(days=7)
                end_date = datetime.now().date()
        else:
            start_date = datetime.now().date() - timedelta(days=7)
            end_date = datetime.now().date()

        # 构建群组查询，关联商户表获取商户名称
        query = db.query(TelegramGroup).join(
            Merchant, TelegramGroup.merchant_id == Merchant.id
        )

        # 商户过滤
        if merchant_id:
            query = query.filter(TelegramGroup.merchant_id == merchant_id)

        # 群组过滤
        if group_id:
            query = query.filter(TelegramGroup.id == group_id)

        # 权限过滤
        if not current_user.is_superuser:
            accessible_merchant_ids = current_user.get_accessible_merchant_ids()
            query = query.filter(TelegramGroup.merchant_id.in_(accessible_merchant_ids))

        # 获取总数
        total = query.count()

        # 分页查询，同时获取商户信息
        offset = (page - 1) * page_size
        groups_with_merchants = query.add_columns(Merchant.name.label('merchant_name')).offset(offset).limit(page_size).all()

        # 构建结果数据
        items = []
        for group, merchant_name in groups_with_merchants:
            # 获取该群组在时间范围内的活动统计
            activity_count = db.query(TelegramBotLog).filter(
                TelegramBotLog.chat_id == group.chat_id,
                TelegramBotLog.created_at >= start_date,
                TelegramBotLog.created_at < end_date + timedelta(days=1)
            ).count()

            # 获取该群组的用户数
            user_count = db.query(TelegramUser).join(
                TelegramBotLog, TelegramUser.telegram_user_id == TelegramBotLog.user_id
            ).filter(
                TelegramBotLog.chat_id == group.chat_id
            ).distinct().count()

            # 计算该群组的成功率
            success_count = db.query(TelegramBotLog).filter(
                TelegramBotLog.chat_id == group.chat_id,
                TelegramBotLog.created_at >= start_date,
                TelegramBotLog.created_at < end_date + timedelta(days=1),
                TelegramBotLog.status == LogStatus.SUCCESS.value
            ).count()

            success_rate = 0.0
            if activity_count > 0:
                success_rate = (success_count / activity_count) * 100
                success_rate = max(0.0, min(100.0, success_rate))
                success_rate = round(success_rate, 1)

            items.append({
                "id": group.id,
                "chat_id": group.chat_id,
                "chat_title": group.chat_title,
                "chat_type": group.chat_type,
                "merchant_id": group.merchant_id,
                "merchant_name": merchant_name,
                "department_id": group.department_id,
                "bind_status": group.bind_status,
                "bind_time": group.bind_time.isoformat() if group.bind_time else None,
                "last_active_time": group.last_active_time.isoformat() if group.last_active_time else None,
                "activity_count": activity_count,
                "user_count": user_count,
                "success_rate": success_rate,
                "created_at": group.created_at.isoformat() if group.created_at else None
            })

        return {
            "items": items,
            "total": total,
            "page": page,
            "page_size": page_size,
            "pages": (total + page_size - 1) // page_size
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取群组统计列表失败: {e}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取群组统计列表失败"
        )


@router.get("/statistics/users")
async def get_user_statistics_list(
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(20, ge=1, le=100, description="每页数量"),
    group_id: Optional[int] = Depends(optional_int_query(None, "群组ID")),
    merchant_id: Optional[int] = Depends(optional_int_query(None, "商户ID")),
    start_time: Optional[str] = Query(None, description="开始时间"),
    end_time: Optional[str] = Query(None, description="结束时间"),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    获取用户统计列表数据
    """
    try:
        # 检查权限
        if not current_user.has_permission("api:telegram:config"):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="权限不足"
            )

        # 解析时间范围
        if start_time and end_time:
            try:
                start_date = datetime.fromisoformat(start_time.replace('Z', '+00:00')).date()
                end_date = datetime.fromisoformat(end_time.replace('Z', '+00:00')).date()
            except ValueError:
                start_date = datetime.now().date() - timedelta(days=7)
                end_date = datetime.now().date()
        else:
            start_date = datetime.now().date() - timedelta(days=7)
            end_date = datetime.now().date()

        # 构建用户查询，使用LEFT JOIN以包含未关联的用户
        query = db.query(TelegramUser).outerjoin(
            User, TelegramUser.system_user_id == User.id
        ).outerjoin(
            Merchant, User.merchant_id == Merchant.id
        )

        # 如果指定了群组，只查询该群组的用户
        if group_id:
            group = db.query(TelegramGroup).filter_by(id=group_id).first()
            if group:
                # 获取在该群组有活动的用户
                user_ids_in_group = db.query(TelegramBotLog.user_id).filter(
                    TelegramBotLog.chat_id == group.chat_id
                ).distinct().subquery()
                query = query.filter(TelegramUser.telegram_user_id.in_(user_ids_in_group))

        # 商户过滤
        if merchant_id:
            query = query.filter(User.merchant_id == merchant_id)

        # 权限过滤
        if not current_user.is_superuser:
            accessible_merchant_ids = current_user.get_accessible_merchant_ids()
            if accessible_merchant_ids:
                # 只显示有权限访问的商户的用户，或者未关联商户的用户
                query = query.filter(
                    (User.merchant_id.in_(accessible_merchant_ids)) |
                    (User.merchant_id.is_(None))
                )

        # 获取总数
        total = query.count()

        # 分页查询，同时获取商户信息
        offset = (page - 1) * page_size
        users_with_merchants = query.add_columns(Merchant.name.label('merchant_name')).offset(offset).limit(page_size).all()

        # 构建结果数据
        items = []
        for user, merchant_name in users_with_merchants:
            # 获取该用户在时间范围内的活动统计
            activity_count = db.query(TelegramBotLog).filter(
                TelegramBotLog.user_id == user.telegram_user_id,
                TelegramBotLog.created_at >= start_date,
                TelegramBotLog.created_at < end_date + timedelta(days=1)
            ).count()

            # 获取该用户参与的群组数
            group_count = db.query(TelegramBotLog.chat_id).filter(
                TelegramBotLog.user_id == user.telegram_user_id
            ).distinct().count()

            # 计算该用户的成功率
            success_count = db.query(TelegramBotLog).filter(
                TelegramBotLog.user_id == user.telegram_user_id,
                TelegramBotLog.created_at >= start_date,
                TelegramBotLog.created_at < end_date + timedelta(days=1),
                TelegramBotLog.status == LogStatus.SUCCESS.value
            ).count()

            failed_count = db.query(TelegramBotLog).filter(
                TelegramBotLog.user_id == user.telegram_user_id,
                TelegramBotLog.created_at >= start_date,
                TelegramBotLog.created_at < end_date + timedelta(days=1),
                TelegramBotLog.status == LogStatus.ERROR.value
            ).count()

            success_rate = 0.0
            if activity_count > 0:
                success_rate = (success_count / activity_count) * 100
                success_rate = max(0.0, min(100.0, success_rate))
                success_rate = round(success_rate, 1)

            items.append({
                "id": user.id,
                "telegram_user_id": user.telegram_user_id,
                "telegram_username": user.telegram_username,
                "telegram_first_name": user.telegram_first_name,
                "telegram_last_name": user.telegram_last_name,
                "system_user_id": user.system_user_id,
                "merchant_name": merchant_name or "未关联商户",
                "verification_status": user.verification_status,
                "verification_time": user.verification_time.isoformat() if user.verification_time else None,
                "last_active_time": user.last_active_time.isoformat() if user.last_active_time else None,
                "activity_count": activity_count,
                "group_count": group_count,
                "success_commands": success_count,
                "failed_commands": failed_count,
                "success_rate": success_rate,
                "created_at": user.created_at.isoformat() if user.created_at else None
            })

        return {
            "items": items,
            "total": total,
            "page": page,
            "page_size": page_size,
            "pages": (total + page_size - 1) // page_size
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取用户统计列表失败: {e}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取用户统计列表失败"
        )


@router.get("/statistics/charts")
async def get_chart_data(
    chart_type: str = Query(..., description="图表类型: group_activity, user_activity, command_trend, command_type"),
    merchant_id: Optional[int] = Depends(optional_int_query(None, "商户ID")),
    start_time: Optional[str] = Query(None, description="开始时间"),
    end_time: Optional[str] = Query(None, description="结束时间"),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    获取图表数据
    """
    try:
        # 检查权限
        if not current_user.has_permission("api:telegram:config"):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="权限不足"
            )

        # 解析时间范围
        if start_time and end_time:
            try:
                start_date = datetime.fromisoformat(start_time.replace('Z', '+00:00')).date()
                end_date = datetime.fromisoformat(end_time.replace('Z', '+00:00')).date()
            except ValueError:
                start_date = datetime.now().date() - timedelta(days=7)
                end_date = datetime.now().date()
        else:
            start_date = datetime.now().date() - timedelta(days=7)
            end_date = datetime.now().date()

        # 权限过滤
        if not current_user.is_superuser:
            accessible_merchant_ids = current_user.get_accessible_merchant_ids()
            if merchant_id and merchant_id not in accessible_merchant_ids:
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail="无权限访问该商户数据"
                )
            if not merchant_id and accessible_merchant_ids:
                merchant_id = accessible_merchant_ids[0]

        # 根据图表类型返回相应数据
        if chart_type == "group_activity":
            return await _get_group_activity_chart_data(db, current_user, merchant_id, start_date, end_date)
        elif chart_type == "user_activity":
            return await _get_user_activity_chart_data(db, current_user, merchant_id, start_date, end_date)
        elif chart_type == "command_trend":
            return await _get_command_trend_chart_data(db, current_user, merchant_id, start_date, end_date)
        elif chart_type == "command_type":
            return await _get_command_type_chart_data(db, current_user, merchant_id, start_date, end_date)
        else:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="不支持的图表类型"
            )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取图表数据失败: {e}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取图表数据失败"
        )


async def _get_group_activity_chart_data(
    db: Session,
    current_user: User,
    merchant_id: Optional[int],
    start_date: date,
    end_date: date
) -> Dict[str, Any]:
    """获取群组活跃度图表数据"""
    try:
        # 构建群组查询
        query = db.query(TelegramGroup)

        # 商户过滤
        if merchant_id:
            query = query.filter(TelegramGroup.merchant_id == merchant_id)

        # 权限过滤
        if not current_user.is_superuser:
            accessible_merchant_ids = current_user.get_accessible_merchant_ids()
            query = query.filter(TelegramGroup.merchant_id.in_(accessible_merchant_ids))

        # 只查询活跃群组
        query = query.filter(TelegramGroup.bind_status == BindStatus.ACTIVE)

        # 获取群组列表
        groups = query.limit(10).all()  # 限制显示前10个群组

        if not groups:
            return {
                "categories": ["暂无数据"],
                "data": [0],
                "message": "暂无群组数据"
            }

        categories = []
        data = []

        for group in groups:
            # 获取该群组在时间范围内的活动数
            activity_count = db.query(TelegramBotLog).filter(
                TelegramBotLog.chat_id == group.chat_id,
                TelegramBotLog.created_at >= start_date,
                TelegramBotLog.created_at < end_date + timedelta(days=1)
            ).count()

            categories.append(group.chat_title or f"群组{group.id}")
            data.append(activity_count)

        return {
            "categories": categories,
            "data": data
        }

    except Exception as e:
        logger.error(f"获取群组活跃度图表数据失败: {e}")
        return {
            "categories": ["暂无数据"],
            "data": [0],
            "message": "获取数据失败"
        }


async def _get_user_activity_chart_data(
    db: Session,
    current_user: User,
    merchant_id: Optional[int],
    start_date: date,
    end_date: date
) -> Dict[str, Any]:
    """获取用户活跃度图表数据"""
    try:
        # 构建查询，按活跃度分组统计用户
        base_query = db.query(TelegramBotLog.user_id, func.count(TelegramBotLog.id).label('activity_count'))

        # 时间过滤
        base_query = base_query.filter(
            TelegramBotLog.created_at >= start_date,
            TelegramBotLog.created_at < end_date + timedelta(days=1)
        )

        # 加入群组表进行权限过滤
        base_query = base_query.join(TelegramGroup, TelegramBotLog.chat_id == TelegramGroup.chat_id)

        # 商户过滤
        if merchant_id:
            base_query = base_query.filter(TelegramGroup.merchant_id == merchant_id)

        # 权限过滤
        if not current_user.is_superuser:
            accessible_merchant_ids = current_user.get_accessible_merchant_ids()
            base_query = base_query.filter(TelegramGroup.merchant_id.in_(accessible_merchant_ids))

        # 分组统计
        user_activities = base_query.group_by(TelegramBotLog.user_id).all()

        if not user_activities:
            return {
                "data": [
                    {"value": 0, "name": "高活跃"},
                    {"value": 0, "name": "中活跃"},
                    {"value": 0, "name": "低活跃"},
                    {"value": 0, "name": "不活跃"}
                ],
                "message": "暂无用户活动数据"
            }

        # 按活跃度分类
        high_active = len([u for u in user_activities if u.activity_count >= 20])
        medium_active = len([u for u in user_activities if 5 <= u.activity_count < 20])
        low_active = len([u for u in user_activities if 1 <= u.activity_count < 5])
        inactive = len([u for u in user_activities if u.activity_count == 0])

        return {
            "data": [
                {"value": high_active, "name": "高活跃"},
                {"value": medium_active, "name": "中活跃"},
                {"value": low_active, "name": "低活跃"},
                {"value": inactive, "name": "不活跃"}
            ]
        }

    except Exception as e:
        logger.error(f"获取用户活跃度图表数据失败: {e}")
        return {
            "data": [
                {"value": 0, "name": "高活跃"},
                {"value": 0, "name": "中活跃"},
                {"value": 0, "name": "低活跃"},
                {"value": 0, "name": "不活跃"}
            ],
            "message": "获取数据失败"
        }


async def _get_command_trend_chart_data(
    db: Session,
    current_user: User,
    merchant_id: Optional[int],
    start_date: date,
    end_date: date
) -> Dict[str, Any]:
    """获取命令趋势图表数据"""
    try:
        # 生成时间序列（按小时）
        current_time = datetime.combine(start_date, datetime.min.time())
        end_time = datetime.combine(end_date, datetime.max.time())

        time_labels = []
        data = []

        # 按4小时间隔生成数据点
        while current_time <= end_time:
            time_labels.append(current_time.strftime("%H:%M"))

            # 查询该时间段的命令数
            next_time = current_time + timedelta(hours=4)
            count = db.query(TelegramBotLog).filter(
                TelegramBotLog.created_at >= current_time,
                TelegramBotLog.created_at < next_time
            )

            # 加入群组表进行权限过滤
            count = count.join(TelegramGroup, TelegramBotLog.chat_id == TelegramGroup.chat_id)

            # 商户过滤
            if merchant_id:
                count = count.filter(TelegramGroup.merchant_id == merchant_id)

            # 权限过滤
            if not current_user.is_superuser:
                accessible_merchant_ids = current_user.get_accessible_merchant_ids()
                count = count.filter(TelegramGroup.merchant_id.in_(accessible_merchant_ids))

            data.append(count.count())
            current_time = next_time

        if not any(data):  # 如果所有数据都是0
            return {
                "categories": ["00:00", "04:00", "08:00", "12:00", "16:00", "20:00"],
                "data": [0, 0, 0, 0, 0, 0],
                "message": "暂无命令数据"
            }

        return {
            "categories": time_labels,
            "data": data
        }

    except Exception as e:
        logger.error(f"获取命令趋势图表数据失败: {e}")
        return {
            "categories": ["00:00", "04:00", "08:00", "12:00", "16:00", "20:00"],
            "data": [0, 0, 0, 0, 0, 0],
            "message": "获取数据失败"
        }


async def _get_command_type_chart_data(
    db: Session,
    current_user: User,
    merchant_id: Optional[int],
    start_date: date,
    end_date: date
) -> Dict[str, Any]:
    """获取命令类型图表数据"""
    try:
        # 查询命令类型统计
        query = db.query(
            TelegramBotLog.command,
            func.count(TelegramBotLog.id).label('count')
        ).filter(
            TelegramBotLog.created_at >= start_date,
            TelegramBotLog.created_at < end_date + timedelta(days=1)
        )

        # 加入群组表进行权限过滤
        query = query.join(TelegramGroup, TelegramBotLog.chat_id == TelegramGroup.chat_id)

        # 商户过滤
        if merchant_id:
            query = query.filter(TelegramGroup.merchant_id == merchant_id)

        # 权限过滤
        if not current_user.is_superuser:
            accessible_merchant_ids = current_user.get_accessible_merchant_ids()
            query = query.filter(TelegramGroup.merchant_id.in_(accessible_merchant_ids))

        # 分组并排序
        results = query.group_by(TelegramBotLog.command).order_by(func.count(TelegramBotLog.id).desc()).limit(10).all()

        if not results:
            return {
                "data": [
                    {"value": 0, "name": "暂无数据"}
                ],
                "message": "暂无命令数据"
            }

        data = []
        for result in results:
            data.append({
                "value": result.count,
                "name": result.command or "未知命令"
            })

        return {
            "data": data
        }

    except Exception as e:
        logger.error(f"获取命令类型图表数据失败: {e}")
        return {
            "data": [
                {"value": 0, "name": "暂无数据"}
            ],
            "message": "获取数据失败"
        }
