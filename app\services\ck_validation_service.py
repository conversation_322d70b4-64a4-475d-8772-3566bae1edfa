"""
CK有效性验证服务
负责验证CK是否仍然有效，并处理失效的CK
"""

import asyncio
from typing import Optional, Dict, Any, List
from sqlalchemy.orm import Session
from app.core.logging import get_logger
from app.core.walmart_api import WalmartAPI
from app.models.walmart_ck import WalmartCK
from app.services.walmart_ck_service_new import WalmartCKService
from app.services.binding_log_service import BindingLogService

logger = get_logger("ck_validation_service")


class CKValidationService:
    """CK有效性验证服务"""

    def __init__(self, db: Session):
        self.db = db
        self.ck_service = WalmartCKService(db)

    def _get_binding_log_service(self) -> BindingLogService:
        """【安全修复】获取BindingLogService实例的辅助方法"""
        return BindingLogService(self.db)

    async def validate_ck_availability(
        self, ck: WalmartCK, bind_context: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        验证单个CK的有效性

        Args:
            ck: CK对象
            bind_context: 绑卡上下文信息，包含record_id, trace_id等

        Returns:
            Dict[str, Any]: 验证结果
            {
                "is_valid": bool,
                "error_message": str,
                "error_code": str,
                "should_disable": bool
            }
        """
        try:
            # 记录CK验证开始，包含绑卡上下文
            if bind_context:
                logger.info(
                    f"开始验证CK {ck.id} 的有效性 (绑卡上下文) | "
                    f"record_id={bind_context.get('record_id')} | "
                    f"trace_id={bind_context.get('trace_id')} | "
                    f"card_number={bind_context.get('card_number', '')[:6]}***"
                )
            else:
                logger.info(f"开始验证CK {ck.id} 的有效性")

            # 解析CK签名获取API参数
            api_params = self._parse_ck_sign(ck)
            if not api_params:
                return {
                    "is_valid": False,
                    "error_message": f"CK签名格式错误: {ck.sign}",
                    "error_code": "INVALID_SIGN_FORMAT",
                    "should_disable": True
                }

            # 创建API客户端
            api_client = await WalmartAPI.create_with_config(
                encryption_key=api_params["encryption_key"],
                version=api_params["version"],
                sign=api_params["sign"],
                db=self.db
            )

            # 记录CK验证API请求日志（如果有绑卡上下文）
            if bind_context and bind_context.get('record_id'):
                try:
                    # 【安全修复】使用辅助方法获取BindingLogService实例
                    binding_log_service = self._get_binding_log_service()
                    await binding_log_service.log_walmart_request(
                        db=self.db,
                        card_record_id=bind_context['record_id'],
                        request_data={
                            "api_method": "query_user",
                            "purpose": "CK验证",
                            "walmart_ck_id": ck.id
                        },
                        walmart_ck_id=str(ck.id),
                        attempt_number="validation",
                        message=f"CK验证API请求 | CK_ID={ck.id}"
                    )
                except Exception as e:
                    logger.error(f"记录CK验证API请求日志失败: {str(e)}")

            # 调用query_user接口验证CK
            response = await api_client.query_user(db=self.db)

            # 记录CK验证API响应日志（如果有绑卡上下文）
            if bind_context and bind_context.get('record_id'):
                try:
                    # 【修复】根据响应结果使用正确的日志类型和消息
                    if response.get("status") is True or response.get("code") == 0:
                        # CK验证成功
                        await binding_log_service.log_walmart_response(
                            db=self.db,
                            card_record_id=bind_context['record_id'],
                            response_data=response,
                            duration_ms=0,  # CK验证不计算具体耗时
                            walmart_ck_id=str(ck.id),
                            attempt_number="validation",
                            message=f"CK验证API响应 | CK_ID={ck.id} | 验证成功"
                        )
                    else:
                        # CK验证失败 - 使用专门的沃尔玛响应日志记录
                        error_msg = (
                            response.get("message") or
                            response.get("msg") or
                            response.get("error", {}).get("message") or
                            "CK验证失败"
                        )
                        await binding_log_service.log_walmart_response(
                            db=self.db,
                            card_record_id=bind_context['record_id'],
                            response_data=response,  # 【修复】保存原始响应数据
                            duration_ms=0,
                            walmart_ck_id=str(ck.id),  # 【修复】确保CK ID被正确记录
                            attempt_number="validation",
                            message=f"沃尔玛响应: CK验证失败: {error_msg}"
                        )
                except Exception as e:
                    logger.error(f"记录CK验证API响应日志失败: {str(e)}")

            # 解析响应
            validation_result = self._parse_validation_response(response, ck.id)

            # 如果CK失效，记录详细日志
            if not validation_result["is_valid"]:
                await self._log_ck_validation_failure(ck, validation_result, bind_context, response)

                # 如果需要禁用CK，执行禁用操作
                if validation_result["should_disable"]:
                    await self._disable_invalid_ck(ck, validation_result["error_message"], bind_context)

            return validation_result

        except Exception as e:
            logger.error(f"验证CK {ck.id} 时发生异常: {e}")
            return {
                "is_valid": False,
                "error_message": f"验证过程中发生异常: {str(e)}",
                "error_code": "VALIDATION_EXCEPTION",
                "should_disable": False  # 验证异常不应该禁用CK，可能是临时的网络或系统问题
            }

    def _parse_ck_sign(self, ck: WalmartCK) -> Optional[Dict[str, Any]]:
        """
        解析CK签名获取API参数

        Args:
            ck: CK对象

        Returns:
            Optional[Dict[str, Any]]: API参数或None
            {
                "sign": str,           # 用户登录凭证
                "encryption_key": str, # 加密密钥
                "version": str         # API版本号
            }
        """
        try:
            user_sign_raw = ck.sign
            sign_parts = user_sign_raw.split("#")

            if len(sign_parts) < 3:
                logger.error(f"CK {ck.id} 签名格式错误，期望格式：ck#token#wx_sign#version，实际：{user_sign_raw}")
                return None

            return {
                "sign": sign_parts[0],           # 第一部分：用户登录凭证
                "encryption_key": sign_parts[1], # 第二部分：加密密钥
                "version": sign_parts[2],        # 第三部分：API版本号
            }

        except Exception as e:
            logger.error(f"解析CK {ck.id} 签名时发生异常: {e}")
            return None

    def _parse_validation_response(self, response, ck_id: int) -> Dict[str, Any]:
        """
        解析验证响应

        Args:
            response: API响应对象
            ck_id: CK ID

        Returns:
            Dict[str, Any]: 解析结果
        """
        try:
            
            response_data = response.json()

            # 提取基本响应信息
            status = response_data.get("status")
            error_info = response_data.get("error", {})
            error_code = error_info.get("errorcode")
            error_message = error_info.get("message")
            log_id = response_data.get("logId", "")

            # 判断响应是否成功
            # 成功条件：status=true 且 errorcode不表示严重错误
            if status is True:
                # 检查是否应该禁用CK（只有明确的CK失效才禁用）
                if self._should_disable_ck(error_code, error_message):
                    logger.error(
                        f"CK {ck_id} 验证失败: status=true但检测到CK失效错误 | "
                        f"errorcode={error_code} | message={error_message} | logId={log_id}"
                    )
                    return {
                        "is_valid": False,
                        "error_message": error_message or f"CK失效错误码: {error_code}",
                        "error_code": str(error_code),
                        "should_disable": True,
                        "log_id": log_id
                    }
                else:
                    # 成功情况
                    data = response_data.get("data", {})
                    card_count = data.get("cardCount", 0)
                    nick_name = data.get("nickName", "")

                    logger.info(
                        f"CK {ck_id} 验证成功 | "
                        f"cardCount={card_count} | nickName={nick_name} | logId={log_id}"
                    )
                    return {
                        "is_valid": True,
                        "error_message": None,
                        "error_code": None,
                        "should_disable": False,
                        "log_id": log_id,
                        "card_count": card_count,
                        "nick_name": nick_name
                    }
            else:
                # 失败情况：status=false
                # 改进日志记录，明确区分不同类型的错误
                if error_code == 203 and "请先去登录" in (error_message or ""):
                    logger.error(
                        f"CK {ck_id} 验证失败: CK无效需要重新登录 | "
                        f"errorcode={error_code} | message={error_message} | logId={log_id}"
                    )
                else:
                    logger.error(
                        f"CK {ck_id} 验证失败: status=false | "
                        f"errorcode={error_code} | message={error_message} | logId={log_id}"
                    )

                # 判断是否需要禁用CK
                should_disable = self._should_disable_ck(error_code, error_message)

                return {
                    "is_valid": False,
                    "error_message": error_message or "API返回失败状态",
                    "error_code": str(error_code) if error_code is not None else "UNKNOWN",
                    "should_disable": should_disable,
                    "log_id": log_id
                }

        except Exception as e:
            logger.error(f"解析CK {ck_id} 验证响应时发生异常: {e}")
            return {
                "is_valid": False,
                "error_message": f"响应解析异常: {str(e)}",
                "error_code": "PARSE_EXCEPTION",
                "should_disable": False  # 解析异常不应该禁用CK，可能是临时的系统问题
            }

    def _is_critical_error_code(self, error_code) -> bool:
        """
        判断错误码是否表示严重错误（即使status=true也需要禁用CK）

        Args:
            error_code: 错误码

        Returns:
            bool: 是否为严重错误码
        """
        if error_code is None:
            return False

        # 定义严重错误码（只有明确的业务层CK失效错误才需要禁用CK）
        # 注意：HTTP状态码错误（401、403、500等）不应该禁用CK，这些可能是临时的网络或服务器问题
        critical_error_codes = {
            203,     # 请先去登录 - 明确的CK失效信号，需要禁用
            # 移除HTTP状态码，因为这些不是CK失效的明确信号
        }

        try:
            error_code_int = int(error_code)
            return error_code_int in critical_error_codes
        except (ValueError, TypeError):
            # 如果无法转换为整数，检查字符串形式的错误码
            str_error_code = str(error_code).upper()
            critical_str_codes = {
                "INVALID_SIGN", "USER_NOT_FOUND", "ACCOUNT_DISABLED",
                "PERMISSION_DENIED", "TOKEN_EXPIRED", "ACCOUNT_LOCKED"
            }
            return str_error_code in critical_str_codes

    def _should_disable_ck(self, error_code, error_message: str) -> bool:
        """
        判断是否应该禁用CK

        重要原则：只有底层沃尔玛API明确返回CK失效错误时才禁用CK
        HTTP错误、网络错误等不应该禁用CK

        Args:
            error_code: 错误码（可能是int或str）
            error_message: 错误消息

        Returns:
            bool: 是否应该禁用
        """
        # 只检查明确表示CK失效的错误：errorcode=203且消息明确表示需要登录
        if error_code == 203 and error_message:
            # 只有errorcode=203且消息明确表示需要登录时才禁用
            ck_invalid_keywords = [
                "请先去登录", "需要登录", "登录失效", "请重新登录"
            ]

            error_message_lower = error_message.lower()
            for keyword in ck_invalid_keywords:
                if keyword in error_message_lower:
                    return True

        # 默认情况下不禁用CK
        # 只有明确的CK失效信号才禁用，避免因临时错误误禁用CK
        return False

    async def _log_ck_validation_failure(
        self, ck: WalmartCK, validation_result: Dict[str, Any],
        bind_context: Optional[Dict[str, Any]] = None,
        raw_response = None  # 新增：原始响应对象
    ):
        """
        记录CK验证失败的详细日志

        Args:
            ck: CK对象
            validation_result: 验证结果
            bind_context: 绑卡上下文信息
        """
        # 构建基础日志信息
        log_message = (
            f"CK验证失败详情 | "
            f"CK_ID={ck.id} | "
            f"商户ID={ck.merchant_id} | "
            f"部门ID={ck.department_id} | "
            f"错误码={validation_result['error_code']} | "
            f"错误消息={validation_result['error_message']} | "
            f"是否禁用={validation_result['should_disable']} | "
            f"当前使用次数={ck.bind_count}/{ck.total_limit}"
        )

        # 如果有绑卡上下文，添加相关信息
        if bind_context:
            log_message += (
                f" | record_id={bind_context.get('record_id')} | "
                f"trace_id={bind_context.get('trace_id')} | "
                f"card_number={bind_context.get('card_number', '')[:6]}***"
            )

        logger.error(log_message)

        # 记录到数据库日志（如果有绑卡上下文）
        if bind_context and bind_context.get('record_id'):
            try:
                # 构建详细信息
                details = {
                    "walmart_ck_id": ck.id,
                    "merchant_id": ck.merchant_id,
                    "department_id": ck.department_id,
                    "error_code": validation_result['error_code'],
                    "error_message": validation_result['error_message'],
                    "should_disable": validation_result['should_disable'],
                    "bind_count": ck.bind_count,
                    "total_limit": ck.total_limit,
                    "trace_id": bind_context.get('trace_id'),
                    "card_number_masked": bind_context.get('card_number', '')[:6] + "***" if bind_context.get('card_number') else None,
                    "validation_context": "绑卡流程中的CK验证"
                }

                # 【修复】记录CK验证失败日志到数据库，使用正确的错误来源标识和保存原始响应
                raw_response_data = None
                if raw_response:
                    try:
                        raw_response_data = raw_response.json()
                    except Exception as e:
                        logger.warning(f"解析原始响应数据失败: {str(e)}")

                await binding_log_service.log_error(
                    db=self.db,
                    card_record_id=bind_context['record_id'],
                    error_message=f"CK验证失败: {validation_result['error_message']}",
                    details=details,
                    walmart_ck_id=str(ck.id),
                    error_source="walmart_api",  # CK验证失败通常是沃尔玛API返回的错误
                    raw_response=raw_response_data  # 【修复】保存原始响应数据
                )

                # 如果CK被禁用，记录额外的禁用日志
                if validation_result['should_disable']:
                    await binding_log_service.log_ck_usage_tracking(
                        db=self.db,
                        card_record_id=bind_context['record_id'],
                        walmart_ck_id=ck.id,
                        action="validation_failed_and_disabled",
                        reason=f"CK验证失败并被禁用: {validation_result['error_message']}",
                        details={
                            "error_code": validation_result['error_code'],
                            "error_message": validation_result['error_message'],
                            "trace_id": bind_context.get('trace_id'),
                            "disable_trigger": "绑卡触发禁用"
                        }
                    )

            except Exception as e:
                logger.error(f"记录CK验证失败日志到数据库时出错: {str(e)}", exc_info=True)

    async def _disable_invalid_ck(
        self, ck: WalmartCK, reason: str, bind_context: Optional[Dict[str, Any]] = None
    ):
        """
        禁用失效的CK

        Args:
            ck: CK对象
            reason: 禁用原因
            bind_context: 绑卡上下文信息
        """
        try:
            # 使用事务确保数据一致性
            ck.active = False
            self.db.commit()

            # 构建禁用日志信息
            log_message = (
                f"CK已被自动禁用 | "
                f"CK_ID={ck.id} | "
                f"商户ID={ck.merchant_id} | "
                f"禁用原因={reason}"
            )

            # 如果有绑卡上下文，添加相关信息
            if bind_context:
                log_message += (
                    f" | record_id={bind_context.get('record_id')} | "
                    f"trace_id={bind_context.get('trace_id')} | "
                    f"绑卡触发禁用"
                )

            logger.warning(log_message)

        except Exception as e:
            self.db.rollback()
            logger.error(f"禁用CK {ck.id} 时发生异常: {e}")
            raise

    async def validate_multiple_cks(self, cks: List[WalmartCK]) -> List[Dict[str, Any]]:
        """
        批量验证多个CK的有效性
        
        Args:
            cks: CK列表
            
        Returns:
            List[Dict[str, Any]]: 验证结果列表
        """
        if not cks:
            return []
            
        logger.info(f"开始批量验证 {len(cks)} 个CK的有效性")
        
        # 并发验证（限制并发数量避免过载）
        semaphore = asyncio.Semaphore(5)  # 最多同时验证5个CK
        
        async def validate_with_semaphore(ck):
            async with semaphore:
                result = await self.validate_ck_availability(ck)
                result["ck_id"] = ck.id
                return result
        
        # 执行并发验证
        tasks = [validate_with_semaphore(ck) for ck in cks]
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # 处理异常结果
        processed_results = []
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                logger.error(f"验证CK {cks[i].id} 时发生异常: {result}")
                processed_results.append({
                    "ck_id": cks[i].id,
                    "is_valid": False,
                    "error_message": f"验证异常: {str(result)}",
                    "error_code": "VALIDATION_EXCEPTION",
                    "should_disable": True
                })
            else:
                processed_results.append(result)
        
        # 统计验证结果
        valid_count = sum(1 for r in processed_results if r["is_valid"])
        invalid_count = len(processed_results) - valid_count
        disabled_count = sum(1 for r in processed_results if r["should_disable"])
        
        logger.info(
            f"批量CK验证完成 | "
            f"总数={len(cks)} | "
            f"有效={valid_count} | "
            f"无效={invalid_count} | "
            f"已禁用={disabled_count}"
        )
        
        return processed_results

    def get_validation_statistics(self) -> Dict[str, Any]:
        """
        获取CK验证统计信息
        
        Returns:
            Dict[str, Any]: 统计信息
        """
        try:
            # 查询CK状态统计
            total_cks = self.db.query(WalmartCK).filter(
                WalmartCK.is_deleted == False
            ).count()
            
            active_cks = self.db.query(WalmartCK).filter(
                WalmartCK.is_deleted == False,
                WalmartCK.active == True
            ).count()
            
            reached_limit_cks = self.db.query(WalmartCK).filter(
                WalmartCK.is_deleted == False,
                WalmartCK.bind_count >= WalmartCK.total_limit
            ).count()
            
            return {
                "total_cks": total_cks,
                "active_cks": active_cks,
                "inactive_cks": total_cks - active_cks,
                "reached_limit_cks": reached_limit_cks,
                "availability_rate": round(active_cks / total_cks * 100, 2) if total_cks > 0 else 0
            }
            
        except Exception as e:
            logger.error(f"获取CK验证统计信息失败: {e}")
            return {
                "error": f"获取统计信息失败: {str(e)}"
            }
