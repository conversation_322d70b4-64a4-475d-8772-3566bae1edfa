from typing import Any, Dict, Optional
from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from pydantic import BaseModel, Field

from app.api import deps
from app.models.user import User
from app.models.walmart_server import WalmartServer
from app.api.deps import get_db
from app.services.walmart_server import WalmartServerService

router = APIRouter()


class walmartServerUpdate(BaseModel):
    """沃尔玛API配置更新模型"""

    apiUrl: str = Field(..., description="API地址")
    referer: str = Field(..., description="HTTP请求Referer头")


class walmartServerResponse(BaseModel):
    """沃尔玛API配置响应模型"""

    apiUrl: str
    referer: str


# 系统参数模型
class SystemParams(BaseModel):
    """系统参数模型"""

    apiTimeout: int = Field(15, description="API超时时间(秒)")
    requestLimit: int = Field(100, description="请求限制")
    cacheTime: int = Field(30, description="缓存时间(分钟)")
    recordRetentionDays: int = Field(90, description="记录保留天数")
    exportLimit: int = Field(1000, description="导出限制条数")
    maintenanceMode: bool = Field(False, description="维护模式")
    maintenanceMessage: str = Field("系统维护中，请稍后再试...", description="维护消息")


# 全局系统参数，用于缓存
_system_params = SystemParams()


@router.get("", response_model=walmartServerResponse)
async def get_walmart_config(
    db: Session = Depends(get_db),
    current_user: User = Depends(deps.get_current_active_superuser),
) -> Any:
    """
    获取沃尔玛API配置
    """
    # 使用服务层获取配置
    service = WalmartServerService(db)
    api_url = await service.get_api_url()
    # 使用固定的Referer地址
    referer = "https://servicewechat.com/wx81d3e1fe4c2e11b4/168/page-frame.html"

    if not api_url:
        # 如果没有配置，返回默认值
        return {
            "apiUrl": "",
            "referer": referer,
        }

    return {
        "apiUrl": api_url,
        "referer": referer,
    }


@router.put("", response_model=walmartServerResponse)
async def update_walmart_config(
    *,
    db: Session = Depends(get_db),
    config_in: walmartServerUpdate,
    current_user: User = Depends(deps.get_current_active_superuser),
) -> Any:
    """
    更新沃尔玛API配置
    """
    service = WalmartServerService(db)

    # 获取激活的配置
    config = service.get_active_config()

    # 如果没有配置，创建新配置
    if not config:
        from app.schemas.walmart_server import WalmartServerBase

        config_data = WalmartServerBase(
            api_url=config_in.apiUrl,
            referer=config_in.referer,
            timeout=30,
            retry_count=3,
            daily_bind_limit=1000,
            api_rate_limit=60,
            max_retry_times=3,
            bind_timeout_seconds=30,
            verification_code_expires=300,
            log_retention_days=90,
            enable_ip_whitelist=True,
            enable_security_audit=True,
            maintenance_mode=False,
            is_active=True,
            extra_config={},
        )
        config = await service.create_server_config(config_data)
    else:
        # 更新现有配置
        from app.schemas.walmart_server import WalmartServerUpdate

        update_data = WalmartServerUpdate(
            api_url=config_in.apiUrl,
            referer=config_in.referer,
            is_active=True
        )
        config = await service.update_server_config(config.id, update_data)

    # 返回更新后的配置
    return {
        "apiUrl": config.api_url,
        "referer": config.referer,
    }
