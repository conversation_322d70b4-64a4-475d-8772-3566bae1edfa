"""
动态权限检查系统 - 完全消除硬编码权限
"""

from typing import Optional, List, Dict, Any
from functools import wraps
from fastapi import Request, Depends, HTTPException
from sqlalchemy.orm import Session

from app.api import deps
from app.models.user import User
from app.services.permission_service import PermissionService
from app.core.exceptions import BusinessException, ErrorCode
from app.core.logging import get_logger

logger = get_logger(__name__)


class DynamicPermissionChecker:
    """动态权限检查器"""
    
    def __init__(self, db: Session):
        self.db = db
        self.permission_service = PermissionService(db)
    
    def check_api_permission(self, user: User, request_path: str, method: str) -> bool:
        """
        检查API权限 - 基于请求路径和方法
        
        Args:
            user: 当前用户
            request_path: 请求路径，如 /api/v1/users
            method: HTTP方法，如 GET, POST, PUT, DELETE
            
        Returns:
            bool: 是否有权限
        """
        try:
            # 超级管理员拥有所有权限
            if user.is_superuser:
                return True
            
            # 构建权限代码：api:method:path
            permission_code = f"api:{method.lower()}:{request_path}"
            
            # 检查用户是否有该权限
            return self.permission_service.check_user_permission(user, permission_code)
            
        except Exception as e:
            logger.error(f"检查API权限失败: {e}")
            return False
    
    def check_resource_permission(self, user: User, resource: str, action: str) -> bool:
        """
        检查资源权限 - 基于资源和操作

        Args:
            user: 当前用户
            resource: 资源名称，如 user, merchant, department
            action: 操作名称，如 view, create, edit, delete

        Returns:
            bool: 是否有权限
        """
        try:
            # 超级管理员拥有所有权限
            if user.is_superuser:
                return True

            # 将废弃的 resource:action 格式映射到标准的 api:module:action 格式
            permission_mapping = {
                "merchant:view": "api:merchants:read",
                "merchant:create": "api:merchants:create",
                "merchant:edit": "api:merchants:update",
                "merchant:delete": "api:merchants:delete",
                "user:view": "api:users:read",
                "user:create": "api:users:create",
                "user:edit": "api:users:update",
                "user:delete": "api:users:delete",
                "department:view": "api:departments:read",
                "department:create": "api:departments:create",
                "department:edit": "api:departments:update",
                "department:delete": "api:departments:delete",
                "role:view": "api:roles:read",
                "role:create": "api:roles:create",
                "role:edit": "api:roles:update",
                "role:delete": "api:roles:delete",
            }

            # 构建权限代码：resource:action
            legacy_permission_code = f"{resource}:{action}"

            # 如果存在映射，使用标准格式的权限代码
            if legacy_permission_code in permission_mapping:
                permission_code = permission_mapping[legacy_permission_code]
                logger.info(f"权限映射: {legacy_permission_code} -> {permission_code}")
            else:
                # 如果没有映射，保持原格式（向后兼容）
                permission_code = legacy_permission_code
                logger.warning(f"未找到权限映射，使用原格式: {permission_code}")

            # 检查用户是否有该权限
            return self.permission_service.check_user_permission(user, permission_code)

        except Exception as e:
            logger.error(f"检查资源权限失败: {e}")
            return False
    
    def get_user_accessible_resources(self, user: User) -> Dict[str, List[str]]:
        """
        获取用户可访问的资源列表
        
        Args:
            user: 当前用户
            
        Returns:
            Dict[str, List[str]]: 资源和操作的映射
        """
        try:
            # 超级管理员拥有所有权限
            if user.is_superuser:
                return {"*": ["*"]}  # 表示所有资源的所有操作
            
            # 获取用户所有权限
            permissions = self.permission_service.get_user_permissions(user)
            
            # 解析权限代码，构建资源映射
            resource_map = {}
            for permission in permissions:
                if ":" in permission:
                    parts = permission.split(":")
                    if len(parts) >= 2:
                        resource = parts[0]
                        action = parts[1]
                        
                        if resource not in resource_map:
                            resource_map[resource] = []
                        
                        if action not in resource_map[resource]:
                            resource_map[resource].append(action)
            
            return resource_map
            
        except Exception as e:
            logger.error(f"获取用户可访问资源失败: {e}")
            return {}


def require_permission(resource: str, action: str):
    """
    权限检查装饰器 - 动态权限检查
    
    Args:
        resource: 资源名称
        action: 操作名称
    """
    def decorator(func):
        @wraps(func)
        async def wrapper(*args, **kwargs):
            # 从kwargs中获取db和current_user
            db = kwargs.get('db')
            current_user = kwargs.get('current_user')
            
            if not db or not current_user:
                raise BusinessException(
                    message="权限检查失败：缺少必要参数",
                    code=ErrorCode.INTERNAL_ERROR
                )
            
            # 创建权限检查器
            checker = DynamicPermissionChecker(db)
            
            # 检查权限
            has_permission = checker.check_resource_permission(current_user, resource, action)
            
            if not has_permission:
                raise BusinessException(
                    message=f"没有{resource}:{action}权限",
                    code=ErrorCode.FORBIDDEN
                )
            
            # 执行原函数
            return await func(*args, **kwargs)
        
        return wrapper
    return decorator


def require_api_permission(request_path: str = None):
    """
    API权限检查装饰器 - 基于请求路径
    
    Args:
        request_path: 可选的请求路径，如果不提供则从request中获取
    """
    def decorator(func):
        @wraps(func)
        async def wrapper(*args, **kwargs):
            # 从kwargs中获取必要参数
            db = kwargs.get('db')
            current_user = kwargs.get('current_user')
            request = kwargs.get('request')
            
            if not db or not current_user:
                raise BusinessException(
                    message="权限检查失败：缺少必要参数",
                    code=ErrorCode.INTERNAL_ERROR
                )
            
            # 获取请求路径和方法
            path = request_path
            if not path and request:
                path = request.url.path
            
            method = request.method if request else "GET"
            
            # 创建权限检查器
            checker = DynamicPermissionChecker(db)
            
            # 检查权限
            has_permission = checker.check_api_permission(current_user, path, method)
            
            if not has_permission:
                raise BusinessException(
                    message=f"没有访问{path}的权限",
                    code=ErrorCode.FORBIDDEN
                )
            
            # 执行原函数
            return await func(*args, **kwargs)
        
        return wrapper
    return decorator


class PermissionHelper:
    """权限辅助类 - 提供便捷的权限检查方法"""
    
    @staticmethod
    def check_permission(
        db: Session,
        current_user: User,
        resource: str,
        action: str,
        raise_exception: bool = True
    ) -> bool:
        """
        检查权限的便捷方法
        
        Args:
            db: 数据库会话
            current_user: 当前用户
            resource: 资源名称
            action: 操作名称
            raise_exception: 是否在无权限时抛出异常
            
        Returns:
            bool: 是否有权限
        """
        checker = DynamicPermissionChecker(db)
        has_permission = checker.check_resource_permission(current_user, resource, action)
        
        if not has_permission and raise_exception:
            raise BusinessException(
                message=f"没有{resource}:{action}权限",
                code=ErrorCode.FORBIDDEN
            )
        
        return has_permission
    
    @staticmethod
    def get_accessible_resources(db: Session, current_user: User) -> Dict[str, List[str]]:
        """
        获取用户可访问资源的便捷方法
        
        Args:
            db: 数据库会话
            current_user: 当前用户
            
        Returns:
            Dict[str, List[str]]: 资源和操作的映射
        """
        checker = DynamicPermissionChecker(db)
        return checker.get_user_accessible_resources(current_user)
