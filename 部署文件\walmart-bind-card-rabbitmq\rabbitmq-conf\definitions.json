{"rabbit_version": "3.12.0", "rabbitmq_version": "3.12.0", "product_name": "RabbitMQ", "product_version": "3.12.0", "users": [{"name": "walmart_card", "password_hash": "7c222fb2927d828af22f592134e8932480637c0d", "hashing_algorithm": "rabbit_password_hashing_sha256", "tags": "administrator"}], "vhosts": [{"name": "/walmart_card"}], "permissions": [{"user": "walmart_card", "vhost": "/walmart_card", "configure": ".*", "write": ".*", "read": ".*"}], "topic_permissions": [], "parameters": [], "global_parameters": [{"name": "cluster_name", "value": "walmart-rabbitmq-cluster"}], "policies": [{"vhost": "/walmart_card", "name": "ha-all", "pattern": ".*", "apply-to": "all", "definition": {"ha-mode": "all", "ha-sync-mode": "automatic"}, "priority": 0}], "queues": [], "exchanges": [], "bindings": []}