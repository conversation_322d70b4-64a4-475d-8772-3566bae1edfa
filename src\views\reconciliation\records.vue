<template>
  <div class="records-container">
    <!-- 面包屑导航 -->
    <BreadcrumbNavigation :department-path="route.query.departmentPath" :filters="filters"
      :extra-items="ckBreadcrumbItems" :show-current-page="false" @navigate="handleNavigate" />


    <!-- 筛选条件 -->
    <el-card class="filter-card" shadow="never">
      <div class="filter-row">
        <TimeRangeSelector v-model="timeRangeData" @change="handleTimeRangeChange" />

        <div class="filter-group">
          <label class="filter-label">搜索：</label>
          <el-input v-model="filters.searchKeyword" placeholder="订单号/卡号" @input="handleSearch" style="width: 200px;"
            clearable>
            <template #prefix>
              <el-icon>
                <Search />
              </el-icon>
            </template>
          </el-input>
        </div>

        <div class="filter-actions">
          <el-button type="primary" @click="refreshData" :loading="loading">
            <el-icon>
              <Refresh />
            </el-icon>
            刷新
          </el-button>
          <el-button @click="exportData" :loading="exportLoading">
            <el-icon>
              <Download />
            </el-icon>
            导出
          </el-button>
        </div>
      </div>
    </el-card>



    <!-- 成功绑卡记录表格 -->
    <el-card class="table-card" shadow="never">
      <template #header>
        <div class="card-header">
          <span class="card-title">
            <el-icon>
              <List />
            </el-icon>
            成功绑卡记录
          </span>
          <div class="header-actions">
            <el-tooltip content="表格说明" placement="top">
              <el-button text @click="showTableHelp">
                <el-icon>
                  <QuestionFilled />
                </el-icon>
              </el-button>
            </el-tooltip>
          </div>
        </div>
      </template>

      <el-table :data="recordList" v-loading="loading" stripe style="width: 100%"
        :default-sort="{ prop: 'createdAt', order: 'descending' }">
        <el-table-column prop="merchantOrderId" label="商户订单号" min-width="180">
          <template #default="{ row }">
            <div class="order-id">
              <el-icon class="order-icon">
                <Document />
              </el-icon>
              <span>{{ row.merchantOrderId }}</span>
            </div>
          </template>
        </el-table-column>

        <el-table-column prop="cardNumber" label="卡号" width="120">
          <template #default="{ row }">
            <code class="card-number">{{ maskCardNumber(row.cardNumber) }}</code>
          </template>
        </el-table-column>

        <el-table-column prop="amount" label="订单金额" width="120" sortable>
          <template #default="{ row }">
            <span class="amount-value">{{ formatAmount(row.amount) }}</span>
          </template>
        </el-table-column>

        <el-table-column prop="actualAmount" label="实际金额" width="120" sortable>
          <template #default="{ row }">
            <span class="amount-value success">{{ formatAmount(row.actualAmount) }}</span>
          </template>
        </el-table-column>

        <el-table-column prop="createdAt" label="绑卡时间" width="160" sortable>
          <template #default="{ row }">
            <div class="time-info">
              <div class="time-value">{{ formatDateTime(row.createdAt) }}</div>
            </div>
          </template>
        </el-table-column>

        <el-table-column prop="callbackStatus" label="回调状态" width="100">
          <template #default="{ row }">
            <el-tag :type="getCallbackStatusType(row.callbackStatus)" size="small">
              {{ getCallbackStatusText(row.callbackStatus) }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column label="操作" width="120" fixed="right">
          <template #default="{ row }">
            <el-button type="primary" size="small" @click="viewDetails(row)">
              明细
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-wrapper" v-if="pagination.total > 0">
        <el-pagination v-model:current-page="pagination.currentPage" v-model:page-size="pagination.pageSize"
          :page-sizes="[10, 20, 50, 100]" :total="pagination.total" layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange" @current-change="handleCurrentChange" />
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  ArrowLeft,
  Document,
  Search,
  Refresh,
  Download,
  List,
  QuestionFilled
} from '@element-plus/icons-vue'
import { useRouter, useRoute } from 'vue-router'
import TimeRangeSelector from '@/components/common/TimeRangeSelector.vue'
import BreadcrumbNavigation from '@/components/common/BreadcrumbNavigation.vue'
import { reconciliationApi } from '@/api'

// Router实例
const router = useRouter()
const route = useRoute()

// 响应式数据
const loading = ref(false)
const exportLoading = ref(false)
const customDateRange = ref([])

// CK信息
const ckInfo = reactive({
  ckId: route.params.ckId,
  ckSign: route.query.ckSign || '未知CK'
})

// 构建CK相关的面包屑项
const ckBreadcrumbItems = computed(() => {
  const items = []

  // 添加CK明细项（可点击，返回CK明细页面）
  if (route.params.ckId) {
    items.push({
      name: `CK明细 (${ckInfo.ckSign})`,
      clickable: true,
      onClick: goBack
    })
  }

  // 添加当前页面（不可点击）
  items.push({
    name: '成功绑卡记录',
    clickable: false
  })

  return items
})

// 处理面包屑导航
const handleNavigate = (routeInfo) => {
  router.push(routeInfo)
}

// 筛选条件
const filters = reactive({
  timeRange: route.query.timeRange || 'today',
  startDate: route.query.startDate || '',
  endDate: route.query.endDate || '',
  searchKeyword: ''
})

// 时间范围数据（用于TimeRangeSelector组件）
const timeRangeData = ref({
  timeRange: route.query.timeRange || 'today',
  startDate: route.query.startDate || '',
  endDate: route.query.endDate || ''
})

// 分页
const pagination = reactive({
  currentPage: 1,
  pageSize: 20,
  total: 0
})

// 绑卡记录数据
const recordList = ref([])

// 获取绑卡记录数据
const getBindingRecords = async () => {
  try {
    const params = {
      page: pagination.currentPage,
      page_size: pagination.pageSize,
      time_range: filters.timeRange,
      search_keyword: filters.searchKeyword
    }

    // 只有在有有效日期时才添加日期参数
    if (filters.startDate && filters.startDate.trim() !== '') {
      params.start_date = filters.startDate
    }
    if (filters.endDate && filters.endDate.trim() !== '') {
      params.end_date = filters.endDate
    }

    const response = await reconciliationApi.getBindingRecords(ckInfo.ckId, params)

    if (response && response.data) {
      recordList.value = response.data
      pagination.total = response.total
      pagination.currentPage = response.page
      pagination.pageSize = response.pageSize
    }
  } catch (error) {
    console.error('获取绑卡记录失败:', error)
    ElMessage.error('获取绑卡记录失败')
    recordList.value = []
  }
}

// 计算属性
const timeRangeText = computed(() => {
  const today = new Date()
  switch (filters.timeRange) {
    case 'today':
      return today.toLocaleDateString()
    case 'yesterday':
      const yesterday = new Date(today)
      yesterday.setDate(yesterday.getDate() - 1)
      return yesterday.toLocaleDateString()
    case 'week':
      return '本周'
    case 'month':
      return '本月'
    case 'custom':
      return customDateRange.value.length === 2 ? `${customDateRange.value[0]} 至 ${customDateRange.value[1]}` : '自定义'
    default:
      return ''
  }
})

// 方法

const formatAmount = (amount) => {
  if (!amount) return '¥0'
  return `¥${(amount / 100).toLocaleString()}`
}

const formatDateTime = (datetime) => {
  if (!datetime) return '-'
  return new Date(datetime).toLocaleString()
}

const maskCardNumber = (cardNumber) => {
  if (!cardNumber) return '-'
  if (cardNumber.length <= 8) return cardNumber
  return cardNumber.substring(0, 4) + '***' + cardNumber.substring(cardNumber.length - 4)
}

const getCallbackStatusType = (status) => {
  switch (status) {
    case 'success': return 'success'
    case 'failed': return 'danger'
    case 'pending': return 'warning'
    default: return 'info'
  }
}

const getCallbackStatusText = (status) => {
  switch (status) {
    case 'success': return '成功'
    case 'failed': return '失败'
    case 'pending': return '待处理'
    default: return '未知'
  }
}

const goBack = () => {
  // 构建返回CK详细页面的路由参数，保持时间状态
  const query = {
    name: route.query.departmentName,
    merchantName: route.query.merchantName,
    departmentPath: route.query.departmentPath,
    timeRange: filters.timeRange,
    startDate: filters.startDate || '',
    endDate: filters.endDate || '',
    merchantId: route.query.merchantId,
    viewLevel: route.query.viewLevel
  }

  // 添加时间戳强制刷新
  query._t = Date.now()

  console.log('返回CK详细页面，传递的时间参数:', {
    timeRange: query.timeRange,
    startDate: query.startDate,
    endDate: query.endDate
  })

  // 从当前路由获取组织ID和类型
  const organizationId = route.query.organizationId || route.params.organizationId
  const organizationType = route.query.organizationType || 'department'

  router.push({
    name: 'ReconciliationCkDetails',
    params: {
      organizationId: organizationId,
      organizationType: organizationType
    },
    query: query
  })
}

// 处理时间范围变化
const handleTimeRangeChange = (timeRange) => {
  // 同步到filters
  filters.timeRange = timeRange.timeRange
  filters.startDate = timeRange.startDate
  filters.endDate = timeRange.endDate

  // 刷新数据
  refreshData()
}

const handleCustomDateChange = () => {
  if (customDateRange.value && customDateRange.value.length === 2) {
    filters.startDate = customDateRange.value[0]
    filters.endDate = customDateRange.value[1]
    refreshData()
  }
}

const handleSearch = () => {
  // 防抖处理
  clearTimeout(handleSearch.timer)
  handleSearch.timer = setTimeout(() => {
    refreshData()
  }, 500)
}

const refreshData = async () => {
  loading.value = true
  try {
    await getBindingRecords()
    ElMessage.success('数据刷新成功')
  } catch (error) {
    ElMessage.error('数据刷新失败')
  } finally {
    loading.value = false
  }
}

const exportData = async () => {
  exportLoading.value = true
  try {
    const params = {
      time_range: filters.timeRange,
      search_keyword: filters.searchKeyword
    }

    // 只有在有有效日期时才添加日期参数
    if (filters.startDate && filters.startDate.trim() !== '') {
      params.start_date = filters.startDate
    }
    if (filters.endDate && filters.endDate.trim() !== '') {
      params.end_date = filters.endDate
    }

    await reconciliationApi.exportBindingRecords(ckInfo.ckId, params)
    ElMessage.success('导出成功')
  } catch (error) {
    console.error('导出失败:', error)
    ElMessage.error('导出失败')
  } finally {
    exportLoading.value = false
  }
}

const viewDetails = (row) => {
  ElMessageBox.alert(
    `订单号：${row.merchantOrderId}\n` +
    `卡号：${row.cardNumber}\n` +
    `订单金额：${formatAmount(row.amount)}\n` +
    `实际金额：${formatAmount(row.actualAmount)}\n` +
    `绑卡时间：${formatDateTime(row.createdAt)}\n` +
    `回调状态：${getCallbackStatusText(row.callbackStatus)}`,
    '绑卡记录详情',
    {
      confirmButtonText: '关闭'
    }
  )
}

const showTableHelp = () => {
  ElMessageBox.alert(
    '• 订单号：商户提交的订单编号\n' +
    '• 卡号：经过脱敏处理的沃尔玛卡号\n' +
    '• 订单金额：商户提交的订单金额\n' +
    '• 实际金额：从沃尔玛获取的卡片实际金额\n' +
    '• 绑卡时间：成功绑卡的时间\n' +
    '• 回调状态：向商户回调通知的状态（不影响绑卡成功统计）',
    '记录说明',
    {
      confirmButtonText: '知道了'
    }
  )
}

const handleSizeChange = (size) => {
  pagination.pageSize = size
  refreshData()
}

const handleCurrentChange = (page) => {
  pagination.currentPage = page
  refreshData()
}

// 初始化
onMounted(async () => {
  // 如果有自定义时间范围，设置日期选择器
  if (filters.timeRange === 'custom' && filters.startDate && filters.endDate) {
    customDateRange.value = [filters.startDate, filters.endDate]
  }
  await getBindingRecords()
})
</script>

<style scoped>
.records-container {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: calc(100vh - 60px);
}



.breadcrumb .el-button {
  padding: 0;
  font-size: 14px;
  color: #409eff;
}

.breadcrumb .el-button:hover {
  color: #66b1ff;
}

/* 页面标题 */
.page-header {
  margin-bottom: 20px;
}

.header-content {
  background: linear-gradient(135deg, #67c23a 0%, #85ce61 100%);
  padding: 24px;
  border-radius: 12px;
  color: white;
}

.page-title {
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 8px;
}

.page-description {
  margin: 0;
  font-size: 14px;
  opacity: 0.9;
}

/* 筛选卡片 */
.filter-card {
  margin-bottom: 20px;
  border: none;
}

.filter-row {
  display: flex;
  align-items: center;
  gap: 20px;
  flex-wrap: wrap;
}

.filter-group {
  display: flex;
  align-items: center;
  gap: 8px;
}

.filter-label {
  font-size: 14px;
  color: #606266;
  white-space: nowrap;
}

.filter-actions {
  margin-left: auto;
  display: flex;
  gap: 12px;
}

/* 统计卡片 */
.stats-cards {
  margin-bottom: 20px;
}

.stat-card {
  border: none;
  transition: all 0.3s ease;
  cursor: pointer;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.stat-content {
  display: flex;
  align-items: center;
  gap: 16px;
}

.stat-icon {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
}

.success-count .stat-icon {
  background: linear-gradient(135deg, #67c23a, #85ce61);
  color: white;
}

.success-amount .stat-icon {
  background: linear-gradient(135deg, #409eff, #66b1ff);
  color: white;
}

.avg-amount .stat-icon {
  background: linear-gradient(135deg, #e6a23c, #ebb563);
  color: white;
}

.time-range .stat-icon {
  background: linear-gradient(135deg, #f56c6c, #f78989);
  color: white;
}

.stat-info {
  flex: 1;
}

.stat-value {
  font-size: 24px;
  font-weight: 600;
  color: #303133;
  line-height: 1;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 14px;
  color: #909399;
}

/* 表格卡片 */
.table-card {
  border: none;
}

.card-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.card-title {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  display: flex;
  align-items: center;
  gap: 8px;
}

.header-actions {
  display: flex;
  gap: 8px;
}

/* 表格样式 */
.order-id {
  display: flex;
  align-items: center;
  gap: 8px;
}

.order-icon {
  color: #409eff;
}

.card-number {
  background-color: #f5f7fa;
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 12px;
  color: #606266;
  border: 1px solid #dcdfe6;
  font-family: 'Courier New', monospace;
}

.amount-value {
  font-weight: 600;
  color: #409eff;
}

.amount-value.success {
  color: #67c23a;
}

.time-info {
  display: flex;
  flex-direction: column;
}

.time-value {
  font-size: 12px;
  color: #909399;
}

/* 分页 */
.pagination-wrapper {
  margin-top: 20px;
  display: flex;
  justify-content: center;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .records-container {
    padding: 12px;
  }

  .filter-row {
    flex-direction: column;
    align-items: stretch;
    gap: 12px;
  }

  .filter-actions {
    margin-left: 0;
    justify-content: center;
  }

  .stat-content {
    gap: 12px;
  }

  .stat-icon {
    width: 40px;
    height: 40px;
    font-size: 20px;
  }

  .stat-value {
    font-size: 20px;
  }
}

/* 加载状态 */
.el-loading-mask {
  border-radius: 8px;
}

/* 表格行悬停效果 */
:deep(.el-table__row:hover) {
  background-color: #f5f7fa;
}

/* 按钮样式优化 */
.el-button--small {
  padding: 5px 12px;
  font-size: 12px;
}

/* 标签样式 */
.el-tag--small {
  font-size: 11px;
  padding: 0 6px;
  height: 20px;
  line-height: 18px;
}

/* 面包屑样式优化 */
:deep(.el-breadcrumb__item:last-child .el-breadcrumb__inner) {
  color: #303133;
  font-weight: 600;
}

/* 搜索框样式 */
.el-input {
  --el-input-focus-border-color: #409eff;
}
</style>
