import { http } from "@/api/request";
import { API_URLS, replaceUrlParams } from "./config";

const { ROLE } = API_URLS;

/**
 * 角色/权限组相关API
 */
export const roleApi = {
  // 获取角色列表
  getList(params) {
    return http.get(ROLE.LIST, { params }).then((res) => {
      if (res && res.data) {
        return res.data;
      } else {
        throw new Error("获取角色列表响应格式错误");
      }
    });
  },

  // 获取角色详情
  getDetail(id) {
    const url = replaceUrlParams(ROLE.DETAIL, { id });
    return http.get(url).then((res) => res.data);
  },

  // 创建角色
  create(data) {
    return http.post(ROLE.CREATE, data).then((res) => res.data);
  },

  // 更新角色
  update(id, data) {
    const url = replaceUrlParams(ROLE.UPDATE, { id });
    return http.put(url, data).then((res) => res.data);
  },

  // 删除角色
  delete(id) {
    const url = replaceUrlParams(ROLE.DELETE, { id });
    return http.delete(url).then((res) => res.data);
  },

  // 获取角色权限
  getPermissions(id) {
    const url = replaceUrlParams(ROLE.PERMISSIONS, { id: id });
    return http.get(url).then((res) => res.data);
  },

  // 更新角色权限
  updatePermissions(id, permissionIds) {
    const url = replaceUrlParams(ROLE.PERMISSIONS, { id: id });
    return http.put(url, permissionIds).then((res) => res.data);
  },

  // 获取角色菜单
  getMenus(id) {
    const url = replaceUrlParams(ROLE.MENUS, { id: id });
    return http.get(url).then((res) => res.data);
  },

  // 更新角色菜单
  updateMenus(id, menus) {
    const url = replaceUrlParams(ROLE.MENUS, { id: id });
    return http.put(url, { menus }).then((res) => res.data);
  },

  // 获取所有权限列表
  getAllPermissions() {
    return http.get(ROLE.ALL_PERMISSIONS).then((res) => res.data);
  },

  // 获取所有菜单列表
  getAllMenus() {
    return http.get(ROLE.ALL_MENUS).then((res) => res.data);
  },

  // 获取角色数据权限
  getDataPermissions(id) {
    const url = replaceUrlParams(ROLE.DATA_PERMISSIONS, { id: id });
    return http.get(url).then((res) => res.data);
  },

  // 更新角色数据权限
  updateDataPermissions(id, data) {
    const url = replaceUrlParams(ROLE.DATA_PERMISSIONS, { id: id });
    return http.put(url, data).then((res) => res.data);
  },

  // 获取角色下的用户列表
  getRoleUsers(id, params) {
    const url = replaceUrlParams(ROLE.USERS, { id: id });
    return http.get(url, { params }).then((res) => res.data);
  },

  // 添加用户到角色
  addUsersToRole(id, userIds) {
    const url = replaceUrlParams(ROLE.ADD_USERS, { id: id });
    return http.post(url, { user_ids: userIds }).then((res) => res.data);
  },

  // 从角色中移除用户
  removeUserFromRole(id, userId) {
    const url = replaceUrlParams(ROLE.REMOVE_USER, { id: id, user_id: userId });
    return http.delete(url).then((res) => res.data);
  },
};

export default roleApi;
