"""
绑卡记录统计API测试用例

测试绑卡记录统计接口的功能，包括：
1. 自动权限判断统计接口 (/api/v1/cards/statistics)
2. 指定商户统计接口 (/api/v1/cards/statistics/{merchant_id})
3. 权限隔离验证
4. 数据过滤功能
"""

import pytest
import uuid
from datetime import datetime, timedelta
from fastapi.testclient import TestClient
from sqlalchemy.orm import Session

from app.main import app
from app.models.card_record import CardRecord, CardStatus
from app.models.user import User
from app.models.merchant import Merchant
from app.models.department import Department
from app.core.deps import get_current_active_user, get_db
from test.conftest import TestDatabase


class TestCardStatisticsAPI:
    """绑卡记录统计API测试类"""

    @pytest.fixture(autouse=True)
    def setup(self, test_db: TestDatabase):
        """测试设置"""
        self.db = test_db.get_session()
        self.client = TestClient(app)
        
        # 创建测试数据
        self._create_test_data()

    def _create_test_data(self):
        """创建测试数据"""
        # 创建测试商户
        self.merchant1 = Merchant(
            id=1001,
            name="测试商户1",
            code="TEST_MERCHANT_1",
            api_key="test_api_key_1",
            api_secret="test_secret_1",
            status=True
        )
        self.merchant2 = Merchant(
            id=1002,
            name="测试商户2", 
            code="TEST_MERCHANT_2",
            api_key="test_api_key_2",
            api_secret="test_secret_2",
            status=True
        )
        self.db.add_all([self.merchant1, self.merchant2])
        
        # 创建测试部门
        self.department1 = Department(
            id=2001,
            merchant_id=1001,
            name="测试部门1",
            code="DEPT_1",
            status=True
        )
        self.department2 = Department(
            id=2002,
            merchant_id=1002,
            name="测试部门2",
            code="DEPT_2", 
            status=True
        )
        self.db.add_all([self.department1, self.department2])
        
        # 创建测试用户
        self.superuser = User(
            id=3001,
            username="superuser",
            email="<EMAIL>",
            hashed_password="hashed_password",
            is_superuser=True,
            is_active=True
        )
        self.merchant_admin = User(
            id=3002,
            username="merchant_admin",
            email="<EMAIL>",
            hashed_password="hashed_password",
            merchant_id=1001,
            department_id=2001,
            is_active=True
        )
        self.ck_supplier = User(
            id=3003,
            username="ck_supplier",
            email="<EMAIL>",
            hashed_password="hashed_password",
            merchant_id=1002,
            department_id=2002,
            is_active=True
        )
        self.db.add_all([self.superuser, self.merchant_admin, self.ck_supplier])
        
        # 创建测试绑卡记录
        self._create_card_records()
        
        self.db.commit()

    def _create_card_records(self):
        """创建测试绑卡记录"""
        # 商户1的记录
        for i in range(10):
            card = CardRecord(
                id=str(uuid.uuid4()),
                merchant_id=1001,
                department_id=2001,
                merchant_order_id=f"ORDER_1_{i}",
                amount=10000 + i * 100,
                card_number=f"1234567890123{i:03d}",
                status=CardStatus.SUCCESS if i < 7 else CardStatus.FAILED,
                request_id=f"REQ_1_{i}",
                request_data={"test": f"data_{i}"}
            )
            self.db.add(card)
        
        # 商户2的记录
        for i in range(5):
            card = CardRecord(
                id=str(uuid.uuid4()),
                merchant_id=1002,
                department_id=2002,
                merchant_order_id=f"ORDER_2_{i}",
                amount=20000 + i * 200,
                card_number=f"2234567890123{i:03d}",
                status=CardStatus.SUCCESS if i < 3 else CardStatus.FAILED,
                request_id=f"REQ_2_{i}",
                request_data={"test": f"data_{i}"}
            )
            self.db.add(card)

    def _get_auth_headers(self, user: User):
        """获取认证头"""
        # 模拟JWT token
        return {"Authorization": f"Bearer test_token_{user.id}"}

    def _mock_current_user(self, user: User):
        """模拟当前用户"""
        def override_get_current_user():
            return user
        
        app.dependency_overrides[get_current_active_user] = override_get_current_user

    def test_statistics_auto_superuser_all_data(self):
        """测试超级管理员获取所有数据统计"""
        self._mock_current_user(self.superuser)
        
        response = self.client.get(
            "/api/v1/cards/statistics",
            headers=self._get_auth_headers(self.superuser)
        )
        
        assert response.status_code == 200
        data = response.json()
        
        assert data["success"] is True
        assert "data" in data
        
        stats = data["data"]
        assert stats["total_count"] == 15  # 10 + 5 records
        assert stats["success_count"] == 10  # 7 + 3 success
        assert stats["failed_count"] == 5   # 3 + 2 failed
        assert stats["is_superuser"] is True

    def test_statistics_auto_superuser_specific_merchant(self):
        """测试超级管理员获取指定商户统计"""
        self._mock_current_user(self.superuser)
        
        response = self.client.get(
            "/api/v1/cards/statistics?merchant_id=1001",
            headers=self._get_auth_headers(self.superuser)
        )
        
        assert response.status_code == 200
        data = response.json()
        
        stats = data["data"]
        assert stats["total_count"] == 10  # Only merchant 1001
        assert stats["success_count"] == 7
        assert stats["failed_count"] == 3

    def test_statistics_auto_merchant_admin(self):
        """测试商户管理员只能看自己商户数据"""
        self._mock_current_user(self.merchant_admin)
        
        response = self.client.get(
            "/api/v1/cards/statistics",
            headers=self._get_auth_headers(self.merchant_admin)
        )
        
        assert response.status_code == 200
        data = response.json()
        
        stats = data["data"]
        assert stats["total_count"] == 10  # Only merchant 1001
        assert stats["success_count"] == 7
        assert stats["failed_count"] == 3
        assert stats["current_user_merchant_id"] == 1001
        assert stats["is_superuser"] is False

    def test_statistics_auto_ck_supplier(self):
        """测试CK供应商只能看自己部门数据"""
        self._mock_current_user(self.ck_supplier)
        
        response = self.client.get(
            "/api/v1/cards/statistics",
            headers=self._get_auth_headers(self.ck_supplier)
        )
        
        assert response.status_code == 200
        data = response.json()
        
        stats = data["data"]
        assert stats["total_count"] == 5   # Only merchant 1002
        assert stats["success_count"] == 3
        assert stats["failed_count"] == 2
        assert stats["current_user_merchant_id"] == 1002
        assert stats["current_user_department_id"] == 2002

    def test_statistics_with_filters(self):
        """测试带过滤条件的统计"""
        self._mock_current_user(self.superuser)
        
        # 测试状态过滤
        response = self.client.get(
            "/api/v1/cards/statistics?status=success",
            headers=self._get_auth_headers(self.superuser)
        )
        
        assert response.status_code == 200
        data = response.json()
        
        stats = data["data"]
        assert stats["total_count"] == 10  # Only success records
        assert stats["success_count"] == 10
        assert stats["failed_count"] == 0

    def test_statistics_with_card_number_search(self):
        """测试卡号搜索统计"""
        self._mock_current_user(self.superuser)
        
        response = self.client.get(
            "/api/v1/cards/statistics?card_number=1234567890123",
            headers=self._get_auth_headers(self.superuser)
        )
        
        assert response.status_code == 200
        data = response.json()
        
        stats = data["data"]
        assert stats["total_count"] == 10  # Only merchant 1001 cards

    def test_statistics_with_pagination(self):
        """测试分页统计"""
        self._mock_current_user(self.superuser)
        
        response = self.client.get(
            "/api/v1/cards/statistics?page=1&page_size=5",
            headers=self._get_auth_headers(self.superuser)
        )
        
        assert response.status_code == 200
        data = response.json()
        
        stats = data["data"]
        assert len(stats["items"]) == 5
        assert stats["page"] == 1
        assert stats["page_size"] == 5
        assert stats["total"] == 15
        assert stats["total_pages"] == 3

    def test_statistics_legacy_endpoint(self):
        """测试向后兼容的统计接口"""
        self._mock_current_user(self.superuser)
        
        response = self.client.get(
            "/api/v1/cards/statistics/1001",
            headers=self._get_auth_headers(self.superuser)
        )
        
        assert response.status_code == 200
        data = response.json()
        
        assert data["success"] is True
        assert "data" in data
        
        stats = data["data"]
        assert stats["merchant_id"] == 1001

    def test_statistics_permission_denied(self):
        """测试权限拒绝"""
        # 创建无权限用户
        no_permission_user = User(
            id=9999,
            username="no_permission",
            email="<EMAIL>",
            hashed_password="hashed_password",
            is_active=True
        )
        self.db.add(no_permission_user)
        self.db.commit()
        
        self._mock_current_user(no_permission_user)
        
        response = self.client.get(
            "/api/v1/cards/statistics",
            headers=self._get_auth_headers(no_permission_user)
        )
        
        # 应该返回403或相关权限错误
        assert response.status_code in [403, 500]  # 根据实际权限检查逻辑

    def teardown_method(self):
        """清理测试数据"""
        if hasattr(self, 'db'):
            self.db.rollback()
            self.db.close()
        
        # 清理依赖覆盖
        app.dependency_overrides.clear()
