#!/usr/bin/env python3
"""
沃尔玛绑卡系统数据完整性检查工具
检查现有数据中的商户CK隔离完整性问题
"""

import sys
import os
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker
from app.core.config import settings
from app.models.user import User
from app.models.merchant import Merchant
from app.models.department import Department
from app.models.walmart_ck import WalmartCK
from app.models.card_record import CardRecord
from app.services.walmart_ck_service_new import WalmartCKService


def create_session():
    """创建数据库会话"""
    engine = create_engine(settings.SQLALCHEMY_DATABASE_URI)
    SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
    return SessionLocal()


def check_basic_data_integrity(db):
    """检查基础数据完整性"""
    print("=== 基础数据完整性检查 ===")
    
    issues = []
    
    # 检查商户数据
    merchants = db.query(Merchant).all()
    print(f"商户总数: {len(merchants)}")
    
    for merchant in merchants:
        if not merchant.api_key or not merchant.api_secret:
            issues.append(f"商户 {merchant.id}({merchant.name}) 缺少API密钥")
    
    # 检查部门数据
    departments = db.query(Department).all()
    print(f"部门总数: {len(departments)}")
    
    for dept in departments:
        if not dept.merchant_id:
            issues.append(f"部门 {dept.id}({dept.name}) 缺少商户关联")
    
    # 检查CK数据
    cks = db.query(WalmartCK).all()
    print(f"CK总数: {len(cks)}")
    
    for ck in cks:
        if not ck.merchant_id:
            issues.append(f"CK {ck.id} 缺少商户关联")
        if ck.department_id:
            dept = db.query(Department).filter(Department.id == ck.department_id).first()
            if not dept:
                issues.append(f"CK {ck.id} 关联的部门 {ck.department_id} 不存在")
            elif dept.merchant_id != ck.merchant_id:
                issues.append(f"CK {ck.id} 的商户({ck.merchant_id})与部门的商户({dept.merchant_id})不匹配")
    
    # 检查卡记录数据
    card_records = db.query(CardRecord).all()
    print(f"卡记录总数: {len(card_records)}")
    
    if issues:
        print(f"\n❌ 发现 {len(issues)} 个基础数据问题:")
        for issue in issues:
            print(f"  - {issue}")
    else:
        print("✅ 基础数据完整性检查通过")
    
    return issues


def check_merchant_ck_isolation(db):
    """检查商户CK隔离完整性"""
    print("\n=== 商户CK隔离完整性检查 ===")
    
    ck_service = WalmartCKService(db)
    merchants = db.query(Merchant).all()
    
    all_issues = []
    
    for merchant in merchants:
        print(f"\n检查商户: {merchant.id}({merchant.name})")
        result = ck_service.check_merchant_isolation_integrity(merchant.id)
        
        if result['is_clean']:
            print(f"  ✅ 商户 {merchant.id} 隔离完整性正常")
        else:
            print(f"  ❌ 商户 {merchant.id} 发现 {result['issues_count']} 个问题:")
            for issue in result['issues']:
                print(f"    - {issue['description']}")
                all_issues.append({
                    'merchant_id': merchant.id,
                    'merchant_name': merchant.name,
                    'issue': issue
                })
    
    return all_issues


def check_card_record_ck_consistency(db):
    """检查卡记录与CK的一致性"""
    print("\n=== 卡记录CK一致性检查 ===")
    
    issues = []
    
    # 查询所有有CK关联的卡记录
    card_records_with_ck = db.query(CardRecord).filter(
        CardRecord.walmart_ck_id.isnot(None)
    ).all()
    
    print(f"有CK关联的卡记录数: {len(card_records_with_ck)}")
    
    for record in card_records_with_ck:
        # 检查CK是否存在
        ck = db.query(WalmartCK).filter(WalmartCK.id == record.walmart_ck_id).first()
        if not ck:
            issues.append({
                'type': 'missing_ck',
                'record_id': str(record.id),
                'ck_id': record.walmart_ck_id,
                'description': f"卡记录 {record.id} 关联的CK {record.walmart_ck_id} 不存在"
            })
            continue
        
        # 检查商户是否匹配
        if record.merchant_id != ck.merchant_id:
            issues.append({
                'type': 'merchant_mismatch',
                'record_id': str(record.id),
                'record_merchant': record.merchant_id,
                'ck_merchant': ck.merchant_id,
                'ck_id': record.walmart_ck_id,
                'description': f"卡记录 {record.id} 的商户({record.merchant_id})与CK {record.walmart_ck_id} 的商户({ck.merchant_id})不匹配"
            })
    
    if issues:
        print(f"❌ 发现 {len(issues)} 个卡记录CK一致性问题:")
        for issue in issues:
            print(f"  - {issue['description']}")
    else:
        print("✅ 卡记录CK一致性检查通过")
    
    return issues


def generate_statistics_report(db):
    """生成统计报告"""
    print("\n=== 统计报告 ===")
    
    # 商户统计
    merchants = db.query(Merchant).all()
    print(f"商户总数: {len(merchants)}")
    
    # CK统计
    total_cks = db.query(WalmartCK).count()
    active_cks = db.query(WalmartCK).filter(WalmartCK.active == True).count()
    print(f"CK总数: {total_cks}, 活跃CK: {active_cks}")
    
    # 卡记录统计
    total_records = db.query(CardRecord).count()
    success_records = db.query(CardRecord).filter(CardRecord.status == 'success').count()
    records_with_ck = db.query(CardRecord).filter(CardRecord.walmart_ck_id.isnot(None)).count()
    
    print(f"卡记录总数: {total_records}")
    print(f"成功绑卡记录: {success_records}")
    print(f"有CK关联的记录: {records_with_ck}")
    
    # 按商户统计
    print("\n按商户统计:")
    for merchant in merchants:
        merchant_cks = db.query(WalmartCK).filter(WalmartCK.merchant_id == merchant.id).count()
        merchant_records = db.query(CardRecord).filter(CardRecord.merchant_id == merchant.id).count()
        merchant_success = db.query(CardRecord).filter(
            CardRecord.merchant_id == merchant.id,
            CardRecord.status == 'success'
        ).count()
        
        print(f"  商户 {merchant.id}({merchant.name}): CK数={merchant_cks}, 记录数={merchant_records}, 成功数={merchant_success}")


def generate_fix_suggestions(all_issues):
    """生成修复建议"""
    print("\n=== 修复建议 ===")
    
    if not all_issues:
        print("✅ 没有发现需要修复的问题")
        return
    
    print("发现以下问题需要修复:")
    
    # 按问题类型分组
    issue_types = {}
    for issue_group in all_issues:
        if isinstance(issue_group, list):
            for issue in issue_group:
                issue_type = issue.get('type', 'unknown')
                if issue_type not in issue_types:
                    issue_types[issue_type] = []
                issue_types[issue_type].append(issue)
        elif isinstance(issue_group, dict):
            issue_type = issue_group.get('type', 'unknown')
            if issue_type not in issue_types:
                issue_types[issue_type] = []
            issue_types[issue_type].append(issue_group)
    
    for issue_type, issues in issue_types.items():
        print(f"\n{issue_type} 类问题 ({len(issues)} 个):")
        for issue in issues[:5]:  # 只显示前5个
            print(f"  - {issue.get('description', str(issue))}")
        if len(issues) > 5:
            print(f"  ... 还有 {len(issues) - 5} 个类似问题")
    
    print("\n建议的修复步骤:")
    print("1. 备份数据库")
    print("2. 修复商户CK关联问题")
    print("3. 清理无效的CK关联")
    print("4. 重新运行完整性检查")
    print("5. 验证修复结果")


def main():
    """主检查函数"""
    print("开始沃尔玛绑卡系统数据完整性检查")
    print(f"检查时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    db = create_session()
    
    try:
        # 执行各项检查
        basic_issues = check_basic_data_integrity(db)
        isolation_issues = check_merchant_ck_isolation(db)
        consistency_issues = check_card_record_ck_consistency(db)
        
        # 生成统计报告
        generate_statistics_report(db)
        
        # 汇总所有问题
        all_issues = [basic_issues, isolation_issues, consistency_issues]
        total_issues = sum(len(issues) if isinstance(issues, list) else 1 for issues in all_issues if issues)
        
        # 生成修复建议
        generate_fix_suggestions(all_issues)
        
        # 总结
        print(f"\n=== 检查总结 ===")
        if total_issues == 0:
            print("🎉 数据完整性检查通过！没有发现问题。")
        else:
            print(f"⚠️  发现 {total_issues} 个问题需要处理。")
        
        print(f"检查完成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
    except Exception as e:
        print(f"❌ 检查过程中发生错误: {e}")
        raise
    finally:
        db.close()


if __name__ == "__main__":
    main()
