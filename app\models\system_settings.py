from sqlalchemy import Column, String, Text, Boolean

from app.models.base import BaseModel, TimestampMixin


class SystemSettings(BaseModel, TimestampMixin):
    """
    系统设置模型

    用于存储系统级别的配置和设置
    """

    __tablename__ = "system_settings"

    # 设置键名
    key = Column(
        String(100), nullable=False, unique=True, index=True, comment="设置键名"
    )

    # 设置值
    value = Column(Text, nullable=True, comment="设置值")

    # 设置描述
    description = Column(String(255), nullable=True, comment="设置描述")

    # 是否启用
    is_enabled = Column(Boolean, default=True, nullable=False, comment="是否启用")

    # 是否为系统内置设置（不可删除）
    is_system = Column(
        Boolean, default=False, nullable=False, comment="是否为系统内置设置"
    )

    def __repr__(self):
        return f"<SystemSettings(id={self.id}, key={self.key})>"
