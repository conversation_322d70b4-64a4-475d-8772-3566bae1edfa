<template>
  <div class="page-container ck-statistics-page">
    <el-card shadow="never">
      <template #header>
        <div class="card-header">
          <div class="header-left">
            <el-button :icon="ArrowLeft" @click="goBack">返回</el-button>
            <span class="header-title">CK统计详情</span>
          </div>
          <div class="header-right">
            <el-button type="primary" :icon="Refresh" @click="refreshData" :loading="loading">刷新数据</el-button>
          </div>
        </div>
      </template>

      <div v-loading="loading" class="statistics-content">
        <!-- 时间范围选择 -->
        <div class="time-range-selector">
          <el-radio-group v-model="timeRange" @change="handleTimeRangeChange">
            <el-radio-button value="today">今日</el-radio-button>
            <el-radio-button value="week">本周</el-radio-button>
            <el-radio-button value="month">本月</el-radio-button>
          </el-radio-group>
        </div>

        <!-- CK基本信息 -->
        <el-card class="info-card" shadow="hover">
          <template #header>
            <div class="card-header">
              <span>CK基本信息</span>
            </div>
          </template>
          <el-row :gutter="20">
            <el-col :span="8">
              <div class="info-item">
                <span class="label">CK签名:</span>
                <span class="value">{{ maskCKSign(ckInfo?.sign) || 'N/A' }}</span>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="info-item">
                <span class="label">描述:</span>
                <span class="value">{{ ckInfo?.description || '无描述' }}</span>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="info-item">
                <span class="label">状态:</span>
                <el-tag :type="ckInfo?.active ? 'success' : 'danger'">
                  {{ ckInfo?.active ? '启用' : '禁用' }}
                </el-tag>
              </div>
            </el-col>
          </el-row>
          <el-row :gutter="20" style="margin-top: 15px;">
            <el-col :span="8">
              <div class="info-item">
                <span class="label">所属商户:</span>
                <span class="value">{{ ckInfo?.merchant_name || 'N/A' }}</span>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="info-item">
                <span class="label">所属部门:</span>
                <span class="value">{{ ckInfo?.department_name || 'N/A' }}</span>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="info-item">
                <span class="label">总限额:</span>
                <span class="value">{{ ckInfo?.total_limit || 0 }}</span>
              </div>
            </el-col>
          </el-row>
        </el-card>

        <!-- 统计数据卡片 -->
        <el-row :gutter="20" style="margin-top: 20px;">
          <!-- 绑卡数量统计 -->
          <el-col :span="12">
            <el-card class="stats-card" shadow="hover">
              <template #header>
                <div class="card-header">
                  <span>绑卡数量统计</span>
                </div>
              </template>
              <div class="stats-grid">
                <div class="stat-item">
                  <div class="stat-value">{{ basicStats?.total_count || 0 }}</div>
                  <div class="stat-label">总绑卡数</div>
                </div>
                <div class="stat-item success">
                  <div class="stat-value">{{ basicStats?.success_count || 0 }}</div>
                  <div class="stat-label">成功数</div>
                </div>
                <div class="stat-item failed">
                  <div class="stat-value">{{ basicStats?.failed_count || 0 }}</div>
                  <div class="stat-label">失败数</div>
                </div>
                <div class="stat-item rate">
                  <div class="stat-value">{{ basicStats?.success_rate || 0 }}%</div>
                  <div class="stat-label">成功率</div>
                </div>
              </div>
            </el-card>
          </el-col>

          <!-- 绑卡金额统计 -->
          <el-col :span="12">
            <el-card class="stats-card" shadow="hover">
              <template #header>
                <div class="card-header">
                  <span>绑卡金额统计</span>
                </div>
              </template>
              <div class="stats-grid">
                <div class="stat-item">
                  <div class="stat-value">{{ formatAmount(amountStats?.total_amount) }}</div>
                  <div class="stat-label">总请求金额</div>
                </div>
                <div class="stat-item success">
                  <div class="stat-value">{{ formatAmount(amountStats?.actual_amount) }}</div>
                  <div class="stat-label">实际绑卡金额</div>
                </div>
                <div class="stat-item">
                  <div class="stat-value">{{ formatAmount(amountStats?.avg_amount) }}</div>
                  <div class="stat-label">平均金额</div>
                </div>
                <div class="stat-item">
                  <div class="stat-value">{{ performanceStats?.avg_process_time || 0 }}s</div>
                  <div class="stat-label">平均处理时间</div>
                </div>
              </div>
            </el-card>
          </el-col>
        </el-row>

        <!-- 每日趋势图表 -->
        <el-card class="trend-card" shadow="hover" style="margin-top: 20px;">
          <template #header>
            <div class="card-header">
              <span>每日绑卡趋势</span>
            </div>
          </template>
          <div class="trend-chart">
            <el-table :data="dailyTrendData" size="small" max-height="300">
              <el-table-column prop="date" label="日期" width="120" />
              <el-table-column prop="total_count" label="总数" width="80" align="center" />
              <el-table-column prop="success_count" label="成功" width="80" align="center" />
              <el-table-column prop="failed_count" label="失败" width="80" align="center" />
              <el-table-column prop="success_rate" label="成功率" width="100" align="center">
                <template #default="scope">
                  <el-tag
                    :type="scope.row.success_rate >= 80 ? 'success' : scope.row.success_rate >= 60 ? 'warning' : 'danger'"
                    size="small">
                    {{ scope.row.success_rate }}%
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column prop="success_amount" label="成功金额" align="right">
                <template #default="scope">
                  {{ formatAmount(scope.row.success_amount) }}
                </template>
              </el-table-column>
            </el-table>
          </div>
        </el-card>

        <!-- 失败原因分析 -->
        <el-card class="failure-card" shadow="hover" style="margin-top: 20px;">
          <template #header>
            <div class="card-header">
              <span>主要失败原因</span>
            </div>
          </template>
          <div class="failure-analysis">
            <el-table :data="failureAnalysisData" size="small" max-height="300">
              <el-table-column prop="rank" label="排名" width="80" align="center" />
              <el-table-column prop="error_message" label="失败原因" min-width="300" show-overflow-tooltip />
              <el-table-column prop="count" label="次数" width="100" align="center" />
              <el-table-column prop="percentage" label="占比" width="100" align="center">
                <template #default="scope">
                  <el-progress :percentage="scope.row.percentage" :stroke-width="6" :show-text="false" />
                  <span style="margin-left: 8px;">{{ scope.row.percentage }}%</span>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </el-card>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ElMessage } from 'element-plus'
import { ArrowLeft, Refresh } from '@element-plus/icons-vue'
import { walmartCKApi } from '@/api/modules/walmartCK'

const router = useRouter()
const route = useRoute()

const loading = ref(false)
const timeRange = ref('month')

// 统计数据
const statisticsData = ref({})
const dailyTrendData = ref([])
const failureAnalysisData = ref([])

// 计算属性
const ckInfo = computed(() => statisticsData.value.ck_info || {})
const basicStats = computed(() => statisticsData.value.basic_stats || {})
const amountStats = computed(() => statisticsData.value.amount_stats || {})
const performanceStats = computed(() => statisticsData.value.performance_stats || {})

// 获取CK ID
const ckId = computed(() => route.params.id)

// 返回上一页
const goBack = () => {
  router.push('/walmart/user')
}

// 获取统计数据
const fetchStatisticsData = async () => {
  if (!ckId.value) {
    ElMessage.error('CK ID不存在')
    goBack()
    return
  }

  loading.value = true
  try {
    // 获取基础统计数据
    const statsResponse = await walmartCKApi.getStatistics(ckId.value, {
      time_range: timeRange.value
    })
    statisticsData.value = statsResponse.data || {}

    // 获取每日趋势数据
    const trendResponse = await walmartCKApi.getDailyTrend(ckId.value, {
      days: timeRange.value === 'today' ? 7 : timeRange.value === 'week' ? 14 : 30
    })
    dailyTrendData.value = trendResponse.data || []

    // 获取失败原因分析
    const failureResponse = await walmartCKApi.getFailureAnalysis(ckId.value, {
      limit: 10
    })
    failureAnalysisData.value = failureResponse.data || []

  } catch (error) {
    console.error('获取CK统计数据失败:', error)
    ElMessage.error('获取CK统计数据失败')
  } finally {
    loading.value = false
  }
}

// 时间范围变化处理
const handleTimeRangeChange = () => {
  fetchStatisticsData()
}

// 刷新数据
const refreshData = () => {
  fetchStatisticsData()
}

// 隐藏CK签名的中间部分
const maskCKSign = (sign) => {
  if (!sign) return ''

  // CK格式：用户名@域名#密钥#版本号
  const parts = sign.split('#')
  if (parts.length !== 3) return sign // 格式不正确，直接返回原值

  const [userDomain, key, version] = parts

  // 隐藏密钥部分的中间内容，只显示前3位和后3位
  let maskedKey = key
  if (key.length > 6) {
    maskedKey = key.substring(0, 3) + '***' + key.substring(key.length - 3)
  }

  // 隐藏用户名@域名部分的中间内容
  let maskedUserDomain = userDomain
  if (userDomain.length > 10) {
    const atIndex = userDomain.indexOf('@')
    if (atIndex > 0) {
      const username = userDomain.substring(0, atIndex)
      const domain = userDomain.substring(atIndex + 1)

      // 隐藏用户名中间部分
      let maskedUsername = username
      if (username.length > 6) {
        maskedUsername = username.substring(0, 3) + '***' + username.substring(username.length - 3)
      }

      // 隐藏域名中间部分
      let maskedDomain = domain
      if (domain.length > 6) {
        maskedDomain = domain.substring(0, 3) + '***' + domain.substring(domain.length - 3)
      }

      maskedUserDomain = maskedUsername + '@' + maskedDomain
    }
  }

  return `${maskedUserDomain}#${maskedKey}#${version}`
}

// 格式化金额显示
const formatAmount = (amount) => {
  if (!amount || amount === 0) return '0.00'
  // 将分转换为元，保留两位小数
  return (amount / 100).toFixed(2)
}

// 页面加载时获取数据
onMounted(() => {
  fetchStatisticsData()
})
</script>

<style scoped>
.ck-statistics-page {
  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .header-left {
    display: flex;
    align-items: center;
    gap: 12px;
  }

  .header-title {
    font-size: 18px;
    font-weight: 600;
    color: var(--el-text-color-primary);
  }
}

.statistics-content {
  min-height: 400px;
}

.time-range-selector {
  text-align: center;
  margin-bottom: 20px;
}

.info-card,
.stats-card,
.trend-card,
.failure-card {
  margin-bottom: 20px;
}

.info-item {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}

.info-item .label {
  font-weight: 600;
  color: var(--el-text-color-regular);
  margin-right: 10px;
  min-width: 80px;
}

.info-item .value {
  color: var(--el-text-color-primary);
  word-break: break-all;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20px;
}

.stat-item {
  text-align: center;
  padding: 20px;
  border-radius: 8px;
  background: var(--el-fill-color-lighter);
  transition: all 0.3s ease;
}

.stat-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.stat-item.success {
  background: linear-gradient(135deg, #67c23a20, #67c23a10);
  border: 1px solid #67c23a30;
}

.stat-item.failed {
  background: linear-gradient(135deg, #f5606620, #f5606610);
  border: 1px solid #f5606630;
}

.stat-item.rate {
  background: linear-gradient(135deg, #409eff20, #409eff10);
  border: 1px solid #409eff30;
}

.stat-value {
  font-size: 24px;
  font-weight: 700;
  color: var(--el-text-color-primary);
  margin-bottom: 5px;
}

.stat-label {
  font-size: 14px;
  color: var(--el-text-color-regular);
  font-weight: 500;
}
</style>
