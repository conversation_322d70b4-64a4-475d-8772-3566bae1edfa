from typing import Any
from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session

from app.api import deps
from app.crud.notification_config import notification_config_crud
from app.schemas.notification_config import (
    NotificationConfigCreate,
    NotificationConfigUpdate,
    NotificationConfigInDB,
)
from app.services.permission_service import PermissionService
from app.models.user import User

router = APIRouter()


@router.get("/", response_model=NotificationConfigInDB)
def get_notification_config(
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_active_user),
) -> Any:
    """
    获取通知配置

    权限要求:
    - "api:notification-configs:read": 查看通知配置权限
    """
    # 权限检查
    permission_service = PermissionService(db)
    has_permission = permission_service.check_user_permission(
        current_user, "api:notification-configs:read"
    )
    if not has_permission:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="没有查看通知配置的权限",
        )

    config = notification_config_crud.get_or_create_config(db)
    return config


@router.put("/{config_id}", response_model=NotificationConfigInDB)
def update_notification_config(
    config_id: int,
    config_in: NotificationConfigUpdate,
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_active_user),
) -> Any:
    """
    更新通知配置

    权限要求:
    - "api:notification-configs:update": 更新通知配置权限
    """
    # 权限检查
    permission_service = PermissionService(db)
    has_permission = permission_service.check_user_permission(
        current_user, "api:notification-configs:update"
    )
    if not has_permission:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="没有更新通知配置的权限",
        )

    config = notification_config_crud.update_config(
        db, config_id=config_id, update_data=config_in.model_dump(exclude_unset=True)
    )
    if not config:
        raise HTTPException(status_code=404, detail="通知配置不存在")
    return config


@router.post("/test", status_code=200)
async def test_notification(
    db: Session = Depends(deps.get_db),
    current_user: Any = Depends(deps.get_current_active_superuser),
) -> Any:
    """测试通知配置"""
    from app.utils.notification import send_alert

    config = notification_config_crud.get_config(db)
    if not config:
        raise HTTPException(status_code=404, detail="通知配置不存在")

    try:
        await send_alert(
            title="测试通知",
            content={
                "message": "这是一条测试通知，用于验证通知配置是否正确。",
                "test_time": "当前时间",
            },
        )
        return {"message": "测试通知已发送"}
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"发送测试通知失败: {str(e)}")
