<template>
  <div class="binding-quick-actions">
    <el-card shadow="hover" class="actions-card">
      <template #header>
        <div class="card-header">
          <span class="title">
            <el-icon><Setting /></el-icon>
            快速绑卡设置
          </span>
          <el-tag type="info" size="small">
            已选择 {{ selectedCount }} 个部门
          </el-tag>
        </div>
      </template>
      
      <div class="quick-actions">
        <!-- 快速开关操作 -->
        <div class="action-group">
          <span class="group-label">进单开关：</span>
          <el-button-group>
            <el-button 
              size="small" 
              type="success"
              @click="batchSetBinding(true)"
              :disabled="selectedCount === 0"
            >
              <el-icon><Check /></el-icon>
              全部启用
            </el-button>
            <el-button 
              size="small" 
              type="danger"
              @click="batchSetBinding(false)"
              :disabled="selectedCount === 0"
            >
              <el-icon><Close /></el-icon>
              全部禁用
            </el-button>
          </el-button-group>
        </div>
        
        <!-- 快速权重设置 -->
        <div class="action-group">
          <span class="group-label">权重设置：</span>
          <div class="weight-actions">
            <el-button-group>
              <el-button 
                size="small"
                @click="batchSetWeight(50)"
                :disabled="selectedCount === 0"
              >
                低权重(50)
              </el-button>
              <el-button 
                size="small"
                @click="batchSetWeight(100)"
                :disabled="selectedCount === 0"
              >
                标准权重(100)
              </el-button>
              <el-button 
                size="small"
                @click="batchSetWeight(200)"
                :disabled="selectedCount === 0"
              >
                高权重(200)
              </el-button>
            </el-button-group>
            
            <div class="custom-weight">
              <el-input-number
                v-model="customWeight"
                :min="0"
                :max="10000"
                :step="10"
                size="small"
                style="width: 120px"
                placeholder="自定义权重"
              />
              <el-button 
                size="small" 
                type="primary"
                @click="batchSetWeight(customWeight)"
                :disabled="selectedCount === 0 || !customWeight"
              >
                应用
              </el-button>
            </div>
          </div>
        </div>
        
        <!-- 预设配置 -->
        <div class="action-group">
          <span class="group-label">预设配置：</span>
          <el-button-group>
            <el-button 
              size="small"
              type="warning"
              @click="applyPreset('high_priority')"
              :disabled="selectedCount === 0"
            >
              高优先级部门
            </el-button>
            <el-button 
              size="small"
              type="info"
              @click="applyPreset('normal')"
              :disabled="selectedCount === 0"
            >
              普通部门
            </el-button>
            <el-button 
              size="small"
              @click="applyPreset('backup')"
              :disabled="selectedCount === 0"
            >
              备用部门
            </el-button>
          </el-button-group>
        </div>
        
        <!-- 统计信息 -->
        <div class="action-group">
          <span class="group-label">统计信息：</span>
          <div class="stats-info">
            <el-tag size="small" type="success">
              启用: {{ enabledCount }}
            </el-tag>
            <el-tag size="small" type="danger">
              禁用: {{ disabledCount }}
            </el-tag>
            <el-tag size="small" type="info">
              平均权重: {{ averageWeight }}
            </el-tag>
          </div>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script>
import { ref, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Setting, Check, Close } from '@element-plus/icons-vue'

export default {
  name: 'DepartmentBindingQuickActions',
  components: {
    Setting,
    Check,
    Close
  },
  props: {
    selectedDepartments: {
      type: Array,
      default: () => []
    }
  },
  emits: ['batch-update'],
  setup(props, { emit }) {
    const customWeight = ref(100)
    
    // 计算属性
    const selectedCount = computed(() => props.selectedDepartments.length)
    
    const enabledCount = computed(() => {
      return props.selectedDepartments.filter(dept => dept.enable_binding).length
    })
    
    const disabledCount = computed(() => {
      return props.selectedDepartments.filter(dept => !dept.enable_binding).length
    })
    
    const averageWeight = computed(() => {
      if (props.selectedDepartments.length === 0) return 0
      const total = props.selectedDepartments.reduce((sum, dept) => sum + (dept.binding_weight || 0), 0)
      return Math.round(total / props.selectedDepartments.length)
    })
    
    // 方法
    const batchSetBinding = async (enableBinding) => {
      try {
        await ElMessageBox.confirm(
          `确定要${enableBinding ? '启用' : '禁用'}选中的 ${selectedCount.value} 个部门的进单功能吗？`,
          '批量设置确认',
          {
            type: 'warning',
            confirmButtonText: '确定',
            cancelButtonText: '取消'
          }
        )
        
        const updateData = {
          department_ids: props.selectedDepartments.map(dept => dept.id),
          enable_binding: enableBinding
        }
        
        emit('batch-update', updateData)
        
      } catch (error) {
        if (error !== 'cancel') {
          console.error('批量设置开关失败:', error)
        }
      }
    }
    
    const batchSetWeight = async (weight) => {
      if (!weight || weight < 0 || weight > 10000) {
        ElMessage.warning('权重值必须在0-10000之间')
        return
      }
      
      try {
        await ElMessageBox.confirm(
          `确定要将选中的 ${selectedCount.value} 个部门的权重设置为 ${weight} 吗？`,
          '批量设置权重确认',
          {
            type: 'warning',
            confirmButtonText: '确定',
            cancelButtonText: '取消'
          }
        )
        
        const updateData = {
          department_ids: props.selectedDepartments.map(dept => dept.id),
          binding_weight: weight
        }
        
        emit('batch-update', updateData)
        
      } catch (error) {
        if (error !== 'cancel') {
          console.error('批量设置权重失败:', error)
        }
      }
    }
    
    const applyPreset = async (presetType) => {
      const presets = {
        high_priority: {
          enable_binding: true,
          binding_weight: 500,
          name: '高优先级部门'
        },
        normal: {
          enable_binding: true,
          binding_weight: 100,
          name: '普通部门'
        },
        backup: {
          enable_binding: true,
          binding_weight: 10,
          name: '备用部门'
        }
      }
      
      const preset = presets[presetType]
      if (!preset) return
      
      try {
        await ElMessageBox.confirm(
          `确定要将选中的 ${selectedCount.value} 个部门应用"${preset.name}"配置吗？\n` +
          `进单开关：${preset.enable_binding ? '启用' : '禁用'}\n` +
          `权重设置：${preset.binding_weight}`,
          '应用预设配置确认',
          {
            type: 'warning',
            confirmButtonText: '确定',
            cancelButtonText: '取消'
          }
        )
        
        const updateData = {
          department_ids: props.selectedDepartments.map(dept => dept.id),
          enable_binding: preset.enable_binding,
          binding_weight: preset.binding_weight
        }
        
        emit('batch-update', updateData)
        
      } catch (error) {
        if (error !== 'cancel') {
          console.error('应用预设配置失败:', error)
        }
      }
    }
    
    return {
      customWeight,
      selectedCount,
      enabledCount,
      disabledCount,
      averageWeight,
      batchSetBinding,
      batchSetWeight,
      applyPreset
    }
  }
}
</script>

<style scoped>
.binding-quick-actions {
  margin-bottom: 20px;
}

.actions-card {
  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    
    .title {
      display: flex;
      align-items: center;
      gap: 8px;
      font-weight: 600;
      color: #303133;
    }
  }
}

.quick-actions {
  .action-group {
    display: flex;
    align-items: center;
    margin-bottom: 15px;
    
    &:last-child {
      margin-bottom: 0;
    }
    
    .group-label {
      min-width: 80px;
      color: #606266;
      font-weight: 500;
      margin-right: 15px;
    }
    
    .weight-actions {
      display: flex;
      align-items: center;
      gap: 15px;
      
      .custom-weight {
        display: flex;
        align-items: center;
        gap: 8px;
      }
    }
    
    .stats-info {
      display: flex;
      gap: 10px;
    }
  }
}

.el-button-group {
  .el-button {
    margin: 0;
  }
}
</style>
