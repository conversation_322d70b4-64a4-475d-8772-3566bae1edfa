# 数据库测试脚本目录

## 目录说明
此目录包含沃尔玛绑卡系统的数据库测试和验证脚本，用于验证数据库结构、权限配置和安全修复的正确性。

## 测试脚本列表

### 安全验证脚本
- `test_security_fixes.sql` - 安全修复验证测试
  - 验证权限修复效果
  - 检查菜单清理状态
  - 验证幂等性
  - 综合安全评估

- `verify_file_order.sql` - 文件执行顺序验证
  - 验证主键升级效果
  - 检查TOTP权限配置
  - 验证菜单清理效果
  - 文件执行顺序验证

## 使用方法

### 测试环境执行
```bash
# 执行安全修复验证
mysql -u root -p walmart_card_db < test/database/test_security_fixes.sql

# 执行文件顺序验证
mysql -u root -p walmart_card_db < test/database/verify_file_order.sql
```

### Docker环境执行
```bash
# 安全修复验证
docker exec -i mysql_container mysql -u root -p walmart_card_db < test/database/test_security_fixes.sql

# 文件顺序验证
docker exec -i mysql_container mysql -u root -p walmart_card_db < test/database/verify_file_order.sql
```

## 测试内容

### test_security_fixes.sql
1. **迁移日志表检查** - 验证migration_logs表存在性
2. **安全修复执行状态** - 检查所有修复脚本执行状态
3. **权限修复效果验证** - 验证商户管理员和CK供应商权限
4. **菜单清理效果验证** - 检查特殊页面和重复菜单清理
5. **幂等性验证** - 测试重复执行的安全性
6. **安全性验证总结** - 综合评估和建议

### verify_file_order.sql
1. **文件执行顺序验证** - 检查迁移日志和执行状态
2. **主键升级效果验证** - 验证INT到BIGINT的升级
3. **TOTP权限配置验证** - 检查各角色TOTP权限分配
4. **菜单清理效果验证** - 验证特殊页面菜单清理
5. **文件执行顺序验证总结** - 综合验证结果

## 预期结果
- 所有检查项应显示 ✓ 状态
- 异常项会显示 ⚠ 或 ✗ 状态
- 提供详细的状态说明和建议

## 注意事项
1. **仅测试环境使用** - 这些脚本仅用于测试环境验证
2. **不修改数据** - 测试脚本只读取和验证，不修改数据
3. **定期执行** - 建议在每次升级后执行验证
4. **结果分析** - 仔细分析测试结果，确保系统正常

## 故障排除
如果测试失败：
1. 检查数据库连接和权限
2. 确认相关升级脚本已正确执行
3. 查看migration_logs表的详细错误信息
4. 根据测试输出的建议进行修复