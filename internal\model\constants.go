package model

// 回调状态常量
const (
	CallbackStatusPending = "pending"
	CallbackStatusSuccess = "success"
	CallbackStatusFailed  = "failed"
	CallbackStatusRetry   = "retry"
)

// 绑卡状态常量
const (
	BindingStatusPending    = "pending"
	BindingStatusProcessing = "processing"
	BindingStatusSuccess    = "success"
	BindingStatusFailed     = "failed"
	BindingStatusCancelled  = "cancelled"
)

// CK状态常量
const (
	CKStatusActive   = "active"
	CKStatusInactive = "inactive"
	CKStatusBlocked  = "blocked"
	CKStatusExpired  = "expired"
)

// 日志级别常量
const (
	LogLevelDebug = "debug"
	LogLevelInfo  = "info"
	LogLevelWarn  = "warn"
	LogLevelError = "error"
	LogLevelFatal = "fatal"
)

// 日志类型常量
const (
	LogTypeBinding  = "binding"
	LogTypeCallback = "callback"
	LogTypeSystem   = "system"
	LogTypeAudit    = "audit"
	LogTypeSecurity = "security"
)