"""
优化的回调服务
针对高并发场景进行性能优化
"""
import uuid
import asyncio
from typing import Dict, Any, Optional, List
from contextlib import asynccontextmanager

import httpx
from sqlalchemy.orm import Session
from sqlalchemy.ext.asyncio import AsyncSession
from typing import Union

from app.core.logging import get_logger
from app.crud import card as card_crud
from app.crud.card import get_card_record_async
from app.crud import merchant as merchant_crud
from app.models.base import local_now
from app.models.card_record import CallbackStatus, CardStatus
from app.utils.queue_producer import send_callback_task
from app.db.session import SessionLocal

# 创建日志记录器
logger = get_logger("optimized_callback_service")

# 回调重试配置
MAX_CALLBACK_RETRIES = 5
MIN_RETRY_WAIT = 5
MAX_RETRY_WAIT = 60 * 30

# 从配置文件读取HTTP客户端配置
from app.core.config import settings

def get_http_client_config():
    """获取HTTP客户端配置"""
    performance_config = settings.yaml_config.get("performance", {})
    http_config = performance_config.get("http_client", {})

    return {
        "timeout": httpx.Timeout(
            http_config.get("timeout", 10.0),
            connect=http_config.get("connect_timeout", 5.0)
        ),
        "limits": httpx.Limits(
            max_keepalive_connections=http_config.get("max_keepalive_connections", 100),
            max_connections=http_config.get("max_connections", 200)
        ),
        "follow_redirects": True,
    }


class OptimizedCallbackService:
    """优化的回调服务"""
    
    def __init__(self):
        self._http_client: Optional[httpx.AsyncClient] = None
        self._client_lock = asyncio.Lock()
    
    @asynccontextmanager
    async def get_http_client(self):
        """获取HTTP客户端（单例模式）"""
        async with self._client_lock:
            if self._http_client is None or self._http_client.is_closed:
                self._http_client = httpx.AsyncClient(**get_http_client_config())
        
        try:
            yield self._http_client
        except Exception as e:
            logger.error(f"HTTP客户端使用异常: {e}")
            # 如果客户端出现问题，重置以便下次重新创建
            if self._http_client and not self._http_client.is_closed:
                await self._http_client.aclose()
            self._http_client = None
            raise
    
    async def close(self):
        """关闭HTTP客户端"""
        if self._http_client and not self._http_client.is_closed:
            await self._http_client.aclose()
    
    async def process_callback_batch(self, db: Session, callback_batch: List[Dict[str, Any]]):
        """
        批量处理回调请求
        
        Args:
            db: 数据库会话
            callback_batch: 回调数据批次
        """
        if not callback_batch:
            return
        
        logger.info(f"开始批量处理回调，数量: {len(callback_batch)}")
        
        # 并发处理回调
        tasks = []
        for callback_data in callback_batch:
            task = asyncio.create_task(
                self._process_single_callback(db, callback_data)
            )
            tasks.append(task)
        
        # 等待所有回调完成
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # 统计结果
        success_count = sum(1 for r in results if r is True)
        failed_count = len(results) - success_count
        
        logger.info(f"批量回调处理完成 - 成功: {success_count}, 失败: {failed_count}")
    
    async def process_callback_from_queue(self, db: Union[Session, AsyncSession], data: Dict[str, Any]):
        """
        从队列处理单个回调请求（兼容原接口）
        """
        await self._process_single_callback(db, data)
    
    async def _process_single_callback(self, db: Session, data: Dict[str, Any]) -> bool:
        """处理单个回调请求"""
        record_id_str = data.get("record_id")
        merchant_id = data.get("merchant_id")
        retry_count = data.get("retry_count", 0)
        ext_data = data.get("ext_data")
        trace_id = data.get("trace_id")

        if not record_id_str or not merchant_id:
            logger.error(f"回调队列数据不完整: {data}")
            return False

        try:
            record_id = uuid.UUID(record_id_str)
        except ValueError:
            logger.error(f"无效的 record_id格式 (非UUID): {record_id_str}")
            return False

        # 检查重试次数
        if retry_count >= MAX_CALLBACK_RETRIES:
            logger.warning(
                f"回调重试次数已达上限 ({MAX_CALLBACK_RETRIES})，不再重试: 记录ID {record_id}"
            )
            return False

        # 执行回调
        success = await self._execute_callback(
            db, record_id, merchant_id, retry_count, ext_data, trace_id
        )

        # 处理重试
        if not success and retry_count < MAX_CALLBACK_RETRIES - 1:
            await self._schedule_retry(
                record_id_str, merchant_id, retry_count, ext_data, trace_id
            )

        return success
    
    async def _execute_callback(
        self,
        db: Session,
        record_id: uuid.UUID,
        merchant_id: int,
        retry_count: int,
        ext_data: Optional[str] = None,
        trace_id: Optional[str] = None,
    ) -> bool:
        """执行回调请求"""
        try:
            # 使用独立的数据库会话避免连接池竞争
            async with self._get_db_session() as session:
                # 验证记录和商户
                record, merchant = await self._validate_callback_prerequisites(
                    session, record_id, merchant_id, retry_count
                )
                if not record or not merchant:
                    return True

                # 准备回调数据
                callback_data = self._prepare_callback_data(record, trace_id, retry_count, ext_data)

                # 发送回调请求
                return await self._send_callback_request(
                    session, record, merchant, callback_data, record_id, retry_count
                )
        except Exception as e:
            logger.exception(f"回调执行异常: {e}")
            return False
    
    @asynccontextmanager
    async def _get_db_session(self):
        """获取数据库会话的上下文管理器"""
        session = SessionLocal()
        try:
            yield session
        finally:
            session.close()
    
    async def _validate_callback_prerequisites(
        self, db: Union[Session, AsyncSession], record_id: uuid.UUID, merchant_id: int, retry_count: int
    ):
        """验证回调前置条件 - 优化版本，减少阻塞"""
        # 减少重试次数，避免长时间阻塞
        max_retries = 3
        base_wait_time = 0.05  # 减少基础等待时间到50ms

        for attempt in range(max_retries):
            try:
                # 【优化】使用非阻塞的方式刷新会话
                try:
                    db.expire_all()
                    db.commit()
                except Exception as db_error:
                    logger.debug(f"数据库会话刷新失败，继续查询: {db_error}")

                # 获取记录 - 使用包含商户隔离的方法
                # 检查是否为异步会话，使用相应的方法
                if isinstance(db, AsyncSession):
                    record = await get_card_record_async(db, str(record_id), merchant_id)
                else:
                    record = card_crud.get_card_record(db, str(record_id), merchant_id)
                if record:
                    if attempt > 0:  # 只在重试成功时记录
                        logger.debug(f"记录查询成功，重试次数: {attempt + 1}")
                    break

                if attempt < max_retries - 1:
                    # 【优化】使用更短的等待时间：50ms, 100ms
                    wait_time = base_wait_time * (attempt + 1)
                    logger.debug(f"记录暂时不存在，等待 {wait_time*1000:.0f}ms 后重试 (第{attempt + 1}次): {record_id}")
                    await asyncio.sleep(wait_time)
                else:
                    logger.warning(f"记录不存在，已重试{max_retries}次: {record_id}")
                    return None, None
            except Exception as e:
                logger.error(f"查询记录时出错 (第{attempt + 1}次): {e}")
                if attempt == max_retries - 1:
                    return None, None
                # 【优化】异常时使用更短的等待时间
                await asyncio.sleep(base_wait_time * (attempt + 1))

        # 检查是否已经回调成功
        if record.callback_status == CallbackStatus.SUCCESS:
            logger.info(f"记录已回调成功，跳过: {record_id}")
            return None, None

        # 获取商户信息
        merchant = merchant_crud.get_merchant(db, merchant_id)
        if not merchant:
            logger.error(f"商户不存在: {merchant_id}")
            return None, None

        if not merchant.callback_url:
            logger.warning(f"商户未配置回调URL: {merchant_id}")
            return None, None

        return record, merchant
    
    def _prepare_callback_data(self, record, trace_id: Optional[str], retry_count: int, ext_data: Optional[str]) -> dict:
        """准备回调数据 - 使用增强的验证器"""
        from app.services.callback_data_validator import CallbackDataValidator

        # 检查余额获取是否成功
        balance_fetch_success = (
            record.balance is not None or
            record.cardBalance is not None or
            record.balanceCnt is not None
        )

        # 使用验证器准备和验证回调数据（抑制重复日志）
        validator = CallbackDataValidator()
        callback_data = validator.prepare_and_validate_callback_data(
            record=record,
            trace_id=trace_id,
            retry_count=retry_count,
            ext_data=ext_data,
            balance_fetch_success=balance_fetch_success,
            suppress_logging=True  # 抑制验证器内部的日志，由回调服务统一处理
        )

        # 获取验证结果摘要并统一记录日志
        validation_summary = validator.get_validation_summary()

        # 统一的日志记录，避免重复
        if validation_summary["has_errors"]:
            logger.error(
                f"[CALLBACK_VALIDATION_ERROR] 回调数据验证失败 | "
                f"record_id={record.id} | errors={validation_summary['error_count']} | "
                f"details={validation_summary['errors']}"
            )

        if validation_summary["has_warnings"]:
            logger.warning(
                f"[CALLBACK_VALIDATION_WARNING] 回调数据验证警告 | "
                f"record_id={record.id} | warnings={validation_summary['warning_count']} | "
                f"details={validation_summary['warnings']}"
            )

        # 添加额外的兼容性字段
        callback_data.update({
            "record_id": str(record.id),
            "merchant_order_id": record.merchant_order_id,
            "card_number": callback_data["cardNumber"],  # 使用已脱敏的卡号
            "message": record.error_message if record.status != "success" else "绑卡成功",
            "created_at": callback_data["requestTime"],
            "updated_at": callback_data["processTime"],
            "trace_id": trace_id,
            "retry_count": retry_count
        })

        # 处理扩展数据
        if ext_data:
            try:
                import json
                ext_dict = json.loads(ext_data)
                # 只添加不冲突的字段
                for key, value in ext_dict.items():
                    if key not in callback_data:
                        callback_data[key] = value
            except (json.JSONDecodeError, TypeError):
                logger.warning(f"扩展数据格式错误: {ext_data}")

        return callback_data
    
    async def _send_callback_request(
        self, db: Session, record, merchant, callback_data: dict,
        record_id: uuid.UUID, retry_count: int
    ) -> bool:
        """发送回调请求 - 优化版本，防止阻塞"""
        try:
            async with self.get_http_client() as client:
                # 【优化】使用 asyncio.wait_for 添加额外的超时保护
                response = await asyncio.wait_for(
                    client.post(
                        merchant.callback_url,
                        json=callback_data,
                        headers={"Content-Type": "application/json"},
                    ),
                    timeout=15.0  # 15秒超时，比HTTP客户端的默认超时稍长
                )

                if 200 <= response.status_code < 300:
                    await self._handle_callback_success(
                        db, record, response, callback_data, record_id, retry_count
                    )
                    return True
                else:
                    await self._handle_callback_failure(
                        db, record, response, callback_data, record_id, retry_count
                    )
                    return False
        except asyncio.TimeoutError:
            error_msg = "回调请求超时（15秒）"
            logger.warning(f"[CALLBACK_TIMEOUT] {error_msg} | record_id={record_id} | url={merchant.callback_url}")
            await self._handle_callback_exception(
                db, record, Exception(error_msg), record_id, retry_count
            )
            return False
        except Exception as e:
            await self._handle_callback_exception(
                db, record, e, record_id, retry_count
            )
            return False
    
    async def _handle_callback_success(
        self, db: Session, record, response, callback_data: dict,
        record_id: uuid.UUID, retry_count: int
    ):
        """处理回调成功 - 优化版本，防止数据库阻塞"""
        try:
            record.callback_status = CallbackStatus.SUCCESS
            record.callback_result = f"HTTP {response.status_code}: {response.text[:200]}"
            record.retry_count = retry_count
            record.callback_time = local_now()
            db.add(record)

            # 【优化】使用 asyncio.to_thread 将同步数据库操作转为异步
            await asyncio.to_thread(db.commit)

            logger.info(f"回调成功: 记录ID {record_id}, 重试次数 {retry_count}")
        except Exception as e:
            logger.error(f"更新回调成功状态失败: {e}")
            # 回滚事务
            try:
                await asyncio.to_thread(db.rollback)
            except Exception as rollback_error:
                logger.error(f"回滚事务失败: {rollback_error}")
    
    async def _handle_callback_failure(
        self, db: Session, record, response, callback_data: dict,
        record_id: uuid.UUID, retry_count: int
    ):
        """处理回调失败 - 优化版本，防止数据库阻塞"""
        try:
            error_msg = f"HTTP {response.status_code} 错误"
            record.callback_status = CallbackStatus.FAILED
            record.callback_result = f"{error_msg}: {response.text[:200]}"
            record.retry_count = retry_count
            record.callback_time = local_now()
            db.add(record)

            # 【优化】使用 asyncio.to_thread 将同步数据库操作转为异步
            await asyncio.to_thread(db.commit)

            logger.warning(f"回调失败 ({error_msg}): 记录ID {record_id}, 重试次数 {retry_count}")
        except Exception as e:
            logger.error(f"更新回调失败状态失败: {e}")
            # 回滚事务
            try:
                await asyncio.to_thread(db.rollback)
            except Exception as rollback_error:
                logger.error(f"回滚事务失败: {rollback_error}")
    
    async def _handle_callback_exception(
        self, db: Session, record, exception: Exception, record_id: uuid.UUID, retry_count: int
    ):
        """处理回调异常 - 优化版本，防止数据库阻塞"""
        try:
            error_msg = f"回调异常: {str(exception)}"
            record.callback_status = CallbackStatus.FAILED
            record.callback_result = error_msg
            record.retry_count = retry_count
            record.callback_time = local_now()
            db.add(record)

            # 【优化】使用 asyncio.to_thread 将同步数据库操作转为异步
            await asyncio.to_thread(db.commit)

            logger.error(f"回调异常: 记录ID {record_id}, 异常: {exception}")
        except Exception as e:
            logger.error(f"更新回调异常状态失败: {e}")
            # 回滚事务
            try:
                await asyncio.to_thread(db.rollback)
            except Exception as rollback_error:
                logger.error(f"回滚事务失败: {rollback_error}")
    
    async def _schedule_retry(
        self, record_id_str: str, merchant_id: int, retry_count: int,
        ext_data: Optional[str], trace_id: Optional[str]
    ):
        """安排重试"""
        next_retry = retry_count + 1
        delay_seconds = min(2 ** next_retry * MIN_RETRY_WAIT, MAX_RETRY_WAIT)

        logger.info(
            f"安排回调重试 #{next_retry}，延迟 {delay_seconds} 秒: 记录ID {record_id_str}"
        )

        await send_callback_task(
            {
                "record_id": record_id_str,
                "merchant_id": merchant_id,
                "retry_count": next_retry,
                "ext_data": ext_data,
                "trace_id": trace_id,
            },
            delay_seconds=delay_seconds,
        )


# 创建全局实例
optimized_callback_service = OptimizedCallbackService()
