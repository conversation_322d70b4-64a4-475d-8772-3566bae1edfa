from typing import Optional
from fastapi import APIRouter, Depends, Query, HTTPException, status
from sqlalchemy.orm import Session, joinedload
from sqlalchemy import and_

from app.api import deps
from app.models.notification import Notification, NotificationType, NotificationStatus
from app.models.user import User
from app.core.errors import BusinessException, ErrorCode
from app.schemas.notification import NotificationCreate, NotificationUpdate
from app.services.permission_service import PermissionService

router = APIRouter()


def _build_notification_query_with_data_isolation(db: Session, current_user: User):
    """
    构建带有数据隔离的通知查询

    数据隔离规则：
    - 超级管理员：可以查看所有通知数据
    - 商户管理员：只能查看自己商户的通知数据
    - 商户CK供应商：只能查看自己部门的通知数据
    """
    if current_user.is_superuser:
        # 超级管理员可以查看所有通知
        return db.query(Notification)

    # 获取用户角色信息
    user_roles = [role.code for role in current_user.roles] if current_user.roles else []

    if 'merchant_admin' in user_roles:
        # 商户管理员：查看自己商户下所有用户的通知
        if current_user.merchant_id:
            # 查询同商户下所有用户的通知
            from app.models.user import User as UserModel
            merchant_user_ids = db.query(UserModel.id).filter(
                UserModel.merchant_id == current_user.merchant_id
            ).subquery()

            return db.query(Notification).filter(
                Notification.user_id.in_(merchant_user_ids)
            )
        else:
            # 如果没有商户ID，只能查看自己的通知
            return db.query(Notification).filter(Notification.user_id == current_user.id)

    elif 'ck_supplier' in user_roles:
        # CK供应商：查看自己部门下所有用户的通知
        if current_user.department_id:
            # 查询同部门下所有用户的通知
            from app.models.user import User as UserModel
            department_user_ids = db.query(UserModel.id).filter(
                UserModel.department_id == current_user.department_id
            ).subquery()

            return db.query(Notification).filter(
                Notification.user_id.in_(department_user_ids)
            )
        else:
            # 如果没有部门ID，只能查看自己的通知
            return db.query(Notification).filter(Notification.user_id == current_user.id)

    else:
        # 其他角色或无角色：只能查看自己的通知
        return db.query(Notification).filter(Notification.user_id == current_user.id)


@router.post("")
async def create_notification(
    notification_in: NotificationCreate,
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_active_user),
):
    """
    创建通知 - 需要通知创建权限

    权限要求:
    - 超级管理员：可以为任何用户创建通知
    - 其他角色：需要有 "api:notifications:create" 权限
    """
    try:
        # 权限检查
        permission_service = PermissionService(db)
        if not current_user.is_superuser:
            has_permission = permission_service.check_user_permission(
                current_user, "api:notifications:create"
            )
            if not has_permission:
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail="没有创建通知的权限",
                )

        # 验证目标用户是否存在
        target_user = db.query(User).filter(User.id == notification_in.user_id).first()
        if not target_user:
            raise BusinessException(message="目标用户不存在", code=ErrorCode.NOT_FOUND)

        # 数据隔离检查：非超级管理员只能为有权限访问的用户创建通知
        if not current_user.is_superuser:
            # 构建数据隔离查询来检查是否有权限为该用户创建通知
            user_query = _build_notification_query_with_data_isolation(db, current_user)
            # 检查是否能查看该用户的通知（如果能查看，就能创建）
            accessible_user_ids = [n.user_id for n in user_query.all()]
            if notification_in.user_id not in accessible_user_ids and notification_in.user_id != current_user.id:
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail="没有权限为该用户创建通知",
                )

        # 创建通知
        notification_data = notification_in.model_dump()
        notification = Notification(**notification_data)
        db.add(notification)
        db.commit()
        db.refresh(notification)

        return notification.to_dict()

    except HTTPException:
        raise
    except BusinessException as e:
        raise e
    except Exception as e:
        raise BusinessException(
            message=f"创建通知失败: {str(e)}", code=ErrorCode.SYSTEM_ERROR
        )


@router.get("/unread-count")
async def get_unread_count(
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_active_user),
):
    """获取未读通知数量 - 支持数据隔离"""
    try:
        # 使用数据隔离查询确保用户只能查看有权限的通知数量
        query = _build_notification_query_with_data_isolation(db, current_user)
        unread_count = query.filter(
            Notification.status == NotificationStatus.UNREAD
        ).count()

        # 直接返回数据，让中间件处理统一格式
        return {"unread_count": unread_count}

    except Exception as e:
        raise BusinessException(
            message=f"获取未读通知数量失败: {str(e)}", code=ErrorCode.SYSTEM_ERROR
        )


# 将具体路径的路由放在参数化路径之前，避免路由冲突

@router.put("/read-all")
async def mark_all_as_read(
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_active_user),
    type: Optional[str] = None,
):
    """标记所有通知为已读 - 支持数据隔离"""
    try:
        # 使用数据隔离查询确保用户只能操作有权限的通知
        query = _build_notification_query_with_data_isolation(db, current_user)
        query = query.filter(Notification.status == NotificationStatus.UNREAD)

        if type:
            # 尝试将字符串转换为枚举值
            try:
                notification_type = NotificationType(type)
                query = query.filter(Notification.type == notification_type)
            except ValueError:
                raise BusinessException(
                    message=f"无效的通知类型: {type}，可选值: {', '.join([t.value for t in NotificationType])}",
                    code=ErrorCode.INVALID_PARAMS,
                )

        query.update({"status": NotificationStatus.READ})
        db.commit()

        # 直接返回数据，让中间件处理统一格式
        return {"message": "已全部标记为已读"}

    except BusinessException as e:
        raise e
    except Exception as e:
        raise BusinessException(
            message=f"标记所有通知已读失败: {str(e)}", code=ErrorCode.SYSTEM_ERROR
        )


@router.get("/{notification_id}")
async def get_notification_detail(
    notification_id: int,
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_active_user),
):
    """获取通知详情 - 支持数据隔离"""
    try:
        # 使用数据隔离查询确保用户只能查看有权限的通知
        query = _build_notification_query_with_data_isolation(db, current_user)
        notification = query.filter(Notification.id == notification_id).first()

        if not notification:
            raise BusinessException(message="通知不存在", code=ErrorCode.NOT_FOUND)

        return notification.to_dict()

    except BusinessException as e:
        raise e
    except Exception as e:
        raise BusinessException(
            message=f"获取通知详情失败: {str(e)}", code=ErrorCode.SYSTEM_ERROR
        )


@router.put("/{notification_id}")
async def update_notification(
    notification_id: int,
    notification_in: NotificationUpdate,
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_active_user),
):
    """
    更新通知 - 需要通知更新权限

    权限要求:
    - 超级管理员：可以更新任何通知
    - 其他角色：需要有 "api:notifications:update" 权限
    """
    try:
        # 权限检查
        permission_service = PermissionService(db)
        if not current_user.is_superuser:
            has_permission = permission_service.check_user_permission(
                current_user, "api:notifications:update"
            )
            if not has_permission:
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail="没有更新通知的权限",
                )

        # 使用数据隔离查询确保用户只能更新有权限的通知
        query = _build_notification_query_with_data_isolation(db, current_user)
        notification = query.filter(Notification.id == notification_id).first()

        if not notification:
            raise BusinessException(message="通知不存在", code=ErrorCode.NOT_FOUND)

        # 更新通知
        update_data = notification_in.model_dump(exclude_unset=True)
        for field, value in update_data.items():
            setattr(notification, field, value)

        db.commit()
        db.refresh(notification)

        return notification.to_dict()

    except HTTPException:
        raise
    except BusinessException as e:
        raise e
    except Exception as e:
        raise BusinessException(
            message=f"更新通知失败: {str(e)}", code=ErrorCode.SYSTEM_ERROR
        )


@router.get("")
async def get_notifications(
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_active_user),
    page: int = Query(1, gt=0),
    page_size: int = Query(10, gt=0),
    type: Optional[str] = None,
    status: Optional[str] = None,
    is_important: Optional[bool] = None,
):
    """
    获取通知列表 - 支持所有角色访问，严格数据隔离

    数据隔离规则：
    - 超级管理员：可以查看所有通知数据
    - 商户管理员：只能查看自己商户的通知数据
    - 商户CK供应商：只能查看自己部门的通知数据
    """
    try:
        # 数据隔离：根据用户角色构建查询条件
        query = _build_notification_query_with_data_isolation(db, current_user)

        # 应用过滤条件
        if type:
            # 尝试将字符串转换为枚举值
            try:
                notification_type = NotificationType(type)
                query = query.filter(Notification.type == notification_type)
            except ValueError:
                raise BusinessException(
                    message=f"无效的通知类型: {type}，可选值: {', '.join([t.value for t in NotificationType])}",
                    code=ErrorCode.INVALID_PARAMS,
                )

        if status:
            # 尝试将字符串转换为枚举值
            try:
                notification_status = NotificationStatus(status)
                query = query.filter(Notification.status == notification_status)
            except ValueError:
                raise BusinessException(
                    message=f"无效的通知状态: {status}，可选值: {', '.join([s.value for s in NotificationStatus])}",
                    code=ErrorCode.INVALID_PARAMS,
                )

        if is_important is not None:
            query = query.filter(Notification.is_important == is_important)

        # 计算总数
        total = query.count()

        # 分页
        notifications = (
            query.order_by(Notification.created_at.desc())
            .offset((page - 1) * page_size)
            .limit(page_size)
            .all()
        )

        # 获取未读数量（应用相同的数据隔离规则）
        unread_query = _build_notification_query_with_data_isolation(db, current_user)
        unread_count = unread_query.filter(
            Notification.status == NotificationStatus.UNREAD
        ).count()

        # 直接返回数据，让中间件处理统一格式
        return {
            "items": [n.to_dict() for n in notifications],
            "total": total,
            "page": page,
            "page_size": page_size,
            "unread_count": unread_count,
        }

    except BusinessException as e:
        raise e
    except Exception as e:
        raise BusinessException(
            message=f"获取通知列表失败: {str(e)}", code=ErrorCode.SYSTEM_ERROR
        )





@router.post("/{notification_id}/read")
async def mark_as_read(
    notification_id: int,
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_active_user),
):
    """标记通知为已读 - 支持数据隔离"""
    try:
        # 使用数据隔离查询确保用户只能操作有权限的通知
        query = _build_notification_query_with_data_isolation(db, current_user)
        notification = query.filter(Notification.id == notification_id).first()

        if not notification:
            raise BusinessException(message="通知不存在", code=ErrorCode.NOT_FOUND)

        notification.status = NotificationStatus.READ
        db.commit()

        # 直接返回数据，让中间件处理统一格式
        return notification.to_dict()

    except BusinessException as e:
        raise e
    except Exception as e:
        raise BusinessException(
            message=f"标记通知已读失败: {str(e)}", code=ErrorCode.SYSTEM_ERROR
        )


@router.post("/{notification_id}/unread")
async def mark_as_unread(
    notification_id: int,
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_active_user),
):
    """标记通知为未读 - 支持数据隔离"""
    try:
        # 使用数据隔离查询确保用户只能操作有权限的通知
        query = _build_notification_query_with_data_isolation(db, current_user)
        notification = query.filter(Notification.id == notification_id).first()

        if not notification:
            raise BusinessException(message="通知不存在", code=ErrorCode.NOT_FOUND)

        notification.status = NotificationStatus.UNREAD
        notification.read_at = None  # 清除阅读时间
        db.commit()

        # 直接返回数据，让中间件处理统一格式
        return notification.to_dict()

    except BusinessException as e:
        raise e
    except Exception as e:
        raise BusinessException(
            message=f"标记通知未读失败: {str(e)}", code=ErrorCode.SYSTEM_ERROR
        )


@router.delete("/{notification_id}")
async def delete_notification(
    notification_id: int,
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_active_user),
):
    """删除通知 - 支持数据隔离"""
    try:
        # 使用数据隔离查询确保用户只能操作有权限的通知
        query = _build_notification_query_with_data_isolation(db, current_user)
        notification = query.filter(Notification.id == notification_id).first()

        if not notification:
            raise BusinessException(message="通知不存在", code=ErrorCode.NOT_FOUND)

        db.delete(notification)
        db.commit()

        # 直接返回数据，让中间件处理统一格式
        return {"message": "删除成功"}

    except BusinessException as e:
        raise e
    except Exception as e:
        raise BusinessException(
            message=f"删除通知失败: {str(e)}", code=ErrorCode.SYSTEM_ERROR
        )



