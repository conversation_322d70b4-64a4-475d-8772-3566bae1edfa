<script setup>
import { ref, onMounted, computed } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage, ElMessageBox, ElTabs, ElTabPane } from 'element-plus'
import StatisticsChart from '@/components/business/merchant/StatisticsChart.vue'
import IpWhitelistManager from '@/components/business/merchant/IpWhitelistManager.vue'
import ApiKeyManager from '@/components/business/merchant/ApiKeyManager.vue'
import { merchantApi } from '@/api/modules/merchant'
import { ArrowLeft } from '@element-plus/icons-vue'
import { formatDateTime } from '@/utils/dateUtils'
const route = useRoute()
const router = useRouter()
const merchantId = computed(() => {
    if (!route.params.id || route.params.id === 'undefined') {
        return null
    }
    return route.params.id
})
const loading = ref(false)
const merchant = ref(null)
const statistics = ref({
    total_records: 0,
    success_records: 0,
    failed_records: 0,
    pending_records: 0,
    today_records: 0,
    yesterday_records: 0
})

// 用户列表
const users = ref([])
const usersLoading = ref(false)

// 修改表单相关
const isEditing = ref(false)
const formData = ref({})
const formRef = ref(null)

// 获取商家详情
const fetchMerchantDetail = async () => {
    if (!merchantId.value) {
        ElMessage.error('无效的商家ID')
        router.push('/merchants')
        return
    }

    loading.value = true
    try {
        const response = await merchantApi.getDetail(merchantId.value)
        merchant.value = response
        formData.value = { ...response }
    } catch (error) {
        console.error('获取商家详情失败:', error)
        ElMessage.error('获取商家详情失败')
        router.push('/merchants')
    } finally {
        loading.value = false
    }
}

// 获取统计数据
const fetchStatistics = async () => {
    if (!merchantId.value) {
        return
    }

    try {
        const response = await merchantApi.getStatistics(merchantId.value)
        statistics.value = response.data || response
    } catch (error) {
        console.error('获取统计数据失败:', error)
        ElMessage.error('获取统计数据失败')
        // 提供正确的默认值结构
        statistics.value = {
            total_records: 0,
            success_records: 0,
            failed_records: 0,
            pending_records: 0,
            today_records: 0,
            yesterday_records: 0
        }
    }
}

// 获取商家用户列表
const fetchMerchantUsers = async () => {
    if (!merchantId.value) {
        return
    }

    usersLoading.value = true
    try {
        const response = await merchantApi.getUsers(merchantId.value, { page: 1, page_size: 100 }) // 示例：获取前100个用户
        users.value = response.items || response
    } catch (error) {
        console.error('获取商家用户失败:', error)
        ElMessage.error('获取商家用户失败')
        users.value = []
    } finally {
        usersLoading.value = false
    }
}

// 返回列表页
const goBack = () => {
    router.push('/merchant/list')
}

// 启用编辑模式
const enableEditing = () => {
    formData.value = { ...merchant.value }
    isEditing.value = true
}

// 取消编辑
const cancelEditing = () => {
    isEditing.value = false
}

// 保存商家信息
const saveMerchant = async () => {
    loading.value = true
    try {
        const response = await merchantApi.update(merchantId.value, formData.value)
        ElMessage.success('更新成功')
        merchant.value = response
        isEditing.value = false
    } catch (error) {
        console.error('更新失败:', error)
        ElMessage.error('更新失败: ' + (error.response?.data?.detail || error.message))
    } finally {
        loading.value = false
    }
}



// 重置API密钥
const resetApiKey = () => {
    ElMessageBox.confirm('重置API密钥后，原有密钥将无法使用，是否继续？', '警告', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
    }).then(async () => {
        loading.value = true
        try {
            const response = await merchantApi.resetApiKey(merchantId.value)
            ElMessage.success('重置成功')
            merchant.value = {
                ...merchant.value,
                api_key: response.api_key
            }
            // 显示新的API密钥和密文
            newApiCredentials.value = {
                api_key: response.api_key,
                api_secret: response.api_secret
            }
            showApiCredentials.value = true
        } catch (error) {
            console.error('重置API密钥失败:', error)
            ElMessage.error('重置API密钥失败')
        } finally {
            loading.value = false
        }
    }).catch(() => {
        // 取消操作
    })
}



// 启用/禁用商家
const toggleMerchantStatus = () => {
    const action = merchant.value.status ? '禁用' : '启用'
    ElMessageBox.confirm(`确定要${action}该商家吗？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
    }).then(async () => {
        loading.value = true
        try {
            await merchantApi.toggleStatus(merchantId.value, !merchant.value.status)
            ElMessage.success(`${action}成功`)
            merchant.value.status = !merchant.value.status
        } catch (error) {
            console.error(`${action}失败:`, error)
            ElMessage.error(`${action}失败`)
        } finally {
            loading.value = false
        }
    }).catch(() => {
        // 取消操作
    })
}

// 刷新统计数据
const refreshStatistics = () => {
    fetchStatistics()
    ElMessage.success('统计数据已刷新')
}

// 添加用户
const addUser = () => {
    ElMessageBox.confirm('暂未实现，请前往用户管理页面添加商家用户', '提示', {
        confirmButtonText: '前往用户管理',
        cancelButtonText: '取消',
        type: 'info'
    }).then(() => {
        router.push('/system/user')
    }).catch(() => {
        // 取消操作
    })
}

// 导出商家数据
const exportMerchantData = () => {
    ElMessage.info('导出功能开发中')
}

// 处理API密钥重置
const handleApiKeyReset = () => {
    // 刷新商家信息
    fetchMerchantDetail()
}

// 处理更新IP白名单
const handleUpdateWhitelist = () => {
    // 实现处理逻辑
}

// 保存回调设置
const saveCallbackSettings = () => {
    // 实现保存回调设置的逻辑
}

onMounted(() => {
    if (!merchantId.value) {
        ElMessage.error('无效的商家ID')
        router.push('/merchants')
        return
    }

    fetchMerchantDetail()
    fetchStatistics()
    fetchMerchantUsers()
})
</script>

<template>
    <div class="merchant-detail-container" v-loading="loading">
        <el-card class="merchant-card">
            <template #header>
                <div class="card-header">
                    <div class="title-area">
                        <el-button @click="goBack" :icon="ArrowLeft" circle></el-button>
                        <span class="title">{{ merchant?.name || '商家详情' }}</span>
                        <el-tag :type="merchant?.status ? 'success' : 'danger'" class="status-tag">
                            {{ merchant?.status ? '启用' : '禁用' }}
                        </el-tag>
                    </div>
                </div>
            </template>

            <el-tabs>
                <!-- 基础信息 -->
                <el-tab-pane label="基础信息">
                    <el-descriptions :column="2" border>
                        <el-descriptions-item label="商家ID">{{ merchant?.id }}</el-descriptions-item>
                        <el-descriptions-item label="创建时间">
                            {{ merchant?.created_at ? formatDateTime(merchant.created_at) : '-' }}
                        </el-descriptions-item>
                        <el-descriptions-item label="商家名称">
                            <template v-if="isEditing">
                                <el-input v-model="formData.name" placeholder="请输入商家名称" />
                            </template>
                            <template v-else>{{ merchant?.name }}</template>
                        </el-descriptions-item>
                        <el-descriptions-item label="商家编码">
                            <template v-if="isEditing">
                                <el-input v-model="formData.code" placeholder="请输入商家编码" />
                            </template>
                            <template v-else>{{ merchant?.code }}</template>
                        </el-descriptions-item>
                        <el-descriptions-item label="联系人">
                            <template v-if="isEditing">
                                <el-input v-model="formData.contact_name" placeholder="请输入联系人" />
                            </template>
                            <template v-else>{{ merchant?.contact_name || '-' }}</template>
                        </el-descriptions-item>
                        <el-descriptions-item label="联系电话">
                            <template v-if="isEditing">
                                <el-input v-model="formData.contact_phone" placeholder="请输入联系电话" />
                            </template>
                            <template v-else>{{ merchant?.contact_phone || '-' }}</template>
                        </el-descriptions-item>
                        <el-descriptions-item label="联系邮箱">
                            <template v-if="isEditing">
                                <el-input v-model="formData.contact_email" placeholder="请输入联系邮箱" />
                            </template>
                            <template v-else>{{ merchant?.contact_email || '-' }}</template>
                        </el-descriptions-item>
                        <el-descriptions-item label="备注">
                            <template v-if="isEditing">
                                <el-input v-model="formData.remark" type="textarea" :rows="2" placeholder="请输入备注" />
                            </template>
                            <template v-else>{{ merchant?.remark || '-' }}</template>
                        </el-descriptions-item>
                    </el-descriptions>

                    <el-divider content-position="left">限制配置</el-divider>
                    <el-descriptions :column="3" border>
                        <el-descriptions-item label="每日绑卡上限">
                            <template v-if="isEditing">
                                <el-input-number v-model="formData.daily_limit" :min="0" :step="100" />
                            </template>
                            <template v-else>{{ merchant?.daily_limit }}</template>
                        </el-descriptions-item>
                        <el-descriptions-item label="每小时绑卡上限">
                            <template v-if="isEditing">
                                <el-input-number v-model="formData.hourly_limit" :min="0" :step="10" />
                            </template>
                            <template v-else>{{ merchant?.hourly_limit }}</template>
                        </el-descriptions-item>
                        <el-descriptions-item label="最大并发数">
                            <template v-if="isEditing">
                                <el-input-number v-model="formData.concurrency_limit" :min="1" :step="10" />
                            </template>
                            <template v-else>{{ merchant?.concurrency_limit }}</template>
                        </el-descriptions-item>
                        <el-descriptions-item label="处理优先级">
                            <template v-if="isEditing">
                                <el-input-number v-model="formData.priority" :min="0" :max="100" />
                            </template>
                            <template v-else>{{ merchant?.priority }}</template>
                        </el-descriptions-item>
                        <el-descriptions-item label="请求超时时间(秒)">
                            <template v-if="isEditing">
                                <el-input-number v-model="formData.request_timeout" :min="1" :max="180" />
                            </template>
                            <template v-else>{{ merchant?.request_timeout }}</template>
                        </el-descriptions-item>
                        <el-descriptions-item label="重试次数">
                            <template v-if="isEditing">
                                <el-input-number v-model="formData.retry_count" :min="0" :max="10" />
                            </template>
                            <template v-else>{{ merchant?.retry_count }}</template>
                        </el-descriptions-item>
                    </el-descriptions>
                </el-tab-pane>

                <!-- API管理 -->
                <el-tab-pane label="API管理">
                    <ApiKeyManager :merchant="merchant" :merchant-id="merchantId" @api-key-reset="handleApiKeyReset" />

                    <el-divider content-position="left">回调配置</el-divider>
                    <el-form label-width="120px" :model="formData" style="max-width: 600px;">
                        <el-form-item label="回调URL">
                            <el-input v-model="formData.callback_url" placeholder="请输入回调URL" clearable />
                        </el-form-item>
                    </el-form>

                    <IpWhitelistManager :merchant-id="merchantId" @update-whitelist="handleUpdateWhitelist" />
                </el-tab-pane>

                <!-- 统计数据 -->
                <el-tab-pane label="统计数据">
                    <div class="statistics-header">
                        <h3>绑卡数据统计</h3>
                        <el-button type="primary" size="small" @click="refreshStatistics" icon="Refresh">
                            刷新数据
                        </el-button>
                    </div>

                    <StatisticsChart :statistics="statistics" />
                </el-tab-pane>

                <!-- 用户管理 -->
                <el-tab-pane label="用户管理">
                    <div class="user-header">
                        <h3>商家用户列表</h3>
                        <el-button type="primary" size="small" @click="addUser" icon="Plus">
                            添加用户
                        </el-button>
                    </div>

                    <el-table :data="users" v-loading="usersLoading" border style="width: 100%">
                        <el-table-column label="用户ID" prop="id" width="80" />
                        <el-table-column label="用户名" prop="username" />
                        <el-table-column label="邮箱" prop="email" />
                        <el-table-column label="姓名" prop="full_name" />
                        <el-table-column label="状态" width="100">
                            <template #default="scope">
                                <el-tag :type="scope.row.is_active ? 'success' : 'danger'">
                                    {{ scope.row.is_active ? '启用' : '禁用' }}
                                </el-tag>
                            </template>
                        </el-table-column>
                        <el-table-column label="操作" width="150">
                            <template #default="scope">
                                <el-button size="small" type="primary" link>编辑</el-button>
                                <el-button size="small" type="danger" link>
                                    {{ scope.row.is_active ? '禁用' : '启用' }}
                                </el-button>
                            </template>
                        </el-table-column>
                    </el-table>
                </el-tab-pane>
            </el-tabs>
        </el-card>


    </div>
</template>

<style scoped>
.merchant-detail-container {
    padding: 20px;
}

.merchant-card {
    margin-bottom: 20px;
}

.card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.title-area {
    display: flex;
    align-items: center;
    gap: 10px;
}

.title {
    font-size: 18px;
    font-weight: bold;
}

.status-tag {
    margin-left: 10px;
}

.operations {
    display: flex;
    gap: 10px;
}

.statistics-header,
.user-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.el-descriptions {
    margin-bottom: 30px;
}


</style>