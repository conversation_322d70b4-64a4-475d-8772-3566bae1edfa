#!/usr/bin/env python3
"""
测试处理时间计算修复
验证绑卡记录的处理耗时是否正确计算
"""

import sys
import os
from pathlib import Path
from datetime import datetime, timedelta
import asyncio

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from app.db.session import SessionLocal
from app.models.card_record import CardRecord
from app.utils.time_utils import get_current_time, ensure_timezone
from app.core.logging import get_logger

logger = get_logger("test_process_time")

def test_process_time_calculation():
    """测试处理时间计算"""
    print("🔍 测试处理时间计算修复")
    print("=" * 60)
    
    db = SessionLocal()
    try:
        # 查询最近的绑卡记录
        recent_records = db.query(CardRecord).filter(
            CardRecord.status.in_(['success', 'failed'])
        ).order_by(CardRecord.created_at.desc()).limit(10).all()
        
        if not recent_records:
            print("❌ 没有找到已完成的绑卡记录")
            return
        
        print(f"📊 找到 {len(recent_records)} 条已完成的绑卡记录")
        print()
        
        for i, record in enumerate(recent_records, 1):
            print(f"📝 记录 {i}: {record.id}")
            print(f"   状态: {record.status}")
            print(f"   创建时间: {record.created_at}")
            print(f"   更新时间: {record.updated_at}")
            
            # 当前存储的处理时间
            current_process_time = record.process_time or 0
            print(f"   当前存储的处理时间: {current_process_time:.2f} 秒")
            
            # 计算正确的总处理时间
            if record.created_at and record.updated_at:
                created_at_tz = ensure_timezone(record.created_at)
                updated_at_tz = ensure_timezone(record.updated_at)
                
                correct_process_time = (updated_at_tz - created_at_tz).total_seconds()
                print(f"   正确的总处理时间: {correct_process_time:.2f} 秒")
                
                # 计算差异
                time_diff = abs(correct_process_time - current_process_time)
                print(f"   时间差异: {time_diff:.2f} 秒")
                
                # 判断是否需要修复
                if time_diff > 1.0:  # 差异超过1秒
                    print(f"   ⚠️  需要修复：差异过大")
                else:
                    print(f"   ✅ 时间计算正确")
            else:
                print(f"   ❌ 缺少时间信息")
            
            print()
    
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        logger.exception("测试处理时间计算失败")
    
    finally:
        db.close()

def test_time_calculation_function():
    """测试时间计算函数"""
    print("🧮 测试时间计算函数")
    print("=" * 60)
    
    # 模拟一个绑卡记录
    class MockRecord:
        def __init__(self, created_at):
            self.id = "test-record"
            self.created_at = created_at
    
    # 创建一个5分钟前的记录
    five_minutes_ago = get_current_time() - timedelta(minutes=5)
    mock_record = MockRecord(five_minutes_ago)
    
    # 导入处理服务
    from app.services.binding_process_service import BindingProcessService
    
    service = BindingProcessService()
    calculated_time = service._calculate_total_process_time(mock_record)
    
    print(f"模拟记录创建时间: {five_minutes_ago}")
    print(f"当前时间: {get_current_time()}")
    print(f"计算的处理时间: {calculated_time:.2f} 秒")
    print(f"预期时间（约5分钟）: {5 * 60} 秒")
    
    # 验证计算是否正确（允许1秒误差）
    expected_time = 5 * 60  # 5分钟
    if abs(calculated_time - expected_time) < 1:
        print("✅ 时间计算函数正确")
    else:
        print("❌ 时间计算函数有误")

def analyze_process_time_distribution():
    """分析处理时间分布"""
    print("📈 分析处理时间分布")
    print("=" * 60)
    
    db = SessionLocal()
    try:
        # 查询最近100条记录
        records = db.query(CardRecord).filter(
            CardRecord.status.in_(['success', 'failed']),
            CardRecord.created_at.isnot(None),
            CardRecord.updated_at.isnot(None)
        ).order_by(CardRecord.created_at.desc()).limit(100).all()
        
        if not records:
            print("❌ 没有找到足够的记录")
            return
        
        print(f"📊 分析 {len(records)} 条记录")
        
        # 计算统计信息
        stored_times = []
        correct_times = []
        
        for record in records:
            stored_time = record.process_time or 0
            stored_times.append(stored_time)
            
            created_at_tz = ensure_timezone(record.created_at)
            updated_at_tz = ensure_timezone(record.updated_at)
            correct_time = (updated_at_tz - created_at_tz).total_seconds()
            correct_times.append(correct_time)
        
        # 统计信息
        print(f"\n📊 当前存储的处理时间统计:")
        print(f"   平均值: {sum(stored_times) / len(stored_times):.2f} 秒")
        print(f"   最小值: {min(stored_times):.2f} 秒")
        print(f"   最大值: {max(stored_times):.2f} 秒")
        
        print(f"\n📊 正确的总处理时间统计:")
        print(f"   平均值: {sum(correct_times) / len(correct_times):.2f} 秒")
        print(f"   最小值: {min(correct_times):.2f} 秒")
        print(f"   最大值: {max(correct_times):.2f} 秒")
        
        # 计算需要修复的记录数量
        need_fix_count = 0
        for stored, correct in zip(stored_times, correct_times):
            if abs(stored - correct) > 1.0:
                need_fix_count += 1
        
        print(f"\n🔧 需要修复的记录数量: {need_fix_count} / {len(records)} ({need_fix_count/len(records)*100:.1f}%)")
        
    except Exception as e:
        print(f"❌ 分析失败: {e}")
        logger.exception("分析处理时间分布失败")
    
    finally:
        db.close()

def main():
    """主函数"""
    print("🔍 处理时间计算修复测试")
    print("=" * 80)
    
    # 执行各项测试
    test_process_time_calculation()
    print()
    
    test_time_calculation_function()
    print()
    
    analyze_process_time_distribution()
    
    print("\n" + "=" * 80)
    print("✅ 测试完成")
    print("\n💡 修复说明:")
    print("1. 修复前：process_time 只包含API调用时间（0.几秒）")
    print("2. 修复后：process_time 包含从创建到完成的总时间")
    print("3. 这样页面显示的处理耗时就是真正的业务处理时间")

if __name__ == "__main__":
    main()
