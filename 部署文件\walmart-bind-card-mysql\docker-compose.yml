# 数据库服务 - 独立部署到数据库服务器
services:
  db:
    container_name: walmart-bind-card-db
    image: docker.1ms.run/mysql:8.0
    restart: always
    command: --character-set-server=utf8mb4 --collation-server=utf8mb4_unicode_ci --default-time-zone='+08:00' --init-connect='SET NAMES utf8mb4 COLLATE utf8mb4_unicode_ci' --skip-character-set-client-handshake
    environment:
      MYSQL_ROOT_PASSWORD: ${MYSQL_ROOT_PASSWORD:-7c222fb2927d828af22f592134e8932480637c0d}
      MYSQL_DATABASE: ${MYSQL_DATABASE:-walmart_card_db}
      MYSQL_USER: ${MYSQL_USER:-walmart_card}
      MYSQL_PASSWORD: ${MYSQL_PASSWORD:-7c222fb2927d828af22f592134e8932480637c0d}
      TZ: Asia/Shanghai
      # MySQL字符集环境变量
      MYSQL_CHARSET: utf8mb4
      MYSQL_COLLATION: utf8mb4_unicode_ci
      # 确保初始化时使用正确的字符集
      MYSQL_INITDB_SKIP_TZINFO: 1
    # 使用端口映射，确保外部可以访问
    ports:
      - "3306:3306"
    volumes:
      - mysql_data:/var/lib/mysql
      - mysql_logs:/var/log/mysql
      - ./mysql-init:/docker-entrypoint-initdb.d
      - ./mysql-conf:/etc/mysql/conf.d
    healthcheck:
      test:
        [
          "CMD",
          "mysqladmin",
          "ping",
          "-h",
          "localhost",
          "-u",
          "root",
          "-p7c222fb2927d828af22f592134e8932480637c0d",
        ]
      interval: 10s
      timeout: 5s
      retries: 5
    networks:
      - walmart-network

volumes:
  mysql_data:
  mysql_logs:

networks:
  walmart-network:
    driver: bridge
