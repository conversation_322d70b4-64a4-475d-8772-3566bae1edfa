import { defineStore } from 'pinia'
import { recoveryApi } from '@/api/modules/recovery'

export const useRecoveryStore = defineStore('recovery', {
    state: () => ({
        loading: false,
        error: null,
        lastResult: null,
    }),

    getters: {
        isLoading: (state) => state.loading,
        getLastResult: (state) => state.lastResult,
    },

    actions: {
        // 处理卡住的请求
        async processStuckRequests(params = {}) {
            this.loading = true
            try {
                const response = await recoveryApi.processStuckRequests(params)
                this.lastResult = response.data
                return response.data
            } catch (error) {
                this.error = error.message || '处理卡住请求失败'
                throw error
            } finally {
                this.loading = false
            }
        },

        // 重置状态
        resetState() {
            this.loading = false
            this.error = null
            this.lastResult = null
        }
    }
})
