<template>
  <div class="ck-details-container">
    <!-- 面包屑导航 -->
    <BreadcrumbNavigation :department-path="route.query.departmentPath" :filters="filters" :extra-items="[
      { name: organizationInfo.name, clickable: false },
      { name: '<PERSON><PERSON>明细统计', clickable: false }
    ]" :show-current-page="false" @navigate="handleNavigate" />

    <!-- 筛选条件 -->
    <el-card class="filter-card" shadow="never">
      <div class="filter-row">
        <TimeRangeSelector v-model="timeRangeData" @change="handleTimeRangeChange" />

        <div class="filter-actions">
          <el-button type="primary" @click="refreshData" :loading="loading">
            <el-icon>
              <Refresh />
            </el-icon>
            刷新
          </el-button>
          <el-button @click="exportData" :loading="exportLoading">
            <el-icon>
              <Download />
            </el-icon>
            导出
          </el-button>
        </div>
      </div>
    </el-card>



    <!-- CK详细统计表格 -->
    <el-card class="table-card" shadow="never">
      <template #header>
        <div class="card-header">
          <span class="card-title">
            <el-icon>
              <List />
            </el-icon>
            CK详细统计
          </span>
          <div class="header-actions">
            <el-tooltip content="表格说明" placement="top">
              <el-button text @click="showTableHelp">
                <el-icon>
                  <QuestionFilled />
                </el-icon>
              </el-button>
            </el-tooltip>
          </div>
        </div>
      </template>

      <el-table :data="ckList" v-loading="loading" stripe style="width: 100%"
        :default-sort="{ prop: 'successAmount', order: 'descending' }">
        <el-table-column prop="ckSign" label="CK标识" min-width="150">
          <template #default="{ row }">
            <div class="ck-sign">
              <el-icon class="ck-icon">
                <Key />
              </el-icon>
              <code>{{ row.ckSign }}</code>
            </div>
          </template>
        </el-table-column>

        <el-table-column prop="departmentName" label="部门名" min-width="120">
          <template #default="{ row }">
            <span class="department-name">{{ row.departmentName }}</span>
          </template>
        </el-table-column>

        <el-table-column prop="merchantName" label="商户名" min-width="200">
          <template #default="{ row }">
            <span class="merchant-name">{{ row.merchantName }}</span>
          </template>
        </el-table-column>

        <el-table-column prop="ckDescription" label="描述" min-width="120">
          <template #default="{ row }">
            <span>{{ row.ckDescription || '-' }}</span>
          </template>
        </el-table-column>

        <el-table-column prop="active" label="状态" width="80">
          <template #default="{ row }">
            <el-tag :type="row.active ? 'success' : 'danger'" size="small">
              {{ row.active ? '启用' : '禁用' }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column prop="successCount" label="成功笔数" width="120" sortable>
          <template #default="{ row }">
            <span class="number-value">{{ formatNumber(row.successCount) }}</span>
          </template>
        </el-table-column>

        <el-table-column prop="successAmount" label="成功金额" width="150" sortable>
          <template #default="{ row }">
            <span class="amount-value">{{ formatAmount(row.successAmount) }}</span>
          </template>
        </el-table-column>



        <el-table-column label="操作" width="120" fixed="right">
          <template #default="{ row }">
            <el-button type="primary" size="small" @click="viewRecords(row)" :disabled="row.successCount === 0">
              明细
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-wrapper" v-if="pagination.total > 0">
        <el-pagination v-model:current-page="pagination.currentPage" v-model:page-size="pagination.pageSize"
          :page-sizes="[10, 20, 50, 100]" :total="pagination.total" layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange" @current-change="handleCurrentChange" />
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  ArrowLeft,
  Key,
  Refresh,
  Download,
  List,
  QuestionFilled
} from '@element-plus/icons-vue'
import { useRouter, useRoute } from 'vue-router'
import TimeRangeSelector from '@/components/common/TimeRangeSelector.vue'
import BreadcrumbNavigation from '@/components/common/BreadcrumbNavigation.vue'
import { reconciliationApi } from '@/api'

// Router实例
const router = useRouter()
const route = useRoute()

// 响应式数据
const loading = ref(false)
const exportLoading = ref(false)
const customDateRange = ref([])

// 组织信息
const organizationInfo = reactive({
  id: route.params.organizationId,
  name: route.query.name || '未知组织',
  type: route.params.organizationType || 'department',
  merchantName: '测试商户A'
})

// 筛选条件
const filters = reactive({
  timeRange: route.query.timeRange || 'today',
  startDate: route.query.startDate || '',
  endDate: route.query.endDate || '',
  minSuccessCount: 1,
  ckStatus: 'all'
})

// 时间范围数据（用于TimeRangeSelector组件）
const timeRangeData = ref({
  timeRange: route.query.timeRange || 'today',
  startDate: route.query.startDate || '',
  endDate: route.query.endDate || ''
})

// 处理面包屑导航
const handleNavigate = (routeInfo) => {
  router.push(routeInfo)
}

// 返回到部门统计页面
const goBackToDepartment = () => {
  console.log('🔍 CK详情页面返回部门统计，当前路由参数:', route.query)

  // 构建返回对账台页面的路由参数
  const query = {
    timeRange: filters.timeRange,
    startDate: filters.startDate || '',
    endDate: filters.endDate || ''
  }

  // 传递必要的查询参数以保持页面状态
  if (route.query.departmentPath) {
    query.departmentPath = route.query.departmentPath
  }

  if (route.query.parentDepartmentId) {
    query.parentDepartmentId = route.query.parentDepartmentId
  }

  if (route.query.parentDepartmentName) {
    query.parentDepartmentName = route.query.parentDepartmentName
  }

  if (route.query.level) {
    query.level = route.query.level
  }

  if (route.query.merchantId) {
    query.merchantId = route.query.merchantId
  }

  if (route.query.viewLevel) {
    query.viewLevel = route.query.viewLevel
  }

  // 添加时间戳强制刷新
  query._t = Date.now()

  console.log('🎯 返回对账台，传递的完整参数:', query)

  router.push({
    name: 'Reconciliation',
    query: query
  })
}

// 分页
const pagination = reactive({
  currentPage: 1,
  pageSize: 20,
  total: 0
})

// CK数据
const ckList = ref([])

// 获取CK统计数据
const getCkStatistics = async () => {
  try {
    const params = {
      page: pagination.currentPage,
      page_size: pagination.pageSize,
      time_range: filters.timeRange,
      min_success_count: filters.minSuccessCount,
      ck_status: filters.ckStatus
    }

    // 只有在有有效日期时才添加日期参数
    if (filters.startDate && filters.startDate.trim() !== '') {
      params.start_date = filters.startDate
    }
    if (filters.endDate && filters.endDate.trim() !== '') {
      params.end_date = filters.endDate
    }

    const response = await reconciliationApi.getCkStatistics(organizationInfo.id, params)

    if (response && response.data) {
      ckList.value = response.data
      pagination.total = response.total
      pagination.currentPage = response.page
      pagination.pageSize = response.pageSize
    }
  } catch (error) {
    console.error('获取CK统计数据失败:', error)
    ElMessage.error('获取CK统计数据失败')
    ckList.value = []
  }
}



// 方法
const formatNumber = (num) => {
  return num ? num.toLocaleString() : '0'
}

const formatAmount = (amount) => {
  if (!amount) return '¥0'
  return `¥${(amount / 100).toLocaleString()}`
}





// 处理时间范围变化
const handleTimeRangeChange = (timeRange) => {
  // 同步到filters
  filters.timeRange = timeRange.timeRange
  filters.startDate = timeRange.startDate
  filters.endDate = timeRange.endDate

  // 刷新数据
  refreshData()
}

const handleCustomDateChange = () => {
  if (customDateRange.value && customDateRange.value.length === 2) {
    filters.startDate = customDateRange.value[0]
    filters.endDate = customDateRange.value[1]
    refreshData()
  }
}

const handleMinSuccessCountChange = () => {
  refreshData()
}

const handleCkStatusChange = () => {
  refreshData()
}

const refreshData = async () => {
  loading.value = true
  try {
    await getCkStatistics()
    ElMessage.success('数据刷新成功')
  } catch (error) {
    ElMessage.error('数据刷新失败')
  } finally {
    loading.value = false
  }
}

const exportData = async () => {
  exportLoading.value = true
  try {
    const params = {
      time_range: filters.timeRange,
      min_success_count: filters.minSuccessCount,
      ck_status: filters.ckStatus
    }

    // 只有在有有效日期时才添加日期参数
    if (filters.startDate && filters.startDate.trim() !== '') {
      params.start_date = filters.startDate
    }
    if (filters.endDate && filters.endDate.trim() !== '') {
      params.end_date = filters.endDate
    }

    await reconciliationApi.exportCkStatistics(organizationInfo.id, params)
    ElMessage.success('导出成功')
  } catch (error) {
    console.error('导出失败:', error)
    ElMessage.error('导出失败')
  } finally {
    exportLoading.value = false
  }
}

const viewRecords = (row) => {
  // 获取从对账台传递过来的部门路径
  const departmentPath = route.query.departmentPath || '[]'

  router.push({
    name: 'ReconciliationRecords',
    params: {
      ckId: row.ckId
    },
    query: {
      ckSign: row.ckSign,
      departmentName: row.departmentName,
      merchantName: row.merchantName,
      departmentPath: departmentPath, // 传递完整的部门路径
      timeRange: filters.timeRange,
      startDate: filters.startDate,
      endDate: filters.endDate,
      minSuccessCount: filters.minSuccessCount,
      ckStatus: filters.ckStatus,
      // 传递组织信息，用于返回时重建路由
      organizationId: organizationInfo.id,
      organizationType: organizationInfo.type
    }
  })
}

const showTableHelp = () => {
  ElMessageBox.alert(
    '• CK标识：经过脱敏处理的CK签名\n' +
    '• 成功笔数：该CK成功绑卡的记录数量\n' +
    '• 成功金额：该CK所有成功绑卡记录的实际金额总和\n' +
    '• 平均金额：成功金额除以成功笔数\n' +
    '• 最后成功时间：该CK最近一次成功绑卡的时间\n' +
    '• 只显示有成功记录的CK',
    'CK统计说明',
    {
      confirmButtonText: '知道了'
    }
  )
}

const handleSizeChange = (size) => {
  pagination.pageSize = size
  refreshData()
}

const handleCurrentChange = (page) => {
  pagination.currentPage = page
  refreshData()
}

// 初始化
onMounted(async () => {
  // 如果有自定义时间范围，设置日期选择器
  if (filters.timeRange === 'custom' && filters.startDate && filters.endDate) {
    customDateRange.value = [filters.startDate, filters.endDate]
  }
  await getCkStatistics()
})
</script>

<style scoped>
.ck-details-container {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: calc(100vh - 60px);
}



.breadcrumb .el-button {
  padding: 0;
  font-size: 14px;
  color: #409eff;
}

.breadcrumb .el-button:hover {
  color: #66b1ff;
}

/* 页面标题 */
.page-header {
  margin-bottom: 20px;
}

.header-content {
  background: linear-gradient(135deg, #409eff 0%, #66b1ff 100%);
  padding: 24px;
  border-radius: 12px;
  color: white;
}

.page-title {
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 8px;
}

.page-description {
  margin: 0;
  font-size: 14px;
  opacity: 0.9;
}

/* 筛选卡片 */
.filter-card {
  margin-bottom: 20px;
  border: none;
}

.filter-row {
  display: flex;
  align-items: center;
  gap: 20px;
  flex-wrap: wrap;
}

.filter-group {
  display: flex;
  align-items: center;
  gap: 8px;
}

.filter-label {
  font-size: 14px;
  color: #606266;
  white-space: nowrap;
}

.filter-actions {
  margin-left: auto;
  display: flex;
  gap: 12px;
}

/* 统计卡片 */
.stats-cards {
  margin-bottom: 20px;
}

.stat-card {
  border: none;
  transition: all 0.3s ease;
  cursor: pointer;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.stat-content {
  display: flex;
  align-items: center;
  gap: 16px;
}

.stat-icon {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
}

.success-count .stat-icon {
  background: linear-gradient(135deg, #67c23a, #85ce61);
  color: white;
}

.success-amount .stat-icon {
  background: linear-gradient(135deg, #409eff, #66b1ff);
  color: white;
}

.avg-amount .stat-icon {
  background: linear-gradient(135deg, #e6a23c, #ebb563);
  color: white;
}

.ck-count .stat-icon {
  background: linear-gradient(135deg, #909399, #b1b3b8);
  color: white;
}

.stat-info {
  flex: 1;
}

.stat-value {
  font-size: 24px;
  font-weight: 600;
  color: #303133;
  line-height: 1;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 14px;
  color: #909399;
}

/* 表格卡片 */
.table-card {
  border: none;
}

.card-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.card-title {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  display: flex;
  align-items: center;
  gap: 8px;
}

.header-actions {
  display: flex;
  gap: 8px;
}

/* 表格样式 */
.ck-sign {
  display: flex;
  align-items: center;
  gap: 8px;
}

.ck-icon {
  color: #409eff;
}

.ck-sign code {
  background-color: #f5f7fa;
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 12px;
  color: #606266;
  border: 1px solid #dcdfe6;
  font-weight: 500;
}

.department-name {
  color: #606266;
  font-weight: 500;
}

.merchant-name {
  color: #606266;
}

.number-value {
  font-weight: 600;
  color: #67c23a;
}

.amount-value {
  font-weight: 600;
  color: #409eff;
}

.time-value {
  font-size: 12px;
  color: #909399;
}

/* 分页 */
.pagination-wrapper {
  margin-top: 20px;
  display: flex;
  justify-content: center;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .ck-details-container {
    padding: 12px;
  }

  .filter-row {
    flex-direction: column;
    align-items: stretch;
    gap: 12px;
  }

  .filter-actions {
    margin-left: 0;
    justify-content: center;
  }

  .stat-content {
    gap: 12px;
  }

  .stat-icon {
    width: 40px;
    height: 40px;
    font-size: 20px;
  }

  .stat-value {
    font-size: 20px;
  }
}

/* 加载状态 */
.el-loading-mask {
  border-radius: 8px;
}

/* 表格行悬停效果 */
:deep(.el-table__row:hover) {
  background-color: #f5f7fa;
}

/* 按钮样式优化 */
.el-button--small {
  padding: 5px 12px;
  font-size: 12px;
}

/* 标签样式 */
.el-tag--small {
  font-size: 11px;
  padding: 0 6px;
  height: 20px;
  line-height: 18px;
}

/* 面包屑样式优化 */
:deep(.el-breadcrumb__item:last-child .el-breadcrumb__inner) {
  color: #303133;
  font-weight: 600;
}

/* 输入框样式 */
.el-input-number {
  width: 120px;
}
</style>
