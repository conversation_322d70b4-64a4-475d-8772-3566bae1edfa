#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
仪表盘测试
测试仪表盘统计数据和图表功能
"""

import sys
import os
import time
import json

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.dirname(__file__))))

from test.conftest import TestBase, get_test_accounts, format_test_result, print_test_summary

class DashboardTestSuite(TestBase):
    """仪表盘测试类"""
    
    def __init__(self):
        super().__init__()
        self.results = []
        self.test_accounts = get_test_accounts()
        self.admin_token = None
        self.merchant_token = None
        
    def setup_test_environment(self):
        """设置测试环境"""
        print("=== 设置仪表盘测试环境 ===")
        
        # 登录管理员账号
        self.admin_token = self.login(
            self.test_accounts["super_admin"]["username"],
            self.test_accounts["super_admin"]["password"]
        )
        
        # 登录商户账号
        self.merchant_token = self.login(
            self.test_accounts["merchant_admin"]["username"],
            self.test_accounts["merchant_admin"]["password"]
        )
        
        if not self.admin_token:
            raise Exception("无法获取管理员token")
        if not self.merchant_token:
            raise Exception("无法获取商户token")
            
        print("✅ 测试环境设置完成")
    
    def test_get_dashboard_statistics(self):
        """测试获取仪表盘统计数据"""
        print("\n=== 测试获取仪表盘统计数据 ===")
        
        # 测试管理员获取统计数据
        status_code, response = self.make_request("GET", "/dashboard/statistics", self.admin_token)
        
        if status_code == 200:
            self.results.append(format_test_result(
                "管理员获取仪表盘统计",
                True,
                "管理员成功获取仪表盘统计数据"
            ))
            print("✅ 管理员成功获取仪表盘统计数据")
            
            # 检查响应数据格式
            if "data" in response:
                data = response["data"]
                expected_fields = ["total_cards", "total_merchants", "today_bindings"]
                if isinstance(data, dict):
                    available_fields = [field for field in expected_fields if field in data]
                    print(f"   📊 统计字段: {available_fields}")
        else:
            self.results.append(format_test_result(
                "管理员获取仪表盘统计",
                False,
                f"获取仪表盘统计失败，状态码: {status_code}"
            ))
            print(f"❌ 获取仪表盘统计失败，状态码: {status_code}")
        
        # 测试商户获取统计数据
        status_code, response = self.make_request("GET", "/dashboard/statistics", self.merchant_token)
        
        if status_code == 200:
            self.results.append(format_test_result(
                "商户获取仪表盘统计",
                True,
                "商户成功获取仪表盘统计数据"
            ))
            print("✅ 商户成功获取仪表盘统计数据")
        else:
            self.results.append(format_test_result(
                "商户获取仪表盘统计",
                False,
                f"商户获取仪表盘统计失败，状态码: {status_code}"
            ))
            print(f"❌ 商户获取仪表盘统计失败，状态码: {status_code}")
    
    def test_get_today_stats(self):
        """测试获取今日统计"""
        print("\n=== 测试获取今日统计 ===")
        
        # 测试获取今日统计
        status_code, response = self.make_request("GET", "/dashboard/today-stats", self.admin_token)
        
        if status_code == 200:
            self.results.append(format_test_result(
                "获取今日统计",
                True,
                "成功获取今日统计数据"
            ))
            print("✅ 成功获取今日统计数据")
        else:
            self.results.append(format_test_result(
                "获取今日统计",
                False,
                f"获取今日统计失败，状态码: {status_code}"
            ))
            print(f"❌ 获取今日统计失败，状态码: {status_code}")
    
    def test_get_merchant_activity(self):
        """测试获取商户活跃度"""
        print("\n=== 测试获取商户活跃度 ===")
        
        # 测试获取商户活跃度
        status_code, response = self.make_request("GET", "/dashboard/merchant-activity", self.admin_token)
        
        if status_code == 200:
            self.results.append(format_test_result(
                "获取商户活跃度",
                True,
                "成功获取商户活跃度数据"
            ))
            print("✅ 成功获取商户活跃度数据")
            
            # 检查响应格式
            if "data" in response:
                data = response["data"]
                if isinstance(data, list):
                    print(f"   📊 找到 {len(data)} 个商户活跃度记录")
        else:
            self.results.append(format_test_result(
                "获取商户活跃度",
                False,
                f"获取商户活跃度失败，状态码: {status_code}"
            ))
            print(f"❌ 获取商户活跃度失败，状态码: {status_code}")
    
    def test_get_bind_trend(self):
        """测试获取绑卡趋势"""
        print("\n=== 测试获取绑卡趋势 ===")
        
        # 测试不同时间周期的绑卡趋势
        periods = ["day", "week", "month"]
        
        for period in periods:
            status_code, response = self.make_request("GET", f"/dashboard/bind-trend/{period}", self.admin_token)
            
            if status_code == 200:
                self.results.append(format_test_result(
                    f"获取{period}绑卡趋势",
                    True,
                    f"成功获取{period}绑卡趋势数据"
                ))
                print(f"✅ 成功获取{period}绑卡趋势数据")
            else:
                self.results.append(format_test_result(
                    f"获取{period}绑卡趋势",
                    False,
                    f"获取{period}绑卡趋势失败，状态码: {status_code}"
                ))
                print(f"❌ 获取{period}绑卡趋势失败，状态码: {status_code}")
    
    def test_get_system_status(self):
        """测试获取系统状态"""
        print("\n=== 测试获取系统状态 ===")
        
        # 测试获取系统状态
        status_code, response = self.make_request("GET", "/dashboard/system-status", self.admin_token)
        
        if status_code == 200:
            self.results.append(format_test_result(
                "获取系统状态",
                True,
                "成功获取系统状态数据"
            ))
            print("✅ 成功获取系统状态数据")
            
            # 检查响应格式
            if "data" in response:
                data = response["data"]
                if isinstance(data, dict):
                    status_fields = ["database", "redis", "queue"]
                    available_status = [field for field in status_fields if field in data]
                    print(f"   📊 系统状态字段: {available_status}")
        else:
            self.results.append(format_test_result(
                "获取系统状态",
                False,
                f"获取系统状态失败，状态码: {status_code}"
            ))
            print(f"❌ 获取系统状态失败，状态码: {status_code}")
    
    def test_get_hourly_distribution(self):
        """测试获取小时分布"""
        print("\n=== 测试获取小时分布 ===")
        
        # 测试获取小时分布
        status_code, response = self.make_request("GET", "/dashboard/hourly-distribution", self.admin_token)
        
        if status_code == 200:
            self.results.append(format_test_result(
                "获取小时分布",
                True,
                "成功获取小时分布数据"
            ))
            print("✅ 成功获取小时分布数据")
        else:
            self.results.append(format_test_result(
                "获取小时分布",
                False,
                f"获取小时分布失败，状态码: {status_code}"
            ))
            print(f"❌ 获取小时分布失败，状态码: {status_code}")
    
    def test_get_merchant_ranking(self):
        """测试获取商户排名"""
        print("\n=== 测试获取商户排名 ===")
        
        # 测试获取商户排名
        status_code, response = self.make_request("GET", "/dashboard/merchant-ranking", self.admin_token)
        
        if status_code == 200:
            self.results.append(format_test_result(
                "获取商户排名",
                True,
                "成功获取商户排名数据"
            ))
            print("✅ 成功获取商户排名数据")
            
            # 检查响应格式
            if "data" in response:
                data = response["data"]
                if isinstance(data, list):
                    print(f"   📊 找到 {len(data)} 个商户排名记录")
        else:
            self.results.append(format_test_result(
                "获取商户排名",
                False,
                f"获取商户排名失败，状态码: {status_code}"
            ))
            print(f"❌ 获取商户排名失败，状态码: {status_code}")
    
    def test_dashboard_access_control(self):
        """测试仪表盘访问控制"""
        print("\n=== 测试仪表盘访问控制 ===")
        
        # 测试未授权访问
        status_code, response = self.make_request("GET", "/dashboard/statistics", None)
        
        if status_code in [401, 403]:
            self.results.append(format_test_result(
                "未授权访问仪表盘",
                True,
                f"未授权访问正确被拒绝，状态码: {status_code}"
            ))
            print(f"✅ 未授权访问正确被拒绝，状态码: {status_code}")
        else:
            self.results.append(format_test_result(
                "未授权访问仪表盘",
                False,
                f"未授权访问应该被拒绝，状态码: {status_code}"
            ))
            print(f"❌ 未授权访问应该被拒绝，状态码: {status_code}")
    
    def test_dashboard_data_consistency(self):
        """测试仪表盘数据一致性"""
        print("\n=== 测试仪表盘数据一致性 ===")
        
        # 获取管理员和商户的统计数据，验证数据一致性
        admin_status_code, admin_response = self.make_request("GET", "/dashboard/statistics", self.admin_token)
        merchant_status_code, merchant_response = self.make_request("GET", "/dashboard/statistics", self.merchant_token)
        
        if admin_status_code == 200 and merchant_status_code == 200:
            admin_data = admin_response.get("data", {})
            merchant_data = merchant_response.get("data", {})
            
            # 管理员应该看到更多或相等的数据
            admin_total = admin_data.get("total_cards", 0) if isinstance(admin_data, dict) else 0
            merchant_total = merchant_data.get("total_cards", 0) if isinstance(merchant_data, dict) else 0
            
            if admin_total >= merchant_total:
                self.results.append(format_test_result(
                    "仪表盘数据一致性",
                    True,
                    f"数据一致性正常，管理员: {admin_total}, 商户: {merchant_total}"
                ))
                print(f"✅ 数据一致性正常，管理员: {admin_total}, 商户: {merchant_total}")
            else:
                self.results.append(format_test_result(
                    "仪表盘数据一致性",
                    False,
                    f"数据一致性异常，商户看到的数据比管理员多"
                ))
                print(f"❌ 数据一致性异常，商户看到的数据比管理员多")
        else:
            print("⚠️ 无法获取统计数据，跳过一致性检查")
    
    def test_dashboard_performance(self):
        """测试仪表盘查询性能"""
        print("\n=== 测试仪表盘查询性能 ===")
        
        # 测试统计数据查询性能
        start_time = time.time()
        status_code, response = self.make_request("GET", "/dashboard/statistics", self.admin_token)
        end_time = time.time()
        
        query_time = end_time - start_time
        
        if status_code == 200 and query_time < 3.0:  # 3秒内完成查询
            self.results.append(format_test_result(
                "仪表盘查询性能",
                True,
                f"查询性能良好，耗时: {query_time:.2f}秒"
            ))
            print(f"✅ 查询性能良好，耗时: {query_time:.2f}秒")
        elif status_code == 200:
            self.results.append(format_test_result(
                "仪表盘查询性能",
                False,
                f"查询性能较慢，耗时: {query_time:.2f}秒"
            ))
            print(f"⚠️ 查询性能较慢，耗时: {query_time:.2f}秒")
        else:
            self.results.append(format_test_result(
                "仪表盘查询性能",
                False,
                f"查询失败，状态码: {status_code}"
            ))
            print(f"❌ 查询失败，状态码: {status_code}")
    
    def run_all_tests(self):
        """运行所有仪表盘测试"""
        print("🧪 开始仪表盘测试")
        print("="*60)
        
        start_time = time.time()
        
        try:
            # 设置测试环境
            self.setup_test_environment()
            
            # 运行测试
            self.test_get_dashboard_statistics()
            self.test_get_today_stats()
            self.test_get_merchant_activity()
            self.test_get_bind_trend()
            self.test_get_system_status()
            self.test_get_hourly_distribution()
            self.test_get_merchant_ranking()
            self.test_dashboard_access_control()
            self.test_dashboard_data_consistency()
            self.test_dashboard_performance()
            
        except Exception as e:
            self.results.append(format_test_result(
                "测试环境设置",
                False,
                f"测试环境设置失败: {str(e)}"
            ))
            print(f"❌ 测试环境设置失败: {str(e)}")
        
        end_time = time.time()
        duration = end_time - start_time
        
        # 打印测试摘要
        print_test_summary(self.results)
        print(f"测试耗时: {duration:.2f}秒")
        
        return self.results

def main():
    """主函数"""
    test = DashboardTestSuite()
    results = test.run_all_tests()
    
    # 判断测试是否通过
    failed_tests = [r for r in results if not r.get("success", False)]
    
    if not failed_tests:
        print("\n🎉 仪表盘测试全部通过！")
        return 0
    else:
        print(f"\n⚠️ 发现 {len(failed_tests)} 个问题需要修复")
        for test in failed_tests:
            print(f"  - {test['test_name']}: {test['message']}")
        return 1

if __name__ == "__main__":
    sys.exit(main())
