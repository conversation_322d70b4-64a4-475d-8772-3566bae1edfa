"""
CK统计功能修复手动验证脚本

这个脚本用于手动验证修复后的统计逻辑是否正确工作。
通过直接调用API接口来测试不同场景下的统计结果。
"""

import requests
import json
from datetime import datetime, timedelta
import time


class CKStatisticsTestHelper:
    """CK统计测试辅助类"""
    
    def __init__(self, base_url="http://localhost:2000", username="admin", password="7c222fb2927d828af22f592134e8932480637c0d"):
        self.base_url = base_url
        self.username = username
        self.password = password
        self.token = None
        self.headers = {}
        
    def login(self):
        """登录获取token"""
        login_url = f"{self.base_url}/api/v1/auth/login"
        login_data = {
            "username": self.username,
            "password": self.password
        }
        
        response = requests.post(login_url, json=login_data)
        if response.status_code == 200:
            result = response.json()
            self.token = result["access_token"]
            self.headers = {"Authorization": f"Bearer {self.token}"}
            print("✅ 登录成功")
            return True
        else:
            print(f"❌ 登录失败: {response.text}")
            return False
    
    def create_test_ck(self, sign_suffix="test"):
        """创建测试CK"""
        create_url = f"{self.base_url}/api/v1/walmart-ck/"
        ck_data = {
            "sign": f"test_ck_{sign_suffix}@token#sign#26",
            "total_limit": 100,
            "active": 1,
            "description": f"测试CK - {sign_suffix}",
            "merchant_id": 1,  # 假设存在商户ID为1
            "department_id": 1  # 假设存在部门ID为1
        }
        
        response = requests.post(create_url, json=ck_data, headers=self.headers)
        if response.status_code == 200:
            result = response.json()
            print(f"✅ 创建CK成功: ID={result['id']}")
            return result['id']
        else:
            print(f"❌ 创建CK失败: {response.text}")
            return None
    
    def delete_ck(self, ck_id):
        """删除CK"""
        delete_url = f"{self.base_url}/api/v1/walmart-ck/{ck_id}"
        
        response = requests.delete(delete_url, headers=self.headers)
        if response.status_code == 200:
            print(f"✅ 删除CK成功: ID={ck_id}")
            return True
        else:
            print(f"❌ 删除CK失败: {response.text}")
            return False
    
    def get_ck_statistics(self, start_time=None, end_time=None):
        """获取CK统计数据"""
        stats_url = f"{self.base_url}/api/v1/walmart-ck/"
        params = {}
        
        if start_time:
            params['start_time'] = start_time.isoformat()
        if end_time:
            params['end_time'] = end_time.isoformat()
        
        response = requests.get(stats_url, params=params, headers=self.headers)
        if response.status_code == 200:
            result = response.json()
            return result.get('statistics', {})
        else:
            print(f"❌ 获取统计数据失败: {response.text}")
            return None
    
    def test_deletion_statistics_fix(self):
        """测试删除统计修复效果"""
        print("\n🧪 开始测试删除统计修复效果...")
        
        # 1. 获取当前时间
        now = datetime.now()
        today_start = now.replace(hour=0, minute=0, second=0, microsecond=0)
        today_end = now.replace(hour=23, minute=59, second=59, microsecond=999999)
        
        # 2. 获取修复前的统计数据（作为基准）
        print("\n📊 获取当前统计数据（基准）...")
        baseline_stats = self.get_ck_statistics(today_start, today_end)
        if baseline_stats:
            print(f"基准数据 - 总数: {baseline_stats.get('summary', {}).get('total_ck_count', 0)}")
            print(f"基准数据 - 删除数量: {baseline_stats.get('summary', {}).get('deleted_ck_count', 0)}")
        
        # 3. 创建一个新的CK
        print("\n🔧 创建测试CK...")
        test_ck_id = self.create_test_ck(f"deletion_test_{int(time.time())}")
        if not test_ck_id:
            print("❌ 无法创建测试CK，测试终止")
            return False
        
        # 4. 等待一秒确保时间差异
        time.sleep(1)
        
        # 5. 删除这个CK
        print(f"\n🗑️ 删除测试CK (ID: {test_ck_id})...")
        if not self.delete_ck(test_ck_id):
            print("❌ 无法删除测试CK，测试终止")
            return False
        
        # 6. 获取删除后的统计数据
        print("\n📊 获取删除后的统计数据...")
        after_stats = self.get_ck_statistics(today_start, today_end)
        if not after_stats:
            print("❌ 无法获取删除后的统计数据")
            return False
        
        # 7. 验证结果
        print("\n🔍 验证修复效果...")
        baseline_total = baseline_stats.get('summary', {}).get('total_ck_count', 0) if baseline_stats else 0
        baseline_deleted = baseline_stats.get('summary', {}).get('deleted_ck_count', 0) if baseline_stats else 0
        
        after_total = after_stats.get('summary', {}).get('total_ck_count', 0)
        after_deleted = after_stats.get('summary', {}).get('deleted_ck_count', 0)
        
        print(f"修复前 - 总数: {baseline_total}, 删除数量: {baseline_deleted}")
        print(f"修复后 - 总数: {after_total}, 删除数量: {after_deleted}")
        
        # 验证逻辑：
        # 1. 总数应该增加1（因为今天创建了一个CK）
        # 2. 删除数量应该增加1（因为今天删除了一个CK）
        total_increase = after_total - baseline_total
        deleted_increase = after_deleted - baseline_deleted
        
        print(f"\n📈 变化量 - 总数增加: {total_increase}, 删除数量增加: {deleted_increase}")
        
        if total_increase == 1 and deleted_increase == 1:
            print("✅ 修复效果验证成功！")
            print("   - 今天创建的CK被正确统计到总数中")
            print("   - 今天删除的CK被正确统计到删除数量中")
            return True
        else:
            print("❌ 修复效果验证失败！")
            if total_increase != 1:
                print(f"   - 总数变化异常，期望增加1，实际增加{total_increase}")
            if deleted_increase != 1:
                print(f"   - 删除数量变化异常，期望增加1，实际增加{deleted_increase}")
            return False
    
    def run_all_tests(self):
        """运行所有测试"""
        print("🚀 开始CK统计功能修复验证...")
        
        # 1. 登录
        if not self.login():
            return False
        
        # 2. 测试删除统计修复
        success = self.test_deletion_statistics_fix()
        
        if success:
            print("\n🎉 所有测试通过！CK统计功能修复验证成功！")
        else:
            print("\n💥 测试失败！需要进一步检查修复效果。")
        
        return success


def main():
    """主函数"""
    print("=" * 60)
    print("CK统计功能修复验证脚本")
    print("=" * 60)
    
    # 创建测试实例
    tester = CKStatisticsTestHelper()
    
    # 运行测试
    success = tester.run_all_tests()
    
    print("\n" + "=" * 60)
    if success:
        print("✅ 验证完成：修复效果良好")
    else:
        print("❌ 验证完成：需要进一步修复")
    print("=" * 60)


if __name__ == "__main__":
    main()
