#!/bin/bash
# 沃尔玛绑卡系统数据库升级脚本执行工具
# 
# 使用方法：
#   ./scripts/migrate.sh                    # 测试环境执行所有待执行的升级脚本
#   ./scripts/migrate.sh production         # 生产环境执行所有待执行的升级脚本
#   ./scripts/migrate.sh test v2.0.5        # 测试环境执行到指定版本
#   ./scripts/migrate.sh production --dry-run # 生产环境试运行

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 获取脚本所在目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
MIGRATIONS_DIR="$PROJECT_ROOT/migrations"

# 默认参数
ENVIRONMENT="test"
TARGET_VERSION=""
DRY_RUN=false

# 解析命令行参数
while [[ $# -gt 0 ]]; do
    case $1 in
        production|test)
            ENVIRONMENT="$1"
            shift
            ;;
        --dry-run)
            DRY_RUN=true
            shift
            ;;
        v*.*)
            TARGET_VERSION="$1"
            shift
            ;;
        -h|--help)
            echo "沃尔玛绑卡系统数据库升级工具"
            echo ""
            echo "使用方法:"
            echo "  $0 [environment] [options] [target_version]"
            echo ""
            echo "参数:"
            echo "  environment    执行环境: test 或 production (默认: test)"
            echo "  target_version 目标版本: 例如 v2.0.5 (可选)"
            echo ""
            echo "选项:"
            echo "  --dry-run      试运行模式，不实际执行"
            echo "  -h, --help     显示帮助信息"
            echo ""
            echo "示例:"
            echo "  $0                        # 测试环境执行所有待执行的升级脚本"
            echo "  $0 production             # 生产环境执行所有待执行的升级脚本"
            echo "  $0 test v2.0.5            # 测试环境执行到v2.0.5版本"
            echo "  $0 production --dry-run   # 生产环境试运行"
            exit 0
            ;;
        *)
            log_error "未知参数: $1"
            exit 1
            ;;
    esac
done

# 显示执行信息
log_info "======================================"
log_info "沃尔玛绑卡系统数据库升级工具"
log_info "======================================"
log_info "执行环境: $ENVIRONMENT"
log_info "目标版本: ${TARGET_VERSION:-最新}"
log_info "试运行模式: $DRY_RUN"
log_info "======================================"

# 检查migrations目录
if [ ! -d "$MIGRATIONS_DIR" ]; then
    log_error "升级脚本目录不存在: $MIGRATIONS_DIR"
    exit 1
fi

# 获取所有升级脚本文件（按版本号排序）
get_migration_files() {
    find "$MIGRATIONS_DIR" -name "v*.sql" | sort -V
}

# 获取数据库配置
get_db_config() {
    if [ "$ENVIRONMENT" = "production" ]; then
        # 生产环境从环境变量获取
        DB_HOST="${DB_HOST:-db}"
        DB_PORT="${DB_PORT:-3306}"
        DB_USER="${DB_USER:-root}"
        DB_PASSWORD="${DB_PASSWORD:-7c222fb2927d828af22f592134e8932480637c0d}"
        DB_NAME="${DB_NAME:-walmart_card_db}"
        CONTAINER_NAME="walmart-bind-card-db"
    else
        # 测试环境使用默认配置
        DB_HOST="${DB_HOST:-localhost}"
        DB_PORT="${DB_PORT:-3306}"
        DB_USER="${DB_USER:-root}"
        DB_PASSWORD="${DB_PASSWORD:-7c222fb2927d828af22f592134e8932480637c0d}"
        DB_NAME="${DB_NAME:-walmart_card_db}"
        CONTAINER_NAME=""
    fi
}

# 执行SQL脚本
execute_sql_file() {
    local file_path="$1"
    local file_name=$(basename "$file_path")
    
    log_info "执行升级脚本: $file_name"
    
    if [ "$DRY_RUN" = true ]; then
        log_warning "[DRY RUN] 跳过实际执行: $file_name"
        return 0
    fi
    
    if [ "$ENVIRONMENT" = "production" ] && [ -n "$CONTAINER_NAME" ]; then
        # 生产环境使用Docker执行
        if docker exec -i "$CONTAINER_NAME" mysql -u"$DB_USER" -p"$DB_PASSWORD" "$DB_NAME" < "$file_path"; then
            log_success "升级脚本执行成功: $file_name"
            return 0
        else
            log_error "升级脚本执行失败: $file_name"
            return 1
        fi
    else
        # 测试环境直接连接数据库
        if mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" "$DB_NAME" < "$file_path"; then
            log_success "升级脚本执行成功: $file_name"
            return 0
        else
            log_error "升级脚本执行失败: $file_name"
            return 1
        fi
    fi
}

# 检查升级脚本是否已执行
is_migration_executed() {
    local migration_name="$1"
    
    # 这里简化处理，实际应该查询migration_logs表
    # 暂时返回false，表示都需要执行
    return 1
}

# 主执行函数
main() {
    # 获取数据库配置
    get_db_config
    
    # 获取所有升级脚本
    migration_files=$(get_migration_files)
    
    if [ -z "$migration_files" ]; then
        log_info "没有发现升级脚本文件"
        return 0
    fi
    
    log_info "发现升级脚本文件:"
    echo "$migration_files" | while read -r file; do
        log_info "  - $(basename "$file")"
    done
    
    # 执行升级脚本
    success_count=0
    total_count=0
    
    echo "$migration_files" | while read -r file; do
        if [ -z "$file" ]; then
            continue
        fi
        
        file_name=$(basename "$file")
        total_count=$((total_count + 1))
        
        # 如果指定了目标版本，检查是否已达到目标
        if [ -n "$TARGET_VERSION" ]; then
            if [[ "$file_name" > "$TARGET_VERSION" ]]; then
                log_info "已达到目标版本 $TARGET_VERSION，停止执行"
                break
            fi
        fi
        
        # 执行升级脚本
        if execute_sql_file "$file"; then
            success_count=$((success_count + 1))
        else
            log_error "升级脚本执行失败，停止后续执行"
            exit 1
        fi
    done
    
    log_success "升级完成 - 成功: $success_count 个脚本"
}

# 验证升级结果
verify_migrations() {
    local verify_script="$PROJECT_ROOT/test/database/verify_file_order.sql"
    
    if [ ! -f "$verify_script" ]; then
        log_warning "验证脚本不存在，跳过验证"
        return 0
    fi
    
    log_info "开始验证升级结果..."
    
    if [ "$DRY_RUN" = true ]; then
        log_warning "[DRY RUN] 跳过验证"
        return 0
    fi
    
    if [ "$ENVIRONMENT" = "production" ] && [ -n "$CONTAINER_NAME" ]; then
        if docker exec -i "$CONTAINER_NAME" mysql -u"$DB_USER" -p"$DB_PASSWORD" "$DB_NAME" < "$verify_script" > /dev/null 2>&1; then
            log_success "升级结果验证通过"
            return 0
        else
            log_error "升级结果验证失败"
            return 1
        fi
    else
        if mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" "$DB_NAME" < "$verify_script" > /dev/null 2>&1; then
            log_success "升级结果验证通过"
            return 0
        else
            log_error "升级结果验证失败"
            return 1
        fi
    fi
}

# 执行主函数
if main; then
    # 验证升级结果
    if verify_migrations; then
        log_success "数据库升级完成！"
        exit 0
    else
        log_error "数据库升级验证失败！"
        exit 1
    fi
else
    log_error "数据库升级失败！"
    exit 1
fi