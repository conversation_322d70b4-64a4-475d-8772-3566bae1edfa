"""
系统设置API测试用例

测试内容：
1. 系统设置CRUD操作
2. CK过期配置API
3. 权限控制验证
4. 参数验证
5. 错误处理
"""

import pytest
from fastapi.testclient import TestClient
from sqlalchemy.orm import Session
from unittest.mock import patch

from app.main import app
from app.models.user import User
from app.models.system_settings import SystemSettings
from app.crud.system_settings import system_settings
from app.db.session import SessionLocal
from app.core.security import create_access_token


class TestSystemSettingsAPI:
    """系统设置API测试类"""

    @pytest.fixture
    def client(self):
        """创建测试客户端"""
        return TestClient(app)

    @pytest.fixture
    def db_session(self):
        """创建测试数据库会话"""
        db = SessionLocal()
        try:
            yield db
        finally:
            db.close()

    @pytest.fixture
    def super_admin_token(self, db_session):
        """创建超级管理员token"""
        # 创建超级管理员用户
        admin_user = User(
            username="test_admin",
            email="<EMAIL>",
            hashed_password="hashed_password",
            is_superuser=True,
            is_active=True
        )
        db_session.add(admin_user)
        db_session.commit()
        
        # 生成token
        token = create_access_token(subject=admin_user.id)
        return f"Bearer {token}"

    @pytest.fixture
    def normal_user_token(self, db_session):
        """创建普通用户token"""
        # 创建普通用户
        normal_user = User(
            username="test_user",
            email="<EMAIL>",
            hashed_password="hashed_password",
            is_superuser=False,
            is_active=True
        )
        db_session.add(normal_user)
        db_session.commit()
        
        # 生成token
        token = create_access_token(subject=normal_user.id)
        return f"Bearer {token}"

    @pytest.fixture
    def test_settings(self, db_session):
        """创建测试设置数据"""
        settings = [
            SystemSettings(
                key="test_setting_1",
                value="test_value_1",
                description="测试设置1",
                is_enabled=True,
                is_system=False
            ),
            SystemSettings(
                key="test_setting_2",
                value="test_value_2",
                description="测试设置2",
                is_enabled=True,
                is_system=True
            )
        ]
        
        for setting in settings:
            db_session.add(setting)
        db_session.commit()
        
        return settings

    def test_get_system_settings_success(self, client, super_admin_token, test_settings):
        """测试获取系统设置列表成功"""
        response = client.get(
            "/api/v1/system-settings/",
            headers={"Authorization": super_admin_token}
        )
        
        assert response.status_code == 200
        data = response.json()
        assert len(data) >= 2  # 至少包含测试数据

    def test_get_system_settings_permission_denied(self, client, normal_user_token):
        """测试普通用户访问系统设置被拒绝"""
        response = client.get(
            "/api/v1/system-settings/",
            headers={"Authorization": normal_user_token}
        )
        
        assert response.status_code == 403
        assert "只有超级管理员可以访问系统设置" in response.json()["detail"]

    def test_get_system_setting_by_key(self, client, super_admin_token, test_settings):
        """测试根据键名获取系统设置"""
        response = client.get(
            "/api/v1/system-settings/test_setting_1",
            headers={"Authorization": super_admin_token}
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data["key"] == "test_setting_1"
        assert data["value"] == "test_value_1"

    def test_get_system_setting_not_found(self, client, super_admin_token):
        """测试获取不存在的系统设置"""
        response = client.get(
            "/api/v1/system-settings/non_existent_key",
            headers={"Authorization": super_admin_token}
        )
        
        assert response.status_code == 404
        assert "不存在" in response.json()["detail"]

    def test_create_system_setting_success(self, client, super_admin_token):
        """测试创建系统设置成功"""
        setting_data = {
            "key": "new_test_setting",
            "value": "new_test_value",
            "description": "新测试设置"
        }
        
        response = client.post(
            "/api/v1/system-settings/",
            json=setting_data,
            headers={"Authorization": super_admin_token}
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data["key"] == "new_test_setting"
        assert data["value"] == "new_test_value"

    def test_create_system_setting_duplicate_key(self, client, super_admin_token, test_settings):
        """测试创建重复键名的系统设置"""
        setting_data = {
            "key": "test_setting_1",  # 已存在的键名
            "value": "duplicate_value",
            "description": "重复设置"
        }
        
        response = client.post(
            "/api/v1/system-settings/",
            json=setting_data,
            headers={"Authorization": super_admin_token}
        )
        
        assert response.status_code == 400
        assert "已存在" in response.json()["detail"]

    def test_update_system_setting_success(self, client, super_admin_token, test_settings):
        """测试更新系统设置成功"""
        update_data = {
            "value": "updated_value",
            "description": "更新后的描述"
        }
        
        response = client.put(
            "/api/v1/system-settings/test_setting_1",
            json=update_data,
            headers={"Authorization": super_admin_token}
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data["value"] == "updated_value"
        assert data["description"] == "更新后的描述"

    def test_delete_system_setting_success(self, client, super_admin_token, test_settings):
        """测试删除自定义系统设置成功"""
        response = client.delete(
            "/api/v1/system-settings/test_setting_1",
            headers={"Authorization": super_admin_token}
        )
        
        assert response.status_code == 200
        assert "删除成功" in response.json()["message"]

    def test_delete_system_setting_protected(self, client, super_admin_token, test_settings):
        """测试删除系统内置设置被拒绝"""
        response = client.delete(
            "/api/v1/system-settings/test_setting_2",  # 系统内置设置
            headers={"Authorization": super_admin_token}
        )
        
        assert response.status_code == 400
        assert "不能删除系统内置设置" in response.json()["detail"]


class TestCKExpireConfigAPI:
    """CK过期配置API测试类"""

    @pytest.fixture
    def client(self):
        """创建测试客户端"""
        return TestClient(app)

    @pytest.fixture
    def super_admin_token(self, db_session):
        """创建超级管理员token"""
        admin_user = User(
            username="test_admin",
            email="<EMAIL>",
            hashed_password="hashed_password",
            is_superuser=True,
            is_active=True
        )
        db_session.add(admin_user)
        db_session.commit()
        
        token = create_access_token(subject=admin_user.id)
        return f"Bearer {token}"

    @pytest.fixture
    def db_session(self):
        """创建测试数据库会话"""
        db = SessionLocal()
        try:
            yield db
        finally:
            db.close()

    def test_get_ck_expire_config_success(self, client, super_admin_token, db_session):
        """测试获取CK过期配置成功"""
        # 设置测试配置
        system_settings.set_value(db_session, 'ck_expire_minutes', '45')
        system_settings.set_value(db_session, 'ck_expire_check_enabled', 'true')
        system_settings.set_value(db_session, 'ck_expire_check_interval', '8')
        
        response = client.get(
            "/api/v1/system-settings/ck-expire/config",
            headers={"Authorization": super_admin_token}
        )
        
        assert response.status_code == 200
        data = response.json()
        assert "config" in data
        config = data["config"]
        assert config["expire_minutes"] == 45
        assert config["check_enabled"] is True
        assert config["check_interval"] == 8

    def test_update_ck_expire_config_success(self, client, super_admin_token):
        """测试更新CK过期配置成功"""
        config_data = {
            "expire_minutes": 60,
            "check_enabled": True,
            "check_interval": 10
        }
        
        response = client.put(
            "/api/v1/system-settings/ck-expire/config",
            params=config_data,
            headers={"Authorization": super_admin_token}
        )
        
        assert response.status_code == 200
        data = response.json()
        assert "config" in data
        config = data["config"]
        assert config["expire_minutes"] == 60
        assert config["check_enabled"] is True
        assert config["check_interval"] == 10

    def test_update_ck_expire_config_invalid_params(self, client, super_admin_token):
        """测试更新CK过期配置参数验证"""
        # 测试过期时间超出范围
        config_data = {
            "expire_minutes": 2000,  # 超过最大值1440
            "check_enabled": True,
            "check_interval": 5
        }
        
        response = client.put(
            "/api/v1/system-settings/ck-expire/config",
            params=config_data,
            headers={"Authorization": super_admin_token}
        )
        
        assert response.status_code == 400
        assert "过期时间必须在1-1440分钟之间" in response.json()["detail"]
        
        # 测试检测间隔超出范围
        config_data = {
            "expire_minutes": 30,
            "check_enabled": True,
            "check_interval": 100  # 超过最大值60
        }
        
        response = client.put(
            "/api/v1/system-settings/ck-expire/config",
            params=config_data,
            headers={"Authorization": super_admin_token}
        )
        
        assert response.status_code == 400
        assert "检测间隔必须在1-60分钟之间" in response.json()["detail"]

    def test_ck_expire_config_permission_denied(self, client, normal_user_token):
        """测试普通用户访问CK过期配置被拒绝"""
        response = client.get(
            "/api/v1/system-settings/ck-expire/config",
            headers={"Authorization": normal_user_token}
        )
        
        assert response.status_code == 403

    def test_unauthorized_access(self, client):
        """测试未授权访问"""
        response = client.get("/api/v1/system-settings/")
        assert response.status_code == 401


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
