-- =====================================================
-- 沃尔玛绑卡系统 - Telegram机器人完整功能迁移脚本
-- 版本: v2.2.0
-- 创建时间: 2025-01-15
-- 描述: 创建Telegram机器人相关表结构、权限和菜单（不使用枚举）
-- =====================================================

-- 设置字符集和时区
SET NAMES utf8mb4;
SET time_zone = '+00:00';

-- 禁用外键检查
SET FOREIGN_KEY_CHECKS = 0;

-- 开始事务
START TRANSACTION;

-- =====================================================
-- 1. 创建Telegram群组绑定表
-- =====================================================

CREATE TABLE IF NOT EXISTS telegram_groups (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
    chat_id BIGINT NOT NULL UNIQUE COMMENT 'Telegram群组ID',
    chat_title VARCHAR(255) NOT NULL COMMENT '群组标题',
    chat_type VARCHAR(20) NOT NULL COMMENT '群组类型: group, supergroup, channel',
    merchant_id BIGINT NOT NULL COMMENT '绑定的商户ID',
    department_id BIGINT NULL COMMENT '绑定的部门ID（可选，用于部门级数据隔离）',
    remark VARCHAR(255) NULL COMMENT '备注',
    bind_token VARCHAR(64) NOT NULL UNIQUE COMMENT '绑定令牌（用于安全绑定验证）',
    bind_status VARCHAR(20) NOT NULL DEFAULT 'pending' COMMENT '绑定状态: pending, active, suspended',
    bind_time DATETIME NULL COMMENT '绑定完成时间',
    bind_user_id BIGINT NULL COMMENT '执行绑定操作的用户ID',
    settings JSON NULL COMMENT '群组设置（通知开关、时区等）',
    last_active_time DATETIME NULL COMMENT '最后活跃时间',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',

    -- 外键约束
    FOREIGN KEY (merchant_id) REFERENCES merchants(id) ON DELETE CASCADE,
    FOREIGN KEY (department_id) REFERENCES departments(id) ON DELETE SET NULL,
    FOREIGN KEY (bind_user_id) REFERENCES users(id) ON DELETE SET NULL,

    -- 索引设计
    INDEX idx_telegram_groups_merchant_id (merchant_id),
    INDEX idx_telegram_groups_department_id (department_id),
    INDEX idx_telegram_groups_bind_token (bind_token),
    INDEX idx_telegram_groups_bind_status (bind_status),
    INDEX idx_telegram_groups_chat_type (chat_type),
    INDEX idx_telegram_groups_created_at (created_at),
    INDEX idx_telegram_groups_updated_at (updated_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
COMMENT='Telegram群组绑定表';

-- =====================================================
-- 2. 创建Telegram用户关联表
-- =====================================================

CREATE TABLE IF NOT EXISTS telegram_users (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
    telegram_user_id BIGINT NOT NULL UNIQUE COMMENT 'Telegram用户ID',
    telegram_username VARCHAR(255) NULL COMMENT 'Telegram用户名',
    telegram_first_name VARCHAR(255) NULL COMMENT 'Telegram名字',
    telegram_last_name VARCHAR(255) NULL COMMENT 'Telegram姓氏',
    system_user_id BIGINT NULL COMMENT '关联的系统用户ID',
    verification_token VARCHAR(64) NULL COMMENT '验证令牌',
    verification_status VARCHAR(20) DEFAULT 'pending' COMMENT '验证状态: pending, verified, expired',
    verification_time DATETIME NULL COMMENT '验证完成时间',
    last_active_time DATETIME NULL COMMENT '最后活跃时间',
    settings JSON NULL COMMENT '用户个人设置',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',

    -- 外键约束
    FOREIGN KEY (system_user_id) REFERENCES users(id) ON DELETE SET NULL,

    -- 索引设计
    INDEX idx_telegram_users_telegram_user_id (telegram_user_id),
    INDEX idx_telegram_users_system_user_id (system_user_id),
    INDEX idx_telegram_users_verification_token (verification_token),
    INDEX idx_telegram_users_verification_status (verification_status),
    INDEX idx_telegram_users_created_at (created_at),
    INDEX idx_telegram_users_updated_at (updated_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
COMMENT='Telegram用户关联表';

-- =====================================================
-- 3. 创建机器人配置表
-- =====================================================

CREATE TABLE IF NOT EXISTS telegram_bot_configs (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
    config_key VARCHAR(100) NOT NULL UNIQUE COMMENT '配置键',
    config_value TEXT NOT NULL COMMENT '配置值',
    config_type VARCHAR(20) DEFAULT 'string' COMMENT '配置类型: string, json, boolean, number',
    config_group VARCHAR(50) NULL COMMENT '配置分组',
    description TEXT NULL COMMENT '配置描述',
    is_encrypted BOOLEAN DEFAULT FALSE COMMENT '是否加密存储',
    is_system BOOLEAN DEFAULT FALSE COMMENT '是否为系统配置',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',

    -- 索引设计
    INDEX idx_telegram_bot_configs_config_key (config_key),
    INDEX idx_config_type (config_type),
    INDEX idx_config_group (config_group),
    INDEX idx_telegram_bot_configs_created_at (created_at),
    INDEX idx_telegram_bot_configs_updated_at (updated_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
COMMENT='机器人配置表';

-- =====================================================
-- 4. 创建机器人操作日志表
-- =====================================================

CREATE TABLE IF NOT EXISTS telegram_bot_logs (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
    chat_id BIGINT NOT NULL COMMENT '群组ID',
    user_id BIGINT NOT NULL COMMENT 'Telegram用户ID',
    command VARCHAR(100) NOT NULL COMMENT '执行的命令',
    request_data JSON NULL COMMENT '请求数据',
    response_data JSON NULL COMMENT '响应数据',
    execution_time INT NULL COMMENT '执行时间（毫秒）',
    status VARCHAR(20) NOT NULL COMMENT '执行状态: success, error, permission_denied',
    error_message TEXT NULL COMMENT '错误信息',
    group_id BIGINT NULL COMMENT '关联的群组记录ID',
    telegram_user_id BIGINT NULL COMMENT '关联的Telegram用户记录ID',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',

    -- 外键约束
    FOREIGN KEY (group_id) REFERENCES telegram_groups(id) ON DELETE SET NULL,
    FOREIGN KEY (telegram_user_id) REFERENCES telegram_users(id) ON DELETE SET NULL,

    -- 索引设计
    INDEX idx_telegram_bot_logs_chat_id (chat_id),
    INDEX idx_telegram_bot_logs_user_id (user_id),
    INDEX idx_telegram_bot_logs_command (command),
    INDEX idx_telegram_bot_logs_status (status),
    INDEX idx_telegram_bot_logs_created_at (created_at),
    INDEX idx_telegram_bot_logs_updated_at (updated_at),
    INDEX idx_telegram_bot_logs_group_id (group_id),
    INDEX idx_telegram_bot_logs_telegram_user_id (telegram_user_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
COMMENT='机器人操作日志表';

-- =====================================================
-- 5. 创建商户级Telegram配置表
-- =====================================================

CREATE TABLE IF NOT EXISTS merchant_telegram_settings (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
    merchant_id BIGINT NOT NULL UNIQUE COMMENT '商户ID',
    settings TEXT NOT NULL COMMENT '商户级Telegram配置（JSON格式）',
    is_active BOOLEAN DEFAULT TRUE COMMENT '是否启用',
    created_by BIGINT NULL COMMENT '创建人ID',
    updated_by BIGINT NULL COMMENT '更新人ID',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    -- 外键约束
    FOREIGN KEY (merchant_id) REFERENCES merchants(id) ON DELETE CASCADE,
    FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL,
    FOREIGN KEY (updated_by) REFERENCES users(id) ON DELETE SET NULL,
    
    -- 索引设计
    INDEX idx_merchant_id (merchant_id),
    INDEX idx_is_active (is_active),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
COMMENT='商户级Telegram配置表';

-- =====================================================
-- 6. 创建Telegram权限模板表
-- =====================================================

CREATE TABLE IF NOT EXISTS telegram_permission_templates (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
    template_name VARCHAR(100) NOT NULL COMMENT '模板名称',
    template_code VARCHAR(50) NOT NULL UNIQUE COMMENT '模板代码',
    description TEXT NULL COMMENT '模板描述',
    settings TEXT NOT NULL COMMENT '权限配置模板（JSON格式）',
    is_system BOOLEAN DEFAULT FALSE COMMENT '是否为系统模板',
    is_active BOOLEAN DEFAULT TRUE COMMENT '是否启用',
    created_by BIGINT NULL COMMENT '创建人ID',
    updated_by BIGINT NULL COMMENT '更新人ID',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    -- 外键约束
    FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL,
    FOREIGN KEY (updated_by) REFERENCES users(id) ON DELETE SET NULL,
    
    -- 索引设计
    INDEX idx_template_code (template_code),
    INDEX idx_is_system (is_system),
    INDEX idx_is_active (is_active),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
COMMENT='Telegram权限模板表';

-- =====================================================
-- 7. 插入Telegram机器人主菜单
-- =====================================================

INSERT IGNORE INTO menus (
    name, code, path, component, icon, parent_id, level, sort_order,
    is_visible, is_enabled, menu_type, description, created_at, updated_at
) VALUES (
    'Telegram机器人', 'telegram', '/telegram', '', 'ChatDotRound', NULL, 1, 800,
    1, 1, 'menu', 'Telegram机器人管理模块', NOW(3), NOW(3)
);

-- 获取刚插入的Telegram主菜单ID
SET @telegram_menu_id = (SELECT id FROM menus WHERE code = 'telegram' LIMIT 1);

-- =====================================================
-- 8. 插入Telegram子菜单
-- =====================================================

INSERT IGNORE INTO menus (
    name, code, path, component, icon, parent_id, level, sort_order,
    is_visible, is_enabled, menu_type, description, created_at, updated_at
) VALUES
-- 机器人概览
(
    '机器人概览', 'telegram:dashboard', '/telegram/dashboard', 'telegram/dashboard/index', 'Monitor',
    @telegram_menu_id, 2, 801, 1, 1, 'menu', 'Telegram机器人状态概览', NOW(3), NOW(3)
),
-- 配置管理
(
    '配置管理', 'telegram:config', '/telegram/config', 'telegram/config/index', 'Setting',
    @telegram_menu_id, 2, 802, 1, 1, 'menu', 'Telegram机器人配置管理', NOW(3), NOW(3)
),
-- 群组管理
(
    '群组管理', 'telegram:groups', '/telegram/groups', 'telegram/groups/index', 'ChatDotRound',
    @telegram_menu_id, 2, 803, 1, 1, 'menu', 'Telegram群组管理', NOW(3), NOW(3)
),
-- 用户管理
(
    '用户管理', 'telegram:users', '/telegram/users', 'telegram/users/index', 'User',
    @telegram_menu_id, 2, 804, 1, 1, 'menu', 'Telegram用户管理', NOW(3), NOW(3)
),
-- 统计分析
(
    '统计分析', 'telegram:statistics', '/telegram/statistics', 'telegram/statistics/index', 'DataAnalysis',
    @telegram_menu_id, 2, 805, 1, 1, 'menu', 'Telegram统计分析', NOW(3), NOW(3)
),
-- 日志管理
(
    '日志管理', 'telegram:logs', '/telegram/logs', 'telegram/logs/index', 'Document',
    @telegram_menu_id, 2, 806, 1, 1, 'menu', 'Telegram日志管理', NOW(3), NOW(3)
);

-- =====================================================
-- 9. 插入Telegram相关权限
-- =====================================================

INSERT IGNORE INTO permissions (code, name, description, resource_type) VALUES
-- API权限
('api:telegram:read', 'Telegram查看权限', '查看Telegram相关数据', 'api'),
('api:telegram:write', 'Telegram编辑权限', '编辑Telegram相关数据', 'api'),
('api:telegram:delete', 'Telegram删除权限', '删除Telegram相关数据', 'api'),
('api:telegram:bind', 'Telegram群组绑定', '绑定Telegram群组到商户', 'api'),
('api:telegram:unbind', 'Telegram群组解绑', '解绑Telegram群组', 'api'),
('api:telegram:config', 'Telegram配置管理', '管理Telegram机器人配置', 'api'),
('api:telegram:stats', 'Telegram统计查询', '通过Telegram查询统计数据', 'api'),
('api:telegram:logs', 'Telegram日志管理', '管理Telegram日志', 'api'),

-- 菜单权限
('menu:telegram', 'Telegram管理菜单', 'Telegram功能管理菜单', 'menu'),
('menu:telegram:dashboard', '机器人概览菜单', '机器人状态概览菜单', 'menu'),
('menu:telegram:config', '配置管理菜单', '机器人配置管理菜单', 'menu'),
('menu:telegram:groups', '群组管理菜单', '群组管理菜单', 'menu'),
('menu:telegram:users', '用户管理菜单', '用户管理菜单', 'menu'),
('menu:telegram:statistics', '统计分析菜单', '统计分析菜单', 'menu'),
('menu:telegram:logs', '日志管理菜单', '日志管理菜单', 'menu');

-- =====================================================
-- 10. 为超级管理员角色分配权限
-- =====================================================

-- 获取超级管理员角色ID
SET @superuser_role_id = (SELECT id FROM roles WHERE code = 'super_admin' OR name = '超级管理员' LIMIT 1);

-- 分配所有Telegram API权限给超级管理员
INSERT IGNORE INTO role_permissions (role_id, permission_id)
SELECT @superuser_role_id, p.id FROM permissions p
WHERE p.code LIKE 'api:telegram:%' AND @superuser_role_id IS NOT NULL;

-- 分配所有Telegram菜单权限给超级管理员
INSERT IGNORE INTO role_permissions (role_id, permission_id)
SELECT @superuser_role_id, p.id FROM permissions p
WHERE p.code LIKE 'menu:telegram%' AND @superuser_role_id IS NOT NULL;

-- 分配菜单访问权限给超级管理员
INSERT IGNORE INTO role_menus (role_id, menu_id, created_at)
SELECT @superuser_role_id, m.id, NOW(3)
FROM menus m
WHERE m.code LIKE 'telegram%' AND @superuser_role_id IS NOT NULL;

-- =====================================================
-- 11. 为商户管理员角色分配基础权限
-- =====================================================

-- 获取商户管理员角色ID
SET @merchant_admin_role_id = (SELECT id FROM roles WHERE code = 'merchant_admin' LIMIT 1);

-- 分配基础Telegram权限给商户管理员
INSERT IGNORE INTO role_permissions (role_id, permission_id)
SELECT @merchant_admin_role_id, p.id FROM permissions p
WHERE p.code IN (
    'api:telegram:read', 'api:telegram:bind', 'api:telegram:unbind', 'api:telegram:stats',
    'menu:telegram', 'menu:telegram:groups', 'menu:telegram:statistics'
) AND @merchant_admin_role_id IS NOT NULL;

-- 分配相关菜单给商户管理员
INSERT IGNORE INTO role_menus (role_id, menu_id, created_at)
SELECT @merchant_admin_role_id, m.id, NOW(3)
FROM menus m
WHERE m.code IN ('telegram', 'telegram:groups', 'telegram:statistics')
AND @merchant_admin_role_id IS NOT NULL;

-- =====================================================
-- 12. 初始化默认配置
-- =====================================================

INSERT IGNORE INTO telegram_bot_configs (config_key, config_value, config_type, config_group, description, is_encrypted, is_system) VALUES
-- 基础配置
('bot_token', '', 'string', 'basic', 'Telegram Bot Token', TRUE, TRUE),
('webhook_url', '', 'string', 'basic', 'Webhook URL', FALSE, TRUE),
('webhook_secret', '', 'string', 'basic', 'Webhook Secret Token', TRUE, TRUE),
('bot_username', '', 'string', 'basic', '机器人用户名', FALSE, TRUE),

-- 限流配置
('rate_limit_global', '1000', 'number', 'rate_limit', '全局每小时请求限制', FALSE, TRUE),
('rate_limit_group', '100', 'number', 'rate_limit', '单群组每小时请求限制', FALSE, TRUE),
('rate_limit_user', '50', 'number', 'rate_limit', '单用户每小时请求限制', FALSE, TRUE),

-- 令牌配置
('bind_token_expire_hours', '24', 'number', 'token', '绑定令牌有效期（小时）', FALSE, TRUE),
('verification_token_expire_minutes', '30', 'number', 'token', '验证令牌有效期（分钟）', FALSE, TRUE),
('max_bind_attempts_per_day', '5', 'number', 'token', '每日最大绑定尝试次数', FALSE, TRUE),

-- 功能开关
('enable_audit_log', 'true', 'boolean', 'feature', '是否启用审计日志', FALSE, TRUE),
('mask_sensitive_data', 'true', 'boolean', 'feature', '是否脱敏敏感数据', FALSE, TRUE),
('enable_group_binding', 'true', 'boolean', 'feature', '是否启用群组绑定功能', FALSE, TRUE),
('enable_user_verification', 'true', 'boolean', 'feature', '是否启用用户验证功能', FALSE, TRUE),

-- 本地化配置
('default_timezone', 'Asia/Shanghai', 'string', 'localization', '默认时区', FALSE, TRUE),
('default_language', 'zh-CN', 'string', 'localization', '默认语言', FALSE, TRUE),
('date_format', 'YYYY-MM-DD HH:mm:ss', 'string', 'localization', '日期格式', FALSE, TRUE),

-- 通知配置
('notification_enabled', 'true', 'boolean', 'notification', '是否启用通知', FALSE, TRUE),
('notification_channels', '["telegram", "email"]', 'json', 'notification', '通知渠道', FALSE, TRUE),
('error_notification_enabled', 'true', 'boolean', 'notification', '是否启用错误通知', FALSE, TRUE);

-- =====================================================
-- 13. 初始化权限模板
-- =====================================================

INSERT IGNORE INTO telegram_permission_templates (template_name, template_code, description, settings, is_system, is_active) VALUES
-- 超级管理员模板
('超级管理员', 'super_admin', '拥有所有Telegram功能权限',
'{"commands": ["*"], "features": ["*"], "data_access": "all", "admin_functions": true}',
TRUE, TRUE),

-- 商户管理员模板
('商户管理员', 'merchant_admin', '商户级Telegram管理权限',
'{"commands": ["bind", "unbind", "stats", "list"], "features": ["group_management", "statistics"], "data_access": "merchant", "admin_functions": false}',
TRUE, TRUE),

-- 普通用户模板
('普通用户', 'normal_user', '基础Telegram查询权限',
'{"commands": ["stats", "help"], "features": ["statistics"], "data_access": "own", "admin_functions": false}',
TRUE, TRUE),

-- 只读用户模板
('只读用户', 'readonly_user', '只读权限模板',
'{"commands": ["help", "status"], "features": ["view_only"], "data_access": "readonly", "admin_functions": false}',
TRUE, TRUE);

-- =====================================================
-- 14. 提交事务并验证
-- =====================================================

COMMIT;

-- 恢复外键检查
SET FOREIGN_KEY_CHECKS = 1;

-- =====================================================
-- 15. 验证创建结果
-- =====================================================

SELECT 'Telegram表创建验证' as check_type;

SELECT 'telegram_groups' as table_name, COUNT(*) as record_count FROM telegram_groups
UNION ALL
SELECT 'telegram_users' as table_name, COUNT(*) as record_count FROM telegram_users
UNION ALL
SELECT 'telegram_bot_configs' as table_name, COUNT(*) as record_count FROM telegram_bot_configs
UNION ALL
SELECT 'telegram_bot_logs' as table_name, COUNT(*) as record_count FROM telegram_bot_logs
UNION ALL
SELECT 'merchant_telegram_settings' as table_name, COUNT(*) as record_count FROM merchant_telegram_settings
UNION ALL
SELECT 'telegram_permission_templates' as table_name, COUNT(*) as record_count FROM telegram_permission_templates;

-- 验证菜单创建
SELECT 'Telegram菜单验证' as check_type, COUNT(*) as menu_count
FROM menus WHERE code LIKE 'telegram%';

-- 验证权限创建
SELECT 'Telegram权限验证' as check_type, COUNT(*) as permission_count
FROM permissions WHERE code LIKE '%telegram%';

-- 验证配置初始化
SELECT 'Telegram配置验证' as check_type,
       COUNT(*) as total_configs,
       COUNT(CASE WHEN is_system = TRUE THEN 1 END) as system_configs,
       COUNT(DISTINCT config_group) as config_groups
FROM telegram_bot_configs;

-- 显示菜单层级结构
SELECT 'Telegram菜单结构' as info,
       COALESCE(m1.name, '根菜单') as parent_menu,
       m2.name as menu_name,
       m2.code as menu_code,
       m2.path as menu_path,
       m2.sort_order
FROM menus m2
LEFT JOIN menus m1 ON m1.id = m2.parent_id
WHERE m2.code LIKE 'telegram%'
ORDER BY m2.sort_order;

-- 最终完成提示
SELECT 'Telegram机器人功能初始化完成！' as message,
       'v2.2.0' as version,
       NOW() as completed_at;
