#!/bin/bash

# Walmart 绑卡系统数据库备份脚本
# 使用方法: ./backup.sh [daily|weekly|monthly]

set -e

# 配置变量
DB_HOST="mysql"
DB_NAME="walmart_card_db"
DB_USER="walmart_user"
BACKUP_DIR="/backups"
DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_TYPE=${1:-daily}

# 从密钥文件读取密码
if [ -f "/run/secrets/mysql_password" ]; then
    DB_PASSWORD=$(cat /run/secrets/mysql_password)
else
    echo "错误: 无法读取数据库密码"
    exit 1
fi

# 创建备份目录
mkdir -p "${BACKUP_DIR}/${BACKUP_TYPE}"

# 备份文件名
BACKUP_FILE="${BACKUP_DIR}/${BACKUP_TYPE}/walmart_card_db_${BACKUP_TYPE}_${DATE}.sql"
COMPRESSED_FILE="${BACKUP_FILE}.gz"

echo "开始备份数据库..."
echo "备份类型: ${BACKUP_TYPE}"
echo "备份时间: $(date)"
echo "备份文件: ${COMPRESSED_FILE}"

# 执行备份
mysqldump \
    --host="${DB_HOST}" \
    --user="${DB_USER}" \
    --password="${DB_PASSWORD}" \
    --single-transaction \
    --routines \
    --triggers \
    --events \
    --add-drop-database \
    --databases "${DB_NAME}" > "${BACKUP_FILE}"

# 压缩备份文件
gzip "${BACKUP_FILE}"

# 验证备份文件
if [ -f "${COMPRESSED_FILE}" ] && [ -s "${COMPRESSED_FILE}" ]; then
    echo "备份成功完成: ${COMPRESSED_FILE}"
    echo "备份文件大小: $(du -h ${COMPRESSED_FILE} | cut -f1)"
else
    echo "错误: 备份失败"
    exit 1
fi

# 清理旧备份
case ${BACKUP_TYPE} in
    "daily")
        # 保留最近7天的日备份
        find "${BACKUP_DIR}/daily" -name "*.sql.gz" -mtime +7 -delete
        ;;
    "weekly")
        # 保留最近4周的周备份
        find "${BACKUP_DIR}/weekly" -name "*.sql.gz" -mtime +28 -delete
        ;;
    "monthly")
        # 保留最近12个月的月备份
        find "${BACKUP_DIR}/monthly" -name "*.sql.gz" -mtime +365 -delete
        ;;
esac

echo "备份清理完成"

# 备份统计信息
echo "=== 备份统计 ==="
echo "日备份数量: $(find ${BACKUP_DIR}/daily -name "*.sql.gz" 2>/dev/null | wc -l)"
echo "周备份数量: $(find ${BACKUP_DIR}/weekly -name "*.sql.gz" 2>/dev/null | wc -l)"
echo "月备份数量: $(find ${BACKUP_DIR}/monthly -name "*.sql.gz" 2>/dev/null | wc -l)"
echo "总备份大小: $(du -sh ${BACKUP_DIR} 2>/dev/null | cut -f1)"

echo "备份脚本执行完成"
