from sqlalchemy import Column, String, Integer, BigInteger, <PERSON><PERSON><PERSON>, Text, Enum
from sqlalchemy.orm import relationship

from app.models.base import BaseModel, TimestampMixin
from app.models.associations import user_permissions, menu_permissions, role_permissions


# 改为使用常量类
class ResourceTypeConstants:
    """资源类型常量"""

    MENU = "MENU"
    API = "API"
    DATA = "DATA"


class Permission(BaseModel, TimestampMixin):
    """权限模型 - 与数据库表结构一致"""

    __tablename__ = "permissions"
    __table_args__ = ({"extend_existing": True},)  # 允许表重定义

    id = Column(BigInteger, primary_key=True, index=True, autoincrement=True)

    # 基本信息
    code = Column(
        String(100),
        nullable=False,
        unique=True,
        index=True,
        comment="权限代码，格式：module:action 或 api:/path",
    )
    name = Column(String(100), nullable=False, comment="权限名称")
    description = Column(Text, nullable=True, comment="权限描述")

    # 资源类型和路径（与SQL表结构一致）
    resource_type = Column(
        String(20),
        nullable=False,
        default=ResourceTypeConstants.API,
        comment="资源类型",
    )
    resource_path = Column(
        String(200), nullable=True, comment="资源路径（菜单路径或API路径）"
    )

    # 权限状态
    is_enabled = Column(Boolean, default=True, nullable=False, comment="是否启用")

    # 排序
    sort_order = Column(Integer, default=0, nullable=False, comment="排序号")

    # 关联关系 - 修复lazy加载策略以支持selectinload预加载
    roles = relationship(
        "Role",
        secondary=role_permissions,
        back_populates="permissions",
        lazy="select",  # 改为select以支持selectinload预加载
    )
    users = relationship(
        "User",
        secondary=user_permissions,
        back_populates="permissions",
        lazy="select",  # 改为select以支持selectinload预加载
    )
    menus = relationship(
        "Menu",
        secondary=menu_permissions,
        back_populates="permissions",
        lazy="select",  # 改为select以支持selectinload预加载
    )

    def __repr__(self):
        return f"<Permission(id={self.id}, code={self.code})>"
