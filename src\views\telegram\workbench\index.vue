<template>
  <div class="telegram-workbench">
    <!-- 页面标题 -->
    <div class="page-header">
      <h2>Telegram机器人管理工作台</h2>
      <p class="page-description">统一管理待办事项，提高工作效率</p>
    </div>

    <!-- 统计卡片 -->
    <el-row :gutter="20" class="stats-cards">
      <el-col :span="6">
        <el-card class="stat-card pending">
          <div class="stat-content">
            <div class="stat-icon">
              <el-icon :size="32" color="#E6A23C">
                <Clock />
              </el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-value">{{ pendingCount }}</div>
              <div class="stat-label">待审批用户</div>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="6">
        <el-card class="stat-card urgent">
          <div class="stat-content">
            <div class="stat-icon">
              <el-icon :size="32" color="#F56C6C">
                <Warning />
              </el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-value">{{ urgentCount }}</div>
              <div class="stat-label">紧急处理</div>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="6">
        <el-card class="stat-card today">
          <div class="stat-content">
            <div class="stat-icon">
              <el-icon :size="32" color="#67C23A">
                <CircleCheck />
              </el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-value">{{ todayProcessed }}</div>
              <div class="stat-label">今日已处理</div>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="6">
        <el-card class="stat-card active">
          <div class="stat-content">
            <div class="stat-icon">
              <el-icon :size="32" color="#409EFF">
                <User />
              </el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-value">{{ activeUsers }}</div>
              <div class="stat-label">活跃用户</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 快速操作区域 -->
    <el-row :gutter="20" class="quick-actions">
      <el-col :span="12">
        <el-card class="action-card">
          <template #header>
            <div class="card-header">
              <span>🚀 快速操作</span>
            </div>
          </template>
          
          <div class="action-buttons">
            <el-button 
              type="primary" 
              size="large" 
              :loading="batchApprovalLoading"
              @click="handleBatchApproval"
              :disabled="selectedUsers.length === 0"
            >
              <el-icon><Select /></el-icon>
              批量审批 ({{ selectedUsers.length }})
            </el-button>
            
            <el-button 
              type="success" 
              size="large"
              @click="handleQuickApproval"
            >
              <el-icon><CircleCheck /></el-icon>
              一键审批全部
            </el-button>
            
            <el-button 
              type="warning" 
              size="large"
              @click="refreshData"
              :loading="refreshLoading"
            >
              <el-icon><Refresh /></el-icon>
              刷新数据
            </el-button>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="12">
        <el-card class="action-card">
          <template #header>
            <div class="card-header">
              <span>📊 今日工作概览</span>
            </div>
          </template>
          
          <div class="work-summary">
            <div class="summary-item">
              <span class="label">处理用户：</span>
              <span class="value">{{ todayProcessed }} 人</span>
            </div>
            <div class="summary-item">
              <span class="label">平均处理时间：</span>
              <span class="value">{{ avgProcessTime }} 分钟</span>
            </div>
            <div class="summary-item">
              <span class="label">工作效率：</span>
              <span class="value efficiency" :class="efficiencyClass">{{ efficiency }}%</span>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 待办事项列表 -->
    <el-card class="todo-card">
      <template #header>
        <div class="card-header">
          <span>📋 待办事项</span>
          <div class="header-actions">
            <el-select v-model="filterType" placeholder="筛选类型" size="small" style="width: 120px;">
              <el-option label="全部" value="all" />
              <el-option label="待审批" value="pending" />
              <el-option label="紧急" value="urgent" />
            </el-select>
            
            <el-button 
              type="primary" 
              size="small"
              @click="handleSelectAll"
            >
              {{ isAllSelected ? '取消全选' : '全选' }}
            </el-button>
          </div>
        </div>
      </template>

      <div class="todo-list" v-loading="todoLoading">
        <div 
          v-for="item in filteredTodoList" 
          :key="item.id"
          class="todo-item"
          :class="{ 'urgent': item.isUrgent, 'selected': selectedUsers.includes(item.id) }"
          @click="toggleSelection(item.id)"
        >
          <div class="todo-checkbox">
            <el-checkbox 
              :model-value="selectedUsers.includes(item.id)"
              @change="toggleSelection(item.id)"
            />
          </div>
          
          <div class="todo-content">
            <div class="todo-header">
              <span class="todo-title">{{ item.title }}</span>
              <div class="todo-badges">
                <el-tag v-if="item.isUrgent" type="danger" size="small">紧急</el-tag>
                <el-tag :type="item.statusType" size="small">{{ item.statusText }}</el-tag>
              </div>
            </div>
            
            <div class="todo-details">
              <span class="detail-item">
                <el-icon><User /></el-icon>
                {{ item.username }}
              </span>
              <span class="detail-item">
                <el-icon><Clock /></el-icon>
                {{ item.timeAgo }}
              </span>
              <span class="detail-item">
                <el-icon><Document /></el-icon>
                {{ item.verificationToken }}
              </span>
            </div>
          </div>
          
          <div class="todo-actions">
            <el-button 
              type="success" 
              size="small"
              @click.stop="handleApprove(item)"
              :loading="item.approving"
            >
              通过
            </el-button>
            <el-button 
              type="danger" 
              size="small"
              @click.stop="handleReject(item)"
              :loading="item.rejecting"
            >
              拒绝
            </el-button>
            <el-button 
              type="info" 
              size="small"
              @click.stop="handleViewDetails(item)"
            >
              详情
            </el-button>
          </div>
        </div>
        
        <div v-if="filteredTodoList.length === 0" class="empty-state">
          <el-empty description="暂无待办事项" />
        </div>
      </div>
    </el-card>

    <!-- 用户详情弹窗 -->
    <el-dialog 
      v-model="detailDialogVisible" 
      title="用户详情" 
      width="600px"
      :close-on-click-modal="false"
    >
      <div v-if="selectedUserDetail" class="user-detail">
        <div class="detail-section">
          <h4>基本信息</h4>
          <div class="detail-grid">
            <div class="detail-item">
              <label>Telegram用户名：</label>
              <span>{{ selectedUserDetail.username }}</span>
            </div>
            <div class="detail-item">
              <label>真实姓名：</label>
              <span>{{ selectedUserDetail.realName }}</span>
            </div>
            <div class="detail-item">
              <label>验证令牌：</label>
              <span class="token">{{ selectedUserDetail.verificationToken }}</span>
            </div>
            <div class="detail-item">
              <label>申请时间：</label>
              <span>{{ selectedUserDetail.createdAt }}</span>
            </div>
          </div>
        </div>
        
        <div class="detail-section">
          <h4>审批建议</h4>
          <div class="approval-suggestion">
            <el-alert 
              :title="selectedUserDetail.suggestion.title"
              :type="selectedUserDetail.suggestion.type"
              :description="selectedUserDetail.suggestion.description"
              show-icon
            />
          </div>
        </div>
      </div>
      
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="detailDialogVisible = false">关闭</el-button>
          <el-button 
            type="success" 
            @click="handleApproveFromDetail"
            :loading="detailApprovalLoading"
          >
            通过审批
          </el-button>
          <el-button 
            type="danger" 
            @click="handleRejectFromDetail"
            :loading="detailRejectionLoading"
          >
            拒绝申请
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Clock,
  Warning,
  CircleCheck,
  User,
  Select,
  Refresh,
  Document
} from '@element-plus/icons-vue'
import { useTelegramStore } from '@/store/modules/telegram'

const telegramStore = useTelegramStore()

// 响应式数据
const pendingCount = ref(0)
const urgentCount = ref(0)
const todayProcessed = ref(0)
const activeUsers = ref(0)
const avgProcessTime = ref(0)
const efficiency = ref(0)

const batchApprovalLoading = ref(false)
const refreshLoading = ref(false)
const todoLoading = ref(false)
const detailDialogVisible = ref(false)
const detailApprovalLoading = ref(false)
const detailRejectionLoading = ref(false)

const filterType = ref('all')
const selectedUsers = ref([])
const todoList = ref([])
const selectedUserDetail = ref(null)

// 计算属性
const efficiencyClass = computed(() => {
  if (efficiency.value >= 90) return 'excellent'
  if (efficiency.value >= 70) return 'good'
  if (efficiency.value >= 50) return 'normal'
  return 'poor'
})

const filteredTodoList = computed(() => {
  if (filterType.value === 'all') return todoList.value
  if (filterType.value === 'pending') return todoList.value.filter(item => item.status === 'pending')
  if (filterType.value === 'urgent') return todoList.value.filter(item => item.isUrgent)
  return todoList.value
})

const isAllSelected = computed(() => {
  return filteredTodoList.value.length > 0 && 
         filteredTodoList.value.every(item => selectedUsers.value.includes(item.id))
})

// 方法
const loadWorkbenchData = async () => {
  try {
    todoLoading.value = true
    
    // 模拟加载数据
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    // 更新统计数据
    pendingCount.value = 8
    urgentCount.value = 2
    todayProcessed.value = 15
    activeUsers.value = 45
    avgProcessTime.value = 12
    efficiency.value = 85
    
    // 更新待办列表
    todoList.value = [
      {
        id: 1,
        title: '用户身份验证申请',
        username: '@zhangsan',
        verificationToken: 'VT_ABC123',
        timeAgo: '5分钟前',
        status: 'pending',
        statusText: '待审批',
        statusType: 'warning',
        isUrgent: true,
        approving: false,
        rejecting: false
      },
      {
        id: 2,
        title: '群组绑定申请',
        username: '@lisi',
        verificationToken: 'VT_DEF456',
        timeAgo: '15分钟前',
        status: 'pending',
        statusText: '待审批',
        statusType: 'warning',
        isUrgent: false,
        approving: false,
        rejecting: false
      }
    ]
    
  } catch (error) {
    ElMessage.error('加载工作台数据失败：' + error.message)
  } finally {
    todoLoading.value = false
  }
}

const toggleSelection = (id) => {
  const index = selectedUsers.value.indexOf(id)
  if (index > -1) {
    selectedUsers.value.splice(index, 1)
  } else {
    selectedUsers.value.push(id)
  }
}

const handleSelectAll = () => {
  if (isAllSelected.value) {
    selectedUsers.value = []
  } else {
    selectedUsers.value = filteredTodoList.value.map(item => item.id)
  }
}

const handleBatchApproval = async () => {
  if (selectedUsers.value.length === 0) {
    ElMessage.warning('请先选择要审批的用户')
    return
  }
  
  try {
    await ElMessageBox.confirm(
      `确定要批量审批 ${selectedUsers.value.length} 个用户吗？`,
      '批量审批确认',
      { type: 'warning' }
    )
    
    batchApprovalLoading.value = true
    
    // 模拟批量审批
    await new Promise(resolve => setTimeout(resolve, 2000))
    
    ElMessage.success(`成功审批 ${selectedUsers.value.length} 个用户`)
    selectedUsers.value = []
    await loadWorkbenchData()
    
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('批量审批失败：' + error.message)
    }
  } finally {
    batchApprovalLoading.value = false
  }
}

const handleQuickApproval = async () => {
  try {
    await ElMessageBox.confirm(
      '确定要一键审批所有待审批用户吗？',
      '一键审批确认',
      { type: 'warning' }
    )
    
    // 实现一键审批逻辑
    ElMessage.success('一键审批完成')
    await loadWorkbenchData()
    
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('一键审批失败：' + error.message)
    }
  }
}

const refreshData = async () => {
  refreshLoading.value = true
  await loadWorkbenchData()
  refreshLoading.value = false
  ElMessage.success('数据已刷新')
}

const handleApprove = async (item) => {
  item.approving = true
  try {
    await new Promise(resolve => setTimeout(resolve, 1000))
    ElMessage.success(`用户 ${item.username} 审批通过`)
    await loadWorkbenchData()
  } catch (error) {
    ElMessage.error('审批失败：' + error.message)
  } finally {
    item.approving = false
  }
}

const handleReject = async (item) => {
  item.rejecting = true
  try {
    await new Promise(resolve => setTimeout(resolve, 1000))
    ElMessage.success(`用户 ${item.username} 申请已拒绝`)
    await loadWorkbenchData()
  } catch (error) {
    ElMessage.error('拒绝失败：' + error.message)
  } finally {
    item.rejecting = false
  }
}

const handleViewDetails = (item) => {
  selectedUserDetail.value = {
    username: item.username,
    realName: '张三',
    verificationToken: item.verificationToken,
    createdAt: '2025-01-10 14:30:25',
    suggestion: {
      title: '建议通过审批',
      type: 'success',
      description: '用户信息完整，符合审批条件，建议通过审批。'
    }
  }
  detailDialogVisible.value = true
}

const handleApproveFromDetail = async () => {
  detailApprovalLoading.value = true
  try {
    await new Promise(resolve => setTimeout(resolve, 1000))
    ElMessage.success('审批通过')
    detailDialogVisible.value = false
    await loadWorkbenchData()
  } catch (error) {
    ElMessage.error('审批失败：' + error.message)
  } finally {
    detailApprovalLoading.value = false
  }
}

const handleRejectFromDetail = async () => {
  detailRejectionLoading.value = true
  try {
    await new Promise(resolve => setTimeout(resolve, 1000))
    ElMessage.success('申请已拒绝')
    detailDialogVisible.value = false
    await loadWorkbenchData()
  } catch (error) {
    ElMessage.error('拒绝失败：' + error.message)
  } finally {
    detailRejectionLoading.value = false
  }
}

// 生命周期
onMounted(() => {
  loadWorkbenchData()
})

// 监听筛选类型变化
watch(filterType, () => {
  selectedUsers.value = []
})
</script>

<style scoped>
.telegram-workbench {
  padding: 20px;
}

.page-header {
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0 0 8px 0;
  color: #303133;
}

.page-description {
  margin: 0;
  color: #909399;
  font-size: 14px;
}

.stats-cards {
  margin-bottom: 20px;
}

.stat-card {
  cursor: pointer;
  transition: all 0.3s;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.stat-content {
  display: flex;
  align-items: center;
  gap: 16px;
}

.stat-info {
  flex: 1;
}

.stat-value {
  font-size: 24px;
  font-weight: bold;
  color: #303133;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 14px;
  color: #909399;
}

.quick-actions {
  margin-bottom: 20px;
}

.action-card .card-header {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: bold;
}

.action-buttons {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
}

.work-summary {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.summary-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.summary-item .label {
  color: #909399;
  font-size: 14px;
}

.summary-item .value {
  font-weight: bold;
  color: #303133;
}

.efficiency.excellent { color: #67C23A; }
.efficiency.good { color: #409EFF; }
.efficiency.normal { color: #E6A23C; }
.efficiency.poor { color: #F56C6C; }

.todo-card .card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-actions {
  display: flex;
  gap: 12px;
  align-items: center;
}

.todo-list {
  max-height: 600px;
  overflow-y: auto;
}

.todo-item {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 16px;
  border: 1px solid #EBEEF5;
  border-radius: 8px;
  margin-bottom: 12px;
  cursor: pointer;
  transition: all 0.3s;
}

.todo-item:hover {
  border-color: #409EFF;
  background-color: #F0F9FF;
}

.todo-item.selected {
  border-color: #409EFF;
  background-color: #E6F7FF;
}

.todo-item.urgent {
  border-left: 4px solid #F56C6C;
}

.todo-content {
  flex: 1;
}

.todo-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.todo-title {
  font-weight: bold;
  color: #303133;
}

.todo-badges {
  display: flex;
  gap: 8px;
}

.todo-details {
  display: flex;
  gap: 16px;
  font-size: 14px;
  color: #909399;
}

.detail-item {
  display: flex;
  align-items: center;
  gap: 4px;
}

.todo-actions {
  display: flex;
  gap: 8px;
}

.empty-state {
  text-align: center;
  padding: 40px;
}

.user-detail {
  padding: 16px;
}

.detail-section {
  margin-bottom: 24px;
}

.detail-section h4 {
  margin: 0 0 16px 0;
  color: #303133;
  border-bottom: 1px solid #EBEEF5;
  padding-bottom: 8px;
}

.detail-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
}

.detail-item {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.detail-item label {
  font-size: 14px;
  color: #909399;
}

.detail-item span {
  font-weight: bold;
  color: #303133;
}

.detail-item .token {
  font-family: monospace;
  background-color: #F5F7FA;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
}

.approval-suggestion {
  margin-top: 16px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}
</style>
