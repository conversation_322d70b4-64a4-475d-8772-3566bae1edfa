import { defineStore } from 'pinia'
import { ElMessage } from 'element-plus'
import { useUserStore } from './user' // 引入用户Store
import { merchantApi } from '@/api/modules/merchant'
import { hasMerchantReadPermission } from '@/utils/permission'

export const useMerchantStore = defineStore('merchant', {
    state: () => ({
        currentMerchant: null,
        merchantList: [],
        merchants: [],
        recentMerchants: [],
        merchantStats: null,
        loading: false,
        error: null
    }),

    getters: {
        getCurrentMerchant: (state) => state.currentMerchant,
        getMerchantList: (state) => state.merchantList,
        getMerchantStats: (state) => state.merchantStats,
        isLoading: (state) => state.loading,
        isMerchantMode: (state) => !!state.currentMerchant,
        currentMerchantId: (state) => state.currentMerchant?.id,
        currentMerchantName: (state) => state.currentMerchant?.name || '所有商家',

        // 获取商家分组
        merchantGroups: (state) => {
            // 按照某个属性对商家进行分组
            const groups = {};

            state.merchants.forEach(merchant => {
                const groupKey = merchant.category || merchant.type || '其他';

                if (!groups[groupKey]) {
                    groups[groupKey] = {
                        id: groupKey,
                        name: groupKey,
                        merchants: []
                    };
                }

                groups[groupKey].merchants.push(merchant);
            });

            // 转换为数组并排序
            return Object.values(groups).sort((a, b) => a.name.localeCompare(b.name));
        }
    },

    actions: {
        // 获取用户特定的存储键
        getUserSpecificKey(baseKey) {
            const userStore = useUserStore()
            const userId = userStore.userId
            return userId ? `${baseKey}_${userId}` : baseKey
        },

        // 获取商户列表
        async fetchMerchantList(params) {
            this.loading = true
            this.error = null
            try {
                const data = await merchantApi.getList(params)
                this.merchantList = data.items || []
                return data
            } catch (error) {
                this.error = error.message || '获取商户列表失败'
                console.error('获取商户列表失败:', error)
                ElMessage.error(this.error)
                return null
            } finally {
                this.loading = false
            }
        },

        // 获取商户详情
        async fetchMerchantDetail(id) {
            this.loading = true
            this.error = null
            try {
                const data = await merchantApi.getDetail(id)
                if (id === this.currentMerchant?.id) {
                    this.currentMerchant = data
                }
                return data
            } catch (error) {
                this.error = error.message || '获取商户详情失败'
                console.error('获取商户详情失败:', error)
                ElMessage.error(this.error)
                return null
            } finally {
                this.loading = false
            }
        },

        // 创建商户
        async createMerchant(merchantData) {
            this.loading = true
            this.error = null
            try {
                const data = await merchantApi.create(merchantData)
                this.merchantList = [...this.merchantList, data]
                return data
            } catch (error) {
                this.error = error.message || '创建商户失败'
                console.error('创建商户失败:', error)
                ElMessage.error(this.error)
                return null
            } finally {
                this.loading = false
            }
        },

        // 更新商户
        async updateMerchant(id, merchantData) {
            this.loading = true
            this.error = null
            try {
                const data = await merchantApi.update(id, merchantData)
                if (id === this.currentMerchant?.id) {
                    this.currentMerchant = data
                }
                this.merchantList = this.merchantList.map(item =>
                    item.id === id ? data : item
                )
                return data
            } catch (error) {
                this.error = error.message || '更新商户失败'
                console.error('更新商户失败:', error)
                ElMessage.error(this.error)
                return null
            } finally {
                this.loading = false
            }
        },

        // 删除商户
        async deleteMerchant(id) {
            this.loading = true
            this.error = null
            try {
                await merchantApi.delete(id)
                if (id === this.currentMerchant?.id) {
                    this.currentMerchant = null
                }
                this.merchantList = this.merchantList.filter(item => item.id !== id)
                return true
            } catch (error) {
                this.error = error.message || '删除商户失败'
                console.error('删除商户失败:', error)
                ElMessage.error(this.error)
                return false
            } finally {
                this.loading = false
            }
        },

        // 设置当前商户
        setCurrentMerchant(merchant) {
            this.currentMerchant = merchant
        },

        // 获取商户统计数据
        async fetchMerchantStats(id) {
            this.loading = true
            this.error = null
            try {
                const data = await merchantApi.getStats(id)
                this.merchantStats = data
                return data
            } catch (error) {
                this.error = error.message || '获取商户统计数据失败'
                console.error('获取商户统计数据失败:', error)
                ElMessage.error(this.error)
                return null
            } finally {
                this.loading = false
            }
        },

        // 重置商户状态
        resetState() {
            this.currentMerchant = null
            this.merchantList = []
            this.merchantStats = null
            this.loading = false
        },

        // 获取商家列表
        async fetchMerchants() {
            // 检查权限
            const userStore = useUserStore()
            const userInfo = userStore.userInfo

            // 只有超级管理员或具有商户查询权限的用户才能获取商户列表
            if (!hasMerchantReadPermission(userInfo)) {
                console.warn('用户无商户查询权限，跳过商户列表获取')
                this.merchants = []
                this.merchantList = []
                return { items: [] }
            }

            this.loading = true
            this.error = null
            try {
                // 从服务器获取商家列表
                const response = await merchantApi.getList()
                this.merchants = response.items || []
                this.merchantList = this.merchants

                // 如果本地存储有最近访问的商家，加载它们（用户特定）
                const userRecentMerchantsKey = this.getUserSpecificKey('recentMerchants')
                const storedRecentMerchants = localStorage.getItem(userRecentMerchantsKey)
                if (storedRecentMerchants) {
                    try {
                        const parsedRecentMerchants = JSON.parse(storedRecentMerchants)

                        // 验证并过滤仍然存在的商家
                        const validRecentMerchants = []
                        for (const recentMerchant of parsedRecentMerchants) {
                            if (recentMerchant && recentMerchant.id) {
                                const currentMerchant = this.merchants.find(m => m.id === recentMerchant.id)
                                if (currentMerchant) {
                                    // 使用最新的商家数据
                                    validRecentMerchants.push(currentMerchant)
                                }
                            }
                        }

                        this.recentMerchants = validRecentMerchants

                        // 如果有无效的商家被过滤掉，更新localStorage
                        if (validRecentMerchants.length !== parsedRecentMerchants.length) {
                            try {
                                localStorage.setItem(userRecentMerchantsKey, JSON.stringify(validRecentMerchants))
                            } catch (storageError) {
                                console.warn('更新最近访问商家列表失败:', storageError)
                            }
                        }
                    } catch (e) {
                        console.error('解析最近访问商家失败:', e)
                        // 清除损坏的数据
                        localStorage.removeItem(userRecentMerchantsKey)
                        this.recentMerchants = []
                    }
                }

                return this.merchants
            } catch (error) {
                this.error = error.message || '获取商家列表失败'
                console.error('获取商家列表失败:', error)
                ElMessage.error(this.error)
                return []
            } finally {
                this.loading = false
            }
        },

        // 切换商家
        async switchMerchant(merchant) {
            try {
                if (!merchant) {
                    // 切换到全局模式（所有商家）
                    this.currentMerchant = null
                    const userCurrentMerchantKey = this.getUserSpecificKey('currentMerchant')
                    localStorage.removeItem(userCurrentMerchantKey)
                    ElMessage.success('已切换到全局视图')
                } else {
                    // 验证商户数据的有效性
                    if (!merchant.id || !merchant.name) {
                        throw new Error('商户数据无效')
                    }

                    // 验证商户是否存在于当前商户列表中
                    const merchantExists = this.merchants.some(m => m.id === merchant.id)
                    if (!merchantExists) {
                        throw new Error('商户不存在或已被删除')
                    }

                    // 切换到指定租户
                    this.currentMerchant = merchant
                    const userCurrentMerchantKey = this.getUserSpecificKey('currentMerchant')

                    try {
                        localStorage.setItem(userCurrentMerchantKey, JSON.stringify(merchant))
                    } catch (storageError) {
                        console.warn('保存商户选择到localStorage失败:', storageError)
                        // 即使localStorage失败，也继续切换商户
                    }

                    // 更新最近访问商家列表
                    this.addToRecentMerchants(merchant)

                    ElMessage.success(`已切换到商家：${merchant.name}`)
                }

                // 刷新当前页面数据
                window.dispatchEvent(new CustomEvent('merchant-switched'))

                return true
            } catch (error) {
                console.error('切换商家失败:', error)
                ElMessage.error('切换商家失败: ' + error.message)
                return false
            }
        },

        // 添加到最近访问商家 (用户特定)
        addToRecentMerchants(merchant) {
            try {
                // 验证商户数据
                if (!merchant || !merchant.id || !merchant.name) {
                    console.warn('无效的商户数据，跳过添加到最近访问列表')
                    return
                }

                // 移除已存在的相同商家
                this.recentMerchants = this.recentMerchants.filter(m => m.id !== merchant.id)

                // 添加到最前面
                this.recentMerchants.unshift(merchant)

                // 保持最多5个最近访问
                if (this.recentMerchants.length > 5) {
                    this.recentMerchants = this.recentMerchants.slice(0, 5)
                }

                // 保存到本地存储 (用户特定)
                const userRecentMerchantsKey = this.getUserSpecificKey('recentMerchants')
                try {
                    localStorage.setItem(userRecentMerchantsKey, JSON.stringify(this.recentMerchants))
                } catch (storageError) {
                    console.warn('保存最近访问商家到localStorage失败:', storageError)
                }
            } catch (error) {
                console.error('添加最近访问商家失败:', error)
            }
        },

        // 初始化租户
        async initMerchant() {
            // 检查权限
            const userStore = useUserStore()
            const userInfo = userStore.userInfo

            // 只有超级管理员或具有商户查询权限的用户才能初始化商户数据
            if (!hasMerchantReadPermission(userInfo)) {
                console.warn('用户无商户查询权限，跳过商户初始化')
                return
            }

            // 先加载商家列表
            await this.fetchMerchants()

            // 从本地存储加载当前商家 (用户特定)
            const userCurrentMerchantKey = this.getUserSpecificKey('currentMerchant')
            const storedCurrentMerchant = localStorage.getItem(userCurrentMerchantKey)

            if (storedCurrentMerchant) {
                try {
                    const parsedMerchant = JSON.parse(storedCurrentMerchant)

                    // 验证存储的商户是否仍然存在于商户列表中
                    const merchantExists = this.merchants.some(m => m.id === parsedMerchant.id)

                    if (merchantExists) {
                        // 使用最新的商户数据（可能有更新）
                        const latestMerchantData = this.merchants.find(m => m.id === parsedMerchant.id)
                        this.currentMerchant = latestMerchantData

                        // 更新localStorage中的商户数据
                        localStorage.setItem(userCurrentMerchantKey, JSON.stringify(latestMerchantData))

                        console.log('已恢复商户选择:', latestMerchantData.name)
                    } else {
                        // 商户不存在，清除无效的存储
                        console.warn('存储的商户已不存在，清除无效数据:', parsedMerchant)
                        localStorage.removeItem(userCurrentMerchantKey)
                        this.currentMerchant = null
                        ElMessage.warning('之前选择的商户已不存在，已切换到全局视图')
                    }
                } catch (e) {
                    console.error('解析当前商家失败:', e)
                    // 清除损坏的数据
                    localStorage.removeItem(userCurrentMerchantKey)
                    this.currentMerchant = null
                }
            }
        },

        // 验证当前选择的商户是否仍然有效
        validateCurrentMerchant() {
            if (!this.currentMerchant) {
                return true // 全局视图总是有效的
            }

            // 检查当前商户是否仍在商户列表中
            const merchantExists = this.merchants.some(m => m.id === this.currentMerchant.id)

            if (!merchantExists) {
                console.warn('当前选择的商户已不存在，切换到全局视图')

                // 清除无效的商户选择
                this.currentMerchant = null
                const userCurrentMerchantKey = this.getUserSpecificKey('currentMerchant')
                localStorage.removeItem(userCurrentMerchantKey)

                ElMessage.warning('当前选择的商户已不存在，已切换到全局视图')

                // 触发商户切换事件
                window.dispatchEvent(new CustomEvent('merchant-switched'))

                return false
            }

            return true
        },

        // 清除商家相关数据 (用于用户登出时调用)
        clearMerchantData() {
            this.currentMerchant = null
            this.recentMerchants = []
            // 注意：merchantList列表可以保留，因为它是全局数据
        }
    }
})