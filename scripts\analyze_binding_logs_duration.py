#!/usr/bin/env python3
"""
分析binding_logs表中duration_ms字段的问题

检查为什么很多日志记录的duration_ms字段为空，并提供修复方案
"""

import sys
import os

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

try:
    from sqlalchemy.orm import Session
    from sqlalchemy import text
    from app.db.session import SessionLocal
    from app.models.card_record import CardRecord
    from app.models.binding_log import BindingLog
    print("✅ 模块导入成功")
except ImportError as e:
    print(f"❌ 模块导入失败: {e}")
    print("请确保在项目根目录运行此脚本")
    sys.exit(1)


def analyze_duration_issue():
    """分析duration_ms字段的问题"""
    print("🔍 分析binding_logs表中duration_ms字段的问题")
    print("=" * 60)
    
    db = SessionLocal()
    try:
        # 1. 统计总体情况
        print("📊 总体统计:")
        
        total_logs = db.query(BindingLog).count()
        logs_with_duration = db.query(BindingLog).filter(BindingLog.duration_ms.isnot(None)).count()
        logs_without_duration = total_logs - logs_with_duration
        
        print(f"   总日志记录数: {total_logs}")
        print(f"   有duration_ms的记录: {logs_with_duration} ({logs_with_duration/total_logs*100:.1f}%)")
        print(f"   无duration_ms的记录: {logs_without_duration} ({logs_without_duration/total_logs*100:.1f}%)")
        print()
        
        # 2. 按日志类型统计
        print("📋 按日志类型统计duration_ms字段:")
        
        log_type_stats = db.execute(text("""
            SELECT 
                log_type,
                COUNT(*) as total_count,
                COUNT(duration_ms) as with_duration_count,
                COUNT(*) - COUNT(duration_ms) as without_duration_count,
                ROUND(COUNT(duration_ms) * 100.0 / COUNT(*), 1) as duration_percentage
            FROM binding_logs 
            GROUP BY log_type 
            ORDER BY total_count DESC
        """)).fetchall()
        
        for row in log_type_stats:
            log_type, total, with_duration, without_duration, percentage = row
            print(f"   {log_type:20} | 总数:{total:4} | 有时间:{with_duration:4} | 无时间:{without_duration:4} | 比例:{percentage:5.1f}%")
        
        print()
        
        # 3. 分析特定卡记录的情况
        print("🔍 分析特定卡记录的日志情况:")
        
        # 查询最近的几个记录
        recent_records = db.query(CardRecord).order_by(CardRecord.created_at.desc()).limit(5).all()
        
        for i, record in enumerate(recent_records, 1):
            print(f"\n   {i}. 记录 {str(record.id)[:8]}... (状态: {record.status})")
            
            # 查询该记录的日志
            logs = db.query(BindingLog).filter(
                BindingLog.card_record_id == str(record.id)
            ).order_by(BindingLog.timestamp).all()
            
            print(f"      日志总数: {len(logs)}")
            
            if logs:
                with_duration = sum(1 for log in logs if log.duration_ms is not None)
                without_duration = len(logs) - with_duration
                
                print(f"      有duration_ms: {with_duration}")
                print(f"      无duration_ms: {without_duration}")
                
                # 显示前几个日志的详情
                print(f"      前3个日志:")
                for j, log in enumerate(logs[:3], 1):
                    duration_info = f"{log.duration_ms:.2f}ms" if log.duration_ms else "无"
                    print(f"         {j}. {log.log_type} | {log.message[:30]}... | 耗时: {duration_info}")
        
        print()
        
        # 4. 检查时间线计算的影响
        print("⏱️  时间线计算影响分析:")
        
        # 查询一个有日志但时间线可能显示为0的记录
        problem_record = db.execute(text("""
            SELECT cr.id, cr.status, cr.process_time,
                   COUNT(bl.id) as log_count,
                   COUNT(bl.duration_ms) as duration_count,
                   SUM(bl.duration_ms) as total_duration_ms
            FROM card_records cr
            LEFT JOIN binding_logs bl ON cr.id = bl.card_record_id
            WHERE cr.status IN ('success', 'failed')
            GROUP BY cr.id, cr.status, cr.process_time
            HAVING log_count > 0
            ORDER BY cr.created_at DESC
            LIMIT 1
        """)).fetchone()
        
        if problem_record:
            record_id, status, process_time, log_count, duration_count, total_duration_ms = problem_record
            
            print(f"   示例记录: {record_id[:8]}...")
            print(f"   记录状态: {status}")
            print(f"   记录process_time: {process_time}秒")
            print(f"   日志总数: {log_count}")
            print(f"   有duration_ms的日志: {duration_count}")
            print(f"   日志总耗时: {total_duration_ms or 0}ms ({(total_duration_ms or 0)/1000:.2f}秒)")
            
            if total_duration_ms is None or total_duration_ms == 0:
                print("   ❌ 问题确认: 日志中没有duration_ms数据，导致时间线显示为0")
            else:
                print("   ✅ 该记录的日志有duration_ms数据")
        
    finally:
        db.close()


def analyze_duration_recording_logic():
    """分析duration_ms记录逻辑"""
    print("\n🔧 分析duration_ms记录逻辑")
    print("=" * 60)
    
    print("📋 当前哪些地方会记录duration_ms:")
    print("   1. log_bind_attempt_result() - 绑卡尝试结果")
    print("   2. log_system() - 系统日志（部分）")
    print("   3. create_log() - 直接创建日志时传入")
    print()
    
    print("❌ 问题分析:")
    print("   1. 大部分系统日志没有记录duration_ms")
    print("   2. API请求/响应日志没有记录duration_ms")
    print("   3. 状态变更日志没有记录duration_ms")
    print("   4. 错误日志没有记录duration_ms")
    print()
    
    print("💡 解决方案:")
    print("   方案1: 修改日志记录逻辑，为每个步骤计算耗时")
    print("   方案2: 基于日志时间戳计算步骤间的耗时")
    print("   方案3: 改进时间线计算逻辑，使用时间戳差值")


def suggest_fixes():
    """建议修复方案"""
    print("\n🛠️  修复方案建议")
    print("=" * 60)
    
    print("🎯 推荐方案: 改进时间线计算逻辑")
    print()
    print("当前逻辑:")
    print("   - 依赖binding_logs.duration_ms字段")
    print("   - 如果duration_ms为空，步骤耗时显示为空")
    print("   - 总耗时基于card_record的created_at和updated_at")
    print()
    
    print("改进后逻辑:")
    print("   1. 如果duration_ms存在，使用该值")
    print("   2. 如果duration_ms为空，计算与下一个日志的时间差")
    print("   3. 最后一个日志使用与记录完成时间的差值")
    print("   4. 总耗时仍使用card_record的时间差")
    print()
    
    print("📝 实现步骤:")
    print("   1. 修改 _build_step_details() 方法")
    print("   2. 添加基于时间戳的耗时计算逻辑")
    print("   3. 保持向后兼容性")
    print("   4. 测试验证修复效果")


if __name__ == "__main__":
    print("🚀 开始分析binding_logs的duration_ms问题")
    print()
    
    try:
        analyze_duration_issue()
        analyze_duration_recording_logic()
        suggest_fixes()
        
        print()
        print("✅ 分析完成！")
        print()
        print("🔧 下一步:")
        print("   1. 查看分析结果，确认问题")
        print("   2. 实施推荐的修复方案")
        print("   3. 测试时间线显示效果")
        
    except Exception as e:
        print(f"❌ 分析过程中出现错误: {str(e)}")
        import traceback
        traceback.print_exc()
