"""
审计日志Schema定义
"""
from typing import Optional, Dict, Any
from pydantic import BaseModel, Field
from datetime import datetime

# 审计相关常量已移至字符串类型


class AuditLogBase(BaseModel):
    """审计日志基础模型"""

    event_type: str = Field(..., description="事件类型")
    level: str = Field(..., description="审计级别")
    user_id: Optional[int] = Field(None, description="用户ID")
    operator_id: Optional[int] = Field(None, description="操作者ID")
    merchant_id: Optional[int] = Field(None, description="商户ID")
    resource_type: Optional[str] = Field(None, description="资源类型")
    resource_id: Optional[str] = Field(None, description="资源ID")
    action: str = Field(..., description="操作动作")
    ip_address: Optional[str] = Field(None, description="IP地址")
    user_agent: Optional[str] = Field(None, description="用户代理")
    request_method: Optional[str] = Field(None, description="请求方法")
    request_path: Optional[str] = Field(None, description="请求路径")
    request_params: Optional[Dict[str, Any]] = Field(None, description="请求参数")
    message: str = Field(..., description="日志消息")
    details: Optional[Dict[str, Any]] = Field(None, description="详细信息")
    trace_id: Optional[str] = Field(None, description="追踪ID")

    class Config:
        from_attributes = True


class AuditLogCreate(AuditLogBase):
    """创建审计日志时的模型"""
    pass


class AuditLogUpdate(BaseModel):
    """更新审计日志时的模型"""

    details: Optional[Dict[str, Any]] = Field(None, description="详细信息")
    message: Optional[str] = Field(None, description="日志消息")

    class Config:
        from_attributes = True


class AuditLogInDB(AuditLogBase):
    """数据库中的审计日志模型"""

    id: int = Field(..., description="审计日志ID")
    created_at: datetime = Field(..., description="创建时间")
    updated_at: Optional[datetime] = Field(None, description="更新时间")

    class Config:
        from_attributes = True


class AuditLog(AuditLogInDB):
    """API响应的审计日志模型"""
    pass


class AuditLogFilter(BaseModel):
    """审计日志过滤条件"""

    user_id: Optional[int] = Field(None, description="用户ID")
    merchant_id: Optional[int] = Field(None, description="商户ID")
    event_type: Optional[str] = Field(None, description="事件类型")
    level: Optional[str] = Field(None, description="审计级别")
    resource_type: Optional[str] = Field(None, description="资源类型")
    action: Optional[str] = Field(None, description="操作动作")
    ip_address: Optional[str] = Field(None, description="IP地址")
    start_date: Optional[datetime] = Field(None, description="开始时间")
    end_date: Optional[datetime] = Field(None, description="结束时间")

    class Config:
        from_attributes = True


class AuditLogStatistics(BaseModel):
    """审计日志统计信息"""

    total_count: int = Field(..., description="总数量")
    event_type_stats: Dict[str, int] = Field(..., description="按事件类型统计")
    level_stats: Dict[str, int] = Field(..., description="按级别统计")
    start_date: Optional[datetime] = Field(None, description="统计开始时间")
    end_date: Optional[datetime] = Field(None, description="统计结束时间")

    class Config:
        from_attributes = True


class SecurityEvent(BaseModel):
    """安全事件模型"""

    id: int = Field(..., description="事件ID")
    event_type: str = Field(..., description="事件类型")
    level: str = Field(..., description="级别")
    user_id: Optional[int] = Field(None, description="用户ID")
    ip_address: Optional[str] = Field(None, description="IP地址")
    action: str = Field(..., description="操作")
    message: str = Field(..., description="消息")
    created_at: datetime = Field(..., description="发生时间")

    class Config:
        from_attributes = True
