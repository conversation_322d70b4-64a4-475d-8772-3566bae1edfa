#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
运行所有绑卡相关测试
包括绑卡API测试、绑卡管理测试、批量绑卡测试等
"""

import sys
import os
import time
from datetime import datetime
from typing import List, Dict, Any

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.dirname(__file__))))

from test.conftest import save_test_report


def run_test_module(module_name: str, test_class_name: str) -> List[Dict[str, Any]]:
    """运行指定的测试模块"""
    try:
        # 动态导入测试模块
        module = __import__(f"test.cards.{module_name}", fromlist=[test_class_name])
        test_class = getattr(module, test_class_name)

        # 创建测试实例并运行
        test_instance = test_class()
        results = test_instance.run_all_tests()

        return results
    except Exception as e:
        print(f"❌ 运行测试模块 {module_name} 失败: {e}")
        return [{
            "test_name": f"{module_name}_error",
            "success": False,
            "message": f"测试模块运行失败: {str(e)}",
            "timestamp": time.time(),
            "datetime": datetime.now().isoformat()
        }]


def main():
    """主函数"""
    print("🧪 开始运行所有绑卡相关测试")
    print("="*80)

    start_time = time.time()
    all_results = []

    # 定义要运行的测试模块
    test_modules = [
        {
            "name": "绑卡API测试",
            "module": "test_bind_card_api",
            "class": "BindCardAPITestSuite",
            "description": "测试外部绑卡API接口"
        },
        {
            "name": "绑卡管理API测试",
            "module": "test_card_management_api",
            "class": "CardManagementAPITestSuite",
            "description": "测试内部绑卡管理接口"
        },
        {
            "name": "批量绑卡API测试",
            "module": "test_batch_bind_api",
            "class": "BatchBindAPITestSuite",
            "description": "测试批量绑卡功能"
        },
        {
            "name": "绑卡CRUD测试",
            "module": "test_cards_crud",
            "class": "CardRecordsCRUDTestSuite",
            "description": "测试绑卡记录CRUD操作"
        }
        # 注意：test_statistics_api.py 使用pytest框架，需要单独运行
        # 可以通过 pytest test/cards/test_statistics_api.py 运行
    ]

    # 运行每个测试模块
    for i, test_config in enumerate(test_modules, 1):
        print(f"\n{'='*20} 测试模块 {i}/{len(test_modules)} {'='*20}")
        print(f"📋 {test_config['name']}")
        print(f"📝 {test_config['description']}")
        print("-" * 60)

        module_start_time = time.time()

        try:
            # 运行测试模块
            results = run_test_module(test_config["module"], test_config["class"])

            # 添加模块标识
            for result in results:
                result["module"] = test_config["name"]
                result["module_key"] = test_config["module"]

            all_results.extend(results)

            # 统计本模块结果
            module_total = len(results)
            module_passed = sum(1 for r in results if r.get("success", False))
            module_failed = module_total - module_passed
            module_duration = time.time() - module_start_time

            print(f"\n📊 {test_config['name']} 结果:")
            print(f"   总测试数: {module_total}")
            print(f"   通过数: {module_passed}")
            print(f"   失败数: {module_failed}")
            print(f"   成功率: {(module_passed/module_total*100):.1f}%" if module_total > 0 else "   成功率: 0%")
            print(f"   耗时: {module_duration:.2f}秒")

            if module_failed > 0:
                print(f"   ⚠️ 失败的测试:")
                for result in results:
                    if not result.get("success", False):
                        print(f"     - {result['test_name']}: {result['message']}")

        except Exception as e:
            print(f"❌ 运行 {test_config['name']} 时发生错误: {e}")
            error_result = {
                "test_name": f"{test_config['module']}_execution_error",
                "success": False,
                "message": f"测试执行错误: {str(e)}",
                "module": test_config["name"],
                "module_key": test_config["module"],
                "timestamp": time.time(),
                "datetime": datetime.now().isoformat()
            }
            all_results.append(error_result)

    # 计算总体统计
    end_time = time.time()
    total_duration = end_time - start_time

    total_tests = len(all_results)
    total_passed = sum(1 for r in all_results if r.get("success", False))
    total_failed = total_tests - total_passed
    overall_success_rate = (total_passed / total_tests * 100) if total_tests > 0 else 0

    # 按模块统计
    module_stats = {}
    for result in all_results:
        module = result.get("module", "Unknown")
        if module not in module_stats:
            module_stats[module] = {"total": 0, "passed": 0, "failed": 0}

        module_stats[module]["total"] += 1
        if result.get("success", False):
            module_stats[module]["passed"] += 1
        else:
            module_stats[module]["failed"] += 1

    # 打印总体结果
    print(f"\n{'='*80}")
    print("🎯 绑卡测试总体结果")
    print(f"{'='*80}")
    print(f"总测试数: {total_tests}")
    print(f"通过数: {total_passed}")
    print(f"失败数: {total_failed}")
    print(f"总成功率: {overall_success_rate:.1f}%")
    print(f"总耗时: {total_duration:.2f}秒")

    # 打印模块统计
    print(f"\n📊 各模块统计:")
    for module, stats in module_stats.items():
        success_rate = (stats["passed"] / stats["total"] * 100) if stats["total"] > 0 else 0
        status_icon = "✅" if stats["failed"] == 0 else "⚠️"
        print(f"   {status_icon} {module}: {stats['passed']}/{stats['total']} ({success_rate:.1f}%)")

    # 保存测试报告
    try:
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        report_filename = f"bind_card_test_report_{timestamp}.json"

        # 构建完整的测试报告
        test_report = {
            "test_type": "绑卡功能测试",
            "timestamp": timestamp,
            "datetime": datetime.now().isoformat(),
            "summary": {
                "total_tests": total_tests,
                "passed_tests": total_passed,
                "failed_tests": total_failed,
                "success_rate": overall_success_rate,
                "duration_seconds": total_duration
            },
            "module_stats": module_stats,
            "test_results": all_results
        }

        report_path = save_test_report([test_report], report_filename)
        print(f"\n📄 测试报告已保存: {report_path}")

    except Exception as e:
        print(f"\n⚠️ 保存测试报告失败: {e}")

    # 显示失败的测试
    if total_failed > 0:
        print(f"\n❌ 失败的测试 ({total_failed} 个):")
        for result in all_results:
            if not result.get("success", False):
                module = result.get("module", "Unknown")
                print(f"   [{module}] {result['test_name']}: {result['message']}")

    # 给出建议
    print(f"\n💡 测试建议:")
    if overall_success_rate >= 95:
        print("   🎉 测试结果优秀！绑卡功能运行良好。")
    elif overall_success_rate >= 80:
        print("   👍 测试结果良好，但仍有改进空间。")
        print("   🔧 建议修复失败的测试用例。")
    else:
        print("   ⚠️ 测试结果需要改进。")
        print("   🔧 建议优先修复失败的测试用例。")
        print("   📋 检查API接口实现和数据库配置。")

    # 返回退出码
    if total_failed == 0:
        print(f"\n🎉 所有绑卡测试通过！")
        return 0
    else:
        print(f"\n⚠️ 发现 {total_failed} 个问题需要修复")
        return 1


if __name__ == "__main__":
    sys.exit(main())
