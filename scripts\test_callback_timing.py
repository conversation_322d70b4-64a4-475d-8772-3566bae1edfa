#!/usr/bin/env python3
"""
测试回调时序问题修复
"""
import sys
import asyncio
import uuid
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from app.db.session import SessionLocal
from app.crud import card as card_crud
from app.services.optimized_callback_service import optimized_callback_service
from app.core.logging import get_logger

logger = get_logger("callback_timing_test")


async def test_callback_timing_fix():
    """测试回调时序问题修复"""
    print("🧪 测试回调时序问题修复...")
    
    # 创建一个测试记录ID（UUID格式）
    test_record_id = str(uuid.uuid4())
    test_merchant_id = 2
    
    print(f"   测试记录ID: {test_record_id}")
    print(f"   测试商户ID: {test_merchant_id}")
    
    # 测试1: 验证记录不存在时的处理
    print("\n1. 测试记录不存在的情况...")
    try:
        with SessionLocal() as db:
            # 直接查询记录
            record = card_crud.get(db, test_record_id)
            print(f"   查询结果: {record}")
            
            if record is None:
                print("   ✅ 记录确实不存在，这是预期的")
            else:
                print("   ❌ 意外找到了记录")
                
    except Exception as e:
        print(f"   ❌ 查询记录时出错: {e}")
        return False
    
    # 测试2: 测试回调服务对不存在记录的处理
    print("\n2. 测试回调服务处理不存在记录...")
    try:
        test_callback_data = {
            "record_id": test_record_id,
            "merchant_id": test_merchant_id,
            "retry_count": 0,
            "ext_data": None,
            "trace_id": "test-timing-fix"
        }
        
        with SessionLocal() as db:
            # 这应该会优雅地处理不存在的记录
            result = await optimized_callback_service._process_single_callback(db, test_callback_data)
            print(f"   处理结果: {result}")
            
            if result is False:
                print("   ✅ 回调服务正确处理了不存在的记录")
            else:
                print("   ⚠️  回调服务返回了意外的结果")
                
    except Exception as e:
        print(f"   ❌ 回调服务处理时出错: {e}")
        return False
    
    # 测试3: 验证UUID格式处理
    print("\n3. 测试UUID格式处理...")
    try:
        # 测试有效的UUID
        valid_uuid = str(uuid.uuid4())
        parsed_uuid = uuid.UUID(valid_uuid)
        print(f"   有效UUID: {valid_uuid} -> {parsed_uuid}")
        print("   ✅ UUID格式处理正常")
        
        # 测试无效的UUID
        try:
            invalid_uuid = "invalid-uuid-format"
            uuid.UUID(invalid_uuid)
            print("   ❌ 应该抛出UUID格式错误")
            return False
        except ValueError:
            print("   ✅ 正确捕获了无效UUID格式")
            
    except Exception as e:
        print(f"   ❌ UUID处理测试失败: {e}")
        return False
    
    # 测试4: 测试数据库连接和查询
    print("\n4. 测试数据库连接和查询...")
    try:
        with SessionLocal() as db:
            # 查询一些实际存在的记录（如果有的话）
            from sqlalchemy import text
            result = db.execute(text("SELECT COUNT(*) FROM card_records")).fetchone()
            record_count = result[0] if result else 0
            print(f"   数据库中的记录总数: {record_count}")
            
            if record_count > 0:
                # 查询最新的一条记录
                latest_record = db.execute(text(
                    "SELECT id, status, callback_status FROM card_records ORDER BY created_at DESC LIMIT 1"
                )).fetchone()
                if latest_record:
                    print(f"   最新记录: ID={latest_record[0]}, status={latest_record[1]}, callback_status={latest_record[2]}")
            
            print("   ✅ 数据库查询正常")
            
    except Exception as e:
        print(f"   ❌ 数据库查询失败: {e}")
        return False
    
    print("\n🎉 所有时序测试通过！")
    return True


async def test_callback_service_methods():
    """测试回调服务的各个方法"""
    print("\n🔧 测试回调服务方法...")
    
    # 测试HTTP客户端配置
    print("1. 测试HTTP客户端配置...")
    try:
        from app.services.optimized_callback_service import get_http_client_config
        config = get_http_client_config()
        print(f"   HTTP配置: 连接数={config['limits'].max_connections}, 超时={config['timeout']}")
        print("   ✅ HTTP客户端配置正常")
    except Exception as e:
        print(f"   ❌ HTTP客户端配置错误: {e}")
        return False
    
    # 测试数据库会话管理
    print("\n2. 测试数据库会话管理...")
    try:
        async with optimized_callback_service._get_db_session() as session:
            print(f"   会话类型: {type(session)}")
            print("   ✅ 数据库会话管理正常")
    except Exception as e:
        print(f"   ❌ 数据库会话管理错误: {e}")
        return False
    
    print("\n✅ 回调服务方法测试通过！")
    return True


async def main():
    """主函数"""
    print("=" * 60)
    print("回调时序问题修复测试")
    print("=" * 60)
    
    try:
        # 基础时序测试
        success1 = await test_callback_timing_fix()
        
        # 服务方法测试
        success2 = await test_callback_service_methods()
        
        if success1 and success2:
            print("\n✅ 所有测试通过！回调时序问题已修复。")
            print("\n📝 修复说明:")
            print("   - 绑卡成功后，数据库事务会先提交")
            print("   - 然后再发送回调任务到队列")
            print("   - 这确保回调处理时能找到已提交的记录")
            return 0
        else:
            print("\n❌ 部分测试失败，请检查修复。")
            return 1
            
    except Exception as e:
        print(f"\n💥 测试过程中发生异常: {e}")
        return 1
    finally:
        # 清理资源
        try:
            await optimized_callback_service.close()
            print("\n🧹 资源清理完成")
        except Exception as e:
            print(f"\n⚠️  资源清理异常: {e}")


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
