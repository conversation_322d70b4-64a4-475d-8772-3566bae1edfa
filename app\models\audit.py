from typing import Dict, Any
from sqlalchemy import Column, Integer, BigInteger, String, JSON, ForeignKey, Text
from sqlalchemy.orm import relationship

from app.db.base_class import Base, TimestampMixin


class AuditEventType:
    """审计事件类型常量类"""

    ACCESS = "access"  # 访问事件
    AUTH = "auth"  # 认证事件
    PERMISSION = "permission"  # 权限事件
    OPERATION = "operation"  # 操作事件
    ATTACK = "attack"  # 攻击事件
    SYSTEM = "system"  # 系统事件
    DATA = "data"  # 数据事件

    @classmethod
    def get_all_values(cls):
        """获取所有事件类型值"""
        return [cls.ACCESS, cls.AUTH, cls.PERMISSION, cls.OPERATION, cls.ATTACK, cls.SYSTEM, cls.DATA]


class AuditLevel:
    """审计级别常量类"""

    INFO = "info"  # 信息
    WARNING = "warning"  # 警告
    ERROR = "error"  # 错误
    CRITICAL = "critical"  # 严重

    @classmethod
    def get_all_values(cls):
        """获取所有审计级别值"""
        return [cls.INFO, cls.WARNING, cls.ERROR, cls.CRITICAL]


class AuditLog(Base, TimestampMixin):
    """统一的审计日志模型"""

    __tablename__ = "audit_log"

    id = Column(BigInteger, primary_key=True, autoincrement=True)
    event_type = Column(String(20), nullable=False, default=AuditEventType.SYSTEM, comment="事件类型")
    level = Column(String(20), nullable=False, default=AuditLevel.INFO, comment="事件级别")
    user_id = Column(BigInteger, ForeignKey("users.id"), nullable=True, comment="用户ID")
    operator_id = Column(
        BigInteger, ForeignKey("users.id"), nullable=True, comment="操作者ID"
    )
    merchant_id = Column(BigInteger, nullable=True, comment="商户ID")

    # 资源信息
    resource_type = Column(String(50), nullable=True, comment="资源类型")
    resource_id = Column(Integer, nullable=True, comment="资源ID")
    action = Column(String(50), nullable=True, comment="操作类型")

    # 请求信息
    ip_address = Column(String(50), nullable=True, comment="IP地址")
    user_agent = Column(String(200), nullable=True, comment="User Agent")
    request_method = Column(String(10), nullable=True, comment="请求方法")
    request_path = Column(String(200), nullable=True, comment="请求路径")
    request_params = Column(JSON, nullable=True, comment="请求参数")

    # 详细信息
    message = Column(Text, nullable=False, comment="事件描述")
    details = Column(JSON, nullable=True, comment="详细信息")
    trace_id = Column(String(50), nullable=True, comment="追踪ID")

    # 关联关系
    user = relationship("User", foreign_keys=[user_id], back_populates="audit_logs")
    operator = relationship(
        "User", foreign_keys=[operator_id], back_populates="operated_logs"
    )

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "id": self.id,
            "event_type": self.event_type,
            "level": self.level,
            "user_id": self.user_id,
            "operator_id": self.operator_id,
            "merchant_id": self.merchant_id,
            "resource_type": self.resource_type,
            "resource_id": self.resource_id,
            "action": self.action,
            "ip_address": self.ip_address,
            "user_agent": self.user_agent,
            "request_method": self.request_method,
            "request_path": self.request_path,
            "request_params": self.request_params,
            "message": self.message,
            "details": self.details,
            "trace_id": self.trace_id,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None,
        }
