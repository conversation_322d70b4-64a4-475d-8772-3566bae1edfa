"""
Session utilities for handling both sync and async database sessions
"""

from typing import Union, Optional, Any, List
from sqlalchemy.orm import Session
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, func
from sqlalchemy.sql import Select


class SessionAdapter:
    """Adapter to handle both sync and async sessions uniformly"""
    
    def __init__(self, session: Union[Session, AsyncSession]):
        self.session = session
        self.is_async = isinstance(session, AsyncSession)
    
    def query(self, model):
        """Create a query for the given model"""
        if self.is_async:
            raise RuntimeError("Use select() statement for AsyncSession instead of query()")
        return self.session.query(model)
    
    def select(self, model):
        """Create a select statement for the given model"""
        return select(model)
    
    async def execute_async(self, stmt):
        """Execute a statement asynchronously"""
        if not self.is_async:
            raise RuntimeError("Cannot execute async on sync session")
        return await self.session.execute(stmt)
    
    def execute_sync(self, stmt):
        """Execute a statement synchronously"""
        if self.is_async:
            raise RuntimeError("Cannot execute sync on async session")
        return self.session.execute(stmt)
    
    async def get_one_async(self, model, id_value):
        """Get a single record by ID asynchronously"""
        if not self.is_async:
            raise RuntimeError("Use get_one_sync for sync session")
        
        stmt = select(model).filter(model.id == id_value)
        result = await self.session.execute(stmt)
        return result.scalar_one_or_none()
    
    def get_one_sync(self, model, id_value):
        """Get a single record by ID synchronously"""
        if self.is_async:
            raise RuntimeError("Use get_one_async for async session")
        
        return self.session.query(model).filter(model.id == id_value).first()
    
    async def get_multi_async(self, model, skip: int = 0, limit: int = 100, filters: Optional[dict] = None):
        """Get multiple records asynchronously"""
        if not self.is_async:
            raise RuntimeError("Use get_multi_sync for async session")
        
        stmt = select(model)
        
        # Apply filters
        if filters:
            for field, value in filters.items():
                if hasattr(model, field) and value is not None:
                    stmt = stmt.filter(getattr(model, field) == value)
        
        stmt = stmt.offset(skip).limit(limit)
        result = await self.session.execute(stmt)
        return result.scalars().all()
    
    def get_multi_sync(self, model, skip: int = 0, limit: int = 100, filters: Optional[dict] = None):
        """Get multiple records synchronously"""
        if self.is_async:
            raise RuntimeError("Use get_multi_async for async session")
        
        query = self.session.query(model)
        
        # Apply filters
        if filters:
            for field, value in filters.items():
                if hasattr(model, field) and value is not None:
                    query = query.filter(getattr(model, field) == value)
        
        return query.offset(skip).limit(limit).all()
    
    async def commit_async(self):
        """Commit transaction asynchronously"""
        if not self.is_async:
            raise RuntimeError("Use commit_sync for sync session")
        await self.session.commit()
    
    def commit_sync(self):
        """Commit transaction synchronously"""
        if self.is_async:
            raise RuntimeError("Use commit_async for async session")
        self.session.commit()
    
    async def refresh_async(self, obj):
        """Refresh object asynchronously"""
        if not self.is_async:
            raise RuntimeError("Use refresh_sync for sync session")
        await self.session.refresh(obj)
    
    def refresh_sync(self, obj):
        """Refresh object synchronously"""
        if self.is_async:
            raise RuntimeError("Use refresh_async for async session")
        self.session.refresh(obj)
    
    def add(self, obj):
        """Add object to session (works for both sync and async)"""
        self.session.add(obj)
    
    async def rollback_async(self):
        """Rollback transaction asynchronously"""
        if not self.is_async:
            raise RuntimeError("Use rollback_sync for sync session")
        await self.session.rollback()
    
    def rollback_sync(self):
        """Rollback transaction synchronously"""
        if self.is_async:
            raise RuntimeError("Use rollback_async for async session")
        self.session.rollback()


def is_async_session(session: Union[Session, AsyncSession]) -> bool:
    """Check if the session is an AsyncSession"""
    return isinstance(session, AsyncSession)


def create_session_adapter(session: Union[Session, AsyncSession]) -> SessionAdapter:
    """Create a session adapter for the given session"""
    return SessionAdapter(session)


async def safe_execute(session: Union[Session, AsyncSession], stmt):
    """Safely execute a statement on either sync or async session"""
    if isinstance(session, AsyncSession):
        return await session.execute(stmt)
    else:
        return session.execute(stmt)


async def safe_commit(session: Union[Session, AsyncSession]):
    """Safely commit a transaction on either sync or async session"""
    if isinstance(session, AsyncSession):
        await session.commit()
    else:
        session.commit()


async def safe_refresh(session: Union[Session, AsyncSession], obj):
    """Safely refresh an object on either sync or async session"""
    if isinstance(session, AsyncSession):
        await session.refresh(obj)
    else:
        session.refresh(obj)


async def safe_rollback(session: Union[Session, AsyncSession]):
    """Safely rollback a transaction on either sync or async session"""
    if isinstance(session, AsyncSession):
        await session.rollback()
    else:
        session.rollback()
