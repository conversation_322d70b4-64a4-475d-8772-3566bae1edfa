<script setup>
import { ref, onMounted, onUnmounted, computed, watch } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { usewalmartCKStore } from '@/store/modules/walmartCK'
import { useMerchantStore } from '@/store/modules/merchant'
import { useUserStore } from '@/store/modules/user'
import merchantSwitchListener from '@/utils/merchantSwitchListener'
import { Close, Check, Refresh, InfoFilled, View } from '@element-plus/icons-vue'

const router = useRouter()
const route = useRoute()
const userConfigStore = usewalmartCKStore()
const merchantStore = useMerchantStore()
const userStore = useUserStore()

const formRef = ref(null)
const loading = ref(false)
const submitting = ref(false)
// syncLoading 已移除 - Redis同步功能不再需要

// 判断是否为编辑模式
const isEdit = computed(() => !!route.params.id)
const pageTitle = computed(() => {
    if (isEdit.value) {
        return '编辑绑卡用户'
    }
    return isBatchMode.value ? '批量新增绑卡用户' : '新增绑卡用户'
})

// 当前使用的表单数据
const currentFormData = computed(() => {
    return isBatchMode.value ? batchData.value : formData.value
})

// 商户和部门数据
const merchants = ref([])
const departments = ref([])
const loadingMerchants = ref(false)
const loadingDepartments = ref(false)

// 批量模式状态
const isBatchMode = ref(false)

// 表单数据
const formData = ref({
    id: '',
    sign: '',
    merchantId: null,
    departmentId: null,
    totalLimit: 20,
    active: true,
    description: ''
})

// 批量模式数据
const batchData = ref({
    signs: '', // 多行文本，每行一个CK签名
    merchantId: null,
    departmentId: null,
    totalLimit: 20,
    active: true,
    description: ''
})

// 批量验证结果
const batchValidation = ref({
    validSigns: [],
    invalidSigns: [],
    duplicateSigns: [],
    totalCount: 0
})

// 备注预览数据
const descriptionPreview = ref({
    visible: false,
    items: []
})

// 当前用户信息
const currentUser = computed(() => userStore.userInfo)

// 表单校验规则
const rules = computed(() => {
    const baseRules = {}

    // 根据模式设置不同的验证规则
    if (isBatchMode.value) {
        // 批量模式验证规则
        baseRules.signs = [
            { required: true, message: '请输入CK签名', trigger: 'blur' },
            {
                validator: (rule, value, callback) => {
                    if (!value || !value.trim()) {
                        callback(new Error('请至少输入一个CK签名'))
                        return
                    }

                    const lines = value.split('\n').filter(line => line.trim())
                    if (lines.length === 0) {
                        callback(new Error('请至少输入一个CK签名'))
                        return
                    }

                    if (lines.length > 1000) {
                        callback(new Error('单次最多支持创建1000个CK'))
                        return
                    }

                    // 检查是否有格式错误或重复
                    if (batchValidation.value.invalidSigns.length > 0) {
                        callback(new Error(`存在${batchValidation.value.invalidSigns.length}个格式错误的CK签名`))
                        return
                    }

                    if (batchValidation.value.duplicateSigns.length > 0) {
                        callback(new Error(`存在${batchValidation.value.duplicateSigns.length}个重复的CK签名`))
                        return
                    }

                    callback()
                },
                trigger: 'blur'
            }
        ]
    } else {
        // 单个模式验证规则
        baseRules.sign = [
            { required: true, message: '请输入用户签名', trigger: 'blur' },
            { min: 10, message: '签名长度不能小于10个字符', trigger: 'blur' },
            {
                validator: (rule, value, callback) => {
                    // 更灵活的CK格式验证：用户名@域名#密钥#数字
                    // 允许用户名和域名部分包含字母、数字
                    // 允许密钥部分包含各种特殊字符（Base64编码常见字符）
                    // 最后部分必须是数字
                    const pattern = /^[a-zA-Z0-9]+@[a-zA-Z0-9]+#[A-Za-z0-9+/=×\-_]+#\d+$/
                    if (!pattern.test(value)) {
                        callback(new Error('签名格式必须为: 用户名@域名#密钥#版本号，例如：141b58191157469d93fb4226edd62cb2@9878461c8196d9227725861e14c887b6#gKR+×XhHSE2H516EYqv/pA==#975'))
                    } else {
                        callback()
                    }
                },
                trigger: 'blur'
            }
        ]
    }

    // 通用验证规则
    baseRules.totalLimit = [
        { required: true, message: '请输入总绑卡次数限制', trigger: 'blur' },
        { type: 'number', message: '请输入数字值', trigger: ['blur', 'change'] }
    ]

    // 如果是超级管理员，需要验证商户和部门选择
    if (userStore.isSuperAdmin) {
        baseRules.merchantId = [
            { required: true, message: '请选择商户', trigger: 'change' }
        ]
        baseRules.departmentId = [
            { required: true, message: '请选择部门', trigger: 'change' }
        ]
    } else {
        // 非超级管理员也需要验证部门选择（虽然会自动设置，但确保数据完整性）
        baseRules.departmentId = [
            { required: true, message: '请选择部门', trigger: 'change' }
        ]
    }

    return baseRules
})

// 获取商户列表
const fetchMerchants = async () => {
    if (!userStore.isSuperAdmin) return

    loadingMerchants.value = true
    try {
        const response = await merchantStore.fetchMerchants()
        merchants.value = response.items || []
    } catch (error) {
        console.error('获取商户列表失败', error)
        ElMessage.error('获取商户列表失败')
    } finally {
        loadingMerchants.value = false
    }
}

// 获取当前选择商户的名称
const currentMerchantName = computed(() => {
    if (userStore.isSuperAdmin) {
        // 超级管理员：优先使用当前选择的商户名称
        if (merchantStore.currentMerchant) {
            return merchantStore.currentMerchant.name
        }
        // 如果没有通过右上角选择，从商户列表中查找
        const merchantId = currentFormData.value.merchantId
        const merchant = merchants.value.find(m => m.id === merchantId)
        if (merchant) {
            return merchant.name
        }
        // 如果在编辑模式下且有商户名称数据，直接使用
        if (isEdit.value && formData.value.merchantName) {
            return formData.value.merchantName
        }
        return '未选择商户'
    } else {
        // 非超级管理员：使用当前用户的商户名称
        return userStore.userInfo?.merchant_name || '未知商户'
    }
})

// 商户选择是否为只读状态
const isMerchantReadonly = computed(() => {
    // 编辑模式下，商户字段始终只读
    if (isEdit.value) {
        return true
    }

    if (userStore.isSuperAdmin) {
        // 超级管理员：当通过右上角切换选择了商户时，表单中的商户字段变为只读
        return !!merchantStore.currentMerchant
    } else {
        // 非超级管理员：始终只读
        return true
    }
})

// 获取当前选择部门的名称
const currentDepartmentName = computed(() => {
    const departmentId = currentFormData.value.departmentId
    if (!departmentId) {
        return '未选择部门'
    }

    const department = departments.value.find(d => d.id === departmentId)
    return department ? department.name : '未知部门'
})

// 部门选择是否为只读状态
const isDepartmentReadonly = computed(() => {
    // 编辑模式下，部门字段始终只读
    if (isEdit.value) {
        return true
    }

    // 添加模式下：
    // - 超级管理员：可以选择部门（非只读）
    // - 商户管理员：可以选择其商户下的部门（非只读）
    // - 其他角色：只读（使用当前用户的部门）
    return false // 在添加模式下，允许所有用户选择部门
})

// 将树形部门数据扁平化为列表
const flattenDepartmentTree = (treeData, level = 0) => {
    const result = []
    if (!Array.isArray(treeData)) return result

    for (const item of treeData) {
        // 添加当前节点，使用缩进显示层级
        const prefix = '　'.repeat(level) // 使用全角空格缩进
        result.push({
            ...item,
            name: level > 0 ? `${prefix}├─ ${item.name}` : item.name
        })

        // 递归处理子节点
        if (item.children && item.children.length > 0) {
            result.push(...flattenDepartmentTree(item.children, level + 1))
        }
    }
    return result
}

// 获取部门列表 - 优化权限检查
const fetchDepartments = async (merchantId) => {
    if (!merchantId) {
        departments.value = []
        return
    }

    // 添加调用栈信息以便调试
    const stack = new Error().stack
    console.log('开始获取部门列表', {
        merchantId,
        type: typeof merchantId,
        caller: stack?.split('\n')[2]?.trim() // 获取调用者信息
    })

    loadingDepartments.value = true
    try {
        // 检查用户是否有部门查询权限
        const { usePermission } = await import('@/composables/usePermission')
        const { hasPermission } = usePermission()

        // 如果用户没有部门查询权限，提供降级方案
        if (!hasPermission('api:departments:read')) {
            console.warn('用户没有部门查询权限，使用降级方案')

            // 降级方案1：如果是非超级管理员，使用当前用户的部门信息
            if (!userStore.isSuperAdmin && userStore.departmentId) {
                departments.value = [{
                    id: userStore.departmentId,
                    name: userStore.userInfo?.department_name || '当前部门',
                    merchant_id: merchantId
                }]
                console.log('使用当前用户部门信息', departments.value)
                return
            }

            // 降级方案2：提示用户联系管理员或使用默认部门
            departments.value = []
            ElMessage.warning('您没有部门查询权限，请联系管理员或选择默认部门')
            return
        }

        // 使用树形结构API获取所有部门，避免分页限制
        const { departmentApi } = await import('@/api/modules/department')
        const response = await departmentApi.getTree({
            merchant_id: merchantId
        })

        console.log('部门API响应', response)

        // 修复数据解析逻辑：根据后端API响应格式正确解析数据
        let treeData = []
        if (response && response.data) {
            // 标准响应格式：{code: 0, data: {items: [...]}}
            if (response.data.items && Array.isArray(response.data.items)) {
                treeData = response.data.items
            } else if (Array.isArray(response.data)) {
                // 直接是数组格式：{code: 0, data: [...]}
                treeData = response.data
            }
        } else if (response && response.items && Array.isArray(response.items)) {
            // 直接返回items格式：{items: [...]}
            treeData = response.items
        } else if (Array.isArray(response)) {
            // 直接是数组格式
            treeData = response
        }

        // 将树形数据扁平化为列表供下拉选择使用
        departments.value = flattenDepartmentTree(treeData)

        console.log('部门列表已更新', { count: departments.value.length, departments: departments.value })
    } catch (error) {
        console.error('获取部门列表失败', error)

        // 权限错误的特殊处理
        if (error.response?.status === 401 || error.response?.status === 403) {
            console.warn('部门查询权限不足，使用降级方案')

            // 如果是权限问题且用户有自己的部门信息，使用降级方案
            if (!userStore.isSuperAdmin && userStore.departmentId) {
                departments.value = [{
                    id: userStore.departmentId,
                    name: userStore.userInfo?.department_name || '当前部门',
                    merchant_id: merchantId
                }]
                ElMessage.warning('部门查询权限不足，已自动选择您的当前部门')
                return
            }

            ElMessage.error('您没有部门查询权限，请联系管理员')
        } else {
            ElMessage.error('获取部门列表失败')
        }

        departments.value = []
    } finally {
        loadingDepartments.value = false
    }
}

// 防止重复调用的标志
const isUpdatingFromMerchantSwitch = ref(false)

// 智能部门预填充逻辑
const handleSmartDepartmentPreFill = async () => {
    try {
        // 检查是否为新增模式（编辑模式不自动预填充）
        if (isEdit.value) {
            console.log('编辑模式，跳过部门预填充')
            return
        }

        // 检查用户是否有部门管理权限
        const { usePermission } = await import('@/composables/usePermission')
        const { hasPermission } = usePermission()

        // 如果用户有部门管理权限，让其自由选择部门
        if (hasPermission('api:departments:read') || hasPermission('api:departments:manage')) {
            console.log('用户有部门管理权限，不自动预填充部门')
            formData.value.departmentId = null
            batchData.value.departmentId = null
            return
        }

        // 检查用户是否只属于单一部门
        const userDepartmentId = userStore.departmentId
        if (userDepartmentId) {
            console.log('用户只属于单一部门且无部门管理权限，自动预填充部门:', {
                departmentId: userDepartmentId,
                departmentName: currentUser.value?.department_name
            })

            // 自动预选用户所属的部门
            formData.value.departmentId = userDepartmentId
            batchData.value.departmentId = userDepartmentId
        } else {
            console.log('用户未分配部门，不进行预填充')
            formData.value.departmentId = null
            batchData.value.departmentId = null
        }
    } catch (error) {
        console.error('智能部门预填充失败:', error)
        // 出错时不预填充，让用户手动选择
        formData.value.departmentId = null
    }
}

// 监听商户变化，重新获取部门（但排除商户切换和初始化触发的更新）
watch(() => currentFormData.value.merchantId, (newMerchantId, oldMerchantId) => {
    console.log('watch监听到merchantId变化', {
        newMerchantId,
        oldMerchantId,
        isUpdatingFromMerchantSwitch: isUpdatingFromMerchantSwitch.value,
        isBatchMode: isBatchMode.value
    })

    // 如果是商户切换或初始化触发的更新，跳过watch处理
    if (isUpdatingFromMerchantSwitch.value) {
        console.log('跳过watch处理：正在进行商户切换或初始化')
        return
    }

    console.log('watch处理：清空部门选择并重新获取部门列表')

    // 根据当前模式清空部门选择
    if (isBatchMode.value) {
        batchData.value.departmentId = null
    } else {
        formData.value.departmentId = null
    }

    if (newMerchantId) {
        fetchDepartments(newMerchantId)
    }
})

// 商户切换监听器
let unregisterMerchantSwitch = null

// 处理商户切换事件
const handleMerchantSwitch = async () => {
    console.log('处理商户切换事件')
    isUpdatingFromMerchantSwitch.value = true // 设置标志，防止watch重复触发

    try {
        if (userStore.isSuperAdmin) {
            // 超级管理员：根据当前选择的商户更新表单
            const currentMerchant = merchantStore.currentMerchant
            if (currentMerchant) {
                // 同时更新两种模式的数据
                formData.value.merchantId = currentMerchant.id
                batchData.value.merchantId = currentMerchant.id

                // 只有在商户真正改变时才清空部门选择
                const oldMerchantId = formData.value.merchantId
                if (oldMerchantId !== currentMerchant.id) {
                    formData.value.departmentId = null
                    batchData.value.departmentId = null
                }

                await fetchDepartments(currentMerchant.id)
            } else {
                // 切换到全局视图，清空商户和部门选择
                formData.value.merchantId = null
                batchData.value.merchantId = null
                formData.value.departmentId = null
                batchData.value.departmentId = null
                departments.value = []
            }
        } else {
            // 非超级管理员：更新当前用户的商户信息，智能处理部门
            const oldMerchantId = formData.value.merchantId
            formData.value.merchantId = userStore.merchantId
            batchData.value.merchantId = userStore.merchantId

            // 应用智能部门预填充逻辑
            await handleSmartDepartmentPreFill()

            if (formData.value.merchantId) {
                await fetchDepartments(formData.value.merchantId)
            }
        }
    } finally {
        // 重置标志
        isUpdatingFromMerchantSwitch.value = false
    }
}

// 获取配置详情 (编辑模式)
const fetchUserConfigDetail = async () => {
    if (!isEdit.value) return
    loading.value = true
    try {
        const configId = route.params.id
        const configData = await userConfigStore.fetchConfigDetail(configId)
        if (configData) {
            formData.value = { ...configData }
            // 如果有商户ID，获取对应的部门列表
            if (configData.merchantId) {
                await fetchDepartments(configData.merchantId)
            }
        } else {
            ElMessage.error('获取配置详情失败')
            router.push('/walmart/user') // 获取失败返回列表页
        }
    } catch (error) {
        console.error('获取配置详情失败', error)
        ElMessage.error('获取配置详情失败')
        router.push('/walmart/user')
    } finally {
        loading.value = false
    }
}

// 提交表单
const handleSubmit = async () => {
    if (!formRef.value) return

    await formRef.value.validate(async (valid) => {
        if (valid) {
            submitting.value = true
            try {
                if (isBatchMode.value) {
                    // 批量模式
                    await handleBatchSubmit()
                } else {
                    // 单个模式
                    await handleSingleSubmit()
                }
            } catch (error) {
                console.error('操作失败', error)
                // ElMessage.error('操作失败') // Store中可能已有错误提示
            } finally {
                submitting.value = false
            }
        }
    })
}

// 单个模式提交
const handleSingleSubmit = async () => {
    // 准备提交数据，转换字段名以匹配后端API
    const dataToSubmit = {
        sign: formData.value.sign,
        merchant_id: formData.value.merchantId,
        department_id: formData.value.departmentId,
        daily_limit: formData.value.dailyLimit,
        hourly_limit: formData.value.hourlyLimit,
        active: formData.value.active,
        description: formData.value.description
    };

    let success = false;
    if (isEdit.value) {
        success = await userConfigStore.updateConfig(formData.value.id, dataToSubmit)
    } else {
        success = await userConfigStore.createConfig(dataToSubmit)
    }

    if (success) {
        ElMessage.success(isEdit.value ? '更新成功' : '新增成功')
        router.push({ path: '/walmart/user', query: { refresh: Date.now() } }) // 返回列表并刷新
    }
}

// 生成格式化的备注
const generateFormattedDescription = (baseDescription, index, timestamp) => {
    // 如果用户备注为空，使用默认备注
    const description = baseDescription?.trim() || 'CK'

    // 生成时间戳：YYYYMMDDHHMM格式
    const timeStr = timestamp.toISOString().replace(/[-:T]/g, '').slice(0, 12)

    // 生成序号（从1开始）
    const sequence = index + 1

    // 返回格式化的备注：{备注}-{时间戳}-{序号}
    return `${description}-${timeStr}-${sequence}`
}

// 批量模式提交
const handleBatchSubmit = async () => {
    // 验证批量数据
    if (batchValidation.value.invalidSigns.length > 0) {
        ElMessage.error('存在格式错误的CK签名，请修正后再提交')
        return
    }

    if (batchValidation.value.duplicateSigns.length > 0) {
        ElMessage.error('存在重复的CK签名，请修正后再提交')
        return
    }

    if (batchValidation.value.validSigns.length === 0) {
        ElMessage.error('请至少输入一个有效的CK签名')
        return
    }

    if (batchValidation.value.validSigns.length > 100) {
        ElMessage.error('单次最多支持创建100个CK')
        return
    }

    // 生成当前时间戳（用于所有CK的备注格式化）
    const currentTime = new Date()

    // 为每个CK生成格式化的备注
    const signsWithFormattedDescriptions = batchValidation.value.validSigns.map((sign, index) => ({
        sign: sign,
        description: generateFormattedDescription(batchData.value.description, index, currentTime)
    }))

    // 准备批量提交数据
    const batchDataToSubmit = {
        signs: signsWithFormattedDescriptions.map(item => item.sign),
        descriptions: signsWithFormattedDescriptions.map(item => item.description), // 新增：每个CK的独立备注
        merchant_id: batchData.value.merchantId,
        department_id: batchData.value.departmentId,
        daily_limit: batchData.value.dailyLimit,
        hourly_limit: batchData.value.hourlyLimit,
        active: batchData.value.active,
        base_description: batchData.value.description // 保留原始备注用于显示
    };

    const result = await userConfigStore.batchCreateConfig(batchDataToSubmit)

    if (result.success) {
        const data = result.data
        ElMessage.success(`批量创建成功！成功创建${data.success_count}个CK`)

        // 如果有失败的项目，显示详细信息
        if (data.failed_count > 0) {
            ElMessage.warning(`${data.failed_count}个CK创建失败，请检查日志`)
        }

        router.push({ path: '/walmart/user', query: { refresh: Date.now() } }) // 返回列表并刷新
    } else {
        ElMessage.error(result.message || '批量创建失败')
    }
}

// 批量验证CK签名
const validateBatchSigns = () => {
    if (!batchData.value.signs) {
        batchValidation.value = {
            validSigns: [],
            invalidSigns: [],
            duplicateSigns: [],
            totalCount: 0
        }
        return
    }

    const lines = batchData.value.signs.split('\n')
    const validSigns = []
    const invalidSigns = []
    const duplicateSigns = []
    const seenSigns = new Set()

    // CK格式验证正则表达式
    const pattern = /^[a-zA-Z0-9]+@[a-zA-Z0-9]+#[A-Za-z0-9+/=×\-_]+#\d+$/

    lines.forEach((line, index) => {
        const sign = line.trim()
        if (!sign) return // 跳过空行

        // 检查格式
        if (!pattern.test(sign)) {
            invalidSigns.push(`第${index + 1}行: ${sign}`)
            return
        }

        // 检查重复
        if (seenSigns.has(sign)) {
            duplicateSigns.push(`第${index + 1}行: ${sign}`)
            return
        }

        seenSigns.add(sign)
        validSigns.push(sign)
    })

    batchValidation.value = {
        validSigns,
        invalidSigns,
        duplicateSigns,
        totalCount: validSigns.length + invalidSigns.length + duplicateSigns.length
    }
}

// 生成备注预览
const generateDescriptionPreview = () => {
    if (!batchValidation.value.validSigns.length) {
        descriptionPreview.value.items = []
        return
    }

    const currentTime = new Date()
    const previewItems = batchValidation.value.validSigns.slice(0, 5).map((sign, index) => ({
        sign: sign.substring(0, 20) + (sign.length > 20 ? '...' : ''), // 截断显示
        description: generateFormattedDescription(batchData.value.description, index, currentTime)
    }))

    descriptionPreview.value.items = previewItems
}

// 监听批量签名变化，实时验证
watch(() => batchData.value.signs, () => {
    validateBatchSigns()
    generateDescriptionPreview()
}, { immediate: true })

// 监听备注变化，更新预览
watch(() => batchData.value.description, generateDescriptionPreview)

// 切换批量模式
const toggleBatchMode = () => {
    if (isEdit.value) {
        ElMessage.warning('编辑模式下不支持批量操作')
        return
    }

    // 设置标志，防止watch监听器重复触发
    isUpdatingFromMerchantSwitch.value = true

    try {
        const oldMode = isBatchMode.value
        // 切换模式状态
        isBatchMode.value = !isBatchMode.value

        console.log('模式切换:', {
            oldMode,
            newMode: isBatchMode.value,
            fromSingle: !isBatchMode.value,
            toBatch: isBatchMode.value
        })

        if (isBatchMode.value) {
            // 切换到批量模式，同步单个模式的配置到批量模式
            batchData.value.merchantId = formData.value.merchantId
            batchData.value.departmentId = formData.value.departmentId
            batchData.value.dailyLimit = formData.value.dailyLimit
            batchData.value.hourlyLimit = formData.value.hourlyLimit
            batchData.value.active = formData.value.active
            batchData.value.description = formData.value.description

            console.log('切换到批量模式，同步数据:', {
                merchantId: batchData.value.merchantId,
                departmentId: batchData.value.departmentId
            })
        } else {
            // 切换到单个模式，同步批量模式的配置到单个模式
            formData.value.merchantId = batchData.value.merchantId
            formData.value.departmentId = batchData.value.departmentId
            formData.value.dailyLimit = batchData.value.dailyLimit
            formData.value.hourlyLimit = batchData.value.hourlyLimit
            formData.value.active = batchData.value.active
            formData.value.description = batchData.value.description

            console.log('切换到单个模式，同步数据:', {
                merchantId: formData.value.merchantId,
                departmentId: formData.value.departmentId
            })
        }

        // 等待DOM更新后清除表单验证错误
        setTimeout(() => {
            if (formRef.value) {
                formRef.value.clearValidate()
                console.log('已清除表单验证错误')
            }
        }, 50)

    } finally {
        // 重置标志
        setTimeout(() => {
            isUpdatingFromMerchantSwitch.value = false
            console.log('重置商户切换标志')
        }, 100)
    }
}

// 隐藏CK签名的中间部分
const maskCKSign = (sign) => {
    if (!sign) return ''

    // CK格式：用户名@域名#密钥#版本号
    const parts = sign.split('#')
    if (parts.length !== 3) return sign // 格式不正确，直接返回原值

    const [userDomain, key, version] = parts

    // 隐藏密钥部分的中间内容，只显示前3位和后3位
    let maskedKey = key
    if (key.length > 6) {
        maskedKey = key.substring(0, 3) + '***' + key.substring(key.length - 3)
    }

    // 隐藏用户名@域名部分的中间内容
    let maskedUserDomain = userDomain
    if (userDomain.length > 10) {
        const atIndex = userDomain.indexOf('@')
        if (atIndex > 0) {
            const username = userDomain.substring(0, atIndex)
            const domain = userDomain.substring(atIndex + 1)

            // 隐藏用户名中间部分
            let maskedUsername = username
            if (username.length > 6) {
                maskedUsername = username.substring(0, 3) + '***' + username.substring(username.length - 3)
            }

            // 隐藏域名中间部分
            let maskedDomain = domain
            if (domain.length > 6) {
                maskedDomain = domain.substring(0, 3) + '***' + domain.substring(domain.length - 3)
            }

            maskedUserDomain = maskedUsername + '@' + maskedDomain
        }
    }

    return `${maskedUserDomain}#${maskedKey}#${version}`
}

// 取消
const handleCancel = () => {
    router.push('/walmart/user')
}

// Redis同步功能已移除 - SimplifiedCKService使用数据库直连，无需缓存同步

onMounted(async () => {
    // 注册商户切换监听器
    unregisterMerchantSwitch = merchantSwitchListener.registerRefreshFunction(handleMerchantSwitch)

    // 设置初始化标志，防止watch监听器重复调用
    isUpdatingFromMerchantSwitch.value = true

    try {
        // 如果是超级管理员，获取商户列表供选择
        if (userStore.isSuperAdmin) {
            await fetchMerchants()

            // 如果当前有选择的商户，设置到表单中
            if (merchantStore.currentMerchant && merchantStore.currentMerchant.id) {
                formData.value.merchantId = merchantStore.currentMerchant.id
                batchData.value.merchantId = merchantStore.currentMerchant.id
                await fetchDepartments(merchantStore.currentMerchant.id)
            }
        } else {
            // 非超级管理员，自动设置当前用户的商户ID
            formData.value.merchantId = userStore.merchantId
            batchData.value.merchantId = userStore.merchantId

            // 智能部门预填充逻辑
            await handleSmartDepartmentPreFill()

            if (formData.value.merchantId) {
                await fetchDepartments(formData.value.merchantId)
            }
        }

        // 获取配置详情（编辑模式）
        await fetchUserConfigDetail()
    } finally {
        // 重置标志，允许后续的watch监听器正常工作
        isUpdatingFromMerchantSwitch.value = false
    }
})

onUnmounted(() => {
    // 取消注册商户切换监听器
    if (unregisterMerchantSwitch) {
        unregisterMerchantSwitch()
    }
})
</script>

<template>
    <div class="page-container user-config-form-container">
        <el-card shadow="never">
            <template #header>
                <div class="card-header">
                    <div class="header-left">
                        <span class="header-title">{{ pageTitle }}</span>
                        <!-- 批量模式切换开关 -->
                        <el-switch v-if="!isEdit" :model-value="isBatchMode" @change="toggleBatchMode" inline-prompt
                            active-text="批量" inactive-text="单个" style="margin-left: 20px;" />
                    </div>
                    <el-button @click="handleCancel" :icon="Close">关闭</el-button>
                </div>
            </template>
            <el-scrollbar style="height: calc(100vh - 210px);"> <!-- Adjust height as needed -->
                <div class="form-content">
                    <el-form ref="formRef" :model="currentFormData" :rules="rules" label-position="top"
                        class="user-config-form" v-loading="loading" label-width="140px">

                        <!-- 商户和部门选择 -->
                        <el-divider content-position="left">商户与部门</el-divider>
                        <el-row :gutter="30">
                            <!-- 商户选择：根据用户类型和状态显示不同的组件 -->
                            <el-col :xs="24" :sm="12">
                                <el-form-item label="选择商户" prop="merchantId">
                                    <!-- 超级管理员且未通过右上角选择商户时：显示下拉选择框 -->
                                    <el-select v-if="userStore.isSuperAdmin && !isMerchantReadonly"
                                        v-model="currentFormData.merchantId" placeholder="请选择商户" style="width: 100%"
                                        :loading="loadingMerchants" clearable>
                                        <el-option v-for="merchant in merchants" :key="merchant.id"
                                            :label="merchant.name" :value="merchant.id" />
                                    </el-select>
                                    <!-- 超级管理员已通过右上角选择商户或非超级管理员：显示只读输入框 -->
                                    <el-input v-else :value="currentMerchantName" readonly style="width: 100%" />
                                    <div class="el-form-item__info">
                                        <span v-if="isEdit">
                                            编辑模式下不允许修改商户
                                        </span>
                                        <span v-else-if="userStore.isSuperAdmin && isMerchantReadonly">
                                            已通过右上角商户切换选择，如需更改请使用右上角切换功能
                                        </span>
                                        <span v-else-if="!userStore.isSuperAdmin">
                                            当前用户所属商户
                                        </span>
                                        <span v-else>
                                            请选择商户或使用右上角商户切换功能
                                        </span>
                                    </div>
                                </el-form-item>
                            </el-col>
                            <!-- 部门选择 -->
                            <el-col :xs="24" :sm="12">
                                <el-form-item label="选择部门" prop="departmentId">
                                    <!-- 添加模式：显示下拉选择框（超级管理员和商户管理员都可以选择） -->
                                    <el-select v-if="!isDepartmentReadonly" v-model="currentFormData.departmentId"
                                        placeholder="请选择部门" style="width: 100%" :loading="loadingDepartments"
                                        :disabled="!currentFormData.merchantId" clearable>
                                        <el-option v-for="department in departments" :key="department.id"
                                            :label="department.name" :value="department.id" />
                                    </el-select>
                                    <!-- 编辑模式：显示只读输入框 -->
                                    <el-input v-else :value="currentDepartmentName" readonly style="width: 100%" />
                                    <div class="el-form-item__info">
                                        <span v-if="isEdit">
                                            编辑模式下不允许修改部门
                                        </span>
                                        <span v-else-if="!currentFormData.merchantId">
                                            请先选择商户
                                        </span>
                                        <span v-else-if="loadingDepartments">
                                            正在加载部门列表...
                                        </span>
                                        <span v-else-if="departments.length === 0 && !userStore.isSuperAdmin">
                                            已自动选择您的当前部门，如需更改请联系管理员
                                        </span>
                                        <span v-else-if="departments.length === 0">
                                            该商户下暂无部门或您没有部门查询权限
                                        </span>
                                        <span v-else>
                                            请选择部门
                                        </span>
                                    </div>
                                </el-form-item>
                            </el-col>
                        </el-row>


                        <el-row :gutter="30">
                            <el-col :span="24">
                                <el-form-item label="备注" prop="description">
                                    <el-input v-model="currentFormData.description" type="textarea" :rows="1"
                                        placeholder="请输入备注信息，方便识别用途" />
                                    <!-- 批量模式下显示备注格式说明 -->
                                    <div v-if="isBatchMode" class="el-form-item__info">
                                        <div style="margin-top: 8px;">
                                            <el-icon style="margin-right: 4px;">
                                                <InfoFilled />
                                            </el-icon>
                                            批量模式下，系统将自动为每个CK的备注添加时间戳和序号后缀
                                        </div>
                                        <div style="margin-top: 4px; color: #909399; font-size: 11px;">
                                            格式：{您的备注}-{时间戳YYYYMMDDHHMM}-{序号}
                                        </div>
                                    </div>
                                </el-form-item>
                            </el-col>
                        </el-row>

                        <!-- CK签名输入 -->
                        <el-divider content-position="left">CK配置</el-divider>

                        <!-- 单个模式：单个CK签名输入 -->
                        <el-row v-if="!isBatchMode" :gutter="30">
                            <el-col :span="24">
                                <el-form-item label="用户签名 (Sign)" prop="sign">
                                    <!-- 编辑模式：显示隐藏中间部分的CK -->
                                    <el-input v-if="isEdit" :value="maskCKSign(formData.sign)" type="textarea"
                                        :autosize="{ minRows: 3, maxRows: 5 }" readonly placeholder="编辑模式下CK不允许编辑" />
                                    <!-- 新增模式：正常输入 -->
                                    <el-input v-else v-model="formData.sign" type="textarea"
                                        :autosize="{ minRows: 3, maxRows: 5 }" placeholder="请输入用户签名，格式：沃尔玛CK#微信CK#数字版本号"
                                        clearable />
                                    <div class="el-form-item__info">
                                        <span v-if="isEdit">
                                            编辑模式下CK签名不允许修改，中间部分已隐藏保护
                                        </span>
                                        <span v-else>
                                            签名是识别用户的唯一凭证，格式为：沃尔玛CK#微信CK#数字版本号，例如：141b58191157469d93fb4226edd62cb2@9878461c8196d9227725861e14c887b6#gKR+×XhHSE2H516EYqv/pA==#975
                                        </span>
                                    </div>
                                </el-form-item>
                            </el-col>
                        </el-row>

                        <!-- 批量模式：多个CK签名输入 -->
                        <el-row v-if="isBatchMode" :gutter="30">
                            <el-col :span="24">
                                <el-form-item label="批量CK签名" prop="signs">
                                    <el-input v-model="batchData.signs" type="textarea"
                                        :autosize="{ minRows: 8, maxRows: 15 }"
                                        placeholder="请输入多个CK签名，每行一个，格式：沃尔玛CK#微信CK#数字版本号" clearable />
                                    <div class="el-form-item__info">
                                        <div>每行输入一个CK签名，最多支持1000个。格式：沃尔玛CK#微信CK#数字版本号</div>
                                        <div style="margin-top: 8px;">
                                            <el-tag v-if="batchValidation.validSigns.length > 0" type="success"
                                                size="small">
                                                有效: {{ batchValidation.validSigns.length }}
                                            </el-tag>
                                            <el-tag v-if="batchValidation.invalidSigns.length > 0" type="danger"
                                                size="small" style="margin-left: 8px;">
                                                格式错误: {{ batchValidation.invalidSigns.length }}
                                            </el-tag>
                                            <el-tag v-if="batchValidation.duplicateSigns.length > 0" type="warning"
                                                size="small" style="margin-left: 8px;">
                                                重复: {{ batchValidation.duplicateSigns.length }}
                                            </el-tag>
                                        </div>
                                        <!-- 显示错误详情 -->
                                        <div v-if="batchValidation.invalidSigns.length > 0"
                                            style="margin-top: 8px; color: #f56c6c;">
                                            <div style="font-weight: bold;">格式错误的CK签名：</div>
                                            <div v-for="sign in batchValidation.invalidSigns.slice(0, 5)" :key="sign"
                                                style="font-size: 12px;">
                                                {{ sign }}
                                            </div>
                                            <div v-if="batchValidation.invalidSigns.length > 5"
                                                style="font-size: 12px;">
                                                ...还有{{ batchValidation.invalidSigns.length - 5 }}个
                                            </div>
                                        </div>
                                        <div v-if="batchValidation.duplicateSigns.length > 0"
                                            style="margin-top: 8px; color: #e6a23c;">
                                            <div style="font-weight: bold;">重复的CK签名：</div>
                                            <div v-for="sign in batchValidation.duplicateSigns.slice(0, 5)" :key="sign"
                                                style="font-size: 12px;">
                                                {{ sign }}
                                            </div>
                                            <div v-if="batchValidation.duplicateSigns.length > 5"
                                                style="font-size: 12px;">
                                                ...还有{{ batchValidation.duplicateSigns.length - 5 }}个
                                            </div>
                                        </div>
                                        <!-- 备注格式预览 -->
                                        <div v-if="batchValidation.validSigns.length > 0 && descriptionPreview.items.length > 0"
                                            style="margin-top: 12px; padding: 12px; background-color: #f5f7fa; border-radius: 4px;">
                                            <div style="font-weight: bold; color: #606266; margin-bottom: 8px;">
                                                <el-icon style="margin-right: 4px;">
                                                    <View />
                                                </el-icon>
                                                备注格式预览（前5个）：
                                            </div>
                                            <div v-for="(item, index) in descriptionPreview.items" :key="index"
                                                style="font-size: 12px; margin-bottom: 4px; color: #606266;">
                                                <span style="color: #909399;">{{ index + 1 }}.</span>
                                                <span style="color: #67c23a; font-weight: 500;">{{ item.description
                                                    }}</span>
                                            </div>
                                            <div v-if="batchValidation.validSigns.length > 5"
                                                style="font-size: 12px; color: #909399; margin-top: 4px;">
                                                ...还有{{ batchValidation.validSigns.length - 5 }}个CK将使用相同格式
                                            </div>
                                        </div>
                                    </div>
                                </el-form-item>
                            </el-col>
                        </el-row>
                        <el-divider content-position="left">限制与状态</el-divider>

                        <el-row :gutter="30">
                            <el-col :xs="24" :sm="12" :md="8">
                                <el-form-item label="总绑卡次数限制" prop="totalLimit">
                                    <el-input-number v-model="currentFormData.totalLimit" :min="1" :max="10000"
                                        controls-position="right" style="width: 100%;" placeholder="设置总绑卡次数限制" />
                                </el-form-item>
                            </el-col>
                            <el-col :xs="24" :sm="12" :md="8">
                                <el-form-item label="状态" prop="active">
                                    <el-switch v-model="currentFormData.active" inline-prompt
                                        style="--el-switch-on-color: #13ce66; --el-switch-off-color: #ff4949"
                                        active-text="启用" inactive-text="禁用" :active-icon="Check"
                                        :inactive-icon="Close" />
                                </el-form-item>
                            </el-col>
                        </el-row>



                        <el-divider />

                        <div class="form-footer">
                            <el-button type="primary" @click="handleSubmit" :loading="submitting"
                                :icon="Check">保存配置</el-button>
                            <!-- Redis同步功能已移除 - SimplifiedCKService使用数据库直连 -->
                            <el-button @click="handleCancel" :icon="Close">取消</el-button>
                        </div>
                    </el-form>
                </div>
            </el-scrollbar>
        </el-card>
    </div>
</template>

<style scoped>
.page-container {
    padding: 20px;
}

.card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.header-left {
    display: flex;
    align-items: center;
}

.header-title {
    font-size: 1.2rem;
    font-weight: 600;
    color: var(--el-text-color-primary);
}

.form-content {
    padding: 10px 20px 20px 10px;
    /* Add padding inside scrollbar */
}

.user-config-form .el-form-item {
    margin-bottom: 24px;
}

.el-form-item__info {
    color: var(--el-text-color-secondary);
    font-size: 12px;
    line-height: 1.5;
    margin-top: 4px;
}

.form-footer {
    margin-top: 30px;
    padding-top: 20px;
    border-top: 1px solid var(--el-border-color-lighter);
    display: flex;
    justify-content: flex-end;
    gap: 15px;
}

.el-divider--horizontal {
    margin: 28px 0;
}

/* Center align switch in its column */
.el-form-item__content>.el-switch {
    margin-top: 8px;
    /* Adjust vertical alignment */
}
</style>