import os
import secrets
import yaml
import urllib.parse
from typing import Any, Dict, List, Optional, Union
from pydantic import EmailStr, field_validator, ValidationInfo
from pydantic_settings import BaseSettings


def load_config() -> Dict[str, Any]:
    """从config.yaml加载配置"""
    # 优先读取当前工作目录的config.yaml（Docker挂载的外部配置）
    config_paths = [
        "config.yaml",  # 当前工作目录（Docker挂载）
        os.path.join(os.getcwd(), "config.yaml"),  # 明确的当前目录
        os.path.join(
            os.path.dirname(os.path.dirname(os.path.dirname(__file__))), "config.yaml"
        ),  # 原始路径（开发环境）
    ]

    for config_path in config_paths:
        try:
            if os.path.exists(config_path):
                print(f"正在加载配置文件: {config_path}")
                with open(config_path, "r", encoding="utf-8") as f:
                    config = yaml.safe_load(f)
                    print(f"配置加载成功，business.binding.enabled = {config.get('business', {}).get('binding', {}).get('enabled', 'NOT_SET')}")
                    return config
        except Exception as e:
            print(f"无法加载配置文件 {config_path}: {e}")
            continue

    print("警告：所有配置文件路径都无法加载，使用空配置")
    return {}


# 加载YAML配置
yaml_config = load_config()


def load_or_generate_secret_key() -> str:
    """加载或生成SECRET_KEY，并确保其在重启后保持一致"""
    # 首先检查环境变量
    if os.environ.get("SECRET_KEY"):
        return os.environ.get("SECRET_KEY")

    # 其次检查配置文件
    if yaml_config.get("api", {}).get("secret_key"):
        return yaml_config.get("api", {}).get("secret_key")

    # 最后尝试从文件加载或生成新的密钥
    secret_key_file = os.path.join(
        os.path.dirname(os.path.dirname(os.path.dirname(__file__))), ".secret_key"
    )

    try:
        # 尝试从文件读取
        if os.path.exists(secret_key_file):
            with open(secret_key_file, "r") as f:
                return f.read().strip()

        # 文件不存在，生成新密钥并保存
        new_key = secrets.token_urlsafe(32)
        with open(secret_key_file, "w") as f:
            f.write(new_key)
        return new_key
    except Exception as e:
        import logging

        logging.getLogger("app.core.config").error(
            f"无法加载或保存SECRET_KEY: {e}，将使用随机生成的密钥（重启后令牌将失效）",
            exc_info=True,
        )
        return secrets.token_urlsafe(32)


class Settings(BaseSettings):
    # 项目名称
    PROJECT_NAME: str = yaml_config.get("api", {}).get(
        "project_name", "Walmart Bind Card API"
    )

    API_V1_STR: str = yaml_config.get("api", {}).get("v1_str", "/api/v1")

    # 使用持久化的SECRET_KEY
    SECRET_KEY: str = load_or_generate_secret_key()

    @field_validator("SECRET_KEY", mode="before")
    def log_secret_key_source(cls, v: str) -> str:
        import logging

        logger = logging.getLogger("app.core.config")
        if os.environ.get("SECRET_KEY"):
            logger.info("使用环境变量中的SECRET_KEY")
        elif yaml_config.get("api", {}).get("secret_key"):
            logger.info("使用配置文件中的SECRET_KEY")
        elif os.path.exists(
            os.path.join(
                os.path.dirname(os.path.dirname(os.path.dirname(__file__))),
                ".secret_key",
            )
        ):
            logger.info("使用持久化文件中的SECRET_KEY")
        else:
            logger.warning("使用随机生成的SECRET_KEY，这可能导致重启后令牌失效")
        return v

    ACCESS_TOKEN_EXPIRE_MINUTES: int = yaml_config.get("api", {}).get(
        "access_token_expire_minutes", 60 * 24 * 8
    )
    REFRESH_TOKEN_EXPIRE_DAYS: int = yaml_config.get("api", {}).get(
        "refresh_token_expire_days", 30
    )
    ALGORITHM: str = yaml_config.get("algorithm", "HS256")

    # CORS配置
    BACKEND_CORS_ORIGINS: List[str] = yaml_config.get("cors", {}).get("origins", ["*"])
    CORS_ALLOW_CREDENTIALS: bool = yaml_config.get("cors", {}).get(
        "allow_credentials", True
    )
    CORS_ALLOW_METHODS: List[str] = yaml_config.get("cors", {}).get(
        "allow_methods", ["*"]
    )
    CORS_ALLOW_HEADERS: List[str] = yaml_config.get("cors", {}).get(
        "allow_headers", ["*"]
    )

    @field_validator("BACKEND_CORS_ORIGINS", mode="before")
    def assemble_cors_origins(cls, v: Union[str, List[str]]) -> Union[List[str], str]:
        if isinstance(v, str) and not v.startswith("["):
            return [i.strip() for i in v.split(",")]
        elif isinstance(v, (list, str)):
            return v
        raise ValueError(v)

    # 数据库配置
    DB_TYPE: str = yaml_config.get("database", {}).get("type", "mysql")
    DB_HOST: str = yaml_config.get("database", {}).get("host", "db")
    DB_PORT: int = yaml_config.get("database", {}).get("port", 3306)
    DB_USER: str = yaml_config.get("database", {}).get("user", "root")
    DB_PASSWORD: str = yaml_config.get("database", {}).get(
        "password", "7c222fb2927d828af22f592134e8932480637c0d"
    )
    DB_NAME: str = yaml_config.get("database", {}).get("db_name", "walmart_card_db")
    SQLALCHEMY_DATABASE_URI: Optional[str] = None

    @field_validator("SQLALCHEMY_DATABASE_URI", mode="before")
    def assemble_db_connection(cls, v: Optional[str], info: ValidationInfo) -> Any:
        if isinstance(v, str):
            return v

        # 从 info.data 中获取值
        values = info.data
        db_type = values.get("DB_TYPE", "mysql")

        if db_type == "mysql":
            # 添加字符集参数解决中文乱码问题（移除collation参数，aiomysql不支持）
            return f"mysql+pymysql://{values.get('DB_USER')}:{values.get('DB_PASSWORD')}@{values.get('DB_HOST')}:{values.get('DB_PORT')}/{values.get('DB_NAME')}?charset=utf8mb4"
        elif db_type == "postgresql":
            return f"postgresql://{values.get('DB_USER')}:{values.get('DB_PASSWORD')}@{values.get('DB_HOST')}:{values.get('DB_PORT')}/{values.get('DB_NAME')}"
        else:
            raise ValueError(f"不支持的数据库类型: {db_type}")

    # Redis配置
    REDIS_HOST: str = yaml_config.get("redis", {}).get("host", "redis")
    REDIS_PORT: int = yaml_config.get("redis", {}).get("port", 6379)
    REDIS_DB: int = yaml_config.get("redis", {}).get("db", 3)
    REDIS_PASSWORD: Optional[str] = yaml_config.get("redis", {}).get(
        "password", "7c222fb2927d828af22f592134e8932480637c0d"
    )

    # 队列配置
    RABBITMQ_HOST: str = yaml_config.get("rabbitmq", {}).get("host", "rabbitmq")
    RABBITMQ_PORT: int = yaml_config.get("rabbitmq", {}).get("port", 5672)
    RABBITMQ_USER: str = yaml_config.get("rabbitmq", {}).get("user", "walmart_card")
    RABBITMQ_PASS: str = yaml_config.get("rabbitmq", {}).get(
        "password", "7c222fb2927d828af22f592134e8932480637c0d"
    )
    RABBITMQ_VHOST: str = yaml_config.get("rabbitmq", {}).get("vhost", "/walmart_card")
    RABBITMQ_QUEUE_BIND_CARD: str = "bind_card_queue"
    RABBITMQ_QUEUE_CALLBACK: str = "bind_card_callback_queue"
    RABBITMQ_CONSUMER_PREFETCH_COUNT: int = yaml_config.get("rabbitmq", {}).get(
        "consumer_prefetch_count", 50
    )
    RABBITMQ_CALLBACK_CONSUMER_PREFETCH_COUNT: int = yaml_config.get("rabbitmq", {}).get(
        "callback_consumer_prefetch_count", 100
    )

    @property
    def RABBITMQ_URL(self) -> str:
        vhost = urllib.parse.quote(self.RABBITMQ_VHOST, safe="")
        return f"amqp://{self.RABBITMQ_USER}:{self.RABBITMQ_PASS}@{self.RABBITMQ_HOST}:{self.RABBITMQ_PORT}/{vhost}"

    # 邮件配置
    SMTP_TLS: bool = yaml_config.get("email", {}).get("smtp_tls", True)
    SMTP_PORT: Optional[int] = yaml_config.get("email", {}).get("smtp_port")
    SMTP_HOST: Optional[str] = yaml_config.get("email", {}).get("smtp_host")
    SMTP_USER: Optional[str] = yaml_config.get("email", {}).get("smtp_user")
    SMTP_PASSWORD: Optional[str] = yaml_config.get("email", {}).get("smtp_password")
    EMAILS_FROM_EMAIL: Optional[EmailStr] = yaml_config.get("email", {}).get(
        "from_email"
    )
    EMAILS_FROM_NAME: Optional[str] = yaml_config.get("email", {}).get("from_name")

    # 管理员配置
    FIRST_SUPERUSER: str = yaml_config.get("admin", {}).get("first_superuser", "admin")
    FIRST_SUPERUSER_PASSWORD: str = yaml_config.get("admin", {}).get(
        "first_superuser_password", "7c222fb2927d828af22f592134e8932480637c0d"
    )
    FIRST_SUPERUSER_EMAIL: EmailStr = yaml_config.get("admin", {}).get(
        "first_superuser_email", "<EMAIL>"
    )

    # API限流配置
    RATE_LIMIT_ENABLED: bool = yaml_config.get("rate_limit", {}).get("enabled", True)
    RATE_LIMIT_PER_MINUTE: int = yaml_config.get("rate_limit", {}).get("per_minute", 60)

    # 绑卡配置
    MAX_CONCURRENCY: int = yaml_config.get("binding", {}).get("max_concurrency", 100)
    WALMART_API_TIMEOUT: int = yaml_config.get("binding", {}).get(
        "walmart_api_timeout", 30
    )

    # 允许的主机配置
    ALLOWED_HOSTS: List[str] = yaml_config.get("security", {}).get(
        "allowed_hosts", ["*"]
    )

    # 双因子认证配置
    TOTP_ENCRYPTION_KEY: str = yaml_config.get("totp", {}).get(
        "encryption_key", "fernet_key_for_totp_encryption_32_chars"
    )
    TOTP_ISSUER_NAME: str = yaml_config.get("totp", {}).get(
        "issuer_name", "沃尔玛绑卡系统"
    )

    # Redis CK优化配置
    ENABLE_REDIS_CK_OPTIMIZATION: bool = yaml_config.get("ck_optimization", {}).get(
        "enable_redis", True
    )
    CK_REDIS_FALLBACK_TO_DB: bool = yaml_config.get("ck_optimization", {}).get(
        "fallback_to_db", True
    )
    REDIS_MAX_CONNECTIONS: int = yaml_config.get("ck_optimization", {}).get(
        "max_connections", 50
    )
    # 是否启用绑卡业务（默认启用）
    BUSINESS_BINDING_ENABLED: bool = yaml_config.get("business", {}).get("binding", {}).get("enabled", True)
    # 是否启用回调业务（默认启用）
    BUSINESS_CALLBACK_ENABLED: bool = yaml_config.get("business", {}).get("callback", {}).get("enabled", True)

    # Pydantic V2 使用 model_config 替代旧版的 Config 类
    model_config = {
        "case_sensitive": True,
        "env_file": ".env",
        "extra": "ignore",  # 允许额外的环境变量
    }

    # 添加yaml_config属性以便其他模块访问
    @property
    def yaml_config(self) -> Dict[str, Any]:
        """获取YAML配置"""
        return yaml_config




settings = Settings()
