from sqlalchemy import (
    Column,
    String,
    Integer,
    BigInteger,
    Boolean,
    Text,
    ForeignKey,
    Enum,
)
from sqlalchemy.orm import relationship


from app.models.base import BaseModel, TimestampMixin
from app.models.associations import menu_permissions, role_menus


# 移除枚举定义，改用常量
class MenuTypeConstants:
    """菜单类型常量"""

    MENU = "MENU"
    BUTTON = "BUTTON"
    LINK = "LINK"


class Menu(BaseModel, TimestampMixin):
    """菜单模型 - 与数据库表结构一致"""

    __tablename__ = "menus"

    # 基本信息
    name = Column(String(100), nullable=False, index=True, comment="菜单名称")
    code = Column(
        String(50), nullable=False, unique=True, index=True, comment="菜单代码"
    )
    path = Column(String(200), nullable=True, comment="菜单路径")
    component = Column(String(200), nullable=True, comment="组件路径")
    icon = Column(String(100), nullable=True, comment="图标")

    # 父菜单和层级
    parent_id = Column(
        BigInteger, ForeignKey("menus.id"), nullable=True, comment="父菜单ID"
    )
    level = Column(Integer, nullable=False, default=1, comment="菜单层级")

    # 排序
    sort_order = Column(Integer, default=0, nullable=False, comment="排序号")

    # 菜单状态
    is_visible = Column(Boolean, default=True, nullable=False, comment="是否可见")
    is_enabled = Column(Boolean, default=True, nullable=False, comment="是否启用")

    # 菜单类型 - 修改为字符串类型
    menu_type = Column(
        String(20), nullable=False, default=MenuTypeConstants.MENU, comment="菜单类型"
    )

    # 菜单描述
    description = Column(Text, nullable=True, comment="菜单描述")
    children = relationship(
        "Menu", back_populates="parent", cascade="all, delete-orphan"
    )
    parent = None  # 将在类定义后设置

    # 权限关联
    permissions = relationship(
        "Permission", secondary=menu_permissions, back_populates="menus"
    )

    # 角色关联
    roles = relationship("Role", secondary=role_menus, back_populates="menus")

    def to_dict(self, include_children=True):
        """转换为字典

        Args:
            include_children: 是否包含子菜单

        Returns:
            dict: 菜单字典
        """
        data = super().to_dict()

        # 添加子菜单
        if include_children and self.children:
            data["children"] = [child.to_dict() for child in self.children]

        return data

    def get_tree(self) -> dict:
        """获取菜单树结构

        Returns:
            dict: 菜单树结构
        """
        result = {
            "id": self.id,
            "name": self.name,
            "code": self.code,
            "path": self.path,
            "component": self.component,
            "icon": self.icon,
            "menu_type": self.menu_type,
            "level": self.level,
            "is_visible": self.is_visible,
            "is_enabled": self.is_enabled,
            "sort_order": self.sort_order,
            "parent_id": self.parent_id,
            "description": self.description,
            "children": [],
        }

        if self.children:
            result["children"] = [
                child.get_tree()
                for child in sorted(self.children, key=lambda x: x.sort_order)
            ]

        return result


# 在类定义完成后设置parent关系
Menu.parent = relationship("Menu", back_populates="children", remote_side=[Menu.id])
