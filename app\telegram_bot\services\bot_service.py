"""
Telegram Bot 服务核心类
"""

import asyncio
from typing import Optional, Dict, Any
from telegram import Bot, Update
from telegram.ext import Application, ContextTypes
from sqlalchemy.orm import Session

from app.core.logging import get_logger
from app.db.session import SessionLocal
from ..config import get_bot_config, BotConfig
from ..webhook_handler import WebhookHandler
from ..command_handlers import CommandHandlerRegistry
from ..rate_limiter import RateLimiter
from ..exceptions import BotServiceError, ConfigurationError
from ..utils.simple_singleton import acquire_bot_singleton, release_bot_singleton, get_bot_singleton_info

logger = get_logger(__name__)


class TelegramBotService:
    """Telegram机器人服务主类"""

    def __init__(self):
        self.application: Optional[Application] = None
        self.webhook_handler: Optional[WebhookHandler] = None
        self.command_registry: Optional[CommandHandlerRegistry] = None
        self.rate_limiter: Optional[RateLimiter] = None
        self.config: Optional[BotConfig] = None
        self._running = False
        self._polling_mode = False  # 标记是否使用轮询模式
        self._polling_task: Optional[asyncio.Task] = None  # 轮询任务
        
    async def initialize(self) -> bool:
        """初始化机器人服务"""
        try:
            logger.info("正在初始化Telegram机器人服务...")
            
            # 获取数据库会话
            db = SessionLocal()
            try:
                # 加载统一配置
                from ..config import load_unified_config, validate_config, get_config_summary
                self.config = load_unified_config(db)

                # 验证配置（开发环境使用非严格模式）
                import os
                from app.core.config import yaml_config

                # 检查环境：优先环境变量，然后YAML配置，默认为production
                app_env = os.getenv("APP_ENV") or yaml_config.get("app", {}).get("env", "production")
                is_development = app_env.lower() == "development"

                if not validate_config(self.config, strict=not is_development):
                    if is_development:
                        logger.warning("机器人配置不完整，但在开发环境中继续运行")
                    else:
                        raise ConfigurationError("机器人配置验证失败")

                # 记录配置摘要
                config_summary = get_config_summary(self.config)
                logger.info(f"机器人配置摘要: {config_summary}")

                # 创建Bot应用（仅在有token时）
                if self.config.bot_token:
                    # 创建自定义的HTTPXRequest配置
                    request_config = self._create_request_config()

                    # 创建Application
                    builder = Application.builder().token(self.config.bot_token)
                    if request_config:
                        builder = builder.request(request_config)

                    self.application = builder.build()

                    # 添加全局错误处理器
                    self.application.add_error_handler(self._handle_global_error)

                    # 初始化组件
                    self.rate_limiter = RateLimiter(self.config)
                    self.command_registry = CommandHandlerRegistry(db, self.config, self.rate_limiter)
                    self.webhook_handler = WebhookHandler(self.application, self.config)

                    logger.info("Telegram Bot 应用创建成功")
                else:
                    logger.warning("Bot token 未配置，Telegram Bot 功能将不可用")
                    self.application = None
                    self.rate_limiter = None
                    self.command_registry = None
                    self.webhook_handler = None
                
                # 注册命令处理器
                await self._register_handlers()
                
                logger.info("Telegram机器人服务初始化完成")
                return True
                
            finally:
                db.close()
                
        except Exception as e:
            logger.error(f"初始化Telegram机器人服务失败: {e}", exc_info=True)
            return False

    def _create_request_config(self):
        """创建自定义的HTTPXRequest配置"""
        try:
            from telegram.request import HTTPXRequest

            # 创建HTTPXRequest配置
            request_kwargs = {
                "connection_pool_size": self.config.max_connections,
                "read_timeout": self.config.read_timeout,
                "write_timeout": self.config.write_timeout,
                "connect_timeout": self.config.connect_timeout,
                "pool_timeout": self.config.pool_timeout
            }

            # 添加代理配置
            if self.config.proxy_url:
                request_kwargs["proxy_url"] = self.config.proxy_url
                logger.info(f"配置代理: {self.config.proxy_url}")

            # 创建HTTPXRequest
            request_config = HTTPXRequest(**request_kwargs)

            logger.info("网络配置已应用到Telegram Bot")
            logger.info(f"- 连接超时: {self.config.connect_timeout}秒")
            logger.info(f"- 读取超时: {self.config.read_timeout}秒")
            logger.info(f"- 写入超时: {self.config.write_timeout}秒")
            logger.info(f"- 连接池大小: {self.config.max_connections}")

            return request_config

        except ImportError as e:
            logger.warning(f"无法导入HTTPXRequest，使用默认网络配置: {e}")
            return None
        except Exception as e:
            logger.error(f"创建网络配置失败，使用默认配置: {e}")
            return None
    
    async def _register_handlers(self):
        """注册命令处理器"""
        if not self.application or not self.command_registry:
            logger.warning("应用或命令注册器未初始化，跳过处理器注册")
            return

        # 注册所有命令处理器
        handlers = self.command_registry.get_all_handlers()
        for handler in handlers:
            self.application.add_handler(handler)

        logger.info(f"已注册 {len(handlers)} 个命令处理器")

    async def _start_polling(self):
        """启动轮询模式"""
        try:
            if not self.application or not self.application.updater:
                logger.error("应用或更新器未初始化，无法启动轮询")
                return False

            # 首先测试网络连接
            logger.info("测试Telegram API连接...")
            connection_test = await self.test_network_connection()
            if not connection_test:
                logger.error("网络连接测试失败，无法启动轮询模式")
                logger.info("建议:")
                logger.info("1. 检查网络连接")
                logger.info("2. 配置代理服务器（如果在中国大陆）")
                logger.info("3. 或者使用Webhook模式替代轮询模式")
                return False

            # 在启动轮询前，先清除任何现有的webhook
            try:
                logger.info("清除现有webhook以避免冲突...")
                await self.application.bot.delete_webhook(drop_pending_updates=True)

                # 验证webhook是否真的被清除
                webhook_info = await self.application.bot.get_webhook_info()
                if webhook_info.url:
                    logger.warning(f"Webhook仍然存在: {webhook_info.url}")
                    # 再次尝试删除
                    await self.application.bot.delete_webhook(drop_pending_updates=True)
                    await asyncio.sleep(1)  # 等待一秒
                    webhook_info = await self.application.bot.get_webhook_info()
                    if webhook_info.url:
                        logger.error(f"无法删除webhook: {webhook_info.url}")
                        return False

                logger.info("Webhook已清除")
            except Exception as e:
                logger.warning(f"清除webhook时出现警告（可忽略）: {e}")

            logger.info("正在启动轮询...")

            # 启动轮询，丢弃待处理的更新
            await self.application.updater.start_polling(
                drop_pending_updates=True,
                allowed_updates=[
                    "message",
                    "edited_message",
                    "channel_post",
                    "edited_channel_post",
                    "callback_query",
                    "my_chat_member",
                    "chat_member"
                ]
            )

            logger.info("轮询启动成功")
            return True

        except Exception as e:
            logger.error(f"启动轮询失败: {e}", exc_info=True)

            # 检查是否是冲突错误
            if "terminated by other getUpdates request" in str(e):
                logger.error("检测到Telegram API冲突错误！")
                logger.error("可能的原因：")
                logger.error("1. 有其他机器人实例正在运行")
                logger.error("2. Webhook设置没有正确清除")
                logger.error("3. 有残留的轮询连接")
                logger.info("建议解决方案：")
                logger.info("1. 运行: python clear_telegram_webhook.py --all")
                logger.info("2. 或者运行: python cleanup_processes.py")
                logger.info("3. 等待几分钟后重试")
                return False

            # 如果是网络错误，尝试重试
            if self._is_network_error(e):
                logger.info(f"检测到网络错误，{self.config.retry_delay}秒后重试...")
                await asyncio.sleep(self.config.retry_delay)
                return await self._start_polling_with_retry()
            return False

    def _is_network_error(self, error: Exception) -> bool:
        """判断是否为网络错误"""
        error_str = str(error).lower()
        network_error_keywords = [
            "server disconnected",
            "connection error",
            "timeout",
            "network error",
            "connection refused",
            "connection reset",
            "remote protocol error",
            "httpx.remotprotocolerror",
            "httpcore.remotprotocolerror"
        ]
        return any(keyword in error_str for keyword in network_error_keywords)

    def _is_conflict_error(self, error: Exception) -> bool:
        """判断是否为冲突错误（多个Bot实例）"""
        error_str = str(error).lower()
        conflict_keywords = [
            "conflict",
            "terminated by other getupdates request",
            "only one bot instance"
        ]
        return any(keyword in error_str for keyword in conflict_keywords)

    async def _start_polling_with_retry(self) -> bool:
        """带重试的轮询启动"""
        for attempt in range(self.config.max_retries):
            try:
                logger.info(f"重试启动轮询 (尝试 {attempt + 1}/{self.config.max_retries})")

                if not self.application or not self.application.updater:
                    logger.error("应用或更新器未初始化，无法启动轮询")
                    return False

                # 启动轮询，丢弃待处理的更新
                await self.application.updater.start_polling(
                    drop_pending_updates=True,
                    allowed_updates=[
                        "message",
                        "edited_message",
                        "channel_post",
                        "edited_channel_post",
                        "callback_query",
                        "my_chat_member",
                        "chat_member"
                    ]
                )

                logger.info("轮询重试启动成功")
                return True

            except Exception as e:
                logger.warning(f"轮询重试失败 (尝试 {attempt + 1}/{self.config.max_retries}): {e}")
                if attempt < self.config.max_retries - 1:
                    await asyncio.sleep(self.config.retry_delay * (attempt + 1))  # 指数退避
                else:
                    logger.error("所有轮询重试都失败了")
                    return False

        return False

    async def _handle_global_error(self, update, context):
        """处理全局错误"""
        error = context.error

        # 如果是冲突错误，记录警告但不记录完整堆栈
        if self._is_conflict_error(error):
            logger.warning("检测到Bot实例冲突，可能有其他Bot实例正在运行")
            logger.warning("建议：确保只有一个Bot实例在运行，或使用Webhook模式")
        # 如果是网络错误，记录详细信息
        elif self._is_network_error(error):
            logger.warning("检测到网络错误，Bot将自动重试")
            await self._handle_network_error(error)
        else:
            logger.error(f"Telegram Bot发生错误: {error}", exc_info=True)

        # 不抛出异常，让Bot继续运行

    async def _handle_network_error(self, error: Exception):
        """处理网络错误"""
        logger.warning(f"网络错误: {error}")

        # 检查代理配置建议
        if not self.config.proxy_url:
            logger.info("🌐 网络连接问题解决建议:")
            logger.info("1. 如果在中国大陆使用，需要配置代理来访问Telegram API")
            logger.info("2. 推荐代理配置示例:")
            logger.info("   - HTTP代理: http://proxy.example.com:8080")
            logger.info("   - SOCKS5代理: socks5://proxy.example.com:1080")
            logger.info("3. 或者使用Webhook模式替代轮询模式")
            logger.info("4. 配置方法:")
            logger.info("   - 环境变量: TELEGRAM_PROXY_URL=http://your-proxy:port")
            logger.info("   - 或在Web界面的Telegram配置中设置代理")

        # 记录网络诊断信息
        logger.info("🔍 网络诊断信息:")
        logger.info(f"- 连接超时: {self.config.connect_timeout}秒")
        logger.info(f"- 读取超时: {self.config.read_timeout}秒")
        logger.info(f"- 代理配置: {'已配置' if self.config.proxy_url else '未配置'}")
        if self.config.proxy_url:
            logger.info(f"- 代理地址: {self.config.proxy_url}")
        logger.info(f"- 最大重试次数: {self.config.max_retries}")
        logger.info(f"- 重试延迟: {self.config.retry_delay}秒")

        # 提供具体的解决方案
        logger.info("💡 解决方案:")
        logger.info("方案1: 配置代理")
        logger.info("  export TELEGRAM_PROXY_URL='http://your-proxy:port'")
        logger.info("方案2: 使用Webhook模式")
        logger.info("  在Web界面配置Webhook URL，避免轮询模式的网络问题")
        logger.info("方案3: 检查防火墙设置")
        logger.info("  确保允许访问 api.telegram.org:443")

    async def test_network_connection(self) -> bool:
        """测试网络连接"""
        try:
            if not self.application:
                logger.error("机器人应用未初始化")
                return False

            logger.info("正在测试Telegram API连接...")

            # 尝试获取机器人信息
            bot_info = await self.application.bot.get_me()
            logger.info(f"网络连接测试成功，机器人信息: @{bot_info.username}")
            return True

        except Exception as e:
            logger.error(f"网络连接测试失败: {e}")
            await self._handle_network_error(e)
            return False

    async def _stop_polling(self):
        """停止轮询模式"""
        try:
            if not self.application or not self.application.updater:
                logger.warning("应用或更新器未初始化，跳过轮询停止")
                return True

            logger.info("正在停止轮询...")

            # 停止轮询
            await self.application.updater.stop()

            logger.info("轮询已停止")
            return True

        except Exception as e:
            logger.error(f"停止轮询失败: {e}", exc_info=True)
            return False
    
    async def _auto_resolve_conflicts(self) -> bool:
        """自动解决冲突"""
        logger.info("🔍 自动检测和解决冲突...")

        try:
            # 1. 强制清除webhook（避免webhook和polling冲突）
            logger.info("清除可能存在的webhook...")
            try:
                await self.application.bot.delete_webhook(drop_pending_updates=True)
                await asyncio.sleep(1)

                # 验证清除结果
                webhook_info = await self.application.bot.get_webhook_info()
                if webhook_info.url:
                    logger.warning(f"Webhook仍存在，再次尝试清除: {webhook_info.url}")
                    await self.application.bot.delete_webhook(drop_pending_updates=True)
                    await asyncio.sleep(2)
                else:
                    logger.info("✅ Webhook已清除")
            except Exception as e:
                logger.warning(f"清除webhook时出错: {e}")

            # 2. 测试API连接，检测是否有其他实例
            logger.info("测试API连接...")
            try:
                # 尝试获取机器人信息
                bot_info = await self.application.bot.get_me()
                logger.info(f"✅ API连接正常: @{bot_info.username}")

                # 尝试一次短暂的轮询测试
                logger.info("测试轮询可用性...")
                updates = await self.application.bot.get_updates(limit=1, timeout=1)
                logger.info("✅ 轮询测试通过")

                return True

            except Exception as e:
                error_msg = str(e)
                if "terminated by other getUpdates request" in error_msg:
                    logger.error("❌ 检测到API冲突！")
                    logger.error("可能原因：")
                    logger.error("1. 有其他应用实例正在使用同一个bot token")
                    logger.error("2. IDE调试器中有残留的机器人实例")
                    logger.error("3. 其他开发环境在使用相同配置")

                    # 建议解决方案
                    logger.info("💡 自动解决建议：")
                    logger.info("1. 切换到Webhook模式（推荐）")
                    logger.info("2. 使用不同的bot token进行开发")
                    logger.info("3. 确保只有一个环境在运行")

                    return False
                else:
                    logger.error(f"API连接测试失败: {e}")
                    return False

        except Exception as e:
            logger.error(f"自动冲突解决失败: {e}")
            return False

    async def _smart_start_mode(self) -> bool:
        """智能选择启动模式"""
        logger.info("🧠 智能选择启动模式...")

        # 优先级1: 如果配置了webhook，尝试webhook模式
        if self.config.webhook_url and self.config.webhook_url.strip():
            logger.info("🌐 尝试Webhook模式...")
            if await self._try_webhook_mode():
                return True
            logger.warning("Webhook模式失败，尝试轮询模式...")

        # 优先级2: 尝试轮询模式
        logger.info("🔄 尝试轮询模式...")
        if await self._try_polling_mode():
            return True

        # 优先级3: 如果轮询失败且没有配置webhook，建议配置webhook
        if not self.config.webhook_url:
            logger.error("❌ 轮询模式失败，且未配置Webhook")
            logger.info("💡 建议解决方案：")
            logger.info("1. 配置Webhook URL以使用Webhook模式")
            logger.info("2. 检查是否有其他实例在运行")
            logger.info("3. 使用不同的bot token进行开发")

        return False

    async def _try_webhook_mode(self) -> bool:
        """尝试启动Webhook模式"""
        try:
            logger.info("设置Webhook模式...")
            self._polling_mode = False

            if self.webhook_handler:
                webhook_success = await self.webhook_handler.setup_webhook()
                if webhook_success:
                    logger.info("✅ Webhook模式启动成功")
                    return True
                else:
                    logger.warning("❌ Webhook设置失败")
            else:
                logger.warning("❌ Webhook处理器未初始化")

            return False

        except Exception as e:
            logger.error(f"Webhook模式启动失败: {e}")
            return False

    async def _try_polling_mode(self) -> bool:
        """尝试启动轮询模式"""
        try:
            logger.info("设置轮询模式...")
            self._polling_mode = True

            # 确保删除任何现有的webhook
            if self.webhook_handler:
                await self.webhook_handler.remove_webhook()

            # 启动轮询
            polling_success = await self._start_polling()
            if polling_success:
                logger.info("✅ 轮询模式启动成功")
                return True
            else:
                logger.warning("❌ 轮询模式启动失败")
                self._polling_mode = False

            return False

        except Exception as e:
            logger.error(f"轮询模式启动失败: {e}")
            self._polling_mode = False
            return False

    async def start(self) -> bool:
        """启动机器人服务"""
        try:
            if self._running:
                logger.warning("机器人服务已在运行中")
                return True

            if not self.application:
                logger.error("机器人应用未初始化")
                return False

            # 检查单例锁
            singleton_info = get_bot_singleton_info()
            if singleton_info:
                logger.warning(f"检测到其他实例正在运行 (PID: {singleton_info['pid']})")
                logger.info("💡 自动解决方案：")

                # 检查进程是否还存在
                from ..utils.simple_singleton import _bot_singleton
                if not _bot_singleton._is_process_running(singleton_info['pid']):
                    logger.info("进程已不存在，清理死锁...")
                    _bot_singleton.force_cleanup()
                else:
                    logger.error("❌ 其他实例仍在运行，无法启动")
                    logger.info("建议：")
                    logger.info("1. 停止其他实例")
                    logger.info("2. 使用不同的bot token")
                    logger.info("3. 配置Webhook模式")
                    return False

            # 获取单例锁
            if not acquire_bot_singleton():
                logger.error("❌ 无法获取单例锁")
                return False

            # 自动解决冲突
            if not await self._auto_resolve_conflicts():
                logger.error("❌ 无法解决冲突，启动失败")
                logger.info("💡 建议切换到Webhook模式避免冲突")
                release_bot_singleton()
                return False

            logger.info("正在启动Telegram机器人服务...")

            # 初始化应用
            await self.application.initialize()

            # 智能选择启动模式
            success = await self._smart_start_mode()
            if not success:
                logger.error("❌ 所有启动模式都失败")
                return False

            # 启动应用
            await self.application.start()

            self._running = True
            mode_text = "轮询模式" if self._polling_mode else "Webhook模式"
            logger.info(f"Telegram机器人服务启动成功 ({mode_text})")
            return True

        except Exception as e:
            logger.error(f"启动Telegram机器人服务失败: {e}", exc_info=True)
            return False
    
    async def stop(self) -> bool:
        """停止机器人服务"""
        try:
            if not self._running:
                logger.warning("机器人服务未在运行")
                # 即使服务未运行，也要释放单例锁
                release_bot_singleton()
                return True

            mode_text = "轮询模式" if self._polling_mode else "Webhook模式"
            logger.info(f"正在停止Telegram机器人服务 ({mode_text})...")

            if self.application:
                # 根据模式停止相应的服务
                if self._polling_mode:
                    # 停止轮询
                    await self._stop_polling()
                else:
                    # 删除Webhook
                    if self.webhook_handler:
                        await self.webhook_handler.remove_webhook()

                # 停止应用
                await self.application.stop()
                await self.application.shutdown()

            # 清理状态
            self._running = False
            self._polling_mode = False
            if self._polling_task:
                self._polling_task.cancel()
                self._polling_task = None

            # 释放单例锁
            release_bot_singleton()

            logger.info("✅ Telegram机器人服务已停止")
            return True

        except Exception as e:
            logger.error(f"停止Telegram机器人服务失败: {e}", exc_info=True)
            # 即使停止失败，也要释放单例锁
            release_bot_singleton()
            return False
    
    async def process_update(self, update_data: Dict[str, Any]) -> bool:
        """处理Webhook更新"""
        try:
            if not self.application:
                logger.error("机器人应用未初始化")
                return False
            
            # 创建Update对象
            update = Update.de_json(update_data, self.application.bot)
            if not update:
                logger.warning("无效的更新数据")
                return False
            
            # 处理更新
            await self.application.process_update(update)
            return True
            
        except Exception as e:
            logger.error(f"处理更新失败: {e}", exc_info=True)
            return False
    
    async def send_message(self, chat_id: int, text: str, **kwargs) -> bool:
        """发送消息"""
        try:
            if not self.application or not self.application.bot:
                logger.error("机器人未初始化")
                return False
            
            await self.application.bot.send_message(
                chat_id=chat_id,
                text=text,
                **kwargs
            )
            return True
            
        except Exception as e:
            logger.error(f"发送消息失败: {e}", exc_info=True)
            return False
    
    def is_running(self) -> bool:
        """检查服务是否在运行"""
        return self._running
    
    def get_bot_info(self) -> Optional[Dict[str, Any]]:
        """获取机器人信息"""
        if not self.application or not self.application.bot:
            return {
                "status": "not_configured",
                "message": "机器人服务未配置或Bot Token无效"
            }

        # 检查机器人服务是否已经启动
        if not self._running:
            return {
                "status": "not_started",
                "message": "机器人服务已配置但尚未启动，请先启动服务"
            }

        try:
            bot = self.application.bot
            return {
                "id": bot.id,
                "username": bot.username,
                "first_name": bot.first_name,
                "can_join_groups": bot.can_join_groups,
                "can_read_all_group_messages": bot.can_read_all_group_messages,
                "supports_inline_queries": bot.supports_inline_queries,
                "status": "running"
            }
        except Exception as e:
            logger.error(f"获取机器人信息失败: {e}")
            # 返回错误信息而不是None，这样前端可以显示具体的错误状态
            return {
                "status": "error",
                "message": f"获取机器人信息失败: {str(e)}"
            }
    
    def get_status(self) -> Dict[str, Any]:
        """获取服务状态"""
        return {
            "running": self._running,
            "mode": "polling" if self._polling_mode else "webhook",
            "initialized": self.application is not None,
            "config_loaded": self.config is not None,
            "webhook_configured": self.webhook_handler is not None,
            "handlers_registered": self.command_registry is not None,
            "rate_limiter_active": self.rate_limiter is not None,
            "bot_info": self.get_bot_info()
        }
    
    async def reload_config(self) -> bool:
        """重新加载配置"""
        try:
            db = SessionLocal()
            try:
                old_config = self.config
                self.config = get_bot_config(db)

                if not self.config.validate():
                    self.config = old_config  # 回滚
                    return False

                # 更新组件配置
                if self.rate_limiter:
                    self.rate_limiter.update_config(self.config)

                if self.command_registry:
                    self.command_registry.update_config(self.config)

                if self.webhook_handler:
                    self.webhook_handler.update_config(self.config)

                logger.info("配置重新加载成功")
                return True

            finally:
                db.close()

        except Exception as e:
            logger.error(f"重新加载配置失败: {e}")
            return False

    async def set_privacy_mode(self, privacy_disabled: bool) -> bool:
        """设置机器人隐私模式

        Args:
            privacy_disabled: True表示关闭隐私模式(可读取所有群组消息), False表示开启隐私模式
        """
        try:
            if not self.application or not self.application.bot:
                logger.error("机器人应用未初始化")
                return False

            # 注意：Telegram Bot API 不支持通过 API 直接修改隐私设置
            # 隐私设置只能通过 BotFather 进行修改
            # 这里我们只能获取当前状态，不能修改

            logger.warning("机器人隐私模式只能通过 BotFather 修改，API 无法直接设置")
            logger.info(f"请通过 BotFather 将隐私模式设置为: {'关闭' if privacy_disabled else '开启'}")

            # 返回 False 表示无法通过 API 设置，需要手动操作
            return False

        except Exception as e:
            logger.error(f"设置隐私模式失败: {e}", exc_info=True)
            return False


# 全局服务实例
_bot_service: Optional[TelegramBotService] = None


def get_bot_service() -> TelegramBotService:
    """获取机器人服务实例"""
    global _bot_service
    if _bot_service is None:
        _bot_service = TelegramBotService()
    return _bot_service


async def initialize_bot_service() -> bool:
    """初始化机器人服务"""
    service = get_bot_service()
    return await service.initialize()


async def start_bot_service() -> bool:
    """启动机器人服务"""
    service = get_bot_service()
    return await service.start()


async def stop_bot_service() -> bool:
    """停止机器人服务"""
    service = get_bot_service()
    return await service.stop()
