#!/usr/bin/env python3
"""
Telegram机器人冲突问题一键修复工具
解决 "Conflict: terminated by other getUpdates request" 错误
"""

import asyncio
import sys
import os
import time
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from app.core.logging import get_logger

logger = get_logger(__name__)


async def fix_telegram_conflict():
    """修复Telegram机器人冲突问题"""
    logger.info("🔧 Telegram机器人冲突问题修复工具")
    logger.info("=" * 60)
    
    success_steps = []
    failed_steps = []
    
    # 步骤1: 清理进程
    logger.info("步骤 1/4: 清理相关进程...")
    try:
        from cleanup_processes import find_related_processes, terminate_processes
        
        processes = find_related_processes()
        if processes:
            logger.info(f"发现 {len(processes)} 个相关进程")
            terminate_processes(processes, force=True, exclude_debugger=True)
            success_steps.append("清理进程")
            logger.info("✅ 进程清理完成")
        else:
            logger.info("✅ 没有发现需要清理的进程")
            success_steps.append("检查进程")
            
        # 等待进程完全终止
        await asyncio.sleep(2)
        
    except Exception as e:
        logger.error(f"❌ 清理进程失败: {e}")
        failed_steps.append(f"清理进程: {e}")
    
    # 步骤2: 清理webhook
    logger.info("步骤 2/4: 清理Telegram webhook...")
    try:
        from clear_telegram_webhook import clear_webhook, get_webhook_info
        
        # 检查当前webhook状态
        webhook_info = await get_webhook_info()
        if webhook_info and webhook_info.get("url"):
            logger.info(f"发现现有webhook: {webhook_info['url']}")
            
            # 清理webhook
            if await clear_webhook():
                success_steps.append("清理webhook")
                logger.info("✅ Webhook清理完成")
            else:
                failed_steps.append("清理webhook失败")
                logger.error("❌ Webhook清理失败")
        else:
            logger.info("✅ 没有发现需要清理的webhook")
            success_steps.append("检查webhook")
            
    except Exception as e:
        logger.error(f"❌ 清理webhook失败: {e}")
        failed_steps.append(f"清理webhook: {e}")
    
    # 步骤3: 清理PID文件
    logger.info("步骤 3/4: 清理PID文件...")
    try:
        from app.telegram_bot.utils.process_manager import ProcessManager
        
        pid_files = [
            "telegram_bot.pid",
            "bot_service.pid",
            ".telegram_bot_pid"
        ]
        
        cleaned_files = []
        for pid_file in pid_files:
            if os.path.exists(pid_file):
                try:
                    os.remove(pid_file)
                    cleaned_files.append(pid_file)
                except Exception as e:
                    logger.warning(f"删除PID文件失败 {pid_file}: {e}")
        
        if cleaned_files:
            logger.info(f"✅ 清理了PID文件: {', '.join(cleaned_files)}")
        else:
            logger.info("✅ 没有发现需要清理的PID文件")
        
        success_steps.append("清理PID文件")
        
    except Exception as e:
        logger.error(f"❌ 清理PID文件失败: {e}")
        failed_steps.append(f"清理PID文件: {e}")
    
    # 步骤4: 测试连接
    logger.info("步骤 4/4: 测试机器人连接...")
    try:
        from clear_telegram_webhook import test_bot_connection
        
        if await test_bot_connection():
            success_steps.append("测试连接")
            logger.info("✅ 机器人连接测试成功")
        else:
            failed_steps.append("机器人连接测试失败")
            logger.error("❌ 机器人连接测试失败")
            
    except Exception as e:
        logger.error(f"❌ 测试连接失败: {e}")
        failed_steps.append(f"测试连接: {e}")
    
    # 总结
    logger.info("=" * 60)
    logger.info("🎯 修复结果总结")
    logger.info("=" * 60)
    
    if success_steps:
        logger.info("✅ 成功完成的步骤:")
        for step in success_steps:
            logger.info(f"   - {step}")
    
    if failed_steps:
        logger.error("❌ 失败的步骤:")
        for step in failed_steps:
            logger.error(f"   - {step}")
    
    # 给出建议
    if not failed_steps:
        logger.info("🎉 所有步骤都成功完成！")
        logger.info("💡 现在可以尝试重新启动Telegram机器人服务")
        logger.info("   方法1: 通过Web界面启动")
        logger.info("   方法2: 运行 python start_bot.py")
    else:
        logger.warning("⚠️  部分步骤失败，建议:")
        logger.info("1. 等待5-10分钟后重试")
        logger.info("2. 检查网络连接")
        logger.info("3. 验证bot token配置")
        logger.info("4. 如果问题持续，考虑使用webhook模式")
    
    return len(failed_steps) == 0


async def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description="Telegram机器人冲突问题修复工具")
    parser.add_argument("--auto", action="store_true", help="自动修复，不需要确认")
    
    args = parser.parse_args()
    
    if not args.auto:
        print("🔧 Telegram机器人冲突问题修复工具")
        print("=" * 60)
        print("此工具将执行以下操作:")
        print("1. 清理相关进程")
        print("2. 清理Telegram webhook设置")
        print("3. 清理PID文件")
        print("4. 测试机器人连接")
        print()
        
        response = input("是否继续? (y/N): ")
        if response.lower() not in ['y', 'yes']:
            print("操作已取消")
            return
    
    # 执行修复
    success = await fix_telegram_conflict()
    
    if success:
        logger.info("🎉 修复完成！")
        sys.exit(0)
    else:
        logger.error("❌ 修复过程中遇到问题")
        sys.exit(1)


if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        logger.info("操作被用户中断")
    except Exception as e:
        logger.error(f"程序执行失败: {e}")
        sys.exit(1)
