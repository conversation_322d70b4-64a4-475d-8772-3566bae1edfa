-- ========================================
-- CK管理机制修改迁移脚本
-- 将时间限制改为总次数限制
-- ========================================

USE `walmart_card_db`;

-- ========================================
-- 1. 备份现有数据
-- ========================================

-- 创建备份表
CREATE TABLE `walmart_ck_backup_before_limit_change` AS 
SELECT * FROM `walmart_ck`;

-- ========================================
-- 2. 修改表结构
-- ========================================

-- 添加新的总限制字段
ALTER TABLE `walmart_ck` 
ADD COLUMN `total_limit` int(11) NOT NULL DEFAULT 20 COMMENT '总绑卡次数限制' 
AFTER `sign`;

-- 将现有的daily_limit值迁移到total_limit
UPDATE `walmart_ck` SET `total_limit` = `daily_limit`;

-- 删除旧的时间限制字段
ALTER TABLE `walmart_ck` DROP COLUMN `daily_limit`;
ALTER TABLE `walmart_ck` DROP COLUMN `hourly_limit`;

-- 修改bind_count字段注释
ALTER TABLE `walmart_ck` 
MODIFY COLUMN `bind_count` int(11) NOT NULL DEFAULT 0 COMMENT '累计已绑卡数量';

-- ========================================
-- 3. 更新索引
-- ========================================

-- 删除旧的索引（如果存在）
DROP INDEX IF EXISTS `idx_walmart_ck_daily_limit` ON `walmart_ck`;
DROP INDEX IF EXISTS `idx_walmart_ck_hourly_limit` ON `walmart_ck`;

-- 创建新的索引
CREATE INDEX `idx_walmart_ck_total_limit` ON `walmart_ck` (`total_limit`);
CREATE INDEX `idx_walmart_ck_usage_status` ON `walmart_ck` (`bind_count`, `total_limit`, `active`);

-- ========================================
-- 4. 数据一致性检查
-- ========================================

-- 检查是否有CK的bind_count超过了total_limit
SELECT 
    id,
    bind_count,
    total_limit,
    active,
    CASE 
        WHEN bind_count >= total_limit THEN 'SHOULD_BE_INACTIVE'
        ELSE 'OK'
    END as status_check
FROM `walmart_ck`
WHERE bind_count >= total_limit AND active = 1;

-- 自动禁用已达到限制的CK
UPDATE `walmart_ck` 
SET `active` = 0 
WHERE `bind_count` >= `total_limit` AND `active` = 1;

-- ========================================
-- 5. 验证迁移结果
-- ========================================

-- 显示迁移统计
SELECT 
    'Migration Summary' as info,
    COUNT(*) as total_cks,
    SUM(CASE WHEN active = 1 THEN 1 ELSE 0 END) as active_cks,
    SUM(CASE WHEN bind_count >= total_limit THEN 1 ELSE 0 END) as reached_limit_cks,
    AVG(total_limit) as avg_total_limit,
    AVG(bind_count) as avg_bind_count
FROM `walmart_ck`;

-- 显示字段变更确认
DESCRIBE `walmart_ck`;

-- ========================================
-- 6. 清理和完成
-- ========================================

-- 记录迁移完成时间
INSERT INTO `system_settings` (`key`, `value`, `description`, `type`, `is_public`, `created_at`) 
VALUES (
    'ck_limit_migration_completed', 
    NOW(), 
    'CK限制机制迁移完成时间', 
    'system', 
    0, 
    NOW()
) ON DUPLICATE KEY UPDATE 
    `value` = NOW(), 
    `updated_at` = NOW();

SELECT 'CK limit migration completed successfully!' AS status;

-- ========================================
-- 回滚脚本（如需要）
-- ========================================

/*
-- 如果需要回滚，执行以下脚本：

-- 恢复旧字段
ALTER TABLE `walmart_ck` 
ADD COLUMN `daily_limit` int(11) NOT NULL DEFAULT 20 COMMENT '每日绑卡限制' AFTER `sign`,
ADD COLUMN `hourly_limit` int(11) NOT NULL DEFAULT 20 COMMENT '每小时绑卡限制' AFTER `daily_limit`;

-- 从total_limit恢复数据
UPDATE `walmart_ck` SET `daily_limit` = `total_limit`;

-- 删除新字段
ALTER TABLE `walmart_ck` DROP COLUMN `total_limit`;

-- 恢复bind_count注释
ALTER TABLE `walmart_ck` 
MODIFY COLUMN `bind_count` int(11) NOT NULL DEFAULT 0 COMMENT '今日已绑卡数量';

-- 恢复索引
DROP INDEX IF EXISTS `idx_walmart_ck_total_limit` ON `walmart_ck`;
DROP INDEX IF EXISTS `idx_walmart_ck_usage_status` ON `walmart_ck`;

*/
