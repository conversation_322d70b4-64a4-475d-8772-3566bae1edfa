<template>
  <el-dialog
    v-model="dialogVisible"
    title="部门详情"
    width="700px"
    :close-on-click-modal="false"
  >
    <div class="department-detail" v-if="department">
      <!-- 基本信息 -->
      <div class="detail-section">
        <h4>基本信息</h4>
        <div class="detail-grid">
          <div class="detail-item">
            <label>部门名称：</label>
            <span>{{ department.name }}</span>
          </div>
          <div class="detail-item">
            <label>部门代码：</label>
            <span>{{ department.code }}</span>
          </div>
          <div class="detail-item">
            <label>所属商户：</label>
            <span>{{ department.merchantName || '未知' }}</span>
          </div>
          <div class="detail-item">
            <label>上级部门：</label>
            <span>{{ department.parentName || '无' }}</span>
          </div>
          <div class="detail-item">
            <label>部门层级：</label>
            <span>第{{ department.level }}级</span>
          </div>
          <div class="detail-item">
            <label>状态：</label>
            <el-tag :type="department.status ? 'success' : 'danger'">
              {{ department.status ? '启用' : '禁用' }}
            </el-tag>
          </div>
          <div class="detail-item full-width" v-if="department.description">
            <label>部门描述：</label>
            <span>{{ department.description }}</span>
          </div>
        </div>
      </div>

      <!-- 负责人信息 -->
      <div class="detail-section" v-if="hasManagerInfo">
        <h4>负责人信息</h4>
        <div class="detail-grid">
          <div class="detail-item" v-if="department.managerName">
            <label>负责人姓名：</label>
            <span>{{ department.managerName }}</span>
          </div>
          <div class="detail-item" v-if="department.managerPhone">
            <label>负责人电话：</label>
            <span>{{ department.managerPhone }}</span>
          </div>
          <div class="detail-item" v-if="department.managerEmail">
            <label>负责人邮箱：</label>
            <span>{{ department.managerEmail }}</span>
          </div>
        </div>
      </div>

      <!-- 业务配置 -->
      <div class="detail-section">
        <h4>业务配置</h4>
        <div class="detail-grid">
          <div class="detail-item" v-if="department.businessScope">
            <label>业务范围：</label>
            <span>{{ department.businessScope }}</span>
          </div>

          <div class="detail-item">
            <label>排序号：</label>
            <span>{{ department.sortOrder || 0 }}</span>
          </div>
          <div class="detail-item full-width" v-if="department.remark">
            <label>备注：</label>
            <span>{{ department.remark }}</span>
          </div>
        </div>
      </div>

      <!-- 统计信息 -->
      <div class="detail-section" v-if="statistics">
        <h4>统计信息</h4>
        <div class="stats-grid">
          <div class="stat-card">
            <div class="stat-number">{{ statistics.userCount || 0 }}</div>
            <div class="stat-label">部门用户数</div>
          </div>
          <div class="stat-card">
            <div class="stat-number">{{ statistics.subDepartmentCount || 0 }}</div>
            <div class="stat-label">子部门数</div>
          </div>
          <div class="stat-card">
            <div class="stat-number">{{ statistics.todayBindCount || 0 }}</div>
            <div class="stat-label">今日绑卡数</div>
          </div>
          <div class="stat-card">
            <div class="stat-number">{{ statistics.totalBindCount || 0 }}</div>
            <div class="stat-label">总绑卡数</div>
          </div>
        </div>
      </div>

      <!-- 部门路径 -->
      <div class="detail-section" v-if="department.path">
        <h4>部门路径</h4>
        <div class="path-display">
          <el-breadcrumb separator="/">
            <el-breadcrumb-item v-for="(pathItem, index) in departmentPath" :key="index">
              {{ pathItem }}
            </el-breadcrumb-item>
          </el-breadcrumb>
        </div>
      </div>

      <!-- 创建信息 -->
      <div class="detail-section">
        <h4>创建信息</h4>
        <div class="detail-grid">
          <div class="detail-item">
            <label>创建时间：</label>
            <span>{{ formatDateTime(department.createdAt) }}</span>
          </div>
          <div class="detail-item">
            <label>更新时间：</label>
            <span>{{ formatDateTime(department.updatedAt) }}</span>
          </div>
          <div class="detail-item" v-if="department.createdBy">
            <label>创建者：</label>
            <span>{{ department.createdByName || `用户${department.createdBy}` }}</span>
          </div>
        </div>
      </div>
    </div>

    <div class="loading-container" v-else>
      <el-skeleton :rows="8" animated />
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="dialogVisible = false">关闭</el-button>
        <el-button
          type="primary"
          @click="handleEdit"
          v-if="hasPermission('department:edit')"
        >
          编辑部门
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, computed, watch, onMounted } from 'vue'
import { useUserStore } from '@/store/modules/user'
import { departmentApi } from '@/api/modules/department'
import { formatDateTime } from '@/utils/dateUtils'

// Props
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  department: {
    type: Object,
    default: null
  }
})

// Emits
const emit = defineEmits(['update:visible', 'edit'])

// Store
const userStore = useUserStore()

// 响应式数据
const statistics = ref(null)
const loading = ref(false)

// 计算属性
const dialogVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
})

const hasManagerInfo = computed(() => {
  if (!props.department) return false
  return props.department.managerName ||
         props.department.managerPhone ||
         props.department.managerEmail
})

const departmentPath = computed(() => {
  if (!props.department?.path) return []

  // 解析路径字符串，例如："/m1/2/3/" -> ["商户1", "部门2", "部门3"]
  const pathParts = props.department.path.split('/').filter(part => part)
  const result = []

  pathParts.forEach((part, index) => {
    if (part.startsWith('m')) {
      // 商户路径
      result.push(props.department.merchantName || `商户${part.substring(1)}`)
    } else {
      // 部门路径
      result.push(`部门${part}`)
    }
  })

  // 添加当前部门
  result.push(props.department.name)

  return result
})

const hasPermission = computed(() => {
  return (permission) => {
    // 简化权限检查：超级管理员拥有所有权限
    if (userStore.isSuperAdmin) {
      return true
    }

    // 商户管理员拥有部门相关权限
    if (userStore.isMerchantAdmin) {
      return permission.startsWith('department:')
    }

    // 其他角色暂时没有编辑权限
    return false
  }
})

// 监听器
watch(() => props.visible, (visible) => {
  if (visible && props.department) {
    loadDepartmentDetail()
    loadStatistics()
  }
})

// 方法
const loadDepartmentDetail = async () => {
  if (!props.department?.id) return

  try {
    loading.value = true
    const response = await departmentApi.getDetail(props.department.id)
    // 更新部门详情数据，包含merchantName和parentName
    Object.assign(props.department, response)
  } catch (error) {
    console.error('加载部门详情失败：', error)
    // 如果获取详情失败，保持使用原有数据
  } finally {
    loading.value = false
  }
}

const loadStatistics = async () => {
  if (!props.department?.id) return

  try {
    const response = await departmentApi.getStatistics(props.department.id)
    statistics.value = response
  } catch (error) {
    console.error('加载部门统计失败：', error)
    // 不显示错误消息，因为统计信息不是必需的
  }
}

const handleEdit = () => {
  emit('edit', props.department)
  dialogVisible.value = false
}
</script>

<style scoped>
.department-detail {
  max-height: 70vh;
  overflow-y: auto;
}

.detail-section {
  margin-bottom: 24px;
  padding-bottom: 20px;
  border-bottom: 1px solid #f0f0f0;
}

.detail-section:last-child {
  border-bottom: none;
  margin-bottom: 0;
  padding-bottom: 0;
}

.detail-section h4 {
  margin: 0 0 16px 0;
  color: #303133;
  font-size: 16px;
  font-weight: 600;
  border-left: 4px solid #409eff;
  padding-left: 12px;
}

.detail-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
}

.detail-item {
  display: flex;
  align-items: flex-start;
}

.detail-item.full-width {
  grid-column: 1 / -1;
  flex-direction: column;
}

.detail-item label {
  font-weight: 600;
  color: #606266;
  min-width: 100px;
  margin-right: 8px;
}

.detail-item.full-width label {
  margin-bottom: 4px;
}

.detail-item span {
  color: #303133;
  word-break: break-all;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 16px;
}

.stat-card {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 20px;
  text-align: center;
  border: 1px solid #e9ecef;
}

.stat-number {
  font-size: 24px;
  font-weight: 700;
  color: #409eff;
  margin-bottom: 8px;
}

.stat-label {
  font-size: 14px;
  color: #606266;
}

.path-display {
  background: #f8f9fa;
  border-radius: 4px;
  padding: 12px;
  border: 1px solid #e9ecef;
}

.loading-container {
  padding: 20px;
}

.dialog-footer {
  text-align: right;
}

@media (max-width: 768px) {
  .detail-grid {
    grid-template-columns: 1fr;
  }

  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}
</style>
