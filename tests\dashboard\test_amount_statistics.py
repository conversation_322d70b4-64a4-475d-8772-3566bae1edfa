#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
沃尔玛绑卡系统 - 仪表盘金额统计功能测试

测试内容：
1. 请求金额统计功能
2. 实际金额统计功能
3. 金额差异分析功能
4. 数据权限隔离测试
"""

import pytest
from datetime import datetime, timedelta
from sqlalchemy.orm import Session
from fastapi.testclient import TestClient

from app.main import app
from app.core.deps import get_db
from app.models.card_record import CardRecord
from app.models.user import User
from app.models.merchant import Merchant
from app.models.department import Department
from app.models.walmart_ck import WalmartCK
from app.services.dashboard_statistics_service import DashboardStatisticsService
from test.conftest import TestBase


class TestAmountStatistics(TestBase):
    """仪表盘金额统计功能测试类"""

    def setUp(self):
        """测试前准备"""
        super().setUp()
        self.client = TestClient(app)
        self.dashboard_service = DashboardStatisticsService(self.db)
        
        # 创建测试数据
        self._create_test_data()

    def _create_test_data(self):
        """创建测试数据"""
        # 创建测试商户
        self.test_merchant = Merchant(
            name="测试商户",
            code="TEST_MERCHANT",
            contact_person="测试联系人",
            contact_phone="13800138000",
            status=True
        )
        self.db.add(self.test_merchant)
        self.db.commit()

        # 创建测试部门
        self.test_department = Department(
            name="测试部门",
            code="TEST_DEPT",
            merchant_id=self.test_merchant.id,
            status=True
        )
        self.db.add(self.test_department)
        self.db.commit()

        # 创建测试CK
        self.test_ck = WalmartCK(
            sign="<EMAIL>#secret#v1",
            merchant_id=self.test_merchant.id,
            department_id=self.test_department.id,
            active=True,
            hourly_limit=100,
            daily_limit=1000
        )
        self.db.add(self.test_ck)
        self.db.commit()

        # 创建测试卡记录
        self._create_test_card_records()

    def _create_test_card_records(self):
        """创建测试卡记录"""
        test_records = [
            # 成功记录 - 请求金额与实际金额一致
            {
                "merchant_order_id": "ORDER_001",
                "amount": 10000,  # 100元
                "actual_amount": 10000,  # 100元
                "status": "success",
                "walmart_ck_id": self.test_ck.id,
                "department_id": self.test_department.id
            },
            # 成功记录 - 实际金额高于请求金额
            {
                "merchant_order_id": "ORDER_002", 
                "amount": 5000,  # 50元
                "actual_amount": 5500,  # 55元
                "status": "success",
                "walmart_ck_id": self.test_ck.id,
                "department_id": self.test_department.id
            },
            # 成功记录 - 实际金额低于请求金额
            {
                "merchant_order_id": "ORDER_003",
                "amount": 8000,  # 80元
                "actual_amount": 7500,  # 75元
                "status": "success",
                "walmart_ck_id": self.test_ck.id,
                "department_id": self.test_department.id
            },
            # 失败记录
            {
                "merchant_order_id": "ORDER_004",
                "amount": 3000,  # 30元
                "actual_amount": None,
                "status": "failed",
                "walmart_ck_id": None,
                "department_id": None
            },
            # 待处理记录
            {
                "merchant_order_id": "ORDER_005",
                "amount": 2000,  # 20元
                "actual_amount": None,
                "status": "pending",
                "walmart_ck_id": None,
                "department_id": None
            }
        ]

        for i, record_data in enumerate(test_records):
            record = CardRecord(
                id=f"test_record_{i+1}",
                merchant_id=self.test_merchant.id,
                card_number=f"1234567890{i:02d}",
                request_id=f"req_{i+1}",
                created_at=datetime.now() - timedelta(hours=i),
                **record_data
            )
            self.db.add(record)
        
        self.db.commit()

    def test_amount_statistics_basic(self):
        """测试基础金额统计功能"""
        # 获取统计数据
        start_time = datetime.now() - timedelta(days=1)
        end_time = datetime.now() + timedelta(hours=1)
        
        stats = self.dashboard_service._get_amount_statistics(
            merchant_id=self.test_merchant.id,
            start_time=start_time,
            end_time=end_time
        )

        # 验证基础统计
        self.assertEqual(stats["total_requests"], 5)
        self.assertEqual(stats["success_count"], 3)
        self.assertEqual(stats["failed_count"], 1)
        self.assertEqual(stats["success_rate"], 60.0)

        # 验证请求金额统计
        expected_total_requested = 10000 + 5000 + 8000 + 3000 + 2000  # 280元
        self.assertEqual(stats["total_requested_amount"], expected_total_requested)
        self.assertEqual(stats["total_requested_amount_yuan"], 280.0)

        expected_success_requested = 10000 + 5000 + 8000  # 230元
        self.assertEqual(stats["success_requested_amount"], expected_success_requested)
        self.assertEqual(stats["success_requested_amount_yuan"], 230.0)

        # 验证实际金额统计
        expected_success_actual = 10000 + 5500 + 7500  # 230元
        self.assertEqual(stats["success_actual_amount"], expected_success_actual)
        self.assertEqual(stats["success_actual_amount_yuan"], 230.0)

    def test_amount_difference_analysis(self):
        """测试金额差异分析"""
        start_time = datetime.now() - timedelta(days=1)
        end_time = datetime.now() + timedelta(hours=1)
        
        stats = self.dashboard_service._get_amount_statistics(
            merchant_id=self.test_merchant.id,
            start_time=start_time,
            end_time=end_time
        )

        # 验证金额差异
        # 实际金额：100 + 55 + 75 = 230元
        # 请求金额：100 + 50 + 80 = 230元
        # 差异：0元
        self.assertEqual(stats["amount_difference"], 0)
        self.assertEqual(stats["amount_difference_yuan"], 0.0)

        # 验证金额准确率
        self.assertEqual(stats["amount_accuracy_rate"], 100.0)

    def test_backward_compatibility(self):
        """测试向后兼容性"""
        start_time = datetime.now() - timedelta(days=1)
        end_time = datetime.now() + timedelta(hours=1)
        
        stats = self.dashboard_service._get_amount_statistics(
            merchant_id=self.test_merchant.id,
            start_time=start_time,
            end_time=end_time
        )

        # 验证旧字段名仍然存在
        self.assertIn("total_amount", stats)
        self.assertIn("success_amount", stats)
        self.assertIn("failed_amount", stats)
        self.assertIn("total_amount_yuan", stats)
        self.assertIn("success_amount_yuan", stats)
        self.assertIn("failed_amount_yuan", stats)

        # 验证旧字段值与新字段值一致
        self.assertEqual(stats["total_amount"], stats["total_requested_amount"])
        self.assertEqual(stats["success_amount"], stats["success_requested_amount"])
        self.assertEqual(stats["failed_amount"], stats["failed_requested_amount"])

    def test_data_isolation(self):
        """测试数据权限隔离"""
        # 创建另一个商户的数据
        other_merchant = Merchant(
            name="其他商户",
            code="OTHER_MERCHANT",
            contact_person="其他联系人",
            contact_phone="13900139000",
            status=True
        )
        self.db.add(other_merchant)
        self.db.commit()

        # 为其他商户创建卡记录
        other_record = CardRecord(
            id="other_record_1",
            merchant_id=other_merchant.id,
            merchant_order_id="OTHER_ORDER_001",
            amount=15000,  # 150元
            actual_amount=15000,
            card_number="9876543210",
            request_id="other_req_1",
            status="success",
            created_at=datetime.now()
        )
        self.db.add(other_record)
        self.db.commit()

        # 测试商户数据隔离
        start_time = datetime.now() - timedelta(days=1)
        end_time = datetime.now() + timedelta(hours=1)
        
        # 查询测试商户数据
        test_stats = self.dashboard_service._get_amount_statistics(
            merchant_id=self.test_merchant.id,
            start_time=start_time,
            end_time=end_time
        )

        # 查询其他商户数据
        other_stats = self.dashboard_service._get_amount_statistics(
            merchant_id=other_merchant.id,
            start_time=start_time,
            end_time=end_time
        )

        # 验证数据隔离
        self.assertEqual(test_stats["total_requests"], 5)
        self.assertEqual(other_stats["total_requests"], 1)
        self.assertEqual(other_stats["success_actual_amount_yuan"], 150.0)

    def test_api_endpoint(self):
        """测试API端点"""
        # 创建测试用户
        test_user = User(
            username="test_merchant_admin",
            email="<EMAIL>",
            merchant_id=self.test_merchant.id,
            is_active=True
        )
        test_user.set_password("testpass123")
        self.db.add(test_user)
        self.db.commit()

        # 登录获取token
        login_response = self.client.post("/api/v1/auth/login", json={
            "username": "test_merchant_admin",
            "password": "testpass123"
        })
        self.assertEqual(login_response.status_code, 200)
        token = login_response.json()["data"]["access_token"]

        # 调用金额统计API
        headers = {"Authorization": f"Bearer {token}"}
        response = self.client.get(
            "/api/v1/dashboard/amount-statistics?time_range=today",
            headers=headers
        )

        self.assertEqual(response.status_code, 200)
        data = response.json()["data"]

        # 验证返回数据结构
        required_fields = [
            "total_requests", "success_count", "failed_count", "success_rate",
            "total_requested_amount_yuan", "success_actual_amount_yuan",
            "amount_difference_yuan", "amount_accuracy_rate"
        ]
        
        for field in required_fields:
            self.assertIn(field, data)

    def tearDown(self):
        """测试后清理"""
        super().tearDown()


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
