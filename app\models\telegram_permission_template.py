"""
Telegram权限模板模型
"""

from sqlalchemy import Column, BigInteger, String, Text, JSON, Boolean, ForeignKey
from sqlalchemy.orm import relationship

from app.models.base import BaseModel, TimestampMixin


class TelegramPermissionTemplate(BaseModel, TimestampMixin):
    """Telegram权限模板表"""
    
    __tablename__ = "telegram_permission_templates"
    
    # 基础字段
    template_name = Column(
        String(100),
        nullable=False,
        comment="模板名称"
    )
    template_code = Column(
        String(50),
        nullable=False,
        unique=True,
        index=True,
        comment="模板代码"
    )
    description = Column(
        Text,
        nullable=True,
        comment="模板描述"
    )
    settings = Column(
        JSON,
        nullable=False,
        comment="权限配置模板"
    )
    is_system = Column(
        Boolean,
        default=False,
        index=True,
        comment="是否为系统模板"
    )
    is_active = Column(
        Boolean,
        default=True,
        index=True,
        comment="是否启用"
    )
    
    # 操作人员字段
    created_by = Column(
        BigInteger,
        ForeignKey("users.id", ondelete="SET NULL"),
        nullable=True,
        comment="创建人ID"
    )
    updated_by = Column(
        BigInteger,
        ForeignKey("users.id", ondelete="SET NULL"),
        nullable=True,
        comment="更新人ID"
    )
    
    # 关联关系
    creator = relationship("User", foreign_keys=[created_by])
    updater = relationship("User", foreign_keys=[updated_by])
    
    def __repr__(self):
        return f"<TelegramPermissionTemplate(id={self.id}, template_code={self.template_code}, is_system={self.is_system})>"
    
    def get_permission_config(self) -> dict:
        """获取权限配置"""
        return self.settings.get('permissions', {}) if self.settings else {}
    
    def get_display_config(self) -> dict:
        """获取显示配置"""
        return self.settings.get('display_settings', {}) if self.settings else {}
    
    def get_notification_config(self) -> dict:
        """获取通知配置"""
        return self.settings.get('notification_settings', {}) if self.settings else {}
    
    def is_permission_allowed(self, permission_key: str) -> bool:
        """检查权限是否被允许"""
        permissions = self.get_permission_config()
        return permissions.get(permission_key, False)
    
    def get_rate_limit(self, limit_type: str) -> int:
        """获取频率限制"""
        permissions = self.get_permission_config()
        rate_limit = permissions.get('rate_limit', {})
        
        default_limits = {
            'commands_per_minute': 10,
            'queries_per_hour': 100
        }
        
        return rate_limit.get(limit_type, default_limits.get(limit_type, 0))
    
    def is_query_allowed(self, query_type: str) -> bool:
        """检查查询类型是否被允许"""
        permissions = self.get_permission_config()
        query_permissions = permissions.get('query_permissions', {})
        return query_permissions.get(query_type, False)
    
    def validate_settings(self) -> tuple[bool, list]:
        """验证模板配置的有效性"""
        errors = []
        
        if not self.settings:
            errors.append("模板配置不能为空")
            return False, errors
        
        if not isinstance(self.settings, dict):
            errors.append("模板配置必须是字典格式")
            return False, errors
        
        # 验证必需的配置节
        required_sections = ['permissions']
        for section in required_sections:
            if section not in self.settings:
                errors.append(f"缺少必需的配置节：{section}")
        
        # 验证权限配置
        if 'permissions' in self.settings:
            permission_errors = self._validate_permissions(self.settings['permissions'])
            errors.extend(permission_errors)
        
        # 验证显示配置
        if 'display_settings' in self.settings:
            display_errors = self._validate_display_settings(self.settings['display_settings'])
            errors.extend(display_errors)
        
        return len(errors) == 0, errors
    
    def _validate_permissions(self, permissions: dict) -> list:
        """验证权限配置"""
        errors = []
        
        # 验证必需的权限字段
        required_fields = ['allow_all_members', 'require_user_verification']
        for field in required_fields:
            if field not in permissions:
                errors.append(f"权限配置缺少必需字段：{field}")
            elif not isinstance(permissions[field], bool):
                errors.append(f"权限配置 {field} 必须是布尔值")
        
        # 验证管理员专用命令
        if 'admin_only_commands' in permissions:
            if not isinstance(permissions['admin_only_commands'], list):
                errors.append("admin_only_commands 必须是数组")
        
        # 验证频率限制
        if 'rate_limit' in permissions:
            rate_limit = permissions['rate_limit']
            if not isinstance(rate_limit, dict):
                errors.append("rate_limit 必须是对象")
            else:
                if 'commands_per_minute' in rate_limit:
                    if not isinstance(rate_limit['commands_per_minute'], int) or rate_limit['commands_per_minute'] < 1:
                        errors.append("commands_per_minute 必须是正整数")
                
                if 'queries_per_hour' in rate_limit:
                    if not isinstance(rate_limit['queries_per_hour'], int) or rate_limit['queries_per_hour'] < 1:
                        errors.append("queries_per_hour 必须是正整数")
        
        # 验证查询权限
        if 'query_permissions' in permissions:
            query_permissions = permissions['query_permissions']
            if not isinstance(query_permissions, dict):
                errors.append("query_permissions 必须是对象")
            else:
                query_types = ['daily_stats', 'weekly_stats', 'monthly_stats', 'custom_range', 'detailed_data']
                for query_type in query_types:
                    if query_type in query_permissions and not isinstance(query_permissions[query_type], bool):
                        errors.append(f"查询权限 {query_type} 必须是布尔值")
        
        return errors
    
    def _validate_display_settings(self, display: dict) -> list:
        """验证显示配置"""
        errors = []
        
        # 验证布尔值字段
        bool_fields = ['show_amount', 'show_details']
        for field in bool_fields:
            if field in display and not isinstance(display[field], bool):
                errors.append(f"显示配置 {field} 必须是布尔值")
        
        # 验证小数位数
        if 'decimal_places' in display:
            if not isinstance(display['decimal_places'], int) or display['decimal_places'] < 0 or display['decimal_places'] > 4:
                errors.append("小数位数必须是0-4之间的整数")
        
        return errors
    
    def clone_template(self, new_name: str, new_code: str, created_by: int = None):
        """克隆模板"""
        return TelegramPermissionTemplate(
            template_name=new_name,
            template_code=new_code,
            description=f"基于 {self.template_name} 克隆",
            settings=self.settings.copy() if self.settings else {},
            is_system=False,
            created_by=created_by,
            updated_by=created_by
        )
    
    @classmethod
    def get_by_code(cls, db, template_code: str):
        """根据模板代码获取模板"""
        return db.query(cls).filter_by(template_code=template_code, is_active=True).first()
    
    @classmethod
    def get_system_templates(cls, db):
        """获取系统模板列表"""
        return db.query(cls).filter_by(is_system=True, is_active=True).all()
    
    @classmethod
    def get_custom_templates(cls, db):
        """获取自定义模板列表"""
        return db.query(cls).filter_by(is_system=False, is_active=True).all()
    
    @classmethod
    def get_active_templates(cls, db):
        """获取所有活跃模板"""
        return db.query(cls).filter_by(is_active=True).order_by(cls.is_system.desc(), cls.template_name).all()
    
    @classmethod
    def create_system_template(cls, db, template_code: str, template_name: str, description: str, settings: dict):
        """创建系统模板"""
        template = cls(
            template_name=template_name,
            template_code=template_code,
            description=description,
            settings=settings,
            is_system=True,
            is_active=True
        )
        
        # 验证配置
        is_valid, errors = template.validate_settings()
        if not is_valid:
            raise ValueError(f"模板配置无效：{'; '.join(errors)}")
        
        db.add(template)
        db.commit()
        db.refresh(template)
        
        return template
    
    @classmethod
    def get_default_templates(cls) -> dict:
        """获取默认模板配置"""
        return {
            'strict': {
                'template_name': '严格模式',
                'description': '只允许已验证用户查询基础统计数据，管理员执行管理操作',
                'settings': {
                    'permissions': {
                        'allow_all_members': False,
                        'require_user_verification': True,
                        'admin_only_commands': ['bind', 'unbind', 'settings'],
                        'rate_limit': {
                            'commands_per_minute': 5,
                            'queries_per_hour': 50
                        },
                        'query_permissions': {
                            'daily_stats': True,
                            'weekly_stats': True,
                            'monthly_stats': False,
                            'custom_range': False,
                            'detailed_data': False
                        }
                    },
                    'display_settings': {
                        'show_amount': True,
                        'show_details': False,
                        'decimal_places': 2
                    }
                }
            },
            'open': {
                'template_name': '开放模式',
                'description': '允许所有群成员查询基础统计数据，无需个人验证',
                'settings': {
                    'permissions': {
                        'allow_all_members': True,
                        'require_user_verification': False,
                        'admin_only_commands': ['bind', 'unbind', 'settings'],
                        'rate_limit': {
                            'commands_per_minute': 10,
                            'queries_per_hour': 100
                        },
                        'query_permissions': {
                            'daily_stats': True,
                            'weekly_stats': True,
                            'monthly_stats': True,
                            'custom_range': False,
                            'detailed_data': False
                        }
                    },
                    'display_settings': {
                        'show_amount': True,
                        'show_details': False,
                        'decimal_places': 2
                    }
                }
            },
            'full_open': {
                'template_name': '完全开放',
                'description': '允许所有群成员查询所有统计数据，包括详细数据和自定义时间范围',
                'settings': {
                    'permissions': {
                        'allow_all_members': True,
                        'require_user_verification': False,
                        'admin_only_commands': ['bind', 'unbind', 'settings'],
                        'rate_limit': {
                            'commands_per_minute': 15,
                            'queries_per_hour': 200
                        },
                        'query_permissions': {
                            'daily_stats': True,
                            'weekly_stats': True,
                            'monthly_stats': True,
                            'custom_range': True,
                            'detailed_data': True
                        }
                    },
                    'display_settings': {
                        'show_amount': True,
                        'show_details': True,
                        'decimal_places': 2
                    }
                }
            },
            'readonly': {
                'template_name': '只读模式',
                'description': '只允许查看今日统计，不允许其他操作',
                'settings': {
                    'permissions': {
                        'allow_all_members': True,
                        'require_user_verification': False,
                        'admin_only_commands': ['bind', 'unbind', 'settings', 'stats_week', 'stats_month', 'stats_custom'],
                        'rate_limit': {
                            'commands_per_minute': 3,
                            'queries_per_hour': 20
                        },
                        'query_permissions': {
                            'daily_stats': True,
                            'weekly_stats': False,
                            'monthly_stats': False,
                            'custom_range': False,
                            'detailed_data': False
                        }
                    },
                    'display_settings': {
                        'show_amount': False,
                        'show_details': False,
                        'decimal_places': 0
                    }
                }
            }
        }
