package metrics

import (
	"sync"
	"sync/atomic"
	"time"
)

// Metrics 性能指标收集器
type Metrics struct {
	// 请求指标
	RequestsTotal     int64 `json:"requests_total"`
	RequestsSuccess   int64 `json:"requests_success"`
	RequestsError     int64 `json:"requests_error"`
	RequestsDropped   int64 `json:"requests_dropped"`
	
	// 批量处理指标
	BatchesProcessed  int64 `json:"batches_processed"`
	BatchSizeTotal    int64 `json:"batch_size_total"`
	BatchSizeAvg      float64 `json:"batch_size_avg"`
	
	// 数据库指标
	DatabaseWrites    int64 `json:"database_writes"`
	DatabaseErrors    int64 `json:"database_errors"`
	DatabaseLatency   *LatencyMetrics `json:"database_latency"`
	
	// Redis指标
	RedisWrites       int64 `json:"redis_writes"`
	RedisErrors       int64 `json:"redis_errors"`
	RedisLatency      *LatencyMetrics `json:"redis_latency"`
	
	// 队列指标
	QueuePublishes    int64 `json:"queue_publishes"`
	QueueErrors       int64 `json:"queue_errors"`
	QueueLatency      *LatencyMetrics `json:"queue_latency"`
	
	// 系统指标
	GoroutineCount    int64 `json:"goroutine_count"`
	MemoryUsage       int64 `json:"memory_usage"`
	CPUUsage          float64 `json:"cpu_usage"`
	
	// 启动时间
	StartTime         time.Time `json:"start_time"`
	
	mutex sync.RWMutex
}

// LatencyMetrics 延迟指标
type LatencyMetrics struct {
	Count    int64         `json:"count"`
	Total    time.Duration `json:"total"`
	Min      time.Duration `json:"min"`
	Max      time.Duration `json:"max"`
	Average  time.Duration `json:"average"`
	P50      time.Duration `json:"p50"`
	P95      time.Duration `json:"p95"`
	P99      time.Duration `json:"p99"`
	
	// 用于计算百分位数的样本
	samples []time.Duration
	mutex   sync.RWMutex
}

// NewMetrics 创建新的指标收集器
func NewMetrics() *Metrics {
	return &Metrics{
		DatabaseLatency: NewLatencyMetrics(),
		RedisLatency:    NewLatencyMetrics(),
		QueueLatency:    NewLatencyMetrics(),
		StartTime:       time.Now(),
	}
}

// NewLatencyMetrics 创建新的延迟指标
func NewLatencyMetrics() *LatencyMetrics {
	return &LatencyMetrics{
		samples: make([]time.Duration, 0, 1000), // 保留最近1000个样本
		Min:     time.Duration(^uint64(0) >> 1), // 最大值作为初始最小值
	}
}

// IncRequestsTotal 增加总请求数
func (m *Metrics) IncRequestsTotal() {
	atomic.AddInt64(&m.RequestsTotal, 1)
}

// IncRequestsSuccess 增加成功请求数
func (m *Metrics) IncRequestsSuccess() {
	atomic.AddInt64(&m.RequestsSuccess, 1)
}

// IncRequestsError 增加错误请求数
func (m *Metrics) IncRequestsError() {
	atomic.AddInt64(&m.RequestsError, 1)
}

// IncRequestsDropped 增加丢弃请求数
func (m *Metrics) IncRequestsDropped() {
	atomic.AddInt64(&m.RequestsDropped, 1)
}

// IncBatchesProcessed 增加批量处理数
func (m *Metrics) IncBatchesProcessed(batchSize int) {
	atomic.AddInt64(&m.BatchesProcessed, 1)
	atomic.AddInt64(&m.BatchSizeTotal, int64(batchSize))
	
	// 计算平均批量大小
	batches := atomic.LoadInt64(&m.BatchesProcessed)
	total := atomic.LoadInt64(&m.BatchSizeTotal)
	if batches > 0 {
		m.mutex.Lock()
		m.BatchSizeAvg = float64(total) / float64(batches)
		m.mutex.Unlock()
	}
}

// IncDatabaseWrites 增加数据库写入数
func (m *Metrics) IncDatabaseWrites(count int64) {
	atomic.AddInt64(&m.DatabaseWrites, count)
}

// IncDatabaseErrors 增加数据库错误数
func (m *Metrics) IncDatabaseErrors() {
	atomic.AddInt64(&m.DatabaseErrors, 1)
}

// RecordDatabaseLatency 记录数据库延迟
func (m *Metrics) RecordDatabaseLatency(duration time.Duration) {
	m.DatabaseLatency.Record(duration)
}

// IncRedisWrites 增加Redis写入数
func (m *Metrics) IncRedisWrites(count int64) {
	atomic.AddInt64(&m.RedisWrites, count)
}

// IncRedisErrors 增加Redis错误数
func (m *Metrics) IncRedisErrors() {
	atomic.AddInt64(&m.RedisErrors, 1)
}

// RecordRedisLatency 记录Redis延迟
func (m *Metrics) RecordRedisLatency(duration time.Duration) {
	m.RedisLatency.Record(duration)
}

// IncQueuePublishes 增加队列发布数
func (m *Metrics) IncQueuePublishes(count int64) {
	atomic.AddInt64(&m.QueuePublishes, count)
}

// IncQueueErrors 增加队列错误数
func (m *Metrics) IncQueueErrors() {
	atomic.AddInt64(&m.QueueErrors, 1)
}

// RecordQueueLatency 记录队列延迟
func (m *Metrics) RecordQueueLatency(duration time.Duration) {
	m.QueueLatency.Record(duration)
}

// UpdateSystemMetrics 更新系统指标
func (m *Metrics) UpdateSystemMetrics(goroutines int64, memory int64, cpu float64) {
	atomic.StoreInt64(&m.GoroutineCount, goroutines)
	atomic.StoreInt64(&m.MemoryUsage, memory)
	
	m.mutex.Lock()
	m.CPUUsage = cpu
	m.mutex.Unlock()
}

// Record 记录延迟样本
func (lm *LatencyMetrics) Record(duration time.Duration) {
	lm.mutex.Lock()
	defer lm.mutex.Unlock()
	
	atomic.AddInt64(&lm.Count, 1)
	lm.Total += duration
	
	// 更新最小值和最大值
	if duration < lm.Min {
		lm.Min = duration
	}
	if duration > lm.Max {
		lm.Max = duration
	}
	
	// 计算平均值
	count := atomic.LoadInt64(&lm.Count)
	lm.Average = lm.Total / time.Duration(count)
	
	// 添加样本用于百分位数计算
	if len(lm.samples) >= cap(lm.samples) {
		// 移除最旧的样本
		copy(lm.samples, lm.samples[1:])
		lm.samples = lm.samples[:len(lm.samples)-1]
	}
	lm.samples = append(lm.samples, duration)
	
	// 计算百分位数
	lm.calculatePercentiles()
}

// calculatePercentiles 计算百分位数
func (lm *LatencyMetrics) calculatePercentiles() {
	if len(lm.samples) == 0 {
		return
	}
	
	// 简单的百分位数计算（实际应用中可以使用更高效的算法）
	samples := make([]time.Duration, len(lm.samples))
	copy(samples, lm.samples)
	
	// 简单排序（对于小样本集合）
	for i := 0; i < len(samples); i++ {
		for j := i + 1; j < len(samples); j++ {
			if samples[i] > samples[j] {
				samples[i], samples[j] = samples[j], samples[i]
			}
		}
	}
	
	// 计算百分位数
	n := len(samples)
	if n > 0 {
		lm.P50 = samples[n*50/100]
		lm.P95 = samples[n*95/100]
		lm.P99 = samples[n*99/100]
	}
}

// GetSnapshot 获取指标快照
func (m *Metrics) GetSnapshot() map[string]interface{} {
	m.mutex.RLock()
	defer m.mutex.RUnlock()
	
	return map[string]interface{}{
		"requests": map[string]interface{}{
			"total":   atomic.LoadInt64(&m.RequestsTotal),
			"success": atomic.LoadInt64(&m.RequestsSuccess),
			"error":   atomic.LoadInt64(&m.RequestsError),
			"dropped": atomic.LoadInt64(&m.RequestsDropped),
		},
		"batches": map[string]interface{}{
			"processed": atomic.LoadInt64(&m.BatchesProcessed),
			"size_avg":  m.BatchSizeAvg,
		},
		"database": map[string]interface{}{
			"writes":  atomic.LoadInt64(&m.DatabaseWrites),
			"errors":  atomic.LoadInt64(&m.DatabaseErrors),
			"latency": m.DatabaseLatency.GetSnapshot(),
		},
		"redis": map[string]interface{}{
			"writes":  atomic.LoadInt64(&m.RedisWrites),
			"errors":  atomic.LoadInt64(&m.RedisErrors),
			"latency": m.RedisLatency.GetSnapshot(),
		},
		"queue": map[string]interface{}{
			"publishes": atomic.LoadInt64(&m.QueuePublishes),
			"errors":    atomic.LoadInt64(&m.QueueErrors),
			"latency":   m.QueueLatency.GetSnapshot(),
		},
		"system": map[string]interface{}{
			"goroutines":   atomic.LoadInt64(&m.GoroutineCount),
			"memory_usage": atomic.LoadInt64(&m.MemoryUsage),
			"cpu_usage":    m.CPUUsage,
		},
		"uptime": time.Since(m.StartTime).String(),
	}
}

// GetSnapshot 获取延迟指标快照
func (lm *LatencyMetrics) GetSnapshot() map[string]interface{} {
	lm.mutex.RLock()
	defer lm.mutex.RUnlock()
	
	return map[string]interface{}{
		"count":   atomic.LoadInt64(&lm.Count),
		"total":   lm.Total.String(),
		"min":     lm.Min.String(),
		"max":     lm.Max.String(),
		"average": lm.Average.String(),
		"p50":     lm.P50.String(),
		"p95":     lm.P95.String(),
		"p99":     lm.P99.String(),
	}
}

// Reset 重置指标
func (m *Metrics) Reset() {
	atomic.StoreInt64(&m.RequestsTotal, 0)
	atomic.StoreInt64(&m.RequestsSuccess, 0)
	atomic.StoreInt64(&m.RequestsError, 0)
	atomic.StoreInt64(&m.RequestsDropped, 0)
	atomic.StoreInt64(&m.BatchesProcessed, 0)
	atomic.StoreInt64(&m.BatchSizeTotal, 0)
	atomic.StoreInt64(&m.DatabaseWrites, 0)
	atomic.StoreInt64(&m.DatabaseErrors, 0)
	atomic.StoreInt64(&m.RedisWrites, 0)
	atomic.StoreInt64(&m.RedisErrors, 0)
	atomic.StoreInt64(&m.QueuePublishes, 0)
	atomic.StoreInt64(&m.QueueErrors, 0)
	
	m.mutex.Lock()
	m.BatchSizeAvg = 0
	m.CPUUsage = 0
	m.mutex.Unlock()
	
	m.DatabaseLatency.Reset()
	m.RedisLatency.Reset()
	m.QueueLatency.Reset()
}

// Reset 重置延迟指标
func (lm *LatencyMetrics) Reset() {
	lm.mutex.Lock()
	defer lm.mutex.Unlock()
	
	atomic.StoreInt64(&lm.Count, 0)
	lm.Total = 0
	lm.Min = time.Duration(^uint64(0) >> 1)
	lm.Max = 0
	lm.Average = 0
	lm.P50 = 0
	lm.P95 = 0
	lm.P99 = 0
	lm.samples = lm.samples[:0]
}
