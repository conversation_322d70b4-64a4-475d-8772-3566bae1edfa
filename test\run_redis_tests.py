#!/usr/bin/env python3
"""
Redis CK优化测试运行器
统一运行所有Redis相关测试
"""

import sys
import os
import subprocess
import argparse
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))


def run_test(test_path: str, test_name: str) -> bool:
    """运行单个测试"""
    print(f"\n{'='*60}")
    print(f"🚀 运行测试: {test_name}")
    print(f"📁 测试文件: {test_path}")
    print(f"{'='*60}")
    
    try:
        # 使用subprocess运行测试
        result = subprocess.run(
            [sys.executable, test_path],
            cwd=str(project_root),
            capture_output=True,
            text=True,
            timeout=120  # 2分钟超时
        )
        
        # 输出结果
        if result.stdout:
            print(result.stdout)
        if result.stderr:
            print("错误输出:", result.stderr)
        
        success = result.returncode == 0
        status = "✅ 通过" if success else "❌ 失败"
        print(f"\n📊 测试结果: {status}")
        
        return success
        
    except subprocess.TimeoutExpired:
        print("❌ 测试超时")
        return False
    except Exception as e:
        print(f"❌ 测试执行异常: {e}")
        return False


def run_pytest_tests(test_dir: str, test_name: str) -> bool:
    """使用pytest运行测试"""
    print(f"\n{'='*60}")
    print(f"🧪 运行pytest测试: {test_name}")
    print(f"📁 测试目录: {test_dir}")
    print(f"{'='*60}")
    
    try:
        # 使用pytest运行测试
        result = subprocess.run(
            [sys.executable, "-m", "pytest", test_dir, "-v", "--tb=short"],
            cwd=str(project_root),
            capture_output=True,
            text=True,
            timeout=120
        )
        
        # 输出结果
        if result.stdout:
            print(result.stdout)
        if result.stderr:
            print("错误输出:", result.stderr)
        
        success = result.returncode == 0
        status = "✅ 通过" if success else "❌ 失败"
        print(f"\n📊 pytest结果: {status}")
        
        return success
        
    except subprocess.TimeoutExpired:
        print("❌ pytest测试超时")
        return False
    except Exception as e:
        print(f"❌ pytest执行异常: {e}")
        return False


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="Redis CK优化测试运行器")
    parser.add_argument("--basic", action="store_true", help="只运行基础测试")
    parser.add_argument("--integration", action="store_true", help="只运行集成测试")
    parser.add_argument("--health", action="store_true", help="只运行健康检查测试")
    parser.add_argument("--pytest", action="store_true", help="使用pytest运行测试")
    parser.add_argument("--all", action="store_true", help="运行所有测试（默认）")
    
    args = parser.parse_args()
    
    # 如果没有指定特定测试，默认运行所有测试
    if not any([args.basic, args.integration, args.health]):
        args.all = True
    
    print("🚀 Redis CK优化测试套件")
    print("=" * 60)
    
    # 定义测试列表
    tests = []
    
    if args.basic or args.all:
        tests.append(("test/redis/test_redis_basic.py", "Redis基础功能测试"))
    
    if args.integration or args.all:
        tests.append(("test/integration/test_redis_ck_integration.py", "Redis CK集成测试"))
    
    if args.health or args.all:
        tests.append(("test/redis/test_redis_health_check.py", "Redis健康检查测试"))
    
    # 运行测试
    results = []
    
    if args.pytest:
        # 使用pytest运行
        if args.basic or args.all:
            success = run_pytest_tests("test/redis/test_redis_basic.py", "Redis基础功能测试")
            results.append(("Redis基础功能测试", success))
        
        if args.integration or args.all:
            success = run_pytest_tests("test/integration/test_redis_ck_integration.py", "Redis CK集成测试")
            results.append(("Redis CK集成测试", success))
        
        if args.health or args.all:
            success = run_pytest_tests("test/redis/test_redis_health_check.py", "Redis健康检查测试")
            results.append(("Redis健康检查测试", success))
    else:
        # 直接运行Python脚本
        for test_path, test_name in tests:
            success = run_test(test_path, test_name)
            results.append((test_name, success))
    
    # 输出总结
    print(f"\n{'='*60}")
    print("📊 测试结果总结")
    print(f"{'='*60}")
    
    success_count = 0
    for test_name, success in results:
        status = "✅ 通过" if success else "❌ 失败"
        print(f"   {test_name}: {status}")
        if success:
            success_count += 1
    
    print(f"\n🎯 总体结果: {success_count}/{len(results)} 项测试通过")
    
    if success_count == len(results):
        print("🎉 所有Redis测试通过！")
        return True
    elif success_count >= len(results) - 1:
        print("⚠️ 大部分Redis测试通过，有少量问题需要关注")
        return True
    else:
        print("❌ Redis测试存在问题，需要检查配置和环境")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
