# 沃尔玛绑卡系统测试套件

这是一个全面、规范、结构化的测试框架，用于测试沃尔玛绑卡系统的所有功能模块。

## 🎯 测试目标

确保沃尔玛绑卡系统的：
- ✅ 功能完整性
- 🔒 数据安全性
- 🛡️ 权限控制
- 🔐 API安全
- 📊 数据隔离

## 📁 目录结构

```
test/
├── __init__.py                 # 测试包初始化
├── conftest.py                 # 全局测试配置和工具
├── run_all_tests.py           # 主测试运行器
├── README.md                  # 本文档
├── reports/                   # 测试报告目录
├── auth/                      # 认证模块测试
│   ├── __init__.py
│   └── test_auth.py          # 登录、登出、Token验证
├── users/                     # 用户管理测试
│   ├── __init__.py
│   └── test_users_crud.py    # 用户CRUD操作
├── merchants/                 # 商户管理测试
│   ├── __init__.py
│   └── test_merchants_crud.py # 商户CRUD操作
├── departments/               # 部门管理测试
│   ├── __init__.py
│   └── test_departments_crud.py # 部门CRUD操作
├── roles/                     # 角色权限测试
│   ├── __init__.py
│   └── test_roles_permissions.py # 角色权限验证
└── security/                  # 安全测试
    ├── __init__.py
    ├── test_data_isolation.py # 数据隔离测试
    └── test_api_security.py   # API安全测试
```

## 🚀 快速开始

### 环境要求

1. **Python 3.7+**
2. **依赖包**：
   ```bash
   pip install requests
   ```

3. **测试服务器**：
   - 地址：`http://localhost:20000`
   - 确保后端服务正在运行
   - 数据库已正确初始化

### 测试账号

- **超级管理员**：
  - 用户名：`admin`
  - 密码：`7c222fb2927d828af22f592134e8932480637c0d`

- **商户管理员A**：
  - 用户名：`test1`
  - 密码：`12345678`

- **商户管理员B**：
  - 用户名：`test_merchant_b`
  - 密码：`12345678`

## 🧪 运行测试

### 运行完整测试套件

```bash
# 进入测试目录
cd test

# 运行所有测试
python run_all_tests.py
```

### 运行单个模块测试

```bash
# 认证模块测试
python auth/test_auth.py

# 用户管理测试
python users/test_users_crud.py

# 商户管理测试
python merchants/test_merchants_crud.py

# 部门管理测试
python departments/test_departments_crud.py

# 角色权限测试
python roles/test_roles_permissions.py

# 数据隔离测试
python security/test_data_isolation.py

# API安全测试
python security/test_api_security.py
```

## 📊 测试覆盖范围

### 1. 认证模块 (`/api/v1/auth/*`)
- ✅ 有效用户登录测试
- ✅ 无效用户登录拒绝
- ✅ Token验证机制
- ✅ 登出功能
- ✅ 密码安全性（SQL注入防护）

### 2. 用户管理 (`/api/v1/users/*`)
- ✅ 用户CRUD操作
- ✅ 用户权限验证
- ✅ 用户数据隔离
- ✅ 输入验证

### 3. 商户管理 (`/api/v1/merchants/*`)
- ✅ 商户CRUD操作
- ✅ 商户数据隔离
- ✅ 跨商户访问防护
- ✅ 权限控制

### 4. 部门管理 (`/api/v1/departments/*`)
- ✅ 部门CRUD操作
- ✅ 部门层级结构
- ✅ 部门数据隔离
- ✅ 权限验证

### 5. 角色权限 (`/api/v1/roles/*`, `/api/v1/permissions/*`)
- ✅ 角色列表获取
- ✅ 权限列表获取
- ✅ 菜单权限验证
- ✅ API权限控制
- ✅ 动态权限系统

### 6. 数据隔离
- ✅ 商户间数据隔离
- ✅ 部门间数据隔离
- ✅ 跨商户访问防护
- ✅ 敏感数据过滤

### 7. API安全
- ✅ SQL注入防护
- ✅ XSS防护
- ✅ 权限绕过防护
- ✅ 参数污染防护
- ✅ 输入验证
- ✅ Token安全性

## 📈 测试报告

测试完成后会在 `test/reports/` 目录生成以下报告：

- **详细JSON报告**：`walmart_comprehensive_test_report_YYYYMMDD_HHMMSS.json`
- **简化文本报告**：`walmart_test_summary_YYYYMMDD_HHMMSS.txt`

### 报告内容包括：
- 📊 测试统计信息
- 📋 模块测试结果
- ❌ 失败测试详情
- ⏱️ 测试耗时统计
- 🔍 测试环境信息

## 🛠️ 扩展测试

### 添加新的测试模块

1. 在相应目录下创建测试文件
2. 继承 `TestBase` 类
3. 实现 `run_all_tests()` 方法
4. 在 `run_all_tests.py` 中注册新模块

### 测试类模板

```python
#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
新模块测试
"""

import sys
import os
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.dirname(__file__))))

from test.conftest import TestBase, format_test_result, print_test_summary

class NewModuleTestSuite(TestBase):
    def __init__(self):
        super().__init__()
        self.results = []
    
    def test_something(self):
        # 实现具体测试
        pass
    
    def run_all_tests(self):
        print("🚀 开始新模块测试")
        start_time = time.time()
        
        self.test_something()
        
        end_time = time.time()
        print_test_summary(self.results)
        return self.results

if __name__ == "__main__":
    test_suite = NewModuleTestSuite()
    results = test_suite.run_all_tests()
```

## 🔧 配置说明

### 测试配置 (`conftest.py`)

```python
TEST_CONFIG = {
    "base_url": "http://localhost:20000",
    "api_prefix": "/api/v1",
    "timeout": 30,
    "retry_count": 3,
    "retry_delay": 1,
    "encoding": "utf-8"
}
```

### 修改测试服务器地址

如需修改测试服务器地址，请编辑 `test/conftest.py` 文件中的 `TEST_CONFIG["base_url"]`。

## ⚠️ 注意事项

1. **测试数据清理**：测试会创建临时数据，运行完成后会自动清理
2. **并发测试**：避免同时运行多个测试实例，可能导致数据冲突
3. **网络连接**：确保测试环境网络连接正常
4. **权限要求**：某些测试需要特定权限，确保测试账号配置正确
5. **数据库状态**：测试前确保数据库处于正常状态

## 🐛 故障排除

### 常见问题

1. **连接失败**
   ```
   ❌ 登录请求失败: Connection refused
   ```
   **解决方案**：确保后端服务在 `localhost:20000` 运行

2. **认证失败**
   ```
   ❌ 用户 admin 登录失败
   ```
   **解决方案**：检查测试账号密码是否正确

3. **权限测试失败**
   ```
   ❌ 商户管理员不应该有系统菜单权限
   ```
   **解决方案**：检查角色权限配置是否正确

4. **编码问题**
   ```
   UnicodeDecodeError
   ```
   **解决方案**：确保系统支持UTF-8编码

## 📞 技术支持

如遇到问题，请检查：
1. 测试环境配置
2. 网络连接状态
3. 数据库初始化状态
4. 后端服务运行状态

---

**测试框架版本**：v1.0  
**最后更新**：2025-01-09  
**维护团队**：沃尔玛绑卡系统开发团队
