from typing import Any, List
from fastapi import APIRouter, Depends, HTTPException, Query, Path, status
from sqlalchemy.orm import Session

from app.api import deps
from app.core.logging import get_logger
from app.models.user import User
from app.models.menu import Menu
from app.services.permission_service import permission_service

logger = get_logger(__name__)

router = APIRouter()


@router.get("/user-menus", response_model=List[dict])
def get_current_user_menus(
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_active_user),
) -> Any:
    """
    获取当前用户的菜单列表。
    返回树形结构的菜单数据。

    权限要求:
    - "api:menus:user-menus": 获取用户菜单权限
    """
    # 权限检查 - 验证用户是否有获取菜单的基本权限
    has_permission = permission_service.check_user_permission(
        current_user, "api:menus:user-menus", db
    )
    if not has_permission:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="没有获取用户菜单的权限",
        )

    # 获取用户角色名称
    role_names = [role.name for role in current_user.roles] if current_user.roles else ["无角色"]
    logger.info(
        f"用户 {current_user.username} (角色: {', '.join(role_names)}) 请求获取菜单"
    )

    try:
        from app.services.menu_service import MenuService

        # 直接使用菜单服务获取用户菜单
        menu_service = MenuService()
        menus = menu_service.get_user_menus(db, current_user)
        logger.info(f"成功获取用户菜单，数量: {len(menus)}")
        # logger.info(f"菜单内容: {menus}")
        return menus

    except Exception as e:
        logger.error(f"获取用户菜单失败: {e}", exc_info=True)
        # 抛出异常
        raise HTTPException(
            status_code=500,
            detail="获取用户菜单失败",
        )


@router.get("/", response_model=List[dict])
async def read_menus(
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_active_user),
    is_tree: bool = Query(False, description="是否返回树形结构"),
    visible_only: bool = Query(True, description="是否只返回可见菜单"),
):
    """
    获取菜单列表

    权限要求:
    - "menu:view": 查看菜单列表
    """
    # 检查权限
    has_permission = permission_service.check_user_permission(
        current_user, "menu:view", db
    )
    if not has_permission:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="没有查看菜单的权限",
        )

    # 获取菜单，根据visible_only参数决定是否过滤
    query = db.query(Menu)
    if visible_only:
        query = query.filter(Menu.is_visible == True)
    menus = query.all()

    # 如果需要树形结构，只返回顶级菜单，子菜单通过关系获取
    if is_tree:
        # 获取顶级菜单（parent_id为空的菜单）
        root_query = db.query(Menu).filter(Menu.parent_id.is_(None))
        if visible_only:
            root_query = root_query.filter(Menu.is_visible == True)
        root_menus = root_query.all()
        return [menu.get_tree() for menu in root_menus]

    # 否则返回平铺列表
    return [menu.to_dict(include_children=False) for menu in menus]


@router.post("/", response_model=dict)
async def create_menu(
    *,
    db: Session = Depends(deps.get_db),
    menu_in: dict,
    current_user: User = Depends(deps.get_current_active_user),
):
    """
    创建新菜单

    权限要求:
    - "menu:create": 创建菜单
    """
    # 检查权限
    has_permission = permission_service.check_user_permission(
        current_user, "menu:create", db
    )
    if not has_permission:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="没有创建菜单的权限",
        )

    # 检查菜单代码是否已存在
    existing_menu = db.query(Menu).filter(Menu.code == menu_in["code"]).first()
    if existing_menu:
        raise HTTPException(
            status_code=400,
            detail="菜单代码已存在",
        )

    # 检查父菜单是否存在
    if menu_in.get("parent_id"):
        parent_menu = db.query(Menu).filter(Menu.id == menu_in["parent_id"]).first()
        if not parent_menu:
            raise HTTPException(
                status_code=404,
                detail="父菜单不存在",
            )

    # 创建菜单
    menu = Menu(**menu_in)
    db.add(menu)
    db.commit()
    db.refresh(menu)

    return menu.to_dict()


@router.get("/{menu_id}", response_model=dict)
async def read_menu(
    *,
    db: Session = Depends(deps.get_db),
    menu_id: int = Path(..., gt=0),
    current_user: User = Depends(deps.get_current_active_user),
):
    """
    获取菜单详情

    权限要求:
    - "menu:view": 查看菜单详情
    """
    # 检查权限
    has_permission = permission_service.check_user_permission(
        current_user, "menu:view", db
    )
    if not has_permission:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="没有查看菜单的权限",
        )

    menu = db.query(Menu).filter(Menu.id == menu_id).first()
    if not menu:
        raise HTTPException(
            status_code=404,
            detail="菜单不存在",
        )

    return menu.to_dict()


@router.put("/{menu_id}", response_model=dict)
async def update_menu(
    *,
    db: Session = Depends(deps.get_db),
    menu_id: int = Path(..., gt=0),
    menu_in: dict,
    current_user: User = Depends(deps.get_current_active_user),
):
    """
    更新菜单信息

    权限要求:
    - "menu:edit": 编辑菜单信息
    """
    # 检查权限
    _check_menu_edit_permission(current_user, db)

    # 获取并验证菜单
    menu = _get_menu_or_404(db, menu_id)

    # 验证菜单更新数据
    _validate_menu_update(db, menu_id, menu_in, menu)

    # 更新菜单
    _update_menu_fields(menu, menu_in)

    db.commit()
    db.refresh(menu)

    return menu.to_dict()


def _check_menu_edit_permission(current_user: User, db: Session):
    """检查菜单编辑权限"""
    has_permission = permission_service.check_user_permission(
        current_user, "menu:edit", db
    )
    if not has_permission:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="没有编辑菜单的权限",
        )


def _get_menu_or_404(db: Session, menu_id: int) -> Menu:
    """获取菜单或返回404错误"""
    menu = db.query(Menu).filter(Menu.id == menu_id).first()
    if not menu:
        raise HTTPException(
            status_code=404,
            detail="菜单不存在",
        )
    return menu


def _validate_menu_update(db: Session, menu_id: int, menu_in: dict, menu: Menu):
    """验证菜单更新数据"""
    _validate_menu_code_unique(db, menu_in, menu)
    _validate_parent_menu(db, menu_id, menu_in)


def _validate_menu_code_unique(db: Session, menu_in: dict, menu: Menu):
    """验证菜单代码唯一性"""
    if "code" in menu_in and menu_in["code"] != menu.code:
        existing_menu = db.query(Menu).filter(Menu.code == menu_in["code"]).first()
        if existing_menu:
            raise HTTPException(
                status_code=400,
                detail="菜单代码已存在",
            )


def _validate_parent_menu(db: Session, menu_id: int, menu_in: dict):
    """验证父菜单设置"""
    if not menu_in.get("parent_id"):
        return

    parent_menu = db.query(Menu).filter(Menu.id == menu_in["parent_id"]).first()
    if not parent_menu:
        raise HTTPException(
            status_code=404,
            detail="父菜单不存在",
        )

    # 检查是否将菜单设置为自己的子菜单
    if menu_in["parent_id"] == menu_id:
        raise HTTPException(
            status_code=400,
            detail="不能将菜单设置为自己的子菜单",
        )

    # 检查是否形成循环依赖
    _check_circular_dependency(db, menu_id, menu_in["parent_id"])


def _check_circular_dependency(db: Session, menu_id: int, parent_id: int):
    """检查循环依赖"""
    current_parent_id = parent_id
    while current_parent_id:
        parent = db.query(Menu).filter(Menu.id == current_parent_id).first()
        if parent.id == menu_id:
            raise HTTPException(
                status_code=400,
                detail="不能形成循环依赖",
            )
        current_parent_id = parent.parent_id


def _update_menu_fields(menu: Menu, menu_in: dict):
    """更新菜单字段"""
    for key, value in menu_in.items():
        setattr(menu, key, value)


@router.delete("/{menu_id}", response_model=dict)
async def delete_menu(
    *,
    db: Session = Depends(deps.get_db),
    menu_id: int = Path(..., gt=0),
    current_user: User = Depends(deps.get_current_active_user),
):
    """
    删除菜单

    权限要求:
    - "menu:delete": 删除菜单
    """
    # 检查权限
    has_permission = permission_service.check_user_permission(
        current_user, "menu:delete", db
    )
    if not has_permission:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="没有删除菜单的权限",
        )

    menu = db.query(Menu).filter(Menu.id == menu_id).first()
    if not menu:
        raise HTTPException(
            status_code=404,
            detail="菜单不存在",
        )

    # 检查是否有子菜单
    if menu.children:
        raise HTTPException(
            status_code=400,
            detail="请先删除子菜单",
        )

    # 删除菜单
    db.delete(menu)
    db.commit()

    return {"id": menu_id, "message": "菜单已删除"}


@router.post("/{menu_id}/permissions", response_model=dict)
async def assign_permissions_to_menu(
    *,
    db: Session = Depends(deps.get_db),
    menu_id: int = Path(..., gt=0),
    permission_ids: List[int],
    current_user: User = Depends(deps.get_current_active_user),
):
    """
    为菜单分配权限

    权限要求:
    - "menu:permission": 管理菜单权限
    """
    # 检查权限
    has_permission = permission_service.check_user_permission(
        current_user, "menu:permission", db
    )
    if not has_permission:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="没有管理菜单权限的权限",
        )

    menu = db.query(Menu).filter(Menu.id == menu_id).first()
    if not menu:
        raise HTTPException(
            status_code=404,
            detail="菜单不存在",
        )

    # 获取权限对象
    from app.models.permission import Permission

    permissions = db.query(Permission).filter(Permission.id.in_(permission_ids)).all()

    # 检查所有权限是否存在
    if len(permissions) != len(permission_ids):
        raise HTTPException(
            status_code=404,
            detail="部分权限不存在",
        )

    # 更新菜单权限
    menu.permissions = permissions
    db.commit()

    return {"id": menu_id, "permissions": [p.code for p in permissions]}


@router.put("/{menu_id}/permissions", response_model=dict)
async def update_menu_permissions(
    *,
    db: Session = Depends(deps.get_db),
    menu_id: int = Path(..., gt=0),
    permission_ids: List[int],
    current_user: User = Depends(deps.get_current_active_user),
):
    """
    更新菜单权限（PUT方式）

    权限要求:
    - "menu:permission": 管理菜单权限
    """
    # 检查权限
    has_permission = permission_service.check_user_permission(
        current_user, "menu:permission", db
    )
    if not has_permission:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="没有管理菜单权限的权限",
        )

    menu = db.query(Menu).filter(Menu.id == menu_id).first()
    if not menu:
        raise HTTPException(
            status_code=404,
            detail="菜单不存在",
        )

    # 获取权限对象
    from app.models.permission import Permission

    permissions = db.query(Permission).filter(Permission.id.in_(permission_ids)).all()

    # 检查所有权限是否存在
    if len(permissions) != len(permission_ids):
        raise HTTPException(
            status_code=404,
            detail="部分权限不存在",
        )

    # 更新菜单权限
    menu.permissions = permissions
    db.commit()

    return {"id": menu_id, "message": "菜单权限更新成功", "permissions": [p.code for p in permissions]}
