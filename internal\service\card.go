package service

import (
	"context"
	"fmt"
	"regexp"
	"strings"

	"gorm.io/gorm"

	"walmart-bind-card-gateway/internal/model"
)

// cardService 卡记录服务实现
type cardService struct {
	db *gorm.DB
}

// NewCardService 创建卡记录服务
func NewCardService(db *gorm.DB) model.CardService {
	return &cardService{
		db: db,
	}
}

// CheckCardExists 检查卡号是否已存在 - 与Python系统完全一致
func (s *cardService) CheckCardExists(ctx context.Context, cardNumber string) (bool, error) {
	var count int64
	
	// 查询卡号是否存在
	err := s.db.WithContext(ctx).
		Model(&model.CardRecord{}).
		Where("card_number = ?", cardNumber).
		Count(&count).Error
	
	if err != nil {
		return false, fmt.Errorf("查询卡号失败: %w", err)
	}
	
	return count > 0, nil
}

// ValidateCardNumber 验证卡号格式 - 严格按照Python系统规则
func (s *cardService) ValidateCardNumber(cardNumber string) error {
	// 1. 检查是否为空
	if cardNumber == "" {
		return fmt.Errorf("卡号不能为空")
	}
	
	// 2. 去除空格
	cardNumber = strings.TrimSpace(cardNumber)
	if cardNumber == "" {
		return fmt.Errorf("卡号不能为空字符串")
	}
	
	// 3. 检查长度 - 与Python系统一致（最少6位）
	if len(cardNumber) < 6 {
		return fmt.Errorf("卡号长度不能少于6位")
	}
	
	// 4. 检查最大长度 - 与Python系统一致（最多50位）
	if len(cardNumber) > 50 {
		return fmt.Errorf("卡号长度不能超过50位")
	}
	
	// 5. 检查2326前缀 - 沃尔玛系统强制要求
	if !strings.HasPrefix(cardNumber, "2326") {
		return fmt.Errorf("暂不支持该类型卡")
	}
	
	// 6. 检查是否全为数字
	if !isNumeric(cardNumber) {
		return fmt.Errorf("卡号必须为数字")
	}
	
	return nil
}

// isNumeric 检查字符串是否全为数字
func isNumeric(s string) bool {
	matched, _ := regexp.MatchString(`^\d+$`, s)
	return matched
}

// ValidateCardPassword 验证卡密码 - 严格按照Python系统规则
func ValidateCardPassword(cardPassword string) error {
	// 1. 检查是否为空
	if cardPassword == "" {
		return fmt.Errorf("卡密码不能为空")
	}
	
	// 2. 去除空格
	cardPassword = strings.TrimSpace(cardPassword)
	if cardPassword == "" {
		return fmt.Errorf("卡密码不能为空字符串")
	}
	
	// 3. 检查长度 - 与Python系统一致
	if len(cardPassword) < 1 || len(cardPassword) > 50 {
		return fmt.Errorf("卡密码长度必须在1-50位之间")
	}
	
	return nil
}

// ValidateMerchantOrderID 验证商户订单号 - 严格按照Python系统规则
func ValidateMerchantOrderID(merchantOrderID string) error {
	// 1. 检查是否为空
	if merchantOrderID == "" {
		return fmt.Errorf("商户订单号不能为空")
	}
	
	// 2. 去除空格
	merchantOrderID = strings.TrimSpace(merchantOrderID)
	if merchantOrderID == "" {
		return fmt.Errorf("商户订单号不能为空字符串")
	}
	
	// 3. 检查长度 - 与Python系统一致
	if len(merchantOrderID) < 1 || len(merchantOrderID) > 255 {
		return fmt.Errorf("商户订单号长度必须在1-255位之间")
	}
	
	return nil
}

// ValidateAmount 验证金额 - 严格按照Python系统规则
func ValidateAmount(amount int) error {
	// 金额验证（单位：分，最小100分即1元）- 与Python系统一致
	if amount < 100 {
		return fmt.Errorf("订单金额必须大于等于100分（1元）")
	}
	
	return nil
}

// ValidateMerchantCode 验证商户编码 - 严格按照Python系统规则
func ValidateMerchantCode(merchantCode string) error {
	// 1. 检查是否为空
	if merchantCode == "" {
		return fmt.Errorf("商户编码不能为空")
	}
	
	// 2. 去除空格
	merchantCode = strings.TrimSpace(merchantCode)
	if merchantCode == "" {
		return fmt.Errorf("商户编码不能为空字符串")
	}
	
	// 3. 检查长度 - 与Python系统一致
	if len(merchantCode) < 1 || len(merchantCode) > 50 {
		return fmt.Errorf("商户代码长度必须在1-50位之间")
	}
	
	return nil
}
