from typing import Any, Dict
from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session

from app.api import deps
from app.services.recovery_service import create_recovery_service
from app.core.logging import get_logger
from app.core.permission_checker import require_permission

router = APIRouter()
logger = get_logger("recovery_api")

@router.post("/stuck-requests", response_model=Dict[str, Any])
@require_permission("system:recovery")
async def reprocess_stuck_requests(
    *,
    db: Session = Depends(deps.get_db),
    hours: int = Query(24, description="查找多少小时内的请求"),
    batch_size: int = Query(50, description="每批处理的请求数量"),
    current_user: Any = Depends(deps.get_current_active_user),
) -> Any:
    """
    重新处理卡在pending状态的绑卡请求

    - 需要管理员权限
    - 可以指定查找时间范围和批处理大小
    """
    try:
        # 创建恢复服务实例
        recovery_service = create_recovery_service(db)

        # 记录操作日志
        logger.info(f"管理员 {current_user.username} 触发了卡住请求恢复处理")

        # 执行恢复处理
        results = await recovery_service.batch_reprocess_stuck_requests(
            current_user=current_user,
            hours_threshold=hours,
            batch_size=batch_size
        )

        # 通知商家
        if results["total"] > 0:
            await recovery_service.notify_merchants(current_user, results)

        # 直接返回数据，让中间件处理统一格式
        return results
    except Exception as e:
        logger.error(f"恢复处理失败: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"恢复处理失败: {str(e)}"
        )


@router.get("/statistics", response_model=Dict[str, Any])
@require_permission("system:recovery")
async def get_recovery_statistics(
    *,
    db: Session = Depends(deps.get_db),
    hours: int = Query(24, description="查找多少小时内的请求"),
    current_user: Any = Depends(deps.get_current_active_user),
) -> Any:
    """
    获取恢复统计信息

    - 需要管理员权限
    - 可以指定查找时间范围
    """
    try:
        # 创建恢复服务实例
        recovery_service = create_recovery_service(db)

        # 获取统计信息
        statistics = await recovery_service.get_recovery_statistics(
            current_user=current_user,
            hours_threshold=hours
        )

        return statistics
    except Exception as e:
        logger.error(f"获取恢复统计信息失败: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取恢复统计信息失败: {str(e)}"
        )


@router.put("/configuration", response_model=Dict[str, Any])
@require_permission("system:recovery")
async def update_recovery_configuration(
    *,
    db: Session = Depends(deps.get_db),
    pending_threshold_minutes: int = Query(None, description="pending状态阈值（分钟）"),
    max_retry_count: int = Query(None, description="最大重试次数"),
    current_user: Any = Depends(deps.get_current_active_user),
) -> Any:
    """
    更新恢复服务配置

    - 需要管理员权限
    - 可以更新pending阈值和最大重试次数
    """
    try:
        # 创建恢复服务实例
        recovery_service = create_recovery_service(db)

        # 更新配置
        result = recovery_service.update_configuration(
            pending_threshold_minutes=pending_threshold_minutes,
            max_retry_count=max_retry_count
        )

        # 记录配置变更日志
        logger.info(f"管理员 {current_user.username} 更新了恢复服务配置: {result}")

        return result
    except Exception as e:
        logger.error(f"更新恢复服务配置失败: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"更新恢复服务配置失败: {str(e)}"
        )
