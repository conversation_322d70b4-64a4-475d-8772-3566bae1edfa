"""
双因子认证前端功能测试
"""

import asyncio
import pytest
from playwright.async_api import async_playwright, <PERSON>, Browser, BrowserContext


class TestTOTPFrontend:
    """TOTP前端功能测试类"""

    def __init__(self):
        self.base_url = "http://localhost:2000"
        self.browser = None
        self.context = None
        self.page = None

    async def setup_method(self):
        """测试前准备"""
        playwright = await async_playwright().start()
        self.browser = await playwright.chromium.launch(headless=False)
        self.context = await self.browser.new_context()
        self.page = await self.context.new_page()

    async def teardown_method(self):
        """测试后清理"""
        if self.page:
            await self.page.close()
        if self.context:
            await self.context.close()
        if self.browser:
            await self.browser.close()

    async def login_as_admin(self):
        """以超级管理员身份登录"""
        await self.page.goto(f"{self.base_url}/#/login")
        await self.page.wait_for_load_state('networkidle')
        
        # 填写登录信息
        await self.page.fill('input[placeholder="请输入用户名"]', 'admin')
        await self.page.fill('input[placeholder="请输入密码"]', '7c222fb2927d828af22f592134e8932480637c0d')
        
        # 点击登录
        await self.page.click('button:has-text("登录")')
        await self.page.wait_for_load_state('networkidle')
        
        # 验证登录成功
        await self.page.wait_for_selector('.el-menu', timeout=10000)

    async def test_security_page_access(self):
        """测试安全设置页面访问"""
        try:
            await self.login_as_admin()
            
            # 导航到安全设置页面
            await self.page.goto(f"{self.base_url}/#/security")
            await self.page.wait_for_load_state('networkidle')
            
            # 验证页面标题
            title = await self.page.text_content('h2')
            assert "安全设置" in title
            
            # 验证双因子认证卡片存在
            totp_card = await self.page.locator('.security-card:has-text("双因子认证")').count()
            assert totp_card > 0
            
            print("✅ 安全设置页面访问测试通过")
            return True
            
        except Exception as e:
            print(f"❌ 安全设置页面访问测试失败: {e}")
            return False

    async def test_totp_setup_flow(self):
        """测试TOTP设置流程"""
        try:
            await self.login_as_admin()
            
            # 导航到安全设置页面
            await self.page.goto(f"{self.base_url}/#/security")
            await self.page.wait_for_load_state('networkidle')
            
            # 检查TOTP状态
            totp_status = await self.page.locator('.el-tag:has-text("未启用")').count()
            if totp_status == 0:
                print("⚠️ TOTP已启用，跳过设置测试")
                return True
            
            # 点击启用双因子认证按钮
            await self.page.click('button:has-text("启用双因子认证")')
            await self.page.wait_for_load_state('networkidle')
            
            # 验证进入设置页面
            setup_title = await self.page.text_content('h3')
            assert "设置双因子认证" in setup_title
            
            # 验证步骤指示器
            steps = await self.page.locator('.el-step').count()
            assert steps == 3
            
            # 点击生成密钥
            await self.page.click('button:has-text("生成密钥")')
            await self.page.wait_for_timeout(2000)
            
            # 验证二维码显示
            qr_code = await self.page.locator('.qr-code').count()
            assert qr_code > 0
            
            # 验证备用码显示
            backup_codes = await self.page.locator('.backup-code').count()
            assert backup_codes == 10
            
            print("✅ TOTP设置流程测试通过")
            return True
            
        except Exception as e:
            print(f"❌ TOTP设置流程测试失败: {e}")
            return False

    async def test_totp_status_display(self):
        """测试TOTP状态显示"""
        try:
            await self.login_as_admin()
            
            # 导航到安全设置页面
            await self.page.goto(f"{self.base_url}/#/security")
            await self.page.wait_for_load_state('networkidle')
            
            # 检查状态标签
            status_tags = await self.page.locator('.el-tag').count()
            assert status_tags > 0
            
            # 验证状态文本
            status_text = await self.page.locator('.el-tag').first.text_content()
            assert status_text in ["已启用", "未启用", "必须启用"]
            
            print("✅ TOTP状态显示测试通过")
            return True
            
        except Exception as e:
            print(f"❌ TOTP状态显示测试失败: {e}")
            return False

    async def test_password_change_dialog(self):
        """测试修改密码对话框"""
        try:
            await self.login_as_admin()
            
            # 导航到安全设置页面
            await self.page.goto(f"{self.base_url}/#/security")
            await self.page.wait_for_load_state('networkidle')
            
            # 点击修改密码按钮
            await self.page.click('button:has-text("修改密码")')
            await self.page.wait_for_timeout(1000)
            
            # 验证对话框显示
            dialog = await self.page.locator('.el-dialog:has-text("修改密码")').count()
            assert dialog > 0
            
            # 验证表单字段
            current_password = await self.page.locator('input[type="password"]').first.count()
            assert current_password > 0
            
            # 关闭对话框
            await self.page.click('button:has-text("取消")')
            await self.page.wait_for_timeout(500)
            
            print("✅ 修改密码对话框测试通过")
            return True
            
        except Exception as e:
            print(f"❌ 修改密码对话框测试失败: {e}")
            return False

    async def test_totp_login_interface(self):
        """测试TOTP登录界面"""
        try:
            # 导航到登录页面
            await self.page.goto(f"{self.base_url}/#/login")
            await self.page.wait_for_load_state('networkidle')
            
            # 验证登录表单
            username_input = await self.page.locator('input[placeholder="请输入用户名"]').count()
            password_input = await self.page.locator('input[placeholder="请输入密码"]').count()
            
            assert username_input > 0
            assert password_input > 0
            
            # 填写用户名和密码
            await self.page.fill('input[placeholder="请输入用户名"]', 'test_user')
            await self.page.fill('input[placeholder="请输入密码"]', 'test_password')
            
            # 检查是否有TOTP输入框（如果用户启用了TOTP）
            totp_input = await self.page.locator('input[placeholder*="验证码"]').count()
            
            print(f"✅ TOTP登录界面测试通过 (TOTP输入框: {totp_input})")
            return True
            
        except Exception as e:
            print(f"❌ TOTP登录界面测试失败: {e}")
            return False

    async def test_security_navigation(self):
        """测试安全设置导航"""
        try:
            await self.login_as_admin()
            
            # 测试直接URL访问
            await self.page.goto(f"{self.base_url}/#/security/index")
            await self.page.wait_for_load_state('networkidle')
            
            # 验证页面加载
            page_title = await self.page.text_content('h2')
            assert "安全设置" in page_title
            
            # 测试TOTP设置页面URL
            await self.page.goto(f"{self.base_url}/#/security/totp-setup")
            await self.page.wait_for_load_state('networkidle')
            
            # 验证设置页面
            setup_content = await self.page.locator('.totp-setup-container').count()
            assert setup_content > 0
            
            print("✅ 安全设置导航测试通过")
            return True
            
        except Exception as e:
            print(f"❌ 安全设置导航测试失败: {e}")
            return False

    async def run_all_tests(self):
        """运行所有测试"""
        tests = [
            ("安全设置页面访问", self.test_security_page_access),
            ("TOTP状态显示", self.test_totp_status_display),
            ("修改密码对话框", self.test_password_change_dialog),
            ("TOTP登录界面", self.test_totp_login_interface),
            ("安全设置导航", self.test_security_navigation),
            ("TOTP设置流程", self.test_totp_setup_flow),
        ]
        
        results = []
        for test_name, test_func in tests:
            print(f"\n🧪 执行测试: {test_name}")
            try:
                await self.setup_method()
                result = await test_func()
                results.append((test_name, result))
            except Exception as e:
                print(f"❌ 测试执行异常: {e}")
                results.append((test_name, False))
            finally:
                await self.teardown_method()
        
        # 输出测试结果
        print("\n" + "="*50)
        print("📊 TOTP前端功能测试结果")
        print("="*50)
        
        passed = 0
        total = len(results)
        
        for test_name, result in results:
            status = "✅ 通过" if result else "❌ 失败"
            print(f"{test_name}: {status}")
            if result:
                passed += 1
        
        print(f"\n总计: {passed}/{total} 个测试通过")
        print(f"成功率: {(passed/total)*100:.1f}%")
        
        return passed == total


async def main():
    """主函数"""
    tester = TestTOTPFrontend()
    success = await tester.run_all_tests()
    return success


if __name__ == "__main__":
    result = asyncio.run(main())
    exit(0 if result else 1)
