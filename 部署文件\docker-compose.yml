services:
  app:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: walmart-bind-card-server
    restart: always
    ports:
      - "20000:20000"
    # depends_on 已移除，因为使用外部独立部署的服务
    volumes:
      - ./logs:/app/logs
      - ./config.yaml:/app/config.yaml
    environment:
      # 时区配置
      - TZ=Asia/Shanghai
      # 数据库配置 - 服务器环境使用实际IP地址
      - DB_TYPE=mysql
      - DB_HOST=${DB_HOST:-**************} # 修改为实际IP地址
      - DB_PORT=3306
      - DB_USER=root
      - DB_PASSWORD=7c222fb2927d828af22f592134e8932480637c0d
      - DB_NAME=walmart_card_db
      # Redis配置 - 服务器环境使用实际IP地址
      - REDIS_HOST=${REDIS_HOST:-**************} # 修改为实际IP地址
      - REDIS_PORT=6379
      - REDIS_PASSWORD=7c222fb2927d828af22f592134e8932480637c0d
      - REDIS_DB=3
      # RabbitMQ配置 - 服务器环境使用实际IP地址
      - RABBITMQ_HOST=${RABBITMQ_HOST:-**************} # 修改为实际IP地址
      - RABBITMQ_PORT=5672
      - RABBITMQ_USER=walmart_card
      - RABBITMQ_PASSWORD=7c222fb2927d828af22f592134e8932480637c0d
      - RABBITMQ_VHOST=/walmart_card
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:20000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
# 以下服务已移除，现在使用外部独立部署的服务：
# - MySQL数据库服务器 (默认: *************:3306)
# - Redis缓存服务器 (默认: *************:6379)
# - RabbitMQ消息队列服务器 (默认: *************:5672)
#
# 如需修改服务器地址，请在启动时设置环境变量：
# DB_HOST=your_db_server_ip
# REDIS_HOST=your_redis_server_ip
# RABBITMQ_HOST=your_rabbitmq_server_ip
