<template>
  <el-breadcrumb class="breadcrumb-navigation" separator="/" v-if="breadcrumbItems.length > 0">
    <!-- 对账台首页 -->
    <el-breadcrumb-item>
      <el-button text @click="navigateToReconciliation">
        <el-icon>
          <ArrowLeft />
        </el-icon>
        对账台
      </el-button>
    </el-breadcrumb-item>

    <!-- 动态显示层级路径 -->
    <el-breadcrumb-item v-for="(item, index) in breadcrumbItems" :key="index">
      <el-button v-if="item.clickable" text @click="item.onClick">
        {{ item.name }}
      </el-button>
      <span v-else class="current-page">{{ item.name }}</span>
    </el-breadcrumb-item>
  </el-breadcrumb>
</template>

<script setup>
import { computed } from 'vue'
import { ArrowLeft } from '@element-plus/icons-vue'

// Props
const props = defineProps({
  // 部门路径数据（JSON字符串或数组）
  departmentPath: {
    type: [String, Array],
    default: () => []
  },
  // 当前页面名称
  currentPage: {
    type: String,
    default: ''
  },
  // 筛选条件
  filters: {
    type: Object,
    default: () => ({})
  },
  // 额外的面包屑项（用于CK明细、绑卡记录等）
  extraItems: {
    type: Array,
    default: () => []
  },
  // 是否显示当前页面
  showCurrentPage: {
    type: Boolean,
    default: true
  }
})

// Emits
const emit = defineEmits(['navigate'])

// 解析部门路径
const parsedDepartmentPath = computed(() => {
  if (!props.departmentPath) return []

  if (typeof props.departmentPath === 'string') {
    try {
      return JSON.parse(props.departmentPath)
    } catch (e) {
      // 如果解析失败，尝试简单的字符串分割
      return props.departmentPath.split('/').filter(name => name.trim()).map((name, index) => ({
        id: `dept_${index}`,
        name: name.trim(),
        level: index + 1
      }))
    }
  }

  return Array.isArray(props.departmentPath) ? props.departmentPath : []
})

// 构建面包屑项
const breadcrumbItems = computed(() => {
  const items = []

  // 添加部门层级路径
  parsedDepartmentPath.value.forEach((dept, index) => {
    items.push({
      name: dept.name,
      clickable: true,
      onClick: () => navigateToDepartment(dept, index)
    })
  })

  // 添加额外的面包屑项（如CK明细）
  props.extraItems.forEach((item, index) => {
    items.push({
      name: item.name,
      clickable: item.clickable !== false,
      onClick: item.clickable !== false ? item.onClick : undefined
    })
  })

  // 添加当前页面（不可点击）
  if (props.showCurrentPage && props.currentPage) {
    items.push({
      name: props.currentPage,
      clickable: false
    })
  }

  return items
})

// 导航到对账台首页
const navigateToReconciliation = () => {
  emit('navigate', {
    name: 'Reconciliation',
    query: {
      timeRange: props.filters.timeRange,
      startDate: props.filters.startDate,
      endDate: props.filters.endDate,
      merchantId: props.filters.merchantId,
      viewLevel: props.filters.viewLevel
    }
  })
}

// 导航到指定部门层级
const navigateToDepartment = (department, index) => {
  console.log('🔍 面包屑导航调试信息:', {
    clickedDepartment: department,
    clickedIndex: index,
    fullDepartmentPath: parsedDepartmentPath.value,
    departmentPathDetails: parsedDepartmentPath.value.map((dept, i) => ({
      index: i,
      id: dept.id,
      name: dept.name,
      level: dept.level
    }))
  })

  // 构建到指定层级的部门路径 - 只包含到被点击部门的父级路径
  const targetPath = parsedDepartmentPath.value.slice(0, index)

  // 计算正确的层级和父部门信息
  let parentDepartmentId = null
  let parentDepartmentName = ''
  let targetLevel = department.level || (index + 1)

  // 如果点击的不是第一个部门，需要设置父部门信息
  if (index > 0) {
    const parentDept = parsedDepartmentPath.value[index - 1]
    parentDepartmentId = parentDept.id
    parentDepartmentName = parentDept.name
  }

  console.log('🎯 导航参数:', {
    parentDepartmentId,
    parentDepartmentName,
    targetLevel,
    targetPath,
    targetPathString: targetPath.length > 0 ? JSON.stringify(targetPath) : undefined
  })

  emit('navigate', {
    name: 'Reconciliation',
    query: {
      parentDepartmentId: parentDepartmentId,
      parentDepartmentName: parentDepartmentName,
      level: targetLevel,
      departmentPath: targetPath.length > 0 ? JSON.stringify(targetPath) : undefined,
      timeRange: props.filters.timeRange,
      startDate: props.filters.startDate,
      endDate: props.filters.endDate,
      merchantId: props.filters.merchantId,
      viewLevel: props.filters.viewLevel
    }
  })
}
</script>

<style scoped>
/* 面包屑导航样式 */
.breadcrumb-navigation {
  margin-bottom: 16px;
}

.breadcrumb-navigation :deep(.el-breadcrumb) {
  display: flex !important;
  align-items: center !important;
  line-height: 1 !important;
  height: 24px;
}

.breadcrumb-navigation :deep(.el-breadcrumb-item) {
  display: flex !important;
  align-items: center !important;
  line-height: 1 !important;
  height: 24px;
  margin: 0 !important;
}

.breadcrumb-navigation :deep(.el-breadcrumb-item__inner) {
  display: flex !important;
  align-items: center !important;
  line-height: 1 !important;
  font-size: 14px !important;
  height: 24px;
  margin: 0 !important;
  padding: 0 !important;
}

.breadcrumb-navigation :deep(.el-breadcrumb-item__separator) {
  display: flex !important;
  align-items: center !important;
  line-height: 1 !important;
  margin: 0 8px !important;
  height: 24px;
  font-size: 14px !important;
}

.breadcrumb-navigation .el-button {
  padding: 0 !important;
  margin: 0 !important;
  font-size: 14px !important;
  color: #409eff !important;
  display: flex !important;
  align-items: center !important;
  gap: 4px;
  line-height: 1 !important;
  border: none !important;
  background: none !important;
  height: 24px;
  min-height: 24px !important;
}

.breadcrumb-navigation .el-button:hover {
  color: #66b1ff !important;
}

.breadcrumb-navigation .el-button .el-icon {
  font-size: 14px !important;
  margin: 0 !important;
}

.current-page {
  font-size: 14px;
  color: #606266;
  line-height: 1;
  height: 24px;
  display: flex;
  align-items: center;
}
</style>
