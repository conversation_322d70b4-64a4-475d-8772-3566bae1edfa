<template>
  <div class="role-management">
    <div class="search-section">
      <div class="search-form">
        <div class="form-item">
          <label>角色名称</label>
          <el-input v-model="searchForm.name" placeholder="请输入角色名称" clearable style="width: 200px" />
        </div>
        <div class="form-item">
          <label>状态</label>
          <el-select v-model="searchForm.status" placeholder="状态" clearable style="width: 120px">
            <el-option label="全部" value="" />
            <el-option label="启用" value="1" />
            <el-option label="禁用" value="0" />
          </el-select>
        </div>
        <div class="actions">
          <el-button type="primary" @click="handleSearch">
            <el-icon>
              <Search />
            </el-icon>
            搜索
          </el-button>
          <el-button @click="handleReset">重置</el-button>
        </div>
        <div class="toolbar">
          <el-button type="primary" @click="handleAdd">
            <el-icon>
              <Plus />
            </el-icon>
            新增角色
          </el-button>
        </div>
      </div>
    </div>

    <el-divider />

    <div class="table-section">
      <el-table :data="tableData" style="width: 100%" v-loading="loading">
        <el-table-column prop="name" label="角色名称" />
        <el-table-column prop="code" label="角色编码" />
        <el-table-column prop="description" label="描述" />
        <el-table-column prop="status" label="状态">
          <template #default="{ row }">
            <el-tag :type="row.status === 1 ? 'success' : 'danger'">
              {{ row.status === 1 ? '启用' : '禁用' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="created_at" label="创建时间" />
        <el-table-column label="操作" width="360" fixed="right">
          <template #default="{ row }">
            <div class="action-buttons">
              <el-button type="primary" size="small" @click="handleEdit(row)">
                编辑
              </el-button>
              <el-button type="warning" size="small" @click="handlePermission(row)">
                权限
              </el-button>
              <el-button type="info" size="small" @click="handleUsers(row)">
                用户
              </el-button>
              <el-button :type="row.status === 1 ? 'danger' : 'success'" size="small" @click="handleToggleStatus(row)">
                {{ row.status === 1 ? '禁用' : '启用' }}
              </el-button>
              <el-button type="danger" size="small" @click="handleDelete(row)" :disabled="row.code === 'super_admin'">
                删除
              </el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>

      <div class="pagination">
        <div class="pagination-info">
          <span>共 {{ total }} 条</span>
          <el-select v-model="pageSize" @change="handlePageSizeChange" style="width: 120px; margin-left: 10px;">
            <el-option label="10条/页" :value="10" />
            <el-option label="20条/页" :value="20" />
            <el-option label="50条/页" :value="50" />
          </el-select>
        </div>
        <el-pagination v-model:current-page="currentPage" v-model:page-size="pageSize" :total="total"
          layout="prev, pager, next, jumper" @current-change="handlePageChange" @size-change="handlePageSizeChange" />
      </div>
    </div>

    <!-- 角色表单对话框 -->
    <el-dialog v-model="dialogVisible" :title="dialogTitle" width="600px">
      <el-form ref="formRef" :model="form" :rules="rules" label-width="100px">
        <el-form-item label="角色名称" prop="name">
          <el-input v-model="form.name" placeholder="请输入角色名称" />
        </el-form-item>
        <el-form-item label="角色编码" prop="code">
          <el-input v-model="form.code" placeholder="请输入角色编码" />
        </el-form-item>
        <el-form-item label="描述" prop="description">
          <el-input v-model="form.description" type="textarea" :rows="3" placeholder="请输入角色描述" />
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="form.status">
            <el-radio :value="1">启用</el-radio>
            <el-radio :value="0">禁用</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleSubmit">确定</el-button>
      </template>
    </el-dialog>


  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Search, Plus } from '@element-plus/icons-vue'
import { roleApi } from '@/api/modules/role'

const router = useRouter()

// 响应式数据
const loading = ref(false)
const tableData = ref([])
const total = ref(0)
const currentPage = ref(1)
const pageSize = ref(10)
const dialogVisible = ref(false)
const dialogTitle = ref('')

const formRef = ref()

// 搜索表单
const searchForm = reactive({
  name: '',
  status: ''
})

// 角色表单
const form = reactive({
  id: null,
  name: '',
  code: '',
  description: '',
  status: 1
})

// 表单验证规则
const rules = {
  name: [
    { required: true, message: '请输入角色名称', trigger: 'blur' }
  ],
  code: [
    { required: true, message: '请输入角色编码', trigger: 'blur' }
  ]
}



// 方法
const fetchData = async () => {
  loading.value = true
  try {
    const params = {
      skip: (currentPage.value - 1) * pageSize.value,
      limit: pageSize.value
    }

    // 添加搜索条件
    if (searchForm.name) {
      params.name = searchForm.name
    }
    if (searchForm.status !== '') {
      params.is_enabled = searchForm.status === '1'
    }

    const response = await roleApi.getList(params)

    if (Array.isArray(response)) {
      // 如果返回的是数组，说明没有分页信息
      tableData.value = response.map(item => ({
        id: item.id,
        name: item.name,
        code: item.code,
        description: item.description,
        status: item.is_enabled ? 1 : 0,
        created_at: item.created_at
      }))
      total.value = response.length
    } else if (response && response.items) {
      // 如果有分页信息
      tableData.value = response.items.map(item => ({
        id: item.id,
        name: item.name,
        code: item.code,
        description: item.description,
        status: item.is_enabled ? 1 : 0,
        created_at: item.created_at
      }))
      total.value = response.total || response.items.length
    } else {
      tableData.value = []
      total.value = 0
    }
  } catch (error) {
    console.error('获取角色列表失败:', error)
    ElMessage.error('获取角色列表失败')
    tableData.value = []
    total.value = 0
  } finally {
    loading.value = false
  }
}

const handleSearch = () => {
  currentPage.value = 1
  fetchData()
}

const handleReset = () => {
  Object.assign(searchForm, {
    name: '',
    status: ''
  })
  handleSearch()
}

const handleAdd = () => {
  dialogTitle.value = '新增角色'
  Object.assign(form, {
    id: null,
    name: '',
    code: '',
    description: '',
    status: 1
  })
  dialogVisible.value = true
}

const handleEdit = (row) => {
  dialogTitle.value = '编辑角色'
  Object.assign(form, { ...row })
  dialogVisible.value = true
}

const handlePermission = (row) => {
  // 跳转到权限配置页面
  router.push(`/system/role/${row.id}/permission`)
}

const handleUsers = (row) => {
  // 跳转到角色用户管理页面
  router.push(`/system/role/${row.id}/users`)
}

const handleToggleStatus = async (row) => {
  const action = row.status === 1 ? '禁用' : '启用'
  try {
    await ElMessageBox.confirm(`确定要${action}该角色吗？`, '提示', {
      type: 'warning'
    })

    // 调用API切换状态
    await roleApi.update(row.id, {
      ...row,
      is_enabled: row.status === 1 ? false : true
    })

    row.status = row.status === 1 ? 0 : 1
    ElMessage.success(`${action}成功`)
  } catch (error) {
    if (error !== 'cancel') {
      console.error(`${action}失败:`, error)
      ElMessage.error(`${action}失败`)
    }
  }
}

const handleDelete = async (row) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除角色 "${row.name}" 吗？此操作不可恢复！`,
      '警告',
      {
        confirmButtonText: '确定删除',
        cancelButtonText: '取消',
        type: 'warning',
        dangerouslyUseHTMLString: true
      }
    )

    await roleApi.delete(row.id)
    ElMessage.success('删除成功')
    fetchData()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除失败:', error)
      // 处理特定的错误信息
      if (error.response && error.response.data && error.response.data.detail) {
        ElMessage.error(error.response.data.detail)
      } else {
        ElMessage.error('删除失败')
      }
    }
  }
}

const handleSubmit = async () => {
  try {
    await formRef.value.validate()

    const submitData = {
      name: form.name,
      code: form.code,
      description: form.description,
      is_enabled: form.status === 1
    }

    // 调用API保存角色
    if (form.id) {
      await roleApi.update(form.id, submitData)
    } else {
      await roleApi.create(submitData)
    }

    ElMessage.success('保存成功')
    dialogVisible.value = false
    fetchData()
  } catch (error) {
    if (error !== false) {
      console.error('保存失败:', error)
      ElMessage.error('保存失败')
    }
  }
}

const handlePageChange = (page) => {
  currentPage.value = page
  fetchData()
}

const handlePageSizeChange = () => {
  currentPage.value = 1
  fetchData()
}











// 生命周期
onMounted(() => {
  fetchData()
})
</script>

<style scoped>
.role-management {
  padding: 20px;
}

.search-section {
  background: #fff;
  padding: 20px;
  border-radius: 8px;
  margin-bottom: 20px;
}

.search-form {
  display: flex;
  align-items: center;
  gap: 20px;
  flex-wrap: wrap;
}

.form-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.form-item label {
  white-space: nowrap;
  font-weight: 500;
}

.actions {
  display: flex;
  gap: 10px;
}

.toolbar {
  margin-left: auto;
}

.table-section {
  background: #fff;
  padding: 20px;
  border-radius: 8px;
}

.action-buttons {
  display: flex;
  gap: 8px;
}

.pagination {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 20px;
}

.pagination-info {
  display: flex;
  align-items: center;
}


</style>
