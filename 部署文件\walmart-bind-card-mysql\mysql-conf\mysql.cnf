# MySQL 8.0 配置文件
# 适用于高并发绑卡场景

[mysqld]
# 基础配置
user = mysql
port = 3306
bind-address = 0.0.0.0

# 字符集配置
character-set-server = utf8mb4
collation-server = utf8mb4_unicode_ci
default-time-zone = '+08:00'
init-connect = 'SET NAMES utf8mb4 COLLATE utf8mb4_unicode_ci'

# 连接配置
max_connections = 1000
max_connect_errors = 100000
wait_timeout = 28800
interactive_timeout = 28800

# InnoDB配置
innodb_buffer_pool_size = 2G
innodb_redo_log_capacity = 536870912
innodb_flush_log_at_trx_commit = 2
innodb_flush_method = O_DIRECT
innodb_io_capacity = 2000
innodb_io_capacity_max = 4000

# 二进制日志配置
sync_binlog = 0
binlog_format = ROW
expire_logs_days = 7

# 慢查询日志
slow_query_log = 1
slow_query_log_file = /var/log/mysql/slow.log
long_query_time = 2

# 错误日志
log_error = /var/log/mysql/error.log

# 通用查询日志（生产环境建议关闭）
# general_log = 1
# general_log_file = /var/log/mysql/general.log

# 性能优化
table_open_cache = 4000
table_definition_cache = 2000
thread_cache_size = 100
tmp_table_size = 256M
max_heap_table_size = 256M

# 安全配置
local_infile = 0
skip_name_resolve = 1

[mysql]
default-character-set = utf8mb4

[client]
default-character-set = utf8mb4
