package model

// ErrorCode 错误码定义 - 与Python系统完全兼容
type ErrorCode struct {
	Code    int
	Message string
}

// 通用错误码 (1-999)
var (
	ErrSystemError       = ErrorCode{1, "系统错误"}
	ErrParamsError       = ErrorCode{2, "参数错误"}
	ErrUnauthorized      = ErrorCode{3, "未授权"}
	ErrForbidden         = ErrorCode{4, "禁止访问"}
	ErrNotFound          = ErrorCode{5, "资源不存在"}
	ErrMethodNotAllowed  = ErrorCode{6, "方法不允许"}
	ErrTimeout           = ErrorCode{7, "请求超时"}
	ErrTooManyRequests   = ErrorCode{8, "请求过于频繁"}
	ErrBusinessError     = ErrorCode{9, "业务错误"}
)

// 认证相关错误 (1000-1999)
var (
	ErrInvalidToken      = ErrorCode{1000, "无效的令牌"}
	ErrTokenExpired      = ErrorCode{1001, "令牌已过期"}
	ErrInvalidCredentials = ErrorCode{1002, "无效的凭据"}
	ErrUserNotFound      = ErrorCode{1003, "用户不存在"}
	ErrUserInactive      = ErrorCode{1004, "用户未激活"}
	ErrPasswordExpired   = ErrorCode{1005, "密码已过期"}
)

// 商户相关错误 (2000-2999)
var (
	ErrMerchantNotFound      = ErrorCode{2000, "商户不存在"}
	ErrMerchantInactive      = ErrorCode{2001, "商户未激活"}
	ErrMerchantAPIKeyInvalid = ErrorCode{2002, "商户API密钥无效"}
	ErrMerchantAPIRateLimit  = ErrorCode{2003, "商户API请求频率限制"}
	ErrMerchantIPRestricted  = ErrorCode{2004, "商户IP受限"}
	ErrMerchantCodeExists    = ErrorCode{2005, "商户代码已存在"}
	ErrMerchantCodeMismatch  = ErrorCode{2006, "商户编码与API密钥不匹配"}
)

// 绑卡相关错误 (3000-3999)
var (
	ErrCardNotFound      = ErrorCode{3000, "卡不存在"}
	ErrCardExpired       = ErrorCode{3001, "卡已过期"}
	ErrCardInvalid       = ErrorCode{3002, "无效的卡"}
	ErrCardBindLimit     = ErrorCode{3003, "绑卡次数限制"}
	ErrCardAlreadyBound  = ErrorCode{3004, "卡号已存在, 请勿重复请求"}
	ErrCardUnsupported   = ErrorCode{3005, "暂不支持该类型卡"}
)

// 签名相关错误 (4000-4999)
var (
	ErrInvalidSignature  = ErrorCode{4000, "无效的签名"}
	ErrSignatureExpired  = ErrorCode{4001, "签名已过期"}
	ErrInvalidTimestamp  = ErrorCode{4002, "无效的时间戳"}
	ErrInvalidNonce      = ErrorCode{4003, "无效的随机数"}
	ErrNonceReused       = ErrorCode{4004, "随机数重复使用"}
)

// API相关错误 (5000-5999)
var (
	ErrAPIKeyMissing     = ErrorCode{5000, "API密钥缺失"}
	ErrAPIKeyInvalid     = ErrorCode{5001, "API密钥无效"}
	ErrAPIRateLimit      = ErrorCode{5002, "API请求频率限制"}
	ErrAPIQuotaExceeded  = ErrorCode{5003, "API配额超限"}
	ErrAPIVersionInvalid = ErrorCode{5004, "API版本无效"}
)

// 业务异常结构
type BusinessException struct {
	ErrorCode ErrorCode
	Details   string
}

func (e *BusinessException) Error() string {
	if e.Details != "" {
		return e.ErrorCode.Message + ": " + e.Details
	}
	return e.ErrorCode.Message
}

// NewBusinessException 创建业务异常
func NewBusinessException(errorCode ErrorCode, details ...string) *BusinessException {
	var detail string
	if len(details) > 0 {
		detail = details[0]
	}
	return &BusinessException{
		ErrorCode: errorCode,
		Details:   detail,
	}
}

// 常用错误响应创建函数
func ErrorResponseWithCode(errorCode ErrorCode) *APIResponse {
	return &APIResponse{
		Code:    errorCode.Code,
		Message: errorCode.Message,
	}
}

func ErrorResponseWithDetails(errorCode ErrorCode, details string) *APIResponse {
	return &APIResponse{
		Code:    errorCode.Code,
		Message: errorCode.Message + ": " + details,
	}
}

// HTTP状态码映射
func GetHTTPStatusCode(errorCode ErrorCode) int {
	switch {
	case errorCode.Code >= 1000 && errorCode.Code < 2000:
		return 401 // 认证错误
	case errorCode.Code >= 2000 && errorCode.Code < 3000:
		return 403 // 商户权限错误
	case errorCode.Code >= 3000 && errorCode.Code < 4000:
		return 400 // 绑卡业务错误
	case errorCode.Code >= 4000 && errorCode.Code < 5000:
		return 400 // 签名错误
	case errorCode.Code >= 5000 && errorCode.Code < 6000:
		return 400 // API错误
	case errorCode.Code == 5:
		return 404 // 资源不存在
	case errorCode.Code == 8:
		return 429 // 请求过于频繁
	default:
		return 400 // 默认客户端错误
	}
}
