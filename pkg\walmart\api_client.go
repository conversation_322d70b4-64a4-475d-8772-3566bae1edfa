package walmart

import (
	"bytes"
	"compress/gzip"
	"context"
	"crypto/hmac"
	"crypto/sha256"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"io"
	"math/rand"
	"net/http"
	"net/http/cookiejar" // 🔧 新增：Cookie管理

	// 🔧 新增：URL解析
	"sort"
	"strconv"
	"strings"
	"time"

	"github.com/sirupsen/logrus"
	"go.uber.org/zap"
)

// APIClient 沃尔玛API客户端
type APIClient struct {
	encryptionKey string
	version       string
	sign          string
	baseURL       string
	httpClient    *http.Client
	logger        *logrus.Logger
	zapLogger     *zap.Logger
}

// APIConfig API配置
type APIConfig struct {
	EncryptionKey string `json:"encryption_key"`
	Version       string `json:"version"`
	Sign          string `json:"sign"`
	BaseURL       string `json:"base_url"`
}

// BindCardRequest 绑卡请求
type BindCardRequest struct {
	CardNo  string `json:"cardNo"`
	CardPwd string `json:"cardPwd"`
}

// BalanceQueryRequest 余额查询请求
type BalanceQueryRequest struct {
	CardStatus   string `json:"cardStatus"`
	CurrentPage  int    `json:"currentPage"`
	PageSize     int    `json:"pageSize"`
	Sign         string `json:"sign"`
}

// UserInfoRequest 用户信息查询请求
type UserInfoRequest struct {
	CurrentPage int    `json:"currentPage"`
	PageSize    int    `json:"pageSize"`
	Sign        string `json:"sign"`
}

// APIResponse API响应 - 兼容旧格式
type APIResponse struct {
	Success         bool                   `json:"success"`
	Code            string                 `json:"code,omitempty"`
	Message         string                 `json:"message,omitempty"`
	Data            map[string]interface{} `json:"data,omitempty"`
	RawAPIResponse  interface{}            `json:"raw_api_response,omitempty"` // 🔧 新增：保存原始API响应数据
}

// WalmartAPIResponse 沃尔玛API实际响应格式
type WalmartAPIResponse struct {
	LogID  string                 `json:"logId"`
	Status bool                   `json:"status"`
	Error  *WalmartAPIError       `json:"error,omitempty"`
	Data   interface{}            `json:"data,omitempty"` // 🔧 修复：data字段可能是数字(绑卡成功时为1)或对象(查询时为复杂结构)
}

// WalmartAPIError 沃尔玛API错误信息
type WalmartAPIError struct {
	ErrorCode  int         `json:"errorcode"`
	Message    string      `json:"message"`
	Redirect   interface{} `json:"redirect"`
	Validators interface{} `json:"validators"`
}

// BindCardResult 绑卡结果
type BindCardResult struct {
	Success         bool        `json:"success"`
	Message         string      `json:"message"`
	CardNo          string      `json:"cardNo,omitempty"`
	Balance         string      `json:"balance,omitempty"`
	CardBalance     string      `json:"cardBalance,omitempty"`
	BalanceCnt      string      `json:"balanceCnt,omitempty"`
	LogID           string      `json:"logId,omitempty"`
	ErrorCode       string      `json:"errorCode,omitempty"`
	RawAPIResponse  interface{} `json:"raw_api_response,omitempty"` // 🔧 新增：保存原始API响应数据
}

// BalanceQueryResult 余额查询结果
type BalanceQueryResult struct {
	Success         bool                     `json:"success"`
	Message         string                   `json:"message"`
	CardList        []map[string]interface{} `json:"cardList,omitempty"`
	TotalCount      int                      `json:"totalCount,omitempty"`
	LogID           string                   `json:"logId,omitempty"`
	ErrorCode       string                   `json:"errorCode,omitempty"`
	// 🔧 新增：匹配卡片的余额信息字段
	Balance         string                   `json:"balance,omitempty"`      // 余额字符串格式（如"10.00"）
	CardBalance     string                   `json:"cardBalance,omitempty"`  // 卡余额（分为单位）
	BalanceCnt      string                   `json:"balanceCnt,omitempty"`   // 余额计数格式
	RawAPIResponse  interface{}              `json:"raw_api_response,omitempty"` // 🔧 新增：保存原始API响应数据
}

// UserInfoResult 用户信息查询结果
type UserInfoResult struct {
	Success         bool        `json:"success"`
	Message         string      `json:"message"`
	NickName        string      `json:"nickName,omitempty"`
	CardCount       int         `json:"cardCount,omitempty"`
	HeadImg         string      `json:"headImg,omitempty"`         // 🔧 新增：用户头像URL
	UpcardOrderUrl  string      `json:"upcardOrderUrl,omitempty"`  // 🔧 新增：订单查询URL
	LogID           string      `json:"logId,omitempty"`
	ErrorCode       string      `json:"errorCode,omitempty"`
	RawAPIResponse  interface{} `json:"raw_api_response,omitempty"` // 🔧 新增：保存原始API响应数据
}

// NewAPIClient 创建新的API客户端
func NewAPIClient(config APIConfig, logger *logrus.Logger) *APIClient {
	// 初始化随机种子（确保nonce生成的随机性）
	rand.Seed(time.Now().UnixNano())

	// 🔧 创建Cookie jar来自动处理会话状态（修复余额查询"请先去登录"问题）
	jar, err := cookiejar.New(nil)
	if err != nil {
		logger.WithError(err).Warn("创建Cookie jar失败，将使用无Cookie模式")
		jar = nil
	}

	// 创建优化的HTTP传输配置
	transport := &http.Transport{
		MaxIdleConns:        200,              // 最大空闲连接数
		MaxIdleConnsPerHost: 100,              // 每个主机最大空闲连接数
		MaxConnsPerHost:     200,              // 每个主机最大连接数
		IdleConnTimeout:     90 * time.Second, // 空闲连接超时
		TLSHandshakeTimeout: 10 * time.Second, // TLS握手超时
		DisableKeepAlives:   false,            // 启用Keep-Alive
		DisableCompression:  false,            // 启用压缩支持（重要：处理gzip响应）
	}

	return &APIClient{
		encryptionKey: config.EncryptionKey,
		version:       config.Version,
		sign:          config.Sign,
		baseURL:       config.BaseURL,
		httpClient: &http.Client{
			Timeout:   30 * time.Second,
			Transport: transport,
			Jar:       jar, // 🔧 启用Cookie自动管理
		},
		logger: logger,
	}
}

// NewAPIClientWithZap 创建带Zap日志的API客户端
func NewAPIClientWithZap(config APIConfig, logger *logrus.Logger, zapLogger *zap.Logger) *APIClient {
	// 初始化随机种子（确保nonce生成的随机性）
	rand.Seed(time.Now().UnixNano())

	// 🔧 创建Cookie jar来自动处理会话状态（修复余额查询"请先去登录"问题）
	jar, err := cookiejar.New(nil)
	if err != nil {
		logger.WithError(err).Warn("创建Cookie jar失败，将使用无Cookie模式")
		jar = nil
	}

	// 创建优化的HTTP传输配置
	transport := &http.Transport{
		MaxIdleConns:        200,              // 最大空闲连接数
		MaxIdleConnsPerHost: 100,              // 每个主机最大空闲连接数
		MaxConnsPerHost:     200,              // 每个主机最大连接数
		IdleConnTimeout:     90 * time.Second, // 空闲连接超时
		TLSHandshakeTimeout: 10 * time.Second, // TLS握手超时
		DisableKeepAlives:   false,            // 启用Keep-Alive
		DisableCompression:  false,            // 启用压缩支持（重要：处理gzip响应）
	}

	return &APIClient{
		encryptionKey: config.EncryptionKey,
		version:       config.Version,
		sign:          config.Sign,
		baseURL:       config.BaseURL,
		httpClient: &http.Client{
			Timeout:   30 * time.Second,
			Transport: transport,
			Jar:       jar, // 🔧 启用Cookie自动管理
		},
		logger:    logger,
		zapLogger: zapLogger,
	}
}

// ParseCKSign 解析CK签名格式 (sign#encryption_key#version)
func ParseCKSign(ckSign string) (APIConfig, error) {
	parts := strings.Split(ckSign, "#")
	if len(parts) < 3 {
		return APIConfig{}, fmt.Errorf("invalid CK sign format: %s", ckSign)
	}

	return APIConfig{
		Sign:          parts[0],
		EncryptionKey: parts[1],
		Version:       parts[2],
	}, nil
}

// generateNonce 生成随机nonce字符串
func (c *APIClient) generateNonce(length int) string {
	const charset = "abcdefghijklmnopqrstuvwxyz"
	b := make([]byte, length)
	for i := range b {
		b[i] = charset[rand.Intn(len(charset))]
	}
	return string(b)
}

// calculateSignature 计算请求签名
func (c *APIClient) calculateSignature(requestBody map[string]interface{}) (string, error) {
	// 对请求体参数进行排序，确保参数顺序一致
	sortedBody := make(map[string]interface{})
	
	// 获取所有键并排序
	keys := make([]string, 0, len(requestBody))
	for k := range requestBody {
		keys = append(keys, k)
	}
	sort.Strings(keys)
	
	// 按排序后的键重新构建map
	for _, k := range keys {
		sortedBody[k] = requestBody[k]
	}

	// 将排序后的请求体转换为JSON字符串（与Python版本保持一致的格式）
	// Python使用 separators=(",", ":") 确保紧凑格式
	bodyBytes, err := json.Marshal(sortedBody)
	if err != nil {
		return "", fmt.Errorf("failed to marshal request body: %w", err)
	}

	// 计算HMAC-SHA256签名
	key := []byte(c.encryptionKey)
	h := hmac.New(sha256.New, key)
	h.Write(bodyBytes)
	signature := hex.EncodeToString(h.Sum(nil))
	
	// 转换为大写（与Python实现保持一致）
	return strings.ToUpper(signature), nil
}

// makeRequest 发送API请求
func (c *APIClient) makeRequest(endpoint string, requestBody map[string]interface{}, method string) (*APIResponse, error) {
	// 构建完整URL
	url := c.baseURL
	if !strings.HasSuffix(url, endpoint) {
		if !strings.HasSuffix(url, "/") && !strings.HasPrefix(endpoint, "/") {
			url += "/"
		}
		url += endpoint
	}

	// 生成请求头参数（使用毫秒级时间戳）
	nonce := c.generateNonce(10)
	timestamp := strconv.FormatInt(time.Now().UnixMilli(), 10)

	// 计算签名
	signature, err := c.calculateSignature(requestBody)
	if err != nil {
		return nil, fmt.Errorf("failed to calculate signature: %w", err)
	}

	// 构建完整的微信小程序请求头（与Python生产环境完全一致）
	headers := map[string]string{
		// 基础HTTP头部（与Python版本保持一致）
		"User-Agent":       "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 MicroMessenger/7.0.20.1781(0x6700143B) NetType/WIFI MiniProgramEnv/Windows WindowsWechat/WMPF WindowsWechat(0x63090a13) XWEB/8555",
		"Connection":       "keep-alive",
		"Accept":           "*/*",
		"Accept-Encoding":  "gzip, deflate, br",
		"Content-Type":     "application/json",
		"Accept-Language":  "zh-CN,zh;q=0.9",

		// 微信小程序特有头部
		"xweb_xhr":         "1",
		"Referer":          "https://servicewechat.com/wx81d3e1fe4c2e11b4/168/page-frame.html", // 与Python版本保持一致

		// 签名相关头部
		"nonce":            nonce,
		"timestamp":        timestamp,
		"signature":        signature,
		"version":          c.version,
	}

	// 序列化请求体
	bodyBytes, err := json.Marshal(requestBody)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal request body: %w", err)
	}

	// 创建HTTP请求
	req, err := http.NewRequest(method, url, bytes.NewBuffer(bodyBytes))
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	// 设置请求头
	for key, value := range headers {
		req.Header.Set(key, value)
	}

	// 记录请求日志
	c.logger.WithFields(logrus.Fields{
		"url":       url,
		"method":    method,
		"nonce":     nonce,
		"timestamp": timestamp,
		"signature": signature[:8] + "...", // 只记录签名前8位
	}).Info("Making API request")

	// 发送请求
	resp, err := c.httpClient.Do(req)
	if err != nil {
		return nil, fmt.Errorf("failed to send request: %w", err)
	}
	defer resp.Body.Close()

	// 检查响应是否为gzip压缩
	var reader io.Reader = resp.Body
	if resp.Header.Get("Content-Encoding") == "gzip" {
		gzipReader, err := gzip.NewReader(resp.Body)
		if err != nil {
			return nil, fmt.Errorf("创建gzip读取器失败: %w", err)
		}
		defer gzipReader.Close()
		reader = gzipReader

		c.logger.WithField("content_encoding", "gzip").Debug("检测到gzip压缩响应，正在解压")
	}

	// 读取响应（可能已解压）
	respBody, err := io.ReadAll(reader)
	if err != nil {
		return nil, fmt.Errorf("failed to read response: %w", err)
	}

	// 记录响应日志
	c.logger.WithFields(logrus.Fields{
		"status_code":     resp.StatusCode,
		"content_encoding": resp.Header.Get("Content-Encoding"),
		"body_length":     len(respBody),
		"response_body":   string(respBody),
	}).Info("API response received")

	// 解析响应
	var apiResp APIResponse
	if err := json.Unmarshal(respBody, &apiResp); err != nil {
		return nil, fmt.Errorf("failed to unmarshal response: %w", err)
	}

	return &apiResp, nil
}

// BindCard 绑卡API调用 - 完整实现
func (c *APIClient) BindCard(ctx context.Context, cardNo, cardPwd string, debug bool, amount int) (*BindCardResult, error) {
	startTime := time.Now()

	// 记录开始日志
	if c.zapLogger != nil {
		c.zapLogger.Info("开始绑卡API调用",
			zap.String("card_no", maskCardNumber(cardNo)),
			zap.Bool("debug", debug),
		)
	}

	// 检查debug模式
	if debug {
		c.logger.WithFields(logrus.Fields{
			"card_no": maskCardNumber(cardNo),
			"amount": amount,
		}).Info("[TEST_MODE] Calling bind card API (using mock data)")

		return c.createMockBindResult(cardNo, amount), nil
	}

	// 构建绑卡请求参数，严格按照Python生产环境的参数顺序
	requestBody := map[string]interface{}{
		"sign":        c.sign,        // 与Python版本保持一致：第一个参数
		"storeId":     "",            // 与Python版本保持一致：第二个参数
		"userPhone":   "",            // 与Python版本保持一致：第三个参数
		"cardNo":      cardNo,        // 与Python版本保持一致：第四个参数
		"cardPwd":     cardPwd,       // 与Python版本保持一致：第五个参数
		"currentPage": 0,             // 与Python版本保持一致：第六个参数
		"pageSize":    0,             // 与Python版本保持一致：第七个参数
	}

	c.logger.WithFields(logrus.Fields{
		"card_no": maskCardNumber(cardNo),
	}).Info("Calling bind card API")

	// 调用API
	response, err := c.makeRequestWithContext(ctx, "/app/card/mem/bind.json", requestBody, "POST")
	if err != nil {
		// 记录错误日志
		if c.zapLogger != nil {
			c.zapLogger.Error("绑卡API调用失败",
				zap.String("card_no", maskCardNumber(cardNo)),
				zap.Error(err),
				zap.Duration("duration", time.Since(startTime)),
			)
		}
		return &BindCardResult{
			Success:   false,
			Message:   fmt.Sprintf("API调用失败: %v", err),
			ErrorCode: "API_CALL_ERROR",
		}, err
	}

	// 解析响应
	result := c.parseBindCardResponse(response)

	// 记录结果日志
	if c.zapLogger != nil {
		c.zapLogger.Info("绑卡API调用完成",
			zap.String("card_no", maskCardNumber(cardNo)),
			zap.Bool("success", result.Success),
			zap.String("message", result.Message),
			zap.Duration("duration", time.Since(startTime)),
		)
	}

	return result, nil
}

// GetCardBalance 查询卡余额API调用 - 完整实现
func (c *APIClient) GetCardBalance(ctx context.Context, cardNo string, debug bool, amount ...int) (*BalanceQueryResult, error) {
	startTime := time.Now()

	// 记录开始日志
	if c.zapLogger != nil {
		c.zapLogger.Info("开始查询卡余额API调用",
			zap.String("card_no", maskCardNumber(cardNo)),
			zap.Bool("debug", debug),
		)
	}

	// 检查debug模式
	if debug {
		// 获取金额参数，如果没有提供则使用默认值100分
		mockAmount := 100
		if len(amount) > 0 {
			mockAmount = amount[0]
		}

		c.logger.WithFields(logrus.Fields{
			"card_no": maskCardNumber(cardNo),
			"amount": mockAmount,
		}).Info("[TEST_MODE] Calling get card balance API (using mock data)")

		return c.createMockBalanceResult(cardNo, mockAmount), nil
	}

	// 构建金额查询请求参数，严格按照Python版本的参数顺序
	requestBody := map[string]interface{}{
		"cardStatus":  "A",          // 与Python版本保持一致：第一个参数
		"currentPage": 1,            // 与Python版本保持一致：第二个参数
		"pageSize":    10,           // 与Python版本保持一致：第三个参数
		"sign":        c.sign,       // 与Python版本保持一致：第四个参数
	}

	c.logger.WithFields(logrus.Fields{
		"card_no": maskCardNumber(cardNo),
	}).Info("Calling get card balance API")

	// 调用API
	response, err := c.makeRequestWithContext(ctx, "/app/card/mem/pageList.json", requestBody, "POST")
	if err != nil {
		// 记录错误日志
		if c.zapLogger != nil {
			c.zapLogger.Error("查询卡余额API调用失败",
				zap.String("card_no", maskCardNumber(cardNo)),
				zap.Error(err),
				zap.Duration("duration", time.Since(startTime)),
			)
		}
		return &BalanceQueryResult{
			Success:   false,
			Message:   fmt.Sprintf("API调用失败: %v", err),
			ErrorCode: "API_CALL_ERROR",
		}, err
	}

	// 解析响应
	result := c.parseBalanceQueryResponse(response, cardNo)

	// 记录结果日志
	if c.zapLogger != nil {
		c.zapLogger.Info("查询卡余额API调用完成",
			zap.String("card_no", maskCardNumber(cardNo)),
			zap.Bool("success", result.Success),
			zap.String("message", result.Message),
			zap.Int("card_count", len(result.CardList)),
			zap.Duration("duration", time.Since(startTime)),
		)
	}

	return result, nil
}

// QueryUserInfo 查询用户信息API调用 - 完整实现
func (c *APIClient) QueryUserInfo(ctx context.Context, debug bool) (*UserInfoResult, error) {
	startTime := time.Now()

	// 记录开始日志
	if c.zapLogger != nil {
		c.zapLogger.Info("开始查询用户信息API调用",
			zap.Bool("debug", debug),
		)
	}

	// 检查debug模式
	if debug {
		c.logger.Info("[TEST_MODE] Calling query user info API (using mock data)")
		return c.createMockUserInfoResult(), nil
	}

	// 构建用户信息查询请求参数，严格按照生产环境抓包数据的参数顺序
	requestBody := map[string]interface{}{
		"currentPage": 0,
		"pageSize":    0,
		"sign":        c.sign,
	}

	c.logger.Info("Calling query user info API")

	// 调用API
	response, err := c.makeRequestWithContext(ctx, "/app/mem/userInfo.json", requestBody, "POST")
	if err != nil {
		// 记录错误日志
		if c.zapLogger != nil {
			c.zapLogger.Error("查询用户信息API调用失败",
				zap.Error(err),
				zap.Duration("duration", time.Since(startTime)),
			)
		}
		return &UserInfoResult{
			Success:   false,
			Message:   fmt.Sprintf("API调用失败: %v", err),
			ErrorCode: "API_CALL_ERROR",
		}, err
	}

	// 解析响应
	result := c.parseUserInfoResponse(response)

	// 记录结果日志
	if c.zapLogger != nil {
		c.zapLogger.Info("查询用户信息API调用完成",
			zap.Bool("success", result.Success),
			zap.String("message", result.Message),
			zap.String("nick_name", result.NickName),
			zap.Int("card_count", result.CardCount),
			zap.Duration("duration", time.Since(startTime)),
		)
	}

	return result, nil
}

// makeRequestWithContext 带上下文的HTTP请求 - 与Python版本保持一致
func (c *APIClient) makeRequestWithContext(ctx context.Context, endpoint string, requestBody map[string]interface{}, method string) (*APIResponse, error) {
	// 构建完整URL
	url := c.baseURL
	if !strings.HasSuffix(url, endpoint) {
		if !strings.HasSuffix(url, "/") && !strings.HasPrefix(endpoint, "/") {
			url += "/"
		}
		url += endpoint
	}

	// 生成请求头参数（使用毫秒级时间戳）
	nonce := c.generateNonce(10)
	timestamp := strconv.FormatInt(time.Now().UnixMilli(), 10)

	// 计算签名
	signature, err := c.calculateSignature(requestBody)
	if err != nil {
		return nil, fmt.Errorf("计算签名失败: %w", err)
	}

	// 构建完整的微信小程序请求头（与Python生产环境完全一致）
	headers := map[string]string{
		// 基础HTTP头部（与Python版本保持一致）
		"User-Agent":       "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 MicroMessenger/7.0.20.1781(0x6700143B) NetType/WIFI MiniProgramEnv/Windows WindowsWechat/WMPF WindowsWechat(0x63090a13) XWEB/8555",
		"Connection":       "keep-alive",
		"Accept":           "*/*",
		"Accept-Encoding":  "gzip, deflate, br",
		"Content-Type":     "application/json",
		"Accept-Language":  "zh-CN,zh;q=0.9",

		// 微信小程序特有头部
		"xweb_xhr":         "1",
		"Referer":          "https://servicewechat.com/wx81d3e1fe4c2e11b4/168/page-frame.html",

		// 签名相关头部
		"nonce":            nonce,
		"timestamp":        timestamp,
		"signature":        signature,
		"version":          c.version,
	}

	// 序列化请求体（直接使用原始请求体，与Python版本保持一致）
	jsonData, err := json.Marshal(requestBody)
	if err != nil {
		return nil, fmt.Errorf("序列化请求体失败: %w", err)
	}

	// 创建HTTP请求
	req, err := http.NewRequestWithContext(ctx, method, url, bytes.NewBuffer(jsonData))
	if err != nil {
		return nil, fmt.Errorf("创建HTTP请求失败: %w", err)
	}

	// 设置请求头
	for key, value := range headers {
		req.Header.Set(key, value)
	}

	// 发送请求
	resp, err := c.httpClient.Do(req)
	if err != nil {
		return nil, fmt.Errorf("HTTP请求失败: %w", err)
	}
	defer resp.Body.Close()

	// 检查响应是否为gzip压缩
	var reader io.Reader = resp.Body
	if resp.Header.Get("Content-Encoding") == "gzip" {
		gzipReader, err := gzip.NewReader(resp.Body)
		if err != nil {
			return nil, fmt.Errorf("创建gzip读取器失败: %w", err)
		}
		defer gzipReader.Close()
		reader = gzipReader

		if c.zapLogger != nil {
			c.zapLogger.Debug("检测到gzip压缩响应，正在解压")
		}
	}

	// 读取响应（可能已解压）
	body, err := io.ReadAll(reader)
	if err != nil {
		return nil, fmt.Errorf("读取响应失败: %w", err)
	}

	// 记录原始响应用于调试
	if c.zapLogger != nil {
		c.zapLogger.Debug("API响应内容",
			zap.String("content_encoding", resp.Header.Get("Content-Encoding")),
			zap.Int("body_length", len(body)),
			zap.String("body", string(body)))
	}

	// 尝试解析为新的沃尔玛API响应格式
	var walmartResp WalmartAPIResponse
	if err := json.Unmarshal(body, &walmartResp); err != nil {
		return nil, fmt.Errorf("解析响应失败: %w", err)
	}

	// 🔧 修复：转换为兼容的APIResponse格式，处理data字段可能是数字或对象的情况
	var dataMap map[string]interface{}

	// 检查data字段的类型
	if walmartResp.Data != nil {
		if dataAsMap, ok := walmartResp.Data.(map[string]interface{}); ok {
			// data是对象，直接使用
			dataMap = dataAsMap
		} else {
			// data是其他类型（如数字1表示绑卡成功），创建新的map并保存原始值
			dataMap = make(map[string]interface{})
			dataMap["data"] = walmartResp.Data
		}
	} else {
		dataMap = make(map[string]interface{})
	}

	apiResp := &APIResponse{
		Success:        walmartResp.Status,
		Data:           dataMap,
		RawAPIResponse: walmartResp, // 🔧 保存原始API响应数据
	}

	// 设置LogID到Data中
	apiResp.Data["logId"] = walmartResp.LogID

	// 🔧 修复：处理错误信息，注意绑卡成功时errorcode为1
	if walmartResp.Error != nil {
		// 检查errorcode：1表示成功，其他值表示失败
		if walmartResp.Error.ErrorCode == 1 {
			// errorcode为1表示成功，保持Success为true
			apiResp.Code = "200"
			apiResp.Message = "操作成功"
		} else {
			// errorcode不为1表示失败
			apiResp.Success = false
			apiResp.Code = fmt.Sprintf("%d", walmartResp.Error.ErrorCode)
			apiResp.Message = walmartResp.Error.Message
		}

		// 将错误信息也放入Data中，保持与现有解析逻辑兼容
		apiResp.Data["error"] = map[string]interface{}{
			"errorcode": walmartResp.Error.ErrorCode,
			"message":   walmartResp.Error.Message,
			"redirect":  walmartResp.Error.Redirect,
			"validators": walmartResp.Error.Validators,
		}
	} else if !walmartResp.Status {
		// 如果status为false但没有error字段，设置默认错误
		apiResp.Success = false
		apiResp.Message = "操作失败"
	}

	return apiResp, nil
}

// parseBindCardResponse 解析绑卡响应
func (c *APIClient) parseBindCardResponse(response *APIResponse) *BindCardResult {
	result := &BindCardResult{
		Success:        response.Success,
		Message:        response.Message,
		RawAPIResponse: response.RawAPIResponse, // 🔧 保存原始API响应数据
	}

	// 设置LogID（从顶层获取）
	if response.Data != nil {
		if logID, ok := response.Data["logId"].(string); ok {
			result.LogID = logID
		}
	}

	if response.Success {
		// 🔧 修复：绑卡成功时，真实API的data字段是数字1，不包含卡片详细信息
		// 卡片信息需要通过单独的获取卡包接口获取
		if response.Data != nil {
			// 检查是否有原始data值（当API返回data:1时，我们存储在data.data中）
			if originalData, exists := response.Data["data"]; exists {
				c.logger.Debug("绑卡成功，API返回data值", zap.Any("data_value", originalData))
				// 对于绑卡成功，data通常是数字1，不需要进一步解析
			}

			// 以下字段在真实绑卡成功响应中通常不存在，仅用于测试模式或特殊情况
			if cardNo, ok := response.Data["cardNo"].(string); ok {
				result.CardNo = cardNo
			}
			if balance, ok := response.Data["balance"].(string); ok {
				result.Balance = balance
			}
			if cardBalance, ok := response.Data["cardBalance"].(string); ok {
				result.CardBalance = cardBalance
			}
			if balanceCnt, ok := response.Data["balanceCnt"].(string); ok {
				result.BalanceCnt = balanceCnt
			}
		}
	} else {
		// 绑卡失败：解析error字段中的错误信息
		// 真实格式：{"error":{"errorcode":10131,"message":"该电子卡已被其他用户绑定"}}
		if errorData, ok := response.Data["error"].(map[string]interface{}); ok {
			if errorCode, ok := errorData["errorcode"].(float64); ok {
				result.ErrorCode = fmt.Sprintf("%.0f", errorCode)
			}
			if errorMessage, ok := errorData["message"].(string); ok {
				result.Message = errorMessage
			}
		}

		// 如果没有从error字段获取到错误码，使用response.Code
		if result.ErrorCode == "" {
			result.ErrorCode = response.Code
			if result.ErrorCode == "" {
				result.ErrorCode = "UNKNOWN_ERROR"
			}
		}
	}

	return result
}

// createMockBindResult 创建模拟绑卡结果
func (c *APIClient) createMockBindResult(cardNo string, amount int) *BindCardResult {
	// 测试模式下100%返回成功
	return &BindCardResult{
		Success:     true,
		Message:     "",    // 成功时message为null/空
		CardNo:      cardNo,
		Balance:     "",    // 绑卡接口不返回金额，留空
		CardBalance: "",    // 绑卡接口不返回金额，留空
		BalanceCnt:  "",    // 绑卡接口不返回金额，留空
		LogID:       fmt.Sprintf("TEST_MOCK_%d", time.Now().UnixNano()),
	}
}

// createMockBindResponse 创建模拟绑卡响应
func (c *APIClient) createMockBindResponse(cardNo string) *APIResponse {
	return &APIResponse{
		Success: true,
		Code:    "200",
		Message: "Mock bind success",
		Data: map[string]interface{}{
			"cardNo":      cardNo,
			"balance":     "10000",
			"cardBalance": "100.00",
			"balanceCnt":  "100.00",
			"logId":       fmt.Sprintf("MOCK_BIND_%d", time.Now().Unix()),
			"status":      true,
		},
	}
}

// createMockBalanceResponse 创建模拟余额查询响应
func (c *APIClient) createMockBalanceResponse(cardNo string) *APIResponse {
	return &APIResponse{
		Success: true,
		Code:    "200",
		Message: "Mock balance query success",
		Data: map[string]interface{}{
			"cardList": []map[string]interface{}{
				{
					"cardNo":      cardNo,
					"balance":     "10000",
					"cardBalance": "100.00",
					"balanceCnt":  "100.00",
					"cardStatus":  "A",
				},
			},
			"totalCount": 1,
			"logId":      fmt.Sprintf("MOCK_BALANCE_%d", time.Now().Unix()),
			"status":     true,
		},
	}
}

// createMockUserInfoResponse 创建模拟用户信息响应
func (c *APIClient) createMockUserInfoResponse() *APIResponse {
	return &APIResponse{
		Success: true,
		Code:    "200",
		Message: "Mock user info query success",
		Data: map[string]interface{}{
			"nickName":   "测试用户",
			"cardCount":  5,
			"totalBalance": "50000",
			"logId":      fmt.Sprintf("MOCK_USER_%d", time.Now().Unix()),
			"status":     true,
		},
	}
}

// maskCardNumber 遮蔽卡号敏感信息
func maskCardNumber(cardNo string) string {
	if len(cardNo) <= 6 {
		return cardNo
	}
	return cardNo[:6] + "***"
}

// SetBaseURL 设置基础URL
func (c *APIClient) SetBaseURL(baseURL string) {
	c.baseURL = baseURL
}

// GetBaseURL 获取基础URL
func (c *APIClient) GetBaseURL() string {
	return c.baseURL
}

// parseBalanceQueryResponse 解析余额查询响应
func (c *APIClient) parseBalanceQueryResponse(response *APIResponse, cardNo string) *BalanceQueryResult {
	result := &BalanceQueryResult{
		Success:        response.Success,
		Message:        response.Message,
		RawAPIResponse: response.RawAPIResponse, // 🔧 保存原始API响应数据
	}



	if response.Data != nil {
		// 解析日志ID
		if logID, ok := response.Data["logId"].(string); ok {
			result.LogID = logID
		}

		// 🔧 修复：直接从response.Data["list"]解析卡片列表
		cardList := make([]map[string]interface{}, 0)
		var targetCard map[string]interface{}

		// 解析list字段（真实API返回的是data.list，不是data.data.list）
		if listData, ok := response.Data["list"].([]interface{}); ok {
				cardList = make([]map[string]interface{}, 0, len(listData))

				// 遍历所有卡片，查找匹配的卡号
				for i, cardData := range listData {
					if cardMap, ok := cardData.(map[string]interface{}); ok {
						cardList = append(cardList, cardMap)

						// 🔧 关键修复：通过卡号匹配找到目标卡片（支持多种类型转换）
						if cardNoField, exists := cardMap["cardNo"]; exists {
							var cardNoStr string

							// 🔧 修复：支持多种类型的cardNo字段
							switch v := cardNoField.(type) {
							case string:
								cardNoStr = v
							case float64:
								// JSON解析大数字时可能变成float64
								cardNoStr = fmt.Sprintf("%.0f", v)
							case int:
								cardNoStr = fmt.Sprintf("%d", v)
							case int64:
								cardNoStr = fmt.Sprintf("%d", v)
							default:
								c.logger.WithFields(logrus.Fields{
									"card_index": i,
									"cardNo_type": fmt.Sprintf("%T", cardNoField),
									"cardNo_value": cardNoField,
								}).Warn("🔍 [调试] cardNo字段类型未知，尝试字符串转换")
								cardNoStr = fmt.Sprintf("%v", cardNoField)
							}

							c.logger.WithFields(logrus.Fields{
								"target_card_no": cardNo,
								"card_index": i,
								"card_no_in_list": cardNoStr,
								"is_match": cardNoStr == cardNo,
								"cardNo_original_type": fmt.Sprintf("%T", cardNoField),
							}).Info("🔍 [调试] 卡号匹配检查")

							if cardNoStr == cardNo {
								targetCard = cardMap
								c.logger.WithFields(logrus.Fields{
									"card_no": maskCardNumber(cardNo),
									"found_card": true,
									"card_index": i,
								}).Info("✅ 找到匹配的卡片")
							}
						} else {
							c.logger.WithFields(logrus.Fields{
								"card_index": i,
							}).Warn("🔍 [调试] 卡片中没有cardNo字段")
						}
					} else {
						c.logger.WithFields(logrus.Fields{
							"card_index": i,
							"cardData_type": fmt.Sprintf("%T", cardData),
						}).Warn("🔍 [调试] cardData不是map类型")
					}
				}
			}

			result.CardList = cardList

			// 🔧 关键修复：从匹配的卡片中提取余额信息
			if targetCard != nil {
				// 提取余额信息（多种格式兼容）
				if balance, ok := targetCard["balance"].(string); ok {
					result.Balance = balance
				}

				// 🔧 修复：根据真实数据，cardBalance是int类型，不是float64
				if cardBalance, ok := targetCard["cardBalance"].(int); ok {
					result.CardBalance = fmt.Sprintf("%d", cardBalance)
				} else if cardBalance, ok := targetCard["cardBalance"].(float64); ok {
					// 兼容float64类型
					result.CardBalance = fmt.Sprintf("%.0f", cardBalance)
				}

				if balanceCnt, ok := targetCard["balanceCnt"].(string); ok {
					result.BalanceCnt = balanceCnt
				}

				// 记录找到的余额信息
				c.logger.WithFields(logrus.Fields{
					"card_no": maskCardNumber(cardNo),
					"balance": result.Balance,
					"card_balance": result.CardBalance,
					"balance_cnt": result.BalanceCnt,
				}).Info("成功提取卡片余额信息")
			} else {
				// 没有找到匹配的卡片
				c.logger.WithFields(logrus.Fields{
					"card_no": maskCardNumber(cardNo),
					"total_cards": len(cardList),
				}).Warn("未找到匹配的卡片")
		}

	// 解析总页数（直接从response.Data获取）
	if totalPages, ok := response.Data["totalPages"].(float64); ok {
		result.TotalCount = int(totalPages)
	}
}

// 设置错误码
if !response.Success {
		result.ErrorCode = response.Code
		if result.ErrorCode == "" {
			result.ErrorCode = "UNKNOWN_ERROR"
		}
	}

	return result
}

// parseUserInfoResponse 解析用户信息响应
func (c *APIClient) parseUserInfoResponse(response *APIResponse) *UserInfoResult {
	result := &UserInfoResult{
		Success:        response.Success,
		Message:        response.Message,
		RawAPIResponse: response.RawAPIResponse, // 🔧 保存原始API响应数据
	}

	// 🔧 修复：logId在顶层，不在data字段中
	if response.Data != nil {
		if logID, ok := response.Data["logId"].(string); ok {
			result.LogID = logID
		}
	}

	if response.Data != nil {
		// 解析data字段中的用户信息
		if dataField, ok := response.Data["data"].(map[string]interface{}); ok {
			// 解析昵称
			if nickName, ok := dataField["nickName"].(string); ok {
				result.NickName = nickName
			}

			// 🔧 修复：cardCount可能是int或float64类型
			if cardCount, ok := dataField["cardCount"].(float64); ok {
				result.CardCount = int(cardCount)
			} else if cardCount, ok := dataField["cardCount"].(int); ok {
				result.CardCount = cardCount
			}

			// 🔧 新增：解析头像URL
			if headImg, ok := dataField["headImg"].(string); ok {
				result.HeadImg = headImg
			}

			// 🔧 新增：解析订单查询URL
			if upcardOrderUrl, ok := dataField["upcardOrderUrl"].(string); ok {
				result.UpcardOrderUrl = upcardOrderUrl
			}
		}
	}

	// 设置错误码
	if !response.Success {
		result.ErrorCode = response.Code
		if result.ErrorCode == "" {
			result.ErrorCode = "UNKNOWN_ERROR"
		}
	}

	return result
}

// createMockBalanceResult 创建模拟余额查询结果
func (c *APIClient) createMockBalanceResult(cardNo string, amount int) *BalanceQueryResult {
	// 测试模式下100%返回成功
	// 将金额从分转换为元（保留两位小数）
	amountInYuan := fmt.Sprintf("%.2f", float64(amount)/100.0)

	// 模拟真实获取卡包接口的返回格式
	return &BalanceQueryResult{
		Success: true,
		Message: "",    // 成功时message为null/空
		CardList: []map[string]interface{}{
			{
				"id":                   167305744,
				"mchNo":               "100530000004",
				"cardNo":              cardNo,
				"code":                "184700372",
				"nickName":            "微信用户（测试模式）",
				"userId":              30045432,
				"sourceType":          "绑卡",
				"cardId":              1,
				"wxCardId":            "ppN5bt0i6b39LoRV0kggnrHaf4Gk",
				"giveId":              0,
				"receiveStatus":       false,
				"status":              1,
				"type":                1,
				"createUserId":        30045432,
				"storeId":             "",
				"storeName":           nil,
				"channelType":         1,
				"holdCardTime":        time.Now().UnixMilli(),
				"colorType":           0,
				"bgPic":               4462,
				"bgPicUrl":            "http://mmbiz.qpic.cn/mmbiz_jpg/MjbyiaBuoxn5Q6fFaDtK0lb0ZkEGBTHoLM6yWU1w2Be5WViaXia5ZvsO7aKtUPZkulsSLb1dflZ3ibaMcicBqE4kxOA/0",
				"initMoney":           amount,                      // 使用用户请求的金额（分）
				"lastHoldCardTime":    time.Now().UnixMilli(),
				"updateTime":          time.Now().UnixMilli(),
				"createTime":          time.Now().UnixMilli(),
				"giveFlag":            true,
				"transferFlag":        true,
				"channelId":           0,
				"channelName":         nil,
				"lockFlag":            false,
				"cardBalance":         amount,                      // 单位：分，使用用户请求的原始金额
				"upcardCardStatus":    nil,
				"cardExpiryDate":      nil,
				"upcardType":          0,
				"bizType":             1,
				"startTime":           nil,
				"endTime":             nil,
				"totalUserNum":        nil,
				"totalCardNum":        nil,
				"balance":             amountInYuan,               // 单位：元，根据实际金额计算
				"expiredDate":         "2028.06.30",
				"brandName":           nil,
				"wxLogoUrl":           nil,
				"logo":                nil,
				"cardName":            nil,
				"statusList":          nil,
				"flag":                nil,
				"sign":                nil,
				"cardColor":           nil,
				"qrcode":              nil,
				"expires":             0,
				"url":                 nil,
				"walWxMemberCardDTO":  nil,
				"unbindType":          nil,
				"gloryCdKey":          nil,
				"gloryExpireDate":     nil,
				"allowUnbind":         1,
				"allowExpire":         0,
				"cardExpiredDate":     "20280630",
				"expireMsg":           nil,
				"hotReason":           nil,
				"consumeFlag":         nil,
				"consumeLimitInfo":    nil,
				"bizTypeList":         nil,
				"bgPicEnc":            "4462?sign=4C04373E819BDC3B641A155709612740",
				"historyFlag":         nil,
				"tbIndex":             nil,
				"tbIndex2":            nil,
				"userTotalFlag":       nil,
				"giveCheck":           nil,
				"expireDateCnt":       "2028.06.30",
				"expireDateInt":       "2028.06.30",
				"expireDateCount":     365,
				"balanceCnt":          amountInYuan,               // 单位：元，根据实际金额计算
			},
		},
		TotalCount: 1,
		LogID:      fmt.Sprintf("TEST_MOCK_%d", time.Now().UnixNano()),
	}
}

// createMockUserInfoResult 创建模拟用户信息结果
func (c *APIClient) createMockUserInfoResult() *UserInfoResult {
	// 测试模式下100%返回成功
	return &UserInfoResult{
		Success:         true,
		Message:         "",    // 成功时message为null/空
		NickName:        "微信用户（测试模式）",
		CardCount:       4,
		HeadImg:         "https://test.example.com/avatar.jpg",  // 🔧 修复：测试头像URL
		UpcardOrderUrl:  "https://test.example.com/order",      // 🔧 修复：测试订单URL
		LogID:           fmt.Sprintf("TEST_MOCK_%d", time.Now().UnixNano()),
	}
}

// CalculateSignatureForTest 测试用的签名计算方法（导出版本）
func (c *APIClient) CalculateSignatureForTest(requestBody map[string]interface{}) (string, error) {
	return c.calculateSignature(requestBody)
}

// GenerateNonceForTest 测试用的nonce生成方法（导出版本）
func (c *APIClient) GenerateNonceForTest(length int) string {
	return c.generateNonce(length)
}

// GetHTTPClientForTest 测试用的HTTP客户端访问方法（导出版本）
func (c *APIClient) GetHTTPClientForTest() *http.Client {
	return c.httpClient
}
