"""
Telegram配置服务测试
"""

import pytest
from sqlalchemy.orm import Session

from app.services.telegram_config_service import TelegramConfigService
from app.models.telegram_group import TelegramGroup, BindStatus, ChatType
from app.models.merchant_telegram_setting import MerchantTelegramSetting
from app.models.telegram_permission_template import TelegramPermissionTemplate
from app.models.merchant import Merchant


class TestTelegramConfigService:
    """测试Telegram配置服务"""
    
    def test_get_global_default_settings(self, db: Session):
        """测试获取全局默认配置"""
        config_service = TelegramConfigService(db)
        settings = config_service.get_global_default_settings()
        
        assert isinstance(settings, dict)
        assert "permissions" in settings
        assert "display_settings" in settings
        assert "notification_settings" in settings
        assert "advanced_settings" in settings
        
        # 验证默认权限配置
        permissions = settings["permissions"]
        assert permissions["allow_all_members"] is False
        assert permissions["require_user_verification"] is True
        assert "rate_limit" in permissions
        assert "query_permissions" in permissions
    
    def test_get_merchant_settings(self, db: Session, test_merchant: Merchant):
        """测试获取商户级配置"""
        config_service = TelegramConfigService(db)
        
        # 测试没有自定义配置的情况
        settings = config_service.get_merchant_settings(test_merchant.id)
        assert settings == {}
        
        # 创建商户配置
        merchant_setting = MerchantTelegramSetting(
            merchant_id=test_merchant.id,
            settings={
                "permissions": {
                    "rate_limit": {
                        "commands_per_minute": 5
                    }
                }
            }
        )
        db.add(merchant_setting)
        db.commit()
        
        # 测试有自定义配置的情况
        settings = config_service.get_merchant_settings(test_merchant.id)
        assert "permissions" in settings
        assert settings["permissions"]["rate_limit"]["commands_per_minute"] == 5
    
    def test_get_group_settings(self, db: Session, test_merchant: Merchant):
        """测试获取群组级配置"""
        config_service = TelegramConfigService(db)
        
        # 创建群组
        group = TelegramGroup(
            chat_id=123456789,
            chat_title="测试群组",
            chat_type=ChatType.GROUP,
            merchant_id=test_merchant.id,
            bind_token="test_token",
            bind_status=BindStatus.ACTIVE,
            settings={
                "display_settings": {
                    "show_details": True
                }
            }
        )
        db.add(group)
        db.commit()
        
        # 测试群组配置
        settings = config_service.get_group_settings(group)
        assert "display_settings" in settings
        assert settings["display_settings"]["show_details"] is True
    
    def test_get_effective_group_settings(self, db: Session, test_merchant: Merchant):
        """测试获取有效群组配置（三级继承）"""
        config_service = TelegramConfigService(db)
        
        # 创建商户配置
        merchant_setting = MerchantTelegramSetting(
            merchant_id=test_merchant.id,
            settings={
                "permissions": {
                    "rate_limit": {
                        "commands_per_minute": 5,
                        "queries_per_hour": 50
                    }
                },
                "display_settings": {
                    "decimal_places": 3
                }
            }
        )
        db.add(merchant_setting)
        db.commit()
        
        # 创建群组配置
        group = TelegramGroup(
            chat_id=123456789,
            chat_title="测试群组",
            chat_type=ChatType.GROUP,
            merchant_id=test_merchant.id,
            bind_token="test_token",
            bind_status=BindStatus.ACTIVE,
            settings={
                "permissions": {
                    "rate_limit": {
                        "commands_per_minute": 3  # 覆盖商户配置
                    }
                },
                "display_settings": {
                    "show_details": True  # 新增配置
                }
            }
        )
        db.add(group)
        db.commit()
        
        # 测试有效配置（三级合并）
        effective_settings = config_service.get_effective_group_settings(group)
        
        # 验证配置继承和覆盖
        assert effective_settings["permissions"]["rate_limit"]["commands_per_minute"] == 3  # 群组覆盖
        assert effective_settings["permissions"]["rate_limit"]["queries_per_hour"] == 50  # 商户继承
        assert effective_settings["display_settings"]["decimal_places"] == 3  # 商户继承
        assert effective_settings["display_settings"]["show_details"] is True  # 群组新增
        assert effective_settings["permissions"]["allow_all_members"] is False  # 全局默认
    
    def test_validate_settings(self, db: Session):
        """测试配置验证"""
        config_service = TelegramConfigService(db)
        
        # 测试有效配置
        valid_settings = {
            "permissions": {
                "allow_all_members": True,
                "rate_limit": {
                    "commands_per_minute": 10,
                    "queries_per_hour": 100
                }
            },
            "display_settings": {
                "decimal_places": 2,
                "language": "zh-CN"
            }
        }
        
        is_valid, errors = config_service.validate_settings(valid_settings)
        assert is_valid is True
        assert len(errors) == 0
        
        # 测试无效配置
        invalid_settings = {
            "permissions": {
                "rate_limit": {
                    "commands_per_minute": 100,  # 超出范围
                    "queries_per_hour": 2000     # 超出范围
                }
            },
            "display_settings": {
                "decimal_places": 10,  # 超出范围
                "language": "invalid"  # 不支持的语言
            }
        }
        
        is_valid, errors = config_service.validate_settings(invalid_settings)
        assert is_valid is False
        assert len(errors) > 0
    
    def test_permission_template_operations(self, db: Session):
        """测试权限模板操作"""
        config_service = TelegramConfigService(db)
        
        # 创建权限模板
        template = config_service.create_permission_template(
            template_name="测试模板",
            template_code="test_template",
            description="测试用权限模板",
            settings={
                "permissions": {
                    "allow_all_members": False,
                    "rate_limit": {
                        "commands_per_minute": 5
                    }
                }
            },
            user_id=1,
            is_system=False
        )
        
        assert template.id is not None
        assert template.template_name == "测试模板"
        assert template.template_code == "test_template"
        assert template.is_system is False
        
        # 获取权限模板列表
        templates = config_service.get_permission_templates(include_system=True, include_custom=True)
        assert len(templates) >= 1
        assert any(t.template_code == "test_template" for t in templates)
    
    def test_apply_template_to_group(self, db: Session, test_merchant: Merchant):
        """测试应用模板到群组"""
        config_service = TelegramConfigService(db)
        
        # 创建权限模板
        template = TelegramPermissionTemplate(
            template_name="严格模式",
            template_code="strict_mode",
            description="严格权限控制",
            settings={
                "permissions": {
                    "allow_all_members": False,
                    "rate_limit": {
                        "commands_per_minute": 3,
                        "queries_per_hour": 30
                    }
                }
            },
            is_system=True
        )
        db.add(template)
        db.commit()
        
        # 创建群组
        group = TelegramGroup(
            chat_id=123456789,
            chat_title="测试群组",
            chat_type=ChatType.GROUP,
            merchant_id=test_merchant.id,
            bind_token="test_token",
            bind_status=BindStatus.ACTIVE
        )
        db.add(group)
        db.commit()
        
        # 应用模板
        updated_group = config_service.apply_permission_template(group, "strict_mode")
        
        # 验证模板应用效果
        assert updated_group.settings["permissions"]["rate_limit"]["commands_per_minute"] == 3
        assert updated_group.settings["permissions"]["rate_limit"]["queries_per_hour"] == 30
    
    def test_cache_functionality(self, db: Session):
        """测试缓存功能"""
        config_service = TelegramConfigService(db)
        
        # 测试缓存统计
        cache_stats = config_service.get_cache_stats()
        assert "cache_size" in cache_stats
        assert "cache_expire_seconds" in cache_stats
        assert "cached_keys" in cache_stats
        
        # 获取全局配置（会被缓存）
        settings1 = config_service.get_global_default_settings()
        settings2 = config_service.get_global_default_settings()
        
        # 验证缓存生效
        cache_stats_after = config_service.get_cache_stats()
        assert cache_stats_after["cache_size"] >= cache_stats["cache_size"]
        
        # 清除缓存
        config_service.clear_all_cache()
        cache_stats_cleared = config_service.get_cache_stats()
        assert cache_stats_cleared["cache_size"] == 0
