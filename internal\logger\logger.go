package logger

import (
	"fmt"
	"os"
	"path/filepath"
	"time"

	"walmart-bind-card-processor/internal/config"

	"github.com/sirupsen/logrus"
)

// Logger 全局日志实例
var Logger *logrus.Logger

// New 创建新的日志实例，与Python系统日志格式完全兼容
func New(cfg config.LoggingConfig) *logrus.Logger {
	logger := logrus.New()
	
	// 设置日志级别
	level, err := logrus.ParseLevel(cfg.Level)
	if err != nil {
		level = logrus.InfoLevel
	}
	logger.SetLevel(level)
	
	// 设置日志格式
	switch cfg.Format {
	case "json":
		logger.SetFormatter(&logrus.JSONFormatter{
			TimestampFormat: "2006-01-02 15:04:05",
			FieldMap: logrus.FieldMap{
				logrus.FieldKeyTime:  "timestamp",
				logrus.FieldKeyLevel: "level",
				logrus.FieldKeyMsg:   "message",
				logrus.FieldKeyFunc:  "function",
				logrus.FieldKeyFile:  "file",
			},
		})
	default:
		logger.SetFormatter(&CustomTextFormatter{
			TimestampFormat: "2006-01-02 15:04:05",
			FullTimestamp:   true,
		})
	}
	
	// 设置输出目标
	switch cfg.Output {
	case "file":
		// 创建日志目录
		logDir := "logs"
		if err := os.MkdirAll(logDir, 0755); err != nil {
			fmt.Printf("Failed to create log directory: %v\n", err)
			logger.SetOutput(os.Stdout)
		} else {
			// 创建日志文件
			logFile := filepath.Join(logDir, fmt.Sprintf("processor-%s.log", time.Now().Format("2006-01-02")))
			file, err := os.OpenFile(logFile, os.O_CREATE|os.O_WRONLY|os.O_APPEND, 0666)
			if err != nil {
				fmt.Printf("Failed to open log file: %v\n", err)
				logger.SetOutput(os.Stdout)
			} else {
				logger.SetOutput(file)
			}
		}
	default:
		logger.SetOutput(os.Stdout)
	}
	
	// 设置调用信息报告
	logger.SetReportCaller(true)
	
	// 设置全局实例
	Logger = logger
	
	return logger
}

// CustomTextFormatter 自定义文本格式化器，与Python系统日志格式兼容
type CustomTextFormatter struct {
	TimestampFormat string
	FullTimestamp   bool
}

// Format 格式化日志条目
func (f *CustomTextFormatter) Format(entry *logrus.Entry) ([]byte, error) {
	timestamp := entry.Time.Format(f.TimestampFormat)
	
	// 获取文件和行号信息
	var fileInfo string
	if entry.HasCaller() {
		fileInfo = fmt.Sprintf(" [%s:%d]", filepath.Base(entry.Caller.File), entry.Caller.Line)
	}
	
	// 构建日志消息
	var logMessage string
	if len(entry.Data) > 0 {
		// 包含额外字段
		logMessage = fmt.Sprintf("[%s] %s%s %s %v\n",
			timestamp,
			entry.Level.String(),
			fileInfo,
			entry.Message,
			entry.Data,
		)
	} else {
		// 简单消息
		logMessage = fmt.Sprintf("[%s] %s%s %s\n",
			timestamp,
			entry.Level.String(),
			fileInfo,
			entry.Message,
		)
	}
	
	return []byte(logMessage), nil
}

// GetLogger 获取全局日志实例
func GetLogger() *logrus.Logger {
	if Logger == nil {
		// 如果没有初始化，创建默认日志实例
		Logger = logrus.New()
		Logger.SetLevel(logrus.InfoLevel)
		Logger.SetFormatter(&logrus.JSONFormatter{
			TimestampFormat: "2006-01-02 15:04:05",
		})
	}
	return Logger
}

// 便捷方法，与Python系统日志调用方式兼容
func Debug(args ...interface{}) {
	GetLogger().Debug(args...)
}

func Debugf(format string, args ...interface{}) {
	GetLogger().Debugf(format, args...)
}

func Info(args ...interface{}) {
	GetLogger().Info(args...)
}

func Infof(format string, args ...interface{}) {
	GetLogger().Infof(format, args...)
}

func Warn(args ...interface{}) {
	GetLogger().Warn(args...)
}

func Warnf(format string, args ...interface{}) {
	GetLogger().Warnf(format, args...)
}

func Error(args ...interface{}) {
	GetLogger().Error(args...)
}

func Errorf(format string, args ...interface{}) {
	GetLogger().Errorf(format, args...)
}

func Fatal(args ...interface{}) {
	GetLogger().Fatal(args...)
}

func Fatalf(format string, args ...interface{}) {
	GetLogger().Fatalf(format, args...)
}

func Panic(args ...interface{}) {
	GetLogger().Panic(args...)
}

func Panicf(format string, args ...interface{}) {
	GetLogger().Panicf(format, args...)
}
