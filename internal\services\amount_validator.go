package services

import (
	"context"
	"fmt"
	"time"

	"walmart-bind-card-processor/internal/config"
	"walmart-bind-card-processor/internal/model"

	"github.com/go-redis/redis/v8"
	"github.com/sirupsen/logrus"
	"gorm.io/gorm"
)

// AmountValidatorService 金额验证服务
type AmountValidatorService struct {
	db     *gorm.DB
	redis  *redis.Client
	logger *logrus.Logger
	config *config.Config
}

// NewAmountValidatorService 创建金额验证服务
func NewAmountValidatorService(
	db *gorm.DB,
	redis *redis.Client,
	logger *logrus.Logger,
	config *config.Config,
) *AmountValidatorService {
	return &AmountValidatorService{
		db:     db,
		redis:  redis,
		logger: logger,
		config: config,
	}
}

// ValidateAmount 验证金额是否一致
func (s *AmountValidatorService) ValidateAmount(ctx context.Context, recordID string, requestAmount, actualAmount int) error {
	s.logger.Infof("验证金额: record_id=%s, 请求金额=%d, 真实金额=%d", recordID, requestAmount, actualAmount)

	if actualAmount != requestAmount {
		s.logger.Warnf("绑卡金额与真实金额不符: record_id=%s, 请求金额=%d, 真实金额=%d", 
			recordID, requestAmount, actualAmount)
		
		// 更新卡记录状态为失败
		if err := s.updateCardBalanceWithValidationFailure(ctx, recordID, requestAmount, actualAmount); err != nil {
			s.logger.Errorf("更新金额验证失败状态失败: %v", err)
		}
		
		return fmt.Errorf("绑卡金额与真实金额不符: 请求金额=%d, 真实金额=%d", requestAmount, actualAmount)
	}

	// 金额一致，更新成功状态
	if err := s.updateCardBalanceSuccess(ctx, recordID, actualAmount); err != nil {
		s.logger.Errorf("更新金额验证成功状态失败: %v", err)
		return err
	}

	s.logger.Infof("金额验证通过: record_id=%s, 金额=%d", recordID, actualAmount)
	return nil
}

// updateCardBalanceWithValidationFailure 更新金额验证失败的卡记录
func (s *AmountValidatorService) updateCardBalanceWithValidationFailure(ctx context.Context, recordID string, requestAmount, actualAmount int) error {
	updateData := map[string]interface{}{
		"status":           "failed",
		"actual_amount":    actualAmount,
		"error_message":    fmt.Sprintf("绑卡金额与真实金额不符: 请求金额=%d, 真实金额=%d", requestAmount, actualAmount),
		"updated_at":       time.Now(),
	}

	if err := s.db.Model(&model.CardRecord{}).Where("id = ?", recordID).Updates(updateData).Error; err != nil {
		return fmt.Errorf("更新金额验证失败状态失败: %w", err)
	}

	s.logger.Infof("已更新金额验证失败状态: record_id=%s, 请求金额=%d, 真实金额=%d", 
		recordID, requestAmount, actualAmount)

	return nil
}

// updateCardBalanceSuccess 更新金额验证成功的卡记录
func (s *AmountValidatorService) updateCardBalanceSuccess(ctx context.Context, recordID string, actualAmount int) error {
	updateData := map[string]interface{}{
		"status":           "success",
		"actual_amount":    actualAmount,
		"updated_at":       time.Now(),
	}

	if err := s.db.Model(&model.CardRecord{}).Where("id = ?", recordID).Updates(updateData).Error; err != nil {
		return fmt.Errorf("更新金额验证成功状态失败: %w", err)
	}

	s.logger.Infof("已更新金额验证成功状态: record_id=%s, 金额=%d", recordID, actualAmount)
	return nil
}

// CreateAmountMismatchCallback 创建金额不符的回调消息
func (s *AmountValidatorService) CreateAmountMismatchCallback(recordID string, merchantID int, merchantOrderID string, requestAmount, actualAmount int) *model.CallbackMessage {
	return &model.CallbackMessage{
		RecordID:        recordID,
		MerchantID:      merchantID,
		MerchantOrderID: merchantOrderID,
		Status:          "failed",
		Amount:          requestAmount,
		ActualAmount:    &actualAmount,
		ErrorMessage:    fmt.Sprintf("绑卡金额与真实金额不符: 请求金额=%d, 真实金额=%d", requestAmount, actualAmount),
		ErrorCode:       "AMOUNT_MISMATCH",
		CreatedAt:       time.Now(),
		CallbackData: map[string]interface{}{
			"validation_failed": true,
			"requested_amount":  requestAmount,
			"actual_amount":     actualAmount,
			"mismatch_reason":   "绑卡金额与真实金额不符",
		},
	}
}

// CreateSuccessCallback 创建成功的回调消息
func (s *AmountValidatorService) CreateSuccessCallback(recordID string, merchantID int, merchantOrderID string, amount int) *model.CallbackMessage {
	return &model.CallbackMessage{
		RecordID:        recordID,
		MerchantID:      merchantID,
		MerchantOrderID: merchantOrderID,
		Status:          "success",
		Amount:          amount,
		ActualAmount:    &amount,
		CreatedAt:       time.Now(),
		CallbackData: map[string]interface{}{
			"validation_passed": true,
			"amount":           amount,
		},
	}
}

// GetValidationStats 获取金额验证统计信息
func (s *AmountValidatorService) GetValidationStats(ctx context.Context) (map[string]interface{}, error) {
	stats := make(map[string]interface{})

	// 查询总验证次数（有实际金额的记录）
	var totalCount int64
	if err := s.db.Model(&model.CardRecord{}).
		Where("actual_amount IS NOT NULL").
		Count(&totalCount).Error; err != nil {
		return nil, fmt.Errorf("查询总验证次数失败: %w", err)
	}
	stats["total_validations"] = totalCount

	// 查询成功次数
	var successCount int64
	if err := s.db.Model(&model.CardRecord{}).
		Where("status = ? AND actual_amount IS NOT NULL", "success").
		Count(&successCount).Error; err != nil {
		return nil, fmt.Errorf("查询成功次数失败: %w", err)
	}
	stats["success_validations"] = successCount

	// 查询金额不符次数
	var mismatchCount int64
	if err := s.db.Model(&model.CardRecord{}).
		Where("error_code = ?", "AMOUNT_MISMATCH").
		Count(&mismatchCount).Error; err != nil {
		return nil, fmt.Errorf("查询金额不符次数失败: %w", err)
	}
	stats["amount_mismatch_count"] = mismatchCount

	// 计算成功率
	if totalCount > 0 {
		successRate := float64(successCount) / float64(totalCount) * 100
		stats["success_rate"] = fmt.Sprintf("%.2f%%", successRate)
		
		mismatchRate := float64(mismatchCount) / float64(totalCount) * 100
		stats["mismatch_rate"] = fmt.Sprintf("%.2f%%", mismatchRate)
	} else {
		stats["success_rate"] = "0.00%"
		stats["mismatch_rate"] = "0.00%"
	}

	return stats, nil
}

// IsAmountMismatchError 检查是否为金额不符错误
func (s *AmountValidatorService) IsAmountMismatchError(errorMsg string) bool {
	return errorMsg != "" && (
		fmt.Sprintf("%s", errorMsg) == "绑卡金额与真实金额不符" ||
		len(errorMsg) > 10 && errorMsg[:10] == "绑卡金额与真实金额不符")
}

// MaskCardNumber 遮蔽卡号
func MaskCardNumber(cardNumber string) string {
	if len(cardNumber) <= 8 {
		return cardNumber
	}
	return cardNumber[:4] + "****" + cardNumber[len(cardNumber)-4:]
}
