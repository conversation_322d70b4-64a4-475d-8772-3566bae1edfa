"""
沃尔玛绑卡系统测试套件

这是一个全面、规范、结构化的测试框架，用于测试沃尔玛绑卡系统的所有功能模块。

测试覆盖范围：
- 认证模块 (auth)
- 用户管理 (users) 
- 商户管理 (merchants)
- 部门管理 (departments)
- 角色权限 (roles/permissions)
- 菜单管理 (menus)
- 卡记录管理 (cards)
- 仪表盘 (dashboard)
- 沃尔玛配置 (walmart-server)

测试类型：
- CRUD操作测试
- 数据隔离测试
- 权限验证测试
- API安全测试
- 错误处理测试

使用方法：
    cd test
    python run_all_tests.py
"""

__version__ = "1.0.0"
__author__ = "Walmart Bind Card System Team"
