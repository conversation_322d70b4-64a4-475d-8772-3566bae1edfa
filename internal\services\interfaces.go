package services

import (
	"context"
	"time"

	"walmart-bind-card-processor/internal/queue"
)

// BindCardServiceInterface 绑卡服务接口
type BindCardServiceInterface interface {
	ProcessBindCard(ctx context.Context, msg *queue.BindCardMessage) error
	UpdateCardStatus(ctx context.Context, recordID string, status string, result map[string]interface{}) error
	GetCardRecord(ctx context.Context, recordID string) (map[string]interface{}, error)
	ValidateCardData(ctx context.Context, msg *queue.BindCardMessage) error
}

// CallbackServiceInterface 回调服务接口
type CallbackServiceInterface interface {
	SendCallback(ctx context.Context, msg *queue.CallbackMessage) error
	ScheduleRetry(ctx context.Context, msg *queue.CallbackMessage, delay time.Duration) error
	UpdateCallbackStatus(ctx context.Context, recordID string, status string, result map[string]interface{}) error
	GetCallbackURL(ctx context.Context, merchantID uint) (string, error)
}

// WalmartAPIServiceInterface 沃尔玛API服务接口
type WalmartAPIServiceInterface interface {
	BindCard(ctx context.Context, cardNumber, cardPassword string, amount int, ckSign string, debug bool) (*WalmartBindResponse, error)
	CheckCardStatus(ctx context.Context, cardNumber string, debug bool) (*WalmartStatusResponse, error)
	GetBalance(ctx context.Context, cardNumber string, debug bool) (*WalmartBalanceResponse, error)
	IsHealthy(ctx context.Context) bool
}

// CKManagerServiceInterface CK管理服务接口
type CKManagerServiceInterface interface {
	GetAvailableCK(ctx context.Context, merchantID uint, departmentID *uint) (*CKInfo, error)
	LockCK(ctx context.Context, ckID uint, duration time.Duration) error
	UnlockCK(ctx context.Context, ckID uint) error
	UpdateCKUsage(ctx context.Context, ckID uint, success bool) error
	GetCKStats(ctx context.Context, ckID uint) (*CKStats, error)
	ValidateCK(ctx context.Context, ckID uint) error
}

// MetricsServiceInterface 指标服务接口
type MetricsServiceInterface interface {
	IncrementCounter(ctx context.Context, metric string, tags map[string]string) error
	RecordDuration(ctx context.Context, metric string, duration time.Duration, tags map[string]string) error
	SetGauge(ctx context.Context, metric string, value float64, tags map[string]string) error
	GetMetrics(ctx context.Context, metric string, timeRange time.Duration) ([]MetricPoint, error)
}

// WalmartBindResponse 沃尔玛绑卡响应
type WalmartBindResponse struct {
	Success      bool                   `json:"success"`
	Message      string                 `json:"message"`
	Data         map[string]interface{} `json:"data"`
	Balance      string                 `json:"balance,omitempty"`
	CardBalance  string                 `json:"cardBalance,omitempty"`
	BalanceCnt   string                 `json:"balanceCnt,omitempty"`
	ActualAmount int                    `json:"actualAmount,omitempty"`
	TraceID      string                 `json:"traceId,omitempty"`
}

// WalmartStatusResponse 沃尔玛状态查询响应
type WalmartStatusResponse struct {
	Success bool                   `json:"success"`
	Status  string                 `json:"status"`
	Data    map[string]interface{} `json:"data"`
}

// WalmartBalanceResponse 沃尔玛余额查询响应
type WalmartBalanceResponse struct {
	Success     bool   `json:"success"`
	Balance     string `json:"balance"`
	CardBalance string `json:"cardBalance"`
	BalanceCnt  string `json:"balanceCnt"`
}

// CKInfo CK信息
type CKInfo struct {
	ID           uint   `json:"id"`
	Sign         string `json:"sign"`
	TotalLimit   int    `json:"total_limit"`
	BindCount    int    `json:"bind_count"`
	LastBindTime string `json:"last_bind_time"`
	MerchantID   uint   `json:"merchant_id"`
	DepartmentID uint   `json:"department_id"`
}

// CKStats CK统计信息
type CKStats struct {
	ID            uint    `json:"id"`
	TotalBinds    int     `json:"total_binds"`
	SuccessBinds  int     `json:"success_binds"`
	FailedBinds   int     `json:"failed_binds"`
	SuccessRate   float64 `json:"success_rate"`
	LastBindTime  string  `json:"last_bind_time"`
	AvgProcessTime float64 `json:"avg_process_time"`
}

// MetricPoint 指标数据点
type MetricPoint struct {
	Timestamp time.Time              `json:"timestamp"`
	Value     float64                `json:"value"`
	Tags      map[string]string      `json:"tags"`
}

// ProcessResult 处理结果
type ProcessResult struct {
	Success      bool                   `json:"success"`
	Status       string                 `json:"status"`
	Message      string                 `json:"message"`
	Data         map[string]interface{} `json:"data"`
	ProcessTime  float64                `json:"process_time"`
	RetryCount   int                    `json:"retry_count"`
	ErrorCode    string                 `json:"error_code,omitempty"`
}

// CallbackResult 回调结果
type CallbackResult struct {
	Success      bool                   `json:"success"`
	StatusCode   int                    `json:"status_code"`
	Response     string                 `json:"response"`
	Duration     time.Duration          `json:"duration"`
	RetryCount   int                    `json:"retry_count"`
	NextRetryAt  *time.Time             `json:"next_retry_at,omitempty"`
}
