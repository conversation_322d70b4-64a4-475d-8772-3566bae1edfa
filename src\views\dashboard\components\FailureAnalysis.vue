<template>
  <el-card class="failure-analysis-card" shadow="hover">
    <template #header>
      <div class="card-header">
        <span>异常/失败分析</span>
        <el-radio-group v-model="timeRange" size="small" @change="fetchData">
          <el-radio-button value="today">今日</el-radio-button>
          <el-radio-button value="week">本周</el-radio-button>
          <el-radio-button value="month">本月</el-radio-button>
        </el-radio-group>
      </div>
    </template>

    <div v-loading="loading" class="failure-content">
      <!-- 失败总览 -->
      <div class="failure-summary">
        <el-row :gutter="16">
          <el-col :xs="24" :sm="8">
            <div class="summary-item failed">
              <div class="summary-value">{{ data.total_failed_count }}</div>
              <div class="summary-label">失败总数</div>
            </div>
          </el-col>
          <el-col :xs="24" :sm="8">
            <div class="summary-item amount">
              <div class="summary-value">{{ formatAmount(data.total_failed_amount_yuan) }}</div>
              <div class="summary-label">失败金额</div>
            </div>
          </el-col>
          <el-col :xs="24" :sm="8">
            <div class="summary-item rate">
              <div class="summary-value">{{ calculateFailureRate() }}%</div>
              <div class="summary-label">失败率</div>
            </div>
          </el-col>
        </el-row>
      </div>

      <!-- 失败状态分布 -->
      <div class="failure-breakdown" v-if="data.failure_breakdown && data.failure_breakdown.length > 0">
        <h4>失败状态分布</h4>
        <el-table :data="data.failure_breakdown" size="small" stripe>
          <el-table-column label="状态" width="100">
            <template #default="scope">
              <el-tag :type="getStatusType(scope.row.status)" size="small">
                {{ getStatusLabel(scope.row.status) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="count" label="数量" width="80" align="right" />
          <el-table-column label="金额" align="right">
            <template #default="scope">
              {{ formatAmount(scope.row.amount_yuan) }}
            </template>
          </el-table-column>
          <el-table-column label="占比" width="120">
            <template #default="scope">
              <el-progress :percentage="getPercentage(scope.row.count)" color="#F56C6C"
                :format="() => `${getPercentage(scope.row.count)}%`" :stroke-width="6" />
            </template>
          </el-table-column>
        </el-table>
      </div>

      <!-- 错误原因排行 -->
      <div class="error-reasons" v-if="data.top_error_reasons && data.top_error_reasons.length > 0">
        <h4>主要错误原因（前10名）</h4>
        <div class="error-list">
          <div v-for="(error, index) in data.top_error_reasons" :key="index" class="error-item">
            <div class="error-rank">{{ index + 1 }}</div>
            <div class="error-message">{{ error.error_message || '未知错误' }}</div>
            <div class="error-count">{{ error.count }}次</div>
          </div>
        </div>
      </div>

      <!-- 无数据提示 -->
      <div v-else-if="!data.failure_breakdown || data.failure_breakdown.length === 0" class="no-data">
        <el-empty description="暂无失败数据" />
      </div>
    </div>
  </el-card>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { dashboardApi } from '@/api/modules/dashboard'
import { useUserStore } from '@/store/modules/user'
import { usePermissionStore } from '@/store/modules/permission'
import { useMerchantStore } from '@/store/modules/merchant'

const userStore = useUserStore()
const permissionStore = usePermissionStore()
const merchantStore = useMerchantStore()

const loading = ref(false)
const timeRange = ref('today')

const data = reactive({
  total_failed_count: 0,
  total_failed_amount_yuan: 0,
  failure_breakdown: [],
  top_error_reasons: []
})

const formatAmount = (amount) => {
  return amount ? amount.toFixed(2) : '0.00'
}

const getStatusType = (status) => {
  const statusMap = {
    'failed': 'danger',
    'timeout': 'warning',
    'cancelled': 'info'
  }
  return statusMap[status] || 'danger'
}

const getStatusLabel = (status) => {
  const labelMap = {
    'failed': '失败',
    'timeout': '超时',
    'cancelled': '取消'
  }
  return labelMap[status] || status
}

const getPercentage = (count) => {
  if (data.total_failed_count === 0) return 0
  return Math.round((count / data.total_failed_count) * 100)
}

const calculateFailureRate = () => {
  // 这里需要从其他地方获取总请求数，暂时返回0
  return 0
}

const fetchData = async () => {
  loading.value = true
  try {
    const params = {
      time_range: timeRange.value
    }

    // 根据用户角色确定查询参数
    if (permissionStore.isSuperAdmin) {
      if (merchantStore.currentMerchantId) {
        params.merchant_id = merchantStore.currentMerchantId
      }
    } else {
      params.merchant_id = userStore.merchantId
    }

    const response = await dashboardApi.getFailureStatistics(params)
    Object.assign(data, response.data)
  } catch (error) {
    console.error('获取失败统计失败:', error)
  } finally {
    loading.value = false
  }
}

onMounted(() => {
  fetchData()
})

// 暴露刷新方法给父组件
defineExpose({
  refresh: fetchData
})
</script>

<style scoped>
.failure-analysis-card {
  margin-bottom: 16px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 16px;
  font-weight: 600;
}

.failure-content {
  min-height: 300px;
}

.failure-summary {
  margin-bottom: 20px;
}

.summary-item {
  text-align: center;
  padding: 16px 8px;
  border-radius: 8px;
  margin-bottom: 8px;
}

.summary-item.failed {
  background: #fef2f2;
  border-left: 4px solid #F56C6C;
}

.summary-item.amount {
  background: #fff7ed;
  border-left: 4px solid #E6A23C;
}

.summary-item.rate {
  background: #f3f4f6;
  border-left: 4px solid #6B7280;
}

.summary-value {
  font-size: 20px;
  font-weight: bold;
  color: #303133;
  margin-bottom: 4px;
}

.summary-label {
  font-size: 12px;
  color: #606266;
}

.failure-breakdown h4,
.error-reasons h4 {
  margin-bottom: 16px;
  color: #303133;
  font-size: 14px;
}

.error-list {
  max-height: 300px;
  overflow-y: auto;
}

.error-item {
  display: flex;
  align-items: center;
  padding: 8px 12px;
  background: #fafafa;
  border-radius: 4px;
  margin-bottom: 8px;
  font-size: 13px;
}

.error-rank {
  width: 24px;
  height: 24px;
  background: #F56C6C;
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: bold;
  margin-right: 12px;
}

.error-message {
  flex: 1;
  color: #303133;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.error-count {
  color: #F56C6C;
  font-weight: 500;
  margin-left: 12px;
}

.no-data {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
}

@media (max-width: 768px) {
  .summary-item {
    margin-bottom: 12px;
  }

  .summary-value {
    font-size: 18px;
  }

  .error-item {
    font-size: 12px;
  }
}
</style>
