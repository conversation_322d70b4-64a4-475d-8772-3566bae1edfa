import { defineStore } from "pinia";
import { telegramApi } from "@/api";

export const useTelegramStore = defineStore("telegram", {
  state: () => ({
    // 机器人状态
    botStatus: {
      running: false,
      initialized: false,
      last_update: null,
      error: null,
    },

    // 机器人信息
    botInfo: {
      id: null,
      username: null,
      first_name: null,
      can_join_groups: false,
      can_read_all_group_messages: false,
      supports_inline_queries: false,
    },

    // 全局配置
    globalConfig: {
      bot_token: "",
      webhook_url: "",
      webhook_secret: "",
      rate_limit_global: 1000,
      rate_limit_group: 100,
      rate_limit_user: 50,
      bind_token_expire_hours: 24,
      verification_token_expire_minutes: 30,
      max_bind_attempts_per_day: 5,
      enable_audit_log: true,
      mask_sensitive_data: true,
      default_timezone: "Asia/Shanghai",
      default_language: "zh-CN",
    },

    // 群组列表
    groups: [],
    groupsTotal: 0,
    groupsLoading: false,

    // 用户列表
    telegramUsers: [],
    telegramUsersTotal: 0,
    telegramUsersLoading: false,

    // 统计数据
    statistics: {
      total_groups: 0,
      active_groups: 0,
      total_users: 0,
      active_users: 0,
      commands_today: 0,
      messages_today: 0,
      success_rate: 0,
    },

    // 日志数据
    logs: [],
    logsTotal: 0,
    logsLoading: false,

    // 加载状态
    loading: {
      botStatus: false,
      botInfo: false,
      globalConfig: false,
      statistics: false,
    },
  }),

  getters: {
    // 机器人是否在线
    isBotOnline: (state) =>
      state.botStatus.running && state.botStatus.initialized,

    // 活跃群组数量
    activeGroupsCount: (state) =>
      state.groups.filter((group) => group.bind_status === "active").length,

    // 今日命令统计
    todayCommandsCount: (state) => state.statistics.commands_today,

    // 成功率百分比
    successRatePercent: (state) =>
      Math.round(state.statistics.success_rate * 100),
  },

  actions: {
    // ==================== 机器人控制 ====================

    /**
     * 获取机器人状态
     */
    async fetchBotStatus() {
      this.loading.botStatus = true;
      try {
        const response = await telegramApi.getBotStatus();
        this.botStatus = response.data || response;
        return response;
      } catch (error) {
        console.error("获取机器人状态失败:", error);
        this.botStatus.error = error.message;
        throw error;
      } finally {
        this.loading.botStatus = false;
      }
    },

    /**
     * 启动机器人
     */
    async startBot() {
      try {
        const response = await telegramApi.startBot();
        await this.fetchBotStatus(); // 刷新状态
        return response;
      } catch (error) {
        console.error("启动机器人失败:", error);
        throw error;
      }
    },

    /**
     * 停止机器人
     */
    async stopBot() {
      try {
        const response = await telegramApi.stopBot();
        await this.fetchBotStatus(); // 刷新状态
        return response;
      } catch (error) {
        console.error("停止机器人失败:", error);
        throw error;
      }
    },

    /**
     * 重启机器人
     */
    async restartBot() {
      try {
        const response = await telegramApi.restartBot();
        await this.fetchBotStatus(); // 刷新状态
        return response;
      } catch (error) {
        console.error("重启机器人失败:", error);
        throw error;
      }
    },

    /**
     * 重新加载关键词配置
     */
    async reloadKeywords() {
      try {
        const response = await telegramApi.reloadKeywords();
        return response;
      } catch (error) {
        console.error("重新加载关键词失败:", error);
        throw error;
      }
    },

    /**
     * 获取机器人信息
     */
    async fetchBotInfo() {
      this.loading.botInfo = true;
      try {
        const response = await telegramApi.getBotInfo();
        this.botInfo = response.data || response;
        return response;
      } catch (error) {
        console.error("获取机器人信息失败:", error);
        throw error;
      } finally {
        this.loading.botInfo = false;
      }
    },

    // ==================== 配置管理 ====================

    /**
     * 获取全局配置
     */
    async fetchGlobalConfig() {
      this.loading.globalConfig = true;
      try {
        const response = await telegramApi.getGlobalConfig();
        this.globalConfig = {
          ...this.globalConfig,
          ...(response.data || response),
        };
        return response;
      } catch (error) {
        console.error("获取全局配置失败:", error);
        throw error;
      } finally {
        this.loading.globalConfig = false;
      }
    },

    /**
     * 更新全局配置
     */
    async updateGlobalConfig(config) {
      try {
        const response = await telegramApi.updateGlobalConfig(config);
        this.globalConfig = { ...this.globalConfig, ...config };
        return response;
      } catch (error) {
        console.error("更新全局配置失败:", error);
        throw error;
      }
    },

    /**
     * 验证配置
     */
    async validateConfig(config) {
      try {
        const response = await telegramApi.validateConfig(config);
        return response;
      } catch (error) {
        console.error("验证配置失败:", error);
        throw error;
      }
    },

    // ==================== 群组管理 ====================

    /**
     * 获取群组列表
     */
    async fetchGroups(params = {}) {
      this.groupsLoading = true;
      try {
        const response = await telegramApi.getGroupList(params);
        const data = response.data || response;
        this.groups = data.items || data.data || [];
        this.groupsTotal = data.total || 0;
        return response;
      } catch (error) {
        console.error("获取群组列表失败:", error);
        this.groups = [];
        this.groupsTotal = 0;
        throw error;
      } finally {
        this.groupsLoading = false;
      }
    },

    /**
     * 创建群组绑定令牌
     */
    async createGroupBindToken(data) {
      try {
        const response = await telegramApi.createGroupBindToken(data);
        return response;
      } catch (error) {
        console.error("创建群组绑定令牌失败:", error);
        throw error;
      }
    },

    /**
     * 解绑群组
     */
    async unbindGroup(groupId) {
      try {
        const response = await telegramApi.unbindGroup(groupId);
        // 刷新群组列表
        await this.fetchGroups();
        return response;
      } catch (error) {
        console.error("解绑群组失败:", error);
        throw error;
      }
    },

    // ==================== 用户管理 ====================

    /**
     * 获取Telegram用户列表
     */
    async fetchTelegramUsers(params = {}) {
      this.telegramUsersLoading = true;
      try {
        const response = await telegramApi.getTelegramUserList(params);
        const data = response.data || response;
        // 修复：根据实际API返回结构获取用户列表
        this.telegramUsers = data.users || data.items || data.data || [];
        this.telegramUsersTotal = data.total || 0;

        // 调试：检查获取到的用户数据
        console.log("=== Store获取用户数据调试 ===");

        console.log("用户列表:", this.telegramUsers);
        this.telegramUsers.forEach((user, index) => {
          console.log(`Store用户 ${index + 1}:`, {
            id: user.id,
            telegram_user_id: user.telegram_user_id,
            verification_status: user.verification_status,
            verification_token: user.verification_token,
            token_length: user.verification_token
              ? user.verification_token.length
              : 0,
          });
        });

        return response;
      } catch (error) {
        console.error("获取Telegram用户列表失败:", error);
        this.telegramUsers = [];
        this.telegramUsersTotal = 0;
        throw error;
      } finally {
        this.telegramUsersLoading = false;
      }
    },

    // ==================== 统计数据 ====================

    /**
     * 获取统计数据
     */
    async fetchStatistics(params = {}) {
      this.loading.statistics = true;
      try {
        const response = await telegramApi.getBotStatistics(params);
        this.statistics = {
          ...this.statistics,
          ...(response.data || response),
        };
        return response;
      } catch (error) {
        console.error("获取统计数据失败:", error);
        throw error;
      } finally {
        this.loading.statistics = false;
      }
    },

    // ==================== 日志管理 ====================

    /**
     * 获取日志列表
     */
    async fetchLogs(params = {}) {
      this.logsLoading = true;
      try {
        const response = await telegramApi.getBotLogs(params);
        const data = response.data || response;
        this.logs = data.items || data.data || [];
        this.logsTotal = data.total || 0;
        return response;
      } catch (error) {
        console.error("获取日志列表失败:", error);
        this.logs = [];
        this.logsTotal = 0;
        throw error;
      } finally {
        this.logsLoading = false;
      }
    },

    // ==================== 工具方法 ====================

    /**
     * 重置状态
     */
    resetState() {
      this.$reset();
    },

    /**
     * 清除缓存
     */
    async clearCache() {
      try {
        const response = await telegramApi.clearCache();
        return response;
      } catch (error) {
        console.error("清除缓存失败:", error);
        throw error;
      }
    },
  },
});
