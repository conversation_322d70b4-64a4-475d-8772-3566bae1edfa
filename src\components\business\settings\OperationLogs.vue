<template>
    <div class="operation-logs">
        <el-card class="box-card">
            <template #header>
                <div class="card-header">
                    <span>操作日志</span>
                    <div class="header-operations">
                        <el-button type="primary" @click="refreshLogs">刷新</el-button>
                        <el-button type="success" @click="exportLogs">导出日志</el-button>
                    </div>
                </div>
            </template>

            <!-- 租户上下文提示 -->
            <div v-if="merchantStore.isMerchantMode" class="merchant-context-tip">
                <el-alert type="info" :closable="false">
                    当前显示租户 "{{ merchantStore.currentMerchantName }}" 的操作日志
                    <el-button type="primary" link @click="viewAllLogs">查看所有日志</el-button>
                </el-alert>
            </div>

            <!-- 搜索区域 -->
            <el-form :inline="true" :model="searchForm" class="search-form">
                <el-form-item label="操作类型">
                    <el-select v-model="searchForm.operationType" placeholder="请选择操作类型" clearable>
                        <el-option label="登录" value="LOGIN" />
                        <el-option label="登出" value="LOGOUT" />
                        <el-option label="创建" value="CREATE" />
                        <el-option label="修改" value="UPDATE" />
                        <el-option label="删除" value="DELETE" />
                        <el-option label="导出" value="EXPORT" />
                    </el-select>
                </el-form-item>
                <el-form-item label="操作人">
                    <el-input v-model="searchForm.operator" placeholder="请输入操作人" clearable />
                </el-form-item>
                <el-form-item label="操作时间">
                    <el-date-picker v-model="searchForm.timeRange" type="daterange" range-separator="至"
                        start-placeholder="开始日期" end-placeholder="结束日期" value-format="YYYY-MM-DD" />
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="handleSearch">搜索</el-button>
                    <el-button @click="resetSearch">重置</el-button>
                </el-form-item>
            </el-form>

            <!-- 日志表格 -->
            <el-table :data="logsList" style="width: 100%" border v-loading="loading">
                <el-table-column prop="id" label="日志ID" width="80" />
                <el-table-column prop="operationType" label="操作类型" width="100">
                    <template #default="scope">
                        <el-tag :type="getOperationTypeTag(scope.row.operationType)">
                            {{ scope.row.operationType }}
                        </el-tag>
                    </template>
                </el-table-column>
                <el-table-column prop="operator" label="操作人" width="120" />
                <el-table-column prop="operationTime" label="操作时间" width="180" />
                <el-table-column prop="ip" label="IP地址" width="130" />
                <el-table-column prop="module" label="操作模块" width="120" />
                <el-table-column prop="merchantName" label="相关租户" width="120" v-if="!merchantStore.isMerchantMode" />
                <el-table-column prop="description" label="操作描述" min-width="250" />
                <el-table-column label="操作" width="100" fixed="right">
                    <template #default="scope">
                        <el-button type="primary" size="small" @click="viewLogDetail(scope.row)" link>
                            详情
                        </el-button>
                    </template>
                </el-table-column>
            </el-table>

            <!-- 分页 -->
            <div class="pagination-container">
                <el-pagination v-model:current-page="currentPage" v-model:page-size="pageSize"
                    :page-sizes="[10, 20, 50, 100]" layout="total, sizes, prev, pager, next, jumper" :total="totalCount"
                    @update:page-size="handleSizeChange" @update:current-page="handleCurrentChange" />
            </div>
        </el-card>

        <!-- 日志详情对话框 -->
        <el-dialog v-model="dialogVisible" title="操作日志详情" width="60%">
            <div v-if="currentLog">
                <el-descriptions :column="2" border>
                    <el-descriptions-item label="操作ID">{{ currentLog.id }}</el-descriptions-item>
                    <el-descriptions-item label="操作类型">
                        <el-tag :type="getOperationTypeTag(currentLog.operationType)">
                            {{ currentLog.operationType }}
                        </el-tag>
                    </el-descriptions-item>
                    <el-descriptions-item label="操作人">{{ currentLog.operator }}</el-descriptions-item>
                    <el-descriptions-item label="操作时间">{{ currentLog.operationTime }}</el-descriptions-item>
                    <el-descriptions-item label="IP地址">{{ currentLog.ip }}</el-descriptions-item>
                    <el-descriptions-item label="操作模块">{{ currentLog.module }}</el-descriptions-item>
                    <el-descriptions-item label="相关租户" v-if="currentLog.merchantName">{{ currentLog.merchantName
                    }}</el-descriptions-item>
                    <el-descriptions-item label="请求方法" :span="2">{{ currentLog.method }}</el-descriptions-item>
                    <el-descriptions-item label="请求参数" :span="2">
                        <pre>{{ currentLog.params }}</pre>
                    </el-descriptions-item>
                    <el-descriptions-item label="操作结果" :span="2">
                        <el-tag :type="currentLog.status ? 'success' : 'danger'">
                            {{ currentLog.status ? '成功' : '失败' }}
                        </el-tag>
                    </el-descriptions-item>
                    <el-descriptions-item label="操作描述" :span="2">{{ currentLog.description }}</el-descriptions-item>
                </el-descriptions>
            </div>
        </el-dialog>
    </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { useMerchantStore } from '../../store/merchant.js'
import { useMerchantFilter } from '../../composables/useMerchantFilter.js'

// 获取租户Store
const merchantStore = useMerchantStore()

// 搜索表单
const searchForm = reactive({
    operationType: '',
    operator: '',
    timeRange: []
})

// 计算查询参数，结合搜索表单和租户过滤
const queryParams = computed(() => {
    const params = { ...searchForm }

    // 添加租户过滤
    if (merchantStore.isMerchantMode) {
        params.merchantId = merchantStore.currentMerchantId
    }

    return params
})

// 使用租户过滤组合式函数
const { merchantQueryParams, fetchMerchantData } = useMerchantFilter({
    fetchData: fetchLogs,
    queryParams: computed(() => searchForm),
    merchantKey: 'merchantId'
})

// 表格数据
const logsList = ref([])
const loading = ref(false)
const currentPage = ref(1)
const pageSize = ref(10)
const totalCount = ref(0)

// 日志详情
const dialogVisible = ref(false)
const currentLog = ref(null)

// 监听租户变化
watch(() => merchantStore.currentMerchant, () => {
    if (logsList.value.length > 0) {
        currentPage.value = 1 // 切换租户时重置到第一页
        fetchLogs()
    }
}, { immediate: false })

// 组件挂载时获取日志
onMounted(() => {
    fetchMerchantData()
})

// 获取日志列表
function fetchLogs(params = {}) {
    loading.value = true

    // 合并查询参数
    const queryParams = { ...params }

    // 添加分页参数
    queryParams.page = currentPage.value
    queryParams.pageSize = pageSize.value

    // 处理时间范围
    if (queryParams.timeRange && queryParams.timeRange.length === 2) {
        queryParams.startDate = queryParams.timeRange[0]
        queryParams.endDate = queryParams.timeRange[1]
        delete queryParams.timeRange
    }
    // 模拟API请求
    return new Promise(resolve => {
        setTimeout(() => {
            // 生成模拟数据
            const mockLogs = []
            const operationTypes = ['LOGIN', 'LOGOUT', 'CREATE', 'UPDATE', 'DELETE', 'EXPORT']
            const modules = ['用户管理', '商家管理', '绑卡管理', 'API配置', '系统设置']
            const operators = ['admin', 'merchant_admin', 'operator1', 'system']
            const merchants = [
                { id: 1, name: '沃尔玛超市' },
                { id: 2, name: '沃尔玛商城' },
                { id: 3, name: '沃尔玛便利店' },
                { id: 4, name: '山姆会员店' }
            ]

            for (let i = 1; i <= 100; i++) {
                const typeIndex = Math.floor(Math.random() * operationTypes.length)
                const moduleIndex = Math.floor(Math.random() * modules.length)
                const operatorIndex = Math.floor(Math.random() * operators.length)
                const merchantIndex = Math.floor(Math.random() * merchants.length)
                const merchant = merchants[merchantIndex]

                // 创建日志记录
                const log = {
                    id: i,
                    operationType: operationTypes[typeIndex],
                    operator: operators[operatorIndex],
                    operationTime: new Date(Date.now() - Math.floor(Math.random() * 30) * 86400000).toISOString().split('T')[0] + ' ' + new Date().toTimeString().split(' ')[0],
                    ip: `192.168.1.${Math.floor(Math.random() * 255)}`,
                    module: modules[moduleIndex],
                    merchantId: merchant.id,
                    merchantName: merchant.name,
                    description: `${operators[operatorIndex]}在${modules[moduleIndex]}模块执行了${operationTypes[typeIndex]}操作`,
                    method: 'POST /api/v1/' + modules[moduleIndex].toLowerCase().replace(/\s/g, '-'),
                    params: JSON.stringify({ id: Math.floor(Math.random() * 1000), data: { status: Math.random() > 0.5 } }, null, 2),
                    status: Math.random() > 0.2 // 80%成功率
                }

                mockLogs.push(log)
            }

            // 应用租户过滤
            let filteredLogs = [...mockLogs]

            // 按租户ID过滤
            if (queryParams.merchantId) {
                filteredLogs = filteredLogs.filter(log => log.merchantId === queryParams.merchantId)
            }

            // 按操作类型过滤
            if (queryParams.operationType) {
                filteredLogs = filteredLogs.filter(log => log.operationType === queryParams.operationType)
            }

            // 按操作人过滤
            if (queryParams.operator) {
                filteredLogs = filteredLogs.filter(log => log.operator.includes(queryParams.operator))
            }

            // 按时间范围过滤
            if (queryParams.startDate && queryParams.endDate) {
                const startDate = new Date(queryParams.startDate)
                const endDate = new Date(queryParams.endDate)
                endDate.setHours(23, 59, 59, 999) // 设置为当天结束

                filteredLogs = filteredLogs.filter(log => {
                    const logDate = new Date(log.operationTime)
                    return logDate >= startDate && logDate <= endDate
                })
            }

            // 计算总数
            totalCount.value = filteredLogs.length

            // 应用分页
            const startIndex = (queryParams.page - 1) * queryParams.pageSize
            const endIndex = startIndex + queryParams.pageSize

            logsList.value = filteredLogs.slice(startIndex, endIndex)
            loading.value = false

            resolve({
                items: logsList.value,
                total: totalCount.value
            })
        }, 500)
    })
}

// 根据操作类型返回标签类型
const getOperationTypeTag = (type) => {
    const map = {
        LOGIN: 'info',
        LOGOUT: 'info',
        CREATE: 'success',
        UPDATE: 'warning',
        DELETE: 'danger',
        EXPORT: 'primary'
    }
    return map[type] || 'info'
}

// 查看日志详情
const viewLogDetail = (log) => {
    currentLog.value = log
    dialogVisible.value = true
}

// 搜索操作
const handleSearch = () => {
    currentPage.value = 1
    fetchMerchantData()
}

// 重置搜索
const resetSearch = () => {
    searchForm.operationType = ''
    searchForm.operator = ''
    searchForm.timeRange = []
    handleSearch()
}

// 刷新日志
const refreshLogs = () => {
    fetchMerchantData()
    ElMessage.success('日志已刷新')
}

// 导出日志
const exportLogs = () => {
    ElMessage.success('日志导出功能待实现')
}

// 查看所有租户的日志
const viewAllLogs = () => {
    merchantStore.switchMerchant(null)
}

// 分页操作
const handleSizeChange = (val) => {
    pageSize.value = val
    fetchMerchantData()
}

const handleCurrentChange = (val) => {
    currentPage.value = val
    fetchMerchantData()
}
</script>

<style scoped>
.operation-logs {
    width: 100%;
}

.card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.search-form {
    margin-bottom: 20px;
}

.pagination-container {
    margin-top: 20px;
    display: flex;
    justify-content: flex-end;
}

pre {
    background-color: #f5f7fa;
    padding: 10px;
    border-radius: 4px;
    max-height: 200px;
    overflow: auto;
    font-size: 12px;
    margin: 0;
}

.merchant-context-tip {
    margin-bottom: 15px;
}
</style>