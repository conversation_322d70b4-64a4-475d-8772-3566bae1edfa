"""
Telegram Bot 配置管理
"""

import os
from typing import Optional, Dict, Any, Union
from dataclasses import dataclass, field
from sqlalchemy.orm import Session
from pydantic import Field
from pydantic_settings import BaseSettings

from app.models.telegram_bot_config import TelegramBotConfig, ConfigType
from app.core.logging import get_logger
from app.core.config import yaml_config

logger = get_logger(__name__)


class TelegramBotSettings(BaseSettings):
    """基于Pydantic的配置设置"""

    # 从YAML配置获取默认值
    _telegram_config = yaml_config.get("telegram_bot", {})

    # 基础配置
    bot_token: str = Field(
        _telegram_config.get("bot_token", ""),
        env="TELEGRAM_BOT_TOKEN",
        description="机器人Token（优先从数据库读取）"
    )
    webhook_url: str = Field(
        _telegram_config.get("webhook_url", ""),
        env="TELEGRAM_WEBHOOK_URL",
        description="Webhook URL"
    )
    webhook_secret: str = Field(
        _telegram_config.get("webhook_secret", ""),
        env="TELEGRAM_WEBHOOK_SECRET",
        description="Webhook密钥"
    )

    # 网络配置
    proxy_url: str = Field(
        _telegram_config.get("network", {}).get("proxy_url", ""),
        env="TELEGRAM_PROXY_URL",
        description="代理URL"
    )
    proxy_username: str = Field(
        _telegram_config.get("network", {}).get("proxy_username", ""),
        env="TELEGRAM_PROXY_USERNAME",
        description="代理用户名"
    )
    proxy_password: str = Field(
        _telegram_config.get("network", {}).get("proxy_password", ""),
        env="TELEGRAM_PROXY_PASSWORD",
        description="代理密码"
    )
    connect_timeout: int = Field(
        _telegram_config.get("network", {}).get("connect_timeout", 30),
        env="TELEGRAM_CONNECT_TIMEOUT",
        description="连接超时"
    )
    read_timeout: int = Field(
        _telegram_config.get("network", {}).get("read_timeout", 30),
        env="TELEGRAM_READ_TIMEOUT",
        description="读取超时"
    )
    write_timeout: int = Field(
        _telegram_config.get("network", {}).get("write_timeout", 30),
        env="TELEGRAM_WRITE_TIMEOUT",
        description="写入超时"
    )
    pool_timeout: int = Field(
        _telegram_config.get("network", {}).get("pool_timeout", 10),
        env="TELEGRAM_POOL_TIMEOUT",
        description="连接池超时"
    )
    max_retries: int = Field(
        _telegram_config.get("network", {}).get("max_retries", 3),
        env="TELEGRAM_MAX_RETRIES",
        description="最大重试次数"
    )
    retry_delay: int = Field(
        _telegram_config.get("network", {}).get("retry_delay", 5),
        env="TELEGRAM_RETRY_DELAY",
        description="重试延迟"
    )
    max_connections: int = Field(
        _telegram_config.get("network", {}).get("max_connections", 100),
        env="TELEGRAM_MAX_CONNECTIONS",
        description="最大连接数"
    )
    max_keepalive_connections: int = Field(
        _telegram_config.get("network", {}).get("max_keepalive_connections", 20),
        env="TELEGRAM_MAX_KEEPALIVE_CONNECTIONS",
        description="最大保持连接数"
    )

    # 频率限制配置
    rate_limit_global: int = Field(
        _telegram_config.get("rate_limit", {}).get("global", 1000),
        env="TELEGRAM_RATE_LIMIT_GLOBAL",
        description="全局频率限制"
    )
    rate_limit_group: int = Field(
        _telegram_config.get("rate_limit", {}).get("group", 100),
        env="TELEGRAM_RATE_LIMIT_GROUP",
        description="群组频率限制"
    )
    rate_limit_user: int = Field(
        _telegram_config.get("rate_limit", {}).get("user", 50),
        env="TELEGRAM_RATE_LIMIT_USER",
        description="用户频率限制"
    )

    # 安全配置
    bind_token_expire_hours: int = Field(
        _telegram_config.get("security", {}).get("bind_token_expire_hours", 24),
        env="TELEGRAM_BIND_TOKEN_EXPIRE_HOURS",
        description="绑定令牌过期时间(小时)"
    )
    verification_token_expire_minutes: int = Field(
        _telegram_config.get("security", {}).get("verification_token_expire_minutes", 30),
        env="TELEGRAM_VERIFICATION_TOKEN_EXPIRE_MINUTES",
        description="验证令牌过期时间(分钟)"
    )
    max_bind_attempts_per_day: int = Field(
        _telegram_config.get("security", {}).get("max_bind_attempts_per_day", 5),
        env="TELEGRAM_MAX_BIND_ATTEMPTS_PER_DAY",
        description="每日最大绑定尝试次数"
    )

    # 功能开关
    enable_audit_log: bool = Field(
        _telegram_config.get("features", {}).get("enable_audit_log", True),
        env="TELEGRAM_ENABLE_AUDIT_LOG",
        description="启用审计日志"
    )
    mask_sensitive_data: bool = Field(
        _telegram_config.get("features", {}).get("mask_sensitive_data", True),
        env="TELEGRAM_MASK_SENSITIVE_DATA",
        description="遮蔽敏感数据"
    )

    # 默认设置
    default_timezone: str = Field(
        _telegram_config.get("defaults", {}).get("timezone", "Asia/Shanghai"),
        env="TELEGRAM_DEFAULT_TIMEZONE",
        description="默认时区"
    )
    default_language: str = Field(
        _telegram_config.get("defaults", {}).get("language", "zh-CN"),
        env="TELEGRAM_DEFAULT_LANGUAGE",
        description="默认语言"
    )

    # 高级配置
    enable_debug: bool = Field(
        _telegram_config.get("features", {}).get("enable_debug", False),
        env="TELEGRAM_ENABLE_DEBUG",
        description="启用调试模式"
    )
    max_message_length: int = Field(
        _telegram_config.get("advanced", {}).get("max_message_length", 4096),
        env="TELEGRAM_MAX_MESSAGE_LENGTH",
        description="最大消息长度"
    )
    command_timeout: int = Field(
        _telegram_config.get("advanced", {}).get("command_timeout", 30),
        env="TELEGRAM_COMMAND_TIMEOUT",
        description="命令超时时间(秒)"
    )

    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
        extra = "ignore"  # 忽略额外的字段


@dataclass
class BotConfig:
    """统一的机器人配置类"""

    # 基础配置
    bot_token: str = ""
    webhook_url: str = ""
    webhook_secret: str = ""

    # 网络配置
    proxy_url: str = ""
    proxy_username: str = ""
    proxy_password: str = ""
    connect_timeout: int = 30
    read_timeout: int = 30
    write_timeout: int = 30
    pool_timeout: int = 10
    max_retries: int = 3
    retry_delay: int = 5
    max_connections: int = 100
    max_keepalive_connections: int = 20

    # 频率限制配置
    rate_limit_global: int = 1000
    rate_limit_group: int = 100
    rate_limit_user: int = 50

    # 安全配置
    bind_token_expire_hours: int = 24
    verification_token_expire_minutes: int = 30
    max_bind_attempts_per_day: int = 5

    # 功能开关
    enable_audit_log: bool = True
    mask_sensitive_data: bool = True

    # 默认设置
    default_timezone: str = "Asia/Shanghai"
    default_language: str = "zh-CN"

    # 高级配置
    enable_debug: bool = False
    max_message_length: int = 4096
    command_timeout: int = 30

    # 配置源优先级：环境变量 > 数据库 > 默认值
    _config_sources: Dict[str, str] = field(default_factory=dict)
    
    @classmethod
    def from_settings(cls, settings: TelegramBotSettings) -> "BotConfig":
        """从Pydantic设置创建配置"""
        config = cls(
            bot_token=settings.bot_token,
            webhook_url=settings.webhook_url,
            webhook_secret=settings.webhook_secret,
            proxy_url=settings.proxy_url,
            proxy_username=settings.proxy_username,
            proxy_password=settings.proxy_password,
            connect_timeout=settings.connect_timeout,
            read_timeout=settings.read_timeout,
            write_timeout=settings.write_timeout,
            pool_timeout=settings.pool_timeout,
            max_retries=settings.max_retries,
            retry_delay=settings.retry_delay,
            max_connections=settings.max_connections,
            max_keepalive_connections=settings.max_keepalive_connections,
            rate_limit_global=settings.rate_limit_global,
            rate_limit_group=settings.rate_limit_group,
            rate_limit_user=settings.rate_limit_user,
            bind_token_expire_hours=settings.bind_token_expire_hours,
            verification_token_expire_minutes=settings.verification_token_expire_minutes,
            max_bind_attempts_per_day=settings.max_bind_attempts_per_day,
            enable_audit_log=settings.enable_audit_log,
            mask_sensitive_data=settings.mask_sensitive_data,
            default_timezone=settings.default_timezone,
            default_language=settings.default_language,
            enable_debug=settings.enable_debug,
            max_message_length=settings.max_message_length,
            command_timeout=settings.command_timeout
        )
        config._config_sources = {"source": "environment"}
        return config

    @classmethod
    def from_database(cls, db: Session) -> "BotConfig":
        """从数据库加载配置"""
        config = cls()
        config_sources = {}

        try:
            # 查询所有配置项
            db_configs = db.query(TelegramBotConfig).all()
            loaded_count = 0

            for db_config in db_configs:
                try:
                    key = db_config.config_key
                    value = db_config.config_value

                    # 根据配置键设置对应的属性
                    if hasattr(config, key):
                        # 使用配置项的解析方法进行类型转换
                        try:
                            parsed_value = db_config.get_parsed_value()
                            setattr(config, key, parsed_value)
                            config_sources[key] = "database"
                            loaded_count += 1
                        except Exception as parse_error:
                            logger.warning(f"解析配置项 {key} 失败: {parse_error}，跳过此配置")
                            continue
                    else:
                        logger.debug(f"未知的配置键: {key}，跳过")

                except Exception as item_error:
                    logger.warning(f"处理配置项失败: {item_error}，跳过此配置")
                    continue

            config._config_sources = config_sources
            logger.info(f"从数据库成功加载了 {loaded_count} 个配置项")

        except Exception as e:
            logger.warning(f"从数据库加载配置失败: {e}")

        return config

    @classmethod
    def from_env(cls) -> "BotConfig":
        """从环境变量加载配置（保持向后兼容）"""
        settings = TelegramBotSettings()
        return cls.from_settings(settings)

    @classmethod
    def merge_configs(cls, env_config: "BotConfig", db_config: "BotConfig") -> "BotConfig":
        """合并环境变量和数据库配置，bot_token 数据库优先，其他环境变量优先"""
        merged = cls()

        # 获取所有配置字段
        config_fields = [field for field in dir(merged) if not field.startswith('_') and not callable(getattr(merged, field))]

        for field_name in config_fields:
            env_value = getattr(env_config, field_name, None)
            db_value = getattr(db_config, field_name, None)
            default_value = getattr(merged, field_name)

            # 对于 bot_token，数据库配置优先级最高
            if field_name == "bot_token":
                if db_value and db_value != default_value:
                    setattr(merged, field_name, db_value)
                    merged._config_sources[field_name] = "database"
                elif env_value and env_value != default_value:
                    setattr(merged, field_name, env_value)
                    merged._config_sources[field_name] = "environment"
                else:
                    merged._config_sources[field_name] = "default"
            else:
                # 其他配置项：环境变量优先，然后是数据库，最后是默认值
                if env_value != default_value:
                    setattr(merged, field_name, env_value)
                    merged._config_sources[field_name] = "environment"
                elif db_value != default_value:
                    setattr(merged, field_name, db_value)
                    merged._config_sources[field_name] = "database"
                else:
                    merged._config_sources[field_name] = "default"

        return merged

    def get_config_source(self, field_name: str) -> str:
        """获取配置项的来源"""
        return self._config_sources.get(field_name, "default")

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        result = {}
        for field_name in dir(self):
            if not field_name.startswith('_') and not callable(getattr(self, field_name)):
                result[field_name] = getattr(self, field_name)
        return result

    def validate(self, strict: bool = True) -> bool:
        """验证配置是否有效

        Args:
            strict: 是否严格验证，False时允许部分配置缺失（开发环境）
        """
        is_valid = True

        if not self.bot_token:
            if strict:
                logger.error("Bot token 未配置")
                is_valid = False
            else:
                logger.warning("Bot token 未配置，Telegram Bot 功能将不可用")

        if not self.webhook_url:
            if strict:
                logger.error("Webhook URL 未配置")
                is_valid = False
            else:
                logger.warning("Webhook URL 未配置，将使用轮询模式")

        if self.rate_limit_global <= 0:
            logger.error("全局频率限制配置无效")
            is_valid = False

        return is_valid
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "bot_token": "***" if self.bot_token else "",  # 隐藏敏感信息
            "webhook_url": self.webhook_url,
            "webhook_secret": "***" if self.webhook_secret else "",  # 隐藏敏感信息
            "rate_limit_global": self.rate_limit_global,
            "rate_limit_group": self.rate_limit_group,
            "rate_limit_user": self.rate_limit_user,
            "bind_token_expire_hours": self.bind_token_expire_hours,
            "verification_token_expire_minutes": self.verification_token_expire_minutes,
            "max_bind_attempts_per_day": self.max_bind_attempts_per_day,
            "enable_audit_log": self.enable_audit_log,
            "mask_sensitive_data": self.mask_sensitive_data,
            "default_timezone": self.default_timezone,
            "default_language": self.default_language
        }


class ConfigManager:
    """配置管理器"""
    
    def __init__(self, db_session):
        self.db_session = db_session
        self._config: Optional[BotConfig] = None
        
    def get_config(self) -> BotConfig:
        """获取配置"""
        if self._config is None:
            self._config = BotConfig.from_database(self.db_session)
            
        return self._config
    
    def reload_config(self) -> BotConfig:
        """重新加载配置"""
        self._config = BotConfig.from_database(self.db_session)
        return self._config
    
    def update_config(self, key: str, value: Any) -> bool:
        """更新配置"""
        try:
            # 确定配置类型
            config_type = "string"
            if isinstance(value, bool):
                config_type = "boolean"
            elif isinstance(value, (int, float)):
                config_type = "number"
            elif isinstance(value, (dict, list)):
                config_type = "json"
            
            # 更新数据库配置
            TelegramBotConfig.set_config(
                self.db_session, 
                key, 
                value, 
                config_type
            )
            
            # 重新加载配置
            self.reload_config()
            
            logger.info(f"配置 {key} 更新成功")
            return True
            
        except Exception as e:
            logger.error(f"更新配置 {key} 失败: {e}")
            return False


# 全局配置实例
_config_manager: Optional[ConfigManager] = None


def get_config_manager(db_session) -> ConfigManager:
    """获取配置管理器实例"""
    global _config_manager
    if _config_manager is None:
        _config_manager = ConfigManager(db_session)
    return _config_manager


def get_bot_config(db_session) -> BotConfig:
    """获取机器人配置"""
    return get_config_manager(db_session).get_config()


def load_unified_config(db_session: Session = None) -> BotConfig:
    """
    统一配置加载函数

    优先级：环境变量 > 数据库 > 默认值
    """
    try:
        # 1. 加载环境变量配置
        env_config = BotConfig.from_env()
        logger.info("环境变量配置加载完成")

        # 2. 如果提供了数据库会话，尝试加载数据库配置
        if db_session:
            try:
                db_config = BotConfig.from_database(db_session)
                logger.info("数据库配置加载完成")

                # 3. 合并配置
                final_config = BotConfig.merge_configs(env_config, db_config)
                logger.info("配置合并完成")

                return final_config
            except Exception as e:
                logger.warning(f"数据库配置加载失败，使用环境变量配置: {e}")
                return env_config
        else:
            logger.info("未提供数据库会话，仅使用环境变量配置")
            return env_config

    except Exception as e:
        logger.error(f"配置加载失败，使用默认配置: {e}")
        return BotConfig()


def validate_config(config: BotConfig, strict: bool = True) -> bool:
    """验证配置完整性

    Args:
        config: 配置对象
        strict: 是否严格验证，False时允许部分配置缺失（开发环境）
    """
    return config.validate(strict=strict)


def get_config_summary(config: BotConfig) -> Dict[str, Any]:
    """获取配置摘要（用于日志和调试）"""
    summary = {
        "bot_token_configured": bool(config.bot_token),
        "webhook_url_configured": bool(config.webhook_url),
        "rate_limits": {
            "global": config.rate_limit_global,
            "group": config.rate_limit_group,
            "user": config.rate_limit_user
        },
        "security": {
            "bind_token_expire_hours": config.bind_token_expire_hours,
            "verification_token_expire_minutes": config.verification_token_expire_minutes,
            "max_bind_attempts_per_day": config.max_bind_attempts_per_day
        },
        "features": {
            "audit_log": config.enable_audit_log,
            "mask_sensitive_data": config.mask_sensitive_data,
            "debug": config.enable_debug
        },
        "config_sources": config._config_sources
    }

    return summary
