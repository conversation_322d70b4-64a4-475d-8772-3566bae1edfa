"""Add is_test_mode field to card_records table

Revision ID: add_test_mode_field
Revises: 
Create Date: 2024-01-01 00:00:00.000000

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'add_test_mode_field'
down_revision = None  # 请根据实际情况修改为上一个版本的revision ID
branch_labels = None
depends_on = None


def upgrade():
    """Add is_test_mode field to card_records table"""
    # 添加is_test_mode字段到card_records表
    op.add_column('card_records', sa.Column('is_test_mode', sa.<PERSON>(), nullable=False, default=False, comment='是否为测试模式，用于并发测试时标识测试数据'))
    
    # 为现有记录设置默认值
    op.execute("UPDATE card_records SET is_test_mode = FALSE WHERE is_test_mode IS NULL")


def downgrade():
    """Remove is_test_mode field from card_records table"""
    # 删除is_test_mode字段
    op.drop_column('card_records', 'is_test_mode')
