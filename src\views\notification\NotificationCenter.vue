<template>
    <div class="notification-center">
        <el-card class="notification-card" shadow="never">
            <template #header>
                <div class="card-header">
                    <span>通知中心</span>
                    <div class="header-actions">
                        <el-button @click="loadNotifications" :icon="Refresh" size="small">刷新</el-button>
                        <el-button type="success" @click="showCreateDialog" :icon="Plus" size="small"
                            v-if="canCreateNotification">创建通知</el-button>
                        <el-button type="primary" @click="markAllRead" :icon="Reading" size="small"
                            :disabled="unreadNotifications.length === 0">全部已读</el-button>
                        <el-button type="danger" @click="clearAll" :icon="Delete" size="small"
                            :disabled="allNotifications.length === 0">清空通知</el-button>
                    </div>
                </div>
            </template>

            <el-tabs v-model="activeTab" class="notification-tabs">
                <el-tab-pane name="all">
                    <template #label>
                        <span class="tab-label">
                            <el-icon>
                                <Bell />
                            </el-icon> 全部 ({{ allNotifications.length }})
                        </span>
                    </template>
                    <notification-list :notifications="allNotifications || []" :loading="loading"
                        @refresh="loadNotifications" @edit="handleEdit" />
                </el-tab-pane>
                <el-tab-pane name="unread">
                    <template #label>
                        <span class="tab-label">
                            <el-icon>
                                <Reading />
                            </el-icon> 未读 ({{ notificationStore.unreadCount || 0 }})
                        </span>
                    </template>
                    <notification-list :notifications="unreadNotifications || []" :loading="loading"
                        @refresh="loadNotifications" @edit="handleEdit" />
                </el-tab-pane>
                <!-- Keep system/business tabs if they are useful, otherwise remove -->
                <el-tab-pane label="系统通知" name="system">
                    <template #label>
                        <span class="tab-label">
                            系统通知
                        </span>
                    </template>
                    <notification-list :notifications="systemNotifications || []" :loading="loading"
                        @refresh="loadNotifications" @edit="handleEdit" />
                </el-tab-pane>
                <el-tab-pane label="业务通知" name="business">
                    <template #label>
                        <span class="tab-label">
                            业务通知
                        </span>
                    </template>
                    <notification-list :notifications="businessNotifications || []" :loading="loading"
                        @refresh="loadNotifications" @edit="handleEdit" />
                </el-tab-pane>
            </el-tabs>

        </el-card>

        <!-- 创建/编辑通知对话框 -->
        <notification-form-dialog
            v-model="showFormDialog"
            :notification="editingNotification"
            @success="handleFormSuccess"
        />
    </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
// import NotificationList from '@/components/notification/NotificationList.vue' // 注释掉导入
import NotificationList from '@/components/business/notification/NotificationList.vue'
import NotificationFormDialog from '@/components/business/notification/NotificationFormDialog.vue'
import { useNotificationStore } from '@/store/modules/notification'
import { useUserStore } from '@/store/modules/user'
import { Bell, Reading, Delete, Refresh, Plus } from '@element-plus/icons-vue'

const notificationStore = useNotificationStore()
const userStore = useUserStore()
const loading = ref(false)
const activeTab = ref('all')
const showFormDialog = ref(false)
const editingNotification = ref(null)

// 判断通知是否已读的辅助函数
const isNotificationRead = (notification) => {
    // 优先使用 read 字段
    if (notification.read !== undefined) {
        return notification.read
    }
    // 其次使用 status 字段
    if (notification.status !== undefined) {
        return notification.status === 'read'
    }
    // 默认为未读
    return false
}

// 获取不同类型的通知
const allNotifications = computed(() => notificationStore.notifications)
const unreadNotifications = computed(() => notificationStore.notifications.filter(n => !isNotificationRead(n)))
const systemNotifications = computed(() => notificationStore.notifications.filter(n => n.type === 'system'))
const businessNotifications = computed(() => notificationStore.notifications.filter(n => n.type === 'business'))

// 权限检查
const canCreateNotification = computed(() => {
    // 超级管理员或有创建通知权限的用户可以创建通知
    return userStore.isSuperAdmin || userStore.userInfo?.permissions?.includes('api:notifications:create')
})

// 加载通知数据
const loadNotifications = async () => {
    loading.value = true
    try {
        await notificationStore.fetchNotifications()
        await notificationStore.fetchUnreadCount()
    } catch (error) {
        console.error('加载通知失败:', error)
        ElMessage.error('加载通知失败')
    } finally {
        loading.value = false
    }
}

// 显示创建通知对话框
const showCreateDialog = () => {
    editingNotification.value = null
    showFormDialog.value = true
}

// 处理编辑通知
const handleEdit = (notification) => {
    editingNotification.value = notification
    showFormDialog.value = true
}

// 处理表单成功事件
const handleFormSuccess = () => {
    showFormDialog.value = false
    editingNotification.value = null
    loadNotifications()
}

// 组件挂载时加载数据
onMounted(() => {
    loadNotifications()
})

// 标记所有通知为已读
const markAllRead = async () => {
    try {
        await ElMessageBox.confirm('确定要将所有通知标记为已读吗？', '提示', { type: 'info' })
        loading.value = true
        await notificationStore.markAllAsRead()
        ElMessage.success('已将所有通知标记为已读')
        await loadNotifications()
    } catch (error) {
        if (error !== 'cancel') {
            console.error('标记已读失败:', error)
            ElMessage.error('操作失败')
        }
    } finally {
        loading.value = false
    }
}

// 清空所有通知
const clearAll = async () => {
    try {
        await ElMessageBox.confirm('确定要清空所有通知吗？此操作不可恢复！', '警告', { type: 'warning' })
        loading.value = true
        await notificationStore.resetState()
        ElMessage.success('已清空所有通知')
        await loadNotifications()
    } catch (error) {
        if (error !== 'cancel') {
            console.error('清空通知失败:', error)
            ElMessage.error('操作失败')
        }
    } finally {
        loading.value = false
    }
}
</script>

<style scoped>
.notification-center {
    padding: 20px;
}

.notification-card {
    min-height: calc(100vh - 120px);
}

.card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.header-actions {
    display: flex;
    gap: 8px;
}

.tab-label {
    display: inline-flex;
    align-items: center;
}

.tab-label .el-icon {
    margin-right: 4px;
}

:deep(.el-tabs__content) {
    padding: 15px 0 0 0;
}

.notification-tabs {
    /* No specific margin needed if card body has padding */
}
</style>