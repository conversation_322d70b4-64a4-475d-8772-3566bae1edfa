from sqlalchemy import Column, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>teger, BigInteger, JSON
from app.models.base import BaseModel, TimestampMixin


class NotificationConfig(BaseModel, TimestampMixin):
    """通知配置模型"""

    __tablename__ = "notification_configs"
    __table_args__ = ({"extend_existing": True},)  # 允许表重定义

    id = Column(BigInteger, primary_key=True, index=True)

    # 通知渠道配置
    wechat_webhook_url = Column(
        String(255), nullable=True, comment="企业微信 Webhook URL"
    )
    dingtalk_webhook_url = Column(
        String(255), nullable=True, comment="钉钉 Webhook URL"
    )
    telegram_bot_token = Column(
        String(255), nullable=True, comment="Telegram 机器人令牌"
    )
    telegram_chat_id = Column(
        String(255), nullable=True, comment="Telegram 目标聊天 ID"
    )

    # 邮件配置
    smtp_host = Column(String(255), nullable=True, comment="SMTP 服务器地址")
    smtp_port = Column(Integer, nullable=True, comment="SMTP 端口")
    smtp_user = Column(String(255), nullable=True, comment="SMTP 用户名")
    smtp_password = Column(String(255), nullable=True, comment="SMTP 密码")
    alert_email_from = Column(String(255), nullable=True, comment="告警发件人")
    alert_email_to = Column(String(255), nullable=True, comment="告警收件人")

    # 告警阈值配置
    alert_threshold_warning = Column(Integer, default=5, comment="警告阈值（分钟）")
    alert_threshold_error = Column(Integer, default=3, comment="错误阈值（分钟）")
    alert_threshold_critical = Column(
        Integer, default=1, comment="严重错误阈值（分钟）"
    )

    # 通知启用状态
    wechat_enabled = Column(Boolean, default=False, comment="是否启用企业微信通知")
    dingtalk_enabled = Column(Boolean, default=False, comment="是否启用钉钉通知")
    telegram_enabled = Column(Boolean, default=False, comment="是否启用 Telegram 通知")
    email_enabled = Column(Boolean, default=False, comment="是否启用邮件通知")

    def __repr__(self):
        return f"<NotificationConfig(id={self.id})>"
