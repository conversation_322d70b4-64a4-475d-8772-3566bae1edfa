#!/usr/bin/env python3
"""
测试增强的负载均衡算法
验证随机化改进和缓存优化的效果
"""

import asyncio
import sys
import os
import time
from collections import Counter
from typing import Dict, List

# 添加项目路径
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.insert(0, project_root)

from app.api.deps import get_db
from app.models.walmart_ck import WalmartCK
from app.services.redis_ck_wrapper import create_optimized_ck_service
from app.core.logging import get_logger
from app.core.redis import get_redis

logger = get_logger("enhanced_load_balance_test")


async def test_enhanced_load_balance(merchant_id=2, rounds=30):
    """测试增强的负载均衡算法"""
    print(f"🚀 测试增强的负载均衡算法")
    print(f"商户ID: {merchant_id}, 测试轮数: {rounds}")
    print("="*80)
    
    db = next(get_db())
    redis_client = None
    
    try:
        # 连接Redis
        redis_client = await get_redis()
        
        # 检查可用CK
        available_cks = db.query(WalmartCK).filter(
            WalmartCK.merchant_id == merchant_id,
            WalmartCK.active == True,
            WalmartCK.is_deleted == False
        ).all()
        
        print(f"📊 可用CK数量: {len(available_cks)}")
        for ck in available_cks:
            print(f"  CK {ck.id}: bind_count={ck.bind_count}, total_limit={ck.total_limit}")
        
        if len(available_cks) < 2:
            print("❌ 需要至少2个可用CK才能测试负载均衡")
            return
        
        # 清理可能的缓存和锁
        print(f"\n🧹 清理测试环境...")
        await cleanup_test_environment(redis_client, merchant_id)
        
        # 创建CK服务
        ck_service = create_optimized_ck_service(db)
        
        # 执行多轮测试
        print(f"\n🎯 开始 {rounds} 轮负载均衡测试:")
        print("-"*60)
        
        selected_cks = []
        selection_times = []
        
        for i in range(rounds):
            start_time = time.time()
            
            # 选择CK
            ck = await ck_service.get_available_ck(merchant_id)
            
            selection_time = time.time() - start_time
            selection_times.append(selection_time)
            
            if ck:
                selected_cks.append(ck.id)
                print(f"轮次 {i+1:2d}: 选择CK {ck.id} ({selection_time*1000:.1f}ms)")
                
                # 立即释放CK
                await ck_service.record_ck_usage(ck_id=ck.id, success=False)
            else:
                selected_cks.append(None)
                print(f"轮次 {i+1:2d}: ❌ 未找到可用CK")
            
            # 小延迟避免时间种子过于相似
            await asyncio.sleep(0.05)
        
        # 分析结果
        await analyze_test_results(selected_cks, selection_times, available_cks)
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        db.close()
        if redis_client:
            await redis_client.aclose()


async def cleanup_test_environment(redis_client, merchant_id):
    """清理测试环境"""
    try:
        # 清理锁
        lock_pattern = f"walmart:ck:lock:*"
        async for key in redis_client.scan_iter(match=lock_pattern):
            await redis_client.delete(key)
        
        # 清理验证缓存（可选，用于测试）
        validation_pattern = f"walmart:ck:validation:*"
        async for key in redis_client.scan_iter(match=validation_pattern):
            await redis_client.delete(key)
        
        # 重置随机计数器
        random_key = f"walmart:ck:random_counter:{merchant_id}"
        await redis_client.delete(random_key)
        
        print("✅ 测试环境清理完成")
        
    except Exception as e:
        print(f"⚠️ 清理测试环境失败: {e}")


async def analyze_test_results(selected_cks: List, selection_times: List[float], available_cks: List):
    """分析测试结果"""
    print("\n" + "="*80)
    print("📈 测试结果分析")
    print("="*80)
    
    # 基础统计
    valid_selections = [ck_id for ck_id in selected_cks if ck_id is not None]
    unique_count = len(set(valid_selections))
    distribution = dict(Counter(valid_selections))
    
    print(f"总测试轮数: {len(selected_cks)}")
    print(f"成功选择次数: {len(valid_selections)}")
    print(f"选择的不同CK数: {unique_count}")
    print(f"可用CK总数: {len(available_cks)}")
    
    # 分布分析
    print(f"\n📊 CK选择分布:")
    for ck_id, count in sorted(distribution.items()):
        percentage = (count / len(valid_selections)) * 100
        bar = "█" * int(percentage / 2)  # 简单的条形图
        print(f"  CK {ck_id}: {count:2d}次 ({percentage:5.1f}%) {bar}")
    
    # 均衡性分析
    if len(distribution) > 1:
        distribution_values = list(distribution.values())
        max_count = max(distribution_values)
        min_count = min(distribution_values)
        balance_score = (1 - (max_count - min_count) / max_count) * 100
        
        print(f"\n⚖️ 负载均衡分析:")
        print(f"  最大选择次数: {max_count}")
        print(f"  最小选择次数: {min_count}")
        print(f"  均衡分数: {balance_score:.1f}/100")
        
        if balance_score >= 80:
            print("  ✅ 负载均衡优秀")
        elif balance_score >= 60:
            print("  ✅ 负载均衡良好")
        elif balance_score >= 40:
            print("  ⚠️ 负载均衡一般")
        else:
            print("  ❌ 负载均衡较差")
    else:
        print(f"\n⚠️ 只选择了一个CK，无法评估负载均衡")
    
    # 性能分析
    if selection_times:
        avg_time = sum(selection_times) / len(selection_times)
        max_time = max(selection_times)
        min_time = min(selection_times)
        
        print(f"\n⏱️ 性能分析:")
        print(f"  平均选择时间: {avg_time*1000:.1f}ms")
        print(f"  最大选择时间: {max_time*1000:.1f}ms")
        print(f"  最小选择时间: {min_time*1000:.1f}ms")
        
        if avg_time < 0.1:
            print("  ✅ 选择性能优秀")
        elif avg_time < 0.2:
            print("  ✅ 选择性能良好")
        else:
            print("  ⚠️ 选择性能需要优化")
    
    # 随机性分析
    print(f"\n🎲 随机性分析:")
    if len(valid_selections) >= 10:
        # 检查连续选择同一CK的情况
        max_consecutive = 1
        current_consecutive = 1
        
        for i in range(1, len(valid_selections)):
            if valid_selections[i] == valid_selections[i-1]:
                current_consecutive += 1
                max_consecutive = max(max_consecutive, current_consecutive)
            else:
                current_consecutive = 1
        
        print(f"  最大连续选择同一CK: {max_consecutive}次")
        
        if max_consecutive <= 3:
            print("  ✅ 随机性良好")
        elif max_consecutive <= 5:
            print("  ⚠️ 随机性一般")
        else:
            print("  ❌ 随机性较差，可能存在偏向")
    
    # 改进建议
    print(f"\n💡 改进建议:")
    if unique_count == len(available_cks):
        print("  ✅ 所有可用CK都被选择，分布良好")
    else:
        unused_cks = len(available_cks) - unique_count
        print(f"  ⚠️ 有 {unused_cks} 个CK未被选择，可能需要调整算法")
    
    if len(distribution) > 1:
        std_dev = calculate_standard_deviation(list(distribution.values()))
        print(f"  选择次数标准差: {std_dev:.2f}")
        if std_dev < 2:
            print("  ✅ 分布均匀性良好")
        else:
            print("  ⚠️ 分布不够均匀，建议优化权重算法")


def calculate_standard_deviation(values: List[int]) -> float:
    """计算标准差"""
    if len(values) <= 1:
        return 0.0
    
    mean = sum(values) / len(values)
    variance = sum((x - mean) ** 2 for x in values) / len(values)
    return variance ** 0.5


async def compare_with_previous_results():
    """与之前的结果进行比较"""
    print(f"\n📋 与修复前的对比:")
    print("  修复前: 10轮测试选择1个不同CK (负载均衡失效)")
    print("  修复后: 预期选择多个不同CK (负载均衡正常)")


async def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description="测试增强的负载均衡算法")
    parser.add_argument("--merchant-id", type=int, default=2, help="商户ID")
    parser.add_argument("--rounds", type=int, default=30, help="测试轮数")
    parser.add_argument("--compare", action="store_true", help="显示对比结果")
    
    args = parser.parse_args()
    
    await test_enhanced_load_balance(args.merchant_id, args.rounds)
    
    if args.compare:
        await compare_with_previous_results()
    
    print(f"\n🎉 测试完成！")


if __name__ == "__main__":
    asyncio.run(main())
