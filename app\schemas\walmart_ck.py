from typing import Optional, List, Dict, Any
from pydantic import BaseModel, Field
from datetime import datetime


# 基础模型
class WalmartCKBase(BaseModel):
    sign: str = Field(..., description="用户签名", min_length=10)
    total_limit: int = Field(20, description="总绑卡次数限制", ge=1, le=10000)
    active: int = Field(1, description="是否启用：1启用，0禁用")
    description: Optional[str] = Field(None, description="设置描述", max_length=255)

    class Config:
        from_attributes = True


# 创建沃尔玛CK
class WalmartCKCreate(WalmartCKBase):
    merchant_id: int = Field(..., description="所属商户ID")
    department_id: int = Field(..., description="所属部门ID")


# 更新沃尔玛CK
class WalmartCKUpdate(BaseModel):
    # sign: Optional[str] = Field(None, description="用户签名", min_length=10)
    total_limit: Optional[int] = Field(None, description="总绑卡次数限制", ge=1, le=10000)
    active: Optional[int] = Field(None, description="是否启用：1启用，0禁用")
    description: Optional[str] = Field(None, description="设置描述", max_length=255)

    class Config:
        from_attributes = True


# 沃尔玛CK响应
class WalmartCK(WalmartCKBase):
    id: int = Field(..., description="主键ID")
    bind_count: int = Field(0, description="累计已绑卡数量")
    last_bind_time: Optional[str] = Field(None, description="最后绑卡时间")
    created_by: Optional[int] = Field(None, description="创建者用户ID")
    merchant_id: int = Field(..., description="所属商户ID")
    department_id: int = Field(..., description="所属部门ID")
    is_deleted: bool = Field(False, description="是否已删除")
    created_at: datetime = Field(..., description="创建时间")
    updated_at: Optional[datetime] = Field(None, description="更新时间")


# 沃尔玛CK响应（包含关联信息）
class WalmartCKWithRelations(WalmartCK):
    merchant_name: Optional[str] = Field(None, description="商户名称")
    department_name: Optional[str] = Field(None, description="部门名称")
    creator_username: Optional[str] = Field(None, description="创建者用户名")
    creator_name: Optional[str] = Field(None, description="创建者姓名")


# 沃尔玛CK列表
class WalmartCKList(BaseModel):
    total: int = Field(..., description="总记录数")
    items: List[WalmartCK] = Field(..., description="沃尔玛CK列表")


# 沃尔玛CK列表（包含关联信息）
class WalmartCKListWithRelations(BaseModel):
    total: int = Field(..., description="总记录数")
    items: List[WalmartCKWithRelations] = Field(..., description="沃尔玛CK列表")


# 沃尔玛CK查询参数
class WalmartCKQuery(BaseModel):
    merchant_id: Optional[int] = Field(None, description="商户ID")
    department_id: Optional[int] = Field(None, description="部门ID")
    active: Optional[int] = Field(None, description="是否启用")
    page: int = Field(1, description="页码", ge=1)
    page_size: int = Field(20, description="每页数量", ge=1, le=100)

    class Config:
        from_attributes = True


# 批量创建沃尔玛CK
class WalmartCKBatchCreate(BaseModel):
    signs: List[str] = Field(..., description="CK签名列表", min_length=1, max_length=100)
    merchant_id: int = Field(..., description="所属商户ID")
    department_id: int = Field(..., description="所属部门ID")
    total_limit: int = Field(20, description="总绑卡次数限制", ge=1, le=10000)
    active: int = Field(1, description="是否启用：1启用，0禁用")
    description: Optional[str] = Field(None, description="设置描述", max_length=255)
    descriptions: Optional[List[str]] = Field(None, description="每个CK的独立备注列表（可选，与signs对应）")
    base_description: Optional[str] = Field(None, description="原始备注（用于显示）", max_length=255)

    class Config:
        from_attributes = True


# 批量创建结果
class WalmartCKBatchResult(BaseModel):
    success_count: int = Field(..., description="成功创建数量")
    failed_count: int = Field(..., description="失败数量")
    total_count: int = Field(..., description="总数量")
    success_items: List[WalmartCK] = Field(..., description="成功创建的CK列表")
    failed_items: List[Dict[str, Any]] = Field(..., description="失败的CK信息")
    message: str = Field(..., description="操作结果消息")

    class Config:
        from_attributes = True


# 沃尔玛CK统计
class WalmartCKStatistics(BaseModel):
    # 新的CK状态统计字段
    total_ck_count: int = Field(0, description="CK总数（包括已删除的）")
    available_ck_count: int = Field(0, description="可用CK数量（active=True且未删除）")
    expired_ck_count: int = Field(0, description="已过期CK数量")
    deleted_ck_count: int = Field(0, description="已删除CK数量")

    # 保持向后兼容的原有字段
    total_count: int = Field(0, description="总CK数量（未删除的）")
    active_count: int = Field(0, description="启用CK数量")
    inactive_count: int = Field(0, description="禁用CK数量")
    today_bind_count: int = Field(0, description="今日绑卡总数")
    success_bind_count: int = Field(0, description="成功绑卡总数")

    class Config:
        from_attributes = True


# 批量删除CK请求
class WalmartCKBatchDelete(BaseModel):
    ck_ids: List[int] = Field(..., description="要删除的CK ID列表", min_items=1)

    class Config:
        from_attributes = True


# 批量删除CK响应
class WalmartCKBatchDeleteResult(BaseModel):
    total_count: int = Field(..., description="总删除数量")
    success_count: int = Field(..., description="成功删除数量")
    failed_count: int = Field(..., description="失败删除数量")
    failed_items: List[Dict[str, Any]] = Field(default_factory=list, description="失败的项目详情")
    success_items: List[int] = Field(default_factory=list, description="成功删除的CK ID列表")

    class Config:
        from_attributes = True


# 批量启用/禁用CK请求
class WalmartCKBatchOperation(BaseModel):
    ck_ids: List[int] = Field(..., description="要操作的CK ID列表", min_items=1)

    class Config:
        from_attributes = True


# 批量启用/禁用CK响应
class WalmartCKBatchOperationResult(BaseModel):
    total_count: int = Field(..., description="总操作数量")
    success_count: int = Field(..., description="成功操作数量")
    failed_count: int = Field(..., description="失败操作数量")
    failed_items: List[Dict[str, Any]] = Field(default_factory=list, description="失败的项目详情")
    success_items: List[int] = Field(default_factory=list, description="成功操作的CK ID列表")

    class Config:
        from_attributes = True
