<script setup>
import { ref, onMounted, reactive } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { merchantApi } from '@/api/modules/merchant'
import { Search, Plus, Download } from '@element-plus/icons-vue'
import { formatDateTime } from '@/utils/dateUtils'



const router = useRouter()
const loading = ref(false)
const merchants = ref([])
const searchForm = reactive({
    search: ''
})
const pagination = reactive({
    total: 0,
    current: 1,
    pageSize: 10
})

// 获取商家列表
const fetchMerchants = async (searchTerm) => {
    loading.value = true
    merchants.value = [] // Reset merchants before fetching
    pagination.total = 0 // Reset pagination
    try {
        const params = {
            skip: (pagination.current - 1) * pagination.pageSize,
            limit: pagination.pageSize
        }

        if (searchTerm) {
            params.search = searchTerm
        }

        const response = await merchantApi.getList(params)

        // --- Add Robustness Checks ---
        if (response && Array.isArray(response.items) && typeof response.total === 'number') {
            merchants.value = response.items
            pagination.total = response.total
        } else {
            console.error('获取商家列表失败: 响应数据格式不正确', response);
            merchants.value = []; // Assign empty array
            pagination.total = 0;
            ElMessage.error('获取商家列表失败：数据格式错误');
        }
        // --- End Checks ---

    } catch (error) {
        console.error('获取商家列表失败:', error)
        merchants.value = []; // Ensure empty array on error
        pagination.total = 0;
        // 尝试显示更具体的错误信息
        const errorMsg = error?.response?.data?.detail || error?.message || '请稍后重试';
        ElMessage.error(`获取商家列表失败: ${errorMsg}`)
    } finally {
        loading.value = false
    }
}

// 处理页码变化
const handlePageChange = (page) => {
    pagination.current = page
    fetchMerchants(searchForm.search)
}

// 处理每页数量变化
const handleSizeChange = (size) => {
    pagination.pageSize = size
    pagination.current = 1
    fetchMerchants(searchForm.search)
}

// 处理搜索
const handleSearch = () => {
    pagination.current = 1
    fetchMerchants(searchForm.search)
}

// 重置搜索
const resetSearch = () => {
    searchForm.search = ''
    pagination.current = 1
    fetchMerchants()
}

// 添加商家
const handleAdd = () => {
    router.push('/merchant/add')
}

// 编辑商家
const handleEdit = (row) => {
    router.push(`/merchant/edit/${row.id}`)
}

// 切换商家状态
const toggleStatus = (row) => {
    const action = row.status ? '禁用' : '启用'
    ElMessageBox.confirm(`确定要${action}该商家吗？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
    }).then(async () => {
        loading.value = true
        try {
            await merchantApi.toggleStatus(row.id, !row.status)
            ElMessage.success(`${action}成功`)
            // 更新本地数据
            row.status = !row.status
        } catch (error) {
            console.error(`${action}失败:`, error)
            ElMessage.error(`${action}失败`)
        } finally {
            loading.value = false
        }
    }).catch(() => {
        // 取消操作
    })
}

// 查看商家详情
const viewDetail = (row) => {
    if (!row.id) {
        ElMessage.error('无效的商家ID')
        return
    }
    router.push(`/merchant/detail/${row.id}`)
}

// 导出商家数据
const exportData = () => {
    ElMessage.info('导出功能开发中')
}

onMounted(() => {
    fetchMerchants(searchForm.search)
})
</script>

<template>
    <div class="merchant-container">
        <!-- 商家列表卡片 -->
        <el-card class="merchant-card" shadow="never">
            <!-- Filter/Operations Area -->
            <el-form :model="searchForm" :inline="true" size="small" class="filter-form">
                <el-form-item label="搜索">
                    <el-input v-model="searchForm.search" placeholder="商家名称或编码" clearable @keyup.enter="handleSearch"
                        style="width: 200px;" />
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="handleSearch" :icon="Search">搜索</el-button>
                    <el-button @click="resetSearch">重置</el-button>
                </el-form-item>
                <el-form-item style="margin-left: auto;"> <!-- Push buttons to the right -->
                    <el-button type="primary" @click="handleAdd" :icon="Plus">
                        新增商家
                    </el-button>
                    <el-button type="success" @click="exportData" :icon="Download">
                        导出数据
                    </el-button>
                </el-form-item>
            </el-form>

            <el-divider />

            <!-- 商家列表 -->
            <el-table :data="merchants" v-loading="loading" border stripe size="small" style="width: 100%">
                <el-table-column prop="name" label="商家名称" min-width="150" show-overflow-tooltip />
                <el-table-column prop="code" label="商家编码" min-width="120" show-overflow-tooltip />
                <el-table-column label="状态" width="80" align="center">
                    <template #default="scope">
                        <el-tag :type="scope.row.status ? 'success' : 'danger'" size="small">
                            {{ scope.row.status ? '启用' : '禁用' }}
                        </el-tag>
                    </template>
                </el-table-column>
                <el-table-column prop="daily_limit" label="每日上限" width="100" align="right" />
                <el-table-column prop="hourly_limit" label="每小时上限" width="110" align="right" />
                <el-table-column prop="concurrency_limit" label="并发数" width="80" align="right" />
                <el-table-column prop="contact_name" label="联系人" width="100" show-overflow-tooltip />
                <el-table-column prop="contact_phone" label="联系电话" width="120" show-overflow-tooltip />
                <el-table-column prop="created_at" label="创建时间" width="160" show-overflow-tooltip sortable>
                    <template #default="scope">
                        {{ formatDateTime(scope.row.created_at) }}
                    </template>
                </el-table-column>
                <el-table-column label="操作" fixed="right" width="200" align="center">
                    <template #default="scope">
                        <el-button type="primary" link size="small" @click="viewDetail(scope.row)">
                            详情
                        </el-button>
                        <el-button type="primary" link size="small" @click="handleEdit(scope.row)">
                            编辑
                        </el-button>
                        <el-button :type="scope.row.status ? 'danger' : 'success'" link size="small"
                            @click="toggleStatus(scope.row)">
                            {{ scope.row.status ? '禁用' : '启用' }}
                        </el-button>
                    </template>
                </el-table-column>
            </el-table>

            <!-- 分页 -->
            <div class="pagination-container">
                <el-pagination v-model:current-page="pagination.current" v-model:page-size="pagination.pageSize"
                    :total="pagination.total" :page-sizes="[10, 20, 50, 100]" background
                    layout="total, sizes, prev, pager, next, jumper" @update:current-page="handlePageChange"
                    @update:page-size="handleSizeChange" />
            </div>
        </el-card>
    </div>
</template>

<style scoped>
.merchant-container {
    padding: 20px;
}

.filter-form {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    /* Allow wrapping on smaller screens */
}

.filter-form .el-form-item {
    margin-bottom: 0;
    /* Remove default bottom margin for inline form */
    margin-right: 10px;
}

.pagination-container {
    display: flex;
    justify-content: flex-end;
    margin-top: 20px;
}

/* Remove old card header styles if they conflict */
/*
.merchant-card {
    margin-bottom: 20px;
}
.card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
}
.card-header span {
    font-size: 18px;
    font-weight: bold;
}
.operations {
    display: flex;
    align-items: center;
}
*/
</style>