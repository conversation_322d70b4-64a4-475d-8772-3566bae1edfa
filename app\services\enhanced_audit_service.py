"""
增强的安全审计服务
扩展现有审计功能，添加更多审计维度和高级分析能力
"""

import json
import hashlib
from typing import Dict, Any, Optional, List
from datetime import datetime, timedelta
from sqlalchemy.orm import Session
from sqlalchemy import func, and_, or_
from fastapi import Request

from app.models.audit import AuditLog, AuditEventType, AuditLevel
from app.models.user import User
from app.services.audit_service import AuditService
from app.core.logging import get_logger

logger = get_logger("enhanced_audit")


class EnhancedAuditService(AuditService):
    """增强的安全审计服务"""

    def __init__(self, db: Session):
        super().__init__(db)
        self.session_tracking = {}  # 会话跟踪
        self.config_baseline = {}   # 配置基线
        self.access_patterns = {}   # 访问模式分析

    async def log_session_event(
        self,
        db: Session,
        user_id: int,
        session_id: str,
        event_type: str,
        request: Optional[Request] = None,
        details: Optional[Dict[str, Any]] = None
    ):
        """记录用户会话相关事件"""
        session_details = {
            "session_id": session_id,
            "event_type": event_type,
            "timestamp": datetime.utcnow().isoformat(),
            **(details or {})
        }

        # 检测会话异常
        anomalies = await self._detect_session_anomalies(db, user_id, session_id, event_type)
        if anomalies:
            session_details["anomalies"] = anomalies

        return await self.log_event(
            db=db,
            event_type=AuditEventType.ACCESS,
            level=AuditLevel.WARNING if anomalies else AuditLevel.INFO,
            message=f"Session event: {event_type}",
            user_id=user_id,
            action="SESSION_EVENT",
            details=session_details,
            request=request
        )

    async def log_configuration_change(
        self,
        db: Session,
        user_id: int,
        config_type: str,
        config_key: str,
        old_value: Any,
        new_value: Any,
        request: Optional[Request] = None,
        merchant_id: Optional[int] = None
    ):
        """记录配置变更事件"""
        # 生成变更摘要
        change_hash = self._generate_change_hash(config_type, config_key, old_value, new_value)
        
        change_details = {
            "config_type": config_type,
            "config_key": config_key,
            "old_value": self._sanitize_sensitive_data(old_value),
            "new_value": self._sanitize_sensitive_data(new_value),
            "change_hash": change_hash,
            "impact_assessment": self._assess_change_impact(config_type, config_key),
            "timestamp": datetime.utcnow().isoformat()
        }

        # 检查是否为敏感配置变更
        is_sensitive = self._is_sensitive_config(config_type, config_key)
        level = AuditLevel.WARNING if is_sensitive else AuditLevel.INFO

        return await self.log_event(
            db=db,
            event_type=AuditEventType.OPERATION,
            level=level,
            message=f"Configuration changed: {config_type}.{config_key}",
            user_id=user_id,
            merchant_id=merchant_id,
            action="CONFIG_CHANGE",
            resource_type="CONFIGURATION",
            resource_id=config_key,
            details=change_details,
            request=request
        )

    async def log_data_access_pattern(
        self,
        db: Session,
        user_id: int,
        resource_type: str,
        access_type: str,
        record_count: int,
        request: Optional[Request] = None,
        merchant_id: Optional[int] = None
    ):
        """记录数据访问模式"""
        # 分析访问模式
        pattern_analysis = await self._analyze_access_pattern(
            db, user_id, resource_type, access_type, record_count
        )

        access_details = {
            "resource_type": resource_type,
            "access_type": access_type,
            "record_count": record_count,
            "pattern_analysis": pattern_analysis,
            "timestamp": datetime.utcnow().isoformat()
        }

        # 根据访问模式确定风险级别
        level = self._determine_access_risk_level(pattern_analysis)

        return await self.log_event(
            db=db,
            event_type=AuditEventType.ACCESS,
            level=level,
            message=f"Data access: {access_type} {record_count} {resource_type} records",
            user_id=user_id,
            merchant_id=merchant_id,
            action=f"DATA_ACCESS_{access_type.upper()}",
            resource_type=resource_type.upper(),
            details=access_details,
            request=request
        )

    async def log_system_resource_usage(
        self,
        db: Session,
        user_id: int,
        operation_type: str,
        resource_usage: Dict[str, Any],
        request: Optional[Request] = None,
        merchant_id: Optional[int] = None
    ):
        """记录系统资源使用情况"""
        # 分析资源使用模式
        usage_analysis = self._analyze_resource_usage(resource_usage)
        
        resource_details = {
            "operation_type": operation_type,
            "resource_usage": resource_usage,
            "usage_analysis": usage_analysis,
            "timestamp": datetime.utcnow().isoformat()
        }

        # 检测资源使用异常
        level = AuditLevel.WARNING if usage_analysis.get("is_abnormal") else AuditLevel.INFO

        return await self.log_event(
            db=db,
            event_type=AuditEventType.SYSTEM,
            level=level,
            message=f"Resource usage: {operation_type}",
            user_id=user_id,
            merchant_id=merchant_id,
            action="RESOURCE_USAGE",
            resource_type="SYSTEM_RESOURCE",
            details=resource_details,
            request=request
        )

    async def log_third_party_api_call(
        self,
        db: Session,
        user_id: int,
        api_endpoint: str,
        method: str,
        status_code: int,
        response_time: float,
        request: Optional[Request] = None,
        merchant_id: Optional[int] = None
    ):
        """记录第三方API调用"""
        api_details = {
            "api_endpoint": api_endpoint,
            "method": method,
            "status_code": status_code,
            "response_time": response_time,
            "timestamp": datetime.utcnow().isoformat()
        }

        # 检测API调用异常
        is_error = status_code >= 400
        is_slow = response_time > 5000  # 5秒以上认为是慢请求

        if is_error:
            level = AuditLevel.ERROR
        elif is_slow:
            level = AuditLevel.WARNING
        else:
            level = AuditLevel.INFO

        return await self.log_event(
            db=db,
            event_type=AuditEventType.SYSTEM,
            level=level,
            message=f"Third-party API call: {method} {api_endpoint}",
            user_id=user_id,
            merchant_id=merchant_id,
            action="THIRD_PARTY_API_CALL",
            resource_type="EXTERNAL_API",
            resource_id=api_endpoint,
            details=api_details,
            request=request
        )

    async def get_audit_analytics(
        self,
        db: Session,
        current_user: User,
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None
    ) -> Dict[str, Any]:
        """获取审计分析数据"""
        if not start_date:
            start_date = datetime.utcnow() - timedelta(days=30)
        if not end_date:
            end_date = datetime.utcnow()

        # 构建基础查询
        query = db.query(AuditLog).filter(
            AuditLog.created_at.between(start_date, end_date)
        )
        
        # 应用数据隔离
        query = self.apply_data_isolation(query, current_user)

        # 统计分析
        analytics = {
            "time_range": {
                "start_date": start_date.isoformat(),
                "end_date": end_date.isoformat()
            },
            "event_statistics": await self._get_event_statistics(query),
            "risk_analysis": await self._get_risk_analysis(query),
            "user_activity": await self._get_user_activity_analysis(query),
            "resource_access": await self._get_resource_access_analysis(query),
            "anomaly_detection": await self._get_anomaly_detection_results(query),
            "trends": await self._get_trend_analysis(query, start_date, end_date)
        }

        return analytics

    async def _detect_session_anomalies(
        self,
        db: Session,
        user_id: int,
        session_id: str,
        event_type: str
    ) -> List[str]:
        """检测会话异常"""
        anomalies = []

        # 检查并发会话
        recent_sessions = db.query(AuditLog).filter(
            and_(
                AuditLog.user_id == user_id,
                AuditLog.action == "SESSION_EVENT",
                AuditLog.created_at > datetime.utcnow() - timedelta(hours=1)
            )
        ).all()

        active_sessions = set()
        for log in recent_sessions:
            if log.details and "session_id" in log.details:
                active_sessions.add(log.details["session_id"])

        if len(active_sessions) > 3:  # 超过3个并发会话
            anomalies.append("multiple_concurrent_sessions")

        # 检查会话超时
        if event_type == "session_timeout":
            last_activity = db.query(AuditLog).filter(
                and_(
                    AuditLog.user_id == user_id,
                    AuditLog.details.contains(f'"session_id": "{session_id}"')
                )
            ).order_by(AuditLog.created_at.desc()).first()

            if last_activity:
                inactive_time = datetime.utcnow() - last_activity.created_at
                if inactive_time > timedelta(hours=8):  # 超过8小时无活动
                    anomalies.append("extended_session_timeout")

        return anomalies

    def _generate_change_hash(self, config_type: str, config_key: str, old_value: Any, new_value: Any) -> str:
        """生成配置变更哈希"""
        change_data = f"{config_type}:{config_key}:{old_value}:{new_value}"
        return hashlib.md5(change_data.encode()).hexdigest()

    def _sanitize_sensitive_data(self, value: Any) -> Any:
        """清理敏感数据"""
        if isinstance(value, str):
            # 检查是否包含敏感信息
            sensitive_patterns = ["password", "token", "secret", "key", "credential"]
            value_lower = value.lower()

            for pattern in sensitive_patterns:
                if pattern in value_lower:
                    return "***REDACTED***"

        return value

    def _assess_change_impact(self, config_type: str, config_key: str) -> str:
        """评估配置变更影响"""
        high_impact_configs = {
            "security": ["authentication", "authorization", "encryption"],
            "system": ["database", "cache", "logging"],
            "business": ["payment", "notification", "workflow"]
        }

        for category, keys in high_impact_configs.items():
            if config_type == category or any(key in config_key.lower() for key in keys):
                return "high"

        return "medium" if config_type in ["user", "merchant"] else "low"

    def _is_sensitive_config(self, config_type: str, config_key: str) -> bool:
        """判断是否为敏感配置"""
        sensitive_types = ["security", "authentication", "authorization"]
        sensitive_keys = ["password", "secret", "key", "token", "credential"]

        return (config_type in sensitive_types or
                any(key in config_key.lower() for key in sensitive_keys))

    async def _analyze_access_pattern(
        self,
        db: Session,
        user_id: int,
        resource_type: str,
        access_type: str,
        record_count: int
    ) -> Dict[str, Any]:
        """分析数据访问模式"""
        # 获取用户最近的访问历史
        recent_access = db.query(AuditLog).filter(
            and_(
                AuditLog.user_id == user_id,
                AuditLog.resource_type == resource_type.upper(),
                AuditLog.created_at > datetime.utcnow() - timedelta(hours=24)
            )
        ).all()

        # 计算访问频率
        access_frequency = len(recent_access)

        # 计算平均记录数
        total_records = sum(
            log.details.get("record_count", 0)
            for log in recent_access
            if log.details and "record_count" in log.details
        )
        avg_records = total_records / max(len(recent_access), 1)

        # 异常检测
        is_bulk_access = record_count > avg_records * 5  # 超过平均值5倍
        is_frequent_access = access_frequency > 50  # 24小时内超过50次访问

        return {
            "access_frequency_24h": access_frequency,
            "average_record_count": avg_records,
            "current_record_count": record_count,
            "is_bulk_access": is_bulk_access,
            "is_frequent_access": is_frequent_access,
            "risk_score": self._calculate_access_risk_score(
                record_count, avg_records, access_frequency
            )
        }

    def _determine_access_risk_level(self, pattern_analysis: Dict[str, Any]) -> str:
        """根据访问模式确定风险级别"""
        risk_score = pattern_analysis.get("risk_score", 0)

        if risk_score >= 80:
            return AuditLevel.CRITICAL
        elif risk_score >= 60:
            return AuditLevel.ERROR
        elif risk_score >= 40:
            return AuditLevel.WARNING
        else:
            return AuditLevel.INFO

    def _calculate_access_risk_score(self, record_count: int, avg_records: float, frequency: int) -> int:
        """计算访问风险分数"""
        score = 0

        # 记录数量风险
        if record_count > avg_records * 10:
            score += 40
        elif record_count > avg_records * 5:
            score += 25
        elif record_count > avg_records * 2:
            score += 10

        # 访问频率风险
        if frequency > 100:
            score += 30
        elif frequency > 50:
            score += 20
        elif frequency > 20:
            score += 10

        # 时间模式风险（夜间访问）
        current_hour = datetime.utcnow().hour
        if current_hour < 6 or current_hour > 22:  # 夜间时段
            score += 15

        return min(score, 100)  # 最高100分

    def _analyze_resource_usage(self, resource_usage: Dict[str, Any]) -> Dict[str, Any]:
        """分析系统资源使用"""
        analysis = {
            "is_abnormal": False,
            "warnings": []
        }

        # CPU使用率检查
        cpu_usage = resource_usage.get("cpu_percent", 0)
        if cpu_usage > 90:
            analysis["is_abnormal"] = True
            analysis["warnings"].append("high_cpu_usage")

        # 内存使用率检查
        memory_usage = resource_usage.get("memory_percent", 0)
        if memory_usage > 85:
            analysis["is_abnormal"] = True
            analysis["warnings"].append("high_memory_usage")

        # 执行时间检查
        execution_time = resource_usage.get("execution_time_ms", 0)
        if execution_time > 30000:  # 超过30秒
            analysis["is_abnormal"] = True
            analysis["warnings"].append("long_execution_time")

        # 数据库连接检查
        db_connections = resource_usage.get("db_connections", 0)
        if db_connections > 50:
            analysis["is_abnormal"] = True
            analysis["warnings"].append("high_db_connections")

        return analysis

    async def _get_event_statistics(self, query) -> Dict[str, Any]:
        """获取事件统计"""
        # 按事件类型统计
        event_type_stats = {}
        for event_type in [AuditEventType.ACCESS, AuditEventType.OPERATION, AuditEventType.SYSTEM]:
            count = query.filter(AuditLog.event_type == event_type).count()
            event_type_stats[event_type] = count

        # 按级别统计
        level_stats = {}
        for level in [AuditLevel.INFO, AuditLevel.WARNING, AuditLevel.ERROR, AuditLevel.CRITICAL]:
            count = query.filter(AuditLog.level == level).count()
            level_stats[level] = count

        return {
            "total_events": query.count(),
            "by_event_type": event_type_stats,
            "by_level": level_stats
        }

    async def _get_risk_analysis(self, query) -> Dict[str, Any]:
        """获取风险分析"""
        # 高风险事件
        high_risk_count = query.filter(
            AuditLog.level.in_([AuditLevel.ERROR, AuditLevel.CRITICAL])
        ).count()

        total_count = query.count()
        risk_ratio = (high_risk_count / max(total_count, 1)) * 100

        # 风险趋势（最近7天）
        risk_trend = []
        for i in range(7):
            date = datetime.utcnow().date() - timedelta(days=i)
            day_start = datetime.combine(date, datetime.min.time())
            day_end = datetime.combine(date, datetime.max.time())

            day_high_risk = query.filter(
                and_(
                    AuditLog.level.in_([AuditLevel.ERROR, AuditLevel.CRITICAL]),
                    AuditLog.created_at.between(day_start, day_end)
                )
            ).count()

            risk_trend.append({
                "date": date.isoformat(),
                "high_risk_count": day_high_risk
            })

        return {
            "high_risk_events": high_risk_count,
            "total_events": total_count,
            "risk_ratio": round(risk_ratio, 2),
            "risk_trend": list(reversed(risk_trend))
        }

    async def _get_user_activity_analysis(self, query) -> Dict[str, Any]:
        """获取用户活动分析"""
        # 最活跃用户
        user_activity = self.db.query(
            AuditLog.user_id,
            func.count(AuditLog.id).label('activity_count')
        ).filter(
            AuditLog.id.in_([log.id for log in query.all()])
        ).group_by(AuditLog.user_id).order_by(
            func.count(AuditLog.id).desc()
        ).limit(10).all()

        # 活动时间分布
        hour_distribution = {}
        for hour in range(24):
            count = query.filter(
                func.extract('hour', AuditLog.created_at) == hour
            ).count()
            hour_distribution[str(hour)] = count

        return {
            "most_active_users": [
                {"user_id": user_id, "activity_count": count}
                for user_id, count in user_activity
            ],
            "hourly_distribution": hour_distribution
        }

    async def _get_resource_access_analysis(self, query) -> Dict[str, Any]:
        """获取资源访问分析"""
        # 按资源类型统计
        resource_stats = self.db.query(
            AuditLog.resource_type,
            func.count(AuditLog.id).label('access_count')
        ).filter(
            AuditLog.id.in_([log.id for log in query.all()])
        ).group_by(AuditLog.resource_type).all()

        # 按操作类型统计
        action_stats = self.db.query(
            AuditLog.action,
            func.count(AuditLog.id).label('action_count')
        ).filter(
            AuditLog.id.in_([log.id for log in query.all()])
        ).group_by(AuditLog.action).all()

        return {
            "by_resource_type": {
                resource_type: count for resource_type, count in resource_stats
            },
            "by_action": {
                action: count for action, count in action_stats
            }
        }

    async def _get_anomaly_detection_results(self, query) -> Dict[str, Any]:
        """获取异常检测结果"""
        anomalies = []

        # 检测异常访问模式
        logs_with_details = query.filter(AuditLog.details.isnot(None)).all()

        for log in logs_with_details:
            if log.details and isinstance(log.details, dict):
                # 检查是否包含异常标记
                if log.details.get("anomalies"):
                    anomalies.append({
                        "type": "session_anomaly",
                        "timestamp": log.created_at.isoformat(),
                        "user_id": log.user_id,
                        "details": log.details.get("anomalies")
                    })

                # 检查高风险访问
                if log.details.get("risk_score", 0) > 70:
                    anomalies.append({
                        "type": "high_risk_access",
                        "timestamp": log.created_at.isoformat(),
                        "user_id": log.user_id,
                        "risk_score": log.details.get("risk_score")
                    })

        return {
            "total_anomalies": len(anomalies),
            "anomalies": anomalies[:20]  # 只返回最近20个异常
        }

    async def _get_trend_analysis(self, query, start_date: datetime, end_date: datetime) -> Dict[str, Any]:
        """获取趋势分析"""
        # 按天统计事件数量
        daily_stats = []
        current_date = start_date.date()
        end_date_only = end_date.date()

        while current_date <= end_date_only:
            day_start = datetime.combine(current_date, datetime.min.time())
            day_end = datetime.combine(current_date, datetime.max.time())

            day_count = query.filter(
                AuditLog.created_at.between(day_start, day_end)
            ).count()

            daily_stats.append({
                "date": current_date.isoformat(),
                "event_count": day_count
            })

            current_date += timedelta(days=1)

        # 计算趋势
        if len(daily_stats) >= 2:
            recent_avg = sum(stat["event_count"] for stat in daily_stats[-7:]) / min(7, len(daily_stats))
            earlier_avg = sum(stat["event_count"] for stat in daily_stats[:-7]) / max(1, len(daily_stats) - 7)
            trend_direction = "increasing" if recent_avg > earlier_avg else "decreasing"
        else:
            trend_direction = "stable"

        return {
            "daily_statistics": daily_stats,
            "trend_direction": trend_direction
        }
