-- ========================================
-- 07-修复数据库ENUM字段为VARCHAR类型
-- 目的：提高数据库扩展性，避免枚举类型的限制
-- 执行顺序：第7步 - 在现有数据库上执行
-- ========================================

USE walmart_card_db;

-- 设置连接字符集
SET NAMES utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 设置时区
SET time_zone = '+08:00';

-- 显示修复开始信息
SELECT '开始修复数据库ENUM字段为VARCHAR类型...' AS status;

-- ========================================
-- 1. 修复 permissions 表的 resource_type 字段
-- ========================================

SELECT '正在修复 permissions.resource_type 字段...' AS status;

-- 显示当前字段定义
SHOW COLUMNS FROM permissions LIKE 'resource_type';

-- 修改字段类型
ALTER TABLE `permissions` 
MODIFY COLUMN `resource_type` VARCHAR(20) NOT NULL DEFAULT 'api' COMMENT '资源类型';

-- 验证修改结果
SELECT '修复后的 permissions.resource_type 字段:' AS status;
SHOW COLUMNS FROM permissions LIKE 'resource_type';

-- ========================================
-- 2. 修复 menus 表的 menu_type 字段
-- ========================================

SELECT '正在修复 menus.menu_type 字段...' AS status;

-- 显示当前字段定义
SHOW COLUMNS FROM menus LIKE 'menu_type';

-- 修改字段类型
ALTER TABLE `menus` 
MODIFY COLUMN `menu_type` VARCHAR(20) NOT NULL DEFAULT 'menu' COMMENT '菜单类型';

-- 验证修改结果
SELECT '修复后的 menus.menu_type 字段:' AS status;
SHOW COLUMNS FROM menus LIKE 'menu_type';

-- ========================================
-- 3. 修复 binding_logs 表的枚举字段
-- ========================================

SELECT '正在修复 binding_logs 表的枚举字段...' AS status;

-- 显示当前字段定义
SHOW COLUMNS FROM binding_logs WHERE Field IN ('log_type', 'log_level');

-- 修改 log_type 字段
ALTER TABLE `binding_logs` 
MODIFY COLUMN `log_type` VARCHAR(20) NOT NULL DEFAULT 'system' COMMENT '日志类型';

-- 修改 log_level 字段
ALTER TABLE `binding_logs` 
MODIFY COLUMN `log_level` VARCHAR(20) NOT NULL DEFAULT 'info' COMMENT '日志级别';

-- 验证修改结果
SELECT '修复后的 binding_logs 枚举字段:' AS status;
SHOW COLUMNS FROM binding_logs WHERE Field IN ('log_type', 'log_level');

-- ========================================
-- 4. 修复 system_settings 表的 type 字段
-- ========================================

SELECT '正在修复 system_settings.type 字段...' AS status;

-- 显示当前字段定义
SHOW COLUMNS FROM system_settings LIKE 'type';

-- 修改字段类型
ALTER TABLE `system_settings` 
MODIFY COLUMN `type` VARCHAR(20) NOT NULL DEFAULT 'string' COMMENT '值类型';

-- 验证修改结果
SELECT '修复后的 system_settings.type 字段:' AS status;
SHOW COLUMNS FROM system_settings LIKE 'type';

-- ========================================
-- 5. 修复 notifications 表的枚举字段
-- ========================================

SELECT '正在修复 notifications 表的枚举字段...' AS status;

-- 显示当前字段定义
SHOW COLUMNS FROM notifications WHERE Field IN ('type', 'status');

-- 修改 type 字段
ALTER TABLE `notifications` 
MODIFY COLUMN `type` VARCHAR(20) NOT NULL DEFAULT 'system' COMMENT '通知类型';

-- 修改 status 字段
ALTER TABLE `notifications` 
MODIFY COLUMN `status` VARCHAR(20) NOT NULL DEFAULT 'unread' COMMENT '通知状态';

-- 验证修改结果
SELECT '修复后的 notifications 枚举字段:' AS status;
SHOW COLUMNS FROM notifications WHERE Field IN ('type', 'status');

-- ========================================
-- 6. 修复 notification_configs 表的 type 字段
-- ========================================

SELECT '正在修复 notification_configs.type 字段...' AS status;

-- 显示当前字段定义
SHOW COLUMNS FROM notification_configs LIKE 'type';

-- 修改字段类型
ALTER TABLE `notification_configs` 
MODIFY COLUMN `type` VARCHAR(20) NOT NULL COMMENT '通知类型';

-- 验证修改结果
SELECT '修复后的 notification_configs.type 字段:' AS status;
SHOW COLUMNS FROM notification_configs LIKE 'type';

-- ========================================
-- 7. 修复 audit_log 表的枚举字段
-- ========================================

SELECT '正在修复 audit_log 表的枚举字段...' AS status;

-- 显示当前字段定义
SHOW COLUMNS FROM audit_log WHERE Field IN ('event_type', 'level');

-- 修改 event_type 字段
ALTER TABLE `audit_log` 
MODIFY COLUMN `event_type` VARCHAR(20) NOT NULL DEFAULT 'SYSTEM' COMMENT '事件类型';

-- 修改 level 字段
ALTER TABLE `audit_log` 
MODIFY COLUMN `level` VARCHAR(20) NOT NULL DEFAULT 'INFO' COMMENT '事件级别';

-- 验证修改结果
SELECT '修复后的 audit_log 枚举字段:' AS status;
SHOW COLUMNS FROM audit_log WHERE Field IN ('event_type', 'level');

-- ========================================
-- 8. 修复 migration_logs 表的 status 字段（如果存在）
-- ========================================

SELECT '正在修复 migration_logs.status 字段（如果存在）...' AS status;

-- 检查表是否存在
SET @table_exists = (SELECT COUNT(*) FROM INFORMATION_SCHEMA.TABLES
                     WHERE TABLE_SCHEMA = 'walmart_card_db'
                     AND TABLE_NAME = 'migration_logs');

-- 如果表存在，则修复字段
SET @sql = IF(@table_exists > 0,
    'ALTER TABLE `migration_logs` MODIFY COLUMN `status` VARCHAR(20) NOT NULL DEFAULT ''started'' COMMENT ''状态''',
    'SELECT ''migration_logs 表不存在，跳过修复'' AS status');

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 验证修复结果（如果表存在）
SET @sql = IF(@table_exists > 0,
    'SHOW COLUMNS FROM migration_logs LIKE ''status''',
    'SELECT ''migration_logs 表不存在'' AS status');

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- ========================================
-- 9. 验证所有修复结果
-- ========================================

SELECT '验证所有ENUM字段修复结果...' AS status;

-- 检查是否还有ENUM字段存在
SELECT 
    TABLE_NAME,
    COLUMN_NAME,
    DATA_TYPE,
    COLUMN_TYPE,
    COLUMN_DEFAULT,
    COLUMN_COMMENT
FROM 
    INFORMATION_SCHEMA.COLUMNS 
WHERE 
    TABLE_SCHEMA = 'walmart_card_db' 
    AND DATA_TYPE = 'enum'
ORDER BY 
    TABLE_NAME, COLUMN_NAME;

-- 显示修复完成信息
SELECT 
    '数据库ENUM字段修复完成！' AS status,
    '所有枚举字段已转换为VARCHAR类型，提高了系统扩展性' AS message,
    NOW() AS completion_time;

-- ========================================
-- 10. 修复完成后的数据完整性检查
-- ========================================

SELECT '执行数据完整性检查...' AS status;

-- 检查各表的数据量
SELECT 'permissions' AS table_name, COUNT(*) AS record_count FROM permissions
UNION ALL
SELECT 'menus' AS table_name, COUNT(*) AS record_count FROM menus
UNION ALL
SELECT 'binding_logs' AS table_name, COUNT(*) AS record_count FROM binding_logs
UNION ALL
SELECT 'system_settings' AS table_name, COUNT(*) AS record_count FROM system_settings
UNION ALL
SELECT 'notifications' AS table_name, COUNT(*) AS record_count FROM notifications
UNION ALL
SELECT 'notification_configs' AS table_name, COUNT(*) AS record_count FROM notification_configs
UNION ALL
SELECT 'audit_log' AS table_name, COUNT(*) AS record_count FROM audit_log;

SELECT '数据完整性检查完成！' AS status;
