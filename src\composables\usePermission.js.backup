import { computed } from 'vue'
import { useUserStore } from '@/store/modules/user'
import { usePermissionStore } from '@/store/modules/permission'

/**
 * 权限检查组合式函数
 * 基于后端全动态权限系统
 */
export function usePermission() {
  const userStore = useUserStore()
  const permissionStore = usePermissionStore()

  // 用户信息
  const userInfo = computed(() => userStore.userInfo)
  const userRoles = computed(() => userInfo.value?.roles || [])
  const isSuperuser = computed(() => userInfo.value?.is_superuser || false)
  const merchantId = computed(() => userInfo.value?.merchant_id)
  const departmentId = computed(() => userInfo.value?.department_id)

  // 权限检查函数
  const hasPermission = (permissionCode) => {
    // 超级管理员拥有所有权限
    if (isSuperuser.value) {
      return true
    }

    // 检查用户权限列表
    const userPermissions = userInfo.value?.permissions || []
    return userPermissions.includes(permissionCode)
  }

  // 角色检查函数
  const hasRole = (roleCode) => {
    if (typeof roleCode === 'string') {
      return userRoles.value.some(role => role.code === roleCode)
    }
    
    if (Array.isArray(roleCode)) {
      return roleCode.some(code => userRoles.value.some(role => role.code === code))
    }
    
    return false
  }

  // 菜单权限检查
  const hasMenuPermission = (menuCode) => {
    // 超级管理员拥有所有菜单权限
    if (isSuperuser.value) {
      return true
    }

    // 检查用户菜单列表
    const userMenus = userInfo.value?.menus || []
    return userMenus.includes(menuCode)
  }

  // 数据权限检查
  const canAccessMerchantData = (targetMerchantId) => {
    // 超级管理员可以访问所有商户数据
    if (isSuperuser.value) {
      return true
    }

    // 其他用户只能访问自己商户的数据
    return merchantId.value === targetMerchantId
  }

  const canAccessDepartmentData = (targetDepartmentId) => {
    // 超级管理员可以访问所有部门数据
    if (isSuperuser.value) {
      return true
    }

    // 商户管理员可以访问本商户所有部门数据
    if (hasRole('merchant_admin')) {
      return true
    }

    // 其他用户只能访问自己部门的数据
    return departmentId.value === targetDepartmentId
  }

  // 操作权限检查
  const canCreate = (resource) => {
    return hasPermission(`${resource}:create`) || hasPermission(`api:/api/v1/${resource}`)
  }

  const canEdit = (resource) => {
    return hasPermission(`${resource}:edit`) || hasPermission(`api:/api/v1/${resource}`)
  }

  const canDelete = (resource) => {
    return hasPermission(`${resource}:delete`) || hasPermission(`api:/api/v1/${resource}`)
  }

  const canView = (resource) => {
    return hasPermission(`${resource}:view`) || hasPermission(`api:/api/v1/${resource}`)
  }

  // 特定角色权限检查
  const isSuperAdmin = computed(() => hasRole('super_admin'))
  const isMerchantAdmin = computed(() => hasRole('merchant_admin'))
  const isMerchantOperator = computed(() => hasRole('merchant_operator'))
  const isCkSupplier = computed(() => hasRole('ck_supplier'))

  // 页面访问权限检查
  const canAccessPage = (pageCode) => {
    // 检查菜单权限
    if (hasMenuPermission(pageCode)) {
      return true
    }

    // 检查对应的API权限
    const apiPermission = `api:/api/v1/${pageCode}`
    return hasPermission(apiPermission)
  }

  // 按钮显示权限检查
  const showButton = (buttonCode) => {
    // 超级管理员显示所有按钮
    if (isSuperuser.value) {
      return true
    }

    // 检查具体的按钮权限
    return hasPermission(`button:${buttonCode}`)
  }

  return {
    // 基础信息
    userInfo,
    userRoles,
    isSuperuser,
    merchantId,
    departmentId,

    // 权限检查
    hasPermission,
    hasRole,
    hasMenuPermission,

    // 数据权限
    canAccessMerchantData,
    canAccessDepartmentData,

    // 操作权限
    canCreate,
    canEdit,
    canDelete,
    canView,

    // 角色检查
    isSuperAdmin,
    isMerchantAdmin,
    isMerchantOperator,
    isCkSupplier,

    // 页面和按钮权限
    canAccessPage,
    showButton
  }
}

/**
 * 权限指令
 * 用于在模板中进行权限控制
 */
export const vPermission = {
  mounted(el, binding) {
    const { hasPermission } = usePermission()
    const permission = binding.value

    if (!hasPermission(permission)) {
      el.style.display = 'none'
    }
  },
  
  updated(el, binding) {
    const { hasPermission } = usePermission()
    const permission = binding.value

    if (!hasPermission(permission)) {
      el.style.display = 'none'
    } else {
      el.style.display = ''
    }
  }
}

/**
 * 角色指令
 * 用于在模板中进行角色控制
 */
export const vRole = {
  mounted(el, binding) {
    const { hasRole } = usePermission()
    const role = binding.value

    if (!hasRole(role)) {
      el.style.display = 'none'
    }
  },
  
  updated(el, binding) {
    const { hasRole } = usePermission()
    const role = binding.value

    if (!hasRole(role)) {
      el.style.display = 'none'
    } else {
      el.style.display = ''
    }
  }
}
