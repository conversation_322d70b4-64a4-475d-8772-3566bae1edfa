[{"test_type": "绑卡功能测试", "timestamp": "20250615_221937", "datetime": "2025-06-15T22:19:37.633403", "summary": {"total_tests": 22, "passed_tests": 13, "failed_tests": 9, "success_rate": 59.09090909090909, "duration_seconds": 12.958651542663574}, "module_stats": {"绑卡API测试": {"total": 9, "passed": 6, "failed": 3}, "绑卡管理API测试": {"total": 4, "passed": 1, "failed": 3}, "批量绑卡API测试": {"total": 4, "passed": 3, "failed": 1}, "绑卡CRUD测试": {"total": 5, "passed": 3, "failed": 2}}, "test_results": [{"test_name": "有效绑卡请求", "success": false, "message": "绑卡请求失败，状态码: 401", "details": {"response": {"code": 1, "message": "签名验证失败", "data": null}}, "timestamp": 1749997169.0584848, "datetime": "2025-06-15T22:19:29.058484", "module": "绑卡API测试", "module_key": "test_bind_card_api"}, {"test_name": "无效卡号验证", "success": false, "message": "未正确验证卡号，状态码: 401", "details": {"response": {"code": 1, "message": "签名验证失败", "data": null}}, "timestamp": 1749997169.0690851, "datetime": "2025-06-15T22:19:29.069085", "module": "绑卡API测试", "module_key": "test_bind_card_api"}, {"test_name": "无效金额验证", "success": true, "message": "正确拒绝了无效金额", "details": {"status_code": 422}, "timestamp": 1749997169.0741098, "datetime": "2025-06-15T22:19:29.074109", "module": "绑卡API测试", "module_key": "test_bind_card_api"}, {"test_name": "无效API密钥验证", "success": true, "message": "正确拒绝了无效API密钥", "details": {"status_code": 401}, "timestamp": 1749997169.0833101, "datetime": "2025-06-15T22:19:29.083310", "module": "绑卡API测试", "module_key": "test_bind_card_api"}, {"test_name": "无效签名验证", "success": true, "message": "正确拒绝了无效签名", "details": {"status_code": 401}, "timestamp": 1749997169.094942, "datetime": "2025-06-15T22:19:29.094942", "module": "绑卡API测试", "module_key": "test_bind_card_api"}, {"test_name": "重复卡号验证", "success": false, "message": "未正确验证重复卡号，状态码: 401", "details": {"response": {"code": 1, "message": "签名验证失败", "data": null}}, "timestamp": 1749997169.1153696, "datetime": "2025-06-15T22:19:29.115369", "module": "绑卡API测试", "module_key": "test_bind_card_api"}, {"test_name": "缺少必填字段验证", "success": true, "message": "正确拒绝了缺少必填字段的请求", "details": {"status_code": 422}, "timestamp": 1749997169.121449, "datetime": "2025-06-15T22:19:29.121449", "module": "绑卡API测试", "module_key": "test_bind_card_api"}, {"test_name": "商户编码不匹配验证", "success": true, "message": "正确拒绝了不匹配的商户编码", "details": {"status_code": 400}, "timestamp": 1749997169.1293516, "datetime": "2025-06-15T22:19:29.129351", "module": "绑卡API测试", "module_key": "test_bind_card_api"}, {"test_name": "过期时间戳验证", "success": true, "message": "正确拒绝了过期时间戳", "details": {"status_code": 401}, "timestamp": 1749997169.1391997, "datetime": "2025-06-15T22:19:29.139199", "module": "绑卡API测试", "module_key": "test_bind_card_api"}, {"test_name": "管理员获取绑卡记录列表", "success": false, "message": "获取记录列表失败，状态码: 500", "details": {"response": {"code": 1, "message": "获取绑卡记录列表失败: 'pending' is not among the defined enum values. Enum name: cardstatus. Possible values: PENDING, PROCESSING, SUCCESS, ..., CANCELLED", "data": null}}, "timestamp": 1749997171.7467768, "datetime": "2025-06-15T22:19:31.746776", "module": "绑卡管理API测试", "module_key": "test_card_management_api"}, {"test_name": "商户获取绑卡记录列表", "success": false, "message": "商户获取记录列表失败，状态码: 500", "details": {"response": {"code": 1, "message": "获取绑卡记录列表失败: 'pending' is not among the defined enum values. Enum name: cardstatus. Possible values: PENDING, PROCESSING, SUCCESS, ..., CANCELLED", "data": null}}, "timestamp": 1749997171.7927904, "datetime": "2025-06-15T22:19:31.792790", "module": "绑卡管理API测试", "module_key": "test_card_management_api"}, {"test_name": "创建绑卡记录", "success": false, "message": "创建绑卡记录失败，状态码: 500", "details": {"response": {"code": 1, "message": "创建绑卡记录失败: 'created_by' is an invalid keyword argument for CardRecord", "data": null}}, "timestamp": 1749997171.819682, "datetime": "2025-06-15T22:19:31.819681", "module": "绑卡管理API测试", "module_key": "test_card_management_api"}, {"test_name": "获取绑卡统计", "success": true, "message": "成功获取绑卡统计信息", "details": {"merchant_id": 1}, "timestamp": 1749997171.887123, "datetime": "2025-06-15T22:19:31.887123", "module": "绑卡管理API测试", "module_key": "test_card_management_api"}, {"test_name": "批量创建绑卡记录", "success": false, "message": "批量创建失败，仅成功创建 0 个记录", "details": {"card_ids": []}, "timestamp": 1749997174.5845628, "datetime": "2025-06-15T22:19:34.584562", "module": "批量绑卡API测试", "module_key": "test_batch_bind_api"}, {"test_name": "按状态批量筛选", "success": true, "message": "状态筛选功能测试完成", "details": {"tested_statuses": ["pending", "success", "failed"]}, "timestamp": 1749997174.7811944, "datetime": "2025-06-15T22:19:34.781194", "module": "批量绑卡API测试", "module_key": "test_batch_bind_api"}, {"test_name": "按日期范围批量筛选", "success": true, "message": "成功筛选今日记录 0 条", "details": {"today_count": 0}, "timestamp": 1749997174.80291, "datetime": "2025-06-15T22:19:34.802910", "module": "批量绑卡API测试", "module_key": "test_batch_bind_api"}, {"test_name": "批量分页查询", "success": true, "message": "分页查询功能测试完成", "details": {"tested_page_sizes": [5, 10, 20]}, "timestamp": 1749997174.8689432, "datetime": "2025-06-15T22:19:34.868943", "module": "批量绑卡API测试", "module_key": "test_batch_bind_api"}, {"test_name": "管理员获取卡记录列表", "success": false, "message": "获取卡记录列表失败，状态码: 500", "details": {}, "timestamp": 1749997177.4196248, "datetime": "2025-06-15T22:19:37.419624", "module": "绑卡CRUD测试", "module_key": "test_cards_crud"}, {"test_name": "商户获取卡记录列表", "success": false, "message": "商户获取卡记录列表失败，状态码: 500", "details": {}, "timestamp": 1749997177.4512017, "datetime": "2025-06-15T22:19:37.451201", "module": "绑卡CRUD测试", "module_key": "test_cards_crud"}, {"test_name": "获取卡记录统计", "success": true, "message": "成功获取卡记录统计", "details": {}, "timestamp": 1749997177.4892273, "datetime": "2025-06-15T22:19:37.489227", "module": "绑卡CRUD测试", "module_key": "test_cards_crud"}, {"test_name": "获取今日卡记录统计", "success": true, "message": "成功获取今日卡记录统计", "details": {}, "timestamp": 1749997177.513381, "datetime": "2025-06-15T22:19:37.513381", "module": "绑卡CRUD测试", "module_key": "test_cards_crud"}, {"test_name": "卡记录数据隔离", "success": true, "message": "数据隔离正常，管理员看到 0 条，商户看到 0 条", "details": {}, "timestamp": 1749997177.631402, "datetime": "2025-06-15T22:19:37.631402", "module": "绑卡CRUD测试", "module_key": "test_cards_crud"}]}]