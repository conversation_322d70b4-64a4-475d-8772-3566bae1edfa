from sqlalchemy import Column, String, Text, Boolean

from app.models.base import BaseModel, TimestampMixin


class ConfigType:
    """配置类型常量"""
    STRING = "string"
    JSON = "json"
    BOOLEAN = "boolean"
    NUMBER = "number"

    @classmethod
    def get_valid_types(cls):
        """获取所有有效的配置类型"""
        return [cls.STRING, cls.JSON, cls.BOOLEAN, cls.NUMBER]


class TelegramBotConfig(BaseModel, TimestampMixin):
    """机器人配置表"""

    __tablename__ = "telegram_bot_configs"

    # 配置信息
    config_key = Column(
        String(100), 
        nullable=False, 
        unique=True,
        index=True,
        comment="配置键"
    )
    config_value = Column(
        Text, 
        nullable=False, 
        comment="配置值"
    )
    config_type = Column(
        String(20),
        nullable=False,
        default=ConfigType.STRING,
        comment="配置类型"
    )
    config_group = Column(
        String(50),
        nullable=True,
        comment="配置分组"
    )
    description = Column(
        Text,
        nullable=True,
        comment="配置描述"
    )
    is_encrypted = Column(
        Boolean,
        nullable=False,
        default=False,
        comment="是否加密存储"
    )
    is_system = Column(
        Boolean,
        nullable=False,
        default=False,
        comment="是否为系统配置"
    )

    def validate_config_type(self):
        """验证配置类型是否有效"""
        if self.config_type not in ConfigType.get_valid_types():
            raise ValueError(f"无效的配置类型: {self.config_type}. 有效类型: {ConfigType.get_valid_types()}")

    def to_dict(self):
        """转换为字典"""
        return {
            "id": self.id,
            "config_key": self.config_key,
            "config_value": self.get_parsed_value(),
            "config_type": self.config_type,
            "description": self.description,
            "is_encrypted": self.is_encrypted,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None,
        }

    def get_parsed_value(self):
        """获取解析后的配置值"""
        if self.is_encrypted:
            # 如果是加密存储，需要解密
            return self._decrypt_value(self.config_value)
        
        if self.config_type == ConfigType.JSON:
            import json
            try:
                return json.loads(self.config_value)
            except (json.JSONDecodeError, TypeError):
                return {}
        elif self.config_type == ConfigType.BOOLEAN:
            return self.config_value.lower() in ('true', '1', 'yes', 'on')
        elif self.config_type == ConfigType.NUMBER:
            try:
                # 尝试转换为整数
                if '.' not in self.config_value:
                    return int(self.config_value)
                # 转换为浮点数
                return float(self.config_value)
            except (ValueError, TypeError):
                return 0
        else:
            return self.config_value

    def set_value(self, value):
        """设置配置值"""
        if self.config_type == ConfigType.JSON:
            import json
            if isinstance(value, (dict, list)):
                self.config_value = json.dumps(value, ensure_ascii=False)
            else:
                self.config_value = str(value)
        elif self.config_type == ConfigType.BOOLEAN:
            self.config_value = str(bool(value)).lower()
        elif self.config_type == ConfigType.NUMBER:
            self.config_value = str(value)
        else:
            self.config_value = str(value)
        
        if self.is_encrypted:
            self.config_value = self._encrypt_value(self.config_value)

    def _encrypt_value(self, value):
        """加密配置值"""
        # 这里应该实现实际的加密逻辑
        # 为了简化，暂时返回原值
        # TODO: 实现AES加密
        return value

    def _decrypt_value(self, encrypted_value):
        """解密配置值"""
        # 这里应该实现实际的解密逻辑
        # 为了简化，暂时返回原值
        # TODO: 实现AES解密
        return encrypted_value

    @classmethod
    def get_config(cls, db_session, key, default=None):
        """获取配置值"""
        config = db_session.query(cls).filter_by(config_key=key).first()
        if config:
            return config.get_parsed_value()
        return default

    @classmethod
    def set_config(cls, db_session, key, value, config_type=ConfigType.STRING, description=None, is_encrypted=False):
        """设置配置值"""
        config = db_session.query(cls).filter_by(config_key=key).first()
        if config:
            config.config_type = config_type
            config.description = description or config.description
            config.is_encrypted = is_encrypted
            config.set_value(value)
        else:
            config = cls(
                config_key=key,
                config_type=config_type,
                description=description,
                is_encrypted=is_encrypted
            )
            config.set_value(value)
            db_session.add(config)
        
        db_session.commit()
        return config

    @classmethod
    def get_all_configs(cls, db_session, include_encrypted=False):
        """获取所有配置"""
        query = db_session.query(cls)
        if not include_encrypted:
            query = query.filter_by(is_encrypted=False)
        
        configs = {}
        for config in query.all():
            configs[config.config_key] = config.get_parsed_value()
        
        return configs

    @classmethod
    def init_default_configs(cls, db_session):
        """初始化默认配置"""
        default_configs = [
            {
                "key": "bot_token",
                "value": "",
                "type": ConfigType.STRING,
                "description": "Telegram Bot Token",
                "encrypted": True
            },
            {
                "key": "webhook_url",
                "value": "",
                "type": ConfigType.STRING,
                "description": "Webhook URL"
            },
            {
                "key": "webhook_secret",
                "value": "",
                "type": ConfigType.STRING,
                "description": "Webhook Secret Token",
                "encrypted": True
            },
            {
                "key": "rate_limit_global",
                "value": "1000",
                "type": ConfigType.NUMBER,
                "description": "全局每小时请求限制"
            },
            {
                "key": "rate_limit_group",
                "value": "100",
                "type": ConfigType.NUMBER,
                "description": "单群组每小时请求限制"
            },
            {
                "key": "rate_limit_user",
                "value": "50",
                "type": ConfigType.NUMBER,
                "description": "单用户每小时请求限制"
            },
            {
                "key": "bind_token_expire_hours",
                "value": "24",
                "type": ConfigType.NUMBER,
                "description": "绑定令牌有效期（小时）"
            },
            {
                "key": "verification_token_expire_minutes",
                "value": "30",
                "type": ConfigType.NUMBER,
                "description": "验证令牌有效期（分钟）"
            },
            {
                "key": "max_bind_attempts_per_day",
                "value": "5",
                "type": ConfigType.NUMBER,
                "description": "每日最大绑定尝试次数"
            },
            {
                "key": "enable_audit_log",
                "value": "true",
                "type": ConfigType.BOOLEAN,
                "description": "是否启用审计日志"
            },
            {
                "key": "mask_sensitive_data",
                "value": "true",
                "type": ConfigType.BOOLEAN,
                "description": "是否脱敏敏感数据"
            },
            {
                "key": "default_timezone",
                "value": "Asia/Shanghai",
                "type": ConfigType.STRING,
                "description": "默认时区"
            },
            {
                "key": "default_language",
                "value": "zh-CN",
                "type": ConfigType.STRING,
                "description": "默认语言"
            }
        ]

        for config_data in default_configs:
            existing = db_session.query(cls).filter_by(config_key=config_data["key"]).first()
            if not existing:
                cls.set_config(
                    db_session,
                    config_data["key"],
                    config_data["value"],
                    config_data["type"],
                    config_data["description"],
                    config_data.get("encrypted", False)
                )
