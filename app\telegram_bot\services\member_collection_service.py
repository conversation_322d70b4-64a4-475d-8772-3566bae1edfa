"""
群成员自动收集服务
通过消息监听自动收集群成员信息并创建验证申请
"""

from datetime import datetime, timedelta
from typing import Optional, Dict, Any
from sqlalchemy.orm import Session
from telegram import Update, User as TelegramUser
from telegram.ext import ContextTypes

from app.core.logging import get_logger
from app.models.base import local_now
from app.models.telegram_group import TelegramGroup
from app.models.telegram_user import TelegramUser as TelegramUserModel, VerificationStatus
from .user_verification_service import UserVerificationService
from ..config import BotConfig

logger = get_logger(__name__)


class MemberCollectionService:
    """群成员自动收集服务"""
    
    def __init__(self, db: Session, config: BotConfig):
        self.db = db
        self.config = config
        self.logger = logger
    
    def is_collection_enabled(self, chat_id: int) -> bool:
        """检查群组是否启用了成员收集模式"""
        try:
            group = self.db.query(TelegramGroup).filter_by(chat_id=chat_id).first()
            if not group or not group.settings:
                return False
            
            # 检查是否启用
            if not group.settings.get('member_collection_enabled', False):
                return False
            
            # 检查是否过期
            expire_time_str = group.settings.get('member_collection_expire_time')
            if expire_time_str:
                expire_time = datetime.fromisoformat(expire_time_str)
                if local_now() > expire_time:
                    # 过期了，自动关闭收集模式
                    self._disable_collection_mode(group)
                    return False
            
            return True
            
        except Exception as e:
            self.logger.error(f"检查成员收集模式状态失败: {e}")
            return False
    
    async def collect_member_from_message(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> bool:
        """从消息中收集群成员信息"""
        try:
            chat = update.effective_chat
            user = update.effective_user
            
            # 只处理群组消息
            if chat.type not in ['group', 'supergroup']:
                return False
            
            # 检查是否启用收集模式
            if not self.is_collection_enabled(chat.id):
                return False
            
            # 跳过机器人
            if user.is_bot:
                return False
            
            # 获取群组信息
            group = self.db.query(TelegramGroup).filter_by(chat_id=chat.id).first()
            if not group:
                return False
            
            # 检查用户是否已经有验证记录
            existing_user = self.db.query(TelegramUserModel).filter_by(
                telegram_user_id=user.id
            ).first()
            
            if existing_user:
                # 如果已经验证，跳过
                if existing_user.verification_status == VerificationStatus.VERIFIED.value:
                    return False
                
                # 如果已经有待审核的申请，跳过
                if existing_user.verification_status == VerificationStatus.PENDING.value:
                    return False
            
            # 获取用户在群组中的角色信息
            try:
                chat_member = await context.bot.get_chat_member(chat.id, user.id)
                member_status = chat_member.status
                member_role = {
                    'creator': '群主',
                    'administrator': '管理员',
                    'member': '群成员',
                    'restricted': '受限成员',
                    'left': '已离开',
                    'kicked': '已被踢出'
                }.get(member_status, '未知')
            except Exception as e:
                self.logger.warning(f"获取用户 {user.id} 群组角色失败: {e}")
                member_status = 'member'
                member_role = '群成员'
            
            # 创建验证申请
            verification_service = UserVerificationService(self.db, self.config)
            verification_token = await verification_service.create_verification_request(
                telegram_user_id=user.id,
                telegram_username=user.username,
                telegram_first_name=user.first_name,
                telegram_last_name=user.last_name,
                additional_info={
                    'group_id': chat.id,
                    'group_title': chat.title,
                    'member_status': member_status,
                    'member_role': member_role,
                    'auto_collected': True,  # 标记为自动收集
                    'collection_time': local_now().isoformat(),
                    'merchant_id': group.merchant_id,
                    'department_id': group.department_id,
                    'message_text': update.message.text[:100] if update.message and update.message.text else None  # 保存触发消息的前100个字符
                }
            )
            
            self.logger.info(f"自动收集群成员 {user.id} ({user.first_name}) 并创建验证申请: {verification_token}")
            
            # 可选：发送私聊通知给用户
            await self._send_collection_notification(user, verification_token, group, context)
            
            return True
            
        except Exception as e:
            self.logger.error(f"自动收集群成员失败: {e}")
            return False
    
    async def _send_collection_notification(self, user: TelegramUser, verification_token: str, group: TelegramGroup, context: ContextTypes.DEFAULT_TYPE):
        """发送收集通知给用户（私聊）"""
        try:
            notification_message = f"""🎉 **自动验证申请已创建**

您好 {user.first_name}！

✅ **系统已自动为您创建身份验证申请**：
• 群组：{group.chat_title}
• 验证令牌：`{verification_token}`
• 申请时间：{local_now().strftime('%Y-%m-%d %H:%M:%S')}

📋 **申请状态**：等待管理员审核

💡 **无需额外操作**：
• 系统已自动收集您的群成员信息
• 管理员审核通过后您将收到通知
• 审核通过后可使用统计查询功能

🔍 **可用功能**（审核通过后）：
• 输入关键词查询统计（如：CK今日、昨日数据等）
• 输入 `/help` 查看帮助信息

⏰ **预计审核时间**：1-2个工作日"""

            await context.bot.send_message(
                chat_id=user.id,
                text=notification_message,
                parse_mode='Markdown'
            )
            
        except Exception as e:
            # 私聊发送失败是正常的（用户可能没有与机器人开始对话）
            self.logger.debug(f"发送收集通知失败（用户可能未与机器人对话）: {e}")
    
    def _disable_collection_mode(self, group: TelegramGroup):
        """关闭群组的成员收集模式"""
        try:
            if group.settings:
                group.settings['member_collection_enabled'] = False
                group.settings['member_collection_disabled_time'] = local_now().isoformat()
                self.db.commit()
                
                self.logger.info(f"群组 {group.chat_id} 成员收集模式已关闭")
                
        except Exception as e:
            self.logger.error(f"关闭成员收集模式失败: {e}")
    
    def get_collection_stats(self, chat_id: int) -> Dict[str, Any]:
        """获取群组成员收集统计"""
        try:
            group = self.db.query(TelegramGroup).filter_by(chat_id=chat_id).first()
            if not group:
                return {"error": "群组不存在"}
            
            # 统计自动收集的用户数量
            auto_collected_count = self.db.query(TelegramUserModel).filter(
                TelegramUserModel.additional_info.contains('"auto_collected": true')
            ).count()
            
            # 获取收集模式信息
            collection_info = {}
            if group.settings:
                collection_info = {
                    'enabled': group.settings.get('member_collection_enabled', False),
                    'start_time': group.settings.get('member_collection_start_time'),
                    'expire_time': group.settings.get('member_collection_expire_time'),
                    'disabled_time': group.settings.get('member_collection_disabled_time')
                }
            
            return {
                'group_id': chat_id,
                'group_title': group.chat_title,
                'auto_collected_count': auto_collected_count,
                'collection_info': collection_info
            }
            
        except Exception as e:
            self.logger.error(f"获取收集统计失败: {e}")
            return {"error": str(e)}


# 创建全局实例
_member_collection_service = None


def get_member_collection_service(db: Session, config: BotConfig) -> MemberCollectionService:
    """获取群成员收集服务实例"""
    global _member_collection_service
    if _member_collection_service is None:
        _member_collection_service = MemberCollectionService(db, config)
    return _member_collection_service
