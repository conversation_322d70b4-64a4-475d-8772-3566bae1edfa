# CK 错误处理增强 - 自动禁用和重试

## 🎯 **需求实现**

当绑卡时底层 API 返回特定错误时，系统会自动：

1. **禁用当前 CK**
2. **重试绑卡操作**（使用新的 CK）

## 🔧 **新增错误处理**

### ✅ **支持的错误码**

#### 1. 错误码 110134 - "错误次数过多,请稍后再试"

```json
{
  "data": null,
  "error": {
    "message": "错误次数过多,请稍后再试",
    "redirect": null,
    "errorcode": 110134,
    "validators": null
  },
  "logId": "bXDCa5wO",
  "status": false
}
```

#### 2. 错误码 110444 - "数据异常"

```json
{
  "data": null,
  "error": {
    "message": "数据异常",
    "redirect": null,
    "errorcode": 110444,
    "validators": null
  },
  "logId": "tBfNrIjd",
  "status": false
}
```

### ✅ **已有的错误处理**

#### 3. 错误码 203 - "请先去登录"

- **处理方式**: 禁用 CK 并重试

#### 4. 错误码 110224 - "您绑卡已超过单日 20 张限制"

- **处理方式**: 禁用 CK 并重试

#### 5. 错误码 200 - 各种服务器错误

- **处理方式**: 禁用 CK 并重试
- **说明**: 只需要判断状态码 200，因为可能有多种错误信息（如"服务器繁忙"等）

## 🔧 **技术实现**

### 1. 错误检测增强

**文件**: `app/core/walmart_api.py`

```python
# 【新增】错误次数过多，需要禁用当前CK并重试
if error_code == 110134 and "错误次数过多" in error_message:
    logger.warning("CK错误次数过多，将禁用当前CK并重试")
    return True

# 【新增】数据异常错误，需要禁用当前CK并重试
if error_code == 110444 and "数据异常" in error_message:
    logger.warning("CK数据异常，将禁用当前CK并重试")
    return True

# 【新增】错误码200错误，需要禁用当前CK并重试
# 只需要判断状态码，因为可能有多种错误信息
if error_code == 200:
    logger.warning(f"检测到错误码200，将禁用当前CK并重试: {error_message}")
    return True
```

### 2. 重试逻辑增强

**文件**: `app/services/retry_logic_service.py`

```python
def _is_ck_disable_error(self, api_response_data: Dict[str, Any], error_message: str) -> bool:
    """判断是否是需要禁用CK的错误（错误次数过多、数据异常）"""
    if not api_response_data:
        return False

    error_code = api_response_data.get("error", {}).get("errorcode")

    # 检查是否是需要禁用CK的错误码
    is_disable_error = (
        (error_code == 110134 and "错误次数过多" in error_message) or
        (error_code == 110444 and "数据异常" in error_message) or
        (error_code == 200)  # 错误码200只需要判断状态码，可能有多种错误信息
    )

    return is_disable_error

def _get_ck_disable_error_type(self, api_response_data: Dict[str, Any], error_message: str) -> str:
    """获取需要禁用CK的错误类型"""
    error_code = api_response_data.get("error", {}).get("errorcode")

    if error_code == 110134:
        return "错误次数过多"
    elif error_code == 110444:
        return "数据异常"
    elif error_code == 200 and "服务器繁忙" in error_message:
        return "服务器繁忙"
    else:
        return "未知CK错误"
```

### 3. 业务失败处理增强

```python
async def handle_business_failure(self, db, api_business_result, walmart_ck, record, attempt):
    """处理业务失败情况"""
    error_message = api_business_result.get("error", "")
    raw_response = api_business_result.get("raw_response", {})

    # 检查是否是CK失效错误
    is_ck_invalid = self._is_ck_invalid_error(raw_response)

    # 检查是否是单日绑卡限制错误
    is_daily_limit_error = self._is_daily_limit_error(raw_response, error_message)

    # 【新增】检查是否是需要禁用CK的错误（错误次数过多、数据异常）
    is_ck_disable_error = self._is_ck_disable_error(raw_response, error_message)

    if is_ck_invalid:
        # CK失效的情况，禁用CK并尝试重试
        retry_result = await self._handle_ck_invalid_retry(db, walmart_ck, record, attempt, "需要登录")
        return retry_result["should_retry"]
    elif is_daily_limit_error:
        # 单日绑卡限制的情况，禁用CK并尝试重试
        retry_result = await self._handle_ck_invalid_retry(db, walmart_ck, record, attempt, "单日绑卡限制")
        return retry_result["should_retry"]
    elif is_ck_disable_error:
        # 【新增】需要禁用CK的错误，禁用CK并尝试重试
        error_type = self._get_ck_disable_error_type(raw_response, error_message)
        retry_result = await self._handle_ck_invalid_retry(db, walmart_ck, record, attempt, error_type)
        return retry_result["should_retry"]
    elif is_server_busy_error:
        # 【新增】服务器繁忙错误，禁用CK并尝试重试
        retry_result = await self._handle_ck_invalid_retry(db, walmart_ck, record, attempt, "服务器繁忙")
        return retry_result["should_retry"]
    else:
        # 其他失败情况，记录错误但不禁用CK，也不重试
        await self._log_non_ck_error(db, walmart_ck, record, error_message)
        return False
```

### 4. 日志记录增强

```python
# 改进日志记录，明确区分不同类型的CK失效
if "请先去登录" in reason or "203" in reason:
    logger.warning(f"CK无效需要重新登录(errorcode=203)，禁用CK {walmart_ck.id} 并尝试获取新CK")
elif "单日绑卡限制" in reason:
    logger.warning(f"CK达到单日绑卡限制(errorcode=110224)，禁用CK {walmart_ck.id} 并尝试获取新CK")
elif "错误次数过多" in reason:
    logger.warning(f"CK错误次数过多(errorcode=110134)，禁用CK {walmart_ck.id} 并尝试获取新CK")
elif "数据异常" in reason:
    logger.warning(f"CK数据异常(errorcode=110444)，禁用CK {walmart_ck.id} 并尝试获取新CK")
elif "服务器繁忙" in reason:
    logger.warning(f"服务器繁忙(errorcode=200)，禁用CK {walmart_ck.id} 并尝试获取新CK")
```

### 5. CK 状态管理增强

**文件**: `app/services/walmart_api_service.py`

```python
# 【修复】处理不同类型的限制情况
if error_code == 110224 and "您绑卡已超过单日20张限制" in error_msg:
    # 单日绑卡限制 - 禁用CK
    logger.warning(f"用户 {walmart_ck.id} 达到单日绑卡限制，禁用CK等待明天重新启用")
    walmart_ck.active = False
    db.add(walmart_ck)
    db.commit()
elif error_code == 110134 and "错误次数过多" in error_msg:
    # 错误次数过多 - 禁用CK
    logger.warning(f"用户 {walmart_ck.id} 错误次数过多，禁用CK")
    walmart_ck.active = False
    db.add(walmart_ck)
    db.commit()
elif error_code == 110444 and "数据异常" in error_msg:
    # 数据异常 - 禁用CK
    logger.warning(f"用户 {walmart_ck.id} 数据异常，禁用CK")
    walmart_ck.active = False
    db.add(walmart_ck)
    db.commit()
elif error_code == 200:
    # 【新增】错误码200 - 禁用CK（可能有多种错误信息）
    logger.warning(f"用户 {walmart_ck.id} 遇到错误码200，禁用CK: {error_msg}")
    walmart_ck.active = False
    db.add(walmart_ck)
    db.commit()
```

## 🎯 **处理流程**

### 绑卡错误处理流程

```
1. 绑卡API调用
   ↓
2. 收到错误响应（110134、110444或200）
   ↓
3. 错误检测：识别为需要禁用CK的错误
   ↓
4. 禁用当前CK：设置 active = False
   ↓
5. 获取新的可用CK
   ↓
6. 使用新CK重试绑卡
   ↓
7. 记录重试日志
```

### 错误分类处理

#### 🔄 **需要重试的错误**

- ✅ **203** - 请先去登录（CK 失效）
- ✅ **110224** - 单日绑卡限制
- ✅ **110134** - 错误次数过多 **（新增）**
- ✅ **110444** - 数据异常 **（新增）**

#### ❌ **不需要重试的错误**

- ❌ **10131** - 卡已被其他用户绑定
- ❌ **5042** - 需要重新登录（但不重试）
- ❌ **5041** - 需要打开特定页面
- ❌ **506** - 需要切换到首页
- ❌ **110445** - 请求过期
- ❌ **9999** - 需要隐式登录

## 📊 **日志示例**

### 错误次数过多（110134）

```
2025-01-19 15:30:25 [WARNING] CK错误次数过多(errorcode=110134)，禁用CK 123 并尝试获取新CK (下一次尝试 2)
2025-01-19 15:30:26 [INFO] 切换到新的CK用户: 124，准备第 2 次重试
2025-01-19 15:30:27 [INFO] 沃尔玛API调用成功 (尝试 2, 用户ID: 124)
```

### 数据异常（110444）

```
2025-01-19 15:30:25 [WARNING] CK数据异常(errorcode=110444)，禁用CK 125 并尝试获取新CK (下一次尝试 2)
2025-01-19 15:30:26 [INFO] 切换到新的CK用户: 126，准备第 2 次重试
2025-01-19 15:30:27 [INFO] 沃尔玛API调用成功 (尝试 2, 用户ID: 126)
```

## ✅ **功能特性**

### 1. 智能错误识别

- ✅ **精确匹配**: 同时检查错误码和错误消息
- ✅ **多层检测**: 检查原始响应和处理后的响应
- ✅ **错误分类**: 区分不同类型的 CK 错误

### 2. 自动 CK 管理

- ✅ **即时禁用**: 检测到错误立即禁用问题 CK
- ✅ **智能切换**: 自动获取新的可用 CK
- ✅ **状态同步**: 更新数据库和缓存状态

### 3. 重试机制

- ✅ **自动重试**: 禁用 CK 后自动使用新 CK 重试
- ✅ **重试限制**: 最大重试次数控制
- ✅ **重试延迟**: 避免频繁重试

### 4. 日志记录

- ✅ **详细日志**: 记录错误类型、CK ID、重试次数
- ✅ **分类记录**: 区分不同错误类型的日志
- ✅ **追踪信息**: 包含 trace_id 便于问题追踪

## 🚀 **使用效果**

### 修改前 ❌

```
绑卡请求 → API返回110134错误 → 绑卡失败 → 不重试
绑卡请求 → API返回110444错误 → 绑卡失败 → 不重试
```

### 修改后 ✅

```
绑卡请求 → API返回110134错误 → 禁用CK → 获取新CK → 重试绑卡 → 成功
绑卡请求 → API返回110444错误 → 禁用CK → 获取新CK → 重试绑卡 → 成功
```

现在系统能够智能处理这两种新的错误情况，自动禁用有问题的 CK 并使用新的 CK 重试，大大提高了绑卡成功率！🎉
