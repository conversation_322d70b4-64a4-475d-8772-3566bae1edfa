<template>
  <div class="group-detail">
    <!-- 返回按钮 -->
    <div class="page-header">
      <el-button @click="$router.back()">
        <el-icon>
          <ArrowLeft />
        </el-icon>
        返回群组列表
      </el-button>
    </div>

    <!-- 群组基本信息 -->
    <el-card class="info-card">
      <template #header>
        <div class="card-header">
          <span>群组信息</span>
          <el-button type="primary" size="small" :loading="loading" @click="refreshGroupInfo">
            刷新
          </el-button>
        </div>
      </template>

      <div v-loading="loading" class="group-info">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="群组ID">
            {{ groupInfo.chat_id || '-' }}
          </el-descriptions-item>
          <el-descriptions-item label="群组名称">
            {{ groupInfo.chat_title || '-' }}
          </el-descriptions-item>
          <el-descriptions-item label="群组类型">
            {{ getGroupTypeText(groupInfo.chat_type) }}
          </el-descriptions-item>
          <el-descriptions-item label="绑定状态">
            <el-tag :type="getStatusTagType(groupInfo.bind_status)">
              {{ getStatusText(groupInfo.bind_status) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="绑定商户">
            {{ groupInfo.merchant_name || '-' }}
          </el-descriptions-item>
          <el-descriptions-item label="绑定部门">
            {{ groupInfo.department_name || '-' }}
          </el-descriptions-item>
          <el-descriptions-item label="绑定时间">
            {{ groupInfo.bind_time ? formatDateTime(groupInfo.bind_time) : '-' }}
          </el-descriptions-item>
          <el-descriptions-item label="最后活跃">
            {{ groupInfo.last_active_time ? formatDateTime(groupInfo.last_active_time) : '-' }}
          </el-descriptions-item>
          <el-descriptions-item label="绑定用户">
            {{ groupInfo.bind_user_name || '-' }}
          </el-descriptions-item>
          <el-descriptions-item label="成员数量">
            {{ groupInfo.member_count || '-' }}
          </el-descriptions-item>
        </el-descriptions>
      </div>
    </el-card>

    <!-- 绑定令牌信息 -->
    <el-card v-if="shouldShowBindToken" class="token-card">
      <template #header>
        <div class="card-header">
          <span>绑定令牌</span>
          <div class="header-actions">
            <el-button v-if="canResetToken" type="warning" size="small" :loading="resetTokenLoading"
              @click="handleResetToken">
              重新生成令牌
            </el-button>
          </div>
        </div>
      </template>

      <div class="token-info">
        <el-alert :title="getTokenAlertTitle" :type="getTokenAlertType" show-icon :closable="false"
          class="token-alert" />

        <div v-if="groupInfo.bind_token" class="token-content">
          <div class="token-display">
            <h4>绑定令牌：</h4>
            <el-input :value="showFullToken ? groupInfo.bind_token : maskedToken" readonly class="token-input">
              <template #append>
                <el-button @click="toggleTokenVisibility">
                  {{ showFullToken ? '隐藏' : '显示' }}
                </el-button>
              </template>
            </el-input>
          </div>

          <div class="bind-command">
            <h4>绑定命令：</h4>
            <el-input :value="bindCommand" readonly class="command-input">
              <template #append>
                <el-button type="primary" @click="copyBindCommand">
                  复制命令
                </el-button>
              </template>
            </el-input>
          </div>

          <div class="usage-instructions">
            <h4>使用说明：</h4>
            <ol>
              <li>将机器人 @{{ botUsername }} 添加到群组</li>
              <li>在群组中发送上述绑定命令</li>
              <li>等待机器人确认绑定成功</li>
            </ol>
          </div>
        </div>
      </div>
    </el-card>

    <!-- 群组配置 -->
    <el-card class="config-card">
      <template #header>
        <div class="card-header">
          <span>群组配置</span>
          <el-button type="primary" size="small" @click="showConfigDialog = true">
            编辑配置
          </el-button>
        </div>
      </template>

      <div class="group-config">
        <el-row :gutter="20">
          <el-col :span="12">
            <h4>权限设置</h4>
            <el-descriptions :column="1" size="small">
              <el-descriptions-item label="允许所有成员">
                <el-tag :type="groupConfig.permissions?.allow_all_members ? 'success' : 'danger'">
                  {{ groupConfig.permissions?.allow_all_members ? '是' : '否' }}
                </el-tag>
              </el-descriptions-item>
              <el-descriptions-item label="需要用户验证">
                <el-tag :type="groupConfig.permissions?.require_user_verification ? 'success' : 'danger'">
                  {{ groupConfig.permissions?.require_user_verification ? '是' : '否' }}
                </el-tag>
              </el-descriptions-item>
              <el-descriptions-item label="每分钟命令限制">
                {{ groupConfig.permissions?.rate_limit?.commands_per_minute || '-' }}
              </el-descriptions-item>
              <el-descriptions-item label="每小时查询限制">
                {{ groupConfig.permissions?.rate_limit?.queries_per_hour || '-' }}
              </el-descriptions-item>
            </el-descriptions>
          </el-col>

          <el-col :span="12">
            <h4>显示设置</h4>
            <el-descriptions :column="1" size="small">
              <el-descriptions-item label="显示金额">
                <el-tag :type="groupConfig.display_settings?.show_amount ? 'success' : 'danger'">
                  {{ groupConfig.display_settings?.show_amount ? '是' : '否' }}
                </el-tag>
              </el-descriptions-item>
              <el-descriptions-item label="显示详情">
                <el-tag :type="groupConfig.display_settings?.show_details ? 'success' : 'danger'">
                  {{ groupConfig.display_settings?.show_details ? '是' : '否' }}
                </el-tag>
              </el-descriptions-item>
              <el-descriptions-item label="时区">
                {{ groupConfig.display_settings?.timezone || '-' }}
              </el-descriptions-item>
              <el-descriptions-item label="语言">
                {{ groupConfig.display_settings?.language || '-' }}
              </el-descriptions-item>
            </el-descriptions>
          </el-col>
        </el-row>
      </div>
    </el-card>

    <!-- 统计数据 -->
    <el-card class="stats-card">
      <template #header>
        <div class="card-header">
          <span>使用统计</span>
          <el-button type="primary" size="small" :loading="statsLoading" @click="refreshStats">
            刷新
          </el-button>
        </div>
      </template>

      <div v-loading="statsLoading" class="group-stats">
        <el-row :gutter="20">
          <el-col :span="6">
            <div class="stat-item">
              <div class="stat-value">{{ groupStats.total_commands || 0 }}</div>
              <div class="stat-label">总命令数</div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="stat-item">
              <div class="stat-value">{{ groupStats.today_commands || 0 }}</div>
              <div class="stat-label">今日命令</div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="stat-item">
              <div class="stat-value">{{ groupStats.active_users || 0 }}</div>
              <div class="stat-label">活跃用户</div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="stat-item">
              <div class="stat-value">{{ (groupStats.success_rate || 0).toFixed(1) }}%</div>
              <div class="stat-label">成功率</div>
            </div>
          </el-col>
        </el-row>
      </div>
    </el-card>

    <!-- 最近活动 -->
    <el-card class="activity-card">
      <template #header>
        <span>最近活动</span>
      </template>

      <el-table :data="recentActivities" v-loading="activitiesLoading" stripe size="small">
        <el-table-column prop="user_name" label="用户" width="120" />
        <el-table-column prop="command" label="命令" width="100" />
        <el-table-column prop="message" label="内容" min-width="200" />
        <el-table-column prop="status" label="状态" width="80">
          <template #default="{ row }">
            <el-tag :type="row.status === 'success' ? 'success' : 'danger'" size="small">
              {{ row.status === 'success' ? '成功' : '失败' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="created_at" label="时间" width="160">
          <template #default="{ row }">
            {{ formatDateTime(row.created_at) }}
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <!-- 配置编辑对话框 -->
    <el-dialog v-model="showConfigDialog" title="编辑群组配置" width="800px">
      <el-form ref="configFormRef" :model="editConfigForm" label-width="150px">
        <!-- 权限设置 -->
        <el-divider content-position="left">权限设置</el-divider>

        <el-form-item label="允许所有成员">
          <el-switch v-model="editConfigForm.permissions.allow_all_members" active-text="是" inactive-text="否" />
        </el-form-item>

        <el-form-item label="需要用户验证">
          <el-switch v-model="editConfigForm.permissions.require_user_verification" active-text="是" inactive-text="否" />
        </el-form-item>

        <el-form-item label="每分钟命令限制">
          <el-input-number v-model="editConfigForm.permissions.rate_limit.commands_per_minute" :min="1" :max="100"
            controls-position="right" />
        </el-form-item>

        <el-form-item label="每小时查询限制">
          <el-input-number v-model="editConfigForm.permissions.rate_limit.queries_per_hour" :min="1" :max="1000"
            controls-position="right" />
        </el-form-item>

        <!-- 显示设置 -->
        <el-divider content-position="left">显示设置</el-divider>

        <el-form-item label="显示金额">
          <el-switch v-model="editConfigForm.display_settings.show_amount" active-text="是" inactive-text="否" />
        </el-form-item>

        <el-form-item label="显示详情">
          <el-switch v-model="editConfigForm.display_settings.show_details" active-text="是" inactive-text="否" />
        </el-form-item>

        <el-form-item label="时区">
          <el-select v-model="editConfigForm.display_settings.timezone" placeholder="请选择时区">
            <el-option label="Asia/Shanghai" value="Asia/Shanghai" />
            <el-option label="Asia/Tokyo" value="Asia/Tokyo" />
            <el-option label="UTC" value="UTC" />
          </el-select>
        </el-form-item>

        <el-form-item label="语言">
          <el-select v-model="editConfigForm.display_settings.language" placeholder="请选择语言">
            <el-option label="简体中文" value="zh-CN" />
            <el-option label="English" value="en-US" />
          </el-select>
        </el-form-item>
      </el-form>

      <template #footer>
        <el-button @click="showConfigDialog = false">取消</el-button>
        <el-button type="primary" :loading="saveConfigLoading" @click="handleSaveConfig">
          保存
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { useRoute } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { ArrowLeft } from '@element-plus/icons-vue'
import { telegramApi } from '@/api'
import { copyToClipboard } from '@/utils/clipboard'
import { useTelegramStore } from '@/store/modules/telegram'

const route = useRoute()
const groupId = route.params.id
const telegramStore = useTelegramStore()

// 数据状态
const loading = ref(false)
const statsLoading = ref(false)
const activitiesLoading = ref(false)
const saveConfigLoading = ref(false)
const resetTokenLoading = ref(false)

// 令牌显示状态
const showFullToken = ref(false)

// 群组信息
const groupInfo = ref({})
const groupConfig = ref({})
const groupStats = ref({})
const recentActivities = ref([])

// 对话框状态
const showConfigDialog = ref(false)
const configFormRef = ref()

// 编辑配置表单
const editConfigForm = reactive({
  permissions: {
    allow_all_members: false,
    require_user_verification: true,
    rate_limit: {
      commands_per_minute: 10,
      queries_per_hour: 100
    }
  },
  display_settings: {
    show_amount: true,
    show_details: false,
    timezone: 'Asia/Shanghai',
    language: 'zh-CN'
  }
})

// 计算属性
const botUsername = computed(() => telegramStore.botInfo?.username || 'your_bot')

const shouldShowBindToken = computed(() => {
  const status = groupInfo.value.bind_status
  return ['pending', 'failed'].includes(status)
})

const canResetToken = computed(() => {
  const status = groupInfo.value.bind_status
  return ['failed', 'suspended'].includes(status)
})

const maskedToken = computed(() => {
  const token = groupInfo.value.bind_token
  if (!token) return ''
  if (token.length <= 10) return token
  return token.substring(0, 6) + '****' + token.substring(token.length - 4)
})

const bindCommand = computed(() => {
  const token = groupInfo.value.bind_token
  return token ? `/bind ${token}` : ''
})

const getTokenAlertTitle = computed(() => {
  const status = groupInfo.value.bind_status
  switch (status) {
    case 'pending':
      return '群组等待绑定，请使用下方命令在Telegram群组中完成绑定'
    case 'failed':
      return '群组绑定失败，请重新尝试绑定或重新生成令牌'
    default:
      return '绑定信息'
  }
})

const getTokenAlertType = computed(() => {
  const status = groupInfo.value.bind_status
  switch (status) {
    case 'pending':
      return 'warning'
    case 'failed':
      return 'error'
    default:
      return 'info'
  }
})

// 工具函数
const getGroupTypeText = (type) => {
  const typeMap = {
    'group': '群组',
    'supergroup': '超级群组',
    'channel': '频道'
  }
  return typeMap[type] || type
}

const getStatusTagType = (status) => {
  const typeMap = {
    'pending': 'warning',
    'active': 'success',
    'inactive': 'info',
    'failed': 'danger'
  }
  return typeMap[status] || 'info'
}

const getStatusText = (status) => {
  const textMap = {
    'pending': '待绑定',
    'active': '已绑定',
    'inactive': '已解绑',
    'failed': '绑定失败'
  }
  return textMap[status] || status
}

const formatDateTime = (dateTime) => {
  if (!dateTime) return '-'
  return new Date(dateTime).toLocaleString('zh-CN')
}

// 刷新群组信息
const refreshGroupInfo = async () => {
  try {
    loading.value = true
    const response = await telegramApi.getGroupDetail(groupId)
    groupInfo.value = response.data || response
  } catch (error) {
    ElMessage.error('获取群组信息失败：' + error.message)
  } finally {
    loading.value = false
  }
}

// 刷新统计数据
const refreshStats = async () => {
  try {
    statsLoading.value = true
    const response = await telegramApi.getGroupStats(groupId)
    groupStats.value = response.data || response
  } catch (error) {
    ElMessage.error('获取统计数据失败：' + error.message)
  } finally {
    statsLoading.value = false
  }
}

// 加载群组配置
const loadGroupConfig = async () => {
  try {
    const response = await telegramApi.getGroupConfig(groupId)
    groupConfig.value = response.data || response

    // 更新编辑表单
    Object.assign(editConfigForm, groupConfig.value)
  } catch (error) {
    ElMessage.error('获取群组配置失败：' + error.message)
  }
}

// 保存配置
const handleSaveConfig = async () => {
  try {
    saveConfigLoading.value = true
    await telegramApi.updateGroupConfig(groupId, editConfigForm)

    // 刷新配置
    await loadGroupConfig()

    showConfigDialog.value = false
    ElMessage.success('配置保存成功')
  } catch (error) {
    ElMessage.error('保存配置失败：' + error.message)
  } finally {
    saveConfigLoading.value = false
  }
}

// 加载最近活动
const loadRecentActivities = async () => {
  try {
    activitiesLoading.value = true
    const response = await telegramApi.getUserLogs(groupId, {
      page: 1,
      page_size: 10,
      group_id: groupId
    })
    recentActivities.value = response.data?.items || response.items || []
  } catch (error) {
    ElMessage.error('获取活动记录失败：' + error.message)
  } finally {
    activitiesLoading.value = false
  }
}

// 令牌相关方法
const toggleTokenVisibility = () => {
  showFullToken.value = !showFullToken.value
}

const copyBindCommand = async () => {
  const command = bindCommand.value
  if (!command) {
    ElMessage.warning('没有可复制的绑定命令')
    return
  }

  try {
    await copyToClipboard(command, {
      successMessage: '绑定命令已复制到剪贴板',
      errorMessage: '复制失败，请手动复制'
    })
  } catch (error) {
    console.error('复制失败:', error)
  }
}

const handleResetToken = async () => {
  try {
    await ElMessageBox.confirm(
      `确定要重置群组 "${groupInfo.value.chat_title}" 的绑定令牌吗？重置后可以重新使用原令牌进行绑定。`,
      '确认重置令牌',
      { type: 'warning' }
    )

    resetTokenLoading.value = true
    await telegramApi.resetBindToken(groupId)
    ElMessage.success('令牌重置成功')

    // 刷新群组信息
    await refreshGroupInfo()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('重置令牌失败：' + error.message)
    }
  } finally {
    resetTokenLoading.value = false
  }
}

// 初始化
onMounted(async () => {
  try {
    await Promise.all([
      refreshGroupInfo(),
      loadGroupConfig(),
      refreshStats(),
      loadRecentActivities(),
      telegramStore.fetchBotInfo()
    ])
  } catch (error) {
    console.error('初始化失败:', error)
  }
})
</script>

<style scoped>
.group-detail {
  padding: 20px;
}

.page-header {
  margin-bottom: 20px;
}

.info-card,
.token-card,
.config-card,
.stats-card,
.activity-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.group-config h4 {
  margin: 0 0 16px 0;
  color: #333;
}

.stat-item {
  text-align: center;
  padding: 20px;
  border: 1px solid #eee;
  border-radius: 4px;
}

.stat-value {
  font-size: 24px;
  font-weight: bold;
  color: #409EFF;
  margin-bottom: 8px;
}

.stat-label {
  font-size: 14px;
  color: #666;
}

/* 令牌卡片样式 */
.token-card .header-actions {
  display: flex;
  gap: 8px;
}

.token-alert {
  margin-bottom: 20px;
}

.token-content h4 {
  margin: 16px 0 8px 0;
  color: #333;
  font-size: 14px;
}

.token-content h4:first-child {
  margin-top: 0;
}

.token-input,
.command-input {
  margin-bottom: 16px;
}

.usage-instructions {
  background: #f5f7fa;
  padding: 16px;
  border-radius: 4px;
  border-left: 4px solid #409EFF;
}

.usage-instructions h4 {
  margin-top: 0 !important;
  margin-bottom: 12px !important;
  color: #409EFF;
}

.usage-instructions ol {
  margin: 0;
  padding-left: 20px;
}

.usage-instructions li {
  margin-bottom: 8px;
  line-height: 1.5;
}
</style>
