<template>
  <div class="permission-test">
    <el-card shadow="never">
      <template #header>
        <div class="card-header">
          <span>权限系统测试页面</span>
        </div>
      </template>

      <!-- 用户信息展示 -->
      <el-row :gutter="20">
        <el-col :span="12">
          <el-card shadow="never" class="info-card">
            <template #header>
              <span>当前用户信息</span>
            </template>
            <el-descriptions :column="1" border>
              <el-descriptions-item label="用户名">{{ userInfo?.username || '-' }}</el-descriptions-item>
              <el-descriptions-item label="姓名">{{ userInfo?.full_name || '-' }}</el-descriptions-item>
              <el-descriptions-item label="邮箱">{{ userInfo?.email || '-' }}</el-descriptions-item>
              <el-descriptions-item label="是否超级用户">
                <el-tag :type="userInfo?.is_superuser ? 'success' : 'info'">
                  {{ userInfo?.is_superuser ? '是' : '否' }}
                </el-tag>
              </el-descriptions-item>
              <el-descriptions-item label="商户ID">{{ userInfo?.merchant_id || '-' }}</el-descriptions-item>
              <el-descriptions-item label="部门ID">{{ userInfo?.department_id || '-' }}</el-descriptions-item>
              <el-descriptions-item label="角色">
                <div v-if="userInfo?.roles && userInfo.roles.length > 0">
                  <el-tag v-for="role in userInfo.roles" :key="role.id" class="role-tag">
                    {{ role.name }} ({{ role.code }})
                  </el-tag>
                </div>
                <span v-else>-</span>
              </el-descriptions-item>
              <el-descriptions-item label="菜单权限">
                <div v-if="userInfo?.menus && userInfo.menus.length > 0">
                  <el-tag v-for="menu in userInfo.menus" :key="menu" size="small" class="menu-tag">
                    {{ menu }}
                  </el-tag>
                </div>
                <span v-else>-</span>
              </el-descriptions-item>
            </el-descriptions>
          </el-card>
        </el-col>

        <el-col :span="12">
          <el-card shadow="never" class="info-card">
            <template #header>
              <span>权限检查结果</span>
            </template>
            <el-descriptions :column="1" border>
              <el-descriptions-item label="是否超级管理员">
                <el-tag :type="isSuperAdmin ? 'success' : 'danger'">
                  {{ isSuperAdmin ? '是' : '否' }}
                </el-tag>
              </el-descriptions-item>
              <el-descriptions-item label="是否商户管理员">
                <el-tag :type="isMerchantAdmin ? 'success' : 'danger'">
                  {{ isMerchantAdmin ? '是' : '否' }}
                </el-tag>
              </el-descriptions-item>
              <el-descriptions-item label="是否CK供应商">
                <el-tag :type="isCkSupplier ? 'success' : 'danger'">
                  {{ isCkSupplier ? '是' : '否' }}
                </el-tag>
              </el-descriptions-item>
              <el-descriptions-item label="用户管理权限">
                <el-tag :type="hasUserPermission ? 'success' : 'danger'">
                  {{ hasUserPermission ? '有权限' : '无权限' }}
                </el-tag>
              </el-descriptions-item>
              <el-descriptions-item label="仪表盘菜单权限">
                <el-tag :type="hasDashboardMenu ? 'success' : 'danger'">
                  {{ hasDashboardMenu ? '有权限' : '无权限' }}
                </el-tag>
              </el-descriptions-item>
              <el-descriptions-item label="系统管理菜单权限">
                <el-tag :type="hasSystemMenu ? 'success' : 'danger'">
                  {{ hasSystemMenu ? '有权限' : '无权限' }}
                </el-tag>
              </el-descriptions-item>
            </el-descriptions>
          </el-card>
        </el-col>
      </el-row>

      <!-- 权限测试按钮 -->
      <el-divider content-position="left">权限指令测试</el-divider>
      
      <el-row :gutter="20">
        <el-col :span="24">
          <el-space wrap>
            <!-- 基于权限的按钮显示 -->
            <el-button type="primary" v-permission="'api:/api/v1/users'">
              用户管理按钮 (需要用户API权限)
            </el-button>
            
            <el-button type="success" v-permission="'menu:dashboard'">
              仪表盘按钮 (需要仪表盘菜单权限)
            </el-button>
            
            <el-button type="warning" v-permission="'api:/api/v1/merchants'">
              商户管理按钮 (需要商户API权限)
            </el-button>
            
            <!-- 基于角色的按钮显示 -->
            <el-button type="danger" v-role="'super_admin'">
              超级管理员专用按钮
            </el-button>
            
            <el-button type="info" v-role="'merchant_admin'">
              商户管理员专用按钮
            </el-button>
            
            <el-button type="primary" v-role="['super_admin', 'merchant_admin']">
              管理员按钮 (多角色)
            </el-button>
          </el-space>
        </el-col>
      </el-row>

      <!-- 手动权限检查 -->
      <el-divider content-position="left">手动权限检查</el-divider>
      
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form :model="testForm" label-width="120px">
            <el-form-item label="权限代码">
              <el-input v-model="testForm.permissionCode" placeholder="输入权限代码，如：api:/api/v1/users" />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="checkPermission">检查权限</el-button>
            </el-form-item>
            <el-form-item label="检查结果" v-if="permissionResult !== null">
              <el-tag :type="permissionResult ? 'success' : 'danger'">
                {{ permissionResult ? '有权限' : '无权限' }}
              </el-tag>
            </el-form-item>
          </el-form>
        </el-col>
        
        <el-col :span="12">
          <el-form :model="testForm" label-width="120px">
            <el-form-item label="角色代码">
              <el-input v-model="testForm.roleCode" placeholder="输入角色代码，如：super_admin" />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="checkRole">检查角色</el-button>
            </el-form-item>
            <el-form-item label="检查结果" v-if="roleResult !== null">
              <el-tag :type="roleResult ? 'success' : 'danger'">
                {{ roleResult ? '有角色' : '无角色' }}
              </el-tag>
            </el-form-item>
          </el-form>
        </el-col>
      </el-row>

      <!-- 刷新用户信息 -->
      <el-divider />
      <el-row>
        <el-col :span="24" style="text-align: center;">
          <el-button type="primary" @click="refreshUserInfo" :loading="loading">
            刷新用户信息
          </el-button>
        </el-col>
      </el-row>
    </el-card>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { useUserStore } from '@/store/modules/user'
import { usePermission } from '@/composables/usePermission'

const userStore = useUserStore()
const {
  userInfo,
  hasPermission,
  hasRole,
  hasMenuPermission,
  isSuperAdmin,
  isMerchantAdmin,
  isCkSupplier
} = usePermission()

const loading = ref(false)
const permissionResult = ref(null)
const roleResult = ref(null)

const testForm = ref({
  permissionCode: '',
  roleCode: ''
})

// 计算属性 - 检查特定权限
const hasUserPermission = computed(() => hasPermission('api:/api/v1/users'))
const hasDashboardMenu = computed(() => hasMenuPermission('dashboard'))
const hasSystemMenu = computed(() => hasMenuPermission('system'))

// 检查权限
const checkPermission = () => {
  if (!testForm.value.permissionCode.trim()) {
    ElMessage.warning('请输入权限代码')
    return
  }
  
  permissionResult.value = hasPermission(testForm.value.permissionCode.trim())
}

// 检查角色
const checkRole = () => {
  if (!testForm.value.roleCode.trim()) {
    ElMessage.warning('请输入角色代码')
    return
  }
  
  roleResult.value = hasRole(testForm.value.roleCode.trim())
}

// 刷新用户信息
const refreshUserInfo = async () => {
  loading.value = true
  try {
    await userStore.getUserInfo()
    ElMessage.success('用户信息刷新成功')
  } catch (error) {
    ElMessage.error('刷新用户信息失败')
  } finally {
    loading.value = false
  }
}

onMounted(() => {
  console.log('权限测试页面加载完成')
  console.log('当前用户信息:', userInfo.value)
})
</script>

<style scoped>
.permission-test {
  padding: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.info-card {
  margin-bottom: 20px;
}

.role-tag {
  margin-right: 8px;
  margin-bottom: 4px;
}

.menu-tag {
  margin-right: 4px;
  margin-bottom: 4px;
}
</style>
