"""
权限冲突检测服务
检测和提示权限配置冲突，确保权限系统的一致性
"""

from typing import List, Dict, Any, Optional, Set, Tuple
from sqlalchemy.orm import Session, selectinload
from sqlalchemy import text

from app.models.user import User
from app.models.role import Role
from app.models.permission import Permission
from app.core.logging import get_logger


class ConflictType:
    """冲突类型常量类"""
    DUPLICATE_PERMISSION = "duplicate_permission"  # 重复权限
    CONFLICTING_PERMISSION = "conflicting_permission"  # 冲突权限
    ORPHANED_PERMISSION = "orphaned_permission"  # 孤立权限
    INVALID_PERMISSION = "invalid_permission"  # 无效权限
    ROLE_CONFLICT = "role_conflict"  # 角色冲突
    USER_CONFLICT = "user_conflict"  # 用户冲突


class ConflictSeverity:
    """冲突严重程度常量类"""
    LOW = "low"  # 低
    MEDIUM = "medium"  # 中
    HIGH = "high"  # 高
    CRITICAL = "critical"  # 严重


class PermissionConflictService:
    """权限冲突检测服务"""

    def __init__(self, db: Session):
        self.db = db
        self.logger = get_logger("permission_conflict")

    def detect_all_conflicts(self) -> Dict[str, Any]:
        """
        检测所有权限冲突

        Returns:
            Dict包含:
            - conflicts: 冲突列表
            - summary: 冲突摘要
            - recommendations: 修复建议
        """
        try:
            conflicts = []

            # 检测各种类型的冲突
            conflicts.extend(self._detect_duplicate_permissions())
            conflicts.extend(self._detect_conflicting_permissions())
            conflicts.extend(self._detect_orphaned_permissions())
            conflicts.extend(self._detect_invalid_permissions())
            conflicts.extend(self._detect_role_conflicts())
            conflicts.extend(self._detect_user_conflicts())

            # 生成摘要
            summary = self._generate_conflict_summary(conflicts)

            # 生成修复建议
            recommendations = self._generate_recommendations(conflicts)

            return {
                "conflicts": conflicts,
                "summary": summary,
                "recommendations": recommendations,
                "total_conflicts": len(conflicts),
                "scan_time": self._get_current_time()
            }

        except Exception as e:
            self.logger.error(f"检测权限冲突失败: {e}")
            raise

    def _detect_duplicate_permissions(self) -> List[Dict[str, Any]]:
        """检测重复权限"""
        conflicts = []

        # 检测相同code的权限
        sql = text("""
            SELECT code, COUNT(*) as count, GROUP_CONCAT(id) as ids
            FROM permissions
            GROUP BY code
            HAVING COUNT(*) > 1
        """)

        result = self.db.execute(sql)
        for row in result:
            conflicts.append({
                "type": ConflictType.DUPLICATE_PERMISSION,
                "severity": ConflictSeverity.HIGH,
                "title": f"重复权限代码: {row.code}",
                "description": f"权限代码 '{row.code}' 存在 {row.count} 个重复项",
                "details": {
                    "permission_code": row.code,
                    "duplicate_count": row.count,
                    "permission_ids": row.ids.split(',') if row.ids else []
                },
                "impact": "可能导致权限检查不一致",
                "suggestion": "删除重复的权限记录，保留一个有效的权限"
            })

        return conflicts

    def _detect_conflicting_permissions(self) -> List[Dict[str, Any]]:
        """检测冲突权限"""
        conflicts = []

        # 检测可能冲突的权限模式
        conflict_patterns = [
            # 读写冲突
            ("read", "write", "读写权限可能存在逻辑冲突"),
            ("view", "edit", "查看和编辑权限可能存在逻辑冲突"),
            ("create", "delete", "创建和删除权限配置需要谨慎"),
        ]

        for pattern1, pattern2, description in conflict_patterns:
            sql = text("""
                SELECT p1.code as code1, p2.code as code2,
                       p1.resource_path as path1, p2.resource_path as path2
                FROM permissions p1, permissions p2
                WHERE p1.code LIKE :pattern1 AND p2.code LIKE :pattern2
                AND p1.resource_path = p2.resource_path
                AND p1.id != p2.id
                AND p1.is_enabled = 1 AND p2.is_enabled = 1
            """)

            result = self.db.execute(sql, {
                "pattern1": f"%{pattern1}%",
                "pattern2": f"%{pattern2}%"
            })

            for row in result:
                conflicts.append({
                    "type": ConflictType.CONFLICTING_PERMISSION,
                    "severity": ConflictSeverity.MEDIUM,
                    "title": f"权限冲突: {row.code1} vs {row.code2}",
                    "description": description,
                    "details": {
                        "permission1": row.code1,
                        "permission2": row.code2,
                        "resource_path": row.path1
                    },
                    "impact": "可能导致权限逻辑混乱",
                    "suggestion": "检查权限逻辑，确保权限配置的一致性"
                })

        return conflicts

    def _detect_orphaned_permissions(self) -> List[Dict[str, Any]]:
        """检测孤立权限（没有被任何角色使用的权限）"""
        conflicts = []

        sql = text("""
            SELECT p.id, p.code, p.name
            FROM permissions p
            LEFT JOIN role_permissions rp ON p.id = rp.permission_id
            LEFT JOIN user_permissions up ON p.id = up.permission_id
            WHERE rp.permission_id IS NULL
            AND up.permission_id IS NULL
            AND p.is_enabled = 1
        """)

        result = self.db.execute(sql)
        for row in result:
            conflicts.append({
                "type": ConflictType.ORPHANED_PERMISSION,
                "severity": ConflictSeverity.LOW,
                "title": f"孤立权限: {row.code}",
                "description": f"权限 '{row.name}' 没有被任何角色或用户使用",
                "details": {
                    "permission_id": row.id,
                    "permission_code": row.code,
                    "permission_name": row.name
                },
                "impact": "权限配置冗余，可能造成管理混乱",
                "suggestion": "考虑删除未使用的权限或将其分配给相应的角色"
            })

        return conflicts

    def _detect_invalid_permissions(self) -> List[Dict[str, Any]]:
        """检测无效权限"""
        conflicts = []

        # 检测权限代码格式不正确的权限
        sql = text("""
            SELECT id, code, name, resource_type
            FROM permissions
            WHERE (resource_type = 'api' AND code NOT LIKE 'api:%')
            OR (resource_type = 'menu' AND code NOT LIKE 'menu:%')
            OR code IS NULL OR code = ''
            OR name IS NULL OR name = ''
        """)

        result = self.db.execute(sql)
        for row in result:
            conflicts.append({
                "type": ConflictType.INVALID_PERMISSION,
                "severity": ConflictSeverity.HIGH,
                "title": f"无效权限: {row.code or 'NULL'}",
                "description": f"权限格式不正确或缺少必要信息",
                "details": {
                    "permission_id": row.id,
                    "permission_code": row.code,
                    "permission_name": row.name,
                    "resource_type": row.resource_type
                },
                "impact": "可能导致权限检查失败",
                "suggestion": "修正权限代码格式或补充缺失信息"
            })

        return conflicts

    def _detect_role_conflicts(self) -> List[Dict[str, Any]]:
        """检测角色冲突"""
        conflicts = []

        # 检测重复角色代码
        sql = text("""
            SELECT code, COUNT(*) as count, GROUP_CONCAT(id) as ids
            FROM roles
            GROUP BY code
            HAVING COUNT(*) > 1
        """)

        result = self.db.execute(sql)
        for row in result:
            conflicts.append({
                "type": ConflictType.ROLE_CONFLICT,
                "severity": ConflictSeverity.HIGH,
                "title": f"重复角色代码: {row.code}",
                "description": f"角色代码 '{row.code}' 存在 {row.count} 个重复项",
                "details": {
                    "role_code": row.code,
                    "duplicate_count": row.count,
                    "role_ids": row.ids.split(',') if row.ids else []
                },
                "impact": "可能导致角色权限分配混乱",
                "suggestion": "删除重复的角色记录，保留一个有效的角色"
            })

        return conflicts

    def _detect_user_conflicts(self) -> List[Dict[str, Any]]:
        """检测用户冲突"""
        conflicts = []

        # 检测用户权限过度分配
        sql = text("""
            SELECT u.id, u.username, COUNT(DISTINCT rp.permission_id) as role_perms,
                   COUNT(DISTINCT up.permission_id) as direct_perms
            FROM users u
            LEFT JOIN user_roles ur ON u.id = ur.user_id
            LEFT JOIN role_permissions rp ON ur.role_id = rp.role_id
            LEFT JOIN user_permissions up ON u.id = up.user_id
            WHERE u.is_active = 1
            GROUP BY u.id, u.username
            HAVING (role_perms + direct_perms) > 50
        """)

        result = self.db.execute(sql)
        for row in result:
            total_perms = (row.role_perms or 0) + (row.direct_perms or 0)
            conflicts.append({
                "type": ConflictType.USER_CONFLICT,
                "severity": ConflictSeverity.MEDIUM,
                "title": f"用户权限过多: {row.username}",
                "description": f"用户拥有 {total_perms} 个权限，可能存在过度授权",
                "details": {
                    "user_id": row.id,
                    "username": row.username,
                    "role_permissions": row.role_perms or 0,
                    "direct_permissions": row.direct_perms or 0,
                    "total_permissions": total_perms
                },
                "impact": "可能存在安全风险，权限过度分配",
                "suggestion": "检查用户权限分配，移除不必要的权限"
            })

        return conflicts

    def _generate_conflict_summary(self, conflicts: List[Dict[str, Any]]) -> Dict[str, Any]:
        """生成冲突摘要"""
        summary = {
            "total_conflicts": len(conflicts),
            "by_type": {},
            "by_severity": {},
            "critical_count": 0,
            "high_count": 0,
            "medium_count": 0,
            "low_count": 0
        }

        for conflict in conflicts:
            # 按类型统计
            conflict_type = conflict["type"]
            if conflict_type not in summary["by_type"]:
                summary["by_type"][conflict_type] = 0
            summary["by_type"][conflict_type] += 1

            # 按严重程度统计
            severity = conflict["severity"]
            if severity not in summary["by_severity"]:
                summary["by_severity"][severity] = 0
            summary["by_severity"][severity] += 1

            # 计数器
            if severity == ConflictSeverity.CRITICAL:
                summary["critical_count"] += 1
            elif severity == ConflictSeverity.HIGH:
                summary["high_count"] += 1
            elif severity == ConflictSeverity.MEDIUM:
                summary["medium_count"] += 1
            elif severity == ConflictSeverity.LOW:
                summary["low_count"] += 1

        return summary

    def _generate_recommendations(self, conflicts: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """生成修复建议"""
        recommendations = []

        # 按严重程度排序冲突
        critical_conflicts = [c for c in conflicts if c["severity"] == ConflictSeverity.CRITICAL]
        high_conflicts = [c for c in conflicts if c["severity"] == ConflictSeverity.HIGH]

        if critical_conflicts:
            recommendations.append({
                "priority": "urgent",
                "title": "立即处理严重冲突",
                "description": f"发现 {len(critical_conflicts)} 个严重冲突，需要立即处理",
                "actions": [
                    "检查并修复重复的权限或角色",
                    "验证权限配置的正确性",
                    "确保系统安全性"
                ]
            })

        if high_conflicts:
            recommendations.append({
                "priority": "high",
                "title": "处理高优先级冲突",
                "description": f"发现 {len(high_conflicts)} 个高优先级冲突",
                "actions": [
                    "清理重复的权限记录",
                    "修正无效的权限格式",
                    "优化权限配置结构"
                ]
            })

        # 通用建议
        recommendations.append({
            "priority": "general",
            "title": "权限系统维护建议",
            "description": "定期维护权限系统以确保最佳性能",
            "actions": [
                "定期运行权限冲突检测",
                "清理未使用的权限",
                "审查用户权限分配",
                "建立权限管理规范"
            ]
        })

        return recommendations

    def _get_current_time(self) -> str:
        """获取当前时间"""
        from datetime import datetime
        return datetime.now().isoformat()

    def resolve_conflict(self, conflict_id: str, resolution_action: str) -> Dict[str, Any]:
        """
        解决特定冲突

        Args:
            conflict_id: 冲突ID
            resolution_action: 解决方案

        Returns:
            解决结果
        """
        try:
            # 这里可以实现具体的冲突解决逻辑
            # 根据不同的冲突类型和解决方案执行相应的操作

            return {
                "success": True,
                "message": f"冲突 {conflict_id} 已解决",
                "action": resolution_action,
                "timestamp": self._get_current_time()
            }

        except Exception as e:
            self.logger.error(f"解决冲突失败: {e}")
            return {
                "success": False,
                "message": f"解决冲突失败: {str(e)}",
                "timestamp": self._get_current_time()
            }
