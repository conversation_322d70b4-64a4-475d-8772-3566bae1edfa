<template>
    <div class="notification-icon">
        <el-badge :value="unreadCount" :max="99" class="notification-badge">
            <el-icon class="notification-bell" @click="toggleDropdown">
                <Bell />
            </el-icon>
        </el-badge>

        <notification-dropdown v-if="showDropdown" :notifications="recentNotifications" @close="showDropdown = false"
            @view-all="navigateToNotificationCenter" @mark-read="markAsRead" />
    </div>
</template>

<script setup>
import { ref, computed, onMounted, onBeforeUnmount } from 'vue'
import { useRouter } from 'vue-router'
import { Bell } from '@element-plus/icons-vue'
import { useNotificationStore } from '@/store/modules/notification'
import NotificationDropdown from './NotificationDropdown.vue'

const router = useRouter()
const notificationStore = useNotificationStore()

const showDropdown = ref(false)

// 计算未读通知数量
const unreadCount = computed(() => {
    return notificationStore.unreadCount
})

// 获取最近的5条通知
const recentNotifications = computed(() => {
    return notificationStore.notifications.slice(0, 5)
})

// 切换下拉菜单显示状态
const toggleDropdown = () => {
    showDropdown.value = !showDropdown.value

    // 如果打开下拉菜单，则获取最新通知
    if (showDropdown.value) {
        notificationStore.fetchNotifications()
    }
}

// 导航到通知中心
const navigateToNotificationCenter = () => {
    router.push('/notification')
    showDropdown.value = false
}

// 标记通知为已读
const markAsRead = (notificationId) => {
    notificationStore.markAsRead(notificationId)
}

// 点击外部关闭下拉菜单
const handleClickOutside = (event) => {
    const element = document.querySelector('.notification-icon')
    if (element && !element.contains(event.target) && showDropdown.value) {
        showDropdown.value = false
    }
}

// 组件挂载和卸载时添加/移除事件监听
onMounted(() => {
    document.addEventListener('click', handleClickOutside)

    // 初始化获取通知
    notificationStore.fetchNotifications()

    // 设置轮询获取通知（实际项目中可考虑使用WebSocket）
    const intervalId = setInterval(() => {
        if (!showDropdown.value) { // 避免在查看通知时刷新
            notificationStore.fetchNotifications()
        }
    }, 60000) // 每分钟检查一次新通知

    // 清除定时器
    onBeforeUnmount(() => {
        clearInterval(intervalId)
        document.removeEventListener('click', handleClickOutside)
    })
})
</script>

<style scoped>
.notification-icon {
    position: relative;
    margin-right: 20px;
    height: 100%;
    display: flex;
    align-items: center;
}

.notification-bell {
    font-size: 20px;
    cursor: pointer;
    color: #606266;
}

.notification-bell:hover {
    color: #409EFF;
}

.notification-badge :deep(.el-badge__content) {
    z-index: 10;
}
</style>