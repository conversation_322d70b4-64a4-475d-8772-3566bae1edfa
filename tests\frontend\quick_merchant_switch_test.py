#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
沃尔玛绑卡系统 - 商户切换功能快速测试

快速验证商户切换功能的基本工作状态
"""

import asyncio
import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from playwright.async_api import async_playwright


async def quick_test():
    """快速测试商户切换功能"""
    print("🚀 开始商户切换功能快速测试...")
    
    async with async_playwright() as p:
        # 启动浏览器
        browser = await p.chromium.launch(headless=False)
        context = await browser.new_context()
        page = await context.new_page()
        
        try:
            # 访问登录页面
            await page.goto("http://localhost:2000/#/login")
            await page.wait_for_load_state('networkidle')
            print("✅ 成功访问登录页面")
            
            # 登录超级管理员
            await page.fill('input[placeholder="请输入用户名"]', 'admin')
            await page.fill('input[placeholder="请输入密码"]', '7c222fb2927d828af22f592134e8932480637c0d')
            await page.click('button[type="submit"]')
            
            # 等待跳转到仪表盘
            await page.wait_for_url("http://localhost:2000/#/dashboard", timeout=10000)
            print("✅ 超级管理员登录成功")
            
            # 检查商户切换器是否存在
            merchant_switcher = page.locator('.merchant-switcher')
            if await merchant_switcher.count() > 0:
                print("✅ 商户切换器组件存在")
                
                # 检查商户选择器
                merchant_selector = page.locator('.merchant-selector')
                if await merchant_selector.count() > 0:
                    print("✅ 商户选择器存在")
                    
                    # 获取当前显示的商户名称
                    merchant_name = await page.locator('.merchant-name').text_content()
                    print(f"✅ 当前商户显示: {merchant_name}")
                    
                    # 点击商户切换器
                    await merchant_selector.click()
                    await page.wait_for_timeout(1000)
                    
                    # 检查下拉菜单是否出现
                    merchant_popover = page.locator('.merchant-popover')
                    if await merchant_popover.count() > 0:
                        print("✅ 商户切换下拉菜单正常显示")
                        
                        # 检查全局视图选项
                        global_view = page.locator('.merchant-item').filter(has_text='全局视图').first()
                        if await global_view.count() > 0:
                            print("✅ 全局视图选项存在")
                        else:
                            print("⚠️ 全局视图选项不存在")
                        
                        # 检查商户列表
                        merchant_items = page.locator('.merchant-item')
                        merchant_count = await merchant_items.count()
                        print(f"✅ 发现 {merchant_count} 个商户选项")
                        
                        # 点击空白处关闭下拉菜单
                        await page.click('body')
                        await page.wait_for_timeout(500)
                        
                    else:
                        print("❌ 商户切换下拉菜单未显示")
                else:
                    print("❌ 商户选择器不存在")
            else:
                print("❌ 商户切换器组件不存在")
            
            # 检查localStorage中的用户信息
            user_info = await page.evaluate("""
                () => {
                    const userInfo = localStorage.getItem('walmart_user_info');
                    return userInfo ? JSON.parse(userInfo) : null;
                }
            """)
            
            if user_info and user_info.get('id'):
                print(f"✅ 用户信息存在，用户ID: {user_info['id']}")
                
                # 检查用户特定的商户选择存储
                user_merchant_key = f"currentMerchant_{user_info['id']}"
                current_merchant = await page.evaluate(f"""
                    () => localStorage.getItem('{user_merchant_key}')
                """)
                
                if current_merchant:
                    print(f"✅ 发现用户特定的商户选择存储: {current_merchant}")
                else:
                    print("ℹ️ 当前无商户选择存储（全局视图）")
            else:
                print("❌ 用户信息不存在或格式错误")
            
            print("✅ 商户切换功能快速测试完成")
            
        except Exception as e:
            print(f"❌ 测试过程中发生错误: {str(e)}")
            import traceback
            traceback.print_exc()
        
        finally:
            await context.close()
            await browser.close()


async def test_merchant_user():
    """测试商户用户是否看不到商户切换器"""
    print("\n🔍 测试商户用户权限...")
    
    async with async_playwright() as p:
        browser = await p.chromium.launch(headless=False)
        context = await browser.new_context()
        page = await context.new_page()
        
        try:
            # 访问登录页面
            await page.goto("http://localhost:2000/#/login")
            await page.wait_for_load_state('networkidle')
            
            # 登录商户用户
            await page.fill('input[placeholder="请输入用户名"]', 'test1')
            await page.fill('input[placeholder="请输入密码"]', '12345678')
            await page.click('button[type="submit"]')
            
            # 等待跳转到仪表盘
            await page.wait_for_url("http://localhost:2000/#/dashboard", timeout=10000)
            print("✅ 商户用户登录成功")
            
            # 检查商户切换器是否不存在
            merchant_switcher = page.locator('.merchant-switcher')
            if await merchant_switcher.count() == 0:
                print("✅ 商户用户正确地看不到商户切换器")
            else:
                print("❌ 商户用户不应该看到商户切换器")
            
        except Exception as e:
            print(f"❌ 商户用户测试失败: {str(e)}")
        
        finally:
            await context.close()
            await browser.close()


async def main():
    """主函数"""
    await quick_test()
    await test_merchant_user()
    print("\n🎉 所有快速测试完成！")


if __name__ == "__main__":
    asyncio.run(main())
