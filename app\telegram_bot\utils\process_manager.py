"""
进程管理工具
用于检查和管理Telegram Bot进程，避免多实例冲突
"""

import os
import sys
import psutil
import signal
from typing import List, Dict, Optional
from app.core.logging import get_logger

logger = get_logger(__name__)


class ProcessManager:
    """进程管理器"""
    
    @staticmethod
    def get_current_process_info() -> Dict[str, any]:
        """获取当前进程信息"""
        current_process = psutil.Process()
        return {
            'pid': current_process.pid,
            'name': current_process.name(),
            'cmdline': current_process.cmdline(),
            'create_time': current_process.create_time(),
            'cwd': current_process.cwd() if hasattr(current_process, 'cwd') else None
        }
    
    @staticmethod
    def find_similar_processes() -> List[Dict[str, any]]:
        """查找相似的进程（可能的重复实例）"""
        current_process = psutil.Process()
        current_pid = current_process.pid
        current_cmdline = current_process.cmdline()
        similar_processes = []

        # 获取当前进程的父进程信息，用于更精确的过滤
        try:
            parent_process = current_process.parent()
            parent_pid = parent_process.pid if parent_process else None
        except (psutil.NoSuchProcess, psutil.AccessDenied):
            parent_pid = None

        # 查找包含main.py的Python进程
        for proc in psutil.process_iter(['pid', 'name', 'cmdline', 'create_time', 'ppid']):
            try:
                if proc.info['name'] and 'python' in proc.info['name'].lower():
                    cmdline = proc.info['cmdline'] or []
                    cmdline_str = ' '.join(cmdline)
                    proc_pid = proc.info['pid']

                    # 跳过当前进程
                    if proc_pid == current_pid:
                        continue

                    # 跳过父进程（如果是uvicorn reloader）
                    if parent_pid and proc_pid == parent_pid:
                        continue

                    # 跳过当前进程的子进程
                    if proc.info.get('ppid') == current_pid:
                        continue

                    # 检查是否是相同的应用，但要更严格的条件
                    if ('main.py' in cmdline_str and
                        'walmart-bind-card-server' in cmdline_str):

                        # 进一步过滤：跳过调试器进程
                        if 'debugpy' in cmdline_str:
                            # 这些是VS Code调试器进程，通常不是真正的重复实例
                            logger.debug(f"跳过调试器进程 PID: {proc_pid}")
                            continue

                        # 检查进程是否真的在运行相同的服务
                        try:
                            proc_obj = psutil.Process(proc_pid)
                            # 检查进程状态
                            if proc_obj.status() in [psutil.STATUS_ZOMBIE, psutil.STATUS_DEAD]:
                                continue

                            similar_processes.append({
                                'pid': proc_pid,
                                'cmdline': cmdline,
                                'create_time': proc.info['create_time'],
                                'is_duplicate': True,
                                'status': proc_obj.status()
                            })
                        except (psutil.NoSuchProcess, psutil.AccessDenied):
                            continue

            except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess):
                continue

        return similar_processes
    
    @staticmethod
    def check_for_conflicts() -> Dict[str, any]:
        """检查进程冲突"""
        current_info = ProcessManager.get_current_process_info()
        similar_processes = ProcessManager.find_similar_processes()
        
        return {
            'current_process': current_info,
            'similar_processes': similar_processes,
            'has_conflicts': len(similar_processes) > 0,
            'conflict_count': len(similar_processes)
        }
    
    @staticmethod
    def terminate_duplicate_processes(dry_run: bool = True) -> Dict[str, any]:
        """终止重复进程"""
        similar_processes = ProcessManager.find_similar_processes()
        terminated = []
        failed = []
        
        for proc_info in similar_processes:
            pid = proc_info['pid']
            try:
                if not dry_run:
                    # 尝试优雅终止
                    proc = psutil.Process(pid)
                    proc.terminate()
                    
                    # 等待进程终止
                    try:
                        proc.wait(timeout=5)
                        terminated.append(pid)
                        logger.info(f"成功终止进程 PID: {pid}")
                    except psutil.TimeoutExpired:
                        # 强制终止
                        proc.kill()
                        terminated.append(pid)
                        logger.warning(f"强制终止进程 PID: {pid}")
                else:
                    terminated.append(pid)
                    logger.info(f"[DRY RUN] 将终止进程 PID: {pid}")
                    
            except (psutil.NoSuchProcess, psutil.AccessDenied) as e:
                failed.append({'pid': pid, 'error': str(e)})
                logger.error(f"无法终止进程 PID {pid}: {e}")
        
        return {
            'terminated': terminated,
            'failed': failed,
            'total_found': len(similar_processes),
            'dry_run': dry_run
        }
    
    @staticmethod
    def create_pid_file(pid_file_path: str) -> bool:
        """创建PID文件"""
        try:
            current_pid = os.getpid()
            
            # 检查PID文件是否已存在
            if os.path.exists(pid_file_path):
                with open(pid_file_path, 'r') as f:
                    old_pid = int(f.read().strip())
                
                # 检查旧进程是否还在运行
                try:
                    old_process = psutil.Process(old_pid)
                    if old_process.is_running():
                        logger.error(f"检测到已有进程运行 PID: {old_pid}")
                        return False
                except psutil.NoSuchProcess:
                    # 旧进程已不存在，可以继续
                    pass
            
            # 写入当前PID
            with open(pid_file_path, 'w') as f:
                f.write(str(current_pid))
            
            logger.info(f"PID文件已创建: {pid_file_path} (PID: {current_pid})")
            return True
            
        except Exception as e:
            logger.error(f"创建PID文件失败: {e}")
            return False
    
    @staticmethod
    def remove_pid_file(pid_file_path: str) -> bool:
        """删除PID文件"""
        try:
            if os.path.exists(pid_file_path):
                os.remove(pid_file_path)
                logger.info(f"PID文件已删除: {pid_file_path}")
            return True
        except Exception as e:
            logger.error(f"删除PID文件失败: {e}")
            return False
    
    @staticmethod
    def setup_signal_handlers(cleanup_callback=None):
        """设置信号处理器"""
        def signal_handler(signum, frame):
            logger.info(f"收到信号 {signum}，正在清理...")
            if cleanup_callback:
                cleanup_callback()
            sys.exit(0)
        
        # 注册信号处理器
        if hasattr(signal, 'SIGTERM'):
            signal.signal(signal.SIGTERM, signal_handler)
        if hasattr(signal, 'SIGINT'):
            signal.signal(signal.SIGINT, signal_handler)


def check_and_resolve_conflicts(auto_resolve: bool = False) -> bool:
    """检查并解决进程冲突"""
    logger.info("检查进程冲突...")

    conflict_info = ProcessManager.check_for_conflicts()

    if not conflict_info['has_conflicts']:
        logger.info("未发现进程冲突")
        return True

    logger.warning(f"发现 {conflict_info['conflict_count']} 个可能的重复进程")

    for proc in conflict_info['similar_processes']:
        logger.warning(f"重复进程 PID: {proc['pid']}, 命令行: {' '.join(proc['cmdline'])}")

    if auto_resolve:
        logger.info("自动解决冲突...")
        result = ProcessManager.terminate_duplicate_processes(dry_run=False)

        if result['terminated']:
            logger.info(f"成功终止 {len(result['terminated'])} 个重复进程")
            return True
        else:
            logger.error("无法终止重复进程")
            return False
    else:
        logger.info("请手动终止重复进程或使用 auto_resolve=True 参数")
        return False


def safe_check_conflicts() -> Dict[str, any]:
    """安全的进程冲突检查（仅检查，不终止）"""
    try:
        return ProcessManager.check_for_conflicts()
    except Exception as e:
        logger.error(f"进程冲突检查失败: {e}")
        return {
            'current_process': {'pid': os.getpid()},
            'similar_processes': [],
            'has_conflicts': False,
            'conflict_count': 0,
            'error': str(e)
        }


if __name__ == "__main__":
    # 命令行工具
    import argparse
    
    parser = argparse.ArgumentParser(description="进程管理工具")
    parser.add_argument("--check", action="store_true", help="检查进程冲突")
    parser.add_argument("--resolve", action="store_true", help="自动解决冲突")
    parser.add_argument("--dry-run", action="store_true", help="仅显示将要执行的操作")
    
    args = parser.parse_args()
    
    if args.check:
        conflict_info = ProcessManager.check_for_conflicts()
        print(f"当前进程: PID {conflict_info['current_process']['pid']}")
        print(f"发现重复进程: {conflict_info['conflict_count']} 个")
        
        for proc in conflict_info['similar_processes']:
            print(f"  - PID: {proc['pid']}, 命令行: {' '.join(proc['cmdline'])}")
    
    elif args.resolve:
        result = ProcessManager.terminate_duplicate_processes(dry_run=args.dry_run)
        print(f"处理结果: 终止 {len(result['terminated'])} 个进程, 失败 {len(result['failed'])} 个")
    
    else:
        parser.print_help()
