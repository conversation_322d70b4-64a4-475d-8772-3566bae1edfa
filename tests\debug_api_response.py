#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试API响应格式
"""

import sys
import os
import json

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(__file__)))

from test.conftest import TestBase


def debug_api_responses():
    """调试API响应格式"""
    api_test = TestBase()
    
    # 管理员登录
    admin_token = api_test.login("admin", "7c222fb2927d828af22f592134e8932480637c0d")
    if not admin_token:
        print("❌ 管理员登录失败")
        return
    
    print("✅ 管理员登录成功")
    
    # 测试创建角色API的响应格式
    print("\n=== 测试创建角色API ===")
    role_data = {
        "name": "调试测试角色",
        "code": "debug_test_role",
        "description": "用于调试的测试角色",
        "is_enabled": True
    }
    
    status_code, response = api_test.make_request(
        "POST", "/roles", admin_token, data=role_data
    )
    
    print(f"状态码: {status_code}")
    print(f"响应内容: {json.dumps(response, indent=2, ensure_ascii=False)}")
    
    # 如果创建成功，尝试删除
    if status_code == 200:
        role_id = None
        if isinstance(response.get("data"), dict):
            role_id = response["data"].get("id")
        elif isinstance(response.get("data"), (int, str)):
            role_id = response["data"]
        elif response.get("id"):
            role_id = response["id"]
        
        if role_id:
            print(f"提取到的角色ID: {role_id}")
            
            # 删除测试角色
            delete_status, delete_response = api_test.make_request(
                "DELETE", f"/roles/{role_id}", admin_token
            )
            print(f"删除状态码: {delete_status}")
            print(f"删除响应: {json.dumps(delete_response, indent=2, ensure_ascii=False)}")
        else:
            print("❌ 无法提取角色ID")
    
    # 测试获取权限列表API
    print("\n=== 测试获取权限列表API ===")
    status_code, response = api_test.make_request(
        "GET", "/permissions?page_size=5", admin_token
    )
    
    print(f"状态码: {status_code}")
    print(f"响应内容: {json.dumps(response, indent=2, ensure_ascii=False)}")
    
    # 测试获取角色列表API
    print("\n=== 测试获取角色列表API ===")
    status_code, response = api_test.make_request(
        "GET", "/roles", admin_token
    )
    
    print(f"状态码: {status_code}")
    print(f"响应内容: {json.dumps(response, indent=2, ensure_ascii=False)}")


if __name__ == "__main__":
    debug_api_responses()
