#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试沃尔玛CK管理页面的时间范围筛选功能
"""

import sys
import os
import time
import json
from datetime import datetime, timedelta

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.dirname(__file__))))

from test.conftest import TestBase, get_test_accounts, format_test_result, print_test_summary


class WalmartCKTimeFilterTestSuite(TestBase):
    """测试沃尔玛CK时间范围筛选功能"""

    def __init__(self):
        super().__init__()
        self.results = []
        self.test_accounts = get_test_accounts()
        self.admin_token = None
        self.merchant_token = None
        self.test_ck_ids = []  # 存储测试创建的CK ID，用于清理

    def setup_test_environment(self):
        """设置测试环境"""
        print("=== 设置沃尔玛CK时间筛选测试环境 ===")

        # 登录管理员账号
        self.admin_token = self.login(
            self.test_accounts["super_admin"]["username"],
            self.test_accounts["super_admin"]["password"]
        )

        # 登录商户账号
        self.merchant_token = self.login(
            self.test_accounts["merchant_admin"]["username"],
            self.test_accounts["merchant_admin"]["password"]
        )

        if not self.admin_token:
            raise Exception("无法获取管理员token")
        if not self.merchant_token:
            raise Exception("无法获取商户token")

    def create_test_ck_with_date(self, description, days_ago=0):
        """创建指定日期的测试CK"""
        # 生成随机签名
        import random
        import string
        random_suffix = ''.join(random.choices(string.ascii_lowercase + string.digits, k=8))
        sign = f"test_ck_{random_suffix}_{days_ago}days_ago"

        ck_data = {
            "sign": sign,
            "description": description,
            "daily_limit": 100,
            "hourly_limit": 10,
            "active": True
        }

        # 创建CK
        response = self.make_request(
            "POST",
            "/walmart-ck/",
            json=ck_data,
            headers={"Authorization": f"Bearer {self.merchant_token}"}
        )

        if response and response.get("success"):
            ck_id = response["data"]["id"]
            self.test_ck_ids.append(ck_id)

            # 如果需要修改创建时间，使用数据库直接更新
            if days_ago > 0:
                # 这里需要直接操作数据库来修改创建时间
                # 由于我们使用的是API测试，暂时跳过时间修改
                pass

            return ck_id
        return None

    def cleanup_test_data(self):
        """清理测试数据"""
        print("=== 清理测试数据 ===")

        # 删除测试创建的CK
        for ck_id in self.test_ck_ids:
            try:
                self.make_request(
                    "DELETE",
                    f"/walmart-ck/{ck_id}",
                    headers={"Authorization": f"Bearer {self.merchant_token}"}
                )
            except Exception as e:
                print(f"删除CK {ck_id} 失败: {e}")

        self.test_ck_ids.clear()

    def test_no_time_filter(self):
        """测试不使用时间筛选时返回所有CK"""
        print("\n--- 测试不使用时间筛选 ---")

        response = self.make_request(
            "GET",
            "/walmart-ck/",
            headers={"Authorization": f"Bearer {self.merchant_token}"}
        )

        success = response and response.get("success", False)
        result = format_test_result(
            "不使用时间筛选获取CK列表",
            success,
            response.get("message", "未知错误") if response else "请求失败"
        )
        self.results.append(result)

        if success:
            items = response.get("data", {}).get("items", [])
            print(f"返回CK数量: {len(items)}")

        return success

    def test_today_filter(self):
        """测试筛选今天创建的CK"""
        print("\n--- 测试今天日期筛选 ---")

        today = datetime.now().strftime("%Y-%m-%d")

        response = self.make_request(
            "GET",
            f"/walmart-ck/?start_date={today}&end_date={today}",
            headers={"Authorization": f"Bearer {self.merchant_token}"}
        )

        success = response and response.get("success", False)
        result = format_test_result(
            f"筛选今天({today})创建的CK",
            success,
            response.get("message", "未知错误") if response else "请求失败"
        )
        self.results.append(result)

        if success:
            items = response.get("data", {}).get("items", [])
            print(f"今天创建的CK数量: {len(items)}")

        return success

    def test_date_range_filter(self):
        """测试日期范围筛选"""
        print("\n--- 测试日期范围筛选 ---")

        start_date = (datetime.now() - timedelta(days=7)).strftime("%Y-%m-%d")
        end_date = datetime.now().strftime("%Y-%m-%d")

        response = self.make_request(
            "GET",
            f"/walmart-ck/?start_date={start_date}&end_date={end_date}",
            headers={"Authorization": f"Bearer {self.merchant_token}"}
        )

        success = response and response.get("success", False)
        result = format_test_result(
            f"筛选日期范围({start_date} 到 {end_date})的CK",
            success,
            response.get("message", "未知错误") if response else "请求失败"
        )
        self.results.append(result)

        if success:
            items = response.get("data", {}).get("items", [])
            print(f"日期范围内的CK数量: {len(items)}")

        return success

    def test_invalid_date_format(self):
        """测试无效的日期格式"""
        print("\n--- 测试无效日期格式 ---")

        response = self.make_request(
            "GET",
            "/walmart-ck/?start_date=invalid-date",
            headers={"Authorization": f"Bearer {self.merchant_token}"}
        )

        # 期望返回400错误
        success = response and not response.get("success", True) and "日期格式错误" in str(response)
        result = format_test_result(
            "测试无效日期格式处理",
            success,
            "正确返回日期格式错误" if success else "未正确处理无效日期格式"
        )
        self.results.append(result)

        return success

    def run_all_tests(self):
        """运行所有测试"""
        print("=== 开始沃尔玛CK时间筛选功能测试 ===")

        try:
            # 设置测试环境
            self.setup_test_environment()

            # 运行测试
            self.test_no_time_filter()
            self.test_today_filter()
            self.test_date_range_filter()
            self.test_invalid_date_format()

        except Exception as e:
            print(f"测试过程中发生错误: {e}")
            result = format_test_result(
                "测试环境设置",
                False,
                str(e)
            )
            self.results.append(result)

        finally:
            # 清理测试数据
            self.cleanup_test_data()

            # 打印测试结果
            print_test_summary(self.results, "沃尔玛CK时间筛选功能测试")


if __name__ == "__main__":
    test_suite = WalmartCKTimeFilterTestSuite()
    test_suite.run_all_tests()
