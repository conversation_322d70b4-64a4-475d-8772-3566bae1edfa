# Redis CK优化测试

本目录包含Redis CK优化功能的相关测试。

## 测试文件说明

### `test_redis_basic.py`
Redis基础功能测试，包括：
- 配置加载测试
- Redis包装器导入测试
- Redis连接测试
- CK服务创建测试
- 健康检查测试

### `test_redis_health_check.py`
Redis健康检查和性能测试，包括：
- Redis优化健康检查
- 性能对比测试
- 系统状态检查
- 集成状态总结

## 运行测试

### 单独运行测试
```bash
# 运行基础测试
python test/redis/test_redis_basic.py

# 运行健康检查测试
python test/redis/test_redis_health_check.py
```

### 使用pytest运行
```bash
# 运行所有Redis测试
pytest test/redis/ -v

# 运行特定测试
pytest test/redis/test_redis_basic.py -v
```

### 使用测试运行器
```bash
# 运行所有Redis相关测试
python test/run_redis_tests.py

# 只运行基础测试
python test/run_redis_tests.py --basic

# 使用pytest运行
python test/run_redis_tests.py --pytest
```

## 测试要求

- Python 3.8+
- Redis服务器运行中
- 正确的配置文件设置
- 数据库连接正常

## 注意事项

1. **Redis连接失败不会导致测试失败**：系统有降级机制，Redis不可用时会自动切换到数据库模式
2. **数据库模型问题**：某些测试可能因为数据库模型依赖问题失败，但不影响Redis优化功能本身
3. **性能测试**：性能对比测试需要有测试数据（商户和CK）才能正常运行

## 故障排查

如果测试失败，请检查：
1. Redis服务是否正常运行
2. 配置文件中的Redis连接信息是否正确
3. 数据库连接是否正常
4. 是否有足够的测试数据
