#!/usr/bin/env python3
"""
进程清理脚本
用于清理重复的机器人进程，解决冲突问题
"""

import os
import sys
import psutil
import argparse
from pathlib import Path

# 添加项目根目录到Python路径
ROOT_DIR = Path(__file__).parent
sys.path.insert(0, str(ROOT_DIR))

from app.core.logging import get_logger

logger = get_logger(__name__)


def find_walmart_processes():
    """查找所有相关的进程"""
    current_pid = os.getpid()
    processes = []
    
    for proc in psutil.process_iter(['pid', 'name', 'cmdline', 'create_time', 'status']):
        try:
            if proc.info['name'] and 'python' in proc.info['name'].lower():
                cmdline = proc.info['cmdline'] or []
                cmdline_str = ' '.join(cmdline)
                
                # 跳过当前进程
                if proc.info['pid'] == current_pid:
                    continue
                
                # 查找包含项目路径的进程
                if ('walmart-bind-card-server' in cmdline_str and 
                    'main.py' in cmdline_str):
                    
                    processes.append({
                        'pid': proc.info['pid'],
                        'cmdline': cmdline_str,
                        'create_time': proc.info['create_time'],
                        'status': proc.info['status'],
                        'is_debugger': 'debugpy' in cmdline_str
                    })
                    
        except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess):
            continue
    
    return processes


def display_processes(processes):
    """显示进程信息"""
    if not processes:
        print("未找到相关进程")
        return
    
    print(f"\n找到 {len(processes)} 个相关进程:")
    print("-" * 80)
    
    for i, proc in enumerate(processes, 1):
        status_text = proc['status']
        type_text = "调试器进程" if proc['is_debugger'] else "应用进程"
        
        print(f"{i}. PID: {proc['pid']} ({type_text}) - 状态: {status_text}")
        print(f"   命令行: {proc['cmdline'][:100]}...")
        print()


def terminate_processes(processes, force=False, exclude_debugger=True):
    """终止进程"""
    if not processes:
        print("没有进程需要终止")
        return
    
    # 过滤进程
    target_processes = []
    for proc in processes:
        if exclude_debugger and proc['is_debugger']:
            print(f"跳过调试器进程 PID: {proc['pid']}")
            continue
        target_processes.append(proc)
    
    if not target_processes:
        print("没有符合条件的进程需要终止")
        return
    
    print(f"\n准备终止 {len(target_processes)} 个进程:")
    
    terminated = []
    failed = []
    
    for proc_info in target_processes:
        pid = proc_info['pid']
        try:
            proc = psutil.Process(pid)
            
            if force:
                proc.kill()
                print(f"强制终止进程 PID: {pid}")
            else:
                proc.terminate()
                print(f"优雅终止进程 PID: {pid}")
            
            # 等待进程终止
            try:
                proc.wait(timeout=5)
                terminated.append(pid)
            except psutil.TimeoutExpired:
                if not force:
                    # 如果优雅终止失败，尝试强制终止
                    proc.kill()
                    print(f"强制终止进程 PID: {pid}")
                terminated.append(pid)
                
        except (psutil.NoSuchProcess, psutil.AccessDenied) as e:
            failed.append({'pid': pid, 'error': str(e)})
            print(f"无法终止进程 PID {pid}: {e}")
    
    print(f"\n结果: 成功终止 {len(terminated)} 个进程, 失败 {len(failed)} 个")
    
    if failed:
        print("失败的进程:")
        for fail in failed:
            print(f"  PID {fail['pid']}: {fail['error']}")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="进程清理工具")
    parser.add_argument("--list", "-l", action="store_true", help="仅列出进程，不终止")
    parser.add_argument("--force", "-f", action="store_true", help="强制终止进程")
    parser.add_argument("--include-debugger", action="store_true", help="包括调试器进程")
    parser.add_argument("--dry-run", action="store_true", help="预览操作，不实际执行")
    
    args = parser.parse_args()
    
    print("正在查找相关进程...")
    processes = find_walmart_processes()
    
    # 显示进程
    display_processes(processes)
    
    if args.list:
        return
    
    if not processes:
        return
    
    if args.dry_run:
        print("\n[预览模式] 将要执行的操作:")
        target_processes = []
        for proc in processes:
            if not args.include_debugger and proc['is_debugger']:
                continue
            target_processes.append(proc)
        
        if target_processes:
            print(f"将终止 {len(target_processes)} 个进程:")
            for proc in target_processes:
                print(f"  - PID: {proc['pid']}")
        else:
            print("没有符合条件的进程需要终止")
        return
    
    # 确认操作
    if not args.force:
        response = input("\n是否继续终止这些进程? (y/N): ")
        if response.lower() not in ['y', 'yes']:
            print("操作已取消")
            return
    
    # 终止进程
    terminate_processes(
        processes, 
        force=args.force,
        exclude_debugger=not args.include_debugger
    )
    
    print("\n清理完成！现在可以重新启动应用。")


if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n操作已中断")
    except Exception as e:
        print(f"发生错误: {e}")
        sys.exit(1)
