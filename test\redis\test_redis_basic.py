#!/usr/bin/env python3
"""
Redis CK优化基础功能测试
测试Redis连接、配置加载和基本服务创建功能
"""

import sys
import os
import pytest
import asyncio

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.insert(0, project_root)

try:
    from app.db.session import SessionLocal
    from app.services.redis_ck_wrapper import create_optimized_ck_service, RedisOptimizedCKWrapper
    from app.core.config import settings
    from app.core.logging import get_logger
except ImportError as e:
    print(f"❌ 导入模块失败: {e}")
    sys.exit(1)

logger = get_logger("test_redis_basic")


class TestRedisBasic:
    """Redis基础功能测试类"""
    
    def test_config_loading(self):
        """测试配置加载"""
        redis_optimization = getattr(settings, 'ENABLE_REDIS_CK_OPTIMIZATION', None)
        fallback_db = getattr(settings, 'CK_REDIS_FALLBACK_TO_DB', None)
        max_connections = getattr(settings, 'REDIS_MAX_CONNECTIONS', None)
        
        assert redis_optimization is not None, "ENABLE_REDIS_CK_OPTIMIZATION配置未找到"
        assert fallback_db is not None, "CK_REDIS_FALLBACK_TO_DB配置未找到"
        assert max_connections is not None, "REDIS_MAX_CONNECTIONS配置未找到"
        
        logger.info(f"配置加载成功: Redis优化={redis_optimization}, 降级={fallback_db}, 最大连接={max_connections}")

    def test_redis_wrapper_import(self):
        """测试Redis包装器导入"""
        assert create_optimized_ck_service is not None, "create_optimized_ck_service导入失败"
        assert RedisOptimizedCKWrapper is not None, "RedisOptimizedCKWrapper导入失败"
        
        logger.info("Redis包装器导入成功")

    @pytest.mark.asyncio
    async def test_redis_connection(self):
        """测试Redis连接"""
        try:
            from app.core.redis import get_redis
            
            redis_client = await get_redis()
            await redis_client.ping()
            
            logger.info("Redis连接测试成功")
            assert True
        except Exception as e:
            logger.warning(f"Redis连接失败: {e}")
            # Redis连接失败不应该导致测试失败，因为系统有降级机制
            pytest.skip(f"Redis不可用，跳过连接测试: {e}")

    def test_service_creation(self):
        """测试CK服务创建"""
        db = SessionLocal()
        
        try:
            # 创建优化服务
            ck_service = create_optimized_ck_service(db)
            
            assert ck_service is not None, "CK服务创建失败"
            assert isinstance(ck_service, RedisOptimizedCKWrapper), "服务类型不正确"
            assert hasattr(ck_service, 'use_redis'), "服务缺少use_redis属性"
            assert hasattr(ck_service, 'fallback_to_db'), "服务缺少fallback_to_db属性"
            
            logger.info(f"CK服务创建成功: Redis启用={ck_service.use_redis}, 降级启用={ck_service.fallback_to_db}")
            
        finally:
            db.close()

    @pytest.mark.asyncio
    async def test_health_check(self):
        """测试健康检查功能"""
        db = SessionLocal()
        
        try:
            ck_service = create_optimized_ck_service(db)
            health_result = await ck_service.health_check()
            
            assert isinstance(health_result, dict), "健康检查结果应该是字典"
            assert "redis_enabled" in health_result, "健康检查结果缺少redis_enabled"
            assert "database_service" in health_result, "健康检查结果缺少database_service"
            
            # 数据库服务应该总是可用的
            assert health_result["database_service"] == "available", "数据库服务不可用"
            
            logger.info(f"健康检查成功: {health_result}")
            
        finally:
            db.close()


def test_config():
    """独立的配置测试函数（兼容旧版本调用）"""
    test_instance = TestRedisBasic()
    test_instance.test_config_loading()


def test_redis_wrapper_import():
    """独立的包装器导入测试函数"""
    test_instance = TestRedisBasic()
    test_instance.test_redis_wrapper_import()


async def test_redis_connection():
    """独立的Redis连接测试函数"""
    test_instance = TestRedisBasic()
    await test_instance.test_redis_connection()


def test_service_creation():
    """独立的服务创建测试函数"""
    test_instance = TestRedisBasic()
    test_instance.test_service_creation()


async def test_health_check():
    """独立的健康检查测试函数"""
    test_instance = TestRedisBasic()
    await test_instance.test_health_check()


def main():
    """主测试函数（用于直接运行）"""
    print("开始Redis CK优化基础测试")
    print("=" * 50)
    
    tests = [
        ("配置加载", test_config),
        ("Redis包装器导入", test_redis_wrapper_import),
        ("Redis连接", test_redis_connection),
        ("服务创建", test_service_creation),
        ("健康检查", test_health_check),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n执行测试: {test_name}")
        try:
            if asyncio.iscoroutinefunction(test_func):
                result = asyncio.run(test_func())
            else:
                result = test_func()
            results.append((test_name, True))
            print(f"通过: {test_name}")
        except Exception as e:
            print(f"失败: {test_name} - {e}")
            results.append((test_name, False))
    
    # 输出总结
    print("\n" + "=" * 50)
    print("测试结果总结:")

    success_count = 0
    for test_name, success in results:
        status = "通过" if success else "失败"
        print(f"   {test_name}: {status}")
        if success:
            success_count += 1

    print(f"\n总体结果: {success_count}/{len(tests)} 项测试通过")

    if success_count == len(tests):
        print("Redis CK优化基础测试全部通过！")
    elif success_count >= len(tests) - 1:
        print("Redis CK优化基础测试基本通过，有少量问题需要关注")
    else:
        print("Redis CK优化基础测试存在问题，需要检查配置和环境")
    
    return success_count == len(tests)


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
