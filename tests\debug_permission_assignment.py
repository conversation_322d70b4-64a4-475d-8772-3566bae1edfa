#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试权限分配API
"""

import sys
import os
import json

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(__file__)))

from test.conftest import TestBase


def debug_permission_assignment():
    """调试权限分配API"""
    api_test = TestBase()
    
    # 管理员登录
    admin_token = api_test.login("admin", "7c222fb2927d828af22f592134e8932480637c0d")
    if not admin_token:
        print("❌ 管理员登录失败")
        return
    
    print("✅ 管理员登录成功")
    
    # 创建一个测试角色
    print("\n=== 创建测试角色 ===")
    role_data = {
        "name": "权限测试角色",
        "code": "permission_test_role",
        "description": "用于测试权限分配的角色",
        "is_enabled": True
    }
    
    status_code, response = api_test.make_request(
        "POST", "/roles", admin_token, data=role_data
    )
    
    print(f"创建角色状态码: {status_code}")
    print(f"创建角色响应: {json.dumps(response, indent=2, ensure_ascii=False)}")
    
    if status_code != 200:
        print("❌ 创建角色失败，无法继续测试")
        return
    
    # 提取角色ID
    data = response.get("data", {})
    if data.get("success") and data.get("data"):
        role_id = data["data"].get("id")
    else:
        print("❌ 无法提取角色ID")
        return
    
    print(f"✅ 成功创建角色，ID: {role_id}")
    
    # 获取一些权限ID用于测试
    print("\n=== 获取权限列表 ===")
    status_code, response = api_test.make_request("GET", "/permissions?page_size=10", admin_token)
    
    if status_code != 200:
        print("❌ 获取权限列表失败")
        return
    
    data = response.get("data", {})
    permissions = data.get("items", [])
    
    if not permissions:
        print("❌ 没有找到权限")
        return
    
    # 选择前3个权限进行测试
    test_permission_ids = [perm["id"] for perm in permissions[:3]]
    print(f"选择的测试权限ID: {test_permission_ids}")
    
    # 测试权限分配API
    print("\n=== 测试权限分配API ===")
    
    # 尝试不同的请求格式
    test_formats = [
        {"permission_ids": test_permission_ids},
        {"permissions": test_permission_ids},
        {"ids": test_permission_ids},
        test_permission_ids,
    ]
    
    for i, permission_data in enumerate(test_formats):
        print(f"\n--- 测试格式 {i+1} ---")
        print(f"请求数据: {json.dumps(permission_data, indent=2, ensure_ascii=False)}")
        
        status_code, response = api_test.make_request(
            "PUT", f"/roles/{role_id}/permissions", admin_token, data=permission_data
        )
        
        print(f"状态码: {status_code}")
        print(f"响应: {json.dumps(response, indent=2, ensure_ascii=False)}")
        
        if status_code == 200:
            print("✅ 权限分配成功！")
            break
        else:
            print("❌ 权限分配失败")
    
    # 清理测试角色
    print(f"\n=== 清理测试角色 ===")
    delete_status, delete_response = api_test.make_request(
        "DELETE", f"/roles/{role_id}", admin_token
    )
    
    if delete_status == 200:
        print("✅ 测试角色已清理")
    else:
        print(f"⚠️ 清理测试角色失败: {delete_response}")


if __name__ == "__main__":
    debug_permission_assignment()
