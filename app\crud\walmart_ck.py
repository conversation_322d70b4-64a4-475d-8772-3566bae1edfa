from typing import List, Optional, Dict, Any
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_, func, desc
from datetime import date

from app.crud.base import CRUDBase
from app.models.walmart_ck import WalmartCK
from app.schemas.walmart_ck import (
    WalmartCKCreate,
    WalmartCKUpdate,
)


class CRUDWalmartCK(CRUDBase[WalmartCK, WalmartCKCreate, WalmartCKUpdate]):
    """沃尔玛CK配置CRUD操作 - 支持数据隔离和统计功能"""

    def get_by_sign(self, db: Session, sign: str) -> Optional[WalmartCK]:
        """根据签名获取配置"""
        return db.query(self.model).filter(self.model.sign == sign).first()

    def get_multi_by_merchant(
        self,
        db: Session,
        merchant_id: int,
        *,
        skip: int = 0,
        limit: int = 100,
        filters: Optional[Dict[str, Any]] = None,
    ) -> List[WalmartCK]:
        """获取商户的CK配置列表（支持商户级数据隔离）"""
        query = db.query(self.model).filter(self.model.merchant_id == merchant_id)

        # 应用过滤条件
        query = self._apply_filters(query, filters)

        return query.order_by(desc(self.model.created_at)).offset(skip).limit(limit).all()

    def get_multi_by_department(
        self,
        db: Session,
        department_id: int,
        *,
        skip: int = 0,
        limit: int = 100,
        filters: Optional[Dict[str, Any]] = None,
    ) -> List[WalmartCK]:
        """获取部门的CK配置列表（支持部门级数据隔离）"""
        query = db.query(self.model).filter(self.model.department_id == department_id)

        # 应用过滤条件
        query = self._apply_filters(query, filters)

        return query.order_by(desc(self.model.created_at)).offset(skip).limit(limit).all()

    def get_active_cks_for_merchant(
        self,
        db: Session,
        merchant_id: int,
    ) -> List[WalmartCK]:
        """获取商户的所有激活CK（用于绑卡时的CK选择，过滤已删除记录）"""
        return db.query(self.model).filter(
            self.model.merchant_id == merchant_id,
            self.model.active == True,
            self.model.is_deleted == False
        ).order_by(self.model.bind_count.asc()).all()  # 按使用次数升序，实现负载均衡

    def get_available_ck_for_binding(
        self,
        db: Session,
        merchant_id: int,
        department_id: Optional[int] = None,
    ) -> Optional[WalmartCK]:
        """获取可用于绑卡的CK（负载均衡选择，过滤已删除记录）"""
        # 导入Department模型以进行关联查询
        from app.models.department import Department

        # 构建基础查询，加入部门表关联以检查部门开关状态
        query = db.query(self.model).join(
            Department, self.model.department_id == Department.id
        ).filter(
            self.model.merchant_id == merchant_id,
            self.model.active == True,
            self.model.bind_count < self.model.total_limit,
            self.model.is_deleted == False,
            # 新增：只选择启用进单的部门的CK
            Department.status == True,
            Department.enable_binding == True,
            Department.binding_weight > 0
        )

        # 如果指定了部门，优先选择部门的CK
        if department_id:
            dept_ck = query.filter(self.model.department_id == department_id).order_by(
                self.model.bind_count.asc()
            ).first()
            if dept_ck:
                return dept_ck

        # 否则选择商户下使用次数最少的CK
        return query.order_by(self.model.bind_count.asc()).first()

    def get_available_ck_with_weight_algorithm(
        self,
        db: Session,
        merchant_id: int,
        department_id: Optional[int] = None,
    ) -> Optional[WalmartCK]:
        """基于权重算法获取可用于绑卡的CK - 修复负载均衡问题"""
        import secrets  # 使用cryptographically secure random
        from app.models.department import Department

        # 获取所有可用的部门及其权重
        available_departments = db.query(Department).filter(
            Department.merchant_id == merchant_id,
            Department.status == True,
            Department.enable_binding == True,
            Department.binding_weight > 0
        ).all()

        if not available_departments:
            return None

        # 如果指定了部门ID，检查该部门是否可用
        if department_id:
            target_dept = next((d for d in available_departments if d.id == department_id), None)
            if target_dept:
                # 直接从指定部门获取CK
                return self._get_ck_from_department(db, merchant_id, department_id)

        # 计算权重总和
        total_weight = sum(dept.binding_weight for dept in available_departments)

        # 【修复】使用cryptographically secure random确保真正的随机性
        random_value = secrets.randbelow(total_weight) + 1
        current_weight = 0

        for dept in available_departments:
            current_weight += dept.binding_weight
            if random_value <= current_weight:
                # 选中这个部门，获取其可用CK
                ck = self._get_ck_from_department(db, merchant_id, dept.id)
                if ck:
                    return ck

        # 如果权重算法没有找到可用CK，回退到传统方法
        return self.get_available_ck_for_binding(db, merchant_id, department_id)

    def _get_ck_from_department(
        self,
        db: Session,
        merchant_id: int,
        department_id: int,
    ) -> Optional[WalmartCK]:
        """从指定部门获取可用CK - 修复负载均衡问题"""
        import secrets

        # 获取负载最低的前3个CK作为候选
        candidates = db.query(self.model).filter(
            self.model.merchant_id == merchant_id,
            self.model.department_id == department_id,
            self.model.active == True,
            self.model.bind_count < self.model.total_limit,
            self.model.is_deleted == False
        ).order_by(self.model.bind_count.asc()).limit(3).all()

        if not candidates:
            return None

        # 【修复】从候选CK中随机选择，确保负载均衡
        return secrets.choice(candidates)

    def _apply_filters(self, query, filters: Optional[Dict[str, Any]]):
        """应用通用过滤条件"""
        if not filters:
            return query

        # 商户ID过滤
        if filters.get("merchant_id"):
            query = query.filter(self.model.merchant_id == filters["merchant_id"])

        # 部门ID过滤
        if filters.get("department_id"):
            query = query.filter(self.model.department_id == filters["department_id"])

        # 签名过滤
        if filters.get("sign"):
            query = query.filter(self.model.sign.like(f"%{filters['sign']}%"))

        # 激活状态过滤
        if filters.get("active") is not None:
            query = query.filter(self.model.active == filters["active"])

        # 描述过滤
        if filters.get("description"):
            query = query.filter(self.model.description.like(f"%{filters['description']}%"))

        # 时间范围过滤
        if filters.get("start_time") and filters.get("end_time"):
            query = query.filter(
                and_(
                    self.model.created_at >= filters["start_time"],
                    self.model.created_at <= filters["end_time"],
                )
            )
        elif filters.get("start_time"):
            query = query.filter(self.model.created_at >= filters["start_time"])
        elif filters.get("end_time"):
            query = query.filter(self.model.created_at <= filters["end_time"])

        return query

    def increment_bind_count(self, db: Session, ck_id: int) -> bool:
        """增加CK的绑卡计数"""
        ck = self.get(db, ck_id)
        if ck:
            ck.bind_count += 1
            db.commit()
            return True
        return False

    def reset_daily_counts(self, db: Session) -> int:
        """重置所有CK的每日计数（已废弃，保留兼容性）

        注意：由于CK限制机制已改为总次数限制，此方法已不再使用
        bind_count现在表示累计绑卡数量，不应重置
        """
        # 不再执行重置操作，返回0表示没有重置任何记录
        return 0


walmart_ck = CRUDWalmartCK(WalmartCK)
