"""
关键词路由处理器
统一处理所有关键词，将其路由到对应的处理器方法
"""

from typing import Dict, Any, Optional
from telegram import Update
from telegram.ext import ContextTypes

from app.core.logging import get_logger
from .stats_handler import StatsCommandHandler
from .ck_stats_handler import CKStatsCommandHandler
from .bind_handler import BindCommandHandler

logger = get_logger(__name__)


class KeywordRouter:
    """关键词路由处理器"""
    
    def __init__(self, db, config, rate_limiter):
        self.db = db
        self.config = config
        self.rate_limiter = rate_limiter
        self.logger = logger
        
        # 初始化处理器
        self.stats_handler = StatsCommandHandler(db, config, rate_limiter)
        self.ck_stats_handler = CKStatsCommandHandler(db, config, rate_limiter)
        self.bind_handler = BindCommandHandler(db, config, rate_limiter)
        
        # 关键词到处理方法的映射
        self.keyword_mappings = self._build_keyword_mappings()
    
    def _build_keyword_mappings(self) -> Dict[str, tuple]:
        """构建关键词到处理方法的映射"""
        return {
            # 基础统计关键词
            "今日数据": (self.stats_handler, "handle_today"),
            "昨日数据": (self.stats_handler, "handle_yesterday"),
            "本周数据": (self.stats_handler, "handle_week"),
            "本月数据": (self.stats_handler, "handle_month"),
            
            # CK统计关键词 - 支持所有大小写组合
            "今日CK": (self.ck_stats_handler, "handle_today"),
            "今日ck": (self.ck_stats_handler, "handle_today"),
            "今日Ck": (self.ck_stats_handler, "handle_today"),
            "今日cK": (self.ck_stats_handler, "handle_today"),
            "昨日CK": (self.ck_stats_handler, "handle_yesterday"),
            "昨日ck": (self.ck_stats_handler, "handle_yesterday"),
            "昨日Ck": (self.ck_stats_handler, "handle_yesterday"),
            "昨日cK": (self.ck_stats_handler, "handle_yesterday"),
            "本周CK": (self.ck_stats_handler, "handle_week"),
            "本周ck": (self.ck_stats_handler, "handle_week"),
            "本周Ck": (self.ck_stats_handler, "handle_week"),
            "本周cK": (self.ck_stats_handler, "handle_week"),
            "本月CK": (self.ck_stats_handler, "handle_month"),
            "本月ck": (self.ck_stats_handler, "handle_month"),
            "本月Ck": (self.ck_stats_handler, "handle_month"),
            "本月cK": (self.ck_stats_handler, "handle_month"),
            
            # 状态查询关键词
            "查看状态": (self.bind_handler, "handle_status"),
            "当前状态": (self.bind_handler, "handle_status"),
            "绑定状态": (self.bind_handler, "handle_status"),
            "群组状态": (self.bind_handler, "handle_status"),
            
        }
    
    async def route_keyword(self, update: Update, context: ContextTypes.DEFAULT_TYPE, matched_keywords: list) -> Optional[Dict[str, Any]]:
        """
        根据匹配的关键词路由到对应的处理方法
        
        Args:
            update: Telegram更新对象
            context: 上下文对象
            matched_keywords: 匹配的关键词列表
            
        Returns:
            处理结果或None
        """
        try:
            # 按优先级处理关键词（精确匹配优先）
            for keyword in matched_keywords:
                if keyword in self.keyword_mappings:
                    handler, method_name = self.keyword_mappings[keyword]
                    method = getattr(handler, method_name)
                    
                    self.logger.info(f"路由关键词 '{keyword}' 到 {handler.__class__.__name__}.{method_name}")
                    
                    try:
                        result = await method(update, context)
                        return result
                    except Exception as e:
                        self.logger.error(f"处理关键词 '{keyword}' 失败: {e}")
                        # 继续尝试其他关键词
                        continue
            
            # 如果没有精确匹配，尝试模糊匹配
            return await self._handle_fuzzy_match(update, context, matched_keywords)
            
        except Exception as e:
            self.logger.error(f"关键词路由失败: {e}")
            return None
    
    async def _handle_fuzzy_match(self, update: Update, context: ContextTypes.DEFAULT_TYPE, matched_keywords: list) -> Optional[Dict[str, Any]]:
        """处理模糊匹配的关键词"""
        message_text = update.message.text.lower() if update.message and update.message.text else ""
        
        # CK统计模糊匹配 - 支持"今日CK"格式
        if any(keyword in message_text for keyword in ["ck", "CK", "Ck", "cK"]):
            if any(keyword in message_text for keyword in ["今日", "今天", "today"]):
                return await self.ck_stats_handler.handle_today(update, context)
            elif any(keyword in message_text for keyword in ["昨日", "昨天", "yesterday"]):
                return await self.ck_stats_handler.handle_yesterday(update, context)
            elif any(keyword in message_text for keyword in ["本周", "这周", "week"]):
                return await self.ck_stats_handler.handle_week(update, context)
            elif any(keyword in message_text for keyword in ["本月", "这月", "month"]):
                return await self.ck_stats_handler.handle_month(update, context)
            else:
                # 默认今日CK统计
                return await self.ck_stats_handler.handle_today(update, context)
        
        # 基础统计模糊匹配
        elif any(keyword in message_text for keyword in ["数据", "统计", "绑卡"]):
            if any(keyword in message_text for keyword in ["今日", "今天", "today"]):
                return await self.stats_handler.handle_today(update, context)
            elif any(keyword in message_text for keyword in ["昨日", "昨天", "yesterday"]):
                return await self.stats_handler.handle_yesterday(update, context)
            elif any(keyword in message_text for keyword in ["本周", "这周", "week"]):
                return await self.stats_handler.handle_week(update, context)
            elif any(keyword in message_text for keyword in ["本月", "这月", "month"]):
                return await self.stats_handler.handle_month(update, context)
            else:
                # 默认今日统计
                return await self.stats_handler.handle_today(update, context)
        
        # 状态查询模糊匹配
        elif any(keyword in message_text for keyword in ["状态", "status", "绑定"]):
            return await self.bind_handler.handle_status(update, context)
        
        return None
    
    def get_supported_keywords(self) -> list:
        """获取支持的关键词列表"""
        return list(self.keyword_mappings.keys())
    
    def get_keyword_help(self) -> str:
        """获取关键词帮助信息"""
        help_text = """📋 **支持的统计查询关键词**

📊 **基础统计**：
• `今日数据` - 查看今日绑卡统计
• `昨日数据` - 查看昨日绑卡统计  
• `本周数据` - 查看本周绑卡统计
• `本月数据` - 查看本月绑卡统计

🔑 **CK统计**：
• `今日CK` / `今日ck` - 查看今日CK使用统计
• `昨日CK` / `昨日ck` - 查看昨日CK使用统计
• `本周CK` / `本周ck` - 查看本周CK使用统计
• `本月CK` / `本月ck` - 查看本月CK使用统计

📋 **状态查询**：
• `状态` / `status` - 查看群组绑定状态

💡 **使用提示**：
• 直接发送关键词即可查询
• 支持大小写混合
• 支持自然语言表达"""
        
        return help_text


# 创建全局实例
_keyword_router = None


def get_keyword_router(db, config, rate_limiter) -> KeywordRouter:
    """获取关键词路由器实例"""
    global _keyword_router
    if _keyword_router is None:
        _keyword_router = KeywordRouter(db, config, rate_limiter)
    return _keyword_router
