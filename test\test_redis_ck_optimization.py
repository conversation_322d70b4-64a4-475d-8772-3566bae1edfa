#!/usr/bin/env python3
"""
Redis CK优化方案集成测试
测试并发性能和负载均衡效果
"""

import sys
import os
import asyncio
import json
import time
import random
from datetime import datetime
from typing import Dict, Any, List
from concurrent.futures import ThreadPoolExecutor

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app.db.session import SessionLocal
from app.models.walmart_ck import WalmartCK
from app.models.merchant import Merchant
from app.models.department import Department
from app.services.redis_ck_service import RedisOptimizedCKService, CKRecoveryService, CKRedisMonitor
from app.core.redis_config import redis_manager, RedisConfig
from app.core.logging import get_logger

logger = get_logger("redis_ck_optimization_test")


class RedisCKOptimizationTest:
    """Redis CK优化测试类"""
    
    def __init__(self):
        self.db = SessionLocal()
        self.redis_client = None
        self.redis_service = None
        self.test_data = {}
        self.test_results = []
    
    async def __aenter__(self):
        # 初始化Redis连接
        await redis_manager.initialize()
        self.redis_client = await redis_manager.get_client()
        self.redis_service = RedisOptimizedCKService(self.db, self.redis_client)
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        await self.cleanup()
        await redis_manager.close()
        self.db.close()
    
    async def cleanup(self):
        """清理测试数据"""
        try:
            # 清理Redis数据
            if self.redis_client:
                # 清理测试相关的Redis key
                patterns = [
                    "walmart:ck:pool:*",
                    "walmart:ck:status:*",
                    "walmart:ck:lock:*",
                    "walmart:ck:validation:*",
                    "walmart:ck:dept:*"
                ]
                
                for pattern in patterns:
                    keys = []
                    async for key in self.redis_client.scan_iter(match=pattern):
                        keys.append(key)
                    
                    if keys:
                        await self.redis_client.delete(*keys)
            
            # 清理数据库测试数据
            if 'test_cks' in self.test_data:
                for ck_id in self.test_data['test_cks']:
                    ck = self.db.query(WalmartCK).filter(WalmartCK.id == ck_id).first()
                    if ck:
                        self.db.delete(ck)
            
            if 'test_department' in self.test_data:
                dept = self.db.query(Department).filter(Department.id == self.test_data['test_department']).first()
                if dept:
                    self.db.delete(dept)
            
            if 'test_merchant' in self.test_data:
                merchant = self.db.query(Merchant).filter(Merchant.id == self.test_data['test_merchant']).first()
                if merchant:
                    self.db.delete(merchant)
            
            self.db.commit()
            logger.info("测试数据清理完成")
            
        except Exception as e:
            self.db.rollback()
            logger.error(f"清理测试数据失败: {e}")
    
    async def create_test_data(self):
        """创建测试数据"""
        try:
            # 创建测试商户
            merchant = Merchant(
                name="Redis优化测试商户",
                code="REDIS_OPT_TEST",
                status="active"
            )
            self.db.add(merchant)
            self.db.commit()
            self.db.refresh(merchant)
            self.test_data['test_merchant'] = merchant.id
            
            # 创建测试部门
            department = Department(
                name="Redis优化测试部门",
                code="REDIS_OPT_DEPT",
                merchant_id=merchant.id,
                status="active"
            )
            self.db.add(department)
            self.db.commit()
            self.db.refresh(department)
            self.test_data['test_department'] = department.id
            
            # 创建测试CK（更多数量用于并发测试）
            test_cks = []
            for i in range(20):  # 创建20个CK
                ck = WalmartCK(
                    sign=f"redis_test_ck_{i}_{datetime.now().timestamp()}",
                    total_limit=100,  # 设置较大的限制
                    bind_count=random.randint(0, 10),  # 随机初始使用次数
                    active=True,
                    merchant_id=merchant.id,
                    department_id=department.id,
                    is_deleted=False,
                    description=f"Redis优化测试CK_{i}"
                )
                self.db.add(ck)
                test_cks.append(ck)
            
            self.db.commit()
            
            # 刷新并保存CK ID
            self.test_data['test_cks'] = []
            for ck in test_cks:
                self.db.refresh(ck)
                self.test_data['test_cks'].append(ck.id)
            
            # 初始化Redis CK池
            await self.redis_service.sync_service.initialize_merchant_ck_pool(merchant.id)
            
            logger.info(f"创建测试数据完成: 商户{merchant.id}, 部门{department.id}, CK数量{len(test_cks)}")
            return True
            
        except Exception as e:
            self.db.rollback()
            logger.error(f"创建测试数据失败: {e}")
            return False
    
    async def test_concurrent_ck_selection(self, concurrent_requests: int = 50):
        """测试并发CK选择"""
        logger.info(f"开始并发CK选择测试，并发数: {concurrent_requests}")
        
        start_time = time.time()
        results = []
        
        async def single_request():
            """单个CK选择请求"""
            try:
                request_start = time.time()
                ck = await self.redis_service.get_available_ck_optimized(
                    self.test_data['test_merchant'],
                    self.test_data['test_department']
                )
                request_time = time.time() - request_start
                
                if ck:
                    # 模拟绑卡处理
                    await asyncio.sleep(0.01)  # 模拟处理时间
                    
                    # 记录使用
                    await self.redis_service.record_ck_usage_optimized(
                        ck.id,
                        self.test_data['test_merchant'],
                        self.test_data['test_department'],
                        True
                    )
                    
                    return {
                        'success': True,
                        'ck_id': ck.id,
                        'response_time': request_time
                    }
                else:
                    return {
                        'success': False,
                        'ck_id': None,
                        'response_time': request_time
                    }
                    
            except Exception as e:
                return {
                    'success': False,
                    'error': str(e),
                    'response_time': time.time() - request_start
                }
        
        # 并发执行请求
        tasks = [single_request() for _ in range(concurrent_requests)]
        results = await asyncio.gather(*tasks)
        
        total_time = time.time() - start_time
        
        # 分析结果
        successful_requests = [r for r in results if r['success']]
        failed_requests = [r for r in results if not r['success']]
        
        response_times = [r['response_time'] for r in results]
        avg_response_time = sum(response_times) / len(response_times)
        
        # 分析CK分布
        ck_usage = {}
        for result in successful_requests:
            ck_id = result['ck_id']
            ck_usage[ck_id] = ck_usage.get(ck_id, 0) + 1
        
        # 计算负载分布方差
        usage_values = list(ck_usage.values())
        if usage_values:
            mean_usage = sum(usage_values) / len(usage_values)
            variance = sum((x - mean_usage) ** 2 for x in usage_values) / len(usage_values)
        else:
            variance = 0
        
        test_result = {
            'test_name': 'concurrent_ck_selection',
            'concurrent_requests': concurrent_requests,
            'total_time': total_time,
            'successful_requests': len(successful_requests),
            'failed_requests': len(failed_requests),
            'success_rate': len(successful_requests) / concurrent_requests * 100,
            'avg_response_time': avg_response_time,
            'qps': concurrent_requests / total_time,
            'ck_usage_distribution': ck_usage,
            'load_variance': variance,
            'unique_cks_used': len(ck_usage)
        }
        
        self.test_results.append(test_result)
        
        logger.info(f"并发测试完成 - 成功率: {test_result['success_rate']:.1f}%, "
                   f"平均响应时间: {avg_response_time*1000:.1f}ms, "
                   f"QPS: {test_result['qps']:.1f}, "
                   f"使用CK数: {len(ck_usage)}")
        
        return test_result
    
    async def test_load_balancing_accuracy(self):
        """测试负载均衡准确性"""
        logger.info("开始负载均衡准确性测试...")
        
        try:
            # 重置所有CK的使用次数
            for ck_id in self.test_data['test_cks']:
                ck = self.db.query(WalmartCK).filter(WalmartCK.id == ck_id).first()
                if ck:
                    ck.bind_count = 0
                    self.db.commit()
            
            # 重新初始化Redis池
            await self.redis_service.sync_service.initialize_merchant_ck_pool(
                self.test_data['test_merchant']
            )
            
            # 执行100次CK选择
            ck_selections = []
            for i in range(100):
                ck = await self.redis_service.get_available_ck_optimized(
                    self.test_data['test_merchant'],
                    self.test_data['test_department']
                )
                
                if ck:
                    ck_selections.append(ck.id)
                    # 记录使用
                    await self.redis_service.record_ck_usage_optimized(
                        ck.id,
                        self.test_data['test_merchant'],
                        self.test_data['test_department'],
                        True
                    )
            
            # 分析分布
            ck_usage = {}
            for ck_id in ck_selections:
                ck_usage[ck_id] = ck_usage.get(ck_id, 0) + 1
            
            # 计算理想分布（均匀分布）
            total_cks = len(self.test_data['test_cks'])
            ideal_usage_per_ck = 100 / total_cks
            
            # 计算分布偏差
            deviations = []
            for ck_id in self.test_data['test_cks']:
                actual_usage = ck_usage.get(ck_id, 0)
                deviation = abs(actual_usage - ideal_usage_per_ck)
                deviations.append(deviation)
            
            avg_deviation = sum(deviations) / len(deviations)
            max_deviation = max(deviations)
            
            # 计算负载均衡准确性（偏差越小越好）
            accuracy = max(0, 100 - (avg_deviation / ideal_usage_per_ck * 100))
            
            test_result = {
                'test_name': 'load_balancing_accuracy',
                'total_selections': 100,
                'ck_usage_distribution': ck_usage,
                'ideal_usage_per_ck': ideal_usage_per_ck,
                'avg_deviation': avg_deviation,
                'max_deviation': max_deviation,
                'accuracy_percentage': accuracy,
                'unique_cks_used': len(ck_usage)
            }
            
            self.test_results.append(test_result)
            
            logger.info(f"负载均衡测试完成 - 准确性: {accuracy:.1f}%, "
                       f"平均偏差: {avg_deviation:.2f}, "
                       f"使用CK数: {len(ck_usage)}")
            
            return test_result
            
        except Exception as e:
            logger.error(f"负载均衡测试失败: {e}")
            return None
    
    async def test_cache_performance(self):
        """测试缓存性能"""
        logger.info("开始缓存性能测试...")
        
        try:
            # 测试验证缓存
            ck_id = self.test_data['test_cks'][0]
            
            # 第一次验证（缓存未命中）
            start_time = time.time()
            result1 = await self.redis_service.validation_cache.get_validation_result(ck_id)
            first_time = time.time() - start_time
            
            # 缓存验证结果
            await self.redis_service.validation_cache.cache_validation_result(
                ck_id, True, ""
            )
            
            # 第二次验证（缓存命中）
            start_time = time.time()
            result2 = await self.redis_service.validation_cache.get_validation_result(ck_id)
            second_time = time.time() - start_time
            
            cache_speedup = first_time / second_time if second_time > 0 else float('inf')
            
            test_result = {
                'test_name': 'cache_performance',
                'cache_miss_time': first_time,
                'cache_hit_time': second_time,
                'speedup_ratio': cache_speedup,
                'cache_hit': result2 is not None and result2.get('cached', False)
            }
            
            self.test_results.append(test_result)
            
            logger.info(f"缓存性能测试完成 - 加速比: {cache_speedup:.1f}x, "
                       f"缓存命中: {test_result['cache_hit']}")
            
            return test_result
            
        except Exception as e:
            logger.error(f"缓存性能测试失败: {e}")
            return None

    async def test_recovery_mechanisms(self):
        """测试恢复机制"""
        logger.info("开始恢复机制测试...")

        try:
            recovery_service = CKRecoveryService(self.redis_client, self.db)

            # 模拟卡住的预占用
            ck_id = self.test_data['test_cks'][0]
            status_key = f"walmart:ck:status:{ck_id}"

            # 设置pending_count但不设置锁
            await self.redis_client.hset(status_key, 'pending_count', 5)

            # 运行恢复
            await recovery_service.recover_stuck_reservations()

            # 检查是否恢复
            pending_count = await self.redis_client.hget(status_key, 'pending_count')

            test_result = {
                'test_name': 'recovery_mechanisms',
                'stuck_reservations_recovered': int(pending_count or 0) == 0,
                'recovery_successful': True
            }

            self.test_results.append(test_result)

            logger.info(f"恢复机制测试完成 - 恢复成功: {test_result['recovery_successful']}")

            return test_result

        except Exception as e:
            logger.error(f"恢复机制测试失败: {e}")
            return None

    async def test_monitoring_metrics(self):
        """测试监控指标"""
        logger.info("开始监控指标测试...")

        try:
            monitor = CKRedisMonitor(self.redis_client)

            # 获取性能指标
            metrics = await monitor.get_performance_metrics()

            test_result = {
                'test_name': 'monitoring_metrics',
                'metrics_available': len(metrics) > 0,
                'redis_info_available': 'redis_info' in metrics,
                'pool_metrics_available': 'ck_pool_status' in metrics,
                'cache_metrics_available': 'cache_hit_rates' in metrics,
                'lock_metrics_available': 'lock_statistics' in metrics,
                'metrics_data': metrics
            }

            self.test_results.append(test_result)

            logger.info(f"监控指标测试完成 - 指标可用: {test_result['metrics_available']}")

            return test_result

        except Exception as e:
            logger.error(f"监控指标测试失败: {e}")
            return None

    async def benchmark_vs_database_method(self):
        """对比数据库方法的性能基准测试"""
        logger.info("开始性能基准对比测试...")

        try:
            from app.services.walmart_ck_service_new import WalmartCKService

            # 数据库方法测试
            db_service = WalmartCKService(self.db)

            # Redis方法测试
            redis_requests = 50
            db_requests = 50

            # 测试Redis方法
            redis_start = time.time()
            redis_tasks = []
            for _ in range(redis_requests):
                task = self.redis_service.get_available_ck_optimized(
                    self.test_data['test_merchant'],
                    self.test_data['test_department']
                )
                redis_tasks.append(task)

            redis_results = await asyncio.gather(*redis_tasks)
            redis_time = time.time() - redis_start
            redis_success = len([r for r in redis_results if r is not None])

            # 测试数据库方法
            db_start = time.time()
            db_success = 0
            for _ in range(db_requests):
                ck = await db_service.get_available_ck(
                    self.test_data['test_merchant'],
                    self.test_data['test_department'],
                    validate_ck=False  # 关闭验证以公平对比
                )
                if ck:
                    db_success += 1

            db_time = time.time() - db_start

            # 计算性能提升
            redis_qps = redis_requests / redis_time
            db_qps = db_requests / db_time

            redis_avg_time = redis_time / redis_requests * 1000  # ms
            db_avg_time = db_time / db_requests * 1000  # ms

            performance_improvement = (db_avg_time - redis_avg_time) / db_avg_time * 100
            qps_improvement = (redis_qps - db_qps) / db_qps * 100

            test_result = {
                'test_name': 'benchmark_vs_database',
                'redis_method': {
                    'total_time': redis_time,
                    'avg_response_time_ms': redis_avg_time,
                    'qps': redis_qps,
                    'success_rate': redis_success / redis_requests * 100
                },
                'database_method': {
                    'total_time': db_time,
                    'avg_response_time_ms': db_avg_time,
                    'qps': db_qps,
                    'success_rate': db_success / db_requests * 100
                },
                'improvements': {
                    'response_time_improvement_percent': performance_improvement,
                    'qps_improvement_percent': qps_improvement
                }
            }

            self.test_results.append(test_result)

            logger.info(f"性能对比测试完成 - 响应时间提升: {performance_improvement:.1f}%, "
                       f"QPS提升: {qps_improvement:.1f}%")

            return test_result

        except Exception as e:
            logger.error(f"性能对比测试失败: {e}")
            return None

    def generate_test_report(self):
        """生成测试报告"""
        total_tests = len(self.test_results)
        passed_tests = sum(1 for r in self.test_results if r.get('success', True))

        # 汇总性能数据
        performance_summary = {}
        for result in self.test_results:
            if result['test_name'] == 'concurrent_ck_selection':
                performance_summary['concurrent_performance'] = {
                    'qps': result['qps'],
                    'avg_response_time_ms': result['avg_response_time'] * 1000,
                    'success_rate': result['success_rate']
                }
            elif result['test_name'] == 'load_balancing_accuracy':
                performance_summary['load_balancing'] = {
                    'accuracy_percentage': result['accuracy_percentage'],
                    'avg_deviation': result['avg_deviation']
                }
            elif result['test_name'] == 'benchmark_vs_database':
                performance_summary['vs_database'] = result['improvements']

        report = {
            "test_summary": {
                "total_tests": total_tests,
                "passed_tests": passed_tests,
                "failed_tests": total_tests - passed_tests,
                "success_rate": f"{passed_tests/total_tests*100:.1f}%" if total_tests > 0 else "0%"
            },
            "performance_summary": performance_summary,
            "test_details": self.test_results,
            "test_data": self.test_data,
            "timestamp": datetime.now().isoformat()
        }

        return report

    async def run_all_tests(self):
        """运行所有测试"""
        logger.info("开始Redis CK优化方案集成测试...")

        # 创建测试数据
        if not await self.create_test_data():
            logger.error("创建测试数据失败，终止测试")
            return False

        # 运行各项测试
        tests = [
            ("并发CK选择测试", lambda: self.test_concurrent_ck_selection(50)),
            ("负载均衡准确性测试", self.test_load_balancing_accuracy),
            ("缓存性能测试", self.test_cache_performance),
            ("恢复机制测试", self.test_recovery_mechanisms),
            ("监控指标测试", self.test_monitoring_metrics),
            ("性能基准对比测试", self.benchmark_vs_database_method),
        ]

        for test_name, test_func in tests:
            logger.info(f"执行测试: {test_name}")
            try:
                await test_func()
            except Exception as e:
                logger.error(f"测试 {test_name} 执行异常: {e}")

        # 生成测试报告
        report = self.generate_test_report()

        # 输出测试结果
        print("\n" + "="*80)
        print("Redis CK优化方案集成测试报告")
        print("="*80)
        print(f"总测试数: {report['test_summary']['total_tests']}")
        print(f"通过测试: {report['test_summary']['passed_tests']}")
        print(f"失败测试: {report['test_summary']['failed_tests']}")
        print(f"成功率: {report['test_summary']['success_rate']}")
        print("\n性能汇总:")

        if 'concurrent_performance' in report['performance_summary']:
            perf = report['performance_summary']['concurrent_performance']
            print(f"  并发性能: {perf['qps']:.1f} QPS, "
                  f"平均响应时间: {perf['avg_response_time_ms']:.1f}ms, "
                  f"成功率: {perf['success_rate']:.1f}%")

        if 'load_balancing' in report['performance_summary']:
            lb = report['performance_summary']['load_balancing']
            print(f"  负载均衡: 准确性 {lb['accuracy_percentage']:.1f}%, "
                  f"平均偏差: {lb['avg_deviation']:.2f}")

        if 'vs_database' in report['performance_summary']:
            vs_db = report['performance_summary']['vs_database']
            print(f"  vs数据库方法: 响应时间提升 {vs_db['response_time_improvement_percent']:.1f}%, "
                  f"QPS提升 {vs_db['qps_improvement_percent']:.1f}%")

        print("="*80)

        for result in report['test_details']:
            status = "✅ 通过" if result.get('success', True) else "❌ 失败"
            print(f"{result['test_name']}: {status}")

        print("="*80)

        # 保存详细报告
        report_file = f"redis_ck_optimization_test_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2)

        logger.info(f"详细测试报告已保存到: {report_file}")

        return report['test_summary']['failed_tests'] == 0


async def main():
    """主函数"""
    async with RedisCKOptimizationTest() as test:
        success = await test.run_all_tests()
        return 0 if success else 1


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
