from typing import List, Optional, Literal
from pydantic import BaseModel, Field
from datetime import datetime


# 使用字符串常量代替枚举
class MenuTypeConstants:
    """菜单类型常量"""

    MENU = "MENU"
    BUTTON = "BUTTON"
    LINK = "LINK"


# 菜单类型定义为字符串字面量类型
MenuType = Literal["MENU", "BUTTON", "LINK"]


class MenuBase(BaseModel):
    """菜单基础模型"""

    name: str = Field(..., description="菜单名称", max_length=100)
    code: str = Field(..., description="菜单代码", max_length=50)
    path: Optional[str] = Field(None, description="菜单路径", max_length=200)
    component: Optional[str] = Field(None, description="组件路径", max_length=200)
    icon: Optional[str] = Field(None, description="图标", max_length=100)
    parent_id: Optional[int] = Field(None, description="父菜单ID")
    level: int = Field(1, description="菜单层级")
    sort_order: int = Field(0, description="排序号")
    is_visible: bool = Field(True, description="是否可见")
    is_enabled: bool = Field(True, description="是否启用")
    menu_type: MenuType = Field(MenuTypeConstants.MENU, description="菜单类型")
    description: Optional[str] = Field(None, description="菜单描述")


class MenuCreate(MenuBase):
    """创建菜单时的模型"""

    pass


class MenuUpdate(BaseModel):
    """更新菜单时的模型"""

    name: Optional[str] = Field(None, description="菜单名称", max_length=100)
    code: Optional[str] = Field(None, description="菜单代码", max_length=50)
    path: Optional[str] = Field(None, description="菜单路径", max_length=200)
    component: Optional[str] = Field(None, description="组件路径", max_length=200)
    icon: Optional[str] = Field(None, description="图标", max_length=100)
    parent_id: Optional[int] = Field(None, description="父菜单ID")
    level: Optional[int] = Field(None, description="菜单层级")
    sort_order: Optional[int] = Field(None, description="排序号")
    is_visible: Optional[bool] = Field(None, description="是否可见")
    is_enabled: Optional[bool] = Field(None, description="是否启用")
    menu_type: Optional[MenuType] = Field(None, description="菜单类型")
    description: Optional[str] = Field(None, description="菜单描述")


class MenuInDB(MenuBase):
    """数据库中的菜单模型"""

    id: int
    created_at: datetime
    updated_at: Optional[datetime] = None

    class Config:
        from_attributes = True


class MenuResponse(MenuInDB):
    """菜单响应模型"""

    roles: Optional[List["RoleSimple"]] = None
    permissions: Optional[List["PermissionSimple"]] = None
    children: Optional[List["MenuResponse"]] = None


class MenuTreeNode(BaseModel):
    """菜单树节点模型"""

    id: int
    name: str
    code: str
    path: Optional[str] = None
    component: Optional[str] = None
    icon: Optional[str] = None
    menu_type: MenuType
    level: int
    is_visible: bool
    is_enabled: bool
    sort_order: int
    parent_id: Optional[int] = None
    description: Optional[str] = None
    children: List["MenuTreeNode"] = []

    class Config:
        from_attributes = True


class MenuTree(BaseModel):
    """菜单树响应模型"""

    menu_tree: List[MenuTreeNode]


class MenuQuery(BaseModel):
    """菜单查询模型"""

    name: Optional[str] = Field(None, description="菜单名称（模糊查询）")
    code: Optional[str] = Field(None, description="菜单代码（模糊查询）")
    menu_type: Optional[MenuType] = Field(None, description="菜单类型")
    is_visible: Optional[bool] = Field(None, description="是否可见")
    is_enabled: Optional[bool] = Field(None, description="是否启用")
    parent_id: Optional[int] = Field(None, description="父菜单ID")
    include_roles: bool = Field(False, description="是否包含角色信息")
    include_permissions: bool = Field(False, description="是否包含权限信息")


class MenuPermissions(BaseModel):
    """菜单权限分配模型"""

    menu_id: int = Field(..., description="菜单ID")
    permission_ids: List[int] = Field(..., description="权限ID列表")


# 简单模型定义（避免循环导入）
class RoleSimple(BaseModel):
    """角色简单模型"""

    id: int
    name: str
    code: str


class PermissionSimple(BaseModel):
    """权限简单模型"""

    id: int
    name: str
    code: str


# 重建模型以解决前向引用
MenuTreeNode.model_rebuild()
MenuResponse.model_rebuild()
