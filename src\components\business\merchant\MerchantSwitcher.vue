<template>
  <div class="merchant-switcher">
    <el-popover placement="bottom" :width="320" trigger="click" popper-class="merchant-popover"
      v-model:visible="popoverVisible">
      <template #reference>
        <div class="merchant-selector" :class="{ 'is-active': merchantStore.isMerchantMode }"
          @keydown.space.prevent="popoverVisible = !popoverVisible" tabindex="0" role="button" aria-haspopup="true"
          aria-expanded="popoverVisible">
          <transition name="merchant-name-fade" mode="out-in">
            <span class="merchant-name" :key="merchantStore.currentMerchantName">{{
              merchantStore.currentMerchantName
            }}</span>
          </transition>
          <el-icon class="el-icon--right">
            <arrow-down />
          </el-icon>
        </div>
      </template>

      <div class="merchant-content" @keydown.esc="closePopover" @keydown.down.prevent="navigateDown"
        @keydown.up.prevent="navigateUp" @keydown.enter.prevent="selectFocusedItem" tabindex="0" ref="popoverContent">
        <div class="merchant-header">
          <h3>切换商家</h3>
        </div>

        <div class="merchant-search">
          <el-input v-model="searchKeyword" placeholder="搜索商家名称或编码，空格分隔多关键词" prefix-icon="Search" clearable
            @keydown.esc="clearSearch" @keydown.enter="focusFirstResult" @keydown.down.prevent="focusResults"
            ref="searchInput" autofocus />
        </div>

        <div class="merchant-list" v-loading="merchantStore.loading">
          <!-- 全局视图选项 -->
          <div class="merchant-item"
            :class="{ active: !merchantStore.currentMerchant, 'keyboard-focused': focusedIndex === -1 }"
            @click="switchToGlobal" ref="globalView" @keydown.enter.prevent="switchToGlobal" tabindex="0">
            <el-icon>
              <Monitor />
            </el-icon>
            <span>全局视图 (所有商家)</span>
          </div>

          <!-- 收藏的商家 -->
          <template v-if="favoriteMerchants.length > 0">
            <div class="merchant-group-title">收藏的商家</div>
            <div v-for="(merchant, index) in favoriteMerchants" :key="'favorite-' + merchant.id" class="merchant-item"
              :class="{
                active: merchantStore.currentMerchant?.id === merchant.id,
                'keyboard-focused': focusedIndex === index && focusedSection === 'favorites'
              }" @click="switchMerchant(merchant)" tabindex="0" @keydown.enter.prevent="switchMerchant(merchant)">
              <el-icon>
                <star-filled />
              </el-icon>
              <span>{{ merchant.name }}</span>
              <el-icon class="action-icon" @click.stop="toggleFavorite(merchant)">
                <close />
              </el-icon>
            </div>
            <el-divider />
          </template>

          <!-- 最近访问 -->
          <template v-if="merchantStore.recentMerchants.length > 0 && !searchKeyword">
            <div class="merchant-group-title">最近访问</div>
            <div v-for="(merchant, index) in merchantStore.recentMerchants" :key="'recent-' + merchant.id"
              class="merchant-item" :class="{
                active: merchantStore.currentMerchant?.id === merchant.id,
                'keyboard-focused': focusedIndex === index && focusedSection === 'recent'
              }" @click="switchMerchant(merchant)" tabindex="0" @keydown.enter.prevent="switchMerchant(merchant)">
              <el-icon>
                <Star />
              </el-icon>
              <span>{{ merchant.name }}</span>
              <el-icon class="action-icon" @click.stop="toggleFavorite(merchant)">
                <star-filled v-if="isFavorite(merchant)" />
                <star v-else />
              </el-icon>
            </div>
            <el-divider />
          </template>

          <!-- 所有商家 -->
          <div class="merchant-group-title">
            {{ searchKeyword ? '搜索结果' : '所有商家' }}
            <span v-if="searchKeyword" class="result-count">({{ filteredMerchants.length }})</span>
          </div>
          <div class="merchant-scroll">
            <!-- 搜索结果或分组展示 -->
            <template v-if="searchKeyword">
              <div v-virtual-scroll="{ list: filteredMerchants, itemHeight: 36 }">
                <div v-for="(merchant, index) in filteredMerchants" :key="merchant.id" class="merchant-item" :class="{
                  active: merchantStore.currentMerchant?.id === merchant.id,
                  'keyboard-focused': focusedIndex === index && focusedSection === 'search'
                }" @click="switchMerchant(merchant)" :ref="el => { if (index === 0) firstResultRef = el }" tabindex="0"
                  @keydown.enter.prevent="switchMerchant(merchant)">
                  <el-icon>
                    <Shop />
                  </el-icon>
                  <span v-html="highlightMatch(merchant.name)"></span>
                  <el-icon class="action-icon" @click.stop="toggleFavorite(merchant)">
                    <star-filled v-if="isFavorite(merchant)" />
                    <star v-else />
                  </el-icon>
                </div>
              </div>
            </template>
            <template v-else>
              <!-- 分组展示 -->
              <el-collapse v-model="activeGroups" accordion>
                <el-collapse-item v-for="group in merchantStore.merchantGroups" :key="group.id" :name="group.id">
                  <template #title>
                    <div class="group-title">
                      <span>{{ group.name }}</span>
                      <span class="group-count">({{ group.merchants.length }})</span>
                    </div>
                  </template>

                  <!-- 分组内的租户 -->
                  <div v-for="(merchant, index) in group.merchants" :key="merchant.id" class="merchant-item" :class="{
                    active: merchantStore.currentMerchant?.id === merchant.id,
                    'keyboard-focused': focusedIndex === index && focusedSection === 'group-' + group.id
                  }" @click="switchMerchant(merchant)" tabindex="0" @keydown.enter.prevent="switchMerchant(merchant)">
                    <el-icon>
                      <Shop />
                    </el-icon>
                    <span>{{ merchant.name }}</span>
                    <el-icon class="action-icon" @click.stop="toggleFavorite(merchant)">
                      <star-filled v-if="isFavorite(merchant)" />
                      <star v-else />
                    </el-icon>
                  </div>
                </el-collapse-item>
              </el-collapse>
            </template>

            <div v-if="filteredMerchants.length === 0 && !searchKeyword.length === 0 && !merchantStore.loading"
              class="no-data">
              <el-empty description="未找到匹配的商家" :image-size="60" />
            </div>
          </div>
        </div>
      </div>
    </el-popover>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch, nextTick } from 'vue'
import { ArrowDown, Monitor, Star, Shop, Search, Close, StarFilled } from '@element-plus/icons-vue'
import { useMerchantStore } from '@/store/modules/merchant'
import { useUserStore } from '@/store/modules/user'


// 创建虚拟滚动指令 - 使用Vue 3的指令定义方式
const vVirtualScroll = {
  mounted(el, binding) {
    const { list, itemHeight } = binding.value
    const containerHeight = 240 // 与CSS中.merchant-scroll的max-height保持一致

    // 只有当列表数量足够大才启用虚拟滚动
    if (list.length > 50) {
      let lastScrollTop = 0
      let visibleCount = Math.ceil(containerHeight / itemHeight) + 2
      let startIndex = 0
      let endIndex = Math.min(startIndex + visibleCount, list.length)

      // 存储所有项的引用
      const allItems = Array.from(el.children)

      // 初始状态：只显示可见范围内的项
      updateVisibility()

      // 监听滚动事件
      el.addEventListener('scroll', () => {
        const scrollTop = el.scrollTop
        const direction = scrollTop > lastScrollTop ? 'down' : 'up'
        lastScrollTop = scrollTop

        // 计算可见范围
        startIndex = Math.floor(scrollTop / itemHeight)
        startIndex = Math.max(0, startIndex - 2) // 多显示2个项缓冲
        endIndex = startIndex + visibleCount + 2 // 尾部多显示2个项目缓冲
        endIndex = Math.min(endIndex, list.length)

        updateVisibility()
      })

      function updateVisibility() {
        allItems.forEach((item, index) => {
          if (index >= startIndex && index < endIndex) {
            item.style.display = ''
          } else {
            item.style.display = 'none'
          }
        })
      }
    }
  },
  updated(el, binding) {
    // 如果列表数据更新，重新应用指令效果
    const { list } = binding.value
    if (list.length > 50) {
      // 这里简单处理，当数据更新时重新计算
      // 实际项目可能需要更复杂的逻辑
      el.scrollTop = 0
    }
  }
}

const merchantStore = useMerchantStore()
const userStore = useUserStore()
const searchKeyword = ref('')
const firstResultRef = ref(null)
const globalView = ref(null)
const favoriteMerchants = ref([])
const activeGroups = ref([]) // 当前展开的分组
const popoverVisible = ref(false)
const popoverContent = ref(null)
const searchInput = ref(null)

// 键盘导航相关状态
const focusedIndex = ref(-1)  // -1 表示全局视图
const focusedSection = ref('global')  // 'global', 'favorites', 'recent', 'search', 'group-{id}'

// 关闭弹出窗口
const closePopover = () => {
  popoverVisible.value = false
  resetFocus()
}

// 重置焦点状态
const resetFocus = () => {
  focusedIndex.value = -1
  focusedSection.value = 'global'
}

// 聚焦到搜索结果
const focusResults = () => {
  if (searchKeyword.value && filteredMerchants.value.length > 0) {
    focusedSection.value = 'search'
    focusedIndex.value = 0
  }
}

// 向下导航
const navigateDown = () => {
  if (focusedSection.value === 'global') {
    // 从全局视图向下导航
    if (favoriteMerchants.value.length > 0) {
      focusedSection.value = 'favorites'
      focusedIndex.value = 0
    } else if (merchantStore.recentMerchants.length > 0 && !searchKeyword.value) {
      focusedSection.value = 'recent'
      focusedIndex.value = 0
    } else if (searchKeyword.value && filteredMerchants.value.length > 0) {
      focusedSection.value = 'search'
      focusedIndex.value = 0
    } else if (merchantStore.merchantGroups.length > 0 && !searchKeyword.value) {
      const firstGroup = merchantStore.merchantGroups[0]
      focusedSection.value = `group-${firstGroup.id}`
      focusedIndex.value = 0
    }
  } else if (focusedSection.value === 'favorites') {
    // 在收藏中导航
    if (focusedIndex.value < favoriteMerchants.value.length - 1) {
      focusedIndex.value++
    } else if (merchantStore.recentMerchants.length > 0 && !searchKeyword.value) {
      focusedSection.value = 'recent'
      focusedIndex.value = 0
    } else if (searchKeyword.value && filteredMerchants.value.length > 0) {
      focusedSection.value = 'search'
      focusedIndex.value = 0
    } else if (merchantStore.merchantGroups.length > 0 && !searchKeyword.value) {
      const firstGroup = merchantStore.merchantGroups[0]
      focusedSection.value = `group-${firstGroup.id}`
      focusedIndex.value = 0
    }
  } else if (focusedSection.value === 'recent') {
    // 在最近访问中导航
    if (focusedIndex.value < merchantStore.recentMerchants.length - 1) {
      focusedIndex.value++
    } else if (searchKeyword.value && filteredMerchants.value.length > 0) {
      focusedSection.value = 'search'
      focusedIndex.value = 0
    } else if (merchantStore.merchantGroups.length > 0 && !searchKeyword.value) {
      const firstGroup = merchantStore.merchantGroups[0]
      focusedSection.value = `group-${firstGroup.id}`
      focusedIndex.value = 0
    }
  } else if (focusedSection.value === 'search') {
    // 在搜索结果中导航
    if (focusedIndex.value < filteredMerchants.value.length - 1) {
      focusedIndex.value++
    }
  } else if (focusedSection.value.startsWith('group-')) {
    // 在分组中导航
    const groupId = focusedSection.value.substring(6)
    const group = merchantStore.merchantGroups.find(g => g.id === groupId)

    if (group && focusedIndex.value < group.merchants.length - 1) {
      focusedIndex.value++
    } else {
      // 移到下一个分组
      const currentGroupIndex = merchantStore.merchantGroups.findIndex(g => g.id === groupId)
      if (currentGroupIndex < merchantStore.merchantGroups.length - 1) {
        const nextGroup = merchantStore.merchantGroups[currentGroupIndex + 1]
        focusedSection.value = `group-${nextGroup.id}`
        focusedIndex.value = 0
        // 确保下一个分组是展开的
        if (!activeGroups.value.includes(nextGroup.id)) {
          activeGroups.value = [nextGroup.id]
        }
      }
    }
  }

  // 滚动到焦点元素
  scrollToFocused()
}

// 向上导航
const navigateUp = () => {
  if (focusedSection.value === 'global') {
    // 已经在最上面，不做任何事
    return
  } else if (focusedSection.value === 'favorites') {
    // 在收藏中向上导航
    if (focusedIndex.value > 0) {
      focusedIndex.value--
    } else {
      focusedSection.value = 'global'
      focusedIndex.value = -1
    }
  } else if (focusedSection.value === 'recent') {
    // 在最近访问中向上导航
    if (focusedIndex.value > 0) {
      focusedIndex.value--
    } else if (favoriteMerchants.value.length > 0) {
      focusedSection.value = 'favorites'
      focusedIndex.value = favoriteMerchants.value.length - 1
    } else {
      focusedSection.value = 'global'
      focusedIndex.value = -1
    }
  } else if (focusedSection.value === 'search') {
    // 在搜索结果中向上导航
    if (focusedIndex.value > 0) {
      focusedIndex.value--
    } else if (merchantStore.recentMerchants.length > 0 && !searchKeyword.value) {
      focusedSection.value = 'recent'
      focusedIndex.value = merchantStore.recentMerchants.length - 1
    } else if (favoriteMerchants.value.length > 0) {
      focusedSection.value = 'favorites'
      focusedIndex.value = favoriteMerchants.value.length - 1
    } else {
      focusedSection.value = 'global'
      focusedIndex.value = -1
    }
  } else if (focusedSection.value.startsWith('group-')) {
    // 在分组中向上导航
    const groupId = focusedSection.value.substring(6)

    if (focusedIndex.value > 0) {
      focusedIndex.value--
    } else {
      // 移到上一个分组
      const currentGroupIndex = merchantStore.merchantGroups.findIndex(g => g.id === groupId)
      if (currentGroupIndex > 0) {
        const prevGroup = merchantStore.merchantGroups[currentGroupIndex - 1]
        focusedSection.value = `group-${prevGroup.id}`
        const prevGroupMerchants = prevGroup.merchants || []
        focusedIndex.value = prevGroupMerchants.length - 1
        // 确保上一个分组是展开的
        if (!activeGroups.value.includes(prevGroup.id)) {
          activeGroups.value = [prevGroup.id]
        }
      } else if (merchantStore.recentMerchants.length > 0 && !searchKeyword.value) {
        focusedSection.value = 'recent'
        focusedIndex.value = merchantStore.recentMerchants.length - 1
      } else if (favoriteMerchants.value.length > 0) {
        focusedSection.value = 'favorites'
        focusedIndex.value = favoriteMerchants.value.length - 1
      } else {
        focusedSection.value = 'global'
        focusedIndex.value = -1
      }
    }
  }

  // 滚动到焦点元素
  scrollToFocused()
}

// 滚动到当前焦点元素
const scrollToFocused = () => {
  nextTick(() => {
    // 找到当前焦点元素
    const focusedEl = document.querySelector('.merchant-item.keyboard-focused')
    if (focusedEl) {
      // 滚动到可见区域
      focusedEl.scrollIntoView({ behavior: 'smooth', block: 'nearest' })
      // 保持焦点
      focusedEl.focus()
    }
  })
}

// 选择当前焦点项
const selectFocusedItem = () => {
  if (focusedSection.value === 'global') {
    switchToGlobal()
  } else if (focusedSection.value === 'favorites' && focusedIndex.value >= 0) {
    const merchant = favoriteMerchants.value[focusedIndex.value]
    if (merchant) switchMerchant(merchant)
  } else if (focusedSection.value === 'recent' && focusedIndex.value >= 0) {
    const merchant = merchantStore.recentMerchants[focusedIndex.value]
    if (merchant) switchMerchant(merchant)
  } else if (focusedSection.value === 'search' && focusedIndex.value >= 0) {
    const merchant = filteredMerchants.value[focusedIndex.value]
    if (merchant) switchMerchant(merchant)
  } else if (focusedSection.value.startsWith('group-') && focusedIndex.value >= 0) {
    const groupId = focusedSection.value.substring(6)
    const group = merchantStore.merchantGroups.find(g => g.id === groupId)
    if (group && focusedIndex.value < group.merchants.length) {
      const merchant = group.merchants[focusedIndex.value]
      if (merchant) switchMerchant(merchant)
    }
  }
}

// 在弹出框打开时聚焦搜索输入框
watch(popoverVisible, (isVisible) => {
  if (isVisible) {
    nextTick(() => {
      if (searchInput.value) {
        searchInput.value.focus()
      }
    })
  } else {
    resetFocus()
  }
})

// 加载时展开包含当前租户的分组
watch(() => merchantStore.currentMerchant, (newValue) => {
  if (newValue) {
    const foundGroup = merchantStore.merchantGroups.find(group =>
      group.merchants.some(merchant => merchant.id === newValue.id)
    )
    if (foundGroup) {
      activeGroups.value = [foundGroup.id]
    }
  }
}, { immediate: true })

// 过滤后的商家列表
const filteredMerchants = computed(() => {
  if (!searchKeyword.value) {
    return merchantStore.merchants || []
  }

  const keywords = searchKeyword.value.toLowerCase().split(/\s+/).filter(Boolean)
  return (merchantStore.merchants || []).filter(merchant => {
    const merchantName = merchant.name.toLowerCase()
    const merchantCode = (merchant.code || '').toLowerCase()
    return keywords.every(keyword =>
      merchantName.includes(keyword) || merchantCode.includes(keyword)
    )
  })
})

// 判断是否为收藏租户
const isFavorite = (merchant) => {
  return favoriteMerchants.value.some(m => m.id === merchant.id)
}

// 切换收藏状态
const toggleFavorite = (merchant) => {
  const index = favoriteMerchants.value.findIndex(m => m.id === merchant.id)
  if (index === -1) {
    favoriteMerchants.value.push(merchant)
  } else {
    favoriteMerchants.value.splice(index, 1)
  }

  // 保存到本地存储
  const userFavoritesKey = merchantStore.getUserSpecificKey('favoriteMerchants')
  localStorage.setItem(
    userFavoritesKey,
    JSON.stringify(favoriteMerchants.value.map(m => m.id))
  )
}

// 高亮匹配文本
const highlightMatch = (text) => {
  if (!searchKeyword.value) return text

  const keywords = searchKeyword.value.toLowerCase().split(/\s+/).filter(Boolean)
  let result = text
  keywords.forEach(keyword => {
    const regex = new RegExp(keyword, 'gi')
    result = result.replace(regex, match => `<span class="highlight">${match}</span>`)
  })
  return result
}

// 清除搜索
const clearSearch = () => {
  searchKeyword.value = ''
  searchInput.value?.focus()
}

// 聚焦第一个搜索结果
const focusFirstResult = () => {
  if (firstResultRef.value) {
    firstResultRef.value.focus()
  }
}

// 切换到全局视图
const switchToGlobal = () => {
  merchantStore.switchMerchant(null)
  closePopover()
}

// 切换到指定租户
const switchMerchant = (merchant) => {
  merchantStore.switchMerchant(merchant)
  closePopover()
}

// 初始化
onMounted(async () => {
  try {
    // 完整初始化商户Store（包括恢复localStorage中的选择）
    await merchantStore.initMerchant()
  } catch (error) {
    console.error('商户Store初始化失败:', error)
    // 如果初始化失败，至少尝试获取商户列表
    try {
      await merchantStore.fetchMerchants()
    } catch (fetchError) {
      console.error('获取商户列表失败:', fetchError)
    }
  }

  // 加载收藏的商家
  const userFavoritesKey = merchantStore.getUserSpecificKey('favoriteMerchants')
  const storedFavorites = localStorage.getItem(userFavoritesKey)
  if (storedFavorites) {
    try {
      const favoriteIds = JSON.parse(storedFavorites)
      favoriteMerchants.value = merchantStore.merchants
        .filter(merchant => favoriteIds.includes(merchant.id))
    } catch (e) {
      console.error('解析收藏商家失败:', e)
      // 清除损坏的收藏数据
      localStorage.removeItem(userFavoritesKey)
    }
  }
})
</script>

<style scoped>
.merchant-switcher {
  margin-right: 20px;
}

.merchant-selector {
  display: flex;
  align-items: center;
  padding: 0 10px;
  height: 36px;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.3s;
  font-weight: 500;
  font-size: 14px;
  color: #606266;
  outline: none;
}

.merchant-selector:hover,
.merchant-selector:focus {
  background-color: #f5f7fa;
}

.merchant-selector.is-active {
  background-color: #ecf5ff;
  color: #409eff;
}

.merchant-name {
  margin-right: 4px;
  max-width: 120px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* 商家名称切换动画 */
.merchant-name-fade-enter-active,
.merchant-name-fade-leave-active {
  transition: opacity 0.3s, transform 0.3s;
}

.merchant-name-fade-enter-from,
.merchant-name-fade-leave-to {
  opacity: 0;
  transform: translateY(5px);
}

.merchant-content {
  display: flex;
  flex-direction: column;
  outline: none;
}

.merchant-header {
  padding: 8px 12px;
  border-bottom: 1px solid #ebeef5;
}

.merchant-header h3 {
  margin: 0;
  font-size: 16px;
  color: #303133;
}

.merchant-search {
  padding: 12px;
  border-bottom: 1px solid #ebeef5;
}

.merchant-list {
  padding: 8px 0;
  max-height: 400px;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.merchant-group-title {
  padding: 8px 12px;
  color: #909399;
  font-size: 12px;
  font-weight: 500;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.result-count {
  font-weight: normal;
  color: #909399;
}

.merchant-scroll {
  overflow-y: auto;
  max-height: 240px;
  position: relative;
  /* 新增：平滑滚动体验 */
  scroll-behavior: smooth;
  will-change: transform;
  -webkit-overflow-scrolling: touch;
}

.merchant-item {
  display: flex;
  align-items: center;
  padding: 8px 12px;
  cursor: pointer;
  transition: all 0.3s;
  position: relative;
  outline: none;
}

.merchant-item:hover {
  background-color: #f5f7fa;
}

.merchant-item.active {
  background-color: #ecf5ff;
  color: #409eff;
}

.merchant-item.keyboard-focused {
  background-color: #f0f7ff;
  box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
}

.merchant-item .el-icon {
  margin-right: 8px;
  font-size: 16px;
}

.merchant-item span {
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.no-data {
  padding: 20px 0;
  display: flex;
  justify-content: center;
}

.highlight {
  color: #409eff;
  font-weight: bold;
}

.action-icon {
  opacity: 0;
  margin-right: 0 !important;
  transition: all 0.2s;
  color: #909399;
  font-size: 14px !important;
}

.merchant-item:hover .action-icon,
.merchant-item.keyboard-focused .action-icon {
  opacity: 1;
}

.action-icon:hover {
  color: #409eff;
  transform: scale(1.2);
}

.group-title {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
}

.group-count {
  font-size: 12px;
  color: #909399;
  font-weight: normal;
}

:deep(.el-collapse) {
  border: none;
}

:deep(.el-collapse-item__header) {
  font-size: 13px;
  color: #606266;
  padding: 0 5px;
  height: 34px;
  line-height: 34px;
}

:deep(.el-collapse-item__content) {
  padding: 0;
}

/* 定制popper样式 */
:deep(.merchant-popover) {
  padding: 0;
  min-width: 200px;
}
</style>