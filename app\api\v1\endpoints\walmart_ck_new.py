"""
沃尔玛CK管理API - 基于WalmartCKService的完整实现
"""

from fastapi import APIRouter, Depends, HTTPException, Query, status as http_status
from fastapi.responses import StreamingResponse
from typing import Optional, Any, Dict
from sqlalchemy.orm import Session
from datetime import datetime
import io
import csv
import json

from app.api import deps
from app.models.user import User
from app.models.merchant import Merchant
from app.models.department import Department
from app.models.walmart_ck import WalmartCK
from app.schemas.walmart_ck import WalmartCKCreate, WalmartCKUpdate, WalmartCKBatchCreate, WalmartCKBatchDelete, WalmartCKBatchOperation
from app.services.walmart_ck_service_new import WalmartCKService
from app.services.permission_service import PermissionService
from app.core.logging import get_logger

router = APIRouter()
logger = get_logger("walmart_ck_api")


class WalmartCKQueryParams:
    """沃尔玛CK查询参数封装"""

    def __init__(
        self,
        page: int = 1,
        page_size: int = 20,
        merchant_id: Optional[int] = None,
        department_id: Optional[int] = None,
        status: Optional[bool] = None,
        is_disabled: Optional[bool] = None,
        username: Optional[str] = None,
    ):
        self.page = page
        self.page_size = page_size
        self.merchant_id = merchant_id
        self.department_id = department_id
        self.status = status
        self.is_disabled = is_disabled
        self.username = username


class WalmartCKQueryBuilder:
    """沃尔玛CK查询构建器"""

    def __init__(
        self, ck_service: WalmartCKService, permission_service: PermissionService
    ):
        self.ck_service = ck_service
        self.permission_service = permission_service

    def build_query(self, params: WalmartCKQueryParams, current_user: User):
        """构建查询"""
        # 创建基础查询
        query = self.ck_service.db.query(self.ck_service.model)

        # 应用数据隔离
        query = self.ck_service.apply_data_isolation(query, current_user)

        # 应用过滤条件
        query = self._apply_filters(query, params, current_user)

        return query

    def _apply_filters(self, query, params: WalmartCKQueryParams, current_user: User):
        """应用过滤条件"""
        # 商户过滤
        if params.merchant_id and current_user.is_superuser:
            query = query.filter(
                self.ck_service.model.merchant_id == params.merchant_id
            )

        # 部门过滤 - 正确的逻辑：在用户权限范围内进行过滤
        if params.department_id:
            # 获取用户可访问的部门
            from app.core.auth import auth_service

            # 超级管理员可以过滤任意部门
            if current_user.is_superuser:
                query = query.filter(
                    self.ck_service.model.department_id == params.department_id
                )
            else:
                # 普通用户只能在可访问范围内过滤
                accessible_depts = auth_service.get_user_accessible_departments(current_user, self.ck_service.db)
                if params.department_id in accessible_depts:
                    query = query.filter(
                        self.ck_service.model.department_id == params.department_id
                    )
                # 如果请求的部门不在可访问范围内，忽略此过滤条件
                # 让数据隔离逻辑来处理权限控制

        # 状态过滤
        if params.status is not None:
            query = query.filter(self.ck_service.model.status == params.status)

        # 禁用状态过滤
        if params.is_disabled is not None:
            query = query.filter(
                self.ck_service.model.is_disabled == params.is_disabled
            )

        # 用户名搜索
        if params.username:
            query = query.filter(
                self.ck_service.model.username.ilike(f"%{params.username}%")
            )

        return query


@router.get("", response_model=Dict[str, Any])
async def read_walmart_cks(
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_active_user),
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(20, ge=1, le=100, description="每页数量"),
    merchant_id: Optional[int] = Query(None, description="商户ID"),
    department_id: Optional[int] = Query(None, description="部门ID"),
    status: Optional[bool] = Query(None, description="状态过滤"),
    start_date: Optional[str] = Query(None, description="开始日期 (YYYY-MM-DD)"),
    end_date: Optional[str] = Query(None, description="结束日期 (YYYY-MM-DD)"),
):
    """
    获取沃尔玛CK列表

    权限要求:
    - "walmart_ck:view": 查看沃尔玛CK权限
    """
    try:
        # 初始化服务
        ck_service = WalmartCKService(db)
        permission_service = PermissionService(db)

        # 权限检查
        has_permission = permission_service.check_user_permission(
            current_user, "api:walmart-ck:read"
        )
        if not has_permission:
            raise HTTPException(
                status_code=http_status.HTTP_403_FORBIDDEN,
                detail="没有查看沃尔玛CK的权限",
            )

        # 【安全修复】：强制商户隔离检查
        if not current_user.is_superuser:
            if not current_user.merchant_id:
                raise HTTPException(
                    status_code=http_status.HTTP_403_FORBIDDEN,
                    detail="用户没有商户信息，无法访问CK数据",
                )

            # 如果指定了merchant_id参数，检查是否匹配用户的商户
            if merchant_id and merchant_id != current_user.merchant_id:
                logger.warning(f"[SECURITY] 用户 {current_user.id} 尝试访问其他商户 {merchant_id} 的CK数据")
                raise HTTPException(
                    status_code=http_status.HTTP_403_FORBIDDEN,
                    detail="无权访问其他商户的CK数据",
                )

        # 使用新的集成方法获取列表和统计数据
        result = ck_service.get_list_with_statistics(
            current_user=current_user,
            page=page,
            page_size=page_size,
            merchant_id=merchant_id,
            department_id=department_id,
            status=status,
            start_date=start_date,
            end_date=end_date
        )

        return result

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取沃尔玛CK列表失败: {str(e)}")
        raise HTTPException(
            status_code=http_status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取沃尔玛CK列表失败: {str(e)}",
        )


def _determine_target_merchant_id_for_ck(
    current_user: User, merchant_id: Optional[int]
) -> Optional[int]:
    """确定目标商户ID"""
    if not current_user.is_superuser:
        # 非超级管理员只能查看自己商户的CK
        return current_user.merchant_id
    else:
        # 超级管理员可以指定商户ID查看特定商户的CK，
        # 或者不指定商户ID查看所有CK
        return merchant_id


def _can_view_creator_info(current_user: User, creator: User) -> bool:
    """
    检查当前用户是否可以查看创建者信息

    权限规则：
    1. 超级管理员可以查看所有创建者信息
    2. 用户可以查看自己创建的记录的创建者信息（即自己）
    3. 有用户管理权限的用户可以查看同商户内的创建者信息
    4. 只有本人数据权限的用户不能查看其他用户信息
    """
    # 超级管理员可以查看所有信息
    if current_user.is_superuser:
        return True

    # 用户可以查看自己的信息
    if current_user.id == creator.id:
        return True

    # 检查是否在同一商户内
    if current_user.merchant_id != creator.merchant_id:
        return False

    # 这里可以根据具体的权限配置进行更细粒度的控制
    # 暂时允许同商户内的用户查看创建者信息
    # 如果需要更严格的控制，可以检查用户的具体权限
    return True


@router.post("", response_model=Dict[str, Any])
async def create_walmart_ck(
    *,
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_active_user),
    ck_in: WalmartCKCreate,
):
    """
    创建沃尔玛CK

    权限要求:
    - "walmart_ck:create": 创建沃尔玛CK权限

    逻辑说明:
    - 超级管理员：必须在前端表单中选择merchant_id和department_id
    - 非超级管理员：自动使用当前用户的merchant_id和department_id
    """
    try:
        # 初始化服务
        ck_service = WalmartCKService(db)
        permission_service = PermissionService(db)

        # 权限检查
        has_permission = permission_service.check_user_permission(
            current_user, "api:walmart-ck:create"
        )
        if not has_permission:
            raise HTTPException(
                status_code=http_status.HTTP_403_FORBIDDEN,
                detail="没有创建沃尔玛CK的权限",
            )

        # 验证必填字段
        if current_user.is_superuser:
            # 超级管理员必须提供merchant_id和department_id
            if not ck_in.merchant_id or not ck_in.department_id:
                raise HTTPException(
                    status_code=http_status.HTTP_400_BAD_REQUEST,
                    detail="超级管理员创建CK时必须选择商户和部门",
                )
        else:
            # 非超级管理员：检查前端提交的merchant_id和department_id
            if not ck_in.merchant_id or not ck_in.department_id:
                raise HTTPException(
                    status_code=http_status.HTTP_400_BAD_REQUEST,
                    detail="请选择商户和部门",
                )

            # 验证商户管理员只能为自己的商户创建CK
            if not current_user.merchant_id:
                raise HTTPException(
                    status_code=http_status.HTTP_400_BAD_REQUEST,
                    detail="用户未分配商户，无法创建CK",
                )

            if ck_in.merchant_id != current_user.merchant_id:
                raise HTTPException(
                    status_code=http_status.HTTP_403_FORBIDDEN,
                    detail="只能为自己所属的商户创建CK",
                )

        # 创建沃尔玛CK
        walmart_ck = ck_service.create_walmart_ck(ck_in, current_user)

        return {
            "success": True,
            "data": walmart_ck.to_dict(),
            "message": "创建沃尔玛CK成功",
        }

    except HTTPException:
        raise
    except ValueError as e:
        raise HTTPException(status_code=http_status.HTTP_400_BAD_REQUEST, detail=str(e))
    except Exception as e:
        logger.error(f"创建沃尔玛CK失败: {str(e)}")
        raise HTTPException(
            status_code=http_status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"创建沃尔玛CK失败: {str(e)}",
        )


@router.post("/batch", response_model=Dict[str, Any])
async def batch_create_walmart_ck(
    *,
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_active_user),
    batch_data: WalmartCKBatchCreate,
):
    """
    批量创建沃尔玛CK

    权限要求:
    - "walmart_ck:create": 创建沃尔玛CK权限

    逻辑说明:
    - 支持一次性创建多个CK，共享相同的配置参数
    - 超级管理员：必须在前端表单中选择merchant_id和department_id
    - 非超级管理员：自动使用当前用户的merchant_id，但可以选择department_id
    - 使用事务确保原子性：要么全部成功，要么全部失败
    - 最多支持1000个CK的批量创建
    """
    try:
        # 初始化服务
        ck_service = WalmartCKService(db)
        permission_service = PermissionService(db)

        # 权限检查
        has_permission = permission_service.check_user_permission(
            current_user, "api:walmart-ck:create"
        )
        if not has_permission:
            raise HTTPException(
                status_code=http_status.HTTP_403_FORBIDDEN,
                detail="没有创建沃尔玛CK的权限",
            )

        # 验证必填字段
        if current_user.is_superuser:
            # 超级管理员必须提供merchant_id和department_id
            if not batch_data.merchant_id or not batch_data.department_id:
                raise HTTPException(
                    status_code=http_status.HTTP_400_BAD_REQUEST,
                    detail="超级管理员创建CK时必须选择商户和部门",
                )
        else:
            # 非超级管理员：检查前端提交的merchant_id和department_id
            if not batch_data.merchant_id or not batch_data.department_id:
                raise HTTPException(
                    status_code=http_status.HTTP_400_BAD_REQUEST,
                    detail="请选择商户和部门",
                )

            # 验证商户管理员只能为自己的商户创建CK
            if not current_user.merchant_id:
                raise HTTPException(
                    status_code=http_status.HTTP_400_BAD_REQUEST,
                    detail="用户未分配商户，无法创建CK",
                )

            if batch_data.merchant_id != current_user.merchant_id:
                raise HTTPException(
                    status_code=http_status.HTTP_403_FORBIDDEN,
                    detail="只能为自己所属的商户创建CK",
                )

        # 验证CK签名数量
        if not batch_data.signs:
            raise HTTPException(
                status_code=http_status.HTTP_400_BAD_REQUEST,
                detail="请至少提供一个CK签名",
            )

        if len(batch_data.signs) > 1000:
            raise HTTPException(
                status_code=http_status.HTTP_400_BAD_REQUEST,
                detail="单次最多支持创建1000个CK",
            )

        # 验证独立备注列表（如果提供）
        if batch_data.descriptions:
            if len(batch_data.descriptions) != len(batch_data.signs):
                raise HTTPException(
                    status_code=http_status.HTTP_400_BAD_REQUEST,
                    detail="独立备注列表的数量必须与CK签名数量一致",
                )

        # 批量创建沃尔玛CK
        result = ck_service.batch_create_walmart_ck(batch_data, current_user)

        return {
            "success": True,
            "data": result,
            "message": result["message"],
        }

    except HTTPException:
        raise
    except ValueError as e:
        raise HTTPException(status_code=http_status.HTTP_400_BAD_REQUEST, detail=str(e))
    except Exception as e:
        logger.error(f"批量创建沃尔玛CK失败: {str(e)}")
        raise HTTPException(
            status_code=http_status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"批量创建沃尔玛CK失败: {str(e)}",
        )


@router.get("/binding-amount-statistics", response_model=Dict[str, Any])
async def get_ck_binding_amount_statistics(
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_active_user),
    merchant_id: Optional[int] = Query(None, description="商户ID"),
    department_id: Optional[int] = Query(None, description="部门ID"),
    start_date: Optional[datetime] = Query(None, description="开始日期"),
    end_date: Optional[datetime] = Query(None, description="结束日期"),
):
    """
    获取CK绑卡金额统计信息

    权限要求:
    - "walmart_ck:view": 查看沃尔玛CK权限

    参数说明:
    - merchant_id: 商户ID（超级管理员可选，其他用户忽略此参数）
    - department_id: 部门ID（可选，用于筛选特定部门的CK）
    - start_date: 开始日期（可选，用于筛选时间范围）
    - end_date: 结束日期（可选，用于筛选时间范围）

    返回数据包含:
    - summary: 汇总统计（总CK数、总记录数、成功数、成功率、金额统计等）
    - ck_details: 每个CK的详细统计信息
    - filters: 应用的筛选条件
    """
    try:
        # 初始化服务
        ck_service = WalmartCKService(db)
        permission_service = PermissionService(db)

        # 权限检查
        has_permission = permission_service.check_user_permission(
            current_user, "api:walmart-ck:read"
        )
        if not has_permission:
            raise HTTPException(
                status_code=http_status.HTTP_403_FORBIDDEN,
                detail="没有查看沃尔玛CK统计的权限",
            )

        # 获取绑卡金额统计信息
        statistics = ck_service.get_ck_binding_amount_statistics(
            current_user=current_user,
            merchant_id=merchant_id,
            department_id=department_id,
            start_date=start_date,
            end_date=end_date
        )

        return statistics

    except HTTPException:
        raise
    except ValueError as e:
        raise HTTPException(
            status_code=http_status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        logger.error(f"获取CK绑卡金额统计失败: {str(e)}")
        raise HTTPException(
            status_code=http_status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取CK绑卡金额统计失败: {str(e)}",
        )


@router.get("/export")
async def export_walmart_cks(
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_active_user),
    format: str = Query("csv", regex="^(csv|json)$", description="导出格式: csv 或 json"),
    merchant_id: Optional[int] = Query(None, description="商户ID"),
    department_id: Optional[int] = Query(None, description="部门ID"),
    status: Optional[bool] = Query(None, description="状态过滤"),
    start_date: Optional[str] = Query(None, description="开始日期 (YYYY-MM-DD)"),
    end_date: Optional[str] = Query(None, description="结束日期 (YYYY-MM-DD)"),
):
    """
    导出沃尔玛CK数据

    支持格式:
    - csv: CSV格式
    - json: JSON格式

    权限要求:
    - "walmart_ck:export": 导出沃尔玛CK权限
    """
    try:
        # 初始化服务
        ck_service = WalmartCKService(db)
        permission_service = PermissionService(db)

        # 权限检查
        has_permission = permission_service.check_user_permission(
            current_user, "api:walmart-ck:export"
        )
        if not has_permission:
            raise HTTPException(
                status_code=http_status.HTTP_403_FORBIDDEN,
                detail="没有导出沃尔玛CK的权限",
            )

        # 【安全修复】：强制商户隔离检查
        target_merchant_id = _determine_target_merchant_id_for_ck(current_user, merchant_id)

        # 构建过滤条件
        filters = {}
        if target_merchant_id:
            filters['merchant_id'] = target_merchant_id
        if department_id:
            filters['department_id'] = department_id
        if status is not None:
            filters['status'] = status

        # 处理日期过滤
        if start_date:
            try:
                start_datetime = datetime.strptime(start_date, "%Y-%m-%d")
                filters['start_time'] = start_datetime
            except ValueError:
                raise HTTPException(
                    status_code=http_status.HTTP_400_BAD_REQUEST,
                    detail="开始日期格式错误，请使用 YYYY-MM-DD 格式"
                )

        if end_date:
            try:
                end_datetime = datetime.strptime(end_date, "%Y-%m-%d").replace(hour=23, minute=59, second=59)
                filters['end_time'] = end_datetime
            except ValueError:
                raise HTTPException(
                    status_code=http_status.HTTP_400_BAD_REQUEST,
                    detail="结束日期格式错误，请使用 YYYY-MM-DD 格式"
                )

        # 获取CK数据
        ck_list = ck_service.get_walmart_cks_with_relations(
            current_user=current_user,
            skip=0,
            limit=10000,  # 导出时获取大量数据
            filters=filters,
            order_by="created_at",
            order_desc=True
        )

        # 准备导出数据
        export_data = []
        for ck_dict in ck_list:
            export_item = {
                "ID": ck_dict.get('id', ''),
                "签名": ck_dict.get('sign', ''),  # 显示完整签名
                "商户名称": ck_dict.get('merchant_name', ''),
                "部门名称": ck_dict.get('department_name', ''),
                "总限制": ck_dict.get('total_limit', 0),
                "已绑卡数": ck_dict.get('bind_count', 0),
                "绑卡金额": ck_dict.get('bind_amount', 0),
                "状态": "启用" if ck_dict.get('active') else "禁用",
                "最后绑卡时间": ck_dict.get('last_bind_time', ''),
                "创建时间": ck_dict.get('created_at').strftime("%Y-%m-%d %H:%M:%S") if ck_dict.get('created_at') else '',
                "更新时间": ck_dict.get('updated_at').strftime("%Y-%m-%d %H:%M:%S") if ck_dict.get('updated_at') else '',
                "描述": ck_dict.get('description', ''),
            }
            export_data.append(export_item)

        # 生成文件名
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        merchant_suffix = f"_merchant_{target_merchant_id}" if target_merchant_id else ""

        if format == "csv":
            return _generate_csv_response(export_data, f"walmart_ck_export{merchant_suffix}_{timestamp}.csv")
        else:  # json
            return _generate_json_response(export_data, f"walmart_ck_export{merchant_suffix}_{timestamp}.json")

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"导出沃尔玛CK数据失败: {str(e)}")
        raise HTTPException(
            status_code=http_status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"导出沃尔玛CK数据失败: {str(e)}",
        )


@router.get("/{ck_id}", response_model=Dict[str, Any])
async def read_walmart_ck(
    ck_id: int,
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_active_user),
):
    """
    获取沃尔玛CK详情

    权限要求:
    - "walmart_ck:view": 查看沃尔玛CK权限
    """
    try:
        # 初始化服务
        ck_service = WalmartCKService(db)
        permission_service = PermissionService(db)

        # 权限检查
        has_permission = permission_service.check_user_permission(
            current_user, "api:walmart-ck:read"
        )
        if not has_permission:
            raise HTTPException(
                status_code=http_status.HTTP_403_FORBIDDEN,
                detail="没有查看沃尔玛CK的权限",
            )

        # 获取沃尔玛CK（应用数据隔离）
        walmart_ck = ck_service.get_with_isolation(ck_id, current_user)
        if not walmart_ck:
            raise HTTPException(
                status_code=http_status.HTTP_404_NOT_FOUND,
                detail="沃尔玛CK不存在或无权限访问",
            )

        # 转换为字典格式并添加关联信息
        ck_dict = walmart_ck.to_dict()

        # 查询商户名称
        if walmart_ck.merchant_id:
            merchant = ck_service.db.query(Merchant).filter(Merchant.id == walmart_ck.merchant_id).first()
            ck_dict['merchant_name'] = merchant.name if merchant else '未知商户'
        else:
            ck_dict['merchant_name'] = '未知商户'

        # 查询部门名称
        if walmart_ck.department_id:
            department = ck_service.db.query(Department).filter(Department.id == walmart_ck.department_id).first()
            ck_dict['department_name'] = department.name if department else '未知部门'
        else:
            ck_dict['department_name'] = '未知部门'

        # 查询创建者信息
        if walmart_ck.created_by:
            creator = ck_service.db.query(User).filter(User.id == walmart_ck.created_by).first()
            if creator:
                # 根据数据权限决定是否显示创建者信息
                if _can_view_creator_info(current_user, creator):
                    ck_dict['creator_username'] = creator.username
                    ck_dict['creator_name'] = creator.full_name if creator.full_name else creator.username
                else:
                    ck_dict['creator_username'] = '***'
                    ck_dict['creator_name'] = '***'
            else:
                ck_dict['creator_username'] = '已删除'
                ck_dict['creator_name'] = '已删除'
        else:
            ck_dict['creator_username'] = '-'
            ck_dict['creator_name'] = '-'

        return ck_dict

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取沃尔玛CK详情失败: {str(e)}")
        raise HTTPException(
            status_code=http_status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取沃尔玛CK详情失败: {str(e)}",
        )


@router.put("/{ck_id}", response_model=Dict[str, Any])
async def update_walmart_ck(
    ck_id: int,
    *,
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_active_user),
    ck_in: WalmartCKUpdate,
):
    """
    更新沃尔玛CK信息

    权限要求:
    - "walmart_ck:update": 更新沃尔玛CK权限
    """
    try:
        # 初始化服务
        ck_service = WalmartCKService(db)
        permission_service = PermissionService(db)

        # 权限检查
        has_permission = permission_service.check_user_permission(
            current_user, "api:walmart-ck:update"
        )
        if not has_permission:
            raise HTTPException(
                status_code=http_status.HTTP_403_FORBIDDEN,
                detail="没有更新沃尔玛CK的权限",
            )

        # 更新沃尔玛CK
        walmart_ck = ck_service.update_walmart_ck(ck_id, ck_in, current_user)
        if not walmart_ck:
            raise HTTPException(
                status_code=http_status.HTTP_404_NOT_FOUND,
                detail="沃尔玛CK不存在或无权限访问",
            )

        return {
            "success": True,
            "data": walmart_ck.to_dict(),
            "message": "更新沃尔玛CK成功",
        }

    except HTTPException:
        raise
    except ValueError as e:
        raise HTTPException(status_code=http_status.HTTP_400_BAD_REQUEST, detail=str(e))
    except Exception as e:
        logger.error(f"更新沃尔玛CK失败: {str(e)}")
        raise HTTPException(
            status_code=http_status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"更新沃尔玛CK失败: {str(e)}",
        )


@router.delete("/{ck_id}", response_model=Dict[str, Any])
async def delete_walmart_ck(
    ck_id: int,
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_active_user),
):
    """
    删除沃尔玛CK

    权限要求:
    - "walmart_ck:delete": 删除沃尔玛CK权限
    """
    try:
        # 初始化服务
        ck_service = WalmartCKService(db)
        permission_service = PermissionService(db)

        # 权限检查
        has_permission = permission_service.check_user_permission(
            current_user, "api:walmart-ck:delete"
        )
        if not has_permission:
            raise HTTPException(
                status_code=http_status.HTTP_403_FORBIDDEN,
                detail="没有删除沃尔玛CK的权限",
            )

        # 删除沃尔玛CK
        success = ck_service.delete_walmart_ck(ck_id, current_user)
        if not success:
            raise HTTPException(
                status_code=http_status.HTTP_404_NOT_FOUND,
                detail="沃尔玛CK不存在或无权限访问",
            )

        return {"success": True, "data": None, "message": "删除沃尔玛CK成功"}

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"删除沃尔玛CK失败: {str(e)}")
        raise HTTPException(
            status_code=http_status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"删除沃尔玛CK失败: {str(e)}",
        )


@router.post("/batch-delete", response_model=Dict[str, Any])
async def batch_delete_walmart_ck(
    batch_delete_data: "WalmartCKBatchDelete",
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_active_user),
):
    """
    批量删除沃尔玛CK（软删除）

    Args:
        batch_delete_data: 批量删除请求数据，包含CK ID列表
        db: 数据库会话
        current_user: 当前用户

    Returns:
        Dict[str, Any]: 批量删除结果

    权限要求:
    - "walmart_ck:delete": 删除沃尔玛CK权限
    """
    try:
        # 初始化服务
        ck_service = WalmartCKService(db)
        permission_service = PermissionService(db)

        # 权限检查
        has_permission = permission_service.check_user_permission(
            current_user, "api:walmart-ck:delete"
        )
        if not has_permission:
            raise HTTPException(
                status_code=http_status.HTTP_403_FORBIDDEN,
                detail="没有删除沃尔玛CK的权限",
            )

        # 验证输入数据
        if not batch_delete_data.ck_ids:
            raise HTTPException(
                status_code=http_status.HTTP_400_BAD_REQUEST,
                detail="CK ID列表不能为空",
            )

        if len(batch_delete_data.ck_ids) > 100:  # 限制批量删除数量
            raise HTTPException(
                status_code=http_status.HTTP_400_BAD_REQUEST,
                detail="单次批量删除数量不能超过100个",
            )

        # 执行批量删除
        result = ck_service.batch_delete_walmart_ck(batch_delete_data.ck_ids, current_user)

        # 构造响应消息
        message = f"批量删除完成：成功 {result['success_count']} 个，失败 {result['failed_count']} 个"

        return {
            "success": True,
            "data": result,
            "message": message,
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"批量删除沃尔玛CK失败: {str(e)}")
        raise HTTPException(
            status_code=http_status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"批量删除沃尔玛CK失败: {str(e)}",
        )


@router.post("/{ck_id}/enable", response_model=Dict[str, Any])
async def enable_walmart_ck(
    ck_id: int,
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_active_user),
):
    """
    启用沃尔玛CK

    权限要求:
    - "walmart_ck:manage": 管理沃尔玛CK权限
    """
    try:
        # 初始化服务
        ck_service = WalmartCKService(db)
        permission_service = PermissionService(db)

        # 权限检查
        has_permission = permission_service.check_user_permission(
            current_user, "api:walmart-ck:update"
        )
        if not has_permission:
            raise HTTPException(
                status_code=http_status.HTTP_403_FORBIDDEN,
                detail="没有管理沃尔玛CK的权限",
            )

        # 启用CK
        success = ck_service.enable_ck(ck_id, current_user)
        if not success:
            raise HTTPException(
                status_code=http_status.HTTP_404_NOT_FOUND,
                detail="沃尔玛CK不存在或无权限访问",
            )

        return {"success": True, "data": None, "message": "启用沃尔玛CK成功"}

    except HTTPException:
        raise
    except ValueError as e:
        raise HTTPException(status_code=http_status.HTTP_400_BAD_REQUEST, detail=str(e))
    except Exception as e:
        logger.error(f"启用沃尔玛CK失败: {str(e)}")
        raise HTTPException(
            status_code=http_status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"启用沃尔玛CK失败: {str(e)}",
        )


@router.post("/sync-redis", response_model=Dict[str, Any])
async def sync_ck_to_redis(
    *,
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_active_user),
    merchant_id: Optional[int] = Query(None, description="商户ID，不提供则同步所有商户"),
):
    """
    手动同步CK状态到Redis缓存

    用于解决Redis缓存与数据库不一致的问题

    权限要求:
    - "walmart_ck:manage": 管理沃尔玛CK权限
    """
    try:
        # 初始化服务
        permission_service = PermissionService(db)

        # 权限检查
        has_permission = permission_service.check_user_permission(
            current_user, "api:walmart-ck:update"
        )
        if not has_permission:
            raise HTTPException(
                status_code=http_status.HTTP_403_FORBIDDEN,
                detail="没有管理沃尔玛CK的权限",
            )

        # 简化CK服务不需要Redis同步
        from app.models.walmart_ck import WalmartCK

        # 统计CK数量
        query = db.query(WalmartCK).filter(WalmartCK.is_deleted == False)
        if merchant_id:
            query = query.filter(WalmartCK.merchant_id == merchant_id)

        ck_count = query.count()

        message = f"简化CK服务不需要Redis同步，商户 {merchant_id} 当前有 {ck_count} 个可用CK" if merchant_id else f"简化CK服务不需要Redis同步，当前有 {ck_count} 个可用CK"

        return {"success": True, "data": {"ck_count": ck_count}, "message": message}

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"同步CK到Redis失败: {str(e)}")
        raise HTTPException(
            status_code=http_status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"同步CK到Redis失败: {str(e)}",
        )


@router.post("/{ck_id}/disable", response_model=Dict[str, Any])
async def disable_walmart_ck(
    ck_id: int,
    *,
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_active_user),
    reason: str = Query(..., description="禁用原因"),
):
    """
    禁用沃尔玛CK

    权限要求:
    - "walmart_ck:manage": 管理沃尔玛CK权限
    """
    try:
        # 初始化服务
        ck_service = WalmartCKService(db)
        permission_service = PermissionService(db)

        # 权限检查
        has_permission = permission_service.check_user_permission(
            current_user, "api:walmart-ck:update"
        )
        if not has_permission:
            raise HTTPException(
                status_code=http_status.HTTP_403_FORBIDDEN,
                detail="没有管理沃尔玛CK的权限",
            )

        # 禁用CK
        success = ck_service.disable_ck(ck_id, reason, current_user)
        if not success:
            raise HTTPException(
                status_code=http_status.HTTP_404_NOT_FOUND,
                detail="沃尔玛CK不存在或无权限访问",
            )

        return {"success": True, "data": None, "message": "禁用沃尔玛CK成功"}

    except HTTPException:
        raise
    except ValueError as e:
        raise HTTPException(status_code=http_status.HTTP_400_BAD_REQUEST, detail=str(e))
    except Exception as e:
        logger.error(f"禁用沃尔玛CK失败: {str(e)}")
        raise HTTPException(
            status_code=http_status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"禁用沃尔玛CK失败: {str(e)}",
        )


@router.post("/batch-enable", response_model=Dict[str, Any])
async def batch_enable_walmart_ck(
    *,
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_active_user),
    batch_data: WalmartCKBatchOperation,
):
    """
    批量启用沃尔玛CK

    权限要求:
    - "walmart_ck:update": 更新沃尔玛CK权限
    """
    try:
        # 初始化服务
        ck_service = WalmartCKService(db)
        permission_service = PermissionService(db)

        # 权限检查
        has_permission = permission_service.check_user_permission(
            current_user, "api:walmart-ck:update"
        )
        if not has_permission:
            raise HTTPException(
                status_code=http_status.HTTP_403_FORBIDDEN,
                detail="没有管理沃尔玛CK的权限",
            )

        # 执行批量启用
        result = ck_service.batch_enable_walmart_ck(batch_data.ck_ids, current_user)

        # 构造响应消息
        message = f"批量启用完成：成功 {result['success_count']} 个，失败 {result['failed_count']} 个"

        return {
            "success": True,
            "data": result,
            "message": message,
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"批量启用沃尔玛CK失败: {str(e)}")
        raise HTTPException(
            status_code=http_status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"批量启用沃尔玛CK失败: {str(e)}",
        )


@router.post("/batch-disable", response_model=Dict[str, Any])
async def batch_disable_walmart_ck(
    *,
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_active_user),
    batch_data: WalmartCKBatchOperation,
):
    """
    批量禁用沃尔玛CK

    权限要求:
    - "walmart_ck:update": 更新沃尔玛CK权限
    """
    try:
        # 初始化服务
        ck_service = WalmartCKService(db)
        permission_service = PermissionService(db)

        # 权限检查
        has_permission = permission_service.check_user_permission(
            current_user, "api:walmart-ck:update"
        )
        if not has_permission:
            raise HTTPException(
                status_code=http_status.HTTP_403_FORBIDDEN,
                detail="没有管理沃尔玛CK的权限",
            )

        # 执行批量禁用
        result = ck_service.batch_disable_walmart_ck(batch_data.ck_ids, current_user)

        # 构造响应消息
        message = f"批量禁用完成：成功 {result['success_count']} 个，失败 {result['failed_count']} 个"

        return {
            "success": True,
            "data": result,
            "message": message,
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"批量禁用沃尔玛CK失败: {str(e)}")
        raise HTTPException(
            status_code=http_status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"批量禁用沃尔玛CK失败: {str(e)}",
        )


@router.get("/statistics/{merchant_id}", response_model=Dict[str, Any])
async def get_walmart_ck_statistics(
    merchant_id: int,
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_active_user),
    start_date: Optional[datetime] = Query(None, description="开始日期"),
    end_date: Optional[datetime] = Query(None, description="结束日期"),
):
    """
    获取沃尔玛CK统计信息

    权限要求:
    - "walmart_ck:view": 查看沃尔玛CK权限
    """
    try:
        # 初始化服务
        ck_service = WalmartCKService(db)
        permission_service = PermissionService(db)

        # 权限检查
        has_permission = permission_service.check_user_permission(
            current_user, "api:walmart-ck:read"
        )
        if not has_permission:
            raise HTTPException(
                status_code=http_status.HTTP_403_FORBIDDEN,
                detail="没有查看沃尔玛CK统计的权限",
            )

        # 获取统计信息
        statistics = ck_service.get_ck_statistics(
            merchant_id, current_user, start_date, end_date
        )

        return {"success": True, "data": statistics, "message": "获取沃尔玛CK统计成功"}

    except HTTPException:
        raise
    except ValueError as e:
        raise HTTPException(status_code=http_status.HTTP_400_BAD_REQUEST, detail=str(e))
    except Exception as e:
        logger.error(f"获取沃尔玛CK统计失败: {str(e)}")
        raise HTTPException(
            status_code=http_status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取沃尔玛CK统计失败: {str(e)}",
        )


@router.get("/{ck_id}/statistics", response_model=Dict[str, Any])
async def get_single_ck_statistics(
    ck_id: int,
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_active_user),
    start_date: Optional[datetime] = Query(None, description="开始日期"),
    end_date: Optional[datetime] = Query(None, description="结束日期"),
    time_range: str = Query("month", regex="^(today|week|month|custom)$", description="时间范围"),
):
    """
    获取单个CK的详细统计信息

    权限要求:
    - "walmart_ck:view": 查看沃尔玛CK权限
    """
    try:
        # 初始化服务
        ck_service = WalmartCKService(db)
        permission_service = PermissionService(db)

        # 权限检查
        has_permission = permission_service.check_user_permission(
            current_user, "api:walmart-ck:read"
        )
        if not has_permission:
            raise HTTPException(
                status_code=http_status.HTTP_403_FORBIDDEN,
                detail="没有查看沃尔玛CK统计的权限",
            )

        # 获取CK详细统计信息
        statistics = ck_service.get_single_ck_detailed_statistics(
            ck_id, current_user, start_date, end_date, time_range
        )

        return {"success": True, "data": statistics, "message": "获取CK详细统计成功"}

    except HTTPException:
        raise
    except ValueError as e:
        raise HTTPException(status_code=http_status.HTTP_400_BAD_REQUEST, detail=str(e))
    except Exception as e:
        logger.error(f"获取沃尔玛CK统计失败: {str(e)}")
        raise HTTPException(
            status_code=http_status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取沃尔玛CK统计失败: {str(e)}",
        )


@router.get("/{ck_id}/daily-trend", response_model=Dict[str, Any])
async def get_ck_daily_trend(
    ck_id: int,
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_active_user),
    days: int = Query(30, ge=1, le=90, description="天数"),
):
    """
    获取CK的每日趋势数据

    权限要求:
    - "walmart_ck:view": 查看沃尔玛CK权限
    """
    try:
        # 初始化服务
        ck_service = WalmartCKService(db)
        permission_service = PermissionService(db)

        # 权限检查
        has_permission = permission_service.check_user_permission(
            current_user, "api:walmart-ck:read"
        )
        if not has_permission:
            raise HTTPException(
                status_code=http_status.HTTP_403_FORBIDDEN,
                detail="没有查看沃尔玛CK统计的权限",
            )

        # 获取每日趋势数据
        daily_trend = ck_service.get_ck_daily_trend(ck_id, current_user, days)

        return {"success": True, "data": daily_trend, "message": "获取CK每日趋势成功"}

    except HTTPException:
        raise
    except ValueError as e:
        raise HTTPException(status_code=http_status.HTTP_400_BAD_REQUEST, detail=str(e))
    except Exception as e:
        logger.error(f"获取CK每日趋势失败: {str(e)}")
        raise HTTPException(
            status_code=http_status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取CK每日趋势失败: {str(e)}",
        )


@router.get("/{ck_id}/failure-analysis", response_model=Dict[str, Any])
async def get_ck_failure_analysis(
    ck_id: int,
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_active_user),
    start_date: Optional[datetime] = Query(None, description="开始日期"),
    end_date: Optional[datetime] = Query(None, description="结束日期"),
    limit: int = Query(10, ge=1, le=50, description="返回的失败原因数量限制"),
):
    """
    获取CK的失败原因分析

    权限要求:
    - "walmart_ck:view": 查看沃尔玛CK权限
    """
    try:
        # 初始化服务
        ck_service = WalmartCKService(db)
        permission_service = PermissionService(db)

        # 权限检查
        has_permission = permission_service.check_user_permission(
            current_user, "api:walmart-ck:read"
        )
        if not has_permission:
            raise HTTPException(
                status_code=http_status.HTTP_403_FORBIDDEN,
                detail="没有查看沃尔玛CK统计的权限",
            )

        # 获取失败原因分析
        failure_analysis = ck_service.get_ck_failure_analysis(
            ck_id, current_user, start_date, end_date, limit
        )

        return {"success": True, "data": failure_analysis, "message": "获取CK失败原因分析成功"}

    except HTTPException:
        raise
    except ValueError as e:
        raise HTTPException(status_code=http_status.HTTP_400_BAD_REQUEST, detail=str(e))
    except Exception as e:
        logger.error(f"获取CK失败原因分析失败: {str(e)}")
        raise HTTPException(
            status_code=http_status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取CK失败原因分析失败: {str(e)}",
        )


@router.get("/merchant/{merchant_id}/isolation-check", response_model=Dict[str, Any])
async def check_merchant_isolation_integrity(
    merchant_id: int,
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_active_user),
):
    """
    检查商户CK隔离完整性

    权限要求:
    - "walmart_ck:view": 查看沃尔玛CK权限
    - 超级管理员或该商户的用户
    """
    try:
        # 初始化服务
        ck_service = WalmartCKService(db)
        permission_service = PermissionService(db)

        # 权限检查
        has_permission = permission_service.check_user_permission(
            current_user, "api:walmart-ck:read"
        )
        if not has_permission:
            raise HTTPException(
                status_code=http_status.HTTP_403_FORBIDDEN,
                detail="没有查看沃尔玛CK的权限",
            )

        # 商户权限检查
        if not current_user.is_superuser and current_user.merchant_id != merchant_id:
            raise HTTPException(
                status_code=http_status.HTTP_403_FORBIDDEN,
                detail="无权限检查该商户的隔离完整性",
            )

        # 执行隔离完整性检查
        result = ck_service.check_merchant_isolation_integrity(merchant_id)

        return {
            "success": True,
            "data": result,
            "message": "商户隔离完整性检查完成",
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"检查商户隔离完整性失败: {str(e)}")
        raise HTTPException(
            status_code=http_status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"检查商户隔离完整性失败: {str(e)}",
        )


@router.post("/validate-isolation", response_model=Dict[str, Any])
async def validate_ck_merchant_isolation(
    *,
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_active_user),
    ck_id: int = Query(..., description="CK ID"),
    merchant_id: int = Query(..., description="商户ID"),
):
    """
    验证CK与商户的隔离关系

    权限要求:
    - "walmart_ck:view": 查看沃尔玛CK权限
    """
    try:
        # 初始化服务
        ck_service = WalmartCKService(db)
        permission_service = PermissionService(db)

        # 权限检查
        has_permission = permission_service.check_user_permission(
            current_user, "api:walmart-ck:read"
        )
        if not has_permission:
            raise HTTPException(
                status_code=http_status.HTTP_403_FORBIDDEN,
                detail="没有查看沃尔玛CK的权限",
            )

        # 商户权限检查
        if not current_user.is_superuser and current_user.merchant_id != merchant_id:
            raise HTTPException(
                status_code=http_status.HTTP_403_FORBIDDEN,
                detail="无权限验证该商户的CK隔离关系",
            )

        # 执行隔离验证
        is_valid = ck_service.validate_ck_merchant_isolation(ck_id, merchant_id)

        return {
            "success": True,
            "data": {
                "ck_id": ck_id,
                "merchant_id": merchant_id,
                "is_valid": is_valid,
                "message": "CK属于该商户" if is_valid else "CK不属于该商户"
            },
            "message": "CK商户隔离验证完成",
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"验证CK商户隔离失败: {str(e)}")
        raise HTTPException(
            status_code=http_status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"验证CK商户隔离失败: {str(e)}",
        )


@router.post("/validate-ck", response_model=Dict[str, Any])
async def validate_ck_availability(
    *,
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_active_user),
    ck_id: int = Query(..., description="CK ID"),
):
    """
    验证单个CK的有效性
    """
    try:
        # 权限检查
        if not current_user.is_superuser:
            # 非超级管理员需要检查CK是否属于其商户
            ck_service = WalmartCKService(db)
            if not ck_service.validate_ck_merchant_isolation(ck_id, current_user.merchant_id):
                raise HTTPException(
                    status_code=http_status.HTTP_403_FORBIDDEN,
                    detail="无权限验证该CK",
                )

        # 获取CK
        ck = db.query(WalmartCK).filter(WalmartCK.id == ck_id).first()
        if not ck:
            raise HTTPException(
                status_code=http_status.HTTP_404_NOT_FOUND,
                detail="CK不存在",
            )

        # 执行验证
        from app.services.ck_validation_service import CKValidationService
        validation_service = CKValidationService(db)
        validation_result = await validation_service.validate_ck_availability(ck)

        return {
            "success": True,
            "data": {
                "ck_id": ck_id,
                "is_valid": validation_result["is_valid"],
                "error_message": validation_result["error_message"],
                "error_code": validation_result["error_code"],
                "should_disable": validation_result["should_disable"],
                "current_usage": f"{ck.bind_count}/{ck.total_limit}"
            },
            "message": "CK验证完成",
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"验证CK有效性失败: {str(e)}")
        raise HTTPException(
            status_code=http_status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"验证CK有效性失败: {str(e)}",
        )


@router.post("/batch-validate-ck", response_model=Dict[str, Any])
async def batch_validate_ck_availability(
    *,
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_active_user),
    merchant_id: Optional[int] = Query(None, description="商户ID（超级管理员可指定）"),
):
    """
    批量验证商户下所有CK的有效性
    """
    try:
        # 确定目标商户ID
        target_merchant_id = merchant_id if current_user.is_superuser else current_user.merchant_id

        if not target_merchant_id:
            raise HTTPException(
                status_code=http_status.HTTP_400_BAD_REQUEST,
                detail="无法确定目标商户ID",
            )

        # 获取商户下的所有活跃CK
        cks = db.query(WalmartCK).filter(
            WalmartCK.merchant_id == target_merchant_id,
            WalmartCK.active == True,
            WalmartCK.is_deleted == False
        ).all()

        if not cks:
            return {
                "success": True,
                "data": {
                    "merchant_id": target_merchant_id,
                    "total_cks": 0,
                    "validation_results": []
                },
                "message": "该商户没有活跃的CK",
            }

        # 执行批量验证
        from app.services.ck_validation_service import CKValidationService
        validation_service = CKValidationService(db)
        validation_results = await validation_service.validate_multiple_cks(cks)

        # 统计结果
        valid_count = sum(1 for r in validation_results if r["is_valid"])
        invalid_count = len(validation_results) - valid_count
        disabled_count = sum(1 for r in validation_results if r["should_disable"])

        return {
            "success": True,
            "data": {
                "merchant_id": target_merchant_id,
                "total_cks": len(cks),
                "valid_cks": valid_count,
                "invalid_cks": invalid_count,
                "disabled_cks": disabled_count,
                "validation_results": validation_results
            },
            "message": f"批量验证完成，共验证{len(cks)}个CK",
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"批量验证CK有效性失败: {str(e)}")
        raise HTTPException(
            status_code=http_status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"批量验证CK有效性失败: {str(e)}",
        )


@router.get("/validation-statistics", response_model=Dict[str, Any])
async def get_ck_validation_statistics(
    *,
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_active_user),
    merchant_id: Optional[int] = Query(None, description="商户ID（超级管理员可指定）"),
):
    """
    获取CK验证统计信息
    """
    try:
        # 确定目标商户ID
        target_merchant_id = merchant_id if current_user.is_superuser else current_user.merchant_id

        # 获取统计信息
        from app.services.ck_validation_service import CKValidationService
        validation_service = CKValidationService(db)

        if target_merchant_id:
            # 商户级统计
            stats = validation_service.get_validation_statistics()
            # 过滤指定商户的数据
            merchant_cks = db.query(WalmartCK).filter(
                WalmartCK.merchant_id == target_merchant_id,
                WalmartCK.is_deleted == False
            ).all()

            total_cks = len(merchant_cks)
            active_cks = sum(1 for ck in merchant_cks if ck.active)
            reached_limit_cks = sum(1 for ck in merchant_cks if ck.bind_count >= ck.total_limit)

            merchant_stats = {
                "merchant_id": target_merchant_id,
                "total_cks": total_cks,
                "active_cks": active_cks,
                "inactive_cks": total_cks - active_cks,
                "reached_limit_cks": reached_limit_cks,
                "availability_rate": round(active_cks / total_cks * 100, 2) if total_cks > 0 else 0
            }

            return {
                "success": True,
                "data": merchant_stats,
                "message": "获取商户CK统计信息成功",
            }
        else:
            # 全局统计（仅超级管理员）
            stats = validation_service.get_validation_statistics()
            return {
                "success": True,
                "data": stats,
                "message": "获取全局CK统计信息成功",
            }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取CK验证统计信息失败: {str(e)}")
        raise HTTPException(
            status_code=http_status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取CK验证统计信息失败: {str(e)}",
        )


def _generate_csv_response(data: list, filename: str) -> StreamingResponse:
    """生成CSV格式的响应"""
    output = io.StringIO()

    if data:
        # 获取字段名
        fieldnames = data[0].keys()
        writer = csv.DictWriter(output, fieldnames=fieldnames)

        # 写入表头
        writer.writeheader()

        # 写入数据
        for row in data:
            writer.writerow(row)

    # 转换为字节流
    output.seek(0)
    content = output.getvalue().encode('utf-8-sig')  # 使用UTF-8 BOM以支持Excel

    return StreamingResponse(
        io.BytesIO(content),
        media_type="text/csv",
        headers={"Content-Disposition": f"attachment; filename={filename}"}
    )


def _generate_json_response(data: list, filename: str) -> StreamingResponse:
    """生成JSON格式的响应"""
    # 准备导出数据
    export_data = {
        "export_time": datetime.now().isoformat(),
        "total_count": len(data),
        "data": data
    }

    # 转换为JSON字符串
    json_content = json.dumps(export_data, ensure_ascii=False, indent=2)
    content = json_content.encode('utf-8')

    return StreamingResponse(
        io.BytesIO(content),
        media_type="application/json",
        headers={"Content-Disposition": f"attachment; filename={filename}"}
    )
