"""
新仪表盘API测试用例

测试重新设计的仪表盘功能，包括：
1. 绑卡金额统计API
2. 绑卡成功率统计API  
3. CK使用效率统计API
4. 异常/失败统计API
5. 部门业绩排名API
6. 权限隔离验证
"""

import pytest
import uuid
from datetime import datetime, timedelta
from fastapi.testclient import TestClient
from sqlalchemy.orm import Session

from app.main import app
from app.models.card_record import CardRecord, CardStatus
from app.models.user import User
from app.models.merchant import Merchant
from app.models.department import Department
from app.models.walmart_ck import WalmartCK
from app.core.deps import get_current_active_user, get_db
from test.conftest import TestDatabase

client = TestClient(app)


class TestNewDashboardAPI:
    """新仪表盘API测试类"""

    @pytest.fixture(autouse=True)
    def setup_method(self, test_db: TestDatabase):
        """测试前置设置"""
        self.db = test_db.get_db()
        self.test_db = test_db
        
        # 创建测试数据
        self._create_test_data()

    def _create_test_data(self):
        """创建测试数据"""
        # 创建测试商户
        self.merchant = Merchant(
            name="测试商户",
            code="TEST001",
            api_key="test_api_key",
            api_secret="test_api_secret",
            status=True
        )
        self.db.add(self.merchant)
        self.db.commit()
        self.db.refresh(self.merchant)

        # 创建测试部门
        self.department = Department(
            name="测试部门",
            code="DEPT001",
            merchant_id=self.merchant.id,
            status=True
        )
        self.db.add(self.department)
        self.db.commit()
        self.db.refresh(self.department)

        # 创建测试CK
        self.walmart_ck = WalmartCK(
            username="test_ck_user",
            password="test_password",
            merchant_id=self.merchant.id,
            department_id=self.department.id,
            status=1
        )
        self.db.add(self.walmart_ck)
        self.db.commit()
        self.db.refresh(self.walmart_ck)

        # 创建测试用户
        self.merchant_admin = User(
            username="merchant_admin",
            email="<EMAIL>",
            hashed_password="hashed_password",
            merchant_id=self.merchant.id,
            is_active=True
        )
        self.db.add(self.merchant_admin)
        self.db.commit()
        self.db.refresh(self.merchant_admin)

        # 创建测试卡记录
        self._create_test_card_records()

    def _create_test_card_records(self):
        """创建测试卡记录"""
        now = datetime.now()
        
        # 创建成功记录
        for i in range(5):
            record = CardRecord(
                id=uuid.uuid4(),
                merchant_id=self.merchant.id,
                department_id=self.department.id,
                walmart_ck_id=self.walmart_ck.id,
                merchant_order_id=f"ORDER_SUCCESS_{i}",
                amount=10000,  # 100元
                card_number=f"1234567890{i}",
                status=CardStatus.SUCCESS,
                request_id=f"req_success_{i}",
                request_data={"test": "data"},
                process_time=2.5,
                created_at=now - timedelta(hours=i)
            )
            self.db.add(record)

        # 创建失败记录
        for i in range(3):
            record = CardRecord(
                id=uuid.uuid4(),
                merchant_id=self.merchant.id,
                merchant_order_id=f"ORDER_FAILED_{i}",
                amount=5000,  # 50元
                card_number=f"9876543210{i}",
                status=CardStatus.FAILED,
                request_id=f"req_failed_{i}",
                request_data={"test": "data"},
                error_message=f"测试错误原因_{i}",
                created_at=now - timedelta(hours=i)
            )
            self.db.add(record)

        self.db.commit()

    def _get_auth_headers(self, user: User):
        """获取认证头"""
        # 模拟JWT token
        return {"Authorization": "Bearer test_token"}

    def test_get_amount_statistics(self):
        """测试绑卡金额统计API"""
        # 模拟当前用户
        def mock_get_current_user():
            return self.merchant_admin

        app.dependency_overrides[get_current_active_user] = mock_get_current_user

        try:
            response = client.get(
                "/api/v1/dashboard/amount-statistics",
                params={"time_range": "today"},
                headers=self._get_auth_headers(self.merchant_admin)
            )
            
            assert response.status_code == 200
            data = response.json()["data"]
            
            # 验证返回数据结构
            assert "total_requests" in data
            assert "success_count" in data
            assert "failed_count" in data
            assert "total_amount_yuan" in data
            assert "success_amount_yuan" in data
            assert "failed_amount_yuan" in data
            assert "success_rate" in data
            
            # 验证数据正确性
            assert data["total_requests"] == 8  # 5成功 + 3失败
            assert data["success_count"] == 5
            assert data["failed_count"] == 3
            assert data["success_rate"] == 62.5  # 5/8 * 100

        finally:
            app.dependency_overrides.clear()

    def test_get_success_rate_statistics(self):
        """测试绑卡成功率统计API"""
        def mock_get_current_user():
            return self.merchant_admin

        app.dependency_overrides[get_current_active_user] = mock_get_current_user

        try:
            response = client.get(
                "/api/v1/dashboard/success-rate-statistics",
                params={"time_range": "today"},
                headers=self._get_auth_headers(self.merchant_admin)
            )
            
            assert response.status_code == 200
            data = response.json()["data"]
            
            # 验证返回数据结构
            assert "overall_success_rate" in data
            assert "total_requests" in data
            assert "total_success" in data
            assert "hourly_breakdown" in data
            
            # 验证数据类型
            assert isinstance(data["hourly_breakdown"], list)

        finally:
            app.dependency_overrides.clear()

    def test_get_ck_efficiency_statistics(self):
        """测试CK使用效率统计API"""
        def mock_get_current_user():
            return self.merchant_admin

        app.dependency_overrides[get_current_active_user] = mock_get_current_user

        try:
            response = client.get(
                "/api/v1/dashboard/ck-efficiency-statistics",
                params={"time_range": "today"},
                headers=self._get_auth_headers(self.merchant_admin)
            )
            
            assert response.status_code == 200
            data = response.json()["data"]
            
            # 验证返回数据结构
            assert "total_ck_count" in data
            assert "active_ck_count" in data
            assert "total_usage" in data
            assert "average_efficiency" in data
            assert "ck_details" in data
            
            # 验证数据类型
            assert isinstance(data["ck_details"], list)

        finally:
            app.dependency_overrides.clear()

    def test_get_failure_statistics(self):
        """测试异常/失败统计API"""
        def mock_get_current_user():
            return self.merchant_admin

        app.dependency_overrides[get_current_active_user] = mock_get_current_user

        try:
            response = client.get(
                "/api/v1/dashboard/failure-statistics",
                params={"time_range": "today"},
                headers=self._get_auth_headers(self.merchant_admin)
            )
            
            assert response.status_code == 200
            data = response.json()["data"]
            
            # 验证返回数据结构
            assert "total_failed_count" in data
            assert "total_failed_amount_yuan" in data
            assert "failure_breakdown" in data
            assert "top_error_reasons" in data
            
            # 验证失败数据
            assert data["total_failed_count"] == 3
            assert isinstance(data["failure_breakdown"], list)
            assert isinstance(data["top_error_reasons"], list)

        finally:
            app.dependency_overrides.clear()

    def test_get_department_ranking(self):
        """测试部门业绩排名API"""
        def mock_get_current_user():
            return self.merchant_admin

        app.dependency_overrides[get_current_active_user] = mock_get_current_user

        try:
            response = client.get(
                "/api/v1/dashboard/department-ranking",
                params={"time_range": "today"},
                headers=self._get_auth_headers(self.merchant_admin)
            )
            
            assert response.status_code == 200
            data = response.json()["data"]
            
            # 验证返回数据类型
            assert isinstance(data, list)
            
            # 如果有数据，验证数据结构
            if data:
                dept_data = data[0]
                assert "rank" in dept_data
                assert "department_name" in dept_data
                assert "department_code" in dept_data
                assert "total_requests" in dept_data
                assert "success_count" in dept_data
                assert "success_rate" in dept_data
                assert "success_amount_yuan" in dept_data

        finally:
            app.dependency_overrides.clear()

    def test_new_dashboard_statistics(self):
        """测试新版仪表盘统计API"""
        def mock_get_current_user():
            return self.merchant_admin

        app.dependency_overrides[get_current_active_user] = mock_get_current_user

        try:
            response = client.get(
                "/api/v1/dashboard/statistics",
                params={"time_range": "today"},
                headers=self._get_auth_headers(self.merchant_admin)
            )
            
            assert response.status_code == 200
            data = response.json()["data"]
            
            # 验证返回数据结构
            assert "time_range" in data
            assert "amount_statistics" in data
            assert "success_rate_statistics" in data
            assert "ck_efficiency_statistics" in data
            assert "failure_statistics" in data
            assert "department_ranking" in data
            assert "generated_at" in data
            
            # 验证时间范围
            assert data["time_range"] == "today"

        finally:
            app.dependency_overrides.clear()

    def test_permission_isolation(self):
        """测试权限隔离"""
        # 创建另一个商户的用户
        other_merchant = Merchant(
            name="其他商户",
            code="OTHER001",
            api_key="other_api_key",
            api_secret="other_api_secret",
            status=True
        )
        self.db.add(other_merchant)
        self.db.commit()
        self.db.refresh(other_merchant)

        other_user = User(
            username="other_user",
            email="<EMAIL>",
            hashed_password="hashed_password",
            merchant_id=other_merchant.id,
            is_active=True
        )
        self.db.add(other_user)
        self.db.commit()
        self.db.refresh(other_user)

        def mock_get_current_user():
            return other_user

        app.dependency_overrides[get_current_active_user] = mock_get_current_user

        try:
            response = client.get(
                "/api/v1/dashboard/amount-statistics",
                params={"time_range": "today"},
                headers=self._get_auth_headers(other_user)
            )
            
            assert response.status_code == 200
            data = response.json()["data"]
            
            # 其他商户用户应该看不到测试商户的数据
            assert data["total_requests"] == 0
            assert data["success_count"] == 0
            assert data["failed_count"] == 0

        finally:
            app.dependency_overrides.clear()


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
