#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
商户管理CRUD操作测试
测试商户的创建、读取、更新、删除功能
"""

import sys
import os
import time
import random
import string

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.dirname(__file__))))

from test.conftest import TestBase, get_test_accounts, format_test_result, print_test_summary

class MerchantsCrudTestSuite(TestBase):
    """商户CRUD操作测试套件"""
    
    def __init__(self):
        super().__init__()
        self.results = []
        self.test_accounts = get_test_accounts()
        self.admin_token = None
        self.merchant_token = None
        self.created_merchants = []  # 记录创建的测试商户，用于清理
    
    def setup(self):
        """测试前置设置"""
        print("=== 测试前置设置 ===")
        
        # 管理员登录
        self.admin_token = self.login("admin", "7c222fb2927d828af22f592134e8932480637c0d")
        if not self.admin_token:
            print("❌ 管理员登录失败")
            return False
        
        # 商户管理员登录
        self.merchant_token = self.login("test1", "********")
        if not self.merchant_token:
            print("❌ 商户管理员登录失败")
            return False
        
        print("✅ 测试前置设置完成")
        return True
    
    def generate_test_merchant_data(self, prefix: str = "test") -> dict:
        """生成测试商户数据"""
        timestamp = int(time.time())
        random_suffix = ''.join(random.choices(string.ascii_lowercase, k=4))
        
        return {
            "name": f"{prefix}_商户_{timestamp}",
            "code": f"{prefix.upper()}_{timestamp}_{random_suffix}",
            "api_key": f"api_key_{timestamp}_{random_suffix}",
            "api_secret": f"api_secret_{timestamp}_{random_suffix}",
            "status": 1,
            "daily_limit": 1000,
            "hourly_limit": 100,
            "contact_name": f"联系人_{timestamp}",
            "contact_phone": "13800138000",
            "contact_email": f"contact_{timestamp}@example.com",
            "remark": f"测试商户_{timestamp}"
        }
    
    def test_create_merchant(self):
        """测试创建商户"""
        print("\n=== 测试创建商户 ===")
        
        if not self.admin_token:
            self.results.append(format_test_result(
                "创建商户前置条件",
                False,
                "缺少管理员token"
            ))
            return
        
        # 测试管理员创建商户
        test_merchant = self.generate_test_merchant_data("admin_create")
        status_code, response = self.make_request(
            "POST", "/merchants", self.admin_token, data=test_merchant
        )
        
        if status_code == 201 or status_code == 200:
            merchant_id = response.get("data", {}).get("id") or response.get("id")
            if merchant_id:
                self.created_merchants.append(merchant_id)
                self.results.append(format_test_result(
                    "管理员创建商户",
                    True,
                    f"成功创建商户: {test_merchant['name']}",
                    {"merchant_id": merchant_id, "merchant_name": test_merchant['name']}
                ))
                print(f"✅ 管理员成功创建商户: {test_merchant['name']}")
            else:
                self.results.append(format_test_result(
                    "管理员创建商户",
                    False,
                    f"创建商户成功但未返回商户ID，状态码: {status_code}"
                ))
                print(f"⚠️ 创建商户成功但未返回商户ID，状态码: {status_code}")
        else:
            self.results.append(format_test_result(
                "管理员创建商户",
                False,
                f"创建商户失败，状态码: {status_code}，响应: {response}"
            ))
            print(f"❌ 管理员创建商户失败，状态码: {status_code}")
        
        # 测试商户管理员创建商户（应该被拒绝）
        test_merchant_by_merchant = self.generate_test_merchant_data("merchant_create")
        status_code, response = self.make_request(
            "POST", "/merchants", self.merchant_token, data=test_merchant_by_merchant
        )
        
        if status_code in [403, 401]:
            self.results.append(format_test_result(
                "商户管理员创建商户权限控制",
                True,
                "正确拒绝商户管理员创建商户"
            ))
            print("✅ 正确拒绝商户管理员创建商户")
        else:
            self.results.append(format_test_result(
                "商户管理员创建商户权限控制",
                False,
                f"商户管理员不应该能创建商户，状态码: {status_code}"
            ))
            print(f"❌ 商户管理员不应该能创建商户，状态码: {status_code}")
    
    def test_get_merchants_list(self):
        """测试获取商户列表"""
        print("\n=== 测试获取商户列表 ===")
        
        # 测试管理员获取商户列表
        if self.admin_token:
            status_code, response = self.make_request("GET", "/merchants", self.admin_token)
            
            if status_code == 200:
                merchants_data = response.get("data", {})
                merchants = merchants_data.get("items", []) if isinstance(merchants_data, dict) else response.get("items", [])
                
                self.results.append(format_test_result(
                    "管理员获取商户列表",
                    True,
                    f"成功获取 {len(merchants)} 个商户",
                    {"merchant_count": len(merchants)}
                ))
                print(f"✅ 管理员成功获取商户列表，共 {len(merchants)} 个商户")
            else:
                self.results.append(format_test_result(
                    "管理员获取商户列表",
                    False,
                    f"获取商户列表失败，状态码: {status_code}"
                ))
                print(f"❌ 管理员获取商户列表失败，状态码: {status_code}")
        
        # 测试商户管理员获取商户列表（应该被限制或只能看到自己的商户）
        if self.merchant_token:
            status_code, response = self.make_request("GET", "/merchants", self.merchant_token)
            
            if status_code == 200:
                merchants_data = response.get("data", {})
                merchants = merchants_data.get("items", []) if isinstance(merchants_data, dict) else response.get("items", [])
                
                # 商户管理员应该只能看到自己的商户或被拒绝访问
                if len(merchants) <= 1:
                    self.results.append(format_test_result(
                        "商户管理员商户列表数据隔离",
                        True,
                        f"商户管理员正确只能看到 {len(merchants)} 个商户（自己的）"
                    ))
                    print(f"✅ 商户管理员正确只能看到 {len(merchants)} 个商户")
                else:
                    self.results.append(format_test_result(
                        "商户管理员商户列表数据隔离",
                        False,
                        f"商户管理员不应该看到 {len(merchants)} 个商户"
                    ))
                    print(f"❌ 商户管理员不应该看到 {len(merchants)} 个商户")
            elif status_code in [403, 401]:
                self.results.append(format_test_result(
                    "商户管理员商户列表权限控制",
                    True,
                    "正确拒绝商户管理员访问商户列表"
                ))
                print("✅ 正确拒绝商户管理员访问商户列表")
            else:
                self.results.append(format_test_result(
                    "商户管理员获取商户列表",
                    False,
                    f"获取商户列表失败，状态码: {status_code}"
                ))
                print(f"❌ 商户管理员获取商户列表失败，状态码: {status_code}")
    
    def test_get_merchant_detail(self):
        """测试获取商户详情"""
        print("\n=== 测试获取商户详情 ===")
        
        if not self.admin_token:
            return
        
        # 先获取商户列表，找一个商户ID
        status_code, response = self.make_request("GET", "/merchants", self.admin_token)
        if status_code != 200:
            self.results.append(format_test_result(
                "获取商户详情前置条件",
                False,
                "无法获取商户列表"
            ))
            return
        
        merchants_data = response.get("data", {})
        merchants = merchants_data.get("items", []) if isinstance(merchants_data, dict) else response.get("items", [])
        
        if not merchants:
            self.results.append(format_test_result(
                "获取商户详情前置条件",
                False,
                "商户列表为空"
            ))
            return
        
        # 测试获取第一个商户的详情
        merchant_id = merchants[0].get("id")
        if not merchant_id:
            self.results.append(format_test_result(
                "获取商户详情前置条件",
                False,
                "商户数据中缺少ID字段"
            ))
            return
        
        status_code, response = self.make_request("GET", f"/merchants/{merchant_id}", self.admin_token)
        
        if status_code == 200:
            merchant_detail = response.get("data", response)
            self.results.append(format_test_result(
                "获取商户详情",
                True,
                f"成功获取商户详情: {merchant_detail.get('name', 'unknown')}",
                {"merchant_id": merchant_id}
            ))
            print(f"✅ 成功获取商户详情: {merchant_detail.get('name', 'unknown')}")
        else:
            self.results.append(format_test_result(
                "获取商户详情",
                False,
                f"获取商户详情失败，状态码: {status_code}"
            ))
            print(f"❌ 获取商户详情失败，状态码: {status_code}")
    
    def test_update_merchant(self):
        """测试更新商户"""
        print("\n=== 测试更新商户 ===")
        
        if not self.admin_token or not self.created_merchants:
            self.results.append(format_test_result(
                "更新商户前置条件",
                False,
                "缺少管理员token或测试商户"
            ))
            return
        
        # 使用创建的测试商户进行更新测试
        merchant_id = self.created_merchants[0]
        update_data = {
            "name": f"更新的测试商户_{int(time.time())}",
            "contact_name": f"更新的联系人_{int(time.time())}",
            "remark": f"更新的备注_{int(time.time())}"
        }
        
        status_code, response = self.make_request(
            "PUT", f"/merchants/{merchant_id}", self.admin_token, data=update_data
        )
        
        if status_code == 200:
            self.results.append(format_test_result(
                "更新商户",
                True,
                f"成功更新商户: {merchant_id}",
                {"merchant_id": merchant_id, "update_data": update_data}
            ))
            print(f"✅ 成功更新商户: {merchant_id}")
        else:
            self.results.append(format_test_result(
                "更新商户",
                False,
                f"更新商户失败，状态码: {status_code}，响应: {response}"
            ))
            print(f"❌ 更新商户失败，状态码: {status_code}")
    
    def test_delete_merchant(self):
        """测试删除商户"""
        print("\n=== 测试删除商户 ===")
        
        if not self.admin_token or not self.created_merchants:
            self.results.append(format_test_result(
                "删除商户前置条件",
                False,
                "缺少管理员token或测试商户"
            ))
            return
        
        # 删除创建的测试商户
        for merchant_id in self.created_merchants[:]:  # 使用切片复制，避免在迭代时修改列表
            status_code, response = self.make_request(
                "DELETE", f"/merchants/{merchant_id}", self.admin_token
            )
            
            if status_code in [200, 204]:
                self.results.append(format_test_result(
                    f"删除商户_{merchant_id}",
                    True,
                    f"成功删除商户: {merchant_id}"
                ))
                print(f"✅ 成功删除商户: {merchant_id}")
                self.created_merchants.remove(merchant_id)
            else:
                self.results.append(format_test_result(
                    f"删除商户_{merchant_id}",
                    False,
                    f"删除商户失败，状态码: {status_code}"
                ))
                print(f"❌ 删除商户失败，状态码: {status_code}")
    
    def cleanup(self):
        """清理测试数据"""
        print("\n=== 清理测试数据 ===")
        
        if self.admin_token and self.created_merchants:
            for merchant_id in self.created_merchants[:]:
                status_code, _ = self.make_request(
                    "DELETE", f"/merchants/{merchant_id}", self.admin_token
                )
                if status_code in [200, 204]:
                    print(f"✅ 清理测试商户: {merchant_id}")
                    self.created_merchants.remove(merchant_id)
                else:
                    print(f"⚠️ 清理测试商户失败: {merchant_id}")
    
    def run_all_tests(self):
        """运行所有商户CRUD测试"""
        print("🚀 开始商户CRUD操作测试")
        print("="*60)
        
        start_time = time.time()
        
        # 前置设置
        if not self.setup():
            print("❌ 测试前置设置失败，跳过测试")
            return []
        
        try:
            # 运行所有测试
            self.test_create_merchant()
            self.test_get_merchants_list()
            self.test_get_merchant_detail()
            self.test_update_merchant()
            self.test_delete_merchant()
        finally:
            # 清理测试数据
            self.cleanup()
        
        end_time = time.time()
        duration = end_time - start_time
        
        # 打印测试摘要
        print_test_summary(self.results)
        print(f"测试耗时: {duration:.2f}秒")
        
        return self.results

def main():
    """主函数"""
    test_suite = MerchantsCrudTestSuite()
    results = test_suite.run_all_tests()
    
    # 判断测试是否通过
    failed_tests = [r for r in results if not r.get("success", False)]
    
    if not failed_tests:
        print("\n🎉 所有商户CRUD测试通过！")
        return 0
    else:
        print(f"\n⚠️ 发现 {len(failed_tests)} 个问题需要修复")
        for test in failed_tests:
            print(f"  - {test['test_name']}: {test['message']}")
        return 1

if __name__ == "__main__":
    sys.exit(main())
