/**
 * 权限检查工具函数
 * 用于在各个模块中进行统一的权限检查
 */

/**
 * 检查用户是否有指定的API权限
 * @param {Object} userInfo - 用户信息对象
 * @param {string} permissionCode - 权限代码
 * @returns {boolean} - 是否有权限
 */
export function hasApiPermission(userInfo, permissionCode) {
    // 超级管理员拥有所有权限
    if (userInfo?.is_superuser) {
        return true
    }
    
    // 检查用户权限列表
    const userPermissions = userInfo?.permissions || []
    return userPermissions.includes(permissionCode)
}

/**
 * 检查用户是否有商户查询权限
 * @param {Object} userInfo - 用户信息对象
 * @returns {boolean} - 是否有商户查询权限
 */
export function hasMerchantReadPermission(userInfo) {
    return hasApiPermission(userInfo, 'api:merchants:read')
}

/**
 * 检查用户是否有菜单访问权限
 * @param {Object} userInfo - 用户信息对象
 * @param {string} menuCode - 菜单代码
 * @returns {boolean} - 是否有菜单权限
 */
export function hasMenuPermission(userInfo, menuCode) {
    // 超级管理员拥有所有菜单权限
    if (userInfo?.is_superuser) {
        return true
    }
    
    // 检查用户菜单列表
    const userMenus = userInfo?.menus || []
    return userMenus.includes(menuCode)
}

/**
 * 检查用户是否可以访问指定商户的数据
 * @param {Object} userInfo - 用户信息对象
 * @param {number} targetMerchantId - 目标商户ID
 * @returns {boolean} - 是否可以访问
 */
export function canAccessMerchantData(userInfo, targetMerchantId) {
    // 超级管理员可以访问所有数据
    if (userInfo?.is_superuser) {
        return true
    }

    // 检查是否有访问所有商户数据的权限
    if (hasApiPermission(userInfo, 'data:merchant:all')) {
        return true
    }

    // 检查是否有访问本商户数据的权限，且商户ID匹配
    if (hasApiPermission(userInfo, 'data:merchant:own')) {
        return userInfo?.merchant_id === targetMerchantId
    }

    return false
}

/**
 * 检查用户是否可以访问指定用户的数据
 * @param {Object} userInfo - 当前用户信息对象
 * @param {number} targetUserId - 目标用户ID
 * @returns {boolean} - 是否可以访问
 */
export function canAccessUserData(userInfo, targetUserId) {
    // 超级管理员可以访问所有用户数据
    if (userInfo?.is_superuser) {
        return true
    }

    // 检查是否有访问所有用户数据的权限
    if (hasApiPermission(userInfo, 'data:user:all')) {
        return true
    }

    // 检查是否只能访问本人数据
    if (hasApiPermission(userInfo, 'data:user:own')) {
        return userInfo?.id === targetUserId
    }

    // 检查是否可以访问本部门用户数据
    if (hasApiPermission(userInfo, 'data:user:department')) {
        // 这里需要获取目标用户的部门信息进行比较
        // 暂时返回 false，实际使用时需要调用API获取用户部门信息
        return false
    }

    return false
}

/**
 * 获取权限检查失败的提示信息
 * @param {string} permissionCode - 权限代码
 * @returns {string} - 提示信息
 */
export function getPermissionDeniedMessage(permissionCode) {
    const permissionMessages = {
        'api:merchants:read': '您没有商户查询权限',
        'api:users:read': '您没有用户查询权限',
        'api:departments:read': '您没有部门查询权限',
        'api:cards:read': '您没有绑卡数据查询权限',
        'api:walmart-ck:read': '您没有CK管理权限'
    }
    
    return permissionMessages[permissionCode] || `您没有 ${permissionCode} 权限`
}

/**
 * 权限检查结果对象
 */
export class PermissionCheckResult {
    constructor(hasPermission, message = '', suggestions = []) {
        this.hasPermission = hasPermission
        this.message = message
        this.suggestions = suggestions
    }
    
    static success(message = '权限检查通过') {
        return new PermissionCheckResult(true, message)
    }
    
    static denied(permissionCode, suggestions = []) {
        const message = getPermissionDeniedMessage(permissionCode)
        return new PermissionCheckResult(false, message, suggestions)
    }
}

/**
 * 综合权限检查函数
 * @param {Object} userInfo - 用户信息对象
 * @param {string|Array} permissions - 权限代码或权限代码数组
 * @param {boolean} requireAll - 是否需要所有权限（默认true）
 * @returns {PermissionCheckResult} - 权限检查结果
 */
export function checkPermissions(userInfo, permissions, requireAll = true) {
    if (!userInfo) {
        return PermissionCheckResult.denied('user:login', ['请先登录系统'])
    }
    
    // 超级管理员拥有所有权限
    if (userInfo.is_superuser) {
        return PermissionCheckResult.success('超级管理员拥有所有权限')
    }
    
    const permissionList = Array.isArray(permissions) ? permissions : [permissions]
    const userPermissions = userInfo.permissions || []
    
    if (requireAll) {
        // 需要所有权限
        const missingPermissions = permissionList.filter(p => !userPermissions.includes(p))
        if (missingPermissions.length > 0) {
            return PermissionCheckResult.denied(
                missingPermissions[0], 
                [`缺少权限: ${missingPermissions.join(', ')}`]
            )
        }
    } else {
        // 只需要任一权限
        const hasAnyPermission = permissionList.some(p => userPermissions.includes(p))
        if (!hasAnyPermission) {
            return PermissionCheckResult.denied(
                permissionList[0],
                [`需要以下任一权限: ${permissionList.join(', ')}`]
            )
        }
    }
    
    return PermissionCheckResult.success()
}
