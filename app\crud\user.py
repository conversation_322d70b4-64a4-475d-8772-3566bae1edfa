from typing import Optional, List, Any, Dict, Union
from sqlalchemy.orm import Session, selectinload
from sqlalchemy import or_

from app.models.user import User
from app.models.permission import Permission
from app.models.role import Role
from app.utils.password import (
    get_password_hash,
    verify_password as utils_verify_password,
)
from app.crud.base import CRUDBase
from app.schemas.user import UserCreate, UserUpdate

from app.core.logging import get_logger

logger = get_logger("crud.user")


def get_user_by_email(db: Session, email: str) -> Optional[User]:
    """
    通过邮箱获取用户

    Args:
        db: 数据库会话
        email: 邮箱

    Returns:
        Optional[User]: 用户对象，不存在则返回 None
    """
    return db.query(User).filter(User.email == email).first()


def get_user_by_id(db: Session, user_id: int) -> Optional[User]:
    """
    通过 ID 获取用户

    Args:
        db: 数据库会话
        user_id: 用户 ID

    Returns:
        Optional[User]: 用户对象，不存在则返回 None
    """
    return db.query(User).filter(User.id == user_id).first()


def get_users(
    db: Session,
    skip: int = 0,
    limit: int = 100,
    merchant_id: Optional[int] = None,
    role_code: Optional[str] = None,
) -> List[User]:
    """
    获取用户列表

    Args:
        db: 数据库会话
        skip: 跳过数量
        limit: 限制数量
        merchant_id: 商家 ID，用于过滤特定商家的用户
        role: 用户角色，用于过滤特定角色的用户

    Returns:
        List[User]: 用户列表
    """
    query = db.query(User)

    if merchant_id is not None:
        query = query.filter(User.merchant_id == merchant_id)

    if role_code is not None:
        # 通过角色关系过滤用户
        query = query.join(User.roles).filter(Role.code == role_code)

    return query.offset(skip).limit(limit).all()


def create_user(
    db: Session,
    username: str,
    password: str,
    email: Optional[str] = None,
    full_name: Optional[str] = None,
    role_codes: Optional[List[str]] = None,
    merchant_id: Optional[int] = None,
    department_id: Optional[int] = None,
    is_active: bool = True,
    **extra_data,
) -> User:
    """
    创建用户

    Args:
        db: 数据库会话
        username: 用户名
        password: 密码
        email: 邮箱
        full_name: 姓名
        role_codes: 用户角色代码列表
        merchant_id: 所属商家 ID
        is_active: 是否激活
        **extra_data: 额外数据

    Returns:
        User: 创建的用户对象
    """
    # 创建用户数据
    user_data = {
        "username": username,
        "hashed_password": get_password_hash(password),
        "email": email,
        "full_name": full_name,
        "merchant_id": merchant_id,
        "department_id": department_id,
        "is_active": is_active,
        **extra_data,
    }

    # 创建用户对象
    db_user = User(**user_data)

    # 添加到数据库
    db.add(db_user)
    db.commit()
    db.refresh(db_user)

    # 分配角色
    if role_codes:
        from app.crud.role import role as role_crud
        for role_code in role_codes:
            role = role_crud.get_by_code(db, role_code)
            if role:
                db_user.roles.append(role)
        db.commit()
        db.refresh(db_user)

    return db_user


def update_user(
    db: Session, user_id: int, update_data: Dict[str, Any]
) -> Optional[User]:
    """
    更新用户信息

    Args:
        db: 数据库会话
        user_id: 用户 ID
        update_data: 更新数据

    Returns:
        Optional[User]: 更新后的用户对象，不存在则返回 None
    """
    # 获取用户
    user = get_user_by_id(db, user_id)
    if not user:
        return None

    # 如果更新数据中包含密码，需要进行哈希处理
    if "password" in update_data:
        update_data["hashed_password"] = get_password_hash(update_data.pop("password"))

    # 更新用户数据
    for key, value in update_data.items():
        if hasattr(user, key):
            setattr(user, key, value)

    # 提交更新
    db.commit()
    db.refresh(user)

    return user


class CRUDUser(CRUDBase[User, UserCreate, UserUpdate]):
    """用户CRUD操作类"""

    def get_by_username(self, db: Session, username: str) -> Optional[User]:
        """通过用户名获取用户"""
        return db.query(User).filter(User.username == username).first()

    def get_by_email(self, db: Session, email: str) -> Optional[User]:
        """通过邮箱获取用户"""
        return get_user_by_email(db, email)

    def get(self, db: Session, id: int) -> Optional[User]:
        """通过ID获取用户，覆盖基类方法，保持与get_by_id兼容"""
        return get_user_by_id(db, user_id=id)

    def get_by_id(self, db: Session, user_id: int) -> Optional[User]:
        """通过ID获取用户（保留此方法以兼容现有代码）"""
        return self.get(db, id=user_id)

    def is_active(self, user: User) -> bool:
        """检查用户是否处于活跃状态"""
        return bool(user.is_active)

    def is_superuser(self, user: User) -> bool:
        """检查用户是否为超级管理员"""
        return user.is_superuser

    def verify_password(self, plain_password: str, hashed_password: str) -> bool:
        """
        验证密码

        Args:
            plain_password: 原始密码
            hashed_password: 哈希后的密码

        Returns:
            bool: 密码是否匹配
        """
        return utils_verify_password(plain_password, hashed_password)

    def create_with_password(
        self,
        db: Session,
        username: str,
        password: str,
        email: Optional[str] = None,
        full_name: Optional[str] = None,
        role_codes: Optional[List[str]] = None,
        merchant_id: Optional[int] = None,
        department_id: Optional[int] = None,
        is_active: bool = True,
        **extra_data,
    ) -> User:
        """创建用户并设置密码"""
        return create_user(
            db,
            username=username,
            password=password,
            email=email,
            full_name=full_name,
            role_codes=role_codes,
            merchant_id=merchant_id,
            department_id=department_id,
            is_active=is_active,
            **extra_data,
        )

    def update_user_info(
        self, db: Session, user_id: int, update_data: Dict[str, Any]
    ) -> Optional[User]:
        """更新用户信息"""
        return update_user(db, user_id, update_data)

    def get_multi_with_filters(
        self, db: Session, *, skip: int = 0, limit: int = 100, filters: dict = None
    ) -> List[User]:
        """
        获取用户列表，支持过滤条件

        Args:
            db: 数据库会话
            skip: 跳过数量
            limit: 限制数量
            filters: 过滤条件字典

        Returns:
            List[User]: 用户列表
        """
        query = db.query(self.model)

        if filters:
            for field, value in filters.items():
                if value is not None:
                    if field == "exclude_roles" and isinstance(value, list):
                        # 排除指定角色（通过角色关系）
                        query = query.filter(~self.model.roles.any(Role.code.in_(value)))
                    elif field == "user_id":
                        # 用户ID过滤（用于data:user:own权限）
                        query = query.filter(self.model.id == value)
                    elif field == "department_id__in" and isinstance(value, list):
                        # 部门ID列表过滤（用于data:user:department权限）
                        query = query.filter(self.model.department_id.in_(value))
                    elif hasattr(self.model, field):
                        if field == "username" and isinstance(value, str):
                            # 用户名模糊查询
                            query = query.filter(
                                getattr(self.model, field).ilike(f"%{value}%")
                            )
                        else:
                            query = query.filter(getattr(self.model, field) == value)

        return query.offset(skip).limit(limit).all()

    def get_multi_with_filters_and_count(
        self, db: Session, *, skip: int = 0, limit: int = 100, filters: dict = None
    ) -> tuple[List[User], int]:
        """
        获取用户列表和总数，支持过滤条件

        Args:
            db: 数据库会话
            skip: 跳过数量
            limit: 限制数量
            filters: 过滤条件字典

        Returns:
            tuple[List[User], int]: 用户列表和总数
        """
        query = db.query(self.model)

        if filters:
            for field, value in filters.items():
                if value is not None:
                    if field == "exclude_roles" and isinstance(value, list):
                        # 排除指定角色（通过角色关系）
                        query = query.filter(~self.model.roles.any(Role.code.in_(value)))
                    elif field == "user_id":
                        # 用户ID过滤（用于data:user:own权限）
                        query = query.filter(self.model.id == value)
                    elif field == "department_id__in" and isinstance(value, list):
                        # 部门ID列表过滤（用于data:user:department权限）
                        query = query.filter(self.model.department_id.in_(value))
                    elif hasattr(self.model, field):
                        if field == "username" and isinstance(value, str):
                            # 用户名模糊查询
                            query = query.filter(
                                getattr(self.model, field).ilike(f"%{value}%")
                            )
                        else:
                            query = query.filter(getattr(self.model, field) == value)

        # 计算总数
        total = query.count()

        # 获取分页数据
        users = query.offset(skip).limit(limit).all()

        return users, total

    def has_permission(self, user: User, permission_code: str) -> bool:
        """
        检查用户是否直接拥有或通过角色拥有指定权限。
        超级管理员默认拥有所有权限。

        Args:
            user: 用户对象 (应预加载 permissions)。
            permission_code: 权限代码 (e.g., "user:view").

        Returns:
            bool: 如果用户拥有权限则返回 True。
        """

        if self.is_superuser(user):
            return True

        # 检查用户直接权限
        if hasattr(user, "permissions"):
            for perm in user.permissions:
                if perm.code == permission_code:
                    return True
        else:
            logger.warning(f"警告：未加载用户 {user.id} 权限以进行has_permission检查。")

        # 由于role_permissions表使用代码关联，需要使用权限服务来检查角色权限
        # 这里暂时返回False，实际权限检查应该使用PermissionService
        logger.info(f"用户 {user.id} 没有直接权限 {permission_code}，需要通过PermissionService检查角色权限")
        return False

    def get_user_with_permissions(self, db: Session, user_id: int) -> Optional[User]:
        """
        获取用户及其关联的权限和角色权限 - 优化N+1查询
        由于role_permissions表使用代码关联，不能使用标准的SQLAlchemy关系加载。
        """
        # 一次性获取用户信息、直接权限和角色，避免N+1查询
        user = (
            db.query(User)
            .options(
                selectinload(User.permissions),
                selectinload(User.roles)
            )
            .filter(User.id == user_id)
            .first()
        )

        return user

    def get_user_data_scope(
        self, db: Session, user_id: int, permission_code: str
    ) -> Optional[str]:
        """
        获取用户针对特定权限的数据范围。
        优先顺序：用户直接权限 > 用户角色权限 (取最宽松的)。
        超级管理员的数据范围总是 'all'。
        """
        user = self.get_user_with_permissions(db, user_id)
        if not user:
            logger.warning(f"获取用户数据范围失败：找不到用户 {user_id}")
            return None

        # Super Admins always have 'all' data scope
        if self.is_superuser(user):
            return "all"

        user_scope = None

        # 检查用户直接权限的数据范围
        if hasattr(user, "permissions"):
            for perm in user.permissions:
                if perm.code == permission_code:
                    user_scope = perm.data_scope  # 用户直接权限优先
                    break
        else:
            logger.warning(f"用户 {user.id} 权限未加载以检查数据范围。")

        # 如果用户直接权限定义了范围，则使用该范围
        if user_scope:
            logger.debug(
                f"用户 {user.id} 权限 {permission_code} 的直接数据范围：{user_scope}"
            )
            return user_scope

        # 由于role_permissions表使用代码关联，角色权限检查需要使用原生SQL
        # 这里暂时返回默认范围，实际应该使用PermissionService
        logger.info(f"用户 {user.id} 没有直接权限 {permission_code}，返回默认数据范围")
        return "self"  # 默认返回个人数据范围


# 创建CRUD实例
user = CRUDUser(User)
