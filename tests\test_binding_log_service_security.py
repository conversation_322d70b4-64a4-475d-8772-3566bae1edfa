"""
测试BindingLogService数据隔离安全修复
验证绑卡日志服务的商户级数据隔离机制
"""

import pytest
from sqlalchemy.orm import Session
from app.models.user import User
from app.models.merchant import Merchant
from app.models.department import Department
from app.models.card_record import CardRecord
from app.models.binding_log import BindingLog, LogType, LogLevel
from app.services.binding_log_service import BindingLogService
from app.schemas.binding_log import BindingLogCreate


class TestBindingLogServiceSecurity:
    """测试BindingLogService安全修复"""

    @pytest.fixture
    def setup_test_data(self, db: Session):
        """设置测试数据"""
        # 创建两个商户
        merchant1 = Merchant(
            name="测试商户1",
            code="TEST_MERCHANT_1",
            api_key="test_key_1",
            api_secret="test_secret_1"
        )
        merchant2 = Merchant(
            name="测试商户2", 
            code="TEST_MERCHANT_2",
            api_key="test_key_2",
            api_secret="test_secret_2"
        )
        db.add_all([merchant1, merchant2])
        db.commit()
        db.refresh(merchant1)
        db.refresh(merchant2)

        # 创建部门
        dept1 = Department(
            name="测试部门1",
            merchant_id=merchant1.id,
            code="DEPT_1"
        )
        dept2 = Department(
            name="测试部门2",
            merchant_id=merchant2.id,
            code="DEPT_2"
        )
        db.add_all([dept1, dept2])
        db.commit()
        db.refresh(dept1)
        db.refresh(dept2)

        # 创建用户
        user1 = User(
            username="test_user_1",
            email="<EMAIL>",
            hashed_password="hashed_password",
            merchant_id=merchant1.id,
            department_id=dept1.id,
            is_superuser=False
        )
        user2 = User(
            username="test_user_2",
            email="<EMAIL>", 
            hashed_password="hashed_password",
            merchant_id=merchant2.id,
            department_id=dept2.id,
            is_superuser=False
        )
        superuser = User(
            username="superuser",
            email="<EMAIL>",
            hashed_password="hashed_password",
            is_superuser=True
        )
        db.add_all([user1, user2, superuser])
        db.commit()
        db.refresh(user1)
        db.refresh(user2)
        db.refresh(superuser)

        # 创建卡记录
        card1 = CardRecord(
            id="card_1",
            card_number="1234567890123456",
            merchant_id=merchant1.id,
            department_id=dept1.id,
            status="pending"
        )
        card2 = CardRecord(
            id="card_2",
            card_number="****************",
            merchant_id=merchant2.id,
            department_id=dept2.id,
            status="pending"
        )
        db.add_all([card1, card2])
        db.commit()

        # 创建绑卡日志（使用新的字段）
        log1 = BindingLog(
            card_record_id="card_1",
            merchant_id=merchant1.id,
            department_id=dept1.id,
            log_type=LogType.SYSTEM,
            log_level=LogLevel.INFO,
            message="测试日志1"
        )
        log2 = BindingLog(
            card_record_id="card_2",
            merchant_id=merchant2.id,
            department_id=dept2.id,
            log_type=LogType.SYSTEM,
            log_level=LogLevel.INFO,
            message="测试日志2"
        )
        db.add_all([log1, log2])
        db.commit()

        return {
            "merchant1": merchant1,
            "merchant2": merchant2,
            "dept1": dept1,
            "dept2": dept2,
            "user1": user1,
            "user2": user2,
            "superuser": superuser,
            "card1": card1,
            "card2": card2,
            "log1": log1,
            "log2": log2
        }

    def test_binding_log_service_inheritance(self, db: Session):
        """测试BindingLogService正确继承BaseService"""
        service = BindingLogService(db)
        
        # 验证继承关系
        from app.services.base_service import BaseService
        assert isinstance(service, BaseService)
        
        # 验证apply_data_isolation方法存在
        assert hasattr(service, 'apply_data_isolation')
        assert callable(getattr(service, 'apply_data_isolation'))

    def test_data_isolation_for_regular_users(self, db: Session, setup_test_data):
        """测试普通用户的数据隔离"""
        service = BindingLogService(db)
        user1 = setup_test_data["user1"]
        user2 = setup_test_data["user2"]

        # 用户1应该只能看到自己商户的日志
        query1 = db.query(BindingLog)
        isolated_query1 = service.apply_data_isolation(query1, user1)
        results1 = isolated_query1.all()
        
        assert len(results1) == 1, f"用户1应该只能看到1个日志，实际看到{len(results1)}个"
        assert results1[0].merchant_id == user1.merchant_id, "用户1应该只能看到自己商户的日志"

        # 用户2应该只能看到自己商户的日志
        query2 = db.query(BindingLog)
        isolated_query2 = service.apply_data_isolation(query2, user2)
        results2 = isolated_query2.all()
        
        assert len(results2) == 1, f"用户2应该只能看到1个日志，实际看到{len(results2)}个"
        assert results2[0].merchant_id == user2.merchant_id, "用户2应该只能看到自己商户的日志"

    def test_superuser_access(self, db: Session, setup_test_data):
        """测试超级管理员可以访问所有数据"""
        service = BindingLogService(db)
        superuser = setup_test_data["superuser"]

        query = db.query(BindingLog)
        isolated_query = service.apply_data_isolation(query, superuser)
        results = isolated_query.all()
        
        assert len(results) == 2, f"超级管理员应该能看到所有2个日志，实际看到{len(results)}个"

    def test_user_without_merchant_id(self, db: Session, setup_test_data):
        """测试没有商户ID的用户被拒绝访问"""
        service = BindingLogService(db)
        
        # 创建没有商户ID的用户
        user_no_merchant = User(
            username="no_merchant_user",
            email="<EMAIL>",
            hashed_password="hashed_password",
            is_superuser=False
        )
        db.add(user_no_merchant)
        db.commit()

        query = db.query(BindingLog)
        isolated_query = service.apply_data_isolation(query, user_no_merchant)
        results = isolated_query.all()
        
        assert len(results) == 0, "没有商户ID的用户应该看不到任何日志"

    def test_create_log_with_merchant_isolation(self, db: Session, setup_test_data):
        """测试创建日志时自动添加商户和部门信息"""
        service = BindingLogService(db)
        card1 = setup_test_data["card1"]
        
        # 使用新的安全日志创建方法
        service._create_log_with_isolation(
            db=db,
            card_record_id=card1.id,
            log_type=LogType.SYSTEM,
            log_level=LogLevel.INFO,
            message="测试安全日志创建"
        )
        
        # 验证日志被正确创建并包含商户信息
        new_log = db.query(BindingLog).filter(
            BindingLog.message == "测试安全日志创建"
        ).first()
        
        assert new_log is not None, "日志应该被成功创建"
        assert new_log.merchant_id == card1.merchant_id, "日志应该包含正确的商户ID"
        assert new_log.department_id == card1.department_id, "日志应该包含正确的部门ID"

    def test_get_merchant_and_department_ids(self, db: Session, setup_test_data):
        """测试获取商户和部门ID的辅助方法"""
        service = BindingLogService(db)
        card1 = setup_test_data["card1"]
        
        merchant_id, department_id = service._get_merchant_and_department_ids(card1.id)
        
        assert merchant_id == card1.merchant_id, "应该返回正确的商户ID"
        assert department_id == card1.department_id, "应该返回正确的部门ID"
        
        # 测试不存在的卡记录
        merchant_id, department_id = service._get_merchant_and_department_ids("non_existent_card")
        assert merchant_id is None, "不存在的卡记录应该返回None"
        assert department_id is None, "不存在的卡记录应该返回None"
