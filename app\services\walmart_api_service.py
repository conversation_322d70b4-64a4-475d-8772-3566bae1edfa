import json
import asyncio
from typing import Dict, Any, Optional, <PERSON><PERSON>
from time import time

from sqlalchemy.orm import Session
from app.core.logging import get_logger
from app.core.walmart_api import WalmartAPI
from app.models.card_record import CardRecord
from app.models.walmart_ck import WalmartCK
from app.services.walmart_server import WalmartServerService
from app.services.walmart_ck_service_new import WalmartCKService
from app.services.binding_log_service import BindingLogService
from app.services.retry_logic_service import RetryLogicService

# 创建日志记录器
logger = get_logger("walmart_api_service")

# 定义模块级别的常量，用于API重试
_MAX_API_RETRIES = 50
_RETRY_DELAY_SECONDS = 1


class WalmartAPIService:
    """专门处理沃尔玛API调用的服务"""

    def __init__(self):
        self.retry_service = RetryLogicService()

    def _get_binding_log_service(self, db: Session) -> BindingLogService:
        """【安全修复】获取BindingLogService实例的辅助方法"""
        return BindingLogService(db)

    async def call_walmart_api_with_retry(
        self,
        db: Session,
        record,  # CardRecord instance
        merchant,  # Merchant instance
        unencrypted_card_password: Optional[str],
        extra_params: Optional[Dict[str, Any]] = None,
        debug: bool = False,
    ) -> Dict[str, Any]:
        """
        调用沃尔玛API，带有重试逻辑

        重要：debug模式下仍需要执行完整的CK选择和预占用逻辑，
        只在最后的HTTP API调用环节使用模拟数据
        """
        # 记录绑卡流程开始（debug和生产模式都需要）
        if debug:
            logger.info(
                f"[TEST_MODE] [BIND_FLOW_START] 开始绑卡流程（测试模式） | record_id={record.id} | "
                f"trace_id={record.trace_id} | card_number={record.card_number[:6]}***"
            )
        else:
            logger.info(
                f"[BIND_FLOW_START] 开始绑卡流程 | record_id={record.id} | "
                f"trace_id={record.trace_id} | card_number={record.card_number[:6]}***"
            )

        # 初始化和验证
        init_result = await self._initialize_api_call(db, record)
        if not init_result["success"]:
            # 即使没有可用CK，也要记录绑卡尝试失败
            logger.error(
                f"[BIND_FLOW_NO_CK] 绑卡流程失败：无可用CK | record_id={record.id} | "
                f"trace_id={record.trace_id} | error={init_result['error_result']['error']}"
            )

            # 【修复】记录CK不可用的绑卡尝试，使用更准确的错误描述
            if db:
                try:
                    # 【修复】检查是否有最后尝试的CK ID
                    last_attempted_ck_id = init_result.get("last_attempted_ck_id", 0)

                    # 【修复】确保CK ID不为0时才记录，否则记录为系统错误
                    if last_attempted_ck_id and last_attempted_ck_id > 0:
                        error_message = f"绑卡尝试失败: {init_result['error_result']['error']}"
                        error_source = "walmart_api"  # 如果有具体的CK ID，说明是在CK验证过程中失败的
                    else:
                        error_message = f"系统错误: {init_result['error_result']['error']}"
                        error_source = "system"

                    # 【安全修复】使用辅助方法获取BindingLogService实例
                    binding_log_service = self._get_binding_log_service(db)
                    await binding_log_service.log_bind_attempt_result(
                        db=db,
                        card_record_id=str(record.id),
                        walmart_ck_id=last_attempted_ck_id if last_attempted_ck_id > 0 else 0,
                        attempt_number=1,
                        success=False,
                        error_code="NO_AVAILABLE_CK",
                        error_message=error_message,
                        details={
                            "trace_id": record.trace_id,
                            "merchant_id": record.merchant_id,
                            "failure_reason": "所有CK已失效或被禁用",
                            "error_source": error_source,
                        }
                    )
                except Exception as e:
                    logger.error(f"记录CK不可用日志失败: {str(e)}")

            return init_result["error_result"]

        walmart_ck = init_result["walmart_ck"]

        # 执行重试循环
        return await self._execute_retry_loop(
            db, record, walmart_ck, unencrypted_card_password, debug
        )

    async def _initialize_api_call(
        self, db: Session, record
    ) -> Dict[str, Any]:
        """初始化API调用，验证配置和获取可用用户"""
        # 获取API配置
        api_url = await self._get_api_url(db)
        if not api_url:
            return {
                "success": False,
                "error_result": self._create_error_result(
                    "系统配置错误：沃尔玛API地址不存在",
                    "CONFIG_ERROR_API_URL",
                    record.card_number
                )
            }

        # 获取可用用户
        bind_context = {}  # 【修复】创建绑卡上下文以获取最后尝试的CK ID
        walmart_ck = await self._get_available_user(db, record.card_number, bind_context)
        if not walmart_ck:
            # 改进错误信息，提供更准确的描述
            # 检查具体的无CK原因
            from app.utils.session_utils import safe_execute
            from sqlalchemy import select, func

            # 使用异步兼容的查询方式获取总CK数量
            total_ck_stmt = select(func.count(WalmartCK.id)).filter(
                WalmartCK.merchant_id == record.merchant_id,
                WalmartCK.is_deleted == False
            )
            total_ck_result = await safe_execute(db, total_ck_stmt)
            total_ck_count = total_ck_result.scalar()

            if total_ck_count == 0:
                error_message = "系统配置错误：商户未配置沃尔玛CK"
                error_code = "NO_CK_CONFIGURED"
            else:
                # 使用异步兼容的查询方式获取活跃CK数量
                active_ck_stmt = select(func.count(WalmartCK.id)).filter(
                    WalmartCK.merchant_id == record.merchant_id,
                    WalmartCK.active == True,
                    WalmartCK.is_deleted == False
                )
                active_ck_result = await safe_execute(db, active_ck_stmt)
                active_ck_count = active_ck_result.scalar()

                if active_ck_count == 0:
                    error_message = "系统繁忙：所有CK已失效或被禁用，请联系管理员"
                    error_code = "ALL_CK_DISABLED"
                else:
                    error_message = "系统繁忙：所有CK已达到使用限制，请稍后重试"
                    error_code = "ALL_CK_LIMIT_REACHED"

            # 【修复】从绑卡上下文中获取最后尝试的CK ID
            last_attempted_ck_id = bind_context.get('last_attempted_ck_id', 0)

            # 【修复】创建包含CK ID的错误结果
            error_result = self._create_error_result(
                error_message,
                error_code,
                record.card_number
            )

            # 【修复】将最后尝试的CK ID添加到错误结果中
            if last_attempted_ck_id and last_attempted_ck_id > 0:
                error_result["walmart_ck_id"] = last_attempted_ck_id

            return {
                "success": False,
                "error_result": error_result,
                "last_attempted_ck_id": last_attempted_ck_id  # 【修复】返回最后尝试的CK ID
            }

        return {
            "success": True,
            "walmart_ck": walmart_ck
        }

    async def _execute_retry_loop(
        self, db: Session, record, walmart_ck, unencrypted_card_password: str, debug: bool = False
    ) -> Dict[str, Any]:
        """执行重试循环逻辑"""
        last_error_result: Optional[Dict[str, Any]] = None

        for attempt in range(1, _MAX_API_RETRIES + 1):
            logger.info(
                f"开始调用沃尔玛API - 尝试 {attempt}/{_MAX_API_RETRIES}，"
                f"卡号: {record.card_number}，使用用户: {walmart_ck.id}"
            )

            # 执行单次尝试
            attempt_result = await self._execute_single_attempt(
                db, record, walmart_ck, unencrypted_card_password, attempt, debug
            )

            if attempt_result["success"]:
                return attempt_result["result"]

            last_error_result = attempt_result["error_result"]

            # 检查是否需要继续重试
            if attempt < _MAX_API_RETRIES and attempt_result.get("should_continue", False):
                new_walmart_ck = attempt_result.get("new_walmart_ck")
                if new_walmart_ck:
                    walmart_ck = new_walmart_ck
                    logger.info(f"切换到新的CK用户: {walmart_ck.id}，准备第 {attempt + 1} 次重试")
                    await asyncio.sleep(_RETRY_DELAY_SECONDS)
                else:
                    # 没有新的CK可用，立即停止重试
                    logger.error(f"没有可用的CK用户，停止重试")
                    return self._create_error_result(
                        "系统繁忙：所有CK用户已失效或不可用",
                        "NO_AVAILABLE_USER_AFTER_CK_INVALID",
                        record.card_number
                    )
            else:
                # 达到最大重试次数或不应该继续重试
                break

        # 所有重试均失败
        logger.error(
            f"绑卡操作在 {_MAX_API_RETRIES} 次尝试后最终失败。卡号: {record.card_number}"
        )
        return last_error_result or self._create_error_result(
            "未知错误导致所有重试失败",
            "ALL_RETRIES_FAILED",
            record.card_number
        )

    async def _execute_single_attempt(
        self, db: Session, record, walmart_ck, unencrypted_card_password: str, attempt: int, debug: bool = False
    ) -> Dict[str, Any]:
        """执行单次API调用尝试"""
        # 准备API参数
        api_params = self._prepare_api_request_params(walmart_ck)
        if not api_params:
            error_result = self._handle_sign_error(walmart_ck, record.card_number, attempt)
            return {
                "success": False,
                "error_result": error_result,
                "should_continue": attempt < _MAX_API_RETRIES
            }

        # 执行API调用
        call_outcome = await self._execute_single_api_call(
            api_params, record, walmart_ck.id, attempt, unencrypted_card_password, db, debug
        )

        # 处理解析错误
        if not call_outcome.get("success_parse"):
            error_result = self._handle_parse_error(call_outcome, attempt)
            return {
                "success": False,
                "error_result": error_result,
                "should_continue": attempt < _MAX_API_RETRIES
            }

        # 评估API业务结果
        api_business_result = self._evaluate_api_result(
            db, call_outcome["data"], walmart_ck, attempt
        )

        if api_business_result["success"]:
            # 成功时将当前使用的walmart_ck_id添加到结果中
            api_business_result["walmart_ck_id"] = walmart_ck.id
            return {
                "success": True,
                "result": api_business_result
            }
        else:
            # 检查是否需要重试或禁用用户
            should_continue_retry = await self.retry_service.handle_business_failure(
                db, api_business_result, walmart_ck, record, attempt
            )

            # 如果需要重试且还有重试次数，尝试获取新的CK
            if should_continue_retry and attempt < _MAX_API_RETRIES:
                # 尝试获取新的可用CK
                from app.services.walmart_ck_service_new import WalmartCKService
                walmart_ck_service = WalmartCKService(db)
                new_walmart_ck = await walmart_ck_service.get_available_ck(
                    merchant_id=record.merchant_id,
                    department_id=record.department_id,
                    exclude_ids=[walmart_ck.id]  # 排除当前失效的CK
                )

                if new_walmart_ck:
                    logger.info(f"CK失效后获取到新的可用CK: {new_walmart_ck.id}")
                    return {
                        "success": False,
                        "error_result": None,
                        "new_walmart_ck": new_walmart_ck,
                        "should_continue": True
                    }
                else:
                    logger.error(f"CK失效后无法获取新的可用CK，停止重试")
                    return {
                        "success": False,
                        "error_result": api_business_result,
                        "should_continue": False
                    }
            else:
                return {
                    "success": False,
                    "error_result": api_business_result,
                    "should_continue": should_continue_retry
                }

    async def _get_api_url(self, db: Session) -> Optional[str]:
        """获取API地址"""
        walmart_server_config_service = WalmartServerService(db)
        api_url = await walmart_server_config_service.get_api_url()
        if not api_url:
            logger.error("沃尔玛API地址不存在")
        return api_url

    async def _get_available_user(self, db: Session, card_number: str, bind_context: Optional[Dict[str, Any]] = None) -> Optional[WalmartCK]:
        """获取可用用户"""
        # 从记录中获取商户ID和部门ID
        from app.utils.session_utils import safe_execute
        from sqlalchemy import select

        # 使用异步兼容的查询方式
        stmt = select(CardRecord).filter(CardRecord.card_number == card_number)
        result = await safe_execute(db, stmt)
        record = result.scalar_one_or_none()

        if not record:
            logger.error(f"找不到卡号 {card_number} 对应的记录")
            return None

        # 准备绑卡上下文信息，传递给CK服务用于日志记录
        if bind_context is None:
            bind_context = {}

        # 更新绑卡上下文信息
        bind_context.update({
            "record_id": record.id,
            "trace_id": record.trace_id,
            "card_number": record.card_number,
            "merchant_id": record.merchant_id,
        })

        walmart_ck_service = WalmartCKService(db)
        walmart_ck = await walmart_ck_service.get_available_ck_with_context(
            merchant_id=record.merchant_id,
            department_id=record.department_id,
            bind_context=bind_context
        )
        if not walmart_ck:
            # 改进错误日志，区分不同的无CK情况
            # 检查是否有CK配置但都不可用
            from app.utils.session_utils import safe_execute
            from sqlalchemy import select, func

            # 使用异步兼容的查询方式获取总CK数量
            total_ck_stmt = select(func.count(WalmartCK.id)).filter(
                WalmartCK.merchant_id == record.merchant_id,
                WalmartCK.is_deleted == False
            )
            total_ck_result = await safe_execute(db, total_ck_stmt)
            total_ck_count = total_ck_result.scalar()

            if total_ck_count == 0:
                logger.error(f"商户 {record.merchant_id} 没有配置任何沃尔玛CK")
            else:
                # 使用异步兼容的查询方式获取活跃CK数量
                active_ck_stmt = select(func.count(WalmartCK.id)).filter(
                    WalmartCK.merchant_id == record.merchant_id,
                    WalmartCK.active == True,
                    WalmartCK.is_deleted == False
                )
                active_ck_result = await safe_execute(db, active_ck_stmt)
                active_ck_count = active_ck_result.scalar()

                if active_ck_count == 0:
                    logger.error(f"商户 {record.merchant_id} 的所有CK都已被禁用或失效")
                else:
                    logger.error(f"商户 {record.merchant_id} 的所有可用CK都已达到使用限制")

            # 【修复】记录最后尝试的CK ID到bind_context中，供后续使用
            logger.info(f"绑卡上下文中的最后尝试CK ID: {bind_context.get('last_attempted_ck_id', '无')}")

        return walmart_ck

    def _prepare_api_request_params(self, walmart_ck: WalmartCK) -> Optional[Dict[str, Any]]:
        """准备沃尔玛API请求所需的签名和头部信息"""
        user_sign_raw = walmart_ck.sign
        sign_parts = user_sign_raw.split("#")
        if len(sign_parts) < 3:
            logger.error(f"沃尔玛CK {walmart_ck.id} 签名格式错误: {user_sign_raw}")
            return None

        return {
            "sign": sign_parts[0],
            "encryption_key": sign_parts[1],
            "version": sign_parts[2],
        }

    async def _execute_single_api_call(
        self,
        api_params: Dict[str, Any],
        record,
        walmart_ck_id: int,
        attempt: int,
        unencrypted_card_password: str,
        db: Session = None,
        debug: bool = False,
    ) -> Dict[str, Any]:
        """执行单次沃尔玛API调用并初步处理响应"""
        try:
            # 记录绑卡尝试开始日志
            if db:
                await self._log_bind_attempt_start(db, record, walmart_ck_id, attempt)
                await self._log_api_request(db, record, walmart_ck_id, attempt)

            # 创建API实例并调用
            current_walmart_api = await WalmartAPI.create_with_config(
                encryption_key=api_params["encryption_key"],
                version=api_params["version"],
                sign=api_params["sign"],
                db=db
            )

            request_data = {
                "cardNo": record.card_number,
                "cardPwd": unencrypted_card_password,  # 使用传入的未加密密码
            }

            api_call_start_time = time()
            # 使用异步方法调用绑卡API
            response = await current_walmart_api.bind_card(
                card_no=request_data["cardNo"],
                card_pwd=request_data["cardPwd"],
                db=db,
                debug=debug
            )
            api_call_duration = time() - api_call_start_time

            logger.info(
                f"沃尔玛API请求耗时: {api_call_duration:.2f}s (用户 ID: {walmart_ck_id}, 尝试 {attempt})"
            )

            response_data = response.json()
            need_retry_with_new_user = getattr(response, "need_retry_with_new_user", False)

            # 记录API响应日志
            if db:
                await self._log_api_response(
                    db, record, response_data, api_call_duration, walmart_ck_id, attempt
                )

                # 记录绑卡尝试结果日志
                await self._log_bind_attempt_result(
                    db, record, walmart_ck_id, attempt, response_data, api_call_duration
                )

            # 【修复】确保返回结果包含CK信息，便于后续保存到card_records表
            # 从数据库获取CK的部门信息
            walmart_ck = None
            if db:
                from app.utils.session_utils import safe_execute
                from sqlalchemy import select

                # 使用异步兼容的查询方式
                ck_stmt = select(WalmartCK).filter(WalmartCK.id == walmart_ck_id)
                ck_result = await safe_execute(db, ck_stmt)
                walmart_ck = ck_result.scalar_one_or_none()

            result = {
                "success_parse": True,
                "data": response_data,
                "raw_response": response_data,
                "need_retry_with_new_user": need_retry_with_new_user,
                # 【关键修复】确保返回结果包含CK信息，无论成功失败都能追踪
                "walmart_ck_id": walmart_ck_id,
                "department_id": walmart_ck.department_id if walmart_ck else None,
            }

            logger.info(
                f"[API_CALL_RESULT] API调用完成 | record_id={record.id} | "
                f"walmart_ck_id={walmart_ck_id} | department_id={result['department_id']} | "
                f"attempt={attempt} | duration={api_call_duration:.2f}s"
            )

            return result

        except json.JSONDecodeError as je:
            error_result = self._handle_json_decode_error(je, attempt, walmart_ck_id, record.card_number)
            # 【修复】确保错误结果也包含CK信息
            error_result["walmart_ck_id"] = walmart_ck_id
            if db:
                from app.utils.session_utils import safe_execute
                from sqlalchemy import select

                # 使用异步兼容的查询方式
                ck_stmt = select(WalmartCK).filter(WalmartCK.id == walmart_ck_id)
                ck_result = await safe_execute(db, ck_stmt)
                walmart_ck = ck_result.scalar_one_or_none()
                error_result["department_id"] = walmart_ck.department_id if walmart_ck else None
            return error_result
        except Exception as e:
            error_result = self._handle_request_exception(e, attempt, walmart_ck_id, record.card_number)
            # 【修复】确保错误结果也包含CK信息
            error_result["walmart_ck_id"] = walmart_ck_id
            if db:
                from app.utils.session_utils import safe_execute
                from sqlalchemy import select

                # 使用异步兼容的查询方式
                ck_stmt = select(WalmartCK).filter(WalmartCK.id == walmart_ck_id)
                ck_result = await safe_execute(db, ck_stmt)
                walmart_ck = ck_result.scalar_one_or_none()
                error_result["department_id"] = walmart_ck.department_id if walmart_ck else None
            return error_result

    async def _log_api_request(self, db: Session, record, walmart_ck_id: int, attempt: int):
        """记录API请求日志"""
        try:
            request_data = {
                "cardNo": record.card_number,
                "cardPwd": "***",  # 不记录真实密码
            }
            # 【安全修复】使用辅助方法获取BindingLogService实例
            binding_log_service = self._get_binding_log_service(db)
            await binding_log_service.log_walmart_request(
                db=db,
                card_record_id=str(record.id),
                request_data=request_data,
                walmart_ck_id=str(walmart_ck_id),
                attempt_number=str(attempt),
                message=f"沃尔玛API请求 (尝试 {attempt})",
            )
        except Exception as e:
            logger.error(f"记录API请求日志失败: {str(e)}")

    async def _log_api_response(
        self, db: Session, record, response_data: Dict[str, Any],
        duration: float, walmart_ck_id: int, attempt: int
    ):
        """记录API响应日志"""
        try:
            # 【安全修复】使用辅助方法获取BindingLogService实例
            binding_log_service = self._get_binding_log_service(db)
            await binding_log_service.log_walmart_response(
                db=db,
                card_record_id=str(record.id),
                response_data=response_data,
                duration_ms=duration * 1000,
                walmart_ck_id=str(walmart_ck_id),
                attempt_number=str(attempt),
                message=f"沃尔玛API响应 (尝试 {attempt})",
            )
        except Exception as e:
            logger.error(f"记录API响应日志失败: {str(e)}")

    async def _log_bind_attempt_start(
        self, db: Session, record, walmart_ck_id: int, attempt: int
    ):
        """记录绑卡尝试开始日志"""
        try:
            logger.info(
                f"[BIND_ATTEMPT_START] 开始绑卡尝试 #{attempt} | record_id={record.id} | "
                f"trace_id={record.trace_id} | walmart_ck_id={walmart_ck_id} | "
                f"card_number={record.card_number[:6]}***"
            )

            # 【安全修复】使用辅助方法获取BindingLogService实例
            binding_log_service = self._get_binding_log_service(db)
            await binding_log_service.log_bind_attempt_start(
                db=db,
                card_record_id=str(record.id),
                walmart_ck_id=walmart_ck_id,
                attempt_number=attempt,
                card_number=record.card_number,
                details={
                    "trace_id": record.trace_id,
                    "merchant_id": record.merchant_id,
                }
            )
        except Exception as e:
            logger.error(f"记录绑卡尝试开始日志失败: {str(e)}")

    async def _log_bind_attempt_result(
        self, db: Session, record, walmart_ck_id: int, attempt: int,
        response_data: Dict[str, Any], duration: float
    ):
        """记录绑卡尝试结果日志"""
        try:
            success = response_data.get("status", False)
            error_info = response_data.get("error", {}) if not success else {}
            error_code = error_info.get("errorcode") if error_info else None
            error_message = error_info.get("message") if error_info else None
            log_id = response_data.get("logId", "")

            if success:
                logger.info(
                    f"[BIND_ATTEMPT_SUCCESS] 绑卡尝试 #{attempt} 成功 | record_id={record.id} | "
                    f"trace_id={record.trace_id} | walmart_ck_id={walmart_ck_id} | "
                    f"card_number={record.card_number[:6]}*** | duration={duration:.2f}s"
                )
            else:
                logger.error(
                    f"[BIND_ATTEMPT_FAILED] 绑卡尝试 #{attempt} 失败 | record_id={record.id} | "
                    f"trace_id={record.trace_id} | walmart_ck_id={walmart_ck_id} | "
                    f"card_number={record.card_number[:6]}*** | error_code={error_code} | "
                    f"error_message={error_message} | logId={log_id} | duration={duration:.2f}s"
                )

            # 【安全修复】使用辅助方法获取BindingLogService实例
            binding_log_service = self._get_binding_log_service(db)
            await binding_log_service.log_bind_attempt_result(
                db=db,
                card_record_id=str(record.id),
                walmart_ck_id=walmart_ck_id,
                attempt_number=attempt,
                success=success,
                error_code=str(error_code) if error_code else None,
                error_message=error_message,
                duration_ms=duration * 1000,
                details={
                    "trace_id": record.trace_id,
                    "merchant_id": record.merchant_id,
                    "log_id": log_id,
                    "response_status": response_data.get("status"),
                    "error_source": "walmart_api",  # 【关键修复】明确标识这是沃尔玛API的响应
                    "raw_response": response_data,  # 【修复】保存原始响应数据
                }
            )
        except Exception as e:
            logger.error(f"记录绑卡尝试结果日志失败: {str(e)}")

    def _handle_json_decode_error(
        self, je: json.JSONDecodeError, attempt: int, walmart_ck_id: int, card_number: str
    ) -> Dict[str, Any]:
        """处理JSON解析错误"""
        logger.exception(
            f"沃尔玛API响应JSON解析错误 (尝试 {attempt}, 用户ID: {walmart_ck_id}): {str(je)}"
        )
        return {
            "success_parse": False,
            "error": f"API响应JSON解析错误: {str(je)}",
            "data": {
                "errorCode": "RESPONSE_PARSE_ERROR",
                "cardNumber": card_number,
            },
            "need_retry_with_new_user": False,
        }

    def _handle_request_exception(
        self, e: Exception, attempt: int, walmart_ck_id: int, card_number: str
    ) -> Dict[str, Any]:
        """处理请求异常"""
        logger.exception(
            f"沃尔玛API请求时发生预料之外的异常 (尝试 {attempt}, 用户ID: {walmart_ck_id}): {str(e)}"
        )
        return {
            "success_parse": False,
            "error": f"API请求异常: {str(e)}",
            "data": {
                "errorCode": "REQUEST_EXCEPTION",
                "cardNumber": card_number,
            },
            "need_retry_with_new_user": False,
        }

    def _handle_sign_error(
        self, walmart_ck: WalmartCK, card_number: str, attempt: int
    ) -> Dict[str, Any]:
        """处理签名错误"""
        error_msg = f"用户 {walmart_ck.id} 签名格式错误"
        if attempt < _MAX_API_RETRIES:
            logger.warning(f"{error_msg}，尝试下一个可用用户。")
        else:
            logger.error(f"所有尝试均失败，最后一次因{error_msg}。")

        return {
            "success": False,
            "error": error_msg,
            "data": {
                "errorCode": "CONFIG_ERROR_USER_SIGN",
                "cardNumber": card_number,
            },
        }

    def _handle_parse_error(self, call_outcome: Dict[str, Any], attempt: int) -> Dict[str, Any]:
        """处理解析错误"""
        if attempt < _MAX_API_RETRIES:
            logger.info(f"API请求/解析失败，准备重试 (下一次尝试 {attempt + 1})")
        else:
            logger.error(f"所有 {_MAX_API_RETRIES} 次API请求/解析尝试均失败。")

        return {
            "success": False,
            "error": call_outcome["error"],
            "data": call_outcome["data"],
            "raw_response_text": call_outcome.get("raw_response_text"),
        }

    def _evaluate_api_result(
        self, db: Session, api_response_data: Dict[str, Any],
        walmart_ck: WalmartCK, attempt: int
    ) -> Dict[str, Any]:
        """评估API业务结果，处理用户限制，并返回格式化的结果字典"""
        if (
            api_response_data.get("status") is True
            or api_response_data.get("code") == 0
        ):
            logger.info(
                f"沃尔玛API调用成功 (尝试 {attempt}, 用户ID: {walmart_ck.id})"
            )
            return {
                "success": True,
                "error": None,
                "data": api_response_data.get("data", {}),
                "raw_response": api_response_data,
                "walmart_ck_id": walmart_ck.id,  # 确保CK ID被传递
                "department_id": walmart_ck.department_id,  # 确保部门ID被传递
            }
        else:
            # 提取完整的错误信息
            error_info = api_response_data.get("error", {})
            error_msg = (
                api_response_data.get("message")
                or api_response_data.get("msg")
                or error_info.get("message")
                or "未知沃尔玛API返回错误"
            )
            error_code = error_info.get("errorcode", "UNKNOWN")
            log_id = api_response_data.get("logId", "")

            # 记录完整的错误信息，区分不同类型的错误
            if error_code == 203 and "请先去登录" in error_msg:
                logger.error(
                    f"沃尔玛API业务失败 - CK无效需要重新登录 (尝试 {attempt}, 用户ID: {walmart_ck.id}): "
                    f"errorcode={error_code}, message={error_msg}, logId={log_id}"
                )
            else:
                logger.error(
                    f"沃尔玛API业务失败 (尝试 {attempt}, 用户ID: {walmart_ck.id}): "
                    f"errorcode={error_code}, message={error_msg}, logId={log_id}"
                )

            # 【修复】处理不同类型的限制情况
            if error_code == 110224 and "您绑卡已超过单日20张限制" in error_msg:
                # 单日绑卡限制 - 不修改bind_count，只禁用CK（明天会重新启用）
                logger.warning(f"用户 {walmart_ck.id} 达到单日绑卡限制，禁用CK等待明天重新启用")
                walmart_ck.active = False
                db.add(walmart_ck)
                db.commit()
            elif error_code == 110134 and "错误次数过多" in error_msg:
                # 错误次数过多 - 禁用CK
                logger.warning(f"用户 {walmart_ck.id} 错误次数过多，禁用CK")
                walmart_ck.active = False
                db.add(walmart_ck)
                db.commit()
            elif error_code == 110444 and "数据异常" in error_msg:
                # 数据异常 - 禁用CK
                logger.warning(f"用户 {walmart_ck.id} 数据异常，禁用CK")
                walmart_ck.active = False
                db.add(walmart_ck)
                db.commit()
            elif error_code == 200:
                # 【新增】错误码200 - 禁用CK（可能有多种错误信息）
                logger.warning(f"用户 {walmart_ck.id} 遇到错误码200，禁用CK: {error_msg}")
                walmart_ck.active = False
                db.add(walmart_ck)
                db.commit()
            elif "limit" in error_msg.lower() or "达到上限" in error_msg:
                # 其他类型的限制 - 设置为总限制并禁用
                logger.info(f"用户 {walmart_ck.id} 达到绑卡上限，标记绑卡数量。")
                walmart_ck.bind_count = walmart_ck.total_limit
                walmart_ck.active = False
                db.add(walmart_ck)
                db.commit()

            return {
                "success": False,
                "error": error_msg,
                "error_code": error_code,
                "log_id": log_id,
                "data": api_response_data.get("data"),
                "raw_response": api_response_data,
                "walmart_ck_id": walmart_ck.id,  # 即使失败也要传递CK ID
                "department_id": walmart_ck.department_id,  # 即使失败也要传递部门ID
            }

    def _create_error_result(self, error: str, error_code: str, card_number: str,
                           log_id: str = "", raw_response: Dict[str, Any] = None) -> Dict[str, Any]:
        """创建错误结果"""
        result = {
            "success": False,
            "error": error,
            "error_code": error_code,
            "data": {
                "errorCode": error_code,
                "cardNumber": card_number,
            },
        }

        if log_id:
            result["log_id"] = log_id
            result["data"]["logId"] = log_id

        if raw_response:
            result["raw_response"] = raw_response

        return result


