"""
CK计数一致性测试
验证修复后的CK选择机制不会出现"绑卡失败但计数增加"的问题
"""

import pytest
import asyncio
from typing import Dict, Any
from unittest.mock import patch, MagicMock
from sqlalchemy.orm import Session

from app.models.walmart_ck import WalmartCK
from app.models.department import Department
from app.models.merchant import Merchant
from app.models.card_record import CardRecord, CardStatus
from app.services.simplified_ck_service import SimplifiedCKService, AtomicCKUpdateService
from app.services.atomic_binding_service import AtomicBindingService


class TestCKCountConsistency:
    """CK计数一致性测试类"""
    
    @pytest.fixture(autouse=True)
    def setup_test_data(self, db: Session):
        """设置测试数据"""
        # 创建测试商户
        merchant = Merchant(
            name="测试商户",
            code="TEST_MERCHANT",
            status=True
        )
        db.add(merchant)
        db.flush()
        
        # 创建测试部门
        department = Department(
            merchant_id=merchant.id,
            name="测试部门",
            code="TEST_DEPT",
            status=True,
            enable_binding=True,
            binding_weight=100
        )
        db.add(department)
        db.flush()
        
        # 创建测试CK
        ck = WalmartCK(
            merchant_id=merchant.id,
            department_id=department.id,
            sign="test_ck@token#sign#version",
            total_limit=10,
            bind_count=0,
            active=True,
            is_deleted=False
        )
        db.add(ck)
        db.commit()
        
        self.merchant = merchant
        self.department = department
        self.ck = ck
    
    @pytest.mark.asyncio
    async def test_ck_selection_no_premature_count_increase(self, db: Session):
        """测试CK选择时不会过早增加计数"""
        print("\n=== 测试CK选择不过早增加计数 ===")
        
        service = AtomicCKUpdateService(db)
        
        # 记录初始计数
        initial_count = self.ck.bind_count
        print(f"初始bind_count: {initial_count}")
        
        # 选择CK（不应该增加计数）
        selected_ck = await service.atomic_ck_selection_and_reserve(
            merchant_id=self.merchant.id
        )
        
        # 验证CK被选中
        assert selected_ck is not None
        assert selected_ck.id == self.ck.id
        
        # 刷新数据库状态
        db.refresh(self.ck)
        
        # 验证计数没有增加
        assert self.ck.bind_count == initial_count, f"CK选择时计数不应增加: {self.ck.bind_count} != {initial_count}"
        print(f"✅ CK选择后bind_count保持不变: {self.ck.bind_count}")
    
    @pytest.mark.asyncio
    async def test_binding_success_increases_count(self, db: Session):
        """测试绑卡成功时正确增加计数"""
        print("\n=== 测试绑卡成功增加计数 ===")
        
        service = AtomicCKUpdateService(db)
        
        # 选择CK
        selected_ck = await service.atomic_ck_selection_and_reserve(
            merchant_id=self.merchant.id
        )
        
        initial_count = self.ck.bind_count
        print(f"绑卡前bind_count: {initial_count}")
        
        # 模拟绑卡成功
        await service.commit_ck_usage(selected_ck.id, success=True)
        
        # 刷新数据库状态
        db.refresh(self.ck)
        
        # 验证计数正确增加
        expected_count = initial_count + 1
        assert self.ck.bind_count == expected_count, f"绑卡成功后计数应增加: {self.ck.bind_count} != {expected_count}"
        print(f"✅ 绑卡成功后bind_count正确增加: {initial_count} -> {self.ck.bind_count}")
    
    @pytest.mark.asyncio
    async def test_binding_failure_no_count_increase(self, db: Session):
        """测试绑卡失败时计数不增加"""
        print("\n=== 测试绑卡失败不增加计数 ===")
        
        service = AtomicCKUpdateService(db)
        
        # 选择CK
        selected_ck = await service.atomic_ck_selection_and_reserve(
            merchant_id=self.merchant.id
        )
        
        initial_count = self.ck.bind_count
        print(f"绑卡前bind_count: {initial_count}")
        
        # 模拟绑卡失败
        await service.commit_ck_usage(selected_ck.id, success=False)
        
        # 刷新数据库状态
        db.refresh(self.ck)
        
        # 验证计数没有增加
        assert self.ck.bind_count == initial_count, f"绑卡失败后计数不应增加: {self.ck.bind_count} != {initial_count}"
        print(f"✅ 绑卡失败后bind_count保持不变: {self.ck.bind_count}")
    
    @pytest.mark.asyncio
    async def test_multiple_binding_failures_consistency(self, db: Session):
        """测试多次绑卡失败的计数一致性"""
        print("\n=== 测试多次绑卡失败的计数一致性 ===")
        
        service = AtomicCKUpdateService(db)
        initial_count = self.ck.bind_count
        
        # 模拟多次绑卡失败
        for i in range(5):
            # 选择CK
            selected_ck = await service.atomic_ck_selection_and_reserve(
                merchant_id=self.merchant.id
            )
            
            # 模拟绑卡失败
            await service.commit_ck_usage(selected_ck.id, success=False)
            
            # 验证计数没有变化
            db.refresh(self.ck)
            assert self.ck.bind_count == initial_count, f"第{i+1}次失败后计数异常: {self.ck.bind_count}"
        
        print(f"✅ 5次绑卡失败后bind_count保持不变: {self.ck.bind_count}")
    
    @pytest.mark.asyncio
    async def test_mixed_success_failure_scenarios(self, db: Session):
        """测试成功和失败混合场景的计数准确性"""
        print("\n=== 测试混合成功失败场景 ===")
        
        service = AtomicCKUpdateService(db)
        initial_count = self.ck.bind_count
        
        # 场景：成功-失败-成功-失败-成功
        scenarios = [True, False, True, False, True]
        expected_successes = sum(scenarios)  # 3次成功
        
        for i, should_succeed in enumerate(scenarios):
            # 选择CK
            selected_ck = await service.atomic_ck_selection_and_reserve(
                merchant_id=self.merchant.id
            )
            
            # 提交结果
            await service.commit_ck_usage(selected_ck.id, success=should_succeed)
            
            print(f"第{i+1}次绑卡: {'成功' if should_succeed else '失败'}")
        
        # 验证最终计数
        db.refresh(self.ck)
        expected_final_count = initial_count + expected_successes
        
        assert self.ck.bind_count == expected_final_count, (
            f"混合场景后计数错误: {self.ck.bind_count} != {expected_final_count}"
        )
        
        print(f"✅ 混合场景后bind_count正确: {initial_count} + {expected_successes} = {self.ck.bind_count}")
    
    @pytest.mark.asyncio
    async def test_atomic_binding_service_consistency(self, db: Session):
        """测试AtomicBindingService的计数一致性"""
        print("\n=== 测试AtomicBindingService计数一致性 ===")
        
        # 创建测试记录
        record = CardRecord(
            merchant_id=self.merchant.id,
            department_id=self.department.id,
            card_number="1234567890",
            status=CardStatus.PENDING,
            request_id="test_request_123",
            request_data={"test": "data"}
        )
        db.add(record)
        db.commit()
        
        atomic_service = AtomicBindingService(db)
        initial_count = self.ck.bind_count
        
        # 测试绑卡成功场景
        api_result = {
            'walmart_ck_id': self.ck.id,
            'success': True,
            'timestamp': '2024-01-01T00:00:00'
        }
        
        success = await atomic_service.execute_atomic_binding(
            record_id=str(record.id),
            merchant_id=self.merchant.id,
            api_result=api_result,
            is_success=True
        )
        
        assert success, "原子性绑卡操作应该成功"
        
        # 验证计数增加
        db.refresh(self.ck)
        expected_count = initial_count + 1
        assert self.ck.bind_count == expected_count, (
            f"AtomicBindingService绑卡成功后计数错误: {self.ck.bind_count} != {expected_count}"
        )
        
        print(f"✅ AtomicBindingService绑卡成功后bind_count正确: {initial_count} -> {self.ck.bind_count}")
    
    @pytest.mark.asyncio
    async def test_concurrent_binding_count_consistency(self, db: Session):
        """测试并发绑卡的计数一致性"""
        print("\n=== 测试并发绑卡计数一致性 ===")
        
        service = AtomicCKUpdateService(db)
        initial_count = self.ck.bind_count
        
        async def single_binding_operation(task_id: int, should_succeed: bool):
            """单个绑卡操作"""
            try:
                # 选择CK
                selected_ck = await service.atomic_ck_selection_and_reserve(
                    merchant_id=self.merchant.id
                )
                
                if selected_ck:
                    # 模拟绑卡处理时间
                    await asyncio.sleep(0.01)
                    
                    # 提交结果
                    await service.commit_ck_usage(selected_ck.id, success=should_succeed)
                    return should_succeed
                
                return False
            except Exception as e:
                print(f"任务{task_id}异常: {e}")
                return False
        
        # 启动10个并发任务：5个成功，5个失败
        tasks = []
        for i in range(10):
            should_succeed = i < 5  # 前5个成功，后5个失败
            task = single_binding_operation(i, should_succeed)
            tasks.append(task)
        
        # 等待所有任务完成
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # 统计成功次数
        successful_operations = sum(1 for result in results if result is True)
        
        # 验证最终计数
        db.refresh(self.ck)
        expected_final_count = initial_count + successful_operations
        
        assert self.ck.bind_count == expected_final_count, (
            f"并发绑卡后计数错误: {self.ck.bind_count} != {expected_final_count} "
            f"(成功操作: {successful_operations})"
        )
        
        print(f"✅ 并发绑卡后bind_count正确: {initial_count} + {successful_operations} = {self.ck.bind_count}")


if __name__ == "__main__":
    pytest.main([__file__, "-v", "-s"])
