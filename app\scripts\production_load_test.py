#!/usr/bin/env python3
"""
生产环境高并发负载均衡压力测试
模拟1秒内100+并发绑卡请求的场景
"""

import asyncio
import sys
import os
import time
import statistics
from collections import Counter, defaultdict
from typing import Dict, List, Tuple
import concurrent.futures

# 添加项目路径
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.insert(0, project_root)

from app.api.deps import get_db
from app.models.walmart_ck import WalmartCK
from app.services.redis_ck_wrapper import create_optimized_ck_service
from app.services.production_ck_service import create_production_ck_service
from app.core.logging import get_logger
from app.core.redis import get_redis

logger = get_logger("production_load_test")


class ProductionLoadTester:
    """生产环境负载测试器"""
    
    def __init__(self):
        self.db = next(get_db())
        self.redis_client = None
        self.results = []
        
    async def __aenter__(self):
        self.redis_client = await get_redis()
        return self
        
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self.db:
            self.db.close()
        if self.redis_client:
            await self.redis_client.aclose()
    
    async def run_high_concurrency_test(
        self, 
        merchant_id: int = 2, 
        concurrent_requests: int = 100,
        test_duration: int = 5
    ):
        """运行高并发测试"""
        print(f"🚀 生产环境高并发负载均衡测试")
        print(f"商户ID: {merchant_id}")
        print(f"并发请求数: {concurrent_requests}")
        print(f"测试持续时间: {test_duration}秒")
        print("="*80)
        
        # 检查可用CK
        available_cks = self.db.query(WalmartCK).filter(
            WalmartCK.merchant_id == merchant_id,
            WalmartCK.active == True,
            WalmartCK.is_deleted == False
        ).all()
        
        print(f"📊 可用CK数量: {len(available_cks)}")
        for ck in available_cks:
            print(f"  CK {ck.id}: bind_count={ck.bind_count}, total_limit={ck.total_limit}")
        
        if len(available_cks) < 2:
            print("❌ 需要至少2个可用CK才能测试负载均衡")
            return
        
        # 清理测试环境
        await self._cleanup_test_environment(merchant_id)
        
        # 执行多轮并发测试
        all_results = []
        
        for round_num in range(test_duration):
            print(f"\n🎯 第 {round_num + 1} 轮并发测试 ({concurrent_requests} 个并发请求)")
            
            round_results = await self._execute_concurrent_requests(
                merchant_id, concurrent_requests
            )
            
            all_results.extend(round_results)
            
            # 分析本轮结果
            self._analyze_round_results(round_results, round_num + 1)
            
            # 短暂休息
            await asyncio.sleep(0.5)
        
        # 综合分析
        print(f"\n" + "="*80)
        print("📈 综合测试结果分析")
        print("="*80)
        
        await self._analyze_comprehensive_results(all_results, available_cks)
        
        return all_results

    async def run_comparison_test(
        self,
        merchant_id: int = 2,
        concurrent_requests: int = 20,
        test_rounds: int = 2
    ):
        """运行对比测试：原有服务 vs 生产级服务"""
        print(f"🔄 生产级服务对比测试")
        print(f"商户ID: {merchant_id}")
        print(f"并发请求数: {concurrent_requests}")
        print(f"测试轮数: {test_rounds}")
        print("="*80)

        # 检查可用CK
        available_cks = self.db.query(WalmartCK).filter(
            WalmartCK.merchant_id == merchant_id,
            WalmartCK.active == True,
            WalmartCK.is_deleted == False
        ).all()

        print(f"📊 可用CK数量: {len(available_cks)}")

        if len(available_cks) < 2:
            print("❌ 需要至少2个可用CK才能测试负载均衡")
            return

        # 清理测试环境
        await self._cleanup_test_environment(merchant_id)

        results = {
            'original_service': [],
            'production_service': []
        }

        # 测试原有服务
        print(f"\n🎯 测试原有服务")
        self.use_production_service = False
        for round_num in range(test_rounds):
            print(f"  第 {round_num + 1} 轮...")
            round_results = await self._execute_concurrent_requests(
                merchant_id, concurrent_requests
            )
            results['original_service'].extend(round_results)
            await asyncio.sleep(1)  # 短暂休息

        # 重新激活CK（如果被禁用）
        await self._reactivate_cks(merchant_id)
        await asyncio.sleep(2)

        # 测试生产级服务
        print(f"\n🎯 测试生产级服务")
        self.use_production_service = True
        for round_num in range(test_rounds):
            print(f"  第 {round_num + 1} 轮...")
            round_results = await self._execute_concurrent_requests(
                merchant_id, concurrent_requests
            )
            results['production_service'].extend(round_results)
            await asyncio.sleep(1)  # 短暂休息

        # 对比分析
        await self._compare_results(results, available_cks)

        return results
    
    async def _execute_concurrent_requests(
        self, 
        merchant_id: int, 
        concurrent_count: int
    ) -> List[Dict]:
        """执行并发请求"""
        
        async def single_request(request_id: int, use_production_service: bool = False) -> Dict:
            """单个请求"""
            start_time = time.time()

            try:
                if use_production_service:
                    # 使用生产级服务
                    ck_service = await create_production_ck_service(self.db)
                    try:
                        ck = await ck_service.get_available_ck(merchant_id, f"req_{request_id}")
                        selection_time = time.time() - start_time

                        result = {
                            'request_id': request_id,
                            'ck_id': ck.id if ck else None,
                            'success': ck is not None,
                            'selection_time': selection_time,
                            'timestamp': time.time(),
                            'service_type': 'production'
                        }

                        # 记录使用结果
                        if ck:
                            await ck_service.record_ck_usage(ck_id=ck.id, success=False)
                    finally:
                        await ck_service.__aexit__(None, None, None)
                else:
                    # 使用原有服务
                    ck_service = create_optimized_ck_service(self.db)
                    ck = await ck_service.get_available_ck(merchant_id)
                    selection_time = time.time() - start_time

                    result = {
                        'request_id': request_id,
                        'ck_id': ck.id if ck else None,
                        'success': ck is not None,
                        'selection_time': selection_time,
                        'timestamp': time.time(),
                        'service_type': 'original'
                    }

                    # 立即释放CK
                    if ck:
                        await ck_service.record_ck_usage(ck_id=ck.id, success=False)

                return result

            except Exception as e:
                return {
                    'request_id': request_id,
                    'ck_id': None,
                    'success': False,
                    'selection_time': time.time() - start_time,
                    'error': str(e),
                    'timestamp': time.time(),
                    'service_type': 'production' if use_production_service else 'original'
                }
        
        # 创建并发任务（默认使用原有服务）
        use_production = getattr(self, 'use_production_service', False)
        tasks = [single_request(i, use_production) for i in range(concurrent_count)]
        
        # 执行并发请求
        start_time = time.time()
        results = await asyncio.gather(*tasks, return_exceptions=True)
        total_time = time.time() - start_time
        
        # 处理异常结果
        processed_results = []
        for result in results:
            if isinstance(result, Exception):
                processed_results.append({
                    'request_id': -1,
                    'ck_id': None,
                    'success': False,
                    'selection_time': 0,
                    'error': str(result),
                    'timestamp': time.time()
                })
            else:
                processed_results.append(result)
        
        print(f"  总耗时: {total_time:.3f}秒")
        print(f"  平均QPS: {concurrent_count/total_time:.1f}")
        
        return processed_results
    
    def _analyze_round_results(self, results: List[Dict], round_num: int):
        """分析单轮结果"""
        successful_results = [r for r in results if r['success']]
        
        if not successful_results:
            print(f"  ❌ 第{round_num}轮：所有请求都失败了")
            return
        
        # CK分布统计
        ck_distribution = Counter(r['ck_id'] for r in successful_results)
        unique_cks = len(ck_distribution)
        
        print(f"  成功请求: {len(successful_results)}/{len(results)}")
        print(f"  使用的不同CK数: {unique_cks}")
        print(f"  CK分布: {dict(ck_distribution)}")
        
        # 检查负载均衡
        if unique_cks == 1:
            print(f"  ❌ 警告：所有请求都使用了同一个CK！")
        elif unique_cks >= 2:
            # 计算分布均匀性
            distribution_values = list(ck_distribution.values())
            max_count = max(distribution_values)
            min_count = min(distribution_values)
            balance_score = (1 - (max_count - min_count) / max_count) * 100
            print(f"  均衡分数: {balance_score:.1f}/100")
            
            if balance_score < 50:
                print(f"  ⚠️ 负载不均衡！")
    
    async def _analyze_comprehensive_results(self, all_results: List[Dict], available_cks: List):
        """综合分析所有结果"""
        successful_results = [r for r in all_results if r['success']]
        
        if not successful_results:
            print("❌ 所有请求都失败了！")
            return
        
        # 基础统计
        total_requests = len(all_results)
        success_rate = len(successful_results) / total_requests * 100
        
        print(f"总请求数: {total_requests}")
        print(f"成功率: {success_rate:.1f}%")
        
        # CK使用分布
        ck_distribution = Counter(r['ck_id'] for r in successful_results)
        unique_cks_used = len(ck_distribution)
        
        print(f"\n📊 CK使用分布:")
        for ck_id, count in sorted(ck_distribution.items()):
            percentage = count / len(successful_results) * 100
            bar = "█" * int(percentage / 2)
            print(f"  CK {ck_id}: {count:3d}次 ({percentage:5.1f}%) {bar}")
        
        # 负载均衡评估
        print(f"\n⚖️ 负载均衡评估:")
        print(f"  可用CK总数: {len(available_cks)}")
        print(f"  实际使用CK数: {unique_cks_used}")
        
        if unique_cks_used == 1:
            print(f"  ❌ 严重问题：所有请求都使用了同一个CK！")
            print(f"  🚨 生产环境风险：极高")
        elif unique_cks_used < len(available_cks):
            unused_cks = len(available_cks) - unique_cks_used
            print(f"  ⚠️ 有 {unused_cks} 个CK未被使用")
            print(f"  🚨 生产环境风险：中等")
        else:
            print(f"  ✅ 所有CK都被使用")
        
        # 分布均匀性
        if len(ck_distribution) > 1:
            distribution_values = list(ck_distribution.values())
            max_count = max(distribution_values)
            min_count = min(distribution_values)
            balance_score = (1 - (max_count - min_count) / max_count) * 100
            std_dev = statistics.stdev(distribution_values)
            
            print(f"  均衡分数: {balance_score:.1f}/100")
            print(f"  标准差: {std_dev:.2f}")
            
            if balance_score >= 80:
                print(f"  ✅ 负载均衡优秀")
                print(f"  🚨 生产环境风险：低")
            elif balance_score >= 60:
                print(f"  ✅ 负载均衡良好")
                print(f"  🚨 生产环境风险：低")
            elif balance_score >= 40:
                print(f"  ⚠️ 负载均衡一般")
                print(f"  🚨 生产环境风险：中等")
            else:
                print(f"  ❌ 负载均衡较差")
                print(f"  🚨 生产环境风险：高")
        
        # 性能分析
        selection_times = [r['selection_time'] for r in successful_results]
        if selection_times:
            avg_time = statistics.mean(selection_times)
            p95_time = statistics.quantiles(selection_times, n=20)[18]  # 95th percentile
            max_time = max(selection_times)
            
            print(f"\n⏱️ 性能分析:")
            print(f"  平均响应时间: {avg_time*1000:.1f}ms")
            print(f"  95%响应时间: {p95_time*1000:.1f}ms")
            print(f"  最大响应时间: {max_time*1000:.1f}ms")
            
            if avg_time > 0.5:
                print(f"  ⚠️ 平均响应时间过长，可能影响生产性能")
        
        # 时间分布分析（检查是否有时间聚集现象）
        print(f"\n🕐 时间分布分析:")
        timestamps = [r['timestamp'] for r in successful_results]
        if len(timestamps) > 1:
            time_span = max(timestamps) - min(timestamps)
            print(f"  请求时间跨度: {time_span:.3f}秒")
            
            # 检查时间聚集
            time_buckets = defaultdict(list)
            for result in successful_results:
                bucket = int(result['timestamp'] * 10) / 10  # 0.1秒精度
                time_buckets[bucket].append(result['ck_id'])
            
            # 检查每个时间段的CK分布
            clustered_selections = 0
            for bucket_time, ck_ids in time_buckets.items():
                if len(set(ck_ids)) == 1 and len(ck_ids) > 1:
                    clustered_selections += len(ck_ids)
            
            if clustered_selections > 0:
                cluster_rate = clustered_selections / len(successful_results) * 100
                print(f"  时间聚集选择率: {cluster_rate:.1f}%")
                if cluster_rate > 20:
                    print(f"  ⚠️ 存在明显的时间聚集现象，可能导致负载不均")
    
    async def _cleanup_test_environment(self, merchant_id: int):
        """清理测试环境"""
        try:
            # 清理锁
            lock_pattern = f"walmart:ck:lock:*"
            async for key in self.redis_client.scan_iter(match=lock_pattern):
                await self.redis_client.delete(key)
            
            # 重置随机计数器
            random_key = f"walmart:ck:random_counter:{merchant_id}"
            await self.redis_client.delete(random_key)
            
            print("✅ 测试环境清理完成")
            
        except Exception as e:
            print(f"⚠️ 清理测试环境失败: {e}")

    async def _reactivate_cks(self, merchant_id: int):
        """重新激活被禁用的CK"""
        try:
            cks = self.db.query(WalmartCK).filter(
                WalmartCK.merchant_id == merchant_id,
                WalmartCK.is_deleted == False
            ).all()

            for ck in cks:
                if not ck.active:
                    ck.active = True
                    print(f"  重新激活CK {ck.id}")

            self.db.commit()
            print("✅ CK重新激活完成")

        except Exception as e:
            self.db.rollback()
            print(f"⚠️ 重新激活CK失败: {e}")

    async def _compare_results(self, results: Dict, available_cks: List):
        """对比分析结果"""
        print(f"\n" + "="*80)
        print("📊 服务对比分析")
        print("="*80)

        original_results = [r for r in results['original_service'] if r['success']]
        production_results = [r for r in results['production_service'] if r['success']]

        # 基础对比
        print(f"原有服务:")
        print(f"  总请求数: {len(results['original_service'])}")
        print(f"  成功请求数: {len(original_results)}")
        print(f"  成功率: {len(original_results)/len(results['original_service'])*100:.1f}%")

        print(f"\n生产级服务:")
        print(f"  总请求数: {len(results['production_service'])}")
        print(f"  成功请求数: {len(production_results)}")
        print(f"  成功率: {len(production_results)/len(results['production_service'])*100:.1f}%")

        # 负载均衡对比
        if original_results:
            original_distribution = Counter(r['ck_id'] for r in original_results)
            original_unique = len(original_distribution)
            original_balance = self._calculate_balance_score(original_distribution)

            print(f"\n原有服务负载均衡:")
            print(f"  使用的不同CK数: {original_unique}")
            print(f"  CK分布: {dict(original_distribution)}")
            print(f"  均衡分数: {original_balance:.1f}/100")

        if production_results:
            production_distribution = Counter(r['ck_id'] for r in production_results)
            production_unique = len(production_distribution)
            production_balance = self._calculate_balance_score(production_distribution)

            print(f"\n生产级服务负载均衡:")
            print(f"  使用的不同CK数: {production_unique}")
            print(f"  CK分布: {dict(production_distribution)}")
            print(f"  均衡分数: {production_balance:.1f}/100")

        # 性能对比
        if original_results:
            original_times = [r['selection_time'] for r in original_results]
            original_avg = statistics.mean(original_times)
            print(f"\n原有服务性能:")
            print(f"  平均响应时间: {original_avg*1000:.1f}ms")

        if production_results:
            production_times = [r['selection_time'] for r in production_results]
            production_avg = statistics.mean(production_times)
            print(f"\n生产级服务性能:")
            print(f"  平均响应时间: {production_avg*1000:.1f}ms")

        # 总结
        print(f"\n🎯 对比总结:")

        if len(production_results) > len(original_results):
            print(f"  ✅ 生产级服务成功率更高")
        elif len(production_results) < len(original_results):
            print(f"  ❌ 生产级服务成功率较低")
        else:
            print(f"  ➖ 两个服务成功率相同")

        if production_results and original_results:
            if production_balance > original_balance:
                print(f"  ✅ 生产级服务负载均衡更好")
            elif production_balance < original_balance:
                print(f"  ❌ 生产级服务负载均衡较差")
            else:
                print(f"  ➖ 两个服务负载均衡相同")

            if production_avg < original_avg:
                print(f"  ✅ 生产级服务响应时间更快")
            elif production_avg > original_avg:
                print(f"  ❌ 生产级服务响应时间较慢")
            else:
                print(f"  ➖ 两个服务响应时间相同")

    def _calculate_balance_score(self, distribution: Counter) -> float:
        """计算负载均衡分数"""
        if len(distribution) <= 1:
            return 100.0

        values = list(distribution.values())
        max_count = max(values)
        min_count = min(values)

        return (1 - (max_count - min_count) / max_count) * 100


async def main():
    """主函数"""
    import argparse

    parser = argparse.ArgumentParser(description="生产环境高并发负载均衡测试")
    parser.add_argument("--merchant-id", type=int, default=2, help="商户ID")
    parser.add_argument("--concurrent", type=int, default=20, help="并发请求数")
    parser.add_argument("--duration", type=int, default=2, help="测试持续时间(秒)")
    parser.add_argument("--compare", action="store_true", help="运行对比测试")

    args = parser.parse_args()

    async with ProductionLoadTester() as tester:
        if args.compare:
            # 运行对比测试
            await tester.run_comparison_test(
                merchant_id=args.merchant_id,
                concurrent_requests=args.concurrent,
                test_rounds=args.duration
            )
        else:
            # 运行标准高并发测试
            await tester.run_high_concurrency_test(
                merchant_id=args.merchant_id,
                concurrent_requests=args.concurrent,
                test_duration=args.duration
            )

    print(f"\n🎯 测试建议:")
    print(f"  1. 如果负载均衡分数 < 60，需要立即优化算法")
    print(f"  2. 如果所有请求使用同一CK，存在生产风险")
    print(f"  3. 建议在生产部署前进行更大规模测试")
    print(f"  4. 使用 --compare 参数对比原有服务和生产级服务")


if __name__ == "__main__":
    asyncio.run(main())
