package signature

import (
	"crypto/hmac"
	"crypto/sha256"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"strconv"
	"strings"
	"time"
)

// SignatureValidator 签名验证器 - 与Python系统完全兼容
type SignatureValidator struct {
	timeWindow int64 // 时间窗口（秒）
}

// NewSignatureValidator 创建签名验证器
func NewSignatureValidator(timeWindow int64) *SignatureValidator {
	if timeWindow <= 0 {
		timeWindow = 300 // 默认5分钟
	}
	return &SignatureValidator{
		timeWindow: timeWindow,
	}
}

// ValidateSignature 验证签名 - 与Python系统算法完全一致
func (sv *SignatureValidator) ValidateSignature(
	requestBody []byte,
	secretKey string,
	timestamp string,
	nonce string,
	signature string,
) error {
	// 1. 验证时间戳
	if err := sv.validateTimestamp(timestamp); err != nil {
		return err
	}

	// 2. 生成签名
	expectedSignature, err := sv.GenerateSignature(requestBody, secretKey, timestamp, nonce)
	if err != nil {
		return fmt.Errorf("生成签名失败: %w", err)
	}

	// 3. 比较签名
	if signature != expectedSignature {
		return fmt.Errorf("签名验证失败")
	}

	return nil
}

// GenerateSignature 生成签名 - 与Python系统算法完全一致
func (sv *SignatureValidator) GenerateSignature(
	requestBody []byte,
	secretKey string,
	timestamp string,
	nonce string,
) (string, error) {
	// 1. 解析请求体为map
	var requestData map[string]interface{}
	if err := json.Unmarshal(requestBody, &requestData); err != nil {
		return "", fmt.Errorf("解析请求体失败: %w", err)
	}

	// 2. 规范化请求数据（排除debug字段）
	normalizedData := sv.normalizeData(requestData)

	// 3. 创建规范化的JSON字符串（与Python保持一致）
	jsonStr, err := sv.createNormalizedJSON(normalizedData)
	if err != nil {
		return "", fmt.Errorf("创建JSON字符串失败: %w", err)
	}

	// 4. 构建签名字符串（使用竖线分隔，与Python一致）
	method := "POST"
	path := "/api/v1/card-bind"
	signatureComponents := []string{method, path, timestamp, nonce, jsonStr, secretKey}
	signStr := strings.Join(signatureComponents, "|")

	// 5. 使用HMAC-SHA256生成签名并Base64编码
	h := hmac.New(sha256.New, []byte(secretKey))
	h.Write([]byte(signStr))
	signature := base64.StdEncoding.EncodeToString(h.Sum(nil))

	return signature, nil
}

// normalizeData 规范化请求数据，排除不参与签名校验的字段
func (sv *SignatureValidator) normalizeData(data map[string]interface{}) map[string]interface{} {
	// 定义不参与签名校验的字段
	excludedFields := map[string]bool{"debug": true}

	normalizedData := make(map[string]interface{})
	for key, value := range data {
		// 跳过排除的字段
		if excludedFields[key] {
			continue
		}
		if value != nil && value != "" {
			normalizedData[key] = value
		}
	}
	return normalizedData
}

// createNormalizedJSON 创建规范化的JSON字符串
func (sv *SignatureValidator) createNormalizedJSON(data map[string]interface{}) (string, error) {
	// 使用与Python json.dumps(separators=(',', ':'), sort_keys=True, ensure_ascii=False)一致的方法
	jsonBytes, err := json.Marshal(data)
	if err != nil {
		return "", err
	}
	
	// 移除所有空白字符，确保紧凑格式
	jsonStr := strings.ReplaceAll(string(jsonBytes), " ", "")
	
	return jsonStr, nil
}

// validateTimestamp 验证时间戳 - 与Python系统保持一致（毫秒时间戳）
func (sv *SignatureValidator) validateTimestamp(timestamp string) error {
	// 解析时间戳
	ts, err := strconv.ParseInt(timestamp, 10, 64)
	if err != nil {
		return fmt.Errorf("无效的时间戳格式")
	}

	// 将毫秒时间戳转换为秒（与Python系统保持一致）
	requestTime := float64(ts) / 1000.0
	currentTime := float64(time.Now().Unix())

	// 检查时间窗口（5分钟过去，1分钟未来）
	timestampTolerancePast := float64(300)   // 5分钟
	timestampToleranceFuture := float64(60)  // 1分钟

	if (currentTime - requestTime) > timestampTolerancePast {
		return fmt.Errorf("时间戳超出允许范围")
	}

	if (requestTime - currentTime) > timestampToleranceFuture {
		return fmt.Errorf("时间戳超出允许范围")
	}

	return nil
}

// abs 计算绝对值
func abs(x int64) int64 {
	if x < 0 {
		return -x
	}
	return x
}

// SignatureMiddleware 签名验证中间件配置
type SignatureMiddleware struct {
	validator *SignatureValidator
	enabled   bool
}

// NewSignatureMiddleware 创建签名验证中间件
func NewSignatureMiddleware(timeWindow int64, enabled bool) *SignatureMiddleware {
	return &SignatureMiddleware{
		validator: NewSignatureValidator(timeWindow),
		enabled:   enabled,
	}
}

// ValidateRequest 验证请求签名
func (sm *SignatureMiddleware) ValidateRequest(
	requestBody []byte,
	secretKey string,
	timestamp string,
	nonce string,
	signature string,
) error {
	if !sm.enabled {
		return nil // 签名验证已禁用
	}

	return sm.validator.ValidateSignature(requestBody, secretKey, timestamp, nonce, signature)
}

// GenerateTestSignature 生成测试签名（用于开发调试）
func (sm *SignatureMiddleware) GenerateTestSignature(
	requestBody []byte,
	secretKey string,
	timestamp string,
	nonce string,
) (string, error) {
	return sm.validator.GenerateSignature(requestBody, secretKey, timestamp, nonce)
}

// NonceManager 随机数管理器（防重放攻击）
type NonceManager struct {
	usedNonces map[string]int64
	timeWindow int64
}

// NewNonceManager 创建随机数管理器
func NewNonceManager(timeWindow int64) *NonceManager {
	return &NonceManager{
		usedNonces: make(map[string]int64),
		timeWindow: timeWindow,
	}
}

// ValidateNonce 验证随机数
func (nm *NonceManager) ValidateNonce(nonce string, timestamp int64) error {
	// 清理过期的nonce
	nm.cleanExpiredNonces(timestamp)

	// 检查nonce是否已使用
	if _, exists := nm.usedNonces[nonce]; exists {
		return fmt.Errorf("随机数重复使用")
	}

	// 记录nonce
	nm.usedNonces[nonce] = timestamp
	return nil
}

// cleanExpiredNonces 清理过期的随机数
func (nm *NonceManager) cleanExpiredNonces(currentTimestamp int64) {
	for nonce, timestamp := range nm.usedNonces {
		if currentTimestamp-timestamp > nm.timeWindow {
			delete(nm.usedNonces, nonce)
		}
	}
}
