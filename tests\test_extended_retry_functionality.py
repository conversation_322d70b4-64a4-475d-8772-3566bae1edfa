#!/usr/bin/env python3
"""
测试扩展的重试功能 - 支持pending状态的记录重试
"""

import sys
import os
import uuid
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from app.core.config import settings
from app.models.user import User
from app.models.merchant import Merchant
from app.models.department import Department
from app.models.walmart_ck import WalmartCK
from app.models.card_record import CardRecord, CardStatus
from app.services.card_record_service import CardRecordService


def create_test_session():
    """创建测试数据库会话"""
    engine = create_engine(settings.SQLALCHEMY_DATABASE_URI)
    SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
    return SessionLocal()


def setup_test_data(db):
    """设置测试数据"""
    print("设置测试数据...")
    
    # 清理可能存在的测试数据
    db.query(CardRecord).filter(CardRecord.merchant_order_id.like("RETRY_TEST_%")).delete()
    db.query(WalmartCK).filter(WalmartCK.sign.like("retry_test_%")).delete()
    db.query(User).filter(User.username.like("retry_test_%")).delete()
    db.query(Department).filter(Department.name.like("重试测试%")).delete()
    db.query(Merchant).filter(Merchant.name.like("重试测试%")).delete()
    db.commit()
    
    # 创建测试商户
    merchant = Merchant(
        name="重试测试商户",
        code="RETRY_TEST_MERCHANT",
        contact_name="测试联系人",
        contact_phone="13800000001",
        api_key="retry_test_api_key",
        api_secret="retry_test_api_secret"
    )
    db.add(merchant)
    db.flush()
    
    # 创建测试部门
    dept = Department(
        name="重试测试部门",
        code="RETRY_TEST_DEPT",
        merchant_id=merchant.id,
        parent_id=None
    )
    db.add(dept)
    db.flush()
    
    # 创建测试用户
    user = User(
        username="retry_test_user",
        email="<EMAIL>",
        hashed_password="test_password",
        merchant_id=merchant.id,
        department_id=dept.id
    )
    db.add(user)
    db.flush()
    
    # 创建测试CK
    ck = WalmartCK(
        sign="retry_test_ck_sign",
        merchant_id=merchant.id,
        department_id=dept.id,
        daily_limit=100,
        hourly_limit=10,
        active=True,
        created_by=user.id
    )
    db.add(ck)
    db.commit()
    
    return {
        'merchant': merchant,
        'dept': dept,
        'user': user,
        'ck': ck
    }


def test_failed_status_retry(db, test_data):
    """测试failed状态的重试功能"""
    print("\n=== 测试failed状态的重试功能 ===")
    
    card_service = CardRecordService(db)
    
    # 创建failed状态的卡记录（模拟CK失效错误）
    failed_card = CardRecord(
        id=str(uuid.uuid4()),
        merchant_id=test_data['merchant'].id,
        department_id=test_data['dept'].id,
        walmart_ck_id=test_data['ck'].id,
        merchant_order_id="RETRY_TEST_FAILED_001",
        amount=10000,
        card_number="1111222233334444",
        status=CardStatus.FAILED,
        request_id=str(uuid.uuid4()),
        request_data={},
        error_message="需要登录",  # CK失效错误
        retry_count=0
    )
    db.add(failed_card)
    db.commit()
    
    print(f"创建failed状态卡记录: {failed_card.id}")
    
    # 测试重试
    try:
        result = card_service.retry_single_card(str(failed_card.id), test_data['user'])
        print(f"✅ failed状态重试成功: {result['message']}")
        print(f"   重试次数: {result['retry_count']}")
        print(f"   新状态: {result['status']}")
    except ValueError as e:
        print(f"❌ failed状态重试失败: {e}")
    
    # 清理
    db.delete(failed_card)
    db.commit()


def test_pending_status_retry(db, test_data):
    """测试pending状态的重试功能"""
    print("\n=== 测试pending状态的重试功能 ===")
    
    card_service = CardRecordService(db)
    
    # 创建pending状态的卡记录
    pending_card = CardRecord(
        id=str(uuid.uuid4()),
        merchant_id=test_data['merchant'].id,
        department_id=test_data['dept'].id,
        walmart_ck_id=None,  # pending状态通常没有分配CK
        merchant_order_id="RETRY_TEST_PENDING_001",
        amount=10000,
        card_number="****************",
        status=CardStatus.PENDING,
        request_id=str(uuid.uuid4()),
        request_data={},
        retry_count=0
    )
    db.add(pending_card)
    db.commit()
    
    print(f"创建pending状态卡记录: {pending_card.id}")
    
    # 测试重试
    try:
        result = card_service.retry_single_card(str(pending_card.id), test_data['user'])
        print(f"✅ pending状态重试成功: {result['message']}")
        print(f"   重试次数: {result['retry_count']}")
        print(f"   新状态: {result['status']}")
    except ValueError as e:
        print(f"❌ pending状态重试失败: {e}")
    
    # 清理
    db.delete(pending_card)
    db.commit()


def test_non_retryable_failed_status(db, test_data):
    """测试不可重试的failed状态"""
    print("\n=== 测试不可重试的failed状态 ===")
    
    card_service = CardRecordService(db)
    
    # 创建failed状态的卡记录（非CK失效错误）
    non_retryable_card = CardRecord(
        id=str(uuid.uuid4()),
        merchant_id=test_data['merchant'].id,
        department_id=test_data['dept'].id,
        walmart_ck_id=test_data['ck'].id,
        merchant_order_id="RETRY_TEST_NON_RETRYABLE_001",
        amount=10000,
        card_number="9999888877776666",
        status=CardStatus.FAILED,
        request_id=str(uuid.uuid4()),
        request_data={},
        error_message="卡号无效",  # 非CK失效错误
        retry_count=0
    )
    db.add(non_retryable_card)
    db.commit()
    
    print(f"创建不可重试的failed状态卡记录: {non_retryable_card.id}")
    
    # 测试重试（应该失败）
    try:
        result = card_service.retry_single_card(str(non_retryable_card.id), test_data['user'])
        print(f"❌ 不可重试的failed状态重试应该失败但却成功了: {result}")
    except ValueError as e:
        if "不允许重试" in str(e):
            print(f"✅ 不可重试的failed状态正确被拒绝: {e}")
        else:
            print(f"❌ 错误信息不正确: {e}")
    
    # 清理
    db.delete(non_retryable_card)
    db.commit()


def test_success_status_retry(db, test_data):
    """测试success状态的重试功能（应该被拒绝）"""
    print("\n=== 测试success状态的重试功能 ===")
    
    card_service = CardRecordService(db)
    
    # 创建success状态的卡记录
    success_card = CardRecord(
        id=str(uuid.uuid4()),
        merchant_id=test_data['merchant'].id,
        department_id=test_data['dept'].id,
        walmart_ck_id=test_data['ck'].id,
        merchant_order_id="RETRY_TEST_SUCCESS_001",
        amount=10000,
        card_number="1234567890123456",
        status=CardStatus.SUCCESS,
        request_id=str(uuid.uuid4()),
        request_data={},
        retry_count=0
    )
    db.add(success_card)
    db.commit()
    
    print(f"创建success状态卡记录: {success_card.id}")
    
    # 测试重试（应该失败）
    try:
        result = card_service.retry_single_card(str(success_card.id), test_data['user'])
        print(f"❌ success状态重试应该失败但却成功了: {result}")
    except ValueError as e:
        if "只能重试失败或待处理状态" in str(e):
            print(f"✅ success状态重试正确被拒绝: {e}")
        else:
            print(f"❌ 错误信息不正确: {e}")
    
    # 清理
    db.delete(success_card)
    db.commit()


def test_merchant_isolation_in_retry(db, test_data):
    """测试重试功能中的商户隔离"""
    print("\n=== 测试重试功能中的商户隔离 ===")
    
    # 创建另一个商户的用户
    other_merchant = Merchant(
        name="其他测试商户",
        code="OTHER_TEST_MERCHANT",
        contact_name="其他联系人",
        contact_phone="13800000002",
        api_key="other_test_api_key",
        api_secret="other_test_api_secret"
    )
    db.add(other_merchant)
    db.flush()
    
    other_user = User(
        username="other_retry_test_user",
        email="<EMAIL>",
        hashed_password="test_password",
        merchant_id=other_merchant.id,
        department_id=None
    )
    db.add(other_user)
    db.commit()
    
    card_service = CardRecordService(db)
    
    # 创建测试商户的卡记录
    test_card = CardRecord(
        id=str(uuid.uuid4()),
        merchant_id=test_data['merchant'].id,
        department_id=test_data['dept'].id,
        walmart_ck_id=None,
        merchant_order_id="RETRY_TEST_ISOLATION_001",
        amount=10000,
        card_number="1111222233334444",
        status=CardStatus.PENDING,
        request_id=str(uuid.uuid4()),
        request_data={},
        retry_count=0
    )
    db.add(test_card)
    db.commit()
    
    print(f"创建测试商户的卡记录: {test_card.id}")
    
    # 尝试用其他商户的用户重试（应该失败）
    try:
        result = card_service.retry_single_card(str(test_card.id), other_user)
        print(f"❌ 跨商户重试应该失败但却成功了: {result}")
    except ValueError as e:
        if "不存在或无权限访问" in str(e):
            print(f"✅ 跨商户重试正确被拒绝: {e}")
        else:
            print(f"❌ 错误信息不正确: {e}")
    
    # 清理
    db.delete(test_card)
    db.delete(other_user)
    db.delete(other_merchant)
    db.commit()


def cleanup_test_data(db):
    """清理测试数据"""
    print("\n清理测试数据...")
    
    db.query(CardRecord).filter(CardRecord.merchant_order_id.like("RETRY_TEST_%")).delete()
    db.query(WalmartCK).filter(WalmartCK.sign.like("retry_test_%")).delete()
    db.query(User).filter(User.username.like("retry_test_%")).delete()
    db.query(User).filter(User.username.like("other_retry_test_%")).delete()
    db.query(Department).filter(Department.name.like("重试测试%")).delete()
    db.query(Merchant).filter(Merchant.name.like("重试测试%")).delete()
    db.query(Merchant).filter(Merchant.name.like("其他测试%")).delete()
    db.commit()
    
    print("✅ 测试数据清理完成")


def main():
    """主测试函数"""
    print("开始扩展重试功能测试")
    print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    db = create_test_session()
    
    try:
        # 设置测试数据
        test_data = setup_test_data(db)
        
        # 执行测试
        test_failed_status_retry(db, test_data)
        test_pending_status_retry(db, test_data)
        test_non_retryable_failed_status(db, test_data)
        test_success_status_retry(db, test_data)
        test_merchant_isolation_in_retry(db, test_data)
        
        print(f"\n🎉 扩展重试功能测试完成！")
        print(f"测试完成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        raise
    finally:
        # 清理测试数据
        cleanup_test_data(db)
        db.close()


if __name__ == "__main__":
    main()
