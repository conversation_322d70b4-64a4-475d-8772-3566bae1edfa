"""
动态权限服务 - 完全基于数据库配置，无硬编码
"""

from typing import List, Optional, Dict, Any
from sqlalchemy.orm import Session
from sqlalchemy import text
from app.models.user import User
import logging

logger = logging.getLogger(__name__)


class PermissionService:
    """动态权限服务类 - 基于数据库配置，无硬编码"""

    def __init__(self, db: Session):
        self.db = db
        self.logger = logger

    def check_user_permission(self, user: User, permission_code: str) -> bool:
        """
        检查用户是否有指定权限

        Args:
            user: 用户对象
            permission_code: 权限代码，如 'menu:dashboard' 或 'api:/api/v1/users'

        Returns:
            bool: 是否有权限
        """
        try:
            # 超级管理员拥有所有权限
            if user.is_superuser:
                return True

            # 检查用户直接权限（通过user_permissions表）
            user_permission_sql = text("""
                SELECT p.code
                FROM permissions p
                INNER JOIN user_permissions up ON p.id = up.permission_id
                WHERE up.user_id = :user_id AND p.code = :permission_code
            """)
            user_permission_result = self.db.execute(
                user_permission_sql,
                {"user_id": user.id, "permission_code": permission_code}
            ).first()
            if user_permission_result:
                return True

            # 检查用户角色权限（通过role_permissions表 - 基于ID外键）
            role_permission_sql = text("""
                SELECT p.code
                FROM permissions p
                INNER JOIN role_permissions rp ON p.id = rp.permission_id
                INNER JOIN user_roles ur ON rp.role_id = ur.role_id
                WHERE ur.user_id = :user_id
                AND p.code = :permission_code
            """)
            role_permission_result = self.db.execute(
                role_permission_sql,
                {"user_id": user.id, "permission_code": permission_code}
            ).first()
            if role_permission_result:
                return True

            return False

        except Exception as e:
            self.logger.error(f"权限检查失败: {e}")
            return False

    def check_menu_permission(self, user: User, menu_code: str) -> bool:
        """检查用户是否有菜单权限"""
        return self.check_user_permission(user, f"menu:{menu_code}")

    def check_api_permission(self, user: User, api_path: str) -> bool:
        """
        检查用户是否有API权限

        Args:
            user: 用户对象
            api_path: API路径，如 '/api/v1/users' 或 '/api/v1/users/123'

        Returns:
            bool: 是否有API权限
        """
        try:
            # 超级管理员拥有所有权限
            if user.is_superuser:
                return True

            # 处理带参数的API路径，提取基础路径
            base_api_path = self._extract_base_api_path(api_path)

            # 构造API权限代码
            api_permission_code = f"api:{base_api_path}"

            # 检查用户是否有该API权限
            return self.check_user_permission(user, api_permission_code)

        except Exception as e:
            self.logger.error(f"API权限检查失败: {e}")
            return False

    def _extract_base_api_path(self, api_path: str) -> str:
        """
        提取API的基础路径，去除路径参数

        Args:
            api_path: 完整的API路径，如 '/api/v1/walmart-ck/123'

        Returns:
            str: 基础API路径，如 '/api/v1/walmart-ck'
        """
        import re

        # 特殊路径映射 - 将特定的API路径映射到对应的权限代码
        special_path_mappings = {
            '/api/v1/merchants/current/api-credentials': 'merchants:api-credentials',
            '/api/v1/merchants/current/verify-password': 'merchants:verify-password',
            '/api/v1/merchants/current/reset-api-key': 'merchants:reset-api-key',
            '/api/v1/merchants/current/generate-api-key': 'merchants:generate-api-key',
        }

        # 检查是否是特殊路径
        if api_path in special_path_mappings:
            return special_path_mappings[api_path]

        # 【修复】对账台模块特殊处理 - 确保权限一致性
        if api_path.startswith('/api/v1/reconciliation/'):
            # 对账台模块的路径映射规则
            reconciliation_patterns = [
                # 部门相关接口统一映射到基础模块权限
                (r'/api/v1/reconciliation/departments/.*', '/api/v1/reconciliation'),
                # CK相关接口统一映射到基础模块权限
                (r'/api/v1/reconciliation/ck/.*', '/api/v1/reconciliation'),
                # 导出相关接口统一映射到基础模块权限
                (r'/api/v1/reconciliation/export/.*', '/api/v1/reconciliation'),
            ]

            for pattern, mapped_path in reconciliation_patterns:
                if re.match(pattern, api_path):
                    return mapped_path

        # 定义常见的API路径模式，将路径参数替换为基础路径
        patterns = [
            # 匹配 /api/v1/resource/{id} 格式
            (r'/api/v1/([^/]+)/\d+$', r'/api/v1/\1'),
            (r'/api/v1/([^/]+)/[^/]+$', r'/api/v1/\1'),
            # 匹配 /api/v1/resource/{id}/action 格式
            (r'/api/v1/([^/]+)/\d+/([^/]+)$', r'/api/v1/\1'),
            (r'/api/v1/([^/]+)/[^/]+/([^/]+)$', r'/api/v1/\1'),
        ]

        for pattern, replacement in patterns:
            if re.match(pattern, api_path):
                return re.sub(pattern, replacement, api_path)

        # 如果没有匹配的模式，返回原路径
        return api_path



    def can_access_merchant_data(self, user: User, merchant_id: int) -> bool:
        """
        检查用户是否可以访问指定商户的数据 - 基于数据权限配置

        Args:
            user: 用户对象
            merchant_id: 商户ID

        Returns:
            bool: 是否可以访问
        """
        try:
            # 超级管理员可以访问所有数据
            if user.is_superuser:
                return True

            # 检查用户是否有访问所有商户数据的权限
            if self.check_data_permission(user, 'data:merchant:all'):
                return True

            # 检查用户是否有访问本商户数据的权限，且商户ID匹配
            if self.check_data_permission(user, 'data:merchant:own'):
                return user.merchant_id == merchant_id

            return False

        except Exception as e:
            self.logger.error(f"检查商户数据访问权限失败: {e}")
            return False

    def get_user_permissions(self, user: User) -> List[str]:
        """获取用户的所有权限代码列表 - 优化N+1查询"""
        try:
            permissions = set()

            # 超级管理员拥有所有权限
            if user.is_superuser:
                from app.models.permission import Permission
                all_perms = self.db.query(Permission).all()
                permissions.update(perm.code for perm in all_perms)
                permissions.add("*:*:*")  # 通配符权限
                return list(permissions)

            # 使用预加载获取用户及其权限和角色，避免N+1查询
            user_with_relations = self._get_user_with_preloaded_relations(user.id)
            if not user_with_relations:
                return []

            # 获取用户直接权限
            if user_with_relations.permissions:
                permissions.update(perm.code for perm in user_with_relations.permissions)

            # 获取用户角色权限 - 批量查询避免N+1
            if user_with_relations.roles:
                role_codes = [role.code for role in user_with_relations.roles]
                role_permissions = self._get_batch_role_permissions(role_codes)
                permissions.update(role_permissions)

            return list(permissions)

        except Exception as e:
            self.logger.error(f"获取用户权限失败: {e}")
            return []

    def get_user_data_permissions(self, user: User) -> List[str]:
        """获取用户的数据权限列表"""
        try:
            data_permissions = set()

            # 超级管理员拥有所有数据权限
            if user.is_superuser:
                data_permissions.update(['data:merchant:all', 'data:department:all', 'data:user:all'])
                return list(data_permissions)

            # 获取用户的所有权限
            all_permissions = self.get_user_permissions(user)

            # 筛选出数据权限
            for perm in all_permissions:
                if perm.startswith('data:'):
                    data_permissions.add(perm)

            return list(data_permissions)

        except Exception as e:
            self.logger.error(f"获取用户数据权限失败: {e}")
            return []

    def check_data_permission(self, user: User, data_permission_code: str) -> bool:
        """检查用户是否有指定的数据权限"""
        try:
            # 超级管理员拥有所有数据权限
            if user.is_superuser:
                return True

            # 检查用户是否有该数据权限
            return self.check_user_permission(user, data_permission_code)

        except Exception as e:
            self.logger.error(f"检查数据权限失败: {e}")
            return False

    def get_user_menus(self, user: User) -> List[Dict[str, Any]]:
        """获取用户可访问的菜单列表 - 基于role_menus表"""
        try:
            # 超级管理员可以访问所有菜单 - 使用原生SQL避免枚举问题
            if user.is_superuser:
                all_menus_sql = text("""
                    SELECT id, name, code, path, component, icon, parent_id, level,
                           sort_order, is_visible, is_enabled, menu_type, description
                    FROM menus
                    WHERE is_visible = 1 AND is_enabled = 1
                    ORDER BY sort_order, id
                """)
                result = self.db.execute(all_menus_sql).fetchall()
                all_menus = self._convert_sql_result_to_menu_objects(result)
                return self._build_menu_tree(all_menus)

            # 直接从role_menus表获取用户可访问的菜单
            accessible_menus = self._get_user_accessible_menus(user)

            if not accessible_menus:
                return self._get_default_menus()

            return self._build_menu_tree(accessible_menus)

        except Exception as e:
            self.logger.error(f"获取用户菜单失败: {e}")
            return self._get_default_menus()

    def _get_user_accessible_menus(self, user: User) -> List:
        """
        从role_menus表获取用户可访问的菜单

        Args:
            user: 用户对象

        Returns:
            List: 菜单对象列表
        """
        try:
            from app.models.menu import Menu

            # 通过role_menus表查询用户可访问的菜单
            accessible_menus_sql = text("""
                SELECT DISTINCT m.*
                FROM menus m
                INNER JOIN role_menus rm ON m.id = rm.menu_id
                INNER JOIN user_roles ur ON rm.role_id = ur.role_id
                WHERE ur.user_id = :user_id
                AND m.is_visible = 1
                AND m.is_enabled = 1
                ORDER BY m.sort_order, m.id
            """)

            result = self.db.execute(accessible_menus_sql, {"user_id": user.id}).fetchall()

            # 将查询结果转换为Menu对象
            menus = []
            for row in result:
                menu = Menu()
                menu.id = row.id
                menu.name = row.name
                menu.code = row.code
                menu.path = row.path
                menu.component = row.component
                menu.icon = row.icon
                menu.parent_id = row.parent_id
                menu.level = row.level
                menu.sort_order = row.sort_order
                menu.is_visible = row.is_visible
                menu.is_enabled = row.is_enabled
                menu.menu_type = row.menu_type
                menu.description = row.description
                # 添加redirect和type属性以兼容_build_menu_tree方法
                menu.redirect = None
                menu.type = row.menu_type
                menus.append(menu)

            return menus

        except Exception as e:
            self.logger.error(f"从role_menus表获取用户菜单失败: {e}")
            return []

    def _convert_sql_result_to_menu_objects(self, result) -> List:
        """
        将SQL查询结果转换为菜单对象列表

        Args:
            result: SQL查询结果

        Returns:
            List: 菜单对象列表
        """
        menus = []
        for row in result:
            # 创建一个简单的菜单对象
            class MenuObject:
                def __init__(self):
                    pass

            menu = MenuObject()
            menu.id = row.id
            menu.name = row.name
            menu.code = row.code
            menu.path = row.path
            menu.component = row.component
            menu.icon = row.icon
            menu.parent_id = row.parent_id
            menu.level = row.level
            menu.sort_order = row.sort_order
            menu.is_visible = row.is_visible
            menu.is_enabled = row.is_enabled
            menu.menu_type = row.menu_type
            menu.description = row.description
            # 添加redirect和type属性以兼容_build_menu_tree方法
            menu.redirect = None
            menu.type = row.menu_type
            menus.append(menu)

        return menus

    def _build_menu_tree(self, menus: List) -> List[Dict[str, Any]]:
        """
        构建菜单树结构，自动包含必要的父菜单

        Args:
            menus: 菜单列表

        Returns:
            List[Dict]: 菜单树
        """
        # 将菜单转换为字典格式
        menu_dict = {}
        for menu in menus:
            menu_dict[menu.id] = {
                "id": menu.id,
                "name": menu.name,
                "code": menu.code,
                "path": menu.path,
                "redirect": menu.redirect,
                "icon": menu.icon,
                "type": menu.type,
                "is_visible": menu.is_visible,
                "sort_order": menu.sort_order,
                "parent_id": menu.parent_id,
                "children": []
            }

        # 【修复】自动包含必要的父菜单
        # 如果子菜单存在但父菜单不存在，则从数据库查询并添加父菜单
        missing_parent_ids = set()
        for menu_data in menu_dict.values():
            parent_id = menu_data["parent_id"]
            if parent_id is not None and parent_id not in menu_dict:
                missing_parent_ids.add(parent_id)

        # 查询缺失的父菜单
        if missing_parent_ids:
            try:
                from sqlalchemy import text
                missing_parents_sql = text("""
                    SELECT id, name, code, path, component, icon, parent_id, level,
                           sort_order, is_visible, is_enabled, menu_type, description
                    FROM menus
                    WHERE id IN :parent_ids
                    AND is_visible = 1 AND is_enabled = 1
                """)
                result = self.db.execute(missing_parents_sql, {"parent_ids": tuple(missing_parent_ids)}).fetchall()

                # 添加缺失的父菜单到menu_dict
                for row in result:
                    if row.id not in menu_dict:
                        menu_dict[row.id] = {
                            "id": row.id,
                            "name": row.name,
                            "code": row.code,
                            "path": row.path,
                            "redirect": getattr(row, 'redirect', None),
                            "icon": row.icon,
                            "type": row.menu_type,
                            "is_visible": row.is_visible,
                            "sort_order": row.sort_order,
                            "parent_id": row.parent_id,
                            "children": []
                        }
                        self.logger.info(f"自动添加父菜单: {row.name} (ID: {row.id})")

                # 递归查询更上级的父菜单
                new_missing_parent_ids = set()
                for row in result:
                    if row.parent_id is not None and row.parent_id not in menu_dict:
                        new_missing_parent_ids.add(row.parent_id)

                # 如果还有更上级的父菜单缺失，递归查询（最多3层，避免无限递归）
                recursion_depth = 0
                while new_missing_parent_ids and recursion_depth < 3:
                    recursion_depth += 1
                    upper_parents_result = self.db.execute(missing_parents_sql, {"parent_ids": tuple(new_missing_parent_ids)}).fetchall()

                    next_missing_parent_ids = set()
                    for row in upper_parents_result:
                        if row.id not in menu_dict:
                            menu_dict[row.id] = {
                                "id": row.id,
                                "name": row.name,
                                "code": row.code,
                                "path": row.path,
                                "redirect": getattr(row, 'redirect', None),
                                "icon": row.icon,
                                "type": row.menu_type,
                                "is_visible": row.is_visible,
                                "sort_order": row.sort_order,
                                "parent_id": row.parent_id,
                                "children": []
                            }
                            self.logger.info(f"自动添加上级父菜单: {row.name} (ID: {row.id})")

                        if row.parent_id is not None and row.parent_id not in menu_dict:
                            next_missing_parent_ids.add(row.parent_id)

                    new_missing_parent_ids = next_missing_parent_ids

            except Exception as e:
                self.logger.error(f"查询缺失父菜单失败: {e}")

        # 构建树结构
        root_menus = []
        for menu_id, menu_data in menu_dict.items():
            parent_id = menu_data["parent_id"]
            if parent_id is None:
                # 根菜单
                root_menus.append(menu_data)
            elif parent_id in menu_dict:
                # 子菜单
                menu_dict[parent_id]["children"].append(menu_data)

        # 递归排序
        def sort_menus(menu_list):
            menu_list.sort(key=lambda x: (x["sort_order"], x["id"]))
            for menu in menu_list:
                if menu["children"]:
                    sort_menus(menu["children"])

        sort_menus(root_menus)
        return root_menus

    def _get_default_menus(self) -> List[Dict[str, Any]]:
        """获取默认菜单（仪表盘）"""
        return [
            {
                "id": 1,
                "name": "仪表盘",
                "code": "dashboard",
                "path": "/dashboard",
                "redirect": None,
                "icon": "el-icon-odometer",
                "type": "menu",
                "is_visible": True,
                "sort_order": 1,
                "parent_id": None,
                "children": []
            }
        ]

    def _get_user_menu_permissions(self, user: User) -> set:
        """获取用户菜单权限"""
        menu_permissions = set()

        # 获取用户直接菜单权限
        if hasattr(user, 'permissions') and user.permissions:
            menu_permissions.update(perm.code for perm in user.permissions if perm.code.startswith('menu:'))

        # 获取用户角色菜单权限
        if hasattr(user, 'roles') and user.roles:
            for role in user.roles:
                if hasattr(role, 'permissions') and role.permissions:
                    menu_permissions.update(perm.code for perm in role.permissions if perm.code.startswith('menu:'))

        return menu_permissions



    def can_access_department_data(self, user: User, department_id: int) -> bool:
        """检查用户是否可以访问指定部门的数据 - 基于数据权限配置"""
        try:
            # 超级管理员可以访问所有数据
            if user.is_superuser:
                return True

            # 检查部门是否属于用户的商户
            sql = text("SELECT COUNT(*) as count FROM departments d WHERE d.id = :department_id AND d.merchant_id = :merchant_id")
            result = self.db.execute(sql, {'department_id': department_id, 'merchant_id': user.merchant_id}).fetchone()

            if not result or result.count == 0:
                return False

            # 检查用户是否有访问所有部门数据的权限
            if self.check_data_permission(user, 'data:department:all'):
                return True

            # 检查用户是否有访问本部门数据的权限
            if self.check_data_permission(user, 'data:department:own'):
                # 需要检查用户是否属于该部门
                return hasattr(user, 'department_id') and user.department_id == department_id

            # 检查用户是否有访问本部门及下级部门数据的权限
            if self.check_data_permission(user, 'data:department:sub'):
                # 需要检查该部门是否是用户部门的下级部门
                return self._is_department_accessible_by_user(user, department_id)

            return False

        except Exception as e:
            self.logger.error(f"检查部门访问权限失败: {e}")
            return False

    def _is_department_accessible_by_user(self, user: User, department_id: int) -> bool:
        """检查部门是否在用户的访问范围内（包括下级部门）"""
        try:
            if not hasattr(user, 'department_id') or not user.department_id:
                return False

            # 检查是否是同一个部门
            if user.department_id == department_id:
                return True

            # 检查是否是下级部门（通过path字段判断）
            sql = text("""
                SELECT COUNT(*) as count
                FROM departments d1, departments d2
                WHERE d1.id = :user_dept_id
                AND d2.id = :target_dept_id
                AND d2.path LIKE CONCAT(d1.path, d1.id, '/%')
            """)
            result = self.db.execute(sql, {
                'user_dept_id': user.department_id,
                'target_dept_id': department_id
            }).fetchone()

            return result and result.count > 0

        except Exception as e:
            self.logger.error(f"检查部门层级关系失败: {e}")
            return False

    def _get_user_with_preloaded_relations(self, user_id: int):
        """获取用户及其预加载的关联数据，避免N+1查询"""
        try:
            from sqlalchemy.orm import selectinload
            from app.models.user import User
            return (
                self.db.query(User)
                .options(
                    selectinload(User.permissions),
                    selectinload(User.roles)
                )
                .filter(User.id == user_id)
                .first()
            )
        except Exception as e:
            self.logger.error(f"获取用户关联数据失败: {e}")
            return None

    def _get_batch_role_permissions(self, role_codes: List[str]) -> set:
        """批量获取角色权限，避免N+1查询 - 基于ID外键"""
        if not role_codes:
            return set()

        try:
            # 使用基于ID的外键查询
            role_permissions_sql = text("""
                SELECT DISTINCT p.code
                FROM permissions p
                INNER JOIN role_permissions rp ON p.id = rp.permission_id
                INNER JOIN roles r ON rp.role_id = r.id
                WHERE r.code IN :role_codes
            """)
            results = self.db.execute(role_permissions_sql, {"role_codes": tuple(role_codes)}).fetchall()
            return {result[0] for result in results}
        except Exception as e:
            self.logger.error(f"批量获取角色权限失败: {e}")
            return set()


# 创建全局权限服务实例（无需数据库会话）
class GlobalPermissionService:
    """全局权限服务，用于装饰器等场景"""

    def check_user_permission(self, user: User, permission_code: str, db: Session) -> bool:
        """检查用户权限（需要传入数据库会话）"""
        service = PermissionService(db)
        return service.check_user_permission(user, permission_code)

    def check_menu_permission(self, user: User, menu_code: str, db: Session) -> bool:
        """检查菜单权限（需要传入数据库会话）"""
        service = PermissionService(db)
        return service.check_menu_permission(user, menu_code)

    def check_api_permission(self, user: User, api_path: str, db: Session) -> bool:
        """检查API权限（需要传入数据库会话）"""
        service = PermissionService(db)
        return service.check_api_permission(user, api_path)

    def get_user_menus(self, user: User, db: Session) -> List[dict]:
        """获取用户菜单（需要传入数据库会话）"""
        service = PermissionService(db)
        return service.get_user_menus(user)

    def can_access_merchant_data(self, user: User, merchant_id: int, db: Session) -> bool:
        """检查商户数据访问权限（需要传入数据库会话）"""
        service = PermissionService(db)
        return service.can_access_merchant_data(user, merchant_id)

    def can_access_department_data(self, user: User, department_id: int, db: Session) -> bool:
        """检查部门数据访问权限（需要传入数据库会话）"""
        service = PermissionService(db)
        return service.can_access_department_data(user, department_id)

    def get_user_data_permissions(self, user: User, db: Session) -> List[str]:
        """获取用户数据权限（需要传入数据库会话）"""
        service = PermissionService(db)
        return service.get_user_data_permissions(user)

    def check_data_permission(self, user: User, data_permission_code: str, db: Session) -> bool:
        """检查数据权限（需要传入数据库会话）"""
        service = PermissionService(db)
        return service.check_data_permission(user, data_permission_code)


# 创建全局实例
permission_service = GlobalPermissionService()
