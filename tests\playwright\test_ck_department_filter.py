"""
使用Playwright测试沃尔玛CK管理页面的部门筛选功能
验证统计数据和列表数据的一致性问题修复
"""
import asyncio
import re
from playwright.async_api import async_playwright, <PERSON>, Browser, BrowserContext


class CKDepartmentFilterTest:
    """CK管理页面部门筛选功能测试"""
    
    def __init__(self):
        self.base_url = "http://localhost:2000"
        self.browser = None
        self.context = None
        self.page = None
    
    async def setup(self):
        """初始化浏览器"""
        playwright = await async_playwright().start()
        self.browser = await playwright.chromium.launch(headless=False)
        self.context = await self.browser.new_context()
        self.page = await self.context.new_page()
    
    async def teardown(self):
        """清理资源"""
        if self.browser:
            await self.browser.close()
    
    async def login(self, username: str, password: str):
        """登录系统"""
        print(f"正在登录用户: {username}")
        
        # 访问登录页面
        await self.page.goto(f"{self.base_url}/#/login")
        await self.page.wait_for_load_state("networkidle")
        
        # 填写登录信息
        await self.page.fill('input[placeholder="请输入用户名"]', username)
        await self.page.fill('input[placeholder="请输入密码"]', password)
        
        # 点击登录按钮
        await self.page.click('button:has-text("登录")')
        
        # 等待登录成功，检查是否跳转到主页
        await self.page.wait_for_url(re.compile(r".*/#/dashboard.*"), timeout=10000)
        print(f"用户 {username} 登录成功")
    
    async def navigate_to_ck_management(self):
        """导航到CK管理页面"""
        print("导航到CK管理页面")

        # 直接访问CK管理页面URL
        await self.page.goto(f"{self.base_url}/#/walmart/user")
        await self.page.wait_for_load_state("networkidle")
        print("已进入CK管理页面")
    
    async def get_statistics_data(self):
        """获取统计卡片数据"""
        print("获取统计数据...")
        
        # 等待统计卡片加载
        await self.page.wait_for_selector('.statistics-section', timeout=10000)
        
        # 获取统计数据
        stats = {}
        
        # 获取CK数量统计
        try:
            ck_count_element = await self.page.wait_for_selector('text=CK数量统计', timeout=5000)
            if ck_count_element:
                parent = await ck_count_element.locator('..').locator('..').locator('..')
                count_text = await parent.locator('.text-2xl').inner_text()
                stats['ck_count'] = int(count_text.strip())
        except:
            stats['ck_count'] = 0
        
        # 获取成功率统计
        try:
            success_rate_element = await self.page.wait_for_selector('text=成功率', timeout=5000)
            if success_rate_element:
                parent = await success_rate_element.locator('..').locator('..').locator('..')
                rate_text = await parent.locator('.text-2xl').inner_text()
                stats['success_rate'] = rate_text.strip()
        except:
            stats['success_rate'] = '0%'
        
        print(f"统计数据: {stats}")
        return stats
    
    async def get_ck_list_data(self):
        """获取CK列表数据"""
        print("获取CK列表数据...")
        
        # 等待表格加载
        await self.page.wait_for_selector('.el-table', timeout=10000)
        
        # 获取表格行数
        rows = await self.page.locator('.el-table tbody tr').count()
        
        # 获取每行的部门信息
        departments = []
        for i in range(rows):
            try:
                # 获取部门列的文本（假设部门在第4列）
                dept_cell = self.page.locator('.el-table tbody tr').nth(i).locator('td').nth(3)
                dept_text = await dept_cell.inner_text()
                departments.append(dept_text.strip())
            except:
                departments.append("未知")
        
        list_data = {
            'total_count': rows,
            'departments': departments
        }
        
        print(f"列表数据: 总数={list_data['total_count']}, 部门={set(departments)}")
        return list_data
    
    async def select_department_filter(self, department_name: str = None):
        """选择部门筛选"""
        print(f"选择部门筛选: {department_name}")
        
        # 点击部门选择器
        await self.page.click('.el-select[placeholder="选择部门"]')
        await self.page.wait_for_timeout(1000)
        
        if department_name:
            # 选择特定部门
            await self.page.click(f'text={department_name}')
        else:
            # 选择"全部部门"
            await self.page.click('text=全部部门')
        
        # 等待数据刷新
        await self.page.wait_for_timeout(2000)
        await self.page.wait_for_load_state("networkidle")
        print(f"已选择部门筛选: {department_name or '全部部门'}")
    
    async def get_available_departments(self):
        """获取可用的部门选项"""
        print("获取可用部门选项...")
        
        # 点击部门选择器
        await self.page.click('.el-select[placeholder="选择部门"]')
        await self.page.wait_for_timeout(1000)
        
        # 获取部门选项
        options = await self.page.locator('.el-select-dropdown .el-option').all()
        departments = []
        
        for option in options:
            text = await option.inner_text()
            if text.strip() and text.strip() != "全部部门":
                departments.append(text.strip())
        
        # 点击其他地方关闭下拉框
        await self.page.click('body')
        await self.page.wait_for_timeout(500)
        
        print(f"可用部门: {departments}")
        return departments
    
    async def test_data_consistency(self):
        """测试数据一致性"""
        print("\n=== 开始测试数据一致性 ===")
        
        # 1. 测试无筛选时的数据
        print("\n1. 测试无筛选状态...")
        await self.select_department_filter(None)  # 选择全部部门
        
        stats_all = await self.get_statistics_data()
        list_all = await self.get_ck_list_data()
        
        print(f"全部数据 - 统计CK数: {stats_all.get('ck_count', 0)}, 列表CK数: {list_all['total_count']}")
        
        # 2. 获取可用部门并测试筛选
        departments = await self.get_available_departments()
        
        if not departments:
            print("没有可用的部门进行筛选测试")
            return False
        
        # 选择第一个部门进行测试
        test_department = departments[0]
        print(f"\n2. 测试部门筛选: {test_department}")
        
        await self.select_department_filter(test_department)
        
        stats_filtered = await self.get_statistics_data()
        list_filtered = await self.get_ck_list_data()
        
        print(f"筛选后数据 - 统计CK数: {stats_filtered.get('ck_count', 0)}, 列表CK数: {list_filtered['total_count']}")
        
        # 3. 验证数据一致性
        consistency_check = True
        
        # 检查统计数据和列表数据是否一致
        if stats_filtered.get('ck_count', 0) != list_filtered['total_count']:
            print(f"❌ 数据不一致: 统计显示{stats_filtered.get('ck_count', 0)}个CK，列表显示{list_filtered['total_count']}个CK")
            consistency_check = False
        else:
            print(f"✅ 数据一致: 统计和列表都显示{list_filtered['total_count']}个CK")
        
        # 检查列表中的CK是否都属于选择的部门
        unique_departments = set(list_filtered['departments'])
        if len(unique_departments) > 1 or (len(unique_departments) == 1 and test_department not in unique_departments):
            print(f"❌ 部门筛选失效: 列表中包含其他部门的CK: {unique_departments}")
            consistency_check = False
        else:
            print(f"✅ 部门筛选正确: 列表中只包含{test_department}的CK")
        
        return consistency_check
    
    async def test_admin_account(self):
        """测试超级管理员账号"""
        print("\n=== 测试超级管理员账号 ===")
        
        await self.login("admin", "7c222fb2927d828af22f592134e8932480637c0d")
        await self.navigate_to_ck_management()
        
        result = await self.test_data_consistency()
        return result
    
    async def test_merchant_account(self):
        """测试商户账号"""
        print("\n=== 测试商户账号 ===")
        
        # 先登出当前账号
        try:
            await self.page.click('.user-dropdown')
            await self.page.wait_for_timeout(500)
            await self.page.click('text=退出登录')
            await self.page.wait_for_timeout(1000)
        except:
            pass
        
        await self.login("test1", "********")
        await self.navigate_to_ck_management()
        
        result = await self.test_data_consistency()
        return result


async def main():
    """主测试函数"""
    test = CKDepartmentFilterTest()
    
    try:
        await test.setup()
        
        # 测试超级管理员账号
        admin_result = await test.test_admin_account()
        
        # 测试商户账号
        merchant_result = await test.test_merchant_account()
        
        # 输出测试结果
        print("\n" + "="*50)
        print("测试结果总结:")
        print(f"超级管理员账号测试: {'✅ 通过' if admin_result else '❌ 失败'}")
        print(f"商户账号测试: {'✅ 通过' if merchant_result else '❌ 失败'}")
        
        if admin_result and merchant_result:
            print("\n🎉 所有测试通过！CK管理页面的部门筛选功能已修复。")
        else:
            print("\n⚠️  部分测试失败，需要进一步检查和修复。")
        
    except Exception as e:
        print(f"测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        await test.teardown()


if __name__ == "__main__":
    asyncio.run(main())
