<template>
  <div class="system-monitoring">
    <!-- 页面头部 -->
    <div class="page-header">
      <h1 class="page-title">系统监控</h1>
      <div class="header-actions">
        <el-button type="primary" :icon="Refresh" @click="refreshAllData" :loading="refreshing">
          刷新数据
        </el-button>
        <el-button type="success" :icon="Download" @click="exportReport">
          导出报告
        </el-button>
      </div>
    </div>

    <!-- 系统概览卡片 -->
    <div class="overview-cards">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-card shadow="hover" class="overview-card">
            <div class="card-content">
              <div class="card-icon success">
                <el-icon><TrendCharts /></el-icon>
              </div>
              <div class="card-info">
                <div class="card-title">系统状态</div>
                <div class="card-value" :class="systemStatus.type">{{ systemStatus.text }}</div>
                <div class="card-desc">运行时间: {{ systemStatus.uptime }}</div>
              </div>
            </div>
          </el-card>
        </el-col>
        
        <el-col :span="6">
          <el-card shadow="hover" class="overview-card">
            <div class="card-content">
              <div class="card-icon primary">
                <el-icon><DataAnalysis /></el-icon>
              </div>
              <div class="card-info">
                <div class="card-title">API请求</div>
                <div class="card-value">{{ formatNumber(apiStats.totalRequests) }}</div>
                <div class="card-desc">今日: {{ formatNumber(apiStats.todayRequests) }}</div>
              </div>
            </div>
          </el-card>
        </el-col>
        
        <el-col :span="6">
          <el-card shadow="hover" class="overview-card">
            <div class="card-content">
              <div class="card-icon warning">
                <el-icon><Timer /></el-icon>
              </div>
              <div class="card-info">
                <div class="card-title">平均响应时间</div>
                <div class="card-value">{{ apiStats.avgResponseTime }}ms</div>
                <div class="card-desc">较昨日: {{ apiStats.responseTimeChange }}</div>
              </div>
            </div>
          </el-card>
        </el-col>
        
        <el-col :span="6">
          <el-card shadow="hover" class="overview-card">
            <div class="card-content">
              <div class="card-icon danger">
                <el-icon><Warning /></el-icon>
              </div>
              <div class="card-info">
                <div class="card-title">错误率</div>
                <div class="card-value">{{ apiStats.errorRate }}%</div>
                <div class="card-desc">较昨日: {{ apiStats.errorRateChange }}</div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 监控图表区域 -->
    <div class="charts-section">
      <el-row :gutter="20">
        <!-- API请求趋势图 -->
        <el-col :span="12">
          <el-card shadow="never" class="chart-card">
            <template #header>
              <div class="card-header">
                <span>API请求趋势</span>
                <el-select v-model="apiTrendPeriod" @change="fetchApiTrendData" style="width: 120px;">
                  <el-option label="今日" value="today" />
                  <el-option label="本周" value="week" />
                  <el-option label="本月" value="month" />
                </el-select>
              </div>
            </template>
            <div ref="apiTrendChart" class="chart-container" v-loading="apiTrendLoading"></div>
          </el-card>
        </el-col>

        <!-- 响应时间分布图 -->
        <el-col :span="12">
          <el-card shadow="never" class="chart-card">
            <template #header>
              <span>响应时间分布</span>
            </template>
            <div ref="responseTimeChart" class="chart-container" v-loading="responseTimeLoading"></div>
          </el-card>
        </el-col>
      </el-row>

      <el-row :gutter="20" style="margin-top: 20px;">
        <!-- 错误统计图 -->
        <el-col :span="12">
          <el-card shadow="never" class="chart-card">
            <template #header>
              <span>错误统计</span>
            </template>
            <div ref="errorStatsChart" class="chart-container" v-loading="errorStatsLoading"></div>
          </el-card>
        </el-col>

        <!-- 系统资源使用 -->
        <el-col :span="12">
          <el-card shadow="never" class="chart-card">
            <template #header>
              <span>系统资源使用</span>
            </template>
            <div ref="resourceChart" class="chart-container" v-loading="resourceLoading"></div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 实时监控表格 -->
    <el-card shadow="never" style="margin-top: 20px;">
      <template #header>
        <div class="card-header">
          <span>实时API监控</span>
          <div class="header-controls">
            <el-switch 
              v-model="realTimeEnabled" 
              @change="toggleRealTimeMonitoring"
              active-text="实时监控"
              inactive-text="暂停监控"
            />
            <el-button type="text" @click="clearLogs" :icon="Delete">清空日志</el-button>
          </div>
        </div>
      </template>

      <el-table :data="realtimeLogs" style="width: 100%" max-height="400">
        <el-table-column prop="timestamp" label="时间" width="180">
          <template #default="{ row }">
            {{ formatTime(row.timestamp) }}
          </template>
        </el-table-column>
        <el-table-column prop="method" label="方法" width="80">
          <template #default="{ row }">
            <el-tag :type="getMethodType(row.method)" size="small">{{ row.method }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="path" label="路径" min-width="200" />
        <el-table-column prop="status" label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="getStatusType(row.status)" size="small">{{ row.status }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="responseTime" label="响应时间" width="100">
          <template #default="{ row }">
            {{ row.responseTime }}ms
          </template>
        </el-table-column>
        <el-table-column prop="userAgent" label="用户代理" min-width="150" show-overflow-tooltip />
        <el-table-column prop="ip" label="IP地址" width="120" />
      </el-table>
    </el-card>

    <!-- 告警信息 -->
    <el-card shadow="never" style="margin-top: 20px;" v-if="alerts.length > 0">
      <template #header>
        <span>系统告警</span>
      </template>
      <div class="alerts-section">
        <el-alert
          v-for="alert in alerts"
          :key="alert.id"
          :title="alert.title"
          :description="alert.description"
          :type="alert.type"
          :closable="true"
          @close="dismissAlert(alert.id)"
          style="margin-bottom: 10px;"
        />
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, onUnmounted, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import { 
  Refresh, Download, TrendCharts, DataAnalysis, Timer, Warning, Delete 
} from '@element-plus/icons-vue'
import * as echarts from 'echarts'

// 响应式数据
const refreshing = ref(false)
const realTimeEnabled = ref(false)
const apiTrendPeriod = ref('today')

// 图表加载状态
const apiTrendLoading = ref(false)
const responseTimeLoading = ref(false)
const errorStatsLoading = ref(false)
const resourceLoading = ref(false)

// 图表引用
const apiTrendChart = ref()
const responseTimeChart = ref()
const errorStatsChart = ref()
const resourceChart = ref()

// 图表实例
let apiTrendChartInstance = null
let responseTimeChartInstance = null
let errorStatsChartInstance = null
let resourceChartInstance = null

// 实时监控
let realTimeInterval = null
const realtimeLogs = ref([])

// 系统状态数据
const systemStatus = reactive({
  type: 'success',
  text: '正常',
  uptime: '0天0小时'
})

// API统计数据
const apiStats = reactive({
  totalRequests: 0,
  todayRequests: 0,
  avgResponseTime: 0,
  responseTimeChange: '+0%',
  errorRate: 0,
  errorRateChange: '+0%'
})

// 告警信息
const alerts = ref([])

// 工具方法
const formatNumber = (num) => {
  if (num >= 1000000) {
    return (num / 1000000).toFixed(1) + 'M'
  } else if (num >= 1000) {
    return (num / 1000).toFixed(1) + 'K'
  }
  return num.toString()
}

const formatTime = (timestamp) => {
  return new Date(timestamp).toLocaleTimeString()
}

const getMethodType = (method) => {
  const types = {
    'GET': 'success',
    'POST': 'primary',
    'PUT': 'warning',
    'DELETE': 'danger',
    'PATCH': 'info'
  }
  return types[method] || 'info'
}

const getStatusType = (status) => {
  if (status >= 200 && status < 300) return 'success'
  if (status >= 300 && status < 400) return 'warning'
  if (status >= 400) return 'danger'
  return 'info'
}

// 数据获取方法
const fetchSystemStatus = async () => {
  try {
    // 模拟API调用
    const response = await new Promise(resolve => {
      setTimeout(() => {
        resolve({
          status: 'healthy',
          uptime: Math.floor(Math.random() * 30) + '天' + Math.floor(Math.random() * 24) + '小时'
        })
      }, 500)
    })

    systemStatus.type = response.status === 'healthy' ? 'success' : 'danger'
    systemStatus.text = response.status === 'healthy' ? '正常' : '异常'
    systemStatus.uptime = response.uptime
  } catch (error) {
    console.error('获取系统状态失败:', error)
  }
}

const fetchApiStats = async () => {
  try {
    // 模拟API调用
    const response = await new Promise(resolve => {
      setTimeout(() => {
        resolve({
          totalRequests: Math.floor(Math.random() * 1000000) + 500000,
          todayRequests: Math.floor(Math.random() * 10000) + 5000,
          avgResponseTime: Math.floor(Math.random() * 200) + 50,
          responseTimeChange: Math.random() > 0.5 ? '+5%' : '-3%',
          errorRate: (Math.random() * 5).toFixed(2),
          errorRateChange: Math.random() > 0.5 ? '+0.5%' : '-0.2%'
        })
      }, 500)
    })

    Object.assign(apiStats, response)
  } catch (error) {
    console.error('获取API统计失败:', error)
  }
}

const fetchApiTrendData = async () => {
  try {
    apiTrendLoading.value = true

    // 模拟API调用
    const response = await new Promise(resolve => {
      setTimeout(() => {
        const hours = []
        const requests = []
        const errors = []

        for (let i = 0; i < 24; i++) {
          hours.push(i + ':00')
          requests.push(Math.floor(Math.random() * 1000) + 100)
          errors.push(Math.floor(Math.random() * 50))
        }

        resolve({ hours, requests, errors })
      }, 1000)
    })

    // 更新图表
    if (apiTrendChartInstance) {
      apiTrendChartInstance.setOption({
        title: {
          text: 'API请求趋势',
          left: 'center'
        },
        tooltip: {
          trigger: 'axis'
        },
        legend: {
          data: ['请求数', '错误数'],
          top: 30
        },
        xAxis: {
          type: 'category',
          data: response.hours
        },
        yAxis: [
          {
            type: 'value',
            name: '请求数'
          },
          {
            type: 'value',
            name: '错误数'
          }
        ],
        series: [
          {
            name: '请求数',
            type: 'line',
            data: response.requests,
            smooth: true,
            itemStyle: { color: '#409EFF' }
          },
          {
            name: '错误数',
            type: 'line',
            yAxisIndex: 1,
            data: response.errors,
            smooth: true,
            itemStyle: { color: '#F56C6C' }
          }
        ]
      })
    }
  } catch (error) {
    console.error('获取API趋势数据失败:', error)
  } finally {
    apiTrendLoading.value = false
  }
}

const initResponseTimeChart = () => {
  if (responseTimeChartInstance) {
    responseTimeChartInstance.setOption({
      title: {
        text: '响应时间分布',
        left: 'center'
      },
      tooltip: {
        trigger: 'item'
      },
      series: [
        {
          type: 'pie',
          radius: '60%',
          data: [
            { value: 35, name: '< 100ms' },
            { value: 40, name: '100-500ms' },
            { value: 20, name: '500ms-1s' },
            { value: 5, name: '> 1s' }
          ],
          emphasis: {
            itemStyle: {
              shadowBlur: 10,
              shadowOffsetX: 0,
              shadowColor: 'rgba(0, 0, 0, 0.5)'
            }
          }
        }
      ]
    })
  }
}

const initErrorStatsChart = () => {
  if (errorStatsChartInstance) {
    errorStatsChartInstance.setOption({
      title: {
        text: '错误统计',
        left: 'center'
      },
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'shadow'
        }
      },
      xAxis: {
        type: 'category',
        data: ['400', '401', '403', '404', '500', '502', '503']
      },
      yAxis: {
        type: 'value'
      },
      series: [
        {
          type: 'bar',
          data: [120, 80, 45, 200, 30, 15, 25],
          itemStyle: {
            color: '#F56C6C'
          }
        }
      ]
    })
  }
}

const initResourceChart = () => {
  if (resourceChartInstance) {
    resourceChartInstance.setOption({
      title: {
        text: '系统资源使用',
        left: 'center'
      },
      tooltip: {
        trigger: 'axis'
      },
      radar: {
        indicator: [
          { name: 'CPU', max: 100 },
          { name: '内存', max: 100 },
          { name: '磁盘', max: 100 },
          { name: '网络', max: 100 },
          { name: '数据库', max: 100 }
        ]
      },
      series: [
        {
          type: 'radar',
          data: [
            {
              value: [65, 78, 45, 32, 58],
              name: '当前使用率'
            }
          ]
        }
      ]
    })
  }
}

// 实时监控方法
const toggleRealTimeMonitoring = (enabled) => {
  if (enabled) {
    startRealTimeMonitoring()
  } else {
    stopRealTimeMonitoring()
  }
}

const startRealTimeMonitoring = () => {
  realTimeInterval = setInterval(() => {
    // 模拟实时日志数据
    const newLog = {
      timestamp: new Date(),
      method: ['GET', 'POST', 'PUT', 'DELETE'][Math.floor(Math.random() * 4)],
      path: ['/api/v1/users', '/api/v1/roles', '/api/v1/permissions', '/api/v1/merchants'][Math.floor(Math.random() * 4)],
      status: Math.random() > 0.1 ? 200 : [400, 401, 403, 404, 500][Math.floor(Math.random() * 5)],
      responseTime: Math.floor(Math.random() * 500) + 50,
      userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
      ip: `192.168.1.${Math.floor(Math.random() * 255)}`
    }

    realtimeLogs.value.unshift(newLog)

    // 只保留最近100条记录
    if (realtimeLogs.value.length > 100) {
      realtimeLogs.value = realtimeLogs.value.slice(0, 100)
    }
  }, 2000)
}

const stopRealTimeMonitoring = () => {
  if (realTimeInterval) {
    clearInterval(realTimeInterval)
    realTimeInterval = null
  }
}

const clearLogs = () => {
  realtimeLogs.value = []
  ElMessage.success('日志已清空')
}

// 告警方法
const dismissAlert = (alertId) => {
  const index = alerts.value.findIndex(alert => alert.id === alertId)
  if (index > -1) {
    alerts.value.splice(index, 1)
  }
}

// 主要操作方法
const refreshAllData = async () => {
  refreshing.value = true
  try {
    await Promise.all([
      fetchSystemStatus(),
      fetchApiStats(),
      fetchApiTrendData()
    ])
    ElMessage.success('数据刷新成功')
  } catch (error) {
    console.error('刷新数据失败:', error)
    ElMessage.error('刷新数据失败')
  } finally {
    refreshing.value = false
  }
}

const exportReport = () => {
  const reportData = {
    timestamp: new Date().toISOString(),
    systemStatus: systemStatus,
    apiStats: apiStats,
    alerts: alerts.value
  }

  const blob = new Blob([JSON.stringify(reportData, null, 2)], { type: 'application/json' })
  const url = URL.createObjectURL(blob)
  const a = document.createElement('a')
  a.href = url
  a.download = `system-monitoring-report-${new Date().toISOString().split('T')[0]}.json`
  a.click()
  URL.revokeObjectURL(url)
  ElMessage.success('报告已导出')
}

// 生命周期
onMounted(async () => {
  await nextTick()

  // 初始化图表
  if (apiTrendChart.value) {
    apiTrendChartInstance = echarts.init(apiTrendChart.value)
  }
  if (responseTimeChart.value) {
    responseTimeChartInstance = echarts.init(responseTimeChart.value)
  }
  if (errorStatsChart.value) {
    errorStatsChartInstance = echarts.init(errorStatsChart.value)
  }
  if (resourceChart.value) {
    resourceChartInstance = echarts.init(resourceChart.value)
  }

  // 加载数据
  await refreshAllData()
  initResponseTimeChart()
  initErrorStatsChart()
  initResourceChart()

  // 模拟一些告警
  alerts.value = [
    {
      id: 1,
      title: '高错误率告警',
      description: 'API错误率超过5%，请检查系统状态',
      type: 'warning'
    },
    {
      id: 2,
      title: '响应时间告警',
      description: '平均响应时间超过500ms',
      type: 'error'
    }
  ]

  // 窗口大小变化时重新调整图表
  window.addEventListener('resize', () => {
    apiTrendChartInstance?.resize()
    responseTimeChartInstance?.resize()
    errorStatsChartInstance?.resize()
    resourceChartInstance?.resize()
  })
})

onUnmounted(() => {
  stopRealTimeMonitoring()

  // 销毁图表实例
  apiTrendChartInstance?.dispose()
  responseTimeChartInstance?.dispose()
  errorStatsChartInstance?.dispose()
  resourceChartInstance?.dispose()

  window.removeEventListener('resize', () => {})
})
</script>

<style scoped>
.system-monitoring {
  padding: 20px;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  background: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.page-title {
  font-size: 24px;
  font-weight: bold;
  margin: 0;
  color: #333;
}

.header-actions {
  display: flex;
  gap: 10px;
}

.overview-cards {
  margin-bottom: 20px;
}

.overview-card {
  height: 120px;
}

.card-content {
  display: flex;
  align-items: center;
  height: 100%;
}

.card-icon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 15px;
  font-size: 24px;
  color: white;
}

.card-icon.success {
  background: linear-gradient(135deg, #67C23A, #85CE61);
}

.card-icon.primary {
  background: linear-gradient(135deg, #409EFF, #66B1FF);
}

.card-icon.warning {
  background: linear-gradient(135deg, #E6A23C, #EEBE77);
}

.card-icon.danger {
  background: linear-gradient(135deg, #F56C6C, #F78989);
}

.card-info {
  flex: 1;
}

.card-title {
  font-size: 14px;
  color: #666;
  margin-bottom: 5px;
}

.card-value {
  font-size: 24px;
  font-weight: bold;
  margin-bottom: 5px;
}

.card-value.success {
  color: #67C23A;
}

.card-value.danger {
  color: #F56C6C;
}

.card-desc {
  font-size: 12px;
  color: #999;
}

.charts-section {
  margin-bottom: 20px;
}

.chart-card {
  height: 400px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.chart-container {
  height: 320px;
  width: 100%;
}

.header-controls {
  display: flex;
  align-items: center;
  gap: 15px;
}

.alerts-section {
  max-height: 300px;
  overflow-y: auto;
}

:deep(.el-card__body) {
  padding: 20px;
}

:deep(.el-card__header) {
  padding: 15px 20px;
  border-bottom: 1px solid #f0f0f0;
}

:deep(.el-table) {
  font-size: 12px;
}

:deep(.el-table .cell) {
  padding: 0 8px;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .overview-cards .el-col {
    margin-bottom: 20px;
  }

  .charts-section .el-col {
    margin-bottom: 20px;
  }
}

@media (max-width: 768px) {
  .system-monitoring {
    padding: 10px;
  }

  .page-header {
    flex-direction: column;
    gap: 15px;
    text-align: center;
  }

  .overview-cards .el-col {
    margin-bottom: 15px;
  }

  .chart-card {
    height: 300px;
  }

  .chart-container {
    height: 220px;
  }
}
</style>
