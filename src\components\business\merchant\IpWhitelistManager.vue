<template>
  <div class="ip-whitelist-manager">
    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <span>IP白名单管理</span>
<!--          <el-button type="primary" @click="addIp">添加IP</el-button>-->
        </div>
      </template>

      <div v-if="!loading && ipList.length === 0" class="empty-data">
        <el-empty description="暂无IP白名单"/>
      </div>

      <el-table v-else :data="ipList" style="width: 100%" v-loading="loading">
        <el-table-column prop="ip" label="IP地址"/>
        <el-table-column prop="description" label="描述"/>
        <el-table-column prop="created_at" label="添加时间">
          <template #default="scope">
            {{ formatDate(scope.row.created_at) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="150">
          <template #default="scope">
            <el-button type="primary" size="small" @click="editIp(scope.row)" text>编辑</el-button>
            <el-button type="danger" size="small" @click="removeIp(scope.row)" text>删除</el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <!-- IP编辑对话框 -->
    <el-dialog v-model="dialogVisible" :title="isEdit ? '编辑IP' : '添加IP'" width="50%">
      <el-form :model="ipForm" label-width="80px" :rules="rules" ref="ipFormRef">
        <el-form-item label="IP地址" prop="ip">
          <el-input v-model="ipForm.ip" placeholder="请输入IP地址，支持单IP和CIDR格式"/>
        </el-form-item>
        <el-form-item label="描述" prop="description">
          <el-input v-model="ipForm.description" placeholder="请输入IP用途描述"/>
        </el-form-item>
      </el-form>
      <template #footer>
                <span class="dialog-footer">
                    <el-button @click="dialogVisible = false">取消</el-button>
                    <el-button type="primary" @click="saveIp">保存</el-button>
                </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import {ref, reactive, onMounted} from 'vue'
import {ElMessage, ElMessageBox} from 'element-plus'
import {merchantApi} from '@/api/modules/merchant'
import {formatDateTime} from '@/utils/dateUtils'

const props = defineProps({
  merchantId: {
    type: [String, Number],
    required: true
  }
})

const emit = defineEmits(['update-whitelist'])

// 数据
const loading = ref(false)
const ipList = ref([])
const isEdit = ref(false)
const currentIpId = ref(null)
const dialogVisible = ref(false)
const ipFormRef = ref(null)
const ipForm = reactive({
  ip: '',
  description: ''
})

// 验证规则
const rules = {
  ip: [
    {required: true, message: '请输入IP地址', trigger: 'blur'},
    {
      pattern: /^(\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}(\/\d{1,2})?)?$/,
      message: '请输入有效的IP地址，支持CIDR格式',
      trigger: 'blur'
    }
  ],
  description: [
    {required: true, message: '请输入IP用途描述', trigger: 'blur'}
  ]
}

// 获取IP白名单列表
const fetchIpList = async () => {
  loading.value = true
  try {
    const response = await merchantApi.getIpWhitelist(props.merchantId)
    ipList.value = response.ip_list || []
  } catch (error) {
    console.error('获取IP白名单失败:', error)
    ElMessage.error('获取IP白名单失败')
  } finally {
    loading.value = false
  }
}

// 格式化日期
const formatDate = (dateString) => {
  if (!dateString) return '-'
  try {
    return formatDateTime(dateString)
  } catch (e) {
    return dateString
  }
}

// 添加IP
const addIp = () => {
  isEdit.value = false
  currentIpId.value = null
  ipForm.ip = ''
  ipForm.description = ''
  dialogVisible.value = true
}

// 编辑IP
const editIp = (row) => {
  isEdit.value = true
  currentIpId.value = row.id
  ipForm.ip = row.ip
  ipForm.description = row.description
  dialogVisible.value = true
}

// 删除IP
const removeIp = (row) => {
  ElMessageBox.confirm(`确定要删除IP ${row.ip} 吗？`, '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async () => {
    loading.value = true
    try {
      await merchantApi.deleteIpWhitelist(props.merchantId, row.id)
      ipList.value = ipList.value.filter(item => item.id !== row.id)
      ElMessage.success('删除成功')
      emit('update-whitelist', ipList.value.map(item => item.ip).join(','))
    } catch (error) {
      console.error('删除IP失败:', error)
      ElMessage.error('删除IP失败')
    } finally {
      loading.value = false
    }
  }).catch(() => {
    // 取消操作
  })
}

// 保存IP
const saveIp = async () => {
  if (!ipFormRef.value) return

  await ipFormRef.value.validate(async (valid) => {
    if (valid) {
      loading.value = true
      try {
        if (isEdit.value) {
          // 更新IP
          await merchantApi.updateIpWhitelist(props.merchantId, currentIpId.value, {
            ip: ipForm.ip,
            description: ipForm.description
          })
          const index = ipList.value.findIndex(item => item.id === currentIpId.value)
          if (index !== -1) {
            ipList.value[index] = {
              ...ipList.value[index],
              ip: ipForm.ip,
              description: ipForm.description
            }
          }
          ElMessage.success('更新成功')
        } else {
          // 添加IP
          const {data} = await merchantApi.createIpWhitelist(props.merchantId, {
            ip: ipForm.ip,
            description: ipForm.description
          })
          ipList.value.push({
            id: data.id,
            ip: ipForm.ip,
            description: ipForm.description,
            created_at: data.created_at
          })
          ElMessage.success('添加成功')
        }

        dialogVisible.value = false
        emit('update-whitelist', ipList.value.map(item => item.ip).join(','))
      } catch (error) {
        console.error('保存IP失败:', error)
        ElMessage.error('保存IP失败')
      } finally {
        loading.value = false
      }
    }
  })
}

onMounted(() => {
  fetchIpList()
})
</script>

<style scoped>
.ip-whitelist-manager {
  margin-top: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.empty-data {
  padding: 30px 0;
}
</style>