import hmac
import hashlib
import json
import base64
from curl_cffi import requests
import time
import random
import string

# 设置utf-8编码


class WalmartApiClient:
    """沃尔玛API客户端"""

    def __init__(self, base_url, encryption_key, version,sign=None):
        """初始化API客户端

        Args:
            base_url: API基础URL
            encryption_key: Base64编码的加密密钥
            token: 用户令牌
            version: API版本号
        """
        self.base_url = base_url
        self.encryption_key = encryption_key
        self.version = version
        self.sign = sign

    def _generate_nonce(self, length=10):
        """生成随机nonce字符串

        Args:
            length: nonce字符串长度

        Returns:
            随机生成的nonce字符串
        """
        return "".join(random.choice(string.ascii_lowercase) for _ in range(length))

    def _calculate_signature(self, headers, request_body):
        """计算请求签名

        Args:
            headers: 请求头部信息
            request_body: 请求体数据

        Returns:
            计算出的签名字符串
        """
        # 使用原始的base64密钥作为HMAC密钥
        key = self.encryption_key.encode("utf-8")

        # 对请求体参数进行排序，确保参数顺序一致
        sorted_body = {}
        # 按照键的字母顺序排序
        for k in sorted(request_body.keys()):
            sorted_body[k] = request_body[k]

        # 将排序后的请求体转换为JSON字符串
        body_str = json.dumps(sorted_body, separators=(",", ":"))

        # 计算HMAC-SHA256签名
        message = body_str.encode("utf-8")
        signature = hmac.new(key, message, hashlib.sha256).hexdigest().upper()

        return signature

    def make_request(self, endpoint, request_body, method="POST"):
        """发送API请求

        Args:
            endpoint: API端点路径
            request_body: 请求体数据
            method: HTTP请求方法，默认为POST

        Returns:
            API响应对象
        """
        # 构建完整URL
        url = f"{self.base_url}{endpoint}"

        # 生成请求头
        timestamp = str(int(time.time() * 1000))  # 当前时间戳（毫秒）
        nonce = self._generate_nonce()

        headers = {
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 MicroMessenger/7.0.20.1781(0x6700143B) NetType/WIFI MiniProgramEnv/Windows WindowsWechat/WMPF WindowsWechat(0x63090a13) XWEB/8555",
            "Connection": "keep-alive",
            "Accept": "*/*",
            "Accept-Encoding": "gzip, deflate, br",
            "Content-Type": "application/json",
            "xweb_xhr": "1",
            "Referer": "https://servicewechat.com/wx81d3e1fe4c2e11b4/168/page-frame.html",
            "Accept-Language": "zh-CN,zh;q=0.9"
        }

        if self.version:
            headers["version"] = self.version
        if nonce:
            headers["nonce"] = nonce
        if timestamp:
            headers["timestamp"] = timestamp

        # 计算签名并添加到请求头
        signature = self._calculate_signature(headers, request_body)
        headers["signature"] = signature

        # 发送请求
        if method.upper() == "POST":
            response = requests.post(url, headers=headers, json=request_body)
        elif method.upper() == "GET":
            response = requests.get(url, headers=headers, params=request_body)
        else:
            raise ValueError(f"不支持的HTTP方法: {method}")

        return response

    def bind_card(self, card_no, card_pwd,sign):
        """查询卡片信息

        Args:
            card_no: 卡号
            card_pwd: 卡密码

        Returns:
            API响应对象
        """
        # 构建请求体
        request_body = {
            "sign": sign,
            "storeId": "",
            "userPhone": "",
            "cardNo": card_no,
            "cardPwd": card_pwd,
            "currentPage": 0,
            "pageSize": 0,
        }

        # 发送请求
        return self.make_request("/app/card/mem/bind.json", request_body)
    
    def query_user(self,sign):
        """查询用户信息

        Returns:
            API响应对象
        """
        # 构建请求体
        request_body = {
            "currentPage": 0,
            "pageSize": 0,
            "sign": sign,
        }

        # 发送请求
        return self.make_request("/app/mem/userInfo.json", request_body)

    def query_card_list(self,sign):
        request_body = {
            "cardStatus": "A",
            "currentPage": 1,
            "pageSize": 10,
            "sign": sign
        }
        # 发送请求
        return self.make_request("/app/card/mem/pageList.json", request_body)

# 使用示例
if __name__ == "__main__":
    # 添加项目根目录到Python路径
    import sys
    import os
    sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

    # 初始化客户端（使用新的API）
    encryption_key = "Di4jB1bazHxll5M12CDsHQ=="
    version = "47"
    sign = "6b95be8d25574dad856cdf7939bfe469@61fa608e14c37c20fde47c700ec90414"

    # 使用新的WalmartAPI类
    from app.core.walmart_api import WalmartAPI

    # 方式1：使用遗留方法创建（保持兼容性）
    api_legacy = WalmartAPI.create_legacy(
        base_url="https://walcardbag.swiftpass.cn",
        encryption_key=encryption_key,
        token=None,
        version=version,
        sign=sign
    )

    # 方式2：使用新的构造函数
    api_new = WalmartAPI(
        encryption_key=encryption_key,
        version=version,
        sign=sign,
        base_url="https://walcardbag.swiftpass.cn"
    )

    # 查询卡片信息
    card_no = "2326992090536890765"
    card_pwd = "811911"

    # 注意：这里只是示例，实际运行可能会失败，因为我们使用的是示例数据
    try:
        # 使用同步方法进行测试
        # print("测试绑卡功能...")
        # response = api_new.bind_card_sync(card_no, card_pwd)
        # print(f"绑卡状态码: {response.status_code}")
        # print(f"绑卡响应内容: {response.text}")

        print("\n测试查询用户功能...")
        response = api_new.query_user_sync()
        print(f"查询用户状态码: {response.status_code}")
        print(f"查询用户响应内容: {response.text}")

        print("\n测试查询卡余额功能...")
        balance_info = api_new.get_card_balance_sync(card_no)
        print(f"卡余额信息: {balance_info}")

    except Exception as e:
        print(f"请求失败: {e}")
