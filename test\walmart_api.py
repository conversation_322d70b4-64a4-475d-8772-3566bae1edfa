import hmac
import hashlib
import json
import base64
from curl_cffi import requests
import time
import random
import string
import sys

# 设置utf-8编码
sys.stdout.reconfigure(encoding='utf-8')


class WalmartApiClient:
    """沃尔玛API客户端"""

    def __init__(self, base_url, encryption_key, token, version="45",sign=None):
        """初始化API客户端

        Args:
            base_url: API基础URL
            encryption_key: Base64编码的加密密钥
            token: 用户令牌
            version: API版本号
        """
        self.base_url = base_url
        self.encryption_key = encryption_key
        self.token = token
        self.version = version
        self.sign = sign

    def _generate_nonce(self, length=10):
        """生成随机nonce字符串

        Args:
            length: nonce字符串长度

        Returns:
            随机生成的nonce字符串
        """
        return "".join(random.choice(string.ascii_lowercase) for _ in range(length))

    def _calculate_signature(self, headers, request_body):
        """计算请求签名

        Args:
            headers: 请求头部信息
            request_body: 请求体数据

        Returns:
            计算出的签名字符串
        """
        # 使用原始的base64密钥作为HMAC密钥
        key = self.encryption_key.encode("utf-8")

        # 对请求体参数进行排序，确保参数顺序一致
        sorted_body = {}
        # 按照键的字母顺序排序
        for k in sorted(request_body.keys()):
            sorted_body[k] = request_body[k]

        # 将排序后的请求体转换为JSON字符串
        body_str = json.dumps(sorted_body, separators=(",", ":"))

        # 计算HMAC-SHA256签名
        message = body_str.encode("utf-8")
        signature = hmac.new(key, message, hashlib.sha256).hexdigest().upper()

        return signature

    def make_request(self, endpoint, request_body, method="POST"):
        """发送API请求

        Args:
            endpoint: API端点路径
            request_body: 请求体数据
            method: HTTP请求方法，默认为POST

        Returns:
            API响应对象
        """
        # 构建完整URL
        url = f"{self.base_url}{endpoint}"

        # 生成请求头
        timestamp = str(int(time.time() * 1000))  # 当前时间戳（毫秒）
        nonce = self._generate_nonce()

        headers = {
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 MicroMessenger/7.0.20.1781(0x6700143B) NetType/WIFI MiniProgramEnv/Windows WindowsWechat/WMPF WindowsWechat(0x63090a13) XWEB/8555",
            "Connection": "keep-alive",
            "Accept": "*/*",
            "Accept-Encoding": "gzip, deflate, br",
            "Content-Type": "application/json",
            "xweb_xhr": "1",
            "Referer": "https://servicewechat.com/wx81d3e1fe4c2e11b4/168/page-frame.html",
            "Accept-Language": "zh-CN,zh;q=0.9"
        }

        if self.token:
            headers["token"] = self.token
        if self.version:
            headers["version"] = self.version
        if nonce:
            headers["nonce"] = nonce
        if timestamp:
            headers["timestamp"] = timestamp

        # 计算签名并添加到请求头
        signature = self._calculate_signature(headers, request_body)
        headers["signature"] = signature

        # 发送请求
        if method.upper() == "POST":
            response = requests.post(url, headers=headers, json=request_body)
        elif method.upper() == "GET":
            response = requests.get(url, headers=headers, params=request_body)
        else:
            raise ValueError(f"不支持的HTTP方法: {method}")

        return response

    def bind_card(self, card_no, card_pwd,sign):
        """查询卡片信息

        Args:
            card_no: 卡号
            card_pwd: 卡密码

        Returns:
            API响应对象
        """
        # 构建请求体
        request_body = {
            "sign": sign,
            "storeId": "",
            "userPhone": "",
            "cardNo": card_no,
            "cardPwd": card_pwd,
            "currentPage": 0,
            "pageSize": 0,
        }

        # 发送请求
        return self.make_request("/app/card/mem/bind.json", request_body)
    
    def query_user(self,sign):
        """查询用户信息

        Returns:
            API响应对象
        """
        # 构建请求体
        request_body = {
            "currentPage": 0,
            "pageSize": 0,
            "sign": sign,
        }

        # 发送请求
        return self.make_request("/app/mem/userInfo.json", request_body)

    def query_card_list(self,sign):
        request_body = {
            "cardStatus": "A",
            "currentPage": 1,
            "pageSize": 10,
            "sign": sign
        }
        # 发送请求
        return self.make_request("/app/card/mem/pageList.json", request_body)

# 使用示例
if __name__ == "__main__":
    # 初始化客户端
    base_url = "https://apicard.swiftpass.cn"
    encryption_key = "4yyQNRI5y6YGVBACQiQBUw=="
    token = None
    version=66
    sign="c51626c504ea4743a6bccd22f4b0fede@76de0f4887b153a338728b4892640b0d"
    client = WalmartApiClient(base_url, encryption_key, token,version)

    # 查询卡片信息
    card_no = "2326992090701193061"
    card_pwd = "746589"

    # 注意：这里只是示例，实际运行可能会失败，因为我们使用的是示例数据
    try:
        #response = client.bind_card(card_no, card_pwd,sign=sign)
        # response = client.query_user(sign=sign)
        response = client.query_card_list(sign=sign)
        print(f"状态码: {response.status_code}")
        print(f"响应内容: {response.text}")
    except Exception as e:
        print(f"请求失败: {e}")
