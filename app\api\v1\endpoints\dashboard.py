from typing import Any, Dict, List, Optional
from fastapi import APIRouter, Depends, Query, HTTPException, status
from sqlalchemy.orm import Session

from app import models, schemas
from app.api import deps
from app.models.user import User
from app.schemas.response import success_response
from app.services.dashboard_service import dashboard_service
from app.services.dashboard_statistics_service import DashboardStatisticsService
from app.services.dashboard_analytics_service import DashboardAnalyticsService
from app.services.permission_service import PermissionService

router = APIRouter()


@router.get("/statistics")
async def get_dashboard_statistics(
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_active_user),
    merchant_id: Optional[int] = None,
    time_range: str = Query("today", regex="^(today|week|month)$", description="时间范围：today, week, month"),
):
    """获取仪表盘统计数据 - 重新设计版本"""
    dashboard_stats_service = DashboardStatisticsService(db)
    data = dashboard_stats_service.get_dashboard_statistics(current_user, merchant_id, time_range)
    return success_response(data=data)


@router.get("/statistics-legacy")
async def get_dashboard_statistics_legacy(
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_active_user),
    merchant_id: Optional[int] = None,
):
    """获取仪表盘统计数据 - 保留原有版本"""
    dashboard_stats_service = DashboardStatisticsService(db)
    data = dashboard_stats_service.get_dashboard_statistics_legacy(current_user, merchant_id)
    return success_response(data=data)


@router.get("/merchant-ranking")
async def get_merchant_ranking(
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_active_user),
    time_unit: str = Query("day", regex="^(day|week|month)$"),
    merchant_id: Optional[int] = None,
):
    """获取商家绑卡排名数据"""
    dashboard_stats_service = DashboardStatisticsService(db)
    data = dashboard_stats_service.get_merchant_ranking(current_user, time_unit, merchant_id)
    return success_response(data=data)


@router.get("/time-distribution")
async def get_time_distribution(
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_active_user),
    time_unit: str = Query("hour", regex="^(hour|day)$"),
    merchant_id: Optional[int] = None,
):
    """获取时间分布数据"""
    dashboard_stats_service = DashboardStatisticsService(db)
    data = dashboard_stats_service.get_time_distribution(current_user, time_unit, merchant_id)
    return success_response(data=data)


@router.get("/trend")
async def get_trend_data(
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_active_user),
    time_unit: str = Query("day", regex="^(day|week|month)$"),
    merchant_id: Optional[int] = None,
):
    """获取趋势数据"""
    dashboard_stats_service = DashboardStatisticsService(db)
    data = dashboard_stats_service.get_trend_data(current_user, time_unit, merchant_id)
    return success_response(data=data)


@router.get("/merchant-activity")
def get_merchant_activity(
    db: Session = Depends(deps.get_db),
    time_range: str = Query(
        "week", description="时间范围：today, week, month, quarter"
    ),
    sort_by: str = Query(
        "total", description="排序方式：total, successRate, avgTime, growthRate"
    ),
    current_user: User = Depends(deps.get_current_active_user),
) -> Any:
    """
    获取商户活跃度数据

    权限要求:
    - "api:dashboard:read": 仪表盘查看权限
    """
    # 权限检查
    permission_service = PermissionService(db)
    has_permission = permission_service.check_user_permission(
        current_user, "api:dashboard:read"
    )
    if not has_permission:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="没有查看仪表盘数据的权限",
        )

    dashboard_analytics_service = DashboardAnalyticsService(db)
    data = dashboard_analytics_service.get_merchant_activity(current_user, time_range, sort_by)
    return success_response(data=data)


@router.get("/bind-trend")
def get_bind_trend(
    db: Session = Depends(deps.get_db),
    time_range: str = Query("week", description="时间范围：week, month, quarter, year"),
    current_user: User = Depends(deps.get_current_active_user),
) -> Any:
    """
    获取绑卡趋势数据

    权限要求:
    - "api:dashboard:read": 仪表盘查看权限
    """
    # 权限检查
    permission_service = PermissionService(db)
    has_permission = permission_service.check_user_permission(
        current_user, "api:dashboard:read"
    )
    if not has_permission:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="没有查看仪表盘数据的权限",
        )

    dashboard_analytics_service = DashboardAnalyticsService(db)
    data = dashboard_analytics_service.get_bind_trend(current_user, time_range)
    return success_response(data=data)


@router.get("/failure-analysis")
def get_failure_analysis(
    db: Session = Depends(deps.get_db),
    time_range: str = Query("week", description="时间范围：today, week, month"),
    current_user: User = Depends(deps.get_current_active_user),
) -> Any:
    """
    获取失败分析数据

    权限要求:
    - "api:dashboard:read": 仪表盘查看权限
    """
    # 权限检查
    permission_service = PermissionService(db)
    has_permission = permission_service.check_user_permission(
        current_user, "api:dashboard:read"
    )
    if not has_permission:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="没有查看仪表盘数据的权限",
        )

    dashboard_analytics_service = DashboardAnalyticsService(db)
    data = dashboard_analytics_service.get_failure_analysis(current_user, time_range)
    return success_response(data=data)


@router.get(
    "/summary",
    response_model=schemas.DashboardSummary,
    summary="获取仪表盘摘要信息",
    dependencies=[Depends(deps.get_current_active_user)],
)
def get_dashboard_summary(
    db: Session = Depends(deps.get_db),
    current_user: models.User = Depends(deps.get_current_active_user),
    merchant_id: Optional[int] = None,
):
    """
    Retrieve dashboard summary statistics.

    权限要求:
    - "api:dashboard:read": 仪表盘查看权限
    """
    # 权限检查
    permission_service = PermissionService(db)
    has_permission = permission_service.check_user_permission(
        current_user, "api:dashboard:read"
    )
    if not has_permission:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="没有查看仪表盘数据的权限",
        )

    summary = dashboard_service.get_summary(db, current_user, merchant_id)
    return summary


@router.get("/amount-statistics")
async def get_amount_statistics(
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_active_user),
    merchant_id: Optional[int] = None,
    time_range: str = Query("today", regex="^(today|week|month)$", description="时间范围"),
):
    """获取绑卡金额统计"""
    dashboard_stats_service = DashboardStatisticsService(db)

    # 【安全修复】使用安全的商户ID获取方法
    safe_merchant_id = dashboard_stats_service._get_safe_merchant_id(current_user, merchant_id)

    # 如果没有有效的商户ID，返回空统计
    if safe_merchant_id is None:
        return success_response(data={
            "total_requests": 0,
            "success_count": 0,
            "failed_count": 0,
            "success_rate": 0.0,
            "total_amount": 0,
            "success_amount": 0
        })

    start_time, end_time = dashboard_stats_service._get_time_range(time_range)
    data = dashboard_stats_service._get_amount_statistics(safe_merchant_id, start_time, end_time)
    return success_response(data=data)


@router.get("/success-rate-statistics")
async def get_success_rate_statistics(
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_active_user),
    merchant_id: Optional[int] = None,
    time_range: str = Query("today", regex="^(today|week|month)$", description="时间范围"),
):
    """获取绑卡成功率统计"""
    dashboard_stats_service = DashboardStatisticsService(db)

    # 【安全修复】使用安全的商户ID获取方法
    safe_merchant_id = dashboard_stats_service._get_safe_merchant_id(current_user, merchant_id)

    # 如果没有有效的商户ID，返回空统计
    if safe_merchant_id is None:
        return success_response(data={
            "hourly_stats": [],
            "overall_success_rate": 0.0,
            "peak_hour": 0,
            "lowest_hour": 0
        })

    start_time, end_time = dashboard_stats_service._get_time_range(time_range)
    data = dashboard_stats_service._get_success_rate_statistics(safe_merchant_id, start_time, end_time)
    return success_response(data=data)


@router.get("/ck-efficiency-statistics")
async def get_ck_efficiency_statistics(
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_active_user),
    merchant_id: Optional[int] = None,
    time_range: str = Query("today", regex="^(today|week|month)$", description="时间范围"),
):
    """获取CK使用效率统计"""
    dashboard_stats_service = DashboardStatisticsService(db)

    # 【安全修复】使用安全的商户ID获取方法
    safe_merchant_id = dashboard_stats_service._get_safe_merchant_id(current_user, merchant_id)

    # 如果没有有效的商户ID，返回空统计
    if safe_merchant_id is None:
        return success_response(data={
            "total_ck_count": 0,
            "active_ck_count": 0,
            "total_usage": 0,
            "average_efficiency": 0.0,
            "ck_details": []
        })

    start_time, end_time = dashboard_stats_service._get_time_range(time_range)
    data = dashboard_stats_service._get_ck_efficiency_statistics(safe_merchant_id, start_time, end_time)
    return success_response(data=data)


@router.get("/failure-statistics")
async def get_failure_statistics(
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_active_user),
    merchant_id: Optional[int] = None,
    time_range: str = Query("today", regex="^(today|week|month)$", description="时间范围"),
):
    """获取异常/失败绑卡统计"""
    dashboard_stats_service = DashboardStatisticsService(db)

    # 权限检查
    if not current_user.is_platform_user():
        merchant_id = current_user.merchant_id

    start_time, end_time = dashboard_stats_service._get_time_range(time_range)
    data = dashboard_stats_service._get_failure_statistics(merchant_id, start_time, end_time)
    return success_response(data=data)


@router.get("/department-ranking")
async def get_department_ranking(
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_active_user),
    time_range: str = Query("today", regex="^(today|week|month)$", description="时间范围"),
):
    """获取部门绑卡业绩排名（仅商户管理员）"""
    dashboard_stats_service = DashboardStatisticsService(db)

    # 只有商户管理员可以查看部门排名
    if current_user.is_platform_user():
        return success_response(data=[], message="超级管理员无法查看部门排名")

    merchant_id = current_user.merchant_id
    if not merchant_id:
        return success_response(data=[], message="用户未关联商户")

    start_time, end_time = dashboard_stats_service._get_time_range(time_range)
    data = dashboard_stats_service._get_department_ranking(merchant_id, start_time, end_time)
    return success_response(data=data)
