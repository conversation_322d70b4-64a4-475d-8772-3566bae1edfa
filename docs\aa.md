绑卡流程：
┌─────────────┐ ┌─────────────┐ ┌─────────────┐
│ 绑卡请求 │ → │ 执行绑卡 │ → │ 判断结果 │
└─────────────┘ └─────────────┘ └─────────────┘
│
┌─────────────────────┼─────────────────────┐
│ │ │
┌─────▼─────┐ ┌─────▼─────┐ ┌─────▼─────┐
│ 绑卡失败 │ │ 绑卡成功 │ │ 需要重试 │
│ 不再重试 │ │ │ │ │
└─────┬─────┘ └─────┬─────┘ └─────┬─────┘
│ │ │
┌─────▼─────┐ ┌─────▼─────┐ │
│ 立即发送 │ │ 发送延迟 │ │
│ 失败回调 │ │ 消息到队列 │ │
└───────────┘ └─────┬─────┘ │
│ │
┌─────▼─────┐ │
│ 等待 10 秒后 │ │
│ 查询余额 │ │
└─────┬─────┘ │
│ │
┌─────▼─────┐ │
│ 根据余额 │ │
│ 查询结果 │ │
│ 发送回调 │ │
└───────────┘ │
│
┌─────▼─────┐
│ 不发送回调 │
│ 继续重试 │
└───────────┘
