"""
用户服务模块 - 提供用户管理的核心功能
"""

from typing import List, Optional, Dict, Any
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_
from passlib.context import CryptContext
import secrets
import logging

from app.services.base_service import BaseService
from app.models.user import User
from app.models.role import Role
from app.models.merchant import Merchant
from app.models.department import Department
from app.models.user_organization import UserOrganization
from app.schemas.user import UserCreate, UserUpdate
from app.core.security import get_password_hash, verify_password

logger = logging.getLogger(__name__)

# 密码加密上下文
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")


class UserService(BaseService[User, UserCreate, UserUpdate]):
    """用户服务类"""

    def __init__(self, db: Session):
        super().__init__(User, db)

    def get_by_username(self, username: str) -> Optional[User]:
        """
        根据用户名获取用户
        
        Args:
            username: 用户名
            
        Returns:
            Optional[User]: 用户对象或None
        """
        try:
            return self.db.query(User).filter(User.username == username).first()
        except Exception as e:
            self.logger.error(f"根据用户名获取用户失败: {e}")
            return None

    def get_by_email(self, email: str) -> Optional[User]:
        """
        根据邮箱获取用户
        
        Args:
            email: 邮箱地址
            
        Returns:
            Optional[User]: 用户对象或None
        """
        try:
            return self.db.query(User).filter(User.email == email).first()
        except Exception as e:
            self.logger.error(f"根据邮箱获取用户失败: {e}")
            return None

    def apply_data_isolation(self, query, current_user: User):
        """
        【安全修复】重写基类方法，添加强制商户隔离
        应用数据隔离规则 - 支持用户级、部门级、商户级权限，确保用户数据的商户隔离

        Args:
            query: SQLAlchemy查询对象
            current_user: 当前用户

        Returns:
            query: 应用隔离规则后的查询对象
        """
        # 【安全修复】：强制商户隔离 - 除超级管理员外，所有用户只能访问自己商户的用户数据
        if not current_user.is_superuser:
            if not current_user.merchant_id:
                # 如果用户没有商户ID，返回空结果
                self.logger.warning(f"[SECURITY] 用户 {current_user.id} 没有商户ID，拒绝用户数据访问")
                query = query.filter(User.id == -1)  # 强制返回空结果
                return query

            # 强制添加商户过滤，确保无法跨商户访问
            query = query.filter(User.merchant_id == current_user.merchant_id)
            self.logger.info(f"[SECURITY] 强制商户隔离：用户 {current_user.id} 只能访问商户 {current_user.merchant_id} 的用户数据")

        # 然后应用基类的数据隔离逻辑
        return super().apply_data_isolation(query, current_user)

    def authenticate(self, username: str, password: str) -> Optional[User]:
        """
        用户认证
        
        Args:
            username: 用户名
            password: 密码
            
        Returns:
            Optional[User]: 认证成功返回用户对象，否则返回None
        """
        try:
            user = self.get_by_username(username)
            if not user:
                return None
            
            if not verify_password(password, user.hashed_password):
                return None
            
            return user
        except Exception as e:
            self.logger.error(f"用户认证失败: {e}")
            return None

    def create_user(
        self,
        user_in: UserCreate,
        current_user: Optional[User] = None
    ) -> Optional[User]:
        """
        创建用户

        Args:
            user_in: 用户创建数据
            current_user: 当前操作用户

        Returns:
            Optional[User]: 创建的用户对象或None
        """
        try:
            # 检查用户名是否已存在
            if self.get_by_username(user_in.username):
                raise ValueError(f"用户名 {user_in.username} 已存在")

            # 检查邮箱是否已存在
            if user_in.email and self.get_by_email(user_in.email):
                raise ValueError(f"邮箱 {user_in.email} 已存在")

            # 准备用户数据（排除password和role_ids）
            user_data = user_in.model_dump(exclude={'password', 'role_ids'})
            user_data['hashed_password'] = get_password_hash(user_in.password)

            # 生成API密钥（如果需要）
            if hasattr(user_in, 'generate_api_key') and getattr(user_in, 'generate_api_key', False):
                user_data['api_key'] = self._generate_api_key()
                user_data['api_secret'] = self._generate_api_secret()

            # 创建用户
            db_user = User(**user_data)
            self.db.add(db_user)
            self.db.commit()
            self.db.refresh(db_user)

            # 分配角色（如果指定了role_ids）
            if user_in.role_ids:
                self._assign_roles_to_user(db_user.id, user_in.role_ids)

            # 创建用户组织关系（如果指定了商户和部门）
            if user_in.merchant_id:
                self._create_user_organization(
                    user_id=db_user.id,
                    merchant_id=user_in.merchant_id,
                    department_id=getattr(user_in, 'department_id', None),
                    position=getattr(user_in, 'position', None)
                )

            return db_user
        except Exception as e:
            self.db.rollback()
            self.logger.error(f"创建用户失败: {e}")
            raise

    def update_user(
        self,
        user_id: int,
        user_in: UserUpdate,
        current_user: User
    ) -> Optional[User]:
        """
        更新用户信息

        Args:
            user_id: 用户ID
            user_in: 更新数据
            current_user: 当前操作用户

        Returns:
            Optional[User]: 更新后的用户对象或None
        """
        try:
            user = self._get_user_for_update(user_id, current_user)
            self._validate_user_update_constraints(user, user_in)
            self._apply_user_updates(user, user_in)

            self.db.commit()
            self.db.refresh(user)
            return user
        except Exception as e:
            self.db.rollback()
            self.logger.error(f"更新用户失败: {e}")
            raise

    def _get_user_for_update(self, user_id: int, current_user: User) -> User:
        """获取要更新的用户"""
        user = self.get_with_isolation(user_id, current_user)
        if not user:
            raise ValueError("用户不存在或无权限访问")
        return user

    def _validate_user_update_constraints(self, user: User, user_in: UserUpdate):
        """验证用户更新约束"""
        self._check_username_uniqueness(user, user_in)
        self._check_email_uniqueness(user, user_in)

    def _check_username_uniqueness(self, user: User, user_in: UserUpdate):
        """检查用户名唯一性"""
        if user_in.username and user_in.username != user.username:
            if self.get_by_username(user_in.username):
                raise ValueError(f"用户名 {user_in.username} 已存在")

    def _check_email_uniqueness(self, user: User, user_in: UserUpdate):
        """检查邮箱唯一性"""
        if user_in.email and user_in.email != user.email:
            if self.get_by_email(user_in.email):
                raise ValueError(f"邮箱 {user_in.email} 已存在")

    def _apply_user_updates(self, user: User, user_in: UserUpdate):
        """应用用户更新"""
        update_data = user_in.model_dump(exclude_unset=True, exclude={'password', 'role_ids'})

        # 处理密码更新
        if user_in.password:
            update_data['hashed_password'] = get_password_hash(user_in.password)

        # 检查是否需要更新部门信息
        department_changed = False
        new_department_id = None
        if 'department_id' in update_data:
            new_department_id = update_data['department_id']
            if user.department_id != new_department_id:
                department_changed = True

        # 更新用户信息
        for field, value in update_data.items():
            if hasattr(user, field):
                setattr(user, field, value)

        # 处理角色更新
        if user_in.role_ids is not None:
            self._update_user_roles(user.id, user_in.role_ids)

        # 如果部门发生变化，同步更新用户组织关系表
        if department_changed:
            self._update_user_organization_department(user.id, new_department_id)

    def delete_user(self, user_id: int, current_user: User) -> bool:
        """
        删除用户
        
        Args:
            user_id: 用户ID
            current_user: 当前操作用户
            
        Returns:
            bool: 是否删除成功
        """
        try:
            # 获取用户（应用数据隔离）
            user = self.get_with_isolation(user_id, current_user)
            if not user:
                return False
            
            # 不能删除自己
            if user.id == current_user.id:
                raise ValueError("不能删除自己的账号")
            
            # 不能删除超级管理员（任何情况下都不允许）
            if user.is_superuser:
                raise ValueError("超级管理员账号不允许删除")
            
            self.db.delete(user)
            self.db.commit()
            return True
        except Exception as e:
            self.db.rollback()
            self.logger.error(f"删除用户失败: {e}")
            raise

    def assign_role(self, user_id: int, role_id: int, current_user: User) -> bool:
        """
        为用户分配角色
        
        Args:
            user_id: 用户ID
            role_id: 角色ID
            current_user: 当前操作用户
            
        Returns:
            bool: 是否分配成功
        """
        try:
            user = self.get_with_isolation(user_id, current_user)
            if not user:
                raise ValueError("用户不存在或无权限访问")
            
            role = self.db.query(Role).filter(Role.id == role_id).first()
            if not role:
                raise ValueError("角色不存在")
            
            # 检查是否已经分配了该角色
            if role not in user.roles:
                user.roles.append(role)
                self.db.commit()
            
            return True
        except Exception as e:
            self.db.rollback()
            self.logger.error(f"分配角色失败: {e}")
            raise

    def remove_role(self, user_id: int, role_id: int, current_user: User) -> bool:
        """
        移除用户角色
        
        Args:
            user_id: 用户ID
            role_id: 角色ID
            current_user: 当前操作用户
            
        Returns:
            bool: 是否移除成功
        """
        try:
            user = self.get_with_isolation(user_id, current_user)
            if not user:
                raise ValueError("用户不存在或无权限访问")
            
            role = self.db.query(Role).filter(Role.id == role_id).first()
            if not role:
                raise ValueError("角色不存在")
            
            # 移除角色
            if role in user.roles:
                user.roles.remove(role)
                self.db.commit()
            
            return True
        except Exception as e:
            self.db.rollback()
            self.logger.error(f"移除角色失败: {e}")
            raise

    def get_user_roles(self, user_id: int, current_user: User) -> List[Role]:
        """
        获取用户的角色列表
        
        Args:
            user_id: 用户ID
            current_user: 当前操作用户
            
        Returns:
            List[Role]: 角色列表
        """
        try:
            user = self.get_with_isolation(user_id, current_user)
            if not user:
                return []
            
            return list(user.roles)
        except Exception as e:
            self.logger.error(f"获取用户角色失败: {e}")
            return []

    def search_users(
        self,
        search_term: str,
        current_user: User,
        skip: int = 0,
        limit: int = 100
    ) -> List[User]:
        """
        搜索用户
        
        Args:
            search_term: 搜索关键词
            current_user: 当前用户
            skip: 跳过的记录数
            limit: 限制返回的记录数
            
        Returns:
            List[User]: 用户列表
        """
        search_fields = ['username', 'full_name', 'email', 'phone']
        return self.search(search_term, search_fields, current_user, skip, limit)

    def _generate_api_key(self) -> str:
        """生成API密钥"""
        return secrets.token_urlsafe(32)

    def _generate_api_secret(self) -> str:
        """生成API密钥密文"""
        return secrets.token_urlsafe(64)

    def _create_user_organization(
        self,
        user_id: int,
        merchant_id: int,
        department_id: Optional[int] = None,
        position: Optional[str] = None
    ) -> Optional[UserOrganization]:
        """
        创建用户组织关系
        
        Args:
            user_id: 用户ID
            merchant_id: 商户ID
            department_id: 部门ID
            position: 职位
            
        Returns:
            Optional[UserOrganization]: 创建的组织关系或None
        """
        try:
            user_org = UserOrganization(
                user_id=user_id,
                merchant_id=merchant_id,
                department_id=department_id,
                position=position,
                is_primary=True,
                status=True
            )
            self.db.add(user_org)
            self.db.commit()
            self.db.refresh(user_org)
            return user_org
        except Exception as e:
            self.db.rollback()
            self.logger.error(f"创建用户组织关系失败: {e}")
            return None

    def _update_user_organization_department(self, user_id: int, new_department_id: Optional[int]) -> None:
        """
        更新用户组织关系中的部门信息

        Args:
            user_id: 用户ID
            new_department_id: 新的部门ID，None表示清除部门关联
        """
        try:
            from app.models.user_organization import UserOrganization
            from app.models.department import Department

            # 获取用户的主要组织关系
            primary_org = (
                self.db.query(UserOrganization)
                .filter(
                    UserOrganization.user_id == user_id,
                    UserOrganization.is_primary == True,
                    UserOrganization.status == True
                )
                .first()
            )

            if new_department_id is None:
                # 清除部门关联
                if primary_org:
                    primary_org.department_id = None
                    self.logger.info(f"已清除用户 {user_id} 的部门关联")
            else:
                # 验证新部门是否存在
                new_dept = self.db.query(Department).filter(Department.id == new_department_id).first()
                if not new_dept:
                    raise ValueError(f"部门ID {new_department_id} 不存在")

                if primary_org:
                    # 更新现有组织关系的部门
                    old_dept_id = primary_org.department_id
                    primary_org.department_id = new_department_id
                    # 如果商户发生变化，也需要更新商户ID
                    if primary_org.merchant_id != new_dept.merchant_id:
                        primary_org.merchant_id = new_dept.merchant_id
                    self.logger.info(f"已将用户 {user_id} 的部门从 {old_dept_id} 更新为 {new_department_id}")
                else:
                    # 如果没有主要组织关系，创建一个新的
                    new_org = UserOrganization(
                        user_id=user_id,
                        merchant_id=new_dept.merchant_id,
                        department_id=new_department_id,
                        is_primary=True,
                        status=True
                    )
                    self.db.add(new_org)
                    self.logger.info(f"已为用户 {user_id} 创建新的组织关系，部门ID: {new_department_id}")

            # 提交更改（注意：这里不单独提交，由调用方统一提交事务）

        except Exception as e:
            self.logger.error(f"更新用户组织关系部门失败: {e}")
            raise

    def _assign_roles_to_user(self, user_id: int, role_ids: List[int]) -> None:
        """
        为用户分配角色

        Args:
            user_id: 用户ID
            role_ids: 角色ID列表
        """
        try:
            # 获取用户对象
            user = self.db.query(User).filter(User.id == user_id).first()
            if not user:
                raise ValueError(f"用户ID {user_id} 不存在")

            # 获取角色对象
            roles = self.db.query(Role).filter(Role.id.in_(role_ids)).all()
            if len(roles) != len(role_ids):
                found_ids = [role.id for role in roles]
                missing_ids = [rid for rid in role_ids if rid not in found_ids]
                raise ValueError(f"角色ID {missing_ids} 不存在")

            # 分配角色
            user.roles.extend(roles)
            self.db.commit()

        except Exception as e:
            self.logger.error(f"分配角色失败: {e}")
            raise

    def _update_user_roles(self, user_id: int, role_ids: List[int]) -> None:
        """
        更新用户角色（替换现有角色）

        Args:
            user_id: 用户ID
            role_ids: 角色ID列表
        """
        try:
            # 获取用户对象
            user = self.db.query(User).filter(User.id == user_id).first()
            if not user:
                raise ValueError(f"用户ID {user_id} 不存在")

            # 获取角色对象
            from app.models.role import Role
            roles = self.db.query(Role).filter(Role.id.in_(role_ids)).all()
            if len(roles) != len(role_ids):
                found_ids = [role.id for role in roles]
                missing_ids = [rid for rid in role_ids if rid not in found_ids]
                raise ValueError(f"角色ID {missing_ids} 不存在")

            # 替换用户角色（清空现有角色，设置新角色）
            user.roles = roles
            # 注意：这里不需要commit，因为在update_user方法中会统一commit

        except Exception as e:
            self.logger.error(f"更新用户角色失败: {e}")
            raise

    def _assign_default_role(self, user: User, current_user: Optional[User] = None) -> None:
        """
        为新用户分配默认角色

        Args:
            user: 新创建的用户对象
            current_user: 当前操作用户
        """
        try:
            from app.models.role import Role

            # 如果是超级管理员创建的用户，不分配默认角色，等待手动分配
            if current_user and current_user.is_superuser:
                self.logger.info(f"超级管理员创建用户 {user.username}，不分配默认角色")
                return

            # 根据用户的商户信息分配默认角色
            if user.merchant_id:
                # 如果有部门信息，分配CK供应商角色
                if user.department_id:
                    default_role_code = 'ck_supplier'
                    self.logger.info(f"为用户 {user.username} 分配CK供应商角色（有部门信息）")
                else:
                    # 没有部门信息，分配商户管理员角色
                    default_role_code = 'merchant_admin'
                    self.logger.info(f"为用户 {user.username} 分配商户管理员角色（无部门信息）")

                # 查找默认角色
                default_role = self.db.query(Role).filter(Role.code == default_role_code).first()
                if default_role:
                    user.roles.append(default_role)
                    self.db.commit()
                    self.logger.info(f"成功为用户 {user.username} 分配默认角色: {default_role_code}")
                else:
                    self.logger.warning(f"未找到默认角色: {default_role_code}")
            else:
                self.logger.info(f"用户 {user.username} 没有商户信息，不分配默认角色")

        except Exception as e:
            self.logger.error(f"分配默认角色失败: {e}")
            # 不抛出异常，因为这不应该阻止用户创建
