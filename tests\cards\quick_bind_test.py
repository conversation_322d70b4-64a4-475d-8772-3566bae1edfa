#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
绑卡功能快速测试
用于快速验证绑卡系统的基本功能是否正常
"""

import sys
import os
import time
import json
import uuid
import hmac
import hashlib
import base64
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.dirname(__file__))))

from test.conftest import TestBase, get_test_accounts


class QuickBindTest(TestBase):
    """绑卡功能快速测试类"""
    
    def __init__(self):
        super().__init__()
        self.test_accounts = get_test_accounts()
        self.admin_token = None
        self.merchant_token = None
        self.test_merchant = None
        
    def setup(self):
        """设置测试环境"""
        print("🔧 设置测试环境...")
        
        # 登录管理员账号
        self.admin_token = self.login(
            self.test_accounts["super_admin"]["username"],
            self.test_accounts["super_admin"]["password"]
        )
        
        if not self.admin_token:
            raise Exception("❌ 无法登录管理员账号")
        
        print("✅ 管理员登录成功")
        
        # 获取测试商户信息
        self._get_test_merchant()
        
        if not self.test_merchant:
            raise Exception("❌ 无法获取测试商户信息")
            
        print(f"✅ 获取测试商户: {self.test_merchant.get('name')}")

    def _get_test_merchant(self):
        """获取测试商户信息"""
        try:
            status_code, response = self.make_request("GET", "/merchants", self.admin_token)
            if status_code == 200:
                merchants_data = response.get("data", response)
                if isinstance(merchants_data, dict):
                    merchants = merchants_data.get("items", [])
                elif isinstance(merchants_data, list):
                    merchants = merchants_data
                else:
                    merchants = []

                if merchants and len(merchants) > 0:
                    # 找到测试商户或使用第一个
                    for merchant in merchants:
                        if "test" in merchant.get("name", "").lower() or merchant.get("code") == "TEST_MERCHANT":
                            self.test_merchant = merchant
                            break
                    
                    if not self.test_merchant:
                        self.test_merchant = merchants[0]
                        
        except Exception as e:
            print(f"获取商户信息失败: {e}")

    def _generate_signature(self, data: dict, secret: str, timestamp: str, nonce: str) -> str:
        """生成API签名"""
        try:
            # 1. 对数据按键排序
            sorted_data = dict(sorted(data.items()))
            
            # 2. 构建签名字符串
            method = "POST"
            path = "/api/v1/card-bind"
            params_str = "&".join([f"{k}={v}" for k, v in sorted_data.items()])
            sign_string = f"{method}|{path}|{timestamp}|{nonce}|{params_str}"
            
            # 3. 使用HMAC-SHA256生成签名并Base64编码
            hmac_digest = hmac.new(
                secret.encode('utf-8'),
                sign_string.encode('utf-8'),
                hashlib.sha256
            ).digest()
            signature = base64.b64encode(hmac_digest).decode('utf-8')
            
            return signature
            
        except Exception as e:
            print(f"生成签名失败: {e}")
            return ""

    def test_basic_connectivity(self):
        """测试基本连接性"""
        print("\n🔗 测试基本连接性...")
        
        # 测试API基本连接
        status_code, response = self.make_request("GET", "/", None)
        
        if status_code in [200, 404]:  # 200或404都表示连接正常
            print("✅ API服务连接正常")
            return True
        else:
            print(f"❌ API服务连接失败，状态码: {status_code}")
            return False

    def test_authentication(self):
        """测试认证功能"""
        print("\n🔐 测试认证功能...")
        
        if self.admin_token:
            # 测试token有效性
            status_code, response = self.make_request("GET", "/auth/me", self.admin_token)
            
            if status_code == 200:
                user_info = response.get("data", {})
                print(f"✅ 认证成功，用户: {user_info.get('username', 'N/A')}")
                return True
            else:
                print(f"❌ 认证失败，状态码: {status_code}")
                return False
        else:
            print("❌ 无法获取认证token")
            return False

    def test_merchant_info(self):
        """测试商户信息获取"""
        print("\n🏢 测试商户信息获取...")
        
        status_code, response = self.make_request("GET", "/merchants", self.admin_token)
        
        if status_code == 200:
            merchants_data = response.get("data", response)
            if isinstance(merchants_data, dict):
                merchants = merchants_data.get("items", [])
            elif isinstance(merchants_data, list):
                merchants = merchants_data
            else:
                merchants = []
                
            print(f"✅ 成功获取 {len(merchants)} 个商户")
            return len(merchants) > 0
        else:
            print(f"❌ 获取商户信息失败，状态码: {status_code}")
            return False

    def test_card_records_access(self):
        """测试绑卡记录访问"""
        print("\n📋 测试绑卡记录访问...")
        
        params = {"page": 1, "size": 5}
        if self.test_merchant:
            params["merchant_id"] = self.test_merchant.get("id")
            
        status_code, response = self.make_request("GET", "/cards", self.admin_token, params=params)
        
        if status_code == 200:
            data = response.get("data", {})
            if isinstance(data, dict):
                total = data.get("total", 0)
                items = data.get("items", [])
            else:
                total = len(response.get("data", []))
                items = response.get("data", [])
                
            print(f"✅ 成功访问绑卡记录，总计 {total} 条，当前页 {len(items)} 条")
            return True
        else:
            print(f"❌ 访问绑卡记录失败，状态码: {status_code}")
            return False

    def test_bind_card_api(self):
        """测试绑卡API"""
        print("\n🔗 测试绑卡API...")
        
        if not self.test_merchant:
            print("❌ 缺少测试商户信息")
            return False
            
        # 构建测试数据
        card_data = {
            "card_number": f"*************{int(time.time()) % 1000:03d}",
            "card_password": "123456",
            "merchant_code": self.test_merchant.get("code", "TEST_MERCHANT"),
            "merchant_order_id": f"QUICK_TEST_{int(time.time())}",
            "amount": 10000,
            "ext_data": "quick_test"
        }
        
        # 生成签名
        api_key = self.test_merchant.get("api_key", "test_api_key")
        api_secret = self.test_merchant.get("api_secret", "test_secret")
        timestamp = str(int(time.time() * 1000))
        nonce = str(uuid.uuid4()).replace("-", "")[:16]
        signature = self._generate_signature(card_data, api_secret, timestamp, nonce)
        
        # 构建请求头
        headers = {
            "Content-Type": "application/json",
            "api-key": api_key,
            "X-Timestamp": timestamp,
            "X-Nonce": nonce,
            "X-Signature": signature
        }
        
        # 发送请求
        url = f"{self.base_url}{self.api_prefix}/card-bind"
        try:
            response = self.session.post(
                url,
                json=card_data,
                headers=headers,
                timeout=self.timeout
            )
            
            if response.status_code == 200:
                response_data = response.json()
                print(f"✅ 绑卡API调用成功")
                print(f"   📋 记录ID: {response_data.get('recordId', 'N/A')}")
                print(f"   📋 状态: {response_data.get('status', 'N/A')}")
                return True
            else:
                try:
                    error_data = response.json()
                    error_msg = error_data.get('detail', error_data.get('message', 'Unknown error'))
                except:
                    error_msg = response.text
                    
                print(f"❌ 绑卡API调用失败，状态码: {response.status_code}")
                print(f"   错误信息: {error_msg}")
                return False
                
        except Exception as e:
            print(f"❌ 绑卡API调用异常: {e}")
            return False

    def run_quick_test(self):
        """运行快速测试"""
        print("🚀 开始绑卡功能快速测试")
        print("="*60)
        
        start_time = time.time()
        test_results = []
        
        try:
            # 设置测试环境
            self.setup()
            
            # 运行测试
            tests = [
                ("基本连接性", self.test_basic_connectivity),
                ("认证功能", self.test_authentication),
                ("商户信息获取", self.test_merchant_info),
                ("绑卡记录访问", self.test_card_records_access),
                ("绑卡API", self.test_bind_card_api),
            ]
            
            for test_name, test_func in tests:
                try:
                    result = test_func()
                    test_results.append((test_name, result))
                except Exception as e:
                    print(f"❌ {test_name} 测试异常: {e}")
                    test_results.append((test_name, False))
            
        except Exception as e:
            print(f"❌ 测试环境设置失败: {e}")
            return False
        
        # 统计结果
        end_time = time.time()
        duration = end_time - start_time
        
        total_tests = len(test_results)
        passed_tests = sum(1 for _, result in test_results if result)
        failed_tests = total_tests - passed_tests
        success_rate = (passed_tests / total_tests * 100) if total_tests > 0 else 0
        
        # 打印结果
        print(f"\n{'='*60}")
        print("📊 快速测试结果")
        print(f"{'='*60}")
        print(f"总测试数: {total_tests}")
        print(f"通过数: {passed_tests}")
        print(f"失败数: {failed_tests}")
        print(f"成功率: {success_rate:.1f}%")
        print(f"耗时: {duration:.2f}秒")
        
        print(f"\n📋 详细结果:")
        for test_name, result in test_results:
            status = "✅ 通过" if result else "❌ 失败"
            print(f"   {status} {test_name}")
        
        # 给出建议
        print(f"\n💡 测试建议:")
        if success_rate == 100:
            print("   🎉 所有测试通过！绑卡系统运行正常。")
        elif success_rate >= 80:
            print("   👍 大部分功能正常，但有部分问题需要关注。")
        else:
            print("   ⚠️ 发现多个问题，建议检查系统配置。")
            print("   🔧 请运行完整测试套件进行详细诊断。")
        
        return success_rate == 100


def main():
    """主函数"""
    test = QuickBindTest()
    success = test.run_quick_test()
    
    if success:
        print(f"\n🎉 快速测试全部通过！")
        return 0
    else:
        print(f"\n⚠️ 快速测试发现问题，建议运行完整测试")
        return 1


if __name__ == "__main__":
    sys.exit(main())
