from typing import Any, Generic, List, Optional, TypeVar, Union, Callable
from pydantic import BaseModel, Field
from fastapi import status, Request
from fastapi.responses import JSONResponse
from starlette.middleware.base import BaseHTTPMiddleware, RequestResponseEndpoint
from starlette.responses import Response
from starlette.types import ASGIApp

# 定义泛型类型参数
T = TypeVar("T")


class ResponseCode:
    """统一响应码常量类"""

    SUCCESS = 0
    FAILED = 1
    INVALID_PARAMS = 400
    UNAUTHORIZED = 401
    FORBIDDEN = 403
    NOT_FOUND = 404
    SERVER_ERROR = 500


class ResponseMessage:
    """统一响应消息"""

    SUCCESS = "操作成功"
    FAILED = "操作失败"
    INVALID_PARAMS = "无效的参数"
    UNAUTHORIZED = "未授权"
    FORBIDDEN = "禁止访问"
    NOT_FOUND = "资源不存在"
    SERVER_ERROR = "服务器内部错误"


class BaseResponse(BaseModel, Generic[T]):
    """统一API响应格式

    Attributes:
        code: 响应码，0表示成功，其他值表示错误
        data: 响应数据
        message: 响应消息
    """

    code: int = Field(
        ResponseCode.SUCCESS, description="响应码，0表示成功，其他值表示错误"
    )
    data: Optional[T] = Field(None, description="响应数据")
    message: str = Field(ResponseMessage.SUCCESS, description="响应消息")

    class Config:
        """Pydantic配置类"""

        arbitrary_types_allowed = True


class MessageResponse(BaseModel):
    """简单消息响应"""
    message: str = Field(..., description="响应消息")

    class Config:
        from_attributes = True


class PageInfo(BaseModel):
    """分页信息

    Attributes:
        total: 总记录数
        page: 当前页码
        page_size: 每页记录数
    """

    total: int = Field(..., description="总记录数")
    page: int = Field(..., description="当前页码")
    page_size: int = Field(..., description="每页记录数")


class PageData(BaseModel, Generic[T]):
    """分页数据结构

    Attributes:
        items: 分页数据列表
        total: 总记录数
        page: 当前页码
        page_size: 每页记录数
        pages: 总页数
    """

    items: List[T] = Field([], description="分页数据列表")
    total: int = Field(..., description="总记录数")
    page: int = Field(..., description="当前页码")
    page_size: int = Field(..., description="每页记录数")
    pages: int = Field(..., description="总页数")

    class Config:
        """Pydantic配置类"""
        arbitrary_types_allowed = True
        extra = "allow"  # 允许额外字段


class PageResponse(BaseModel, Generic[T]):
    """分页数据响应格式

    Attributes:
        code: 响应码，0表示成功，其他值表示错误
        data: 分页数据对象
        message: 响应消息
    """

    code: int = Field(
        ResponseCode.SUCCESS, description="响应码，0表示成功，其他值表示错误"
    )
    data: PageData[T] = Field(..., description="分页数据对象")
    message: str = Field(ResponseMessage.SUCCESS, description="响应消息")

    class Config:
        """Pydantic配置类"""

        arbitrary_types_allowed = True


class CustomJSONResponse(JSONResponse):
    """自定义JSON响应类，用于统一处理响应格式"""

    def render(self, content: Any) -> bytes:
        """重写渲染方法，统一处理响应格式"""
        if isinstance(content, (BaseResponse, PageResponse)):
            return super().render(content.dict())

        # 如果是分页数据（字典格式）
        if isinstance(content, dict) and all(
            key in content for key in ["items", "total", "page", "page_size"]
        ):
            # 计算总页数
            pages = (content["total"] + content["page_size"] - 1) // content["page_size"]

            # 创建分页数据，包含额外字段
            page_data_dict = {
                "items": content["items"],
                "total": content["total"],
                "page": content["page"],
                "page_size": content["page_size"],
                "pages": pages
            }

            # 添加额外字段（如unread_count）
            for key, value in content.items():
                if key not in ["items", "total", "page", "page_size", "pages"]:
                    page_data_dict[key] = value

            page_data = PageData(**page_data_dict)

            response = PageResponse(
                code=ResponseCode.SUCCESS,
                data=page_data,
                message=ResponseMessage.SUCCESS,
            )
            return super().render(response.dict())

        # 普通响应
        response = BaseResponse(
            code=ResponseCode.SUCCESS, data=content, message=ResponseMessage.SUCCESS
        )
        return super().render(response.dict())


# 修改中间件实现
def create_response_middleware(app: ASGIApp) -> Callable:
    """创建响应中间件

    Args:
        app: ASGI应用实例

    Returns:
        中间件处理函数
    """

    async def response_middleware(
        request: Request, call_next: RequestResponseEndpoint
    ) -> Response:
        """处理请求和响应"""
        try:
            response = await call_next(request)

            # 如果响应已经是 CustomJSONResponse，直接返回
            if isinstance(response, CustomJSONResponse):
                return response

            # 如果是其他类型的响应（如 FileResponse），直接返回
            if not isinstance(response, JSONResponse):
                return response

            # 转换为自定义响应格式
            return CustomJSONResponse(
                content=response.body,
                status_code=response.status_code,
                headers=dict(response.headers),
                media_type=response.media_type,
            )

        except Exception as e:
            # 处理异常情况
            return CustomJSONResponse(
                content=BaseResponse(
                    code=ResponseCode.SERVER_ERROR, message=str(e), data=None
                ).dict(),
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

    return response_middleware


# 工具函数
def wrap_response(data: Any, message: str = ResponseMessage.SUCCESS) -> BaseResponse:
    """包装响应数据"""
    return BaseResponse(code=ResponseCode.SUCCESS, data=data, message=message)


def wrap_page_response(
    items: List[Any],
    total: int,
    page: int = 1,
    page_size: int = 10,
    message: str = ResponseMessage.SUCCESS,
) -> PageResponse:
    """包装分页响应数据"""
    pages = (total + page_size - 1) // page_size
    page_data = PageData(
        items=items,
        total=total,
        page=page,
        page_size=page_size,
        pages=pages
    )
    return PageResponse(
        code=ResponseCode.SUCCESS,
        data=page_data,
        message=message,
    )


def wrap_error_response(
    code: int = ResponseCode.FAILED,
    message: str = ResponseMessage.FAILED,
    data: Any = None,
) -> BaseResponse:
    """包装错误响应数据"""
    return BaseResponse(code=code, data=data, message=message)


class ResponseFactory:
    """响应工厂类"""

    @staticmethod
    def success(
        data: Any = None, message: str = ResponseMessage.SUCCESS
    ) -> BaseResponse:
        """生成成功响应

        Args:
            data: 响应数据
            message: 响应消息

        Returns:
            统一格式的成功响应
        """
        return BaseResponse(code=ResponseCode.SUCCESS, data=data, message=message)

    @staticmethod
    def error(
        code: int = ResponseCode.FAILED,
        message: str = ResponseMessage.FAILED,
        data: Any = None,
    ) -> BaseResponse:
        """生成错误响应

        Args:
            code: 错误码
            message: 错误消息
            data: 错误数据

        Returns:
            统一格式的错误响应
        """
        return BaseResponse(code=code, data=data, message=message)

    @staticmethod
    def page(
        data: List[Any],
        total: int,
        page: int = 1,
        page_size: int = 10,
        message: str = "获取分页数据成功",
    ) -> PageResponse:
        """生成分页响应

        Args:
            data: 分页数据列表
            total: 总记录数
            page: 当前页码
            page_size: 每页大小
            message: 响应消息

        Returns:
            统一格式的分页响应
        """
        pages = (total + page_size - 1) // page_size
        page_data = PageData(
            items=data,
            total=total,
            page=page,
            page_size=page_size,
            pages=pages
        )
        return PageResponse(
            code=ResponseCode.SUCCESS,
            data=page_data,
            message=message,
        )


# 快捷方法
def success_response(
    data: Any = None, message: str = ResponseMessage.SUCCESS
) -> BaseResponse:
    """快捷方法：生成成功响应"""
    return ResponseFactory.success(data, message)


def error_response(
    code: int = ResponseCode.FAILED,
    message: str = ResponseMessage.FAILED,
    data: Any = None,
) -> BaseResponse:
    """快捷方法：生成错误响应"""
    return ResponseFactory.error(code, message, data)


def page_response(
    data: List[Any],
    total: int,
    page: int = 1,
    page_size: int = 10,
    message: str = "获取分页数据成功",
) -> PageResponse:
    """快捷方法：生成分页响应"""
    return ResponseFactory.page(data, total, page, page_size, message)
