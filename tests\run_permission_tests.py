#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
权限管理模块综合测试脚本
测试角色、权限、菜单管理的所有API端点
"""

import sys
import os
import time
import subprocess
import json
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(__file__)))

def run_test_module(module_path, module_name):
    """运行单个测试模块"""
    print(f"\n{'='*60}")
    print(f"运行测试模块: {module_name}")
    print(f"{'='*60}")
    
    try:
        # 使用pytest运行测试
        result = subprocess.run([
            sys.executable, "-m", "pytest", 
            module_path, 
            "-v", 
            "--tb=short",
            "--no-header"
        ], capture_output=True, text=True, cwd=os.path.dirname(os.path.dirname(__file__)))
        
        print(f"测试输出:")
        print(result.stdout)
        
        if result.stderr:
            print(f"错误输出:")
            print(result.stderr)
        
        return result.returncode == 0
        
    except Exception as e:
        print(f"运行测试模块 {module_name} 时出错: {e}")
        return False

def main():
    """主函数"""
    print("沃尔玛绑卡系统 - 权限管理模块综合测试")
    print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 测试模块列表
    test_modules = [
        ("test/permissions/test_permissions_crud.py", "权限管理CRUD测试"),
        ("test/menus/test_menus_crud.py", "菜单管理CRUD测试"),
        ("test/roles/test_role_management_crud.py", "角色管理CRUD测试"),
        ("test/roles/test_roles_permissions.py", "角色权限综合测试"),
    ]
    
    results = {}
    total_tests = len(test_modules)
    passed_tests = 0
    
    # 运行所有测试模块
    for module_path, module_name in test_modules:
        if os.path.exists(module_path):
            success = run_test_module(module_path, module_name)
            results[module_name] = success
            if success:
                passed_tests += 1
        else:
            print(f"⚠️ 测试文件不存在: {module_path}")
            results[module_name] = False
    
    # 输出测试总结
    print(f"\n{'='*60}")
    print("测试总结")
    print(f"{'='*60}")
    
    for module_name, success in results.items():
        status = "✅ 通过" if success else "❌ 失败"
        print(f"{module_name}: {status}")
    
    print(f"\n总计: {passed_tests}/{total_tests} 个测试模块通过")
    
    if passed_tests == total_tests:
        print("🎉 所有权限管理测试通过！")
        return 0
    else:
        print("⚠️ 部分测试失败，请检查上述输出")
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
