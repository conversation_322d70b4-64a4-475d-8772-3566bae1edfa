<template>
  <div class="test-container">
    <!-- 测试导航 -->
    <div class="test-header">
      <h1>🧪 对账台UI测试页面</h1>
      <p>点击下方按钮测试不同的对账台页面</p>
    </div>
    
    <!-- 测试按钮 -->
    <div class="test-buttons">
      <el-button type="primary" size="large" @click="goToMain">
        <el-icon><Money /></el-icon>
        主对账台
      </el-button>
      
      <el-button type="success" size="large" @click="goToCkDetails">
        <el-icon><Key /></el-icon>
        CK明细统计
      </el-button>
      
      <el-button type="warning" size="large" @click="goToRecords">
        <el-icon><Document /></el-icon>
        绑卡记录详情
      </el-button>
    </div>
    
    <!-- 当前页面展示 -->
    <div class="current-page" v-if="currentComponent">
      <div class="page-header">
        <h2>{{ currentPageTitle }}</h2>
        <el-button @click="currentComponent = null">关闭预览</el-button>
      </div>
      <component :is="currentComponent" />
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { ElMessage } from 'element-plus'
import { Money, Key, Document } from '@element-plus/icons-vue'

// 动态导入组件
import ReconciliationMain from './reconciliation/index.vue'
import ReconciliationCkDetails from './reconciliation/ck-details.vue'
import ReconciliationRecords from './reconciliation/records.vue'

const currentComponent = ref(null)
const currentPageTitle = ref('')

const goToMain = () => {
  currentComponent.value = ReconciliationMain
  currentPageTitle.value = '主对账台'
  ElMessage.success('已加载主对账台页面')
}

const goToCkDetails = () => {
  currentComponent.value = ReconciliationCkDetails
  currentPageTitle.value = 'CK明细统计'
  ElMessage.success('已加载CK明细统计页面')
}

const goToRecords = () => {
  currentComponent.value = ReconciliationRecords
  currentPageTitle.value = '绑卡记录详情'
  ElMessage.success('已加载绑卡记录详情页面')
}
</script>

<style scoped>
.test-container {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.test-header {
  text-align: center;
  margin-bottom: 40px;
  padding: 30px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 12px;
  color: white;
}

.test-header h1 {
  margin: 0 0 10px 0;
  font-size: 28px;
}

.test-header p {
  margin: 0;
  opacity: 0.9;
}

.test-buttons {
  display: flex;
  justify-content: center;
  gap: 20px;
  margin-bottom: 40px;
  flex-wrap: wrap;
}

.test-buttons .el-button {
  padding: 15px 30px;
  font-size: 16px;
}

.current-page {
  border: 2px solid #e4e7ed;
  border-radius: 8px;
  overflow: hidden;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 20px;
  background: #f5f7fa;
  border-bottom: 1px solid #e4e7ed;
}

.page-header h2 {
  margin: 0;
  color: #303133;
}

@media (max-width: 768px) {
  .test-buttons {
    flex-direction: column;
    align-items: center;
  }
  
  .test-buttons .el-button {
    width: 200px;
  }
}
</style>
