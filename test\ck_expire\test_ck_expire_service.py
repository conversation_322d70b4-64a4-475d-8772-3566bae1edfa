"""
CK自动过期管理服务测试用例

测试内容：
1. CK过期检测逻辑
2. 配置管理功能
3. 权限控制机制
4. 软删除机制验证
5. 定时任务集成
"""

import pytest
from datetime import datetime, timedelta
from sqlalchemy.orm import Session
from unittest.mock import patch, MagicMock

from app.services.ck_expire_service import CKExpireService, create_ck_expire_service
from app.models.walmart_ck import WalmartCK
from app.models.system_settings import SystemSettings
from app.crud.system_settings import system_settings
from app.db.session import SessionLocal


class TestCKExpireService:
    """CK过期服务测试类"""

    @pytest.fixture
    def db_session(self):
        """创建测试数据库会话"""
        db = SessionLocal()
        try:
            yield db
        finally:
            db.close()

    @pytest.fixture
    def ck_expire_service(self, db_session):
        """创建CK过期服务实例"""
        return create_ck_expire_service(db_session)

    @pytest.fixture
    def test_ck_data(self, db_session):
        """创建测试CK数据"""
        # 创建过期的CK
        expired_ck = WalmartCK(
            sign="expired_ck_test_sign",
            daily_limit=20,
            hourly_limit=20,
            merchant_id=1,
            department_id=1,
            active=True,
            is_deleted=False,
            created_at=datetime.now() - timedelta(minutes=60)  # 1小时前创建
        )
        
        # 创建未过期的CK
        active_ck = WalmartCK(
            sign="active_ck_test_sign",
            daily_limit=20,
            hourly_limit=20,
            merchant_id=1,
            department_id=1,
            active=True,
            is_deleted=False,
            created_at=datetime.now() - timedelta(minutes=10)  # 10分钟前创建
        )
        
        db_session.add(expired_ck)
        db_session.add(active_ck)
        db_session.commit()
        
        return {
            'expired_ck': expired_ck,
            'active_ck': active_ck
        }

    def test_get_expire_config_default(self, ck_expire_service):
        """测试获取默认过期配置"""
        config = ck_expire_service.get_expire_config()
        
        assert config is not None
        assert 'expire_minutes' in config
        assert 'check_enabled' in config
        assert 'check_interval' in config
        
        # 验证默认值
        assert config['expire_minutes'] == 30
        assert config['check_enabled'] is True
        assert config['check_interval'] == 5

    def test_get_expire_config_from_db(self, ck_expire_service, db_session):
        """测试从数据库获取过期配置"""
        # 设置测试配置
        system_settings.set_value(db_session, 'ck_expire_minutes', '60')
        system_settings.set_value(db_session, 'ck_expire_check_enabled', 'false')
        system_settings.set_value(db_session, 'ck_expire_check_interval', '10')
        
        config = ck_expire_service.get_expire_config()
        
        assert config['expire_minutes'] == 60
        assert config['check_enabled'] is False
        assert config['check_interval'] == 10

    def test_find_expired_cks(self, ck_expire_service, test_ck_data, db_session):
        """测试查找过期CK"""
        # 设置30分钟过期时间
        system_settings.set_value(db_session, 'ck_expire_minutes', '30')
        
        expired_cks = ck_expire_service.find_expired_cks()
        
        # 应该找到1个过期的CK（1小时前创建的）
        assert len(expired_cks) == 1
        assert expired_cks[0].sign == "expired_ck_test_sign"

    def test_find_expired_cks_disabled(self, ck_expire_service, test_ck_data, db_session):
        """测试禁用过期检测时的行为"""
        # 禁用过期检测
        system_settings.set_value(db_session, 'ck_expire_check_enabled', 'false')
        
        expired_cks = ck_expire_service.find_expired_cks()
        
        # 应该返回空列表
        assert len(expired_cks) == 0

    def test_expire_ck(self, ck_expire_service, test_ck_data, db_session):
        """测试CK过期处理"""
        ck = test_ck_data['active_ck']
        
        # 验证初始状态
        assert ck.active is True
        assert ck.is_deleted is False
        
        # 执行过期处理
        result = ck_expire_service.expire_ck(ck, "测试过期")
        
        assert result is True
        
        # 验证过期后状态
        db_session.refresh(ck)
        assert ck.active is False
        assert ck.is_deleted is True

    def test_batch_expire_cks(self, ck_expire_service, test_ck_data, db_session):
        """测试批量过期处理"""
        # 设置30分钟过期时间
        system_settings.set_value(db_session, 'ck_expire_minutes', '30')
        
        result = ck_expire_service.batch_expire_cks()
        
        assert result['total_found'] == 1
        assert result['total_processed'] == 1
        assert result['total_failed'] == 0
        
        # 验证过期的CK状态
        expired_ck = test_ck_data['expired_ck']
        db_session.refresh(expired_ck)
        assert expired_ck.active is False
        assert expired_ck.is_deleted is True

    def test_batch_expire_cks_merchant_filter(self, ck_expire_service, test_ck_data, db_session):
        """测试按商户过滤的批量过期处理"""
        # 设置30分钟过期时间
        system_settings.set_value(db_session, 'ck_expire_minutes', '30')
        
        # 只处理商户1的CK
        result = ck_expire_service.batch_expire_cks(merchant_id=1)
        
        assert result['total_found'] == 1
        assert result['total_processed'] == 1
        
        # 处理不存在的商户
        result = ck_expire_service.batch_expire_cks(merchant_id=999)
        
        assert result['total_found'] == 0
        assert result['total_processed'] == 0

    def test_get_expire_statistics(self, ck_expire_service, test_ck_data, db_session):
        """测试获取过期统计信息"""
        # 设置30分钟过期时间
        system_settings.set_value(db_session, 'ck_expire_minutes', '30')
        
        stats = ck_expire_service.get_expire_statistics()
        
        assert 'config' in stats
        assert 'statistics' in stats
        assert 'expire_threshold' in stats
        
        # 验证统计数据
        statistics = stats['statistics']
        assert statistics['total_count'] == 2  # 总共2个CK
        assert statistics['active_count'] == 2  # 都是活跃的
        assert statistics['expired_count'] == 1  # 1个过期
        assert statistics['deleted_count'] == 0  # 还没有删除的

    def test_expire_ck_already_deleted(self, ck_expire_service, test_ck_data):
        """测试处理已删除的CK"""
        ck = test_ck_data['active_ck']
        
        # 先标记为已删除
        ck.is_deleted = True
        
        # 尝试再次过期处理
        result = ck_expire_service.expire_ck(ck, "测试重复过期")
        
        # 应该仍然成功
        assert result is True

    def test_error_handling(self, db_session):
        """测试错误处理"""
        # 使用无效的数据库会话测试错误处理
        with patch.object(db_session, 'query', side_effect=Exception("数据库错误")):
            service = CKExpireService(db_session)
            
            # 测试查找过期CK的错误处理
            expired_cks = service.find_expired_cks()
            assert len(expired_cks) == 0
            
            # 测试批量处理的错误处理
            result = service.batch_expire_cks()
            assert 'error' in result
            assert result['total_processed'] == 0


class TestCKExpireServiceIntegration:
    """CK过期服务集成测试"""

    def test_service_creation(self):
        """测试服务创建"""
        db = SessionLocal()
        try:
            service = create_ck_expire_service(db)
            assert isinstance(service, CKExpireService)
        finally:
            db.close()

    def test_config_update_integration(self):
        """测试配置更新集成"""
        db = SessionLocal()
        try:
            service = create_ck_expire_service(db)
            
            # 更新配置
            system_settings.set_value(db, 'ck_expire_minutes', '45')
            system_settings.set_value(db, 'ck_expire_check_enabled', 'true')
            system_settings.set_value(db, 'ck_expire_check_interval', '8')
            
            # 验证配置更新
            config = service.get_expire_config()
            assert config['expire_minutes'] == 45
            assert config['check_enabled'] is True
            assert config['check_interval'] == 8
            
        finally:
            db.close()


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
