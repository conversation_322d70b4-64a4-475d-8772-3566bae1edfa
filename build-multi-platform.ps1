# 多平台构建脚本 - 不影响全局环境变量
# 支持同时构建Windows和Linux版本

param(
    [string]$Platform = "both",  # windows, linux, both
    [switch]$Obfuscate = $true,
    [switch]$Verbose = $false
)

function Write-Info($message) { Write-Host "[INFO] $message" -ForegroundColor Green }
function Write-Success($message) { Write-Host "[SUCCESS] $message" -ForegroundColor Cyan }
function Write-Warning($message) { Write-Host "[WARN] $message" -ForegroundColor Yellow }
function Write-Error($message) { Write-Host "[ERROR] $message" -ForegroundColor Red }
function Write-Debug($message) { if ($Verbose) { Write-Host "[DEBUG] $message" -ForegroundColor Gray } }

function Build-Platform {
    param(
        [string]$TargetOS,
        [string]$TargetArch,
        [string]$OutputFile,
        [bool]$UseObfuscation
    )
    
    Write-Info "构建 $TargetOS/$TargetArch 版本..."
    
    # 构建参数
    $buildTime = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    $version = "v2.0.0"
    $ldflags = "-w -s -X 'main.Version=$version' -X 'main.BuildTime=$buildTime'"
    
    try {
        if ($UseObfuscation) {
            Write-Debug "使用混淆构建: $OutputFile"
            
            # 使用临时环境变量，不影响全局
            $env_backup = @{
                GOOS = $env:GOOS
                GOARCH = $env:GOARCH
                CGO_ENABLED = $env:CGO_ENABLED
            }
            
            try {
                $env:GOOS = $TargetOS
                $env:GOARCH = $TargetArch
                $env:CGO_ENABLED = "0"
                
                Write-Debug "环境变量: GOOS=$env:GOOS, GOARCH=$env:GOARCH"
                
                # 执行混淆构建
                & garble -tiny -literals build -ldflags="$ldflags" -trimpath -o $OutputFile main.go
                
                if ($LASTEXITCODE -ne 0) {
                    throw "混淆构建失败"
                }
                
            } finally {
                # 恢复原始环境变量
                if ($env_backup.GOOS) { $env:GOOS = $env_backup.GOOS } else { Remove-Item Env:GOOS -ErrorAction SilentlyContinue }
                if ($env_backup.GOARCH) { $env:GOARCH = $env_backup.GOARCH } else { Remove-Item Env:GOARCH -ErrorAction SilentlyContinue }
                if ($env_backup.CGO_ENABLED) { $env:CGO_ENABLED = $env_backup.CGO_ENABLED } else { Remove-Item Env:CGO_ENABLED -ErrorAction SilentlyContinue }
            }
            
        } else {
            Write-Debug "使用标准构建: $OutputFile"
            
            # 标准构建，使用命令行参数指定平台
            $buildCmd = "go"
            $buildArgs = @(
                "build",
                "-ldflags=$ldflags",
                "-trimpath",
                "-o", $OutputFile
            )
            
            # 使用临时环境变量进行标准构建
            $env_backup = @{
                GOOS = $env:GOOS
                GOARCH = $env:GOARCH
                CGO_ENABLED = $env:CGO_ENABLED
            }

            try {
                $env:GOOS = $TargetOS
                $env:GOARCH = $TargetArch
                $env:CGO_ENABLED = "0"

                # 执行构建
                & go build -ldflags="$ldflags" -trimpath -o $OutputFile main.go

                if ($LASTEXITCODE -ne 0) {
                    throw "标准构建失败"
                }

            } finally {
                # 恢复原始环境变量
                if ($env_backup.GOOS) { $env:GOOS = $env_backup.GOOS } else { Remove-Item Env:GOOS -ErrorAction SilentlyContinue }
                if ($env_backup.GOARCH) { $env:GOARCH = $env_backup.GOARCH } else { Remove-Item Env:GOARCH -ErrorAction SilentlyContinue }
                if ($env_backup.CGO_ENABLED) { $env:CGO_ENABLED = $env_backup.CGO_ENABLED } else { Remove-Item Env:CGO_ENABLED -ErrorAction SilentlyContinue }
            }
        }
        
        # 检查构建结果
        if (Test-Path $OutputFile) {
            $fileInfo = Get-Item $OutputFile
            Write-Success "✅ $TargetOS/$TargetArch 构建成功"
            Write-Info "   文件: $($fileInfo.Name)"
            Write-Info "   大小: $([math]::Round($fileInfo.Length / 1MB, 2)) MB"
            Write-Info "   时间: $($fileInfo.CreationTime)"
            return $true
        } else {
            throw "构建文件不存在"
        }
        
    } catch {
        Write-Error "❌ $TargetOS/$TargetArch 构建失败: $($_.Exception.Message)"
        return $false
    }
}

function Show-Summary {
    param([hashtable]$Results)
    
    Write-Host ""
    Write-Host "=========================================" -ForegroundColor Magenta
    Write-Host "🎯 构建完成总结" -ForegroundColor Magenta
    Write-Host "=========================================" -ForegroundColor Magenta
    Write-Host ""
    
    $successCount = 0
    $totalCount = 0
    
    foreach ($platform in $Results.Keys) {
        $totalCount++
        if ($Results[$platform]) {
            $successCount++
            Write-Success "✅ $platform"
        } else {
            Write-Error "❌ $platform"
        }
    }
    
    Write-Host ""
    Write-Info "成功: $successCount/$totalCount"
    
    if ($successCount -gt 0) {
        Write-Host ""
        Write-Info "构建文件:"
        Get-ChildItem walmart-gateway* | ForEach-Object {
            Write-Info "  $($_.Name) ($([math]::Round($_.Length / 1MB, 2)) MB)"
        }
        
        Write-Host ""
        Write-Info "使用方法:"
        if (Test-Path "walmart-gateway-windows.exe") {
            Write-Info "  Windows: .\walmart-gateway-windows.exe"
        }
        if (Test-Path "walmart-gateway-secure") {
            Write-Info "  Linux: ./walmart-gateway-secure"
        }
        if (Test-Path "walmart-gateway-linux") {
            Write-Info "  Linux: ./walmart-gateway-linux"
        }
    }
    
    Write-Host ""
}

# 主逻辑
try {
    Write-Host "=========================================" -ForegroundColor Cyan
    Write-Host "🚀 多平台构建脚本" -ForegroundColor Cyan
    Write-Host "=========================================" -ForegroundColor Cyan
    Write-Host ""
    
    Write-Info "构建配置:"
    Write-Info "  平台: $Platform"
    Write-Info "  混淆: $Obfuscate"
    Write-Info "  详细输出: $Verbose"
    Write-Host ""
    
    # 保存当前环境变量
    $originalEnv = @{
        GOOS = $env:GOOS
        GOARCH = $env:GOARCH
        CGO_ENABLED = $env:CGO_ENABLED
    }
    
    Write-Debug "原始环境变量: GOOS=$($originalEnv.GOOS), GOARCH=$($originalEnv.GOARCH), CGO_ENABLED=$($originalEnv.CGO_ENABLED)"
    
    $results = @{}
    
    # 构建Windows版本
    if ($Platform -eq "windows" -or $Platform -eq "both") {
        $outputFile = if ($Obfuscate) { "walmart-gateway-windows-secure.exe" } else { "walmart-gateway-windows.exe" }
        $results["Windows"] = Build-Platform -TargetOS "windows" -TargetArch "amd64" -OutputFile $outputFile -UseObfuscation $Obfuscate
    }
    
    # 构建Linux版本
    if ($Platform -eq "linux" -or $Platform -eq "both") {
        $outputFile = if ($Obfuscate) { "walmart-gateway-secure" } else { "walmart-gateway-linux" }
        $results["Linux"] = Build-Platform -TargetOS "linux" -TargetArch "amd64" -OutputFile $outputFile -UseObfuscation $Obfuscate
    }
    
    # 显示构建总结
    Show-Summary -Results $results
    
    # 验证环境变量未被改变
    Write-Debug "最终环境变量: GOOS=$env:GOOS, GOARCH=$env:GOARCH, CGO_ENABLED=$env:CGO_ENABLED"
    
    if ($env:GOOS -ne $originalEnv.GOOS -or $env:GOARCH -ne $originalEnv.GOARCH -or $env:CGO_ENABLED -ne $originalEnv.CGO_ENABLED) {
        Write-Warning "环境变量已被修改，正在恢复..."
        if ($originalEnv.GOOS) { $env:GOOS = $originalEnv.GOOS } else { Remove-Item Env:GOOS -ErrorAction SilentlyContinue }
        if ($originalEnv.GOARCH) { $env:GOARCH = $originalEnv.GOARCH } else { Remove-Item Env:GOARCH -ErrorAction SilentlyContinue }
        if ($originalEnv.CGO_ENABLED) { $env:CGO_ENABLED = $originalEnv.CGO_ENABLED } else { Remove-Item Env:CGO_ENABLED -ErrorAction SilentlyContinue }
        Write-Success "环境变量已恢复"
    } else {
        Write-Success "环境变量未被修改"
    }
    
} catch {
    Write-Error "构建过程中发生异常: $($_.Exception.Message)"
    exit 1
}
