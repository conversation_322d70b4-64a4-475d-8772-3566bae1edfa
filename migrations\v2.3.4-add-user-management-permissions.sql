-- ========================================
-- 添加用户管理缺失的权限配置
-- 描述: 为用户增删改查接口添加完整的权限配置，确保用户管理功能的权限控制完整性
-- ========================================

USE `walmart_card_db`;

-- ========================================
-- 1. 记录迁移开始
-- ========================================
INSERT INTO `migration_logs` (`migration_name`, `status`, `message`, `created_at`) VALUES
('user_management_permissions_v2.3.4', 'started', '开始添加用户管理缺失的权限配置', NOW(3));

-- ========================================
-- 2. 添加缺失的用户管理权限
-- ========================================

-- 添加用户状态更新权限（如果不存在）
INSERT IGNORE INTO `permissions` (`code`, `name`, `description`, `resource_type`, `resource_path`, `is_enabled`, `sort_order`, `created_at`, `updated_at`) VALUES
('api:users:status', '修改用户状态', '修改用户状态权限（启用/禁用用户）', 'api', '/api/v1/users/{id}/status', 1, 118, NOW(3), NOW(3));

-- 添加更新个人信息权限（如果不存在）
INSERT IGNORE INTO `permissions` (`code`, `name`, `description`, `resource_type`, `resource_path`, `is_enabled`, `sort_order`, `created_at`, `updated_at`) VALUES
('api:users:update-me', '更新个人信息', '更新当前用户个人信息权限', 'api', '/api/v1/users/me', 1, 119, NOW(3), NOW(3));

-- ========================================
-- 3. 为角色分配新增的权限
-- ========================================

-- 获取角色ID
SET @super_admin_role_id = (SELECT id FROM `roles` WHERE code = 'super_admin' LIMIT 1);
SET @merchant_admin_role_id = (SELECT id FROM `roles` WHERE code = 'merchant_admin' LIMIT 1);
SET @ck_supplier_role_id = (SELECT id FROM `roles` WHERE code = 'ck_supplier' LIMIT 1);

-- 为超级管理员分配所有新增权限
INSERT IGNORE INTO `role_permissions` (`role_id`, `permission_id`, `created_at`)
SELECT @super_admin_role_id, p.id, NOW(3)
FROM `permissions` p
WHERE p.code IN (
    'api:users:status',
    'api:users:update-me'
) AND @super_admin_role_id IS NOT NULL;

-- 为商户管理员分配用户状态更新权限
INSERT IGNORE INTO `role_permissions` (`role_id`, `permission_id`, `created_at`)
SELECT @merchant_admin_role_id, p.id, NOW(3)
FROM `permissions` p
WHERE p.code IN (
    'api:users:status',
    'api:users:update-me'
) AND @merchant_admin_role_id IS NOT NULL;

-- 为CK供应商分配个人信息更新权限
INSERT IGNORE INTO `role_permissions` (`role_id`, `permission_id`, `created_at`)
SELECT @ck_supplier_role_id, p.id, NOW(3)
FROM `permissions` p
WHERE p.code IN (
    'api:users:update-me'
) AND @ck_supplier_role_id IS NOT NULL;

-- ========================================
-- 4. 验证权限配置
-- ========================================

-- 检查新增权限是否正确插入
SELECT
    '用户管理权限检查' as check_type,
    COUNT(*) as permission_count,
    CASE
        WHEN COUNT(*) >= 2 THEN '✓ 用户管理权限配置完整'
        ELSE '⚠ 用户管理权限配置不完整'
    END as status
FROM `permissions`
WHERE code IN ('api:users:status', 'api:users:update-me');

-- 检查超级管理员权限分配
SELECT
    '超级管理员用户管理权限' as check_type,
    COUNT(*) as assigned_permissions,
    CASE
        WHEN COUNT(*) >= 2 THEN '✓ 超级管理员权限分配完整'
        ELSE '⚠ 超级管理员权限分配不完整'
    END as status
FROM `role_permissions` rp
JOIN `roles` r ON rp.role_id = r.id
JOIN `permissions` p ON rp.permission_id = p.id
WHERE r.code = 'super_admin'
AND p.code IN ('api:users:status', 'api:users:update-me');

-- 检查商户管理员权限分配
SELECT
    '商户管理员用户管理权限' as check_type,
    COUNT(*) as assigned_permissions,
    CASE
        WHEN COUNT(*) >= 2 THEN '✓ 商户管理员权限分配完整'
        ELSE '⚠ 商户管理员权限分配不完整'
    END as status
FROM `role_permissions` rp
JOIN `roles` r ON rp.role_id = r.id
JOIN `permissions` p ON rp.permission_id = p.id
WHERE r.code = 'merchant_admin'
AND p.code IN ('api:users:status', 'api:users:update-me');

-- 检查CK供应商权限分配
SELECT
    'CK供应商用户管理权限' as check_type,
    COUNT(*) as assigned_permissions,
    CASE
        WHEN COUNT(*) >= 1 THEN '✓ CK供应商权限分配完整'
        ELSE '⚠ CK供应商权限分配不完整'
    END as status
FROM `role_permissions` rp
JOIN `roles` r ON rp.role_id = r.id
JOIN `permissions` p ON rp.permission_id = p.id
WHERE r.code = 'ck_supplier'
AND p.code IN ('api:users:update-me');

-- ========================================
-- 5. 显示当前所有用户管理相关权限
-- ========================================
SELECT
    '=== 用户管理权限列表 ===' as summary,
    p.code as permission_code,
    p.name as permission_name,
    p.sort_order as sort_order,
    GROUP_CONCAT(r.name ORDER BY r.name SEPARATOR ', ') as assigned_roles
FROM `permissions` p
LEFT JOIN `role_permissions` rp ON p.id = rp.permission_id
LEFT JOIN `roles` r ON rp.role_id = r.id
WHERE p.code LIKE 'api:users:%'
GROUP BY p.id, p.code, p.name, p.sort_order
ORDER BY p.sort_order;

-- ========================================
-- 6. 记录迁移完成
-- ========================================
UPDATE `migration_logs`
SET
    `status` = 'completed',
    `message` = '用户管理权限配置更新完成 - 权限已添加并分配给相应角色',
    `completed_at` = NOW(3),
    `data_summary` = JSON_OBJECT(
        'permissions_added', (SELECT COUNT(*) FROM `permissions` WHERE code IN ('api:users:status', 'api:users:update-me')),
        'super_admin_permissions', (
            SELECT COUNT(*) FROM `role_permissions` rp
            JOIN `roles` r ON rp.role_id = r.id
            JOIN `permissions` p ON rp.permission_id = p.id
            WHERE r.code = 'super_admin' AND p.code IN ('api:users:status', 'api:users:update-me')
        ),
        'merchant_admin_permissions', (
            SELECT COUNT(*) FROM `role_permissions` rp
            JOIN `roles` r ON rp.role_id = r.id
            JOIN `permissions` p ON rp.permission_id = p.id
            WHERE r.code = 'merchant_admin' AND p.code IN ('api:users:status', 'api:users:update-me')
        ),
        'ck_supplier_permissions', (
            SELECT COUNT(*) FROM `role_permissions` rp
            JOIN `roles` r ON rp.role_id = r.id
            JOIN `permissions` p ON rp.permission_id = p.id
            WHERE r.code = 'ck_supplier' AND p.code IN ('api:users:update-me')
        ),
        'total_user_permissions', (SELECT COUNT(*) FROM `permissions` WHERE code LIKE 'api:users:%'),
        'migration_timestamp', NOW(3)
    )
WHERE `migration_name` = 'user_management_permissions_v2.3.4';

-- ========================================
-- 7. 显示迁移结果摘要
-- ========================================
SELECT
    '=== 用户管理权限迁移完成 ===' as summary,
    (SELECT COUNT(*) FROM `permissions` WHERE code IN ('api:users:status', 'api:users:update-me')) as permissions_added,
    (SELECT COUNT(*) FROM `role_permissions` rp
     JOIN `roles` r ON rp.role_id = r.id
     JOIN `permissions` p ON rp.permission_id = p.id
     WHERE r.code = 'super_admin' AND p.code IN ('api:users:status', 'api:users:update-me')) as super_admin_assigned,
    (SELECT COUNT(*) FROM `role_permissions` rp
     JOIN `roles` r ON rp.role_id = r.id
     JOIN `permissions` p ON rp.permission_id = p.id
     WHERE r.code = 'merchant_admin' AND p.code IN ('api:users:status', 'api:users:update-me')) as merchant_admin_assigned,
    (SELECT COUNT(*) FROM `role_permissions` rp
     JOIN `roles` r ON rp.role_id = r.id
     JOIN `permissions` p ON rp.permission_id = p.id
     WHERE r.code = 'ck_supplier' AND p.code IN ('api:users:update-me')) as ck_supplier_assigned,
    (SELECT COUNT(*) FROM `permissions` WHERE code LIKE 'api:users:%') as total_user_permissions;
