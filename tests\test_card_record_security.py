"""
绑卡数据安全测试 - 验证跨商户数据泄露修复效果
"""

import pytest
from sqlalchemy.orm import Session
from fastapi.testclient import TestClient
from unittest.mock import Mock
import uuid

from app.models.user import User
from app.models.merchant import Merchant
from app.models.department import Department
from app.models.card_record import CardRecord
from app.services.card_record_service import CardRecordService
from app.services.card_api_service import CardAPIService
from app.services.security_service import SecurityService
from app.core.auth import AuthService
from app.services.permission_service import PermissionService


class TestCardRecordSecurity:
    """绑卡数据安全测试类"""

    @pytest.fixture
    def setup_test_data(self, db: Session):
        """设置测试数据"""
        # 创建两个商户
        merchant1 = Merchant(
            id=1,
            name="商户1",
            code="MERCHANT_1",
            api_key="test_key_1",
            api_secret="test_secret_1"
        )
        merchant2 = Merchant(
            id=2,
            name="商户2", 
            code="MERCHANT_2",
            api_key="test_key_2",
            api_secret="test_secret_2"
        )
        db.add_all([merchant1, merchant2])

        # 创建部门
        dept1 = Department(
            id=1,
            name="部门1",
            merchant_id=1
        )
        dept2 = Department(
            id=2,
            name="部门2",
            merchant_id=2
        )
        db.add_all([dept1, dept2])

        # 创建用户
        user1 = User(
            id=1,
            username="user1",
            email="<EMAIL>",
            hashed_password="hashed_password",
            merchant_id=1,
            department_id=1,
            is_superuser=False
        )
        user2 = User(
            id=2,
            username="user2",
            email="<EMAIL>",
            hashed_password="hashed_password",
            merchant_id=2,
            department_id=2,
            is_superuser=False
        )
        superuser = User(
            id=3,
            username="admin",
            email="<EMAIL>",
            hashed_password="hashed_password",
            merchant_id=None,
            department_id=None,
            is_superuser=True
        )
        db.add_all([user1, user2, superuser])

        # 创建绑卡记录
        card1 = CardRecord(
            id=str(uuid.uuid4()),
            merchant_id=1,
            department_id=1,
            card_number="1234567890123456",
            merchant_order_id="ORDER_001",
            amount=10000,  # 100元
            status="pending",
            request_id=str(uuid.uuid4()),
            request_data={},
            created_by=1
        )
        card2 = CardRecord(
            id=str(uuid.uuid4()),
            merchant_id=2,
            department_id=2,
            card_number="****************",
            merchant_order_id="ORDER_002",
            amount=20000,  # 200元
            status="pending",
            request_id=str(uuid.uuid4()),
            request_data={},
            created_by=2
        )
        db.add_all([card1, card2])

        db.commit()
        return {
            "merchant1": merchant1,
            "merchant2": merchant2,
            "user1": user1,
            "user2": user2,
            "superuser": superuser,
            "card1": card1,
            "card2": card2
        }

    def test_card_record_merchant_isolation(self, db: Session, setup_test_data):
        """测试绑卡记录的商户隔离"""
        card_service = CardRecordService(db)
        user1 = setup_test_data["user1"]  # 属于商户1
        user2 = setup_test_data["user2"]  # 属于商户2
        card1 = setup_test_data["card1"]  # 属于商户1
        card2 = setup_test_data["card2"]  # 属于商户2

        # 用户1应该能访问自己商户的绑卡记录
        result1 = card_service.get_with_security_check(card1.id, user1)
        assert result1 is not None, "用户应该能访问自己商户的绑卡记录"

        # 用户1不应该能访问其他商户的绑卡记录
        result2 = card_service.get_with_security_check(card2.id, user1)
        assert result2 is None, "用户不应该能访问其他商户的绑卡记录"

    def test_card_api_service_security(self, db: Session, setup_test_data):
        """测试CardAPIService的安全检查"""
        card_api_service = CardAPIService(db)
        user1 = setup_test_data["user1"]
        user2 = setup_test_data["user2"]

        # 测试跨商户访问检查
        with pytest.raises(Exception) as exc_info:
            card_api_service.get_card_records(
                current_user=user1,
                merchant_id=2,  # 尝试访问其他商户
                page=1,
                page_size=10
            )
        assert "无权访问其他商户" in str(exc_info.value)

    def test_data_isolation_in_queries(self, db: Session, setup_test_data):
        """测试查询中的数据隔离"""
        card_service = CardRecordService(db)
        user1 = setup_test_data["user1"]
        user2 = setup_test_data["user2"]

        # 用户1的查询应该只返回商户1的绑卡记录
        query1 = db.query(CardRecord)
        isolated_query1 = card_service.apply_data_isolation(query1, user1)
        results1 = isolated_query1.all()
        
        assert len(results1) == 1, f"用户1应该只能看到1个绑卡记录，实际看到{len(results1)}个"
        assert results1[0].merchant_id == 1, "用户1应该只能看到自己商户的绑卡记录"

        # 用户2的查询应该只返回商户2的绑卡记录
        query2 = db.query(CardRecord)
        isolated_query2 = card_service.apply_data_isolation(query2, user2)
        results2 = isolated_query2.all()
        
        assert len(results2) == 1, f"用户2应该只能看到1个绑卡记录，实际看到{len(results2)}个"
        assert results2[0].merchant_id == 2, "用户2应该只能看到自己商户的绑卡记录"

    def test_security_service_card_validation(self, db: Session, setup_test_data):
        """测试安全服务的绑卡记录验证"""
        security_service = SecurityService(db)
        user1 = setup_test_data["user1"]
        card1 = setup_test_data["card1"]
        card2 = setup_test_data["card2"]

        # 用户1访问自己商户的绑卡记录应该通过
        assert security_service.validate_card_access(user1, card1.id, "read")

        # 用户1访问其他商户的绑卡记录应该失败
        assert not security_service.validate_card_access(user1, card2.id, "read")

    def test_superuser_access(self, db: Session, setup_test_data):
        """测试超级管理员访问"""
        card_service = CardRecordService(db)
        security_service = SecurityService(db)
        superuser = setup_test_data["superuser"]
        card1 = setup_test_data["card1"]
        card2 = setup_test_data["card2"]

        # 超级管理员应该能访问所有绑卡记录
        assert card_service.get_with_security_check(card1.id, superuser) is not None
        assert card_service.get_with_security_check(card2.id, superuser) is not None

        # 超级管理员应该通过所有安全验证
        assert security_service.validate_card_access(superuser, card1.id, "read")
        assert security_service.validate_card_access(superuser, card2.id, "read")

    def test_batch_operations_security(self, db: Session, setup_test_data):
        """测试批量操作的安全性"""
        card_api_service = CardAPIService(db)
        user1 = setup_test_data["user1"]
        card1 = setup_test_data["card1"]
        card2 = setup_test_data["card2"]

        # 批量操作包含其他商户的记录应该失败
        with pytest.raises(Exception) as exc_info:
            card_api_service.batch_bind_cards([card1.id, card2.id], user1)
        assert "无权限绑定" in str(exc_info.value)

    def test_join_query_isolation(self, db: Session, setup_test_data):
        """测试JOIN查询的数据隔离"""
        card_api_service = CardAPIService(db)
        user1 = setup_test_data["user1"]

        # 获取绑卡记录列表（包含JOIN查询）
        result = card_api_service.get_card_records(
            current_user=user1,
            page=1,
            page_size=10
        )

        # 应该只返回用户1商户的数据
        assert result["total"] == 1, "JOIN查询应该只返回用户商户的数据"
        assert result["items"][0]["merchant_id"] == 1, "返回的记录应该属于用户的商户"


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
