{"version": "0.2.0", "configurations": [{"name": "启动沃尔玛绑卡处理器", "type": "go", "request": "launch", "mode": "debug", "cwd": "${workspaceFolder}", "program": "${workspaceFolder}/main.go", "args": ["--config", "config.yaml"], "env": {"APP_ENV": "development", "APP_DEBUG": "true"}, "showLog": true, "console": "integratedTerminal", "stopOnEntry": false}, {"name": "验证配置文件", "type": "go", "request": "launch", "mode": "auto", "cwd": "${workspaceFolder}", "program": "${workspaceFolder}/main.go", "args": ["validate-config", "--config", "config.yaml"], "env": {"APP_ENV": "development"}, "showLog": true, "console": "integratedTerminal"}]}