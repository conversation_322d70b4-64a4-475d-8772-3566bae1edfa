# 沃尔玛API响应解析修复

## 📋 问题描述

用户报告沃尔玛API返回的数据没有正确解析出来，提供的实际API响应格式如下：

```json
{
  "logId": "YhxuJ22J",
  "status": false,
  "error": {
    "errorcode": 203,
    "message": "请先去登录",
    "redirect": null,
    "validators": null
  },
  "data": null
}
```

## 🚨 原有问题

### ❌ 结构体字段不匹配

**原有的APIResponse结构体**：
```go
type APIResponse struct {
    Success bool                   `json:"success"`  // ❌ 实际字段是 "status"
    Code    string                 `json:"code,omitempty"`
    Message string                 `json:"message,omitempty"`
    Data    map[string]interface{} `json:"data,omitempty"`
}
```

**实际API响应字段**：
- ✅ `logId` - 日志ID（原结构体缺失）
- ✅ `status` - 状态标识（原结构体字段名为`success`）
- ✅ `error` - 错误对象（原结构体缺失）
- ✅ `data` - 数据对象

### ❌ 错误信息解析失败

当API返回错误时：
- 错误消息在 `error.message` 中，不是顶层的 `message`
- 错误码在 `error.errorcode` 中，不是顶层的 `code`
- 导致错误信息无法正确提取，影响重试策略判断

## ✅ 解决方案

### 1. 新增WalmartAPIResponse结构体

**文件**: `pkg/walmart/api_client.go`

```go
// WalmartAPIResponse 沃尔玛API实际响应格式
type WalmartAPIResponse struct {
    LogID  string                 `json:"logId"`
    Status bool                   `json:"status"`
    Error  *WalmartAPIError       `json:"error,omitempty"`
    Data   map[string]interface{} `json:"data,omitempty"`
}

// WalmartAPIError 沃尔玛API错误信息
type WalmartAPIError struct {
    ErrorCode  int         `json:"errorcode"`
    Message    string      `json:"message"`
    Redirect   interface{} `json:"redirect"`
    Validators interface{} `json:"validators"`
}
```

### 2. 更新响应解析逻辑

**修改**: `makeRequestWithContext` 方法

```go
// 尝试解析为新的沃尔玛API响应格式
var walmartResp WalmartAPIResponse
if err := json.Unmarshal(body, &walmartResp); err != nil {
    return nil, fmt.Errorf("解析响应失败: %w", err)
}

// 转换为兼容的APIResponse格式
apiResp := &APIResponse{
    Success: walmartResp.Status,
    Data:    walmartResp.Data,
}

// 设置LogID到Data中
if apiResp.Data == nil {
    apiResp.Data = make(map[string]interface{})
}
apiResp.Data["logId"] = walmartResp.LogID

// 处理错误信息
if walmartResp.Error != nil {
    apiResp.Success = false
    apiResp.Code = fmt.Sprintf("%d", walmartResp.Error.ErrorCode)
    apiResp.Message = walmartResp.Error.Message
    
    // 将错误信息也放入Data中，保持与现有解析逻辑兼容
    apiResp.Data["error"] = map[string]interface{}{
        "errorcode": walmartResp.Error.ErrorCode,
        "message":   walmartResp.Error.Message,
        "redirect":  walmartResp.Error.Redirect,
        "validators": walmartResp.Error.Validators,
    }
}
```

### 3. 保持向后兼容

- 保留原有的 `APIResponse` 结构体
- 新的解析逻辑将沃尔玛格式转换为兼容格式
- 现有的 `parseBindCardResponse` 等方法无需修改

## 🎯 修复效果

### ✅ **正确解析错误信息**

**测试用例**：
```json
{
  "logId": "YhxuJ22J",
  "status": false,
  "error": {
    "errorcode": 203,
    "message": "请先去登录"
  }
}
```

**解析结果**：
```
✅ WalmartAPIResponse解析成功:
  - LogID: YhxuJ22J
  - Status: false
  - Error.ErrorCode: 203
  - Error.Message: 请先去登录

✅ 转换为APIResponse格式:
  - Success: false
  - Code: 203
  - Message: 请先去登录
  - Data["logId"]: YhxuJ22J
  - Data["error"]["errorcode"]: 203
  - Data["error"]["message"]: 请先去登录
```

### ✅ **错误分类正确工作**

- 错误码 `203` + 消息 `"请先去登录"` 被正确识别
- 触发CK切换重试策略（在配置文件的 `ck_switch_errors` 中）
- 重试策略服务能够正确评估错误类型

### ✅ **成功响应也正确解析**

```json
{
  "logId": "ABC123",
  "status": true,
  "data": {
    "cardNo": "1234****5678",
    "balance": "10000"
  }
}
```

解析为：
```
✅ 成功响应解析:
  - LogID: ABC123
  - Status: true
  - Error: <nil>
  - Data: map[balance:10000 cardNo:1234****5678]
```

## 🔧 关键改进

### 1. **字段映射正确**
- `status` → `Success`
- `logId` → `Data["logId"]`
- `error.errorcode` → `Code`
- `error.message` → `Message`

### 2. **错误信息完整保留**
- 错误对象完整保存在 `Data["error"]` 中
- 现有的错误解析逻辑（如 `parseBindCardResponse`）继续工作
- 支持错误码和错误消息的正确提取

### 3. **调试信息增强**
- 添加原始响应体的调试日志
- 便于排查API响应解析问题

### 4. **向后兼容**
- 不破坏现有代码
- 所有现有的解析方法继续工作
- 平滑升级，无需大规模重构

## 🚀 总结

通过这次修复：

1. **🎯 解决了核心问题**: API响应解析失败导致错误信息丢失
2. **⚡ 提升了重试策略**: 错误码203能够正确触发CK切换
3. **🔧 增强了调试能力**: 原始响应日志帮助排查问题
4. **🛡️ 保持了兼容性**: 现有代码无需修改

现在系统能够正确解析沃尔玛API的实际响应格式，确保错误信息能够被准确提取和处理！
