"""
测试数据管理器
负责创建、管理和清理测试数据
"""

import uuid
import random
import string
import time
from typing import Dict, List, Any, Optional
from sqlalchemy.orm import Session
import logging

logger = logging.getLogger(__name__)


class TestDataManager:
    """测试数据管理器"""
    
    def __init__(self, db: Session):
        """
        初始化测试数据管理器
        
        Args:
            db: 数据库会话
        """
        self.db = db
        self.created_data = {
            'merchants': [],
            'departments': [],
            'walmart_cks': [],
            'card_records': [],
            'users': []
        }
    
    async def create_test_environment(
        self,
        merchant_count: int = 2,
        departments_per_merchant: int = 4,
        cks_per_department: int = 3,
        cards_per_merchant: int = 100
    ) -> Dict[str, Any]:
        """
        创建完整的测试环境
        
        Args:
            merchant_count: 商户数量
            departments_per_merchant: 每个商户的部门数量
            cks_per_department: 每个部门的CK数量
            cards_per_merchant: 每个商户的卡记录数量
            
        Returns:
            Dict: 创建的测试数据信息
        """
        logger.info("开始创建测试环境...")
        
        try:
            # 1. 创建测试商户
            merchants = await self._create_test_merchants(merchant_count)
            
            # 2. 为每个商户创建部门
            departments = []
            for merchant in merchants:
                dept_list = await self._create_test_departments(
                    merchant['id'], departments_per_merchant
                )
                departments.extend(dept_list)
            
            # 3. 为每个部门创建CK
            walmart_cks = []
            for department in departments:
                ck_list = await self._create_test_walmart_cks(
                    department['merchant_id'], 
                    department['id'], 
                    cks_per_department
                )
                walmart_cks.extend(ck_list)
            
            # 4. 为每个商户创建卡记录
            card_records = []
            for merchant in merchants:
                card_list = await self._create_test_card_records(
                    merchant['id'], cards_per_merchant
                )
                card_records.extend(card_list)
            
            test_env = {
                'merchants': merchants,
                'departments': departments,
                'walmart_cks': walmart_cks,
                'card_records': card_records,
                'summary': {
                    'merchant_count': len(merchants),
                    'department_count': len(departments),
                    'ck_count': len(walmart_cks),
                    'card_count': len(card_records)
                }
            }
            
            logger.info(f"测试环境创建完成: {test_env['summary']}")
            return test_env
            
        except Exception as e:
            logger.error(f"创建测试环境失败: {e}")
            await self.cleanup_all()
            raise
    
    async def _create_test_merchants(self, count: int) -> List[Dict[str, Any]]:
        """创建测试商户"""
        from app.models.merchant import Merchant
        
        merchants = []
        for i in range(count):
            timestamp = int(time.time())
            random_suffix = ''.join(random.choices(string.ascii_lowercase, k=4))
            
            merchant_data = {
                'name': f'测试商户_{i+1}_{timestamp}',
                'code': f'TEST_MERCHANT_{i+1}_{timestamp}_{random_suffix}',
                'api_key': f'test_api_key_{timestamp}_{random_suffix}',
                'api_secret': f'test_api_secret_{timestamp}_{random_suffix}',
                'status': True,
                'callback_url': None,
                'allowed_ips': None,
                'daily_limit': 10000,
                'hourly_limit': 1000,
                'concurrency_limit': 100,
                'priority': 0,
                'request_timeout': 30,
                'retry_count': 3,
                'contact_name': f'测试联系人_{i+1}',
                'contact_phone': '13800138000',
                'contact_email': f'test_merchant_{i+1}@example.com',
                'remark': f'测试商户_{i+1}备注',
                'custom_config': None,
                'created_by': None,
                'api_key_updated_at': None,
                'api_secret_updated_at': None
            }
            
            merchant = Merchant(**merchant_data)
            self.db.add(merchant)
            self.db.flush()  # 获取ID但不提交
            
            merchant_info = {
                'id': merchant.id,
                'name': merchant.name,
                'code': merchant.code,
                'api_key': merchant.api_key
            }
            merchants.append(merchant_info)
            self.created_data['merchants'].append(merchant.id)
        
        self.db.commit()
        logger.info(f"创建了 {len(merchants)} 个测试商户")
        return merchants
    
    async def _create_test_departments(
        self, 
        merchant_id: int, 
        count: int
    ) -> List[Dict[str, Any]]:
        """创建测试部门（配置不同权重）"""
        from app.models.department import Department
        
        # 预定义的权重配置
        weight_configs = [
            {'weight': 800, 'name_suffix': '高权重部门'},
            {'weight': 200, 'name_suffix': '中权重部门'},
            {'weight': 100, 'name_suffix': '低权重部门'},
            {'weight': 0, 'name_suffix': '禁用部门'},
        ]
        
        departments = []
        for i in range(count):
            timestamp = int(time.time())
            random_suffix = ''.join(random.choices(string.ascii_lowercase, k=4))
            
            # 循环使用权重配置
            config = weight_configs[i % len(weight_configs)]
            
            department_data = {
                'merchant_id': merchant_id,
                'name': f'{config["name_suffix"]}_{i+1}_{timestamp}',
                'code': f'DEPT_{i+1}_{timestamp}_{random_suffix}',
                'description': f'测试部门描述_{i+1}',
                'status': True,
                'enable_binding': config['weight'] > 0,  # 权重为0则禁用绑卡
                'binding_weight': config['weight']
            }
            
            department = Department(**department_data)
            self.db.add(department)
            self.db.flush()
            
            dept_info = {
                'id': department.id,
                'merchant_id': merchant_id,
                'name': department.name,
                'binding_weight': department.binding_weight,
                'enable_binding': department.enable_binding
            }
            departments.append(dept_info)
            self.created_data['departments'].append(department.id)
        
        self.db.commit()
        logger.info(f"为商户 {merchant_id} 创建了 {len(departments)} 个测试部门")
        return departments    
    async def _create_test_walmart_cks(
        self, 
        merchant_id: int, 
        department_id: int, 
        count: int
    ) -> List[Dict[str, Any]]:
        """创建测试沃尔玛CK"""
        from app.models.walmart_ck import WalmartCK
        
        walmart_cks = []
        for i in range(count):
            timestamp = int(time.time())
            random_suffix = ''.join(random.choices(string.ascii_lowercase + string.digits, k=8))
            
            ck_data = {
                'merchant_id': merchant_id,
                'department_id': department_id,
                'sign': f'test_ck_{timestamp}_{random_suffix}@example.com#token123#wxsign456#27',
                'total_limit': random.randint(50, 200),  # 随机限制数量
                'bind_count': random.randint(0, 10),     # 随机已绑定数量
                'active': True,
                'description': f'测试CK_{i+1}_{timestamp}',
                'is_deleted': False
            }
            
            walmart_ck = WalmartCK(**ck_data)
            self.db.add(walmart_ck)
            self.db.flush()
            
            ck_info = {
                'id': walmart_ck.id,
                'merchant_id': merchant_id,
                'department_id': department_id,
                'sign': walmart_ck.sign,
                'total_limit': walmart_ck.total_limit,
                'bind_count': walmart_ck.bind_count,
                'available_count': walmart_ck.total_limit - walmart_ck.bind_count
            }
            walmart_cks.append(ck_info)
            self.created_data['walmart_cks'].append(walmart_ck.id)
        
        self.db.commit()
        logger.info(f"为部门 {department_id} 创建了 {len(walmart_cks)} 个测试CK")
        return walmart_cks
    
    async def _create_test_card_records(
        self, 
        merchant_id: int, 
        count: int
    ) -> List[Dict[str, Any]]:
        """创建测试卡记录"""
        from app.models.card_record import CardRecord
        
        card_records = []
        for i in range(count):
            timestamp = int(time.time())
            random_suffix = ''.join(random.choices(string.digits, k=6))
            
            card_data = {
                'id': str(uuid.uuid4()),
                'merchant_id': merchant_id,
                'card_number': f'6222{timestamp}{random_suffix}',
                'card_password': f'test_pwd_{random_suffix}',
                'status': 'pending',
                'amount': random.randint(1000, 50000),  # 随机金额(分)
                'trace_id': str(uuid.uuid4()),
                'ext_data': f'{{"test_data": "value_{i}"}}',
                'created_by': 1  # 假设存在ID为1的用户
            }
            
            card_record = CardRecord(**card_data)
            self.db.add(card_record)
            self.db.flush()
            
            record_info = {
                'id': card_record.id,
                'merchant_id': merchant_id,
                'card_number': card_record.card_number,
                'status': card_record.status,
                'amount': card_record.amount
            }
            card_records.append(record_info)
            self.created_data['card_records'].append(card_record.id)
        
        self.db.commit()
        logger.info(f"为商户 {merchant_id} 创建了 {len(card_records)} 个测试卡记录")
        return card_records
    
    async def create_test_user(self) -> Dict[str, Any]:
        """创建测试用户"""
        from app.models.user import User
        
        timestamp = int(time.time())
        random_suffix = ''.join(random.choices(string.ascii_lowercase, k=4))
        
        user_data = {
            'username': f'test_user_{timestamp}_{random_suffix}',
            'password': 'test_password_hash',  # 实际应该是哈希值
            'full_name': f'测试用户_{timestamp}',
            'email': f'test_user_{timestamp}@example.com',
            'is_active': True,
            'is_superuser': False
        }
        
        user = User(**user_data)
        self.db.add(user)
        self.db.commit()
        
        user_info = {
            'id': user.id,
            'username': user.username,
            'full_name': user.full_name,
            'email': user.email
        }
        
        self.created_data['users'].append(user.id)
        logger.info(f"创建测试用户: {user.username}")
        return user_info
    
    def get_department_weight_config(self, departments: List[Dict[str, Any]]) -> Dict[int, float]:
        """
        获取部门权重配置（用于验证权重分配）
        
        Args:
            departments: 部门列表
            
        Returns:
            Dict: 部门ID -> 期望权重比例的映射
        """
        # 计算总权重
        total_weight = sum(dept['binding_weight'] for dept in departments if dept['enable_binding'])
        
        if total_weight == 0:
            return {}
        
        # 计算每个部门的期望比例
        weight_config = {}
        for dept in departments:
            if dept['enable_binding'] and dept['binding_weight'] > 0:
                expected_ratio = dept['binding_weight'] / total_weight
                weight_config[dept['id']] = expected_ratio
        
        return weight_config
    
    async def cleanup_all(self):
        """清理所有创建的测试数据"""
        logger.info("开始清理测试数据...")
        
        try:
            # 按依赖关系逆序删除
            cleanup_order = [
                ('card_records', 'app.models.card_record', 'CardRecord'),
                ('walmart_cks', 'app.models.walmart_ck', 'WalmartCK'),
                ('departments', 'app.models.department', 'Department'),
                ('merchants', 'app.models.merchant', 'Merchant'),
                ('users', 'app.models.user', 'User')
            ]
            
            for data_type, module_path, model_name in cleanup_order:
                if self.created_data[data_type]:
                    await self._cleanup_data_type(data_type, module_path, model_name)
            
            self.db.commit()
            logger.info("测试数据清理完成")
            
        except Exception as e:
            logger.error(f"清理测试数据失败: {e}")
            self.db.rollback()
            raise
    
    async def _cleanup_data_type(self, data_type: str, module_path: str, model_name: str):
        """清理特定类型的数据"""
        if not self.created_data[data_type]:
            return
        
        try:
            # 动态导入模型
            module = __import__(module_path, fromlist=[model_name])
            model_class = getattr(module, model_name)
            
            # 批量删除
            deleted_count = self.db.query(model_class).filter(
                model_class.id.in_(self.created_data[data_type])
            ).delete(synchronize_session=False)
            
            logger.info(f"清理 {data_type}: 删除了 {deleted_count} 条记录")
            self.created_data[data_type].clear()
            
        except Exception as e:
            logger.error(f"清理 {data_type} 失败: {e}")
            raise
    
    def get_test_summary(self) -> Dict[str, Any]:
        """获取测试数据摘要"""
        return {
            'created_counts': {
                key: len(value) for key, value in self.created_data.items()
            },
            'total_created': sum(len(value) for value in self.created_data.values())
        }