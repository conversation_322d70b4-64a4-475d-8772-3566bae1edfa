"""
Telegram机器人消息格式化服务
提供统一的消息格式化和美化功能
"""

from typing import Dict, Any, List, Optional
from datetime import datetime
from app.core.logging import get_logger

logger = get_logger(__name__)


class MessageFormatter:
    """消息格式化器"""

    # 表情符号映射
    EMOJI_MAP = {
        "success": "✅",
        "error": "❌",
        "warning": "⚠️",
        "info": "ℹ️",
        "loading": "⏳",
        "money": "💰",
        "chart": "📊",
        "calendar": "📅",
        "clock": "🕐",
        "user": "👤",
        "group": "👥",
        "key": "🔑",
        "link": "🔗",
        "settings": "⚙️",
        "help": "❓",
        "star": "⭐",
        "fire": "🔥",
        "rocket": "🚀",
        "trophy": "🏆",
        "target": "🎯",
        "bell": "🔔",
        "lock": "🔒",
        "unlock": "🔓",
        "progress": "📈",
        "check": "☑️",
        "cross": "❎",
        "hourglass": "⏰",
        "gear": "⚙️",
        "magic": "✨",
        "thumbs_up": "👍",
        "thumbs_down": "👎"
    }
    
    def __init__(self):
        self.logger = logger

    def format_progress_message(self, operation: str, progress: int, total: int, details: str = "") -> str:
        """
        格式化进度消息

        Args:
            operation: 操作名称
            progress: 当前进度
            total: 总数
            details: 详细信息

        Returns:
            str: 格式化的进度消息
        """
        try:
            percentage = int((progress / total) * 100) if total > 0 else 0
            progress_bar = self._create_progress_bar(percentage)

            message = f"""{self.EMOJI_MAP['loading']} **{operation}中...**

{progress_bar} {percentage}%

📊 **进度详情**：
• 已完成：{progress}/{total}
• 剩余：{total - progress}"""

            if details:
                message += f"\n• 当前：{details}"

            message += f"\n\n{self.EMOJI_MAP['clock']} 请稍候，正在处理中..."

            return message

        except Exception as e:
            self.logger.error(f"格式化进度消息失败: {e}")
            return f"{self.EMOJI_MAP['loading']} 正在处理中..."

    def format_confirmation_message(self, action: str, details: Dict[str, Any], warning: str = "") -> str:
        """
        格式化确认消息

        Args:
            action: 操作名称
            details: 操作详情
            warning: 警告信息

        Returns:
            str: 格式化的确认消息
        """
        try:
            message = f"""{self.EMOJI_MAP['warning']} **确认操作**

🎯 **即将执行**：{action}

📋 **操作详情**："""

            for key, value in details.items():
                message += f"\n• {key}：{value}"

            if warning:
                message += f"\n\n⚠️ **注意**：{warning}"

            message += f"""

{self.EMOJI_MAP['help']} **确认操作**：
• 输入 `确认` 或 `yes` 继续
• 输入 `取消` 或 `no` 取消操作
• 30秒内无响应将自动取消"""

            return message

        except Exception as e:
            self.logger.error(f"格式化确认消息失败: {e}")
            return f"{self.EMOJI_MAP['warning']} 请确认是否继续操作？"
    
    def format_statistics_message(self, stats: Dict[str, Any]) -> str:
        """
        格式化统计数据消息
        
        Args:
            stats: 统计数据
            
        Returns:
            str: 格式化的消息
        """
        try:
            # 基础信息
            title = stats.get('title', '数据统计')
            period = stats.get('period', '今日')
            
            # 统计数据
            total_count = stats.get('total_count', 0)
            success_count = stats.get('success_count', 0)
            failed_count = stats.get('failed_count', 0)
            success_rate = stats.get('success_rate', 0)
            
            # 金额数据
            total_amount = stats.get('total_amount', 0)
            success_amount = stats.get('success_amount', 0)
            
            # 格式化消息
            message = f"""📊 **{title}**

📅 **统计周期**：{period}

📈 **绑卡数据**：
• 总数量：{total_count:,} 笔
• 成功：{self.EMOJI_MAP['success']} {success_count:,} 笔
• 失败：{self.EMOJI_MAP['error']} {failed_count:,} 笔
• 成功率：{self.EMOJI_MAP['star']} {success_rate:.1f}%

💰 **金额统计**：
• 总金额：¥{total_amount/100:,.2f}
• 成功金额：¥{success_amount/100:,.2f}"""

            # 添加详细状态分布（如果有）
            if 'status_distribution' in stats:
                message += "\n\n📋 **状态分布**："
                for status, count in stats['status_distribution'].items():
                    status_name = self._get_status_display_name(status)
                    message += f"\n• {status_name}：{count:,} 笔"
            
            # 添加时间信息
            if 'generated_at' in stats:
                generated_time = stats['generated_at']
                if isinstance(generated_time, str):
                    message += f"\n\n🕐 **更新时间**：{generated_time}"
                elif isinstance(generated_time, datetime):
                    message += f"\n\n🕐 **更新时间**：{generated_time.strftime('%Y-%m-%d %H:%M:%S')}"
            
            return message
            
        except Exception as e:
            self.logger.error(f"格式化统计消息失败: {e}")
            return f"{self.EMOJI_MAP['error']} 数据格式化失败，请稍后再试"
    
    def format_group_status_message(self, group_info: Dict[str, Any]) -> str:
        """
        格式化群组状态消息
        
        Args:
            group_info: 群组信息
            
        Returns:
            str: 格式化的消息
        """
        try:
            group_name = group_info.get('title', '当前群组')
            bind_status = group_info.get('bind_status', 'unknown')
            merchant_name = group_info.get('merchant_name', '未知')
            department_name = group_info.get('department_name')
            bind_time = group_info.get('bind_time')
            
            # 状态图标
            status_emoji = {
                'ACTIVE': self.EMOJI_MAP['success'],
                'PENDING': self.EMOJI_MAP['loading'],
                'SUSPENDED': self.EMOJI_MAP['warning']
            }.get(bind_status, self.EMOJI_MAP['error'])
            
            message = f"""🔗 **群组绑定状态**

👥 **群组名称**：{group_name}
{status_emoji} **绑定状态**：{self._get_bind_status_display_name(bind_status)}
🏢 **绑定商户**：{merchant_name}"""

            if department_name:
                message += f"\n🏬 **绑定部门**：{department_name}"
            
            if bind_time:
                if isinstance(bind_time, str):
                    message += f"\n🕐 **绑定时间**：{bind_time}"
                elif isinstance(bind_time, datetime):
                    message += f"\n🕐 **绑定时间**：{bind_time.strftime('%Y-%m-%d %H:%M:%S')}"
            
            return message
            
        except Exception as e:
            self.logger.error(f"格式化群组状态消息失败: {e}")
            return f"{self.EMOJI_MAP['error']} 状态信息格式化失败"
    
    def format_user_verification_message(self, verification_info: Dict[str, Any]) -> str:
        """
        格式化用户验证消息
        
        Args:
            verification_info: 验证信息
            
        Returns:
            str: 格式化的消息
        """
        try:
            verification_token = verification_info.get('verification_token', '')
            expire_minutes = verification_info.get('expire_minutes', 30)
            
            message = f"""📝 **身份验证申请已提交**

🔑 **验证令牌**：`{verification_token}`

📋 **下一步操作**：
1. 将此验证令牌提供给系统管理员
2. 管理员将在后台完成身份关联
3. 完成后您将收到确认通知

⏰ **注意事项**：
• 验证令牌有效期为 {expire_minutes} 分钟
• 请及时联系管理员完成验证
• 验证完成前部分功能可能受限

{self.EMOJI_MAP['help']} 如需帮助，请输入 `/help` 查看更多信息。"""

            return message
            
        except Exception as e:
            self.logger.error(f"格式化验证消息失败: {e}")
            return f"{self.EMOJI_MAP['error']} 验证信息格式化失败"
    
    def format_help_message(self, commands: List[Dict[str, str]]) -> str:
        """
        格式化帮助消息
        
        Args:
            commands: 命令列表
            
        Returns:
            str: 格式化的帮助消息
        """
        try:
            message = f"""👋 **欢迎使用沃尔玛绑卡统计机器人！**

🤖 **我能为您做什么？**
• 📊 查询绑卡统计数据
• 🔗 管理群组绑定设置
• 👥 处理用户身份验证

📋 **可用命令列表**："""

            for cmd in commands:
                command = cmd.get('command', '')
                description = cmd.get('description', '')
                emoji = cmd.get('emoji', '•')
                message += f"\n{emoji} `{command}` - {description}"
            
            message += f"""

💡 **使用提示**：
• 点击命令可直接复制
• 部分命令需要管理员权限
• 如遇问题请联系管理员

{self.EMOJI_MAP['help']} **需要帮助？** 随时输入 `/help` 查看此信息"""

            return message
            
        except Exception as e:
            self.logger.error(f"格式化帮助消息失败: {e}")
            return f"{self.EMOJI_MAP['error']} 帮助信息格式化失败"
    
    def format_bind_success_message(self, bind_info: Dict[str, Any]) -> str:
        """
        格式化绑定成功消息
        
        Args:
            bind_info: 绑定信息
            
        Returns:
            str: 格式化的消息
        """
        try:
            merchant_name = bind_info.get('merchant_name', '未知商户')
            department_name = bind_info.get('department_name')
            
            message = f"""{self.EMOJI_MAP['success']} **群组绑定成功！**

🎉 恭喜！群组已成功绑定到商户系统。

🏢 **绑定商户**：{merchant_name}"""

            if department_name:
                message += f"\n🏬 **绑定部门**：{department_name}"
            
            message += f"""

✨ **现在您可以**：
• 使用 `/stats` 查看今日统计
• 使用 `/stats_week` 查看本周统计
• 使用 `/stats_month` 查看本月统计
• 使用 `/status` 查看群组状态

{self.EMOJI_MAP['help']} 输入 `/help` 查看所有可用命令"""

            return message
            
        except Exception as e:
            self.logger.error(f"格式化绑定成功消息失败: {e}")
            return f"{self.EMOJI_MAP['success']} 绑定成功！"
    
    def _get_status_display_name(self, status: str) -> str:
        """获取状态显示名称"""
        status_map = {
            'success': f"{self.EMOJI_MAP['success']} 成功",
            'failed': f"{self.EMOJI_MAP['error']} 失败",
            'pending': f"{self.EMOJI_MAP['loading']} 处理中",
            'cancelled': f"{self.EMOJI_MAP['warning']} 已取消"
        }
        return status_map.get(status, f"• {status}")
    
    def _get_bind_status_display_name(self, status: str) -> str:
        """获取绑定状态显示名称"""
        status_map = {
            'ACTIVE': '已激活',
            'PENDING': '待绑定',
            'SUSPENDED': '已暂停'
        }
        return status_map.get(status, '未知状态')

    def _create_progress_bar(self, percentage: int, length: int = 10) -> str:
        """
        创建进度条

        Args:
            percentage: 百分比 (0-100)
            length: 进度条长度

        Returns:
            str: 进度条字符串
        """
        try:
            filled = int((percentage / 100) * length)
            empty = length - filled
            return "█" * filled + "░" * empty
        except Exception:
            return "░" * length

    def format_operation_result(self, operation: str, success: bool, details: Dict[str, Any] = None, suggestions: List[str] = None) -> str:
        """
        格式化操作结果消息

        Args:
            operation: 操作名称
            success: 是否成功
            details: 结果详情
            suggestions: 建议操作

        Returns:
            str: 格式化的结果消息
        """
        try:
            emoji = self.EMOJI_MAP['success'] if success else self.EMOJI_MAP['error']
            status = "成功" if success else "失败"

            message = f"""{emoji} **{operation}{status}**

📊 **操作结果**："""

            if details:
                for key, value in details.items():
                    message += f"\n• {key}：{value}"

            if suggestions:
                message += f"\n\n💡 **建议操作**："
                for suggestion in suggestions:
                    message += f"\n• {suggestion}"

            if success:
                message += f"\n\n{self.EMOJI_MAP['thumbs_up']} 操作已完成！"
            else:
                message += f"\n\n{self.EMOJI_MAP['help']} 如需帮助，请输入 `/help`"

            return message

        except Exception as e:
            self.logger.error(f"格式化操作结果失败: {e}")
            emoji = self.EMOJI_MAP['success'] if success else self.EMOJI_MAP['error']
            return f"{emoji} {operation}{'成功' if success else '失败'}"

    def format_status_update(self, title: str, items: List[Dict[str, Any]], footer: str = "") -> str:
        """
        格式化状态更新消息

        Args:
            title: 标题
            items: 状态项目列表
            footer: 页脚信息

        Returns:
            str: 格式化的状态消息
        """
        try:
            message = f"""{self.EMOJI_MAP['info']} **{title}**

📋 **状态详情**："""

            for item in items:
                status_emoji = self.EMOJI_MAP.get(item.get('status', 'info'), '•')
                name = item.get('name', '未知')
                value = item.get('value', '无')
                message += f"\n{status_emoji} {name}：{value}"

            if footer:
                message += f"\n\n{footer}"

            return message

        except Exception as e:
            self.logger.error(f"格式化状态更新失败: {e}")
            return f"{self.EMOJI_MAP['info']} {title}"


# 创建全局消息格式化器实例
message_formatter = MessageFormatter()
