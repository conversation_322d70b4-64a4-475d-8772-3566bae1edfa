<template>
  <div class="advanced-analytics">
    <!-- 页面头部 -->
    <div class="page-header">
      <h1 class="page-title">高级数据分析</h1>
      <div class="header-actions">
        <el-button type="primary" :icon="DataAnalysis" @click="runAnalysis" :loading="analyzing">
          运行分析
        </el-button>
        <el-button type="success" :icon="Download" @click="exportResults">
          导出结果
        </el-button>
      </div>
    </div>

    <!-- 分析类型选择 -->
    <el-card shadow="never" style="margin-bottom: 20px;">
      <template #header>
        <span>分析配置</span>
      </template>
      
      <el-tabs v-model="activeAnalysisType" @tab-change="handleAnalysisTypeChange">
        <!-- 多维度数据透视 -->
        <el-tab-pane label="多维度透视" name="multidimensional">
          <el-form :model="multidimensionalConfig" inline>
            <el-form-item label="分析维度">
              <el-select v-model="multidimensionalConfig.dimensions" multiple style="width: 300px;">
                <el-option label="用户ID" value="user_id" />
                <el-option label="商户ID" value="merchant_id" />
                <el-option label="事件类型" value="event_type" />
                <el-option label="操作类型" value="action" />
                <el-option label="资源类型" value="resource_type" />
                <el-option label="小时" value="hour" />
                <el-option label="星期" value="weekday" />
              </el-select>
            </el-form-item>
            
            <el-form-item label="分析指标">
              <el-select v-model="multidimensionalConfig.metrics" multiple style="width: 200px;">
                <el-option label="计数" value="count" />
                <el-option label="独立用户数" value="unique_users" />
                <el-option label="人均次数" value="avg_per_user" />
              </el-select>
            </el-form-item>
            
            <el-form-item label="时间范围">
              <el-date-picker
                v-model="multidimensionalConfig.timeRange"
                type="datetimerange"
                range-separator="至"
                start-placeholder="开始时间"
                end-placeholder="结束时间"
                style="width: 350px;"
              />
            </el-form-item>
          </el-form>
        </el-tab-pane>

        <!-- 趋势分析和预测 -->
        <el-tab-pane label="趋势预测" name="trend">
          <el-form :model="trendConfig" inline>
            <el-form-item label="分析指标">
              <el-select v-model="trendConfig.metric" style="width: 150px;">
                <el-option label="事件数量" value="event_count" />
                <el-option label="用户活跃度" value="user_activity" />
                <el-option label="错误率" value="error_rate" />
              </el-select>
            </el-form-item>
            
            <el-form-item label="时间粒度">
              <el-select v-model="trendConfig.timePeriod" style="width: 120px;">
                <el-option label="每小时" value="hourly" />
                <el-option label="每日" value="daily" />
                <el-option label="每周" value="weekly" />
              </el-select>
            </el-form-item>
            
            <el-form-item label="预测天数">
              <el-input-number v-model="trendConfig.predictionDays" :min="1" :max="30" style="width: 120px;" />
            </el-form-item>
          </el-form>
        </el-tab-pane>

        <!-- 用户行为漏斗 -->
        <el-tab-pane label="行为漏斗" name="funnel">
          <el-form :model="funnelConfig" inline>
            <el-form-item label="漏斗步骤">
              <el-select v-model="funnelConfig.steps" multiple style="width: 400px;">
                <el-option label="登录" value="LOGIN" />
                <el-option label="查看用户" value="VIEW_USER" />
                <el-option label="创建商户" value="CREATE_MERCHANT" />
                <el-option label="编辑权限" value="UPDATE_PERMISSION" />
                <el-option label="导出数据" value="EXPORT_DATA" />
              </el-select>
            </el-form-item>
            
            <el-form-item label="分析时间">
              <el-date-picker
                v-model="funnelConfig.timeRange"
                type="datetimerange"
                range-separator="至"
                start-placeholder="开始时间"
                end-placeholder="结束时间"
                style="width: 350px;"
              />
            </el-form-item>
          </el-form>
        </el-tab-pane>

        <!-- 队列分析 -->
        <el-tab-pane label="队列分析" name="cohort">
          <el-form :model="cohortConfig" inline>
            <el-form-item label="队列类型">
              <el-select v-model="cohortConfig.cohortType" style="width: 120px;">
                <el-option label="按月" value="monthly" />
                <el-option label="按周" value="weekly" />
                <el-option label="按日" value="daily" />
              </el-select>
            </el-form-item>
            
            <el-form-item label="分析指标">
              <el-select v-model="cohortConfig.metric" style="width: 120px;">
                <el-option label="留存率" value="retention" />
                <el-option label="活跃度" value="activity" />
                <el-option label="转化率" value="conversion" />
              </el-select>
            </el-form-item>
          </el-form>
        </el-tab-pane>
      </el-tabs>
    </el-card>

    <!-- 分析结果展示 -->
    <div v-if="analysisResults" class="results-section">
      <!-- 多维度透视结果 -->
      <div v-if="activeAnalysisType === 'multidimensional'" class="multidimensional-results">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-card shadow="never" class="chart-card">
              <template #header>
                <span>维度分析</span>
              </template>
              <div ref="dimensionChart" class="chart-container" v-loading="chartLoading"></div>
            </el-card>
          </el-col>
          
          <el-col :span="12">
            <el-card shadow="never" class="chart-card">
              <template #header>
                <span>交叉分析</span>
              </template>
              <div ref="crossChart" class="chart-container" v-loading="chartLoading"></div>
            </el-card>
          </el-col>
        </el-row>

        <!-- 分析摘要 -->
        <el-card shadow="never" style="margin-top: 20px;">
          <template #header>
            <span>分析摘要</span>
          </template>
          <div class="summary-content">
            <div v-for="(insights, dimension) in analysisResults.summary?.top_dimensions" :key="dimension" class="dimension-summary">
              <h4>{{ dimension }} Top 5:</h4>
              <el-tag v-for="[key, value] in insights" :key="key" style="margin-right: 10px; margin-bottom: 5px;">
                {{ key }}: {{ value }}
              </el-tag>
            </div>
          </div>
        </el-card>
      </div>

      <!-- 趋势预测结果 -->
      <div v-if="activeAnalysisType === 'trend'" class="trend-results">
        <el-row :gutter="20">
          <el-col :span="16">
            <el-card shadow="never" class="chart-card">
              <template #header>
                <span>趋势分析与预测</span>
              </template>
              <div ref="trendChart" class="chart-container" v-loading="chartLoading"></div>
            </el-card>
          </el-col>
          
          <el-col :span="8">
            <el-card shadow="never" class="insight-card">
              <template #header>
                <span>趋势洞察</span>
              </template>
              <div class="insights-content">
                <div v-for="insight in analysisResults.insights" :key="insight" class="insight-item">
                  <el-icon><TrendCharts /></el-icon>
                  <span>{{ insight }}</span>
                </div>
              </div>
            </el-card>
          </el-col>
        </el-row>

        <!-- 季节性分析 -->
        <el-card shadow="never" style="margin-top: 20px;" v-if="analysisResults.seasonality">
          <template #header>
            <span>季节性分析</span>
          </template>
          <div ref="seasonalityChart" class="chart-container" v-loading="chartLoading"></div>
        </el-card>
      </div>

      <!-- 漏斗分析结果 -->
      <div v-if="activeAnalysisType === 'funnel'" class="funnel-results">
        <el-row :gutter="20">
          <el-col :span="16">
            <el-card shadow="never" class="chart-card">
              <template #header>
                <span>用户行为漏斗</span>
              </template>
              <div ref="funnelChart" class="chart-container" v-loading="chartLoading"></div>
            </el-card>
          </el-col>
          
          <el-col :span="8">
            <el-card shadow="never" class="conversion-card">
              <template #header>
                <span>转化率详情</span>
              </template>
              <div class="conversion-details">
                <div v-for="rate in analysisResults.conversion_rates" :key="rate.step" class="conversion-item">
                  <div class="step-name">{{ rate.step }}</div>
                  <div class="conversion-rate" :class="getConversionRateClass(rate.conversion_rate)">
                    {{ rate.conversion_rate }}%
                  </div>
                  <div class="user-count">{{ rate.users_count }} 用户</div>
                </div>
              </div>
            </el-card>
          </el-col>
        </el-row>

        <!-- 优化建议 -->
        <el-card shadow="never" style="margin-top: 20px;" v-if="analysisResults.recommendations?.length > 0">
          <template #header>
            <span>优化建议</span>
          </template>
          <div class="recommendations">
            <el-alert
              v-for="(recommendation, index) in analysisResults.recommendations"
              :key="index"
              :title="recommendation"
              type="info"
              :closable="false"
              style="margin-bottom: 10px;"
            />
          </div>
        </el-card>
      </div>

      <!-- 队列分析结果 -->
      <div v-if="activeAnalysisType === 'cohort'" class="cohort-results">
        <el-card shadow="never" class="chart-card">
          <template #header>
            <span>队列分析热力图</span>
          </template>
          <div ref="cohortChart" class="chart-container" v-loading="chartLoading"></div>
        </el-card>
      </div>
    </div>

    <!-- 无结果状态 -->
    <el-card shadow="never" v-if="!analysisResults && !analyzing">
      <el-empty description="请选择分析类型并运行分析">
        <el-button type="primary" @click="runAnalysis">开始分析</el-button>
      </el-empty>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import { DataAnalysis, Download, TrendCharts } from '@element-plus/icons-vue'
import * as echarts from 'echarts'

// 响应式数据
const analyzing = ref(false)
const chartLoading = ref(false)
const activeAnalysisType = ref('multidimensional')
const analysisResults = ref(null)

// 图表引用
const dimensionChart = ref()
const crossChart = ref()
const trendChart = ref()
const seasonalityChart = ref()
const funnelChart = ref()
const cohortChart = ref()

// 图表实例
let dimensionChartInstance = null
let crossChartInstance = null
let trendChartInstance = null
let seasonalityChartInstance = null
let funnelChartInstance = null
let cohortChartInstance = null

// 分析配置
const multidimensionalConfig = reactive({
  dimensions: ['user_id', 'action'],
  metrics: ['count', 'unique_users'],
  timeRange: [new Date(Date.now() - 7 * 24 * 60 * 60 * 1000), new Date()]
})

const trendConfig = reactive({
  metric: 'event_count',
  timePeriod: 'daily',
  predictionDays: 7
})

const funnelConfig = reactive({
  steps: ['LOGIN', 'VIEW_USER', 'CREATE_MERCHANT'],
  timeRange: [new Date(Date.now() - 7 * 24 * 60 * 60 * 1000), new Date()]
})

const cohortConfig = reactive({
  cohortType: 'monthly',
  metric: 'retention'
})

// 工具方法
const getConversionRateClass = (rate) => {
  if (rate >= 80) return 'high-conversion'
  if (rate >= 50) return 'medium-conversion'
  return 'low-conversion'
}

// 事件处理方法
const handleAnalysisTypeChange = (type) => {
  analysisResults.value = null
  // 清理图表实例
  clearCharts()
}

const clearCharts = () => {
  dimensionChartInstance?.clear()
  crossChartInstance?.clear()
  trendChartInstance?.clear()
  seasonalityChartInstance?.clear()
  funnelChartInstance?.clear()
  cohortChartInstance?.clear()
}

// 数据获取方法
const runAnalysis = async () => {
  try {
    analyzing.value = true

    let mockResults = null

    if (activeAnalysisType.value === 'multidimensional') {
      mockResults = await getMockMultidimensionalResults()
    } else if (activeAnalysisType.value === 'trend') {
      mockResults = await getMockTrendResults()
    } else if (activeAnalysisType.value === 'funnel') {
      mockResults = await getMockFunnelResults()
    } else if (activeAnalysisType.value === 'cohort') {
      mockResults = await getMockCohortResults()
    }

    analysisResults.value = mockResults

    // 初始化图表
    await nextTick()
    initCharts()

    ElMessage.success('分析完成')
  } catch (error) {
    console.error('分析失败:', error)
    ElMessage.error('分析失败')
  } finally {
    analyzing.value = false
  }
}

// 模拟数据方法
const getMockMultidimensionalResults = async () => {
  return new Promise(resolve => {
    setTimeout(() => {
      resolve({
        dimensions: multidimensionalConfig.dimensions,
        metrics: multidimensionalConfig.metrics,
        total_records: 15420,
        analysis: {
          user_id: {
            count: { '1001': 245, '1002': 189, '1003': 156, '1004': 134, '1005': 98 },
            unique_users: { '1001': 1, '1002': 1, '1003': 1, '1004': 1, '1005': 1 }
          },
          action: {
            count: { 'LOGIN': 2500, 'VIEW_USER': 1800, 'CREATE_MERCHANT': 450, 'UPDATE_PERMISSION': 320 },
            unique_users: { 'LOGIN': 850, 'VIEW_USER': 650, 'CREATE_MERCHANT': 280, 'UPDATE_PERMISSION': 180 }
          }
        },
        summary: {
          top_dimensions: {
            user_id: [['1001', 245], ['1002', 189], ['1003', 156], ['1004', 134], ['1005', 98]],
            action: [['LOGIN', 2500], ['VIEW_USER', 1800], ['CREATE_MERCHANT', 450], ['UPDATE_PERMISSION', 320]]
          }
        }
      })
    }, 1500)
  })
}

const getMockTrendResults = async () => {
  return new Promise(resolve => {
    setTimeout(() => {
      const historicalData = []
      const predictions = []

      // 生成历史数据
      for (let i = 0; i < 30; i++) {
        const date = new Date(Date.now() - (29 - i) * 24 * 60 * 60 * 1000)
        const baseValue = 100 + Math.sin(i * 0.2) * 20
        const noise = (Math.random() - 0.5) * 10
        historicalData.push({
          date: date.toISOString().split('T')[0],
          value: Math.max(0, Math.round(baseValue + noise))
        })
      }

      // 生成预测数据
      for (let i = 1; i <= trendConfig.predictionDays; i++) {
        const date = new Date(Date.now() + i * 24 * 60 * 60 * 1000)
        const predictedValue = 120 + Math.sin((30 + i) * 0.2) * 20
        predictions.push({
          date: date.toISOString().split('T')[0],
          predicted_value: Math.round(predictedValue),
          confidence: Math.max(0.5, 1 - (i * 0.1))
        })
      }

      resolve({
        metric: trendConfig.metric,
        time_period: trendConfig.timePeriod,
        historical_data: historicalData,
        trend_analysis: {
          trend_direction: 'increasing',
          change_rate: 15.5,
          volatility: 0.25,
          min_value: 85,
          max_value: 135,
          avg_value: 110.2
        },
        predictions: predictions,
        seasonality: {
          weekly_pattern: [
            { weekday: 'Monday', average_value: 95.2 },
            { weekday: 'Tuesday', average_value: 108.5 },
            { weekday: 'Wednesday', average_value: 115.8 },
            { weekday: 'Thursday', average_value: 112.3 },
            { weekday: 'Friday', average_value: 98.7 },
            { weekday: 'Saturday', average_value: 78.4 },
            { weekday: 'Sunday', average_value: 82.1 }
          ],
          has_weekly_seasonality: true
        },
        insights: [
          '数据呈上升趋势，变化率为 15.5%',
          '预测未来平均值约为 120.00',
          '数据具有明显的周期性模式'
        ]
      })
    }, 1500)
  })
}

const getMockFunnelResults = async () => {
  return new Promise(resolve => {
    setTimeout(() => {
      resolve({
        funnel_steps: funnelConfig.steps,
        total_users: 1000,
        funnel_data: [
          { step: 'LOGIN', step_index: 0, users_count: 1000 },
          { step: 'VIEW_USER', step_index: 1, users_count: 750 },
          { step: 'CREATE_MERCHANT', step_index: 2, users_count: 450 }
        ],
        conversion_rates: [
          { step: 'LOGIN', conversion_rate: 100.0, users_count: 1000 },
          { step: 'VIEW_USER', conversion_rate: 75.0, users_count: 750 },
          { step: 'CREATE_MERCHANT', conversion_rate: 60.0, users_count: 450 }
        ],
        path_analysis: {
          total_unique_paths: 25,
          common_paths: [
            { path: 'LOGIN -> VIEW_USER -> CREATE_MERCHANT', count: 350 },
            { path: 'LOGIN -> VIEW_USER', count: 300 },
            { path: 'LOGIN', count: 250 }
          ]
        },
        churn_analysis: {
          churn_by_step: [
            { step: 'LOGIN', completed: 1000, dropped_off: 0, churn_rate: 0 },
            { step: 'VIEW_USER', completed: 750, dropped_off: 250, churn_rate: 25.0 },
            { step: 'CREATE_MERCHANT', completed: 450, dropped_off: 300, churn_rate: 40.0 }
          ],
          highest_churn_step: 'CREATE_MERCHANT'
        },
        recommendations: [
          '步骤 CREATE_MERCHANT 流失率最高，需要重点关注',
          '优化 VIEW_USER 到 CREATE_MERCHANT 的转化流程'
        ]
      })
    }, 1500)
  })
}

const getMockCohortResults = async () => {
  return new Promise(resolve => {
    setTimeout(() => {
      // 生成队列热力图数据
      const cohortData = []
      const months = ['2024-01', '2024-02', '2024-03', '2024-04', '2024-05', '2024-06']

      months.forEach((month, monthIndex) => {
        for (let period = 0; period < 6; period++) {
          const retentionRate = Math.max(0, 100 - period * 15 - Math.random() * 10)
          cohortData.push([monthIndex, period, Math.round(retentionRate)])
        }
      })

      resolve({
        cohort_type: cohortConfig.cohortType,
        metric: cohortConfig.metric,
        cohort_data: cohortData,
        cohort_metrics: {
          months: months,
          periods: ['Month 0', 'Month 1', 'Month 2', 'Month 3', 'Month 4', 'Month 5']
        },
        insights: [
          '新用户留存率在第一个月下降最快',
          '长期留存用户表现稳定',
          '建议加强新用户引导'
        ]
      })
    }, 1500)
  })
}

// 图表初始化方法
const initCharts = () => {
  if (activeAnalysisType.value === 'multidimensional') {
    initDimensionChart()
    initCrossChart()
  } else if (activeAnalysisType.value === 'trend') {
    initTrendChart()
    initSeasonalityChart()
  } else if (activeAnalysisType.value === 'funnel') {
    initFunnelChart()
  } else if (activeAnalysisType.value === 'cohort') {
    initCohortChart()
  }
}

const initDimensionChart = () => {
  if (dimensionChartInstance && analysisResults.value) {
    const actionData = analysisResults.value.analysis.action.count

    dimensionChartInstance.setOption({
      title: {
        text: '操作类型分布',
        left: 'center'
      },
      tooltip: {
        trigger: 'item'
      },
      series: [
        {
          type: 'pie',
          radius: '60%',
          data: Object.entries(actionData).map(([name, value]) => ({ name, value })),
          emphasis: {
            itemStyle: {
              shadowBlur: 10,
              shadowOffsetX: 0,
              shadowColor: 'rgba(0, 0, 0, 0.5)'
            }
          }
        }
      ]
    })
  }
}

const initCrossChart = () => {
  if (crossChartInstance && analysisResults.value) {
    const userData = analysisResults.value.analysis.user_id.count

    crossChartInstance.setOption({
      title: {
        text: '用户活动分布',
        left: 'center'
      },
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'shadow'
        }
      },
      xAxis: {
        type: 'category',
        data: Object.keys(userData)
      },
      yAxis: {
        type: 'value',
        name: '活动次数'
      },
      series: [
        {
          type: 'bar',
          data: Object.values(userData),
          itemStyle: {
            color: '#409EFF'
          }
        }
      ]
    })
  }
}

const initTrendChart = () => {
  if (trendChartInstance && analysisResults.value) {
    const historical = analysisResults.value.historical_data
    const predictions = analysisResults.value.predictions

    const dates = historical.map(item => item.date)
    const values = historical.map(item => item.value)
    const predictionDates = predictions.map(item => item.date)
    const predictionValues = predictions.map(item => item.predicted_value)

    trendChartInstance.setOption({
      title: {
        text: '趋势分析与预测',
        left: 'center'
      },
      tooltip: {
        trigger: 'axis'
      },
      legend: {
        data: ['历史数据', '预测数据'],
        top: 30
      },
      xAxis: {
        type: 'category',
        data: [...dates, ...predictionDates]
      },
      yAxis: {
        type: 'value',
        name: '数值'
      },
      series: [
        {
          name: '历史数据',
          type: 'line',
          data: [...values, ...Array(predictionDates.length).fill(null)],
          itemStyle: { color: '#409EFF' },
          smooth: true
        },
        {
          name: '预测数据',
          type: 'line',
          data: [...Array(dates.length).fill(null), ...predictionValues],
          itemStyle: { color: '#E6A23C' },
          lineStyle: { type: 'dashed' },
          smooth: true
        }
      ]
    })
  }
}

const initSeasonalityChart = () => {
  if (seasonalityChartInstance && analysisResults.value?.seasonality) {
    const weeklyPattern = analysisResults.value.seasonality.weekly_pattern

    seasonalityChartInstance.setOption({
      title: {
        text: '周期性模式',
        left: 'center'
      },
      tooltip: {
        trigger: 'axis'
      },
      xAxis: {
        type: 'category',
        data: weeklyPattern.map(item => item.weekday)
      },
      yAxis: {
        type: 'value',
        name: '平均值'
      },
      series: [
        {
          type: 'bar',
          data: weeklyPattern.map(item => item.average_value),
          itemStyle: {
            color: '#67C23A'
          }
        }
      ]
    })
  }
}

const initFunnelChart = () => {
  if (funnelChartInstance && analysisResults.value) {
    const funnelData = analysisResults.value.funnel_data

    funnelChartInstance.setOption({
      title: {
        text: '用户行为漏斗',
        left: 'center'
      },
      tooltip: {
        trigger: 'item',
        formatter: '{b}: {c} 用户 ({d}%)'
      },
      series: [
        {
          type: 'funnel',
          left: '10%',
          width: '80%',
          data: funnelData.map(item => ({
            name: item.step,
            value: item.users_count
          }))
        }
      ]
    })
  }
}

const initCohortChart = () => {
  if (cohortChartInstance && analysisResults.value) {
    const cohortData = analysisResults.value.cohort_data
    const months = analysisResults.value.cohort_metrics.months
    const periods = analysisResults.value.cohort_metrics.periods

    cohortChartInstance.setOption({
      title: {
        text: '队列分析热力图',
        left: 'center'
      },
      tooltip: {
        position: 'top',
        formatter: function (params) {
          return `${months[params.data[0]]} - ${periods[params.data[1]]}: ${params.data[2]}%`
        }
      },
      grid: {
        height: '50%',
        top: '10%'
      },
      xAxis: {
        type: 'category',
        data: periods,
        splitArea: {
          show: true
        }
      },
      yAxis: {
        type: 'category',
        data: months,
        splitArea: {
          show: true
        }
      },
      visualMap: {
        min: 0,
        max: 100,
        calculable: true,
        orient: 'horizontal',
        left: 'center',
        bottom: '15%',
        inRange: {
          color: ['#313695', '#4575b4', '#74add1', '#abd9e9', '#e0f3f8', '#ffffbf', '#fee090', '#fdae61', '#f46d43', '#d73027', '#a50026']
        }
      },
      series: [
        {
          type: 'heatmap',
          data: cohortData,
          label: {
            show: true,
            formatter: '{c}%'
          },
          emphasis: {
            itemStyle: {
              shadowBlur: 10,
              shadowColor: 'rgba(0, 0, 0, 0.5)'
            }
          }
        }
      ]
    })
  }
}

// 其他方法
const exportResults = () => {
  if (!analysisResults.value) {
    ElMessage.warning('请先运行分析')
    return
  }

  const exportData = {
    timestamp: new Date().toISOString(),
    analysis_type: activeAnalysisType.value,
    config: getCurrentConfig(),
    results: analysisResults.value
  }

  const blob = new Blob([JSON.stringify(exportData, null, 2)], { type: 'application/json' })
  const url = URL.createObjectURL(blob)
  const a = document.createElement('a')
  a.href = url
  a.download = `advanced-analytics-${activeAnalysisType.value}-${new Date().toISOString().split('T')[0]}.json`
  a.click()
  URL.revokeObjectURL(url)
  ElMessage.success('分析结果已导出')
}

const getCurrentConfig = () => {
  switch (activeAnalysisType.value) {
    case 'multidimensional':
      return multidimensionalConfig
    case 'trend':
      return trendConfig
    case 'funnel':
      return funnelConfig
    case 'cohort':
      return cohortConfig
    default:
      return {}
  }
}

// 生命周期
onMounted(async () => {
  await nextTick()

  // 初始化图表
  if (dimensionChart.value) {
    dimensionChartInstance = echarts.init(dimensionChart.value)
  }
  if (crossChart.value) {
    crossChartInstance = echarts.init(crossChart.value)
  }
  if (trendChart.value) {
    trendChartInstance = echarts.init(trendChart.value)
  }
  if (seasonalityChart.value) {
    seasonalityChartInstance = echarts.init(seasonalityChart.value)
  }
  if (funnelChart.value) {
    funnelChartInstance = echarts.init(funnelChart.value)
  }
  if (cohortChart.value) {
    cohortChartInstance = echarts.init(cohortChart.value)
  }

  // 窗口大小变化时重新调整图表
  window.addEventListener('resize', () => {
    dimensionChartInstance?.resize()
    crossChartInstance?.resize()
    trendChartInstance?.resize()
    seasonalityChartInstance?.resize()
    funnelChartInstance?.resize()
    cohortChartInstance?.resize()
  })
})
</script>

<style scoped>
.advanced-analytics {
  padding: 20px;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  background: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.page-title {
  font-size: 24px;
  font-weight: bold;
  margin: 0;
  color: #333;
}

.header-actions {
  display: flex;
  gap: 10px;
}

.results-section {
  margin-top: 20px;
}

.chart-card {
  height: 400px;
}

.chart-container {
  height: 320px;
  width: 100%;
}

.insight-card {
  height: 400px;
}

.insights-content {
  padding: 10px 0;
}

.insight-item {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
  padding: 10px;
  background: #f8f9fa;
  border-radius: 4px;
}

.insight-item .el-icon {
  margin-right: 10px;
  color: #409EFF;
}

.conversion-card {
  height: 400px;
}

.conversion-details {
  padding: 10px 0;
}

.conversion-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px;
  margin-bottom: 10px;
  background: #f8f9fa;
  border-radius: 4px;
}

.step-name {
  font-weight: bold;
  color: #333;
}

.conversion-rate {
  font-size: 18px;
  font-weight: bold;
}

.conversion-rate.high-conversion {
  color: #67C23A;
}

.conversion-rate.medium-conversion {
  color: #E6A23C;
}

.conversion-rate.low-conversion {
  color: #F56C6C;
}

.user-count {
  font-size: 12px;
  color: #666;
}

.summary-content {
  padding: 10px 0;
}

.dimension-summary {
  margin-bottom: 20px;
}

.dimension-summary h4 {
  margin-bottom: 10px;
  color: #333;
}

.recommendations {
  padding: 10px 0;
}

:deep(.el-card__body) {
  padding: 20px;
}

:deep(.el-card__header) {
  padding: 15px 20px;
  border-bottom: 1px solid #f0f0f0;
}

:deep(.el-tabs__content) {
  padding-top: 20px;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .results-section .el-col {
    margin-bottom: 20px;
  }
}

@media (max-width: 768px) {
  .advanced-analytics {
    padding: 10px;
  }

  .page-header {
    flex-direction: column;
    gap: 15px;
    text-align: center;
  }

  .chart-card {
    height: 300px;
  }

  .chart-container {
    height: 220px;
  }
}
</style>
