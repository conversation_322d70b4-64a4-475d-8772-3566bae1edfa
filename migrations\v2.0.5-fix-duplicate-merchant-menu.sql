-- ========================================
-- 修复重复的商户管理菜单（生产环境安全版本）
-- 执行时间：2025-06-14
-- 目的：安全删除系统管理下重复的商户管理菜单
--
-- 安全机制：
-- 1. 检查数据库初始化状态，避免在生产环境重复执行危险操作
-- 2. 使用条件执行，只在首次初始化或明确需要修复时执行
-- 3. 添加执行标记，防止重复执行
-- 4. 保持向后兼容性，不影响现有部署
-- ========================================

-- 设置连接字符集
SET NAMES utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 设置时区
SET time_zone = '+08:00';

USE `walmart_card_db`;

-- 创建迁移日志表（如果不存在）
CREATE TABLE IF NOT EXISTS `migration_logs` (
    `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '日志ID',
    `migration_name` varchar(100) NOT NULL COMMENT '迁移名称',
    `status` varchar(20) NOT NULL DEFAULT 'started' COMMENT '状态',
    `message` text NULL COMMENT '消息',
    `data_summary` text NULL COMMENT '数据摘要',
    `created_at` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '创建时间',
    `completed_at` datetime(3) NULL COMMENT '完成时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_migration_logs_name` (`migration_name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='迁移日志表';

-- 检查是否已经执行过此修复脚本
SET @duplicate_menu_fix_exists = (SELECT COUNT(*) FROM `migration_logs` WHERE `migration_name` = 'duplicate_merchant_menu_fix_v2.1.0');

-- 只在未执行过的情况下进行菜单修复
SET @should_fix_menus = (@duplicate_menu_fix_exists = 0);

-- 记录修复开始
INSERT IGNORE INTO `migration_logs` (`migration_name`, `status`, `message`, `created_at`)
VALUES ('duplicate_merchant_menu_fix_v2.1.0', 'started', '开始修复重复的商户管理菜单', NOW(3));

-- 安全删除重复的系统管理下的商户管理菜单（仅在需要时执行）
-- 1. 先删除角色菜单关联
SET @sql_delete_role_menus = IF(@should_fix_menus = 1,
    'DELETE rm FROM role_menus rm JOIN menus m ON rm.menu_id = m.id WHERE m.code = ''system:merchant'';',
    'SELECT ''跳过角色菜单关联删除 - 已执行过修复'' as status;'
);

PREPARE stmt_delete_role_menus FROM @sql_delete_role_menus;
EXECUTE stmt_delete_role_menus;
DEALLOCATE PREPARE stmt_delete_role_menus;

-- 2. 删除菜单权限关联（如果存在）
SET @sql_delete_menu_permissions = IF(@should_fix_menus = 1,
    'DELETE mp FROM menu_permissions mp JOIN menus m ON mp.menu_id = m.id WHERE m.code = ''system:merchant'';',
    'SELECT ''跳过菜单权限关联删除 - 已执行过修复'' as status;'
);

PREPARE stmt_delete_menu_permissions FROM @sql_delete_menu_permissions;
EXECUTE stmt_delete_menu_permissions;
DEALLOCATE PREPARE stmt_delete_menu_permissions;

-- 3. 删除权限记录（如果存在）
SET @sql_delete_permissions = IF(@should_fix_menus = 1,
    'DELETE FROM permissions WHERE code = ''menu:system:merchant'';',
    'SELECT ''跳过权限记录删除 - 已执行过修复'' as status;'
);

PREPARE stmt_delete_permissions FROM @sql_delete_permissions;
EXECUTE stmt_delete_permissions;
DEALLOCATE PREPARE stmt_delete_permissions;

-- 4. 最后删除菜单记录
SET @sql_delete_menus = IF(@should_fix_menus = 1,
    'DELETE FROM menus WHERE code = ''system:merchant'';',
    'SELECT ''跳过菜单记录删除 - 已执行过修复'' as status;'
);

PREPARE stmt_delete_menus FROM @sql_delete_menus;
EXECUTE stmt_delete_menus;
DEALLOCATE PREPARE stmt_delete_menus;

-- 验证删除结果（仅在执行修复时显示）
SET @sql_verify_results = IF(@should_fix_menus = 1,
    'SELECT ''删除完成'' as status, (SELECT COUNT(*) FROM menus WHERE code = ''system:merchant'') as remaining_menus, (SELECT COUNT(*) FROM permissions WHERE code = ''menu:system:merchant'') as remaining_permissions;',
    'SELECT ''修复验证跳过 - 已执行过修复'' as status;'
);

PREPARE stmt_verify_results FROM @sql_verify_results;
EXECUTE stmt_verify_results;
DEALLOCATE PREPARE stmt_verify_results;

-- 记录修复完成
UPDATE `migration_logs`
SET `status` = 'completed',
    `message` = '重复商户管理菜单修复完成',
    `completed_at` = NOW(3),
    `data_summary` = JSON_OBJECT(
        'fix_executed', @should_fix_menus,
        'remaining_duplicate_menus', (SELECT COUNT(*) FROM `menus` WHERE `code` = 'system:merchant'),
        'remaining_duplicate_permissions', (SELECT COUNT(*) FROM `permissions` WHERE `code` = 'menu:system:merchant'),
        'completion_timestamp', NOW(3)
    )
WHERE `migration_name` = 'duplicate_merchant_menu_fix_v2.1.0';

-- 显示修复结果摘要
SELECT
    '=== 重复菜单修复脚本执行完成 ===' as summary,
    CASE
        WHEN @should_fix_menus = 1 THEN '✓ 重复菜单修复已执行'
        ELSE '⚠ 重复菜单修复已跳过（之前已执行）'
    END as execution_status,
    NOW(3) as completion_time;

-- ========================================
-- 修复完成
-- ========================================
