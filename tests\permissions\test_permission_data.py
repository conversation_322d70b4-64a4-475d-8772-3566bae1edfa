"""
权限数据测试 - 验证权限数据是否正确加载
"""

import pytest
from sqlalchemy.orm import Session
from app.models.role import Role
from app.models.permission import Permission
from app.models.user import User
from app.services.role_service_new import RoleService
from app.services.permission_service import PermissionService


class TestPermissionData:
    """权限数据测试类"""

    def test_permissions_exist(self, db: Session):
        """测试权限数据是否存在"""
        # 检查基本API权限
        api_permissions = db.query(Permission).filter(
            Permission.code.like('api:%')
        ).all()
        
        assert len(api_permissions) > 0, "应该有API权限数据"
        
        # 检查具体权限
        expected_permissions = [
            'api:/api/v1/users',
            'api:/api/v1/roles', 
            'api:/api/v1/permissions',
            'api:users:create',
            'api:users:read',
            'api:roles:create',
            'api:roles:read'
        ]
        
        existing_codes = [p.code for p in api_permissions]
        for expected in expected_permissions:
            assert expected in existing_codes, f"权限 {expected} 应该存在"

    def test_roles_have_permissions(self, db: Session):
        """测试角色是否有权限分配"""
        # 获取超级管理员角色
        super_admin = db.query(Role).filter(Role.code == 'super_admin').first()
        assert super_admin is not None, "超级管理员角色应该存在"
        
        # 检查超级管理员是否有权限
        permissions = list(super_admin.permissions)
        assert len(permissions) > 0, "超级管理员应该有权限"
        
        # 获取商户管理员角色
        merchant_admin = db.query(Role).filter(Role.code == 'merchant_admin').first()
        assert merchant_admin is not None, "商户管理员角色应该存在"
        
        # 检查商户管理员是否有权限
        merchant_permissions = list(merchant_admin.permissions)
        assert len(merchant_permissions) > 0, "商户管理员应该有权限"

    def test_role_service_get_permissions(self, db: Session):
        """测试RoleService获取权限功能"""
        role_service = RoleService(db)
        
        # 获取超级管理员角色
        super_admin = db.query(Role).filter(Role.code == 'super_admin').first()
        assert super_admin is not None
        
        # 创建测试用户
        test_user = User(
            username="test_permission_user",
            email="<EMAIL>",
            is_superuser=True
        )
        
        # 测试获取角色权限
        permissions = role_service.get_role_permissions(super_admin.id, test_user)
        assert isinstance(permissions, list), "应该返回权限列表"
        assert len(permissions) > 0, "超级管理员应该有权限"

    def test_permission_service_get_user_permissions(self, db: Session):
        """测试PermissionService获取用户权限功能"""
        permission_service = PermissionService(db)
        
        # 创建测试超级管理员用户
        test_user = User(
            username="test_super_user",
            email="<EMAIL>", 
            is_superuser=True
        )
        
        # 测试获取用户权限
        permissions = permission_service.get_user_permissions(test_user)
        assert isinstance(permissions, list), "应该返回权限列表"
        assert len(permissions) > 0, "超级管理员应该有权限"
        assert "*:*:*" in permissions, "超级管理员应该有通配符权限"

    def test_role_permissions_relationship(self, db: Session):
        """测试角色权限关系是否正常工作"""
        # 获取一个有权限的角色
        role = db.query(Role).filter(Role.code == 'merchant_admin').first()
        assert role is not None, "商户管理员角色应该存在"
        
        # 测试权限关系
        try:
            permissions = list(role.permissions)
            assert isinstance(permissions, list), "权限应该是列表类型"
            
            if permissions:
                # 检查权限对象的属性
                first_permission = permissions[0]
                assert hasattr(first_permission, 'code'), "权限应该有code属性"
                assert hasattr(first_permission, 'name'), "权限应该有name属性"
                
        except Exception as e:
            pytest.fail(f"获取角色权限时出错: {e}")

    def test_permission_codes_format(self, db: Session):
        """测试权限代码格式是否正确"""
        permissions = db.query(Permission).all()
        
        for permission in permissions:
            assert permission.code is not None, f"权限 {permission.id} 的代码不能为空"
            assert len(permission.code) > 0, f"权限 {permission.id} 的代码不能为空字符串"
            
            # 检查API权限格式
            if permission.code.startswith('api:'):
                assert ':' in permission.code, f"API权限 {permission.code} 格式应该包含冒号"

    def test_role_permission_assignment(self, db: Session):
        """测试角色权限分配是否正确"""
        # 检查超级管理员是否有所有权限
        super_admin = db.query(Role).filter(Role.code == 'super_admin').first()
        if super_admin:
            admin_permissions = list(super_admin.permissions)
            total_permissions = db.query(Permission).filter(Permission.is_enabled == True).count()
            
            # 超级管理员应该有大部分权限
            assert len(admin_permissions) > 0, "超级管理员应该有权限"
            
        # 检查商户管理员权限
        merchant_admin = db.query(Role).filter(Role.code == 'merchant_admin').first()
        if merchant_admin:
            merchant_permissions = list(merchant_admin.permissions)
            permission_codes = [p.code for p in merchant_permissions]
            
            # 商户管理员应该有基本的管理权限
            expected_codes = ['api:users:read', 'api:departments:read']
            for code in expected_codes:
                if db.query(Permission).filter(Permission.code == code).first():
                    assert code in permission_codes, f"商户管理员应该有权限: {code}"

    def test_permission_resource_types(self, db: Session):
        """测试权限资源类型是否正确"""
        permissions = db.query(Permission).all()
        
        valid_resource_types = ['api', 'menu', 'data', 'operation']
        
        for permission in permissions:
            if permission.resource_type:
                assert permission.resource_type in valid_resource_types, \
                    f"权限 {permission.code} 的资源类型 {permission.resource_type} 无效"
