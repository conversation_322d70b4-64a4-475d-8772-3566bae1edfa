#!/usr/bin/env python3
"""
调试绑卡计数问题
检查实际绑卡记录和CK使用情况
"""

import asyncio
import sys
import os
from datetime import datetime, timedelta

# 添加项目路径
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.insert(0, project_root)

from app.api.deps import get_db
from app.models.walmart_ck import WalmartCK
from app.models.card_record import CardRecord
from app.core.logging import get_logger

logger = get_logger("debug_bind_count")


class BindCountDebugger:
    """绑卡计数调试器"""
    
    def __init__(self):
        self.db = next(get_db())
        
    def __enter__(self):
        return self
        
    def __exit__(self, exc_type, exc_val, exc_tb):
        if self.db:
            self.db.close()
    
    async def debug_recent_bindings(self, merchant_id: int = 2, hours: int = 24):
        """调试最近的绑卡记录"""
        print(f"🔍 调试最近 {hours} 小时的绑卡记录")
        print(f"商户ID: {merchant_id}")
        print("="*80)
        
        # 计算时间范围
        now = datetime.utcnow()
        start_time = now - timedelta(hours=hours)
        
        # 查询最近的绑卡记录
        recent_records = self.db.query(CardRecord).filter(
            CardRecord.merchant_id == merchant_id,
            CardRecord.created_at >= start_time,
            CardRecord.status == 'success'  # 只看成功的
        ).order_by(CardRecord.created_at.desc()).limit(20).all()
        
        print(f"📊 找到 {len(recent_records)} 条成功绑卡记录")
        
        if not recent_records:
            print("❌ 没有找到最近的成功绑卡记录")
            return
        
        # 分析每条记录
        print(f"\n📋 绑卡记录详情:")
        for i, record in enumerate(recent_records, 1):
            print(f"\n{i}. 记录ID: {record.id}")
            print(f"   卡号: {record.card_number[:6]}***")
            print(f"   状态: {record.status}")
            print(f"   金额: {record.amount}元")
            print(f"   使用的CK: {record.walmart_ck_id}")
            print(f"   部门ID: {record.department_id}")
            print(f"   创建时间: {record.created_at}")
            print(f"   更新时间: {record.updated_at}")
            
            # 检查对应的CK状态
            if record.walmart_ck_id:
                ck = self.db.query(WalmartCK).filter(WalmartCK.id == record.walmart_ck_id).first()
                if ck:
                    print(f"   CK状态: bind_count={ck.bind_count}, total_limit={ck.total_limit}, active={ck.active}")
                else:
                    print(f"   ❌ CK {record.walmart_ck_id} 不存在")
            else:
                print(f"   ⚠️ 没有记录使用的CK ID")
        
        # 统计CK使用情况
        await self._analyze_ck_usage(recent_records)
    
    async def _analyze_ck_usage(self, records):
        """分析CK使用情况"""
        print(f"\n📈 CK使用情况分析:")
        
        # 统计每个CK的使用次数
        ck_usage = {}
        records_with_ck = []
        records_without_ck = []
        
        for record in records:
            if record.walmart_ck_id:
                records_with_ck.append(record)
                ck_usage[record.walmart_ck_id] = ck_usage.get(record.walmart_ck_id, 0) + 1
            else:
                records_without_ck.append(record)
        
        print(f"  有CK记录的绑卡: {len(records_with_ck)}")
        print(f"  没有CK记录的绑卡: {len(records_without_ck)}")
        
        if records_without_ck:
            print(f"\n⚠️ 没有CK记录的绑卡详情:")
            for record in records_without_ck:
                print(f"    记录ID {record.id}: {record.card_number[:6]}***, 金额={record.amount}元")
        
        if ck_usage:
            print(f"\n📊 CK使用统计:")
            for ck_id, count in ck_usage.items():
                ck = self.db.query(WalmartCK).filter(WalmartCK.id == ck_id).first()
                if ck:
                    print(f"  CK {ck_id}: 记录中使用{count}次, 数据库bind_count={ck.bind_count}")
                    
                    # 检查是否一致
                    if count != ck.bind_count:
                        print(f"    ❌ 不一致！记录显示使用{count}次，但bind_count是{ck.bind_count}")
                    else:
                        print(f"    ✅ 一致")
                else:
                    print(f"  CK {ck_id}: ❌ CK不存在")
    
    async def check_specific_ck(self, ck_id: int):
        """检查特定CK的详细情况"""
        print(f"\n🔍 检查CK {ck_id} 的详细情况")
        print("="*60)
        
        # 获取CK信息
        ck = self.db.query(WalmartCK).filter(WalmartCK.id == ck_id).first()
        if not ck:
            print(f"❌ CK {ck_id} 不存在")
            return
        
        print(f"📊 CK基本信息:")
        print(f"  ID: {ck.id}")
        print(f"  商户ID: {ck.merchant_id}")
        print(f"  部门ID: {ck.department_id}")
        print(f"  bind_count: {ck.bind_count}")
        print(f"  total_limit: {ck.total_limit}")
        print(f"  active: {ck.active}")
        print(f"  last_bind_time: {ck.last_bind_time}")
        print(f"  创建时间: {ck.created_at}")
        
        # 查询使用这个CK的绑卡记录
        records_using_ck = self.db.query(CardRecord).filter(
            CardRecord.walmart_ck_id == ck_id,
            CardRecord.status == 'success'
        ).order_by(CardRecord.created_at.desc()).limit(10).all()
        
        print(f"\n📋 使用此CK的成功绑卡记录 (最近10条):")
        print(f"  找到 {len(records_using_ck)} 条记录")
        
        if records_using_ck:
            for i, record in enumerate(records_using_ck, 1):
                print(f"  {i}. 记录ID {record.id}: {record.card_number[:6]}***, "
                      f"金额={record.amount}元, 时间={record.created_at}")
        
        # 分析一致性
        expected_count = len(records_using_ck)
        actual_count = ck.bind_count
        
        print(f"\n📈 一致性分析:")
        print(f"  数据库记录的成功绑卡次数: {expected_count}")
        print(f"  CK的bind_count: {actual_count}")
        
        if expected_count == actual_count:
            print(f"  ✅ 数据一致")
        else:
            print(f"  ❌ 数据不一致！差异: {actual_count - expected_count}")
            
            if actual_count < expected_count:
                print(f"  🔍 可能原因: bind_count更新失败或事务回滚")
            else:
                print(f"  🔍 可能原因: 重复计数或测试数据")
    
    async def fix_ck_bind_count(self, ck_id: int, dry_run: bool = True):
        """修复CK的bind_count"""
        print(f"\n🔧 {'模拟' if dry_run else '执行'}修复CK {ck_id} 的bind_count")
        print("="*60)
        
        # 获取CK
        ck = self.db.query(WalmartCK).filter(WalmartCK.id == ck_id).first()
        if not ck:
            print(f"❌ CK {ck_id} 不存在")
            return
        
        # 计算正确的bind_count
        correct_count = self.db.query(CardRecord).filter(
            CardRecord.walmart_ck_id == ck_id,
            CardRecord.status == 'success'
        ).count()
        
        current_count = ck.bind_count
        
        print(f"📊 修复分析:")
        print(f"  当前bind_count: {current_count}")
        print(f"  正确bind_count: {correct_count}")
        print(f"  需要调整: {correct_count - current_count}")
        
        if current_count == correct_count:
            print(f"  ✅ 数据已经正确，无需修复")
            return
        
        if dry_run:
            print(f"  🔍 模拟模式：如果执行修复，将设置bind_count为 {correct_count}")
        else:
            try:
                ck.bind_count = correct_count
                self.db.commit()
                print(f"  ✅ 修复完成：bind_count已更新为 {correct_count}")
            except Exception as e:
                self.db.rollback()
                print(f"  ❌ 修复失败: {e}")


async def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description="绑卡计数问题调试")
    parser.add_argument("--merchant-id", type=int, default=2, help="商户ID")
    parser.add_argument("--hours", type=int, default=24, help="检查最近多少小时的记录")
    parser.add_argument("--ck-id", type=int, help="检查特定CK ID")
    parser.add_argument("--fix", action="store_true", help="修复CK计数（默认为模拟模式）")
    parser.add_argument("--execute", action="store_true", help="实际执行修复（与--fix一起使用）")
    
    args = parser.parse_args()
    
    with BindCountDebugger() as debugger:
        # 调试最近的绑卡记录
        await debugger.debug_recent_bindings(args.merchant_id, args.hours)
        
        # 检查特定CK
        if args.ck_id:
            await debugger.check_specific_ck(args.ck_id)
            
            # 修复CK计数
            if args.fix:
                dry_run = not args.execute
                await debugger.fix_ck_bind_count(args.ck_id, dry_run)
    
    print(f"\n🎉 调试完成！")


if __name__ == "__main__":
    asyncio.run(main())
