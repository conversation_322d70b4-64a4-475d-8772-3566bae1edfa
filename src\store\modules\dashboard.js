import { defineStore } from 'pinia'
import { ref } from 'vue'
import { dashboardApi } from '@/api/modules/dashboard'
import { ElMessage } from 'element-plus'

export const useDashboardStore = defineStore('dashboard', {
    state: () => ({
        summaryData: null,
        loading: false,
        error: null
    }),

    getters: {
        getSummaryData: (state) => state.summaryData,
        getLoading: (state) => state.loading,
        getError: (state) => state.error
    },

    actions: {
        async fetchSummary(params = {}) {
            this.loading = true;
            this.error = null;
            this.summaryData = null;
            try {
                // 过滤掉undefined和空字符串参数
                const filteredParams = Object.fromEntries(
                    Object.entries(params || {}).filter(([key, value]) =>
                        value !== undefined && value !== null && value !== ''
                    )
                )
                const response = await dashboardApi.getSummary(filteredParams);
                if (response) {
                    this.summaryData = response;
                }
            } catch (err) {
                this.error = err;
                ElMessage.error(err.message || '加载仪表盘数据失败');
            } finally {
                this.loading = false;
            }
        },

        resetState() {
            this.summaryData = null;
            this.loading = false;
            this.error = null;
        }
    }
});

