<script setup>
import { ref, onMounted, reactive, computed } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { merchantApi } from '@/api/modules/merchant'

const router = useRouter()
const route = useRoute()
const isEdit = computed(() => !!route.params.id)
const pageTitle = computed(() => isEdit.value ? '编辑商家' : '新增商家')
const loading = ref(false)
const formRef = ref(null)

// 表单数据
const formData = ref({
  id: null,
  name: '',
  code: '',
  status: true,
  api_key: '',
  api_secret: '',
  callback_url: '',
  allowed_ips: '',
  daily_limit: 100000000,
  hourly_limit: 100000000,
  concurrency_limit: 100,
  priority: 0,
  request_timeout: 30,
  retry_count: 3,
  contact_name: '',
  contact_phone: '',
  contact_email: '',
  remark: ''
})

// 表单验证规则
const rules = {
  name: [
    { required: true, message: '请输入商家名称', trigger: 'blur' },
    { min: 2, max: 100, message: '长度在 2 到 100 个字符', trigger: 'blur' }
  ],
  code: [
    { required: false, message: '请输入商家编码', trigger: 'blur' },
    { pattern: /^[a-zA-Z0-9_-]+$/, message: '只能包含字母、数字、下划线和连字符', trigger: 'blur' }
  ],
  daily_limit: [
    { required: true, message: '请输入每日绑卡限制', trigger: 'blur' },
    { type: 'number', message: '必须是数字', trigger: 'blur' }
  ],
  hourly_limit: [
    { required: true, message: '请输入每小时绑卡限制', trigger: 'blur' },
    { type: 'number', message: '必须是数字', trigger: 'blur' }
  ],
  concurrency_limit: [
    { required: true, message: '请输入最大并发数', trigger: 'blur' },
    { type: 'number', message: '必须是数字', trigger: 'blur' }
  ],
  priority: [
    { required: true, message: '请输入处理优先级', trigger: 'blur' },
    { type: 'number', message: '必须是数字', trigger: 'blur' }
  ],
  request_timeout: [
    { required: true, message: '请输入请求超时时间', trigger: 'blur' },
    { type: 'number', message: '必须是数字', trigger: 'blur' }
  ],
  retry_count: [
    { required: true, message: '请输入重试次数', trigger: 'blur' },
    { type: 'number', message: '必须是数字', trigger: 'blur' }
  ],
  contact_email: [
    { type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' }
  ]
}

// 获取商户详情（编辑模式）
const fetchMerchantDetail = async () => {
  if (!isEdit.value) return

  const merchantId = route.params.id
  loading.value = true

  try {
    const response = await merchantApi.getDetail(merchantId)
    formData.value = {
      ...response,
      api_secret: '' // 不显示密文
    }
  } catch (error) {
    console.error('获取商家详情失败:', error)
    ElMessage.error('获取商家详情失败')
    // 获取失败时返回列表页
    router.push('/merchant/list')
  } finally {
    loading.value = false
  }
}

// 生成API密钥和密文
const generateApiKey = async () => {
  loading.value = true
  try {
    const response = await merchantApi.generateApiKey()
    formData.value.api_key = response.api_key
    formData.value.api_secret = response.api_secret
    ElMessage.success('生成成功')
  } catch (error) {
    console.error('生成API密钥失败:', error)
    ElMessage.error('生成API密钥失败')
  } finally {
    loading.value = false
  }
}

// 提交表单
const submitForm = async () => {
  if (!formRef.value) return

  await formRef.value.validate(async (valid) => {
    if (valid) {
      loading.value = true
      try {
        if (isEdit.value) {
          // 编辑商家
          await merchantApi.update(formData.value.id, formData.value)
          ElMessage.success('更新成功')
        } else {
          // 添加商家
          await merchantApi.create(formData.value)
          ElMessage.success('添加成功')
        }
        // 操作成功后返回列表页
        router.push('/merchant/list')
      } catch (error) {
        console.error('操作失败:', error)
        ElMessage.error('操作失败: ' + (error.response?.data?.detail || error.message))
      } finally {
        loading.value = false
      }
    }
  })
}

// 返回列表页
const goBack = () => {
  if (loading.value) return

  router.push('/merchant/list')
}

// 重置表单
const resetForm = () => {
  if (formRef.value) {
    formRef.value.resetFields()
  }

  if (!isEdit.value) {
    formData.value = {
      id: null,
      name: '',
      code: '',
      status: true,
      api_key: '',
      api_secret: '',
      callback_url: '',
      allowed_ips: '',
      daily_limit: 100000000,
      hourly_limit: 100000000,
      concurrency_limit: 100,
      priority: 0,
      request_timeout: 30,
      retry_count: 3,
      contact_name: '',
      contact_phone: '',
      contact_email: '',
      remark: ''
    }
  }
}

// 离开页面前确认
const confirmLeave = (callback) => {
  ElMessageBox.confirm('您有未保存的更改，确定要离开吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    callback()
  }).catch(() => {
  })
}

onMounted(() => {
  fetchMerchantDetail()
})
</script>

<template>
  <div class="merchant-form-container">
    <el-card class="merchant-form-card" v-loading="loading">
      <template #header>
        <div class="card-header">
          <span>{{ pageTitle }}</span>
          <div class="operations">
            <el-button @click="goBack">返回</el-button>
            <el-button type="primary" @click="submitForm" :loading="loading">保存</el-button>
          </div>
        </div>
      </template>

      <!-- 商户表单 -->
      <el-form ref="formRef" :model="formData" :rules="rules" label-width="140px" :disabled="loading">
        <el-tabs>
          <el-tab-pane label="基础信息">
            <el-form-item label="商家名称" prop="name">
              <el-input v-model="formData.name" placeholder="请输入商家名称" />
            </el-form-item>

            <el-form-item label="商家编码" prop="code">
              <el-input v-model="formData.code" placeholder="请输入商家编码，用于API调用" />
            </el-form-item>

            <el-form-item label="状态" prop="status">
              <el-switch v-model="formData.status" active-text="启用" inactive-text="禁用" />
            </el-form-item>

            <el-form-item label="联系人" prop="contact_name">
              <el-input v-model="formData.contact_name" placeholder="请输入联系人姓名" />
            </el-form-item>

            <el-form-item label="联系电话" prop="contact_phone">
              <el-input v-model="formData.contact_phone" placeholder="请输入联系电话" />
            </el-form-item>

            <el-form-item label="联系邮箱" prop="contact_email">
              <el-input v-model="formData.contact_email" placeholder="请输入联系邮箱" />
            </el-form-item>
          </el-tab-pane>

          <el-tab-pane label="API配置">
            <el-form-item label="API密钥" prop="api_key">
              <el-input v-model="formData.api_key" placeholder="API密钥" readonly>
              </el-input>
            </el-form-item>

            <el-form-item label="API密文" prop="api_secret">
              <el-input v-model="formData.api_secret" placeholder="API密文" type="password" readonly show-password />
              <div class="form-tip">生成后请妥善保管，不会再显示</div>
            </el-form-item>

            <el-form-item label="回调URL" prop="callback_url">
              <el-input v-model="formData.callback_url" placeholder="请输入回调URL" />
            </el-form-item>

            <el-form-item label="IP白名单" prop="allowed_ips">
              <el-input v-model="formData.allowed_ips" type="textarea" :rows="3" placeholder="请输入IP白名单，多个IP用逗号分隔" />
              <div class="form-tip">留空表示不限制IP</div>
            </el-form-item>
          </el-tab-pane>

          <el-tab-pane label="绑卡配置">
            <el-form-item label="每日绑卡上限" prop="daily_limit">
              <el-input-number v-model="formData.daily_limit" :min="0" :step="100" />
              <div class="form-tip">每天最大绑卡数量</div>
            </el-form-item>

            <el-form-item label="每小时绑卡上限" prop="hourly_limit">
              <el-input-number v-model="formData.hourly_limit" :min="0" :step="50" />
              <div class="form-tip">每小时最大绑卡数量</div>
            </el-form-item>

            <el-form-item label="最大并发数" prop="concurrency_limit">
              <el-input-number v-model="formData.concurrency_limit" :min="1" :max="500" :step="10" />
              <div class="form-tip">同时处理的最大请求数</div>
            </el-form-item>

            <el-form-item label="处理优先级" prop="priority">
              <el-input-number v-model="formData.priority" :min="0" :max="100" :step="1" />
              <div class="form-tip">数字越大优先级越高</div>
            </el-form-item>

            <el-form-item label="请求超时时间" prop="request_timeout">
              <el-input-number v-model="formData.request_timeout" :min="1" :max="120" :step="1" />
              <div class="form-tip">API请求超时时间(秒)</div>
            </el-form-item>

            <el-form-item label="重试次数" prop="retry_count">
              <el-input-number v-model="formData.retry_count" :min="0" :max="10" :step="1" />
              <div class="form-tip">请求失败后的重试次数</div>
            </el-form-item>
          </el-tab-pane>

          <el-tab-pane label="其他信息">
            <el-form-item label="备注" prop="remark">
              <el-input v-model="formData.remark" type="textarea" :rows="5" placeholder="请输入备注信息" />
            </el-form-item>
          </el-tab-pane>
        </el-tabs>

        <div class="form-actions">
          <el-button @click="resetForm">重置</el-button>
          <el-button @click="goBack">取消</el-button>
          <el-button type="primary" @click="submitForm" :loading="loading">保存</el-button>
        </div>
      </el-form>
    </el-card>
  </div>
</template>

<style scoped>
.merchant-form-container {
  padding: 20px;
}

.merchant-form-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-header span {
  font-size: 18px;
  font-weight: bold;
}

.operations {
  display: flex;
  align-items: center;
}

.form-tip {
  font-size: 12px;
  color: #909399;
  margin-top: 5px;
}

.form-actions {
  display: flex;
  justify-content: center;
  margin-top: 30px;
  gap: 20px;
}

:deep(.el-input-number) {
  width: 240px;
}
</style>