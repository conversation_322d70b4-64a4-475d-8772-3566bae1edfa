header={
  "sv": "3",
  "nonce": "1158f8500",
  "timestamp": "1751174220771",
  "signature": "107BAE44C39F6878E2CB2D7F832DE9BDFA77364208514F627473B8B920B3113A",
  "xweb_xhr": "1",
  "version": "45",
  "Sec-Fetch-Site": "cross-site",
  "Sec-Fetch-Mode": "cors",
  "Sec-Fetch-Dest": "empty",
  "Referer": "https://servicewechat.com/wx81d3e1fe4c2e11b4/175/page-frame.html",
  "Accept-Language": "zh-CN,zh;q=0.9"
}
body={
  "cardNo": "2326992090536890765",
  "cardPwd": "811911",
  "currentPage": 0,
  "pageSize": 0,
  "sign": "6b95be8d25574dad856cdf7939bfe469@61fa608e14c37c20fde47c700ec90414",
  "storeId": "",
  "userPhone": ""
}
# response={
#   "logId": "Dp3Es0X0",
#   "status": false,
#   "error": {
#     "errorcode": 10131,
#     "message": "该电子卡已被其他用户绑定",
#     "redirect": null,
#     "validators": null
#   },
#   "data": null
# }