package services

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"runtime"
	"time"

	"walmart-bind-card-processor/internal/config"

	"github.com/go-redis/redis/v8"
	"go.uber.org/zap"
	"gorm.io/gorm"
)

// HealthStatus 健康状态
type HealthStatus string

const (
	HealthStatusHealthy   HealthStatus = "healthy"
	HealthStatusDegraded  HealthStatus = "degraded"
	HealthStatusUnhealthy HealthStatus = "unhealthy"
)

// ComponentHealth 组件健康状态
type ComponentHealth struct {
	Status      HealthStatus           `json:"status"`
	Message     string                 `json:"message,omitempty"`
	LastChecked time.Time              `json:"last_checked"`
	Details     map[string]interface{} `json:"details,omitempty"`
}

// SystemHealth 系统健康状态
type SystemHealth struct {
	Status     HealthStatus                   `json:"status"`
	Timestamp  time.Time                      `json:"timestamp"`
	Version    string                         `json:"version"`
	Uptime     time.Duration                  `json:"uptime"`
	Components map[string]ComponentHealth     `json:"components"`
	Metrics    map[string]interface{}         `json:"metrics"`
}

// HealthCheckService 健康检查服务
type HealthCheckService struct {
	db                *gorm.DB
	redis             *redis.Client
	config            *config.Config
	logger            *zap.Logger
	performanceMonitor *PerformanceMonitor
	
	startTime         time.Time
	version           string
}

// NewHealthCheckService 创建健康检查服务
func NewHealthCheckService(
	db *gorm.DB,
	redis *redis.Client,
	config *config.Config,
	logger *zap.Logger,
	performanceMonitor *PerformanceMonitor,
) *HealthCheckService {
	return &HealthCheckService{
		db:                 db,
		redis:              redis,
		config:             config,
		logger:             logger,
		performanceMonitor: performanceMonitor,
		startTime:          time.Now(),
		version:            "1.0.0", // 可以从构建信息获取
	}
}

// CheckHealth 检查系统健康状态
func (h *HealthCheckService) CheckHealth(ctx context.Context) *SystemHealth {
	health := &SystemHealth{
		Timestamp:  time.Now(),
		Version:    h.version,
		Uptime:     time.Since(h.startTime),
		Components: make(map[string]ComponentHealth),
		Metrics:    make(map[string]interface{}),
	}

	// 检查各个组件
	h.checkDatabase(ctx, health)
	h.checkRedis(ctx, health)
	h.checkSystem(health)
	h.collectMetrics(health)

	// 确定整体状态
	h.determineOverallStatus(health)

	return health
}

// checkDatabase 检查数据库健康状态
func (h *HealthCheckService) checkDatabase(ctx context.Context, health *SystemHealth) {
	component := ComponentHealth{
		LastChecked: time.Now(),
		Details:     make(map[string]interface{}),
	}

	// 检查数据库连接
	sqlDB, err := h.db.DB()
	if err != nil {
		component.Status = HealthStatusUnhealthy
		component.Message = fmt.Sprintf("获取数据库连接失败: %v", err)
		health.Components["database"] = component
		return
	}

	// 检查数据库连接池状态
	stats := sqlDB.Stats()
	component.Details["open_connections"] = stats.OpenConnections
	component.Details["in_use"] = stats.InUse
	component.Details["idle"] = stats.Idle
	component.Details["max_open"] = stats.MaxOpenConnections
	component.Details["wait_count"] = stats.WaitCount
	component.Details["wait_duration"] = stats.WaitDuration.String()

	// Ping数据库
	pingCtx, cancel := context.WithTimeout(ctx, 5*time.Second)
	defer cancel()

	if err := sqlDB.PingContext(pingCtx); err != nil {
		component.Status = HealthStatusUnhealthy
		component.Message = fmt.Sprintf("数据库ping失败: %v", err)
	} else {
		// 检查连接池使用率
		if stats.MaxOpenConnections > 0 {
			usage := float64(stats.InUse) / float64(stats.MaxOpenConnections)
			if usage > 0.9 {
				component.Status = HealthStatusDegraded
				component.Message = fmt.Sprintf("数据库连接池使用率过高: %.1f%%", usage*100)
			} else if usage > 0.8 {
				component.Status = HealthStatusDegraded
				component.Message = fmt.Sprintf("数据库连接池使用率较高: %.1f%%", usage*100)
			} else {
				component.Status = HealthStatusHealthy
				component.Message = "数据库连接正常"
			}
		} else {
			component.Status = HealthStatusHealthy
			component.Message = "数据库连接正常"
		}
	}

	health.Components["database"] = component
}

// checkRedis 检查Redis健康状态
func (h *HealthCheckService) checkRedis(ctx context.Context, health *SystemHealth) {
	component := ComponentHealth{
		LastChecked: time.Now(),
		Details:     make(map[string]interface{}),
	}

	if h.redis == nil {
		component.Status = HealthStatusUnhealthy
		component.Message = "Redis客户端未初始化"
		health.Components["redis"] = component
		return
	}

	// 检查Redis连接池状态
	poolStats := h.redis.PoolStats()
	component.Details["total_conns"] = poolStats.TotalConns
	component.Details["idle_conns"] = poolStats.IdleConns
	component.Details["stale_conns"] = poolStats.StaleConns
	component.Details["hits"] = poolStats.Hits
	component.Details["misses"] = poolStats.Misses

	// Ping Redis
	pingCtx, cancel := context.WithTimeout(ctx, 5*time.Second)
	defer cancel()

	if err := h.redis.Ping(pingCtx).Err(); err != nil {
		component.Status = HealthStatusUnhealthy
		component.Message = fmt.Sprintf("Redis ping失败: %v", err)
	} else {
		// 检查连接池状态
		if poolStats.TotalConns > 0 {
			idleRatio := float64(poolStats.IdleConns) / float64(poolStats.TotalConns)
			if idleRatio < 0.1 {
				component.Status = HealthStatusDegraded
				component.Message = fmt.Sprintf("Redis连接池空闲连接过少: %.1f%%", idleRatio*100)
			} else {
				component.Status = HealthStatusHealthy
				component.Message = "Redis连接正常"
			}
		} else {
			component.Status = HealthStatusHealthy
			component.Message = "Redis连接正常"
		}
	}

	health.Components["redis"] = component
}

// checkSystem 检查系统资源状态
func (h *HealthCheckService) checkSystem(health *SystemHealth) {
	component := ComponentHealth{
		LastChecked: time.Now(),
		Details:     make(map[string]interface{}),
	}

	// 收集系统指标
	var m runtime.MemStats
	runtime.ReadMemStats(&m)

	goroutines := runtime.NumGoroutine()
	memoryMB := m.Alloc / 1024 / 1024

	component.Details["goroutines"] = goroutines
	component.Details["memory_mb"] = memoryMB
	component.Details["gc_cycles"] = m.NumGC
	component.Details["heap_objects"] = m.HeapObjects

	// 评估系统状态
	if goroutines > 2000 {
		component.Status = HealthStatusDegraded
		component.Message = fmt.Sprintf("协程数量过多: %d", goroutines)
	} else if memoryMB > 2048 { // 2GB
		component.Status = HealthStatusDegraded
		component.Message = fmt.Sprintf("内存使用过高: %d MB", memoryMB)
	} else if goroutines > 1000 || memoryMB > 1024 {
		component.Status = HealthStatusDegraded
		component.Message = "系统资源使用较高"
	} else {
		component.Status = HealthStatusHealthy
		component.Message = "系统资源正常"
	}

	health.Components["system"] = component
}

// collectMetrics 收集性能指标
func (h *HealthCheckService) collectMetrics(health *SystemHealth) {
	if h.performanceMonitor != nil {
		// 获取增强的性能指标
		metrics := h.performanceMonitor.GetEnhancedMetrics(h.db)
		if metrics != nil {
			health.Metrics["performance"] = map[string]interface{}{
				"bind_card_count":        metrics.BindCardCount,
				"bind_card_success_rate": metrics.BindCardSuccessRate,
				"preoccupation_count":    metrics.PreoccupationCount,
				"weight_selection_count": metrics.WeightSelectionCount,
				"active_ck_count":        metrics.ActiveCKCount,
				"available_ck_count":     metrics.AvailableCKCount,
			}
		}

		// 获取健康状态
		healthStatus := h.performanceMonitor.CheckSystemHealth(h.db)
		if healthStatus != nil {
			health.Metrics["health_score"] = healthStatus["status"]
			health.Metrics["alerts"] = healthStatus["alerts"]
		}
	}

	// 添加运行时指标
	var m runtime.MemStats
	runtime.ReadMemStats(&m)

	health.Metrics["runtime"] = map[string]interface{}{
		"goroutines":     runtime.NumGoroutine(),
		"memory_alloc":   m.Alloc,
		"memory_sys":     m.Sys,
		"gc_cycles":      m.NumGC,
		"last_gc":        time.Unix(0, int64(m.LastGC)),
		"next_gc":        m.NextGC,
	}
}

// determineOverallStatus 确定整体健康状态
func (h *HealthCheckService) determineOverallStatus(health *SystemHealth) {
	unhealthyCount := 0
	degradedCount := 0
	totalComponents := len(health.Components)

	for _, component := range health.Components {
		switch component.Status {
		case HealthStatusUnhealthy:
			unhealthyCount++
		case HealthStatusDegraded:
			degradedCount++
		}
	}

	// 确定整体状态
	if unhealthyCount > 0 {
		health.Status = HealthStatusUnhealthy
	} else if degradedCount > 0 || float64(degradedCount)/float64(totalComponents) > 0.3 {
		health.Status = HealthStatusDegraded
	} else {
		health.Status = HealthStatusHealthy
	}
}

// ServeHTTP 实现HTTP处理器接口
func (h *HealthCheckService) ServeHTTP(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()
	
	// 检查健康状态
	health := h.CheckHealth(ctx)
	
	// 设置响应头
	w.Header().Set("Content-Type", "application/json")
	
	// 根据健康状态设置HTTP状态码
	switch health.Status {
	case HealthStatusHealthy:
		w.WriteHeader(http.StatusOK)
	case HealthStatusDegraded:
		w.WriteHeader(http.StatusOK) // 降级状态仍返回200，但在响应体中标明
	case HealthStatusUnhealthy:
		w.WriteHeader(http.StatusServiceUnavailable)
	}
	
	// 编码并返回响应
	if err := json.NewEncoder(w).Encode(health); err != nil {
		h.logger.Error("编码健康检查响应失败", zap.Error(err))
		http.Error(w, "Internal Server Error", http.StatusInternalServerError)
	}
}

// GetReadinessCheck 获取就绪检查（简化版健康检查）
func (h *HealthCheckService) GetReadinessCheck(ctx context.Context) map[string]interface{} {
	ready := true
	checks := make(map[string]interface{})
	
	// 检查数据库
	if sqlDB, err := h.db.DB(); err != nil {
		ready = false
		checks["database"] = "failed"
	} else if err := sqlDB.PingContext(ctx); err != nil {
		ready = false
		checks["database"] = "failed"
	} else {
		checks["database"] = "ok"
	}
	
	// 检查Redis
	if h.redis == nil {
		ready = false
		checks["redis"] = "failed"
	} else if err := h.redis.Ping(ctx).Err(); err != nil {
		ready = false
		checks["redis"] = "failed"
	} else {
		checks["redis"] = "ok"
	}
	
	return map[string]interface{}{
		"ready":  ready,
		"checks": checks,
		"timestamp": time.Now(),
	}
}

// GetLivenessCheck 获取存活检查（最基本的检查）
func (h *HealthCheckService) GetLivenessCheck() map[string]interface{} {
	return map[string]interface{}{
		"alive":     true,
		"timestamp": time.Now(),
		"uptime":    time.Since(h.startTime),
		"version":   h.version,
	}
}
