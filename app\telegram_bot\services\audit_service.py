"""
Telegram机器人审计服务
提供全面的审计日志记录、监控和报告功能
"""

import json
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_, func, desc

from app.models.audit import AuditLog, AuditEventType, AuditLevel
from app.models.telegram_user import TelegramUser
from app.models.telegram_bot_log import TelegramBotLog
from app.models.user import User
from app.core.logging import get_logger

logger = get_logger(__name__)


class TelegramAuditService:
    """Telegram审计服务"""
    
    def __init__(self, db: Session):
        self.db = db
    
    def log_user_action(
        self,
        telegram_user_id: int,
        action: str,
        details: Dict[str, Any],
        ip_address: str = None,
        user_agent: str = None,
        result: str = "success"
    ):
        """
        记录用户操作
        
        Args:
            telegram_user_id: Telegram用户ID
            action: 操作类型
            details: 操作详情
            ip_address: IP地址
            user_agent: 用户代理
            result: 操作结果
        """
        try:
            # 获取系统用户ID
            telegram_user = self.db.query(TelegramUser).filter_by(
                telegram_user_id=telegram_user_id
            ).first()
            
            system_user_id = telegram_user.system_user_id if telegram_user else None
            
            audit_log = AuditLog(
                event_type=AuditEventType.USER_ACTION.value,
                level=AuditLevel.INFO.value if result == "success" else AuditLevel.WARNING.value,
                resource_type="telegram_user_action",
                resource_id=str(telegram_user_id),
                action=action,
                details={
                    **details,
                    "result": result,
                    "telegram_user_id": telegram_user_id
                },
                user_id=system_user_id,
                ip_address=ip_address,
                user_agent=user_agent
            )
            
            self.db.add(audit_log)
            self.db.commit()
            
        except Exception as e:
            logger.error(f"记录用户操作失败: {e}")
    
    def log_permission_change(
        self,
        target_user_id: int,
        changed_by: int,
        change_type: str,
        old_permissions: Dict[str, Any],
        new_permissions: Dict[str, Any],
        reason: str = None
    ):
        """
        记录权限变更
        
        Args:
            target_user_id: 目标用户ID
            changed_by: 操作人ID
            change_type: 变更类型
            old_permissions: 旧权限
            new_permissions: 新权限
            reason: 变更原因
        """
        try:
            audit_log = AuditLog(
                event_type=AuditEventType.PERMISSION_CHANGE.value,
                level=AuditLevel.WARNING.value,
                resource_type="telegram_user_permission",
                resource_id=str(target_user_id),
                action=change_type,
                details={
                    "target_user_id": target_user_id,
                    "old_permissions": old_permissions,
                    "new_permissions": new_permissions,
                    "reason": reason,
                    "changes": self._calculate_permission_diff(old_permissions, new_permissions)
                },
                user_id=changed_by
            )
            
            self.db.add(audit_log)
            self.db.commit()
            
        except Exception as e:
            logger.error(f"记录权限变更失败: {e}")
    
    def log_security_event(
        self,
        event_type: str,
        severity: str,
        details: Dict[str, Any],
        user_id: int = None,
        ip_address: str = None
    ):
        """
        记录安全事件
        
        Args:
            event_type: 事件类型
            severity: 严重程度
            details: 事件详情
            user_id: 用户ID
            ip_address: IP地址
        """
        try:
            level_mapping = {
                "low": AuditLevel.INFO.value,
                "medium": AuditLevel.WARNING.value,
                "high": AuditLevel.ERROR.value,
                "critical": AuditLevel.CRITICAL.value
            }
            
            audit_log = AuditLog(
                event_type=AuditEventType.SECURITY.value,
                level=level_mapping.get(severity, AuditLevel.WARNING.value),
                resource_type="telegram_security",
                action=event_type,
                details=details,
                user_id=user_id,
                ip_address=ip_address
            )
            
            self.db.add(audit_log)
            self.db.commit()
            
        except Exception as e:
            logger.error(f"记录安全事件失败: {e}")
    
    def get_user_activity_report(
        self,
        user_id: int = None,
        start_date: datetime = None,
        end_date: datetime = None,
        action_types: List[str] = None
    ) -> Dict[str, Any]:
        """
        获取用户活动报告
        
        Args:
            user_id: 用户ID（可选）
            start_date: 开始日期
            end_date: 结束日期
            action_types: 操作类型列表
            
        Returns:
            Dict: 活动报告
        """
        try:
            # 设置默认时间范围（最近30天）
            if not end_date:
                end_date = datetime.now()
            if not start_date:
                start_date = end_date - timedelta(days=30)
            
            # 构建查询
            query = self.db.query(AuditLog).filter(
                and_(
                    AuditLog.created_at >= start_date,
                    AuditLog.created_at <= end_date,
                    AuditLog.resource_type.like("telegram_%")
                )
            )
            
            if user_id:
                query = query.filter(AuditLog.user_id == user_id)
            
            if action_types:
                query = query.filter(AuditLog.action.in_(action_types))
            
            # 获取活动记录
            activities = query.order_by(desc(AuditLog.created_at)).all()
            
            # 统计分析
            stats = self._calculate_activity_stats(activities)
            
            # 按日期分组
            daily_stats = self._group_activities_by_date(activities)
            
            # 按操作类型分组
            action_stats = self._group_activities_by_action(activities)
            
            return {
                "summary": stats,
                "daily_activities": daily_stats,
                "action_breakdown": action_stats,
                "recent_activities": [
                    {
                        "id": activity.id,
                        "action": activity.action,
                        "resource_type": activity.resource_type,
                        "level": activity.level,
                        "created_at": activity.created_at,
                        "details": activity.details,
                        "ip_address": activity.ip_address
                    }
                    for activity in activities[:50]  # 最近50条记录
                ]
            }
            
        except Exception as e:
            logger.error(f"获取用户活动报告失败: {e}")
            return {"error": str(e)}
    
    def get_security_report(
        self,
        start_date: datetime = None,
        end_date: datetime = None
    ) -> Dict[str, Any]:
        """
        获取安全报告
        
        Args:
            start_date: 开始日期
            end_date: 结束日期
            
        Returns:
            Dict: 安全报告
        """
        try:
            # 设置默认时间范围（最近7天）
            if not end_date:
                end_date = datetime.now()
            if not start_date:
                start_date = end_date - timedelta(days=7)
            
            # 查询安全事件
            security_events = self.db.query(AuditLog).filter(
                and_(
                    AuditLog.event_type == AuditEventType.SECURITY.value,
                    AuditLog.created_at >= start_date,
                    AuditLog.created_at <= end_date
                )
            ).order_by(desc(AuditLog.created_at)).all()
            
            # 统计分析
            total_events = len(security_events)
            critical_events = len([e for e in security_events if e.level == AuditLevel.CRITICAL.value])
            high_events = len([e for e in security_events if e.level == AuditLevel.ERROR.value])
            medium_events = len([e for e in security_events if e.level == AuditLevel.WARNING.value])
            
            # 按事件类型分组
            event_types = {}
            for event in security_events:
                action = event.action
                if action not in event_types:
                    event_types[action] = 0
                event_types[action] += 1
            
            # 按IP地址分组（识别可疑IP）
            ip_stats = {}
            for event in security_events:
                if event.ip_address:
                    if event.ip_address not in ip_stats:
                        ip_stats[event.ip_address] = 0
                    ip_stats[event.ip_address] += 1
            
            # 排序获取最活跃的IP
            top_ips = sorted(ip_stats.items(), key=lambda x: x[1], reverse=True)[:10]
            
            return {
                "summary": {
                    "total_events": total_events,
                    "critical_events": critical_events,
                    "high_events": high_events,
                    "medium_events": medium_events,
                    "period": {
                        "start_date": start_date,
                        "end_date": end_date
                    }
                },
                "event_types": event_types,
                "top_ips": top_ips,
                "recent_critical_events": [
                    {
                        "id": event.id,
                        "action": event.action,
                        "level": event.level,
                        "created_at": event.created_at,
                        "details": event.details,
                        "ip_address": event.ip_address,
                        "user_id": event.user_id
                    }
                    for event in security_events 
                    if event.level in [AuditLevel.CRITICAL.value, AuditLevel.ERROR.value]
                ][:20]
            }
            
        except Exception as e:
            logger.error(f"获取安全报告失败: {e}")
            return {"error": str(e)}
    
    def get_permission_change_report(
        self,
        start_date: datetime = None,
        end_date: datetime = None
    ) -> Dict[str, Any]:
        """
        获取权限变更报告
        
        Args:
            start_date: 开始日期
            end_date: 结束日期
            
        Returns:
            Dict: 权限变更报告
        """
        try:
            # 设置默认时间范围（最近30天）
            if not end_date:
                end_date = datetime.now()
            if not start_date:
                start_date = end_date - timedelta(days=30)
            
            # 查询权限变更事件
            permission_changes = self.db.query(AuditLog).filter(
                and_(
                    AuditLog.event_type == AuditEventType.PERMISSION_CHANGE.value,
                    AuditLog.created_at >= start_date,
                    AuditLog.created_at <= end_date
                )
            ).order_by(desc(AuditLog.created_at)).all()
            
            # 统计分析
            total_changes = len(permission_changes)
            
            # 按变更类型分组
            change_types = {}
            for change in permission_changes:
                action = change.action
                if action not in change_types:
                    change_types[action] = 0
                change_types[action] += 1
            
            # 按操作人分组
            operators = {}
            for change in permission_changes:
                if change.user_id:
                    if change.user_id not in operators:
                        operators[change.user_id] = 0
                    operators[change.user_id] += 1
            
            # 获取操作人详细信息
            operator_details = {}
            for user_id in operators.keys():
                user = self.db.query(User).filter_by(id=user_id).first()
                if user:
                    operator_details[user_id] = {
                        "username": user.username,
                        "name": f"{user.first_name} {user.last_name}".strip(),
                        "changes_count": operators[user_id]
                    }
            
            return {
                "summary": {
                    "total_changes": total_changes,
                    "period": {
                        "start_date": start_date,
                        "end_date": end_date
                    }
                },
                "change_types": change_types,
                "operators": operator_details,
                "recent_changes": [
                    {
                        "id": change.id,
                        "action": change.action,
                        "resource_id": change.resource_id,
                        "created_at": change.created_at,
                        "details": change.details,
                        "user_id": change.user_id
                    }
                    for change in permission_changes[:50]
                ]
            }
            
        except Exception as e:
            logger.error(f"获取权限变更报告失败: {e}")
            return {"error": str(e)}
    
    def _calculate_permission_diff(
        self, 
        old_permissions: Dict[str, Any], 
        new_permissions: Dict[str, Any]
    ) -> Dict[str, Any]:
        """计算权限差异"""
        changes = {
            "added": [],
            "removed": [],
            "modified": []
        }
        
        # 这里实现权限差异计算逻辑
        # 比较两个权限字典的差异
        
        return changes
    
    def _calculate_activity_stats(self, activities: List[AuditLog]) -> Dict[str, Any]:
        """计算活动统计"""
        total_activities = len(activities)
        
        # 按级别统计
        level_stats = {}
        for activity in activities:
            level = activity.level
            if level not in level_stats:
                level_stats[level] = 0
            level_stats[level] += 1
        
        # 按事件类型统计
        event_type_stats = {}
        for activity in activities:
            event_type = activity.event_type
            if event_type not in event_type_stats:
                event_type_stats[event_type] = 0
            event_type_stats[event_type] += 1
        
        return {
            "total_activities": total_activities,
            "level_breakdown": level_stats,
            "event_type_breakdown": event_type_stats
        }
    
    def _group_activities_by_date(self, activities: List[AuditLog]) -> Dict[str, int]:
        """按日期分组活动"""
        daily_stats = {}
        for activity in activities:
            date_key = activity.created_at.strftime("%Y-%m-%d")
            if date_key not in daily_stats:
                daily_stats[date_key] = 0
            daily_stats[date_key] += 1
        
        return daily_stats
    
    def _group_activities_by_action(self, activities: List[AuditLog]) -> Dict[str, int]:
        """按操作类型分组活动"""
        action_stats = {}
        for activity in activities:
            action = activity.action
            if action not in action_stats:
                action_stats[action] = 0
            action_stats[action] += 1
        
        return action_stats
