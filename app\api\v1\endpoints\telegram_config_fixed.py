"""
Telegram配置管理API接口
"""

from typing import Optional, Dict, Any
from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from app.api.deps import get_db, get_current_user
from app.models.user import User
from app.models.telegram_group import TelegramGroup
from app.models.merchant_telegram_setting import MerchantTelegramSetting
from app.models.telegram_permission_template import TelegramPermissionTemplate
from app.services.telegram_config_service import TelegramConfigService
from app.schemas.response import BaseResponse
from app.core.logging import get_logger

logger = get_logger(__name__)
router = APIRouter()


# =====================================================
# 群组配置管理接口
# =====================================================

@router.get("/groups/{group_id}/settings")
async def get_group_settings(
    group_id: int,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """获取群组配置"""
    try:
        # 权限检查
        if not current_user.has_permission("api:telegram:config:read"):
            raise HTTPException(status_code=403, detail="权限不足")
        
        group = db.query(TelegramGroup).filter_by(id=group_id).first()
        if not group:
            raise HTTPException(status_code=404, detail="群组不存在")
        
        # 检查用户是否有权限管理该群组
        if not current_user.can_access_merchant_data(group.merchant_id):
            raise HTTPException(status_code=403, detail="无权限访问该群组")
        
        config_service = TelegramConfigService(db)
        
        return {
                "group_id": group_id,
                "group_info": {
                    "chat_id": group.chat_id,
                    "chat_title": group.chat_title,
                    "merchant_id": group.merchant_id,
                    "department_id": group.department_id,
                    "bind_status": group.bind_status  # bind_status已经是字符串，不需要.value
                },
                "group_settings": config_service.get_group_settings(group),
                "effective_settings": config_service.get_effective_group_settings(group),
                "merchant_settings": config_service.get_merchant_settings(group.merchant_id),
                "global_settings": config_service.get_global_default_settings()
            }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取群组配置失败: {e}")
        raise HTTPException(status_code=500, detail="获取群组配置失败")


@router.put("/groups/{group_id}/settings")
async def update_group_settings(
    group_id: int,
    settings: Dict[str, Any],
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """更新群组配置"""
    try:
        # 权限检查
        if not current_user.has_permission("api:telegram:config:write"):
            raise HTTPException(status_code=403, detail="权限不足")
        
        group = db.query(TelegramGroup).filter_by(id=group_id).first()
        if not group:
            raise HTTPException(status_code=404, detail="群组不存在")
        
        if not current_user.can_access_merchant_data(group.merchant_id):
            raise HTTPException(status_code=403, detail="无权限访问该群组")
        
        config_service = TelegramConfigService(db)
        
        # 验证配置
        is_valid, errors = config_service.validate_settings(settings)
        if not is_valid:
            raise HTTPException(status_code=400, detail=f"配置验证失败: {'; '.join(errors)}")
        
        updated_group = config_service.update_group_settings(group, settings)
        
        logger.info(f"用户 {current_user.username} 更新了群组 {group_id} 的配置")
        
        return {
                "group_id": group_id,
                "updated_settings": updated_group.settings,
                "effective_settings": config_service.get_effective_group_settings(updated_group)
            }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"更新群组配置失败: {e}")
        raise HTTPException(status_code=500, detail="更新群组配置失败")


@router.post("/groups/{group_id}/apply-template")
async def apply_permission_template(
    group_id: int,
    template_code: str,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """应用权限模板到群组"""
    try:
        # 权限检查
        if not current_user.has_permission("api:telegram:config:write"):
            raise HTTPException(status_code=403, detail="权限不足")
        
        group = db.query(TelegramGroup).filter_by(id=group_id).first()
        if not group:
            raise HTTPException(status_code=404, detail="群组不存在")
        
        if not current_user.can_access_merchant_data(group.merchant_id):
            raise HTTPException(status_code=403, detail="无权限访问该群组")
        
        config_service = TelegramConfigService(db)
        updated_group = config_service.apply_permission_template(group, template_code)
        
        logger.info(f"用户 {current_user.username} 为群组 {group_id} 应用了权限模板 {template_code}")
        
        return {
                "group_id": group_id,
                "template_code": template_code,
                "effective_settings": config_service.get_effective_group_settings(updated_group)
            }
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"应用权限模板失败: {e}")
        raise HTTPException(status_code=500, detail="应用权限模板失败")


# =====================================================
# 商户级配置管理接口
# =====================================================

@router.get("/merchants/{merchant_id}/telegram-settings")
async def get_merchant_telegram_settings(
    merchant_id: int,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """获取商户级Telegram配置"""
    try:
        # 权限检查
        if not current_user.has_permission("api:telegram:config:read"):
            raise HTTPException(status_code=403, detail="权限不足")
        
        if not current_user.can_access_merchant_data(merchant_id):
            raise HTTPException(status_code=403, detail="无权限访问该商户")
        
        config_service = TelegramConfigService(db)
        
        # 获取商户配置记录
        merchant_setting = db.query(MerchantTelegramSetting).filter_by(
            merchant_id=merchant_id,
            is_active=True
        ).first()
        
        return {
                "merchant_id": merchant_id,
                "has_custom_settings": merchant_setting is not None,
                "merchant_settings": config_service.get_merchant_settings(merchant_id),
                "global_settings": config_service.get_global_default_settings(),
                "setting_record": {
                    "id": merchant_setting.id if merchant_setting else None,
                    "created_at": merchant_setting.created_at.isoformat() if merchant_setting else None,
                    "updated_at": merchant_setting.updated_at.isoformat() if merchant_setting else None,
                    "created_by": merchant_setting.created_by if merchant_setting else None,
                    "updated_by": merchant_setting.updated_by if merchant_setting else None
                } if merchant_setting else None
            }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取商户Telegram配置失败: {e}")
        raise HTTPException(status_code=500, detail="获取商户配置失败")


@router.put("/merchants/{merchant_id}/telegram-settings")
async def update_merchant_telegram_settings(
    merchant_id: int,
    settings: Dict[str, Any],
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """更新商户级Telegram配置"""
    try:
        # 权限检查
        if not current_user.has_permission("api:telegram:config:write"):
            raise HTTPException(status_code=403, detail="权限不足")
        
        if not current_user.can_access_merchant_data(merchant_id):
            raise HTTPException(status_code=403, detail="无权限访问该商户")
        
        config_service = TelegramConfigService(db)
        
        # 验证配置
        is_valid, errors = config_service.validate_settings(settings)
        if not is_valid:
            raise HTTPException(status_code=400, detail=f"配置验证失败: {'; '.join(errors)}")
        
        updated_setting = config_service.update_merchant_settings(
            merchant_id, 
            settings, 
            current_user.id
        )
        
        logger.info(f"用户 {current_user.username} 更新了商户 {merchant_id} 的Telegram配置")
        
        return {
                "merchant_id": merchant_id,
                "updated_settings": updated_setting.settings,
                "setting_id": updated_setting.id
            }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"更新商户Telegram配置失败: {e}")
        raise HTTPException(status_code=500, detail="更新商户配置失败")


@router.post("/merchants/{merchant_id}/apply-template")
async def apply_template_to_merchant(
    merchant_id: int,
    template_code: str,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """应用权限模板到商户"""
    try:
        # 权限检查
        if not current_user.has_permission("api:telegram:config:write"):
            raise HTTPException(status_code=403, detail="权限不足")
        
        if not current_user.can_access_merchant_data(merchant_id):
            raise HTTPException(status_code=403, detail="无权限访问该商户")
        
        config_service = TelegramConfigService(db)
        updated_setting = config_service.apply_template_to_merchant(
            merchant_id, 
            template_code, 
            current_user.id
        )
        
        logger.info(f"用户 {current_user.username} 为商户 {merchant_id} 应用了权限模板 {template_code}")
        
        return {
                "merchant_id": merchant_id,
                "template_code": template_code,
                "updated_settings": updated_setting.settings
            }
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"应用权限模板到商户失败: {e}")
        raise HTTPException(status_code=500, detail="应用权限模板失败")


# =====================================================
# 权限模板管理接口
# =====================================================

@router.get("/permission-templates")
async def get_permission_templates(
    include_system: bool = True,
    include_custom: bool = True,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """获取权限模板列表"""
    try:
        if not current_user.has_permission("api:telegram:config:read"):
            raise HTTPException(status_code=403, detail="权限不足")

        config_service = TelegramConfigService(db)
        templates = config_service.get_permission_templates(include_system, include_custom)

        return {
                "templates": [
                    {
                        "id": t.id,
                        "template_name": t.template_name,
                        "template_code": t.template_code,
                        "description": t.description,
                        "settings": t.settings,
                        "is_system": t.is_system,
                        "is_active": t.is_active,
                        "created_at": t.created_at.isoformat() if t.created_at else None,
                        "updated_at": t.updated_at.isoformat() if t.updated_at else None
                    }
                    for t in templates
                ],
                "total": len(templates)
            }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取权限模板列表失败: {e}")
        raise HTTPException(status_code=500, detail="获取权限模板列表失败")


@router.post("/permission-templates")
async def create_permission_template(
    template_data: Dict[str, Any],
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """创建权限模板"""
    try:
        if not current_user.has_permission("api:telegram:template:manage"):
            raise HTTPException(status_code=403, detail="权限不足")

        # 验证必需字段
        required_fields = ['template_name', 'template_code', 'settings']
        for field in required_fields:
            if field not in template_data:
                raise HTTPException(status_code=400, detail=f"缺少必需字段: {field}")

        config_service = TelegramConfigService(db)
        template = config_service.create_permission_template(
            template_name=template_data['template_name'],
            template_code=template_data['template_code'],
            description=template_data.get('description', ''),
            settings=template_data['settings'],
            user_id=current_user.id,
            is_system=False  # 用户创建的模板不是系统模板
        )

        logger.info(f"用户 {current_user.username} 创建了权限模板 {template.template_code}")

        return {
                "template": {
                    "id": template.id,
                    "template_name": template.template_name,
                    "template_code": template.template_code,
                    "description": template.description,
                    "settings": template.settings,
                    "is_system": template.is_system,
                    "created_at": template.created_at.isoformat()
                }
            }
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"创建权限模板失败: {e}")
        raise HTTPException(status_code=500, detail="创建权限模板失败")


@router.put("/permission-templates/{template_id}")
async def update_permission_template(
    template_id: int,
    template_data: Dict[str, Any],
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """更新权限模板"""
    try:
        if not current_user.has_permission("api:telegram:template:manage"):
            raise HTTPException(status_code=403, detail="权限不足")

        template = db.query(TelegramPermissionTemplate).filter_by(id=template_id).first()
        if not template:
            raise HTTPException(status_code=404, detail="权限模板不存在")

        # 系统模板不允许修改
        if template.is_system:
            raise HTTPException(status_code=403, detail="系统模板不允许修改")

        # 更新字段
        if 'template_name' in template_data:
            template.template_name = template_data['template_name']
        if 'description' in template_data:
            template.description = template_data['description']
        if 'settings' in template_data:
            template.settings = template_data['settings']
            # 验证配置
            is_valid, errors = template.validate_settings()
            if not is_valid:
                raise HTTPException(status_code=400, detail=f"配置验证失败: {'; '.join(errors)}")

        template.updated_by = current_user.id
        db.commit()
        db.refresh(template)

        logger.info(f"用户 {current_user.username} 更新了权限模板 {template.template_code}")

        return {
                "template": {
                    "id": template.id,
                    "template_name": template.template_name,
                    "template_code": template.template_code,
                    "description": template.description,
                    "settings": template.settings,
                    "updated_at": template.updated_at.isoformat()
                }
            }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"更新权限模板失败: {e}")
        raise HTTPException(status_code=500, detail="更新权限模板失败")


@router.delete("/permission-templates/{template_id}")
async def delete_permission_template(
    template_id: int,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """删除权限模板"""
    try:
        if not current_user.has_permission("api:telegram:template:manage"):
            raise HTTPException(status_code=403, detail="权限不足")

        template = db.query(TelegramPermissionTemplate).filter_by(id=template_id).first()
        if not template:
            raise HTTPException(status_code=404, detail="权限模板不存在")

        # 系统模板不允许删除
        if template.is_system:
            raise HTTPException(status_code=403, detail="系统模板不允许删除")

        # 软删除：设置为不活跃
        template.is_active = False
        template.updated_by = current_user.id
        db.commit()

        logger.info(f"用户 {current_user.username} 删除了权限模板 {template.template_code}")

        return {"template_id": template_id}
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"删除权限模板失败: {e}")
        raise HTTPException(status_code=500, detail="删除权限模板失败")


# =====================================================
# 配置验证和工具接口
# =====================================================

@router.post("/validate-settings")
async def validate_settings(
    settings: Dict[str, Any],
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """验证配置的有效性"""
    try:
        if not current_user.has_permission("api:telegram:config:read"):
            raise HTTPException(status_code=403, detail="权限不足")

        config_service = TelegramConfigService(db)
        is_valid, errors = config_service.validate_settings(settings)

        return {
                "is_valid": is_valid,
                "errors": errors,
                "settings": settings
            }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"配置验证失败: {e}")
        raise HTTPException(status_code=500, detail="配置验证失败")


@router.get("/cache-stats")
async def get_cache_stats(
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """获取缓存统计信息"""
    try:
        if not current_user.has_permission("api:telegram:config:read"):
            raise HTTPException(status_code=403, detail="权限不足")

        config_service = TelegramConfigService(db)
        cache_stats = config_service.get_cache_stats()

        return cache_stats
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取缓存统计失败: {e}")
        raise HTTPException(status_code=500, detail="获取缓存统计失败")


@router.post("/clear-cache")
async def clear_cache(
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """清除配置缓存"""
    try:
        if not current_user.has_permission("api:telegram:config:write"):
            raise HTTPException(status_code=403, detail="权限不足")

        config_service = TelegramConfigService(db)
        config_service.clear_all_cache()

        logger.info(f"用户 {current_user.username} 清除了Telegram配置缓存")

        return {"cleared": True}
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"清除缓存失败: {e}")
        raise HTTPException(status_code=500, detail="清除缓存失败")


# =====================================================
# 全局配置管理接口
# =====================================================

@router.get("/global")
async def get_global_config(
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """获取全局配置"""
    try:
        # 检查权限：仅允许超级管理员
        if not current_user.is_superuser:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="仅超级管理员可以管理全局配置"
            )

        config_service = TelegramConfigService(db)
        global_config = config_service.get_global_config()

        return BaseResponse(
            success=True,
            data=global_config,
            message="获取全局配置成功"
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取全局配置失败: {e}")
        raise HTTPException(status_code=500, detail="获取全局配置失败")


@router.put("/global")
async def update_global_config(
    config_data: Dict[str, Any],
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """更新全局配置"""
    try:
        # 检查权限：仅允许超级管理员
        if not current_user.is_superuser:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="仅超级管理员可以管理全局配置"
            )

        config_service = TelegramConfigService(db)

        # 验证配置
        is_valid, errors = config_service.validate_settings(config_data)
        if not is_valid:
            raise HTTPException(status_code=400, detail=f"配置验证失败: {'; '.join(errors)}")

        # 更新配置
        updated_config = config_service.update_global_config(config_data)

        return BaseResponse(
            success=True,
            data=updated_config,
            message="更新全局配置成功"
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"更新全局配置失败: {e}")
        raise HTTPException(status_code=500, detail="更新全局配置失败")


# =====================================================
# 全局默认配置管理接口
# =====================================================

@router.get("/global-defaults")
async def get_global_default_settings(
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """获取全局默认配置"""
    try:
        if not current_user.has_permission("api:telegram:config:read"):
            raise HTTPException(status_code=403, detail="权限不足")

        config_service = TelegramConfigService(db)
        global_settings = config_service.get_global_default_settings()

        return {
                "global_settings": global_settings,
                "cache_info": {
                    "cached": True,
                    "expire_seconds": config_service._cache_expire
                }
            }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取全局默认配置失败: {e}")
        raise HTTPException(status_code=500, detail="获取全局默认配置失败")


@router.get("/permission-templates/{template_id}")
async def get_permission_template_detail(
    template_id: int,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """获取权限模板详情"""
    try:
        if not current_user.has_permission("api:telegram:config:read"):
            raise HTTPException(status_code=403, detail="权限不足")

        template = db.query(TelegramPermissionTemplate).filter_by(
            id=template_id,
            is_active=True
        ).first()

        if not template:
            raise HTTPException(status_code=404, detail="权限模板不存在")

        return {
                "template": {
                    "id": template.id,
                    "template_name": template.template_name,
                    "template_code": template.template_code,
                    "description": template.description,
                    "settings": template.settings,
                    "is_system": template.is_system,
                    "is_active": template.is_active,
                    "created_at": template.created_at.isoformat() if template.created_at else None,
                    "updated_at": template.updated_at.isoformat() if template.updated_at else None,
                    "created_by": template.created_by,
                    "updated_by": template.updated_by
                }
            }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取权限模板详情失败: {e}")
        raise HTTPException(status_code=500, detail="获取权限模板详情失败")


@router.post("/permission-templates/{template_id}/duplicate")
async def duplicate_permission_template(
    template_id: int,
    new_template_data: Dict[str, Any],
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """复制权限模板"""
    try:
        if not current_user.has_permission("api:telegram:template:manage"):
            raise HTTPException(status_code=403, detail="权限不足")

        # 获取原模板
        original_template = db.query(TelegramPermissionTemplate).filter_by(
            id=template_id,
            is_active=True
        ).first()

        if not original_template:
            raise HTTPException(status_code=404, detail="原权限模板不存在")

        # 验证必需字段
        required_fields = ['template_name', 'template_code']
        for field in required_fields:
            if field not in new_template_data:
                raise HTTPException(status_code=400, detail=f"缺少必需字段: {field}")

        config_service = TelegramConfigService(db)

        # 创建新模板，使用原模板的设置
        new_template = config_service.create_permission_template(
            template_name=new_template_data['template_name'],
            template_code=new_template_data['template_code'],
            description=new_template_data.get('description', f"复制自: {original_template.template_name}"),
            settings=original_template.settings,  # 使用原模板的设置
            user_id=current_user.id,
            is_system=False
        )

        logger.info(f"用户 {current_user.username} 复制了权限模板 {original_template.template_code} 为 {new_template.template_code}")

        return {
                "original_template": {
                    "id": original_template.id,
                    "template_code": original_template.template_code,
                    "template_name": original_template.template_name
                },
                "new_template": {
                    "id": new_template.id,
                    "template_name": new_template.template_name,
                    "template_code": new_template.template_code,
                    "description": new_template.description,
                    "settings": new_template.settings,
                    "created_at": new_template.created_at.isoformat()
                }
            }
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"复制权限模板失败: {e}")
        raise HTTPException(status_code=500, detail="复制权限模板失败")