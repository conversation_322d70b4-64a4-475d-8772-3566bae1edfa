# 沃尔玛绑卡系统CK负载均衡问题修复指南

## 🔍 问题概述

沃尔玛绑卡系统存在严重的负载均衡问题：
- 系统中已添加100个CK，但每次绑卡都使用同一个CK
- 即使当前CK达到20次使用上限，也不会自动切换到其他可用CK
- 负载均衡机制完全失效

## 🔧 问题根本原因

经过深入分析，发现以下根本原因：

### 1. Redis CK服务未正确初始化
- 应用启动时没有初始化Redis CK优化服务
- 系统始终运行在数据库降级模式下
- Redis负载均衡机制未生效

### 2. 数据库模式的隐藏问题
- CK的`bind_count`字段可能统计不准确
- 查询结果可能被缓存
- 事务回滚导致计数更新失效

### 3. 缺乏有效监控
- 系统缺乏对CK服务状态的实时监控
- 负载分布情况不透明
- 问题难以及时发现

## 🛠️ 修复方案

### 立即诊断

首先运行诊断脚本检查当前状态：

```bash
# 诊断所有商户的CK负载均衡状态
python app/scripts/diagnose_ck_load_balance.py

# 诊断特定商户
python app/scripts/diagnose_ck_load_balance.py --merchant-id 1

# 保存诊断结果到文件
python app/scripts/diagnose_ck_load_balance.py --output diagnosis_result.json
```

### 执行修复

运行修复脚本解决问题：

```bash
# 修复所有商户的负载均衡问题
python app/scripts/fix_ck_load_balance.py

# 修复特定商户
python app/scripts/fix_ck_load_balance.py --merchant-id 1

# 强制执行修复（跳过确认）
python app/scripts/fix_ck_load_balance.py --force

# 保存修复结果到文件
python app/scripts/fix_ck_load_balance.py --output fix_result.json
```

### 验证修复效果

修复完成后，再次运行诊断脚本验证：

```bash
python app/scripts/diagnose_ck_load_balance.py
```

## 📊 API监控接口

系统新增了CK负载均衡监控API，可以通过Web界面查看状态：

### 1. 获取负载均衡状态
```
GET /api/v1/ck-monitor/status?merchant_id=1
```

### 2. 获取服务健康状态
```
GET /api/v1/ck-monitor/health
```

### 3. 测试负载均衡功能
```
POST /api/v1/ck-monitor/test-load-balance?merchant_id=1&test_rounds=20
```

### 4. 同步CK数据到Redis
```
POST /api/v1/ck-monitor/sync-redis?merchant_id=1
```

### 5. 获取CK使用统计
```
GET /api/v1/ck-monitor/statistics?merchant_id=1&days=7
```

## 🔄 系统改进

### 1. 应用启动时初始化Redis CK服务

在`app/main.py`中添加了Redis CK服务初始化：

```python
# 初始化Redis CK优化服务
logger.info("正在初始化Redis CK优化服务...")
try:
    from app.services.redis_ck_wrapper import init_redis_optimization
    redis_init_success = await init_redis_optimization()
    if redis_init_success:
        logger.info("Redis CK优化服务初始化成功")
    else:
        logger.warning("Redis CK优化服务初始化失败，将使用数据库模式")
except Exception as e:
    logger.warning(f"Redis CK优化服务初始化失败: {str(e)}")
```

### 2. 增强的负载均衡监控

新增`CKLoadBalanceMonitor`服务，提供：
- 实时监控CK使用情况
- 负载分布分析
- 自动告警机制
- 健康状态检查

### 3. 完善的诊断和修复工具

提供了完整的诊断和修复脚本：
- `diagnose_ck_load_balance.py`: 诊断负载均衡问题
- `fix_ck_load_balance.py`: 自动修复负载均衡问题

## 📈 监控指标

### 负载均衡分数
- 100分：完全均衡
- 60-99分：基本均衡
- 30-59分：轻度不均衡
- 0-29分：严重不均衡

### 告警阈值
- 单个CK使用率超过70%：高级告警
- 最大使用次数超过最小使用次数3倍：中级告警
- Redis连续失败5次：高级告警
- 服务健康分数低于70：中级告警

## 🚀 使用建议

### 1. 定期监控
- 每天检查CK负载均衡状态
- 关注告警信息
- 定期运行诊断脚本

### 2. 预防措施
- 确保Redis服务稳定运行
- 定期同步CK数据到Redis
- 监控CK使用次数统计

### 3. 故障处理
- 发现负载不均衡时立即运行修复脚本
- 检查Redis连接状态
- 验证CK配置正确性

## 🔧 故障排除

### 问题1：Redis连接失败
**症状**：诊断显示Redis连接失败
**解决方案**：
1. 检查Redis服务是否运行
2. 验证Redis配置（主机、端口、密码）
3. 检查网络连接

### 问题2：负载均衡测试失败
**症状**：测试总是选择相同的CK
**解决方案**：
1. 运行修复脚本同步数据
2. 检查CK的bind_count字段
3. 验证CK状态（active=true）

### 问题3：CK使用次数不更新
**症状**：绑卡成功但bind_count不增加
**解决方案**：
1. 检查绑卡成功后的回调逻辑
2. 验证数据库事务提交
3. 检查Redis同步状态

## 📞 技术支持

如果遇到问题，请：
1. 运行诊断脚本收集详细信息
2. 查看应用日志中的错误信息
3. 检查Redis和数据库连接状态
4. 联系技术支持团队

## 📝 更新日志

### v1.0.0 (2024-01-XX)
- 修复Redis CK服务初始化问题
- 添加负载均衡监控功能
- 提供诊断和修复工具
- 增强API监控接口
- 完善文档和使用指南
