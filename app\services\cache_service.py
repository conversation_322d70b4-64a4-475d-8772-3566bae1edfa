"""
Redis缓存服务
提供统一的缓存管理功能，包括热点数据缓存、查询结果缓存等
"""

import json
import logging
from typing import Any, Dict, List, Optional, Union
from datetime import datetime, timedelta

from app.core.cache import redis_manager
from app.core.config import settings

logger = logging.getLogger(__name__)


class CacheService:
    """Redis缓存服务类"""
    
    def __init__(self):
        self.redis = redis_manager.get_client()
        self.default_ttl = 3600  # 默认1小时过期
        
    # ========================================
    # 基础缓存操作
    # ========================================
    
    def get(self, key: str) -> Optional[Any]:
        """获取缓存值"""
        try:
            value = self.redis.get(key)
            if value:
                return json.loads(value)
            return None
        except Exception as e:
            logger.error(f"获取缓存失败 {key}: {e}")
            return None
    
    def set(self, key: str, value: Any, ttl: Optional[int] = None) -> bool:
        """设置缓存值"""
        try:
            ttl = ttl or self.default_ttl
            serialized_value = json.dumps(value, default=str)
            return self.redis.setex(key, ttl, serialized_value)
        except Exception as e:
            logger.error(f"设置缓存失败 {key}: {e}")
            return False
    
    def delete(self, key: str) -> bool:
        """删除缓存"""
        try:
            return bool(self.redis.delete(key))
        except Exception as e:
            logger.error(f"删除缓存失败 {key}: {e}")
            return False
    
    def exists(self, key: str) -> bool:
        """检查缓存是否存在"""
        try:
            return bool(self.redis.exists(key))
        except Exception as e:
            logger.error(f"检查缓存存在性失败 {key}: {e}")
            return False
    
    def expire(self, key: str, ttl: int) -> bool:
        """设置缓存过期时间"""
        try:
            return bool(self.redis.expire(key, ttl))
        except Exception as e:
            logger.error(f"设置缓存过期时间失败 {key}: {e}")
            return False
    
    # ========================================
    # 业务缓存方法
    # ========================================
    
    def get_user_permissions(self, user_id: int) -> Optional[List[str]]:
        """获取用户权限缓存"""
        key = f"user:permissions:{user_id}"
        return self.get(key)
    
    def set_user_permissions(self, user_id: int, permissions: List[str], ttl: int = 1800) -> bool:
        """设置用户权限缓存（30分钟）"""
        key = f"user:permissions:{user_id}"
        return self.set(key, permissions, ttl)
    
    def get_user_roles(self, user_id: int) -> Optional[List[Dict]]:
        """获取用户角色缓存"""
        key = f"user:roles:{user_id}"
        return self.get(key)
    
    def set_user_roles(self, user_id: int, roles: List[Dict], ttl: int = 1800) -> bool:
        """设置用户角色缓存（30分钟）"""
        key = f"user:roles:{user_id}"
        return self.set(key, roles, ttl)
    
    def get_merchant_config(self, merchant_id: int) -> Optional[Dict]:
        """获取商户配置缓存"""
        key = f"merchant:config:{merchant_id}"
        return self.get(key)
    
    def set_merchant_config(self, merchant_id: int, config: Dict, ttl: int = 3600) -> bool:
        """设置商户配置缓存（1小时）"""
        key = f"merchant:config:{merchant_id}"
        return self.set(key, config, ttl)
    
    def get_ck_availability(self, ck_id: int) -> Optional[bool]:
        """获取CK可用性缓存"""
        key = f"ck:availability:{ck_id}"
        return self.get(key)
    
    def set_ck_availability(self, ck_id: int, available: bool, ttl: int = 300) -> bool:
        """设置CK可用性缓存（5分钟）"""
        key = f"ck:availability:{ck_id}"
        return self.set(key, available, ttl)
    
    def get_statistics_cache(self, cache_key: str) -> Optional[Dict]:
        """获取统计数据缓存"""
        key = f"stats:{cache_key}"
        return self.get(key)
    
    def set_statistics_cache(self, cache_key: str, data: Dict, ttl: int = 600) -> bool:
        """设置统计数据缓存（10分钟）"""
        key = f"stats:{cache_key}"
        return self.set(key, data, ttl)
    
    def get_menu_tree(self, role_id: int) -> Optional[List[Dict]]:
        """获取菜单树缓存"""
        key = f"menu:tree:{role_id}"
        return self.get(key)
    
    def set_menu_tree(self, role_id: int, menu_tree: List[Dict], ttl: int = 3600) -> bool:
        """设置菜单树缓存（1小时）"""
        key = f"menu:tree:{role_id}"
        return self.set(key, menu_tree, ttl)
    
    # ========================================
    # 缓存失效方法
    # ========================================
    
    def invalidate_user_cache(self, user_id: int) -> bool:
        """清除用户相关缓存"""
        try:
            keys = [
                f"user:permissions:{user_id}",
                f"user:roles:{user_id}",
            ]
            for key in keys:
                self.delete(key)
            return True
        except Exception as e:
            logger.error(f"清除用户缓存失败 {user_id}: {e}")
            return False
    
    def invalidate_merchant_cache(self, merchant_id: int) -> bool:
        """清除商户相关缓存"""
        try:
            pattern = f"merchant:*:{merchant_id}"
            keys = self.redis.keys(pattern)
            if keys:
                self.redis.delete(*keys)
            return True
        except Exception as e:
            logger.error(f"清除商户缓存失败 {merchant_id}: {e}")
            return False
    
    def invalidate_statistics_cache(self, pattern: str = "stats:*") -> bool:
        """清除统计缓存"""
        try:
            keys = self.redis.keys(pattern)
            if keys:
                self.redis.delete(*keys)
            return True
        except Exception as e:
            logger.error(f"清除统计缓存失败: {e}")
            return False
    
    def invalidate_menu_cache(self) -> bool:
        """清除菜单缓存"""
        try:
            keys = self.redis.keys("menu:*")
            if keys:
                self.redis.delete(*keys)
            return True
        except Exception as e:
            logger.error(f"清除菜单缓存失败: {e}")
            return False
    
    # ========================================
    # 缓存预热方法
    # ========================================
    
    def warm_up_cache(self) -> bool:
        """缓存预热"""
        try:
            logger.info("开始缓存预热...")
            
            # 预热系统配置
            self._warm_up_system_config()
            
            # 预热热点数据
            self._warm_up_hot_data()
            
            logger.info("缓存预热完成")
            return True
        except Exception as e:
            logger.error(f"缓存预热失败: {e}")
            return False
    
    def _warm_up_system_config(self):
        """预热系统配置"""
        # 这里可以预热一些系统级配置
        pass
    
    def _warm_up_hot_data(self):
        """预热热点数据"""
        # 这里可以预热一些热点数据
        pass
    
    # ========================================
    # 缓存监控方法
    # ========================================
    
    def get_cache_info(self) -> Dict[str, Any]:
        """获取缓存信息"""
        try:
            info = self.redis.info()
            return {
                'used_memory': info.get('used_memory_human', 'N/A'),
                'connected_clients': info.get('connected_clients', 0),
                'total_commands_processed': info.get('total_commands_processed', 0),
                'keyspace_hits': info.get('keyspace_hits', 0),
                'keyspace_misses': info.get('keyspace_misses', 0),
                'hit_rate': self._calculate_hit_rate(info),
            }
        except Exception as e:
            logger.error(f"获取缓存信息失败: {e}")
            return {}
    
    def _calculate_hit_rate(self, info: Dict) -> float:
        """计算缓存命中率"""
        hits = info.get('keyspace_hits', 0)
        misses = info.get('keyspace_misses', 0)
        total = hits + misses
        return round(hits / total * 100, 2) if total > 0 else 0.0


# 全局缓存服务实例
cache_service = CacheService()
