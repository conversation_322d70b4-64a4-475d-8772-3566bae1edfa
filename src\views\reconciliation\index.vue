<template>
  <div class="reconciliation-container">
    <!-- 面包屑导航 -->
    <BreadcrumbNavigation v-if="currentLevel > 1" :department-path="route.query.departmentPath"
      :current-page="currentLevelTitle" :filters="filters" @navigate="handleNavigate" />

    <!-- 筛选条件 -->
    <el-card class="filter-card" shadow="never">
      <div class="filter-row">
        <TimeRangeSelector v-model="timeRangeData" @change="handleTimeRangeChange" />

        <div class="filter-actions">
          <el-button type="primary" @click="refreshData" :loading="loading">
            <el-icon>
              <Refresh />
            </el-icon>
            刷新
          </el-button>
          <el-button @click="exportData" :loading="exportLoading">
            <el-icon>
              <Download />
            </el-icon>
            导出
          </el-button>
        </div>
      </div>
    </el-card>



    <!-- 组织统计表格 -->
    <el-card class="table-card" shadow="never">
      <template #header>
        <div class="card-header">
          <span class="card-title">
            <el-icon>
              <List />
            </el-icon>
            {{ filters.viewLevel === 'merchant' ? '商户统计' : '部门统计' }}
          </span>
          <div class="header-actions">
            <el-tooltip content="表格说明" placement="top">
              <el-button text @click="showTableHelp">
                <el-icon>
                  <QuestionFilled />
                </el-icon>
              </el-button>
            </el-tooltip>
          </div>
        </div>
      </template>

      <el-table :data="organizationList" v-loading="loading" stripe style="width: 100%"
        :default-sort="{ prop: 'successAmount', order: 'descending' }">


        <el-table-column prop="name" label="部门名" min-width="150">
          <template #default="{ row }">
            <div class="org-name">
              <el-icon class="org-icon">
                <Shop v-if="row.type === 'merchant'" />
                <OfficeBuilding v-else />
              </el-icon>
              <span>{{ row.name }}</span>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="parentDepartment" label="上级部门" min-width="120" v-if="currentLevel > 1">
          <template #default="{ row }">
            <span class="parent-department">{{ row.parentDepartment }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="merchantName" label="商户名" min-width="200">
          <template #default="{ row }">
            <span class="merchant-name">{{ row.merchantName }}</span>
          </template>
        </el-table-column>

        <el-table-column prop="successCount" label="成功笔数" width="120" sortable>
          <template #default="{ row }">
            <span class="number-value">{{ formatNumber(row.successCount) }}</span>
          </template>
        </el-table-column>

        <el-table-column prop="successAmount" label="成功金额" width="150" sortable>
          <template #default="{ row }">
            <span class="amount-value">{{ formatAmount(row.successAmount) }}</span>
          </template>
        </el-table-column>



        <el-table-column label="操作" width="120" fixed="right">
          <template #default="{ row }">
            <el-button type="primary" size="small" @click="viewDetails(row)" :disabled="row.successCount === 0">
              {{ getActionButtonText(row) }}
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-wrapper" v-if="pagination.total > 0">
        <el-pagination v-model:current-page="pagination.currentPage" v-model:page-size="pagination.pageSize"
          :page-sizes="[10, 20, 50, 100]" :total="pagination.total" layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange" @current-change="handleCurrentChange" />
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Money,
  Refresh,
  Download,
  List,
  QuestionFilled,
  Shop,
  OfficeBuilding,
  ArrowLeft
} from '@element-plus/icons-vue'
import { useRouter, useRoute } from 'vue-router'
import TimeRangeSelector from '@/components/common/TimeRangeSelector.vue'
import BreadcrumbNavigation from '@/components/common/BreadcrumbNavigation.vue'
import { useUserStore } from '@/store/modules/user'
import { usePermissionStore } from '@/store/modules/permission'
import { reconciliationApi } from '@/api'

// Store实例
const userStore = useUserStore()
const permissionStore = usePermissionStore()
const router = useRouter()
const route = useRoute()

// 响应式数据
const loading = ref(false)
const exportLoading = ref(false)
const customDateRange = ref([])

// 筛选条件 - 从URL参数初始化，避免硬编码默认值
const filters = reactive({
  timeRange: route.query.timeRange || 'today',
  merchantId: null,
  viewLevel: 'department',
  startDate: route.query.startDate || '',
  endDate: route.query.endDate || ''
})

// 时间范围数据（用于TimeRangeSelector组件）- 从URL参数初始化
const timeRangeData = ref({
  timeRange: route.query.timeRange || 'today',
  startDate: route.query.startDate || '',
  endDate: route.query.endDate || ''
})

// 分页
const pagination = reactive({
  currentPage: 1,
  pageSize: 20,
  total: 0
})

// 当前层级信息
const currentLevel = ref(parseInt(route.query.level) || 1)
const parentDepartmentId = ref(route.query.parentDepartmentId || null)
const parentDepartmentName = ref(route.query.parentDepartmentName || '')

// 处理面包屑导航
const handleNavigate = (routeInfo) => {
  router.push(routeInfo)
}

// 计算属性
const currentLevelTitle = computed(() => {
  if (currentLevel.value === 1) {
    return '部门统计'
  } else {
    return `${parentDepartmentName.value} - 子部门统计`
  }
})

const pageTitle = computed(() => {
  if (currentLevel.value === 1) {
    return '对账台 - 成功绑卡统计'
  } else {
    return `${parentDepartmentName.value} - 子部门统计`
  }
})

const pageDescription = computed(() => {
  if (currentLevel.value === 1) {
    return '统计绑卡成功且获取到实际金额的记录，回调状态不影响统计结果'
  } else {
    return `查看${parentDepartmentName.value}下各子部门的成功绑卡统计数据`
  }
})

// 组织数据
const organizationList = ref([])

// 获取组织数据
const getOrganizationData = async () => {
  try {
    const params = {
      page: pagination.currentPage,
      page_size: pagination.pageSize,
      parent_department_id: parentDepartmentId.value,
      level: currentLevel.value,
      time_range: filters.timeRange,
      merchant_id: filters.merchantId
    }

    // 只有在有有效日期时才添加日期参数
    if (filters.startDate && filters.startDate.trim() !== '') {
      params.start_date = filters.startDate
    }
    if (filters.endDate && filters.endDate.trim() !== '') {
      params.end_date = filters.endDate
    }

    const response = await reconciliationApi.getDepartmentStatistics(params)

    if (response && response.data) {
      organizationList.value = response.data
      pagination.total = response.total
      pagination.currentPage = response.page
      pagination.pageSize = response.pageSize
    }
  } catch (error) {
    console.error('获取部门统计数据失败:', error)
    ElMessage.error('获取部门统计数据失败')
    organizationList.value = []
  }
}



// 方法
const formatNumber = (num) => {
  return num ? num.toLocaleString() : '0'
}

const formatAmount = (amount) => {
  if (!amount) return '¥0'
  return `¥${(amount / 100).toLocaleString()}`
}

// 设置时间范围
const setTimeRange = (range) => {
  filters.timeRange = range
  if (range !== 'custom') {
    customDateRange.value = []
  }
  refreshData()
}

// 处理时间范围变化
const handleTimeRangeChange = (timeRange) => {
  // 同步到filters
  filters.timeRange = timeRange.timeRange
  filters.startDate = timeRange.startDate
  filters.endDate = timeRange.endDate

  // 刷新数据
  refreshData()
}

const handleCustomDateChange = () => {
  if (customDateRange.value && customDateRange.value.length === 2) {
    filters.startDate = customDateRange.value[0]
    filters.endDate = customDateRange.value[1]
    refreshData()
  }
}

const handleMerchantChange = () => {
  refreshData()
}

const handleViewLevelChange = () => {
  refreshData()
}

const refreshData = async () => {
  loading.value = true
  try {
    await getOrganizationData()
    ElMessage.success('数据刷新成功')
  } catch (error) {
    ElMessage.error('数据刷新失败')
  } finally {
    loading.value = false
  }
}

const exportData = async () => {
  exportLoading.value = true
  try {
    const params = {
      parent_department_id: parentDepartmentId.value,
      level: currentLevel.value,
      time_range: filters.timeRange,
      merchant_id: filters.merchantId
    }

    // 只有在有有效日期时才添加日期参数
    if (filters.startDate && filters.startDate.trim() !== '') {
      params.start_date = filters.startDate
    }
    if (filters.endDate && filters.endDate.trim() !== '') {
      params.end_date = filters.endDate
    }

    await reconciliationApi.exportDepartmentStatistics(params)
    ElMessage.success('导出成功')
  } catch (error) {
    console.error('导出失败:', error)
    ElMessage.error('导出失败')
  } finally {
    exportLoading.value = false
  }
}

// 返回顶级
const goToTopLevel = () => {
  router.push({
    name: 'Reconciliation',
    query: {
      timeRange: filters.timeRange,
      startDate: filters.startDate,
      endDate: filters.endDate,
      _t: Date.now() // 添加时间戳强制刷新
    }
  })
}



// 获取操作按钮文本
const getActionButtonText = (row) => {
  if (row.hasChildren) {
    return '明细'
  } else {
    return '明细'
  }
}

// 统一的查看详情方法
const viewDetails = (row) => {
  if (row.hasChildren) {
    // 查看子部门
    viewSubDepartments(row)
  } else {
    // 查看CK明细
    viewCkDetails(row)
  }
}

// 构建完整的部门路径
const buildDepartmentPath = () => {
  const path = []

  // 从路由查询参数中获取当前的层级信息
  const currentLevelValue = route.query.level ? parseInt(route.query.level) : 1
  const parentDepartmentName = route.query.parentDepartmentName
  const parentDepartmentId = route.query.parentDepartmentId

  // 构建从一级部门到当前层级的完整路径
  if (currentLevelValue > 1) {
    // 如果有现有的部门路径，先解析它
    const existingPath = route.query.departmentPath
    if (existingPath) {
      try {
        const departments = JSON.parse(existingPath)
        path.push(...departments)
      } catch (e) {
        // 解析失败，使用简单的父部门信息
        if (parentDepartmentName && parentDepartmentId) {
          path.push({
            id: parentDepartmentId,
            name: parentDepartmentName,
            level: currentLevelValue - 1
          })
        }
      }
    } else if (parentDepartmentName && parentDepartmentId) {
      // 没有现有路径，但有父部门信息
      path.push({
        id: parentDepartmentId,
        name: parentDepartmentName,
        level: currentLevelValue - 1
      })
    }
  }

  return path
}

// 查看子部门
const viewSubDepartments = (row) => {
  // 构建完整的部门路径
  const currentPath = buildDepartmentPath()

  // 添加当前部门到路径中
  currentPath.push({
    id: row.id,
    name: row.name,
    level: row.level
  })

  // 跳转到子部门页面
  router.push({
    name: 'Reconciliation',
    query: {
      parentDepartmentId: row.id,
      parentDepartmentName: row.name,
      level: row.level + 1,
      departmentPath: JSON.stringify(currentPath), // 传递完整的部门路径
      timeRange: filters.timeRange,
      startDate: filters.startDate,
      endDate: filters.endDate,
      merchantId: filters.merchantId,
      viewLevel: filters.viewLevel,
      _t: Date.now() // 添加时间戳强制刷新
    }
  })
}

// 查看CK明细
const viewCkDetails = (row) => {
  // 构建完整的部门路径，包含当前正在查看的部门
  const departmentPath = buildDepartmentPath()

  // 添加当前正在查看的部门到路径中
  departmentPath.push({
    id: row.id,
    name: row.name,
    level: row.level
  })

  console.log('🔍 跳转到CK详情，传递的部门路径:', departmentPath)

  router.push({
    name: 'ReconciliationCkDetails',
    params: {
      organizationId: row.id,
      organizationType: row.type
    },
    query: {
      name: row.name,
      merchantName: row.merchantName,
      departmentPath: JSON.stringify(departmentPath),
      timeRange: filters.timeRange,
      startDate: filters.startDate,
      endDate: filters.endDate,
      merchantId: filters.merchantId,
      viewLevel: filters.viewLevel
    }
  })
}

const showTableHelp = () => {
  ElMessageBox.alert(
    '• 成功笔数：绑卡状态为成功且获取到实际金额的记录数量\n' +
    '• 成功金额：所有成功绑卡记录的实际金额总和\n' +
    '• 平均金额：成功金额除以成功笔数\n' +
    '• CK数量：参与成功绑卡的CK数量/总CK数量\n' +
    '• 回调状态不影响绑卡成功统计',
    '统计说明',
    {
      confirmButtonText: '知道了'
    }
  )
}

const handleSizeChange = (size) => {
  pagination.pageSize = size
  refreshData()
}

const handleCurrentChange = (page) => {
  pagination.currentPage = page
  refreshData()
}

// 初始化
// 初始化数据的函数
const initializeData = async () => {
  // 根据URL参数初始化层级信息
  currentLevel.value = parseInt(route.query.level) || 1
  parentDepartmentId.value = route.query.parentDepartmentId || null
  parentDepartmentName.value = route.query.parentDepartmentName || ''

  // 如果有时间范围参数，更新筛选条件（只在参数变化时更新）
  if (route.query.timeRange && route.query.timeRange !== filters.timeRange) {
    filters.timeRange = route.query.timeRange
    filters.startDate = route.query.startDate || ''
    filters.endDate = route.query.endDate || ''

    // 同步到timeRangeData
    timeRangeData.value = {
      timeRange: filters.timeRange,
      startDate: filters.startDate,
      endDate: filters.endDate
    }
  }

  // 获取数据
  await getOrganizationData()
}

// 监听路由变化
watch(() => route.query, async () => {
  await initializeData()
  // 同步timeRangeData
  timeRangeData.value = {
    timeRange: filters.timeRange,
    startDate: filters.startDate,
    endDate: filters.endDate
  }
}, { deep: true })

onMounted(async () => {
  // 根据用户权限设置默认值
  if (!permissionStore.isSuperAdmin && userStore.merchantId) {
    filters.merchantId = userStore.merchantId
  }

  await initializeData()
})
</script>

<style scoped>
.reconciliation-container {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: calc(100vh - 60px);
}



/* 页面标题 */
.page-header {
  margin-bottom: 20px;
}

.header-content {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 24px;
  border-radius: 12px;
  color: white;
}

.page-title {
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 8px;
}

.page-description {
  margin: 0;
  font-size: 14px;
  opacity: 0.9;
}

/* 筛选卡片 */
.filter-card {
  margin-bottom: 20px;
  border: none;
}

.filter-row {
  display: flex;
  align-items: center;
  gap: 20px;
  flex-wrap: wrap;
}

.filter-group {
  display: flex;
  align-items: center;
  gap: 8px;
}

.filter-label {
  font-size: 14px;
  color: #606266;
  white-space: nowrap;
}



.filter-actions {
  margin-left: auto;
  display: flex;
  gap: 12px;
}



/* 表格卡片 */
.table-card {
  border: none;
}

.card-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.card-title {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  display: flex;
  align-items: center;
  gap: 8px;
}

.header-actions {
  display: flex;
  gap: 8px;
}

/* 表格样式 */
.org-name {
  display: flex;
  align-items: center;
  gap: 8px;
}

.org-icon {
  color: #409eff;
}

.merchant-name {
  color: #606266;
}

.parent-department {
  color: #909399;
  font-size: 14px;
}

.number-value {
  font-weight: 600;
  color: #67c23a;
}

.amount-value {
  font-weight: 600;
  color: #409eff;
}

/* 分页 */
.pagination-wrapper {
  margin-top: 20px;
  display: flex;
  justify-content: center;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .reconciliation-container {
    padding: 12px;
  }

  .filter-row {
    flex-direction: column;
    align-items: stretch;
    gap: 12px;
  }

  .filter-actions {
    margin-left: 0;
    justify-content: center;
  }

  .stat-content {
    gap: 12px;
  }

  .stat-icon {
    width: 40px;
    height: 40px;
    font-size: 20px;
  }

  .stat-value {
    font-size: 20px;
  }
}

/* 加载状态 */
.el-loading-mask {
  border-radius: 8px;
}

/* 表格行悬停效果 */
:deep(.el-table__row:hover) {
  background-color: #f5f7fa;
}

/* 按钮样式优化 */
.el-button--small {
  padding: 5px 12px;
  font-size: 12px;
}

/* 标签样式 */
.el-tag--small {
  font-size: 11px;
  padding: 0 6px;
  height: 20px;
  line-height: 18px;
}
</style>
