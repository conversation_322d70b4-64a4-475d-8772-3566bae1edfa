from typing import Callable
from fastapi import FastAPI, Request, Response
from starlette.middleware.base import BaseHTTPMiddleware
from starlette.responses import JSONResponse
from app.core.logging import get_logger
from app.core.exceptions import BusinessException

# 移除不再使用的响应函数导入

# 创建日志记录器
logger = get_logger("middleware")


class UnifiedPermissionMiddleware(BaseHTTPMiddleware):
    """
    统一权限验证中间件
    基于用户菜单权限进行API访问控制，确保数据安全
    """

    # 不需要权限验证的路径
    EXCLUDED_PATHS = {
        "/api/v1/auth/login",
        "/api/v1/auth/logout",
        "/api/v1/auth/refresh",
        "/api/v1/card-bind",  # 绑卡接口使用API Key验证，不需要token验证
        "/api/v1/health",     # 健康检查接口
        "/api/v1/totp",       # TOTP个人安全设置，所有登录用户都可访问
        "/api/v1/users/change-password",  # 密码修改接口，所有登录用户都可访问
        "/api/v1/telegram/webhook",  # Telegram webhook接口，使用secret token验证
        "/docs",
        "/redoc",
        "/openapi.json",
        "/health"
    }

    # API路径与菜单代码的映射关系（基于菜单权限控制API访问）
    PATH_MENU_MAPPING = {
        # 仪表盘相关
        "/api/v1/dashboard": "dashboard",

        # 商户管理相关
        "/api/v1/merchants": "merchant",

        # 部门管理相关
        "/api/v1/departments": "merchant:department",

        # 用户管理相关
        "/api/v1/users": "system:user",

        # 角色管理相关
        "/api/v1/roles": "system:role",

        # 菜单管理相关
        "/api/v1/menus": "system:menu",

        # 绑卡数据相关（卡记录查询）
        "/api/v1/cards": "cards",

        # 沃尔玛CK用户配置相关
        "/api/v1/walmart-ck": "walmart:user",

        # 沃尔玛服务器配置相关
        "/api/v1/walmart-server": "walmart:walmart",

        # 绑卡日志相关
        "/api/v1/binding-logs": "cards",

        # 通知配置相关
        "/api/v1/notification-configs": "notification",

        # 通知相关
        "/api/v1/notifications": "notification",

        # 绑卡相关
        "/api/v1/bind": "bind",
        "/api/v1/single-bind": "single-bind",
        "/api/v1/batch-bind": "batch-bind",

        # 系统设置相关（只有超级管理员可访问）
        "/api/v1/system-settings": "system:settings",

    }

    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        """
        统一权限验证处理
        """
        try:
            # 检查是否是API请求
            if not request.url.path.startswith("/api"):
                return await call_next(request)

            # OPTIONS请求（CORS预检请求）直接放行
            if request.method == "OPTIONS":
                return await call_next(request)

            # 检查是否是排除的路径
            if self._is_excluded_path(request.url.path):
                return await call_next(request)

            # 验证用户身份和权限
            user_info = await self._verify_user_permission(request)
            if not user_info:
                return JSONResponse(
                    status_code=401,
                    content={
                        "code": 401,
                        "message": "未授权访问",
                        "data": None
                    }
                )

            # 将用户信息添加到请求中，供后续使用
            request.state.current_user = user_info

            # 继续处理请求
            return await call_next(request)

        except Exception as e:
            logger.error(f"权限验证中间件异常: {str(e)}", exc_info=True)
            return JSONResponse(
                status_code=500,
                content={
                    "code": 500,
                    "message": "服务器内部错误",
                    "data": None
                }
            )

    def _is_excluded_path(self, path: str) -> bool:
        """检查是否是排除的路径"""
        # 精确匹配排除路径
        if path in self.EXCLUDED_PATHS:
            return True

        # 认证相关路径排除
        if path.startswith("/api/v1/auth/"):
            return True

        # 公共接口排除
        if path.startswith("/api/v1/public/"):
            return True

        # 用户个人信息接口排除（已登录用户都可以访问）
        if path == "/api/v1/users/me":
            return True

        # 用户菜单接口排除（已登录用户都可以访问）
        if path == "/api/v1/menus/user-menus":
            return True

        # 商户切换相关接口排除（已登录用户都可以访问）
        if path.startswith("/api/v1/merchants/switch"):
            return True

        # TOTP个人安全设置接口排除（已登录用户都可以访问）
        if path.startswith("/api/v1/totp"):
            return True

        # 通知中心接口排除（已登录用户都可以访问，但有数据隔离）
        if path.startswith("/api/v1/notifications"):
            return True

        return False

    async def _verify_user_permission(self, request: Request) -> dict:
        """验证用户权限"""
        try:
            # 1. 获取token
            authorization = request.headers.get("Authorization")
            if not authorization or not authorization.startswith("Bearer "):
                return None

            token = authorization.split(" ")[1]

            # 2. 解析token获取用户信息
            try:
                from jose import jwt
                from app.core.config import settings
                payload = jwt.decode(token, settings.SECRET_KEY, algorithms=[settings.ALGORITHM])
                user_id = payload.get("sub")
                if not user_id:
                    return None
            except Exception:
                return None

            # 3. 获取用户详细信息
            try:
                from app.crud.user import user
                from app.db.session import SessionLocal

                db = SessionLocal()
                try:
                    user_obj = user.get(db, id=user_id)
                    if not user_obj or not user_obj.is_active:
                        return None

                    user_info = {
                        "id": user_obj.id,
                        "username": user_obj.username,
                        "merchant_id": user_obj.merchant_id,
                        "department_id": user_obj.department_id,
                        "is_superuser": user_obj.is_superuser
                    }
                finally:
                    db.close()
            except Exception as e:
                logger.error(f"获取用户信息失败: {str(e)}")
                return None

            # 4. 检查API权限（基于动态权限系统）
            has_permission = await self._check_user_api_permission(user_info, request.url.path)
            if not has_permission:
                logger.warning(f"用户 {user_info['username']} 访问 {request.url.path} API权限不足")
                return None

            return user_info

        except Exception as e:
            logger.error(f"权限验证失败: {str(e)}")
            return None

    def _get_required_menu(self, path: str) -> str:
        """根据路径获取所需的菜单代码"""
        # 精确匹配
        if path in self.PATH_MENU_MAPPING:
            return self.PATH_MENU_MAPPING[path]

        # 模糊匹配（处理带ID的路径）
        for pattern, menu_code in self.PATH_MENU_MAPPING.items():
            # 处理带ID的路径，如 /api/v1/users/123
            if path.startswith(pattern + "/"):
                return menu_code

        return None

    async def _check_user_api_permission(self, user_info: dict, api_path: str) -> bool:
        """检查用户是否有指定API的权限"""
        try:
            from app.services.permission_service import PermissionService
            from app.db.session import SessionLocal
            from app.crud.user import user

            db = SessionLocal()
            try:
                # 获取完整的用户对象
                user_obj = user.get(db, id=user_info["id"])
                if not user_obj:
                    return False

                # 使用动态权限服务检查API权限
                permission_service = PermissionService(db)
                return permission_service.check_api_permission(user_obj, api_path)
            finally:
                db.close()
        except Exception as e:
            logger.error(f"API权限检查失败: {str(e)}")
            return False


class ResponseFormatterMiddleware(BaseHTTPMiddleware):
    """
    响应格式化中间件
    用于确保所有API响应都遵循统一的格式，包括：
    {
        "code": 0,         # 0 表示成功，其他值表示错误码
        "data": {},        # 响应数据
        "message": ""      # 响应消息
    }
    """

    async def _log_server_error(self, response: Response, request: Request):
        """记录服务器错误日志"""
        if response.status_code >= 500:
            logger.error(
                f"服务器错误: 状态码 {response.status_code}, 路径: {request.url.path}"
            )
            try:
                # 检查响应是否有body方法
                if hasattr(response, 'body') and callable(getattr(response, 'body')):
                    body = await response.body()
                    if body:
                        logger.error(f"错误响应内容: {body.decode('utf-8')}")
                else:
                    logger.error("响应对象不支持读取body内容")
            except Exception as e:
                logger.error(f"无法读取响应内容: {str(e)}")

    def _should_skip_formatting(self, request: Request, response: Response) -> bool:
        """检查是否应该跳过格式化"""
        # 非API请求跳过
        if not request.url.path.startswith("/api"):
            return True

        # 重定向或文件响应跳过
        if response.status_code in (301, 302, 307, 308) or not hasattr(response, "body"):
            return True

        # 非JSON响应跳过
        content_type = response.headers.get("content-type")
        if content_type and "application/json" not in content_type:
            return True

        return False

    def _is_already_formatted(self, content) -> bool:
        """检查响应是否已经是统一格式"""
        return (
            isinstance(content, dict)
            and "code" in content
            and "data" in content
            and "message" in content
        )

    def _format_success_response(self, content, response: Response) -> JSONResponse:
        """格式化成功响应"""
        # 检查是否是分页数据
        if isinstance(content, dict) and all(
            key in content for key in ["items", "total", "page", "page_size"]
        ):
            # 分页响应格式
            formatted_content = {
                "code": 0,
                "data": {
                    "items": content["items"],
                    "total": content["total"],
                    "page": content["page"],
                    "page_size": content["page_size"],
                    "pages": content.get("pages", (content["total"] + content["page_size"] - 1) // content["page_size"])
                },
                "message": "操作成功"
            }
            # 如果有额外字段（如unread_count），添加到data中
            for key, value in content.items():
                if key not in ["items", "total", "page", "page_size", "pages"]:
                    formatted_content["data"][key] = value
        else:
            # 普通响应格式
            formatted_content = {
                "code": 0,
                "data": content,
                "message": "操作成功"
            }

        return JSONResponse(
            content=formatted_content,
            status_code=response.status_code,
            headers=dict(response.headers),
        )

    def _format_error_response(self, content, response: Response) -> JSONResponse:
        """格式化错误响应"""
        error_msg = (
            content.get("detail", "操作失败")
            if isinstance(content, dict)
            else "操作失败"
        )
        formatted_content = {
            "code": 1,
            "message": error_msg,
            "data": (
                content
                if isinstance(content, dict) and "detail" not in content
                else None
            )
        }
        return JSONResponse(
            content=formatted_content,
            status_code=response.status_code,
            headers=dict(response.headers),
        )

    async def _parse_and_format_response(self, response: Response) -> Response:
        """解析并格式化响应内容"""
        try:
            # 检查响应是否有body方法
            if not hasattr(response, 'body') or not callable(getattr(response, 'body')):
                return response

            body = await response.body()
            if not body:
                return response

            import json
            content = json.loads(body)

            # 如果响应已经是统一格式，直接返回
            if self._is_already_formatted(content):
                return response

            # 根据状态码格式化响应
            if 200 <= response.status_code < 400:
                return self._format_success_response(content, response)
            else:
                return self._format_error_response(content, response)

        except Exception:
            # 处理解析错误，直接返回原始响应
            return response

    def _create_error_response(self, error_message: str) -> JSONResponse:
        """创建错误响应"""
        return JSONResponse(
            status_code=500,
            content={
                "code": 1,
                "message": f"服务器内部错误: {error_message}",
                "data": None,
            },
        )

    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        """
        处理请求并格式化响应
        """
        try:
            # 处理请求
            response = await call_next(request)

            # 记录服务器错误日志
            await self._log_server_error(response, request)

            # 检查是否需要跳过格式化
            if self._should_skip_formatting(request, response):
                return response

            # 解析并格式化响应
            return await self._parse_and_format_response(response)

        except Exception as e:
            # 记录中间件异常
            logger.error(f"响应格式化中间件异常: {str(e)}", exc_info=True)
            return self._create_error_response(str(e))


def setup_middlewares(app: FastAPI) -> None:
    """
    设置中间件

    Args:
        app: FastAPI应用实例
    """
    logger.info("开始设置中间件...")

    # 添加统一权限验证中间件
    logger.info("添加统一权限验证中间件")
    app.add_middleware(UnifiedPermissionMiddleware)

    # 添加响应格式化中间件
    logger.info("添加响应格式化中间件")
    app.add_middleware(ResponseFormatterMiddleware)

    logger.info("中间件设置完成")


async def exception_handler(request: Request, exc: BusinessException):
    """统一异常处理"""
    logger.error(f"业务异常: {exc.message} | 路径: {request.url.path}")
    return JSONResponse(
        status_code=exc.code,
        content={"code": exc.code, "message": exc.message, "data": exc.data},
    )


def setup_exception_handlers(app):
    """设置异常处理器"""
    app.add_exception_handler(BusinessException, exception_handler)
