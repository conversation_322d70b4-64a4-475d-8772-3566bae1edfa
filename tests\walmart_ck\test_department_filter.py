"""
测试沃尔玛CK管理页面的部门筛选功能
验证统计数据和列表数据的一致性
"""
import pytest
import requests
import json
from typing import Dict, Any


class TestWalmartCKDepartmentFilter:
    """测试CK管理页面部门筛选功能"""
    
    BASE_URL = "http://localhost:8000"
    
    def setup_method(self):
        """测试前准备"""
        # 登录获取token
        self.admin_token = self._login("admin", "7c222fb2927d828af22f592134e8932480637c0d")
        self.merchant_token = self._login("test1", "12345678")
        
    def _login(self, username: str, password: str) -> str:
        """登录获取token"""
        login_data = {
            "username": username,
            "password": password
        }
        headers = {"Content-Type": "application/x-www-form-urlencoded"}
        response = requests.post(f"{self.BASE_URL}/api/v1/auth/login", data=login_data, headers=headers)
        print(f"Login response status: {response.status_code}")
        print(f"Login response body: {response.text}")
        assert response.status_code == 200
        response_data = response.json()
        if "data" in response_data:
            return response_data["data"]["access_token"]
        else:
            return response_data["access_token"]
    
    def _get_headers(self, token: str) -> Dict[str, str]:
        """获取请求头"""
        return {
            "Authorization": f"Bearer {token}",
            "Content-Type": "application/json"
        }
    
    def test_ck_list_without_department_filter(self):
        """测试不带部门筛选的CK列表"""
        headers = self._get_headers(self.admin_token)
        response = requests.get(f"{self.BASE_URL}/api/v1/walmart-ck", headers=headers)
        
        assert response.status_code == 200
        data = response.json()["data"]
        assert "items" in data
        assert "total" in data
        
        # 记录总数用于后续比较
        self.total_ck_count = data["total"]
        self.all_cks = data["items"]
        
    def test_ck_list_with_department_filter(self):
        """测试带部门筛选的CK列表"""
        # 先获取部门列表
        headers = self._get_headers(self.admin_token)
        dept_response = requests.get(f"{self.BASE_URL}/api/v1/departments", headers=headers)
        assert dept_response.status_code == 200
        
        departments = dept_response.json()["data"]["items"]
        if not departments:
            pytest.skip("没有部门数据，跳过部门筛选测试")
        
        # 选择第一个部门进行测试
        test_department = departments[0]
        department_id = test_department["id"]
        
        # 测试带部门筛选的CK列表
        params = {"department_id": department_id}
        response = requests.get(f"{self.BASE_URL}/api/v1/walmart-ck", headers=headers, params=params)
        
        assert response.status_code == 200
        data = response.json()["data"]
        
        # 验证返回的CK都属于指定部门
        for ck in data["items"]:
            assert ck["department_id"] == department_id, f"CK {ck['id']} 不属于部门 {department_id}"
    
    def test_ck_statistics_with_department_filter(self):
        """测试带部门筛选的CK统计"""
        # 先获取部门列表
        headers = self._get_headers(self.admin_token)
        dept_response = requests.get(f"{self.BASE_URL}/api/v1/departments", headers=headers)
        assert dept_response.status_code == 200
        
        departments = dept_response.json()["data"]["items"]
        if not departments:
            pytest.skip("没有部门数据，跳过部门筛选测试")
        
        # 选择第一个部门进行测试
        test_department = departments[0]
        department_id = test_department["id"]
        
        # 测试带部门筛选的统计数据
        params = {"department_id": department_id}
        response = requests.get(f"{self.BASE_URL}/api/v1/walmart-ck/binding-amount-statistics", 
                              headers=headers, params=params)
        
        assert response.status_code == 200
        data = response.json()["data"]
        
        # 验证统计数据结构
        assert "summary" in data
        assert "ck_details" in data
    
    def test_data_consistency_between_list_and_statistics(self):
        """测试列表数据和统计数据的一致性"""
        # 先获取部门列表
        headers = self._get_headers(self.admin_token)
        dept_response = requests.get(f"{self.BASE_URL}/api/v1/departments", headers=headers)
        assert dept_response.status_code == 200
        
        departments = dept_response.json()["data"]["items"]
        if not departments:
            pytest.skip("没有部门数据，跳过一致性测试")
        
        # 选择第一个部门进行测试
        test_department = departments[0]
        department_id = test_department["id"]
        
        # 获取列表数据
        list_params = {"department_id": department_id}
        list_response = requests.get(f"{self.BASE_URL}/api/v1/walmart-ck", 
                                   headers=headers, params=list_params)
        assert list_response.status_code == 200
        list_data = list_response.json()["data"]
        
        # 获取统计数据
        stats_params = {"department_id": department_id}
        stats_response = requests.get(f"{self.BASE_URL}/api/v1/walmart-ck/binding-amount-statistics", 
                                    headers=headers, params=stats_params)
        assert stats_response.status_code == 200
        stats_data = stats_response.json()["data"]
        
        # 验证数据一致性
        list_ck_count = len(list_data["items"])
        stats_ck_count = len(stats_data.get("ck_details", []))
        
        # 统计数据中的CK数量应该与列表中的CK数量一致
        assert list_ck_count == stats_ck_count, \
            f"列表CK数量({list_ck_count})与统计CK数量({stats_ck_count})不一致"
        
        # 验证列表中的所有CK都属于指定部门
        for ck in list_data["items"]:
            assert ck["department_id"] == department_id, \
                f"列表中的CK {ck['id']} 不属于部门 {department_id}"
    
    def test_merchant_data_isolation(self):
        """测试商户数据隔离"""
        # 使用商户账号登录
        headers = self._get_headers(self.merchant_token)
        
        # 获取商户的CK列表
        response = requests.get(f"{self.BASE_URL}/api/v1/walmart-ck", headers=headers)
        assert response.status_code == 200
        
        data = response.json()["data"]
        
        # 验证返回的CK都属于当前商户
        for ck in data["items"]:
            # 商户用户应该只能看到自己商户的CK
            # 这里需要根据实际的商户ID进行验证
            assert ck["merchant_id"] is not None, f"CK {ck['id']} 缺少商户ID"
    
    def test_department_filter_with_merchant_account(self):
        """测试商户账号的部门筛选功能"""
        # 使用商户账号登录
        headers = self._get_headers(self.merchant_token)
        
        # 先获取商户可见的部门列表
        dept_response = requests.get(f"{self.BASE_URL}/api/v1/departments", headers=headers)
        
        if dept_response.status_code == 200:
            departments = dept_response.json()["data"]["items"]
            if departments:
                # 选择第一个部门进行测试
                test_department = departments[0]
                department_id = test_department["id"]
                
                # 测试带部门筛选的CK列表
                params = {"department_id": department_id}
                response = requests.get(f"{self.BASE_URL}/api/v1/walmart-ck", 
                                      headers=headers, params=params)
                
                assert response.status_code == 200
                data = response.json()["data"]
                
                # 验证返回的CK都属于指定部门
                for ck in data["items"]:
                    assert ck["department_id"] == department_id, \
                        f"CK {ck['id']} 不属于部门 {department_id}"


if __name__ == "__main__":
    # 运行测试
    pytest.main([__file__, "-v"])
