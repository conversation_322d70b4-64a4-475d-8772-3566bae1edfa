#!/usr/bin/env python3
"""
CK Redis缓存同步修复脚本

用于诊断和修复CK状态在数据库和Redis之间的不一致问题
"""

import asyncio
import sys
import os
from typing import List, Dict, Any, Optional

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from sqlalchemy.orm import Session
from app.core.database import SessionLocal
from app.core.redis import get_redis
from app.models.walmart_ck import WalmartCK
from app.services.redis_ck_service import CKDataSyncService
from app.core.logging import get_logger

logger = get_logger("ck_redis_sync_fix")


class CKRedisSyncFixer:
    """CK Redis缓存同步修复器"""
    
    def __init__(self):
        self.db: Optional[Session] = None
        self.redis = None
        self.sync_service = None
    
    async def initialize(self):
        """初始化连接"""
        try:
            self.db = SessionLocal()
            self.redis = await get_redis()
            self.sync_service = CKDataSyncService(self.redis, self.db)
            logger.info("初始化完成")
        except Exception as e:
            logger.error(f"初始化失败: {e}")
            raise
    
    async def diagnose_ck_status(self, merchant_id: Optional[int] = None) -> Dict[str, Any]:
        """
        诊断CK状态一致性
        
        Args:
            merchant_id: 可选的商户ID，如果提供则只检查该商户的CK
            
        Returns:
            Dict[str, Any]: 诊断结果
        """
        try:
            # 查询数据库中的CK
            query = self.db.query(WalmartCK).filter(WalmartCK.is_deleted == False)
            if merchant_id:
                query = query.filter(WalmartCK.merchant_id == merchant_id)
            
            db_cks = query.all()
            
            inconsistent_cks = []
            missing_in_redis = []
            
            for ck in db_cks:
                # 检查Redis中的状态
                status_key = f"walmart:ck:status:{ck.id}"
                redis_status = await self.redis.hgetall(status_key)
                
                if not redis_status:
                    missing_in_redis.append({
                        'ck_id': ck.id,
                        'merchant_id': ck.merchant_id,
                        'department_id': ck.department_id,
                        'db_active': ck.active,
                        'reason': 'Redis中不存在该CK状态'
                    })
                    continue
                
                redis_active = bool(int(redis_status.get('active', 0)))
                
                if ck.active != redis_active:
                    inconsistent_cks.append({
                        'ck_id': ck.id,
                        'merchant_id': ck.merchant_id,
                        'department_id': ck.department_id,
                        'db_active': ck.active,
                        'redis_active': redis_active,
                        'reason': '数据库和Redis状态不一致'
                    })
                
                # 检查CK池状态
                if ck.active:
                    pool_key = f"walmart:ck:pool:{ck.merchant_id}"
                    pool_score = await self.redis.zscore(pool_key, str(ck.id))
                    
                    if pool_score is None:
                        inconsistent_cks.append({
                            'ck_id': ck.id,
                            'merchant_id': ck.merchant_id,
                            'department_id': ck.department_id,
                            'db_active': ck.active,
                            'redis_active': redis_active,
                            'reason': '活跃CK未在商户池中'
                        })
            
            result = {
                'total_cks': len(db_cks),
                'inconsistent_count': len(inconsistent_cks),
                'missing_in_redis_count': len(missing_in_redis),
                'inconsistent_cks': inconsistent_cks,
                'missing_in_redis': missing_in_redis
            }
            
            logger.info(f"诊断完成: 总CK数={len(db_cks)}, 不一致={len(inconsistent_cks)}, Redis缺失={len(missing_in_redis)}")
            
            return result
            
        except Exception as e:
            logger.error(f"诊断CK状态失败: {e}")
            raise
    
    async def fix_ck_sync_issues(self, merchant_id: Optional[int] = None) -> Dict[str, Any]:
        """
        修复CK同步问题
        
        Args:
            merchant_id: 可选的商户ID，如果提供则只修复该商户的CK
            
        Returns:
            Dict[str, Any]: 修复结果
        """
        try:
            # 先诊断问题
            diagnosis = await self.diagnose_ck_status(merchant_id)
            
            if diagnosis['inconsistent_count'] == 0 and diagnosis['missing_in_redis_count'] == 0:
                logger.info("没有发现同步问题")
                return {
                    'fixed_count': 0,
                    'message': '没有发现同步问题'
                }
            
            # 获取需要修复的CK
            query = self.db.query(WalmartCK).filter(WalmartCK.is_deleted == False)
            if merchant_id:
                query = query.filter(WalmartCK.merchant_id == merchant_id)
            
            cks_to_fix = query.all()
            fixed_count = 0
            
            for ck in cks_to_fix:
                try:
                    await self.sync_service.sync_ck_to_redis(ck)
                    fixed_count += 1
                    logger.info(f"已修复CK {ck.id} 的同步问题")
                except Exception as e:
                    logger.error(f"修复CK {ck.id} 失败: {e}")
            
            logger.info(f"修复完成: 共修复 {fixed_count} 个CK")
            
            return {
                'fixed_count': fixed_count,
                'total_issues': diagnosis['inconsistent_count'] + diagnosis['missing_in_redis_count'],
                'message': f'成功修复 {fixed_count} 个CK的同步问题'
            }
            
        except Exception as e:
            logger.error(f"修复CK同步问题失败: {e}")
            raise
    
    async def cleanup_redis_orphaned_data(self) -> Dict[str, Any]:
        """清理Redis中的孤立数据"""
        try:
            cleaned_keys = []
            
            # 查找所有CK状态键
            pattern = "walmart:ck:status:*"
            async for key in self.redis.scan_iter(match=pattern):
                ck_id = int(key.split(':')[-1])
                
                # 检查数据库中是否存在该CK
                ck = self.db.query(WalmartCK).filter(WalmartCK.id == ck_id).first()
                
                if not ck or ck.is_deleted:
                    # 删除孤立的Redis数据
                    await self.redis.delete(key)
                    cleaned_keys.append(key)
                    logger.info(f"清理孤立的Redis键: {key}")
            
            return {
                'cleaned_count': len(cleaned_keys),
                'cleaned_keys': cleaned_keys,
                'message': f'清理了 {len(cleaned_keys)} 个孤立的Redis键'
            }
            
        except Exception as e:
            logger.error(f"清理Redis孤立数据失败: {e}")
            raise
    
    def close(self):
        """关闭连接"""
        if self.db:
            self.db.close()


async def main():
    """主函数"""
    fixer = CKRedisSyncFixer()
    
    try:
        await fixer.initialize()
        
        print("=== CK Redis缓存同步诊断和修复工具 ===")
        print("1. 诊断所有商户的CK同步状态")
        print("2. 修复所有商户的CK同步问题")
        print("3. 诊断指定商户的CK同步状态")
        print("4. 修复指定商户的CK同步问题")
        print("5. 清理Redis中的孤立数据")
        print("0. 退出")
        
        while True:
            choice = input("\n请选择操作 (0-5): ").strip()
            
            if choice == '0':
                break
            elif choice == '1':
                result = await fixer.diagnose_ck_status()
                print(f"\n诊断结果:")
                print(f"总CK数: {result['total_cks']}")
                print(f"不一致CK数: {result['inconsistent_count']}")
                print(f"Redis缺失CK数: {result['missing_in_redis_count']}")
                
                if result['inconsistent_cks']:
                    print("\n不一致的CK:")
                    for ck in result['inconsistent_cks'][:10]:  # 只显示前10个
                        print(f"  CK {ck['ck_id']}: DB={ck['db_active']}, Redis={ck.get('redis_active', 'N/A')} - {ck['reason']}")
                
            elif choice == '2':
                result = await fixer.fix_ck_sync_issues()
                print(f"\n修复结果: {result['message']}")
                
            elif choice == '3':
                merchant_id = input("请输入商户ID: ").strip()
                if merchant_id.isdigit():
                    result = await fixer.diagnose_ck_status(int(merchant_id))
                    print(f"\n商户 {merchant_id} 诊断结果:")
                    print(f"总CK数: {result['total_cks']}")
                    print(f"不一致CK数: {result['inconsistent_count']}")
                    print(f"Redis缺失CK数: {result['missing_in_redis_count']}")
                else:
                    print("无效的商户ID")
                    
            elif choice == '4':
                merchant_id = input("请输入商户ID: ").strip()
                if merchant_id.isdigit():
                    result = await fixer.fix_ck_sync_issues(int(merchant_id))
                    print(f"\n修复结果: {result['message']}")
                else:
                    print("无效的商户ID")
                    
            elif choice == '5':
                result = await fixer.cleanup_redis_orphaned_data()
                print(f"\n清理结果: {result['message']}")
                
            else:
                print("无效的选择")
    
    except KeyboardInterrupt:
        print("\n操作被用户中断")
    except Exception as e:
        logger.error(f"执行失败: {e}")
        print(f"执行失败: {e}")
    finally:
        fixer.close()


if __name__ == "__main__":
    asyncio.run(main())
