# Walmart 绑卡系统站点配置

# HTTP 重定向到 HTTPS
server {
    listen 80;
    server_name api.walmart-bind-card.com localhost;
    
    # 健康检查不重定向
    location /health {
        proxy_pass http://walmart_app;
        access_log off;
    }
    
    # 其他请求重定向到 HTTPS
    location / {
        return 301 https://$server_name$request_uri;
    }
}

# HTTPS 主站点
server {
    listen 443 ssl http2;
    server_name api.walmart-bind-card.com localhost;

    # SSL 配置
    ssl_certificate /etc/nginx/ssl/walmart-bind-card.crt;
    ssl_certificate_key /etc/nginx/ssl/walmart-bind-card.key;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512:ECDHE-RSA-AES256-GCM-SHA384:DHE-RSA-AES256-GCM-SHA384;
    ssl_prefer_server_ciphers off;
    ssl_session_cache shared:SSL:10m;
    ssl_session_timeout 10m;
    ssl_stapling on;
    ssl_stapling_verify on;

    # 安全头
    add_header X-Frame-Options DENY always;
    add_header X-Content-Type-Options nosniff always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header Referrer-Policy "strict-origin-when-cross-origin" always;
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;
    add_header Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline'; img-src 'self' data:; font-src 'self';" always;

    # 日志配置
    access_log /var/log/nginx/walmart-bind-card.access.log main;
    error_log /var/log/nginx/walmart-bind-card.error.log;

    # 根路径重定向到 API 文档
    location = / {
        return 302 /docs;
    }

    # API 接口
    location /api/ {
        # 限流
        limit_req zone=api burst=20 nodelay;
        
        # 代理配置
        proxy_pass http://walmart_app;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header X-Forwarded-Host $host;
        proxy_set_header X-Forwarded-Port $server_port;
        
        # 禁用缓存
        proxy_cache off;
        proxy_no_cache 1;
        proxy_cache_bypass 1;
        
        # 错误处理
        proxy_intercept_errors on;
        error_page 502 503 504 /50x.html;
    }

    # 登录接口特殊限流
    location /api/v1/auth/login {
        limit_req zone=login burst=5 nodelay;
        
        proxy_pass http://walmart_app;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    # 绑卡接口（商户调用）
    location /api/v1/card-bind {
        # 更严格的限流
        limit_req zone=api burst=10 nodelay;
        
        proxy_pass http://walmart_app;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # 增加超时时间
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
    }

    # API 文档
    location /docs {
        proxy_pass http://walmart_app;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    # ReDoc 文档
    location /redoc {
        proxy_pass http://walmart_app;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    # OpenAPI JSON
    location /openapi.json {
        proxy_pass http://walmart_app;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # 缓存 OpenAPI 规范
        proxy_cache_valid 200 5m;
    }

    # 健康检查
    location /health {
        proxy_pass http://walmart_app;
        access_log off;
        
        # 内部健康检查不限流
        allow 127.0.0.1;
        allow **********/12;
        allow ***********/16;
        allow 10.0.0.0/8;
    }

    # 静态文件缓存
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
        proxy_pass http://walmart_app;
        expires 1y;
        add_header Cache-Control "public, immutable";
        add_header Vary Accept-Encoding;
    }

    # 错误页面
    location /50x.html {
        root /usr/share/nginx/html;
        internal;
    }

    # 禁止访问隐藏文件
    location ~ /\. {
        deny all;
        access_log off;
        log_not_found off;
    }

    # 禁止访问备份文件
    location ~* \.(bak|backup|old|orig|save|swp|tmp)$ {
        deny all;
        access_log off;
        log_not_found off;
    }
}

# 开发环境配置（仅 HTTP）
server {
    listen 80;
    server_name localhost 127.0.0.1;
    
    # 开发环境不强制 HTTPS
    location / {
        proxy_pass http://walmart_app;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
