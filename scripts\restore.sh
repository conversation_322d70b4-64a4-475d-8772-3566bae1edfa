#!/bin/bash

# Walmart 绑卡系统数据库恢复脚本
# 使用方法: ./restore.sh <backup_file.sql.gz>

set -e

# 检查参数
if [ $# -ne 1 ]; then
    echo "使用方法: $0 <backup_file.sql.gz>"
    echo "示例: $0 /backups/daily/walmart_card_db_daily_20231221_120000.sql.gz"
    exit 1
fi

BACKUP_FILE="$1"

# 检查备份文件是否存在
if [ ! -f "${BACKUP_FILE}" ]; then
    echo "错误: 备份文件不存在: ${BACKUP_FILE}"
    exit 1
fi

# 配置变量
DB_HOST="mysql"
DB_NAME="walmart_card_db"
DB_USER="walmart_user"
TEMP_DIR="/tmp"
TEMP_SQL="${TEMP_DIR}/restore_$(date +%Y%m%d_%H%M%S).sql"

# 从密钥文件读取密码
if [ -f "/run/secrets/mysql_password" ]; then
    DB_PASSWORD=$(cat /run/secrets/mysql_password)
else
    echo "错误: 无法读取数据库密码"
    exit 1
fi

echo "=== 数据库恢复操作 ==="
echo "备份文件: ${BACKUP_FILE}"
echo "目标数据库: ${DB_NAME}"
echo "恢复时间: $(date)"
echo ""

# 确认操作
read -p "警告: 此操作将覆盖现有数据库。是否继续? (yes/no): " confirm
if [ "${confirm}" != "yes" ]; then
    echo "操作已取消"
    exit 0
fi

echo "开始恢复数据库..."

# 解压备份文件
echo "解压备份文件..."
gunzip -c "${BACKUP_FILE}" > "${TEMP_SQL}"

# 验证解压后的文件
if [ ! -f "${TEMP_SQL}" ] || [ ! -s "${TEMP_SQL}" ]; then
    echo "错误: 解压失败或文件为空"
    rm -f "${TEMP_SQL}"
    exit 1
fi

echo "解压完成，文件大小: $(du -h ${TEMP_SQL} | cut -f1)"

# 创建恢复前备份
echo "创建恢复前备份..."
RECOVERY_BACKUP="/tmp/pre_restore_backup_$(date +%Y%m%d_%H%M%S).sql.gz"
mysqldump \
    --host="${DB_HOST}" \
    --user="${DB_USER}" \
    --password="${DB_PASSWORD}" \
    --single-transaction \
    --routines \
    --triggers \
    --events \
    --add-drop-database \
    --databases "${DB_NAME}" | gzip > "${RECOVERY_BACKUP}"

echo "恢复前备份已保存: ${RECOVERY_BACKUP}"

# 执行恢复
echo "执行数据库恢复..."
mysql \
    --host="${DB_HOST}" \
    --user="${DB_USER}" \
    --password="${DB_PASSWORD}" < "${TEMP_SQL}"

# 验证恢复结果
echo "验证恢复结果..."
TABLE_COUNT=$(mysql \
    --host="${DB_HOST}" \
    --user="${DB_USER}" \
    --password="${DB_PASSWORD}" \
    --database="${DB_NAME}" \
    --execute="SHOW TABLES;" | wc -l)

if [ ${TABLE_COUNT} -gt 1 ]; then
    echo "恢复成功! 数据库包含 $((TABLE_COUNT-1)) 个表"
else
    echo "警告: 恢复后数据库表数量异常"
fi

# 清理临时文件
rm -f "${TEMP_SQL}"

echo ""
echo "=== 恢复完成 ==="
echo "恢复时间: $(date)"
echo "恢复前备份: ${RECOVERY_BACKUP}"
echo ""
echo "建议执行以下检查:"
echo "1. 检查应用程序连接"
echo "2. 验证关键数据完整性"
echo "3. 运行应用程序测试"
