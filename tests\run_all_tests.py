#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
沃尔玛绑卡系统全面测试套件运行器
运行所有测试模块并生成详细报告
"""

import sys
import os
import time
import json
import subprocess
import importlib.util
from typing import Dict, List, Tuple
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(__file__)))

from test.conftest import save_test_report, print_test_summary

class WalmartTestRunner:
    """沃尔玛绑卡系统测试运行器"""
    
    def __init__(self):
        self.test_modules = [
            # 认证模块测试
            {
                "name": "认证模块测试",
                "module": "test.auth.test_auth",
                "description": "测试用户登录、登出、Token验证等功能"
            },
            # 用户管理测试
            {
                "name": "用户CRUD测试",
                "module": "test.users.test_users_crud",
                "description": "测试用户的创建、读取、更新、删除功能"
            },
            # 商户管理测试
            {
                "name": "商户CRUD测试",
                "module": "test.merchants.test_merchants_crud",
                "description": "测试商户的创建、读取、更新、删除功能"
            },
            # 部门管理测试
            {
                "name": "部门CRUD测试",
                "module": "test.departments.test_departments_crud",
                "description": "测试部门的创建、读取、更新、删除功能"
            },
            # 角色权限测试
            {
                "name": "角色权限测试",
                "module": "test.roles.test_roles_permissions",
                "description": "测试角色管理和权限验证功能"
            },
            # 卡记录管理测试
            {
                "name": "卡记录管理测试",
                "module": "test.cards.test_cards_crud",
                "description": "测试卡记录的查询、统计、敏感信息获取等功能"
            },
            # 沃尔玛CK管理测试
            {
                "name": "沃尔玛CK管理测试",
                "module": "test.walmart_ck.test_walmart_ck_crud",
                "description": "测试沃尔玛CK的创建、读取、更新、删除功能"
            },
            # 绑定日志测试
            {
                "name": "绑定日志测试",
                "module": "test.binding_logs.test_binding_logs",
                "description": "测试绑定日志的查询和统计功能"
            },
            # 通知管理测试
            {
                "name": "通知管理测试",
                "module": "test.notifications.test_notifications_crud",
                "description": "测试通知的创建、读取、更新、删除功能"
            },
            # 仪表盘测试
            {
                "name": "仪表盘测试",
                "module": "test.dashboard.test_dashboard",
                "description": "测试仪表盘统计数据和图表功能"
            },
            # API安全测试
            {
                "name": "API安全测试",
                "module": "test.security.test_api_security",
                "description": "测试SQL注入、XSS、权限绕过等安全漏洞"
            },
            # 跨边界数据操作测试
            {
                "name": "跨边界数据操作测试",
                "module": "test.security.test_cross_boundary_operations",
                "description": "全面测试跨商户、跨部门、跨角色的数据操作权限控制"
            },
            # 部门层级权限测试
            {
                "name": "部门层级权限测试",
                "module": "test.security.test_department_hierarchy_permissions",
                "description": "测试多层级部门结构的权限继承、数据隔离和访问控制"
            },
            # 数据隔离测试
            {
                "name": "数据隔离测试",
                "module": "test.security.test_data_isolation",
                "description": "测试商户间、部门间的数据隔离功能"
            }
        ]
        self.results = []
        self.start_time = None
        self.end_time = None
    
    def run_single_test_module(self, test_module: Dict) -> Tuple[bool, List, float, str]:
        """运行单个测试模块"""
        module_name = test_module["name"]
        module_path = test_module["module"]
        
        print(f"\n{'='*80}")
        print(f"运行 {module_name}")
        print(f"{test_module['description']}")
        print(f"{'='*80}")
        
        start_time = time.time()
        
        try:
            # 动态导入测试模块
            module = importlib.import_module(module_path)
            
            # 查找测试套件类
            test_suite_class = None
            for attr_name in dir(module):
                attr = getattr(module, attr_name)
                if (isinstance(attr, type) and 
                    attr_name.endswith('TestSuite') and 
                    hasattr(attr, 'run_all_tests')):
                    test_suite_class = attr
                    break
            
            if not test_suite_class:
                end_time = time.time()
                duration = end_time - start_time
                return False, [], duration, f"未找到测试套件类"
            
            # 运行测试
            test_suite = test_suite_class()
            test_results = test_suite.run_all_tests()
            
            end_time = time.time()
            duration = end_time - start_time
            
            # 检查测试是否成功
            failed_tests = [r for r in test_results if not r.get("success", False)]
            success = len(failed_tests) == 0
            
            return success, test_results, duration, ""
            
        except Exception as e:
            end_time = time.time()
            duration = end_time - start_time
            error_msg = f"运行测试模块时发生异常: {str(e)}"
            print(f"[ERROR] {error_msg}")
            return False, [], duration, error_msg
    
    def run_all_tests(self) -> Dict:
        """运行所有测试模块"""
        print("开始沃尔玛绑卡系统全面测试套件")
        print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"测试模块数量: {len(self.test_modules)}")
        print(f"测试服务器: http://localhost:20000")
        
        self.start_time = time.time()
        all_test_results = []
        module_summaries = []
        
        for i, test_module in enumerate(self.test_modules, 1):
            print(f"\n进度: {i}/{len(self.test_modules)}")

            success, test_results, duration, error_msg = self.run_single_test_module(test_module)
            
            # 记录模块摘要
            module_summary = {
                "module_name": test_module["name"],
                "module_path": test_module["module"],
                "success": success,
                "test_count": len(test_results),
                "passed_count": sum(1 for r in test_results if r.get("success", False)),
                "failed_count": sum(1 for r in test_results if not r.get("success", False)),
                "duration": duration,
                "error_message": error_msg,
                "timestamp": time.time()
            }
            module_summaries.append(module_summary)
            
            # 添加模块标识到每个测试结果
            for result in test_results:
                result["module"] = test_module["name"]
                result["module_path"] = test_module["module"]
            
            all_test_results.extend(test_results)
            
            # 实时显示结果
            if success:
                passed = module_summary["passed_count"]
                total = module_summary["test_count"]
                print(f"[PASS] {test_module['name']} 完成 - {passed}/{total} 通过 ({duration:.2f}秒)")
            else:
                failed = module_summary["failed_count"]
                total = module_summary["test_count"]
                print(f"[FAIL] {test_module['name']} 失败 - {failed}/{total} 失败 ({duration:.2f}秒)")
                if error_msg:
                    print(f"   错误: {error_msg}")
        
        self.end_time = time.time()
        
        # 生成综合报告
        return self.generate_comprehensive_report(all_test_results, module_summaries)
    
    def generate_comprehensive_report(self, all_results: List, module_summaries: List) -> Dict:
        """生成综合测试报告"""
        total_duration = self.end_time - self.start_time
        total_tests = len(all_results)
        passed_tests = sum(1 for r in all_results if r.get("success", False))
        failed_tests = total_tests - passed_tests
        success_rate = (passed_tests / total_tests * 100) if total_tests > 0 else 0
        
        # 按模块统计
        module_stats = {}
        for summary in module_summaries:
            module_stats[summary["module_name"]] = {
                "total": summary["test_count"],
                "passed": summary["passed_count"],
                "failed": summary["failed_count"],
                "success_rate": (summary["passed_count"] / summary["test_count"] * 100) if summary["test_count"] > 0 else 0,
                "duration": summary["duration"],
                "success": summary["success"]
            }
        
        # 失败的测试详情
        failed_test_details = [r for r in all_results if not r.get("success", False)]
        
        report = {
            "summary": {
                "total_tests": total_tests,
                "passed_tests": passed_tests,
                "failed_tests": failed_tests,
                "success_rate": success_rate,
                "total_duration": total_duration,
                "start_time": datetime.fromtimestamp(self.start_time).isoformat(),
                "end_time": datetime.fromtimestamp(self.end_time).isoformat(),
                "overall_success": failed_tests == 0
            },
            "module_stats": module_stats,
            "module_summaries": module_summaries,
            "all_test_results": all_results,
            "failed_test_details": failed_test_details,
            "test_environment": {
                "server_url": "http://localhost:20000",
                "test_accounts": ["admin", "test1", "test_merchant_b"],
                "python_version": sys.version,
                "test_framework": "Custom Walmart Test Suite v1.0"
            }
        }
        
        # 保存报告
        self.save_reports(report)
        
        # 打印摘要
        self.print_final_summary(report)
        
        return report
    
    def save_reports(self, report: Dict):
        """保存测试报告"""
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            
            # 保存详细JSON报告
            json_filename = f"walmart_comprehensive_test_report_{timestamp}.json"
            json_path = save_test_report(report, json_filename)
            print(f"\n📄 详细报告已保存到: {json_path}")
            
            # 保存简化文本报告
            txt_filename = f"walmart_test_summary_{timestamp}.txt"
            txt_path = os.path.join("test", "reports", txt_filename)
            os.makedirs(os.path.dirname(txt_path), exist_ok=True)
            
            with open(txt_path, 'w', encoding='utf-8') as f:
                f.write("沃尔玛绑卡系统测试报告\n")
                f.write("="*50 + "\n\n")
                f.write(f"测试时间: {report['summary']['start_time']}\n")
                f.write(f"总测试数: {report['summary']['total_tests']}\n")
                f.write(f"通过数: {report['summary']['passed_tests']}\n")
                f.write(f"失败数: {report['summary']['failed_tests']}\n")
                f.write(f"成功率: {report['summary']['success_rate']:.1f}%\n")
                f.write(f"总耗时: {report['summary']['total_duration']:.2f}秒\n\n")
                
                f.write("模块测试结果:\n")
                f.write("-"*30 + "\n")
                for module_name, stats in report['module_stats'].items():
                    status = "✅ 通过" if stats['success'] else "❌ 失败"
                    f.write(f"{status} {module_name}: {stats['passed']}/{stats['total']} ({stats['success_rate']:.1f}%)\n")
                
                if report['failed_test_details']:
                    f.write(f"\n失败的测试详情:\n")
                    f.write("-"*30 + "\n")
                    for test in report['failed_test_details']:
                        f.write(f"❌ {test.get('test_name', 'Unknown')}: {test.get('message', 'No message')}\n")
            
            print(f"📄 简化报告已保存到: {txt_path}")
            
        except Exception as e:
            print(f"⚠️ 保存报告失败: {e}")
    
    def print_final_summary(self, report: Dict):
        """打印最终测试摘要"""
        summary = report["summary"]
        
        print(f"\n{'='*80}")
        print(f"沃尔玛绑卡系统测试完成")
        print(f"{'='*80}")
        print(f"总测试数: {summary['total_tests']}")
        print(f"通过数: {summary['passed_tests']}")
        print(f"失败数: {summary['failed_tests']}")
        print(f"成功率: {summary['success_rate']:.1f}%")
        print(f"总耗时: {summary['total_duration']:.2f}秒")
        
        print(f"\n模块测试结果:")
        print(f"{'-'*80}")
        for module_name, stats in report['module_stats'].items():
            status_icon = "[PASS]" if stats['success'] else "[FAIL]"
            print(f"{status_icon} {module_name:<25} {stats['passed']:>3}/{stats['total']:<3} ({stats['success_rate']:>5.1f}%) {stats['duration']:>6.2f}s")
        
        if report['failed_test_details']:
            print(f"\n需要修复的问题:")
            print(f"{'-'*80}")
            for i, test in enumerate(report['failed_test_details'][:10], 1):  # 只显示前10个
                print(f"{i:>2}. {test.get('test_name', 'Unknown')}: {test.get('message', 'No message')}")

            if len(report['failed_test_details']) > 10:
                print(f"   ... 还有 {len(report['failed_test_details']) - 10} 个问题")
        
        print(f"{'='*80}")

def main():
    """主函数"""
    runner = WalmartTestRunner()
    
    try:
        report = runner.run_all_tests()
        
        # 根据测试结果返回适当的退出码
        if report['summary']['overall_success']:
            print("\n所有测试通过！系统安全性和功能正常。")
            return 0
        else:
            failed_count = report['summary']['failed_tests']
            print(f"\n发现 {failed_count} 个问题，请检查并修复。")
            return 1
            
    except KeyboardInterrupt:
        print("\n测试被用户中断")
        return 130
    except Exception as e:
        print(f"\n测试运行器异常: {e}")
        return 1

if __name__ == "__main__":
    sys.exit(main())
