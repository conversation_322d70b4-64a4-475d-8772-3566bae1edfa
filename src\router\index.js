import { createRouter, createWebHashHistory } from "vue-router";
import Layout from "@/components/layout/index.vue";
import Blank from "@/components/layout/Blank.vue";
import { hasMerchantReadPermission } from "@/utils/permission";

const routes = [
  {
    path: "/login",
    component: () => import("@/views/login/index.vue"),
    meta: { title: "登录" },
  },
  {
    path: "/404",
    component: () => import("@/views/error/404.vue"),
    meta: { title: "页面未找到" },
  },
  {
    path: "/403",
    component: () => import("@/views/error/403.vue"),
    meta: { title: "无权限" },
  },
  {
    path: "/",
    component: Layout,
    redirect: "/dashboard",
    children: [
      // 仪表盘
      {
        path: "dashboard",
        component: () => import("@/views/dashboard/index.vue"),
        meta: {
          title: "仪表盘",
          requiresAuth: true,
          menuCode: "dashboard",
        },
      },
      // 绑卡数据
      {
        path: "cards",
        component: () => import("@/views/cards/index.vue"),
        meta: {
          title: "绑卡数据",
          requiresAuth: true,
          menuCode: "cards",
        },
      },
      // 对账台测试页面
      {
        path: "reconciliation-test",
        name: "ReconciliationTest",
        component: () => import("@/views/reconciliation-test.vue"),
        meta: {
          title: "对账台测试",
          requiresAuth: true,
        },
      },
      // 对账台
      {
        path: "reconciliation",
        name: "Reconciliation",
        component: () => import("@/views/reconciliation/index.vue"),
        meta: {
          title: "对账台",
          requiresAuth: true,
          menuCode: "reconciliation",
        },
      },
      {
        path: "reconciliation/ck-details/:organizationId/:organizationType",
        name: "ReconciliationCkDetails",
        component: () => import("@/views/reconciliation/ck-details.vue"),
        meta: {
          title: "CK明细统计",
          requiresAuth: true,
          menuCode: "reconciliation",
        },
      },
      {
        path: "reconciliation/records/:ckId",
        name: "ReconciliationRecords",
        component: () => import("@/views/reconciliation/records.vue"),
        meta: {
          title: "绑卡记录详情",
          requiresAuth: true,
          menuCode: "reconciliation",
        },
      },
      // ---------------- 系统管理 ----------------
      {
        path: "system",
        component: Blank,
        redirect: "/system/user",
        meta: {
          title: "系统管理",
          requiresAuth: true,
          menuCode: "system",
        },
        children: [
          {
            path: "user",
            component: () => import("@/views/system-management/user/index.vue"),
            meta: {
              title: "用户管理",
              requiresAuth: true,
              menuCode: "system:user",
            },
          },
          {
            path: "user/add",
            component: () => import("@/views/system-management/user/form.vue"),
            meta: {
              title: "新增用户",
              requiresAuth: true,
              menuCode: "system:user",
            },
          },
          {
            path: "user/edit/:id",
            component: () => import("@/views/system-management/user/form.vue"),
            meta: {
              title: "编辑用户",
              requiresAuth: true,
              menuCode: "system:user",
            },
          },
          {
            path: "role",
            component: () => import("@/views/system-management/role/index.vue"),
            meta: {
              title: "角色管理",
              requiresAuth: true,
              menuCode: "system:role",
            },
          },
          {
            path: "role/:id/permission",
            component: () =>
              import("@/views/system-management/role/permission.vue"),
            meta: {
              title: "角色权限配置",
              requiresAuth: true,
              menuCode: "system:role",
            },
          },
          {
            path: "role/:id/users",
            component: () => import("@/views/system-management/role/users.vue"),
            meta: {
              title: "角色用户管理",
              requiresAuth: true,
              menuCode: "system:role",
            },
          },
          {
            path: "permission",
            component: () =>
              import("@/views/system-management/permission/index.vue"),
            meta: {
              title: "权限管理",
              requiresAuth: true,
              menuCode: "system:permission",
            },
          },
          {
            path: "menu",
            component: () => import("@/views/system-management/menu/index.vue"),
            meta: {
              title: "菜单管理",
              requiresAuth: true,
              menuCode: "system:menu",
            },
          },
          {
            path: "settings",
            component: () =>
              import("@/views/system-management/settings/index.vue"),
            meta: {
              title: "系统设置",
              requiresAuth: true,
              menuCode: "system:settings",
            },
          },
          {
            path: "recovery",
            component: () =>
              import("@/views/system-management/recovery/index.vue"),
            meta: {
              title: "恢复处理",
              requiresAuth: true,
              menuCode: "system:recovery",
            },
          },
        ],
      },
      // ---------------- 商家管理 ----------------
      {
        path: "merchant",
        component: Blank,
        redirect: "/merchant/list",
        meta: {
          title: "商家管理",
          requiresAuth: true,
          menuCode: "merchant",
        },
        children: [
          {
            path: "list",
            component: () => import("@/views/merchant/index.vue"),
            meta: {
              title: "商家列表",
              requiresAuth: true,
              menuCode: "merchant:list",
            },
          },
          {
            path: "add",
            component: () => import("@/views/merchant/form.vue"),
            meta: {
              title: "新增商家",
              requiresAuth: true,
              menuCode: "merchant:list",
            },
          },
          {
            path: "edit/:id",
            component: () => import("@/views/merchant/form.vue"),
            meta: {
              title: "编辑商家",
              requiresAuth: true,
              menuCode: "merchant:list",
            },
          },
          {
            path: "detail/:id",
            component: () => import("@/views/merchant/detail.vue"),
            meta: {
              title: "商家详情",
              requiresAuth: true,
              menuCode: "merchant:list",
            },
          },
          {
            path: "department",
            component: () =>
              import("@/views/department/DepartmentManagement.vue"),
            meta: {
              title: "部门管理",
              requiresAuth: true,
              menuCode: "merchant:department",
            },
          },
        ],
      },
      // ---------------- 沃尔玛配置 ----------------
      {
        path: "walmart",
        component: Blank,
        redirect: "/walmart/user", // 默认重定向到CK管理页面，适合所有有权限的用户
        meta: {
          title: "沃尔玛配置",
          requiresAuth: true,
          menuCode: "walmart",
        },
        children: [
          {
            path: "walmart-server",
            component: () => import("@/views/walmart-server/index.vue"),
            meta: {
              title: "沃尔玛API配置",
              requiresAuth: true,
              menuCode: "walmart",
            },
          },
          {
            path: "user",
            component: () => import("@/views/walmart/index.vue"),
            meta: {
              title: "绑卡用户",
              requiresAuth: true,
              menuCode: "walmart:user",
            },
          },
          {
            path: "user/add",
            component: () => import("@/views/walmart/form.vue"),
            meta: {
              title: "新增CK",
              requiresAuth: true,
              menuCode: "walmart:user",
            },
          },
          {
            path: "user/edit/:id",
            component: () => import("@/views/walmart/form.vue"),
            meta: {
              title: "编辑CK",
              requiresAuth: true,
              menuCode: "walmart:user",
            },
          },
          {
            path: "user/statistics/:id",
            component: () => import("@/views/walmart/statistics.vue"),
            meta: {
              title: "CK统计详情",
              requiresAuth: true,
              menuCode: "walmart:user",
            },
          },
          {
            path: "user/sync-test",
            component: () => import("@/views/walmart/sync-test.vue"),
            meta: {
              title: "CK同步测试",
              requiresAuth: true,
              menuCode: "walmart:user",
            },
          },
        ],
      },
      // ---------------- Telegram机器人 ----------------
      {
        path: "telegram",
        component: Blank,
        redirect: "/telegram/dashboard",
        meta: {
          title: "Telegram机器人",
          requiresAuth: true,
          menuCode: "telegram",
        },
        children: [
          {
            path: "dashboard",
            component: () => import("@/views/telegram/dashboard/index.vue"),
            meta: {
              title: "机器人概览",
              requiresAuth: true,
              menuCode: "telegram:dashboard",
            },
          },
          {
            path: "config",
            component: () => import("@/views/telegram/config/index.vue"),
            meta: {
              title: "配置管理",
              requiresAuth: true,
              menuCode: "telegram:config",
            },
          },
          {
            path: "groups",
            component: () => import("@/views/telegram/groups/index.vue"),
            meta: {
              title: "群组管理",
              requiresAuth: true,
              menuCode: "telegram:groups",
            },
          },
          {
            path: "groups/detail/:id",
            component: () => import("@/views/telegram/groups/detail.vue"),
            meta: {
              title: "群组详情",
              requiresAuth: true,
              menuCode: "telegram:groups",
            },
          },
          {
            path: "users",
            component: () => import("@/views/telegram/users/index.vue"),
            meta: {
              title: "用户管理",
              requiresAuth: true,
              menuCode: "telegram:users",
            },
          },
          {
            path: "statistics",
            component: () => import("@/views/telegram/statistics/index.vue"),
            meta: {
              title: "统计分析",
              requiresAuth: true,
              menuCode: "telegram:statistics",
            },
          },
          {
            path: "logs",
            component: () => import("@/views/telegram/logs/index.vue"),
            meta: {
              title: "日志管理",
              requiresAuth: true,
              menuCode: "telegram:logs",
            },
          },
        ],
      },
      // 通知中心
      {
        path: "notification",
        component: () => import("@/views/notification/NotificationCenter.vue"),
        meta: {
          title: "通知中心",
          requiresAuth: true,
          menuCode: "notification",
        },
      },
      // ---------------- 绑卡操作 ----------------
      {
        path: "bind",
        component: Blank,
        redirect: "/bind/single-bind",
        meta: {
          title: "绑卡操作",
          requiresAuth: true,
          menuCode: "bind",
        },
        children: [
          {
            path: "single-bind",
            component: () => import("@/views/bind/single-bind.vue"),
            meta: {
              title: "单卡绑定",
              requiresAuth: true,
              menuCode: "bind:single",
            },
          },
          {
            path: "batch-bind",
            component: () => import("@/views/bind/batch-bind.vue"),
            meta: {
              title: "批量绑卡",
              requiresAuth: true,
              menuCode: "bind:batch",
            },
          },
        ],
      },
      // 个人安全设置（不需要特殊权限，所有登录用户都可访问）
      {
        path: "security",
        component: Blank,
        redirect: "/security/index",
        meta: {
          title: "个人安全设置",
          requiresAuth: true,
          // 移除 menuCode，个人设置不需要菜单权限验证
        },
        children: [
          {
            path: "index",
            component: () => import("@/views/security/index.vue"),
            meta: {
              title: "个人安全设置",
              requiresAuth: true,
              // 移除 menuCode，个人设置不需要菜单权限验证
            },
          },
          {
            path: "totp-setup",
            component: () => import("@/views/security/totp-setup.vue"),
            meta: {
              title: "设置双因子认证",
              requiresAuth: true,
              // 移除 menuCode，个人设置不需要菜单权限验证
            },
          },
        ],
      },
      // ---------------- 开发工具 ----------------
      {
        path: "tools",
        component: Blank,
        redirect: "/tools/api-test",
        meta: {
          title: "开发工具",
          requiresAuth: true,
          menuCode: "tools",
        },
        children: [
          {
            path: "api-test",
            component: () => import("@/views/api-test/index.vue"),
            meta: {
              title: "API测试工具",
              requiresAuth: true,
              menuCode: "tools:api-test",
            },
          },
          {
            path: "monitoring",
            component: () => import("@/views/system-monitoring/index.vue"),
            meta: {
              title: "系统监控",
              requiresAuth: true,
              menuCode: "tools:monitoring",
            },
          },
          {
            path: "enhanced-audit",
            component: () => import("@/views/security/enhanced-audit.vue"),
            meta: {
              title: "增强安全审计",
              requiresAuth: true,
              menuCode: "tools:enhanced-audit",
            },
          },
          {
            path: "anomaly-detection",
            component: () => import("@/views/security/anomaly-detection.vue"),
            meta: {
              title: "异常行为检测",
              requiresAuth: true,
              menuCode: "tools:anomaly-detection",
            },
          },
          {
            path: "advanced-analytics",
            component: () => import("@/views/analytics/advanced-analytics.vue"),
            meta: {
              title: "高级数据分析",
              requiresAuth: true,
              menuCode: "tools:advanced-analytics",
            },
          },
          {
            path: "batch-operations",
            component: () =>
              import("@/views/data-management/batch-operations.vue"),
            meta: {
              title: "批量数据操作",
              requiresAuth: true,
              menuCode: "tools:batch-operations",
            },
          },
        ],
      },
      // 测试首页
      {
        path: "test",
        component: () => import("@/views/test/index.vue"),
        meta: {
          title: "测试工具",
          requiresAuth: true,
          menuCode: "cards", // 使用绑卡数据权限
        },
      },
      // 测试
      {
        path: "test/permission",
        component: () => import("@/views/test/PermissionTest.vue"),
        meta: {
          title: "权限测试",
          requiresAuth: true,
          menuCode: "system:user", // 测试页面使用用户管理权限
        },
      },
      // 并发测试
      {
        path: "test/concurrent-binding",
        component: () => import("@/views/test/concurrent-binding.vue"),
        meta: {
          title: "并发绑卡测试",
          requiresAuth: true,
          menuCode: "bind:single", // 使用绑卡权限
        },
      },
      // 时间线测试
      {
        path: "test/timeline",
        component: () => import("@/views/test/timeline-test.vue"),
        meta: {
          title: "绑卡时间线测试",
          requiresAuth: true,
          menuCode: "cards", // 使用绑卡数据权限
        },
      },
    ],
  },
];

const router = createRouter({
  history: createWebHashHistory(),
  routes,
});

// 增强路由守卫 - 支持权限检查
router.beforeEach(async (to, _from, next) => {
  document.title = to.meta.title
    ? `${to.meta.title} - 沃尔玛绑卡管理系统`
    : "沃尔玛绑卡管理系统";

  // 公开页面直接放行
  const publicPaths = ["/login", "/404", "/403"];
  if (publicPaths.includes(to.path)) {
    next();
    return;
  }

  // 检查登录状态
  const token = localStorage.getItem("walmart_token");
  if (!token) {
    console.log("未找到token，跳转到登录页面");
    next("/login");
    return;
  }

  // 检查token是否过期
  const tokenExpire = localStorage.getItem("walmart_token_expire");
  if (tokenExpire && new Date().getTime() > parseInt(tokenExpire)) {
    console.log("Token已过期，清除登录状态");
    localStorage.removeItem("walmart_token");
    localStorage.removeItem("walmart_user_info");
    localStorage.removeItem("walmart_token_expire");
    next("/login");
    return;
  }

  try {
    // 动态导入Store
    const { usePermissionStore } = await import("@/store/modules/permission");
    const { useUserStore } = await import("@/store/modules/user");
    const permissionStore = usePermissionStore();
    const userStore = useUserStore();

    // 如果用户信息不存在或权限信息不完整，先获取用户信息
    if (
      !userStore.hasUserInfo ||
      (!permissionStore.isSuperuser &&
        permissionStore.accessibleMenus.length === 0)
    ) {
      try {
        const success = await userStore.getUserInfo();
        if (!success) {
          throw new Error("获取用户信息失败");
        }

        // 给权限信息一点时间来更新
        await new Promise((resolve) => setTimeout(resolve, 100));
      } catch (error) {
        console.error("获取用户信息失败:", error);
        // 获取用户信息失败，可能token已过期，跳转到登录页
        localStorage.removeItem("walmart_token");
        localStorage.removeItem("walmart_user_info");
        localStorage.removeItem("walmart_token_expire");
        next("/login");
        return;
      }
    }

    // 基于权限初始化商户Store
    if (hasMerchantReadPermission(userStore.userInfo)) {
      try {
        const { useMerchantStore } = await import("@/store/modules/merchant");
        const merchantStore = useMerchantStore();

        // 如果商户列表为空，说明需要初始化
        if (merchantStore.merchants.length === 0) {
          await merchantStore.initMerchant();
        }
      } catch (error) {
        console.warn("商户Store初始化失败:", error);
        // 商户初始化失败不影响页面访问
      }
    } else {
      console.log("用户无商户查询权限，跳过商户Store初始化");
    }

    // 检查是否需要权限验证
    if (to.meta?.requiresAuth && to.meta?.menuCode) {
      // 特殊检查：Telegram机器人相关页面仅允许超级管理员访问
      if (to.meta.menuCode.startsWith("telegram:")) {
        if (!permissionStore.isSuperuser) {
          console.warn(
            `非超级管理员尝试访问Telegram页面: ${to.path}, 需要超级管理员权限`
          );
          next("/403");
          return;
        }
      } else {
        // 其他页面使用常规菜单权限检查
        const hasAccess = permissionStore.hasMenuAccess(to.meta.menuCode);

        if (!hasAccess) {
          console.warn(
            `用户无权限访问页面: ${to.path}, 需要菜单权限: ${to.meta.menuCode}`
          );
          next("/403");
          return;
        }
      }
    }

    console.log("路由守卫检查通过，允许访问:", to.path);
    next();
  } catch (error) {
    console.error("路由守卫执行失败:", error);
    // 发生错误时，清除token并重定向到登录页面
    localStorage.removeItem("walmart_token");
    localStorage.removeItem("walmart_user_info");
    localStorage.removeItem("walmart_token_expire");
    next("/login");
  }
});

export default router;
