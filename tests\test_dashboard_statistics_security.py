"""
测试DashboardStatisticsService数据隔离安全修复
验证仪表盘统计服务的商户级数据隔离机制
"""

import pytest
from datetime import datetime, timedelta
from sqlalchemy.orm import Session
from app.models.user import User
from app.models.merchant import Merchant
from app.models.card_record import CardRecord, CardStatus
from app.services.dashboard_statistics_service import DashboardStatisticsService


class TestDashboardStatisticsServiceSecurity:
    """测试DashboardStatisticsService安全修复"""

    @pytest.fixture
    def setup_test_data(self, db: Session):
        """设置测试数据"""
        # 创建两个商户
        merchant1 = Merchant(
            name="测试商户1",
            code="TEST_MERCHANT_1",
            api_key="test_key_1",
            api_secret="test_secret_1"
        )
        merchant2 = Merchant(
            name="测试商户2", 
            code="TEST_MERCHANT_2",
            api_key="test_key_2",
            api_secret="test_secret_2"
        )
        db.add_all([merchant1, merchant2])
        db.commit()
        db.refresh(merchant1)
        db.refresh(merchant2)

        # 创建用户
        user1 = User(
            username="test_user_1",
            email="<EMAIL>",
            hashed_password="hashed_password",
            merchant_id=merchant1.id,
            is_superuser=False
        )
        user2 = User(
            username="test_user_2",
            email="<EMAIL>", 
            hashed_password="hashed_password",
            merchant_id=merchant2.id,
            is_superuser=False
        )
        superuser = User(
            username="superuser",
            email="<EMAIL>",
            hashed_password="hashed_password",
            is_superuser=True
        )
        db.add_all([user1, user2, superuser])
        db.commit()
        db.refresh(user1)
        db.refresh(user2)
        db.refresh(superuser)

        # 创建卡记录
        now = datetime.now()
        card1 = CardRecord(
            id="card_1",
            card_number="1234567890123456",
            merchant_id=merchant1.id,
            status=CardStatus.SUCCESS,
            amount=10000,  # 100元
            created_at=now
        )
        card2 = CardRecord(
            id="card_2",
            card_number="****************",
            merchant_id=merchant2.id,
            status=CardStatus.SUCCESS,
            amount=20000,  # 200元
            created_at=now
        )
        db.add_all([card1, card2])
        db.commit()

        return {
            "merchant1": merchant1,
            "merchant2": merchant2,
            "user1": user1,
            "user2": user2,
            "superuser": superuser,
            "card1": card1,
            "card2": card2
        }

    def test_get_safe_merchant_id_for_regular_users(self, db: Session, setup_test_data):
        """测试普通用户的安全商户ID获取"""
        service = DashboardStatisticsService(db)
        user1 = setup_test_data["user1"]
        user2 = setup_test_data["user2"]

        # 用户1请求自己的商户数据
        safe_id = service._get_safe_merchant_id(user1, user1.merchant_id)
        assert safe_id == user1.merchant_id, "用户应该能访问自己的商户数据"

        # 用户1尝试请求其他商户数据（应该被拒绝）
        safe_id = service._get_safe_merchant_id(user1, user2.merchant_id)
        assert safe_id == user1.merchant_id, "用户不应该能访问其他商户数据，应该返回自己的商户ID"

        # 用户1不指定商户ID（应该返回自己的商户ID）
        safe_id = service._get_safe_merchant_id(user1, None)
        assert safe_id == user1.merchant_id, "用户不指定商户ID时应该返回自己的商户ID"

    def test_get_safe_merchant_id_for_superuser(self, db: Session, setup_test_data):
        """测试超级管理员的商户ID获取"""
        service = DashboardStatisticsService(db)
        superuser = setup_test_data["superuser"]
        merchant1 = setup_test_data["merchant1"]
        merchant2 = setup_test_data["merchant2"]

        # 超级管理员可以访问任何商户数据
        safe_id = service._get_safe_merchant_id(superuser, merchant1.id)
        assert safe_id == merchant1.id, "超级管理员应该能访问任何商户数据"

        safe_id = service._get_safe_merchant_id(superuser, merchant2.id)
        assert safe_id == merchant2.id, "超级管理员应该能访问任何商户数据"

        # 超级管理员不指定商户ID时返回None（全局数据）
        safe_id = service._get_safe_merchant_id(superuser, None)
        assert safe_id is None, "超级管理员不指定商户ID时应该返回None"

    def test_get_safe_merchant_id_for_user_without_merchant(self, db: Session, setup_test_data):
        """测试没有商户ID的用户"""
        service = DashboardStatisticsService(db)
        
        # 创建没有商户ID的用户
        user_no_merchant = User(
            username="no_merchant_user",
            email="<EMAIL>",
            hashed_password="hashed_password",
            is_superuser=False
        )
        db.add(user_no_merchant)
        db.commit()

        safe_id = service._get_safe_merchant_id(user_no_merchant, None)
        assert safe_id is None, "没有商户ID的用户应该返回None"

    def test_amount_statistics_data_isolation(self, db: Session, setup_test_data):
        """测试金额统计的数据隔离"""
        service = DashboardStatisticsService(db)
        user1 = setup_test_data["user1"]
        user2 = setup_test_data["user2"]
        
        now = datetime.now()
        start_time = now - timedelta(days=1)
        end_time = now + timedelta(days=1)

        # 用户1应该只能看到自己商户的统计
        stats1 = service._get_amount_statistics(user1.merchant_id, start_time, end_time)
        assert stats1["total_requests"] == 1, "用户1应该只能看到1个请求"
        assert stats1["total_amount"] == 10000, "用户1应该只能看到自己商户的金额"

        # 用户2应该只能看到自己商户的统计
        stats2 = service._get_amount_statistics(user2.merchant_id, start_time, end_time)
        assert stats2["total_requests"] == 1, "用户2应该只能看到1个请求"
        assert stats2["total_amount"] == 20000, "用户2应该只能看到自己商户的金额"

        # 测试merchant_id为None的情况（应该返回空结果）
        stats_none = service._get_amount_statistics(None, start_time, end_time)
        assert stats_none["total_requests"] == 0, "merchant_id为None时应该返回空结果"

    def test_success_rate_statistics_data_isolation(self, db: Session, setup_test_data):
        """测试成功率统计的数据隔离"""
        service = DashboardStatisticsService(db)
        user1 = setup_test_data["user1"]
        
        now = datetime.now()
        start_time = now - timedelta(days=1)
        end_time = now + timedelta(days=1)

        # 用户1应该只能看到自己商户的统计
        stats1 = service._get_success_rate_statistics(user1.merchant_id, start_time, end_time)
        assert len(stats1["hourly_stats"]) >= 0, "应该返回小时统计数据"

        # 测试merchant_id为None的情况（应该返回空结果）
        stats_none = service._get_success_rate_statistics(None, start_time, end_time)
        assert len(stats_none["hourly_stats"]) == 0, "merchant_id为None时应该返回空结果"

    def test_ck_efficiency_statistics_data_isolation(self, db: Session, setup_test_data):
        """测试CK效率统计的数据隔离"""
        service = DashboardStatisticsService(db)
        user1 = setup_test_data["user1"]
        
        now = datetime.now()
        start_time = now - timedelta(days=1)
        end_time = now + timedelta(days=1)

        # 用户1应该只能看到自己商户的统计
        stats1 = service._get_ck_efficiency_statistics(user1.merchant_id, start_time, end_time)
        assert "total_ck_count" in stats1, "应该返回CK统计数据"

        # 测试merchant_id为None的情况（应该返回空结果）
        stats_none = service._get_ck_efficiency_statistics(None, start_time, end_time)
        assert stats_none["total_ck_count"] == 0, "merchant_id为None时应该返回空结果"

    def test_failure_statistics_data_isolation(self, db: Session, setup_test_data):
        """测试失败统计的数据隔离"""
        service = DashboardStatisticsService(db)
        user1 = setup_test_data["user1"]
        
        now = datetime.now()
        start_time = now - timedelta(days=1)
        end_time = now + timedelta(days=1)

        # 用户1应该只能看到自己商户的统计
        stats1 = service._get_failure_statistics(user1.merchant_id, start_time, end_time)
        assert "error_types" in stats1, "应该返回失败统计数据"

        # 测试merchant_id为None的情况（应该返回空结果）
        stats_none = service._get_failure_statistics(None, start_time, end_time)
        assert len(stats_none["error_types"]) == 0, "merchant_id为None时应该返回空结果"

    def test_trend_base_query_data_isolation(self, db: Session, setup_test_data):
        """测试趋势查询的数据隔离"""
        service = DashboardStatisticsService(db)
        user1 = setup_test_data["user1"]

        # 用户1应该只能看到自己商户的数据
        query1 = service._build_trend_base_query(user1.merchant_id)
        results1 = query1.all()
        assert len(results1) == 1, "用户1应该只能看到1个记录"
        assert results1[0].merchant_id == user1.merchant_id, "应该只返回用户1商户的数据"

        # 测试merchant_id为None的情况（应该返回空结果）
        query_none = service._build_trend_base_query(None)
        results_none = query_none.all()
        assert len(results_none) == 0, "merchant_id为None时应该返回空结果"
