"""
双因子认证API测试
"""

import pytest
import json
from fastapi.testclient import TestClient
from sqlalchemy.orm import Session

from app.main import app
from app.core.config import settings
from app.models.user import User
from app.services.totp_service import TOTPService
from test.conftest import get_test_db, create_test_user, get_auth_headers


client = TestClient(app)


class TestTOTPAPI:
    """TOTP API测试类"""

    def setup_method(self):
        """测试前准备"""
        self.db = next(get_test_db())
        self.test_user = create_test_user(self.db, "totp_test_user", "test123456")
        self.auth_headers = get_auth_headers(self.test_user.username, "test123456")

    def teardown_method(self):
        """测试后清理"""
        # 清理测试用户的TOTP设置
        if self.test_user:
            self.test_user.totp_enabled = False
            self.test_user.totp_secret = None
            self.test_user.totp_backup_codes = None
            self.db.commit()
        self.db.close()

    def test_get_totp_status_not_enabled(self):
        """测试获取TOTP状态 - 未启用"""
        response = client.get(
            f"{settings.API_V1_STR}/totp/status",
            headers=self.auth_headers
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data["enabled"] is False
        assert data["is_required"] is False
        assert data["backup_codes_remaining"] == 0

    def test_setup_totp(self):
        """测试设置TOTP"""
        response = client.post(
            f"{settings.API_V1_STR}/totp/setup",
            json={},
            headers=self.auth_headers
        )
        
        assert response.status_code == 200
        data = response.json()
        
        # 验证返回数据
        assert "secret" in data
        assert "qr_code_url" in data
        assert "backup_codes" in data
        assert "manual_entry_key" in data
        
        # 验证密钥格式
        assert len(data["secret"]) == 32  # Base32编码的密钥长度
        assert data["qr_code_url"].startswith("data:image/png;base64,")
        assert len(data["backup_codes"]) == 10  # 默认10个备用码
        assert data["manual_entry_key"] == data["secret"]

    def test_setup_totp_already_enabled(self):
        """测试重复设置TOTP"""
        # 先设置一次
        client.post(
            f"{settings.API_V1_STR}/totp/setup",
            json={},
            headers=self.auth_headers
        )
        
        # 再次设置应该失败
        response = client.post(
            f"{settings.API_V1_STR}/totp/setup",
            json={},
            headers=self.auth_headers
        )
        
        assert response.status_code == 400

    def test_verify_totp_invalid_code(self):
        """测试验证无效的TOTP码"""
        # 先设置TOTP
        client.post(
            f"{settings.API_V1_STR}/totp/setup",
            json={},
            headers=self.auth_headers
        )
        
        # 验证无效码
        response = client.post(
            f"{settings.API_V1_STR}/totp/verify",
            json={"code": "123456"},
            headers=self.auth_headers
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is False
        assert "错误" in data["message"]

    def test_enable_totp_workflow(self):
        """测试完整的TOTP启用流程"""
        # 1. 设置TOTP
        setup_response = client.post(
            f"{settings.API_V1_STR}/totp/setup",
            json={},
            headers=self.auth_headers
        )
        assert setup_response.status_code == 200
        setup_data = setup_response.json()
        
        # 2. 生成有效的TOTP码（模拟）
        import pyotp
        totp = pyotp.TOTP(setup_data["secret"])
        valid_code = totp.now()
        
        # 3. 启用TOTP
        enable_response = client.post(
            f"{settings.API_V1_STR}/totp/enable",
            json={"code": valid_code},
            headers=self.auth_headers
        )
        assert enable_response.status_code == 200
        
        # 4. 验证状态已更新
        status_response = client.get(
            f"{settings.API_V1_STR}/totp/status",
            headers=self.auth_headers
        )
        assert status_response.status_code == 200
        status_data = status_response.json()
        assert status_data["enabled"] is True

    def test_disable_totp(self):
        """测试禁用TOTP"""
        # 先启用TOTP
        self._enable_totp_for_user()
        
        # 生成有效的TOTP码
        totp_service = TOTPService(self.db)
        secret = totp_service._decrypt_data(self.test_user.totp_secret)
        import pyotp
        totp = pyotp.TOTP(secret)
        valid_code = totp.now()
        
        # 禁用TOTP
        response = client.post(
            f"{settings.API_V1_STR}/totp/disable",
            json={
                "password": "test123456",
                "code": valid_code
            },
            headers=self.auth_headers
        )
        
        assert response.status_code == 200
        
        # 验证状态已更新
        status_response = client.get(
            f"{settings.API_V1_STR}/totp/status",
            headers=self.auth_headers
        )
        status_data = status_response.json()
        assert status_data["enabled"] is False

    def test_verify_backup_code(self):
        """测试验证备用码"""
        # 设置TOTP并获取备用码
        setup_response = client.post(
            f"{settings.API_V1_STR}/totp/setup",
            json={},
            headers=self.auth_headers
        )
        setup_data = setup_response.json()
        backup_codes = setup_data["backup_codes"]
        
        # 启用TOTP
        import pyotp
        totp = pyotp.TOTP(setup_data["secret"])
        valid_code = totp.now()
        client.post(
            f"{settings.API_V1_STR}/totp/enable",
            json={"code": valid_code},
            headers=self.auth_headers
        )
        
        # 使用备用码验证
        response = client.post(
            f"{settings.API_V1_STR}/totp/backup-code/verify",
            json={"backup_code": backup_codes[0]},
            headers=self.auth_headers
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True

    def test_check_totp_required(self):
        """测试检查TOTP要求"""
        response = client.get(
            f"{settings.API_V1_STR}/totp/required",
            headers=self.auth_headers
        )
        
        assert response.status_code == 200
        data = response.json()
        assert "required" in data
        assert "in_grace_period" in data
        assert "enabled" in data

    def test_totp_login_integration(self):
        """测试TOTP登录集成"""
        # 启用TOTP
        self._enable_totp_for_user()
        
        # 尝试不带TOTP码的登录
        login_data = {
            "username": self.test_user.username,
            "password": "test123456"
        }
        response = client.post(
            f"{settings.API_V1_STR}/auth/login",
            data=login_data
        )
        
        # 应该要求提供TOTP码
        assert response.status_code == 400
        assert "双因子认证码" in response.json()["detail"]
        
        # 带TOTP码的登录
        totp_service = TOTPService(self.db)
        secret = totp_service._decrypt_data(self.test_user.totp_secret)
        import pyotp
        totp = pyotp.TOTP(secret)
        valid_code = totp.now()
        
        response = client.post(
            f"{settings.API_V1_STR}/auth/login",
            data=login_data,
            headers={"X-TOTP-Code": valid_code}
        )
        
        assert response.status_code == 200
        assert "access_token" in response.json()

    def _enable_totp_for_user(self):
        """为测试用户启用TOTP的辅助方法"""
        # 设置TOTP
        setup_response = client.post(
            f"{settings.API_V1_STR}/totp/setup",
            json={},
            headers=self.auth_headers
        )
        setup_data = setup_response.json()
        
        # 启用TOTP
        import pyotp
        totp = pyotp.TOTP(setup_data["secret"])
        valid_code = totp.now()
        
        client.post(
            f"{settings.API_V1_STR}/totp/enable",
            json={"code": valid_code},
            headers=self.auth_headers
        )


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
