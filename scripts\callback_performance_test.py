#!/usr/bin/env python3
"""
回调性能测试脚本
测试优化后的回调系统性能
"""
import asyncio
import aiohttp
import time
import json
import uuid
from datetime import datetime
from typing import List, Dict, Any
from dataclasses import dataclass, asdict
import argparse

@dataclass
class TestResult:
    """测试结果"""
    total_requests: int
    successful_requests: int
    failed_requests: int
    total_time: float
    average_response_time: float
    min_response_time: float
    max_response_time: float
    requests_per_second: float
    success_rate: float
    errors: List[str]


class CallbackPerformanceTester:
    """回调性能测试器"""
    
    def __init__(self, base_url: str = "http://localhost:8000"):
        self.base_url = base_url
        self.session: aiohttp.ClientSession = None
    
    async def __aenter__(self):
        connector = aiohttp.TCPConnector(
            limit=200,  # 总连接池大小
            limit_per_host=100,  # 每个主机的连接数
            ttl_dns_cache=300,
            use_dns_cache=True,
        )
        
        timeout = aiohttp.ClientTimeout(
            total=60,  # 总超时时间
            connect=10,  # 连接超时
            sock_read=30,  # 读取超时
        )
        
        self.session = aiohttp.ClientSession(
            connector=connector,
            timeout=timeout,
            headers={"Content-Type": "application/json"}
        )
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self.session:
            await self.session.close()
    
    async def test_callback_endpoint(self, callback_data: Dict[str, Any]) -> Dict[str, Any]:
        """测试单个回调请求"""
        start_time = time.time()
        
        try:
            # 模拟发送到回调服务的请求
            async with self.session.post(
                "http://localhost:3001/callback/walmart",  # 回调测试服务
                json=callback_data
            ) as response:
                response_time = time.time() - start_time
                response_text = await response.text()
                
                return {
                    "success": 200 <= response.status < 300,
                    "status_code": response.status,
                    "response_time": response_time,
                    "response_text": response_text[:200],  # 限制响应文本长度
                    "error": None
                }
        except Exception as e:
            response_time = time.time() - start_time
            return {
                "success": False,
                "status_code": 0,
                "response_time": response_time,
                "response_text": "",
                "error": str(e)
            }
    
    async def test_concurrent_callbacks(
        self, 
        concurrent_count: int = 100,
        total_requests: int = 1000
    ) -> TestResult:
        """测试并发回调"""
        print(f"开始并发回调测试: {concurrent_count}并发, 总请求数: {total_requests}")
        
        # 准备测试数据
        test_data_list = []
        for i in range(total_requests):
            test_data = {
                "record_id": str(uuid.uuid4()),
                "merchant_order_id": f"TEST_ORDER_{i}",
                "card_number": f"1234567890123{i:03d}",
                "amount": 10000 + i,
                "status": "success",
                "message": "绑卡成功",
                "created_at": datetime.now().isoformat(),
                "trace_id": f"test_trace_{i}",
                "retry_count": 0,
            }
            test_data_list.append(test_data)
        
        # 执行并发测试
        start_time = time.time()
        semaphore = asyncio.Semaphore(concurrent_count)
        
        async def limited_request(data):
            async with semaphore:
                return await self.test_callback_endpoint(data)
        
        # 创建所有任务
        tasks = [limited_request(data) for data in test_data_list]
        
        # 执行所有任务
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        total_time = time.time() - start_time
        
        # 分析结果
        successful_requests = 0
        failed_requests = 0
        response_times = []
        errors = []
        
        for result in results:
            if isinstance(result, Exception):
                failed_requests += 1
                errors.append(str(result))
            elif result.get("success"):
                successful_requests += 1
                response_times.append(result["response_time"])
            else:
                failed_requests += 1
                error_msg = result.get("error") or f"HTTP {result.get('status_code')}"
                errors.append(error_msg)
        
        # 计算统计信息
        if response_times:
            average_response_time = sum(response_times) / len(response_times)
            min_response_time = min(response_times)
            max_response_time = max(response_times)
        else:
            average_response_time = min_response_time = max_response_time = 0
        
        requests_per_second = total_requests / total_time if total_time > 0 else 0
        success_rate = (successful_requests / total_requests * 100) if total_requests > 0 else 0
        
        return TestResult(
            total_requests=total_requests,
            successful_requests=successful_requests,
            failed_requests=failed_requests,
            total_time=total_time,
            average_response_time=average_response_time,
            min_response_time=min_response_time,
            max_response_time=max_response_time,
            requests_per_second=requests_per_second,
            success_rate=success_rate,
            errors=errors[:10]  # 只保留前10个错误
        )
    
    async def test_callback_monitor_api(self) -> Dict[str, Any]:
        """测试回调监控API"""
        print("测试回调监控API...")
        
        # 需要认证token，这里简化处理
        headers = {
            "Authorization": "Bearer your_test_token_here",
            "Content-Type": "application/json"
        }
        
        try:
            # 测试健康检查
            async with self.session.get(
                f"{self.base_url}/api/v1/callback-monitor/health"
            ) as response:
                health_data = await response.json()
                
            # 测试当前统计
            async with self.session.get(
                f"{self.base_url}/api/v1/callback-monitor/stats/current",
                headers=headers
            ) as response:
                if response.status == 200:
                    stats_data = await response.json()
                else:
                    stats_data = {"error": f"HTTP {response.status}"}
            
            return {
                "health_check": health_data,
                "current_stats": stats_data,
                "api_available": True
            }
        except Exception as e:
            return {
                "health_check": None,
                "current_stats": None,
                "api_available": False,
                "error": str(e)
            }


def print_test_results(result: TestResult):
    """打印测试结果"""
    print("\n" + "="*60)
    print("回调性能测试结果")
    print("="*60)
    print(f"总请求数:           {result.total_requests}")
    print(f"成功请求数:         {result.successful_requests}")
    print(f"失败请求数:         {result.failed_requests}")
    print(f"成功率:             {result.success_rate:.2f}%")
    print(f"总耗时:             {result.total_time:.2f}秒")
    print(f"平均响应时间:       {result.average_response_time*1000:.2f}ms")
    print(f"最小响应时间:       {result.min_response_time*1000:.2f}ms")
    print(f"最大响应时间:       {result.max_response_time*1000:.2f}ms")
    print(f"每秒请求数(QPS):    {result.requests_per_second:.2f}")
    
    if result.errors:
        print(f"\n前{len(result.errors)}个错误:")
        for i, error in enumerate(result.errors, 1):
            print(f"  {i}. {error}")
    
    print("="*60)


async def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="回调性能测试")
    parser.add_argument("--concurrent", type=int, default=100, help="并发数")
    parser.add_argument("--total", type=int, default=1000, help="总请求数")
    parser.add_argument("--base-url", default="http://localhost:8000", help="API基础URL")
    
    args = parser.parse_args()
    
    async with CallbackPerformanceTester(args.base_url) as tester:
        # 1. 测试回调监控API
        print("1. 测试回调监控API...")
        monitor_result = await tester.test_callback_monitor_api()
        print(f"监控API可用: {monitor_result['api_available']}")
        if monitor_result.get("error"):
            print(f"监控API错误: {monitor_result['error']}")
        
        # 2. 测试并发回调
        print(f"\n2. 开始并发回调测试...")
        test_result = await tester.test_concurrent_callbacks(
            concurrent_count=args.concurrent,
            total_requests=args.total
        )
        
        # 打印结果
        print_test_results(test_result)
        
        # 保存结果到文件
        result_file = f"callback_test_result_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(result_file, 'w', encoding='utf-8') as f:
            json.dump({
                "test_config": {
                    "concurrent_count": args.concurrent,
                    "total_requests": args.total,
                    "base_url": args.base_url,
                    "test_time": datetime.now().isoformat()
                },
                "results": asdict(test_result),
                "monitor_api": monitor_result
            }, f, indent=2, ensure_ascii=False)
        
        print(f"\n测试结果已保存到: {result_file}")


if __name__ == "__main__":
    asyncio.run(main())
