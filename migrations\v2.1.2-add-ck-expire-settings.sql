-- ========================================
-- 06-添加CK自动过期管理配置
-- 执行顺序：第6步 - 添加CK过期管理相关配置
-- 
-- 功能说明：
-- 1. 添加CK自动过期时间配置（默认30分钟）
-- 2. 添加CK过期检测开关配置
-- 3. 添加CK过期检测间隔配置
-- 4. 支持重复执行，不会重复插入数据
-- ========================================

-- 设置连接字符集
SET NAMES utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 设置时区
SET time_zone = '+08:00';

USE `walmart_card_db`;

-- ========================================
-- 1. 添加CK自动过期管理配置
-- ========================================

-- 插入CK过期管理相关配置
INSERT IGNORE INTO `system_settings` (`key`, `value`, `description`, `type`, `is_public`) VALUES
('ck_expire_minutes', '30', 'CK自动过期时间（分钟）', 'number', 0),
('ck_expire_check_enabled', 'true', '是否启用CK自动过期检测', 'boolean', 0),
('ck_expire_check_interval', '5', 'CK过期检测间隔（分钟）', 'number', 0);

-- ========================================
-- 2. 验证配置是否添加成功
-- ========================================

-- 查询新添加的配置
SELECT 
    'ck_expire_settings_added' as `migration_name`,
    'completed' as `status`,
    '沃尔玛绑卡系统CK自动过期管理配置添加完成' as `message`,
    JSON_OBJECT(
        'ck_expire_minutes', (SELECT value FROM `system_settings` WHERE `key` = 'ck_expire_minutes'),
        'ck_expire_check_enabled', (SELECT value FROM `system_settings` WHERE `key` = 'ck_expire_check_enabled'),
        'ck_expire_check_interval', (SELECT value FROM `system_settings` WHERE `key` = 'ck_expire_check_interval'),
        'migration_timestamp', NOW(3)
    ) as `details`;

-- ========================================
-- 3. 配置说明
-- ========================================

/*
配置项说明：

1. ck_expire_minutes (默认: 30)
   - CK自动过期时间，单位：分钟
   - 从CK创建时间开始计算，超过此时间的CK将被自动标记为过期
   - 只有超级管理员可以修改此配置

2. ck_expire_check_enabled (默认: true)
   - 是否启用CK自动过期检测
   - 设置为false时，系统将停止自动过期检测
   - 只有超级管理员可以修改此配置

3. ck_expire_check_interval (默认: 5)
   - CK过期检测任务的执行间隔，单位：分钟
   - 定时任务将按此间隔检测过期的CK
   - 只有超级管理员可以修改此配置

过期处理逻辑：
- 过期的CK将被设置为 is_deleted=True 和 active=False
- 过期处理遵循现有的软删除机制
- 过期的CK不会被绑卡逻辑选中使用
- 过期处理会记录日志便于追踪
*/
