
import logging
import logging.handlers
import os
import sys
from pathlib import Path
from typing import Dict, Any, Optional

from app.core.config import settings


def setup_logging(log_level: str = "INFO") -> None:
    """
    设置应用程序的日志配置
    
    Args:
        log_level: 日志级别，默认为INFO
    """
    # 创建日志目录
    log_dir = Path("logs")
    log_dir.mkdir(exist_ok=True)
    
    # 设置日志级别
    level = getattr(logging, log_level.upper())
    
    # 创建格式化器
    formatter = logging.Formatter("%(asctime)s - %(name)s - %(levelname)s - %(message)s")
    
    # 创建处理器
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setFormatter(formatter)
    
    # 文件处理器 - 所有日志
    try:
        file_handler = logging.handlers.RotatingFileHandler(
            filename=log_dir / "app.log",
            maxBytes=10 * 1024 * 1024,  # 10 MB
            backupCount=5,
            encoding="utf-8",
        )
        file_handler.setFormatter(formatter)
        
        # 文件处理器 - 仅错误日志
        error_file_handler = logging.handlers.RotatingFileHandler(
            filename=log_dir / "error.log",
            maxBytes=10 * 1024 * 1024,  # 10 MB
            backupCount=5,
            encoding="utf-8",
        )
        error_file_handler.setLevel(logging.ERROR)
        error_file_handler.setFormatter(formatter)
        
        handlers = [console_handler, file_handler, error_file_handler]
    except Exception as e:
        print(f"警告: 无法创建日志文件处理器: {str(e)}")
        handlers = [console_handler]
    
    # 配置根日志记录器
    logging.basicConfig(
        level=level,
        handlers=handlers,
    )
    
    # 设置第三方库的日志级别
    logging.getLogger("uvicorn").setLevel(logging.WARNING)
    logging.getLogger("fastapi").setLevel(logging.WARNING)
    logging.getLogger("sqlalchemy").setLevel(logging.WARNING)
    
    # 获取应用程序日志记录器并设置级别
    app_logger = logging.getLogger("app")
    app_logger.setLevel(level)
    
    # 记录日志配置完成
    app_logger.info(f"日志系统初始化完成，级别: {log_level}")


def get_logger(name: str) -> logging.Logger:
    """
    获取指定名称的日志记录器
    
    Args:
        name: 日志记录器名称
        
    Returns:
        logging.Logger: 日志记录器实例
    """
    return logging.getLogger(f"app.{name}")
