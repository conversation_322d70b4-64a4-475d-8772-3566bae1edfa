#!/usr/bin/env python3
"""
沃尔玛绑卡系统测试工具使用示例

本文件展示如何使用测试工具包中的各个组件
"""

import asyncio
import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from utils import MockWalmartAPI, TestDataManager, PerformanceAnalyzer, ConcurrencyTester, ConcurrencyConfig


async def example_mock_api_usage():
    """示例：Mock API的使用"""
    print("=" * 50)
    print("Mock API使用示例")
    print("=" * 50)
    
    # 创建Mock API实例
    mock_api = MockWalmartAPI(
        success_rate=0.8,  # 80%成功率
        delay_range=(0.05, 0.2),  # 50-200ms延迟
        enable_random_errors=True
    )
    
    print("1. 基本API调用:")
    for i in range(5):
        response = await mock_api.bind_card(f"6222000000000{i:03d}", "123456")
        result = response.json()
        
        print(f"  卡号: 6222000000000{i:03d}")
        print(f"  结果: {'成功' if result['success'] else '失败'}")
        if not result['success']:
            print(f"  错误: {result['error']} (代码: {result['errorcode']})")
        print()
    
    print("2. API统计信息:")
    stats = mock_api.get_stats()
    print(f"  总调用次数: {stats['total_calls']}")
    print(f"  成功次数: {stats['success_calls']}")
    print(f"  失败次数: {stats['failed_calls']}")
    print(f"  成功率: {stats['success_rate']:.1%}")
    print(f"  平均响应时间: {stats['average_response_time']:.3f}秒")
    print(f"  错误分布: {stats['error_distribution']}")
    
    print("3. 最近调用记录:")
    recent_calls = mock_api.get_recent_calls(3)
    for call in recent_calls:
        print(f"  {call['timestamp']:.0f}: {call['card_no']} -> {'成功' if call['success'] else '失败'}")


async def example_performance_analyzer_usage():
    """示例：性能分析器的使用"""
    print("\n" + "=" * 50)
    print("性能分析器使用示例")
    print("=" * 50)
    
    # 创建性能分析器
    analyzer = PerformanceAnalyzer()
    
    # 模拟一些测试结果
    mock_results = [
        {'success': True, 'response_time': 0.15, 'ck_id': 1, 'department_id': 1},
        {'success': True, 'response_time': 0.12, 'ck_id': 2, 'department_id': 1},
        {'success': False, 'response_time': 0.08, 'error_type': '1001'},
        {'success': True, 'response_time': 0.18, 'ck_id': 1, 'department_id': 2},
        {'success': True, 'response_time': 0.14, 'ck_id': 3, 'department_id': 2},
    ]
    
    print("1. 分析模拟结果:")
    analysis = analyzer.analyze_concurrent_results(mock_results)
    
    basic_metrics = analysis['basic_metrics']
    print(f"  总请求数: {basic_metrics['total_requests']}")
    print(f"  成功率: {basic_metrics['success_rate']:.1%}")
    
    response_time_analysis = analysis['response_time_analysis']
    print(f"  平均响应时间: {response_time_analysis['average']:.3f}秒")
    print(f"  P95响应时间: {response_time_analysis['p95']:.3f}秒")
    
    load_balance = analysis['load_balance_analysis']
    print(f"  CK负载均衡分数: {load_balance['ck_load_balance']['balance_score']:.3f}")
    
    print("2. 权重分配验证:")
    expected_weights = {1: 0.6, 2: 0.4}  # 部门1期望60%，部门2期望40%
    weight_verification = analyzer.verify_weight_distribution(expected_weights, tolerance=0.2)
    
    print(f"  验证结果: {'通过' if weight_verification['passed'] else '未通过'}")
    for dept_id, details in weight_verification['details'].items():
        print(f"  部门{dept_id}: 期望{details['expected_ratio']:.1%}, 实际{details['actual_ratio']:.1%}")
    
    print("3. 性能报告:")
    report = analyzer.generate_report()
    print(report)


async def example_concurrency_tester_usage():
    """示例：并发测试器的使用"""
    print("\n" + "=" * 50)
    print("并发测试器使用示例")
    print("=" * 50)
    
    # 创建并发测试配置
    config = ConcurrencyConfig(
        concurrent_level=10,
        test_duration=5,
        ramp_up_time=1,
        max_workers=5
    )
    
    # 创建并发测试器
    tester = ConcurrencyTester(config)
    
    # 模拟绑卡函数
    async def mock_binding_function(card_data):
        # 模拟处理时间
        await asyncio.sleep(0.1)
        
        # 模拟结果
        import random
        success = random.random() > 0.3  # 70%成功率
        
        return {
            'success': success,
            'response_time': 0.1,
            'card_id': card_data['id'],
            'ck_id': random.randint(1, 3),
            'department_id': random.randint(1, 2),
            'error_type': None if success else 'mock_error'
        }
    
    # 准备测试数据
    test_data = [{'id': f'card_{i}', 'card_number': f'6222{i:012d}'} for i in range(20)]
    
    print("1. 执行并发测试:")
    results = await tester.run_concurrent_binding_test(mock_binding_function, test_data)
    
    print(f"  完成请求数: {len(results)}")
    successful = sum(1 for r in results if r['success'])
    print(f"  成功请求数: {successful}")
    print(f"  成功率: {successful/len(results):.1%}")
    
    print("2. 测试摘要:")
    summary = tester.get_test_summary()
    print(f"  配置并发级别: {summary['config']['concurrent_level']}")
    print(f"  实际处理请求: {summary['results']['total_requests']}")
    print(f"  总耗时: {summary['timing']['total_duration']:.2f}秒")
    print(f"  QPS: {summary['timing']['requests_per_second']:.2f}")
    
    print("3. 监控数据:")
    monitoring_data = tester.get_monitoring_data()
    if monitoring_data:
        print(f"  监控点数量: {len(monitoring_data)}")
        last_point = monitoring_data[-1]
        print(f"  最终QPS: {last_point['qps']:.2f}")
        print(f"  最终成功率: {last_point['success_rate']:.1%}")


async def example_integrated_usage():
    """示例：集成使用所有工具"""
    print("\n" + "=" * 50)
    print("集成使用示例")
    print("=" * 50)
    
    # 设置测试环境
    os.environ['TESTING'] = 'true'
    
    # 1. 创建Mock API
    mock_api = MockWalmartAPI(success_rate=0.75, delay_range=(0.05, 0.15))
    
    # 2. 创建性能分析器
    analyzer = PerformanceAnalyzer()
    analyzer.start_analysis()
    
    # 3. 创建并发测试器
    config = ConcurrencyConfig(concurrent_level=20, test_duration=10)
    tester = ConcurrencyTester(config)
    
    # 4. 定义集成绑卡函数
    async def integrated_binding_function(card_data):
        import time
        start_time = time.time()
        
        try:
            # 调用Mock API
            response = await mock_api.bind_card(card_data['card_number'], 'test_pwd')
            result = response.json()
            
            response_time = time.time() - start_time
            
            return {
                'success': result['success'],
                'response_time': response_time,
                'card_id': card_data['id'],
                'ck_id': 1,
                'department_id': 1,
                'error_type': result.get('errorcode') if not result['success'] else None
            }
            
        except Exception as e:
            response_time = time.time() - start_time
            return {
                'success': False,
                'response_time': response_time,
                'card_id': card_data['id'],
                'error_type': 'exception',
                'error': str(e)
            }
    
    # 5. 准备测试数据
    test_cards = [
        {'id': f'card_{i}', 'card_number': f'6222{i:012d}'}
        for i in range(50)
    ]
    
    print("执行集成测试...")
    
    # 6. 执行并发测试
    results = await tester.run_concurrent_binding_test(
        integrated_binding_function,
        test_cards
    )
    
    # 7. 结束性能分析
    analyzer.end_analysis()
    
    # 8. 分析结果
    analysis = analyzer.analyze_concurrent_results(results)
    
    print("集成测试结果:")
    basic_metrics = analysis['basic_metrics']
    print(f"  总请求数: {basic_metrics['total_requests']}")
    print(f"  成功率: {basic_metrics['success_rate']:.1%}")
    print(f"  QPS: {basic_metrics['requests_per_second']:.2f}")
    
    response_time_analysis = analysis['response_time_analysis']
    print(f"  平均响应时间: {response_time_analysis['average']:.3f}秒")
    print(f"  P95响应时间: {response_time_analysis['p95']:.3f}秒")
    
    performance_grade = analysis['performance_grade']
    print(f"  性能等级: {performance_grade['overall_grade']}")
    print(f"  性能总结: {performance_grade['performance_summary']}")
    
    # 9. 获取API统计
    api_stats = mock_api.get_stats()
    print(f"  API调用统计: 总计{api_stats['total_calls']}次, 成功率{api_stats['success_rate']:.1%}")


async def main():
    """主函数"""
    print("沃尔玛绑卡系统测试工具使用示例")
    print("=" * 60)
    
    try:
        # 运行各个示例
        await example_mock_api_usage()
        await example_performance_analyzer_usage()
        await example_concurrency_tester_usage()
        await example_integrated_usage()
        
        print("\n" + "=" * 60)
        print("✅ 所有示例运行完成!")
        print("💡 更多详细用法请参考 README.md")
        
    except Exception as e:
        print(f"\n💥 示例运行失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    asyncio.run(main())