import { computed } from 'vue'
import { usePermissionStore } from '@/store/modules/permission'

/**
 * 简化权限组合式函数
 */
export function usePermissions() {
  const permissionStore = usePermissionStore()

  // 检查菜单权限
  const hasMenuAccess = (menuCode) => {
    return computed(() => permissionStore.hasMenuAccess(menuCode))
  }

  // 检查数据权限
  const canAccessMerchantData = (merchantId) => {
    return computed(() => permissionStore.canAccessMerchantData(merchantId))
  }

  // 是否为超级管理员
  const isSuperAdmin = computed(() => permissionStore.isSuperuser)

  // 是否为商户管理员
  const isMerchantAdmin = computed(() => permissionStore.isMerchantAdmin)

  return {
    hasMenuAccess,
    canAccessMerchantData,
    isSuperAdmin,
    isMerchantAdmin
  }
}
