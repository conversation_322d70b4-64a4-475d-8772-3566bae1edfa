import { nextTick } from 'vue'
import router from '@/router'

/**
 * 全局商家切换监听器
 * 当超级管理员切换商家时，自动刷新当前页面的数据
 */
class MerchantSwitchListener {
    constructor() {
        this.listeners = new Set()
        this.isListening = false
    }

    /**
     * 开始监听商家切换事件
     */
    startListening() {
        if (this.isListening) return

        window.addEventListener('merchant-switched', this.handleMerchantSwitch.bind(this))
        this.isListening = true
        console.log('全局商家切换监听器已启动')
    }

    /**
     * 停止监听商家切换事件
     */
    stopListening() {
        if (!this.isListening) return

        window.removeEventListener('merchant-switched', this.handleMerchantSwitch.bind(this))
        this.isListening = false
        this.listeners.clear()
        console.log('全局商家切换监听器已停止')
    }

    /**
     * 注册页面级别的数据刷新函数
     * @param {Function} refreshFunction 页面数据刷新函数
     * @returns {Function} 取消注册的函数
     */
    registerRefreshFunction(refreshFunction) {
        this.listeners.add(refreshFunction)
        
        // 返回取消注册的函数
        return () => {
            this.listeners.delete(refreshFunction)
        }
    }

    /**
     * 处理商家切换事件
     */
    async handleMerchantSwitch() {
        console.log('检测到商家切换，开始刷新页面数据...')
        
        // 等待下一个tick，确保store状态已更新
        await nextTick()
        
        // 调用所有注册的刷新函数
        for (const refreshFunction of this.listeners) {
            try {
                await refreshFunction()
            } catch (error) {
                console.error('刷新页面数据失败:', error)
            }
        }
        
        console.log(`已刷新 ${this.listeners.size} 个页面的数据`)
    }

    /**
     * 获取当前路由对应的页面刷新函数
     * 这个方法可以根据路由自动判断需要刷新哪些数据
     */
    getRouteSpecificRefreshFunctions() {
        const currentRoute = router.currentRoute.value
        const refreshFunctions = []

        // 根据路由路径判断需要刷新的数据
        switch (currentRoute.path) {
            case '/cards':
                // 绑卡数据页面
                refreshFunctions.push('refreshCardData')
                break
            case '/merchants':
                // 商户管理页面
                refreshFunctions.push('refreshMerchantData')
                break
            case '/departments':
                // 部门管理页面
                refreshFunctions.push('refreshDepartmentData')
                break
            case '/users':
                // 用户管理页面
                refreshFunctions.push('refreshUserData')
                break
            case '/roles':
                // 角色管理页面
                refreshFunctions.push('refreshRoleData')
                break
            case '/dashboard':
                // 仪表盘页面
                refreshFunctions.push('refreshDashboardData')
                break
            default:
                // 其他页面，尝试通用刷新
                break
        }

        return refreshFunctions
    }
}

// 创建全局实例
const merchantSwitchListener = new MerchantSwitchListener()

export default merchantSwitchListener
