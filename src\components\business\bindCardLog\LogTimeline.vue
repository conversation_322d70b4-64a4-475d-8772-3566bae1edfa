<template>
  <div class="log-timeline">
    <div class="timeline-header">
      <h3>绑卡操作日志</h3>
      <div class="timeline-actions">
        <el-button type="primary" size="small" @click="refreshLogs" :loading="loading">
          <el-icon>
            <Refresh />
          </el-icon> 刷新
        </el-button>
      </div>
    </div>

    <div v-if="loading" class="loading-container">
      <el-skeleton :rows="5" animated />
    </div>
    <div v-else-if="logs.length === 0" class="empty-container">
      <el-empty description="暂无日志记录" />
    </div>
    <div v-else class="timeline-container">
      <el-timeline>
        <el-timeline-item v-for="log in logs" :key="log.id" :timestamp="formatTimestamp(log.timestamp)"
          :type="getLogTypeIcon(log.log_type).type" :icon="getLogTypeIcon(log.log_type).icon"
          :color="getLogLevelColor(log.log_level)" :size="log.log_level === 'error' ? 'large' : 'normal'">
          <el-card class="timeline-card" :class="{ 'is-error': log.log_level === 'error' }">
            <template #header>
              <div class="card-header">
                <span class="log-type-badge" :style="{ backgroundColor: getLogTypeColor(log.log_type) }">
                  {{ getLogTypeLabel(log.log_type) }}
                </span>
                <span class="log-message">{{ log.message }}</span>
                <el-tag v-if="log.attempt_number" size="small" type="info" class="attempt-tag">
                  尝试 #{{ log.attempt_number }}
                </el-tag>
              </div>
            </template>

            <div class="log-content">
              <!-- 请求数据 -->
              <div v-if="log.request_data" class="log-section">
                <div class="section-header" @click="toggleSection('request_' + log.id)">
                  <el-icon>
                    <Document />
                  </el-icon>
                  <span>请求数据</span>
                  <el-icon class="toggle-icon" :class="{ 'is-active': expandedSections['request_' + log.id] }">
                    <ArrowDown />
                  </el-icon>
                </div>
                <div v-show="expandedSections['request_' + log.id]" class="section-content">
                  <pre>{{ formatJson(log.request_data) }}</pre>
                </div>
              </div>

              <!-- 响应数据 -->
              <div v-if="log.response_data" class="log-section">
                <div class="section-header" @click="toggleSection('response_' + log.id)">
                  <el-icon>
                    <Document />
                  </el-icon>
                  <span>响应数据</span>
                  <el-icon class="toggle-icon" :class="{ 'is-active': expandedSections['response_' + log.id] }">
                    <ArrowDown />
                  </el-icon>
                </div>
                <div v-show="expandedSections['response_' + log.id]" class="section-content">
                  <pre>{{ formatJson(log.response_data) }}</pre>
                </div>
              </div>

              <!-- 详细信息 -->
              <div v-if="log.details" class="log-section">
                <div class="section-header" @click="toggleSection('details_' + log.id)">
                  <el-icon>
                    <InfoFilled />
                  </el-icon>
                  <span>详细信息</span>
                  <el-icon class="toggle-icon" :class="{ 'is-active': expandedSections['details_' + log.id] }">
                    <ArrowDown />
                  </el-icon>
                </div>
                <div v-show="expandedSections['details_' + log.id]" class="section-content">
                  <pre>{{ formatJson(log.details) }}</pre>
                </div>
              </div>

              <!-- 其他元数据 -->
              <div class="log-metadata">
                <el-tag v-if="log.walmart_ck_id" size="small" type="info">
                  沃尔玛CK: {{ log.walmart_ck_id }}
                </el-tag>
                <el-tag v-if="log.duration_ms" size="small" type="info">
                  耗时: {{ (log.duration_ms / 1000).toFixed(2) }}s
                </el-tag>
                <el-tag v-if="log.ip_address" size="small" type="info">
                  IP: {{ log.ip_address }}
                </el-tag>
              </div>
            </div>
          </el-card>
        </el-timeline-item>
      </el-timeline>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination v-model:current-page="currentPage" v-model:page-size="pageSize" :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper" :total="total" @size-change="handleSizeChange"
          @current-change="handleCurrentChange" />
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, reactive, watch } from 'vue'
import { useBindingLogsStore } from '@/store/modules/bindingLogs'
import {
  Refresh, Document, InfoFilled, ArrowDown,
  Loading, Warning, CircleCheck, CircleClose
} from '@element-plus/icons-vue'
import { formatDateTime } from '@/utils/dateUtils'

const props = defineProps({
  cardId: {
    type: String,
    required: true
  }
})

const bindingLogsStore = useBindingLogsStore()
const loading = computed(() => bindingLogsStore.isLoading)
const logs = computed(() => bindingLogsStore.getLogs)
const pagination = computed(() => bindingLogsStore.getPagination)

const currentPage = ref(1)
const pageSize = ref(20)
const total = computed(() => pagination.value.total)

// 展开/折叠状态
const expandedSections = reactive({})

// 切换展开/折叠状态
const toggleSection = (sectionId) => {
  expandedSections[sectionId] = !expandedSections[sectionId]
}

// 获取日志
const fetchLogs = async () => {
  await bindingLogsStore.fetchLogsByCardId(props.cardId, {
    page: currentPage.value,
    pageSize: pageSize.value
  })
}

// 刷新日志
const refreshLogs = () => {
  fetchLogs()
}

// 暴露方法给父组件调用
defineExpose({
  refreshLogs,
  fetchLogs
})

// 处理分页变化
const handleSizeChange = (size) => {
  pageSize.value = size
  fetchLogs()
}

const handleCurrentChange = (page) => {
  currentPage.value = page
  fetchLogs()
}

// 格式化时间戳
const formatTimestamp = (timestamp) => {
  if (!timestamp) return '-'
  return formatDateTime(timestamp)
}

// 格式化JSON
const formatJson = (json) => {
  if (!json) return '{}'
  try {
    return JSON.stringify(json, null, 2)
  } catch (e) {
    return String(json)
  }
}

// 获取日志类型图标
const getLogTypeIcon = (logType) => {
  const icons = {
    'api_request': { icon: Document, type: 'primary' },
    'api_response': { icon: Document, type: 'success' },
    'walmart_request': { icon: Document, type: 'primary' },
    'walmart_response': { icon: Document, type: 'success' },
    'bind_attempt': { icon: CircleCheck, type: 'warning' },  // 新增：绑卡尝试日志
    'retry': { icon: Loading, type: 'warning' },
    'error': { icon: CircleClose, type: 'danger' },
    'status_change': { icon: CircleCheck, type: 'success' },
    'callback': { icon: Document, type: 'info' },
    'system': { icon: InfoFilled, type: 'info' }
  }
  return icons[logType] || { icon: InfoFilled, type: 'info' }
}

// 获取日志级别颜色
const getLogLevelColor = (logLevel) => {
  const colors = {
    'debug': '#909399',
    'info': '#409EFF',
    'warning': '#E6A23C',
    'error': '#F56C6C'
  }
  return colors[logLevel] || '#909399'
}

// 获取日志类型颜色
const getLogTypeColor = (logType) => {
  const colors = {
    'api_request': '#409EFF',
    'api_response': '#67C23A',
    'walmart_request': '#409EFF',
    'walmart_response': '#67C23A',
    'retry': '#E6A23C',
    'error': '#F56C6C',
    'status_change': '#67C23A',
    'callback': '#909399',
    'system': '#909399'
  }
  return colors[logType] || '#909399'
}

// 获取日志类型标签
const getLogTypeLabel = (logType) => {
  const labels = {
    'api_request': 'API请求',
    'api_response': 'API响应',
    'walmart_request': '沃尔玛请求',
    'walmart_response': '沃尔玛响应',
    'bind_attempt': '绑卡尝试',  // 新增：绑卡尝试日志标签
    'retry': '重试',
    'error': '错误',
    'status_change': '状态变更',
    'callback': '回调',
    'system': '系统'
  }
  return labels[logType] || '未知'
}

// 监听 cardId 变化，自动刷新数据
watch(() => props.cardId, (newCardId, oldCardId) => {
  if (newCardId && newCardId !== oldCardId) {
    // 重置分页
    currentPage.value = 1
    fetchLogs()
  }
}, { immediate: false })

onMounted(() => {
  fetchLogs()
})
</script>

<style scoped>
.log-timeline {
  padding: 16px;
}

.timeline-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.timeline-container {
  margin-top: 20px;
}

.timeline-card {
  margin-bottom: 10px;
}

.timeline-card.is-error {
  border: 1px solid #F56C6C;
}

.card-header {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 8px;
}

.log-type-badge {
  padding: 2px 6px;
  border-radius: 4px;
  color: white;
  font-size: 12px;
  font-weight: bold;
}

.log-message {
  flex: 1;
  font-weight: bold;
}

.attempt-tag {
  margin-left: auto;
}

.log-content {
  margin-top: 10px;
}

.log-section {
  margin-bottom: 12px;
  border: 1px solid #EBEEF5;
  border-radius: 4px;
  overflow: hidden;
}

.section-header {
  display: flex;
  align-items: center;
  padding: 8px 12px;
  background-color: #F5F7FA;
  cursor: pointer;
  user-select: none;
}

.section-header .el-icon {
  margin-right: 8px;
}

.toggle-icon {
  margin-left: auto;
  transition: transform 0.3s;
}

.toggle-icon.is-active {
  transform: rotate(180deg);
}

.section-content {
  padding: 12px;
  background-color: #FAFAFA;
  max-height: 300px;
  overflow: auto;
}

.section-content pre {
  margin: 0;
  white-space: pre-wrap;
  word-break: break-word;
}

.log-metadata {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-top: 12px;
}

.loading-container,
.empty-container {
  padding: 20px;
  text-align: center;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: center;
}
</style>
