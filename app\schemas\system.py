from typing import Optional
from pydantic import BaseModel, Field


class SystemParams(BaseModel):
    """系统参数模型"""

    daily_bind_limit: int = Field(1000, description="每日绑卡限制")
    api_rate_limit: int = Field(60, description="API速率限制(次/分钟)")
    max_retry_times: int = Field(3, description="最大重试次数")
    bind_timeout_seconds: int = Field(30, description="绑卡超时时间(秒)")
    verification_code_expires: int = Field(300, description="验证码过期时间(秒)")
    log_retention_days: int = Field(90, description="日志保留天数")
    enable_ip_whitelist: bool = Field(True, description="是否启用IP白名单")
    enable_security_audit: bool = Field(True, description="是否启用安全审计")
    maintenance_mode: bool = Field(False, description="维护模式")
    maintenance_message: Optional[str] = Field(None, description="维护模式消息")


class PublicSystemParams(BaseModel):
    """公开系统参数模型，不需要认证即可访问"""

    api_timeout: int = Field(15, description="API请求超时时间(秒)")
    maintenance_mode: bool = Field(False, description="系统是否处于维护模式")
    maintenance_message: Optional[str] = Field(None, description="维护模式消息")
    version: str = Field("1.0.0", description="系统版本")
