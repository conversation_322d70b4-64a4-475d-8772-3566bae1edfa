<template>
    <div class="today-bind-stats">
        <el-card shadow="hover" class="stats-card">
            <template #header>
                <div class="card-header">
                    <span>今日绑卡统计</span>
                    <el-button type="primary" size="small" :icon="Refresh" circle @click="refreshStats"></el-button>
                </div>
            </template>

            <div class="stats-content" v-loading="loading">
                <!-- 统计卡片 -->
                <el-row :gutter="20" class="mb-4">
                    <el-col :span="6" v-for="(item, index) in statsItems" :key="index">
                        <el-card shadow="hover" class="stat-card">
                            <template #header>
                                <div class="stat-header">
                                    <el-icon :style="{ color: item.color }">
                                        <component :is="item.icon" />
                                    </el-icon>
                                    <span>{{ item.label }}</span>
                                </div>
                            </template>
                            <div class="stat-value" :style="{ color: item.color }">{{ item.value }}</div>
                        </el-card>
                    </el-col>
                </el-row>

                <!-- 成功率和小时数据 -->
                <el-row :gutter="20">
                    <el-col :span="12">
                        <el-card class="full-width">
                            <template #header>
                                <div class="card-header">
                                    <span>绑卡成功率</span>
                                </div>
                            </template>
                            <div class="success-rate-container">
                                <el-progress
                                    type="dashboard"
                                    :percentage="successRate"
                                    :color="getSuccessRateColor"
                                >
                                    <template #default="{ percentage }">
                                        <div class="progress-content">
                                            <span class="progress-value">{{ percentage }}%</span>
                                            <span class="progress-label">成功率</span>
                                        </div>
                                    </template>
                                </el-progress>
                                <div class="success-rate-details">
                                    <div class="detail-item">
                                        <span class="label">成功：</span>
                                        <span class="value success">{{ statsData.success }}</span>
                                    </div>
                                    <div class="detail-item">
                                        <span class="label">失败：</span>
                                        <span class="value error">{{ statsData.failed }}</span>
                                    </div>
                                    <div class="detail-item">
                                        <span class="label">待处理：</span>
                                        <span class="value warning">{{ statsData.pending }}</span>
                                    </div>
                                </div>
                            </div>
                        </el-card>
                    </el-col>
                    <el-col :span="12">
                        <el-card class="full-width">
                            <template #header>
                                <div class="card-header">
                                    <span>每小时绑卡数据</span>
                                </div>
                            </template>
                            <el-table :data="hourlyTableData" height="300" style="width: 100%">
                                <el-table-column prop="hour" label="时间" width="100" />
                                <el-table-column prop="count" label="绑卡数量" width="100" />
                                <el-table-column label="趋势" align="center">
                                    <template #default="scope">
                                        <div class="trend-cell">
                                            <el-progress
                                                :percentage="getHourlyPercentage(scope.row.count)"
                                                :color="scope.row.count > 0 ? '#67C23A' : '#909399'"
                                                :show-text="false"
                                                :stroke-width="8"
                                            />
                                        </div>
                                    </template>
                                </el-table-column>
                            </el-table>
                        </el-card>
                    </el-col>
                </el-row>
            </div>
        </el-card>
    </div>
</template>

<script setup>
import { ref, onMounted, onBeforeUnmount, watch, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { Loading, CircleCheck, CircleClose, Document, Select, Refresh } from '@element-plus/icons-vue'
import {cardDataApi} from '@/api/modules/cardData'

const props = defineProps({
    merchantId: {
        type: [String, Number],
        default: null
    }
})

// 状态
const loading = ref(false)
const statsData = ref({
    total: 0,
    success: 0,
    failed: 0,
    pending: 0,
    success_rate: 0,
    hourly_data: []
})

// 统计项目
const statsItems = computed(() => [
    {
        label: '总绑卡数',
        value: statsData.value.total,
        icon: Document,
        color: '#409eff'
    },
    {
        label: '成功数',
        value: statsData.value.success,
        icon: CircleCheck,
        color: '#67c23a'
    },
    {
        label: '失败数',
        value: statsData.value.failed,
        icon: CircleClose,
        color: '#f56c6c'
    },
    {
        label: '待处理',
        value: statsData.value.pending,
        icon: Loading,
        color: '#e6a23c'
    }
])

// 计算成功率
const successRate = computed(() => {
    const total = statsData.value.success + statsData.value.failed
    return total > 0 ? Math.round((statsData.value.success / total) * 100) : 0
})

// 获取成功率颜色
const getSuccessRateColor = computed(() => {
    const rate = successRate.value
    if (rate >= 90) return '#67C23A'
    if (rate >= 70) return '#E6A23C'
    return '#F56C6C'
})

// 处理小时数据
const hourlyTableData = computed(() => {
    return statsData.value.hourly_data.map(item => ({
        hour: item.hour,
        count: item.count
    }))
})

// 计算每小时数据的百分比
const getHourlyPercentage = (count) => {
    const maxCount = Math.max(...statsData.value.hourly_data.map(item => item.count))
    return maxCount > 0 ? (count / maxCount) * 100 : 0
}

// 获取统计数据
const fetchStats = async () => {
    loading.value = true
    try {
        // 构建请求参数
        const params = {}
        if (props.merchantId) {
            params.merchantId = props.merchantId
        }

        // 调用API获取统计数据
        const data = await cardDataApi.getCardStatistics(params)

        // 更新统计数据
        statsData.value = {
            total: data.totalSuccess + data.totalFailed + (data.pendingCount || 0),
            success: data.totalSuccess,
            failed: data.totalFailed,
            pending: data.pendingCount || 0,
            success_rate: data.totalSuccess / (data.totalSuccess + data.totalFailed || 1),
            hourly_data: data.hourlyData || generateEmptyHourlyData()
        }
    } catch (error) {
        console.error('获取统计数据失败:', error)
        ElMessage.error('获取统计数据失败')
    } finally {
        loading.value = false
    }
}

// 生成空的小时数据
const generateEmptyHourlyData = () => {
    const hourlyData = []
    for (let i = 0; i < 24; i++) {
        const hour = i.toString().padStart(2, '0') + ':00'
        hourlyData.push({ hour, count: 0 })
    }
    return hourlyData
}

// 刷新统计
const refreshStats = () => {
    fetchStats()
    ElMessage.success('数据已刷新')
}

// 监听商家ID变化
watch(() => props.merchantId, () => {
    fetchStats()
})

// 组件挂载时获取数据
onMounted(() => {
    fetchStats()
})

onBeforeUnmount(() => {
    // 清理逻辑
})
</script>

<style scoped>
.today-bind-stats {
    margin-bottom: 20px;
}

.mb-4 {
    margin-bottom: 16px;
}

.card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.stat-card {
    height: 100%;
}

.stat-header {
    display: flex;
    align-items: center;
    gap: 8px;
}

.stat-value {
    font-size: 24px;
    font-weight: bold;
    text-align: center;
}

.full-width {
    width: 100%;
}

.success-rate-container {
    display: flex;
    justify-content: space-around;
    align-items: center;
    padding: 20px;
}

.progress-content {
    display: flex;
    flex-direction: column;
    align-items: center;
}

.progress-value {
    font-size: 28px;
    font-weight: bold;
    color: #303133;
}

.progress-label {
    font-size: 14px;
    color: #909399;
    margin-top: 5px;
}

.success-rate-details {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.detail-item {
    display: flex;
    align-items: center;
    gap: 8px;
}

.detail-item .label {
    color: #909399;
}

.detail-item .value {
    font-weight: bold;
}

.detail-item .value.success {
    color: #67C23A;
}

.detail-item .value.error {
    color: #F56C6C;
}

.detail-item .value.warning {
    color: #E6A23C;
}

.trend-cell {
    padding: 0 20px;
}
</style>