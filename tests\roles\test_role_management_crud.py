"""
角色管理CRUD操作测试
"""
import pytest
from fastapi.testclient import TestClient
from sqlalchemy.orm import Session

from app.main import app
from app.models.role import Role
from app.models.permission import Permission
from app.models.user import User
from test.conftest import get_test_db, create_test_user, create_test_merchant


class TestRoleManagementCRUD:
    """角色管理CRUD测试类"""

    def setup_method(self):
        """每个测试方法前的设置"""
        self.client = TestClient(app)
        self.db = next(get_test_db())
        
        # 创建测试商户
        self.test_merchant = create_test_merchant(self.db, "test_merchant_role_crud")
        
        # 创建超级管理员用户
        self.admin_user = create_test_user(
            self.db, 
            username="admin_role_crud_test",
            role_codes=["super_admin"],
            merchant_id=None,
            is_superuser=True
        )

    def teardown_method(self):
        """每个测试方法后的清理"""
        self.db.close()

    def test_create_role_success(self):
        """测试创建角色成功"""
        # 使用超级管理员登录
        login_data = {"username": "admin_role_crud_test", "password": "testpass123"}
        login_response = self.client.post("/api/v1/auth/login", data=login_data)
        assert login_response.status_code == 200
        token = login_response.json()["access_token"]
        headers = {"Authorization": f"Bearer {token}"}

        # 创建角色
        role_data = {
            "name": "测试角色",
            "code": "test_role_crud",
            "description": "用于测试的角色",
            "is_enabled": True,
            "is_system": False,
            "sort_order": 1
        }
        
        response = self.client.post(
            "/api/v1/roles/",
            json=role_data,
            headers=headers
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data["code"] == "test_role_crud"
        assert data["name"] == "测试角色"

    def test_assign_permissions_to_role(self):
        """测试为角色分配权限"""
        # 使用超级管理员登录
        login_data = {"username": "admin_role_crud_test", "password": "testpass123"}
        login_response = self.client.post("/api/v1/auth/login", data=login_data)
        token = login_response.json()["access_token"]
        headers = {"Authorization": f"Bearer {token}"}

        # 创建角色
        role_data = {
            "name": "权限测试角色",
            "code": "perm_test_role_crud",
            "description": "权限测试角色"
        }
        
        role_response = self.client.post(
            "/api/v1/roles/",
            json=role_data,
            headers=headers
        )
        role_id = role_response.json()["id"]

        # 创建权限
        permission_data = {
            "code": "role:test_perm_crud",
            "name": "角色测试权限",
            "resource_type": "api"
        }
        
        perm_response = self.client.post(
            "/api/v1/permissions/",
            json=permission_data,
            headers=headers
        )
        permission_id = perm_response.json()["id"]

        # 为角色分配权限
        assign_data = {
            "role_id": role_id,
            "permission_ids": [permission_id]
        }
        
        response = self.client.post(
            f"/api/v1/roles/{role_id}/permissions",
            json=assign_data,
            headers=headers
        )
        assert response.status_code == 200

    def test_update_role_permissions_put(self):
        """测试更新角色权限（PUT方式）"""
        # 使用超级管理员登录
        login_data = {"username": "admin_role_crud_test", "password": "testpass123"}
        login_response = self.client.post("/api/v1/auth/login", data=login_data)
        token = login_response.json()["access_token"]
        headers = {"Authorization": f"Bearer {token}"}

        # 创建角色
        role_data = {
            "name": "更新权限角色",
            "code": "update_perm_role_crud"
        }
        
        role_response = self.client.post(
            "/api/v1/roles/",
            json=role_data,
            headers=headers
        )
        role_id = role_response.json()["id"]

        # 创建多个权限
        permissions = []
        for i in range(3):
            permission_data = {
                "code": f"update:test_perm_crud_{i}",
                "name": f"更新测试权限{i}",
                "resource_type": "api"
            }
            
            perm_response = self.client.post(
                "/api/v1/permissions/",
                json=permission_data,
                headers=headers
            )
            permissions.append(perm_response.json()["id"])

        # 更新角色权限
        update_data = {
            "role_id": role_id,
            "permission_ids": permissions
        }
        
        response = self.client.put(
            f"/api/v1/roles/{role_id}/permissions",
            json=update_data,
            headers=headers
        )
        assert response.status_code == 200
        data = response.json()
        assert len(data["permissions"]) == 3

    def test_assign_menus_to_role(self):
        """测试为角色分配菜单"""
        # 使用超级管理员登录
        login_data = {"username": "admin_role_crud_test", "password": "testpass123"}
        login_response = self.client.post("/api/v1/auth/login", data=login_data)
        token = login_response.json()["access_token"]
        headers = {"Authorization": f"Bearer {token}"}

        # 创建角色
        role_data = {
            "name": "菜单测试角色",
            "code": "menu_test_role_crud"
        }
        
        role_response = self.client.post(
            "/api/v1/roles/",
            json=role_data,
            headers=headers
        )
        role_id = role_response.json()["id"]

        # 创建菜单
        menu_data = {
            "code": "role_test_menu_crud",
            "name": "角色测试菜单",
            "title": "角色测试菜单",
            "path": "/role-test-crud"
        }
        
        menu_response = self.client.post(
            "/api/v1/menus/",
            json=menu_data,
            headers=headers
        )
        menu_id = menu_response.json()["id"]

        # 为角色分配菜单
        assign_data = {
            "role_id": role_id,
            "menu_ids": [menu_id]
        }
        
        response = self.client.post(
            f"/api/v1/roles/{role_id}/menus",
            json=assign_data,
            headers=headers
        )
        assert response.status_code == 200

    def test_update_role_menus_put(self):
        """测试更新角色菜单（PUT方式）"""
        # 使用超级管理员登录
        login_data = {"username": "admin_role_crud_test", "password": "testpass123"}
        login_response = self.client.post("/api/v1/auth/login", data=login_data)
        token = login_response.json()["access_token"]
        headers = {"Authorization": f"Bearer {token}"}

        # 创建角色
        role_data = {
            "name": "更新菜单角色",
            "code": "update_menu_role_crud"
        }

        role_response = self.client.post(
            "/api/v1/roles/",
            json=role_data,
            headers=headers
        )
        role_id = role_response.json()["id"]

        # 创建多个菜单
        menus = []
        for i in range(2):
            menu_data = {
                "code": f"update_menu_crud_{i}",
                "name": f"更新测试菜单{i}",
                "title": f"更新测试菜单{i}",
                "path": f"/update-menu-crud-{i}"
            }

            menu_response = self.client.post(
                "/api/v1/menus/",
                json=menu_data,
                headers=headers
            )
            menus.append(menu_response.json()["id"])

        # 更新角色菜单 - 使用新的API格式
        response = self.client.put(
            f"/api/v1/roles/{role_id}/menus",
            json={"menus": menus},  # 修改为新的请求格式
            headers=headers
        )
        assert response.status_code == 200
        response_data = response.json()

        # 处理嵌套的响应格式
        data = response_data.get('data', response_data)
        assert len(data["menus"]) == 2
        assert data["message"] == "角色菜单更新成功"

    def test_user_role_assignment(self):
        """测试用户角色分配"""
        # 使用超级管理员登录
        login_data = {"username": "admin_role_crud_test", "password": "testpass123"}
        login_response = self.client.post("/api/v1/auth/login", data=login_data)
        token = login_response.json()["access_token"]
        headers = {"Authorization": f"Bearer {token}"}

        # 创建角色
        role_data = {
            "name": "用户测试角色",
            "code": "user_test_role_crud"
        }
        
        role_response = self.client.post(
            "/api/v1/roles/",
            json=role_data,
            headers=headers
        )
        role_id = role_response.json()["id"]

        # 创建用户
        user_data = {
            "username": "test_role_user_crud",
            "password": "testpass123",
            "email": "<EMAIL>",
            "full_name": "测试角色用户",
            "merchant_id": self.test_merchant.id,
            "is_active": True
        }
        
        user_response = self.client.post(
            "/api/v1/users",
            json=user_data,
            headers=headers
        )
        user_id = user_response.json()["id"]

        # 为用户分配角色
        response = self.client.post(
            f"/api/v1/users/{user_id}/roles",
            json=[role_id],
            headers=headers
        )
        assert response.status_code == 200

        # 获取用户角色
        get_response = self.client.get(f"/api/v1/users/{user_id}/roles", headers=headers)
        assert get_response.status_code == 200
        data = get_response.json()
        assert len(data["roles"]) == 1
        assert data["roles"][0]["id"] == role_id

    def test_update_user_roles_put(self):
        """测试更新用户角色（PUT方式）"""
        # 使用超级管理员登录
        login_data = {"username": "admin_role_crud_test", "password": "testpass123"}
        login_response = self.client.post("/api/v1/auth/login", data=login_data)
        token = login_response.json()["access_token"]
        headers = {"Authorization": f"Bearer {token}"}

        # 创建多个角色
        roles = []
        for i in range(2):
            role_data = {
                "name": f"用户更新角色{i}",
                "code": f"user_update_role_crud_{i}"
            }
            
            role_response = self.client.post(
                "/api/v1/roles/",
                json=role_data,
                headers=headers
            )
            roles.append(role_response.json()["id"])

        # 创建用户
        user_data = {
            "username": "test_update_role_user_crud",
            "password": "testpass123",
            "email": "<EMAIL>",
            "full_name": "测试更新角色用户",
            "merchant_id": self.test_merchant.id,
            "is_active": True
        }
        
        user_response = self.client.post(
            "/api/v1/users",
            json=user_data,
            headers=headers
        )
        user_id = user_response.json()["id"]

        # 更新用户角色
        response = self.client.put(
            f"/api/v1/users/{user_id}/roles",
            json=roles,
            headers=headers
        )
        assert response.status_code == 200
        data = response.json()
        assert len(data["roles"]) == 2
