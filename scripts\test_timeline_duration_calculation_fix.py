#!/usr/bin/env python3
"""
测试时间线耗时计算修复

验证新的时间线计算逻辑是否能正确处理duration_ms为空的情况
"""

import sys
import os

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

try:
    from sqlalchemy.orm import Session
    from app.db.session import SessionLocal
    from app.models.card_record import CardRecord
    from app.models.binding_log import BindingLog
    from app.services.binding_timeline_service import BindingTimelineService
    print("✅ 模块导入成功")
except ImportError as e:
    print(f"❌ 模块导入失败: {e}")
    print("请确保在项目根目录运行此脚本")
    sys.exit(1)


def test_timeline_calculation_fix():
    """测试时间线计算修复"""
    print("🔧 测试时间线耗时计算修复")
    print("=" * 60)
    
    db = SessionLocal()
    try:
        # 查询一个有多个日志记录的绑卡记录
        record = db.query(CardRecord).join(BindingLog).filter(
            CardRecord.status.in_(['success', 'failed'])
        ).order_by(CardRecord.created_at.desc()).first()
        
        if not record:
            print("❌ 没有找到有日志记录的绑卡记录")
            return
        
        print(f"📝 测试记录: {str(record.id)[:8]}...")
        print(f"📊 记录状态: {record.status}")
        print(f"📊 记录处理耗时: {record.process_time}秒")
        print()
        
        # 查询该记录的日志
        logs = db.query(BindingLog).filter(
            BindingLog.card_record_id == str(record.id)
        ).order_by(BindingLog.timestamp).all()
        
        print(f"📋 日志记录分析:")
        print(f"   总日志数: {len(logs)}")
        
        logs_with_duration = sum(1 for log in logs if log.duration_ms is not None)
        logs_without_duration = len(logs) - logs_with_duration
        
        print(f"   有duration_ms: {logs_with_duration}")
        print(f"   无duration_ms: {logs_without_duration}")
        print()
        
        # 显示原始日志信息
        print("📄 原始日志信息:")
        for i, log in enumerate(logs[:5], 1):  # 只显示前5个
            duration_info = f"{log.duration_ms:.2f}ms" if log.duration_ms else "无"
            print(f"   {i}. {log.timestamp} | {log.log_type} | 耗时: {duration_info}")
            print(f"      消息: {log.message[:50]}...")
        
        if len(logs) > 5:
            print(f"   ... 还有 {len(logs) - 5} 个日志记录")
        
        print()
        
        # 创建时间线服务并获取时间线
        timeline_service = BindingTimelineService(db)
        
        try:
            timeline = timeline_service.get_binding_timeline(str(record.id))
            
            print("✅ 时间线API调用成功")
            print()
            
            print("📊 修复后的时间线数据:")
            print(f"   总处理时间: {timeline.total_duration:.2f}秒")
            print(f"   总处理时间(ms): {timeline.total_duration_ms:.2f}毫秒")
            print(f"   格式化时间: {timeline.total_duration_formatted}")
            print(f"   步骤总数: {len(timeline.steps)}")
            print()
            
            # 分析步骤耗时
            print("⏱️  步骤耗时分析:")
            
            steps_with_duration = 0
            steps_without_duration = 0
            total_steps_duration = 0
            
            for i, step in enumerate(timeline.steps[:10], 1):  # 只显示前10个步骤
                if step.duration_ms is not None:
                    steps_with_duration += 1
                    total_steps_duration += step.duration_ms
                    duration_display = f"{step.duration_ms:.2f}ms"
                    if step.duration_formatted:
                        duration_display += f" ({step.duration_formatted})"
                else:
                    steps_without_duration += 1
                    duration_display = "无"
                
                print(f"   {i:2}. {step.step_name[:30]:30} | {duration_display}")
            
            if len(timeline.steps) > 10:
                print(f"   ... 还有 {len(timeline.steps) - 10} 个步骤")
            
            print()
            print(f"📈 步骤耗时统计:")
            print(f"   有耗时的步骤: {steps_with_duration}/{len(timeline.steps)} ({steps_with_duration/len(timeline.steps)*100:.1f}%)")
            print(f"   无耗时的步骤: {steps_without_duration}/{len(timeline.steps)} ({steps_without_duration/len(timeline.steps)*100:.1f}%)")
            print(f"   步骤总耗时: {total_steps_duration:.2f}ms ({total_steps_duration/1000:.2f}秒)")
            
            # 验证修复效果
            print()
            print("🔍 修复效果验证:")
            
            if timeline.total_duration > 0:
                print(f"   ✅ 总处理时间不为0: {timeline.total_duration:.2f}秒")
            else:
                print(f"   ❌ 总处理时间仍为0: {timeline.total_duration}")
            
            if steps_with_duration > logs_with_duration:
                improvement = steps_with_duration - logs_with_duration
                print(f"   ✅ 步骤耗时计算改进: 新增了 {improvement} 个步骤的耗时数据")
            elif steps_with_duration == logs_with_duration:
                print(f"   ℹ️  步骤耗时数量未变: {steps_with_duration} (可能原始数据就完整)")
            else:
                print(f"   ⚠️  步骤耗时数量减少: 从 {logs_with_duration} 减少到 {steps_with_duration}")
            
            # 与记录的process_time对比
            if record.process_time is not None:
                diff = abs(timeline.total_duration - record.process_time)
                if diff < 1.0:
                    print(f"   ✅ 与记录process_time一致: timeline={timeline.total_duration:.2f}秒, record={record.process_time:.2f}秒")
                else:
                    print(f"   ⚠️  与记录process_time有差异: timeline={timeline.total_duration:.2f}秒, record={record.process_time:.2f}秒, 差异={diff:.2f}秒")
            
        except Exception as e:
            print(f"❌ 获取时间线失败: {str(e)}")
            import traceback
            traceback.print_exc()
            
    finally:
        db.close()


def test_multiple_records():
    """测试多个记录的修复效果"""
    print("\n🔧 测试多个记录的修复效果")
    print("=" * 60)
    
    db = SessionLocal()
    try:
        # 查询最近的几个有日志的记录
        records = db.query(CardRecord).join(BindingLog).filter(
            CardRecord.status.in_(['success', 'failed'])
        ).order_by(CardRecord.created_at.desc()).limit(5).all()
        
        if not records:
            print("❌ 没有找到有日志记录的绑卡记录")
            return
        
        timeline_service = BindingTimelineService(db)
        
        print(f"📝 测试 {len(records)} 个记录:")
        print()
        
        success_count = 0
        zero_duration_count = 0
        improved_count = 0
        
        for i, record in enumerate(records, 1):
            try:
                # 获取原始日志统计
                logs = db.query(BindingLog).filter(
                    BindingLog.card_record_id == str(record.id)
                ).all()
                
                original_duration_count = sum(1 for log in logs if log.duration_ms is not None)
                
                # 获取时间线
                timeline = timeline_service.get_binding_timeline(str(record.id))
                
                # 统计时间线中的耗时数据
                timeline_duration_count = sum(1 for step in timeline.steps if step.duration_ms is not None)
                
                status = "✅" if timeline.total_duration and timeline.total_duration > 0 else "❌"
                duration_display = f"{timeline.total_duration:.2f}秒" if timeline.total_duration else "0或空"
                
                improvement = timeline_duration_count - original_duration_count
                improvement_info = f"(+{improvement})" if improvement > 0 else f"({improvement})" if improvement < 0 else ""
                
                print(f"   {i}. {str(record.id)[:8]}... | {status} | {duration_display} | 耗时步骤: {timeline_duration_count}/{len(timeline.steps)} {improvement_info}")
                
                if timeline.total_duration and timeline.total_duration > 0:
                    success_count += 1
                else:
                    zero_duration_count += 1
                
                if improvement > 0:
                    improved_count += 1
                    
            except Exception as e:
                print(f"   {i}. {str(record.id)[:8]}... | ❌ | 错误: {str(e)} | {record.status}")
        
        print()
        print(f"📊 修复效果统计:")
        print(f"   成功显示时间: {success_count}/{len(records)} ({success_count/len(records)*100:.1f}%)")
        print(f"   时间为0或空: {zero_duration_count}/{len(records)} ({zero_duration_count/len(records)*100:.1f}%)")
        print(f"   步骤耗时改进: {improved_count}/{len(records)} ({improved_count/len(records)*100:.1f}%)")
        
        if zero_duration_count == 0:
            print("   🎉 所有记录的时间线都正确显示了总处理时间！")
        else:
            print(f"   ⚠️  仍有 {zero_duration_count} 个记录的时间线显示为0")
            
    finally:
        db.close()


if __name__ == "__main__":
    print("🚀 开始测试时间线耗时计算修复")
    print()
    
    try:
        # 测试单个记录
        test_timeline_calculation_fix()
        
        # 测试多个记录
        test_multiple_records()
        
        print()
        print("✅ 测试完成！")
        print()
        print("💡 修复说明:")
        print("   - 优先使用原有的duration_ms字段")
        print("   - 如果duration_ms为空，计算与下一个日志的时间差")
        print("   - 总处理时间基于card_record的创建和完成时间")
        print("   - 保持向后兼容性，不影响现有数据")
        
    except Exception as e:
        print(f"❌ 测试过程中出现错误: {str(e)}")
        import traceback
        traceback.print_exc()
