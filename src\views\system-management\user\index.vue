<template>
  <div class="user-management">
    <!-- 用户列表页面 -->
    <div>
      <el-card shadow="never">
        <!-- Filter/Operations Area -->
        <el-form :model="queryParams" :inline="true" label-position="left" label-width="60px" size="small"
          class="filter-form">
          <el-form-item label="用户名">
            <el-input v-model="queryParams.username" placeholder="用户名" clearable style="width: 150px;" />
          </el-form-item>
          <el-form-item label="角色">
            <el-select v-model="queryParams.role" placeholder="角色" clearable style="width: 140px;">
              <el-option v-for="item in roleOptions" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
          <el-form-item label="状态">
            <el-select v-model="queryParams.is_active" placeholder="状态" clearable style="width: 100px;">
              <el-option label="启用" :value="true" />
              <el-option label="禁用" :value="false" />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleQuery" :icon="Search">查询</el-button>
            <el-button @click="resetQuery" :icon="Refresh">重置</el-button>
          </el-form-item>
          <el-form-item style="margin-left: auto;">
            <el-button type="primary" @click="handleAdd" :icon="Plus"
              v-permission="'api:/api/v1/users'">新增用户</el-button>
          </el-form-item>
        </el-form>

        <el-divider />

        <!-- 用户列表 -->
        <el-table :data="userList" v-loading="loading" @selection-change="handleSelectionChange" size="small" stripe>
          <el-table-column type="selection" width="55" align="center" :selectable="checkSelectable" />
          <el-table-column label="用户名" prop="username" min-width="180" show-overflow-tooltip />
          <el-table-column label="姓名" prop="full_name" min-width="120" show-overflow-tooltip />
          <el-table-column label="角色" prop="roles" width="130">
            <template #default="scope">
              {{ getRoleNames(scope.row.roles) }}
            </template>
          </el-table-column>
          <el-table-column label="商户名称" prop="merchant_name" width="120" show-overflow-tooltip>
            <template #default="scope">
              {{ scope.row.merchant_name || '-' }}
            </template>
          </el-table-column>
          <el-table-column label="所属部门" prop="department_name" width="120" show-overflow-tooltip>
            <template #default="scope">
              {{ scope.row.department_name || '-' }}
            </template>
          </el-table-column>
          <el-table-column label="状态" prop="is_active" width="80" align="center">
            <template #default="scope">
              <el-tag :type="scope.row.is_active ? 'success' : 'danger'" size="small">
                {{ scope.row.is_active ? '启用' : '禁用' }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="创建时间" prop="created_at" width="160" sortable>
            <template #default="scope">
              {{ formatDateTime(scope.row.created_at) }}
            </template>
          </el-table-column>
          <el-table-column label="最后登录IP" prop="last_login_ip" width="140" show-overflow-tooltip />
          <el-table-column label="最后登录时间" prop="last_login_time" width="160" sortable>
            <template #default="scope">
              {{ scope.row.last_login_time ? formatDateTime(scope.row.last_login_time) : '-' }}
            </template>
          </el-table-column>
          <el-table-column label="操作" width="200" fixed="right" align="center">
            <template #default="scope">
              <!-- 超级管理员账号不显示任何操作按钮 -->
              <template v-if="!scope.row.is_superuser">
                <!-- 检查用户数据权限 -->
                <template v-if="canAccessUserData(currentUser, scope.row.id)">
                  <!-- 编辑按钮 - 当前用户不能编辑自己 -->
                  <el-tooltip
                    :content="isCurrentUser(scope.row.id) ? '不能编辑自己的用户记录' : '编辑用户'"
                    placement="top"
                  >
                    <el-button
                      type="primary"
                      link
                      size="small"
                      :disabled="isCurrentUser(scope.row.id)"
                      @click="handleEdit(scope.row)"
                      v-permission="'api:/api/v1/users'"
                    >
                      编辑
                    </el-button>
                  </el-tooltip>

                  <!-- 禁用/启用按钮 - 当前用户不能禁用自己 -->
                  <el-tooltip
                    :content="isCurrentUser(scope.row.id) ? '不能禁用自己的账户' : (scope.row.is_active ? '禁用用户' : '启用用户')"
                    placement="top"
                  >
                    <el-button
                      :type="scope.row.is_active ? 'danger' : 'success'"
                      link
                      size="small"
                      :disabled="isCurrentUser(scope.row.id)"
                      @click="handleToggleStatus(scope.row)"
                      v-permission="'api:/api/v1/users'"
                    >
                      {{ scope.row.is_active ? '禁用' : '启用' }}
                    </el-button>
                  </el-tooltip>

                  <!-- 删除按钮 - 当前用户不能删除自己 -->
                  <el-tooltip
                    :content="isCurrentUser(scope.row.id) ? '不能删除自己的用户记录' : '删除用户'"
                    placement="top"
                  >
                    <el-button
                      type="danger"
                      link
                      size="small"
                      :disabled="isCurrentUser(scope.row.id)"
                      @click="handleDelete(scope.row)"
                      v-permission="'api:/api/v1/users'"
                    >
                      删除
                    </el-button>
                  </el-tooltip>
                </template>
                <template v-else>
                  <el-tag type="info" size="small">无权限</el-tag>
                </template>
              </template>
              <template v-else>
                <el-tag type="info" size="small">系统内置</el-tag>
              </template>
            </template>
          </el-table-column>
        </el-table>

        <!-- 分页 -->
        <div class="pagination-container">
          <el-pagination v-model:current-page="queryParams.page" v-model:page-size="queryParams.page_size"
            :page-sizes="[10, 20, 50, 100]" :total="total" layout="total, sizes, prev, pager, next, jumper" background
            @update:current-page="(val) => { queryParams.page = val; fetchWithMerchantFilter(); }"
            @update:page-size="(val) => { queryParams.page_size = val; fetchWithMerchantFilter(); }" />
        </div>
      </el-card>
    </div>

    <!-- 用户权限配置弹窗 -->
    <el-dialog v-model="permissionDialogVisible" :title="`配置用户权限 - ${selectedUser?.username || ''}`" width="800px"
      :close-on-click-modal="false">
      <div v-loading="permissionLoading">
        <div class="permission-config">
          <div class="config-section">
            <h4>角色分配</h4>
            <div class="role-selection">
              <el-checkbox-group v-model="selectedRoles">
                <el-checkbox v-for="role in filteredRoles" :key="role.id" :value="role.id">
                  {{ role.name }}
                  <span class="role-description">{{ role.description }}</span>
                </el-checkbox>
              </el-checkbox-group>
            </div>
          </div>
        </div>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="permissionDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleSaveUserPermissions" :loading="saving">保存</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, onMounted, computed, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useRouter, useRoute } from 'vue-router'
import { useUserStore } from '@/store/modules/user'
import { userApi, merchantApi } from '@/api'
import { roleApi } from '@/api/modules/role'
import { Search, Plus, Refresh } from '@element-plus/icons-vue' // Import icons
import { formatDateTime } from '@/utils/dateUtils'
import useMerchantSwitch from '@/composables/useMerchantSwitch'
import { usePermission } from '@/composables/usePermission'
import { canAccessUserData } from '@/utils/permission'

const router = useRouter()
const route = useRoute()
const userStore = useUserStore()
const currentUser = userStore.userInfo
const { isSuperAdmin, canAccessMerchantData, canAccessUserData: canAccessUserDataComposable } = usePermission()

// 角色选项（过滤掉超级管理员）
const roleOptions = [
  { label: '平台管理员', value: 'platform_admin' },
  { label: '商户管理员', value: 'merchant_admin' },
  { label: 'CK供应商', value: 'ck_supplier' },
]

// 查询参数
const queryParams = ref({
  page: 1,
  page_size: 10,
  username: '',
  role: '',
  merchant_id: '',
  is_active: ''
})

const loading = ref(false)
const userList = ref([])
const total = ref(0)
const merchantList = ref([])
const selectedUsers = ref([])

// 权限配置相关
const permissionDialogVisible = ref(false)
const selectedUser = ref(null)
const allRoles = ref([])
const selectedRoles = ref([])
const permissionLoading = ref(false)
const saving = ref(false)

// 是否可以选择商户
const canSelectMerchant = computed(() => {
  return isSuperAdmin.value
})

// 过滤后的角色列表（排除超级管理员）
const filteredRoles = computed(() => {
  return allRoles.value.filter(role => role.code !== 'super_admin')
})

// 判断是否为当前用户（用户自我保护逻辑）
const isCurrentUser = (userId) => {
  return currentUser?.id === userId
}

// 获取角色名称（支持新的角色数组格式）
const getRoleNames = (roles) => {
  if (!roles || !Array.isArray(roles)) {
    return '-'
  }

  return roles.map(role => {
    const option = roleOptions.find(item => item.value === role.code)
    return option ? option.label : role.name || role.code
  }).join(', ')
}

// 获取商户列表
const fetchMerchants = async () => {
  try {
    const response = await merchantApi.getList()
    merchantList.value = response.items || response
    total.value = response.total || 0;
  } catch (error) {
    console.error('获取商户列表失败:', error)
    ElMessage.error('获取商户列表失败')
  }
}

// 查询用户列表
const fetchUserList = async (params = {}, resetPage = false) => {
  if (resetPage) {
    queryParams.value.page = 1 // 页码大小变化时，重置到第一页
  }
  loading.value = true
  try {
    const requestParams = {
      page: queryParams.value.page,
      page_size: queryParams.value.page_size,
      username: queryParams.value.username || undefined,
      role: queryParams.value.role || undefined,
      merchant_id: queryParams.value.merchant_id === '' ? undefined : queryParams.value.merchant_id,
      is_active: queryParams.value.is_active === '' ? undefined : queryParams.value.is_active,
      ...params
    }

    const response = await userApi.getList(requestParams)
    userList.value = response.items || []
    total.value = response.total || 0
  } catch (error) {
    console.error('获取用户列表失败:', error)
    ElMessage.error('获取用户列表失败')
  } finally {
    loading.value = false
  }
}

// 使用商家切换监听器
const { fetchWithMerchantFilter } = useMerchantSwitch(
  fetchUserList,
  queryParams,
  'merchant_id'
)

// 查询
const handleQuery = () => {
  queryParams.value.page = 1
  fetchWithMerchantFilter()
}

// 重置查询
const resetQuery = () => {
  queryParams.value = {
    page: 1,
    page_size: 10,
    username: '',
    role: '',
    merchant_id: '',
    is_active: ''
  }
  fetchWithMerchantFilter()
}

// 新增用户
const handleAdd = () => {
  router.push('/system/user/add')
}

// 编辑用户
const handleEdit = (row) => {
  // 检查是否为超级管理员
  if (row.is_superuser) {
    ElMessage.warning('超级管理员账号不允许编辑')
    return
  }

  // 用户自我保护：不能编辑自己
  if (isCurrentUser(row.id)) {
    ElMessage.warning('不能编辑自己的用户记录')
    return
  }

  // 检查用户数据权限
  if (!canAccessUserData(currentUser, row.id)) {
    ElMessage.warning('您没有权限编辑该用户')
    return
  }

  router.push(`/system/user/edit/${row.id}`)
}

// 切换用户状态
const handleToggleStatus = async (row) => {
  // 检查是否为超级管理员
  if (row.is_superuser) {
    ElMessage.warning('超级管理员账号不允许禁用')
    return
  }

  // 用户自我保护：不能禁用自己
  if (isCurrentUser(row.id)) {
    ElMessage.warning('不能禁用自己的账户')
    return
  }

  try {
    await ElMessageBox.confirm(
      `确定要${row.is_active ? '禁用' : '启用'}用户 ${row.username} 吗？`,
      '提示',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )

    await userApi.activateUser(row.id, !row.is_active)
    ElMessage.success(`${row.is_active ? '禁用' : '启用'}成功`)
    fetchWithMerchantFilter()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('操作失败:', error)
      ElMessage.error('操作失败')
    }
  }
}

// 删除用户
const handleDelete = async (row) => {
  // 检查是否为超级管理员
  if (row.is_superuser) {
    ElMessage.warning('超级管理员账号不允许删除')
    return
  }

  // 用户自我保护：不能删除自己
  if (isCurrentUser(row.id)) {
    ElMessage.warning('不能删除自己的用户记录')
    return
  }

  try {
    await ElMessageBox.confirm(
      `确定要删除用户 ${row.username} 吗？此操作不可恢复！`,
      '警告',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )

    await userApi.delete(row.id)
    ElMessage.success('删除成功')
    fetchWithMerchantFilter()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除失败:', error)
      ElMessage.error('删除失败')
    }
  }
}

// 批量删除
const handleBatchDelete = async () => {
  if (!selectedUsers.value.length) {
    ElMessage.warning('请选择要删除的用户')
    return
  }

  // 检查是否包含超级管理员
  const hasSuperuser = selectedUsers.value.some(user => user.is_superuser)
  if (hasSuperuser) {
    ElMessage.warning('选中的用户中包含超级管理员，无法删除')
    return
  }

  // 用户自我保护：检查是否包含当前用户
  const hasCurrentUser = selectedUsers.value.some(user => isCurrentUser(user.id))
  if (hasCurrentUser) {
    ElMessage.warning('选中的用户中包含您自己，无法删除')
    return
  }

  try {
    await ElMessageBox.confirm(
      `确定要删除选中的 ${selectedUsers.value.length} 个用户吗？此操作不可恢复！`,
      '警告',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )

    // TODO: 实现批量删除API
    const promises = selectedUsers.value.map(user => userApi.delete(user.id))
    await Promise.all(promises)

    ElMessage.success('批量删除成功')
    fetchWithMerchantFilter()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('批量删除失败:', error)
      ElMessage.error('批量删除失败')
    }
  }
}

// 检查行是否可选择（超级管理员和当前用户不可选择）
const checkSelectable = (row) => {
  return !row.is_superuser && !isCurrentUser(row.id)
}

// 表格选择变更
const handleSelectionChange = (selection) => {
  selectedUsers.value = selection
}

// 权限管理
const handlePermission = (row) => {
  selectedUser.value = row
  permissionDialogVisible.value = true
  loadUserRoles(row.id)
}

// 获取所有角色列表
const fetchAllRoles = async () => {
  try {
    const response = await roleApi.getList()
    allRoles.value = Array.isArray(response) ? response : (response.items || [])
  } catch (error) {
    console.error('获取角色列表失败:', error)
    ElMessage.error('获取角色列表失败')
  }
}

// 加载用户角色
const loadUserRoles = async (userId) => {
  try {
    permissionLoading.value = true
    // 使用用户API获取用户角色
    const response = await userApi.getUserRoles(userId)
    console.log('获取用户角色响应:', response)

    // 处理返回的角色数据，提取角色ID
    let roleIds = []
    if (response?.roles && Array.isArray(response.roles)) {
      roleIds = response.roles.map(role => role.id)
    } else if (Array.isArray(response)) {
      roleIds = response.map(role => role.id)
    }

    selectedRoles.value = roleIds
    console.log('设置的选中角色ID:', selectedRoles.value)
  } catch (error) {
    console.error('获取用户角色失败:', error)
    ElMessage.error('获取用户角色失败')
    selectedRoles.value = []
  } finally {
    permissionLoading.value = false
  }
}

// 保存用户权限
const handleSaveUserPermissions = async () => {
  if (!selectedUser.value) return

  try {
    saving.value = true
    // 调用用户API更新角色
    await userApi.updateUserRoles(selectedUser.value.id, selectedRoles.value)
    ElMessage.success('用户权限配置保存成功')
    permissionDialogVisible.value = false
    // 刷新用户列表
    fetchWithMerchantFilter()
  } catch (error) {
    console.error('保存用户权限配置失败:', error)
    ElMessage.error('保存用户权限配置失败')
  } finally {
    saving.value = false
  }
}

// 监听路由查询参数变化以刷新列表
watch(
  () => route.query.refresh,
  (newValue, oldValue) => {
    // 仅当 refresh 参数实际发生变化时才刷新
    if (newValue && newValue !== oldValue) {
      fetchWithMerchantFilter();
    }
  }
);

onMounted(async () => {
  if (canSelectMerchant.value) {
    await fetchMerchants()
  }
  await fetchAllRoles()
  fetchWithMerchantFilter()
})
</script>

<style scoped>
.user-management {}

.filter-form {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
}

.filter-form .el-form-item {
  margin-bottom: 0;
  margin-right: 10px;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}

/* 权限配置对话框样式 */
.permission-config {
  padding: 10px 0;
}

.config-section {
  margin-bottom: 20px;
}

.config-section h4 {
  margin: 0 0 15px 0;
  color: var(--el-text-color-primary);
  font-weight: 600;
}

.role-selection {
  max-height: 300px;
  overflow-y: auto;
  border: 1px solid var(--el-border-color-light);
  border-radius: 4px;
  padding: 15px;
  background-color: var(--el-bg-color-page);
}

.role-selection .el-checkbox {
  display: block;
  margin-bottom: 12px;
  margin-right: 0;
}

.role-description {
  color: var(--el-text-color-secondary);
  font-size: 12px;
  margin-left: 8px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

/* 用户自我保护 - 禁用按钮样式 */
.el-button.is-disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.el-button.is-disabled:hover {
  opacity: 0.5;
}

/* Remove old card header styles */
/*
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
*/
</style>
