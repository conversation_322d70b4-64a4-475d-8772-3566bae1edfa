"""
批量数据操作服务
提供批量导入、批量编辑、数据验证和错误处理等功能
"""

import csv
import json
import pandas as pd
from typing import Dict, Any, List, Optional, Tuple, Union
from datetime import datetime
from io import StringIO, BytesIO
from sqlalchemy.orm import Session
from sqlalchemy.exc import IntegrityError
from pydantic import BaseModel, ValidationError

from app.models.user import User
from app.models.merchant import Merchant
from app.models.role import Role
from app.core.logging import get_logger

logger = get_logger("batch_operations")


class BatchOperationResult(BaseModel):
    """批量操作结果"""
    total_records: int
    successful_records: int
    failed_records: int
    errors: List[Dict[str, Any]]
    warnings: List[Dict[str, Any]]
    operation_id: str
    started_at: datetime
    completed_at: Optional[datetime] = None


class BatchValidationError(BaseModel):
    """批量验证错误"""
    row_number: int
    field: str
    value: Any
    error_message: str
    error_type: str


class ValidationResult(BaseModel):
    """验证结果"""
    errors: List[BatchValidationError]
    warnings: List[BatchValidationError]


class BatchOperationsService:
    """批量数据操作服务"""

    def __init__(self, db: Session):
        self.db = db
        self.supported_formats = ['csv', 'xlsx', 'json']
        self.max_batch_size = 10000  # 最大批量处理数量

    async def import_users_batch(
        self,
        file_content: Union[str, bytes],
        file_format: str,
        validation_rules: Optional[Dict[str, Any]] = None,
        dry_run: bool = False
    ) -> BatchOperationResult:
        """批量导入用户"""
        
        operation_id = f"import_users_{datetime.utcnow().strftime('%Y%m%d_%H%M%S')}"
        started_at = datetime.utcnow()
        
        try:
            # 解析文件数据
            data_records = await self._parse_file_data(file_content, file_format)
            
            if len(data_records) > self.max_batch_size:
                raise ValueError(f"批量导入数量超过限制 ({self.max_batch_size})")
            
            # 数据验证
            validation_result = await self._validate_user_data(data_records, validation_rules)
            
            if dry_run:
                return BatchOperationResult(
                    total_records=len(data_records),
                    successful_records=len(data_records) - len(validation_result.errors),
                    failed_records=len(validation_result.errors),
                    errors=[error.dict() for error in validation_result.errors],
                    warnings=[warning.dict() for warning in validation_result.warnings],
                    operation_id=operation_id,
                    started_at=started_at,
                    completed_at=datetime.utcnow()
                )
            
            # 执行批量导入
            import_result = await self._execute_user_import(data_records, validation_result)
            
            return BatchOperationResult(
                total_records=len(data_records),
                successful_records=import_result['successful'],
                failed_records=import_result['failed'],
                errors=import_result['errors'],
                warnings=[warning.dict() for warning in validation_result.warnings],
                operation_id=operation_id,
                started_at=started_at,
                completed_at=datetime.utcnow()
            )
            
        except Exception as e:
            logger.error(f"批量导入用户失败: {e}")
            return BatchOperationResult(
                total_records=0,
                successful_records=0,
                failed_records=0,
                errors=[{"error": str(e), "type": "system_error"}],
                warnings=[],
                operation_id=operation_id,
                started_at=started_at,
                completed_at=datetime.utcnow()
            )

    async def batch_update_users(
        self,
        update_data: List[Dict[str, Any]],
        update_fields: List[str],
        dry_run: bool = False
    ) -> BatchOperationResult:
        """批量更新用户"""
        
        operation_id = f"update_users_{datetime.utcnow().strftime('%Y%m%d_%H%M%S')}"
        started_at = datetime.utcnow()
        
        try:
            # 验证更新数据
            validation_result = await self._validate_update_data(update_data, update_fields, User)
            
            if dry_run:
                return BatchOperationResult(
                    total_records=len(update_data),
                    successful_records=len(update_data) - len(validation_result.errors),
                    failed_records=len(validation_result.errors),
                    errors=[error.dict() for error in validation_result.errors],
                    warnings=[warning.dict() for warning in validation_result.warnings],
                    operation_id=operation_id,
                    started_at=started_at,
                    completed_at=datetime.utcnow()
                )
            
            # 执行批量更新
            update_result = await self._execute_batch_update(update_data, update_fields, User)
            
            return BatchOperationResult(
                total_records=len(update_data),
                successful_records=update_result['successful'],
                failed_records=update_result['failed'],
                errors=update_result['errors'],
                warnings=[warning.dict() for warning in validation_result.warnings],
                operation_id=operation_id,
                started_at=started_at,
                completed_at=datetime.utcnow()
            )
            
        except Exception as e:
            logger.error(f"批量更新用户失败: {e}")
            return BatchOperationResult(
                total_records=len(update_data),
                successful_records=0,
                failed_records=len(update_data),
                errors=[{"error": str(e), "type": "system_error"}],
                warnings=[],
                operation_id=operation_id,
                started_at=started_at,
                completed_at=datetime.utcnow()
            )

    async def batch_delete_records(
        self,
        model_class,
        record_ids: List[int],
        soft_delete: bool = True,
        dry_run: bool = False
    ) -> BatchOperationResult:
        """批量删除记录"""
        
        operation_id = f"delete_{model_class.__name__.lower()}_{datetime.utcnow().strftime('%Y%m%d_%H%M%S')}"
        started_at = datetime.utcnow()
        
        try:
            # 验证记录存在性
            existing_records = self.db.query(model_class).filter(model_class.id.in_(record_ids)).all()
            existing_ids = {record.id for record in existing_records}
            missing_ids = set(record_ids) - existing_ids
            
            errors = []
            if missing_ids:
                for missing_id in missing_ids:
                    errors.append({
                        "record_id": missing_id,
                        "error": "记录不存在",
                        "type": "not_found"
                    })
            
            if dry_run:
                return BatchOperationResult(
                    total_records=len(record_ids),
                    successful_records=len(existing_ids),
                    failed_records=len(missing_ids),
                    errors=errors,
                    warnings=[],
                    operation_id=operation_id,
                    started_at=started_at,
                    completed_at=datetime.utcnow()
                )
            
            # 执行批量删除
            delete_result = await self._execute_batch_delete(existing_records, soft_delete)
            
            return BatchOperationResult(
                total_records=len(record_ids),
                successful_records=delete_result['successful'],
                failed_records=delete_result['failed'] + len(missing_ids),
                errors=errors + delete_result['errors'],
                warnings=[],
                operation_id=operation_id,
                started_at=started_at,
                completed_at=datetime.utcnow()
            )
            
        except Exception as e:
            logger.error(f"批量删除失败: {e}")
            return BatchOperationResult(
                total_records=len(record_ids),
                successful_records=0,
                failed_records=len(record_ids),
                errors=[{"error": str(e), "type": "system_error"}],
                warnings=[],
                operation_id=operation_id,
                started_at=started_at,
                completed_at=datetime.utcnow()
            )

    async def export_data_batch(
        self,
        model_class,
        filters: Optional[Dict[str, Any]] = None,
        export_format: str = 'csv',
        fields: Optional[List[str]] = None
    ) -> Tuple[Union[str, bytes], str]:
        """批量导出数据"""
        
        try:
            # 构建查询
            query = self.db.query(model_class)
            
            # 应用过滤条件
            if filters:
                for field, value in filters.items():
                    if hasattr(model_class, field):
                        if isinstance(value, list):
                            query = query.filter(getattr(model_class, field).in_(value))
                        else:
                            query = query.filter(getattr(model_class, field) == value)
            
            records = query.all()
            
            # 转换为字典格式
            data = []
            for record in records:
                record_dict = {}
                for column in record.__table__.columns:
                    if not fields or column.name in fields:
                        value = getattr(record, column.name)
                        # 处理特殊类型
                        if isinstance(value, datetime):
                            value = value.isoformat()
                        record_dict[column.name] = value
                data.append(record_dict)
            
            # 根据格式导出
            if export_format == 'csv':
                return self._export_to_csv(data), 'text/csv'
            elif export_format == 'xlsx':
                return self._export_to_xlsx(data), 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
            elif export_format == 'json':
                return json.dumps(data, ensure_ascii=False, indent=2), 'application/json'
            else:
                raise ValueError(f"不支持的导出格式: {export_format}")
                
        except Exception as e:
            logger.error(f"批量导出失败: {e}")
            raise

    # 私有辅助方法
    async def _parse_file_data(self, file_content: Union[str, bytes], file_format: str) -> List[Dict[str, Any]]:
        """解析文件数据"""
        try:
            if file_format == 'csv':
                if isinstance(file_content, bytes):
                    file_content = file_content.decode('utf-8')

                csv_reader = csv.DictReader(StringIO(file_content))
                return list(csv_reader)

            elif file_format == 'xlsx':
                if isinstance(file_content, str):
                    file_content = file_content.encode('utf-8')

                df = pd.read_excel(BytesIO(file_content))
                return df.to_dict('records')

            elif file_format == 'json':
                if isinstance(file_content, bytes):
                    file_content = file_content.decode('utf-8')

                return json.loads(file_content)

            else:
                raise ValueError(f"不支持的文件格式: {file_format}")

        except Exception as e:
            logger.error(f"解析文件数据失败: {e}")
            raise ValueError(f"文件解析失败: {str(e)}")

    async def _validate_user_data(
        self,
        data_records: List[Dict[str, Any]],
        validation_rules: Optional[Dict[str, Any]] = None
    ) -> 'ValidationResult':
        """验证用户数据"""
        errors = []
        warnings = []

        required_fields = ['username', 'email']
        optional_fields = ['full_name', 'phone', 'department', 'is_active']

        for row_num, record in enumerate(data_records, 1):
            # 检查必填字段
            for field in required_fields:
                if not record.get(field) or str(record[field]).strip() == '':
                    errors.append(BatchValidationError(
                        row_number=row_num,
                        field=field,
                        value=record.get(field),
                        error_message=f"字段 {field} 不能为空",
                        error_type="required_field"
                    ))

            # 验证邮箱格式
            email = record.get('email')
            if email and '@' not in email:
                errors.append(BatchValidationError(
                    row_number=row_num,
                    field='email',
                    value=email,
                    error_message="邮箱格式不正确",
                    error_type="format_error"
                ))

            # 检查用户名唯一性
            username = record.get('username')
            if username:
                existing_user = self.db.query(User).filter(User.username == username).first()
                if existing_user:
                    errors.append(BatchValidationError(
                        row_number=row_num,
                        field='username',
                        value=username,
                        error_message=f"用户名 {username} 已存在",
                        error_type="duplicate_value"
                    ))

            # 检查邮箱唯一性
            if email:
                existing_email = self.db.query(User).filter(User.email == email).first()
                if existing_email:
                    errors.append(BatchValidationError(
                        row_number=row_num,
                        field='email',
                        value=email,
                        error_message=f"邮箱 {email} 已存在",
                        error_type="duplicate_value"
                    ))

            # 验证手机号格式
            phone = record.get('phone')
            if phone and not str(phone).isdigit():
                warnings.append(BatchValidationError(
                    row_number=row_num,
                    field='phone',
                    value=phone,
                    error_message="手机号格式可能不正确",
                    error_type="format_warning"
                ))

        return ValidationResult(errors=errors, warnings=warnings)

    async def _execute_user_import(
        self,
        data_records: List[Dict[str, Any]],
        validation_result: 'ValidationResult'
    ) -> Dict[str, Any]:
        """执行用户导入"""
        successful = 0
        failed = 0
        errors = []

        # 过滤掉有错误的记录
        error_rows = {error.row_number for error in validation_result.errors}
        valid_records = [
            record for i, record in enumerate(data_records, 1)
            if i not in error_rows
        ]

        for i, record in enumerate(valid_records, 1):
            try:
                # 创建用户对象
                user = User(
                    username=record['username'],
                    email=record['email'],
                    full_name=record.get('full_name', ''),
                    phone=record.get('phone', ''),
                    department=record.get('department', ''),
                    is_active=record.get('is_active', True),
                    password_hash='default_hash'  # 需要设置默认密码或要求用户重置
                )

                self.db.add(user)
                self.db.flush()  # 获取ID但不提交
                successful += 1

            except Exception as e:
                failed += 1
                errors.append({
                    "row_number": i,
                    "record": record,
                    "error": str(e),
                    "type": "import_error"
                })

        try:
            self.db.commit()
        except Exception as e:
            self.db.rollback()
            # 如果提交失败，所有记录都失败
            failed = len(valid_records)
            successful = 0
            errors = [{"error": f"数据库提交失败: {str(e)}", "type": "commit_error"}]

        return {
            "successful": successful,
            "failed": failed,
            "errors": errors
        }

    async def _validate_update_data(
        self,
        update_data: List[Dict[str, Any]],
        update_fields: List[str],
        model_class
    ) -> 'ValidationResult':
        """验证更新数据"""
        errors = []
        warnings = []

        for row_num, record in enumerate(update_data, 1):
            # 检查ID字段
            if 'id' not in record:
                errors.append(BatchValidationError(
                    row_number=row_num,
                    field='id',
                    value=None,
                    error_message="缺少ID字段",
                    error_type="required_field"
                ))
                continue

            # 检查记录是否存在
            record_id = record['id']
            existing_record = self.db.query(model_class).filter(model_class.id == record_id).first()
            if not existing_record:
                errors.append(BatchValidationError(
                    row_number=row_num,
                    field='id',
                    value=record_id,
                    error_message=f"ID {record_id} 对应的记录不存在",
                    error_type="not_found"
                ))
                continue

            # 验证更新字段
            for field in update_fields:
                if field in record and hasattr(model_class, field):
                    value = record[field]
                    # 这里可以添加更多字段特定的验证逻辑
                    if field == 'email' and value and '@' not in str(value):
                        errors.append(BatchValidationError(
                            row_number=row_num,
                            field=field,
                            value=value,
                            error_message="邮箱格式不正确",
                            error_type="format_error"
                        ))

        return ValidationResult(errors=errors, warnings=warnings)

    async def _execute_batch_update(
        self,
        update_data: List[Dict[str, Any]],
        update_fields: List[str],
        model_class
    ) -> Dict[str, Any]:
        """执行批量更新"""
        successful = 0
        failed = 0
        errors = []

        for i, record in enumerate(update_data, 1):
            try:
                record_id = record['id']
                existing_record = self.db.query(model_class).filter(model_class.id == record_id).first()

                if existing_record:
                    # 更新指定字段
                    for field in update_fields:
                        if field in record and hasattr(existing_record, field):
                            setattr(existing_record, field, record[field])

                    successful += 1
                else:
                    failed += 1
                    errors.append({
                        "row_number": i,
                        "record_id": record_id,
                        "error": "记录不存在",
                        "type": "not_found"
                    })

            except Exception as e:
                failed += 1
                errors.append({
                    "row_number": i,
                    "record": record,
                    "error": str(e),
                    "type": "update_error"
                })

        try:
            self.db.commit()
        except Exception as e:
            self.db.rollback()
            failed = len(update_data)
            successful = 0
            errors = [{"error": f"数据库提交失败: {str(e)}", "type": "commit_error"}]

        return {
            "successful": successful,
            "failed": failed,
            "errors": errors
        }

    async def _execute_batch_delete(
        self,
        records: List,
        soft_delete: bool = True
    ) -> Dict[str, Any]:
        """执行批量删除"""
        successful = 0
        failed = 0
        errors = []

        for record in records:
            try:
                if soft_delete and hasattr(record, 'is_active'):
                    # 软删除：设置为非活跃状态
                    record.is_active = False
                else:
                    # 硬删除：从数据库中删除
                    self.db.delete(record)

                successful += 1

            except Exception as e:
                failed += 1
                errors.append({
                    "record_id": record.id,
                    "error": str(e),
                    "type": "delete_error"
                })

        try:
            self.db.commit()
        except Exception as e:
            self.db.rollback()
            failed = len(records)
            successful = 0
            errors = [{"error": f"数据库提交失败: {str(e)}", "type": "commit_error"}]

        return {
            "successful": successful,
            "failed": failed,
            "errors": errors
        }

    def _export_to_csv(self, data: List[Dict[str, Any]]) -> str:
        """导出为CSV格式"""
        if not data:
            return ""

        output = StringIO()
        fieldnames = data[0].keys()
        writer = csv.DictWriter(output, fieldnames=fieldnames)

        writer.writeheader()
        for row in data:
            writer.writerow(row)

        return output.getvalue()

    def _export_to_xlsx(self, data: List[Dict[str, Any]]) -> bytes:
        """导出为Excel格式"""
        if not data:
            return b""

        df = pd.DataFrame(data)
        output = BytesIO()

        with pd.ExcelWriter(output, engine='openpyxl') as writer:
            df.to_excel(writer, index=False, sheet_name='Data')

        return output.getvalue()

    async def get_batch_operation_template(self, operation_type: str, model_name: str) -> Dict[str, Any]:
        """获取批量操作模板"""
        templates = {
            "user_import": {
                "required_fields": ["username", "email"],
                "optional_fields": ["full_name", "phone", "department", "is_active"],
                "field_descriptions": {
                    "username": "用户名（必填，唯一）",
                    "email": "邮箱地址（必填，唯一）",
                    "full_name": "真实姓名（可选）",
                    "phone": "手机号码（可选）",
                    "department": "所属部门（可选）",
                    "is_active": "是否激活（可选，true/false）"
                },
                "sample_data": [
                    {
                        "username": "john_doe",
                        "email": "<EMAIL>",
                        "full_name": "John Doe",
                        "phone": "13800138000",
                        "department": "IT部门",
                        "is_active": True
                    },
                    {
                        "username": "jane_smith",
                        "email": "<EMAIL>",
                        "full_name": "Jane Smith",
                        "phone": "13900139000",
                        "department": "财务部门",
                        "is_active": True
                    }
                ]
            },
            "user_update": {
                "required_fields": ["id"],
                "optional_fields": ["full_name", "phone", "department", "is_active"],
                "field_descriptions": {
                    "id": "用户ID（必填）",
                    "full_name": "真实姓名（可选）",
                    "phone": "手机号码（可选）",
                    "department": "所属部门（可选）",
                    "is_active": "是否激活（可选，true/false）"
                },
                "sample_data": [
                    {
                        "id": 1,
                        "full_name": "Updated Name",
                        "department": "新部门",
                        "is_active": True
                    }
                ]
            }
        }

        template_key = f"{model_name}_{operation_type}"
        return templates.get(template_key, {
            "error": f"不支持的操作类型: {template_key}"
        })

    async def validate_batch_operation(
        self,
        operation_type: str,
        data: List[Dict[str, Any]],
        options: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """验证批量操作"""

        validation_result = {
            "is_valid": True,
            "total_records": len(data),
            "validation_errors": [],
            "validation_warnings": [],
            "estimated_time": self._estimate_operation_time(len(data), operation_type)
        }

        # 检查数据量限制
        if len(data) > self.max_batch_size:
            validation_result["is_valid"] = False
            validation_result["validation_errors"].append({
                "type": "size_limit",
                "message": f"数据量超过限制，最大支持 {self.max_batch_size} 条记录"
            })

        # 检查数据格式
        if not data:
            validation_result["is_valid"] = False
            validation_result["validation_errors"].append({
                "type": "empty_data",
                "message": "没有提供数据"
            })

        return validation_result

    def _estimate_operation_time(self, record_count: int, operation_type: str) -> str:
        """估算操作时间"""
        # 简单的时间估算逻辑
        base_time_per_record = {
            "import": 0.1,  # 每条记录0.1秒
            "update": 0.05,  # 每条记录0.05秒
            "delete": 0.02   # 每条记录0.02秒
        }

        time_per_record = base_time_per_record.get(operation_type, 0.1)
        estimated_seconds = record_count * time_per_record

        if estimated_seconds < 60:
            return f"{int(estimated_seconds)} 秒"
        elif estimated_seconds < 3600:
            return f"{int(estimated_seconds / 60)} 分钟"
        else:
            return f"{int(estimated_seconds / 3600)} 小时"
