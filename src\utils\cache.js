/**
 * 缓存工具类
 * 用于管理系统参数等数据的本地缓存
 */

// 缓存键名
const CACHE_KEYS = {
    PUBLIC_PARAMS: 'walmart_public_params',
    SYSTEM_PARAMS: 'walmart_system_params',
    USER_SETTINGS: 'walmart_ck_settings'
}

// 默认缓存时间（毫秒）
const DEFAULT_EXPIRES = {
    PUBLIC_PARAMS: 30 * 60 * 1000, // 30分钟
    SYSTEM_PARAMS: 60 * 60 * 1000, // 1小时
}

/**
 * 带过期时间的缓存项
 */
class CacheItem {
    constructor(data, expires = 0) {
        this.data = data
        this.timestamp = Date.now()
        this.expires = expires
    }

    /**
     * 检查缓存项是否已过期
     * @returns {boolean} 是否过期
     */
    isExpired() {
        if (this.expires === 0) return false
        return Date.now() - this.timestamp > this.expires
    }
}

/**
 * 缓存管理器
 */
export const cacheManager = {
    // 获取公开参数缓存
    getPublicParams() {
        try {
            const cached = localStorage.getItem(CACHE_KEYS.PUBLIC_PARAMS)
            return cached ? JSON.parse(cached) : null
        } catch (error) {
            console.error('解析公开参数缓存失败:', error)
            return null
        }
    },

    // 设置公开参数缓存
    setPublicParams(params) {
        try {
            localStorage.setItem(CACHE_KEYS.PUBLIC_PARAMS, JSON.stringify(params))
        } catch (error) {
            console.error('设置公开参数缓存失败:', error)
        }
    },

    // 获取系统参数缓存
    getSystemParams() {
        try {
            const cached = localStorage.getItem(CACHE_KEYS.SYSTEM_PARAMS)
            return cached ? JSON.parse(cached) : null
        } catch (error) {
            console.error('解析系统参数缓存失败:', error)
            return null
        }
    },

    // 设置系统参数缓存
    setSystemParams(params) {
        try {
            localStorage.setItem(CACHE_KEYS.SYSTEM_PARAMS, JSON.stringify(params))
        } catch (error) {
            console.error('设置系统参数缓存失败:', error)
        }
    },

    // 获取用户设置缓存
    getUserSettings() {
        try {
            const cached = localStorage.getItem(CACHE_KEYS.USER_SETTINGS)
            return cached ? JSON.parse(cached) : null
        } catch (error) {
            console.error('解析用户设置缓存失败:', error)
            return null
        }
    },

    // 设置用户设置缓存
    setUserSettings(settings) {
        try {
            localStorage.setItem(CACHE_KEYS.USER_SETTINGS, JSON.stringify(settings))
        } catch (error) {
            console.error('设置用户设置缓存失败:', error)
        }
    },

    // 移除指定缓存
    removeCache(key) {
        localStorage.removeItem(key)
    },

    // 清除所有缓存
    clearAll() {
        Object.values(CACHE_KEYS).forEach(key => {
            localStorage.removeItem(key)
        })
    }
} 