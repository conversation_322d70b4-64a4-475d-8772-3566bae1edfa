<template>
  <div class="role-users-page">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-left">
        <el-button @click="goBack" :icon="ArrowLeft" circle></el-button>
        <span class="page-title">角色用户管理 - {{ roleInfo?.name || '' }}</span>
      </div>
      <div class="header-actions">
        <el-button type="primary" :icon="Plus" @click="showAddUserDialog">添加用户</el-button>
        <el-button type="warning" :icon="Download" @click="exportUsers">导出用户</el-button>
      </div>
    </div>

    <!-- 角色信息卡片 -->
    <el-card shadow="never" style="margin-bottom: 20px;">
      <div class="role-info">
        <div class="info-item">
          <span class="label">角色名称：</span>
          <span class="value">{{ roleInfo?.name }}</span>
        </div>
        <div class="info-item">
          <span class="label">角色编码：</span>
          <span class="value">{{ roleInfo?.code }}</span>
        </div>
        <div class="info-item">
          <span class="label">角色描述：</span>
          <span class="value">{{ roleInfo?.description || '无' }}</span>
        </div>
        <div class="info-item">
          <span class="label">用户数量：</span>
          <span class="value">{{ total }}</span>
        </div>
      </div>
    </el-card>

    <!-- 搜索区域 -->
    <el-card shadow="never" style="margin-bottom: 20px;">
      <div class="search-section">
        <el-form :model="searchForm" inline>
          <el-form-item label="用户名">
            <el-input 
              v-model="searchForm.username" 
              placeholder="请输入用户名" 
              clearable 
              style="width: 200px"
            />
          </el-form-item>
          <el-form-item label="邮箱">
            <el-input 
              v-model="searchForm.email" 
              placeholder="请输入邮箱" 
              clearable 
              style="width: 200px"
            />
          </el-form-item>
          <el-form-item label="状态">
            <el-select v-model="searchForm.status" placeholder="状态" clearable style="width: 120px">
              <el-option label="全部" value="" />
              <el-option label="启用" value="1" />
              <el-option label="禁用" value="0" />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleSearch" :icon="Search">搜索</el-button>
            <el-button @click="handleReset">重置</el-button>
          </el-form-item>
        </el-form>
      </div>
    </el-card>

    <!-- 用户列表 -->
    <el-card shadow="never">
      <el-table :data="tableData" style="width: 100%" v-loading="loading">
        <el-table-column type="selection" width="55" />
        <el-table-column prop="username" label="用户名" />
        <el-table-column prop="email" label="邮箱" />
        <el-table-column prop="full_name" label="姓名" />
        <el-table-column prop="phone" label="手机号" />
        <el-table-column prop="department" label="部门" />
        <el-table-column prop="is_active" label="状态">
          <template #default="{ row }">
            <el-tag :type="row.is_active ? 'success' : 'danger'">
              {{ row.is_active ? '启用' : '禁用' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="last_login" label="最后登录" />
        <el-table-column prop="created_at" label="加入时间" />
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <el-button type="primary" size="small" @click="viewUserDetail(row)">详情</el-button>
            <el-button type="warning" size="small" @click="editUserRoles(row)">编辑角色</el-button>
            <el-button type="danger" size="small" @click="removeUserFromRole(row)">移除</el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination">
        <div class="pagination-info">
          <span>共 {{ total }} 条</span>
          <el-select v-model="pageSize" @change="handlePageSizeChange" style="width: 120px; margin-left: 10px;">
            <el-option label="10条/页" :value="10" />
            <el-option label="20条/页" :value="20" />
            <el-option label="50条/页" :value="50" />
          </el-select>
        </div>
        <el-pagination 
          v-model:current-page="currentPage" 
          v-model:page-size="pageSize" 
          :total="total"
          layout="prev, pager, next, jumper" 
          @current-change="handlePageChange" 
          @size-change="handlePageSizeChange" 
        />
      </div>
    </el-card>

    <!-- 添加用户对话框 -->
    <el-dialog v-model="addUserDialogVisible" title="添加用户到角色" width="600px">
      <div class="add-user-section">
        <el-form :model="addUserForm" label-width="80px">
          <el-form-item label="搜索用户">
            <el-input 
              v-model="userSearchKeyword" 
              placeholder="输入用户名或邮箱搜索" 
              @input="searchAvailableUsers"
              clearable
            />
          </el-form-item>
        </el-form>
        
        <div class="available-users" v-loading="searchingUsers">
          <div class="user-list">
            <div 
              v-for="user in availableUsers" 
              :key="user.id" 
              class="user-item"
              :class="{ selected: selectedUsers.includes(user.id) }"
              @click="toggleUserSelection(user.id)"
            >
              <div class="user-info">
                <div class="user-name">{{ user.username }}</div>
                <div class="user-email">{{ user.email }}</div>
              </div>
              <div class="user-status">
                <el-tag :type="user.is_active ? 'success' : 'danger'" size="small">
                  {{ user.is_active ? '启用' : '禁用' }}
                </el-tag>
              </div>
            </div>
          </div>
          
          <div v-if="availableUsers.length === 0 && userSearchKeyword" class="no-users">
            <el-empty description="未找到匹配的用户" />
          </div>
        </div>
      </div>
      
      <template #footer>
        <el-button @click="addUserDialogVisible = false">取消</el-button>
        <el-button 
          type="primary" 
          @click="handleAddUsersToRole" 
          :disabled="selectedUsers.length === 0"
          :loading="addingUsers"
        >
          添加选中用户 ({{ selectedUsers.length }})
        </el-button>
      </template>
    </el-dialog>

    <!-- 用户详情对话框 -->
    <el-dialog v-model="userDetailDialogVisible" title="用户详情" width="800px">
      <div v-if="selectedUserDetail" class="user-detail">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="用户名">{{ selectedUserDetail.username }}</el-descriptions-item>
          <el-descriptions-item label="邮箱">{{ selectedUserDetail.email }}</el-descriptions-item>
          <el-descriptions-item label="姓名">{{ selectedUserDetail.full_name || '未设置' }}</el-descriptions-item>
          <el-descriptions-item label="手机号">{{ selectedUserDetail.phone || '未设置' }}</el-descriptions-item>
          <el-descriptions-item label="部门">{{ selectedUserDetail.department || '未设置' }}</el-descriptions-item>
          <el-descriptions-item label="状态">
            <el-tag :type="selectedUserDetail.is_active ? 'success' : 'danger'">
              {{ selectedUserDetail.is_active ? '启用' : '禁用' }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="最后登录">{{ selectedUserDetail.last_login || '从未登录' }}</el-descriptions-item>
          <el-descriptions-item label="创建时间">{{ selectedUserDetail.created_at }}</el-descriptions-item>
        </el-descriptions>
        
        <el-divider content-position="left">用户角色</el-divider>
        <div class="user-roles">
          <el-tag 
            v-for="role in selectedUserDetail.roles" 
            :key="role.id" 
            type="primary" 
            style="margin-right: 10px; margin-bottom: 10px;"
          >
            {{ role.name }}
          </el-tag>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { ArrowLeft, Plus, Download, Search } from '@element-plus/icons-vue'
import { roleApi } from '@/api/modules/role'
import { userApi } from '@/api/modules/user'

const route = useRoute()
const router = useRouter()

// 响应式数据
const loading = ref(false)
const tableData = ref([])
const total = ref(0)
const currentPage = ref(1)
const pageSize = ref(10)
const roleInfo = ref(null)

// 对话框状态
const addUserDialogVisible = ref(false)
const userDetailDialogVisible = ref(false)
const selectedUserDetail = ref(null)

// 搜索表单
const searchForm = reactive({
  username: '',
  email: '',
  status: ''
})

// 添加用户相关
const userSearchKeyword = ref('')
const availableUsers = ref([])
const selectedUsers = ref([])
const searchingUsers = ref(false)
const addingUsers = ref(false)
const addUserForm = reactive({})

// 计算属性
const roleId = computed(() => parseInt(route.params.id))

// 方法实现
const goBack = () => {
  router.push('/system/role')
}

const fetchRoleInfo = async () => {
  try {
    const response = await roleApi.getDetail(roleId.value)
    roleInfo.value = response
  } catch (error) {
    console.error('获取角色信息失败:', error)
    ElMessage.error('获取角色信息失败')
  }
}

const fetchRoleUsers = async () => {
  try {
    loading.value = true
    const params = {
      page: currentPage.value,
      page_size: pageSize.value,
      role_id: roleId.value,
      ...searchForm
    }
    
    const response = await roleApi.getRoleUsers(roleId.value, params)
    tableData.value = response.items || []
    total.value = response.total || 0
  } catch (error) {
    console.error('获取角色用户失败:', error)
    ElMessage.error('获取角色用户失败')
    tableData.value = []
    total.value = 0
  } finally {
    loading.value = false
  }
}

const handleSearch = () => {
  currentPage.value = 1
  fetchRoleUsers()
}

const handleReset = () => {
  Object.assign(searchForm, {
    username: '',
    email: '',
    status: ''
  })
  handleSearch()
}

const handlePageChange = (page) => {
  currentPage.value = page
  fetchRoleUsers()
}

const handlePageSizeChange = () => {
  currentPage.value = 1
  fetchRoleUsers()
}

const showAddUserDialog = () => {
  addUserDialogVisible.value = true
  userSearchKeyword.value = ''
  availableUsers.value = []
  selectedUsers.value = []
}

const searchAvailableUsers = async () => {
  if (!userSearchKeyword.value.trim()) {
    availableUsers.value = []
    return
  }

  try {
    searchingUsers.value = true
    const response = await userApi.searchUsers({
      keyword: userSearchKeyword.value,
      exclude_role_id: roleId.value,
      page_size: 20
    })
    availableUsers.value = response.items || []
  } catch (error) {
    console.error('搜索用户失败:', error)
    ElMessage.error('搜索用户失败')
  } finally {
    searchingUsers.value = false
  }
}

const toggleUserSelection = (userId) => {
  const index = selectedUsers.value.indexOf(userId)
  if (index > -1) {
    selectedUsers.value.splice(index, 1)
  } else {
    selectedUsers.value.push(userId)
  }
}

const handleAddUsersToRole = async () => {
  if (selectedUsers.value.length === 0) {
    ElMessage.warning('请选择要添加的用户')
    return
  }

  try {
    addingUsers.value = true
    await roleApi.addUsersToRole(roleId.value, selectedUsers.value)
    ElMessage.success(`成功添加 ${selectedUsers.value.length} 个用户到角色`)
    addUserDialogVisible.value = false
    fetchRoleUsers()
  } catch (error) {
    console.error('添加用户到角色失败:', error)
    ElMessage.error('添加用户到角色失败')
  } finally {
    addingUsers.value = false
  }
}

const viewUserDetail = async (user) => {
  try {
    const response = await userApi.getDetail(user.id)
    selectedUserDetail.value = response
    userDetailDialogVisible.value = true
  } catch (error) {
    console.error('获取用户详情失败:', error)
    ElMessage.error('获取用户详情失败')
  }
}

const editUserRoles = (user) => {
  router.push(`/system/user/${user.id}/roles`)
}

const removeUserFromRole = async (user) => {
  try {
    await ElMessageBox.confirm(
      `确定要将用户 "${user.username}" 从角色 "${roleInfo.value?.name}" 中移除吗？`,
      '确认操作',
      { type: 'warning' }
    )

    await roleApi.removeUserFromRole(roleId.value, user.id)
    ElMessage.success('用户移除成功')
    fetchRoleUsers()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('移除用户失败:', error)
      ElMessage.error('移除用户失败')
    }
  }
}

const exportUsers = () => {
  const data = tableData.value.map(user => ({
    用户名: user.username,
    邮箱: user.email,
    姓名: user.full_name || '',
    手机号: user.phone || '',
    部门: user.department || '',
    状态: user.is_active ? '启用' : '禁用',
    最后登录: user.last_login || '从未登录',
    创建时间: user.created_at
  }))

  const csvContent = [
    Object.keys(data[0]).join(','),
    ...data.map(row => Object.values(row).join(','))
  ].join('\n')

  const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' })
  const link = document.createElement('a')
  link.href = URL.createObjectURL(blob)
  link.download = `角色用户_${roleInfo.value?.name}_${new Date().toISOString().split('T')[0]}.csv`
  link.click()
  URL.revokeObjectURL(link.href)
  ElMessage.success('用户数据已导出')
}

// 生命周期
onMounted(async () => {
  await fetchRoleInfo()
  fetchRoleUsers()
})
</script>

<style scoped>
.role-users-page {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 15px;
}

.page-title {
  font-size: 20px;
  font-weight: bold;
}

.header-actions {
  display: flex;
  gap: 10px;
}

.role-info {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
}

.info-item {
  display: flex;
  align-items: center;
}

.label {
  font-weight: bold;
  margin-right: 8px;
  color: #666;
}

.value {
  color: #333;
}

.search-section {
  padding: 10px 0;
}

.pagination {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 20px;
}

.pagination-info {
  display: flex;
  align-items: center;
}

.add-user-section {
  max-height: 400px;
}

.user-list {
  max-height: 300px;
  overflow-y: auto;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
}

.user-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  border-bottom: 1px solid #f0f0f0;
  cursor: pointer;
  transition: background-color 0.2s;
}

.user-item:hover {
  background-color: #f5f7fa;
}

.user-item.selected {
  background-color: #e6f7ff;
  border-color: #1890ff;
}

.user-item:last-child {
  border-bottom: none;
}

.user-info {
  flex: 1;
}

.user-name {
  font-weight: bold;
  margin-bottom: 4px;
}

.user-email {
  font-size: 12px;
  color: #666;
}

.user-status {
  margin-left: 10px;
}

.no-users {
  padding: 40px 0;
  text-align: center;
}

.user-detail {
  padding: 10px 0;
}

.user-roles {
  margin-top: 10px;
}

:deep(.el-card__body) {
  padding: 20px;
}

:deep(.el-descriptions__label) {
  font-weight: bold;
}
</style>
