# 沃尔玛绑卡系统 - 部门进单开关和权重系统使用指南

## 概述

部门进单开关和权重系统是沃尔玛绑卡系统的核心功能，允许管理员精确控制各部门的绑卡参与度和优先级。通过这套系统，您可以：

- **灵活控制**：快速启用或禁用特定部门的绑卡功能
- **智能分配**：基于权重算法实现 CK 资源的合理分配
- **负载均衡**：根据业务需求动态调整各部门的绑卡比例
- **实时监控**：查看权重分配效果和系统运行状态

## 核心概念

### 1. 进单开关 (enable_binding)

**定义**：控制部门是否参与绑卡处理的布尔开关

**取值**：

- `true`：启用绑卡，该部门的 CK 可以被绑卡请求使用
- `false`：禁用绑卡，该部门的 CK 不会被绑卡请求使用

**应用场景**：

- 部门 CK 出现问题时快速禁用
- 部门维护期间暂停绑卡服务
- 新部门上线前的测试阶段

### 2. 进单权重 (binding_weight)

**定义**：决定部门 CK 被选中概率的数值权重

**取值范围**：0-10000

- `0`：不参与绑卡分配
- `1-10000`：权重越高，被选中概率越大

**权重分配示例**：

```
部门A权重：800
部门B权重：200
总权重：1000

分配比例：
- 部门A：80% (800/1000)
- 部门B：20% (200/1000)
```

## 功能特性

### 1. 实时生效

- 开关和权重变更立即生效，无需重启服务
- 支持热更新，不影响正在进行的绑卡请求

### 2. 数据隔离

- 严格的商户级数据隔离
- 部门级权限控制
- 操作日志完整记录

### 3. 高性能

- 优化的权重算法，单次选择耗时<1ms
- 支持高并发，>1000 次/秒的处理能力
- 内存使用稳定，无内存泄漏

### 4. 智能验证

- 自动检测权重配置合理性
- 提供配置建议和警告
- 支持权重算法效果测试

## 使用指南

### 1. 基础配置

#### 1.1 设置部门进单开关

**API 接口**：

```http
PUT /api/v1/departments/{department_id}/binding-controls
Content-Type: application/json

{
  "enable_binding": true,
  "binding_weight": 100
}
```

**前端操作**：

1. 进入部门管理页面
2. 选择目标部门
3. 在"绑卡控制"区域切换开关
4. 点击"保存更改"确认

#### 1.2 调整部门权重

**权重设置建议**：

- **高优先级部门**：500-1000
- **普通部门**：100-300
- **备用部门**：10-50
- **测试部门**：1-10

**批量设置**：

```http
POST /api/v1/departments/batch-binding-controls
Content-Type: application/json

{
  "department_ids": [1, 2, 3],
  "enable_binding": true,
  "binding_weight": 200
}
```

### 2. 权重算法配置

#### 2.1 权重分配策略

**均匀分配**：

```
部门A：100
部门B：100
部门C：100
结果：各部门平均分配，每个约33.3%
```

**优先级分配**：

```
主要部门：600
次要部门：300
备用部门：100
结果：主要60%，次要30%，备用10%
```

**阶梯分配**：

```
一级部门：500
二级部门：300
三级部门：150
四级部门：50
结果：按层级递减分配
```

#### 2.2 权重算法测试

**测试步骤**：

1. 配置各部门权重
2. 执行权重算法测试
3. 查看分配结果对比
4. 根据建议调整配置

**测试 API**：

```http
POST /api/v1/departments/test-weight-algorithm?test_count=1000
```

**结果解读**：

- **准确度>90%**：配置优秀，无需调整
- **准确度 70-90%**：配置良好，可微调
- **准确度<70%**：需要重新配置

### 3. 监控和维护

#### 3.1 状态监控

**获取部门绑卡状态**：

```http
GET /api/v1/departments/{department_id}/binding-status
```

**响应示例**：

```json
{
  "id": 1,
  "name": "技术部",
  "enable_binding": true,
  "binding_weight": 150,
  "can_bind_cards": true,
  "available_ck_count": 5,
  "total_ck_count": 8
}
```

#### 3.2 权重统计

**获取权重分配统计**：

```http
GET /api/v1/departments/binding-weight-stats?merchant_id=1
```

**统计信息包括**：

- 总部门数和总权重
- 各部门权重占比
- 配置验证结果
- 优化建议

#### 3.3 操作日志

系统自动记录所有操作日志，包括：

- 开关状态变更
- 权重调整记录
- 批量操作详情
- CK 选择过程

**日志格式**：

```
[DEPT_BINDING_CONTROL] update_binding_controls |
部门: 技术部(1) | 操作员: admin |
变更: {"binding_weight": {"old": 100, "new": 150}}
```

## 最佳实践

### 1. 权重配置原则

#### 1.1 业务优先级

- 根据部门业务重要性设置权重
- 核心业务部门给予更高权重
- 测试和备用部门设置较低权重

#### 1.2 CK 资源考虑

- 权重应与部门 CK 数量相匹配
- 避免高权重部门 CK 资源不足
- 定期检查 CK 可用性

#### 1.3 负载均衡

- 避免单一部门权重过高（>80%）
- 保持合理的权重分布
- 根据实际使用情况调整

### 2. 运维建议

#### 2.1 定期检查

- 每周检查权重分配效果
- 监控各部门 CK 使用情况
- 及时处理异常部门

#### 2.2 应急处理

- 部门故障时立即禁用开关
- 准备备用部门应急方案
- 建立快速恢复流程

#### 2.3 性能优化

- 避免频繁修改权重配置
- 合理设置权重数值范围
- 定期清理无效部门

### 3. 故障排查

#### 3.1 常见问题

**问题 1：部门无法绑卡**

- 检查进单开关是否启用
- 确认权重是否大于 0
- 验证部门是否有可用 CK

**问题 2：权重分配不准确**

- 增加测试次数验证
- 检查部门 CK 可用性
- 确认权重配置合理性

**问题 3：性能问题**

- 检查部门数量是否过多
- 优化权重数值设置
- 监控系统资源使用

#### 3.2 诊断工具

**权重配置验证**：

```http
GET /api/v1/departments/binding-weight-stats
```

**算法效果测试**：

```http
POST /api/v1/departments/test-weight-algorithm
```

**部门状态检查**：

```http
GET /api/v1/departments/{id}/binding-status
```

## API 参考

### 1. 部门绑卡控制

| 接口                                  | 方法 | 说明             |
| ------------------------------------- | ---- | ---------------- |
| `/departments/{id}/binding-status`    | GET  | 获取部门绑卡状态 |
| `/departments/{id}/binding-controls`  | PUT  | 更新部门绑卡控制 |
| `/departments/batch-binding-controls` | POST | 批量更新绑卡控制 |
| `/departments/binding-weight-stats`   | GET  | 获取权重统计     |
| `/departments/test-weight-algorithm`  | POST | 测试权重算法     |

### 2. 请求参数

#### 更新绑卡控制

```json
{
  "enable_binding": true,
  "binding_weight": 150
}
```

#### 批量更新

```json
{
  "department_ids": [1, 2, 3],
  "enable_binding": true,
  "binding_weight": 200
}
```

#### 权重测试

```json
{
  "test_count": 1000
}
```

## 技术架构

### 1. 核心组件

- **DepartmentWeightService**：权重算法核心服务
- **EnhancedCKService**：增强 CK 获取服务
- **DepartmentOperationLogService**：操作日志服务

### 2. 算法实现

**加权随机选择算法**：

1. 收集所有启用部门及权重
2. 计算权重总和
3. 生成随机数
4. 按权重累计选择部门
5. 从选中部门获取最优 CK

### 3. 性能指标

- **选择延迟**：<1ms
- **并发能力**：>1000 次/秒
- **准确度**：>95%
- **内存使用**：稳定无泄漏

## 更新日志

### v2.3.0 (2025-01-12)

- 新增部门进单开关功能
- 实现基于权重的 CK 分配算法
- 添加权重算法测试工具
- 完善操作日志记录
- 优化前端管理界面

## 部署指南

### 1. 数据库迁移

**执行迁移脚本**：

```bash
# 生产环境
docker exec -i walmart-bind-card-db mysql -u root -p walmart_card_db < migrations/v2.3.0-add-department-binding-controls.sql

# 测试环境
mysql -u root -p walmart_card_db < migrations/v2.3.0-add-department-binding-controls.sql
```

**验证迁移结果**：

```sql
-- 检查新字段是否添加成功
DESCRIBE departments;

-- 检查索引是否创建
SHOW INDEX FROM departments WHERE Key_name = 'idx_dept_binding_controls';

-- 验证数据完整性
SELECT
    COUNT(*) as total_departments,
    SUM(enable_binding) as enabled_departments,
    AVG(binding_weight) as avg_weight
FROM departments;
```

### 2. 服务配置

**环境变量配置**：

```bash
# 权重算法配置
DEPARTMENT_WEIGHT_ALGORITHM_ENABLED=true
DEPARTMENT_WEIGHT_DEFAULT_VALUE=100
DEPARTMENT_WEIGHT_MAX_VALUE=10000

# 性能配置
WEIGHT_SELECTION_CACHE_TTL=300
WEIGHT_ALGORITHM_TEST_MAX_COUNT=1000

# 日志配置
DEPARTMENT_OPERATION_LOG_LEVEL=INFO
DEPARTMENT_OPERATION_LOG_RETENTION_DAYS=30
```

**Redis 配置**（如果使用 Redis 缓存）：

```yaml
redis:
  department_weight_cache:
    ttl: 300
    key_prefix: "dept_weight:"
  ck_selection_cache:
    ttl: 60
    key_prefix: "ck_selection:"
```

### 3. 前端部署

**构建前端资源**：

```bash
# 安装依赖
npm install

# 构建生产版本
npm run build

# 部署到Web服务器
cp -r dist/* /var/www/html/
```

**Nginx 配置示例**：

```nginx
location /api/v1/departments/ {
    proxy_pass http://backend:8000;
    proxy_set_header Host $host;
    proxy_set_header X-Real-IP $remote_addr;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;

    # 增加超时时间以支持权重测试
    proxy_read_timeout 60s;
    proxy_connect_timeout 10s;
}
```

### 4. 监控配置

**Prometheus 指标**：

```yaml
# 部门权重相关指标
- department_weight_selections_total
- department_weight_selection_duration_seconds
- department_binding_controls_changes_total
- department_ck_availability_ratio
```

**告警规则**：

```yaml
groups:
  - name: department_binding_controls
    rules:
      - alert: DepartmentWeightSelectionSlow
        expr: histogram_quantile(0.95, department_weight_selection_duration_seconds) > 0.005
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "部门权重选择延迟过高"

      - alert: DepartmentCKUnavailable
        expr: department_ck_availability_ratio < 0.1
        for: 2m
        labels:
          severity: critical
        annotations:
          summary: "部门CK可用性过低"
```

## 安全考虑

### 1. 权限控制

**API 权限要求**：

- `api:departments:read`：查看部门绑卡状态
- `api:departments:update`：修改部门绑卡控制
- `api:departments:batch_update`：批量修改权限

**数据隔离**：

- 严格的商户级数据隔离
- 部门级权限验证
- 操作用户身份验证

### 2. 输入验证

**权重值验证**：

- 范围：0-10000
- 类型：整数
- 特殊值：0 表示不参与分配

**批量操作限制**：

- 单次最多操作 100 个部门
- 操作频率限制：每分钟最多 10 次
- 权限验证：每个部门单独验证

### 3. 审计日志

**操作记录**：

- 所有配置变更记录
- 操作用户和时间戳
- 变更前后的值对比
- 操作结果和影响范围

**日志保留**：

- 操作日志保留 90 天
- 性能日志保留 30 天
- 错误日志保留 180 天

---

**技术支持**：如有问题请联系技术团队或查看系统日志获取详细信息。
