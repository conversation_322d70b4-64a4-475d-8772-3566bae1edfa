#!/usr/bin/env python3
"""
测试重复调用修复
验证修复后不会重复计算CK使用次数
"""

import asyncio
import sys
import os

# 添加项目路径
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.insert(0, project_root)

from app.api.deps import get_db
from app.models.walmart_ck import WalmartCK
from app.services.redis_ck_wrapper import create_optimized_ck_service
from app.core.logging import get_logger

logger = get_logger("test_duplicate_fix")


async def test_single_call_only():
    """测试确保只有一次调用"""
    print(f"🧪 测试重复调用修复")
    print("="*60)
    
    db = next(get_db())
    
    try:
        # 获取一个可用CK
        ck = db.query(WalmartCK).filter(
            WalmartCK.merchant_id == 2,
            WalmartCK.active == True,
            WalmartCK.is_deleted == False
        ).first()
        
        if not ck:
            print("❌ 没有可用的CK")
            return
        
        print(f"📊 使用CK {ck.id} 进行测试")
        print(f"  初始bind_count: {ck.bind_count}")
        
        # 记录初始计数
        initial_count = ck.bind_count
        
        # 模拟绑卡成功，只调用一次record_ck_usage
        ck_service = create_optimized_ck_service(db)
        
        print(f"🎯 调用 record_ck_usage(ck_id={ck.id}, success=True)")
        result = await ck_service.record_ck_usage(ck.id, True)
        
        # 刷新数据
        db.refresh(ck)
        final_count = ck.bind_count
        
        print(f"📈 结果:")
        print(f"  record_ck_usage返回: {result}")
        print(f"  bind_count变化: {initial_count} -> {final_count}")
        print(f"  实际增加: +{final_count - initial_count}")
        
        if final_count == initial_count + 1:
            print(f"  ✅ 正确：只增加了1次")
        else:
            print(f"  ❌ 错误：期望+1，实际+{final_count - initial_count}")
        
        # 验证修复：确保binding_process_service不会再次调用
        print(f"\n🔍 验证修复效果:")
        print(f"  现在binding_process_service._save_ck_information不会调用record_ck_usage")
        print(f"  只有card_record_service._update_bind_result会调用")
        print(f"  这样避免了重复计数问题")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        db.close()


async def main():
    """主函数"""
    await test_single_call_only()
    
    print(f"\n🎯 修复总结:")
    print(f"  ✅ 移除了binding_process_service中的重复调用")
    print(f"  ✅ 保留了card_record_service中的正确调用")
    print(f"  ✅ 避免了重复计数问题")
    print(f"  ✅ 确保数据一致性")


if __name__ == "__main__":
    asyncio.run(main())
