
from sqlalchemy import (
    Column,
    String,
    BigInteger,
    Boolean,
    Text,
    ForeignKey,
    DateTime,
)
from sqlalchemy.dialects.mysql import JSON
from sqlalchemy.orm import relationship

from app.models.base import BaseModel, TimestampMixin


class NotificationType:
    """通知类型常量类"""

    SYSTEM = "SYSTEM"
    CARD = "CARD"
    MERCHANT = "MERCHANT"
    USER = "USER"

    @classmethod
    def get_all_values(cls):
        """获取所有通知类型值"""
        return [cls.SYSTEM, cls.CARD, cls.MERCHANT, cls.USER]


class NotificationStatus:
    """通知状态常量类"""

    UNREAD = "UNREAD"
    READ = "READ"
    ARCHIVED = "ARCHIVED"

    @classmethod
    def get_all_values(cls):
        """获取所有通知状态值"""
        return [cls.UNREAD, cls.READ, cls.ARCHIVED]


class Notification(BaseModel, TimestampMixin):
    """通知模型"""

    __tablename__ = "notifications"
    __table_args__ = ({"extend_existing": True},)

    id = Column(BigInteger, primary_key=True, index=True, autoincrement=True)
    user_id = Column(
        BigInteger,
        ForeignKey("users.id", ondelete="CASCADE"),
        nullable=False,
        comment="接收用户ID",
    )
    title = Column(String(255), nullable=False, comment="通知标题")
    content = Column(Text, nullable=False, comment="通知内容")
    type = Column(
        String(20),
        nullable=False,
        default="SYSTEM",
        comment="通知类型",
    )
    status = Column(
        String(20),
        nullable=False,
        default="UNREAD",
        comment="通知状态",
    )
    link = Column(String(255), nullable=True, comment="相关链接")
    read_at = Column(DateTime, nullable=True, comment="阅读时间")
    is_important = Column(Boolean, default=False, comment="是否重要")
    extra_data = Column(JSON, nullable=True, comment="额外数据")

    user = relationship(
        "User",
        back_populates="notifications",
        lazy="joined",
        innerjoin=True,
    )

    def to_dict(self):
        return {c.name: getattr(self, c.name) for c in self.__table__.columns}
