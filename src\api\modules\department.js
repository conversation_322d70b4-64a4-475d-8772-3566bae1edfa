import { http } from "@/api/request";
import { API_URLS, replaceUrlParams } from "./config";

const { DEPARTMENT } = API_URLS;

/**
 * 部门管理相关API - 基于新的AO架构（商户+部门二级结构）
 */
export const departmentApi = {
  /**
   * 获取部门列表
   * @param {Object} params - 查询参数
   * @param {number} params.page - 页码
   * @param {number} params.page_size - 每页记录数
   * @param {number} params.merchant_id - 商户ID（可选）
   * @param {number} params.parent_id - 父部门ID（可选）
   * @param {boolean} params.status - 状态过滤（可选）
   * @param {boolean} params.is_tree - 是否返回树形结构（可选）
   * @param {string} params.name - 部门名称搜索（可选）
   * @returns {Promise} 部门列表响应
   */
  async getList(params = {}) {
    try {
      // 修复参数名不一致问题：前端status -> 后端dept_status
      const backendParams = { ...params };
      if (backendParams.status !== undefined) {
        backendParams.dept_status = backendParams.status;
        delete backendParams.status;
      }

      const response = await http.get(DEPARTMENT.LIST, {
        params: backendParams,
      });
      return response.data;
    } catch (error) {
      throw new Error("获取部门列表失败：" + error.message);
    }
  },

  /**
   * 获取部门树形结构
   * @param {Object} params - 查询参数
   * @param {number} params.merchant_id - 商户ID（可选）
   * @returns {Promise} 部门树形结构
   */
  async getTree(params = {}) {
    try {
      // 确保包含 is_tree=true 参数
      const treeParams = { ...params, is_tree: true };
      const response = await http.get(DEPARTMENT.TREE, { params: treeParams });
      return response.data;
    } catch (error) {
      throw new Error("获取部门树失败：" + error.message);
    }
  },

  /**
   * 获取部门详情
   * @param {number} id - 部门ID
   * @returns {Promise} 部门详情
   */
  async getDetail(id) {
    try {
      const url = replaceUrlParams(DEPARTMENT.DETAIL, { id });
      const response = await http.get(url);
      return response.data;
    } catch (error) {
      throw new Error("获取部门详情失败：" + error.message);
    }
  },

  /**
   * 创建部门
   * @param {Object} data - 部门数据
   * @param {number} data.merchant_id - 商户ID
   * @param {string} data.name - 部门名称
   * @param {string} data.code - 部门代码
   * @param {string} data.description - 部门描述（可选）
   * @param {number} data.parent_id - 父部门ID（可选）
   * @param {string} data.manager_name - 负责人姓名（可选）
   * @param {string} data.manager_phone - 负责人电话（可选）
   * @param {string} data.manager_email - 负责人邮箱（可选）
   * @param {string} data.business_scope - 业务范围（可选）
   * @param {number} data.sort_order - 排序号（可选）
   * @param {string} data.remark - 备注（可选）
   * @returns {Promise} 创建的部门信息
   */
  async create(data) {
    try {
      const response = await http.post(DEPARTMENT.CREATE, data);
      return response.data;
    } catch (error) {
      throw new Error("创建部门失败：" + error.message);
    }
  },

  /**
   * 更新部门
   * @param {number} id - 部门ID
   * @param {Object} data - 更新的部门数据
   * @returns {Promise} 更新后的部门信息
   */
  async update(id, data) {
    try {
      const url = replaceUrlParams(DEPARTMENT.UPDATE, { id });
      const response = await http.put(url, data);
      return response.data;
    } catch (error) {
      throw new Error("更新部门失败：" + error.message);
    }
  },

  /**
   * 删除部门
   * @param {number} id - 部门ID
   * @returns {Promise} 删除结果
   */
  async delete(id) {
    try {
      const url = replaceUrlParams(DEPARTMENT.DELETE, { id });
      const response = await http.delete(url);
      return response.data;
    } catch (error) {
      throw new Error("删除部门失败：" + error.message);
    }
  },

  /**
   * 获取子部门列表
   * @param {number} parentId - 父部门ID
   * @param {Object} params - 查询参数
   * @returns {Promise} 子部门列表
   */
  async getChildren(parentId, params = {}) {
    try {
      const url = replaceUrlParams(DEPARTMENT.CHILDREN, { id: parentId });
      const response = await http.get(url, { params });
      return response.data;
    } catch (error) {
      throw new Error("获取子部门失败：" + error.message);
    }
  },

  /**
   * 移动部门
   * @param {number} id - 要移动的部门ID
   * @param {Object} data - 移动参数
   * @param {number} data.new_parent_id - 新的父部门ID（null表示移动到根级别）
   * @returns {Promise} 移动结果
   */
  async move(id, data) {
    try {
      const url = replaceUrlParams(DEPARTMENT.MOVE, { id });
      const response = await http.post(url, data);
      return response.data;
    } catch (error) {
      throw new Error("移动部门失败：" + error.message);
    }
  },

  /**
   * 批量删除部门
   * @param {Array<number>} ids - 部门ID数组
   * @returns {Promise} 删除结果
   */
  async batchDelete(ids) {
    try {
      const response = await http.post("/departments/batch-delete", { ids });
      return response.data;
    } catch (error) {
      throw new Error("批量删除部门失败：" + error.message);
    }
  },

  /**
   * 启用/禁用部门
   * @param {number} id - 部门ID
   * @param {boolean} status - 状态（true为启用，false为禁用）
   * @returns {Promise} 操作结果
   */
  async toggleStatus(id, status) {
    try {
      const url = replaceUrlParams(DEPARTMENT.UPDATE, { id });
      const response = await http.patch(url, { status });
      return response.data;
    } catch (error) {
      throw new Error("操作失败：" + error.message);
    }
  },

  /**
   * 获取部门统计信息
   * @param {number} id - 部门ID
   * @returns {Promise} 统计信息
   */
  async getStatistics(id) {
    try {
      const response = await http.get(`${DEPARTMENT.LIST}/${id}/statistics`);
      return response.data;
    } catch (error) {
      throw new Error("获取部门统计失败：" + error.message);
    }
  },

  /**
   * 获取部门用户列表
   * @param {number} id - 部门ID
   * @param {Object} params - 查询参数
   * @returns {Promise} 用户列表
   */
  async getUsers(id, params = {}) {
    try {
      const response = await http.get(`/departments/${id}/users`, { params });
      return response.data;
    } catch (error) {
      throw new Error("获取部门用户失败：" + error.message);
    }
  },

  /**
   * 分配用户到部门
   * @param {number} departmentId - 部门ID
   * @param {Array<number>} userIds - 用户ID数组
   * @returns {Promise} 分配结果
   */
  async assignUsers(departmentId, userIds) {
    try {
      const response = await http.post(`/departments/${departmentId}/users`, {
        user_ids: userIds,
      });
      return response.data;
    } catch (error) {
      throw new Error("分配用户失败：" + error.message);
    }
  },

  /**
   * 从部门移除用户
   * @param {number} departmentId - 部门ID
   * @param {Array<number>} userIds - 用户ID数组
   * @returns {Promise} 移除结果
   */
  async removeUsers(departmentId, userIds) {
    try {
      const response = await http.delete(`/departments/${departmentId}/users`, {
        data: { user_ids: userIds },
      });
      return response.data;
    } catch (error) {
      throw new Error("移除用户失败：" + error.message);
    }
  },

  /**
   * 获取部门权限范围内的数据
   * @param {number} id - 部门ID
   * @param {string} dataType - 数据类型（如：users, cards等）
   * @param {Object} params - 查询参数
   * @returns {Promise} 数据列表
   */
  async getScopeData(id, dataType, params = {}) {
    try {
      const response = await http.get(`/departments/${id}/scope/${dataType}`, {
        params,
      });
      return response.data;
    } catch (error) {
      throw new Error("获取部门数据失败：" + error.message);
    }
  },

  /**
   * 检查部门名称是否重复
   * @param {Object} params - 检查参数
   * @param {string} params.name - 部门名称
   * @param {number} params.merchant_id - 商户ID（可选）
   * @param {number} params.parent_id - 父部门ID（可选）
   * @param {number} params.exclude_id - 排除的部门ID（可选，用于编辑时排除自己）
   * @returns {Promise} 检查结果
   */
  async checkNameExists(params) {
    try {
      const response = await http.get(DEPARTMENT.LIST + "/check-name", {
        params,
      });
      return response.data;
    } catch (error) {
      throw new Error("检查部门名称失败：" + error.message);
    }
  },

  /**
   * 获取当前用户所属部门信息（CK供应商专用API）
   * @returns {Promise} 用户所属部门信息
   */
  async getMyDepartment() {
    try {
      const response = await http.get(DEPARTMENT.MY_DEPARTMENT);
      return response.data;
    } catch (error) {
      throw new Error("获取用户部门失败：" + error.message);
    }
  },

  // ========================================
  // 绑卡控制相关API
  // ========================================

  /**
   * 获取部门绑卡状态
   * @param {number} id - 部门ID
   * @returns {Promise} 部门绑卡状态信息
   */
  async getBindingStatus(id) {
    try {
      const url = replaceUrlParams(DEPARTMENT.BINDING_STATUS, { id });
      const response = await http.get(url);
      // 处理统一响应格式 {code: 0, data: {...}, message: "..."}
      if (response.code === 0) {
        return response.data;
      } else {
        throw new Error(response.message || "获取部门绑卡状态失败");
      }
    } catch (error) {
      throw new Error("获取部门绑卡状态失败：" + error.message);
    }
  },

  /**
   * 更新部门绑卡控制设置
   * @param {number} id - 部门ID
   * @param {Object} data - 绑卡控制设置
   * @param {boolean} data.enable_binding - 进单开关
   * @param {number} data.binding_weight - 进单权重
   * @returns {Promise} 更新结果
   */
  async updateBindingControls(id, data) {
    try {
      const url = replaceUrlParams(DEPARTMENT.BINDING_CONTROLS, { id });
      const response = await http.put(url, data);
      // 处理统一响应格式
      if (response.code === 0) {
        return response.data;
      } else {
        throw new Error(response.message || "更新部门绑卡控制设置失败");
      }
    } catch (error) {
      throw new Error("更新部门绑卡控制设置失败：" + error.message);
    }
  },

  /**
   * 批量更新部门绑卡控制设置
   * @param {Object} data - 批量更新数据
   * @param {number[]} data.department_ids - 部门ID列表
   * @param {boolean} data.enable_binding - 进单开关（可选）
   * @param {number} data.binding_weight - 进单权重（可选）
   * @returns {Promise} 批量更新结果
   */
  async batchUpdateBindingControls(data) {
    try {
      const response = await http.post(DEPARTMENT.BATCH_BINDING_CONTROLS, data);
      // 处理统一响应格式
      if (response.code === 0) {
        return response.data;
      } else {
        throw new Error(response.message || "批量更新部门绑卡控制设置失败");
      }
    } catch (error) {
      throw new Error("批量更新部门绑卡控制设置失败：" + error.message);
    }
  },

  /**
   * 获取绑卡权重分配统计
   * @param {Object} params - 查询参数
   * @param {number} params.merchant_id - 商户ID（可选）
   * @returns {Promise} 权重分配统计信息
   */
  async getBindingWeightStats(params = {}) {
    try {
      const response = await http.get(DEPARTMENT.BINDING_WEIGHT_STATS, {
        params,
      });
      // 处理统一响应格式
      if (response.code === 0) {
        return response.data;
      } else {
        throw new Error(response.message || "获取绑卡权重统计失败");
      }
    } catch (error) {
      throw new Error("获取绑卡权重统计失败：" + error.message);
    }
  },

  /**
   * 测试权重算法分配效果
   * @param {Object} params - 测试参数
   * @param {number} params.merchant_id - 商户ID（可选）
   * @param {number} params.test_count - 测试次数（默认100）
   * @returns {Promise} 测试结果
   */
  async testWeightAlgorithm(params = {}) {
    try {
      const response = await http.post(DEPARTMENT.TEST_WEIGHT_ALGORITHM, null, {
        params,
      });
      // 处理统一响应格式
      if (response.code === 0) {
        return response.data;
      } else {
        throw new Error(response.message || "测试权重算法失败");
      }
    } catch (error) {
      throw new Error("测试权重算法失败：" + error.message);
    }
  },
};
