#!/usr/bin/env python3
"""
简化CK服务测试 - 验证负载均衡和并发安全性
"""

import asyncio
import pytest
from typing import Dict, List
from collections import defaultdict
from sqlalchemy.orm import Session

from app.services.simplified_ck_service import SimplifiedCKService, AtomicCKUpdateService
from app.models.walmart_ck import WalmartCK
from app.models.department import Department
from app.models.merchant import Merchant
from tests.conftest import TestBase


class TestSimplifiedCKService:
    """简化CK服务测试类"""
    
    @pytest.fixture
    def setup_test_data(self, db: Session):
        """设置测试数据"""
        # 创建测试商户
        merchant = Merchant(
            id=1,
            name="测试商户",
            status="active"
        )
        db.add(merchant)
        
        # 创建测试部门
        departments = [
            Department(
                id=1,
                merchant_id=1,
                name="部门1",
                status=True,
                enable_binding=True,
                binding_weight=30
            ),
            Department(
                id=2,
                merchant_id=1,
                name="部门2",
                status=True,
                enable_binding=True,
                binding_weight=50
            ),
            Department(
                id=3,
                merchant_id=1,
                name="部门3",
                status=True,
                enable_binding=True,
                binding_weight=20
            )
        ]
        for dept in departments:
            db.add(dept)
        
        # 创建测试CK
        cks = []
        for dept_id in [1, 2, 3]:
            for i in range(3):  # 每个部门3个CK
                ck = WalmartCK(
                    merchant_id=1,
                    department_id=dept_id,
                    sign=f"test_ck_{dept_id}_{i}",
                    total_limit=100,
                    bind_count=0,
                    active=True,
                    is_deleted=False
                )
                cks.append(ck)
                db.add(ck)
        
        db.commit()
        return {
            "merchant": merchant,
            "departments": departments,
            "cks": cks
        }
    
    @pytest.mark.asyncio
    async def test_load_balance_distribution(self, db: Session, setup_test_data):
        """测试负载均衡分布"""
        service = SimplifiedCKService(db)
        
        # 执行100次CK选择
        test_rounds = 100
        ck_usage_count = defaultdict(int)
        dept_usage_count = defaultdict(int)
        
        for i in range(test_rounds):
            ck = await service.get_available_ck(merchant_id=1)
            assert ck is not None, f"第{i+1}次选择失败"
            
            ck_usage_count[ck.id] += 1
            dept_usage_count[ck.department_id] += 1
            
            # 模拟CK使用
            ck.bind_count += 1
            db.commit()
        
        # 验证负载分布
        print("\n=== CK使用分布 ===")
        for ck_id, count in ck_usage_count.items():
            percentage = count / test_rounds * 100
            print(f"CK {ck_id}: {count} 次 ({percentage:.1f}%)")
        
        print("\n=== 部门使用分布 ===")
        for dept_id, count in dept_usage_count.items():
            percentage = count / test_rounds * 100
            print(f"部门 {dept_id}: {count} 次 ({percentage:.1f}%)")
        
        # 验证没有单个CK被过度使用（超过50%）
        max_usage = max(ck_usage_count.values())
        assert max_usage < test_rounds * 0.5, f"CK负载不均衡，最大使用次数: {max_usage}"
        
        # 验证部门权重分布大致正确
        # 部门1权重30%，部门2权重50%，部门3权重20%
        dept1_usage = dept_usage_count[1] / test_rounds
        dept2_usage = dept_usage_count[2] / test_rounds
        dept3_usage = dept_usage_count[3] / test_rounds
        
        # 允许20%的误差范围
        assert 0.2 <= dept1_usage <= 0.4, f"部门1使用率异常: {dept1_usage:.2f}"
        assert 0.4 <= dept2_usage <= 0.6, f"部门2使用率异常: {dept2_usage:.2f}"
        assert 0.1 <= dept3_usage <= 0.3, f"部门3使用率异常: {dept3_usage:.2f}"
    
    @pytest.mark.asyncio
    async def test_concurrent_safety(self, db: Session, setup_test_data):
        """测试并发安全性"""
        service = AtomicCKUpdateService(db)
        
        async def concurrent_ck_selection(task_id: int) -> int:
            """并发CK选择任务"""
            ck = await service.atomic_ck_selection_and_reserve(merchant_id=1)
            if ck:
                # 模拟绑卡处理时间
                await asyncio.sleep(0.01)
                await service.commit_ck_usage(ck.id, success=True)
                return ck.id
            return None
        
        # 启动50个并发任务
        concurrent_count = 50
        tasks = [
            concurrent_ck_selection(i) 
            for i in range(concurrent_count)
        ]
        
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # 统计结果
        successful_selections = [r for r in results if isinstance(r, int)]
        failed_selections = [r for r in results if r is None]
        exceptions = [r for r in results if isinstance(r, Exception)]
        
        print(f"\n=== 并发测试结果 ===")
        print(f"成功选择: {len(successful_selections)}")
        print(f"选择失败: {len(failed_selections)}")
        print(f"异常数量: {len(exceptions)}")
        
        # 验证没有异常
        assert len(exceptions) == 0, f"并发测试出现异常: {exceptions}"
        
        # 验证大部分请求成功
        success_rate = len(successful_selections) / concurrent_count
        assert success_rate >= 0.8, f"并发成功率过低: {success_rate:.2f}"
        
        # 验证CK使用计数正确
        total_bind_count = db.query(
            db.func.sum(WalmartCK.bind_count)
        ).filter(
            WalmartCK.merchant_id == 1
        ).scalar() or 0
        
        assert total_bind_count == len(successful_selections), \
            f"CK使用计数不匹配: 数据库={total_bind_count}, 成功选择={len(successful_selections)}"
    
    @pytest.mark.asyncio
    async def test_performance_benchmark(self, db: Session, setup_test_data):
        """性能基准测试"""
        import time
        
        service = SimplifiedCKService(db)
        
        # 测试1000次CK选择的性能
        test_count = 1000
        start_time = time.time()
        
        successful_count = 0
        for i in range(test_count):
            ck = await service.get_available_ck(merchant_id=1)
            if ck:
                successful_count += 1
        
        end_time = time.time()
        
        total_time = end_time - start_time
        avg_time = total_time / test_count
        requests_per_second = test_count / total_time
        
        print(f"\n=== 性能基准测试结果 ===")
        print(f"总测试次数: {test_count}")
        print(f"成功次数: {successful_count}")
        print(f"总耗时: {total_time:.2f}秒")
        print(f"平均响应时间: {avg_time*1000:.2f}ms")
        print(f"每秒处理请求: {requests_per_second:.0f}")
        
        # 验证性能要求
        assert avg_time < 0.01, f"平均响应时间过长: {avg_time*1000:.2f}ms"
        assert requests_per_second >= 100, f"每秒处理请求数过低: {requests_per_second:.0f}"
    
    @pytest.mark.asyncio
    async def test_exclude_ids_functionality(self, db: Session, setup_test_data):
        """测试排除CK功能"""
        service = SimplifiedCKService(db)
        
        # 获取所有CK ID
        all_ck_ids = [ck.id for ck in setup_test_data["cks"]]
        
        # 排除前两个CK
        exclude_ids = all_ck_ids[:2]
        
        # 执行10次选择
        for i in range(10):
            ck = await service.get_available_ck(
                merchant_id=1, 
                exclude_ids=exclude_ids
            )
            assert ck is not None, f"第{i+1}次选择失败"
            assert ck.id not in exclude_ids, f"选择了被排除的CK: {ck.id}"
    
    @pytest.mark.asyncio
    async def test_department_specific_selection(self, db: Session, setup_test_data):
        """测试指定部门CK选择"""
        service = SimplifiedCKService(db)
        
        # 从部门1选择CK
        department_id = 1
        
        for i in range(10):
            ck = await service.get_available_ck(
                merchant_id=1, 
                department_id=department_id
            )
            assert ck is not None, f"第{i+1}次选择失败"
            assert ck.department_id == department_id, \
                f"选择了错误部门的CK: 期望={department_id}, 实际={ck.department_id}"
    
    @pytest.mark.asyncio
    async def test_ck_statistics(self, db: Session, setup_test_data):
        """测试CK统计功能"""
        service = SimplifiedCKService(db)
        
        # 获取统计信息
        stats = await service.get_ck_statistics(merchant_id=1)
        
        assert "total_cks" in stats
        assert "active_cks" in stats
        assert "available_cks" in stats
        assert "load_distribution" in stats
        
        # 验证统计数据
        assert stats["total_cks"] == 9  # 3个部门 × 3个CK
        assert stats["active_cks"] == 9
        assert stats["available_cks"] == 9
        assert len(stats["load_distribution"]) == 9
        
        # 验证负载分布数据结构
        for ck_info in stats["load_distribution"]:
            assert "ck_id" in ck_info
            assert "bind_count" in ck_info
            assert "total_limit" in ck_info
            assert "usage_rate" in ck_info
            assert "department_id" in ck_info


class TestAtomicCKUpdateService:
    """原子性CK更新服务测试"""
    
    @pytest.mark.asyncio
    async def test_atomic_selection_and_reserve(self, db: Session, setup_test_data):
        """测试原子性选择和预占用"""
        service = AtomicCKUpdateService(db)
        
        # 选择CK
        ck = await service.atomic_ck_selection_and_reserve(merchant_id=1)
        assert ck is not None
        
        # 验证bind_count已增加
        db.refresh(ck)
        assert ck.bind_count == 1
    
    @pytest.mark.asyncio
    async def test_commit_ck_usage_success(self, db: Session, setup_test_data):
        """测试提交成功的CK使用"""
        service = AtomicCKUpdateService(db)
        
        # 选择并预占用CK
        ck = await service.atomic_ck_selection_and_reserve(merchant_id=1)
        original_bind_count = ck.bind_count
        
        # 提交成功使用
        await service.commit_ck_usage(ck.id, success=True)
        
        # 验证状态
        db.refresh(ck)
        assert ck.bind_count == original_bind_count  # 计数不变（已在选择时增加）
        assert ck.last_bind_time is not None  # 更新了最后使用时间
    
    @pytest.mark.asyncio
    async def test_commit_ck_usage_failure(self, db: Session, setup_test_data):
        """测试提交失败的CK使用"""
        service = AtomicCKUpdateService(db)
        
        # 选择并预占用CK
        ck = await service.atomic_ck_selection_and_reserve(merchant_id=1)
        original_bind_count = ck.bind_count
        
        # 提交失败使用
        await service.commit_ck_usage(ck.id, success=False)
        
        # 验证计数回滚
        db.refresh(ck)
        assert ck.bind_count == original_bind_count - 1


if __name__ == "__main__":
    # 运行测试
    pytest.main([__file__, "-v", "-s"])
