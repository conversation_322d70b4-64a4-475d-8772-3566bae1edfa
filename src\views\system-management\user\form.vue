<template>
  <div class="page-container user-form-container">
    <el-card shadow="never">
      <template #header>
        <div class="card-header">
          <span class="header-title">{{ isEdit ? '编辑用户' : '新增用户' }}</span>
          <el-button @click="handleCancel" :icon="Close">关闭</el-button>
        </div>
      </template>
      <el-scrollbar class="form-scrollbar">
        <div class="form-content">
          <el-form ref="formRef" :model="form" :rules="rules" label-position="left" label-width="100px"
            class="user-form">
            <el-row :gutter="20">
              <el-col :xs="24" :sm="12">
                <el-form-item label="用户名" prop="username">
                  <el-input v-model="form.username" placeholder="用于登录系统" :prefix-icon="User" clearable
                    :disabled="isEdit" />
                  <div v-if="!isEdit" class="el-form-item__info">创建后用户名不可修改。</div>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12">
                <el-form-item label="密码" prop="password">
                  <el-input v-model="form.password" type="password" :placeholder="isEdit ? '留空则不修改密码' : '请输入至少6位密码'"
                    show-password :prefix-icon="Lock" clearable />
                  <div v-if="isEdit" class="el-form-item__info">如需修改密码，请输入新密码。</div>
                </el-form-item>
              </el-col>
            </el-row>

            <el-row :gutter="20">
              <el-col :xs="24" :sm="12">
                <el-form-item label="姓名" prop="full_name">
                  <el-input v-model="form.full_name" placeholder="用户真实姓名或昵称" :prefix-icon="UserFilled" clearable />
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12">
                <el-form-item label="用户角色" prop="role">
                  <el-select v-model="form.role" placeholder="请选择用户角色" @change="handleRoleChange" style="width: 100%;"
                    filterable>
                    <el-option v-for="role in roleOptions" :key="role.value" :label="role.label" :value="role.value" />
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>

            <el-row :gutter="20">
              <el-col :xs="24" :sm="12">
                <el-form-item label="邮箱" prop="email">
                  <el-input v-model="form.email" placeholder="请输入邮箱地址 (选填)" :prefix-icon="Message" clearable />
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12">
                <el-form-item label="手机号" prop="phone">
                  <el-input v-model="form.phone" placeholder="请输入手机号码 (选填)" :prefix-icon="Phone" clearable />
                </el-form-item>
              </el-col>
            </el-row>

            <el-row :gutter="20">
              <el-col :xs="24" :sm="12">
                <el-form-item label="所属商户" prop="merchant_id" v-if="showMerchantSelect" :required="showMerchantSelect">
                  <!-- 超级管理员：显示商户选择下拉框 -->
                  <el-select
                    v-if="canSelectMerchant"
                    v-model="form.merchant_id"
                    placeholder="请选择所属商户"
                    style="width: 100%;"
                    filterable
                    clearable
                    @change="handleMerchantChange">
                    <el-option v-for="merchant in merchantList" :key="merchant.id"
                      :label="merchant.name + ' (' + merchant.code + ')'" :value="merchant.id" />
                  </el-select>

                  <!-- 商户管理员：显示只读文本框 -->
                  <el-input
                    v-else
                    :value="currentMerchantInfo ? currentMerchantInfo.name + ' (' + currentMerchantInfo.code + ')' : '当前商户'"
                    readonly
                    style="width: 100%;"
                    placeholder="当前登录商户">
                  </el-input>

                  <div v-if="!canSelectMerchant && showMerchantSelect" class="el-form-item__info">
                    商户管理员只能为当前登录商户添加用户
                  </div>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12">
                <el-form-item label="所属部门" prop="department_id" v-if="showDepartmentSelect" :required="showDepartmentSelect">
                  <el-select v-model="form.department_id" placeholder="请选择所属部门"
                    style="width: 100%;" filterable clearable>
                    <el-option v-for="department in filteredDepartmentList" :key="department.id"
                      :label="department.name + ' (' + department.code + ')'" :value="department.id" />
                  </el-select>
                  <div class="el-form-item__info">部门管理员或操作员归属于指定部门。</div>
                </el-form-item>
              </el-col>
            </el-row>

            <el-row :gutter="20">
              <el-col :xs="24" :sm="12">
                <el-form-item label="账户状态" prop="is_active">
                  <el-switch v-model="form.is_active" inline-prompt
                    style="--el-switch-on-color: #13ce66; --el-switch-off-color: #ff4949" active-text="启用"
                    inactive-text="禁用" :active-icon="Check" :inactive-icon="Close" />
                  <div class="el-form-item__info">禁用后，用户将无法登录系统。</div>
                </el-form-item>
              </el-col>
            </el-row>

            <el-row :gutter="20">
              <el-col :span="24">
                <el-form-item label="备注信息" prop="remark">
                  <el-input v-model="form.remark" type="textarea" :rows="3" placeholder="请输入备注信息，例如用户职责等 (选填)"
                    show-word-limit maxlength="200" />
                </el-form-item>
              </el-col>
            </el-row>

            <el-divider />

            <div class="form-footer">
              <el-button type="primary" @click="handleSubmit" :loading="submitting" :icon="Check">保存用户</el-button>
              <el-button @click="handleCancel" :icon="Close">取消</el-button>
            </div>
          </el-form>
        </div>
      </el-scrollbar>
    </el-card>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { useRouter, useRoute } from 'vue-router'
import { useUserStore } from '@/store/modules/user'
import { userApi } from '@/api/modules/user'
import { merchantApi } from '@/api/modules/merchant'
import { roleApi } from '@/api/modules/role'
import { departmentApi } from '@/api/modules/department'
import { User, Lock, Message, Phone, OfficeBuilding, UserFilled, Check, Close, Avatar } from '@element-plus/icons-vue'

const router = useRouter()
const route = useRoute()
const userStore = useUserStore()

const formRef = ref(null)
const merchantList = ref([])
const roleList = ref([])
const departmentList = ref([])
const submitting = ref(false)

// 判断是否为编辑模式
const isEdit = computed(() => !!route.params.id)

// 表单数据
const form = ref({
  username: '',
  password: '',
  full_name: '',
  email: '',
  phone: '',
  role: '',
  merchant_id: null,
  department_id: null,
  is_active: true,
  remark: ''
})

// 是否显示商户选择
const showMerchantSelect = computed(() => {
  if (!form.value.role) return false

  // 基于角色代码判断是否需要选择商户
  // 除了超级管理员外，其他角色都需要选择商户
  const nonMerchantRoles = ['super_admin']
  return !nonMerchantRoles.includes(form.value.role)
})

// 是否可以选择商户 (取决于当前登录用户权限)
const canSelectMerchant = computed(() => {
  // 如果用户信息未加载，可能需要默认值或异步处理
  if (!userStore.userInfo) return false;
  // 只有超级管理员可以选择商户
  return userStore.isSuperAdmin
})

// 获取当前用户的商户信息（用于商户管理员显示）
const currentMerchantInfo = computed(() => {
  if (!userStore.userInfo || !userStore.userInfo.merchant_id) return null;
  // 从商户列表中查找当前用户的商户信息
  const merchant = merchantList.value.find(m => m.id === userStore.userInfo.merchant_id);
  return merchant || { id: userStore.userInfo.merchant_id, name: '当前商户', code: '' };
})

// 是否显示部门选择
const showDepartmentSelect = computed(() => {
  // 只要选择了商户，就可以选择部门（不限制角色）
  return !!form.value.merchant_id
})

// 过滤后的部门列表（根据选择的商户）
const filteredDepartmentList = computed(() => {
  if (!form.value.merchant_id) return []
  return departmentList.value.filter(dept => dept.merchant_id === form.value.merchant_id)
})

// 角色选项
const roleOptions = computed(() => {
  // 过滤掉超级管理员选项，并转换为前端需要的格式
  return roleList.value
    .filter(role => role.code !== 'super_admin')
    .map(role => ({
      label: role.name,
      value: role.code
    }))
})

// 表单验证规则
const rules = {
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' },
    { min: 3, max: 20, message: '长度在 3 到 20 个字符', trigger: 'blur' }
  ],
  password: [
    { required: !isEdit.value, message: '请输入密码', trigger: 'blur' },
    { min: 6, max: 64, message: '长度在 6 到 64 个字符', trigger: 'blur' }
  ],
  full_name: [
    { required: true, message: '请输入姓名', trigger: 'blur' }
  ],
  email: [
    { required: false, message: '请输入邮箱', trigger: 'blur' },
    { type: 'email', message: '请输入正确的邮箱格式', trigger: 'blur' }
  ],
  phone: [
    { required: false, message: '请输入手机号', trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号', trigger: 'blur' }
  ],
  role: [
    { required: true, message: '请选择用户角色', trigger: 'change' },
    {
      validator: (rule, value, callback) => {
        if (value === 'super_admin') {
          callback(new Error('禁止创建超级管理员'))
        } else {
          callback()
        }
      },
      trigger: 'change'
    }
  ],
  merchant_id: [
    {
      validator: (rule, value, callback) => {
        if (showMerchantSelect.value && !value) {
          callback(new Error('请选择所属商户'))
        } else {
          callback()
        }
      },
      trigger: 'change'
    }
  ],
  department_id: [
    {
      validator: (rule, value, callback) => {
        if (showDepartmentSelect.value && !value) {
          callback(new Error('请选择所属部门'))
        } else {
          callback()
        }
      },
      trigger: 'change'
    }
  ]
}

// 角色变更处理
const handleRoleChange = async (role) => {
  // 基于角色代码判断是否需要商户
  // 除了超级管理员外，其他角色都需要选择商户
  const nonMerchantRoles = ['super_admin']
  const needsMerchant = !nonMerchantRoles.includes(role)

  // 如果切换到不需要商户的角色，清空商户ID和部门ID
  if (!needsMerchant) {
    form.value.merchant_id = null
    form.value.department_id = null
  }
  // 如果需要商户且当前用户是商户管理员，自动设置商户ID且不允许修改
  else if (needsMerchant && userStore.isMerchantAdmin) {
    form.value.merchant_id = userStore.merchantId
    // 自动加载该商户的部门列表
    if (userStore.merchantId) {
      await fetchDepartments(userStore.merchantId)
    }
  }
  // 如果需要商户且是超级管理员，需要选择商户
  else if (needsMerchant && userStore.isSuperAdmin) {
    form.value.merchant_id = null
  }
}

// 商户变更处理
const handleMerchantChange = (merchantId) => {
  // 清空部门选择
  form.value.department_id = null
  // 重新获取该商户的部门列表
  if (merchantId) {
    fetchDepartments(merchantId)
  }
}

// 获取角色列表
const fetchRoles = async () => {
  try {
    const response = await roleApi.getList()
    roleList.value = Array.isArray(response) ? response : (response.items || [])
  } catch (error) {
    console.error('获取角色列表失败:', error)
    ElMessage.error('获取角色列表失败')
  }
}

// 获取商户列表（仅超级管理员需要）
const fetchMerchants = async () => {
  // 只有超级管理员才需要获取商户列表
  if (!userStore.isSuperAdmin) {
    // 商户管理员使用自己的商户信息
    if (userStore.userInfo && userStore.userInfo.merchant_id) {
      merchantList.value = [{
        id: userStore.userInfo.merchant_id,
        name: userStore.userInfo.merchant_name || '当前商户',
        code: userStore.userInfo.merchant_code || ''
      }];
    }
    return;
  }

  try {
    const response = await merchantApi.getList()
    merchantList.value = response.items || response
  } catch (error) {
    console.error('获取商户列表失败:', error)
    ElMessage.error('获取商户列表失败')
  }
}

// 获取部门列表
const fetchDepartments = async (merchantId = null) => {
  try {
    const params = {}
    if (merchantId) {
      params.merchant_id = merchantId
    }
    const response = await departmentApi.getList(params)
    departmentList.value = response.items || response
  } catch (error) {
    console.error('获取部门列表失败:', error)
    ElMessage.error('获取部门列表失败')
  }
}

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return

  await formRef.value.validate(async (valid) => {
    if (valid) {
      submitting.value = true
      try {
        const formData = { ...form.value }

        // 如果邮箱为空，从提交数据中移除
        if (!formData.email) {
          delete formData.email
        }

        // 将角色代码转换为角色ID列表
        if (formData.role) {
          const selectedRole = roleList.value.find(role => role.code === formData.role)
          if (selectedRole) {
            formData.role_ids = [selectedRole.id]
          }
          // 移除role字段，使用role_ids
          delete formData.role
        }

        if (isEdit.value) {
          // 获取用户id
          await userApi.update(formData.id, formData)
        } else {
          await userApi.create(formData)
          ElMessage.success('创建用户成功')
        }
        router.push({ path: '/system/user', query: { refresh: Date.now() } })
      } catch (error) {
        console.error(isEdit.value ? '更新用户失败:' : '创建用户失败:', error)
        ElMessage.error(error.message || (isEdit.value ? '更新用户失败' : '创建用户失败'))
      } finally {
        submitting.value = false
      }
    }
  })
}

// 获取用户数据 (编辑模式下)
const fetchUserData = async () => {
  if (!isEdit.value) return;
  try {
    const userId = route.params.id;
    const response = await userApi.getDetail(userId);

    // 检查是否为超级管理员
    if (response.is_superuser) {
      ElMessage.warning('超级管理员账号不允许编辑');
      router.push('/system/user');
      return;
    }

    form.value = { ...response };
    form.value.password = '';

    // 处理角色数据回显
    if (response.roles && Array.isArray(response.roles) && response.roles.length > 0) {
      // 使用第一个角色作为当前选中的角色
      form.value.role = response.roles[0].code;
    } else {
      form.value.role = '';
    }

    // 如果用户有商户ID，需要加载对应的部门列表
    if (form.value.merchant_id) {
      await fetchDepartments(form.value.merchant_id);
    }
  } catch (error) {
    console.error('获取用户详情失败:', error);
    ElMessage.error('加载用户信息失败');
    router.push('/system/user');
  }
};

// 取消
const handleCancel = () => {
  router.push('/system/user')
}

onMounted(async () => {
  // 获取角色列表
  await fetchRoles()

  // 根据用户权限获取商户列表
  await fetchMerchants()

  // 如果是商户管理员且不是编辑模式，自动设置商户ID并加载部门列表
  if (!isEdit.value && userStore.isMerchantAdmin && userStore.merchantId) {
    form.value.merchant_id = userStore.merchantId
    // 自动加载该商户的部门列表
    await fetchDepartments(userStore.merchantId)
  }

  if (isEdit.value) {
    fetchUserData();
  }
})
</script>

<style scoped>
.page-container {
  background-color: var(--el-bg-color-page);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-title {
  font-size: 1.2rem;
  font-weight: 600;
  color: var(--el-text-color-primary);
}

.form-scrollbar {
  padding-right: 5px;
}

.form-content {
  padding: 20px 15px 20px 0px;
}

.user-form .el-form-item {
  margin-bottom: 18px;
}

.el-form-item__info {
  color: var(--el-text-color-secondary);
  font-size: 12px;
  line-height: 1.4;
  margin-top: 2px;
}

.user-form .el-select {
  width: 100%;
}

.el-divider--horizontal {
  margin: 25px 0;
}

.form-footer {
  margin-top: 25px;
  padding-top: 20px;
  border-top: 1px solid var(--el-border-color-lighter);
  display: flex;
  justify-content: flex-end;
  gap: 15px;
}

:deep(.el-form-item__label) {
  line-height: 32px;
}

:deep(.el-form-item__content) {
  line-height: 32px;
}

.el-form-item--default .el-switch {
  margin-top: 4px;
}
</style>