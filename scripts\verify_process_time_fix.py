#!/usr/bin/env python3
"""
验证处理时间修复是否成功
专门用于验证历史数据修复的效果
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from app.db.session import SessionLocal
from app.models.card_record import CardRecord
from app.utils.time_utils import ensure_timezone
from app.core.logging import get_logger

logger = get_logger("verify_fix")

def verify_process_time_accuracy():
    """验证处理时间的准确性"""
    print("🔍 验证处理时间修复效果")
    print("=" * 60)
    
    db = SessionLocal()
    try:
        # 查询所有已完成的记录
        records = db.query(CardRecord).filter(
            CardRecord.status.in_(['success', 'failed']),
            CardRecord.created_at.isnot(None),
            CardRecord.updated_at.isnot(None),
            CardRecord.process_time.isnot(None)
        ).order_by(CardRecord.created_at.desc()).limit(100).all()
        
        if not records:
            print("❌ 没有找到符合条件的记录")
            return
        
        print(f"📊 检查最近 {len(records)} 条记录")
        
        accurate_count = 0
        inaccurate_count = 0
        tolerance = 1.0  # 允许1秒误差
        
        inaccurate_records = []
        
        for record in records:
            stored_time = record.process_time
            
            # 计算期望的处理时间
            created_at_tz = ensure_timezone(record.created_at)
            updated_at_tz = ensure_timezone(record.updated_at)
            expected_time = (updated_at_tz - created_at_tz).total_seconds()
            
            # 计算差异
            time_diff = abs(stored_time - expected_time)
            
            if time_diff <= tolerance:
                accurate_count += 1
            else:
                inaccurate_count += 1
                inaccurate_records.append({
                    'id': str(record.id)[:8],
                    'status': record.status,
                    'stored_time': stored_time,
                    'expected_time': expected_time,
                    'diff': time_diff,
                    'created_at': record.created_at,
                    'updated_at': record.updated_at
                })
        
        # 显示统计结果
        total_count = len(records)
        accuracy_rate = (accurate_count / total_count) * 100
        
        print(f"\n📊 验证结果统计:")
        print(f"   总记录数: {total_count}")
        print(f"   ✅ 准确记录: {accurate_count}")
        print(f"   ❌ 不准确记录: {inaccurate_count}")
        print(f"   📈 准确率: {accuracy_rate:.1f}%")
        
        # 显示不准确的记录详情
        if inaccurate_records:
            print(f"\n⚠️  不准确的记录详情 (前10条):")
            for i, item in enumerate(inaccurate_records[:10], 1):
                print(f"  {i}. {item['id']}... | 状态: {item['status']}")
                print(f"     存储时间: {item['stored_time']:.2f}s")
                print(f"     期望时间: {item['expected_time']:.2f}s")
                print(f"     差异: {item['diff']:.2f}s")
                print(f"     创建: {item['created_at']}")
                print(f"     更新: {item['updated_at']}")
                print()
        
        # 判断修复是否成功
        if accuracy_rate >= 95:
            print("🎉 修复成功！处理时间计算准确率达到95%以上")
        elif accuracy_rate >= 90:
            print("✅ 修复基本成功，准确率达到90%以上")
        else:
            print("⚠️  修复效果不理想，建议重新检查修复逻辑")
        
        return accuracy_rate
        
    except Exception as e:
        print(f"❌ 验证失败: {e}")
        logger.exception("验证处理时间准确性失败")
        return 0
    
    finally:
        db.close()

def check_recent_records():
    """检查最近的记录是否使用了新的计算逻辑"""
    print("\n🔍 检查最近记录的处理时间")
    print("=" * 60)
    
    db = SessionLocal()
    try:
        # 查询最近创建的记录
        recent_records = db.query(CardRecord).filter(
            CardRecord.status.in_(['success', 'failed'])
        ).order_by(CardRecord.created_at.desc()).limit(5).all()
        
        if not recent_records:
            print("❌ 没有找到最近的记录")
            return
        
        print(f"📊 检查最近 {len(recent_records)} 条记录")
        
        for i, record in enumerate(recent_records, 1):
            print(f"\n📝 记录 {i}: {str(record.id)[:8]}...")
            print(f"   状态: {record.status}")
            print(f"   创建时间: {record.created_at}")
            print(f"   更新时间: {record.updated_at}")
            
            if record.process_time is not None:
                print(f"   存储的处理时间: {record.process_time:.2f}s")
                
                # 计算期望时间
                if record.created_at and record.updated_at:
                    created_at_tz = ensure_timezone(record.created_at)
                    updated_at_tz = ensure_timezone(record.updated_at)
                    expected_time = (updated_at_tz - created_at_tz).total_seconds()
                    print(f"   期望的处理时间: {expected_time:.2f}s")
                    
                    diff = abs(record.process_time - expected_time)
                    if diff <= 1.0:
                        print(f"   ✅ 时间计算正确 (差异: {diff:.2f}s)")
                    else:
                        print(f"   ❌ 时间计算错误 (差异: {diff:.2f}s)")
                else:
                    print(f"   ⚠️  缺少时间信息")
            else:
                print(f"   ⚠️  没有处理时间信息")
    
    except Exception as e:
        print(f"❌ 检查失败: {e}")
        logger.exception("检查最近记录失败")
    
    finally:
        db.close()

def analyze_time_distribution():
    """分析处理时间分布"""
    print("\n📈 分析处理时间分布")
    print("=" * 60)
    
    db = SessionLocal()
    try:
        # 查询有处理时间的记录
        records = db.query(CardRecord).filter(
            CardRecord.status.in_(['success', 'failed']),
            CardRecord.process_time.isnot(None)
        ).all()
        
        if not records:
            print("❌ 没有找到有处理时间的记录")
            return
        
        process_times = [record.process_time for record in records]
        
        # 计算统计信息
        total_count = len(process_times)
        avg_time = sum(process_times) / total_count
        min_time = min(process_times)
        max_time = max(process_times)
        
        # 分类统计
        very_fast = len([t for t in process_times if t < 5])  # 小于5秒
        fast = len([t for t in process_times if 5 <= t < 30])  # 5-30秒
        normal = len([t for t in process_times if 30 <= t < 300])  # 30秒-5分钟
        slow = len([t for t in process_times if 300 <= t < 1800])  # 5-30分钟
        very_slow = len([t for t in process_times if t >= 1800])  # 超过30分钟
        
        print(f"📊 处理时间分布统计 (总计 {total_count} 条记录):")
        print(f"   平均时间: {avg_time:.2f}s ({avg_time/60:.1f}分钟)")
        print(f"   最短时间: {min_time:.2f}s")
        print(f"   最长时间: {max_time:.2f}s ({max_time/3600:.1f}小时)")
        
        print(f"\n📊 时间分布:")
        print(f"   ⚡ 极快 (<5s): {very_fast} 条 ({very_fast/total_count*100:.1f}%)")
        print(f"   🚀 快速 (5-30s): {fast} 条 ({fast/total_count*100:.1f}%)")
        print(f"   ⏱️  正常 (30s-5m): {normal} 条 ({normal/total_count*100:.1f}%)")
        print(f"   🐌 较慢 (5-30m): {slow} 条 ({slow/total_count*100:.1f}%)")
        print(f"   🐢 很慢 (>30m): {very_slow} 条 ({very_slow/total_count*100:.1f}%)")
        
        # 判断分布是否合理
        reasonable_count = very_fast + fast + normal  # 5分钟内的记录
        reasonable_rate = reasonable_count / total_count * 100
        
        if reasonable_rate >= 80:
            print(f"\n✅ 时间分布合理，{reasonable_rate:.1f}% 的记录在5分钟内完成")
        else:
            print(f"\n⚠️  时间分布可能异常，只有 {reasonable_rate:.1f}% 的记录在5分钟内完成")
    
    except Exception as e:
        print(f"❌ 分析失败: {e}")
        logger.exception("分析时间分布失败")
    
    finally:
        db.close()

def main():
    """主函数"""
    print("🔍 处理时间修复验证工具")
    print("=" * 80)
    
    # 验证处理时间准确性
    accuracy_rate = verify_process_time_accuracy()
    
    # 检查最近的记录
    check_recent_records()
    
    # 分析时间分布
    analyze_time_distribution()
    
    print("\n" + "=" * 80)
    
    if accuracy_rate >= 95:
        print("🎉 验证完成：处理时间修复成功！")
    elif accuracy_rate >= 90:
        print("✅ 验证完成：处理时间修复基本成功")
    else:
        print("⚠️  验证完成：处理时间修复效果不理想，建议进一步检查")

if __name__ == "__main__":
    main()
