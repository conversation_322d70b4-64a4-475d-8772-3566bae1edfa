from typing import Dict, Any, Optional
from sqlalchemy.orm import Session

from app.models.walmart_server import WalmartServer
from app.models.user import User
from app.core.errors import BusinessException, ErrorCode
from fastapi import status
from app.schemas.system import SystemParams  # 从新的schema文件导入


class SystemService:
    """系统服务，处理系统参数相关的业务逻辑"""

    @staticmethod
    async def get_system_params(db: Session):
        """获取系统参数"""
        try:
            # 从数据库获取系统参数
            config = db.query(WalmartServer).first()
            return config
        except Exception as e:
            # 记录异常并返回None
            return None

    @staticmethod
    async def update_system_params(db: Session, params_in: SystemParams):
        """更新系统参数"""
        try:
            # 检查配置是否存在
            config = db.query(WalmartServer).first()

            if config:
                # 更新现有配置
                config.daily_bind_limit = params_in.daily_bind_limit
                config.api_rate_limit = params_in.api_rate_limit
                config.max_retry_times = params_in.max_retry_times
                config.bind_timeout_seconds = params_in.bind_timeout_seconds
                config.verification_code_expires = params_in.verification_code_expires
                config.log_retention_days = params_in.log_retention_days
                config.enable_ip_whitelist = params_in.enable_ip_whitelist
                config.enable_security_audit = params_in.enable_security_audit
                config.maintenance_mode = params_in.maintenance_mode
                config.maintenance_message = params_in.maintenance_message
            else:
                # 创建新配置
                config = WalmartServer(
                    api_url="https://api.walmart.com",  # 默认API地址
                    daily_bind_limit=params_in.daily_bind_limit,
                    api_rate_limit=params_in.api_rate_limit,
                    max_retry_times=params_in.max_retry_times,
                    bind_timeout_seconds=params_in.bind_timeout_seconds,
                    verification_code_expires=params_in.verification_code_expires,
                    log_retention_days=params_in.log_retention_days,
                    enable_ip_whitelist=params_in.enable_ip_whitelist,
                    enable_security_audit=params_in.enable_security_audit,
                    maintenance_mode=params_in.maintenance_mode,
                    maintenance_message=params_in.maintenance_message,
                )
                db.add(config)

            # 提交更改
            db.commit()
            db.refresh(config)
            return config
        except Exception as e:
            db.rollback()
            raise BusinessException(
                message=f"更新系统参数失败: {str(e)}", code=ErrorCode.SYSTEM_ERROR
            )

    @staticmethod
    async def get_merchant_system_params(
        db: Session, merchant_id: int, current_user: User
    ):
        """获取商家系统参数"""
        # 检查权限
        if not current_user.is_superuser and current_user.merchant_id != merchant_id:
            raise BusinessException(
                message="没有权限访问此商家的系统参数",
                code=ErrorCode.FORBIDDEN,
                status_code=status.HTTP_403_FORBIDDEN,
            )

        try:
            # 从数据库中读取租户特定的系统参数
            # 这里可以根据实际需求实现
            return {
                "apiTimeout": 10 + (merchant_id % 5),
                "requestLimit": 100 + (merchant_id * 10),
                "dailyBindQuota": 500 + (merchant_id * 100),
                "apiRateLimit": 30 + (merchant_id * 5),
            }
        except Exception as e:
            raise BusinessException(
                message=f"获取商家系统参数失败: {str(e)}", code=ErrorCode.SYSTEM_ERROR
            )

    @staticmethod
    async def update_merchant_system_params(
        db: Session, merchant_id: int, params: Dict[str, Any], current_user: User
    ):
        """更新商家系统参数"""
        # 检查权限
        if not current_user.is_superuser and current_user.merchant_id != merchant_id:
            raise BusinessException(
                message="没有权限更新此商家的系统参数",
                code=ErrorCode.FORBIDDEN,
                status_code=status.HTTP_403_FORBIDDEN,
            )

        try:
            # 这里可以更新数据库中租户特定的系统参数
            # 实际实现应该根据您的数据模型来处理
            return params
        except Exception as e:
            raise BusinessException(
                message=f"更新商家系统参数失败: {str(e)}", code=ErrorCode.SYSTEM_ERROR
            )
