#!/usr/bin/env python3
"""
沃尔玛绑卡系统错误处理机制综合测试

测试目标：
1. 验证API错误响应解析的准确性
2. 验证错误信息传播链路的完整性
3. 验证日志记录的完整性
4. 模拟各种API错误格式进行测试
"""

import asyncio
import json
import unittest
from unittest.mock import Mock, patch, AsyncMock
from sqlalchemy.orm import Session

from app.core.walmart_api import WalmartAPI
from app.services.walmart_api_service import WalmartAPIService
from app.services.binding_process_service import BindingProcessService
from app.models.card_record import CardRecord
from app.models.merchant import Merchant
from app.models.walmart_ck import WalmartCK


class TestErrorHandlingComprehensive(unittest.TestCase):
    """错误处理综合测试"""

    def setUp(self):
        """测试初始化"""
        self.api_service = WalmartAPIService()
        
        # 模拟数据库会话
        self.mock_db = Mock(spec=Session)
        
        # 模拟卡记录
        self.mock_record = Mock(spec=CardRecord)
        self.mock_record.id = 1
        self.mock_record.card_number = "1234567890123456"
        
        # 模拟商户
        self.mock_merchant = Mock(spec=Merchant)
        self.mock_merchant.id = 1
        
        # 模拟CK
        self.mock_walmart_ck = Mock(spec=WalmartCK)
        self.mock_walmart_ck.id = 1

    def test_api_error_response_parsing(self):
        """测试API错误响应解析准确性"""
        print("\n=== 测试API错误响应解析准确性 ===")
        
        # 模拟沃尔玛API返回的错误格式
        mock_error_response = {
            "logId": "cYsLnfCa",
            "status": False,
            "error": {
                "errorcode": 10004,
                "message": "无效的请求参数",
                "redirect": None,
                "validators": None
            },
            "data": None
        }
        
        # 创建API实例
        api = WalmartAPI(
            encryption_key="test_key",
            version="47",
            sign="test_sign"
        )
        
        # 测试错误处理方法
        should_retry = api.handle_error(10004, mock_error_response)
        
        # 验证返回值
        self.assertFalse(should_retry)
        print("✅ 错误处理方法正确返回不重试")
        
        # 验证错误信息提取（通过日志验证，这里简化处理）
        print("✅ 错误信息解析测试通过")

    def test_error_propagation_chain(self):
        """测试错误信息传播链路"""
        print("\n=== 测试错误信息传播链路 ===")
        
        # 模拟API响应数据
        mock_api_response = {
            "logId": "test_log_id_123",
            "status": False,
            "error": {
                "errorcode": 203,
                "message": "请先去登录"
            },
            "data": None
        }
        
        # 测试_evaluate_api_result方法
        result = self.api_service._evaluate_api_result(
            self.mock_db, mock_api_response, self.mock_walmart_ck, 1
        )
        
        # 验证错误信息传播
        self.assertFalse(result["success"])
        self.assertEqual(result["error"], "请先去登录")
        self.assertEqual(result["error_code"], 203)
        self.assertEqual(result["log_id"], "test_log_id_123")
        self.assertIsNotNone(result["raw_response"])
        
        print("✅ 错误信息传播链路测试通过")

    def test_error_logging_completeness(self):
        """测试日志记录完整性"""
        print("\n=== 测试日志记录完整性 ===")
        
        # 模拟完整的错误响应
        mock_error_response = {
            "logId": "error_log_12345",
            "status": False,
            "error": {
                "errorcode": 5042,
                "message": "需要重新登录"
            },
            "data": None
        }
        
        with patch('app.core.walmart_api.logger') as mock_logger:
            api = WalmartAPI(
                encryption_key="test_key",
                version="47", 
                sign="test_sign"
            )
            
            api.handle_error(5042, mock_error_response)
            
            # 验证日志调用
            mock_logger.error.assert_called_once()
            log_call_args = mock_logger.error.call_args[0][0]
            
            # 验证日志包含所有必要信息
            self.assertIn("errorcode=5042", log_call_args)
            self.assertIn("message=需要重新登录", log_call_args)
            self.assertIn("logId=error_log_12345", log_call_args)
            
        print("✅ 日志记录完整性测试通过")

    def test_various_error_formats(self):
        """测试各种错误格式处理"""
        print("\n=== 测试各种错误格式处理 ===")
        
        test_cases = [
            {
                "name": "标准错误格式",
                "response": {
                    "logId": "std_error_001",
                    "status": False,
                    "error": {
                        "errorcode": 10131,
                        "message": "该电子卡已被其他用户绑定"
                    }
                },
                "expected_retry": False
            },
            {
                "name": "需要重试的错误",
                "response": {
                    "logId": "retry_error_001", 
                    "status": False,
                    "error": {
                        "errorcode": 203,
                        "message": "请先去登录"
                    }
                },
                "expected_retry": True
            },
            {
                "name": "缺少logId的错误",
                "response": {
                    "status": False,
                    "error": {
                        "errorcode": 9999,
                        "message": "需要隐式登录"
                    }
                },
                "expected_retry": False
            },
            {
                "name": "缺少errorcode的错误",
                "response": {
                    "logId": "no_code_001",
                    "status": False,
                    "error": {
                        "message": "未知错误"
                    }
                },
                "expected_retry": False
            }
        ]
        
        api = WalmartAPI(
            encryption_key="test_key",
            version="47",
            sign="test_sign"
        )
        
        for case in test_cases:
            with self.subTest(case=case["name"]):
                error_code = case["response"].get("error", {}).get("errorcode", 0)
                should_retry = api.handle_error(error_code, case["response"])
                
                self.assertEqual(
                    should_retry, 
                    case["expected_retry"],
                    f"错误格式 '{case['name']}' 的重试判断不正确"
                )
                
        print("✅ 各种错误格式处理测试通过")

    def test_error_result_creation(self):
        """测试错误结果创建"""
        print("\n=== 测试错误结果创建 ===")
        
        # 测试带完整信息的错误结果创建
        error_result = self.api_service._create_error_result(
            error="测试错误消息",
            error_code="TEST_ERROR_001",
            card_number="1234567890123456",
            log_id="test_log_123",
            raw_response={"test": "data"}
        )
        
        # 验证错误结果结构
        self.assertFalse(error_result["success"])
        self.assertEqual(error_result["error"], "测试错误消息")
        self.assertEqual(error_result["error_code"], "TEST_ERROR_001")
        self.assertEqual(error_result["log_id"], "test_log_123")
        self.assertIsNotNone(error_result["raw_response"])
        self.assertEqual(error_result["data"]["errorCode"], "TEST_ERROR_001")
        self.assertEqual(error_result["data"]["cardNumber"], "1234567890123456")
        self.assertEqual(error_result["data"]["logId"], "test_log_123")
        
        print("✅ 错误结果创建测试通过")

    def test_user_friendly_error_messages(self):
        """测试用户友好的错误消息"""
        print("\n=== 测试用户友好的错误消息 ===")
        
        # 测试常见错误码的用户友好消息
        error_code_messages = {
            10004: "请求参数无效，请检查卡号和密码",
            10131: "该卡片已被其他用户绑定",
            203: "登录状态已过期，系统正在重试",
            5042: "需要重新登录，系统正在处理"
        }
        
        for error_code, expected_message in error_code_messages.items():
            mock_response = {
                "logId": f"test_{error_code}",
                "status": False,
                "error": {
                    "errorcode": error_code,
                    "message": expected_message
                }
            }
            
            result = self.api_service._evaluate_api_result(
                self.mock_db, mock_response, self.mock_walmart_ck, 1
            )
            
            self.assertEqual(result["error"], expected_message)
            
        print("✅ 用户友好错误消息测试通过")


def run_error_handling_tests():
    """运行错误处理测试"""
    print("🧪 开始错误处理综合测试")
    print("="*60)
    
    # 创建测试套件
    suite = unittest.TestLoader().loadTestsFromTestCase(TestErrorHandlingComprehensive)
    
    # 运行测试
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    # 打印测试结果
    if result.wasSuccessful():
        print("\n🎉 所有错误处理测试通过！")
        print("\n📋 测试覆盖范围:")
        print("1. ✅ API错误响应解析准确性")
        print("2. ✅ 错误信息传播链路完整性")
        print("3. ✅ 日志记录完整性")
        print("4. ✅ 各种错误格式处理")
        print("5. ✅ 错误结果创建")
        print("6. ✅ 用户友好错误消息")
    else:
        print(f"\n❌ 测试失败: {len(result.failures)} 个失败, {len(result.errors)} 个错误")
        
    return result.wasSuccessful()


if __name__ == "__main__":
    run_error_handling_tests()
