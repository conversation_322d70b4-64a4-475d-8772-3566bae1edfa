from sqlalchemy import Column, String, Integer, BigInteger, Boolean, Text, ForeignKey, DateTime
from sqlalchemy.orm import relationship
from typing import List

from app.models.base import BaseModel, TimestampMixin
from app.models.associations import user_permissions, user_roles


# UserRole枚举已移除，改用动态角色系统


class User(BaseModel, TimestampMixin):
    """用户模型"""

    __tablename__ = "users"
    __table_args__ = ({"extend_existing": True},)  # 允许表重定义

    id = Column(BigInteger, primary_key=True, index=True, autoincrement=True)
    username = Column(
        String(50), unique=True, index=True, nullable=False, comment="用户名"
    )
    email = Column(String(100), unique=True, index=True, nullable=True, comment="邮箱")
    hashed_password = Column(String(200), nullable=False, comment="密码哈希")
    full_name = Column(String(100), nullable=True, comment="姓名")
    phone = Column(String(20), nullable=True, comment="电话")

    # 用户状态和权限
    is_active = Column(Boolean, default=True, comment="是否激活")
    is_superuser = Column(Boolean, default=False, comment="是否为超级用户")
    # 移除role字段，改用roles多对多关系

    # 外键关联 - 保持向后兼容
    merchant_id = Column(
        BigInteger, ForeignKey("merchants.id"), nullable=True, comment="所属商家ID（兼容字段）"
    )
    merchant = relationship(
        "Merchant", back_populates="users", foreign_keys=[merchant_id]
    )

    # 部门关联 - 用户的主要部门
    department_id = Column(
        BigInteger, ForeignKey("departments.id"), nullable=True, comment="所属部门ID"
    )
    department = relationship(
        "Department", foreign_keys=[department_id]
    )

    # 新的组织架构关联（通过user_organizations表，不直接关联department）
    position = Column(String(100), nullable=True, comment="职位")
    user_organizations = relationship(
        "UserOrganization", back_populates="user", foreign_keys="[UserOrganization.user_id]",
        cascade="all, delete-orphan", lazy="dynamic"
    )

    # 卡记录关联
    # card_records = relationship("CardRecord", back_populates="user", lazy="dynamic")

    # API相关配置（针对API用户）
    api_key = Column(String(64), nullable=True, unique=True, comment="API密钥")
    api_secret = Column(String(128), nullable=True, comment="API密钥密文")
    sign = Column(String(128), nullable=True, comment="签名密钥")
    daily_bind_limit = Column(Integer, default=100, comment="每日绑卡数量限制")
    hourly_bind_limit = Column(Integer, default=20, comment="每小时绑卡数量限制")

    # 其他信息
    last_login_ip = Column(String(64), nullable=True, comment="最后登录IP")
    last_login_time = Column(String(64), nullable=True, comment="最后登录时间")
    remark = Column(Text, nullable=True, comment="备注")

    # 双因子认证相关字段
    totp_secret = Column(String(255), nullable=True, comment="谷歌验证器密钥(加密存储)")
    totp_enabled = Column(Boolean, default=False, comment="是否启用双因子认证")
    totp_backup_codes = Column(Text, nullable=True, comment="备用恢复码(JSON格式,加密存储)")
    totp_last_used_at = Column(DateTime(timezone=True), nullable=True, comment="最后使用TOTP的时间")
    totp_setup_at = Column(DateTime(timezone=True), nullable=True, comment="TOTP设置时间")

    # 权限相关
    permissions = relationship(
        "Permission",
        secondary=user_permissions,
        back_populates="users",
    )

    def get_primary_organization(self):
        """获取用户的主要组织关系"""
        return self.user_organizations.filter_by(is_primary=True, status=True).first()

    def get_all_organizations(self, active_only=True):
        """获取用户的所有组织关系"""
        query = self.user_organizations
        if active_only:
            from datetime import date
            today = date.today()
            query = query.filter(
                self.user_organizations.c.status == True,
                (self.user_organizations.c.start_date.is_(None) |
                 (self.user_organizations.c.start_date <= today)),
                (self.user_organizations.c.end_date.is_(None) |
                 (self.user_organizations.c.end_date >= today))
            )
        return query.all()

    def get_accessible_merchants(self):
        """获取用户可访问的商户列表（基于新的AO架构）"""
        if self.is_platform_user():
            # 超级管理员和平台管理员可以访问所有商户
            return []  # 空列表表示无限制

        # 商户用户只能访问自己的商户（严格数据隔离）
        if self.merchant_id:
            return [self.merchant_id]

        # 如果用户有组织关系，从组织关系中获取
        organizations = self.get_all_organizations()
        return list(set([org.merchant_id for org in organizations]))

    def get_accessible_departments(self, include_descendants=True):
        """获取用户可访问的部门列表（基于动态权限系统）"""
        # 超级管理员可以访问所有部门
        if self.is_superuser:
            return []  # 空列表表示无限制

        accessible_dept_ids = []

        # 从组织关系中获取可访问的部门
        organizations = self.get_all_organizations()
        for org in organizations:
            if org.department_id:
                accessible_dept_ids.append(org.department_id)

                # 如果需要包含子部门，添加子部门ID
                if include_descendants and org.department:
                    child_ids = org.department.get_all_children_ids()
                    accessible_dept_ids.extend(child_ids)

        return list(set(accessible_dept_ids))

    def can_access_merchant(self, merchant_id):
        """检查是否可以访问指定商户"""
        if self.is_platform_user():
            return True
        return merchant_id in self.get_accessible_merchants()

    def can_access_department(self, department_id):
        """检查是否可以访问指定部门"""
        if self.is_platform_user():
            return True
        return department_id in self.get_accessible_departments()

    def is_platform_user(self):
        """检查是否为平台用户（基于动态权限系统）"""
        return self.is_superuser

    def is_merchant_admin(self):
        """检查是否为商户管理员（基于动态权限系统）"""
        return self.is_superuser  # 简化：只有超级管理员才有全局权限

    def is_department_admin(self):
        """检查是否为部门管理员（基于动态权限系统）"""
        return self.is_superuser  # 简化：只有超级管理员才有全局权限

    def can_access_merchant_data_new(self, target_merchant_id: int) -> bool:
        """检查用户是否可以访问指定商户的数据（新的AO架构）"""
        if self.is_platform_user():
            return True

        return self.merchant_id == target_merchant_id

    def can_access_department_data_new(self, target_department_id: int, target_merchant_id: int = None) -> bool:
        """检查用户是否可以访问指定部门的数据（基于动态权限系统）"""
        if self.is_platform_user():
            return True

        # 首先检查商户权限
        if target_merchant_id and not self.can_access_merchant_data_new(target_merchant_id):
            return False

        # 通过组织关系检查部门访问权限
        primary_org = self.get_primary_organization()
        return primary_org and primary_org.department_id == target_department_id

    def get_organization_scope(self):
        """获取用户的组织权限范围"""
        if self.is_platform_user():
            return "platform"
        elif self.is_merchant_admin():
            return "merchant"
        elif self.is_department_admin():
            return "department"
        else:
            return "own"

    def get_data_filter_conditions(self, target_table_alias=None):
        """获取数据过滤条件（用于查询时的权限控制）"""
        conditions = []

        if self.is_platform_user():
            # 平台用户可以访问所有数据
            return conditions

        # 获取用户可访问的商户和部门
        accessible_merchants = self.get_accessible_merchants()
        accessible_departments = self.get_accessible_departments()

        if target_table_alias:
            if accessible_merchants:
                conditions.append(f"{target_table_alias}.merchant_id IN ({','.join(map(str, accessible_merchants))})")
            if accessible_departments:
                conditions.append(f"{target_table_alias}.department_id IN ({','.join(map(str, accessible_departments))})")

        return conditions
    roles = relationship(
        "Role",
        secondary=user_roles,
        back_populates="users",
    )

    # TOTP日志关联
    totp_logs = relationship("TOTPLog", back_populates="user", lazy="dynamic")

    # 权限黑名单关系已删除

    # 通知关系
    notifications = relationship(
        "Notification",
        back_populates="user",
        lazy="dynamic",
        cascade="all, delete-orphan",
    )

    # 审计日志关系
    audit_logs = relationship(
        "AuditLog",
        foreign_keys="[AuditLog.user_id]",
        back_populates="user",
    )
    operated_logs = relationship(
        "AuditLog",
        foreign_keys="[AuditLog.operator_id]",
        back_populates="operator",
    )

    def to_dict(self, include_sensitive=False):
        """转换为字典

        Args:
            include_sensitive: 是否包含敏感信息

        Returns:
            dict: 用户字典
        """
        data = {
            "id": self.id,
            "username": self.username,
            "email": self.email,
            "isActive": self.is_active,
            "isSuperuser": self.is_superuser,
            "merchantId": self.merchant_id,
            "createdAt": self.created_at.isoformat() if self.created_at else None,
            "updatedAt": self.updated_at.isoformat() if self.updated_at else None,
        }

        # 移除敏感信息
        if not include_sensitive:
            data.pop("hashed_password", None)
            data.pop("api_secret", None)

        # 添加角色名称（基于动态角色系统）
        data["roleName"] = self.get_role_name()
        data["roles"] = [role.name for role in self.roles] if hasattr(self, 'roles') and self.roles else []

        return data

    def get_role_name(self):
        """获取角色名称（基于动态角色系统）"""
        if self.is_superuser:
            return "超级管理员"

        if hasattr(self, 'roles') and self.roles:
            # 返回第一个角色的名称，或者可以返回所有角色名称的组合
            return self.roles[0].name if self.roles else "普通用户"

        return "普通用户"

    def has_permission(self, permission_code):
        """检查用户是否有特定权限

        Args:
            permission_code: 权限代码

        Returns:
            bool: 是否有权限
        """
        # 超级管理员拥有所有权限
        if self.is_superuser:
            return True

        # 检查直接分配的用户权限
        for permission in self.permissions:
            if permission.code == permission_code:
                return True

        # 检查通过角色获得的权限
        for role in self.roles:
            if role.has_permission(permission_code):
                return True

        return False

    def has_menu_access(self, menu_code):
        """检查用户是否有菜单访问权限

        Args:
            menu_code: 菜单代码

        Returns:
            bool: 是否有访问权限
        """
        # 超级管理员拥有所有菜单权限
        if self.is_superuser:
            return True

        # 检查通过角色获得的菜单权限
        for role in self.roles:
            if role.has_menu(menu_code):
                return True

        return False

    def get_all_permissions(self):
        """获取用户的所有权限（包括角色权限）

        Returns:
            set: 权限代码集合
        """
        permissions = set()

        # 添加直接分配的权限
        for permission in self.permissions:
            permissions.add(permission.code)

        # 添加角色权限
        for role in self.roles:
            for permission in role.permissions:
                permissions.add(permission.code)

        return permissions

    def get_all_menus(self):
        """获取用户的所有菜单（通过角色）

        Returns:
            list: 菜单对象列表
        """
        menus = []
        menu_ids = set()

        for role in self.roles:
            for menu in role.menus:
                if menu.id not in menu_ids:
                    menus.append(menu)
                    menu_ids.add(menu.id)

        return menus

    def is_admin(self):
        """是否为管理员（基于动态权限系统）"""
        return self.is_superuser

    def is_merchant_user(self) -> bool:
        """判断是否为商户用户（基于动态权限系统）"""
        # 有商户ID的用户就是商户用户
        return self.merchant_id is not None

    def can_access_merchant_data(self, merchant_id: int) -> bool:
        """
        判断用户是否可以访问指定商户的数据

        Args:
            merchant_id: 商户ID

        Returns:
            bool: 是否可以访问
        """
        # 平台用户可以访问所有商户数据
        if self.is_platform_user():
            return True

        # 商户用户只能访问自己商户的数据
        return self.merchant_id == merchant_id

    def get_accessible_merchant_ids(self) -> List[int]:
        """
        获取用户可以访问的商户ID列表

        Returns:
            List[int]: 可访问的商户ID列表
        """
        # 平台用户可以访问所有商户
        if self.is_platform_user():
            return []  # 空列表表示可以访问所有商户

        # 商户用户只能访问自己的商户
        return [self.merchant_id] if self.merchant_id else []
