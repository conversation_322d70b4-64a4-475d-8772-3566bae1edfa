#!/bin/bash
# 沃尔玛绑卡网关日志轮转配置脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查是否为root用户
check_root() {
    if [[ $EUID -ne 0 ]]; then
        log_error "此脚本需要root权限运行"
        log_info "请使用: sudo $0"
        exit 1
    fi
}

# 创建日志目录
create_log_directory() {
    local log_dir="/var/log/walmart-gateway"
    
    log_info "创建日志目录: $log_dir"
    
    mkdir -p "$log_dir"
    chown walmart-gateway:walmart-gateway "$log_dir" 2>/dev/null || {
        log_warn "用户 walmart-gateway 不存在，使用当前用户"
        chown $USER:$USER "$log_dir"
    }
    chmod 755 "$log_dir"
    
    log_info "日志目录创建完成"
}

# 创建logrotate配置
create_logrotate_config() {
    local config_file="/etc/logrotate.d/walmart-gateway"
    
    log_info "创建logrotate配置: $config_file"
    
    cat > "$config_file" << 'EOF'
# 沃尔玛绑卡网关日志轮转配置
/var/log/walmart-gateway/*.log {
    # 每日轮转
    daily
    
    # 如果日志文件不存在，不报错
    missingok
    
    # 保留30天的日志
    rotate 30
    
    # 压缩旧日志文件
    compress
    
    # 延迟压缩（下次轮转时压缩）
    delaycompress
    
    # 如果日志文件为空，不轮转
    notifempty
    
    # 创建新日志文件的权限
    create 644 walmart-gateway walmart-gateway
    
    # 当日志文件大小超过100MB时立即轮转
    size 100M
    
    # 轮转后执行的命令
    postrotate
        # 发送USR1信号给进程，让其重新打开日志文件
        /bin/kill -USR1 $(cat /var/run/walmart-gateway.pid 2>/dev/null) 2>/dev/null || true
        
        # 或者重启服务（如果使用systemd）
        # systemctl reload walmart-gateway 2>/dev/null || true
    endscript
    
    # 复制并截断原文件而不是移动
    copytruncate
}

# 应用程序特定日志
/var/log/walmart-gateway/error.log {
    daily
    missingok
    rotate 60
    compress
    delaycompress
    notifempty
    create 644 walmart-gateway walmart-gateway
    size 50M
    copytruncate
}

# 访问日志
/var/log/walmart-gateway/access.log {
    daily
    missingok
    rotate 14
    compress
    delaycompress
    notifempty
    create 644 walmart-gateway walmart-gateway
    size 200M
    copytruncate
}
EOF

    log_info "logrotate配置创建完成"
}

# 测试logrotate配置
test_logrotate() {
    log_info "测试logrotate配置..."
    
    if logrotate -d /etc/logrotate.d/walmart-gateway; then
        log_info "✅ logrotate配置测试通过"
    else
        log_error "❌ logrotate配置测试失败"
        return 1
    fi
}

# 创建日志清理脚本
create_cleanup_script() {
    local cleanup_script="/usr/local/bin/walmart-gateway-log-cleanup.sh"
    
    log_info "创建日志清理脚本: $cleanup_script"
    
    cat > "$cleanup_script" << 'EOF'
#!/bin/bash
# 沃尔玛绑卡网关日志清理脚本

LOG_DIR="/var/log/walmart-gateway"
MAX_AGE_DAYS=30
MAX_SIZE_GB=5

# 删除超过30天的日志文件
find "$LOG_DIR" -name "*.log.*" -type f -mtime +$MAX_AGE_DAYS -delete

# 如果日志目录超过5GB，删除最旧的文件
current_size=$(du -s "$LOG_DIR" | cut -f1)
max_size_kb=$((MAX_SIZE_GB * 1024 * 1024))

if [ "$current_size" -gt "$max_size_kb" ]; then
    echo "日志目录超过${MAX_SIZE_GB}GB，清理最旧的文件..."
    find "$LOG_DIR" -name "*.log.*" -type f -printf '%T@ %p\n' | sort -n | head -10 | cut -d' ' -f2- | xargs rm -f
fi

# 压缩超过1天的日志文件
find "$LOG_DIR" -name "*.log" -type f -mtime +1 ! -name "*.gz" -exec gzip {} \;

echo "日志清理完成: $(date)"
EOF

    chmod +x "$cleanup_script"
    log_info "日志清理脚本创建完成"
}

# 设置定时任务
setup_cron_job() {
    log_info "设置定时清理任务..."
    
    # 添加到crontab（每天凌晨2点执行）
    (crontab -l 2>/dev/null; echo "0 2 * * * /usr/local/bin/walmart-gateway-log-cleanup.sh >> /var/log/walmart-gateway/cleanup.log 2>&1") | crontab -
    
    log_info "定时清理任务设置完成（每天凌晨2点执行）"
}

# 显示配置信息
show_config_info() {
    echo ""
    log_info "========================================="
    log_info "🎯 日志轮转配置完成！"
    log_info "========================================="
    echo ""
    log_info "📁 日志目录: /var/log/walmart-gateway/"
    log_info "📋 配置文件: /etc/logrotate.d/walmart-gateway"
    log_info "🧹 清理脚本: /usr/local/bin/walmart-gateway-log-cleanup.sh"
    echo ""
    log_info "📊 轮转策略:"
    log_info "  • 每日轮转或文件超过100MB时轮转"
    log_info "  • 保留30天的日志文件"
    log_info "  • 自动压缩旧日志文件"
    log_info "  • 每天凌晨2点自动清理"
    echo ""
    log_info "🔧 手动操作命令:"
    log_info "  • 手动轮转: sudo logrotate -f /etc/logrotate.d/walmart-gateway"
    log_info "  • 手动清理: sudo /usr/local/bin/walmart-gateway-log-cleanup.sh"
    log_info "  • 查看日志: tail -f /var/log/walmart-gateway/walmart-gateway.log"
    echo ""
}

# 主函数
main() {
    log_info "开始配置沃尔玛绑卡网关日志轮转..."
    
    check_root
    create_log_directory
    create_logrotate_config
    test_logrotate
    create_cleanup_script
    setup_cron_job
    show_config_info
    
    log_info "✅ 日志轮转配置完成！"
}

# 执行主函数
main "$@"
