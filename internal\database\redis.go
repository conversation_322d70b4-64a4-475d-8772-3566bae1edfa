package database

import (
	"context"
	"fmt"
	"time"

	"walmart-bind-card-processor/internal/config"

	"github.com/go-redis/redis/v8"
)

// RedisClient 全局Redis客户端实例
var RedisClient *redis.Client

// NewRedis 创建新的Redis连接，与Python系统配置完全兼容
func NewRedis(cfg config.RedisConfig) (*redis.Client, error) {
	// 创建Redis客户端，配置与Python系统保持一致
	rdb := redis.NewClient(&redis.Options{
		Addr:         cfg.GetRedisAddr(),
		Password:     cfg.Password,
		DB:           cfg.DB,
		PoolSize:     cfg.PoolSize,
		MinIdleConns: cfg.MinIdleConns,
		DialTimeout:  5 * time.Second,
		ReadTimeout:  3 * time.Second,
		WriteTimeout: 3 * time.Second,
		PoolTimeout:  4 * time.Second,
		IdleTimeout:  5 * time.Minute,
	})
	
	// 测试连接
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()
	
	if err := rdb.Ping(ctx).Err(); err != nil {
		return nil, fmt.Errorf("failed to ping Redis: %w", err)
	}
	
	// 设置全局实例
	RedisClient = rdb
	
	return rdb, nil
}

// GetRedisClient 获取Redis客户端实例
func GetRedisClient() *redis.Client {
	return RedisClient
}

// CloseRedis 关闭Redis连接
func CloseRedis() error {
	if RedisClient != nil {
		return RedisClient.Close()
	}
	return nil
}

// RedisHealthCheck Redis健康检查
func RedisHealthCheck() error {
	if RedisClient == nil {
		return fmt.Errorf("Redis client is nil")
	}
	
	ctx, cancel := context.WithTimeout(context.Background(), 3*time.Second)
	defer cancel()
	
	if err := RedisClient.Ping(ctx).Err(); err != nil {
		return fmt.Errorf("Redis ping failed: %w", err)
	}
	
	return nil
}

// GetRedisStats 获取Redis统计信息
func GetRedisStats() map[string]interface{} {
	if RedisClient == nil {
		return map[string]interface{}{
			"status": "disconnected",
		}
	}
	
	ctx, cancel := context.WithTimeout(context.Background(), 3*time.Second)
	defer cancel()
	
	// 获取连接池统计
	poolStats := RedisClient.PoolStats()
	
	// 获取Redis信息
	info, err := RedisClient.Info(ctx).Result()
	if err != nil {
		return map[string]interface{}{
			"status": "error",
			"error":  err.Error(),
		}
	}
	
	return map[string]interface{}{
		"status":      "connected",
		"pool_stats": map[string]interface{}{
			"hits":        poolStats.Hits,
			"misses":      poolStats.Misses,
			"timeouts":    poolStats.Timeouts,
			"total_conns": poolStats.TotalConns,
			"idle_conns":  poolStats.IdleConns,
			"stale_conns": poolStats.StaleConns,
		},
		"server_info": info,
	}
}
