[{"test_type": "绑卡功能测试", "timestamp": "20250615_221757", "datetime": "2025-06-15T22:17:57.632411", "summary": {"total_tests": 22, "passed_tests": 13, "failed_tests": 9, "success_rate": 59.09090909090909, "duration_seconds": 11.20955491065979}, "module_stats": {"绑卡API测试": {"total": 9, "passed": 6, "failed": 3}, "绑卡管理API测试": {"total": 4, "passed": 1, "failed": 3}, "批量绑卡API测试": {"total": 4, "passed": 3, "failed": 1}, "绑卡CRUD测试": {"total": 5, "passed": 3, "failed": 2}}, "test_results": [{"test_name": "有效绑卡请求", "success": false, "message": "绑卡请求失败，状态码: 401", "details": {"response": {"code": 1, "message": "签名验证失败", "data": null}}, "timestamp": 1749997068.984113, "datetime": "2025-06-15T22:17:48.984113", "module": "绑卡API测试", "module_key": "test_bind_card_api"}, {"test_name": "无效卡号验证", "success": false, "message": "未正确验证卡号，状态码: 401", "details": {"response": {"code": 1, "message": "签名验证失败", "data": null}}, "timestamp": 1749997068.9921505, "datetime": "2025-06-15T22:17:48.992150", "module": "绑卡API测试", "module_key": "test_bind_card_api"}, {"test_name": "无效金额验证", "success": true, "message": "正确拒绝了无效金额", "details": {"status_code": 422}, "timestamp": 1749997069.0001493, "datetime": "2025-06-15T22:17:49.000149", "module": "绑卡API测试", "module_key": "test_bind_card_api"}, {"test_name": "无效API密钥验证", "success": true, "message": "正确拒绝了无效API密钥", "details": {"status_code": 401}, "timestamp": 1749997069.0041516, "datetime": "2025-06-15T22:17:49.004151", "module": "绑卡API测试", "module_key": "test_bind_card_api"}, {"test_name": "无效签名验证", "success": true, "message": "正确拒绝了无效签名", "details": {"status_code": 401}, "timestamp": 1749997069.0174942, "datetime": "2025-06-15T22:17:49.017494", "module": "绑卡API测试", "module_key": "test_bind_card_api"}, {"test_name": "重复卡号验证", "success": false, "message": "未正确验证重复卡号，状态码: 401", "details": {"response": {"code": 1, "message": "签名验证失败", "data": null}}, "timestamp": 1749997069.0355804, "datetime": "2025-06-15T22:17:49.035580", "module": "绑卡API测试", "module_key": "test_bind_card_api"}, {"test_name": "缺少必填字段验证", "success": true, "message": "正确拒绝了缺少必填字段的请求", "details": {"status_code": 422}, "timestamp": 1749997069.039579, "datetime": "2025-06-15T22:17:49.039579", "module": "绑卡API测试", "module_key": "test_bind_card_api"}, {"test_name": "商户编码不匹配验证", "success": true, "message": "正确拒绝了不匹配的商户编码", "details": {"status_code": 400}, "timestamp": 1749997069.0472412, "datetime": "2025-06-15T22:17:49.047241", "module": "绑卡API测试", "module_key": "test_bind_card_api"}, {"test_name": "过期时间戳验证", "success": true, "message": "正确拒绝了过期时间戳", "details": {"status_code": 401}, "timestamp": 1749997069.0552533, "datetime": "2025-06-15T22:17:49.055253", "module": "绑卡API测试", "module_key": "test_bind_card_api"}, {"test_name": "管理员获取绑卡记录列表", "success": false, "message": "获取记录列表失败，状态码: 500", "details": {"response": {"code": 1, "message": "获取绑卡记录列表失败: 'pending' is not among the defined enum values. Enum name: cardstatus. Possible values: PENDING, PROCESSING, SUCCESS, ..., CANCELLED", "data": null}}, "timestamp": 1749997071.6466527, "datetime": "2025-06-15T22:17:51.646652", "module": "绑卡管理API测试", "module_key": "test_card_management_api"}, {"test_name": "商户获取绑卡记录列表", "success": false, "message": "商户获取记录列表失败，状态码: 500", "details": {"response": {"code": 1, "message": "获取绑卡记录列表失败: 'pending' is not among the defined enum values. Enum name: cardstatus. Possible values: PENDING, PROCESSING, SUCCESS, ..., CANCELLED", "data": null}}, "timestamp": 1749997071.6897554, "datetime": "2025-06-15T22:17:51.689755", "module": "绑卡管理API测试", "module_key": "test_card_management_api"}, {"test_name": "创建绑卡记录", "success": false, "message": "创建绑卡记录失败，状态码: 500", "details": {"response": {"code": 1, "message": "创建绑卡记录失败: 'created_by' is an invalid keyword argument for CardRecord", "data": null}}, "timestamp": 1749997071.7164776, "datetime": "2025-06-15T22:17:51.716477", "module": "绑卡管理API测试", "module_key": "test_card_management_api"}, {"test_name": "获取绑卡统计", "success": true, "message": "成功获取绑卡统计信息", "details": {"merchant_id": 1}, "timestamp": 1749997071.7895539, "datetime": "2025-06-15T22:17:51.789553", "module": "绑卡管理API测试", "module_key": "test_card_management_api"}, {"test_name": "批量创建绑卡记录", "success": false, "message": "批量创建失败，仅成功创建 0 个记录", "details": {"card_ids": []}, "timestamp": 1749997074.5053267, "datetime": "2025-06-15T22:17:54.505326", "module": "批量绑卡API测试", "module_key": "test_batch_bind_api"}, {"test_name": "按状态批量筛选", "success": true, "message": "状态筛选功能测试完成", "details": {"tested_statuses": ["pending", "success", "failed"]}, "timestamp": 1749997074.7181873, "datetime": "2025-06-15T22:17:54.718187", "module": "批量绑卡API测试", "module_key": "test_batch_bind_api"}, {"test_name": "按日期范围批量筛选", "success": true, "message": "成功筛选今日记录 0 条", "details": {"today_count": 0}, "timestamp": 1749997074.7421792, "datetime": "2025-06-15T22:17:54.742179", "module": "批量绑卡API测试", "module_key": "test_batch_bind_api"}, {"test_name": "批量分页查询", "success": true, "message": "分页查询功能测试完成", "details": {"tested_page_sizes": [5, 10, 20]}, "timestamp": 1749997074.8138664, "datetime": "2025-06-15T22:17:54.813866", "module": "批量绑卡API测试", "module_key": "test_batch_bind_api"}, {"test_name": "管理员获取卡记录列表", "success": false, "message": "获取卡记录列表失败，状态码: 500", "details": {}, "timestamp": 1749997077.4025059, "datetime": "2025-06-15T22:17:57.402506", "module": "绑卡CRUD测试", "module_key": "test_cards_crud"}, {"test_name": "商户获取卡记录列表", "success": false, "message": "商户获取卡记录列表失败，状态码: 500", "details": {}, "timestamp": 1749997077.4393413, "datetime": "2025-06-15T22:17:57.439341", "module": "绑卡CRUD测试", "module_key": "test_cards_crud"}, {"test_name": "获取卡记录统计", "success": true, "message": "成功获取卡记录统计", "details": {}, "timestamp": 1749997077.4807117, "datetime": "2025-06-15T22:17:57.480711", "module": "绑卡CRUD测试", "module_key": "test_cards_crud"}, {"test_name": "获取今日卡记录统计", "success": true, "message": "成功获取今日卡记录统计", "details": {}, "timestamp": 1749997077.5032177, "datetime": "2025-06-15T22:17:57.503217", "module": "绑卡CRUD测试", "module_key": "test_cards_crud"}, {"test_name": "卡记录数据隔离", "success": true, "message": "数据隔离正常，管理员看到 0 条，商户看到 0 条", "details": {}, "timestamp": 1749997077.6303785, "datetime": "2025-06-15T22:17:57.630378", "module": "绑卡CRUD测试", "module_key": "test_cards_crud"}]}]