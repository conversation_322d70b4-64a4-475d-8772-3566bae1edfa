-- ========================================
-- 恢复card_records表的正确业务逻辑
-- department_id和walmart_ck_id字段应该在创建时为NULL
-- 只有在绑卡成功后才填入实际使用的CK信息
-- ========================================

USE `walmart_card_db`;

-- 1. 检查当前数据状态
SELECT 
    COUNT(*) as total_records,
    COUNT(department_id) as has_department_id,
    COUNT(walmart_ck_id) as has_walmart_ck_id,
    COUNT(*) - COUNT(department_id) as null_department_id,
    COUNT(*) - COUNT(walmart_ck_id) as null_walmart_ck_id,
    COUNT(CASE WHEN status = 'pending' THEN 1 END) as pending_records,
    COUNT(CASE WHEN status = 'success' THEN 1 END) as success_records,
    COUNT(CASE WHEN status = 'failed' THEN 1 END) as failed_records
FROM card_records;

-- 2. 对于状态为pending的记录，这些字段应该为NULL（符合业务逻辑）
-- 清空pending状态记录的department_id和walmart_ck_id
UPDATE card_records 
SET 
    department_id = NULL,
    walmart_ck_id = NULL
WHERE status = 'pending';

-- 3. 对于状态为failed但这两个字段不为NULL的记录
-- 这些记录可能是绑卡过程中失败的，需要根据具体情况处理
-- 如果是CK失效导致的失败，应该清空这些字段以便重试
UPDATE card_records 
SET 
    department_id = NULL,
    walmart_ck_id = NULL
WHERE status = 'failed' 
AND (
    error_message LIKE '%需要登录%' OR
    error_message LIKE '%登录失效%' OR
    error_message LIKE '%CK失效%' OR
    error_message LIKE '%用户未登录%' OR
    error_message LIKE '%session过期%' OR
    error_message LIKE '%认证失败%' OR
    error_message LIKE '%401%' OR
    error_message LIKE '%403%' OR
    JSON_EXTRACT(response_data, '$.need_retry_with_new_user') = true OR
    JSON_EXTRACT(response_data, '$.error_code') IN (203, 401, 403) OR
    JSON_EXTRACT(response_data, '$.code') IN (203, 401, 403)
);

-- 4. 验证修复结果
SELECT 
    '修复后的数据状态' as description,
    COUNT(*) as total_records,
    COUNT(department_id) as has_department_id,
    COUNT(walmart_ck_id) as has_walmart_ck_id,
    COUNT(*) - COUNT(department_id) as null_department_id,
    COUNT(*) - COUNT(walmart_ck_id) as null_walmart_ck_id
FROM card_records;

-- 5. 按状态分组查看字段分布
SELECT 
    status,
    COUNT(*) as total,
    COUNT(department_id) as has_dept_id,
    COUNT(walmart_ck_id) as has_ck_id,
    COUNT(*) - COUNT(department_id) as null_dept_id,
    COUNT(*) - COUNT(walmart_ck_id) as null_ck_id
FROM card_records 
GROUP BY status
ORDER BY status;

-- 6. 显示业务逻辑说明
SELECT 
    '业务逻辑说明' as info,
    'pending状态: department_id和walmart_ck_id应该为NULL' as pending_logic,
    'success状态: 这两个字段应该有值，记录实际使用的CK信息' as success_logic,
    'failed状态: 根据失败原因决定是否清空这两个字段' as failed_logic;

-- 7. 检查是否有异常数据需要手动处理
SELECT 
    id,
    merchant_id,
    department_id,
    walmart_ck_id,
    status,
    error_message,
    created_at
FROM card_records 
WHERE (
    -- pending状态但有CK信息的异常记录
    (status = 'pending' AND (department_id IS NOT NULL OR walmart_ck_id IS NOT NULL))
    OR
    -- success状态但缺少CK信息的异常记录
    (status = 'success' AND (department_id IS NULL OR walmart_ck_id IS NULL))
)
LIMIT 10;

-- 8. 显示修复统计
SELECT 
    'card_records表业务逻辑修复完成' as message,
    (SELECT COUNT(*) FROM card_records WHERE status = 'pending' AND department_id IS NULL AND walmart_ck_id IS NULL) as correct_pending_records,
    (SELECT COUNT(*) FROM card_records WHERE status = 'success' AND department_id IS NOT NULL AND walmart_ck_id IS NOT NULL) as correct_success_records,
    (SELECT COUNT(*) FROM card_records WHERE status = 'pending' AND (department_id IS NOT NULL OR walmart_ck_id IS NOT NULL)) as incorrect_pending_records,
    (SELECT COUNT(*) FROM card_records WHERE status = 'success' AND (department_id IS NULL OR walmart_ck_id IS NULL)) as incorrect_success_records;
