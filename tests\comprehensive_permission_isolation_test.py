#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
沃尔玛绑卡系统综合权限控制和数据隔离测试套件
按照严格的测试顺序执行：创建角色 -> 分配权限 -> 创建商户 -> 创建部门 -> 创建用户 -> 登录验证 -> 权限验证
"""

import sys
import os
import time
import asyncio
from datetime import datetime
from typing import Dict, List, Any, Optional

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(__file__)))

from test.conftest import TestBase, get_test_accounts, format_test_result, print_test_summary, save_test_report
from test.base_playwright_test import BasePlaywrightTest


class ComprehensivePermissionIsolationTest:
    """综合权限控制和数据隔离测试套件"""
    
    def __init__(self):
        self.api_test = TestBase()
        self.ui_test = BasePlaywrightTest()
        self.results = []
        self.test_data = {
            "roles": [],
            "merchants": [],
            "departments": [],
            "users": [],
            "permissions": []
        }
        self.admin_token = None
        
        # 测试用的角色配置
        self.test_roles = [
            {
                "name": "测试超级管理员",
                "code": "test_super_admin",
                "description": "测试用超级管理员角色",
                "permissions": ["all"]  # 所有权限
            },
            {
                "name": "测试商户管理员",
                "code": "test_merchant_admin", 
                "description": "测试用商户管理员角色",
                "permissions": [
                    # 菜单权限
                    "menu:dashboard", "menu:system:user", "menu:merchant:department",
                    # API权限
                    "api:users:read", "api:users:create", "api:users:update", "api:users:delete",
                    "api:departments:read", "api:departments:create", "api:departments:update", "api:departments:delete",
                    "api:roles:read", "api:menus:user-menus",
                    # 数据权限
                    "data:merchant:own"
                ]
            },
            {
                "name": "测试部门管理员",
                "code": "test_department_admin",
                "description": "测试用部门管理员角色", 
                "permissions": [
                    # 菜单权限
                    "menu:dashboard", "menu:system:user",
                    # API权限
                    "api:users:read", "api:users:create", "api:users:update",
                    "api:menus:user-menus",
                    # 数据权限
                    "data:department:own"
                ]
            },
            {
                "name": "测试普通用户",
                "code": "test_normal_user",
                "description": "测试用普通用户角色",
                "permissions": [
                    # 菜单权限
                    "menu:dashboard",
                    # API权限
                    "api:menus:user-menus",
                    # 数据权限
                    "data:personal:own"
                ]
            }
        ]
        
        # 测试用的商户配置
        self.test_merchants = [
            {
                "name": "测试商户A有限公司",
                "code": "test_merchant_a",
                "api_key": "test_merchant_a_key_001",
                "api_secret": "test_merchant_a_secret_001",
                "status": True,
                "daily_limit": 1000,
                "hourly_limit": 100
            },
            {
                "name": "测试商户B有限公司", 
                "code": "test_merchant_b",
                "api_key": "test_merchant_b_key_002",
                "api_secret": "test_merchant_b_secret_002",
                "status": True,
                "daily_limit": 500,
                "hourly_limit": 50
            }
        ]
        
        # 测试用的部门配置
        self.test_departments = [
            {
                "name": "测试商户A-技术部",
                "code": "tech_dept_a",
                "description": "商户A的技术部门"
            },
            {
                "name": "测试商户A-运营部",
                "code": "ops_dept_a", 
                "description": "商户A的运营部门"
            },
            {
                "name": "测试商户B-技术部",
                "code": "tech_dept_b",
                "description": "商户B的技术部门"
            }
        ]
        
        # 测试用的用户配置
        self.test_users = [
            {
                "username": "test_merchant_a_admin",
                "password": "test123456",
                "email": "<EMAIL>",
                "full_name": "商户A管理员",
                "role_code": "test_merchant_admin",
                "merchant_code": "test_merchant_a",
                "department_code": None
            },
            {
                "username": "test_merchant_b_admin", 
                "password": "test123456",
                "email": "<EMAIL>",
                "full_name": "商户B管理员",
                "role_code": "test_merchant_admin",
                "merchant_code": "test_merchant_b",
                "department_code": None
            },
            {
                "username": "test_dept_a_admin",
                "password": "test123456", 
                "email": "<EMAIL>",
                "full_name": "商户A技术部管理员",
                "role_code": "test_department_admin",
                "merchant_code": "test_merchant_a",
                "department_code": "tech_dept_a"
            },
            {
                "username": "test_normal_user_a",
                "password": "test123456",
                "email": "<EMAIL>", 
                "full_name": "商户A普通用户",
                "role_code": "test_normal_user",
                "merchant_code": "test_merchant_a",
                "department_code": "tech_dept_a"
            }
        ]

    async def setup_test_environment(self):
        """设置测试环境"""
        print("🚀 开始设置测试环境...")
        print("="*80)
        
        # 初始化浏览器环境
        await self.ui_test.setup()
        
        # 管理员登录
        self.admin_token = self.api_test.login("admin", "7c222fb2927d828af22f592134e8932480637c0d")
        if not self.admin_token:
            self.results.append(format_test_result(
                "管理员登录", False, "超级管理员登录失败，无法继续测试"
            ))
            return False
        
        print("✅ 测试环境设置完成")
        return True

    async def cleanup_test_environment(self):
        """清理测试环境"""
        print("\n🧹 开始清理测试环境...")
        
        # 清理测试数据（按创建的逆序删除）
        await self.cleanup_test_users()
        await self.cleanup_test_departments()
        await self.cleanup_test_merchants()
        await self.cleanup_test_roles()
        
        # 清理浏览器环境
        await self.ui_test.cleanup()
        
        print("✅ 测试环境清理完成")

    def step_1_create_roles(self):
        """步骤1: 创建角色"""
        print("\n" + "="*80)
        print("📋 步骤1: 创建测试角色")
        print("="*80)
        
        if not self.admin_token:
            self.results.append(format_test_result(
                "创建角色前置条件", False, "缺少管理员token"
            ))
            return False
        
        success_count = 0
        
        for role_config in self.test_roles:
            print(f"\n创建角色: {role_config['name']}")
            
            # 准备角色数据
            role_data = {
                "name": role_config["name"],
                "code": role_config["code"],
                "description": role_config["description"],
                "is_enabled": True
            }
            
            # 调用创建角色API
            status_code, response = self.api_test.make_request(
                "POST", "/roles", self.admin_token, data=role_data
            )
            
            if status_code == 200 and response.get("success"):
                role_id = response.get("data", {}).get("id")
                if role_id:
                    role_config["id"] = role_id
                    self.test_data["roles"].append(role_config)
                    success_count += 1
                    
                    self.results.append(format_test_result(
                        f"创建角色_{role_config['code']}", True,
                        f"成功创建角色: {role_config['name']} (ID: {role_id})"
                    ))
                    print(f"✅ 成功创建角色: {role_config['name']} (ID: {role_id})")
                else:
                    self.results.append(format_test_result(
                        f"创建角色_{role_config['code']}", False,
                        f"创建角色成功但未返回ID: {response}"
                    ))
                    print(f"❌ 创建角色成功但未返回ID")
            else:
                self.results.append(format_test_result(
                    f"创建角色_{role_config['code']}", False,
                    f"创建角色失败: {response.get('message', '未知错误')} (状态码: {status_code})"
                ))
                print(f"❌ 创建角色失败: {response.get('message', '未知错误')}")
        
        print(f"\n📊 角色创建结果: 成功 {success_count}/{len(self.test_roles)}")
        return success_count > 0

    def step_2_assign_permissions(self):
        """步骤2: 为角色分配权限"""
        print("\n" + "="*80)
        print("🔐 步骤2: 为角色分配权限")
        print("="*80)

        if not self.test_data["roles"]:
            self.results.append(format_test_result(
                "分配权限前置条件", False, "没有可用的测试角色"
            ))
            return False

        # 首先获取系统中所有可用的权限
        status_code, response = self.api_test.make_request(
            "GET", "/permissions?page_size=1000", self.admin_token
        )

        if status_code != 200 or not response.get("success"):
            self.results.append(format_test_result(
                "获取权限列表", False, f"获取权限列表失败: {response.get('message', '未知错误')}"
            ))
            return False

        all_permissions = response.get("data", {}).get("items", [])
        permission_map = {perm["code"]: perm["id"] for perm in all_permissions}

        print(f"📋 系统中共有 {len(all_permissions)} 个权限")

        success_count = 0

        for role_config in self.test_data["roles"]:
            print(f"\n为角色分配权限: {role_config['name']}")

            role_id = role_config.get("id")
            if not role_id:
                continue

            # 处理权限分配
            if role_config["permissions"] == ["all"]:
                # 超级管理员分配所有权限
                permission_ids = list(permission_map.values())
            else:
                # 根据权限代码获取权限ID
                permission_ids = []
                for perm_code in role_config["permissions"]:
                    if perm_code in permission_map:
                        permission_ids.append(permission_map[perm_code])
                    else:
                        print(f"⚠️ 权限代码不存在: {perm_code}")

            if not permission_ids:
                self.results.append(format_test_result(
                    f"分配权限_{role_config['code']}", False,
                    f"没有找到有效的权限ID"
                ))
                continue

            # 调用权限分配API
            permission_data = {"permission_ids": permission_ids}
            status_code, response = self.api_test.make_request(
                "PUT", f"/roles/{role_id}/permissions", self.admin_token, data=permission_data
            )

            if status_code == 200 and response.get("success"):
                success_count += 1
                self.results.append(format_test_result(
                    f"分配权限_{role_config['code']}", True,
                    f"成功为角色 {role_config['name']} 分配 {len(permission_ids)} 个权限"
                ))
                print(f"✅ 成功为角色 {role_config['name']} 分配 {len(permission_ids)} 个权限")
            else:
                self.results.append(format_test_result(
                    f"分配权限_{role_config['code']}", False,
                    f"权限分配失败: {response.get('message', '未知错误')}"
                ))
                print(f"❌ 权限分配失败: {response.get('message', '未知错误')}")

        print(f"\n📊 权限分配结果: 成功 {success_count}/{len(self.test_data['roles'])}")
        return success_count > 0

    def step_3_create_merchants(self):
        """步骤3: 创建商户"""
        print("\n" + "="*80)
        print("🏢 步骤3: 创建测试商户")
        print("="*80)

        success_count = 0

        for merchant_config in self.test_merchants:
            print(f"\n创建商户: {merchant_config['name']}")

            # 准备商户数据
            merchant_data = {
                "name": merchant_config["name"],
                "code": merchant_config["code"],
                "api_key": merchant_config["api_key"],
                "api_secret": merchant_config["api_secret"],
                "status": merchant_config["status"],
                "daily_limit": merchant_config["daily_limit"],
                "hourly_limit": merchant_config["hourly_limit"],
                "concurrency_limit": 10,
                "priority": 0,
                "request_timeout": 30,
                "retry_count": 3
            }

            # 调用创建商户API
            status_code, response = self.api_test.make_request(
                "POST", "/merchants", self.admin_token, data=merchant_data
            )

            if status_code == 200 and response.get("success"):
                merchant_id = response.get("data", {}).get("id")
                if merchant_id:
                    merchant_config["id"] = merchant_id
                    self.test_data["merchants"].append(merchant_config)
                    success_count += 1

                    self.results.append(format_test_result(
                        f"创建商户_{merchant_config['code']}", True,
                        f"成功创建商户: {merchant_config['name']} (ID: {merchant_id})"
                    ))
                    print(f"✅ 成功创建商户: {merchant_config['name']} (ID: {merchant_id})")
                else:
                    self.results.append(format_test_result(
                        f"创建商户_{merchant_config['code']}", False,
                        f"创建商户成功但未返回ID: {response}"
                    ))
                    print(f"❌ 创建商户成功但未返回ID")
            else:
                self.results.append(format_test_result(
                    f"创建商户_{merchant_config['code']}", False,
                    f"创建商户失败: {response.get('message', '未知错误')} (状态码: {status_code})"
                ))
                print(f"❌ 创建商户失败: {response.get('message', '未知错误')}")

        print(f"\n📊 商户创建结果: 成功 {success_count}/{len(self.test_merchants)}")
        return success_count > 0

    def step_4_create_departments(self):
        """步骤4: 创建部门"""
        print("\n" + "="*80)
        print("🏛️ 步骤4: 创建测试部门")
        print("="*80)

        if not self.test_data["merchants"]:
            self.results.append(format_test_result(
                "创建部门前置条件", False, "没有可用的测试商户"
            ))
            return False

        # 创建商户代码到ID的映射
        merchant_map = {m["code"]: m["id"] for m in self.test_data["merchants"]}

        success_count = 0

        for dept_config in self.test_departments:
            print(f"\n创建部门: {dept_config['name']}")

            # 根据部门名称确定所属商户
            if "商户A" in dept_config["name"]:
                merchant_id = merchant_map.get("test_merchant_a")
            elif "商户B" in dept_config["name"]:
                merchant_id = merchant_map.get("test_merchant_b")
            else:
                merchant_id = None

            if not merchant_id:
                self.results.append(format_test_result(
                    f"创建部门_{dept_config['code']}", False,
                    f"无法确定部门所属商户"
                ))
                continue

            # 准备部门数据
            dept_data = {
                "name": dept_config["name"],
                "code": dept_config["code"],
                "description": dept_config["description"],
                "merchant_id": merchant_id,
                "is_enabled": True
            }

            # 调用创建部门API
            status_code, response = self.api_test.make_request(
                "POST", "/departments", self.admin_token, data=dept_data
            )

            if status_code == 200 and response.get("success"):
                dept_id = response.get("data", {}).get("id")
                if dept_id:
                    dept_config["id"] = dept_id
                    dept_config["merchant_id"] = merchant_id
                    self.test_data["departments"].append(dept_config)
                    success_count += 1

                    self.results.append(format_test_result(
                        f"创建部门_{dept_config['code']}", True,
                        f"成功创建部门: {dept_config['name']} (ID: {dept_id})"
                    ))
                    print(f"✅ 成功创建部门: {dept_config['name']} (ID: {dept_id})")
                else:
                    self.results.append(format_test_result(
                        f"创建部门_{dept_config['code']}", False,
                        f"创建部门成功但未返回ID: {response}"
                    ))
                    print(f"❌ 创建部门成功但未返回ID")
            else:
                self.results.append(format_test_result(
                    f"创建部门_{dept_config['code']}", False,
                    f"创建部门失败: {response.get('message', '未知错误')} (状态码: {status_code})"
                ))
                print(f"❌ 创建部门失败: {response.get('message', '未知错误')}")

        print(f"\n📊 部门创建结果: 成功 {success_count}/{len(self.test_departments)}")
        return success_count > 0

    def step_5_create_users(self):
        """步骤5: 创建用户并分配权限"""
        print("\n" + "="*80)
        print("👥 步骤5: 创建测试用户并分配权限")
        print("="*80)

        if not self.test_data["roles"] or not self.test_data["merchants"]:
            self.results.append(format_test_result(
                "创建用户前置条件", False, "缺少必要的角色或商户数据"
            ))
            return False

        # 创建映射表
        role_map = {r["code"]: r["id"] for r in self.test_data["roles"]}
        merchant_map = {m["code"]: m["id"] for m in self.test_data["merchants"]}
        dept_map = {d["code"]: d["id"] for d in self.test_data["departments"]}

        success_count = 0

        for user_config in self.test_users:
            print(f"\n创建用户: {user_config['username']}")

            # 获取角色ID
            role_id = role_map.get(user_config["role_code"])
            if not role_id:
                self.results.append(format_test_result(
                    f"创建用户_{user_config['username']}", False,
                    f"找不到角色: {user_config['role_code']}"
                ))
                continue

            # 获取商户ID
            merchant_id = merchant_map.get(user_config["merchant_code"])
            if not merchant_id:
                self.results.append(format_test_result(
                    f"创建用户_{user_config['username']}", False,
                    f"找不到商户: {user_config['merchant_code']}"
                ))
                continue

            # 获取部门ID（可选）
            department_id = None
            if user_config["department_code"]:
                department_id = dept_map.get(user_config["department_code"])

            # 准备用户数据
            user_data = {
                "username": user_config["username"],
                "password": user_config["password"],
                "email": user_config["email"],
                "full_name": user_config["full_name"],
                "merchant_id": merchant_id,
                "department_id": department_id,
                "is_active": True,
                "role_ids": [role_id]  # 分配角色
            }

            # 调用创建用户API
            status_code, response = self.api_test.make_request(
                "POST", "/users", self.admin_token, data=user_data
            )

            if status_code == 200 and response.get("success"):
                user_id = response.get("data", {}).get("id")
                if user_id:
                    user_config["id"] = user_id
                    user_config["merchant_id"] = merchant_id
                    user_config["department_id"] = department_id
                    user_config["role_id"] = role_id
                    self.test_data["users"].append(user_config)
                    success_count += 1

                    self.results.append(format_test_result(
                        f"创建用户_{user_config['username']}", True,
                        f"成功创建用户: {user_config['username']} (ID: {user_id})"
                    ))
                    print(f"✅ 成功创建用户: {user_config['username']} (ID: {user_id})")
                else:
                    self.results.append(format_test_result(
                        f"创建用户_{user_config['username']}", False,
                        f"创建用户成功但未返回ID: {response}"
                    ))
                    print(f"❌ 创建用户成功但未返回ID")
            else:
                self.results.append(format_test_result(
                    f"创建用户_{user_config['username']}", False,
                    f"创建用户失败: {response.get('message', '未知错误')} (状态码: {status_code})"
                ))
                print(f"❌ 创建用户失败: {response.get('message', '未知错误')}")

        print(f"\n📊 用户创建结果: 成功 {success_count}/{len(self.test_users)}")
        return success_count > 0

    def step_6_login_verification(self):
        """步骤6: 用户登录验证"""
        print("\n" + "="*80)
        print("🔑 步骤6: 用户登录验证")
        print("="*80)

        if not self.test_data["users"]:
            self.results.append(format_test_result(
                "登录验证前置条件", False, "没有可用的测试用户"
            ))
            return False

        success_count = 0

        for user_config in self.test_data["users"]:
            print(f"\n测试用户登录: {user_config['username']}")

            # 尝试登录
            token = self.api_test.login(user_config["username"], user_config["password"])

            if token:
                user_config["token"] = token
                success_count += 1

                # 验证用户信息
                status_code, response = self.api_test.make_request(
                    "GET", "/auth/me", token
                )

                if status_code == 200:
                    user_info = response.get("data", response)
                    self.results.append(format_test_result(
                        f"登录验证_{user_config['username']}", True,
                        f"用户 {user_config['username']} 登录成功，获取用户信息正常"
                    ))
                    print(f"✅ 用户 {user_config['username']} 登录成功")
                else:
                    self.results.append(format_test_result(
                        f"登录验证_{user_config['username']}", False,
                        f"用户 {user_config['username']} 登录成功但获取用户信息失败"
                    ))
                    print(f"⚠️ 用户 {user_config['username']} 登录成功但获取用户信息失败")
            else:
                self.results.append(format_test_result(
                    f"登录验证_{user_config['username']}", False,
                    f"用户 {user_config['username']} 登录失败"
                ))
                print(f"❌ 用户 {user_config['username']} 登录失败")

        print(f"\n📊 登录验证结果: 成功 {success_count}/{len(self.test_data['users'])}")
        return success_count > 0

    async def step_7_ui_permission_verification(self):
        """步骤7: UI权限验证测试"""
        print("\n" + "="*80)
        print("🖥️ 步骤7: UI权限验证测试")
        print("="*80)

        if not self.test_data["users"]:
            self.results.append(format_test_result(
                "UI权限验证前置条件", False, "没有可用的测试用户"
            ))
            return False

        success_count = 0

        # 测试不同角色的用户登录前端系统
        for user_config in self.test_data["users"]:
            print(f"\n测试用户前端登录: {user_config['username']}")

            try:
                # 导航到登录页面
                await self.ui_test.page.goto(f"{self.ui_test.base_url}/#/login")
                await self.ui_test.page.wait_for_load_state('networkidle')

                # 填写登录表单
                await self.ui_test.page.fill('input[placeholder*="用户名"]', user_config["username"])
                await self.ui_test.page.fill('input[placeholder*="密码"]', user_config["password"])

                # 点击登录按钮
                await self.ui_test.page.click('button:has-text("登录")')

                # 等待登录结果
                try:
                    await self.ui_test.page.wait_for_url("**/dashboard", timeout=10000)
                    await self.ui_test.page.wait_for_load_state('networkidle')

                    success_count += 1
                    self.results.append(format_test_result(
                        f"UI登录_{user_config['username']}", True,
                        f"用户 {user_config['username']} 前端登录成功"
                    ))
                    print(f"✅ 用户 {user_config['username']} 前端登录成功")

                    # 测试菜单权限
                    await self.test_menu_permissions(user_config)

                    # 退出登录
                    await self.ui_test.logout()

                except Exception as e:
                    self.results.append(format_test_result(
                        f"UI登录_{user_config['username']}", False,
                        f"用户 {user_config['username']} 前端登录失败: {str(e)}"
                    ))
                    print(f"❌ 用户 {user_config['username']} 前端登录失败: {str(e)}")

            except Exception as e:
                self.results.append(format_test_result(
                    f"UI登录_{user_config['username']}", False,
                    f"UI测试异常: {str(e)}"
                ))
                print(f"❌ UI测试异常: {str(e)}")

        print(f"\n📊 UI权限验证结果: 成功 {success_count}/{len(self.test_data['users'])}")
        return success_count > 0

    async def test_menu_permissions(self, user_config):
        """测试菜单权限"""
        print(f"  测试 {user_config['username']} 的菜单权限...")

        # 根据角色测试不同的菜单访问权限
        role_code = user_config["role_code"]

        if role_code == "test_merchant_admin":
            # 商户管理员应该能访问的菜单
            expected_menus = [
                ("仪表盘", "dashboard"),
                ("用户管理", "system/user"),
                ("部门管理", "merchant/department")
            ]

            # 不应该能访问的菜单
            forbidden_menus = [
                ("角色管理", "system/role"),
                ("菜单管理", "system/menu")
            ]

        elif role_code == "test_department_admin":
            # 部门管理员应该能访问的菜单
            expected_menus = [
                ("仪表盘", "dashboard"),
                ("用户管理", "system/user")
            ]

            # 不应该能访问的菜单
            forbidden_menus = [
                ("部门管理", "merchant/department"),
                ("角色管理", "system/role")
            ]

        elif role_code == "test_normal_user":
            # 普通用户应该能访问的菜单
            expected_menus = [
                ("仪表盘", "dashboard")
            ]

            # 不应该能访问的菜单
            forbidden_menus = [
                ("用户管理", "system/user"),
                ("部门管理", "merchant/department")
            ]
        else:
            return

        # 测试可访问的菜单
        for menu_name, menu_path in expected_menus:
            try:
                await self.ui_test.page.goto(f"{self.ui_test.base_url}/#/{menu_path}")
                await self.ui_test.page.wait_for_load_state('networkidle', timeout=5000)

                # 检查是否成功加载页面（没有跳转到403页面）
                current_url = self.ui_test.page.url
                if "403" not in current_url and "login" not in current_url:
                    self.results.append(format_test_result(
                        f"菜单权限_{user_config['username']}_{menu_path}", True,
                        f"用户 {user_config['username']} 可以正常访问 {menu_name}"
                    ))
                    print(f"    ✅ 可以访问 {menu_name}")
                else:
                    self.results.append(format_test_result(
                        f"菜单权限_{user_config['username']}_{menu_path}", False,
                        f"用户 {user_config['username']} 无法访问 {menu_name}，被重定向到: {current_url}"
                    ))
                    print(f"    ❌ 无法访问 {menu_name}")

            except Exception as e:
                self.results.append(format_test_result(
                    f"菜单权限_{user_config['username']}_{menu_path}", False,
                    f"测试菜单 {menu_name} 时发生异常: {str(e)}"
                ))
                print(f"    ❌ 测试菜单 {menu_name} 时发生异常")

        # 测试不可访问的菜单
        for menu_name, menu_path in forbidden_menus:
            try:
                await self.ui_test.page.goto(f"{self.ui_test.base_url}/#/{menu_path}")
                await self.ui_test.page.wait_for_load_state('networkidle', timeout=5000)

                # 检查是否被正确拒绝访问
                current_url = self.ui_test.page.url
                if "403" in current_url or "login" in current_url:
                    self.results.append(format_test_result(
                        f"菜单权限拒绝_{user_config['username']}_{menu_path}", True,
                        f"用户 {user_config['username']} 正确被拒绝访问 {menu_name}"
                    ))
                    print(f"    ✅ 正确拒绝访问 {menu_name}")
                else:
                    self.results.append(format_test_result(
                        f"菜单权限拒绝_{user_config['username']}_{menu_path}", False,
                        f"用户 {user_config['username']} 不应该能访问 {menu_name}，但实际可以访问"
                    ))
                    print(f"    ❌ 权限控制失效，不应该能访问 {menu_name}")

            except Exception as e:
                # 如果访问被拒绝导致异常，这可能是正常的
                self.results.append(format_test_result(
                    f"菜单权限拒绝_{user_config['username']}_{menu_path}", True,
                    f"用户 {user_config['username']} 访问 {menu_name} 被正确拒绝"
                ))
                print(f"    ✅ 访问 {menu_name} 被正确拒绝")

    def step_8_data_isolation_verification(self):
        """步骤8: 数据隔离验证测试"""
        print("\n" + "="*80)
        print("🔒 步骤8: 数据隔离验证测试")
        print("="*80)

        if not self.test_data["users"]:
            self.results.append(format_test_result(
                "数据隔离验证前置条件", False, "没有可用的测试用户"
            ))
            return False

        success_count = 0

        # 测试商户间数据隔离
        merchant_a_users = [u for u in self.test_data["users"] if u["merchant_code"] == "test_merchant_a"]
        merchant_b_users = [u for u in self.test_data["users"] if u["merchant_code"] == "test_merchant_b"]

        # 测试商户A用户是否只能看到商户A的数据
        for user_config in merchant_a_users:
            if not user_config.get("token"):
                continue

            print(f"\n测试 {user_config['username']} 的数据隔离...")

            # 测试用户列表数据隔离
            status_code, response = self.api_test.make_request(
                "GET", "/users", user_config["token"]
            )

            if status_code == 200:
                users_data = response.get("data", {})
                if isinstance(users_data, dict):
                    users = users_data.get("items", [])
                else:
                    users = users_data if isinstance(users_data, list) else []

                # 检查返回的用户是否都属于同一商户
                merchant_ids = set()
                for user in users:
                    if user.get("merchant_id"):
                        merchant_ids.add(user["merchant_id"])

                if len(merchant_ids) <= 1:
                    success_count += 1
                    self.results.append(format_test_result(
                        f"用户数据隔离_{user_config['username']}", True,
                        f"用户 {user_config['username']} 只能看到本商户的 {len(users)} 个用户"
                    ))
                    print(f"  ✅ 用户数据隔离正常，只能看到本商户的 {len(users)} 个用户")
                else:
                    self.results.append(format_test_result(
                        f"用户数据隔离_{user_config['username']}", False,
                        f"用户 {user_config['username']} 可以看到多个商户的用户数据，存在数据泄露"
                    ))
                    print(f"  ❌ 数据隔离失效，可以看到多个商户的用户数据")
            elif status_code in [401, 403]:
                success_count += 1
                self.results.append(format_test_result(
                    f"用户数据隔离_{user_config['username']}", True,
                    f"用户 {user_config['username']} 正确被拒绝访问用户列表"
                ))
                print(f"  ✅ 正确被拒绝访问用户列表")
            else:
                self.results.append(format_test_result(
                    f"用户数据隔离_{user_config['username']}", False,
                    f"用户数据隔离测试失败，状态码: {status_code}"
                ))
                print(f"  ❌ 用户数据隔离测试失败，状态码: {status_code}")

        print(f"\n📊 数据隔离验证结果: 成功 {success_count} 项测试")
        return success_count > 0

    async def cleanup_test_users(self):
        """清理测试用户"""
        print("清理测试用户...")

        for user_config in self.test_data["users"]:
            if user_config.get("id"):
                status_code, response = self.api_test.make_request(
                    "DELETE", f"/users/{user_config['id']}", self.admin_token
                )
                if status_code == 200:
                    print(f"  ✅ 删除用户: {user_config['username']}")
                else:
                    print(f"  ⚠️ 删除用户失败: {user_config['username']}")

    async def cleanup_test_departments(self):
        """清理测试部门"""
        print("清理测试部门...")

        for dept_config in self.test_data["departments"]:
            if dept_config.get("id"):
                status_code, response = self.api_test.make_request(
                    "DELETE", f"/departments/{dept_config['id']}", self.admin_token
                )
                if status_code == 200:
                    print(f"  ✅ 删除部门: {dept_config['name']}")
                else:
                    print(f"  ⚠️ 删除部门失败: {dept_config['name']}")

    async def cleanup_test_merchants(self):
        """清理测试商户"""
        print("清理测试商户...")

        for merchant_config in self.test_data["merchants"]:
            if merchant_config.get("id"):
                status_code, response = self.api_test.make_request(
                    "DELETE", f"/merchants/{merchant_config['id']}", self.admin_token
                )
                if status_code == 200:
                    print(f"  ✅ 删除商户: {merchant_config['name']}")
                else:
                    print(f"  ⚠️ 删除商户失败: {merchant_config['name']}")

    async def cleanup_test_roles(self):
        """清理测试角色"""
        print("清理测试角色...")

        for role_config in self.test_data["roles"]:
            if role_config.get("id"):
                status_code, response = self.api_test.make_request(
                    "DELETE", f"/roles/{role_config['id']}", self.admin_token
                )
                if status_code == 200:
                    print(f"  ✅ 删除角色: {role_config['name']}")
                else:
                    print(f"  ⚠️ 删除角色失败: {role_config['name']}")

    async def run_comprehensive_test(self):
        """运行综合测试"""
        print("🎯 开始运行沃尔玛绑卡系统综合权限控制和数据隔离测试")
        print("="*100)

        start_time = time.time()

        try:
            # 设置测试环境
            if not await self.setup_test_environment():
                print("❌ 测试环境设置失败，终止测试")
                return self.results

            # 按照严格顺序执行测试步骤
            print("\n🔄 开始执行测试步骤...")

            # 步骤1: 创建角色
            if not self.step_1_create_roles():
                print("❌ 创建角色失败，终止测试")
                return self.results

            # 步骤2: 分配权限
            if not self.step_2_assign_permissions():
                print("❌ 分配权限失败，终止测试")
                return self.results

            # 步骤3: 创建商户
            if not self.step_3_create_merchants():
                print("❌ 创建商户失败，终止测试")
                return self.results

            # 步骤4: 创建部门
            if not self.step_4_create_departments():
                print("❌ 创建部门失败，终止测试")
                return self.results

            # 步骤5: 创建用户并分配权限
            if not self.step_5_create_users():
                print("❌ 创建用户失败，终止测试")
                return self.results

            # 步骤6: 用户登录验证
            if not self.step_6_login_verification():
                print("❌ 用户登录验证失败，终止测试")
                return self.results

            # 步骤7: UI权限验证测试
            if not await self.step_7_ui_permission_verification():
                print("⚠️ UI权限验证测试失败，但继续执行后续测试")

            # 步骤8: 数据隔离验证测试
            if not self.step_8_data_isolation_verification():
                print("⚠️ 数据隔离验证测试失败")

            end_time = time.time()
            duration = end_time - start_time

            print(f"\n🎉 综合测试执行完成，耗时: {duration:.2f}秒")

        except Exception as e:
            print(f"❌ 测试执行过程中发生异常: {str(e)}")
            self.results.append(format_test_result(
                "测试执行异常", False, f"测试执行过程中发生异常: {str(e)}"
            ))

        finally:
            # 清理测试环境
            await self.cleanup_test_environment()

        return self.results

    def generate_test_report(self):
        """生成测试报告"""
        print("\n" + "="*100)
        print("📊 综合权限控制和数据隔离测试报告")
        print("="*100)

        # 统计测试结果
        total_tests = len(self.results)
        passed_tests = sum(1 for r in self.results if r.get("success", False))
        failed_tests = total_tests - passed_tests
        success_rate = (passed_tests / total_tests * 100) if total_tests > 0 else 0

        print(f"总测试数: {total_tests}")
        print(f"通过数: {passed_tests}")
        print(f"失败数: {failed_tests}")
        print(f"成功率: {success_rate:.1f}%")

        # 按测试类型分组显示结果
        test_categories = {
            "角色创建": [],
            "权限分配": [],
            "商户创建": [],
            "部门创建": [],
            "用户创建": [],
            "登录验证": [],
            "UI权限验证": [],
            "菜单权限": [],
            "数据隔离": [],
            "其他": []
        }

        for result in self.results:
            test_name = result["test_name"]
            categorized = False

            for category in test_categories.keys():
                if any(keyword in test_name for keyword in [
                    "创建角色", "分配权限", "创建商户", "创建部门", "创建用户",
                    "登录验证", "UI登录", "菜单权限", "数据隔离"
                ]):
                    if "创建角色" in test_name:
                        test_categories["角色创建"].append(result)
                    elif "分配权限" in test_name:
                        test_categories["权限分配"].append(result)
                    elif "创建商户" in test_name:
                        test_categories["商户创建"].append(result)
                    elif "创建部门" in test_name:
                        test_categories["部门创建"].append(result)
                    elif "创建用户" in test_name:
                        test_categories["用户创建"].append(result)
                    elif "登录验证" in test_name:
                        test_categories["登录验证"].append(result)
                    elif "UI登录" in test_name:
                        test_categories["UI权限验证"].append(result)
                    elif "菜单权限" in test_name:
                        test_categories["菜单权限"].append(result)
                    elif "数据隔离" in test_name:
                        test_categories["数据隔离"].append(result)
                    categorized = True
                    break

            if not categorized:
                test_categories["其他"].append(result)

        # 显示各类别的测试结果
        for category, results in test_categories.items():
            if results:
                print(f"\n📋 {category} ({len(results)} 项测试):")
                for result in results:
                    status = "✅ 通过" if result["success"] else "❌ 失败"
                    print(f"  {status} {result['test_name']}: {result['message']}")

        # 保存测试报告
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        report_filename = f"comprehensive_permission_test_report_{timestamp}.json"
        report_path = save_test_report(self.results, report_filename)

        print(f"\n📄 详细测试报告已保存至: {report_path}")

        return {
            "total": total_tests,
            "passed": passed_tests,
            "failed": failed_tests,
            "success_rate": success_rate,
            "report_path": report_path
        }


async def main():
    """主函数"""
    print("🚀 启动沃尔玛绑卡系统综合权限控制和数据隔离测试")
    print("="*100)
    print("测试目标:")
    print("1. 验证角色创建和权限分配功能")
    print("2. 验证商户和部门管理功能")
    print("3. 验证用户创建和角色分配功能")
    print("4. 验证用户登录和身份认证功能")
    print("5. 验证前端菜单权限控制功能")
    print("6. 验证API接口权限控制功能")
    print("7. 验证数据隔离和访问控制功能")
    print("="*100)

    # 创建测试实例
    test_suite = ComprehensivePermissionIsolationTest()

    try:
        # 运行综合测试
        results = await test_suite.run_comprehensive_test()

        # 生成测试报告
        report_summary = test_suite.generate_test_report()

        # 判断测试是否通过
        if report_summary["failed"] == 0:
            print("\n🎉 所有测试通过！系统权限控制和数据隔离功能正常")
            return 0
        else:
            print(f"\n⚠️ 发现 {report_summary['failed']} 个问题需要修复")
            print("请查看详细测试报告了解具体问题")
            return 1

    except Exception as e:
        print(f"❌ 测试执行失败: {str(e)}")
        return 1


if __name__ == "__main__":
    # 运行异步测试
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
