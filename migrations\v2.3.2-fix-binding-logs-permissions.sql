-- ========================================
-- 修复绑卡日志权限验证问题
-- 描述: 确保绑卡日志相关权限接口存在并正确分配给角色
-- ========================================

USE `walmart_card_db`;

-- ========================================
-- 1. 记录迁移开始
-- ========================================
INSERT INTO `migration_logs` (`migration_name`, `status`, `message`, `created_at`) VALUES
('binding_logs_permissions_fix_v2.3.2', 'started', '开始修复绑卡日志权限验证问题', NOW(3));

-- ========================================
-- 2. 确保绑卡日志相关权限接口存在
-- ========================================

-- 插入绑卡日志模块权限（如果不存在）
INSERT IGNORE INTO `permissions` (`code`, `name`, `description`, `resource_type`, `resource_path`, `is_enabled`, `sort_order`, `created_at`, `updated_at`) VALUES
('api:/api/v1/binding-logs', '绑卡日志API模块', '绑卡日志相关API模块权限', 'api', '/api/v1/binding-logs', 1, 320, NOW(3), NOW(3));

-- 插入绑卡日志具体操作权限（如果不存在）
INSERT IGNORE INTO `permissions` (`code`, `name`, `description`, `resource_type`, `resource_path`, `is_enabled`, `sort_order`, `created_at`, `updated_at`) VALUES
('api:binding-logs:read', '查看绑卡日志', '查看绑卡日志权限', 'api', '/api/v1/binding-logs', 1, 321, NOW(3), NOW(3)),
('api:binding-logs:create', '创建绑卡日志', '创建绑卡日志权限', 'api', '/api/v1/binding-logs', 1, 322, NOW(3), NOW(3)),
('api:binding-logs:update', '更新绑卡日志', '更新绑卡日志权限', 'api', '/api/v1/binding-logs', 1, 323, NOW(3), NOW(3)),
('api:binding-logs:delete', '删除绑卡日志', '删除绑卡日志权限', 'api', '/api/v1/binding-logs', 1, 324, NOW(3), NOW(3)),
('api:binding-logs:export', '导出绑卡日志', '导出绑卡日志权限', 'api', '/api/v1/binding-logs/export', 1, 325, NOW(3), NOW(3));

-- ========================================
-- 3. 为超级管理员分配所有绑卡日志权限
-- ========================================

-- 获取超级管理员角色ID
SET @super_admin_role_id = (SELECT id FROM `roles` WHERE code = 'super_admin' LIMIT 1);

-- 为超级管理员分配绑卡日志相关权限
INSERT IGNORE INTO `role_permissions` (`role_id`, `permission_id`, `created_at`)
SELECT @super_admin_role_id, p.id, NOW(3)
FROM `permissions` p
WHERE p.code IN (
    'api:/api/v1/binding-logs',
    'api:binding-logs:read',
    'api:binding-logs:create',
    'api:binding-logs:update',
    'api:binding-logs:delete',
    'api:binding-logs:export'
) AND @super_admin_role_id IS NOT NULL;

-- ========================================
-- 4. 为商户管理员分配绑卡日志查看权限
-- ========================================

-- 获取商户管理员角色ID
SET @merchant_admin_role_id = (SELECT id FROM `roles` WHERE code = 'merchant_admin' LIMIT 1);

-- 为商户管理员分配绑卡日志查看权限
INSERT IGNORE INTO `role_permissions` (`role_id`, `permission_id`, `created_at`)
SELECT @merchant_admin_role_id, p.id, NOW(3)
FROM `permissions` p
WHERE p.code IN (
    'api:/api/v1/binding-logs',
    'api:binding-logs:read'
) AND @merchant_admin_role_id IS NOT NULL;

-- ========================================
-- 5. 验证权限配置
-- ========================================

-- 检查权限是否正确插入
SELECT 
    '绑卡日志权限检查' as check_type,
    COUNT(*) as permission_count,
    CASE 
        WHEN COUNT(*) >= 6 THEN '✓ 绑卡日志权限配置完整'
        ELSE '⚠ 绑卡日志权限配置不完整'
    END as status
FROM `permissions` 
WHERE code IN (
    'api:/api/v1/binding-logs',
    'api:binding-logs:read',
    'api:binding-logs:create',
    'api:binding-logs:update',
    'api:binding-logs:delete',
    'api:binding-logs:export'
);

-- 检查超级管理员权限分配
SELECT 
    '超级管理员绑卡日志权限' as check_type,
    COUNT(*) as assigned_permissions,
    CASE 
        WHEN COUNT(*) >= 6 THEN '✓ 超级管理员权限分配完整'
        ELSE '⚠ 超级管理员权限分配不完整'
    END as status
FROM `role_permissions` rp
JOIN `roles` r ON rp.role_id = r.id
JOIN `permissions` p ON rp.permission_id = p.id
WHERE r.code = 'super_admin' 
AND p.code IN (
    'api:/api/v1/binding-logs',
    'api:binding-logs:read',
    'api:binding-logs:create',
    'api:binding-logs:update',
    'api:binding-logs:delete',
    'api:binding-logs:export'
);

-- 检查商户管理员权限分配
SELECT 
    '商户管理员绑卡日志权限' as check_type,
    COUNT(*) as assigned_permissions,
    CASE 
        WHEN COUNT(*) >= 2 THEN '✓ 商户管理员权限分配完整'
        ELSE '⚠ 商户管理员权限分配不完整'
    END as status
FROM `role_permissions` rp
JOIN `roles` r ON rp.role_id = r.id
JOIN `permissions` p ON rp.permission_id = p.id
WHERE r.code = 'merchant_admin' 
AND p.code IN (
    'api:/api/v1/binding-logs',
    'api:binding-logs:read'
);

-- ========================================
-- 6. 记录迁移完成
-- ========================================
UPDATE `migration_logs`
SET
    `status` = 'completed',
    `message` = '绑卡日志权限验证问题修复完成 - 权限接口已添加并分配给相应角色',
    `completed_at` = NOW(3),
    `data_summary` = JSON_OBJECT(
        'permissions_added', (SELECT COUNT(*) FROM `permissions` WHERE code LIKE '%binding-logs%'),
        'super_admin_permissions', (
            SELECT COUNT(*) FROM `role_permissions` rp
            JOIN `roles` r ON rp.role_id = r.id
            JOIN `permissions` p ON rp.permission_id = p.id
            WHERE r.code = 'super_admin' AND p.code LIKE '%binding-logs%'
        ),
        'merchant_admin_permissions', (
            SELECT COUNT(*) FROM `role_permissions` rp
            JOIN `roles` r ON rp.role_id = r.id
            JOIN `permissions` p ON rp.permission_id = p.id
            WHERE r.code = 'merchant_admin' AND p.code LIKE '%binding-logs%'
        ),
        'fix_timestamp', NOW(3)
    )
WHERE `migration_name` = 'binding_logs_permissions_fix_v2.3.2';

-- ========================================
-- 7. 显示修复结果摘要
-- ========================================
SELECT 
    '=== 绑卡日志权限修复完成 ===' as summary,
    (SELECT COUNT(*) FROM `permissions` WHERE code LIKE '%binding-logs%') as total_permissions,
    (SELECT COUNT(*) FROM `role_permissions` rp
     JOIN `roles` r ON rp.role_id = r.id
     JOIN `permissions` p ON rp.permission_id = p.id
     WHERE r.code = 'super_admin' AND p.code LIKE '%binding-logs%') as super_admin_permissions,
    (SELECT COUNT(*) FROM `role_permissions` rp
     JOIN `roles` r ON rp.role_id = r.id
     JOIN `permissions` p ON rp.permission_id = p.id
     WHERE r.code = 'merchant_admin' AND p.code LIKE '%binding-logs%') as merchant_admin_permissions;

