package model

import (
	"context"
	"encoding/json"
	"time"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

// CardRecord 卡记录模型 - 与Python系统数据库表结构保持一致
type CardRecord struct {
	ID                string    `gorm:"column:id;primaryKey;size:36" json:"id"`                                    // UUID主键
	MerchantID        int64     `gorm:"column:merchant_id;not null;index" json:"merchant_id"`                      // 商户ID
	DepartmentID      *int64    `gorm:"column:department_id;index" json:"department_id"`                           // 部门ID
	WalmartCKID       *int64    `gorm:"column:walmart_ck_id;index" json:"walmart_ck_id"`                           // 沃尔玛CK ID
	MerchantOrderID   string    `gorm:"column:merchant_order_id;size:255;not null" json:"merchant_order_id"`       // 商户订单号
	Amount            int       `gorm:"column:amount;not null" json:"amount"`                                      // 金额（分）
	ActualAmount      *int      `gorm:"column:actual_amount" json:"actual_amount"`                                 // 实际金额（分）
	Balance           *string   `gorm:"column:balance;size:32" json:"balance"`                                     // balance字段
	CardBalance       *string   `gorm:"column:cardBalance;size:32" json:"cardBalance"`                             // cardBalance字段
	BalanceCnt        *string   `gorm:"column:balanceCnt;size:32" json:"balanceCnt"`                               // balanceCnt字段
	CardNumber        string    `gorm:"column:card_number;size:50;not null" json:"card_number"`                    // 卡号
	CardPassword      *string   `gorm:"column:card_password;size:512" json:"card_password"`                        // 卡密
	Status            string    `gorm:"column:status;size:20;not null;default:'pending';index" json:"status"`      // 状态
	RequestID         string    `gorm:"column:request_id;size:64;not null;uniqueIndex" json:"request_id"`          // 请求ID
	TraceID           *string   `gorm:"column:trace_id;size:64" json:"trace_id"`                                   // 追踪ID
	RequestData       string    `gorm:"column:request_data;type:json;not null" json:"request_data"`                // 请求数据
	ResponseData      *string   `gorm:"column:response_data;type:json" json:"response_data"`                       // 响应数据
	ErrorMessage      *string   `gorm:"column:error_message;size:500" json:"error_message"`                        // 错误信息
	RetryCount        int       `gorm:"column:retry_count;not null;default:0" json:"retry_count"`                  // 重试次数
	ProcessTime       *float64  `gorm:"column:process_time" json:"process_time"`                                   // 处理时间
	IPAddress         *string   `gorm:"column:ip_address;size:64" json:"ip_address"`                               // IP地址
	Remark            *string   `gorm:"column:remark;size:500" json:"remark"`                                      // 备注
	ExtData           *string   `gorm:"column:ext_data;size:512" json:"ext_data"`                                  // 扩展数据
	IsTestMode        bool      `gorm:"column:is_test_mode;not null;default:false" json:"is_test_mode"`            // 测试模式
	CallbackStatus    string    `gorm:"column:callback_status;size:20;not null;default:'pending'" json:"callback_status"` // 回调状态
	CallbackResult    *string   `gorm:"column:callback_result;size:500" json:"callback_result"`                    // 回调结果
	CallbackTime      *time.Time `gorm:"column:callback_time" json:"callback_time"`                                // 回调时间
	CreatedAt         time.Time `gorm:"column:created_at;not null;index" json:"created_at"`                        // 创建时间
	UpdatedAt         time.Time `gorm:"column:updated_at;not null" json:"updated_at"`                              // 更新时间
}

// TableName 指定表名
func (CardRecord) TableName() string {
	return "card_records"
}

// BeforeCreate GORM钩子 - 在创建记录前执行
func (c *CardRecord) BeforeCreate(tx *gorm.DB) error {
	// 如果ID为空，生成UUID
	if c.ID == "" {
		c.ID = uuid.New().String()
	}

	// 设置创建和更新时间
	now := time.Now()
	c.CreatedAt = now
	c.UpdatedAt = now

	return nil
}

// BindCardRequest 绑卡请求模型 - 与Python系统完全兼容
type BindCardRequest struct {
	CardNumber      string  `json:"card_number" binding:"required" validate:"min=1,max=50"`
	CardPassword    string  `json:"card_password" binding:"required" validate:"min=1,max=50"`
	MerchantCode    string  `json:"merchant_code" binding:"required" validate:"min=1,max=50"`
	MerchantOrderID string  `json:"merchant_order_id" binding:"required" validate:"min=1,max=255"`
	Amount          int     `json:"amount" binding:"required" validate:"min=100"` // 单位：分，最小1元
	ExtData         *string `json:"ext_data,omitempty" validate:"max=512"`        // 扩展数据，回调时原样返回
	Debug           *bool   `json:"debug,omitempty"`                              // 调试模式
}

// InternalBindRequest 内部绑卡请求模型
type InternalBindRequest struct {
	RequestID       string            `json:"request_id"`
	CardNumber      string            `json:"card_number"`
	CardPassword    string            `json:"card_password"`
	MerchantCode    string            `json:"merchant_code"`
	MerchantOrderID string            `json:"merchant_order_id"`
	Amount          float64           `json:"amount"`
	ClientIP        string            `json:"client_ip"`
	UserAgent       string            `json:"user_agent"`
	CallbackURL     string            `json:"callback_url,omitempty"`
	ExtData         string            `json:"ext_data,omitempty"`
	Headers         map[string]string `json:"headers,omitempty"`
	ReceivedAt      time.Time         `json:"received_at"`
	MerchantID      int64             `json:"merchant_id"` // 添加商户ID字段
	Debug           bool              `json:"debug"`       // 调试模式标识，为true时使用模拟数据
	IsTestMode      bool              `json:"is_test_mode"` // 测试模式标识，为true时使用模拟数据
}

// ToCardRecord 转换为卡记录 - 与Python系统数据库表结构保持一致
func (r *InternalBindRequest) ToCardRecord(merchantID int64) *CardRecord {
	// 生成UUID作为主键
	id := uuid.New().String()

	// 将字符串指针转换为指针类型
	var cardPassword *string
	if r.CardPassword != "" {
		cardPassword = &r.CardPassword
	}

	var ipAddress *string
	if r.ClientIP != "" {
		ipAddress = &r.ClientIP
	}

	// 将请求数据序列化为JSON字符串
	requestDataBytes, _ := json.Marshal(map[string]interface{}{
		"card_number":       r.CardNumber,
		"card_password":     r.CardPassword,
		"merchant_order_id": r.MerchantOrderID,
		"amount":            r.Amount,
		"client_ip":         r.ClientIP,
		"user_agent":        r.UserAgent,
	})
	requestDataStr := string(requestDataBytes)

	return &CardRecord{
		ID:              id,
		MerchantID:      merchantID,
		MerchantOrderID: r.MerchantOrderID,
		Amount:          int(r.Amount),
		CardNumber:      r.CardNumber,
		CardPassword:    cardPassword,
		Status:          "pending",
		RequestID:       r.RequestID,
		RequestData:     requestDataStr,
		IPAddress:       ipAddress,
		RetryCount:      0,
		IsTestMode:      r.IsTestMode,
		CallbackStatus:  "pending",
		CreatedAt:       r.ReceivedAt,
		UpdatedAt:       r.ReceivedAt,
	}
}

// BindCardResponse 绑卡响应模型 - 与Python系统完全兼容
type BindCardResponse struct {
	RecordID        string `json:"recordId"`        // 记录ID
	RequestID       string `json:"requestId"`       // 请求ID
	Status          string `json:"status"`          // 状态：processing
	MerchantOrderID string `json:"merchantOrderId"` // 商户订单号
	Amount          int    `json:"amount"`          // 金额（分）
}

// APIResponse 统一API响应格式 - 与Python系统完全兼容
type APIResponse struct {
	Code    int         `json:"code"`    // 响应码，0表示成功
	Message string      `json:"message"` // 响应消息
	Data    interface{} `json:"data,omitempty"`
}

// SuccessResponse 成功响应
func SuccessResponse(data interface{}) *APIResponse {
	return &APIResponse{
		Code:    0,
		Message: "操作成功",
		Data:    data,
	}
}

// ErrorResponse 错误响应
func ErrorResponse(code int, message string) *APIResponse {
	return &APIResponse{
		Code:    code,
		Message: message,
	}
}

// BatchInsertResult 批量插入结果
type BatchInsertResult struct {
	SuccessCount int      `json:"success_count"`
	FailedCount  int      `json:"failed_count"`
	Errors       []string `json:"errors,omitempty"`
}

// QueueMessage 队列消息模型 - 与Python系统期望的格式完全一致
type QueueMessage struct {
	RecordID        string            `json:"record_id"`         // 数据库记录ID（必需）
	RequestID       string            `json:"request_id"`        // 请求ID
	CardNumber      string            `json:"card_number"`       // 卡号
	CardPassword    string            `json:"card_password"`     // 卡密
	MerchantID      int64             `json:"merchant_id"`       // 商户ID（必需）
	MerchantCode    string            `json:"merchant_code"`     // 商户代码
	MerchantOrderID string            `json:"merchant_order_id"` // 商户订单号
	Amount          int               `json:"amount"`            // 金额（分）
	ClientIP        string            `json:"client_ip"`         // 客户端IP
	ExtData         string            `json:"ext_data,omitempty"` // 扩展数据
	TraceID         string            `json:"trace_id"`          // 追踪ID
	Debug           bool              `json:"debug,omitempty"`   // 调试模式
	IsTestMode      bool              `json:"is_test_mode"`      // 测试模式
	Headers         map[string]string `json:"headers,omitempty"` // 请求头
	CreatedAt       time.Time         `json:"created_at"`        // 创建时间
}

// FromInternalRequest 从内部请求创建队列消息 - 与Python系统期望的格式完全一致
func (q *QueueMessage) FromInternalRequest(req *InternalBindRequest, recordID string) {
	q.RecordID = recordID                                    // 数据库记录ID（必需）
	q.RequestID = req.RequestID                              // 请求ID
	q.CardNumber = req.CardNumber                            // 卡号
	q.CardPassword = req.CardPassword                        // 卡密
	q.MerchantID = req.MerchantID                           // 商户ID（必需）
	q.MerchantCode = req.MerchantCode                       // 商户代码
	q.MerchantOrderID = req.MerchantOrderID                 // 商户订单号
	q.Amount = int(req.Amount)                              // 金额（转换为int）
	q.ClientIP = req.ClientIP                               // 客户端IP
	q.ExtData = req.ExtData                                 // 扩展数据
	q.TraceID = req.RequestID                               // 使用RequestID作为TraceID
	q.Debug = req.Debug                                     // 传递调试模式标识
	q.IsTestMode = req.IsTestMode                           // 传递测试模式标识
	q.Headers = req.Headers                                 // 请求头
	q.CreatedAt = req.ReceivedAt                            // 创建时间
}

// RedisCardStatus Redis卡状态模型
type RedisCardStatus struct {
	RequestID    string    `json:"request_id"`
	Status       string    `json:"status"`
	MerchantCode string    `json:"merchant_code"`
	CreatedAt    time.Time `json:"created_at"`
	TTL          int       `json:"ttl"` // 秒
}

// GetRedisKey 获取Redis键
func (r *RedisCardStatus) GetRedisKey() string {
	return "card_status:" + r.RequestID
}

// CardStatus 卡状态常量
const (
	StatusPending      = "pending"
	StatusProcessing   = "processing"
	StatusSuccess      = "success"
	StatusFailed       = "failed"
	StatusTimeout      = "timeout"
	StatusBalanceFailed = "balance_failed" // 绑卡成功但获取余额失败
)

// IsValidStatus 检查状态是否有效
func IsValidStatus(status string) bool {
	validStatuses := []string{
		StatusPending,
		StatusProcessing,
		StatusSuccess,
		StatusFailed,
		StatusTimeout,
		StatusBalanceFailed,
	}

	for _, validStatus := range validStatuses {
		if status == validStatus {
			return true
		}
	}
	return false
}

// CardService 卡记录服务接口
type CardService interface {
	// CheckCardExists 检查卡号是否已存在
	CheckCardExists(ctx context.Context, cardNumber string) (bool, error)

	// ValidateCardNumber 验证卡号格式
	ValidateCardNumber(cardNumber string) error
}
