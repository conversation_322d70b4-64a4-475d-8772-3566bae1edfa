from sqlalchemy import Column, String, BigInteger, <PERSON><PERSON><PERSON>, Foreign<PERSON>ey, Date
from sqlalchemy.orm import relationship
from datetime import date
from typing import Optional

from app.models.base import BaseModel, TimestampMixin


class UserOrganization(BaseModel, TimestampMixin):
    """用户组织关系表 - 支持用户多组织归属"""

    __tablename__ = "user_organizations"

    id = Column(BigInteger, primary_key=True, index=True, autoincrement=True)
    user_id = Column(BigInteger, ForeignKey("users.id"), nullable=False, comment="用户ID")
    merchant_id = Column(BigInteger, ForeignKey("merchants.id"), nullable=False, comment="商户ID")
    department_id = Column(BigInteger, ForeignKey("departments.id"), nullable=True, comment="部门ID")
    position = Column(String(100), nullable=True, comment="职位")
    is_primary = Column(Boolean, default=True, nullable=False, comment="是否主要组织关系")
    status = Column(Boolean, default=True, nullable=False, comment="状态：1启用，0禁用")
    start_date = Column(Date, nullable=True, comment="开始日期")
    end_date = Column(Date, nullable=True, comment="结束日期")
    created_by = Column(BigInteger, ForeignKey("users.id"), nullable=True, comment="创建者ID")

    # 关联关系
    user = relationship("User", foreign_keys=[user_id], back_populates="user_organizations")
    merchant = relationship("Merchant", back_populates="user_organizations")
    department = relationship("Department", back_populates="user_organizations")
    creator = relationship("User", foreign_keys=[created_by], post_update=True)

    def __repr__(self):
        return f"<UserOrganization(user_id={self.user_id}, merchant_id={self.merchant_id}, department_id={self.department_id}, is_primary={self.is_primary})>"

    def to_dict(self, include_details: bool = False) -> dict:
        """转换为字典格式"""
        data = {
            "id": self.id,
            "user_id": self.user_id,
            "merchant_id": self.merchant_id,
            "department_id": self.department_id,
            "position": self.position,
            "is_primary": self.is_primary,
            "status": self.status,
            "start_date": self.start_date.isoformat() if self.start_date else None,
            "end_date": self.end_date.isoformat() if self.end_date else None,
            "created_by": self.created_by,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None,
        }
        
        if include_details:
            data.update({
                "merchant_name": self.merchant.name if self.merchant else None,
                "department_name": self.department.name if self.department else None,
                "user_name": self.user.full_name if self.user else None,
                "user_username": self.user.username if self.user else None,
            })
            
        return data

    @property
    def is_active(self) -> bool:
        """检查关系是否有效"""
        if not self.status:
            return False
            
        today = date.today()
        
        # 检查开始日期
        if self.start_date and self.start_date > today:
            return False
            
        # 检查结束日期
        if self.end_date and self.end_date < today:
            return False
            
        return True

    @property
    def is_expired(self) -> bool:
        """检查关系是否已过期"""
        if self.end_date:
            return self.end_date < date.today()
        return False

    @property
    def is_future(self) -> bool:
        """检查关系是否为未来生效"""
        if self.start_date:
            return self.start_date > date.today()
        return False

    def get_full_position_path(self) -> str:
        """获取完整的职位路径"""
        parts = []

        if self.merchant:
            parts.append(self.merchant.name)

        if self.department:
            parts.append(self.department.get_full_path())

        if self.position:
            parts.append(self.position)

        return " - ".join(parts)

    def can_access_department(self, target_department_id: int) -> bool:
        """检查是否可以访问指定部门"""
        if not self.is_active:
            return False
            
        # 如果没有部门限制，可以访问商户内所有部门
        if not self.department_id:
            return True
            
        # 如果是同一个部门
        if self.department_id == target_department_id:
            return True
            
        # 检查是否为子部门
        if self.department:
            child_ids = self.department.get_all_children_ids()
            return target_department_id in child_ids
            
        return False

    def can_access_merchant(self, target_merchant_id: int) -> bool:
        """检查是否可以访问指定商户"""
        if not self.is_active:
            return False
        return self.merchant_id == target_merchant_id

    @classmethod
    def get_user_primary_organization(cls, db_session, user_id: int):
        """获取用户的主要组织关系"""
        return db_session.query(cls).filter(
            cls.user_id == user_id,
            cls.is_primary == True,
            cls.status == True
        ).first()

    @classmethod
    def get_user_organizations(cls, db_session, user_id: int, active_only: bool = True):
        """获取用户的所有组织关系"""
        query = db_session.query(cls).filter(cls.user_id == user_id)
        
        if active_only:
            today = date.today()
            query = query.filter(
                cls.status == True,
                (cls.start_date.is_(None) | (cls.start_date <= today)),
                (cls.end_date.is_(None) | (cls.end_date >= today))
            )
            
        return query.all()

    @classmethod
    def get_merchant_users(cls, db_session, merchant_id: int, active_only: bool = True):
        """获取商户的所有用户关系"""
        query = db_session.query(cls).filter(cls.merchant_id == merchant_id)

        if active_only:
            today = date.today()
            query = query.filter(
                cls.status == True,
                (cls.start_date.is_(None) | (cls.start_date <= today)),
                (cls.end_date.is_(None) | (cls.end_date >= today))
            )

        return query.all()

    @classmethod
    def get_department_users(cls, db_session, department_id: int, active_only: bool = True):
        """获取部门的所有用户关系"""
        query = db_session.query(cls).filter(cls.department_id == department_id)
        
        if active_only:
            today = date.today()
            query = query.filter(
                cls.status == True,
                (cls.start_date.is_(None) | (cls.start_date <= today)),
                (cls.end_date.is_(None) | (cls.end_date >= today))
            )
            
        return query.all()

    @classmethod
    def set_primary_organization(cls, db_session, user_id: int, organization_id: int):
        """设置用户的主要组织关系"""
        # 先取消所有主要关系
        db_session.query(cls).filter(
            cls.user_id == user_id,
            cls.is_primary == True
        ).update({"is_primary": False})
        
        # 设置新的主要关系
        db_session.query(cls).filter(
            cls.id == organization_id,
            cls.user_id == user_id
        ).update({"is_primary": True})
        
        db_session.commit()

    @classmethod
    def transfer_user_to_department(cls, db_session, user_id: int, new_department_id: int, 
                                  position: Optional[str] = None, end_current: bool = True):
        """将用户转移到新部门"""
        from app.models.department import Department
        
        # 获取新部门信息
        new_dept = db_session.query(Department).filter(Department.id == new_department_id).first()
        if not new_dept:
            raise ValueError("目标部门不存在")
        
        if end_current:
            # 结束当前的主要组织关系
            current_org = cls.get_user_primary_organization(db_session, user_id)
            if current_org:
                current_org.end_date = date.today()
                current_org.is_primary = False
        
        # 创建新的组织关系
        new_org = cls(
            user_id=user_id,
            merchant_id=new_dept.merchant_id,
            department_id=new_department_id,
            position=position,
            is_primary=True,
            status=True,
            start_date=date.today()
        )
        
        db_session.add(new_org)
        db_session.commit()
        
        return new_org

    def extend_relationship(self, new_end_date: Optional[date] = None):
        """延长组织关系"""
        self.end_date = new_end_date
        
    def terminate_relationship(self, end_date: Optional[date] = None):
        """终止组织关系"""
        self.end_date = end_date or date.today()
        self.status = False
