#!/usr/bin/env python3
"""
测试移除省略号后的步骤名称显示

验证时间线组件显示完整的步骤名称，不再使用省略号
"""

import sys
import os

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

try:
    from sqlalchemy.orm import Session
    from app.db.session import SessionLocal
    from app.models.card_record import CardRecord
    from app.models.binding_log import BindingLog, LogType
    from app.services.binding_timeline_service import BindingTimelineService
    print("✅ 模块导入成功")
except ImportError as e:
    print(f"❌ 模块导入失败: {e}")
    sys.exit(1)


def test_no_ellipsis_in_step_names():
    """测试步骤名称中不再包含省略号"""
    print("🔧 测试移除省略号后的步骤名称")
    print("=" * 60)
    
    db = SessionLocal()
    try:
        # 查询一个有多个日志记录的绑卡记录
        record = db.query(CardRecord).join(BindingLog).filter(
            CardRecord.status.in_(['success', 'failed'])
        ).order_by(CardRecord.created_at.desc()).first()
        
        if not record:
            print("❌ 没有找到有日志记录的绑卡记录")
            return
        
        print(f"📝 测试记录: {str(record.id)[:8]}...")
        print(f"📊 记录状态: {record.status}")
        print()
        
        # 创建时间线服务并获取时间线
        timeline_service = BindingTimelineService(db)
        
        try:
            timeline = timeline_service.get_binding_timeline(str(record.id))
            
            print("✅ 时间线API调用成功")
            print()
            
            # 检查步骤名称中是否包含省略号
            print("🔍 检查步骤名称中的省略号:")
            print("-" * 60)
            
            has_ellipsis = False
            long_step_names = []
            
            for i, step in enumerate(timeline.steps, 1):
                step_name = step.step_name
                contains_ellipsis = "..." in step_name
                
                if contains_ellipsis:
                    has_ellipsis = True
                    status_icon = "❌"
                else:
                    status_icon = "✅"
                
                # 记录长步骤名称
                if len(step_name) > 50:
                    long_step_names.append({
                        'index': i,
                        'name': step_name,
                        'length': len(step_name)
                    })
                
                print(f"{i:2d}. {status_icon} {step_name}")
                
                if contains_ellipsis:
                    print(f"      ⚠️  包含省略号！")
            
            print()
            print("📊 省略号检查结果:")
            print("-" * 60)
            
            if has_ellipsis:
                print("   ❌ 发现包含省略号的步骤名称")
                print("   需要进一步修复代码")
            else:
                print("   ✅ 所有步骤名称都不包含省略号")
                print("   省略号移除成功！")
            
            # 显示长步骤名称统计
            if long_step_names:
                print()
                print("📏 长步骤名称统计:")
                print("-" * 60)
                
                for step_info in long_step_names:
                    print(f"   {step_info['index']:2d}. 长度: {step_info['length']:3d}字符")
                    print(f"       名称: {step_info['name']}")
                    print()
                
                avg_length = sum(s['length'] for s in long_step_names) / len(long_step_names)
                max_length = max(s['length'] for s in long_step_names)
                
                print(f"   📈 长步骤统计:")
                print(f"      数量: {len(long_step_names)}/{len(timeline.steps)}")
                print(f"      平均长度: {avg_length:.1f}字符")
                print(f"      最长: {max_length}字符")
                
                if max_length > 100:
                    print("   ⚠️  存在超长步骤名称，可能影响界面显示")
                elif max_length > 80:
                    print("   ⚠️  存在较长步骤名称，建议关注界面适配")
                else:
                    print("   ✅ 步骤名称长度合理")
            else:
                print()
                print("📏 步骤名称长度:")
                print("-" * 60)
                
                lengths = [len(step.step_name) for step in timeline.steps]
                if lengths:
                    avg_length = sum(lengths) / len(lengths)
                    max_length = max(lengths)
                    min_length = min(lengths)
                    
                    print(f"   平均长度: {avg_length:.1f}字符")
                    print(f"   最长: {max_length}字符")
                    print(f"   最短: {min_length}字符")
                    print("   ✅ 所有步骤名称长度适中")
            
            # 显示完整的步骤名称示例
            print()
            print("📝 完整步骤名称示例:")
            print("-" * 60)
            
            for i, step in enumerate(timeline.steps[:5], 1):  # 只显示前5个
                duration_display = f"{step.duration_ms:.2f}ms" if step.duration_ms else "无"
                status_icon = {
                    'success': '✅',
                    'failed': '❌',
                    'running': '🔄',
                    'pending': '⏳'
                }.get(step.status, '❓')
                
                print(f"{i:2d}. {status_icon} {step.step_name}")
                print(f"      耗时: {duration_display} | 消息: {step.message[:60]}...")
                print()
            
            if len(timeline.steps) > 5:
                print(f"   ... 还有 {len(timeline.steps) - 5} 个步骤")
                
        except Exception as e:
            print(f"❌ 获取时间线失败: {str(e)}")
            import traceback
            traceback.print_exc()
            
    finally:
        db.close()


def test_step_name_generation_without_ellipsis():
    """测试步骤名称生成不包含省略号"""
    print("\n🧪 测试步骤名称生成（无省略号）")
    print("=" * 60)
    
    db = SessionLocal()
    try:
        timeline_service = BindingTimelineService(db)
        
        # 创建测试用的长消息日志对象
        test_cases = [
            {
                'log_type': LogType.SYSTEM,
                'message': '手动重试操作 - 从失败状态重试 - 用户ID: 12345 - 操作时间: 2025-08-02 10:30:45',
                'expected_contains': '手动重试操作 - 从失败状态重试 - 用户ID: 12345 - 操作时间: 2025-08-02 10:30:45'
            },
            {
                'log_type': LogType.API_REQUEST,
                'message': '发送API请求到沃尔玛服务器 - 请求类型: 绑卡验证 - 超时设置: 30秒 - 重试次数: 3',
                'expected_contains': '发送API请求到沃尔玛服务器 - 请求类型: 绑卡验证 - 超时设置: 30秒 - 重试次数: 3'
            },
            {
                'log_type': LogType.ERROR,
                'message': '网络连接超时错误 - 目标服务器: walmart.api.com - 错误代码: TIMEOUT_001 - 建议: 检查网络连接',
                'expected_contains': '网络连接超时错误 - 目标服务器: walmart.api.com - 错误代码: TIMEOUT_001 - 建议: 检查网络连接'
            }
        ]
        
        print("测试长消息处理:")
        print("-" * 60)
        
        for i, case in enumerate(test_cases, 1):
            # 创建模拟的日志对象
            class MockLog:
                def __init__(self, log_type, message):
                    self.log_type = log_type
                    self.message = message
                    self.attempt_number = None
            
            mock_log = MockLog(case['log_type'], case['message'])
            actual = timeline_service._get_step_name(mock_log)
            
            contains_ellipsis = "..." in actual
            contains_expected = case['expected_contains'] in actual
            
            status = "✅" if not contains_ellipsis and contains_expected else "❌"
            
            print(f"{i:2d}. {status} 类型: {case['log_type']}")
            print(f"      原消息长度: {len(case['message'])}字符")
            print(f"      生成名称长度: {len(actual)}字符")
            print(f"      包含省略号: {'是' if contains_ellipsis else '否'}")
            print(f"      包含完整内容: {'是' if contains_expected else '否'}")
            print(f"      生成名称: {actual}")
            print()
        
    finally:
        db.close()


if __name__ == "__main__":
    print("🚀 开始测试移除省略号效果")
    print()
    
    try:
        # 测试实际数据中的省略号移除
        test_no_ellipsis_in_step_names()
        
        # 测试步骤名称生成不包含省略号
        test_step_name_generation_without_ellipsis()
        
        print()
        print("✅ 测试完成！")
        print()
        print("🎯 省略号移除总结:")
        print("   1. ✅ 系统操作: 显示完整的操作描述")
        print("   2. ✅ API请求: 显示完整的请求信息")
        print("   3. ✅ 错误处理: 显示完整的错误详情")
        print("   4. ✅ 所有类型: 不再截断消息内容")
        print("   5. ✅ 用户体验: 可以看到完整的操作信息")
        
    except Exception as e:
        print(f"❌ 测试过程中出现错误: {str(e)}")
        import traceback
        traceback.print_exc()
