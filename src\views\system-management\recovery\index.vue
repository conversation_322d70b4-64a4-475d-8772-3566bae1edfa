<template>
  <div class="recovery-container">
    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <span>卡住请求恢复处理</span>
          <el-button type="primary" @click="handleProcess" :loading="loading">
            开始处理
          </el-button>
        </div>
      </template>
      
      <el-form :model="formData" label-width="120px" size="default">
        <el-form-item label="查找时间范围">
          <el-input-number 
            v-model="formData.hours" 
            :min="1" 
            :max="72"
            controls-position="right"
          />
          <span class="form-item-hint">小时（查找多少小时内的请求）</span>
        </el-form-item>
        
        <el-form-item label="批处理大小">
          <el-input-number 
            v-model="formData.batchSize" 
            :min="10" 
            :max="200"
            controls-position="right"
          />
          <span class="form-item-hint">条（每批处理的请求数量）</span>
        </el-form-item>
      </el-form>
      
      <div v-if="lastResult" class="result-container">
        <h3>处理结果</h3>
        <el-descriptions border>
          <el-descriptions-item label="总请求数">{{ lastResult.total }}</el-descriptions-item>
          <el-descriptions-item label="成功处理">{{ lastResult.success }}</el-descriptions-item>
          <el-descriptions-item label="处理失败">{{ lastResult.failed }}</el-descriptions-item>
        </el-descriptions>
        
        <div v-if="lastResult.total > 0" class="details-container">
          <h4>详细信息</h4>
          <el-table :data="lastResult.details" border style="width: 100%">
            <el-table-column prop="record_id" label="记录ID" width="280" />
            <el-table-column prop="merchant_id" label="商家ID" width="100" />
            <el-table-column prop="card_number" label="卡号" width="150" />
            <el-table-column prop="created_at" label="创建时间" width="180" />
            <el-table-column prop="success" label="处理结果">
              <template #default="scope">
                <el-tag :type="scope.row.success ? 'success' : 'danger'">
                  {{ scope.row.success ? '成功' : '失败' }}
                </el-tag>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useRecoveryStore } from '@/store/modules/recovery'

const recoveryStore = useRecoveryStore()
const loading = computed(() => recoveryStore.isLoading)
const lastResult = computed(() => recoveryStore.lastResult)

const formData = ref({
  hours: 24,
  batchSize: 50
})

// 处理卡住的请求
const handleProcess = async () => {
  try {
    await ElMessageBox.confirm(
      '此操作将重新处理卡在"pending"状态的绑卡请求，是否继续？',
      '确认操作',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    const params = {
      hours: formData.value.hours,
      batch_size: formData.value.batchSize
    }
    
    const result = await recoveryStore.processStuckRequests(params)
    
    if (result.total === 0) {
      ElMessage.info('没有找到需要处理的卡住请求')
    } else {
      ElMessage.success(`成功处理了 ${result.success} 个卡住请求`)
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('处理卡住请求失败', error)
      ElMessage.error('处理卡住请求失败: ' + (error.message || error))
    }
  }
}
</script>

<style scoped>
.recovery-container {
  padding: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.form-item-hint {
  margin-left: 10px;
  color: #909399;
  font-size: 14px;
}

.result-container {
  margin-top: 20px;
  border-top: 1px solid #ebeef5;
  padding-top: 20px;
}

.details-container {
  margin-top: 20px;
}
</style>
