#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
沃尔玛CK管理CRUD测试
测试沃尔玛CK的创建、读取、更新、删除功能
"""

import sys
import os
import time
import json
import random
import string

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.dirname(__file__))))

from test.conftest import TestBase, get_test_accounts, format_test_result, print_test_summary

class WalmartCKCRUDTestSuite(TestBase):
    """沃尔玛CK管理CRUD测试类"""

    def __init__(self):
        super().__init__()
        self.results = []
        self.test_accounts = get_test_accounts()
        self.admin_token = None
        self.merchant_token = None
        self.test_ck_ids = []  # 存储测试创建的CK ID，用于清理

    def setup_test_environment(self):
        """设置测试环境"""
        print("=== 设置沃尔玛CK测试环境 ===")

        # 登录管理员账号
        self.admin_token = self.login(
            self.test_accounts["super_admin"]["username"],
            self.test_accounts["super_admin"]["password"]
        )

        # 登录商户账号
        self.merchant_token = self.login(
            self.test_accounts["merchant_admin"]["username"],
            self.test_accounts["merchant_admin"]["password"]
        )

        if not self.admin_token:
            raise Exception("无法获取管理员token")
        if not self.merchant_token:
            raise Exception("无法获取商户token")

        print("✅ 测试环境设置完成")

    def get_valid_merchant_and_department(self):
        """获取有效的商户和部门ID"""
        # 获取第一个商户
        status_code, response = self.make_request("GET", "/merchants", self.admin_token)
        if status_code == 200 and response.get("data"):
            merchants = response["data"].get("items", []) if isinstance(response["data"], dict) else response["data"]
            if merchants:
                merchant_id = merchants[0]["id"]

                # 获取该商户的部门
                status_code, dept_response = self.make_request("GET", f"/departments?merchant_id={merchant_id}", self.admin_token)
                if status_code == 200 and dept_response.get("data"):
                    departments = dept_response["data"].get("items", []) if isinstance(dept_response["data"], dict) else dept_response["data"]
                    if departments:
                        return merchant_id, departments[0]["id"]

                # 如果没有部门，返回商户ID和None
                return merchant_id, None

        # 如果没有找到有效的商户，使用默认值
        return 1, None

    def generate_test_ck_data(self):
        """生成测试CK数据"""
        random_suffix = ''.join(random.choices(string.ascii_lowercase + string.digits, k=6))
        merchant_id, department_id = self.get_valid_merchant_and_department()

        return {
            "sign": f"test_ck_{random_suffix}@example.com#token123#wxsign456#27",
            "merchant_id": merchant_id,
            "department_id": department_id,
            "daily_limit": 100,
            "hourly_limit": 50,
            "active": 1,  # 修复：使用active而不是status
            "description": f"测试CK用户_{random_suffix}"
        }

    def test_get_walmart_ck_list(self):
        """测试获取沃尔玛CK列表"""
        print("\n=== 测试获取沃尔玛CK列表 ===")

        # 测试管理员获取CK列表
        status_code, response = self.make_request("GET", "/walmart-ck", self.admin_token)

        if status_code == 200:
            self.results.append(format_test_result(
                "管理员获取沃尔玛CK列表",
                True,
                "管理员成功获取沃尔玛CK列表"
            ))
            print("✅ 管理员成功获取沃尔玛CK列表")

            # 检查响应格式
            if "data" in response:
                data = response["data"]
                if isinstance(data, dict) and "items" in data:
                    print(f"   📊 找到 {len(data['items'])} 个沃尔玛CK")
                elif isinstance(data, list):
                    print(f"   📊 找到 {len(data)} 个沃尔玛CK")
        else:
            self.results.append(format_test_result(
                "管理员获取沃尔玛CK列表",
                False,
                f"获取沃尔玛CK列表失败，状态码: {status_code}"
            ))
            print(f"❌ 获取沃尔玛CK列表失败，状态码: {status_code}")

        # 测试商户获取CK列表（应该只能看到自己的数据）
        status_code, response = self.make_request("GET", "/walmart-ck", self.merchant_token)

        if status_code == 200:
            self.results.append(format_test_result(
                "商户获取沃尔玛CK列表",
                True,
                "商户成功获取沃尔玛CK列表"
            ))
            print("✅ 商户成功获取沃尔玛CK列表")
        else:
            self.results.append(format_test_result(
                "商户获取沃尔玛CK列表",
                False,
                f"商户获取沃尔玛CK列表失败，状态码: {status_code}"
            ))
            print(f"❌ 商户获取沃尔玛CK列表失败，状态码: {status_code}")

    def test_create_walmart_ck(self):
        """测试创建沃尔玛CK"""
        print("\n=== 测试创建沃尔玛CK ===")

        # 生成测试数据
        test_data = self.generate_test_ck_data()

        # 测试管理员创建CK
        status_code, response = self.make_request("POST", "/walmart-ck", self.admin_token, data=test_data)

        if status_code in [200, 201]:
            # 获取创建的CK ID
            ck_id = None
            print(f"   创建响应: {response}")

            # 处理嵌套的响应格式
            if "data" in response and isinstance(response["data"], dict):
                if "data" in response["data"] and isinstance(response["data"]["data"], dict):
                    # 嵌套格式：response.data.data.id
                    ck_id = response["data"]["data"].get("id")
                else:
                    # 简单格式：response.data.id
                    ck_id = response["data"].get("id")
            elif "id" in response:
                ck_id = response["id"]

            if ck_id:
                self.test_ck_ids.append(ck_id)
                self.results.append(format_test_result(
                    "管理员创建沃尔玛CK",
                    True,
                    f"管理员成功创建沃尔玛CK，ID: {ck_id}"
                ))
                print(f"✅ 管理员成功创建沃尔玛CK，ID: {ck_id}")
            else:
                self.results.append(format_test_result(
                    "管理员创建沃尔玛CK",
                    False,
                    f"创建成功但无法获取CK ID，响应: {response}"
                ))
                print(f"⚠️ 创建成功但无法获取CK ID，响应: {response}")
        else:
            error_msg = f"创建沃尔玛CK失败，状态码: {status_code}"
            if response and 'message' in response:
                error_msg += f"，错误信息: {response['message']}"
            self.results.append(format_test_result(
                "管理员创建沃尔玛CK",
                False,
                error_msg
            ))
            print(f"❌ {error_msg}")
            print(f"   响应内容: {response}")

        # 测试商户创建CK（权限控制）
        test_data_merchant = self.generate_test_ck_data()
        status_code, response = self.make_request("POST", "/walmart-ck", self.merchant_token, data=test_data_merchant)

        if status_code in [200, 201, 403]:  # 200/201表示有权限，403表示无权限
            self.results.append(format_test_result(
                "商户创建沃尔玛CK权限控制",
                True,
                f"商户创建CK权限控制正常，状态码: {status_code}"
            ))
            print(f"✅ 商户创建CK权限控制正常，状态码: {status_code}")

            # 如果商户有权限创建，记录ID用于清理
            if status_code in [200, 201]:
                ck_id = None
                # 处理嵌套的响应格式
                if "data" in response and isinstance(response["data"], dict):
                    if "data" in response["data"] and isinstance(response["data"]["data"], dict):
                        # 嵌套格式：response.data.data.id
                        ck_id = response["data"]["data"].get("id")
                    else:
                        # 简单格式：response.data.id
                        ck_id = response["data"].get("id")
                elif "id" in response:
                    ck_id = response["id"]
                if ck_id:
                    self.test_ck_ids.append(ck_id)
        else:
            error_msg = f"商户创建CK权限控制异常，状态码: {status_code}"
            if response and 'message' in response:
                error_msg += f"，错误信息: {response['message']}"
            self.results.append(format_test_result(
                "商户创建沃尔玛CK权限控制",
                False,
                error_msg
            ))
            print(f"❌ {error_msg}")
            print(f"   响应内容: {response}")

    def test_update_walmart_ck(self):
        """测试更新沃尔玛CK"""
        print("\n=== 测试更新沃尔玛CK ===")

        if not self.test_ck_ids:
            print("⚠️ 没有可用的测试CK ID，跳过更新测试")
            return

        ck_id = self.test_ck_ids[0]
        update_data = {
            "daily_limit": 200,
            "description": "更新后的测试CK用户",
            "status": 1
        }

        # 测试管理员更新CK
        status_code, response = self.make_request("PUT", f"/walmart-ck/{ck_id}", self.admin_token, data=update_data)

        if status_code == 200:
            self.results.append(format_test_result(
                "管理员更新沃尔玛CK",
                True,
                "管理员成功更新沃尔玛CK"
            ))
            print("✅ 管理员成功更新沃尔玛CK")
        else:
            self.results.append(format_test_result(
                "管理员更新沃尔玛CK",
                False,
                f"更新沃尔玛CK失败，状态码: {status_code}"
            ))
            print(f"❌ 更新沃尔玛CK失败，状态码: {status_code}")

    def test_delete_walmart_ck(self):
        """测试删除沃尔玛CK"""
        print("\n=== 测试删除沃尔玛CK ===")

        if not self.test_ck_ids:
            print("⚠️ 没有可用的测试CK ID，跳过删除测试")
            return

        ck_id = self.test_ck_ids[0]

        # 测试管理员删除CK
        status_code, response = self.make_request("DELETE", f"/walmart-ck/{ck_id}", self.admin_token)

        if status_code in [200, 204]:
            self.results.append(format_test_result(
                "管理员删除沃尔玛CK",
                True,
                "管理员成功删除沃尔玛CK"
            ))
            print("✅ 管理员成功删除沃尔玛CK")
            # 从列表中移除已删除的ID
            self.test_ck_ids.remove(ck_id)
        else:
            self.results.append(format_test_result(
                "管理员删除沃尔玛CK",
                False,
                f"删除沃尔玛CK失败，状态码: {status_code}"
            ))
            print(f"❌ 删除沃尔玛CK失败，状态码: {status_code}")

    def test_walmart_ck_data_isolation(self):
        """测试沃尔玛CK数据隔离"""
        print("\n=== 测试沃尔玛CK数据隔离 ===")

        # 获取管理员看到的CK数量
        status_code, admin_response = self.make_request("GET", "/walmart-ck", self.admin_token)
        admin_count = 0

        if status_code == 200 and "data" in admin_response:
            data = admin_response["data"]
            if isinstance(data, dict) and "total" in data:
                admin_count = data["total"]
            elif isinstance(data, list):
                admin_count = len(data)

        # 获取商户看到的CK数量
        status_code, merchant_response = self.make_request("GET", "/walmart-ck", self.merchant_token)
        merchant_count = 0

        if status_code == 200 and "data" in merchant_response:
            data = merchant_response["data"]
            if isinstance(data, dict) and "total" in data:
                merchant_count = data["total"]
            elif isinstance(data, list):
                merchant_count = len(data)

        # 验证数据隔离
        if admin_count >= merchant_count:
            self.results.append(format_test_result(
                "沃尔玛CK数据隔离",
                True,
                f"数据隔离正常，管理员看到 {admin_count} 个，商户看到 {merchant_count} 个"
            ))
            print(f"✅ 数据隔离正常，管理员看到 {admin_count} 个，商户看到 {merchant_count} 个")
        else:
            self.results.append(format_test_result(
                "沃尔玛CK数据隔离",
                False,
                f"数据隔离异常，商户看到的数据比管理员多"
            ))
            print(f"❌ 数据隔离异常，商户看到的数据比管理员多")

    def cleanup_test_data(self):
        """清理测试数据"""
        print("\n=== 清理测试数据 ===")

        for ck_id in self.test_ck_ids:
            try:
                status_code, _ = self.make_request("DELETE", f"/walmart-ck/{ck_id}", self.admin_token)
                if status_code in [200, 204]:
                    print(f"✅ 成功清理测试CK: {ck_id}")
                else:
                    print(f"⚠️ 清理测试CK失败: {ck_id}")
            except Exception as e:
                print(f"⚠️ 清理测试CK异常: {ck_id}, 错误: {str(e)}")

    def run_all_tests(self):
        """运行所有沃尔玛CK测试"""
        print("🧪 开始沃尔玛CK管理CRUD测试")
        print("="*60)

        start_time = time.time()

        try:
            # 设置测试环境
            self.setup_test_environment()

            # 运行测试
            self.test_get_walmart_ck_list()
            self.test_create_walmart_ck()
            self.test_update_walmart_ck()
            self.test_delete_walmart_ck()
            self.test_walmart_ck_data_isolation()

        except Exception as e:
            self.results.append(format_test_result(
                "测试环境设置",
                False,
                f"测试环境设置失败: {str(e)}"
            ))
            print(f"❌ 测试环境设置失败: {str(e)}")
        finally:
            # 清理测试数据
            self.cleanup_test_data()

        end_time = time.time()
        duration = end_time - start_time

        # 打印测试摘要
        print_test_summary(self.results)
        print(f"测试耗时: {duration:.2f}秒")

        return self.results

def main():
    """主函数"""
    test = WalmartCKCRUDTestSuite()
    results = test.run_all_tests()

    # 判断测试是否通过
    failed_tests = [r for r in results if not r.get("success", False)]

    if not failed_tests:
        print("\n🎉 沃尔玛CK管理测试全部通过！")
        return 0
    else:
        print(f"\n⚠️ 发现 {len(failed_tests)} 个问题需要修复")
        for test in failed_tests:
            print(f"  - {test['test_name']}: {test['message']}")
        return 1

if __name__ == "__main__":
    sys.exit(main())
