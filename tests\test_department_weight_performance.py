"""
部门权重系统性能测试

测试高并发环境下的性能和稳定性
"""

import asyncio
import time
import statistics
from concurrent.futures import ThreadPoolExecutor, as_completed
from unittest.mock import Mock, patch
import pytest

from app.services.department_weight_service import DepartmentWeightService
from app.services.enhanced_ck_service import EnhancedCKService


class TestDepartmentWeightPerformance:
    """部门权重系统性能测试类"""
    
    @pytest.fixture
    def mock_db(self):
        """模拟数据库会话"""
        return Mock()
    
    @pytest.fixture
    def weight_service(self, mock_db):
        """创建权重服务实例"""
        return DepartmentWeightService(mock_db)
    
    @pytest.fixture
    def large_department_dataset(self):
        """大规模部门数据集"""
        departments = []
        for i in range(100):  # 100个部门
            departments.append({
                'id': i + 1,
                'name': f'部门{i + 1}',
                'binding_weight': (i % 10 + 1) * 10,  # 权重10-100
                'total_ck_count': (i % 5 + 1) * 2,    # CK数量2-10
                'active_ck_count': (i % 5 + 1) * 2,
                'available_ck_count': (i % 5 + 1),    # 可用CK数量1-5
                'can_bind': True
            })
        return departments
    
    def test_weight_selection_performance_single_thread(self, weight_service, large_department_dataset):
        """测试单线程权重选择性能"""
        with patch.object(weight_service, 'get_available_departments_with_weights') as mock_get_depts:
            mock_get_depts.return_value = large_department_dataset
            
            # 预热
            for _ in range(10):
                weight_service.select_department_by_weight(merchant_id=1)
            
            # 性能测试
            start_time = time.time()
            test_count = 1000
            
            for _ in range(test_count):
                result = weight_service.select_department_by_weight(merchant_id=1)
                assert result is not None
            
            end_time = time.time()
            duration = end_time - start_time
            avg_time = duration / test_count
            
            print(f"\n单线程性能测试结果:")
            print(f"总耗时: {duration:.3f}秒")
            print(f"平均每次选择耗时: {avg_time*1000:.3f}毫秒")
            print(f"每秒处理能力: {test_count/duration:.0f}次/秒")
            
            # 性能要求：平均每次选择应在1毫秒内完成
            assert avg_time < 0.001, f"性能不达标，平均耗时{avg_time*1000:.3f}毫秒"
    
    def test_weight_selection_performance_multi_thread(self, weight_service, large_department_dataset):
        """测试多线程权重选择性能"""
        with patch.object(weight_service, 'get_available_departments_with_weights') as mock_get_depts:
            mock_get_depts.return_value = large_department_dataset
            
            def select_department_task():
                """单个选择任务"""
                start = time.time()
                result = weight_service.select_department_by_weight(merchant_id=1)
                end = time.time()
                return result is not None, end - start
            
            # 多线程性能测试
            thread_count = 10
            tasks_per_thread = 100
            total_tasks = thread_count * tasks_per_thread
            
            start_time = time.time()
            
            with ThreadPoolExecutor(max_workers=thread_count) as executor:
                futures = [
                    executor.submit(select_department_task) 
                    for _ in range(total_tasks)
                ]
                
                results = []
                execution_times = []
                
                for future in as_completed(futures):
                    success, exec_time = future.result()
                    results.append(success)
                    execution_times.append(exec_time)
            
            end_time = time.time()
            total_duration = end_time - start_time
            
            # 统计结果
            success_count = sum(results)
            success_rate = success_count / total_tasks * 100
            avg_exec_time = statistics.mean(execution_times)
            max_exec_time = max(execution_times)
            min_exec_time = min(execution_times)
            
            print(f"\n多线程性能测试结果:")
            print(f"线程数: {thread_count}")
            print(f"总任务数: {total_tasks}")
            print(f"总耗时: {total_duration:.3f}秒")
            print(f"成功率: {success_rate:.1f}%")
            print(f"平均执行时间: {avg_exec_time*1000:.3f}毫秒")
            print(f"最大执行时间: {max_exec_time*1000:.3f}毫秒")
            print(f"最小执行时间: {min_exec_time*1000:.3f}毫秒")
            print(f"并发处理能力: {total_tasks/total_duration:.0f}次/秒")
            
            # 性能要求
            assert success_rate >= 99.0, f"成功率过低: {success_rate:.1f}%"
            assert avg_exec_time < 0.005, f"平均执行时间过长: {avg_exec_time*1000:.3f}毫秒"
            assert total_tasks/total_duration >= 1000, f"并发处理能力不足: {total_tasks/total_duration:.0f}次/秒"
    
    def test_weight_distribution_accuracy_under_load(self, weight_service):
        """测试高负载下权重分配的准确性"""
        # 简化的部门数据，便于验证分配准确性
        departments = [
            {'id': 1, 'name': '部门A', 'binding_weight': 70, 'can_bind': True},
            {'id': 2, 'name': '部门B', 'binding_weight': 30, 'can_bind': True}
        ]
        
        with patch.object(weight_service, 'get_available_departments_with_weights') as mock_get_depts:
            mock_get_depts.return_value = departments
            
            def selection_task(task_count):
                """选择任务"""
                results = {}
                for _ in range(task_count):
                    result = weight_service.select_department_by_weight(merchant_id=1)
                    if result:
                        dept_id = result['id']
                        results[dept_id] = results.get(dept_id, 0) + 1
                return results
            
            # 高负载测试
            thread_count = 20
            tasks_per_thread = 500
            total_selections = thread_count * tasks_per_thread
            
            start_time = time.time()
            
            with ThreadPoolExecutor(max_workers=thread_count) as executor:
                futures = [
                    executor.submit(selection_task, tasks_per_thread) 
                    for _ in range(thread_count)
                ]
                
                combined_results = {}
                for future in as_completed(futures):
                    thread_results = future.result()
                    for dept_id, count in thread_results.items():
                        combined_results[dept_id] = combined_results.get(dept_id, 0) + count
            
            end_time = time.time()
            duration = end_time - start_time
            
            # 计算分配比例
            dept_a_count = combined_results.get(1, 0)
            dept_b_count = combined_results.get(2, 0)
            total_count = dept_a_count + dept_b_count
            
            dept_a_percentage = (dept_a_count / total_count) * 100 if total_count > 0 else 0
            dept_b_percentage = (dept_b_count / total_count) * 100 if total_count > 0 else 0
            
            print(f"\n高负载权重分配准确性测试结果:")
            print(f"总选择次数: {total_count}")
            print(f"测试耗时: {duration:.3f}秒")
            print(f"部门A选择次数: {dept_a_count} ({dept_a_percentage:.1f}%)")
            print(f"部门B选择次数: {dept_b_count} ({dept_b_percentage:.1f}%)")
            print(f"理论分配: 部门A 70%, 部门B 30%")
            
            # 准确性验证（允许±5%误差）
            assert 65 <= dept_a_percentage <= 75, f"部门A分配比例偏差过大: {dept_a_percentage:.1f}%"
            assert 25 <= dept_b_percentage <= 35, f"部门B分配比例偏差过大: {dept_b_percentage:.1f}%"
            assert total_count == total_selections, f"选择次数不匹配: {total_count} vs {total_selections}"
    
    @pytest.mark.asyncio
    async def test_enhanced_ck_service_performance(self):
        """测试增强CK服务的性能"""
        mock_db = Mock()
        ck_service = EnhancedCKService(mock_db)
        
        # 模拟权重服务
        with patch.object(ck_service.weight_service, 'select_department_by_weight') as mock_select:
            with patch.object(ck_service.weight_service, 'get_ck_from_selected_department') as mock_get_ck:
                
                # 模拟返回结果
                mock_select.return_value = {'id': 1, 'name': '部门A', 'binding_weight': 100}
                mock_ck = Mock()
                mock_ck.id = 1
                mock_get_ck.return_value = mock_ck
                
                # 异步性能测试
                async def get_ck_task():
                    start = time.time()
                    result = await ck_service.get_optimal_ck(merchant_id=1, use_weight_algorithm=True)
                    end = time.time()
                    return result is not None, end - start
                
                # 并发测试
                concurrent_tasks = 50
                start_time = time.time()
                
                tasks = [get_ck_task() for _ in range(concurrent_tasks)]
                results = await asyncio.gather(*tasks)
                
                end_time = time.time()
                total_duration = end_time - start_time
                
                # 统计结果
                success_results = [r[0] for r in results]
                execution_times = [r[1] for r in results]
                
                success_count = sum(success_results)
                success_rate = success_count / concurrent_tasks * 100
                avg_exec_time = statistics.mean(execution_times)
                
                print(f"\n增强CK服务异步性能测试结果:")
                print(f"并发任务数: {concurrent_tasks}")
                print(f"总耗时: {total_duration:.3f}秒")
                print(f"成功率: {success_rate:.1f}%")
                print(f"平均执行时间: {avg_exec_time*1000:.3f}毫秒")
                print(f"并发处理能力: {concurrent_tasks/total_duration:.0f}次/秒")
                
                # 性能要求
                assert success_rate >= 99.0, f"异步成功率过低: {success_rate:.1f}%"
                assert avg_exec_time < 0.01, f"异步平均执行时间过长: {avg_exec_time*1000:.3f}毫秒"
    
    def test_memory_usage_under_load(self, weight_service, large_department_dataset):
        """测试高负载下的内存使用情况"""
        import psutil
        import os
        
        process = psutil.Process(os.getpid())
        
        with patch.object(weight_service, 'get_available_departments_with_weights') as mock_get_depts:
            mock_get_depts.return_value = large_department_dataset
            
            # 记录初始内存使用
            initial_memory = process.memory_info().rss / 1024 / 1024  # MB
            
            # 大量选择操作
            for _ in range(10000):
                weight_service.select_department_by_weight(merchant_id=1)
            
            # 记录最终内存使用
            final_memory = process.memory_info().rss / 1024 / 1024  # MB
            memory_increase = final_memory - initial_memory
            
            print(f"\n内存使用测试结果:")
            print(f"初始内存: {initial_memory:.1f}MB")
            print(f"最终内存: {final_memory:.1f}MB")
            print(f"内存增长: {memory_increase:.1f}MB")
            
            # 内存增长应该控制在合理范围内（小于50MB）
            assert memory_increase < 50, f"内存增长过多: {memory_increase:.1f}MB"


if __name__ == '__main__':
    pytest.main([__file__, '-v', '-s'])
