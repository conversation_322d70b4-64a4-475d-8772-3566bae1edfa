from typing import Any, Dict, List, Union
from fastapi import APIRouter, Request, HTTPException, status
from pydantic import BaseModel, Field, validator
import json
import re
from datetime import datetime

from app.core.logging import get_logger

router = APIRouter()
logger = get_logger("public_api")

# 危险关键词列表 - 防止代码执行攻击
DANGEROUS_KEYWORDS = [
    # Python 执行相关
    'exec', 'eval', 'compile', '__import__', 'globals', 'locals', 'vars',
    'dir', 'getattr', 'setattr', 'delattr', 'hasattr',
    # 系统命令相关
    'os.system', 'subprocess', 'popen', 'shell', 'cmd', 'command',
    # 文件操作相关
    'open', 'file', 'read', 'write', 'delete', 'remove', 'unlink',
    # 网络相关
    'urllib', 'requests', 'socket', 'http', 'ftp', 'ssh',
    # 数据库相关
    'drop', 'delete', 'truncate', 'alter', 'create', 'insert', 'update',
    # 脚本标签
    '<script', '</script>', 'javascript:', 'vbscript:', 'onload', 'onerror',
    # SQL注入相关
    'union', 'select', 'from', 'where', 'order by', 'group by',
    # 其他危险操作
    'pickle', 'marshal', 'base64', 'decode', 'encode'
]

# 最大参数限制
MAX_PARAM_COUNT = 50  # 最大参数数量
MAX_PARAM_SIZE = 1024  # 单个参数最大字符数
MAX_TOTAL_SIZE = 10240  # 总参数大小限制


class PublicEchoRequest(BaseModel):
    """公开回显请求模型"""
    data: Dict[str, Any] = Field(..., description="要回显的数据")
    
    @validator('data')
    def validate_data(cls, v):
        """验证数据安全性"""
        if not isinstance(v, dict):
            raise ValueError("数据必须是字典格式")
        
        # 检查参数数量
        if len(v) > MAX_PARAM_COUNT:
            raise ValueError(f"参数数量不能超过 {MAX_PARAM_COUNT} 个")
        
        # 检查总大小
        total_size = len(json.dumps(v, ensure_ascii=False))
        if total_size > MAX_TOTAL_SIZE:
            raise ValueError(f"参数总大小不能超过 {MAX_TOTAL_SIZE} 字符")
        
        # 递归检查所有值
        cls._check_value_safety(v)
        
        return v
    
    @classmethod
    def _check_value_safety(cls, obj: Any, depth: int = 0) -> None:
        """递归检查值的安全性"""
        # 防止过深嵌套
        if depth > 10:
            raise ValueError("数据嵌套层级不能超过 10 层")
        
        if isinstance(obj, dict):
            for key, value in obj.items():
                cls._check_string_safety(str(key))
                cls._check_value_safety(value, depth + 1)
        elif isinstance(obj, list):
            for item in obj:
                cls._check_value_safety(item, depth + 1)
        elif isinstance(obj, str):
            cls._check_string_safety(obj)
    
    @classmethod
    def _check_string_safety(cls, text: str) -> None:
        """检查字符串安全性"""
        if len(text) > MAX_PARAM_SIZE:
            raise ValueError(f"单个参数长度不能超过 {MAX_PARAM_SIZE} 字符")
        
        # 转换为小写进行检查
        text_lower = text.lower()
        
        # 检查危险关键词
        for keyword in DANGEROUS_KEYWORDS:
            if keyword in text_lower:
                raise ValueError(f"参数包含危险关键词: {keyword}")
        
        # 检查可能的脚本注入
        script_patterns = [
            r'<script[^>]*>.*?</script>',
            r'javascript\s*:',
            r'vbscript\s*:',
            r'on\w+\s*=',
            r'expression\s*\(',
        ]
        
        for pattern in script_patterns:
            if re.search(pattern, text_lower, re.IGNORECASE | re.DOTALL):
                raise ValueError("参数包含可能的脚本注入内容")


class PublicEchoResponse(BaseModel):
    """公开回显响应模型"""
    success: bool = Field(True, description="请求是否成功")
    message: str = Field("数据回显成功", description="响应消息")
    data: Dict[str, Any] = Field(..., description="回显的数据")
    timestamp: datetime = Field(default_factory=datetime.now, description="响应时间")
    request_id: str = Field(..., description="请求ID")


@router.post("/echo", response_model=PublicEchoResponse)
async def public_echo(
    request: Request,
    echo_request: PublicEchoRequest
) -> PublicEchoResponse:
    """
    公开回显接口
    
    这是一个完全公开的接口，不需要任何认证或权限验证。
    接收客户端发送的数据并原样返回，但会过滤危险内容。
    
    安全措施：
    - 过滤危险关键词，防止代码执行攻击
    - 限制参数数量和大小，防止DoS攻击
    - 检查脚本注入，防止XSS攻击
    - 限制嵌套深度，防止递归攻击
    """
    # 生成请求ID
    import uuid
    request_id = str(uuid.uuid4())
    
    # 获取客户端信息
    client_ip = request.client.host
    user_agent = request.headers.get("user-agent", "Unknown")
    
    # 记录请求日志
    logger.info(
        f"公开回显请求 - 请求ID: {request_id}, "
        f"客户端IP: {client_ip}, "
        f"User-Agent: {user_agent}, "
        f"参数数量: {len(echo_request.data)}"
    )
    
    try:
        # 返回回显数据
        response = PublicEchoResponse(
            data=echo_request.data,
            request_id=request_id
        )
        
        logger.info(f"公开回显成功 - 请求ID: {request_id}")
        return response
        
    except Exception as e:
        logger.error(f"公开回显失败 - 请求ID: {request_id}, 错误: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"处理请求失败: {str(e)}"
        )


@router.get("/echo", response_model=Dict[str, Any])
async def public_echo_get(
    request: Request
) -> Dict[str, Any]:
    """
    公开回显接口 (GET方法)
    
    通过URL查询参数接收数据并返回。
    相比POST方法，GET方法的参数限制更严格。
    """
    # 生成请求ID
    import uuid
    request_id = str(uuid.uuid4())
    
    # 获取客户端信息
    client_ip = request.client.host
    user_agent = request.headers.get("user-agent", "Unknown")
    
    # 获取查询参数
    query_params = dict(request.query_params)
    
    # 记录请求日志
    logger.info(
        f"公开回显GET请求 - 请求ID: {request_id}, "
        f"客户端IP: {client_ip}, "
        f"User-Agent: {user_agent}, "
        f"参数数量: {len(query_params)}"
    )
    
    try:
        # 验证参数安全性
        if len(query_params) > MAX_PARAM_COUNT:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"参数数量不能超过 {MAX_PARAM_COUNT} 个"
            )
        
        # 检查每个参数
        for key, value in query_params.items():
            PublicEchoRequest._check_string_safety(key)
            PublicEchoRequest._check_string_safety(value)
        
        # 返回结果
        response = {
            "success": True,
            "message": "GET参数回显成功",
            "data": query_params,
            "timestamp": datetime.now(),
            "request_id": request_id
        }
        
        logger.info(f"公开回显GET成功 - 请求ID: {request_id}")
        return response
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"公开回显GET失败 - 请求ID: {request_id}, 错误: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"处理请求失败: {str(e)}"
        )


@router.get("/ping")
async def public_ping() -> Dict[str, Any]:
    """
    简单的ping接口，用于测试服务可用性
    """
    return {
        "success": True,
        "message": "pong",
        "timestamp": datetime.now(),
        "service": "walmart-bind-card-server"
    }
