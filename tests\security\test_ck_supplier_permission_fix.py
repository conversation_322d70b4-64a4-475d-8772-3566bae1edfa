#!/usr/bin/env python3
"""
CK供应商权限修复验证测试

测试目标：
1. 验证CK供应商无法访问部门列表API
2. 验证CK供应商可以访问专用的部门信息API
3. 验证权限隔离效果
"""

import pytest
import requests
import json
from typing import Dict, Any

class TestCKSupplierPermissionFix:
    """CK供应商权限修复测试类"""

    def __init__(self):
        self.base_url = "http://localhost:8000"
        self.admin_token = None
        self.merchant_token = None
        self.ck_supplier_token = None

    def setup_method(self):
        """测试前准备"""
        # 获取各种角色的token
        self.admin_token = self._login("admin", "7c222fb2927d828af22f592134e8932480637c0d")
        self.merchant_token = self._login("test1", "12345678")
        self.ck_supplier_token = self._login("ck_supplier", "12345678")

    def _login(self, username: str, password: str) -> str:
        """登录获取token"""
        try:
            # 使用form-data格式，符合OAuth2PasswordRequestForm要求
            form_data = {
                "username": username,
                "password": password
            }
            response = requests.post(
                f"{self.base_url}/api/v1/auth/login",
                data=form_data  # 使用data而不是json
            )
            if response.status_code == 200:
                data = response.json()
                # 检查响应格式，可能在data字段中
                token = data.get("access_token") or data.get("data", {}).get("access_token")
                return token
            else:
                print(f"登录失败 {username}: {response.status_code} - {response.text}")
                return None
        except Exception as e:
            print(f"登录异常 {username}: {e}")
            return None

    def _make_request(self, method: str, endpoint: str, token: str, **kwargs) -> requests.Response:
        """发送HTTP请求"""
        headers = {"Authorization": f"Bearer {token}"} if token else {}
        url = f"{self.base_url}{endpoint}"

        if method.upper() == "GET":
            return requests.get(url, headers=headers, **kwargs)
        elif method.upper() == "POST":
            return requests.post(url, headers=headers, **kwargs)
        elif method.upper() == "PUT":
            return requests.put(url, headers=headers, **kwargs)
        elif method.upper() == "DELETE":
            return requests.delete(url, headers=headers, **kwargs)

    def test_admin_can_access_departments(self):
        """测试超级管理员可以访问部门列表API"""
        if not self.admin_token:
            pytest.skip("超级管理员登录失败")

        response = self._make_request("GET", "/api/v1/departments", self.admin_token)

        print(f"超级管理员访问部门列表: {response.status_code}")
        print(f"响应内容: {response.text}")

        assert response.status_code == 200, f"超级管理员应该能访问部门列表，但返回: {response.status_code}"

        data = response.json()
        # 检查不同的响应格式
        if data.get("success") is True or data.get("code") == 0 or "items" in data:
            print("✅ 超级管理员可以正常访问部门列表API")
        else:
            print(f"响应格式检查失败: {data}")
            assert False, f"响应格式不符合预期: {data}"

    def test_merchant_admin_can_access_departments(self):
        """测试商户管理员可以访问部门列表API"""
        if not self.merchant_token:
            pytest.skip("商户管理员登录失败")

        response = self._make_request("GET", "/api/v1/departments", self.merchant_token)

        print(f"商户管理员访问部门列表: {response.status_code}")
        print(f"响应内容: {response.text}")

        assert response.status_code == 200, f"商户管理员应该能访问部门列表，但返回: {response.status_code}"

        data = response.json()
        # 检查不同的响应格式
        if data.get("success") is True or data.get("code") == 0 or "items" in data:
            print("✅ 商户管理员可以正常访问部门列表API")
        else:
            print(f"响应格式检查失败: {data}")
            assert False, f"响应格式不符合预期: {data}"

    def test_ck_supplier_cannot_access_departments(self):
        """🔒 核心测试：验证CK供应商无法访问部门列表API"""
        if not self.ck_supplier_token:
            pytest.skip("CK供应商登录失败")

        response = self._make_request("GET", "/api/v1/departments", self.ck_supplier_token)

        print(f"CK供应商访问部门列表: {response.status_code}")
        print(f"响应内容: {response.text}")

        # CK供应商应该被拒绝访问
        assert response.status_code == 403, f"CK供应商应该被拒绝访问部门列表，但返回: {response.status_code}"

        data = response.json()
        error_message = data.get("detail", "").lower()
        assert "ck供应商" in error_message or "权限" in error_message, "错误信息应该明确说明权限问题"

        print("✅ CK供应商已被正确阻止访问部门列表API")

    def test_ck_supplier_can_access_my_department(self):
        """测试CK供应商可以访问专用的部门信息API"""
        if not self.ck_supplier_token:
            pytest.skip("CK供应商登录失败")

        response = self._make_request("GET", "/api/v1/departments/my-department", self.ck_supplier_token)

        print(f"CK供应商访问专用API: {response.status_code}")
        print(f"响应内容: {response.text}")

        # CK供应商应该能访问专用API
        assert response.status_code == 200, f"CK供应商应该能访问专用API，但返回: {response.status_code}"

        data = response.json()
        assert data.get("success") is True, "响应应该成功"

        # 验证返回的部门信息
        dept_data = data.get("data")
        if dept_data:
            assert "id" in dept_data, "应该包含部门ID"
            assert "name" in dept_data, "应该包含部门名称"
            print(f"✅ CK供应商可以正常访问专用API，获取到部门: {dept_data.get('name')}")
        else:
            print("✅ CK供应商访问专用API成功，但未分配部门")

    def test_non_ck_supplier_cannot_access_my_department(self):
        """测试非CK供应商无法访问专用API"""
        if not self.merchant_token:
            pytest.skip("商户管理员登录失败")

        response = self._make_request("GET", "/api/v1/departments/my-department", self.merchant_token)

        print(f"商户管理员访问CK专用API: {response.status_code}")

        # 非CK供应商应该被拒绝访问
        assert response.status_code == 403, f"非CK供应商应该被拒绝访问专用API，但返回: {response.status_code}"

        data = response.json()
        error_message = data.get("detail", "").lower()
        assert "ck供应商" in error_message, "错误信息应该明确说明仅限CK供应商"

        print("✅ 非CK供应商已被正确阻止访问CK专用API")

    def test_permission_boundary_isolation(self):
        """测试权限边界隔离效果"""
        print("\n=== 权限边界隔离测试 ===")

        # 测试各种敏感API端点
        sensitive_endpoints = [
            "/api/v1/departments",
            "/api/v1/departments/1",
            "/api/v1/users",
            "/api/v1/merchants"
        ]

        if not self.ck_supplier_token:
            pytest.skip("CK供应商登录失败")

        blocked_count = 0
        for endpoint in sensitive_endpoints:
            try:
                response = self._make_request("GET", endpoint, self.ck_supplier_token)
                if response.status_code == 403:
                    blocked_count += 1
                    print(f"✅ {endpoint}: 正确阻止 (403)")
                elif response.status_code == 404:
                    print(f"⚠️  {endpoint}: 资源不存在 (404)")
                else:
                    print(f"❌ {endpoint}: 意外允许访问 ({response.status_code})")
            except Exception as e:
                print(f"⚠️  {endpoint}: 请求异常 - {e}")

        print(f"权限隔离效果: {blocked_count}/{len(sensitive_endpoints)} 个端点被正确阻止")

    def run_all_tests(self):
        """运行所有测试"""
        print("🔒 开始CK供应商权限修复验证测试")
        print("=" * 50)

        try:
            self.setup_method()

            # 基础权限测试
            print("\n1. 测试超级管理员权限...")
            self.test_admin_can_access_departments()

            print("\n2. 测试商户管理员权限...")
            self.test_merchant_admin_can_access_departments()

            print("\n3. 🔒 测试CK供应商权限限制...")
            self.test_ck_supplier_cannot_access_departments()

            print("\n4. 测试CK供应商专用API...")
            self.test_ck_supplier_can_access_my_department()

            print("\n5. 测试专用API访问控制...")
            self.test_non_ck_supplier_cannot_access_my_department()

            print("\n6. 测试权限边界隔离...")
            self.test_permission_boundary_isolation()

            print("\n" + "=" * 50)
            print("🎉 所有测试完成！CK供应商权限修复验证通过")

        except Exception as e:
            print(f"\n❌ 测试失败: {e}")
            raise


if __name__ == "__main__":
    # 直接运行测试
    tester = TestCKSupplierPermissionFix()
    tester.run_all_tests()
