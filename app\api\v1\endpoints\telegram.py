"""
Telegram Bot API 端点
"""

from typing import Optional
from fastapi import APIRouter, Depends, HTTPException, Request, Header, status
from sqlalchemy.orm import Session

from app.api import deps
from app.models.user import User
from app.core.logging import get_logger
from app.telegram_bot.services.bot_service import get_bot_service
from app.telegram_bot.webhook_tester import test_webhook_config

logger = get_logger(__name__)

router = APIRouter()


@router.post("/webhook")
async def telegram_webhook(
    request: Request,
    x_telegram_bot_api_secret_token: Optional[str] = Header(None),
    db: Session = Depends(deps.get_db)
):
    """
    处理Telegram Webhook请求
    注意：此接口不需要JWT token验证，使用Telegram secret token进行安全验证
    """
    try:
        # 获取请求体
        body = await request.body()
        update_data = await request.json()

        # 获取机器人服务
        bot_service = get_bot_service()
        if not bot_service.is_running():
            raise HTTPException(
                status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
                detail="Telegram机器人服务未运行"
            )

        # 验证Telegram webhook签名（如果配置了secret token）
        if bot_service.webhook_handler:
            # 使用webhook_handler进行签名验证
            if not bot_service.webhook_handler.verify_webhook_signature(
                body, x_telegram_bot_api_secret_token or ""
            ):
                logger.warning("Telegram webhook签名验证失败")
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="Webhook签名验证失败"
                )

        # 处理更新
        success = await bot_service.process_update(update_data)
        if not success:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="处理Webhook更新失败"
            )

        logger.info(f"成功处理Telegram webhook更新: {update_data.get('update_id', 'unknown')}")

        return {"processed": True}
        
    except Exception as e:
        logger.error(f"处理Telegram Webhook失败: {e}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Webhook处理失败"
        )


@router.get("/status")
async def get_bot_status(
    current_user: User = Depends(deps.get_current_user),
    db: Session = Depends(deps.get_db)
):
    """
    获取机器人状态
    """
    try:
        # 检查权限
        if not current_user.has_permission("api:telegram:config"):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="权限不足"
            )

        # 获取机器人服务状态
        bot_service = get_bot_service()
        status_info = bot_service.get_status()

        return status_info

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取机器人状态失败: {e}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取状态失败"
        )


@router.post("/start")
async def start_bot(
    current_user: User = Depends(deps.get_current_user),
    db: Session = Depends(deps.get_db)
):
    """
    启动机器人服务
    """
    try:
        # 检查权限：仅允许超级管理员
        if not current_user.is_superuser:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="仅超级管理员可以管理Telegram机器人"
            )

        # 启动机器人服务
        logger.info(f"用户 {current_user.username} 请求启动Telegram机器人服务")

        bot_service = get_bot_service()

        # 检查是否已经在运行
        if bot_service.is_running():
            return {"running": True}

        # 启动服务
        success = await bot_service.start()
        if not success:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="机器人服务启动失败"
            )

        return {"running": True}

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"启动机器人服务失败: {e}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="启动服务失败"
        )


@router.post("/stop")
async def stop_bot(
    current_user: User = Depends(deps.get_current_user),
    db: Session = Depends(deps.get_db)
):
    """
    停止机器人服务
    """
    try:
        # 检查权限
        if not current_user.has_permission("api:telegram:config"):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="权限不足"
            )
        
        # 停止机器人服务
        bot_service = get_bot_service()
        
        if not bot_service.is_running():
            return {"running": False}
        
        if not await bot_service.stop():
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="机器人服务停止失败"
            )
        
        return {"running": False}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"停止机器人服务失败: {e}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="停止服务失败"
        )


@router.post("/reload-config")
async def reload_config(
    current_user: User = Depends(deps.get_current_user),
    db: Session = Depends(deps.get_db)
):
    """
    重新加载配置
    """
    try:
        # 检查权限：仅允许超级管理员
        if not current_user.is_superuser:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="仅超级管理员可以管理Telegram机器人"
            )

        # 重新加载配置
        bot_service = get_bot_service()
        success = await bot_service.reload_config()

        if not success:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="重新加载配置失败"
            )

        return {"reloaded": True}

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"重新加载配置失败: {e}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="重新加载配置失败"
        )


@router.post("/reload-keywords")
async def reload_keywords(
    current_user: User = Depends(deps.get_current_user),
    db: Session = Depends(deps.get_db)
):
    """
    重新加载关键词配置
    """
    try:
        # 检查权限：仅允许超级管理员
        if not current_user.is_superuser:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="仅超级管理员可以管理Telegram机器人"
            )

        # 重新加载关键词
        from app.telegram_bot.keywords import get_telegram_keywords
        keywords_manager = get_telegram_keywords()
        keywords_manager.reload_keywords()

        logger.info(f"用户 {current_user.username} 重新加载了Telegram机器人关键词配置")

        return {
            "reloaded": True,
            "total_keywords": len(keywords_manager.get_keywords()),
            "message": "关键词配置已重新加载"
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"重新加载关键词失败: {e}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="重新加载关键词失败"
        )


@router.get("/keywords")
async def get_keywords(
    current_user: User = Depends(deps.get_current_user),
    db: Session = Depends(deps.get_db)
):
    """
    获取所有关键词配置
    """
    try:
        # 检查权限：仅允许超级管理员
        if not current_user.is_superuser:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="仅超级管理员可以管理Telegram机器人"
            )

        # 获取关键词配置
        from app.telegram_bot.keywords import get_telegram_keywords
        keywords_manager = get_telegram_keywords()

        return {
            "categories": keywords_manager.get_keywords_by_category(),
            "statistics": keywords_manager.get_statistics()
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取关键词配置失败: {e}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取关键词配置失败"
        )


@router.get("/keywords/test")
async def test_keyword_match(
    message_text: str,
    current_user: User = Depends(deps.get_current_user)
):
    """
    测试关键词匹配
    """
    try:
        # 检查权限：仅允许超级管理员
        if not current_user.is_superuser:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="仅超级管理员可以管理Telegram机器人"
            )

        # 测试关键词匹配
        from app.telegram_bot.keywords import check_keyword_match
        is_match, matched_keywords = check_keyword_match(message_text)

        return {
            "message_text": message_text,
            "is_match": is_match,
            "matched_keywords": matched_keywords
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"测试关键词匹配失败: {e}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="测试关键词匹配失败"
        )


@router.get("/bot-info")
async def get_bot_info(
    current_user: User = Depends(deps.get_current_user),
    db: Session = Depends(deps.get_db)
):
    """
    获取机器人信息
    """
    try:
        # 检查权限：仅允许超级管理员
        if not current_user.is_superuser:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="仅超级管理员可以管理Telegram机器人"
            )
        
        # 获取机器人信息
        bot_service = get_bot_service()
        bot_info = bot_service.get_bot_info()

        if not bot_info:
            raise HTTPException(
                status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
                detail="机器人服务未运行或未初始化"
            )

        # 检查机器人状态
        bot_status = bot_info.get("status", "unknown")
        if bot_status == "error":
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=bot_info.get("message", "获取机器人信息时发生错误")
            )
        elif bot_status in ["not_configured", "not_started"]:
            # 这些状态不是错误，而是正常的状态信息
            return bot_info

        return bot_info
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取机器人信息失败: {e}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取机器人信息失败"
        )


@router.get("/privacy-guide")
async def get_privacy_guide(
    current_user: User = Depends(deps.get_current_user)
):
    """
    获取机器人隐私设置指南
    """
    try:
        # 检查权限：仅允许超级管理员
        if not current_user.is_superuser:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="仅超级管理员可以管理Telegram机器人"
            )

        # 获取机器人信息
        bot_service = get_bot_service()
        bot_info = bot_service.get_bot_info()

        current_privacy_status = "开启" if not bot_info.get("can_read_all_group_messages", False) else "关闭"

        guide = {
            "current_status": {
                "can_read_all_group_messages": bot_info.get("can_read_all_group_messages", False),
                "privacy_mode": current_privacy_status,
                "description": f"当前隐私模式：{current_privacy_status}"
            },
            "how_to_change": {
                "method": "通过 BotFather 设置",
                "steps": [
                    "1. 在 Telegram 中找到 @BotFather",
                    "2. 发送 /mybots 命令",
                    "3. 选择你的机器人 @walmart_bind_card_bot",
                    "4. 选择 'Bot Settings'",
                    "5. 选择 'Group Privacy'",
                    "6. 选择 'Turn off' 来关闭隐私模式（允许读取所有群组消息）",
                    "7. 或选择 'Turn on' 来开启隐私模式（只能读取命令和回复）"
                ],
                "botfather_url": "https://t.me/BotFather"
            },
            "explanation": {
                "privacy_on": {
                    "description": "隐私模式开启时",
                    "can_read": [
                        "直接发给机器人的私聊消息",
                        "群组中以 / 开头的命令",
                        "回复机器人消息的消息",
                        "通过内联键盘触发的回调"
                    ],
                    "cannot_read": [
                        "群组中的普通聊天消息",
                        "不是命令的普通文本"
                    ]
                },
                "privacy_off": {
                    "description": "隐私模式关闭时",
                    "can_read": [
                        "群组中的所有消息",
                        "所有类型的消息内容"
                    ],
                    "note": "机器人可以看到群组中的所有消息，但仍需要适当的权限处理"
                }
            },
            "recommendation": "为了让机器人能够更好地服务群组用户，建议关闭隐私模式"
        }

        return guide

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取隐私设置指南失败: {e}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取隐私设置指南失败"
        )


@router.post("/webhook/test")
async def test_webhook_configuration(
    current_user: User = Depends(deps.get_current_user)
):
    """
    测试Webhook配置
    """
    try:
        # 检查权限：仅允许超级管理员
        if not current_user.is_superuser:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="仅超级管理员可以测试Webhook配置"
            )

        # 获取机器人服务和配置
        bot_service = get_bot_service()
        if not bot_service or not bot_service.config:
            raise HTTPException(
                status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
                detail="机器人服务未初始化"
            )

        # 运行webhook配置测试
        test_results = await test_webhook_config(bot_service.config)

        logger.info(f"用户 {current_user.username} 执行了Webhook配置测试")

        return {
            "test_results": test_results,
            "message": "Webhook配置测试完成"
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"测试Webhook配置失败: {e}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="测试Webhook配置失败"
        )
