from typing import List, Optional, Dict, Any
from datetime import datetime
from pydantic import BaseModel, Field


class BindingStepDetail(BaseModel):
    """绑卡步骤详情"""
    step_name: str = Field(..., description="步骤名称")
    step_type: str = Field(..., description="步骤类型")
    log_level: str = Field(..., description="日志级别")
    message: str = Field(..., description="步骤消息")
    start_time: datetime = Field(..., description="开始时间")
    end_time: Optional[datetime] = Field(None, description="结束时间")
    duration_ms: Optional[float] = Field(None, description="执行耗时(毫秒)")
    duration_formatted: Optional[str] = Field(None, description="格式化的耗时")
    status: str = Field(..., description="步骤状态: success/failed/running")
    details: Optional[Dict[str, Any]] = Field(None, description="详细信息")
    request_data: Optional[Dict[str, Any]] = Field(None, description="请求数据")
    response_data: Optional[Dict[str, Any]] = Field(None, description="响应数据")
    attempt_number: Optional[str] = Field(None, description="尝试序号")
    walmart_ck_id: Optional[int] = Field(None, description="使用的CK ID")
    error_message: Optional[str] = Field(None, description="错误信息")


class BindingTimelineResponse(BaseModel):
    """绑卡时间线响应"""
    card_record_id: str = Field(..., description="绑卡记录ID")
    card_number: str = Field(..., description="卡号（脱敏）")
    total_duration: Optional[float] = Field(None, description="总耗时(秒)")
    total_duration_ms: Optional[float] = Field(None, description="总耗时(毫秒)")
    total_duration_formatted: Optional[str] = Field(None, description="格式化的总耗时")
    overall_status: str = Field(..., description="整体状态")
    start_time: datetime = Field(..., description="开始时间")
    end_time: Optional[datetime] = Field(None, description="结束时间")
    steps: List[BindingStepDetail] = Field(..., description="步骤列表")
    summary: Dict[str, Any] = Field(..., description="统计摘要")


class BindingStepSummary(BaseModel):
    """绑卡步骤统计摘要"""
    total_steps: int = Field(..., description="总步骤数")
    completed_steps: int = Field(..., description="已完成步骤数")
    failed_steps: int = Field(..., description="失败步骤数")
    running_steps: int = Field(..., description="运行中步骤数")
    total_attempts: int = Field(..., description="总尝试次数")
    success_rate: float = Field(..., description="成功率")
    average_step_duration_ms: Optional[float] = Field(None, description="平均步骤耗时(毫秒)")
    longest_step_duration_ms: Optional[float] = Field(None, description="最长步骤耗时(毫秒)")
    longest_step_name: Optional[str] = Field(None, description="最长步骤名称")


class BindingPerformanceAnalysis(BaseModel):
    """绑卡性能分析"""
    card_record_id: str = Field(..., description="绑卡记录ID")
    timeline: BindingTimelineResponse = Field(..., description="时间线详情")
    performance_metrics: Dict[str, Any] = Field(..., description="性能指标")
    bottlenecks: List[Dict[str, Any]] = Field(..., description="性能瓶颈")
    recommendations: List[str] = Field(..., description="优化建议")


class StepTypeStatistics(BaseModel):
    """步骤类型统计"""
    step_type: str = Field(..., description="步骤类型")
    count: int = Field(..., description="数量")
    total_duration_ms: float = Field(..., description="总耗时(毫秒)")
    average_duration_ms: float = Field(..., description="平均耗时(毫秒)")
    success_count: int = Field(..., description="成功次数")
    failure_count: int = Field(..., description="失败次数")
    success_rate: float = Field(..., description="成功率")
