"""
角色管理API - 基于RoleService的完整实现
"""

from typing import List, Optional, Any, Dict
from fastapi import APIRouter, Depends, HTTPException, Query, Path, Body, status
from sqlalchemy.orm import Session

from app.api import deps
from app.models.user import User
from app.schemas.role import (
    RoleCreate,
    RoleUpdate,
    RoleInDB,
    RolePermissions,
    RoleMenus,
)
from app.services.role_service_new import RoleService
from app.services.permission_service import PermissionService
from app.core.exceptions import BusinessException
from app.core.logging import get_logger

router = APIRouter()
logger = get_logger("roles_api")


@router.get("", response_model=Dict[str, Any])
async def read_roles(
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_active_user),
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(20, ge=1, le=100, description="每页数量"),
    name: Optional[str] = Query(None, description="角色名称（模糊查询）"),
    code: Optional[str] = Query(None, description="角色代码（模糊查询）"),
    is_enabled: Optional[bool] = Query(None, description="是否启用"),
    is_system: Optional[bool] = Query(None, description="是否系统角色"),
):
    """
    获取角色列表

    权限要求:
    - "api:roles:read": 查看角色权限
    """
    try:
        # 初始化服务
        role_service = RoleService(db)
        permission_service = PermissionService(db)

        # 权限检查 - 使用数据库中配置的权限代码，避免硬编码
        has_permission = permission_service.check_user_permission(
            current_user, "api:roles:read"
        )
        if not has_permission:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="没有查看角色的权限",
            )

        # 构建查询条件
        query = db.query(role_service.model)

        # 过滤掉超级管理员角色（保护系统安全）
        query = query.filter(role_service.model.code != 'super_admin')

        if name:
            query = query.filter(role_service.model.name.like(f"%{name}%"))
        if code:
            query = query.filter(role_service.model.code.like(f"%{code}%"))
        if is_enabled is not None:
            query = query.filter(role_service.model.is_enabled == is_enabled)
        if is_system is not None:
            query = query.filter(role_service.model.is_system == is_system)

        # 分页
        total = query.count()
        roles = query.offset((page - 1) * page_size).limit(page_size).all()

        return {
            "items": [role.to_dict() for role in roles],
            "total": total,
            "page": page,
            "page_size": page_size,
            "pages": (total + page_size - 1) // page_size,
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取角色列表失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取角色列表失败: {str(e)}",
        )


@router.post("", response_model=Dict[str, Any])
async def create_role(
    *,
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_active_user),
    role_in: RoleCreate,
):
    """
    创建角色

    权限要求:
    - "api:roles:create": 创建角色权限
    """
    try:
        # 初始化服务
        role_service = RoleService(db)
        permission_service = PermissionService(db)

        # 权限检查 - 使用数据库中配置的权限代码，避免硬编码
        has_permission = permission_service.check_user_permission(
            current_user, "api:roles:create"
        )
        if not has_permission:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="没有创建角色的权限",
            )

        # 创建角色
        role = role_service.create_role(role_in, current_user)

        return {"success": True, "data": role.to_dict(), "message": "创建角色成功"}

    except HTTPException:
        raise
    except ValueError as e:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))
    except Exception as e:
        logger.error(f"创建角色失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"创建角色失败: {str(e)}",
        )


@router.get("/{role_id}", response_model=Dict[str, Any])
async def read_role(
    role_id: int,
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_active_user),
    include_permissions: bool = Query(False, description="是否包含权限"),
    include_menus: bool = Query(False, description="是否包含菜单"),
):
    """
    获取角色详情

    权限要求:
    - "api:roles:read": 查看角色权限
    """
    try:
        # 初始化服务
        role_service = RoleService(db)
        permission_service = PermissionService(db)

        # 权限检查 - 使用数据库中配置的权限代码，避免硬编码
        has_permission = permission_service.check_user_permission(
            current_user, "api:roles:read"
        )
        if not has_permission:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="没有查看角色的权限",
            )

        # 获取角色
        role = role_service.get(role_id)
        if not role:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND, detail="角色不存在"
            )

        # 构建返回数据
        role_data = role.to_dict()

        if include_permissions:
            permissions = role_service.get_role_permissions(role_id, current_user)
            role_data["permissions"] = [perm.to_dict() for perm in permissions]

        if include_menus:
            menus = role_service.get_role_menus(role_id, current_user)
            role_data["menus"] = [menu.to_dict() for menu in menus]

        return {"success": True, "data": role_data, "message": "获取角色详情成功"}

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取角色详情失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取角色详情失败: {str(e)}",
        )


@router.put("/{role_id}", response_model=Dict[str, Any])
async def update_role(
    role_id: int,
    *,
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_active_user),
    role_in: RoleUpdate,
):
    """
    更新角色信息

    权限要求:
    - "api:roles:update": 更新角色权限
    """
    try:
        # 初始化服务
        role_service = RoleService(db)
        permission_service = PermissionService(db)

        # 权限检查 - 使用数据库中配置的权限代码，避免硬编码
        has_permission = permission_service.check_user_permission(
            current_user, "api:roles:update"
        )
        if not has_permission:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="没有更新角色的权限",
            )

        # 更新角色
        role = role_service.update_role(role_id, role_in, current_user)
        if not role:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND, detail="角色不存在"
            )

        return {"success": True, "data": role.to_dict(), "message": "更新角色成功"}

    except HTTPException:
        raise
    except ValueError as e:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))
    except Exception as e:
        logger.error(f"更新角色失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"更新角色失败: {str(e)}",
        )


@router.delete("/{role_id}", response_model=Dict[str, Any])
async def delete_role(
    role_id: int,
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_active_user),
):
    """
    删除角色

    权限要求:
    - "api:roles:delete": 删除角色权限
    """
    try:
        # 初始化服务
        role_service = RoleService(db)
        permission_service = PermissionService(db)

        # 权限检查 - 使用数据库中配置的权限代码，避免硬编码
        has_permission = permission_service.check_user_permission(
            current_user, "api:roles:delete"
        )
        if not has_permission:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="没有删除角色的权限",
            )

        # 删除角色
        success = role_service.delete_role(role_id, current_user)
        if not success:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND, detail="角色不存在"
            )

        return {"success": True, "data": None, "message": "删除角色成功"}

    except HTTPException:
        raise
    except ValueError as e:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))
    except Exception as e:
        logger.error(f"删除角色失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"删除角色失败: {str(e)}",
        )


@router.get("/{role_id}/permissions", response_model=Dict[str, Any])
async def get_role_permissions(
    role_id: int,
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_active_user),
):
    """
    获取角色权限列表

    权限要求:
    - "api:roles:read": 查看角色权限
    """
    try:
        # 初始化服务
        role_service = RoleService(db)
        permission_service = PermissionService(db)

        # 权限检查 - 使用数据库中配置的权限代码，避免硬编码
        has_permission = permission_service.check_user_permission(
            current_user, "api:roles:read"
        )
        if not has_permission:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="没有查看角色权限的权限",
            )

        # 获取角色权限
        permissions = role_service.get_role_permissions(role_id, current_user)

        return {
            "success": True,
            "data": {
                "role_id": role_id,
                "permissions": [perm.to_dict() for perm in permissions],
            },
            "message": "获取角色权限成功",
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取角色权限失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取角色权限失败: {str(e)}",
        )


@router.put("/{role_id}/permissions", response_model=Dict[str, Any])
async def update_role_permissions(
    role_id: int,
    *,
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_active_user),
    permission_ids: List[int],
):
    """
    批量更新角色权限

    权限要求:
    - 超级管理员权限（仅超级管理员可以修改角色权限）
    """
    try:
        # 初始化服务
        role_service = RoleService(db)

        # 权限检查 - 仅超级管理员可以修改角色权限，避免硬编码
        if not current_user.is_superuser:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="只有超级管理员可以修改角色权限",
            )

        # 批量分配权限
        success = role_service.batch_assign_permissions(
            role_id, permission_ids, current_user
        )
        if not success:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST, detail="权限分配失败"
            )

        # 获取更新后的权限列表
        permissions = role_service.get_role_permissions(role_id, current_user)

        return {
            "success": True,
            "data": {
                "role_id": role_id,
                "permissions": [perm.to_dict() for perm in permissions],
            },
            "message": "更新角色权限成功",
        }

    except HTTPException:
        raise
    except ValueError as e:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))
    except Exception as e:
        logger.error(f"更新角色权限失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"更新角色权限失败: {str(e)}",
        )


@router.get("/{role_id}/menus", response_model=Dict[str, Any])
async def get_role_menus(
    role_id: int,
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_active_user),
):
    """
    获取角色菜单列表

    权限要求:
    - "api:roles:read": 查看角色权限
    """
    try:
        # 初始化服务
        role_service = RoleService(db)
        permission_service = PermissionService(db)

        # 权限检查 - 使用数据库中配置的权限代码，避免硬编码
        has_permission = permission_service.check_user_permission(
            current_user, "api:roles:read"
        )
        if not has_permission:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="没有查看角色菜单的权限",
            )

        # 获取角色菜单
        menus = role_service.get_role_menus(role_id, current_user)

        return {"role_id": role_id, "menus": [menu.to_dict() for menu in menus]}

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取角色菜单失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取角色菜单失败: {str(e)}",
        )


@router.put("/{role_id}/menus", response_model=Dict[str, Any])
async def update_role_menus(
    role_id: int,
    request_data: Dict[str, Any] = Body(..., description="包含菜单ID列表的请求数据"),
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_active_user),
):
    """
    更新角色菜单列表

    权限要求:
    - "api:roles:update": 编辑角色权限
    """
    try:
        # 初始化服务
        role_service = RoleService(db)
        permission_service = PermissionService(db)

        # 权限检查 - 使用数据库中配置的权限代码，避免硬编码
        has_permission = permission_service.check_user_permission(
            current_user, "api:roles:update"
        )
        if not has_permission:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="没有编辑角色菜单的权限",
            )

        # 从请求数据中提取菜单ID列表
        menus = request_data.get("menus", [])
        if not isinstance(menus, list):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="menus字段必须是一个列表",
            )

        # 更新角色菜单
        success = role_service.batch_assign_menus(role_id, menus, current_user)

        if not success:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="更新角色菜单失败",
            )

        # 获取更新后的菜单列表
        updated_menus = role_service.get_role_menus(role_id, current_user)

        return {
            "role_id": role_id,
            "menus": [menu.to_dict() for menu in updated_menus],
            "message": "角色菜单更新成功"
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"更新角色菜单失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"更新角色菜单失败: {str(e)}",
        )


@router.get("/{role_id}/data-permissions", response_model=Dict[str, Any])
async def get_role_data_permissions(
    role_id: int,
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_active_user),
):
    """
    获取角色数据权限列表

    权限要求:
    - "api:roles:read": 查看角色权限
    """
    try:
        # 初始化服务
        role_service = RoleService(db)
        permission_service = PermissionService(db)

        # 权限检查 - 使用数据库中配置的权限代码，避免硬编码
        has_permission = permission_service.check_user_permission(
            current_user, "api:roles:read"
        )
        if not has_permission:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="没有查看角色数据权限的权限",
            )

        # 获取角色的所有权限
        all_permissions = role_service.get_role_permissions(role_id, current_user)

        # 筛选出数据权限
        data_permissions = [
            perm for perm in all_permissions
            if perm.resource_type == 'data'
        ]

        return {
            "success": True,
            "data": {
                "role_id": role_id,
                "data_permissions": [perm.to_dict() for perm in data_permissions],
            },
            "message": "获取角色数据权限成功",
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取角色数据权限失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取角色数据权限失败: {str(e)}",
        )


@router.put("/{role_id}/data-permissions", response_model=Dict[str, Any])
async def update_role_data_permissions(
    role_id: int,
    request_data: Dict[str, Any] = Body(..., description="包含数据权限ID列表的请求数据"),
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_active_user),
):
    """
    更新角色数据权限列表

    权限要求:
    - 超级管理员权限（仅超级管理员可以修改角色权限）
    """
    try:
        # 初始化服务
        role_service = RoleService(db)

        # 权限检查 - 仅超级管理员可以修改角色权限，避免硬编码
        if not current_user.is_superuser:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="只有超级管理员可以修改角色数据权限",
            )

        # 从请求数据中提取数据权限ID列表
        data_permission_ids = request_data.get("data_permission_ids", [])
        if not isinstance(data_permission_ids, list):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="data_permission_ids字段必须是一个列表",
            )

        # 获取角色当前的所有权限
        current_permissions = role_service.get_role_permissions(role_id, current_user)

        # 分离数据权限和其他权限
        non_data_permission_ids = [
            perm.id for perm in current_permissions
            if perm.resource_type != 'data'
        ]

        # 合并非数据权限和新的数据权限
        all_permission_ids = non_data_permission_ids + data_permission_ids

        # 批量更新权限
        success = role_service.batch_assign_permissions(
            role_id, all_permission_ids, current_user
        )

        if not success:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="更新角色数据权限失败",
            )

        # 获取更新后的数据权限列表
        updated_permissions = role_service.get_role_permissions(role_id, current_user)
        updated_data_permissions = [
            perm for perm in updated_permissions
            if perm.resource_type == 'data'
        ]

        return {
            "success": True,
            "data": {
                "role_id": role_id,
                "data_permissions": [perm.to_dict() for perm in updated_data_permissions],
            },
            "message": "更新角色数据权限成功",
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"更新角色数据权限失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"更新角色数据权限失败: {str(e)}",
        )
