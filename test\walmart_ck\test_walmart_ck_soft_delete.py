"""
沃尔玛CK软删除功能测试
测试软删除功能的完整性和数据一致性
"""

import sys
import os
import time
import requests
import uuid
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from tests.conftest import TestBase, format_test_result, print_test_summary, get_test_accounts


class WalmartCKSoftDeleteTestSuite(TestBase):
    """沃尔玛CK软删除功能测试类"""

    def __init__(self):
        super().__init__()
        self.results = []
        self.test_accounts = get_test_accounts()
        self.admin_token = None
        self.merchant_token = None
        self.test_ck_ids = []  # 存储测试创建的CK ID
        self.deleted_ck_ids = []  # 存储已软删除的CK ID

    def setup_test_environment(self):
        """设置测试环境"""
        print("=== 设置CK软删除测试环境 ===")

        # 登录管理员账号
        self.admin_token = self.login(
            self.test_accounts["super_admin"]["username"],
            self.test_accounts["super_admin"]["password"]
        )

        # 登录商户账号
        self.merchant_token = self.login(
            self.test_accounts["merchant_admin"]["username"],
            self.test_accounts["merchant_admin"]["password"]
        )

        if not self.admin_token:
            raise Exception("无法获取管理员token")
        if not self.merchant_token:
            raise Exception("无法获取商户token")

        print("✅ 测试环境设置完成")

    def create_test_ck(self, token, description_suffix=""):
        """创建测试用的CK"""
        test_data = {
            "sign": f"test_ck_{uuid.uuid4().hex[:8]}@token#signature#26{description_suffix}",
            "daily_limit": 100,
            "hourly_limit": 50,
            "active": 1,
            "description": f"软删除测试CK{description_suffix}",
            "merchant_id": 1,
            "department_id": 1
        }

        status_code, response = self.make_request("POST", "/walmart-ck", token, data=test_data)
        
        if status_code == 201 and response and "data" in response:
            ck_id = response["data"].get("id")
            if ck_id:
                self.test_ck_ids.append(ck_id)
                return ck_id
        
        return None

    def test_soft_delete_functionality(self):
        """测试软删除基本功能"""
        print("\n=== 测试软删除基本功能 ===")

        # 创建测试CK
        ck_id = self.create_test_ck(self.admin_token, "_for_soft_delete")
        if not ck_id:
            self.results.append(format_test_result(
                "创建测试CK",
                False,
                "无法创建测试CK，跳过软删除测试"
            ))
            return

        # 1. 执行软删除
        status_code, response = self.make_request("DELETE", f"/walmart-ck/{ck_id}", self.admin_token)
        
        if status_code == 200:
            self.results.append(format_test_result(
                "执行软删除操作",
                True,
                "软删除操作成功"
            ))
            print("✅ 软删除操作成功")
            self.deleted_ck_ids.append(ck_id)
        else:
            self.results.append(format_test_result(
                "执行软删除操作",
                False,
                f"软删除操作失败，状态码: {status_code}"
            ))
            print(f"❌ 软删除操作失败，状态码: {status_code}")
            return

        # 2. 验证CK在列表中不可见
        status_code, response = self.make_request("GET", "/walmart-ck", self.admin_token)
        
        if status_code == 200 and "data" in response:
            items = response["data"].get("items", [])
            deleted_ck_visible = any(item.get("id") == ck_id for item in items)
            
            if not deleted_ck_visible:
                self.results.append(format_test_result(
                    "已删除CK在列表中不可见",
                    True,
                    "已删除的CK正确地从列表中隐藏"
                ))
                print("✅ 已删除的CK正确地从列表中隐藏")
            else:
                self.results.append(format_test_result(
                    "已删除CK在列表中不可见",
                    False,
                    "已删除的CK仍然在列表中可见"
                ))
                print("❌ 已删除的CK仍然在列表中可见")
        else:
            self.results.append(format_test_result(
                "获取CK列表",
                False,
                f"无法获取CK列表，状态码: {status_code}"
            ))

        # 3. 验证无法获取已删除CK的详情
        status_code, response = self.make_request("GET", f"/walmart-ck/{ck_id}", self.admin_token)
        
        if status_code == 404:
            self.results.append(format_test_result(
                "已删除CK详情不可访问",
                True,
                "已删除的CK详情正确返回404"
            ))
            print("✅ 已删除的CK详情正确返回404")
        else:
            self.results.append(format_test_result(
                "已删除CK详情不可访问",
                False,
                f"已删除的CK详情仍可访问，状态码: {status_code}"
            ))
            print(f"❌ 已删除的CK详情仍可访问，状态码: {status_code}")

    def test_soft_delete_statistics_consistency(self):
        """测试软删除后统计数据的一致性"""
        print("\n=== 测试统计数据一致性 ===")

        # 获取删除前的统计数据
        status_code, before_response = self.make_request("GET", "/walmart-ck/statistics/1", self.admin_token)
        
        if status_code != 200:
            self.results.append(format_test_result(
                "获取删除前统计数据",
                False,
                f"无法获取统计数据，状态码: {status_code}"
            ))
            return

        before_stats = before_response.get("data", {})
        before_total = before_stats.get("total_count", 0)

        # 创建并删除一个CK
        ck_id = self.create_test_ck(self.admin_token, "_for_stats_test")
        if not ck_id:
            self.results.append(format_test_result(
                "创建统计测试CK",
                False,
                "无法创建统计测试CK"
            ))
            return

        # 删除CK
        status_code, _ = self.make_request("DELETE", f"/walmart-ck/{ck_id}", self.admin_token)
        if status_code != 200:
            self.results.append(format_test_result(
                "删除统计测试CK",
                False,
                f"无法删除统计测试CK，状态码: {status_code}"
            ))
            return

        self.deleted_ck_ids.append(ck_id)

        # 获取删除后的统计数据
        status_code, after_response = self.make_request("GET", "/walmart-ck/statistics/1", self.admin_token)
        
        if status_code == 200:
            after_stats = after_response.get("data", {})
            after_total = after_stats.get("total_count", 0)

            # 验证统计数据一致性（删除后总数应该等于删除前的总数）
            if after_total == before_total:
                self.results.append(format_test_result(
                    "统计数据一致性",
                    True,
                    f"统计数据正确，删除前后总数保持一致: {before_total}"
                ))
                print(f"✅ 统计数据正确，删除前后总数保持一致: {before_total}")
            else:
                self.results.append(format_test_result(
                    "统计数据一致性",
                    False,
                    f"统计数据不一致，删除前: {before_total}，删除后: {after_total}"
                ))
                print(f"❌ 统计数据不一致，删除前: {before_total}，删除后: {after_total}")
        else:
            self.results.append(format_test_result(
                "获取删除后统计数据",
                False,
                f"无法获取删除后统计数据，状态码: {status_code}"
            ))

    def test_soft_delete_binding_prevention(self):
        """测试软删除后无法用于绑卡"""
        print("\n=== 测试软删除CK无法用于绑卡 ===")

        # 创建测试CK
        ck_id = self.create_test_ck(self.admin_token, "_for_binding_test")
        if not ck_id:
            self.results.append(format_test_result(
                "创建绑卡测试CK",
                False,
                "无法创建绑卡测试CK"
            ))
            return

        # 软删除CK
        status_code, _ = self.make_request("DELETE", f"/walmart-ck/{ck_id}", self.admin_token)
        if status_code != 200:
            self.results.append(format_test_result(
                "删除绑卡测试CK",
                False,
                f"无法删除绑卡测试CK，状态码: {status_code}"
            ))
            return

        self.deleted_ck_ids.append(ck_id)

        # 尝试获取可用CK（应该不包含已删除的CK）
        # 这里我们通过检查CK列表来验证已删除的CK不会被选择
        status_code, response = self.make_request("GET", "/walmart-ck", self.admin_token)
        
        if status_code == 200 and "data" in response:
            items = response["data"].get("items", [])
            deleted_ck_in_available = any(item.get("id") == ck_id for item in items)
            
            if not deleted_ck_in_available:
                self.results.append(format_test_result(
                    "已删除CK不在可用列表",
                    True,
                    "已删除的CK正确地从可用CK列表中排除"
                ))
                print("✅ 已删除的CK正确地从可用CK列表中排除")
            else:
                self.results.append(format_test_result(
                    "已删除CK不在可用列表",
                    False,
                    "已删除的CK仍在可用CK列表中"
                ))
                print("❌ 已删除的CK仍在可用CK列表中")

    def cleanup_test_data(self):
        """清理测试数据"""
        print("\n=== 清理测试数据 ===")
        
        # 注意：由于是软删除，我们不需要额外清理已删除的CK
        # 只需要清理未删除的测试CK
        remaining_ck_ids = [ck_id for ck_id in self.test_ck_ids if ck_id not in self.deleted_ck_ids]
        
        for ck_id in remaining_ck_ids:
            try:
                status_code, _ = self.make_request("DELETE", f"/walmart-ck/{ck_id}", self.admin_token)
                if status_code == 200:
                    print(f"✅ 清理测试CK: {ck_id}")
                else:
                    print(f"⚠️ 清理测试CK失败: {ck_id}")
            except Exception as e:
                print(f"⚠️ 清理测试CK异常: {ck_id}, 错误: {e}")

        print("✅ 测试数据清理完成")

    def run_all_tests(self):
        """运行所有软删除测试"""
        print("🧪 开始沃尔玛CK软删除功能测试")
        print("="*60)

        start_time = time.time()

        try:
            # 设置测试环境
            self.setup_test_environment()

            # 运行测试
            self.test_soft_delete_functionality()
            self.test_soft_delete_statistics_consistency()
            self.test_soft_delete_binding_prevention()

        except Exception as e:
            self.results.append(format_test_result(
                "测试环境设置",
                False,
                f"测试环境设置失败: {str(e)}"
            ))
            print(f"❌ 测试环境设置失败: {str(e)}")
        finally:
            # 清理测试数据
            self.cleanup_test_data()

        end_time = time.time()
        duration = end_time - start_time

        # 打印测试摘要
        print_test_summary(self.results)
        print(f"测试耗时: {duration:.2f}秒")

        return self.results


def main():
    """主函数"""
    test_suite = WalmartCKSoftDeleteTestSuite()
    results = test_suite.run_all_tests()
    
    # 统计测试结果
    total_tests = len(results)
    passed_tests = sum(1 for result in results if result["status"] == "PASS")
    
    print(f"\n{'='*60}")
    print(f"最终结果: {passed_tests}/{total_tests} 通过")
    print(f"{'='*60}")
    
    # 如果有测试失败，退出码为1
    if passed_tests < total_tests:
        sys.exit(1)


if __name__ == "__main__":
    main()
