"""
CK选择机制性能测试
验证修复后的CK选择机制在高并发场景下的性能表现
"""

import pytest
import asyncio
import time
import statistics
from typing import List, Dict, Any
from sqlalchemy.orm import Session
from concurrent.futures import ThreadPoolExecutor

from app.models.walmart_ck import WalmartCK
from app.models.department import Department
from app.models.merchant import Merchant
from app.services.simplified_ck_service import SimplifiedCKService


class TestCKPerformance:
    """CK选择机制性能测试类"""
    
    @pytest.fixture(autouse=True)
    def setup_performance_test_data(self, db: Session):
        """设置性能测试数据"""
        # 创建测试商户
        merchant = Merchant(
            name="性能测试商户",
            code="PERF_TEST_MERCHANT",
            status=True
        )
        db.add(merchant)
        db.flush()
        
        # 创建多个部门（模拟真实场景）
        departments = []
        for i in range(10):  # 10个部门
            dept = Department(
                merchant_id=merchant.id,
                name=f"性能测试部门{i+1}",
                code=f"PERF_DEPT_{i+1}",
                status=True,
                enable_binding=True,
                binding_weight=100 + i * 10  # 权重从100到190
            )
            db.add(dept)
            departments.append(dept)
        
        db.flush()
        
        # 创建大量CK（模拟生产环境）
        walmart_cks = []
        for dept in departments:
            for j in range(20):  # 每个部门20个CK，总共200个CK
                ck = WalmartCK(
                    merchant_id=merchant.id,
                    department_id=dept.id,
                    sign=f"perf_test_ck_{dept.id}_{j}@token#sign#version",
                    total_limit=100,  # 较大的限制值
                    bind_count=0,
                    active=True,
                    is_deleted=False
                )
                db.add(ck)
                walmart_cks.append(ck)
        
        db.commit()
        
        self.merchant = merchant
        self.departments = departments
        self.walmart_cks = walmart_cks
    
    @pytest.mark.asyncio
    async def test_single_request_performance(self, db: Session):
        """测试单个请求的性能"""
        print("\n=== 单个请求性能测试 ===")
        
        service = SimplifiedCKService(db)
        response_times = []
        
        # 测试100次单个请求
        for _ in range(100):
            start_time = time.time()
            ck = await service.get_available_ck(merchant_id=self.merchant.id)
            end_time = time.time()
            
            response_time = (end_time - start_time) * 1000  # 转换为毫秒
            response_times.append(response_time)
            
            assert ck is not None, "CK选择失败"
        
        # 计算性能指标
        avg_response_time = statistics.mean(response_times)
        p95_response_time = statistics.quantiles(response_times, n=20)[18]  # 95th percentile
        p99_response_time = statistics.quantiles(response_times, n=100)[98]  # 99th percentile
        max_response_time = max(response_times)
        
        print(f"单个请求性能指标:")
        print(f"平均响应时间: {avg_response_time:.2f}ms")
        print(f"95%响应时间: {p95_response_time:.2f}ms")
        print(f"99%响应时间: {p99_response_time:.2f}ms")
        print(f"最大响应时间: {max_response_time:.2f}ms")
        
        # 性能断言（根据实际需求调整）
        assert avg_response_time < 50, f"平均响应时间过长: {avg_response_time:.2f}ms"
        assert p95_response_time < 100, f"95%响应时间过长: {p95_response_time:.2f}ms"
    
    @pytest.mark.asyncio
    async def test_concurrent_performance(self, db: Session):
        """测试并发性能"""
        print("\n=== 并发性能测试 ===")
        
        concurrent_levels = [10, 50, 100, 200]
        
        for concurrent_level in concurrent_levels:
            print(f"\n测试并发级别: {concurrent_level}")
            
            async def single_request():
                service = SimplifiedCKService(db)
                start_time = time.time()
                ck = await service.get_available_ck(merchant_id=self.merchant.id)
                end_time = time.time()
                return {
                    'success': ck is not None,
                    'response_time': (end_time - start_time) * 1000,
                    'ck_id': ck.id if ck else None
                }
            
            # 执行并发测试
            start_time = time.time()
            tasks = [single_request() for _ in range(concurrent_level)]
            results = await asyncio.gather(*tasks, return_exceptions=True)
            end_time = time.time()
            
            # 分析结果
            successful_results = [r for r in results if isinstance(r, dict) and r['success']]
            failed_results = [r for r in results if isinstance(r, dict) and not r['success']]
            exception_results = [r for r in results if isinstance(r, Exception)]
            
            total_time = (end_time - start_time) * 1000
            success_rate = len(successful_results) / len(results)
            qps = len(results) / (total_time / 1000)
            
            if successful_results:
                response_times = [r['response_time'] for r in successful_results]
                avg_response_time = statistics.mean(response_times)
                max_response_time = max(response_times)
            else:
                avg_response_time = 0
                max_response_time = 0
            
            print(f"  总耗时: {total_time:.2f}ms")
            print(f"  成功率: {success_rate:.2%}")
            print(f"  QPS: {qps:.2f}")
            print(f"  平均响应时间: {avg_response_time:.2f}ms")
            print(f"  最大响应时间: {max_response_time:.2f}ms")
            print(f"  异常数量: {len(exception_results)}")
            
            # 性能断言
            assert success_rate > 0.95, f"成功率过低: {success_rate:.2%}"
            assert len(exception_results) == 0, f"存在异常: {len(exception_results)}"
    
    @pytest.mark.asyncio
    async def test_load_balancing_performance(self, db: Session):
        """测试负载均衡性能"""
        print("\n=== 负载均衡性能测试 ===")
        
        service = SimplifiedCKService(db)
        test_rounds = 1000
        ck_usage_count = {}
        department_usage_count = {}
        
        start_time = time.time()
        
        for _ in range(test_rounds):
            ck = await service.get_available_ck(merchant_id=self.merchant.id)
            if ck:
                ck_usage_count[ck.id] = ck_usage_count.get(ck.id, 0) + 1
                department_usage_count[ck.department_id] = department_usage_count.get(ck.department_id, 0) + 1
        
        end_time = time.time()
        total_time = (end_time - start_time) * 1000
        
        # 分析负载分布
        ck_usage_values = list(ck_usage_count.values())
        dept_usage_values = list(department_usage_count.values())
        
        if ck_usage_values:
            ck_std_dev = statistics.stdev(ck_usage_values)
            ck_mean = statistics.mean(ck_usage_values)
            ck_cv = ck_std_dev / ck_mean if ck_mean > 0 else 0  # 变异系数
        else:
            ck_cv = 0
        
        if dept_usage_values:
            dept_std_dev = statistics.stdev(dept_usage_values)
            dept_mean = statistics.mean(dept_usage_values)
            dept_cv = dept_std_dev / dept_mean if dept_mean > 0 else 0
        else:
            dept_cv = 0
        
        qps = test_rounds / (total_time / 1000)
        
        print(f"负载均衡性能指标:")
        print(f"总耗时: {total_time:.2f}ms")
        print(f"QPS: {qps:.2f}")
        print(f"CK负载变异系数: {ck_cv:.3f}")
        print(f"部门负载变异系数: {dept_cv:.3f}")
        print(f"使用的CK数量: {len(ck_usage_count)}")
        print(f"使用的部门数量: {len(department_usage_count)}")
        
        # 性能断言
        assert qps > 100, f"QPS过低: {qps:.2f}"
        assert ck_cv < 0.5, f"CK负载分布不均: {ck_cv:.3f}"
        assert len(ck_usage_count) > len(self.walmart_cks) * 0.5, "CK使用覆盖率过低"
    
    @pytest.mark.asyncio
    async def test_memory_usage_stability(self, db: Session):
        """测试内存使用稳定性"""
        print("\n=== 内存使用稳定性测试 ===")
        
        import psutil
        import os
        
        process = psutil.Process(os.getpid())
        initial_memory = process.memory_info().rss / 1024 / 1024  # MB
        
        service = SimplifiedCKService(db)
        
        # 执行大量请求
        for i in range(5000):
            ck = await service.get_available_ck(merchant_id=self.merchant.id)
            
            # 每1000次检查一次内存
            if i % 1000 == 0:
                current_memory = process.memory_info().rss / 1024 / 1024
                memory_increase = current_memory - initial_memory
                print(f"  第{i}次请求后内存使用: {current_memory:.2f}MB (增长: {memory_increase:.2f}MB)")
                
                # 内存增长不应超过100MB
                assert memory_increase < 100, f"内存泄漏风险: 增长{memory_increase:.2f}MB"
        
        final_memory = process.memory_info().rss / 1024 / 1024
        total_increase = final_memory - initial_memory
        
        print(f"最终内存使用: {final_memory:.2f}MB")
        print(f"总内存增长: {total_increase:.2f}MB")
        
        # 总内存增长不应超过50MB
        assert total_increase < 50, f"内存使用过多: {total_increase:.2f}MB"


if __name__ == "__main__":
    pytest.main([__file__, "-v", "-s"])
