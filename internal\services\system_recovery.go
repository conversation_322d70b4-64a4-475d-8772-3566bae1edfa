package services

import (
	"context"
	"fmt"
	"sync"
	"time"

	"walmart-bind-card-processor/internal/database"

	"github.com/go-redis/redis/v8"
	"go.uber.org/zap"
	"gorm.io/gorm"
)

// SystemRecoveryService 系统恢复服务
type SystemRecoveryService struct {
	db     *gorm.DB
	redis  *redis.Client
	logger *zap.Logger
	
	// 恢复状态
	mu                sync.RWMutex
	isDBRecovering    bool
	isRedisRecovering bool
	lastDBCheck       time.Time
	lastRedisCheck    time.Time
	
	// 配置
	checkInterval     time.Duration
	recoveryTimeout   time.Duration
	maxRecoveryAttempts int
}

// RecoveryStatus 恢复状态
type RecoveryStatus struct {
	IsHealthy        bool      `json:"is_healthy"`
	DBStatus         string    `json:"db_status"`
	RedisStatus      string    `json:"redis_status"`
	LastCheck        time.Time `json:"last_check"`
	RecoveryAttempts int       `json:"recovery_attempts"`
	Errors           []string  `json:"errors"`
}

// NewSystemRecoveryService 创建系统恢复服务
func NewSystemRecoveryService(db *gorm.DB, redis *redis.Client, logger *zap.Logger) *SystemRecoveryService {
	return &SystemRecoveryService{
		db:                  db,
		redis:              redis,
		logger:              logger,
		checkInterval:       10 * time.Second, // 10秒检查一次
		recoveryTimeout:     30 * time.Second, // 30秒恢复超时
		maxRecoveryAttempts: 5,                // 最大恢复尝试次数
	}
}

// Start 启动系统恢复监控
func (s *SystemRecoveryService) Start(ctx context.Context) {
	ticker := time.NewTicker(s.checkInterval)
	defer ticker.Stop()
	
	s.logger.Info("系统恢复服务启动", zap.Duration("check_interval", s.checkInterval))
	
	for {
		select {
		case <-ctx.Done():
			s.logger.Info("系统恢复服务停止")
			return
		case <-ticker.C:
			s.performHealthCheck(ctx)
		}
	}
}

// performHealthCheck 执行健康检查
func (s *SystemRecoveryService) performHealthCheck(ctx context.Context) {
	s.mu.Lock()
	defer s.mu.Unlock()
	
	// 检查数据库健康状态
	if err := s.checkDatabaseHealth(ctx); err != nil {
		s.logger.Error("数据库健康检查失败", zap.Error(err))
		if !s.isDBRecovering {
			go s.recoverDatabase(ctx)
		}
	} else {
		s.isDBRecovering = false
		s.lastDBCheck = time.Now()
	}
	
	// 检查Redis健康状态
	if err := s.checkRedisHealth(ctx); err != nil {
		s.logger.Error("Redis健康检查失败", zap.Error(err))
		if !s.isRedisRecovering {
			go s.recoverRedis(ctx)
		}
	} else {
		s.isRedisRecovering = false
		s.lastRedisCheck = time.Now()
	}
}

// checkDatabaseHealth 检查数据库健康状态
func (s *SystemRecoveryService) checkDatabaseHealth(ctx context.Context) error {
	// 检查数据库连接
	if err := database.HealthCheck(); err != nil {
		return fmt.Errorf("数据库连接检查失败: %w", err)
	}
	
	// 检查连接池状态
	stats := database.GetConnectionStats()
	if status, ok := stats["status"].(string); ok && status != "connected" {
		return fmt.Errorf("数据库连接池状态异常: %s", status)
	}
	
	// 检查连接池使用率
	if usageRate, ok := stats["usage_rate"].(float64); ok && usageRate > 95 {
		return fmt.Errorf("数据库连接池使用率过高: %.1f%%", usageRate)
	}
	
	// 检查连接等待
	if waitCount, ok := stats["wait_count"].(int64); ok && waitCount > 50 {
		return fmt.Errorf("数据库连接等待过多: %d", waitCount)
	}
	
	return nil
}

// checkRedisHealth 检查Redis健康状态
func (s *SystemRecoveryService) checkRedisHealth(ctx context.Context) error {
	// 检查Redis连接
	if err := s.redis.Ping(ctx).Err(); err != nil {
		return fmt.Errorf("redis连接检查失败: %w", err)
	}
	
	// 检查Redis连接池状态
	poolStats := s.redis.PoolStats()
	if poolStats.Timeouts > 0 {
		return fmt.Errorf("redis连接超时: %d次", poolStats.Timeouts)
	}
	
	return nil
}

// recoverDatabase 恢复数据库连接
func (s *SystemRecoveryService) recoverDatabase(ctx context.Context) {
	s.mu.Lock()
	s.isDBRecovering = true
	s.mu.Unlock()
	
	defer func() {
		s.mu.Lock()
		s.isDBRecovering = false
		s.mu.Unlock()
	}()
	
	s.logger.Info("开始数据库恢复流程")
	
	for attempt := 1; attempt <= s.maxRecoveryAttempts; attempt++ {
		s.logger.Info("数据库恢复尝试", zap.Int("attempt", attempt))
		
		// 尝试重新连接数据库
		if err := s.attemptDatabaseRecovery(ctx); err != nil {
			s.logger.Error("数据库恢复失败", 
				zap.Int("attempt", attempt), 
				zap.Error(err))
			
			if attempt < s.maxRecoveryAttempts {
				// 等待后重试
				time.Sleep(time.Duration(attempt) * 5 * time.Second)
				continue
			}
		} else {
			s.logger.Info("数据库恢复成功", zap.Int("attempt", attempt))
			return
		}
	}
	
	s.logger.Error("数据库恢复失败，已达到最大尝试次数", 
		zap.Int("max_attempts", s.maxRecoveryAttempts))
}

// attemptDatabaseRecovery 尝试数据库恢复
func (s *SystemRecoveryService) attemptDatabaseRecovery(ctx context.Context) error {
	// 检查当前连接状态
	if err := database.HealthCheck(); err == nil {
		return nil // 已经恢复
	}
	
	// 获取连接池统计
	stats := database.GetConnectionStats()
	s.logger.Info("数据库连接池状态", zap.Any("stats", stats))
	
	// 如果连接池耗尽，尝试清理空闲连接
	if usageRate, ok := stats["usage_rate"].(float64); ok && usageRate > 90 {
		s.logger.Info("连接池使用率过高，尝试清理空闲连接")
		// 这里可以添加连接池清理逻辑
	}
	
	// 重新测试连接
	return database.HealthCheck()
}

// recoverRedis 恢复Redis连接
func (s *SystemRecoveryService) recoverRedis(ctx context.Context) {
	s.mu.Lock()
	s.isRedisRecovering = true
	s.mu.Unlock()
	
	defer func() {
		s.mu.Lock()
		s.isRedisRecovering = false
		s.mu.Unlock()
	}()
	
	s.logger.Info("开始Redis恢复流程")
	
	for attempt := 1; attempt <= s.maxRecoveryAttempts; attempt++ {
		s.logger.Info("Redis恢复尝试", zap.Int("attempt", attempt))
		
		// 尝试重新连接Redis
		if err := s.attemptRedisRecovery(ctx); err != nil {
			s.logger.Error("Redis恢复失败", 
				zap.Int("attempt", attempt), 
				zap.Error(err))
			
			if attempt < s.maxRecoveryAttempts {
				// 等待后重试
				time.Sleep(time.Duration(attempt) * 3 * time.Second)
				continue
			}
		} else {
			s.logger.Info("Redis恢复成功", zap.Int("attempt", attempt))
			return
		}
	}
	
	s.logger.Error("Redis恢复失败，已达到最大尝试次数", 
		zap.Int("max_attempts", s.maxRecoveryAttempts))
}

// attemptRedisRecovery 尝试Redis恢复
func (s *SystemRecoveryService) attemptRedisRecovery(ctx context.Context) error {
	// 检查当前连接状态
	if err := s.redis.Ping(ctx).Err(); err == nil {
		return nil // 已经恢复
	}
	
	// 获取连接池统计
	poolStats := s.redis.PoolStats()
	s.logger.Info("Redis连接池状态",
		zap.Uint32("total_conns", poolStats.TotalConns),
		zap.Uint32("idle_conns", poolStats.IdleConns),
		zap.Uint64("timeouts", uint64(poolStats.Timeouts)))
	
	// 重新测试连接
	return s.redis.Ping(ctx).Err()
}

// GetRecoveryStatus 获取恢复状态
func (s *SystemRecoveryService) GetRecoveryStatus(ctx context.Context) *RecoveryStatus {
	s.mu.RLock()
	defer s.mu.RUnlock()
	
	status := &RecoveryStatus{
		LastCheck: time.Now(),
		Errors:    make([]string, 0),
	}
	
	// 检查数据库状态
	if err := s.checkDatabaseHealth(ctx); err != nil {
		status.DBStatus = "unhealthy"
		status.Errors = append(status.Errors, "数据库: "+err.Error())
	} else {
		status.DBStatus = "healthy"
	}
	
	// 检查Redis状态
	if err := s.checkRedisHealth(ctx); err != nil {
		status.RedisStatus = "unhealthy"
		status.Errors = append(status.Errors, "Redis: "+err.Error())
	} else {
		status.RedisStatus = "healthy"
	}
	
	// 整体健康状态
	status.IsHealthy = status.DBStatus == "healthy" && status.RedisStatus == "healthy"
	
	return status
}

// TriggerManualRecovery 触发手动恢复
func (s *SystemRecoveryService) TriggerManualRecovery(ctx context.Context) error {
	s.logger.Info("触发手动系统恢复")
	
	// 并行恢复数据库和Redis
	var wg sync.WaitGroup
	var dbErr, redisErr error
	
	wg.Add(2)
	
	go func() {
		defer wg.Done()
		dbErr = s.attemptDatabaseRecovery(ctx)
	}()
	
	go func() {
		defer wg.Done()
		redisErr = s.attemptRedisRecovery(ctx)
	}()
	
	wg.Wait()
	
	if dbErr != nil || redisErr != nil {
		return fmt.Errorf("手动恢复失败 - 数据库: %v, Redis: %v", dbErr, redisErr)
	}
	
	s.logger.Info("手动系统恢复成功")
	return nil
}
