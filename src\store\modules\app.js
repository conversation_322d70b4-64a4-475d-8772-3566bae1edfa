import { defineStore } from 'pinia'

export const useAppStore = defineStore('app', {
  state: () => ({
    sidebar: {
      opened: localStorage.getItem('sidebarStatus') 
        ? !!+localStorage.getItem('sidebarStatus') 
        : true,
      withoutAnimation: false
    },
    device: 'desktop',
    size: localStorage.getItem('size') || 'medium',
    theme: localStorage.getItem('theme') || 'light'
  }),

  getters: {
    sidebarOpened: (state) => state.sidebar.opened,
    currentDevice: (state) => state.device,
    currentSize: (state) => state.size,
    currentTheme: (state) => state.theme
  },

  actions: {
    toggleSidebar() {
      this.sidebar.opened = !this.sidebar.opened
      this.sidebar.withoutAnimation = false
      if (this.sidebar.opened) {
        localStorage.setItem('sidebarStatus', '1')
      } else {
        localStorage.setItem('sidebarStatus', '0')
      }
    },
    
    closeSidebar({ withoutAnimation }) {
      localStorage.setItem('sidebarStatus', '0')
      this.sidebar.opened = false
      this.sidebar.withoutAnimation = withoutAnimation
    },
    
    toggleDevice(device) {
      this.device = device
    },
    
    setSize(size) {
      this.size = size
      localStorage.setItem('size', size)
    },
    
    setTheme(theme) {
      this.theme = theme
      localStorage.setItem('theme', theme)
      // 应用主题
      document.documentElement.setAttribute('data-theme', theme)
    }
  }
})
