"""
沃尔玛CK服务模块 - 提供沃尔玛CK管理的核心功能
"""

from typing import List, Optional, Dict, Any
from sqlalchemy.orm import Session, joinedload
from sqlalchemy import or_
from sqlalchemy.exc import IntegrityError
import logging
import random
import asyncio
from datetime import datetime

from app.services.base_service import BaseService
from app.services.security_service import SecurityService
from app.models.walmart_ck import WalmartCK
from app.models.user import User
from app.models.merchant import Merchant
from app.models.department import Department
from app.schemas.walmart_ck import WalmartCKCreate, WalmartCKUpdate

logger = logging.getLogger(__name__)


class WalmartCKService(BaseService[WalmartCK, WalmartCKCreate, WalmartCKUpdate]):
    """沃尔玛CK服务类"""

    def __init__(self, db: Session):
        super().__init__(WalmartCK, db)
        self.security_service = SecurityService(db)

    def get_by_sign(self, sign: str, merchant_id: Optional[int] = None) -> Optional[WalmartCK]:
        """
        根据签名获取CK（过滤已删除记录）

        Args:
            sign: CK签名
            merchant_id: 商户ID（用于数据隔离）

        Returns:
            Optional[WalmartCK]: CK对象或None
        """
        try:
            query = self.db.query(WalmartCK).filter(
                WalmartCK.sign == sign,
                WalmartCK.is_deleted == False
            )
            if merchant_id:
                query = query.filter(WalmartCK.merchant_id == merchant_id)
            return query.first()
        except Exception as e:
            self.logger.error(f"根据签名获取CK失败: {e}")
            return None

    def get_by_sign_global(self, sign: str) -> Optional[WalmartCK]:
        """
        根据签名全局获取CK（不限制商户，但过滤已删除记录）

        Args:
            sign: CK签名

        Returns:
            Optional[WalmartCK]: CK对象或None
        """
        try:
            return self.db.query(WalmartCK).filter(
                WalmartCK.sign == sign,
                WalmartCK.is_deleted == False
            ).first()
        except Exception as e:
            self.logger.error(f"根据签名全局获取CK失败: {e}")
            return None

    def create_walmart_ck(
        self,
        ck_in: WalmartCKCreate,
        current_user: User
    ) -> Optional[WalmartCK]:
        """
        创建沃尔玛CK

        Args:
            ck_in: CK创建数据
            current_user: 当前操作用户

        Returns:
            Optional[WalmartCK]: 创建的CK对象或None
        """
        try:
            # 根据用户角色自动设置merchant_id和department_id
            if current_user.is_superuser:
                # 超级管理员：使用前端传入的merchant_id和department_id
                target_merchant_id = ck_in.merchant_id
                target_department_id = ck_in.department_id
            else:
                # 非超级管理员：使用前端传入的merchant_id和department_id
                # 但需要验证权限（在后面的权限检查中进行）
                target_merchant_id = ck_in.merchant_id
                target_department_id = ck_in.department_id

                if not target_merchant_id or not target_department_id:
                    raise ValueError("请选择商户和部门")

            # 检查商户是否存在
            merchant = self.db.query(Merchant).filter(Merchant.id == target_merchant_id).first()
            if not merchant:
                raise ValueError("商户不存在")

            # 检查部门是否存在且属于同一商户
            department = self.db.query(Department).filter(
                Department.id == target_department_id,
                Department.merchant_id == target_merchant_id
            ).first()
            if not department:
                raise ValueError("部门不存在或不属于该商户")

            # 权限检查：非超级管理员只能在自己的商户下创建CK
            if not current_user.is_superuser:
                if not current_user.merchant_id:
                    raise ValueError("用户未分配商户，无法创建CK")
                if current_user.merchant_id != target_merchant_id:
                    raise ValueError("只能为自己所属的商户创建CK")
                # 注意：商户管理员可以为其商户下的任何部门创建CK，不限制部门

            # 检查签名是否已存在（全局唯一性检查）
            if self.get_by_sign_global(ck_in.sign):
                raise ValueError("该CK签名已存在，请使用其他签名")

            # 准备CK数据
            ck_data = ck_in.model_dump()
            ck_data['merchant_id'] = target_merchant_id
            ck_data['department_id'] = target_department_id
            ck_data['created_by'] = current_user.id

            # 创建CK
            db_ck = WalmartCK(**ck_data)
            self.db.add(db_ck)
            self.db.commit()
            self.db.refresh(db_ck)

            return db_ck
        except IntegrityError as e:
            self.db.rollback()
            # 检查是否是唯一约束冲突
            error_msg = str(e.orig) if hasattr(e, 'orig') else str(e)
            if 'uk_walmart_ck_sign' in error_msg or 'Duplicate entry' in error_msg:
                self.logger.warning(f"CK签名重复: {ck_in.sign}")
                raise ValueError("该CK签名已存在，请使用其他签名")
            else:
                self.logger.error(f"数据库完整性错误: {e}")
                raise ValueError("数据保存失败，请检查输入数据")
        except ValueError as e:
            # ValueError是业务逻辑错误，直接抛出
            self.db.rollback()
            raise
        except Exception as e:
            self.db.rollback()
            self.logger.error(f"创建沃尔玛CK失败: {e}")
            raise ValueError("创建CK失败，请稍后重试")

    def batch_create_walmart_ck(
        self,
        batch_data: 'WalmartCKBatchCreate',
        current_user: User
    ) -> Dict[str, Any]:
        """
        批量创建沃尔玛CK

        Args:
            batch_data: 批量创建数据
            current_user: 当前操作用户

        Returns:
            Dict: 批量创建结果

        Raises:
            ValueError: 业务逻辑错误
        """
        from app.schemas.walmart_ck import WalmartCKCreate

        try:
            # 验证权限
            if not current_user.is_superuser and batch_data.merchant_id != current_user.merchant_id:
                raise ValueError("只能为自己所属的商户创建CK")

            # 验证部门权限
            if not self._validate_department_access(current_user, batch_data.department_id):
                raise ValueError("没有权限为该部门创建CK")

            # 验证CK签名格式和重复性
            valid_signs, invalid_signs = self._validate_batch_signs(batch_data.signs)

            if invalid_signs:
                raise ValueError(f"以下CK签名格式不正确: {', '.join(invalid_signs)}")

            # 检查数据库中是否已存在相同签名的CK
            existing_signs = self._check_existing_signs(valid_signs)
            if existing_signs:
                raise ValueError(f"以下CK签名已存在: {', '.join(existing_signs)}")

            # 开始事务批量创建
            created_cks = []
            failed_items = []

            for index, sign in enumerate(valid_signs):
                try:
                    # 确定当前CK的备注
                    # 优先使用独立备注列表，如果没有则使用通用备注
                    current_description = batch_data.description
                    if batch_data.descriptions and index < len(batch_data.descriptions):
                        current_description = batch_data.descriptions[index]

                    # 创建单个CK数据
                    ck_create = WalmartCKCreate(
                        sign=sign,
                        merchant_id=batch_data.merchant_id,
                        department_id=batch_data.department_id,
                        total_limit=batch_data.total_limit,
                        active=batch_data.active,
                        description=current_description
                    )

                    # 创建CK记录
                    db_ck = WalmartCK(
                        sign=ck_create.sign,
                        merchant_id=ck_create.merchant_id,
                        department_id=ck_create.department_id,
                        total_limit=ck_create.total_limit,
                        active=ck_create.active,
                        description=ck_create.description,
                        created_by=current_user.id,
                        bind_count=0,
                        last_bind_time=None
                    )

                    self.db.add(db_ck)
                    created_cks.append(db_ck)

                except Exception as e:
                    self.logger.error(f"创建CK失败 {sign}: {e}")
                    failed_items.append({
                        "sign": sign,
                        "error": str(e)
                    })

            # 如果有失败项，回滚事务
            if failed_items:
                self.db.rollback()
                raise ValueError(f"批量创建失败，{len(failed_items)}个CK创建失败")

            # 提交事务
            self.db.commit()

            # 刷新对象以获取ID等信息
            for ck in created_cks:
                self.db.refresh(ck)

            self.logger.info(f"批量创建CK成功: 用户{current_user.username}创建了{len(created_cks)}个CK")

            return {
                "success_count": len(created_cks),
                "failed_count": len(failed_items),
                "total_count": len(batch_data.signs),
                "success_items": [ck.to_dict() for ck in created_cks],
                "failed_items": failed_items,
                "message": f"成功创建{len(created_cks)}个CK"
            }

        except ValueError as e:
            # ValueError是业务逻辑错误，直接抛出
            self.db.rollback()
            raise
        except Exception as e:
            self.db.rollback()
            self.logger.error(f"批量创建沃尔玛CK失败: {e}")
            raise ValueError("批量创建CK失败，请稍后重试")

    def _validate_batch_signs(self, signs: List[str]) -> tuple[List[str], List[str]]:
        """
        验证批量CK签名格式

        Returns:
            tuple: (有效签名列表, 无效签名列表)
        """
        import re

        # CK格式验证正则表达式
        pattern = re.compile(r'^[a-zA-Z0-9]+@[a-zA-Z0-9]+#[A-Za-z0-9+/=×\-_]+#\d+$')

        valid_signs = []
        invalid_signs = []
        seen_signs = set()

        for sign in signs:
            sign = sign.strip()
            if not sign:
                continue

            # 检查格式
            if not pattern.match(sign):
                invalid_signs.append(sign)
                continue

            # 检查批量内部重复
            if sign in seen_signs:
                invalid_signs.append(f"{sign} (重复)")
                continue

            seen_signs.add(sign)
            valid_signs.append(sign)

        return valid_signs, invalid_signs

    def _check_existing_signs(self, signs: List[str]) -> List[str]:
        """
        检查数据库中是否已存在相同签名的CK

        Returns:
            List[str]: 已存在的签名列表
        """
        existing_cks = self.db.query(WalmartCK).filter(
            WalmartCK.sign.in_(signs)
        ).all()

        return [ck.sign for ck in existing_cks]

    def _validate_department_access(self, current_user: User, department_id: int) -> bool:
        """
        验证用户是否有权限访问指定部门

        Args:
            current_user: 当前用户
            department_id: 部门ID

        Returns:
            bool: 是否有权限
        """
        if current_user.is_superuser:
            return True

        # 检查部门是否属于用户的商户
        from app.models.department import Department
        department = self.db.query(Department).filter(
            Department.id == department_id
        ).first()

        if not department:
            return False

        return department.merchant_id == current_user.merchant_id

    def update_walmart_ck(
        self,
        ck_id: int,
        ck_in: WalmartCKUpdate,
        current_user: User
    ) -> Optional[WalmartCK]:
        """
        更新沃尔玛CK信息

        Args:
            ck_id: CK ID
            ck_in: 更新数据
            current_user: 当前操作用户

        Returns:
            Optional[WalmartCK]: 更新后的CK对象或None
        """
        try:
            # 获取CK（应用数据隔离）
            ck = self.get_with_isolation(ck_id, current_user)
            if not ck:
                raise ValueError("CK不存在或无权限访问")

            # 检查签名唯一性（全局唯一性检查）
            if hasattr(ck_in, 'sign') and ck_in.sign and ck_in.sign != ck.sign:
                if self.get_by_sign_global(ck_in.sign):
                    raise ValueError("该CK签名已存在，请使用其他签名")

            # 更新CK信息
            update_data = ck_in.model_dump(exclude_unset=True)
            for field, value in update_data.items():
                if hasattr(ck, field) and field not in ['merchant_id']:
                    setattr(ck, field, value)

            self.db.commit()
            self.db.refresh(ck)
            return ck
        except IntegrityError as e:
            self.db.rollback()
            # 检查是否是唯一约束冲突
            error_msg = str(e.orig) if hasattr(e, 'orig') else str(e)
            if 'uk_walmart_ck_sign' in error_msg or 'Duplicate entry' in error_msg:
                self.logger.warning(f"CK签名重复: {ck_in.sign if hasattr(ck_in, 'sign') else 'unknown'}")
                raise ValueError("该CK签名已存在，请使用其他签名")
            else:
                self.logger.error(f"数据库完整性错误: {e}")
                raise ValueError("数据保存失败，请检查输入数据")
        except ValueError as e:
            # ValueError是业务逻辑错误，直接抛出
            self.db.rollback()
            raise
        except Exception as e:
            self.db.rollback()
            self.logger.error(f"更新沃尔玛CK失败: {e}")
            raise ValueError("更新CK失败，请稍后重试")

    def delete_walmart_ck(self, ck_id: int, current_user: User) -> bool:
        """
        软删除沃尔玛CK（将is_deleted设置为True，同时禁用CK）

        Args:
            ck_id: CK ID
            current_user: 当前操作用户

        Returns:
            bool: 是否删除成功
        """
        try:
            # 获取CK（应用数据隔离）
            ck = self.get_with_isolation(ck_id, current_user)
            if not ck:
                return False

            # 检查CK是否已经被删除
            if ck.is_deleted:
                self.logger.warning(f"CK {ck_id} 已经被删除")
                return False

            # 记录删除前状态
            old_active = ck.active
            old_deleted = ck.is_deleted

            # 软删除：设置is_deleted为True，同时禁用CK
            ck.is_deleted = True
            ck.active = False  # 确保已删除的CK不会被绑卡逻辑选中

            self.db.commit()

            self.logger.info(
                f"CK {ck_id} 软删除成功，操作用户: {current_user.username} | "
                f"状态变更: active({old_active}→{ck.active}), is_deleted({old_deleted}→{ck.is_deleted})"
            )
            return True
        except Exception as e:
            self.db.rollback()
            self.logger.error(f"软删除沃尔玛CK失败: {e}")
            raise

    def batch_delete_walmart_ck(self, ck_ids: List[int], current_user: User) -> Dict[str, Any]:
        """
        批量软删除沃尔玛CK（将is_deleted设置为True，同时禁用CK）

        Args:
            ck_ids: CK ID列表
            current_user: 当前操作用户

        Returns:
            Dict[str, Any]: 批量删除结果
        """
        from app.schemas.walmart_ck import WalmartCKBatchDeleteResult

        total_count = len(ck_ids)
        success_count = 0
        failed_count = 0
        failed_items = []
        success_items = []

        self.logger.info(f"开始批量删除CK，用户: {current_user.username}，CK数量: {total_count}，CK IDs: {ck_ids}")

        for ck_id in ck_ids:
            try:
                # 获取CK（应用数据隔离）
                ck = self.get_with_isolation(ck_id, current_user)
                if not ck:
                    failed_count += 1
                    failed_items.append({
                        "ck_id": ck_id,
                        "error": "CK不存在或无权限访问"
                    })
                    continue

                # 检查CK是否已经被删除
                if ck.is_deleted:
                    self.logger.warning(f"CK {ck_id} 已经被删除，跳过")
                    failed_count += 1
                    failed_items.append({
                        "ck_id": ck_id,
                        "error": "CK已经被删除"
                    })
                    continue

                # 记录删除前状态
                old_active = ck.active
                old_deleted = ck.is_deleted

                # 软删除：设置is_deleted为True，同时禁用CK
                ck.is_deleted = True
                ck.active = False  # 确保已删除的CK不会被绑卡逻辑选中

                # 提交单个CK的删除
                self.db.commit()

                success_count += 1
                success_items.append(ck_id)

                self.logger.info(
                    f"CK {ck_id} 批量删除成功，操作用户: {current_user.username} | "
                    f"状态变更: active({old_active}→{ck.active}), is_deleted({old_deleted}→{ck.is_deleted})"
                )

            except Exception as e:
                self.db.rollback()
                failed_count += 1
                error_msg = str(e)
                failed_items.append({
                    "ck_id": ck_id,
                    "error": error_msg
                })
                self.logger.error(f"批量删除CK {ck_id} 失败: {error_msg}")

        # 记录批量删除操作日志
        self.logger.info(
            f"批量删除CK完成，用户: {current_user.username} | "
            f"总数: {total_count}，成功: {success_count}，失败: {failed_count}"
        )

        return WalmartCKBatchDeleteResult(
            total_count=total_count,
            success_count=success_count,
            failed_count=failed_count,
            failed_items=failed_items,
            success_items=success_items
        ).model_dump()

    async def get_available_ck(
        self,
        merchant_id: int,
        department_id: Optional[int] = None,
        exclude_ids: Optional[List[int]] = None,
        validate_ck: bool = True
    ) -> Optional[WalmartCK]:
        """
        获取可用的CK - 使用简化服务确保负载均衡

        Args:
            merchant_id: 商户ID（必须）
            department_id: 部门ID（可选）
            exclude_ids: 排除的CK ID列表
            validate_ck: 是否验证CK有效性（默认True）

        Returns:
            Optional[WalmartCK]: 可用的CK对象或None
        """
        try:
            # 严格验证商户ID
            if not merchant_id:
                self.logger.error("商户ID不能为空，严格禁止跨商户CK使用")
                return None

            # 【负载均衡修复】使用简化CK服务替换复杂逻辑
            from app.services.simplified_ck_service import SimplifiedCKService

            simplified_service = SimplifiedCKService(self.db)
            ck = await simplified_service.get_available_ck(
                merchant_id=merchant_id,
                department_id=department_id,
                exclude_ids=exclude_ids
            )

            if ck:
                self.logger.info(f"简化服务为商户{merchant_id}选择CK: {ck.id}")

                # 如果启用验证，进行CK验证
                if validate_ck:
                    # 这里可以添加CK验证逻辑
                    pass

                return ck
            else:
                self.logger.warning(f"简化服务未找到商户{merchant_id}的可用CK")
                return None

        except Exception as e:
            self.logger.error(f"获取可用CK失败: {e}")
            return None

    async def get_available_ck_with_context(
        self,
        merchant_id: int,
        department_id: Optional[int] = None,
        exclude_ids: Optional[List[int]] = None,
        validate_ck: bool = True,
        bind_context: Optional[Dict[str, Any]] = None,
        return_detailed_result: bool = False  # 【新增】是否返回详细结果
    ) -> Optional[WalmartCK]:
        """
        获取可用的CK - 带绑卡上下文的版本，使用简化服务确保负载均衡

        Args:
            merchant_id: 商户ID（必须）
            department_id: 部门ID（可选）
            exclude_ids: 排除的CK ID列表
            validate_ck: 是否验证CK有效性（默认True）
            bind_context: 绑卡上下文信息

        Returns:
            Optional[WalmartCK]: 可用的CK对象或None
        """
        try:
            # 严格验证商户ID
            if not merchant_id:
                self.logger.error("商户ID不能为空，严格禁止跨商户CK使用")
                return None

            # 记录绑卡上下文日志
            if bind_context:
                self.logger.info(
                    f"为商户{merchant_id}查找可用CK (绑卡上下文) | "
                    f"record_id={bind_context.get('record_id')} | "
                    f"trace_id={bind_context.get('trace_id')} | "
                    f"card_number={bind_context.get('card_number', '')[:6]}***"
                )

            # 【负载均衡修复】使用简化CK服务
            from app.services.simplified_ck_service import SimplifiedCKService

            simplified_service = SimplifiedCKService(self.db)
            ck = await simplified_service.get_available_ck(
                merchant_id=merchant_id,
                department_id=department_id,
                exclude_ids=exclude_ids,
                bind_context=bind_context
            )

            if ck:
                if bind_context:
                    self.logger.info(
                        f"简化服务为商户{merchant_id}选择CK: {ck.id} | "
                        f"record_id={bind_context.get('record_id')} | "
                        f"trace_id={bind_context.get('trace_id')}"
                    )
                else:
                    self.logger.info(f"简化服务为商户{merchant_id}选择CK: {ck.id}")

                # 如果启用验证，进行CK验证
                if validate_ck:
                    # 这里可以添加CK验证逻辑
                    pass

                return ck
            else:
                if bind_context:
                    self.logger.warning(
                        f"简化服务未找到商户{merchant_id}的可用CK | "
                        f"record_id={bind_context.get('record_id')} | "
                        f"trace_id={bind_context.get('trace_id')}"
                    )
                else:
                    self.logger.warning(f"简化服务未找到商户{merchant_id}的可用CK")
                return None

        except Exception as e:
            error_msg = f"获取可用CK失败: {e}"
            if bind_context:
                error_msg += (
                    f" | record_id={bind_context.get('record_id')} | "
                    f"trace_id={bind_context.get('trace_id')}"
                )
            self.logger.error(error_msg)
            return None

    async def get_available_ck_with_weight(
        self,
        merchant_id: int,
        department_id: Optional[int] = None,
        exclude_ids: Optional[List[int]] = None,
        validate_ck: bool = True
    ) -> Optional[WalmartCK]:
        """
        基于权重算法获取可用的CK

        Args:
            merchant_id: 商户ID（必须）
            department_id: 部门ID（可选，如果指定则直接从该部门获取）
            exclude_ids: 排除的CK ID列表
            validate_ck: 是否验证CK有效性（默认True）

        Returns:
            Optional[WalmartCK]: 可用的CK对象或None
        """
        try:
            # 严格验证商户ID
            if not merchant_id:
                self.logger.error("商户ID不能为空，严格禁止跨商户CK使用")
                return None

            # 使用CRUD层的权重算法
            from app.crud.walmart_ck import walmart_ck

            # 如果指定了部门ID，直接从该部门获取
            if department_id:
                ck = walmart_ck._get_ck_from_department(self.db, merchant_id, department_id)
                if ck and (not exclude_ids or ck.id not in exclude_ids):
                    if validate_ck:
                        # 这里可以添加CK验证逻辑
                        pass
                    return ck

            # 使用权重算法选择部门和CK
            ck = walmart_ck.get_available_ck_with_weight_algorithm(
                self.db, merchant_id, department_id
            )

            if ck and exclude_ids and ck.id in exclude_ids:
                # 如果选中的CK在排除列表中，尝试获取其他CK（防止无限递归）
                self.logger.info(f"选中的CK {ck.id} 在排除列表中，尝试获取其他CK")

                # 添加递归深度限制，防止无限递归
                max_retry_depth = getattr(self, '_retry_depth', 0) + 1
                if max_retry_depth > 5:  # 最大重试5次
                    self.logger.warning(f"CK选择重试次数超过限制，停止递归")
                    return None

                # 设置递归深度并重试
                self._retry_depth = max_retry_depth
                try:
                    result = await self.get_available_ck(
                        merchant_id, department_id, exclude_ids, validate_ck
                    )
                    return result
                finally:
                    # 重置递归深度
                    self._retry_depth = 0

            if ck and validate_ck:
                # 这里可以添加CK验证逻辑
                pass

            return ck

        except Exception as e:
            self.logger.error(f"基于权重获取可用CK失败: {e}")
            return None

    def validate_ck_merchant_isolation(
        self,
        ck_id: int,
        merchant_id: int
    ) -> bool:
        """
        验证CK与商户的隔离关系 - 确保CK属于指定商户

        Args:
            ck_id: CK ID
            merchant_id: 商户ID

        Returns:
            bool: 是否通过验证
        """
        try:
            ck = self.get(ck_id)
            if not ck:
                self.logger.error(f"CK {ck_id} 不存在")
                return False

            if ck.merchant_id != merchant_id:
                self.logger.error(f"严重安全违规：尝试使用不属于商户{merchant_id}的CK {ck_id}，该CK属于商户{ck.merchant_id}")
                return False

            return True
        except Exception as e:
            self.logger.error(f"验证CK商户隔离失败: {e}")
            return False

    def get_with_security_check(self, ck_id: int, current_user: User) -> Optional[WalmartCK]:
        """
        获取CK（带安全检查）

        Args:
            ck_id: CK ID
            current_user: 当前用户

        Returns:
            Optional[WalmartCK]: CK对象或None
        """
        try:
            # 使用安全服务验证访问权限
            if not self.security_service.validate_ck_access(current_user, ck_id, "read"):
                return None

            # 记录数据访问
            ck = self.get(ck_id)
            if ck:
                self.security_service.log_data_access(
                    current_user,
                    "read",
                    "walmart_ck",
                    ck_id,
                    ck.merchant_id
                )

            return ck

        except Exception as e:
            self.logger.error(f"安全检查获取CK失败: {e}")
            return None

    def record_ck_usage(
        self,
        ck_id: int,
        success: bool,
        error_message: Optional[str] = None
    ) -> bool:
        """
        记录CK使用情况，并检查是否需要基于次数自动禁用
        使用行锁确保并发安全，增强日志记录

        Args:
            ck_id: CK ID
            success: 是否成功
            error_message: 错误信息

        Returns:
            bool: 是否记录成功
        """
        try:
            # 使用行锁获取CK，防止并发问题
            ck = self.db.query(WalmartCK).filter(
                WalmartCK.id == ck_id
            ).with_for_update().first()

            if not ck:
                self.logger.warning(f"CK {ck_id} 不存在，无法记录使用情况")
                return False

            # 记录操作前的状态用于详细日志
            old_bind_count = ck.bind_count
            old_active = ck.active

            # 更新使用统计 - 只更新现有字段
            if success:
                ck.bind_count += 1
                ck.last_bind_time = datetime.now().isoformat()

                # 检查是否达到总次数限制，如果达到则自动禁用
                if ck.total_limit and ck.bind_count >= ck.total_limit:
                    ck.active = False
                    self.logger.info(
                        f"[CK_AUTO_DISABLE] CK {ck_id} 达到总次数限制({ck.total_limit})，自动禁用 | "
                        f"bind_count: {old_bind_count} -> {ck.bind_count} | "
                        f"active: {old_active} -> {ck.active} | "
                        f"merchant_id: {ck.merchant_id}"
                    )
                else:
                    self.logger.debug(
                        f"[CK_USAGE_SUCCESS] CK {ck_id} 使用统计更新 | "
                        f"bind_count: {old_bind_count} -> {ck.bind_count} | "
                        f"merchant_id: {ck.merchant_id}"
                    )
            else:
                self.logger.debug(
                    f"[CK_USAGE_FAILED] CK {ck_id} 绑卡失败，不更新使用统计 | "
                    f"error: {error_message or 'Unknown'} | "
                    f"merchant_id: {ck.merchant_id}"
                )

            self.db.commit()
            return True
        except Exception as e:
            self.db.rollback()
            self.logger.error(f"[CK_USAGE_ERROR] 记录CK使用情况失败: ck_id={ck_id}, error={e}")
            return False

    def enable_ck(self, ck_id: int, current_user: User) -> bool:
        """
        启用CK

        Args:
            ck_id: CK ID
            current_user: 当前操作用户

        Returns:
            bool: 是否启用成功
        """
        try:
            ck = self.get_with_isolation(ck_id, current_user)
            if not ck:
                raise ValueError("CK不存在或无权限访问")

            ck.active = True
            self.db.commit()

            self.logger.info(f"CK {ck_id} 启用成功")
            return True
        except Exception as e:
            self.db.rollback()
            self.logger.error(f"启用CK失败: {e}")
            raise

    def disable_ck(
        self,
        ck_id: int,
        reason: str,
        current_user: User
    ) -> bool:
        """
        禁用CK

        Args:
            ck_id: CK ID
            reason: 禁用原因
            current_user: 当前操作用户

        Returns:
            bool: 是否禁用成功
        """
        try:
            ck = self.get_with_isolation(ck_id, current_user)
            if not ck:
                raise ValueError("CK不存在或无权限访问")

            ck.active = False
            self.db.commit()

            self.logger.info(f"CK {ck_id} 禁用成功，原因: {reason}")
            return True
        except Exception as e:
            self.db.rollback()
            self.logger.error(f"禁用CK失败: {e}")
            raise

    def batch_enable_walmart_ck(self, ck_ids: List[int], current_user: User) -> Dict[str, Any]:
        """
        批量启用沃尔玛CK

        Args:
            ck_ids: CK ID列表
            current_user: 当前操作用户

        Returns:
            Dict[str, Any]: 批量启用结果
        """
        total_count = len(ck_ids)
        success_count = 0
        failed_count = 0
        failed_items = []
        success_items = []

        self.logger.info(f"开始批量启用CK，用户: {current_user.username}，CK数量: {total_count}，CK IDs: {ck_ids}")

        for ck_id in ck_ids:
            try:
                # 获取CK（应用数据隔离）
                ck = self.get_with_isolation(ck_id, current_user)
                if not ck:
                    failed_count += 1
                    failed_items.append({
                        "ck_id": ck_id,
                        "error": "CK不存在或无权限访问"
                    })
                    continue

                # 检查CK是否已经被删除
                if ck.is_deleted:
                    self.logger.warning(f"CK {ck_id} 已经被删除，无法启用")
                    failed_count += 1
                    failed_items.append({
                        "ck_id": ck_id,
                        "error": "CK已经被删除，无法启用"
                    })
                    continue

                # 记录启用前状态
                old_active = ck.active

                # 启用CK
                ck.active = True

                # 提交单个CK的启用
                self.db.commit()

                success_count += 1
                success_items.append(ck_id)

                self.logger.info(
                    f"CK {ck_id} 批量启用成功，操作用户: {current_user.username} | "
                    f"状态变更: active({old_active}→{ck.active})"
                )

            except Exception as e:
                self.db.rollback()
                failed_count += 1
                error_msg = str(e)
                failed_items.append({
                    "ck_id": ck_id,
                    "error": error_msg
                })
                self.logger.error(f"批量启用CK {ck_id} 失败: {error_msg}")

        result = {
            "total_count": total_count,
            "success_count": success_count,
            "failed_count": failed_count,
            "success_items": success_items,
            "failed_items": failed_items
        }

        self.logger.info(f"批量启用CK完成，总数: {total_count}，成功: {success_count}，失败: {failed_count}")
        return result

    def batch_disable_walmart_ck(self, ck_ids: List[int], current_user: User) -> Dict[str, Any]:
        """
        批量禁用沃尔玛CK

        Args:
            ck_ids: CK ID列表
            current_user: 当前操作用户

        Returns:
            Dict[str, Any]: 批量禁用结果
        """
        total_count = len(ck_ids)
        success_count = 0
        failed_count = 0
        failed_items = []
        success_items = []

        self.logger.info(f"开始批量禁用CK，用户: {current_user.username}，CK数量: {total_count}，CK IDs: {ck_ids}")

        for ck_id in ck_ids:
            try:
                # 获取CK（应用数据隔离）
                ck = self.get_with_isolation(ck_id, current_user)
                if not ck:
                    failed_count += 1
                    failed_items.append({
                        "ck_id": ck_id,
                        "error": "CK不存在或无权限访问"
                    })
                    continue

                # 检查CK是否已经被删除
                if ck.is_deleted:
                    self.logger.warning(f"CK {ck_id} 已经被删除，无法禁用")
                    failed_count += 1
                    failed_items.append({
                        "ck_id": ck_id,
                        "error": "CK已经被删除，无法禁用"
                    })
                    continue

                # 记录禁用前状态
                old_active = ck.active

                # 禁用CK
                ck.active = False

                # 提交单个CK的禁用
                self.db.commit()

                success_count += 1
                success_items.append(ck_id)

                self.logger.info(
                    f"CK {ck_id} 批量禁用成功，操作用户: {current_user.username} | "
                    f"状态变更: active({old_active}→{ck.active})"
                )

            except Exception as e:
                self.db.rollback()
                failed_count += 1
                error_msg = str(e)
                failed_items.append({
                    "ck_id": ck_id,
                    "error": error_msg
                })
                self.logger.error(f"批量禁用CK {ck_id} 失败: {error_msg}")

        result = {
            "total_count": total_count,
            "success_count": success_count,
            "failed_count": failed_count,
            "success_items": success_items,
            "failed_items": failed_items
        }

        self.logger.info(f"批量禁用CK完成，总数: {total_count}，成功: {success_count}，失败: {failed_count}")
        return result

    def auto_disable_ck_by_limit(self, ck_id: int, reason: str = "达到使用限制") -> bool:
        """
        基于使用限制自动禁用CK（系统内部调用，无需权限检查）

        Args:
            ck_id: CK ID
            reason: 禁用原因

        Returns:
            bool: 是否禁用成功
        """
        try:
            ck = self.get(ck_id)
            if not ck:
                self.logger.error(f"自动禁用失败：CK {ck_id} 不存在")
                return False

            if not ck.active:
                self.logger.info(f"CK {ck_id} 已经是禁用状态，跳过自动禁用")
                return True

            ck.active = False
            self.db.commit()

            # 同步到Redis缓存
            self._sync_ck_to_redis(ck)

            self.logger.info(
                f"CK {ck_id} 自动禁用成功 | reason={reason} | "
                f"bind_count={ck.bind_count} | daily_limit={ck.daily_limit}"
            )
            return True
        except Exception as e:
            self.db.rollback()
            self.logger.error(f"自动禁用CK {ck_id} 失败: {e}")
            return False

    def batch_disable_exceeded_cks(self, merchant_id: Optional[int] = None) -> int:
        """
        批量禁用达到限制的CK

        Args:
            merchant_id: 可选的商户ID，如果提供则只处理该商户的CK

        Returns:
            int: 禁用的CK数量
        """
        try:
            query = self.db.query(WalmartCK).filter(
                WalmartCK.active == True,
                WalmartCK.bind_count >= WalmartCK.total_limit,
                WalmartCK.total_limit > 0  # 确保有设置限制
            )

            if merchant_id:
                query = query.filter(WalmartCK.merchant_id == merchant_id)

            exceeded_cks = query.all()
            disabled_count = 0

            for ck in exceeded_cks:
                ck.active = False
                disabled_count += 1
                self.logger.info(
                    f"批量禁用CK {ck.id} | bind_count={ck.bind_count} | total_limit={ck.total_limit}"
                )

            self.db.commit()

            if disabled_count > 0:
                self.logger.info(f"批量禁用了 {disabled_count} 个达到限制的CK")

            return disabled_count

        except Exception as e:
            self.db.rollback()
            self.logger.error(f"批量禁用CK失败: {e}")
            return 0

    def get_ck_statistics(
        self,
        merchant_id: int,
        current_user: User,
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None
    ) -> Dict[str, Any]:
        """
        获取CK统计信息 - 包含绑卡成功追踪

        注意：
        1. CK数量统计基于未删除的CK（用于显示当前可用CK数量）
        2. 绑卡统计数据独立于CK删除状态，确保历史数据完整性
        3. 时间过滤基于CK创建时间，与列表API保持一致

        Args:
            merchant_id: 商户ID
            current_user: 当前用户
            start_date: 开始日期（可选，基于CK创建时间过滤）
            end_date: 结束日期（可选，基于CK创建时间过滤）

        Returns:
            Dict[str, Any]: 统计信息
        """
        try:
            from sqlalchemy import func, case

            # 权限检查
            if not current_user.is_superuser and current_user.merchant_id != merchant_id:
                raise ValueError("无权限查看该商户的CK统计")

            # 构建基础查询
            base_query = self.db.query(WalmartCK).filter(WalmartCK.merchant_id == merchant_id)

            # 应用时间过滤 - 基于CK创建时间，与列表API保持一致
            if start_date:
                base_query = base_query.filter(WalmartCK.created_at >= start_date)
            if end_date:
                base_query = base_query.filter(WalmartCK.created_at <= end_date)

            # CK过期逻辑基于使用次数限制和状态，不再基于时间

            # 统计各种状态的CK数量
            # CK总数（包括已删除的）
            total_ck_count = base_query.count()

            # 可用CK数量（active=True且未删除）
            available_ck_count = base_query.filter(
                WalmartCK.active == True,
                WalmartCK.is_deleted == False
            ).count()

            # 已过期CK数量（达到总次数限制或被禁用，但未删除）
            expired_ck_count = base_query.filter(
                WalmartCK.is_deleted == False,
                or_(
                    WalmartCK.active == False,  # 被禁用的CK
                    WalmartCK.bind_count >= WalmartCK.total_limit  # 达到限制的CK
                )
            ).count()

            # 已删除CK数量
            deleted_ck_count = base_query.filter(WalmartCK.is_deleted == True).count()

            # 为了保持向后兼容，获取原有的统计数据
            legacy_query = base_query.filter(WalmartCK.is_deleted == False)
            legacy_stats = legacy_query.with_entities(
                func.count(WalmartCK.id).label('total_count'),
                func.sum(case((WalmartCK.active == True, 1), else_=0)).label('active_count'),
                func.sum(WalmartCK.bind_count).label('bind_count_sum')
            ).first()

            total_count = legacy_stats.total_count or 0
            active_count = legacy_stats.active_count or 0
            bind_count_sum = legacy_stats.bind_count_sum or 0

            # 获取基于card_records表的实际绑卡成功统计
            # 重要：这里不过滤CK删除状态，确保历史绑卡数据的完整性
            # 即使CK被删除，其历史绑卡记录仍应被统计，符合系统设计原则
            actual_success_stats = self._get_actual_bind_success_stats(merchant_id, start_date, end_date)

            return {
                'merchant_id': merchant_id,
                # 新的CK状态统计字段
                'total_ck_count': total_ck_count,  # CK总数（包括已删除的）
                'available_ck_count': available_ck_count,  # 可用CK数量
                'expired_ck_count': expired_ck_count,  # 已过期CK数量
                'deleted_ck_count': deleted_ck_count,  # 已删除CK数量

                # 保持向后兼容的原有字段
                'total_count': total_count,
                'active_count': active_count,
                'inactive_count': total_count - active_count,
                'total_bind_count': bind_count_sum,
                'actual_success_count': actual_success_stats['total_success'],
                'ck_success_details': actual_success_stats['ck_details']
            }
        except Exception as e:
            self.logger.error(f"获取CK统计信息失败: {e}")
            raise

    def _get_actual_bind_success_stats(
        self,
        merchant_id: int,
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None
    ) -> Dict[str, Any]:
        """
        基于card_records表获取实际的绑卡成功统计

        注意：统计所有历史绑卡记录，不受CK删除影响，确保数据统计的准确性

        Args:
            merchant_id: 商户ID
            start_date: 开始日期（可选，基于绑卡记录创建时间过滤）
            end_date: 结束日期（可选，基于绑卡记录创建时间过滤）

        Returns:
            Dict[str, Any]: 实际绑卡成功统计
        """
        try:
            from app.models.card_record import CardRecord
            from sqlalchemy import func

            # 直接统计绑卡成功记录，不受CK删除状态影响
            # 这样确保历史绑卡数据的完整性和统计准确性
            ck_success_query = self.db.query(
                CardRecord.walmart_ck_id,
                func.count(CardRecord.id).label('success_count')
            ).filter(
                CardRecord.merchant_id == merchant_id,
                CardRecord.status == 'success',
                CardRecord.walmart_ck_id.isnot(None)  # 只统计有CK关联的记录
            )

            # 应用时间过滤 - 基于绑卡记录创建时间
            if start_date:
                ck_success_query = ck_success_query.filter(CardRecord.created_at >= start_date)
            if end_date:
                ck_success_query = ck_success_query.filter(CardRecord.created_at <= end_date)

            ck_success_query = ck_success_query.group_by(CardRecord.walmart_ck_id).all()

            # 构建CK成功详情
            ck_details = {}
            total_success = 0

            for ck_id, success_count in ck_success_query:
                ck_details[ck_id] = success_count
                total_success += success_count

            return {
                'total_success': total_success,
                'ck_details': ck_details
            }
        except Exception as e:
            self.logger.error(f"获取实际绑卡成功统计失败: {e}")
            return {
                'total_success': 0,
                'ck_details': {}
            }

    def get_list_with_statistics(
        self,
        current_user: User,
        page: int = 1,
        page_size: int = 20,
        merchant_id: Optional[int] = None,
        department_id: Optional[int] = None,
        status: Optional[bool] = None,
        start_date: Optional[str] = None,
        end_date: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        获取CK列表和统计数据（集成接口）

        Args:
            current_user: 当前用户
            page: 页码
            page_size: 每页数量
            merchant_id: 商户ID（可选）
            department_id: 部门ID（可选）
            status: 状态过滤（可选）
            is_disabled: 是否禁用（可选）
            username: 用户名搜索（可选）
            start_date: 开始日期字符串 (YYYY-MM-DD)
            end_date: 结束日期字符串 (YYYY-MM-DD)

        Returns:
            Dict[str, Any]: 包含列表数据和统计数据的完整响应
        """
        try:
            from app.models.card_record import CardRecord
            from app.models.department import Department
            from app.models.merchant import Merchant
            from app.models.user import User as UserModel
            from sqlalchemy import func

            # 构建统一的过滤条件
            filters = {}
            if merchant_id and current_user.is_superuser:
                filters['merchant_id'] = merchant_id

            # 部门过滤 - 修复权限绕过问题
            if department_id:
                # 获取用户可访问的部门列表
                from app.core.auth import auth_service
                accessible_depts = auth_service.get_user_accessible_departments(current_user, self.db)

                # 只有当请求的部门在用户可访问范围内时，才应用部门过滤
                if current_user.is_superuser or department_id in accessible_depts:
                    filters['department_id'] = department_id
                else:
                    # 如果请求的部门不在可访问范围内，记录警告但不抛出异常
                    # 让数据隔离逻辑来处理权限控制
                    self.logger.warning(f"用户 {current_user.id} 尝试访问无权限的部门 {department_id}，忽略此过滤条件")

            if status is not None:
                filters['active'] = 1 if status else 0

            # 处理时间范围过滤 - 与列表API保持完全一致
            if start_date:
                try:
                    from datetime import datetime
                    start_datetime = datetime.strptime(start_date, "%Y-%m-%d")
                    filters['start_time'] = start_datetime
                except ValueError:
                    raise ValueError("开始日期格式错误，请使用 YYYY-MM-DD 格式")

            if end_date:
                try:
                    from datetime import datetime
                    end_datetime = datetime.strptime(end_date, "%Y-%m-%d").replace(hour=23, minute=59, second=59)
                    filters['end_time'] = end_datetime
                except ValueError:
                    raise ValueError("结束日期格式错误，请使用 YYYY-MM-DD 格式")

            # 1. 获取CK列表数据
            cks = self.get_multi_with_isolation(
                current_user=current_user,
                skip=(page - 1) * page_size,
                limit=page_size,
                filters=filters,
                order_by="created_at",
                order_desc=True
            )

            # 获取总数
            total = self.count_with_isolation(current_user, filters)

            # 转换为字典格式并添加关联信息
            ck_list = []
            for ck in cks:
                ck_dict = ck.to_dict()
                # ck 脱敏
                ck_dict['sign'] = ck.sign[:4] + '****' + ck.sign[-4:]
                # 统计CK绑定真实金额的总金额
                actual_amount = self.db.query(func.sum(CardRecord.actual_amount)).filter(
                    CardRecord.walmart_ck_id == ck.id,
                    CardRecord.status == 'success'
                ).scalar() or 0
                ck_dict['actual_amount'] = actual_amount

                # 查询商户名称
                if ck.merchant_id:
                    merchant = self.db.query(Merchant).filter(Merchant.id == ck.merchant_id).first()
                    ck_dict['merchant_name'] = merchant.name if merchant else '未知商户'
                else:
                    ck_dict['merchant_name'] = '未知商户'

                # 查询部门名称
                if ck.department_id:
                    department = self.db.query(Department).filter(Department.id == ck.department_id).first()
                    ck_dict['department_name'] = department.name if department else '未知部门'
                else:
                    ck_dict['department_name'] = '未知部门'

                # 查询创建者信息
                if ck.created_by:
                    creator = self.db.query(UserModel).filter(UserModel.id == ck.created_by).first()
                    if creator:
                        # 根据数据权限决定是否显示创建者信息
                        if self._can_view_creator_info(current_user, creator):
                            ck_dict['creator_username'] = creator.username
                            ck_dict['creator_name'] = creator.full_name if creator.full_name else creator.username
                        else:
                            ck_dict['creator_username'] = '***'
                            ck_dict['creator_name'] = '***'
                    else:
                        ck_dict['creator_username'] = '已删除'
                        ck_dict['creator_name'] = '已删除'
                else:
                    ck_dict['creator_username'] = '-'
                    ck_dict['creator_name'] = '-'

                ck_list.append(ck_dict)

            # 2. 获取统计数据 - 使用相同的过滤条件
            statistics = self._get_statistics_with_filters(current_user, filters)

            return {
                "items": ck_list,
                "total": total,
                "page": page,
                "page_size": page_size,
                "pages": (total + page_size - 1) // page_size,
                "statistics": statistics
            }

        except Exception as e:
            self.logger.error(f"获取CK列表和统计数据失败: {e}")
            raise

    def _can_view_creator_info(self, current_user: User, creator: User) -> bool:
        """判断是否可以查看创建者信息"""
        # 超级管理员可以查看所有信息
        if current_user.is_superuser:
            return True

        # 同一商户的用户可以查看
        if current_user.merchant_id == creator.merchant_id:
            return True

        return False

    def _get_statistics_with_filters(
        self,
        current_user: User,
        filters: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        基于过滤条件获取统计数据

        重要：不同统计维度使用不同的时间过滤逻辑：
        - CK数量统计（总数、可用、过期）：基于CK创建时间过滤
        - 删除数量统计：基于CK删除时间（updated_at）过滤
        - 绑卡统计：基于绑卡记录创建时间过滤

        Args:
            current_user: 当前用户
            filters: 与列表查询相同的过滤条件

        Returns:
            Dict[str, Any]: 统计数据
        """
        try:
            from app.models.card_record import CardRecord
            from app.models.department import Department
            from app.models.merchant import Merchant
            from sqlalchemy import func, case

            # 1. 获取CK数量统计（基于CK创建时间过滤）
            # 构建基础查询，应用数据隔离但不过滤已删除记录
            base_ck_query = self.db.query(WalmartCK)
            # 应用基类的数据隔离逻辑，但不过滤已删除记录
            base_ck_query = super().apply_data_isolation(base_ck_query, current_user)

            # 应用过滤条件（基于CK创建时间）
            creation_filtered_query = base_ck_query
            if filters:
                # 商户过滤
                if 'merchant_id' in filters and current_user.is_superuser:
                    creation_filtered_query = creation_filtered_query.filter(WalmartCK.merchant_id == filters['merchant_id'])
                # 部门过滤
                if 'department_id' in filters:
                    creation_filtered_query = creation_filtered_query.filter(WalmartCK.department_id == filters['department_id'])
                # 时间过滤（基于CK创建时间）
                if 'start_time' in filters:
                    creation_filtered_query = creation_filtered_query.filter(WalmartCK.created_at >= filters['start_time'])
                if 'end_time' in filters:
                    creation_filtered_query = creation_filtered_query.filter(WalmartCK.created_at <= filters['end_time'])

            # 统计基于创建时间的CK数量
            # CK总数（包括已删除的，在指定时间范围内创建的）
            total_ck_count = creation_filtered_query.count()

            # 可用CK数量（active=True且未删除，在指定时间范围内创建的）
            available_ck_count = creation_filtered_query.filter(
                WalmartCK.active == True,
                WalmartCK.is_deleted == False
            ).count()

            # 已过期CK数量（达到总次数限制或被禁用，但未删除，在指定时间范围内创建的）
            expired_ck_count = creation_filtered_query.filter(
                WalmartCK.is_deleted == False,
                or_(
                    WalmartCK.active == False,  # 被禁用的CK
                    WalmartCK.bind_count >= WalmartCK.total_limit  # 达到限制的CK
                )
            ).count()

            # 2. 获取删除数量统计（基于CK删除时间过滤）
            # 构建删除统计专用查询
            deletion_query = self.db.query(WalmartCK).filter(WalmartCK.is_deleted == True)
            # 应用基类的数据隔离逻辑（但不过滤已删除记录，因为我们就是要统计删除的）
            deletion_query = super().apply_data_isolation(deletion_query, current_user)

            # 应用过滤条件（基于CK删除时间，即updated_at）
            if filters:
                # 商户过滤
                if 'merchant_id' in filters and current_user.is_superuser:
                    deletion_query = deletion_query.filter(WalmartCK.merchant_id == filters['merchant_id'])
                # 部门过滤
                if 'department_id' in filters:
                    deletion_query = deletion_query.filter(WalmartCK.department_id == filters['department_id'])
                # 时间过滤（基于CK删除时间，即updated_at）
                if 'start_time' in filters:
                    deletion_query = deletion_query.filter(WalmartCK.updated_at >= filters['start_time'])
                if 'end_time' in filters:
                    deletion_query = deletion_query.filter(WalmartCK.updated_at <= filters['end_time'])

            # 已删除CK数量（在指定时间范围内删除的）
            deleted_ck_count = deletion_query.count()

            # 为了保持向后兼容，仍然获取未删除的CK数量用于原有统计
            total_cks = creation_filtered_query.filter(WalmartCK.is_deleted == False).count()

            # 3. 获取绑卡统计数据（不受CK删除状态影响）
            # 重要：这里使用card_records为主表，确保历史绑卡数据的完整性
            stats_query = self.db.query(
                CardRecord.walmart_ck_id.label('ck_id'),
                WalmartCK.sign.label('ck_sign'),
                WalmartCK.description.label('ck_description'),
                Department.name.label('department_name'),
                Merchant.name.label('merchant_name'),
                func.count(CardRecord.id).label('total_records'),
                func.sum(case((CardRecord.status == 'success', 1), else_=0)).label('success_count'),
                func.sum(case((CardRecord.status == 'success', CardRecord.amount), else_=0)).label('total_request_amount'),
                func.sum(case((CardRecord.status == 'success', CardRecord.actual_amount), else_=0)).label('total_actual_amount')
            ).outerjoin(
                WalmartCK, CardRecord.walmart_ck_id == WalmartCK.id
            ).outerjoin(
                Department, WalmartCK.department_id == Department.id
            ).outerjoin(
                Merchant, WalmartCK.merchant_id == Merchant.id
            ).filter(
                CardRecord.walmart_ck_id.isnot(None)  # 只统计有CK关联的记录
            )

            # 应用权限过滤（基于商户和部门）
            if not current_user.is_superuser:
                # 获取用户数据权限范围
                from app.core.auth import AuthService
                auth_service = AuthService()
                data_scope = auth_service.get_user_data_scope(current_user, self.db)

                # 根据数据权限范围应用相应的过滤逻辑
                if data_scope == "all":
                    # 可以访问所有数据，不添加任何过滤条件
                    pass
                elif data_scope == "merchant":
                    # 商户级数据隔离
                    if current_user.merchant_id:
                        stats_query = stats_query.filter(WalmartCK.merchant_id == current_user.merchant_id)
                    else:
                        stats_query = stats_query.filter(WalmartCK.merchant_id.is_(None))
                elif data_scope == "department_sub":
                    # 本部门及子部门数据
                    accessible_dept_ids = self._get_cached_accessible_departments(current_user)
                    if accessible_dept_ids:
                        stats_query = stats_query.filter(WalmartCK.department_id.in_(accessible_dept_ids))
                    elif current_user.department_id:
                        stats_query = stats_query.filter(WalmartCK.department_id == current_user.department_id)
                    # 同时应用商户级隔离
                    if current_user.merchant_id:
                        stats_query = stats_query.filter(WalmartCK.merchant_id == current_user.merchant_id)
                    else:
                        stats_query = stats_query.filter(WalmartCK.merchant_id.is_(None))
                elif data_scope == "department":
                    # 本部门数据
                    if current_user.department_id:
                        stats_query = stats_query.filter(WalmartCK.department_id == current_user.department_id)
                    # 同时应用商户级隔离
                    if current_user.merchant_id:
                        stats_query = stats_query.filter(WalmartCK.merchant_id == current_user.merchant_id)
                    else:
                        stats_query = stats_query.filter(WalmartCK.merchant_id.is_(None))
                elif data_scope == "self":
                    # 本人数据 - 只能访问自己创建的数据
                    stats_query = stats_query.filter(WalmartCK.created_by == current_user.id)
                    # 同时应用商户级隔离
                    if current_user.merchant_id:
                        stats_query = stats_query.filter(WalmartCK.merchant_id == current_user.merchant_id)
                    else:
                        stats_query = stats_query.filter(WalmartCK.merchant_id.is_(None))
                else:
                    # 默认只能访问自己商户的数据
                    if current_user.merchant_id:
                        stats_query = stats_query.filter(WalmartCK.merchant_id == current_user.merchant_id)
                    else:
                        stats_query = stats_query.filter(WalmartCK.merchant_id.is_(None))

            # 应用过滤条件（但不包括CK删除状态）
            if filters:
                # 商户过滤
                if 'merchant_id' in filters and current_user.is_superuser:
                    stats_query = stats_query.filter(WalmartCK.merchant_id == filters['merchant_id'])

                # 部门过滤
                if 'department_id' in filters:
                    stats_query = stats_query.filter(WalmartCK.department_id == filters['department_id'])

                # 时间过滤（基于绑卡记录创建时间）
                if 'start_time' in filters:
                    stats_query = stats_query.filter(CardRecord.created_at >= filters['start_time'])
                if 'end_time' in filters:
                    stats_query = stats_query.filter(CardRecord.created_at <= filters['end_time'])

            # 按CK分组
            stats_query = stats_query.group_by(
                CardRecord.walmart_ck_id,
                WalmartCK.sign,
                WalmartCK.description,
                Department.name,
                Merchant.name
            )

            # 执行查询
            results = stats_query.all()

            # 计算汇总统计
            # total_cks 已在上面计算（未删除的CK数量）
            total_records = sum(r.total_records for r in results)
            total_success = sum(r.success_count for r in results)
            total_request_amount = sum(r.total_request_amount or 0 for r in results)
            total_actual_amount = sum(r.total_actual_amount or 0 for r in results)

            # 计算成功率
            success_rate = (total_success / total_records * 100) if total_records > 0 else 0

            # 构建CK详情列表
            ck_details = []
            for r in results:
                ck_success_rate = (r.success_count / r.total_records * 100) if r.total_records > 0 else 0
                # 安全处理CK签名显示
                ck_sign_display = r.ck_sign or '未知CK'
                

                ck_details.append({
                    'ck_id': r.ck_id,
                    'ck_sign': ck_sign_display,
                    'ck_description': r.ck_description or '无描述',
                    'department_name': r.department_name or '未知部门',
                    'merchant_name': r.merchant_name or '未知商户',
                    'total_records': r.total_records,
                    'success_count': r.success_count,
                    'success_rate': round(ck_success_rate, 2),
                    'total_request_amount': r.total_request_amount or 0,
                    'total_actual_amount': r.total_actual_amount or 0,
                    'amount_difference': (r.total_actual_amount or 0) - (r.total_request_amount or 0)
                })

            return {
                'summary': {
                    # 新的CK状态统计字段
                    'total_ck_count': total_ck_count,  # CK总数（包括已删除的）
                    'available_ck_count': available_ck_count,  # 可用CK数量
                    'expired_ck_count': expired_ck_count,  # 已过期CK数量
                    'deleted_ck_count': deleted_ck_count,  # 已删除CK数量

                    # 保持向后兼容的原有字段
                    'total_cks': total_cks,  # 未删除的CK数量（向后兼容）
                    'total_records': total_records,
                    'total_success': total_success,
                    'success_rate': round(success_rate, 2),
                    'total_request_amount': total_request_amount,
                    'total_actual_amount': total_actual_amount,
                    'amount_difference': total_actual_amount - total_request_amount
                },
                'ck_details': ck_details
            }

        except Exception as e:
            self.logger.error(f"获取统计数据失败: {e}")
            raise

    def get_ck_binding_amount_statistics(
        self,
        current_user: User,
        merchant_id: Optional[int] = None,
        department_id: Optional[int] = None,
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None
    ) -> Dict[str, Any]:
        """
        获取CK绑卡金额统计信息

        注意：时间过滤基于CK创建时间，与列表API保持一致

        Args:
            current_user: 当前用户
            merchant_id: 商户ID（可选）
            department_id: 部门ID（可选）
            start_date: 开始日期（可选，基于CK创建时间过滤）
            end_date: 结束日期（可选，基于CK创建时间过滤）

        Returns:
            Dict[str, Any]: 绑卡金额统计信息
        """
        try:
            from app.models.card_record import CardRecord
            from sqlalchemy import func, case, and_

            # 权限检查和数据范围确定 - 基于动态数据权限配置
            from app.services.permission_service import PermissionService
            permission_service = PermissionService(self.db)

            # 1. 商户级权限检查和数据范围确定
            if current_user.is_superuser:
                # 超级管理员：可以访问所有数据
                target_merchant_id = merchant_id
            elif permission_service.check_data_permission(current_user, 'data:merchant:all'):
                # 有访问所有商户数据的权限
                target_merchant_id = merchant_id
            elif permission_service.check_data_permission(current_user, 'data:merchant:own'):
                # 只能访问本商户数据
                target_merchant_id = current_user.merchant_id
                if merchant_id and merchant_id != current_user.merchant_id:
                    raise ValueError("无权限查看其他商户的数据")
            else:
                # 没有商户级数据权限，默认只能访问自己的商户
                target_merchant_id = current_user.merchant_id
                if merchant_id and merchant_id != current_user.merchant_id:
                    raise ValueError("无权限查看其他商户的数据")

            # 2. 部门级权限检查和数据范围确定
            if current_user.is_superuser:
                # 超级管理员：可以指定任意部门
                target_department_id = department_id
            elif permission_service.check_data_permission(current_user, 'data:department:all'):
                # 有访问所有部门数据的权限
                target_department_id = department_id
            elif permission_service.check_data_permission(current_user, 'data:department:own'):
                # 只能访问本部门数据
                if current_user.department_id is None:
                    raise ValueError("用户未分配部门，无法访问部门数据")

                # 验证请求的部门是否为用户所属部门
                if department_id and department_id != current_user.department_id:
                    raise ValueError("无权限查看其他部门的数据")

                target_department_id = current_user.department_id
            elif permission_service.check_data_permission(current_user, 'data:department:sub'):
                # 可以访问本部门及子部门数据
                if current_user.department_id is None:
                    raise ValueError("用户未分配部门，无法访问部门数据")

                if department_id:
                    # 验证请求的部门是否在用户的访问范围内
                    if not permission_service.can_access_department_data(current_user, department_id):
                        raise ValueError("无权限查看该部门的数据")
                    target_department_id = department_id
                else:
                    # 没有指定部门，默认使用用户所属部门
                    target_department_id = current_user.department_id
            else:
                # 没有明确的部门数据权限，使用默认逻辑（通常是本部门）
                if current_user.department_id:
                    if department_id and department_id != current_user.department_id:
                        # 验证是否有权限访问指定部门
                        if not permission_service.can_access_department_data(current_user, department_id):
                            raise ValueError("无权限查看该部门的数据")
                    target_department_id = department_id or current_user.department_id
                else:
                    target_department_id = department_id

            # 构建基础查询 - 以card_records为主表，确保历史数据完整性
            # 使用LEFT JOIN确保即使CK被软删除，相关的绑卡记录仍能被统计
            query = self.db.query(
                CardRecord.walmart_ck_id.label('ck_id'),
                WalmartCK.sign.label('ck_sign'),
                WalmartCK.description.label('ck_description'),
                Department.name.label('department_name'),
                Merchant.name.label('merchant_name'),
                func.count(CardRecord.id).label('total_records'),
                func.sum(case((CardRecord.status == 'success', 1), else_=0)).label('success_count'),
                func.sum(case((CardRecord.status == 'success', CardRecord.amount), else_=0)).label('total_request_amount'),
                func.sum(case((CardRecord.status == 'success', CardRecord.actual_amount), else_=0)).label('total_actual_amount')
            ).outerjoin(
                WalmartCK, CardRecord.walmart_ck_id == WalmartCK.id
            ).outerjoin(
                Department, WalmartCK.department_id == Department.id
            ).outerjoin(
                Merchant, WalmartCK.merchant_id == Merchant.id
            ).filter(
                CardRecord.walmart_ck_id.isnot(None)  # 只统计有CK关联的记录
            )

            # 应用商户过滤
            if target_merchant_id:
                query = query.filter(WalmartCK.merchant_id == target_merchant_id)

            # 应用部门过滤
            if target_department_id:
                query = query.filter(WalmartCK.department_id == target_department_id)

            # 应用日期过滤 - 基于绑卡记录创建时间，确保统计历史数据的准确性
            if start_date:
                query = query.filter(CardRecord.created_at >= start_date)
            if end_date:
                query = query.filter(CardRecord.created_at <= end_date)

            # 按CK分组
            query = query.group_by(
                CardRecord.walmart_ck_id,
                WalmartCK.sign,
                WalmartCK.description,
                Department.name,
                Merchant.name
            )

            # 执行查询
            results = query.all()

            # 计算汇总统计
            # 统计有绑卡记录的CK数量，确保与绑卡数据保持一致
            total_cks = len(results)

            # 调试信息
            print(f"DEBUG: 统计API - 用户: {current_user.username}, 目标商户: {target_merchant_id}, 目标部门: {target_department_id}")
            print(f"DEBUG: 统计API - 时间范围: {start_date} 到 {end_date}")
            print(f"DEBUG: 统计API - CK总数: {total_cks}, 查询结果数量: {len(results)}")

            # 计算绑卡相关统计
            total_records = sum(r.total_records for r in results)
            total_success = sum(r.success_count for r in results)
            total_request_amount = sum(r.total_request_amount or 0 for r in results)
            total_actual_amount = sum(r.total_actual_amount or 0 for r in results)

            # 计算成功率
            success_rate = (total_success / total_records * 100) if total_records > 0 else 0

            # 构建CK详情列表
            ck_details = []
            for r in results:
                ck_success_rate = (r.success_count / r.total_records * 100) if r.total_records > 0 else 0
                ck_details.append({
                    'ck_id': r.ck_id,
                    'ck_sign': r.ck_sign[:20] + '...' if len(r.ck_sign) > 20 else r.ck_sign,  # 截断显示
                    'ck_description': r.ck_description,
                    'department_name': r.department_name,
                    'merchant_name': r.merchant_name,
                    'total_records': r.total_records,
                    'success_count': r.success_count,
                    'success_rate': round(ck_success_rate, 2),
                    'total_request_amount': r.total_request_amount or 0,
                    'total_actual_amount': r.total_actual_amount or 0,
                    'amount_difference': (r.total_actual_amount or 0) - (r.total_request_amount or 0)
                })

            return {
                'summary': {
                    'total_cks': total_cks,
                    'total_records': total_records,
                    'total_success': total_success,
                    'success_rate': round(success_rate, 2),
                    'total_request_amount': total_request_amount,
                    'total_actual_amount': total_actual_amount,
                    'amount_difference': total_actual_amount - total_request_amount
                },
                'ck_details': ck_details,
                'filters': {
                    'merchant_id': target_merchant_id,
                    'department_id': target_department_id,
                    'start_date': start_date.isoformat() if start_date else None,
                    'end_date': end_date.isoformat() if end_date else None
                }
            }

        except Exception as e:
            self.logger.error(f"获取CK绑卡金额统计失败: {e}")
            raise

    def get_walmart_cks_with_relations(
        self,
        current_user: User,
        skip: int = 0,
        limit: int = 100,
        filters: Optional[Dict[str, Any]] = None,
        order_by: Optional[str] = None,
        order_desc: bool = False
    ) -> List[Dict[str, Any]]:
        """
        获取沃尔玛CK列表（包含商户和部门信息）

        Args:
            current_user: 当前用户
            skip: 跳过的记录数
            limit: 限制返回的记录数
            filters: 过滤条件字典
            order_by: 排序字段
            order_desc: 是否降序排列

        Returns:
            List[Dict[str, Any]]: 包含关联信息的CK列表
        """
        try:
            # 构建JOIN查询
            query = self.db.query(
                WalmartCK,
                Merchant.name.label('merchant_name'),
                Department.name.label('department_name')
            ).outerjoin(
                Merchant, WalmartCK.merchant_id == Merchant.id
            ).outerjoin(
                Department, WalmartCK.department_id == Department.id
            )

            # 应用数据隔离
            query = self.apply_data_isolation_to_join_query(query, current_user)

            # 应用过滤条件
            if filters:
                for field, value in filters.items():
                    if hasattr(WalmartCK, field) and value is not None:
                        query = query.filter(getattr(WalmartCK, field) == value)

            # 应用排序
            if order_by and hasattr(WalmartCK, order_by):
                order_field = getattr(WalmartCK, order_by)
                if order_desc:
                    from sqlalchemy import desc
                    query = query.order_by(desc(order_field))
                else:
                    from sqlalchemy import asc
                    query = query.order_by(asc(order_field))
            else:
                # 默认按创建时间降序排列
                from sqlalchemy import desc
                query = query.order_by(desc(WalmartCK.created_at))

            # 执行查询
            results = query.offset(skip).limit(limit).all()

            # 【性能优化】批量查询所有CK的绑卡金额，避免N+1查询问题
            from app.models.card_record import CardRecord
            from sqlalchemy import func

            ck_ids = [ck.id for ck, _, _ in results]
            if ck_ids:
                # 一次性查询所有CK的绑卡金额
                bind_amounts = self.db.query(
                    CardRecord.walmart_ck_id,
                    func.sum(CardRecord.actual_amount).label('total_amount')
                ).filter(
                    CardRecord.walmart_ck_id.in_(ck_ids),
                    CardRecord.status == 'success'
                ).group_by(CardRecord.walmart_ck_id).all()

                # 转换为字典以便快速查找，并将分转换为元
                bind_amount_dict = {ck_id: float(amount or 0) / 100 for ck_id, amount in bind_amounts}
            else:
                bind_amount_dict = {}

            # 转换结果格式
            ck_list = []
            for ck, merchant_name, department_name in results:
                ck_dict = {
                    'id': ck.id,
                    'sign': ck.sign,
                    'total_limit': ck.total_limit,
                    'bind_count': ck.bind_count,
                    'last_bind_time': ck.last_bind_time,
                    'active': ck.active,
                    'description': ck.description,
                    'created_by': ck.created_by,
                    'merchant_id': ck.merchant_id,
                    'department_id': ck.department_id,
                    'created_at': ck.created_at,
                    'updated_at': ck.updated_at,
                    'merchant_name': merchant_name,
                    'department_name': department_name,
                    'bind_amount': bind_amount_dict.get(ck.id, 0.0)  # 从预查询结果中获取绑卡金额
                }
                ck_list.append(ck_dict)

            return ck_list
        except Exception as e:
            self.logger.error(f"获取沃尔玛CK列表失败: {e}")
            return []

    def apply_data_isolation(self, query, current_user: User):
        """
        重写基类方法，添加软删除过滤和强制商户隔离
        应用数据隔离规则 - 支持用户级、部门级、商户级权限，并过滤已删除记录

        Args:
            query: SQLAlchemy查询对象
            current_user: 当前用户

        Returns:
            query: 应用隔离规则后的查询对象
        """
        # 首先过滤已删除的记录
        query = query.filter(WalmartCK.is_deleted == False)

        # 【安全修复】：强制商户隔离 - 除超级管理员外，所有用户只能访问自己商户的CK
        if not current_user.is_superuser:
            if not current_user.merchant_id:
                # 如果用户没有商户ID，返回空结果
                self.logger.warning(f"[SECURITY] 用户 {current_user.id} 没有商户ID，拒绝CK访问")
                query = query.filter(WalmartCK.id == -1)  # 强制返回空结果
                return query

            # 强制添加商户过滤，确保无法跨商户访问
            query = query.filter(WalmartCK.merchant_id == current_user.merchant_id)
            self.logger.info(f"[SECURITY] 强制商户隔离：用户 {current_user.id} 只能访问商户 {current_user.merchant_id} 的CK")

        # 然后应用基类的数据隔离逻辑
        return super().apply_data_isolation(query, current_user)

    def apply_data_isolation_to_join_query(self, query, current_user: User):
        """
        对JOIN查询应用数据隔离规则 - 支持用户级、部门级、商户级权限，并过滤已删除记录
        修复权限优先级问题：使用统一的权限范围判断，避免严格权限覆盖宽松权限

        Args:
            query: SQLAlchemy查询对象
            current_user: 当前用户

        Returns:
            query: 应用隔离规则后的查询对象
        """
        # 首先过滤已删除的记录
        query = query.filter(WalmartCK.is_deleted == False)

        if not current_user:
            return query

        # 超级管理员可以访问所有数据（但仍需过滤已删除记录）
        if current_user.is_superuser:
            return query

        # 获取权限服务实例和认证服务实例
        from app.services.permission_service import PermissionService
        from app.core.auth import AuthService
        permission_service = PermissionService(self.db)
        auth_service = AuthService()

        # 使用统一的权限范围判断，确保权限优先级正确
        data_scope = auth_service.get_user_data_scope(current_user, self.db)

        # 根据数据权限范围应用相应的过滤逻辑
        if data_scope == "all":
            # 可以访问所有数据，不添加任何过滤条件
            pass
        elif data_scope == "merchant":
            # 商户级数据隔离
            if current_user.merchant_id:
                query = query.filter(WalmartCK.merchant_id == current_user.merchant_id)
            else:
                query = query.filter(WalmartCK.merchant_id.is_(None))
        elif data_scope == "department_sub":
            # 本部门及子部门数据
            accessible_dept_ids = self._get_cached_accessible_departments(current_user)
            if accessible_dept_ids:
                query = query.filter(WalmartCK.department_id.in_(accessible_dept_ids))
            elif current_user.department_id:
                query = query.filter(WalmartCK.department_id == current_user.department_id)
            # 同时应用商户级隔离
            if current_user.merchant_id:
                query = query.filter(WalmartCK.merchant_id == current_user.merchant_id)
            else:
                query = query.filter(WalmartCK.merchant_id.is_(None))
        elif data_scope == "department":
            # 本部门数据
            if current_user.department_id:
                query = query.filter(WalmartCK.department_id == current_user.department_id)
            # 同时应用商户级隔离
            if current_user.merchant_id:
                query = query.filter(WalmartCK.merchant_id == current_user.merchant_id)
            else:
                query = query.filter(WalmartCK.merchant_id.is_(None))
        elif data_scope == "self":
            # 本人数据 - 只能访问自己创建的数据
            query = query.filter(WalmartCK.created_by == current_user.id)
            # 同时应用商户级隔离
            if current_user.merchant_id:
                query = query.filter(WalmartCK.merchant_id == current_user.merchant_id)
            else:
                query = query.filter(WalmartCK.merchant_id.is_(None))
        else:
            # 默认情况：应用最严格的隔离
            query = query.filter(WalmartCK.created_by == current_user.id)
            # 同时应用商户级隔离
            if current_user.merchant_id:
                query = query.filter(WalmartCK.merchant_id == current_user.merchant_id)
            else:
                query = query.filter(WalmartCK.merchant_id.is_(None))

        return query

    def get_multi_with_isolation(
        self,
        current_user: User,
        skip: int = 0,
        limit: int = 100,
        filters: Optional[Dict[str, Any]] = None,
        order_by: Optional[str] = None,
        order_desc: bool = False
    ) -> List[WalmartCK]:
        """
        获取多个CK对象（应用数据隔离和正确的时间过滤）
        重写基类方法以支持时间范围过滤

        Args:
            current_user: 当前用户
            skip: 跳过的记录数
            limit: 限制返回的记录数
            filters: 过滤条件字典
            order_by: 排序字段
            order_desc: 是否降序排列

        Returns:
            List[WalmartCK]: CK对象列表
        """
        try:
            query = self.db.query(self.model)

            # 应用数据隔离
            query = self.apply_data_isolation(query, current_user)

            # 应用过滤条件 - 使用CRUD层的_apply_filters方法来正确处理时间过滤
            if filters:
                # 使用CRUD层的过滤逻辑
                from app.crud.walmart_ck import walmart_ck
                query = walmart_ck._apply_filters(query, filters)

            # 应用排序
            if order_by and hasattr(self.model, order_by):
                order_field = getattr(self.model, order_by)
                if order_desc:
                    from sqlalchemy import desc
                    query = query.order_by(desc(order_field))
                else:
                    from sqlalchemy import asc
                    query = query.order_by(asc(order_field))

            return query.offset(skip).limit(limit).all()
        except Exception as e:
            self.logger.error(f"获取CK列表失败: {e}")
            return []

    def count_with_isolation(self, current_user: User, filters: Optional[Dict[str, Any]] = None) -> int:
        """
        获取符合条件的CK总数（应用数据隔离和正确的时间过滤）
        重写基类方法以支持时间范围过滤

        Args:
            current_user: 当前用户
            filters: 过滤条件字典

        Returns:
            int: 符合条件的记录总数
        """
        try:
            query = self.db.query(self.model)

            # 应用数据隔离
            query = self.apply_data_isolation(query, current_user)

            # 应用过滤条件 - 使用CRUD层的_apply_filters方法来正确处理时间过滤
            if filters:
                # 使用CRUD层的过滤逻辑
                from app.crud.walmart_ck import walmart_ck
                query = walmart_ck._apply_filters(query, filters)

            return query.count()
        except Exception as e:
            self.logger.error(f"获取CK总数失败: {e}")
            return 0

    def search_walmart_cks(
        self,
        search_term: str,
        current_user: User,
        skip: int = 0,
        limit: int = 100
    ) -> List[WalmartCK]:
        """
        搜索沃尔玛CK

        Args:
            search_term: 搜索关键词
            current_user: 当前用户
            skip: 跳过的记录数
            limit: 限制返回的记录数

        Returns:
            List[WalmartCK]: CK列表
        """
        search_fields = ['sign', 'description']  # 修复：使用实际存在的字段
        return self.search(search_term, search_fields, current_user, skip, limit)

    def _is_ck_available(self, ck: WalmartCK) -> bool:
        """
        检查CK是否可用（纯查询方法，不修改CK状态）

        检查项目：
        1. 未被软删除 (is_deleted=False)
        2. 处于启用状态 (active=True)
        3. 未达到总次数限制

        Args:
            ck: CK对象

        Returns:
            bool: 是否可用
        """
        # 检查是否被软删除
        if ck.is_deleted:
            return False

        # 检查基本状态
        if not ck.active:
            return False

        # 检查总次数限制 - 使用bind_count作为累计使用次数
        if ck.total_limit and ck.bind_count >= ck.total_limit:
            return False

        return True

    def _select_best_ck(self, available_cks: List[WalmartCK]) -> WalmartCK:
        """
        从可用CK中选择最佳的一个 - 修复负载均衡问题

        Args:
            available_cks: 可用CK列表

        Returns:
            WalmartCK: 最佳CK
        """
        import secrets  # 使用cryptographically secure random

        # 按使用次数排序，选择使用次数最少的
        available_cks.sort(key=lambda x: x.bind_count)

        # 如果有多个使用次数相同的，使用安全随机选择一个
        min_used = available_cks[0].bind_count
        candidates = [ck for ck in available_cks if ck.bind_count == min_used]

        # 【修复】使用cryptographically secure random确保真正的随机性
        return secrets.choice(candidates)

    async def _select_validated_ck(
        self, candidate_cks: List[WalmartCK], merchant_id: int,
        bind_context: Optional[Dict[str, Any]] = None
    ) -> Optional[WalmartCK]:
        """
        从候选CK中选择经过验证的有效CK

        Args:
            candidate_cks: 候选CK列表
            merchant_id: 商户ID
            bind_context: 绑卡上下文信息

        Returns:
            Optional[WalmartCK]: 验证有效的CK或None
        """
        from app.services.ck_validation_service import CKValidationService

        validation_service = CKValidationService(self.db)

        # 按使用次数排序候选CK
        sorted_cks = sorted(candidate_cks, key=lambda x: x.bind_count)

        # 增强日志记录，包含绑卡上下文
        if bind_context:
            self.logger.info(
                f"开始验证商户{merchant_id}的{len(sorted_cks)}个候选CK (绑卡上下文) | "
                f"record_id={bind_context.get('record_id')} | "
                f"trace_id={bind_context.get('trace_id')}"
            )
        else:
            self.logger.info(f"开始验证商户{merchant_id}的{len(sorted_cks)}个候选CK")

        # 逐个验证CK，直到找到有效的CK
        for ck in sorted_cks:
            try:
                # 记录CK选择日志
                if bind_context:
                    self.logger.info(
                        f"验证CK {ck.id} (使用次数: {ck.bind_count}/{ck.total_limit}) | "
                        f"record_id={bind_context.get('record_id')} | "
                        f"trace_id={bind_context.get('trace_id')}"
                    )
                else:
                    self.logger.info(f"验证CK {ck.id} (使用次数: {ck.bind_count}/{ck.total_limit})")

                # 传递绑卡上下文给验证服务
                validation_result = await validation_service.validate_ck_availability(ck, bind_context)

                if validation_result["is_valid"]:
                    if bind_context:
                        self.logger.info(
                            f"CK {ck.id} 验证通过，选择为可用CK | "
                            f"record_id={bind_context.get('record_id')} | "
                            f"trace_id={bind_context.get('trace_id')}"
                        )
                    else:
                        self.logger.info(f"CK {ck.id} 验证通过，选择为可用CK")
                    return ck
                else:
                    warning_msg = (
                        f"CK {ck.id} 验证失败: {validation_result['error_message']} "
                        f"(错误码: {validation_result['error_code']})"
                    )
                    if bind_context:
                        warning_msg += (
                            f" | record_id={bind_context.get('record_id')} | "
                            f"trace_id={bind_context.get('trace_id')}"
                        )
                    self.logger.warning(warning_msg)
                    # 验证失败的CK已在validation_service中处理（如需要禁用）
                    continue

            except Exception as e:
                error_msg = f"验证CK {ck.id} 时发生异常: {e}"
                if bind_context:
                    error_msg += (
                        f" | record_id={bind_context.get('record_id')} | "
                        f"trace_id={bind_context.get('trace_id')}"
                    )
                self.logger.error(error_msg)
                continue

        # 所有CK都验证失败
        error_msg = f"商户{merchant_id}的所有候选CK都验证失败"
        if bind_context:
            error_msg += (
                f" | record_id={bind_context.get('record_id')} | "
                f"trace_id={bind_context.get('trace_id')}"
            )
        self.logger.error(error_msg)
        return None

    def check_merchant_isolation_integrity(self, merchant_id: int) -> Dict[str, Any]:
        """
        检查商户隔离的数据完整性

        Args:
            merchant_id: 商户ID

        Returns:
            Dict[str, Any]: 检查结果
        """
        try:
            from app.models.card_record import CardRecord

            issues = []

            # 检查是否有card_records使用了其他商户的CK
            cross_merchant_records = self.db.query(CardRecord).join(
                WalmartCK, CardRecord.walmart_ck_id == WalmartCK.id
            ).filter(
                CardRecord.merchant_id == merchant_id,
                WalmartCK.merchant_id != merchant_id,
                CardRecord.walmart_ck_id.isnot(None)
            ).all()

            if cross_merchant_records:
                issues.append({
                    'type': 'cross_merchant_ck_usage',
                    'count': len(cross_merchant_records),
                    'description': f'发现{len(cross_merchant_records)}条记录使用了其他商户的CK',
                    'record_ids': [str(r.id) for r in cross_merchant_records[:10]]  # 只显示前10条
                })

            # 检查是否有CK被其他商户的记录使用
            merchant_cks = self.db.query(WalmartCK).filter(
                WalmartCK.merchant_id == merchant_id
            ).all()

            for ck in merchant_cks:
                other_merchant_usage = self.db.query(CardRecord).filter(
                    CardRecord.walmart_ck_id == ck.id,
                    CardRecord.merchant_id != merchant_id
                ).count()

                if other_merchant_usage > 0:
                    issues.append({
                        'type': 'ck_used_by_other_merchant',
                        'ck_id': ck.id,
                        'usage_count': other_merchant_usage,
                        'description': f'CK {ck.id} 被其他商户的{other_merchant_usage}条记录使用'
                    })

            return {
                'merchant_id': merchant_id,
                'is_clean': len(issues) == 0,
                'issues_count': len(issues),
                'issues': issues
            }

        except Exception as e:
            self.logger.error(f"检查商户隔离完整性失败: {e}")
            return {
                'merchant_id': merchant_id,
                'is_clean': False,
                'error': str(e)
            }

    def get_single_ck_detailed_statistics(
        self,
        ck_id: int,
        current_user: User,
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None,
        time_range: str = "month"
    ) -> Dict[str, Any]:
        """
        获取单个CK的详细统计信息

        Args:
            ck_id: CK ID
            current_user: 当前用户
            start_date: 开始日期
            end_date: 结束日期
            time_range: 时间范围

        Returns:
            Dict[str, Any]: CK详细统计信息
        """
        try:
            from sqlalchemy import func, case, desc
            from app.models.card_record import CardRecord
            from datetime import datetime, timedelta

            # 获取CK基本信息（过滤已删除的CK）
            ck = self.db.query(WalmartCK).filter(
                WalmartCK.id == ck_id,
                WalmartCK.is_deleted == False
            ).first()
            if not ck:
                raise ValueError("CK不存在或已被删除")

            # 权限检查
            if not current_user.is_superuser and current_user.merchant_id != ck.merchant_id:
                raise ValueError("无权限查看该CK的统计信息")

            # 处理时间范围
            if time_range == "today":
                start_date = datetime.now().replace(hour=0, minute=0, second=0, microsecond=0)
                end_date = datetime.now().replace(hour=23, minute=59, second=59, microsecond=999999)
            elif time_range == "week":
                start_date = datetime.now() - timedelta(days=7)
                end_date = datetime.now()
            elif time_range == "month":
                start_date = datetime.now() - timedelta(days=30)
                end_date = datetime.now()
            elif time_range == "custom" and start_date and end_date:
                # 使用传入的自定义时间范围
                pass
            else:
                # 默认最近30天
                start_date = datetime.now() - timedelta(days=30)
                end_date = datetime.now()

            # 基础统计查询
            base_query = self.db.query(CardRecord).filter(
                CardRecord.walmart_ck_id == ck_id,
                CardRecord.created_at.between(start_date, end_date)
            )

            # 获取基础统计数据
            stats_result = base_query.with_entities(
                func.count(CardRecord.id).label('total_count'),
                func.sum(case((CardRecord.status == 'success', 1), else_=0)).label('success_count'),
                func.sum(case((CardRecord.status == 'failed', 1), else_=0)).label('failed_count'),
                func.sum(case((CardRecord.status == 'pending', 1), else_=0)).label('pending_count'),
                func.sum(case((CardRecord.status == 'processing', 1), else_=0)).label('processing_count'),
                # 金额统计
                func.sum(CardRecord.amount).label('total_amount'),
                func.sum(case((CardRecord.status == 'success', CardRecord.amount), else_=0)).label('success_amount'),
                func.sum(case((CardRecord.status == 'success', CardRecord.actual_amount), else_=0)).label('actual_amount'),
                # 处理时间统计
                func.avg(case((CardRecord.status == 'success', CardRecord.process_time), else_=None)).label('avg_process_time')
            ).first()

            total_count = stats_result.total_count or 0
            success_count = stats_result.success_count or 0
            failed_count = stats_result.failed_count or 0
            pending_count = stats_result.pending_count or 0
            processing_count = stats_result.processing_count or 0

            # 计算成功率
            success_rate = (success_count / total_count * 100) if total_count > 0 else 0

            # 金额统计
            total_amount = stats_result.total_amount or 0
            success_amount = stats_result.success_amount or 0
            actual_amount = stats_result.actual_amount or 0
            avg_process_time = stats_result.avg_process_time or 0

            # 平均金额
            avg_amount = (actual_amount / success_count) if success_count > 0 else 0

            return {
                'ck_info': {
                    'id': ck.id,
                    'sign': ck.sign,
                    'description': ck.description,
                    'merchant_name': ck.merchant.name if ck.merchant else None,
                    'department_name': ck.department.name if ck.department else None,
                    'active': ck.active,
                    'total_limit': ck.total_limit,
                    'bind_count': ck.bind_count
                },
                'basic_stats': {
                    'total_count': total_count,
                    'success_count': success_count,
                    'failed_count': failed_count,
                    'pending_count': pending_count,
                    'processing_count': processing_count,
                    'success_rate': round(success_rate, 2)
                },
                'amount_stats': {
                    'total_amount': total_amount,
                    'success_amount': success_amount,
                    'actual_amount': actual_amount,
                    'avg_amount': round(avg_amount, 2),
                    'amount_difference': actual_amount - success_amount
                },
                'performance_stats': {
                    'avg_process_time': round(avg_process_time, 2) if avg_process_time else 0
                },
                'time_range': {
                    'start_date': start_date.isoformat(),
                    'end_date': end_date.isoformat(),
                    'range_type': time_range
                }
            }

        except Exception as e:
            self.logger.error(f"获取CK详细统计信息失败: {e}")
            raise

    def get_ck_daily_trend(
        self,
        ck_id: int,
        current_user: User,
        days: int = 30
    ) -> List[Dict[str, Any]]:
        """
        获取CK的每日趋势数据

        Args:
            ck_id: CK ID
            current_user: 当前用户
            days: 天数

        Returns:
            List[Dict[str, Any]]: 每日趋势数据
        """
        try:
            from sqlalchemy import func, case
            from app.models.card_record import CardRecord
            from datetime import datetime, timedelta

            # 权限检查
            ck = self.db.query(WalmartCK).filter(WalmartCK.id == ck_id).first()
            if not ck:
                raise ValueError("CK不存在")

            if not current_user.is_superuser and current_user.merchant_id != ck.merchant_id:
                raise ValueError("无权限查看该CK的统计信息")

            # 计算时间范围
            end_date = datetime.now().date()
            start_date = end_date - timedelta(days=days)

            # 查询每日统计数据
            daily_stats = self.db.query(
                func.date(CardRecord.created_at).label('date'),
                func.count(CardRecord.id).label('total_count'),
                func.sum(case((CardRecord.status == 'success', 1), else_=0)).label('success_count'),
                func.sum(case((CardRecord.status == 'failed', 1), else_=0)).label('failed_count'),
                func.sum(CardRecord.amount).label('total_amount'),
                func.sum(case((CardRecord.status == 'success', CardRecord.actual_amount), else_=0)).label('success_amount')
            ).filter(
                CardRecord.walmart_ck_id == ck_id,
                func.date(CardRecord.created_at) >= start_date,
                func.date(CardRecord.created_at) <= end_date
            ).group_by(
                func.date(CardRecord.created_at)
            ).order_by(
                func.date(CardRecord.created_at)
            ).all()

            # 构建完整的日期序列
            daily_trend = []
            current_date = start_date
            stats_dict = {stat.date: stat for stat in daily_stats}

            while current_date <= end_date:
                if current_date in stats_dict:
                    stat = stats_dict[current_date]
                    total_count = stat.total_count or 0
                    success_count = stat.success_count or 0
                    failed_count = stat.failed_count or 0
                    total_amount = stat.total_amount or 0
                    success_amount = stat.success_amount or 0
                    success_rate = (success_count / total_count * 100) if total_count > 0 else 0
                else:
                    total_count = success_count = failed_count = 0
                    total_amount = success_amount = 0
                    success_rate = 0

                daily_trend.append({
                    'date': current_date.isoformat(),
                    'total_count': total_count,
                    'success_count': success_count,
                    'failed_count': failed_count,
                    'success_rate': round(success_rate, 2),
                    'total_amount': total_amount,
                    'success_amount': success_amount
                })

                current_date += timedelta(days=1)

            return daily_trend

        except Exception as e:
            self.logger.error(f"获取CK每日趋势失败: {e}")
            raise

    def get_ck_failure_analysis(
        self,
        ck_id: int,
        current_user: User,
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None,
        limit: int = 10
    ) -> List[Dict[str, Any]]:
        """
        获取CK的失败原因分析

        Args:
            ck_id: CK ID
            current_user: 当前用户
            start_date: 开始日期
            end_date: 结束日期
            limit: 返回的失败原因数量限制

        Returns:
            List[Dict[str, Any]]: 失败原因分析
        """
        try:
            from sqlalchemy import func
            from app.models.card_record import CardRecord
            from datetime import datetime, timedelta

            # 权限检查
            ck = self.db.query(WalmartCK).filter(WalmartCK.id == ck_id).first()
            if not ck:
                raise ValueError("CK不存在")

            if not current_user.is_superuser and current_user.merchant_id != ck.merchant_id:
                raise ValueError("无权限查看该CK的统计信息")

            # 默认时间范围
            if not start_date:
                start_date = datetime.now() - timedelta(days=30)
            if not end_date:
                end_date = datetime.now()

            # 查询失败原因统计
            failure_stats = self.db.query(
                CardRecord.error_message,
                func.count(CardRecord.id).label('count')
            ).filter(
                CardRecord.walmart_ck_id == ck_id,
                CardRecord.status == 'failed',
                CardRecord.error_message.isnot(None),
                CardRecord.created_at.between(start_date, end_date)
            ).group_by(
                CardRecord.error_message
            ).order_by(
                func.count(CardRecord.id).desc()
            ).limit(limit).all()

            # 构建失败原因列表
            failure_analysis = []
            for i, (error_message, count) in enumerate(failure_stats, 1):
                failure_analysis.append({
                    'rank': i,
                    'error_message': error_message,
                    'count': count,
                    'percentage': 0  # 将在后面计算
                })

            # 计算总失败数和百分比
            total_failures = sum(item['count'] for item in failure_analysis)
            for item in failure_analysis:
                item['percentage'] = round((item['count'] / total_failures * 100), 2) if total_failures > 0 else 0

            return failure_analysis

        except Exception as e:
            self.logger.error(f"获取CK失败原因分析失败: {e}")
            raise
