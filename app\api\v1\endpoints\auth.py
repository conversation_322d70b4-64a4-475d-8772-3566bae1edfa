from datetime import timedelta, datetime
from typing import Any, List, Optional
from fastapi import APIRouter, Depends, HTTPException, status, Request
from fastapi.security import OAuth2PasswordRequestForm
from sqlalchemy.orm import Session
import logging

from app import models, schemas
from app.api import deps
from app.core import security
from app.core.config import settings
from app.utils.password import verify_password
from app.models.user import User
from app.services.totp_service import TOTPService

router = APIRouter()
# 添加日志器
logger = logging.getLogger("app.api.auth")


def _get_client_ip(request: Request) -> str:
    """获取客户端IP地址"""
    # 优先从X-Forwarded-For头获取（适用于代理/负载均衡器）
    forwarded_for = request.headers.get("X-Forwarded-For")
    if forwarded_for:
        # X-Forwarded-For可能包含多个IP，取第一个
        return forwarded_for.split(",")[0].strip()

    # 从X-Real-IP头获取（Nginx代理常用）
    real_ip = request.headers.get("X-Real-IP")
    if real_ip:
        return real_ip.strip()

    # 最后使用客户端直连IP
    return request.client.host if request.client else "unknown"


def _update_user_login_info(db: Session, user: models.User, client_ip: str) -> None:
    """更新用户登录信息"""
    try:
        # 修复时区问题：使用上海时区时间
        from app.utils.time_utils import get_current_time
        current_time = get_current_time().strftime("%Y-%m-%d %H:%M:%S")
        user.last_login_ip = client_ip
        user.last_login_time = current_time
        db.commit()
        logger.info(f"更新用户 {user.username} 登录信息: IP={client_ip}, 时间={current_time}")
    except Exception as e:
        logger.error(f"更新用户登录信息失败: {e}")
        db.rollback()
        # 不抛出异常，避免影响登录流程


def _find_user_by_username(db: Session, username: str) -> Optional[models.User]:
    """通过用户名查找用户"""
    user = db.query(models.User).filter(models.User.username == username).first()
    if user:
        logger.info(f"通过用户名找到用户: {username}")
    else:
        logger.info(f"通过用户名未找到用户: {username}")
    return user


def _find_user_by_email(db: Session, email: str) -> Optional[models.User]:
    """通过邮箱查找用户"""
    logger.info(f"尝试使用邮箱查找用户: {email}")
    user = db.query(models.User).filter(models.User.email == email).first()
    if user:
        logger.info(f"通过邮箱找到用户: {email}")
    else:
        logger.info(f"通过邮箱未找到用户: {email}")
    return user


def _authenticate_user(db: Session, username: str) -> Optional[models.User]:
    """认证用户，支持用户名和邮箱登录"""
    # 首先尝试用户名
    user = _find_user_by_username(db, username)

    # 如果未找到且包含@符号，尝试邮箱
    if not user and "@" in username:
        user = _find_user_by_email(db, username)

    return user


def _validate_user_credentials(user: Optional[models.User], password: str, username: str):
    """验证用户凭据"""
    if not user:
        logger.warning(f"登录失败: 用户不存在 - {username}")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="用户名或密码不正确",
            headers={"WWW-Authenticate": "Bearer"},
        )

    # 验证密码
    password_verified = verify_password(password, user.hashed_password)
    logger.info(f"密码验证结果: {password_verified} (用户: {username})")

    if not password_verified:
        logger.warning(f"登录失败: 密码不正确 - {username}")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="用户名或密码不正确",
            headers={"WWW-Authenticate": "Bearer"},
        )


def _validate_user_status(user: models.User, username: str):
    """验证用户状态"""
    if not user.is_active:
        logger.warning(f"登录失败: 用户未激活 - {username}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST, detail="用户未激活"
        )


def _validate_merchant_status(db: Session, user: models.User, username: str):
    """验证商户状态"""
    if user.merchant_id and not user.is_superuser:
        merchant = db.query(models.Merchant).filter(models.Merchant.id == user.merchant_id).first()
        if not merchant:
            logger.warning(f"登录失败: 用户所属商家不存在 - {username}")
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST, detail="用户所属商家不存在"
            )
        if not merchant.status:
            logger.warning(f"登录失败: 用户所属商家已被禁用 - {username}")
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN, detail="所属商家已被禁用，无法登录"
            )


def _validate_totp_if_required(db: Session, user: models.User, totp_code: str, client_ip: str):
    """验证双因子认证（如果需要）"""
    from app.services.totp_service import TOTPService

    try:
        totp_service = TOTPService(db)
        is_required, in_grace_period = totp_service.check_totp_required(user)

        # 如果用户启用了TOTP，必须验证
        if user.totp_enabled:
            if not totp_code:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="需要提供双因子认证码"
                )

            verify_result = totp_service.verify_totp(user, totp_code, client_ip)
            if not verify_result.success:
                # 尝试验证备用码
                if not totp_service._verify_backup_code(user, totp_code):
                    raise HTTPException(
                        status_code=status.HTTP_401_UNAUTHORIZED,
                        detail="双因子认证码错误"
                    )

        # 如果强制要求但未启用，且不在宽限期内
        elif is_required and not in_grace_period:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="必须启用双因子认证才能登录"
            )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"TOTP验证失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="双因子认证验证失败"
        )


def _create_access_token(user: models.User) -> str:
    """创建访问令牌"""
    access_token_expires = timedelta(minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES)
    return security.create_access_token(user.id, expires_delta=access_token_expires)


@router.post("/check-totp", response_model=dict)
def check_user_totp_status(
    request_data: dict,
    db: Session = Depends(deps.get_db)
) -> Any:
    """
    检查用户是否启用了TOTP（登录前预检查）
    """
    username = request_data.get("username")
    try:
        # 查找用户（不验证密码）
        user = db.query(User).filter(User.username == username).first()
        if not user:
            # 为了安全，不透露用户是否存在，返回默认值
            return {
                "totp_enabled": False,
                "totp_required": False
            }

        # 检查TOTP状态
        totp_service = TOTPService(db)
        is_required, in_grace_period = totp_service.check_totp_required(user)

        return {
            "totp_enabled": user.totp_enabled or False,
            "totp_required": is_required and not in_grace_period
        }

    except Exception as e:
        logger.error(f"检查用户TOTP状态失败: {e}")
        # 出错时返回安全的默认值
        return {
            "totp_enabled": False,
            "totp_required": False
        }


@router.post("/login", response_model=schemas.Token)
def login_access_token(
    request: Request,
    db: Session = Depends(deps.get_db),
    form_data: OAuth2PasswordRequestForm = Depends()
) -> Any:
    """
    获取OAuth2兼容的令牌
    """
    try:
        logger.info(f"尝试登录用户: {form_data.username}")

        # 认证用户
        user = _authenticate_user(db, form_data.username)

        # 验证用户凭据
        _validate_user_credentials(user, form_data.password, form_data.username)

        # 验证用户状态
        _validate_user_status(user, form_data.username)

        # 验证商户状态
        _validate_merchant_status(db, user, form_data.username)

        # 检查双因子认证
        client_ip = _get_client_ip(request)
        totp_code = request.headers.get("X-TOTP-Code")  # 从请求头获取TOTP码
        _validate_totp_if_required(db, user, totp_code, client_ip)

        # 创建访问令牌
        token = _create_access_token(user)

        # 更新用户登录信息
        _update_user_login_info(db, user, client_ip)

        logger.info(f"用户登录成功: {form_data.username}")

        return {
            "access_token": token,
            "token_type": "bearer",
            "expires_in": settings.ACCESS_TOKEN_EXPIRE_MINUTES * 60,  # 转换为秒
        }
    except Exception as e:
        # 记录未捕获的异常
        logger.error(f"登录过程中发生未捕获的异常: {str(e)}", exc_info=True)
        raise


@router.post("/logout")
def logout(current_user: models.User = Depends(deps.get_current_user)) -> Any:
    """
    用户登出

    目前JWT机制无需后端处理登出，由前端删除token即可
    此接口仅作为占位符，可用于后续实现如令牌黑名单等功能
    """
    logger.info(f"用户登出: {current_user.username}")
    return {"msg": "登出成功"}


@router.post("/refresh-token", response_model=schemas.Token)
def refresh_token(current_user: models.User = Depends(deps.get_current_user)) -> Any:
    """
    刷新令牌
    """
    logger.info(f"刷新用户令牌: {current_user.username}")
    access_token_expires = timedelta(minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES)
    return {
        "access_token": security.create_access_token(
            current_user.id, expires_delta=access_token_expires
        ),
        "token_type": "bearer",
        "expires_in": settings.ACCESS_TOKEN_EXPIRE_MINUTES * 60,  # 转换为秒
    }


@router.put("/change-password", response_model=schemas.MessageResponse)
def change_password(
    password_data: schemas.PasswordChange,
    current_user: models.User = Depends(deps.get_current_user),
    db: Session = Depends(deps.get_db)
) -> Any:
    """
    修改密码
    """
    try:
        from app.core.security import verify_password, get_password_hash

        # 验证当前密码
        if not verify_password(password_data.current_password, current_user.hashed_password):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="当前密码错误"
            )

        # 更新密码
        current_user.hashed_password = get_password_hash(password_data.new_password)
        db.commit()

        logger.info(f"用户修改密码成功: {current_user.username}")
        return schemas.MessageResponse(message="密码修改成功")

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"修改密码失败: {e}")
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="修改密码失败"
        )


# 添加诊断路由
@router.get("/system-check")
def system_check(db: Session = Depends(deps.get_db)) -> Any:
    """
    系统组件诊断接口，用于检查数据库连接等关键组件
    """
    try:
        # 测试数据库连接
        db_result = db.execute("SELECT 1").fetchone()
        db_status = "正常" if db_result else "异常"

        # 检查配置
        secret_key_status = "已设置" if settings.SECRET_KEY else "未设置"

        # 验证用户表是否存在
        user_count = db.query(models.User).count()

        return {
            "status": "ok",
            "database": db_status,
            "secret_key": secret_key_status,
            "user_count": user_count,
            "api_version": "1.0",
        }
    except Exception as e:
        logger.error(f"系统诊断失败: {str(e)}", exc_info=True)
        return {
            "status": "error",
            "error": str(e),
            "type": e.__class__.__name__,
        }


@router.get("/me", response_model=schemas.User)
def read_users_me(current_user: models.User = Depends(deps.get_current_active_user)) -> Any:
    """
    获取当前用户信息
    """
    logger.info(f"用户 {current_user.username} 获取个人信息")
    return current_user


@router.get("/permissions", response_model=List[str])
def get_current_user_permissions(
    current_user: models.User = Depends(deps.get_current_active_user),
    db: Session = Depends(deps.get_db),
) -> Any:
    """
    获取当前用户的权限列表
    """
    try:
        from app.core.auth import auth_service

        # 使用新的权限服务获取用户权限
        permissions = auth_service.get_user_permissions(current_user, db)
        logger.info(f"用户 {current_user.username} 获取权限成功，权限数量: {len(permissions)}")
        return permissions

    except Exception as e:
        logger.error(f"获取用户权限失败: {e}", exc_info=True)
        # 返回基础权限
        return ["dashboard:view"]
