from typing import Optional, Dict, Any
from pydantic import BaseModel, Field
from datetime import datetime


class MigrationLogBase(BaseModel):
    """迁移日志基础模型"""
    migration_name: str = Field(..., description="迁移名称")
    status: str = Field(..., description="状态")
    message: Optional[str] = Field(None, description="消息")
    data_summary: Optional[Dict[str, Any]] = Field(None, description="数据摘要")

    class Config:
        from_attributes = True


class MigrationLogCreate(MigrationLogBase):
    """创建迁移日志模型"""
    pass


class MigrationLogUpdate(BaseModel):
    """更新迁移日志模型"""
    status: Optional[str] = Field(None, description="状态")
    message: Optional[str] = Field(None, description="消息")
    data_summary: Optional[Dict[str, Any]] = Field(None, description="数据摘要")
    completed_at: Optional[datetime] = Field(None, description="完成时间")


class MigrationLogInDB(MigrationLogBase):
    """数据库中的迁移日志模型"""
    id: int
    created_at: datetime
    completed_at: Optional[datetime] = None


class MigrationLogOut(BaseModel):
    """返回给前端的迁移日志模型"""
    id: int
    migration_name: str
    status: str
    message: Optional[str] = None
    data_summary: Optional[Dict[str, Any]] = None
    created_at: datetime
    completed_at: Optional[datetime] = None

    class Config:
        from_attributes = True
