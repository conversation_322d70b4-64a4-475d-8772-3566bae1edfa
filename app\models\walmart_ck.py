from sqlalchemy import Column, String, Integer, BigInteger, Boolean, Text, Index, ForeignKey
from sqlalchemy.orm import relationship
from app.models.base import BaseModel, TimestampMixin


class WalmartCK(BaseModel, TimestampMixin):
    """沃尔玛CK配置模型"""

    __tablename__ = "walmart_ck"

    id = Column(BigInteger, primary_key=True, index=True, autoincrement=True, comment="主键ID")
    sign = Column(
        Text,
        nullable=False,
        comment="用户签名,格式：ck#token#wx_sign#version,如：25487f6f129649999ef6b1f269b2a1f5@d0e0e25d37720f856a3ba753089b1e47#mmb5Lz2g3i4ilzn/kHXpFg==#26",
    )
    total_limit = Column(Integer, nullable=False, default=20, comment="总绑卡次数限制")
    bind_count = Column(Integer, nullable=False, default=0, comment="累计已绑卡数量")
    last_bind_time = Column(String(64), nullable=True, comment="最后绑卡时间")
    active = Column(Boolean, nullable=False, default=True, comment="是否启用")
    description = Column(String(255), nullable=True, comment="设置描述")
    created_by = Column(BigInteger, ForeignKey("users.id", ondelete="SET NULL"), nullable=True, comment="创建者用户ID")
    merchant_id = Column(BigInteger, ForeignKey("merchants.id", ondelete="CASCADE"), nullable=False, comment="所属商户ID（数据隔离）")
    department_id = Column(BigInteger, ForeignKey("departments.id", ondelete="CASCADE"), nullable=False, comment="所属部门ID（数据隔离）")
    is_deleted = Column(Boolean, nullable=False, default=False, comment="是否已删除：0未删除，1已删除")

    # 关联关系
    creator = relationship("User", foreign_keys=[created_by])
    merchant = relationship("Merchant", back_populates="walmart_cks")
    department = relationship("Department", back_populates="walmart_cks")
    card_records = relationship("CardRecord", back_populates="walmart_ck")
    binding_logs = relationship("BindingLog", back_populates="walmart_ck")

    # 使用 Index 对象创建带长度的索引
    __table_args__ = (
        Index("uk_walmart_ck_sign", "sign", unique=True, mysql_length=255),
    )

    def to_dict(self):
        """转换为字典"""
        return {
            "id": self.id,
            "sign": self.sign,
            "total_limit": self.total_limit,
            "bind_count": self.bind_count,
            "last_bind_time": self.last_bind_time,
            "active": self.active,
            "description": self.description,
            "created_by": self.created_by,
            "merchant_id": self.merchant_id,
            "department_id": self.department_id,
            "is_deleted": self.is_deleted,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None,
        }
