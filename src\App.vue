<script setup>
import { ref, provide } from 'vue'
import { ElLoading } from 'element-plus'

// 全局加载状态，可通过 inject('isLoading') 在任何组件中使用
const isLoading = ref(false)
provide('isLoading', isLoading)

// 全局加载指示器服务
const showGlobalLoading = () => {
    const loading = ElLoading.service({
        lock: true,
        text: '加载中...',
        background: 'rgba(0, 0, 0, 0.7)'
    })
    return loading
}

// 提供全局加载方法
provide('showGlobalLoading', showGlobalLoading)
</script>

<template>
    <div class="app-wrapper">
        <!-- 路由视图 -->
        <router-view v-slot="{ Component }">
            <transition name="fade-transform" mode="out-in">
                <component :is="Component" />
            </transition>
        </router-view>
    </div>
</template>

<style>
/* 全局样式重置 */
html,
body {
    margin: 0;
    padding: 0;
    height: 100%;
    font-family: 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', Arial, sans-serif;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

#app {
    height: 100%;
}

.app-wrapper {
    height: 100%;
}

/* 路由切换动画 */
.fade-transform-enter-active,
.fade-transform-leave-active {
    transition: all 0.3s;
}

.fade-transform-enter-from {
    opacity: 0;
    transform: translateX(-20px);
}

.fade-transform-leave-to {
    opacity: 0;
    transform: translateX(20px);
}

/* 一些全局工具类 */
.flex-center {
    display: flex;
    justify-content: center;
    align-items: center;
}

.text-ellipsis {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

/* 主题相关变量，可在组件中引用 */
:root {
    --primary-color: #409EFF;
    --success-color: #67C23A;
    --warning-color: #E6A23C;
    --danger-color: #F56C6C;
    --info-color: #909399;

    --header-height: 60px;
    --sidebar-width: 220px;
    --sidebar-collapsed-width: 64px;

    --background-color: #f0f2f5;
    --text-color: #303133;
    --text-color-secondary: #909399;
}
</style>
