#!/usr/bin/env python3
"""
测试CK绑卡计数更新
验证绑卡成功后CK的bind_count是否正确更新
"""

import asyncio
import sys
import os
import time
from typing import Dict, List

# 添加项目路径
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.insert(0, project_root)

from app.api.deps import get_db
from app.models.walmart_ck import WalmartCK
from app.services.redis_ck_wrapper import create_optimized_ck_service
from app.services.production_ck_service import ProductionCKService
from app.core.logging import get_logger

logger = get_logger("test_ck_bind_count")


class CKBindCountTester:
    """CK绑卡计数测试器"""
    
    def __init__(self):
        self.db = next(get_db())
        
    def __enter__(self):
        return self
        
    def __exit__(self, exc_type, exc_val, exc_tb):
        if self.db:
            self.db.close()
    
    async def test_ck_bind_count_update(self, merchant_id: int = 2):
        """测试CK绑卡计数更新"""
        print(f"🧪 测试CK绑卡计数更新")
        print(f"商户ID: {merchant_id}")
        print("="*60)
        
        # 获取可用CK
        available_cks = self.db.query(WalmartCK).filter(
            WalmartCK.merchant_id == merchant_id,
            WalmartCK.active == True,
            WalmartCK.is_deleted == False
        ).all()
        
        if not available_cks:
            print("❌ 没有可用的CK")
            return
        
        print(f"📊 找到 {len(available_cks)} 个可用CK")
        
        # 显示初始状态
        print(f"\n📋 初始CK状态:")
        initial_states = {}
        for ck in available_cks:
            initial_states[ck.id] = {
                'bind_count': ck.bind_count,
                'total_limit': ck.total_limit,
                'active': ck.active
            }
            print(f"  CK {ck.id}: bind_count={ck.bind_count}, total_limit={ck.total_limit}, active={ck.active}")
        
        # 测试原有服务
        print(f"\n🎯 测试原有服务的CK计数更新:")
        await self._test_original_service(merchant_id, available_cks[0])
        
        # 测试生产级服务
        print(f"\n🎯 测试生产级服务的CK计数更新:")
        await self._test_production_service(merchant_id, available_cks[0] if len(available_cks) > 0 else None)
        
        # 显示最终状态
        print(f"\n📋 最终CK状态:")
        final_states = {}
        for ck in available_cks:
            self.db.refresh(ck)  # 刷新数据
            final_states[ck.id] = {
                'bind_count': ck.bind_count,
                'total_limit': ck.total_limit,
                'active': ck.active
            }
            initial = initial_states[ck.id]
            change = ck.bind_count - initial['bind_count']
            print(f"  CK {ck.id}: bind_count={ck.bind_count} (+{change}), total_limit={ck.total_limit}, active={ck.active}")
        
        # 分析结果
        await self._analyze_results(initial_states, final_states)
    
    async def _test_original_service(self, merchant_id: int, test_ck: WalmartCK):
        """测试原有服务"""
        print(f"  使用CK {test_ck.id} 测试原有服务...")

        # 生产环境保护
        print(f"    ⚠️ 注意：这是测试模式，会影响CK的bind_count")

        try:
            # 记录初始计数
            initial_count = test_ck.bind_count

            # 创建原有服务
            ck_service = create_optimized_ck_service(self.db)

            # 模拟绑卡成功（测试用途）
            print(f"    [测试] 调用 record_ck_usage(ck_id={test_ck.id}, success=True)")
            result = await ck_service.record_ck_usage(test_ck.id, True)

            # 刷新数据
            self.db.refresh(test_ck)
            new_count = test_ck.bind_count

            print(f"    结果: {result}")
            print(f"    bind_count: {initial_count} -> {new_count} (变化: +{new_count - initial_count})")

            if new_count == initial_count + 1:
                print(f"    ✅ 原有服务计数更新正确")
            else:
                print(f"    ❌ 原有服务计数更新错误！期望+1，实际+{new_count - initial_count}")

            # 测试后回滚（可选）
            print(f"    💡 提示：如需回滚测试数据，请运行修复脚本")

        except Exception as e:
            print(f"    ❌ 原有服务测试失败: {e}")
    
    async def _test_production_service(self, merchant_id: int, test_ck: WalmartCK):
        """测试生产级服务"""
        if not test_ck:
            print(f"  没有可用的CK进行测试")
            return
            
        print(f"  使用CK {test_ck.id} 测试生产级服务...")
        
        try:
            # 记录初始计数
            initial_count = test_ck.bind_count
            
            # 创建生产级服务
            async with ProductionCKService(self.db) as ck_service:
                # 模拟绑卡成功
                print(f"    模拟绑卡成功，调用 record_ck_usage(ck_id={test_ck.id}, success=True)")
                await ck_service.record_ck_usage(test_ck.id, True)
            
            # 刷新数据
            self.db.refresh(test_ck)
            new_count = test_ck.bind_count
            
            print(f"    bind_count: {initial_count} -> {new_count} (变化: +{new_count - initial_count})")
            
            if new_count == initial_count + 1:
                print(f"    ✅ 生产级服务计数更新正确")
            else:
                print(f"    ❌ 生产级服务计数更新错误！期望+1，实际+{new_count - initial_count}")
                
        except Exception as e:
            print(f"    ❌ 生产级服务测试失败: {e}")
    
    async def _analyze_results(self, initial_states: Dict, final_states: Dict):
        """分析测试结果"""
        print(f"\n📈 测试结果分析:")
        
        total_changes = 0
        for ck_id in initial_states:
            initial = initial_states[ck_id]
            final = final_states[ck_id]
            change = final['bind_count'] - initial['bind_count']
            total_changes += change
        
        print(f"  总计数变化: +{total_changes}")
        
        if total_changes == 2:  # 两个测试各增加1
            print(f"  ✅ 所有测试的CK计数更新都正确")
        elif total_changes == 1:
            print(f"  ⚠️ 只有一个测试的CK计数更新正确")
        elif total_changes == 0:
            print(f"  ❌ 所有测试的CK计数都没有更新！")
        else:
            print(f"  ❓ 计数变化异常: {total_changes}")
    
    async def test_real_binding_scenario(self, merchant_id: int = 2):
        """测试真实绑卡场景"""
        print(f"\n🔄 测试真实绑卡场景")
        print("="*60)
        
        try:
            # 获取一个可用CK
            ck_service = create_optimized_ck_service(self.db)
            ck = await ck_service.get_available_ck(merchant_id)
            
            if not ck:
                print("❌ 没有可用的CK")
                return
            
            print(f"📊 选择的CK: {ck.id}")
            print(f"  初始bind_count: {ck.bind_count}")
            print(f"  total_limit: {ck.total_limit}")
            
            # 模拟绑卡成功流程
            print(f"\n🎯 模拟绑卡成功流程:")
            
            # 1. 选择CK（已完成）
            print(f"  1. ✅ 选择CK {ck.id}")
            
            # 2. 执行绑卡（模拟）
            print(f"  2. 🔄 执行绑卡操作（模拟成功）")
            
            # 3. 记录CK使用
            print(f"  3. 📝 记录CK使用情况...")
            initial_count = ck.bind_count
            
            result = await ck_service.record_ck_usage(ck.id, True)
            
            # 4. 验证结果
            self.db.refresh(ck)
            final_count = ck.bind_count
            
            print(f"  4. ✅ 验证结果:")
            print(f"     record_ck_usage返回: {result}")
            print(f"     bind_count变化: {initial_count} -> {final_count}")
            
            if final_count == initial_count + 1:
                print(f"     ✅ CK计数更新正确！")
            else:
                print(f"     ❌ CK计数更新错误！期望+1，实际+{final_count - initial_count}")
                
                # 详细诊断
                print(f"\n🔍 详细诊断:")
                print(f"     CK ID: {ck.id}")
                print(f"     商户ID: {ck.merchant_id}")
                print(f"     部门ID: {ck.department_id}")
                print(f"     是否激活: {ck.active}")
                print(f"     是否删除: {ck.is_deleted}")
                
        except Exception as e:
            print(f"❌ 真实绑卡场景测试失败: {e}")
            import traceback
            traceback.print_exc()


async def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description="CK绑卡计数测试")
    parser.add_argument("--merchant-id", type=int, default=2, help="商户ID")
    parser.add_argument("--scenario", choices=["count", "real", "both"], default="both", help="测试场景")
    
    args = parser.parse_args()
    
    with CKBindCountTester() as tester:
        if args.scenario in ["count", "both"]:
            await tester.test_ck_bind_count_update(args.merchant_id)
        
        if args.scenario in ["real", "both"]:
            await tester.test_real_binding_scenario(args.merchant_id)
    
    print(f"\n🎉 测试完成！")


if __name__ == "__main__":
    asyncio.run(main())
