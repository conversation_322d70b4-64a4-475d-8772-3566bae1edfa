"""
Telegram Bot 健康检查服务器
"""

import asyncio
import json
import os
from datetime import datetime
from typing import Dict, Any

from aiohttp import web
from app.core.logging import get_logger

logger = get_logger(__name__)

class HealthCheckServer:
    """健康检查服务器"""
    
    def __init__(self, bot_service):
        self.bot_service = bot_service
        self.app = None
        self.runner = None
        self.site = None
        self.port = int(os.getenv('HEALTH_CHECK_PORT', 8080))
    
    async def start(self):
        """启动健康检查服务器"""
        try:
            self.app = web.Application()
            self.app.router.add_get('/health', self.health_check)
            self.app.router.add_get('/ready', self.readiness_check)
            self.app.router.add_get('/status', self.status_check)
            
            self.runner = web.AppRunner(self.app)
            await self.runner.setup()
            
            self.site = web.TCPSite(self.runner, '0.0.0.0', self.port)
            await self.site.start()
            
            logger.info(f"健康检查服务器启动在端口 {self.port}")
            
        except Exception as e:
            logger.error(f"启动健康检查服务器失败: {e}", exc_info=True)
            raise
    
    async def stop(self):
        """停止健康检查服务器"""
        try:
            if self.site:
                await self.site.stop()
            if self.runner:
                await self.runner.cleanup()
            logger.info("健康检查服务器已停止")
        except Exception as e:
            logger.error(f"停止健康检查服务器失败: {e}", exc_info=True)
    
    async def health_check(self, request):
        """基础健康检查"""
        try:
            if self.bot_service and self.bot_service.is_running():
                return web.json_response({
                    'status': 'healthy',
                    'timestamp': datetime.utcnow().isoformat(),
                    'service': 'telegram-bot'
                })
            else:
                return web.json_response({
                    'status': 'unhealthy',
                    'timestamp': datetime.utcnow().isoformat(),
                    'service': 'telegram-bot',
                    'error': 'Bot service not running'
                }, status=503)
        except Exception as e:
            logger.error(f"健康检查失败: {e}", exc_info=True)
            return web.json_response({
                'status': 'error',
                'timestamp': datetime.utcnow().isoformat(),
                'service': 'telegram-bot',
                'error': str(e)
            }, status=500)
    
    async def readiness_check(self, request):
        """就绪检查"""
        try:
            if self.bot_service and self.bot_service.is_running():
                # 检查机器人是否能正常响应
                status = self.bot_service.get_status()
                if status.get('initialized', False):
                    return web.json_response({
                        'status': 'ready',
                        'timestamp': datetime.utcnow().isoformat(),
                        'service': 'telegram-bot'
                    })
            
            return web.json_response({
                'status': 'not_ready',
                'timestamp': datetime.utcnow().isoformat(),
                'service': 'telegram-bot'
            }, status=503)
            
        except Exception as e:
            logger.error(f"就绪检查失败: {e}", exc_info=True)
            return web.json_response({
                'status': 'error',
                'timestamp': datetime.utcnow().isoformat(),
                'service': 'telegram-bot',
                'error': str(e)
            }, status=500)
    
    async def status_check(self, request):
        """详细状态检查"""
        try:
            status_data = {
                'timestamp': datetime.utcnow().isoformat(),
                'service': 'telegram-bot',
                'version': '1.0.0'
            }
            
            if self.bot_service:
                bot_status = self.bot_service.get_status()
                status_data.update(bot_status)
            else:
                status_data['status'] = 'not_initialized'
            
            return web.json_response(status_data)
            
        except Exception as e:
            logger.error(f"状态检查失败: {e}", exc_info=True)
            return web.json_response({
                'status': 'error',
                'timestamp': datetime.utcnow().isoformat(),
                'service': 'telegram-bot',
                'error': str(e)
            }, status=500)
