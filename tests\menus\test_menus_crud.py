"""
菜单管理CRUD操作测试
"""
import pytest
from fastapi.testclient import TestClient
from sqlalchemy.orm import Session

from app.main import app
from app.models.menu import Menu
from app.models.user import User
from test.conftest import get_test_db, create_test_user, create_test_merchant


class TestMenusCRUD:
    """菜单管理CRUD测试类"""

    def setup_method(self):
        """每个测试方法前的设置"""
        self.client = TestClient(app)
        self.db = next(get_test_db())
        
        # 创建测试商户
        self.test_merchant = create_test_merchant(self.db, "test_merchant_menu")
        
        # 创建超级管理员用户
        self.admin_user = create_test_user(
            self.db, 
            username="admin_menu_test",
            role_codes=["super_admin"],
            merchant_id=None,
            is_superuser=True
        )
        
        # 创建普通用户
        self.normal_user = create_test_user(
            self.db,
            username="normal_menu_test", 
            role_codes=["merchant_admin"],
            merchant_id=self.test_merchant.id
        )

    def teardown_method(self):
        """每个测试方法后的清理"""
        self.db.close()

    def test_create_menu_success(self):
        """测试创建菜单成功"""
        # 使用超级管理员登录
        login_data = {"username": "admin_menu_test", "password": "testpass123"}
        login_response = self.client.post("/api/v1/auth/login", data=login_data)
        assert login_response.status_code == 200
        token = login_response.json()["access_token"]
        headers = {"Authorization": f"Bearer {token}"}

        # 创建菜单
        menu_data = {
            "code": "test_menu",
            "name": "测试菜单",
            "title": "测试菜单标题",
            "path": "/test",
            "component": "TestComponent",
            "icon": "test-icon",
            "sort_order": 1,
            "is_enabled": True,
            "is_visible": True
        }
        
        response = self.client.post(
            "/api/v1/menus/",
            json=menu_data,
            headers=headers
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data["code"] == "test_menu"
        assert data["name"] == "测试菜单"

    def test_create_submenu_success(self):
        """测试创建子菜单成功"""
        # 使用超级管理员登录
        login_data = {"username": "admin_menu_test", "password": "testpass123"}
        login_response = self.client.post("/api/v1/auth/login", data=login_data)
        token = login_response.json()["access_token"]
        headers = {"Authorization": f"Bearer {token}"}

        # 先创建父菜单
        parent_menu_data = {
            "code": "parent_menu",
            "name": "父菜单",
            "title": "父菜单标题",
            "path": "/parent",
            "sort_order": 1
        }
        
        parent_response = self.client.post(
            "/api/v1/menus/",
            json=parent_menu_data,
            headers=headers
        )
        parent_id = parent_response.json()["id"]

        # 创建子菜单
        child_menu_data = {
            "code": "child_menu",
            "name": "子菜单",
            "title": "子菜单标题",
            "path": "/parent/child",
            "parent_id": parent_id,
            "sort_order": 1
        }
        
        response = self.client.post(
            "/api/v1/menus/",
            json=child_menu_data,
            headers=headers
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data["parent_id"] == parent_id

    def test_get_menus_list(self):
        """测试获取菜单列表"""
        # 使用超级管理员登录
        login_data = {"username": "admin_menu_test", "password": "testpass123"}
        login_response = self.client.post("/api/v1/auth/login", data=login_data)
        token = login_response.json()["access_token"]
        headers = {"Authorization": f"Bearer {token}"}

        response = self.client.get("/api/v1/menus/", headers=headers)
        assert response.status_code == 200
        data = response.json()
        assert isinstance(data, list)

    def test_get_menus_tree(self):
        """测试获取菜单树结构"""
        # 使用超级管理员登录
        login_data = {"username": "admin_menu_test", "password": "testpass123"}
        login_response = self.client.post("/api/v1/auth/login", data=login_data)
        token = login_response.json()["access_token"]
        headers = {"Authorization": f"Bearer {token}"}

        response = self.client.get("/api/v1/menus/?is_tree=true", headers=headers)
        assert response.status_code == 200
        data = response.json()
        assert isinstance(data, list)

    def test_get_user_menus(self):
        """测试获取用户菜单"""
        # 使用普通用户登录
        login_data = {"username": "normal_menu_test", "password": "testpass123"}
        login_response = self.client.post("/api/v1/auth/login", data=login_data)
        token = login_response.json()["access_token"]
        headers = {"Authorization": f"Bearer {token}"}

        response = self.client.get("/api/v1/menus/user-menus", headers=headers)
        assert response.status_code == 200
        data = response.json()
        assert isinstance(data, list)

    def test_update_menu(self):
        """测试更新菜单"""
        # 使用超级管理员登录
        login_data = {"username": "admin_menu_test", "password": "testpass123"}
        login_response = self.client.post("/api/v1/auth/login", data=login_data)
        token = login_response.json()["access_token"]
        headers = {"Authorization": f"Bearer {token}"}

        # 先创建一个菜单
        menu_data = {
            "code": "update_menu",
            "name": "待更新菜单",
            "title": "待更新菜单标题",
            "path": "/update"
        }
        
        create_response = self.client.post(
            "/api/v1/menus/",
            json=menu_data,
            headers=headers
        )
        menu_id = create_response.json()["id"]

        # 更新菜单
        update_data = {
            "name": "已更新菜单",
            "title": "已更新菜单标题"
        }
        
        response = self.client.put(
            f"/api/v1/menus/{menu_id}",
            json=update_data,
            headers=headers
        )
        assert response.status_code == 200
        data = response.json()
        assert data["name"] == "已更新菜单"

    def test_delete_menu(self):
        """测试删除菜单"""
        # 使用超级管理员登录
        login_data = {"username": "admin_menu_test", "password": "testpass123"}
        login_response = self.client.post("/api/v1/auth/login", data=login_data)
        token = login_response.json()["access_token"]
        headers = {"Authorization": f"Bearer {token}"}

        # 先创建一个菜单
        menu_data = {
            "code": "delete_menu",
            "name": "待删除菜单",
            "title": "待删除菜单标题",
            "path": "/delete"
        }
        
        create_response = self.client.post(
            "/api/v1/menus/",
            json=menu_data,
            headers=headers
        )
        menu_id = create_response.json()["id"]

        # 删除菜单
        response = self.client.delete(f"/api/v1/menus/{menu_id}", headers=headers)
        assert response.status_code == 200

        # 验证菜单已删除
        get_response = self.client.get(f"/api/v1/menus/{menu_id}", headers=headers)
        assert get_response.status_code == 404

    def test_assign_permissions_to_menu(self):
        """测试为菜单分配权限"""
        # 使用超级管理员登录
        login_data = {"username": "admin_menu_test", "password": "testpass123"}
        login_response = self.client.post("/api/v1/auth/login", data=login_data)
        token = login_response.json()["access_token"]
        headers = {"Authorization": f"Bearer {token}"}

        # 先创建一个菜单
        menu_data = {
            "code": "perm_menu",
            "name": "权限菜单",
            "title": "权限菜单标题",
            "path": "/perm"
        }
        
        create_response = self.client.post(
            "/api/v1/menus/",
            json=menu_data,
            headers=headers
        )
        menu_id = create_response.json()["id"]

        # 创建权限
        permission_data = {
            "code": "menu:perm_test",
            "name": "菜单权限测试",
            "resource_type": "menu"
        }
        
        perm_response = self.client.post(
            "/api/v1/permissions/",
            json=permission_data,
            headers=headers
        )
        permission_id = perm_response.json()["id"]

        # 为菜单分配权限
        response = self.client.post(
            f"/api/v1/menus/{menu_id}/permissions",
            json=[permission_id],
            headers=headers
        )
        assert response.status_code == 200

    def test_menu_access_denied_for_normal_user(self):
        """测试普通用户无权限管理菜单"""
        # 使用普通用户登录
        login_data = {"username": "normal_menu_test", "password": "testpass123"}
        login_response = self.client.post("/api/v1/auth/login", data=login_data)
        token = login_response.json()["access_token"]
        headers = {"Authorization": f"Bearer {token}"}

        # 尝试创建菜单
        menu_data = {
            "code": "forbidden_menu",
            "name": "禁止访问菜单",
            "path": "/forbidden"
        }
        
        response = self.client.post(
            "/api/v1/menus/",
            json=menu_data,
            headers=headers
        )
        assert response.status_code == 403
