#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
通知管理CRUD测试
测试通知的创建、读取、更新、删除功能
"""

import sys
import os
import time
import json
import random
import string

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.dirname(__file__))))

from test.conftest import TestBase, get_test_accounts, format_test_result, print_test_summary

class NotificationsCRUDTestSuite(TestBase):
    """通知管理CRUD测试类"""
    
    def __init__(self):
        super().__init__()
        self.results = []
        self.test_accounts = get_test_accounts()
        self.admin_token = None
        self.merchant_token = None
        self.test_notification_ids = []  # 存储测试创建的通知ID，用于清理
        
    def setup_test_environment(self):
        """设置测试环境"""
        print("=== 设置通知管理测试环境 ===")
        
        # 登录管理员账号
        self.admin_token = self.login(
            self.test_accounts["super_admin"]["username"],
            self.test_accounts["super_admin"]["password"]
        )
        
        # 登录商户账号
        self.merchant_token = self.login(
            self.test_accounts["merchant_admin"]["username"],
            self.test_accounts["merchant_admin"]["password"]
        )
        
        if not self.admin_token:
            raise Exception("无法获取管理员token")
        if not self.merchant_token:
            raise Exception("无法获取商户token")
            
        print("✅ 测试环境设置完成")
    
    def generate_test_notification_data(self):
        """生成测试通知数据"""
        random_suffix = ''.join(random.choices(string.ascii_lowercase + string.digits, k=6))
        return {
            "title": f"测试通知_{random_suffix}",
            "content": f"这是一个测试通知内容_{random_suffix}",
            "type": "info",
            "priority": "normal"
        }
    
    def test_get_notifications_list(self):
        """测试获取通知列表"""
        print("\n=== 测试获取通知列表 ===")
        
        # 测试管理员获取通知列表
        status_code, response = self.make_request("GET", "/notifications", self.admin_token)
        
        if status_code == 200:
            self.results.append(format_test_result(
                "管理员获取通知列表",
                True,
                "管理员成功获取通知列表"
            ))
            print("✅ 管理员成功获取通知列表")
            
            # 检查响应格式
            if "data" in response:
                data = response["data"]
                if isinstance(data, dict) and "items" in data:
                    print(f"   📊 找到 {len(data['items'])} 条通知")
                elif isinstance(data, list):
                    print(f"   📊 找到 {len(data)} 条通知")
        else:
            self.results.append(format_test_result(
                "管理员获取通知列表",
                False,
                f"获取通知列表失败，状态码: {status_code}"
            ))
            print(f"❌ 获取通知列表失败，状态码: {status_code}")
        
        # 测试商户获取通知列表
        status_code, response = self.make_request("GET", "/notifications", self.merchant_token)
        
        if status_code == 200:
            self.results.append(format_test_result(
                "商户获取通知列表",
                True,
                "商户成功获取通知列表"
            ))
            print("✅ 商户成功获取通知列表")
        else:
            self.results.append(format_test_result(
                "商户获取通知列表",
                False,
                f"商户获取通知列表失败，状态码: {status_code}"
            ))
            print(f"❌ 商户获取通知列表失败，状态码: {status_code}")
    
    def test_create_notification(self):
        """测试创建通知"""
        print("\n=== 测试创建通知 ===")
        
        # 生成测试数据
        test_data = self.generate_test_notification_data()
        
        # 测试管理员创建通知
        status_code, response = self.make_request("POST", "/notifications", self.admin_token, data=test_data)
        
        if status_code in [200, 201]:
            # 获取创建的通知ID
            notification_id = None
            if "data" in response and isinstance(response["data"], dict):
                notification_id = response["data"].get("id")
            elif "id" in response:
                notification_id = response["id"]
            
            if notification_id:
                self.test_notification_ids.append(notification_id)
                self.results.append(format_test_result(
                    "管理员创建通知",
                    True,
                    f"管理员成功创建通知，ID: {notification_id}"
                ))
                print(f"✅ 管理员成功创建通知，ID: {notification_id}")
            else:
                self.results.append(format_test_result(
                    "管理员创建通知",
                    False,
                    "创建成功但无法获取通知ID"
                ))
                print("⚠️ 创建成功但无法获取通知ID")
        else:
            self.results.append(format_test_result(
                "管理员创建通知",
                False,
                f"创建通知失败，状态码: {status_code}"
            ))
            print(f"❌ 创建通知失败，状态码: {status_code}")
        
        # 测试商户创建通知（权限控制）
        test_data_merchant = self.generate_test_notification_data()
        status_code, response = self.make_request("POST", "/notifications", self.merchant_token, data=test_data_merchant)
        
        if status_code in [200, 201, 403]:  # 200/201表示有权限，403表示无权限
            self.results.append(format_test_result(
                "商户创建通知权限控制",
                True,
                f"商户创建通知权限控制正常，状态码: {status_code}"
            ))
            print(f"✅ 商户创建通知权限控制正常，状态码: {status_code}")
            
            # 如果商户有权限创建，记录ID用于清理
            if status_code in [200, 201]:
                notification_id = None
                if "data" in response and isinstance(response["data"], dict):
                    notification_id = response["data"].get("id")
                elif "id" in response:
                    notification_id = response["id"]
                if notification_id:
                    self.test_notification_ids.append(notification_id)
        else:
            self.results.append(format_test_result(
                "商户创建通知权限控制",
                False,
                f"商户创建通知权限控制异常，状态码: {status_code}"
            ))
            print(f"❌ 商户创建通知权限控制异常，状态码: {status_code}")
    
    def test_read_notification(self):
        """测试标记通知为已读"""
        print("\n=== 测试标记通知为已读 ===")
        
        if not self.test_notification_ids:
            print("⚠️ 没有可用的测试通知ID，跳过已读测试")
            return
        
        notification_id = self.test_notification_ids[0]
        
        # 测试标记通知为已读
        status_code, response = self.make_request("POST", f"/notifications/{notification_id}/read", self.admin_token)
        
        if status_code == 200:
            self.results.append(format_test_result(
                "标记通知为已读",
                True,
                "成功标记通知为已读"
            ))
            print("✅ 成功标记通知为已读")
        else:
            self.results.append(format_test_result(
                "标记通知为已读",
                False,
                f"标记通知为已读失败，状态码: {status_code}"
            ))
            print(f"❌ 标记通知为已读失败，状态码: {status_code}")
    
    def test_get_unread_count(self):
        """测试获取未读通知数量"""
        print("\n=== 测试获取未读通知数量 ===")
        
        # 测试管理员获取未读通知数量
        status_code, response = self.make_request("GET", "/notifications/unread-count", self.admin_token)
        
        if status_code == 200:
            self.results.append(format_test_result(
                "管理员获取未读通知数量",
                True,
                "管理员成功获取未读通知数量"
            ))
            print("✅ 管理员成功获取未读通知数量")
            
            # 检查响应格式
            if "data" in response:
                count = response["data"].get("count", 0) if isinstance(response["data"], dict) else response["data"]
                print(f"   📊 未读通知数量: {count}")
        else:
            self.results.append(format_test_result(
                "管理员获取未读通知数量",
                False,
                f"获取未读通知数量失败，状态码: {status_code}"
            ))
            print(f"❌ 获取未读通知数量失败，状态码: {status_code}")
        
        # 测试商户获取未读通知数量
        status_code, response = self.make_request("GET", "/notifications/unread-count", self.merchant_token)
        
        if status_code == 200:
            self.results.append(format_test_result(
                "商户获取未读通知数量",
                True,
                "商户成功获取未读通知数量"
            ))
            print("✅ 商户成功获取未读通知数量")
        else:
            self.results.append(format_test_result(
                "商户获取未读通知数量",
                False,
                f"商户获取未读通知数量失败，状态码: {status_code}"
            ))
            print(f"❌ 商户获取未读通知数量失败，状态码: {status_code}")
    
    def test_mark_all_read(self):
        """测试标记所有通知为已读"""
        print("\n=== 测试标记所有通知为已读 ===")
        
        # 测试标记所有通知为已读
        status_code, response = self.make_request("POST", "/notifications/read-all", self.admin_token)
        
        if status_code == 200:
            self.results.append(format_test_result(
                "标记所有通知为已读",
                True,
                "成功标记所有通知为已读"
            ))
            print("✅ 成功标记所有通知为已读")
        else:
            self.results.append(format_test_result(
                "标记所有通知为已读",
                False,
                f"标记所有通知为已读失败，状态码: {status_code}"
            ))
            print(f"❌ 标记所有通知为已读失败，状态码: {status_code}")
    
    def test_delete_notification(self):
        """测试删除通知"""
        print("\n=== 测试删除通知 ===")
        
        if not self.test_notification_ids:
            print("⚠️ 没有可用的测试通知ID，跳过删除测试")
            return
        
        notification_id = self.test_notification_ids[0]
        
        # 测试管理员删除通知
        status_code, response = self.make_request("DELETE", f"/notifications/{notification_id}", self.admin_token)
        
        if status_code in [200, 204]:
            self.results.append(format_test_result(
                "管理员删除通知",
                True,
                "管理员成功删除通知"
            ))
            print("✅ 管理员成功删除通知")
            # 从列表中移除已删除的ID
            self.test_notification_ids.remove(notification_id)
        else:
            self.results.append(format_test_result(
                "管理员删除通知",
                False,
                f"删除通知失败，状态码: {status_code}"
            ))
            print(f"❌ 删除通知失败，状态码: {status_code}")
    
    def test_notifications_data_isolation(self):
        """测试通知数据隔离"""
        print("\n=== 测试通知数据隔离 ===")
        
        # 获取管理员看到的通知数量
        status_code, admin_response = self.make_request("GET", "/notifications", self.admin_token)
        admin_count = 0
        
        if status_code == 200 and "data" in admin_response:
            data = admin_response["data"]
            if isinstance(data, dict) and "total" in data:
                admin_count = data["total"]
            elif isinstance(data, list):
                admin_count = len(data)
        
        # 获取商户看到的通知数量
        status_code, merchant_response = self.make_request("GET", "/notifications", self.merchant_token)
        merchant_count = 0
        
        if status_code == 200 and "data" in merchant_response:
            data = merchant_response["data"]
            if isinstance(data, dict) and "total" in data:
                merchant_count = data["total"]
            elif isinstance(data, list):
                merchant_count = len(data)
        
        # 验证数据隔离
        if admin_count >= merchant_count:
            self.results.append(format_test_result(
                "通知数据隔离",
                True,
                f"数据隔离正常，管理员看到 {admin_count} 条，商户看到 {merchant_count} 条"
            ))
            print(f"✅ 数据隔离正常，管理员看到 {admin_count} 条，商户看到 {merchant_count} 条")
        else:
            self.results.append(format_test_result(
                "通知数据隔离",
                False,
                f"数据隔离异常，商户看到的数据比管理员多"
            ))
            print(f"❌ 数据隔离异常，商户看到的数据比管理员多")
    
    def cleanup_test_data(self):
        """清理测试数据"""
        print("\n=== 清理测试数据 ===")
        
        for notification_id in self.test_notification_ids:
            try:
                status_code, _ = self.make_request("DELETE", f"/notifications/{notification_id}", self.admin_token)
                if status_code in [200, 204]:
                    print(f"✅ 成功清理测试通知: {notification_id}")
                else:
                    print(f"⚠️ 清理测试通知失败: {notification_id}")
            except Exception as e:
                print(f"⚠️ 清理测试通知异常: {notification_id}, 错误: {str(e)}")
    
    def run_all_tests(self):
        """运行所有通知管理测试"""
        print("🧪 开始通知管理CRUD测试")
        print("="*60)
        
        start_time = time.time()
        
        try:
            # 设置测试环境
            self.setup_test_environment()
            
            # 运行测试
            self.test_get_notifications_list()
            self.test_create_notification()
            self.test_read_notification()
            self.test_get_unread_count()
            self.test_mark_all_read()
            self.test_delete_notification()
            self.test_notifications_data_isolation()
            
        except Exception as e:
            self.results.append(format_test_result(
                "测试环境设置",
                False,
                f"测试环境设置失败: {str(e)}"
            ))
            print(f"❌ 测试环境设置失败: {str(e)}")
        finally:
            # 清理测试数据
            self.cleanup_test_data()
        
        end_time = time.time()
        duration = end_time - start_time
        
        # 打印测试摘要
        print_test_summary(self.results)
        print(f"测试耗时: {duration:.2f}秒")
        
        return self.results

def main():
    """主函数"""
    test = NotificationsCRUDTestSuite()
    results = test.run_all_tests()
    
    # 判断测试是否通过
    failed_tests = [r for r in results if not r.get("success", False)]
    
    if not failed_tests:
        print("\n🎉 通知管理测试全部通过！")
        return 0
    else:
        print(f"\n⚠️ 发现 {len(failed_tests)} 个问题需要修复")
        for test in failed_tests:
            print(f"  - {test['test_name']}: {test['message']}")
        return 1

if __name__ == "__main__":
    sys.exit(main())
