#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
API安全测试
测试SQL注入、XSS、权限绕过等安全漏洞
"""

import sys
import os
import time

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.dirname(__file__))))

from test.conftest import TestBase, get_test_accounts, format_test_result, print_test_summary

class ApiSecurityTestSuite(TestBase):
    """API安全测试套件"""
    
    def __init__(self):
        super().__init__()
        self.results = []
        self.test_accounts = get_test_accounts()
        self.admin_token = None
        self.merchant_token = None
    
    def setup(self):
        """测试前置设置"""
        print("=== 测试前置设置 ===")
        
        # 管理员登录
        self.admin_token = self.login("admin", "7c222fb2927d828af22f592134e8932480637c0d")
        if not self.admin_token:
            print("❌ 管理员登录失败")
            return False
        
        # 商户管理员登录
        self.merchant_token = self.login("test1", "********")
        if not self.merchant_token:
            print("❌ 商户管理员登录失败")
            return False
        
        print("✅ 测试前置设置完成")
        return True
    
    def test_sql_injection_protection(self):
        """测试SQL注入防护"""
        print("\n=== 测试SQL注入防护 ===")
        
        if not self.admin_token:
            return
        
        # SQL注入测试载荷
        sql_payloads = [
            "' OR '1'='1",
            "'; DROP TABLE users; --",
            "' UNION SELECT * FROM users --",
            "admin'--",
            "' OR 1=1 --",
            "'; INSERT INTO users VALUES ('hacker', 'password'); --"
        ]
        
        # 测试用户列表接口的SQL注入防护
        for payload in sql_payloads:
            params = {"search": payload}
            status_code, response = self.make_request(
                "GET", "/users", self.admin_token, params=params
            )
            
            # 检查是否正常处理（不应该返回500错误或异常数据）
            if status_code == 200:
                self.results.append(format_test_result(
                    f"SQL注入防护_用户列表_{payload[:10]}",
                    True,
                    f"正确处理SQL注入尝试: {payload[:20]}..."
                ))
                print(f"✅ 正确处理SQL注入尝试: {payload[:20]}...")
            elif status_code == 400:
                # 400错误也是可接受的，说明输入验证生效
                self.results.append(format_test_result(
                    f"SQL注入防护_用户列表_{payload[:10]}",
                    True,
                    f"输入验证拒绝SQL注入: {payload[:20]}..."
                ))
                print(f"✅ 输入验证拒绝SQL注入: {payload[:20]}...")
            elif status_code == 500:
                self.results.append(format_test_result(
                    f"SQL注入防护_用户列表_{payload[:10]}",
                    False,
                    f"SQL注入可能导致服务器错误: {payload[:20]}..."
                ))
                print(f"❌ SQL注入可能导致服务器错误: {payload[:20]}...")
            else:
                self.results.append(format_test_result(
                    f"SQL注入防护_用户列表_{payload[:10]}",
                    True,
                    f"返回状态码 {status_code}，未发现明显SQL注入漏洞"
                ))
                print(f"✅ 返回状态码 {status_code}，未发现明显SQL注入漏洞")
    
    def test_xss_protection(self):
        """测试XSS防护"""
        print("\n=== 测试XSS防护 ===")
        
        if not self.admin_token:
            return
        
        # XSS测试载荷
        xss_payloads = [
            "<script>alert('XSS')</script>",
            "<img src=x onerror=alert('XSS')>",
            "javascript:alert('XSS')",
            "<svg onload=alert('XSS')>",
            "';alert('XSS');//"
        ]
        
        # 测试创建用户时的XSS防护
        for payload in xss_payloads:
            test_user = {
                "username": f"test_xss_{int(time.time())}",
                "password": "Test123456!",
                "full_name": payload,  # 在姓名字段中注入XSS
                "email": f"test_{int(time.time())}@example.com"
            }
            
            status_code, response = self.make_request(
                "POST", "/users", self.admin_token, data=test_user
            )
            
            if status_code in [200, 201]:
                # 检查返回的数据是否进行了XSS过滤
                user_data = response.get("data", response)
                returned_name = user_data.get("full_name", "")
                
                if payload in returned_name:
                    self.results.append(format_test_result(
                        f"XSS防护_创建用户_{payload[:10]}",
                        False,
                        f"XSS载荷未被过滤: {payload[:20]}..."
                    ))
                    print(f"❌ XSS载荷未被过滤: {payload[:20]}...")
                else:
                    self.results.append(format_test_result(
                        f"XSS防护_创建用户_{payload[:10]}",
                        True,
                        f"XSS载荷被正确过滤: {payload[:20]}..."
                    ))
                    print(f"✅ XSS载荷被正确过滤: {payload[:20]}...")
            elif status_code == 400:
                self.results.append(format_test_result(
                    f"XSS防护_创建用户_{payload[:10]}",
                    True,
                    f"输入验证拒绝XSS载荷: {payload[:20]}..."
                ))
                print(f"✅ 输入验证拒绝XSS载荷: {payload[:20]}...")
            else:
                self.results.append(format_test_result(
                    f"XSS防护_创建用户_{payload[:10]}",
                    True,
                    f"创建用户失败，状态码: {status_code}，可能有输入验证"
                ))
                print(f"✅ 创建用户失败，状态码: {status_code}，可能有输入验证")
    
    def test_authorization_bypass(self):
        """测试权限绕过"""
        print("\n=== 测试权限绕过 ===")
        
        if not self.merchant_token:
            return
        
        # 测试商户用户尝试访问管理员接口
        admin_endpoints = [
            "/roles",
            "/permissions",
            "/system/params",
            "/merchants",
            "/users"
        ]
        
        for endpoint in admin_endpoints:
            status_code, response = self.make_request("GET", endpoint, self.merchant_token)
            
            if status_code in [401, 403]:
                self.results.append(format_test_result(
                    f"权限绕过防护_{endpoint}",
                    True,
                    f"正确阻止商户用户访问 {endpoint}"
                ))
                print(f"✅ 正确阻止商户用户访问 {endpoint}")
            elif status_code == 200:
                # 检查返回的数据是否经过了权限过滤
                data = response.get("data", {})
                items = data.get("items", []) if isinstance(data, dict) else response.get("items", [])
                
                if len(items) == 0:
                    self.results.append(format_test_result(
                        f"权限绕过防护_{endpoint}",
                        True,
                        f"商户用户访问 {endpoint} 返回空数据（权限过滤生效）"
                    ))
                    print(f"✅ 商户用户访问 {endpoint} 返回空数据（权限过滤生效）")
                else:
                    self.results.append(format_test_result(
                        f"权限绕过防护_{endpoint}",
                        False,
                        f"商户用户不应该能访问 {endpoint} 并获取数据"
                    ))
                    print(f"❌ 商户用户不应该能访问 {endpoint} 并获取数据")
            else:
                self.results.append(format_test_result(
                    f"权限绕过测试_{endpoint}",
                    True,
                    f"访问 {endpoint} 返回状态码 {status_code}，未发现明显权限绕过"
                ))
                print(f"✅ 访问 {endpoint} 返回状态码 {status_code}，未发现明显权限绕过")
    
    def test_parameter_pollution(self):
        """测试参数污染"""
        print("\n=== 测试参数污染 ===")
        
        if not self.admin_token:
            return
        
        # 测试重复参数
        params = {
            "page": ["1", "999"],  # 尝试参数污染
            "limit": ["10", "999999"]
        }
        
        status_code, response = self.make_request(
            "GET", "/users", self.admin_token, params=params
        )
        
        if status_code == 200:
            data = response.get("data", {})
            items = data.get("items", []) if isinstance(data, dict) else response.get("items", [])
            
            # 检查是否返回了异常大量的数据
            if len(items) > 100:
                self.results.append(format_test_result(
                    "参数污染防护",
                    False,
                    f"参数污染可能导致返回过多数据: {len(items)} 条"
                ))
                print(f"❌ 参数污染可能导致返回过多数据: {len(items)} 条")
            else:
                self.results.append(format_test_result(
                    "参数污染防护",
                    True,
                    f"参数污染防护正常，返回 {len(items)} 条数据"
                ))
                print(f"✅ 参数污染防护正常，返回 {len(items)} 条数据")
        else:
            self.results.append(format_test_result(
                "参数污染防护",
                True,
                f"参数污染请求被拒绝，状态码: {status_code}"
            ))
            print(f"✅ 参数污染请求被拒绝，状态码: {status_code}")
    
    def test_input_validation(self):
        """测试输入验证"""
        print("\n=== 测试输入验证 ===")
        
        if not self.admin_token:
            return
        
        # 测试无效的输入数据
        invalid_inputs = [
            {
                "username": "",  # 空用户名
                "password": "123",  # 过短密码
                "email": "invalid-email"  # 无效邮箱
            },
            {
                "username": "a" * 256,  # 过长用户名
                "password": "validpassword",
                "email": "<EMAIL>"
            },
            {
                "username": "test_user",
                "password": "",  # 空密码
                "email": "<EMAIL>"
            }
        ]
        
        for i, invalid_data in enumerate(invalid_inputs):
            status_code, response = self.make_request(
                "POST", "/users", self.admin_token, data=invalid_data
            )
            
            if status_code == 400:
                self.results.append(format_test_result(
                    f"输入验证_{i+1}",
                    True,
                    f"正确拒绝无效输入数据"
                ))
                print(f"✅ 正确拒绝无效输入数据 #{i+1}")
            elif status_code == 422:
                self.results.append(format_test_result(
                    f"输入验证_{i+1}",
                    True,
                    f"输入验证生效，返回422状态码"
                ))
                print(f"✅ 输入验证生效，返回422状态码 #{i+1}")
            elif status_code in [200, 201]:
                self.results.append(format_test_result(
                    f"输入验证_{i+1}",
                    False,
                    f"无效输入数据被错误接受"
                ))
                print(f"❌ 无效输入数据被错误接受 #{i+1}")
            else:
                self.results.append(format_test_result(
                    f"输入验证_{i+1}",
                    True,
                    f"返回状态码 {status_code}，可能有输入验证"
                ))
                print(f"✅ 返回状态码 {status_code}，可能有输入验证 #{i+1}")
    
    def test_token_security(self):
        """测试Token安全性"""
        print("\n=== 测试Token安全性 ===")
        
        # 测试无效Token
        invalid_tokens = [
            "invalid_token",
            "Bearer invalid_token",
            "",
            "null",
            "undefined"
        ]
        
        for token in invalid_tokens:
            status_code, response = self.make_request("GET", "/auth/me", token)
            
            if status_code in [401, 403]:
                self.results.append(format_test_result(
                    f"Token安全性_{token[:10]}",
                    True,
                    f"正确拒绝无效Token: {token[:20]}..."
                ))
                print(f"✅ 正确拒绝无效Token: {token[:20]}...")
            else:
                self.results.append(format_test_result(
                    f"Token安全性_{token[:10]}",
                    False,
                    f"无效Token被错误接受: {token[:20]}..."
                ))
                print(f"❌ 无效Token被错误接受: {token[:20]}...")
    
    def run_all_tests(self):
        """运行所有API安全测试"""
        print("🚀 开始API安全测试")
        print("="*60)
        
        start_time = time.time()
        
        # 前置设置
        if not self.setup():
            print("❌ 测试前置设置失败，跳过测试")
            return []
        
        # 运行所有测试
        self.test_sql_injection_protection()
        self.test_xss_protection()
        self.test_authorization_bypass()
        self.test_parameter_pollution()
        self.test_input_validation()
        self.test_token_security()
        
        end_time = time.time()
        duration = end_time - start_time
        
        # 打印测试摘要
        print_test_summary(self.results)
        print(f"测试耗时: {duration:.2f}秒")
        
        return self.results

def main():
    """主函数"""
    test_suite = ApiSecurityTestSuite()
    results = test_suite.run_all_tests()
    
    # 判断测试是否通过
    failed_tests = [r for r in results if not r.get("success", False)]
    
    if not failed_tests:
        print("\n🎉 所有API安全测试通过！")
        return 0
    else:
        print(f"\n⚠️ 发现 {len(failed_tests)} 个问题需要修复")
        for test in failed_tests:
            print(f"  - {test['test_name']}: {test['message']}")
        return 1

if __name__ == "__main__":
    sys.exit(main())
