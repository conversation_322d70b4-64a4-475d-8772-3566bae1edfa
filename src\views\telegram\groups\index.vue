<template>
  <div class="telegram-groups">
    <!-- 搜索和操作栏 -->
    <el-card class="search-card">
      <el-form :model="searchForm" inline>
        <el-form-item label="群组名称">
          <el-input v-model="searchForm.chat_title" placeholder="请输入群组名称" clearable @keyup.enter="handleSearch" />
        </el-form-item>

        <el-form-item label="绑定状态">
          <el-select style="width: 100px;" v-model="searchForm.bind_status" placeholder="请选择状态" clearable>
            <el-option label="待绑定" value="pending" />
            <el-option label="已绑定" value="active" />
            <el-option label="已暂停" value="suspended" />
            <el-option label="绑定失败" value="failed" />
          </el-select>
        </el-form-item>

        <el-form-item label="商户">
          <el-select v-model="searchForm.merchant_id" placeholder="请选择商户" clearable filterable>
            <el-option v-for="merchant in merchantOptions" :key="merchant.id" :label="merchant.name"
              :value="merchant.id" />
          </el-select>
        </el-form-item>

        <el-form-item>
          <el-button type="primary" @click="handleSearch">
            <el-icon>
              <Search />
            </el-icon>
            搜索
          </el-button>
          <el-button @click="handleReset">
            <el-icon>
              <Refresh />
            </el-icon>
            重置
          </el-button>
          <el-button type="success" @click="showCreateTokenDialog = true">
            <el-icon>
              <Plus />
            </el-icon>
            创建绑定令牌
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 群组列表 -->
    <el-card>
      <template #header>
        <div class="card-header">
          <span>群组列表</span>
          <el-button type="primary" size="small" :loading="telegramStore.groupsLoading" @click="handleRefresh">
            刷新
          </el-button>
        </div>
      </template>

      <el-table :data="telegramStore.groups" v-loading="telegramStore.groupsLoading" stripe border>
        <el-table-column prop="chat_id" label="群组ID" width="120" />

        <el-table-column prop="chat_title" label="群组名称" min-width="200">
          <template #default="{ row }">
            <div class="group-info">
              <div class="group-title">{{ row.chat_title }}</div>
              <div class="group-type">{{ getGroupTypeText(row.chat_type) }}</div>
            </div>
          </template>
        </el-table-column>

        <el-table-column prop="merchant_name" label="绑定商户" width="150" />

        <el-table-column prop="department_name" label="部门" width="120">
          <template #default="{ row }">
            {{ row.department_name || '-' }}
          </template>
        </el-table-column>

        <el-table-column prop="remark" label="备注" width="150">
          <template #default="{ row }">
            {{ row.remark || '-' }}
          </template>
        </el-table-column>

        <el-table-column prop="bind_status" label="绑定状态" width="100">
          <template #default="{ row }">
            <el-tag :type="getStatusTagType(row.bind_status)">
              {{ getStatusText(row.bind_status) }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column prop="bind_time" label="绑定时间" width="160">
          <template #default="{ row }">
            {{ row.bind_time ? formatDateTime(row.bind_time) : '-' }}
          </template>
        </el-table-column>

        <el-table-column prop="last_active_time" label="最后活跃" width="160">
          <template #default="{ row }">
            {{ row.last_active_time ? formatDateTime(row.last_active_time) : '-' }}
          </template>
        </el-table-column>

        <el-table-column label="操作" width="320" fixed="right">
          <template #default="{ row }">
            <el-button type="primary" size="small" @click="handleViewDetail(row)">
              详情
            </el-button>
            <el-button v-if="['pending', 'failed'].includes(row.bind_status)" type="success" size="small"
              @click="handleCopyBindCommand(row)">
              复制绑定命令
            </el-button>
            <el-button v-if="row.bind_status === 'active'" type="warning" size="small" @click="handleUnbind(row)">
              解绑
            </el-button>
            <el-button v-if="['failed', 'suspended'].includes(row.bind_status)" type="success" size="small"
              @click="handleResetToken(row)">
              重置令牌
            </el-button>
            <el-button type="info" size="small" @click="handleViewStats(row)">
              统计
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-wrapper">
        <el-pagination v-model:current-page="pagination.page" v-model:page-size="pagination.pageSize"
          :total="telegramStore.groupsTotal" :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper" @size-change="handleSizeChange"
          @current-change="handleCurrentChange" />
      </div>
    </el-card>

    <!-- 创建绑定令牌对话框 -->
    <el-dialog v-model="showCreateTokenDialog" title="创建群组绑定令牌" width="600px">
      <el-form ref="tokenFormRef" :model="tokenForm" :rules="tokenRules" label-width="120px">
        <el-form-item label="选择商户" prop="merchant_id">
          <el-select v-model="tokenForm.merchant_id" placeholder="请选择商户" filterable style="width: 100%"
            @change="handleMerchantChange">
            <el-option v-for="merchant in merchantOptions" :key="merchant.id" :label="merchant.name"
              :value="merchant.id" />
          </el-select>
        </el-form-item>

        <el-form-item label="选择部门">
          <el-select v-model="tokenForm.department_id" placeholder="请选择部门（可选）" clearable filterable style="width: 100%">
            <el-option v-for="department in departmentOptions" :key="department.id" :label="department.name"
              :value="department.id" />
          </el-select>
        </el-form-item>

        <el-form-item label="备注">
          <el-input v-model="tokenForm.remark" type="textarea" :rows="3" placeholder="请输入备注信息（可选）" />
        </el-form-item>
      </el-form>

      <template #footer>
        <el-button @click="showCreateTokenDialog = false">取消</el-button>
        <el-button type="primary" :loading="createTokenLoading" @click="handleCreateToken">
          创建令牌
        </el-button>
      </template>
    </el-dialog>

    <!-- 绑定令牌结果对话框 -->
    <el-dialog v-model="showTokenResultDialog" title="绑定令牌创建成功" width="600px">
      <div v-if="createdToken" class="token-result">
        <el-alert title="请将以下绑定令牌发送给群组管理员" type="success" show-icon :closable="false" />

        <div class="token-info">
          <h4>绑定令牌：</h4>
          <el-input :value="createdToken.bind_token" readonly class="token-input">
            <template #append>
              <el-button @click="copyToken">复制</el-button>
            </template>
          </el-input>

          <h4>使用说明：</h4>
          <ol>
            <li>将机器人 @{{ botUsername }} 添加到群组</li>
            <li>在群组中发送命令：<code>/bind {{ createdToken.bind_token }}</code></li>
            <li>等待机器人确认绑定成功</li>
          </ol>

          <div class="token-expire">
            <el-icon>
              <Warning />
            </el-icon>
            令牌将在 {{ tokenExpireHours }} 小时后过期
          </div>
        </div>
      </div>

      <template #footer>
        <el-button type="primary" @click="showTokenResultDialog = false">
          我知道了
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Search, Refresh, Plus, Warning } from '@element-plus/icons-vue'
import { useTelegramStore } from '@/store/modules/telegram'
import { useMerchantStore } from '@/store/modules/merchant'
import { departmentApi } from '@/api'
import { copyToClipboard } from '@/utils/clipboard'

const router = useRouter()
const telegramStore = useTelegramStore()
const merchantStore = useMerchantStore()

// 搜索表单
const searchForm = reactive({
  chat_title: '',
  bind_status: '',
  merchant_id: ''
})

// 分页
const pagination = reactive({
  page: 1,
  pageSize: 20
})

// 对话框状态
const showCreateTokenDialog = ref(false)
const showTokenResultDialog = ref(false)
const createTokenLoading = ref(false)

// 创建令牌表单
const tokenFormRef = ref()
const tokenForm = reactive({
  merchant_id: '',
  department_id: '',
  remark: ''
})

const tokenRules = {
  merchant_id: [
    { required: true, message: '请选择商户', trigger: 'change' }
  ]
}

// 创建的令牌信息
const createdToken = ref(null)

// 部门列表数据
const departmentList = ref([])

// 计算属性
const merchantOptions = computed(() => merchantStore.merchants || [])
const departmentOptions = computed(() => departmentList.value)

const botUsername = computed(() => telegramStore.botInfo.username || 'your_bot')
const tokenExpireHours = computed(() => telegramStore.globalConfig.bind_token_expire_hours || 24)

// 获取群组类型文本
const getGroupTypeText = (type) => {
  const typeMap = {
    'group': '群组',
    'supergroup': '超级群组',
    'channel': '频道'
  }
  return typeMap[type] || type
}

// 获取状态标签类型
const getStatusTagType = (status) => {
  const typeMap = {
    'pending': 'warning',
    'active': 'success',
    'suspended': 'info',
    'failed': 'danger'
  }
  return typeMap[status] || 'info'
}

// 获取状态文本
const getStatusText = (status) => {
  const textMap = {
    'pending': '待绑定',
    'active': '已绑定',
    'suspended': '已暂停',
    'failed': '绑定失败'
  }
  return textMap[status] || status
}

// 格式化日期时间
const formatDateTime = (dateTime) => {
  if (!dateTime) return '-'
  return new Date(dateTime).toLocaleString('zh-CN')
}

// 搜索
const handleSearch = () => {
  pagination.page = 1
  loadGroups()
}

// 重置搜索
const handleReset = () => {
  Object.assign(searchForm, {
    chat_title: '',
    bind_status: '',
    merchant_id: ''
  })
  pagination.page = 1
  loadGroups()
}

// 刷新
const handleRefresh = () => {
  loadGroups()
}

// 分页变化
const handleSizeChange = (size) => {
  pagination.pageSize = size
  pagination.page = 1
  loadGroups()
}

const handleCurrentChange = (page) => {
  pagination.page = page
  loadGroups()
}

// 查看详情
const handleViewDetail = (row) => {
  router.push(`/telegram/groups/detail/${row.id}`)
}

// 解绑群组
const handleUnbind = async (row) => {
  try {
    await ElMessageBox.confirm(
      `确定要解绑群组 "${row.chat_title}" 吗？解绑后该群组将无法使用机器人功能。`,
      '确认解绑',
      { type: 'warning' }
    )

    await telegramStore.unbindGroup(row.id)
    ElMessage.success('群组解绑成功')
    loadGroups()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('解绑失败：' + error.message)
    }
  }
}

// 查看统计
const handleViewStats = (row) => {
  router.push(`/telegram/statistics?group_id=${row.id}`)
}

// 复制绑定命令
const handleCopyBindCommand = async (row) => {
  if (!row.bind_token) {
    ElMessage.warning('该群组没有可用的绑定令牌')
    return
  }

  const bindCommand = `/bind ${row.bind_token}`

  try {
    await copyToClipboard(bindCommand, {
      successMessage: '绑定命令已复制到剪贴板',
      errorMessage: '复制失败，请手动复制'
    })
  } catch (error) {
    console.error('复制失败:', error)
  }
}

// 重置令牌
const handleResetToken = async (row) => {
  try {
    await ElMessageBox.confirm(
      `确定要重置群组 "${row.chat_title}" 的绑定令牌吗？重置后可以重新使用原令牌进行绑定。`,
      '确认重置令牌',
      { type: 'warning' }
    )

    await telegramApi.resetBindToken(row.id)
    ElMessage.success('令牌重置成功')

    // 刷新列表
    loadGroups()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('重置令牌失败：' + error.message)
    }
  }
}

// 创建绑定令牌
const handleCreateToken = async () => {
  try {
    await tokenFormRef.value.validate()

    createTokenLoading.value = true
    const response = await telegramStore.createGroupBindToken(tokenForm)

    createdToken.value = response.data || response
    showCreateTokenDialog.value = false
    showTokenResultDialog.value = true

    // 重置表单
    Object.assign(tokenForm, {
      merchant_id: '',
      department_id: '',
      remark: ''
    })

    ElMessage.success('绑定令牌创建成功')
  } catch (error) {
    if (error.fields) {
      ElMessage.error('请检查表单输入')
      return
    }
    ElMessage.error('创建失败：' + error.message)
  } finally {
    createTokenLoading.value = false
  }
}

// 复制令牌
const copyToken = async () => {
  try {
    await navigator.clipboard.writeText(createdToken.value.bind_token)
    ElMessage.success('令牌已复制到剪贴板')
  } catch (error) {
    ElMessage.error('复制失败，请手动复制')
  }
}

// 加载部门列表
const loadDepartments = async (merchantId) => {
  if (!merchantId) {
    departmentList.value = []
    return
  }

  try {
    // 分页获取所有部门
    let allDepartments = []
    let page = 1
    let hasMore = true

    while (hasMore) {
      const response = await departmentApi.getList({
        merchant_id: merchantId,
        page: page,
        page_size: 100 // 每页最大100条
      })

      const items = response.items || []
      allDepartments = allDepartments.concat(items)

      // 检查是否还有更多数据
      hasMore = items.length === 100 && response.total > allDepartments.length
      page++
    }

    departmentList.value = allDepartments
  } catch (error) {
    console.error('加载部门列表失败:', error)
    ElMessage.error('加载部门列表失败：' + error.message)
    departmentList.value = []
  }
}

// 处理商户变更
const handleMerchantChange = (merchantId) => {
  // 清空部门选择
  tokenForm.department_id = ''
  // 加载新商户的部门列表
  loadDepartments(merchantId)
}

// 加载群组列表
const loadGroups = async () => {
  try {
    const params = {
      page: pagination.page,
      page_size: pagination.pageSize,
      ...searchForm
    }

    // 过滤空值
    Object.keys(params).forEach(key => {
      if (params[key] === '' || params[key] === null || params[key] === undefined) {
        delete params[key]
      }
    })

    await telegramStore.fetchGroups(params)
  } catch (error) {
    ElMessage.error('加载群组列表失败：' + error.message)
  }
}

// 初始化
onMounted(async () => {
  try {
    await Promise.all([
      loadGroups(),
      merchantStore.fetchMerchants({ page: 1, page_size: 100 }),
      telegramStore.fetchBotInfo()
    ])
  } catch (error) {
    console.error('初始化失败:', error)
  }
})
</script>

<style scoped>
.telegram-groups {
  padding: 20px;
}

.search-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.group-info {
  display: flex;
  flex-direction: column;
}

.group-title {
  font-weight: bold;
  margin-bottom: 4px;
}

.group-type {
  font-size: 12px;
  color: #999;
}

.pagination-wrapper {
  margin-top: 20px;
  text-align: right;
}

.token-result {
  padding: 20px 0;
}

.token-info {
  margin-top: 20px;
}

.token-info h4 {
  margin: 16px 0 8px 0;
  color: #333;
}

.token-input {
  margin-bottom: 16px;
}

.token-info ol {
  margin: 8px 0;
  padding-left: 20px;
}

.token-info li {
  margin-bottom: 8px;
  line-height: 1.5;
}

.token-info code {
  background: #f5f5f5;
  padding: 2px 6px;
  border-radius: 3px;
  font-family: monospace;
}

.token-expire {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #E6A23C;
  background: #FDF6EC;
  padding: 12px;
  border-radius: 4px;
  margin-top: 16px;
}
</style>
