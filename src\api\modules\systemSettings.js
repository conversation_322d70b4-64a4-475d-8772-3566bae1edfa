import { http } from '@/api/request'
import { API_URLS, replaceUrlParams } from './config'

const { SYSTEM_SETTINGS } = API_URLS

/**
 * 系统设置相关 API
 */
export const systemSettingsApi = {
    // 获取系统设置列表
    getSystemSettings(params = {}) {
        return http.get(SYSTEM_SETTINGS.LIST, { params }).then(res => res.data)
    },

    // 根据键名获取系统设置
    getSystemSetting(key) {
        const url = replaceUrlParams(SYSTEM_SETTINGS.DETAIL, { key })
        return http.get(url).then(res => res.data)
    },

    // 创建系统设置
    createSystemSetting(data) {
        return http.post(SYSTEM_SETTINGS.CREATE, data).then(res => res.data)
    },

    // 更新系统设置
    updateSystemSetting(key, data) {
        const url = replaceUrlParams(SYSTEM_SETTINGS.UPDATE, { key })
        return http.put(url, data).then(res => res.data)
    },

    // 删除系统设置
    deleteSystemSetting(key) {
        const url = replaceUrlParams(SYSTEM_SETTINGS.DELETE, { key })
        return http.delete(url).then(res => res.data)
    },

    // 获取CK过期配置
    getCkExpireConfig() {
        return http.get(SYSTEM_SETTINGS.CK_EXPIRE_CONFIG).then(res => res.data)
    },

    // 更新CK过期配置
    updateCkExpireConfig(config) {
        return http.put(SYSTEM_SETTINGS.CK_EXPIRE_CONFIG, config, {
            headers: {
                'Content-Type': 'application/json'
            }
        }).then(res => res.data)
    }
}
