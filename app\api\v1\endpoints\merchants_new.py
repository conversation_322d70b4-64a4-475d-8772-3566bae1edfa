"""
商户管理API端点 - 基于MerchantService重构，完全动态权限
"""

from typing import Any, Optional, List, Dict
from fastapi import APIRouter, Depends, HTTPException, Query, Path, status, Body
from sqlalchemy.orm import Session

from app.api import deps
from app.models.user import User
from app.schemas.merchant import (
    MerchantCreate,
    MerchantUpdate,
    PasswordVerify,
)
from app import schemas
from app.services.merchant_service import MerchantService
from app.core.exceptions import BusinessException, ErrorCode
from app.core.logging import get_logger
from app.core.dynamic_permissions import PermissionHelper

logger = get_logger(__name__)
router = APIRouter()


def merchant_to_dict(merchant) -> Dict[str, Any]:
    """将商户对象转换为字典格式"""
    return {
        "id": merchant.id,
        "name": merchant.name,
        "code": merchant.code,
        "contact_name": getattr(merchant, 'contact_name', None),
        "contact_phone": getattr(merchant, 'contact_phone', None),
        "contact_email": getattr(merchant, 'contact_email', None),
        "api_key": getattr(merchant, 'api_key', None),
        "api_secret": getattr(merchant, 'api_secret', None),
        "status": getattr(merchant, 'status', True),
        "callback_url": getattr(merchant, 'callback_url', None),
        "allowed_ips": getattr(merchant, 'allowed_ips', None),
        "daily_limit": getattr(merchant, 'daily_limit', 0),
        "hourly_limit": getattr(merchant, 'hourly_limit', 0),
        "concurrency_limit": getattr(merchant, 'concurrency_limit', 100),
        "priority": getattr(merchant, 'priority', 0),
        "request_timeout": getattr(merchant, 'request_timeout', 30),
        "retry_count": getattr(merchant, 'retry_count', 3),
        "custom_config": getattr(merchant, 'custom_config', None),
        "created_by": getattr(merchant, 'created_by', None),
        "api_key_updated_at": getattr(merchant, 'api_key_updated_at', None),
        "api_secret_updated_at": getattr(merchant, 'api_secret_updated_at', None),
        "remark": getattr(merchant, 'remark', None),
        "created_at": merchant.created_at,
        "updated_at": merchant.updated_at,
    }


@router.get("/current/api-credentials", response_model=Dict[str, Any])
async def get_current_merchant_api_credentials(
    *,
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_active_user),
):
    """
    获取当前用户所属商户的API凭据

    权限要求:
    - 具体权限检查：api:merchants:api-credentials
    - 数据隔离：只能查看自己所属商户的API凭据
    """
    try:
        # 检查权限 - 使用具体的API凭证权限，遵循最小权限原则
        from app.services.permission_service import PermissionService
        permission_service = PermissionService(db)

        if not permission_service.check_user_permission(current_user, "api:merchants:api-credentials"):
            raise BusinessException(
                message="没有获取商户API凭证的权限",
                code=ErrorCode.FORBIDDEN
            )

        # 初始化商户API服务
        from app.services.merchant_api_service import MerchantAPIService
        merchant_api_service = MerchantAPIService(db)

        # 获取当前用户所属商户的API凭据
        result = merchant_api_service.get_current_merchant_api_credentials(current_user)

        return result

    except BusinessException:
        raise
    except Exception as e:
        logger.error(f"获取当前商户API凭据失败: {e}")
        raise BusinessException(
            message=f"获取当前商户API凭据失败: {str(e)}",
            code=ErrorCode.INTERNAL_ERROR
        )


@router.get("", response_model=Dict[str, Any])
async def read_merchants(
    db: Session = Depends(deps.get_db),
    page: int = Query(1, ge=1, description="页码，从1开始"),
    page_size: int = Query(10, ge=1, le=100, description="每页记录数，最大100"),
    current_user: User = Depends(deps.get_current_active_user),
    search: Optional[str] = Query(None, description="搜索关键词"),
    is_active: Optional[bool] = Query(None, description="商户状态筛选"),
):
    """
    获取商户列表

    权限要求:
    - 动态权限检查：merchant:view
    - 数据隔离：非超级管理员只能查看自己的商户
    """
    try:
        # 检查权限 - 动态权限检查
        PermissionHelper.check_permission(db, current_user, "merchant", "view")

        # 初始化商户服务
        merchant_service = MerchantService(db)

        # 计算分页参数
        skip = (page - 1) * page_size

        # 使用CRUD层的正确方法获取商户列表和总数
        from app.crud.merchant import merchant as merchant_crud

        # 应用数据隔离：非超级管理员只能查看自己的商户
        if not current_user.is_superuser:
            # 非超级管理员只能查看自己的商户
            if current_user.merchant_id:
                merchant = merchant_crud.get(db, current_user.merchant_id)
                merchants = [merchant] if merchant else []
                total = 1 if merchant else 0
            else:
                merchants = []
                total = 0
        else:
            # 超级管理员可以查看所有商户
            result = merchant_crud.get_multi_by_filter(
                db, skip=skip, limit=page_size, search=search, status=is_active
            )
            merchants = result["items"]
            total = result["total"]

        # 转换为响应格式
        merchant_list = [merchant_to_dict(merchant) for merchant in merchants]

        # 直接返回数据，让中间件处理统一格式
        return {
            "items": merchant_list,
            "total": total,
            "page": page,
            "page_size": page_size,
            "pages": (total + page_size - 1) // page_size
        }

    except BusinessException:
        raise
    except Exception as e:
        logger.error(f"获取商户列表失败: {e}")
        raise BusinessException(
            message=f"获取商户列表失败: {str(e)}",
            code=ErrorCode.INTERNAL_ERROR
        )


@router.post("", response_model=Dict[str, Any])
async def create_merchant(
    *,
    db: Session = Depends(deps.get_db),
    merchant_in: MerchantCreate,
    current_user: User = Depends(deps.get_current_active_user),
):
    """
    创建新商户

    权限要求:
    - 动态权限检查：merchant:create
    - 只有超级管理员可以创建商户
    """
    try:
        # 检查权限 - 动态权限检查
        PermissionHelper.check_permission(db, current_user, "merchant", "create")

        # 初始化商户服务并创建商户
        merchant_service = MerchantService(db)
        new_merchant = merchant_service.create_merchant(merchant_in, current_user)

        # 直接返回数据，让中间件处理统一格式
        return merchant_to_dict(new_merchant)

    except BusinessException:
        raise
    except ValueError as e:
        raise BusinessException(
            message=str(e),
            code=ErrorCode.DATA_VALIDATION_ERROR
        )
    except Exception as e:
        logger.error(f"创建商户失败: {e}")
        raise BusinessException(
            message=f"创建商户失败: {str(e)}",
            code=ErrorCode.INTERNAL_ERROR
        )


@router.get("/{merchant_id}", response_model=Dict[str, Any])
async def read_merchant(
    *,
    db: Session = Depends(deps.get_db),
    merchant_id: int = Path(..., gt=0),
    current_user: User = Depends(deps.get_current_active_user),
):
    """
    获取商户详情

    权限要求:
    - 动态权限检查：merchant:view
    - 数据隔离：只能查看有权限访问的商户
    """
    try:
        # 检查权限 - 动态权限检查
        PermissionHelper.check_permission(db, current_user, "merchant", "view")

        # 初始化商户服务
        merchant_service = MerchantService(db)

        # 获取商户（应用数据隔离）
        merchant = merchant_service.get_with_isolation(merchant_id, current_user)
        if not merchant:
            raise BusinessException(
                message="商户不存在或无权限访问",
                code=ErrorCode.NOT_FOUND
            )

        # 直接返回数据，让中间件处理统一格式
        return merchant_to_dict(merchant)

    except BusinessException:
        raise
    except Exception as e:
        logger.error(f"获取商户详情失败: {e}")
        raise BusinessException(
            message=f"获取商户详情失败: {str(e)}",
            code=ErrorCode.INTERNAL_ERROR
        )


@router.put("/{merchant_id}", response_model=Dict[str, Any])
async def update_merchant(
    *,
    db: Session = Depends(deps.get_db),
    merchant_id: int = Path(..., gt=0),
    merchant_in: MerchantUpdate,
    current_user: User = Depends(deps.get_current_active_user),
):
    """
    更新商户信息

    权限要求:
    - 动态权限检查：merchant:edit
    - 数据隔离：只能编辑有权限访问的商户
    """
    try:
        # 检查权限 - 动态权限检查
        PermissionHelper.check_permission(db, current_user, "merchant", "edit")

        # 初始化商户服务并更新商户
        merchant_service = MerchantService(db)
        updated_merchant = merchant_service.update_merchant(merchant_id, merchant_in, current_user)

        if not updated_merchant:
            raise BusinessException(
                message="商户不存在或无权限访问",
                code=ErrorCode.NOT_FOUND
            )

        # 直接返回数据，让中间件处理统一格式
        return merchant_to_dict(updated_merchant)

    except BusinessException:
        raise
    except ValueError as e:
        raise BusinessException(
            message=str(e),
            code=ErrorCode.DATA_VALIDATION_ERROR
        )
    except Exception as e:
        logger.error(f"更新商户失败: {e}")
        raise BusinessException(
            message=f"更新商户失败: {str(e)}",
            code=ErrorCode.INTERNAL_ERROR
        )


@router.patch("/{merchant_id}/status", response_model=Dict[str, Any])
async def update_merchant_status(
    *,
    db: Session = Depends(deps.get_db),
    merchant_id: int = Path(..., gt=0),
    status_data: Dict[str, bool] = Body(...),
    current_user: User = Depends(deps.get_current_active_user),
):
    """
    更新商户状态（启用/禁用）

    权限要求:
    - 动态权限检查：merchant:edit
    - 数据隔离：只能操作有权限访问的商户
    """
    try:
        # 检查权限 - 动态权限检查
        PermissionHelper.check_permission(db, current_user, "merchant", "edit")

        # 验证请求数据
        if "status" not in status_data:
            raise BusinessException(
                message="缺少status参数",
                code=ErrorCode.DATA_VALIDATION_ERROR
            )

        new_status = status_data["status"]
        if not isinstance(new_status, bool):
            raise BusinessException(
                message="status参数必须为布尔值",
                code=ErrorCode.DATA_VALIDATION_ERROR
            )

        # 初始化商户服务
        merchant_service = MerchantService(db)

        # 获取商户（应用数据隔离）
        merchant = merchant_service.get_with_isolation(merchant_id, current_user)
        if not merchant:
            raise BusinessException(
                message="商户不存在或无权限访问",
                code=ErrorCode.NOT_FOUND
            )

        # 更新商户状态
        merchant.status = new_status
        db.commit()
        db.refresh(merchant)

        # 记录操作日志
        action = "启用" if new_status else "禁用"
        logger.info(f"用户 {current_user.username} {action}了商户 {merchant.name} (ID: {merchant_id})")

        # 直接返回数据，让中间件处理统一格式
        return merchant_to_dict(merchant)

    except BusinessException:
        raise
    except Exception as e:
        logger.error(f"更新商户状态失败: {e}")
        raise BusinessException(
            message=f"更新商户状态失败: {str(e)}",
            code=ErrorCode.INTERNAL_ERROR
        )


@router.delete("/{merchant_id}", response_model=Dict[str, Any])
async def delete_merchant(
    *,
    db: Session = Depends(deps.get_db),
    merchant_id: int = Path(..., gt=0),
    current_user: User = Depends(deps.get_current_active_user),
):
    """
    删除商户

    权限要求:
    - 动态权限检查：merchant:delete
    - 只有超级管理员可以删除商户
    """
    try:
        # 检查权限 - 动态权限检查
        PermissionHelper.check_permission(db, current_user, "merchant", "delete")

        # 初始化商户服务并删除商户
        merchant_service = MerchantService(db)
        success = merchant_service.delete_merchant(merchant_id, current_user)

        if not success:
            raise BusinessException(
                message="商户不存在或无权限访问",
                code=ErrorCode.NOT_FOUND
            )

        return {
            "success": True,
            "data": {"merchant_id": merchant_id},
            "message": "商户删除成功"
        }

    except BusinessException:
        raise
    except ValueError as e:
        raise BusinessException(
            message=str(e),
            code=ErrorCode.DATA_VALIDATION_ERROR
        )
    except Exception as e:
        logger.error(f"删除商户失败: {e}")
        raise BusinessException(
            message=f"删除商户失败: {str(e)}",
            code=ErrorCode.INTERNAL_ERROR
        )


@router.get("/{merchant_id}/statistics", response_model=Dict[str, Any])
async def get_merchant_statistics(
    *,
    db: Session = Depends(deps.get_db),
    merchant_id: int = Path(..., gt=0),
    current_user: User = Depends(deps.get_current_active_user),
):
    """
    获取商户统计信息

    权限要求:
    - 动态权限检查：merchant:view
    - 数据隔离：只能查看有权限访问的商户统计
    """
    try:
        # 检查权限 - 动态权限检查
        PermissionHelper.check_permission(db, current_user, "merchant", "view")

        # 初始化商户服务
        merchant_service = MerchantService(db)

        # 获取商户统计（应用数据隔离）
        statistics = merchant_service.get_merchant_statistics(merchant_id, current_user)

        if not statistics:
            raise BusinessException(
                message="商户不存在或无权限访问",
                code=ErrorCode.NOT_FOUND
            )

        # 直接返回数据，让中间件处理统一格式
        return statistics

    except BusinessException:
        raise
    except Exception as e:
        logger.error(f"获取商户统计失败: {e}")
        raise BusinessException(
            message=f"获取商户统计失败: {str(e)}",
            code=ErrorCode.INTERNAL_ERROR
        )


@router.post("/{merchant_id}/reset-api-key", response_model=Dict[str, Any])
async def reset_merchant_api_key(
    *,
    db: Session = Depends(deps.get_db),
    merchant_id: int = Path(..., gt=0),
    current_user: User = Depends(deps.get_current_active_user),
):
    """
    重置商户API密钥

    权限要求:
    - 动态权限检查：merchant:edit
    - 数据隔离：只能操作有权限访问的商户
    """
    try:
        # 检查权限 - 动态权限检查
        PermissionHelper.check_permission(db, current_user, "merchant", "edit")

        # 初始化商户服务
        merchant_service = MerchantService(db)

        # 重新生成API密钥
        updated_merchant = merchant_service.regenerate_api_key(merchant_id, current_user)

        if not updated_merchant:
            raise BusinessException(
                message="商户不存在或无权限访问",
                code=ErrorCode.NOT_FOUND
            )

        # 直接返回数据，让中间件处理统一格式
        return {
            "merchant_id": merchant_id,
            "api_key": updated_merchant.api_key,
            "api_secret": updated_merchant.api_secret
        }

    except BusinessException:
        raise
    except Exception as e:
        logger.error(f"重新生成API密钥失败: {e}")
        raise BusinessException(
            message=f"重新生成API密钥失败: {str(e)}",
            code=ErrorCode.INTERNAL_ERROR
        )


@router.get("/{merchant_id}/api-credentials", response_model=Dict[str, Any])
async def get_merchant_api_credentials(
    *,
    db: Session = Depends(deps.get_db),
    merchant_id: int = Path(..., gt=0),
    current_user: User = Depends(deps.get_current_active_user),
):
    """
    获取商户API凭据

    权限要求:
    - 动态权限检查：merchant:view
    - 数据隔离：只能查看有权限访问的商户的API凭据
    """
    try:
        # 检查权限 - 动态权限检查
        PermissionHelper.check_permission(db, current_user, "merchant", "view")

        # 初始化商户服务
        merchant_service = MerchantService(db)

        # 获取商户（应用数据隔离）
        merchant = merchant_service.get_with_isolation(merchant_id, current_user)
        if not merchant:
            raise BusinessException(
                message="商户不存在或无权限访问",
                code=ErrorCode.NOT_FOUND
            )

        # 返回API凭据信息
        return {
            "api_key": merchant.api_key,
            "api_secret": merchant.api_secret,
            "merchant_code": merchant.code,
            "merchant_name": merchant.name,
            "merchant_id": merchant.id,
            "api_key_updated_at": merchant.api_key_updated_at,
        }

    except BusinessException:
        raise
    except Exception as e:
        logger.error(f"获取商户API凭据失败: {e}")
        raise BusinessException(
            message=f"获取商户API凭据失败: {str(e)}",
            code=ErrorCode.INTERNAL_ERROR
        )


@router.get("/{merchant_id}/users", response_model=Dict[str, Any])
async def get_merchant_users(
    *,
    db: Session = Depends(deps.get_db),
    merchant_id: int = Path(..., gt=0),
    current_user: User = Depends(deps.get_current_active_user),
    page: int = Query(1, ge=1, description="页码，从1开始"),
    page_size: int = Query(10, ge=1, le=100, description="每页记录数，最大100"),
):
    """
    获取商户用户列表

    权限要求:
    - 动态权限检查：merchant:view 和 user:view
    - 数据隔离：只能查看有权限访问的商户用户
    """
    try:
        # 检查权限 - 动态权限检查
        PermissionHelper.check_permission(db, current_user, "merchant", "view")
        PermissionHelper.check_permission(db, current_user, "user", "view")

        # 初始化商户服务
        merchant_service = MerchantService(db)

        # 计算分页参数
        skip = (page - 1) * page_size

        # 获取商户用户列表
        users = merchant_service.get_merchant_users(
            merchant_id,
            current_user,
            skip=skip,
            limit=page_size
        )

        if users is None:
            raise BusinessException(
                message="商户不存在或无权限访问",
                code=ErrorCode.NOT_FOUND
            )

        # 获取总数
        total_users = merchant_service.get_merchant_users(
            merchant_id,
            current_user,
            skip=0,
            limit=1000
        )
        total = len(total_users) if total_users else 0

        # 转换为响应格式
        from app.api.v1.endpoints.users_new import user_to_dict
        user_list = [user_to_dict(user) for user in users]

        # 直接返回数据，让中间件处理统一格式
        return {
            "items": user_list,
            "total": total,
            "page": page,
            "page_size": page_size,
            "pages": (total + page_size - 1) // page_size
        }

    except BusinessException:
        raise
    except Exception as e:
        logger.error(f"获取商户用户列表失败: {e}")
        raise BusinessException(
            message=f"获取商户用户列表失败: {str(e)}",
            code=ErrorCode.INTERNAL_ERROR
        )


@router.get("/{merchant_id}/api-usage", response_model=Dict[str, Any])
async def get_merchant_api_usage(
    *,
    db: Session = Depends(deps.get_db),
    merchant_id: int = Path(..., gt=0),
    current_user: User = Depends(deps.get_current_active_user),
):
    """
    获取商户API使用情况

    权限要求:
    - 动态权限检查：merchant:view
    - 数据隔离：只能查看有权限访问的商户的API使用情况
    """
    try:
        # 检查权限 - 动态权限检查
        PermissionHelper.check_permission(db, current_user, "merchant", "view")

        # 初始化商户服务
        merchant_service = MerchantService(db)

        # 获取商户（应用数据隔离）
        merchant = merchant_service.get_with_isolation(merchant_id, current_user)
        if not merchant:
            raise BusinessException(
                message="商户不存在或无权限访问",
                code=ErrorCode.NOT_FOUND
            )

        # 获取API使用统计（简化实现）
        api_usage = {
            "merchant_id": merchant_id,
            "merchant_name": merchant.name,
            "total_requests": 0,
            "today_requests": 0,
            "success_rate": 0.0,
            "avg_response_time": 0.0,
            "last_request_time": None,
            "daily_limit": merchant.daily_limit or 10000,
            "hourly_limit": merchant.hourly_limit or 1000,
            "remaining_daily": merchant.daily_limit or 10000,
            "remaining_hourly": merchant.hourly_limit or 1000
        }

        return api_usage

    except BusinessException:
        raise
    except Exception as e:
        logger.error(f"获取商户API使用情况失败: {e}")
        raise BusinessException(
            message=f"获取商户API使用情况失败: {str(e)}",
            code=ErrorCode.INTERNAL_ERROR
        )


@router.get("/{merchant_id}/ip-whitelist", response_model=Dict[str, Any])
async def get_merchant_ip_whitelist(
    *,
    db: Session = Depends(deps.get_db),
    merchant_id: int = Path(..., gt=0),
    current_user: User = Depends(deps.get_current_active_user),
):
    """
    获取商户IP白名单

    权限要求:
    - 动态权限检查：merchant:view
    - 数据隔离：只能查看有权限访问的商户的IP白名单
    """
    try:
        # 检查权限 - 动态权限检查
        PermissionHelper.check_permission(db, current_user, "merchant", "view")

        # 初始化商户服务
        merchant_service = MerchantService(db)

        # 获取商户（应用数据隔离）
        merchant = merchant_service.get_with_isolation(merchant_id, current_user)
        if not merchant:
            raise BusinessException(
                message="商户不存在或无权限访问",
                code=ErrorCode.NOT_FOUND
            )

        # 获取IP白名单（简化实现）
        ip_whitelist = []

        return {
            "merchant_id": merchant_id,
            "merchant_name": merchant.name,
            "ip_list": ip_whitelist,
            "total": len(ip_whitelist)
        }

    except BusinessException:
        raise
    except Exception as e:
        logger.error(f"获取商户IP白名单失败: {e}")
        raise BusinessException(
            message=f"获取商户IP白名单失败: {str(e)}",
            code=ErrorCode.INTERNAL_ERROR
        )


@router.post("/{merchant_id}/verify-password", response_model=Dict[str, Any])
async def verify_merchant_password(
    *,
    db: Session = Depends(deps.get_db),
    merchant_id: int = Path(..., gt=0),
    password_data: schemas.PasswordVerify,
    current_user: User = Depends(deps.get_current_active_user),
):
    """
    验证密码并获取API密文

    权限要求:
    - 动态权限检查：merchant:view
    - 数据隔离：只能验证有权限访问的商户密码
    """
    try:
        # 检查权限 - 动态权限检查
        PermissionHelper.check_permission(db, current_user, "merchant", "view")

        # 初始化商户API服务
        from app.services.merchant_api_service import MerchantAPIService
        merchant_api_service = MerchantAPIService(db)

        # 验证密码并获取API密文
        result = merchant_api_service.verify_password_and_get_secret(
            current_user, merchant_id, password_data
        )

        return result

    except BusinessException:
        raise
    except Exception as e:
        logger.error(f"验证商户密码失败: {e}")
        raise BusinessException(
            message=f"验证密码失败: {str(e)}",
            code=ErrorCode.INTERNAL_ERROR
        )
