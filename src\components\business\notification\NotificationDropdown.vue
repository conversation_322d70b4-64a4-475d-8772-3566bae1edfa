<template>
    <div class="notification-dropdown">
        <div class="dropdown-header">
            <h3 class="dropdown-title">通知</h3>
            <el-button link @click="$emit('view-all')">查看全部</el-button>
        </div>

        <div class="dropdown-content" v-if="notifications.length > 0">
            <div v-for="notification in notifications" :key="notification.id" class="notification-item"
                :class="{ 'unread': !notification.read }">
                <div class="notification-icon">
                    <el-icon :size="20" :color="getIconColor(notification.type)">
                        <component :is="getIconForType(notification.type)"></component>
                    </el-icon>
                </div>
                <div class="notification-body" @click="viewNotification(notification)">
                    <div class="notification-title">
                        {{ notification.title }}
                        <el-tag v-if="notification.priority === 'high'" type="danger" size="small">紧急</el-tag>
                        <el-tag v-else-if="notification.priority === 'medium'" type="warning" size="small">重要</el-tag>
                    </div>
                    <div class="notification-content">{{ notification.content }}</div>
                    <div class="notification-time">{{ formatTime(notification.createdAt) }}</div>
                </div>
                <div class="notification-actions">
                    <el-button v-if="!notification.read" type="primary" size="small" circle
                        @click.stop="markAsRead(notification.id)" title="标记为已读">
                        <el-icon>
                            <Check />
                        </el-icon>
                    </el-button>
                </div>
            </div>
        </div>

        <div class="dropdown-empty" v-else>
            <el-empty description="暂无通知" :image-size="60"></el-empty>
        </div>

        <div class="dropdown-footer">
            <el-button link @click="handleMarkAllRead" :disabled="!hasUnread">全部标为已读</el-button>
            <el-button link @click="$emit('close')">关闭</el-button>
        </div>
    </div>
</template>

<script setup>
import { computed } from 'vue'
import { useRouter } from 'vue-router'
import {
    Bell,
    Warning,
    InfoFilled,
    CircleCheck,
    Check
} from '@element-plus/icons-vue'
import { useNotificationStore } from '@/store/modules/notification'

const props = defineProps({
    notifications: {
        type: Array,
        default: () => []
    }
})

const emit = defineEmits(['close', 'view-all', 'mark-read'])
const router = useRouter()
const notificationStore = useNotificationStore()

// 计算是否有未读通知
const hasUnread = computed(() => {
    return props.notifications.some(notification => !notification.read)
})

// 根据通知类型获取图标
const getIconForType = (type) => {
    const icons = {
        'system': InfoFilled,
        'security': Warning,
        'business': Bell,
        'success': CircleCheck
    }
    return icons[type] || InfoFilled
}

// 根据通知类型获取图标颜色
const getIconColor = (type) => {
    const colors = {
        'system': '#909399',
        'security': '#F56C6C',
        'business': '#E6A23C',
        'success': '#67C23A'
    }
    return colors[type] || '#909399'
}

// 格式化时间
const formatTime = (timestamp) => {
    const now = new Date()
    const date = new Date(timestamp)
    const diff = Math.floor((now - date) / 1000) // 差异，单位为秒

    if (diff < 60) {
        return '刚刚'
    } else if (diff < 3600) {
        return `${Math.floor(diff / 60)}分钟前`
    } else if (diff < 86400) {
        return `${Math.floor(diff / 3600)}小时前`
    } else if (diff < 259200) { // 3天内
        return `${Math.floor(diff / 86400)}天前`
    } else {
        return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`
    }
}

// 标记单个通知为已读
const markAsRead = (id) => {
    emit('mark-read', id)
}

// 标记所有通知为已读
const handleMarkAllRead = () => {
    notificationStore.markAllAsRead()
}

// 查看通知详情
const viewNotification = (notification) => {
    // 如果通知未读，标记为已读
    if (!notification.read) {
        markAsRead(notification.id)
    }

    // 根据通知类型和关联ID跳转到相应页面
    if (notification.relatedPath) {
        router.push(notification.relatedPath)
        emit('close')
    } else {
        // 如果没有关联页面，则跳转到通知中心
        emit('view-all')
    }
}
</script>

<style scoped>
.notification-dropdown {
    position: absolute;
    top: 50px;
    right: 0;
    width: 350px;
    max-height: 500px;
    background-color: #fff;
    border-radius: 4px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    z-index: 2000;
    display: flex;
    flex-direction: column;
}

.dropdown-header {
    padding: 12px 15px;
    border-bottom: 1px solid #EBEEF5;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.dropdown-title {
    margin: 0;
    font-size: 16px;
    font-weight: 500;
}

.dropdown-content {
    flex: 1;
    overflow-y: auto;
    max-height: 350px;
}

.notification-item {
    padding: 10px 15px;
    border-bottom: 1px solid #EBEEF5;
    display: flex;
    align-items: flex-start;
    transition: background-color 0.3s;
    cursor: pointer;
}

.notification-item:hover {
    background-color: #F5F7FA;
}

.notification-item.unread {
    background-color: #ECF5FF;
}

.notification-icon {
    padding-top: 2px;
    margin-right: 10px;
}

.notification-body {
    flex: 1;
}

.notification-title {
    font-weight: 500;
    margin-bottom: 5px;
    display: flex;
    align-items: center;
}

.notification-title .el-tag {
    margin-left: 5px;
}

.notification-content {
    color: #606266;
    font-size: 13px;
    margin-bottom: 5px;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
}

.notification-time {
    color: #909399;
    font-size: 12px;
}

.notification-actions {
    display: flex;
    align-items: center;
    padding-left: 10px;
}

.dropdown-empty {
    padding: 20px 0;
    display: flex;
    justify-content: center;
    align-items: center;
}

.dropdown-footer {
    padding: 10px 15px;
    border-top: 1px solid #EBEEF5;
    display: flex;
    justify-content: space-between;
}
</style>