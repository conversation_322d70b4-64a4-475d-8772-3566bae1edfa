from typing import Any
from sqlalchemy.ext.declarative import as_declarative, declared_attr

# Import all the models, so that Base has them before being
# imported by Alembic
from app.db.base_class import Base  # noqa

# 导入所有模型，确保Alembic可以为它们创建表
from app.models.user import User  # noqa
from app.models.merchant import Merchant  # noqa
from app.models.department import Department  # noqa
from app.models.card_record import CardRecord  # noqa
from app.models.binding_log import BindingLog  # noqa
from app.models.system_settings import SystemSettings  # noqa
from app.models.ip_whitelist import IpWhitelist  # noqa
from app.models.notification_config import NotificationConfig  # noqa
from app.models.notification import Notification  # noqa
from app.models.permission import Permission  # noqa
from app.models.walmart_server import WalmartServer  # noqa
from app.models.walmart_ck import WalmartCK  # noqa
from app.models.role import Role  # noqa

# 导入Telegram机器人模型
from app.models.telegram_group import TelegramGroup  # noqa
from app.models.telegram_user import TelegramUser  # noqa
from app.models.telegram_bot_config import TelegramBotConfig  # noqa
from app.models.telegram_bot_log import TelegramBotLog  # noqa
from app.models.menu import Menu  # noqa
from app.models.audit import AuditLog  # noqa
from app.models.organization_relation import OrganizationRelation  # noqa
from app.models.user_organization import UserOrganization  # noqa
# 导入关联表
from app.models.associations import (  # noqa
    user_roles,
    user_permissions,
    role_permissions,
    role_menus,
    menu_permissions,
)


@as_declarative()
class Base:
    """
    SQLAlchemy模型的基类。

    所有模型都继承自这个类，它提供了一个自动生成的表名（小写模型类名）和id列。
    """

    id: Any
    __name__: str

    # 生成表名为小写的类名
    @declared_attr
    def __tablename__(cls) -> str:
        return cls.__name__.lower()
