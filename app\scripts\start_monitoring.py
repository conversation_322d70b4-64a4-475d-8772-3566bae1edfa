#!/usr/bin/env python3
"""
启动CK计数一致性监控
确保新生成的绑卡数据能够正确计数
"""

import sys
import os
import asyncio
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from app.scripts.monitor_ck_consistency import CKConsistencyMonitor
from app.core.logging import get_logger

logger = get_logger(__name__)

async def main():
    """启动监控"""
    print("🔍 启动CK计数一致性监控")
    print("=" * 60)
    print("监控功能:")
    print("  ✅ 每6小时检查一次CK计数一致性")
    print("  ✅ 自动发现数据不一致问题")
    print("  ✅ 超过阈值时发出警报")
    print("  ✅ 提供修复建议")
    print("=" * 60)
    
    monitor = CKConsistencyMonitor()
    
    try:
        # 先执行一次检查
        print("🔍 执行初始检查...")
        result = await monitor.check_consistency()
        
        if result['inconsistent_count'] == 0:
            print("✅ 初始检查通过，所有CK计数都是一致的")
        else:
            print(f"⚠️  初始检查发现 {result['inconsistent_count']} 个不一致CK")
            print("💡 建议先运行修复脚本: python app/scripts/fix_ck_count_consistency.py --auto")
        
        print("\n🚀 开始持续监控...")
        print("按 Ctrl+C 停止监控")
        
        # 开始持续监控（每6小时检查一次）
        await monitor.run_monitor(interval_hours=6)
        
    except KeyboardInterrupt:
        print("\n👋 监控已停止")
    except Exception as e:
        logger.error(f"监控失败: {e}")
        sys.exit(1)

if __name__ == "__main__":
    asyncio.run(main())
