import uuid
from datetime import datetime
from typing import Dict, Any, Optional

import httpx
from sqlalchemy.orm import Session
from sqlalchemy.ext.asyncio import AsyncSession
from typing import Union

from app.core.logging import get_logger
from app.crud import card as card_crud
from app.crud.card import get_card_record_async
from app.crud import merchant as merchant_crud
from app.models.base import local_now
from app.models.card_record import CallbackStatus, CardStatus
from app.services.binding_log_service import BindingLogService
from app.utils.queue_producer import send_callback_task

# 创建日志记录器
logger = get_logger("callback_service")

# 回调重试配置
MAX_CALLBACK_RETRIES = 5  # 最大重试次数
MIN_RETRY_WAIT = 5  # 最小等待时间（秒）
MAX_RETRY_WAIT = 60 * 30  # 最大等待时间（30分钟）


class CallbackService:
    """
    回调服务，处理绑卡结果的回调逻辑
    """

    def _get_binding_log_service(self, db: Session) -> BindingLogService:
        """【安全修复】获取BindingLogService实例的辅助方法"""
        return BindingLogService(db)

    async def process_callback_from_queue(self, db: Union[Session, AsyncSession], data: Dict[str, Any]):
        """
        从队列处理回调请求

        Args:
            db: 数据库会话
            data: 队列数据，包含record_id、merchant_id和retry_count
        """
        record_id_str = data.get("record_id")
        merchant_id = data.get("merchant_id")
        retry_count = data.get("retry_count", 0)
        ext_data = data.get("ext_data")
        trace_id = data.get("trace_id")

        if not record_id_str or not merchant_id:
            logger.error(f"回调队列数据不完整: {data}")
            return

        try:
            record_id = uuid.UUID(record_id_str)
        except ValueError:
            logger.error(f"无效的 record_id格式 (非UUID): {record_id_str}")
            return

        # 检查重试次数是否达到上限
        if retry_count >= MAX_CALLBACK_RETRIES:
            logger.warning(
                f"回调重试次数已达上限 ({MAX_CALLBACK_RETRIES})，不再重试: 记录ID {record_id}"
            )
            return

        # 执行回调
        success = await self._process_callback(
            db, record_id, merchant_id, retry_count, ext_data, trace_id
        )

        # 如果回调失败且未达到最大重试次数，发送延迟消息到队列
        if not success and retry_count < MAX_CALLBACK_RETRIES - 1:
            next_retry = retry_count + 1
            # 计算下次重试的延迟时间（指数退避）
            delay_seconds = min(2 ** next_retry * MIN_RETRY_WAIT, MAX_RETRY_WAIT)

            logger.info(
                f"安排回调重试 #{next_retry}，延迟 {delay_seconds} 秒: 记录ID {record_id}"
            )

            # 发送延迟消息到队列
            await send_callback_task(
                {
                    "record_id": record_id_str,
                    "merchant_id": merchant_id,
                    "retry_count": next_retry,
                    "ext_data": ext_data,
                    "trace_id": trace_id,
                },
                delay_seconds=delay_seconds,
            )

    async def _process_callback(
            self,
            db: Union[Session, AsyncSession],
            record_id: uuid.UUID,
            merchant_id: int,
            retry_count: int,
            ext_data: Optional[str] = None,
            trace_id: Optional[str] = None,
    ) -> bool:
        """处理回调"""
        try:
            # 直接使用传入的数据库会话，不创建新的会话
            # 验证记录和商户
            record, merchant = await self._validate_callback_prerequisites(
                db, record_id, merchant_id, retry_count
            )
            if not record or not merchant:
                return True

            # 准备回调数据
            callback_data = self._prepare_callback_data(record, trace_id, retry_count, ext_data)

            # 发送回调请求
            return await self._send_callback_request(
                db, record, merchant, callback_data, record_id, retry_count
            )
        except Exception as e:
            logger.exception(f"回调处理中发生异常: {e}")
            return False

    async def _validate_callback_prerequisites(
        self, new_db: Union[Session, AsyncSession], record_id: uuid.UUID, merchant_id: int, retry_count: int
    ) -> tuple:
        """验证回调前置条件"""
        # 获取记录
        record_id_str = str(record_id)
        # 检查是否为异步会话，使用相应的方法
        if isinstance(new_db, AsyncSession):
            record = await get_card_record_async(new_db, record_id_str, merchant_id)
        else:
            record = card_crud.get_card_record(new_db, record_id_str, merchant_id)
        if not record:
            logger.error(f"卡记录不存在: {record_id_str}")
            return None, None

        # 检查回调状态，防止重复回调
        if record.callback_status == CallbackStatus.SUCCESS:
            logger.info(f"记录 {record_id} 回调状态已为 SUCCESS，跳过重复回调。")
            return None, None

        # 获取商家配置
        merchant = merchant_crud.get_merchant(new_db, merchant_id)
        if not merchant or not merchant.callback_url:
            error_msg = f"商家 {merchant_id} 不存在或未配置回调URL"
            logger.error(error_msg)
            await self._update_callback_failed(new_db, record, error_msg, retry_count)
            return None, None

        return record, merchant

    def _prepare_callback_data(self, record, trace_id: str, retry_count: int, ext_data: str) -> dict:
        """准备回调数据 - 使用增强的验证器"""
        from app.services.callback_data_validator import CallbackDataValidator

        # 检查余额获取是否成功
        balance_fetch_success = (
            record.balance is not None or
            record.cardBalance is not None or
            record.balanceCnt is not None
        )

        # 使用验证器准备和验证回调数据（抑制重复日志）
        validator = CallbackDataValidator()
        callback_data = validator.prepare_and_validate_callback_data(
            record=record,
            trace_id=trace_id,
            retry_count=retry_count,
            ext_data=ext_data,
            balance_fetch_success=balance_fetch_success,
            suppress_logging=True  # 抑制验证器内部的日志，由回调服务统一处理
        )

        # 获取验证结果摘要并统一记录日志
        validation_summary = validator.get_validation_summary()

        # 统一的日志记录，避免重复
        if validation_summary["has_errors"]:
            logger.error(
                f"[CALLBACK_VALIDATION_ERROR] 回调数据验证失败 | "
                f"record_id={record.id} | errors={validation_summary['error_count']} | "
                f"details={validation_summary['errors']}"
            )

        if validation_summary["has_warnings"]:
            logger.warning(
                f"[CALLBACK_VALIDATION_WARNING] 回调数据验证警告 | "
                f"record_id={record.id} | warnings={validation_summary['warning_count']} | "
                f"details={validation_summary['warnings']}"
            )

        # 获取验证结果摘要
        validation_summary = validator.get_validation_summary()

        # 如果有验证错误，记录详细信息
        if validation_summary["has_errors"]:
            logger.error(
                f"[CALLBACK_DATA_ERROR] 回调数据验证失败 | "
                f"record_id={record.id} | errors={validation_summary['error_count']} | "
                f"details={validation_summary['errors']}"
            )

        # 如果有验证警告，记录警告信息
        if validation_summary["has_warnings"]:
            logger.warning(
                f"[CALLBACK_DATA_WARNING] 回调数据验证警告 | "
                f"record_id={record.id} | warnings={validation_summary['warning_count']} | "
                f"details={validation_summary['warnings']}"
            )

        return callback_data

    async def _send_callback_request(
        self, new_db: Session, record, merchant, callback_data: dict,
        record_id: uuid.UUID, retry_count: int
    ) -> bool:
        """发送回调请求"""
        try:
            async with httpx.AsyncClient() as client:
                response = await client.post(
                    merchant.callback_url,
                    json=callback_data,
                    headers={"Content-Type": "application/json"},
                    timeout=10,
                )

                if 200 <= response.status_code < 300:
                    await self._handle_callback_success(
                        new_db, record, response, callback_data, record_id, retry_count
                    )
                    return True
                else:
                    await self._handle_callback_failure(
                        new_db, record, response, callback_data, record_id, retry_count
                    )
                    return False
        except Exception as e:
            await self._handle_callback_exception(
                new_db, record, e, record_id, retry_count
            )
            return False


    async def _update_callback_failed(
        self, new_db: Session, record, error_msg: str, retry_count: int
    ):
        """更新回调失败状态"""
        record.callback_status = CallbackStatus.FAILED
        record.callback_result = error_msg
        record.retry_count = retry_count
        record.callback_time = local_now()
        new_db.add(record)
        new_db.commit()

    async def _handle_callback_success(
        self, new_db: Session, record, response, callback_data: dict,
        record_id: uuid.UUID, retry_count: int
    ):
        """处理回调成功"""
        record.callback_status = CallbackStatus.SUCCESS
        record.callback_result = f"HTTP {response.status_code}: {response.text[:200]}"
        record.retry_count = retry_count
        record.callback_time = local_now()
        new_db.add(record)
        new_db.commit()

        # 记录回调成功日志
        try:
            # 【安全修复】使用辅助方法获取BindingLogService实例
            binding_log_service = self._get_binding_log_service(new_db)
            await binding_log_service.log_callback(
                db=new_db,
                card_record_id=str(record_id),
                success=True,
                details={
                    "status_code": response.status_code,
                    "request_data": callback_data,
                    "response_text": response.text[:200],
                    "retry_count": retry_count,
                    "callback_time": datetime.now().isoformat(),
                },
                attempt_number=str(retry_count) if retry_count > 0 else None,
            )
        except Exception as log_error:
            logger.error(f"记录回调成功日志失败: {str(log_error)}")

        logger.info(f"回调成功: 记录ID {record_id}, 重试次数 {retry_count}")

    async def _handle_callback_failure(
        self, new_db: Session, record, response, callback_data: dict,
        record_id: uuid.UUID, retry_count: int
    ):
        """处理回调失败"""
        record.callback_status = CallbackStatus.FAILED
        record.callback_result = f"HTTP {response.status_code}: {response.text[:200]}"
        record.retry_count = retry_count
        record.callback_time = local_now()
        new_db.add(record)
        new_db.commit()

        # 记录回调失败日志
        try:
            # 【安全修复】使用辅助方法获取BindingLogService实例
            binding_log_service = self._get_binding_log_service(new_db)
            await binding_log_service.log_callback(
                db=new_db,
                card_record_id=str(record_id),
                success=False,
                details={
                    "status_code": response.status_code,
                    "request_data": callback_data,
                    "response_text": response.text[:200],
                    "retry_count": retry_count,
                    "callback_time": datetime.now().isoformat(),
                    "error": f"HTTP {response.status_code} 错误",
                },
                attempt_number=str(retry_count) if retry_count > 0 else None,
            )
        except Exception as log_error:
            logger.error(f"记录回调失败日志失败: {str(log_error)}")

        logger.warning(f"回调失败 (HTTP {response.status_code}): 记录ID {record_id}, 重试次数 {retry_count}")

    async def _handle_callback_exception(
        self, new_db: Session, record, exception: Exception,
        record_id: uuid.UUID, retry_count: int
    ):
        """处理回调异常"""
        logger.exception(f"回调过程中发生异常: {exception}")
        record.callback_status = CallbackStatus.FAILED
        record.callback_result = f"Exception: {str(exception)}"
        record.retry_count = retry_count
        record.callback_time = local_now()
        new_db.add(record)
        new_db.commit()

        # 记录回调异常日志
        try:
            # 【安全修复】使用辅助方法获取BindingLogService实例
            binding_log_service = self._get_binding_log_service(new_db)
            await binding_log_service.log_callback(
                db=new_db,
                card_record_id=str(record_id),
                success=False,
                details={
                    "exception": str(exception),
                    "retry_count": retry_count,
                    "callback_time": datetime.now().isoformat(),
                    "error": "回调过程中发生异常",
                },
                attempt_number=str(retry_count) if retry_count > 0 else None,
            )
        except Exception as log_error:
            logger.error(f"记录回调异常日志失败: {str(log_error)}")


# 创建回调服务实例
callback_service = CallbackService()
