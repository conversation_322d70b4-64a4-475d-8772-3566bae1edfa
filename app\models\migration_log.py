from sqlalchemy import Column, String, Integer, Text, DateTime, JSON
from sqlalchemy.sql import func

from app.models.base import BaseModel


class MigrationLog(BaseModel):
    """迁移日志模型"""

    __tablename__ = "migration_logs"

    id = Column(Integer, primary_key=True, autoincrement=True, comment="日志ID")
    migration_name = Column(String(255), nullable=False, unique=True, comment="迁移名称")
    status = Column(String(50), nullable=False, comment="状态")
    message = Column(Text, nullable=True, comment="消息")
    data_summary = Column(JSON, nullable=True, comment="数据摘要")
    created_at = Column(DateTime(timezone=True), nullable=False, default=func.now(), comment="创建时间")
    completed_at = Column(DateTime(timezone=True), nullable=True, comment="完成时间")

    def to_dict(self):
        """转换为字典"""
        return {
            "id": self.id,
            "migration_name": self.migration_name,
            "status": self.status,
            "message": self.message,
            "data_summary": self.data_summary,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "completed_at": self.completed_at.isoformat() if self.completed_at else None,
        }
