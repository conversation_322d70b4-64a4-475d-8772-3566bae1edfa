/**
 * 剪贴板工具
 * 提供跨浏览器兼容的剪贴板操作功能
 */

import { ElMessage } from 'element-plus'

/**
 * 复制文本到剪贴板
 * 优先使用现代 Clipboard API，如果不支持则使用备选方案
 *
 * @param {string} text 要复制的文本
 * @param {Object} options 配置选项
 * @param {string} options.successMessage 成功时的提示消息
 * @param {string} options.errorMessage 失败时的提示消息
 * @param {boolean} options.showMessage 是否显示消息提示
 * @returns {Promise<boolean>} 是否复制成功
 */
export function copyToClipboard(text, options = {}) {
  const {
    successMessage = '已复制到剪贴板',
    errorMessage = '复制失败，请手动复制',
    showMessage = true
  } = options

  if (!text) {
    if (showMessage) {
      ElMessage.warning('没有可复制的内容')
    }
    return Promise.resolve(false)
  }

  // 优先使用现代 Clipboard API
  if (navigator.clipboard && typeof navigator.clipboard.writeText === 'function') {
    return navigator.clipboard.writeText(text)
      .then(() => {
        if (showMessage) {
          ElMessage.success(successMessage)
        }
        return true
      })
      .catch((err) => {
        console.warn('使用 Clipboard API 失败:', err)
        // 如果 Clipboard API 失败，尝试其他方法
        return tryAlternativeMethods(text, { successMessage, errorMessage, showMessage })
      })
  } else {
    // 浏览器不支持 Clipboard API，尝试其他方法
    return tryAlternativeMethods(text, { successMessage, errorMessage, showMessage })
  }
}

/**
 * 尝试备选复制方法
 *
 * @param {string} text 要复制的文本
 * @param {Object} options 配置选项
 * @returns {Promise<boolean>} 是否复制成功
 */
function tryAlternativeMethods(text, { successMessage, errorMessage, showMessage }) {
  // 尝试使用 ClipboardItem API (更现代的方法)
  if (window.ClipboardItem && navigator.clipboard && navigator.clipboard.write) {
    const type = 'text/plain'
    const blob = new Blob([text], { type })
    const data = [new ClipboardItem({ [type]: blob })]

    return navigator.clipboard.write(data)
      .then(() => {
        if (showMessage) {
          ElMessage.success(successMessage)
        }
        return true
      })
      .catch((err) => {
        console.warn('使用 ClipboardItem API 失败:', err)
        // 如果这个也失败，回退到传统方法
        return useExecCommand(text, { successMessage, errorMessage, showMessage })
      })
  }

  // 如果上述方法都不可用，尝试传统方法
  return Promise.resolve(useExecCommand(text, { successMessage, errorMessage, showMessage }))
}

/**
 * 使用 execCommand 方法复制文本（仅作为兼容性的最后手段）
 * 注意：execCommand 已经被废弃，但在某些旧浏览器中仍然是唯一可用的方法
 *
 * @param {string} text 要复制的文本
 * @param {Object} options 配置选项
 * @returns {boolean} 是否复制成功
 */
function useExecCommand(text, { successMessage, errorMessage, showMessage }) {
  // 创建一个临时的文本区域
  const textarea = document.createElement('textarea')

  // 设置文本区域的样式使其不可见
  textarea.style.position = 'fixed'
  textarea.style.opacity = '0'
  textarea.style.pointerEvents = 'none'
  textarea.style.left = '-9999px'
  textarea.style.top = '0'
  textarea.value = text

  // 添加到文档中
  document.body.appendChild(textarea)

  try {
    // 选中文本
    textarea.focus()
    textarea.select()

    // 对于移动设备
    if (typeof textarea.setSelectionRange === 'function') {
      textarea.setSelectionRange(0, text.length)
    }

    // 执行复制命令 (已废弃但兼容性好)
    const successful = document.execCommand('copy')

    if (successful) {
      if (showMessage) {
        ElMessage.success(successMessage)
      }
      return true
    } else {
      // 如果 execCommand 失败，尝试提示用户手动复制
      console.warn('execCommand 复制失败')
      if (showMessage) {
        ElMessage.error(errorMessage)
      }
      return false
    }
  } catch (err) {
    console.error('复制失败:', err)
    if (showMessage) {
      ElMessage.error(errorMessage)
    }
    return false
  } finally {
    // 清理
    document.body.removeChild(textarea)
  }
}

export default {
  copyToClipboard
}
