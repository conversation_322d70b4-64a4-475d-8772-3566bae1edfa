#!/usr/bin/env python3
"""
绑卡记录重试功能快速测试脚本
"""
import sys
import os
import uuid
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from fastapi.testclient import TestClient
from sqlalchemy.orm import Session

from app.main import app
from app.core.database import get_db
from app.models.card_record import CardRecord, CardStatus
from app.models.user import User
from app.models.merchant import Merchant
from app.models.department import Department
from app.services.auth_service import AuthService

client = TestClient(app)


def get_test_db():
    """获取测试数据库会话"""
    db = next(get_db())
    try:
        yield db
    finally:
        db.close()


def create_test_data(db: Session):
    """创建测试数据"""
    print("🔧 创建测试数据...")
    
    # 查找现有的测试商户
    merchant = db.query(Merchant).filter(Merchant.name == "test1").first()
    if not merchant:
        print("❌ 未找到测试商户 test1，请先运行系统初始化")
        return None, None, None
    
    # 查找测试商户的部门
    department = db.query(Department).filter(
        Department.merchant_id == merchant.id
    ).first()
    if not department:
        print("❌ 未找到测试商户的部门")
        return None, None, None
    
    # 查找测试用户
    test_user = db.query(User).filter(User.username == "test1").first()
    if not test_user:
        print("❌ 未找到测试用户 test1")
        return None, None, None
    
    # 创建失败的测试卡记录
    card_record = CardRecord(
        id=str(uuid.uuid4()),
        merchant_id=merchant.id,
        department_id=department.id,
        merchant_order_id=f"RETRY_TEST_{uuid.uuid4().hex[:8]}",
        amount=10000,  # 100元
        card_number="1234567890123456",
        card_password="encrypted_password",
        status=CardStatus.FAILED,
        request_id=str(uuid.uuid4()),
        trace_id=str(uuid.uuid4()),
        request_data={"test": "retry_data"},
        error_message="需要登录",  # CK失效错误
        response_data={"need_retry_with_new_user": True},
        retry_count=0
    )
    db.add(card_record)
    db.commit()
    db.refresh(card_record)
    
    print(f"✅ 创建测试卡记录: {card_record.id}")
    return merchant, test_user, card_record


def get_auth_token(username: str, password: str):
    """获取认证token"""
    response = client.post(
        "/api/v1/auth/login",
        data={
            "username": username,
            "password": password
        }
    )
    if response.status_code == 200:
        return response.json()["access_token"]
    else:
        print(f"❌ 登录失败: {response.json()}")
        return None


def test_retry_functionality():
    """测试重试功能"""
    print("🚀 开始测试绑卡记录重试功能...")
    
    db = next(get_test_db())
    try:
        # 创建测试数据
        merchant, test_user, card_record = create_test_data(db)
        if not all([merchant, test_user, card_record]):
            return False
        
        # 获取认证token
        print("🔐 获取认证token...")
        token = get_auth_token("test1", "12345678")
        if not token:
            return False
        
        headers = {"Authorization": f"Bearer {token}"}
        
        # 测试1: 重试失败的卡记录
        print(f"📝 测试1: 重试失败的卡记录 {card_record.id}")
        response = client.post(
            f"/api/v1/cards/{card_record.id}/retry",
            headers=headers
        )
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ 重试成功: {data}")
            
            # 验证卡记录状态是否更新
            db.refresh(card_record)
            if card_record.status == CardStatus.PENDING:
                print("✅ 卡记录状态已更新为 pending")
            else:
                print(f"❌ 卡记录状态未正确更新: {card_record.status}")
                
            if card_record.retry_count == 1:
                print("✅ 重试次数已正确增加")
            else:
                print(f"❌ 重试次数未正确更新: {card_record.retry_count}")
                
        else:
            print(f"❌ 重试失败: {response.status_code} - {response.json()}")
            return False
        
        # 测试2: 重试非失败状态的卡记录
        print("📝 测试2: 重试非失败状态的卡记录")
        card_record.status = CardStatus.SUCCESS
        db.commit()
        
        response = client.post(
            f"/api/v1/cards/{card_record.id}/retry",
            headers=headers
        )
        
        if response.status_code == 400:
            print("✅ 正确拒绝重试非失败状态的记录")
        else:
            print(f"❌ 应该拒绝重试非失败状态的记录: {response.status_code}")
            return False
        
        # 测试3: 重试不可重试的失败记录
        print("📝 测试3: 重试不可重试的失败记录")
        card_record.status = CardStatus.FAILED
        card_record.error_message = "卡号无效"  # 非CK失效错误
        card_record.response_data = {"error_code": 400}
        db.commit()
        
        response = client.post(
            f"/api/v1/cards/{card_record.id}/retry",
            headers=headers
        )
        
        if response.status_code == 400:
            print("✅ 正确拒绝重试不可重试的失败记录")
        else:
            print(f"❌ 应该拒绝重试不可重试的失败记录: {response.status_code}")
            return False
        
        # 测试4: 重试不存在的卡记录
        print("📝 测试4: 重试不存在的卡记录")
        fake_id = str(uuid.uuid4())
        response = client.post(
            f"/api/v1/cards/{fake_id}/retry",
            headers=headers
        )
        
        if response.status_code == 400:
            print("✅ 正确处理不存在的卡记录")
        else:
            print(f"❌ 应该正确处理不存在的卡记录: {response.status_code}")
            return False
        
        print("🎉 所有测试通过！")
        return True
        
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        return False
    finally:
        db.close()


def main():
    """主函数"""
    print("=" * 60)
    print("🧪 绑卡记录重试功能快速测试")
    print("=" * 60)
    
    success = test_retry_functionality()
    
    print("=" * 60)
    if success:
        print("✅ 测试完成 - 所有功能正常")
        sys.exit(0)
    else:
        print("❌ 测试失败 - 请检查错误信息")
        sys.exit(1)


if __name__ == "__main__":
    main()
