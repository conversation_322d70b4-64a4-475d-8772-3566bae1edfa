"""
部门操作日志服务

专门用于记录部门绑卡控制相关的操作日志，包括：
1. 开关状态变更日志
2. 权重调整日志
3. 批量操作日志
4. 操作审计追踪
"""

import json
import logging
from typing import Dict, Any, Optional, List
from datetime import datetime, timezone
from sqlalchemy.orm import Session

from app.models.user import User
from app.models.department import Department


logger = logging.getLogger(__name__)


class DepartmentOperationLogService:
    """部门操作日志服务"""
    
    def __init__(self, db: Session):
        self.db = db
        self.logger = logger
    
    def log_binding_controls_change(
        self,
        department: Department,
        changes: Dict[str, Any],
        operator: User,
        operation_type: str = "update_binding_controls",
        additional_context: Optional[Dict[str, Any]] = None
    ) -> bool:
        """
        记录绑卡控制变更日志
        
        Args:
            department: 部门对象
            changes: 变更内容
            operator: 操作用户
            operation_type: 操作类型
            additional_context: 额外上下文信息
            
        Returns:
            bool: 是否记录成功
        """
        try:
            log_data = {
                "operation_type": operation_type,
                "department_info": {
                    "id": department.id,
                    "name": department.name,
                    "code": department.code,
                    "merchant_id": department.merchant_id
                },
                "operator_info": {
                    "id": operator.id,
                    "username": operator.username,
                    "name": operator.full_name or operator.username
                },
                "changes": changes,
                "timestamp": datetime.now(timezone.utc).isoformat(),
                "additional_context": additional_context or {}
            }
            
            # 记录到应用日志
            self.logger.info(
                f"[DEPT_BINDING_CONTROL] {operation_type} | "
                f"部门: {department.name}({department.id}) | "
                f"操作员: {operator.username} | "
                f"变更: {json.dumps(changes, ensure_ascii=False)}"
            )
            
            # 这里可以扩展到数据库日志表或外部日志系统
            # 例如：存储到 operation_logs 表
            
            return True
            
        except Exception as e:
            self.logger.error(f"记录部门绑卡控制变更日志失败: {e}")
            return False
    
    def log_batch_operation(
        self,
        operation_type: str,
        department_ids: List[int],
        operation_data: Dict[str, Any],
        operator: User,
        results: Dict[str, Any],
        additional_context: Optional[Dict[str, Any]] = None
    ) -> bool:
        """
        记录批量操作日志
        
        Args:
            operation_type: 操作类型
            department_ids: 涉及的部门ID列表
            operation_data: 操作数据
            operator: 操作用户
            results: 操作结果
            additional_context: 额外上下文信息
            
        Returns:
            bool: 是否记录成功
        """
        try:
            log_data = {
                "operation_type": operation_type,
                "batch_info": {
                    "department_count": len(department_ids),
                    "department_ids": department_ids,
                    "operation_data": operation_data
                },
                "operator_info": {
                    "id": operator.id,
                    "username": operator.username,
                    "name": operator.full_name or operator.username
                },
                "results": results,
                "timestamp": datetime.now(timezone.utc).isoformat(),
                "additional_context": additional_context or {}
            }
            
            # 记录到应用日志
            self.logger.info(
                f"[DEPT_BATCH_OPERATION] {operation_type} | "
                f"部门数量: {len(department_ids)} | "
                f"操作员: {operator.username} | "
                f"成功数量: {results.get('updated_count', 0)} | "
                f"操作数据: {json.dumps(operation_data, ensure_ascii=False)}"
            )
            
            return True
            
        except Exception as e:
            self.logger.error(f"记录批量操作日志失败: {e}")
            return False
    
    def log_weight_algorithm_test(
        self,
        merchant_id: int,
        test_parameters: Dict[str, Any],
        test_results: Dict[str, Any],
        operator: User,
        additional_context: Optional[Dict[str, Any]] = None
    ) -> bool:
        """
        记录权重算法测试日志
        
        Args:
            merchant_id: 商户ID
            test_parameters: 测试参数
            test_results: 测试结果
            operator: 操作用户
            additional_context: 额外上下文信息
            
        Returns:
            bool: 是否记录成功
        """
        try:
            log_data = {
                "operation_type": "test_weight_algorithm",
                "merchant_id": merchant_id,
                "test_parameters": test_parameters,
                "test_results": {
                    "total_departments": test_results.get('total_departments', 0),
                    "test_count": test_results.get('test_count', 0),
                    "department_selection_summary": self._summarize_department_selection(
                        test_results.get('department_selection_count', {})
                    )
                },
                "operator_info": {
                    "id": operator.id,
                    "username": operator.username,
                    "name": operator.full_name or operator.username
                },
                "timestamp": datetime.now(timezone.utc).isoformat(),
                "additional_context": additional_context or {}
            }
            
            # 记录到应用日志
            self.logger.info(
                f"[WEIGHT_ALGORITHM_TEST] 商户: {merchant_id} | "
                f"操作员: {operator.username} | "
                f"测试次数: {test_parameters.get('test_count', 0)} | "
                f"涉及部门: {test_results.get('total_departments', 0)}"
            )
            
            return True
            
        except Exception as e:
            self.logger.error(f"记录权重算法测试日志失败: {e}")
            return False
    
    def log_ck_selection_with_weight(
        self,
        merchant_id: int,
        selected_department: Dict[str, Any],
        selected_ck_id: Optional[int],
        weight_context: Dict[str, Any],
        additional_context: Optional[Dict[str, Any]] = None
    ) -> bool:
        """
        记录基于权重的CK选择日志
        
        Args:
            merchant_id: 商户ID
            selected_department: 选中的部门信息
            selected_ck_id: 选中的CK ID
            weight_context: 权重上下文信息
            additional_context: 额外上下文信息
            
        Returns:
            bool: 是否记录成功
        """
        try:
            log_data = {
                "operation_type": "ck_selection_with_weight",
                "merchant_id": merchant_id,
                "selected_department": selected_department,
                "selected_ck_id": selected_ck_id,
                "weight_context": weight_context,
                "timestamp": datetime.now(timezone.utc).isoformat(),
                "additional_context": additional_context or {}
            }
            
            # 记录到应用日志（调试级别，避免过多日志）
            self.logger.debug(
                f"[CK_WEIGHT_SELECTION] 商户: {merchant_id} | "
                f"选中部门: {selected_department.get('name', 'Unknown')} | "
                f"权重: {selected_department.get('binding_weight', 0)} | "
                f"CK_ID: {selected_ck_id}"
            )
            
            return True
            
        except Exception as e:
            self.logger.error(f"记录CK权重选择日志失败: {e}")
            return False
    
    def _summarize_department_selection(
        self, 
        department_selection_count: Dict[int, Dict[str, Any]]
    ) -> Dict[str, Any]:
        """
        汇总部门选择统计
        
        Args:
            department_selection_count: 部门选择计数
            
        Returns:
            Dict: 汇总信息
        """
        try:
            if not department_selection_count:
                return {}
            
            total_selections = sum(
                dept_info['count'] for dept_info in department_selection_count.values()
            )
            
            summary = {
                "total_selections": total_selections,
                "departments": []
            }
            
            for dept_id, dept_info in department_selection_count.items():
                summary["departments"].append({
                    "department_id": dept_id,
                    "department_name": dept_info.get('name', 'Unknown'),
                    "weight": dept_info.get('weight', 0),
                    "selection_count": dept_info.get('count', 0),
                    "actual_percentage": dept_info.get('actual_percentage', 0)
                })
            
            # 按选择次数降序排列
            summary["departments"].sort(
                key=lambda x: x['selection_count'], reverse=True
            )
            
            return summary
            
        except Exception as e:
            self.logger.error(f"汇总部门选择统计失败: {e}")
            return {}
    
    def get_operation_summary(
        self,
        merchant_id: Optional[int] = None,
        department_id: Optional[int] = None,
        operation_type: Optional[str] = None,
        start_time: Optional[datetime] = None,
        end_time: Optional[datetime] = None
    ) -> Dict[str, Any]:
        """
        获取操作日志汇总
        
        Args:
            merchant_id: 商户ID过滤
            department_id: 部门ID过滤
            operation_type: 操作类型过滤
            start_time: 开始时间过滤
            end_time: 结束时间过滤
            
        Returns:
            Dict: 操作日志汇总
        """
        try:
            # 这里可以实现从数据库或日志文件中查询操作记录
            # 目前返回基本的汇总信息
            
            summary = {
                "filters": {
                    "merchant_id": merchant_id,
                    "department_id": department_id,
                    "operation_type": operation_type,
                    "start_time": start_time.isoformat() if start_time else None,
                    "end_time": end_time.isoformat() if end_time else None
                },
                "summary": {
                    "total_operations": 0,
                    "operation_types": {},
                    "most_active_operators": [],
                    "most_modified_departments": []
                },
                "message": "操作日志汇总功能需要配合数据库日志表实现"
            }
            
            return summary
            
        except Exception as e:
            self.logger.error(f"获取操作日志汇总失败: {e}")
            return {
                "error": str(e),
                "summary": {
                    "total_operations": 0,
                    "operation_types": {},
                    "most_active_operators": [],
                    "most_modified_departments": []
                }
            }
