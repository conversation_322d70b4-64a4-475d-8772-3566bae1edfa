import asyncio
from typing import Dict, Any, Optional

from sqlalchemy.orm import Session
from app.core.logging import get_logger
from app.models.binding_log import LogLevel
from app.models.walmart_ck import WalmartCK
from app.services.walmart_ck_service_new import WalmartCKService
from app.services.binding_log_service import BindingLogService

# 创建日志记录器
logger = get_logger("retry_logic_service")


class RetryLogicService:
    """专门处理重试逻辑的服务"""
    
    def __init__(self):
        pass
    
    async def should_retry_with_new_user(
        self,
        db: Session,
        call_outcome: Dict[str, Any],
        walmart_ck: WalmartCK,
        record,
        attempt: int,
    ) -> Dict[str, Any]:
        """
        判断是否需要使用新用户重试
        
        Returns:
            Dict containing:
            - should_retry: bool
            - new_walmart_ck: WalmartCK or None
            - reason: str
        """
        need_retry_with_new_user = call_outcome.get("need_retry_with_new_user", False)
        
        if need_retry_with_new_user:
            return await self._handle_ck_invalid_retry(
                db, walmart_ck, record, attempt, "错误码203"
            )
        
        return {
            "should_retry": False,
            "new_walmart_ck": None,
            "reason": "不需要重试"
        }
    
    async def handle_business_failure(
        self,
        db: Session,
        api_business_result: Dict[str, Any],
        walmart_ck: WalmartCK,
        record,
        attempt: int,
    ) -> bool:
        """
        处理业务失败情况

        Returns:
            bool: 是否应该继续重试
        """
        error_message = api_business_result.get("error", "")
        raw_response = api_business_result.get("raw_response", {})

        # 检查是否是CK失效错误
        is_ck_invalid = self._is_ck_invalid_error(raw_response)

        # 【新增】检查是否是单日绑卡限制错误
        is_daily_limit_error = self._is_daily_limit_error(raw_response, error_message)

        # 【新增】检查是否是需要禁用CK的错误（错误次数过多、数据异常）
        is_ck_disable_error = self._is_ck_disable_error(raw_response, error_message)

        # 【新增】检查是否是服务器繁忙错误
        is_server_busy_error = self._is_server_busy_error(raw_response, error_message)

        if is_ck_invalid:
            # CK失效的情况，禁用CK并尝试重试
            retry_result = await self._handle_ck_invalid_retry(
                db, walmart_ck, record, attempt, "需要登录"
            )
            return retry_result["should_retry"]
        elif is_daily_limit_error:
            # 【新增】单日绑卡限制的情况，禁用CK并尝试重试
            retry_result = await self._handle_ck_invalid_retry(
                db, walmart_ck, record, attempt, "单日绑卡限制"
            )
            return retry_result["should_retry"]
        elif is_ck_disable_error:
            # 【新增】需要禁用CK的错误（错误次数过多、数据异常），禁用CK并尝试重试
            error_type = self._get_ck_disable_error_type(raw_response, error_message)
            retry_result = await self._handle_ck_invalid_retry(
                db, walmart_ck, record, attempt, error_type
            )
            return retry_result["should_retry"]
        elif is_server_busy_error:
            # 【新增】服务器繁忙错误，禁用CK并尝试重试
            retry_result = await self._handle_ck_invalid_retry(
                db, walmart_ck, record, attempt, "服务器繁忙"
            )
            return retry_result["should_retry"]
        else:
            # 其他失败情况，记录错误但不禁用CK，也不重试
            await self._log_non_ck_error(
                db, walmart_ck, record, error_message
            )
            return False
    
    async def _handle_ck_invalid_retry(
        self,
        db: Session,
        walmart_ck: WalmartCK,
        record,
        attempt: int,
        reason: str,
    ) -> Dict[str, Any]:
        """处理CK失效的重试逻辑"""
        # 统一的日志记录，直接输出错误信息
        logger.warning(
            f"CK失效({reason})，禁用CK {walmart_ck.id} 并尝试获取新CK (下一次尝试 {attempt + 1})"
        )
        
        # 禁用当前CK用户
        walmart_ck.active = False
        db.add(walmart_ck)
        await db.commit()
        
        # 记录重试日志
        # 【安全修复】创建BindingLogService实例
        binding_log_service = BindingLogService(db)
        await binding_log_service.log_retry(
            db=db,
            card_record_id=str(record.id),
            attempt_number=str(attempt + 1),
            reason=f"CK失效({reason})，禁用当前用户并尝试获取新用户",
            details={
                "old_walmart_ck_id": str(walmart_ck.id),
                "is_ck_invalid": True,
                "reason": reason,
            },
            walmart_ck_id=str(walmart_ck.id),
        )
        
        # 尝试获取新的可用用户
        walmart_ck_service = WalmartCKService(db)
        new_walmart_ck = await walmart_ck_service.get_available_ck(
            merchant_id=record.merchant_id,
            department_id=record.department_id,
            exclude_ids=[walmart_ck.id]  # 排除当前失效的CK
        )
        
        if not new_walmart_ck:
            logger.error(f"CK失效后无法获取新的可用用户，停止重试")
            return {
                "should_retry": False,
                "new_walmart_ck": None,
                "reason": "无可用用户"
            }
        
        logger.info(f"获取到新的可用用户: {new_walmart_ck.id}")
        return {
            "should_retry": True,
            "new_walmart_ck": new_walmart_ck,
            "reason": f"CK失效，切换到新用户 {new_walmart_ck.id}"
        }
    
    async def _log_non_ck_error(
        self,
        db: Session,
        walmart_ck: WalmartCK,
        record,
        error_message: str,
    ):
        """记录非CK错误，但不禁用CK"""
        logger.info(
            f"API业务失败(非CK失效)，记录错误但不禁用CK | "
            f"walmart_ck_id={walmart_ck.id} | error={error_message}"
        )

        # 记录错误日志，但不禁用CK
        # 【安全修复】创建BindingLogService实例
        binding_log_service = BindingLogService(db)
        await binding_log_service.log_system(
            db=db,
            card_record_id=str(record.id),
            message=f"绑卡失败(非CK失效)：{error_message}",
            log_level=LogLevel.INFO,
            details={
                "walmart_ck_id": str(walmart_ck.id),
                "error": error_message,
                "reason": "API业务失败(非CK失效)，CK未被禁用",
            },
        )
    
    def _is_ck_invalid_error(self, api_response_data: Dict[str, Any]) -> bool:
        """
        判断是否是CK失效(需要登录)的错误

        重要原则：只有底层沃尔玛API明确返回CK失效错误时才认为是CK失效
        其他错误（HTTP错误、网络错误等）不应该被认为是CK失效

        Args:
            api_response_data: API响应数据

        Returns:
            bool: 是否是CK失效错误
        """
        if not api_response_data:
            return False

        # 只检查明确的CK失效错误：错误码203且消息包含"请先去登录"
        error_code = api_response_data.get("error", {}).get("errorcode")
        error_message = (
            api_response_data.get("message")
            or api_response_data.get("msg")
            or api_response_data.get("error", {}).get("message")
            or ""
        )

        # 严格检查：必须是错误码203且消息明确表示需要登录
        is_ck_invalid = (error_code == 203 and "请先去登录" in error_message)

        # 如果没有检测到，也检查原始响应中的错误信息
        if not is_ck_invalid:
            # 检查raw_response中的错误信息
            raw_response = api_response_data.get("raw_response", {})
            if raw_response:
                raw_error_code = raw_response.get("error", {}).get("errorcode")
                raw_error_message = (
                    raw_response.get("message")
                    or raw_response.get("msg")
                    or raw_response.get("error", {}).get("message")
                    or ""
                )
                is_ck_invalid = (raw_error_code == 203 and "请先去登录" in raw_error_message)

        if is_ck_invalid:
            logger.warning(f"检测到CK无效错误(需要重新登录) | error_code={error_code} | message={error_message}")
        else:
            logger.debug(f"未检测到CK失效错误 | error_code={error_code} | message={error_message}")

        return is_ck_invalid

    def _is_ck_disable_error(self, api_response_data: Dict[str, Any], error_message: str) -> bool:
        """
        判断是否是需要禁用CK的错误（错误次数过多、数据异常）

        Args:
            api_response_data: API响应数据
            error_message: 错误消息

        Returns:
            bool: 是否是需要禁用CK的错误
        """
        if not api_response_data:
            return False

        # 检查错误码110134（错误次数过多）和110444（数据异常）
        error_code = api_response_data.get("error", {}).get("errorcode")

        # 检查原始响应中的错误信息
        raw_response = api_response_data.get("raw_response", {})
        if raw_response:
            raw_error_code = raw_response.get("error", {}).get("errorcode")
            if raw_error_code:
                error_code = raw_error_code

        # 检查是否是需要禁用CK的错误码
        is_disable_error = (
            (error_code == 110134) or
            (error_code == 110444) or
            (error_code == 200)  # 错误码200只需要判断状态码，可能有多种错误信息
        )

        if is_disable_error:
            logger.warning(f"检测到需要禁用CK的错误 | error_code={error_code} | message={error_message}")

        return is_disable_error

    def _get_ck_disable_error_type(self, api_response_data: Dict[str, Any], error_message: str) -> str:
        """
        获取需要禁用CK的错误类型

        Args:
            api_response_data: API响应数据
            error_message: 错误消息

        Returns:
            str: 错误类型描述
        """
        error_code = api_response_data.get("error", {}).get("errorcode")

        # 检查原始响应中的错误信息
        raw_response = api_response_data.get("raw_response", {})
        if raw_response:
            raw_error_code = raw_response.get("error", {}).get("errorcode")
            if raw_error_code:
                error_code = raw_error_code

        if error_code == 110134:
            return error_message
        elif error_code == 110444:
            return error_message
        elif error_code == 200:
            return error_message  # 错误码200只需要判断状态码，可能有多种错误信息
        else:
            return "未知CK错误:"+error_message

    def _is_daily_limit_error(self, raw_response: Dict[str, Any], error_message: str) -> bool:
        """
        检测是否是单日绑卡限制错误

        检查条件：
        1. 错误码是110224
        2. 错误信息包含"您绑卡已超过单日20张限制，请明天再试"
        """
        # 检查错误码
        error_code = None
        if raw_response:
            error_info = raw_response.get("error", {})
            error_code = error_info.get("errorcode")

        # 检查错误信息
        daily_limit_keywords = [
            "您绑卡已超过单日20张限制",
            "单日20张限制",
            "请明天再试"
        ]

        # 条件1：错误码是110224
        is_error_code_match = (error_code == 110224)

        # 条件2：错误信息包含关键词
        is_message_match = any(keyword in error_message for keyword in daily_limit_keywords)

        # 同时满足两个条件才认为是单日限制错误
        is_daily_limit = is_error_code_match and is_message_match

        if is_daily_limit:
            logger.warning(
                f"检测到单日绑卡限制错误 | error_code={error_code} | message={error_message}"
            )
        else:
            logger.debug(
                f"未检测到单日绑卡限制错误 | error_code={error_code} | message={error_message}"
            )

        return is_daily_limit

    def _is_server_busy_error(self, raw_response: Dict[str, Any], error_message: str) -> bool:
        """
        检测是否是服务器繁忙错误

        检查条件：
        1. 错误码是200（只需要判断状态码，因为可能有多种错误信息）
        """
        # 检查错误码
        error_code = None
        if raw_response:
            error_info = raw_response.get("error", {})
            error_code = error_info.get("errorcode")

        # 只需要判断错误码是200，因为可能有多种错误信息
        is_server_busy = (error_code == 200)

        if is_server_busy:
            logger.warning(
                f"检测到错误码200，需要禁用CK并重试 | error_code={error_code} | message={error_message}"
            )
        else:
            logger.debug(
                f"未检测到错误码200 | error_code={error_code} | message={error_message}"
            )

        return is_server_busy

    async def _log_non_ck_error(
        self,
        db: Session,
        walmart_ck: WalmartCK,
        record,
        error_message: str,
    ):
        """记录非CK失效的错误"""
        # 【安全修复】创建BindingLogService实例
        binding_log_service = BindingLogService(db)
        await binding_log_service.log_error(
            db=db,
            card_record_id=str(record.id),
            error_message=f"绑卡失败(非CK失效): {error_message}",
            details={
                "walmart_ck_id": str(walmart_ck.id),
                "error_type": "business_failure",
                "is_ck_invalid": False,
            },
            walmart_ck_id=str(walmart_ck.id),
        )
