#!/usr/bin/env python3
"""
CK负载均衡修复测试脚本
用于验证负载均衡问题的修复效果
"""

import os
import sys
import asyncio
import time
from collections import Counter
from typing import Dict, List, Any, Optional

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.insert(0, project_root)

from app.core.database import get_db
from app.models.walmart_ck import WalmartCK
from app.services.redis_ck_wrapper import create_optimized_ck_service
from app.core.logging import get_logger
from app.core.redis import get_redis

logger = get_logger("ck_load_balance_test")


class CKLoadBalanceTestSuite:
    """CK负载均衡测试套件"""
    
    def __init__(self):
        self.db = next(get_db())
        self.redis_client = None
        
    async def __aenter__(self):
        """异步上下文管理器入口"""
        try:
            self.redis_client = await get_redis()
        except Exception as e:
            logger.warning(f"Redis连接失败: {e}")
            self.redis_client = None
        return self
        
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器出口"""
        if self.db:
            self.db.close()
        if self.redis_client:
            await self.redis_client.close()
    
    async def run_comprehensive_test(self, merchant_id: Optional[int] = None) -> Dict[str, Any]:
        """运行综合负载均衡测试"""
        logger.info("开始CK负载均衡综合测试")
        
        test_results = {
            "test_timestamp": time.time(),
            "merchant_id": merchant_id,
            "pre_test_analysis": {},
            "load_balance_tests": {},
            "post_test_analysis": {},
            "recommendations": []
        }
        
        try:
            # 1. 预测试分析
            test_results["pre_test_analysis"] = await self._analyze_ck_state(merchant_id)
            
            # 2. 执行负载均衡测试
            test_results["load_balance_tests"] = await self._run_load_balance_tests(merchant_id)
            
            # 3. 后测试分析
            test_results["post_test_analysis"] = await self._analyze_ck_state(merchant_id)
            
            # 4. 生成建议
            test_results["recommendations"] = self._generate_recommendations(test_results)
            
            # 5. 清理测试数据
            await self._cleanup_test_data(merchant_id)
            
        except Exception as e:
            logger.error(f"综合测试失败: {e}")
            test_results["error"] = str(e)
        
        return test_results
    
    async def _analyze_ck_state(self, merchant_id: Optional[int]) -> Dict[str, Any]:
        """分析CK状态"""
        analysis = {
            "available_cks": [],
            "total_count": 0,
            "active_count": 0,
            "redis_pool_status": {},
            "load_distribution": {}
        }
        
        try:
            # 查询可用CK
            query = self.db.query(WalmartCK).filter(WalmartCK.is_deleted == False)
            if merchant_id:
                query = query.filter(WalmartCK.merchant_id == merchant_id)
            
            all_cks = query.all()
            active_cks = [ck for ck in all_cks if ck.active]
            
            analysis["total_count"] = len(all_cks)
            analysis["active_count"] = len(active_cks)
            
            for ck in active_cks:
                analysis["available_cks"].append({
                    "id": ck.id,
                    "merchant_id": ck.merchant_id,
                    "department_id": ck.department_id,
                    "bind_count": ck.bind_count,
                    "total_limit": ck.total_limit,
                    "usage_ratio": ck.bind_count / ck.total_limit if ck.total_limit > 0 else 0
                })
            
            # 分析Redis池状态
            if self.redis_client and merchant_id:
                analysis["redis_pool_status"] = await self._analyze_redis_pool(merchant_id)
            
        except Exception as e:
            logger.error(f"CK状态分析失败: {e}")
            analysis["error"] = str(e)
        
        return analysis
    
    async def _analyze_redis_pool(self, merchant_id: int) -> Dict[str, Any]:
        """分析Redis池状态"""
        pool_status = {
            "pool_exists": False,
            "pool_size": 0,
            "ck_scores": {},
            "pending_counts": {}
        }
        
        try:
            pool_key = f"walmart:ck:pool:{merchant_id}"
            
            # 检查池是否存在
            pool_exists = await self.redis_client.exists(pool_key)
            pool_status["pool_exists"] = bool(pool_exists)
            
            if pool_exists:
                # 获取池大小
                pool_status["pool_size"] = await self.redis_client.zcard(pool_key)
                
                # 获取所有CK的分数
                members = await self.redis_client.zrange(pool_key, 0, -1, withscores=True)
                for member, score in members:
                    ck_id = member.decode() if isinstance(member, bytes) else str(member)
                    pool_status["ck_scores"][ck_id] = score
                    
                    # 获取pending_count
                    status_key = f"walmart:ck:status:{ck_id}"
                    pending_count = await self.redis_client.hget(status_key, 'pending_count')
                    pool_status["pending_counts"][ck_id] = int(pending_count) if pending_count else 0
        
        except Exception as e:
            logger.error(f"Redis池分析失败: {e}")
            pool_status["error"] = str(e)
        
        return pool_status
    
    async def _run_load_balance_tests(self, merchant_id: Optional[int]) -> Dict[str, Any]:
        """运行负载均衡测试"""
        tests = {
            "basic_test": await self._basic_load_balance_test(merchant_id),
            "concurrent_test": await self._concurrent_load_balance_test(merchant_id),
            "stress_test": await self._stress_load_balance_test(merchant_id)
        }
        
        return tests
    
    async def _basic_load_balance_test(self, merchant_id: Optional[int]) -> Dict[str, Any]:
        """基础负载均衡测试"""
        test_result = {
            "test_name": "基础负载均衡测试",
            "test_rounds": 20,
            "selected_cks": [],
            "selection_details": [],
            "unique_ck_count": 0,
            "distribution": {},
            "is_balanced": False,
            "balance_score": 0
        }
        
        try:
            if not merchant_id:
                # 找一个有CK的商户
                ck = self.db.query(WalmartCK).filter(
                    WalmartCK.active == True,
                    WalmartCK.is_deleted == False
                ).first()
                if ck:
                    merchant_id = ck.merchant_id
                else:
                    test_result["error"] = "没有找到可用的CK进行测试"
                    return test_result
            
            ck_service = create_optimized_ck_service(self.db)
            
            # 进行多轮CK选择测试
            for i in range(test_result["test_rounds"]):
                logger.info(f"基础测试第 {i+1} 轮")
                
                start_time = time.time()
                ck = await ck_service.get_available_ck(merchant_id)
                selection_time = time.time() - start_time
                
                if ck:
                    test_result["selected_cks"].append(ck.id)
                    test_result["selection_details"].append({
                        "round": i + 1,
                        "ck_id": ck.id,
                        "bind_count": ck.bind_count,
                        "selection_time": selection_time
                    })
                    
                    # 立即释放CK
                    try:
                        await ck_service.record_ck_usage(
                            ck_id=ck.id,
                            merchant_id=merchant_id,
                            success=False  # 测试用途
                        )
                        logger.debug(f"基础测试轮次 {i+1}: 选择并释放CK {ck.id}")
                    except Exception as e:
                        logger.warning(f"释放CK {ck.id} 失败: {e}")
                else:
                    test_result["selected_cks"].append(None)
                    logger.warning(f"基础测试轮次 {i+1}: 未找到可用CK")
                
                # 添加小延迟
                await asyncio.sleep(0.05)
            
            # 分析结果
            valid_selections = [ck_id for ck_id in test_result["selected_cks"] if ck_id is not None]
            test_result["unique_ck_count"] = len(set(valid_selections))
            test_result["distribution"] = dict(Counter(valid_selections))
            
            # 计算均衡分数
            if len(valid_selections) > 0:
                distribution_values = list(test_result["distribution"].values())
                max_count = max(distribution_values)
                min_count = min(distribution_values)
                test_result["balance_score"] = (1 - (max_count - min_count) / max_count) * 100
                test_result["is_balanced"] = test_result["balance_score"] > 70  # 70分以上认为均衡
            
        except Exception as e:
            logger.error(f"基础负载均衡测试失败: {e}")
            test_result["error"] = str(e)
        
        return test_result
    
    async def _concurrent_load_balance_test(self, merchant_id: Optional[int]) -> Dict[str, Any]:
        """并发负载均衡测试"""
        test_result = {
            "test_name": "并发负载均衡测试",
            "concurrent_requests": 10,
            "selected_cks": [],
            "unique_ck_count": 0,
            "distribution": {},
            "is_balanced": False
        }
        
        try:
            if not merchant_id:
                ck = self.db.query(WalmartCK).filter(
                    WalmartCK.active == True,
                    WalmartCK.is_deleted == False
                ).first()
                if ck:
                    merchant_id = ck.merchant_id
                else:
                    test_result["error"] = "没有找到可用的CK进行测试"
                    return test_result
            
            # 创建多个并发任务
            async def select_and_release_ck(task_id: int):
                ck_service = create_optimized_ck_service(self.db)
                ck = await ck_service.get_available_ck(merchant_id)
                if ck:
                    # 立即释放
                    await ck_service.record_ck_usage(
                        ck_id=ck.id,
                        merchant_id=merchant_id,
                        success=False
                    )
                    return ck.id
                return None
            
            # 并发执行
            tasks = [select_and_release_ck(i) for i in range(test_result["concurrent_requests"])]
            results = await asyncio.gather(*tasks, return_exceptions=True)
            
            # 处理结果
            for result in results:
                if isinstance(result, Exception):
                    logger.error(f"并发任务失败: {result}")
                    test_result["selected_cks"].append(None)
                else:
                    test_result["selected_cks"].append(result)
            
            # 分析结果
            valid_selections = [ck_id for ck_id in test_result["selected_cks"] if ck_id is not None]
            test_result["unique_ck_count"] = len(set(valid_selections))
            test_result["distribution"] = dict(Counter(valid_selections))
            test_result["is_balanced"] = test_result["unique_ck_count"] > 1
            
        except Exception as e:
            logger.error(f"并发负载均衡测试失败: {e}")
            test_result["error"] = str(e)
        
        return test_result
    
    async def _stress_load_balance_test(self, merchant_id: Optional[int]) -> Dict[str, Any]:
        """压力负载均衡测试"""
        test_result = {
            "test_name": "压力负载均衡测试",
            "test_rounds": 50,
            "selected_cks": [],
            "unique_ck_count": 0,
            "distribution": {},
            "avg_selection_time": 0,
            "is_balanced": False
        }
        
        try:
            if not merchant_id:
                ck = self.db.query(WalmartCK).filter(
                    WalmartCK.active == True,
                    WalmartCK.is_deleted == False
                ).first()
                if ck:
                    merchant_id = ck.merchant_id
                else:
                    test_result["error"] = "没有找到可用的CK进行测试"
                    return test_result
            
            ck_service = create_optimized_ck_service(self.db)
            selection_times = []
            
            # 快速连续选择测试
            for i in range(test_result["test_rounds"]):
                start_time = time.time()
                ck = await ck_service.get_available_ck(merchant_id)
                selection_time = time.time() - start_time
                selection_times.append(selection_time)
                
                if ck:
                    test_result["selected_cks"].append(ck.id)
                    # 立即释放
                    await ck_service.record_ck_usage(
                        ck_id=ck.id,
                        merchant_id=merchant_id,
                        success=False
                    )
                else:
                    test_result["selected_cks"].append(None)
            
            # 分析结果
            valid_selections = [ck_id for ck_id in test_result["selected_cks"] if ck_id is not None]
            test_result["unique_ck_count"] = len(set(valid_selections))
            test_result["distribution"] = dict(Counter(valid_selections))
            test_result["avg_selection_time"] = sum(selection_times) / len(selection_times)
            test_result["is_balanced"] = test_result["unique_ck_count"] > 1
            
        except Exception as e:
            logger.error(f"压力负载均衡测试失败: {e}")
            test_result["error"] = str(e)
        
        return test_result
    
    async def _cleanup_test_data(self, merchant_id: Optional[int]):
        """清理测试数据"""
        try:
            if self.redis_client and merchant_id:
                # 清理可能残留的锁
                pattern = f"walmart:ck:lock:*"
                async for key in self.redis_client.scan_iter(match=pattern):
                    await self.redis_client.delete(key)
                logger.info("清理测试锁数据完成")
        except Exception as e:
            logger.warning(f"清理测试数据失败: {e}")
    
    def _generate_recommendations(self, test_results: Dict[str, Any]) -> List[str]:
        """生成建议"""
        recommendations = []
        
        try:
            pre_analysis = test_results.get("pre_test_analysis", {})
            load_tests = test_results.get("load_balance_tests", {})
            
            # 检查可用CK数量
            available_count = pre_analysis.get("active_count", 0)
            if available_count <= 1:
                recommendations.append(f"建议增加CK数量，当前只有 {available_count} 个可用CK")
            
            # 检查基础测试结果
            basic_test = load_tests.get("basic_test", {})
            if basic_test.get("is_balanced", False):
                recommendations.append("✅ 基础负载均衡测试通过")
            else:
                balance_score = basic_test.get("balance_score", 0)
                recommendations.append(f"❌ 基础负载均衡测试失败，均衡分数: {balance_score:.1f}")
            
            # 检查并发测试结果
            concurrent_test = load_tests.get("concurrent_test", {})
            if concurrent_test.get("is_balanced", False):
                recommendations.append("✅ 并发负载均衡测试通过")
            else:
                recommendations.append("❌ 并发负载均衡测试失败")
            
            # 检查压力测试结果
            stress_test = load_tests.get("stress_test", {})
            avg_time = stress_test.get("avg_selection_time", 0)
            if avg_time > 0.1:
                recommendations.append(f"⚠️ CK选择平均耗时较长: {avg_time:.3f}秒")
            
        except Exception as e:
            logger.error(f"生成建议失败: {e}")
            recommendations.append("建议生成失败，请检查日志")
        
        return recommendations


async def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description="CK负载均衡修复测试")
    parser.add_argument("--merchant-id", type=int, help="指定商户ID")
    parser.add_argument("--verbose", action="store_true", help="详细输出")
    
    args = parser.parse_args()
    
    if args.verbose:
        import logging
        logging.getLogger().setLevel(logging.DEBUG)
    
    async with CKLoadBalanceTestSuite() as test_suite:
        results = await test_suite.run_comprehensive_test(args.merchant_id)
        
        print("\n" + "="*80)
        print("CK负载均衡测试结果")
        print("="*80)
        
        print(f"测试商户: {results['merchant_id']}")
        print(f"可用CK数量: {results['pre_test_analysis'].get('active_count', 0)}")
        
        # 基础测试结果
        basic_test = results['load_balance_tests'].get('basic_test', {})
        print(f"\n基础测试:")
        print(f"  - 测试轮数: {basic_test.get('test_rounds', 0)}")
        print(f"  - 选择的不同CK数: {basic_test.get('unique_ck_count', 0)}")
        print(f"  - 均衡分数: {basic_test.get('balance_score', 0):.1f}")
        print(f"  - 是否均衡: {'✅' if basic_test.get('is_balanced', False) else '❌'}")
        
        if basic_test.get('distribution'):
            print(f"  - 分布情况: {basic_test['distribution']}")
        
        # 并发测试结果
        concurrent_test = results['load_balance_tests'].get('concurrent_test', {})
        print(f"\n并发测试:")
        print(f"  - 并发请求数: {concurrent_test.get('concurrent_requests', 0)}")
        print(f"  - 选择的不同CK数: {concurrent_test.get('unique_ck_count', 0)}")
        print(f"  - 是否均衡: {'✅' if concurrent_test.get('is_balanced', False) else '❌'}")
        
        # 建议
        print(f"\n建议:")
        for rec in results.get('recommendations', []):
            print(f"  - {rec}")
        
        print("\n" + "="*80)


if __name__ == "__main__":
    asyncio.run(main())
