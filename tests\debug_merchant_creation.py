#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试商户创建API
"""

import sys
import os
import json
import time

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(__file__)))

from test.conftest import TestBase


def debug_merchant_creation():
    """调试商户创建API"""
    api_test = TestBase()
    
    # 管理员登录
    admin_token = api_test.login("admin", "7c222fb2927d828af22f592134e8932480637c0d")
    if not admin_token:
        print("❌ 管理员登录失败")
        return
    
    print("✅ 管理员登录成功")
    
    # 查看现有商户
    print("\n=== 查看现有商户 ===")
    status_code, response = api_test.make_request("GET", "/merchants?page_size=10", admin_token)
    print(f"获取商户列表状态码: {status_code}")
    print(f"响应: {json.dumps(response, indent=2, ensure_ascii=False)}")
    
    # 测试创建商户
    print("\n=== 测试创建商户 ===")
    timestamp = str(int(time.time()))
    
    merchant_data = {
        "name": f"调试测试商户_{timestamp}",
        "code": f"debug_merchant_{timestamp}",
        "api_key": f"debug_key_{timestamp}",
        "api_secret": f"debug_secret_{timestamp}",
        "status": True,
        "daily_limit": 1000,
        "hourly_limit": 100,
        "concurrency_limit": 10,
        "priority": 0,
        "request_timeout": 30,
        "retry_count": 3
    }
    
    print(f"商户数据: {json.dumps(merchant_data, indent=2, ensure_ascii=False)}")
    
    status_code, response = api_test.make_request(
        "POST", "/merchants", admin_token, data=merchant_data
    )
    
    print(f"创建商户状态码: {status_code}")
    print(f"创建商户响应: {json.dumps(response, indent=2, ensure_ascii=False)}")
    
    if status_code == 200:
        # 提取商户ID并清理
        data = response.get("data", {})
        if data.get("success") and data.get("data"):
            merchant_id = data["data"].get("id")
            if merchant_id:
                print(f"✅ 成功创建商户，ID: {merchant_id}")
                
                # 清理测试商户
                delete_status, delete_response = api_test.make_request(
                    "DELETE", f"/merchants/{merchant_id}", admin_token
                )
                if delete_status == 200:
                    print("✅ 测试商户已清理")
                else:
                    print(f"⚠️ 清理测试商户失败: {delete_response}")
    else:
        print("❌ 创建商户失败")
        
        # 尝试不同的字段组合
        print("\n=== 尝试简化的商户数据 ===")
        simple_merchant_data = {
            "name": f"简单测试商户_{timestamp}",
            "code": f"simple_merchant_{timestamp}",
            "api_key": f"simple_key_{timestamp}",
            "api_secret": f"simple_secret_{timestamp}",
            "status": True
        }
        
        print(f"简化商户数据: {json.dumps(simple_merchant_data, indent=2, ensure_ascii=False)}")
        
        status_code2, response2 = api_test.make_request(
            "POST", "/merchants", admin_token, data=simple_merchant_data
        )
        
        print(f"简化创建状态码: {status_code2}")
        print(f"简化创建响应: {json.dumps(response2, indent=2, ensure_ascii=False)}")


if __name__ == "__main__":
    debug_merchant_creation()
