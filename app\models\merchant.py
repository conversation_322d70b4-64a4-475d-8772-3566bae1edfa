from sqlalchemy import (
    Column,
    String,
    Integer,
    BigInteger,
    Boolean,
    Text,
    DateTime,
)
from sqlalchemy.orm import relationship

from app.models.base import BaseModel, TimestampMixin


class Merchant(BaseModel, TimestampMixin):
    """商户模型 - 企业/公司表"""

    __tablename__ = "merchants"
    __table_args__ = ({"extend_existing": True},)

    id = Column(BigInteger, primary_key=True, index=True, autoincrement=True, comment="商户ID")
    name = Column(String(100), nullable=False, comment="商户名称")
    code = Column(String(50), nullable=True, unique=True, comment="商户代码")
    api_key = Column(String(64), nullable=False, unique=True, comment="API密钥")
    api_secret = Column(String(128), nullable=False, comment="API密钥密文")
    status = Column(Boolean, nullable=False, default=True, comment="状态：1启用，0禁用")
    callback_url = Column(String(255), nullable=True, comment="回调URL")
    allowed_ips = Column(Text, nullable=True, comment="IP白名单")
    daily_limit = Column(Integer, nullable=False, default=10000, comment="每日绑卡上限")
    hourly_limit = Column(Integer, nullable=False, default=1000, comment="每小时绑卡上限")
    concurrency_limit = Column(Integer, nullable=False, default=100, comment="最大并发数")
    priority = Column(Integer, nullable=False, default=0, comment="处理优先级")
    request_timeout = Column(Integer, nullable=False, default=30, comment="请求超时时间")
    retry_count = Column(Integer, nullable=False, default=3, comment="重试次数")
    contact_name = Column(String(100), nullable=True, comment="联系人")
    contact_phone = Column(String(50), nullable=True, comment="联系电话")
    contact_email = Column(String(100), nullable=True, comment="联系邮箱")
    remark = Column(Text, nullable=True, comment="备注")
    custom_config = Column(Text, nullable=True, comment="自定义配置")
    created_by = Column(BigInteger, nullable=True, comment="创建者ID")
    api_key_updated_at = Column(DateTime(timezone=True), nullable=True, comment="API密钥更新时间")
    api_secret_updated_at = Column(DateTime(timezone=True), nullable=True, comment="API密文更新时间")

    # 关联关系
    departments = relationship(
        "Department",
        back_populates="merchant",
        cascade="all, delete-orphan",
        lazy="dynamic"
    )
    users = relationship(
        "User",
        back_populates="merchant",
        foreign_keys="[User.merchant_id]",
        lazy="dynamic"
    )
    user_organizations = relationship(
        "UserOrganization",
        back_populates="merchant",
        cascade="all, delete-orphan",
        lazy="dynamic"
    )
    walmart_cks = relationship(
        "WalmartCK",
        back_populates="merchant",
        cascade="all, delete-orphan",
        lazy="dynamic"
    )
    card_records = relationship(
        "CardRecord",
        back_populates="merchant",
        lazy="dynamic"
    )
    ip_whitelist = relationship(
        "IpWhitelist",
        back_populates="merchant",
        cascade="all, delete-orphan",
        lazy="dynamic"
    )
    telegram_groups = relationship(
        "TelegramGroup",
        back_populates="merchant",
        cascade="all, delete-orphan",
        lazy="dynamic"
    )
    telegram_setting = relationship(
        "MerchantTelegramSetting",
        back_populates="merchant",
        uselist=False,
        cascade="all, delete-orphan"
    )

    # Optional: Define relationship back to the creator User if needed
    # creator = relationship("User", foreign_keys=[created_by])

    # __repr__ method if needed
    # def __repr__(self):
    #     return f"<Merchant(id={self.id}, name='{self.name}')>"

    def to_dict(self):
        """转换为字典，隐藏敏感信息"""
        data = super().to_dict()
        # 隐藏敏感信息
        data.pop("api_secret", None)
        return data

    def validate_api_auth(self, api_key, signature, timestamp, nonce, data):
        """验证API调用的签名

        Args:
            api_key: API密钥
            signature: 签名
            timestamp: 时间戳
            nonce: 随机字符串
            data: 请求数据

        Returns:
            bool: 验证是否通过
        """
        if self.api_key != api_key:
            return False

        # TODO: 实现签名验证逻辑
        # 1. 检查timestamp是否在有效期内
        # 2. 检查nonce是否已使用
        # 3. 校验签名

        return True

    def get_department_count(self) -> int:
        """获取部门数量"""
        return self.departments.count()

    def get_user_count(self) -> int:
        """获取用户数量"""
        return self.users.count()

    def get_root_department(self):
        """获取根部门"""
        return self.departments.filter_by(code="ROOT", parent_id=None).first()

    def get_departments_tree(self) -> list:
        """获取部门树形结构"""
        root_dept = self.get_root_department()
        if root_dept:
            return [root_dept.get_tree()]
        return []

    def can_bind_card(self) -> bool:
        """检查是否可以绑卡"""
        return self.status and self.api_key and self.api_secret
