<template>
  <div class="telegram-config">
    <el-card>
      <template #header>
        <div class="card-header">
          <span>Telegram机器人配置管理</span>
          <div class="header-actions">
            <el-button type="primary" :loading="loading" @click="handleSave">
              保存配置
            </el-button>
            <el-button type="info" :loading="loading" @click="handleValidate">
              验证配置
            </el-button>
            <el-button :loading="loading" @click="handleReset">
              重置
            </el-button>
          </div>
        </div>
      </template>

      <el-form ref="configFormRef" :model="configForm" :rules="configRules" label-width="180px"
        v-loading="telegramStore.loading.globalConfig">
        <!-- 基础配置 -->
        <el-divider content-position="left">基础配置</el-divider>

        <el-form-item label="Bot Token" prop="bot_token">
          <el-input v-model="configForm.bot_token" type="password" placeholder="请输入Telegram Bot Token" show-password
            clearable />
          <div class="form-tip">
            从 @BotFather 获取的机器人令牌
          </div>
        </el-form-item>

        <el-form-item label="Webhook URL" prop="webhook_url">
          <el-input v-model="configForm.webhook_url" placeholder="https://your-domain.com/api/v1/telegram/webhook"
            clearable />
          <div class="form-tip">
            接收Telegram更新的Webhook地址
          </div>
        </el-form-item>

        <el-form-item label="Webhook Secret" prop="webhook_secret">
          <el-input v-model="configForm.webhook_secret" type="password" placeholder="请输入Webhook密钥" show-password
            clearable />
          <div class="form-tip">
            用于验证Webhook请求的密钥
          </div>
        </el-form-item>

        <!-- 频率限制配置 -->
        <el-divider content-position="left">频率限制配置</el-divider>

        <el-form-item label="全局每小时限制" prop="rate_limit_global">
          <el-input-number v-model="configForm.rate_limit_global" :min="1" :max="10000" controls-position="right" />
          <div class="form-tip">
            全局每小时最大请求数量
          </div>
        </el-form-item>

        <el-form-item label="群组每小时限制" prop="rate_limit_group">
          <el-input-number v-model="configForm.rate_limit_group" :min="1" :max="1000" controls-position="right" />
          <div class="form-tip">
            单个群组每小时最大请求数量
          </div>
        </el-form-item>

        <el-form-item label="用户每小时限制" prop="rate_limit_user">
          <el-input-number v-model="configForm.rate_limit_user" :min="1" :max="500" controls-position="right" />
          <div class="form-tip">
            单个用户每小时最大请求数量
          </div>
        </el-form-item>

        <!-- 安全配置 -->
        <el-divider content-position="left">安全配置</el-divider>

        <el-form-item label="绑定令牌有效期" prop="bind_token_expire_hours">
          <el-input-number v-model="configForm.bind_token_expire_hours" :min="1" :max="168" controls-position="right" />
          <span class="unit">小时</span>
          <div class="form-tip">
            群组绑定令牌的有效期
          </div>
        </el-form-item>

        <el-form-item label="验证令牌有效期" prop="verification_token_expire_minutes">
          <el-input-number v-model="configForm.verification_token_expire_minutes" :min="5" :max="120"
            controls-position="right" />
          <span class="unit">分钟</span>
          <div class="form-tip">
            用户验证令牌的有效期
          </div>
        </el-form-item>

        <el-form-item label="每日最大绑定尝试" prop="max_bind_attempts_per_day">
          <el-input-number v-model="configForm.max_bind_attempts_per_day" :min="1" :max="50"
            controls-position="right" />
          <div class="form-tip">
            每个用户每日最大绑定尝试次数
          </div>
        </el-form-item>

        <!-- 功能开关 -->
        <el-divider content-position="left">功能开关</el-divider>

        <el-form-item label="启用审计日志">
          <el-switch v-model="configForm.enable_audit_log" active-text="开启" inactive-text="关闭" />
          <div class="form-tip">
            记录所有操作的审计日志
          </div>
        </el-form-item>

        <el-form-item label="脱敏敏感数据">
          <el-switch v-model="configForm.mask_sensitive_data" active-text="开启" inactive-text="关闭" />
          <div class="form-tip">
            在日志中脱敏显示敏感信息
          </div>
        </el-form-item>

        <!-- 默认设置 -->
        <el-divider content-position="left">默认设置</el-divider>

        <el-form-item label="默认时区" prop="default_timezone">
          <el-select v-model="configForm.default_timezone" placeholder="请选择时区" filterable>
            <el-option v-for="timezone in timezoneOptions" :key="timezone.value" :label="timezone.label"
              :value="timezone.value" />
          </el-select>
        </el-form-item>

        <el-form-item label="默认语言" prop="default_language">
          <el-select v-model="configForm.default_language" placeholder="请选择语言">
            <el-option label="简体中文" value="zh-CN" />
            <el-option label="繁体中文" value="zh-TW" />
            <el-option label="English" value="en-US" />
          </el-select>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 配置验证结果对话框 -->
    <el-dialog v-model="validationDialogVisible" title="配置验证结果" width="600px">
      <div v-if="validationResult">
        <el-alert :type="validationResult.valid ? 'success' : 'error'"
          :title="validationResult.valid ? '配置验证通过' : '配置验证失败'" show-icon :closable="false" />

        <div v-if="validationResult.errors && validationResult.errors.length > 0" class="validation-errors">
          <h4>错误详情：</h4>
          <ul>
            <li v-for="error in validationResult.errors" :key="error">
              {{ error }}
            </li>
          </ul>
        </div>

        <div v-if="validationResult.warnings && validationResult.warnings.length > 0" class="validation-warnings">
          <h4>警告信息：</h4>
          <ul>
            <li v-for="warning in validationResult.warnings" :key="warning">
              {{ warning }}
            </li>
          </ul>
        </div>
      </div>

      <template #footer>
        <el-button @click="validationDialogVisible = false">关闭</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useTelegramStore } from '@/store/modules/telegram'
import { telegramApi } from '@/api'

const telegramStore = useTelegramStore()
const configFormRef = ref()
const loading = ref(false)
const validationDialogVisible = ref(false)
const validationResult = ref(null)

// 表单数据
const configForm = reactive({
  bot_token: '',
  webhook_url: '',
  webhook_secret: '',
  rate_limit_global: 1000,
  rate_limit_group: 100,
  rate_limit_user: 50,
  bind_token_expire_hours: 24,
  verification_token_expire_minutes: 30,
  max_bind_attempts_per_day: 5,
  enable_audit_log: true,
  mask_sensitive_data: true,
  default_timezone: 'Asia/Shanghai',
  default_language: 'zh-CN'
})

// 表单验证规则
const configRules = {
  bot_token: [
    { required: true, message: '请输入Bot Token', trigger: 'blur' },
    { min: 40, message: 'Bot Token长度不能少于40个字符', trigger: 'blur' }
  ],
  webhook_url: [
    { required: true, message: '请输入Webhook URL', trigger: 'blur' },
    { type: 'url', message: '请输入有效的URL地址', trigger: 'blur' }
  ],
  webhook_secret: [
    { required: true, message: '请输入Webhook Secret', trigger: 'blur' },
    { min: 8, message: 'Webhook Secret长度不能少于8个字符', trigger: 'blur' }
  ]
}

// 时区选项
const timezoneOptions = [
  { label: 'Asia/Shanghai (UTC+8)', value: 'Asia/Shanghai' },
  { label: 'Asia/Tokyo (UTC+9)', value: 'Asia/Tokyo' },
  { label: 'Asia/Seoul (UTC+9)', value: 'Asia/Seoul' },
  { label: 'Asia/Hong_Kong (UTC+8)', value: 'Asia/Hong_Kong' },
  { label: 'Asia/Singapore (UTC+8)', value: 'Asia/Singapore' },
  { label: 'UTC (UTC+0)', value: 'UTC' },
  { label: 'America/New_York (UTC-5)', value: 'America/New_York' },
  { label: 'Europe/London (UTC+0)', value: 'Europe/London' }
]

// 保存配置
const handleSave = async () => {
  try {
    await configFormRef.value.validate()

    loading.value = true
    await telegramStore.updateGlobalConfig(configForm)
    ElMessage.success('配置保存成功')
  } catch (error) {
    if (error.fields) {
      ElMessage.error('请检查表单输入')
      return
    }
    ElMessage.error('保存失败：' + error.message)
  } finally {
    loading.value = false
  }
}

// 验证配置
const handleValidate = async () => {
  try {
    await configFormRef.value.validate()

    loading.value = true
    const response = await telegramStore.validateConfig(configForm)
    validationResult.value = response.data || response
    validationDialogVisible.value = true
  } catch (error) {
    if (error.fields) {
      ElMessage.error('请检查表单输入')
      return
    }
    ElMessage.error('验证失败：' + error.message)
  } finally {
    loading.value = false
  }
}

// 重置配置
const handleReset = async () => {
  try {
    await ElMessageBox.confirm('确定要重置配置吗？这将恢复到上次保存的状态。', '确认操作', {
      type: 'warning'
    })

    await loadConfig()
    ElMessage.success('配置已重置')
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('重置失败：' + error.message)
    }
  }
}

// 加载配置
const loadConfig = async () => {
  try {
    await telegramStore.fetchGlobalConfig()
    Object.assign(configForm, telegramStore.globalConfig)
  } catch (error) {
    ElMessage.error('加载配置失败：' + error.message)
  }
}

// 初始化
onMounted(() => {
  loadConfig()
})
</script>

<style scoped>
.telegram-config {
  padding: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-actions {
  display: flex;
  gap: 12px;
}

.form-tip {
  font-size: 12px;
  color: #999;
  margin-top: 4px;
}

.unit {
  margin-left: 8px;
  color: #666;
}

.validation-errors,
.validation-warnings {
  margin-top: 16px;
}

.validation-errors h4 {
  color: #F56C6C;
  margin-bottom: 8px;
}

.validation-warnings h4 {
  color: #E6A23C;
  margin-bottom: 8px;
}

.validation-errors ul,
.validation-warnings ul {
  margin: 0;
  padding-left: 20px;
}

.validation-errors li,
.validation-warnings li {
  margin-bottom: 4px;
}
</style>
