/**
 * API 统一出口
 * 集中导出所有API模块，方便统一引入
 */

// 导入所有API模块
import { authApi } from "./modules/auth";
import { userApi } from "./modules/user";
import { roleApi } from "./modules/role";
import { menuApi } from "./modules/menu";
import { permissionApi } from "./modules/permission";
import { merchantApi } from "./modules/merchant";
import { cardDataApi } from "./modules/cardData";
import { dashboardApi } from "./modules/dashboard";
import { systemApi } from "./modules/system";
import { notificationApi } from "./modules/notification";
import { walmartServerApi } from "./modules/walmartServer";
import { walmartCKApi } from "./modules/walmartCK";
import { notificationConfigApi } from "./modules/notificationConfig";
import { bindingLogsApi } from "./modules/bindingLogs";
import { recoveryApi } from "./modules/recovery";
import { totpApi } from "./modules/totp";
import { systemSettingsApi } from "./modules/systemSettings";
import { telegramApi } from "./modules/telegram";
import { ckMonitorApi } from "./modules/ckMonitor";
import { departmentApi } from "./modules/department";
import { reconciliationApi } from "./modules/reconciliation";

// 导出API配置
export { API_URLS, API_PREFIX, SERVER_URL } from "./modules/config";

// 导出请求工具
export { http } from "./request";

// 导出所有API模块
export {
  authApi,
  userApi,
  roleApi,
  menuApi,
  permissionApi,
  merchantApi,
  cardDataApi,
  dashboardApi,
  systemApi,
  notificationApi,
  walmartServerApi,
  walmartCKApi,
  notificationConfigApi,
  bindingLogsApi,
  recoveryApi,
  totpApi,
  systemSettingsApi,
  telegramApi,
  ckMonitorApi,
  departmentApi,
  reconciliationApi,
};
