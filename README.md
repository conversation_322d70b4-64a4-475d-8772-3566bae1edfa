# Walmart 绑卡管理系统 - 管理端

## 项目简介

Walmart 绑卡管理系统管理端是一个基于 Vue.js 的现代化 Web 应用，用于管理沃尔玛会员卡绑定服务。通过该平台，管理员可以管理商家、用户权限和监控绑卡数据，同时商家可以管理自己的绑卡业务和查看数据分析。

### 主要功能

- **数据仪表盘**：展示绑卡数据统计和趋势分析
- **商家管理**：管理商家信息、配置和 API 密钥
- **用户管理**：支持多级用户权限管理
- **绑卡记录**：查看和搜索绑卡记录，支持数据导出
- **API 配置**：管理 API 接口设置和监控 API 调用情况
- **系统设置**：系统参数配置和操作日志查看

## 技术栈

- **框架**：Vue 3
- **UI 库**：Element Plus
- **状态管理**：Pinia
- **路由**：Vue Router
- **请求处理**：Axios
- **图表**：ECharts
- **类型检查**：TypeScript
- **构建工具**：Vite

## 快速开始

### 环境要求

- Node.js >= 16
- npm >= 8

### 安装与运行

1. **克隆代码库**

```bash
git clone https://github.com/yourusername/walmart-bind-card-admin.git
cd walmart-bind-card-admin
```

2. **安装依赖**

```bash
npm install
```

3. **配置环境变量**

创建 `.env.local` 文件设置 API 地址：

```
VITE_API_BASE_URL=http://localhost:20000
```

4. **开发环境运行**

```bash
npm run dev
```

5. **构建生产版本**

```bash
npm run build
```

6. **预览生产构建**

```bash
npm run preview
```

## 项目结构

```
src/
│── api/                  # API请求模块
│── assets/               # 静态资源
│── components/           # 通用组件
│── composables/          # 组合式函数
│── config/               # 应用配置
│── directives/           # 自定义指令
│── hooks/                # 自定义钩子
│── layouts/              # 布局组件
│── router/               # 路由配置
│── store/                # Pinia状态管理
│── styles/               # 全局样式
│── utils/                # 工具函数
│── views/                # 页面组件
│   ├── dashboard/        # 仪表盘页面
│   ├── merchant/         # 商家管理
│   ├── card-data/        # 绑卡数据
│   ├── user-config/      # 用户配置
│   ├── api-config/       # API配置
│   └── settings/         # 系统设置
├── App.vue               # 根组件
└── main.js               # 应用入口

public/                   # 公共资源
vite.config.js            # Vite配置
package.json              # 项目配置和依赖
```

## 功能模块

### 仪表盘

仪表盘页面展示关键业务数据，包括：

- 今日绑卡统计（总数、成功数、失败数）
- 绑卡趋势图（日/周/月）
- 成功率环形图
- 热门商家排行
- 实时绑卡动态

### 商家管理

商家管理模块提供商家增删改查功能：

- 商家基本信息管理
- API 密钥管理和重置
- 商家状态管理
- 绑卡配额设置
- 接口回调配置
- IP 白名单设置

### 绑卡数据管理

绑卡数据模块提供绑卡记录查询和管理：

- 绑卡记录搜索（按卡号、用户 ID、时间等）
- 绑卡记录详情查看
- 数据统计视图
- 数据导出（Excel 格式）

### 用户管理

用户管理模块支持系统用户管理：

- 用户创建和编辑
- 角色分配
- 权限管理
- 密码重置
- 商家用户关联

### API 配置

API 配置模块允许管理员配置 API 接口：

- API 接口地址配置
- 默认请求头设置
- API 密钥管理
- 接口调用监控

### 系统设置

系统设置模块提供全局配置功能：

- 系统参数设置
- 操作日志查看
- 系统通知设置
- 任务队列监控

## 权限控制

系统实现了完整的 RBAC 权限控制模型：

- **基于角色的权限控制**：

  - 动态菜单：根据用户角色显示不同菜单
  - 按钮权限：根据权限控制按钮显示/隐藏
  - 路由守卫：拦截未授权访问

- **权限指令**

  ```vue
  <button v-permission="'merchant:create'">添加商家</button>
  ```

- **多租户视图**：
  - 超级管理员：可查看全部数据
  - 商家管理员：仅可查看本商家数据

## 系统架构设计优化

### 多租户架构

- 采用多租户 SaaS 架构，每个商家作为独立租户
- 数据存储采用租户 ID 隔离模式，确保跨租户数据不会互相干扰
- 使用分布式缓存存储会话信息，支持水平扩展

### 高并发处理

- 引入消息队列（如 RabbitMQ）处理绑卡请求，避免直接处理高峰时段的大量请求
- 实现请求限流和熔断机制，防止系统过载
- 使用缓存减轻数据库压力，关键数据如商家配置、用户限额等进行缓存
- 实现任务调度系统，按照优先级和配额处理绑卡请求

### 数据隔离设计

- 数据库表设计添加 merchant_id（商家 ID）字段
- 所有查询都必须包含 merchant_id 条件
- 使用数据库视图进一步保障数据隔离
- 实现数据访问层，统一处理 merchant_id，避免开发人员遗漏

## 权限系统设计

### 角色与权限模型

- 设计 RBAC（基于角色的访问控制）模型
- 预设角色：超级管理员、平台运营、商家管理员、商家操作员等
- 细粒度权限控制：页面权限、按钮权限、数据权限
- 权限继承与权限组合机制

### 多级管理体系

- 平台级管理：可查看和管理所有商家数据
- 商家级管理：只能查看和管理自己商家的数据
- 操作员级别：只能执行有限的操作，如查看数据等

## API 安全机制

### 认证与授权

- 实现 JWT（JSON Web Token）认证机制
- API 签名验证：timestamp + nonce + signature
- 商家 API 密钥机制：AppID + AppSecret
- 权限校验中间件，确保 API 调用符合权限要求

### API 保护

- 实现请求频率限制(Rate Limiting)
- IP 白名单机制
- 防重放攻击：通过 nonce(唯一随机数)和 timestamp 验证
- 全站 HTTPS 加密传输

## 前端改进方案

### 权限控制

- 动态路由机制：根据用户角色动态加载可访问路由
- 组件级权限控制：按钮、表单等根据权限显示/隐藏
- 多级菜单权限控制
- 数据权限过滤

### 界面优化

- 多角色界面定制：根据不同角色展示不同界面
- 商家专属界面：只展示该商家相关的数据和功能
- 平台管理界面：可切换商家，查看所有数据

### 操作审计

- 关键操作日志记录
- 操作审计跟踪
- 异常操作预警

## 开发指南

### 添加新页面

1. 在 `views` 文件夹中创建页面组件
2. 在 `router` 中添加路由配置
3. 如需存储状态，在 `store` 中创建对应的 store
4. 创建 API 请求方法并在组件中调用

### 添加新组件

1. 在 `components` 目录中添加新组件
2. 如果是通用组件，建议添加适当的文档和示例
3. 在需要的页面中导入和使用组件

### 风格指南

- 遵循 Vue 3 组合式 API 风格
- 使用 TypeScript 进行类型定义
- 遵循 Element Plus 的设计规范
- 使用 ESLint 和 Prettier 保持代码风格一致

## 贡献指南

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/amazing-feature`)
3. 提交更改 (`git commit -m 'Add some amazing feature'`)
4. 推送到分支 (`git push origin feature/amazing-feature`)
5. 开启 Pull Request

## 许可证

[闭源]
