# 🛒 Walmart 绑卡管理系统

<div align="center">

![Python](https://img.shields.io/badge/Python-3.9+-blue.svg)
![FastAPI](https://img.shields.io/badge/FastAPI-0.104+-green.svg)
![MySQL](https://img.shields.io/badge/MySQL-8.0+-orange.svg)
![Redis](https://img.shields.io/badge/Redis-6.0+-red.svg)
![Docker](https://img.shields.io/badge/Docker-Supported-blue.svg)
![License](https://img.shields.io/badge/License-Proprietary-yellow.svg)

**企业级多租户沃尔玛会员卡绑定管理系统**

[快速开始](#快速开始) • [API 文档](#api-接口文档) • [部署指南](#部署指南) • [开发指南](#开发指南)

</div>

---

## 📋 目录

- [项目简介](#项目简介)
- [功能特性](#功能特性)
- [技术栈](#技术栈)
- [系统架构](#系统架构)
- [快速开始](#快速开始)
- [项目结构](#项目结构)
- [API 接口文档](#api-接口文档)
- [部署指南](#部署指南)
- [开发指南](#开发指南)
- [测试指南](#测试指南)
- [贡献指南](#贡献指南)
- [许可证](#许可证)

## 🚀 项目简介

Walmart 绑卡管理系统是一个企业级多租户 SaaS 应用，专为管理商户的沃尔玛会员卡绑定服务而设计。系统采用现代化微服务架构，支持多商家接入，每个商家作为独立租户，实现完全的数据隔离和权限管理。

### 🎯 核心价值

- **🏢 多租户架构**：支持无限商家接入，数据完全隔离，安全可靠
- **⚡ 高性能处理**：支持高并发绑卡请求，异步队列处理，响应迅速
- **🔐 企业级安全**：JWT 认证、API 签名验证、IP 白名单、权限控制
- **📊 智能统计**：实时数据统计、可视化报表、业务洞察
- **🔧 易于集成**：RESTful API、完整文档、多语言 SDK 支持

## ✨ 功能特性

### 🏪 商户管理
- 多商户注册与认证
- API 密钥管理与配额控制
- IP 白名单安全策略
- 商户数据完全隔离

### 💳 绑卡服务
- 高并发卡号绑定处理
- 实时余额查询与验证
- 异步队列处理机制
- 智能重试与错误恢复

### 👥 用户权限
- 基于 RBAC 的权限管理
- 多级权限体系（平台/商户/部门/个人）
- 细粒度权限控制（菜单/API/数据）
- 动态权限分配与继承

### 📈 数据统计
- 实时绑卡数据统计
- 多维度数据分析
- 可视化报表展示
- 数据导出功能

### 🔌 API 接口
- RESTful API 设计
- 完整的 Swagger 文档
- 多种认证方式支持
- 标准化响应格式

## 🛠 技术栈

### 后端框架
- **FastAPI** - 现代化 Python Web 框架
- **SQLAlchemy** - 强大的 ORM 框架
- **Pydantic** - 数据验证与序列化
- **APScheduler** - 定时任务调度

### 数据存储
- **MySQL 8.0+** - 主数据库
- **Redis 6.0+** - 缓存与会话存储
- **RabbitMQ** - 消息队列（可选）

### 安全认证
- **JWT** - 无状态身份认证
- **bcrypt** - 密码加密
- **API 签名验证** - 接口安全保护

### 开发工具
- **Docker & Docker Compose** - 容器化部署
- **Pytest** - 单元测试框架
- **Swagger UI / ReDoc** - API 文档
- **Uvicorn** - ASGI 服务器

## 🏗 系统架构

### 架构概览

```mermaid
graph TB
    Client[客户端应用] --> LB[负载均衡器]
    LB --> API[FastAPI 应用]
    API --> Auth[认证中间件]
    API --> Perm[权限中间件]
    API --> Cache[Redis 缓存]
    API --> DB[(MySQL 数据库)]
    API --> MQ[RabbitMQ 队列]
    MQ --> Worker[异步处理器]
    Worker --> Walmart[沃尔玛 API]
    API --> Monitor[监控日志]
```

### 核心组件

- **API 网关层**：请求路由、认证授权、限流熔断
- **业务服务层**：核心业务逻辑、数据处理、权限控制
- **数据访问层**：ORM 映射、数据库操作、缓存管理
- **消息队列层**：异步任务处理、削峰填谷、解耦服务
- **外部接口层**：第三方 API 调用、数据同步、回调处理

## 🚀 快速开始

### 📋 环境要求

| 组件 | 版本要求 | 说明 |
|------|----------|------|
| Python | 3.9+ | 推荐使用 3.11 |
| MySQL | 8.0+ | 主数据库 |
| Redis | 6.0+ | 缓存和会话存储 |
| RabbitMQ | 3.8+ | 消息队列（可选） |
| Docker | 20.0+ | 容器化部署（推荐） |

### 🔧 本地开发环境

#### 1. 克隆项目

```bash
git clone https://github.com/yourusername/walmart-bind-card-server.git
cd walmart-bind-card-server
```

#### 2. 创建虚拟环境

```bash
# 创建虚拟环境
python -m venv .venv

# 激活虚拟环境
# Windows
.venv\Scripts\activate
# Linux/MacOS
source .venv/bin/activate
```

#### 3. 安装依赖

```bash
pip install -r requirements.txt
```

#### 4. 配置环境

```bash
# 复制配置文件
cp config.yaml.example config.yaml

# 编辑配置文件，修改数据库连接等信息
vim config.yaml
```

#### 5. 初始化数据库

```bash
# 创建数据库（如果不存在）
mysql -u root -p -e "CREATE DATABASE IF NOT EXISTS walmart_card_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;"

# 导入初始化脚本
mysql -u root -p walmart_card_db < mysql-init/01-create-tables.sql
mysql -u root -p walmart_card_db < mysql-init/02-init-data.sql
```

#### 6. 启动服务

```bash
# 开发模式启动（支持热重载）
python app/main.py

# 或使用 uvicorn 直接启动
uvicorn app.main:app --host 0.0.0.0 --port 20000 --reload
```

### 🐳 Docker 部署（推荐）

#### 1. 使用 Docker Compose 一键部署

```bash
# 构建并启动所有服务
docker-compose up -d

# 查看服务状态
docker-compose ps

# 查看应用日志
docker-compose logs -f app
```

#### 2. 验证部署

```bash
# 健康检查
curl http://localhost:20000/health

# 访问 API 文档
open http://localhost:20000/docs
```

### 🔑 默认管理员账户

| 字段 | 值 |
|------|-----|
| 用户名 | admin |
| 密码 | 7c222fb2927d828af22f592134e8932480637c0d |
| 邮箱 | <EMAIL> |

> ⚠️ **安全提示**：生产环境请立即修改默认密码！

## 📁 项目结构

```
walmart-bind-card-server/
├── 📁 app/                          # 应用主目录
│   ├── 📁 api/                      # API 路由层
│   │   └── 📁 v1/                   # API v1 版本
│   │       ├── 📁 endpoints/        # API 端点实现
│   │       │   ├── auth.py          # 认证相关接口
│   │       │   ├── bind_card.py     # 绑卡核心接口
│   │       │   ├── merchants.py     # 商户管理接口
│   │       │   ├── users.py         # 用户管理接口
│   │       │   └── ...              # 其他业务接口
│   │       └── router.py            # 路由注册
│   ├── 📁 core/                     # 核心配置层
│   │   ├── config.py                # 配置管理
│   │   ├── security.py              # 安全认证
│   │   ├── middleware.py            # 中间件
│   │   └── walmart_api.py           # 沃尔玛 API 客户端
│   ├── 📁 crud/                     # 数据访问层
│   │   ├── base.py                  # 基础 CRUD 操作
│   │   ├── user.py                  # 用户数据操作
│   │   ├── merchant.py              # 商户数据操作
│   │   └── card_record.py           # 绑卡记录操作
│   ├── 📁 db/                       # 数据库层
│   │   ├── session.py               # 数据库会话
│   │   └── init_db.py               # 数据库初始化
│   ├── 📁 models/                   # 数据模型层
│   │   ├── __init__.py              # 模型导出
│   │   ├── user.py                  # 用户模型
│   │   ├── merchant.py              # 商户模型
│   │   ├── card_record.py           # 绑卡记录模型
│   │   └── ...                      # 其他业务模型
│   ├── 📁 schemas/                  # 数据验证层
│   │   ├── user.py                  # 用户数据验证
│   │   ├── merchant.py              # 商户数据验证
│   │   ├── card.py                  # 绑卡数据验证
│   │   └── response.py              # 响应格式定义
│   ├── 📁 services/                 # 业务服务层
│   │   ├── binding_service.py       # 绑卡业务服务
│   │   ├── permission_service.py    # 权限业务服务
│   │   ├── queue_consumer.py        # 队列消费服务
│   │   └── scheduler_service.py     # 定时任务服务
│   ├── 📁 utils/                    # 工具函数层
│   │   ├── common.py                # 通用工具
│   │   ├── security.py              # 安全工具
│   │   └── validators.py            # 验证工具
│   └── main.py                      # 应用入口
├── 📁 mysql-init/                   # 数据库初始化
│   ├── 01-create-tables.sql         # 建表脚本
│   ├── 02-init-data.sql             # 初始数据
│   └── ...                          # 其他 SQL 脚本
├── 📁 test/                         # 测试目录
│   ├── 📁 api/                      # API 测试
│   ├── 📁 services/                 # 服务测试
│   └── conftest.py                  # 测试配置
├── 📁 docs/                         # 文档目录
├── 📁 logs/                         # 日志目录
├── config.yaml                      # 应用配置文件
├── requirements.txt                 # Python 依赖
├── Dockerfile                       # Docker 构建文件
├── docker-compose.yml               # Docker Compose 配置
└── README.md                        # 项目说明文档
```

## 📚 API 接口文档

### 🌐 在线文档

启动服务后，可通过以下 URL 访问完整的 API 文档：

| 文档类型 | 访问地址 | 说明 |
|----------|----------|------|
| **Swagger UI** | http://localhost:20000/docs | 交互式 API 文档，支持在线测试 |
| **ReDoc** | http://localhost:20000/redoc | 美观的 API 文档，适合阅读 |
| **OpenAPI JSON** | http://localhost:20000/api/v1/openapi.json | 原始 OpenAPI 规范文件 |
| **详细 API 文档** | [docs/API.md](docs/API.md) | 完整的接口调用指南和示例 |
| **部署指南** | [docs/DEPLOYMENT.md](docs/DEPLOYMENT.md) | 生产环境部署详细说明 |
| **Postman 集合** | [docs/postman/](docs/postman/) | API 测试集合和环境配置 |

### 🔑 认证方式

系统支持两种认证方式：

#### 1. JWT Bearer Token（管理后台）
```http
Authorization: Bearer <your_jwt_token>
```

#### 2. API Key + 签名（商户接口）
```http
api-key: <your_api_key>
X-Timestamp: <unix_timestamp_ms>
X-Nonce: <random_string>
X-Signature: <calculated_signature>
```

### 📋 核心接口概览

#### 🔐 认证接口
| 方法 | 路径 | 说明 | 认证 |
|------|------|------|------|
| `POST` | `/api/v1/auth/login` | 用户登录 | ❌ |
| `POST` | `/api/v1/auth/logout` | 用户登出 | ✅ |
| `POST` | `/api/v1/auth/refresh-token` | 刷新令牌 | ✅ |
| `GET` | `/api/v1/auth/me` | 获取当前用户信息 | ✅ |

#### 👥 用户管理接口
| 方法 | 路径 | 说明 | 权限 |
|------|------|------|------|
| `GET` | `/api/v1/users` | 获取用户列表 | `users:read` |
| `POST` | `/api/v1/users` | 创建用户 | `users:create` |
| `GET` | `/api/v1/users/{user_id}` | 获取用户详情 | `users:read` |
| `PUT` | `/api/v1/users/{user_id}` | 更新用户 | `users:update` |
| `DELETE` | `/api/v1/users/{user_id}` | 删除用户 | `users:delete` |

#### 🏪 商户管理接口
| 方法 | 路径 | 说明 | 权限 |
|------|------|------|------|
| `GET` | `/api/v1/merchants` | 获取商户列表 | `merchants:read` |
| `POST` | `/api/v1/merchants` | 创建商户 | `merchants:create` |
| `GET` | `/api/v1/merchants/{merchant_id}` | 获取商户详情 | `merchants:read` |
| `PUT` | `/api/v1/merchants/{merchant_id}` | 更新商户 | `merchants:update` |
| `DELETE` | `/api/v1/merchants/{merchant_id}` | 删除商户 | `merchants:delete` |

#### 💳 绑卡核心接口
| 方法 | 路径 | 说明 | 认证方式 |
|------|------|------|----------|
| `POST` | `/api/v1/card-bind` | **绑定卡片** | API Key + 签名 |
| `GET` | `/api/v1/cards` | 获取绑卡记录 | JWT Token |
| `GET` | `/api/v1/cards/{record_id}` | 获取记录详情 | JWT Token |
| `GET` | `/api/v1/cards/statistics` | 获取统计数据 | JWT Token |
| `GET` | `/api/v1/cards/export` | 导出记录 | JWT Token |

### 🚀 绑卡接口详细说明

#### 接口概述

绑卡接口是系统的核心功能，用于将沃尔玛会员卡绑定到商户订单。该接口采用严格的安全验证机制，确保数据安全和接口稳定性。

#### 请求示例

**接口地址**：`POST /api/v1/card-bind`

**请求头**：
```http
Content-Type: application/json
api-key: your_merchant_api_key
X-Timestamp: 1703123456789
X-Nonce: abc123def456ghi789
X-Signature: calculated_signature_hash
```

**请求体**：
```json
{
  "card_number": "1234567890123456",
  "card_password": "123456",
  "merchant_code": "MERCHANT001",
  "merchant_order_id": "ORDER20231221001",
  "amount": 10000,
  "ext_data": "custom_data_for_callback"
}
```

**响应示例**：
```json
{
  "code": 0,
  "message": "绑卡成功",
  "data": {
    "request_id": "req_1703123456789_abc123",
    "status": "success",
    "actual_amount": 10000,
    "balance": "100.00",
    "card_balance": "100.00",
    "process_time": 1.23
  }
}
```

#### 签名算法

```python
import hashlib
import hmac
import json
from urllib.parse import urlencode

def generate_signature(data, secret_key, timestamp, nonce, method="POST", path="/api/v1/card-bind"):
    """生成 API 请求签名"""
    # 1. 构建签名字符串
    if isinstance(data, dict):
        # 对字典按键排序并转换为查询字符串格式
        sorted_params = sorted(data.items())
        query_string = urlencode(sorted_params)
    else:
        query_string = str(data)

    # 2. 构建待签名字符串
    sign_string = f"{method}\n{path}\n{query_string}\n{timestamp}\n{nonce}"

    # 3. 使用 HMAC-SHA256 生成签名
    signature = hmac.new(
        secret_key.encode('utf-8'),
        sign_string.encode('utf-8'),
        hashlib.sha256
    ).hexdigest()

    return signature
```

#### 错误码说明

| 错误码 | 说明 | 解决方案 |
|--------|------|----------|
| `2000` | 商户不存在 | 检查 API Key 是否正确 |
| `2001` | 商户已禁用 | 联系平台管理员 |
| `2002` | API Key 无效 | 重新获取 API Key |
| `2004` | IP 地址受限 | 将 IP 添加到白名单 |
| `3000` | 卡号不存在 | 检查卡号是否正确 |
| `3001` | 卡片已过期 | 使用有效的卡片 |
| `3002` | 卡号或密码错误 | 检查卡号和密码 |
| `3004` | 卡片已被绑定 | 该卡片已被其他订单绑定 |

#### 状态码说明

| 状态 | 说明 | 后续处理 |
|------|------|----------|
| `pending` | 处理中 | 等待处理完成 |
| `success` | 绑定成功 | 订单可以继续处理 |
| `failed` | 绑定失败 | 检查错误信息并重试 |
| `timeout` | 处理超时 | 可以重新提交请求 |

### 🔧 多语言调用示例

#### Python 示例

```python
import requests
import hashlib
import hmac
import time
import uuid
import json

class WalmartBindCardClient:
    def __init__(self, api_key, secret_key, base_url="http://localhost:20000"):
        self.api_key = api_key
        self.secret_key = secret_key
        self.base_url = base_url

    def bind_card(self, card_number, card_password, merchant_code, merchant_order_id, amount, ext_data=None):
        """绑定卡片"""
        url = f"{self.base_url}/api/v1/card-bind"

        # 构建请求数据
        data = {
            "card_number": card_number,
            "card_password": card_password,
            "merchant_code": merchant_code,
            "merchant_order_id": merchant_order_id,
            "amount": amount
        }
        if ext_data:
            data["ext_data"] = ext_data

        # 生成签名
        timestamp = str(int(time.time() * 1000))
        nonce = str(uuid.uuid4()).replace('-', '')
        signature = self._generate_signature(data, timestamp, nonce)

        # 构建请求头
        headers = {
            "Content-Type": "application/json",
            "api-key": self.api_key,
            "X-Timestamp": timestamp,
            "X-Nonce": nonce,
            "X-Signature": signature
        }

        # 发送请求
        response = requests.post(url, json=data, headers=headers)
        return response.json()

    def _generate_signature(self, data, timestamp, nonce, method="POST", path="/api/v1/card-bind"):
        """生成签名"""
        # 对数据进行排序并构建查询字符串
        sorted_params = sorted(data.items())
        query_string = "&".join([f"{k}={v}" for k, v in sorted_params])

        # 构建签名字符串
        sign_string = f"{method}\n{path}\n{query_string}\n{timestamp}\n{nonce}"

        # 生成 HMAC-SHA256 签名
        signature = hmac.new(
            self.secret_key.encode('utf-8'),
            sign_string.encode('utf-8'),
            hashlib.sha256
        ).hexdigest()

        return signature

# 使用示例
client = WalmartBindCardClient("your_api_key", "your_secret_key")
result = client.bind_card(
    card_number="1234567890123456",
    card_password="123456",
    merchant_code="MERCHANT001",
    merchant_order_id="ORDER20231221001",
    amount=10000
)
print(result)
```

#### JavaScript/Node.js 示例

```javascript
const crypto = require('crypto');
const axios = require('axios');

class WalmartBindCardClient {
    constructor(apiKey, secretKey, baseUrl = 'http://localhost:20000') {
        this.apiKey = apiKey;
        this.secretKey = secretKey;
        this.baseUrl = baseUrl;
    }

    async bindCard(cardNumber, cardPassword, merchantCode, merchantOrderId, amount, extData = null) {
        const url = `${this.baseUrl}/api/v1/card-bind`;

        // 构建请求数据
        const data = {
            card_number: cardNumber,
            card_password: cardPassword,
            merchant_code: merchantCode,
            merchant_order_id: merchantOrderId,
            amount: amount
        };
        if (extData) {
            data.ext_data = extData;
        }

        // 生成签名
        const timestamp = Date.now().toString();
        const nonce = crypto.randomBytes(16).toString('hex');
        const signature = this._generateSignature(data, timestamp, nonce);

        // 构建请求头
        const headers = {
            'Content-Type': 'application/json',
            'api-key': this.apiKey,
            'X-Timestamp': timestamp,
            'X-Nonce': nonce,
            'X-Signature': signature
        };

        try {
            const response = await axios.post(url, data, { headers });
            return response.data;
        } catch (error) {
            throw new Error(`绑卡请求失败: ${error.message}`);
        }
    }

    _generateSignature(data, timestamp, nonce, method = 'POST', path = '/api/v1/card-bind') {
        // 对数据进行排序并构建查询字符串
        const sortedParams = Object.keys(data).sort().map(key => `${key}=${data[key]}`);
        const queryString = sortedParams.join('&');

        // 构建签名字符串
        const signString = `${method}\n${path}\n${queryString}\n${timestamp}\n${nonce}`;

        // 生成 HMAC-SHA256 签名
        const signature = crypto
            .createHmac('sha256', this.secretKey)
            .update(signString)
            .digest('hex');

        return signature;
    }
}

// 使用示例
const client = new WalmartBindCardClient('your_api_key', 'your_secret_key');
client.bindCard(
    '1234567890123456',
    '123456',
    'MERCHANT001',
    'ORDER20231221001',
    10000
).then(result => {
    console.log(result);
}).catch(error => {
    console.error(error);
});
```

#### cURL 示例

```bash
#!/bin/bash

# 配置参数
API_KEY="your_api_key"
SECRET_KEY="your_secret_key"
BASE_URL="http://localhost:20000"
TIMESTAMP=$(date +%s%3N)
NONCE=$(openssl rand -hex 16)

# 请求数据
CARD_NUMBER="1234567890123456"
CARD_PASSWORD="123456"
MERCHANT_CODE="MERCHANT001"
MERCHANT_ORDER_ID="ORDER20231221001"
AMOUNT=10000

# 构建查询字符串（按字母顺序排序）
QUERY_STRING="amount=${AMOUNT}&card_number=${CARD_NUMBER}&card_password=${CARD_PASSWORD}&merchant_code=${MERCHANT_CODE}&merchant_order_id=${MERCHANT_ORDER_ID}"

# 构建签名字符串
SIGN_STRING="POST\n/api/v1/card-bind\n${QUERY_STRING}\n${TIMESTAMP}\n${NONCE}"

# 生成签名
SIGNATURE=$(echo -n "${SIGN_STRING}" | openssl dgst -sha256 -hmac "${SECRET_KEY}" -binary | xxd -p -c 256)

# 发送请求
curl -X POST "${BASE_URL}/api/v1/card-bind" \
  -H "Content-Type: application/json" \
  -H "api-key: ${API_KEY}" \
  -H "X-Timestamp: ${TIMESTAMP}" \
  -H "X-Nonce: ${NONCE}" \
  -H "X-Signature: ${SIGNATURE}" \
  -d "{
    \"card_number\": \"${CARD_NUMBER}\",
    \"card_password\": \"${CARD_PASSWORD}\",
    \"merchant_code\": \"${MERCHANT_CODE}\",
    \"merchant_order_id\": \"${MERCHANT_ORDER_ID}\",
    \"amount\": ${AMOUNT}
  }"
```

## 🚀 部署指南

### 🐳 生产环境部署

#### 1. 环境准备

```bash
# 创建部署目录
mkdir -p /opt/walmart-bind-card
cd /opt/walmart-bind-card

# 克隆代码
git clone https://github.com/yourusername/walmart-bind-card-server.git .
```

#### 2. 配置文件

```bash
# 复制并编辑生产配置
cp config.yaml.example config.yaml
vim config.yaml
```

**生产配置示例**：
```yaml
# 生产环境配置
api:
  secret_key: "your_production_secret_key_here"
  access_token_expire_minutes: 1440  # 24小时

database:
  host: "your_mysql_host"
  port: 3306
  user: "walmart_user"
  password: "your_secure_password"
  db_name: "walmart_card_db"

redis:
  host: "your_redis_host"
  port: 6379
  password: "your_redis_password"

# 安全配置
cors:
  origins: ["https://your-frontend-domain.com"]

rate_limit:
  per_minute: 1000
```

#### 3. 使用 Docker Compose 部署

```bash
# 启动生产环境
docker-compose -f docker-compose.prod.yml up -d

# 查看服务状态
docker-compose ps

# 查看日志
docker-compose logs -f app
```

#### 4. 反向代理配置（Nginx）

```nginx
server {
    listen 80;
    server_name your-api-domain.com;

    # 重定向到 HTTPS
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name your-api-domain.com;

    # SSL 配置
    ssl_certificate /path/to/your/certificate.crt;
    ssl_certificate_key /path/to/your/private.key;

    # 安全头
    add_header X-Frame-Options DENY;
    add_header X-Content-Type-Options nosniff;
    add_header X-XSS-Protection "1; mode=block";

    # API 代理
    location /api/ {
        proxy_pass http://localhost:20000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;

        # 超时设置
        proxy_connect_timeout 30s;
        proxy_send_timeout 30s;
        proxy_read_timeout 30s;
    }

    # 健康检查
    location /health {
        proxy_pass http://localhost:20000/health;
        access_log off;
    }
}
```

### 📊 监控和日志

#### 1. 日志配置

系统会自动生成以下日志文件：

```
logs/
├── app.log          # 应用主日志
├── api.log          # API 请求日志
├── auth.log         # 认证相关日志
├── error.log        # 错误日志
└── security_audit.log # 安全审计日志
```

#### 2. 健康检查

```bash
# 检查服务状态
curl http://localhost:20000/health

# 检查数据库连接
curl http://localhost:20000/api/v1/system/health
```

#### 3. 性能监控

建议使用以下工具进行监控：

- **Prometheus + Grafana**：指标监控
- **ELK Stack**：日志分析
- **Sentry**：错误追踪
- **New Relic / DataDog**：APM 监控

## 🛠 开发指南

### 🏗 开发环境搭建

#### 1. 代码规范

项目使用以下代码规范：

```bash
# 安装开发依赖
pip install black isort flake8 mypy pytest-cov

# 代码格式化
black app/
isort app/

# 代码检查
flake8 app/
mypy app/
```

#### 2. 添加新 API

**步骤 1：定义数据模型**
```python
# app/schemas/your_module.py
from pydantic import BaseModel

class YourRequest(BaseModel):
    field1: str
    field2: int

class YourResponse(BaseModel):
    result: str
```

**步骤 2：创建数据库操作**
```python
# app/crud/your_module.py
from app.crud.base import CRUDBase
from app.models.your_model import YourModel

class CRUDYourModule(CRUDBase[YourModel, YourCreate, YourUpdate]):
    pass

your_crud = CRUDYourModule(YourModel)
```

**步骤 3：实现 API 端点**
```python
# app/api/v1/endpoints/your_module.py
from fastapi import APIRouter, Depends
from app.schemas.your_module import YourRequest, YourResponse

router = APIRouter()

@router.post("/", response_model=YourResponse)
async def create_item(
    *,
    request: YourRequest,
    current_user = Depends(get_current_user)
):
    # 实现业务逻辑
    pass
```

**步骤 4：注册路由**
```python
# app/api/v1/router.py
from app.api.v1.endpoints import your_module

api_router.include_router(
    your_module.router,
    prefix="/your-module",
    tags=["your-module"]
)
```

#### 3. 数据库迁移

```bash
# 创建迁移文件
alembic revision --autogenerate -m "Add new table"

# 应用迁移
alembic upgrade head

# 回滚迁移
alembic downgrade -1
```

#### 4. 单元测试

```python
# test/test_your_module.py
import pytest
from fastapi.testclient import TestClient
from app.main import app

client = TestClient(app)

def test_your_api():
    response = client.post("/api/v1/your-module/", json={
        "field1": "test",
        "field2": 123
    })
    assert response.status_code == 200
    assert response.json()["result"] == "expected"
```

### 🔧 常用开发命令

```bash
# 启动开发服务器
python app/main.py

# 运行测试
pytest

# 生成测试覆盖率报告
pytest --cov=app --cov-report=html

# 检查代码质量
flake8 app/
black --check app/
isort --check-only app/

# 更新依赖
pip-compile requirements.in
pip install -r requirements.txt
```

## 🧪 测试指南

### 🔍 测试框架

项目使用 **Playwright** 进行端到端测试，**pytest** 进行单元测试。

#### 1. 运行所有测试

```bash
# 运行单元测试
pytest test/

# 运行集成测试
python test/run_comprehensive_tests.py

# 运行权限隔离测试
python test/comprehensive_permission_isolation_test.py
```

#### 2. 测试环境配置

**测试数据库配置**：
```yaml
# config.yaml (测试环境)
database:
  host: localhost
  port: 3306
  user: root
  password: 7c222fb2927d828af22f592134e8932480637c0d
  db_name: walmart_card_test_db
```

**测试用户账户**：
- 超级管理员：`admin` / `7c222fb2927d828af22f592134e8932480637c0d`
- 测试商户：通过测试脚本自动创建

#### 3. 权限测试

系统提供完整的权限隔离测试：

```bash
# 运行权限测试
python test/comprehensive_permission_isolation_test.py

# 检查数据隔离
python test/data_integrity_check.py

# 验证 API 权限
python test/api_permission_isolation_test.py
```

#### 4. 前端测试

使用 Playwright 进行前端自动化测试：

```bash
# 安装 Playwright
pip install playwright
playwright install

# 运行前端测试
python test/frontend/test_login_flow.py
python test/frontend/test_permission_system.py
```

### 📋 测试用例覆盖

| 测试类型 | 覆盖范围 | 测试文件 |
|----------|----------|----------|
| **单元测试** | 业务逻辑、工具函数 | `test/unit/` |
| **集成测试** | API 接口、数据库操作 | `test/integration/` |
| **权限测试** | RBAC 权限系统 | `test/permissions/` |
| **安全测试** | 认证、授权、数据隔离 | `test/security/` |
| **性能测试** | 并发处理、响应时间 | `test/performance/` |
| **端到端测试** | 完整业务流程 | `test/e2e/` |

## 🤝 贡献指南

### 📝 贡献流程

1. **Fork 项目**
   ```bash
   git clone https://github.com/yourusername/walmart-bind-card-server.git
   cd walmart-bind-card-server
   ```

2. **创建功能分支**
   ```bash
   git checkout -b feature/amazing-feature
   ```

3. **开发和测试**
   ```bash
   # 编写代码
   # 运行测试
   pytest
   # 检查代码质量
   black app/
   flake8 app/
   ```

4. **提交更改**
   ```bash
   git add .
   git commit -m 'feat: add amazing feature'
   ```

5. **推送分支**
   ```bash
   git push origin feature/amazing-feature
   ```

6. **创建 Pull Request**
   - 在 GitHub 上创建 PR
   - 填写详细的描述
   - 等待代码审查

### 📋 代码规范

#### 1. 提交信息规范

使用 [Conventional Commits](https://www.conventionalcommits.org/) 规范：

```
<type>[optional scope]: <description>

[optional body]

[optional footer(s)]
```

**类型说明**：
- `feat`: 新功能
- `fix`: 修复 bug
- `docs`: 文档更新
- `style`: 代码格式调整
- `refactor`: 代码重构
- `test`: 测试相关
- `chore`: 构建过程或辅助工具的变动

**示例**：
```
feat(api): add card binding retry mechanism
fix(auth): resolve JWT token expiration issue
docs(readme): update API documentation
```

#### 2. 代码风格

- **Python**: 遵循 PEP 8 规范，使用 Black 格式化
- **导入顺序**: 使用 isort 自动排序
- **类型注解**: 使用 mypy 进行类型检查
- **文档字符串**: 使用 Google 风格的 docstring

#### 3. 测试要求

- 新功能必须包含单元测试
- 测试覆盖率不低于 80%
- 关键业务逻辑需要集成测试
- API 变更需要更新文档

### 🐛 问题报告

发现 bug 时，请提供以下信息：

1. **环境信息**
   - 操作系统版本
   - Python 版本
   - 依赖包版本

2. **重现步骤**
   - 详细的操作步骤
   - 预期结果 vs 实际结果
   - 错误日志或截图

3. **最小重现示例**
   - 提供可重现问题的最小代码示例

### 💡 功能建议

提出新功能建议时，请说明：

1. **使用场景**：什么情况下需要这个功能
2. **解决方案**：建议的实现方式
3. **影响范围**：对现有功能的影响
4. **优先级**：功能的重要程度

## 📄 许可证

本项目为 **专有软件**，版权归项目所有者所有。

### 使用条款

- ✅ 允许内部使用和开发
- ✅ 允许为特定客户定制
- ❌ 禁止未经授权的分发
- ❌ 禁止商业用途（除非获得许可）
- ❌ 禁止逆向工程

### 联系方式

如需商业许可或技术支持，请联系：

- **邮箱**: <EMAIL>
- **电话**: +86-xxx-xxxx-xxxx
- **官网**: https://walmart-bind-card.com

---

<div align="center">

**🎉 感谢使用 Walmart 绑卡管理系统！**

如果这个项目对您有帮助，请给我们一个 ⭐ Star！

[回到顶部](#-walmart-绑卡管理系统) • [报告问题](https://github.com/yourusername/walmart-bind-card-server/issues) • [功能建议](https://github.com/yourusername/walmart-bind-card-server/discussions)

</div>
