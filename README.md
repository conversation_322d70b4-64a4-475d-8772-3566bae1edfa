# 沃尔玛绑卡网关

高性能绑卡网关系统，基于 Go 语言开发，支持高并发请求处理和外部服务连接。

## 技术栈

- **语言**: Go 1.21+
- **Web 框架**: Gin
- **数据库**: MySQL 8.0 + GORM
- **缓存**: Redis 7.0
- **消息队列**: RabbitMQ 3.12
- **容器化**: Docker & Docker Compose

## 项目结构

```
walmart-bind-card-gateway/
├── main.go                 # 主程序入口
├── config.yaml             # 配置文件
├── go.mod                  # Go模块文件
├── Dockerfile              # Docker构建文件
├── docker-compose.yml      # Docker编排文件
├── internal/               # 内部包
│   ├── config/            # 配置管理
│   ├── handler/           # HTTP处理器
│   ├── model/             # 数据模型
│   ├── repository/        # 数据仓储层
│   └── service/           # 业务服务层
├── pkg/                   # 公共包
│   ├── database/          # 数据库客户端
│   ├── redis/             # Redis客户端
│   ├── queue/             # 消息队列客户端
│   ├── metrics/           # 监控指标
│   └── logger/            # 日志组件
├── scripts/               # 构建和部署脚本
└── tests/                 # 测试文件
    └── test_api_compatibility.go  # API兼容性测试
```

## 🚀 快速开始

### 本地开发环境（Windows + Docker）

```powershell
# 1. 配置环境变量
cp .env.example .env
# 编辑 .env 文件，设置外部服务地址

# 2. Docker开发环境构建
.\build-docker.ps1

# 3. 验证服务
curl http://localhost:21000/health
```

### 生产环境部署（Linux 可执行文件）

```powershell
# 1. 构建安全的Linux可执行文件（防反编译）
.\build-production.ps1

# 2. 上传部署包到Linux服务器
# 上传 dist/ 目录中的部署包

# 3. 在Linux服务器上部署
chmod +x walmart-gateway start.sh
cp config.yaml.example config.yaml
# 编辑 config.yaml 配置外部服务
./start.sh
```

## 🛠️ 构建脚本说明

### 生产环境构建（防反编译）

```powershell
# 安全构建Linux可执行文件
.\build-production.ps1

# 构建选项
.\build-production.ps1 -SkipTests    # 跳过测试
.\build-production.ps1 -Verbose      # 详细输出
```

**安全特性：**

- ✅ 代码混淆（garble）
- ✅ 符号表移除
- ✅ 调试信息清除
- ✅ UPX 压缩保护
- ✅ 静态链接编译
- ✅ 防反编译保护

### 本地开发构建

```powershell
# Docker开发环境
.\build-docker.ps1

# 构建选项
.\build-docker.ps1 -Clean           # 清理重建
.\build-docker.ps1 -NoBuild         # 只启动服务
.\build-docker.ps1 -Verbose         # 详细输出
```

### 传统构建（兼容性）

```powershell
# 通用构建脚本
.\build.ps1 -Platform linux         # Linux版本
.\build.ps1 -Platform windows       # Windows版本
.\build.ps1 -Platform both          # 两个版本
```

## API 接口

### 绑卡接口

```http
POST /api/v1/card-bind
Content-Type: application/json

{
    "card_number": "1234567890123456",
    "card_password": "123456",
    "merchant_code": "WALMART_TEST",
    "amount": 100.00,
    "callback_url": "http://example.com/callback"
}
```

### 状态查询

```http
GET /api/v1/card-bind/{request_id}/status
```

### 健康检查

```http
GET /health
```

## 配置说明

主要配置项在 `config.yaml` 文件中：

- `server`: 服务器配置（端口、超时等）
- `database`: MySQL 数据库配置
- `redis`: Redis 缓存配置
- `rabbitmq`: RabbitMQ 消息队列配置
- `logging`: 日志配置
- `api`: API 相关配置

## 监控

- 健康检查: `http://localhost:20000/health`
- 监控指标: `http://localhost:20000/api/v1/metrics`
- RabbitMQ 管理: `http://localhost:15672`

## 性能特性

- **高吞吐量**: 支持每秒数万次请求
- **低延迟**: 平均响应时间 < 5ms
- **批量处理**: 支持批量数据库写入和消息发送
- **连接池**: 优化的数据库和 Redis 连接池
- **限流保护**: 内置限流机制防止过载

## 安全特性

- **源码保护**: 默认启用代码混淆和加密编译
- **反编译保护**: 符号表清除、调试信息移除、UPX 压缩
- **运行时安全**: 容器化部署、最小权限运行
- **传输安全**: HTTPS 强制、API 签名验证
- **数据安全**: 敏感信息加密存储

## 开发说明

### 添加新接口

1. 在 `internal/handler/` 中添加处理器
2. 在 `internal/service/` 中添加业务逻辑
3. 在 `internal/model/` 中定义数据模型
4. 在 `main.go` 中注册路由

### 数据库操作

使用 `internal/repository/` 中的仓储层进行数据库操作，支持批量操作以提升性能。

**注意**: 系统不会自动迁移数据库，请确保数据库表已经创建。如需迁移，可以使用 `scripts/init.sql` 脚本。

### 日志记录

使用 `pkg/logger/` 中的结构化日志组件，支持不同级别的日志输出。

### API 兼容性测试

运行 API 兼容性测试：

```bash
cd tests
go run test_api_compatibility.go
```

## 部署

### 环境要求

- Go 1.21+
- MySQL 8.0+
- Redis 7.0+
- RabbitMQ 3.12+

### Docker 部署

项目包含完整的 Docker 配置，可以一键部署：

```bash
# 完整部署
./scripts/deploy.sh

# 查看服务状态
./scripts/deploy.sh status

# 查看日志
./scripts/deploy.sh logs
```

## 许可证

MIT License
