"""
对账台API接口
提供部门统计、CK统计、绑卡记录查询等功能
"""
from typing import List, Optional, Any, Dict
from fastapi import APIRouter, Depends, HTTPException, Query, status, Response
from sqlalchemy.orm import Session
from datetime import datetime, date
import io
import pandas as pd

from app.api import deps
from app.models.user import User
from app.services.reconciliation_service import ReconciliationService
from app.services.permission_service import PermissionService
from app.core.logging import get_logger

router = APIRouter()
logger = get_logger("reconciliation_api")


def parse_date_param(date_str: Optional[str]) -> Optional[date]:
    """解析日期参数，处理空字符串的情况"""
    if not date_str or date_str.strip() == "":
        return None
    try:
        return datetime.strptime(date_str, "%Y-%m-%d").date()
    except ValueError:
        return None


@router.get("/departments/statistics", response_model=Dict[str, Any])
async def get_department_statistics(
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_active_user),
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(20, ge=1, le=100, description="每页数量"),
    parent_department_id: Optional[int] = Query(None, description="父部门ID，为空时查询一级部门"),
    level: Optional[int] = Query(None, description="部门层级"),
    merchant_id: Optional[int] = Query(None, description="商户ID"),
    start_date: Optional[str] = Query(None, description="开始日期"),
    end_date: Optional[str] = Query(None, description="结束日期"),
    time_range: Optional[str] = Query("today", description="时间范围：today/yesterday/week/month/custom"),
):
    """
    获取部门绑卡统计数据

    支持层级钻取：
    - 不传parent_department_id时，返回一级部门统计
    - 传入parent_department_id时，返回该部门的子部门统计

    权限要求:
    - "reconciliation:read": 查看对账台权限
    """
    try:
        # 权限检查
        permission_service = PermissionService(db)
        has_permission = permission_service.check_user_permission(
            current_user, "api:reconciliation:read"
        )
        if not has_permission:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="没有查看对账台的权限",
            )

        # 【安全修复】：强制商户隔离检查
        if not current_user.is_superuser:
            if not current_user.merchant_id:
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail="用户没有商户信息，无法访问对账台数据",
                )

        # 解析日期参数
        parsed_start_date = parse_date_param(start_date)
        parsed_end_date = parse_date_param(end_date)

        reconciliation_service = ReconciliationService(db)
        data = await reconciliation_service.get_department_statistics(
            current_user=current_user,
            page=page,
            page_size=page_size,
            parent_department_id=parent_department_id,
            level=level,
            merchant_id=merchant_id,
            start_date=parsed_start_date,
            end_date=parsed_end_date,
            time_range=time_range,
        )
        return data
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取部门统计数据失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取部门统计数据失败: {str(e)}"
        )


@router.get("/departments/{department_id}/ck-statistics", response_model=Dict[str, Any])
async def get_department_ck_statistics(
    department_id: int,
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_active_user),
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(20, ge=1, le=100, description="每页数量"),
    start_date: Optional[str] = Query(None, description="开始日期"),
    end_date: Optional[str] = Query(None, description="结束日期"),
    time_range: Optional[str] = Query("today", description="时间范围：today/yesterday/week/month/custom"),
    min_success_count: Optional[int] = Query(1, description="最小成功笔数筛选"),
    ck_status: Optional[str] = Query("all", description="CK状态筛选：all/active/inactive"),
):
    """
    获取部门下CK的统计数据
    
    权限要求:
    - "reconciliation:read": 查看对账台权限
    """
    try:
        # 权限检查
        permission_service = PermissionService(db)
        has_permission = permission_service.check_user_permission(
            current_user, "api:reconciliation:read"
        )
        if not has_permission:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="没有查看对账台的权限",
            )

        # 【安全修复】：强制商户隔离检查
        if not current_user.is_superuser:
            if not current_user.merchant_id:
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail="用户没有商户信息，无法访问对账台数据",
                )

        # 解析日期参数
        parsed_start_date = parse_date_param(start_date)
        parsed_end_date = parse_date_param(end_date)

        reconciliation_service = ReconciliationService(db)
        data = await reconciliation_service.get_department_ck_statistics(
            current_user=current_user,
            department_id=department_id,
            page=page,
            page_size=page_size,
            start_date=parsed_start_date,
            end_date=parsed_end_date,
            time_range=time_range,
            min_success_count=min_success_count,
            ck_status=ck_status,
        )
        return data
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取CK统计数据失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取CK统计数据失败: {str(e)}"
        )


@router.get("/ck/{ck_id}/records", response_model=Dict[str, Any])
async def get_ck_binding_records(
    ck_id: int,
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_active_user),
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(20, ge=1, le=100, description="每页数量"),
    start_date: Optional[str] = Query(None, description="开始日期"),
    end_date: Optional[str] = Query(None, description="结束日期"),
    time_range: Optional[str] = Query("today", description="时间范围：today/yesterday/week/month/custom"),
    search_keyword: Optional[str] = Query(None, description="搜索关键词（订单号/卡号）"),
):
    """
    获取CK的成功绑卡记录明细
    
    权限要求:
    - "reconciliation:read": 查看对账台权限
    """
    try:
        # 权限检查
        permission_service = PermissionService(db)
        has_permission = permission_service.check_user_permission(
            current_user, "api:reconciliation:read"
        )
        if not has_permission:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="没有查看对账台的权限",
            )

        # 【安全修复】：强制商户隔离检查
        if not current_user.is_superuser:
            if not current_user.merchant_id:
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail="用户没有商户信息，无法访问对账台数据",
                )

        # 解析日期参数
        parsed_start_date = parse_date_param(start_date)
        parsed_end_date = parse_date_param(end_date)

        reconciliation_service = ReconciliationService(db)
        data = await reconciliation_service.get_ck_binding_records(
            current_user=current_user,
            ck_id=ck_id,
            page=page,
            page_size=page_size,
            start_date=parsed_start_date,
            end_date=parsed_end_date,
            time_range=time_range,
            search_keyword=search_keyword,
        )
        return data
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取绑卡记录失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取绑卡记录失败: {str(e)}"
        )


@router.get("/export/departments", response_class=Response)
async def export_department_statistics(
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_active_user),
    parent_department_id: Optional[int] = Query(None, description="父部门ID"),
    level: Optional[int] = Query(None, description="部门层级"),
    merchant_id: Optional[int] = Query(None, description="商户ID"),
    start_date: Optional[str] = Query(None, description="开始日期"),
    end_date: Optional[str] = Query(None, description="结束日期"),
    time_range: Optional[str] = Query("today", description="时间范围"),
):
    """
    导出部门统计数据为Excel文件
    
    权限要求:
    - "reconciliation:export": 导出对账台数据权限
    """
    try:
        # 权限检查
        permission_service = PermissionService(db)
        has_permission = permission_service.check_user_permission(
            current_user, "api:reconciliation:export"
        )
        if not has_permission:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="没有导出对账台数据的权限",
            )

        # 【安全修复】：强制商户隔离检查
        if not current_user.is_superuser:
            if not current_user.merchant_id:
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail="用户没有商户信息，无法访问对账台数据",
                )

        # 解析日期参数
        parsed_start_date = parse_date_param(start_date)
        parsed_end_date = parse_date_param(end_date)

        reconciliation_service = ReconciliationService(db)
        excel_data = await reconciliation_service.export_department_statistics(
            current_user=current_user,
            parent_department_id=parent_department_id,
            level=level,
            merchant_id=merchant_id,
            start_date=parsed_start_date,
            end_date=parsed_end_date,
            time_range=time_range,
        )
        
        # 设置响应头
        headers = {
            'Content-Disposition': f'attachment; filename="department_statistics_{datetime.now().strftime("%Y%m%d_%H%M%S")}.xlsx"',
            'Content-Type': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        }
        
        return Response(content=excel_data, headers=headers)
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"导出部门统计数据失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"导出部门统计数据失败: {str(e)}"
        )


@router.get("/export/ck/{department_id}", response_class=Response)
async def export_ck_statistics(
    department_id: int,
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_active_user),
    start_date: Optional[str] = Query(None, description="开始日期"),
    end_date: Optional[str] = Query(None, description="结束日期"),
    time_range: Optional[str] = Query("today", description="时间范围"),
    min_success_count: Optional[int] = Query(1, description="最小成功笔数筛选"),
    ck_status: Optional[str] = Query("all", description="CK状态筛选"),
):
    """
    导出CK统计数据为Excel文件
    
    权限要求:
    - "reconciliation:export": 导出对账台数据权限
    """
    try:
        # 权限检查
        permission_service = PermissionService(db)
        has_permission = permission_service.check_user_permission(
            current_user, "api:reconciliation:export"
        )
        if not has_permission:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="没有导出对账台数据的权限",
            )

        # 【安全修复】：强制商户隔离检查
        if not current_user.is_superuser:
            if not current_user.merchant_id:
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail="用户没有商户信息，无法访问对账台数据",
                )

        # 解析日期参数
        parsed_start_date = parse_date_param(start_date)
        parsed_end_date = parse_date_param(end_date)

        reconciliation_service = ReconciliationService(db)
        excel_data = await reconciliation_service.export_ck_statistics(
            current_user=current_user,
            department_id=department_id,
            start_date=parsed_start_date,
            end_date=parsed_end_date,
            time_range=time_range,
            min_success_count=min_success_count,
            ck_status=ck_status,
        )
        
        # 设置响应头
        headers = {
            'Content-Disposition': f'attachment; filename="ck_statistics_{department_id}_{datetime.now().strftime("%Y%m%d_%H%M%S")}.xlsx"',
            'Content-Type': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        }
        
        return Response(content=excel_data, headers=headers)
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"导出CK统计数据失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"导出CK统计数据失败: {str(e)}"
        )


@router.get("/export/records/{ck_id}", response_class=Response)
async def export_binding_records(
    ck_id: int,
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_active_user),
    start_date: Optional[str] = Query(None, description="开始日期"),
    end_date: Optional[str] = Query(None, description="结束日期"),
    time_range: Optional[str] = Query("today", description="时间范围"),
    search_keyword: Optional[str] = Query(None, description="搜索关键词"),
):
    """
    导出绑卡记录为Excel文件
    
    权限要求:
    - "reconciliation:export": 导出对账台数据权限
    """
    try:
        # 权限检查
        permission_service = PermissionService(db)
        has_permission = permission_service.check_user_permission(
            current_user, "api:reconciliation:export"
        )
        if not has_permission:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="没有导出对账台数据的权限",
            )

        # 【安全修复】：强制商户隔离检查
        if not current_user.is_superuser:
            if not current_user.merchant_id:
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail="用户没有商户信息，无法访问对账台数据",
                )

        # 解析日期参数
        parsed_start_date = parse_date_param(start_date)
        parsed_end_date = parse_date_param(end_date)

        reconciliation_service = ReconciliationService(db)
        excel_data = await reconciliation_service.export_binding_records(
            current_user=current_user,
            ck_id=ck_id,
            start_date=parsed_start_date,
            end_date=parsed_end_date,
            time_range=time_range,
            search_keyword=search_keyword,
        )
        
        # 设置响应头
        headers = {
            'Content-Disposition': f'attachment; filename="binding_records_{ck_id}_{datetime.now().strftime("%Y%m%d_%H%M%S")}.xlsx"',
            'Content-Type': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        }
        
        return Response(content=excel_data, headers=headers)
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"导出绑卡记录失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"导出绑卡记录失败: {str(e)}"
        )
