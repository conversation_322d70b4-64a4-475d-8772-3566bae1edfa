#!/usr/bin/env python3
"""
检查CK数据状态
"""

import sys
import os

# 添加项目路径
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.insert(0, project_root)

from app.api.deps import get_db
from app.models.walmart_ck import WalmartCK

def check_ck_data():
    """检查CK数据"""
    db = next(get_db())
    
    try:
        # 查询所有CK
        all_cks = db.query(WalmartCK).filter(WalmartCK.is_deleted == False).all()
        
        print(f"总CK数量: {len(all_cks)}")
        print("\n所有CK详情:")
        print("-" * 80)
        
        active_count = 0
        merchant_groups = {}
        
        for ck in all_cks:
            status = "✅ 活跃" if ck.active else "❌ 禁用"
            usage_ratio = (ck.bind_count / ck.total_limit * 100) if ck.total_limit > 0 else 0
            
            print(f"CK {ck.id:2d}: {status} | 商户 {ck.merchant_id} | "
                  f"使用 {ck.bind_count:2d}/{ck.total_limit:2d} ({usage_ratio:5.1f}%) | "
                  f"部门 {ck.department_id or 'N/A'}")
            
            if ck.active:
                active_count += 1
            
            # 按商户分组
            if ck.merchant_id not in merchant_groups:
                merchant_groups[ck.merchant_id] = {'total': 0, 'active': 0, 'cks': []}
            merchant_groups[ck.merchant_id]['total'] += 1
            merchant_groups[ck.merchant_id]['cks'].append(ck.id)
            if ck.active:
                merchant_groups[ck.merchant_id]['active'] += 1
        
        print("-" * 80)
        print(f"活跃CK数量: {active_count}/{len(all_cks)}")
        
        print("\n按商户分组:")
        print("-" * 50)
        for merchant_id, info in merchant_groups.items():
            print(f"商户 {merchant_id}: {info['active']}/{info['total']} 活跃, CK IDs: {info['cks']}")
        
        # 检查是否有足够的CK进行负载均衡测试
        print("\n负载均衡测试建议:")
        print("-" * 50)
        
        for merchant_id, info in merchant_groups.items():
            if info['active'] == 0:
                print(f"❌ 商户 {merchant_id}: 没有活跃CK，无法进行测试")
            elif info['active'] == 1:
                print(f"⚠️ 商户 {merchant_id}: 只有1个活跃CK，负载均衡测试会选择同一个CK")
            else:
                print(f"✅ 商户 {merchant_id}: 有{info['active']}个活跃CK，可以进行负载均衡测试")
        
    finally:
        db.close()


def activate_more_cks():
    """激活更多CK用于测试"""
    db = next(get_db())
    
    try:
        # 查找被禁用的CK
        inactive_cks = db.query(WalmartCK).filter(
            WalmartCK.is_deleted == False,
            WalmartCK.active == False
        ).all()
        
        print(f"找到 {len(inactive_cks)} 个被禁用的CK")
        
        if len(inactive_cks) > 0:
            print("\n是否要激活一些CK用于测试？(y/n): ", end="")
            choice = input().strip().lower()
            
            if choice == 'y':
                # 激活前几个CK
                activated = 0
                for ck in inactive_cks[:3]:  # 最多激活3个
                    if ck.bind_count < ck.total_limit:  # 确保还有使用次数
                        ck.active = True
                        activated += 1
                        print(f"激活CK {ck.id} (商户 {ck.merchant_id})")
                
                if activated > 0:
                    db.commit()
                    print(f"成功激活 {activated} 个CK")
                else:
                    print("没有合适的CK可以激活")
            else:
                print("跳过激活")
        else:
            print("没有被禁用的CK")
    
    except Exception as e:
        db.rollback()
        print(f"激活CK失败: {e}")
    finally:
        db.close()


if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description="检查CK数据状态")
    parser.add_argument("--activate", action="store_true", help="激活更多CK用于测试")
    
    args = parser.parse_args()
    
    check_ck_data()
    
    if args.activate:
        print("\n" + "="*50)
        activate_more_cks()
