<template>
    <div class="system-params">
        <el-card class="box-card">
            <template #header>
                <div class="card-header">
                    <span>系统参数设置</span>
                    <div class="header-operations">
                        <el-button type="primary" @click="saveSettings">保存设置</el-button>
                        <el-button link v-if="merchantStore.isMerchantMode" @click="resetToGlobalSettings">重置为全局设置</el-button>
                    </div>
                </div>
            </template>

            <!-- 租户上下文提示 -->
            <div v-if="merchantStore.isMerchantMode" class="merchant-context-tip">
                <el-alert type="info" :closable="false">
                    正在编辑租户 "{{ merchantStore.currentMerchantName }}" 的系统参数设置
                </el-alert>
            </div>

            <el-form :model="systemParams" label-width="180px" :rules="rules" ref="formRef" v-loading="loading">
                <el-form-item label="API请求超时时间(秒)" prop="apiTimeout">
                    <el-input-number v-model="systemParams.apiTimeout" :min="1" :max="60" />
                </el-form-item>
                <el-form-item label="绑卡请求限流(次/分钟)" prop="requestLimit">
                    <el-input-number v-model="systemParams.requestLimit" :min="1" :max="1000" />
                </el-form-item>
                <el-form-item label="数据缓存时间(分钟)" prop="cacheTime">
                    <el-input-number v-model="systemParams.cacheTime" :min="1" :max="1440" />
                </el-form-item>
                <el-form-item label="绑卡记录保留天数" prop="recordRetentionDays">
                    <el-input-number v-model="systemParams.recordRetentionDays" :min="1" :max="1000" />
                </el-form-item>
                <el-form-item label="默认导出记录数量上限" prop="exportLimit">
                    <el-input-number v-model="systemParams.exportLimit" :min="100" :max="10000" />
                </el-form-item>
                <el-form-item label="系统维护模式" prop="maintenanceMode" v-if="!merchantStore.isMerchantMode">
                    <el-switch v-model="systemParams.maintenanceMode" />
                </el-form-item>
                <el-form-item label="维护模式消息" prop="maintenanceMessage"
                    v-if="systemParams.maintenanceMode && !merchantStore.isMerchantMode">
                    <el-input v-model="systemParams.maintenanceMessage" type="textarea" :rows="2"
                        placeholder="系统维护中，请稍后再试..." />
                </el-form-item>
                <el-form-item label="租户每日绑卡配额" prop="dailyBindQuota" v-if="merchantStore.isMerchantMode">
                    <el-input-number v-model="systemParams.dailyBindQuota" :min="1" :max="10000" />
                </el-form-item>
                <el-form-item label="接口访问限流" prop="apiRateLimit" v-if="merchantStore.isMerchantMode">
                    <el-input-number v-model="systemParams.apiRateLimit" :min="1" :max="1000" />
                    <span class="param-description">次/分钟</span>
                </el-form-item>
            </el-form>

            <!-- 参数说明 -->
            <div class="params-description">
                <el-divider content-position="left">参数说明</el-divider>
                <ul>
                    <li><strong>API请求超时时间</strong>: 系统默认API请求超时时间，超过此时间未响应的请求将被取消</li>
                    <li><strong>绑卡请求限流</strong>: 每分钟允许的最大绑卡请求数量</li>
                    <li><strong>数据缓存时间</strong>: 系统数据缓存的有效期，减少数据库压力</li>
                    <li><strong>绑卡记录保留天数</strong>: 超过此天数的绑卡记录将被归档</li>
                    <li><strong>默认导出记录数量上限</strong>: 一次导出允许的最大记录数</li>
                    <li v-if="!merchantStore.isMerchantMode"><strong>系统维护模式</strong>: 开启后除登录接口外的所有接口将返回维护中状态</li>
                    <li v-if="merchantStore.isMerchantMode"><strong>租户每日绑卡配额</strong>: 该租户每天允许的最大绑卡数量</li>
                    <li v-if="merchantStore.isMerchantMode"><strong>接口访问限流</strong>: 该租户每分钟允许的最大API请求数量</li>
                </ul>
            </div>
        </el-card>
    </div>
</template>

<script setup>
import { ref, reactive, onMounted, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useSystemStore } from '@/store/modules/system'
import { useMerchantStore } from '@/store/modules/merchant'
import { systemApi } from '@/api/modules/system'

const formRef = ref(null)
const systemStore = useSystemStore()
const merchantStore = useMerchantStore()
const loading = ref(false)

// 全局参数设置
const globalParams = reactive({ ...systemStore.systemParams })

// 当前参数设置（会根据租户上下文变化）
const systemParams = reactive({ ...globalParams })

// 租户特定参数设置缓存
const merchantParamsCache = new Map()

// 添加租户特定参数
if (merchantStore.isMerchantMode) {
    systemParams.dailyBindQuota = 1000
    systemParams.apiRateLimit = 60
}

const rules = {
    apiTimeout: [
        { required: true, message: '请输入API超时时间', trigger: 'blur' },
        { type: 'number', min: 1, max: 60, message: '超时时间必须在1-60秒之间', trigger: 'blur' }
    ],
    requestLimit: [
        { required: true, message: '请输入请求限流数', trigger: 'blur' },
        { type: 'number', min: 1, max: 1000, message: '限流数必须在1-1000之间', trigger: 'blur' }
    ],
    cacheTime: [
        { required: true, message: '请输入缓存时间', trigger: 'blur' },
        { type: 'number', min: 1, max: 1440, message: '缓存时间必须在1-1440分钟之间', trigger: 'blur' }
    ],
    recordRetentionDays: [
        { required: true, message: '请输入记录保留天数', trigger: 'blur' },
        { type: 'number', min: 1, max: 1000, message: '保留天数必须在1-1000天之间', trigger: 'blur' }
    ],
    exportLimit: [
        { required: true, message: '请输入导出记录数量上限', trigger: 'blur' },
        { type: 'number', min: 100, max: 10000, message: '导出上限必须在100-10000条之间', trigger: 'blur' }
    ],
    maintenanceMessage: [
        { required: true, message: '请输入维护模式消息', trigger: 'blur' }
    ],
    dailyBindQuota: [
        { required: true, message: '请输入每日绑卡配额', trigger: 'blur' },
        { type: 'number', min: 1, max: 10000, message: '配额必须在1-10000之间', trigger: 'blur' }
    ],
    apiRateLimit: [
        { required: true, message: '请输入接口访问限流', trigger: 'blur' },
        { type: 'number', min: 1, max: 1000, message: '限流必须在1-1000之间', trigger: 'blur' }
    ]
}

// 监听租户变化
watch(() => merchantStore.currentMerchant, async () => {
    // 保存当前设置到缓存
    saveToCacheIfNeeded()

    // 加载新租户的设置
    await loadParamsForCurrentMerchant()
}, { immediate: false })

// 从当前租户中加载参数设置
async function loadParamsForCurrentMerchant() {
    loading.value = true
    try {
        if (merchantStore.isMerchantMode) {
            // 尝试从缓存加载
            if (merchantParamsCache.has(merchantStore.currentMerchantId)) {
                Object.assign(systemParams, merchantParamsCache.get(merchantStore.currentMerchantId))
                loading.value = false
                return
            }

            // 实际API调用获取租户特定设置
            try {
                // 获取租户特定的系统参数
                const response = await systemApi.getMerchantSystemParams(merchantStore.currentMerchantId)
                const merchantSpecificParams = {
                    ...globalParams,
                    ...response
                }

                // 更新参数
                Object.assign(systemParams, merchantSpecificParams)

                // 缓存参数
                merchantParamsCache.set(merchantStore.currentMerchantId, { ...merchantSpecificParams })
            } catch (error) {
                console.error('获取租户系统参数失败:', error)
                ElMessage.warning('获取租户系统参数失败，使用全局默认参数')
                // 失败时使用全局参数
                Object.assign(systemParams, globalParams)
            }
        } else {
            // 全局视图，使用全局设置
            Object.assign(systemParams, globalParams)
        }
    } catch (error) {
        console.error('加载系统参数失败:', error)
        ElMessage.error('加载系统参数失败')
    } finally {
        loading.value = false
    }
}

// 保存当前设置到缓存（如果是租户视图）
function saveToCacheIfNeeded() {
    if (merchantStore.isMerchantMode && merchantStore.currentMerchantId) {
        merchantParamsCache.set(merchantStore.currentMerchantId, { ...systemParams })
    }
}

// 保存设置
const saveSettings = async () => {
    if (!formRef.value) return

    await formRef.value.validate(async (valid) => {
        if (valid) {
            loading.value = true
            try {
                if (merchantStore.isMerchantMode) {
                    // 实际API调用保存租户设置
                    await systemApi.updateMerchantSystemParams(merchantStore.currentMerchantId, systemParams)

                    // 更新缓存
                    merchantParamsCache.set(merchantStore.currentMerchantId, { ...systemParams })

                    ElMessage.success('租户系统参数保存成功')
                } else {
                    // 保存全局设置
                    await systemStore.updateSystemParams(systemParams)

                    // 同步更新本地全局参数
                    Object.assign(globalParams, systemParams)

                    ElMessage.success('全局系统参数保存成功')
                }
            } catch (error) {
                console.error('保存系统参数失败:', error)
                ElMessage.error('保存系统参数失败: ' + (error.message || '未知错误'))
            } finally {
                loading.value = false
            }
        } else {
            ElMessage.warning('请修正表单中的错误')
        }
    })
}

// 重置为全局设置
const resetToGlobalSettings = () => {
    ElMessageBox.confirm('确定要重置为全局设置吗？这将丢失当前租户的自定义设置。', '确认操作', {
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        type: 'warning'
    })
        .then(async () => {
            // 重置参数
            const resetParams = {
                ...globalParams,
                dailyBindQuota: 1000,
                apiRateLimit: 60
            }

            // 重置为全局设置加默认租户特定参数
            Object.assign(systemParams, resetParams)

            // 更新缓存
            merchantParamsCache.set(merchantStore.currentMerchantId, { ...resetParams })

            ElMessage.success('已重置为全局设置')
        })
        .catch(() => {
            // 用户取消操作
        })
}

// 在组件挂载时获取系统参数
onMounted(async () => {
    try {
        await systemStore.fetchSystemParams()
        // 获取成功后更新本地全局参数对象
        Object.assign(globalParams, systemStore.systemParams)

        // 加载当前租户的参数
        await loadParamsForCurrentMerchant()
    } catch (error) {
        console.error('获取系统参数失败', error)
        ElMessage.error('获取系统参数失败')
    }
})
</script>

<style scoped>
.system-params {
    width: 100%;
}

.card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.header-operations {
    display: flex;
    gap: 10px;
}

.merchant-context-tip {
    margin-bottom: 15px;
}

.params-description {
    margin-top: 20px;
}

.params-description ul {
    padding-left: 20px;
    line-height: 1.8;
    color: #606266;
}

.param-description {
    margin-left: 10px;
    color: #909399;
    font-size: 13px;
}
</style>