from typing import List, Optional, Dict, Any
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_, func, desc
from datetime import datetime, date

from app.crud.base import CRUDBase
from app.models.card_record import CardRecord
from app.schemas.card_record import CardRecord<PERSON>reate, CardRecordUpdate


class CRUDCardRecord(CRUDBase[CardRecord, CardRecordCreate, CardRecordUpdate]):
    """卡记录CRUD操作 - 支持新的数据隔离和CK统计功能"""

    def get_by_request_id(self, db: Session, request_id: str) -> Optional[CardRecord]:
        """根据请求ID获取记录"""
        return db.query(self.model).filter(self.model.request_id == request_id).first()

    def get_multi_by_merchant(
        self,
        db: Session,
        merchant_id: int,
        *,
        skip: int = 0,
        limit: int = 100,
        filters: Optional[Dict[str, Any]] = None,
    ) -> List[CardRecord]:
        """获取商户的卡记录列表（支持数据隔离） - 优化版本，避免N+1查询"""
        from sqlalchemy.orm import selectinload

        # 使用预加载避免N+1查询
        query = db.query(self.model).options(
            selectinload(self.model.merchant),
            selectinload(self.model.department),
            selectinload(self.model.walmart_ck)
        ).filter(self.model.merchant_id == merchant_id)

        # 应用过滤条件
        query = self._apply_filters(query, filters)

        return query.order_by(desc(self.model.created_at)).offset(skip).limit(limit).all()

    def get_multi_by_department(
        self,
        db: Session,
        department_id: int,
        *,
        skip: int = 0,
        limit: int = 100,
        filters: Optional[Dict[str, Any]] = None,
    ) -> List[CardRecord]:
        """获取部门的卡记录列表（支持部门级数据隔离）"""
        query = db.query(self.model).filter(self.model.department_id == department_id)

        # 应用过滤条件
        query = self._apply_filters(query, filters)

        return query.order_by(desc(self.model.created_at)).offset(skip).limit(limit).all()

    def get_multi_by_walmart_ck(
        self,
        db: Session,
        walmart_ck_id: int,
        *,
        skip: int = 0,
        limit: int = 100,
        filters: Optional[Dict[str, Any]] = None,
    ) -> List[CardRecord]:
        """获取某个CK的卡记录列表（用于CK使用分析） - 优化版本，避免N+1查询"""
        from sqlalchemy.orm import selectinload

        # 使用预加载避免N+1查询
        query = db.query(self.model).options(
            selectinload(self.model.merchant),
            selectinload(self.model.department),
            selectinload(self.model.walmart_ck)
        ).filter(self.model.walmart_ck_id == walmart_ck_id)

        # 应用过滤条件
        query = self._apply_filters(query, filters)

        return query.order_by(desc(self.model.created_at)).offset(skip).limit(limit).all()

    def _apply_filters(self, query, filters: Optional[Dict[str, Any]]):
        """应用通用过滤条件 - 使用过滤器工厂模式"""
        from app.core.filter_factory import create_card_record_filter_factory

        if not filters:
            return query

        # 创建过滤器工厂
        filter_factory = create_card_record_filter_factory(self.model)

        # 应用基本过滤条件
        query = filter_factory.apply_filters(query, filters)

        # 应用创建时间范围过滤
        query = filter_factory.apply_created_time_filters(query, filters)

        return query

    # ========================================
    # CK统计相关方法
    # ========================================

    def get_ck_statistics(
        self,
        db: Session,
        walmart_ck_id: int,
        start_date: Optional[date] = None,
        end_date: Optional[date] = None,
    ) -> Dict[str, Any]:
        """获取CK绑卡统计信息"""
        query = db.query(self.model).filter(self.model.walmart_ck_id == walmart_ck_id)

        # 时间范围过滤
        if start_date:
            query = query.filter(func.date(self.model.created_at) >= start_date)
        if end_date:
            query = query.filter(func.date(self.model.created_at) <= end_date)

        # 统计各种状态的数量
        total_count = query.count()
        success_count = query.filter(self.model.status == 'success').count()
        failed_count = query.filter(self.model.status == 'failed').count()
        pending_count = query.filter(self.model.status == 'pending').count()
        processing_count = query.filter(self.model.status == 'processing').count()

        # 计算成功率
        success_rate = (success_count / total_count * 100) if total_count > 0 else 0

        return {
            "walmart_ck_id": walmart_ck_id,
            "total_count": total_count,
            "success_count": success_count,
            "failed_count": failed_count,
            "pending_count": pending_count,
            "processing_count": processing_count,
            "success_rate": round(success_rate, 2),
            "start_date": start_date,
            "end_date": end_date,
        }

    def get_ck_daily_statistics(
        self,
        db: Session,
        walmart_ck_id: int,
        days: int = 30,
    ) -> List[Dict[str, Any]]:
        """获取CK每日绑卡统计"""
        from datetime import timedelta
        # 修复时区问题：使用上海时区的当前日期
        from app.utils.time_utils import get_current_time

        end_date = get_current_time().date()
        start_date = end_date - timedelta(days=days-1)

        # 按日期分组统计
        query = db.query(
            func.date(self.model.created_at).label('date'),
            func.count(self.model.id).label('total_count'),
            func.sum(func.case((self.model.status == 'success', 1), else_=0)).label('success_count'),
            func.sum(func.case((self.model.status == 'failed', 1), else_=0)).label('failed_count'),
        ).filter(
            self.model.walmart_ck_id == walmart_ck_id,
            func.date(self.model.created_at) >= start_date,
            func.date(self.model.created_at) <= end_date,
        ).group_by(func.date(self.model.created_at)).order_by(func.date(self.model.created_at))

        results = []
        for row in query.all():
            success_rate = (row.success_count / row.total_count * 100) if row.total_count > 0 else 0
            results.append({
                "date": row.date.isoformat(),
                "total_count": row.total_count,
                "success_count": row.success_count,
                "failed_count": row.failed_count,
                "success_rate": round(success_rate, 2),
            })

        return results

    def get_ck_ranking(
        self,
        db: Session,
        merchant_id: Optional[int] = None,
        department_id: Optional[int] = None,
        start_date: Optional[date] = None,
        end_date: Optional[date] = None,
        limit: int = 10,
    ) -> List[Dict[str, Any]]:
        """获取CK绑卡成功数量排行榜"""
        query = db.query(
            self.model.walmart_ck_id,
            func.count(self.model.id).label('total_count'),
            func.sum(func.case((self.model.status == 'success', 1), else_=0)).label('success_count'),
        ).filter(self.model.walmart_ck_id.isnot(None))

        # 数据隔离过滤
        if merchant_id:
            query = query.filter(self.model.merchant_id == merchant_id)
        if department_id:
            query = query.filter(self.model.department_id == department_id)

        # 时间范围过滤
        if start_date:
            query = query.filter(func.date(self.model.created_at) >= start_date)
        if end_date:
            query = query.filter(func.date(self.model.created_at) <= end_date)

        query = query.group_by(self.model.walmart_ck_id).order_by(
            desc(func.sum(func.case((self.model.status == 'success', 1), else_=0)))
        ).limit(limit)

        results = []
        for row in query.all():
            success_rate = (row.success_count / row.total_count * 100) if row.total_count > 0 else 0
            results.append({
                "walmart_ck_id": row.walmart_ck_id,
                "total_count": row.total_count,
                "success_count": row.success_count,
                "success_rate": round(success_rate, 2),
            })

        return results


card_record = CRUDCardRecord(CardRecord)
