-- ========================================
-- 沃尔玛绑卡系统主键类型升级脚本（生产环境安全版本）
-- 将INT类型主键升级为BIGINT类型
--
-- 安全机制：
-- 1. 检查数据库初始化状态，避免在生产环境重复执行
-- 2. 主键升级是不可逆操作，需要严格控制执行条件
-- 3. 添加执行标记，防止重复执行
-- 4. 分阶段执行，确保数据完整性
-- ========================================

-- 设置连接字符集
SET NAMES utf8mb4 COLLATE utf8mb4_unicode_ci;
SET time_zone = '+08:00';

USE `walmart_card_db`;

-- 设置安全模式
SET SESSION sql_mode = 'STRICT_TRANS_TABLES,NO_ZERO_DATE,NO_ZERO_IN_DATE,ERROR_FOR_DIVISION_BY_ZERO';

-- 创建迁移日志表（如果不存在）
CREATE TABLE IF NOT EXISTS `migration_logs` (
    `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '日志ID',
    `migration_name` varchar(100) NOT NULL COMMENT '迁移名称',
    `status` varchar(20) NOT NULL DEFAULT 'started' COMMENT '状态',
    `message` text NULL COMMENT '消息',
    `data_summary` text NULL COMMENT '数据摘要',
    `created_at` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '创建时间',
    `completed_at` datetime(3) NULL COMMENT '完成时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_migration_logs_name` (`migration_name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='迁移日志表';

-- 检查是否已经执行过主键升级
SET @primary_key_upgrade_exists = (SELECT COUNT(*) FROM `migration_logs` WHERE `migration_name` = 'primary_key_upgrade_v2.1.0');

-- 只在未执行过的情况下进行主键升级
SET @should_upgrade_keys = (@primary_key_upgrade_exists = 0);

-- 记录升级开始
INSERT IGNORE INTO `migration_logs` (`migration_name`, `status`, `message`, `created_at`)
VALUES ('primary_key_upgrade_v2.1.0', 'started', '开始主键类型升级（INT → BIGINT）', NOW(3));

-- 记录升级开始时间（仅在执行升级时显示）
SET @sql_start_time = IF(@should_upgrade_keys = 1,
    'SELECT CONCAT(''升级开始时间: '', NOW()) AS upgrade_start;',
    'SELECT ''主键升级跳过 - 已执行过升级'' as status;'
);

PREPARE stmt_start_time FROM @sql_start_time;
EXECUTE stmt_start_time;
DEALLOCATE PREPARE stmt_start_time;

-- ========================================
-- 阶段1: 升级核心业务表（高优先级）
-- ========================================

-- 条件执行阶段1升级（仅在需要时执行）
SET @sql_stage1 = IF(@should_upgrade_keys = 1,
    'START TRANSACTION; SET FOREIGN_KEY_CHECKS = 0; SELECT ''正在升级用户表...'' AS status; ALTER TABLE users MODIFY COLUMN id BIGINT AUTO_INCREMENT COMMENT ''用户ID''; SELECT ''正在升级商户表...'' AS status; ALTER TABLE merchants MODIFY COLUMN id BIGINT AUTO_INCREMENT COMMENT ''商户ID''; SELECT ''正在升级部门表...'' AS status; ALTER TABLE departments MODIFY COLUMN id BIGINT AUTO_INCREMENT COMMENT ''部门ID''; ALTER TABLE departments MODIFY COLUMN merchant_id BIGINT NOT NULL COMMENT ''所属商户ID''; ALTER TABLE departments MODIFY COLUMN parent_id BIGINT NULL COMMENT ''父部门ID''; ALTER TABLE departments MODIFY COLUMN manager_id BIGINT NULL COMMENT ''部门经理ID''; ALTER TABLE departments MODIFY COLUMN created_by BIGINT NULL COMMENT ''创建者ID''; SELECT ''正在升级沃尔玛CK表...'' AS status; ALTER TABLE walmart_ck MODIFY COLUMN id BIGINT AUTO_INCREMENT COMMENT ''主键ID''; ALTER TABLE walmart_ck MODIFY COLUMN created_by BIGINT NULL COMMENT ''创建者用户ID''; ALTER TABLE walmart_ck MODIFY COLUMN merchant_id BIGINT NULL COMMENT ''所属商户ID''; ALTER TABLE walmart_ck MODIFY COLUMN department_id BIGINT NULL COMMENT ''所属部门ID''; SET FOREIGN_KEY_CHECKS = 1; COMMIT;',
    'SELECT ''跳过阶段1升级 - 已执行过升级'' as status;'
);

-- 注意：由于MySQL的PREPARE语句限制，我们需要分别执行每个阶段
-- 如果需要升级，则执行实际的升级操作
IF @should_upgrade_keys = 1 THEN
    START TRANSACTION;

    -- 临时禁用外键检查
    SET FOREIGN_KEY_CHECKS = 0;

    -- 1.1 升级用户表
    SELECT '正在升级用户表...' AS status;
    ALTER TABLE users MODIFY COLUMN id BIGINT AUTO_INCREMENT COMMENT '用户ID';

    -- 1.2 升级商户表
    SELECT '正在升级商户表...' AS status;
    ALTER TABLE merchants MODIFY COLUMN id BIGINT AUTO_INCREMENT COMMENT '商户ID';

    -- 1.3 升级部门表
    SELECT '正在升级部门表...' AS status;
    ALTER TABLE departments MODIFY COLUMN id BIGINT AUTO_INCREMENT COMMENT '部门ID';
    ALTER TABLE departments MODIFY COLUMN merchant_id BIGINT NOT NULL COMMENT '所属商户ID';
    ALTER TABLE departments MODIFY COLUMN parent_id BIGINT NULL COMMENT '父部门ID';
    ALTER TABLE departments MODIFY COLUMN manager_id BIGINT NULL COMMENT '部门经理ID';
    ALTER TABLE departments MODIFY COLUMN created_by BIGINT NULL COMMENT '创建者ID';

    -- 1.4 升级沃尔玛CK表
    SELECT '正在升级沃尔玛CK表...' AS status;
    ALTER TABLE walmart_ck MODIFY COLUMN id BIGINT AUTO_INCREMENT COMMENT '主键ID';
    ALTER TABLE walmart_ck MODIFY COLUMN created_by BIGINT NULL COMMENT '创建者用户ID';
    ALTER TABLE walmart_ck MODIFY COLUMN merchant_id BIGINT NULL COMMENT '所属商户ID';
    ALTER TABLE walmart_ck MODIFY COLUMN department_id BIGINT NULL COMMENT '所属部门ID';

    -- 重新启用外键检查
    SET FOREIGN_KEY_CHECKS = 1;

    COMMIT;
ELSE
    SELECT '跳过阶段1升级 - 已执行过升级' as status;
END IF;

-- ========================================
-- 阶段2: 升级权限管理表（中优先级）
-- ========================================

-- 条件执行阶段2升级（仅在需要时执行）
IF @should_upgrade_keys = 1 THEN
    START TRANSACTION;

    SET FOREIGN_KEY_CHECKS = 0;

    -- 2.1 升级角色表
    SELECT '正在升级角色表...' AS status;
    ALTER TABLE roles MODIFY COLUMN id BIGINT AUTO_INCREMENT COMMENT '角色ID';
    ALTER TABLE roles MODIFY COLUMN created_by BIGINT NULL COMMENT '创建者ID';

    -- 2.2 升级权限表
    SELECT '正在升级权限表...' AS status;
    ALTER TABLE permissions MODIFY COLUMN id BIGINT AUTO_INCREMENT COMMENT '权限ID';

    -- 2.3 升级菜单表
    SELECT '正在升级菜单表...' AS status;
    ALTER TABLE menus MODIFY COLUMN id BIGINT AUTO_INCREMENT COMMENT '菜单ID';
    ALTER TABLE menus MODIFY COLUMN parent_id BIGINT NULL COMMENT '父菜单ID';

    -- 2.4 升级沃尔玛服务器配置表
    SELECT '正在升级沃尔玛服务器配置表...' AS status;
    ALTER TABLE walmart_server MODIFY COLUMN id BIGINT AUTO_INCREMENT COMMENT '配置ID';

    SET FOREIGN_KEY_CHECKS = 1;

    COMMIT;
ELSE
    SELECT '跳过阶段2升级 - 已执行过升级' as status;
END IF;

-- ========================================
-- 阶段3: 升级关联表（中优先级）
-- ========================================

START TRANSACTION;

SET FOREIGN_KEY_CHECKS = 0;

-- 3.1 升级用户角色关联表
SELECT '正在升级用户角色关联表...' AS status;
ALTER TABLE user_roles MODIFY COLUMN id BIGINT AUTO_INCREMENT COMMENT '关联ID';
ALTER TABLE user_roles MODIFY COLUMN user_id BIGINT NOT NULL COMMENT '用户ID';
ALTER TABLE user_roles MODIFY COLUMN role_id BIGINT NOT NULL COMMENT '角色ID';

-- 3.2 升级用户权限关联表
SELECT '正在升级用户权限关联表...' AS status;
ALTER TABLE user_permissions MODIFY COLUMN id BIGINT AUTO_INCREMENT COMMENT '关联ID';
ALTER TABLE user_permissions MODIFY COLUMN user_id BIGINT NOT NULL COMMENT '用户ID';
ALTER TABLE user_permissions MODIFY COLUMN permission_id BIGINT NOT NULL COMMENT '权限ID';

-- 3.3 升级角色权限关联表
SELECT '正在升级角色权限关联表...' AS status;
ALTER TABLE role_permissions MODIFY COLUMN id BIGINT AUTO_INCREMENT COMMENT '关联ID';

-- 3.4 升级角色菜单关联表
SELECT '正在升级角色菜单关联表...' AS status;
ALTER TABLE role_menus MODIFY COLUMN id BIGINT AUTO_INCREMENT COMMENT '关联ID';

-- 3.5 升级菜单权限关联表
SELECT '正在升级菜单权限关联表...' AS status;
ALTER TABLE menu_permissions MODIFY COLUMN id BIGINT AUTO_INCREMENT COMMENT '关联ID';
ALTER TABLE menu_permissions MODIFY COLUMN menu_id BIGINT NOT NULL COMMENT '菜单ID';
ALTER TABLE menu_permissions MODIFY COLUMN permission_id BIGINT NOT NULL COMMENT '权限ID';

SET FOREIGN_KEY_CHECKS = 1;

COMMIT;

-- ========================================
-- 阶段4: 升级业务表中的外键字段
-- ========================================

START TRANSACTION;

SET FOREIGN_KEY_CHECKS = 0;

-- 4.1 升级卡记录表中的外键字段
SELECT '正在升级卡记录表外键字段...' AS status;
ALTER TABLE card_records MODIFY COLUMN merchant_id BIGINT NOT NULL COMMENT '商家ID';
ALTER TABLE card_records MODIFY COLUMN department_id BIGINT NULL COMMENT '所属部门ID（数据隔离）';
ALTER TABLE card_records MODIFY COLUMN walmart_ck_id BIGINT NULL COMMENT '使用的沃尔玛CK ID（关键字段：直接关联CK用于统计）';

-- 4.2 升级绑卡日志表中的外键字段
SELECT '正在升级绑卡日志表外键字段...' AS status;
ALTER TABLE binding_logs MODIFY COLUMN walmart_ck_id BIGINT NULL COMMENT '沃尔玛CKID（关键字段：用于统计CK绑卡成功数）';

-- 4.3 升级用户表中的外键字段
SELECT '正在升级用户表外键字段...' AS status;
ALTER TABLE users MODIFY COLUMN merchant_id BIGINT NULL COMMENT '所属商家ID（兼容字段）';
ALTER TABLE users MODIFY COLUMN department_id BIGINT NULL COMMENT '所属部门ID';

SET FOREIGN_KEY_CHECKS = 1;

COMMIT;

-- ========================================
-- 阶段5: 数据完整性验证
-- ========================================

SELECT '开始数据完整性验证...' AS status;

-- 验证主键自增值
SELECT 
    'users' AS table_name,
    MAX(id) AS max_id,
    AUTO_INCREMENT AS next_id
FROM users, INFORMATION_SCHEMA.TABLES 
WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'users'

UNION ALL

SELECT 
    'merchants' AS table_name,
    MAX(id) AS max_id,
    AUTO_INCREMENT AS next_id
FROM merchants, INFORMATION_SCHEMA.TABLES 
WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'merchants'

UNION ALL

SELECT 
    'departments' AS table_name,
    MAX(id) AS max_id,
    AUTO_INCREMENT AS next_id
FROM departments, INFORMATION_SCHEMA.TABLES 
WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'departments'

UNION ALL

SELECT 
    'walmart_ck' AS table_name,
    MAX(id) AS max_id,
    AUTO_INCREMENT AS next_id
FROM walmart_ck, INFORMATION_SCHEMA.TABLES 
WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'walmart_ck';

-- 验证外键关系
SELECT '验证外键关系...' AS status;

-- 检查用户-商户关系
SELECT 
    COUNT(*) AS total_users,
    COUNT(merchant_id) AS users_with_merchant
FROM users;

-- 检查用户-部门关系
SELECT 
    COUNT(*) AS total_users,
    COUNT(department_id) AS users_with_department
FROM users;

-- 检查卡记录-商户关系
SELECT 
    COUNT(*) AS total_card_records,
    COUNT(merchant_id) AS records_with_merchant,
    COUNT(department_id) AS records_with_department,
    COUNT(walmart_ck_id) AS records_with_ck
FROM card_records;

-- ========================================
-- 阶段6: 性能优化建议
-- ========================================

SELECT '升级完成，建议执行以下优化操作...' AS status;

-- 重建统计信息
ANALYZE TABLE users, merchants, departments, walmart_ck, roles, permissions, menus;
ANALYZE TABLE user_roles, user_permissions, role_permissions, role_menus, menu_permissions;
ANALYZE TABLE card_records, binding_logs;

-- 记录升级完成时间
SELECT CONCAT('升级完成时间: ', NOW()) AS upgrade_complete;

-- 显示升级摘要
SELECT 
    '主键类型升级完成' AS summary,
    'INT -> BIGINT' AS change_type,
    '所有表的主键和外键已升级为BIGINT类型' AS description,
    '容量从21亿提升至922万亿' AS capacity_improvement;

-- ========================================
-- 升级验证查询
-- ========================================

-- 查看表结构变更确认
SELECT 
    TABLE_NAME,
    COLUMN_NAME,
    DATA_TYPE,
    IS_NULLABLE,
    COLUMN_KEY,
    EXTRA
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() 
    AND COLUMN_NAME = 'id' 
    AND DATA_TYPE = 'bigint'
ORDER BY TABLE_NAME;

-- 查看外键字段类型确认（仅在执行升级时显示）
IF @should_upgrade_keys = 1 THEN
    SELECT
        TABLE_NAME,
        COLUMN_NAME,
        DATA_TYPE,
        IS_NULLABLE,
        COLUMN_KEY
    FROM INFORMATION_SCHEMA.COLUMNS
    WHERE TABLE_SCHEMA = DATABASE()
        AND (COLUMN_NAME LIKE '%_id' OR COLUMN_NAME = 'id')
        AND DATA_TYPE IN ('bigint', 'int')
    ORDER BY TABLE_NAME, COLUMN_NAME;
ELSE
    SELECT '外键字段类型确认跳过 - 已执行过升级' as status;
END IF;

-- ========================================
-- 升级完成处理
-- ========================================

-- 记录升级完成
UPDATE `migration_logs`
SET `status` = 'completed',
    `message` = '主键类型升级完成（INT → BIGINT）',
    `completed_at` = NOW(3),
    `data_summary` = JSON_OBJECT(
        'upgrade_executed', @should_upgrade_keys,
        'bigint_primary_keys_count', (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_SCHEMA = DATABASE() AND COLUMN_NAME = 'id' AND DATA_TYPE = 'bigint'),
        'remaining_int_primary_keys', (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_SCHEMA = DATABASE() AND COLUMN_NAME = 'id' AND DATA_TYPE = 'int'),
        'completion_timestamp', NOW(3)
    )
WHERE `migration_name` = 'primary_key_upgrade_v2.1.0';

-- 显示升级结果摘要
SELECT
    '=== 主键升级脚本执行完成 ===' as summary,
    CASE
        WHEN @should_upgrade_keys = 1 THEN '✓ 主键升级已执行'
        ELSE '⚠ 主键升级已跳过（之前已执行）'
    END as execution_status,
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_SCHEMA = DATABASE() AND COLUMN_NAME = 'id' AND DATA_TYPE = 'bigint') as bigint_primary_keys,
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_SCHEMA = DATABASE() AND COLUMN_NAME = 'id' AND DATA_TYPE = 'int') as remaining_int_keys,
    NOW(3) as completion_time;

-- 升级完成提示
SELECT
    '主键升级完成' as upgrade_status,
    '所有表的主键已从INT升级为BIGINT类型' as description,
    '这将支持更大的数据量，避免主键溢出问题' as benefit;
