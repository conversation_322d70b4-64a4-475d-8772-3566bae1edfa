[client]
# 客户端字符集配置
default-character-set = utf8mb4

[mysql]
# MySQL客户端字符集配置
default-character-set = utf8mb4

[mysqld]
# 服务器字符集配置（核心配置）
character-set-server = utf8mb4
collation-server = utf8mb4_unicode_ci

# 初始化连接字符集（关键配置）
init_connect = 'SET NAMES utf8mb4 COLLATE utf8mb4_unicode_ci'

# 跳过客户端字符集握手，强制使用服务器字符集
skip-character-set-client-handshake = true

# 确保所有连接都使用UTF8MB4
character-set-client-handshake = FALSE

# 时区设置
default-time-zone = '+08:00'

# ========================================
# 高并发性能优化配置 (支持100+并发)
# ========================================

# 连接配置
max_connections = 500                    # 支持500个并发连接
max_connect_errors = 100000             # 增加连接错误限制
max_user_connections = 450              # 单用户最大连接数
back_log = 600                          # 连接队列大小
connect_timeout = 10                    # 连接超时时间
wait_timeout = 600                      # 非交互连接超时(10分钟)
interactive_timeout = 600               # 交互连接超时(10分钟)

# InnoDB 缓冲池优化 (根据服务器内存调整)
innodb_buffer_pool_size = 1G            # 建议设置为可用内存的70-80%
innodb_buffer_pool_instances = 4        # 多实例提高并发性能
innodb_buffer_pool_chunk_size = 256M    # 缓冲池块大小

# InnoDB 日志优化
innodb_log_file_size = 256M             # 增大日志文件提高写性能
innodb_log_buffer_size = 64M            # 日志缓冲区大小
innodb_log_files_in_group = 2           # 日志文件组数量
innodb_flush_log_at_trx_commit = 2      # 每秒刷新日志(性能优先)
innodb_flush_method = O_DIRECT          # 直接IO，避免双重缓冲

# InnoDB 并发优化
innodb_thread_concurrency = 0           # 0表示不限制线程数
innodb_read_io_threads = 8              # 读IO线程数
innodb_write_io_threads = 8             # 写IO线程数
innodb_io_capacity = 2000               # IO容量
innodb_io_capacity_max = 4000           # 最大IO容量
innodb_purge_threads = 4                # 清理线程数
innodb_page_cleaners = 4                # 页面清理线程数

# InnoDB 锁优化
innodb_lock_wait_timeout = 50           # 锁等待超时时间
innodb_deadlock_detect = ON             # 死锁检测
innodb_print_all_deadlocks = ON         # 记录所有死锁

# 表缓存优化
table_open_cache = 4000                 # 表缓存数量
table_definition_cache = 2000           # 表定义缓存
open_files_limit = 65535                # 打开文件限制

# 临时表优化
tmp_table_size = 256M                   # 内存临时表大小
max_heap_table_size = 256M              # 堆表最大大小
sort_buffer_size = 4M                   # 排序缓冲区
read_buffer_size = 2M                   # 读缓冲区
read_rnd_buffer_size = 4M               # 随机读缓冲区
join_buffer_size = 4M                   # 连接缓冲区

# 网络优化
max_allowed_packet = 128M               # 最大数据包大小
net_buffer_length = 32K                 # 网络缓冲区长度
net_read_timeout = 30                   # 网络读超时
net_write_timeout = 30                  # 网络写超时

# 线程优化
thread_cache_size = 100                 # 线程缓存大小
thread_stack = 512K                     # 线程栈大小

# 日志配置
general_log = 0                         # 关闭通用日志提高性能
slow_query_log = 1                      # 开启慢查询日志
slow_query_log_file = /var/log/mysql/slow.log
long_query_time = 1                     # 慢查询阈值降低到1秒
log_queries_not_using_indexes = 1       # 记录未使用索引的查询
min_examined_row_limit = 1000           # 最小检查行数限制

# SQL模式配置
sql_mode = STRICT_TRANS_TABLES,NO_ZERO_DATE,NO_ZERO_IN_DATE,ERROR_FOR_DIVISION_BY_ZERO

# 二进制日志配置
log-bin = mysql-bin
binlog_format = ROW
binlog_cache_size = 2M                  # 二进制日志缓存
max_binlog_cache_size = 128M            # 最大二进制日志缓存
max_binlog_size = 512M                  # 单个二进制日志文件大小
expire_logs_days = 7
sync_binlog = 1                         # 二进制日志同步频率

# 性能监控配置
performance_schema = ON                 # 开启性能监控
performance_schema_max_table_instances = 12500
performance_schema_max_table_handles = 4000

[mysqldump]
# mysqldump工具字符集配置
default-character-set = utf8mb4
quick
quote-names
max_allowed_packet = 64M
