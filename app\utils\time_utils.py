"""
统一的时间处理工具模块

解决系统中时间记录不一致的问题，提供统一的时间处理接口
"""

import time
from datetime import datetime, timedelta
from zoneinfo import ZoneInfo
from typing import Optional, Union

# 使用上海时区作为系统标准时区
TARGET_TZ = ZoneInfo("Asia/Shanghai")


def get_current_time() -> datetime:
    """
    获取当前时间（带时区信息）
    
    Returns:
        datetime: 当前上海时区时间
    """
    return datetime.now(TARGET_TZ)


def get_current_timestamp() -> float:
    """
    获取当前Unix时间戳
    
    Returns:
        float: 当前时间戳（秒）
    """
    return time.time()


def datetime_to_isoformat(dt: Optional[datetime]) -> Optional[str]:
    """
    将datetime对象转换为ISO格式字符串
    
    Args:
        dt: datetime对象
        
    Returns:
        str: ISO格式时间字符串，如果输入为None则返回None
    """
    if dt is None:
        return None
    
    # 如果没有时区信息，假设为上海时区
    if dt.tzinfo is None:
        dt = dt.replace(tzinfo=TARGET_TZ)
    
    return dt.isoformat()


def calculate_duration_seconds(start_timestamp: float, end_timestamp: Optional[float] = None) -> float:
    """
    计算时间间隔（秒）
    
    Args:
        start_timestamp: 开始时间戳
        end_timestamp: 结束时间戳，如果为None则使用当前时间
        
    Returns:
        float: 时间间隔（秒）
    """
    if end_timestamp is None:
        end_timestamp = get_current_timestamp()
    
    return end_timestamp - start_timestamp


def calculate_duration_ms(start_timestamp: float, end_timestamp: Optional[float] = None) -> float:
    """
    计算时间间隔（毫秒）
    
    Args:
        start_timestamp: 开始时间戳
        end_timestamp: 结束时间戳，如果为None则使用当前时间
        
    Returns:
        float: 时间间隔（毫秒）
    """
    return calculate_duration_seconds(start_timestamp, end_timestamp) * 1000


def format_duration(duration_seconds: float) -> str:
    """
    格式化时间间隔为可读字符串
    
    Args:
        duration_seconds: 时间间隔（秒）
        
    Returns:
        str: 格式化的时间间隔字符串
    """
    if duration_seconds < 1:
        return f"{duration_seconds * 1000:.2f}ms"
    elif duration_seconds < 60:
        return f"{duration_seconds:.2f}s"
    elif duration_seconds < 3600:
        minutes = int(duration_seconds // 60)
        seconds = duration_seconds % 60
        return f"{minutes}m{seconds:.2f}s"
    else:
        hours = int(duration_seconds // 3600)
        minutes = int((duration_seconds % 3600) // 60)
        seconds = duration_seconds % 60
        return f"{hours}h{minutes}m{seconds:.2f}s"


def parse_datetime_string(date_str: Union[str, datetime, None]) -> Optional[datetime]:
    """
    解析日期时间字符串为datetime对象
    
    Args:
        date_str: 日期时间字符串、datetime对象或None
        
    Returns:
        datetime: 解析后的datetime对象，如果解析失败则返回None
    """
    if date_str is None:
        return None
    
    if isinstance(date_str, datetime):
        # 如果没有时区信息，添加上海时区
        if date_str.tzinfo is None:
            return date_str.replace(tzinfo=TARGET_TZ)
        return date_str
    
    if not isinstance(date_str, str):
        return None
    
    # 尝试多种格式解析
    formats = [
        "%Y-%m-%d %H:%M:%S",
        "%Y-%m-%dT%H:%M:%S",
        "%Y-%m-%dT%H:%M:%S.%f",
        "%Y-%m-%dT%H:%M:%S%z",
        "%Y-%m-%dT%H:%M:%S.%f%z",
        "%Y-%m-%d",
    ]
    
    for fmt in formats:
        try:
            dt = datetime.strptime(date_str, fmt)
            # 如果没有时区信息，添加上海时区
            if dt.tzinfo is None:
                dt = dt.replace(tzinfo=TARGET_TZ)
            return dt
        except ValueError:
            continue
    
    return None


def ensure_timezone(dt: datetime) -> datetime:
    """
    确保datetime对象有时区信息

    Args:
        dt: datetime对象

    Returns:
        datetime: 带时区信息的datetime对象
    """
    if dt.tzinfo is None:
        return dt.replace(tzinfo=TARGET_TZ)
    return dt


def safe_datetime_compare(dt1: datetime, dt2: datetime) -> bool:
    """
    安全地比较两个datetime对象，自动处理时区问题

    Args:
        dt1: 第一个datetime对象
        dt2: 第二个datetime对象

    Returns:
        bool: dt1 > dt2 的比较结果
    """
    # 确保两个datetime都有时区信息
    dt1_tz = ensure_timezone(dt1)
    dt2_tz = ensure_timezone(dt2)

    return dt1_tz > dt2_tz


def is_expired(created_time: datetime, expire_duration: timedelta) -> bool:
    """
    检查是否已过期

    Args:
        created_time: 创建时间
        expire_duration: 过期时长

    Returns:
        bool: 是否已过期
    """
    if not created_time:
        return True

    # 确保时区一致性
    created_time_tz = ensure_timezone(created_time)
    expire_time = created_time_tz + expire_duration
    current_time = get_current_time()

    return current_time > expire_time


def get_process_time_details(start_timestamp: float, end_timestamp: Optional[float] = None) -> dict:
    """
    获取详细的处理时间信息
    
    Args:
        start_timestamp: 开始时间戳
        end_timestamp: 结束时间戳，如果为None则使用当前时间
        
    Returns:
        dict: 包含各种时间格式的详细信息
    """
    if end_timestamp is None:
        end_timestamp = get_current_timestamp()
    
    duration_seconds = calculate_duration_seconds(start_timestamp, end_timestamp)
    duration_ms = duration_seconds * 1000
    
    start_time = datetime.fromtimestamp(start_timestamp, tz=TARGET_TZ)
    end_time = datetime.fromtimestamp(end_timestamp, tz=TARGET_TZ)
    
    return {
        "start_timestamp": start_timestamp,
        "end_timestamp": end_timestamp,
        "start_time": start_time,
        "end_time": end_time,
        "start_time_iso": start_time.isoformat(),
        "end_time_iso": end_time.isoformat(),
        "duration_seconds": duration_seconds,
        "duration_ms": duration_ms,
        "duration_formatted": format_duration(duration_seconds),
    }


class TimeTracker:
    """
    时间跟踪器，用于跟踪操作的开始和结束时间
    """
    
    def __init__(self, operation_name: str = "operation"):
        self.operation_name = operation_name
        self.start_timestamp: Optional[float] = None
        self.end_timestamp: Optional[float] = None
    
    def start(self) -> float:
        """
        开始计时
        
        Returns:
            float: 开始时间戳
        """
        self.start_timestamp = get_current_timestamp()
        return self.start_timestamp
    
    def end(self) -> float:
        """
        结束计时
        
        Returns:
            float: 结束时间戳
        """
        self.end_timestamp = get_current_timestamp()
        return self.end_timestamp
    
    def get_duration_seconds(self) -> Optional[float]:
        """
        获取持续时间（秒）
        
        Returns:
            float: 持续时间，如果未开始或结束则返回None
        """
        if self.start_timestamp is None:
            return None
        
        end_time = self.end_timestamp or get_current_timestamp()
        return calculate_duration_seconds(self.start_timestamp, end_time)
    
    def get_duration_ms(self) -> Optional[float]:
        """
        获取持续时间（毫秒）
        
        Returns:
            float: 持续时间，如果未开始或结束则返回None
        """
        duration = self.get_duration_seconds()
        return duration * 1000 if duration is not None else None
    
    def get_details(self) -> dict:
        """
        获取详细的时间信息
        
        Returns:
            dict: 详细的时间信息
        """
        if self.start_timestamp is None:
            return {"operation_name": self.operation_name, "status": "not_started"}
        
        end_time = self.end_timestamp or get_current_timestamp()
        details = get_process_time_details(self.start_timestamp, end_time)
        details["operation_name"] = self.operation_name
        details["status"] = "completed" if self.end_timestamp else "running"
        
        return details
    
    def __enter__(self):
        """上下文管理器入口"""
        self.start()
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """上下文管理器出口"""
        self.end()
