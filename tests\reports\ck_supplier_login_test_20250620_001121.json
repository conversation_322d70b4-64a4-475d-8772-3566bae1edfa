[{"test_name": "test2用户登录", "success": true, "message": "登录成功", "details": "获得访问令牌: eyJhbGciOiJIUzI1NiIs...", "timestamp": "2025-06-20T00:11:21.826383"}, {"test_name": "用户信息获取", "success": false, "message": "用户信息缺少字段: ['id', 'username', 'roles', 'permissions', 'menus']", "details": {"code": 0, "data": {"id": 10, "username": "test2", "email": null, "full_name": "test2", "merchant_id": 1, "merchant_name": "测试商户A", "department_id": 16, "department_name": "商务部子部门1", "is_active": true, "is_superuser": false, "phone": "", "remark": "test2", "created_at": "2025-06-19T23:33:11.916000", "updated_at": "2025-06-20T00:11:21.805000", "last_login_ip": "127.0.0.1", "last_login_time": "2025-06-20 00:11:21", "roles": [{"id": 3, "name": "商户CK供应商", "code": "ck_supplier"}], "permissions": ["api:walmart-ck:delete", "api:walmart-ck:read", "api:walmart-ck:create", "api:/api/v1/walmart-ck", "data:user:own", "api:walmart-ck:update", "data:department:own"], "menus": ["notification", "walmart:user", "walmart"]}, "message": "操作成功"}, "timestamp": "2025-06-20T00:11:21.865434"}, {"test_name": "CK管理API访问", "success": true, "message": "可以正常访问CK管理API", "details": "响应状态: 200", "timestamp": "2025-06-20T00:11:21.908536"}, {"test_name": "通知中心API访问", "success": true, "message": "可以正常访问通知中心API", "details": "响应状态: 200", "timestamp": "2025-06-20T00:11:21.934058"}, {"test_name": "权限隔离验证-商户管理API", "success": false, "message": "权限隔离失败，不应该能访问商户管理API", "details": "状态码: 401", "timestamp": "2025-06-20T00:11:21.947062"}, {"test_name": "权限隔离验证-用户管理API", "success": false, "message": "权限隔离失败，不应该能访问用户管理API", "details": "状态码: 401", "timestamp": "2025-06-20T00:11:21.959581"}, {"test_name": "权限隔离验证-角色管理API", "success": false, "message": "权限隔离失败，不应该能访问角色管理API", "details": "状态码: 401", "timestamp": "2025-06-20T00:11:21.971534"}, {"test_name": "权限隔离验证-沃尔玛服务器配置API", "success": false, "message": "权限隔离失败，不应该能访问沃尔玛服务器配置API", "details": "状态码: 401", "timestamp": "2025-06-20T00:11:21.983565"}]