from sqlalchemy import Column, String, Integer, BigI<PERSON>ger, <PERSON><PERSON><PERSON>, Text, ForeignKey
from sqlalchemy.orm import relationship

from app.models.base import BaseModel, TimestampMixin
from app.models.associations import user_roles, role_permissions, role_menus


class Role(BaseModel, TimestampMixin):
    """角色模型"""

    __tablename__ = "roles"

    id = Column(BigInteger, primary_key=True, index=True, autoincrement=True)

    # 基本信息
    name = Column(String(50), nullable=False, index=True, comment="角色名称")
    code = Column(
        String(50), nullable=False, unique=True, index=True, comment="角色代码"
    )
    description = Column(Text, nullable=True, comment="角色描述")

    # 角色状态
    is_enabled = Column(Boolean, default=True, nullable=False, comment="是否启用")
    is_system = Column(
        Boolean, default=False, nullable=False, comment="是否系统内置角色"
    )

    # 排序
    sort_order = Column(Integer, default=0, nullable=False, comment="排序号")

    # 移除硬编码的数据范围字段，改用动态权限管理

    # 关联关系 - 修复lazy加载策略以支持selectinload预加载
    permissions = relationship(
        "Permission",
        secondary=role_permissions,
        back_populates="roles",
        lazy="select"  # 改为select以支持selectinload预加载
    )
    menus = relationship(
        "Menu",
        secondary=role_menus,
        back_populates="roles",
        lazy="select"
    )
    users = relationship("User", secondary=user_roles, back_populates="roles", lazy="select")

    # 创建者
    created_by = Column(
        BigInteger, ForeignKey("users.id"), nullable=True, comment="创建者ID"
    )
    creator = relationship("User", foreign_keys=[created_by])

    # 备注
    remark = Column(Text, nullable=True, comment="备注")

    def to_dict(self, include_permissions=False, include_menus=False):
        """转换为字典

        Args:
            include_permissions: 是否包含权限
            include_menus: 是否包含菜单

        Returns:
            dict: 角色字典
        """
        data = super().to_dict()

        # 添加权限
        if include_permissions and self.permissions:
            data["permissions"] = [p.to_dict() for p in self.permissions]

        # 添加菜单
        if include_menus and self.menus:
            data["menus"] = [m.to_dict(include_children=False) for m in self.menus]

        return data

    def has_permission(self, permission_code):
        """检查角色是否有特定权限"""
        return any(perm.code == permission_code for perm in self.permissions)

    def has_menu(self, menu_code):
        """检查角色是否有特定菜单"""
        return any(menu.code == menu_code for menu in self.menus)

    def add_permission(self, permission):
        """为角色添加权限"""
        if permission not in self.permissions:
            self.permissions.append(permission)

    def remove_permission(self, permission):
        """从角色移除权限"""
        if permission in self.permissions:
            self.permissions.remove(permission)

    def add_menu(self, menu):
        """为角色添加菜单"""
        if menu not in self.menus:
            self.menus.append(menu)

    def remove_menu(self, menu):
        """从角色移除菜单"""
        if menu in self.menus:
            self.menus.remove(menu)
