import { http } from '@/api/request'
import { API_URLS, replaceUrlParams } from './config'

const { PERMISSION } = API_URLS

export const permissionApi = {
    /**
     * 获取权限列表（支持搜索和筛选）
     */
    getList(params) {
        return http.get(PERMISSION.LIST, { params }).then(res => {
            if (res && res.data) {
                return res.data;
            } else {
                throw new Error('获取权限列表响应格式错误');
            }
        });
    },

    /**
     * 获取权限详情
     */
    getDetail(id) {
        const url = replaceUrlParams(PERMISSION.DETAIL, { id: id })
        return http.get(url).then(res => {
            if (res && res.data) {
                return res.data;
            } else {
                throw new Error('获取权限详情响应格式错误');
            }
        });
    },

    /**
     * 创建权限
     */
    create(data) {
        return http.post(PERMISSION.CREATE, data).then(res => {
            if (res && res.data) {
                return res.data;
            } else {
                throw new Error('创建权限响应格式错误');
            }
        });
    },

    /**
     * 更新权限
     */
    update(id, data) {
        const url = replaceUrlParams(PERMISSION.UPDATE, { id: id })
        return http.put(url, data).then(res => {
            if (res && res.data) {
                return res.data;
            } else {
                throw new Error('更新权限响应格式错误');
            }
        });
    },

    /**
     * 删除权限
     */
    delete(id) {
        const url = replaceUrlParams(PERMISSION.DELETE, { id: id })
        return http.delete(url).then(res => {
            return res.data || { message: '删除成功' };
        });
    }
}