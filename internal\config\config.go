package config

import (
	"fmt"
	"net/url"
	"strings"
	"time"

	"github.com/spf13/viper"
)

// Config 应用配置结构
type Config struct {
	Server      ServerConfig      `mapstructure:"server"`
	Concurrency ConcurrencyConfig `mapstructure:"concurrency"`
	Database    DatabaseConfig    `mapstructure:"database"`
	Redis       RedisConfig       `mapstructure:"redis"`
	RabbitMQ    RabbitMQConfig    `mapstructure:"rabbitmq"`
	Logging     LoggingConfig     `mapstructure:"logging"`
	API         APIConfig         `mapstructure:"api"`
	Performance PerformanceConfig `mapstructure:"performance"`
	Monitoring  MonitoringConfig  `mapstructure:"monitoring"`
	Security    SecurityConfig    `mapstructure:"security"`
	Business    BusinessConfig    `mapstructure:"business"`
}

// ServerConfig 服务器配置
type ServerConfig struct {
	Port           int           `mapstructure:"port"`
	Mode           string        `mapstructure:"mode"`
	Host           string        `mapstructure:"host"`
	ReadTimeout    time.Duration `mapstructure:"read_timeout"`
	WriteTimeout   time.Duration `mapstructure:"write_timeout"`
	IdleTimeout    time.Duration `mapstructure:"idle_timeout"`
	MaxHeaderBytes int           `mapstructure:"max_header_bytes"`
}

// ConcurrencyConfig 并发配置
type ConcurrencyConfig struct {
	MaxGoroutines            int           `mapstructure:"max_goroutines"`
	WorkerPoolSize           int           `mapstructure:"worker_pool_size"`
	QueueBufferSize          int           `mapstructure:"queue_buffer_size"`
	BatchSize                int           `mapstructure:"batch_size"`
	BatchTimeout             time.Duration `mapstructure:"batch_timeout"`
	GracefulShutdownTimeout  time.Duration `mapstructure:"graceful_shutdown_timeout"`
}

// DatabaseConfig 数据库配置
type DatabaseConfig struct {
	Type            string        `mapstructure:"type"`
	Host            string        `mapstructure:"host"`
	Port            int           `mapstructure:"port"`
	User            string        `mapstructure:"user"`
	Password        string        `mapstructure:"password"`
	DBName          string        `mapstructure:"db_name"`
	Charset         string        `mapstructure:"charset"`
	MaxOpenConns    int           `mapstructure:"max_open_conns"`
	MaxIdleConns    int           `mapstructure:"max_idle_conns"`
	ConnMaxLifetime time.Duration `mapstructure:"conn_max_lifetime"`
	ConnMaxIdleTime time.Duration `mapstructure:"conn_max_idle_time"`
	QueryTimeout    time.Duration `mapstructure:"query_timeout"`
	ExecTimeout     time.Duration `mapstructure:"exec_timeout"`
}

// RedisConfig Redis配置
type RedisConfig struct {
	Host         string        `mapstructure:"host"`
	Port         int           `mapstructure:"port"`
	Password     string        `mapstructure:"password"`
	DB           int           `mapstructure:"db"`
	PoolSize     int           `mapstructure:"pool_size"`
	MinIdleConns int           `mapstructure:"min_idle_conns"`
	MaxRetries   int           `mapstructure:"max_retries"`
	DialTimeout  time.Duration `mapstructure:"dial_timeout"`
	ReadTimeout  time.Duration `mapstructure:"read_timeout"`
	WriteTimeout time.Duration `mapstructure:"write_timeout"`
	PoolTimeout  time.Duration `mapstructure:"pool_timeout"`
	PipelineSize int           `mapstructure:"pipeline_size"`
}

// RabbitMQConfig RabbitMQ配置
type RabbitMQConfig struct {
	// 连接配置（支持两种方式）
	URL      string `mapstructure:"url"`      // URL方式（可选）
	Host     string `mapstructure:"host"`     // 主机地址
	Port     int    `mapstructure:"port"`     // 端口
	User     string `mapstructure:"user"`     // 用户名
	Password string `mapstructure:"password"` // 密码
	VHost    string `mapstructure:"vhost"`    // 虚拟主机

	// 连接池配置
	MaxConnections       int               `mapstructure:"max_connections"`
	MaxChannelsPerConn   int               `mapstructure:"max_channels_per_conn"`
	BatchPublishSize     int               `mapstructure:"batch_publish_size"`
	BatchTimeout         time.Duration     `mapstructure:"batch_timeout"`
	ConfirmMode          bool              `mapstructure:"confirm_mode"`
	ConnectionPoolSize   int               `mapstructure:"connection_pool_size"`
	ChannelPoolSize      int               `mapstructure:"channel_pool_size"`
	Queues               map[string]string `mapstructure:"queues"`
}

// GetRabbitMQURL 获取RabbitMQ连接URL
func (r *RabbitMQConfig) GetRabbitMQURL() string {
	// 如果配置了URL，直接使用
	if r.URL != "" {
		return r.URL
	}

	// 否则根据分离的字段构建URL
	if r.Host == "" {
		r.Host = "localhost"
	}
	if r.Port == 0 {
		r.Port = 5672
	}
	if r.User == "" {
		r.User = "guest"
	}
	if r.Password == "" {
		r.Password = "guest"
	}
	// 处理虚拟主机名 - 与Python系统保持一致
	vhost := r.VHost
	if vhost == "" {
		vhost = "/"
	} else {
		// 确保虚拟主机名前面有斜杠（如果不是根路径的话）
		if vhost != "/" && !strings.HasPrefix(vhost, "/") {
			vhost = "/" + vhost
		}
	}

	// URL编码虚拟主机名 - 关键修复！与Python系统一致
	// Python: urllib.parse.quote(self.RABBITMQ_VHOST, safe="")
	encodedVhost := url.QueryEscape(vhost)

	return fmt.Sprintf("amqp://%s:%s@%s:%d/%s", r.User, r.Password, r.Host, r.Port, encodedVhost)
}

// LoggingConfig 日志配置
type LoggingConfig struct {
	Level  string            `mapstructure:"level"`
	Format string            `mapstructure:"format"`
	Output string            `mapstructure:"output"`
	Fields map[string]string `mapstructure:"fields"`
}

// APIConfig API配置
type APIConfig struct {
	ProjectName string        `mapstructure:"project_name"`
	Version     string        `mapstructure:"version"`
	Prefix      string        `mapstructure:"prefix"`
	JWT         JWTConfig     `mapstructure:"jwt"`
	RateLimit   RateLimitConfig `mapstructure:"rate_limit"`
}

// JWTConfig JWT配置
type JWTConfig struct {
	SecretKey    string `mapstructure:"secret_key"`
	ExpireHours  int    `mapstructure:"expire_hours"`
	Algorithm    string `mapstructure:"algorithm"`
}

// RateLimitConfig 限流配置
type RateLimitConfig struct {
	Enabled           bool `mapstructure:"enabled"`
	RequestsPerMinute int  `mapstructure:"requests_per_minute"`
	BurstSize         int  `mapstructure:"burst_size"`
	UseRedis          bool `mapstructure:"use_redis"`
}

// PerformanceConfig 性能配置
type PerformanceConfig struct {
	HTTPClient HTTPClientConfig `mapstructure:"http_client"`
	Memory     MemoryConfig     `mapstructure:"memory"`
	Cache      CacheConfig      `mapstructure:"cache"`
}

// HTTPClientConfig HTTP客户端配置
type HTTPClientConfig struct {
	Timeout              time.Duration `mapstructure:"timeout"`
	DialTimeout          time.Duration `mapstructure:"dial_timeout"`
	KeepAlive            time.Duration `mapstructure:"keep_alive"`
	MaxIdleConns         int           `mapstructure:"max_idle_conns"`
	MaxIdleConnsPerHost  int           `mapstructure:"max_idle_conns_per_host"`
	MaxConnsPerHost      int           `mapstructure:"max_conns_per_host"`
}

// MemoryConfig 内存配置
type MemoryConfig struct {
	GCPercent   int    `mapstructure:"gc_percent"`
	MaxHeapSize string `mapstructure:"max_heap_size"`
}

// CacheConfig 缓存配置
type CacheConfig struct {
	DefaultTTL  time.Duration `mapstructure:"default_ttl"`
	UserTTL     time.Duration `mapstructure:"user_ttl"`
	MerchantTTL time.Duration `mapstructure:"merchant_ttl"`
	MaxSize     int           `mapstructure:"max_size"`
}

// MonitoringConfig 监控配置
type MonitoringConfig struct {
	Metrics MetricsConfig `mapstructure:"metrics"`
	Health  HealthConfig  `mapstructure:"health"`
}

// MetricsConfig 指标配置
type MetricsConfig struct {
	Enabled bool   `mapstructure:"enabled"`
	Port    int    `mapstructure:"port"`
	Path    string `mapstructure:"path"`
}

// HealthConfig 健康检查配置
type HealthConfig struct {
	Enabled  bool          `mapstructure:"enabled"`
	Port     int           `mapstructure:"port"`
	Path     string        `mapstructure:"path"`
	Interval time.Duration `mapstructure:"interval"`
}

// SecurityConfig 安全配置
type SecurityConfig struct {
	CORS CORSConfig `mapstructure:"cors"`
}

// CORSConfig CORS配置
type CORSConfig struct {
	AllowedOrigins   []string `mapstructure:"allowed_origins"`
	AllowedMethods   []string `mapstructure:"allowed_methods"`
	AllowedHeaders   []string `mapstructure:"allowed_headers"`
	AllowCredentials bool     `mapstructure:"allow_credentials"`
}

// BusinessConfig 业务配置
type BusinessConfig struct {
	Binding  BindingConfig  `mapstructure:"binding"`
	Callback CallbackConfig `mapstructure:"callback"`
}

// BindingConfig 绑卡配置
type BindingConfig struct {
	MaxConcurrency int           `mapstructure:"max_concurrency"`
	Timeout        time.Duration `mapstructure:"timeout"`
	RetryAttempts  int           `mapstructure:"retry_attempts"`
	RetryDelay     time.Duration `mapstructure:"retry_delay"`
}

// CallbackConfig 回调配置
type CallbackConfig struct {
	Timeout       time.Duration `mapstructure:"timeout"`
	RetryAttempts int           `mapstructure:"retry_attempts"`
	RetryDelay    time.Duration `mapstructure:"retry_delay"`
	MaxRetryDelay time.Duration `mapstructure:"max_retry_delay"`
}

// LoadConfig 加载配置文件
func LoadConfig(configPath string) (*Config, error) {
	viper.SetConfigFile(configPath)
	viper.AutomaticEnv()
	viper.SetEnvKeyReplacer(strings.NewReplacer(".", "_"))

	if err := viper.ReadInConfig(); err != nil {
		return nil, fmt.Errorf("读取配置文件失败: %w", err)
	}

	var config Config
	if err := viper.Unmarshal(&config); err != nil {
		return nil, fmt.Errorf("解析配置文件失败: %w", err)
	}

	// 验证配置
	if err := validateConfig(&config); err != nil {
		return nil, fmt.Errorf("配置验证失败: %w", err)
	}

	return &config, nil
}

// GetDSN 获取数据库连接字符串
func (c *DatabaseConfig) GetDSN() string {
	return fmt.Sprintf("%s:%s@tcp(%s:%d)/%s?charset=%s&parseTime=True&loc=Local",
		c.User, c.Password, c.Host, c.Port, c.DBName, c.Charset)
}

// GetRedisAddr 获取Redis地址
func (c *RedisConfig) GetRedisAddr() string {
	return fmt.Sprintf("%s:%d", c.Host, c.Port)
}

// GetServerAddr 获取服务器地址
func (c *ServerConfig) GetServerAddr() string {
	return fmt.Sprintf("%s:%d", c.Host, c.Port)
}

// validateConfig 验证配置参数
func validateConfig(config *Config) error {
	// 验证服务器配置
	if config.Server.Port <= 0 || config.Server.Port > 65535 {
		return fmt.Errorf("无效的服务器端口: %d", config.Server.Port)
	}

	// 验证数据库配置
	if config.Database.Host == "" {
		return fmt.Errorf("数据库主机不能为空")
	}
	if config.Database.User == "" {
		return fmt.Errorf("数据库用户不能为空")
	}
	if config.Database.DBName == "" {
		return fmt.Errorf("数据库名不能为空")
	}

	// 验证Redis配置
	if config.Redis.Host == "" {
		return fmt.Errorf("Redis主机不能为空")
	}

	// 验证RabbitMQ配置
	if config.RabbitMQ.URL == "" && config.RabbitMQ.Host == "" {
		return fmt.Errorf("RabbitMQ URL或Host必须配置其中一个")
	}

	// 验证并发配置
	if config.Concurrency.BatchSize <= 0 {
		return fmt.Errorf("批量大小必须大于0")
	}
	if config.Concurrency.WorkerPoolSize <= 0 {
		return fmt.Errorf("Worker池大小必须大于0")
	}

	return nil
}
