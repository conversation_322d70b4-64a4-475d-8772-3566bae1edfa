"""
双因子认证服务模块
"""

import json
import secrets
import pyotp
import qrcode
import io
import base64
from typing import List, Optional, Dict, Any, Tu<PERSON>
from datetime import datetime, timedelta
from sqlalchemy.orm import Session
from cryptography.fernet import Fernet

from app.models.user import User
from app.models.totp import TOTPLog, TOTPPolicy
from app.schemas.totp import (
    TOTPSetupResponse, TOTPVerifyResponse, TOTPStatusResponse
)
from app.core.config import settings
import logging

logger = logging.getLogger(__name__)


class TOTPService:
    """双因子认证服务"""

    def __init__(self, db: Session):
        """
        初始化双因子认证服务

        Args:
            db: 数据库会话
        """
        self.db = db
        self.logger = logger
        # 初始化加密器
        self.cipher = Fernet(settings.TOTP_ENCRYPTION_KEY.encode())

    def _encrypt_data(self, data: str) -> str:
        """加密数据"""
        return self.cipher.encrypt(data.encode()).decode()

    def _decrypt_data(self, encrypted_data: str) -> str:
        """解密数据"""
        return self.cipher.decrypt(encrypted_data.encode()).decode()

    def _generate_backup_codes(self, count: int = 10) -> List[str]:
        """生成备用恢复码"""
        codes = []
        for _ in range(count):
            # 生成8位随机字符串
            code = ''.join(secrets.choice('0123456789ABCDEF') for _ in range(8))
            codes.append(code)
        return codes

    def _log_totp_action(self, user_id: int, action: str, success: bool, 
                        ip_address: str = None, error_message: str = None):
        """记录TOTP操作日志"""
        try:
            log = TOTPLog(
                user_id=user_id,
                action=action,
                success=success,
                ip_address=ip_address,
                error_message=error_message
            )
            self.db.add(log)
            self.db.commit()
        except Exception as e:
            logger.error(f"记录TOTP日志失败: {e}")
            self.db.rollback()

    def setup_totp(self, user: User, ip_address: str = None) -> TOTPSetupResponse:
        """
        为用户设置TOTP
        
        Args:
            user: 用户对象
            ip_address: 客户端IP地址
            
        Returns:
            TOTPSetupResponse: 设置响应
        """
        try:
            # 检查是否已经设置
            if user.totp_enabled:
                raise ValueError("用户已启用双因子认证")

            # 生成密钥
            secret = pyotp.random_base32()
            
            # 获取策略配置
            policy = self._get_user_totp_policy(user)
            backup_codes_count = policy.backup_codes_count if policy else 10
            
            # 生成备用码
            backup_codes = self._generate_backup_codes(backup_codes_count)
            
            # 加密存储密钥和备用码
            encrypted_secret = self._encrypt_data(secret)
            encrypted_backup_codes = self._encrypt_data(json.dumps(backup_codes))
            
            # 更新用户信息（暂时不启用，等待验证）
            user.totp_secret = encrypted_secret
            user.totp_backup_codes = encrypted_backup_codes
            
            # 生成二维码URL
            totp = pyotp.TOTP(secret)
            provisioning_uri = totp.provisioning_uri(
                name=user.username,
                issuer_name="沃尔玛绑卡系统"
            )
            
            # 生成二维码
            qr = qrcode.QRCode(version=1, box_size=10, border=5)
            qr.add_data(provisioning_uri)
            qr.make(fit=True)
            
            img = qr.make_image(fill_color="black", back_color="white")
            img_buffer = io.BytesIO()
            img.save(img_buffer, format='PNG')
            img_buffer.seek(0)
            
            # 转换为base64
            qr_code_base64 = base64.b64encode(img_buffer.getvalue()).decode()
            qr_code_url = f"data:image/png;base64,{qr_code_base64}"
            
            self.db.commit()
            
            # 记录日志
            self._log_totp_action(user.id, "setup", True, ip_address)
            
            return TOTPSetupResponse(
                secret=secret,
                qr_code_url=qr_code_url,
                backup_codes=backup_codes,
                manual_entry_key=secret
            )
            
        except Exception as e:
            self.db.rollback()
            self._log_totp_action(user.id, "setup", False, ip_address, str(e))
            raise e

    def verify_totp(self, user: User, code: str, ip_address: str = None) -> TOTPVerifyResponse:
        """
        验证TOTP码
        
        Args:
            user: 用户对象
            code: 验证码
            ip_address: 客户端IP地址
            
        Returns:
            TOTPVerifyResponse: 验证响应
        """
        try:
            if not user.totp_secret:
                return TOTPVerifyResponse(success=False, message="用户未设置双因子认证")
            
            # 解密密钥
            secret = self._decrypt_data(user.totp_secret)
            totp = pyotp.TOTP(secret)
            
            # 验证码（允许30秒的时间偏差）
            is_valid = totp.verify(code, valid_window=1)
            
            if is_valid:
                # 更新最后使用时间
                user.totp_last_used_at = datetime.now()
                self.db.commit()

                self._log_totp_action(user.id, "verify", True, ip_address)
                return TOTPVerifyResponse(success=True, message="验证成功")
            else:
                self._log_totp_action(user.id, "verify", False, ip_address, "验证码错误")
                return TOTPVerifyResponse(success=False, message="验证码错误")
                
        except Exception as e:
            self._log_totp_action(user.id, "verify", False, ip_address, str(e))
            return TOTPVerifyResponse(success=False, message="验证失败")

    def enable_totp(self, user: User, code: str, ip_address: str = None) -> bool:
        """
        启用TOTP（需要验证码确认）
        
        Args:
            user: 用户对象
            code: 验证码
            ip_address: 客户端IP地址
            
        Returns:
            bool: 是否成功启用
        """
        try:
            # 验证码
            verify_result = self.verify_totp(user, code, ip_address)
            if not verify_result.success:
                return False
            
            # 启用TOTP
            user.totp_enabled = True
            user.totp_setup_at = datetime.now()
            self.db.commit()
            
            self._log_totp_action(user.id, "enable", True, ip_address)
            return True
            
        except Exception as e:
            self.db.rollback()
            self._log_totp_action(user.id, "enable", False, ip_address, str(e))
            return False

    def disable_totp(self, user: User, password: str, code: str = None, 
                    ip_address: str = None) -> bool:
        """
        禁用TOTP
        
        Args:
            user: 用户对象
            password: 用户密码
            code: 验证码或备用码
            ip_address: 客户端IP地址
            
        Returns:
            bool: 是否成功禁用
        """
        try:
            from app.core.security import verify_password
            
            # 验证密码
            if not verify_password(password, user.hashed_password):
                self._log_totp_action(user.id, "disable", False, ip_address, "密码错误")
                return False
            
            # 如果启用了TOTP，需要验证码
            if user.totp_enabled and code:
                # 尝试验证TOTP码
                verify_result = self.verify_totp(user, code, ip_address)
                if not verify_result.success:
                    # 尝试验证备用码
                    if not self._verify_backup_code(user, code):
                        self._log_totp_action(user.id, "disable", False, ip_address, "验证码错误")
                        return False
            
            # 禁用TOTP
            user.totp_enabled = False
            user.totp_secret = None
            user.totp_backup_codes = None
            user.totp_last_used_at = None
            user.totp_setup_at = None
            
            self.db.commit()
            
            self._log_totp_action(user.id, "disable", True, ip_address)
            return True
            
        except Exception as e:
            self.db.rollback()
            self._log_totp_action(user.id, "disable", False, ip_address, str(e))
            return False

    def _verify_backup_code(self, user: User, code: str) -> bool:
        """
        验证备用恢复码

        Args:
            user: 用户对象
            code: 备用码

        Returns:
            bool: 是否验证成功
        """
        try:
            if not user.totp_backup_codes:
                return False

            # 解密备用码
            backup_codes_json = self._decrypt_data(user.totp_backup_codes)
            backup_codes = json.loads(backup_codes_json)

            if code in backup_codes:
                # 移除已使用的备用码
                backup_codes.remove(code)

                # 重新加密存储
                user.totp_backup_codes = self._encrypt_data(json.dumps(backup_codes))
                self.db.commit()

                self._log_totp_action(user.id, "backup_used", True)
                return True

            return False

        except Exception as e:
            logger.error(f"验证备用码失败: {e}")
            return False

    def get_totp_status(self, user: User) -> TOTPStatusResponse:
        """
        获取用户TOTP状态

        Args:
            user: 用户对象

        Returns:
            TOTPStatusResponse: TOTP状态
        """
        try:
            # 获取策略
            policy = self._get_user_totp_policy(user)
            is_required = policy.is_required if policy else False

            # 计算剩余备用码数量
            backup_codes_remaining = 0
            if user.totp_backup_codes:
                try:
                    backup_codes_json = self._decrypt_data(user.totp_backup_codes)
                    backup_codes = json.loads(backup_codes_json)
                    backup_codes_remaining = len(backup_codes)
                except:
                    pass

            # 计算宽限期到期时间
            grace_period_expires = None
            if is_required and not user.totp_enabled and policy:
                # 从用户创建时间开始计算宽限期
                if user.created_at:
                    # 处理created_at可能是datetime对象或字符串的情况
                    if isinstance(user.created_at, str):
                        created_at = datetime.fromisoformat(user.created_at.replace('Z', '+00:00'))
                    else:
                        created_at = user.created_at
                    grace_period_expires = created_at + timedelta(days=policy.grace_period_days)

            # 处理setup_at和last_used_at字段，支持datetime对象和字符串
            def safe_datetime_convert(dt_field):
                """安全地转换datetime字段，支持datetime对象和字符串"""
                if dt_field is None:
                    return None
                if isinstance(dt_field, datetime):
                    return dt_field
                if isinstance(dt_field, str):
                    try:
                        return datetime.fromisoformat(dt_field.replace('Z', '+00:00'))
                    except ValueError:
                        return None
                return None

            return TOTPStatusResponse(
                enabled=user.totp_enabled or False,
                setup_at=safe_datetime_convert(user.totp_setup_at),
                last_used_at=safe_datetime_convert(user.totp_last_used_at),
                backup_codes_remaining=backup_codes_remaining,
                is_required=is_required,
                grace_period_expires=grace_period_expires
            )

        except Exception as e:
            logger.error(f"获取TOTP状态失败: {e}")
            return TOTPStatusResponse(
                enabled=False,
                backup_codes_remaining=0,
                is_required=False
            )

    def _get_user_totp_policy(self, user: User) -> Optional[TOTPPolicy]:
        """
        获取用户的TOTP策略

        Args:
            user: 用户对象

        Returns:
            Optional[TOTPPolicy]: TOTP策略
        """
        try:
            # 获取用户的主要角色
            if user.roles:
                role_name = user.roles[0].name  # 修复：使用name字段而不是role_name
                policy = self.db.query(TOTPPolicy).filter(
                    TOTPPolicy.role_name == role_name
                ).first()
                return policy
            return None
        except Exception as e:
            logger.error(f"获取TOTP策略失败: {e}")
            return None

    def check_totp_required(self, user: User) -> Tuple[bool, bool]:
        """
        检查用户是否需要启用TOTP

        Args:
            user: 用户对象

        Returns:
            Tuple[bool, bool]: (是否必须启用, 是否在宽限期内)
        """
        try:
            policy = self._get_user_totp_policy(user)
            if not policy or not policy.is_required:
                return False, True

            # 如果已启用，不需要检查
            if user.totp_enabled:
                return True, True

            # 检查宽限期
            if user.created_at and policy.grace_period_days > 0:
                # 处理created_at可能是datetime对象或字符串的情况
                if isinstance(user.created_at, str):
                    created_at = datetime.fromisoformat(user.created_at.replace('Z', '+00:00'))
                else:
                    created_at = user.created_at
                grace_period_end = created_at + timedelta(days=policy.grace_period_days)
                # 修复时区问题：使用上海时区时间进行比较
                from app.utils.time_utils import get_current_time
                in_grace_period = get_current_time() < grace_period_end
                return True, in_grace_period

            return True, False

        except Exception as e:
            logger.error(f"检查TOTP要求失败: {e}")
            return False, True
