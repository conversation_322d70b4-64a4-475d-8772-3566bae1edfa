"""
测试Telegram模型
"""

import pytest
from datetime import datetime, timedelta
from sqlalchemy.orm import Session

from app.models.telegram_group import TelegramGroup, BindStatus, ChatType
from app.models.telegram_user import TelegramUser, VerificationStatus
from app.models.telegram_bot_config import TelegramBotConfig, ConfigType
from app.models.telegram_bot_log import TelegramBotLog, LogStatus
from app.models.merchant import Merchant
from app.models.user import User
from app.models.base import local_now


class TestTelegramGroup:
    """测试TelegramGroup模型"""
    
    def test_create_telegram_group(self, db: Session, test_merchant: Merchant):
        """测试创建Telegram群组"""
        group = TelegramGroup(
            chat_id=123456789,
            chat_title="测试群组",
            chat_type=ChatType.GROUP,
            merchant_id=test_merchant.id,
            bind_token="test_token_123",
            bind_status=BindStatus.PENDING
        )
        
        db.add(group)
        db.commit()
        db.refresh(group)
        
        assert group.id is not None
        assert group.chat_id == 123456789
        assert group.chat_title == "测试群组"
        assert group.chat_type == ChatType.GROUP
        assert group.bind_status == BindStatus.PENDING
        assert group.merchant_id == test_merchant.id
    
    def test_group_is_active(self, db: Session, test_merchant: Merchant):
        """测试群组活跃状态检查"""
        # 创建活跃群组
        active_group = TelegramGroup(
            chat_id=123456789,
            chat_title="活跃群组",
            chat_type=ChatType.GROUP,
            merchant_id=test_merchant.id,
            bind_token="active_token",
            bind_status=BindStatus.ACTIVE
        )
        
        # 创建待绑定群组
        pending_group = TelegramGroup(
            chat_id=987654321,
            chat_title="待绑定群组",
            chat_type=ChatType.GROUP,
            merchant_id=test_merchant.id,
            bind_token="pending_token",
            bind_status=BindStatus.PENDING
        )
        
        assert active_group.is_active() is True
        assert pending_group.is_active() is False
    
    def test_group_can_query_stats(self, db: Session, test_merchant: Merchant):
        """测试群组统计查询权限"""
        group = TelegramGroup(
            chat_id=123456789,
            chat_title="测试群组",
            chat_type=ChatType.GROUP,
            merchant_id=test_merchant.id,
            bind_token="test_token",
            bind_status=BindStatus.ACTIVE
        )
        
        assert group.can_query_stats() is True
        
        group.bind_status = BindStatus.SUSPENDED
        assert group.can_query_stats() is False
    
    def test_update_last_active(self, db: Session, test_merchant: Merchant):
        """测试更新最后活跃时间"""
        group = TelegramGroup(
            chat_id=123456789,
            chat_title="测试群组",
            chat_type=ChatType.GROUP,
            merchant_id=test_merchant.id,
            bind_token="test_token",
            bind_status=BindStatus.ACTIVE
        )
        
        db.add(group)
        db.commit()
        
        # 初始时间应该为空
        assert group.last_active_time is None
        
        # 更新活跃时间
        group.update_last_active()
        assert group.last_active_time is not None
        assert isinstance(group.last_active_time, datetime)
    
    def test_group_settings(self, db: Session, test_merchant: Merchant):
        """测试群组设置"""
        group = TelegramGroup(
            chat_id=123456789,
            chat_title="测试群组",
            chat_type=ChatType.GROUP,
            merchant_id=test_merchant.id,
            bind_token="test_token",
            bind_status=BindStatus.ACTIVE
        )
        
        # 测试默认设置
        default_settings = group.get_default_settings()
        assert isinstance(default_settings, dict)
        assert "auto_notification" in default_settings
        assert "timezone" in default_settings
        
        # 测试合并设置
        new_settings = {"auto_notification": False, "new_option": True}
        merged = group.merge_settings(new_settings)
        
        assert merged["auto_notification"] is False
        assert merged["new_option"] is True
        assert "timezone" in merged  # 保留默认设置


class TestTelegramUser:
    """测试TelegramUser模型"""
    
    def test_create_telegram_user(self, db: Session):
        """测试创建Telegram用户"""
        user = TelegramUser(
            telegram_user_id=123456789,
            telegram_username="testuser",
            telegram_first_name="Test",
            telegram_last_name="User",
            verification_status=VerificationStatus.PENDING
        )
        
        db.add(user)
        db.commit()
        db.refresh(user)
        
        assert user.id is not None
        assert user.telegram_user_id == 123456789
        assert user.telegram_username == "testuser"
        assert user.verification_status == VerificationStatus.PENDING
    
    def test_get_full_name(self, db: Session):
        """测试获取完整姓名"""
        # 有名字和姓氏
        user1 = TelegramUser(
            telegram_user_id=123456789,
            telegram_first_name="Test",
            telegram_last_name="User"
        )
        assert user1.get_full_name() == "Test User"
        
        # 只有名字
        user2 = TelegramUser(
            telegram_user_id=123456790,
            telegram_first_name="Test"
        )
        assert user2.get_full_name() == "Test"
        
        # 只有用户名
        user3 = TelegramUser(
            telegram_user_id=123456791,
            telegram_username="testuser"
        )
        assert user3.get_full_name() == "testuser"
        
        # 什么都没有
        user4 = TelegramUser(telegram_user_id=123456792)
        assert user4.get_full_name() == "User123456792"
    
    def test_is_verified(self, db: Session, test_user: User):
        """测试用户验证状态"""
        # 未验证用户
        unverified_user = TelegramUser(
            telegram_user_id=123456789,
            verification_status=VerificationStatus.PENDING
        )
        assert unverified_user.is_verified() is False
        
        # 已验证用户
        verified_user = TelegramUser(
            telegram_user_id=123456790,
            system_user_id=test_user.id,
            verification_status=VerificationStatus.VERIFIED
        )
        assert verified_user.is_verified() is True
    
    def test_generate_verification_token(self, db: Session):
        """测试生成验证令牌"""
        user = TelegramUser(
            telegram_user_id=123456789,
            verification_status=VerificationStatus.PENDING
        )
        
        token = user.generate_verification_token()
        
        assert token is not None
        assert len(token) == 32
        assert user.verification_token == token
        assert user.verification_status == VerificationStatus.PENDING
    
    def test_verify_with_system_user(self, db: Session, test_user: User):
        """测试与系统用户关联验证"""
        telegram_user = TelegramUser(
            telegram_user_id=123456789,
            verification_status=VerificationStatus.PENDING
        )
        
        telegram_user.verify_with_system_user(test_user.id)
        
        assert telegram_user.system_user_id == test_user.id
        assert telegram_user.verification_status == VerificationStatus.VERIFIED
        assert telegram_user.verification_time is not None
        assert telegram_user.verification_token is None


class TestTelegramBotConfig:
    """测试TelegramBotConfig模型"""
    
    def test_create_config(self, db: Session):
        """测试创建配置"""
        config = TelegramBotConfig(
            config_key="test_key",
            config_value="test_value",
            config_type=ConfigType.STRING,
            description="测试配置"
        )
        
        db.add(config)
        db.commit()
        db.refresh(config)
        
        assert config.id is not None
        assert config.config_key == "test_key"
        assert config.config_value == "test_value"
        assert config.config_type == ConfigType.STRING
    
    def test_get_parsed_value(self, db: Session):
        """测试解析配置值"""
        # 字符串类型
        string_config = TelegramBotConfig(
            config_key="string_key",
            config_value="test_value",
            config_type=ConfigType.STRING
        )
        assert string_config.get_parsed_value() == "test_value"
        
        # 布尔类型
        bool_config = TelegramBotConfig(
            config_key="bool_key",
            config_value="true",
            config_type=ConfigType.BOOLEAN
        )
        assert bool_config.get_parsed_value() is True
        
        # 数字类型
        number_config = TelegramBotConfig(
            config_key="number_key",
            config_value="123",
            config_type=ConfigType.NUMBER
        )
        assert number_config.get_parsed_value() == 123
        
        # JSON类型
        json_config = TelegramBotConfig(
            config_key="json_key",
            config_value='{"key": "value"}',
            config_type=ConfigType.JSON
        )
        parsed = json_config.get_parsed_value()
        assert isinstance(parsed, dict)
        assert parsed["key"] == "value"
    
    def test_set_value(self, db: Session):
        """测试设置配置值"""
        config = TelegramBotConfig(
            config_key="test_key",
            config_value="",
            config_type=ConfigType.JSON
        )
        
        # 设置字典值
        test_dict = {"key1": "value1", "key2": 123}
        config.set_value(test_dict)
        
        assert '"key1": "value1"' in config.config_value
        assert '"key2": 123' in config.config_value


class TestTelegramBotLog:
    """测试TelegramBotLog模型"""
    
    def test_create_log(self, db: Session):
        """测试创建日志"""
        log = TelegramBotLog(
            chat_id=123456789,
            user_id=987654321,
            command="/test",
            status=LogStatus.SUCCESS,
            execution_time=100
        )
        
        db.add(log)
        db.commit()
        db.refresh(log)
        
        assert log.id is not None
        assert log.chat_id == 123456789
        assert log.user_id == 987654321
        assert log.command == "/test"
        assert log.status == LogStatus.SUCCESS
        assert log.execution_time == 100
    
    def test_log_status_checks(self, db: Session):
        """测试日志状态检查"""
        success_log = TelegramBotLog(
            chat_id=123456789,
            user_id=987654321,
            command="/test",
            status=LogStatus.SUCCESS
        )
        assert success_log.is_success() is True
        assert success_log.is_error() is False
        assert success_log.is_permission_denied() is False
        
        error_log = TelegramBotLog(
            chat_id=123456789,
            user_id=987654321,
            command="/test",
            status=LogStatus.ERROR
        )
        assert error_log.is_success() is False
        assert error_log.is_error() is True
        assert error_log.is_permission_denied() is False
    
    def test_create_log_methods(self, db: Session):
        """测试日志创建方法"""
        # 测试成功日志
        success_log = TelegramBotLog.create_log(
            db, 123456789, 987654321, "/test", LogStatus.SUCCESS,
            execution_time=100
        )
        assert success_log.status == LogStatus.SUCCESS
        assert success_log.execution_time == 100
        
        # 测试错误日志
        error_log = TelegramBotLog.create_log(
            db, 123456789, 987654321, "/test", LogStatus.ERROR,
            error_message="测试错误"
        )
        assert error_log.status == LogStatus.ERROR
        assert error_log.error_message == "测试错误"
