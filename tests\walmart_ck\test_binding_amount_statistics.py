"""
CK绑卡金额统计功能测试

测试内容：
1. API接口正确性
2. 数据权限隔离
3. 筛选功能
4. 统计数据准确性
"""

import pytest
import requests
import json
from datetime import datetime, timedelta
from test.conftest import TestBase, get_test_config, get_test_accounts


class TestCKBindingAmountStatistics:
    """CK绑卡金额统计测试类"""

    def setup_method(self):
        """测试方法设置"""
        self.test_base = TestBase()
        self.config = get_test_config()
        self.accounts = get_test_accounts()
        self.api_base = f"{self.config['base_url']}{self.config['api_prefix']}"

    def test_get_binding_amount_statistics_super_admin(self):
        """测试超级管理员获取绑卡金额统计"""
        # 登录超级管理员
        admin_account = self.accounts['super_admin']
        token = self.test_base.login(admin_account['username'], admin_account['password'])
        assert token is not None, "超级管理员登录失败"

        # 发送请求
        status_code, data = self.test_base.make_request(
            "GET", "/walmart-ck/binding-amount-statistics", token
        )

        assert status_code == 200, f"请求失败，状态码: {status_code}, 响应: {data}"
        assert data.get('success') is True, f"API返回失败: {data}"
        assert 'data' in data, "响应中缺少data字段"

        # 验证返回数据结构
        statistics = data['data']
        assert 'summary' in statistics
        assert 'ck_details' in statistics
        assert 'filters' in statistics
        
        # 验证汇总数据结构
        summary = statistics['summary']
        required_fields = [
            'total_cks', 'total_records', 'total_success', 'success_rate',
            'total_request_amount', 'total_actual_amount', 'amount_difference'
        ]
        for field in required_fields:
            assert field in summary
            assert isinstance(summary[field], (int, float))

    def test_get_binding_amount_statistics_merchant_admin(self):
        """测试商户管理员获取绑卡金额统计（数据隔离）"""
        # 登录商户管理员
        merchant_account = self.accounts['merchant_admin']
        token = self.test_base.login(merchant_account['username'], merchant_account['password'])
        assert token is not None, "商户管理员登录失败"

        # 发送请求
        status_code, data = self.test_base.make_request(
            "GET", "/walmart-ck/binding-amount-statistics", token
        )

        assert status_code == 200, f"请求失败，状态码: {status_code}, 响应: {data}"
        assert data.get('success') is True, f"API返回失败: {data}"

        # 验证商户管理员只能看到自己商户的数据
        statistics = data['data']
        if statistics['ck_details']:
            for ck_detail in statistics['ck_details']:
                # 所有CK都应该属于test1用户的商户
                assert 'merchant_name' in ck_detail

    def test_data_permission_based_access_control(self):
        """测试基于数据权限的访问控制（不依赖硬编码角色）"""
        # 登录超级管理员
        admin_account = self.accounts['super_admin']
        token = self.test_base.login(admin_account['username'], admin_account['password'])
        assert token is not None, "超级管理员登录失败"

        # 测试超级管理员可以访问所有数据
        status_code, data = self.test_base.make_request(
            "GET", "/walmart-ck/binding-amount-statistics", token
        )
        assert status_code == 200, f"超级管理员请求失败: {data}"

        # 测试商户管理员的数据隔离
        merchant_account = self.accounts['merchant_admin']
        merchant_token = self.test_base.login(merchant_account['username'], merchant_account['password'])

        # 商户管理员尝试访问其他商户数据应该被拒绝
        status_code, data = self.test_base.make_request(
            "GET", "/walmart-ck/binding-amount-statistics", merchant_token,
            params={'merchant_id': 999}  # 假设的其他商户ID
        )

        # 应该返回400或403错误，或者忽略merchant_id参数
        assert status_code in [200, 400, 403], f"意外的状态码: {status_code}"

        if status_code == 200:
            # 如果返回200，验证数据是否正确隔离
            statistics = data['data']
            # 应该只包含商户管理员自己商户的数据
            assert statistics['filters']['merchant_id'] != 999, "数据隔离失败"

    def test_department_permission_isolation(self):
        """测试部门级权限隔离"""
        # 登录商户管理员
        merchant_account = self.accounts['merchant_admin']
        token = self.test_base.login(merchant_account['username'], merchant_account['password'])
        assert token is not None, "商户管理员登录失败"

        # 获取部门列表
        dept_status_code, dept_data = self.test_base.make_request(
            "GET", "/departments", token
        )

        if dept_status_code == 200:
            departments = dept_data.get('data', {}).get('items', [])

            if departments:
                # 测试访问有权限的部门
                valid_dept_id = departments[0]['id']
                status_code, data = self.test_base.make_request(
                    "GET", "/walmart-ck/binding-amount-statistics", token,
                    params={'department_id': valid_dept_id}
                )
                assert status_code == 200, f"访问有权限部门失败: {data}"

                # 验证返回的数据确实是指定部门的
                statistics = data['data']
                assert statistics['filters']['department_id'] == valid_dept_id

    def test_get_binding_amount_statistics_with_department_filter(self):
        """测试部门筛选功能"""
        # 登录超级管理员
        admin_account = self.accounts['super_admin']
        token = self.test_base.login(admin_account['username'], admin_account['password'])
        assert token is not None, "超级管理员登录失败"

        # 先获取部门列表
        dept_status_code, dept_data = self.test_base.make_request(
            "GET", "/departments", token
        )
        assert dept_status_code == 200, f"获取部门列表失败: {dept_data}"
        departments = dept_data.get('data', {}).get('items', [])
        
        if departments:
            department_id = departments[0]['id']

            # 使用部门筛选获取统计
            status_code, data = self.test_base.make_request(
                "GET", "/walmart-ck/binding-amount-statistics", token,
                params={'department_id': department_id}
            )

            assert status_code == 200, f"请求失败，状态码: {status_code}, 响应: {data}"
            assert data.get('success') is True, f"API返回失败: {data}"

            # 验证筛选条件被正确应用
            statistics = data['data']
            assert statistics['filters']['department_id'] == department_id

    def test_get_binding_amount_statistics_with_date_filter(self):
        """测试日期筛选功能"""
        # 登录超级管理员
        admin_account = self.accounts['super_admin']
        token = self.test_base.login(admin_account['username'], admin_account['password'])
        assert token is not None, "超级管理员登录失败"

        # 设置日期范围（最近7天）
        end_date = datetime.now()
        start_date = end_date - timedelta(days=7)

        status_code, data = self.test_base.make_request(
            "GET", "/walmart-ck/binding-amount-statistics", token,
            params={
                'start_date': start_date.strftime('%Y-%m-%d'),
                'end_date': end_date.strftime('%Y-%m-%d')
            }
        )

        assert status_code == 200, f"请求失败，状态码: {status_code}, 响应: {data}"
        assert data.get('success') is True, f"API返回失败: {data}"
        
        # 验证日期筛选条件被正确应用
        statistics = data['data']
        assert statistics['filters']['start_date'] is not None
        assert statistics['filters']['end_date'] is not None

    def test_get_binding_amount_statistics_permission_denied(self):
        """测试无权限用户访问统计接口"""
        # 使用无效的token
        status_code, data = self.test_base.make_request(
            "GET", "/walmart-ck/binding-amount-statistics", "invalid_token"
        )

        assert status_code == 401, f"应该返回401未授权，实际状态码: {status_code}"

    def test_get_binding_amount_statistics_data_consistency(self):
        """测试统计数据一致性"""
        # 登录超级管理员
        admin_account = self.accounts['super_admin']
        token = self.test_base.login(admin_account['username'], admin_account['password'])
        assert token is not None, "超级管理员登录失败"

        status_code, data = self.test_base.make_request(
            "GET", "/walmart-ck/binding-amount-statistics", token
        )

        assert status_code == 200, f"请求失败，状态码: {status_code}, 响应: {data}"
        statistics = data['data']
        
        # 验证汇总数据与详细数据的一致性
        summary = statistics['summary']
        ck_details = statistics['ck_details']
        
        if ck_details:
            # 计算详细数据的汇总
            detail_total_records = sum(detail['total_records'] for detail in ck_details)
            detail_total_success = sum(detail['success_count'] for detail in ck_details)
            detail_total_actual_amount = sum(detail['total_actual_amount'] for detail in ck_details)
            
            # 验证一致性
            assert summary['total_records'] == detail_total_records
            assert summary['total_success'] == detail_total_success
            assert summary['total_actual_amount'] == detail_total_actual_amount

    def test_get_binding_amount_statistics_with_combined_filters(self):
        """测试组合筛选条件"""
        headers = get_auth_headers('admin', '7c222fb2927d828af22f592134e8932480637c0d')
        
        # 获取部门列表
        dept_response = requests.get(
            f"{API_BASE_URL}/departments",
            headers=headers
        )
        
        if dept_response.status_code == 200:
            departments = dept_response.json()['data']['items']
            
            if departments:
                department_id = departments[0]['id']
                end_date = datetime.now()
                start_date = end_date - timedelta(days=30)
                
                response = requests.get(
                    f"{API_BASE_URL}/walmart-ck/binding-amount-statistics",
                    headers=headers,
                    params={
                        'department_id': department_id,
                        'start_date': start_date.strftime('%Y-%m-%d'),
                        'end_date': end_date.strftime('%Y-%m-%d')
                    }
                )
                
                assert response.status_code == 200
                data = response.json()
                assert data['success'] is True
                
                # 验证所有筛选条件都被应用
                statistics = data['data']
                filters = statistics['filters']
                assert filters['department_id'] == department_id
                assert filters['start_date'] is not None
                assert filters['end_date'] is not None

    def test_binding_amount_statistics_response_format(self):
        """测试响应格式的正确性"""
        headers = get_auth_headers('admin', '7c222fb2927d828af22f592134e8932480637c0d')
        
        response = requests.get(
            f"{API_BASE_URL}/walmart-ck/binding-amount-statistics",
            headers=headers
        )
        
        assert response.status_code == 200
        data = response.json()
        
        # 验证基本响应结构
        assert 'success' in data
        assert 'data' in data
        assert 'message' in data
        
        statistics = data['data']
        
        # 验证CK详情结构
        if statistics['ck_details']:
            for ck_detail in statistics['ck_details']:
                required_fields = [
                    'ck_id', 'ck_sign', 'department_name', 'merchant_name',
                    'total_records', 'success_count', 'success_rate',
                    'total_request_amount', 'total_actual_amount', 'amount_difference'
                ]
                for field in required_fields:
                    assert field in ck_detail


if __name__ == '__main__':
    # 运行测试
    pytest.main([__file__, '-v'])
