#!/usr/bin/env python3
"""
沃尔玛API响应格式验证工具
用于验证实际API响应是否符合预期格式
"""

import sys
import os
import json
import asyncio
from typing import Dict, Any, List

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app.core.walmart_api import WalmartAPI
from app.services.ck_validation_service import CKValidationService
from app.db.session import SessionLocal
from app.models.walmart_ck import WalmartCK
from app.core.logging import get_logger

logger = get_logger("api_response_validator")


class APIResponseValidator:
    """API响应格式验证器"""

    def __init__(self):
        self.db = SessionLocal()
        self.validation_results = []

    def __enter__(self):
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        self.db.close()

    def validate_response_structure(self, response_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        验证响应结构是否符合预期格式
        
        Args:
            response_data: API响应数据
            
        Returns:
            Dict[str, Any]: 验证结果
        """
        validation_result = {
            "is_valid_structure": True,
            "missing_fields": [],
            "unexpected_fields": [],
            "field_type_errors": [],
            "details": {}
        }

        # 预期的响应结构
        expected_structure = {
            "logId": str,
            "status": bool,
            "error": dict,
            "data": (dict, type(None))
        }

        # 预期的error结构
        expected_error_structure = {
            "errorcode": (int, str, type(None)),
            "message": (str, type(None)),
            "redirect": (str, type(None)),
            "validators": (list, type(None))
        }

        # 检查顶级字段
        for field, expected_type in expected_structure.items():
            if field not in response_data:
                validation_result["missing_fields"].append(field)
                validation_result["is_valid_structure"] = False
            else:
                value = response_data[field]
                if not isinstance(value, expected_type):
                    validation_result["field_type_errors"].append({
                        "field": field,
                        "expected_type": str(expected_type),
                        "actual_type": str(type(value)),
                        "value": value
                    })
                    validation_result["is_valid_structure"] = False

        # 检查error字段结构
        if "error" in response_data and isinstance(response_data["error"], dict):
            error_data = response_data["error"]
            for field, expected_type in expected_error_structure.items():
                if field not in error_data:
                    validation_result["missing_fields"].append(f"error.{field}")
                    validation_result["is_valid_structure"] = False
                else:
                    value = error_data[field]
                    if not isinstance(value, expected_type):
                        validation_result["field_type_errors"].append({
                            "field": f"error.{field}",
                            "expected_type": str(expected_type),
                            "actual_type": str(type(value)),
                            "value": value
                        })
                        validation_result["is_valid_structure"] = False

        # 检查data字段结构（如果存在且不为None）
        if "data" in response_data and response_data["data"] is not None:
            data = response_data["data"]
            if isinstance(data, dict):
                # 检查常见的data字段
                expected_data_fields = {
                    "cardCount": (int, type(None)),
                    "nickName": (str, type(None)),
                    "headImg": (str, type(None)),
                    "upcardOrderUrl": (str, type(None))
                }
                
                for field, expected_type in expected_data_fields.items():
                    if field in data:
                        value = data[field]
                        if not isinstance(value, expected_type):
                            validation_result["field_type_errors"].append({
                                "field": f"data.{field}",
                                "expected_type": str(expected_type),
                                "actual_type": str(type(value)),
                                "value": value
                            })

        # 记录详细信息
        validation_result["details"] = {
            "response_keys": list(response_data.keys()),
            "status_value": response_data.get("status"),
            "error_code": response_data.get("error", {}).get("errorcode"),
            "has_data": response_data.get("data") is not None,
            "log_id": response_data.get("logId")
        }

        return validation_result

    async def test_query_user_response_format(self, test_sign: str) -> Dict[str, Any]:
        """
        测试query_user接口的响应格式
        
        Args:
            test_sign: 测试用的CK签名
            
        Returns:
            Dict[str, Any]: 测试结果
        """
        try:
            logger.info(f"测试query_user接口响应格式，使用签名: {test_sign[:20]}...")
            
            # 创建API客户端
            api_client = WalmartAPI(sign=test_sign)
            
            # 调用query_user接口
            response = await api_client.query_user(db=self.db)
            
            # 解析响应
            if response.status_code != 200:
                return {
                    "success": False,
                    "error": f"HTTP错误: {response.status_code}",
                    "response_text": response.text if hasattr(response, 'text') else str(response)
                }
            
            try:
                response_data = response.json()
            except Exception as e:
                return {
                    "success": False,
                    "error": f"JSON解析失败: {e}",
                    "response_text": response.text if hasattr(response, 'text') else str(response)
                }
            
            # 验证响应结构
            structure_validation = self.validate_response_structure(response_data)
            
            # 测试CK验证服务的解析逻辑
            validation_service = CKValidationService(self.db)
            
            # 创建临时CK对象用于测试
            temp_ck = WalmartCK(
                id=999999,
                sign=test_sign,
                total_limit=20,
                bind_count=0,
                active=True,
                merchant_id=1,
                department_id=1,
                is_deleted=False
            )
            
            # 测试解析逻辑
            parse_result = validation_service._parse_validation_response(response, temp_ck.id)
            
            return {
                "success": True,
                "http_status": response.status_code,
                "raw_response": response_data,
                "structure_validation": structure_validation,
                "parse_result": parse_result,
                "recommendations": self._generate_recommendations(response_data, structure_validation, parse_result)
            }
            
        except Exception as e:
            logger.error(f"测试query_user响应格式时发生异常: {e}")
            return {
                "success": False,
                "error": f"测试异常: {e}",
                "exception_type": type(e).__name__
            }

    def _generate_recommendations(self, response_data: Dict[str, Any], 
                                structure_validation: Dict[str, Any], 
                                parse_result: Dict[str, Any]) -> List[str]:
        """
        生成改进建议
        
        Args:
            response_data: 原始响应数据
            structure_validation: 结构验证结果
            parse_result: 解析结果
            
        Returns:
            List[str]: 建议列表
        """
        recommendations = []
        
        # 结构验证建议
        if not structure_validation["is_valid_structure"]:
            if structure_validation["missing_fields"]:
                recommendations.append(f"缺少必要字段: {', '.join(structure_validation['missing_fields'])}")
            
            if structure_validation["field_type_errors"]:
                for error in structure_validation["field_type_errors"]:
                    recommendations.append(
                        f"字段类型错误: {error['field']} 期望 {error['expected_type']} 实际 {error['actual_type']}"
                    )
        
        # 解析结果建议
        if not parse_result.get("is_valid"):
            error_code = parse_result.get("error_code")
            error_message = parse_result.get("error_message")
            
            if error_code:
                recommendations.append(f"错误码 {error_code} 需要特殊处理")
            
            if parse_result.get("should_disable"):
                recommendations.append("此响应会导致CK被禁用，请确认是否符合预期")
        
        # 数据完整性建议
        status = response_data.get("status")
        error_code = response_data.get("error", {}).get("errorcode")
        data = response_data.get("data")
        
        if status is True and data is None:
            recommendations.append("status=true但data为空，可能需要检查业务逻辑")
        
        if status is True and error_code not in [None, 0, 1]:
            recommendations.append(f"status=true但errorcode={error_code}，需要确认是否为正常状态")
        
        return recommendations

    def generate_test_report(self, test_results: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        生成测试报告
        
        Args:
            test_results: 测试结果列表
            
        Returns:
            Dict[str, Any]: 测试报告
        """
        total_tests = len(test_results)
        successful_tests = sum(1 for r in test_results if r.get("success", False))
        
        report = {
            "summary": {
                "total_tests": total_tests,
                "successful_tests": successful_tests,
                "failed_tests": total_tests - successful_tests,
                "success_rate": f"{successful_tests/total_tests*100:.1f}%" if total_tests > 0 else "0%"
            },
            "test_results": test_results,
            "overall_recommendations": []
        }
        
        # 生成整体建议
        if successful_tests > 0:
            # 分析成功的测试结果
            successful_results = [r for r in test_results if r.get("success", False)]
            
            # 检查响应格式一致性
            status_values = [r.get("raw_response", {}).get("status") for r in successful_results]
            error_codes = [r.get("raw_response", {}).get("error", {}).get("errorcode") for r in successful_results]
            
            if len(set(status_values)) > 1:
                report["overall_recommendations"].append("响应中status字段值不一致，需要统一处理逻辑")
            
            if len(set(error_codes)) > 1:
                report["overall_recommendations"].append("响应中errorcode字段值变化较大，需要完善错误码处理")
        
        return report


async def main():
    """主函数"""
    print("沃尔玛API响应格式验证工具")
    print("=" * 50)
    
    # 这里需要提供真实的CK签名进行测试
    # 注意：请使用测试环境的CK，避免影响生产数据
    test_signs = [
        # "your_test_ck_sign_1",
        # "your_test_ck_sign_2",
    ]
    
    if not test_signs:
        print("警告：没有提供测试CK签名")
        print("请在代码中添加测试用的CK签名以进行实际测试")
        print("格式示例：25487f6f129649999ef6b1f269b2a1f5@d0e0e25d37720f856a3ba753089b1e47#mmb5Lz2g3i4ilzn/kHXpFg==#26")
        return
    
    with APIResponseValidator() as validator:
        test_results = []
        
        for i, test_sign in enumerate(test_signs):
            print(f"\n测试 {i+1}/{len(test_signs)}: {test_sign[:20]}...")
            
            result = await validator.test_query_user_response_format(test_sign)
            test_results.append(result)
            
            if result["success"]:
                print("✅ 测试成功")
                if result["structure_validation"]["is_valid_structure"]:
                    print("✅ 响应结构验证通过")
                else:
                    print("❌ 响应结构验证失败")
                
                if result["parse_result"]["is_valid"]:
                    print("✅ 解析逻辑验证通过")
                else:
                    print("❌ 解析逻辑验证失败")
            else:
                print(f"❌ 测试失败: {result.get('error', '未知错误')}")
        
        # 生成测试报告
        report = validator.generate_test_report(test_results)
        
        print("\n" + "=" * 50)
        print("测试报告")
        print("=" * 50)
        print(f"总测试数: {report['summary']['total_tests']}")
        print(f"成功测试: {report['summary']['successful_tests']}")
        print(f"失败测试: {report['summary']['failed_tests']}")
        print(f"成功率: {report['summary']['success_rate']}")
        
        if report["overall_recommendations"]:
            print("\n整体建议:")
            for rec in report["overall_recommendations"]:
                print(f"- {rec}")
        
        # 保存详细报告
        report_file = f"api_response_validation_report.json"
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2)
        
        print(f"\n详细报告已保存到: {report_file}")


if __name__ == "__main__":
    asyncio.run(main())
