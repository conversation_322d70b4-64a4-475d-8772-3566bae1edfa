"""
CK统计命令处理器
"""

import time
from datetime import datetime, timedelta
from typing import Dict, Any, Optional
from telegram import Update
from telegram.ext import ContextTypes
from sqlalchemy import func, case, text

from app.core.logging import get_logger
from app.models.base import local_now
from app.models.telegram_group import TelegramGroup
from app.models.walmart_ck import WalmartCK
from app.models.card_record import CardRecord, CardStatus

from .base_handler import BaseCommandHandler
from ..services.message_formatter import message_formatter
from ..utils.message_utils import format_ck_statistics_html, format_ck_usage_html

logger = get_logger(__name__)


class CKStatsCommandHandler(BaseCommandHandler):
    """CK统计命令处理器"""

    def _get_department_and_children_ids(self, department_id: int) -> list[int]:
        """获取部门及其所有子部门的ID列表（递归）"""
        try:
            # 使用递归CTE查询获取部门及其所有子部门
            sql = text("""
                WITH RECURSIVE department_tree AS (
                    -- 基础查询：指定部门
                    SELECT id, parent_id, merchant_id
                    FROM departments
                    WHERE id = :department_id

                    UNION ALL

                    -- 递归查询：子部门的子部门
                    SELECT d.id, d.parent_id, d.merchant_id
                    FROM departments d
                    INNER JOIN department_tree dt ON d.parent_id = dt.id
                )
                SELECT id FROM department_tree
            """)

            result = self.db.execute(sql, {'department_id': department_id})
            department_ids = [row.id for row in result.fetchall()]

            logger.info(f"部门 {department_id} 及其子部门ID列表: {department_ids}")
            return department_ids

        except Exception as e:
            logger.error(f"获取部门层级ID失败: {e}")
            # 如果递归查询失败，至少返回原部门ID
            return [department_id]

    async def verify_permissions(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """验证权限 - 群组绑定成功后所有群成员都可以查询"""
        chat_id = update.effective_chat.id
        user_id = update.effective_user.id

        # 验证群组已绑定
        group = await self.verify_group_bound(chat_id)

        # 新的权限逻辑：群组绑定成功后，所有群成员都可以查询CK统计数据
        # 无需验证用户身份，只需要群组绑定成功即可
        self.logger.info(f"群组 {chat_id} 已绑定，用户 {user_id} 可以查询CK统计数据")

        return group
    
    async def execute_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """执行CK统计命令"""
        group = await self.verify_permissions(update, context)

        # 获取CK统计数据
        ck_stats = await self._get_ck_statistics(group)

        # 格式化响应 - 使用HTML格式避免Markdown解析问题
        response_text = format_ck_statistics_html(group, ck_stats)
        await self.send_response(update, response_text, context=context, parse_mode='HTML')

        return {
            "command": "ck_stats",
            "merchant_id": group.merchant_id,
            "department_id": group.department_id,
            "stats": ck_stats
        }

    async def handle(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """处理CK统计命令（兼容BaseHandler的handle方法）"""
        return await self.execute_command(update, context)
    
    async def handle_today(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """处理今日CK统计命令"""
        group = await self.verify_permissions(update, context)
        
        # 获取今日CK使用统计
        today = local_now().date()
        usage_stats = await self._get_ck_usage_statistics(group, today, today)
        
        # 格式化响应 - 使用HTML格式避免Markdown解析问题
        response_text = format_ck_usage_html(group, usage_stats, "今日")
        await self.send_response(update, response_text, context=context, parse_mode='HTML')
        
        return {
            "command": "ck_stats_today",
            "date": today.isoformat(),
            "merchant_id": group.merchant_id,
            "stats": usage_stats
        }

    async def handle_yesterday(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """处理昨日CK统计命令"""
        group = await self.verify_permissions(update, context)

        # 获取昨日CK使用统计
        today = local_now().date()
        yesterday = today - timedelta(days=1)
        usage_stats = await self._get_ck_usage_statistics(group, yesterday, yesterday)

        # 格式化响应 - 使用HTML格式避免Markdown解析问题
        response_text = format_ck_usage_html(group, usage_stats, "昨日")
        await self.send_response(update, response_text, context=context, parse_mode='HTML')

        return {
            "command": "ck_stats_yesterday",
            "date": yesterday.isoformat(),
            "merchant_id": group.merchant_id,
            "stats": usage_stats
        }

    async def handle_week(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """处理本周CK统计命令"""
        group = await self.verify_permissions(update, context)
        
        # 计算本周日期范围
        today = local_now().date()
        start_of_week = today - timedelta(days=today.weekday())
        end_of_week = start_of_week + timedelta(days=6)
        
        # 获取本周CK使用统计
        usage_stats = await self._get_ck_usage_statistics(group, start_of_week, end_of_week)
        
        # 格式化响应 - 使用HTML格式避免Markdown解析问题
        response_text = format_ck_usage_html(group, usage_stats, "本周")
        await self.send_response(update, response_text, context=context, parse_mode='HTML')
        
        return {
            "command": "ck_stats_week",
            "start_date": start_of_week.isoformat(),
            "end_date": end_of_week.isoformat(),
            "merchant_id": group.merchant_id,
            "stats": usage_stats
        }
    
    async def handle_month(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """处理本月CK统计命令"""
        group = await self.verify_permissions(update, context)
        
        # 计算本月日期范围
        today = local_now().date()
        start_of_month = today.replace(day=1)
        if today.month == 12:
            end_of_month = today.replace(year=today.year + 1, month=1, day=1) - timedelta(days=1)
        else:
            end_of_month = today.replace(month=today.month + 1, day=1) - timedelta(days=1)
        
        # 获取本月CK使用统计
        usage_stats = await self._get_ck_usage_statistics(group, start_of_month, end_of_month)
        
        # 格式化响应 - 使用HTML格式避免Markdown解析问题
        response_text = format_ck_usage_html(group, usage_stats, "本月")
        await self.send_response(update, response_text, context=context, parse_mode='HTML')
        
        return {
            "command": "ck_stats_month",
            "start_date": start_of_month.isoformat(),
            "end_date": end_of_month.isoformat(),
            "merchant_id": group.merchant_id,
            "stats": usage_stats
        }
    
    async def _get_ck_statistics(self, group: TelegramGroup) -> Dict[str, Any]:
        """获取CK基础统计数据"""
        try:
            
            # 构建基础查询 - 严格按商户和部门过滤
            query = self.db.query(WalmartCK).filter(
                WalmartCK.merchant_id == group.merchant_id,
                WalmartCK.is_deleted == False
            )

            # 如果群组绑定了特定部门，查询该部门及其所有子部门的CK
            if group.department_id:
                department_ids = self._get_department_and_children_ids(group.department_id)
                query = query.filter(WalmartCK.department_id.in_(department_ids))
            
            all_cks = query.all()
            
            # 统计CK状态
            total_count = len(all_cks)
            active_count = len([ck for ck in all_cks if ck.active])
            inactive_count = total_count - active_count
            
            # 计算可用CK数量（active且未达到限制）
            available_count = 0
            expired_count = 0
            
            for ck in all_cks:
                if not ck.active:
                    continue
                    
                # 检查是否达到总限制
                if ck.total_limit and ck.bind_count >= ck.total_limit:
                    expired_count += 1
                else:
                    available_count += 1
            
            # 获取绑卡统计（基于CK使用记录）
            bind_stats = self._get_ck_bind_statistics(group, all_cks)
            
            return {
                "total_count": total_count,
                "active_count": active_count,
                "inactive_count": inactive_count,
                "available_count": available_count,
                "expired_count": expired_count,
                "bind_stats": bind_stats,
                "ck_details": self._get_ck_details(all_cks[:10])  # 只显示前10个CK的详情
            }
            
        except Exception as e:
            logger.error(f"获取CK统计数据失败: {e}")
            return {
                "total_count": 0,
                "active_count": 0,
                "inactive_count": 0,
                "available_count": 0,
                "expired_count": 0,
                "bind_stats": {},
                "ck_details": []
            }
    
    def _get_ck_bind_statistics(self, group: TelegramGroup, cks: list) -> Dict[str, Any]:
        """获取CK绑卡统计"""
        try:
            from sqlalchemy import func
            
            if not cks:
                return {
                    "total_bind_count": 0,
                    "success_bind_count": 0,
                    "failed_bind_count": 0,
                    "success_rate": 0.0
                }
            
            ck_ids = [ck.id for ck in cks]
            
            # 查询绑卡记录统计
            bind_query = self.db.query(
                func.count(CardRecord.id).label('total_count'),
                func.sum(case((CardRecord.status == CardStatus.SUCCESS, 1), else_=0)).label('success_count'),
                func.sum(case((CardRecord.status == CardStatus.FAILED, 1), else_=0)).label('failed_count')
            ).filter(
                CardRecord.walmart_ck_id.in_(ck_ids),
                CardRecord.merchant_id == group.merchant_id
            )
            
            # 如果群组绑定了特定部门，统计该部门及其所有子部门的绑卡记录
            if group.department_id:
                department_ids = self._get_department_and_children_ids(group.department_id)
                bind_query = bind_query.filter(CardRecord.department_id.in_(department_ids))
            
            result = bind_query.first()
            
            total_count = result.total_count or 0
            success_count = result.success_count or 0
            failed_count = result.failed_count or 0
            success_rate = (success_count / total_count * 100) if total_count > 0 else 0.0
            
            return {
                "total_bind_count": total_count,
                "success_bind_count": success_count,
                "failed_bind_count": failed_count,
                "success_rate": round(success_rate, 2)
            }
            
        except Exception as e:
            logger.error(f"获取CK绑卡统计失败: {e}")
            return {
                "total_bind_count": 0,
                "success_bind_count": 0,
                "failed_bind_count": 0,
                "success_rate": 0.0
            }
    
    def _get_ck_details(self, cks: list) -> list:
        """获取CK详情列表（脱敏处理）"""
        details = []
        for ck in cks:
            # 脱敏处理：只显示CK ID和部分信息
            masked_sign = f"CK{ck.id}***{ck.sign[-6:] if len(ck.sign) > 6 else '***'}"
            
            status = "可用"
            if not ck.active:
                status = "已禁用"
            elif ck.total_limit and ck.bind_count >= ck.total_limit:
                status = "已达限制"
            
            details.append({
                "id": ck.id,
                "masked_sign": masked_sign,
                "description": ck.description or "无描述",
                "bind_count": ck.bind_count,
                "total_limit": ck.total_limit,
                "status": status,
                "usage_rate": f"{(ck.bind_count / ck.total_limit * 100):.1f}%" if ck.total_limit else "无限制"
            })
        
        return details

    async def _get_ck_usage_statistics(self, group: TelegramGroup, start_date, end_date) -> Dict[str, Any]:
        """获取CK使用统计（按时间段）"""
        from app.utils.datetime_utils import is_today

        # 【修复数据实时性】刷新数据库会话，确保能读取到最新数据
        self.refresh_db_session()

        # 直接查询数据库获取实时数据，不使用缓存

        try:

            # 构建CK查询 - 严格按商户和部门过滤
            ck_query = self.db.query(WalmartCK).filter(
                WalmartCK.merchant_id == group.merchant_id,
                WalmartCK.is_deleted == False
            )

            if group.department_id:
                department_ids = self._get_department_and_children_ids(group.department_id)
                ck_query = ck_query.filter(WalmartCK.department_id.in_(department_ids))

            cks = ck_query.all()
            ck_ids = [ck.id for ck in cks] if cks else []

            if not ck_ids:
                return {
                    "period": f"{start_date} 至 {end_date}",
                    "total_usage": 0,
                    "success_usage": 0,
                    "failed_usage": 0,
                    "success_rate": 0.0,
                    "active_ck_count": 0,
                    "used_ck_count": 0,
                    "top_cks": []
                }

            # 【修复】使用正确的时间范围查询绑卡记录
            from app.utils.datetime_utils import get_date_range_for_query, debug_time_range, is_today

            # 获取正确的时间范围
            query_start, query_end = get_date_range_for_query(start_date, end_date)

            # 记录调试信息（仅今日数据）
            if is_today(start_date) and start_date == end_date:
                debug_info = debug_time_range(start_date, end_date)
                logger.info(f"[CK_STATS_TODAY_DEBUG] 今日CK统计查询时间范围: {debug_info}")

            # 查询时间段内的绑卡记录 - 使用正确的datetime范围
            usage_query = self.db.query(
                CardRecord.walmart_ck_id,
                func.count(CardRecord.id).label('total_count'),
                func.sum(case((CardRecord.status == CardStatus.SUCCESS, 1), else_=0)).label('success_count'),
                func.sum(case((CardRecord.status == CardStatus.FAILED, 1), else_=0)).label('failed_count')
            ).filter(
                CardRecord.walmart_ck_id.in_(ck_ids),
                CardRecord.merchant_id == group.merchant_id,
                CardRecord.created_at >= query_start,
                CardRecord.created_at <= query_end
            )

            if group.department_id:
                department_ids = self._get_department_and_children_ids(group.department_id)
                usage_query = usage_query.filter(CardRecord.department_id.in_(department_ids))

            usage_results = usage_query.group_by(CardRecord.walmart_ck_id).all()

            # 汇总统计
            total_usage = sum(r.total_count for r in usage_results)
            success_usage = sum(r.success_count for r in usage_results)
            failed_usage = sum(r.failed_count for r in usage_results)
            success_rate = (success_usage / total_usage * 100) if total_usage > 0 else 0.0

            # 活跃CK统计
            active_ck_count = len([ck for ck in cks if ck.active])
            used_ck_count = len(usage_results)

            # 获取使用量最高的CK
            top_cks = self._get_top_used_cks(usage_results, cks)

            result = {
                "period": f"{start_date} 至 {end_date}",
                "total_usage": total_usage,
                "success_usage": success_usage,
                "failed_usage": failed_usage,
                "success_rate": round(success_rate, 2),
                "active_ck_count": active_ck_count,
                "used_ck_count": used_ck_count,
                "top_cks": top_cks
            }

            return result

        except Exception as e:
            logger.error(f"获取CK使用统计失败: {e}")
            return {
                "period": f"{start_date} 至 {end_date}",
                "total_usage": 0,
                "success_usage": 0,
                "failed_usage": 0,
                "success_rate": 0.0,
                "active_ck_count": 0,
                "used_ck_count": 0,
                "top_cks": []
            }

    def _get_top_used_cks(self, usage_results, cks: list, limit: int = 5) -> list:
        """获取使用量最高的CK列表"""
        try:
            # 创建CK映射
            ck_map = {ck.id: ck for ck in cks}

            # 按使用量排序
            sorted_results = sorted(usage_results, key=lambda x: x.total_count, reverse=True)

            top_cks = []
            for result in sorted_results[:limit]:
                ck = ck_map.get(result.walmart_ck_id)
                if ck:
                    success_rate = (result.success_count / result.total_count * 100) if result.total_count > 0 else 0.0

                    top_cks.append({
                        "id": ck.id,
                        "masked_sign": f"CK{ck.id}***{ck.sign[-6:] if len(ck.sign) > 6 else '***'}",
                        "description": ck.description or "无描述",
                        "total_usage": result.total_count,
                        "success_usage": result.success_count,
                        "failed_usage": result.failed_count,
                        "success_rate": round(success_rate, 2)
                    })

            return top_cks

        except Exception as e:
            logger.error(f"获取热门CK列表失败: {e}")
            return []

    def _format_ck_statistics_message(self, group: TelegramGroup, stats: Dict[str, Any]) -> str:
        """格式化CK统计消息"""
        try:
            merchant_name = group.merchant.name if group.merchant else "未知商户"
            department_name = group.department.name if group.department else "全部门"

            bind_stats = stats.get("bind_stats", {})

            message = f"""🔧 **CK统计概览**

🏢 **商户**：{merchant_name}
🏬 **部门**：{department_name}

📊 **CK状态统计**：
• 总CK数量：{stats.get('total_count', 0):,} 个
• 启用CK：{stats.get('active_count', 0):,} 个
• 禁用CK：{stats.get('inactive_count', 0):,} 个
• 可用CK：{stats.get('available_count', 0):,} 个
• 已达限制：{stats.get('expired_count', 0):,} 个

⚡ **使用效率**：
• 总绑卡次数：{bind_stats.get('total_bind_count', 0):,} 次
• 成功绑卡：{bind_stats.get('success_bind_count', 0):,} 次
• 失败绑卡：{bind_stats.get('failed_bind_count', 0):,} 次
• 成功率：{bind_stats.get('success_rate', 0):.2f}%"""

            # 添加CK详情
            ck_details = stats.get("ck_details", [])
            if ck_details:
                message += "\n\n🔍 **CK详情**（前10个）："
                for ck in ck_details:
                    message += f"\n• {ck['masked_sign']} - {ck['status']}"
                    message += f" ({ck['bind_count']}/{ck['total_limit'] or '∞'})"

            message += f"\n\n🕐 **更新时间**：{local_now().strftime('%Y-%m-%d %H:%M:%S')}"
            message += f"\n\n💡 **提示**：输入 `CK今日` 查看今日使用统计"

            return message

        except Exception as e:
            logger.error(f"格式化CK统计消息失败: {e}")
            return "❌ CK统计数据格式化失败，请稍后再试"

    def _format_ck_usage_message(self, group: TelegramGroup, stats: Dict[str, Any], period_name: str) -> str:
        """格式化CK使用统计消息"""
        try:
            merchant_name = group.merchant.name if group.merchant else "未知商户"
            department_name = group.department.name if group.department else "全部门"

            message = f"""📈 **CK使用统计 - {period_name}**

🏢 **商户**：{merchant_name}
🏬 **部门**：{department_name}
📅 **时间段**：{stats.get('period', '未知')}

📊 **使用统计**：
• 总使用次数：{stats.get('total_usage', 0):,} 次
• 成功次数：{stats.get('success_usage', 0):,} 次
• 失败次数：{stats.get('failed_usage', 0):,} 次
• 成功率：{stats.get('success_rate', 0):.2f}%

🔧 **CK活跃度**：
• 活跃CK数：{stats.get('active_ck_count', 0):,} 个
• 已使用CK：{stats.get('used_ck_count', 0):,} 个
• 使用率：{(stats.get('used_ck_count', 0) / max(stats.get('active_ck_count', 1), 1) * 100):.1f}%"""

            # 添加热门CK
            top_cks = stats.get("top_cks", [])
            if top_cks:
                message += "\n\n🏆 **使用量排行**："
                for i, ck in enumerate(top_cks, 1):
                    message += f"\n{i}. {ck['masked_sign']}"
                    message += f" - {ck['total_usage']}次 ({ck['success_rate']:.1f}%成功率)"

            message += f"\n\n🕐 **更新时间**：{local_now().strftime('%Y-%m-%d %H:%M:%S')}"

            return message

        except Exception as e:
            logger.error(f"格式化CK使用消息失败: {e}")
            return f"❌ {period_name}CK使用统计格式化失败，请稍后再试"
