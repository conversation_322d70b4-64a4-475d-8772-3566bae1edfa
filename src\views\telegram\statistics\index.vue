<template>
  <div class="telegram-statistics">
    <!-- 时间筛选 -->
    <el-card class="filter-card">
      <el-form :model="filterForm" inline>
        <el-form-item label="时间范围">
          <el-date-picker v-model="filterForm.dateRange" type="datetimerange" range-separator="至"
            start-placeholder="开始时间" end-placeholder="结束时间" format="YYYY-MM-DD HH:mm:ss"
            value-format="YYYY-MM-DD HH:mm:ss" />
        </el-form-item>

        <el-form-item label="群组">
          <el-select v-model="filterForm.group_id" placeholder="请选择群组" clearable filterable>
            <el-option v-for="group in groupOptions" :key="group.id" :label="group.chat_title" :value="group.id" />
          </el-select>
        </el-form-item>

        <el-form-item label="商户">
          <el-select v-model="filterForm.merchant_id" placeholder="请选择商户" clearable filterable>
            <el-option v-for="merchant in merchantOptions" :key="merchant.id" :label="merchant.name"
              :value="merchant.id" />
          </el-select>
        </el-form-item>

        <el-form-item>
          <el-button type="primary" @click="handleFilter">
            <el-icon>
              <Search />
            </el-icon>
            查询
          </el-button>
          <el-button @click="handleReset">
            <el-icon>
              <Refresh />
            </el-icon>
            重置
          </el-button>
          <el-button type="success" @click="handleExport">
            <el-icon>
              <Download />
            </el-icon>
            导出
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 统计概览 -->
    <el-row :gutter="20" class="overview-cards">
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon">
              <el-icon :size="32" color="#409EFF">
                <ChatDotRound />
              </el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-value">{{ overviewStats.total_commands || 0 }}</div>
              <div class="stat-label">总命令数</div>
            </div>
          </div>
        </el-card>
      </el-col>

      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon">
              <el-icon :size="32" color="#67C23A">
                <User />
              </el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-value">{{ overviewStats.active_users || 0 }}</div>
              <div class="stat-label">活跃用户</div>
            </div>
          </div>
        </el-card>
      </el-col>

      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon">
              <el-icon :size="32" color="#E6A23C">
                <ChatLineRound />
              </el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-value">{{ overviewStats.active_groups || 0 }} / {{ overviewStats.total_groups || 0 }}
              </div>
              <div class="stat-label">活跃群组/总群组</div>
            </div>
          </div>
        </el-card>
      </el-col>

      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon">
              <el-icon :size="32" color="#F56C6C">
                <TrendCharts />
              </el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-value">{{ (overviewStats.success_rate || 0).toFixed(1) }}%</div>
              <div class="stat-label">成功率</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 图表区域 -->
    <el-row :gutter="20" class="charts-section">
      <!-- 命令趋势图 -->
      <el-col :span="12">
        <el-card>
          <template #header>
            <span>命令使用趋势</span>
          </template>
          <div ref="commandTrendChart" class="chart-container"></div>
        </el-card>
      </el-col>

      <!-- 命令类型分布 -->
      <el-col :span="12">
        <el-card>
          <template #header>
            <span>命令类型分布</span>
          </template>
          <div ref="commandTypeChart" class="chart-container"></div>
        </el-card>
      </el-col>
    </el-row>

    <el-row :gutter="20" class="charts-section">
      <!-- 群组活跃度 -->
      <el-col :span="12">
        <el-card>
          <template #header>
            <span>群组活跃度排行</span>
          </template>
          <div ref="groupActivityChart" class="chart-container"></div>
        </el-card>
      </el-col>

      <!-- 用户活跃度 -->
      <el-col :span="12">
        <el-card>
          <template #header>
            <span>用户活跃度分布</span>
          </template>
          <div ref="userActivityChart" class="chart-container"></div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 详细数据表格 -->
    <el-card class="table-section">
      <template #header>
        <div class="card-header">
          <span>详细统计数据</span>
          <el-radio-group v-model="tableType" @change="handleTableTypeChange">
            <el-radio-button value="commands">命令统计</el-radio-button>
            <el-radio-button value="groups">群组统计</el-radio-button>
            <el-radio-button value="users">用户统计</el-radio-button>
          </el-radio-group>
        </div>
      </template>

      <!-- 命令统计表格 -->
      <el-table v-if="tableType === 'commands'" :data="commandStats" v-loading="statsLoading" stripe border
        :empty-text="commandStats.length === 0 ? '暂无命令统计数据' : ''">
        <el-table-column prop="command" label="命令" width="120" />
        <el-table-column prop="total_count" label="总次数" width="100" />
        <el-table-column prop="success_count" label="成功次数" width="100" />
        <el-table-column prop="failed_count" label="失败次数" width="100" />
        <el-table-column prop="success_rate" label="成功率" width="100">
          <template #default="{ row }">
            {{ getSuccessRateDisplay(row.success_rate) }}
          </template>
        </el-table-column>
        <el-table-column prop="avg_response_time" label="平均响应时间" width="120">
          <template #default="{ row }">
            {{ row.avg_response_time }}ms
          </template>
        </el-table-column>
        <el-table-column prop="last_used_time" label="最后使用时间" min-width="160">
          <template #default="{ row }">
            {{ formatDateTime(row.last_used_time) }}
          </template>
        </el-table-column>
      </el-table>

      <!-- 群组统计表格 -->
      <el-table v-if="tableType === 'groups'" :data="groupStats" v-loading="statsLoading" stripe border
        :empty-text="groupStats.length === 0 ? '暂无群组统计数据' : ''">
        <el-table-column label="群组名称" min-width="200">
          <template #default="{ row }">
            {{ row.chat_title || row.group_name || '未知群组' }}
          </template>
        </el-table-column>
        <el-table-column label="总命令数" width="100">
          <template #default="{ row }">
            {{ row.activity_count || row.total_commands || 0 }}
          </template>
        </el-table-column>
        <el-table-column label="活跃用户" width="100">
          <template #default="{ row }">
            {{ row.user_count || row.active_users || 0 }}
          </template>
        </el-table-column>
        <el-table-column label="成功率" width="100">
          <template #default="{ row }">
            {{ getSuccessRateDisplay(row.success_rate) }}
          </template>
        </el-table-column>
        <el-table-column label="最后活跃" width="160">
          <template #default="{ row }">
            {{ formatDateTime(row.last_active_time) }}
          </template>
        </el-table-column>
        <el-table-column label="所属商户" width="150">
          <template #default="{ row }">
            {{ row.merchant_name || '未知商户' }}
          </template>
        </el-table-column>
      </el-table>

      <!-- 用户统计表格 -->
      <el-table v-if="tableType === 'users'" :data="userStats" v-loading="statsLoading" stripe border
        :empty-text="userStats.length === 0 ? '暂无用户统计数据' : ''">
        <el-table-column label="用户名" width="150">
          <template #default="{ row }">
            {{ getUserDisplayName(row) }}
          </template>
        </el-table-column>
        <el-table-column label="总命令数" width="100">
          <template #default="{ row }">
            {{ row.activity_count || row.total_commands || 0 }}
          </template>
        </el-table-column>
        <el-table-column label="成功命令" width="100">
          <template #default="{ row }">
            {{ row.success_commands || 0 }}
          </template>
        </el-table-column>
        <el-table-column label="失败命令" width="100">
          <template #default="{ row }">
            {{ row.failed_commands || 0 }}
          </template>
        </el-table-column>
        <el-table-column label="成功率" width="100">
          <template #default="{ row }">
            {{ getSuccessRateDisplay(row.success_rate) }}
          </template>
        </el-table-column>
        <el-table-column label="最后活跃" width="160">
          <template #default="{ row }">
            {{ formatDateTime(row.last_active_time) }}
          </template>
        </el-table-column>
        <el-table-column label="常用命令" width="120">
          <template #default="{ row }">
            {{ row.favorite_command || row.common_commands || '-' }}
          </template>
        </el-table-column>
        <el-table-column label="所属商户" width="150">
          <template #default="{ row }">
            {{ row.merchant_name || '未知商户' }}
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-wrapper">
        <el-pagination v-model:current-page="pagination.page" v-model:page-size="pagination.pageSize"
          :total="pagination.total" :page-sizes="[10, 20, 50, 100]" layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange" @current-change="handleCurrentChange" />
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import { Search, Refresh, Download, ChatDotRound, User, ChatLineRound, TrendCharts } from '@element-plus/icons-vue'
import * as echarts from 'echarts'
import { telegramApi } from '@/api'

// 图表引用
const commandTrendChart = ref()
const commandTypeChart = ref()
const groupActivityChart = ref()
const userActivityChart = ref()

// 数据状态
const statsLoading = ref(false)
const overviewStats = ref({})
const commandStats = ref([])
const groupStats = ref([])
const userStats = ref([])
const groupOptions = ref([])
const merchantOptions = ref([])

// 筛选表单
const filterForm = reactive({
  dateRange: [],
  group_id: '',
  merchant_id: ''
})

// 表格类型
const tableType = ref('commands')

// 分页
const pagination = reactive({
  page: 1,
  pageSize: 20,
  total: 0
})

// 格式化日期时间
const formatDateTime = (dateTime) => {
  if (!dateTime) return '-'
  return new Date(dateTime).toLocaleString('zh-CN')
}

// 获取用户显示名称
const getUserDisplayName = (user) => {
  if (user.user_name) return user.user_name
  if (user.telegram_username) return `@${user.telegram_username}`
  if (user.telegram_first_name || user.telegram_last_name) {
    return `${user.telegram_first_name || ''} ${user.telegram_last_name || ''}`.trim()
  }
  return `用户${user.telegram_user_id || user.id || '未知'}`
}

// 获取成功率显示
const getSuccessRateDisplay = (rate) => {
  if (rate === null || rate === undefined || isNaN(rate)) {
    return '0.0%'
  }
  // 确保成功率在0-100范围内
  const validRate = Math.max(0, Math.min(100, Number(rate)))
  return `${validRate.toFixed(1)}%`
}

// 筛选
const handleFilter = () => {
  pagination.page = 1
  loadStatistics()
}

// 重置
const handleReset = () => {
  Object.assign(filterForm, {
    dateRange: [],
    group_id: '',
    merchant_id: ''
  })
  pagination.page = 1
  loadStatistics()
}

// 导出
const handleExport = () => {
  ElMessage.info('导出功能开发中...')
}

// 表格类型变化
const handleTableTypeChange = () => {
  pagination.page = 1
  loadTableData()
}

// 分页变化
const handleSizeChange = (size) => {
  pagination.pageSize = size
  pagination.page = 1
  loadTableData()
}

const handleCurrentChange = (page) => {
  pagination.page = page
  loadTableData()
}

// 加载统计数据
const loadStatistics = async () => {
  try {
    statsLoading.value = true

    const params = {
      ...filterForm
    }

    if (filterForm.dateRange && filterForm.dateRange.length === 2) {
      params.start_time = filterForm.dateRange[0]
      params.end_time = filterForm.dateRange[1]
      delete params.dateRange
    }

    // 加载概览统计
    const overviewResponse = await telegramApi.getBotStatistics(params)
    overviewStats.value = overviewResponse.data || overviewResponse

    // 加载表格数据
    await loadTableData()

    // 加载图表数据
    await loadChartData()

  } catch (error) {
    console.error('加载统计数据失败:', error)
    ElMessage.error('加载统计数据失败：' + (error.message || '网络错误'))

    // 重置数据为默认值
    overviewStats.value = {
      total_count: 0,
      success_count: 0,
      failed_count: 0,
      success_rate: 0,
      total_groups: 0,
      active_groups: 0,
      total_commands: 0,
      successful_commands: 0
    }
  } finally {
    statsLoading.value = false
  }
}

// 加载表格数据
const loadTableData = async () => {
  try {
    const params = {
      page: pagination.page,
      page_size: pagination.pageSize,
      ...filterForm
    }

    if (filterForm.dateRange && filterForm.dateRange.length === 2) {
      params.start_time = filterForm.dateRange[0]
      params.end_time = filterForm.dateRange[1]
      delete params.dateRange
    }

    let response
    switch (tableType.value) {
      case 'commands':
        response = await telegramApi.getCommandStatistics(params)
        commandStats.value = response.data?.items || response.items || []
        break
      case 'groups':
        response = await telegramApi.getGroupStatistics(params)
        groupStats.value = response.data?.items || response.items || []
        break
      case 'users':
        response = await telegramApi.getUserStatistics(params)
        userStats.value = response.data?.items || response.items || []
        break
    }

    pagination.total = response.data?.total || response.total || 0

  } catch (error) {
    console.error('加载表格数据失败:', error)
    ElMessage.error('加载表格数据失败：' + (error.message || '网络错误'))

    // 清空数据并显示友好提示
    switch (tableType.value) {
      case 'commands':
        commandStats.value = []
        break
      case 'groups':
        groupStats.value = []
        break
      case 'users':
        userStats.value = []
        break
    }
    pagination.total = 0
  }
}

// 加载图表数据
const loadChartData = async () => {
  try {
    // 构建时间参数
    const timeParams = {}
    if (filterForm.dateRange && filterForm.dateRange.length === 2) {
      timeParams.start_time = filterForm.dateRange[0]
      timeParams.end_time = filterForm.dateRange[1]
    }

    // 添加商户过滤
    if (filterForm.merchant_id) {
      timeParams.merchant_id = filterForm.merchant_id
    }

    await nextTick()

    // 等待DOM完全渲染
    await new Promise(resolve => setTimeout(resolve, 100))

    // 初始化图表
    console.log('开始初始化图表，timeParams:', timeParams)

    await initCommandTrendChart(timeParams)
    console.log('命令趋势图初始化完成')

    await initCommandTypeChart(timeParams)
    console.log('命令类型图初始化完成')

    await initGroupActivityChart(timeParams)
    console.log('群组活跃度图初始化完成')

    await initUserActivityChart(timeParams)
    console.log('用户活跃度图初始化完成')

  } catch (error) {
    console.error('加载图表数据失败:', error)
    ElMessage.warning('图表数据加载失败，显示默认数据')

    // 使用默认参数初始化图表，确保图表能正常显示
    await nextTick()

    // 等待DOM完全渲染
    await new Promise(resolve => setTimeout(resolve, 100))

    await initCommandTrendChart({})
    await initCommandTypeChart({})
    await initGroupActivityChart({})
    await initUserActivityChart({})
  }
}

// 初始化命令趋势图
const initCommandTrendChart = async (params = {}) => {
  const chart = echarts.init(commandTrendChart.value)

  try {
    // 调用API获取真实数据
    const response = await telegramApi.getChartData('command_trend', params)
    // telegramApi.getChartData 现在直接返回图表数据
    const chartData = response

    const option = {
      title: {
        text: '命令使用趋势',
        left: 'center'
      },
      tooltip: {
        trigger: 'axis'
      },
      xAxis: {
        type: 'category',
        data: chartData.categories || ['00:00', '04:00', '08:00', '12:00', '16:00', '20:00']
      },
      yAxis: {
        type: 'value'
      },
      series: [{
        data: chartData.data || [0, 0, 0, 0, 0, 0],
        type: 'line',
        smooth: true
      }]
    }

    chart.setOption(option)

    // 如果有提示信息，显示在图表上
    if (chartData.message) {
      console.log('命令趋势图:', chartData.message)
    }
  } catch (error) {
    console.error('获取命令趋势数据失败:', error)
    // 使用默认数据
    const option = {
      title: {
        text: '命令使用趋势',
        left: 'center'
      },
      tooltip: {
        trigger: 'axis'
      },
      xAxis: {
        type: 'category',
        data: ['00:00', '04:00', '08:00', '12:00', '16:00', '20:00']
      },
      yAxis: {
        type: 'value'
      },
      series: [{
        data: [0, 0, 0, 0, 0, 0],
        type: 'line',
        smooth: true
      }]
    }
    chart.setOption(option)
  }
}

// 初始化命令类型分布图
const initCommandTypeChart = async (params = {}) => {
  const chart = echarts.init(commandTypeChart.value)

  try {
    // 调用API获取真实数据
    const response = await telegramApi.getChartData('command_type', params)
    // telegramApi.getChartData 现在直接返回图表数据
    const chartData = response

    const option = {
      title: {
        text: '命令类型分布',
        left: 'center'
      },
      tooltip: {
        trigger: 'item'
      },
      series: [{
        type: 'pie',
        radius: '50%',
        data: chartData.data || [
          { value: 0, name: '暂无数据' }
        ]
      }]
    }

    chart.setOption(option)

    // 如果有提示信息，显示在图表上
    if (chartData.message) {
      console.log('命令类型图:', chartData.message)
    }
  } catch (error) {
    console.error('获取命令类型数据失败:', error)
    // 使用默认数据
    const option = {
      title: {
        text: '命令类型分布',
        left: 'center'
      },
      tooltip: {
        trigger: 'item'
      },
      series: [{
        type: 'pie',
        radius: '50%',
        data: [
          { value: 0, name: '暂无数据' }
        ]
      }]
    }
    chart.setOption(option)
  }
}

// 初始化群组活跃度图
const initGroupActivityChart = async (params = {}) => {
  console.log('=== 开始初始化群组活跃度图 ===')
  console.log('传入参数:', params)

  // 检查DOM元素是否存在
  if (!groupActivityChart.value) {
    console.error('群组活跃度图表DOM元素不存在')
    return
  }
  console.log('DOM元素检查通过:', groupActivityChart.value)

  const chart = echarts.init(groupActivityChart.value)
  console.log('ECharts实例创建成功:', chart)

  try {
    // 调用API获取真实数据
    console.log('开始调用API...')
    console.log('API参数:', params)

    const response = await telegramApi.getChartData('group_activity', params)

    console.log('响应类型:', typeof response)
    console.log('响应是否为数组:', Array.isArray(response))
    console.log('响应结构:', Object.keys(response || {}))
    console.log('响应完整内容:', JSON.stringify(response, null, 2))

    // 数据解析逻辑
    // telegramApi.getChartData 现在直接返回图表数据 {categories: [...], data: [...]}
    const chartData = response

    console.log('群组活跃度解析后数据:', chartData)
    console.log('chartData类型:', typeof chartData)
    console.log('chartData是否为数组:', Array.isArray(chartData))
    console.log('chartData结构:', Object.keys(chartData || {}))
    console.log('categories:', chartData?.categories)
    console.log('data:', chartData?.data)

    // 验证数据有效性
    if (!chartData) {
      console.error('chartData为空或undefined')
      throw new Error('图表数据为空')
    }

    // 检查是否为数组（错误的数据格式）
    if (Array.isArray(chartData)) {
      console.error('chartData是数组，但期望是对象')
      console.log('数组内容:', chartData)
      throw new Error('图表数据格式错误：接收到数组而不是对象')
    }

    if (!chartData.categories || !chartData.data) {
      console.error('缺少必需的categories或data字段')
      console.log('可用字段:', Object.keys(chartData))
      console.log('chartData完整内容:', JSON.stringify(chartData, null, 2))
      throw new Error('图表数据格式不正确')
    }

    // 验证数据类型
    if (!Array.isArray(chartData.categories) || !Array.isArray(chartData.data)) {
      console.error('categories或data不是数组')
      console.log('categories类型:', typeof chartData.categories, 'categories值:', chartData.categories)
      console.log('data类型:', typeof chartData.data, 'data值:', chartData.data)
      throw new Error('categories和data必须是数组')
    }

    const option = {
      title: {
        text: '群组活跃度排行',
        left: 'center'
      },
      tooltip: {
        trigger: 'axis'
      },
      xAxis: {
        type: 'category',
        data: chartData.categories || ['暂无数据']
      },
      yAxis: {
        type: 'value'
      },
      series: [{
        data: chartData.data || [0],
        type: 'bar'
      }]
    }

    console.log('群组活跃度图表配置:', option)
    chart.setOption(option)

    // 如果有提示信息，显示在图表上
    if (chartData.message) {
      console.log('群组活跃度图:', chartData.message)
    }
  } catch (error) {
    console.error('获取群组活跃度数据失败:', error)
    console.error('错误详情:', error.message, error.stack)

    // 使用默认数据
    const option = {
      title: {
        text: '群组活跃度排行',
        left: 'center'
      },
      tooltip: {
        trigger: 'axis'
      },
      xAxis: {
        type: 'category',
        data: ['暂无数据']
      },
      yAxis: {
        type: 'value'
      },
      series: [{
        data: [0],
        type: 'bar'
      }]
    }

    console.log('设置默认图表配置:', option)
    chart.setOption(option)
    console.log('默认图表配置设置完成')
  }
}

// 初始化用户活跃度图
const initUserActivityChart = async (params = {}) => {
  const chart = echarts.init(userActivityChart.value)

  try {
    // 调用API获取真实数据
    const response = await telegramApi.getChartData('user_activity', params)


    // telegramApi.getChartData 现在直接返回图表数据
    const chartData = response

    const option = {
      title: {
        text: '用户活跃度分布',
        left: 'center'
      },
      tooltip: {
        trigger: 'item'
      },
      series: [{
        type: 'pie',
        radius: ['40%', '70%'],
        data: chartData.data || [
          { value: 0, name: '高活跃' },
          { value: 0, name: '中活跃' },
          { value: 0, name: '低活跃' },
          { value: 0, name: '不活跃' }
        ]
      }]
    }

    console.log('用户活跃度图表配置:', option)
    chart.setOption(option)

    // 如果有提示信息，显示在图表上
    if (chartData.message) {
      console.log('用户活跃度图:', chartData.message)
    }
  } catch (error) {
    console.error('获取用户活跃度数据失败:', error)
    // 使用默认数据
    const option = {
      title: {
        text: '用户活跃度分布',
        left: 'center'
      },
      tooltip: {
        trigger: 'item'
      },
      series: [{
        type: 'pie',
        radius: ['40%', '70%'],
        data: [
          { value: 0, name: '高活跃' },
          { value: 0, name: '中活跃' },
          { value: 0, name: '低活跃' },
          { value: 0, name: '不活跃' }
        ]
      }]
    }
    chart.setOption(option)
  }
}

// 初始化
onMounted(async () => {
  try {
    // 设置默认时间范围（最近7天）
    const endTime = new Date()
    const startTime = new Date(endTime.getTime() - 7 * 24 * 60 * 60 * 1000)
    filterForm.dateRange = [
      startTime.toISOString().slice(0, 19).replace('T', ' '),
      endTime.toISOString().slice(0, 19).replace('T', ' ')
    ]

    await loadStatistics()
  } catch (error) {
    console.error('初始化失败:', error)
  }
})
</script>

<style scoped>
.telegram-statistics {
  padding: 20px;
}

.filter-card {
  margin-bottom: 20px;
}

.overview-cards {
  margin-bottom: 20px;
}

.stat-card {
  height: 120px;
}

.stat-content {
  display: flex;
  align-items: center;
  height: 100%;
}

.stat-icon {
  margin-right: 16px;
}

.stat-info {
  flex: 1;
}

.stat-value {
  font-size: 24px;
  font-weight: bold;
  color: #333;
  margin-bottom: 8px;
}

.stat-label {
  font-size: 14px;
  color: #666;
}

.charts-section {
  margin-bottom: 20px;
}

.chart-container {
  height: 300px;
}

.table-section {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.pagination-wrapper {
  margin-top: 20px;
  text-align: right;
}
</style>
