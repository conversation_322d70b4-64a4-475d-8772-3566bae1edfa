<template>
  <div class="department-management">
    <div class="page-header">
      <h2>部门管理</h2>
      <p>基于新的OA架构：商户 → 部门 → 子部门 → 用户</p>
    </div>

    <!-- 操作栏 -->
    <div class="operation-bar">
      <el-button type="primary" @click="createDepartment">
        创建部门
      </el-button>

      <el-button @click="toggleTreeView" :type="isTreeView ? 'success' : 'default'">
        {{ isTreeView ? '列表视图' : '树形视图' }}
      </el-button>

      <el-button @click="refreshData">
        刷新
      </el-button>

      <el-button type="warning" @click="openBatchBindingControls" :disabled="selectedDepartments.length === 0">
        批量绑卡设置 ({{ selectedDepartments.length }})
      </el-button>

      <el-button type="info" @click="showWeightStats">
        权重统计
      </el-button>

      <el-button type="warning" @click="showDebugInfo">
        调试信息
      </el-button>
    </div>

    <!-- 搜索栏 -->
    <div class="search-bar">
      <el-form :model="searchForm" inline>
        <el-form-item label="商户">
          <el-select v-model="searchForm.merchant_id" placeholder="选择商户" clearable style="width: 200px"
            v-if="userStore.userInfo?.role === 'super_admin'">
            <el-option v-for="merchant in merchants" :key="merchant.id" :label="merchant.name" :value="merchant.id" />
          </el-select>
        </el-form-item>

        <el-form-item label="部门名称">
          <el-input v-model="searchForm.name" placeholder="输入部门名称" clearable style="width: 200px" />
        </el-form-item>

        <el-form-item label="状态">
          <el-select v-model="searchForm.status" placeholder="选择状态" clearable style="width: 120px">
            <el-option label="启用" :value="true" />
            <el-option label="禁用" :value="false" />
          </el-select>
        </el-form-item>

        <el-form-item>
          <el-button type="primary" @click="searchDepartments">搜索</el-button>
          <el-button @click="resetSearch">重置</el-button>
        </el-form-item>
      </el-form>
    </div>



    <!-- 部门列表/树形视图 -->
    <div class="department-content">
      <!-- 树形视图 -->
      <el-tree v-if="isTreeView" :data="departmentTree" :props="treeProps" node-key="id" default-expand-all
        :expand-on-click-node="false" class="department-tree">
        <template #default="{ node, data }">
          <div class="tree-node">
            <span class="node-label">
              <el-icon>
                <OfficeBuilding />
              </el-icon>
              {{ data.name }}
              <el-tag size="small" :type="data.status ? 'success' : 'danger'">
                {{ data.status ? '启用' : '禁用' }}
              </el-tag>
            </span>
            <div class="node-actions">
              <el-button size="small" @click="viewDepartment(data)">
                查看
              </el-button>
              <el-button size="small" type="primary" @click="editDepartment(data)">
                编辑
              </el-button>
              <el-button size="small" type="success" @click="createSubDepartment(data)">
                添加子部门
              </el-button>
              <el-button size="small" type="danger" @click="deleteDepartment(data)">
                删除
              </el-button>
            </div>
          </div>
        </template>
      </el-tree>

      <!-- 列表视图 -->
      <el-table v-else :data="departments" v-loading="loading" stripe class="department-table"
        @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" />
        <el-table-column prop="name" label="部门名称" min-width="150" />
        <el-table-column prop="code" label="部门代码" width="120" />
        <el-table-column label="所属商户" width="150">
          <template #default="{ row }">
            {{ row.merchantName || getMerchantName(row.merchant_id) }}
          </template>
        </el-table-column>
        <el-table-column label="上级部门" width="150">
          <template #default="{ row }">
            {{ getParentName(row.parent_id) }}
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" width="80">
          <template #default="{ row }">
            <el-tag :type="row.status ? 'success' : 'danger'">
              {{ row.status ? '启用' : '禁用' }}
            </el-tag>
          </template>
        </el-table-column>

        <!-- 绑卡控制列 -->
        <el-table-column label="进单开关" width="130" align="center">
          <template #default="{ row }">
            <el-switch v-model="row.enable_binding" @change="handleBindingSwitchChange(row)"
              :loading="row._switchLoading" active-text="启用" inactive-text="禁用" size="small" />
          </template>
        </el-table-column>

        <el-table-column label="进单权重" width="160" align="center">
          <template #default="{ row }">
            <el-input-number v-model="row.binding_weight" :min="0" :max="10000" :step="10" size="small"
              style="width: 100px" @change="handleWeightChange(row)" :disabled="!row.enable_binding" />
          </template>
        </el-table-column>

        <el-table-column label="绑卡状态" width="100" align="center">
          <template #default="{ row }">
            <el-tag :type="getBindingStatusType(row)" size="small">
              {{ getBindingStatusText(row) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="level" label="层级" width="80" />
        <el-table-column prop="manager_name" label="负责人" width="120" />
        <el-table-column label="创建时间" width="180">
          <template #default="{ row }">
            {{ formatDate(row.created_at) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="400" fixed="right">
          <template #default="{ row }">
            <el-button size="small" @click="viewDepartment(row)">
              查看
            </el-button>
            <el-button size="small" type="primary" @click="editDepartment(row)">
              编辑
            </el-button>
            <el-button size="small" type="warning" @click="openBindingControls(row)">
              绑卡设置
            </el-button>
            <el-button size="small" type="success" @click="createSubDepartment(row)">
              添加子部门
            </el-button>
            <el-button size="small" type="danger" @click="deleteDepartment(row)">
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 快速绑卡设置工具栏 -->
    <DepartmentBindingQuickActions v-if="selectedDepartments.length > 0" :selected-departments="selectedDepartments"
      @batch-update="handleQuickBatchUpdate" />

    <!-- 分页 -->
    <div class="pagination" v-if="!isTreeView">
      <el-pagination v-model:current-page="pagination.page" v-model:page-size="pagination.pageSize"
        :page-sizes="[10, 20, 50, 100]" :total="pagination.total" layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange" @current-change="handleCurrentChange" />
    </div>

    <!-- 创建/编辑部门对话框 -->
    <DepartmentDialog v-model:visible="showCreateDialog" :department="currentDepartment"
      :parent-department="parentDepartment" :current-merchant-id="searchForm.merchant_id"
      @success="handleDialogSuccess" />

    <!-- 部门详情对话框 -->
    <DepartmentDetailDialog v-model:visible="showDetailDialog" :department="currentDepartment" />

    <!-- 绑卡控制对话框 -->
    <DepartmentBindingControls v-if="bindingControlsVisible" :department-id="selectedDepartmentId"
      v-model:visible="bindingControlsVisible" @updated="handleBindingControlsUpdated" />

    <!-- 批量绑卡控制对话框 -->
    <DepartmentBatchBindingControls v-model:visible="batchBindingControlsVisible" :departments="selectedDepartments"
      @updated="handleBatchBindingControlsUpdated" />

    <!-- 权重统计对话框 -->
    <el-dialog title="权重分配统计" v-model="weightStatsVisible" width="800px">
      <div v-if="weightStatsData" class="weight-stats-content">
        <div class="stats-overview">
          <div class="stat-item">
            <span class="number">{{ weightStatsData.total_departments }}</span>
            <span class="label">启用部门</span>
          </div>
          <div class="stat-item">
            <span class="number">{{ weightStatsData.total_weight }}</span>
            <span class="label">总权重</span>
          </div>
        </div>

        <el-table :data="weightStatsData.departments" border size="small">
          <el-table-column prop="name" label="部门名称" />
          <el-table-column prop="binding_weight" label="权重" width="80" align="center" />
          <el-table-column label="权重占比" width="100" align="center">
            <template #default="{ row }">
              {{ row.weight_percentage }}%
            </template>
          </el-table-column>
          <el-table-column prop="available_ck_count" label="可用CK" width="80" align="center" />
          <el-table-column prop="total_ck_count" label="总CK" width="80" align="center" />
        </el-table>
      </div>

      <template #footer>
        <el-button @click="weightStatsVisible = false">关闭</el-button>
        <el-button type="primary" @click="testWeightAlgorithm">测试权重算法</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, watch, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { OfficeBuilding } from '@element-plus/icons-vue'
import { useUserStore } from '@/store/modules/user'
import { departmentApi } from '@/api/modules/department'
import { merchantApi } from '@/api/modules/merchant'
import DepartmentDialog from './components/DepartmentDialog.vue'
import DepartmentDetailDialog from './components/DepartmentDetailDialog.vue'
import DepartmentBindingControls from '@/components/department/DepartmentBindingControls.vue'
import DepartmentBatchBindingControls from '@/components/department/DepartmentBatchBindingControls.vue'
import DepartmentBindingQuickActions from '@/components/department/DepartmentBindingQuickActions.vue'
import useMerchantSwitch from '@/composables/useMerchantSwitch'

// Store
const userStore = useUserStore()

// 响应式数据
const loading = ref(false)
const isTreeView = ref(false)  // 默认使用列表视图来测试
const departments = ref([])
const departmentTree = ref([])
const merchants = ref([])
const showCreateDialog = ref(false)
const showDetailDialog = ref(false)
const currentDepartment = ref(null)
const parentDepartment = ref(null)

// 绑卡控制相关数据
const selectedDepartments = ref([])
const selectedDepartmentId = ref(null)
const bindingControlsVisible = ref(false)
const batchBindingControlsVisible = ref(false)
const weightStatsVisible = ref(false)
const weightStatsData = ref(null)

// 搜索表单
const searchForm = reactive({
  merchant_id: null,
  name: '',
  status: null
})

// 分页
const pagination = reactive({
  page: 1,
  pageSize: 20,
  total: 0
})

// 树形组件配置
const treeProps = {
  children: 'children',
  label: 'name'
}

// 计算属性
// 暂时移除权限检查，后续可以根据需要添加

// 辅助函数
const getMerchantName = (merchantId) => {
  if (!merchantId) return '未知商户'

  // 对于非超级管理员，不应该依赖merchants数组（因为他们不会调用商户列表API）
  // 这个函数主要作为备用方案，正常情况下应该使用后端返回的merchantName字段
  if (userStore.userInfo?.role !== 'super_admin') {
    return '未知商户'
  }

  const merchant = merchants.value.find(m => m.id === merchantId)
  return merchant ? merchant.name : '未知商户'
}

const getParentName = (parentId) => {
  if (!parentId) return '无'
  const parent = departments.value.find(d => d.id === parentId)
  return parent ? parent.name : '未知部门'
}

const formatDate = (dateStr) => {
  if (!dateStr) return ''
  return new Date(dateStr).toLocaleString('zh-CN')
}

// 方法
const loadDepartments = async (extraParams = {}) => {
  loading.value = true
  try {
    // 构建请求参数，将前端的status参数转换为后端期望的dept_status
    const params = {
      page: pagination.page,
      page_size: pagination.pageSize,
      is_tree: isTreeView.value,
      merchant_id: searchForm.merchant_id,
      name: searchForm.name,
      ...extraParams
    }

    // 如果有状态筛选，添加dept_status参数
    if (searchForm.status !== null && searchForm.status !== undefined) {
      params.dept_status = searchForm.status
    }

    const response = await departmentApi.getList(params)

    // 检查响应结构并正确提取数据
    let departmentData = []
    let totalCount = 0

    if (Array.isArray(response)) {
      // 如果response直接是数组
      departmentData = response
      totalCount = response.length
    } else if (response.data && Array.isArray(response.data)) {
      // 如果data是数组，直接使用
      departmentData = response.data
      totalCount = response.total || response.data.length
    } else if (response.items && Array.isArray(response.items)) {
      // 如果items是数组，使用items
      departmentData = response.items
      totalCount = response.total || response.items.length
    } else {
      console.error('无法识别的响应格式：', response)
      return // 如果无法识别格式，不更新数据
    }

    // 处理绑卡控制字段的默认值
    departmentData = departmentData.map(dept => ({
      ...dept,
      enable_binding: dept.enable_binding !== undefined ? dept.enable_binding : true,
      binding_weight: dept.binding_weight !== undefined ? dept.binding_weight : 100,
      _switchLoading: false // 添加开关加载状态
    }))

    // 只有在成功获取到数据时才更新
    if (departmentData.length > 0 || totalCount === 0) {
      if (isTreeView.value) {
        departmentTree.value = departmentData
      } else {
        departments.value = departmentData
        pagination.total = totalCount
      }
    }

  } catch (error) {
    console.error('部门API调用失败：', error)
    ElMessage.error('加载部门列表失败：' + error.message)
  } finally {
    loading.value = false
  }
}

// 使用商家切换监听器
// 创建不包含merchant_id的基础查询参数，让useMerchantSwitch自动添加商家过滤
const baseQueryParams = computed(() => ({
  page: pagination.page,
  page_size: pagination.pageSize,
  is_tree: isTreeView.value,
  name: searchForm.name,
  dept_status: searchForm.status
}))

const { fetchWithMerchantFilter } = useMerchantSwitch(
  loadDepartments,
  baseQueryParams,
  'merchant_id'
)

const loadMerchants = async () => {
  try {
    const response = await merchantApi.getList()

    // 检查响应结构
    if (response) {
      if (response.items) {
        // 商户API直接返回 {total: ..., items: [...]}
        merchants.value = response.items
      } else if (response.data && response.data.items) {
        // 如果是包装格式 {data: {total: ..., items: [...]}}
        merchants.value = response.data.items
      } else {
        merchants.value = []
      }
    } else {
      merchants.value = []
    }

    // 如果是超级管理员且有商户数据，自动选择ID为1的商户（因为部门数据都属于merchant_id=1）
    if (userStore.userInfo?.role === 'super_admin' && merchants.value.length > 0 && !searchForm.merchant_id) {
      // 查找ID为1的商户
      const targetMerchant = merchants.value.find(m => m.id === 1) || merchants.value[0]
      searchForm.merchant_id = Number(targetMerchant.id)  // 确保是数字类型
      // 自动加载部门数据
      fetchWithMerchantFilter()
    }
  } catch (error) {
    console.error('加载商户列表失败：', error)
    ElMessage.error('加载商户列表失败：' + error.message)
  }
}

const toggleTreeView = () => {
  isTreeView.value = !isTreeView.value
  fetchWithMerchantFilter()
}

const refreshData = () => {
  fetchWithMerchantFilter()
}

const searchDepartments = () => {
  pagination.page = 1
  fetchWithMerchantFilter()
}

const resetSearch = () => {
  Object.assign(searchForm, {
    merchant_id: null,
    name: '',
    status: null
  })
  searchDepartments()
}

const viewDepartment = (department) => {
  currentDepartment.value = department
  showDetailDialog.value = true
}

const createDepartment = () => {
  // 清理状态，确保是创建模式
  currentDepartment.value = null
  parentDepartment.value = null
  showCreateDialog.value = true
}

const editDepartment = (department) => {
  // 设置编辑模式
  currentDepartment.value = { ...department }
  parentDepartment.value = null
  showCreateDialog.value = true
}

const createSubDepartment = (parentDept) => {
  // 设置创建子部门模式
  currentDepartment.value = null
  parentDepartment.value = parentDept
  showCreateDialog.value = true
}

const deleteDepartment = async (department) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除部门"${department.name}"吗？此操作不可恢复。`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    await departmentApi.delete(department.id)
    ElMessage.success('删除成功')
    fetchWithMerchantFilter()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败：' + error.message)
    }
  }
}

const handleDialogSuccess = () => {
  // 关闭对话框并清理状态
  showCreateDialog.value = false
  currentDepartment.value = null
  parentDepartment.value = null
  // 刷新数据
  fetchWithMerchantFilter()
}

const handleSizeChange = (size) => {
  pagination.pageSize = size
  pagination.page = 1
  fetchWithMerchantFilter()
}

const handleCurrentChange = (page) => {
  pagination.page = page
  fetchWithMerchantFilter()
}

// ========================================
// 绑卡控制相关方法
// ========================================

// 处理表格选择变化
const handleSelectionChange = (selection) => {
  selectedDepartments.value = selection
}

// 处理进单开关变化
const handleBindingSwitchChange = async (row) => {
  try {
    row._switchLoading = true

    const updateData = {
      enable_binding: row.enable_binding,
      binding_weight: row.binding_weight
    }

    await departmentApi.updateBindingControls(row.id, updateData)
    ElMessage.success(`${row.enable_binding ? '启用' : '禁用'}进单成功`)

    // 刷新该行数据
    await refreshDepartmentRow(row)
  } catch (error) {
    // 恢复原状态
    row.enable_binding = !row.enable_binding
    ElMessage.error('更新失败：' + error.message)
  } finally {
    row._switchLoading = false
  }
}

// 处理权重变化
const handleWeightChange = async (row) => {
  try {
    const updateData = {
      enable_binding: row.enable_binding,
      binding_weight: row.binding_weight
    }

    await departmentApi.updateBindingControls(row.id, updateData)
    ElMessage.success('权重更新成功')

    // 刷新该行数据
    await refreshDepartmentRow(row)
  } catch (error) {
    ElMessage.error('更新失败：' + error.message)
    // 重新加载数据以恢复原状态
    fetchWithMerchantFilter()
  }
}

// 刷新单个部门行数据
const refreshDepartmentRow = async (row) => {
  try {
    const response = await departmentApi.getBindingStatus(row.id)
    Object.assign(row, response)
  } catch (error) {
    console.error('刷新部门数据失败:', error)
  }
}

// 获取绑卡状态类型
const getBindingStatusType = (row) => {
  if (!row.enable_binding) return 'info'
  if (row.binding_weight <= 0) return 'warning'
  return 'success'
}

// 获取绑卡状态文本
const getBindingStatusText = (row) => {
  if (!row.enable_binding) return '已禁用'
  if (row.binding_weight <= 0) return '权重为0'
  return '可绑卡'
}

// 打开单个部门绑卡控制
const openBindingControls = (row) => {
  selectedDepartmentId.value = row.id
  bindingControlsVisible.value = true
}

// 打开批量绑卡控制
const openBatchBindingControls = () => {
  if (selectedDepartments.value.length === 0) {
    ElMessage.warning('请先选择要设置的部门')
    return
  }
  batchBindingControlsVisible.value = true
}

// 显示权重统计
const showWeightStats = async () => {
  try {
    // 确定商户ID
    let merchantId = searchForm.merchant_id

    // 如果是商户管理员，使用用户所属的商户ID
    if (userStore.userInfo?.role !== 'super_admin') {
      merchantId = userStore.userInfo?.merchant_id
    }

    // 检查商户ID是否存在
    if (!merchantId) {
      console.log('权重统计调试信息:', {
        userRole: userStore.userInfo?.role,
        searchFormMerchantId: searchForm.merchant_id,
        userMerchantId: userStore.userInfo?.merchant_id,
        userInfo: userStore.userInfo
      })
      ElMessage.warning('请先选择商户或确保用户已分配商户')
      return
    }

    const response = await departmentApi.getBindingWeightStats({
      merchant_id: merchantId
    })
    weightStatsData.value = response.weight_distribution
    weightStatsVisible.value = true
  } catch (error) {
    ElMessage.error('获取权重统计失败：' + error.message)
  }
}

// 测试权重算法
const testWeightAlgorithm = async () => {
  try {
    // 确定商户ID
    let merchantId = searchForm.merchant_id

    // 如果是商户管理员，使用用户所属的商户ID
    if (userStore.userInfo?.role !== 'super_admin') {
      merchantId = userStore.userInfo?.merchant_id
    }

    // 检查商户ID是否存在
    if (!merchantId) {
      console.log('调试信息:', {
        userRole: userStore.userInfo?.role,
        searchFormMerchantId: searchForm.merchant_id,
        userMerchantId: userStore.userInfo?.merchant_id,
        userInfo: userStore.userInfo
      })
      ElMessage.warning('请先选择商户或确保用户已分配商户')
      return
    }

    const response = await departmentApi.testWeightAlgorithm({
      merchant_id: merchantId,
      test_count: 100
    })

    ElMessage.success('权重算法测试完成')
    console.log('测试结果:', response.test_result)

    // 这里可以打开详细的测试结果对话框
    ElMessageBox.alert(
      `测试完成！共进行 ${response.test_result.test_count} 次测试，涉及 ${response.test_result.total_departments} 个部门。详细结果请查看控制台。`,
      '权重算法测试结果',
      { type: 'success' }
    )
  } catch (error) {
    ElMessage.error('测试权重算法失败：' + error.message)
  }
}

// 处理绑卡控制更新
const handleBindingControlsUpdated = (data) => {
  ElMessage.success('绑卡控制设置更新成功')
  fetchWithMerchantFilter()
}

// 处理批量绑卡控制更新
const handleBatchBindingControlsUpdated = (data) => {
  ElMessage.success(`成功更新 ${data.updated_count} 个部门的绑卡控制设置`)
  selectedDepartments.value = []
  fetchWithMerchantFilter()
}

// 处理快速批量更新
const handleQuickBatchUpdate = async (updateData) => {
  try {
    const response = await departmentApi.batchUpdateBindingControls(updateData)

    if (response.success) {
      ElMessage.success(`成功更新 ${response.updated_count} 个部门的绑卡控制设置`)
      selectedDepartments.value = []
      fetchWithMerchantFilter()
    } else {
      ElMessage.error('批量更新失败：' + response.message)
    }
  } catch (error) {
    ElMessage.error('批量更新失败：' + error.message)
  }
}

// 显示调试信息
const showDebugInfo = () => {
  const debugInfo = {
    用户角色: userStore.userInfo?.role,
    用户商户ID: userStore.userInfo?.merchant_id,
    搜索表单商户ID: searchForm.merchant_id,
    商户列表: merchants.value.map(m => ({ id: m.id, name: m.name })),
    用户完整信息: userStore.userInfo
  }

  console.log('🔍 调试信息:', debugInfo)

  ElMessageBox.alert(
    `用户角色: ${debugInfo.用户角色}\n` +
    `用户商户ID: ${debugInfo.用户商户ID}\n` +
    `搜索表单商户ID: ${debugInfo.搜索表单商户ID}\n` +
    `商户数量: ${debugInfo.商户列表.length}\n\n` +
    `详细信息已输出到控制台`,
    '调试信息',
    { type: 'info' }
  )
}

// 生命周期
onMounted(() => {
  if (userStore.userInfo?.role === 'super_admin') {
    // 超级管理员先加载商户，然后自动选择第一个商户并加载部门
    loadMerchants()
  } else {
    // 商户管理员直接加载部门数据，不调用商户列表API
    // 商户名称信息由部门API返回的merchantName字段提供
    fetchWithMerchantFilter()
  }
})
</script>

<style scoped>
.department-management {
  padding: 20px;
}

.page-header {
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0 0 8px 0;
  color: #303133;
}

.page-header p {
  margin: 0;
  color: #909399;
  font-size: 14px;
}

.operation-bar {
  margin-bottom: 20px;
}

.search-bar {
  margin-bottom: 20px;
  padding: 20px;
  background: #f5f7fa;
  border-radius: 4px;
}

.department-content {
  margin-bottom: 20px;
}

.department-tree {
  border: 1px solid #dcdfe6;
  border-radius: 4px;
}

.tree-node {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  padding: 8px 0;
}

.node-label {
  display: flex;
  align-items: center;
  gap: 8px;
}

.node-actions {
  display: flex;
  gap: 8px;
}

.pagination {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}

/* 权重统计样式 */
.weight-stats-content {
  .stats-overview {
    display: flex;
    justify-content: space-around;
    margin-bottom: 20px;
    padding: 20px;
    background: #f5f7fa;
    border-radius: 8px;

    .stat-item {
      text-align: center;

      .number {
        display: block;
        font-size: 28px;
        font-weight: 600;
        color: #409EFF;
        margin-bottom: 5px;
      }

      .label {
        color: #606266;
        font-size: 14px;
      }
    }
  }
}

/* 表格中的绑卡控制样式 */
.department-table {
  .el-input-number {
    .el-input__inner {
      text-align: center;
    }
  }

  .el-switch {
    --el-switch-on-color: #13ce66;
    --el-switch-off-color: #ff4949;
  }
}
</style>
