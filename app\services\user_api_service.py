"""
用户API业务逻辑服务
专门处理用户管理相关的业务逻辑
"""

from typing import Any, Optional, List, Dict, Set
from sqlalchemy.orm import Session
from fastapi import HTTPException, status

from app import crud, models
from app.models.user import User
from app.core.security import get_password_hash
from app.core.auth import auth_service
from app.core.logging import get_logger
from app.schemas.user import UserCreate, UserUpdate, UserStatusUpdate

logger = get_logger(__name__)


class UserAPIService:
    """用户API业务逻辑服务"""
    
    def __init__(self, db: Session):
        self.db = db
    
    def get_users_with_filters(
        self,
        current_user: User,
        page: int = 1,
        page_size: int = 10,
        merchant_id: Optional[int] = None,
        role: Optional[str] = None,
        is_active: Optional[bool] = None,
        username: Optional[str] = None,
    ) -> Dict[str, Any]:
        """
        获取用户列表（带过滤条件）
        
        Args:
            current_user: 当前用户
            page: 页码
            page_size: 每页大小
            merchant_id: 商户ID过滤
            role: 角色过滤
            is_active: 状态过滤
            username: 用户名过滤
            
        Returns:
            Dict: 包含用户列表和分页信息
        """
        # 构建查询条件
        filters = {}

        # 处理商户ID过滤（基于数据权限配置）
        if not current_user.is_platform_user():
            # 非平台用户只能查看自己商户的用户
            filters["merchant_id"] = current_user.merchant_id

            # 使用权限服务检查用户数据权限
            from app.services.permission_service import PermissionService
            permission_service = PermissionService(self.db)

            # 检查用户数据权限级别
            if permission_service.check_data_permission(current_user, 'data:user:own'):
                # 只能查看自己的数据
                filters["user_id"] = current_user.id
            elif permission_service.check_data_permission(current_user, 'data:user:department'):
                # 可以查看本部门的用户数据
                accessible_dept_ids = self._get_accessible_departments(current_user)
                if accessible_dept_ids:
                    filters["department_id__in"] = accessible_dept_ids
                else:
                    # 如果没有部门信息，只能查看自己
                    filters["user_id"] = current_user.id
            elif permission_service.check_data_permission(current_user, 'data:user:all'):
                # 可以查看商户内所有用户数据，不需要额外过滤
                pass
            else:
                # 没有明确的用户数据权限，默认只能查看自己
                filters["user_id"] = current_user.id
        elif merchant_id is not None:
            # 平台用户可以按商户ID筛选
            filters["merchant_id"] = merchant_id
        
        # 处理其他过滤条件
        if role is not None:
            filters["role"] = role
        if is_active is not None:
            filters["is_active"] = is_active
        if username is not None:
            filters["username"] = username
        
        # 计算分页参数
        skip = (page - 1) * page_size
        limit = page_size
        
        # 获取用户列表和总数
        users, total = crud.user.get_multi_with_filters_and_count(
            self.db, skip=skip, limit=limit, filters=filters
        )
        
        return {
            "items": users,
            "total": total,
            "page": page,
            "page_size": page_size
        }
    
    def create_user(self, user_in: UserCreate, current_user: User) -> User:
        """
        创建新用户
        
        Args:
            user_in: 用户创建数据
            current_user: 当前用户
            
        Returns:
            User: 创建的用户对象
            
        Raises:
            HTTPException: 各种业务异常
        """
        # 检查用户名是否已存在
        existing_user = crud.user.get_by_username(self.db, username=user_in.username)
        if existing_user:
            raise HTTPException(
                status_code=400,
                detail="用户名已存在",
            )
        
        # 检查邮箱是否已存在
        if user_in.email:
            existing_email = crud.user.get_by_email(self.db, email=user_in.email)
            if existing_email:
                raise HTTPException(
                    status_code=400,
                    detail="邮箱已存在",
                )
        
        # 数据权限检查：商户用户只能创建自己商户的用户
        if not auth_service.can_access_merchant_data(current_user, user_in.merchant_id, self.db):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="只能创建自己商户的用户",
            )
        
        # 创建用户
        user = crud.user.create_with_password(
            db=self.db,
            username=user_in.username,
            password=user_in.password,
            email=user_in.email,
            full_name=user_in.full_name,
            role_codes=None,  # 暂时不处理角色，后续可以通过role_ids处理
            merchant_id=user_in.merchant_id,
            department_id=user_in.department_id,
            is_active=user_in.is_active,
            phone=user_in.phone,
            remark=user_in.remark,
        )
        
        return user
    
    def get_user_detail(self, user_id: int, current_user: User) -> User:
        """
        获取用户详情
        
        Args:
            user_id: 用户ID
            current_user: 当前用户
            
        Returns:
            User: 用户对象
            
        Raises:
            HTTPException: 用户不存在或权限不足
        """
        user = crud.user.get(self.db, id=user_id)
        if not user:
            raise HTTPException(
                status_code=404,
                detail="用户不存在",
            )
        
        # 数据权限检查：基于用户数据权限配置
        if current_user.id != user_id:
            # 使用权限服务检查用户数据权限
            from app.services.permission_service import PermissionService
            permission_service = PermissionService(self.db)

            # 检查是否有查看该用户数据的权限
            can_access = False

            if permission_service.check_data_permission(current_user, 'data:user:all'):
                # 可以查看商户内所有用户数据
                can_access = auth_service.can_access_merchant_data(current_user, user.merchant_id, self.db)
            elif permission_service.check_data_permission(current_user, 'data:user:department'):
                # 可以查看本部门用户数据
                can_access = (user.department_id == current_user.department_id and
                            auth_service.can_access_merchant_data(current_user, user.merchant_id, self.db))
            elif permission_service.check_data_permission(current_user, 'data:user:own'):
                # 只能查看自己的数据
                can_access = False  # 已经在上面检查了 current_user.id != user_id

            if not can_access:
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail="没有权限查看该用户信息",
                )
        
        return user
    
    def update_user(self, user_id: int, user_in: UserUpdate, current_user: User) -> User:
        """
        更新用户信息

        Args:
            user_id: 用户ID
            user_in: 更新数据
            current_user: 当前用户

        Returns:
            User: 更新后的用户对象
        """
        user = self.get_user_detail(user_id, current_user)

        # 不能编辑超级管理员（除非是超级管理员自己编辑自己）
        if user.is_superuser and current_user.id != user_id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="不能编辑超级管理员账号",
            )

        # 数据权限检查：只能编辑有权限访问的用户
        if current_user.id != user_id:
            if not auth_service.can_access_merchant_data(current_user, user.merchant_id, self.db):
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail="只能更新自己商家的用户",
                )

            # 检查是否试图更改商户ID
            if user_in.merchant_id is not None and user_in.merchant_id != user.merchant_id:
                if not auth_service.can_access_merchant_data(current_user, user_in.merchant_id, self.db):
                    raise HTTPException(
                        status_code=status.HTTP_403_FORBIDDEN,
                        detail="没有权限更改用户的商家",
                    )
        
        # 处理密码更新
        if user_in.password:
            user_in_dict = user_in.model_dump(exclude_unset=True)
            user_in_dict["hashed_password"] = get_password_hash(user_in.password)
            user_in_dict.pop("password", None)
            updated_user = crud.user.update(self.db, db_obj=user, obj_in=user_in_dict)
        else:
            updated_user = crud.user.update(self.db, db_obj=user, obj_in=user_in)
        
        return updated_user
    
    def delete_user(self, user_id: int, current_user: User) -> User:
        """
        删除用户
        
        Args:
            user_id: 用户ID
            current_user: 当前用户
            
        Returns:
            User: 被删除的用户对象
        """
        user = crud.user.get(self.db, id=user_id)
        if not user:
            raise HTTPException(
                status_code=404,
                detail="用户不存在",
            )
        
        # 不能删除自己
        if user_id == current_user.id:
            raise HTTPException(
                status_code=400,
                detail="不能删除自己",
            )
        
        # 不能删除超级管理员
        if user.is_superuser:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="不能删除超级管理员",
            )
        
        # 数据权限检查：只能删除有权限访问的用户
        if not auth_service.can_access_merchant_data(current_user, user.merchant_id, self.db):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="只能删除自己商家的用户",
            )
        
        user = crud.user.remove(self.db, id=user_id)
        return user
    
    def update_user_status(self, user_id: int, status_in: UserStatusUpdate, current_user: User) -> User:
        """
        更新用户状态
        
        Args:
            user_id: 用户ID
            status_in: 状态更新数据
            current_user: 当前用户
            
        Returns:
            User: 更新后的用户对象
        """
        user = crud.user.get(self.db, id=user_id)
        if not user:
            raise HTTPException(
                status_code=404,
                detail="用户不存在",
            )
        
        # 不能修改自己的状态，也不能修改超级管理员的状态
        if user.id == current_user.id:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="不能修改自己的状态",
            )
        if user.is_superuser:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="不能修改超级管理员的状态",
            )
        
        # 数据权限检查：只能修改有权限访问的用户状态
        if not auth_service.can_access_merchant_data(current_user, user.merchant_id, self.db):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="只能修改自己商家的用户状态",
            )
        
        # 更新用户状态
        updated_user = crud.user.update_user_info(
            self.db, user_id=user_id, update_data={"is_active": status_in.is_active}
        )
        
        if not updated_user:
            raise HTTPException(
                status_code=500,
                detail="更新用户状态失败",
            )
        
        return updated_user
    
    def reset_user_password(self, user_id: int, new_password: str, current_user: User) -> User:
        """
        重置用户密码
        
        Args:
            user_id: 用户ID
            new_password: 新密码
            current_user: 当前用户
            
        Returns:
            User: 更新后的用户对象
        """
        user = crud.user.get(self.db, id=user_id)
        if not user:
            raise HTTPException(
                status_code=404,
                detail="用户不存在",
            )
        
        # 不能重置超级管理员的密码
        if user.is_superuser and current_user.id != user_id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="不能重置超级管理员的密码",
            )
        
        # 数据权限检查：只能重置有权限访问的用户密码
        if current_user.id != user_id:
            if not auth_service.can_access_merchant_data(current_user, user.merchant_id, self.db):
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail="只能操作自己商家的用户",
                )
        
        hashed_password = get_password_hash(new_password)
        user = crud.user.update(
            self.db, db_obj=user, obj_in={"hashed_password": hashed_password}
        )
        return user
    
    def _check_merchant_scope(self, user: User) -> bool:
        """检查用户是否有商户级别的权限"""
        if hasattr(user, 'roles') and user.roles:
            for role in user.roles:
                if role.data_scope in ['merchant', 'all']:
                    return True
        return False
    
    def _get_accessible_departments(self, user: User) -> List[int]:
        """获取用户可访问的部门ID列表"""
        from app.services.department_service import department_service
        return department_service.get_user_accessible_departments(
            self.db, user, user.merchant_id
        )
