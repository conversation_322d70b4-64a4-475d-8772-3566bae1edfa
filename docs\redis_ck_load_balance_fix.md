# Redis CK负载均衡问题修复指南

## 问题分析

通过对Redis CK负载均衡系统的深入分析，发现了以下核心问题：

### 1. 主要问题：测试中缺少CK释放逻辑

**问题描述**：
- 负载均衡测试中，每次选择CK后没有释放它
- 导致第一个被选中的CK一直处于"预占用"状态
- 后续测试继续选择同一个CK，因为它仍然是最佳候选者

**影响**：
- 10轮测试都选择了同一个CK（ID 17）
- 负载均衡算法看起来失效
- 缓存验证总是返回True，负载始终为1

### 2. 次要问题

**ZRANGE排序问题**：
- 当多个CK分数相同时，Redis的`ZRANGE`命令按字典序返回
- 可能导致固定的选择顺序

**锁机制干扰**：
- 每个CK选择后设置30秒锁
- 快速连续测试可能受锁机制影响

**随机化不足**：
- 原版算法缺乏随机化
- 改进版的随机化在测试中可能未生效

## 修复方案

### 1. 修复测试逻辑

**修改文件**：`app/scripts/diagnose_ck_load_balance.py`

**主要改进**：
```python
# 在每次CK选择后立即释放
await ck_service.record_ck_usage(
    ck_id=ck.id,
    merchant_id=merchant_id,
    success=False  # 测试用途，不算成功绑卡
)
```

**新增功能**：
- 检查可用CK数量
- 记录选择详情
- 改进的均衡判断逻辑
- 添加小延迟避免锁冲突

### 2. 改进CK选择算法

**修改文件**：`app/services/redis_ck_service.py`

**主要改进**：
- 扩大候选范围（从5个增加到10个）
- 增加基于时间的随机种子
- 实现真正的随机选择
- 改进锁获取失败时的重试逻辑

**核心算法**：
```lua
-- 生成基于时间的随机种子
local time_info = redis.call('TIME')
local seed = (tonumber(time_info[1]) * 1000000 + tonumber(time_info[2])) % 1000000

-- 随机选择一个可用的CK
local selected_index = (seed % #available_cks) + 1
```

### 3. 新增测试工具

**文件**：`app/scripts/test_ck_load_balance_fix.py`

**功能**：
- 基础负载均衡测试（20轮）
- 并发负载均衡测试（10个并发请求）
- 压力负载均衡测试（50轮快速测试）
- 详细的结果分析和建议

**使用方法**：
```bash
# 测试指定商户
python app/scripts/test_ck_load_balance_fix.py --merchant-id 1

# 详细输出
python app/scripts/test_ck_load_balance_fix.py --verbose
```

### 4. Redis池修复工具

**文件**：`app/scripts/fix_redis_ck_pool.py`

**功能**：
- 诊断Redis池状态
- 检查数据库与Redis的一致性
- 自动修复不一致问题
- 清理孤立的锁和状态键

**使用方法**：
```bash
# 修复指定商户的Redis池
python app/scripts/fix_redis_ck_pool.py --merchant-id 1

# 修复所有商户
python app/scripts/fix_redis_ck_pool.py
```

## 使用指南

### 步骤1：诊断当前问题

```bash
# 运行Redis池修复工具进行诊断
python app/scripts/fix_redis_ck_pool.py --merchant-id YOUR_MERCHANT_ID
```

### 步骤2：修复Redis池状态

如果发现问题，工具会自动应用修复：
- 清理孤立的锁
- 重建缺失的Redis池
- 修复CK数据不一致
- 清理孤立的状态键

### 步骤3：验证负载均衡

```bash
# 运行负载均衡测试
python app/scripts/test_ck_load_balance_fix.py --merchant-id YOUR_MERCHANT_ID --verbose
```

### 步骤4：查看测试结果

测试结果包括：
- 基础测试：20轮选择，检查分布均匀性
- 并发测试：10个并发请求，检查并发安全性
- 压力测试：50轮快速测试，检查性能

**成功指标**：
- 选择了多个不同的CK（unique_ck_count > 1）
- 均衡分数 > 70分
- 分布相对均匀

## 预期效果

### 修复前
```
测试轮数: 10
选择的CK: [17, 17, 17, 17, 17, 17, 17, 17, 17, 17]
不同CK数: 1
是否均衡: ❌
```

### 修复后
```
测试轮数: 20
选择的CK: [17, 23, 45, 17, 23, 45, 17, 23, 45, 17, ...]
不同CK数: 3
均衡分数: 85.2
是否均衡: ✅
分布情况: {17: 7, 23: 7, 45: 6}
```

## 监控建议

### 1. 定期检查
- 每日运行Redis池状态检查
- 每周运行负载均衡测试
- 监控CK选择分布情况

### 2. 告警设置
- CK选择失败率 > 5%
- 负载均衡分数 < 60分
- Redis池与数据库不一致

### 3. 性能监控
- CK选择平均耗时
- Redis连接状态
- 锁竞争情况

## 故障排除

### 问题1：仍然选择同一个CK
**可能原因**：
- 只有一个可用CK
- Redis池未正确初始化
- 锁未正确释放

**解决方法**：
1. 检查数据库中可用CK数量
2. 运行Redis池修复工具
3. 清理残留的锁

### 问题2：CK选择失败
**可能原因**：
- Redis连接问题
- CK状态不正确
- 数据库连接问题

**解决方法**：
1. 检查Redis连接状态
2. 验证CK数据完整性
3. 重新同步CK池

### 问题3：性能问题
**可能原因**：
- Redis响应慢
- 锁竞争激烈
- 候选CK范围过大

**解决方法**：
1. 优化Redis配置
2. 调整锁超时时间
3. 调整候选CK数量

## 技术细节

### Lua脚本优化
- 使用原子操作确保一致性
- 基于时间的真随机数生成
- 智能的锁重试机制
- 详细的选择统计

### 测试策略
- 多层次测试（基础、并发、压力）
- 自动CK释放避免状态污染
- 详细的结果分析和建议
- 可配置的测试参数

### 监控指标
- 选择分布均匀性
- 响应时间统计
- 错误率监控
- 资源使用情况

## 总结

通过以上修复方案，Redis CK负载均衡系统应该能够：

1. **正确分配负载**：在多个可用CK之间均匀分布请求
2. **提高并发性能**：支持高并发场景下的CK选择
3. **增强稳定性**：自动检测和修复数据不一致问题
4. **便于监控**：提供详细的测试和诊断工具

建议在生产环境部署前，先在测试环境验证修复效果，确保所有功能正常工作。
