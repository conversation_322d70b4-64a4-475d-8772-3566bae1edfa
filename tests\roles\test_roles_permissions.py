#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
角色权限测试
测试角色管理和权限验证功能
"""

import sys
import os
import time

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.dirname(__file__))))

from test.conftest import TestBase, get_test_accounts, format_test_result, print_test_summary

class RolesPermissionsTestSuite(TestBase):
    """角色权限测试套件"""

    def __init__(self):
        super().__init__()
        self.results = []
        self.test_accounts = get_test_accounts()
        self.admin_token = None
        self.merchant_token = None

    def setup(self):
        """测试前置设置"""
        print("=== 测试前置设置 ===")

        # 管理员登录
        self.admin_token = self.login("admin", "7c222fb2927d828af22f592134e8932480637c0d")
        if not self.admin_token:
            print("❌ 管理员登录失败")
            return False

        # 商户管理员登录
        self.merchant_token = self.login("test1", "********")
        if not self.merchant_token:
            print("❌ 商户管理员登录失败")
            return False

        print("✅ 测试前置设置完成")
        return True

    def test_get_roles_list(self):
        """测试获取角色列表"""
        print("\n=== 测试获取角色列表 ===")

        # 测试管理员获取角色列表
        if self.admin_token:
            status_code, response = self.make_request("GET", "/roles", self.admin_token)

            if status_code == 200:
                roles_data = response.get("data", {})
                roles = roles_data.get("items", []) if isinstance(roles_data, dict) else response.get("items", [])

                self.results.append(format_test_result(
                    "管理员获取角色列表",
                    True,
                    f"成功获取 {len(roles)} 个角色",
                    {"role_count": len(roles)}
                ))
                print(f"✅ 管理员成功获取角色列表，共 {len(roles)} 个角色")

                # 检查是否包含基本角色（不包括super_admin，因为应该被过滤掉）
                role_codes = [role.get("code", "") for role in roles]
                expected_roles = ["merchant_admin", "ck_supplier"]
                found_roles = [role for role in expected_roles if role in role_codes]

                # 验证超级管理员角色被过滤掉
                super_admin_found = "super_admin" in role_codes
                if super_admin_found:
                    self.results.append(format_test_result(
                        "超级管理员角色过滤检查",
                        False,
                        "超级管理员角色应该被过滤掉，但仍然出现在列表中"
                    ))
                    print(f"❌ 超级管理员角色应该被过滤掉，但仍然出现在列表中")
                else:
                    self.results.append(format_test_result(
                        "超级管理员角色过滤检查",
                        True,
                        "超级管理员角色已被正确过滤"
                    ))
                    print(f"✅ 超级管理员角色已被正确过滤")

                if len(found_roles) >= 1:
                    self.results.append(format_test_result(
                        "基本角色存在性检查",
                        True,
                        f"找到基本角色: {found_roles}"
                    ))
                    print(f"✅ 找到基本角色: {found_roles}")
                else:
                    self.results.append(format_test_result(
                        "基本角色存在性检查",
                        False,
                        f"缺少基本角色，只找到: {found_roles}"
                    ))
                    print(f"⚠️ 缺少基本角色，只找到: {found_roles}")
            else:
                self.results.append(format_test_result(
                    "管理员获取角色列表",
                    False,
                    f"获取角色列表失败，状态码: {status_code}"
                ))
                print(f"❌ 管理员获取角色列表失败，状态码: {status_code}")

        # 测试商户管理员获取角色列表（应该被限制）
        if self.merchant_token:
            status_code, response = self.make_request("GET", "/roles", self.merchant_token)

            if status_code in [403, 401]:
                self.results.append(format_test_result(
                    "商户管理员角色列表权限控制",
                    True,
                    "正确限制商户管理员访问角色列表"
                ))
                print("✅ 正确限制商户管理员访问角色列表")
            elif status_code == 200:
                roles_data = response.get("data", {})
                roles = roles_data.get("items", []) if isinstance(roles_data, dict) else response.get("items", [])

                self.results.append(format_test_result(
                    "商户管理员角色列表访问",
                    False,
                    f"商户管理员不应该能访问角色列表，但获取了 {len(roles)} 个角色"
                ))
                print(f"❌ 商户管理员不应该能访问角色列表，但获取了 {len(roles)} 个角色")
            else:
                self.results.append(format_test_result(
                    "商户管理员获取角色列表",
                    False,
                    f"获取角色列表失败，状态码: {status_code}"
                ))
                print(f"❌ 商户管理员获取角色列表失败，状态码: {status_code}")

    def test_get_permissions_list(self):
        """测试获取权限列表"""
        print("\n=== 测试获取权限列表 ===")

        # 测试管理员获取权限列表
        if self.admin_token:
            status_code, response = self.make_request("GET", "/permissions", self.admin_token)

            if status_code == 200:
                permissions_data = response.get("data", {})
                permissions = permissions_data.get("items", []) if isinstance(permissions_data, dict) else response.get("items", [])

                self.results.append(format_test_result(
                    "管理员获取权限列表",
                    True,
                    f"成功获取 {len(permissions)} 个权限",
                    {"permission_count": len(permissions)}
                ))
                print(f"✅ 管理员成功获取权限列表，共 {len(permissions)} 个权限")

                # 检查是否包含基本权限
                permission_codes = [perm.get("code", "") for perm in permissions]
                expected_permissions = ["user:view", "merchant:view", "dashboard:view"]
                found_permissions = [perm for perm in expected_permissions if perm in permission_codes]

                if len(found_permissions) >= 2:
                    self.results.append(format_test_result(
                        "基本权限存在性检查",
                        True,
                        f"找到基本权限: {found_permissions}"
                    ))
                    print(f"✅ 找到基本权限: {found_permissions}")
                else:
                    self.results.append(format_test_result(
                        "基本权限存在性检查",
                        False,
                        f"缺少基本权限，只找到: {found_permissions}"
                    ))
                    print(f"⚠️ 缺少基本权限，只找到: {found_permissions}")
            else:
                self.results.append(format_test_result(
                    "管理员获取权限列表",
                    False,
                    f"获取权限列表失败，状态码: {status_code}"
                ))
                print(f"❌ 管理员获取权限列表失败，状态码: {status_code}")

        # 测试商户管理员获取权限列表（应该被限制）
        if self.merchant_token:
            status_code, response = self.make_request("GET", "/permissions", self.merchant_token)

            if status_code in [403, 401]:
                self.results.append(format_test_result(
                    "商户管理员权限列表权限控制",
                    True,
                    "正确限制商户管理员访问权限列表"
                ))
                print("✅ 正确限制商户管理员访问权限列表")
            elif status_code == 200:
                permissions_data = response.get("data", {})
                permissions = permissions_data.get("items", []) if isinstance(permissions_data, dict) else response.get("items", [])

                self.results.append(format_test_result(
                    "商户管理员权限列表访问",
                    False,
                    f"商户管理员不应该能访问权限列表，但获取了 {len(permissions)} 个权限"
                ))
                print(f"❌ 商户管理员不应该能访问权限列表，但获取了 {len(permissions)} 个权限")
            else:
                self.results.append(format_test_result(
                    "商户管理员获取权限列表",
                    False,
                    f"获取权限列表失败，状态码: {status_code}"
                ))
                print(f"❌ 商户管理员获取权限列表失败，状态码: {status_code}")

    def test_menu_permissions(self):
        """测试菜单权限"""
        print("\n=== 测试菜单权限 ===")

        # 测试管理员获取菜单
        if self.admin_token:
            status_code, response = self.make_request("GET", "/menus/user-menus", self.admin_token)

            if status_code == 200:
                menus = response.get("data", response.get("menus", []))

                self.results.append(format_test_result(
                    "管理员获取菜单",
                    True,
                    f"管理员成功获取 {len(menus)} 个菜单",
                    {"menu_count": len(menus)}
                ))
                print(f"✅ 管理员成功获取 {len(menus)} 个菜单")

                # 检查管理员是否有系统管理菜单
                menu_codes = []
                def extract_menu_codes(menu_list):
                    for menu in menu_list:
                        if isinstance(menu, dict):
                            menu_codes.append(menu.get("code", ""))
                            if "children" in menu and isinstance(menu["children"], list):
                                extract_menu_codes(menu["children"])

                extract_menu_codes(menus)

                admin_menus = ["system-users", "merchants", "roles"]
                found_admin_menus = [menu for menu in admin_menus if menu in menu_codes]

                if len(found_admin_menus) >= 1:
                    self.results.append(format_test_result(
                        "管理员系统菜单权限",
                        True,
                        f"管理员正确拥有系统菜单: {found_admin_menus}"
                    ))
                    print(f"✅ 管理员正确拥有系统菜单: {found_admin_menus}")
                else:
                    self.results.append(format_test_result(
                        "管理员系统菜单权限",
                        False,
                        f"管理员缺少系统菜单权限"
                    ))
                    print(f"⚠️ 管理员缺少系统菜单权限")
            else:
                self.results.append(format_test_result(
                    "管理员获取菜单",
                    False,
                    f"获取菜单失败，状态码: {status_code}"
                ))
                print(f"❌ 管理员获取菜单失败，状态码: {status_code}")

        # 测试商户管理员获取菜单
        if self.merchant_token:
            status_code, response = self.make_request("GET", "/menus/user-menus", self.merchant_token)

            if status_code == 200:
                menus = response.get("data", response.get("menus", []))

                self.results.append(format_test_result(
                    "商户管理员获取菜单",
                    True,
                    f"商户管理员成功获取 {len(menus)} 个菜单",
                    {"menu_count": len(menus)}
                ))
                print(f"✅ 商户管理员成功获取 {len(menus)} 个菜单")

                # 检查商户管理员是否没有系统管理菜单
                menu_codes = []
                def extract_menu_codes(menu_list):
                    for menu in menu_list:
                        if isinstance(menu, dict):
                            menu_codes.append(menu.get("code", ""))
                            if "children" in menu and isinstance(menu["children"], list):
                                extract_menu_codes(menu["children"])

                extract_menu_codes(menus)

                system_menus = ["system-users", "merchants", "roles"]
                found_system_menus = [menu for menu in system_menus if menu in menu_codes]

                if len(found_system_menus) == 0:
                    self.results.append(format_test_result(
                        "商户管理员系统菜单权限控制",
                        True,
                        "商户管理员正确没有系统管理菜单权限"
                    ))
                    print("✅ 商户管理员正确没有系统管理菜单权限")
                else:
                    self.results.append(format_test_result(
                        "商户管理员系统菜单权限控制",
                        False,
                        f"商户管理员不应该有系统菜单: {found_system_menus}"
                    ))
                    print(f"❌ 商户管理员不应该有系统菜单: {found_system_menus}")
            else:
                self.results.append(format_test_result(
                    "商户管理员获取菜单",
                    False,
                    f"获取菜单失败，状态码: {status_code}"
                ))
                print(f"❌ 商户管理员获取菜单失败，状态码: {status_code}")

    def test_api_permissions(self):
        """测试API权限"""
        print("\n=== 测试API权限 ===")

        # 测试商户管理员访问系统级API（应该被拒绝）
        if self.merchant_token:
            system_apis = [
                "/roles",
                "/permissions",
                "/system/params"
            ]

            for api in system_apis:
                status_code, response = self.make_request("GET", api, self.merchant_token)

                if status_code in [403, 401]:
                    self.results.append(format_test_result(
                        f"API权限控制_{api}",
                        True,
                        f"正确拒绝商户管理员访问 {api}"
                    ))
                    print(f"✅ 正确拒绝商户管理员访问 {api}")
                else:
                    self.results.append(format_test_result(
                        f"API权限控制_{api}",
                        False,
                        f"商户管理员不应该能访问 {api}，状态码: {status_code}"
                    ))
                    print(f"❌ 商户管理员不应该能访问 {api}，状态码: {status_code}")

    def test_dynamic_permission_system(self):
        """测试动态权限系统"""
        print("\n=== 测试动态权限系统 ===")

        # 测试用户当前权限
        if self.admin_token:
            status_code, response = self.get_user_info(self.admin_token)

            if status_code == 200:
                user_info = response.get("data", response)

                # 检查用户信息中是否包含角色信息
                has_roles = "roles" in user_info or "role" in user_info

                if has_roles:
                    self.results.append(format_test_result(
                        "用户角色信息",
                        True,
                        "用户信息正确包含角色信息"
                    ))
                    print("✅ 用户信息正确包含角色信息")
                else:
                    self.results.append(format_test_result(
                        "用户角色信息",
                        False,
                        "用户信息缺少角色信息"
                    ))
                    print("⚠️ 用户信息缺少角色信息")
            else:
                self.results.append(format_test_result(
                    "获取用户信息",
                    False,
                    f"获取用户信息失败，状态码: {status_code}"
                ))
                print(f"❌ 获取用户信息失败，状态码: {status_code}")

    def test_new_user_menu_permission_issue(self):
        """测试新用户菜单权限问题 - 重现"没有获取用户菜单的权限"错误"""
        print("\n=== 测试新用户菜单权限问题 ===")

        test_user_id = None
        test_role_id = None
        test_user_token = None

        try:
            # 1. 创建测试角色
            print("步骤1: 创建测试角色")
            role_data = {
                "name": "测试菜单权限角色",
                "code": "test_menu_permission_role",
                "description": "用于测试菜单权限问题的角色",
                "is_enabled": True
            }

            status_code, response = self.make_request("POST", "/roles", self.admin_token, role_data)

            # 处理嵌套的响应格式
            if status_code == 200:
                # 检查多层嵌套的响应格式
                if response.get("code") == 0 and response.get("data", {}).get("success"):
                    test_role_id = response["data"]["data"]["id"]
                    print(f"✅ 测试角色创建成功，ID: {test_role_id}")
                elif response.get("success"):
                    test_role_id = response["data"]["id"]
                    print(f"✅ 测试角色创建成功，ID: {test_role_id}")
                else:
                    self.results.append(format_test_result(
                        "创建测试角色",
                        False,
                        f"创建测试角色失败: {response}"
                    ))
                    print(f"❌ 创建测试角色失败: {response}")
                    return
            else:
                self.results.append(format_test_result(
                    "创建测试角色",
                    False,
                    f"创建测试角色失败，状态码: {status_code}, 响应: {response}"
                ))
                print(f"❌ 创建测试角色失败，状态码: {status_code}")
                return

            # 2. 获取菜单权限并分配给角色
            print("步骤2: 为角色分配菜单权限")

            # 获取所有权限，查找菜单相关权限
            status_code, response = self.make_request("GET", "/permissions", self.admin_token)

            if status_code == 200:
                permissions_data = response.get("data", {})
                permissions = permissions_data.get("items", []) if isinstance(permissions_data, dict) else response.get("items", [])

                # 查找用户菜单相关权限
                menu_permission_ids = []
                for perm in permissions:
                    perm_code = perm.get("code", "")
                    if "api:menus:user-menus" in perm_code:
                        menu_permission_ids.append(perm["id"])
                        print(f"找到菜单权限: {perm_code}")

                if menu_permission_ids:
                    # 为角色分配权限
                    status_code, response = self.make_request(
                        "PUT",
                        f"/roles/{test_role_id}/permissions",
                        self.admin_token,
                        menu_permission_ids
                    )

                    if status_code == 200:
                        print(f"✅ 成功为角色分配菜单权限，权限数量: {len(menu_permission_ids)}")
                    else:
                        print(f"❌ 分配菜单权限失败: {response}")
                        return
                else:
                    print("❌ 没有找到菜单相关权限")
                    return
            else:
                print(f"❌ 获取权限列表失败: {response}")
                return

            # 3. 获取菜单并分配给角色
            print("步骤3: 为角色分配菜单")

            status_code, response = self.make_request("GET", "/menus", self.admin_token)

            if status_code == 200:
                menus = response.get("data", [])
                if menus:
                    # 选择一些基础菜单
                    basic_menu_codes = ["dashboard"]
                    selected_menus = []

                    for menu in menus:
                        if menu.get("code") in basic_menu_codes:
                            selected_menus.append({"id": menu["id"], "code": menu["code"]})

                    if selected_menus:
                        menu_data = {"menus": selected_menus}

                        status_code, response = self.make_request(
                            "PUT",
                            f"/roles/{test_role_id}/menus",
                            self.admin_token,
                            menu_data
                        )

                        if status_code == 200:
                            print(f"✅ 成功为角色分配菜单: {[m['code'] for m in selected_menus]}")
                        else:
                            print(f"❌ 分配菜单失败: {response}")
                            return
                    else:
                        print("❌ 没有找到基础菜单")
                        return
                else:
                    print("❌ 没有找到任何菜单")
                    return
            else:
                print(f"❌ 获取菜单列表失败: {response}")
                return

            # 4. 创建测试用户
            print("步骤4: 创建测试用户")

            user_data = {
                "username": "test_menu_permission_user",
                "password": "test123456",
                "email": "<EMAIL>",
                "full_name": "测试菜单权限用户",
                "is_active": True,
                "role_ids": [test_role_id]
            }

            status_code, response = self.make_request("POST", "/users", self.admin_token, user_data)

            if status_code == 200 and response.get("success"):
                test_user_id = response["data"]["id"]
                print(f"✅ 测试用户创建成功，ID: {test_user_id}")
            else:
                print(f"❌ 创建测试用户失败: {response}")
                return

            # 5. 测试用户登录
            print("步骤5: 测试用户登录")

            test_user_token = self.login("test_menu_permission_user", "test123456")

            if test_user_token:
                print("✅ 测试用户登录成功")
            else:
                self.results.append(format_test_result(
                    "测试用户登录",
                    False,
                    "测试用户登录失败"
                ))
                print("❌ 测试用户登录失败")
                return

            # 6. 测试获取用户菜单（这里可能会失败）
            print("步骤6: 测试获取用户菜单")

            status_code, response = self.make_request("GET", "/menus/user-menus", test_user_token)

            print(f"请求状态码: {status_code}")
            print(f"响应内容: {response}")

            if status_code == 200:
                self.results.append(format_test_result(
                    "新用户获取菜单权限",
                    True,
                    "新用户成功获取菜单权限"
                ))
                print("✅ 新用户成功获取菜单权限")
            elif status_code == 403:
                self.results.append(format_test_result(
                    "新用户获取菜单权限",
                    False,
                    f"新用户菜单权限被拒绝 - 这就是需要修复的问题！响应: {response}"
                ))
                print("❌ 新用户菜单权限被拒绝 - 这就是需要修复的问题！")
            else:
                self.results.append(format_test_result(
                    "新用户获取菜单权限",
                    False,
                    f"获取用户菜单失败，状态码: {status_code}, 响应: {response}"
                ))
                print(f"❌ 获取用户菜单失败，状态码: {status_code}")

        except Exception as e:
            self.results.append(format_test_result(
                "新用户菜单权限测试",
                False,
                f"测试过程中发生异常: {str(e)}"
            ))
            print(f"❌ 测试过程中发生异常: {e}")

        finally:
            # 清理测试数据
            print("步骤7: 清理测试数据")

            if test_user_id:
                status_code, response = self.make_request("DELETE", f"/users/{test_user_id}", self.admin_token)
                if status_code == 200:
                    print("✅ 测试用户删除成功")
                else:
                    print(f"❌ 删除测试用户失败: {response}")

            if test_role_id:
                status_code, response = self.make_request("DELETE", f"/roles/{test_role_id}", self.admin_token)
                if status_code == 200:
                    print("✅ 测试角色删除成功")
                else:
                    print(f"❌ 删除测试角色失败: {response}")

    def run_all_tests(self):
        """运行所有角色权限测试"""
        print("🚀 开始角色权限测试")
        print("="*60)

        start_time = time.time()

        # 前置设置
        if not self.setup():
            print("❌ 测试前置设置失败，跳过测试")
            return []

        # 运行所有测试
        self.test_get_roles_list()
        self.test_get_permissions_list()
        self.test_menu_permissions()
        self.test_api_permissions()
        self.test_dynamic_permission_system()
        self.test_new_user_menu_permission_issue()

        end_time = time.time()
        duration = end_time - start_time

        # 打印测试摘要
        print_test_summary(self.results)
        print(f"测试耗时: {duration:.2f}秒")

        return self.results

def main():
    """主函数"""
    test_suite = RolesPermissionsTestSuite()
    results = test_suite.run_all_tests()

    # 判断测试是否通过
    failed_tests = [r for r in results if not r.get("success", False)]

    if not failed_tests:
        print("\n🎉 所有角色权限测试通过！")
        return 0
    else:
        print(f"\n⚠️ 发现 {len(failed_tests)} 个问题需要修复")
        for test in failed_tests:
            print(f"  - {test['test_name']}: {test['message']}")
        return 1

if __name__ == "__main__":
    sys.exit(main())
