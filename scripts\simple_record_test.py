#!/usr/bin/env python3
"""
简单的记录查询测试
"""
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from app.db.session import SessionLocal
from app.crud import card as card_crud
from sqlalchemy import text

def test_record_query():
    """测试记录查询"""
    # 测试最近的记录
    record_id = "1e461906-1ffb-4d99-9fc8-401990413426"
    print(f"测试记录ID: {record_id}")
    
    # 方法1: CRUD查询
    print("\n1. CRUD查询测试:")
    try:
        with SessionLocal() as db:
            record = card_crud.get(db, record_id)
            if record:
                print(f"   ✅ CRUD查询找到记录")
                print(f"   状态: {record.status}")
                print(f"   回调状态: {record.callback_status}")
                print(f"   商户ID: {record.merchant_id}")
            else:
                print("   ❌ CRUD查询未找到记录")
    except Exception as e:
        print(f"   ❌ CRUD查询失败: {e}")
    
    # 方法2: 直接SQL查询
    print("\n2. 直接SQL查询测试:")
    try:
        with SessionLocal() as db:
            result = db.execute(text(
                "SELECT id, status, callback_status, merchant_id FROM card_records WHERE id = :id"
            ), {"id": record_id}).fetchone()
            
            if result:
                print(f"   ✅ SQL查询找到记录")
                print(f"   状态: {result[1]}")
                print(f"   回调状态: {result[2]}")
                print(f"   商户ID: {result[3]}")
            else:
                print("   ❌ SQL查询未找到记录")
    except Exception as e:
        print(f"   ❌ SQL查询失败: {e}")
    
    # 方法3: 查询最新的几条记录
    print("\n3. 查询最新记录:")
    try:
        with SessionLocal() as db:
            results = db.execute(text(
                "SELECT id, status, callback_status, created_at FROM card_records "
                "ORDER BY created_at DESC LIMIT 5"
            )).fetchall()
            
            print(f"   最新的5条记录:")
            for i, row in enumerate(results, 1):
                print(f"   {i}. ID: {row[0][:8]}..., 状态: {row[1]}, 回调: {row[2]}, 时间: {row[3]}")
                
    except Exception as e:
        print(f"   ❌ 查询最新记录失败: {e}")

if __name__ == "__main__":
    test_record_query()
