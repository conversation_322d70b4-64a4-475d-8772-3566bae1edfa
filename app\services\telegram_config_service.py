"""
Telegram配置管理服务
"""

import time
from typing import Dict, Any, Optional, List, Tuple
from sqlalchemy.orm import Session

from app.db.session import SessionLocal
from app.core.logging import get_logger
from app.models.telegram_group import TelegramGroup
from app.models.merchant_telegram_setting import MerchantTelegramSetting
from app.models.telegram_permission_template import TelegramPermissionTemplate
from app.models.telegram_bot_config import TelegramBotConfig

logger = get_logger(__name__)


class TelegramConfigService:
    """Telegram配置管理服务"""
    
    def __init__(self, db: Session = None):
        self.db = db or SessionLocal()
        self._cache = {}
        self._cache_expire = 300  # 5分钟缓存
    
    def get_global_default_settings(self) -> dict:
        """获取全局默认配置"""
        cache_key = "global_default_settings"

        if self._is_cache_valid(cache_key):
            return self._cache[cache_key]['data']

        # 从数据库获取基础配置
        basic_configs = self.db.query(TelegramBotConfig).filter(
            ~TelegramBotConfig.config_key.like('global_default_%')
        ).all()

        # 从数据库获取全局默认配置（用于复杂嵌套结构）
        global_configs = self.db.query(TelegramBotConfig).filter(
            TelegramBotConfig.config_key.like('global_default_%')
        ).all()
        
        # 初始化配置结构，包含基础配置和复杂嵌套配置
        settings = {}

        # 添加基础配置
        for config in basic_configs:
            key = config.config_key
            value = self._parse_config_value(config.config_value, config.config_type)
            settings[key] = value

        # 添加默认的复杂嵌套配置结构
        nested_settings = {
            "permissions": {
                "allow_all_members": False,
                "require_user_verification": True,
                "admin_only_commands": ["bind", "unbind", "settings"],
                "rate_limit": {
                    "commands_per_minute": 10,
                    "queries_per_hour": 100
                },
                "query_permissions": {
                    "daily_stats": True,
                    "weekly_stats": True,
                    "monthly_stats": True,
                    "custom_range": False,
                    "detailed_data": False
                }
            },
            "display_settings": {
                "show_amount": True,
                "show_details": False,
                "decimal_places": 2,
                "timezone": "Asia/Shanghai",
                "language": "zh-CN",
                "currency_symbol": "¥",
                "date_format": "YYYY-MM-DD",
                "time_format": "HH:mm:ss"
            },
            "notification_settings": {
                "auto_notification": True,
                "welcome_message": True,
                "command_help": True,
                "auto_delete_commands": False,
                "error_notification": True,
                "daily_report": False,
                "daily_report_time": "09:00"
            },
            "advanced_settings": {
                "enable_cache": True,
                "cache_expire_minutes": 5,
                "enable_audit_log": True,
                "custom_commands": {},
                "webhook_settings": {
                    "enable_webhook": False,
                    "webhook_url": "",
                    "webhook_secret": ""
                }
            }
        }

        # 合并嵌套配置
        settings.update(nested_settings)
        
        # 应用数据库中的全局配置覆盖
        for config in global_configs:
            self._apply_global_config(settings, config)
        
        self._set_cache(cache_key, settings)
        return settings

    def get_global_config(self) -> dict:
        """获取全局配置（别名方法）"""
        return self.get_global_default_settings()

    def update_global_config(self, config_data: dict) -> dict:
        """更新全局配置"""
        try:
            # 定义配置键映射和类型
            config_mappings = {
                # 基础配置（直接存储）
                'bot_token': {'type': 'string', 'group': 'basic', 'encrypted': True},
                'webhook_url': {'type': 'string', 'group': 'basic'},
                'webhook_secret': {'type': 'string', 'group': 'basic', 'encrypted': True},
                'rate_limit_global': {'type': 'number', 'group': 'rate_limit'},
                'rate_limit_group': {'type': 'number', 'group': 'rate_limit'},
                'rate_limit_user': {'type': 'number', 'group': 'rate_limit'},
                'bind_token_expire_hours': {'type': 'number', 'group': 'security'},
                'verification_token_expire_minutes': {'type': 'number', 'group': 'security'},
                'max_bind_attempts_per_day': {'type': 'number', 'group': 'security'},
                'enable_audit_log': {'type': 'boolean', 'group': 'features'},
                'mask_sensitive_data': {'type': 'boolean', 'group': 'features'},
                'default_timezone': {'type': 'string', 'group': 'defaults'},
                'default_language': {'type': 'string', 'group': 'defaults'},
            }

            # 保存基础配置
            for key, value in config_data.items():
                if key in config_mappings:
                    mapping = config_mappings[key]

                    # 获取或创建配置记录
                    config = self.db.query(TelegramBotConfig).filter_by(config_key=key).first()
                    if config:
                        # 更新现有配置
                        config.config_value = str(value)
                        config.config_type = mapping['type']
                        config.config_group = mapping['group']
                        config.is_encrypted = mapping.get('encrypted', False)
                    else:
                        # 创建新配置
                        config = TelegramBotConfig(
                            config_key=key,
                            config_value=str(value),
                            config_type=mapping['type'],
                            config_group=mapping['group'],
                            is_encrypted=mapping.get('encrypted', False),
                            is_system=True
                        )
                        self.db.add(config)

            # 提交事务
            self.db.commit()

            # 清除缓存
            self._cache.pop("global_default_settings", None)

            # 返回更新后的配置
            return self.get_global_default_settings()

        except Exception as e:
            # 回滚事务
            self.db.rollback()
            logger.error(f"更新全局配置失败: {e}")
            raise

    def get_merchant_settings(self, merchant_id: int) -> dict:
        """获取商户级配置"""
        cache_key = f"merchant_settings_{merchant_id}"
        
        if self._is_cache_valid(cache_key):
            return self._cache[cache_key]['data']
        
        merchant_setting = self.db.query(MerchantTelegramSetting).filter_by(
            merchant_id=merchant_id,
            is_active=True
        ).first()
        
        settings = merchant_setting.settings if merchant_setting else {}
        self._set_cache(cache_key, settings)
        return settings
    
    def get_group_settings(self, group: TelegramGroup) -> dict:
        """获取群组级配置"""
        return group.settings or {}
    
    def get_effective_group_settings(self, group: TelegramGroup) -> dict:
        """获取群组的最终生效配置（合并继承）"""
        cache_key = f"effective_group_settings_{group.id}"
        
        if self._is_cache_valid(cache_key):
            return self._cache[cache_key]['data']
        
        # 获取各级配置
        global_settings = self.get_global_default_settings()
        merchant_settings = self.get_merchant_settings(group.merchant_id)
        group_settings = self.get_group_settings(group)
        
        # 深度合并配置
        effective_settings = self._deep_merge_settings([
            global_settings,
            merchant_settings,
            group_settings
        ])
        
        self._set_cache(cache_key, effective_settings)
        return effective_settings
    
    def update_merchant_settings(
        self, 
        merchant_id: int, 
        settings: dict, 
        user_id: int
    ) -> MerchantTelegramSetting:
        """更新商户级配置"""
        merchant_setting = self.db.query(MerchantTelegramSetting).filter_by(
            merchant_id=merchant_id
        ).first()
        
        if merchant_setting:
            # 合并现有配置
            current_settings = merchant_setting.settings or {}
            merged_settings = self._deep_merge_settings([current_settings, settings])
            merchant_setting.settings = merged_settings
            merchant_setting.updated_by = user_id
        else:
            # 创建新配置
            merchant_setting = MerchantTelegramSetting(
                merchant_id=merchant_id,
                settings=settings,
                created_by=user_id,
                updated_by=user_id
            )
            self.db.add(merchant_setting)
        
        self.db.commit()
        
        # 清除相关缓存
        self._clear_merchant_cache(merchant_id)
        
        return merchant_setting
    
    def update_group_settings(
        self, 
        group: TelegramGroup, 
        settings: dict
    ) -> TelegramGroup:
        """更新群组级配置"""
        current_settings = group.settings or {}
        merged_settings = self._deep_merge_settings([current_settings, settings])
        group.settings = merged_settings
        
        self.db.commit()
        
        # 清除相关缓存
        self._clear_group_cache(group.id)
        
        return group
    
    def apply_permission_template(
        self, 
        group: TelegramGroup, 
        template_code: str
    ) -> TelegramGroup:
        """应用权限模板到群组"""
        template = self.db.query(TelegramPermissionTemplate).filter_by(
            template_code=template_code,
            is_active=True
        ).first()
        
        if not template:
            raise ValueError(f"权限模板 {template_code} 不存在或已禁用")
        
        return self.update_group_settings(group, template.settings)
    
    def apply_template_to_merchant(
        self,
        merchant_id: int,
        template_code: str,
        user_id: int
    ) -> MerchantTelegramSetting:
        """应用权限模板到商户"""
        template = self.db.query(TelegramPermissionTemplate).filter_by(
            template_code=template_code,
            is_active=True
        ).first()
        
        if not template:
            raise ValueError(f"权限模板 {template_code} 不存在或已禁用")
        
        return self.update_merchant_settings(merchant_id, template.settings, user_id)
    
    def get_permission_templates(self, include_system: bool = True, include_custom: bool = True) -> List[TelegramPermissionTemplate]:
        """获取权限模板列表"""
        query = self.db.query(TelegramPermissionTemplate).filter_by(is_active=True)
        
        if not include_system and not include_custom:
            return []
        elif not include_system:
            query = query.filter_by(is_system=False)
        elif not include_custom:
            query = query.filter_by(is_system=True)
        
        return query.order_by(TelegramPermissionTemplate.is_system.desc(), TelegramPermissionTemplate.template_name).all()
    
    def create_permission_template(
        self,
        template_name: str,
        template_code: str,
        description: str,
        settings: dict,
        user_id: int,
        is_system: bool = False
    ) -> TelegramPermissionTemplate:
        """创建权限模板"""
        # 检查模板代码是否已存在
        existing = self.db.query(TelegramPermissionTemplate).filter_by(
            template_code=template_code
        ).first()
        
        if existing:
            raise ValueError(f"模板代码 {template_code} 已存在")
        
        template = TelegramPermissionTemplate(
            template_name=template_name,
            template_code=template_code,
            description=description,
            settings=settings,
            is_system=is_system,
            created_by=user_id,
            updated_by=user_id
        )
        
        # 验证配置
        is_valid, errors = template.validate_settings()
        if not is_valid:
            raise ValueError(f"模板配置无效：{'; '.join(errors)}")
        
        self.db.add(template)
        self.db.commit()
        self.db.refresh(template)
        
        return template
    
    def validate_settings(self, settings: dict) -> Tuple[bool, List[str]]:
        """验证配置的有效性"""
        errors = []

        if not isinstance(settings, dict):
            errors.append("配置必须是字典格式")
            return False, errors

        # 验证基础配置
        basic_errors = self._validate_basic_settings(settings)
        errors.extend(basic_errors)

        # 验证权限配置
        if 'permissions' in settings:
            permission_errors = self._validate_permissions(settings['permissions'])
            errors.extend(permission_errors)

        # 验证显示配置
        if 'display_settings' in settings:
            display_errors = self._validate_display_settings(settings['display_settings'])
            errors.extend(display_errors)

        # 验证通知配置
        if 'notification_settings' in settings:
            notification_errors = self._validate_notification_settings(settings['notification_settings'])
            errors.extend(notification_errors)

        return len(errors) == 0, errors

    def _validate_basic_settings(self, settings: dict) -> List[str]:
        """验证基础配置"""
        errors = []

        # 验证Bot Token
        if 'bot_token' in settings:
            bot_token = settings['bot_token']
            if not bot_token or not isinstance(bot_token, str):
                errors.append("Bot Token不能为空")
            elif len(bot_token) < 40:
                errors.append("Bot Token长度不能少于40个字符")

        # 验证Webhook URL
        if 'webhook_url' in settings:
            webhook_url = settings['webhook_url']
            if not webhook_url or not isinstance(webhook_url, str):
                errors.append("Webhook URL不能为空")
            elif not webhook_url.startswith('https://'):
                errors.append("Webhook URL必须使用HTTPS协议")

        # 验证Webhook Secret
        if 'webhook_secret' in settings:
            webhook_secret = settings['webhook_secret']
            if not webhook_secret or not isinstance(webhook_secret, str):
                errors.append("Webhook Secret不能为空")
            elif len(webhook_secret) < 8:
                errors.append("Webhook Secret长度不能少于8个字符")

        # 验证频率限制配置
        rate_limit_fields = ['rate_limit_global', 'rate_limit_group', 'rate_limit_user']
        for field in rate_limit_fields:
            if field in settings:
                value = settings[field]
                if not isinstance(value, (int, float)) or value <= 0:
                    errors.append(f"{field}必须是大于0的数字")

        # 验证时间配置
        time_fields = ['bind_token_expire_hours', 'verification_token_expire_minutes', 'max_bind_attempts_per_day']
        for field in time_fields:
            if field in settings:
                value = settings[field]
                if not isinstance(value, (int, float)) or value <= 0:
                    errors.append(f"{field}必须是大于0的数字")

        # 验证布尔配置
        boolean_fields = ['enable_audit_log', 'mask_sensitive_data']
        for field in boolean_fields:
            if field in settings:
                value = settings[field]
                if not isinstance(value, bool):
                    errors.append(f"{field}必须是布尔值")

        # 验证字符串配置
        string_fields = ['default_timezone', 'default_language']
        for field in string_fields:
            if field in settings:
                value = settings[field]
                if not isinstance(value, str) or not value.strip():
                    errors.append(f"{field}不能为空")

        return errors

    def _apply_global_config(self, settings: dict, config: TelegramBotConfig):
        """应用全局配置到设置中"""
        key = config.config_key.replace('global_default_', '')
        value = self._parse_config_value(config.config_value, config.config_type)
        
        # 根据配置分组设置值
        if config.config_group == 'permissions':
            if 'permissions' not in settings:
                settings['permissions'] = {}
            settings['permissions'][key] = value
        elif config.config_group == 'display':
            if 'display_settings' not in settings:
                settings['display_settings'] = {}
            settings['display_settings'][key] = value
        elif config.config_group == 'notification':
            if 'notification_settings' not in settings:
                settings['notification_settings'] = {}
            settings['notification_settings'][key] = value
        elif config.config_group == 'rate_limit':
            if 'permissions' not in settings:
                settings['permissions'] = {}
            if 'rate_limit' not in settings['permissions']:
                settings['permissions']['rate_limit'] = {}
            settings['permissions']['rate_limit'][key] = value
    
    def _parse_config_value(self, value: str, config_type: str) -> Any:
        """解析配置值"""
        if config_type == 'boolean':
            return value.lower() in ('true', '1', 'yes', 'on')
        elif config_type == 'number':
            try:
                return int(value)
            except ValueError:
                try:
                    return float(value)
                except ValueError:
                    return 0
        elif config_type == 'json':
            import json
            try:
                return json.loads(value)
            except json.JSONDecodeError:
                return {}
        else:
            return value
    
    def _deep_merge_settings(self, settings_list: list) -> dict:
        """深度合并多个配置字典"""
        result = {}
        
        for settings in settings_list:
            if not settings:
                continue
                
            for key, value in settings.items():
                if key not in result:
                    result[key] = value
                elif isinstance(value, dict) and isinstance(result[key], dict):
                    result[key] = self._deep_merge_settings([result[key], value])
                else:
                    result[key] = value
        
        return result

    def _validate_permissions(self, permissions: dict) -> List[str]:
        """验证权限配置"""
        errors = []

        # 验证布尔值字段
        bool_fields = ['allow_all_members', 'require_user_verification']
        for field in bool_fields:
            if field in permissions and not isinstance(permissions[field], bool):
                errors.append(f"权限配置 {field} 必须是布尔值")

        # 验证频率限制
        if 'rate_limit' in permissions:
            rate_limit = permissions['rate_limit']
            if 'commands_per_minute' in rate_limit:
                if not isinstance(rate_limit['commands_per_minute'], int) or rate_limit['commands_per_minute'] < 1 or rate_limit['commands_per_minute'] > 60:
                    errors.append("每分钟命令数必须是1-60之间的整数")

            if 'queries_per_hour' in rate_limit:
                if not isinstance(rate_limit['queries_per_hour'], int) or rate_limit['queries_per_hour'] < 1 or rate_limit['queries_per_hour'] > 1000:
                    errors.append("每小时查询数必须是1-1000之间的整数")

        return errors

    def _validate_display_settings(self, display: dict) -> List[str]:
        """验证显示配置"""
        errors = []

        # 验证小数位数
        if 'decimal_places' in display:
            if not isinstance(display['decimal_places'], int) or display['decimal_places'] < 0 or display['decimal_places'] > 4:
                errors.append("小数位数必须是0-4之间的整数")

        # 验证语言代码
        if 'language' in display:
            supported_languages = ['zh-CN', 'zh-TW', 'en-US']
            if display['language'] not in supported_languages:
                errors.append(f"不支持的语言代码，支持的语言：{', '.join(supported_languages)}")

        return errors

    def _validate_notification_settings(self, notification: dict) -> List[str]:
        """验证通知配置"""
        errors = []

        # 验证日报时间格式
        if 'daily_report_time' in notification:
            import re
            time_pattern = r'^([01]?[0-9]|2[0-3]):[0-5][0-9]$'
            if not re.match(time_pattern, notification['daily_report_time']):
                errors.append("日报时间格式必须为 HH:MM")

        return errors

    def _is_cache_valid(self, key: str) -> bool:
        """检查缓存是否有效"""
        if key not in self._cache:
            return False

        cache_time = self._cache[key]['timestamp']
        return time.time() - cache_time < self._cache_expire

    def _set_cache(self, key: str, data: Any):
        """设置缓存"""
        self._cache[key] = {
            'data': data,
            'timestamp': time.time()
        }

    def _clear_merchant_cache(self, merchant_id: int):
        """清除商户相关缓存"""
        keys_to_remove = [
            f"merchant_settings_{merchant_id}",
        ]

        # 清除该商户下所有群组的缓存
        groups = self.db.query(TelegramGroup).filter_by(merchant_id=merchant_id).all()
        for group in groups:
            keys_to_remove.append(f"effective_group_settings_{group.id}")

        for key in keys_to_remove:
            self._cache.pop(key, None)

    def _clear_group_cache(self, group_id: int):
        """清除群组相关缓存"""
        key = f"effective_group_settings_{group_id}"
        self._cache.pop(key, None)

    def clear_all_cache(self):
        """清除所有缓存"""
        self._cache.clear()

    def get_cache_stats(self) -> dict:
        """获取缓存统计信息"""
        return {
            "cache_size": len(self._cache),
            "cache_expire_seconds": self._cache_expire,
            "cached_keys": list(self._cache.keys())
        }
