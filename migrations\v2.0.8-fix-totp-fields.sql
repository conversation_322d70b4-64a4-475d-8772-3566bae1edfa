-- ========================================
-- 修复TOTP相关字段问题
-- 执行时间：2025-06-28
-- ========================================

USE walmart_card_db;

-- 1. 修复users表的totp_secret字段长度
-- 当前VARCHAR(32)不足以存储加密后的数据，需要扩展到VARCHAR(255)
SELECT '修复users表totp_secret字段长度...' AS status;

-- 检查字段是否存在
SET @column_exists = (
    SELECT COUNT(*)
    FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_SCHEMA = DATABASE() 
    AND TABLE_NAME = 'users' 
    AND COLUMN_NAME = 'totp_secret'
);

-- 如果字段存在，修改其长度
SET @sql = IF(@column_exists > 0, 
    'ALTER TABLE users MODIFY COLUMN totp_secret VARCHAR(255) NULL COMMENT ''谷歌验证器密钥(加密存储)''',
    'SELECT ''totp_secret字段不存在，跳过修改'' AS message'
);

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 2. 修复totp_logs表的updated_at字段
-- 检查totp_logs表是否存在
SELECT '修复totp_logs表updated_at字段...' AS status;

SET @table_exists = (
    SELECT COUNT(*)
    FROM INFORMATION_SCHEMA.TABLES 
    WHERE TABLE_SCHEMA = DATABASE() 
    AND TABLE_NAME = 'totp_logs'
);

-- 如果表存在，检查updated_at字段是否存在
SET @updated_at_exists = IF(@table_exists > 0, (
    SELECT COUNT(*)
    FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_SCHEMA = DATABASE() 
    AND TABLE_NAME = 'totp_logs' 
    AND COLUMN_NAME = 'updated_at'
), 0);

-- 如果表存在但updated_at字段不存在，则添加该字段
SET @sql = CASE 
    WHEN @table_exists = 0 THEN 'SELECT ''totp_logs表不存在，跳过修改'' AS message'
    WHEN @updated_at_exists > 0 THEN 'SELECT ''updated_at字段已存在，跳过添加'' AS message'
    ELSE 'ALTER TABLE totp_logs ADD COLUMN updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT ''更新时间'' AFTER created_at'
END;

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 3. 验证修改结果
SELECT '验证修改结果...' AS status;

-- 检查users表的totp_secret字段
SELECT 
    COLUMN_NAME,
    DATA_TYPE,
    CHARACTER_MAXIMUM_LENGTH,
    IS_NULLABLE,
    COLUMN_COMMENT
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() 
    AND TABLE_NAME = 'users' 
    AND COLUMN_NAME = 'totp_secret';

-- 检查totp_logs表的字段
SELECT 
    COLUMN_NAME,
    DATA_TYPE,
    IS_NULLABLE,
    COLUMN_COMMENT
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() 
    AND TABLE_NAME = 'totp_logs' 
    AND COLUMN_NAME IN ('created_at', 'updated_at')
ORDER BY COLUMN_NAME;

SELECT '修复完成！' AS status;
