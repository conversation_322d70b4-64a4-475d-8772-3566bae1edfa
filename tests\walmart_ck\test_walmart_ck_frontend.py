"""
沃尔玛CK前端表单功能测试
使用Playwright自动测试前端表单的各项功能
"""

import sys
import os
import asyncio
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from test.base_playwright_test import BasePlaywrightTest


class WalmartCKFrontendTest(BasePlaywrightTest):
    """沃尔玛CK前端表单测试类"""

    def __init__(self):
        super().__init__()
        self.test_name = "沃尔玛CK前端表单功能测试"

    async def test_super_admin_form_display(self):
        """测试超级管理员表单显示"""
        print("\n=== 测试超级管理员表单显示 ===")

        try:
            # 以超级管理员身份登录
            await self.login_as_admin()
            
            # 导航到CK管理页面
            await self.page.goto(f"{self.base_url}/#/walmart/user")
            await self.page.wait_for_load_state('networkidle')
            
            # 点击新增按钮
            await self.page.click('button:has-text("新增cookie")')
            await self.page.wait_for_load_state('networkidle')
            
            # 验证表单元素存在
            # 商户选择应该是下拉框（如果没有通过右上角选择商户）
            merchant_select = self.page.locator('el-select[placeholder="请选择商户"]')
            if await merchant_select.count() > 0:
                print("✅ 超级管理员显示商户选择下拉框")
            else:
                # 可能是只读输入框（如果已通过右上角选择商户）
                merchant_input = self.page.locator('input[readonly]').first
                if await merchant_input.count() > 0:
                    print("✅ 超级管理员显示商户只读输入框（已选择商户）")
                else:
                    print("❌ 未找到商户选择组件")
                    return False
            
            # 部门选择应该是下拉框
            department_select = self.page.locator('el-select[placeholder="请选择部门"]')
            if await department_select.count() > 0:
                print("✅ 超级管理员显示部门选择下拉框")
            else:
                print("❌ 未找到部门选择下拉框")
                return False
            
            # 验证必填字段标识
            required_fields = await self.page.locator('.el-form-item.is-required').count()
            if required_fields >= 4:  # 至少应该有签名、商户、部门、每日限制等必填字段
                print(f"✅ 找到{required_fields}个必填字段")
            else:
                print(f"❌ 必填字段数量不足，只找到{required_fields}个")
                return False
            
            return True
            
        except Exception as e:
            print(f"❌ 超级管理员表单显示测试失败: {e}")
            return False

    async def test_merchant_admin_form_display(self):
        """测试商户管理员表单显示"""
        print("\n=== 测试商户管理员表单显示 ===")

        try:
            # 以商户管理员身份登录
            await self.login_as_merchant()
            
            # 导航到CK管理页面
            await self.page.goto(f"{self.base_url}/#/walmart/user")
            await self.page.wait_for_load_state('networkidle')
            
            # 点击新增按钮
            await self.page.click('button:has-text("新增cookie")')
            await self.page.wait_for_load_state('networkidle')
            
            # 验证商户字段为只读
            merchant_readonly = self.page.locator('input[readonly]').first
            if await merchant_readonly.count() > 0:
                merchant_value = await merchant_readonly.input_value()
                if merchant_value and merchant_value != "":
                    print(f"✅ 商户管理员显示只读商户字段: {merchant_value}")
                else:
                    print("❌ 商户只读字段为空")
                    return False
            else:
                print("❌ 未找到商户只读字段")
                return False
            
            # 验证部门字段为只读
            department_readonly = self.page.locator('input[readonly]').nth(1)
            if await department_readonly.count() > 0:
                department_value = await department_readonly.input_value()
                if department_value and department_value != "":
                    print(f"✅ 商户管理员显示只读部门字段: {department_value}")
                else:
                    print("❌ 部门只读字段为空")
                    return False
            else:
                print("❌ 未找到部门只读字段")
                return False
            
            return True
            
        except Exception as e:
            print(f"❌ 商户管理员表单显示测试失败: {e}")
            return False

    async def test_form_validation(self):
        """测试表单验证"""
        print("\n=== 测试表单验证 ===")

        try:
            # 以超级管理员身份登录
            await self.login_as_admin()
            
            # 导航到CK管理页面
            await self.page.goto(f"{self.base_url}/#/walmart/user")
            await self.page.wait_for_load_state('networkidle')
            
            # 点击新增按钮
            await self.page.click('button:has-text("新增cookie")')
            await self.page.wait_for_load_state('networkidle')
            
            # 尝试提交空表单
            await self.page.click('button:has-text("保存配置")')
            await self.page.wait_for_timeout(1000)
            
            # 验证错误提示
            error_messages = await self.page.locator('.el-form-item__error').count()
            if error_messages > 0:
                print(f"✅ 表单验证正常，显示{error_messages}个错误提示")
            else:
                print("❌ 表单验证失败，未显示错误提示")
                return False
            
            # 填写无效的签名格式
            await self.page.fill('textarea[placeholder*="签名"]', "invalid_sign_format")
            await self.page.click('button:has-text("保存配置")')
            await self.page.wait_for_timeout(1000)
            
            # 验证签名格式错误提示
            sign_error = self.page.locator('.el-form-item__error:has-text("签名格式")')
            if await sign_error.count() > 0:
                print("✅ 签名格式验证正常")
            else:
                print("❌ 签名格式验证失败")
                return False
            
            return True
            
        except Exception as e:
            print(f"❌ 表单验证测试失败: {e}")
            return False

    async def test_form_submission(self):
        """测试表单提交"""
        print("\n=== 测试表单提交 ===")

        try:
            # 以超级管理员身份登录
            await self.login_as_admin()
            
            # 导航到CK管理页面
            await self.page.goto(f"{self.base_url}/#/walmart/user")
            await self.page.wait_for_load_state('networkidle')
            
            # 点击新增按钮
            await self.page.click('button:has-text("新增cookie")')
            await self.page.wait_for_load_state('networkidle')
            
            # 填写表单
            timestamp = datetime.now().strftime("%Y%m%d%H%M%S")
            test_sign = f"25487f6f129649999ef6b1f269b2a1f{timestamp}@d0e0e25d37720f856a3ba753089b1e47#mmb5Lz2g3i4ilzn/kHXpFg==#26"
            
            await self.page.fill('textarea[placeholder*="签名"]', test_sign)
            
            # 选择商户（如果是下拉框）
            merchant_select = self.page.locator('el-select[placeholder="请选择商户"]')
            if await merchant_select.count() > 0:
                await merchant_select.click()
                await self.page.wait_for_timeout(500)
                # 选择第一个商户
                await self.page.click('.el-select-dropdown__item:first-child')
                await self.page.wait_for_timeout(500)
            
            # 选择部门
            department_select = self.page.locator('el-select[placeholder="请选择部门"]')
            if await department_select.count() > 0:
                await department_select.click()
                await self.page.wait_for_timeout(500)
                # 选择第一个部门
                await self.page.click('.el-select-dropdown__item:first-child')
                await self.page.wait_for_timeout(500)
            
            # 填写描述
            await self.page.fill('textarea[placeholder*="描述"]', f"测试CK-{timestamp}")
            
            # 提交表单
            await self.page.click('button:has-text("保存配置")')
            
            # 等待提交完成
            await self.page.wait_for_timeout(3000)
            
            # 验证是否返回列表页面
            current_url = self.page.url
            if "/walmart/user" in current_url and "/add" not in current_url:
                print("✅ 表单提交成功，已返回列表页面")
                return True
            else:
                print(f"❌ 表单提交后未正确跳转，当前URL: {current_url}")
                return False
            
        except Exception as e:
            print(f"❌ 表单提交测试失败: {e}")
            return False

    async def test_merchant_switch_functionality(self):
        """测试商户切换功能"""
        print("\n=== 测试商户切换功能 ===")

        try:
            # 以超级管理员身份登录
            await self.login_as_admin()
            
            # 导航到CK管理页面
            await self.page.goto(f"{self.base_url}/#/walmart/user")
            await self.page.wait_for_load_state('networkidle')
            
            # 点击新增按钮
            await self.page.click('button:has-text("新增cookie")')
            await self.page.wait_for_load_state('networkidle')
            
            # 检查是否有商户切换器
            merchant_switcher = self.page.locator('.merchant-switcher, .el-dropdown')
            if await merchant_switcher.count() > 0:
                print("✅ 找到商户切换器")
                
                # 这里可以添加更多的商户切换测试逻辑
                # 由于商户切换器的具体实现可能比较复杂，暂时只验证存在性
                return True
            else:
                print("⚠️ 未找到商户切换器（可能在其他位置）")
                return True  # 不作为失败条件
            
        except Exception as e:
            print(f"❌ 商户切换功能测试失败: {e}")
            return False

    async def run_all_tests(self):
        """运行所有测试"""
        print(f"\n{'='*50}")
        print(f"开始执行: {self.test_name}")
        print(f"{'='*50}")

        results = []
        
        try:
            # 启动浏览器
            await self.setup()
            
            # 执行各项测试
            tests = [
                ("超级管理员表单显示", self.test_super_admin_form_display),
                ("商户管理员表单显示", self.test_merchant_admin_form_display),
                ("表单验证", self.test_form_validation),
                ("表单提交", self.test_form_submission),
                ("商户切换功能", self.test_merchant_switch_functionality),
            ]
            
            for test_name, test_func in tests:
                try:
                    result = await test_func()
                    results.append({
                        "test_name": test_name,
                        "status": "PASS" if result else "FAIL",
                        "message": "测试通过" if result else "测试失败"
                    })
                except Exception as e:
                    results.append({
                        "test_name": test_name,
                        "status": "FAIL",
                        "message": f"测试异常: {e}"
                    })
            
        finally:
            # 清理资源
            await self.cleanup()

        # 输出测试结果
        print(f"\n{'='*50}")
        print("测试结果汇总:")
        print(f"{'='*50}")
        
        for result in results:
            status_icon = "✅" if result["status"] == "PASS" else "❌"
            print(f"{status_icon} {result['test_name']}: {result['message']}")
        
        # 统计
        total_tests = len(results)
        passed_tests = sum(1 for r in results if r["status"] == "PASS")
        
        print(f"\n测试完成: {passed_tests}/{total_tests} 通过")
        
        return results


async def main():
    """主函数"""
    test = WalmartCKFrontendTest()
    results = await test.run_all_tests()
    
    # 统计测试结果
    total_tests = len(results)
    passed_tests = sum(1 for result in results if result["status"] == "PASS")
    
    print(f"\n{'='*50}")
    print(f"最终结果: {passed_tests}/{total_tests} 通过")
    print(f"{'='*50}")
    
    # 如果有测试失败，退出码为1
    if passed_tests < total_tests:
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())
