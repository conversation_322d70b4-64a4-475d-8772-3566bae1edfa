# 生产环境分离式部署指南

## 问题分析

当订单量增大时，将所有服务部署在同一台服务器上会导致：

1. **资源竞争**：CPU、内存、磁盘 I/O、网络带宽竞争
2. **性能瓶颈**：单点故障，无法水平扩展
3. **MQ 高 CPU 占用**：RabbitMQ 在高并发下 CPU 占用率飙升
4. **数据库锁竞争**：MySQL 在高并发下锁等待严重

## 解决方案

### 方案一：分离式部署（推荐）

将不同服务部署到不同的服务器上：

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   应用服务器1    │    │   应用服务器2    │    │   应用服务器N    │
│  (App Server)   │    │  (App Server)   │    │  (App Server)   │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         └───────────────────────┼───────────────────────┘
                                 │
    ┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
    │   数据库服务器   │    │   Redis服务器    │    │  RabbitMQ服务器  │
    │  (MySQL 8.0)   │    │  (Redis 7.4)   │    │ (RabbitMQ 3.12) │
    └─────────────────┘    └─────────────────┘    └─────────────────┘
```

### 服务器配置建议

#### 应用服务器（可多台）

- **CPU**: 4 核心以上
- **内存**: 8GB 以上
- **磁盘**: SSD 100GB 以上
- **网络**: 千兆网卡
- **数量**: 根据并发量决定，建议至少 2 台

#### 数据库服务器

- **CPU**: 8 核心以上
- **内存**: 16GB 以上（推荐 32GB）
- **磁盘**: SSD 500GB 以上，RAID 10
- **网络**: 千兆网卡
- **数量**: 1 台主库 + 1 台从库（读写分离）

#### Redis 服务器

- **CPU**: 4 核心以上
- **内存**: 16GB 以上
- **磁盘**: SSD 200GB 以上
- **网络**: 千兆网卡
- **数量**: 1 台主库 + 1 台从库（主从复制）

#### RabbitMQ 服务器

- **CPU**: 8 核心以上
- **内存**: 16GB 以上
- **磁盘**: SSD 200GB 以上
- **网络**: 千兆网卡
- **数量**: 3 台（集群模式，高可用）

## 部署步骤

### 1. 准备环境

```bash
# 在每台服务器上安装Docker和Docker Compose
curl -fsSL https://get.docker.com | sh
sudo usermod -aG docker $USER

# 安装Docker Compose
sudo curl -L "https://github.com/docker/compose/releases/latest/download/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
sudo chmod +x /usr/local/bin/docker-compose
```

### 2. 配置环境变量

```bash
# 复制环境配置文件
cp .env.production.example .env.production

# 编辑配置文件
vim .env.production
```

### 3. 分别部署各个服务

#### 在数据库服务器上：

```bash
# 设置部署开关
export DEPLOY_DATABASE=true
export DEPLOY_REDIS=false
export DEPLOY_RABBITMQ=false
export DEPLOY_APP=false

# 执行部署
./deploy-production.sh
```

#### 在 Redis 服务器上：

```bash
# 设置部署开关
export DEPLOY_DATABASE=false
export DEPLOY_REDIS=true
export DEPLOY_RABBITMQ=false
export DEPLOY_APP=false

# 执行部署
./deploy-production.sh
```

#### 在 RabbitMQ 服务器上：

```bash
# 设置部署开关
export DEPLOY_DATABASE=false
export DEPLOY_REDIS=false
export DEPLOY_RABBITMQ=true
export DEPLOY_APP=false

# 执行部署
./deploy-production.sh
```

#### 在应用服务器上：

```bash
# 设置部署开关
export DEPLOY_DATABASE=false
export DEPLOY_REDIS=false
export DEPLOY_RABBITMQ=false
export DEPLOY_APP=true

# 执行部署
./deploy-production.sh
```

### 4. 负载均衡配置

在应用服务器前面部署 Nginx 负载均衡：

```nginx
upstream walmart_app {
    server ************:20000 weight=1;
    server ************:20000 weight=1;
    server ************:20000 weight=1;
}

server {
    listen 80;
    server_name your-domain.com;

    location / {
        proxy_pass http://walmart_app;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    }
}
```

## 性能优化建议

### RabbitMQ 优化

1. **升级版本**：使用 RabbitMQ 3.12 最新版本
2. **内存配置**：设置合适的内存高水位线
3. **队列优化**：使用 quorum 队列提高性能
4. **集群部署**：3 节点集群提高可用性

### MySQL 优化

1. **连接池**：增加最大连接数到 1000
2. **缓冲池**：设置 innodb_buffer_pool_size 为内存的 70%
3. **日志优化**：调整 innodb_log_file_size
4. **查询缓存**：启用查询缓存

### Redis 优化

1. **内存策略**：设置 maxmemory-policy 为 allkeys-lru
2. **持久化**：使用 AOF + RDB 混合持久化
3. **连接优化**：调整 tcp-keepalive 和 timeout

## 监控和维护

### 监控指标

1. **应用服务器**：CPU、内存、磁盘、网络、响应时间
2. **数据库**：连接数、慢查询、锁等待、缓存命中率
3. **Redis**：内存使用率、命中率、连接数
4. **RabbitMQ**：队列长度、消息速率、内存使用

### 日志管理

```bash
# 查看各服务日志
docker-compose -f docker-compose.production.yml logs -f app
docker-compose -f docker-compose.database.yml logs -f db
docker-compose -f docker-compose.redis.yml logs -f redis
docker-compose -f docker-compose.rabbitmq.yml logs -f rabbitmq
```

## 快速开始

### 一键部署

```bash
# 1. 克隆项目到各个服务器
git clone <your-repo> walmart-bind-card-server
cd walmart-bind-card-server

# 2. 运行快速设置脚本
chmod +x setup-production.sh
./setup-production.sh

# 3. 根据提示选择部署模式
# 选项1: 单机部署（测试环境）
# 选项2-5: 分离式部署（生产环境）
```

### 手动部署

```bash
# 1. 复制环境配置
cp .env.production.example .env.production

# 2. 编辑配置文件
vim .env.production

# 3. 设置权限并部署
chmod +x deploy-production.sh
./deploy-production.sh
```

## 预期效果

分离式部署后预期可以达到：

1. **性能提升**：单机并发能力提升 3-5 倍
2. **可扩展性**：可以根据负载动态增加应用服务器
3. **高可用性**：单个服务故障不影响整体系统
4. **资源利用率**：各服务独享资源，避免竞争
5. **MQ 性能优化**：独立部署后 CPU 占用率降低 60-80%

## 成本估算

以阿里云为例：

- **应用服务器** (2 台): 4 核 8G × 2 = 约 600 元/月
- **数据库服务器** (1 台): 8 核 16G = 约 800 元/月
- **Redis 服务器** (1 台): 4 核 8G = 约 400 元/月
- **RabbitMQ 服务器** (1 台): 4 核 8G = 约 400 元/月
- **负载均衡器**: 约 100 元/月

**总计**: 约 2300 元/月（相比单机部署增加约 1800 元/月）

投入产出比：性能提升 3-5 倍，成本增加约 2 倍，ROI 非常高。

## 故障排查

### 常见问题

1. **RabbitMQ CPU 占用高**

   ```bash
   # 检查队列积压
   docker exec rabbitmq rabbitmqctl list_queues

   # 检查连接数
   docker exec rabbitmq rabbitmqctl list_connections

   # 重启RabbitMQ
   docker-compose -f docker-compose.rabbitmq.yml restart
   ```

2. **数据库连接超时**

   ```bash
   # 检查连接数
   docker exec mysql mysql -u root -p -e "SHOW PROCESSLIST;"

   # 检查慢查询
   docker exec mysql mysql -u root -p -e "SHOW VARIABLES LIKE 'slow_query_log';"
   ```

3. **Redis 内存不足**

   ```bash
   # 检查内存使用
   docker exec redis redis-cli INFO memory

   # 清理过期键
   docker exec redis redis-cli FLUSHDB
   ```
