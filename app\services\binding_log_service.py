from typing import Dict, Any, List, Optional, Tuple, Union
from sqlalchemy.orm import Session
from sqlalchemy.ext.asyncio import AsyncSession
from app.crud.binding_log import binding_log
from app.models.binding_log import BindingLog, LogType, LogLevel
from app.schemas.binding_log import BindingLogCreate, BindingLogUpdate
from app.services.base_service import BaseService
from app.core.logging import get_logger
from app.utils.time_utils import get_current_time, datetime_to_isoformat
from app.utils.response_data_handler import response_data_handler

logger = get_logger("binding_log_service")


class BindingLogService(BaseService[BindingLog, BindingLogCreate, BindingLogUpdate]):
    """绑卡日志服务 - 【安全修复】继承BaseService实现数据隔离"""

    def __init__(self, db: Union[Session, AsyncSession]):
        super().__init__(BindingLog, db)

    def apply_data_isolation(self, query, current_user):
        """
        【安全修复】重写基类方法，添加强制商户隔离
        应用数据隔离规则 - 支持用户级、部门级、商户级权限，确保绑卡日志数据的商户隔离

        Args:
            query: SQLAlchemy查询对象
            current_user: 当前用户

        Returns:
            query: 应用隔离规则后的查询对象
        """
        # 【安全修复】：强制商户隔离 - 除超级管理员外，所有用户只能访问自己商户的绑卡日志
        if not current_user.is_superuser:
            if not current_user.merchant_id:
                # 如果用户没有商户ID，返回空结果
                self.logger.warning(f"[SECURITY] 用户 {current_user.id} 没有商户ID，拒绝绑卡日志访问")
                query = query.filter(BindingLog.id == 'impossible_id')  # 强制返回空结果
                return query

            # 强制添加商户过滤，确保无法跨商户访问
            query = query.filter(BindingLog.merchant_id == current_user.merchant_id)
            self.logger.info(f"[SECURITY] 强制商户隔离：用户 {current_user.id} 只能访问商户 {current_user.merchant_id} 的绑卡日志")

        # 然后应用基类的数据隔离逻辑
        return super().apply_data_isolation(query, current_user)

    def _get_merchant_and_department_ids(self, card_record_id: str) -> Tuple[Optional[int], Optional[int]]:
        """
        【安全修复】从卡记录获取商户ID和部门ID，确保日志数据正确关联（同步版本）

        Args:
            card_record_id: 卡记录ID

        Returns:
            Tuple[merchant_id, department_id]: 商户ID和部门ID
        """
        try:
            from app.models.card_record import CardRecord
            from sqlalchemy.ext.asyncio import AsyncSession

            if isinstance(self.db, AsyncSession):
                # 异步会话 - 不应该调用此方法
                self.logger.error(f"[SECURITY] 异步会话不能调用同步方法，请使用 _get_merchant_and_department_ids_async")
                return None, None
            else:
                # 同步会话
                card_record = self.db.query(CardRecord).filter(CardRecord.id == card_record_id).first()
                if card_record:
                    return card_record.merchant_id, card_record.department_id
                else:
                    self.logger.warning(f"[SECURITY] 未找到卡记录 {card_record_id}，无法获取商户和部门信息")
                    return None, None
        except Exception as e:
            self.logger.error(f"[SECURITY] 获取卡记录商户信息失败: {e}")
            return None, None

    def _get_merchant_and_department_ids_with_session(self, db: Session, card_record_id: str) -> Tuple[Optional[int], Optional[int]]:
        """
        【安全修复】从卡记录获取商户ID和部门ID，使用指定的同步会话

        Args:
            db: 同步数据库会话
            card_record_id: 卡记录ID

        Returns:
            Tuple[merchant_id, department_id]: 商户ID和部门ID
        """
        try:
            from app.models.card_record import CardRecord
            from sqlalchemy.ext.asyncio import AsyncSession

            if isinstance(db, AsyncSession):
                self.logger.error(f"[SECURITY] 传入的是异步会话，请使用 _get_merchant_and_department_ids_async_with_session")
                return None, None

            # 同步会话
            card_record = db.query(CardRecord).filter(CardRecord.id == card_record_id).first()
            if card_record:
                return card_record.merchant_id, card_record.department_id
            else:
                self.logger.warning(f"[SECURITY] 未找到卡记录 {card_record_id}，无法获取商户和部门信息")
                return None, None
        except Exception as e:
            self.logger.error(f"[SECURITY] 获取卡记录商户信息失败: {e}")
            return None, None

    async def _get_merchant_and_department_ids_async_with_session(self, db: AsyncSession, card_record_id: str) -> Tuple[Optional[int], Optional[int]]:
        """
        【安全修复】从卡记录获取商户ID和部门ID，使用指定的异步会话

        Args:
            db: 异步数据库会话
            card_record_id: 卡记录ID

        Returns:
            Tuple[merchant_id, department_id]: 商户ID和部门ID
        """
        try:
            from app.models.card_record import CardRecord
            from sqlalchemy.ext.asyncio import AsyncSession
            from sqlalchemy import select

            if not isinstance(db, AsyncSession):
                self.logger.error(f"[SECURITY] 传入的不是异步会话，请使用 _get_merchant_and_department_ids_with_session")
                return None, None

            # 异步会话
            stmt = select(CardRecord).where(CardRecord.id == card_record_id)
            result = await db.execute(stmt)
            card_record = result.scalar_one_or_none()

            if card_record:
                return card_record.merchant_id, card_record.department_id
            else:
                self.logger.warning(f"[SECURITY] 未找到卡记录 {card_record_id}，无法获取商户和部门信息")
                return None, None
        except Exception as e:
            self.logger.error(f"[SECURITY] 获取卡记录商户信息失败: {e}")
            return None, None

    async def _get_merchant_and_department_ids_async(self, card_record_id: str) -> Tuple[Optional[int], Optional[int]]:
        """
        【安全修复】从卡记录获取商户ID和部门ID，确保日志数据正确关联（异步版本）

        Args:
            card_record_id: 卡记录ID

        Returns:
            Tuple[merchant_id, department_id]: 商户ID和部门ID
        """
        try:
            from app.models.card_record import CardRecord
            from sqlalchemy.ext.asyncio import AsyncSession
            from sqlalchemy import select

            if isinstance(self.db, AsyncSession):
                # 异步会话
                stmt = select(CardRecord).where(CardRecord.id == card_record_id)
                result = await self.db.execute(stmt)
                card_record = result.scalar_one_or_none()

                if card_record:
                    return card_record.merchant_id, card_record.department_id
                else:
                    self.logger.warning(f"[SECURITY] 未找到卡记录 {card_record_id}，无法获取商户和部门信息")
                    return None, None
            else:
                # 同步会话 - 调用同步版本
                return self._get_merchant_and_department_ids(card_record_id)
        except Exception as e:
            self.logger.error(f"[SECURITY] 获取卡记录商户信息失败: {e}")
            return None, None

    async def _create_log_with_isolation(
        self,
        db: Union[Session, AsyncSession],
        card_record_id: str,
        log_type: str,
        log_level: str,
        message: str,
        **kwargs
    ) -> None:
        """
        【安全修复】创建日志记录，自动添加商户和部门信息确保数据隔离

        Args:
            db: 数据库会话
            card_record_id: 卡记录ID
            log_type: 日志类型
            log_level: 日志级别
            message: 日志消息
            **kwargs: 其他日志字段
        """
        try:
            # 【安全修复】获取商户和部门信息（支持异步）
            from sqlalchemy.ext.asyncio import AsyncSession
            if isinstance(db, AsyncSession):
                # 使用传入的异步会话
                merchant_id, department_id = await self._get_merchant_and_department_ids_async_with_session(db, card_record_id)
            else:
                # 使用传入的同步会话
                merchant_id, department_id = self._get_merchant_and_department_ids_with_session(db, card_record_id)

            binding_log.create_log(
                db=db,
                card_record_id=card_record_id,
                log_type=log_type,
                log_level=log_level,
                message=message,
                merchant_id=merchant_id,  # 【安全修复】自动添加商户ID
                department_id=department_id,  # 【安全修复】自动添加部门ID
                **kwargs
            )
        except Exception as e:
            logger.error(f"创建日志记录失败: {str(e)}", exc_info=True)

    def _process_response_data(
        self,
        response_data: Optional[Dict[str, Any]],
        context: str = "unknown"
    ) -> Tuple[Optional[Dict[str, Any]], Optional[str]]:
        """
        处理响应数据，确保符合数据库存储要求

        Args:
            response_data: 原始响应数据
            context: 上下文信息，用于日志记录

        Returns:
            Tuple[处理后的响应数据, 警告信息]
        """
        if not response_data:
            return None, None

        try:
            processed_data, warning = response_data_handler.process_response_data(response_data, context)

            # 如果有警告信息，记录到日志
            if warning:
                logger.warning(f"响应数据处理警告: {warning} | context: {context}")

            return processed_data, warning

        except Exception as e:
            logger.error(f"处理响应数据时发生错误: {e} | context: {context}")
            # 返回None以避免数据库写入失败
            return None, f"响应数据处理失败: {str(e)}"

    async def log_api_request(
        self,
        db: Union[Session, AsyncSession],
        card_record_id: str,
        request_data: Dict[str, Any],
        ip_address: Optional[str] = None,
        message: str = "API请求",
    ) -> None:
        """记录API请求日志"""
        try:
            # 【安全修复】使用新的安全日志创建方法
            await self._create_log_with_isolation(
                db=db,
                card_record_id=card_record_id,
                log_type=LogType.API_REQUEST,
                log_level=LogLevel.INFO,
                message=message,
                request_data=self._sanitize_sensitive_data(request_data),
                ip_address=ip_address,
            )
        except Exception as e:
            logger.error(f"记录API请求日志失败: {str(e)}", exc_info=True)

    async def log_api_response(
        self,
        db: Union[Session, AsyncSession],
        card_record_id: str,
        response_data: Dict[str, Any],
        duration_ms: float,
        message: str = "API响应",
    ) -> None:
        """记录API响应日志"""
        try:
            # 【新增】处理响应数据大小限制
            processed_response, warning = self._process_response_data(
                response_data, f"api_response_{card_record_id}"
            )

            # 如果有警告，添加到消息中
            if warning:
                message = f"{message} [数据处理: {warning}]"

            # 【安全修复】使用新的安全日志创建方法
            await self._create_log_with_isolation(
                db=db,
                card_record_id=card_record_id,
                log_type=LogType.API_RESPONSE,
                log_level=LogLevel.INFO,
                message=message,
                response_data=processed_response,
                duration_ms=duration_ms,
            )
        except Exception as e:
            logger.error(f"记录API响应日志失败: {str(e)}", exc_info=True)
            # 【新增】容错处理：如果因为数据过大导致失败，尝试保存最小化版本
            try:
                fallback_response = {
                    "_fallback": True,
                    "_error": "原始数据保存失败",
                    "_original_error": str(e),
                    "status": response_data.get("status") if response_data else None,
                    "error": str(response_data.get("error", ""))[:200] if response_data else None,
                }

                binding_log.create_log(
                    db=db,
                    card_record_id=card_record_id,
                    log_type=LogType.API_RESPONSE,
                    log_level=LogLevel.WARNING,
                    message=f"{message} [降级保存]",
                    response_data=fallback_response,
                    duration_ms=duration_ms,
                )
                logger.warning(f"API响应日志降级保存成功 | card_record_id: {card_record_id}")
            except Exception as fallback_error:
                logger.error(f"API响应日志降级保存也失败: {fallback_error}")
                # 最后的容错：不保存响应数据，只保存基本信息
                try:
                    binding_log.create_log(
                        db=db,
                        card_record_id=card_record_id,
                        log_type=LogType.API_RESPONSE,
                        log_level=LogLevel.ERROR,
                        message=f"{message} [仅基本信息]",
                        response_data=None,
                        duration_ms=duration_ms,
                    )
                except Exception as final_error:
                    logger.error(f"API响应日志最终保存失败: {final_error}")

    async def log_walmart_request(
        self,
        db: Union[Session, AsyncSession],
        card_record_id: str,
        request_data: Dict[str, Any],
        walmart_ck_id: str,
        attempt_number: str,
        message: str = "沃尔玛API请求",
    ) -> None:
        """记录沃尔玛API请求日志"""
        try:
            # 【安全修复】使用新的安全日志创建方法
            await self._create_log_with_isolation(
                db=db,
                card_record_id=card_record_id,
                log_type=LogType.WALMART_REQUEST,
                log_level=LogLevel.INFO,
                message=message,
                request_data=self._sanitize_sensitive_data(request_data),
                walmart_ck_id=walmart_ck_id,
                attempt_number=attempt_number,
            )
        except Exception as e:
            logger.error(f"记录沃尔玛API请求日志失败: {str(e)}", exc_info=True)

    async def log_walmart_response(
        self,
        db: Union[Session, AsyncSession],
        card_record_id: str,
        response_data: Dict[str, Any],
        duration_ms: float,
        walmart_ck_id: str,
        attempt_number: str,
        message: str = "沃尔玛API响应",
    ) -> None:
        """记录沃尔玛API响应日志"""
        try:
            # 【新增】处理响应数据大小限制
            processed_response, warning = self._process_response_data(
                response_data, f"walmart_response_{card_record_id}_{attempt_number}"
            )

            # 根据响应确定日志级别
            log_level = LogLevel.INFO
            if response_data and not response_data.get("status", False):
                if response_data.get("error"):
                    log_level = LogLevel.ERROR
                    message = f"沃尔玛API错误: {response_data.get('error', {}).get('message', '未知错误')}"

            # 如果有警告，添加到消息中
            if warning:
                message = f"{message} [数据处理: {warning}]"

            # 【安全修复】使用新的安全日志创建方法
            await self._create_log_with_isolation(
                db=db,
                card_record_id=card_record_id,
                log_type=LogType.WALMART_RESPONSE,
                log_level=log_level,
                message=message,
                response_data=processed_response,
                duration_ms=duration_ms,
                walmart_ck_id=walmart_ck_id,
                attempt_number=attempt_number,
            )
        except Exception as e:
            logger.error(f"记录沃尔玛API响应日志失败: {str(e)}", exc_info=True)
            # 【新增】容错处理：保存关键信息
            try:
                fallback_response = {
                    "_fallback": True,
                    "_error": "原始数据保存失败",
                    "_original_error": str(e),
                }

                # 尝试保留关键字段
                if response_data:
                    for key in ["status", "error", "errorcode", "logId", "message"]:
                        if key in response_data:
                            try:
                                fallback_response[key] = str(response_data[key])[:200]
                            except:
                                fallback_response[key] = "[无法序列化]"

                binding_log.create_log(
                    db=db,
                    card_record_id=card_record_id,
                    log_type=LogType.WALMART_RESPONSE,
                    log_level=LogLevel.WARNING,
                    message=f"{message} [降级保存]",
                    response_data=fallback_response,
                    duration_ms=duration_ms,
                    walmart_ck_id=walmart_ck_id,
                    attempt_number=attempt_number,
                )
                logger.warning(f"沃尔玛API响应日志降级保存成功 | card_record_id: {card_record_id}")
            except Exception as fallback_error:
                logger.error(f"沃尔玛API响应日志降级保存失败: {fallback_error}")

    async def log_retry(
        self,
        db: Union[Session, AsyncSession],
        card_record_id: str,
        attempt_number: str,
        reason: str,
        details: Optional[Dict[str, Any]] = None,
        walmart_ck_id: Optional[str] = None,
    ) -> None:
        """记录重试操作日志"""
        try:
            binding_log.create_log(
                db=db,
                card_record_id=card_record_id,
                log_type=LogType.RETRY,
                log_level=LogLevel.WARNING,
                message=f"重试操作 #{attempt_number}: {reason}",
                details=details,
                attempt_number=attempt_number,
                walmart_ck_id=walmart_ck_id,
            )
        except Exception as e:
            logger.error(f"记录重试操作日志失败: {str(e)}", exc_info=True)

    async def log_error(
        self,
        db: Union[Session, AsyncSession],
        card_record_id: str,
        error_message: str,
        details: Optional[Dict[str, Any]] = None,
        attempt_number: Optional[str] = None,
        walmart_ck_id: Optional[str] = None,
        error_source: Optional[str] = None,  # 新增：错误来源标识
        raw_response: Optional[Dict[str, Any]] = None,  # 新增：原始响应数据
    ) -> None:
        """记录错误日志"""
        try:
            # 【修复】根据错误来源使用正确的日志类型和消息前缀
            if error_source == "walmart_api":
                message_prefix = "沃尔玛响应"
                log_type = LogType.WALMART_RESPONSE  # 【修复】使用正确的日志类型
            elif error_source == "system":
                message_prefix = "系统错误"
                log_type = LogType.ERROR
            else:
                # 根据错误消息内容智能判断来源
                if any(keyword in error_message.lower() for keyword in ["请先去登录", "errorcode", "logid", "沃尔玛"]):
                    message_prefix = "沃尔玛响应"
                    log_type = LogType.WALMART_RESPONSE  # 【修复】使用正确的日志类型
                else:
                    message_prefix = "系统错误"
                    log_type = LogType.ERROR

            formatted_message = f"{message_prefix}: {error_message}"

            # 【新增】处理原始响应数据大小限制
            processed_response, warning = self._process_response_data(
                raw_response, f"error_response_{card_record_id}"
            )

            # 如果有警告，添加到details中
            if warning and details:
                details["_response_data_warning"] = warning

            # 【修复】使用正确的日志类型和保存原始响应数据
            binding_log.create_log(
                db=db,
                card_record_id=card_record_id,
                log_type=log_type,  # 【修复】使用动态确定的日志类型
                log_level=LogLevel.ERROR,
                message=formatted_message,
                details=details,
                response_data=processed_response,  # 【修复】保存处理后的响应数据
                attempt_number=attempt_number,
                walmart_ck_id=int(walmart_ck_id) if walmart_ck_id and str(walmart_ck_id).isdigit() else None,
            )
        except Exception as e:
            logger.error(f"记录错误日志失败: {str(e)}", exc_info=True)

    async def log_status_change(
        self,
        db: Union[Session, AsyncSession],
        card_record_id: str,
        old_status: str,
        new_status: str,
        details: Optional[Dict[str, Any]] = None,
        walmart_ck_id: Optional[int] = None,  # 【修复】新增walmart_ck_id参数
    ) -> None:
        """记录状态变更日志"""
        try:
            binding_log.create_log(
                db=db,
                card_record_id=card_record_id,
                log_type=LogType.STATUS_CHANGE,
                log_level=LogLevel.INFO,
                message=f"状态变更: {old_status} -> {new_status}",
                details=details,
                walmart_ck_id=walmart_ck_id,  # 【修复】传递walmart_ck_id
            )
        except Exception as e:
            logger.error(f"记录状态变更日志失败: {str(e)}", exc_info=True)

    async def log_callback(
        self,
        db: Union[Session, AsyncSession],
        card_record_id: str,
        success: bool,
        details: Dict[str, Any],
        attempt_number: Optional[str] = None,
    ) -> None:
        """记录回调操作日志"""
        try:
            log_level = LogLevel.INFO if success else LogLevel.ERROR
            message = "回调成功" if success else f"回调失败: {details.get('error', '未知错误')}"
            
            binding_log.create_log(
                db=db,
                card_record_id=card_record_id,
                log_type=LogType.CALLBACK,
                log_level=log_level,
                message=message,
                details=details,
                attempt_number=attempt_number,
            )
        except Exception as e:
            logger.error(f"记录回调操作日志失败: {str(e)}", exc_info=True)

    async def log_system(
        self,
        db: Union[Session, AsyncSession],
        card_record_id: str,
        message: str,
        log_level: LogLevel = LogLevel.INFO,
        details: Optional[Dict[str, Any]] = None,
    ) -> None:
        """记录系统操作日志"""
        try:
            # 【安全修复】使用新的安全日志创建方法
            await self._create_log_with_isolation(
                db=db,
                card_record_id=card_record_id,
                log_type=LogType.SYSTEM,
                log_level=log_level,
                message=message,
                details=details,
            )
        except Exception as e:
            logger.error(f"记录系统操作日志失败: {str(e)}", exc_info=True)

    async def log_bind_attempt_start(
        self,
        db: Union[Session, AsyncSession],
        card_record_id: str,
        walmart_ck_id: int,
        attempt_number: int,
        card_number: str,
        details: Optional[Dict[str, Any]] = None,
    ) -> None:
        """记录绑卡尝试开始日志"""
        try:
            message = f"开始绑卡尝试 #{attempt_number} | CK_ID={walmart_ck_id} | 卡号={card_number[:6]}***"

            attempt_details = {
                "walmart_ck_id": walmart_ck_id,
                "attempt_number": attempt_number,
                "card_number_masked": f"{card_number[:6]}***",
                "attempt_start_time": datetime_to_isoformat(get_current_time()),
            }

            if details:
                attempt_details.update(details)

            binding_log.create_log(
                db=db,
                card_record_id=card_record_id,
                log_type=LogType.WALMART_REQUEST,
                log_level=LogLevel.INFO,
                message=message,
                details=attempt_details,
                attempt_number=str(attempt_number),
                walmart_ck_id=walmart_ck_id,
            )
        except Exception as e:
            logger.error(f"记录绑卡尝试开始日志失败: {str(e)}", exc_info=True)

    async def log_bind_attempt_result(
        self,
        db: Union[Session, AsyncSession],
        card_record_id: str,
        walmart_ck_id: int,
        attempt_number: int,
        success: bool,
        error_code: Optional[str] = None,
        error_message: Optional[str] = None,
        duration_ms: Optional[float] = None,
        details: Optional[Dict[str, Any]] = None,
    ) -> None:
        """记录绑卡尝试结果日志"""
        try:
            if success:
                message = f"绑卡尝试 #{attempt_number} 成功 | CK_ID={walmart_ck_id}"
                log_level = LogLevel.INFO
            else:
                # 【修复】改进失败消息格式，区分错误来源
                error_source = details.get("error_source") if details else None
                if error_source == "walmart_api":
                    error_prefix = "沃尔玛响应"
                elif error_source == "system":
                    error_prefix = "系统错误"
                else:
                    # 智能判断错误来源
                    if error_message and any(keyword in error_message.lower() for keyword in ["请先去登录", "errorcode", "logid", "沃尔玛"]):
                        error_prefix = "沃尔玛响应"
                    else:
                        error_prefix = "系统错误"

                # 【修复】改进错误消息格式，避免重复的"绑卡尝试失败"前缀
                clean_error_message = error_message or '未知错误'
                if clean_error_message.startswith("绑卡尝试失败: "):
                    clean_error_message = clean_error_message[7:].strip()  # 移除"绑卡尝试失败: "前缀并去除空格
                elif clean_error_message.startswith("系统错误: "):
                    clean_error_message = clean_error_message[5:].strip()  # 移除"系统错误: "前缀并去除空格

                message = f"绑卡尝试 #{attempt_number} 失败 | CK_ID={walmart_ck_id} | {error_prefix}={clean_error_message}"
                log_level = LogLevel.ERROR

            result_details = {
                "walmart_ck_id": walmart_ck_id,
                "attempt_number": attempt_number,
                "success": success,
                "error_code": error_code,
                "error_message": error_message,
                "duration_ms": duration_ms,
                "attempt_end_time": datetime_to_isoformat(get_current_time()),
            }

            if details:
                result_details.update(details)

            # 【关键修复】使用专门的绑卡尝试日志类型，避免与沃尔玛API响应混淆
            log_type = LogType.BIND_ATTEMPT  # 统一使用绑卡尝试日志类型

            binding_log.create_log(
                db=db,
                card_record_id=card_record_id,
                log_type=log_type,
                log_level=log_level,
                message=message,
                details=result_details,
                duration_ms=duration_ms,
                attempt_number=str(attempt_number),
                walmart_ck_id=walmart_ck_id,
            )
        except Exception as e:
            logger.error(f"记录绑卡尝试结果日志失败: {str(e)}", exc_info=True)

    async def log_ck_usage_tracking(
        self,
        db: Union[Session, AsyncSession],
        card_record_id: str,
        walmart_ck_id: int,
        action: str,  # 'selected', 'validation_failed', 'bind_attempt', 'disabled'
        reason: Optional[str] = None,
        details: Optional[Dict[str, Any]] = None,
    ) -> None:
        """记录CK使用追踪日志"""
        try:
            action_messages = {
                'selected': f"选择CK {walmart_ck_id} 用于绑卡",
                'validation_failed': f"CK {walmart_ck_id} 验证失败: {reason or '未知原因'}",
                'bind_attempt': f"使用CK {walmart_ck_id} 进行绑卡尝试",
                'disabled': f"CK {walmart_ck_id} 已被禁用: {reason or '未知原因'}",
            }

            message = action_messages.get(action, f"CK {walmart_ck_id} 操作: {action}")
            log_level = LogLevel.WARNING if action in ['validation_failed', 'disabled'] else LogLevel.INFO

            tracking_details = {
                "walmart_ck_id": walmart_ck_id,
                "action": action,
                "reason": reason,
                "tracking_time": datetime_to_isoformat(get_current_time()),
            }

            if details:
                tracking_details.update(details)

            binding_log.create_log(
                db=db,
                card_record_id=card_record_id,
                log_type=LogType.SYSTEM,
                log_level=log_level,
                message=message,
                details=tracking_details,
                walmart_ck_id=walmart_ck_id,
            )
        except Exception as e:
            logger.error(f"记录CK使用追踪日志失败: {str(e)}", exc_info=True)

    async def get_logs_by_card_record_id(
        self, db: Union[Session, AsyncSession], card_record_id: str, skip: int = 0, limit: int = 100
    ) -> List[Dict[str, Any]]:
        """获取卡记录的日志列表"""
        logs = binding_log.get_by_card_record_id(db, card_record_id, skip, limit)
        return [log.to_dict() for log in logs]

    async def count_logs_by_card_record_id(self, db: Union[Session, AsyncSession], card_record_id: str) -> int:
        """获取卡记录的日志数量"""
        return binding_log.count_by_card_record_id(db, card_record_id)

    def _sanitize_sensitive_data(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """清理敏感数据"""
        if not data:
            return {}
            
        # 创建数据的副本，避免修改原始数据
        sanitized = data.copy()
        
        # 敏感字段列表
        sensitive_fields = ["card_password", "cardPwd", "password", "api_secret", "token"]
        
        # 遍历字典，替换敏感字段
        for key in sanitized:
            if key.lower() in [field.lower() for field in sensitive_fields]:
                sanitized[key] = "******"
            elif isinstance(sanitized[key], dict):
                # 递归处理嵌套字典
                sanitized[key] = self._sanitize_sensitive_data(sanitized[key])
                
        return sanitized


# 【安全修复】移除全局实例化，改为在需要时创建实例
# 由于BindingLogService现在继承BaseService，需要db参数，不能全局实例化
# 使用时请通过 BindingLogService(db) 创建实例
