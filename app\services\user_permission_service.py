"""
用户权限和角色管理服务
专门处理用户权限、角色相关的业务逻辑
"""

from typing import Any, Optional, List, Dict, Set
from sqlalchemy.orm import Session
from fastapi import HTTPException, status
from sqlalchemy import text

from app import crud, models
from app.models.user import User
from app.core.auth import auth_service
from app.core.logging import get_logger
from app.core.cache_decorators import cache_user_data, invalidate_user_cache

logger = get_logger(__name__)


class UserPermissionService:
    """用户权限和角色管理服务"""
    
    def __init__(self, db: Session):
        self.db = db
    
    def get_user_me_info(self, current_user: User) -> Dict[str, Any]:
        """
        获取当前用户完整信息（包含权限、角色、菜单）
        
        Args:
            current_user: 当前用户
            
        Returns:
            Dict: 用户完整信息
        """
        try:
            # 获取完整的用户信息（包含角色关系）
            full_user = crud.user.get_user_with_permissions(self.db, current_user.id)
            if not full_user:
                full_user = current_user
            
            # 获取用户权限
            permissions = auth_service.get_user_permissions(full_user, self.db)
            
            # 获取数据权限范围
            data_scope = auth_service.get_user_data_scope(full_user, self.db)
            
            # 获取用户菜单
            user_menus = self._get_user_menus(current_user)
            menu_codes = self._extract_menu_codes(user_menus)
            
            # 获取用户角色信息
            user_roles = self._get_user_roles_info(full_user)
            
            # 构建返回数据
            user_data = {
                "id": full_user.id,
                "username": full_user.username,
                "email": full_user.email,
                "full_name": full_user.full_name,
                "roles": user_roles,
                "is_superuser": full_user.is_superuser,
                "merchant_id": full_user.merchant_id,
                "is_active": full_user.is_active,
                "phone": full_user.phone,
                "remark": full_user.remark,
                "created_at": full_user.created_at,
                "updated_at": full_user.updated_at,
                "permissions": permissions,
                "data_scope": data_scope,
                "menus": list(set(menu_codes))  # 去重
            }
            
            return user_data
            
        except Exception as e:
            logger.error(f"获取用户信息失败: {e}")
            import traceback
            logger.error(f"详细错误信息: {traceback.format_exc()}")
            
            # 返回基础用户信息
            return self._get_fallback_user_info(current_user)
    
    @cache_user_data(ttl=1800)  # 缓存30分钟
    def get_user_permissions(self, current_user: User) -> Dict[str, Any]:
        """
        获取当前用户的所有权限代码 - 优化N+1查询 + 缓存

        Args:
            current_user: 当前用户

        Returns:
            Dict: 包含权限列表的字典
        """
        permission_codes: Set[str] = set()

        # 如果用户是超级管理员，赋予所有权限
        if current_user.is_superuser:
            # 使用批量查询获取所有权限
            all_perms = self.db.query(models.Permission).all()
            permission_codes.update(perm.code for perm in all_perms)
            permission_codes.add("*:*:*")
            return {"permissions": sorted(list(permission_codes))}

        # 使用预加载获取用户及其关联数据，避免N+1查询
        user_with_relations = self._get_user_with_preloaded_relations(current_user.id)
        if not user_with_relations:
            return {"permissions": []}

        # 1. 直接分配给用户的权限
        if user_with_relations.permissions:
            permission_codes.update(perm.code for perm in user_with_relations.permissions)

        # 2. 通过角色获取的权限 - 批量查询避免N+1
        if user_with_relations.roles:
            role_codes = [role.code for role in user_with_relations.roles]
            role_permissions = self._get_batch_role_permissions(role_codes)
            permission_codes.update(role_permissions)

        return {"permissions": sorted(list(permission_codes))}
    
    def get_user_roles(self, user_id: int, current_user: User) -> Dict[str, Any]:
        """
        获取用户角色列表 - 优化N+1查询

        Args:
            user_id: 用户ID
            current_user: 当前用户

        Returns:
            Dict: 用户角色信息
        """
        # 使用预加载获取用户及其角色，避免N+1查询
        user = self._get_user_with_preloaded_relations(user_id)
        if not user:
            raise HTTPException(
                status_code=404,
                detail="用户不存在",
            )

        # 数据权限检查：只能查看有权限访问的用户
        if current_user.id != user_id:
            if not auth_service.can_access_merchant_data(current_user, user.merchant_id, self.db):
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail="只能查看自己商家的用户角色",
                )

        # 获取用户角色 - 数据已预加载，无N+1查询
        user_roles = []
        if user.roles:
            user_roles = [
                {
                    "id": role.id,
                    "name": role.name,
                    "code": role.code,
                    "description": role.description,
                    "is_enabled": role.is_enabled
                }
                for role in user.roles
            ]

        return {
            "user_id": user_id,
            "username": user.username,
            "roles": user_roles
        }
    
    @invalidate_user_cache(user_id_param='user_id')  # 更新后清除用户缓存
    def assign_roles_to_user(self, user_id: int, role_ids: List[int], current_user: User) -> Dict[str, Any]:
        """
        为用户分配角色 - 优化N+1查询 + 自动缓存失效

        Args:
            user_id: 用户ID
            role_ids: 角色ID列表
            current_user: 当前用户

        Returns:
            Dict: 分配结果
        """
        user = crud.user.get(self.db, id=user_id)
        if not user:
            raise HTTPException(
                status_code=404,
                detail="用户不存在",
            )

        # 不能修改超级管理员的角色
        if user.is_superuser:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="不能修改超级管理员的角色",
            )

        # 数据权限检查：只能管理有权限访问的用户
        if not auth_service.can_access_merchant_data(current_user, user.merchant_id, self.db):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="只能管理自己商家的用户角色",
            )

        # 批量获取角色对象，避免N+1查询
        from app.models.role import Role
        roles = self.db.query(Role).filter(Role.id.in_(role_ids)).all()

        # 检查所有角色是否存在
        if len(roles) != len(role_ids):
            raise HTTPException(
                status_code=404,
                detail="部分角色不存在",
            )

        # 分配角色
        user.roles = roles
        self.db.commit()

        # 重新获取用户及其角色，确保数据一致性
        updated_user = self._get_user_with_preloaded_relations(user_id)

        # 返回更新后的角色列表
        user_roles = [
            {
                "id": role.id,
                "name": role.name,
                "code": role.code,
                "description": role.description
            }
            for role in updated_user.roles
        ] if updated_user and updated_user.roles else []

        return {
            "user_id": user_id,
            "message": "角色分配成功",
            "roles": user_roles
        }
    
    def update_user_roles(self, user_id: int, role_ids: List[int], current_user: User) -> Dict[str, Any]:
        """
        更新用户角色（PUT方式）
        
        Args:
            user_id: 用户ID
            role_ids: 角色ID列表
            current_user: 当前用户
            
        Returns:
            Dict: 更新结果
        """
        # 复用分配角色的逻辑
        result = self.assign_roles_to_user(user_id, role_ids, current_user)
        result["message"] = "角色更新成功"
        return result
    
    def _get_user_menus(self, current_user: User) -> List[Dict[str, Any]]:
        """获取用户菜单"""
        try:
            from app.services.menu_service import menu_service
            return menu_service.get_user_menus(self.db, current_user)
        except Exception as e:
            logger.error(f"获取用户菜单失败: {e}")
            return []
    
    def _extract_menu_codes(self, user_menus: List[Dict[str, Any]]) -> List[str]:
        """提取菜单代码"""
        menu_codes = []
        for menu in user_menus:
            menu_codes.append(menu.get('code'))
            # 递归获取子菜单代码
            def extract_menu_codes(menu_item):
                codes = []
                if menu_item.get('code'):
                    codes.append(menu_item['code'])
                for child in menu_item.get('children', []):
                    codes.extend(extract_menu_codes(child))
                return codes
            menu_codes.extend(extract_menu_codes(menu))
        return menu_codes
    
    def _get_user_roles_info(self, user: User) -> List[Dict[str, Any]]:
        """获取用户角色信息 - 优化版本，假设角色已预加载"""
        if not user or not hasattr(user, 'roles') or not user.roles:
            return []

        return [
            {
                "id": role.id,
                "name": role.name,
                "code": role.code,
                "data_scope": role.data_scope
            }
            for role in user.roles
        ]
    
    def _get_user_with_preloaded_relations(self, user_id: int) -> Optional[User]:
        """获取用户及其预加载的关联数据，避免N+1查询"""
        try:
            from sqlalchemy.orm import selectinload
            return (
                self.db.query(User)
                .options(
                    selectinload(User.permissions),
                    selectinload(User.roles)
                )
                .filter(User.id == user_id)
                .first()
            )
        except Exception as e:
            logger.error(f"获取用户关联数据失败: {e}")
            return None

    def _get_batch_role_permissions(self, role_codes: List[str]) -> Set[str]:
        """批量获取角色权限，避免N+1查询"""
        if not role_codes:
            return set()

        try:
            # 使用原生SQL批量查询角色权限
            role_permissions_sql = text("""
                SELECT DISTINCT permission_code
                FROM role_permissions
                WHERE role_code = ANY(:role_codes)
            """)
            role_permission_results = self.db.execute(
                role_permissions_sql,
                {"role_codes": role_codes}
            ).fetchall()

            return {result[0] for result in role_permission_results}
        except Exception as e:
            logger.error(f"批量获取角色权限失败: {e}")
            return set()

    def _get_role_permissions(self, role_code: str) -> Set[str]:
        """获取单个角色权限（保留向后兼容）"""
        return self._get_batch_role_permissions([role_code])
    
    def _get_fallback_user_info(self, current_user: User) -> Dict[str, Any]:
        """获取备用用户信息"""
        return {
            "id": current_user.id,
            "username": current_user.username,
            "email": current_user.email,
            "full_name": current_user.full_name,
            "roles": [],
            "is_superuser": current_user.is_superuser,
            "merchant_id": current_user.merchant_id,
            "is_active": current_user.is_active,
            "phone": current_user.phone,
            "remark": current_user.remark,
            "created_at": current_user.created_at,
            "updated_at": current_user.updated_at,
            "permissions": ["dashboard:view"],
            "data_scope": "self",
            "menus": ["dashboard"]
        }
