"""
测试绑卡参数验证功能

验证所有绑卡相关方法的参数验证是否正确工作
"""

import pytest
import asyncio
from unittest.mock import Mock, AsyncMock
from sqlalchemy.orm import Session

from app.core.walmart_api import WalmartAPI
from app.services.binding_service import BindingService
from app.services.card_record_service import CardRecordService
from app.schemas.card import BindCardParams
from app.models.user import User
from pydantic import ValidationError


class TestWalmartAPIValidation:
    """测试WalmartAPI类的参数验证"""
    
    def setup_method(self):
        """设置测试环境"""
        self.api = WalmartAPI(
            sign="test_sign",
            encryption_key="test_key",
            version="29"
        )
    
    @pytest.mark.asyncio
    async def test_bind_card_empty_card_no(self):
        """测试空卡号参数"""
        with pytest.raises(ValueError, match="卡号不能为空、None或空字符串"):
            await self.api.bind_card("", "123456")
    
    @pytest.mark.asyncio
    async def test_bind_card_none_card_no(self):
        """测试None卡号参数"""
        with pytest.raises(ValueError, match="卡号不能为空、None或空字符串"):
            await self.api.bind_card(None, "123456")
    
    @pytest.mark.asyncio
    async def test_bind_card_whitespace_card_no(self):
        """测试空白字符卡号参数"""
        with pytest.raises(ValueError, match="卡号不能为空、None或空字符串"):
            await self.api.bind_card("   ", "123456")
    
    @pytest.mark.asyncio
    async def test_bind_card_empty_card_pwd(self):
        """测试空卡密码参数"""
        with pytest.raises(ValueError, match="卡密码不能为空、None或空字符串"):
            await self.api.bind_card("1234567890", "")
    
    @pytest.mark.asyncio
    async def test_bind_card_none_card_pwd(self):
        """测试None卡密码参数"""
        with pytest.raises(ValueError, match="卡密码不能为空、None或空字符串"):
            await self.api.bind_card("1234567890", None)
    
    @pytest.mark.asyncio
    async def test_bind_card_whitespace_card_pwd(self):
        """测试空白字符卡密码参数"""
        with pytest.raises(ValueError, match="卡密码不能为空、None或空字符串"):
            await self.api.bind_card("1234567890", "   ")
    
    def test_bind_card_sync_empty_card_no(self):
        """测试同步版本空卡号参数"""
        with pytest.raises(ValueError, match="卡号不能为空、None或空字符串"):
            self.api.bind_card_sync("", "123456")
    
    def test_bind_card_sync_empty_card_pwd(self):
        """测试同步版本空卡密码参数"""
        with pytest.raises(ValueError, match="卡密码不能为空、None或空字符串"):
            self.api.bind_card_sync("1234567890", "")


class TestBindingServiceValidation:
    """测试BindingService类的参数验证"""
    
    def setup_method(self):
        """设置测试环境"""
        self.service = BindingService()
        self.mock_db = Mock(spec=Session)
    
    @pytest.mark.asyncio
    async def test_bind_card_empty_card_number(self):
        """测试空卡号参数"""
        with pytest.raises(ValueError, match="卡号不能为空、None或空字符串"):
            await self.service.bind_card(
                self.mock_db, "", "123456", "user123", 1
            )
    
    @pytest.mark.asyncio
    async def test_bind_card_empty_card_password(self):
        """测试空卡密码参数"""
        with pytest.raises(ValueError, match="卡密码不能为空、None或空字符串"):
            await self.service.bind_card(
                self.mock_db, "1234567890", "", "user123", 1
            )
    
    @pytest.mark.asyncio
    async def test_bind_card_empty_user_id(self):
        """测试空用户ID参数"""
        with pytest.raises(ValueError, match="用户ID不能为空、None或空字符串"):
            await self.service.bind_card(
                self.mock_db, "1234567890", "123456", "", 1
            )
    
    @pytest.mark.asyncio
    async def test_bind_card_invalid_merchant_id(self):
        """测试无效商户ID参数"""
        with pytest.raises(ValueError, match="商户ID必须是正整数"):
            await self.service.bind_card(
                self.mock_db, "1234567890", "123456", "user123", 0
            )
    
    @pytest.mark.asyncio
    async def test_bind_card_negative_merchant_id(self):
        """测试负数商户ID参数"""
        with pytest.raises(ValueError, match="商户ID必须是正整数"):
            await self.service.bind_card(
                self.mock_db, "1234567890", "123456", "user123", -1
            )


class TestCardRecordServiceValidation:
    """测试CardRecordService类的参数验证"""
    
    def setup_method(self):
        """设置测试环境"""
        self.mock_db = Mock(spec=Session)
        self.service = CardRecordService(self.mock_db)
        self.mock_user = Mock(spec=User)
        self.mock_user.id = "user123"
    
    def test_bind_card_empty_card_id(self):
        """测试空卡记录ID参数"""
        with pytest.raises(ValueError, match="卡记录ID不能为空、None或空字符串"):
            self.service.bind_card("", 1, self.mock_user)
    
    def test_bind_card_invalid_walmart_ck_id(self):
        """测试无效沃尔玛CK ID参数"""
        with pytest.raises(ValueError, match="沃尔玛CK ID必须是正整数"):
            self.service.bind_card("card123", 0, self.mock_user)
    
    def test_bind_card_none_user(self):
        """测试None用户参数"""
        with pytest.raises(ValueError, match="当前用户不能为空"):
            self.service.bind_card("card123", 1, None)
    
    def test_batch_bind_cards_invalid_list(self):
        """测试无效的卡记录ID列表"""
        with pytest.raises(ValueError, match="卡记录ID列表必须是列表类型"):
            self.service.batch_bind_cards("not_a_list", self.mock_user)
    
    def test_batch_bind_cards_empty_card_id_in_list(self):
        """测试列表中包含空卡记录ID"""
        with pytest.raises(ValueError, match="第1个卡记录ID不能为空、None或空字符串"):
            self.service.batch_bind_cards([""], self.mock_user)
    
    def test_batch_bind_cards_none_user(self):
        """测试批量绑卡None用户参数"""
        with pytest.raises(ValueError, match="当前用户不能为空"):
            self.service.batch_bind_cards(["card123"], None)


class TestBindCardParamsValidation:
    """测试BindCardParams模型的参数验证"""
    
    def test_valid_params(self):
        """测试有效参数"""
        params = BindCardParams(
            card_number="1234567890123456",
            card_password="123456",
            merchant_code="MERCHANT001",
            merchant_order_id="ORDER123",
            amount=1000
        )
        assert params.card_number == "1234567890123456"
        assert params.card_password == "123456"
    
    def test_empty_card_number(self):
        """测试空卡号"""
        with pytest.raises(ValidationError) as exc_info:
            BindCardParams(
                card_number="",
                card_password="123456",
                merchant_code="MERCHANT001",
                merchant_order_id="ORDER123",
                amount=1000
            )
        assert "卡号不能为空字符串" in str(exc_info.value)
    
    def test_empty_card_password(self):
        """测试空卡密码"""
        with pytest.raises(ValidationError) as exc_info:
            BindCardParams(
                card_number="1234567890123456",
                card_password="",
                merchant_code="MERCHANT001",
                merchant_order_id="ORDER123",
                amount=1000
            )
        assert "卡密码不能为空字符串" in str(exc_info.value)
    
    def test_whitespace_card_number(self):
        """测试空白字符卡号"""
        with pytest.raises(ValidationError) as exc_info:
            BindCardParams(
                card_number="   ",
                card_password="123456",
                merchant_code="MERCHANT001",
                merchant_order_id="ORDER123",
                amount=1000
            )
        assert "卡号不能为空字符串" in str(exc_info.value)
    
    def test_short_card_number(self):
        """测试过短的卡号"""
        with pytest.raises(ValidationError) as exc_info:
            BindCardParams(
                card_number="12345",
                card_password="123456",
                merchant_code="MERCHANT001",
                merchant_order_id="ORDER123",
                amount=1000
            )
        assert "卡号长度不能少于6位" in str(exc_info.value)
    
    def test_amount_too_small(self):
        """测试金额过小"""
        with pytest.raises(ValidationError) as exc_info:
            BindCardParams(
                card_number="1234567890123456",
                card_password="123456",
                merchant_code="MERCHANT001",
                merchant_order_id="ORDER123",
                amount=50
            )
        assert "greater than or equal to 100" in str(exc_info.value)


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
