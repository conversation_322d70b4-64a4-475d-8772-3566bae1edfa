import aio_pika
import json
import asyncio
from typing import Dict, Any
from app.core.config import settings
from app.core.logging import get_logger

logger = get_logger("queue_producer")


async def send_bind_card_task(message: Dict[str, Any]):
    """
    发送绑卡任务到RabbitMQ
    """
    print("发送绑卡任务到RabbitMQ", settings.RABBITMQ_URL)
    connection = await aio_pika.connect_robust(settings.RABBITMQ_URL)
    async with connection:
        channel = await connection.channel()
        queue = await channel.declare_queue(
            settings.RABBITMQ_QUEUE_BIND_CARD, durable=True
        )
        await channel.default_exchange.publish(
            aio_pika.Message(
                body=json.dumps(message).encode(),
                delivery_mode=aio_pika.DeliveryMode.PERSISTENT,
            ),
            routing_key=queue.name,
        )


async def send_callback_task(message: Dict[str, Any], delay_seconds: int = 0):
    """
    发送回调任务到RabbitMQ，支持延迟发送

    Args:
        message: 消息内容
        delay_seconds: 延迟秒数，0表示立即发送
    """
    connection = await aio_pika.connect_robust(settings.RABBITMQ_URL)
    async with connection:
        channel = await connection.channel()

        # 声明交换机和队列
        exchange_name = "bind_card_callback_exchange"
        queue_name = settings.RABBITMQ_QUEUE_CALLBACK

        # 声明直接交换机
        exchange = await channel.declare_exchange(
            exchange_name, aio_pika.ExchangeType.DIRECT, durable=True
        )

        # 声明队列
        queue = await channel.declare_queue(queue_name, durable=True)

        # 绑定队列到交换机
        await queue.bind(exchange, routing_key=queue_name)

        # 如果需要延迟发送
        if delay_seconds > 0:
            # 声明延迟队列所需的交换机和队列
            delay_exchange_name = "delay_exchange"
            delay_queue_name = f"delay.{queue_name}.{delay_seconds}"

            # 声明延迟交换机
            delay_exchange = await channel.declare_exchange(
                delay_exchange_name, aio_pika.ExchangeType.DIRECT, durable=True
            )

            # 声明延迟队列，设置消息过期时间和死信交换机
            delay_queue = await channel.declare_queue(
                delay_queue_name,
                durable=True,
                arguments={
                    "x-dead-letter-exchange": exchange_name,
                    "x-dead-letter-routing-key": queue_name,
                    "x-message-ttl": delay_seconds * 1000,  # 毫秒
                },
            )

            # 绑定延迟队列到延迟交换机
            await delay_queue.bind(delay_exchange, routing_key=delay_queue_name)

            # 发布消息到延迟交换机
            await delay_exchange.publish(
                aio_pika.Message(
                    body=json.dumps(message).encode(),
                    delivery_mode=aio_pika.DeliveryMode.PERSISTENT,
                ),
                routing_key=delay_queue_name,
            )

            logger.info(f"已发送延迟回调任务到队列，延迟{delay_seconds}秒: {message}")
        else:
            # 直接发布消息到主交换机
            await exchange.publish(
                aio_pika.Message(
                    body=json.dumps(message).encode(),
                    delivery_mode=aio_pika.DeliveryMode.PERSISTENT,
                ),
                routing_key=queue_name,
            )

            logger.info(f"已发送回调任务到队列: {message}")


# 用于同步环境下调用
def send_bind_card_task_sync(message: Dict[str, Any]):
    asyncio.run(send_bind_card_task(message))


# 用于同步环境下调用
def send_callback_task_sync(message: Dict[str, Any], delay_seconds: int = 0):
    asyncio.run(send_callback_task(message, delay_seconds))
