<script setup>
import { ref, computed } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ElMessage } from 'element-plus'
import {
    Setting,
    User,
    DataLine,
    Odometer,
    Expand,
    Fold,
    Shop,
    Tools,
    ArrowDown,
    Bell,
    InfoFilled
} from '@element-plus/icons-vue'
import NotificationIcon from '@/components/business/notification/NotificationIcon.vue'
import MerchantSwitcher from '@/components/business/merchant/MerchantSwitcher.vue'
import SideMenu from './SideMenu.vue'
import { useUserStore } from '@/store/modules/user'

const router = useRouter()
const route = useRoute()
const userStore = useUserStore()

const isCollapse = ref(false)
const toggleSidebar = () => {
    isCollapse.value = !isCollapse.value
}

const activeMenu = computed(() => {
    return route.path
})

// 只有超级管理员和平台管理员可以切换租户
const canSwitchMerchant = computed(() => {
    return userStore.isSuperAdmin || userStore.isPlatformAdmin
})

// 处理下拉菜单命令
const handleDropdownCommand = (command) => {
    switch (command) {
        case 'settings':
            handlePersonalSettings()
            break
        case 'logout':
            handleLogout()
            break
        default:
            console.warn('未知的下拉菜单命令:', command)
    }
}

// 处理个人设置
const handlePersonalSettings = () => {
    try {
        console.log('跳转到个人设置页面...')
        router.push('/security/index')
    } catch (error) {
        console.error('跳转到个人设置失败:', error)
        ElMessage.error('跳转失败，请重试')
    }
}

// 退出登录
const handleLogout = async () => {
    try {
        console.log('开始退出登录...')

        // 执行登出
        await userStore.logout()

        console.log('登出成功，跳转到登录页')

        // 跳转到登录页
        router.replace('/login')

        // 显示退出成功消息
        ElMessage.success('已退出登录')
    } catch (error) {
        console.error('退出登录失败:', error)
        // 即使退出失败，也要跳转到登录页
        router.replace('/login')
    }
}
</script>

<template>
    <div class="app-container">
        <!-- 侧边栏 -->
        <el-aside :width="isCollapse ? '64px' : '220px'" class="sidebar-container">
            <div class="logo-container">
                <h1 v-if="!isCollapse">沃尔玛绑卡系统</h1>
                <span v-else>WM</span>
            </div>

            <!-- SideMenu 组件自己获取菜单 -->
            <side-menu :collapse="isCollapse" />
        </el-aside>

        <!-- 主要内容区域 -->
        <el-container class="main-container">
            <!-- 头部导航 -->
            <el-header class="header">
                <div class="header-left">
                    <el-icon class="collapse-btn" @click="toggleSidebar">
                        <component :is="isCollapse ? Expand : Fold" />
                    </el-icon>
                    <span class="breadcrumb">{{ route.meta.title || '首页' }}</span>
                </div>
                <div class="header-right">
                    <!-- 商家切换器 - 仅管理员可见 -->
                    <merchant-switcher v-if="canSwitchMerchant" />
                    <notification-icon class="notification-icon-container" />
                    <el-dropdown @command="handleDropdownCommand">
                        <span class="user-dropdown">
                            {{ userStore.userName || '管理员' }} <el-icon class="el-icon--right"><arrow-down /></el-icon>
                        </span>
                        <template #dropdown>
                            <el-dropdown-menu>
                                <el-dropdown-item command="settings">个人设置</el-dropdown-item>
                                <el-dropdown-item divided command="logout">退出登录</el-dropdown-item>
                            </el-dropdown-menu>
                        </template>
                    </el-dropdown>
                </div>
            </el-header>

            <!-- 内容区域 -->
            <el-main>
                <router-view />
            </el-main>
        </el-container>
    </div>
</template>

<style scoped>
.app-container {
    height: 100vh;
    display: flex;
}

.sidebar-container {
    background-color: #304156;
    height: 100%;
    transition: width 0.3s;
    overflow-x: hidden;
}

.logo-container {
    height: 60px;
    padding: 0 15px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #fff;
    background-color: #2b3649;
    font-size: 18px;
    white-space: nowrap;
    overflow: hidden;
}

.main-container {
    flex: 1;
    display: flex;
    flex-direction: column;
    height: 100%;
    background-color: #f0f2f5;
}

.header {
    background-color: #fff;
    box-shadow: 0 1px 4px rgba(0, 21, 41, .08);
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 15px;
}

.header-left {
    display: flex;
    align-items: center;
}

.collapse-btn {
    cursor: pointer;
    font-size: 20px;
    margin-right: 15px;
}

.breadcrumb {
    font-size: 16px;
    font-weight: 500;
}

.user-dropdown {
    cursor: pointer;
    display: flex;
    align-items: center;
}

:deep(.el-main) {
    padding-left: 20px;
    padding-right: 20px;
    overflow-y: auto;
}

.header-right {
    display: flex;
    align-items: center;
}

.notification-icon-container {
    margin-right: 20px;
}
</style>