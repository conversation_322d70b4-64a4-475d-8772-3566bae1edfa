"""
仪表盘统计服务类
负责处理仪表盘相关的统计业务逻辑
"""

from typing import Any, Dict, List, Optional, Tuple, Union
from datetime import datetime, timedelta
from decimal import Decimal
from sqlalchemy import func, case, and_, or_, text
from sqlalchemy.orm import Session

from app.models.user import User
from app.models.merchant import Merchant
from app.models.card_record import CardRecord, CardStatus
from app.models.department import Department
from app.models.walmart_ck import WalmartCK
from app.core.logging import get_logger


class DashboardStatisticsService:
    """仪表盘统计业务逻辑服务类 - 【安全修复】加强商户数据隔离"""

    def __init__(self, db: Session):
        self.db = db
        self.logger = get_logger("dashboard_statistics_service")

    def _get_safe_merchant_id(self, current_user: User, requested_merchant_id: Optional[int] = None) -> Optional[int]:
        """
        【安全修复】安全获取商户ID，确保数据隔离

        Args:
            current_user: 当前用户
            requested_merchant_id: 请求的商户ID

        Returns:
            Optional[int]: 安全的商户ID，如果用户无权限则返回None
        """
        if current_user.is_superuser:
            # 超级管理员可以访问任何商户的数据
            return requested_merchant_id

        if not current_user.merchant_id:
            # 如果用户没有商户ID，记录安全警告
            self.logger.warning(f"[SECURITY] 用户 {current_user.id} 没有商户ID，拒绝统计数据访问")
            return None

        # 非超级管理员只能访问自己商户的数据，忽略请求的merchant_id
        if requested_merchant_id and requested_merchant_id != current_user.merchant_id:
            self.logger.warning(f"[SECURITY] 用户 {current_user.id} 尝试访问其他商户 {requested_merchant_id} 的数据，已拒绝")

        return current_user.merchant_id

    def _safe_float(self, value: Union[int, float, Decimal, None], default: float = 0.0) -> float:
        """
        安全地将数值转换为 float 类型，处理 Decimal、None 等情况

        Args:
            value: 要转换的数值，可能是 int、float、Decimal 或 None
            default: 默认值，当 value 为 None 或转换失败时返回

        Returns:
            float: 转换后的浮点数
        """
        if value is None:
            return default

        try:
            if isinstance(value, Decimal):
                return float(value)
            elif isinstance(value, (int, float)):
                return float(value)
            else:
                # 尝试转换字符串或其他类型
                return float(value)
        except (ValueError, TypeError, OverflowError) as e:
            self.logger.warning(f"数值转换失败: {value} -> {e}, 使用默认值: {default}")
            return default

    def _safe_int(self, value: Union[int, float, Decimal, None], default: int = 0) -> int:
        """
        安全地将数值转换为 int 类型

        Args:
            value: 要转换的数值
            default: 默认值

        Returns:
            int: 转换后的整数
        """
        if value is None:
            return default

        try:
            if isinstance(value, Decimal):
                return int(value)
            elif isinstance(value, (int, float)):
                return int(value)
            else:
                return int(float(value))
        except (ValueError, TypeError, OverflowError) as e:
            self.logger.warning(f"整数转换失败: {value} -> {e}, 使用默认值: {default}")
            return default

    def get_dashboard_statistics(
        self,
        current_user: User,
        merchant_id: Optional[int] = None,
        time_range: str = "today"
    ) -> Dict[str, Any]:
        """
        获取仪表盘统计数据 - 重新设计版本

        Args:
            current_user: 当前用户
            merchant_id: 商家ID（可选）
            time_range: 时间范围 (today, week, month)

        Returns:
            Dict[str, Any]: 重新设计的统计数据
        """
        # 根据用户角色确定数据范围
        if not current_user.is_platform_user():
            # 商家用户只能查看自己商家的数据，忽略传入的merchant_id参数
            merchant_id = current_user.merchant_id

        # 获取时间范围
        start_time, end_time = self._get_time_range(time_range)

        # 获取绑卡金额统计
        amount_stats = self._get_amount_statistics(merchant_id, start_time, end_time)

        # 获取绑卡成功率统计
        success_rate_stats = self._get_success_rate_statistics(merchant_id, start_time, end_time)

        # 获取CK使用效率统计
        ck_efficiency_stats = self._get_ck_efficiency_statistics(merchant_id, start_time, end_time)

        # 获取异常/失败统计
        failure_stats = self._get_failure_statistics(merchant_id, start_time, end_time)

        # 获取部门业绩排名（仅商户管理员）
        department_ranking = []
        if not current_user.is_platform_user() and merchant_id:
            department_ranking = self._get_department_ranking(merchant_id, start_time, end_time)

        return {
            "time_range": time_range,
            "amount_statistics": amount_stats,
            "success_rate_statistics": success_rate_stats,
            "ck_efficiency_statistics": ck_efficiency_stats,
            "failure_statistics": failure_stats,
            "department_ranking": department_ranking,
            "generated_at": datetime.now().isoformat()
        }

    def get_dashboard_statistics_legacy(
        self,
        current_user: User,
        merchant_id: Optional[int] = None,
    ) -> Dict[str, Any]:
        """
        获取仪表盘统计数据 - 保留原有版本以确保兼容性

        Args:
            current_user: 当前用户
            merchant_id: 商家ID（可选）

        Returns:
            Dict[str, Any]: 统计数据
        """
        # 根据用户角色确定数据范围
        if not current_user.is_platform_user():
            # 商家用户只能查看自己商家的数据，忽略传入的merchant_id参数
            merchant_id = current_user.merchant_id

        # 构建基础查询，只选择需要的字段
        query = self.db.query(
            CardRecord.id,
            CardRecord.merchant_id,
            CardRecord.status,
            CardRecord.created_at,
        )

        # 【安全修复】强制商户隔离 - 如果没有merchant_id，返回空结果
        if merchant_id is not None:
            query = query.filter(CardRecord.merchant_id == merchant_id)
        else:
            # 没有merchant_id时返回空结果，防止跨商户数据泄露
            self.logger.warning("[SECURITY] _build_time_distribution_query: merchant_id为None，返回空结果")
            query = query.filter(CardRecord.id == -1)  # 强制返回空结果

        # 获取今日数据 - 修复时区问题
        from app.utils.time_utils import get_current_time
        today = get_current_time().date()
        today_start = datetime.combine(today, datetime.min.time())
        today_end = datetime.combine(today, datetime.max.time())

        today_records = query.filter(
            CardRecord.created_at.between(today_start, today_end)
        )

        # 统计数据
        total_success = today_records.filter(CardRecord.status == "success").count()
        total_failed = today_records.filter(CardRecord.status == "failed").count()
        total = total_success + total_failed

        # 计算成功率
        success_rate = (total_success / total * 100) if total > 0 else 0

        # 根据用户角色返回不同的统计数据
        if current_user.is_platform_user():
            # 平台用户可以看到全局统计数据 - 优化版本，使用单次聚合查询避免N+1查询
            # 使用单次查询获取商户统计
            merchant_stats = self.db.query(
                func.count(Merchant.id).label("total_merchants"),
                func.sum(case((Merchant.status == True, 1), else_=0)).label(
                    "active_merchants"
                ),
            ).first()

            # 使用单次查询获取用户统计
            user_stats = (
                self.db.query(
                    func.count(User.id).label("total_users"),
                )
                .filter(User.is_active == True)
                .first()
            )

            merchant_count = merchant_stats.total_merchants or 0
            active_merchant_count = merchant_stats.active_merchants or 0
            total_users = user_stats.total_users or 0
            total_merchants = merchant_count  # 避免重复查询
            active_merchants = active_merchant_count  # 避免重复查询

            return {
                "merchant_count": merchant_count,
                "active_merchant_count": active_merchant_count,
                "today_total": total,
                "today_success": total_success,
                "today_failed": total_failed,
                "success_rate": round(success_rate, 2),
                "total_users": total_users,
                "total_merchants": total_merchants,
                "active_merchants": active_merchants,
                "comparison": 0,  # TODO: 计算环比数据
            }
        else:
            # 商家用户只能看到自己的业务数据，不显示系统级统计
            return {
                "today_total": total,
                "today_success": total_success,
                "today_failed": total_failed,
                "success_rate": round(success_rate, 2),
                "comparison": 0,  # TODO: 计算环比数据
            }

    def get_merchant_ranking(
        self,
        current_user: User,
        time_unit: str = "day",
        merchant_id: Optional[int] = None,
    ) -> Dict[str, Any]:
        """
        获取商家绑卡排名数据

        Args:
            current_user: 当前用户
            time_unit: 时间单位 (day/week/month)
            merchant_id: 商家ID（可选）

        Returns:
            Dict[str, Any]: 排名数据
        """
        # 根据用户角色确定数据范围
        if not current_user.is_platform_user():
            # 商家用户只能查看自己商家的数据，忽略传入的merchant_id参数
            merchant_id = current_user.merchant_id

        now = datetime.now()

        # 根据时间单位确定查询时间范围
        if time_unit == "day":
            start_time = datetime.combine(now.date(), datetime.min.time())
        elif time_unit == "week":
            start_time = now - timedelta(days=now.weekday())
            start_time = datetime.combine(start_time.date(), datetime.min.time())
        else:  # month
            start_time = datetime.combine(
                now.replace(day=1).date(), datetime.min.time()
            )

        # 查询每个商家的绑卡数据
        query = (
            self.db.query(
                CardRecord.merchant_id,
                Merchant.name.label("merchant_name"),
                func.count().label("total_count"),
                func.sum(case((CardRecord.status == "success", 1), else_=0)).label(
                    "success_count"
                ),
            )
            .join(Merchant, CardRecord.merchant_id == Merchant.id)
            .filter(CardRecord.created_at >= start_time)
            .group_by(CardRecord.merchant_id, Merchant.name)
            .order_by(func.count().desc())
        )

        # 【安全修复】强制商户隔离 - 如果没有merchant_id，返回空结果
        if merchant_id is not None:
            query = query.filter(CardRecord.merchant_id == merchant_id)
        else:
            # 没有merchant_id时返回空结果，防止跨商户数据泄露
            self.logger.warning("[SECURITY] get_merchant_activity: merchant_id为None，返回空结果")
            query = query.filter(CardRecord.id == -1)  # 强制返回空结果

        results = query.limit(10).all()

        merchants = []
        for result in results:
            success_rate = (
                (result.success_count / result.total_count * 100)
                if result.total_count > 0
                else 0
            )
            merchants.append(
                {
                    "id": result.merchant_id,
                    "name": result.merchant_name,
                    "value": result.total_count,
                    "successRate": round(success_rate, 2),
                }
            )

        return {"merchants": merchants}

    def get_time_distribution(
        self,
        current_user: User,
        time_unit: str = "hour",
        merchant_id: Optional[int] = None,
    ) -> List[Dict[str, Any]]:
        """
        获取时间分布数据

        Args:
            current_user: 当前用户
            time_unit: 时间单位 (hour/day)
            merchant_id: 商家ID（可选）

        Returns:
            List[Dict[str, Any]]: 时间分布数据
        """
        # 确定数据范围
        target_merchant_id = self._determine_merchant_scope_for_distribution(
            current_user, merchant_id
        )

        # 构建基础查询
        base_query = self._build_time_distribution_query(target_merchant_id)

        # 根据时间单位获取数据
        if time_unit == "hour":
            return self._get_hourly_distribution_data(base_query)
        else:  # day
            return self._get_daily_distribution_data(base_query)

    def _determine_merchant_scope_for_distribution(
        self, current_user: User, merchant_id: Optional[int]
    ) -> Optional[int]:
        """确定时间分布的商户数据范围"""
        if not current_user.is_platform_user():
            # 商家用户只能查看自己商家的数据
            return current_user.merchant_id
        return merchant_id

    def _build_time_distribution_query(self, merchant_id: Optional[int]):
        """构建时间分布基础查询"""
        query = self.db.query(
            CardRecord.id,
            CardRecord.merchant_id,
            CardRecord.status,
            CardRecord.created_at,
        )

        # 【安全修复】强制商户隔离 - 如果没有merchant_id，返回空结果
        if merchant_id is not None:
            query = query.filter(CardRecord.merchant_id == merchant_id)
        else:
            # 没有merchant_id时返回空结果，防止跨商户数据泄露
            self.logger.warning("[SECURITY] _build_time_distribution_query: merchant_id为None，返回空结果")
            query = query.filter(CardRecord.id == -1)  # 强制返回空结果

        return query

    def _get_hourly_distribution_data(self, query) -> List[Dict[str, Any]]:
        """获取每小时分布数据 - 优化版本，使用单次聚合查询避免N+1查询"""
        now = datetime.now()
        today_start = datetime.combine(now.date(), datetime.min.time())
        today_end = datetime.combine(now.date(), datetime.max.time())

        # 使用单次聚合查询获取今天所有小时的数据
        hourly_stats = (
            query.filter(CardRecord.created_at.between(today_start, today_end))
            .with_entities(
                func.hour(CardRecord.created_at).label("hour"),
                func.count(CardRecord.id).label("total_count"),
                func.sum(case((CardRecord.status == "success", 1), else_=0)).label(
                    "success_count"
                ),
            )
            .group_by(func.hour(CardRecord.created_at))
            .all()
        )

        # 创建小时数据字典
        hour_data = {stat.hour: stat for stat in hourly_stats}

        # 构建24小时完整数据
        results = []
        for hour in range(24):
            stat = hour_data.get(hour)
            if stat:
                total = stat.total_count
                success = stat.success_count
            else:
                total = 0
                success = 0

            results.append(
                {
                    "time": f"{hour:02d}:00",
                    "count": total,
                    "successRate": round(
                        (success / total * 100) if total > 0 else 0, 2
                    ),
                }
            )

        return results

    def _get_daily_distribution_data(self, query) -> List[Dict[str, Any]]:
        """获取每日分布数据 - 优化版本，使用单次聚合查询避免N+1查询"""
        now = datetime.now()
        start_date = now.date() - timedelta(days=6)
        end_date = now.date()

        # 使用单次聚合查询获取最近7天的数据
        daily_stats = (
            query.filter(func.date(CardRecord.created_at).between(start_date, end_date))
            .with_entities(
                func.date(CardRecord.created_at).label("date"),
                func.count(CardRecord.id).label("total_count"),
                func.sum(case((CardRecord.status == "success", 1), else_=0)).label(
                    "success_count"
                ),
            )
            .group_by(func.date(CardRecord.created_at))
            .all()
        )

        # 创建日期数据字典
        date_data = {stat.date: stat for stat in daily_stats}

        # 构建7天完整数据
        results = []
        for i in range(7):
            current_date = start_date + timedelta(days=i)
            stat = date_data.get(current_date)

            if stat:
                total = stat.total_count
                success = stat.success_count
            else:
                total = 0
                success = 0

            results.append(
                {
                    "time": current_date.strftime("%m-%d"),
                    "count": total,
                    "successRate": round(
                        (success / total * 100) if total > 0 else 0, 2
                    ),
                }
            )

        return results

    def _get_trend_time_config(self, time_unit: str) -> Tuple[int, str]:
        """获取趋势分析的时间配置"""
        time_configs = {
            "day": (7, "%Y-%m-%d"),  # 最近7天
            "week": (28, "%Y-%W"),  # 最近4周
            "month": (365, "%Y-%m"),  # 最近12个月
        }
        return time_configs.get(time_unit, (7, "%Y-%m-%d"))

    def _resolve_merchant_id(
        self, current_user: User, merchant_id: Optional[int]
    ) -> Optional[int]:
        """解析商户ID"""
        if not current_user.is_platform_user():
            # 商家用户只能查看自己商家的数据
            return current_user.merchant_id
        return merchant_id

    def _build_trend_base_query(self, merchant_id: Optional[int]):
        """构建趋势分析基础查询"""
        query = self.db.query(CardRecord)
        # 【安全修复】强制商户隔离 - 如果没有merchant_id，返回空结果
        if merchant_id is not None:
            query = query.filter(CardRecord.merchant_id == merchant_id)
        else:
            # 没有merchant_id时返回空结果，防止跨商户数据泄露
            self.logger.warning("[SECURITY] _build_trend_base_query: merchant_id为None，返回空结果")
            query = query.filter(CardRecord.id == -1)  # 强制返回空结果
        return query

    def _get_period_data(
        self, query, start_time: datetime, end_time: datetime, group_format: str
    ):
        """获取指定时期的数据"""
        return (
            query.filter(CardRecord.created_at.between(start_time, end_time))
            .with_entities(
                func.date_format(CardRecord.created_at, group_format).label(
                    "time_group"
                ),
                func.count().label("total"),
                func.sum(case((CardRecord.status == "success", 1), else_=0)).label(
                    "success_count"
                ),
            )
            .group_by("time_group")
            .all()
        )

    def _calculate_success_rate(self, success_count: int, total: int) -> float:
        """计算成功率"""
        return (success_count / total * 100) if total > 0 else 0

    def _find_previous_data(self, previous_data, time_group: str):
        """查找对应的上期数据"""
        return next((p for p in previous_data if p.time_group == time_group), None)

    def _process_trend_results(
        self, current_data, previous_data
    ) -> List[Dict[str, Any]]:
        """处理趋势分析结果"""
        results = []
        for current in current_data:
            current_rate = self._calculate_success_rate(
                current.success_count, current.total
            )

            # 查找对应的上期数据
            previous = self._find_previous_data(previous_data, current.time_group)
            previous_rate = (
                self._calculate_success_rate(previous.success_count, previous.total)
                if previous
                else 0
            )

            results.append(
                {
                    "time": current.time_group,
                    "total": current.total,
                    "currentRate": round(current_rate, 2),
                    "previousRate": round(previous_rate, 2),
                    "comparison": round(current_rate - previous_rate, 2),
                }
            )

        return results

    def get_trend_data(
        self,
        current_user: User,
        time_unit: str = "day",
        merchant_id: Optional[int] = None,
    ) -> List[Dict[str, Any]]:
        """
        获取趋势数据

        Args:
            current_user: 当前用户
            time_unit: 时间单位 (day/week/month)
            merchant_id: 商家ID（可选）

        Returns:
            List[Dict[str, Any]]: 趋势数据
        """
        # 解析商户ID
        merchant_id = self._resolve_merchant_id(current_user, merchant_id)

        # 获取时间配置
        days, group_format = self._get_trend_time_config(time_unit)

        # 计算时间范围
        now = datetime.now()
        start_time = datetime.combine(
            (now - timedelta(days=days - 1)).date(), datetime.min.time()
        )

        # 构建查询
        query = self._build_trend_base_query(merchant_id)

        # 获取当期数据
        current_data = self._get_period_data(query, start_time, now, group_format)

        # 获取上期数据
        previous_start = start_time - timedelta(days=days)
        previous_data = self._get_period_data(
            query, previous_start, start_time, group_format
        )

        # 处理并返回结果
        return self._process_trend_results(current_data, previous_data)

    def _get_time_range(self, time_range: str) -> Tuple[datetime, datetime]:
        """
        获取时间范围

        Args:
            time_range: 时间范围 (today, week, month)

        Returns:
            Tuple[datetime, datetime]: 开始时间和结束时间
        """
        # 修复时区问题：使用上海时区的当前时间
        from app.utils.time_utils import get_current_time
        now = get_current_time()
        end_time = now

        if time_range == "today":
            start_time = datetime.combine(now.date(), datetime.min.time())
        elif time_range == "week":
            # 本周开始（周一）
            start_time = now - timedelta(days=now.weekday())
            start_time = datetime.combine(start_time.date(), datetime.min.time())
        elif time_range == "month":
            # 本月开始
            start_time = datetime.combine(now.replace(day=1).date(), datetime.min.time())
        else:
            # 默认今天
            start_time = datetime.combine(now.date(), datetime.min.time())

        return start_time, end_time

    def _get_amount_statistics(
        self,
        merchant_id: Optional[int],
        start_time: datetime,
        end_time: datetime
    ) -> Dict[str, Any]:
        """
        获取绑卡金额统计

        Args:
            merchant_id: 商户ID
            start_time: 开始时间
            end_time: 结束时间

        Returns:
            Dict[str, Any]: 金额统计数据
        """
        query = self.db.query(CardRecord)

        # 【安全修复】强制商户隔离 - 如果没有merchant_id，返回空结果
        if merchant_id is not None:
            query = query.filter(CardRecord.merchant_id == merchant_id)
        else:
            # 没有merchant_id时返回空结果，防止跨商户数据泄露
            self.logger.warning("[SECURITY] _get_amount_statistics: merchant_id为None，返回空结果")
            query = query.filter(CardRecord.id == -1)  # 强制返回空结果

        # 时间范围过滤
        query = query.filter(CardRecord.created_at.between(start_time, end_time))

        # 统计查询 - 增强版本，同时统计请求金额和实际金额
        stats = query.with_entities(
            func.count(CardRecord.id).label('total_requests'),
            func.sum(case((CardRecord.status == CardStatus.SUCCESS, 1), else_=0)).label('success_count'),
            func.sum(case((CardRecord.status == CardStatus.FAILED, 1), else_=0)).label('failed_count'),
            # 请求金额统计（用户提交的原始金额）
            func.sum(CardRecord.amount).label('total_requested_amount'),
            func.sum(case((CardRecord.status == CardStatus.SUCCESS, CardRecord.amount), else_=0)).label('success_requested_amount'),
            func.sum(case((CardRecord.status == CardStatus.FAILED, CardRecord.amount), else_=0)).label('failed_requested_amount'),
            # 实际金额统计（真实绑卡金额）
            func.sum(case((CardRecord.status == CardStatus.SUCCESS, CardRecord.actual_amount), else_=0)).label('success_actual_amount'),
            func.sum(case((CardRecord.actual_amount.isnot(None), CardRecord.actual_amount), else_=0)).label('total_actual_amount'),
        ).first()

        # 使用安全转换函数处理可能的 Decimal 类型
        total_requests = self._safe_int(stats.total_requests, 0)
        success_count = self._safe_int(stats.success_count, 0)
        failed_count = self._safe_int(stats.failed_count, 0)

        # 请求金额数据
        total_requested_amount = self._safe_int(stats.total_requested_amount, 0)
        success_requested_amount = self._safe_int(stats.success_requested_amount, 0)
        failed_requested_amount = self._safe_int(stats.failed_requested_amount, 0)

        # 实际金额数据
        success_actual_amount = self._safe_int(stats.success_actual_amount, 0)
        total_actual_amount = self._safe_int(stats.total_actual_amount, 0)

        # 计算成功率
        success_rate = (success_count / total_requests * 100) if total_requests > 0 else 0.0

        # 计算金额差异
        amount_difference = success_actual_amount - success_requested_amount if success_actual_amount > 0 and success_requested_amount > 0 else 0

        return {
            "total_requests": total_requests,
            "success_count": success_count,
            "failed_count": failed_count,
            "success_rate": round(success_rate, 2),

            # 请求金额统计（用户提交的原始金额）
            "total_requested_amount": total_requested_amount,  # 单位：分
            "success_requested_amount": success_requested_amount,  # 单位：分
            "failed_requested_amount": failed_requested_amount,  # 单位：分
            "total_requested_amount_yuan": round(self._safe_float(total_requested_amount) / 100, 2),  # 转换为元
            "success_requested_amount_yuan": round(self._safe_float(success_requested_amount) / 100, 2),  # 转换为元
            "failed_requested_amount_yuan": round(self._safe_float(failed_requested_amount) / 100, 2),  # 转换为元

            # 实际金额统计（真实绑卡金额）
            "success_actual_amount": success_actual_amount,  # 单位：分
            "total_actual_amount": total_actual_amount,  # 单位：分
            "success_actual_amount_yuan": round(self._safe_float(success_actual_amount) / 100, 2),  # 转换为元
            "total_actual_amount_yuan": round(self._safe_float(total_actual_amount) / 100, 2),  # 转换为元

            # 金额差异分析
            "amount_difference": amount_difference,  # 单位：分
            "amount_difference_yuan": round(self._safe_float(amount_difference) / 100, 2),  # 转换为元
            "amount_accuracy_rate": round((self._safe_float(success_actual_amount) / self._safe_float(success_requested_amount, 1) * 100), 2) if success_requested_amount > 0 else 0.0,

            # 保持向后兼容性（旧字段名）
            "total_amount": total_requested_amount,  # 兼容旧版本
            "success_amount": success_requested_amount,  # 兼容旧版本
            "failed_amount": failed_requested_amount,  # 兼容旧版本
            "total_amount_yuan": round(self._safe_float(total_requested_amount) / 100, 2),  # 兼容旧版本
            "success_amount_yuan": round(self._safe_float(success_requested_amount) / 100, 2),  # 兼容旧版本
            "failed_amount_yuan": round(self._safe_float(failed_requested_amount) / 100, 2),  # 兼容旧版本
        }

    def _get_success_rate_statistics(
        self,
        merchant_id: Optional[int],
        start_time: datetime,
        end_time: datetime
    ) -> Dict[str, Any]:
        """
        获取绑卡成功率统计

        Args:
            merchant_id: 商户ID
            start_time: 开始时间
            end_time: 结束时间

        Returns:
            Dict[str, Any]: 成功率统计数据
        """
        query = self.db.query(CardRecord)

        # 【安全修复】强制商户隔离 - 如果没有merchant_id，返回空结果
        if merchant_id is not None:
            query = query.filter(CardRecord.merchant_id == merchant_id)
        else:
            # 没有merchant_id时返回空结果，防止跨商户数据泄露
            self.logger.warning("[SECURITY] _get_success_rate_statistics: merchant_id为None，返回空结果")
            query = query.filter(CardRecord.id == -1)  # 强制返回空结果

        # 时间范围过滤
        query = query.filter(CardRecord.created_at.between(start_time, end_time))

        # 按小时统计成功率
        hourly_stats = query.with_entities(
            func.hour(CardRecord.created_at).label('hour'),
            func.count(CardRecord.id).label('total'),
            func.sum(case((CardRecord.status == CardStatus.SUCCESS, 1), else_=0)).label('success')
        ).group_by(func.hour(CardRecord.created_at)).all()

        # 计算各时段成功率
        hourly_rates = []
        for stat in hourly_stats:
            # 使用安全转换函数处理可能的 Decimal 类型
            total_safe = self._safe_int(stat.total, 0)
            success_safe = self._safe_int(stat.success, 0)
            rate = (success_safe / total_safe * 100) if total_safe > 0 else 0.0
            hourly_rates.append({
                "hour": self._safe_int(stat.hour, 0),
                "total": total_safe,
                "success": success_safe,
                "rate": round(self._safe_float(rate), 2)
            })

        # 计算总体成功率
        total_count = sum(self._safe_int(stat.total, 0) for stat in hourly_stats)
        total_success = sum(self._safe_int(stat.success, 0) for stat in hourly_stats)
        overall_rate = (total_success / total_count * 100) if total_count > 0 else 0.0

        return {
            "overall_success_rate": round(overall_rate, 2),
            "total_requests": total_count,
            "total_success": total_success,
            "hourly_breakdown": hourly_rates
        }

    def _get_ck_efficiency_statistics(
        self,
        merchant_id: Optional[int],
        start_time: datetime,
        end_time: datetime
    ) -> Dict[str, Any]:
        """
        获取CK使用效率统计

        Args:
            merchant_id: 商户ID
            start_time: 开始时间
            end_time: 结束时间

        Returns:
            Dict[str, Any]: CK效率统计数据
        """
        # 查询CK使用统计（显示所有CK，包括已禁用的）
        ck_query = self.db.query(
            WalmartCK.id,
            WalmartCK.sign,
            WalmartCK.active,
            func.count(CardRecord.id).label('total_used'),
            func.sum(case((CardRecord.status == CardStatus.SUCCESS, 1), else_=0)).label('success_count'),
            func.avg(CardRecord.process_time).label('avg_process_time')
        ).outerjoin(
            CardRecord,
            and_(
                CardRecord.walmart_ck_id == WalmartCK.id,
                CardRecord.created_at.between(start_time, end_time)
            )
        )

        # 【安全修复】强制商户隔离 - 如果没有merchant_id，返回空结果
        if merchant_id is not None:
            ck_query = ck_query.filter(WalmartCK.merchant_id == merchant_id)
        else:
            # 没有merchant_id时返回空结果，防止跨商户数据泄露
            self.logger.warning("[SECURITY] _get_ck_efficiency_statistics: merchant_id为None，返回空结果")
            ck_query = ck_query.filter(WalmartCK.id == -1)  # 强制返回空结果

        ck_stats = ck_query.group_by(WalmartCK.id, WalmartCK.sign, WalmartCK.active).all()

        # 处理CK统计数据
        ck_efficiency = []
        total_ck_count = 0
        active_ck_count = 0
        total_usage = 0

        for stat in ck_stats:
            total_ck_count += 1
            if stat.active == 1:  # 1表示活跃状态
                active_ck_count += 1

            # 使用安全转换函数处理可能的 Decimal 类型
            usage_count = self._safe_int(stat.total_used, 0)
            success_count = self._safe_int(stat.success_count, 0)
            avg_time = self._safe_float(stat.avg_process_time, 0.0)

            total_usage += usage_count

            efficiency_rate = (success_count / usage_count * 100) if usage_count > 0 else 0.0

            # 从sign中提取用户名，并添加区分信息
            base_username = stat.sign.split('@')[0] if stat.sign and '@' in stat.sign else stat.sign[:20] if stat.sign else f"CK_{stat.id}"

            # 如果用户名太长，截取前12位并添加省略号
            if len(base_username) > 12:
                base_username = base_username[:12] + "..."

            # 添加状态和CK ID来区分相同用户名的不同CK
            status_text = "启用" if stat.active == 1 else "禁用"
            display_name = f"{base_username}({status_text}#{stat.id})"

            # 确保所有数值都是 float 类型
            efficiency_rate_safe = self._safe_float(efficiency_rate, 0.0)
            avg_time_safe = self._safe_float(avg_time, 0.0)

            ck_efficiency.append({
                "ck_id": stat.id,
                "username": display_name,
                "status": 1 if stat.active else 0,  # 转换为数字格式以匹配前端期望
                "total_used": usage_count,
                "success_count": success_count,
                "efficiency_rate": round(efficiency_rate_safe, 2),
                "avg_process_time": round(avg_time_safe, 2)
            })

        # 计算平均效率 - 使用安全的数值转换
        if ck_efficiency:
            # 确保所有 efficiency_rate 都是 float 类型
            efficiency_rates = [self._safe_float(ck['efficiency_rate'], 0.0) for ck in ck_efficiency]
            avg_efficiency = sum(efficiency_rates) / len(efficiency_rates)
        else:
            avg_efficiency = 0.0

        return {
            "total_ck_count": total_ck_count,
            "active_ck_count": active_ck_count,
            "total_usage": total_usage,
            "average_efficiency": round(avg_efficiency, 2),
            "ck_details": ck_efficiency[:10]  # 只返回前10个CK的详细信息
        }

    def _get_failure_statistics(
        self,
        merchant_id: Optional[int],
        start_time: datetime,
        end_time: datetime
    ) -> Dict[str, Any]:
        """
        获取异常/失败绑卡统计

        Args:
            merchant_id: 商户ID
            start_time: 开始时间
            end_time: 结束时间

        Returns:
            Dict[str, Any]: 失败统计数据
        """
        query = self.db.query(CardRecord)

        # 【安全修复】强制商户隔离 - 如果没有merchant_id，返回空结果
        if merchant_id is not None:
            query = query.filter(CardRecord.merchant_id == merchant_id)
        else:
            # 没有merchant_id时返回空结果，防止跨商户数据泄露
            self.logger.warning("[SECURITY] _get_failure_statistics: merchant_id为None，返回空结果")
            query = query.filter(CardRecord.id == -1)  # 强制返回空结果

        # 时间范围过滤
        query = query.filter(CardRecord.created_at.between(start_time, end_time))

        # 统计各种失败状态
        failure_stats = query.filter(
            CardRecord.status.in_([CardStatus.FAILED, CardStatus.TIMEOUT, CardStatus.CANCELLED])
        ).with_entities(
            CardRecord.status,
            func.count(CardRecord.id).label('count'),
            func.sum(CardRecord.amount).label('amount')
        ).group_by(CardRecord.status).all()

        # 统计失败原因
        error_reasons = query.filter(
            CardRecord.status == CardStatus.FAILED
        ).filter(
            CardRecord.error_message.isnot(None)
        ).with_entities(
            CardRecord.error_message,
            func.count(CardRecord.id).label('count')
        ).group_by(CardRecord.error_message).order_by(func.count(CardRecord.id).desc()).limit(10).all()

        # 处理失败统计
        failure_breakdown = []
        total_failed_count = 0
        total_failed_amount = 0

        for stat in failure_stats:
            # 使用安全转换函数处理可能的 Decimal 类型
            count = self._safe_int(stat.count, 0)
            amount = self._safe_int(stat.amount, 0)
            total_failed_count += count
            total_failed_amount += amount

            failure_breakdown.append({
                "status": stat.status,
                "count": count,
                "amount": amount,
                "amount_yuan": round(self._safe_float(amount) / 100, 2)
            })

        # 处理错误原因
        error_breakdown = []
        for error in error_reasons:
            error_breakdown.append({
                "error_message": error.error_message,
                "count": self._safe_int(error.count, 0)
            })

        return {
            "total_failed_count": total_failed_count,
            "total_failed_amount": total_failed_amount,
            "total_failed_amount_yuan": round(self._safe_float(total_failed_amount) / 100, 2),
            "failure_breakdown": failure_breakdown,
            "top_error_reasons": error_breakdown
        }

    def _get_department_ranking(
        self,
        merchant_id: int,
        start_time: datetime,
        end_time: datetime
    ) -> List[Dict[str, Any]]:
        """
        获取部门绑卡业绩排名

        Args:
            merchant_id: 商户ID
            start_time: 开始时间
            end_time: 结束时间

        Returns:
            List[Dict[str, Any]]: 部门排名数据
        """
        # 查询部门绑卡统计
        dept_stats = self.db.query(
            Department.id,
            Department.name,
            Department.code,
            func.count(CardRecord.id).label('total_requests'),
            func.sum(case((CardRecord.status == CardStatus.SUCCESS, 1), else_=0)).label('success_count'),
            func.sum(CardRecord.amount).label('total_amount'),
            func.sum(case((CardRecord.status == CardStatus.SUCCESS, CardRecord.amount), else_=0)).label('success_amount')
        ).outerjoin(
            CardRecord,
            and_(
                CardRecord.department_id == Department.id,
                CardRecord.created_at.between(start_time, end_time)
            )
        ).filter(
            Department.merchant_id == merchant_id
        ).group_by(
            Department.id, Department.name, Department.code
        ).order_by(
            func.sum(case((CardRecord.status == CardStatus.SUCCESS, CardRecord.amount), else_=0)).desc()
        ).all()

        # 处理部门排名数据
        department_ranking = []
        for i, stat in enumerate(dept_stats, 1):
            total_requests = stat.total_requests or 0
            success_count = stat.success_count or 0
            total_amount = stat.total_amount or 0
            success_amount = stat.success_amount or 0

            success_rate = (success_count / total_requests * 100) if total_requests > 0 else 0

            department_ranking.append({
                "rank": i,
                "department_id": stat.id,
                "department_name": stat.name,
                "department_code": stat.code,
                "total_requests": total_requests,
                "success_count": success_count,
                "success_rate": round(success_rate, 2),
                "total_amount": total_amount,
                "success_amount": success_amount,
                "total_amount_yuan": round(total_amount / 100, 2),
                "success_amount_yuan": round(success_amount / 100, 2)
            })

        return department_ranking
