"""
回调服务适配器
提供平滑迁移和兼容性支持
"""
from typing import Dict, Any
from sqlalchemy.orm import Session

from app.core.logging import get_logger
from app.services.optimized_callback_service import optimized_callback_service
from app.services.callback_service import callback_service as original_callback_service

logger = get_logger("callback_service_adapter")


class CallbackServiceAdapter:
    """
    回调服务适配器
    提供原有接口的兼容性，内部使用优化的回调服务
    """
    
    def __init__(self, use_optimized: bool = True):
        self.use_optimized = use_optimized
        self._fallback_enabled = True
    
    async def process_callback_from_queue(self, db: Session, data: Dict[str, Any]):
        """
        从队列处理回调请求（兼容原有接口）
        
        Args:
            db: 数据库会话
            data: 队列数据
        """
        if self.use_optimized:
            try:
                # 优先使用优化的回调服务
                await optimized_callback_service.process_callback_from_queue(db, data)
                return
            except Exception as e:
                logger.error(f"优化回调服务处理失败: {e}")
                
                # 如果启用了回退机制，使用原有服务
                if self._fallback_enabled:
                    logger.info("回退到原有回调服务")
                    try:
                        await original_callback_service.process_callback_from_queue(db, data)
                        return
                    except Exception as fallback_error:
                        logger.error(f"原有回调服务也失败: {fallback_error}")
                        raise fallback_error
                else:
                    raise e
        else:
            # 直接使用原有服务
            await original_callback_service.process_callback_from_queue(db, data)
    
    def enable_fallback(self, enabled: bool = True):
        """启用或禁用回退机制"""
        self._fallback_enabled = enabled
        logger.info(f"回调服务回退机制: {'启用' if enabled else '禁用'}")
    
    def switch_to_optimized(self, enabled: bool = True):
        """切换到优化服务"""
        self.use_optimized = enabled
        service_type = "优化服务" if enabled else "原有服务"
        logger.info(f"回调服务切换到: {service_type}")
    
    def get_status(self) -> Dict[str, Any]:
        """获取适配器状态"""
        return {
            "use_optimized": self.use_optimized,
            "fallback_enabled": self._fallback_enabled,
            "optimized_service_available": True,  # 优化服务总是可用
            "original_service_available": True,   # 原有服务总是可用
        }


# 创建全局适配器实例
callback_service_adapter = CallbackServiceAdapter(use_optimized=True)
