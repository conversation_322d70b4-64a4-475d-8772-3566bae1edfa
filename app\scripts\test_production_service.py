#!/usr/bin/env python3
"""
简化的生产级服务测试
验证生产级CK服务的基本功能
"""

import asyncio
import sys
import os
import time
from collections import Counter

# 添加项目路径
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.insert(0, project_root)

from app.api.deps import get_db
from app.models.walmart_ck import WalmartCK
from app.services.production_ck_service import ProductionCKService
from app.core.logging import get_logger

logger = get_logger("test_production_service")


async def test_production_service(merchant_id=2, rounds=10):
    """测试生产级服务"""
    print(f"🚀 测试生产级CK服务")
    print(f"商户ID: {merchant_id}, 测试轮数: {rounds}")
    print("="*60)
    
    # 获取数据库连接
    db = next(get_db())
    
    try:
        # 检查可用CK
        available_cks = db.query(WalmartCK).filter(
            WalmartCK.merchant_id == merchant_id,
            WalmartCK.active == True,
            WalmartCK.is_deleted == False
        ).all()
        
        print(f"📊 可用CK数量: {len(available_cks)}")
        for ck in available_cks:
            print(f"  CK {ck.id}: bind_count={ck.bind_count}, total_limit={ck.total_limit}")
        
        if len(available_cks) == 0:
            print("❌ 没有可用的CK")
            return
        
        # 创建生产级服务
        async with ProductionCKService(db) as ck_service:
            print(f"\n🎯 开始 {rounds} 轮测试:")
            print("-"*40)
            
            selected_cks = []
            selection_times = []
            
            for i in range(rounds):
                start_time = time.time()
                
                # 选择CK
                ck = await ck_service.get_available_ck(merchant_id, f"test_req_{i}")
                
                selection_time = time.time() - start_time
                selection_times.append(selection_time)
                
                if ck:
                    selected_cks.append(ck.id)
                    print(f"轮次 {i+1:2d}: 选择CK {ck.id} ({selection_time*1000:.1f}ms)")
                    
                    # 记录使用结果
                    await ck_service.record_ck_usage(ck_id=ck.id, success=False)
                else:
                    selected_cks.append(None)
                    print(f"轮次 {i+1:2d}: ❌ 未找到可用CK")
                
                # 小延迟
                await asyncio.sleep(0.1)
            
            # 分析结果
            print("\n" + "="*60)
            print("📈 测试结果分析")
            print("="*60)
            
            valid_selections = [ck_id for ck_id in selected_cks if ck_id is not None]
            unique_count = len(set(valid_selections))
            distribution = dict(Counter(valid_selections))
            
            print(f"总测试轮数: {rounds}")
            print(f"成功选择次数: {len(valid_selections)}")
            print(f"成功率: {len(valid_selections)/rounds*100:.1f}%")
            print(f"选择的不同CK数: {unique_count}")
            print(f"选择序列: {selected_cks}")
            print(f"分布情况: {distribution}")
            
            # 性能分析
            if selection_times:
                avg_time = sum(selection_times) / len(selection_times)
                max_time = max(selection_times)
                min_time = min(selection_times)
                
                print(f"\n⏱️ 性能分析:")
                print(f"  平均选择时间: {avg_time*1000:.1f}ms")
                print(f"  最大选择时间: {max_time*1000:.1f}ms")
                print(f"  最小选择时间: {min_time*1000:.1f}ms")
            
            # 负载均衡分析
            if len(distribution) > 1:
                distribution_values = list(distribution.values())
                max_count = max(distribution_values)
                min_count = min(distribution_values)
                balance_score = (1 - (max_count - min_count) / max_count) * 100
                
                print(f"\n⚖️ 负载均衡分析:")
                print(f"  均衡分数: {balance_score:.1f}/100")
                
                if balance_score >= 80:
                    print("  ✅ 负载均衡优秀")
                elif balance_score >= 60:
                    print("  ✅ 负载均衡良好")
                elif balance_score >= 40:
                    print("  ⚠️ 负载均衡一般")
                else:
                    print("  ❌ 负载均衡较差")
            else:
                print(f"\n⚠️ 只选择了一个CK，无法评估负载均衡")
            
            # 获取统计信息
            stats = await ck_service.get_ck_statistics(merchant_id)
            print(f"\n📊 CK统计信息:")
            print(f"  总CK数: {stats['total_cks']}")
            print(f"  健康CK数: {stats['healthy_cks']}")
            
            for ck_detail in stats['ck_details']:
                status = "✅" if ck_detail['is_healthy'] else "❌"
                print(f"  CK {ck_detail['ck_id']}: {status} "
                      f"使用率 {ck_detail['usage_ratio']*100:.1f}% "
                      f"失败次数 {ck_detail['failure_count']}")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        db.close()


async def test_concurrent_requests(merchant_id=2, concurrent_count=10):
    """测试并发请求"""
    print(f"\n🔄 并发测试")
    print(f"商户ID: {merchant_id}, 并发数: {concurrent_count}")
    print("="*60)
    
    db = next(get_db())
    
    try:
        async def single_request(request_id: int):
            """单个请求"""
            async with ProductionCKService(db) as ck_service:
                ck = await ck_service.get_available_ck(merchant_id, f"concurrent_req_{request_id}")
                if ck:
                    await ck_service.record_ck_usage(ck_id=ck.id, success=False)
                    return ck.id
                return None
        
        # 创建并发任务
        tasks = [single_request(i) for i in range(concurrent_count)]
        
        # 执行并发请求
        start_time = time.time()
        results = await asyncio.gather(*tasks, return_exceptions=True)
        total_time = time.time() - start_time
        
        # 分析结果
        successful_results = []
        for result in results:
            if isinstance(result, Exception):
                print(f"  ❌ 请求失败: {result}")
            elif result is not None:
                successful_results.append(result)
        
        print(f"并发测试结果:")
        print(f"  总请求数: {concurrent_count}")
        print(f"  成功请求数: {len(successful_results)}")
        print(f"  成功率: {len(successful_results)/concurrent_count*100:.1f}%")
        print(f"  总耗时: {total_time:.3f}秒")
        print(f"  平均QPS: {concurrent_count/total_time:.1f}")
        
        if successful_results:
            distribution = dict(Counter(successful_results))
            unique_count = len(set(successful_results))
            print(f"  使用的不同CK数: {unique_count}")
            print(f"  CK分布: {distribution}")
    
    except Exception as e:
        print(f"❌ 并发测试失败: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        db.close()


async def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description="生产级CK服务测试")
    parser.add_argument("--merchant-id", type=int, default=2, help="商户ID")
    parser.add_argument("--rounds", type=int, default=10, help="测试轮数")
    parser.add_argument("--concurrent", type=int, default=10, help="并发测试数量")
    
    args = parser.parse_args()
    
    # 基础测试
    await test_production_service(args.merchant_id, args.rounds)
    
    # 并发测试
    await test_concurrent_requests(args.merchant_id, args.concurrent)
    
    print(f"\n🎉 测试完成！")


if __name__ == "__main__":
    asyncio.run(main())
