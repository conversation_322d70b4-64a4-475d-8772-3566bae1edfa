<template>
  <el-card class="ck-efficiency-card" shadow="hover">
    <template #header>
      <div class="card-header">
        <span>CK使用效率统计</span>
        <el-radio-group v-model="timeRange" size="small" @change="fetchData">
          <el-radio-button value="today">今日</el-radio-button>
          <el-radio-button value="week">本周</el-radio-button>
          <el-radio-button value="month">本月</el-radio-button>
        </el-radio-group>
      </div>
    </template>

    <div v-loading="loading" class="efficiency-content">
      <!-- 总体统计 -->
      <el-row :gutter="16" class="summary-row">
        <el-col :xs="12" :sm="6">
          <div class="summary-item">
            <div class="summary-value">{{ data.total_ck_count }}</div>
            <div class="summary-label">总CK数量</div>
          </div>
        </el-col>
        <el-col :xs="12" :sm="6">
          <div class="summary-item active">
            <div class="summary-value">{{ data.active_ck_count }}</div>
            <div class="summary-label">活跃CK数量</div>
          </div>
        </el-col>
        <el-col :xs="12" :sm="6">
          <div class="summary-item usage">
            <div class="summary-value">{{ data.total_usage }}</div>
            <div class="summary-label">总使用次数</div>
          </div>
        </el-col>
        <el-col :xs="12" :sm="6">
          <div class="summary-item efficiency">
            <div class="summary-value">{{ data.average_efficiency }}%</div>
            <div class="summary-label">平均效率</div>
          </div>
        </el-col>
      </el-row>

      <!-- CK详细列表 -->
      <div class="ck-details" v-if="data.ck_details && data.ck_details.length > 0">
        <h4>CK效率排行榜（前10名）</h4>
        <el-table :data="data.ck_details" size="small" stripe>
          <el-table-column prop="username" label="CK用户名" width="120" show-overflow-tooltip />
          <el-table-column label="状态" width="80" align="center">
            <template #default="scope">
              <el-tag :type="scope.row.status === 1 ? 'success' : 'danger'" size="small">
                {{ scope.row.status === 1 ? '活跃' : '禁用' }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="total_used" label="使用次数" width="100" align="right" />
          <el-table-column prop="success_count" label="成功次数" width="100" align="right" />
          <el-table-column label="效率" width="120" align="center">
            <template #default="scope">
              <el-progress 
                :percentage="scope.row.efficiency_rate" 
                :color="getEfficiencyColor(scope.row.efficiency_rate)"
                :format="() => `${scope.row.efficiency_rate}%`"
                :stroke-width="6"
              />
            </template>
          </el-table-column>
          <el-table-column label="平均处理时间" align="right">
            <template #default="scope">
              <span>{{ scope.row.avg_process_time }}s</span>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <!-- 无数据提示 -->
      <div v-else class="no-data">
        <el-empty description="暂无CK数据" />
      </div>
    </div>
  </el-card>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { dashboardApi } from '@/api/modules/dashboard'
import { useUserStore } from '@/store/modules/user'
import { usePermissionStore } from '@/store/modules/permission'
import { useMerchantStore } from '@/store/modules/merchant'

const userStore = useUserStore()
const permissionStore = usePermissionStore()
const merchantStore = useMerchantStore()

const loading = ref(false)
const timeRange = ref('today')

const data = reactive({
  total_ck_count: 0,
  active_ck_count: 0,
  total_usage: 0,
  average_efficiency: 0,
  ck_details: []
})

const getEfficiencyColor = (rate) => {
  if (rate >= 90) return '#67C23A'
  if (rate >= 70) return '#E6A23C'
  return '#F56C6C'
}

const fetchData = async () => {
  loading.value = true
  try {
    const params = {
      time_range: timeRange.value
    }
    
    // 根据用户角色确定查询参数
    if (permissionStore.isSuperAdmin) {
      if (merchantStore.currentMerchantId) {
        params.merchant_id = merchantStore.currentMerchantId
      }
    } else {
      params.merchant_id = userStore.merchantId
    }

    const response = await dashboardApi.getCkEfficiencyStatistics(params)
    Object.assign(data, response.data)
  } catch (error) {
    console.error('获取CK效率统计失败:', error)
  } finally {
    loading.value = false
  }
}

onMounted(() => {
  fetchData()
})

// 暴露刷新方法给父组件
defineExpose({
  refresh: fetchData
})
</script>

<style scoped>
.ck-efficiency-card {
  margin-bottom: 16px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 16px;
  font-weight: 600;
}

.efficiency-content {
  min-height: 300px;
}

.summary-row {
  margin-bottom: 20px;
}

.summary-item {
  text-align: center;
  padding: 16px 8px;
  border-radius: 8px;
  background: #f8f9fa;
  margin-bottom: 8px;
}

.summary-item.active {
  background: #f0f9ff;
  border-left: 4px solid #67C23A;
}

.summary-item.usage {
  background: #fff7ed;
  border-left: 4px solid #E6A23C;
}

.summary-item.efficiency {
  background: #f8f9ff;
  border-left: 4px solid #409EFF;
}

.summary-value {
  font-size: 20px;
  font-weight: bold;
  color: #303133;
  margin-bottom: 4px;
}

.summary-label {
  font-size: 12px;
  color: #606266;
}

.ck-details h4 {
  margin-bottom: 16px;
  color: #303133;
  font-size: 14px;
}

.no-data {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
}

@media (max-width: 768px) {
  .summary-item {
    margin-bottom: 12px;
  }
  
  .summary-value {
    font-size: 18px;
  }
}
</style>
