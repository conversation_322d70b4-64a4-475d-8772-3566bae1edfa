# 对账台数据权限修复报告

## 问题描述

用户 `bumen1dezibumen1` 在对账台页面能看到不应该看到的部门数据：
- 该用户属于 `bumen1` 的子部门
- 设置了"本部门及子部门数据权限"和"本人数据权限"
- 但是能看到"管理部门"等其他部门的统计数据

## 问题根本原因

在 `ReconciliationService.get_department_statistics` 方法中，**缺少了部门数据权限的检查**：

### 原有逻辑的问题

```python
# 原有代码 - 只有商户隔离，没有部门权限检查
async def get_department_statistics(self, current_user: User, ...):
    # 构建部门查询
    dept_query = self.db.query(Department)
    
    # 只应用了商户隔离
    if not current_user.is_superuser and current_user.merchant_id:
        dept_query = dept_query.filter(Department.merchant_id == current_user.merchant_id)
    
    # 获取部门列表 - 没有部门权限过滤！
    departments = dept_query.all()
```

### 问题分析

1. **商户隔离正常**: 用户只能看到自己商户的部门
2. **部门权限缺失**: 没有检查用户是否有权限访问这些部门
3. **权限配置被忽略**: 用户的 `data:department:sub` 权限没有生效

## 修复方案

### 1. 添加部门权限过滤逻辑

在 `get_department_statistics` 方法中添加部门权限过滤：

```python
# 修复后的代码
async def get_department_statistics(self, current_user: User, ...):
    # ... 原有的商户隔离逻辑 ...
    
    # 获取部门列表
    all_departments = dept_query.all()
    
    # 【修复】应用部门数据权限过滤
    departments = self._filter_departments_by_permission(all_departments, current_user)
```

### 2. 实现部门权限过滤方法

```python
def _filter_departments_by_permission(self, departments: List[Department], current_user: User) -> List[Department]:
    """根据用户的部门数据权限过滤部门列表"""
    try:
        # 超级管理员可以访问所有部门
        if current_user.is_superuser:
            return departments
        
        from app.services.permission_service import PermissionService
        permission_service = PermissionService(self.db)
        
        # 检查用户的部门数据权限
        if permission_service.check_data_permission(current_user, 'data:department:all'):
            # 有访问本商户所有部门数据的权限
            return [dept for dept in departments if dept.merchant_id == current_user.merchant_id]
        elif permission_service.check_data_permission(current_user, 'data:department:sub'):
            # 有访问本部门及子部门数据的权限
            accessible_departments = []
            for dept in departments:
                if permission_service.can_access_department_data(current_user, dept.id):
                    accessible_departments.append(dept)
            return accessible_departments
        elif permission_service.check_data_permission(current_user, 'data:department:own'):
            # 只有访问本部门数据的权限
            if hasattr(current_user, 'department_id') and current_user.department_id:
                return [dept for dept in departments if dept.id == current_user.department_id]
            return []
        else:
            # 没有部门数据权限，返回空列表
            logger.warning(f"用户 {current_user.id} 没有部门数据权限，无法访问对账台数据")
            return []
            
    except Exception as e:
        logger.error(f"过滤部门权限失败: {e}")
        # 出错时返回空列表，确保安全
        return []
```

### 3. 修复CK详细统计接口

同时修复了 `get_department_ck_statistics` 方法的权限检查：

```python
# 修复前 - 只有简单的商户检查
if not current_user.is_superuser and current_user.merchant_id != department.merchant_id:
    raise HTTPException(status_code=403, detail="无权限访问该部门数据")

# 修复后 - 完整的部门权限检查
if not current_user.is_superuser:
    # 首先检查商户隔离
    if current_user.merchant_id != department.merchant_id:
        raise HTTPException(status_code=403, detail="无权限访问该部门数据")
    
    # 然后检查部门数据权限
    permission_service = PermissionService(self.db)
    if not permission_service.can_access_department_data(current_user, department_id):
        raise HTTPException(status_code=403, detail="无权限访问该部门数据")
```

## 权限检查逻辑

### 部门数据权限层级

1. **`data:department:all`**: 可以访问本商户所有部门的数据
2. **`data:department:sub`**: 可以访问本部门及子部门的数据
3. **`data:department:own`**: 只能访问本部门的数据
4. **无权限**: 无法访问任何部门数据

### 权限验证流程

```
用户请求对账台数据
    ↓
1. 检查API权限 (api:reconciliation:read)
    ↓
2. 应用商户隔离 (merchant_id)
    ↓
3. 获取所有符合条件的部门
    ↓
4. 【新增】应用部门数据权限过滤
    ↓
5. 返回用户有权限访问的部门数据
```

## 修复效果

### 修复前
- ❌ 用户 `bumen1dezibumen1` 能看到"管理部门"等其他部门数据
- ❌ 部门数据权限配置不生效
- ❌ 存在数据泄露风险

### 修复后
- ✅ 用户 `bumen1dezibumen1` 只能看到自己部门及子部门的数据
- ✅ 部门数据权限配置正确生效
- ✅ 数据安全得到保障

## 安全增强

### 1. 防御性编程
- 权限检查失败时返回空列表
- 异常处理确保不会泄露数据
- 详细的日志记录便于审计

### 2. 多层权限验证
- API权限检查
- 商户隔离检查
- 部门数据权限检查

### 3. 权限最小化原则
- 用户只能访问明确授权的数据
- 默认拒绝访问策略

## 相关文件

### 修改的文件
- `app/services/reconciliation_service.py` - 添加部门权限过滤逻辑

### 相关文件（未修改）
- `app/services/permission_service.py` - 权限检查服务
- `app/api/v1/endpoints/reconciliation.py` - 对账台API接口
- `app/services/base_service.py` - 基础数据隔离逻辑

## 测试建议

### 1. 权限验证测试
- 使用 `bumen1dezibumen1` 账号登录
- 访问对账台页面
- 验证只能看到自己部门及子部门的数据

### 2. 边界情况测试
- 测试没有部门权限的用户
- 测试跨商户访问（应该被拒绝）
- 测试超级管理员访问（应该看到所有数据）

### 3. 性能测试
- 验证权限过滤不会显著影响性能
- 测试大量部门数据的过滤效率

## 总结

此次修复解决了对账台数据权限控制的关键问题：

1. **补全了缺失的部门权限检查**
2. **确保了数据权限配置的正确生效**
3. **提升了系统的数据安全性**
4. **遵循了权限最小化原则**

现在用户 `bumen1dezibumen1` 将只能看到自己有权限访问的部门数据，不会再看到其他部门的统计信息。
