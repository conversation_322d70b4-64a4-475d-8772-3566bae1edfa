#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速测试脚本
运行核心功能的基本测试，用于快速验证系统状态
"""

import sys
import os
import time

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(__file__)))

from test.conftest import TestBase, get_test_accounts, format_test_result, print_test_summary

class QuickTestSuite(TestBase):
    """快速测试套件"""
    
    def __init__(self):
        super().__init__()
        self.results = []
        self.test_accounts = get_test_accounts()
    
    def test_server_connectivity(self):
        """测试服务器连接"""
        print("=== 测试服务器连接 ===")
        
        try:
            # 尝试访问健康检查接口
            status_code, response = self.make_request("GET", "/health", None)
            
            if status_code == 200:
                self.results.append(format_test_result(
                    "服务器连接",
                    True,
                    "服务器连接正常"
                ))
                print("✅ 服务器连接正常")
            else:
                # 尝试访问登录接口作为备选
                status_code, response = self.make_request("POST", "/auth/login", None, 
                                                        data={"username": "test", "password": "test"})
                if status_code in [400, 401, 422]:  # 这些状态码说明服务器在运行
                    self.results.append(format_test_result(
                        "服务器连接",
                        True,
                        "服务器连接正常（通过登录接口验证）"
                    ))
                    print("✅ 服务器连接正常（通过登录接口验证）")
                else:
                    self.results.append(format_test_result(
                        "服务器连接",
                        False,
                        f"服务器连接异常，状态码: {status_code}"
                    ))
                    print(f"❌ 服务器连接异常，状态码: {status_code}")
        except Exception as e:
            self.results.append(format_test_result(
                "服务器连接",
                False,
                f"服务器连接失败: {str(e)}"
            ))
            print(f"❌ 服务器连接失败: {str(e)}")
    
    def test_basic_authentication(self):
        """测试基本认证功能"""
        print("\n=== 测试基本认证功能 ===")
        
        # 测试管理员登录
        admin_token = self.login("admin", "7c222fb2927d828af22f592134e8932480637c0d")
        if admin_token:
            self.results.append(format_test_result(
                "管理员登录",
                True,
                "管理员登录成功"
            ))
            print("✅ 管理员登录成功")
            
            # 测试获取用户信息
            status_code, response = self.get_user_info(admin_token)
            if status_code == 200:
                self.results.append(format_test_result(
                    "管理员Token验证",
                    True,
                    "管理员Token验证成功"
                ))
                print("✅ 管理员Token验证成功")
            else:
                self.results.append(format_test_result(
                    "管理员Token验证",
                    False,
                    f"Token验证失败，状态码: {status_code}"
                ))
                print(f"❌ Token验证失败，状态码: {status_code}")
        else:
            self.results.append(format_test_result(
                "管理员登录",
                False,
                "管理员登录失败"
            ))
            print("❌ 管理员登录失败")
        
        # 测试商户登录
        merchant_token = self.login("test1", "12345678")
        if merchant_token:
            self.results.append(format_test_result(
                "商户登录",
                True,
                "商户登录成功"
            ))
            print("✅ 商户登录成功")
        else:
            self.results.append(format_test_result(
                "商户登录",
                False,
                "商户登录失败"
            ))
            print("❌ 商户登录失败")
    
    def test_basic_api_access(self):
        """测试基本API访问"""
        print("\n=== 测试基本API访问 ===")

        # 先登录获取token
        admin_token = self.login("admin", "7c222fb2927d828af22f592134e8932480637c0d")
        if not admin_token:
            self.results.append(format_test_result(
                "API访问前置条件",
                False,
                "无法获取管理员token"
            ))
            return

        # 获取第一个商户ID用于测试
        merchant_id = self._get_first_merchant_id(admin_token)

        # 测试核心API接口
        api_tests = [
            ("/users", "用户列表", {}),
            ("/merchants", "商户列表", {}),
            ("/departments", "部门列表", {"merchant_id": merchant_id} if merchant_id else {}),
            ("/menus/user-menus", "用户菜单", {})
        ]

        for endpoint, name, params in api_tests:
            status_code, response = self.make_request("GET", endpoint, admin_token, params=params)

            if status_code == 200:
                self.results.append(format_test_result(
                    f"API访问_{name}",
                    True,
                    f"{name}接口访问正常"
                ))
                print(f"✅ {name}接口访问正常")
            else:
                self.results.append(format_test_result(
                    f"API访问_{name}",
                    False,
                    f"{name}接口访问失败，状态码: {status_code}, 响应: {response.get('detail', '无详细信息')}"
                ))
                print(f"❌ {name}接口访问失败，状态码: {status_code}, 响应: {response.get('detail', '无详细信息')}")
    
    def test_basic_permissions(self):
        """测试基本权限控制"""
        print("\n=== 测试基本权限控制 ===")
        
        # 获取商户token
        merchant_token = self.login("test1", "12345678")
        if not merchant_token:
            self.results.append(format_test_result(
                "权限测试前置条件",
                False,
                "无法获取商户token"
            ))
            return
        
        # 测试商户访问系统级接口（应该被拒绝）
        restricted_apis = [
            ("/roles", "角色管理"),
            ("/permissions", "权限管理")
        ]
        
        for endpoint, name in restricted_apis:
            status_code, response = self.make_request("GET", endpoint, merchant_token)
            
            if status_code in [401, 403]:
                self.results.append(format_test_result(
                    f"权限控制_{name}",
                    True,
                    f"正确拒绝商户访问{name}接口"
                ))
                print(f"✅ 正确拒绝商户访问{name}接口")
            else:
                self.results.append(format_test_result(
                    f"权限控制_{name}",
                    False,
                    f"商户不应该能访问{name}接口，状态码: {status_code}"
                ))
                print(f"❌ 商户不应该能访问{name}接口，状态码: {status_code}")
    
    def test_database_connectivity(self):
        """测试数据库连接"""
        print("\n=== 测试数据库连接 ===")
        
        # 通过API间接测试数据库连接
        admin_token = self.login("admin", "7c222fb2927d828af22f592134e8932480637c0d")
        if not admin_token:
            self.results.append(format_test_result(
                "数据库连接测试",
                False,
                "无法获取管理员token，可能数据库连接有问题"
            ))
            return
        
        # 尝试获取用户列表来验证数据库连接
        status_code, response = self.make_request("GET", "/users", admin_token)
        
        if status_code == 200:
            users_data = response.get("data", {})
            users = users_data.get("items", []) if isinstance(users_data, dict) else response.get("items", [])
            
            if len(users) > 0:
                self.results.append(format_test_result(
                    "数据库连接",
                    True,
                    f"数据库连接正常，找到 {len(users)} 个用户"
                ))
                print(f"✅ 数据库连接正常，找到 {len(users)} 个用户")
            else:
                self.results.append(format_test_result(
                    "数据库连接",
                    False,
                    "数据库连接正常但用户数据为空"
                ))
                print("⚠️ 数据库连接正常但用户数据为空")
        else:
            self.results.append(format_test_result(
                "数据库连接",
                False,
                f"获取用户列表失败，可能数据库连接有问题，状态码: {status_code}"
            ))
            print(f"❌ 获取用户列表失败，可能数据库连接有问题，状态码: {status_code}")

    def _get_first_merchant_id(self, token):
        """获取第一个商户ID用于测试"""
        try:
            status_code, response = self.make_request("GET", "/merchants", token)
            if status_code == 200:
                # 处理不同的响应格式
                merchants_data = response.get("data", response)
                if isinstance(merchants_data, dict):
                    merchants = merchants_data.get("items", [])
                elif isinstance(merchants_data, list):
                    merchants = merchants_data
                else:
                    merchants = []

                if merchants and len(merchants) > 0:
                    return merchants[0].get("id")
            return None
        except Exception as e:
            print(f"获取商户ID失败: {e}")
            return None

    def run_quick_tests(self):
        """运行快速测试"""
        print("开始沃尔玛绑卡系统快速测试")
        print("="*60)
        print("快速测试仅验证核心功能，完整测试请运行 run_all_tests.py")
        print("="*60)
        
        start_time = time.time()
        
        # 运行快速测试
        self.test_server_connectivity()
        self.test_basic_authentication()
        self.test_basic_api_access()
        self.test_basic_permissions()
        self.test_database_connectivity()
        
        end_time = time.time()
        duration = end_time - start_time
        
        # 打印测试摘要
        print_test_summary(self.results)
        print(f"测试耗时: {duration:.2f}秒")
        
        return self.results

def main():
    """主函数"""
    test_suite = QuickTestSuite()
    results = test_suite.run_quick_tests()
    
    # 判断测试是否通过
    failed_tests = [r for r in results if not r.get("success", False)]
    
    if not failed_tests:
        print("\n快速测试全部通过！系统基本功能正常。")
        print("如需完整测试，请运行: python run_all_tests.py")
        return 0
    else:
        print(f"\n发现 {len(failed_tests)} 个问题需要修复")
        for test in failed_tests:
            print(f"  - {test['test_name']}: {test['message']}")
        print("\n建议运行完整测试套件进行详细诊断: python run_all_tests.py")
        return 1

if __name__ == "__main__":
    sys.exit(main())
