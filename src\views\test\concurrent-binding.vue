<template>
  <div class="concurrent-test-container">
    <!-- 环境检测 -->
    <div v-if="!isTestEnvironment" class="env-warning">
      <el-alert
        title="功能不可用"
        type="error"
        :closable="false"
        show-icon
      >
        <template #default>
          并发测试功能仅在开发/测试环境中可用。当前环境：{{ environmentName }}
        </template>
      </el-alert>
    </div>

    <div v-else>
      <!-- 页面标题和说明 -->
      <div class="test-header">
        <h1>
          <el-icon><Monitor /></el-icon>
          并发绑卡测试
          <el-tag :type="environmentTagType" size="small" class="env-tag">
            {{ environmentName }}
          </el-tag>
        </h1>
        <el-alert
          title="测试环境专用功能"
          type="warning"
          :closable="false"
          show-icon
        >
          <template #default>
            此功能仅用于性能测试，启用debug模式时将跳过真实API调用。测试数据不会影响生产统计。
          </template>
        </el-alert>
      </div>

    <!-- 测试配置区域 -->
    <el-card class="config-card" shadow="hover">
      <template #header>
        <div class="card-header">
          <span><el-icon><Setting /></el-icon> 测试配置</span>
        </div>
      </template>

      <el-form :model="testConfig" :rules="configRules" ref="configFormRef" label-width="120px">
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="并发数量" prop="concurrentCount">
              <el-input-number
                v-model="testConfig.concurrentCount"
                :min="1"
                :max="100"
                :step="1"
                controls-position="right"
                style="width: 100%"
              />
              <div class="form-tip">建议从小数量开始测试</div>
            </el-form-item>
          </el-col>

          <el-col :span="8">
            <el-form-item label="Debug模式" prop="debugMode">
              <el-switch
                v-model="testConfig.debugMode"
                active-text="启用"
                inactive-text="禁用"
                active-color="#13ce66"
                inactive-color="#ff4949"
              />
              <div class="form-tip">启用后跳过真实API调用</div>
            </el-form-item>
          </el-col>

          <el-col :span="8">
            <el-form-item label="卡号前缀" prop="cardPrefix">
              <el-input
                v-model="testConfig.cardPrefix"
                placeholder="如: 2326"
                maxlength="10"
              />
              <div class="form-tip">测试卡号的前缀</div>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="测试金额" prop="amount">
              <el-input-number
                v-model="testConfig.amount"
                :min="100"
                :max="1000000"
                :step="100"
                controls-position="right"
                style="width: 100%"
              />
              <div class="form-tip">单位：分（100=1元）</div>
            </el-form-item>
          </el-col>

          <el-col :span="8">
            <el-form-item label="请求间隔" prop="requestDelay">
              <el-input-number
                v-model="testConfig.requestDelay"
                :min="0"
                :max="5000"
                :step="100"
                controls-position="right"
                style="width: 100%"
              />
              <div class="form-tip">毫秒，0表示无间隔</div>
            </el-form-item>
          </el-col>

          <el-col :span="8">
            <el-form-item label="超时时间" prop="timeout">
              <el-input-number
                v-model="testConfig.timeout"
                :min="5000"
                :max="60000"
                :step="1000"
                controls-position="right"
                style="width: 100%"
              />
              <div class="form-tip">毫秒</div>
            </el-form-item>
          </el-col>
        </el-row>

        <!-- API配置状态提示 -->
        <el-row v-if="testConfig.apiKey && testConfig.secretKey && testConfig.merchantCode">
          <el-col :span="24">
            <el-alert
              title="API配置已就绪"
              type="success"
              :closable="false"
              show-icon
            >
              <template #default>
                商户编码: {{ testConfig.merchantCode }} | API密钥: {{ testConfig.apiKey.substring(0, 8) }}***
              </template>
            </el-alert>
          </el-col>
        </el-row>

        <el-row v-else>
          <el-col :span="24">
            <el-alert
              title="请配置API信息"
              type="warning"
              :closable="false"
              show-icon
            >
              <template #default>
                请点击"自动填充API配置"按钮获取商户API信息，或手动配置API密钥
              </template>
            </el-alert>
          </el-col>
        </el-row>

        <el-form-item>
          <el-button
            type="success"
            @click="autoFillApiCredentials(userStore.isSuperAdmin && merchantStore.currentMerchant ? merchantStore.currentMerchant.id : null)"
            :loading="autoFillLoading"
            size="large"
          >
            <el-icon><Refresh /></el-icon>
            {{ autoFillLoading ? '获取中...' : '自动填充API配置' }}
          </el-button>

          <el-button
            type="primary"
            @click="startTest"
            :loading="testing"
            :disabled="testing"
            size="large"
          >
            <el-icon><VideoPlay /></el-icon>
            {{ testing ? '测试进行中...' : '开始测试' }}
          </el-button>

          <el-button
            v-if="testing"
            type="danger"
            @click="stopTest"
            size="large"
          >
            <el-icon><VideoPause /></el-icon>
            停止测试
          </el-button>

          <el-button
            v-if="testResults.length > 0"
            @click="clearResults"
            size="large"
          >
            <el-icon><Delete /></el-icon>
            清空结果
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 实时状态显示 -->
    <el-card v-if="testing || testResults.length > 0" class="status-card" shadow="hover">
      <template #header>
        <div class="card-header">
          <span><el-icon><DataAnalysis /></el-icon> 测试状态</span>
        </div>
      </template>
      
      <div v-if="testing" class="progress-section">
        <div class="progress-info">
          <span>测试进度：{{ completedCount }}/{{ testConfig.concurrentCount }}</span>
          <span class="elapsed-time">已用时：{{ elapsedTime }}s</span>
        </div>
        <el-progress
          :percentage="progressPercentage"
          :status="progressStatus"
          :stroke-width="8"
        />
      </div>
      
      <!-- 结果统计面板 -->
      <div v-if="testResults.length > 0" class="stats-panel">
        <el-row :gutter="20">
          <el-col :span="4">
            <div class="stat-item success">
              <div class="stat-value">{{ statistics.successCount }}</div>
              <div class="stat-label">成功</div>
            </div>
          </el-col>
          <el-col :span="4">
            <div class="stat-item error">
              <div class="stat-value">{{ statistics.failureCount }}</div>
              <div class="stat-label">失败</div>
            </div>
          </el-col>
          <el-col :span="4">
            <div class="stat-item info">
              <div class="stat-value">{{ statistics.successRate }}%</div>
              <div class="stat-label">成功率</div>
            </div>
          </el-col>
          <el-col :span="4">
            <div class="stat-item primary">
              <div class="stat-value">{{ statistics.avgResponseTime }}ms</div>
              <div class="stat-label">平均响应</div>
            </div>
          </el-col>
          <el-col :span="4">
            <div class="stat-item warning">
              <div class="stat-value">{{ statistics.qps }}</div>
              <div class="stat-label">QPS</div>
            </div>
          </el-col>
          <el-col :span="4">
            <div class="stat-item">
              <div class="stat-value">{{ statistics.totalTime }}s</div>
              <div class="stat-label">总耗时</div>
            </div>
          </el-col>
        </el-row>
      </div>
    </el-card>

    <!-- 详细结果列表 -->
    <el-card v-if="testResults.length > 0" class="results-card" shadow="hover">
      <template #header>
        <div class="card-header">
          <span><el-icon><List /></el-icon> 详细结果</span>
          <div class="header-actions">
            <el-button size="small" @click="exportResults">
              <el-icon><Download /></el-icon>
              导出结果
            </el-button>
          </div>
        </div>
      </template>
      
      <el-table
        :data="paginatedResults"
        stripe
        border
        height="400"
        v-loading="testing"
      >
        <el-table-column prop="index" label="序号" width="60" align="center" />
        <el-table-column prop="cardNumber" label="卡号" width="180">
          <template #default="{ row }">
            <span class="card-number">{{ formatCardNumber(row.cardNumber) }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" width="80" align="center">
          <template #default="{ row }">
            <el-tag :type="row.success ? 'success' : 'danger'" size="small">
              {{ row.success ? '成功' : '失败' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="responseTime" label="响应时间" width="100" align="center">
          <template #default="{ row }">
            <span :class="getResponseTimeClass(row.responseTime)">
              {{ row.responseTime }}ms
            </span>
          </template>
        </el-table-column>
        <el-table-column prop="debugMode" label="模式" width="80" align="center">
          <template #default="{ row }">
            <el-tag :type="row.debugMode ? 'warning' : 'info'" size="small">
              {{ row.debugMode ? 'TEST' : 'PROD' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="timestamp" label="时间" width="160">
          <template #default="{ row }">
            {{ formatTime(row.timestamp) }}
          </template>
        </el-table-column>
        <el-table-column prop="error" label="错误信息" min-width="200">
          <template #default="{ row }">
            <span v-if="row.error" class="error-message">{{ row.error }}</span>
            <span v-else class="success-message">{{ row.message || '请求成功' }}</span>
          </template>
        </el-table-column>
      </el-table>
      
      <div class="pagination-wrapper">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="testResults.length"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 测试历史记录 -->
    <el-card v-if="testHistory.length > 0" class="history-card" shadow="hover">
      <template #header>
        <div class="card-header">
          <span><el-icon><Clock /></el-icon> 测试历史</span>
          <div class="header-actions">
            <el-button size="small" @click="clearHistory">
              <el-icon><Delete /></el-icon>
              清空历史
            </el-button>
          </div>
        </div>
      </template>
      
      <el-table :data="testHistory" stripe>
        <el-table-column prop="timestamp" label="测试时间" width="160">
          <template #default="{ row }">
            {{ formatTime(row.timestamp) }}
          </template>
        </el-table-column>
        <el-table-column prop="concurrentCount" label="并发数" width="80" align="center" />
        <el-table-column prop="debugMode" label="模式" width="80" align="center">
          <template #default="{ row }">
            <el-tag :type="row.debugMode ? 'warning' : 'info'" size="small">
              {{ row.debugMode ? 'TEST' : 'PROD' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="successRate" label="成功率" width="80" align="center">
          <template #default="{ row }">
            <span :class="getSuccessRateClass(row.successRate)">
              {{ row.successRate }}%
            </span>
          </template>
        </el-table-column>
        <el-table-column prop="avgResponseTime" label="平均响应" width="100" align="center">
          <template #default="{ row }">
            {{ row.avgResponseTime }}ms
          </template>
        </el-table-column>
        <el-table-column prop="qps" label="QPS" width="80" align="center" />
        <el-table-column prop="totalTime" label="总耗时" width="80" align="center">
          <template #default="{ row }">
            {{ row.totalTime }}s
          </template>
        </el-table-column>
        <el-table-column label="操作" width="100" align="center">
          <template #default="{ row, $index }">
            <el-button
              size="small"
              type="primary"
              link
              @click="loadHistoryTest(row, $index)"
            >
              查看详情
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, onUnmounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Monitor, Setting, VideoPlay, VideoPause, Delete, DataAnalysis,
  List, Download, Clock, Refresh
} from '@element-plus/icons-vue'
import { useUserStore } from '@/store/modules/user'
import { useMerchantStore } from '@/store/modules/merchant'
import { merchantApi } from '@/api/modules/merchant'
import merchantSwitchListener from '@/utils/merchantSwitchListener'
import {
  executeConcurrentTest,
  calculateTestStatistics,
  exportResultsToCSV,
  validateTestConfig
} from '@/api/modules/concurrentTest'
import {
  isTestFeatureEnabled,
  getEnvironmentName,
  isDevelopment
} from '@/utils/envCheck'

// 存储和状态
const userStore = useUserStore()
const merchantStore = useMerchantStore()

// 响应式数据
const configFormRef = ref()
const testing = ref(false)
const testResults = ref([])
const testHistory = ref([])
const completedCount = ref(0)
const startTime = ref(null)
const elapsedTime = ref(0)
const currentPage = ref(1)
const pageSize = ref(20)
const autoFillLoading = ref(false)
let testTimer = null
let elapsedTimer = null
let unregisterMerchantSwitch = null

// 测试配置
const testConfig = reactive({
  concurrentCount: 10,
  debugMode: true,
  cardPrefix: '2326',
  amount: 10000, // 100元
  requestDelay: 0,
  timeout: 30000,
  merchantCode: '',
  apiKey: '',
  secretKey: ''
})

// 表单验证规则
const configRules = {
  concurrentCount: [
    { required: true, message: '请输入并发数量', trigger: 'blur' },
    { type: 'number', min: 1, max: 100, message: '并发数量必须在1-100之间', trigger: 'blur' }
  ],
  cardPrefix: [
    { required: true, message: '请输入卡号前缀', trigger: 'blur' },
    { min: 2, max: 10, message: '卡号前缀长度在2-10个字符', trigger: 'blur' }
  ],
  amount: [
    { required: true, message: '请输入测试金额', trigger: 'blur' },
    { type: 'number', min: 100, message: '金额不能少于100分（1元）', trigger: 'blur' }
  ]
}

// 计算属性
const progressPercentage = computed(() => {
  if (testConfig.concurrentCount === 0) return 0
  return Math.round((completedCount.value / testConfig.concurrentCount) * 100)
})

const progressStatus = computed(() => {
  if (testing.value) return 'active'
  if (completedCount.value === testConfig.concurrentCount) return 'success'
  return 'normal'
})

const statistics = computed(() => {
  return calculateTestStatistics(testResults.value, elapsedTime.value)
})

const paginatedResults = computed(() => {
  const start = (currentPage.value - 1) * pageSize.value
  const end = start + pageSize.value
  return testResults.value.slice(start, end)
})

// 环境检测
const isTestEnvironment = computed(() => isTestFeatureEnabled())
const environmentName = computed(() => getEnvironmentName())
const environmentTagType = computed(() => {
  if (isDevelopment()) return 'success'
  return 'warning'
})



// 开始测试
const startTest = async () => {
  // 表单验证
  if (!configFormRef.value) return

  const valid = await configFormRef.value.validate().catch(() => false)
  if (!valid) {
    ElMessage.error('请完成表单配置')
    return
  }

  // 使用API模块验证配置
  const validation = validateTestConfig(testConfig)
  if (!validation.valid) {
    ElMessage.error(validation.errors.join('; '))
    return
  }

  // 频率限制检查
  const lastTestTime = localStorage.getItem('lastConcurrentTestTime')
  if (lastTestTime) {
    const timeDiff = Date.now() - parseInt(lastTestTime)
    if (timeDiff < 10000) { // 10秒限制
      ElMessage.warning('请等待10秒后再进行测试')
      return
    }
  }

  try {
    testing.value = true
    testResults.value = []
    completedCount.value = 0
    startTime.value = Date.now()
    elapsedTime.value = 0

    // 记录测试时间
    localStorage.setItem('lastConcurrentTestTime', String(Date.now()))

    // 启动计时器
    elapsedTimer = setInterval(() => {
      elapsedTime.value = (Date.now() - startTime.value) / 1000
    }, 100)

    ElMessage.success(`开始并发测试，共${testConfig.concurrentCount}个请求`)

    // 使用API模块执行并发测试
    const results = await executeConcurrentTest(testConfig, (result) => {
      // 进度回调
      testResults.value.push(result)
      completedCount.value = testResults.value.length
    })

    // 如果没有通过进度回调获取结果，则直接设置结果
    if (testResults.value.length === 0) {
      testResults.value = results
      completedCount.value = results.length
    }

    // 保存测试历史
    saveTestHistory()

    ElMessage.success('并发测试完成')

  } catch (error) {
    console.error('测试执行失败:', error)
    ElMessage.error('测试执行失败: ' + error.message)
  } finally {
    testing.value = false
    if (elapsedTimer) {
      clearInterval(elapsedTimer)
      elapsedTimer = null
    }
  }
}

// 停止测试
const stopTest = () => {
  testing.value = false
  if (elapsedTimer) {
    clearInterval(elapsedTimer)
    elapsedTimer = null
  }
  ElMessage.warning('测试已停止')
}

// 清空结果
const clearResults = () => {
  testResults.value = []
  completedCount.value = 0
  currentPage.value = 1
}

// 保存测试历史
const saveTestHistory = () => {
  const historyItem = {
    timestamp: Date.now(),
    concurrentCount: testConfig.concurrentCount,
    debugMode: testConfig.debugMode,
    successRate: statistics.value.successRate,
    avgResponseTime: statistics.value.avgResponseTime,
    qps: statistics.value.qps,
    totalTime: statistics.value.totalTime,
    results: [...testResults.value] // 深拷贝结果
  }

  testHistory.value.unshift(historyItem)

  // 只保留最近10次测试
  if (testHistory.value.length > 10) {
    testHistory.value = testHistory.value.slice(0, 10)
  }

  // 保存到localStorage
  localStorage.setItem('concurrentTestHistory', JSON.stringify(testHistory.value))
}

// 加载历史测试
const loadHistoryTest = (historyItem, index) => {
  testResults.value = [...historyItem.results]
  completedCount.value = historyItem.results.length
  ElMessage.success(`已加载第${index + 1}次测试结果`)
}

// 清空历史
const clearHistory = () => {
  testHistory.value = []
  localStorage.removeItem('concurrentTestHistory')
  ElMessage.success('历史记录已清空')
}

// 导出结果
const exportResults = () => {
  if (testResults.value.length === 0) {
    ElMessage.warning('没有测试结果可导出')
    return
  }

  const csvContent = exportResultsToCSV(testResults.value, statistics.value)

  const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' })
  const link = document.createElement('a')
  const url = URL.createObjectURL(blob)
  link.setAttribute('href', url)
  link.setAttribute('download', `concurrent_test_${Date.now()}.csv`)
  link.style.visibility = 'hidden'
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)

  ElMessage.success('测试结果已导出')
}

// 自动填充API配置
const autoFillApiCredentials = async (merchantId = null) => {
  try {
    autoFillLoading.value = true
    let apiCredentials = null

    if (userStore.isSuperAdmin && merchantId) {
      // 超级管理员获取指定商家的API密钥
      apiCredentials = await merchantApi.getApiCredentials(merchantId)
    } else {
      // 商家用户获取自己商家的API密钥
      apiCredentials = await merchantApi.getCurrentMerchantApiCredentials()
    }

    if (apiCredentials) {
      testConfig.apiKey = apiCredentials.api_key || ''
      testConfig.secretKey = apiCredentials.api_secret || ''
      testConfig.merchantCode = apiCredentials.merchant_code || ''

      ElMessage.success(`已自动填充 ${apiCredentials.merchant_name} 的API密钥信息`)
    } else {
      ElMessage.warning('未获取到API配置信息')
    }
  } catch (error) {
    console.error('获取API密钥失败:', error)
    ElMessage.error('获取API密钥失败: ' + (error.response?.data?.detail || error.message))
  } finally {
    autoFillLoading.value = false
  }
}

// 监听商家切换事件（仅超级管理员）
const handleMerchantSwitch = async () => {
  if (userStore.isSuperAdmin && merchantStore.currentMerchant) {
    await autoFillApiCredentials(merchantStore.currentMerchant.id)
  }
}

// 格式化函数
const formatCardNumber = (cardNumber) => {
  if (!cardNumber) return ''
  return cardNumber.replace(/(\d{4})(?=\d)/g, '$1 ')
}

const formatTime = (timestamp) => {
  return new Date(timestamp).toLocaleString()
}

const getResponseTimeClass = (responseTime) => {
  if (responseTime < 1000) return 'response-fast'
  if (responseTime < 3000) return 'response-normal'
  return 'response-slow'
}

const getSuccessRateClass = (successRate) => {
  if (successRate >= 95) return 'success-rate-excellent'
  if (successRate >= 80) return 'success-rate-good'
  return 'success-rate-poor'
}

// 分页处理
const handleSizeChange = (val) => {
  pageSize.value = val
  currentPage.value = 1
}

const handleCurrentChange = (val) => {
  currentPage.value = val
}

// 生命周期
onMounted(async () => {
  // 注册商家切换监听器
  if (userStore.isSuperAdmin) {
    unregisterMerchantSwitch = merchantSwitchListener.registerRefreshFunction(handleMerchantSwitch)
  }

  // 自动填充API密钥（如果是商家用户或超级管理员已选择商家）
  if (!userStore.isSuperAdmin || (userStore.isSuperAdmin && merchantStore.currentMerchant)) {
    await autoFillApiCredentials(userStore.isSuperAdmin && merchantStore.currentMerchant ? merchantStore.currentMerchant.id : null)
  }

  // 加载历史记录
  const savedHistory = localStorage.getItem('concurrentTestHistory')
  if (savedHistory) {
    try {
      testHistory.value = JSON.parse(savedHistory)
    } catch (error) {
      console.error('加载历史记录失败:', error)
    }
  }
})

onUnmounted(() => {
  if (elapsedTimer) {
    clearInterval(elapsedTimer)
  }
  if (unregisterMerchantSwitch) {
    unregisterMerchantSwitch()
  }
})
</script>

<style scoped>
.concurrent-test-container {
  padding: 20px;
  max-width: 1400px;
  margin: 0 auto;
}

.test-header {
  margin-bottom: 20px;
}

.test-header h1 {
  display: flex;
  align-items: center;
  gap: 10px;
  margin: 0 0 15px 0;
  color: #303133;
  font-size: 24px;
  font-weight: 600;
}

.env-warning {
  margin-bottom: 20px;
}

.env-tag {
  margin-left: 10px;
}

.config-card,
.status-card,
.results-card,
.history-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-weight: 600;
}

.card-header span {
  display: flex;
  align-items: center;
  gap: 8px;
}

.header-actions {
  display: flex;
  gap: 10px;
}

.form-tip {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
}

.progress-section {
  margin-bottom: 20px;
}

.progress-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
  font-size: 14px;
  color: #606266;
}

.elapsed-time {
  font-weight: 600;
  color: #409eff;
}

.stats-panel {
  margin-top: 20px;
}

.stat-item {
  text-align: center;
  padding: 20px;
  border-radius: 8px;
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  transition: all 0.3s ease;
}

.stat-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.stat-item.success {
  background: linear-gradient(135deg, #67c23a, #85ce61);
  color: white;
  border-color: #67c23a;
}

.stat-item.error {
  background: linear-gradient(135deg, #f56c6c, #f78989);
  color: white;
  border-color: #f56c6c;
}

.stat-item.info {
  background: linear-gradient(135deg, #909399, #a6a9ad);
  color: white;
  border-color: #909399;
}

.stat-item.primary {
  background: linear-gradient(135deg, #409eff, #66b1ff);
  color: white;
  border-color: #409eff;
}

.stat-item.warning {
  background: linear-gradient(135deg, #e6a23c, #ebb563);
  color: white;
  border-color: #e6a23c;
}

.stat-value {
  font-size: 24px;
  font-weight: 700;
  margin-bottom: 5px;
}

.stat-label {
  font-size: 14px;
  opacity: 0.9;
}

.card-number {
  font-family: 'Courier New', monospace;
  font-weight: 600;
}

.response-fast {
  color: #67c23a;
  font-weight: 600;
}

.response-normal {
  color: #e6a23c;
  font-weight: 600;
}

.response-slow {
  color: #f56c6c;
  font-weight: 600;
}

.success-rate-excellent {
  color: #67c23a;
  font-weight: 600;
}

.success-rate-good {
  color: #e6a23c;
  font-weight: 600;
}

.success-rate-poor {
  color: #f56c6c;
  font-weight: 600;
}

.error-message {
  color: #f56c6c;
  font-size: 12px;
}

.success-message {
  color: #67c23a;
  font-size: 12px;
}

.pagination-wrapper {
  margin-top: 20px;
  display: flex;
  justify-content: center;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .concurrent-test-container {
    padding: 10px;
  }

  .stat-item {
    margin-bottom: 10px;
  }

  .stat-value {
    font-size: 18px;
  }

  .card-header {
    flex-direction: column;
    gap: 10px;
    align-items: flex-start;
  }

  .header-actions {
    width: 100%;
    justify-content: flex-end;
  }
}

/* 动画效果 */
.el-card {
  transition: all 0.3s ease;
}

.el-card:hover {
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.el-progress {
  margin: 10px 0;
}

.el-table {
  font-size: 14px;
}

.el-table .el-table__cell {
  padding: 8px 0;
}

/* 测试状态指示器 */
.testing-indicator {
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 9999;
  background: #409eff;
  color: white;
  padding: 10px 20px;
  border-radius: 20px;
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.3);
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    box-shadow: 0 4px 12px rgba(64, 158, 255, 0.3);
  }
  50% {
    box-shadow: 0 4px 20px rgba(64, 158, 255, 0.6);
  }
  100% {
    box-shadow: 0 4px 12px rgba(64, 158, 255, 0.3);
  }
}
</style>
