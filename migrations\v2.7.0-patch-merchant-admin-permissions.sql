-- ========================================
-- 对账台功能权限迁移脚本
-- 为对账台模块添加完整的权限配置
-- ========================================

USE `walmart_card_db`;

-- ========================================
-- 1. 记录迁移开始
-- ========================================

INSERT INTO `migration_logs` (`migration_name`, `status`, `message`, `created_at`)
VALUES ('v2.7.0-patch-merchant-admin-permissions', 'started', '开始执行对账台权限迁移', NOW());

-- ========================================
-- 2. 清理可能存在的错误权限配置
-- ========================================

DELETE FROM `role_menus` WHERE `menu_id` IN (
    SELECT id FROM `menus` WHERE code LIKE 'reconciliation%'
);
DELETE FROM `role_permissions` WHERE `permission_id` IN (
    SELECT id FROM `permissions` WHERE code LIKE '%reconciliation%'
);
DELETE FROM `menus` WHERE code LIKE 'reconciliation%';
DELETE FROM `permissions` WHERE code LIKE '%reconciliation%';

-- ========================================
-- 3. 创建对账台菜单
-- ========================================

INSERT INTO `menus` (
    `name`, `code`, `path`, `component`, `icon`, `parent_id`,
    `level`, `sort_order`, `is_visible`, `is_enabled`, `menu_type`,
    `description`, `created_at`
) VALUES (
    '对账台', 'reconciliation', '/reconciliation', 'Reconciliation', 'money-collect', NULL,
    1, 5, 1, 1, 'menu',
    '绑卡数据对账统计模块',
    NOW()
);

-- ========================================
-- 4. 创建对账台权限
-- ========================================

-- 菜单权限
INSERT IGNORE INTO `permissions` (
    `code`,
    `name`,
    `description`,
    `resource_type`,
    `resource_path`,
    `is_enabled`,
    `sort_order`
) VALUES (
    'menu:reconciliation',
    '对账台菜单权限',
    '对账台菜单访问权限',
    'menu',
    '/reconciliation',
    1,
    1200
);

-- API模块权限
INSERT IGNORE INTO `permissions` (
    `code`,
    `name`,
    `description`,
    `resource_type`,
    `resource_path`,
    `is_enabled`,
    `sort_order`
) VALUES (
    'api:/api/v1/reconciliation',
    '对账台API模块',
    '对账台相关API模块权限',
    'api',
    '/api/v1/reconciliation',
    1,
    1201
);

-- 对账台查看权限
INSERT IGNORE INTO `permissions` (
    `code`,
    `name`,
    `description`,
    `resource_type`,
    `resource_path`,
    `is_enabled`,
    `sort_order`
) VALUES (
    'api:reconciliation:read',
    '查看对账台数据',
    '允许查看对账台统计数据，包括部门统计、CK统计、绑卡记录等',
    'api',
    '/api/v1/reconciliation',
    1,
    1202
);

-- 对账台导出权限
INSERT IGNORE INTO `permissions` (
    `code`,
    `name`,
    `description`,
    `resource_type`,
    `resource_path`,
    `is_enabled`,
    `sort_order`
) VALUES (
    'api:reconciliation:export',
    '导出对账台数据',
    '允许导出对账台数据为Excel格式，支持部门统计、CK统计、绑卡记录导出',
    'api',
    '/api/v1/reconciliation/export',
    1,
    1203
);

-- 【修复】权限映射逻辑已修复
-- 现在所有对账台API路径都会映射到统一的 api:/api/v1/reconciliation 权限
-- 因此不需要添加额外的权限定义，现有的权限配置已经足够

-- ========================================
-- 5. 为超级管理员分配权限
-- ========================================

-- 分配对账台菜单权限给超级管理员
INSERT IGNORE INTO `role_menus` (`role_id`, `menu_id`)
SELECT r.id, m.id
FROM `roles` r
CROSS JOIN `menus` m
WHERE r.code = 'super_admin'
  AND m.code = 'reconciliation';

-- 分配所有对账台权限给超级管理员
INSERT IGNORE INTO `role_permissions` (`role_id`, `permission_id`)
SELECT r.id, p.id
FROM `roles` r
CROSS JOIN `permissions` p
WHERE r.code = 'super_admin'
  AND p.code LIKE '%reconciliation%';

-- ========================================
-- 6. 为商户管理员分配权限
-- ========================================

-- 分配对账台菜单权限给商户管理员
INSERT IGNORE INTO `role_menus` (`role_id`, `menu_id`)
SELECT r.id, m.id
FROM `roles` r
CROSS JOIN `menus` m
WHERE r.code = 'merchant_admin'
  AND m.code = 'reconciliation';

-- 分配对账台权限给商户管理员
INSERT IGNORE INTO `role_permissions` (`role_id`, `permission_id`)
SELECT r.id, p.id
FROM `roles` r
CROSS JOIN `permissions` p
WHERE r.code = 'merchant_admin'
  AND p.code IN (
    'menu:reconciliation',
    'api:/api/v1/reconciliation',
    'api:reconciliation:read',
    'api:reconciliation:export'
  );

-- ========================================
-- 7. 为操作员分配只读权限
-- ========================================

-- 分配对账台菜单权限给操作员
INSERT IGNORE INTO `role_menus` (`role_id`, `menu_id`)
SELECT r.id, m.id
FROM `roles` r
CROSS JOIN `menus` m
WHERE r.code = 'operator'
  AND m.code = 'reconciliation';

-- 分配对账台只读权限给操作员
INSERT IGNORE INTO `role_permissions` (`role_id`, `permission_id`)
SELECT r.id, p.id
FROM `roles` r
CROSS JOIN `permissions` p
WHERE r.code = 'operator'
  AND p.code IN (
    'menu:reconciliation',
    'api:/api/v1/reconciliation',
    'api:reconciliation:read'
  );

-- ========================================
-- 8. 验证权限配置
-- ========================================

-- 检查对账台权限是否正确插入
SELECT
    '对账台权限检查' as check_type,
    COUNT(*) as permission_count,
    CASE
        WHEN COUNT(*) >= 4 THEN '✓ 对账台权限配置完整'
        ELSE '⚠ 对账台权限配置不完整'
    END as status
FROM `permissions`
WHERE code LIKE '%reconciliation%';

-- 检查商户管理员权限分配
SELECT
    '商户管理员对账台权限' as check_type,
    COUNT(*) as assigned_permissions,
    CASE
        WHEN COUNT(*) >= 4 THEN '✓ 商户管理员权限分配完整'
        ELSE '⚠ 商户管理员权限分配不完整'
    END as status
FROM `role_permissions` rp
JOIN `roles` r ON rp.role_id = r.id
JOIN `permissions` p ON rp.permission_id = p.id
WHERE r.code = 'merchant_admin'
AND p.code LIKE '%reconciliation%';

-- 检查操作员权限分配
SELECT
    '操作员对账台权限' as check_type,
    COUNT(*) as assigned_permissions,
    CASE
        WHEN COUNT(*) >= 3 THEN '✓ 操作员权限分配完整'
        ELSE '⚠ 操作员权限分配不完整'
    END as status
FROM `role_permissions` rp
JOIN `roles` r ON rp.role_id = r.id
JOIN `permissions` p ON rp.permission_id = p.id
WHERE r.code = 'operator'
AND p.code LIKE '%reconciliation%';

-- ========================================
-- 9. 显示权限分配结果
-- ========================================

SELECT
    '=== 对账台权限分配结果 ===' as summary,
    r.name as role_name,
    r.code as role_code,
    p.name as permission_name,
    p.code as permission_code
FROM `role_permissions` rp
JOIN `roles` r ON rp.role_id = r.id
JOIN `permissions` p ON rp.permission_id = p.id
WHERE p.code LIKE '%reconciliation%'
ORDER BY r.code, p.code;

-- ========================================
-- 10. 记录迁移完成
-- ========================================

UPDATE `migration_logs`
SET `status` = 'completed',
    `message` = '对账台权限迁移执行完成',
    `completed_at` = NOW()
WHERE `migration_name` = 'v2.7.0-patch-merchant-admin-permissions'
AND `status` = 'started';

-- ========================================
-- 11. 迁移完成提示
-- ========================================

SELECT
    '🎉 迁移完成' as status,
    '对账台权限已成功配置，API路径映射逻辑已修复' as message,
    '所有对账台API现在统一映射到 api:/api/v1/reconciliation 权限' as fix_details,
    '超级管理员、商户管理员、操作员' as assigned_roles;
