import { http } from '@/api/request'
import { API_URLS, replaceUrlParams } from './config'

const { RECONCILIATION } = API_URLS

/**
 * 对账台相关API
 */
export const reconciliationApi = {
    /**
     * 获取部门绑卡统计数据
     * @param {object} params 查询参数
     * @returns {Promise<object>} API响应数据
     */
    getDepartmentStatistics(params = {}) {
        return http.get(RECONCILIATION.DEPARTMENT_STATISTICS, { params }).then(res => res.data || res)
    },

    /**
     * 获取部门下CK的统计数据
     * @param {number} departmentId 部门ID
     * @param {object} params 查询参数
     * @returns {Promise<object>} API响应数据
     */
    getCkStatistics(departmentId, params = {}) {
        const url = replaceUrlParams(RECONCILIATION.CK_STATISTICS, { departmentId })
        return http.get(url, { params }).then(res => res.data || res)
    },

    /**
     * 获取CK的成功绑卡记录明细
     * @param {number} ckId CK ID
     * @param {object} params 查询参数
     * @returns {Promise<object>} API响应数据
     */
    getBindingRecords(ckId, params = {}) {
        const url = replaceUrlParams(RECONCILIATION.BINDING_RECORDS, { ckId })
        return http.get(url, { params }).then(res => res.data || res)
    },

    /**
     * 导出部门统计数据
     * @param {object} params 查询参数
     * @returns {Promise<Blob>} Excel文件数据
     */
    exportDepartmentStatistics(params = {}) {
        return http.get(RECONCILIATION.EXPORT_DEPARTMENTS, { 
            params,
            responseType: 'blob'
        }).then(res => {
            // 创建下载链接
            const blob = new Blob([res.data], { 
                type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' 
            })
            const url = window.URL.createObjectURL(blob)
            const link = document.createElement('a')
            link.href = url
            link.download = `department_statistics_${new Date().getTime()}.xlsx`
            document.body.appendChild(link)
            link.click()
            document.body.removeChild(link)
            window.URL.revokeObjectURL(url)
            return res
        })
    },

    /**
     * 导出CK统计数据
     * @param {number} departmentId 部门ID
     * @param {object} params 查询参数
     * @returns {Promise<Blob>} Excel文件数据
     */
    exportCkStatistics(departmentId, params = {}) {
        const url = replaceUrlParams(RECONCILIATION.EXPORT_CK, { departmentId })
        return http.get(url, { 
            params,
            responseType: 'blob'
        }).then(res => {
            // 创建下载链接
            const blob = new Blob([res.data], { 
                type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' 
            })
            const downloadUrl = window.URL.createObjectURL(blob)
            const link = document.createElement('a')
            link.href = downloadUrl
            link.download = `ck_statistics_${departmentId}_${new Date().getTime()}.xlsx`
            document.body.appendChild(link)
            link.click()
            document.body.removeChild(link)
            window.URL.revokeObjectURL(downloadUrl)
            return res
        })
    },

    /**
     * 导出绑卡记录数据
     * @param {number} ckId CK ID
     * @param {object} params 查询参数
     * @returns {Promise<Blob>} Excel文件数据
     */
    exportBindingRecords(ckId, params = {}) {
        const url = replaceUrlParams(RECONCILIATION.EXPORT_RECORDS, { ckId })
        return http.get(url, { 
            params,
            responseType: 'blob'
        }).then(res => {
            // 创建下载链接
            const blob = new Blob([res.data], { 
                type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' 
            })
            const downloadUrl = window.URL.createObjectURL(blob)
            const link = document.createElement('a')
            link.href = downloadUrl
            link.download = `binding_records_${ckId}_${new Date().getTime()}.xlsx`
            document.body.appendChild(link)
            link.click()
            document.body.removeChild(link)
            window.URL.revokeObjectURL(downloadUrl)
            return res
        })
    }
}

export default reconciliationApi
