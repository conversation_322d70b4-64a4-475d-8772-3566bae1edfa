# 数据权限安全修复报告

## 问题概述

系统中发现严重的数据权限问题：使用配置了"本商户所有数据"、"所有部门数据"、"所有用户数据"权限的角色账号能够查看到所有商户的 CK（Cookie/凭证）数据，违反了数据隔离原则。

## 问题根因分析

### 1. 权限范围判断逻辑错误

**位置**: `app/core/auth.py` 第 48 行

**问题代码**:

```python
if 'data:merchant:all' in data_permissions or 'data:department:all' in data_permissions:
    return "all"
```

**问题分析**:

- `data:department:all` 被错误地理解为全局权限
- 实际上它应该是"本商户范围内的所有部门数据"
- 当用户有此权限时，系统返回 `"all"` 范围，导致可以访问所有商户的数据

### 2. 权限语义理解错误

**问题**: 权限配置的语义不明确

- "所有部门数据" 被理解为跨商户的全局权限
- "所有用户数据" 被理解为跨商户的全局权限
- 实际应该限制在本商户范围内

### 3. 缺乏强制商户隔离

**问题**: 数据查询中缺乏强制的商户隔离检查

- 依赖权限范围判断，但判断逻辑有误
- 缺乏额外的安全验证机制

## 修复方案

### 1. 修复数据权限范围判断逻辑

**文件**: `app/core/auth.py`

**修复内容**:

```python
# 修复前
if 'data:merchant:all' in data_permissions or 'data:department:all' in data_permissions:
    return "all"

# 修复后
if 'data:merchant:all' in data_permissions:
    logger.warning(f"[SECURITY] 用户 {user.id} 拥有全局商户权限")
    return "all"
elif 'data:department:all' in data_permissions:
    logger.info(f"[DEBUG] 用户有本商户所有部门权限，返回 'merchant' 范围")
    return "merchant"  # 修复：返回商户范围而不是全局范围
elif 'data:user:all' in data_permissions:
    logger.info(f"[DEBUG] 用户有本商户所有用户权限，返回 'merchant' 范围")
    return "merchant"  # 修复：返回商户范围而不是全局范围
```

### 2. 修复权限配置语义

**文件**: `mysql-init/02-init-data.sql`, `src/views/system-management/role/permission.vue`

**修复内容**:

- 更新权限描述，明确权限范围
- `data:department:all` → "本商户所有部门数据"
- `data:user:all` → "本商户所有用户数据"
- 添加安全警告标识

### 3. 强化 CK 查询的商户隔离

**文件**: `app/services/walmart_ck_service_new.py`

**修复内容**:

```python
def apply_data_isolation(self, query, current_user: User):
    # 首先过滤已删除的记录
    query = query.filter(WalmartCK.is_deleted == False)

    # 【安全修复】：强制商户隔离
    if not current_user.is_superuser:
        if not current_user.merchant_id:
            query = query.filter(WalmartCK.id == -1)  # 强制返回空结果
            return query

        # 强制添加商户过滤
        query = query.filter(WalmartCK.merchant_id == current_user.merchant_id)

    return super().apply_data_isolation(query, current_user)
```

### 4. 增加安全验证和审计

**新增文件**: `app/services/security_service.py`

**功能**:

- 商户隔离验证
- CK 访问权限验证
- 安全违规事件记录
- 数据访问审计日志
- 权限提升检测

### 5. API 接口安全加固

**文件**: `app/api/v1/endpoints/walmart_ck_new.py`

**修复内容**:

- 添加强制商户隔离检查
- 验证用户商户信息
- 记录跨商户访问尝试

## 修复验证

### 1. 测试用例

**文件**: `tests/test_data_permission_security.py`

**测试内容**:

- 数据权限范围修复验证
- 商户隔离功能测试
- 安全服务验证测试
- 超级管理员访问测试
- 查询数据隔离测试

### 2. 验证脚本

**文件**: `scripts/verify_data_permission_fix.py`

**功能**:

- 自动验证修复效果
- 检查权限范围判断
- 验证商户隔离机制
- 确认超级管理员权限

## 安全影响评估

### 修复前风险

- **严重**: 跨商户数据泄露
- **影响范围**: 所有配置了"所有部门数据"或"所有用户数据"权限的用户
- **数据类型**: CK 凭证、用户信息、部门信息等敏感数据

### 修复后安全性

- **商户隔离**: 强制执行，无法绕过
- **权限范围**: 明确限制在商户范围内
- **审计追踪**: 完整的安全事件记录
- **多层防护**: 权限检查 + 数据隔离 + 安全验证

## 部署建议

### 1. 立即执行

```bash
# 1. 备份数据库
mysqldump -u root -p walmart_bind_card > backup_$(date +%Y%m%d_%H%M%S).sql

# 2. 应用数据库修复
mysql -u root -p walmart_bind_card < mysql-init/02-init-data.sql

# 3. 重启应用服务
systemctl restart walmart-bind-card-server

# 4. 运行验证脚本
python scripts/verify_data_permission_fix.py
```

### 2. 监控建议

- 监控安全违规日志
- 定期检查权限配置
- 审计跨商户访问尝试

### 3. 用户通知

- 通知管理员权限配置变更
- 更新权限配置文档
- 培训用户正确的权限理解

## 绑卡数据安全修复

### 发现的绑卡数据漏洞

经过全面检查，发现绑卡数据（card_records 表）存在与 CK 数据相同的跨商户数据泄露漏洞：

1. **权限范围判断问题影响**: CardRecordService 受到相同的权限范围判断问题影响
2. **缺乏强制商户隔离**: 没有像 WalmartCKService 那样的双重保险机制
3. **API 接口安全检查不足**: 缺乏对跨商户访问尝试的检测和阻止

### 绑卡数据修复方案

**文件**: `app/services/card_record_service.py`

**修复内容**:

```python
def apply_data_isolation(self, query, current_user: User):
    # 【安全修复】：强制商户隔离
    if not current_user.is_superuser:
        if not current_user.merchant_id:
            self.logger.warning(f"[SECURITY] 用户 {current_user.id} 没有商户ID，拒绝绑卡数据访问")
            query = query.filter(CardRecord.id == 'impossible_id')
            return query

        # 强制添加商户过滤
        query = query.filter(CardRecord.merchant_id == current_user.merchant_id)
        self.logger.info(f"[SECURITY] 强制商户隔离：用户 {current_user.id} 只能访问商户 {current_user.merchant_id} 的绑卡数据")

    return super().apply_data_isolation(query, current_user)
```

**文件**: `app/services/card_api_service.py`

**修复内容**:

- 添加强制商户隔离检查
- 集成 SecurityService 进行安全验证
- 记录跨商户访问尝试
- 增强批量操作的权限检查

**文件**: `app/services/security_service.py`

**新增功能**:

```python
def validate_card_access(self, current_user: User, card_id: str, operation: str = "access") -> bool:
    # 验证绑卡记录访问权限
    # 确保用户只能访问自己商户的绑卡记录
```

### 绑卡数据测试验证

**文件**: `tests/test_card_record_security.py`

- 绑卡记录商户隔离测试
- CardAPIService 安全检查测试
- 查询数据隔离测试
- 批量操作安全性测试

**文件**: `scripts/verify_card_record_security.py`

- 自动化安全验证脚本
- 绑卡数据权限范围验证
- API 接口安全性验证

## 总结

本次修复彻底解决了 CK 数据和绑卡数据的跨商户数据泄露问题：

### CK 数据修复

1. ✅ **根因修复**: 修正了权限范围判断逻辑
2. ✅ **语义明确**: 更新了权限配置描述
3. ✅ **强制隔离**: 添加了多层商户隔离机制
4. ✅ **安全审计**: 实现了完整的安全事件追踪

### 绑卡数据修复

1. ✅ **商户隔离**: 为 CardRecordService 添加强制商户隔离
2. ✅ **API 安全**: 增强 CardAPIService 的安全检查
3. ✅ **权限验证**: 集成 SecurityService 进行统一验证
4. ✅ **测试覆盖**: 创建全面的安全测试用例

### 综合安全保障

1. ✅ **统一安全服务**: SecurityService 提供跨模块的安全验证
2. ✅ **完整审计追踪**: 所有安全事件可追溯和分析
3. ✅ **自动化验证**: 提供脚本确保修复效果持续有效
4. ✅ **多层防护**: 权限检查 + 数据隔离 + 安全验证

## 部门数据安全修复

### 发现的部门数据漏洞

经过全面检查，发现部门数据（departments 表）存在与 CK 数据和绑卡数据相同的跨商户数据泄露漏洞，而且由于部门数据作为组织架构基础的特殊性，其风险更加严重：

1. **权限范围判断问题影响**: DepartmentService 受到相同的权限范围判断问题影响
2. **缺乏强制商户隔离**: 没有像其他服务那样的双重保险机制
3. **组织架构泄露风险**: 部门数据暴露会泄露商户的组织结构信息
4. **级联数据安全边界破坏**: 部门是数据权限的重要边界，其泄露影响整个权限体系

### 部门数据修复方案

**文件**: `app/services/department_service_new.py`

**修复内容**:

```python
def apply_data_isolation(self, query, current_user: User):
    # 【安全修复】：强制商户隔离
    if not current_user.is_superuser:
        if not current_user.merchant_id:
            self.logger.warning(f"[SECURITY] 用户 {current_user.id} 没有商户ID，拒绝部门数据访问")
            query = query.filter(Department.id == -1)
            return query

        # 强制添加商户过滤
        query = query.filter(Department.merchant_id == current_user.merchant_id)
        self.logger.info(f"[SECURITY] 强制商户隔离：用户 {current_user.id} 只能访问商户 {current_user.merchant_id} 的部门数据")

    return super().apply_data_isolation(query, current_user)
```

**文件**: `app/api/v1/endpoints/departments.py`

**修复内容**:

- 添加强制商户隔离检查
- 集成 SecurityService 进行安全验证
- 记录跨商户访问尝试
- 增强部门树形结构的安全性

**文件**: `app/services/security_service.py`

**新增功能**:

```python
def validate_department_access(self, current_user: User, department_id: int, operation: str = "access") -> bool:
    # 验证部门访问权限
    # 确保用户只能访问自己商户的部门
```

**权限配置语义修复**:

```python
def _filter_departments_by_data_permission(self, departments: List[Department], current_user: User):
    if permission_service.check_data_permission(current_user, 'data:department:all'):
        # 【安全修复】：有访问本商户所有部门数据的权限（不是全局权限）
        return [dept for dept in departments if dept.merchant_id == current_user.merchant_id]
```

### 部门数据测试验证

**文件**: `tests/test_department_security.py`

- 部门商户隔离测试
- 部门树形结构安全测试
- 查询数据隔离测试
- 部门搜索安全性测试
- 部门统计和用户查询安全测试

**文件**: `scripts/verify_department_security.py`

- 自动化安全验证脚本
- 部门数据权限范围验证
- 部门树形结构安全验证
- 部门搜索功能安全验证

修复后，除超级管理员外，所有用户都严格按照商户维度进行数据隔离，确保 CK 数据、绑卡数据和部门数据的安全。
