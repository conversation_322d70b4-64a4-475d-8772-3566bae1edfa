"""
并发测试工具
用于执行高并发绑卡测试并收集性能数据
"""

import asyncio
import threading
import time
import uuid
from typing import Dict, List, Any, Optional, Callable, Awaitable
from concurrent.futures import ThreadPoolExecutor, as_completed
from dataclasses import dataclass
import logging

logger = logging.getLogger(__name__)


@dataclass
class ConcurrencyConfig:
    """并发测试配置"""
    concurrent_level: int = 100
    test_duration: int = 60
    ramp_up_time: int = 10
    max_workers: int = 50
    timeout_per_request: int = 30
    enable_real_time_monitoring: bool = True


class ConcurrencyTester:
    """并发测试器"""
    
    def __init__(self, config: Optional[ConcurrencyConfig] = None):
        """
        初始化并发测试器
        
        Args:
            config: 并发测试配置
        """
        self.config = config or ConcurrencyConfig()
        self.results: List[Dict[str, Any]] = []
        self.start_time: Optional[float] = None
        self.end_time: Optional[float] = None
        self.monitoring_data: List[Dict[str, Any]] = []
        self._stop_monitoring = False
    
    async def run_concurrent_binding_test(
        self,
        binding_function: Callable[..., Awaitable[Dict[str, Any]]],
        test_data: List[Dict[str, Any]],
        **kwargs
    ) -> List[Dict[str, Any]]:
        """
        运行并发绑卡测试
        
        Args:
            binding_function: 绑卡函数
            test_data: 测试数据列表
            **kwargs: 传递给绑卡函数的额外参数
            
        Returns:
            List[Dict]: 测试结果列表
        """
        logger.info(f"开始并发绑卡测试: 并发级别={self.config.concurrent_level}")
        
        self.start_time = time.time()
        self.results.clear()
        self.monitoring_data.clear()
        self._stop_monitoring = False
        
        try:
            # 启动实时监控
            if self.config.enable_real_time_monitoring:
                monitoring_task = asyncio.create_task(self._real_time_monitor())
            
            # 执行并发测试
            if len(test_data) < self.config.concurrent_level:
                # 如果测试数据不足，重复使用
                test_data = test_data * ((self.config.concurrent_level // len(test_data)) + 1)
            
            # 选择测试数据
            selected_data = test_data[:self.config.concurrent_level]
            
            # 创建并发任务
            tasks = []
            for i, data in enumerate(selected_data):
                task = asyncio.create_task(
                    self._execute_single_binding(
                        binding_function, 
                        data, 
                        request_id=i,
                        **kwargs
                    )
                )
                tasks.append(task)
                
                # 渐进式启动（ramp-up）
                if self.config.ramp_up_time > 0 and i > 0:
                    delay = self.config.ramp_up_time / self.config.concurrent_level
                    await asyncio.sleep(delay)
            
            # 等待所有任务完成
            results = await asyncio.gather(*tasks, return_exceptions=True)
            
            # 处理结果
            for i, result in enumerate(results):
                if isinstance(result, Exception):
                    self.results.append({
                        'request_id': i,
                        'success': False,
                        'error': str(result),
                        'response_time': 0.0,
                        'timestamp': time.time()
                    })
                else:
                    self.results.append(result)
            
            # 停止监控
            self._stop_monitoring = True
            if self.config.enable_real_time_monitoring:
                await monitoring_task
            
            self.end_time = time.time()
            total_duration = self.end_time - self.start_time
            
            logger.info(
                f"并发测试完成: 总耗时={total_duration:.2f}秒, "
                f"成功={sum(1 for r in self.results if r['success'])}, "
                f"失败={sum(1 for r in self.results if not r['success'])}"
            )
            
            return self.results
            
        except Exception as e:
            logger.error(f"并发测试执行失败: {e}")
            self._stop_monitoring = True
            raise
    
    async def _execute_single_binding(
        self,
        binding_function: Callable[..., Awaitable[Dict[str, Any]]],
        test_data: Dict[str, Any],
        request_id: int,
        **kwargs
    ) -> Dict[str, Any]:
        """
        执行单个绑卡请求
        
        Args:
            binding_function: 绑卡函数
            test_data: 测试数据
            request_id: 请求ID
            **kwargs: 额外参数
            
        Returns:
            Dict: 请求结果
        """
        request_start_time = time.time()
        
        try:
            # 执行绑卡请求
            result = await asyncio.wait_for(
                binding_function(test_data, **kwargs),
                timeout=self.config.timeout_per_request
            )
            
            response_time = time.time() - request_start_time
            
            return {
                'request_id': request_id,
                'success': result.get('success', False),
                'response_time': response_time,
                'timestamp': request_start_time,
                'ck_id': result.get('ck_id'),
                'department_id': result.get('department_id'),
                'error_type': result.get('error_type'),
                'result_data': result
            }
            
        except asyncio.TimeoutError:
            response_time = time.time() - request_start_time
            return {
                'request_id': request_id,
                'success': False,
                'response_time': response_time,
                'timestamp': request_start_time,
                'error_type': 'timeout',
                'error': f'请求超时 ({self.config.timeout_per_request}秒)'
            }
            
        except Exception as e:
            response_time = time.time() - request_start_time
            return {
                'request_id': request_id,
                'success': False,
                'response_time': response_time,
                'timestamp': request_start_time,
                'error_type': 'exception',
                'error': str(e)
            }
    
    async def _real_time_monitor(self):
        """实时监控系统状态"""
        logger.info("启动实时监控...")
        
        while not self._stop_monitoring:
            try:
                current_time = time.time()
                elapsed_time = current_time - self.start_time if self.start_time else 0
                
                # 统计当前状态
                completed_requests = len(self.results)
                successful_requests = sum(1 for r in self.results if r['success'])
                failed_requests = completed_requests - successful_requests
                
                success_rate = successful_requests / completed_requests if completed_requests > 0 else 0.0
                qps = completed_requests / elapsed_time if elapsed_time > 0 else 0.0
                
                monitoring_point = {
                    'timestamp': current_time,
                    'elapsed_time': elapsed_time,
                    'completed_requests': completed_requests,
                    'successful_requests': successful_requests,
                    'failed_requests': failed_requests,
                    'success_rate': success_rate,
                    'qps': qps
                }
                
                self.monitoring_data.append(monitoring_point)
                
                # 每5秒输出一次监控信息
                if len(self.monitoring_data) % 5 == 0:
                    logger.info(
                        f"实时监控 - 已完成: {completed_requests}, "
                        f"成功率: {success_rate:.2%}, QPS: {qps:.2f}"
                    )
                
                await asyncio.sleep(1)  # 每秒监控一次
                
            except Exception as e:
                logger.error(f"实时监控异常: {e}")
                await asyncio.sleep(1)
    
    def run_thread_based_test(
        self,
        sync_function: Callable[..., Dict[str, Any]],
        test_data: List[Dict[str, Any]],
        **kwargs
    ) -> List[Dict[str, Any]]:
        """
        运行基于线程的并发测试（用于同步函数）
        
        Args:
            sync_function: 同步绑卡函数
            test_data: 测试数据列表
            **kwargs: 额外参数
            
        Returns:
            List[Dict]: 测试结果列表
        """
        logger.info(f"开始线程并发测试: 并发级别={self.config.concurrent_level}")
        
        self.start_time = time.time()
        self.results.clear()
        
        # 准备测试数据
        if len(test_data) < self.config.concurrent_level:
            test_data = test_data * ((self.config.concurrent_level // len(test_data)) + 1)
        
        selected_data = test_data[:self.config.concurrent_level]
        
        # 使用线程池执行并发测试
        with ThreadPoolExecutor(max_workers=self.config.max_workers) as executor:
            # 提交所有任务
            future_to_request_id = {
                executor.submit(
                    self._execute_sync_binding, 
                    sync_function, 
                    data, 
                    i, 
                    **kwargs
                ): i
                for i, data in enumerate(selected_data)
            }
            
            # 收集结果
            for future in as_completed(future_to_request_id, timeout=self.config.timeout_per_request * 2):
                try:
                    result = future.result()
                    self.results.append(result)
                except Exception as e:
                    request_id = future_to_request_id[future]
                    self.results.append({
                        'request_id': request_id,
                        'success': False,
                        'error': str(e),
                        'response_time': 0.0,
                        'timestamp': time.time(),
                        'error_type': 'thread_exception'
                    })
        
        self.end_time = time.time()
        total_duration = self.end_time - self.start_time
        
        logger.info(
            f"线程并发测试完成: 总耗时={total_duration:.2f}秒, "
            f"成功={sum(1 for r in self.results if r['success'])}, "
            f"失败={sum(1 for r in self.results if not r['success'])}"
        )
        
        return self.results
    
    def _execute_sync_binding(
        self,
        sync_function: Callable[..., Dict[str, Any]],
        test_data: Dict[str, Any],
        request_id: int,
        **kwargs
    ) -> Dict[str, Any]:
        """
        执行同步绑卡请求
        
        Args:
            sync_function: 同步绑卡函数
            test_data: 测试数据
            request_id: 请求ID
            **kwargs: 额外参数
            
        Returns:
            Dict: 请求结果
        """
        request_start_time = time.time()
        
        try:
            result = sync_function(test_data, **kwargs)
            response_time = time.time() - request_start_time
            
            return {
                'request_id': request_id,
                'success': result.get('success', False),
                'response_time': response_time,
                'timestamp': request_start_time,
                'ck_id': result.get('ck_id'),
                'department_id': result.get('department_id'),
                'error_type': result.get('error_type'),
                'result_data': result
            }
            
        except Exception as e:
            response_time = time.time() - request_start_time
            return {
                'request_id': request_id,
                'success': False,
                'response_time': response_time,
                'timestamp': request_start_time,
                'error_type': 'sync_exception',
                'error': str(e)
            }
    
    async def run_stress_test(
        self,
        binding_function: Callable[..., Awaitable[Dict[str, Any]]],
        test_data: List[Dict[str, Any]],
        stress_levels: List[int] = None,
        **kwargs
    ) -> Dict[int, List[Dict[str, Any]]]:
        """
        运行压力测试（多个并发级别）
        
        Args:
            binding_function: 绑卡函数
            test_data: 测试数据列表
            stress_levels: 压力级别列表
            **kwargs: 额外参数
            
        Returns:
            Dict: {并发级别: 测试结果} 的映射
        """
        if stress_levels is None:
            stress_levels = [50, 100, 200, 500]
        
        logger.info(f"开始压力测试: 测试级别={stress_levels}")
        
        stress_results = {}
        
        for level in stress_levels:
            logger.info(f"执行压力测试: 并发级别={level}")
            
            # 更新配置
            original_level = self.config.concurrent_level
            self.config.concurrent_level = level
            
            try:
                # 执行测试
                results = await self.run_concurrent_binding_test(
                    binding_function, test_data, **kwargs
                )
                stress_results[level] = results
                
                # 短暂休息，避免系统过载
                await asyncio.sleep(2)
                
            except Exception as e:
                logger.error(f"压力测试失败 (级别={level}): {e}")
                stress_results[level] = []
            
            finally:
                # 恢复原始配置
                self.config.concurrent_level = original_level
        
        logger.info("压力测试完成")
        return stress_results
    
    def get_monitoring_data(self) -> List[Dict[str, Any]]:
        """获取监控数据"""
        return self.monitoring_data.copy()
    
    def get_test_summary(self) -> Dict[str, Any]:
        """获取测试摘要"""
        if not self.results:
            return {}
        
        successful_results = [r for r in self.results if r['success']]
        failed_results = [r for r in self.results if not r['success']]
        
        total_duration = self.end_time - self.start_time if self.end_time and self.start_time else 0
        
        return {
            'config': {
                'concurrent_level': self.config.concurrent_level,
                'test_duration': self.config.test_duration,
                'ramp_up_time': self.config.ramp_up_time
            },
            'results': {
                'total_requests': len(self.results),
                'successful_requests': len(successful_results),
                'failed_requests': len(failed_results),
                'success_rate': len(successful_results) / len(self.results) if self.results else 0.0
            },
            'timing': {
                'total_duration': total_duration,
                'requests_per_second': len(self.results) / total_duration if total_duration > 0 else 0.0
            },
            'monitoring_points': len(self.monitoring_data)
        }