<template>
  <div class="department-test">
    <h2>部门管理测试页面</h2>
    <p>这是一个简单的测试页面，用于验证路由是否正常工作。</p>
    
    <el-button type="primary" @click="testApi">测试部门API</el-button>
    <el-button type="success" @click="testUserApi">测试用户API</el-button>
    
    <div v-if="loading">加载中...</div>
    <div v-if="error" style="color: red;">错误: {{ error }}</div>
    <div v-if="result">
      <h3>API测试结果:</h3>
      <pre>{{ JSON.stringify(result, null, 2) }}</pre>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { ElMessage } from 'element-plus'
import { departmentApi } from '@/api/modules/department'
import { userApi } from '@/api/modules/user'

const loading = ref(false)
const error = ref('')
const result = ref(null)

const testApi = async () => {
  loading.value = true
  error.value = ''
  result.value = null

  try {
    console.log('开始调用部门API...')
    console.log('API配置:', {
      baseURL: window.APP_CONFIG?.API_BASE_URL,
      fullURL: `${window.APP_CONFIG?.API_BASE_URL}/api/v1/departments`
    })

    const response = await departmentApi.getList({ page: 1, page_size: 10 })
    result.value = response
    ElMessage.success('API调用成功')
  } catch (err) {
    console.error('API调用详细错误:', err)
    error.value = `${err.message} (状态码: ${err.response?.status || '无'})`
    ElMessage.error('API调用失败: ' + err.message)
  } finally {
    loading.value = false
  }
}

const testUserApi = async () => {
  loading.value = true
  error.value = ''
  result.value = null

  try {
    console.log('开始调用用户API...')
    const response = await userApi.getList({ page: 1, page_size: 10 })
    result.value = response
    ElMessage.success('用户API调用成功')
  } catch (err) {
    console.error('用户API调用详细错误:', err)
    error.value = `用户API: ${err.message} (状态码: ${err.response?.status || '无'})`
    ElMessage.error('用户API调用失败: ' + err.message)
  } finally {
    loading.value = false
  }
}
</script>

<style scoped>
.department-test {
  padding: 20px;
}

pre {
  background: #f5f5f5;
  padding: 10px;
  border-radius: 4px;
  overflow: auto;
}
</style>
