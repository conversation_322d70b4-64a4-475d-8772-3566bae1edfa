#!/usr/bin/env python3
"""
沃尔玛绑卡系统综合测试运行器

使用方法:
    python run_comprehensive_test.py [选项]

选项:
    --concurrent-level: 并发级别 (默认: 100)
    --success-rate: Mock API成功率 (默认: 0.7)
    --test-duration: 测试持续时间(秒) (默认: 60)
    --verbose: 详细输出
    --report-file: 测试报告文件路径
"""

import argparse
import asyncio
import sys
import os
import time
import json
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

import pytest
import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('walmart_binding_test.log')
    ]
)

logger = logging.getLogger(__name__)


def parse_arguments():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description='沃尔玛绑卡系统综合测试')
    
    parser.add_argument(
        '--concurrent-level',
        type=int,
        default=100,
        help='并发级别 (默认: 100)'
    )
    
    parser.add_argument(
        '--success-rate',
        type=float,
        default=0.7,
        help='Mock API成功率 (默认: 0.7)'
    )
    
    parser.add_argument(
        '--test-duration',
        type=int,
        default=60,
        help='测试持续时间(秒) (默认: 60)'
    )
    
    parser.add_argument(
        '--verbose',
        action='store_true',
        help='详细输出'
    )
    
    parser.add_argument(
        '--report-file',
        type=str,
        default='test_report.json',
        help='测试报告文件路径'
    )
    
    parser.add_argument(
        '--test-case',
        type=str,
        choices=[
            'all',
            'concurrent',
            'weight',
            'load_balance',
            'queue',
            'edge_cases',
            'performance'
        ],
        default='all',
        help='要运行的测试用例'
    )
    
    return parser.parse_args()


def setup_test_environment(args):
    """设置测试环境"""
    # 设置环境变量
    os.environ['TESTING'] = 'true'
    os.environ['DISABLE_WALMART_API'] = 'true'
    
    # 更新测试配置
    test_config = {
        "concurrent_levels": [args.concurrent_level],
        "success_rate": args.success_rate,
        "test_duration": args.test_duration,
        "api_delay_range": (0.05, 0.2),
        "performance_thresholds": {
            "response_time": 0.5,
            "success_rate": 0.95,
            "load_balance": 0.7
        },
        "test_data_config": {
            "merchant_count": 2,
            "departments_per_merchant": 4,
            "cks_per_department": 3,
            "cards_per_merchant": max(200, args.concurrent_level * 2)
        }
    }
    
    return test_config


def run_specific_test(test_case: str, verbose: bool = False):
    """运行特定的测试用例"""
    test_file = Path(__file__).parent / "test_walmart_binding_comprehensive.py"
    
    # 构建pytest参数
    pytest_args = [str(test_file)]
    
    if verbose:
        pytest_args.extend(["-v", "-s"])
    
    # 根据测试用例选择特定的测试方法
    if test_case != 'all':
        test_method_map = {
            'concurrent': 'test_concurrent_binding_requests',
            'weight': 'test_weight_distribution_algorithm',
            'load_balance': 'test_ck_load_balancing',
            'queue': 'test_queue_processing_mechanism',
            'edge_cases': 'test_edge_cases_and_error_handling',
            'performance': 'test_performance_under_load'
        }
        
        if test_case in test_method_map:
            pytest_args.append(f"-k {test_method_map[test_case]}")
    
    return pytest.main(pytest_args)


def generate_test_report(args, test_result, execution_time):
    """生成测试报告"""
    report = {
        "test_execution": {
            "timestamp": time.strftime("%Y-%m-%d %H:%M:%S"),
            "execution_time": execution_time,
            "test_case": args.test_case,
            "exit_code": test_result
        },
        "test_configuration": {
            "concurrent_level": args.concurrent_level,
            "success_rate": args.success_rate,
            "test_duration": args.test_duration,
            "verbose": args.verbose
        },
        "test_status": {
            "passed": test_result == 0,
            "status": "PASSED" if test_result == 0 else "FAILED"
        }
    }
    
    # 保存报告
    try:
        with open(args.report_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False)
        logger.info(f"测试报告已保存到: {args.report_file}")
    except Exception as e:
        logger.error(f"保存测试报告失败: {e}")
    
    return report


def print_test_summary(report):
    """打印测试摘要"""
    print("\n" + "="*60)
    print("沃尔玛绑卡系统综合测试摘要")
    print("="*60)
    print(f"测试时间: {report['test_execution']['timestamp']}")
    print(f"执行耗时: {report['test_execution']['execution_time']:.2f}秒")
    print(f"测试用例: {report['test_execution']['test_case']}")
    print(f"并发级别: {report['test_configuration']['concurrent_level']}")
    print(f"API成功率: {report['test_configuration']['success_rate']:.1%}")
    print(f"测试状态: {report['test_status']['status']}")
    print("="*60)


def check_dependencies():
    """检查测试依赖"""
    required_packages = [
        'pytest',
        'pytest-asyncio',
        'sqlalchemy',
        'fastapi'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package.replace('-', '_'))
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        logger.error(f"缺少依赖包: {', '.join(missing_packages)}")
        logger.error("请运行: pip install " + " ".join(missing_packages))
        return False
    
    return True


def main():
    """主函数"""
    print("沃尔玛绑卡系统综合测试启动...")
    
    # 解析参数
    args = parse_arguments()
    
    # 检查依赖
    if not check_dependencies():
        sys.exit(1)
    
    # 设置测试环境
    test_config = setup_test_environment(args)
    logger.info(f"测试配置: {test_config}")
    
    # 记录开始时间
    start_time = time.time()
    
    try:
        # 运行测试
        logger.info(f"开始运行测试用例: {args.test_case}")
        test_result = run_specific_test(args.test_case, args.verbose)
        
        # 计算执行时间
        execution_time = time.time() - start_time
        
        # 生成测试报告
        report = generate_test_report(args, test_result, execution_time)
        
        # 打印测试摘要
        print_test_summary(report)
        
        # 返回测试结果
        if test_result == 0:
            logger.info("所有测试通过!")
            print("\n✅ 测试成功完成!")
        else:
            logger.error("测试失败!")
            print("\n❌ 测试失败!")
        
        sys.exit(test_result)
        
    except KeyboardInterrupt:
        logger.info("测试被用户中断")
        print("\n⚠️  测试被中断")
        sys.exit(1)
        
    except Exception as e:
        logger.error(f"测试执行异常: {e}")
        print(f"\n💥 测试执行异常: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()