from __future__ import annotations
from typing import Any, Dict, List, Optional, Union
from sqlalchemy.orm import Session

from app.crud.base import CRUDBase
from app.models.permission import Permission
from app.models.role import Role, role_permissions


class CRUDPermission(CRUDBase[Permission, "PermissionCreate", "PermissionUpdate"]):
    """Permission的CRUD操作类"""

    def get_by_code(self, db: Session, code: str) -> Optional[Permission]:
        """通过权限代码获取权限"""
        return db.query(Permission).filter(Permission.code == code).first()

    def get_multi_by_codes(self, db: Session, codes: List[str]) -> List[Permission]:
        """通过权限代码列表获取多个权限"""
        return db.query(Permission).filter(Permission.code.in_(codes)).all()

    def get_by_module(self, db: Session, *, module: str) -> List[Permission]:
        return db.query(Permission).filter(Permission.module == module).all()

    def get_by_role(self, db: Session, *, role: str) -> List[Permission]:
        """获取角色拥有的所有权限"""
        role_obj = db.query(Role).filter(Role.code == role).first()
        if not role_obj:
            return []
        return role_obj.permissions

    def assign_to_role(
        self, db: Session, *, role: str, permission_codes: List[str]
    ) -> None:
        """为角色分配权限"""
        role_obj = db.query(Role).filter(Role.code == role).first()
        if not role_obj:
            return

        # 获取权限对象列表
        permissions = (
            db.query(Permission).filter(Permission.code.in_(permission_codes)).all()
        )

        # 设置新的权限
        role_obj.permissions = permissions
        db.commit()

    def revoke_from_role(
        self, db: Session, *, role: str, permission_codes: List[str]
    ) -> None:
        """从角色中撤销权限"""
        role_obj = db.query(Role).filter(Role.code == role).first()
        if not role_obj:
            return

        # 获取要移除的权限对象
        permissions_to_remove = (
            db.query(Permission).filter(Permission.code.in_(permission_codes)).all()
        )

        # 移除权限
        for permission in permissions_to_remove:
            if permission in role_obj.permissions:
                role_obj.permissions.remove(permission)

        db.commit()


def get_role_permissions(db: Session, role: str) -> List[Permission]:
    """获取角色的所有权限

    Args:
        db: 数据库会话
        role: 角色名称

    Returns:
        List[Permission]: 权限列表
    """
    role_obj = db.query(Role).filter(Role.code == role).first()
    if not role_obj:
        return []
    return role_obj.permissions


def set_role_permissions(db: Session, role: str, permission_codes: List[str]) -> None:
    """设置角色的权限

    Args:
        db: 数据库会话
        role: 角色名称
        permission_codes: 权限代码列表
    """
    role_obj = db.query(Role).filter(Role.code == role).first()
    if not role_obj:
        return

    # 获取权限对象列表
    permissions = (
        db.query(Permission).filter(Permission.code.in_(permission_codes)).all()
    )

    # 设置新的权限
    role_obj.permissions = permissions
    db.commit()


def remove_role_permissions(
    db: Session, role: str, permission_codes: List[str]
) -> None:
    """移除角色的指定权限

    Args:
        db: 数据库会话
        role: 角色名称
        permission_codes: 权限代码列表
    """
    role_obj = db.query(Role).filter(Role.code == role).first()
    if not role_obj:
        return

    # 获取要移除的权限对象
    permissions_to_remove = (
        db.query(Permission).filter(Permission.code.in_(permission_codes)).all()
    )

    # 移除权限
    for permission in permissions_to_remove:
        if permission in role_obj.permissions:
            role_obj.permissions.remove(permission)

    db.commit()


permission = CRUDPermission(Permission)
