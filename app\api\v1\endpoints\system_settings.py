"""
系统设置API接口

功能说明：
1. 管理系统级别的配置设置
2. 包括CK过期时间配置等
3. 只允许超级管理员访问和修改
4. 支持动态配置修改和实时生效
"""

from typing import Any, List, Dict, Optional
from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session

from app.api import deps
from app.models.user import User
from app.schemas.system_settings import (
    SystemSettings as SystemSettingsSchema,
    SystemSettingsCreate,
    SystemSettingsUpdate
)
from app.crud.system_settings import system_settings
from app.core.logging import get_logger

logger = get_logger("system_settings_api")
router = APIRouter()


def check_super_admin(current_user: User) -> None:
    """
    检查是否为超级管理员
    
    Args:
        current_user: 当前用户
        
    Raises:
        HTTPException: 如果不是超级管理员
    """
    if not current_user.is_superuser:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="只有超级管理员可以访问系统设置"
        )


@router.get("/", response_model=List[SystemSettingsSchema])
def get_system_settings(
    *,
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_active_user),
    skip: int = 0,
    limit: int = 100,
    search: Optional[str] = None
) -> Any:
    """
    获取系统设置列表
    
    权限要求：
    - 超级管理员
    """
    check_super_admin(current_user)
    
    try:
        settings, total = system_settings.get_multi_with_search(
            db, skip=skip, limit=limit, search=search
        )
        
        logger.info(
            f"获取系统设置列表成功 | 用户: {current_user.username} | "
            f"总数: {total} | 返回: {len(settings)}"
        )
        
        return settings
    except Exception as e:
        logger.error(f"获取系统设置列表失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取系统设置失败"
        )


@router.get("/{key}", response_model=SystemSettingsSchema)
def get_system_setting(
    *,
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_active_user),
    key: str
) -> Any:
    """
    根据键名获取系统设置
    
    权限要求：
    - 超级管理员
    """
    check_super_admin(current_user)
    
    try:
        setting = system_settings.get_by_key(db, key)
        if not setting:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"系统设置 '{key}' 不存在"
            )
        
        logger.info(f"获取系统设置成功 | 用户: {current_user.username} | 键: {key}")
        return setting
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取系统设置失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取系统设置失败"
        )


@router.post("/", response_model=SystemSettingsSchema)
def create_system_setting(
    *,
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_active_user),
    setting_in: SystemSettingsCreate
) -> Any:
    """
    创建系统设置
    
    权限要求：
    - 超级管理员
    """
    check_super_admin(current_user)
    
    try:
        # 检查键名是否已存在
        existing = system_settings.get_by_key(db, setting_in.key)
        if existing:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"系统设置 '{setting_in.key}' 已存在"
            )
        
        setting = system_settings.create(db, obj_in=setting_in)
        
        logger.info(
            f"创建系统设置成功 | 用户: {current_user.username} | "
            f"键: {setting.key} | 值: {setting.value}"
        )
        
        return setting
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"创建系统设置失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="创建系统设置失败"
        )


@router.put("/{key}", response_model=SystemSettingsSchema)
def update_system_setting(
    *,
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_active_user),
    key: str,
    setting_in: SystemSettingsUpdate
) -> Any:
    """
    更新系统设置
    
    权限要求：
    - 超级管理员
    """
    check_super_admin(current_user)
    
    try:
        setting = system_settings.get_by_key(db, key)
        if not setting:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"系统设置 '{key}' 不存在"
            )
        
        old_value = setting.value
        setting = system_settings.update(db, db_obj=setting, obj_in=setting_in)
        
        logger.info(
            f"更新系统设置成功 | 用户: {current_user.username} | "
            f"键: {key} | 旧值: {old_value} | 新值: {setting.value}"
        )
        
        return setting
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"更新系统设置失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="更新系统设置失败"
        )


@router.delete("/{key}")
def delete_system_setting(
    *,
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_active_user),
    key: str
) -> Any:
    """
    删除系统设置（只能删除非系统内置设置）
    
    权限要求：
    - 超级管理员
    """
    check_super_admin(current_user)
    
    try:
        setting = system_settings.get_by_key(db, key)
        if not setting:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"系统设置 '{key}' 不存在"
            )
        
        # 检查是否为系统内置设置
        if setting.is_system:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="不能删除系统内置设置"
            )
        
        success = system_settings.delete_custom_setting(db, key)
        if not success:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="删除系统设置失败"
            )
        
        logger.info(f"删除系统设置成功 | 用户: {current_user.username} | 键: {key}")
        
        return {"message": "删除成功"}
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"删除系统设置失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="删除系统设置失败"
        )


@router.get("/ck-expire/config", response_model=Dict[str, Any])
def get_ck_expire_config(
    *,
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_active_user)
) -> Any:
    """
    获取CK过期配置
    
    权限要求：
    - 超级管理员
    """
    check_super_admin(current_user)
    
    try:
        from app.services.ck_expire_service import create_ck_expire_service
        
        ck_expire_service = create_ck_expire_service(db)
        config = ck_expire_service.get_expire_config()
        
        logger.info(f"获取CK过期配置成功 | 用户: {current_user.username}")
        
        return {
            "config": config,
            "message": "获取CK过期配置成功"
        }
    except Exception as e:
        logger.error(f"获取CK过期配置失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取CK过期配置失败"
        )


@router.put("/ck-expire/config", response_model=Dict[str, Any])
def update_ck_expire_config(
    *,
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_active_user),
    config_data: Dict[str, Any]
) -> Any:
    """
    更新CK过期配置

    权限要求：
    - 超级管理员
    """
    check_super_admin(current_user)

    try:
        # 从请求数据中提取参数
        expire_minutes = int(config_data.get('expire_minutes', 30))
        check_enabled = bool(config_data.get('check_enabled', True))
        check_interval = int(config_data.get('check_interval', 5))

        # 验证参数
        if expire_minutes < 1 or expire_minutes > 1440:  # 1分钟到24小时
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="过期时间必须在1-1440分钟之间"
            )

        if check_interval < 1 or check_interval > 60:  # 1分钟到1小时
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="检测间隔必须在1-60分钟之间"
            )

        # 更新配置
        system_settings.set_value(
            db, 'ck_expire_minutes', str(expire_minutes),
            'CK自动过期时间（分钟）'
        )
        system_settings.set_value(
            db, 'ck_expire_check_enabled', str(check_enabled).lower(),
            '是否启用CK自动过期检测'
        )
        system_settings.set_value(
            db, 'ck_expire_check_interval', str(check_interval),
            'CK过期检测间隔（分钟）'
        )

        logger.info(
            f"更新CK过期配置成功 | 用户: {current_user.username} | "
            f"过期时间: {expire_minutes}分钟 | 启用检测: {check_enabled} | "
            f"检测间隔: {check_interval}分钟"
        )

        return {
            "config": {
                "expire_minutes": expire_minutes,
                "check_enabled": check_enabled,
                "check_interval": check_interval
            },
            "message": "更新CK过期配置成功"
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"更新CK过期配置失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="更新CK过期配置失败"
        )
