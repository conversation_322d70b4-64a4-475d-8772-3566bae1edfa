"""
测试角色列表API过滤超级管理员功能
"""
import pytest
from fastapi.testclient import TestClient
from sqlalchemy.orm import Session

from app.main import app
from app.models.role import Role
from app.models.user import User
from test.conftest import get_test_db, create_test_user, create_test_merchant


class TestRoleSuperAdminFilter:
    """角色超级管理员过滤测试类"""

    def setup_method(self):
        """每个测试方法前的设置"""
        self.client = TestClient(app)
        self.db = next(get_test_db())
        
        # 创建测试商户
        self.test_merchant = create_test_merchant(self.db, "test_merchant_role_filter")
        
        # 创建超级管理员用户
        self.admin_user = create_test_user(
            self.db, 
            username="admin_role_filter_test",
            role_codes=["super_admin"],
            merchant_id=None,
            is_superuser=True
        )
        
        # 创建普通商户管理员用户
        self.merchant_user = create_test_user(
            self.db,
            username="merchant_role_filter_test",
            role_codes=["merchant_admin"],
            merchant_id=self.test_merchant.id,
            is_superuser=False
        )

    def teardown_method(self):
        """每个测试方法后的清理"""
        self.db.close()

    def test_role_list_filters_super_admin_for_admin(self):
        """测试超级管理员调用角色列表API时过滤掉超级管理员角色"""
        # 使用超级管理员登录
        login_data = {"username": "admin_role_filter_test", "password": "testpass123"}
        login_response = self.client.post("/api/v1/auth/login", data=login_data)
        assert login_response.status_code == 200
        token = login_response.json()["access_token"]
        headers = {"Authorization": f"Bearer {token}"}

        # 获取角色列表
        response = self.client.get("/api/v1/roles", headers=headers)
        assert response.status_code == 200
        
        data = response.json()
        items = data.get('items', [])
        
        # 验证超级管理员角色被过滤掉
        super_admin_roles = [role for role in items if role.get('code') == 'super_admin']
        assert len(super_admin_roles) == 0, "超级管理员角色应该被过滤掉"
        
        # 验证其他角色仍然存在
        other_roles = [role for role in items if role.get('code') in ['merchant_admin', 'ck_supplier']]
        assert len(other_roles) > 0, "其他角色应该仍然存在"

    def test_role_list_filters_super_admin_for_merchant(self):
        """测试商户管理员调用角色列表API时过滤掉超级管理员角色"""
        # 使用商户管理员登录
        login_data = {"username": "merchant_role_filter_test", "password": "testpass123"}
        login_response = self.client.post("/api/v1/auth/login", data=login_data)
        assert login_response.status_code == 200
        token = login_response.json()["access_token"]
        headers = {"Authorization": f"Bearer {token}"}

        # 获取角色列表
        response = self.client.get("/api/v1/roles", headers=headers)
        assert response.status_code == 200
        
        data = response.json()
        items = data.get('items', [])
        
        # 验证超级管理员角色被过滤掉
        super_admin_roles = [role for role in items if role.get('code') == 'super_admin']
        assert len(super_admin_roles) == 0, "超级管理员角色应该被过滤掉"

    def test_role_list_with_search_filters_super_admin(self):
        """测试带搜索条件的角色列表API也过滤掉超级管理员角色"""
        # 使用超级管理员登录
        login_data = {"username": "admin_role_filter_test", "password": "testpass123"}
        login_response = self.client.post("/api/v1/auth/login", data=login_data)
        assert login_response.status_code == 200
        token = login_response.json()["access_token"]
        headers = {"Authorization": f"Bearer {token}"}

        # 搜索超级管理员角色
        response = self.client.get(
            "/api/v1/roles", 
            headers=headers,
            params={"name": "超级管理员"}
        )
        assert response.status_code == 200
        
        data = response.json()
        items = data.get('items', [])
        
        # 即使搜索超级管理员，也应该被过滤掉
        super_admin_roles = [role for role in items if role.get('code') == 'super_admin']
        assert len(super_admin_roles) == 0, "即使搜索超级管理员角色，也应该被过滤掉"

    def test_role_list_with_code_search_filters_super_admin(self):
        """测试按代码搜索超级管理员角色时也被过滤掉"""
        # 使用超级管理员登录
        login_data = {"username": "admin_role_filter_test", "password": "testpass123"}
        login_response = self.client.post("/api/v1/auth/login", data=login_data)
        assert login_response.status_code == 200
        token = login_response.json()["access_token"]
        headers = {"Authorization": f"Bearer {token}"}

        # 按代码搜索超级管理员角色
        response = self.client.get(
            "/api/v1/roles", 
            headers=headers,
            params={"code": "super_admin"}
        )
        assert response.status_code == 200
        
        data = response.json()
        items = data.get('items', [])
        
        # 即使按代码搜索超级管理员，也应该被过滤掉
        super_admin_roles = [role for role in items if role.get('code') == 'super_admin']
        assert len(super_admin_roles) == 0, "即使按代码搜索超级管理员角色，也应该被过滤掉"

    def test_role_list_pagination_filters_super_admin(self):
        """测试分页查询时也过滤掉超级管理员角色"""
        # 使用超级管理员登录
        login_data = {"username": "admin_role_filter_test", "password": "testpass123"}
        login_response = self.client.post("/api/v1/auth/login", data=login_data)
        assert login_response.status_code == 200
        token = login_response.json()["access_token"]
        headers = {"Authorization": f"Bearer {token}"}

        # 分页查询角色列表
        response = self.client.get(
            "/api/v1/roles", 
            headers=headers,
            params={"page": 1, "page_size": 10}
        )
        assert response.status_code == 200
        
        data = response.json()
        items = data.get('items', [])
        
        # 验证超级管理员角色被过滤掉
        super_admin_roles = [role for role in items if role.get('code') == 'super_admin']
        assert len(super_admin_roles) == 0, "分页查询时超级管理员角色应该被过滤掉"

    def test_role_detail_super_admin_still_accessible(self):
        """测试超级管理员角色详情仍然可以通过ID访问（用于系统内部需要）"""
        # 使用超级管理员登录
        login_data = {"username": "admin_role_filter_test", "password": "testpass123"}
        login_response = self.client.post("/api/v1/auth/login", data=login_data)
        assert login_response.status_code == 200
        token = login_response.json()["access_token"]
        headers = {"Authorization": f"Bearer {token}"}

        # 获取超级管理员角色
        super_admin_role = self.db.query(Role).filter(Role.code == 'super_admin').first()
        if super_admin_role:
            # 通过ID获取角色详情
            response = self.client.get(
                f"/api/v1/roles/{super_admin_role.id}", 
                headers=headers
            )
            assert response.status_code == 200
            
            data = response.json()
            role_data = data.get('data', {})
            assert role_data.get('code') == 'super_admin', "应该能够通过ID获取超级管理员角色详情"
