"""
测试沃尔玛CK批量创建功能
"""

import pytest
from fastapi.testclient import TestClient
from sqlalchemy.orm import Session

from app.main import app
from app.core.config import settings
from app.models.user import User
from app.models.merchant import Merchant
from app.models.department import Department
from app.models.walmart_ck import WalmartCK
from test.conftest import TestingSessionLocal, override_get_db


client = TestClient(app)


class TestWalmartCKBatchCreate:
    """沃尔玛CK批量创建测试"""

    def setup_method(self):
        """每个测试方法前的设置"""
        self.db = TestingSessionLocal()
        app.dependency_overrides[override_get_db] = lambda: self.db

    def teardown_method(self):
        """每个测试方法后的清理"""
        self.db.close()
        app.dependency_overrides.clear()

    def test_batch_create_success(self):
        """测试批量创建成功"""
        # 创建测试数据
        merchant = Merchant(name="测试商户", code="TEST_MERCHANT")
        self.db.add(merchant)
        self.db.commit()
        self.db.refresh(merchant)

        department = Department(
            name="测试部门",
            code="TEST_DEPT",
            merchant_id=merchant.id
        )
        self.db.add(department)
        self.db.commit()
        self.db.refresh(department)

        user = User(
            username="testuser",
            email="<EMAIL>",
            hashed_password="hashed_password",
            merchant_id=merchant.id,
            department_id=department.id,
            is_active=True
        )
        self.db.add(user)
        self.db.commit()
        self.db.refresh(user)

        # 准备批量创建数据
        batch_data = {
            "signs": [
                "test1@domain#key1#123",
                "test2@domain#key2#456",
                "test3@domain#key3#789"
            ],
            "merchant_id": merchant.id,
            "department_id": department.id,
            "daily_limit": 50,
            "hourly_limit": 10,
            "active": 1,
            "description": "批量测试CK"
        }

        # 模拟用户登录
        headers = {"Authorization": f"Bearer test_token"}

        # 发送批量创建请求
        response = client.post(
            "/api/v1/walmart-ck/batch",
            json=batch_data,
            headers=headers
        )

        # 验证响应
        assert response.status_code == 200
        result = response.json()
        assert result["success"] is True
        assert result["data"]["success_count"] == 3
        assert result["data"]["failed_count"] == 0
        assert result["data"]["total_count"] == 3

        # 验证数据库中的记录
        created_cks = self.db.query(WalmartCK).filter(
            WalmartCK.merchant_id == merchant.id
        ).all()
        assert len(created_cks) == 3

        for ck in created_cks:
            assert ck.merchant_id == merchant.id
            assert ck.department_id == department.id
            assert ck.daily_limit == 50
            assert ck.hourly_limit == 10
            assert ck.active == 1
            assert ck.description == "批量测试CK"

    def test_batch_create_with_invalid_signs(self):
        """测试包含无效签名的批量创建"""
        # 创建测试数据
        merchant = Merchant(name="测试商户", code="TEST_MERCHANT")
        self.db.add(merchant)
        self.db.commit()
        self.db.refresh(merchant)

        department = Department(
            name="测试部门",
            code="TEST_DEPT",
            merchant_id=merchant.id
        )
        self.db.add(department)
        self.db.commit()
        self.db.refresh(department)

        # 准备包含无效签名的批量数据
        batch_data = {
            "signs": [
                "valid@domain#key1#123",
                "invalid_format",  # 无效格式
                "test2@domain#key2#456"
            ],
            "merchant_id": merchant.id,
            "department_id": department.id,
            "daily_limit": 50,
            "hourly_limit": 10,
            "active": 1,
            "description": "测试无效签名"
        }

        headers = {"Authorization": f"Bearer test_token"}

        response = client.post(
            "/api/v1/walmart-ck/batch",
            json=batch_data,
            headers=headers
        )

        # 应该返回400错误，因为有无效签名
        assert response.status_code == 400
        result = response.json()
        assert "格式不正确" in result["detail"]

    def test_batch_create_with_duplicate_signs(self):
        """测试包含重复签名的批量创建"""
        # 创建测试数据
        merchant = Merchant(name="测试商户", code="TEST_MERCHANT")
        self.db.add(merchant)
        self.db.commit()
        self.db.refresh(merchant)

        department = Department(
            name="测试部门",
            code="TEST_DEPT",
            merchant_id=merchant.id
        )
        self.db.add(department)
        self.db.commit()
        self.db.refresh(department)

        # 准备包含重复签名的批量数据
        batch_data = {
            "signs": [
                "test1@domain#key1#123",
                "test1@domain#key1#123",  # 重复签名
                "test2@domain#key2#456"
            ],
            "merchant_id": merchant.id,
            "department_id": department.id,
            "daily_limit": 50,
            "hourly_limit": 10,
            "active": 1,
            "description": "测试重复签名"
        }

        headers = {"Authorization": f"Bearer test_token"}

        response = client.post(
            "/api/v1/walmart-ck/batch",
            json=batch_data,
            headers=headers
        )

        # 应该返回400错误，因为有重复签名
        assert response.status_code == 400

    def test_batch_create_too_many_signs(self):
        """测试超过限制数量的批量创建"""
        # 创建测试数据
        merchant = Merchant(name="测试商户", code="TEST_MERCHANT")
        self.db.add(merchant)
        self.db.commit()
        self.db.refresh(merchant)

        department = Department(
            name="测试部门",
            code="TEST_DEPT",
            merchant_id=merchant.id
        )
        self.db.add(department)
        self.db.commit()
        self.db.refresh(department)

        # 准备超过100个签名的批量数据
        signs = [f"test{i}@domain#key{i}#{i}" for i in range(101)]
        batch_data = {
            "signs": signs,
            "merchant_id": merchant.id,
            "department_id": department.id,
            "daily_limit": 50,
            "hourly_limit": 10,
            "active": 1,
            "description": "测试超量签名"
        }

        headers = {"Authorization": f"Bearer test_token"}

        response = client.post(
            "/api/v1/walmart-ck/batch",
            json=batch_data,
            headers=headers
        )

        # 应该返回400错误，因为超过限制
        assert response.status_code == 400
        result = response.json()
        assert "最多支持创建100个CK" in result["detail"]

    def test_batch_create_permission_denied(self):
        """测试权限不足的批量创建"""
        # 创建测试数据
        merchant1 = Merchant(name="商户1", code="MERCHANT1")
        merchant2 = Merchant(name="商户2", code="MERCHANT2")
        self.db.add_all([merchant1, merchant2])
        self.db.commit()

        department1 = Department(
            name="部门1",
            code="DEPT1",
            merchant_id=merchant1.id
        )
        department2 = Department(
            name="部门2",
            code="DEPT2",
            merchant_id=merchant2.id
        )
        self.db.add_all([department1, department2])
        self.db.commit()

        # 用户属于商户1，但尝试为商户2创建CK
        batch_data = {
            "signs": ["test@domain#key#123"],
            "merchant_id": merchant2.id,  # 不同的商户
            "department_id": department2.id,
            "daily_limit": 50,
            "hourly_limit": 10,
            "active": 1,
            "description": "测试权限"
        }

        headers = {"Authorization": f"Bearer test_token"}

        response = client.post(
            "/api/v1/walmart-ck/batch",
            json=batch_data,
            headers=headers
        )

        # 应该返回403错误，因为权限不足
        assert response.status_code == 403
        result = response.json()
        assert "只能为自己所属的商户创建CK" in result["detail"]
