package services

import (
	"context"
	"fmt"
	"regexp"
	"time"

	"walmart-bind-card-processor/internal/config"
	"walmart-bind-card-processor/internal/model"

	"github.com/sirupsen/logrus"
)

// RetryStrategyService 重试策略服务
type RetryStrategyService struct {
	config    *config.Config
	logger    *logrus.Logger
	ckManager *CKManagerService
}

// NewRetryStrategyService 创建重试策略服务
func NewRetryStrategyService(config *config.Config, logger *logrus.Logger, ckManager *CKManagerService) *RetryStrategyService {
	return &RetryStrategyService{
		config:    config,
		logger:    logger,
		ckManager: ckManager,
	}
}

// RetryDecision 重试决策结果
type RetryDecision struct {
	ShouldRetry    bool          `json:"should_retry"`
	ShouldSwitchCK bool          `json:"should_switch_ck"`
	RetryDelay     time.Duration `json:"retry_delay"`
	Reason         string        `json:"reason"`
	Strategy       string        `json:"strategy"`
	ErrorType      string        `json:"error_type"`      // 错误类型：system, connection, retryable, ck_switch, non_retryable
}

// EvaluateBindCardRetry 评估绑卡重试策略
func (s *RetryStrategyService) EvaluateBindCardRetry(ctx context.Context, errorMsg string, errorCode string, retryCount int) *RetryDecision {
	retryConfig := s.config.RetryStrategy.BindCard

	s.logger.Infof("评估绑卡重试策略: error_msg=%s, error_code=%s, retry_count=%d", 
		errorMsg, errorCode, retryCount)

	// 1. 检查重试次数限制
	if retryCount >= retryConfig.MaxAttempts {
		return &RetryDecision{
			ShouldRetry:    false,
			ShouldSwitchCK: false,
			Reason:         fmt.Sprintf("已达到最大重试次数: %d", retryConfig.MaxAttempts),
			Strategy:       "max_attempts_reached",
			ErrorType:      "max_attempts",
		}
	}

	// 2. 检查API调用错误
	if errorCode == "API_CALL_ERROR" {
		// API调用失败通常需要重试，但不需要切换CK
		delay := s.calculateRetryDelay(retryConfig, retryCount+1)
		return &RetryDecision{
			ShouldRetry:    true,
			ShouldSwitchCK: false,
			RetryDelay:     delay,
			Reason:         "API调用失败，进行普通重试",
			Strategy:       "normal_retry",
			ErrorType:      "api_call",
		}
	}

	// 3. 检查是否为不可重试的错误（优先级最高）
	if s.isNonRetryableError(errorMsg, retryConfig.NonRetryableErrors) {
		return &RetryDecision{
			ShouldRetry:    false,
			ShouldSwitchCK: false,
			Reason:         fmt.Sprintf("不可重试的错误: %s", errorMsg),
			Strategy:       "non_retryable_error",
			ErrorType:      "non_retryable",
		}
	}

	// 4. 检查是否为系统错误（快速重试）
	if s.isSystemError(errorMsg, retryConfig.SystemErrors) {
		delay := s.calculateSystemErrorRetryDelay(retryCount + 1)
		return &RetryDecision{
			ShouldRetry:    true,
			ShouldSwitchCK: false,
			RetryDelay:     delay,
			Reason:         fmt.Sprintf("系统错误，快速重试: %s", errorMsg),
			Strategy:       "system_error_retry",
			ErrorType:      "system",
		}
	}

	// 5. 检查是否为连接错误（恢复重试）
	if s.isConnectionError(errorMsg, retryConfig.ConnectionErrors) {
		delay := s.calculateConnectionErrorRetryDelay(retryCount + 1)
		return &RetryDecision{
			ShouldRetry:    true,
			ShouldSwitchCK: false,
			RetryDelay:     delay,
			Reason:         fmt.Sprintf("连接错误，恢复重试: %s", errorMsg),
			Strategy:       "connection_error_retry",
			ErrorType:      "connection",
		}
	}

	// 6. 检查是否需要切换CK重试
	if s.isCKSwitchError(errorMsg, retryConfig.CKSwitchErrors) {
		delay := s.calculateRetryDelay(retryConfig, retryCount+1)
		return &RetryDecision{
			ShouldRetry:    true,
			ShouldSwitchCK: true,
			RetryDelay:     delay,
			Reason:         fmt.Sprintf("需要切换CK重试: %s", errorMsg),
			Strategy:       "ck_switch_retry",
			ErrorType:      "ck_switch",
		}
	}

	// 7. 检查是否为可重试的错误
	if s.isRetryableError(errorMsg, retryConfig.RetryableErrors) {
		delay := s.calculateRetryDelay(retryConfig, retryCount+1)
		return &RetryDecision{
			ShouldRetry:    true,
			ShouldSwitchCK: false,
			RetryDelay:     delay,
			Reason:         fmt.Sprintf("可重试的错误: %s", errorMsg),
			Strategy:       "normal_retry",
			ErrorType:      "retryable",
		}
	}

	// 8. 默认不重试
	return &RetryDecision{
		ShouldRetry:    false,
		ShouldSwitchCK: false,
		Reason:         fmt.Sprintf("未匹配任何重试条件: %s", errorMsg),
		Strategy:       "no_match",
		ErrorType:      "unknown",
	}
}

// EvaluateBalanceQueryRetry 评估金额查询重试策略
// 注意：金额查询不支持CK切换，因为必须使用与绑卡相同的CK查询同一用户的卡包
// 切换CK会查询到不同用户的卡包，无法找到目标卡号或获取错误金额
func (s *RetryStrategyService) EvaluateBalanceQueryRetry(ctx context.Context, errorMsg string, retryCount int) *RetryDecision {
	retryConfig := s.config.RetryStrategy.BalanceQuery

	s.logger.Infof("评估金额查询重试策略: error_msg=%s, retry_count=%d", errorMsg, retryCount)

	// 1. 检查重试次数限制
	if retryCount >= retryConfig.MaxAttempts {
		return &RetryDecision{
			ShouldRetry:    false,
			ShouldSwitchCK: false,
			Reason:         fmt.Sprintf("金额查询重试次数已达上限: %d", retryConfig.MaxAttempts),
			Strategy:       "max_attempts_reached",
		}
	}

	// 2. 检查是否为可重试的错误
	if s.isRetryableError(errorMsg, retryConfig.RetryableErrors) {
		delay := s.calculateBalanceQueryRetryDelay(retryConfig, retryCount+1)
		return &RetryDecision{
			ShouldRetry:    true,
			ShouldSwitchCK: false,
			RetryDelay:     delay,
			Reason:         fmt.Sprintf("金额查询可重试错误: %s", errorMsg),
			Strategy:       "balance_query_retry",
		}
	}

	// 3. 默认不重试
	return &RetryDecision{
		ShouldRetry:    false,
		ShouldSwitchCK: false,
		Reason:         fmt.Sprintf("金额查询不可重试错误: %s", errorMsg),
		Strategy:       "non_retryable",
	}
}

// isSystemError 检查是否为系统错误（快速重试）
func (s *RetryStrategyService) isSystemError(errorMsg string, patterns []string) bool {
	for _, pattern := range patterns {
		if matched, err := regexp.MatchString(pattern, errorMsg); err == nil && matched {
			s.logger.Debugf("匹配到系统错误模式: pattern=%s, error=%s", pattern, errorMsg)
			return true
		}
	}
	return false
}

// isConnectionError 检查是否为连接错误（恢复重试）
func (s *RetryStrategyService) isConnectionError(errorMsg string, patterns []string) bool {
	for _, pattern := range patterns {
		if matched, err := regexp.MatchString(pattern, errorMsg); err == nil && matched {
			s.logger.Debugf("匹配到连接错误模式: pattern=%s, error=%s", pattern, errorMsg)
			return true
		}
	}
	return false
}

// isRetryableError 检查是否为可重试的错误
func (s *RetryStrategyService) isRetryableError(errorMsg string, patterns []string) bool {
	for _, pattern := range patterns {
		if matched, err := regexp.MatchString(pattern, errorMsg); err == nil && matched {
			s.logger.Debugf("匹配到可重试错误模式: pattern=%s, error=%s", pattern, errorMsg)
			return true
		}
	}
	return false
}

// isCKSwitchError 检查是否需要切换CK的错误
func (s *RetryStrategyService) isCKSwitchError(errorMsg string, patterns []string) bool {
	for _, pattern := range patterns {
		if matched, err := regexp.MatchString(pattern, errorMsg); err == nil && matched {
			s.logger.Debugf("匹配到CK切换错误模式: pattern=%s, error=%s", pattern, errorMsg)
			return true
		}
	}
	return false
}

// isNonRetryableError 检查是否为不可重试的错误
func (s *RetryStrategyService) isNonRetryableError(errorMsg string, patterns []string) bool {
	for _, pattern := range patterns {
		if matched, err := regexp.MatchString(pattern, errorMsg); err == nil && matched {
			s.logger.Debugf("匹配到不可重试错误模式: pattern=%s, error=%s", pattern, errorMsg)
			return true
		}
	}
	return false
}

// calculateSystemErrorRetryDelay 计算系统错误重试延迟（快速重试）
func (s *RetryStrategyService) calculateSystemErrorRetryDelay(retryCount int) time.Duration {
	// 系统错误使用快速重试：200ms, 500ms, 800ms, 1100ms, 1400ms...
	baseDelay := 200 * time.Millisecond
	increment := 300 * time.Millisecond
	delay := baseDelay + time.Duration(retryCount-1)*increment

	// 最大延时限制为5秒
	maxDelay := 5 * time.Second
	if delay > maxDelay {
		delay = maxDelay
	}

	s.logger.Debugf("计算系统错误重试延迟: retry_count=%d, delay=%v", retryCount, delay)
	return delay
}

// calculateConnectionErrorRetryDelay 计算连接错误重试延迟（恢复重试）
func (s *RetryStrategyService) calculateConnectionErrorRetryDelay(retryCount int) time.Duration {
	// 连接错误使用恢复重试：1s, 2s, 3s, 4s, 5s...
	delay := time.Duration(retryCount) * time.Second

	// 最大延时限制为30秒
	maxDelay := 30 * time.Second
	if delay > maxDelay {
		delay = maxDelay
	}

	s.logger.Debugf("计算连接错误重试延迟: retry_count=%d, delay=%v", retryCount, delay)
	return delay
}

// calculateRetryDelay 计算绑卡重试延迟
func (s *RetryStrategyService) calculateRetryDelay(config config.BindCardRetryConfig, retryCount int) time.Duration {
	delay := config.InitialDelay

	// 指数退避
	for i := 1; i < retryCount; i++ {
		delay = time.Duration(float64(delay) * config.BackoffMultiplier)
		if delay > config.MaxDelay {
			delay = config.MaxDelay
			break
		}
	}

	s.logger.Debugf("计算绑卡重试延迟: retry_count=%d, delay=%v", retryCount, delay)
	return delay
}

// calculateBalanceQueryRetryDelay 计算金额查询重试延迟
func (s *RetryStrategyService) calculateBalanceQueryRetryDelay(config config.BalanceQueryRetryConfig, retryCount int) time.Duration {
	delay := config.InitialDelay
	
	// 指数退避
	for i := 1; i < retryCount; i++ {
		delay = time.Duration(float64(delay) * config.BackoffMultiplier)
		if delay > config.MaxDelay {
			delay = config.MaxDelay
			break
		}
	}
	
	s.logger.Debugf("计算金额查询重试延迟: retry_count=%d, delay=%v", retryCount, delay)
	return delay
}

// CreateRetryMessage 创建重试消息
func (s *RetryStrategyService) CreateRetryMessage(
	originalType model.MessageType,
	originalMessage map[string]interface{},
	decision *RetryDecision,
	recordID string,
	traceID string,
	retryCount int,
) *model.RetryMessage {
	
	retryMsg := &model.RetryMessage{
		OriginalMessageType: originalType,
		OriginalMessage:     originalMessage,
		RetryReason:         decision.Reason,
		RetryStrategy:       decision.Strategy,
		NextRetryAt:         time.Now().Add(decision.RetryDelay),
		RetryCount:          retryCount,
		TraceID:             traceID,
		RecordID:            recordID,
		CreatedAt:           time.Now(),
		ShouldSwitchCK:      decision.ShouldSwitchCK,
	}

	// 设置最大重试次数
	switch originalType {
	case model.MessageTypeBindCard:
		retryMsg.MaxRetries = s.config.RetryStrategy.BindCard.MaxAttempts
	case model.MessageTypeBalanceQuery:
		retryMsg.MaxRetries = s.config.RetryStrategy.BalanceQuery.MaxAttempts
	default:
		retryMsg.MaxRetries = 3 // 默认值
	}

	s.logger.Infof("创建重试消息: type=%s, strategy=%s, next_retry_at=%v", 
		originalType, decision.Strategy, retryMsg.NextRetryAt)

	return retryMsg
}

// ShouldTriggerCallback 判断是否应该触发回调
func (s *RetryStrategyService) ShouldTriggerCallback(messageType model.MessageType, decision *RetryDecision, retryCount int) bool {
	// 如果决定重试，则不触发回调
	if decision.ShouldRetry {
		return false
	}

	// 对于金额查询，检查是否在静默失败范围内
	if messageType == model.MessageTypeBalanceQuery {
		if decision.Strategy == "silent_failure" {
			return false // 静默失败，不触发回调
		}
	}

	// 其他情况都触发回调
	return true
}

// HandleCKSwitchError 处理需要切换CK的错误
func (s *RetryStrategyService) HandleCKSwitchError(ckID uint, merchantID uint, errorMsg string) (*model.WalmartCK, error) {
	s.logger.WithFields(logrus.Fields{
		"ck_id":       ckID,
		"merchant_id": merchantID,
		"error_msg":   errorMsg,
	}).Warning("处理CK切换错误")

	// 1. 禁用当前CK
	if err := s.ckManager.DisableCK(ckID, errorMsg); err != nil {
		s.logger.WithError(err).Error("禁用CK失败")
		return nil, fmt.Errorf("禁用CK失败: %w", err)
	}

	// 2. 获取新的可用CK
	newCK, err := s.ckManager.GetNextAvailableCK(merchantID, []uint{ckID})
	if err != nil {
		s.logger.WithError(err).Error("获取新CK失败")
		return nil, fmt.Errorf("获取新CK失败: %w", err)
	}

	s.logger.WithFields(logrus.Fields{
		"old_ck_id": ckID,
		"new_ck_id": newCK.ID,
	}).Info("CK切换成功")

	return newCK, nil
}

// HandleCKSwitchErrorWithWeight 使用权重算法处理CK切换错误
func (s *RetryStrategyService) HandleCKSwitchErrorWithWeight(ckID uint, merchantID uint, departmentID *uint, errorMsg string) (*model.WalmartCK, error) {
	s.logger.WithFields(logrus.Fields{
		"ck_id":         ckID,
		"merchant_id":   merchantID,
		"department_id": departmentID,
		"error_msg":     errorMsg,
	}).Warning("使用权重算法处理CK切换错误")

	// 1. 禁用当前CK
	if err := s.ckManager.DisableCK(ckID, errorMsg); err != nil {
		s.logger.WithError(err).Error("禁用CK失败")
		return nil, fmt.Errorf("禁用CK失败: %w", err)
	}

	// 2. 使用权重算法获取新的可用CK
	newCK, err := s.ckManager.GetAvailableCKWithWeight(merchantID, departmentID)
	if err != nil {
		s.logger.WithError(err).Error("使用权重算法获取新CK失败")
		return nil, fmt.Errorf("使用权重算法获取新CK失败: %w", err)
	}

	s.logger.WithFields(logrus.Fields{
		"old_ck_id":     ckID,
		"new_ck_id":     newCK.ID,
		"new_dept_id":   newCK.DepartmentID,
	}).Info("权重算法CK切换成功")

	return newCK, nil
}
