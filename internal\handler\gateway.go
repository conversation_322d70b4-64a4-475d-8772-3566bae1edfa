package handler

import (
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"time"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"

	"walmart-bind-card-gateway/internal/model"
	"walmart-bind-card-gateway/internal/service"
	"walmart-bind-card-gateway/pkg/signature"
)

// GatewayHandler 网关处理器
type GatewayHandler struct {
	gatewayService     service.GatewayService
	merchantService    model.MerchantService
	signatureValidator *signature.SignatureValidator
	logger             *zap.Logger
	db                 interface{ GetConnectionStats() map[string]interface{} } // 数据库连接池监控
	cardService        model.CardService // 卡记录服务
}

// NewGatewayHandler 创建网关处理器
func NewGatewayHandler(gatewayService service.GatewayService, merchantService model.MerchantService, cardService model.CardService, db interface{ GetConnectionStats() map[string]interface{} }, logger *zap.Logger) *GatewayHandler {
	return &GatewayHandler{
		gatewayService:     gatewayService,
		merchantService:    merchantService,
		cardService:        cardService,
		signatureValidator: signature.NewSignatureValidator(300), // 5分钟时间窗口
		logger:             logger,
		db:                 db,
	}
}

// BindCard 绑卡请求处理器 - 与Python系统完全兼容
func (h *GatewayHandler) BindCard(c *gin.Context) {
	startTime := time.Now()

	// 1. 获取请求头 - 与Python系统完全一致
	apiKey := c.GetHeader("api-key")
	xTimestamp := c.GetHeader("X-Timestamp")
	xNonce := c.GetHeader("X-Nonce")
	xSignature := c.GetHeader("X-Signature")

	// 2. 验证必需的请求头
	if apiKey == "" {
		c.JSON(http.StatusBadRequest, model.ErrorResponseWithCode(model.ErrAPIKeyMissing))
		return
	}
	if xTimestamp == "" || xNonce == "" || xSignature == "" {
		c.JSON(http.StatusBadRequest, model.ErrorResponseWithCode(model.ErrInvalidSignature))
		return
	}

	// 3. 读取请求体
	requestBody, err := io.ReadAll(c.Request.Body)
	if err != nil {
		h.logger.Error("读取请求体失败", zap.Error(err))
		c.JSON(http.StatusBadRequest, model.ErrorResponseWithCode(model.ErrParamsError))
		return
	}

	// 4. 解析请求参数
	var req model.BindCardRequest
	if err := json.Unmarshal(requestBody, &req); err != nil {
		h.logger.Debug("参数解析失败", zap.Error(err))
		c.JSON(http.StatusBadRequest, model.ErrorResponseWithCode(model.ErrParamsError))
		return
	}

	// 5. 验证商户和API密钥
	merchant, err := h.merchantService.ValidateAPIKey(apiKey)
	if err != nil {
		h.logger.Warn("商户验证失败", zap.Error(err), zap.String("api_key", apiKey))
		c.JSON(http.StatusBadRequest, model.ErrorResponseWithCode(model.ErrAPIKeyInvalid))
		return
	}

	// 8. 验证商户编码 - 与Python系统完全一致
	if req.MerchantCode != merchant.Code {
		h.logger.Warn("商户编码与API密钥不匹配",
			zap.String("api_key", apiKey),
			zap.String("request_merchant_code", req.MerchantCode),
			zap.String("actual_merchant_code", merchant.Code))
		c.JSON(http.StatusBadRequest, model.ErrorResponseWithCode(model.ErrMerchantCodeMismatch))
		return
	}

	// 9. 验证签名 - 与Python系统完全一致
	secretKey := merchant.GetAPISecret()

	// 调试：生成期望的签名
	expectedSignature, _ := h.signatureValidator.GenerateSignature(requestBody, secretKey, xTimestamp, xNonce)
	h.logger.Info("签名调试信息",
		zap.String("api_key", apiKey),
		zap.String("received_signature", xSignature),
		zap.String("expected_signature", expectedSignature),
		zap.String("secret_key", secretKey),
		zap.String("timestamp", xTimestamp),
		zap.String("nonce", xNonce),
		zap.String("request_body", string(requestBody)))

	if err := h.signatureValidator.ValidateSignature(requestBody, secretKey, xTimestamp, xNonce, xSignature); err != nil {
		h.logger.Warn("签名验证失败", zap.Error(err), zap.String("api_key", apiKey))
		c.JSON(http.StatusBadRequest, model.ErrorResponseWithCode(model.ErrInvalidSignature))
		return
	}

	// 10. 严格数据验证 - 与Python系统完全一致
	if err := h.ValidateBindCardRequestStrict(&req); err != nil {
		c.JSON(http.StatusBadRequest, model.ErrorResponseWithDetails(model.ErrParamsError, err.Error()))
		return
	}

	// 11. 卡号重复性检查 - 与Python系统完全一致
	exists, err := h.cardService.CheckCardExists(c.Request.Context(), req.CardNumber)
	if err != nil {
		h.logger.Error("检查卡号重复性失败", zap.Error(err), zap.String("card_number", req.CardNumber))
		c.JSON(http.StatusInternalServerError, model.ErrorResponseWithCode(model.ErrSystemError))
		return
	}
	if exists {
		h.logger.Warn("卡号已存在", zap.String("card_number", req.CardNumber))
		c.JSON(http.StatusBadRequest, model.ErrorResponseWithCode(model.ErrCardAlreadyBound))
		return
	}

	// 12. 生成请求ID和记录ID
	requestID := h.gatewayService.GenerateRequestID()
	recordID := h.gatewayService.GenerateRequestID() // 生成唯一的记录ID

	// 13. 构建内部请求对象
	internalReq := &model.InternalBindRequest{
		RequestID:       requestID,
		RecordID:        recordID,        // 预生成的记录ID
		CardNumber:      req.CardNumber,
		CardPassword:    req.CardPassword,
		MerchantCode:    req.MerchantCode,
		MerchantOrderID: req.MerchantOrderID,
		Amount:          float64(req.Amount), // 转换为float64
		ClientIP:        c.ClientIP(),
		UserAgent:       c.GetHeader("User-Agent"),
		Headers:         extractHeaders(c),
		ReceivedAt:      startTime,
		MerchantID:      merchant.ID, // 添加商户ID
		Debug:           false,       // 默认为false
	}

	// 添加扩展数据
	if req.ExtData != nil {
		internalReq.ExtData = *req.ExtData
	}

	// 添加调试模式标识
	if req.Debug != nil {
		internalReq.Debug = *req.Debug
		internalReq.IsTestMode = *req.Debug  // 同时设置测试模式标识
	}

	// 14. 提交到批量处理器（非阻塞）
	if err := h.gatewayService.SubmitRequest(internalReq); err != nil {
		h.logger.Error("提交请求失败", zap.Error(err), zap.String("request_id", requestID))
		c.JSON(http.StatusServiceUnavailable, model.ErrorResponseWithCode(model.ErrSystemError))
		return
	}

	// 15. 返回响应 - 与Python系统完全一致
	bindCardResponse := &model.BindCardResponse{
		RecordID:        recordID, // 使用预生成的记录ID
		RequestID:       requestID,
		Status:          "processing",
		MerchantOrderID: req.MerchantOrderID,
		Amount:          req.Amount,
	}

	// 使用标准API响应格式，包含code字段
	response := model.SuccessResponse(bindCardResponse)
	c.JSON(http.StatusOK, response)

	// 16. 记录处理时间
	h.gatewayService.RecordLatency("request_receive", time.Since(startTime))

	h.logger.Info("绑卡请求处理完成",
		zap.String("request_id", requestID),
		zap.String("merchant_code", req.MerchantCode),
		zap.String("merchant_order_id", req.MerchantOrderID),
		zap.Int("amount", req.Amount),
		zap.Duration("duration", time.Since(startTime)),
	)
}

// GetStatus 获取请求状态
func (h *GatewayHandler) GetStatus(c *gin.Context) {
	requestID := c.Param("request_id")
	if requestID == "" {
		c.JSON(http.StatusBadRequest, model.ErrorResponse(1, "请求ID不能为空"))
		return
	}
	
	// TODO: 实现状态查询逻辑
	// 这里可以从Redis或数据库查询状态
	
	response := model.SuccessResponse(map[string]interface{}{
		"request_id": requestID,
		"status":     "processing",
		"message":    "请求处理中",
	})
	
	c.JSON(http.StatusOK, response)
}

// Health 健康检查
func (h *GatewayHandler) Health(c *gin.Context) {
	// 简单的健康检查
	response := map[string]interface{}{
		"status":    "healthy",
		"timestamp": time.Now().Unix(),
		"service":   "walmart-gateway",
	}
	
	c.JSON(http.StatusOK, response)
}

// DBStats 数据库连接池统计
func (h *GatewayHandler) DBStats(c *gin.Context) {
	stats := h.db.GetConnectionStats()

	response := map[string]interface{}{
		"status":    "success",
		"timestamp": time.Now().Unix(),
		"db_stats":  stats,
	}

	c.JSON(http.StatusOK, response)
}

// Metrics 监控指标
func (h *GatewayHandler) Metrics(c *gin.Context) {
	metrics := h.gatewayService.GetMetrics()
	c.JSON(http.StatusOK, metrics)
}

// FastCORS 快速CORS中间件
func (h *GatewayHandler) FastCORS() gin.HandlerFunc {
	return func(c *gin.Context) {
		c.Header("Access-Control-Allow-Origin", "*")
		c.Header("Access-Control-Allow-Methods", "POST, GET, OPTIONS")
		c.Header("Access-Control-Allow-Headers", "Content-Type, Authorization")
		
		if c.Request.Method == "OPTIONS" {
			c.AbortWithStatus(http.StatusNoContent)
			return
		}
		
		c.Next()
	}
}

// FastRateLimit 快速限流中间件
func (h *GatewayHandler) FastRateLimit() gin.HandlerFunc {
	return func(c *gin.Context) {
		// 使用网关服务的限流器
		if !h.gatewayService.CheckRateLimit(c.ClientIP()) {
			c.JSON(http.StatusTooManyRequests, model.ErrorResponse(1, "请求过于频繁"))
			c.Abort()
			return
		}
		c.Next()
	}
}

// RequestLogger 请求日志中间件
func (h *GatewayHandler) RequestLogger() gin.HandlerFunc {
	return gin.LoggerWithFormatter(func(param gin.LogFormatterParams) string {
		// 自定义日志格式
		h.logger.Info("HTTP请求",
			zap.String("method", param.Method),
			zap.String("path", param.Path),
			zap.Int("status", param.StatusCode),
			zap.Duration("latency", param.Latency),
			zap.String("client_ip", param.ClientIP),
			zap.String("user_agent", param.Request.UserAgent()),
		)
		return ""
	})
}

// Recovery 恢复中间件
func (h *GatewayHandler) Recovery() gin.HandlerFunc {
	return gin.CustomRecovery(func(c *gin.Context, recovered interface{}) {
		h.logger.Error("请求处理panic",
			zap.Any("recovered", recovered),
			zap.String("path", c.Request.URL.Path),
			zap.String("method", c.Request.Method),
		)
		
		c.JSON(http.StatusInternalServerError, model.ErrorResponse(1, "服务器内部错误"))
	})
}

// extractHeaders 提取请求头
func extractHeaders(c *gin.Context) map[string]string {
	headers := make(map[string]string)
	
	// 只提取重要的头部信息
	importantHeaders := []string{
		"Content-Type",
		"Authorization",
		"X-Forwarded-For",
		"X-Real-IP",
		"X-Request-ID",
		"User-Agent",
	}
	
	for _, headerName := range importantHeaders {
		if value := c.GetHeader(headerName); value != "" {
			headers[headerName] = value
		}
	}
	
	return headers
}

// ValidateBindCardRequestStrict 严格验证绑卡请求 - 与Python系统完全一致
func (h *GatewayHandler) ValidateBindCardRequestStrict(req *model.BindCardRequest) error {
	// 1. 卡号验证 - 使用CardService的严格验证
	if err := h.cardService.ValidateCardNumber(req.CardNumber); err != nil {
		return err
	}

	// 2. 卡密码验证 - 与Python系统完全一致
	if err := service.ValidateCardPassword(req.CardPassword); err != nil {
		return err
	}

	// 3. 商户代码验证 - 与Python系统完全一致
	if err := service.ValidateMerchantCode(req.MerchantCode); err != nil {
		return err
	}

	// 4. 商户订单号验证 - 与Python系统完全一致
	if err := service.ValidateMerchantOrderID(req.MerchantOrderID); err != nil {
		return err
	}

	// 5. 金额验证 - 与Python系统完全一致
	if err := service.ValidateAmount(req.Amount); err != nil {
		return err
	}

	// 6. 扩展数据验证
	if req.ExtData != nil && len(*req.ExtData) > 512 {
		return fmt.Errorf("扩展数据长度不能超过512字符")
	}

	return nil
}

// ValidateBindCardRequest 验证绑卡请求 - 与Python系统完全一致
func (h *GatewayHandler) ValidateBindCardRequest(req *model.BindCardRequest) error {
	// 卡号验证
	if len(req.CardNumber) < 1 || len(req.CardNumber) > 50 {
		return fmt.Errorf("卡号长度必须在1-50位之间")
	}

	// 密码验证
	if len(req.CardPassword) < 1 || len(req.CardPassword) > 50 {
		return fmt.Errorf("卡密码长度必须在1-50位之间")
	}

	// 商户代码验证
	if len(req.MerchantCode) < 1 || len(req.MerchantCode) > 50 {
		return fmt.Errorf("商户代码长度必须在1-50位之间")
	}

	// 商户订单号验证
	if len(req.MerchantOrderID) < 1 || len(req.MerchantOrderID) > 255 {
		return fmt.Errorf("商户订单号长度必须在1-255位之间")
	}

	// 金额验证（单位：分，最小100分即1元）
	if req.Amount < 100 {
		return fmt.Errorf("订单金额必须大于等于100分（1元）")
	}

	// 扩展数据验证
	if req.ExtData != nil && len(*req.ExtData) > 512 {
		return fmt.Errorf("扩展数据长度不能超过512字符")
	}

	return nil
}

// BatchStatus 批量状态查询
func (h *GatewayHandler) BatchStatus(c *gin.Context) {
	var requestIDs []string
	if err := c.ShouldBindJSON(&requestIDs); err != nil {
		c.JSON(http.StatusBadRequest, model.ErrorResponse(1, "参数格式错误"))
		return
	}
	
	if len(requestIDs) == 0 {
		c.JSON(http.StatusBadRequest, model.ErrorResponse(1, "请求ID列表不能为空"))
		return
	}
	
	if len(requestIDs) > 100 {
		c.JSON(http.StatusBadRequest, model.ErrorResponse(1, "一次最多查询100个请求"))
		return
	}
	
	// TODO: 实现批量状态查询逻辑
	
	response := model.SuccessResponse(map[string]interface{}{
		"count":   len(requestIDs),
		"results": []map[string]interface{}{},
	})
	
	c.JSON(http.StatusOK, response)
}

// Stats 统计信息
func (h *GatewayHandler) Stats(c *gin.Context) {
	stats := map[string]interface{}{
		"gateway_metrics": h.gatewayService.GetMetrics(),
		"timestamp":       time.Now().Unix(),
		"uptime":         time.Since(startTime).String(),
	}
	
	c.JSON(http.StatusOK, stats)
}

var startTime = time.Now()

// Ping 简单的ping接口
func (h *GatewayHandler) Ping(c *gin.Context) {
	c.JSON(http.StatusOK, map[string]interface{}{
		"message": "pong",
		"timestamp": time.Now().Unix(),
	})
}


