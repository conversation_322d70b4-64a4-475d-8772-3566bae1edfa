import json
import time
import hashlib
import hmac
import random
import string
from typing import Optional, Dict, Any
from curl_cffi import requests
from sqlalchemy.orm import Session

from app.core.logging import get_logger
from app.core.config_manager import WalmartAPIConfigManager


logger = get_logger("walmart_api")


class WalmartAPI:
    """沃尔玛微信小程序API封装"""

    def __init__(self, encryption_key: str, version: str, sign: str, base_url: Optional[str] = None):
        """初始化API客户端

        Args:
            encryption_key: Base64编码的加密密钥
            version: API版本号
            sign: 用户登录凭证，格式为"authkey@signkey"
            base_url: API基础URL（可选，如果不提供则从配置获取）
        """
        self.encryption_key = encryption_key
        self.version = version
        self.sign = sign
        self.base_url = base_url  # 如果为None，将在需要时动态获取

    @classmethod
    def create_legacy(cls, base_url: str, encryption_key: str, token: Optional[str], version: str, sign: Optional[str] = None):
        """
        创建API实例的遗留方法（保持向后兼容）

        Args:
            base_url: API基础URL
            encryption_key: Base64编码的加密密钥
            token: 用户令牌（已废弃，将被忽略）
            version: API版本号
            sign: 用户登录凭证

        Returns:
            WalmartAPI: API实例
        """
        if token is not None:
            logger.warning("token参数已废弃，将被忽略")

        if sign is None:
            raise ValueError("sign参数是必需的")

        return cls(encryption_key=encryption_key, version=version, sign=sign, base_url=base_url)

    @classmethod
    async def create_with_config(cls, encryption_key: str, version: str, sign: str, db: Optional[Session] = None):
        """
        使用配置管理器创建API实例

        Args:
            encryption_key: Base64编码的加密密钥
            version: API版本号
            sign: 用户登录凭证
            db: 数据库会话（用于获取配置）

        Returns:
            WalmartAPI: API实例
        """
        base_url = await WalmartAPIConfigManager.get_api_base_url(db)
        return cls(encryption_key=encryption_key, version=version, sign=sign, base_url=base_url)

    async def _get_base_url(self, db: Optional[Session] = None) -> str:
        """获取基础URL"""
        if self.base_url:
            return self.base_url

        # 动态获取配置
        self.base_url = await WalmartAPIConfigManager.get_api_base_url(db)
        return self.base_url

    def _generate_nonce(self, length=10):
        """生成随机nonce字符串

        Args:
            length: nonce字符串长度

        Returns:
            随机生成的nonce字符串
        """
        return "".join(random.choice(string.ascii_lowercase) for _ in range(length))

    def _calculate_signature(self, request_body):
        """计算请求签名

        Args:
            request_body: 请求体数据

        Returns:
            计算出的签名字符串
        """
        # 使用原始的base64密钥作为HMAC密钥
        key = self.encryption_key.encode("utf-8")

        # 对请求体参数进行排序，确保参数顺序一致
        sorted_body = {}
        # 按照键的字母顺序排序
        for k in sorted(request_body.keys()):
            sorted_body[k] = request_body[k]

        # 将排序后的请求体转换为JSON字符串
        body_str = json.dumps(sorted_body, separators=(",", ":"))

        # 计算HMAC-SHA256签名
        message = body_str.encode("utf-8")
        signature = hmac.new(key, message, hashlib.sha256).hexdigest().upper()

        return signature

    async def make_request(self, endpoint: str, request_body: dict, method: str = "POST", db: Optional[Session] = None):
        """发送API请求

        Args:
            endpoint: API端点路径
            request_body: 请求体数据
            method: HTTP请求方法，默认为POST
            db: 数据库会话（用于获取配置）

        Returns:
            API响应对象
        """
        # 获取基础URL
        base_url = await self._get_base_url(db)
        # 如果base_url中已经包含 endpoint，则不重复添加
        if base_url.endswith(endpoint):
            url = base_url
        else:
            url = f"{base_url}{endpoint}"

        # 使用固定的Referer配置
        referer = await WalmartAPIConfigManager.get_referer()

        # 生成请求头
        timestamp = str(int(time.time() * 1000))  # 当前时间戳（毫秒）
        nonce = self._generate_nonce()

        headers = {
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 MicroMessenger/7.0.20.1781(0x6700143B) NetType/WIFI MiniProgramEnv/Windows WindowsWechat/WMPF WindowsWechat(0x63090a13) XWEB/8555",
            "Connection": "keep-alive",
            "Accept": "*/*",
            "Accept-Encoding": "gzip, deflate, br",
            "Content-Type": "application/json",
            "xweb_xhr": "1",
            "Referer": referer,
            "Accept-Language": "zh-CN,zh;q=0.9"
        }

        # 添加必要的请求头
        if self.version:
            headers["version"] = self.version
        if nonce:
            headers["nonce"] = nonce
        if timestamp:
            headers["timestamp"] = timestamp

        # 计算签名并添加到请求头
        signature = self._calculate_signature(request_body)
        headers["signature"] = signature

        # 发送请求
        if method.upper() == "POST":
            response = requests.post(url, headers=headers, json=request_body)
        elif method.upper() == "GET":
            response = requests.get(url, headers=headers, params=request_body)
        else:
            raise ValueError(f"不支持的HTTP方法: {method}")

        return response

    def make_request_sync(self, endpoint: str, request_body: dict, method: str = "POST"):
        """
        同步版本的请求方法（保持向后兼容）

        Args:
            endpoint: API端点路径
            request_body: 请求体数据
            method: HTTP请求方法，默认为POST

        Returns:
            API响应对象
        """
        # 如果没有base_url，使用默认配置
        if not self.base_url:
            self.base_url = WalmartAPIConfigManager.DEFAULT_CONFIG["api_base_url"]

        url = f"{self.base_url}{endpoint}"

        # 使用默认Referer
        referer = WalmartAPIConfigManager.DEFAULT_CONFIG["referer"]

        # 生成请求头
        timestamp = str(int(time.time() * 1000))  # 当前时间戳（毫秒）
        nonce = self._generate_nonce()

        headers = {
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 MicroMessenger/7.0.20.1781(0x6700143B) NetType/WIFI MiniProgramEnv/Windows WindowsWechat/WMPF WindowsWechat(0x63090a13) XWEB/8555",
            "Connection": "keep-alive",
            "Accept": "*/*",
            "Accept-Encoding": "gzip, deflate, br",
            "Content-Type": "application/json",
            "xweb_xhr": "1",
            "Referer": referer,
            "Accept-Language": "zh-CN,zh;q=0.9"
        }

        # 添加必要的请求头
        if self.version:
            headers["version"] = self.version
        if nonce:
            headers["nonce"] = nonce
        if timestamp:
            headers["timestamp"] = timestamp

        # 计算签名并添加到请求头
        signature = self._calculate_signature(request_body)
        headers["signature"] = signature

        # 发送请求
        if method.upper() == "POST":
            response = requests.post(url, headers=headers, json=request_body)
        elif method.upper() == "GET":
            response = requests.get(url, headers=headers, params=request_body)
        else:
            raise ValueError(f"不支持的HTTP方法: {method}")

        return response

    def handle_error(self, error_code, response_data):
        """处理API错误

        Args:
            error_code: 错误代码
            response_data: 完整响应数据

        Returns:
            bool: 是否需要重试并更换新的walmart_ck
        """
        # 提取完整的错误信息
        error_info = response_data.get("error", {})
        error_message = error_info.get("message", "未知错误")
        error_code_from_api = error_info.get("errorcode", error_code)
        log_id = response_data.get("logId", "")

        # 记录完整的错误信息，区分不同类型的错误
        if error_code_from_api == 203 and "请先去登录" in error_message:
            logger.error(
                f"API错误 - CK无效需要重新登录: errorcode={error_code_from_api}, message={error_message}, "
                f"logId={log_id}"
            )
        else:
            logger.error(
                f"API错误: errorcode={error_code_from_api}, message={error_message}, "
                f"logId={log_id}, raw_response={response_data}"
            )

        # 禁止重试的情况
        if error_code == 10131 or "该电子卡已被其他用户" in error_message:
            logger.info("禁止重试 - 卡已被其他用户绑定")
            return False

        # 需要重试并更换新的walmart_ck的情况
        if error_code == 203 and "请先去登录" in error_message:
            logger.warning("CK无效需要重新登录，将禁用当前CK并重试")
            return True

        # 【新增】单日绑卡限制错误，需要禁用当前CK并重试
        if error_code == 110224 and "您绑卡已超过单日20张限制" in error_message:
            logger.warning("CK达到单日绑卡限制，将禁用当前CK并重试")
            return True

        # 【新增】错误次数过多，需要禁用当前CK并重试
        if error_code == 110134 and "错误次数过多" in error_message:
            logger.warning("CK错误次数过多，将禁用当前CK并重试")
            return True

        # 【新增】数据异常错误，需要禁用当前CK并重试
        if error_code == 110444 and "数据异常" in error_message:
            logger.warning("CK数据异常，将禁用当前CK并重试")
            return True

        # 【新增】服务器繁忙错误，需要禁用当前CK并重试
        # 只需要判断状态码200，因为可能有多种错误信息
        if error_code == 200:
            logger.warning(f"检测到错误码200，将禁用当前CK并重试: {error_message}")
            return True

        # 其他错误码处理，不需要重试
        if error_code in [5042]:
            logger.info("需要重新登录，但不重试")
        elif error_code == 5041:
            logger.info("需要打开特定页面")
        elif error_code == 506:
            logger.info("需要切换到首页")
        elif error_code == 110445:
            logger.info("验签问题: 请求过期")
        elif error_code == 9999:
            logger.info("需要隐式登录")

        # 默认不重试
        return False

    async def bind_card(self, card_no: str, card_pwd: str, db: Optional[Session] = None, debug: bool = False):
        """绑定卡

        Args:
            card_no: 卡号
            card_pwd: 卡密码
            db: 数据库会话（用于获取配置）
            debug: 调试模式，为True时返回模拟数据

        Returns:
            API响应对象

        Raises:
            ValueError: 当参数验证失败时
        """
        # 严格的参数验证
        if not card_no or not isinstance(card_no, str) or not card_no.strip():
            logger.error("绑卡失败: 卡号参数无效 - 卡号不能为空、None或空字符串")
            raise ValueError("卡号不能为空、None或空字符串")

        if not card_pwd or not isinstance(card_pwd, str) or not card_pwd.strip():
            logger.error("绑卡失败: 卡密码参数无效 - 卡密码不能为空、None或空字符串")
            raise ValueError("卡密码不能为空、None或空字符串")

        # 清理参数（去除首尾空格）
        card_no = card_no.strip()
        card_pwd = card_pwd.strip()

        # 记录绑卡开始（debug和生产模式都需要记录）
        if debug:
            logger.info(f"[TEST_MODE] 绑定卡: 卡号={card_no[:6]}***（参数验证通过，将跳过HTTP请求）")
        else:
            logger.info(f"绑定卡: 卡号={card_no[:6]}***（参数验证通过）")

        # 构建请求体
        request_body = {
            "sign": self.sign,
            "storeId": "",
            "userPhone": "",
            "cardNo": card_no,
            "cardPwd": card_pwd,
            "currentPage": 0,
            "pageSize": 0,
        }

        # 发送请求（debug模式下使用模拟响应）
        if debug:
            logger.info(f"[TEST_MODE] 跳过HTTP请求，返回模拟绑卡响应")
            response = self._create_mock_bind_response(card_no)
        else:
            response = await self.make_request("/app/card/mem/bind.json", request_body, db=db)

        # 检查响应
        try:
            response_data = response.json()
            if response_data.get("status", False):
                logger.info(f"成功绑定卡: {card_no}")
            else:
                error_info = response_data.get("error", {})
                error_message = error_info.get("message", "未知错误")
                error_code = error_info.get("errorcode", "UNKNOWN")
                log_id = response_data.get("logId", "")

                logger.error(
                    f"绑定卡失败: card_no={card_no}, errorcode={error_code}, "
                    f"message={error_message}, logId={log_id}"
                )
        except Exception as e:
            logger.error(f"解析响应失败: card_no={card_no}, error={e}")

        return response

    def bind_card_sync(self, card_no: str, card_pwd: str, sign: Optional[str] = None):
        """
        绑定卡的同步版本（保持向后兼容）

        Args:
            card_no: 卡号
            card_pwd: 卡密码
            sign: 用户登录凭证（可选，如果不提供则使用实例的sign）

        Returns:
            API响应对象

        Raises:
            ValueError: 当参数验证失败时
        """
        # 严格的参数验证
        if not card_no or not isinstance(card_no, str) or not card_no.strip():
            logger.error("绑卡失败: 卡号参数无效 - 卡号不能为空、None或空字符串")
            raise ValueError("卡号不能为空、None或空字符串")

        if not card_pwd or not isinstance(card_pwd, str) or not card_pwd.strip():
            logger.error("绑卡失败: 卡密码参数无效 - 卡密码不能为空、None或空字符串")
            raise ValueError("卡密码不能为空、None或空字符串")

        # 清理参数（去除首尾空格）
        card_no = card_no.strip()
        card_pwd = card_pwd.strip()

        logger.info(f"绑定卡: 卡号={card_no[:6]}***（参数验证通过）")

        # 使用传入的sign或实例的sign
        used_sign = sign if sign is not None else self.sign
        if not used_sign:
            logger.error("绑卡失败: sign参数无效 - sign参数是必需的")
            raise ValueError("sign参数是必需的")

        # 构建请求体
        request_body = {
            "sign": used_sign,
            "storeId": "",
            "userPhone": "",
            "cardNo": card_no,
            "cardPwd": card_pwd,
            "currentPage": 0,
            "pageSize": 0,
        }

        # 发送请求
        response = self.make_request_sync("/app/card/mem/bind.json", request_body)

        # 检查响应
        try:
            response_data = response.json()
            if response_data.get("status", False):
                logger.info(f"成功绑定卡: {card_no}")
            else:
                error_info = response_data.get("error", {})
                logger.error(f"绑定卡失败: {error_info.get('message', '未知错误')}")
        except Exception as e:
            logger.error(f"解析响应失败: {e}")

        return response

    async def query_user(self, db: Optional[Session] = None, debug: bool = False):
        """查询用户信息

        Args:
            db: 数据库会话（用于获取配置）
            debug: 调试模式，为True时返回模拟数据

        Returns:
            API响应对象
        """
        if debug:
            logger.info("[TEST_MODE] 查询用户信息（使用模拟数据）")
            return self._create_mock_user_response()
        else:
            logger.info("查询用户信息")

        # 构建请求体
        request_body = {
            "currentPage": 0,
            "pageSize": 0,
            "sign": self.sign,
        }

        # 发送请求
        response = await self.make_request("/app/mem/userInfo.json", request_body, db=db)

        # 检查响应
        try:
            response_data = response.json()
            status = response_data.get("status")
            error_info = response_data.get("error", {})
            error_code = error_info.get("errorcode")
            error_message = error_info.get("message")
            log_id = response_data.get("logId", "")

            if status is True:
                # 检查是否有严重错误码
                if error_code in [-1, -999, 401, 403, 404, 500]:
                    logger.error(
                        f"查询用户信息失败: status=true但存在严重错误 | "
                        f"errorcode={error_code} | message={error_message} | logId={log_id}"
                    )
                else:
                    data = response_data.get("data", {})
                    card_count = data.get("cardCount", 0)
                    nick_name = data.get("nickName", "")
                    logger.info(
                        f"成功查询用户信息 | cardCount={card_count} | "
                        f"nickName={nick_name} | logId={log_id}"
                    )
            else:
                logger.error(
                    f"查询用户信息失败: status=false | "
                    f"errorcode={error_code} | message={error_message} | logId={log_id}"
                )
        except Exception as e:
            logger.error(f"解析响应失败: {e}")

        return response

    def query_user_sync(self, sign: Optional[str] = None):
        """
        查询用户信息的同步版本（保持向后兼容）

        Args:
            sign: 用户登录凭证（可选，如果不提供则使用实例的sign）

        Returns:
            API响应对象
        """
        logger.info("查询用户信息")

        # 使用传入的sign或实例的sign
        used_sign = sign if sign is not None else self.sign
        if not used_sign:
            raise ValueError("sign参数是必需的")

        # 构建请求体
        request_body = {
            "currentPage": 0,
            "pageSize": 0,
            "sign": used_sign,
        }

        # 发送请求
        response = self.make_request_sync("/app/mem/userInfo.json", request_body)

        # 检查响应
        try:
            response_data = response.json()
            status = response_data.get("status")
            error_info = response_data.get("error", {})
            error_code = error_info.get("errorcode")
            error_message = error_info.get("message")
            log_id = response_data.get("logId", "")

            if status is True:
                # 检查是否有严重错误码
                if error_code in [-1, -999, 401, 403, 404, 500]:
                    logger.error(
                        f"查询用户信息失败: status=true但存在严重错误 | "
                        f"errorcode={error_code} | message={error_message} | logId={log_id}"
                    )
                else:
                    data = response_data.get("data", {})
                    card_count = data.get("cardCount", 0)
                    nick_name = data.get("nickName", "")
                    logger.info(
                        f"成功查询用户信息 | cardCount={card_count} | "
                        f"nickName={nick_name} | logId={log_id}"
                    )
            else:
                logger.error(
                    f"查询用户信息失败: status=false | "
                    f"errorcode={error_code} | message={error_message} | logId={log_id}"
                )
        except Exception as e:
            logger.error(f"解析响应失败: {e}")

        return response

    async def get_card_balance(self, card_no: str, db: Optional[Session] = None, debug: bool = False, amount: int = None):
        """查询卡余额

        Args:
            card_no: 卡号
            db: 数据库会话（用于获取配置）
            debug: 调试模式，为True时返回模拟数据
            amount: 原始金额（分），用于debug模式生成模拟数据

        Returns:
            Dict: 卡余额信息，包含balance、cardBalance、balanceCnt字段
        """
        # 记录查询开始（debug和生产模式都需要记录）
        if debug:
            logger.info(f"[TEST_MODE] 查询卡余额: 卡号={card_no}（将跳过HTTP请求）")
        else:
            logger.info(f"查询卡余额: 卡号={card_no}")

        # 构建请求体 - 使用新的API格式
        request_body = {
            "cardStatus": "A",
            "currentPage": 1,
            "pageSize": 10,
            "sign": self.sign
        }

        # 发送请求到新的API接口（debug模式下使用模拟响应）
        if debug:
            logger.info(f"[TEST_MODE] 跳过HTTP请求，返回模拟余额数据")
            # 如果没有提供amount，使用默认值
            if amount is None:
                amount = 10000  # 默认100元
            return self._create_mock_balance_data(card_no, amount)
        else:
            response = await self.make_request("/app/card/mem/pageList.json", request_body, db=db)

        # 检查响应
        try:
            response_data = response.json()
            if response_data.get("status", False):
                logger.info(f"API调用成功，开始查找卡号: {card_no}")

                # 解析新的分页列表格式：data.list[]
                data = response_data.get("data", {})
                card_list = data.get("list", [])

                if isinstance(card_list, list) and len(card_list) > 0:
                    # 根据卡号在列表中查找匹配的卡片
                    for card_info in card_list:
                        if card_info.get("cardNo") == card_no:
                            logger.info(f"找到匹配的卡号: {card_no}")
                            return {
                                "balance": card_info.get("balance"),
                                "cardBalance": card_info.get("cardBalance"),
                                "balanceCnt": card_info.get("balanceCnt"),
                            }

                    # 如果没有找到匹配的卡号
                    logger.warning(f"在返回的卡片列表中未找到卡号: {card_no}")
                    return {}
                else:
                    logger.warning(f"卡余额查询返回空列表: {card_no}")
                    return {}
            else:
                error_info = response_data.get("error", {})
                logger.error(f"查询卡余额失败: {error_info.get('message', '未知错误')}")
                return {}
        except Exception as e:
            logger.error(f"解析响应失败: {e}")
            return {}

    def get_card_balance_sync(self, card_no: str, debug: bool = None):
        """
        查询卡余额的同步版本（保持向后兼容）

        Args:
            card_no: 卡号
            debug: 调试模式，为True时返回模拟数据，为None时自动检测

        Returns:
            Dict: 卡余额信息，包含balance、cardBalance、balanceCnt字段
        """
        # 自动检测测试模式
        if debug is None:
            import os
            debug = os.getenv('TESTING', 'false').lower() == 'true'

        # 记录查询开始（debug和生产模式都需要记录）
        if debug:
            logger.info(f"[TEST_MODE] 查询卡余额: 卡号={card_no}（将跳过HTTP请求）")
        else:
            logger.info(f"查询卡余额: 卡号={card_no}")

        # 如果是测试模式，返回模拟数据
        if debug:
            logger.info(f"[TEST_MODE] 跳过HTTP请求，返回模拟余额数据")
            # 使用默认金额100元（10000分）
            return self._create_mock_balance_data(card_no, 10000)

        # 构建请求体 - 使用新的API格式
        request_body = {
            "cardStatus": "A",
            "currentPage": 1,
            "pageSize": 10,
            "sign": self.sign
        }

        # 发送请求到新的API接口
        response = self.make_request_sync("/app/card/mem/pageList.json", request_body)

        # 检查响应
        try:
            response_data = response.json()
            if response_data.get("status", False):
                logger.info(f"API调用成功，开始查找卡号: {card_no}")

                # 解析新的分页列表格式：data.list[]
                data = response_data.get("data", {})
                card_list = data.get("list", [])

                if isinstance(card_list, list) and len(card_list) > 0:
                    # 根据卡号在列表中查找匹配的卡片
                    for card_info in card_list:
                        if card_info.get("cardNo") == card_no:
                            logger.info(f"找到匹配的卡号: {card_no}")
                            return {
                                "balance": card_info.get("balance"),
                                "cardBalance": card_info.get("cardBalance"),
                                "balanceCnt": card_info.get("balanceCnt"),
                            }

                    # 如果没有找到匹配的卡号
                    logger.warning(f"在返回的卡片列表中未找到卡号: {card_no}")
                    return {}
                else:
                    logger.warning(f"卡余额查询返回空列表: {card_no}")
                    return {}
            else:
                error_info = response_data.get("error", {})
                logger.error(f"查询卡余额失败: {error_info.get('message', '未知错误')}")
                return {}
        except Exception as e:
            logger.error(f"解析响应失败: {e}")
            return {}

    def _create_mock_bind_response(self, card_no: str):
        """创建模拟绑卡响应"""
        import time
        from unittest.mock import Mock

        # 创建模拟响应对象
        mock_response = Mock()
        mock_response.status_code = 200

        # 模拟成功的绑卡响应数据
        mock_data = {
            "status": True,
            "logId": f"TEST_BIND_{int(time.time())}_{card_no[-4:]}",
            "data": {
                "cardNo": card_no,
                "bindStatus": "success",
                "message": "绑卡成功（测试模式）"
            },
            "timestamp": int(time.time())
        }

        mock_response.json.return_value = mock_data
        mock_response.text = str(mock_data)

        logger.info(f"[TEST_MODE] 返回模拟绑卡成功响应: {card_no}")
        return mock_response

    def _create_mock_user_response(self):
        """创建模拟用户信息响应"""
        import time
        from unittest.mock import Mock

        mock_response = Mock()
        mock_response.status_code = 200

        # 模拟成功的用户信息响应数据
        mock_data = {
            "status": True,
            "logId": f"TEST_USER_{int(time.time())}",
            "data": {
                "nickName": "测试用户",
                "userId": "test_user_123",
                "cardCount": 5,
                "totalBalance": "100.00"
            },
            "timestamp": int(time.time())
        }

        mock_response.json.return_value = mock_data
        mock_response.text = str(mock_data)

        logger.info("[TEST_MODE] 返回模拟用户信息响应")
        return mock_response

    def _create_mock_balance_data(self, card_no: str, amount: int):
        """创建模拟余额数据

        Args:
            card_no: 卡号
            amount: 原始金额（分）

        Returns:
            Dict: 模拟的余额数据
        """
        # 将分转换为元
        balance_yuan = amount / 100

        mock_data = {
            "balance": str(balance_yuan),  # 元
            "cardBalance": str(amount),    # 分
            "balanceCnt": str(balance_yuan)  # 元
        }

        logger.info(f"[TEST_MODE] 返回模拟余额数据: {card_no}, balance={mock_data['balance']}元, cardBalance={mock_data['cardBalance']}分")
        return mock_data


