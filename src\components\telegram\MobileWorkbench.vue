<template>
  <div class="mobile-workbench">
    <!-- 移动端头部 -->
    <div class="mobile-header">
      <div class="header-content">
        <h3>工作台</h3>
        <div class="header-actions">
          <el-badge :value="pendingCount" :hidden="pendingCount === 0">
            <el-button type="primary" size="small" circle @click="showPendingList">
              <el-icon><Bell /></el-icon>
            </el-button>
          </el-badge>
          <el-button size="small" circle @click="refreshData" :loading="refreshLoading">
            <el-icon><Refresh /></el-icon>
          </el-button>
        </div>
      </div>
    </div>

    <!-- 统计卡片 - 移动端布局 -->
    <div class="mobile-stats">
      <div class="stats-grid">
        <div class="stat-item pending" @click="filterByType('pending')">
          <div class="stat-icon">
            <el-icon><Clock /></el-icon>
          </div>
          <div class="stat-info">
            <div class="stat-value">{{ pendingCount }}</div>
            <div class="stat-label">待审批</div>
          </div>
        </div>
        
        <div class="stat-item urgent" @click="filterByType('urgent')">
          <div class="stat-icon">
            <el-icon><Warning /></el-icon>
          </div>
          <div class="stat-info">
            <div class="stat-value">{{ urgentCount }}</div>
            <div class="stat-label">紧急</div>
          </div>
        </div>
        
        <div class="stat-item completed" @click="filterByType('completed')">
          <div class="stat-icon">
            <el-icon><CircleCheck /></el-icon>
          </div>
          <div class="stat-info">
            <div class="stat-value">{{ todayProcessed }}</div>
            <div class="stat-label">今日完成</div>
          </div>
        </div>
        
        <div class="stat-item active" @click="showActiveUsers">
          <div class="stat-icon">
            <el-icon><User /></el-icon>
          </div>
          <div class="stat-info">
            <div class="stat-value">{{ activeUsers }}</div>
            <div class="stat-label">活跃用户</div>
          </div>
        </div>
      </div>
    </div>

    <!-- 快速操作按钮 -->
    <div class="quick-actions">
      <el-button 
        type="primary" 
        size="large" 
        :disabled="selectedItems.length === 0"
        @click="handleBatchApproval"
        :loading="batchLoading"
        class="action-btn"
      >
        <el-icon><Select /></el-icon>
        批量审批 ({{ selectedItems.length }})
      </el-button>
      
      <el-button 
        type="success" 
        size="large"
        @click="handleQuickApproval"
        class="action-btn"
      >
        <el-icon><CircleCheck /></el-icon>
        一键审批
      </el-button>
    </div>

    <!-- 待办列表 -->
    <div class="todo-section">
      <div class="section-header">
        <h4>待办事项</h4>
        <div class="filter-tabs">
          <span 
            v-for="filter in filterOptions" 
            :key="filter.value"
            class="filter-tab"
            :class="{ active: currentFilter === filter.value }"
            @click="setFilter(filter.value)"
          >
            {{ filter.label }}
          </span>
        </div>
      </div>

      <div class="todo-list" v-loading="todoLoading">
        <div 
          v-for="item in filteredTodoList" 
          :key="item.id"
          class="todo-card"
          :class="{ 'urgent': item.isUrgent, 'selected': selectedItems.includes(item.id) }"
        >
          <!-- 卡片头部 -->
          <div class="card-header" @click="toggleSelection(item.id)">
            <div class="card-checkbox">
              <el-checkbox 
                :model-value="selectedItems.includes(item.id)"
                @change="toggleSelection(item.id)"
              />
            </div>
            <div class="card-title">
              <span class="title-text">{{ item.title }}</span>
              <div class="title-badges">
                <el-tag v-if="item.isUrgent" type="danger" size="small">紧急</el-tag>
                <el-tag :type="item.statusType" size="small">{{ item.statusText }}</el-tag>
              </div>
            </div>
          </div>

          <!-- 卡片内容 -->
          <div class="card-content">
            <div class="content-row">
              <span class="label">用户：</span>
              <span class="value">{{ item.username }}</span>
            </div>
            <div class="content-row">
              <span class="label">令牌：</span>
              <span class="value token">{{ item.verificationToken }}</span>
            </div>
            <div class="content-row">
              <span class="label">时间：</span>
              <span class="value">{{ item.timeAgo }}</span>
            </div>
          </div>

          <!-- 卡片操作 -->
          <div class="card-actions">
            <el-button 
              type="success" 
              size="small"
              @click="handleApprove(item)"
              :loading="item.approving"
            >
              通过
            </el-button>
            <el-button 
              type="danger" 
              size="small"
              @click="handleReject(item)"
              :loading="item.rejecting"
            >
              拒绝
            </el-button>
            <el-button 
              type="info" 
              size="small"
              @click="handleViewDetails(item)"
            >
              详情
            </el-button>
          </div>
        </div>

        <!-- 空状态 -->
        <div v-if="filteredTodoList.length === 0" class="empty-state">
          <el-icon size="48" color="#C0C4CC"><DocumentRemove /></el-icon>
          <p>暂无待办事项</p>
        </div>
      </div>
    </div>

    <!-- 底部浮动操作栏 -->
    <div class="floating-actions" v-if="selectedItems.length > 0">
      <div class="floating-content">
        <span class="selected-count">已选择 {{ selectedItems.length }} 项</span>
        <div class="floating-buttons">
          <el-button type="success" size="small" @click="handleBatchApproval">
            批量通过
          </el-button>
          <el-button type="danger" size="small" @click="handleBatchReject">
            批量拒绝
          </el-button>
          <el-button size="small" @click="clearSelection">
            取消选择
          </el-button>
        </div>
      </div>
    </div>

    <!-- 用户详情抽屉 -->
    <el-drawer
      v-model="detailDrawerVisible"
      title="用户详情"
      direction="btt"
      size="70%"
    >
      <div v-if="selectedUserDetail" class="user-detail-mobile">
        <div class="detail-section">
          <h4>基本信息</h4>
          <div class="detail-list">
            <div class="detail-item">
              <span class="item-label">Telegram用户名</span>
              <span class="item-value">{{ selectedUserDetail.username }}</span>
            </div>
            <div class="detail-item">
              <span class="item-label">真实姓名</span>
              <span class="item-value">{{ selectedUserDetail.realName }}</span>
            </div>
            <div class="detail-item">
              <span class="item-label">验证令牌</span>
              <span class="item-value token">{{ selectedUserDetail.verificationToken }}</span>
            </div>
            <div class="detail-item">
              <span class="item-label">申请时间</span>
              <span class="item-value">{{ selectedUserDetail.createdAt }}</span>
            </div>
          </div>
        </div>

        <div class="detail-section">
          <h4>审批建议</h4>
          <el-alert 
            :title="selectedUserDetail.suggestion.title"
            :type="selectedUserDetail.suggestion.type"
            :description="selectedUserDetail.suggestion.description"
            show-icon
          />
        </div>

        <div class="detail-actions">
          <el-button 
            type="success" 
            size="large"
            @click="handleApproveFromDetail"
            :loading="detailApprovalLoading"
            block
          >
            通过审批
          </el-button>
          <el-button 
            type="danger" 
            size="large"
            @click="handleRejectFromDetail"
            :loading="detailRejectionLoading"
            block
          >
            拒绝申请
          </el-button>
        </div>
      </div>
    </el-drawer>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Bell,
  Refresh,
  Clock,
  Warning,
  CircleCheck,
  User,
  Select,
  DocumentRemove
} from '@element-plus/icons-vue'

// 响应式数据
const pendingCount = ref(8)
const urgentCount = ref(2)
const todayProcessed = ref(15)
const activeUsers = ref(45)

const refreshLoading = ref(false)
const todoLoading = ref(false)
const batchLoading = ref(false)
const detailDrawerVisible = ref(false)
const detailApprovalLoading = ref(false)
const detailRejectionLoading = ref(false)

const currentFilter = ref('all')
const selectedItems = ref([])
const todoList = ref([])
const selectedUserDetail = ref(null)

// 筛选选项
const filterOptions = [
  { label: '全部', value: 'all' },
  { label: '待审批', value: 'pending' },
  { label: '紧急', value: 'urgent' }
]

// 计算属性
const filteredTodoList = computed(() => {
  if (currentFilter.value === 'all') return todoList.value
  if (currentFilter.value === 'pending') return todoList.value.filter(item => item.status === 'pending')
  if (currentFilter.value === 'urgent') return todoList.value.filter(item => item.isUrgent)
  return todoList.value
})

// 方法
const loadData = async () => {
  try {
    todoLoading.value = true
    
    // 模拟数据加载
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    todoList.value = [
      {
        id: 1,
        title: '用户身份验证申请',
        username: '@zhangsan',
        verificationToken: 'VT_ABC123',
        timeAgo: '5分钟前',
        status: 'pending',
        statusText: '待审批',
        statusType: 'warning',
        isUrgent: true,
        approving: false,
        rejecting: false
      },
      {
        id: 2,
        title: '群组绑定申请',
        username: '@lisi',
        verificationToken: 'VT_DEF456',
        timeAgo: '15分钟前',
        status: 'pending',
        statusText: '待审批',
        statusType: 'warning',
        isUrgent: false,
        approving: false,
        rejecting: false
      }
    ]
    
  } catch (error) {
    ElMessage.error('加载数据失败：' + error.message)
  } finally {
    todoLoading.value = false
  }
}

const refreshData = async () => {
  refreshLoading.value = true
  await loadData()
  refreshLoading.value = false
  ElMessage.success('数据已刷新')
}

const filterByType = (type) => {
  setFilter(type)
}

const setFilter = (filter) => {
  currentFilter.value = filter
  selectedItems.value = []
}

const toggleSelection = (id) => {
  const index = selectedItems.value.indexOf(id)
  if (index > -1) {
    selectedItems.value.splice(index, 1)
  } else {
    selectedItems.value.push(id)
  }
}

const clearSelection = () => {
  selectedItems.value = []
}

const handleBatchApproval = async () => {
  if (selectedItems.value.length === 0) {
    ElMessage.warning('请先选择要审批的项目')
    return
  }
  
  try {
    await ElMessageBox.confirm(
      `确定要批量审批 ${selectedItems.value.length} 个用户吗？`,
      '批量审批确认',
      { type: 'warning' }
    )
    
    batchLoading.value = true
    await new Promise(resolve => setTimeout(resolve, 2000))
    
    ElMessage.success(`成功审批 ${selectedItems.value.length} 个用户`)
    selectedItems.value = []
    await loadData()
    
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('批量审批失败：' + error.message)
    }
  } finally {
    batchLoading.value = false
  }
}

const handleBatchReject = async () => {
  if (selectedItems.value.length === 0) {
    ElMessage.warning('请先选择要拒绝的项目')
    return
  }
  
  try {
    await ElMessageBox.confirm(
      `确定要批量拒绝 ${selectedItems.value.length} 个申请吗？`,
      '批量拒绝确认',
      { type: 'warning' }
    )
    
    ElMessage.success(`成功拒绝 ${selectedItems.value.length} 个申请`)
    selectedItems.value = []
    await loadData()
    
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('批量拒绝失败：' + error.message)
    }
  }
}

const handleQuickApproval = async () => {
  try {
    await ElMessageBox.confirm(
      '确定要一键审批所有待审批用户吗？',
      '一键审批确认',
      { type: 'warning' }
    )
    
    ElMessage.success('一键审批完成')
    await loadData()
    
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('一键审批失败：' + error.message)
    }
  }
}

const handleApprove = async (item) => {
  item.approving = true
  try {
    await new Promise(resolve => setTimeout(resolve, 1000))
    ElMessage.success(`用户 ${item.username} 审批通过`)
    await loadData()
  } catch (error) {
    ElMessage.error('审批失败：' + error.message)
  } finally {
    item.approving = false
  }
}

const handleReject = async (item) => {
  item.rejecting = true
  try {
    await new Promise(resolve => setTimeout(resolve, 1000))
    ElMessage.success(`用户 ${item.username} 申请已拒绝`)
    await loadData()
  } catch (error) {
    ElMessage.error('拒绝失败：' + error.message)
  } finally {
    item.rejecting = false
  }
}

const handleViewDetails = (item) => {
  selectedUserDetail.value = {
    username: item.username,
    realName: '张三',
    verificationToken: item.verificationToken,
    createdAt: '2025-01-10 14:30:25',
    suggestion: {
      title: '建议通过审批',
      type: 'success',
      description: '用户信息完整，符合审批条件，建议通过审批。'
    }
  }
  detailDrawerVisible.value = true
}

const handleApproveFromDetail = async () => {
  detailApprovalLoading.value = true
  try {
    await new Promise(resolve => setTimeout(resolve, 1000))
    ElMessage.success('审批通过')
    detailDrawerVisible.value = false
    await loadData()
  } catch (error) {
    ElMessage.error('审批失败：' + error.message)
  } finally {
    detailApprovalLoading.value = false
  }
}

const handleRejectFromDetail = async () => {
  detailRejectionLoading.value = true
  try {
    await new Promise(resolve => setTimeout(resolve, 1000))
    ElMessage.success('申请已拒绝')
    detailDrawerVisible.value = false
    await loadData()
  } catch (error) {
    ElMessage.error('拒绝失败：' + error.message)
  } finally {
    detailRejectionLoading.value = false
  }
}

const showPendingList = () => {
  setFilter('pending')
}

const showActiveUsers = () => {
  ElMessage.info('查看活跃用户功能开发中')
}

// 生命周期
onMounted(() => {
  loadData()
})
</script>

<style scoped>
.mobile-workbench {
  padding: 0;
  background-color: #f5f7fa;
  min-height: 100vh;
}

.mobile-header {
  background: linear-gradient(135deg, #409EFF 0%, #67C23A 100%);
  color: white;
  padding: 16px;
  position: sticky;
  top: 0;
  z-index: 100;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-content h3 {
  margin: 0;
  font-size: 18px;
  font-weight: bold;
}

.header-actions {
  display: flex;
  gap: 8px;
}

.mobile-stats {
  padding: 16px;
  background: white;
  margin-bottom: 8px;
}

.stats-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 12px;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s;
}

.stat-item:active {
  transform: scale(0.98);
}

.stat-item.pending { border-left: 4px solid #E6A23C; }
.stat-item.urgent { border-left: 4px solid #F56C6C; }
.stat-item.completed { border-left: 4px solid #67C23A; }
.stat-item.active { border-left: 4px solid #409EFF; }

.stat-icon {
  font-size: 24px;
}

.stat-value {
  font-size: 20px;
  font-weight: bold;
  color: #303133;
}

.stat-label {
  font-size: 12px;
  color: #909399;
}

.quick-actions {
  padding: 16px;
  background: white;
  margin-bottom: 8px;
  display: flex;
  gap: 12px;
}

.action-btn {
  flex: 1;
}

.todo-section {
  background: white;
  margin-bottom: 80px;
}

.section-header {
  padding: 16px 16px 0 16px;
  border-bottom: 1px solid #EBEEF5;
}

.section-header h4 {
  margin: 0 0 12px 0;
  color: #303133;
}

.filter-tabs {
  display: flex;
  gap: 0;
  margin-bottom: 16px;
}

.filter-tab {
  flex: 1;
  text-align: center;
  padding: 8px 16px;
  background: #f5f7fa;
  color: #909399;
  cursor: pointer;
  transition: all 0.3s;
  border-bottom: 2px solid transparent;
}

.filter-tab:first-child {
  border-radius: 4px 0 0 4px;
}

.filter-tab:last-child {
  border-radius: 0 4px 4px 0;
}

.filter-tab.active {
  background: #409EFF;
  color: white;
  border-bottom-color: #409EFF;
}

.todo-list {
  padding: 16px;
}

.todo-card {
  background: white;
  border: 1px solid #EBEEF5;
  border-radius: 8px;
  margin-bottom: 12px;
  overflow: hidden;
  transition: all 0.3s;
}

.todo-card.urgent {
  border-left: 4px solid #F56C6C;
}

.todo-card.selected {
  border-color: #409EFF;
  background-color: #F0F9FF;
}

.card-header {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px;
  border-bottom: 1px solid #EBEEF5;
}

.card-title {
  flex: 1;
}

.title-text {
  font-weight: bold;
  color: #303133;
  display: block;
  margin-bottom: 8px;
}

.title-badges {
  display: flex;
  gap: 8px;
}

.card-content {
  padding: 16px;
}

.content-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.content-row:last-child {
  margin-bottom: 0;
}

.label {
  color: #909399;
  font-size: 14px;
}

.value {
  color: #303133;
  font-weight: 500;
}

.value.token {
  font-family: monospace;
  font-size: 12px;
  background: #f5f7fa;
  padding: 2px 6px;
  border-radius: 4px;
}

.card-actions {
  display: flex;
  gap: 8px;
  padding: 16px;
  border-top: 1px solid #EBEEF5;
  background: #fafbfc;
}

.card-actions .el-button {
  flex: 1;
}

.empty-state {
  text-align: center;
  padding: 40px 20px;
  color: #909399;
}

.floating-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: white;
  border-top: 1px solid #EBEEF5;
  padding: 16px;
  z-index: 1000;
}

.floating-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.selected-count {
  font-size: 14px;
  color: #409EFF;
  font-weight: bold;
}

.floating-buttons {
  display: flex;
  gap: 8px;
}

.user-detail-mobile {
  padding: 16px;
}

.detail-section {
  margin-bottom: 24px;
}

.detail-section h4 {
  margin: 0 0 16px 0;
  color: #303133;
  font-size: 16px;
  border-bottom: 1px solid #EBEEF5;
  padding-bottom: 8px;
}

.detail-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.detail-item {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.item-label {
  font-size: 14px;
  color: #909399;
}

.item-value {
  font-weight: bold;
  color: #303133;
}

.item-value.token {
  font-family: monospace;
  background: #f5f7fa;
  padding: 8px;
  border-radius: 4px;
  font-size: 12px;
}

.detail-actions {
  display: flex;
  flex-direction: column;
  gap: 12px;
  margin-top: 24px;
}

/* 响应式设计 */
@media (max-width: 480px) {
  .stats-grid {
    grid-template-columns: 1fr 1fr;
    gap: 8px;
  }
  
  .stat-item {
    padding: 12px;
    gap: 8px;
  }
  
  .stat-value {
    font-size: 18px;
  }
  
  .quick-actions {
    flex-direction: column;
  }
  
  .floating-content {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }
  
  .floating-buttons {
    justify-content: center;
  }
}
</style>
