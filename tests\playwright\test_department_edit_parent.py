"""
部门编辑parent_id功能的Playwright自动化测试
"""
import asyncio
import time
from playwright.async_api import async_playwright


class DepartmentEditParentTest:
    """部门编辑parent_id功能测试类"""
    
    def __init__(self):
        self.base_url = "http://localhost:2000"
        self.login_url = f"{self.base_url}/#/login"
        self.department_url = f"{self.base_url}/#/department"
        
        # 测试账号
        self.admin_username = "admin"
        self.admin_password = "7c222fb2927d828af22f592134e8932480637c0d"
        
        self.results = []

    async def run_test(self):
        """运行完整测试"""
        print("🚀 开始部门编辑parent_id功能的Playwright测试")
        print("="*60)
        
        async with async_playwright() as p:
            # 启动浏览器
            browser = await p.chromium.launch(headless=False, slow_mo=1000)
            context = await browser.new_context()
            page = await context.new_page()
            
            try:
                # 执行测试步骤
                await self.test_login(page)
                await self.test_navigate_to_department(page)
                await self.test_edit_department_parent(page)
                
            except Exception as e:
                print(f"❌ 测试过程中发生错误: {e}")
                self.results.append({
                    "test": "整体测试",
                    "success": False,
                    "error": str(e)
                })
            finally:
                await browser.close()
        
        # 打印测试结果
        self.print_results()

    async def test_login(self, page):
        """测试登录功能"""
        print("🔐 测试登录功能...")
        
        try:
            # 导航到登录页面
            await page.goto(self.login_url)
            await page.wait_for_load_state('networkidle')
            
            # 等待登录表单加载
            await page.wait_for_selector('input[placeholder*="用户名"], input[placeholder*="账号"]', timeout=10000)
            
            # 输入用户名和密码
            username_input = page.locator('input[placeholder*="用户名"], input[placeholder*="账号"]').first
            password_input = page.locator('input[placeholder*="密码"]').first
            
            await username_input.fill(self.admin_username)
            await password_input.fill(self.admin_password)
            
            # 点击登录按钮
            login_button = page.locator('button:has-text("登录"), button:has-text("登 录")').first
            await login_button.click()
            
            # 等待登录成功，检查是否跳转到主页面
            await page.wait_for_url('**/dashboard', timeout=15000)
            
            print("✅ 登录成功")
            self.results.append({
                "test": "登录功能",
                "success": True,
                "message": "超级管理员登录成功"
            })
            
        except Exception as e:
            print(f"❌ 登录失败: {e}")
            self.results.append({
                "test": "登录功能",
                "success": False,
                "error": str(e)
            })
            raise

    async def test_navigate_to_department(self, page):
        """测试导航到部门管理页面"""
        print("🧭 导航到部门管理页面...")
        
        try:
            # 等待页面加载完成
            await page.wait_for_load_state('networkidle')
            
            # 查找部门管理菜单项
            department_menu = page.locator('text="部门管理"').first
            await department_menu.click()
            
            # 等待部门管理页面加载
            await page.wait_for_load_state('networkidle')
            await page.wait_for_selector('.department-list, .el-table', timeout=10000)
            
            print("✅ 成功导航到部门管理页面")
            self.results.append({
                "test": "导航到部门管理",
                "success": True,
                "message": "成功进入部门管理页面"
            })
            
        except Exception as e:
            print(f"❌ 导航到部门管理页面失败: {e}")
            self.results.append({
                "test": "导航到部门管理",
                "success": False,
                "error": str(e)
            })
            raise

    async def test_edit_department_parent(self, page):
        """测试编辑部门的parent_id"""
        print("✏️ 测试编辑部门的上级部门...")
        
        try:
            # 等待部门列表加载
            await page.wait_for_selector('.el-table tbody tr', timeout=10000)
            
            # 获取部门列表
            department_rows = page.locator('.el-table tbody tr')
            row_count = await department_rows.count()
            
            if row_count < 2:
                print("⚠️ 部门数量不足，无法进行测试")
                self.results.append({
                    "test": "编辑部门parent_id",
                    "success": False,
                    "error": "部门数量不足"
                })
                return
            
            # 选择第一个部门进行编辑
            first_row = department_rows.nth(0)
            
            # 查找编辑按钮（可能是图标或文字）
            edit_button = first_row.locator('button:has-text("编辑"), .el-button--primary, [title="编辑"]').first
            await edit_button.click()
            
            # 等待编辑对话框打开
            await page.wait_for_selector('.el-dialog', timeout=5000)
            
            # 查找上级部门选择器
            parent_selector = page.locator('.el-select:has(.el-input__inner)').first
            await parent_selector.click()
            
            # 等待下拉选项加载
            await page.wait_for_selector('.el-select-dropdown .el-option', timeout=5000)
            
            # 选择一个不同的上级部门
            options = page.locator('.el-select-dropdown .el-option')
            option_count = await options.count()
            
            if option_count > 0:
                # 选择第一个选项
                await options.nth(0).click()
                
                # 点击保存按钮
                save_button = page.locator('.el-dialog button:has-text("保存"), .el-dialog button:has-text("确定")').first
                await save_button.click()
                
                # 等待保存完成
                await page.wait_for_load_state('networkidle')
                
                # 检查是否有成功提示
                success_message = page.locator('.el-message--success, .el-notification--success')
                if await success_message.count() > 0:
                    print("✅ 部门上级部门更新成功")
                    self.results.append({
                        "test": "编辑部门parent_id",
                        "success": True,
                        "message": "成功更新部门的上级部门"
                    })
                else:
                    print("⚠️ 未检测到成功提示，但操作可能已完成")
                    self.results.append({
                        "test": "编辑部门parent_id",
                        "success": True,
                        "message": "部门编辑操作已执行，未检测到明确的成功提示"
                    })
            else:
                print("⚠️ 没有可选择的上级部门选项")
                self.results.append({
                    "test": "编辑部门parent_id",
                    "success": False,
                    "error": "没有可选择的上级部门选项"
                })
                
        except Exception as e:
            print(f"❌ 编辑部门parent_id失败: {e}")
            self.results.append({
                "test": "编辑部门parent_id",
                "success": False,
                "error": str(e)
            })

    def print_results(self):
        """打印测试结果"""
        print("\n" + "="*60)
        print("📊 测试结果摘要")
        print("="*60)
        
        total_tests = len(self.results)
        passed_tests = sum(1 for r in self.results if r["success"])
        failed_tests = total_tests - passed_tests
        
        print(f"总测试数: {total_tests}")
        print(f"通过数: {passed_tests}")
        print(f"失败数: {failed_tests}")
        print(f"成功率: {(passed_tests/total_tests*100):.1f}%" if total_tests > 0 else "0%")
        
        print("\n详细结果:")
        for result in self.results:
            status = "✅" if result["success"] else "❌"
            print(f"{status} {result['test']}: {result.get('message', result.get('error', ''))}")
        
        print("="*60)


async def main():
    """主函数"""
    test = DepartmentEditParentTest()
    await test.run_test()


if __name__ == "__main__":
    asyncio.run(main())
