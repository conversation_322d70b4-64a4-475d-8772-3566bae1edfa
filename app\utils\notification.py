import json
import logging
from typing import Any, Dict, Optional
import httpx
import smtplib
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart

from app.core.config import settings
from app.crud.notification_config import notification_config_crud
from app.db.session import SessionLocal

logger = logging.getLogger(__name__)


async def send_alert(title: str, content: Dict[str, Any]) -> None:
    """发送告警通知到所有已配置的通知渠道"""
    try:
        # 获取通知配置
        with SessionLocal() as db:
            config = notification_config_crud.get_config(db)
            if not config:
                logger.warning("未找到通知配置，将使用默认配置")
                config = notification_config_crud.create_default_config(db)

        tasks = []

        # 企业微信通知
        if config.wechat_enabled and config.wechat_webhook_url:
            tasks.append(
                send_wechat_notification(config.wechat_webhook_url, title, content)
            )

        # 钉钉通知
        if config.dingtalk_enabled and config.dingtalk_webhook_url:
            tasks.append(
                send_dingtalk_notification(config.dingtalk_webhook_url, title, content)
            )

        # Telegram通知
        if (
            config.telegram_enabled
            and config.telegram_bot_token
            and config.telegram_chat_id
        ):
            tasks.append(
                send_telegram_notification(
                    config.telegram_bot_token,
                    config.telegram_chat_id,
                    title,
                    content,
                )
            )

        # 邮件通知
        if config.email_enabled and all(
            [
                config.smtp_host,
                config.smtp_port,
                config.smtp_user,
                config.smtp_password,
                config.alert_email_from,
                config.alert_email_to,
            ]
        ):
            tasks.append(
                send_email_notification(
                    host=config.smtp_host,
                    port=config.smtp_port,
                    username=config.smtp_user,
                    password=config.smtp_password,
                    from_email=config.alert_email_from,
                    to_email=config.alert_email_to,
                    title=title,
                    content=content,
                )
            )

        # 执行所有通知任务
        async with httpx.AsyncClient() as client:
            for task in tasks:
                try:
                    await task(client)
                except Exception as e:
                    logger.error(f"发送通知失败: {str(e)}")

    except Exception as e:
        logger.error(f"发送告警通知失败: {str(e)}")


async def send_wechat_notification(
    webhook_url: str,
    title: str,
    content: Dict[str, Any],
) -> None:
    """发送企业微信通知"""

    async def _send(client: httpx.AsyncClient) -> None:
        message = {
            "msgtype": "markdown",
            "markdown": {
                "content": f"## {title}\n"
                + "\n".join([f"- {k}: {v}" for k, v in content.items()])
            },
        }
        response = await client.post(webhook_url, json=message)
        response.raise_for_status()

    return _send


async def send_dingtalk_notification(
    webhook_url: str,
    title: str,
    content: Dict[str, Any],
) -> None:
    """发送钉钉通知"""

    async def _send(client: httpx.AsyncClient) -> None:
        message = {
            "msgtype": "markdown",
            "markdown": {
                "title": title,
                "text": f"## {title}\n"
                + "\n".join([f"- {k}: {v}" for k, v in content.items()]),
            },
        }
        response = await client.post(webhook_url, json=message)
        response.raise_for_status()

    return _send


async def send_telegram_notification(
    bot_token: str,
    chat_id: str,
    title: str,
    content: Dict[str, Any],
) -> None:
    """发送Telegram通知"""

    async def _send(client: httpx.AsyncClient) -> None:
        message = f"*{title}*\n" + "\n".join(
            [f"• {k}: {v}" for k, v in content.items()]
        )
        api_url = f"https://api.telegram.org/bot{bot_token}/sendMessage"
        params = {
            "chat_id": chat_id,
            "text": message,
            "parse_mode": "Markdown",
        }
        response = await client.post(api_url, json=params)
        response.raise_for_status()

    return _send


async def send_email_notification(
    host: str,
    port: int,
    username: str,
    password: str,
    from_email: str,
    to_email: str,
    title: str,
    content: Dict[str, Any],
) -> None:
    """发送邮件通知"""

    async def _send(_: httpx.AsyncClient) -> None:
        msg = MIMEMultipart()
        msg["From"] = from_email
        msg["To"] = to_email
        msg["Subject"] = title

        body = "\n".join([f"{k}: {v}" for k, v in content.items()])
        msg.attach(MIMEText(body, "plain"))

        with smtplib.SMTP(host, port) as server:
            server.starttls()
            server.login(username, password)
            server.send_message(msg)

    return _send
