#!/usr/bin/env python3
"""
生产级CK负载均衡服务
专为高并发场景设计，确保CK的正确性和负载均衡
"""

import asyncio
import time
import hashlib
import random
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass
from collections import defaultdict

from app.core.redis import get_redis
from app.core.logging import get_logger
from app.models.walmart_ck import WalmartCK

logger = get_logger("production_ck_service")


@dataclass
class CKInfo:
    """CK信息"""
    ck_id: int
    merchant_id: int
    department_id: Optional[int]
    bind_count: int
    total_limit: int
    last_used: float
    failure_count: int
    is_healthy: bool


class ProductionCKService:
    """生产级CK负载均衡服务"""
    
    def __init__(self, db_session):
        self.db = db_session
        self.redis = None
        self.ck_cache = {}  # 本地CK缓存
        self.failure_tracker = defaultdict(int)  # 失败计数器
        self.last_sync = 0  # 上次同步时间
        self.sync_interval = 30  # 同步间隔（秒）
        
    async def __aenter__(self):
        """异步上下文管理器入口"""
        self.redis = await get_redis()
        await self._sync_ck_pool()
        return self
        
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器出口"""
        if self.redis:
            await self.redis.aclose()
    
    async def get_available_ck(self, merchant_id: int, request_id: str = None) -> Optional[WalmartCK]:
        """
        获取可用CK - 生产级实现
        
        Args:
            merchant_id: 商户ID
            request_id: 请求ID，用于负载均衡
            
        Returns:
            可用的CK对象或None
        """
        try:
            # 1. 检查是否需要同步CK池
            await self._check_and_sync()
            
            # 2. 获取健康的CK列表
            healthy_cks = await self._get_healthy_cks(merchant_id)
            
            if not healthy_cks:
                logger.warning(f"商户 {merchant_id} 没有健康的CK")
                return None
            
            # 3. 使用生产级负载均衡算法选择CK
            selected_ck_id = await self._select_ck_with_load_balance(
                healthy_cks, merchant_id, request_id
            )
            
            if not selected_ck_id:
                logger.warning(f"商户 {merchant_id} 负载均衡选择失败")
                return None
            
            # 4. 获取CK对象并验证
            ck = await self._get_and_validate_ck(selected_ck_id, merchant_id)
            
            if ck:
                # 5. 记录使用情况
                await self._record_ck_selection(ck.id, merchant_id)
                logger.info(f"生产级服务选择CK {ck.id} for merchant {merchant_id}")
            
            return ck
            
        except Exception as e:
            logger.error(f"获取可用CK失败: {e}")
            return None
    
    async def _get_healthy_cks(self, merchant_id: int) -> List[CKInfo]:
        """获取健康的CK列表"""
        try:
            # 从数据库获取活跃CK
            active_cks = self.db.query(WalmartCK).filter(
                WalmartCK.merchant_id == merchant_id,
                WalmartCK.active == True,
                WalmartCK.is_deleted == False
            ).all()
            
            healthy_cks = []
            current_time = time.time()
            
            for ck in active_cks:
                # 检查CK健康状态
                failure_count = self.failure_tracker.get(ck.id, 0)
                
                # 健康判断条件
                is_healthy = (
                    failure_count < 3 and  # 失败次数少于3次
                    ck.bind_count < ck.total_limit * 0.9 and  # 使用率低于90%
                    current_time - self.ck_cache.get(ck.id, {}).get('last_used', 0) > 1  # 至少1秒未使用
                )
                
                if is_healthy:
                    ck_info = CKInfo(
                        ck_id=ck.id,
                        merchant_id=ck.merchant_id,
                        department_id=ck.department_id,
                        bind_count=ck.bind_count,
                        total_limit=ck.total_limit,
                        last_used=self.ck_cache.get(ck.id, {}).get('last_used', 0),
                        failure_count=failure_count,
                        is_healthy=True
                    )
                    healthy_cks.append(ck_info)
            
            return healthy_cks
            
        except Exception as e:
            logger.error(f"获取健康CK列表失败: {e}")
            return []
    
    async def _select_ck_with_load_balance(
        self, 
        healthy_cks: List[CKInfo], 
        merchant_id: int, 
        request_id: str = None
    ) -> Optional[int]:
        """
        生产级负载均衡算法
        结合轮询、权重和随机化
        """
        if not healthy_cks:
            return None
        
        if len(healthy_cks) == 1:
            return healthy_cks[0].ck_id
        
        try:
            # 1. 计算权重（基于剩余容量和最后使用时间）
            current_time = time.time()
            weighted_cks = []
            
            for ck_info in healthy_cks:
                # 剩余容量权重
                remaining_capacity = ck_info.total_limit - ck_info.bind_count
                capacity_weight = remaining_capacity / ck_info.total_limit
                
                # 时间权重（越久未使用权重越高）
                time_since_last_use = current_time - ck_info.last_used
                time_weight = min(time_since_last_use / 60, 1.0)  # 最大1分钟
                
                # 失败权重（失败越少权重越高）
                failure_weight = max(0.1, 1.0 - ck_info.failure_count * 0.2)
                
                # 综合权重
                total_weight = capacity_weight * 0.5 + time_weight * 0.3 + failure_weight * 0.2
                
                weighted_cks.append((ck_info.ck_id, total_weight))
            
            # 2. 基于请求ID的一致性哈希（如果提供）
            if request_id:
                # 使用请求ID生成稳定的随机数
                hash_value = int(hashlib.md5(request_id.encode()).hexdigest()[:8], 16)
                random.seed(hash_value)
            else:
                # 使用时间和商户ID生成随机种子
                seed = int(time.time() * 1000000) % 1000000 + merchant_id
                random.seed(seed)
            
            # 3. 加权随机选择
            total_weight = sum(weight for _, weight in weighted_cks)
            if total_weight <= 0:
                # 如果所有权重都为0，使用简单轮询
                return healthy_cks[random.randint(0, len(healthy_cks) - 1)].ck_id
            
            # 轮盘赌选择
            random_value = random.random() * total_weight
            cumulative_weight = 0
            
            for ck_id, weight in weighted_cks:
                cumulative_weight += weight
                if random_value <= cumulative_weight:
                    return ck_id
            
            # 兜底：返回第一个
            return weighted_cks[0][0]
            
        except Exception as e:
            logger.error(f"负载均衡选择失败: {e}")
            # 兜底：简单随机选择
            return random.choice(healthy_cks).ck_id
    
    async def _get_and_validate_ck(self, ck_id: int, merchant_id: int) -> Optional[WalmartCK]:
        """获取并验证CK"""
        try:
            # 从数据库获取CK
            ck = self.db.query(WalmartCK).filter(
                WalmartCK.id == ck_id,
                WalmartCK.merchant_id == merchant_id,
                WalmartCK.active == True,
                WalmartCK.is_deleted == False
            ).first()
            
            if not ck:
                logger.warning(f"CK {ck_id} 不存在或已禁用")
                return None
            
            # 简单验证（避免频繁API调用）
            if ck.total_limit is not None and ck.bind_count >= ck.total_limit:
                logger.warning(f"CK {ck_id} 已达到使用限制")
                return None
            
            return ck
            
        except Exception as e:
            logger.error(f"获取和验证CK {ck_id} 失败: {e}")
            return None
    
    async def _record_ck_selection(self, ck_id: int, merchant_id: int):
        """记录CK选择"""
        try:
            current_time = time.time()
            
            # 更新本地缓存
            if ck_id not in self.ck_cache:
                self.ck_cache[ck_id] = {}
            
            self.ck_cache[ck_id]['last_used'] = current_time
            self.ck_cache[ck_id]['use_count'] = self.ck_cache[ck_id].get('use_count', 0) + 1
            
            # 更新Redis统计
            stats_key = f"walmart:ck:stats:{merchant_id}:{ck_id}"
            await self.redis.hincrby(stats_key, 'use_count', 1)
            await self.redis.hset(stats_key, 'last_used', current_time)
            await self.redis.expire(stats_key, 3600)  # 1小时过期
            
        except Exception as e:
            logger.error(f"记录CK选择失败: {e}")
    
    async def record_ck_usage(self, ck_id: int, success: bool = True):
        """记录CK使用结果"""
        try:
            if success:
                # 成功使用，重置失败计数
                if ck_id in self.failure_tracker:
                    self.failure_tracker[ck_id] = 0
                
                # 更新数据库bind_count
                ck = self.db.query(WalmartCK).filter(WalmartCK.id == ck_id).first()
                if ck and success:
                    ck.bind_count += 1
                    ck.last_bind_time = time.time()
                    self.db.commit()
                    
                logger.debug(f"CK {ck_id} 使用成功")
            else:
                # 失败使用，增加失败计数
                self.failure_tracker[ck_id] += 1
                
                # 如果失败次数过多，临时标记为不健康
                if self.failure_tracker[ck_id] >= 5:
                    logger.warning(f"CK {ck_id} 失败次数过多，临时标记为不健康")
                
                logger.debug(f"CK {ck_id} 使用失败，失败计数: {self.failure_tracker[ck_id]}")
            
        except Exception as e:
            logger.error(f"记录CK使用结果失败: {e}")
    
    async def _check_and_sync(self):
        """检查并同步CK池"""
        current_time = time.time()
        if current_time - self.last_sync > self.sync_interval:
            await self._sync_ck_pool()
            self.last_sync = current_time
    
    async def _sync_ck_pool(self):
        """同步CK池"""
        try:
            # 清理过期的失败计数
            current_time = time.time()
            expired_cks = []
            
            for ck_id, last_used in self.ck_cache.items():
                if current_time - last_used.get('last_used', 0) > 300:  # 5分钟未使用
                    expired_cks.append(ck_id)
            
            for ck_id in expired_cks:
                if ck_id in self.failure_tracker:
                    self.failure_tracker[ck_id] = max(0, self.failure_tracker[ck_id] - 1)
            
            logger.debug("CK池同步完成")
            
        except Exception as e:
            logger.error(f"同步CK池失败: {e}")
    
    async def get_ck_statistics(self, merchant_id: int) -> Dict:
        """获取CK使用统计"""
        try:
            stats = {
                'merchant_id': merchant_id,
                'total_cks': 0,
                'healthy_cks': 0,
                'ck_details': []
            }
            
            # 获取所有CK
            all_cks = self.db.query(WalmartCK).filter(
                WalmartCK.merchant_id == merchant_id,
                WalmartCK.is_deleted == False
            ).all()
            
            stats['total_cks'] = len(all_cks)
            
            for ck in all_cks:
                failure_count = self.failure_tracker.get(ck.id, 0)
                is_healthy = (
                    ck.active and
                    failure_count < 3 and
                    ck.bind_count < ck.total_limit * 0.9
                )
                
                if is_healthy:
                    stats['healthy_cks'] += 1
                
                ck_detail = {
                    'ck_id': ck.id,
                    'active': ck.active,
                    'bind_count': ck.bind_count,
                    'total_limit': ck.total_limit,
                    'usage_ratio': ck.bind_count / ck.total_limit if ck.total_limit > 0 else 0,
                    'failure_count': failure_count,
                    'is_healthy': is_healthy,
                    'last_used': self.ck_cache.get(ck.id, {}).get('last_used', 0)
                }
                
                stats['ck_details'].append(ck_detail)
            
            return stats
            
        except Exception as e:
            logger.error(f"获取CK统计失败: {e}")
            return {'error': str(e)}


# 工厂函数
async def create_production_ck_service(db_session):
    """创建生产级CK服务"""
    service = ProductionCKService(db_session)
    await service.__aenter__()
    return service
