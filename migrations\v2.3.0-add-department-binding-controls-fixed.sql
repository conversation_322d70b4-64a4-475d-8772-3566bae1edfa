-- ========================================
-- 沃尔玛绑卡系统 - 部门进单开关和权重系统 (修复版)
-- 版本: v2.3.0-fixed
-- 功能: 为departments表添加进单开关和权重控制字段
-- 修复: 解决字符集排序规则冲突问题

-- ========================================

-- 设置字符集和时区，统一使用utf8mb4_unicode_ci排序规则
SET NAMES utf8mb4 COLLATE utf8mb4_unicode_ci;
SET time_zone = '+00:00';
SET collation_connection = 'utf8mb4_unicode_ci';
SET collation_database = 'utf8mb4_unicode_ci';

-- 使用数据库
USE `walmart_card_db`;

-- ========================================
-- 1. 安全检查和备份准备
-- ========================================

-- 创建迁移日志表（如果不存在）
-- 确保使用统一的字符集排序规则
CREATE TABLE IF NOT EXISTS `migration_logs` (
    `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '日志ID',
    `migration_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '迁移名称',
    `status` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'started' COMMENT '状态',
    `message` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '消息',
    `data_summary` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '数据摘要',
    `created_at` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '创建时间',
    `completed_at` datetime(3) NULL COMMENT '完成时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_migration_logs_name` (`migration_name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='迁移日志表';

-- 检查是否已经执行过此迁移
SET @migration_name = 'department_binding_controls_v2.3.0';
SET @migration_exists = 0;

-- 使用BINARY比较避免排序规则冲突
SELECT COUNT(*) INTO @migration_exists 
FROM migration_logs 
WHERE BINARY migration_name = BINARY @migration_name 
  AND BINARY status = BINARY 'completed';

-- 记录迁移开始（如果尚未执行）
INSERT IGNORE INTO migration_logs (migration_name, status, message, created_at)
VALUES (@migration_name, 'started', '开始执行部门进单控制功能迁移', NOW(3));

SELECT CASE
    WHEN @migration_exists > 0 THEN CONCAT('迁移 ', @migration_name, ' 已经执行过，跳过执行')
    ELSE '开始执行部门进单控制功能迁移...'
END AS status;

-- ========================================
-- 2. 数据备份（可选，生产环境建议）
-- ========================================

-- 创建departments表的备份（仅在生产环境或需要时执行）
-- CREATE TABLE `departments_backup_v2_3_0` AS SELECT * FROM `departments`;
-- SELECT '部门表备份完成' AS status;

-- ========================================
-- 3. 检查表结构和字段是否存在
-- ========================================

-- 检查departments表是否存在
SET @table_exists = 0;
SELECT COUNT(*) INTO @table_exists
FROM information_schema.tables
WHERE table_schema = DATABASE() AND table_name = 'departments';

-- 如果表不存在则报错（通过除零错误）
SELECT CASE WHEN @table_exists = 0 THEN 1/0 ELSE 1 END INTO @dummy;

-- 检查enable_binding字段是否已存在
SET @enable_binding_exists = 0;
SELECT COUNT(*) INTO @enable_binding_exists 
FROM information_schema.columns 
WHERE table_schema = DATABASE() 
  AND table_name = 'departments' 
  AND column_name = 'enable_binding';

-- 检查binding_weight字段是否已存在
SET @binding_weight_exists = 0;
SELECT COUNT(*) INTO @binding_weight_exists 
FROM information_schema.columns 
WHERE table_schema = DATABASE() 
  AND table_name = 'departments' 
  AND column_name = 'binding_weight';

SELECT CONCAT('enable_binding字段存在状态: ', @enable_binding_exists) AS check_result;
SELECT CONCAT('binding_weight字段存在状态: ', @binding_weight_exists) AS check_result;

-- ========================================
-- 4. 添加新字段
-- ========================================

-- 添加进单开关字段（如果不存在）
SET @sql = CASE
    WHEN @enable_binding_exists = 0 THEN
        'ALTER TABLE `departments` ADD COLUMN `enable_binding` tinyint(1) NOT NULL DEFAULT 1 COMMENT ''进单开关：1启用绑卡，0禁用绑卡，默认启用'' AFTER `status`'
    ELSE
        'SELECT ''enable_binding字段已存在，跳过添加'' AS status'
END;

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SELECT CASE
    WHEN @enable_binding_exists = 0 THEN '成功添加enable_binding字段'
    ELSE 'enable_binding字段已存在，跳过添加'
END AS status;

-- 添加进单权重字段（如果不存在）
SET @sql = CASE
    WHEN @binding_weight_exists = 0 THEN
        'ALTER TABLE `departments` ADD COLUMN `binding_weight` int(11) NOT NULL DEFAULT 100 COMMENT ''进单权重：数值越大优先级越高，用于CK分配算法，默认100'' AFTER `enable_binding`'
    ELSE
        'SELECT ''binding_weight字段已存在，跳过添加'' AS status'
END;

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SELECT CASE
    WHEN @binding_weight_exists = 0 THEN '成功添加binding_weight字段'
    ELSE 'binding_weight字段已存在，跳过添加'
END AS status;

-- ========================================
-- 5. 创建索引优化查询性能
-- ========================================

-- 为新字段创建索引以优化查询性能
-- 复合索引：merchant_id + enable_binding + binding_weight
SET @index_exists = 0;
SELECT COUNT(*) INTO @index_exists 
FROM information_schema.statistics 
WHERE table_schema = DATABASE() 
  AND table_name = 'departments' 
  AND index_name = 'idx_dept_binding_controls';

-- 创建索引（如果不存在）
SET @sql = CASE
    WHEN @index_exists = 0 THEN
        'CREATE INDEX `idx_dept_binding_controls` ON `departments` (`merchant_id`, `enable_binding`, `binding_weight`)'
    ELSE
        'SELECT ''部门绑卡控制索引已存在，跳过创建'' AS status'
END;

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SELECT CASE
    WHEN @index_exists = 0 THEN '成功创建部门绑卡控制索引'
    ELSE '部门绑卡控制索引已存在，跳过创建'
END AS status;

-- ========================================
-- 6. 数据初始化和验证
-- ========================================

-- 统计当前部门数量
SET @total_departments = 0;
SELECT COUNT(*) INTO @total_departments FROM `departments`;

-- 确保所有现有部门都有默认值（防止NULL值）
UPDATE `departments` 
SET `enable_binding` = 1 
WHERE `enable_binding` IS NULL;

UPDATE `departments` 
SET `binding_weight` = 100 
WHERE `binding_weight` IS NULL OR `binding_weight` <= 0;

-- 验证数据完整性
SET @enabled_departments = 0;
SET @total_weight = 0;
SELECT COUNT(*) INTO @enabled_departments FROM `departments` WHERE `enable_binding` = 1;
SELECT SUM(`binding_weight`) INTO @total_weight FROM `departments` WHERE `enable_binding` = 1;

SELECT CONCAT('总部门数: ', @total_departments) AS summary;
SELECT CONCAT('启用绑卡的部门数: ', @enabled_departments) AS summary;
SELECT CONCAT('启用部门总权重: ', IFNULL(@total_weight, 0)) AS summary;

-- ========================================
-- 7. 完成迁移并记录日志
-- ========================================

-- 更新迁移日志为完成状态（使用BINARY比较避免排序规则冲突）
UPDATE migration_logs 
SET status = 'completed', 
    completed_at = NOW(3),
    message = '部门进单控制功能迁移完成',
    data_summary = JSON_OBJECT(
        'total_departments', @total_departments,
        'enabled_departments', @enabled_departments,
        'total_weight', IFNULL(@total_weight, 0),
        'enable_binding_added', IF(@enable_binding_exists = 0, 'yes', 'existed'),
        'binding_weight_added', IF(@binding_weight_exists = 0, 'yes', 'existed'),
        'index_created', IF(@index_exists = 0, 'yes', 'existed')
    )
WHERE BINARY migration_name = BINARY @migration_name;

-- ========================================
-- 8. 验证迁移结果
-- ========================================

SELECT '========================================' AS `separator`;
SELECT '部门进单控制功能迁移验证结果' AS `title`;
SELECT '========================================' AS `separator`;

-- 验证字段是否正确添加
SELECT
    COLUMN_NAME as `字段名`,
    DATA_TYPE as `数据类型`,
    IS_NULLABLE as `允许NULL`,
    COLUMN_DEFAULT as `默认值`,
    COLUMN_COMMENT as `注释`
FROM information_schema.columns
WHERE table_schema = DATABASE()
  AND table_name = 'departments'
  AND column_name IN ('enable_binding', 'binding_weight')
ORDER BY ORDINAL_POSITION;

-- 验证索引是否正确创建
SELECT
    INDEX_NAME as `索引名`,
    COLUMN_NAME as `字段名`,
    SEQ_IN_INDEX as `顺序`
FROM information_schema.statistics
WHERE table_schema = DATABASE()
  AND table_name = 'departments'
  AND index_name = 'idx_dept_binding_controls'
ORDER BY SEQ_IN_INDEX;

-- 显示部门绑卡控制状态统计
SELECT
    enable_binding as `进单开关`,
    COUNT(*) as `部门数量`,
    AVG(binding_weight) as `平均权重`,
    SUM(binding_weight) as `总权重`
FROM departments
GROUP BY enable_binding;

SELECT '迁移执行完成！' AS `final_status`;
SELECT '请验证上述结果是否符合预期' AS `reminder`;

-- ========================================
-- 9. 使用说明和注意事项
-- ========================================

/*
使用说明：

1. 进单开关 (enable_binding)：
   - 1: 启用绑卡，该部门的CK可以被绑卡请求使用
   - 0: 禁用绑卡，该部门的CK不会被绑卡请求使用
   - 默认值: 1 (启用)

2. 进单权重 (binding_weight)：
   - 数值越大，该部门的CK被选中的概率越高
   - 权重为0的部门不参与CK分配
   - 默认值: 100
   - 建议范围: 1-1000

3. 权重分配示例：
   - 部门A权重80，部门B权重20
   - 则约80%的绑卡请求使用部门A的CK，20%使用部门B的CK

4. 注意事项：
   - 只有enable_binding=1且binding_weight>0的部门才参与CK分配
   - 权重变更会立即生效，无需重启服务
   - 建议定期监控各部门的CK使用情况，合理调整权重

5. 相关API接口：
   - GET /api/v1/departments/{id}/binding-status - 查询部门绑卡状态
   - PUT /api/v1/departments/{id}/binding-controls - 更新部门绑卡控制
   - POST /api/v1/departments/batch-binding-controls - 批量更新绑卡控制

6. 字符集排序规则修复说明：
   - 本修复版本统一使用utf8mb4_unicode_ci排序规则
   - 使用BINARY比较避免排序规则冲突
   - 使用DATABASE()函数代替硬编码数据库名
   - 确保所有字符串字段都明确指定字符集和排序规则

7. 故障排除：
   - 如果仍然遇到排序规则错误，请检查数据库默认字符集设置
   - 可以通过以下命令查看当前数据库字符集：
     SHOW VARIABLES LIKE 'character_set%';
     SHOW VARIABLES LIKE 'collation%';
   - 如需修改数据库默认字符集：
     ALTER DATABASE walmart_card_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
*/
