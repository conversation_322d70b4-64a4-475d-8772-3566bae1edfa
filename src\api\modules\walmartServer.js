import { http } from '@/api/request'
import { API_URLS } from './config'

const { WALMART_CONFIG } = API_URLS

/**
 * 沃尔玛服务端配置相关API
 */
export const walmartServerApi = {
    // 获取配置列表
    getList() {
        return http.get(WALMART_CONFIG.LIST).then(res => res.data)
    },
    update(data) {
        return http.put(WALMART_CONFIG.UPDATE, data).then(res => res.data)
    },
}

export default walmartServerApi