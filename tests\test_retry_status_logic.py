#!/usr/bin/env python3
"""
测试重试状态逻辑 - 简化版本，只测试状态检查逻辑
"""

import sys
import os
import uuid
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from app.core.config import settings
from app.models.user import User
from app.models.merchant import Merchant
from app.models.department import Department
from app.models.walmart_ck import WalmartCK
from app.models.card_record import CardRecord, CardStatus
from app.services.card_record_service import CardRecordService


def create_test_session():
    """创建测试数据库会话"""
    engine = create_engine(settings.SQLALCHEMY_DATABASE_URI)
    SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
    return SessionLocal()


def test_retry_status_validation():
    """测试重试状态验证逻辑"""
    print("=== 测试重试状态验证逻辑 ===")
    
    db = create_test_session()
    card_service = CardRecordService(db)
    
    # 获取现有的商户和用户进行测试
    merchant = db.query(Merchant).first()
    user = db.query(User).filter(User.merchant_id == merchant.id).first()
    
    if not merchant or not user:
        print("❌ 没有找到测试用的商户和用户")
        return
    
    print(f"使用商户: {merchant.id}({merchant.name})")
    print(f"使用用户: {user.id}({user.username})")
    
    # 测试用例1: failed状态 + CK失效错误（应该允许重试）
    print("\n测试1: failed状态 + CK失效错误")
    failed_card_retryable = CardRecord(
        id=str(uuid.uuid4()),
        merchant_id=merchant.id,
        department_id=user.department_id,
        walmart_ck_id=None,
        merchant_order_id="TEST_FAILED_RETRYABLE",
        amount=10000,
        card_number="1111222233334444",
        status=CardStatus.FAILED,
        request_id=str(uuid.uuid4()),
        request_data={},
        error_message="需要登录",  # CK失效错误
        retry_count=0
    )
    db.add(failed_card_retryable)
    db.commit()
    
    try:
        # 模拟重试验证逻辑（不实际重新提交队列）
        card = card_service.get_with_isolation(str(failed_card_retryable.id), user)
        
        # 检查状态
        if card.status not in ['failed', 'pending']:
            print(f"❌ 状态检查失败: {card.status}")
        else:
            print(f"✅ 状态检查通过: {card.status}")
        
        # 检查是否可重试
        if card.status == 'failed' and not card_service._is_retryable_failure(card):
            print("❌ 重试检查失败: 不允许重试")
        else:
            print("✅ 重试检查通过: 允许重试")
            
    except Exception as e:
        print(f"❌ 测试1失败: {e}")
    finally:
        db.delete(failed_card_retryable)
        db.commit()
    
    # 测试用例2: failed状态 + 非CK失效错误（应该拒绝重试）
    print("\n测试2: failed状态 + 非CK失效错误")
    failed_card_non_retryable = CardRecord(
        id=str(uuid.uuid4()),
        merchant_id=merchant.id,
        department_id=user.department_id,
        walmart_ck_id=None,
        merchant_order_id="TEST_FAILED_NON_RETRYABLE",
        amount=10000,
        card_number="****************",
        status=CardStatus.FAILED,
        request_id=str(uuid.uuid4()),
        request_data={},
        error_message="卡号无效",  # 非CK失效错误
        retry_count=0
    )
    db.add(failed_card_non_retryable)
    db.commit()
    
    try:
        card = card_service.get_with_isolation(str(failed_card_non_retryable.id), user)
        
        # 检查状态
        if card.status not in ['failed', 'pending']:
            print(f"❌ 状态检查失败: {card.status}")
        else:
            print(f"✅ 状态检查通过: {card.status}")
        
        # 检查是否可重试
        if card.status == 'failed' and not card_service._is_retryable_failure(card):
            print("✅ 重试检查通过: 正确拒绝重试")
        else:
            print("❌ 重试检查失败: 应该拒绝重试但却允许了")
            
    except Exception as e:
        print(f"❌ 测试2失败: {e}")
    finally:
        db.delete(failed_card_non_retryable)
        db.commit()
    
    # 测试用例3: pending状态（应该允许重试）
    print("\n测试3: pending状态")
    pending_card = CardRecord(
        id=str(uuid.uuid4()),
        merchant_id=merchant.id,
        department_id=user.department_id,
        walmart_ck_id=None,
        merchant_order_id="TEST_PENDING",
        amount=10000,
        card_number="9999888877776666",
        status=CardStatus.PENDING,
        request_id=str(uuid.uuid4()),
        request_data={},
        retry_count=0
    )
    db.add(pending_card)
    db.commit()
    
    try:
        card = card_service.get_with_isolation(str(pending_card.id), user)
        
        # 检查状态
        if card.status not in ['failed', 'pending']:
            print(f"❌ 状态检查失败: {card.status}")
        else:
            print(f"✅ 状态检查通过: {card.status}")
        
        # pending状态应该直接允许重试
        print("✅ pending状态允许重试")
            
    except Exception as e:
        print(f"❌ 测试3失败: {e}")
    finally:
        db.delete(pending_card)
        db.commit()
    
    # 测试用例4: success状态（应该拒绝重试）
    print("\n测试4: success状态")
    success_card = CardRecord(
        id=str(uuid.uuid4()),
        merchant_id=merchant.id,
        department_id=user.department_id,
        walmart_ck_id=None,
        merchant_order_id="TEST_SUCCESS",
        amount=10000,
        card_number="1234567890123456",
        status=CardStatus.SUCCESS,
        request_id=str(uuid.uuid4()),
        request_data={},
        retry_count=0
    )
    db.add(success_card)
    db.commit()
    
    try:
        card = card_service.get_with_isolation(str(success_card.id), user)
        
        # 检查状态
        if card.status not in ['failed', 'pending']:
            print(f"✅ 状态检查通过: 正确拒绝{card.status}状态")
        else:
            print(f"❌ 状态检查失败: 不应该允许{card.status}状态重试")
            
    except Exception as e:
        print(f"❌ 测试4失败: {e}")
    finally:
        db.delete(success_card)
        db.commit()
    
    db.close()


def test_is_retryable_failure():
    """测试重试失败判断逻辑"""
    print("\n=== 测试重试失败判断逻辑 ===")
    
    db = create_test_session()
    card_service = CardRecordService(db)
    
    # 测试CK失效关键词
    ck_invalid_keywords = [
        "需要登录",
        "登录失效", 
        "CK失效",
        "用户未登录",
        "session过期",
        "认证失败",
        "401",
        "403"
    ]
    
    for keyword in ck_invalid_keywords:
        # 创建测试卡记录
        test_card = CardRecord(
            id=str(uuid.uuid4()),
            merchant_id=1,
            department_id=1,
            walmart_ck_id=None,
            merchant_order_id=f"TEST_{keyword.replace(' ', '_')}",
            amount=10000,
            card_number="1111222233334444",
            status=CardStatus.FAILED,
            request_id=str(uuid.uuid4()),
            request_data={},
            error_message=f"绑卡失败: {keyword}",
            retry_count=0
        )
        
        # 测试判断逻辑
        is_retryable = card_service._is_retryable_failure(test_card)
        if is_retryable:
            print(f"✅ 关键词 '{keyword}' 正确识别为可重试")
        else:
            print(f"❌ 关键词 '{keyword}' 未被识别为可重试")
    
    # 测试非CK失效错误
    non_ck_errors = [
        "卡号无效",
        "金额错误",
        "网络超时",
        "系统错误"
    ]
    
    for error in non_ck_errors:
        test_card = CardRecord(
            id=str(uuid.uuid4()),
            merchant_id=1,
            department_id=1,
            walmart_ck_id=None,
            merchant_order_id=f"TEST_{error.replace(' ', '_')}",
            amount=10000,
            card_number="1111222233334444",
            status=CardStatus.FAILED,
            request_id=str(uuid.uuid4()),
            request_data={},
            error_message=f"绑卡失败: {error}",
            retry_count=0
        )
        
        is_retryable = card_service._is_retryable_failure(test_card)
        if not is_retryable:
            print(f"✅ 错误 '{error}' 正确识别为不可重试")
        else:
            print(f"❌ 错误 '{error}' 错误识别为可重试")
    
    db.close()


def main():
    """主测试函数"""
    print("开始重试状态逻辑测试")
    print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    try:
        test_retry_status_validation()
        test_is_retryable_failure()
        
        print(f"\n🎉 重试状态逻辑测试完成！")
        print(f"测试完成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        raise


if __name__ == "__main__":
    main()
