"""
绑定命令处理器
"""

from datetime import datetime, timedelta
from telegram import Update
from telegram.ext import ContextTypes

from app.models.telegram_group import TelegramGroup, BindStatus
from app.models.telegram_user import TelegramUser, VerificationStatus
from app.models.base import local_now
from .base_handler import BaseCommandHandler
from ..services.user_verification_service import UserVerificationService
from ..services.error_handler import telegram_error_handler, ErrorType
from ..services.interaction_service import interaction_service
from ..services.message_formatter import message_formatter
from ..services.user_guide_service import UserGuideService, GuideType
from ..services.enhanced_error_handler import EnhancedErrorHandler
from ..services.progress_tracker import get_progress_tracker, ProgressType
from ..exceptions import InvalidTokenError, PermissionError


class BindCommandHandler(BaseCommandHandler):
    """绑定命令处理器"""

    def __init__(self, db, config, rate_limiter):
        super().__init__(db, config, rate_limiter)

        # 初始化增强服务
        self.user_guide_service = UserGuideService(db, config)
        self.enhanced_error_handler = EnhancedErrorHandler(config)

    def _get_user_display_name(self, update: Update) -> str:
        """获取用户显示名称"""
        user = update.effective_user
        if user.first_name and user.last_name:
            return f"{user.first_name} {user.last_name}"
        elif user.first_name:
            return user.first_name
        elif user.username:
            return f"@{user.username}"
        else:
            return "朋友"

    async def verify_permissions(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """验证绑定权限"""
        # 绑定命令需要群组管理员权限
        user = update.effective_user
        chat = update.effective_chat

        # 检查是否为群组
        if chat.type not in ['group', 'supergroup']:
            await self._send_private_chat_guidance(update, context)
            raise PermissionError("绑定命令只能在群组中使用")

        # 检查用户是否为管理员
        chat_member = await context.bot.get_chat_member(chat.id, user.id)
        if chat_member.status not in ['creator', 'administrator']:
            await self._send_non_admin_guidance(update, chat_member.status, context)
            raise PermissionError("只有群组管理员可以执行绑定操作")

    async def _send_private_chat_guidance(self, update: Update, context: ContextTypes.DEFAULT_TYPE = None):
        """发送私聊中的引导信息"""
        guidance_text = """🤖 **群组绑定指南**

您正在私聊中尝试绑定群组，这是不可以的哦！

📋 **正确的绑定步骤**：
1️⃣ 将机器人添加到需要绑定的群组中
2️⃣ 在群组中输入绑定命令：`/bind <绑定令牌>`
3️⃣ 确保您是群组的管理员或创建者

💡 **如果您是普通群组成员**：
• 请联系群组管理员执行绑定操作
• 或者请管理员将您设置为群组管理员

🔗 **需要绑定令牌？**
• 联系系统管理员获取绑定令牌
• 绑定令牌通常格式为：`tg_bind_xxxxxxxxx`

📞 **需要帮助？** 输入 `/help` 查看更多信息"""

        await self.send_response(update, guidance_text, context=context, parse_mode='Markdown')

    async def _send_non_admin_guidance(self, update: Update, user_status: str, context: ContextTypes.DEFAULT_TYPE = None):
        """发送非管理员用户的引导信息"""
        user_name = self.get_user_display_name(update)
        chat_title = update.effective_chat.title or "此群组"

        # 根据用户状态提供不同的引导
        if user_status == 'member':
            status_desc = "普通成员"
            solutions = [
                "请联系群组管理员执行绑定操作",
                "或者请管理员将您提升为群组管理员",
                "群组创建者和管理员可以在群组设置中管理管理员权限"
            ]
        elif user_status == 'restricted':
            status_desc = "受限用户"
            solutions = [
                "您的账户在此群组中受到限制",
                "请联系群组管理员解除限制",
                "或者请其他管理员执行绑定操作"
            ]
        elif user_status == 'left':
            status_desc = "已离开群组"
            solutions = [
                "您已经离开了这个群组",
                "请重新加入群组后再尝试",
                "或者请群组管理员执行绑定操作"
            ]
        elif user_status == 'kicked':
            status_desc = "被踢出群组"
            solutions = [
                "您已被踢出此群组",
                "请联系群组管理员重新邀请您",
                "或者请其他管理员执行绑定操作"
            ]
        else:
            status_desc = f"当前状态：{user_status}"
            solutions = [
                "请联系群组管理员执行绑定操作",
                "确认您有足够的权限执行此操作"
            ]

        guidance_text = f"""🚫 **权限不足**

您好，{user_name}！

❌ **当前状态**：您在 "{chat_title}" 中是 **{status_desc}**
🔒 **权限要求**：只有群组创建者和管理员可以执行绑定操作

💡 **解决方案**：
"""

        for i, solution in enumerate(solutions, 1):
            guidance_text += f"{i}️⃣ {solution}\n"

        guidance_text += f"""
🔍 **如何查看群组管理员**：
• 在群组中点击群组名称
• 查看"管理员"列表
• 联系列表中的任意管理员

📞 **需要帮助？**
• 输入 `/help` 查看更多信息
• 联系系统管理员获取支持

💡 **提示**：绑定令牌仍然有效，管理员可以使用相同的令牌完成绑定"""

        await self.send_response(update, guidance_text, context=context, parse_mode='Markdown')

    async def execute_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """执行绑定命令"""
        args = context.args
        if not args:
            error_context = {
                "command_usage": "/bind <绑定令牌>",
                "user_name": self.get_user_display_name(update)
            }
            error_message = telegram_error_handler.get_error_with_suggestions(
                ErrorType.INVALID_PARAMETER,
                error_context
            )
            await self.send_response(update, error_message, context=context, parse_mode='Markdown')
            return {"error": "missing_token"}

        bind_token = args[0]
        chat_id = update.effective_chat.id
        user_id = update.effective_user.id

        # 显示绑定进度
        await interaction_service.show_progress(update, "群组绑定", 1, 3, "验证令牌...")

        # 验证绑定令牌并执行绑定
        result = await self._process_bind(chat_id, user_id, bind_token, update, context)
        return result
    
    async def _process_bind(self, chat_id: int, user_id: int, bind_token: str, update: Update, context: ContextTypes.DEFAULT_TYPE = None) -> dict:
        """处理绑定逻辑"""
        try:
            # 更新进度：检查现有绑定
            await interaction_service.show_progress(update, "群组绑定", 2, 3, "检查现有绑定...")

            # 检查群组是否已绑定
            existing_group = await self.get_telegram_group(chat_id)
            if existing_group and existing_group.bind_status == BindStatus.ACTIVE:
                # 完成进度显示（失败）
                await interaction_service.complete_progress(
                    update, "群组绑定", False,
                    {"错误": "群组已绑定", "商户": existing_group.merchant.name if existing_group.merchant else '未知'},
                    ["使用 `/unbind` 解绑后重新绑定", "输入 `/help` 查看帮助"]
                )
                return {"error": "already_bound", "merchant": existing_group.merchant.name if existing_group.merchant else None}

            # 查找有效的绑定令牌（支持PENDING和FAILED状态重试）
            pending_group = self.db.query(TelegramGroup).filter(
                TelegramGroup.bind_token == bind_token,
                TelegramGroup.bind_status.in_(BindStatus.get_retryable_values())
            ).first()

            if not pending_group:
                # 检查是否存在已绑定的令牌
                bound_group = self.db.query(TelegramGroup).filter_by(
                    bind_token=bind_token,
                    bind_status=BindStatus.ACTIVE
                ).first()

                if bound_group:
                    # 令牌已被使用
                    await interaction_service.complete_progress(
                        update, "群组绑定", False,
                        {"错误": "令牌已被使用", "商户": bound_group.merchant.name if bound_group.merchant else '未知'},
                        ["该令牌已成功绑定到其他群组", "联系管理员获取新令牌", "输入 `/help` 查看帮助"]
                    )
                    return {"error": "token_already_used"}
                else:
                    # 令牌不存在或已过期
                    await interaction_service.complete_progress(
                        update, "群组绑定", False,
                        {"错误": "令牌无效或已过期", "令牌": f"{bind_token[:8]}..."},
                        ["检查令牌是否正确", "联系管理员获取新令牌", "输入 `/help` 查看帮助"]
                    )
                    return {"error": "invalid_token"}

            # 检查令牌是否过期
            if self._is_token_expired(pending_group):
                # 完成进度显示（失败）
                await interaction_service.complete_progress(
                    update, "群组绑定", False,
                    {"错误": "令牌已过期", "商户": pending_group.merchant.name if pending_group.merchant else '未知'},
                    ["联系管理员获取新令牌", "输入 `/help` 查看帮助"]
                )
                return {"error": "token_expired"}

            # 获取执行绑定操作的系统用户ID（可选）
            # 注意：群组绑定基于绑定令牌验证，不需要用户身份验证
            system_user_id = None
            telegram_user = self.db.query(TelegramUser).filter_by(
                telegram_user_id=user_id
            ).first()

            # 如果用户已验证，记录其系统用户ID；否则使用None
            if telegram_user and telegram_user.is_verified() and telegram_user.system_user_id:
                system_user_id = telegram_user.system_user_id

            # 群组绑定不需要用户身份验证，只需要有效的绑定令牌
            # 管理员可以使用绑定令牌直接绑定群组

            # 更新进度：执行绑定
            await interaction_service.show_progress(update, "群组绑定", 3, 3, "执行绑定操作...")

            # 使用事务确保绑定操作的原子性
            try:
                # 更新群组信息
                pending_group.chat_id = chat_id
                pending_group.chat_title = update.effective_chat.title or f"Chat {chat_id}"
                pending_group.chat_type = update.effective_chat.type
                pending_group.bind_status = BindStatus.ACTIVE
                pending_group.bind_time = local_now()
                pending_group.bind_user_id = system_user_id  # 使用系统用户ID而不是Telegram用户ID
                pending_group.last_active_time = local_now()

                # 如果存在旧的绑定记录，删除它
                if existing_group:
                    self.db.delete(existing_group)

                # 提交事务
                self.db.commit()

            except Exception as bind_error:
                # 绑定失败，回滚事务并设置失败状态
                self.db.rollback()
                self.logger.error(f"绑定操作失败: {bind_error}", exc_info=True)

                # 设置为失败状态，允许重试
                pending_group.bind_status = BindStatus.FAILED
                self.db.commit()

                await interaction_service.complete_progress(
                    update, "群组绑定", False,
                    {"错误": "绑定操作失败", "商户": pending_group.merchant.name if pending_group.merchant else '未知'},
                    ["令牌仍可重试使用", "稍后重新执行绑定命令", "联系管理员获取帮助"]
                )
                return {"error": "bind_operation_failed", "retryable": True}

            # 绑定成功后，自动获取所有群成员并创建验证申请
            member_count = await self._auto_create_member_verifications(update, context, pending_group)

            # 完成进度显示（成功）
            await interaction_service.complete_progress(
                update, "群组绑定", True,
                {
                    "商户": pending_group.merchant.name if pending_group.merchant else '未知',
                    "部门": pending_group.department.name if pending_group.department else '全部',
                    "群组": pending_group.chat_title,
                    "绑定时间": pending_group.bind_time.strftime('%Y-%m-%d %H:%M:%S'),
                    "群成员": f"已自动获取 {member_count} 个群成员"
                },
                ["群成员已自动创建验证申请", "管理员可批量审核群成员", "输入 `/help` 查看可用命令"]
            )
            
            return {
                "success": True,
                "merchant_id": pending_group.merchant_id,
                "merchant_name": pending_group.merchant.name if pending_group.merchant else None,
                "department_id": pending_group.department_id,
                "department_name": pending_group.department.name if pending_group.department else None
            }
            
        except Exception as e:
            self.logger.error(f"绑定处理失败: {e}", exc_info=True)
            await self.send_response(
                update,
                "❌ 绑定处理失败，请稍后再试",
                context=context
            )
            return {"error": "bind_failed", "message": str(e)}

    async def _auto_create_member_verifications(self, update: Update, context: ContextTypes.DEFAULT_TYPE, group) -> int:
        """自动获取群组所有成员并创建验证申请"""
        try:
            chat_id = update.effective_chat.id
            member_count = 0
            created_count = 0

            self.logger.info(f"开始自动获取群组 {chat_id} 的所有成员")

            # 方法1: 获取群组管理员
            administrators = await context.bot.get_chat_administrators(chat_id)

            for member in administrators:
                try:
                    await self._create_member_verification(member, group, context)
                    created_count += 1
                    member_count += 1
                except Exception as e:
                    self.logger.warning(f"为管理员 {member.user.id} 创建验证申请失败: {e}")
                    member_count += 1

            # 方法2: 尝试通过遍历用户ID获取群成员（实验性方法）
            additional_members = await self._try_get_members_by_iteration(chat_id, context, group)
            created_count += additional_members

            # 获取群组成员总数（仅用于显示）
            try:
                chat_info = await context.bot.get_chat(chat_id)
                total_members = getattr(chat_info, 'member_count', 0)
            except:
                total_members = 0

            # 发送通知消息给群组
            member_info = f"群组总成员: {total_members}" if total_members > 0 else "群组成员数量未知"

            notification_message = f"""🎉 **群组绑定成功！**

✅ **绑定信息**：
• 商户：{group.merchant.name if group.merchant else '未知'}
• 部门：{group.department.name if group.department else '全部'}
• 群组：{group.chat_title}
• {member_info}

👥 **成员验证状态**：
• ✅ 已为 {created_count} 个管理员自动创建验证申请
• ⏳ 普通群成员请使用 `/verify` 命令申请验证

📋 **管理员操作**：
• 登录系统审核群成员验证申请
• 审核通过后，成员可使用统计查询功能

💡 **群成员操作**：
• 输入 `/verify` 申请身份验证
• 输入关键词查询统计（如：CK今日、昨日数据等）
• 输入 `/help` 查看帮助信息

🔔 **重要提醒**：
由于Telegram API限制，只能自动为管理员创建验证申请
普通群成员需要主动使用 `/verify` 命令申请验证"""

            await self.send_response(
                update,
                notification_message,
                context=context,
                parse_mode='Markdown'
            )

            self.logger.info(f"群组 {chat_id} 自动创建了 {created_count} 个验证申请")
            return created_count

        except Exception as e:
            self.logger.error(f"自动获取群成员失败: {e}", exc_info=True)
            return 0

    async def _try_get_members_by_iteration(self, chat_id: int, context: ContextTypes.DEFAULT_TYPE, group) -> int:
        """尝试通过遍历方法获取群成员（实验性）"""
        created_count = 0

        try:
            self.logger.info(f"尝试通过消息监听收集群组 {chat_id} 的成员信息")

            # 启用群组成员收集模式
            await self._enable_member_collection_mode(chat_id, context, group)

            # 发送引导消息，鼓励群成员互动
            guidance_message = f"""🔔 **群组绑定成功通知**

✅ 本群组已成功绑定到系统！

👥 **自动成员收集**：
• 系统将自动收集活跃群成员信息
• 当群成员发送消息时，系统会自动为其创建验证申请
• 无需手动操作，系统会智能识别群成员

💡 **加速验证流程**：
• 群成员可以发送任意消息（如：打个招呼👋）
• 或者直接使用 `/verify` 命令申请验证
• 系统会自动收集您的信息并创建验证申请

📊 **可用功能**：
• 输入关键词查询统计（如：CK今日、昨日数据等）
• 输入 `/help` 查看帮助信息

🎯 **目标**：让所有群成员都能便捷地使用统计查询功能！"""

            await context.bot.send_message(
                chat_id=chat_id,
                text=guidance_message,
                parse_mode='Markdown'
            )

            self.logger.info(f"已为群组 {chat_id} 启用成员收集模式")

        except Exception as e:
            self.logger.error(f"启用成员收集模式失败: {e}")

        return created_count

    async def _enable_member_collection_mode(self, chat_id: int, context: ContextTypes.DEFAULT_TYPE, group):
        """启用群组成员收集模式"""
        try:
            # 在数据库中标记该群组启用成员收集模式
            group.settings = group.settings or {}
            group.settings['member_collection_enabled'] = True
            group.settings['member_collection_start_time'] = local_now().isoformat()

            # 设置收集模式的过期时间（比如7天后自动关闭）
            from datetime import timedelta
            expire_time = local_now() + timedelta(days=7)
            group.settings['member_collection_expire_time'] = expire_time.isoformat()

            self.db.commit()

            self.logger.info(f"群组 {chat_id} 成员收集模式已启用，将在7天后自动关闭")

        except Exception as e:
            self.logger.error(f"启用成员收集模式失败: {e}")
            raise

    async def _create_member_verification(self, member, group, context):
        """为单个群成员创建验证申请"""
        try:
            user = member.user

            # 跳过机器人
            if user.is_bot:
                return

            # 检查用户是否已经有验证记录
            existing_user = self.db.query(TelegramUser).filter_by(
                telegram_user_id=user.id
            ).first()

            if existing_user and existing_user.verification_status == VerificationStatus.VERIFIED.value:
                self.logger.info(f"用户 {user.id} 已经验证，跳过")
                return

            # 获取用户角色信息
            member_role = {
                'creator': '群主',
                'administrator': '管理员',
                'member': '群成员',
                'restricted': '受限成员',
                'left': '已离开',
                'kicked': '已被踢出'
            }.get(member.status, '未知')

            # 创建用户验证服务
            verification_service = UserVerificationService(self.db, self.config)

            # 创建验证请求
            verification_token = await verification_service.create_verification_request(
                telegram_user_id=user.id,
                telegram_username=user.username,
                telegram_first_name=user.first_name,
                telegram_last_name=user.last_name,
                additional_info={
                    'group_id': group.chat_id,
                    'group_title': group.chat_title,
                    'member_status': member.status,
                    'member_role': member_role,
                    'auto_created': True,  # 标记为自动创建
                    'merchant_id': group.merchant_id,
                    'department_id': group.department_id
                }
            )

            self.logger.info(f"为用户 {user.id} ({user.first_name}) 自动创建验证申请: {verification_token}")

        except Exception as e:
            self.logger.error(f"为用户 {user.id if 'user' in locals() else 'unknown'} 创建验证申请失败: {e}")
            raise
    
    def _is_token_expired(self, group: TelegramGroup) -> bool:
        """检查令牌是否过期"""
        if not group.created_at:
            return True

        # 使用统一的时间处理工具
        from app.utils.time_utils import is_expired

        expire_hours = self.config.bind_token_expire_hours
        expire_duration = timedelta(hours=expire_hours)

        return is_expired(group.created_at, expire_duration)
    
    async def handle_unbind(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """处理解绑命令"""
        try:
            # 验证权限
            await self.verify_permissions(update, context)
            
            chat_id = update.effective_chat.id
            
            # 查找绑定的群组
            group = await self.get_telegram_group(chat_id)
            if not group:
                await self.send_response(
                    update,
                    "⚠️ 群组未绑定，无需解绑",
                    context=context
                )
                return {"error": "not_bound"}
            
            # 保存信息用于响应
            merchant_name = group.merchant.name if group.merchant else "未知"
            
            # 删除绑定记录
            self.db.delete(group)
            self.db.commit()
            
            await self.send_response(
                update,
                f"✅ **群组解绑成功**\n\n已解除与商户「{merchant_name}」的绑定关系",
                context=context
            )
            
            return {"success": True, "merchant_name": merchant_name}
            
        except Exception as e:
            self.logger.error(f"解绑处理失败: {e}", exc_info=True)
            await self.send_response(
                update,
                "❌ 解绑处理失败，请稍后再试",
                context=context
            )
            return {"error": "unbind_failed", "message": str(e)}
    
    async def handle_verify(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """处理用户验证命令 - 自动获取群成员等待验证"""
        user_id = update.effective_user.id
        user = update.effective_user
        user_name = self.get_user_display_name(update)
        chat = update.effective_chat

        # 检查是否在群组中使用
        if chat.type not in ['group', 'supergroup']:
            await self.send_response(
                update,
                f"""❓ **验证命令使用说明 - {user_name}**

⚠️ 验证命令只能在群组中使用！

📋 **正确使用方法**：
1. 在已绑定的群组中输入 `/verify`
2. 系统会自动获取群成员列表
3. 等待管理员审核验证

💡 **提示**：
• 请在群组中使用此命令
• 确保群组已经绑定到系统""",
                context=context,
                parse_mode='Markdown'
            )
            return {"error": "not_in_group"}

        # 检查群组是否已绑定
        try:
            group = await self.verify_group_bound(chat.id)
        except Exception as e:
            await self.send_response(
                update,
                f"""❌ **群组未绑定 - {user_name}**

此群组尚未绑定到系统，无法使用验证功能。

📋 **解决方法**：
• 请群组管理员先使用 `/bind <绑定令牌>` 绑定群组
• 绑定完成后即可使用验证功能

💡 **需要帮助？**
输入 `/help` 查看详细说明""",
                context=context,
                parse_mode='Markdown'
            )
            return {"error": "group_not_bound"}

        # 自动获取群成员并创建验证请求
        return await self._handle_auto_verification_request(update, context, user, user_name, group)

    async def _handle_auto_verification_request(self, update: Update, context: ContextTypes.DEFAULT_TYPE, user, user_name: str, group):
        """处理自动验证请求 - 获取群成员信息"""
        user_id = user.id
        chat = update.effective_chat

        try:
            # 检查用户是否已经验证
            existing_user = self.db.query(TelegramUser).filter_by(
                telegram_user_id=user_id,
                verification_status=VerificationStatus.VERIFIED.value
            ).first()

            if existing_user:
                await self.send_response(
                    update,
                    f"""✅ **身份验证状态 - {user_name}**

🎉 您已经完成身份验证，可以正常使用所有功能！

📊 **验证信息**：
• 验证时间：{existing_user.verification_time.strftime('%Y-%m-%d %H:%M:%S') if existing_user.verification_time else '未知'}
• 关联用户：{existing_user.system_user.username if existing_user.system_user else '未知'}
• 所在群组：{chat.title}

💡 **可用功能**：
• 输入 `/status` 查看详细状态
• 输入关键词查询统计数据（如：CK今日、昨日数据等）""",
                    context=context,
                    parse_mode='Markdown'
                )
                return {"status": "already_verified"}

            # 检查是否有待审核的验证请求
            pending_user = self.db.query(TelegramUser).filter_by(
                telegram_user_id=user_id,
                verification_status=VerificationStatus.PENDING.value
            ).first()

            if pending_user and pending_user.verification_token:
                # 检查令牌是否过期
                temp_service = UserVerificationService(self.db, self.config)
                if not temp_service._is_verification_token_expired(pending_user):
                    await self.send_response(
                        update,
                        f"""⏳ **验证申请进行中 - {user_name}**

您已有一个正在处理的验证申请。

🔑 **当前验证令牌**：`{pending_user.verification_token}`
📍 **申请群组**：{chat.title}
📋 **申请状态**：等待管理员审核

💡 **系统已自动获取您的群成员信息**：
• 用户名：@{user.username if user.username else '未设置'}
• 姓名：{user.first_name} {user.last_name or ''}
• 群组角色：群成员

📞 **联系管理员**：
• 如果超过30分钟未处理，请联系管理员
• 提供验证令牌以加快审核速度

⏰ **令牌有效期**：{self.config.verification_token_expire_minutes} 分钟""",
                        context=context,
                        parse_mode='Markdown'
                    )
                    return {"status": "pending_verification", "token": pending_user.verification_token}

            # 获取用户在群组中的角色信息
            try:
                chat_member = await context.bot.get_chat_member(chat.id, user_id)
                member_status = chat_member.status
                member_role = {
                    'creator': '群主',
                    'administrator': '管理员',
                    'member': '群成员',
                    'restricted': '受限成员',
                    'left': '已离开',
                    'kicked': '已被踢出'
                }.get(member_status, '未知')
            except Exception as e:
                self.logger.warning(f"获取群成员信息失败: {e}")
                member_status = 'member'
                member_role = '群成员'

            # 创建用户验证服务
            verification_service = UserVerificationService(self.db, self.config)

            # 创建新的验证请求，包含群成员信息
            verification_token = await verification_service.create_verification_request(
                telegram_user_id=user_id,
                telegram_username=user.username,
                telegram_first_name=user.first_name,
                telegram_last_name=user.last_name,
                additional_info={
                    'group_id': chat.id,
                    'group_title': chat.title,
                    'member_status': member_status,
                    'member_role': member_role,
                    'join_date': chat_member.until_date.isoformat() if hasattr(chat_member, 'until_date') and chat_member.until_date else None
                }
            )

            # 创建进度跟踪
            progress_tracker = get_progress_tracker(context.bot, self.db)
            await progress_tracker.create_progress_silent(
                user_id=user_id,
                progress_type=ProgressType.USER_VERIFICATION,
                title="身份验证申请",
                description=f"正在处理来自群组 {chat.title} 的身份验证申请",
                estimated_duration=30
            )

            await self.send_response(
                update,
                f"""🚀 **验证申请已提交 - {user_name}**

✅ 系统已自动获取您的群成员信息并创建验证申请！

🔑 **验证令牌**：`{verification_token}`

📊 **自动获取的信息**：
• 用户名：@{user.username if user.username else '未设置'}
• 姓名：{user.first_name} {user.last_name or ''}
• 群组：{chat.title}
• 群组角色：{member_role}

📋 **申请状态**：等待管理员审核

💡 **接下来**：
• 管理员会根据您的群成员信息进行审核
• 审核通过后您将收到通知
• 无需额外操作，请耐心等待

📞 **联系管理员**：
• 如需加快审核，请提供验证令牌：`{verification_token}`
• 输入 `/status` 随时查看审核进度

⏰ **重要提醒**：
• 验证令牌有效期为 {self.config.verification_token_expire_minutes} 分钟
• 系统已自动收集必要信息，无需手动提供""",
                context=context,
                parse_mode='Markdown'
            )

            return {
                "success": True,
                "verification_token": verification_token,
                "user_id": user_id,
                "group_info": {
                    "group_id": chat.id,
                    "group_title": chat.title,
                    "member_role": member_role
                }
            }

        except Exception as e:
            self.logger.error(f"自动验证申请失败: {e}", exc_info=True)

            error_message = f"""❌ **验证申请失败 - {user_name}**

处理您的验证申请时发生错误。

🔍 **可能的原因**：
• 系统暂时繁忙
• 网络连接问题
• 群组权限设置问题

🔧 **解决方法**：
• 请稍后重试 `/verify` 命令
• 确保机器人有获取群成员信息的权限
• 如问题持续，请联系管理员

💡 **需要帮助？**
输入 `/help` 获取更多支持信息"""

            await self.send_response(update, error_message, context=context, parse_mode='Markdown')
            return {"error": "auto_verification_failed", "message": str(e)}

    async def _handle_verification_request(self, update: Update, context: ContextTypes.DEFAULT_TYPE, user, user_name: str):
        """处理验证申请请求"""
        user_id = user.id

        try:
            # 创建用户验证服务
            verification_service = UserVerificationService(self.db, self.config)

            # 检查用户是否已经验证
            existing_user = self.db.query(TelegramUser).filter_by(
                telegram_user_id=user_id,
                verification_status=VerificationStatus.VERIFIED.value
            ).first()

            if existing_user:
                await self.send_response(
                    update,
                    f"""✅ **身份验证状态 - {user_name}**

🎉 您已经完成身份验证，可以正常使用所有功能！

📊 **验证信息**：
• 验证时间：{existing_user.verification_time.strftime('%Y-%m-%d %H:%M:%S') if existing_user.verification_time else '未知'}
• 关联用户：{existing_user.system_user.username if existing_user.system_user else '未知'}

💡 **可用功能**：
• 输入 `/status` 查看详细状态
• 输入 `/help` 查看所有可用命令""",
                    context=context,
                    parse_mode='Markdown'
                )
                return {"status": "already_verified"}

            # 检查是否有待审核的验证请求
            pending_user = self.db.query(TelegramUser).filter_by(
                telegram_user_id=user_id,
                verification_status=VerificationStatus.PENDING.value
            ).first()

            if pending_user and pending_user.verification_token:
                # 检查令牌是否过期
                temp_service = UserVerificationService(self.db, self.config)
                if not temp_service._is_verification_token_expired(pending_user):
                    await self.send_response(
                        update,
                        f"""⏳ **验证申请进行中 - {user_name}**

您已有一个正在处理的验证申请。

🔑 **当前验证令牌**：`{pending_user.verification_token}`

📋 **申请状态**：等待管理员审核

💡 **您需要做的**：
• 将上述验证令牌发送给系统管理员
• 等待审核完成通知
• 输入 `/status` 随时查看进度

📞 **联系管理员**：
• 如果超过30分钟未处理，请联系管理员
• 输入 `/help` 获取联系方式

⏰ **令牌有效期**：{self.config.verification_token_expire_minutes} 分钟""",
                        context=context,
                        parse_mode='Markdown'
                    )
                    return {"status": "pending_verification", "token": pending_user.verification_token}

            # 创建新的验证请求
            verification_token = await verification_service.create_verification_request(
                telegram_user_id=user_id,
                telegram_username=user.username,
                telegram_first_name=user.first_name,
                telegram_last_name=user.last_name
            )

            # 创建进度跟踪（静默模式，不发送通知）
            progress_tracker = get_progress_tracker(context.bot, self.db)
            progress_id = await progress_tracker.create_progress_silent(
                user_id=user_id,
                progress_type=ProgressType.USER_VERIFICATION,
                title="身份验证申请",
                description="正在处理您的身份验证申请",
                estimated_duration=30  # 预估30分钟
            )

            # 添加验证步骤（静默模式）
            await progress_tracker.add_step_silent(progress_id, "提交验证申请", "用户提交身份验证申请")
            await progress_tracker.add_step_silent(progress_id, "等待管理员审核", "管理员审核用户身份信息")
            await progress_tracker.add_step_silent(progress_id, "完成身份关联", "系统完成身份验证")

            # 开始第一步（静默模式）
            await progress_tracker.start_step_silent(progress_id, 0)
            await progress_tracker.complete_step_silent(progress_id, 0, "验证申请已成功提交")

            # 开始第二步（静默模式）
            await progress_tracker.start_step_silent(progress_id, 1)

            # 发送详细的验证指导
            await self.send_response(
                update,
                f"""📝 **身份验证申请已提交 - {user_name}**

🔑 **验证令牌**：`{verification_token}`

📋 **验证流程说明**：
1. ✅ 您的申请已提交
2. ⏳ 等待管理员审核（通常在30分钟内）
3. ⏳ 系统完成身份关联

💡 **您需要做的**：
• **第一步**：复制上面的验证令牌
• **第二步**：将验证令牌发送给系统管理员
• **第三步**：等待审核完成通知

📞 **联系管理员**：
• 请将验证令牌发送给管理员进行审核
• 输入 `/help` 获取详细联系方式

📊 **查看进度**：
• 输入 `/status` 随时查看验证进度
• 系统会在审核完成后自动通知您

⏰ **重要提醒**：
• 验证令牌有效期为 {self.config.verification_token_expire_minutes} 分钟
• 请及时联系管理员完成审核""",
                context=context,
                parse_mode='Markdown'
            )

            return {
                "success": True,
                "verification_token": verification_token,
                "user_id": user_id
            }

        except Exception as e:
            self.logger.error(f"用户验证申请失败: {e}", exc_info=True)

            # 使用增强错误处理
            error_context = {
                "user_id": user_id,
                "user_name": user_name,
                "error_details": str(e)
            }
            error_message = telegram_error_handler.get_error_with_suggestions(
                ErrorType.VERIFICATION_FAILED,
                error_context
            )
            await self.send_response(update, error_message, context=context, parse_mode='Markdown')
            return {"error": "verification_request_failed", "message": str(e)}

    async def _handle_verification_guidance(self, update: Update, user_input: str, user_name: str, context: ContextTypes.DEFAULT_TYPE = None):
        """处理用户错误使用验证命令的情况"""

        await self.send_response(
            update,
            f"""❓ **验证流程说明 - {user_name}**

看起来您可能对验证流程有些误解。让我为您详细说明：

❌ **您刚才的操作**：`/verify {user_input}`
✅ **正确的操作**：`/verify`（不需要参数）

📋 **正确的验证流程**：

**第一步：申请验证**
• 输入：`/verify`（不带任何参数）
• 系统会生成一个验证令牌

**第二步：联系管理员**
• 将系统生成的验证令牌发送给管理员
• 管理员在后台系统中审核您的申请

**第三步：等待审核**
• 管理员审核通过后，您就可以使用所有功能了
• 输入 `/status` 可以查看审核进度

💡 **重要说明**：
• 您不需要自己提供"验证码"
• 验证令牌是系统自动生成的
• 只有管理员可以完成最终的身份验证

🚀 **现在就开始**：
输入 `/verify` 开始申请验证

📞 **需要帮助**：
输入 `/help` 获取详细帮助和联系方式""",
            context=context,
            parse_mode='Markdown'
        )

        return {"error": "incorrect_usage", "user_input": user_input}
    
    async def handle_status(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """处理状态查询命令"""
        chat_id = update.effective_chat.id
        user_id = update.effective_user.id
        
        # 获取群组信息
        group = await self.get_telegram_group(chat_id)
        telegram_user = await self.get_telegram_user(user_id)
        
        if not group:
            await self.send_response(
                update,
                "⚠️ **群组未绑定**\n\n请使用 `/bind <令牌>` 绑定群组",
                context=context
            )
            return {"status": "not_bound"}
        
        # 生成状态信息
        status_text = f"""📊 **群组状态信息**

🏢 **绑定信息**：
• 商户：{group.merchant.name if group.merchant else '未知'}
• 部门：{group.department.name if group.department else '全部'}
• 状态：{'🟢 正常' if group.is_active() else '🔴 异常'}
• 绑定时间：{group.bind_time.strftime('%Y-%m-%d %H:%M:%S') if group.bind_time else '未知'}

👤 **用户状态**：
• 验证状态：{'✅ 已验证' if telegram_user and telegram_user.is_verified() else '❌ 未验证'}
• 最后活跃：{group.last_active_time.strftime('%Y-%m-%d %H:%M:%S') if group.last_active_time else '未知'}

💡 **提示**：
{self._get_status_tips(group, telegram_user)}"""
        
        await self.send_response(update, status_text, context=context, parse_mode='Markdown')
        
        return {
            "group_bound": group.is_active(),
            "user_verified": telegram_user.is_verified() if telegram_user else False,
            "merchant_name": group.merchant.name if group.merchant else None
        }
    
    def _get_status_tips(self, group: TelegramGroup, telegram_user: TelegramUser) -> str:
        """获取状态提示"""
        tips = []
        
        if not group.is_active():
            tips.append("• 群组状态异常，请联系管理员")
        
        if not telegram_user or not telegram_user.is_verified():
            tips.append("• 请使用 `/verify <验证码>` 验证身份")
        
        if group.is_active() and telegram_user and telegram_user.is_verified():
            tips.append("• 可以使用 `/stats` 查询统计数据")
            tips.append("• 输入 `/help` 查看所有命令")
        
        return "\n".join(tips) if tips else "• 一切正常，可以正常使用所有功能"
    
    async def handle_settings(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """处理设置命令"""
        try:
            # 验证权限
            await self.verify_permissions(update, context)
            
            # TODO: 实现群组设置功能
            await self.send_response(
                update,
                "⚙️ 群组设置功能正在开发中，敬请期待！",
                context=context
            )
            
            return {"info": "settings_not_implemented"}
            
        except Exception as e:
            await self.send_response(
                update,
                f"❌ 设置处理失败：{str(e)}",
                context=context
            )
            return {"error": "settings_failed"}
