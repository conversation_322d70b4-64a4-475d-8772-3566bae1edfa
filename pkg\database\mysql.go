package database

import (
	"context"
	"fmt"
	"strings"
	"time"

	"gorm.io/driver/mysql"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"

	"walmart-bind-card-gateway/internal/config"
	"walmart-bind-card-gateway/internal/model"
)

// DB 数据库接口
type DB interface {
	// 基础操作
	GetDB() *gorm.DB
	Close() error
	Ping() error
	
	// 批量操作
	BatchInsertCardRecords(ctx context.Context, records []*model.CardRecord) error
	BatchInsertCardRecordsFromRequests(ctx context.Context, requests []*model.InternalBindRequest) error
	BatchInsertCardRecordsAndReturn(ctx context.Context, requests []*model.InternalBindRequest) ([]*model.CardRecord, error)

	// 监控
	GetConnectionStats() map[string]interface{}
	
	// 查询操作
	GetCardRecord(ctx context.Context, requestID string) (*model.CardRecord, error)
	GetCardRecordsByStatus(ctx context.Context, status string, limit int) ([]*model.CardRecord, error)
	
	// 更新操作
	UpdateCardStatus(ctx context.Context, requestID string, status string) error
	BatchUpdateCardStatus(ctx context.Context, updates map[string]string) error
}

// MySQLDB MySQL数据库实现
type MySQLDB struct {
	db     *gorm.DB
	config *config.DatabaseConfig
}

// NewMySQLDB 创建MySQL数据库连接
func NewMySQLDB(cfg *config.DatabaseConfig) (DB, error) {
	// 构建DSN
	dsn := cfg.GetDSN()
	
	// GORM配置
	gormConfig := &gorm.Config{
		Logger: logger.Default.LogMode(logger.Info),
		NowFunc: func() time.Time {
			return time.Now().Local()
		},
		// 禁用外键约束检查以提升性能
		DisableForeignKeyConstraintWhenMigrating: true,
	}
	
	// 连接数据库
	db, err := gorm.Open(mysql.Open(dsn), gormConfig)
	if err != nil {
		return nil, fmt.Errorf("连接数据库失败: %w", err)
	}
	
	// 获取底层sql.DB
	sqlDB, err := db.DB()
	if err != nil {
		return nil, fmt.Errorf("获取sql.DB失败: %w", err)
	}
	
	// 配置连接池
	sqlDB.SetMaxOpenConns(cfg.MaxOpenConns)
	sqlDB.SetMaxIdleConns(cfg.MaxIdleConns)
	sqlDB.SetConnMaxLifetime(cfg.ConnMaxLifetime)
	sqlDB.SetConnMaxIdleTime(cfg.ConnMaxIdleTime)

	// 记录连接池配置
	fmt.Printf("数据库连接池配置: MaxOpen=%d, MaxIdle=%d, MaxLifetime=%v, MaxIdleTime=%v\n",
		cfg.MaxOpenConns, cfg.MaxIdleConns, cfg.ConnMaxLifetime, cfg.ConnMaxIdleTime)
	
	// 测试连接
	if err := sqlDB.Ping(); err != nil {
		return nil, fmt.Errorf("数据库连接测试失败: %w", err)
	}
	
	mysqlDB := &MySQLDB{
		db:     db,
		config: cfg,
	}

	// 不进行自动迁移，使用现有数据库
	// 如果需要迁移，可以手动调用 mysqlDB.autoMigrate()

	return mysqlDB, nil
}

// GetDB 获取GORM数据库实例
func (m *MySQLDB) GetDB() *gorm.DB {
	return m.db
}

// Close 关闭数据库连接
func (m *MySQLDB) Close() error {
	sqlDB, err := m.db.DB()
	if err != nil {
		return err
	}
	return sqlDB.Close()
}

// Ping 测试数据库连接
func (m *MySQLDB) Ping() error {
	sqlDB, err := m.db.DB()
	if err != nil {
		return err
	}
	return sqlDB.Ping()
}

// AutoMigrate 手动迁移数据库表（可选）
func (m *MySQLDB) AutoMigrate() error {
	return m.db.AutoMigrate(
		&model.CardRecord{},
	)
}

// BatchInsertCardRecords 批量插入卡记录
func (m *MySQLDB) BatchInsertCardRecords(ctx context.Context, records []*model.CardRecord) error {
	if len(records) == 0 {
		return nil
	}
	
	// 使用事务确保数据一致性
	return m.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		// 使用GORM的批量创建，每批1000条
		batchSize := 1000
		if len(records) < batchSize {
			batchSize = len(records)
		}
		
		return tx.CreateInBatches(records, batchSize).Error
	})
}

// BatchInsertCardRecordsFromRequests 从请求批量插入卡记录
func (m *MySQLDB) BatchInsertCardRecordsFromRequests(ctx context.Context, requests []*model.InternalBindRequest) error {
	if len(requests) == 0 {
		return nil
	}
	
	// 转换为卡记录
	records := make([]*model.CardRecord, len(requests))
	for i, req := range requests {
		records[i] = req.ToCardRecord(req.MerchantID)
	}
	
	return m.BatchInsertCardRecords(ctx, records)
}

// BatchInsertCardRecordsAndReturn 批量插入卡记录并返回创建的记录
func (m *MySQLDB) BatchInsertCardRecordsAndReturn(ctx context.Context, requests []*model.InternalBindRequest) ([]*model.CardRecord, error) {
	if len(requests) == 0 {
		return nil, nil
	}

	// 转换为卡记录
	records := make([]*model.CardRecord, len(requests))
	for i, req := range requests {
		records[i] = req.ToCardRecord(req.MerchantID)
	}

	// 批量插入到数据库
	if err := m.BatchInsertCardRecords(ctx, records); err != nil {
		return nil, err
	}

	// 返回创建的记录
	return records, nil
}

// BatchInsertCardRecordsRaw 使用原生SQL批量插入（更高性能）
func (m *MySQLDB) BatchInsertCardRecordsRaw(ctx context.Context, records []*model.CardRecord) error {
	if len(records) == 0 {
		return nil
	}
	
	// 构建批量插入SQL - 与Python系统数据库表结构保持一致
	valueStrings := make([]string, 0, len(records))
	valueArgs := make([]interface{}, 0, len(records)*15) // 增加字段数量

	for _, record := range records {
		valueStrings = append(valueStrings, "(?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)")
		valueArgs = append(valueArgs,
			record.ID,
			record.MerchantID,
			record.MerchantOrderID,
			record.Amount,
			record.CardNumber,
			record.CardPassword,
			record.Status,
			record.RequestID,
			record.RequestData,
			record.IPAddress,
			record.RetryCount,
			record.IsTestMode,
			record.CallbackStatus,
			record.CreatedAt,
			record.UpdatedAt,
		)
	}
	
	// 执行批量插入 - 与Python系统数据库表结构保持一致
	sql := fmt.Sprintf(`
		INSERT INTO card_records
		(id, merchant_id, merchant_order_id, amount, card_number, card_password, status, request_id, request_data, ip_address, retry_count, is_test_mode, callback_status, created_at, updated_at)
		VALUES %s
	`, strings.Join(valueStrings, ","))
	
	return m.db.WithContext(ctx).Exec(sql, valueArgs...).Error
}

// GetConnectionStats 获取连接池统计信息
func (m *MySQLDB) GetConnectionStats() map[string]interface{} {
	sqlDB, err := m.db.DB()
	if err != nil {
		return map[string]interface{}{"error": err.Error()}
	}

	stats := sqlDB.Stats()
	return map[string]interface{}{
		"max_open_connections": stats.MaxOpenConnections,
		"open_connections":     stats.OpenConnections,
		"in_use":              stats.InUse,
		"idle":                stats.Idle,
		"wait_count":          stats.WaitCount,
		"wait_duration":       stats.WaitDuration.String(),
		"max_idle_closed":     stats.MaxIdleClosed,
		"max_idle_time_closed": stats.MaxIdleTimeClosed,
		"max_lifetime_closed": stats.MaxLifetimeClosed,
	}
}

// GetCardRecord 获取卡记录
func (m *MySQLDB) GetCardRecord(ctx context.Context, requestID string) (*model.CardRecord, error) {
	var record model.CardRecord
	err := m.db.WithContext(ctx).Where("request_id = ?", requestID).First(&record).Error
	if err != nil {
		return nil, err
	}
	return &record, nil
}

// GetCardRecordsByStatus 根据状态获取卡记录
func (m *MySQLDB) GetCardRecordsByStatus(ctx context.Context, status string, limit int) ([]*model.CardRecord, error) {
	var records []*model.CardRecord
	err := m.db.WithContext(ctx).
		Where("status = ?", status).
		Order("created_at ASC").
		Limit(limit).
		Find(&records).Error
	
	if err != nil {
		return nil, err
	}
	return records, nil
}

// UpdateCardStatus 更新卡状态
func (m *MySQLDB) UpdateCardStatus(ctx context.Context, requestID string, status string) error {
	return m.db.WithContext(ctx).
		Model(&model.CardRecord{}).
		Where("request_id = ?", requestID).
		Update("status", status).Error
}

// BatchUpdateCardStatus 批量更新卡状态
func (m *MySQLDB) BatchUpdateCardStatus(ctx context.Context, updates map[string]string) error {
	if len(updates) == 0 {
		return nil
	}
	
	return m.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		for requestID, status := range updates {
			if err := tx.Model(&model.CardRecord{}).
				Where("request_id = ?", requestID).
				Update("status", status).Error; err != nil {
				return err
			}
		}
		return nil
	})
}
