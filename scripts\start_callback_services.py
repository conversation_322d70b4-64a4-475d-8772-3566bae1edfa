#!/usr/bin/env python3
"""
回调服务启动脚本
启动优化的回调服务和监控服务
"""
import asyncio
import signal
import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from app.core.logging import get_logger
from app.services.optimized_callback_service import optimized_callback_service
from app.services.callback_monitor_service import callback_monitor
from app.services.queue_consumer import start_callback_consumer

logger = get_logger("callback_services_startup")


class CallbackServicesManager:
    """回调服务管理器"""
    
    def __init__(self):
        self.services = []
        self.shutdown_event = asyncio.Event()
        self.running = False
    
    async def start_all_services(self):
        """启动所有回调相关服务"""
        logger.info("正在启动回调服务...")
        
        try:
            # 1. 启动回调监控服务
            logger.info("启动回调监控服务...")
            monitor_task = asyncio.create_task(
                callback_monitor.start_monitoring(interval_seconds=300)
            )
            self.services.append(("callback_monitor", monitor_task))
            
            # 2. 启动回调队列消费者
            logger.info("启动回调队列消费者...")
            consumer_task = asyncio.create_task(start_callback_consumer())
            self.services.append(("callback_consumer", consumer_task))
            
            self.running = True
            logger.info(f"所有回调服务已启动，共{len(self.services)}个服务")
            
            # 等待关闭信号
            await self.shutdown_event.wait()
            
        except Exception as e:
            logger.error(f"启动回调服务失败: {e}")
            raise
        finally:
            await self.shutdown_all_services()
    
    async def shutdown_all_services(self):
        """关闭所有服务"""
        if not self.running:
            return
        
        logger.info("正在关闭回调服务...")
        
        # 关闭监控服务
        try:
            await callback_monitor.stop_monitoring()
            logger.info("回调监控服务已关闭")
        except Exception as e:
            logger.error(f"关闭监控服务失败: {e}")
        
        # 关闭HTTP客户端
        try:
            await optimized_callback_service.close()
            logger.info("回调HTTP客户端已关闭")
        except Exception as e:
            logger.error(f"关闭HTTP客户端失败: {e}")
        
        # 取消所有任务
        for service_name, task in self.services:
            if not task.done():
                logger.info(f"正在取消服务: {service_name}")
                task.cancel()
                try:
                    await task
                except asyncio.CancelledError:
                    logger.info(f"服务已取消: {service_name}")
                except Exception as e:
                    logger.error(f"取消服务{service_name}时发生异常: {e}")
        
        self.services.clear()
        self.running = False
        logger.info("所有回调服务已关闭")
    
    def signal_handler(self, signum, frame):
        """信号处理器"""
        logger.info(f"收到信号 {signum}，准备关闭服务...")
        self.shutdown_event.set()


async def main():
    """主函数"""
    manager = CallbackServicesManager()
    
    # 注册信号处理器
    signal.signal(signal.SIGINT, manager.signal_handler)
    signal.signal(signal.SIGTERM, manager.signal_handler)
    
    try:
        await manager.start_all_services()
    except KeyboardInterrupt:
        logger.info("收到键盘中断信号")
    except Exception as e:
        logger.error(f"服务运行异常: {e}")
        sys.exit(1)
    
    logger.info("回调服务管理器退出")


if __name__ == "__main__":
    # 检查环境变量
    if not os.getenv("PYTHONPATH"):
        os.environ["PYTHONPATH"] = str(project_root)
    
    # 运行服务
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        logger.info("程序被用户中断")
    except Exception as e:
        logger.error(f"程序异常退出: {e}")
        sys.exit(1)
