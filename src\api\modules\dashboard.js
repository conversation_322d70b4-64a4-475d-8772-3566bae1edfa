import { http } from '@/api/request'
import { API_URLS, replaceUrlParams } from './config'

const { DASHBOARD } = API_URLS

/**
 * 仪表盘相关API - 重新设计版本
 */
export const dashboardApi = {
    // 获取整体统计数据 - 新版本
    getStatistics(params = {}) {
        return http.get(DASHBOARD.STATISTICS, { params }).then(res => res.data)
    },

    // 获取整体统计数据 - 旧版本（保持兼容性）
    getStatisticsLegacy(params = {}) {
        return http.get(DASHBOARD.STATISTICS_LEGACY, { params }).then(res => res.data)
    },

    // 获取绑卡金额统计
    getAmountStatistics(params = {}) {
        return http.get(DASHBOARD.AMOUNT_STATISTICS, { params }).then(res => res.data)
    },

    // 获取绑卡成功率统计
    getSuccessRateStatistics(params = {}) {
        return http.get(DASHBOARD.SUCCESS_RATE_STATISTICS, { params }).then(res => res.data)
    },

    // 获取CK使用效率统计
    getCkEfficiencyStatistics(params = {}) {
        return http.get(DASHBOARD.CK_EFFICIENCY_STATISTICS, { params }).then(res => res.data)
    },

    // 获取异常/失败统计
    getFailureStatistics(params = {}) {
        return http.get(DASHBOARD.FAILURE_STATISTICS, { params }).then(res => res.data)
    },

    // 获取部门业绩排名
    getDepartmentRanking(params = {}) {
        return http.get(DASHBOARD.DEPARTMENT_RANKING, { params }).then(res => res.data)
    },

    // 获取商家活跃度
    getMerchantActivity(params = {}) {
        return http.get(DASHBOARD.MERCHANT_ACTIVITY, { params }).then(res => res.data)
    },

    // 获取绑卡趋势
    getBindTrend(period, params = {}) {
        const url = replaceUrlParams(DASHBOARD.BIND_TREND, { period })
        return http.get(url, { params }).then(res => res.data)
    },

    // 获取系统状态
    getSystemStatus() {
        return http.get(DASHBOARD.SYSTEM_STATUS).then(res => res.data)
    },

    // 获取今日统计
    getTodayStats(params = {}) {
        return http.get(DASHBOARD.TODAY_STATS, { params }).then(res => res.data)
    },

    // 获取小时分布
    getHourlyDistribution(params = {}) {
        return http.get(DASHBOARD.HOURLY_DISTRIBUTION, { params }).then(res => res.data)
    },

    // 获取商家排名
    getMerchantRanking(params = {}) {
        return http.get(DASHBOARD.MERCHANT_RANKING, { params }).then(res => res.data)
    },

    // 获取时间分布
    getTimeDistribution(params = {}) {
        return http.get(DASHBOARD.TIME_DISTRIBUTION, { params }).then(res => res.data)
    },

    // 获取趋势数据
    getTrendData(params = {}) {
        return http.get(DASHBOARD.TREND, { params }).then(res => res.data)
    },

    // 获取概览数据
    getSummary(params = {}) {
        return http.get(DASHBOARD.SUMMARY, { params }).then(res => res.data)
    }
}


