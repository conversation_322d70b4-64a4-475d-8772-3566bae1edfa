package main

import (
	"context"
	"fmt"
	"log"
	"net/http"
	"os"
	"os/signal"
	"runtime"
	"syscall"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"

	"walmart-bind-card-gateway/internal/config"
	"walmart-bind-card-gateway/internal/handler"
	"walmart-bind-card-gateway/internal/repository"
	"walmart-bind-card-gateway/internal/service"
	"walmart-bind-card-gateway/pkg/database"
	"walmart-bind-card-gateway/pkg/queue"
	"walmart-bind-card-gateway/pkg/redis"
)

func main() {
	// 设置运行时参数
	runtime.GOMAXPROCS(runtime.NumCPU())

	// 加载配置
	cfg, err := config.LoadConfig("config.yaml")
	if err != nil {
		log.Fatal("配置加载失败:", err)
	}

	// 初始化日志
	logger, err := initLogger(cfg.Logging)
	if err != nil {
		log.Fatal("日志初始化失败:", err)
	}
	defer logger.Sync()

	logger.Info("启动沃尔玛绑卡网关服务",
		zap.String("version", cfg.API.Version),
		zap.String("mode", cfg.Server.Mode),
	)

	// 初始化数据库（使用现有数据库，不进行自动迁移）
	db, err := database.NewMySQLDB(&cfg.Database)
	if err != nil {
		logger.Fatal("数据库连接失败", zap.Error(err))
	}
	defer db.Close()
	logger.Info("数据库连接成功")

	// 初始化Redis
	redisClient, err := redis.NewRedisClient(&cfg.Redis)
	if err != nil {
		logger.Fatal("Redis连接失败", zap.Error(err))
	}
	defer redisClient.Close()
	logger.Info("Redis连接成功")

	// 初始化RabbitMQ
	mqPublisher, err := queue.NewRabbitMQPublisher(&cfg.RabbitMQ)
	if err != nil {
		logger.Fatal("RabbitMQ连接失败", zap.Error(err))
	}
	defer mqPublisher.Close()
	logger.Info("RabbitMQ连接成功")

	// 初始化仓储层
	cardRepo := repository.NewCardRepository(db)

	// 初始化服务层
	gatewayService := service.NewHighThroughputGateway(
		cfg,
		logger,
		cardRepo,
		redisClient,
		mqPublisher,
	)

	// 启动网关服务
	if err := gatewayService.Start(); err != nil {
		logger.Fatal("网关服务启动失败", zap.Error(err))
	}
	defer gatewayService.Stop()

	// 初始化商户仓储和服务
	merchantRepo := repository.NewMerchantRepository(db.GetDB())
	merchantService := service.NewMerchantService(merchantRepo)

	// 初始化卡记录服务
	cardService := service.NewCardService(db.GetDB())

	// 初始化HTTP处理器
	gatewayHandler := handler.NewGatewayHandler(gatewayService, merchantService, cardService, db, logger)

	// 设置路由
	router := setupRouter(cfg, gatewayHandler)

	// 创建HTTP服务器
	srv := &http.Server{
		Addr:           cfg.Server.GetServerAddr(),
		Handler:        router,
		ReadTimeout:    cfg.Server.ReadTimeout,
		WriteTimeout:   cfg.Server.WriteTimeout,
		IdleTimeout:    cfg.Server.IdleTimeout,
		MaxHeaderBytes: cfg.Server.MaxHeaderBytes,
	}

	// 启动HTTP服务器
	go func() {
		logger.Info("HTTP服务器启动",
			zap.String("addr", srv.Addr),
			zap.String("mode", cfg.Server.Mode),
		)

		if err := srv.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			logger.Fatal("HTTP服务器启动失败", zap.Error(err))
		}
	}()

	// 等待中断信号
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)
	<-quit

	logger.Info("正在关闭服务器...")

	// 优雅关闭HTTP服务器
	ctx, cancel := context.WithTimeout(context.Background(), cfg.Concurrency.GracefulShutdownTimeout)
	defer cancel()

	if err := srv.Shutdown(ctx); err != nil {
		logger.Error("服务器强制关闭", zap.Error(err))
	} else {
		logger.Info("服务器优雅关闭完成")
	}
}

// initLogger 初始化日志
func initLogger(cfg config.LoggingConfig) (*zap.Logger, error) {
	var zapConfig zap.Config

	if cfg.Format == "json" {
		zapConfig = zap.NewProductionConfig()
	} else {
		zapConfig = zap.NewDevelopmentConfig()
	}

	// 设置日志级别
	level, err := zap.ParseAtomicLevel(cfg.Level)
	if err != nil {
		return nil, fmt.Errorf("无效的日志级别: %s", cfg.Level)
	}
	zapConfig.Level = level

	// 添加字段
	logger, err := zapConfig.Build()
	if err != nil {
		return nil, err
	}

	// 添加服务信息
	if cfg.Fields != nil {
		fields := make([]zap.Field, 0, len(cfg.Fields))
		for key, value := range cfg.Fields {
			fields = append(fields, zap.String(key, value))
		}
		logger = logger.With(fields...)
	}

	return logger, nil
}

// setupRouter 设置路由
func setupRouter(cfg *config.Config, handler *handler.GatewayHandler) *gin.Engine {
	// 设置Gin模式
	gin.SetMode(cfg.Server.Mode)

	// 创建路由器
	r := gin.New()

	// 添加中间件
	r.Use(handler.Recovery())
	r.Use(handler.RequestLogger())
	r.Use(handler.FastCORS())

	// 全局限流（如果启用）
	if cfg.API.RateLimit.Enabled {
		r.Use(handler.FastRateLimit())
	}

	// 基础路由
	r.GET("/ping", handler.Ping)
	r.GET("/health", handler.Health)

	// API路由组
	v1 := r.Group(cfg.API.Prefix)
	{
		// 绑卡接口
		v1.POST("/card-bind", handler.BindCard)

		// 状态查询接口
		v1.GET("/card-bind/:request_id/status", handler.GetStatus)
		v1.POST("/card-bind/batch-status", handler.BatchStatus)

		// 监控接口
		v1.GET("/metrics", handler.Metrics)
		v1.GET("/stats", handler.Stats)
		v1.GET("/stats/db", handler.DBStats)
	}

	return r
}