"""
综合安全测试套件
验证所有修复的服务和API的数据隔离机制
"""

import pytest
from sqlalchemy.orm import Session
from app.models.user import User
from app.models.merchant import Merchant
from app.models.department import Department
from app.models.card_record import CardRecord, CardStatus
from app.models.walmart_ck import WalmartCK
from app.models.binding_log import BindingLog
from app.models.audit_log import AuditLog

# 导入所有需要测试的服务
from app.services.user_service import UserService
from app.services.merchant_service import MerchantService
from app.services.department_service_new import DepartmentService
from app.services.card_record_service import CardRecordService
from app.services.walmart_ck_service_new import WalmartCKService
from app.services.binding_log_service import BindingLogService
from app.services.audit_service import AuditService
from app.services.dashboard_statistics_service import DashboardStatisticsService


class TestComprehensiveSecurity:
    """综合安全测试"""

    @pytest.fixture
    def setup_comprehensive_test_data(self, db: Session):
        """设置综合测试数据"""
        # 创建两个商户
        merchant1 = Merchant(
            name="安全测试商户1",
            code="SECURITY_TEST_1",
            api_key="security_key_1",
            api_secret="security_secret_1"
        )
        merchant2 = Merchant(
            name="安全测试商户2", 
            code="SECURITY_TEST_2",
            api_key="security_key_2",
            api_secret="security_secret_2"
        )
        db.add_all([merchant1, merchant2])
        db.commit()
        db.refresh(merchant1)
        db.refresh(merchant2)

        # 创建部门
        dept1 = Department(
            name="安全测试部门1",
            merchant_id=merchant1.id,
            code="SEC_DEPT_1"
        )
        dept2 = Department(
            name="安全测试部门2",
            merchant_id=merchant2.id,
            code="SEC_DEPT_2"
        )
        db.add_all([dept1, dept2])
        db.commit()
        db.refresh(dept1)
        db.refresh(dept2)

        # 创建用户
        user1 = User(
            username="security_user_1",
            email="<EMAIL>",
            hashed_password="hashed_password",
            merchant_id=merchant1.id,
            department_id=dept1.id,
            is_superuser=False
        )
        user2 = User(
            username="security_user_2",
            email="<EMAIL>", 
            hashed_password="hashed_password",
            merchant_id=merchant2.id,
            department_id=dept2.id,
            is_superuser=False
        )
        superuser = User(
            username="security_superuser",
            email="<EMAIL>",
            hashed_password="hashed_password",
            is_superuser=True
        )
        db.add_all([user1, user2, superuser])
        db.commit()
        db.refresh(user1)
        db.refresh(user2)
        db.refresh(superuser)

        # 创建CK
        ck1 = WalmartCK(
            sign="security_ck_1",
            merchant_id=merchant1.id,
            department_id=dept1.id,
            total_limit=100,
            active=True
        )
        ck2 = WalmartCK(
            sign="security_ck_2",
            merchant_id=merchant2.id,
            department_id=dept2.id,
            total_limit=100,
            active=True
        )
        db.add_all([ck1, ck2])
        db.commit()
        db.refresh(ck1)
        db.refresh(ck2)

        # 创建卡记录
        card1 = CardRecord(
            id="security_card_1",
            card_number="1111222233334444",
            merchant_id=merchant1.id,
            department_id=dept1.id,
            status=CardStatus.SUCCESS,
            amount=10000
        )
        card2 = CardRecord(
            id="security_card_2",
            card_number="****************",
            merchant_id=merchant2.id,
            department_id=dept2.id,
            status=CardStatus.SUCCESS,
            amount=20000
        )
        db.add_all([card1, card2])
        db.commit()

        return {
            "merchant1": merchant1,
            "merchant2": merchant2,
            "dept1": dept1,
            "dept2": dept2,
            "user1": user1,
            "user2": user2,
            "superuser": superuser,
            "ck1": ck1,
            "ck2": ck2,
            "card1": card1,
            "card2": card2
        }

    def test_all_services_data_isolation(self, db: Session, setup_comprehensive_test_data):
        """测试所有服务的数据隔离"""
        data = setup_comprehensive_test_data
        user1 = data["user1"]
        user2 = data["user2"]

        # 测试所有服务
        services = [
            (UserService(db), User),
            (MerchantService(db), Merchant),
            (DepartmentService(db), Department),
            (CardRecordService(db), CardRecord),
            (WalmartCKService(db), WalmartCK),
            (BindingLogService(db), BindingLog),
            (AuditService(db), AuditLog),
        ]

        for service, model in services:
            # 用户1的查询
            query1 = db.query(model)
            isolated_query1 = service.apply_data_isolation(query1, user1)
            results1 = isolated_query1.all()

            # 用户2的查询
            query2 = db.query(model)
            isolated_query2 = service.apply_data_isolation(query2, user2)
            results2 = isolated_query2.all()

            # 验证数据隔离
            if hasattr(model, 'merchant_id'):
                # 检查用户1的结果
                for result in results1:
                    if hasattr(result, 'merchant_id') and result.merchant_id is not None:
                        assert result.merchant_id == user1.merchant_id, f"{service.__class__.__name__}: 用户1看到了其他商户的数据"

                # 检查用户2的结果
                for result in results2:
                    if hasattr(result, 'merchant_id') and result.merchant_id is not None:
                        assert result.merchant_id == user2.merchant_id, f"{service.__class__.__name__}: 用户2看到了其他商户的数据"

    def test_cross_merchant_access_prevention(self, db: Session, setup_comprehensive_test_data):
        """测试跨商户访问防护"""
        data = setup_comprehensive_test_data
        user1 = data["user1"]
        user2 = data["user2"]

        # 测试各种服务的get_with_isolation方法
        services_and_ids = [
            (UserService(db), user2.id),
            (MerchantService(db), data["merchant2"].id),
            (DepartmentService(db), data["dept2"].id),
            (CardRecordService(db), data["card2"].id),
            (WalmartCKService(db), data["ck2"].id),
        ]

        for service, target_id in services_and_ids:
            # 用户1尝试访问用户2商户的资源（应该失败）
            result = service.get_with_isolation(target_id, user1)
            assert result is None, f"{service.__class__.__name__}: 用户1不应该能访问其他商户的资源"

    def test_superuser_access_all_data(self, db: Session, setup_comprehensive_test_data):
        """测试超级管理员可以访问所有数据"""
        data = setup_comprehensive_test_data
        superuser = data["superuser"]

        # 测试所有服务
        services = [
            (UserService(db), User),
            (MerchantService(db), Merchant),
            (DepartmentService(db), Department),
            (CardRecordService(db), CardRecord),
            (WalmartCKService(db), WalmartCK),
        ]

        for service, model in services:
            # 超级管理员的查询
            query = db.query(model)
            isolated_query = service.apply_data_isolation(query, superuser)
            results = isolated_query.all()

            # 超级管理员应该能看到所有数据（至少包含测试数据）
            if model == User:
                assert len(results) >= 3, f"{service.__class__.__name__}: 超级管理员应该能看到所有用户"
            elif model == Merchant:
                assert len(results) >= 2, f"{service.__class__.__name__}: 超级管理员应该能看到所有商户"
            elif model in [Department, CardRecord, WalmartCK]:
                assert len(results) >= 2, f"{service.__class__.__name__}: 超级管理员应该能看到所有{model.__name__}"

    def test_dashboard_statistics_security(self, db: Session, setup_comprehensive_test_data):
        """测试仪表盘统计的安全性"""
        data = setup_comprehensive_test_data
        user1 = data["user1"]
        user2 = data["user2"]
        
        dashboard_service = DashboardStatisticsService(db)

        # 测试安全的商户ID获取
        safe_id1 = dashboard_service._get_safe_merchant_id(user1, None)
        assert safe_id1 == user1.merchant_id, "用户1应该得到自己的商户ID"

        safe_id2 = dashboard_service._get_safe_merchant_id(user2, None)
        assert safe_id2 == user2.merchant_id, "用户2应该得到自己的商户ID"

        # 测试跨商户访问防护
        safe_id_cross = dashboard_service._get_safe_merchant_id(user1, user2.merchant_id)
        assert safe_id_cross == user1.merchant_id, "用户1不应该能指定其他商户ID"

    def test_security_logging_and_audit(self, db: Session, setup_comprehensive_test_data):
        """测试安全日志和审计"""
        data = setup_comprehensive_test_data
        user1 = data["user1"]
        
        audit_service = AuditService(db)

        # 记录一个安全事件
        await audit_service.log_operation(
            db=db,
            user_id=user1.id,
            operation="SECURITY_TEST",
            resource_type="test_resource",
            details={"test": "security_audit"}
        )

        # 验证审计日志的数据隔离
        query = db.query(AuditLog)
        isolated_query = audit_service.apply_data_isolation(query, user1)
        audit_logs = isolated_query.all()

        # 用户1应该只能看到自己商户的审计日志
        for log in audit_logs:
            if log.merchant_id is not None:
                assert log.merchant_id == user1.merchant_id, "审计日志应该应用商户隔离"
