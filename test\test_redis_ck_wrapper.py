#!/usr/bin/env python3
"""
Redis CK包装器测试
使用现有Redis配置的简化测试
"""

import sys
import os
import asyncio
import json
import time
from datetime import datetime
from typing import Dict, Any, List

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app.db.session import SessionLocal
from app.models.walmart_ck import WalmartCK
from app.models.merchant import Merchant
from app.models.department import Department
from app.services.redis_ck_wrapper import RedisOptimizedCKWrapper, redis_health_monitor
from app.core.logging import get_logger

logger = get_logger("redis_ck_wrapper_test")


class RedisCKWrapperTest:
    """Redis CK包装器测试类"""
    
    def __init__(self):
        self.db = SessionLocal()
        self.test_data = {}
        self.test_results = []
    
    def __enter__(self):
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        self.cleanup()
        self.db.close()
    
    def cleanup(self):
        """清理测试数据"""
        try:
            # 删除测试创建的CK
            if 'test_cks' in self.test_data:
                for ck_id in self.test_data['test_cks']:
                    ck = self.db.query(WalmartCK).filter(WalmartCK.id == ck_id).first()
                    if ck:
                        self.db.delete(ck)
            
            # 删除测试部门
            if 'test_department' in self.test_data:
                dept = self.db.query(Department).filter(Department.id == self.test_data['test_department']).first()
                if dept:
                    self.db.delete(dept)
            
            # 删除测试商户
            if 'test_merchant' in self.test_data:
                merchant = self.db.query(Merchant).filter(Merchant.id == self.test_data['test_merchant']).first()
                if merchant:
                    self.db.delete(merchant)
            
            self.db.commit()
            logger.info("测试数据清理完成")
            
        except Exception as e:
            self.db.rollback()
            logger.error(f"清理测试数据失败: {e}")
    
    def create_test_data(self):
        """创建测试数据"""
        try:
            # 创建测试商户
            merchant = Merchant(
                name="Redis包装器测试商户",
                code="REDIS_WRAPPER_TEST",
                status="active"
            )
            self.db.add(merchant)
            self.db.commit()
            self.db.refresh(merchant)
            self.test_data['test_merchant'] = merchant.id
            
            # 创建测试部门
            department = Department(
                name="Redis包装器测试部门",
                code="REDIS_WRAPPER_DEPT",
                merchant_id=merchant.id,
                status="active"
            )
            self.db.add(department)
            self.db.commit()
            self.db.refresh(department)
            self.test_data['test_department'] = department.id
            
            # 创建测试CK
            test_cks = []
            for i in range(5):
                ck = WalmartCK(
                    sign=f"wrapper_test_ck_{i}_{datetime.now().timestamp()}",
                    total_limit=50,
                    bind_count=i * 2,
                    active=True,
                    merchant_id=merchant.id,
                    department_id=department.id,
                    is_deleted=False,
                    description=f"包装器测试CK_{i}"
                )
                self.db.add(ck)
                test_cks.append(ck)
            
            self.db.commit()
            
            # 刷新并保存CK ID
            self.test_data['test_cks'] = []
            for ck in test_cks:
                self.db.refresh(ck)
                self.test_data['test_cks'].append(ck.id)
            
            logger.info(f"创建测试数据完成: 商户{merchant.id}, 部门{department.id}, CK数量{len(test_cks)}")
            return True
            
        except Exception as e:
            self.db.rollback()
            logger.error(f"创建测试数据失败: {e}")
            return False
    
    async def test_redis_health(self):
        """测试Redis健康状态"""
        logger.info("开始Redis健康检查测试...")
        
        try:
            # 检查Redis连接
            is_healthy = await redis_health_monitor.check_redis_health()
            
            # 获取Redis信息
            redis_info = await redis_health_monitor.get_redis_info()
            
            test_result = {
                'test_name': 'redis_health_check',
                'redis_healthy': is_healthy,
                'redis_info': redis_info,
                'passed': is_healthy
            }
            
            self.test_results.append(test_result)
            
            logger.info(f"Redis健康检查完成 - 状态: {'健康' if is_healthy else '不健康'}")
            return test_result
            
        except Exception as e:
            logger.error(f"Redis健康检查失败: {e}")
            test_result = {
                'test_name': 'redis_health_check',
                'passed': False,
                'error': str(e)
            }
            self.test_results.append(test_result)
            return test_result
    
    async def test_wrapper_initialization(self):
        """测试包装器初始化"""
        logger.info("开始包装器初始化测试...")
        
        try:
            # 创建包装器服务
            wrapper = RedisOptimizedCKWrapper(self.db)
            
            # 检查健康状态
            health_status = await wrapper.health_check()
            
            # 尝试同步数据
            await wrapper.sync_ck_to_redis(self.test_data['test_merchant'])
            
            test_result = {
                'test_name': 'wrapper_initialization',
                'health_status': health_status,
                'redis_enabled': wrapper.is_redis_enabled(),
                'passed': True
            }
            
            self.test_results.append(test_result)
            
            logger.info(f"包装器初始化测试完成 - Redis启用: {wrapper.is_redis_enabled()}")
            return test_result
            
        except Exception as e:
            logger.error(f"包装器初始化测试失败: {e}")
            test_result = {
                'test_name': 'wrapper_initialization',
                'passed': False,
                'error': str(e)
            }
            self.test_results.append(test_result)
            return test_result
    
    async def test_ck_selection_performance(self):
        """测试CK选择性能"""
        logger.info("开始CK选择性能测试...")
        
        try:
            wrapper = RedisOptimizedCKWrapper(self.db)
            
            # 同步数据到Redis
            await wrapper.sync_ck_to_redis(self.test_data['test_merchant'])
            
            # 测试多次CK选择
            selection_times = []
            selected_cks = []
            
            for i in range(20):
                start_time = time.time()
                
                ck = await wrapper.get_available_ck(
                    self.test_data['test_merchant'],
                    self.test_data['test_department']
                )
                
                end_time = time.time()
                selection_time = (end_time - start_time) * 1000  # 转换为毫秒
                
                selection_times.append(selection_time)
                if ck:
                    selected_cks.append(ck.id)
                    # 记录使用
                    await wrapper.record_ck_usage(ck.id, True)
            
            # 计算统计数据
            avg_time = sum(selection_times) / len(selection_times)
            max_time = max(selection_times)
            min_time = min(selection_times)
            
            # 分析CK分布
            ck_usage = {}
            for ck_id in selected_cks:
                ck_usage[ck_id] = ck_usage.get(ck_id, 0) + 1
            
            test_result = {
                'test_name': 'ck_selection_performance',
                'total_selections': len(selection_times),
                'successful_selections': len(selected_cks),
                'avg_response_time_ms': avg_time,
                'max_response_time_ms': max_time,
                'min_response_time_ms': min_time,
                'ck_distribution': ck_usage,
                'unique_cks_used': len(ck_usage),
                'passed': len(selected_cks) > 0 and avg_time < 100  # 期望平均响应时间<100ms
            }
            
            self.test_results.append(test_result)
            
            logger.info(f"CK选择性能测试完成 - 平均响应时间: {avg_time:.2f}ms, "
                       f"成功率: {len(selected_cks)/len(selection_times)*100:.1f}%")
            
            return test_result
            
        except Exception as e:
            logger.error(f"CK选择性能测试失败: {e}")
            test_result = {
                'test_name': 'ck_selection_performance',
                'passed': False,
                'error': str(e)
            }
            self.test_results.append(test_result)
            return test_result
    
    async def test_fallback_mechanism(self):
        """测试降级机制"""
        logger.info("开始降级机制测试...")
        
        try:
            wrapper = RedisOptimizedCKWrapper(self.db)
            
            # 强制禁用Redis
            wrapper.use_redis = False
            
            # 测试CK选择（应该降级到数据库）
            ck = await wrapper.get_available_ck(
                self.test_data['test_merchant'],
                self.test_data['test_department']
            )
            
            # 测试使用记录
            if ck:
                result = await wrapper.record_ck_usage(ck.id, True)
            else:
                result = False
            
            test_result = {
                'test_name': 'fallback_mechanism',
                'ck_selected': ck is not None,
                'usage_recorded': result,
                'redis_enabled': wrapper.is_redis_enabled(),
                'passed': ck is not None  # 即使Redis禁用，也应该能选择CK
            }
            
            self.test_results.append(test_result)
            
            logger.info(f"降级机制测试完成 - CK选择: {'成功' if ck else '失败'}, "
                       f"Redis状态: {'启用' if wrapper.is_redis_enabled() else '禁用'}")
            
            return test_result
            
        except Exception as e:
            logger.error(f"降级机制测试失败: {e}")
            test_result = {
                'test_name': 'fallback_mechanism',
                'passed': False,
                'error': str(e)
            }
            self.test_results.append(test_result)
            return test_result
    
    def generate_test_report(self):
        """生成测试报告"""
        total_tests = len(self.test_results)
        passed_tests = sum(1 for r in self.test_results if r.get('passed', False))
        
        report = {
            "test_summary": {
                "total_tests": total_tests,
                "passed_tests": passed_tests,
                "failed_tests": total_tests - passed_tests,
                "success_rate": f"{passed_tests/total_tests*100:.1f}%" if total_tests > 0 else "0%"
            },
            "test_details": self.test_results,
            "test_data": self.test_data,
            "timestamp": datetime.now().isoformat()
        }
        
        return report
    
    async def run_all_tests(self):
        """运行所有测试"""
        logger.info("开始Redis CK包装器测试...")
        
        # 创建测试数据
        if not self.create_test_data():
            logger.error("创建测试数据失败，终止测试")
            return False
        
        # 运行各项测试
        tests = [
            ("Redis健康检查", self.test_redis_health),
            ("包装器初始化", self.test_wrapper_initialization),
            ("CK选择性能", self.test_ck_selection_performance),
            ("降级机制", self.test_fallback_mechanism),
        ]
        
        for test_name, test_func in tests:
            logger.info(f"执行测试: {test_name}")
            try:
                await test_func()
            except Exception as e:
                logger.error(f"测试 {test_name} 执行异常: {e}")
        
        # 生成测试报告
        report = self.generate_test_report()
        
        # 输出测试结果
        print("\n" + "="*60)
        print("Redis CK包装器测试报告")
        print("="*60)
        print(f"总测试数: {report['test_summary']['total_tests']}")
        print(f"通过测试: {report['test_summary']['passed_tests']}")
        print(f"失败测试: {report['test_summary']['failed_tests']}")
        print(f"成功率: {report['test_summary']['success_rate']}")
        print("="*60)
        
        for result in report['test_details']:
            status = "✅ 通过" if result.get('passed', False) else "❌ 失败"
            print(f"{result['test_name']}: {status}")
            if not result.get('passed', False) and 'error' in result:
                print(f"  错误: {result['error']}")
        
        print("="*60)
        
        # 保存详细报告
        report_file = f"redis_wrapper_test_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2)
        
        logger.info(f"详细测试报告已保存到: {report_file}")
        
        return report['test_summary']['failed_tests'] == 0


async def main():
    """主函数"""
    with RedisCKWrapperTest() as test:
        success = await test.run_all_tests()
        return 0 if success else 1


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
