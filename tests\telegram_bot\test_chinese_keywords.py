"""
Telegram机器人中文关键词功能测试
"""

import pytest
import asyncio
from unittest.mock import Mock, AsyncMock, patch
from telegram import Update, Message, Chat, User
from telegram.ext import ContextTypes

from app.telegram_bot.command_handlers.help_handler import HelpCommandHandler
from app.telegram_bot.config import BotConfig
from app.telegram_bot.rate_limiter import RateLimiter
from app.models.telegram_group import TelegramGroup, BindStatus
from app.models.telegram_user import TelegramUser, VerificationStatus


class TestChineseKeywords:
    """中文关键词功能测试类"""
    
    @pytest.fixture
    def mock_db(self):
        """模拟数据库会话"""
        return Mock()
    
    @pytest.fixture
    def mock_config(self):
        """模拟配置"""
        config = BotConfig()
        config.default_language = "zh-CN"
        return config
    
    @pytest.fixture
    def mock_rate_limiter(self):
        """模拟频率限制器"""
        rate_limiter = Mock(spec=RateLimiter)
        rate_limiter.check_all_limits = Mock()
        return rate_limiter
    
    @pytest.fixture
    def help_handler(self, mock_db, mock_config, mock_rate_limiter):
        """创建帮助处理器实例"""
        handler = HelpCommandHandler(mock_db, mock_config, mock_rate_limiter)
        handler.send_response = AsyncMock()
        handler.get_telegram_group = AsyncMock()
        handler.get_telegram_user = AsyncMock()
        handler.execute_command = AsyncMock()
        return handler
    
    @pytest.fixture
    def mock_update(self):
        """创建模拟的Telegram更新对象"""
        update = Mock(spec=Update)
        update.message = Mock(spec=Message)
        update.effective_chat = Mock(spec=Chat)
        update.effective_user = Mock(spec=User)
        
        # 设置基本属性
        update.effective_chat.id = 12345
        update.effective_chat.type = 'group'
        update.effective_chat.title = '测试群组'
        update.effective_user.id = 67890
        update.effective_user.username = 'testuser'
        update.effective_user.first_name = '测试'
        update.effective_user.last_name = '用户'
        
        return update
    
    @pytest.fixture
    def mock_context(self):
        """创建模拟的上下文对象"""
        return Mock(spec=ContextTypes.DEFAULT_TYPE)
    
    @pytest.mark.asyncio
    async def test_help_keywords_chinese(self, help_handler, mock_update, mock_context):
        """测试中文帮助关键词"""
        test_cases = [
            "帮助",
            "help", 
            "使用说明",
            "怎么用",
            "命令",
            "指南",
            "教程"
        ]
        
        for keyword in test_cases:
            mock_update.message.text = keyword
            await help_handler.handle_message(mock_update, mock_context)
            help_handler.execute_command.assert_called()
            help_handler.execute_command.reset_mock()
    
    @pytest.mark.asyncio
    async def test_status_keywords_chinese(self, help_handler, mock_update, mock_context):
        """测试中文状态关键词"""
        test_cases = [
            "状态",
            "status",
            "绑定状态", 
            "群组状态",
            "当前状态",
            "查看状态"
        ]
        
        # 模拟绑定处理器
        with patch('app.telegram_bot.command_handlers.help_handler.BindCommandHandler') as mock_bind_handler_class:
            mock_bind_handler = Mock()
            mock_bind_handler.handle_status = AsyncMock()
            mock_bind_handler_class.return_value = mock_bind_handler
            
            for keyword in test_cases:
                mock_update.message.text = keyword
                await help_handler.handle_message(mock_update, mock_context)
                mock_bind_handler.handle_status.assert_called_with(mock_update, mock_context)
                mock_bind_handler.handle_status.reset_mock()
    
    @pytest.mark.asyncio
    async def test_stats_keywords_chinese_unbound_group(self, help_handler, mock_update, mock_context):
        """测试统计关键词 - 未绑定群组"""
        # 模拟未绑定的群组
        help_handler.get_telegram_group.return_value = None
        
        test_cases = [
            "统计",
            "数据", 
            "绑卡",
            "今日",
            "本周",
            "本月",
            "查询",
            "报表"
        ]
        
        for keyword in test_cases:
            mock_update.message.text = keyword
            await help_handler.handle_message(mock_update, mock_context)
            help_handler.send_response.assert_called()
            
            # 检查响应内容是否包含未绑定提示
            call_args = help_handler.send_response.call_args
            response_text = call_args[0][1]
            assert "群组未绑定" in response_text
            help_handler.send_response.reset_mock()
    
    @pytest.mark.asyncio
    async def test_stats_keywords_chinese_bound_group_unverified_user(self, help_handler, mock_update, mock_context):
        """测试统计关键词 - 已绑定群组但用户未验证"""
        # 模拟已绑定的群组
        mock_group = Mock(spec=TelegramGroup)
        mock_group.is_active.return_value = True
        help_handler.get_telegram_group.return_value = mock_group
        
        # 模拟未验证的用户
        help_handler.get_telegram_user.return_value = None
        
        mock_update.message.text = "统计"
        await help_handler.handle_message(mock_update, mock_context)
        
        help_handler.send_response.assert_called()
        call_args = help_handler.send_response.call_args
        response_text = call_args[0][1]
        assert "需要身份验证" in response_text
    
    @pytest.mark.asyncio
    async def test_stats_keywords_chinese_smart_query(self, help_handler, mock_update, mock_context):
        """测试统计关键词智能查询"""
        # 模拟已绑定的群组和已验证的用户
        mock_group = Mock(spec=TelegramGroup)
        mock_group.is_active.return_value = True
        help_handler.get_telegram_group.return_value = mock_group
        
        mock_user = Mock(spec=TelegramUser)
        mock_user.is_verified.return_value = True
        help_handler.get_telegram_user.return_value = mock_user
        
        # 模拟统计处理器
        with patch('app.telegram_bot.command_handlers.help_handler.StatsCommandHandler') as mock_stats_handler_class:
            mock_stats_handler = Mock()
            mock_stats_handler.handle = AsyncMock()
            mock_stats_handler.handle_week = AsyncMock()
            mock_stats_handler.handle_month = AsyncMock()
            mock_stats_handler_class.return_value = mock_stats_handler
            
            # 测试今日关键词
            test_cases = [
                ("今日", "handle"),
                ("今天", "handle"),
                ("本周", "handle_week"),
                ("这周", "handle_week"),
                ("本月", "handle_month"),
                ("这月", "handle_month")
            ]
            
            for keyword, expected_method in test_cases:
                mock_update.message.text = keyword
                await help_handler.handle_message(mock_update, mock_context)
                
                method = getattr(mock_stats_handler, expected_method)
                method.assert_called_with(mock_update, mock_context)
                method.reset_mock()
    
    @pytest.mark.asyncio
    async def test_bind_keywords_chinese(self, help_handler, mock_update, mock_context):
        """测试中文绑定关键词"""
        test_cases = [
            ("绑定", False),  # 未绑定
            ("bind", False),
            ("绑卡", False),
            ("连接", False),
            ("关联", False)
        ]
        
        for keyword, is_bound in test_cases:
            if is_bound:
                mock_group = Mock(spec=TelegramGroup)
                mock_group.is_active.return_value = True
                mock_group.merchant = Mock()
                mock_group.merchant.name = "测试商户"
                help_handler.get_telegram_group.return_value = mock_group
            else:
                help_handler.get_telegram_group.return_value = None
            
            mock_update.message.text = keyword
            await help_handler.handle_message(mock_update, mock_context)
            
            help_handler.send_response.assert_called()
            call_args = help_handler.send_response.call_args
            response_text = call_args[0][1]
            
            if is_bound:
                assert "群组已绑定" in response_text
            else:
                assert "群组绑定指南" in response_text
            
            help_handler.send_response.reset_mock()
    
    @pytest.mark.asyncio
    async def test_verify_keywords_chinese(self, help_handler, mock_update, mock_context):
        """测试中文验证关键词"""
        test_cases = [
            "验证",
            "verify",
            "身份验证",
            "认证",
            "授权"
        ]
        
        # 测试未验证用户
        help_handler.get_telegram_user.return_value = None
        
        for keyword in test_cases:
            mock_update.message.text = keyword
            await help_handler.handle_message(mock_update, mock_context)
            
            help_handler.send_response.assert_called()
            call_args = help_handler.send_response.call_args
            response_text = call_args[0][1]
            assert "身份验证指南" in response_text
            help_handler.send_response.reset_mock()
        
        # 测试已验证用户
        mock_user = Mock(spec=TelegramUser)
        mock_user.is_verified.return_value = True
        help_handler.get_telegram_user.return_value = mock_user
        
        mock_update.message.text = "验证"
        await help_handler.handle_message(mock_update, mock_context)
        
        help_handler.send_response.assert_called()
        call_args = help_handler.send_response.call_args
        response_text = call_args[0][1]
        assert "身份已验证" in response_text
    
    @pytest.mark.asyncio
    async def test_no_keyword_match(self, help_handler, mock_update, mock_context):
        """测试无关键词匹配的消息"""
        # 测试普通聊天消息不会触发任何响应
        mock_update.message.text = "这是一条普通的聊天消息"
        await help_handler.handle_message(mock_update, mock_context)
        
        # 确保没有调用任何响应方法
        help_handler.send_response.assert_not_called()
        help_handler.execute_command.assert_not_called()
    
    @pytest.mark.asyncio
    async def test_empty_message(self, help_handler, mock_update, mock_context):
        """测试空消息处理"""
        mock_update.message.text = ""
        await help_handler.handle_message(mock_update, mock_context)
        
        # 确保没有调用任何响应方法
        help_handler.send_response.assert_not_called()
        help_handler.execute_command.assert_not_called()
    
    @pytest.mark.asyncio
    async def test_case_insensitive_matching(self, help_handler, mock_update, mock_context):
        """测试大小写不敏感匹配"""
        test_cases = [
            "帮助",
            "HELP",
            "Help",
            "状态",
            "STATUS",
            "Status"
        ]
        
        for keyword in test_cases:
            mock_update.message.text = keyword
            await help_handler.handle_message(mock_update, mock_context)
            
            # 应该有响应（帮助或状态）
            assert (help_handler.execute_command.called or 
                   help_handler.send_response.called)
            
            # 重置mock
            help_handler.execute_command.reset_mock()
            help_handler.send_response.reset_mock()


if __name__ == "__main__":
    # 运行测试
    pytest.main([__file__, "-v"])
