#!/usr/bin/env python3
"""
Telegram Bot 启动脚本
用于安全启动机器人服务，避免多实例冲突
"""

import os
import sys
import asyncio
import argparse
from pathlib import Path

# 添加项目根目录到Python路径
ROOT_DIR = Path(__file__).parent
sys.path.insert(0, str(ROOT_DIR))

from app.core.logging import get_logger
from app.telegram_bot.utils.process_manager import ProcessManager, check_and_resolve_conflicts

logger = get_logger(__name__)


class BotStarter:
    """机器人启动器"""
    
    def __init__(self):
        self.pid_file = ROOT_DIR / "telegram_bot.pid"
        
    def check_existing_instance(self) -> bool:
        """检查是否已有实例运行"""
        if not self.pid_file.exists():
            return False
            
        try:
            with open(self.pid_file, 'r') as f:
                pid = int(f.read().strip())
            
            # 检查进程是否还在运行
            import psutil
            try:
                process = psutil.Process(pid)
                if process.is_running():
                    logger.error(f"检测到已有机器人实例运行 (PID: {pid})")
                    return True
            except psutil.NoSuchProcess:
                # 进程已不存在，删除PID文件
                self.pid_file.unlink()
                return False
                
        except (ValueError, FileNotFoundError):
            # PID文件损坏或不存在
            if self.pid_file.exists():
                self.pid_file.unlink()
            return False
            
        return False
    
    def create_pid_file(self) -> bool:
        """创建PID文件"""
        try:
            current_pid = os.getpid()
            with open(self.pid_file, 'w') as f:
                f.write(str(current_pid))
            logger.info(f"PID文件已创建: {self.pid_file} (PID: {current_pid})")
            return True
        except Exception as e:
            logger.error(f"创建PID文件失败: {e}")
            return False
    
    def cleanup_pid_file(self):
        """清理PID文件"""
        try:
            if self.pid_file.exists():
                self.pid_file.unlink()
                logger.info("PID文件已清理")
        except Exception as e:
            logger.error(f"清理PID文件失败: {e}")
    
    async def start_bot_service(self) -> bool:
        """启动机器人服务"""
        try:
            from app.telegram_bot.main import TelegramBotApp
            
            # 创建应用实例
            bot_app = TelegramBotApp()
            
            # 设置清理回调
            def cleanup():
                logger.info("正在清理资源...")
                self.cleanup_pid_file()
                asyncio.create_task(bot_app.shutdown())
            
            # 设置信号处理器
            ProcessManager.setup_signal_handlers(cleanup)
            
            # 启动应用
            await bot_app.startup()
            
            logger.info("机器人服务启动成功，按 Ctrl+C 停止")
            
            # 保持运行
            try:
                while True:
                    await asyncio.sleep(1)
            except KeyboardInterrupt:
                logger.info("收到停止信号")
            finally:
                await bot_app.shutdown()
                cleanup()
            
            return True
            
        except Exception as e:
            logger.error(f"启动机器人服务失败: {e}", exc_info=True)
            return False
    
    async def start(self, force: bool = False, check_conflicts: bool = True) -> bool:
        """启动机器人"""
        try:
            # 检查现有实例
            if not force and self.check_existing_instance():
                logger.error("已有机器人实例运行，使用 --force 参数强制启动")
                return False
            
            # 检查进程冲突
            if check_conflicts:
                logger.info("检查进程冲突...")
                if not check_and_resolve_conflicts(auto_resolve=True):
                    if not force:
                        logger.error("检测到进程冲突，使用 --force 参数强制启动")
                        return False
                    else:
                        logger.warning("强制启动模式，忽略进程冲突")
            
            # 创建PID文件
            if not self.create_pid_file():
                logger.error("无法创建PID文件")
                return False
            
            # 启动机器人服务
            return await self.start_bot_service()
            
        except Exception as e:
            logger.error(f"启动失败: {e}", exc_info=True)
            self.cleanup_pid_file()
            return False


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="Telegram Bot 启动器")
    parser.add_argument("--force", action="store_true", help="强制启动，忽略现有实例检查")
    parser.add_argument("--no-conflict-check", action="store_true", help="跳过进程冲突检查")
    parser.add_argument("--check-only", action="store_true", help="仅检查状态，不启动")
    parser.add_argument("--stop", action="store_true", help="停止现有实例")
    
    args = parser.parse_args()
    
    starter = BotStarter()
    
    if args.check_only:
        # 仅检查状态
        if starter.check_existing_instance():
            print("机器人实例正在运行")
            sys.exit(0)
        else:
            print("未检测到运行中的机器人实例")
            sys.exit(1)
    
    elif args.stop:
        # 停止现有实例
        if starter.pid_file.exists():
            try:
                with open(starter.pid_file, 'r') as f:
                    pid = int(f.read().strip())
                
                import psutil
                process = psutil.Process(pid)
                process.terminate()
                
                # 等待进程终止
                try:
                    process.wait(timeout=10)
                    print(f"机器人实例已停止 (PID: {pid})")
                except psutil.TimeoutExpired:
                    process.kill()
                    print(f"强制终止机器人实例 (PID: {pid})")
                
                starter.cleanup_pid_file()
                
            except (ValueError, FileNotFoundError, psutil.NoSuchProcess):
                print("未找到运行中的机器人实例")
                starter.cleanup_pid_file()
            except Exception as e:
                print(f"停止实例失败: {e}")
                sys.exit(1)
        else:
            print("未找到PID文件")
    
    else:
        # 启动机器人
        try:
            success = asyncio.run(starter.start(
                force=args.force,
                check_conflicts=not args.no_conflict_check
            ))
            
            if not success:
                sys.exit(1)
                
        except KeyboardInterrupt:
            logger.info("用户中断启动")
        except Exception as e:
            logger.error(f"启动异常: {e}", exc_info=True)
            sys.exit(1)


if __name__ == "__main__":
    main()
